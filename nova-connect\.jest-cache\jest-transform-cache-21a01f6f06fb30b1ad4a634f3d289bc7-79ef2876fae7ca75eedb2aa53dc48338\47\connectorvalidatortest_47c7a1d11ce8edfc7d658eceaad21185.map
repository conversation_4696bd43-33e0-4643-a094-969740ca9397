{"version": 3, "names": ["ConnectorValidator", "require", "fs", "path", "describe", "googleCloudConnector", "microsoftDefenderConnector", "awsSecurityHubConnector", "beforeAll", "JSON", "parse", "readFileSync", "join", "__dirname", "test", "result", "validateConnector", "expect", "<PERSON><PERSON><PERSON><PERSON>", "toBe", "errors", "toHave<PERSON>ength", "invalidConnector", "authentication", "type", "fields", "<PERSON><PERSON><PERSON><PERSON>", "required", "toContain", "metadata", "version", "category", "description", "name", "configuration", "headers", "baseUrl", "endpoints", "method", "id", "validateMetadata", "validateAuthentication", "validateConfiguration", "timeout", "rateLimit", "validateEndpoints", "isValidUrl", "isValidVersion"], "sources": ["connector-validator.test.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector Validator Tests\n * \n * This module contains tests for the connector validator.\n */\n\nconst { ConnectorValidator } = require('../../src/validation');\nconst fs = require('fs');\nconst path = require('path');\n\ndescribe('ConnectorValidator', () => {\n  let googleCloudConnector;\n  let microsoftDefenderConnector;\n  let awsSecurityHubConnector;\n  \n  beforeAll(() => {\n    // Load test connectors\n    googleCloudConnector = JSON.parse(\n      fs.readFileSync(path.join(__dirname, '../../templates/google-cloud-security.json'), 'utf8')\n    );\n    \n    microsoftDefenderConnector = JSON.parse(\n      fs.readFileSync(path.join(__dirname, '../../templates/microsoft-defender-cloud.json'), 'utf8')\n    );\n    \n    awsSecurityHubConnector = JSON.parse(\n      fs.readFileSync(path.join(__dirname, '../../templates/aws-security-hub.json'), 'utf8')\n    );\n  });\n  \n  describe('validateConnector', () => {\n    test('should validate a valid connector', () => {\n      const result = ConnectorValidator.validateConnector(googleCloudConnector);\n      expect(result.isValid).toBe(true);\n      expect(result.errors).toHaveLength(0);\n    });\n    \n    test('should validate another valid connector', () => {\n      const result = ConnectorValidator.validateConnector(microsoftDefenderConnector);\n      expect(result.isValid).toBe(true);\n      expect(result.errors).toHaveLength(0);\n    });\n    \n    test('should validate a third valid connector', () => {\n      const result = ConnectorValidator.validateConnector(awsSecurityHubConnector);\n      expect(result.isValid).toBe(true);\n      expect(result.errors).toHaveLength(0);\n    });\n    \n    test('should return errors for an invalid connector', () => {\n      const invalidConnector = {\n        // Missing metadata\n        authentication: {\n          type: 'API_KEY',\n          fields: {\n            apiKey: {\n              type: 'string',\n              required: true\n            }\n          }\n        }\n      };\n      \n      const result = ConnectorValidator.validateConnector(invalidConnector);\n      expect(result.isValid).toBe(false);\n      expect(result.errors).toContain('Connector metadata is required');\n    });\n    \n    test('should return errors for a connector with invalid metadata', () => {\n      const invalidConnector = {\n        metadata: {\n          // Missing name\n          version: '1.0.0',\n          category: 'Test',\n          description: 'Test connector'\n        },\n        authentication: {\n          type: 'API_KEY',\n          fields: {\n            apiKey: {\n              type: 'string',\n              required: true\n            }\n          }\n        }\n      };\n      \n      const result = ConnectorValidator.validateConnector(invalidConnector);\n      expect(result.isValid).toBe(false);\n      expect(result.errors).toContain('Connector name is required');\n    });\n    \n    test('should return errors for a connector with invalid authentication', () => {\n      const invalidConnector = {\n        metadata: {\n          name: 'Test Connector',\n          version: '1.0.0',\n          category: 'Test',\n          description: 'Test connector'\n        },\n        authentication: {\n          type: 'INVALID_TYPE', // Invalid type\n          fields: {\n            apiKey: {\n              type: 'string',\n              required: true\n            }\n          }\n        }\n      };\n      \n      const result = ConnectorValidator.validateConnector(invalidConnector);\n      expect(result.isValid).toBe(false);\n      expect(result.errors).toContain('Authentication type must be one of: API_KEY, BASIC, OAUTH2, CUSTOM, NONE');\n    });\n    \n    test('should return errors for a connector with missing configuration', () => {\n      const invalidConnector = {\n        metadata: {\n          name: 'Test Connector',\n          version: '1.0.0',\n          category: 'Test',\n          description: 'Test connector'\n        },\n        authentication: {\n          type: 'API_KEY',\n          fields: {\n            apiKey: {\n              type: 'string',\n              required: true\n            }\n          }\n        }\n        // Missing configuration\n      };\n      \n      const result = ConnectorValidator.validateConnector(invalidConnector);\n      expect(result.isValid).toBe(false);\n      expect(result.errors).toContain('Connector configuration is required');\n    });\n    \n    test('should return errors for a connector with invalid configuration', () => {\n      const invalidConnector = {\n        metadata: {\n          name: 'Test Connector',\n          version: '1.0.0',\n          category: 'Test',\n          description: 'Test connector'\n        },\n        authentication: {\n          type: 'API_KEY',\n          fields: {\n            apiKey: {\n              type: 'string',\n              required: true\n            }\n          }\n        },\n        configuration: {\n          // Missing baseUrl\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        }\n      };\n      \n      const result = ConnectorValidator.validateConnector(invalidConnector);\n      expect(result.isValid).toBe(false);\n      expect(result.errors).toContain('Configuration base URL is required');\n    });\n    \n    test('should return errors for a connector with missing endpoints', () => {\n      const invalidConnector = {\n        metadata: {\n          name: 'Test Connector',\n          version: '1.0.0',\n          category: 'Test',\n          description: 'Test connector'\n        },\n        authentication: {\n          type: 'API_KEY',\n          fields: {\n            apiKey: {\n              type: 'string',\n              required: true\n            }\n          }\n        },\n        configuration: {\n          baseUrl: 'https://api.example.com'\n        }\n        // Missing endpoints\n      };\n      \n      const result = ConnectorValidator.validateConnector(invalidConnector);\n      expect(result.isValid).toBe(false);\n      expect(result.errors).toContain('Connector endpoints are required');\n    });\n    \n    test('should return errors for a connector with invalid endpoints', () => {\n      const invalidConnector = {\n        metadata: {\n          name: 'Test Connector',\n          version: '1.0.0',\n          category: 'Test',\n          description: 'Test connector'\n        },\n        authentication: {\n          type: 'API_KEY',\n          fields: {\n            apiKey: {\n              type: 'string',\n              required: true\n            }\n          }\n        },\n        configuration: {\n          baseUrl: 'https://api.example.com'\n        },\n        endpoints: [\n          {\n            // Missing id\n            name: 'Test Endpoint',\n            path: '/test',\n            method: 'GET'\n          }\n        ]\n      };\n      \n      const result = ConnectorValidator.validateConnector(invalidConnector);\n      expect(result.isValid).toBe(false);\n      expect(result.errors).toContain('Endpoint at index 0 must have an ID');\n    });\n    \n    test('should return errors for a connector with duplicate endpoint IDs', () => {\n      const invalidConnector = {\n        metadata: {\n          name: 'Test Connector',\n          version: '1.0.0',\n          category: 'Test',\n          description: 'Test connector'\n        },\n        authentication: {\n          type: 'API_KEY',\n          fields: {\n            apiKey: {\n              type: 'string',\n              required: true\n            }\n          }\n        },\n        configuration: {\n          baseUrl: 'https://api.example.com'\n        },\n        endpoints: [\n          {\n            id: 'test',\n            name: 'Test Endpoint 1',\n            path: '/test1',\n            method: 'GET'\n          },\n          {\n            id: 'test', // Duplicate ID\n            name: 'Test Endpoint 2',\n            path: '/test2',\n            method: 'GET'\n          }\n        ]\n      };\n      \n      const result = ConnectorValidator.validateConnector(invalidConnector);\n      expect(result.isValid).toBe(false);\n      expect(result.errors).toContain('Duplicate endpoint IDs found: test');\n    });\n  });\n  \n  describe('validateMetadata', () => {\n    test('should validate valid metadata', () => {\n      const errors = [];\n      ConnectorValidator.validateMetadata(googleCloudConnector, errors);\n      expect(errors).toHaveLength(0);\n    });\n    \n    test('should return errors for invalid metadata', () => {\n      const errors = [];\n      const invalidConnector = {\n        metadata: {\n          name: 123, // Should be a string\n          version: '1.0', // Invalid semver\n          category: 'Test',\n          description: 'Test connector'\n        }\n      };\n      \n      ConnectorValidator.validateMetadata(invalidConnector, errors);\n      expect(errors).toContain('Connector name must be a string');\n      expect(errors).toContain('Connector version must be in semver format (e.g., 1.0.0)');\n    });\n  });\n  \n  describe('validateAuthentication', () => {\n    test('should validate valid authentication', () => {\n      const errors = [];\n      ConnectorValidator.validateAuthentication(googleCloudConnector, errors);\n      expect(errors).toHaveLength(0);\n    });\n    \n    test('should return errors for invalid authentication', () => {\n      const errors = [];\n      const invalidConnector = {\n        authentication: {\n          type: 'INVALID_TYPE',\n          fields: {\n            apiKey: {\n              type: 'invalid_type',\n              required: 'yes' // Should be a boolean\n            }\n          }\n        }\n      };\n      \n      ConnectorValidator.validateAuthentication(invalidConnector, errors);\n      expect(errors).toContain('Authentication type must be one of: API_KEY, BASIC, OAUTH2, CUSTOM, NONE');\n      expect(errors).toContain('Authentication field \\'apiKey\\' type must be one of: string, number, boolean, object, array');\n      expect(errors).toContain('Authentication field \\'apiKey\\' required must be a boolean');\n    });\n  });\n  \n  describe('validateConfiguration', () => {\n    test('should validate valid configuration', () => {\n      const errors = [];\n      ConnectorValidator.validateConfiguration(googleCloudConnector, errors);\n      expect(errors).toHaveLength(0);\n    });\n    \n    test('should return errors for invalid configuration', () => {\n      const errors = [];\n      const invalidConnector = {\n        configuration: {\n          baseUrl: 'invalid-url',\n          timeout: -1, // Should be positive\n          rateLimit: 'invalid' // Should be an object\n        }\n      };\n      \n      ConnectorValidator.validateConfiguration(invalidConnector, errors);\n      expect(errors).toContain('Configuration base URL must be a valid URL');\n      expect(errors).toContain('Configuration timeout must be greater than 0');\n      expect(errors).toContain('Configuration rate limit must be an object');\n    });\n  });\n  \n  describe('validateEndpoints', () => {\n    test('should validate valid endpoints', () => {\n      const errors = [];\n      ConnectorValidator.validateEndpoints(googleCloudConnector, errors);\n      expect(errors).toHaveLength(0);\n    });\n    \n    test('should return errors for invalid endpoints', () => {\n      const errors = [];\n      const invalidConnector = {\n        endpoints: [\n          {\n            // Missing id\n            name: 'Test Endpoint',\n            path: '/test',\n            method: 'INVALID_METHOD' // Invalid method\n          }\n        ]\n      };\n      \n      ConnectorValidator.validateEndpoints(invalidConnector, errors);\n      expect(errors).toContain('Endpoint at index 0 must have an ID');\n      expect(errors).toContain('Endpoint at index 0 method must be one of: GET, POST, PUT, PATCH, DELETE');\n    });\n  });\n  \n  describe('utility methods', () => {\n    test('isValidUrl should validate URLs correctly', () => {\n      expect(ConnectorValidator.isValidUrl('https://example.com')).toBe(true);\n      expect(ConnectorValidator.isValidUrl('http://example.com/path')).toBe(true);\n      expect(ConnectorValidator.isValidUrl('invalid-url')).toBe(false);\n      expect(ConnectorValidator.isValidUrl('example.com')).toBe(false);\n    });\n    \n    test('isValidVersion should validate semver versions correctly', () => {\n      expect(ConnectorValidator.isValidVersion('1.0.0')).toBe(true);\n      expect(ConnectorValidator.isValidVersion('1.0.0-alpha')).toBe(true);\n      expect(ConnectorValidator.isValidVersion('1.0.0-alpha.1')).toBe(true);\n      expect(ConnectorValidator.isValidVersion('1.0')).toBe(false);\n      expect(ConnectorValidator.isValidVersion('v1.0.0')).toBe(false);\n      expect(ConnectorValidator.isValidVersion('1')).toBe(false);\n    });\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAmB,CAAC,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC9D,MAAMC,EAAE,GAAGD,OAAO,CAAC,IAAI,CAAC;AACxB,MAAME,IAAI,GAAGF,OAAO,CAAC,MAAM,CAAC;AAE5BG,QAAQ,CAAC,oBAAoB,EAAE,MAAM;EACnC,IAAIC,oBAAoB;EACxB,IAAIC,0BAA0B;EAC9B,IAAIC,uBAAuB;EAE3BC,SAAS,CAAC,MAAM;IACd;IACAH,oBAAoB,GAAGI,IAAI,CAACC,KAAK,CAC/BR,EAAE,CAACS,YAAY,CAACR,IAAI,CAACS,IAAI,CAACC,SAAS,EAAE,4CAA4C,CAAC,EAAE,MAAM,CAC5F,CAAC;IAEDP,0BAA0B,GAAGG,IAAI,CAACC,KAAK,CACrCR,EAAE,CAACS,YAAY,CAACR,IAAI,CAACS,IAAI,CAACC,SAAS,EAAE,+CAA+C,CAAC,EAAE,MAAM,CAC/F,CAAC;IAEDN,uBAAuB,GAAGE,IAAI,CAACC,KAAK,CAClCR,EAAE,CAACS,YAAY,CAACR,IAAI,CAACS,IAAI,CAACC,SAAS,EAAE,uCAAuC,CAAC,EAAE,MAAM,CACvF,CAAC;EACH,CAAC,CAAC;EAEFT,QAAQ,CAAC,mBAAmB,EAAE,MAAM;IAClCU,IAAI,CAAC,mCAAmC,EAAE,MAAM;MAC9C,MAAMC,MAAM,GAAGf,kBAAkB,CAACgB,iBAAiB,CAACX,oBAAoB,CAAC;MACzEY,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MACjCF,MAAM,CAACF,MAAM,CAACK,MAAM,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC;IAEFP,IAAI,CAAC,yCAAyC,EAAE,MAAM;MACpD,MAAMC,MAAM,GAAGf,kBAAkB,CAACgB,iBAAiB,CAACV,0BAA0B,CAAC;MAC/EW,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MACjCF,MAAM,CAACF,MAAM,CAACK,MAAM,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC;IAEFP,IAAI,CAAC,yCAAyC,EAAE,MAAM;MACpD,MAAMC,MAAM,GAAGf,kBAAkB,CAACgB,iBAAiB,CAACT,uBAAuB,CAAC;MAC5EU,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MACjCF,MAAM,CAACF,MAAM,CAACK,MAAM,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC;IAEFP,IAAI,CAAC,+CAA+C,EAAE,MAAM;MAC1D,MAAMQ,gBAAgB,GAAG;QACvB;QACAC,cAAc,EAAE;UACdC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;YACNC,MAAM,EAAE;cACNF,IAAI,EAAE,QAAQ;cACdG,QAAQ,EAAE;YACZ;UACF;QACF;MACF,CAAC;MAED,MAAMZ,MAAM,GAAGf,kBAAkB,CAACgB,iBAAiB,CAACM,gBAAgB,CAAC;MACrEL,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAClCF,MAAM,CAACF,MAAM,CAACK,MAAM,CAAC,CAACQ,SAAS,CAAC,gCAAgC,CAAC;IACnE,CAAC,CAAC;IAEFd,IAAI,CAAC,4DAA4D,EAAE,MAAM;MACvE,MAAMQ,gBAAgB,GAAG;QACvBO,QAAQ,EAAE;UACR;UACAC,OAAO,EAAE,OAAO;UAChBC,QAAQ,EAAE,MAAM;UAChBC,WAAW,EAAE;QACf,CAAC;QACDT,cAAc,EAAE;UACdC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;YACNC,MAAM,EAAE;cACNF,IAAI,EAAE,QAAQ;cACdG,QAAQ,EAAE;YACZ;UACF;QACF;MACF,CAAC;MAED,MAAMZ,MAAM,GAAGf,kBAAkB,CAACgB,iBAAiB,CAACM,gBAAgB,CAAC;MACrEL,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAClCF,MAAM,CAACF,MAAM,CAACK,MAAM,CAAC,CAACQ,SAAS,CAAC,4BAA4B,CAAC;IAC/D,CAAC,CAAC;IAEFd,IAAI,CAAC,kEAAkE,EAAE,MAAM;MAC7E,MAAMQ,gBAAgB,GAAG;QACvBO,QAAQ,EAAE;UACRI,IAAI,EAAE,gBAAgB;UACtBH,OAAO,EAAE,OAAO;UAChBC,QAAQ,EAAE,MAAM;UAChBC,WAAW,EAAE;QACf,CAAC;QACDT,cAAc,EAAE;UACdC,IAAI,EAAE,cAAc;UAAE;UACtBC,MAAM,EAAE;YACNC,MAAM,EAAE;cACNF,IAAI,EAAE,QAAQ;cACdG,QAAQ,EAAE;YACZ;UACF;QACF;MACF,CAAC;MAED,MAAMZ,MAAM,GAAGf,kBAAkB,CAACgB,iBAAiB,CAACM,gBAAgB,CAAC;MACrEL,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAClCF,MAAM,CAACF,MAAM,CAACK,MAAM,CAAC,CAACQ,SAAS,CAAC,0EAA0E,CAAC;IAC7G,CAAC,CAAC;IAEFd,IAAI,CAAC,iEAAiE,EAAE,MAAM;MAC5E,MAAMQ,gBAAgB,GAAG;QACvBO,QAAQ,EAAE;UACRI,IAAI,EAAE,gBAAgB;UACtBH,OAAO,EAAE,OAAO;UAChBC,QAAQ,EAAE,MAAM;UAChBC,WAAW,EAAE;QACf,CAAC;QACDT,cAAc,EAAE;UACdC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;YACNC,MAAM,EAAE;cACNF,IAAI,EAAE,QAAQ;cACdG,QAAQ,EAAE;YACZ;UACF;QACF;QACA;MACF,CAAC;MAED,MAAMZ,MAAM,GAAGf,kBAAkB,CAACgB,iBAAiB,CAACM,gBAAgB,CAAC;MACrEL,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAClCF,MAAM,CAACF,MAAM,CAACK,MAAM,CAAC,CAACQ,SAAS,CAAC,qCAAqC,CAAC;IACxE,CAAC,CAAC;IAEFd,IAAI,CAAC,iEAAiE,EAAE,MAAM;MAC5E,MAAMQ,gBAAgB,GAAG;QACvBO,QAAQ,EAAE;UACRI,IAAI,EAAE,gBAAgB;UACtBH,OAAO,EAAE,OAAO;UAChBC,QAAQ,EAAE,MAAM;UAChBC,WAAW,EAAE;QACf,CAAC;QACDT,cAAc,EAAE;UACdC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;YACNC,MAAM,EAAE;cACNF,IAAI,EAAE,QAAQ;cACdG,QAAQ,EAAE;YACZ;UACF;QACF,CAAC;QACDO,aAAa,EAAE;UACb;UACAC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF;MACF,CAAC;MAED,MAAMpB,MAAM,GAAGf,kBAAkB,CAACgB,iBAAiB,CAACM,gBAAgB,CAAC;MACrEL,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAClCF,MAAM,CAACF,MAAM,CAACK,MAAM,CAAC,CAACQ,SAAS,CAAC,oCAAoC,CAAC;IACvE,CAAC,CAAC;IAEFd,IAAI,CAAC,6DAA6D,EAAE,MAAM;MACxE,MAAMQ,gBAAgB,GAAG;QACvBO,QAAQ,EAAE;UACRI,IAAI,EAAE,gBAAgB;UACtBH,OAAO,EAAE,OAAO;UAChBC,QAAQ,EAAE,MAAM;UAChBC,WAAW,EAAE;QACf,CAAC;QACDT,cAAc,EAAE;UACdC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;YACNC,MAAM,EAAE;cACNF,IAAI,EAAE,QAAQ;cACdG,QAAQ,EAAE;YACZ;UACF;QACF,CAAC;QACDO,aAAa,EAAE;UACbE,OAAO,EAAE;QACX;QACA;MACF,CAAC;MAED,MAAMrB,MAAM,GAAGf,kBAAkB,CAACgB,iBAAiB,CAACM,gBAAgB,CAAC;MACrEL,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAClCF,MAAM,CAACF,MAAM,CAACK,MAAM,CAAC,CAACQ,SAAS,CAAC,kCAAkC,CAAC;IACrE,CAAC,CAAC;IAEFd,IAAI,CAAC,6DAA6D,EAAE,MAAM;MACxE,MAAMQ,gBAAgB,GAAG;QACvBO,QAAQ,EAAE;UACRI,IAAI,EAAE,gBAAgB;UACtBH,OAAO,EAAE,OAAO;UAChBC,QAAQ,EAAE,MAAM;UAChBC,WAAW,EAAE;QACf,CAAC;QACDT,cAAc,EAAE;UACdC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;YACNC,MAAM,EAAE;cACNF,IAAI,EAAE,QAAQ;cACdG,QAAQ,EAAE;YACZ;UACF;QACF,CAAC;QACDO,aAAa,EAAE;UACbE,OAAO,EAAE;QACX,CAAC;QACDC,SAAS,EAAE,CACT;UACE;UACAJ,IAAI,EAAE,eAAe;UACrB9B,IAAI,EAAE,OAAO;UACbmC,MAAM,EAAE;QACV,CAAC;MAEL,CAAC;MAED,MAAMvB,MAAM,GAAGf,kBAAkB,CAACgB,iBAAiB,CAACM,gBAAgB,CAAC;MACrEL,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAClCF,MAAM,CAACF,MAAM,CAACK,MAAM,CAAC,CAACQ,SAAS,CAAC,qCAAqC,CAAC;IACxE,CAAC,CAAC;IAEFd,IAAI,CAAC,kEAAkE,EAAE,MAAM;MAC7E,MAAMQ,gBAAgB,GAAG;QACvBO,QAAQ,EAAE;UACRI,IAAI,EAAE,gBAAgB;UACtBH,OAAO,EAAE,OAAO;UAChBC,QAAQ,EAAE,MAAM;UAChBC,WAAW,EAAE;QACf,CAAC;QACDT,cAAc,EAAE;UACdC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;YACNC,MAAM,EAAE;cACNF,IAAI,EAAE,QAAQ;cACdG,QAAQ,EAAE;YACZ;UACF;QACF,CAAC;QACDO,aAAa,EAAE;UACbE,OAAO,EAAE;QACX,CAAC;QACDC,SAAS,EAAE,CACT;UACEE,EAAE,EAAE,MAAM;UACVN,IAAI,EAAE,iBAAiB;UACvB9B,IAAI,EAAE,QAAQ;UACdmC,MAAM,EAAE;QACV,CAAC,EACD;UACEC,EAAE,EAAE,MAAM;UAAE;UACZN,IAAI,EAAE,iBAAiB;UACvB9B,IAAI,EAAE,QAAQ;UACdmC,MAAM,EAAE;QACV,CAAC;MAEL,CAAC;MAED,MAAMvB,MAAM,GAAGf,kBAAkB,CAACgB,iBAAiB,CAACM,gBAAgB,CAAC;MACrEL,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAClCF,MAAM,CAACF,MAAM,CAACK,MAAM,CAAC,CAACQ,SAAS,CAAC,oCAAoC,CAAC;IACvE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxB,QAAQ,CAAC,kBAAkB,EAAE,MAAM;IACjCU,IAAI,CAAC,gCAAgC,EAAE,MAAM;MAC3C,MAAMM,MAAM,GAAG,EAAE;MACjBpB,kBAAkB,CAACwC,gBAAgB,CAACnC,oBAAoB,EAAEe,MAAM,CAAC;MACjEH,MAAM,CAACG,MAAM,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC;IAEFP,IAAI,CAAC,2CAA2C,EAAE,MAAM;MACtD,MAAMM,MAAM,GAAG,EAAE;MACjB,MAAME,gBAAgB,GAAG;QACvBO,QAAQ,EAAE;UACRI,IAAI,EAAE,GAAG;UAAE;UACXH,OAAO,EAAE,KAAK;UAAE;UAChBC,QAAQ,EAAE,MAAM;UAChBC,WAAW,EAAE;QACf;MACF,CAAC;MAEDhC,kBAAkB,CAACwC,gBAAgB,CAAClB,gBAAgB,EAAEF,MAAM,CAAC;MAC7DH,MAAM,CAACG,MAAM,CAAC,CAACQ,SAAS,CAAC,iCAAiC,CAAC;MAC3DX,MAAM,CAACG,MAAM,CAAC,CAACQ,SAAS,CAAC,0DAA0D,CAAC;IACtF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxB,QAAQ,CAAC,wBAAwB,EAAE,MAAM;IACvCU,IAAI,CAAC,sCAAsC,EAAE,MAAM;MACjD,MAAMM,MAAM,GAAG,EAAE;MACjBpB,kBAAkB,CAACyC,sBAAsB,CAACpC,oBAAoB,EAAEe,MAAM,CAAC;MACvEH,MAAM,CAACG,MAAM,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC;IAEFP,IAAI,CAAC,iDAAiD,EAAE,MAAM;MAC5D,MAAMM,MAAM,GAAG,EAAE;MACjB,MAAME,gBAAgB,GAAG;QACvBC,cAAc,EAAE;UACdC,IAAI,EAAE,cAAc;UACpBC,MAAM,EAAE;YACNC,MAAM,EAAE;cACNF,IAAI,EAAE,cAAc;cACpBG,QAAQ,EAAE,KAAK,CAAC;YAClB;UACF;QACF;MACF,CAAC;MAED3B,kBAAkB,CAACyC,sBAAsB,CAACnB,gBAAgB,EAAEF,MAAM,CAAC;MACnEH,MAAM,CAACG,MAAM,CAAC,CAACQ,SAAS,CAAC,0EAA0E,CAAC;MACpGX,MAAM,CAACG,MAAM,CAAC,CAACQ,SAAS,CAAC,6FAA6F,CAAC;MACvHX,MAAM,CAACG,MAAM,CAAC,CAACQ,SAAS,CAAC,4DAA4D,CAAC;IACxF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxB,QAAQ,CAAC,uBAAuB,EAAE,MAAM;IACtCU,IAAI,CAAC,qCAAqC,EAAE,MAAM;MAChD,MAAMM,MAAM,GAAG,EAAE;MACjBpB,kBAAkB,CAAC0C,qBAAqB,CAACrC,oBAAoB,EAAEe,MAAM,CAAC;MACtEH,MAAM,CAACG,MAAM,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC;IAEFP,IAAI,CAAC,gDAAgD,EAAE,MAAM;MAC3D,MAAMM,MAAM,GAAG,EAAE;MACjB,MAAME,gBAAgB,GAAG;QACvBY,aAAa,EAAE;UACbE,OAAO,EAAE,aAAa;UACtBO,OAAO,EAAE,CAAC,CAAC;UAAE;UACbC,SAAS,EAAE,SAAS,CAAC;QACvB;MACF,CAAC;MAED5C,kBAAkB,CAAC0C,qBAAqB,CAACpB,gBAAgB,EAAEF,MAAM,CAAC;MAClEH,MAAM,CAACG,MAAM,CAAC,CAACQ,SAAS,CAAC,4CAA4C,CAAC;MACtEX,MAAM,CAACG,MAAM,CAAC,CAACQ,SAAS,CAAC,8CAA8C,CAAC;MACxEX,MAAM,CAACG,MAAM,CAAC,CAACQ,SAAS,CAAC,4CAA4C,CAAC;IACxE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxB,QAAQ,CAAC,mBAAmB,EAAE,MAAM;IAClCU,IAAI,CAAC,iCAAiC,EAAE,MAAM;MAC5C,MAAMM,MAAM,GAAG,EAAE;MACjBpB,kBAAkB,CAAC6C,iBAAiB,CAACxC,oBAAoB,EAAEe,MAAM,CAAC;MAClEH,MAAM,CAACG,MAAM,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC;IAEFP,IAAI,CAAC,4CAA4C,EAAE,MAAM;MACvD,MAAMM,MAAM,GAAG,EAAE;MACjB,MAAME,gBAAgB,GAAG;QACvBe,SAAS,EAAE,CACT;UACE;UACAJ,IAAI,EAAE,eAAe;UACrB9B,IAAI,EAAE,OAAO;UACbmC,MAAM,EAAE,gBAAgB,CAAC;QAC3B,CAAC;MAEL,CAAC;MAEDtC,kBAAkB,CAAC6C,iBAAiB,CAACvB,gBAAgB,EAAEF,MAAM,CAAC;MAC9DH,MAAM,CAACG,MAAM,CAAC,CAACQ,SAAS,CAAC,qCAAqC,CAAC;MAC/DX,MAAM,CAACG,MAAM,CAAC,CAACQ,SAAS,CAAC,0EAA0E,CAAC;IACtG,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxB,QAAQ,CAAC,iBAAiB,EAAE,MAAM;IAChCU,IAAI,CAAC,2CAA2C,EAAE,MAAM;MACtDG,MAAM,CAACjB,kBAAkB,CAAC8C,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC3B,IAAI,CAAC,IAAI,CAAC;MACvEF,MAAM,CAACjB,kBAAkB,CAAC8C,UAAU,CAAC,yBAAyB,CAAC,CAAC,CAAC3B,IAAI,CAAC,IAAI,CAAC;MAC3EF,MAAM,CAACjB,kBAAkB,CAAC8C,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC3B,IAAI,CAAC,KAAK,CAAC;MAChEF,MAAM,CAACjB,kBAAkB,CAAC8C,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC3B,IAAI,CAAC,KAAK,CAAC;IAClE,CAAC,CAAC;IAEFL,IAAI,CAAC,0DAA0D,EAAE,MAAM;MACrEG,MAAM,CAACjB,kBAAkB,CAAC+C,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC5B,IAAI,CAAC,IAAI,CAAC;MAC7DF,MAAM,CAACjB,kBAAkB,CAAC+C,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC5B,IAAI,CAAC,IAAI,CAAC;MACnEF,MAAM,CAACjB,kBAAkB,CAAC+C,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC5B,IAAI,CAAC,IAAI,CAAC;MACrEF,MAAM,CAACjB,kBAAkB,CAAC+C,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC5B,IAAI,CAAC,KAAK,CAAC;MAC5DF,MAAM,CAACjB,kBAAkB,CAAC+C,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC5B,IAAI,CAAC,KAAK,CAAC;MAC/DF,MAAM,CAACjB,kBAAkB,CAAC+C,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC5B,IAAI,CAAC,KAAK,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}