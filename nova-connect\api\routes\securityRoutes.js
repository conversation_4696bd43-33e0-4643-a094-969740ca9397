/**
 * Security Routes
 */

const express = require('express');
const router = express.Router();
const SecurityController = require('../controllers/SecurityController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');
const { hasFeatureAccess, trackFeatureUsage } = require('../middleware/featureAccessMiddleware');

// All routes require authentication
router.use(authenticate);

// Security audit routes
router.get('/audit', 
  hasPermission('security:view_audit'),
  (req, res, next) => {
    SecurityController.getSecurityAuditLogs(req, res, next);
  }
);

// IP restriction routes
router.get('/ip-restrictions', 
  hasFeatureAccess('security.ip_restrictions'),
  hasPermission('security:view'),
  (req, res, next) => {
    SecurityController.getAllIpRestrictions(req, res, next);
  }
);

router.get('/ip-restrictions/:id', 
  hasFeatureAccess('security.ip_restrictions'),
  hasPermission('security:view'),
  (req, res, next) => {
    SecurityController.getIpRestrictionById(req, res, next);
  }
);

router.post('/ip-restrictions', 
  hasFeatureAccess('security.ip_restrictions'),
  hasPermission('security:manage'),
  trackFeatureUsage('security.ip_restrictions'),
  (req, res, next) => {
    SecurityController.createIpRestriction(req, res, next);
  }
);

router.put('/ip-restrictions/:id', 
  hasFeatureAccess('security.ip_restrictions'),
  hasPermission('security:manage'),
  (req, res, next) => {
    SecurityController.updateIpRestriction(req, res, next);
  }
);

router.delete('/ip-restrictions/:id', 
  hasFeatureAccess('security.ip_restrictions'),
  hasPermission('security:manage'),
  (req, res, next) => {
    SecurityController.deleteIpRestriction(req, res, next);
  }
);

router.get('/resources/:resourceType/:resourceId/ip-access', 
  hasFeatureAccess('security.ip_restrictions'),
  (req, res, next) => {
    SecurityController.checkIpAccess(req, res, next);
  }
);

// Encryption key routes
router.get('/encryption-keys', 
  hasFeatureAccess('security.encryption'),
  hasPermission('security:view'),
  (req, res, next) => {
    SecurityController.getAllEncryptionKeys(req, res, next);
  }
);

router.get('/encryption-keys/:id', 
  hasFeatureAccess('security.encryption'),
  hasPermission('security:view'),
  (req, res, next) => {
    SecurityController.getEncryptionKeyById(req, res, next);
  }
);

router.post('/encryption-keys', 
  hasFeatureAccess('security.encryption'),
  hasPermission('security:manage'),
  trackFeatureUsage('security.encryption'),
  (req, res, next) => {
    SecurityController.createEncryptionKey(req, res, next);
  }
);

router.put('/encryption-keys/:id', 
  hasFeatureAccess('security.encryption'),
  hasPermission('security:manage'),
  (req, res, next) => {
    SecurityController.updateEncryptionKey(req, res, next);
  }
);

router.post('/encryption-keys/:id/rotate', 
  hasFeatureAccess('security.encryption'),
  hasPermission('security:manage'),
  (req, res, next) => {
    SecurityController.rotateEncryptionKey(req, res, next);
  }
);

router.delete('/encryption-keys/:id', 
  hasFeatureAccess('security.encryption'),
  hasPermission('security:manage'),
  (req, res, next) => {
    SecurityController.deleteEncryptionKey(req, res, next);
  }
);

router.post('/encrypt', 
  hasFeatureAccess('security.encryption'),
  (req, res, next) => {
    SecurityController.encryptData(req, res, next);
  }
);

router.post('/decrypt', 
  hasFeatureAccess('security.encryption'),
  (req, res, next) => {
    SecurityController.decryptData(req, res, next);
  }
);

// Security policy routes
router.get('/policies', 
  hasFeatureAccess('security.policies'),
  hasPermission('security:view'),
  (req, res, next) => {
    SecurityController.getAllSecurityPolicies(req, res, next);
  }
);

router.get('/policies/:id', 
  hasFeatureAccess('security.policies'),
  hasPermission('security:view'),
  (req, res, next) => {
    SecurityController.getSecurityPolicyById(req, res, next);
  }
);

router.post('/policies', 
  hasFeatureAccess('security.policies'),
  hasPermission('security:manage'),
  trackFeatureUsage('security.policies'),
  (req, res, next) => {
    SecurityController.createSecurityPolicy(req, res, next);
  }
);

router.put('/policies/:id', 
  hasFeatureAccess('security.policies'),
  hasPermission('security:manage'),
  (req, res, next) => {
    SecurityController.updateSecurityPolicy(req, res, next);
  }
);

router.delete('/policies/:id', 
  hasFeatureAccess('security.policies'),
  hasPermission('security:manage'),
  (req, res, next) => {
    SecurityController.deleteSecurityPolicy(req, res, next);
  }
);

router.post('/policies/:id/evaluate', 
  hasFeatureAccess('security.policies'),
  (req, res, next) => {
    SecurityController.evaluateSecurityPolicy(req, res, next);
  }
);

module.exports = router;
