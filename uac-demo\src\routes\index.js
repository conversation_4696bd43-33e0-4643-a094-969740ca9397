/**
 * API Routes
 * 
 * This module defines the API routes for the UAC demo.
 */

const express = require('express');
const router = express.Router();

// Import route modules
const authRoutes = require('./auth');
const connectorRoutes = require('./connector');
const complianceRoutes = require('./compliance');
const demoRoutes = require('./demo');

// Auth routes
router.use('/auth', authRoutes);

// Connector routes
router.use('/connector', connectorRoutes);

// Compliance routes
router.use('/compliance', complianceRoutes);

// Demo routes
router.use('/demo', demoRoutes);

// API status route
router.get('/status', (req, res) => {
  res.json({
    status: 'operational',
    timestamp: new Date(),
    version: '0.1.0'
  });
});

module.exports = router;
