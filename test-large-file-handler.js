/**
 * Test script for the large file handler
 * 
 * This script creates a large test file and uploads it to test the large file handler.
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');

// Configuration
const TEST_FILE_SIZE_MB = 100; // Size of test file in MB
const TEST_FILE_PATH = path.join(__dirname, 'test-large-file.dat');
const UPLOAD_URL = 'http://localhost:3000/api/v1/large-files/upload';

/**
 * Create a large test file
 * @param {string} filePath - Path to create the file
 * @param {number} sizeMB - Size of the file in MB
 */
async function createLargeFile(filePath, sizeMB) {
  console.log(`Creating test file of ${sizeMB}MB at ${filePath}...`);
  
  const writeStream = fs.createWriteStream(filePath);
  const chunkSize = 1024 * 1024; // 1MB chunks
  const buffer = Buffer.alloc(chunkSize, 'A');
  
  for (let i = 0; i < sizeMB; i++) {
    writeStream.write(buffer);
    
    // Log progress every 10MB
    if (i % 10 === 0) {
      console.log(`Written ${i}MB...`);
    }
  }
  
  return new Promise((resolve, reject) => {
    writeStream.end(() => {
      console.log(`Test file created: ${filePath} (${sizeMB}MB)`);
      resolve();
    });
    
    writeStream.on('error', (err) => {
      console.error('Error creating test file:', err);
      reject(err);
    });
  });
}

/**
 * Upload the test file
 * @param {string} filePath - Path to the file to upload
 * @param {string} url - URL to upload to
 */
async function uploadFile(filePath, url) {
  console.log(`Uploading file ${filePath} to ${url}...`);
  
  const form = new FormData();
  form.append('file', fs.createReadStream(filePath));
  
  try {
    const response = await axios.post(url, form, {
      headers: {
        ...form.getHeaders()
      },
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        console.log(`Upload progress: ${percentCompleted}%`);
      }
    });
    
    console.log('Upload successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Upload failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    throw error;
  }
}

/**
 * Clean up test file
 * @param {string} filePath - Path to the file to clean up
 */
function cleanupFile(filePath) {
  if (fs.existsSync(filePath)) {
    console.log(`Cleaning up test file: ${filePath}`);
    fs.unlinkSync(filePath);
    console.log('Test file removed');
  }
}

/**
 * Run the test
 */
async function runTest() {
  try {
    // Create test file
    await createLargeFile(TEST_FILE_PATH, TEST_FILE_SIZE_MB);
    
    // Upload test file
    await uploadFile(TEST_FILE_PATH, UPLOAD_URL);
    
    // Clean up
    cleanupFile(TEST_FILE_PATH);
    
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
    
    // Clean up on error
    cleanupFile(TEST_FILE_PATH);
  }
}

// Run the test
runTest();
