{"name": "novafuse-compliance-store-api", "version": "1.0.0", "description": "API for the NovaFuse Compliance App Store", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest"}, "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.17.1", "express-validator": "^6.12.1", "jsonwebtoken": "^8.5.1", "mongoose": "^5.13.7", "morgan": "^1.10.0", "nodemailer": "^6.6.3"}, "devDependencies": {"jest": "^27.0.6", "nodemon": "^2.0.12", "supertest": "^6.1.6"}}