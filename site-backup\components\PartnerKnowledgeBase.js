import React, { useState } from 'react';
import Link from 'next/link';

const PartnerKnowledgeBase = () => {
  const [activeCategory, setActiveCategory] = useState('overview');

  // Knowledge base structure
  const knowledgeBase = {
    overview: {
      title: 'Partner Program Overview',
      content: [
        {
          title: 'Benefits Summary',
          description: `
            <p>NovaFuse partners enjoy unparalleled benefits designed to drive revenue, expand market reach, and create competitive differentiation:</p>
            <ul>
              <li><strong>Revenue Sharing Model:</strong> Up to 30% revenue share for preferred partners via white-label APIs, referrals, or embedded deployments, while NovaFuse retains 70%</li>
              <li><strong>Accelerated GTM:</strong> Remove compliance blockers from sales cycles for faster closes</li>
              <li><strong>Differentiation Without Dev Overhead:</strong> Turn compliance into a feature, not a chore</li>
              <li><strong>Market Expansion Opportunities:</strong> Enter healthcare, government, financial services with confidence</li>
            </ul>
          `
        },
        {
          title: 'Eligibility Criteria',
          description: `
            <p>The ideal NovaFuse partner demonstrates the following attributes:</p>
            <ul>
              <li>Enterprise client base, especially in regulated verticals</li>
              <li>API-driven architecture (REST, GraphQL, FHIR, HL7, etc.)</li>
              <li>Need for Compliance-as-a-Service</li>
              <li>Willingness to co-market and expand TAM</li>
              <li>Interest in recurring revenue through reselling, white-labeling, or bundling</li>
              <li>Ambitious growth goals that align with compliance needs</li>
            </ul>
          `
        },
        {
          title: 'Partnership Tiers',
          description: `
            <p>NovaFuse offers multiple partnership tiers to accommodate different business models and engagement levels:</p>
            <ul>
              <li><strong>Preferred Partners:</strong> 30% revenue share to partner (70% to NovaFuse), strategic alignment, co-marketing, early access</li>
              <li><strong>Strategic Partners:</strong> 25% revenue share to partner (75% to NovaFuse), joint go-to-market, dedicated support</li>
              <li><strong>Integration Partners:</strong> 20% revenue share to partner (80% to NovaFuse), embedded deployments, technical enablement</li>
              <li><strong>High-Volume Partners:</strong> Up to 40% revenue share to partner (60% to NovaFuse), high-volume API usage, self-service tools</li>
            </ul>
          `
        }
      ]
    },
    integration: {
      title: 'Integration Support',
      content: [
        {
          title: 'API Documentation',
          description: `
            <p>NovaFuse provides comprehensive API documentation to facilitate seamless integration:</p>
            <ul>
              <li>RESTful API with OpenAPI 3.0 specifications</li>
              <li>GraphQL API for flexible data querying</li>
              <li>Webhook support for event-driven architectures</li>
              <li>Rate limits and performance considerations</li>
              <li>Authentication and authorization mechanisms</li>
            </ul>
            <p><a href="/api-docs" class="text-blue-400 hover:underline">View full API documentation →</a></p>
          `
        },
        {
          title: 'SDKs & Connectors',
          description: `
            <p>NovaFuse offers SDKs and pre-built connectors to accelerate integration:</p>
            <ul>
              <li>JavaScript/TypeScript SDK for web applications</li>
              <li>Python SDK for data science and backend systems</li>
              <li>Java SDK for enterprise applications</li>
              <li>100+ pre-built connectors with auto-auth & policy translation</li>
              <li>Custom connector development support</li>
            </ul>
          `
        },
        {
          title: 'Example Use Cases by Partner Type',
          description: `
            <p>NovaFuse enables various use cases depending on partner type:</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div class="bg-gradient-to-r from-blue-700 to-purple-700 p-4 rounded-lg shadow-md border border-blue-400">
                <h4 class="font-semibold text-white mb-2">Cloud Providers</h4>
                <ul class="list-disc list-inside text-sm space-y-1 text-blue-100">
                  <li><strong>GCP:</strong> Native compliance for BigQuery, Vertex AI, and Google Workspace</li>
                  <li><strong>AWS:</strong> Tailored solutions for AWS GovCloud and regulated workloads</li>
                  <li><strong>Azure:</strong> Compliance automation for Azure Government and healthcare</li>
                  <li><strong>Snowflake:</strong> Data warehousing compliance for regulated industries</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-blue-700 to-purple-700 p-4 rounded-lg shadow-md border border-blue-400">
                <h4 class="font-semibold text-white mb-2">Collaboration Tools</h4>
                <ul class="list-disc list-inside text-sm space-y-1 text-blue-100">
                  <li><strong>Slack/Teams:</strong> Embedded compliance controls for sensitive communications</li>
                  <li><strong>Zoom:</strong> GDPR/CCPA-compliant meeting analytics and recording retention</li>
                  <li><strong>Notion:</strong> Compliance-safe knowledge management for regulated content</li>
                  <li><strong>Monday.com:</strong> Compliant workflow templates for healthcare & legal</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-blue-700 to-purple-700 p-4 rounded-lg shadow-md border border-blue-400">
                <h4 class="font-semibold text-white mb-2">Integration Platforms</h4>
                <ul class="list-disc list-inside text-sm space-y-1 text-blue-100">
                  <li><strong>Zapier:</strong> Compliance-safe automation across 3,000+ apps</li>
                  <li><strong>Workato/Tray.io:</strong> Enterprise workflow automation with compliance guardrails</li>
                  <li><strong>Segment:</strong> Ensure data pipeline compliance for customer data platforms</li>
                  <li><strong>MuleSoft:</strong> API-led compliance for enterprise integrations</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-blue-700 to-purple-700 p-4 rounded-lg shadow-md border border-blue-400">
                <h4 class="font-semibold text-white mb-2">Identity & Security</h4>
                <ul class="list-disc list-inside text-sm space-y-1 text-blue-100">
                  <li><strong>Okta/Auth0:</strong> Bundle identity and compliance posture as a co-branded offering</li>
                  <li><strong>CrowdStrike:</strong> Integrate compliance automation with threat detection</li>
                  <li><strong>Palo Alto Networks:</strong> Holistic security and compliance monitoring</li>
                  <li><strong>Proofpoint:</strong> Email compliance and data loss prevention enhancements</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-blue-700 to-purple-700 p-4 rounded-lg shadow-md border border-blue-400">
                <h4 class="font-semibold text-white mb-2">Industry-Specific</h4>
                <ul class="list-disc list-inside text-sm space-y-1 text-blue-100">
                  <li><strong>HealthTech:</strong> HIPAA compliance for telehealth platforms (Hinge Health, Ro)</li>
                  <li><strong>FinTech:</strong> Automated SOX compliance for spend management (Brex, Ramp)</li>
                  <li><strong>EdTech:</strong> FERPA compliance for student data (Canvas, Blackboard)</li>
                  <li><strong>E-Commerce:</strong> PCI-DSS automation and GDPR compliance (Shopify, BigCommerce)</li>
                </ul>
              </div>

              <div class="bg-gradient-to-r from-blue-700 to-purple-700 p-4 rounded-lg shadow-md border border-blue-400">
                <h4 class="font-semibold text-white mb-2">Emerging Tech</h4>
                <ul class="list-disc list-inside text-sm space-y-1 text-blue-100">
                  <li><strong>IoT Platforms:</strong> Compliance for device data in healthcare and manufacturing</li>
                  <li><strong>Blockchain:</strong> Regulatory monitoring for crypto transactions and dApps</li>
                  <li><strong>ClimateTech:</strong> Carbon accounting compliance for SEC climate disclosure rules</li>
                  <li><strong>AI/ML:</strong> Ethical AI governance and compliance for sensitive data processing</li>
                </ul>
              </div>
            </div>
          `
        },
        {
          title: 'Onboarding Checklist',
          description: `
            <p>Follow these steps to successfully onboard as a NovaFuse partner:</p>
            <ol>
              <li>Complete partner application and sign partnership agreement</li>
              <li>Receive API keys and access to partner portal</li>
              <li>Complete technical integration using documentation and SDKs</li>
              <li>Validate integration through compliance testing</li>
              <li>Develop go-to-market strategy with NovaFuse team</li>
              <li>Launch and start generating revenue</li>
            </ol>
          `
        }
      ]
    },
    compliance: {
      title: 'Compliance Enablement',
      content: [
        {
          title: 'Supported Frameworks',
          description: `
            <p>NovaFuse supports 15+ compliance frameworks out of the box:</p>
            <ul>
              <li><strong>Healthcare:</strong> HIPAA, HITECH, GDPR (health data)</li>
              <li><strong>Financial:</strong> SOX, PCI DSS, GLBA</li>
              <li><strong>Security:</strong> SOC 2, ISO 27001, NIST CSF, CIS Controls</li>
              <li><strong>Privacy:</strong> GDPR, CCPA/CPRA, LGPD, PIPEDA</li>
              <li><strong>Government:</strong> FedRAMP, CMMC, StateRAMP</li>
            </ul>
          `
        },
        {
          title: 'Compliance Overlay Features',
          description: `
            <p>NovaFuse provides a comprehensive compliance overlay that includes:</p>
            <ul>
              <li>Real-time compliance monitoring and alerting</li>
              <li>Automated evidence collection and documentation</li>
              <li>Policy mapping and gap analysis</li>
              <li>Risk assessment and remediation recommendations</li>
              <li>Audit-ready reporting and dashboard</li>
            </ul>
          `
        },
        {
          title: 'Case Studies in Healthcare/Fintech/Legal',
          description: `
            <p>NovaFuse has enabled compliance success across various industries:</p>
            <ul>
              <li><strong>Healthcare:</strong> Enabled a telemedicine platform to achieve HIPAA compliance in 4 weeks, expanding their TAM by 300%</li>
              <li><strong>Fintech:</strong> Helped a payment processor meet PCI DSS requirements, reducing audit costs by 65%</li>
              <li><strong>Legal:</strong> Enabled a document management system to implement proper retention policies and access controls, opening doors to enterprise legal departments</li>
            </ul>
          `
        }
      ]
    },
    marketing: {
      title: 'Sales & Marketing Resources',
      content: [
        {
          title: 'One-Pagers',
          description: `
            <p>NovaFuse provides customizable one-pagers for different partner types and use cases:</p>
            <ul>
              <li>Partner program overview</li>
              <li>Technical integration guide</li>
              <li>Compliance framework summaries</li>
              <li>Industry-specific solutions</li>
              <li>ROI and business case builders</li>
            </ul>
          `
        },
        {
          title: 'Co-Branded Decks',
          description: `
            <p>NovaFuse offers co-branded presentation decks that can be customized for your audience:</p>
            <ul>
              <li>Partner overview presentation</li>
              <li>Technical deep dive</li>
              <li>Compliance framework specific presentations</li>
              <li>Industry vertical solutions</li>
              <li>Executive briefing materials</li>
            </ul>
          `
        },
        {
          title: 'ROI Calculators',
          description: `
            <p>NovaFuse provides ROI calculators to help quantify the value of compliance solutions:</p>
            <ul>
              <li>Cost savings calculator (audit preparation, manual work reduction)</li>
              <li>Revenue opportunity calculator (new markets, faster sales cycles)</li>
              <li>Risk reduction calculator (breach prevention, penalty avoidance)</li>
              <li>Time-to-value calculator (implementation speed vs. traditional methods)</li>
            </ul>
          `
        },
        {
          title: 'Webinar Recordings',
          description: `
            <p>Access recorded webinars on various compliance and partnership topics:</p>
            <ul>
              <li>Partner program overview</li>
              <li>Technical integration deep dives</li>
              <li>Compliance framework walkthroughs</li>
              <li>Industry-specific compliance solutions</li>
              <li>Partner success stories and case studies</li>
            </ul>
          `
        },
        {
          title: 'Press Coverage',
          description: `
            <p>NovaFuse has been featured in various publications and media outlets:</p>
            <ul>
              <li>Industry analyst reports and mentions</li>
              <li>Press releases about major partnerships</li>
              <li>Media coverage of compliance innovations</li>
              <li>Thought leadership articles and interviews</li>
            </ul>
          `
        }
      ]
    },
    revenue: {
      title: 'Revenue & Growth Tools',
      content: [
        {
          title: 'The NovaFuse Win/Win Revenue Sharing Model',
          description: `
            <div class="bg-gradient-to-r from-blue-700 to-purple-700 p-4 rounded-lg shadow-md border border-blue-400 mb-4">
              <h4 class="font-semibold text-white mb-2">Our Partnership Philosophy</h4>
              <p class="text-blue-100">Leveraging the Universal API Connector (UAC) and AI-driven compliance automation, this model incentivizes partners to drive adoption while rewarding mutual growth. At NovaFuse, we believe value should drive reward, not incumbency.</p>
            </div>

            <h4 class="font-semibold mb-2">Core Principles</h4>
            <ul class="mb-4">
              <li><strong>Aligned Incentives:</strong> Partners earn more as they drive value</li>
              <li><strong>Flexibility:</strong> Tailored structures for different partner types</li>
              <li><strong>Transparency:</strong> Real-time tracking via UAC-powered analytics</li>
            </ul>

            <h4 class="font-semibold mb-2">Revenue Sharing Tiers</h4>
            <p class="italic text-sm mb-2">Percentages represent partner's share of revenue</p>

            <div class="overflow-x-auto mb-4">
              <table class="w-full border-collapse">
                <thead>
                  <tr class="bg-blue-900">
                    <th class="border border-blue-700 p-2 text-left text-white">Partner Type</th>
                    <th class="border border-blue-700 p-2 text-left text-white">Model</th>
                    <th class="border border-blue-700 p-2 text-left text-white">Partner Earns</th>
                    <th class="border border-blue-700 p-2 text-left text-white">NovaFuse Keeps</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="bg-gray-800">
                    <td class="border border-blue-700 p-2">Integration Partners<br><span class="text-xs text-gray-400">(Zapier, AWS, GCP)</span></td>
                    <td class="border border-blue-700 p-2">Usage-Based</td>
                    <td class="border border-blue-700 p-2">15% of revenue<br><span class="text-xs text-gray-400">+5% bonus for >1K monthly integrations</span></td>
                    <td class="border border-blue-700 p-2">85%</td>
                  </tr>
                  <tr class="bg-gray-900">
                    <td class="border border-blue-700 p-2">GRC Tool Partners<br><span class="text-xs text-gray-400">(Drata, Vanta)</span></td>
                    <td class="border border-blue-700 p-2">Co-Sell Acceleration</td>
                    <td class="border border-blue-700 p-2">20% of joint deal value</td>
                    <td class="border border-blue-700 p-2">80%</td>
                  </tr>
                  <tr class="bg-gray-800">
                    <td class="border border-blue-700 p-2">Training/Certification<br><span class="text-xs text-gray-400">(ISACA, Coursera)</span></td>
                    <td class="border border-blue-700 p-2">Education Licensing</td>
                    <td class="border border-blue-700 p-2">30% of course revenue</td>
                    <td class="border border-blue-700 p-2">70%</td>
                  </tr>
                  <tr class="bg-gray-900">
                    <td class="border border-blue-700 p-2">Advisory/Consulting<br><span class="text-xs text-gray-400">(Deloitte, EY)</span></td>
                    <td class="border border-blue-700 p-2">Managed Service</td>
                    <td class="border border-blue-700 p-2">25% of client subscriptions</td>
                    <td class="border border-blue-700 p-2">75%</td>
                  </tr>
                  <tr class="bg-gray-800">
                    <td class="border border-blue-700 p-2">Resellers<br><span class="text-xs text-gray-400">(CDW, SHI)</span></td>
                    <td class="border border-blue-700 p-2">Tiered Margins</td>
                    <td class="border border-blue-700 p-2">20-30% based on volume</td>
                    <td class="border border-blue-700 p-2">70-80%</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <h4 class="font-semibold mb-2">Unique Value Incentives</h4>
            <ul class="mb-4">
              <li><strong>UAC Amplification Bonus:</strong> +5% recurring revenue for every net-new integration built</li>
              <li><strong>Compliance Velocity Rewards:</strong> 2% of deal value for each client achieving &lt;48-hour audit readiness</li>
              <li><strong>Co-Marketing Boost:</strong> +10% rev share for 90 days post-campaign</li>
              <li><strong>AI Adoption Incentive:</strong> 15% of upsell revenue from AI-powered features</li>
            </ul>

            <div class="bg-blue-900 p-4 rounded-lg mb-4">
              <h4 class="font-semibold text-white mb-2">Fairness Assessment</h4>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div class="bg-gradient-to-r from-blue-700 to-purple-700 p-3 rounded-lg shadow-md border border-blue-400">
                  <h5 class="font-semibold text-white mb-1">Partner Value: Fair</h5>
                  <p class="text-sm text-blue-100">Low risk, performance-based rewards, transparent tracking, with flexible options for businesses of all sizes.</p>
                </div>
                <div class="bg-gradient-to-r from-blue-700 to-purple-700 p-3 rounded-lg shadow-md border border-blue-400">
                  <h5 class="font-semibold text-white mb-1">NovaFuse Sustainability: Strong</h5>
                  <p class="text-sm text-blue-100">Healthy margins (70-85%), with caps to protect unit economics while still rewarding top performers.</p>
                </div>
                <div class="bg-gradient-to-r from-blue-700 to-purple-700 p-3 rounded-lg shadow-md border border-blue-400">
                  <h5 class="font-semibold text-white mb-1">Market Standards: Competitive</h5>
                  <p class="text-sm text-blue-100">Meets or exceeds industry benchmarks with unique performance incentives that differentiate from competitors.</p>
                </div>
              </div>
            </div>

            <div class="border-l-4 border-blue-400 pl-4 italic mb-4">
              <p>"As a boutique consultancy, I love that we can earn more than giants by driving real outcomes." – NovaFuse Partner</p>
              <p class="mt-2">"The transparency dashboard gives me confidence we'll get paid fairly—and fast." – Beta Partner Feedback</p>
            </div>

            <p>In a landscape where partner programs often feel extractive, NovaFuse offers a value-aligned, merit-driven model that shares the win. This isn't just partner-friendly—it's partnership-redefining.</p>
          `
        },
        {
          title: 'Invoicing & Payout Process',
          description: `
            <p>NovaFuse has a streamlined invoicing and payout process:</p>
            <ul>
              <li>Monthly revenue reporting through the partner portal</li>
              <li>Automated invoice generation based on revenue share</li>
              <li>Net-30 payment terms</li>
              <li>Multiple payment options (ACH, wire transfer, etc.)</li>
              <li>Detailed transaction history and reporting</li>
            </ul>
          `
        },
        {
          title: 'Go-to-Market Playbook',
          description: `
            <p>NovaFuse provides a comprehensive go-to-market playbook for partners:</p>
            <ul>
              <li>Target audience identification and messaging</li>
              <li>Sales enablement materials and training</li>
              <li>Marketing campaign templates and assets</li>
              <li>Lead generation strategies and tactics</li>
              <li>Success metrics and KPIs</li>
            </ul>
          `
        }
      ]
    },
    faq: {
      title: 'FAQs & Troubleshooting',
      content: [
        {
          title: 'Integration FAQs',
          description: `
            <p><strong>Q: How long does integration typically take?</strong><br>
            A: Most partners complete basic integration within 2-4 weeks, with more complex integrations taking 6-8 weeks.</p>

            <p><strong>Q: What technical resources do I need for integration?</strong><br>
            A: Typically, you'll need a developer familiar with REST APIs and your platform's integration capabilities.</p>

            <p><strong>Q: Do you provide integration support?</strong><br>
            A: Yes, all partners receive technical integration support, with Preferred Partners receiving dedicated integration assistance.</p>

            <p><strong>Q: Can we white-label the compliance features?</strong><br>
            A: Yes, our APIs and UIs can be white-labeled to match your brand and user experience.</p>
          `
        },
        {
          title: 'Common Objections (with rebuttals)',
          description: `
            <p><strong>Objection: "We already have our own compliance solution."</strong><br>
            Response: Most in-house solutions focus on specific frameworks or requirements. NovaFuse provides comprehensive coverage across 15+ frameworks, reducing the maintenance burden and ensuring you stay current with evolving regulations.</p>

            <p><strong>Objection: "Compliance isn't a priority for our customers."</strong><br>
            Response: While end-users may not explicitly ask for compliance, it's often a hidden blocker in enterprise sales cycles. By proactively addressing compliance, you can accelerate deals and expand into regulated markets.</p>

            <p><strong>Objection: "We're concerned about sharing customer data."</strong><br>
            Response: NovaFuse is designed with privacy and security in mind. Our architecture minimizes data sharing, and we offer deployment options that keep sensitive data within your environment.</p>
          `
        },
        {
          title: 'Compliance Myths Debunked',
          description: `
            <p><strong>Myth: "Compliance is just a checkbox exercise."</strong><br>
            Reality: Modern compliance is continuous and integrated into business processes. NovaFuse enables real-time compliance monitoring and adaptation to changing requirements.</p>

            <p><strong>Myth: "Small companies don't need to worry about compliance."</strong><br>
            Reality: Compliance requirements apply regardless of company size, and many regulations have specific provisions for smaller organizations. NovaFuse scales to fit organizations of all sizes.</p>

            <p><strong>Myth: "Cloud services can't be compliant with strict regulations."</strong><br>
            Reality: Cloud services can achieve high levels of compliance when properly configured and monitored. NovaFuse helps ensure your cloud deployments meet regulatory requirements.</p>
          `
        },
        {
          title: 'Troubleshooting Connection Errors',
          description: `
            <p>Common integration issues and solutions:</p>
            <ul>
              <li><strong>Authentication failures:</strong> Verify API keys and secrets, check token expiration</li>
              <li><strong>Rate limiting:</strong> Implement proper backoff strategies, optimize API usage</li>
              <li><strong>Data mapping errors:</strong> Review schema documentation, use validation endpoints</li>
              <li><strong>Webhook failures:</strong> Ensure endpoint availability, implement retry logic</li>
              <li><strong>Performance issues:</strong> Optimize queries, implement caching where appropriate</li>
            </ul>
            <p>For persistent issues, contact partner support at <a href="mailto:<EMAIL>" class="text-blue-400 hover:underline"><EMAIL></a></p>
          `
        }
      ]
    }
  };

  return (
    <div className="bg-secondary rounded-lg overflow-hidden">
      {/* Category Navigation */}
      <div className="flex flex-wrap border-b border-gray-700">
        {Object.keys(knowledgeBase).map((category) => (
          <button
            key={category}
            className={`px-4 py-3 text-sm font-medium ${
              activeCategory === category
                ? 'bg-gradient-to-r from-blue-700 to-purple-700 text-white shadow-md border-b-2 border-blue-400'
                : 'text-gray-300 hover:bg-gray-800'
            }`}
            onClick={() => setActiveCategory(category)}
          >
            {knowledgeBase[category].title}
          </button>
        ))}
      </div>

      {/* Content Area */}
      <div className="p-6">
        <h2 className="text-2xl font-bold mb-6">{knowledgeBase[activeCategory].title}</h2>

        <div className="space-y-8">
          {knowledgeBase[activeCategory].content.map((item, index) => (
            <div key={index} className="border-b border-gray-700 pb-6 last:border-0">
              <h3 className="text-xl font-semibold mb-3">{item.title}</h3>
              <div
                className="text-gray-300"
                dangerouslySetInnerHTML={{ __html: item.description }}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PartnerKnowledgeBase;
