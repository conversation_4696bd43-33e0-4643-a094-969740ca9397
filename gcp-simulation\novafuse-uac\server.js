const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const winston = require('winston');
const dotenv = require('dotenv');
const axios = require('axios');
const NodeCache = require('node-cache');

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Configure middleware
app.use(cors());
app.use(helmet());
app.use(express.json());
app.use(morgan('combined'));

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'novafuse-uac' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  logger.info('Connected to MongoDB successfully');
})
.catch(err => {
  logger.error('MongoDB connection error:', err);
  process.exit(1);
});

// Create a cache for API responses
const apiCache = new NodeCache({ stdTTL: 300, checkperiod: 60 });

// Define connector schema
const connectorSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  type: { type: String, required: true },
  baseUrl: { type: String, required: true },
  authType: { type: String, enum: ['none', 'basic', 'oauth2', 'apiKey'], required: true },
  authConfig: {
    username: { type: String },
    password: { type: String },
    apiKey: { type: String },
    apiKeyHeader: { type: String },
    clientId: { type: String },
    clientSecret: { type: String },
    tokenUrl: { type: String }
  },
  endpoints: [{
    name: { type: String, required: true },
    path: { type: String, required: true },
    method: { type: String, enum: ['GET', 'POST', 'PUT', 'DELETE'], required: true },
    requestMapping: { type: Object },
    responseMapping: { type: Object }
  }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const Connector = mongoose.model('Connector', connectorSchema);

// Define routes
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Connector management routes
app.get('/connectors', async (req, res) => {
  try {
    const connectors = await Connector.find().select('-authConfig.password -authConfig.apiKey -authConfig.clientSecret');
    res.json({ data: connectors });
  } catch (err) {
    logger.error('Error fetching connectors:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/connectors/:id', async (req, res) => {
  try {
    const connector = await Connector.findById(req.params.id).select('-authConfig.password -authConfig.apiKey -authConfig.clientSecret');
    if (!connector) {
      return res.status(404).json({ error: 'Connector not found' });
    }
    res.json({ data: connector });
  } catch (err) {
    logger.error('Error fetching connector:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/connectors', async (req, res) => {
  try {
    const connector = new Connector(req.body);
    await connector.save();
    res.status(201).json({ data: connector });
  } catch (err) {
    logger.error('Error creating connector:', err);
    res.status(400).json({ error: err.message });
  }
});

app.put('/connectors/:id', async (req, res) => {
  try {
    const connector = await Connector.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedAt: Date.now() },
      { new: true }
    );
    if (!connector) {
      return res.status(404).json({ error: 'Connector not found' });
    }
    res.json({ data: connector });
  } catch (err) {
    logger.error('Error updating connector:', err);
    res.status(400).json({ error: err.message });
  }
});

app.delete('/connectors/:id', async (req, res) => {
  try {
    const connector = await Connector.findByIdAndDelete(req.params.id);
    if (!connector) {
      return res.status(404).json({ error: 'Connector not found' });
    }
    res.json({ message: 'Connector deleted successfully' });
  } catch (err) {
    logger.error('Error deleting connector:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Connector execution routes
app.post('/execute/:connectorId/:endpointName', async (req, res) => {
  try {
    const { connectorId, endpointName } = req.params;
    const { params, body } = req.body;
    
    // Find the connector
    const connector = await Connector.findById(connectorId);
    if (!connector) {
      return res.status(404).json({ error: 'Connector not found' });
    }
    
    // Find the endpoint
    const endpoint = connector.endpoints.find(e => e.name === endpointName);
    if (!endpoint) {
      return res.status(404).json({ error: 'Endpoint not found' });
    }
    
    // Build the request URL
    let url = connector.baseUrl + endpoint.path;
    
    // Replace path parameters
    if (params) {
      Object.keys(params).forEach(key => {
        url = url.replace(`{${key}}`, params[key]);
      });
    }
    
    // Build the request headers
    const headers = {};
    
    // Add authentication
    if (connector.authType === 'basic') {
      const auth = Buffer.from(`${connector.authConfig.username}:${connector.authConfig.password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    } else if (connector.authType === 'apiKey') {
      headers[connector.authConfig.apiKeyHeader] = connector.authConfig.apiKey;
    } else if (connector.authType === 'oauth2') {
      // In a real implementation, this would handle OAuth2 token acquisition
      headers['Authorization'] = `Bearer simulation-oauth-token`;
    }
    
    // Execute the request
    const response = await axios({
      method: endpoint.method,
      url,
      headers,
      data: body
    });
    
    // Apply response mapping if defined
    let responseData = response.data;
    if (endpoint.responseMapping) {
      // In a real implementation, this would transform the response based on the mapping
      // For simulation, we'll just return the original response
      logger.info('Response mapping would be applied here');
    }
    
    res.json({ data: responseData });
  } catch (err) {
    logger.error('Error executing connector:', err);
    res.status(500).json({ error: 'Error executing connector', details: err.message });
  }
});

// Google Cloud Integration routes
app.get('/integrations/gcp/connectors', (req, res) => {
  res.json({
    data: [
      {
        id: 'gcp-scc',
        name: 'Google Security Command Center',
        description: 'Connector for Google Security Command Center',
        type: 'gcp',
        status: 'active'
      },
      {
        id: 'gcp-iam',
        name: 'Google Cloud IAM',
        description: 'Connector for Google Cloud IAM',
        type: 'gcp',
        status: 'active'
      },
      {
        id: 'gcp-bigquery',
        name: 'Google BigQuery',
        description: 'Connector for Google BigQuery',
        type: 'gcp',
        status: 'active'
      }
    ]
  });
});

// Sample connectors for demonstration
app.get('/sample-connectors', (req, res) => {
  res.json({
    data: [
      {
        name: 'Google Security Command Center',
        description: 'Connector for Google Security Command Center',
        type: 'gcp',
        baseUrl: 'http://security-command-center:8081',
        authType: 'oauth2',
        authConfig: {
          clientId: 'simulation-client-id',
          clientSecret: 'simulation-client-secret',
          tokenUrl: 'http://security-command-center:8081/token'
        },
        endpoints: [
          {
            name: 'listFindings',
            path: '/v1/organizations/{organizationId}/sources/{sourceId}/findings',
            method: 'GET',
            requestMapping: {},
            responseMapping: {}
          },
          {
            name: 'createFinding',
            path: '/v1/organizations/{organizationId}/sources/{sourceId}/findings',
            method: 'POST',
            requestMapping: {},
            responseMapping: {}
          }
        ]
      },
      {
        name: 'Google Cloud IAM',
        description: 'Connector for Google Cloud IAM',
        type: 'gcp',
        baseUrl: 'http://cloud-iam:8082',
        authType: 'oauth2',
        authConfig: {
          clientId: 'simulation-client-id',
          clientSecret: 'simulation-client-secret',
          tokenUrl: 'http://cloud-iam:8082/token'
        },
        endpoints: [
          {
            name: 'listRoles',
            path: '/v1/projects/{projectId}/roles',
            method: 'GET',
            requestMapping: {},
            responseMapping: {}
          },
          {
            name: 'getPolicy',
            path: '/v1/projects/{projectId}:getIamPolicy',
            method: 'POST',
            requestMapping: {},
            responseMapping: {}
          }
        ]
      },
      {
        name: 'Google BigQuery',
        description: 'Connector for Google BigQuery',
        type: 'gcp',
        baseUrl: 'http://bigquery:8083',
        authType: 'oauth2',
        authConfig: {
          clientId: 'simulation-client-id',
          clientSecret: 'simulation-client-secret',
          tokenUrl: 'http://bigquery:8083/token'
        },
        endpoints: [
          {
            name: 'runQuery',
            path: '/v2/projects/{projectId}/queries',
            method: 'POST',
            requestMapping: {},
            responseMapping: {}
          },
          {
            name: 'getDataset',
            path: '/v2/projects/{projectId}/datasets/{datasetId}',
            method: 'GET',
            requestMapping: {},
            responseMapping: {}
          }
        ]
      }
    ]
  });
});

// Start the server
const PORT = process.env.PORT || 3002;
app.listen(PORT, () => {
  logger.info(`Server running on port ${PORT}`);
  console.log(`NovaConnect UAC running on http://localhost:${PORT}`);
});

module.exports = app;
