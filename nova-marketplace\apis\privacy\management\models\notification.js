/**
 * Notification Model
 * 
 * Represents a notification related to privacy management activities,
 * such as new data subject requests, consent withdrawals, or data breaches.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const notificationSchema = new Schema({
  recipient: {
    type: String,
    required: true,
    trim: true
  },
  subject: {
    type: String,
    required: true,
    trim: true
  },
  content: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    required: true,
    enum: ['dsr', 'consent', 'breach', 'compliance', 'system', 'other'],
    trim: true
  },
  priority: {
    type: String,
    required: true,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'sent', 'delivered', 'read', 'failed'],
    default: 'pending'
  },
  channel: {
    type: String,
    required: true,
    enum: ['email', 'sms', 'push', 'in-app', 'slack', 'teams', 'other'],
    default: 'email'
  },
  relatedEntityType: {
    type: String,
    enum: ['subject-request', 'consent-record', 'data-breach', 'privacy-notice', 'integration', 'other'],
    trim: true
  },
  relatedEntityId: {
    type: String,
    trim: true
  },
  metadata: {
    type: Object
  },
  sentAt: {
    type: Date
  },
  deliveredAt: {
    type: Date
  },
  readAt: {
    type: Date
  },
  failureReason: {
    type: String,
    trim: true
  },
  retryCount: {
    type: Number,
    default: 0
  },
  nextRetryAt: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create a text index for searching
notificationSchema.index({
  recipient: 'text',
  subject: 'text',
  content: 'text'
});

// Pre-save hook to update the updatedAt field
notificationSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual for age of the notification
notificationSchema.virtual('age').get(function() {
  const now = new Date();
  const createdAt = new Date(this.createdAt);
  const diffTime = now - createdAt;
  const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
  return diffHours;
});

// Virtual for delivery status
notificationSchema.virtual('deliveryStatus').get(function() {
  if (this.status === 'read') return 'read';
  if (this.status === 'delivered') return 'delivered';
  if (this.status === 'sent') return 'sent';
  if (this.status === 'failed') {
    if (this.retryCount < 3 && this.nextRetryAt && new Date(this.nextRetryAt) > new Date()) {
      return 'retry-scheduled';
    }
    return 'failed';
  }
  return 'pending';
});

const Notification = mongoose.model('Notification', notificationSchema);

module.exports = Notification;
