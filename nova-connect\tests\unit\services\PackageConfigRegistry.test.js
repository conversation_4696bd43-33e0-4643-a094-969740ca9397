/**
 * Package Configuration Registry Tests
 */

const PackageConfigRegistry = require('../../../api/services/PackageConfigRegistry');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

describe('PackageConfigRegistry', () => {
  let packageRegistry;
  let testDir;
  
  beforeEach(async () => {
    // Create a temporary directory for testing
    testDir = path.join(os.tmpdir(), `nova-connect-test-${Date.now()}`);
    await fs.mkdir(testDir, { recursive: true });
    
    // Initialize the package registry with the test directory
    packageRegistry = new PackageConfigRegistry(testDir);
    
    // Wait for the data directory to be created
    await new Promise(resolve => setTimeout(resolve, 100));
  });
  
  afterEach(async () => {
    // Clean up the test directory
    try {
      await fs.rm(testDir, { recursive: true, force: true });
    } catch (error) {
      console.error('Error cleaning up test directory:', error);
    }
  });
  
  test('should initialize with default packages', async () => {
    const packages = await packageRegistry.getAllPackages();
    
    expect(packages).toHaveLength(4);
    expect(packages[0].id).toBe('core');
    expect(packages[1].id).toBe('secure');
    expect(packages[2].id).toBe('enterprise');
    expect(packages[3].id).toBe('ai_boost');
  });
  
  test('should get package by ID', async () => {
    const pkg = await packageRegistry.getPackageById('core');
    
    expect(pkg).toBeDefined();
    expect(pkg.id).toBe('core');
    expect(pkg.name).toBe('NovaConnect Core');
    expect(pkg.features).toContain('core.basic_connectors');
  });
  
  test('should create a new package', async () => {
    const newPackage = {
      id: 'test-package',
      name: 'Test Package',
      description: 'A test package',
      tier: 'test',
      features: ['test.feature1', 'test.feature2'],
      limits: {
        connections: 5,
        operations_per_day: 500
      }
    };
    
    await packageRegistry.createPackage(newPackage);
    
    const pkg = await packageRegistry.getPackageById('test-package');
    
    expect(pkg).toBeDefined();
    expect(pkg.id).toBe('test-package');
    expect(pkg.name).toBe('Test Package');
    expect(pkg.features).toContain('test.feature1');
  });
  
  test('should update a package', async () => {
    const updateData = {
      name: 'Updated Core Package',
      description: 'Updated description'
    };
    
    await packageRegistry.updatePackage('core', updateData);
    
    const pkg = await packageRegistry.getPackageById('core');
    
    expect(pkg).toBeDefined();
    expect(pkg.name).toBe('Updated Core Package');
    expect(pkg.description).toBe('Updated description');
  });
  
  test('should delete a package', async () => {
    await packageRegistry.deletePackage('core');
    
    await expect(packageRegistry.getPackageById('core')).rejects.toThrow('Package with ID core not found');
  });
  
  test('should set and get tenant mapping', async () => {
    await packageRegistry.setTenantMapping('test-tenant', 'enterprise', ['custom.feature1'], { connections: 200 });
    
    const mapping = await packageRegistry.getTenantMapping('test-tenant');
    
    expect(mapping).toBeDefined();
    expect(mapping.tenantId).toBe('test-tenant');
    expect(mapping.packageId).toBe('enterprise');
    expect(mapping.customFeatures).toContain('custom.feature1');
    expect(mapping.customLimits.connections).toBe(200);
  });
  
  test('should check if tenant has feature access', async () => {
    // Set tenant mapping
    await packageRegistry.setTenantMapping('test-tenant', 'secure', ['custom.feature1'], {});
    
    // Check access to package feature
    const hasAccessToPackageFeature = await packageRegistry.hasTenantFeatureAccess('test-tenant', 'security.encryption');
    expect(hasAccessToPackageFeature).toBe(true);
    
    // Check access to custom feature
    const hasAccessToCustomFeature = await packageRegistry.hasTenantFeatureAccess('test-tenant', 'custom.feature1');
    expect(hasAccessToCustomFeature).toBe(true);
    
    // Check access to feature not in package or custom features
    const hasAccessToOtherFeature = await packageRegistry.hasTenantFeatureAccess('test-tenant', 'enterprise.advanced_connectors');
    expect(hasAccessToOtherFeature).toBe(false);
  });
  
  test('should get tenant feature limit', async () => {
    // Set tenant mapping
    await packageRegistry.setTenantMapping('test-tenant', 'secure', [], { connections: 200 });
    
    // Check custom limit
    const customLimit = await packageRegistry.getTenantFeatureLimit('test-tenant', 'connections');
    expect(customLimit).toBe(200);
    
    // Check package limit
    const packageLimit = await packageRegistry.getTenantFeatureLimit('test-tenant', 'operations_per_day');
    expect(packageLimit).toBe(5000);
    
    // Check non-existent limit
    const nonExistentLimit = await packageRegistry.getTenantFeatureLimit('test-tenant', 'non_existent_limit');
    expect(nonExistentLimit).toBeNull();
  });
  
  test('should get tenant available features', async () => {
    // Set tenant mapping
    await packageRegistry.setTenantMapping('test-tenant', 'secure', ['custom.feature1'], {});
    
    // Get available features
    const features = await packageRegistry.getTenantAvailableFeatures('test-tenant');
    
    expect(features).toContain('core.basic_connectors');
    expect(features).toContain('security.encryption');
    expect(features).toContain('custom.feature1');
    expect(features).not.toContain('enterprise.advanced_connectors');
  });
  
  test('should use cache for repeated calls', async () => {
    // Mock the loadData method to track calls
    const originalLoadData = packageRegistry.loadData;
    const loadDataMock = jest.fn().mockImplementation(originalLoadData);
    packageRegistry.loadData = loadDataMock;
    
    // First call should hit the file system
    await packageRegistry.getAllPackages();
    expect(loadDataMock).toHaveBeenCalledTimes(1);
    
    // Second call should use cache
    await packageRegistry.getAllPackages();
    expect(loadDataMock).toHaveBeenCalledTimes(1);
    
    // Clear cache
    packageRegistry.clearCache();
    
    // After clearing cache, should hit file system again
    await packageRegistry.getAllPackages();
    expect(loadDataMock).toHaveBeenCalledTimes(2);
    
    // Restore original method
    packageRegistry.loadData = originalLoadData;
  });
});
