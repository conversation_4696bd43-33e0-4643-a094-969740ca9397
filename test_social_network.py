#!/usr/bin/env python3
"""
Test script for UUFT Social Network generation
"""

import os
import logging
import networkx as nx
import matplotlib.pyplot as plt
from uuft_social_analyzer import UUFTSocialNetwork

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_social.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('Test_Social')

# Constants
RESULTS_DIR = "uuft_results/social"
os.makedirs(RESULTS_DIR, exist_ok=True)

def test_social_network():
    """Test the social network generation."""
    logger.info("Testing social network generation")
    
    # Create a small-world network
    network = UUFTSocialNetwork(
        num_nodes=50,
        network_type="small_world",
        uuft_bias=0.5
    )
    
    # Get network stats
    stats = network.get_network_stats()
    logger.info(f"Network stats: {stats}")
    
    # Visualize network
    network.visualize_network(
        highlight_1882=True,
        save_path=os.path.join(RESULTS_DIR, "test_network.png")
    )
    
    logger.info("Social network test completed successfully")

if __name__ == "__main__":
    test_social_network()
