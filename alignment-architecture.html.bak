<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3–6–9–12–13 Alignment Architecture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .diagram-title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 10px;
            color: #333;
        }
        .diagram-subtitle {
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
            font-style: italic;
        }
        .level-box {
            border: 2px solid #555;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f8f8f8;
        }
        .level-title {
            font-size: 18px;
            margin-bottom: 15px;
            color: #444;
            text-align: center;
        }
        .items-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }
        .item {
            padding: 8px 12px;
            border: 1px solid #666;
            border-radius: 4px;
            background-color: white;
            font-size: 14px;
        }
        .inventor {
            text-align: right;
            font-style: italic;
            margin-top: 30px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="diagram-container">
        <h1 class="diagram-title">3–6–9–12–13 ALIGNMENT ARCHITECTURE</h1>
        <h2 class="diagram-subtitle">Structured Emergence of Value-Based Intelligence</h2>
        
        <div class="level-box">
            <h3 class="level-title">3 – Foundational Pillars</h3>
            <div class="items-container">
                <div class="item">Truth</div>
                <div class="item">Trust</div>
                <div class="item">Transparency</div>
            </div>
        </div>
        
        <div class="level-box">
            <h3 class="level-title">6 – Core Capacities</h3>
            <div class="items-container">
                <div class="item">Awareness</div>
                <div class="item">Intelligence</div>
                <div class="item">Control</div>
                <div class="item">Resilience</div>
                <div class="item">Adaptability</div>
                <div class="item">Responsibility</div>
            </div>
        </div>
        
        <div class="level-box">
            <h3 class="level-title">9 – Operational Engines</h3>
            <div class="items-container">
                <div class="item">Risk Analysis</div>
                <div class="item">Policy Automation</div>
                <div class="item">Identity Flow</div>
                <div class="item">Audit Trail</div>
                <div class="item">Knowledge Sync</div>
                <div class="item">AI Guidance</div>
                <div class="item">GRC Mapping</div>
                <div class="item">User Training</div>
                <div class="item">Threat Detection</div>
            </div>
        </div>
        
        <div class="level-box">
            <h3 class="level-title">12 – Integration Points</h3>
            <div class="items-container">
                <div class="item">APIs</div>
                <div class="item">Compliance Modules</div>
                <div class="item">Data Gateways</div>
                <div class="item">Third-Party Connectors</div>
                <div class="item">Logging Systems</div>
                <div class="item">Reports</div>
                <div class="item">Real-Time Monitoring</div>
                <div class="item">Feedback Channels</div>
                <div class="item">User Interfaces</div>
                <div class="item">Team Roles</div>
                <div class="item">Workflows</div>
                <div class="item">Security Zones</div>
            </div>
        </div>
        
        <div class="level-box">
            <h3 class="level-title">13 – NovaFuse Alignment</h3>
            <div class="items-container">
                <div class="item">All Universal Components Activated in Harmony</div>
            </div>
        </div>
        
        <div class="inventor">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
