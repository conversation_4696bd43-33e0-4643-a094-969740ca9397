const request = require('supertest');
const app = require('../../../server');

describe('API Lifecycle API', () => {
  // Test GET /api-management/lifecycle/apis
  describe('GET /api-management/lifecycle/apis', () => {
    it('should return a list of APIs', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
    
    it('should filter APIs by status', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis?status=production')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(api => api.status === 'production')).toBe(true);
    });
    
    it('should filter APIs by type', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis?type=rest')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(api => api.type === 'rest')).toBe(true);
    });
    
    it('should paginate results', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis?page=1&limit=2')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeLessThanOrEqual(2);
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(2);
    });
  });
  
  // Test GET /api-management/lifecycle/apis/:id
  describe('GET /api-management/lifecycle/apis/:id', () => {
    it('should return a specific API', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis/api-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('api-001');
    });
    
    it('should return 404 for non-existent API', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test POST /api-management/lifecycle/apis
  describe('POST /api-management/lifecycle/apis', () => {
    it('should create a new API', async () => {
      const newAPI = {
        name: 'Test API',
        description: 'This is a test API',
        version: '1.0.0',
        status: 'development',
        type: 'rest',
        owner: 'Test Team',
        team: 'Engineering',
        baseUrl: 'https://api.example.com/test',
        documentation: 'https://docs.example.com/test',
        repository: 'https://github.com/example/test-api',
        tags: ['test', 'example']
      };
      
      const response = await request(app)
        .post('/api-management/lifecycle/apis')
        .set('apikey', 'test-api-key')
        .send(newAPI);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.name).toBe(newAPI.name);
      expect(response.body.data.version).toBe(newAPI.version);
      expect(response.body.data).toHaveProperty('id');
    });
    
    it('should return 400 for invalid input', async () => {
      const invalidAPI = {
        // Missing required fields
        description: 'This is an invalid API'
      };
      
      const response = await request(app)
        .post('/api-management/lifecycle/apis')
        .set('apikey', 'test-api-key')
        .send(invalidAPI);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test PUT /api-management/lifecycle/apis/:id
  describe('PUT /api-management/lifecycle/apis/:id', () => {
    it('should update an existing API', async () => {
      const updateData = {
        name: 'Updated API',
        status: 'testing'
      };
      
      const response = await request(app)
        .put('/api-management/lifecycle/apis/api-001')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.name).toBe(updateData.name);
      expect(response.body.data.status).toBe(updateData.status);
    });
    
    it('should return 404 for non-existent API', async () => {
      const updateData = {
        name: 'Updated API'
      };
      
      const response = await request(app)
        .put('/api-management/lifecycle/apis/non-existent-id')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test DELETE /api-management/lifecycle/apis/:id
  describe('DELETE /api-management/lifecycle/apis/:id', () => {
    it('should delete an existing API', async () => {
      const response = await request(app)
        .delete('/api-management/lifecycle/apis/api-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 404 for non-existent API', async () => {
      const response = await request(app)
        .delete('/api-management/lifecycle/apis/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /api-management/lifecycle/apis/:id/versions
  describe('GET /api-management/lifecycle/apis/:id/versions', () => {
    it('should return versions for a specific API', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis/api-002/versions')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
    
    it('should return 404 for non-existent API', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis/non-existent-id/versions')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test POST /api-management/lifecycle/apis/:id/versions
  describe('POST /api-management/lifecycle/apis/:id/versions', () => {
    it('should add a version to an API', async () => {
      const newVersion = {
        version: '2.0.0',
        status: 'development',
        releaseDate: null,
        endOfLifeDate: null,
        changeLog: 'Major update with new features',
        baseUrl: 'https://api.example.com/v2/payments',
        documentation: 'https://docs.example.com/apis/payments/v2'
      };
      
      const response = await request(app)
        .post('/api-management/lifecycle/apis/api-002/versions')
        .set('apikey', 'test-api-key')
        .send(newVersion);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.version).toBe(newVersion.version);
      expect(response.body.data.status).toBe(newVersion.status);
      expect(response.body.data).toHaveProperty('id');
    });
    
    it('should return 400 for invalid input', async () => {
      const invalidVersion = {
        // Missing required fields
        changeLog: 'Invalid version'
      };
      
      const response = await request(app)
        .post('/api-management/lifecycle/apis/api-002/versions')
        .set('apikey', 'test-api-key')
        .send(invalidVersion);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 404 for non-existent API', async () => {
      const newVersion = {
        version: '2.0.0',
        status: 'development',
        changeLog: 'Major update with new features'
      };
      
      const response = await request(app)
        .post('/api-management/lifecycle/apis/non-existent-id/versions')
        .set('apikey', 'test-api-key')
        .send(newVersion);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /api-management/lifecycle/apis/:id/versions/:versionId
  describe('GET /api-management/lifecycle/apis/:id/versions/:versionId', () => {
    it('should return a specific API version', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis/api-001/versions/ver-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('ver-001');
    });
    
    it('should return 404 for non-existent API', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis/non-existent-id/versions/ver-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 404 for non-existent version', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis/api-001/versions/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test PUT /api-management/lifecycle/apis/:id/versions/:versionId
  describe('PUT /api-management/lifecycle/apis/:id/versions/:versionId', () => {
    it('should update an API version', async () => {
      const updateData = {
        status: 'deprecated',
        endOfLifeDate: '2025-01-01'
      };
      
      const response = await request(app)
        .put('/api-management/lifecycle/apis/api-001/versions/ver-001')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.status).toBe(updateData.status);
      expect(response.body.data.endOfLifeDate).toBe(updateData.endOfLifeDate);
    });
    
    it('should return 404 for non-existent API', async () => {
      const updateData = {
        status: 'deprecated'
      };
      
      const response = await request(app)
        .put('/api-management/lifecycle/apis/non-existent-id/versions/ver-001')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 404 for non-existent version', async () => {
      const updateData = {
        status: 'deprecated'
      };
      
      const response = await request(app)
        .put('/api-management/lifecycle/apis/api-001/versions/non-existent-id')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /api-management/lifecycle/apis/:id/dependencies
  describe('GET /api-management/lifecycle/apis/:id/dependencies', () => {
    it('should return dependencies for a specific API', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis/api-001/dependencies')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
    
    it('should return 404 for non-existent API', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis/non-existent-id/dependencies')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test POST /api-management/lifecycle/apis/:id/dependencies
  describe('POST /api-management/lifecycle/apis/:id/dependencies', () => {
    it('should add a dependency to an API', async () => {
      const newDependency = {
        dependencyType: 'library',
        name: 'Test Library',
        version: '1.2.3',
        description: 'A test library dependency',
        criticality: 'medium'
      };
      
      const response = await request(app)
        .post('/api-management/lifecycle/apis/api-001/dependencies')
        .set('apikey', 'test-api-key')
        .send(newDependency);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.name).toBe(newDependency.name);
      expect(response.body.data.dependencyType).toBe(newDependency.dependencyType);
      expect(response.body.data).toHaveProperty('id');
    });
    
    it('should return 400 for invalid input', async () => {
      const invalidDependency = {
        // Missing required fields
        description: 'Invalid dependency'
      };
      
      const response = await request(app)
        .post('/api-management/lifecycle/apis/api-001/dependencies')
        .set('apikey', 'test-api-key')
        .send(invalidDependency);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 404 for non-existent API', async () => {
      const newDependency = {
        dependencyType: 'library',
        name: 'Test Library'
      };
      
      const response = await request(app)
        .post('/api-management/lifecycle/apis/non-existent-id/dependencies')
        .set('apikey', 'test-api-key')
        .send(newDependency);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /api-management/lifecycle/apis/:id/consumers
  describe('GET /api-management/lifecycle/apis/:id/consumers', () => {
    it('should return consumers for a specific API', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis/api-001/consumers')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
    
    it('should return 404 for non-existent API', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/apis/non-existent-id/consumers')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test POST /api-management/lifecycle/apis/:id/consumers
  describe('POST /api-management/lifecycle/apis/:id/consumers', () => {
    it('should add a consumer to an API', async () => {
      const newConsumer = {
        name: 'Test Consumer',
        type: 'internal',
        contact: '<EMAIL>',
        usageLevel: 'medium',
        apiVersions: ['2.0.0']
      };
      
      const response = await request(app)
        .post('/api-management/lifecycle/apis/api-001/consumers')
        .set('apikey', 'test-api-key')
        .send(newConsumer);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.name).toBe(newConsumer.name);
      expect(response.body.data.type).toBe(newConsumer.type);
      expect(response.body.data).toHaveProperty('id');
    });
    
    it('should return 400 for invalid input', async () => {
      const invalidConsumer = {
        // Missing required fields
        contact: '<EMAIL>'
      };
      
      const response = await request(app)
        .post('/api-management/lifecycle/apis/api-001/consumers')
        .set('apikey', 'test-api-key')
        .send(invalidConsumer);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 404 for non-existent API', async () => {
      const newConsumer = {
        name: 'Test Consumer',
        type: 'internal'
      };
      
      const response = await request(app)
        .post('/api-management/lifecycle/apis/non-existent-id/consumers')
        .set('apikey', 'test-api-key')
        .send(newConsumer);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /api-management/lifecycle/types
  describe('GET /api-management/lifecycle/types', () => {
    it('should return a list of API types', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/types')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0]).toHaveProperty('id');
      expect(response.body.data[0]).toHaveProperty('name');
      expect(response.body.data[0]).toHaveProperty('description');
    });
  });
  
  // Test GET /api-management/lifecycle/statuses
  describe('GET /api-management/lifecycle/statuses', () => {
    it('should return a list of API statuses', async () => {
      const response = await request(app)
        .get('/api-management/lifecycle/statuses')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0]).toHaveProperty('id');
      expect(response.body.data[0]).toHaveProperty('name');
      expect(response.body.data[0]).toHaveProperty('description');
    });
  });
});
