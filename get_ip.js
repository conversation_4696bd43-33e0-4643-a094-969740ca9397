const os = require('os');
const interfaces = os.networkInterfaces();
const results = {};

for (const name of Object.keys(interfaces)) {
  for (const iface of interfaces[name]) {
    // Skip over non-IPv4 and internal (loopback) addresses
    if (iface.family === 'IPv4' && !iface.internal) {
      if (!results[name]) {
        results[name] = [];
      }
      results[name].push(iface.address);
    }
  }
}

console.log('Your local IP addresses:');
console.log(JSON.stringify(results, null, 2));
console.log('\nShare these URLs with your team:');
Object.keys(results).forEach(name => {
  results[name].forEach(ip => {
    console.log(`Main site: http://${ip}:3000`);
    console.log(`UAC demo: http://${ip}:3030`);
  });
});
