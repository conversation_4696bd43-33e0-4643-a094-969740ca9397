{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 1, "numPassedTests": 1, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 1, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1745774709882, "success": false, "testResults": [{"leaks": false, "numFailingTests": 0, "numPassingTests": 1, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1745774713379, "runtime": 2117, "slow": false, "start": 1745774711262}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\unit\\novatrack\\tracking-manager.simple.test.js", "testResults": [{"ancestorTitles": ["NovaTrack TrackingManager - Simple Tests", "create_requirement"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "NovaTrack TrackingManager - Simple Tests create_requirement should create a new requirement with valid data", "invocations": 1, "location": null, "numPassingAsserts": 13, "retryReasons": [], "status": "passed", "title": "should create a new requirement with valid data"}], "failureMessage": null}], "wasInterrupted": false}