import React, { useState } from 'react';

const UACInfographic = () => {
  const [activeTab, setActiveTab] = useState('connector'); // 'connector' or 'compliance'

  return (
    <div className="uac-infographic-container bg-blue-900 rounded-lg overflow-hidden shadow-xl border border-blue-700 mb-8">
      {/* Header */}
      <div className="uac-header bg-gradient-to-r from-blue-800 to-purple-900 p-6 text-center">
        <h2 className="text-2xl md:text-3xl font-bold mb-2">The Dual Power of UAC</h2>
        <p className="text-lg opacity-90">One acronym, two complementary meanings, infinite possibilities</p>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-blue-700">
        <button 
          className={`flex-1 py-3 px-4 text-center font-semibold transition-all duration-200 ${activeTab === 'connector' ? 'bg-blue-800 text-white' : 'bg-blue-900 text-blue-300 hover:bg-blue-800'}`}
          onClick={() => setActiveTab('connector')}
        >
          Universal API Connector
        </button>
        <button 
          className={`flex-1 py-3 px-4 text-center font-semibold transition-all duration-200 ${activeTab === 'compliance' ? 'bg-blue-800 text-white' : 'bg-blue-900 text-blue-300 hover:bg-blue-800'}`}
          onClick={() => setActiveTab('compliance')}
        >
          Unified AI Compliance
        </button>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Two Sections */}
        <div className="uac-segment grid grid-cols-1 md:grid-cols-2 gap-6">
          {activeTab === 'connector' ? (
            <>
              {/* Left Section - Universal API Connector */}
              <div className="uac-left bg-blue-800 bg-opacity-50 p-5 rounded-lg border border-blue-600">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-blue-700 flex items-center justify-center mr-4">
                    <span className="text-2xl">🔌</span>
                  </div>
                  <h3 className="text-xl font-bold">Universal API Connector</h3>
                </div>
                <p className="mb-4">The connectivity layer that seamlessly integrates disparate systems, translating between different APIs and ensuring data flows securely across your enterprise.</p>
                
                <h4 className="font-semibold mb-2 text-blue-300">Key Capabilities:</h4>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">✓</span>
                    <span>Connect to any API regardless of type (REST, GraphQL, SOAP)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">✓</span>
                    <span>Transform data between formats seamlessly</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">✓</span>
                    <span>Secure data exchange with encryption and authentication</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">✓</span>
                    <span>Plug & Play integration at enterprise scale</span>
                  </li>
                </ul>
              </div>

              {/* Right Section - How It Works */}
              <div className="uac-right">
                <h3 className="text-xl font-bold mb-4">How It Works</h3>
                <div className="space-y-4">
                  <div className="bg-blue-800 bg-opacity-30 p-4 rounded-lg border-l-4 border-blue-500">
                    <h4 className="font-semibold mb-1">1. Connect</h4>
                    <p className="text-sm">The UAC establishes secure connections to any API using a universal adapter framework that supports all major protocols and authentication methods.</p>
                  </div>
                  <div className="bg-blue-800 bg-opacity-30 p-4 rounded-lg border-l-4 border-blue-500">
                    <h4 className="font-semibold mb-1">2. Transform</h4>
                    <p className="text-sm">Data is normalized through the Semantic Translation Layer, which understands field meanings beyond just formats, enabling seamless data exchange.</p>
                  </div>
                  <div className="bg-blue-800 bg-opacity-30 p-4 rounded-lg border-l-4 border-blue-500">
                    <h4 className="font-semibold mb-1">3. Secure</h4>
                    <p className="text-sm">Zero Trust security model verifies every connection and data transfer, with comprehensive audit trails and encryption at rest and in transit.</p>
                  </div>
                  <div className="bg-blue-800 bg-opacity-30 p-4 rounded-lg border-l-4 border-blue-500">
                    <h4 className="font-semibold mb-1">4. Orchestrate</h4>
                    <p className="text-sm">Complex workflows across multiple systems are managed through the orchestration layer, enabling sophisticated business processes.</p>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <>
              {/* Left Section - Unified AI Compliance */}
              <div className="uac-left bg-blue-800 bg-opacity-50 p-5 rounded-lg border border-blue-600">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-blue-700 flex items-center justify-center mr-4">
                    <span className="text-2xl">🧠</span>
                  </div>
                  <h3 className="text-xl font-bold">Unified AI Compliance</h3>
                </div>
                <p className="mb-4">The intelligence layer that continuously monitors, interprets, predicts, and adapts to evolving compliance requirements across multiple frameworks in real-time.</p>
                
                <h4 className="font-semibold mb-2 text-blue-300">Key Capabilities:</h4>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">✓</span>
                    <span>Real-Time Risk Interception before violations occur</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">✓</span>
                    <span>Explainable Compliance Intelligence (XAI)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">✓</span>
                    <span>Self-Updating Compliance Frameworks</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">✓</span>
                    <span>Cross-Border Compliance Handling</span>
                  </li>
                </ul>
              </div>

              {/* Right Section - How It Works */}
              <div className="uac-right">
                <h3 className="text-xl font-bold mb-4">How It Works</h3>
                <div className="space-y-4">
                  <div className="bg-blue-800 bg-opacity-30 p-4 rounded-lg border-l-4 border-purple-500">
                    <h4 className="font-semibold mb-1">1. Ingest Rules</h4>
                    <p className="text-sm">The UAC absorbs regulatory frameworks (HIPAA, GDPR, SOX, NIST), industry standards, and internal policies, creating a comprehensive compliance knowledge base.</p>
                  </div>
                  <div className="bg-blue-800 bg-opacity-30 p-4 rounded-lg border-l-4 border-purple-500">
                    <h4 className="font-semibold mb-1">2. Monitor & Predict</h4>
                    <p className="text-sm">AI continuously monitors data flows and system behaviors, predicting potential compliance issues before they occur and taking preventive action.</p>
                  </div>
                  <div className="bg-blue-800 bg-opacity-30 p-4 rounded-lg border-l-4 border-purple-500">
                    <h4 className="font-semibold mb-1">3. Explain & Report</h4>
                    <p className="text-sm">XAI provides clear explanations for compliance decisions, generating audit-ready reports with contextual metadata for every action taken.</p>
                  </div>
                  <div className="bg-blue-800 bg-opacity-30 p-4 rounded-lg border-l-4 border-purple-500">
                    <h4 className="font-semibold mb-1">4. Adapt & Learn</h4>
                    <p className="text-sm">The system continuously improves through machine learning, adapting to new regulations, evolving threats, and organization-specific compliance patterns.</p>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Benefits */}
        <div className="uac-benefits mt-8">
          <h3 className="text-xl font-bold mb-4 text-center">The Power of Integration</h3>
          <p className="text-center mb-6">When Universal API Connector meets Unified AI Compliance, organizations gain:</p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-blue-800 bg-opacity-40 p-4 rounded-lg text-center">
              <div className="text-3xl mb-2">🛡️</div>
              <h4 className="font-semibold mb-1">Proactive Protection</h4>
              <p className="text-sm">Prevent compliance violations before they occur</p>
            </div>
            <div className="bg-blue-800 bg-opacity-40 p-4 rounded-lg text-center">
              <div className="text-3xl mb-2">⚡</div>
              <h4 className="font-semibold mb-1">Accelerated Innovation</h4>
              <p className="text-sm">Deploy new systems with built-in compliance</p>
            </div>
            <div className="bg-blue-800 bg-opacity-40 p-4 rounded-lg text-center">
              <div className="text-3xl mb-2">📊</div>
              <h4 className="font-semibold mb-1">Unified Governance</h4>
              <p className="text-sm">One system for all compliance needs</p>
            </div>
            <div className="bg-blue-800 bg-opacity-40 p-4 rounded-lg text-center">
              <div className="text-3xl mb-2">💰</div>
              <h4 className="font-semibold mb-1">Cost Reduction</h4>
              <p className="text-sm">Lower integration and compliance costs</p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="uac-footer mt-8 text-center">
          <p className="italic">
            "The UAC is not just a product. It's a platform. A movement. A paradigm shift in how organizations approach connectivity and compliance."
          </p>
        </div>
      </div>
    </div>
  );
};

export default UACInfographic;
