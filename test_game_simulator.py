#!/usr/bin/env python3
"""
Test script for UUFT Game Theory Simulator
"""

import os
import logging
import numpy as np
import matplotlib.pyplot as plt
from uuft_game_analyzer import UUFTGameScenario
from uuft_game_simulator import UUFTGameSimulator

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_game_sim.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('Test_Game_Sim')

# Constants
RESULTS_DIR = "uuft_results/game"
os.makedirs(RESULTS_DIR, exist_ok=True)

def test_game_simulator():
    """Test the game theory simulator."""
    logger.info("Testing game theory simulator")
    
    # Create a Prisoner's Dilemma game
    game = UUFTGameScenario(
        game_type="prisoners_dilemma",
        num_players=2,
        num_strategies=2,
        uuft_bias=0.0
    )
    
    # Create simulator
    simulator = UUFTGameSimulator(
        game_scenario=game,
        num_agents=50,
        learning_rate=0.1,
        uuft_parameters={
            "pi_influence": 0.5,
            "pattern_1882_strength": 0.7,
            "temporal_stability": 0.8
        }
    )
    
    # Run simulation
    logger.info("Running game simulation")
    results = simulator.run_simulation(num_steps=50)
    
    # Print final strategy distribution
    final_dist = results.iloc[-1]["strategy_distribution"]
    logger.info(f"Final strategy distribution: {final_dist}")
    
    # Visualize strategy evolution
    simulator.visualize_strategy_evolution(
        save_path=os.path.join(RESULTS_DIR, "test_strategy_evolution.png")
    )
    
    # Visualize payoff distribution
    simulator.visualize_payoff_distribution(
        save_path=os.path.join(RESULTS_DIR, "test_payoff_distribution.png")
    )
    
    # Test with UUFT bias
    logger.info("Testing game simulator with UUFT bias")
    game_with_bias = UUFTGameScenario(
        game_type="prisoners_dilemma",
        num_players=2,
        num_strategies=2,
        uuft_bias=0.7
    )
    
    # Create simulator with bias
    simulator_with_bias = UUFTGameSimulator(
        game_scenario=game_with_bias,
        num_agents=50,
        learning_rate=0.1,
        uuft_parameters={
            "pi_influence": 0.8,
            "pattern_1882_strength": 0.9,
            "temporal_stability": 0.8
        }
    )
    
    # Run simulation with bias
    logger.info("Running game simulation with UUFT bias")
    results_with_bias = simulator_with_bias.run_simulation(num_steps=50)
    
    # Print final strategy distribution with bias
    final_dist_with_bias = results_with_bias.iloc[-1]["strategy_distribution"]
    logger.info(f"Final strategy distribution with UUFT bias: {final_dist_with_bias}")
    
    # Visualize strategy evolution with bias
    simulator_with_bias.visualize_strategy_evolution(
        save_path=os.path.join(RESULTS_DIR, "test_strategy_evolution_with_bias.png")
    )
    
    logger.info("Game theory simulator test completed successfully")

if __name__ == "__main__":
    test_game_simulator()
