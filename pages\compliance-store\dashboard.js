import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/link';
import PageWithSidebar from '../../components/PageWithSidebar';
import ProtectedRoute from '../../components/ProtectedRoute';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';

// API URL
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

function DashboardContent() {
  // Define sidebar items for the Dashboard page
  const sidebarItems = [
    { type: 'category', label: 'Dashboard', items: [
      { label: 'My Connectors', href: '#my-connectors' },
      { label: 'Usage Statistics', href: '#usage-statistics' },
      { label: 'Compliance Status', href: '#compliance-status' },
      { label: 'Notifications', href: '#notifications' }
    ]},
    { type: 'category', label: 'Tools', items: [
      { label: 'Cross-Framework Mapping', href: '/compliance-store/mapping' }
    ]},
    { type: 'category', label: 'Navigation', items: [
      { label: 'Browse Connectors', href: '/compliance-store/browse' },
      { label: 'Compliance Store Home', href: '/compliance-store' }
    ]}
  ];

  // SEO metadata
  const pageProps = {
    title: 'My Dashboard - NovaFuse Compliance App Store',
    description: 'Manage your compliance connectors, view usage statistics, and monitor your compliance status.',
    keywords: 'compliance dashboard, connector management, compliance monitoring, NovaFuse Compliance App Store',
    canonical: 'https://novafuse.io/compliance-store/dashboard',
    ogImage: '/images/dashboard-og-image.png'
  };

  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Installed connectors state
  const [installedConnectors, setInstalledConnectors] = useState([
    {
      id: 'gdpr-shield',
      name: 'GDPR Shield',
      vendor: 'Compliance Partners Inc.',
      framework: 'gdpr',
      status: 'active',
      lastSync: '2023-06-15T14:30:00Z',
      healthStatus: 'healthy',
      usageStats: {
        apiCalls: 1243,
        dataProcessed: '2.3 GB',
        complianceChecks: 567
      }
    },
    {
      id: 'soc2-automator',
      name: 'SOC2 Automator',
      vendor: 'TrustTech Solutions',
      framework: 'soc2',
      status: 'active',
      lastSync: '2023-06-14T09:15:00Z',
      healthStatus: 'warning',
      usageStats: {
        apiCalls: 876,
        dataProcessed: '1.5 GB',
        complianceChecks: 432
      }
    },
    {
      id: 'hipaa-guardian',
      name: 'HIPAA Guardian',
      vendor: 'HealthSec Systems',
      framework: 'hipaa',
      status: 'inactive',
      lastSync: '2023-06-10T11:45:00Z',
      healthStatus: 'error',
      usageStats: {
        apiCalls: 0,
        dataProcessed: '0 GB',
        complianceChecks: 0
      }
    }
  ]);

  // Sample compliance status (in a real app, this would come from an API)
  const complianceStatus = {
    gdpr: {
      status: 'compliant',
      lastAssessment: '2023-06-12T10:00:00Z',
      score: 92,
      findings: 3,
      criticalFindings: 0
    },
    soc2: {
      status: 'warning',
      lastAssessment: '2023-06-11T14:30:00Z',
      score: 78,
      findings: 8,
      criticalFindings: 2
    },
    hipaa: {
      status: 'non-compliant',
      lastAssessment: '2023-06-05T09:15:00Z',
      score: 65,
      findings: 12,
      criticalFindings: 4
    }
  };

  // Sample notifications (in a real app, this would come from an API)
  const notifications = [
    {
      id: 1,
      type: 'warning',
      title: 'SOC2 Automator Needs Attention',
      message: 'Your SOC2 Automator connector has detected 2 critical findings that require immediate attention.',
      date: '2023-06-14T15:30:00Z',
      read: false
    },
    {
      id: 2,
      type: 'info',
      title: 'GDPR Shield Updated',
      message: 'GDPR Shield has been updated to version 2.3.0 with new features and improvements.',
      date: '2023-06-13T10:45:00Z',
      read: true
    },
    {
      id: 3,
      type: 'error',
      title: 'HIPAA Guardian Connection Error',
      message: 'HIPAA Guardian is unable to connect to your systems. Please check your configuration.',
      date: '2023-06-10T12:15:00Z',
      read: false
    }
  ];

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Fetch data from API
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError('');

        // In a real app, these would be API calls
        // const connectorsResponse = await axios.get(`${API_URL}/dashboard/connectors`);
        // const complianceResponse = await axios.get(`${API_URL}/dashboard/compliance`);
        // const notificationsResponse = await axios.get(`${API_URL}/dashboard/notifications`);

        // setInstalledConnectors(connectorsResponse.data);
        // setComplianceStatus(complianceResponse.data);
        // setNotifications(notificationsResponse.data);

        // For now, we'll use the sample data
        // This would be replaced with actual API calls in production

        setLoading(false);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError('Failed to load dashboard data. Please try again.');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Toggle connector status
  const toggleConnectorStatus = async (id) => {
    try {
      // In a real app, this would be an API call
      // await axios.put(`${API_URL}/dashboard/connectors/${id}/status`, {
      //   status: installedConnectors.find(c => c.id === id).status === 'active' ? 'inactive' : 'active'
      // });

      // For now, we'll update the state directly
      setInstalledConnectors(connectors =>
        connectors.map(connector =>
          connector.id === id
            ? { ...connector, status: connector.status === 'active' ? 'inactive' : 'active' }
            : connector
        )
      );
    } catch (error) {
      console.error('Error toggling connector status:', error);
      setError('Failed to update connector status. Please try again.');
    }
  };

  return (
    <PageWithSidebar title={pageProps.title} sidebarItems={sidebarItems} {...pageProps}>
      {error && (
        <div className="bg-red-900 bg-opacity-30 border border-red-700 rounded-lg p-4 mb-8 text-red-400">
          {error}
        </div>
      )}
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">My Dashboard</h1>
        <p className="text-xl">
          Manage your compliance connectors, view usage statistics, and monitor your compliance status.
        </p>
      </div>

      {/* Coming Soon Notice */}
      <div className="bg-blue-900 bg-opacity-30 border border-blue-700 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-bold mb-2">Coming Soon</h2>
        <p>
          The dashboard is currently in preview mode. Full functionality will be available when the Compliance App Store launches.
        </p>
      </div>

      {/* My Connectors */}
      <div id="my-connectors" className="mb-12">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">My Connectors</h2>
          <Link href="/compliance-store/browse" className="text-blue-400 hover:text-blue-300 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
            Add Connector
          </Link>
        </div>

        <div className="grid grid-cols-1 gap-6">
          {installedConnectors.map((connector) => (
            <div key={connector.id} className="bg-secondary rounded-lg overflow-hidden shadow-lg">
              <div className="p-6">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <div className="flex items-center mb-4 md:mb-0">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center text-white text-xl font-bold mr-4 ${
                      connector.healthStatus === 'healthy' ? 'bg-green-600' :
                      connector.healthStatus === 'warning' ? 'bg-yellow-600' : 'bg-red-600'
                    }`}>
                      {connector.framework.substring(0, 1).toUpperCase()}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">{connector.name}</h3>
                      <p className="text-sm text-gray-400">by {connector.vendor}</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium mr-4 ${
                      connector.status === 'active' ? 'bg-green-900 text-green-300' : 'bg-gray-700 text-gray-300'
                    }`}>
                      {connector.status === 'active' ? 'Active' : 'Inactive'}
                    </span>
                    <button
                      onClick={() => toggleConnectorStatus(connector.id)}
                      className={`px-3 py-1 rounded text-xs font-medium ${
                        connector.status === 'active'
                          ? 'bg-red-900 bg-opacity-30 text-red-300 hover:bg-opacity-50'
                          : 'bg-green-900 bg-opacity-30 text-green-300 hover:bg-opacity-50'
                      }`}
                    >
                      {connector.status === 'active' ? 'Deactivate' : 'Activate'}
                    </button>
                  </div>
                </div>

                <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-gray-800 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-400 mb-2">Health Status</h4>
                    <div className="flex items-center">
                      <span className={`h-3 w-3 rounded-full mr-2 ${
                        connector.healthStatus === 'healthy' ? 'bg-green-500' :
                        connector.healthStatus === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                      }`}></span>
                      <span className="capitalize">{connector.healthStatus}</span>
                    </div>
                  </div>

                  <div className="bg-gray-800 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-400 mb-2">Last Sync</h4>
                    <div>
                      {formatDate(connector.lastSync)}
                    </div>
                  </div>

                  <div className="bg-gray-800 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-400 mb-2">Framework</h4>
                    <div className="flex items-center">
                      <span className="bg-blue-600 text-xs px-2 py-1 rounded-full">
                        {connector.framework.toUpperCase()}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-4 flex justify-end">
                  <Link href={`/compliance-store/connectors/${connector.id}`} className="text-blue-400 hover:text-blue-300 mr-4">
                    View Details
                  </Link>
                  <Link href={`/compliance-store/dashboard/connector/${connector.id}`} className="text-blue-400 hover:text-blue-300">
                    Manage
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Usage Statistics */}
      <div id="usage-statistics" className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Usage Statistics</h2>
        <div className="bg-secondary rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-800 rounded-lg p-6 text-center">
              <h3 className="text-lg font-semibold mb-2">Total API Calls</h3>
              <p className="text-3xl font-bold text-blue-400">
                {installedConnectors.reduce((total, connector) => total + connector.usageStats.apiCalls, 0).toLocaleString()}
              </p>
              <p className="text-sm text-gray-400 mt-2">Last 30 days</p>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 text-center">
              <h3 className="text-lg font-semibold mb-2">Data Processed</h3>
              <p className="text-3xl font-bold text-blue-400">
                {installedConnectors.reduce((total, connector) => {
                  const gb = parseFloat(connector.usageStats.dataProcessed);
                  return isNaN(gb) ? total : total + gb;
                }, 0).toFixed(1)} GB
              </p>
              <p className="text-sm text-gray-400 mt-2">Last 30 days</p>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 text-center">
              <h3 className="text-lg font-semibold mb-2">Compliance Checks</h3>
              <p className="text-3xl font-bold text-blue-400">
                {installedConnectors.reduce((total, connector) => total + connector.usageStats.complianceChecks, 0).toLocaleString()}
              </p>
              <p className="text-sm text-gray-400 mt-2">Last 30 days</p>
            </div>
          </div>

          <div className="mt-6 text-center">
            <Link href="/compliance-store/dashboard/usage" className="text-blue-400 hover:text-blue-300">
              View Detailed Usage Reports
            </Link>
          </div>
        </div>
      </div>

      {/* Compliance Status */}
      <div id="compliance-status" className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Compliance Status</h2>
        <div className="bg-secondary rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {Object.entries(complianceStatus).map(([framework, status]) => (
              <div key={framework} className="bg-gray-800 rounded-lg overflow-hidden">
                <div className={`p-4 ${
                  status.status === 'compliant' ? 'bg-green-900 bg-opacity-30' :
                  status.status === 'warning' ? 'bg-yellow-900 bg-opacity-30' : 'bg-red-900 bg-opacity-30'
                }`}>
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold">{framework.toUpperCase()}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      status.status === 'compliant' ? 'bg-green-900 text-green-300' :
                      status.status === 'warning' ? 'bg-yellow-900 text-yellow-300' : 'bg-red-900 text-red-300'
                    }`}>
                      {status.status === 'compliant' ? 'Compliant' :
                       status.status === 'warning' ? 'Warning' : 'Non-Compliant'}
                    </span>
                  </div>
                </div>

                <div className="p-4">
                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm">Compliance Score</span>
                      <span className="text-sm font-medium">{status.score}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          status.score >= 90 ? 'bg-green-500' :
                          status.score >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${status.score}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-400">Findings</p>
                      <p className="font-medium">{status.findings}</p>
                    </div>
                    <div>
                      <p className="text-gray-400">Critical</p>
                      <p className={`font-medium ${status.criticalFindings > 0 ? 'text-red-400' : ''}`}>
                        {status.criticalFindings}
                      </p>
                    </div>
                  </div>

                  <div className="mt-4 text-xs text-gray-400">
                    Last assessment: {formatDate(status.lastAssessment)}
                  </div>

                  <div className="mt-4">
                    <Link href={`/compliance-store/dashboard/compliance/${framework}`} className="text-blue-400 hover:text-blue-300 text-sm">
                      View Details
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Notifications */}
      <div id="notifications" className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Notifications</h2>
        <div className="bg-secondary rounded-lg p-6">
          <div className="space-y-4">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 rounded-lg border ${
                  notification.read ? 'border-gray-700 bg-gray-800' :
                  notification.type === 'warning' ? 'border-yellow-700 bg-yellow-900 bg-opacity-20' :
                  notification.type === 'error' ? 'border-red-700 bg-red-900 bg-opacity-20' :
                  'border-blue-700 bg-blue-900 bg-opacity-20'
                }`}
              >
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-3">
                    {notification.type === 'warning' ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                    ) : notification.type === 'error' ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <h3 className="text-lg font-semibold">{notification.title}</h3>
                      <span className="text-xs text-gray-400">{formatDate(notification.date)}</span>
                    </div>
                    <p className="mt-1">{notification.message}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 text-center">
            <Link href="/compliance-store/dashboard/notifications" className="text-blue-400 hover:text-blue-300">
              View All Notifications
            </Link>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-blue-900 rounded-lg p-8 text-center mb-12">
        <h2 className="text-2xl font-bold mb-4">Need help with your compliance program?</h2>
        <p className="mb-6">Our compliance experts are available to help you optimize your compliance program.</p>
        <Link href="/contact" className="inline-block bg-white text-blue-700 hover:bg-gray-100 font-bold py-2 px-6 rounded-lg">
          Schedule a Consultation
        </Link>
      </div>

      {/* Confidentiality Notice */}
      <div className="border border-blue-800 bg-blue-900 bg-opacity-20 rounded-lg p-4">
        <div className="flex items-start">
          <div className="text-yellow-400 mr-3 mt-1">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <p className="text-sm">
              <strong>CONFIDENTIAL:</strong> The NovaFuse Compliance App Store is currently under IP protection review.
              All content is considered confidential and proprietary. Unauthorized access or sharing is prohibited.
            </p>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
}

// Export the dashboard with protection
export default function Dashboard() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  );
}
