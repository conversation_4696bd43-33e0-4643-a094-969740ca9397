import React from 'react';
import Sidebar from '../components/Sidebar';
import Link from 'next/link';

export default function GamificationSuite() {
  const sidebarItems = [
    { type: 'category', label: 'Gamification Suite', items: [
      { label: 'Overview', href: '#overview' },
      { label: 'NovaPlay', href: '#novaplay' },
      { label: 'NovaQuest', href: '#novaquest' },
      { label: 'NovaXP', href: '#novaxp' },
      { label: 'Integration', href: '#integration' },
      { label: 'Use Cases', href: '#use-cases' }
    ]},
    { type: 'category', label: 'Related Products', items: [
      { label: 'Gamification APIs', href: '/gamification-apis' },
      { label: 'Partner Ecosystem', href: '/partner-ecosystem' }
    ]}
  ];

  return (
    <div className="flex flex-col md:flex-row gap-8">
      {/* Sidebar - Hidden on mobile, shown on desktop */}
      <div className="hidden md:block md:w-1/4 lg:w-1/5">
        <div className="sticky top-4">
          <Sidebar items={sidebarItems} title="Gamification Suite" />
        </div>
      </div>

      {/* Mobile Sidebar Toggle - Shown on mobile only */}
      <div className="md:hidden mb-4">
        <select 
          className="w-full bg-secondary text-white border border-gray-700 rounded p-2"
          onChange={(e) => {
            if (e.target.value) window.location.href = e.target.value;
          }}
          defaultValue=""
        >
          <option value="" disabled>Navigate to...</option>
          {sidebarItems.map((item, index) => (
            item.type === 'category' ? (
              <optgroup key={index} label={item.label}>
                {item.items.map((subItem, subIndex) => (
                  <option key={`${index}-${subIndex}`} value={subItem.href}>
                    {subItem.label}
                  </option>
                ))}
              </optgroup>
            ) : (
              <option key={index} value={item.href}>
                {item.label}
              </option>
            )
          ))}
        </select>
      </div>

      {/* Main Content */}
      <div className="w-full md:w-3/4 lg:w-4/5">
        {/* Hero Section */}
        <div id="overview" className="bg-secondary p-8 rounded-lg mb-12">
          <h2 className="text-3xl font-bold mb-4 text-center">Transform Compliance into Engagement</h2>
          <p className="text-xl mb-6 text-center">
            The NovaGRC Gamification Suite turns compliance activities into engaging experiences that drive adoption and create a culture of cyber-safety.
          </p>
          <div className="text-center">
            <span className="text-2xl font-bold">NovaPlay™ | NovaQuest™ | NovaXP™</span>
            <p className="text-gray-400 mt-2">Built to train. Designed to test. Made to dominate.</p>
          </div>
        </div>
        
        {/* GRC Olympics Banner */}
        <div className="bg-gradient-to-r from-blue-600 via-red-500 to-yellow-500 p-4 rounded-lg mb-12 text-center relative overflow-hidden">
          <div className="absolute top-0 left-0 right-0 bottom-0 opacity-10 bg-[url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100\" height=\"100\" viewBox=\"0 0 100 100\"><circle cx=\"20\" cy=\"50\" r=\"15\" fill=\"none\" stroke=\"rgba(255,255,255,0.2)\" stroke-width=\"2\"/><circle cx=\"50\" cy=\"50\" r=\"15\" fill=\"none\" stroke=\"rgba(255,255,255,0.2)\" stroke-width=\"2\"/><circle cx=\"80\" cy=\"50\" r=\"15\" fill=\"none\" stroke=\"rgba(255,255,255,0.2)\" stroke-width=\"2\"/><circle cx=\"35\" cy=\"65\" r=\"15\" fill=\"none\" stroke=\"rgba(255,255,255,0.2)\" stroke-width=\"2\"/><circle cx=\"65\" cy=\"65\" r=\"15\" fill=\"none\" stroke=\"rgba(255,255,255,0.2)\" stroke-width=\"2\"/></svg>')] bg-center bg-no-repeat"></div>
          <div className="relative z-10">
            <h3 className="text-2xl font-bold text-white mb-2">🏆 Stay Tuned for the Upcoming GRC Olympics! 🏆</h3>
            <p className="text-white text-lg">
              The ultimate compliance competition is coming. Will your team take home the gold?
            </p>
            <div className="mt-4">
              <button className="bg-white text-blue-600 px-4 py-2 rounded-lg font-bold hover:bg-blue-50">
                Join the Waitlist
              </button>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {/* NovaPlay */}
          <div id="novaplay" className="bg-secondary rounded-lg overflow-hidden border-t-4 border-blue-500 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-2xl font-bold text-blue-400">NovaPlay™</h3>
                <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded">Core Compliance</span>
              </div>
              <p className="text-gray-300 mb-6">
                Transform routine compliance tasks into engaging experiences that drive participation and enhance knowledge retention.
              </p>
              <div className="mb-6">
                <h4 className="font-semibold mb-2">Key Features:</h4>
                <ul className="list-disc list-inside text-gray-300 space-y-1">
                  <li>Achievement badges for completing compliance tasks</li>
                  <li>Earn points for routine security activities</li>
                  <li>Progress tracking for compliance training</li>
                  <li>Personalized learning paths</li>
                </ul>
              </div>
              <div className="text-center mt-8">
                <p className="text-xl font-bold text-blue-400">"Turn policy into play."</p>
              </div>
            </div>
          </div>
          
          {/* NovaQuest */}
          <div id="novaquest" className="bg-secondary rounded-lg overflow-hidden border-t-4 border-red-500 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-2xl font-bold text-red-400">NovaQuest™</h3>
                <span className="bg-red-600 text-white text-xs px-2 py-1 rounded">Crisis Scenarios</span>
              </div>
              <p className="text-gray-300 mb-6">
                Prepare for real-world incidents with immersive challenges that test your team's readiness and response capabilities.
              </p>
              <div className="mb-6">
                <h4 className="font-semibold mb-2">Key Features:</h4>
                <ul className="list-disc list-inside text-gray-300 space-y-1">
                  <li>Breach simulation challenges</li>
                  <li>Disaster recovery scenarios</li>
                  <li>Regulatory response quests</li>
                  <li>Crisis management credentials</li>
                </ul>
              </div>
              <div className="text-center mt-8">
                <p className="text-xl font-bold text-red-400">"Every incident is an opportunity to prove your preparedness."</p>
              </div>
            </div>
          </div>
          
          {/* NovaXP */}
          <div id="novaxp" className="bg-secondary rounded-lg overflow-hidden border-t-4 border-yellow-500 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-2xl font-bold text-yellow-400">NovaXP™</h3>
                <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded">Leaderboards</span>
              </div>
              <p className="text-gray-300 mb-6">
                Fuel friendly competition and recognize your top compliance performers with dynamic leaderboards and ranking systems.
              </p>
              <div className="mb-6">
                <h4 className="font-semibold mb-2">Key Features:</h4>
                <ul className="list-disc list-inside text-gray-300 space-y-1">
                  <li>Department and individual leaderboards</li>
                  <li>Compliance ranking system</li>
                  <li>Competitive challenges</li>
                  <li>Recognition for compliance champions</li>
                </ul>
              </div>
              <div className="text-center mt-8">
                <p className="text-xl font-bold text-yellow-400">"Because in compliance, the best rise."</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Integration Section */}
        <div id="integration" className="bg-secondary p-6 rounded-lg mb-12">
          <h3 className="text-2xl font-bold mb-6 text-center">Seamless Integration</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-4 border border-blue-600 rounded-lg">
              <h4 className="text-lg font-semibold mb-2">API-First Architecture</h4>
              <p className="text-gray-300">
                The NovaGRC Gamification Suite is designed with an API-first architecture, ensuring easy integration with your existing systems and workflows.
              </p>
            </div>
            
            <div className="p-4 border border-blue-600 rounded-lg">
              <h4 className="text-lg font-semibold mb-2">Partner Ecosystem</h4>
              <p className="text-gray-300">
                Integrate with leading gamification platforms like BadgeOS, Bunchball, and more to supercharge your compliance engagement.
              </p>
            </div>
          </div>
        </div>
        
        {/* Use Cases */}
        <div id="use-cases" className="bg-secondary p-6 rounded-lg">
          <h3 className="text-2xl font-bold mb-6 text-center">Real-World Use Cases</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-4 border border-gray-700 rounded-lg">
              <h4 className="text-lg font-semibold mb-2">Security Awareness Training</h4>
              <p className="text-gray-300">
                Transform mandatory security training into an interactive experience with NovaPlay™. Employees earn points and badges as they complete modules and pass quizzes.
              </p>
            </div>
            
            <div className="p-4 border border-gray-700 rounded-lg">
              <h4 className="text-lg font-semibold mb-2">Incident Response Readiness</h4>
              <p className="text-gray-300">
                Prepare your team for security incidents with NovaQuest™. Simulate breaches and test response capabilities in a gamified environment.
              </p>
            </div>
            
            <div className="p-4 border border-gray-700 rounded-lg">
              <h4 className="text-lg font-semibold mb-2">Compliance Competition</h4>
              <p className="text-gray-300">
                Fuel department rivalry and celebrate the best performers with NovaXP™. Reward the teams that maintain the highest compliance standards.
              </p>
            </div>
          </div>
          
          <div className="text-center mt-8">
            <Link href="/partner-ecosystem" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
              Back to Partner Ecosystem
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
