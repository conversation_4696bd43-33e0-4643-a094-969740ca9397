# Testing NovaFuse API Superstore in Firebase

This guide explains how to test the NovaFuse API Superstore in a simulated Firebase environment.

## Prerequisites

- Node.js and npm installed
- Firebase CLI installed (`npm install -g firebase-tools`)

## Setup

1. The project has been configured with Firebase:
   - `firebase.json` - Configuration for Firebase services and emulators
   - `.firebaserc` - Project configuration
   - `marketplace-ui/firebase-config.js` - Firebase configuration
   - `marketplace-ui/firebase-init.js` - Firebase initialization

2. Firebase dependencies have been added to the marketplace UI:
   - firebase
   - firebase-admin

## Testing with Firebase Emulators

1. Start the Firebase emulators:
   ```
   start-emulators.bat
   ```

   This will start the following emulators:
   - Hosting (port 5500)
   - Authentication (port 9599)
   - Firestore (port 8580)
   - Functions (port 5501)
   - Emulator UI (port 4500)

2. In a separate terminal, start the marketplace UI in development mode:
   ```
   cd marketplace-ui
   npm run dev
   ```

3. Access the application:
   - Marketplace UI: http://localhost:3000
   - Firebase Emulator UI: http://localhost:4500

## Deploying to Firebase

To deploy the application to Firebase:

1. Build and deploy the marketplace UI:
   ```
   deploy-to-firebase.bat
   ```

2. Access the deployed application at:
   https://novafuse-api-superstore.web.app

## Notes

- The Firebase emulators provide a local simulation of Firebase services without connecting to production.
- Any data created in the emulators is temporary and will be lost when the emulators are stopped.
- For persistent data, you can use the `--export-on-exit` and `--import` flags with the Firebase emulators.

## Troubleshooting

If you encounter port conflicts with the Docker containers:

1. Stop the Docker containers:
   ```
   stop.bat
   ```

2. Start the Firebase emulators:
   ```
   start-emulators.bat
   ```

3. Start the marketplace UI in development mode:
   ```
   cd marketplace-ui
   npm run dev
   ```

