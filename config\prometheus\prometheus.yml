global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

alerting:
  alertmanagers:
    - static_configs:
        - targets: []
      scheme: http
      timeout: 10s
      api_version: v1

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'novafuse-api'
    metrics_path: '/metrics'
    scrape_interval: 5s
    static_configs:
      - targets: ['novafuse-api:3000']
    
  - job_name: 'quantum-inference'
    metrics_path: '/metrics/quantum'
    scrape_interval: 5s
    static_configs:
      - targets: ['novafuse-api:3000']

  - job_name: 'trinity-csde'
    metrics_path: '/metrics/trinity'
    scrape_interval: 5s
    static_configs:
      - targets: ['novafuse-api:3000']
