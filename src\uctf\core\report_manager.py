"""
Report Manager for the Universal Compliance Testing Framework.

This module provides functionality for generating test reports.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Callable

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ReportManager:
    """
    Manager for test reports.
    
    This class is responsible for generating reports from test results.
    """
    
    def __init__(self, reports_dir: Optional[str] = None):
        """
        Initialize the Report Manager.
        
        Args:
            reports_dir: Path to a directory for storing test reports
        """
        logger.info("Initializing Report Manager")
        
        # Set the reports directory
        self.reports_dir = reports_dir or os.path.join(os.getcwd(), 'test_reports')
        
        # Create the reports directory if it doesn't exist
        os.makedirs(self.reports_dir, exist_ok=True)
        
        # Dictionary to store report generators
        self.report_generators: Dict[str, Callable] = {}
        
        # Register default report generators
        self._register_default_report_generators()
        
        logger.info("Report Manager initialized")
    
    def _register_default_report_generators(self) -> None:
        """Register default report generators."""
        self.register_report_generator('summary', self._generate_summary_report)
        self.register_report_generator('detailed', self._generate_detailed_report)
        self.register_report_generator('compliance', self._generate_compliance_report)
        self.register_report_generator('executive', self._generate_executive_report)
    
    def register_report_generator(self, report_type: str, generator_func: Callable) -> None:
        """
        Register a report generator.
        
        Args:
            report_type: The type of report
            generator_func: The report generator function
        """
        self.report_generators[report_type] = generator_func
        logger.info(f"Registered report generator: {report_type}")
    
    def generate_report(self, 
                       report_type: str, 
                       test_run: Dict[str, Any], 
                       parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a report.
        
        Args:
            report_type: The type of report to generate
            test_run: The test run data
            parameters: Parameters for the report
            
        Returns:
            The generated report
            
        Raises:
            ValueError: If the report type does not exist
        """
        logger.info(f"Generating report: {report_type}")
        
        if report_type not in self.report_generators:
            raise ValueError(f"Report type not found: {report_type}")
        
        try:
            # Generate the report
            generator_func = self.report_generators[report_type]
            report = generator_func(test_run, parameters)
            
            # Save the report
            self._save_report(report)
            
            logger.info(f"Report generated: {report_type}")
            
            return report
        except Exception as e:
            logger.error(f"Failed to generate report {report_type}: {e}")
            raise
    
    def get_report(self, report_id: str) -> Dict[str, Any]:
        """
        Get a report.
        
        Args:
            report_id: The ID of the report
            
        Returns:
            The report
            
        Raises:
            ValueError: If the report does not exist
        """
        logger.info(f"Getting report: {report_id}")
        
        try:
            # Create the file path
            file_path = os.path.join(self.reports_dir, f"{report_id}.json")
            
            # Check if the file exists
            if not os.path.exists(file_path):
                raise ValueError(f"Report not found: {report_id}")
            
            # Load the report from the JSON file
            with open(file_path, 'r', encoding='utf-8') as f:
                report = json.load(f)
            
            logger.info(f"Loaded report: {report_id}")
            
            return report
        except Exception as e:
            logger.error(f"Failed to get report {report_id}: {e}")
            raise
    
    def get_all_reports(self) -> List[Dict[str, Any]]:
        """
        Get all reports.
        
        Returns:
            List of reports
        """
        logger.info("Getting all reports")
        
        reports = []
        
        try:
            # Get all JSON files in the reports directory
            report_files = [f for f in os.listdir(self.reports_dir) if f.endswith('.json')]
            
            for report_file in report_files:
                try:
                    # Get the report ID from the file name
                    report_id = os.path.splitext(report_file)[0]
                    
                    # Load the report
                    report = self.get_report(report_id)
                    
                    # Add the report to the list
                    reports.append(report)
                
                except Exception as e:
                    logger.error(f"Failed to load report from {report_file}: {e}")
            
            logger.info(f"Loaded {len(reports)} reports")
            
            return reports
        except Exception as e:
            logger.error(f"Failed to get all reports: {e}")
            return []
    
    def _save_report(self, report: Dict[str, Any]) -> None:
        """
        Save a report.
        
        Args:
            report: The report to save
        """
        try:
            # Get the report ID
            report_id = report.get('id')
            
            if not report_id:
                raise ValueError("Report ID is missing")
            
            # Create the file path
            file_path = os.path.join(self.reports_dir, f"{report_id}.json")
            
            # Save the report to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"Saved report: {report_id}")
        except Exception as e:
            logger.error(f"Failed to save report: {e}")
    
    def _generate_summary_report(self, test_run: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a summary report.
        
        Args:
            test_run: The test run data
            parameters: Parameters for the report
            
        Returns:
            The generated report
        """
        logger.info("Generating summary report")
        
        # Get the test result
        result = test_run.get('result', {})
        
        # Generate a unique report ID
        import uuid
        report_id = f"summary_{test_run.get('id', str(uuid.uuid4()))}"
        
        # Create the report
        report = {
            'id': report_id,
            'type': 'summary',
            'test_run_id': test_run.get('id'),
            'test_id': test_run.get('test_id'),
            'status': test_run.get('status'),
            'passed': result.get('passed', False),
            'score': result.get('score', 0),
            'findings_summary': {
                'total': len(result.get('findings', [])),
                'passed': len([f for f in result.get('findings', []) if f.get('status') == 'passed']),
                'warning': len([f for f in result.get('findings', []) if f.get('status') == 'warning']),
                'failed': len([f for f in result.get('findings', []) if f.get('status') == 'failed'])
            },
            'generated_at': self._get_current_timestamp()
        }
        
        return report
    
    def _generate_detailed_report(self, test_run: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a detailed report.
        
        Args:
            test_run: The test run data
            parameters: Parameters for the report
            
        Returns:
            The generated report
        """
        logger.info("Generating detailed report")
        
        # Get the test result
        result = test_run.get('result', {})
        
        # Generate a unique report ID
        import uuid
        report_id = f"detailed_{test_run.get('id', str(uuid.uuid4()))}"
        
        # Create the report
        report = {
            'id': report_id,
            'type': 'detailed',
            'test_run_id': test_run.get('id'),
            'test_id': test_run.get('test_id'),
            'status': test_run.get('status'),
            'passed': result.get('passed', False),
            'score': result.get('score', 0),
            'findings': result.get('findings', []),
            'parameters': test_run.get('parameters', {}),
            'start_time': test_run.get('start_time'),
            'end_time': test_run.get('end_time'),
            'generated_at': self._get_current_timestamp()
        }
        
        return report
    
    def _generate_compliance_report(self, test_run: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a compliance report.
        
        Args:
            test_run: The test run data
            parameters: Parameters for the report
            
        Returns:
            The generated report
        """
        logger.info("Generating compliance report")
        
        # Check if this is a test suite run
        is_suite = 'suite_id' in test_run
        
        # Generate a unique report ID
        import uuid
        report_id = f"compliance_{test_run.get('id', str(uuid.uuid4()))}"
        
        # Create the report
        if is_suite:
            # This is a test suite run
            report = self._generate_suite_compliance_report(test_run, parameters, report_id)
        else:
            # This is a single test run
            report = self._generate_single_test_compliance_report(test_run, parameters, report_id)
        
        return report
    
    def _generate_suite_compliance_report(self, test_run: Dict[str, Any], parameters: Dict[str, Any], report_id: str) -> Dict[str, Any]:
        """
        Generate a compliance report for a test suite.
        
        Args:
            test_run: The test run data
            parameters: Parameters for the report
            report_id: The report ID
            
        Returns:
            The generated report
        """
        # Get the test runs
        test_runs = test_run.get('test_runs', [])
        
        # Calculate overall compliance
        total_score = 0
        passed_count = 0
        
        for run in test_runs:
            result = run.get('result', {})
            total_score += result.get('score', 0)
            if result.get('passed', False):
                passed_count += 1
        
        avg_score = total_score / len(test_runs) if test_runs else 0
        
        # Create the report
        report = {
            'id': report_id,
            'type': 'compliance',
            'test_run_id': test_run.get('id'),
            'suite_id': test_run.get('suite_id'),
            'status': test_run.get('status'),
            'overall_score': avg_score,
            'tests_passed': passed_count,
            'tests_total': len(test_runs),
            'compliance_status': 'compliant' if passed_count == len(test_runs) else 'non_compliant',
            'test_results': [
                {
                    'test_id': run.get('test_id'),
                    'passed': run.get('result', {}).get('passed', False),
                    'score': run.get('result', {}).get('score', 0),
                    'findings_summary': {
                        'total': len(run.get('result', {}).get('findings', [])),
                        'passed': len([f for f in run.get('result', {}).get('findings', []) if f.get('status') == 'passed']),
                        'warning': len([f for f in run.get('result', {}).get('findings', []) if f.get('status') == 'warning']),
                        'failed': len([f for f in run.get('result', {}).get('findings', []) if f.get('status') == 'failed'])
                    }
                }
                for run in test_runs
            ],
            'generated_at': self._get_current_timestamp()
        }
        
        return report
    
    def _generate_single_test_compliance_report(self, test_run: Dict[str, Any], parameters: Dict[str, Any], report_id: str) -> Dict[str, Any]:
        """
        Generate a compliance report for a single test.
        
        Args:
            test_run: The test run data
            parameters: Parameters for the report
            report_id: The report ID
            
        Returns:
            The generated report
        """
        # Get the test result
        result = test_run.get('result', {})
        
        # Create the report
        report = {
            'id': report_id,
            'type': 'compliance',
            'test_run_id': test_run.get('id'),
            'test_id': test_run.get('test_id'),
            'status': test_run.get('status'),
            'score': result.get('score', 0),
            'compliance_status': 'compliant' if result.get('passed', False) else 'non_compliant',
            'findings': result.get('findings', []),
            'generated_at': self._get_current_timestamp()
        }
        
        return report
    
    def _generate_executive_report(self, test_run: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate an executive report.
        
        Args:
            test_run: The test run data
            parameters: Parameters for the report
            
        Returns:
            The generated report
        """
        logger.info("Generating executive report")
        
        # Check if this is a test suite run
        is_suite = 'suite_id' in test_run
        
        # Generate a unique report ID
        import uuid
        report_id = f"executive_{test_run.get('id', str(uuid.uuid4()))}"
        
        # Create the report
        if is_suite:
            # This is a test suite run
            report = self._generate_suite_executive_report(test_run, parameters, report_id)
        else:
            # This is a single test run
            report = self._generate_single_test_executive_report(test_run, parameters, report_id)
        
        return report
    
    def _generate_suite_executive_report(self, test_run: Dict[str, Any], parameters: Dict[str, Any], report_id: str) -> Dict[str, Any]:
        """
        Generate an executive report for a test suite.
        
        Args:
            test_run: The test run data
            parameters: Parameters for the report
            report_id: The report ID
            
        Returns:
            The generated report
        """
        # Get the test runs
        test_runs = test_run.get('test_runs', [])
        
        # Calculate overall compliance
        total_score = 0
        passed_count = 0
        total_findings = 0
        failed_findings = 0
        
        for run in test_runs:
            result = run.get('result', {})
            total_score += result.get('score', 0)
            if result.get('passed', False):
                passed_count += 1
            
            findings = result.get('findings', [])
            total_findings += len(findings)
            failed_findings += len([f for f in findings if f.get('status') == 'failed'])
        
        avg_score = total_score / len(test_runs) if test_runs else 0
        
        # Create the report
        report = {
            'id': report_id,
            'type': 'executive',
            'test_run_id': test_run.get('id'),
            'suite_id': test_run.get('suite_id'),
            'status': test_run.get('status'),
            'overall_score': avg_score,
            'tests_passed': passed_count,
            'tests_total': len(test_runs),
            'compliance_status': 'compliant' if passed_count == len(test_runs) else 'non_compliant',
            'risk_level': self._calculate_risk_level(avg_score, failed_findings),
            'key_findings': self._extract_key_findings(test_runs),
            'recommendations': self._generate_recommendations(test_runs),
            'generated_at': self._get_current_timestamp()
        }
        
        return report
    
    def _generate_single_test_executive_report(self, test_run: Dict[str, Any], parameters: Dict[str, Any], report_id: str) -> Dict[str, Any]:
        """
        Generate an executive report for a single test.
        
        Args:
            test_run: The test run data
            parameters: Parameters for the report
            report_id: The report ID
            
        Returns:
            The generated report
        """
        # Get the test result
        result = test_run.get('result', {})
        
        # Calculate risk level
        score = result.get('score', 0)
        failed_findings = len([f for f in result.get('findings', []) if f.get('status') == 'failed'])
        
        # Create the report
        report = {
            'id': report_id,
            'type': 'executive',
            'test_run_id': test_run.get('id'),
            'test_id': test_run.get('test_id'),
            'status': test_run.get('status'),
            'score': score,
            'compliance_status': 'compliant' if result.get('passed', False) else 'non_compliant',
            'risk_level': self._calculate_risk_level(score, failed_findings),
            'key_findings': self._extract_key_findings([test_run]),
            'recommendations': self._generate_recommendations([test_run]),
            'generated_at': self._get_current_timestamp()
        }
        
        return report
    
    def _calculate_risk_level(self, score: float, failed_findings: int) -> str:
        """
        Calculate the risk level based on the score and failed findings.
        
        Args:
            score: The compliance score
            failed_findings: The number of failed findings
            
        Returns:
            The risk level
        """
        if score >= 90 and failed_findings == 0:
            return 'low'
        elif score >= 70 and failed_findings <= 2:
            return 'medium'
        else:
            return 'high'
    
    def _extract_key_findings(self, test_runs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract key findings from test runs.
        
        Args:
            test_runs: The test runs
            
        Returns:
            The key findings
        """
        key_findings = []
        
        for run in test_runs:
            result = run.get('result', {})
            findings = result.get('findings', [])
            
            # Add failed findings
            for finding in findings:
                if finding.get('status') == 'failed':
                    key_findings.append({
                        'test_id': run.get('test_id'),
                        'finding_id': finding.get('id'),
                        'description': finding.get('description'),
                        'status': finding.get('status'),
                        'details': finding.get('details')
                    })
            
            # Add warning findings
            for finding in findings:
                if finding.get('status') == 'warning':
                    key_findings.append({
                        'test_id': run.get('test_id'),
                        'finding_id': finding.get('id'),
                        'description': finding.get('description'),
                        'status': finding.get('status'),
                        'details': finding.get('details')
                    })
        
        return key_findings
    
    def _generate_recommendations(self, test_runs: List[Dict[str, Any]]) -> List[str]:
        """
        Generate recommendations based on test runs.
        
        Args:
            test_runs: The test runs
            
        Returns:
            The recommendations
        """
        recommendations = []
        
        for run in test_runs:
            result = run.get('result', {})
            findings = result.get('findings', [])
            
            # Add recommendations for failed findings
            for finding in findings:
                if finding.get('status') == 'failed':
                    recommendations.append(f"Address {finding.get('id')}: {finding.get('description')}")
            
            # Add recommendations for warning findings
            for finding in findings:
                if finding.get('status') == 'warning':
                    recommendations.append(f"Improve {finding.get('id')}: {finding.get('description')}")
        
        return recommendations
    
    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.
        
        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
