version: '3.8'

x-common: &common
  restart: unless-stopped
  logging:
    driver: json-file
    options:
      max-size: 10m
      max-file: 3
  networks:
    - kethernet-net

services:
  # Core Server with Government Configuration
  server:
    <<: *common
    build: .
    container_name: kethernet-gov
    environment:
      - NODE_ENV=production
      - PORT=8080
      - HOST=0.0.0.0
      - ASIC_ENABLED=true
      - SECURITY_TIER=ts
      - PQC_ENCRYPTION=dilithium5
      - VALIDATOR_THRESHOLD=2847
      - LOG_LEVEL=debug
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 30s
    volumes:
      - ./logs:/app/logs:z
      - ./config:/app/config:ro
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G

  # Prometheus Monitoring
  prometheus:
    <<: *common
    image: prom/prometheus:latest
    container_name: kethernet-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    volumes:
      - ./monitoring/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    depends_on:
      - server

  # Grafana Dashboard
  grafana:
    <<: *common
    image: grafana/grafana:latest
    container_name: kethernet-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    ports:
      - "3001:3000"
    depends_on:
      - prometheus

  # Node Exporter for Host Metrics
  node-exporter:
    <<: *common
    image: prom/node-exporter:latest
    container_name: kethernet-node-exporter
    command:
      - '--path.rootfs=/host'
    pid: host
    volumes:
      - '/:/host:ro,rslave'
    deploy:
      mode: global
    ports:
      - "9100:9100"

networks:
  kethernet-net:
    driver: bridge
    driver_opts:
      encrypted: ""

volumes:
  prometheus_data:
    driver: local
    driver_opts:
      type: crypt
      device: /var/lib/prometheus
      o: "cipher=aes-xts-plain64,size=256"
  grafana_data:
    driver: local
    driver_opts:
      type: crypt
      device: /var/lib/grafana
      o: "cipher=aes-xts-plain64,size=256"
