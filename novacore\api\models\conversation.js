/**
 * Conversation Model
 *
 * This model represents a conversation between a user and NovaAssistAI.
 * It stores the conversation history, context, and metadata.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const AttachmentSchema = new Schema({
  url: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  type: {
    type: String,
    default: 'application/octet-stream'
  },
  size: {
    type: Number,
    default: 0
  }
});

const MessageSchema = new Schema({
  role: {
    type: String,
    enum: ['user', 'assistant', 'system'],
    required: true
  },
  content: {
    type: String,
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  attachments: [AttachmentSchema]
});

const ConversationSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  title: {
    type: String,
    default: function() {
      // Generate title from first message if available
      if (this.messages && this.messages.length > 0) {
        const firstMessage = this.messages[0].content;
        return firstMessage.substring(0, 50) + (firstMessage.length > 50 ? '...' : '');
      }
      return 'New Conversation';
    }
  },
  messages: [MessageSchema],
  context: {
    type: Map,
    of: Schema.Types.Mixed,
    default: {}
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  endedAt: {
    type: Date,
    default: null
  },
  feedback: {
    rating: {
      type: Number,
      min: 1,
      max: 5,
      default: null
    },
    comment: {
      type: String,
      default: null
    },
    submittedAt: {
      type: Date,
      default: null
    }
  },
  tags: [{
    type: String
  }]
});

// Update the updatedAt field before saving
ConversationSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Generate title from first message if not set
ConversationSchema.pre('save', function(next) {
  if (!this.title && this.messages && this.messages.length > 0) {
    const firstMessage = this.messages[0].content;
    this.title = firstMessage.substring(0, 50) + (firstMessage.length > 50 ? '...' : '');
  }
  next();
});

// Virtual for conversation duration
ConversationSchema.virtual('duration').get(function() {
  if (!this.endedAt) {
    return null;
  }
  return this.endedAt - this.createdAt;
});

// Virtual for message count
ConversationSchema.virtual('messageCount').get(function() {
  return this.messages.length;
});

// Virtual for isActive
ConversationSchema.virtual('isActive').get(function() {
  return this.endedAt === null;
});

module.exports = mongoose.model('Conversation', ConversationSchema);
