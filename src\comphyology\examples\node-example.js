/**
 * Comphyology NovaVision Integration - Node.js Example
 * 
 * This example demonstrates how to use the Comphyology NovaVision integration in Node.js.
 */

const fs = require('fs');
const path = require('path');

// Import NovaVision
const { novaVision } = require('../../novavision');

// Import Comphyology NovaVision Integration
const ComphyologyNovaVisionIntegration = require('../novavision_integration');

/**
 * Generate Comphyology visualization schemas and save them to files
 */
function generateAndSaveSchemas() {
  console.log('Generating Comphyology visualization schemas...');
  
  try {
    // Initialize Comphyology NovaVision Integration
    const comphyologyIntegration = new ComphyologyNovaVisionIntegration({
      novaVision,
      enableLogging: true
    });
    
    // Generate schemas
    const morphologicalSchema = comphyologyIntegration.generateMorphologicalResonanceSchema();
    const quantumSchema = comphyologyIntegration.generateQuantumPhaseSpaceSchema();
    const dashboardSchema = comphyologyIntegration.generateComphyologyDashboardSchema();
    
    // Create output directory
    const outputDir = path.join(__dirname, '../../../comphyology_schemas');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Save schemas to files
    fs.writeFileSync(
      path.join(outputDir, 'morphological_schema.json'),
      JSON.stringify(morphologicalSchema, null, 2)
    );
    
    fs.writeFileSync(
      path.join(outputDir, 'quantum_schema.json'),
      JSON.stringify(quantumSchema, null, 2)
    );
    
    fs.writeFileSync(
      path.join(outputDir, 'dashboard_schema.json'),
      JSON.stringify(dashboardSchema, null, 2)
    );
    
    console.log('Schemas generated and saved to:', outputDir);
    
    // Generate HTML example
    generateHtmlExample(outputDir, morphologicalSchema, quantumSchema, dashboardSchema);
    
    return {
      morphologicalSchema,
      quantumSchema,
      dashboardSchema,
      outputDir
    };
  } catch (error) {
    console.error('Error generating schemas:', error);
    throw error;
  }
}

/**
 * Generate HTML example using the schemas
 * 
 * @param {string} outputDir - Output directory
 * @param {Object} morphologicalSchema - Morphological Resonance Field schema
 * @param {Object} quantumSchema - Quantum Phase Space Map schema
 * @param {Object} dashboardSchema - Comphyology Dashboard schema
 */
function generateHtmlExample(outputDir, morphologicalSchema, quantumSchema, dashboardSchema) {
  console.log('Generating HTML example...');
  
  // Create HTML content
  const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comphyology NovaVision Integration</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      text-align: center;
      margin-bottom: 40px;
    }
    
    h1 {
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    .subtitle {
      color: #7f8c8d;
      font-size: 1.2em;
      margin-bottom: 20px;
    }
    
    .intro {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 30px;
      border-left: 5px solid #3498db;
    }
    
    .schema-container {
      margin-bottom: 40px;
    }
    
    .schema-title {
      font-size: 1.5em;
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    .schema-description {
      color: #7f8c8d;
      margin-bottom: 20px;
    }
    
    pre {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      overflow-x: auto;
      border: 1px solid #ddd;
    }
    
    code {
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }
    
    .note {
      background-color: #fff8e1;
      padding: 15px;
      border-radius: 8px;
      margin-top: 20px;
      border-left: 5px solid #ffc107;
    }
    
    footer {
      margin-top: 50px;
      text-align: center;
      color: #7f8c8d;
      font-size: 0.9em;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }
  </style>
</head>
<body>
  <header>
    <h1>Comphyology (Ψᶜ) NovaVision Integration</h1>
    <div class="subtitle">Visualizing Comphyology concepts using NovaFuse's Universal UI Framework</div>
  </header>
  
  <div class="intro">
    <p>
      This page demonstrates the integration of Comphyology with NovaVision, allowing Comphyology concepts
      to be visualized using NovaFuse's Universal UI Framework.
    </p>
    <p>
      The schemas below can be used with NovaVision's rendering system to create interactive visualizations
      of Comphyology concepts.
    </p>
  </div>
  
  <div class="schema-container">
    <div class="schema-title">Morphological Resonance Field</div>
    <div class="schema-description">
      Visualization of how structural complexity interacts with environmental factors
    </div>
    <pre><code>${JSON.stringify(morphologicalSchema, null, 2)}</code></pre>
  </div>
  
  <div class="schema-container">
    <div class="schema-title">Quantum Phase Space Map</div>
    <div class="schema-description">
      Visualization of entropy-phase relationships and pattern detection
    </div>
    <pre><code>${JSON.stringify(quantumSchema, null, 2)}</code></pre>
  </div>
  
  <div class="schema-container">
    <div class="schema-title">Comphyology Dashboard</div>
    <div class="schema-description">
      Comprehensive visualization of Comphyology concepts
    </div>
    <pre><code>${JSON.stringify(dashboardSchema, null, 2)}</code></pre>
  </div>
  
  <div class="note">
    <p>
      <strong>Note:</strong> To use these schemas with NovaVision, you need to:
    </p>
    <ol>
      <li>Import the NovaVision library</li>
      <li>Load the schema</li>
      <li>Use NovaVision's rendering system to render the UI</li>
    </ol>
    <p>
      See the React example in <code>src/comphyology/examples/react-example.jsx</code> for a complete example.
    </p>
  </div>
  
  <footer>
    <p>NovaFuse Comphyology (Ψᶜ) Framework - Copyright © ${new Date().getFullYear()}</p>
  </footer>
</body>
</html>
  `;
  
  // Save HTML file
  fs.writeFileSync(
    path.join(outputDir, 'comphyology_novavision_example.html'),
    htmlContent
  );
  
  console.log('HTML example generated and saved to:', path.join(outputDir, 'comphyology_novavision_example.html'));
}

/**
 * Main function
 */
function main() {
  console.log('Comphyology NovaVision Integration - Node.js Example');
  
  try {
    // Generate and save schemas
    const { outputDir } = generateAndSaveSchemas();
    
    console.log('\nExample completed successfully!');
    console.log('Output directory:', outputDir);
    console.log('\nTo view the HTML example, open:');
    console.log(path.join(outputDir, 'comphyology_novavision_example.html'));
  } catch (error) {
    console.error('\nExample failed:', error);
    process.exit(1);
  }
}

// Run the example if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = {
  generateAndSaveSchemas,
  generateHtmlExample,
  main
};
