<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>12 Pillars of Cyber-Safety Framework</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 800px;
            height: 600px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            box-sizing: border-box;
        }
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
        }
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            box-sizing: border-box;
            font-size: 14px;
            line-height: 1.2;
        }
        .component-label {
            font-weight: bold;
            margin-bottom: 4px;
        }
        .component-number {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 20px;
            height: 20px;
            background-color: #333;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
        }
        .arrow {
            position: absolute;
            background-color: #333;
            width: 2px;
        }
        .legend {
            position: absolute;
            right: 10px;
            bottom: -90px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            width: 200px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border: 1px solid #333;
        }
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: -90px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>FIG. 2: 12 Pillars of Cyber-Safety Framework</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 600px; left: 25px; top: 20px;">
            <div class="container-label">CYBER-SAFETY FRAMEWORK: 12 PILLARS</div>
        </div>
        
        <!-- Top Row - Pillars 1-4 -->
        <div class="component-box" style="left: 100px; top: 80px; width: 150px; height: 100px;">
            <div class="component-number">201</div>
            <div class="component-label">Pillar 1</div>
            Universal Cyber-Safety Kernel
        </div>
        
        <div class="component-box" style="left: 270px; top: 80px; width: 150px; height: 100px;">
            <div class="component-number">202</div>
            <div class="component-label">Pillar 2</div>
            Regulation-Specific ZK Batch Prover
        </div>
        
        <div class="component-box" style="left: 440px; top: 80px; width: 150px; height: 100px;">
            <div class="component-number">203</div>
            <div class="component-label">Pillar 3</div>
            Self-Destructing Compliance Servers
        </div>
        
        <div class="component-box" style="left: 610px; top: 80px; width: 150px; height: 100px;">
            <div class="component-number">204</div>
            <div class="component-label">Pillar 4</div>
            GDPR-by-Default Compiler
        </div>
        
        <!-- Middle Row - Pillars 5-8 -->
        <div class="component-box" style="left: 100px; top: 200px; width: 150px; height: 100px;">
            <div class="component-number">205</div>
            <div class="component-label">Pillar 5</div>
            Blockchain-Based Compliance Reconstruction
        </div>
        
        <div class="component-box" style="left: 270px; top: 200px; width: 150px; height: 100px;">
            <div class="component-number">206</div>
            <div class="component-label">Pillar 6</div>
            Cost-aware Compliance Optimizer
        </div>
        
        <div class="component-box" style="left: 440px; top: 200px; width: 150px; height: 100px;">
            <div class="component-number">207</div>
            <div class="component-label">Pillar 7</div>
            Clean-Room Regulatory Training Data
        </div>
        
        <div class="component-box" style="left: 610px; top: 200px; width: 150px; height: 100px;">
            <div class="component-number">208</div>
            <div class="component-label">Pillar 8</div>
            Three-Layer AI/Human Dispute Resolution
        </div>
        
        <!-- Bottom Row - Pillars 9-12 -->
        <div class="component-box" style="left: 100px; top: 320px; width: 150px; height: 100px;">
            <div class="component-number">209</div>
            <div class="component-label">Pillar 9</div>
            Post-Quantum Immutable Compliance Journal
        </div>
        
        <div class="component-box" style="left: 270px; top: 320px; width: 150px; height: 100px;">
            <div class="component-number">210</div>
            <div class="component-label">Pillar 10</div>
            Game-Theoretic Regulatory Negotiators
        </div>
        
        <div class="component-box" style="left: 440px; top: 320px; width: 150px; height: 100px;">
            <div class="component-number">211</div>
            <div class="component-label">Pillar 11</div>
            Temporal Compliance Markov Engine
        </div>
        
        <div class="component-box" style="left: 610px; top: 320px; width: 150px; height: 100px;">
            <div class="component-number">212</div>
            <div class="component-label">Pillar 12</div>
            C-Suite Directive to Code Compiler
        </div>
        
        <!-- Core Methodology Container -->
        <div class="container-box" style="width: 700px; height: 120px; left: 80px; top: 450px;">
            <div class="container-label">CORE METHODOLOGY</div>
        </div>
        
        <div class="component-box" style="left: 100px; top: 490px; width: 150px; height: 60px;">
            <div class="component-number">213</div>
            <div class="component-label">Unified Data Model</div>
        </div>
        
        <div class="component-box" style="left: 270px; top: 490px; width: 150px; height: 60px;">
            <div class="component-number">214</div>
            <div class="component-label">Native Unification Engine</div>
        </div>
        
        <div class="component-box" style="left: 440px; top: 490px; width: 150px; height: 60px;">
            <div class="component-number">215</div>
            <div class="component-label">Dynamic UI Enforcement</div>
        </div>
        
        <div class="component-box" style="left: 610px; top: 490px; width: 150px; height: 60px;">
            <div class="component-number">216</div>
            <div class="component-label">Cross-Domain Intelligence</div>
        </div>
        
        <!-- Connecting arrows -->
        <div class="arrow" style="left: 175px; top: 180px; height: 20px;"></div>
        <div class="arrow" style="left: 345px; top: 180px; height: 20px;"></div>
        <div class="arrow" style="left: 515px; top: 180px; height: 20px;"></div>
        <div class="arrow" style="left: 685px; top: 180px; height: 20px;"></div>
        
        <div class="arrow" style="left: 175px; top: 300px; height: 20px;"></div>
        <div class="arrow" style="left: 345px; top: 300px; height: 20px;"></div>
        <div class="arrow" style="left: 515px; top: 300px; height: 20px;"></div>
        <div class="arrow" style="left: 685px; top: 300px; height: 20px;"></div>
        
        <div class="arrow" style="left: 175px; top: 420px; height: 30px;"></div>
        <div class="arrow" style="left: 345px; top: 420px; height: 30px;"></div>
        <div class="arrow" style="left: 515px; top: 420px; height: 30px;"></div>
        <div class="arrow" style="left: 685px; top: 420px; height: 30px;"></div>
        
        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>12 Pillars of Cyber-Safety</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Core Methodology</div>
            </div>
        </div>
        
        <!-- Inventor Label -->
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
