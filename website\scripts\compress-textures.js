#!/usr/bin/env node

/**
 * Nova Texture Compression Tool
 * Handles GPU-accelerated texture compression for quantum visualization
 */

const fs = require('fs-extra');
const path = require('path');
const { execSync } = require('child_process');
const os = require('os');
const sharp = require('sharp');
const config = require('../nova.config.js');

class TextureCompressor {
  constructor(options = {}) {
    this.options = {
      format: 'astc',
      quality: 'high',
      inputDir: 'public/textures',
      outputDir: 'public/textures/compressed',
      ...options
    };
    
    this.supportedFormats = ['astc', 'etc', 'pvrtc', 's3tc'];
    this.qualityPresets = {
      low: { effort: 1, quality: 50 },
      medium: { effort: 3, quality: 75 },
      high: { effort: 5, quality: 90 },
      ultra: { effort: 7, quality: 100 }
    };
  }

  /**
   * Process all textures in the input directory
   */
  async processTextures() {
    console.log('🚀 Starting texture compression...');
    console.log(`📁 Input: ${path.resolve(this.options.inputDir)}`);
    console.log(`💾 Output: ${path.resolve(this.options.outputDir)}`);
    console.log(`🎨 Format: ${this.options.format.toUpperCase()}`);
    console.log(`✨ Quality: ${this.options.quality}`);

    // Validate format
    if (!this.supportedFormats.includes(this.options.format.toLowerCase())) {
      throw new Error(`Unsupported format: ${this.options.format}. Supported formats: ${this.supportedFormats.join(', ')}`);
    }

    // Ensure output directory exists
    await fs.ensureDir(this.options.outputDir);

    // Get all texture files
    const files = await this.findTextureFiles();
    
    if (files.length === 0) {
      console.log('ℹ️ No texture files found to process');
      return;
    }

    console.log(`🔍 Found ${files.length} textures to process`);

    // Process each file
    let processed = 0;
    const startTime = Date.now();
    
    for (const file of files) {
      try {
        await this.processFile(file);
        processed++;
        console.log(`✅ Processed ${file} (${processed}/${files.length})`);
      } catch (error) {
        console.error(`❌ Error processing ${file}:`, error.message);
      }
    }

    const elapsed = ((Date.now() - startTime) / 1000).toFixed(2);
    console.log(`\n✨ Compression complete!`);
    console.log(`   Processed: ${processed} of ${files.length} files`);
    console.log(`   Time: ${elapsed}s`);
    console.log(`   Output: ${path.resolve(this.options.outputDir)}`);
  }

  /**
   * Find all texture files in the input directory
   */
  async findTextureFiles() {
    const extensions = ['.png', '.jpg', '.jpeg', '.webp', '.tga', '.tif', '.tiff', '.bmp'];
    const files = [];
    
    const walkDir = async (dir) => {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
          await walkDir(fullPath);
        } else if (extensions.includes(path.extname(entry.name).toLowerCase())) {
          files.push(fullPath);
        }
      }
    };

    await walkDir(this.options.inputDir);
    return files;
  }

  /**
   * Process a single texture file
   */
  async processFile(inputPath) {
    const relativePath = path.relative(this.options.inputDir, inputPath);
    const outputPath = path.join(
      this.options.outputDir,
      path.dirname(relativePath),
      `${path.basename(relativePath, path.extname(relativePath))}.${this.getOutputExtension()}`
    );

    // Ensure output directory exists
    await fs.ensureDir(path.dirname(outputPath));

    // Get quality settings
    const quality = this.qualityPresets[this.options.quality] || this.qualityPresets.high;

    // Process based on format
    switch (this.options.format.toLowerCase()) {
      case 'astc':
        await this.compressASTC(inputPath, outputPath, quality);
        break;
      case 'etc':
        await this.compressETC(inputPath, outputPath, quality);
        break;
      case 'pvrtc':
        await this.compressPVRTC(inputPath, outputPath, quality);
        break;
      case 's3tc':
        await this.compressS3TC(inputPath, outputPath, quality);
        break;
      default:
        throw new Error(`Unsupported format: ${this.options.format}`);
    }
  }

  /**
   * Get the output file extension for the current format
   */
  getOutputExtension() {
    const extensions = {
      astc: 'astc',
      etc: 'ktx',
      pvrtc: 'pvr',
      s3tc: 'dds'
    };
    return extensions[this.options.format.toLowerCase()] || 'ktx';
  }

  /**
   * Compress texture using ASTC format
   */
  async compressASTC(inputPath, outputPath, quality) {
    // Convert to ASTC using sharp for the initial image processing
    const image = sharp(inputPath);
    const metadata = await image.metadata();
    
    // For ASTC, we need to use a specialized tool like astcenc
    // This is a simplified example - in a real implementation, you'd call astcenc
    const tempFile = outputPath + '.temp.png';
    
    // First, resize if needed and save as temporary PNG
    await image
      .resize({
        width: Math.min(metadata.width, 4096),
        height: Math.min(metadata.height, 4096),
        fit: 'inside',
        withoutEnlargement: true
      })
      .toFile(tempFile);
    
    // In a real implementation, you would call astcenc here
    // Example: astcenc -cl temp.png output.astc 6x6 -medium
    console.log(`[ASTC] Would compress ${inputPath} -> ${outputPath}`);
    
    // Clean up temp file
    await fs.remove(tempFile);
    
    // For now, just copy the file as a placeholder
    await fs.copy(inputPath, outputPath);
  }

  /**
   * Compress texture using ETC format
   */
  async compressETC(inputPath, outputPath, quality) {
    // Similar to ASTC but for ETC format
    console.log(`[ETC] Would compress ${inputPath} -> ${outputPath}`);
    await fs.copy(inputPath, outputPath);
  }

  /**
   * Compress texture using PVRTC format
   */
  async compressPVRTC(inputPath, outputPath, quality) {
    // Similar to ASTC but for PVRTC format
    console.log(`[PVRTC] Would compress ${inputPath} -> ${outputPath}`);
    await fs.copy(inputPath, outputPath);
  }

  /**
   * Compress texture using S3TC (DXT) format
   */
  async compressS3TC(inputPath, outputPath, quality) {
    // Similar to ASTC but for S3TC format
    console.log(`[S3TC] Would compress ${inputPath} -> ${outputPath}`);
    await fs.copy(inputPath, outputPath);
  }
}

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    format: 'astc',
    quality: 'high',
    inputDir: 'public/textures',
    outputDir: 'public/textures/compressed'
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    if (arg === '--format' || arg === '-f') {
      options.format = args[++i];
    } else if (arg === '--quality' || arg === '-q') {
      options.quality = args[++i];
    } else if (arg === '--input' || arg === '-i') {
      options.inputDir = args[++i];
    } else if (arg === '--output' || arg === '-o') {
      options.outputDir = args[++i];
    } else if (arg === '--help' || arg === '-h') {
      showHelp();
      process.exit(0);
    }
  }

  return options;
}

function showHelp() {
  console.log(`
Nova Texture Compression Tool

Usage:
  node scripts/compress-textures.js [options]

Options:
  -f, --format FORMAT    Output format (astc, etc, pvrtc, s3tc) [default: astc]
  -q, --quality QUALITY  Quality preset (low, medium, high, ultra) [default: high]
  -i, --input DIR        Input directory [default: public/textures]
  -o, --output DIR       Output directory [default: public/textures/compressed]
  -h, --help             Show this help message

Examples:
  # Compress textures with default settings
  node scripts/compress-textures.js

  # Compress textures with ETC format and high quality
  node scripts/compress-textures.js --format etc --quality high
`);
}

// Main function
async function main() {
  try {
    const options = parseArgs();
    const compressor = new TextureCompressor(options);
    await compressor.processTextures();
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Run the main function
main();
