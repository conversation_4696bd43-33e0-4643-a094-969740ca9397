/**
 * @swagger
 * components:
 *   schemas:
 *     ESGReport:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the ESG report
 *         title:
 *           type: string
 *           description: Title of the ESG report
 *         description:
 *           type: string
 *           description: Description of the ESG report
 *         reportType:
 *           type: string
 *           enum: [annual, quarterly, sustainability, impact, custom]
 *           description: Type of ESG report
 *         reportingPeriod:
 *           type: object
 *           properties:
 *             startDate:
 *               type: string
 *               format: date
 *               description: Start date of the reporting period
 *             endDate:
 *               type: string
 *               format: date
 *               description: End date of the reporting period
 *         status:
 *           type: string
 *           enum: [draft, in-review, published, archived]
 *           description: Status of the report
 *         frameworks:
 *           type: array
 *           items:
 *             type: string
 *           description: List of ESG frameworks used in the report
 *         metrics:
 *           type: array
 *           items:
 *             type: string
 *           description: List of ESG metric IDs included in the report
 *         sections:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               content:
 *                 type: string
 *               order:
 *                 type: integer
 *           description: Sections of the report
 *         attachments:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               fileType:
 *                 type: string
 *               url:
 *                 type: string
 *           description: Attachments for the report
 *         createdBy:
 *           type: string
 *           description: User who created the report
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the report was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the report was last updated
 *         publishedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the report was published
 *       required:
 *         - id
 *         - title
 *         - reportType
 *         - reportingPeriod
 *         - status
 *         - createdBy
 *         - createdAt
 *         - updatedAt
 *     
 *     ReportTemplate:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the report template
 *         name:
 *           type: string
 *           description: Name of the template
 *         description:
 *           type: string
 *           description: Description of the template
 *         reportType:
 *           type: string
 *           enum: [annual, quarterly, sustainability, impact, custom]
 *           description: Type of report this template is for
 *         frameworks:
 *           type: array
 *           items:
 *             type: string
 *           description: List of ESG frameworks supported by this template
 *         structure:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               order:
 *                 type: integer
 *               metricCategories:
 *                 type: array
 *                 items:
 *                   type: string
 *           description: Structure of the template
 *         isDefault:
 *           type: boolean
 *           description: Whether this is a default template
 *         createdBy:
 *           type: string
 *           description: User who created the template
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the template was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the template was last updated
 *       required:
 *         - id
 *         - name
 *         - reportType
 *         - structure
 *         - createdBy
 *         - createdAt
 *         - updatedAt
 */

// Sample ESG reports
const esgReports = [
  {
    id: 'rep-001',
    title: '2023 Annual Sustainability Report',
    description: 'Comprehensive report on our sustainability initiatives and performance for 2023',
    reportType: 'annual',
    reportingPeriod: {
      startDate: '2023-01-01',
      endDate: '2023-12-31'
    },
    status: 'published',
    frameworks: ['gri', 'sasb'],
    metrics: ['met-001', 'met-002', 'met-003', 'met-004', 'met-005'],
    sections: [
      {
        title: 'Executive Summary',
        content: 'This report outlines our sustainability performance for 2023, highlighting our achievements in reducing carbon emissions, improving water management, and enhancing diversity and inclusion.',
        order: 1
      },
      {
        title: 'Environmental Performance',
        content: 'Our environmental initiatives have resulted in a 15% reduction in carbon emissions and a 20% improvement in water efficiency compared to 2022.',
        order: 2
      },
      {
        title: 'Social Impact',
        content: 'We have increased diversity in leadership positions by 10% and implemented new community engagement programs that have benefited over 5,000 people.',
        order: 3
      },
      {
        title: 'Governance',
        content: 'We have strengthened our governance framework with new policies on ethical business conduct and enhanced board oversight of ESG matters.',
        order: 4
      }
    ],
    attachments: [
      {
        name: 'Carbon Emissions Data',
        fileType: 'xlsx',
        url: '/attachments/carbon-emissions-2023.xlsx'
      },
      {
        name: 'Water Management Report',
        fileType: 'pdf',
        url: '/attachments/water-management-2023.pdf'
      }
    ],
    createdBy: 'user-123',
    createdAt: '2023-10-15T09:00:00Z',
    updatedAt: '2023-12-10T14:30:00Z',
    publishedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: 'rep-002',
    title: 'Q1 2024 ESG Update',
    description: 'Quarterly update on our ESG performance for Q1 2024',
    reportType: 'quarterly',
    reportingPeriod: {
      startDate: '2024-01-01',
      endDate: '2024-03-31'
    },
    status: 'in-review',
    frameworks: ['sasb'],
    metrics: ['met-001', 'met-003', 'met-006'],
    sections: [
      {
        title: 'Q1 Highlights',
        content: 'Key achievements and challenges in our ESG performance for Q1 2024.',
        order: 1
      },
      {
        title: 'Progress on Targets',
        content: 'Update on our progress towards our annual ESG targets.',
        order: 2
      },
      {
        title: 'Looking Ahead',
        content: 'Initiatives and focus areas for the upcoming quarter.',
        order: 3
      }
    ],
    attachments: [
      {
        name: 'Q1 ESG Metrics',
        fileType: 'xlsx',
        url: '/attachments/q1-2024-esg-metrics.xlsx'
      }
    ],
    createdBy: 'user-456',
    createdAt: '2024-03-20T11:15:00Z',
    updatedAt: '2024-04-05T16:45:00Z',
    publishedAt: null
  },
  {
    id: 'rep-003',
    title: '2023 Climate Impact Report',
    description: 'Focused report on our climate-related initiatives and performance',
    reportType: 'impact',
    reportingPeriod: {
      startDate: '2023-01-01',
      endDate: '2023-12-31'
    },
    status: 'published',
    frameworks: ['tcfd', 'gri'],
    metrics: ['met-001', 'met-002', 'met-007', 'met-008'],
    sections: [
      {
        title: 'Climate Strategy',
        content: 'Our approach to addressing climate change and reducing our environmental footprint.',
        order: 1
      },
      {
        title: 'Emissions Performance',
        content: 'Detailed analysis of our Scope 1, 2, and 3 emissions and reduction initiatives.',
        order: 2
      },
      {
        title: 'Climate Risk Management',
        content: 'How we identify, assess, and manage climate-related risks and opportunities.',
        order: 3
      },
      {
        title: 'Targets and Metrics',
        content: 'Our climate-related targets and the metrics we use to measure progress.',
        order: 4
      }
    ],
    attachments: [
      {
        name: 'Climate Risk Assessment',
        fileType: 'pdf',
        url: '/attachments/climate-risk-assessment-2023.pdf'
      },
      {
        name: 'Emissions Data',
        fileType: 'xlsx',
        url: '/attachments/emissions-data-2023.xlsx'
      }
    ],
    createdBy: 'user-789',
    createdAt: '2023-11-10T13:20:00Z',
    updatedAt: '2024-01-20T09:45:00Z',
    publishedAt: '2024-02-01T11:00:00Z'
  }
];

// Sample report templates
const reportTemplates = [
  {
    id: 'tpl-001',
    name: 'Annual Sustainability Report Template',
    description: 'Standard template for annual sustainability reports following GRI and SASB frameworks',
    reportType: 'annual',
    frameworks: ['gri', 'sasb'],
    structure: [
      {
        title: 'Executive Summary',
        description: 'Overview of key sustainability achievements and challenges',
        order: 1,
        metricCategories: ['general']
      },
      {
        title: 'Environmental Performance',
        description: 'Details on environmental initiatives and performance metrics',
        order: 2,
        metricCategories: ['environmental', 'climate']
      },
      {
        title: 'Social Impact',
        description: 'Information on social initiatives, diversity, and community engagement',
        order: 3,
        metricCategories: ['social', 'diversity', 'community']
      },
      {
        title: 'Governance',
        description: 'Overview of governance structure and ethical business practices',
        order: 4,
        metricCategories: ['governance', 'ethics']
      },
      {
        title: 'Appendices',
        description: 'Additional data, methodologies, and supporting information',
        order: 5,
        metricCategories: []
      }
    ],
    isDefault: true,
    createdBy: 'system',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: 'tpl-002',
    name: 'TCFD Climate Report Template',
    description: 'Template for climate-related disclosures following TCFD recommendations',
    reportType: 'impact',
    frameworks: ['tcfd'],
    structure: [
      {
        title: 'Governance',
        description: 'Board and management oversight of climate-related risks and opportunities',
        order: 1,
        metricCategories: ['governance']
      },
      {
        title: 'Strategy',
        description: 'Climate-related risks and opportunities and their impact on business strategy',
        order: 2,
        metricCategories: ['strategy', 'climate']
      },
      {
        title: 'Risk Management',
        description: 'Processes for identifying, assessing, and managing climate-related risks',
        order: 3,
        metricCategories: ['risk']
      },
      {
        title: 'Metrics and Targets',
        description: 'Metrics and targets used to assess and manage climate-related risks and opportunities',
        order: 4,
        metricCategories: ['climate', 'environmental']
      }
    ],
    isDefault: true,
    createdBy: 'system',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: 'tpl-003',
    name: 'Quarterly ESG Update Template',
    description: 'Streamlined template for quarterly ESG performance updates',
    reportType: 'quarterly',
    frameworks: ['sasb'],
    structure: [
      {
        title: 'Quarterly Highlights',
        description: 'Key ESG achievements and challenges for the quarter',
        order: 1,
        metricCategories: ['general']
      },
      {
        title: 'Progress on Targets',
        description: 'Update on progress towards annual ESG targets',
        order: 2,
        metricCategories: ['environmental', 'social', 'governance']
      },
      {
        title: 'Looking Ahead',
        description: 'Initiatives and focus areas for the upcoming quarter',
        order: 3,
        metricCategories: ['general']
      }
    ],
    isDefault: true,
    createdBy: 'system',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  }
];

module.exports = {
  esgReports,
  reportTemplates
};
