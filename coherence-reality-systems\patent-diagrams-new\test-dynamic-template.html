<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patent Diagram Template</title>
    <style>
        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }
        
        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 800px;
            height: 700px; /* Extended height for adequate space */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: hidden; /* Prevents content from spilling out */
        }
        
        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: hidden; /* Prevents content from spilling out */
        }
        
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: hidden; /* Prevents content from spilling out */
            text-overflow: ellipsis; /* Shows ellipsis for overflowing text */
        }
        
        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            overflow: hidden; /* Prevents content from spilling out */
        }
        
        /* Component Number Styles - Nestled in corner */
        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 20px;
            height: 20px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
            z-index: 2;
        }
        
        .component-label {
            font-weight: bold;
            margin-bottom: 6px;
            font-size: 14px;
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: hidden; /* Prevents content from spilling out */
            text-overflow: ellipsis; /* Shows ellipsis for overflowing text */
        }
        
        .component-content {
            font-size: 12px;
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: hidden; /* Prevents content from spilling out */
            text-overflow: ellipsis; /* Shows ellipsis for overflowing text */
        }
        
        /* Arrow Styles */
        .arrow-line {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            z-index: 0;
        }
        
        .arrow-head {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 0;
        }
        
        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }
        
        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }
        
        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }
        
        /* SVG Styles */
        svg {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 0;
        }
        
        /* Equation Styles */
        .equation {
            font-weight: bold;
            font-size: 14px;
            margin-top: 5px;
            text-align: center;
        }
    </style>
    <script>
        // Dynamic Template Functions
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure component numbers are nestled in corners
            const componentBoxes = document.querySelectorAll('.component-box');
            componentBoxes.forEach(box => {
                // Check if box already has a component number
                if (!box.querySelector('.component-number-inside')) {
                    // If not, create one with the next available number
                    const componentNumber = document.createElement('div');
                    componentNumber.className = 'component-number-inside';
                    componentNumber.textContent = getNextComponentNumber();
                    box.prepend(componentNumber);
                }
            });
            
            // Ensure all elements are contained within main container
            const mainContainer = document.querySelector('.container-box');
            if (mainContainer) {
                // Find the lowest element
                let lowestPoint = 0;
                document.querySelectorAll('.component-box, .container-box').forEach(element => {
                    if (element !== mainContainer) {
                        const bottom = element.offsetTop + element.offsetHeight;
                        if (bottom > lowestPoint) {
                            lowestPoint = bottom;
                        }
                    }
                });
                
                // Add padding to ensure everything fits
                const padding = 50;
                const newHeight = lowestPoint - mainContainer.offsetTop + padding;
                mainContainer.style.height = newHeight + 'px';
            }
            
            // Ensure inventor label and legend are properly positioned
            const inventorLabel = document.querySelector('.inventor-label');
            const legend = document.querySelector('.legend');
            
            if (inventorLabel) {
                inventorLabel.style.position = 'absolute';
                inventorLabel.style.left = '10px';
                inventorLabel.style.bottom = '30px';
                inventorLabel.style.zIndex = '10';
            }
            
            if (legend) {
                legend.style.position = 'absolute';
                legend.style.right = '10px';
                legend.style.bottom = '30px';
                legend.style.zIndex = '10';
            }
        });
        
        // Helper function to get next available component number
        function getNextComponentNumber() {
            const existingNumbers = Array.from(document.querySelectorAll('.component-number-inside'))
                .map(el => parseInt(el.textContent, 10))
                .filter(num => !isNaN(num));
            
            if (existingNumbers.length === 0) {
                return 1;
            }
            
            return Math.max(...existingNumbers) + 1;
        }
    </script>
</head>
<body>
    <h1>FIG. X: Test Dynamic Template</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 580px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">DYNAMIC TEMPLATE DEMONSTRATION</div>
        </div>
        
        <!-- Example Component Boxes - Replace with your actual components -->
        <div class="component-box" style="left: 100px; top: 100px; width: 150px; height: 80px;">
            <div class="component-number-inside">1</div>
            <div class="component-label">[Component 1]</div>
            <div class="component-content">
                [Component 1 Description]
            </div>
        </div>
        
        <div class="component-box" style="left: 300px; top: 100px; width: 150px; height: 80px;">
            <div class="component-number-inside">2</div>
            <div class="component-label">[Component 2]</div>
            <div class="component-content">
                [Component 2 Description]
            </div>
        </div>
        
        <!-- Add more components as needed -->
        
        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #555555;"></div>
                <div>[Legend Item 1]</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: white; border: 2px solid #555555;"></div>
                <div>[Legend Item 2]</div>
            </div>
        </div>
        
        <!-- Inventor Label -->
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
    
    <!-- Instructions for using this template -->
    </body>
</html>

