/**
 * NovaVision - Dashboard Example
 *
 * This example demonstrates how to use NovaVision to generate and render a dashboard.
 */

const { novaVision } = require('../index');

// Create a dashboard configuration
const dashboardConfig = {
  id: 'compliance-dashboard',
  title: 'Compliance Dashboard',
  description: 'Overview of compliance status across the organization',
  filters: [
    {
      id: 'dateRange',
      type: 'daterange',
      label: 'Date Range',
      defaultValue: {
        start: '2023-01-01',
        end: '2023-12-31'
      }
    },
    {
      id: 'department',
      type: 'select',
      label: 'Department',
      options: [
        { value: 'all', label: 'All Departments' },
        { value: 'it', label: 'IT' },
        { value: 'finance', label: 'Finance' },
        { value: 'hr', label: 'Human Resources' },
        { value: 'legal', label: 'Legal' }
      ],
      defaultValue: 'all'
    }
  ],
  sections: [
    {
      id: 'overview',
      title: 'Overview',
      widgets: [
        {
          id: 'compliance-score',
          type: 'metric',
          title: 'Overall Compliance Score',
          metricConfig: {
            value: 85,
            previousValue: 78,
            format: 'percentage'
          }
        },
        {
          id: 'open-issues',
          type: 'metric',
          title: 'Open Issues',
          metricConfig: {
            value: 24,
            previousValue: 32,
            format: 'number'
          }
        },
        {
          id: 'upcoming-deadlines',
          type: 'metric',
          title: 'Upcoming Deadlines',
          metricConfig: {
            value: 8,
            previousValue: 5,
            format: 'number'
          }
        },
        {
          id: 'compliance-by-framework',
          type: 'chart',
          title: 'Compliance by Framework',
          chartConfig: {
            type: 'bar',
            data: {
              labels: ['GDPR', 'PCI DSS', 'HIPAA', 'SOX', 'ISO 27001'],
              datasets: [{
                label: 'Compliance Score',
                data: [92, 78, 85, 90, 88]
              }]
            },
            options: {
              scales: {
                y: {
                  beginAtZero: true,
                  max: 100
                }
              }
            }
          }
        }
      ]
    },
    {
      id: 'issues',
      title: 'Compliance Issues',
      widgets: [
        {
          id: 'issues-by-severity',
          type: 'chart',
          title: 'Issues by Severity',
          chartConfig: {
            type: 'pie',
            data: {
              labels: ['Critical', 'High', 'Medium', 'Low'],
              datasets: [{
                data: [3, 8, 15, 22],
                backgroundColor: ['#FF4136', '#FF851B', '#FFDC00', '#2ECC40']
              }]
            }
          }
        },
        {
          id: 'issues-by-department',
          type: 'chart',
          title: 'Issues by Department',
          chartConfig: {
            type: 'bar',
            data: {
              labels: ['IT', 'Finance', 'HR', 'Legal', 'Operations'],
              datasets: [{
                label: 'Open Issues',
                data: [12, 5, 3, 2, 6]
              }]
            }
          }
        },
        {
          id: 'recent-issues',
          type: 'table',
          title: 'Recent Issues',
          tableConfig: {
            columns: [
              { id: 'id', label: 'ID', type: 'string' },
              { id: 'title', label: 'Title', type: 'string' },
              { id: 'severity', label: 'Severity', type: 'string' },
              { id: 'department', label: 'Department', type: 'string' },
              { id: 'dateIdentified', label: 'Date Identified', type: 'date' },
              { id: 'status', label: 'Status', type: 'string' }
            ],
            data: [
              { id: 'ISSUE-001', title: 'Missing data encryption', severity: 'Critical', department: 'IT', dateIdentified: '2023-11-15', status: 'Open' },
              { id: 'ISSUE-002', title: 'Incomplete access controls', severity: 'High', department: 'IT', dateIdentified: '2023-11-10', status: 'In Progress' },
              { id: 'ISSUE-003', title: 'Outdated privacy policy', severity: 'Medium', department: 'Legal', dateIdentified: '2023-11-05', status: 'Open' },
              { id: 'ISSUE-004', title: 'Insufficient audit logs', severity: 'High', department: 'Finance', dateIdentified: '2023-11-01', status: 'In Progress' },
              { id: 'ISSUE-005', title: 'Incomplete employee training', severity: 'Medium', department: 'HR', dateIdentified: '2023-10-28', status: 'Open' }
            ],
            pagination: true,
            pageSize: 5
          }
        }
      ]
    },
    {
      id: 'tasks',
      title: 'Compliance Tasks',
      widgets: [
        {
          id: 'tasks-by-status',
          type: 'chart',
          title: 'Tasks by Status',
          chartConfig: {
            type: 'doughnut',
            data: {
              labels: ['Completed', 'In Progress', 'Not Started', 'Overdue'],
              datasets: [{
                data: [45, 15, 10, 5],
                backgroundColor: ['#2ECC40', '#0074D9', '#AAAAAA', '#FF4136']
              }]
            }
          }
        },
        {
          id: 'upcoming-tasks',
          type: 'list',
          title: 'Upcoming Tasks',
          listConfig: {
            items: [
              { id: 'TASK-001', title: 'Update data processing agreement', dueDate: '2023-12-05', assignee: 'John Doe', priority: 'High' },
              { id: 'TASK-002', title: 'Conduct security awareness training', dueDate: '2023-12-10', assignee: 'Jane Smith', priority: 'Medium' },
              { id: 'TASK-003', title: 'Review access control policy', dueDate: '2023-12-15', assignee: 'Mike Johnson', priority: 'High' },
              { id: 'TASK-004', title: 'Update incident response plan', dueDate: '2023-12-20', assignee: 'Sarah Williams', priority: 'Medium' },
              { id: 'TASK-005', title: 'Conduct vulnerability assessment', dueDate: '2023-12-25', assignee: 'David Brown', priority: 'Critical' }
            ]
          }
        }
      ]
    }
  ]
};

// Generate UI schema for the dashboard
const dashboardSchema = novaVision.generateDashboardSchema(dashboardConfig);

// Sample dashboard data
const dashboardData = {
  dateRange: {
    start: '2023-01-01',
    end: '2023-12-31'
  },
  department: 'all'
};

// Render UI from schema
const renderedDashboard = novaVision.renderUiFromSchema(dashboardSchema, dashboardData);

// Export the schema and rendered dashboard
module.exports = {
  dashboardConfig,
  dashboardSchema,
  dashboardData,
  renderedDashboard
};

// Example of how to use the rendered dashboard in a React application:
/*
import React from 'react';
import { DashboardRenderer } from 'novavision-react';

function ComplianceDashboard() {
  const [dashboardData, setDashboardData] = React.useState({
    dateRange: {
      start: '2023-01-01',
      end: '2023-12-31'
    },
    department: 'all'
  });

  const handleFilterChange = (newData) => {
    setDashboardData(newData);
    // Fetch updated dashboard data based on filters
  };

  return (
    <DashboardRenderer
      schema={dashboardSchema}
      data={dashboardData}
      onFilterChange={handleFilterChange}
    />
  );
}

export default ComplianceDashboard;
*/
