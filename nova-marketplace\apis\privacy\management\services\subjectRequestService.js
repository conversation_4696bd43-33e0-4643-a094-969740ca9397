/**
 * Subject Request Service
 * 
 * This service handles business logic for data subject requests.
 */

const { SubjectRequest } = require('../models');
const { logger } = require('../utils/logger');

/**
 * Get all subject requests with pagination and filtering
 * 
 * @param {Object} options - Query options
 * @param {number} options.page - Page number
 * @param {number} options.limit - Items per page
 * @param {Object} options.filter - Filter criteria
 * @param {Object} options.sort - Sort criteria
 * @returns {Promise<Object>} - Paginated results
 */
async function getAllRequests(options) {
  try {
    const { page = 1, limit = 10, filter = {}, sort = { createdAt: -1 } } = options;
    const skip = (page - 1) * limit;
    
    // Execute query with pagination
    const requests = await SubjectRequest
      .find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit);
    
    // Get total count for pagination
    const total = await SubjectRequest.countDocuments(filter);
    
    return {
      data: requests,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error(`<PERSON>rror getting subject requests: ${error.message}`);
    throw error;
  }
}

/**
 * Get a subject request by ID
 * 
 * @param {string} id - Request ID
 * @returns {Promise<Object>} - Subject request
 */
async function getRequestById(id) {
  try {
    const request = await SubjectRequest.findById(id);
    
    if (!request) {
      const error = new Error('Data subject request not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    return request;
  } catch (error) {
    logger.error(`Error getting subject request by ID: ${error.message}`);
    throw error;
  }
}

/**
 * Create a new subject request
 * 
 * @param {Object} requestData - Request data
 * @returns {Promise<Object>} - Created request
 */
async function createRequest(requestData) {
  try {
    const request = new SubjectRequest(requestData);
    await request.save();
    
    logger.info(`Created new subject request with ID: ${request._id}`);
    return request;
  } catch (error) {
    logger.error(`Error creating subject request: ${error.message}`);
    throw error;
  }
}

/**
 * Update a subject request
 * 
 * @param {string} id - Request ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object>} - Updated request
 */
async function updateRequest(id, updateData) {
  try {
    const request = await getRequestById(id);
    
    // Update only the fields that are provided in the update data
    Object.keys(updateData).forEach(key => {
      request[key] = updateData[key];
    });
    
    await request.save();
    
    logger.info(`Updated subject request with ID: ${id}`);
    return request;
  } catch (error) {
    logger.error(`Error updating subject request: ${error.message}`);
    throw error;
  }
}

/**
 * Process a subject request
 * 
 * @param {string} id - Request ID
 * @returns {Promise<Object>} - Processed request
 */
async function processRequest(id) {
  try {
    const request = await getRequestById(id);
    
    // Update the status
    request.status = 'in-progress';
    
    // In a real implementation, this would:
    // 1. Identify all systems that may contain the data subject's personal data
    // 2. Execute appropriate actions on each system (export, delete, update)
    // 3. Update the request status and affected systems
    
    // Mock affected systems
    request.affectedSystems = [
      {
        systemId: 'system-1',
        systemName: 'CRM System',
        status: 'in-progress',
        details: 'Processing data export'
      },
      {
        systemId: 'system-2',
        systemName: 'Marketing System',
        status: 'pending',
        details: 'Scheduled for processing'
      }
    ];
    
    await request.save();
    
    logger.info(`Processed subject request with ID: ${id}`);
    return request;
  } catch (error) {
    logger.error(`Error processing subject request: ${error.message}`);
    throw error;
  }
}

/**
 * Generate a data export for a subject request
 * 
 * @param {string} id - Request ID
 * @returns {Promise<Object>} - Export data
 */
async function generateDataExport(id) {
  try {
    const request = await getRequestById(id);
    
    if (request.requestType !== 'access') {
      const error = new Error('Data export is only available for access requests');
      error.name = 'ValidationError';
      throw error;
    }
    
    // In a real implementation, this would generate a comprehensive data export
    // For now, we'll just return a mock export
    const exportData = {
      id: request._id,
      exportId: `export-${Date.now()}`,
      exportUrl: `https://api.novafuse.com/privacy/management/exports/export-${Date.now()}`,
      exportFormat: 'json',
      exportSize: '1.2 MB',
      exportCreatedAt: new Date(),
      exportExpiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
    };
    
    logger.info(`Generated data export for subject request with ID: ${id}`);
    return exportData;
  } catch (error) {
    logger.error(`Error generating data export: ${error.message}`);
    throw error;
  }
}

/**
 * Identify affected systems for a subject request
 * 
 * @param {string} id - Request ID
 * @returns {Promise<Object>} - Affected systems data
 */
async function identifyAffectedSystems(id) {
  try {
    const request = await getRequestById(id);
    
    // In a real implementation, this would:
    // 1. Query system inventory
    // 2. Check data mapping
    // 3. Determine which systems contain the data subject's personal data
    
    // For now, we'll just return mock affected systems
    const affectedSystems = [
      {
        systemId: 'system-1',
        systemName: 'CRM System',
        dataCategories: ['contact_info', 'preferences'],
        processingPurposes: ['customer_service', 'marketing'],
        retentionPeriod: '2 years',
        accessMethod: 'API'
      },
      {
        systemId: 'system-2',
        systemName: 'Marketing System',
        dataCategories: ['contact_info', 'preferences', 'marketing_activity'],
        processingPurposes: ['marketing'],
        retentionPeriod: '3 years',
        accessMethod: 'Database'
      },
      {
        systemId: 'system-3',
        systemName: 'Analytics Platform',
        dataCategories: ['usage_data', 'preferences'],
        processingPurposes: ['analytics'],
        retentionPeriod: '1 year',
        accessMethod: 'API'
      }
    ];
    
    logger.info(`Identified affected systems for subject request with ID: ${id}`);
    
    return {
      id: request._id,
      dataSubjectEmail: request.dataSubjectEmail,
      affectedSystems
    };
  } catch (error) {
    logger.error(`Error identifying affected systems: ${error.message}`);
    throw error;
  }
}

module.exports = {
  getAllRequests,
  getRequestById,
  createRequest,
  updateRequest,
  processRequest,
  generateDataExport,
  identifyAffectedSystems
};
