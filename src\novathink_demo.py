"""
Demo script for NovaThink (NUCI) - Universal Compliance Intelligence.

This script demonstrates how to use NovaThink to manage compliance knowledge.
"""

import os
import sys
import json
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import NovaThink components
from novathink import KnowledgeManager, KnowledgeAPI

def create_sample_data():
    """Create sample data for the knowledge base."""
    logger.info("Creating sample data for the knowledge base")

    # Initialize the Knowledge API
    knowledge_api = KnowledgeAPI({
        'data_dir': os.path.join(os.path.dirname(__file__), 'knowledge_output')
    })

    # Create frameworks
    frameworks = []

    # GDPR
    gdpr = knowledge_api.create_entity('framework', {
        'name': 'General Data Protection Regulation (GDPR)',
        'description': 'The General Data Protection Regulation is a regulation in EU law on data protection and privacy in the European Union and the European Economic Area.',
        'version': '2016/679',
        'issuer': 'European Union',
        'url': 'https://gdpr.eu/',
        'category': 'privacy',
        'tags': ['privacy', 'data protection', 'eu', 'regulation'],
        'domains': [
            {
                'id': 'gdpr_principles',
                'name': 'Principles',
                'description': 'Principles relating to processing of personal data'
            },
            {
                'id': 'gdpr_rights',
                'name': 'Rights of the Data Subject',
                'description': 'Rights of the data subject'
            },
            {
                'id': 'gdpr_controller',
                'name': 'Controller and Processor',
                'description': 'Responsibilities of the controller and processor'
            },
            {
                'id': 'gdpr_transfers',
                'name': 'Transfers of Personal Data',
                'description': 'Transfers of personal data to third countries or international organisations'
            },
            {
                'id': 'gdpr_remedies',
                'name': 'Remedies, Liability and Penalties',
                'description': 'Remedies, liability and penalties'
            }
        ]
    })

    frameworks.append(gdpr)

    # SOC 2
    soc2 = knowledge_api.create_entity('framework', {
        'name': 'SOC 2',
        'description': 'SOC 2 is a voluntary compliance standard for service organizations, developed by the American Institute of CPAs (AICPA), which specifies how organizations should manage customer data.',
        'version': '2017',
        'issuer': 'AICPA',
        'url': 'https://www.aicpa.org/interestareas/frc/assuranceadvisoryservices/serviceorganization-smanagement.html',
        'category': 'security',
        'tags': ['security', 'privacy', 'compliance', 'audit'],
        'domains': [
            {
                'id': 'soc2_security',
                'name': 'Security',
                'description': 'The system is protected against unauthorized access (both physical and logical).'
            },
            {
                'id': 'soc2_availability',
                'name': 'Availability',
                'description': 'The system is available for operation and use as committed or agreed.'
            },
            {
                'id': 'soc2_processing_integrity',
                'name': 'Processing Integrity',
                'description': 'System processing is complete, accurate, timely, and authorized.'
            },
            {
                'id': 'soc2_confidentiality',
                'name': 'Confidentiality',
                'description': 'Information designated as confidential is protected as committed or agreed.'
            },
            {
                'id': 'soc2_privacy',
                'name': 'Privacy',
                'description': 'Personal information is collected, used, retained, disclosed, and disposed of in conformity with the commitments in the entity\'s privacy notice and with criteria set forth in Generally Accepted Privacy Principles (GAPP).'
            }
        ]
    })

    frameworks.append(soc2)

    # Create controls
    controls = []

    # GDPR Controls
    data_subject_rights = knowledge_api.create_entity('control', {
        'name': 'Data Subject Rights',
        'description': 'Implement processes for handling data subject rights requests',
        'framework_id': gdpr['id'],
        'domain_id': 'gdpr_rights',
        'control_number': 'GDPR-DSR-01',
        'objective': 'Ensure that data subjects can exercise their rights under GDPR',
        'risk': 'Failure to comply with data subject rights requests can result in regulatory fines and reputational damage',
        'implementation_guidance': 'Implement processes and procedures for handling data subject rights requests, including verification of identity, response timelines, and documentation',
        'assessment_guidance': 'Review processes and procedures for handling data subject rights requests, sample test requests, and review documentation',
        'evidence_requirements': [
            'Data subject rights request process documentation',
            'Sample data subject rights request responses',
            'Training materials for staff handling data subject rights requests'
        ],
        'tags': ['gdpr', 'data subject rights', 'privacy']
    })

    controls.append(data_subject_rights)

    data_protection_impact_assessment = knowledge_api.create_entity('control', {
        'name': 'Data Protection Impact Assessment',
        'description': 'Conduct data protection impact assessments for high-risk processing',
        'framework_id': gdpr['id'],
        'domain_id': 'gdpr_principles',
        'control_number': 'GDPR-DPIA-01',
        'objective': 'Identify and mitigate privacy risks before processing personal data',
        'risk': 'Failure to conduct DPIAs for high-risk processing can result in regulatory fines and privacy violations',
        'implementation_guidance': 'Implement a DPIA process that includes risk assessment, mitigation measures, and documentation',
        'assessment_guidance': 'Review DPIA process, sample completed DPIAs, and verify that high-risk processing activities have been assessed',
        'evidence_requirements': [
            'DPIA process documentation',
            'Sample completed DPIAs',
            'Risk assessment methodology'
        ],
        'tags': ['gdpr', 'dpia', 'risk assessment', 'privacy']
    })

    controls.append(data_protection_impact_assessment)

    # SOC 2 Controls
    access_control = knowledge_api.create_entity('control', {
        'name': 'Access Control',
        'description': 'Implement access controls to restrict access to information assets',
        'framework_id': soc2['id'],
        'domain_id': 'soc2_security',
        'control_number': 'SOC2-AC-01',
        'objective': 'Ensure that access to information assets is restricted to authorized individuals',
        'risk': 'Unauthorized access to information assets can result in data breaches and security incidents',
        'implementation_guidance': 'Implement role-based access control, least privilege, and regular access reviews',
        'assessment_guidance': 'Review access control policies, sample user access rights, and verify that access reviews are conducted regularly',
        'evidence_requirements': [
            'Access control policy',
            'User access rights review documentation',
            'Role-based access control matrix'
        ],
        'tags': ['soc2', 'access control', 'security']
    })

    controls.append(access_control)

    incident_response = knowledge_api.create_entity('control', {
        'name': 'Incident Response',
        'description': 'Implement incident response processes to detect and respond to security incidents',
        'framework_id': soc2['id'],
        'domain_id': 'soc2_security',
        'control_number': 'SOC2-IR-01',
        'objective': 'Ensure that security incidents are detected, contained, and remediated in a timely manner',
        'risk': 'Failure to respond to security incidents can result in extended outages, data loss, and reputational damage',
        'implementation_guidance': 'Implement incident response processes that include detection, containment, eradication, recovery, and lessons learned',
        'assessment_guidance': 'Review incident response plan, sample incident reports, and verify that incidents are handled according to the plan',
        'evidence_requirements': [
            'Incident response plan',
            'Sample incident reports',
            'Incident response team training materials'
        ],
        'tags': ['soc2', 'incident response', 'security']
    })

    controls.append(incident_response)

    # Create control mappings
    mappings = []

    # Map GDPR Data Subject Rights to SOC 2 Privacy
    mapping1 = knowledge_api.create_relationship(
        'control_to_control',
        'control',
        data_subject_rights['id'],
        'control',
        access_control['id'],
        {
            'relationship_type': 'related_to',
            'strength': 'moderate',
            'notes': 'GDPR Data Subject Rights are related to SOC 2 Access Control as both involve managing access to data'
        }
    )

    mappings.append(mapping1)

    # Create guidance
    guidance = []

    dsr_guidance = knowledge_api.create_entity('guidance', {
        'title': 'Implementing Data Subject Rights Processes',
        'content': '''
# Implementing Data Subject Rights Processes

## Overview

The General Data Protection Regulation (GDPR) grants individuals (data subjects) specific rights regarding their personal data. Organizations must implement processes to handle data subject rights requests effectively.

## Key Data Subject Rights

1. **Right to Access**: Data subjects have the right to know if their personal data is being processed, where, and for what purpose.
2. **Right to Rectification**: Data subjects have the right to have inaccurate personal data rectified.
3. **Right to Erasure**: Data subjects have the right to request the deletion of their personal data.
4. **Right to Restrict Processing**: Data subjects have the right to request the restriction of processing of their personal data.
5. **Right to Data Portability**: Data subjects have the right to receive their personal data in a structured, commonly used, and machine-readable format.
6. **Right to Object**: Data subjects have the right to object to the processing of their personal data.
7. **Right to Not Be Subject to Automated Decision-Making**: Data subjects have the right not to be subject to a decision based solely on automated processing.

## Implementation Steps

1. **Establish a Data Subject Rights Request Process**:
   - Create a dedicated email address or web form for data subject rights requests
   - Develop templates for responding to different types of requests
   - Establish verification procedures to confirm the identity of requesters

2. **Define Roles and Responsibilities**:
   - Designate individuals responsible for handling data subject rights requests
   - Ensure that staff are trained on data subject rights and request handling procedures

3. **Document the Process**:
   - Create process documentation for handling data subject rights requests
   - Develop flowcharts for different types of requests

4. **Implement Technical Measures**:
   - Ensure that systems can support data subject rights requests
   - Implement mechanisms for data extraction, rectification, and erasure

5. **Establish Response Timelines**:
   - Develop procedures to ensure that requests are handled within the required timeframe (typically 30 days)
   - Implement monitoring to track request status and response times

6. **Maintain Records**:
   - Keep records of all data subject rights requests and responses
   - Document any exemptions or reasons for denying requests

## Best Practices

- Regularly review and update the data subject rights request process
- Conduct training for staff involved in handling data subject rights requests
- Perform periodic audits to ensure compliance with GDPR requirements
- Consider implementing a dedicated data subject rights management system for larger organizations
''',
        'framework_id': gdpr['id'],
        'control_id': data_subject_rights['id'],
        'author': 'Privacy Team',
        'created_at': datetime.now().isoformat(),
        'updated_at': datetime.now().isoformat(),
        'category': 'implementation',
        'tags': ['gdpr', 'data subject rights', 'privacy', 'implementation']
    })

    guidance.append(dsr_guidance)

    # Create resources
    resources = []

    dsr_template = knowledge_api.create_entity('resource', {
        'name': 'Data Subject Rights Request Form Template',
        'description': 'Template form for data subject rights requests',
        'type': 'template',
        'framework_id': gdpr['id'],
        'control_id': data_subject_rights['id'],
        'author': 'Privacy Team',
        'created_at': datetime.now().isoformat(),
        'updated_at': datetime.now().isoformat(),
        'tags': ['gdpr', 'data subject rights', 'template', 'form']
    })

    resources.append(dsr_template)

    # Create glossary terms
    glossary_terms = []

    dpia_term = knowledge_api.create_entity('glossary_term', {
        'term': 'Data Protection Impact Assessment',
        'definition': 'A process designed to help organizations identify and minimize the data protection risks of a project. Required under GDPR for high-risk processing activities.',
        'abbreviation': 'DPIA',
        'framework_id': gdpr['id'],
        'source': 'GDPR Article 35',
        'tags': ['gdpr', 'dpia', 'risk assessment', 'privacy']
    })

    glossary_terms.append(dpia_term)

    # Create FAQs
    faqs = []

    dsr_faq = knowledge_api.create_entity('faq', {
        'question': 'What is the timeframe for responding to a data subject rights request?',
        'answer': 'Under GDPR, organizations must respond to data subject rights requests without undue delay and at the latest within one month of receipt of the request. This period can be extended by two further months where necessary, taking into account the complexity and number of the requests. The organization must inform the data subject of any such extension within one month of receipt of the request, together with the reasons for the delay.',
        'framework_id': gdpr['id'],
        'control_id': data_subject_rights['id'],
        'category': 'compliance',
        'tags': ['gdpr', 'data subject rights', 'timeframe', 'response']
    })

    faqs.append(dsr_faq)

    return {
        'frameworks': frameworks,
        'controls': controls,
        'mappings': mappings,
        'guidance': guidance,
        'resources': resources,
        'glossary_terms': glossary_terms,
        'faqs': faqs
    }

def demonstrate_knowledge_base():
    """Demonstrate NovaThink (NUCI) - Universal Compliance Intelligence."""
    logger.info("Demonstrating NovaThink (NUCI) - Universal Compliance Intelligence")

    # Initialize the output directory
    output_dir = os.path.join(os.path.dirname(__file__), 'knowledge_output')
    os.makedirs(output_dir, exist_ok=True)

    # Step 1: Initialize the Knowledge API
    logger.info("Step 1: Initializing the Knowledge API")

    knowledge_api = KnowledgeAPI({
        'data_dir': output_dir
    })

    # Step 2: Create sample data
    logger.info("Step 2: Creating sample data")

    sample_data = create_sample_data()

    # Step 3: Get all frameworks
    logger.info("Step 3: Getting all frameworks")

    frameworks = knowledge_api.get_frameworks()

    # Save frameworks to a file
    with open(os.path.join(output_dir, 'frameworks.json'), 'w', encoding='utf-8') as f:
        json.dump(frameworks, f, indent=2)

    # Step 4: Get controls for a framework
    logger.info("Step 4: Getting controls for a framework")

    gdpr_framework = next((f for f in frameworks if 'GDPR' in f['name']), None)

    if gdpr_framework:
        gdpr_controls = knowledge_api.get_framework_controls(gdpr_framework['id'])

        # Save GDPR controls to a file
        with open(os.path.join(output_dir, 'gdpr_controls.json'), 'w', encoding='utf-8') as f:
            json.dump(gdpr_controls, f, indent=2)

    # Step 5: Get guidance for a control
    logger.info("Step 5: Getting guidance for a control")

    if gdpr_controls:
        dsr_control = next((c for c in gdpr_controls if 'Data Subject Rights' in c['name']), None)

        if dsr_control:
            dsr_guidance = knowledge_api.get_control_guidance(dsr_control['id'])

            # Save DSR guidance to a file
            with open(os.path.join(output_dir, 'dsr_guidance.json'), 'w', encoding='utf-8') as f:
                json.dump(dsr_guidance, f, indent=2)

    # Step 6: Search the knowledge base
    logger.info("Step 6: Searching the knowledge base")

    search_results = knowledge_api.search('data subject rights')

    # Save search results to a file
    with open(os.path.join(output_dir, 'search_results.json'), 'w', encoding='utf-8') as f:
        json.dump(search_results, f, indent=2)

    # Step 7: Get glossary terms
    logger.info("Step 7: Getting glossary terms")

    glossary_terms = knowledge_api.get_glossary_terms()

    # Save glossary terms to a file
    with open(os.path.join(output_dir, 'glossary_terms.json'), 'w', encoding='utf-8') as f:
        json.dump(glossary_terms, f, indent=2)

    # Step 8: Get FAQs
    logger.info("Step 8: Getting FAQs")

    faqs = knowledge_api.get_faqs()

    # Save FAQs to a file
    with open(os.path.join(output_dir, 'faqs.json'), 'w', encoding='utf-8') as f:
        json.dump(faqs, f, indent=2)

    # Step 9: Generate a summary report
    logger.info("Step 9: Generating a summary report")

    summary_report = {
        'timestamp': datetime.now().isoformat(),
        'frameworks': len(frameworks),
        'controls': len(sample_data['controls']),
        'mappings': len(sample_data['mappings']),
        'guidance': len(sample_data['guidance']),
        'resources': len(sample_data['resources']),
        'glossary_terms': len(sample_data['glossary_terms']),
        'faqs': len(sample_data['faqs']),
        'output_directory': output_dir
    }

    with open(os.path.join(output_dir, 'summary_report.json'), 'w', encoding='utf-8') as f:
        json.dump(summary_report, f, indent=2)

    logger.info("Knowledge base demonstration completed")
    logger.info(f"Output files saved to: {output_dir}")

    return summary_report

def main():
    """Main function."""
    logger.info("Starting NovaThink (NUCI) - Universal Compliance Intelligence demo")

    try:
        # Demonstrate the knowledge base
        summary_report = demonstrate_knowledge_base()

        logger.info("NovaThink demo completed successfully")
        logger.info(f"Summary report: {summary_report}")
        logger.info(f"All output files are in: {os.path.join(os.path.dirname(os.path.abspath(__file__)), 'knowledge_output')}")

    except Exception as e:
        logger.error(f"Demo failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
