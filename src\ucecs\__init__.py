"""
Universal Compliance Evidence Collection System (UCECS).

A system that automates the collection, validation, and management of compliance evidence.
"""

from .core.evidence_manager import EvidenceManager
from .core.collector_manager import CollectorManager
from .core.validator_manager import ValidatorManager
from .core.storage_manager import StorageManager

__version__ = '0.1.0'
__all__ = [
    'EvidenceManager',
    'CollectorManager',
    'ValidatorManager',
    'StorageManager'
]
