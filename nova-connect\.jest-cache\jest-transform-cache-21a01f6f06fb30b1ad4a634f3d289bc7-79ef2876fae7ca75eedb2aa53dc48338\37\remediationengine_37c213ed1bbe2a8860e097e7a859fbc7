eca35b275b67dd48b15fa58bde4fe244
/**
 * NovaConnect Remediation Engine
 * 
 * High-performance remediation engine capable of executing complex
 * multi-step remediation workflows for security and compliance findings.
 */

const {
  performance
} = require('perf_hooks');
const EventEmitter = require('events');
class RemediationEngine extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = {
      enableMetrics: true,
      maxConcurrentRemediations: 10,
      defaultTimeout: 30000,
      // 30 seconds
      retryCount: 3,
      retryDelay: 1000,
      // 1 second
      ...options
    };

    // Registry of remediation actions
    this.actions = new Map();

    // Registry of connectors
    this.connectors = new Map();

    // Active remediations
    this.activeRemediations = new Map();

    // Initialize metrics
    this.metrics = {
      totalRemediations: 0,
      successfulRemediations: 0,
      failedRemediations: 0,
      totalSteps: 0,
      successfulSteps: 0,
      failedSteps: 0,
      totalRemediationTime: 0,
      averageRemediationTime: 0,
      totalStepTime: 0,
      averageStepTime: 0
    };
  }

  /**
   * Register a remediation action
   * @param {string} actionName - Name of the action
   * @param {Function} handler - Action handler function
   * @param {Object} metadata - Action metadata
   */
  registerAction(actionName, handler, metadata = {}) {
    if (typeof handler !== 'function') {
      throw new Error('Action handler must be a function');
    }
    this.actions.set(actionName, {
      handler,
      metadata: {
        description: '',
        parameters: [],
        resourceTypes: [],
        providers: [],
        ...metadata
      }
    });
  }

  /**
   * Register a connector
   * @param {string} connectorName - Name of the connector
   * @param {Object} connector - Connector instance
   */
  registerConnector(connectorName, connector) {
    this.connectors.set(connectorName, connector);
  }

  /**
   * Execute a remediation workflow
   * @param {Object} scenario - Remediation scenario
   * @returns {Object} - Remediation result
   */
  async executeRemediation(scenario) {
    const startTime = this.options.enableMetrics ? performance.now() : 0;

    // Generate a unique remediation ID if not provided
    const remediationId = scenario.id || `rem-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Create remediation context
    const remediationContext = {
      id: remediationId,
      scenario,
      startTime: new Date().toISOString(),
      endTime: null,
      status: 'in_progress',
      steps: [],
      result: null,
      error: null
    };

    // Store in active remediations
    this.activeRemediations.set(remediationId, remediationContext);

    // Emit start event
    this.emit('remediation:start', {
      remediationId,
      scenario
    });
    try {
      // Execute each step in sequence
      if (scenario.remediationSteps && scenario.remediationSteps.length > 0) {
        for (const step of scenario.remediationSteps) {
          const stepResult = await this._executeStep(step, remediationContext);
          remediationContext.steps.push(stepResult);

          // Emit step completion event
          this.emit('remediation:step', {
            remediationId,
            step: stepResult
          });

          // If step failed and is critical, stop remediation
          if (!stepResult.success && step.critical !== false) {
            remediationContext.status = 'failed';
            remediationContext.error = {
              message: `Critical step ${step.id} failed: ${stepResult.error?.message || 'Unknown error'}`,
              step: step.id
            };
            break;
          }
        }
      }

      // If we got here without setting status to failed, it's successful
      if (remediationContext.status === 'in_progress') {
        remediationContext.status = 'completed';
      }

      // Set result based on status
      remediationContext.result = {
        success: remediationContext.status === 'completed',
        status: remediationContext.status,
        steps: remediationContext.steps.map(step => ({
          id: step.id,
          action: step.action,
          status: step.status,
          success: step.success
        }))
      };
    } catch (error) {
      // Handle unexpected errors
      remediationContext.status = 'failed';
      remediationContext.error = {
        message: error.message,
        stack: error.stack
      };
      remediationContext.result = {
        success: false,
        status: 'failed',
        error: error.message
      };

      // Emit error event
      this.emit('remediation:error', {
        remediationId,
        error
      });
    }

    // Set end time
    remediationContext.endTime = new Date().toISOString();

    // Update metrics
    if (this.options.enableMetrics) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.metrics.totalRemediations++;
      if (remediationContext.status === 'completed') {
        this.metrics.successfulRemediations++;
      } else {
        this.metrics.failedRemediations++;
      }
      this.metrics.totalRemediationTime += duration;
      this.metrics.averageRemediationTime = this.metrics.totalRemediationTime / this.metrics.totalRemediations;
    }

    // Emit completion event
    this.emit('remediation:complete', {
      remediationId,
      result: remediationContext.result
    });

    // Remove from active remediations
    this.activeRemediations.delete(remediationId);
    return {
      id: remediationId,
      status: remediationContext.status,
      startTime: remediationContext.startTime,
      endTime: remediationContext.endTime,
      steps: remediationContext.steps,
      result: remediationContext.result,
      error: remediationContext.error
    };
  }

  /**
   * Execute a remediation step
   * @param {Object} step - Step configuration
   * @param {Object} context - Remediation context
   * @returns {Object} - Step result
   * @private
   */
  async _executeStep(step, context) {
    const startTime = this.options.enableMetrics ? performance.now() : 0;

    // Create step context
    const stepContext = {
      id: step.id,
      action: step.action,
      parameters: step.parameters || {},
      startTime: new Date().toISOString(),
      endTime: null,
      status: 'in_progress',
      success: false,
      result: null,
      error: null,
      attempts: 0
    };

    // Emit step start event
    this.emit('remediation:step:start', {
      remediationId: context.id,
      step: stepContext
    });
    try {
      // Get the action handler
      const action = this.actions.get(step.action);
      if (!action) {
        throw new Error(`Action ${step.action} not registered`);
      }

      // Execute the action with retry logic
      let result;
      let error;
      let success = false;
      for (let attempt = 1; attempt <= this.options.retryCount + 1; attempt++) {
        stepContext.attempts = attempt;
        try {
          // Execute the action
          result = await action.handler({
            parameters: step.parameters || {},
            resource: context.scenario.resource,
            finding: context.scenario.finding,
            context: {
              remediationId: context.id,
              stepId: step.id,
              framework: context.scenario.framework,
              control: context.scenario.control,
              previousSteps: context.steps
            }
          });
          success = true;
          break;
        } catch (err) {
          error = err;

          // Emit retry event
          if (attempt <= this.options.retryCount) {
            this.emit('remediation:step:retry', {
              remediationId: context.id,
              step: stepContext,
              attempt,
              error
            });

            // Wait before retrying
            await new Promise(resolve => setTimeout(resolve, this.options.retryDelay));
          }
        }
      }

      // Update step context based on result
      if (success) {
        stepContext.status = 'completed';
        stepContext.success = true;
        stepContext.result = result;
      } else {
        stepContext.status = 'failed';
        stepContext.success = false;
        stepContext.error = {
          message: error.message,
          stack: error.stack
        };
      }
    } catch (error) {
      // Handle unexpected errors
      stepContext.status = 'failed';
      stepContext.success = false;
      stepContext.error = {
        message: error.message,
        stack: error.stack
      };

      // Emit error event
      this.emit('remediation:step:error', {
        remediationId: context.id,
        step: stepContext,
        error
      });
    }

    // Set end time
    stepContext.endTime = new Date().toISOString();

    // Update metrics
    if (this.options.enableMetrics) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.metrics.totalSteps++;
      if (stepContext.success) {
        this.metrics.successfulSteps++;
      } else {
        this.metrics.failedSteps++;
      }
      this.metrics.totalStepTime += duration;
      this.metrics.averageStepTime = this.metrics.totalStepTime / this.metrics.totalSteps;
    }

    // Emit step completion event
    this.emit('remediation:step:complete', {
      remediationId: context.id,
      step: stepContext
    });
    return stepContext;
  }

  /**
   * Resolve conflicting compliance requirements
   * @param {Object} scenario - Conflict scenario
   * @returns {Object} - Resolution result
   */
  async resolveConflict(scenario) {
    const startTime = this.options.enableMetrics ? performance.now() : 0;
    try {
      // Generate a unique resolution ID
      const resolutionId = `res-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // Emit start event
      this.emit('conflict:start', {
        resolutionId,
        scenario
      });

      // Implement conflict resolution logic
      // This is a simplified implementation - in a real system, this would be more complex
      const frameworks = scenario.frameworks;
      const conflictingRequirements = scenario.conflictingRequirements;

      // Default strategy: apply the most stringent requirement
      // In a real implementation, this would use more sophisticated logic
      const primaryFramework = frameworks[0];

      // Create resolution result
      const resolution = {
        id: resolutionId,
        success: true,
        strategy: 'most_stringent',
        appliedFramework: primaryFramework,
        justification: `Applied the most stringent requirements from ${frameworks.join(' and ')}`,
        details: {
          conflicts: conflictingRequirements.map(req => ({
            framework: req.framework,
            control: req.control,
            requirement: req.requirement,
            applied: req.framework === primaryFramework
          }))
        },
        startTime: new Date(startTime).toISOString(),
        endTime: new Date().toISOString()
      };

      // Emit completion event
      this.emit('conflict:complete', {
        resolutionId,
        resolution
      });

      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        if (!this.metrics.conflictResolutions) {
          this.metrics.conflictResolutions = {
            total: 0,
            successful: 0,
            failed: 0,
            totalTime: 0,
            averageTime: 0
          };
        }
        this.metrics.conflictResolutions.total++;
        this.metrics.conflictResolutions.successful++;
        this.metrics.conflictResolutions.totalTime += duration;
        this.metrics.conflictResolutions.averageTime = this.metrics.conflictResolutions.totalTime / this.metrics.conflictResolutions.total;
      }
      return resolution;
    } catch (error) {
      // Handle unexpected errors
      const endTime = performance.now();

      // Emit error event
      this.emit('conflict:error', {
        scenario,
        error
      });

      // Update metrics
      if (this.options.enableMetrics) {
        const duration = endTime - startTime;
        if (!this.metrics.conflictResolutions) {
          this.metrics.conflictResolutions = {
            total: 0,
            successful: 0,
            failed: 0,
            totalTime: 0,
            averageTime: 0
          };
        }
        this.metrics.conflictResolutions.total++;
        this.metrics.conflictResolutions.failed++;
        this.metrics.conflictResolutions.totalTime += duration;
        this.metrics.conflictResolutions.averageTime = this.metrics.conflictResolutions.totalTime / this.metrics.conflictResolutions.total;
      }
      return {
        success: false,
        error: {
          message: error.message,
          stack: error.stack
        },
        startTime: new Date(startTime).toISOString(),
        endTime: new Date().toISOString()
      };
    }
  }

  /**
   * Handle a failed remediation
   * @param {Object} scenario - Failed remediation scenario
   * @returns {Object} - Handling result
   */
  async handleFailedRemediation(scenario) {
    const startTime = this.options.enableMetrics ? performance.now() : 0;
    try {
      // Generate a unique handling ID
      const handlingId = `handle-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // Emit start event
      this.emit('handling:start', {
        handlingId,
        scenario
      });

      // Create escalation
      const escalation = {
        id: `escalation-${Date.now()}`,
        type: 'manual_intervention',
        priority: scenario.severity || 'medium',
        assignedTo: scenario.escalationTarget || 'security-team',
        status: 'pending',
        details: {
          remediationId: scenario.remediationId,
          framework: scenario.framework,
          control: scenario.control,
          resource: scenario.resource,
          finding: scenario.finding,
          error: scenario.error
        }
      };

      // In a real implementation, this would create a ticket in a ticketing system
      // or send a notification to the appropriate team

      // Create handling result
      const result = {
        id: handlingId,
        success: true,
        escalation,
        startTime: new Date(startTime).toISOString(),
        endTime: new Date().toISOString()
      };

      // Emit completion event
      this.emit('handling:complete', {
        handlingId,
        result
      });

      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        if (!this.metrics.failureHandling) {
          this.metrics.failureHandling = {
            total: 0,
            successful: 0,
            failed: 0,
            totalTime: 0,
            averageTime: 0
          };
        }
        this.metrics.failureHandling.total++;
        this.metrics.failureHandling.successful++;
        this.metrics.failureHandling.totalTime += duration;
        this.metrics.failureHandling.averageTime = this.metrics.failureHandling.totalTime / this.metrics.failureHandling.total;
      }
      return result;
    } catch (error) {
      // Handle unexpected errors
      const endTime = performance.now();

      // Emit error event
      this.emit('handling:error', {
        scenario,
        error
      });

      // Update metrics
      if (this.options.enableMetrics) {
        const duration = endTime - startTime;
        if (!this.metrics.failureHandling) {
          this.metrics.failureHandling = {
            total: 0,
            successful: 0,
            failed: 0,
            totalTime: 0,
            averageTime: 0
          };
        }
        this.metrics.failureHandling.total++;
        this.metrics.failureHandling.failed++;
        this.metrics.failureHandling.totalTime += duration;
        this.metrics.failureHandling.averageTime = this.metrics.failureHandling.totalTime / this.metrics.failureHandling.total;
      }
      return {
        success: false,
        error: {
          message: error.message,
          stack: error.stack
        },
        startTime: new Date(startTime).toISOString(),
        endTime: new Date().toISOString()
      };
    }
  }

  /**
   * Get metrics for the remediation engine
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return this.metrics;
  }

  /**
   * Get all registered actions
   * @returns {Array} - Actions with metadata
   */
  getActions() {
    const actions = [];
    for (const [name, action] of this.actions.entries()) {
      actions.push({
        name,
        ...action.metadata
      });
    }
    return actions;
  }

  /**
   * Get all active remediations
   * @returns {Array} - Active remediations
   */
  getActiveRemediations() {
    return Array.from(this.activeRemediations.values());
  }
}
module.exports = RemediationEngine;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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