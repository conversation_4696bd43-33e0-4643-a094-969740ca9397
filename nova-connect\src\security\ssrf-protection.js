/**
 * NovaFuse Universal API Connector SSRF Protection
 * 
 * This module provides SSRF (Server-Side Request Forgery) protection for the UAC.
 */

const { URL } = require('url');
const dns = require('dns').promises;
const net = require('net');

/**
 * SSRF Protection Utility
 * 
 * Checks if a URL is safe to connect to by validating against a whitelist
 * and checking for private IP addresses.
 */
class SSRFProtection {
  constructor(options = {}) {
    this.options = {
      allowedHosts: [],
      allowedDomains: [],
      allowedProtocols: ['https:'],
      allowPrivateIPs: false,
      ...options
    };
  }

  /**
   * Check if a URL is safe to connect to
   * @param {string} url - URL to check
   * @returns {Promise<boolean>} - Whether the URL is safe
   */
  async isSafeUrl(url) {
    try {
      // Parse URL
      const parsedUrl = new URL(url);
      
      // Check protocol
      if (!this.options.allowedProtocols.includes(parsedUrl.protocol)) {
        console.warn(`SSRF Protection: Blocked URL with disallowed protocol: ${parsedUrl.protocol}`);
        return false;
      }
      
      // Check hostname against whitelist if enabled
      if (this.options.allowedHosts.length > 0) {
        const isHostAllowed = this.options.allowedHosts.some(host => 
          parsedUrl.hostname === host
        );
        
        if (!isHostAllowed) {
          // Check if hostname matches any allowed domain patterns
          const isDomainAllowed = this.options.allowedDomains.some(domain => {
            // Allow wildcards (e.g., *.example.com)
            if (domain.startsWith('*.')) {
              const baseDomain = domain.substring(2);
              return parsedUrl.hostname.endsWith(baseDomain);
            }
            return parsedUrl.hostname === domain;
          });
          
          if (!isDomainAllowed) {
            console.warn(`SSRF Protection: Blocked URL with disallowed host: ${parsedUrl.hostname}`);
            return false;
          }
        }
      }
      
      // Check for private IP addresses
      if (!this.options.allowPrivateIPs) {
        // Check if hostname is an IP address
        if (net.isIP(parsedUrl.hostname)) {
          if (this.isPrivateIP(parsedUrl.hostname)) {
            console.warn(`SSRF Protection: Blocked URL with private IP: ${parsedUrl.hostname}`);
            return false;
          }
        } else {
          // Resolve hostname to IP address
          try {
            const addresses = await dns.resolve(parsedUrl.hostname);
            
            // Check if any resolved IP is private
            for (const address of addresses) {
              if (this.isPrivateIP(address)) {
                console.warn(`SSRF Protection: Blocked URL with hostname resolving to private IP: ${address}`);
                return false;
              }
            }
          } catch (error) {
            console.error(`SSRF Protection: Error resolving hostname: ${parsedUrl.hostname}`, error);
            return false;
          }
        }
      }
      
      return true;
    } catch (error) {
      console.error('SSRF Protection: Error checking URL:', error);
      return false;
    }
  }

  /**
   * Check if an IP address is private
   * @param {string} ip - IP address to check
   * @returns {boolean} - Whether the IP is private
   */
  isPrivateIP(ip) {
    // Check for loopback addresses
    if (ip === '127.0.0.1' || ip === '::1') {
      return true;
    }
    
    // Check for private IPv4 ranges
    if (ip.startsWith('10.') || 
        ip.startsWith('172.16.') || ip.startsWith('172.17.') || 
        ip.startsWith('172.18.') || ip.startsWith('172.19.') || 
        ip.startsWith('172.20.') || ip.startsWith('172.21.') || 
        ip.startsWith('172.22.') || ip.startsWith('172.23.') || 
        ip.startsWith('172.24.') || ip.startsWith('172.25.') || 
        ip.startsWith('172.26.') || ip.startsWith('172.27.') || 
        ip.startsWith('172.28.') || ip.startsWith('172.29.') || 
        ip.startsWith('172.30.') || ip.startsWith('172.31.') || 
        ip.startsWith('192.168.')) {
      return true;
    }
    
    // Check for link-local addresses
    if (ip.startsWith('169.254.')) {
      return true;
    }
    
    // Check for private IPv6 ranges
    if (ip.startsWith('fc00:') || ip.startsWith('fd00:')) {
      return true;
    }
    
    return false;
  }

  /**
   * Add allowed hosts to the whitelist
   * @param {string|string[]} hosts - Host(s) to add
   */
  addAllowedHosts(hosts) {
    if (Array.isArray(hosts)) {
      this.options.allowedHosts = [...this.options.allowedHosts, ...hosts];
    } else {
      this.options.allowedHosts.push(hosts);
    }
  }

  /**
   * Add allowed domains to the whitelist
   * @param {string|string[]} domains - Domain(s) to add
   */
  addAllowedDomains(domains) {
    if (Array.isArray(domains)) {
      this.options.allowedDomains = [...this.options.allowedDomains, ...domains];
    } else {
      this.options.allowedDomains.push(domains);
    }
  }

  /**
   * Set allowed protocols
   * @param {string|string[]} protocols - Protocol(s) to allow
   */
  setAllowedProtocols(protocols) {
    if (Array.isArray(protocols)) {
      this.options.allowedProtocols = protocols;
    } else {
      this.options.allowedProtocols = [protocols];
    }
  }

  /**
   * Allow or disallow private IP addresses
   * @param {boolean} allow - Whether to allow private IPs
   */
  setAllowPrivateIPs(allow) {
    this.options.allowPrivateIPs = allow;
  }
}

module.exports = SSRFProtection;
