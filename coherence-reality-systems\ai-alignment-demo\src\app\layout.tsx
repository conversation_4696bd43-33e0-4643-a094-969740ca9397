import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'NovaAlign Studio - NovaFuse Technologies',
  description: 'The World\'s First Consciousness-Based AI Alignment Platform - Powered by NovaConnect',
  keywords: ['AI Alignment', 'Superintelligence', 'AI Safety', 'Consciousness', 'AGI', 'ASI'],
  authors: [{ name: 'NovaFuse Technologies' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <script src="/novaconnect-integration.js" async></script>
      </head>
      <body className={inter.className}>
        {children}
      </body>
    </html>
  )
}
