"""
Example of using custom validation scripts with the Validator Manager.

This example demonstrates how to create and use custom validation scripts
with the enhanced Validator Manager.
"""

import os
import sys
import json
import logging

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.validator_manager import ValidatorManager, ValidationLevel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the custom validation script example."""
    # Create a validator manager
    validator_manager = ValidatorManager()
    
    try:
        # Create a custom validation script for AWS S3 bucket policy validation
        script_id = "s3_bucket_policy_validator"
        script_content = """
"""\"
S3 Bucket Policy Validator

This script validates that S3 bucket policies meet security requirements.

<AUTHOR>
@version 1.0.0
@confidence_level CRITICAL
@tags aws, s3, security, compliance
\"""

# Get the evidence data
data = evidence.get('data', {})
bucket_name = data.get('bucket_name')
bucket_policy = data.get('bucket_policy', {})

# Initialize validation flags
has_secure_transport = False
has_restricted_access = False
has_encryption = False
errors = []
warnings = []

# Check for HTTPS requirement
if bucket_policy.get('Statement'):
    for statement in bucket_policy['Statement']:
        # Check for secure transport
        if statement.get('Effect') == 'Deny' and statement.get('Condition', {}).get('Bool', {}).get('aws:SecureTransport') == 'false':
            has_secure_transport = True
        
        # Check for restricted access
        if statement.get('Effect') == 'Allow' and statement.get('Principal') == '*':
            if not statement.get('Condition'):
                warnings.append("Bucket policy allows public access without conditions")
                has_restricted_access = False
            else:
                has_restricted_access = True
        else:
            has_restricted_access = True
        
        # Check for encryption
        if statement.get('Effect') == 'Deny' and statement.get('Condition', {}).get('StringNotEquals', {}).get('s3:x-amz-server-side-encryption') in ['AES256', 'aws:kms']:
            has_encryption = True

# Add errors for missing requirements
if not has_secure_transport:
    errors.append("Bucket policy does not enforce HTTPS (aws:SecureTransport)")

if not has_restricted_access:
    errors.append("Bucket policy allows unrestricted public access")

if not has_encryption:
    warnings.append("Bucket policy does not enforce server-side encryption")

# Calculate score based on requirements
score = 0
if has_secure_transport:
    score += 40
if has_restricted_access:
    score += 40
if has_encryption:
    score += 20

# Create the validation result
result = ValidationResult(
    validator_id='s3_bucket_policy_validator',
    is_valid=len(errors) == 0,
    confidence_level=ValidationLevel.CRITICAL,
    score=score,
    details={
        'bucket_name': bucket_name,
        'has_secure_transport': has_secure_transport,
        'has_restricted_access': has_restricted_access,
        'has_encryption': has_encryption
    },
    errors=errors,
    warnings=warnings
)
"""
        
        # Create the validation script
        script = validator_manager.create_validation_script(
            script_id=script_id,
            content=script_content
        )
        
        logger.info(f"Created validation script: {script_id}")
        logger.info(f"Script metadata: {json.dumps(script['metadata'], indent=2)}")
        
        # Create a sample S3 bucket policy evidence (compliant)
        compliant_evidence = {
            'type': 'aws',
            'source': 's3',
            'data': {
                'bucket_name': 'example-bucket',
                'bucket_policy': {
                    'Version': '2012-10-17',
                    'Statement': [
                        {
                            'Sid': 'DenyInsecureTransport',
                            'Effect': 'Deny',
                            'Principal': '*',
                            'Action': 's3:*',
                            'Resource': 'arn:aws:s3:::example-bucket/*',
                            'Condition': {
                                'Bool': {
                                    'aws:SecureTransport': 'false'
                                }
                            }
                        },
                        {
                            'Sid': 'RestrictAccess',
                            'Effect': 'Allow',
                            'Principal': {
                                'AWS': 'arn:aws:iam::123456789012:role/S3AccessRole'
                            },
                            'Action': 's3:GetObject',
                            'Resource': 'arn:aws:s3:::example-bucket/*'
                        },
                        {
                            'Sid': 'EnforceEncryption',
                            'Effect': 'Deny',
                            'Principal': '*',
                            'Action': 's3:PutObject',
                            'Resource': 'arn:aws:s3:::example-bucket/*',
                            'Condition': {
                                'StringNotEquals': {
                                    's3:x-amz-server-side-encryption': 'AES256'
                                }
                            }
                        }
                    ]
                }
            }
        }
        
        # Validate the compliant evidence
        compliant_result = validator_manager.validate(script_id, compliant_evidence)
        
        logger.info(f"Compliant validation result: {json.dumps(compliant_result.to_dict(), indent=2)}")
        
        # Create a non-compliant S3 bucket policy evidence
        non_compliant_evidence = {
            'type': 'aws',
            'source': 's3',
            'data': {
                'bucket_name': 'example-bucket-2',
                'bucket_policy': {
                    'Version': '2012-10-17',
                    'Statement': [
                        {
                            'Sid': 'PublicAccess',
                            'Effect': 'Allow',
                            'Principal': '*',
                            'Action': 's3:GetObject',
                            'Resource': 'arn:aws:s3:::example-bucket-2/*'
                        }
                    ]
                }
            }
        }
        
        # Validate the non-compliant evidence
        non_compliant_result = validator_manager.validate(script_id, non_compliant_evidence)
        
        logger.info(f"Non-compliant validation result: {json.dumps(non_compliant_result.to_dict(), indent=2)}")
        
        # Create another validation script for password policy validation
        password_script_id = "password_policy_validator"
        password_script_content = """
# Password Policy Validator
# This script validates that password policies meet security requirements

# Get the evidence data
data = evidence.get('data', {})
password_policy = data.get('password_policy', {})

# Initialize validation flags
errors = []
warnings = []

# Check password policy requirements
min_length = password_policy.get('minimum_length', 0)
requires_uppercase = password_policy.get('requires_uppercase', False)
requires_lowercase = password_policy.get('requires_lowercase', False)
requires_numbers = password_policy.get('requires_numbers', False)
requires_symbols = password_policy.get('requires_symbols', False)
max_age = password_policy.get('max_age', 0)
prevent_reuse = password_policy.get('prevent_reuse', 0)

# Validate minimum length
if min_length < 8:
    errors.append(f"Password minimum length ({min_length}) is less than required (8)")
elif min_length < 12:
    warnings.append(f"Password minimum length ({min_length}) is less than recommended (12)")

# Validate character requirements
if not requires_uppercase:
    errors.append("Password policy does not require uppercase letters")
if not requires_lowercase:
    errors.append("Password policy does not require lowercase letters")
if not requires_numbers:
    errors.append("Password policy does not require numbers")
if not requires_symbols:
    warnings.append("Password policy does not require special characters")

# Validate password age
if max_age == 0:
    warnings.append("Password policy does not enforce password expiration")
elif max_age > 90:
    warnings.append(f"Password maximum age ({max_age} days) is greater than recommended (90 days)")

# Validate password reuse
if prevent_reuse < 5:
    warnings.append(f"Password policy prevents reuse of only {prevent_reuse} previous passwords (recommended: 5+)")

# Calculate score based on requirements
score = 0
if min_length >= 8:
    score += 20
    if min_length >= 12:
        score += 10
if requires_uppercase:
    score += 15
if requires_lowercase:
    score += 15
if requires_numbers:
    score += 15
if requires_symbols:
    score += 10
if max_age > 0 and max_age <= 90:
    score += 10
if prevent_reuse >= 5:
    score += 5

# Create the validation result
result = ValidationResult(
    validator_id='password_policy_validator',
    is_valid=len(errors) == 0,
    confidence_level=ValidationLevel.HIGH,
    score=score,
    details={
        'min_length': min_length,
        'requires_uppercase': requires_uppercase,
        'requires_lowercase': requires_lowercase,
        'requires_numbers': requires_numbers,
        'requires_symbols': requires_symbols,
        'max_age': max_age,
        'prevent_reuse': prevent_reuse
    },
    errors=errors,
    warnings=warnings
)
"""
        
        # Create the password policy validation script
        password_script = validator_manager.create_validation_script(
            script_id=password_script_id,
            content=password_script_content,
            name="Password Policy Validator",
            description="Validates that password policies meet security requirements",
            author="NovaFuse",
            version="1.0.0",
            confidence_level="HIGH",
            tags=["security", "password", "compliance"]
        )
        
        logger.info(f"Created validation script: {password_script_id}")
        
        # Create a sample password policy evidence (compliant)
        password_evidence = {
            'type': 'security',
            'source': 'password_policy',
            'data': {
                'password_policy': {
                    'minimum_length': 12,
                    'requires_uppercase': True,
                    'requires_lowercase': True,
                    'requires_numbers': True,
                    'requires_symbols': True,
                    'max_age': 90,
                    'prevent_reuse': 10
                }
            }
        }
        
        # Validate the password policy evidence
        password_result = validator_manager.validate(password_script_id, password_evidence)
        
        logger.info(f"Password policy validation result: {json.dumps(password_result.to_dict(), indent=2)}")
        
        # Clean up
        validator_manager.delete_validation_script(script_id)
        validator_manager.delete_validation_script(password_script_id)
        
    except Exception as e:
        logger.error(f"Error: {e}")

if __name__ == "__main__":
    main()
