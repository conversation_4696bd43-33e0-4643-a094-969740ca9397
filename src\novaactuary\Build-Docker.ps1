# NovaActuary™ Docker Build Script (PowerShell)
# The ∂Ψ=0 Underwriting Revolution

Write-Host ""
Write-Host "🚀 NOVAACTUARY™ DOCKER BUILD SCRIPT" -ForegroundColor Cyan
Write-Host "The ∂Ψ=0 Underwriting Revolution" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host ""

# Display current directory
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "Dockerfile")) {
    Write-Host "❌ Dockerfile not found in current directory" -ForegroundColor Red
    Write-Host "Please navigate to: src\novaactuary" -ForegroundColor Yellow
    Write-Host "Then run this script again" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-Path "docker-compose.yml")) {
    Write-Host "❌ docker-compose.yml not found in current directory" -ForegroundColor Red
    Write-Host "Please navigate to: src\novaactuary" -ForegroundColor Yellow
    Write-Host "Then run this script again" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Found Dockerfile and docker-compose.yml" -ForegroundColor Green
Write-Host ""

# Check Docker Desktop
Write-Host "[STEP 1] Checking Docker Desktop..." -ForegroundColor Blue
try {
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker not found"
    }
    Write-Host "✅ Docker Desktop is available" -ForegroundColor Green
    Write-Host $dockerVersion -ForegroundColor Gray
} catch {
    Write-Host "❌ Docker Desktop not found or not running" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please:" -ForegroundColor Yellow
    Write-Host "1. Install Docker Desktop if not installed" -ForegroundColor Yellow
    Write-Host "2. Start Docker Desktop" -ForegroundColor Yellow
    Write-Host "3. Wait for it to fully initialize" -ForegroundColor Yellow
    Write-Host "4. Run this script again" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

# Check Docker Compose
Write-Host "[STEP 2] Checking Docker Compose..." -ForegroundColor Blue
try {
    $composeVersion = docker-compose --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker Compose not found"
    }
    Write-Host "✅ Docker Compose is available" -ForegroundColor Green
    Write-Host $composeVersion -ForegroundColor Gray
} catch {
    Write-Host "❌ Docker Compose not available" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

# Clean up existing containers
Write-Host "[STEP 3] Cleaning up existing containers..." -ForegroundColor Blue
docker-compose down --remove-orphans 2>$null
Write-Host "✅ Cleanup completed" -ForegroundColor Green
Write-Host ""

# Build the container
Write-Host "[STEP 4] Building NovaActuary™ container..." -ForegroundColor Blue
Write-Host "This will take 5-10 minutes on first build..." -ForegroundColor Yellow
Write-Host "Building with full output for debugging..." -ForegroundColor Yellow
Write-Host ""

# Execute the build
$buildResult = docker-compose build --no-cache novaactuary
$buildExitCode = $LASTEXITCODE

if ($buildExitCode -eq 0) {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "✅ NOVAACTUARY™ CONTAINER BUILD SUCCESS!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    
    # Show built images
    Write-Host "📦 Built Images:" -ForegroundColor Cyan
    docker images | Select-String "novaactuary"
    Write-Host ""
    
    Write-Host "🚀 Next Steps:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "1. Start the container:" -ForegroundColor Yellow
    Write-Host "   docker-compose up -d novaactuary" -ForegroundColor Gray
    Write-Host ""
    Write-Host "2. Check container status:" -ForegroundColor Yellow
    Write-Host "   docker-compose ps" -ForegroundColor Gray
    Write-Host ""
    Write-Host "3. Run health check:" -ForegroundColor Yellow
    Write-Host "   docker-compose exec novaactuary node novaactuary/health-check.js" -ForegroundColor Gray
    Write-Host ""
    Write-Host "4. Run quick test:" -ForegroundColor Yellow
    Write-Host "   docker-compose exec novaactuary node novaactuary/quick-test.js" -ForegroundColor Gray
    Write-Host ""
    Write-Host "5. View logs:" -ForegroundColor Yellow
    Write-Host "   docker-compose logs novaactuary" -ForegroundColor Gray
    Write-Host ""
    Write-Host "6. Stop container:" -ForegroundColor Yellow
    Write-Host "   docker-compose down" -ForegroundColor Gray
    Write-Host ""
    
} else {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "❌ NOVAACTUARY™ CONTAINER BUILD FAILED!" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host ""
    Write-Host "Common solutions:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "1. Ensure Docker Desktop is running" -ForegroundColor Yellow
    Write-Host "2. Check available disk space (need 2GB+)" -ForegroundColor Yellow
    Write-Host "3. Check internet connection" -ForegroundColor Yellow
    Write-Host "4. Try running as Administrator" -ForegroundColor Yellow
    Write-Host "5. Restart Docker Desktop and try again" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "If problems persist, check the error messages above." -ForegroundColor Yellow
    Write-Host ""
}

Read-Host "Press Enter to continue"
