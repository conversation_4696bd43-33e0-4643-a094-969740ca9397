/**
 * Test Session Recording Service
 * 
 * This service handles recording user interactions and feedback during test sessions.
 */

import axios from 'axios';

// Base API URL
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

/**
 * Test Session Recording Service
 */
class TestSessionRecordingService {
  /**
   * Start a new test session
   * @param {Object} sessionData - Test session data
   * @returns {Promise} - Promise that resolves to the created session
   */
  async startSession(sessionData) {
    try {
      const response = await axios.post(`${API_URL}/user-testing/sessions`, sessionData);
      
      // Store session ID in local storage for recovery
      localStorage.setItem('currentTestSessionId', response.data.id);
      
      return response.data;
    } catch (error) {
      console.error('Error starting test session:', error);
      throw error;
    }
  }
  
  /**
   * End a test session
   * @param {string} sessionId - Test session ID
   * @param {Object} sessionData - Test session data
   * @returns {Promise} - Promise that resolves to the updated session
   */
  async endSession(sessionId, sessionData) {
    try {
      const response = await axios.put(`${API_URL}/user-testing/sessions/${sessionId}/end`, sessionData);
      
      // Remove session ID from local storage
      localStorage.removeItem('currentTestSessionId');
      
      return response.data;
    } catch (error) {
      console.error('Error ending test session:', error);
      throw error;
    }
  }
  
  /**
   * Record a user interaction
   * @param {string} sessionId - Test session ID
   * @param {Object} interactionData - Interaction data
   * @returns {Promise} - Promise that resolves to the created interaction
   */
  async recordInteraction(sessionId, interactionData) {
    try {
      const response = await axios.post(
        `${API_URL}/user-testing/sessions/${sessionId}/interactions`, 
        interactionData
      );
      
      return response.data;
    } catch (error) {
      console.error('Error recording interaction:', error);
      
      // Store interaction in local storage for later submission
      this.storeInteractionLocally(sessionId, interactionData);
      
      throw error;
    }
  }
  
  /**
   * Record task completion
   * @param {string} sessionId - Test session ID
   * @param {string} taskId - Task ID
   * @param {Object} taskData - Task completion data
   * @returns {Promise} - Promise that resolves to the updated task
   */
  async recordTaskCompletion(sessionId, taskId, taskData) {
    try {
      const response = await axios.put(
        `${API_URL}/user-testing/sessions/${sessionId}/tasks/${taskId}/complete`, 
        taskData
      );
      
      return response.data;
    } catch (error) {
      console.error('Error recording task completion:', error);
      
      // Store task completion in local storage for later submission
      this.storeTaskCompletionLocally(sessionId, taskId, taskData);
      
      throw error;
    }
  }
  
  /**
   * Record user feedback
   * @param {string} sessionId - Test session ID
   * @param {Object} feedbackData - Feedback data
   * @returns {Promise} - Promise that resolves to the created feedback
   */
  async recordFeedback(sessionId, feedbackData) {
    try {
      const response = await axios.post(
        `${API_URL}/user-testing/sessions/${sessionId}/feedback`, 
        feedbackData
      );
      
      return response.data;
    } catch (error) {
      console.error('Error recording feedback:', error);
      
      // Store feedback in local storage for later submission
      this.storeFeedbackLocally(sessionId, feedbackData);
      
      throw error;
    }
  }
  
  /**
   * Record final feedback
   * @param {string} sessionId - Test session ID
   * @param {Object} feedbackData - Final feedback data
   * @returns {Promise} - Promise that resolves to the updated session
   */
  async recordFinalFeedback(sessionId, feedbackData) {
    try {
      const response = await axios.post(
        `${API_URL}/user-testing/sessions/${sessionId}/final-feedback`, 
        feedbackData
      );
      
      return response.data;
    } catch (error) {
      console.error('Error recording final feedback:', error);
      
      // Store final feedback in local storage for later submission
      this.storeFinalFeedbackLocally(sessionId, feedbackData);
      
      throw error;
    }
  }
  
  /**
   * Submit all locally stored data
   * @param {string} sessionId - Test session ID
   * @returns {Promise} - Promise that resolves when all data is submitted
   */
  async submitLocalData(sessionId) {
    try {
      // Get locally stored data
      const interactions = this.getLocalInteractions(sessionId);
      const taskCompletions = this.getLocalTaskCompletions(sessionId);
      const feedback = this.getLocalFeedback(sessionId);
      const finalFeedback = this.getLocalFinalFeedback(sessionId);
      
      // Submit interactions
      for (const interaction of interactions) {
        await this.recordInteraction(sessionId, interaction);
      }
      
      // Submit task completions
      for (const taskCompletion of taskCompletions) {
        await this.recordTaskCompletion(sessionId, taskCompletion.taskId, taskCompletion.data);
      }
      
      // Submit feedback
      for (const feedbackItem of feedback) {
        await this.recordFeedback(sessionId, feedbackItem);
      }
      
      // Submit final feedback
      if (finalFeedback) {
        await this.recordFinalFeedback(sessionId, finalFeedback);
      }
      
      // Clear local storage
      this.clearLocalData(sessionId);
      
      return true;
    } catch (error) {
      console.error('Error submitting local data:', error);
      throw error;
    }
  }
  
  /**
   * Store interaction locally
   * @param {string} sessionId - Test session ID
   * @param {Object} interactionData - Interaction data
   */
  storeInteractionLocally(sessionId, interactionData) {
    const key = `interactions_${sessionId}`;
    const interactions = JSON.parse(localStorage.getItem(key) || '[]');
    
    interactions.push({
      ...interactionData,
      timestamp: new Date().toISOString()
    });
    
    localStorage.setItem(key, JSON.stringify(interactions));
  }
  
  /**
   * Store task completion locally
   * @param {string} sessionId - Test session ID
   * @param {string} taskId - Task ID
   * @param {Object} taskData - Task completion data
   */
  storeTaskCompletionLocally(sessionId, taskId, taskData) {
    const key = `taskCompletions_${sessionId}`;
    const taskCompletions = JSON.parse(localStorage.getItem(key) || '[]');
    
    taskCompletions.push({
      taskId,
      data: {
        ...taskData,
        completedAt: new Date().toISOString()
      }
    });
    
    localStorage.setItem(key, JSON.stringify(taskCompletions));
  }
  
  /**
   * Store feedback locally
   * @param {string} sessionId - Test session ID
   * @param {Object} feedbackData - Feedback data
   */
  storeFeedbackLocally(sessionId, feedbackData) {
    const key = `feedback_${sessionId}`;
    const feedback = JSON.parse(localStorage.getItem(key) || '[]');
    
    feedback.push({
      ...feedbackData,
      timestamp: new Date().toISOString()
    });
    
    localStorage.setItem(key, JSON.stringify(feedback));
  }
  
  /**
   * Store final feedback locally
   * @param {string} sessionId - Test session ID
   * @param {Object} feedbackData - Final feedback data
   */
  storeFinalFeedbackLocally(sessionId, feedbackData) {
    const key = `finalFeedback_${sessionId}`;
    
    localStorage.setItem(key, JSON.stringify({
      ...feedbackData,
      timestamp: new Date().toISOString()
    }));
  }
  
  /**
   * Get locally stored interactions
   * @param {string} sessionId - Test session ID
   * @returns {Array} - Array of interactions
   */
  getLocalInteractions(sessionId) {
    const key = `interactions_${sessionId}`;
    return JSON.parse(localStorage.getItem(key) || '[]');
  }
  
  /**
   * Get locally stored task completions
   * @param {string} sessionId - Test session ID
   * @returns {Array} - Array of task completions
   */
  getLocalTaskCompletions(sessionId) {
    const key = `taskCompletions_${sessionId}`;
    return JSON.parse(localStorage.getItem(key) || '[]');
  }
  
  /**
   * Get locally stored feedback
   * @param {string} sessionId - Test session ID
   * @returns {Array} - Array of feedback
   */
  getLocalFeedback(sessionId) {
    const key = `feedback_${sessionId}`;
    return JSON.parse(localStorage.getItem(key) || '[]');
  }
  
  /**
   * Get locally stored final feedback
   * @param {string} sessionId - Test session ID
   * @returns {Object|null} - Final feedback or null if not found
   */
  getLocalFinalFeedback(sessionId) {
    const key = `finalFeedback_${sessionId}`;
    const data = localStorage.getItem(key);
    
    return data ? JSON.parse(data) : null;
  }
  
  /**
   * Clear locally stored data
   * @param {string} sessionId - Test session ID
   */
  clearLocalData(sessionId) {
    localStorage.removeItem(`interactions_${sessionId}`);
    localStorage.removeItem(`taskCompletions_${sessionId}`);
    localStorage.removeItem(`feedback_${sessionId}`);
    localStorage.removeItem(`finalFeedback_${sessionId}`);
  }
  
  /**
   * Check if there is a current test session
   * @returns {string|null} - Session ID or null if no session
   */
  getCurrentSessionId() {
    return localStorage.getItem('currentTestSessionId');
  }
  
  /**
   * Get test session
   * @param {string} sessionId - Test session ID
   * @returns {Promise} - Promise that resolves to the session
   */
  async getSession(sessionId) {
    try {
      const response = await axios.get(`${API_URL}/user-testing/sessions/${sessionId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting test session:', error);
      throw error;
    }
  }
}

// Create and export service instance
const testSessionRecordingService = new TestSessionRecordingService();
export default testSessionRecordingService;
