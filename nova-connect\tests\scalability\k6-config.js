/**
 * NovaConnect UAC k6 Load Testing Configuration
 * 
 * This file contains the configuration for k6 load testing.
 */

export const options = {
  // Define stages for ramping up and down load
  stages: [
    // Warm-up: Ramp up to 50 users over 1 minute
    { duration: '1m', target: 50 },
    
    // Steady load: Stay at 50 users for 3 minutes
    { duration: '3m', target: 50 },
    
    // Ramp-up: Increase to 200 users over 2 minutes
    { duration: '2m', target: 200 },
    
    // Stress test: Stay at 200 users for 5 minutes
    { duration: '5m', target: 200 },
    
    // Spike test: Spike to 500 users for 30 seconds
    { duration: '30s', target: 500 },
    
    // Recovery: Scale back to 100 users over 1 minute
    { duration: '1m', target: 100 },
    
    // Cool-down: Ramp down to 0 users over 1 minute
    { duration: '1m', target: 0 }
  ],
  
  // Define thresholds for pass/fail criteria
  thresholds: {
    // 95% of requests must complete within 500ms
    'http_req_duration': ['p(95)<500'],
    
    // 99% of requests must complete within 1s
    'http_req_duration{scenario:api}': ['p(99)<1000'],
    
    // 99.9% of requests must succeed
    'http_req_failed': ['rate<0.001'],
    
    // Data normalization must complete within 10ms for 95% of requests
    'data_normalization_duration': ['p(95)<10'],
    
    // Error rate must be less than 0.1%
    'error_rate': ['rate<0.001']
  },
  
  // Define scenarios for different test cases
  scenarios: {
    // API endpoints scenario
    api: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '1m', target: 50 },
        { duration: '3m', target: 50 },
        { duration: '2m', target: 200 },
        { duration: '5m', target: 200 },
        { duration: '30s', target: 500 },
        { duration: '1m', target: 100 },
        { duration: '1m', target: 0 }
      ],
      gracefulRampDown: '30s'
    },
    
    // Data normalization scenario
    normalization: {
      executor: 'constant-arrival-rate',
      rate: 1000,
      timeUnit: '1s',
      duration: '5m',
      preAllocatedVUs: 100,
      maxVUs: 200
    },
    
    // Connector scenario
    connectors: {
      executor: 'per-vu-iterations',
      vus: 50,
      iterations: 100,
      maxDuration: '10m'
    }
  },
  
  // Define batch size for metrics
  batchPerHost: 10,
  
  // Define HTTP settings
  http: {
    timeout: '30s'
  }
};
