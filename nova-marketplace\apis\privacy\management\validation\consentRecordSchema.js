/**
 * Consent Record Validation Schema
 * 
 * This file defines the validation schema for consent records.
 */

const Joi = require('joi');

// Create schema
const createConsentRecordSchema = Joi.object({
  dataSubjectId: Joi.string()
    .allow(null, '')
    .messages({
      'string.base': 'Data subject ID must be a string'
    }),
  
  dataSubjectName: Joi.string()
    .required()
    .messages({
      'any.required': 'Data subject name is required',
      'string.empty': 'Data subject name cannot be empty'
    }),
  
  dataSubjectEmail: Joi.string()
    .email()
    .required()
    .messages({
      'any.required': 'Data subject email is required',
      'string.empty': 'Data subject email cannot be empty',
      'string.email': 'Data subject email must be a valid email address'
    }),
  
  consentType: Joi.string()
    .valid('marketing', 'analytics', 'profiling', 'third_party', 'research', 'other')
    .required()
    .messages({
      'any.required': 'Consent type is required',
      'string.empty': 'Consent type cannot be empty',
      'any.only': 'Consent type must be one of: marketing, analytics, profiling, third_party, research, other'
    }),
  
  consentDetails: Joi.string()
    .required()
    .messages({
      'any.required': 'Consent details are required',
      'string.empty': 'Consent details cannot be empty'
    }),
  
  purposes: Joi.array()
    .items(Joi.string())
    .required()
    .messages({
      'any.required': 'Purposes are required',
      'array.base': 'Purposes must be an array'
    }),
  
  dataCategories: Joi.array()
    .items(Joi.string())
    .required()
    .messages({
      'any.required': 'Data categories are required',
      'array.base': 'Data categories must be an array'
    }),
  
  thirdParties: Joi.array()
    .items(Joi.object({
      name: Joi.string().required(),
      purpose: Joi.string().required(),
      location: Joi.string().allow(null, '')
    }))
    .default([])
    .messages({
      'array.base': 'Third parties must be an array'
    }),
  
  collectionMethod: Joi.string()
    .valid('web-form', 'mobile-app', 'paper-form', 'verbal', 'email', 'other')
    .required()
    .messages({
      'any.required': 'Collection method is required',
      'string.empty': 'Collection method cannot be empty',
      'any.only': 'Collection method must be one of: web-form, mobile-app, paper-form, verbal, email, other'
    }),
  
  collectionLocation: Joi.string()
    .allow(null, '')
    .messages({
      'string.base': 'Collection location must be a string'
    }),
  
  collectionTimestamp: Joi.date()
    .required()
    .messages({
      'any.required': 'Collection timestamp is required',
      'date.base': 'Collection timestamp must be a valid date'
    }),
  
  ipAddress: Joi.string()
    .allow(null, '')
    .messages({
      'string.base': 'IP address must be a string'
    }),
  
  userAgent: Joi.string()
    .allow(null, '')
    .messages({
      'string.base': 'User agent must be a string'
    }),
  
  formVersion: Joi.string()
    .allow(null, '')
    .messages({
      'string.base': 'Form version must be a string'
    }),
  
  status: Joi.string()
    .valid('active', 'withdrawn', 'expired')
    .default('active')
    .messages({
      'any.only': 'Status must be one of: active, withdrawn, expired'
    }),
  
  withdrawalTimestamp: Joi.date()
    .allow(null)
    .messages({
      'date.base': 'Withdrawal timestamp must be a valid date'
    }),
  
  withdrawalMethod: Joi.string()
    .valid('web-form', 'mobile-app', 'paper-form', 'verbal', 'email', 'other')
    .allow(null, '')
    .messages({
      'any.only': 'Withdrawal method must be one of: web-form, mobile-app, paper-form, verbal, email, other'
    }),
  
  withdrawalReason: Joi.string()
    .allow(null, '')
    .messages({
      'string.base': 'Withdrawal reason must be a string'
    }),
  
  expiryDate: Joi.date()
    .allow(null)
    .messages({
      'date.base': 'Expiry date must be a valid date'
    }),
  
  proofOfConsent: Joi.string()
    .allow(null, '')
    .messages({
      'string.base': 'Proof of consent must be a string'
    }),
  
  proofType: Joi.string()
    .valid('screenshot', 'form-submission', 'log-record', 'email', 'other')
    .allow(null, '')
    .messages({
      'any.only': 'Proof type must be one of: screenshot, form-submission, log-record, email, other'
    }),
  
  notes: Joi.array()
    .items(Joi.object({
      content: Joi.string().required(),
      createdBy: Joi.string().required(),
      createdAt: Joi.date().default(Date.now)
    }))
    .default([])
    .messages({
      'array.base': 'Notes must be an array'
    })
});

// Update schema
const updateConsentRecordSchema = createConsentRecordSchema.fork(
  ['dataSubjectName', 'dataSubjectEmail', 'consentType', 'consentDetails', 'purposes', 'dataCategories', 'collectionMethod', 'collectionTimestamp'],
  (schema) => schema.optional()
);

// Withdraw consent schema
const withdrawConsentSchema = Joi.object({
  withdrawalMethod: Joi.string()
    .valid('web-form', 'mobile-app', 'paper-form', 'verbal', 'email', 'other')
    .required()
    .messages({
      'any.required': 'Withdrawal method is required',
      'string.empty': 'Withdrawal method cannot be empty',
      'any.only': 'Withdrawal method must be one of: web-form, mobile-app, paper-form, verbal, email, other'
    }),
  
  withdrawalReason: Joi.string()
    .allow(null, '')
    .messages({
      'string.base': 'Withdrawal reason must be a string'
    })
});

module.exports = {
  createConsentRecordSchema,
  updateConsentRecordSchema,
  withdrawConsentSchema
};
