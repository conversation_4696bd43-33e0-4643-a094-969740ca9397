/**
 * Monitoring Dashboard Page
 * 
 * This page provides tools for monitoring connector health and managing alerts.
 */

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Button, 
  Container, 
  Tab, 
  Tabs, 
  Typography 
} from '@mui/material';
import DashboardIcon from '@mui/icons-material/Dashboard';
import NotificationsIcon from '@mui/icons-material/Notifications';
import SettingsIcon from '@mui/icons-material/Settings';
import RefreshIcon from '@mui/icons-material/Refresh';
import DashboardLayout from '../../layouts/DashboardLayout';
import ConnectorHealthDashboard from '../../components/monitoring/ConnectorHealthDashboard';
import AlertList from '../../components/monitoring/AlertList';
import AlertConfiguration from '../../components/monitoring/AlertConfiguration';

const MonitoringPage = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [connectors, setConnectors] = useState([]);
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  
  useEffect(() => {
    fetchData();
  }, []);
  
  const fetchData = async () => {
    setLoading(true);
    
    try {
      // In a real implementation, this would fetch data from the API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock connectors data
      const mockConnectors = [
        {
          id: 'conn1',
          name: 'GitHub API Connector',
          description: 'Connect to GitHub API for repository management',
          category: 'development',
          version: '1.0.0',
          status: 'active',
          baseUrl: 'https://api.github.com'
        },
        {
          id: 'conn2',
          name: 'Jira API Connector',
          description: 'Connect to Jira for issue tracking and project management',
          category: 'development',
          version: '1.2.0',
          status: 'active',
          baseUrl: 'https://your-domain.atlassian.net/rest/api/3'
        },
        {
          id: 'conn3',
          name: 'Salesforce API Connector',
          description: 'Connect to Salesforce CRM',
          category: 'sales',
          version: '2.0.1',
          status: 'active',
          baseUrl: 'https://your-instance.salesforce.com/services/data/v56.0'
        },
        {
          id: 'conn4',
          name: 'Google Analytics API Connector',
          description: 'Connect to Google Analytics for website analytics',
          category: 'analytics',
          version: '0.9.0',
          status: 'draft',
          baseUrl: 'https://analyticsdata.googleapis.com/v1beta'
        },
        {
          id: 'conn5',
          name: 'Stripe API Connector',
          description: 'Connect to Stripe for payment processing',
          category: 'finance',
          version: '1.5.0',
          status: 'active',
          baseUrl: 'https://api.stripe.com/v1'
        },
        {
          id: 'conn6',
          name: 'AWS S3 Connector',
          description: 'Connect to Amazon S3 for file storage',
          category: 'cloud',
          version: '1.1.0',
          status: 'active',
          baseUrl: 'https://s3.amazonaws.com'
        },
        {
          id: 'conn7',
          name: 'Slack API Connector',
          description: 'Connect to Slack for messaging and notifications',
          category: 'communication',
          version: '0.8.5',
          status: 'draft',
          baseUrl: 'https://slack.com/api'
        },
        {
          id: 'conn8',
          name: 'HubSpot API Connector',
          description: 'Connect to HubSpot for marketing and CRM',
          category: 'marketing',
          version: '1.0.0',
          status: 'deprecated',
          baseUrl: 'https://api.hubapi.com'
        }
      ];
      
      // Mock alerts data
      const mockAlerts = [
        {
          id: 'alert1',
          connector: {
            id: 'conn1',
            name: 'GitHub API Connector',
            version: '1.0.0',
            category: 'development',
            status: 'active'
          },
          severity: 'high',
          status: 'active',
          message: 'High response time detected',
          description: 'The GitHub API connector is experiencing high response times exceeding the configured threshold.',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          details: {
            responseTime: 1250,
            threshold: 1000,
            endpoint: '/repos/{owner}/{repo}/issues'
          },
          comments: [
            {
              user: 'System',
              timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
              text: 'Alert created'
            }
          ],
          timeline: [
            {
              timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
              message: 'Alert created'
            }
          ]
        },
        {
          id: 'alert2',
          connector: {
            id: 'conn3',
            name: 'Salesforce API Connector',
            version: '2.0.1',
            category: 'sales',
            status: 'active'
          },
          severity: 'critical',
          status: 'active',
          message: 'Connection failure',
          description: 'The Salesforce API connector is unable to establish a connection to the API endpoint.',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          details: {
            errorType: 'connection',
            errorMessage: 'Connection timeout after 30000ms'
          },
          comments: [
            {
              user: 'System',
              timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
              text: 'Alert created'
            },
            {
              user: 'John Doe',
              timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
              text: 'Investigating the issue. Salesforce status page shows no outages.'
            }
          ],
          timeline: [
            {
              timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
              message: 'Alert created'
            },
            {
              timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
              message: 'Alert acknowledged by John Doe'
            }
          ]
        },
        {
          id: 'alert3',
          connector: {
            id: 'conn5',
            name: 'Stripe API Connector',
            version: '1.5.0',
            category: 'finance',
            status: 'active'
          },
          severity: 'medium',
          status: 'acknowledged',
          message: 'Low success rate',
          description: 'The Stripe API connector is experiencing a lower than expected success rate.',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          details: {
            successRate: 85,
            threshold: 90,
            requestCount: 120,
            errorCount: 18
          },
          comments: [
            {
              user: 'System',
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
              text: 'Alert created'
            },
            {
              user: 'Jane Smith',
              timestamp: new Date(Date.now() - 1.5 * 60 * 60 * 1000).toISOString(),
              text: 'Acknowledged. Investigating the issue.'
            }
          ],
          timeline: [
            {
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
              message: 'Alert created'
            },
            {
              timestamp: new Date(Date.now() - 1.5 * 60 * 60 * 1000).toISOString(),
              message: 'Alert acknowledged by Jane Smith'
            }
          ]
        },
        {
          id: 'alert4',
          connector: {
            id: 'conn6',
            name: 'AWS S3 Connector',
            version: '1.1.0',
            category: 'cloud',
            status: 'active'
          },
          severity: 'low',
          status: 'resolved',
          message: 'Increased error rate',
          description: 'The AWS S3 connector experienced a temporary increase in error rate.',
          timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
          details: {
            errorRate: 12,
            threshold: 10,
            requestCount: 250,
            errorCount: 30
          },
          comments: [
            {
              user: 'System',
              timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
              text: 'Alert created'
            },
            {
              user: 'Mike Johnson',
              timestamp: new Date(Date.now() - 4.5 * 60 * 60 * 1000).toISOString(),
              text: 'Investigating the issue.'
            },
            {
              user: 'Mike Johnson',
              timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
              text: 'Issue resolved. AWS had a brief service degradation in us-east-1 region.'
            }
          ],
          timeline: [
            {
              timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
              message: 'Alert created'
            },
            {
              timestamp: new Date(Date.now() - 4.5 * 60 * 60 * 1000).toISOString(),
              message: 'Alert acknowledged by Mike Johnson'
            },
            {
              timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
              message: 'Alert resolved by Mike Johnson'
            }
          ]
        }
      ];
      
      setConnectors(mockConnectors);
      setAlerts(mockAlerts);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  const handleRefresh = () => {
    fetchData();
  };
  
  return (
    <DashboardLayout>
      <Container maxWidth="xl">
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1">
            Monitoring & Alerts
          </Typography>
          
          <Box>
            <Typography variant="body2" color="textSecondary" sx={{ mr: 2, display: 'inline' }}>
              Last updated: {lastUpdated.toLocaleTimeString()}
            </Typography>
            
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRefresh}
              disabled={loading}
              sx={{ mr: 2 }}
            >
              Refresh
            </Button>
          </Box>
        </Box>
        
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={activeTab} onChange={handleTabChange} aria-label="monitoring tabs">
            <Tab label="Dashboard" value="dashboard" icon={<DashboardIcon />} iconPosition="start" />
            <Tab 
              label={`Alerts ${alerts.filter(a => a.status === 'active').length > 0 ? `(${alerts.filter(a => a.status === 'active').length})` : ''}`} 
              value="alerts" 
              icon={<NotificationsIcon />} 
              iconPosition="start" 
            />
            <Tab label="Configuration" value="configuration" icon={<SettingsIcon />} iconPosition="start" />
          </Tabs>
        </Box>
        
        {activeTab === 'dashboard' && (
          <ConnectorHealthDashboard connectors={connectors} />
        )}
        
        {activeTab === 'alerts' && (
          <AlertList alerts={alerts} />
        )}
        
        {activeTab === 'configuration' && (
          <AlertConfiguration connectors={connectors} />
        )}
      </Container>
    </DashboardLayout>
  );
};

export default MonitoringPage;
