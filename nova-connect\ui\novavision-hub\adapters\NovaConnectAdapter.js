/**
 * NovaConnect Adapter for NovaVision
 * 
 * This adapter connects NovaConnect with NovaVision, allowing NovaVision to render UI schemas
 * based on NovaConnect data and functionality.
 */

/**
 * NovaConnect Adapter class
 */
class NovaConnectAdapter {
  /**
   * Constructor
   * 
   * @param {Object} options - Adapter options
   * @param {Object} options.novaConnect - NovaConnect instance
   * @param {Object} options.novaVision - NovaVision instance
   * @param {boolean} [options.enableLogging=false] - Whether to enable logging
   * @param {Array} [options.subscribeTopics=[]] - Topics to subscribe to
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: false,
      subscribeTopics: [],
      ...options
    };
    
    this.novaConnect = options.novaConnect;
    this.novaVision = options.novaVision;
    this.logger = options.logger || console;
    
    if (!this.novaConnect) {
      throw new Error('NovaConnect instance is required');
    }
    
    if (!this.novaVision) {
      throw new Error('NovaVision instance is required');
    }
    
    this.logger.info('NovaConnect Adapter initialized');
  }
  
  /**
   * Initialize the adapter
   * 
   * @returns {Promise} - Promise that resolves when initialization is complete
   */
  async initialize() {
    if (this.options.enableLogging) {
      this.logger.info('Initializing NovaConnect Adapter...');
    }
    
    try {
      // Subscribe to NovaConnect events
      if (this.options.subscribeTopics.length > 0) {
        await this._subscribeToEvents();
      }
      
      if (this.options.enableLogging) {
        this.logger.info('NovaConnect Adapter initialized successfully');
      }
      
      return true;
    } catch (error) {
      this.logger.error('Error initializing NovaConnect Adapter', error);
      throw error;
    }
  }
  
  /**
   * Subscribe to NovaConnect events
   * 
   * @private
   * @returns {Promise} - Promise that resolves when subscription is complete
   */
  async _subscribeToEvents() {
    if (this.options.enableLogging) {
      this.logger.info('Subscribing to NovaConnect events...');
    }
    
    try {
      // Subscribe to events
      for (const topic of this.options.subscribeTopics) {
        if (topic.startsWith('novaConnect.')) {
          const eventName = topic.split('.')[1];
          
          // Subscribe to event
          this.novaConnect.on(eventName, (data) => {
            if (this.options.enableLogging) {
              this.logger.info(`NovaConnect event: ${eventName}`, data);
            }
            
            // Handle event
            this._handleEvent(eventName, data);
          });
          
          if (this.options.enableLogging) {
            this.logger.info(`Subscribed to NovaConnect event: ${eventName}`);
          }
        }
      }
      
      return true;
    } catch (error) {
      this.logger.error('Error subscribing to NovaConnect events', error);
      throw error;
    }
  }
  
  /**
   * Handle NovaConnect event
   * 
   * @private
   * @param {string} eventName - Event name
   * @param {Object} data - Event data
   */
  _handleEvent(eventName, data) {
    // Handle event based on event name
    switch (eventName) {
      case 'connectorCreated':
      case 'connectorUpdated':
      case 'connectorDeleted':
        // Update connector list UI
        this._updateConnectorListUI();
        break;
      
      case 'connectorExecuted':
        // Update connector execution UI
        this._updateConnectorExecutionUI(data);
        break;
      
      case 'userAuthenticated':
      case 'userLoggedOut':
        // Update user UI
        this._updateUserUI();
        break;
      
      default:
        // Unknown event
        if (this.options.enableLogging) {
          this.logger.warn(`Unknown NovaConnect event: ${eventName}`);
        }
        break;
    }
  }
  
  /**
   * Update connector list UI
   * 
   * @private
   */
  async _updateConnectorListUI() {
    try {
      // Get connector list schema
      const schema = await this.getUISchema('connectorList');
      
      // Notify NovaVision of schema update
      this.novaVision.updateSchema('novaConnect.connectorList', schema);
    } catch (error) {
      this.logger.error('Error updating connector list UI', error);
    }
  }
  
  /**
   * Update connector execution UI
   * 
   * @private
   * @param {Object} data - Execution data
   */
  async _updateConnectorExecutionUI(data) {
    try {
      // Get connector execution schema
      const schema = await this.getUISchema('connectorExecution', { connectorId: data.connectorId });
      
      // Notify NovaVision of schema update
      this.novaVision.updateSchema('novaConnect.connectorExecution', schema);
    } catch (error) {
      this.logger.error('Error updating connector execution UI', error);
    }
  }
  
  /**
   * Update user UI
   * 
   * @private
   */
  async _updateUserUI() {
    try {
      // Get user schema
      const schema = await this.getUISchema('user');
      
      // Notify NovaVision of schema update
      this.novaVision.updateSchema('novaConnect.user', schema);
    } catch (error) {
      this.logger.error('Error updating user UI', error);
    }
  }
  
  /**
   * Get UI schema for NovaConnect
   * 
   * @param {string} schemaType - Schema type
   * @param {Object} options - Schema options
   * @returns {Promise<Object>} - UI schema
   */
  async getUISchema(schemaType, options = {}) {
    if (this.options.enableLogging) {
      this.logger.info(`Getting UI schema for NovaConnect.${schemaType}...`);
    }
    
    try {
      // Get schema based on schema type
      switch (schemaType) {
        case 'connectorList':
          return await this._getConnectorListSchema(options);
        
        case 'connectorDetail':
          return await this._getConnectorDetailSchema(options);
        
        case 'connectorExecution':
          return await this._getConnectorExecutionSchema(options);
        
        case 'user':
          return await this._getUserSchema(options);
        
        case 'dashboard':
          return await this.getDashboardSchema(options);
        
        default:
          throw new Error(`Unknown schema type: ${schemaType}`);
      }
    } catch (error) {
      this.logger.error(`Error getting UI schema for NovaConnect.${schemaType}`, error);
      throw error;
    }
  }
  
  /**
   * Get connector list schema
   * 
   * @private
   * @param {Object} options - Schema options
   * @returns {Promise<Object>} - Connector list schema
   */
  async _getConnectorListSchema(options = {}) {
    try {
      // Get connectors from NovaConnect
      const connectors = await this.novaConnect.getConnectors();
      
      // Create connector list schema
      return {
        type: 'table',
        title: 'Connectors',
        columns: [
          { field: 'name', header: 'Name' },
          { field: 'type', header: 'Type' },
          { field: 'status', header: 'Status' },
          { field: 'lastRunAt', header: 'Last Run' },
          { field: 'actions', header: 'Actions' }
        ],
        data: connectors.map(connector => ({
          name: connector.name,
          type: connector.type,
          status: connector.status,
          lastRunAt: connector.lastRunAt,
          actions: {
            type: 'buttonGroup',
            buttons: [
              {
                type: 'button',
                text: 'View',
                variant: 'primary',
                size: 'sm',
                onClick: `novaConnect.viewConnector:${connector.id}`
              },
              {
                type: 'button',
                text: 'Run',
                variant: 'success',
                size: 'sm',
                onClick: `novaConnect.runConnector:${connector.id}`
              },
              {
                type: 'button',
                text: 'Edit',
                variant: 'secondary',
                size: 'sm',
                onClick: `novaConnect.editConnector:${connector.id}`
              },
              {
                type: 'button',
                text: 'Delete',
                variant: 'danger',
                size: 'sm',
                onClick: `novaConnect.deleteConnector:${connector.id}`
              }
            ]
          }
        })),
        actions: [
          {
            type: 'button',
            text: 'Add Connector',
            variant: 'primary',
            onClick: 'novaConnect.addConnector'
          }
        ]
      };
    } catch (error) {
      this.logger.error('Error getting connector list schema', error);
      throw error;
    }
  }
  
  /**
   * Get connector detail schema
   * 
   * @private
   * @param {Object} options - Schema options
   * @returns {Promise<Object>} - Connector detail schema
   */
  async _getConnectorDetailSchema(options = {}) {
    try {
      // Get connector from NovaConnect
      const connector = await this.novaConnect.getConnector(options.connectorId);
      
      // Create connector detail schema
      return {
        type: 'form',
        title: `Connector: ${connector.name}`,
        fields: [
          {
            type: 'textField',
            name: 'name',
            label: 'Name',
            value: connector.name,
            required: true
          },
          {
            type: 'select',
            name: 'type',
            label: 'Type',
            value: connector.type,
            options: [
              { value: 'aws', label: 'AWS' },
              { value: 'azure', label: 'Azure' },
              { value: 'gcp', label: 'Google Cloud' },
              { value: 'github', label: 'GitHub' },
              { value: 'jira', label: 'Jira' },
              { value: 'slack', label: 'Slack' }
            ],
            required: true
          },
          {
            type: 'textField',
            name: 'description',
            label: 'Description',
            value: connector.description,
            multiline: true
          },
          {
            type: 'jsonEditor',
            name: 'config',
            label: 'Configuration',
            value: connector.config
          },
          {
            type: 'jsonEditor',
            name: 'credentials',
            label: 'Credentials',
            value: connector.credentials
          }
        ],
        actions: [
          {
            type: 'button',
            text: 'Save',
            variant: 'primary',
            onClick: 'novaConnect.saveConnector'
          },
          {
            type: 'button',
            text: 'Cancel',
            variant: 'secondary',
            onClick: 'novaConnect.cancelConnector'
          },
          {
            type: 'button',
            text: 'Test',
            variant: 'info',
            onClick: 'novaConnect.testConnector'
          }
        ]
      };
    } catch (error) {
      this.logger.error('Error getting connector detail schema', error);
      throw error;
    }
  }
  
  /**
   * Get dashboard schema
   * 
   * @param {Object} options - Schema options
   * @returns {Promise<Object>} - Dashboard schema
   */
  async getDashboardSchema(options = {}) {
    try {
      // Get connector stats from NovaConnect
      const stats = await this.novaConnect.getConnectorStats();
      
      // Create dashboard schema
      return {
        type: 'card',
        title: 'NovaConnect Dashboard',
        content: {
          type: 'grid',
          columns: 2,
          rows: 2,
          areas: [
            ['connectorStats', 'connectorTypes'],
            ['recentExecutions', 'recentExecutions']
          ],
          components: [
            {
              type: 'card',
              gridArea: 'connectorStats',
              header: 'Connector Stats',
              content: {
                type: 'stats',
                stats: [
                  { label: 'Total Connectors', value: stats.totalConnectors },
                  { label: 'Active Connectors', value: stats.activeConnectors },
                  { label: 'Executions Today', value: stats.executionsToday },
                  { label: 'Success Rate', value: `${stats.successRate}%` }
                ]
              }
            },
            {
              type: 'card',
              gridArea: 'connectorTypes',
              header: 'Connector Types',
              content: {
                type: 'chart',
                chartType: 'pie',
                data: {
                  labels: Object.keys(stats.connectorTypes),
                  datasets: [
                    {
                      data: Object.values(stats.connectorTypes),
                      backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF',
                        '#FF9F40'
                      ]
                    }
                  ]
                }
              }
            },
            {
              type: 'card',
              gridArea: 'recentExecutions',
              header: 'Recent Executions',
              content: {
                type: 'table',
                columns: [
                  { field: 'connector', header: 'Connector' },
                  { field: 'status', header: 'Status' },
                  { field: 'startTime', header: 'Start Time' },
                  { field: 'duration', header: 'Duration' }
                ],
                data: stats.recentExecutions
              }
            }
          ]
        }
      };
    } catch (error) {
      this.logger.error('Error getting dashboard schema', error);
      throw error;
    }
  }
  
  /**
   * Handle action from NovaVision
   * 
   * @param {string} action - Action name
   * @param {Object} data - Action data
   * @returns {Promise<Object>} - Action result
   */
  async handleAction(action, data) {
    if (this.options.enableLogging) {
      this.logger.info(`Handling NovaConnect action: ${action}...`, data);
    }
    
    try {
      // Handle action based on action name
      switch (action) {
        case 'viewConnector':
          return await this.novaConnect.viewConnector(data.connectorId);
        
        case 'runConnector':
          return await this.novaConnect.runConnector(data.connectorId);
        
        case 'editConnector':
          return await this.novaConnect.editConnector(data.connectorId);
        
        case 'deleteConnector':
          return await this.novaConnect.deleteConnector(data.connectorId);
        
        case 'addConnector':
          return await this.novaConnect.addConnector();
        
        case 'saveConnector':
          return await this.novaConnect.saveConnector(data.connector);
        
        case 'cancelConnector':
          return await this.novaConnect.cancelConnector();
        
        case 'testConnector':
          return await this.novaConnect.testConnector(data.connector);
        
        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error) {
      this.logger.error(`Error handling NovaConnect action: ${action}`, error);
      throw error;
    }
  }
}

export default NovaConnectAdapter;
