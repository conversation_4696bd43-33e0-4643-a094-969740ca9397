<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comphyology Patent Diagrams</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      text-align: center;
      margin-bottom: 30px;
    }
    .diagram-list {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;
    }
    .diagram-card {
      border: 1px solid #ccc;
      border-radius: 8px;
      padding: 15px;
      width: 300px;
      text-align: center;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }
    .diagram-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .diagram-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    .view-button {
      display: inline-block;
      background-color: #0070f3;
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      text-decoration: none;
      margin-top: 10px;
    }
    .view-button:hover {
      background-color: #0060df;
    }
  </style>
</head>
<body>
  <h1>Comphyology Patent Diagrams</h1>
  
  <div class="diagram-list">
    <div class="diagram-card">
      <div class="diagram-title">1. High-Level System Architecture</div>
      <p>Overview of the NovaFuse Platform and Comphyology Framework</p>
      <a href="diagrams/high-level-architecture.png" class="view-button" target="_blank">View Diagram</a>
    </div>
    
    <div class="diagram-card">
      <div class="diagram-title">2. Finite Universe Paradigm</div>
      <p>Visualization of the Finite Universe Paradigm concept</p>
      <a href="diagrams/finite-universe.png" class="view-button" target="_blank">View Diagram</a>
    </div>
    
    <div class="diagram-card">
      <div class="diagram-title">3. Three-Body Problem Reframing</div>
      <p>Comparison of classical and Comphyological approaches</p>
      <a href="diagrams/three-body-problem.png" class="view-button" target="_blank">View Diagram</a>
    </div>
    
    <div class="diagram-card">
      <div class="diagram-title">4. UUFT Equation Flow</div>
      <p>Visualization of the UUFT equation (A⊗B⊕C)×π10³</p>
      <a href="diagrams/uuft-equation-flow.png" class="view-button" target="_blank">View Diagram</a>
    </div>
    
    <div class="diagram-card">
      <div class="diagram-title">5. Trinity Equation Visualization</div>
      <p>Visualization of the Trinity Equation concept</p>
      <a href="diagrams/trinity-equation.png" class="view-button" target="_blank">View Diagram</a>
    </div>
    
    <div class="diagram-card">
      <div class="diagram-title">6. Meta-Field Schema</div>
      <p>Representation of the Meta-Field Schema</p>
      <a href="diagrams/meta-field-schema.png" class="view-button" target="_blank">View Diagram</a>
    </div>
    
    <div class="diagram-card">
      <div class="diagram-title">8. Pattern Translation Process</div>
      <p>Diagram of the Universal Pattern Language</p>
      <a href="diagrams/pattern-translation.png" class="view-button" target="_blank">View Diagram</a>
    </div>
    
    <div class="diagram-card">
      <div class="diagram-title">9. 13 NovaFuse Components</div>
      <p>Overview of the 13 Universal NovaFuse Components</p>
      <a href="diagrams/novafuse-components.png" class="view-button" target="_blank">View Diagram</a>
    </div>
    
    <div class="diagram-card">
      <div class="diagram-title">10. 3-6-9-12-13 Alignment Architecture</div>
      <p>Visualization of the Alignment Architecture</p>
      <a href="diagrams/alignment-architecture.png" class="view-button" target="_blank">View Diagram</a>
    </div>
    
    <div class="diagram-card">
      <div class="diagram-title">11. Cross-Module Data Processing Pipeline</div>
      <p>Diagram of the data processing pipeline</p>
      <a href="diagrams/data-processing-pipeline.png" class="view-button" target="_blank">View Diagram</a>
    </div>
    
    <div class="diagram-card">
      <div class="diagram-title">12. Cyber-Safety Incident Response Workflow</div>
      <p>Workflow diagram for incident response</p>
      <a href="diagrams/incident-response.png" class="view-button" target="_blank">View Diagram</a>
    </div>
    
    <div class="diagram-card">
      <div class="diagram-title">13. Healthcare Implementation</div>
      <p>Implementation diagram for healthcare industry</p>
      <a href="diagrams/healthcare-implementation.png" class="view-button" target="_blank">View Diagram</a>
    </div>
    
    <div class="diagram-card">
      <div class="diagram-title">14. Dashboard and Visualization Examples</div>
      <p>Examples of dashboards and visualizations</p>
      <a href="diagrams/dashboard-visualization.png" class="view-button" target="_blank">View Diagram</a>
    </div>
    
    <div class="diagram-card">
      <div class="diagram-title">15. Hardware Architecture</div>
      <p>Diagram of the hardware architecture</p>
      <a href="diagrams/hardware-architecture.png" class="view-button" target="_blank">View Diagram</a>
    </div>
  </div>
</body>
</html>
