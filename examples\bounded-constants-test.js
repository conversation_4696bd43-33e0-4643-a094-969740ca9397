/**
 * Bounded Constants Test
 * 
 * This script tests the bounded constants implementation to ensure
 * that all operations remain within finite boundaries.
 */

const { 
  PI_10_CUBED, 
  GOLDEN_RATIO, 
  MAX_SAFE_BOUNDS, 
  saturate, 
  asymptotic 
} = require('../src/quantum/constants');

const { 
  hardenInput, 
  sanitizeTensor, 
  sanitizeCrossDomainInput 
} = require('../src/quantum/input-sanitizer');

/**
 * Test bounded constants
 */
function testBoundedConstants() {
  console.log('=== Testing Bounded Constants ===\n');
  
  // Test PI_10_CUBED
  console.log(`PI_10_CUBED = ${PI_10_CUBED}`);
  console.log(`Is finite: ${Number.isFinite(PI_10_CUBED)}`);
  console.log(`Is not Infinity: ${PI_10_CUBED !== Infinity}`);
  console.log(`Is not -Infinity: ${PI_10_CUBED !== -Infinity}`);
  console.log(`Is not NaN: ${!Number.isNaN(PI_10_CUBED)}`);
  
  // Test GOLDEN_RATIO
  console.log(`\nGOLDEN_RATIO = ${GOLDEN_RATIO}`);
  console.log(`Is finite: ${Number.isFinite(GOLDEN_RATIO)}`);
  console.log(`Is not Infinity: ${GOLDEN_RATIO !== Infinity}`);
  console.log(`Is not -Infinity: ${GOLDEN_RATIO !== -Infinity}`);
  console.log(`Is not NaN: ${!Number.isNaN(GOLDEN_RATIO)}`);
  
  // Test MAX_SAFE_BOUNDS
  console.log('\n=== Testing MAX_SAFE_BOUNDS ===');
  for (const domain in MAX_SAFE_BOUNDS) {
    console.log(`\nDomain: ${domain}`);
    for (const key in MAX_SAFE_BOUNDS[domain]) {
      const value = MAX_SAFE_BOUNDS[domain][key];
      console.log(`  ${key} = ${value}`);
      console.log(`  Is finite: ${Number.isFinite(value)}`);
    }
  }
}

/**
 * Test saturation functions
 */
function testSaturationFunctions() {
  console.log('\n=== Testing Saturation Functions ===\n');
  
  // Test values
  const testValues = [
    { value: 0, description: 'Zero' },
    { value: 1, description: 'One' },
    { value: -1, description: 'Negative one' },
    { value: Number.MAX_VALUE, description: 'Number.MAX_VALUE' },
    { value: Number.MIN_VALUE, description: 'Number.MIN_VALUE' },
    { value: Infinity, description: 'Infinity' },
    { value: -Infinity, description: 'Negative infinity' },
    { value: NaN, description: 'NaN' }
  ];
  
  // Test domains
  const domains = ['universal', 'cyber', 'financial', 'biological'];
  
  // Test each value with each domain
  for (const domain of domains) {
    console.log(`\nDomain: ${domain}`);
    
    for (const test of testValues) {
      const saturated = saturate.forDomain(domain, test.value);
      
      console.log(`\n  Input: ${test.description} (${test.value})`);
      console.log(`  Saturated: ${saturated}`);
      console.log(`  Is finite: ${Number.isFinite(saturated)}`);
      
      // Check if within bounds
      const bounds = MAX_SAFE_BOUNDS[domain.toUpperCase()];
      const withinBounds = 
        saturated >= -bounds.MAX_VALUE && 
        saturated <= bounds.MAX_VALUE;
      
      console.log(`  Within bounds: ${withinBounds}`);
    }
  }
}

/**
 * Test asymptotic thresholding
 */
function testAsymptoticThresholding() {
  console.log('\n=== Testing Asymptotic Thresholding ===\n');
  
  // Test values
  const testValues = [
    { value: 0, description: 'Zero' },
    { value: 1, description: 'One' },
    { value: -1, description: 'Negative one' },
    { value: Number.MAX_VALUE, description: 'Number.MAX_VALUE' },
    { value: Number.MIN_VALUE, description: 'Number.MIN_VALUE' },
    { value: Infinity, description: 'Infinity' },
    { value: -Infinity, description: 'Negative infinity' },
    { value: NaN, description: 'NaN' }
  ];
  
  // Test domains
  const domains = ['universal', 'cyber', 'financial', 'biological'];
  
  // Test each value with each domain
  for (const domain of domains) {
    console.log(`\nDomain: ${domain}`);
    
    for (const test of testValues) {
      const thresholded = asymptotic.forDomain(domain, test.value);
      
      console.log(`\n  Input: ${test.description} (${test.value})`);
      console.log(`  Thresholded: ${thresholded}`);
      console.log(`  Is finite: ${Number.isFinite(thresholded)}`);
      
      // Check if within bounds
      const bounds = MAX_SAFE_BOUNDS[domain.toUpperCase()];
      const withinBounds = 
        thresholded >= -bounds.MAX_VALUE && 
        thresholded <= bounds.MAX_VALUE;
      
      console.log(`  Within bounds: ${withinBounds}`);
      
      // Check if approaches but never reaches bound
      const approachesBound = 
        Math.abs(thresholded) < bounds.MAX_VALUE;
      
      console.log(`  Approaches but never reaches bound: ${approachesBound}`);
    }
  }
}

/**
 * Test input sanitization
 */
function testInputSanitization() {
  console.log('\n=== Testing Input Sanitization ===\n');
  
  // Test values
  const testValues = [
    { value: 0, description: 'Zero' },
    { value: 1, description: 'One' },
    { value: -1, description: 'Negative one' },
    { value: Number.MAX_VALUE, description: 'Number.MAX_VALUE' },
    { value: Number.MIN_VALUE, description: 'Number.MIN_VALUE' },
    { value: Infinity, description: 'Infinity' },
    { value: -Infinity, description: 'Negative infinity' },
    { value: NaN, description: 'NaN' },
    { value: null, description: 'Null' },
    { value: undefined, description: 'Undefined' },
    { value: 'not a number', description: 'String' },
    { value: {}, description: 'Empty object' },
    { value: [], description: 'Empty array' }
  ];
  
  // Test domains
  const domains = ['universal', 'cyber', 'financial', 'biological'];
  
  // Test each value with each domain
  for (const domain of domains) {
    console.log(`\nDomain: ${domain}`);
    
    for (const test of testValues) {
      const hardened = hardenInput(test.value, domain);
      
      console.log(`\n  Input: ${test.description} (${test.value})`);
      console.log(`  Hardened: ${hardened}`);
      console.log(`  Is finite: ${Number.isFinite(hardened)}`);
      
      // Check if within bounds
      const bounds = MAX_SAFE_BOUNDS[domain.toUpperCase()];
      const withinBounds = 
        hardened >= -bounds.MAX_VALUE && 
        hardened <= bounds.MAX_VALUE;
      
      console.log(`  Within bounds: ${withinBounds}`);
    }
  }
}

/**
 * Test tensor sanitization
 */
function testTensorSanitization() {
  console.log('\n=== Testing Tensor Sanitization ===\n');
  
  // Test tensors
  const testTensors = [
    {
      tensor: {
        dimensions: [2, 2],
        values: [1, 2, 3, 4]
      },
      description: 'Valid tensor'
    },
    {
      tensor: {
        dimensions: [2, 2],
        values: [Infinity, 2, 3, 4]
      },
      description: 'Tensor with Infinity'
    },
    {
      tensor: {
        dimensions: [2, 2],
        values: [NaN, 2, 3, 4]
      },
      description: 'Tensor with NaN'
    },
    {
      tensor: {
        dimensions: [1000000],
        values: Array(1000000).fill(1)
      },
      description: 'Tensor with excessive dimensions'
    },
    {
      tensor: null,
      description: 'Null tensor'
    },
    {
      tensor: {},
      description: 'Empty object tensor'
    },
    {
      tensor: {
        dimensions: [2, 2],
        values: null
      },
      description: 'Tensor with null values'
    }
  ];
  
  // Test domains
  const domains = ['universal', 'cyber', 'financial', 'biological'];
  
  // Test each tensor with each domain
  for (const domain of domains) {
    console.log(`\nDomain: ${domain}`);
    
    for (const test of testTensors) {
      console.log(`\n  Input: ${test.description}`);
      
      try {
        const sanitized = sanitizeTensor(test.tensor, domain);
        
        console.log(`  Sanitized dimensions: [${sanitized.dimensions}]`);
        console.log(`  Sanitized values length: ${sanitized.values.length}`);
        console.log(`  All values finite: ${sanitized.values.every(v => Number.isFinite(v))}`);
        
        // Check if dimensions are within bounds
        const bounds = MAX_SAFE_BOUNDS[domain.toUpperCase()];
        const dimensionsWithinBounds = sanitized.dimensions.every(d => d <= bounds.MAX_DIMENSION);
        
        console.log(`  Dimensions within bounds: ${dimensionsWithinBounds}`);
        
        // Check if tensor size is within bounds
        const tensorSize = sanitized.dimensions.reduce((a, b) => a * b, 1);
        const sizeWithinBounds = tensorSize <= bounds.MAX_TENSOR_SIZE;
        
        console.log(`  Tensor size within bounds: ${sizeWithinBounds}`);
      } catch (error) {
        console.log(`  Error: ${error.message}`);
      }
    }
  }
}

/**
 * Run all tests
 */
function runAllTests() {
  testBoundedConstants();
  testSaturationFunctions();
  testAsymptoticThresholding();
  testInputSanitization();
  testTensorSanitization();
}

// Run tests
runAllTests();
