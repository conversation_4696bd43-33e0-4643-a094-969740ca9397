/**
 * NovaFuse API Superstore - Logger Middleware
 * 
 * This middleware handles logging for the API Gateway.
 */

const winston = require('winston');
const morgan = require('morgan');
const config = require('../config');

// Create logger
const logger = winston.createLogger({
  level: config.logging.level,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'api-gateway' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({ 
      filename: `${config.logging.directory}/error.log`, 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: `${config.logging.directory}/combined.log` 
    })
  ]
});

// Create stream for morgan
const stream = {
  write: (message) => {
    logger.info(message.trim());
  }
};

// Create morgan middleware
const httpLogger = morgan('combined', { stream });

module.exports = {
  logger,
  httpLogger
};
