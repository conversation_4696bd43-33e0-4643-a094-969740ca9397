#!/usr/bin/env python3
"""
AEONIX DIVINE API STARTUP SCRIPT
Launch the complete FastAPI microservice architecture
"""

import subprocess
import sys
import time
import asyncio
import httpx
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("📦 Installing requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def start_api_server():
    """Start the FastAPI server"""
    print("🚀 Starting AEONIX Divine API Server...")
    print("📡 Server will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/divine/docs")
    print("🔮 WebSocket Stream: ws://localhost:8000/ws/divine-stream")
    print()
    print("⚡ Starting server with auto-reload...")
    
    try:
        # Start uvicorn server
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload",
            "--log-level", "info"
        ])
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")

async def test_api_health():
    """Test if API is responding"""
    print("🔍 Testing API health...")
    
    max_retries = 10
    for attempt in range(max_retries):
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:8000/divine/status", timeout=5.0)
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ API is healthy!")
                    print(f"   Kernel: {data['kernel']}")
                    print(f"   Engines: {data['engines']}")
                    print(f"   φ-Coupling: {data['phi_coupling']}")
                    return True
        except Exception as e:
            print(f"⏳ Attempt {attempt + 1}/{max_retries}: API not ready yet...")
            await asyncio.sleep(2)
    
    print("❌ API health check failed")
    return False

def show_api_info():
    """Display API information"""
    print("🌟 AEONIX DIVINE API - READY FOR DEPLOYMENT")
    print("=" * 60)
    print("📡 BASE URL: http://localhost:8000")
    print()
    print("🔧 ENGINE ENDPOINTS:")
    print("   POST /api/harmonics      - NEPI Fibonacci Analysis")
    print("   POST /api/predators      - NEFC Institutional Mapping")
    print("   POST /api/prophecy       - NEPE Event Seeding")
    print("   POST /api/sentiment      - NEEE Emotion Cycles")
    print("   POST /api/vulnerability  - NERS Prey Assessment")
    print()
    print("🎼 ORCHESTRATION:")
    print("   POST /api/divine-simulation - Complete AEONIX Analysis")
    print()
    print("📊 MONITORING:")
    print("   GET  /divine/status      - System Health")
    print("   WS   /ws/divine-stream   - Real-time Updates")
    print()
    print("📚 DOCUMENTATION:")
    print("   GET  /divine/docs        - Interactive API Docs")
    print("   GET  /divine/redoc       - ReDoc Documentation")
    print()
    print("🧪 TESTING:")
    print("   python test_divine_api.py - Run test suite")
    print("=" * 60)

def main():
    """Main startup function"""
    print("🔮 AEONIX DIVINE API STARTUP")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("❌ main.py not found. Please run from the aeonix-divine-api directory")
        return
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements. Exiting.")
        return
    
    # Show API information
    show_api_info()
    
    # Ask user what to do
    print("\n🚀 STARTUP OPTIONS:")
    print("1. Start API Server (with auto-reload)")
    print("2. Test API Health Check")
    print("3. Run Complete Test Suite")
    print("4. Show API Information")
    print("5. Exit")
    
    while True:
        try:
            choice = input("\n🎯 Choose option (1-5): ").strip()
            
            if choice == "1":
                start_api_server()
                break
            elif choice == "2":
                print("🔍 Running health check...")
                result = asyncio.run(test_api_health())
                if not result:
                    print("💡 Make sure the API server is running first (option 1)")
            elif choice == "3":
                print("🧪 Running complete test suite...")
                try:
                    subprocess.run([sys.executable, "test_divine_api.py"])
                except Exception as e:
                    print(f"❌ Test error: {e}")
            elif choice == "4":
                show_api_info()
            elif choice == "5":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter 1-5.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
