/**
 * NovaFuse Universal API Connector Credential Model
 * 
 * This model defines the schema for API credentials in the NovaConnect UAC.
 */

const mongoose = require('mongoose');
const crypto = require('crypto');
const Schema = mongoose.Schema;

// Define encryption helper functions
const encryptionKey = process.env.ENCRYPTION_KEY || 'novafuse-uac-encryption-key-for-development';
const algorithm = 'aes-256-gcm';

/**
 * Encrypt sensitive data
 * @param {string} text - Text to encrypt
 * @returns {Object} - Encrypted data with iv and tag
 */
const encrypt = (text) => {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv(algorithm, Buffer.from(encryptionKey, 'hex'), iv);
  
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return {
    iv: iv.toString('hex'),
    content: encrypted,
    tag: cipher.getAuthTag().toString('hex')
  };
};

/**
 * Decrypt sensitive data
 * @param {Object} encrypted - Encrypted data with iv and tag
 * @returns {string} - Decrypted text
 */
const decrypt = (encrypted) => {
  const decipher = crypto.createDecipheriv(
    algorithm, 
    Buffer.from(encryptionKey, 'hex'), 
    Buffer.from(encrypted.iv, 'hex')
  );
  
  decipher.setAuthTag(Buffer.from(encrypted.tag, 'hex'));
  
  let decrypted = decipher.update(encrypted.content, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
};

// Define credential schema
const credentialSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  connectorId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Connector', 
    required: true 
  },
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  teamId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Team' 
  },
  environmentId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Environment' 
  },
  type: { 
    type: String, 
    required: true, 
    enum: ['API_KEY', 'BASIC', 'OAUTH2', 'JWT', 'AWS_SIG_V4', 'CUSTOM'], 
    default: 'API_KEY' 
  },
  data: { 
    type: Object, 
    required: true 
  },
  encryptedData: {
    iv: { type: String },
    content: { type: String },
    tag: { type: String }
  },
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'revoked'], 
    default: 'active' 
  },
  expiresAt: { 
    type: Date 
  },
  lastUsed: { 
    type: Date 
  },
  usageCount: { 
    type: Number, 
    default: 0 
  },
  metadata: { 
    type: Object 
  }
}, {
  timestamps: true
});

// Add pre-save hook to encrypt sensitive data
credentialSchema.pre('save', function(next) {
  // Only encrypt data if it's modified or new
  if (!this.isModified('data')) return next();
  
  try {
    // Encrypt data
    const encryptedData = encrypt(JSON.stringify(this.data));
    this.encryptedData = encryptedData;
    
    // Clear unencrypted data
    this.data = undefined;
    
    next();
  } catch (error) {
    next(error);
  }
});

// Add virtual for decrypted data
credentialSchema.virtual('decryptedData').get(function() {
  if (!this.encryptedData) return null;
  
  try {
    return JSON.parse(decrypt(this.encryptedData));
  } catch (error) {
    console.error('Error decrypting credential data:', error);
    return null;
  }
});

// Add method to check if credential is expired
credentialSchema.methods.isExpired = function() {
  return this.expiresAt && this.expiresAt < new Date();
};

// Add method to check if credential is active
credentialSchema.methods.isActive = function() {
  return this.status === 'active' && !this.isExpired();
};

// Add method to update usage
credentialSchema.methods.updateUsage = function() {
  this.lastUsed = new Date();
  this.usageCount += 1;
  return this.save();
};

// Add method to revoke credential
credentialSchema.methods.revoke = function() {
  this.status = 'revoked';
  return this.save();
};

// Add indexes
credentialSchema.index({ connectorId: 1 });
credentialSchema.index({ userId: 1 });
credentialSchema.index({ teamId: 1 });
credentialSchema.index({ environmentId: 1 });
credentialSchema.index({ status: 1 });
credentialSchema.index({ expiresAt: 1 });

// Create model
const Credential = mongoose.model('Credential', credentialSchema);

module.exports = Credential;
