import React, { useMemo, useEffect, useRef } from 'react';
import { 
  <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>Axis, CartesianGrid, 
  Tooltip, Legend, ResponsiveContainer, Area, 
  AreaChart, ReferenceLine, Label
} from 'recharts';
import * as d3 from 'd3';
import '../styles/EntropyVisualization.css';

const WAVE_ANIMATION_DURATION = 2000; // ms

const EntropyVisualization = ({ entropyHistory = [], coherence = 1, isQuantumActive }) => {
  const waveRef = useRef(null);
  const animationFrame = useRef(null);
  const lastUpdateTime = useRef(0);
  
  // Process history data for visualization
  const { chartData, stats } = useMemo(() => {
    if (entropyHistory.length === 0) return { chartData: [], stats: {} };
    
    // Prepare data for the chart
    const processedData = entropyHistory.map((entry, index) => ({
      timestamp: entry.timestamp || Date.now(),
      entropy: entry.entropy || 0,
      mode: entry.mode || 'classical',
      coherence: entry.coherence || 1,
      isAnomaly: entry.anomaly?.isAnomaly || false,
      id: entry.id || `entry-${index}`
    }));
    
    // Calculate statistics
    const quantumEntries = processedData.filter(d => d.mode === 'quantum');
    const classicalEntries = processedData.filter(d => d.mode === 'classical');
    
    const stats = {
      total: processedData.length,
      quantum: quantumEntries.length,
      classical: classicalEntries.length,
      avgQuantum: quantumEntries.length > 0 
        ? d3.mean(quantumEntries, d => d.entropy) 
        : 0,
      avgClassical: classicalEntries.length > 0 
        ? d3.mean(classicalEntries, d => d.entropy) 
        : 0,
      anomalies: processedData.filter(d => d.isAnomaly).length,
      lastUpdated: processedData[processedData.length - 1]?.timestamp || null
    };
    
    return { chartData: processedData, stats };
  }, [entropyHistory]);
  
  // Animate quantum wave effect
  useEffect(() => {
    if (!isQuantumActive || !waveRef.current) return;
    
    let startTime = null;
    const wave = waveRef.current;
    
    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const elapsed = timestamp - startTime;
      
      // Update wave animation
      if (elapsed < WAVE_ANIMATION_DURATION) {
        const progress = elapsed / WAVE_ANIMATION_DURATION;
        const amplitude = 0.1 * coherence;
        const frequency = 2 * Math.PI * (coherence * 2);
        
        // Generate wave path
        const points = [];
        const width = wave.clientWidth;
        const height = wave.clientHeight;
        const segments = 50;
        
        for (let i = 0; i <= segments; i++) {
          const x = (i / segments) * width;
          const t = (i / segments) * 2 * Math.PI + progress * 2 * Math.PI;
          const y = height / 2 + Math.sin(t * frequency) * amplitude * height;
          points.push(`${x},${y}`);
        }
        
        // Update wave path
        wave.setAttribute('d', `M${points.join(' L')} V${height} H0 Z`);
        animationFrame.current = requestAnimationFrame(animate);
      } else {
        // Reset animation
        startTime = null;
        animationFrame.current = requestAnimationFrame(animate);
      }
    };
    
    animationFrame.current = requestAnimationFrame(animate);
    
    return () => {
      if (animationFrame.current) {
        cancelAnimationFrame(animationFrame.current);
      }
    };
  }, [isQuantumActive, coherence]);
  
  // Format timestamp for display
  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    return new Date(timestamp).toLocaleTimeString();
  };
  
  // Generate gradient ID for the area chart
  const gradientId = 'entropy-gradient';
  
  return (
    <div className="entropy-visualization">
      <div className="visualization-header">
        <h3>Quantum Entropy Analysis</h3>
        <div className="coherence-indicator">
          <span className="label">Coherence:</span>
          <span className="value">{coherence.toFixed(4)}</span>
          <div 
            className="coherence-bar" 
            style={{ '--coherence-level': coherence * 100 + '%' }}
          ></div>
        </div>
      </div>
      
      <div className="visualization-container">
        {chartData.length === 0 ? (
          <div className="no-data">
            <p>No entropy data available</p>
            <p>Submit a task to see quantum measurements</p>
          </div>
        ) : (
          <>
            <div className="chart-container">
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={chartData}>
                  <defs>
                    <linearGradient id={gradientId} x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#8a2be2" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#8a2be2" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  
                  <CartesianGrid strokeDasharray="3 3" stroke="#2d3748" />
                  
                  <XAxis 
                    dataKey="timestamp"
                    tickFormatter={formatTime}
                    tick={{ fill: '#a0aec0', fontSize: 12 }}
                    axisLine={{ stroke: '#4a5568' }}
                    tickLine={{ stroke: '#4a5568' }}
                  />
                  
                  <YAxis 
                    domain={[0, 1]}
                    tick={{ fill: '#a0aec0', fontSize: 12 }}
                    axisLine={{ stroke: '#4a5568' }}
                    tickLine={{ stroke: '#4a5568' }}
                  >
                    <Label 
                      angle={-90} 
                      value="Entropy Level" 
                      position="insideLeft" 
                      style={{ textAnchor: 'middle', fill: '#a0aec0' }}
                    />
                  </YAxis>
                  
                  <Tooltip 
                    content={({ active, payload, label }) => {
                      if (!active || !payload || payload.length === 0) return null;
                      
                      const data = payload[0].payload;
                      return (
                        <div className="entropy-tooltip">
                          <p className="time">{formatTime(label)}</p>
                          <p className="value">
                            Entropy: <strong>{data.entropy.toFixed(4)}</strong>
                          </p>
                          <p className="mode">
                            Mode: <span className={`badge ${data.mode}`}>
                              {data.mode.toUpperCase()}
                            </span>
                          </p>
                          {data.isAnomaly && (
                            <p className="anomaly-warning">⚠️ Anomaly Detected</p>
                          )}
                        </div>
                      );
                    }}
                  />
                  
                  <Area
                    type="monotone"
                    dataKey="entropy"
                    stroke="#8a2be2"
                    fillOpacity={1}
                    fill={`url(#${gradientId})`}
                    activeDot={{ r: 6, stroke: '#fff', strokeWidth: 2 }}
                  />
                  
                  <ReferenceLine 
                    y={0.5} 
                    stroke="#e2e8f0" 
                    strokeDasharray="3 3" 
                    strokeOpacity={0.5} 
                  />
                  
                  <Legend 
                    verticalAlign="top"
                    height={36}
                    formatter={(value) => (
                      <span style={{ color: '#e2e8f0' }}>
                        {value === 'entropy' ? 'Quantum Entropy' : value}
                      </span>
                    )}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
            
            <div className="quantum-wave-container">
              <svg className="quantum-wave" viewBox="0 0 100 20" preserveAspectRatio="none">
                <path 
                  ref={waveRef}
                  fill="url(#wave-gradient)" 
                  opacity="0.3"
                />
                <defs>
                  <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#8a2be2" />
                    <stop offset="100%" stopColor="#4a00e0" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
            
            <div className="stats-summary">
              <div className="stat">
                <span className="stat-label">Total Measurements:</span>
                <span className="stat-value">{stats.total}</span>
              </div>
              <div className="stat">
                <span className="stat-label">Quantum Avg:</span>
                <span className="stat-value">{stats.avgQuantum.toFixed(4)}</span>
              </div>
              <div className="stat">
                <span className="stat-label">Anomalies:</span>
                <span className="stat-value">{stats.anomalies}</span>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

EntropyVisualization.propTypes = {
  entropyHistory: PropTypes.arrayOf(PropTypes.shape({
    entropy: PropTypes.number.isRequired,
    timestamp: PropTypes.number.isRequired,
    mode: PropTypes.oneOf(['quantum', 'classical']).isRequired,
    coherence: PropTypes.number,
    anomaly: PropTypes.shape({
      isAnomaly: PropTypes.bool,
      deviation: PropTypes.number
    })
  })),
  coherence: PropTypes.number,
  isQuantumActive: PropTypes.bool
};

EntropyVisualization.defaultProps = {
  entropyHistory: [],
  coherence: 1,
  isQuantumActive: true
};

export default React.memo(EntropyVisualization);
                />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="value" 
                  stroke="#8884d8" 
                  activeDot={{ r: 8 }} 
                  strokeWidth={3}
                  name="Entropy Value"
                />
              </LineChart>
            </ResponsiveContainer>
            
            <div className="entropy-values">
              {data.map((item, index) => (
                <div key={index} className={`entropy-value ${item.type}`}>
                  <span className="label">{item.name}:</span>
                  <span className={`value ${getRiskLevel(item.value)}`}>
                    {item.value.toFixed(4)}
                  </span>
                  <span className="risk-level">
                    {getRiskLevel(item.value).toUpperCase()} RISK
                  </span>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="no-data">
            <p>Submit a task to see entropy analysis</p>
          </div>
        )}
      </div>
      
      <div className="legend">
        <div className="legend-item">
          <span className="dot quantum"></span>
          <span>Quantum Entropy</span>
        </div>
        <div className="legend-item">
          <span className="dot classical"></span>
          <span>Classical Entropy</span>
        </div>
      </div>
    </div>
  );
};

export default EntropyVisualization;
