/**
 * NEPI Test Runner
 *
 * This script runs all NEPI tests for the Comphyon framework.
 */

const { NEPITestRunner } = require('./nepi-test-framework');
const { createFoundationalPhysicsTestSuite } = require('./foundational-physics-tests');
const { createMeterTestSuite } = require('./meter-tests');
const { createAdversarialTestSuite } = require('./adversarial-tests');
const { createQuantumResilienceTestSuite } = require('./quantum-resilience-tests');
const { createPiConstantProtectionTestSuite } = require('./pi-constant-protection-tests');
const { createTensorIntegrityTestSuite } = require('./tensor-integrity-tests');
const fs = require('fs');
const path = require('path');

/**
 * Run all NEPI tests
 */
async function runNEPITests() {
  console.log('=== NEPI Testing Framework ===\n');
  console.log('A Rigorous Regimen for Emergent Intelligence Testing\n');

  // Create test runner
  console.log('Creating NEPI Test Runner...');
  const testRunner = new NEPITestRunner({
    enableLogging: true,
    parallelSuites: false
  });

  // Add test suites
  console.log('Adding Foundational Physics Test Suite...');
  testRunner.addSuite(createFoundationalPhysicsTestSuite());

  console.log('Adding Meter Test Suite...');
  testRunner.addSuite(createMeterTestSuite());

  console.log('Adding Adversarial Test Suite...');
  testRunner.addSuite(createAdversarialTestSuite());

  // Add Quantum Resilience Test Suites
  console.log('Adding Quantum Resilience Test Suite...');
  testRunner.addSuite(createQuantumResilienceTestSuite());

  console.log('Adding π10³ Constant Protection Test Suite...');
  testRunner.addSuite(createPiConstantProtectionTestSuite());

  console.log('Adding Tensor Integrity Test Suite...');
  testRunner.addSuite(createTensorIntegrityTestSuite());

  // Run tests
  try {
    console.log('\nRunning NEPI tests...');
    const result = await testRunner.run();

    console.log('\nGenerating NEPI test report...');
    // Generate report
    generateNEPIReport(result);

    // Exit with appropriate code
    process.exit(result.failed > 0 ? 1 : 0);
  } catch (error) {
    console.error('Error running NEPI tests:', error);
    process.exit(1);
  }
}

/**
 * Generate NEPI test report
 * @param {Object} result - Test result
 */
function generateNEPIReport(result) {
  // Prepare report data
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      total: result.total,
      passed: result.passed,
      failed: result.failed,
      skipped: result.skipped,
      duration: result.duration
    },
    suites: result.suites.map(suite => ({
      name: suite.name,
      total: suite.result.total,
      passed: suite.result.passed,
      failed: suite.result.failed,
      skipped: suite.result.skipped,
      duration: suite.result.duration,
      testingLayer: suite.testingLayer,
      domains: suite.domains,
      coherenceImpact: suite.result.coherenceImpact,
      entropyMetrics: suite.result.entropyMetrics,
      ethicalCompliance: suite.result.ethicalCompliance,
      testCases: suite.testCases.map(testCase => ({
        name: testCase.name,
        status: testCase.result.status,
        duration: testCase.result.duration,
        error: testCase.result.error,
        testingType: testCase.options.testingType,
        coherenceImpact: testCase.options.coherenceImpact,
        domains: testCase.options.domains
      }))
    }))
  };

  // Generate HTML report
  const htmlReport = generateNEPIHtmlReport(reportData);

  // Create reports directory if it doesn't exist
  const reportsDir = path.join(__dirname, '..', '..', 'reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  // Write HTML report
  const reportPath = path.join(reportsDir, `nepi-test-report-${new Date().toISOString().replace(/:/g, '-')}.html`);
  fs.writeFileSync(reportPath, htmlReport);

  console.log(`\nNEPI test report generated: ${reportPath}`);
}

/**
 * Generate NEPI HTML report
 * @param {Object} data - Report data
 * @returns {string} - HTML report
 */
function generateNEPIHtmlReport(data) {
  const passRate = data.summary.total > 0 ? Math.round((data.summary.passed / data.summary.total) * 100) : 0;
  
  // Count tests by testing layer
  const testingLayers = {};
  data.suites.forEach(suite => {
    const layer = suite.testingLayer || 'Unknown';
    testingLayers[layer] = (testingLayers[layer] || 0) + suite.total;
  });
  
  // Count tests by domain
  const domains = {};
  data.suites.forEach(suite => {
    (suite.domains || ['Unknown']).forEach(domain => {
      domains[domain] = (domains[domain] || 0) + suite.total;
    });
  });
  
  // Count tests by coherence impact
  const coherenceImpact = {
    positive: 0,
    negative: 0,
    neutral: 0
  };
  data.suites.forEach(suite => {
    coherenceImpact.positive += suite.coherenceImpact.positive || 0;
    coherenceImpact.negative += suite.coherenceImpact.negative || 0;
    coherenceImpact.neutral += suite.coherenceImpact.neutral || 0;
  });
  
  // Generate HTML
  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NEPI Test Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;
    }
    .summary-box {
      background-color: #f5f5f5;
      border-radius: 5px;
      padding: 15px;
      width: 30%;
    }
    .pass-rate {
      font-size: 24px;
      font-weight: bold;
      color: ${passRate >= 90 ? '#4CAF50' : passRate >= 75 ? '#FF9800' : '#F44336'};
    }
    .suite {
      margin-bottom: 30px;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
    }
    .suite-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    .suite-status {
      font-weight: bold;
      color: ${passRate >= 90 ? '#4CAF50' : passRate >= 75 ? '#FF9800' : '#F44336'};
    }
    .test-case {
      margin: 10px 0;
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 3px;
    }
    .test-case.passed {
      border-left: 5px solid #4CAF50;
    }
    .test-case.failed {
      border-left: 5px solid #F44336;
    }
    .test-case.skipped {
      border-left: 5px solid #FF9800;
    }
    .error {
      color: #F44336;
      font-family: monospace;
      white-space: pre-wrap;
      margin-top: 10px;
      padding: 10px;
      background-color: #ffebee;
      border-radius: 3px;
    }
    .charts {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-bottom: 30px;
    }
    .chart {
      width: 48%;
      margin-bottom: 20px;
      background-color: #f5f5f5;
      border-radius: 5px;
      padding: 15px;
    }
  </style>
</head>
<body>
  <h1>NEPI Test Report</h1>
  <p>Generated: ${new Date(data.timestamp).toLocaleString()}</p>
  
  <div class="summary">
    <div class="summary-box">
      <h2>Test Summary</h2>
      <p>Total: ${data.summary.total}</p>
      <p>Passed: ${data.summary.passed}</p>
      <p>Failed: ${data.summary.failed}</p>
      <p>Skipped: ${data.summary.skipped}</p>
      <p>Duration: ${(data.summary.duration / 1000).toFixed(2)}s</p>
      <p>Pass Rate: <span class="pass-rate">${passRate}%</span></p>
    </div>
    
    <div class="summary-box">
      <h2>Testing Layers</h2>
      ${Object.entries(testingLayers).map(([layer, count]) => `
        <p>${layer}: ${count} tests</p>
      `).join('')}
    </div>
    
    <div class="summary-box">
      <h2>Domains</h2>
      ${Object.entries(domains).map(([domain, count]) => `
        <p>${domain}: ${count} tests</p>
      `).join('')}
    </div>
  </div>
  
  <div class="charts">
    <div class="chart">
      <h2>Coherence Impact</h2>
      <p>Positive: ${coherenceImpact.positive}</p>
      <p>Neutral: ${coherenceImpact.neutral}</p>
      <p>Negative: ${coherenceImpact.negative}</p>
    </div>
    
    <div class="chart">
      <h2>Entropy Metrics</h2>
      <p>Coming soon...</p>
    </div>
  </div>
  
  <h2>Test Suites</h2>
  ${data.suites.map(suite => `
    <div class="suite">
      <div class="suite-header">
        <h3>${suite.name}</h3>
        <span class="suite-status">
          ${suite.passed}/${suite.total} passed (${Math.round((suite.passed / suite.total) * 100)}%)
        </span>
      </div>
      
      <p>Testing Layer: ${suite.testingLayer}</p>
      <p>Domains: ${suite.domains.join(', ')}</p>
      <p>Duration: ${(suite.duration / 1000).toFixed(2)}s</p>
      
      <h4>Test Cases</h4>
      ${suite.testCases.map(testCase => `
        <div class="test-case ${testCase.status}">
          <strong>${testCase.name}</strong>
          <p>Status: ${testCase.status}</p>
          <p>Duration: ${(testCase.duration / 1000).toFixed(2)}s</p>
          <p>Testing Type: ${testCase.testingType || 'Unknown'}</p>
          <p>Coherence Impact: ${testCase.coherenceImpact || 'Unknown'}</p>
          <p>Domains: ${(testCase.domains || ['Unknown']).join(', ')}</p>
          ${testCase.error ? `<div class="error">${testCase.error}</div>` : ''}
        </div>
      `).join('')}
    </div>
  `).join('')}
</body>
</html>
  `;
}

// Run tests
runNEPITests();
