const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const port = process.env.PORT || 3005;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Mock AWS Security Hub API
app.get('/aws/securityhub/findings', (req, res) => {
  res.json({
    Findings: [
      {
        Id: 'finding-1',
        Title: 'Unencrypted S3 Bucket',
        Description: 'S3 bucket is not encrypted',
        Severity: { Label: 'HIGH' },
        Resources: [{ Type: 'AwsS3Bucket', Id: 'bucket-123' }],
        Compliance: { Status: 'FAILED' },
        CreatedAt: new Date().toISOString()
      },
      {
        Id: 'finding-2',
        Title: 'IAM User with Admin Privileges',
        Description: 'IAM user has administrator privileges',
        Severity: { Label: 'MEDIUM' },
        Resources: [{ Type: 'AwsIamUser', Id: 'user-456' }],
        Compliance: { Status: 'WARNING' },
        CreatedAt: new Date().toISOString()
      },
      {
        Id: 'finding-3',
        Title: 'Security Group with Open Ports',
        Description: 'Security group allows unrestricted access',
        Severity: { Label: 'CRITICAL' },
        Resources: [{ Type: 'AwsEc2SecurityGroup', Id: 'sg-789' }],
        Compliance: { Status: 'FAILED' },
        CreatedAt: new Date().toISOString()
      }
    ]
  });
});

// Mock Okta API
app.get('/okta/users', (req, res) => {
  res.json([
    {
      id: 'user-1',
      profile: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        login: '<EMAIL>'
      },
      status: 'ACTIVE',
      created: new Date().toISOString(),
      lastLogin: new Date().toISOString()
    },
    {
      id: 'user-2',
      profile: {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        login: '<EMAIL>'
      },
      status: 'ACTIVE',
      created: new Date().toISOString(),
      lastLogin: new Date().toISOString()
    },
    {
      id: 'user-3',
      profile: {
        firstName: 'Bob',
        lastName: 'Johnson',
        email: '<EMAIL>',
        login: '<EMAIL>'
      },
      status: 'INACTIVE',
      created: new Date().toISOString(),
      lastLogin: null
    }
  ]);
});

app.get('/okta/groups', (req, res) => {
  res.json([
    {
      id: 'group-1',
      profile: {
        name: 'Administrators',
        description: 'Administrator group'
      },
      created: new Date().toISOString()
    },
    {
      id: 'group-2',
      profile: {
        name: 'Users',
        description: 'Regular users'
      },
      created: new Date().toISOString()
    }
  ]);
});

// Mock Jira API
app.get('/jira/issues', (req, res) => {
  res.json({
    issues: [
      {
        id: 'ISSUE-1',
        key: 'COMP-123',
        fields: {
          summary: 'Fix security vulnerability',
          description: 'Address the security vulnerability in the authentication module',
          status: { name: 'In Progress' },
          priority: { name: 'High' },
          assignee: { displayName: 'John Doe' },
          created: new Date().toISOString(),
          updated: new Date().toISOString()
        }
      },
      {
        id: 'ISSUE-2',
        key: 'COMP-124',
        fields: {
          summary: 'Update compliance documentation',
          description: 'Update the compliance documentation for SOC 2',
          status: { name: 'To Do' },
          priority: { name: 'Medium' },
          assignee: { displayName: 'Jane Smith' },
          created: new Date().toISOString(),
          updated: new Date().toISOString()
        }
      },
      {
        id: 'ISSUE-3',
        key: 'COMP-125',
        fields: {
          summary: 'Implement MFA',
          description: 'Implement multi-factor authentication for all users',
          status: { name: 'Done' },
          priority: { name: 'High' },
          assignee: { displayName: 'Bob Johnson' },
          created: new Date().toISOString(),
          updated: new Date().toISOString()
        }
      }
    ]
  });
});

// Mock ServiceNow API
app.get('/servicenow/incidents', (req, res) => {
  res.json({
    result: [
      {
        sys_id: 'incident-1',
        number: 'INC0001',
        short_description: 'System outage',
        description: 'Complete system outage affecting all users',
        priority: '1',
        state: 'In Progress',
        assigned_to: { display_value: 'John Doe' },
        opened_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        sys_id: 'incident-2',
        number: 'INC0002',
        short_description: 'Email service down',
        description: 'Email service is not working for some users',
        priority: '2',
        state: 'New',
        assigned_to: { display_value: 'Jane Smith' },
        opened_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]
  });
});

// Start the server
app.listen(port, () => {
  console.log(`Mock API server running on port ${port}`);
});
