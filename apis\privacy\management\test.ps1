# Set environment variables for testing
$env:NODE_ENV = "test"

# Create directory for test reports
New-Item -ItemType Directory -Force -Path test-reports | Out-Null

# Run tests with coverage
Write-Host "Running tests with coverage..."
npx jest tests/privacy/management --coverage --coverageDirectory=./test-reports/coverage

# Generate test report
Write-Host "Generating test report..."
npx jest tests/privacy/management --json --outputFile=./test-reports/test-results.json

# Print test summary
Write-Host "Test Summary:"
Write-Host "============="
npx jest tests/privacy/management --coverage --coverageReporters="text-summary"

# Check if all tests passed
if ($LASTEXITCODE -eq 0) {
  Write-Host "All tests passed!"
  exit 0
} else {
  Write-Host "Some tests failed!"
  exit 1
}
