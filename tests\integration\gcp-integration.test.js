/**
 * NovaConnect Integration Tests - Google Cloud Integration
 * 
 * These tests validate the integration between NovaConnect and Google Cloud services
 * using the GCP service simulators.
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// Helper function to measure execution time
const measureExecutionTime = async (fn) => {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  const duration = end - start;
  
  // Record response time in global metrics
  if (global.recordResponseTime) {
    global.recordResponseTime(duration);
  }
  
  return { result, duration };
};

// Test configuration
const config = {
  novaConnectUrl: process.env.NOVA_CONNECT_URL || 'http://localhost:3001',
  gcpSimulatorsUrl: process.env.GCP_SIMULATORS_URL || 'http://localhost',
  gcpServicesUrl: process.env.GCP_SERVICES_URL || 'http://localhost',
  sccPort: process.env.SCC_PORT || '8081',
  iamPort: process.env.IAM_PORT || '8082',
  bigqueryPort: process.env.BIGQUERY_PORT || '8083',
  storagePort: process.env.CLOUD_STORAGE_PORT || '8084',
  functionsPort: process.env.CLOUD_FUNCTIONS_PORT || '8085',
  monitoringPort: process.env.CLOUD_MONITORING_PORT || '8086'
};

describe('Google Cloud Integration Tests', () => {
  // Set a longer timeout for integration tests
  jest.setTimeout(30000);
  
  // Test data
  let testCredential;
  
  beforeAll(async () => {
    // Create test credential for GCP
    try {
      const response = await axios.post(`${config.novaConnectUrl}/api/credentials`, {
        id: 'gcp-test-credential',
        connectorId: 'gcp-scc',
        name: 'GCP Test Credential',
        type: 'oauth2',
        data: {
          access_token: 'gcp-test-token',
          refresh_token: 'gcp-test-refresh-token',
          expires_at: Date.now() + 3600000,
          project_id: 'test-project'
        },
        userId: 'integration-test-user'
      });
      
      testCredential = response.data;
    } catch (error) {
      // Ignore if credential already exists
      if (error.response && error.response.status !== 409) {
        console.error('Error creating test credential:', error.message);
      } else {
        // Get existing credential
        const response = await axios.get(`${config.novaConnectUrl}/api/credentials/gcp-test-credential`);
        testCredential = response.data;
      }
    }
  });
  
  // Test Security Command Center integration
  describe('Security Command Center Integration', () => {
    it('should retrieve findings from SCC', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.novaConnectUrl}/api/testing/execute/gcp-scc/getFindings`, {
          credentialId: testCredential.id,
          parameters: {
            filter: 'severity="HIGH"',
            pageSize: 10
          },
          userId: 'integration-test-user'
        });
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('success', true);
      expect(result.data).toHaveProperty('data');
      expect(Array.isArray(result.data.data)).toBe(true);
      expect(result.data.data.length).toBeGreaterThan(0);
      expect(duration).toBeLessThan(1000); // Should complete in less than 1 second
      
      console.log(`SCC findings retrieval completed in ${duration.toFixed(2)} ms`);
      console.log(`Retrieved ${result.data.data.length} findings`);
    });
    
    it('should normalize SCC findings to NovaConnect format', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.novaConnectUrl}/api/testing/execute/gcp-scc/getFindings`, {
          credentialId: testCredential.id,
          parameters: {
            filter: 'severity="HIGH"',
            pageSize: 10,
            normalize: true
          },
          userId: 'integration-test-user'
        });
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('success', true);
      expect(result.data).toHaveProperty('data');
      expect(Array.isArray(result.data.data)).toBe(true);
      
      // Check normalization
      const firstFinding = result.data.data[0];
      expect(firstFinding).toHaveProperty('id');
      expect(firstFinding).toHaveProperty('title');
      expect(firstFinding).toHaveProperty('severity');
      expect(firstFinding).toHaveProperty('category');
      expect(firstFinding).toHaveProperty('resourceName');
      expect(firstFinding).toHaveProperty('createdAt');
      
      // Verify normalization was done correctly
      expect(['high', 'medium', 'low']).toContain(firstFinding.severity);
      expect(typeof firstFinding.createdAt).toBe('number');
      
      console.log(`SCC findings normalization completed in ${duration.toFixed(2)} ms`);
    });
  });
  
  // Test Cloud IAM integration
  describe('Cloud IAM Integration', () => {
    it('should retrieve IAM roles', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.novaConnectUrl}/api/testing/execute/gcp-iam/getRoles`, {
          credentialId: testCredential.id,
          parameters: {
            projectId: 'test-project'
          },
          userId: 'integration-test-user'
        });
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('success', true);
      expect(result.data).toHaveProperty('data');
      expect(Array.isArray(result.data.data)).toBe(true);
      expect(result.data.data.length).toBeGreaterThan(0);
      expect(duration).toBeLessThan(1000); // Should complete in less than 1 second
      
      console.log(`IAM roles retrieval completed in ${duration.toFixed(2)} ms`);
      console.log(`Retrieved ${result.data.data.length} roles`);
    });
    
    it('should retrieve IAM policy', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.novaConnectUrl}/api/testing/execute/gcp-iam/getPolicy`, {
          credentialId: testCredential.id,
          parameters: {
            projectId: 'test-project'
          },
          userId: 'integration-test-user'
        });
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('success', true);
      expect(result.data).toHaveProperty('data');
      expect(result.data.data).toHaveProperty('bindings');
      expect(Array.isArray(result.data.data.bindings)).toBe(true);
      expect(duration).toBeLessThan(1000); // Should complete in less than 1 second
      
      console.log(`IAM policy retrieval completed in ${duration.toFixed(2)} ms`);
    });
  });
  
  // Test BigQuery integration
  describe('BigQuery Integration', () => {
    it('should execute a BigQuery query', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.novaConnectUrl}/api/testing/execute/gcp-bigquery/executeQuery`, {
          credentialId: testCredential.id,
          parameters: {
            projectId: 'test-project',
            query: 'SELECT * FROM `test-dataset.test-table` LIMIT 10'
          },
          userId: 'integration-test-user'
        });
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('success', true);
      expect(result.data).toHaveProperty('data');
      expect(result.data.data).toHaveProperty('jobComplete', true);
      expect(duration).toBeLessThan(1500); // Should complete in less than 1.5 seconds
      
      console.log(`BigQuery query execution completed in ${duration.toFixed(2)} ms`);
    });
  });
  
  // Test Cloud Storage integration
  describe('Cloud Storage Integration', () => {
    it('should store evidence in Cloud Storage', async () => {
      const evidenceData = {
        id: 'test-evidence-1',
        type: 'compliance',
        content: 'This is test evidence content',
        metadata: {
          source: 'integration-test',
          timestamp: new Date().toISOString(),
          framework: 'SOC2',
          control: 'CC7.1'
        }
      };
      
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.novaConnectUrl}/api/testing/execute/gcp-storage/storeEvidence`, {
          credentialId: testCredential.id,
          parameters: {
            bucketName: 'evidence-binder',
            evidenceData
          },
          userId: 'integration-test-user'
        });
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('success', true);
      expect(result.data).toHaveProperty('data');
      expect(result.data.data).toHaveProperty('bucket', 'evidence-binder');
      expect(duration).toBeLessThan(1000); // Should complete in less than 1 second
      
      console.log(`Cloud Storage evidence storage completed in ${duration.toFixed(2)} ms`);
    });
  });
  
  // Test multi-service orchestration
  describe('Multi-Service Orchestration', () => {
    it('should orchestrate a compliance workflow across multiple GCP services', async () => {
      const workflowParams = {
        projectId: 'test-project',
        framework: 'SOC2',
        control: 'CC7.1',
        evidenceType: 'automated'
      };
      
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.novaConnectUrl}/api/workflows/execute/gcp-compliance-assessment`, {
          credentialId: testCredential.id,
          parameters: workflowParams,
          userId: 'integration-test-user'
        });
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('success', true);
      expect(result.data).toHaveProperty('workflowId');
      expect(result.data).toHaveProperty('status', 'completed');
      expect(result.data).toHaveProperty('results');
      expect(duration).toBeLessThan(8000); // Should complete in less than 8 seconds
      
      console.log(`Multi-service orchestration completed in ${duration.toFixed(2)} ms`);
      
      // Verify workflow steps were executed
      expect(result.data.results).toHaveProperty('findingsCollection');
      expect(result.data.results).toHaveProperty('policyAssessment');
      expect(result.data.results).toHaveProperty('evidenceStorage');
      expect(result.data.results).toHaveProperty('complianceScore');
    });
  });
});
