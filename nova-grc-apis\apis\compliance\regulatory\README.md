# Regulatory Compliance API

This API provides endpoints for managing regulatory frameworks, requirements, and jurisdictions.

## Features

- **Regulatory Frameworks**: Manage regulatory frameworks like GDPR, HIPAA, CCPA, etc.
- **Regulatory Requirements**: Manage specific requirements within regulatory frameworks
- **Jurisdictions**: Manage jurisdictions where regulations apply
- **Regulatory Changes**: Track changes to regulations
- **Regulatory Reports**: Manage compliance reports

## API Endpoints

### Frameworks

- `GET /compliance/regulatory/frameworks` - Get a list of regulatory frameworks
- `GET /compliance/regulatory/frameworks/:id` - Get a specific regulatory framework
- `POST /compliance/regulatory/frameworks` - Create a new regulatory framework
- `PUT /compliance/regulatory/frameworks/:id` - Update a regulatory framework
- `DELETE /compliance/regulatory/frameworks/:id` - Delete a regulatory framework

### Requirements

- `GET /compliance/regulatory/requirements` - Get a list of regulatory requirements
- `GET /compliance/regulatory/requirements/:id` - Get a specific regulatory requirement
- `POST /compliance/regulatory/requirements` - Create a new regulatory requirement
- `PUT /compliance/regulatory/requirements/:id` - Update a regulatory requirement
- `DELETE /compliance/regulatory/requirements/:id` - Delete a regulatory requirement

### Jurisdictions

- `GET /compliance/regulatory/jurisdictions` - Get a list of jurisdictions
- `GET /compliance/regulatory/jurisdictions/:id` - Get a specific jurisdiction
- `POST /compliance/regulatory/jurisdictions` - Create a new jurisdiction

### Changes

- `GET /compliance/regulatory/changes` - Get a list of regulatory changes
- `GET /compliance/regulatory/changes/:id` - Get a specific regulatory change
- `POST /compliance/regulatory/changes` - Create a new regulatory change
- `PUT /compliance/regulatory/changes/:id` - Update a regulatory change
- `DELETE /compliance/regulatory/changes/:id` - Delete a regulatory change

### Reports

- `GET /compliance/regulatory/reports` - Get a list of regulatory reports
- `GET /compliance/regulatory/reports/:id` - Get a specific regulatory report
- `POST /compliance/regulatory/reports` - Create a new regulatory report
- `PUT /compliance/regulatory/reports/:id` - Update a regulatory report
- `DELETE /compliance/regulatory/reports/:id` - Delete a regulatory report

## Implementation Status

**Status**: Complete (100%)

All endpoints have been implemented and tested. The API provides comprehensive functionality for managing regulatory frameworks, requirements, jurisdictions, changes, and reports.

## Integration with Other APIs

The Regulatory Compliance API integrates with the following APIs:

1. **Compliance Automation API**
   - Links regulatory requirements to compliance controls
   - Automates evidence collection for regulatory compliance
   - Streamlines compliance assessments

2. **Control Testing API**
   - Maps regulatory requirements to controls
   - Tracks control effectiveness for regulatory compliance
   - Provides evidence for regulatory audits

## Testing

Run the tests using:

```
npm test -- tests/compliance/regulatory
```
