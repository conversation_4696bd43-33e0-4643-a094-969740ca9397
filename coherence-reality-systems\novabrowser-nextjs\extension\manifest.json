{"manifest_version": 3, "name": "Comphyological Browsing Engine (CBE)", "version": "1.0.0", "description": "Revolutionary consciousness-driven browser with Ψ-Snap threshold enforcement, KetherNet verification, and divine content enhancement", "permissions": ["activeTab", "storage", "background", "scripting", "webRequest", "webRequestBlocking", "tabs", "declarativeContent"], "host_permissions": ["http://localhost:8080/*", "http://localhost:3000/*", "<all_urls>"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content/consciousness-analyzer.js", "content/quantum-enhancer.js", "content/3ms-monitor.js"], "css": ["content/quantum-consciousness.css"], "run_at": "document_start"}], "action": {"default_popup": "popup/popup.html", "default_title": "CBE - Consciousness Browser", "default_icon": {"16": "icons/cbe-16.png", "32": "icons/cbe-32.png", "48": "icons/cbe-48.png", "128": "icons/cbe-128.png"}}, "icons": {"16": "icons/cbe-16.png", "32": "icons/cbe-32.png", "48": "icons/cbe-48.png", "128": "icons/cbe-128.png"}, "web_accessible_resources": [{"resources": ["wasm/consciousness-analyzer.wasm", "css/quantum-consciousness.css", "js/kethernet-bridge.js"], "matches": ["<all_urls>"]}], "declarative_net_request": {"rule_resources": [{"id": "consciousness_filter", "enabled": true, "path": "rules/consciousness-filter.json"}]}, "options_page": "options/options.html", "commands": {"toggle_cbe": {"suggested_key": {"default": "Ctrl+Shift+C", "mac": "Command+Shift+C"}, "description": "Toggle CBE consciousness mode"}, "psi_snap_analysis": {"suggested_key": {"default": "Ctrl+Shift+P", "mac": "Command+Shift+P"}, "description": "Run Ψ-Snap consciousness analysis"}, "open_3ms_dashboard": {"suggested_key": {"default": "Ctrl+Shift+M", "mac": "Command+Shift+M"}, "description": "Open 3Ms consciousness dashboard"}}, "externally_connectable": {"matches": ["http://localhost:8080/*", "http://localhost:3000/*"]}}