/**
 * GraphQL Service
 * 
 * This service handles operations related to GraphQL APIs.
 */

const axios = require('axios');
const { ValidationError } = require('../utils/errors');

class GraphQLService {
  /**
   * Execute a GraphQL query
   * 
   * @param {string} endpoint - GraphQL endpoint URL
   * @param {string} query - GraphQL query or mutation
   * @param {Object} variables - Query variables
   * @param {Object} headers - HTTP headers
   * @param {Object} auth - Authentication details
   * @returns {Promise<Object>} - Query result
   */
  async executeQuery(endpoint, query, variables = {}, headers = {}, auth = null) {
    try {
      if (!endpoint) {
        throw new ValidationError('GraphQL endpoint URL is required');
      }
      
      if (!query) {
        throw new ValidationError('GraphQL query is required');
      }
      
      // Prepare request headers
      const requestHeaders = { ...headers };
      
      // Add authentication if provided
      if (auth) {
        switch (auth.type) {
          case 'bearer':
            requestHeaders['Authorization'] = `Bearer ${auth.token}`;
            break;
          case 'api_key':
            requestHeaders[auth.headerName || 'X-API-Key'] = auth.apiKey;
            break;
          // Add other auth types as needed
        }
      }
      
      // Execute the GraphQL query
      const startTime = Date.now();
      const response = await axios({
        url: endpoint,
        method: 'POST',
        headers: requestHeaders,
        data: {
          query,
          variables
        }
      });
      const endTime = Date.now();
      
      // Return the response with timing information
      return {
        data: response.data,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        responseTime: endTime - startTime
      };
    } catch (error) {
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return {
          data: error.response.data,
          status: error.response.status,
          statusText: error.response.statusText,
          headers: error.response.headers,
          responseTime: 0,
          error: true
        };
      } else if (error.request) {
        // The request was made but no response was received
        throw new Error('No response received from GraphQL server');
      } else {
        // Something happened in setting up the request
        throw error;
      }
    }
  }
  
  /**
   * Fetch GraphQL schema using introspection
   * 
   * @param {string} endpoint - GraphQL endpoint URL
   * @param {Object} headers - HTTP headers
   * @param {Object} auth - Authentication details
   * @returns {Promise<Object>} - GraphQL schema
   */
  async fetchSchema(endpoint, headers = {}, auth = null) {
    // GraphQL introspection query to fetch the schema
    const introspectionQuery = `
      query IntrospectionQuery {
        __schema {
          queryType { name }
          mutationType { name }
          subscriptionType { name }
          types {
            ...FullType
          }
          directives {
            name
            description
            locations
            args {
              ...InputValue
            }
          }
        }
      }
      
      fragment FullType on __Type {
        kind
        name
        description
        fields(includeDeprecated: true) {
          name
          description
          args {
            ...InputValue
          }
          type {
            ...TypeRef
          }
          isDeprecated
          deprecationReason
        }
        inputFields {
          ...InputValue
        }
        interfaces {
          ...TypeRef
        }
        enumValues(includeDeprecated: true) {
          name
          description
          isDeprecated
          deprecationReason
        }
        possibleTypes {
          ...TypeRef
        }
      }
      
      fragment InputValue on __InputValue {
        name
        description
        type { ...TypeRef }
        defaultValue
      }
      
      fragment TypeRef on __Type {
        kind
        name
        ofType {
          kind
          name
          ofType {
            kind
            name
            ofType {
              kind
              name
              ofType {
                kind
                name
                ofType {
                  kind
                  name
                  ofType {
                    kind
                    name
                    ofType {
                      kind
                      name
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;
    
    try {
      const result = await this.executeQuery(endpoint, introspectionQuery, {}, headers, auth);
      
      if (result.error || !result.data || !result.data.data || !result.data.data.__schema) {
        throw new Error('Failed to fetch GraphQL schema');
      }
      
      return result.data.data.__schema;
    } catch (error) {
      throw new Error(`Error fetching GraphQL schema: ${error.message}`);
    }
  }
  
  /**
   * Validate a GraphQL query against a schema
   * 
   * @param {string} query - GraphQL query
   * @param {Object} schema - GraphQL schema
   * @returns {Object} - Validation result
   */
  validateQuery(query, schema) {
    // This is a placeholder for actual GraphQL validation
    // In a real implementation, we would use a library like graphql-js to validate the query
    
    // For now, we'll just do some basic checks
    if (!query) {
      return {
        valid: false,
        errors: ['Query is empty']
      };
    }
    
    if (!schema) {
      return {
        valid: false,
        errors: ['Schema is not available for validation']
      };
    }
    
    // Check for basic syntax errors
    const errors = [];
    
    // Check for missing curly braces
    const openBraces = (query.match(/{/g) || []).length;
    const closeBraces = (query.match(/}/g) || []).length;
    
    if (openBraces !== closeBraces) {
      errors.push('Syntax error: Mismatched curly braces');
    }
    
    // Check for missing query keyword for non-shorthand queries
    if (!query.trim().startsWith('{') && !query.includes('query') && !query.includes('mutation')) {
      errors.push('Syntax error: Missing query or mutation keyword');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Generate a sample GraphQL query based on schema
   * 
   * @param {Object} schema - GraphQL schema
   * @param {string} operationType - 'query' or 'mutation'
   * @returns {string} - Sample GraphQL query
   */
  generateSampleQuery(schema, operationType = 'query') {
    if (!schema) {
      return '# Schema not available for generating sample query';
    }
    
    try {
      let rootType;
      
      if (operationType === 'query') {
        rootType = schema.types.find(type => type.name === schema.queryType.name);
      } else if (operationType === 'mutation') {
        if (!schema.mutationType) {
          return '# This GraphQL API does not support mutations';
        }
        rootType = schema.types.find(type => type.name === schema.mutationType.name);
      } else {
        return '# Unsupported operation type';
      }
      
      if (!rootType || !rootType.fields || rootType.fields.length === 0) {
        return `# No ${operationType} fields available`;
      }
      
      // Take the first field as an example
      const exampleField = rootType.fields[0];
      let sampleQuery = `${operationType} {\n`;
      
      sampleQuery += `  ${exampleField.name}`;
      
      // Add arguments if any
      if (exampleField.args && exampleField.args.length > 0) {
        sampleQuery += '(';
        sampleQuery += exampleField.args.map(arg => {
          const argType = this.getBaseType(arg.type);
          let sampleValue;
          
          switch (argType) {
            case 'String':
              sampleValue = '"example"';
              break;
            case 'Int':
              sampleValue = '1';
              break;
            case 'Float':
              sampleValue = '1.0';
              break;
            case 'Boolean':
              sampleValue = 'true';
              break;
            case 'ID':
              sampleValue = '"123"';
              break;
            default:
              sampleValue = 'null';
          }
          
          return `${arg.name}: ${sampleValue}`;
        }).join(', ');
        sampleQuery += ')';
      }
      
      // Add selection set for object types
      const returnType = this.getNamedType(exampleField.type);
      const returnTypeInfo = schema.types.find(type => type.name === returnType);
      
      if (returnTypeInfo && returnTypeInfo.kind === 'OBJECT' && returnTypeInfo.fields) {
        sampleQuery += ' {\n';
        
        // Add a few fields from the return type
        const fieldsToInclude = returnTypeInfo.fields.slice(0, 3);
        fieldsToInclude.forEach(field => {
          sampleQuery += `    ${field.name}\n`;
        });
        
        sampleQuery += '  }';
      }
      
      sampleQuery += '\n}';
      
      return sampleQuery;
    } catch (error) {
      return `# Error generating sample query: ${error.message}`;
    }
  }
  
  /**
   * Get the base type name from a GraphQL type reference
   * 
   * @param {Object} typeRef - GraphQL type reference
   * @returns {string} - Base type name
   */
  getBaseType(typeRef) {
    if (!typeRef.kind) {
      return 'Unknown';
    }
    
    if (typeRef.kind !== 'NON_NULL' && typeRef.kind !== 'LIST') {
      return typeRef.name;
    }
    
    return this.getBaseType(typeRef.ofType);
  }
  
  /**
   * Get the named type from a GraphQL type reference
   * 
   * @param {Object} typeRef - GraphQL type reference
   * @returns {string} - Named type
   */
  getNamedType(typeRef) {
    if (!typeRef.kind) {
      return 'Unknown';
    }
    
    if (typeRef.kind === 'SCALAR' || typeRef.kind === 'OBJECT' || 
        typeRef.kind === 'INTERFACE' || typeRef.kind === 'UNION' || 
        typeRef.kind === 'ENUM' || typeRef.kind === 'INPUT_OBJECT') {
      return typeRef.name;
    }
    
    return this.getNamedType(typeRef.ofType);
  }
}

module.exports = GraphQLService;
