/**
 * Cross-Component Workflow Example
 * 
 * This example demonstrates how to use the NovaVision Hub to create workflows that span multiple Nova components.
 * In this example, we'll create a workflow that:
 * 1. Verifies a user's identity using NovaDNA
 * 2. Checks for security threats using NovaShield
 * 3. Ensures compliance with regulations using NovaTrack
 * 4. Creates a connector using NovaConnect
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { NovaVisionHubComponent } from '../';

/**
 * Cross-Component Workflow component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} props.novaDNA - NovaDNA instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Cross-Component Workflow component
 */
const CrossComponentWorkflow = ({
  novaConnect,
  novaShield,
  novaTrack,
  novaDNA,
  enableLogging = false
}) => {
  const [workflowState, setWorkflowState] = useState({
    step: 1,
    totalSteps: 4,
    identityVerified: false,
    securityChecked: false,
    complianceChecked: false,
    connectorCreated: false,
    identityId: null,
    securityReport: null,
    complianceReport: null,
    connector: null,
    error: null
  });
  
  // Handle workflow actions
  const handleAction = async (action, data) => {
    try {
      if (enableLogging) {
        console.log(`Handling workflow action: ${action}...`, data);
      }
      
      switch (action) {
        case 'workflow.start':
          // Start workflow
          setWorkflowState(prevState => ({
            ...prevState,
            step: 1,
            identityVerified: false,
            securityChecked: false,
            complianceChecked: false,
            connectorCreated: false,
            identityId: null,
            securityReport: null,
            complianceReport: null,
            connector: null,
            error: null
          }));
          break;
        
        case 'workflow.next':
          // Move to next step
          setWorkflowState(prevState => ({
            ...prevState,
            step: Math.min(prevState.step + 1, prevState.totalSteps)
          }));
          break;
        
        case 'workflow.previous':
          // Move to previous step
          setWorkflowState(prevState => ({
            ...prevState,
            step: Math.max(prevState.step - 1, 1)
          }));
          break;
        
        case 'novaDNA.verifyIdentity':
          // Verify identity using NovaDNA
          const identityResult = await novaDNA.verifyIdentity(data);
          
          setWorkflowState(prevState => ({
            ...prevState,
            identityVerified: true,
            identityId: identityResult.identityId,
            step: 2 // Move to next step
          }));
          break;
        
        case 'novaShield.checkSecurity':
          // Check security using NovaShield
          const securityResult = await novaShield.scanForThreats({
            identityId: workflowState.identityId,
            ...data
          });
          
          setWorkflowState(prevState => ({
            ...prevState,
            securityChecked: true,
            securityReport: securityResult,
            step: 3 // Move to next step
          }));
          break;
        
        case 'novaTrack.checkCompliance':
          // Check compliance using NovaTrack
          const complianceResult = await novaTrack.assessCompliance({
            identityId: workflowState.identityId,
            securityReport: workflowState.securityReport,
            ...data
          });
          
          setWorkflowState(prevState => ({
            ...prevState,
            complianceChecked: true,
            complianceReport: complianceResult,
            step: 4 // Move to next step
          }));
          break;
        
        case 'novaConnect.createConnector':
          // Create connector using NovaConnect
          const connectorResult = await novaConnect.createConnector({
            identityId: workflowState.identityId,
            securityReport: workflowState.securityReport,
            complianceReport: workflowState.complianceReport,
            ...data
          });
          
          setWorkflowState(prevState => ({
            ...prevState,
            connectorCreated: true,
            connector: connectorResult
          }));
          break;
        
        default:
          // Unknown action
          console.warn(`Unknown workflow action: ${action}`);
          break;
      }
    } catch (error) {
      console.error(`Error handling workflow action: ${action}`, error);
      
      setWorkflowState(prevState => ({
        ...prevState,
        error: error.message
      }));
    }
  };
  
  // Create workflow schema based on current step
  const createWorkflowSchema = () => {
    // Base schema
    const schema = {
      type: 'card',
      title: 'Cross-Component Workflow',
      content: {
        type: 'stepper',
        activeStep: workflowState.step - 1,
        steps: [
          {
            label: 'Identity Verification',
            completed: workflowState.identityVerified,
            content: {
              type: 'form',
              title: 'Identity Verification',
              description: 'Verify your identity using NovaDNA',
              fields: [
                {
                  type: 'textField',
                  name: 'firstName',
                  label: 'First Name',
                  required: true
                },
                {
                  type: 'textField',
                  name: 'lastName',
                  label: 'Last Name',
                  required: true
                },
                {
                  type: 'textField',
                  name: 'email',
                  label: 'Email',
                  required: true
                },
                {
                  type: 'select',
                  name: 'verificationMethod',
                  label: 'Verification Method',
                  required: true,
                  options: [
                    { value: 'biometric', label: 'Biometric' },
                    { value: 'document', label: 'Document' },
                    { value: 'knowledge', label: 'Knowledge-based' }
                  ]
                }
              ],
              actions: [
                {
                  type: 'button',
                  text: 'Verify Identity',
                  variant: 'primary',
                  onClick: 'novaDNA.verifyIdentity'
                }
              ]
            }
          },
          {
            label: 'Security Check',
            completed: workflowState.securityChecked,
            content: {
              type: 'form',
              title: 'Security Check',
              description: 'Check for security threats using NovaShield',
              fields: [
                {
                  type: 'select',
                  name: 'scanType',
                  label: 'Scan Type',
                  required: true,
                  options: [
                    { value: 'quick', label: 'Quick Scan' },
                    { value: 'full', label: 'Full Scan' },
                    { value: 'custom', label: 'Custom Scan' }
                  ]
                },
                {
                  type: 'checkbox',
                  name: 'includeVulnerabilities',
                  label: 'Include Vulnerabilities',
                  defaultValue: true
                },
                {
                  type: 'checkbox',
                  name: 'includeThreatIntelligence',
                  label: 'Include Threat Intelligence',
                  defaultValue: true
                }
              ],
              actions: [
                {
                  type: 'button',
                  text: 'Check Security',
                  variant: 'primary',
                  onClick: 'novaShield.checkSecurity'
                },
                {
                  type: 'button',
                  text: 'Back',
                  variant: 'secondary',
                  onClick: 'workflow.previous'
                }
              ]
            }
          },
          {
            label: 'Compliance Check',
            completed: workflowState.complianceChecked,
            content: {
              type: 'form',
              title: 'Compliance Check',
              description: 'Check compliance with regulations using NovaTrack',
              fields: [
                {
                  type: 'select',
                  name: 'framework',
                  label: 'Compliance Framework',
                  required: true,
                  options: [
                    { value: 'gdpr', label: 'GDPR' },
                    { value: 'hipaa', label: 'HIPAA' },
                    { value: 'pci', label: 'PCI DSS' },
                    { value: 'soc2', label: 'SOC 2' }
                  ]
                },
                {
                  type: 'checkbox',
                  name: 'includeControls',
                  label: 'Include Controls',
                  defaultValue: true
                },
                {
                  type: 'checkbox',
                  name: 'includeEvidence',
                  label: 'Include Evidence',
                  defaultValue: true
                }
              ],
              actions: [
                {
                  type: 'button',
                  text: 'Check Compliance',
                  variant: 'primary',
                  onClick: 'novaTrack.checkCompliance'
                },
                {
                  type: 'button',
                  text: 'Back',
                  variant: 'secondary',
                  onClick: 'workflow.previous'
                }
              ]
            }
          },
          {
            label: 'Create Connector',
            completed: workflowState.connectorCreated,
            content: {
              type: 'form',
              title: 'Create Connector',
              description: 'Create a connector using NovaConnect',
              fields: [
                {
                  type: 'textField',
                  name: 'name',
                  label: 'Connector Name',
                  required: true
                },
                {
                  type: 'select',
                  name: 'type',
                  label: 'Connector Type',
                  required: true,
                  options: [
                    { value: 'aws', label: 'AWS' },
                    { value: 'azure', label: 'Azure' },
                    { value: 'gcp', label: 'Google Cloud' },
                    { value: 'github', label: 'GitHub' }
                  ]
                },
                {
                  type: 'textField',
                  name: 'description',
                  label: 'Description',
                  multiline: true
                }
              ],
              actions: [
                {
                  type: 'button',
                  text: 'Create Connector',
                  variant: 'primary',
                  onClick: 'novaConnect.createConnector'
                },
                {
                  type: 'button',
                  text: 'Back',
                  variant: 'secondary',
                  onClick: 'workflow.previous'
                }
              ]
            }
          }
        ]
      }
    };
    
    // Add success message if workflow is complete
    if (workflowState.connectorCreated) {
      schema.content.successMessage = {
        type: 'alert',
        variant: 'success',
        title: 'Workflow Complete',
        message: `Successfully created connector: ${workflowState.connector.name}`,
        actions: [
          {
            type: 'button',
            text: 'Start New Workflow',
            variant: 'primary',
            onClick: 'workflow.start'
          }
        ]
      };
    }
    
    // Add error message if there's an error
    if (workflowState.error) {
      schema.content.errorMessage = {
        type: 'alert',
        variant: 'danger',
        title: 'Error',
        message: workflowState.error,
        actions: [
          {
            type: 'button',
            text: 'Retry',
            variant: 'primary',
            onClick: 'workflow.start'
          }
        ]
      };
    }
    
    return schema;
  };
  
  return (
    <div className="cross-component-workflow">
      <h1>Cross-Component Workflow</h1>
      <p>This example demonstrates how to use the NovaVision Hub to create workflows that span multiple Nova components.</p>
      
      <NovaVisionHubComponent
        novaConnect={novaConnect}
        novaShield={novaShield}
        novaTrack={novaTrack}
        novaDNA={novaDNA}
        schema={createWorkflowSchema()}
        onAction={handleAction}
        enableLogging={enableLogging}
      />
    </div>
  );
};

CrossComponentWorkflow.propTypes = {
  novaConnect: PropTypes.object.isRequired,
  novaShield: PropTypes.object.isRequired,
  novaTrack: PropTypes.object.isRequired,
  novaDNA: PropTypes.object.isRequired,
  enableLogging: PropTypes.bool
};

export default CrossComponentWorkflow;
