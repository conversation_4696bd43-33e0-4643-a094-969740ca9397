#!/bin/bash
# NovaConnect UAC Chaos Test Runner
# This script runs the chaos test for NovaConnect UAC

# Set variables
GCP_PROJECT_ID=${GCP_PROJECT_ID:-"novafuse-marketplace"}
GCP_SERVICE_URL=${GCP_SERVICE_URL:-"api.novafuse.io"}
NAMESPACE=${NAMESPACE:-"nova-marketplace"}
API_KEY=${API_KEY:-"test-api-key"}
TARGET_URL=${TARGET_URL:-"http://localhost:3001"}
RESULTS_DIR="./tests/scalability/results"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Create results directory
mkdir -p ${RESULTS_DIR}

# Print section header
echo -e "\n${YELLOW}==== NovaConnect UAC Chaos Test ====${NC}\n"

# Check if Artillery is installed
if ! command -v artillery &> /dev/null; then
  echo -e "${RED}Error: Artillery is required but not installed.${NC}"
  echo "Install with: npm install -g artillery"
  exit 1
fi

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
  echo -e "${YELLOW}Warning: kubectl is not installed. Chaos actions will be simulated.${NC}"
fi

# Check if we're connected to a Kubernetes cluster
if command -v kubectl &> /dev/null; then
  if ! kubectl get nodes &> /dev/null; then
    echo -e "${YELLOW}Warning: Not connected to a Kubernetes cluster. Chaos actions will be simulated.${NC}"
  fi
fi

# Function to simulate pod kills
simulate_pod_kill() {
  local selector=$1
  local namespace=$2
  local count=$3
  
  echo -e "${YELLOW}Simulating pod kill: ${count} pods with selector ${selector} in namespace ${namespace}${NC}"
  
  if command -v kubectl &> /dev/null && kubectl get nodes &> /dev/null; then
    # Get pods
    pods=$(kubectl get pods -n ${namespace} -l ${selector} -o jsonpath='{.items[*].metadata.name}')
    pod_array=($pods)
    
    # Kill pods
    for i in $(seq 1 ${count}); do
      if [ ${#pod_array[@]} -gt 0 ]; then
        random_index=$((RANDOM % ${#pod_array[@]}))
        pod_to_kill=${pod_array[$random_index]}
        echo -e "${YELLOW}Killing pod: ${pod_to_kill}${NC}"
        kubectl delete pod ${pod_to_kill} -n ${namespace} &
        # Remove the pod from the array
        pod_array=("${pod_array[@]:0:$random_index}" "${pod_array[@]:$((random_index+1))}")
      fi
    done
  else
    echo -e "${YELLOW}Simulating pod kill (no actual pods affected)${NC}"
  fi
}

# Function to simulate network issues
simulate_network_issues() {
  local selector=$1
  local namespace=$2
  local type=$3
  local value=$4
  local duration=$5
  
  echo -e "${YELLOW}Simulating network ${type}: ${value} for pods with selector ${selector} in namespace ${namespace} for ${duration} seconds${NC}"
  
  if command -v kubectl &> /dev/null && kubectl get nodes &> /dev/null; then
    # Get pods
    pods=$(kubectl get pods -n ${namespace} -l ${selector} -o jsonpath='{.items[*].metadata.name}')
    
    for pod in $pods; do
      # Check if pod has a running container
      if kubectl exec ${pod} -n ${namespace} -c novafuse-uac -- ls &> /dev/null; then
        echo -e "${YELLOW}Adding network ${type} to pod: ${pod}${NC}"
        
        if [ "$type" == "delay" ]; then
          kubectl exec ${pod} -n ${namespace} -c novafuse-uac -- tc qdisc add dev eth0 root netem delay ${value}ms
        elif [ "$type" == "loss" ]; then
          kubectl exec ${pod} -n ${namespace} -c novafuse-uac -- tc qdisc add dev eth0 root netem loss ${value}%
        fi
        
        # Schedule removal of network issue
        (
          sleep ${duration}
          echo -e "${YELLOW}Removing network ${type} from pod: ${pod}${NC}"
          kubectl exec ${pod} -n ${namespace} -c novafuse-uac -- tc qdisc del dev eth0 root
        ) &
      fi
    done
  else
    echo -e "${YELLOW}Simulating network ${type} (no actual pods affected)${NC}"
  fi
}

# Run the chaos test
echo -e "${YELLOW}Starting chaos test...${NC}"
echo -e "${YELLOW}Target URL: ${TARGET_URL}${NC}"
echo -e "${YELLOW}GCP Service URL: ${GCP_SERVICE_URL}${NC}"
echo -e "${YELLOW}Results will be saved to: ${RESULTS_DIR}${NC}"

# Export environment variables for Artillery
export GCP_PROJECT_ID=${GCP_PROJECT_ID}
export GCP_SERVICE_URL=${GCP_SERVICE_URL}
export NAMESPACE=${NAMESPACE}
export API_KEY=${API_KEY}
export TARGET_URL=${TARGET_URL}

# Start Artillery in the background
artillery run \
  --output ${RESULTS_DIR}/chaos-test-results.json \
  tests/scalability/chaos-test.yml &

ARTILLERY_PID=$!

# Wait for Artillery to start
sleep 10

# Monitor the test and inject chaos
echo -e "${YELLOW}Artillery test started with PID: ${ARTILLERY_PID}${NC}"
echo -e "${YELLOW}Monitoring test and injecting chaos...${NC}"

# Wait for the pod kill phase
echo -e "${YELLOW}Waiting for pod kill phase...${NC}"
sleep 480  # 60 + 120 + 300 = 480 seconds

# Simulate pod kills
echo -e "${YELLOW}Injecting chaos: Pod kills${NC}"
simulate_pod_kill "app=novafuse-uac" ${NAMESPACE} 3

# Wait for the network issues phase
echo -e "${YELLOW}Waiting for network issues phase...${NC}"
sleep 420  # 120 + 300 = 420 seconds

# Simulate network delay
echo -e "${YELLOW}Injecting chaos: Network delay${NC}"
simulate_network_issues "app=novafuse-uac" ${NAMESPACE} "delay" 1000 60

# Wait a bit
sleep 30

# Simulate packet loss
echo -e "${YELLOW}Injecting chaos: Packet loss${NC}"
simulate_network_issues "app=novafuse-uac" ${NAMESPACE} "loss" 20 30

# Wait for Artillery to finish
echo -e "${YELLOW}Waiting for Artillery to finish...${NC}"
wait ${ARTILLERY_PID}

# Generate report
echo -e "${YELLOW}Generating report...${NC}"
artillery report ${RESULTS_DIR}/chaos-test-results.json --output ${RESULTS_DIR}/chaos-test-report.html

echo -e "${GREEN}Chaos test completed!${NC}"
echo -e "${GREEN}Report saved to: ${RESULTS_DIR}/chaos-test-report.html${NC}"

exit 0
