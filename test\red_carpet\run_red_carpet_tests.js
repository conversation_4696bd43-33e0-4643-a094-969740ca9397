/**
 * NovaFuse Red Carpet Testing Strategy
 * Test Runner
 * 
 * This script runs all red carpet tests and generates a comprehensive report.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

// Import test modules
const { runEnhancedCertaintyTests } = require('./enhanced_certainty_test');
const { runFalsePositiveTests } = require('./false_positive_test');

// Configuration
const config = {
  resultsDir: path.join(__dirname, '../../results/red_carpet'),
  tests: [
    {
      name: 'Enhanced Certainty Rate',
      run: runEnhancedCertaintyTests,
      weight: 0.4 // 40% of overall score
    },
    {
      name: 'False Positive Rate',
      run: runFalsePositiveTests,
      weight: 0.4 // 40% of overall score
    }
    // Additional tests will be added here
  ]
};

/**
 * Run all red carpet tests
 */
async function runRedCarpetTests() {
  console.log('=== NovaFuse Red Carpet Testing Strategy ===\n');
  console.log(`Running ${config.tests.length} tests:\n`);
  config.tests.forEach((test, index) => {
    console.log(`${index + 1}. ${test.name} (Weight: ${test.weight * 100}%)`);
  });
  
  // Ensure results directory exists
  await ensureResultsDir();
  
  // Initialize results
  const results = {
    timestamp: new Date().toISOString(),
    tests: [],
    summary: {
      testsRun: 0,
      testsPassed: 0,
      overallScore: 0,
      redCarpetReady: false
    }
  };
  
  // Run each test
  for (let i = 0; i < config.tests.length; i++) {
    const test = config.tests[i];
    
    console.log(`\n\n=== Running Test ${i + 1}/${config.tests.length}: ${test.name} ===\n`);
    
    try {
      const testResult = await test.run();
      
      results.tests.push({
        name: test.name,
        weight: test.weight,
        passed: testResult.summary.redCarpetReady,
        score: calculateTestScore(testResult),
        result: testResult
      });
      
      results.summary.testsRun++;
      
      if (testResult.summary.redCarpetReady) {
        results.summary.testsPassed++;
      }
    } catch (error) {
      console.error(`Error running test ${test.name}:`, error);
      
      results.tests.push({
        name: test.name,
        weight: test.weight,
        passed: false,
        score: 0,
        error: error.message
      });
      
      results.summary.testsRun++;
    }
  }
  
  // Calculate overall score
  results.summary.overallScore = calculateOverallScore(results.tests);
  
  // Determine if red carpet ready
  results.summary.redCarpetReady = 
    results.summary.testsPassed === results.summary.testsRun &&
    results.summary.overallScore >= 0.9; // 90% threshold for red carpet readiness
  
  // Save results
  await saveResults(results);
  
  // Print final assessment
  console.log('\n\n=== Red Carpet Testing Summary ===');
  console.log(`Tests Run: ${results.summary.testsRun}/${config.tests.length}`);
  console.log(`Tests Passed: ${results.summary.testsPassed}/${results.summary.testsRun}`);
  console.log(`Overall Score: ${(results.summary.overallScore * 100).toFixed(2)}%`);
  console.log(`\nRed Carpet Ready: ${results.summary.redCarpetReady ? 'YES' : 'NO'}`);
  
  if (!results.summary.redCarpetReady) {
    console.log('\nFailed Tests:');
    results.tests.filter(test => !test.passed).forEach(test => {
      console.log(`- ${test.name}: ${(test.score * 100).toFixed(2)}%`);
    });
  }
  
  return results;
}

/**
 * Calculate test score
 * @param {Object} testResult - Test result
 * @returns {number} - Test score (0-1)
 */
function calculateTestScore(testResult) {
  // Different scoring logic for different test types
  if (testResult.summary.hasOwnProperty('overallAverageCertaintyRate')) {
    // Enhanced certainty test
    const targetRate = 0.5; // 50%
    const minimumRate = 0.3; // 30%
    const actualRate = testResult.summary.overallAverageCertaintyRate;
    
    if (actualRate >= targetRate) {
      return 1.0; // Full score if target is met
    } else if (actualRate >= minimumRate) {
      // Partial score if between minimum and target
      return 0.7 + (0.3 * (actualRate - minimumRate) / (targetRate - minimumRate));
    } else {
      // Below minimum gets a base score
      return Math.max(0.5, actualRate / minimumRate);
    }
  } else if (testResult.summary.hasOwnProperty('overallAverageFalsePositiveRate')) {
    // False positive test
    const targetRate = 0.01; // 1%
    const maximumRate = 0.05; // 5%
    const actualRate = testResult.summary.overallAverageFalsePositiveRate;
    
    if (actualRate <= targetRate) {
      return 1.0; // Full score if target is met
    } else if (actualRate <= maximumRate) {
      // Partial score if between target and maximum
      return 0.7 + (0.3 * (maximumRate - actualRate) / (maximumRate - targetRate));
    } else {
      // Above maximum gets a base score
      return Math.max(0.5, maximumRate / actualRate);
    }
  }
  
  // Default scoring for other tests
  return testResult.summary.redCarpetReady ? 1.0 : 0.5;
}

/**
 * Calculate overall score
 * @param {Array} tests - Test results
 * @returns {number} - Overall score (0-1)
 */
function calculateOverallScore(tests) {
  // Calculate weighted average of test scores
  const totalWeight = tests.reduce((sum, test) => sum + test.weight, 0);
  
  if (totalWeight === 0) {
    return 0;
  }
  
  const weightedSum = tests.reduce((sum, test) => sum + (test.score * test.weight), 0);
  
  return weightedSum / totalWeight;
}

/**
 * Ensure results directory exists
 */
async function ensureResultsDir() {
  try {
    await mkdir(config.resultsDir, { recursive: true });
    console.log(`Results directory: ${config.resultsDir}`);
  } catch (error) {
    if (error.code !== 'EEXIST') {
      console.error('Error creating results directory:', error);
      throw error;
    }
  }
}

/**
 * Save test results
 * @param {Object} results - Test results
 */
async function saveResults(results) {
  const resultsPath = path.join(config.resultsDir, `red_carpet_results_${new Date().toISOString().replace(/:/g, '-')}.json`);
  
  await writeFile(resultsPath, JSON.stringify(results, null, 2));
  console.log(`\nResults saved to: ${resultsPath}`);
  
  // Generate summary report
  const summaryPath = path.join(config.resultsDir, `red_carpet_summary_${new Date().toISOString().replace(/:/g, '-')}.txt`);
  
  const summaryReport = generateSummaryReport(results);
  await writeFile(summaryPath, summaryReport);
  console.log(`Summary report saved to: ${summaryPath}`);
}

/**
 * Generate summary report
 * @param {Object} results - Test results
 * @returns {string} - Summary report
 */
function generateSummaryReport(results) {
  return `
=== NovaFuse Red Carpet Testing Strategy ===
Test Date: ${new Date().toISOString()}

=== Overall Summary ===
Tests Run: ${results.summary.testsRun}/${config.tests.length}
Tests Passed: ${results.summary.testsPassed}/${results.summary.testsRun}
Overall Score: ${(results.summary.overallScore * 100).toFixed(2)}%

Red Carpet Ready: ${results.summary.redCarpetReady ? 'YES' : 'NO'}

=== Individual Test Results ===
${results.tests.map(test => `
${test.name} (Weight: ${test.weight * 100}%)
- Score: ${(test.score * 100).toFixed(2)}%
- Passed: ${test.passed ? 'YES' : 'NO'}
${test.error ? `- Error: ${test.error}` : ''}
`).join('')}

=== Red Carpet Assessment ===
${results.summary.redCarpetReady ? 
  'NovaFuse has successfully passed all red carpet tests and is ready for a prestigious market debut.' : 
  'NovaFuse has not yet met all red carpet criteria. See individual test results for areas that need improvement.'}
`;
}

// Run the tests if this file is executed directly
if (require.main === module) {
  runRedCarpetTests().catch(error => {
    console.error('Error running tests:', error);
    process.exit(1);
  });
}

module.exports = {
  runRedCarpetTests
};
