"""
Run the complete fine-tuning pipeline for the compliance language model.

This script runs the entire pipeline:
1. Prepare the data
2. Fine-tune the model
3. Evaluate the model
"""

import os
import argparse
import logging
import subprocess
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(command):
    """
    Run a shell command and log the output.
    
    Args:
        command: The command to run
        
    Returns:
        The return code of the command
    """
    logger.info(f"Running command: {command}")
    process = subprocess.Popen(
        command,
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True
    )
    
    # Print output in real-time
    for line in process.stdout:
        logger.info(line.strip())
    
    # Wait for the process to complete
    return_code = process.wait()
    logger.info(f"Command completed with return code: {return_code}")
    return return_code

def run_pipeline(
    model_name,
    data_dir,
    output_dir,
    num_train_epochs,
    per_device_train_batch_size,
    learning_rate,
    max_length,
    output_format,
    no_gpu
):
    """
    Run the complete fine-tuning pipeline.
    
    Args:
        model_name: Name or path of the pre-trained model
        data_dir: Path to the data directory
        output_dir: Directory to save outputs
        num_train_epochs: Number of training epochs
        per_device_train_batch_size: Batch size per device during training
        learning_rate: Learning rate
        max_length: Maximum sequence length
        output_format: Format of the output data
        no_gpu: Disable GPU usage even if available
    """
    # Create timestamp for this run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    run_dir = os.path.join(output_dir, f"run_{timestamp}")
    os.makedirs(run_dir, exist_ok=True)
    
    # Create subdirectories
    data_output_dir = os.path.join(run_dir, "data")
    model_output_dir = os.path.join(run_dir, "model")
    eval_output_dir = os.path.join(run_dir, "eval")
    os.makedirs(data_output_dir, exist_ok=True)
    os.makedirs(model_output_dir, exist_ok=True)
    os.makedirs(eval_output_dir, exist_ok=True)
    
    # Step 1: Prepare the data
    logger.info("Step 1: Preparing the data")
    prepare_data_cmd = (
        f"python prepare_data.py "
        f"--data_dir {data_dir} "
        f"--output_dir {data_output_dir} "
        f"--output_format {output_format}"
    )
    if run_command(prepare_data_cmd) != 0:
        logger.error("Data preparation failed")
        return
    
    # Step 2: Fine-tune the model
    logger.info("Step 2: Fine-tuning the model")
    train_file = os.path.join(data_output_dir, f"train.{output_format}")
    val_file = os.path.join(data_output_dir, f"val.{output_format}")
    
    fine_tune_cmd = (
        f"python fine_tune.py "
        f"--model_name {model_name} "
        f"--train_file {train_file} "
        f"--val_file {val_file} "
        f"--output_dir {model_output_dir} "
        f"--num_train_epochs {num_train_epochs} "
        f"--per_device_train_batch_size {per_device_train_batch_size} "
        f"--learning_rate {learning_rate} "
        f"--block_size {max_length}"
    )
    if no_gpu:
        fine_tune_cmd += " --no_gpu"
    
    if run_command(fine_tune_cmd) != 0:
        logger.error("Fine-tuning failed")
        return
    
    # Step 3: Evaluate the model
    logger.info("Step 3: Evaluating the model")
    eval_output_file = os.path.join(eval_output_dir, "evaluation_results.json")
    
    evaluate_cmd = (
        f"python evaluate_model.py "
        f"--model_path {model_output_dir} "
        f"--test_file {val_file} "
        f"--output_file {eval_output_file} "
        f"--max_length {max_length}"
    )
    if no_gpu:
        evaluate_cmd += " --no_gpu"
    
    if run_command(evaluate_cmd) != 0:
        logger.error("Evaluation failed")
        return
    
    logger.info(f"Pipeline completed successfully. Outputs saved to {run_dir}")

def main():
    parser = argparse.ArgumentParser(description='Run the complete fine-tuning pipeline for the compliance language model')
    parser.add_argument('--model_name', type=str, default='gpt2', help='Name or path of the pre-trained model')
    parser.add_argument('--data_dir', type=str, default='../ucia/data', help='Path to the data directory')
    parser.add_argument('--output_dir', type=str, default='./runs', help='Directory to save outputs')
    parser.add_argument('--num_train_epochs', type=int, default=3, help='Number of training epochs')
    parser.add_argument('--per_device_train_batch_size', type=int, default=8, help='Batch size per device during training')
    parser.add_argument('--learning_rate', type=float, default=5e-5, help='Learning rate')
    parser.add_argument('--max_length', type=int, default=512, help='Maximum sequence length')
    parser.add_argument('--output_format', type=str, default='jsonl', choices=['jsonl', 'json'], help='Format of the output data')
    parser.add_argument('--no_gpu', action='store_true', help='Disable GPU usage even if available')
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Run the pipeline
    run_pipeline(
        model_name=args.model_name,
        data_dir=args.data_dir,
        output_dir=args.output_dir,
        num_train_epochs=args.num_train_epochs,
        per_device_train_batch_size=args.per_device_train_batch_size,
        learning_rate=args.learning_rate,
        max_length=args.max_length,
        output_format=args.output_format,
        no_gpu=args.no_gpu
    )

if __name__ == "__main__":
    main()
