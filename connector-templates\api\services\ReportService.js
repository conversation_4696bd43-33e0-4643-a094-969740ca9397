/**
 * Report Service
 *
 * This service handles report generation and management.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const AuditService = require('./AuditService');

class ReportService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.reportsDir = path.join(this.dataDir, 'reports');
    this.reportsFile = path.join(this.reportsDir, 'reports.json');
    this.reportTemplatesFile = path.join(this.reportsDir, 'report_templates.json');
    this.scheduledReportsFile = path.join(this.reportsDir, 'scheduled_reports.json');
    this.auditService = new AuditService(dataDir);

    // Define default report templates
    this.defaultTemplates = [
      {
        id: 'api-usage',
        name: 'API Usage Report',
        description: 'Shows API usage statistics over time',
        type: 'analytics',
        dataSource: 'analytics',
        format: 'chart',
        config: {
          metrics: ['requests', 'errors', 'latency'],
          dimensions: ['endpoint', 'date'],
          chartType: 'line'
        },
        isSystem: true,
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'compliance-summary',
        name: 'Compliance Summary Report',
        description: 'Summarizes compliance status across all policies',
        type: 'compliance',
        dataSource: 'policies',
        format: 'table',
        config: {
          metrics: ['violations', 'compliant', 'total'],
          dimensions: ['policy', 'resource'],
          filters: []
        },
        isSystem: true,
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    ];

    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.reportsDir, { recursive: true });

      // Initialize files if they don't exist
      await this.initializeFile(this.reportsFile, []);
      await this.initializeFile(this.reportTemplatesFile, []);
      await this.initializeFile(this.scheduledReportsFile, []);
    } catch (error) {
      console.error('Error creating reports directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);

      // For report templates, we want to ensure default templates exist
      if (filePath === this.reportTemplatesFile) {
        await this.ensureDefaultTemplates();
      }
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        if (filePath === this.reportTemplatesFile) {
          // For report templates, use default templates
          await fs.writeFile(filePath, JSON.stringify(this.defaultTemplates, null, 2));
        } else {
          await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
        }
      } else {
        throw error;
      }
    }
  }

  /**
   * Ensure default templates exist in the templates file
   */
  async ensureDefaultTemplates() {
    const templates = await this.loadData(this.reportTemplatesFile);
    let changed = false;

    // Check each default template
    for (const defaultTemplate of this.defaultTemplates) {
      const existingTemplate = templates.find(t => t.id === defaultTemplate.id);

      if (!existingTemplate) {
        // Add missing default template
        templates.push(defaultTemplate);
        changed = true;
      } else if (existingTemplate.isSystem) {
        // Update existing system template
        Object.assign(existingTemplate, defaultTemplate);
        changed = true;
      }
    }

    if (changed) {
      await this.saveData(this.reportTemplatesFile, templates);
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get all report templates
   */
  async getAllReportTemplates() {
    return this.loadData(this.reportTemplatesFile);
  }

  /**
   * Get report template by ID
   */
  async getReportTemplateById(id) {
    const templates = await this.loadData(this.reportTemplatesFile);
    const template = templates.find(t => t.id === id);

    if (!template) {
      throw new NotFoundError(`Report template with ID ${id} not found`);
    }

    return template;
  }

  /**
   * Get report templates by type
   */
  async getReportTemplatesByType(type) {
    const templates = await this.loadData(this.reportTemplatesFile);
    return templates.filter(t => t.type === type);
  }

  /**
   * Create a new report template
   */
  async createReportTemplate(data, userId) {
    if (!data.name) {
      throw new ValidationError('Template name is required');
    }

    if (!data.type) {
      throw new ValidationError('Template type is required');
    }

    if (!data.dataSource) {
      throw new ValidationError('Data source is required');
    }

    if (!data.format) {
      throw new ValidationError('Report format is required');
    }

    const templates = await this.loadData(this.reportTemplatesFile);

    // Generate ID if not provided
    const id = data.id || this.generateTemplateId(data.name);

    // Check for duplicate ID
    if (templates.some(t => t.id === id)) {
      throw new ValidationError(`Template with ID ${id} already exists`);
    }

    // Create new template
    const newTemplate = {
      id,
      name: data.name,
      description: data.description || '',
      type: data.type,
      dataSource: data.dataSource,
      format: data.format,
      config: data.config || {},
      isSystem: false,
      createdBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };

    templates.push(newTemplate);
    await this.saveData(this.reportTemplatesFile, templates);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'CREATE',
      resourceType: 'report_template',
      resourceId: id,
      details: {
        name: newTemplate.name,
        type: newTemplate.type
      }
    });

    return newTemplate;
  }

  /**
   * Update a report template
   */
  async updateReportTemplate(id, data, userId) {
    const templates = await this.loadData(this.reportTemplatesFile);
    const index = templates.findIndex(t => t.id === id);

    if (index === -1) {
      throw new NotFoundError(`Report template with ID ${id} not found`);
    }

    const template = templates[index];

    // Check if trying to update a system template
    if (template.isSystem) {
      throw new ValidationError('Cannot update system templates');
    }

    // Update template
    const updatedTemplate = {
      ...template,
      ...data,
      id, // Don't allow changing the ID
      isSystem: false, // Don't allow changing system status
      updated: new Date().toISOString()
    };

    templates[index] = updatedTemplate;
    await this.saveData(this.reportTemplatesFile, templates);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UPDATE',
      resourceType: 'report_template',
      resourceId: id,
      details: {
        name: updatedTemplate.name,
        type: updatedTemplate.type
      }
    });

    return updatedTemplate;
  }

  /**
   * Generate a template ID from name
   */
  generateTemplateId(name) {
    // Convert name to kebab-case and add a unique suffix
    const baseId = name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-|-$/g, '');

    const uniqueSuffix = uuidv4().substring(0, 8);
    return `${baseId}-${uniqueSuffix}`;
  }

  /**
   * Delete a report template
   */
  async deleteReportTemplate(id, userId) {
    const templates = await this.loadData(this.reportTemplatesFile);
    const index = templates.findIndex(t => t.id === id);

    if (index === -1) {
      throw new NotFoundError(`Report template with ID ${id} not found`);
    }

    const template = templates[index];

    // Check if trying to delete a system template
    if (template.isSystem) {
      throw new ValidationError('Cannot delete system templates');
    }

    // Check if template is in use by any reports or scheduled reports
    const reports = await this.loadData(this.reportsFile);
    if (reports.some(r => r.templateId === id)) {
      throw new ValidationError(`Cannot delete template that is in use by reports`);
    }

    const scheduledReports = await this.loadData(this.scheduledReportsFile);
    if (scheduledReports.some(r => r.templateId === id)) {
      throw new ValidationError(`Cannot delete template that is in use by scheduled reports`);
    }

    // Remove the template
    templates.splice(index, 1);
    await this.saveData(this.reportTemplatesFile, templates);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'DELETE',
      resourceType: 'report_template',
      resourceId: id,
      details: {
        name: template.name,
        type: template.type
      }
    });

    return { success: true, message: `Report template ${id} deleted` };
  }

  /**
   * Clone a report template
   */
  async cloneReportTemplate(id, data, userId) {
    const sourceTemplate = await this.getReportTemplateById(id);

    // Create new template based on source
    const newTemplate = {
      name: data.name || `${sourceTemplate.name} (Copy)`,
      description: data.description || sourceTemplate.description,
      type: sourceTemplate.type,
      dataSource: sourceTemplate.dataSource,
      format: sourceTemplate.format,
      config: JSON.parse(JSON.stringify(sourceTemplate.config)),
      isSystem: false
    };

    // Create the new template
    const createdTemplate = await this.createReportTemplate(newTemplate, userId);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'CLONE',
      resourceType: 'report_template',
      resourceId: createdTemplate.id,
      details: {
        name: createdTemplate.name,
        sourceId: id,
        sourceName: sourceTemplate.name
      }
    });

    return createdTemplate;
  }

  /**
   * Generate a report from a template
   */
  async generateReport(templateId, parameters = {}, userId) {
    // Get the template
    const template = await this.getReportTemplateById(templateId);

    // Generate report data based on template type and data source
    let reportData;

    switch (template.dataSource) {
      case 'analytics':
        reportData = await this.generateAnalyticsReport(template, parameters);
        break;
      case 'policies':
        reportData = await this.generatePoliciesReport(template, parameters);
        break;
      case 'connectors':
        reportData = await this.generateConnectorsReport(template, parameters);
        break;
      case 'audit':
        reportData = await this.generateAuditReport(template, parameters);
        break;
      default:
        throw new ValidationError(`Unsupported data source: ${template.dataSource}`);
    }

    // Create report record
    const report = {
      id: uuidv4(),
      templateId,
      name: parameters.name || template.name,
      parameters,
      data: reportData,
      format: template.format,
      createdBy: userId,
      created: new Date().toISOString()
    };

    // Save report
    const reports = await this.loadData(this.reportsFile);
    reports.push(report);
    await this.saveData(this.reportsFile, reports);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'GENERATE',
      resourceType: 'report',
      resourceId: report.id,
      details: {
        name: report.name,
        templateId
      }
    });

    return report;
  }

  /**
   * Generate analytics report
   */
  async generateAnalyticsReport(template, parameters) {
    // In a real implementation, this would query the analytics database
    // For now, just generate sample data

    const metrics = template.config.metrics || [];
    const dimensions = template.config.dimensions || [];

    // Generate sample data based on metrics and dimensions
    const data = {
      metrics,
      dimensions,
      rows: []
    };

    // Generate sample rows
    if (dimensions.includes('endpoint') && dimensions.includes('date')) {
      const endpoints = ['/api/connectors', '/api/testing', '/api/auth', '/api/analytics'];
      const dates = this.generateDateRange(parameters.startDate, parameters.endDate || new Date());

      for (const endpoint of endpoints) {
        for (const date of dates) {
          const row = {};

          // Add dimensions
          row.endpoint = endpoint;
          row.date = date;

          // Add metrics
          if (metrics.includes('requests')) {
            row.requests = Math.floor(Math.random() * 1000);
          }

          if (metrics.includes('errors')) {
            row.errors = Math.floor(Math.random() * 100);
          }

          if (metrics.includes('latency')) {
            row.latency = Math.floor(Math.random() * 500);
          }

          data.rows.push(row);
        }
      }
    }

    return data;
  }

  /**
   * Generate policies report
   */
  async generatePoliciesReport(template, parameters) {
    // In a real implementation, this would query the policies database
    // For now, just generate sample data

    const metrics = template.config.metrics || [];
    const dimensions = template.config.dimensions || [];

    // Generate sample data based on metrics and dimensions
    const data = {
      metrics,
      dimensions,
      rows: []
    };

    // Generate sample rows
    if (dimensions.includes('policy') && dimensions.includes('resource')) {
      const policies = ['data-encryption', 'access-control', 'password-policy', 'audit-logging'];
      const resources = ['api-keys', 'connectors', 'credentials', 'users'];

      for (const policy of policies) {
        for (const resource of resources) {
          const row = {};

          // Add dimensions
          row.policy = policy;
          row.resource = resource;

          // Add metrics
          if (metrics.includes('violations')) {
            row.violations = Math.floor(Math.random() * 10);
          }

          if (metrics.includes('compliant')) {
            row.compliant = Math.floor(Math.random() * 100);
          }

          if (metrics.includes('total')) {
            row.total = (row.violations || 0) + (row.compliant || 0);
          }

          data.rows.push(row);
        }
      }
    }

    return data;
  }

  /**
   * Generate connectors report
   */
  async generateConnectorsReport(template, parameters) {
    // In a real implementation, this would query the connectors database
    // For now, just generate sample data

    // Sample data for connectors report
    return {
      metrics: ['status', 'uptime', 'requests'],
      dimensions: ['connector', 'environment'],
      rows: [
        { connector: 'salesforce', environment: 'production', status: 'healthy', uptime: 99.9, requests: 5432 },
        { connector: 'salesforce', environment: 'staging', status: 'healthy', uptime: 99.5, requests: 1234 },
        { connector: 'jira', environment: 'production', status: 'degraded', uptime: 98.2, requests: 3210 },
        { connector: 'jira', environment: 'staging', status: 'healthy', uptime: 99.7, requests: 987 },
        { connector: 'github', environment: 'production', status: 'healthy', uptime: 99.8, requests: 4567 },
        { connector: 'github', environment: 'staging', status: 'healthy', uptime: 99.6, requests: 1432 }
      ]
    };
  }

  /**
   * Generate audit report
   */
  async generateAuditReport(template, parameters) {
    // In a real implementation, this would query the audit database
    // For now, just generate sample data

    // Sample data for audit report
    return {
      metrics: ['count'],
      dimensions: ['action', 'resourceType', 'user'],
      rows: [
        { action: 'CREATE', resourceType: 'connector', user: 'admin', count: 12 },
        { action: 'UPDATE', resourceType: 'connector', user: 'admin', count: 34 },
        { action: 'DELETE', resourceType: 'connector', user: 'admin', count: 5 },
        { action: 'CREATE', resourceType: 'api_key', user: 'admin', count: 8 },
        { action: 'DELETE', resourceType: 'api_key', user: 'admin', count: 3 },
        { action: 'LOGIN', resourceType: 'user', user: 'admin', count: 45 },
        { action: 'LOGIN', resourceType: 'user', user: 'user1', count: 23 },
        { action: 'LOGIN', resourceType: 'user', user: 'user2', count: 17 }
      ]
    };
  }

  /**
   * Generate a range of dates
   */
  generateDateRange(startDate, endDate) {
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // Default to 7 days ago
    const end = endDate ? new Date(endDate) : new Date();

    const dates = [];
    const current = new Date(start);

    while (current <= end) {
      dates.push(current.toISOString().split('T')[0]); // YYYY-MM-DD format
      current.setDate(current.getDate() + 1);
    }

    return dates;
  }

  /**
   * Get all reports
   */
  async getAllReports(filters = {}) {
    const reports = await this.loadData(this.reportsFile);

    // Apply filters
    let filteredReports = reports;

    if (filters.templateId) {
      filteredReports = filteredReports.filter(r => r.templateId === filters.templateId);
    }

    if (filters.createdBy) {
      filteredReports = filteredReports.filter(r => r.createdBy === filters.createdBy);
    }

    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      filteredReports = filteredReports.filter(r => new Date(r.created) >= startDate);
    }

    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      filteredReports = filteredReports.filter(r => new Date(r.created) <= endDate);
    }

    // Sort by created date (newest first)
    filteredReports.sort((a, b) => new Date(b.created) - new Date(a.created));

    return filteredReports;
  }

  /**
   * Get reports for a user
   */
  async getReportsForUser(userId, filters = {}) {
    return this.getAllReports({ ...filters, createdBy: userId });
  }

  /**
   * Get report by ID
   */
  async getReportById(id) {
    const reports = await this.loadData(this.reportsFile);
    const report = reports.find(r => r.id === id);

    if (!report) {
      throw new NotFoundError(`Report with ID ${id} not found`);
    }

    return report;
  }

  /**
   * Delete a report
   */
  async deleteReport(id, userId) {
    const reports = await this.loadData(this.reportsFile);
    const index = reports.findIndex(r => r.id === id);

    if (index === -1) {
      throw new NotFoundError(`Report with ID ${id} not found`);
    }

    const report = reports[index];

    // Check if user has permission to delete
    if (report.createdBy !== userId) {
      throw new AuthorizationError('You do not have permission to delete this report');
    }

    // Remove the report
    reports.splice(index, 1);
    await this.saveData(this.reportsFile, reports);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'DELETE',
      resourceType: 'report',
      resourceId: id,
      details: {
        name: report.name,
        templateId: report.templateId
      }
    });

    return { success: true, message: `Report ${id} deleted` };
  }

  /**
   * Export report to different formats
   */
  async exportReport(id, format) {
    const report = await this.getReportById(id);

    // Validate format
    const validFormats = ['json', 'csv', 'pdf', 'excel'];
    if (!validFormats.includes(format)) {
      throw new ValidationError(`Invalid export format: ${format}. Valid formats: ${validFormats.join(', ')}`);
    }

    // In a real implementation, this would convert the report data to the requested format
    // For now, just return the report data in JSON format

    if (format === 'json') {
      return {
        format: 'json',
        data: JSON.stringify(report.data, null, 2),
        contentType: 'application/json'
      };
    } else if (format === 'csv') {
      // Convert report data to CSV
      const csv = this.convertToCSV(report.data);

      return {
        format: 'csv',
        data: csv,
        contentType: 'text/csv'
      };
    } else {
      // For other formats, just return a message
      return {
        format,
        data: `Export to ${format} format is not implemented yet`,
        contentType: 'text/plain'
      };
    }
  }

  /**
   * Convert report data to CSV
   */
  convertToCSV(data) {
    if (!data || !data.rows || !Array.isArray(data.rows) || data.rows.length === 0) {
      return '';
    }

    // Get all unique columns from the data
    const columns = new Set();
    data.rows.forEach(row => {
      Object.keys(row).forEach(key => columns.add(key));
    });

    const columnArray = Array.from(columns);

    // Create header row
    let csv = columnArray.join(',') + '\n';

    // Add data rows
    data.rows.forEach(row => {
      const rowValues = columnArray.map(column => {
        const value = row[column];

        // Handle different value types
        if (value === null || value === undefined) {
          return '';
        } else if (typeof value === 'string') {
          // Escape quotes and wrap in quotes if contains comma or quote
          if (value.includes(',') || value.includes('"')) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        } else {
          return String(value);
        }
      });

      csv += rowValues.join(',') + '\n';
    });

    return csv;
  }

  /**
   * Get all scheduled reports
   */
  async getAllScheduledReports(filters = {}) {
    const scheduledReports = await this.loadData(this.scheduledReportsFile);

    // Apply filters
    let filteredReports = scheduledReports;

    if (filters.templateId) {
      filteredReports = filteredReports.filter(r => r.templateId === filters.templateId);
    }

    if (filters.createdBy) {
      filteredReports = filteredReports.filter(r => r.createdBy === filters.createdBy);
    }

    if (filters.status) {
      filteredReports = filteredReports.filter(r => r.status === filters.status);
    }

    // Sort by created date (newest first)
    filteredReports.sort((a, b) => new Date(b.created) - new Date(a.created));

    return filteredReports;
  }

  /**
   * Get scheduled reports for a user
   */
  async getScheduledReportsForUser(userId, filters = {}) {
    return this.getAllScheduledReports({ ...filters, createdBy: userId });
  }

  /**
   * Get scheduled report by ID
   */
  async getScheduledReportById(id) {
    const scheduledReports = await this.loadData(this.scheduledReportsFile);
    const report = scheduledReports.find(r => r.id === id);

    if (!report) {
      throw new NotFoundError(`Scheduled report with ID ${id} not found`);
    }

    return report;
  }

  /**
   * Create a scheduled report
   */
  async createScheduledReport(data, userId) {
    if (!data.templateId) {
      throw new ValidationError('Template ID is required');
    }

    if (!data.schedule) {
      throw new ValidationError('Schedule is required');
    }

    if (!data.schedule.frequency) {
      throw new ValidationError('Schedule frequency is required');
    }

    // Validate frequency
    const validFrequencies = ['daily', 'weekly', 'monthly', 'quarterly'];
    if (!validFrequencies.includes(data.schedule.frequency)) {
      throw new ValidationError(`Invalid frequency: ${data.schedule.frequency}. Valid frequencies: ${validFrequencies.join(', ')}`);
    }

    // Check if template exists
    await this.getReportTemplateById(data.templateId);

    const scheduledReports = await this.loadData(this.scheduledReportsFile);

    // Create new scheduled report
    const newScheduledReport = {
      id: uuidv4(),
      templateId: data.templateId,
      name: data.name || `Scheduled Report ${new Date().toISOString().split('T')[0]}`,
      description: data.description || '',
      parameters: data.parameters || {},
      schedule: data.schedule,
      recipients: data.recipients || [],
      exportFormat: data.exportFormat || 'pdf',
      status: 'active',
      lastRun: null,
      nextRun: this.calculateNextRunDate(data.schedule),
      createdBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };

    scheduledReports.push(newScheduledReport);
    await this.saveData(this.scheduledReportsFile, scheduledReports);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'CREATE',
      resourceType: 'scheduled_report',
      resourceId: newScheduledReport.id,
      details: {
        name: newScheduledReport.name,
        templateId: newScheduledReport.templateId,
        frequency: newScheduledReport.schedule.frequency
      }
    });

    return newScheduledReport;
  }

  /**
   * Update a scheduled report
   */
  async updateScheduledReport(id, data, userId) {
    const scheduledReports = await this.loadData(this.scheduledReportsFile);
    const index = scheduledReports.findIndex(r => r.id === id);

    if (index === -1) {
      throw new NotFoundError(`Scheduled report with ID ${id} not found`);
    }

    const report = scheduledReports[index];

    // Check if user has permission to update
    if (report.createdBy !== userId) {
      throw new AuthorizationError('You do not have permission to update this scheduled report');
    }

    // If schedule is being updated, validate it
    if (data.schedule) {
      if (!data.schedule.frequency) {
        throw new ValidationError('Schedule frequency is required');
      }

      // Validate frequency
      const validFrequencies = ['daily', 'weekly', 'monthly', 'quarterly'];
      if (!validFrequencies.includes(data.schedule.frequency)) {
        throw new ValidationError(`Invalid frequency: ${data.schedule.frequency}. Valid frequencies: ${validFrequencies.join(', ')}`);
      }
    }

    // Update scheduled report
    const updatedReport = {
      ...report,
      ...data,
      id, // Don't allow changing the ID
      createdBy: report.createdBy, // Don't allow changing the creator
      created: report.created, // Don't allow changing the creation date
      updated: new Date().toISOString()
    };

    // If schedule was updated, recalculate next run date
    if (data.schedule) {
      updatedReport.nextRun = this.calculateNextRunDate(updatedReport.schedule);
    }

    scheduledReports[index] = updatedReport;
    await this.saveData(this.scheduledReportsFile, scheduledReports);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UPDATE',
      resourceType: 'scheduled_report',
      resourceId: id,
      details: {
        name: updatedReport.name,
        templateId: updatedReport.templateId,
        frequency: updatedReport.schedule.frequency
      }
    });

    return updatedReport;
  }

  /**
   * Delete a scheduled report
   */
  async deleteScheduledReport(id, userId) {
    const scheduledReports = await this.loadData(this.scheduledReportsFile);
    const index = scheduledReports.findIndex(r => r.id === id);

    if (index === -1) {
      throw new NotFoundError(`Scheduled report with ID ${id} not found`);
    }

    const report = scheduledReports[index];

    // Check if user has permission to delete
    if (report.createdBy !== userId) {
      throw new AuthorizationError('You do not have permission to delete this scheduled report');
    }

    // Remove the scheduled report
    scheduledReports.splice(index, 1);
    await this.saveData(this.scheduledReportsFile, scheduledReports);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'DELETE',
      resourceType: 'scheduled_report',
      resourceId: id,
      details: {
        name: report.name,
        templateId: report.templateId
      }
    });

    return { success: true, message: `Scheduled report ${id} deleted` };
  }

  /**
   * Calculate next run date based on schedule
   */
  calculateNextRunDate(schedule) {
    const now = new Date();
    let nextRun = new Date(now);

    switch (schedule.frequency) {
      case 'daily':
        // Set to next day at specified time or default to 1:00 AM
        nextRun.setDate(nextRun.getDate() + 1);
        nextRun.setHours(schedule.hour || 1, schedule.minute || 0, 0, 0);
        break;

      case 'weekly':
        // Set to next occurrence of specified day of week or default to Monday
        const dayOfWeek = schedule.dayOfWeek || 1; // 0 = Sunday, 1 = Monday, etc.
        const daysUntilNext = (dayOfWeek - now.getDay() + 7) % 7;
        nextRun.setDate(nextRun.getDate() + (daysUntilNext === 0 ? 7 : daysUntilNext));
        nextRun.setHours(schedule.hour || 1, schedule.minute || 0, 0, 0);
        break;

      case 'monthly':
        // Set to specified day of month or default to 1st
        nextRun.setMonth(nextRun.getMonth() + 1);
        nextRun.setDate(schedule.dayOfMonth || 1);
        nextRun.setHours(schedule.hour || 1, schedule.minute || 0, 0, 0);
        break;

      case 'quarterly':
        // Set to first day of next quarter
        const currentMonth = now.getMonth();
        const currentQuarter = Math.floor(currentMonth / 3);
        const nextQuarterStartMonth = (currentQuarter + 1) * 3 % 12; // 0, 3, 6, 9

        nextRun.setMonth(nextQuarterStartMonth);
        nextRun.setDate(schedule.dayOfMonth || 1);

        if (nextQuarterStartMonth < currentMonth) {
          // If we've wrapped around to the next year
          nextRun.setFullYear(nextRun.getFullYear() + 1);
        }

        nextRun.setHours(schedule.hour || 1, schedule.minute || 0, 0, 0);
        break;
    }

    return nextRun.toISOString();
  }

  /**
   * Run scheduled reports that are due
   */
  async runScheduledReports() {
    const scheduledReports = await this.loadData(this.scheduledReportsFile);
    const now = new Date();
    const reportsToRun = scheduledReports.filter(r =>
      r.status === 'active' &&
      r.nextRun &&
      new Date(r.nextRun) <= now
    );

    const results = [];

    for (const report of reportsToRun) {
      try {
        // Generate the report
        const generatedReport = await this.generateReport(
          report.templateId,
          report.parameters,
          report.createdBy
        );

        // Update the scheduled report
        const updatedReport = {
          ...report,
          lastRun: now.toISOString(),
          nextRun: this.calculateNextRunDate(report.schedule),
          lastRunStatus: 'success',
          lastRunReport: generatedReport.id
        };

        // Save the updated report
        const index = scheduledReports.findIndex(r => r.id === report.id);
        scheduledReports[index] = updatedReport;

        // In a real implementation, this would also handle sending the report to recipients
        // For now, just log that it would be sent

        results.push({
          id: report.id,
          name: report.name,
          status: 'success',
          reportId: generatedReport.id
        });
      } catch (error) {
        // Update the scheduled report with error status
        const updatedReport = {
          ...report,
          lastRun: now.toISOString(),
          nextRun: this.calculateNextRunDate(report.schedule),
          lastRunStatus: 'error',
          lastRunError: error.message
        };

        // Save the updated report
        const index = scheduledReports.findIndex(r => r.id === report.id);
        scheduledReports[index] = updatedReport;

        results.push({
          id: report.id,
          name: report.name,
          status: 'error',
          error: error.message
        });
      }
    }

    // Save all updated scheduled reports
    await this.saveData(this.scheduledReportsFile, scheduledReports);

    return {
      success: true,
      reportsRun: results.length,
      results
    };
  }
}

module.exports = ReportService;
