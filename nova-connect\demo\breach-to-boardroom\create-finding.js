/**
 * NovaConnect "Breach to Boardroom" Demo - Create Finding
 * 
 * This script manually creates a Security Command Center finding
 * for the "Breach to Boardroom" demo in case the automatic
 * detection fails.
 * 
 * Usage:
 *   node demo/breach-to-boardroom/create-finding.js
 * 
 * Environment Variables:
 *   GCP_PROJECT_ID - Google Cloud Project ID
 *   GCP_ORGANIZATION_ID - Google Cloud Organization ID
 *   DATASET_ID - BigQuery dataset ID (default: patient_records)
 */

const { SecurityCenterClient } = require('@google-cloud/security-center');

// Configuration
const config = {
  projectId: process.env.GCP_PROJECT_ID,
  organizationId: process.env.GCP_ORGANIZATION_ID,
  datasetId: process.env.DATASET_ID || 'patient_records',
  sourceId: process.env.SCC_SOURCE_ID || '12345678'
};

/**
 * Create a Security Command Center finding
 */
async function createFinding() {
  console.log('Creating Security Command Center finding...');
  
  try {
    // Validate configuration
    if (!config.projectId) {
      throw new Error('GCP_PROJECT_ID environment variable is required');
    }
    
    if (!config.organizationId) {
      throw new Error('GCP_ORGANIZATION_ID environment variable is required');
    }
    
    // Initialize SCC client
    const sccClient = new SecurityCenterClient();
    
    // Create finding
    const findingId = `phi-exposure-${Date.now()}`;
    const [finding] = await sccClient.createFinding({
      parent: `organizations/${config.organizationId}/sources/${config.sourceId}`,
      findingId,
      finding: {
        state: 'ACTIVE',
        category: 'DATA_LEAK',
        severity: 'HIGH',
        resourceName: `//bigquery.googleapis.com/projects/${config.projectId}/datasets/${config.datasetId}`,
        eventTime: { seconds: Math.floor(Date.now() / 1000) },
        sourceProperties: {
          finding_type: 'Sensitive Data Exposure',
          finding_description: 'PHI data exposed in BigQuery dataset',
          data_type: 'PHI',
          compliance_frameworks: 'HIPAA,GDPR'
        }
      }
    });
    
    console.log(`Created SCC finding: ${finding.name}`);
    console.log('Finding details:');
    console.log(`- Category: ${finding.category}`);
    console.log(`- Severity: ${finding.severity}`);
    console.log(`- Resource: ${finding.resourceName}`);
    console.log(`- State: ${finding.state}`);
    
    return finding;
  } catch (error) {
    console.error('Error creating SCC finding:', error);
    throw error;
  }
}

/**
 * Main function
 */
async function main() {
  console.log('Starting manual finding creation for "Breach to Boardroom" demo...');
  
  try {
    // Create finding
    const finding = await createFinding();
    
    console.log('\nFinding created successfully!');
    console.log('The NovaConnect system should now detect this finding and trigger the remediation workflow.');
    console.log('Watch the NovaConnect logs and the Looker dashboard for updates.');
  } catch (error) {
    console.error('\nFinding creation failed:', error);
    console.error('\nTroubleshooting steps:');
    console.error('1. Check that GCP_PROJECT_ID and GCP_ORGANIZATION_ID environment variables are set correctly');
    console.error('2. Verify that you have the necessary permissions to create findings');
    console.error('3. Check that the Security Command Center API is enabled');
    console.error('4. If all else fails, use the pre-recorded demo video');
    process.exit(1);
  }
}

// Run the main function
if (require.main === module) {
  main().catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

module.exports = { createFinding };
