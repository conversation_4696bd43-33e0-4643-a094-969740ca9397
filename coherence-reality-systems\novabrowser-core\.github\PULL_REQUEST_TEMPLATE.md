<!--
Before submitting a PR, please read https://github.com/tauri-apps/tauri/blob/dev/.github/CONTRIBUTING.md#pull-request-guidelines

1. Give the PR a descriptive title.

  Examples of good title:
    - fix(windows): fix race condition in navigation handler
    - docs: update docstrings
    - feat: add `Window::set_fullscreen`

  Examples of bad title:
    - fix #7123
    - update docs
    - fix bugs

2. If there is a related issue, reference it in the PR text, e.g. closes #123.
3. If this change requires a new version, then add a change file in `.changes` directory with the appropriate bump, see https://github.com/tauri-apps/wry/blob/dev/.changes/readme.md
4. Ensure that all your commits are signed https://docs.github.com/en/authentication/managing-commit-signature-verification/signing-commits
5. Ensure `cargo test` passes.
6. Open as a draft PR if your work is still in progress.
-->
