/**
 * Dashboard
 * 
 * This module implements the Dashboard component.
 * It provides real-time visualization of the Comphyology framework.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');
const NovaVisionIntegration = require('./nova-vision-integration');

/**
 * Dashboard class
 */
class Dashboard extends EventEmitter {
  /**
   * Create a new Dashboard instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      enableMetrics: true,
      updateInterval: 5000, // ms
      maxHistorySize: 100,
      novaVision: null, // NovaVision instance
      ...options
    };
    
    // Initialize state
    this.state = {
      isRunning: false,
      entropyHistory: [],
      riskHistory: [],
      activeAlerts: [],
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      updatesProcessed: 0,
      renderingTimeMs: 0,
      rendersPerformed: 0
    };
    
    // Initialize NovaVision integration
    this.novaVisionIntegration = new NovaVisionIntegration({
      enableLogging: this.options.enableLogging,
      enableMetrics: this.options.enableMetrics,
      novaVision: this.options.novaVision
    });
    
    if (this.options.enableLogging) {
      console.log('Dashboard initialized');
    }
  }
  
  /**
   * Start the dashboard
   * @returns {boolean} - Success status
   */
  async start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('Dashboard is already running');
      }
      return false;
    }
    
    try {
      // Connect to NovaVision
      if (!this.novaVisionIntegration.state.isConnected && this.options.novaVision) {
        this.novaVisionIntegration.connect(this.options.novaVision);
      }
      
      // Register visualization schemas
      if (!this.novaVisionIntegration.state.isRegistered) {
        await this.novaVisionIntegration.register();
      }
      
      // Start update interval
      this._startUpdateInterval();
      
      // Update state
      this.state.isRunning = true;
      this.state.lastUpdateTime = Date.now();
      
      if (this.options.enableLogging) {
        console.log('Dashboard started');
      }
      
      this.emit('start');
      
      return true;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('Failed to start dashboard:', error);
      }
      
      throw error;
    }
  }
  
  /**
   * Stop the dashboard
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('Dashboard is not running');
      }
      return false;
    }
    
    // Stop update interval
    this._stopUpdateInterval();
    
    // Update state
    this.state.isRunning = false;
    this.state.lastUpdateTime = Date.now();
    
    if (this.options.enableLogging) {
      console.log('Dashboard stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Update dashboard with new data
   * @param {Object} data - Dashboard data
   * @returns {boolean} - Success status
   */
  update(data) {
    const startTime = performance.now();
    
    if (!data) {
      throw new Error('Data is required');
    }
    
    // Update entropy history
    if (data.universalEntropy !== undefined) {
      this.state.entropyHistory.push({
        universalEntropy: data.universalEntropy,
        domainEntropy: data.domainEntropy || {},
        comphyonValue: data.comphyonValue || 0,
        timestamp: data.timestamp || Date.now()
      });
      
      // Limit history size
      if (this.state.entropyHistory.length > this.options.maxHistorySize) {
        this.state.entropyHistory.shift();
      }
    }
    
    // Update risk history
    if (data.unifiedRiskScore !== undefined) {
      this.state.riskHistory.push({
        unifiedRiskScore: data.unifiedRiskScore,
        domainRiskScores: data.domainRiskScores || {},
        riskStatus: data.riskStatus || 'low',
        timestamp: data.timestamp || Date.now()
      });
      
      // Limit history size
      if (this.state.riskHistory.length > this.options.maxHistorySize) {
        this.state.riskHistory.shift();
      }
    }
    
    // Update active alerts
    if (data.activeAlerts) {
      this.state.activeAlerts = data.activeAlerts;
    }
    
    // Update last update time
    this.state.lastUpdateTime = Date.now();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.updatesProcessed++;
    
    // Emit event
    this.emit('update', {
      timestamp: this.state.lastUpdateTime
    });
    
    if (this.options.enableLogging) {
      console.log('Dashboard updated');
    }
    
    return true;
  }
  
  /**
   * Render dashboard
   * @param {string} dashboardType - Dashboard type
   * @param {Object} target - Target element
   * @returns {boolean} - Success status
   */
  render(dashboardType, target) {
    const startTime = performance.now();
    
    if (!this.state.isRunning) {
      throw new Error('Dashboard is not running');
    }
    
    if (!target) {
      throw new Error('Target element is required');
    }
    
    // Generate dashboard data
    const dashboardData = this._generateDashboardData(dashboardType);
    
    // Generate dashboard schema
    let schemaId;
    
    switch (dashboardType) {
      case 'universal-entropy':
        this.novaVisionIntegration.generateUniversalEntropyDashboardSchema(dashboardData);
        schemaId = 'universal-entropy-dashboard';
        break;
      case 'cross-domain-risk':
        this.novaVisionIntegration.generateCrossDomainRiskDashboardSchema(dashboardData);
        schemaId = 'cross-domain-risk-dashboard';
        break;
      default:
        throw new Error(`Unknown dashboard type: ${dashboardType}`);
    }
    
    // Render dashboard
    this.novaVisionIntegration.render(schemaId, target);
    
    // Update metrics
    this.metrics.renderingTimeMs += performance.now() - startTime;
    this.metrics.rendersPerformed++;
    
    // Emit event
    this.emit('render', {
      dashboardType,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`Dashboard rendered: ${dashboardType}`);
    }
    
    return true;
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      novaVisionIntegration: this.novaVisionIntegration.getMetrics()
    };
  }
  
  /**
   * Start update interval
   * @private
   */
  _startUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
    }
    
    this._updateInterval = setInterval(() => {
      this.emit('update-interval');
    }, this.options.updateInterval);
  }
  
  /**
   * Stop update interval
   * @private
   */
  _stopUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
      this._updateInterval = null;
    }
  }
  
  /**
   * Generate dashboard data
   * @param {string} dashboardType - Dashboard type
   * @returns {Object} - Dashboard data
   * @private
   */
  _generateDashboardData(dashboardType) {
    switch (dashboardType) {
      case 'universal-entropy':
        return this._generateUniversalEntropyDashboardData();
      case 'cross-domain-risk':
        return this._generateCrossDomainRiskDashboardData();
      default:
        throw new Error(`Unknown dashboard type: ${dashboardType}`);
    }
  }
  
  /**
   * Generate universal entropy dashboard data
   * @returns {Object} - Dashboard data
   * @private
   */
  _generateUniversalEntropyDashboardData() {
    // Get latest entropy data
    const latestEntropy = this.state.entropyHistory.length > 0
      ? this.state.entropyHistory[this.state.entropyHistory.length - 1]
      : {
          universalEntropy: 0,
          domainEntropy: {
            cyber: { overallEntropy: 0, policyEntropy: 0, auditEntropy: 0, regulatoryEntropy: 0 },
            financial: { overallEntropy: 0, transactionEntropy: 0, attackSurfaceCoherence: 0, marketStress: 0 },
            biological: { overallEntropy: 0, telomereLength: 0, mtorActivation: 0, inflammationLevel: 0 }
          },
          comphyonValue: 0,
          timestamp: Date.now()
        };
    
    // Calculate Comphyon trend
    const comphyonTrend = this.state.entropyHistory.length > 1
      ? latestEntropy.comphyonValue - this.state.entropyHistory[this.state.entropyHistory.length - 2].comphyonValue
      : 0;
    
    return {
      universalEntropy: latestEntropy.universalEntropy,
      domainEntropy: latestEntropy.domainEntropy,
      comphyonValue: latestEntropy.comphyonValue,
      comphyonTrend,
      entropyHistory: this.state.entropyHistory,
      activeAlerts: this.state.activeAlerts,
      timestamp: latestEntropy.timestamp
    };
  }
  
  /**
   * Generate cross-domain risk dashboard data
   * @returns {Object} - Dashboard data
   * @private
   */
  _generateCrossDomainRiskDashboardData() {
    // Get latest risk data
    const latestRisk = this.state.riskHistory.length > 0
      ? this.state.riskHistory[this.state.riskHistory.length - 1]
      : {
          unifiedRiskScore: 0,
          domainRiskScores: {
            cyber: 0,
            financial: 0,
            biological: 0
          },
          riskStatus: 'low',
          timestamp: Date.now()
        };
    
    // Generate domain correlations
    const domainCorrelations = {
      cyberFinancial: 0.7,
      cyberBiological: 0.5,
      financialBiological: 0.6
    };
    
    // Generate top risk factors
    const topRiskFactors = [
      { domain: 'Cyber', type: 'Policy Entropy', impact: 0.8, likelihood: 0.7 },
      { domain: 'Financial', type: 'Transaction Entropy', impact: 0.7, likelihood: 0.6 },
      { domain: 'Biological', type: 'Inflammation Level', impact: 0.6, likelihood: 0.5 },
      { domain: 'Cyber', type: 'Regulatory Entropy', impact: 0.5, likelihood: 0.8 },
      { domain: 'Financial', type: 'Market Stress', impact: 0.4, likelihood: 0.9 }
    ];
    
    return {
      unifiedRiskScore: latestRisk.unifiedRiskScore,
      domainRiskScores: latestRisk.domainRiskScores,
      riskStatus: latestRisk.riskStatus,
      riskHistory: this.state.riskHistory,
      domainCorrelations,
      topRiskFactors,
      timestamp: latestRisk.timestamp
    };
  }
}

module.exports = Dashboard;
