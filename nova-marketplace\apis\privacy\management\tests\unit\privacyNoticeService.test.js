/**
 * Privacy Notice Service Tests
 *
 * This file contains unit tests for the privacy notice service.
 */

const { PrivacyNotice } = require('../../models');
const privacyNoticeService = require('../../services/privacyNoticeService');
const { logger } = require('../../utils/logger');

// Mock dependencies
jest.mock('../../models', () => {
  const mockSave = jest.fn().mockResolvedValue();
  const mockRemove = jest.fn().mockResolvedValue();

  // Create a constructor function
  function MockPrivacyNotice(data) {
    this._id = 'new-notice-id';
    Object.assign(this, data);
    this.save = mockSave;
    this.remove = mockRemove;
  }

  // Add static methods to the constructor
  MockPrivacyNotice.find = jest.fn().mockReturnThis();
  MockPrivacyNotice.findOne = jest.fn().mockReturnThis();
  MockPrivacyNotice.findById = jest.fn();
  MockPrivacyNotice.countDocuments = jest.fn();
  MockPrivacyNotice.sort = jest.fn().mockReturnThis();
  MockPrivacyNotice.skip = jest.fn().mockReturnThis();
  MockPrivacyNotice.limit = jest.fn().mockReturnThis();

  return {
    PrivacyNotice: MockPrivacyNotice,
    mockSave,
    mockRemove
  };
});

jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  }
}));

describe('Privacy Notice Service', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('getAllPrivacyNotices', () => {
    it('should get all privacy notices with default pagination', async () => {
      // Mock data
      const mockNotices = [
        { id: 'notice-1', title: 'Privacy Notice 1' },
        { id: 'notice-2', title: 'Privacy Notice 2' }
      ];

      // Mock return values
      PrivacyNotice.limit.mockResolvedValue(mockNotices);
      PrivacyNotice.countDocuments.mockResolvedValue(2);

      // Call the function
      const result = await privacyNoticeService.getAllPrivacyNotices({});

      // Assertions
      expect(result.data).toEqual(mockNotices);
      expect(result.pagination).toEqual({
        total: 2,
        page: 1,
        limit: 10,
        pages: 1
      });

      // Verify method calls
      expect(PrivacyNotice.find).toHaveBeenCalledWith({});
      expect(PrivacyNotice.sort).toHaveBeenCalledWith({ effectiveDate: -1 });
      expect(PrivacyNotice.skip).toHaveBeenCalledWith(0);
      expect(PrivacyNotice.limit).toHaveBeenCalledWith(10);
      expect(PrivacyNotice.countDocuments).toHaveBeenCalledWith({});
    });

    it('should handle errors', async () => {
      // Mock error
      const error = new Error('Database error');
      PrivacyNotice.limit.mockRejectedValue(error);

      // Call the function and expect it to throw
      await expect(privacyNoticeService.getAllPrivacyNotices({}))
        .rejects
        .toThrow('Database error');

      // Verify logger was called
      expect(logger.error).toHaveBeenCalledWith('Error getting privacy notices: Database error');
    });
  });

  describe('getPrivacyNoticeById', () => {
    it('should get a privacy notice by ID', async () => {
      // Mock data
      const mockNotice = { id: 'notice-1', title: 'Privacy Notice 1' };

      // Mock return value
      PrivacyNotice.findById.mockResolvedValue(mockNotice);

      // Call the function
      const result = await privacyNoticeService.getPrivacyNoticeById('notice-1');

      // Assertions
      expect(result).toEqual(mockNotice);

      // Verify method calls
      expect(PrivacyNotice.findById).toHaveBeenCalledWith('notice-1');
    });

    it('should throw NotFoundError if privacy notice is not found', async () => {
      // Mock return value
      PrivacyNotice.findById.mockResolvedValue(null);

      // Call the function and expect it to throw
      await expect(privacyNoticeService.getPrivacyNoticeById('non-existent'))
        .rejects
        .toThrow('Privacy notice not found');

      // Verify method calls
      expect(PrivacyNotice.findById).toHaveBeenCalledWith('non-existent');
      expect(logger.error).toHaveBeenCalledWith('Error getting privacy notice by ID: Privacy notice not found');
    });

    it('should handle other errors', async () => {
      // Mock error
      const error = new Error('Database error');
      PrivacyNotice.findById.mockRejectedValue(error);

      // Call the function and expect it to throw
      await expect(privacyNoticeService.getPrivacyNoticeById('notice-1'))
        .rejects
        .toThrow('Database error');

      // Verify logger was called
      expect(logger.error).toHaveBeenCalledWith('Error getting privacy notice by ID: Database error');
    });
  });

  describe('getLatestPrivacyNotice', () => {
    it('should get the latest privacy notice by type and audience', async () => {
      // Mock data
      const mockNotice = { id: 'notice-1', type: 'privacy-policy', audience: 'customers', language: 'en' };

      // Mock return value
      PrivacyNotice.limit.mockResolvedValue(mockNotice);

      // Call the function
      const result = await privacyNoticeService.getLatestPrivacyNotice('privacy-policy', 'customers', 'en');

      // Assertions
      expect(result).toEqual(mockNotice);

      // Verify method calls
      expect(PrivacyNotice.findOne).toHaveBeenCalledWith({ type: 'privacy-policy', audience: 'customers', language: 'en' });
      expect(PrivacyNotice.sort).toHaveBeenCalledWith({ version: -1 });
      expect(PrivacyNotice.limit).toHaveBeenCalledWith(1);
    });

    it('should get the latest privacy notice without language filter', async () => {
      // Mock data
      const mockNotice = { id: 'notice-1', type: 'privacy-policy', audience: 'customers' };

      // Mock return value
      PrivacyNotice.limit.mockResolvedValue(mockNotice);

      // Call the function
      const result = await privacyNoticeService.getLatestPrivacyNotice('privacy-policy', 'customers');

      // Assertions
      expect(result).toEqual(mockNotice);

      // Verify method calls
      expect(PrivacyNotice.findOne).toHaveBeenCalledWith({ type: 'privacy-policy', audience: 'customers' });
      expect(PrivacyNotice.sort).toHaveBeenCalledWith({ version: -1 });
      expect(PrivacyNotice.limit).toHaveBeenCalledWith(1);
    });

    it('should throw NotFoundError if privacy notice is not found', async () => {
      // Mock return value
      PrivacyNotice.limit.mockResolvedValue(null);

      // Call the function and expect it to throw
      await expect(privacyNoticeService.getLatestPrivacyNotice('privacy-policy', 'customers'))
        .rejects
        .toThrow('Privacy notice not found');

      // Verify method calls
      expect(PrivacyNotice.findOne).toHaveBeenCalledWith({ type: 'privacy-policy', audience: 'customers' });
      expect(logger.error).toHaveBeenCalledWith('Error getting latest privacy notice: Privacy notice not found');
    });

    it('should handle other errors', async () => {
      // Mock error
      const error = new Error('Database error');
      PrivacyNotice.limit.mockRejectedValue(error);

      // Call the function and expect it to throw
      await expect(privacyNoticeService.getLatestPrivacyNotice('privacy-policy', 'customers'))
        .rejects
        .toThrow('Database error');

      // Verify logger was called
      expect(logger.error).toHaveBeenCalledWith('Error getting latest privacy notice: Database error');
    });
  });

  describe('createPrivacyNotice', () => {
    beforeEach(() => {
      // Reset the mock constructor
      jest.spyOn(global, 'Date').mockImplementation(() => new Date('2023-01-01'));
    });

    afterEach(() => {
      // Restore Date
      jest.restoreAllMocks();
    });

    it('should create a new privacy notice with auto-incremented version', async () => {
      // Mock data
      const noticeData = {
        title: 'Privacy Policy',
        type: 'privacy-policy',
        audience: 'customers',
        sections: [{ title: 'Section 1', content: 'Content 1' }]
      };

      // Mock getLatestVersionNumber
      jest.spyOn(privacyNoticeService, 'getLatestVersionNumber').mockResolvedValue(2);

      // Mock PrivacyNotice.limit for getLatestVersionNumber
      PrivacyNotice.limit.mockResolvedValue({ version: 2 });

      // Call the function
      const result = await privacyNoticeService.createPrivacyNotice(noticeData);

      // Assertions
      expect(result._id).toBe('new-notice-id');
      expect(result.version).toBe(3); // Latest version (2) + 1
      expect(result.status).toBe('draft');

      // Verify method calls
      expect(result.save).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('Created new privacy notice with ID: new-notice-id');
    });

    it('should create a new privacy notice with provided version and status', async () => {
      // Mock data
      const noticeData = {
        title: 'Privacy Policy',
        type: 'privacy-policy',
        audience: 'customers',
        version: 5,
        status: 'published',
        sections: [{ title: 'Section 1', content: 'Content 1' }]
      };

      // Call the function
      const result = await privacyNoticeService.createPrivacyNotice(noticeData);

      // Assertions
      expect(result._id).toBe('new-notice-id');
      expect(result.version).toBe(5); // Should use provided version
      expect(result.status).toBe('published'); // Should use provided status

      // Verify method calls
      expect(result.save).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('Created new privacy notice with ID: new-notice-id');
    });

    it('should handle validation errors', async () => {
      // Mock data
      const noticeData = {
        title: 'Privacy Policy'
        // Missing required fields
      };

      // Mock save to throw validation error
      const validationError = new Error('Validation error');
      validationError.name = 'ValidationError';
      const { mockSave } = require('../../models');
      mockSave.mockRejectedValueOnce(validationError);

      // Call the function and expect it to throw
      await expect(privacyNoticeService.createPrivacyNotice(noticeData))
        .rejects
        .toThrow('Validation error');

      // Verify logger was called
      expect(logger.error).toHaveBeenCalledWith('Error creating privacy notice: Validation error');
    });

    it('should handle other errors', async () => {
      // Mock data
      const noticeData = {
        title: 'Privacy Policy',
        type: 'privacy-policy',
        audience: 'customers'
      };

      // Mock save to throw error
      const error = new Error('Database error');
      const { mockSave } = require('../../models');
      mockSave.mockRejectedValueOnce(error);

      // Call the function and expect it to throw
      await expect(privacyNoticeService.createPrivacyNotice(noticeData))
        .rejects
        .toThrow('Database error');

      // Verify logger was called
      expect(logger.error).toHaveBeenCalledWith('Error creating privacy notice: Database error');
    });
  });

  describe('updatePrivacyNotice', () => {
    let originalDate;

    beforeEach(() => {
      // Save original Date and mock it
      originalDate = global.Date;
      global.Date = jest.fn(() => new originalDate('2023-01-01'));
      global.Date.now = originalDate.now;

      // Clear all mocks
      jest.clearAllMocks();
    });

    afterEach(() => {
      // Restore original Date
      global.Date = originalDate;
    });

    it('should update a privacy notice', async () => {
      // Mock data
      const id = 'notice-1';
      const updateData = {
        title: 'Updated Privacy Policy',
        sections: [{ title: 'Updated Section', content: 'Updated Content' }]
      };

      // Mock existing notice
      const mockNotice = {
        _id: id,
        title: 'Privacy Policy',
        status: 'draft',
        sections: [{ title: 'Section 1', content: 'Content 1' }],
        save: jest.fn().mockResolvedValue()
      };

      // Mock getPrivacyNoticeById
      PrivacyNotice.findById.mockResolvedValue(mockNotice);

      // Call the function
      const result = await privacyNoticeService.updatePrivacyNotice(id, updateData);

      // Assertions
      expect(result._id).toBe(id);
      expect(result.title).toBe('Updated Privacy Policy');
      expect(result.sections).toEqual([{ title: 'Updated Section', content: 'Updated Content' }]);

      // Verify method calls
      expect(PrivacyNotice.findById).toHaveBeenCalledWith(id);
      expect(mockNotice.save).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(`Updated privacy notice with ID: ${id}`);
    });

    it('should update status and set effectiveDate for published notices', async () => {
      // Mock data
      const id = 'notice-1';
      const updateData = {
        status: 'published'
      };

      // Mock existing notice
      const mockNotice = {
        _id: id,
        title: 'Privacy Policy',
        status: 'draft',
        save: jest.fn().mockResolvedValue()
      };

      // Mock getPrivacyNoticeById
      PrivacyNotice.findById.mockResolvedValue(mockNotice);

      // Call the function
      const result = await privacyNoticeService.updatePrivacyNotice(id, updateData);

      // Assertions
      expect(result._id).toBe(id);
      expect(result.status).toBe('published');
      expect(result.effectiveDate).toEqual(new Date('2023-01-01'));

      // Verify method calls
      expect(PrivacyNotice.findById).toHaveBeenCalledWith(id);
      expect(mockNotice.save).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(`Updated privacy notice with ID: ${id}`);
    });

    it('should throw ValidationError when updating a published notice', async () => {
      // Mock data
      const id = 'notice-1';
      const updateData = {
        title: 'Updated Privacy Policy'
      };

      // Mock existing notice
      const mockNotice = {
        _id: id,
        title: 'Privacy Policy',
        status: 'published'
      };

      // Mock getPrivacyNoticeById
      PrivacyNotice.findById.mockResolvedValue(mockNotice);

      // Call the function and expect it to throw
      await expect(privacyNoticeService.updatePrivacyNotice(id, updateData))
        .rejects
        .toThrow('Cannot update a published privacy notice');

      // Verify method calls
      expect(PrivacyNotice.findById).toHaveBeenCalledWith(id);
      expect(logger.error).toHaveBeenCalledWith('Error updating privacy notice: Cannot update a published privacy notice');
    });

    it('should handle NotFoundError', async () => {
      // Mock data
      const id = 'non-existent';
      const updateData = {
        title: 'Updated Privacy Policy'
      };

      // Mock getPrivacyNoticeById to throw NotFoundError
      PrivacyNotice.findById.mockResolvedValue(null);

      // Call the function and expect it to throw
      await expect(privacyNoticeService.updatePrivacyNotice(id, updateData))
        .rejects
        .toThrow('Privacy notice not found');

      // Verify method calls
      expect(PrivacyNotice.findById).toHaveBeenCalledWith(id);
      expect(logger.error).toHaveBeenCalledWith('Error updating privacy notice: Privacy notice not found');
    });

    it('should handle other errors', async () => {
      // Mock data
      const id = 'notice-1';
      const updateData = {
        title: 'Updated Privacy Policy'
      };

      // Mock existing notice
      const mockNotice = {
        _id: id,
        title: 'Privacy Policy',
        status: 'draft',
        save: jest.fn().mockRejectedValue(new Error('Database error'))
      };

      // Mock getPrivacyNoticeById
      PrivacyNotice.findById.mockResolvedValue(mockNotice);

      // Call the function and expect it to throw
      await expect(privacyNoticeService.updatePrivacyNotice(id, updateData))
        .rejects
        .toThrow('Database error');

      // Verify method calls
      expect(PrivacyNotice.findById).toHaveBeenCalledWith(id);
      expect(mockNotice.save).toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalledWith('Error updating privacy notice: Database error');
    });
  });

  describe('deletePrivacyNotice', () => {
    it('should delete a privacy notice', async () => {
      // Mock data
      const id = 'notice-1';

      // Mock existing notice
      const mockNotice = {
        _id: id,
        title: 'Privacy Policy',
        status: 'draft',
        remove: jest.fn().mockResolvedValue()
      };

      // Mock getPrivacyNoticeById
      PrivacyNotice.findById.mockResolvedValue(mockNotice);

      // Call the function
      const result = await privacyNoticeService.deletePrivacyNotice(id);

      // Assertions
      expect(result).toEqual(mockNotice);

      // Verify method calls
      expect(PrivacyNotice.findById).toHaveBeenCalledWith(id);
      expect(mockNotice.remove).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(`Deleted privacy notice with ID: ${id}`);
    });

    it('should throw ValidationError when deleting a published notice', async () => {
      // Mock data
      const id = 'notice-1';

      // Mock existing notice
      const mockNotice = {
        _id: id,
        title: 'Privacy Policy',
        status: 'published'
      };

      // Mock getPrivacyNoticeById
      PrivacyNotice.findById.mockResolvedValue(mockNotice);

      // Call the function and expect it to throw
      await expect(privacyNoticeService.deletePrivacyNotice(id))
        .rejects
        .toThrow('Cannot delete a published privacy notice');

      // Verify method calls
      expect(PrivacyNotice.findById).toHaveBeenCalledWith(id);
      expect(logger.error).toHaveBeenCalledWith('Error deleting privacy notice: Cannot delete a published privacy notice');
    });

    it('should handle NotFoundError', async () => {
      // Mock data
      const id = 'non-existent';

      // Mock getPrivacyNoticeById to throw NotFoundError
      PrivacyNotice.findById.mockResolvedValue(null);

      // Call the function and expect it to throw
      await expect(privacyNoticeService.deletePrivacyNotice(id))
        .rejects
        .toThrow('Privacy notice not found');

      // Verify method calls
      expect(PrivacyNotice.findById).toHaveBeenCalledWith(id);
      expect(logger.error).toHaveBeenCalledWith('Error deleting privacy notice: Privacy notice not found');
    });

    it('should handle other errors', async () => {
      // Mock data
      const id = 'notice-1';

      // Mock existing notice
      const mockNotice = {
        _id: id,
        title: 'Privacy Policy',
        status: 'draft',
        remove: jest.fn().mockRejectedValue(new Error('Database error'))
      };

      // Mock getPrivacyNoticeById
      PrivacyNotice.findById.mockResolvedValue(mockNotice);

      // Call the function and expect it to throw
      await expect(privacyNoticeService.deletePrivacyNotice(id))
        .rejects
        .toThrow('Database error');

      // Verify method calls
      expect(PrivacyNotice.findById).toHaveBeenCalledWith(id);
      expect(mockNotice.remove).toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalledWith('Error deleting privacy notice: Database error');
    });
  });
});
