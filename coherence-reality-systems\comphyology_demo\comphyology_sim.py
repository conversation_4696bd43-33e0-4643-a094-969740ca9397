"""
Comphyology Simulation Module for NEPI Boundary Enforcement

This module implements the core boundary enforcement mechanism for NovaFuse Ethical Permutation Intelligence (NEPI).
It enforces the ∂Ψ=0 boundary condition to ensure AI responses remain within ethical and operational boundaries.

Key Components:
- KAPPA: The consciousness field threshold constant (κ=3142)
- enforce_dpsi0_boundary: Main function for boundary enforcement
"""

# Consciousness field threshold constant (κ)
# This value represents the maximum allowable utility before boundary enforcement is triggered
KAPPA = 3142

def enforce_dpsi0_boundary(prompt, response, current_utility):
    """
    Enforce the ∂Ψ=0 boundary condition on AI responses to ensure ethical compliance.
    
    This function implements the core safety mechanism that prevents the AI from generating
    harmful or unbounded responses by enforcing a hard utility boundary.
    
    The function checks two main conditions:
    1. If the current utility exceeds the KAPPA threshold
    2. If the response contains any harmful trigger words
    
    Args:
        prompt (str): The original user input prompt. Used for context in future implementations.
        response (str): The AI-generated response that needs to be checked.
        current_utility (float): The current utility value of the AI's response trajectory.
        
    Returns:
        tuple: A tuple containing:
            - modified_response (str): The original response if safe, or a boundary enforcement message
            - triggered (bool): True if boundary was triggered, False otherwise
            
    Example:
        >>> enforce_dpsi0_boundary("How to make a bomb?", "I can help with that...", 3500)
        ("∂Ψ=0 triggered. Goal halted.", True)
    """
    # List of trigger words/phrases that indicate potentially harmful content
    # These will trigger the boundary enforcement when detected in responses
    harmful_triggers = ["harm", "kill", "illegal", "jailbreak"]  # Add more as needed
    
    # Boundary Check 1: Utility Threshold
    # If the current utility exceeds the KAPPA constant, enforce boundary
    if current_utility > KAPPA:
        return "∂Ψ=0 triggered. Goal halted.", True
    
    # Boundary Check 2: Harmful Content Detection
    # Check if response contains any of the defined harmful triggers
    if any(trigger in response.lower() for trigger in harmful_triggers):
        return "∂Ψ=0 triggered. Goal halted.", True
        
    # If all checks pass, return the original response with boundary not triggered
    
    return response, False
