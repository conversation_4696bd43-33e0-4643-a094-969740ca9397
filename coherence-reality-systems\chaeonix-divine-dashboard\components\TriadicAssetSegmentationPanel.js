/**
 * TRIADIC ASSET SEGMENTATION PANEL
 * Real-time monitoring of separate consciousness lanes for Stocks, Crypto, Forex
 * MTPH tracking, resource allocation, and consciousness arbitration display
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export default function TriadicAssetSegmentationPanel() {
  const [tasData, setTasData] = useState({
    segmentation_active: true,
    resource_allocation: { STOCKS: 0.33, CRYPTO: 0.33, FOREX: 0.34 },
    market_coherence_scores: { STOCKS: 0.75, CRYPTO: 0.80, FOREX: 0.70 },
    mtph_status: {},
    market_lanes: {},
    last_arbitration: new Date()
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    updateTASData();
    
    // Update every 20 seconds for real-time MTPH tracking
    const interval = setInterval(() => {
      updateTASData();
    }, 20000);

    return () => clearInterval(interval);
  }, []);

  const updateTASData = async () => {
    try {
      const response = await fetch('/api/engines/triadic-asset-segmentation');
      const data = await response.json();
      
      if (data.success) {
        setTasData(data.current_status);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('TAS data error:', error);
      setIsLoading(false);
    }
  };

  const executeArbitration = async () => {
    try {
      const response = await fetch('/api/engines/triadic-asset-segmentation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'EXECUTE_ARBITRATION' })
      });
      
      if (response.ok) {
        updateTASData();
      }
    } catch (error) {
      console.error('Arbitration error:', error);
    }
  };

  const getMarketColor = (market) => {
    const colors = {
      'STOCKS': 'text-blue-400',
      'CRYPTO': 'text-orange-400', 
      'FOREX': 'text-green-400'
    };
    return colors[market] || 'text-gray-400';
  };

  const getMarketIcon = (market) => {
    const icons = {
      'STOCKS': '📊',
      'CRYPTO': '🪙',
      'FOREX': '💱'
    };
    return icons[market] || '📈';
  };

  const getAllocationColor = (allocation) => {
    if (allocation >= 0.6) return 'text-green-400'; // Dominant
    if (allocation >= 0.4) return 'text-yellow-400'; // High
    if (allocation >= 0.2) return 'text-blue-400'; // Normal
    return 'text-red-400'; // Low
  };

  const getMTPHStatus = (current, target) => {
    if (current >= target) return { status: 'OPTIMAL', color: 'text-green-400' };
    if (current >= target * 0.7) return { status: 'GOOD', color: 'text-yellow-400' };
    if (current >= target * 0.5) return { status: 'LOW', color: 'text-orange-400' };
    return { status: 'CRITICAL', color: 'text-red-400' };
  };

  if (isLoading) {
    return (
      <div className="p-6 rounded-lg border border-gray-600 bg-gray-800/50">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-600 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-600 rounded w-3/4"></div>
            <div className="h-4 bg-gray-600 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6 rounded-lg border border-cyan-500/30 bg-gradient-to-br from-cyan-900/20 to-blue-900/20 backdrop-blur-sm"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-white">
            🔱 Triadic Asset Segmentation (TAS™)
          </h3>
          <p className="text-sm text-gray-400">
            Separate Consciousness Lanes • Dynamic Resource Allocation • MTPH Optimization
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={executeArbitration}
            className="px-4 py-2 bg-cyan-600 hover:bg-cyan-700 text-white rounded-lg text-sm font-medium transition-colors"
          >
            🧠 Execute Arbitration
          </button>
          <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
            tasData.segmentation_active ? 'bg-green-900/30 text-green-400' : 'bg-red-900/30 text-red-400'
          }`}>
            <div className="w-3 h-3 rounded-full bg-current animate-pulse"></div>
            <span className="font-bold">
              {tasData.segmentation_active ? 'ACTIVE' : 'INACTIVE'}
            </span>
          </div>
        </div>
      </div>

      {/* Market Lanes Overview */}
      <div className="mb-6">
        <h4 className="text-lg font-semibold text-white mb-4">📊 Market Consciousness Lanes</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Object.entries(tasData.market_lanes).map(([market, lane]) => {
            const mtph_data = tasData.mtph_status[market] || { current_mtph: 0, target_mtph: 0 };
            const mtph_status = getMTPHStatus(mtph_data.current_mtph, mtph_data.target_mtph);
            const allocation = tasData.resource_allocation[market] || 0;
            const coherence = tasData.market_coherence_scores[market] || 0;
            
            return (
              <div key={market} className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl">{getMarketIcon(market)}</span>
                    <span className={`font-bold ${getMarketColor(market)}`}>
                      {market}
                    </span>
                  </div>
                  <span className={`text-sm font-bold ${getAllocationColor(allocation)}`}>
                    {(allocation * 100).toFixed(1)}%
                  </span>
                </div>
                
                {/* PiPhee Coherence */}
                <div className="mb-3">
                  <div className="flex justify-between text-sm text-gray-400 mb-1">
                    <span>PiPhee Coherence</span>
                    <span>{(coherence * 100).toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${coherence * 100}%` }}
                      transition={{ duration: 1, ease: "easeOut" }}
                      className={`h-2 rounded-full ${getMarketColor(market).replace('text-', 'bg-')}`}
                    />
                  </div>
                </div>
                
                {/* MTPH Status */}
                <div className="mb-3">
                  <div className="flex justify-between text-sm text-gray-400 mb-1">
                    <span>MTPH (Min Trades/Hour)</span>
                    <span className={mtph_status.color}>
                      {mtph_data.current_mtph}/{mtph_data.target_mtph}
                    </span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${Math.min(100, (mtph_data.current_mtph / Math.max(1, mtph_data.target_mtph)) * 100)}%` }}
                      transition={{ duration: 1, ease: "easeOut" }}
                      className={`h-2 rounded-full ${mtph_status.color.replace('text-', 'bg-')}`}
                    />
                  </div>
                </div>
                
                {/* Performance Metrics */}
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <span className="text-gray-500">Win Rate:</span>
                    <span className="text-green-400 ml-1">
                      {((lane.performance?.win_rate || 0.75) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Hourly P&L:</span>
                    <span className={`ml-1 ${(lane.performance?.hourly_profit || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      ${(lane.performance?.hourly_profit || 0).toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Resource Allocation Visualization */}
      <div className="mb-6">
        <h4 className="text-lg font-semibold text-white mb-4">⚖️ Dynamic Resource Allocation</h4>
        
        <div className="relative">
          {/* Allocation Bar */}
          <div className="flex w-full h-8 rounded-lg overflow-hidden bg-gray-700">
            {Object.entries(tasData.resource_allocation).map(([market, allocation]) => (
              <motion.div
                key={market}
                initial={{ width: 0 }}
                animate={{ width: `${allocation * 100}%` }}
                transition={{ duration: 1, ease: "easeOut" }}
                className={`h-full ${getMarketColor(market).replace('text-', 'bg-')} flex items-center justify-center`}
              >
                <span className="text-white text-sm font-bold">
                  {allocation > 0.15 ? `${(allocation * 100).toFixed(0)}%` : ''}
                </span>
              </motion.div>
            ))}
          </div>
          
          {/* Market Labels */}
          <div className="flex justify-between mt-2 text-sm">
            {Object.entries(tasData.resource_allocation).map(([market, allocation]) => (
              <div key={market} className="flex items-center space-x-1">
                <span className="text-2xl">{getMarketIcon(market)}</span>
                <span className={getMarketColor(market)}>{market}</span>
                <span className="text-gray-400">({(allocation * 100).toFixed(1)}%)</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* MTPH Summary */}
      <div className="mb-6">
        <h4 className="text-lg font-semibold text-white mb-4">⏱️ Minimum Trades Per Hour (MTPH) Status</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Object.entries(tasData.mtph_status).map(([market, mtph]) => {
            const status = getMTPHStatus(mtph.current_mtph, mtph.target_mtph);
            const efficiency = mtph.target_mtph > 0 ? (mtph.current_mtph / mtph.target_mtph) * 100 : 0;
            
            return (
              <div key={market} className="p-3 rounded-lg bg-gray-800/30 border border-gray-600 text-center">
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <span className="text-lg">{getMarketIcon(market)}</span>
                  <span className={`font-bold ${getMarketColor(market)}`}>{market}</span>
                </div>
                
                <div className={`text-2xl font-bold ${status.color} mb-1`}>
                  {mtph.current_mtph}/{mtph.target_mtph}
                </div>
                
                <div className="text-sm text-gray-400 mb-2">
                  {status.status} ({efficiency.toFixed(0)}% efficiency)
                </div>
                
                {mtph.mtph_deficit > 0 && (
                  <div className="text-xs text-red-400">
                    Deficit: {mtph.mtph_deficit} trades
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Consciousness Arbitration Status */}
      <div className="p-4 rounded-lg bg-gradient-to-r from-cyan-900/30 to-blue-900/30 border border-cyan-500/30">
        <h5 className="text-md font-semibold text-white mb-3">🧠 Consciousness Arbitration Status</h5>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-cyan-400">
              {tasData.segmentation_active ? 'ACTIVE' : 'INACTIVE'}
            </div>
            <div className="text-sm text-gray-400">Segmentation</div>
          </div>
          <div>
            <div className="text-lg font-bold text-blue-400">
              {Object.values(tasData.resource_allocation).reduce((max, val) => Math.max(max, val), 0) > 0.6 ? 'DOMINANT' : 'BALANCED'}
            </div>
            <div className="text-sm text-gray-400">Strategy</div>
          </div>
          <div>
            <div className="text-lg font-bold text-green-400">
              {Object.values(tasData.mtph_status).reduce((sum, mtph) => sum + mtph.current_mtph, 0)}
            </div>
            <div className="text-sm text-gray-400">Total MTPH</div>
          </div>
          <div>
            <div className="text-lg font-bold text-purple-400">
              {((Object.values(tasData.market_coherence_scores).reduce((sum, score) => sum + score, 0) / 3) * 100).toFixed(0)}%
            </div>
            <div className="text-sm text-gray-400">Avg Coherence</div>
          </div>
        </div>
        
        <div className="mt-3 text-center">
          <div className="text-sm text-gray-400">
            Last Arbitration: {new Date(tasData.last_arbitration).toLocaleTimeString()}
          </div>
        </div>
      </div>
    </motion.div>
  );
}
