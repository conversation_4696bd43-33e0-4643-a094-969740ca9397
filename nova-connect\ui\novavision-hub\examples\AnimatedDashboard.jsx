/**
 * Animated Dashboard Example
 * 
 * This example demonstrates how to use the animation system.
 */

import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  DashboardCard,
  Animated,
  Transition,
  TransitionGroup,
  SkipLink,
  ThemeSelector
} from '../components';
import { ThemeProvider } from '../theme';
import { PreferencesProvider } from '../preferences';
import { OfflineProvider } from '../offline';
import { AnimationProvider, useAnimationContext } from '../animation';

/**
 * Animated Dashboard Content component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Animated Dashboard Content component
 */
const AnimatedDashboardContent = ({
  novaConnect,
  novaShield,
  novaTrack,
  enableLogging = false
}) => {
  const { enabled, reducedMotion, setEnabled } = useAnimationContext();
  
  // State
  const [cards, setCards] = useState([
    { id: 'card-1', title: 'Compliance Overview', content: 'Compliance status across all frameworks' },
    { id: 'card-2', title: 'Security Metrics', content: 'Security metrics and alerts' },
    { id: 'card-3', title: 'Identity Management', content: 'User identity and access management' }
  ]);
  const [selectedAnimation, setSelectedAnimation] = useState('fadeIn');
  const [selectedCard, setSelectedCard] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  
  // Add card
  const handleAddCard = useCallback(() => {
    const id = `card-${cards.length + 1}`;
    const newCard = {
      id,
      title: `New Card ${cards.length + 1}`,
      content: `Content for card ${cards.length + 1}`
    };
    
    setCards(prevCards => [...prevCards, newCard]);
  }, [cards.length]);
  
  // Remove card
  const handleRemoveCard = useCallback((id) => {
    setCards(prevCards => prevCards.filter(card => card.id !== id));
  }, []);
  
  // Toggle animations
  const toggleAnimations = useCallback(() => {
    setEnabled(!enabled);
  }, [enabled, setEnabled]);
  
  // Handle animation change
  const handleAnimationChange = useCallback((e) => {
    setSelectedAnimation(e.target.value);
  }, []);
  
  // Show card details
  const showCardDetails = useCallback((card) => {
    setSelectedCard(card);
    setShowDetails(true);
  }, []);
  
  // Hide card details
  const hideCardDetails = useCallback(() => {
    setShowDetails(false);
  }, []);
  
  // Animation presets
  const animationPresets = [
    'fadeIn',
    'slideInRight',
    'slideInLeft',
    'slideInUp',
    'slideInDown',
    'zoomIn',
    'flipInX',
    'flipInY',
    'pulse',
    'shake',
    'bounce'
  ];
  
  return (
    <div className="space-y-6">
      {/* Skip link for keyboard navigation */}
      <SkipLink targetId="main-content" />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <Animated animation="slideInLeft">
          <h1 className="text-2xl font-bold text-textPrimary" id="dashboard-title">
            Animated Dashboard
          </h1>
        </Animated>
        
        <div className="flex items-center space-x-2">
          <ThemeSelector variant="dropdown" />
          
          <button
            className={`px-4 py-2 border rounded-md transition-colors duration-200 ${
              enabled
                ? 'bg-primary text-primaryContrast border-primary'
                : 'bg-surface text-textPrimary border-divider'
            }`}
            onClick={toggleAnimations}
          >
            {enabled ? 'Disable Animations' : 'Enable Animations'}
          </button>
        </div>
      </div>
      
      {/* Controls */}
      <div className="flex flex-wrap gap-4 items-center bg-surface p-4 rounded-md border border-divider">
        <div>
          <label htmlFor="animation-select" className="block text-sm font-medium text-textSecondary mb-1">
            Animation
          </label>
          <select
            id="animation-select"
            className="px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
            value={selectedAnimation}
            onChange={handleAnimationChange}
          >
            {animationPresets.map(preset => (
              <option key={preset} value={preset}>
                {preset}
              </option>
            ))}
          </select>
        </div>
        
        <div>
          <button
            className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200 mt-5"
            onClick={handleAddCard}
          >
            Add Card
          </button>
        </div>
        
        {reducedMotion && (
          <div className="ml-auto">
            <div className="text-sm text-warning">
              Reduced motion preference detected. Animations are simplified.
            </div>
          </div>
        )}
      </div>
      
      {/* Main content */}
      <main id="main-content" tabIndex="-1">
        <TransitionGroup className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {cards.map(card => (
            <Transition
              key={card.id}
              in={true}
              enter={selectedAnimation}
              exit="fadeOut"
              timeout={500}
              unmountOnExit
            >
              <div className="h-full">
                <DashboardCard
                  title={card.title}
                  collapsible={true}
                  actions={[
                    {
                      label: 'View',
                      onClick: () => showCardDetails(card)
                    },
                    {
                      label: 'Remove',
                      onClick: () => handleRemoveCard(card.id)
                    }
                  ]}
                >
                  <div className="p-4 h-40">
                    <p className="text-textPrimary">{card.content}</p>
                    <p className="text-textSecondary mt-2">
                      This card is animated with the <code>{selectedAnimation}</code> animation.
                    </p>
                  </div>
                </DashboardCard>
              </div>
            </Transition>
          ))}
        </TransitionGroup>
        
        {/* Animation examples */}
        <div className="mt-8">
          <h2 className="text-xl font-bold text-textPrimary mb-4">Animation Examples</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <DashboardCard title="Hover Animation">
              <div className="p-4 h-40 flex items-center justify-center">
                <div
                  className="w-20 h-20 bg-primary rounded-md cursor-pointer transition-transform duration-300 hover:scale-110"
                  title="Hover to see animation"
                />
              </div>
            </DashboardCard>
            
            <DashboardCard title="Click Animation">
              <div className="p-4 h-40 flex items-center justify-center">
                <Animated
                  animation="pulse"
                  autoPlay={false}
                  as="button"
                  className="w-20 h-20 bg-secondary rounded-md cursor-pointer"
                  onClick={(e) => e.currentTarget.getAnimations()[0]?.play()}
                  title="Click to see animation"
                />
              </div>
            </DashboardCard>
            
            <DashboardCard title="Continuous Animation">
              <div className="p-4 h-40 flex items-center justify-center">
                <Animated
                  animation={{
                    keyframes: [
                      { transform: 'translateY(0)' },
                      { transform: 'translateY(-10px)' },
                      { transform: 'translateY(0)' }
                    ],
                    duration: 1500,
                    iterations: Infinity
                  }}
                  className="w-20 h-20 bg-tertiary rounded-md"
                />
              </div>
            </DashboardCard>
          </div>
        </div>
      </main>
      
      {/* Card details modal */}
      <Transition
        in={showDetails}
        enter="fadeIn"
        exit="fadeOut"
        timeout={300}
        unmountOnExit
      >
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Animated
            animation={selectedAnimation}
            className="w-full max-w-2xl bg-background rounded-lg shadow-lg overflow-hidden"
          >
            {selectedCard && (
              <div>
                <div className="px-6 py-4 border-b border-divider flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-textPrimary">{selectedCard.title}</h2>
                  <button
                    className="text-textSecondary hover:text-textPrimary"
                    onClick={hideCardDetails}
                  >
                    ✕
                  </button>
                </div>
                <div className="p-6">
                  <p className="text-textPrimary">{selectedCard.content}</p>
                  <p className="text-textSecondary mt-4">
                    This modal is animated with the <code>{selectedAnimation}</code> animation.
                  </p>
                  <div className="mt-6 flex justify-end">
                    <button
                      className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
                      onClick={hideCardDetails}
                    >
                      Close
                    </button>
                  </div>
                </div>
              </div>
            )}
          </Animated>
        </div>
      </Transition>
    </div>
  );
};

AnimatedDashboardContent.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  enableLogging: PropTypes.bool
};

/**
 * Animated Dashboard component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} [props.initialTheme] - Initial theme
 * @param {boolean} [props.enableSystemPreference=true] - Whether to enable system preference detection
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Animated Dashboard component
 */
const AnimatedDashboard = ({
  novaConnect,
  novaShield,
  novaTrack,
  initialTheme,
  enableSystemPreference = true,
  enableLogging = false
}) => {
  return (
    <ThemeProvider theme={initialTheme} enableSystemPreference={enableSystemPreference}>
      <PreferencesProvider>
        <OfflineProvider>
          <AnimationProvider>
            <AnimatedDashboardContent
              novaConnect={novaConnect}
              novaShield={novaShield}
              novaTrack={novaTrack}
              enableLogging={enableLogging}
            />
          </AnimationProvider>
        </OfflineProvider>
      </PreferencesProvider>
    </ThemeProvider>
  );
};

AnimatedDashboard.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  initialTheme: PropTypes.object,
  enableSystemPreference: PropTypes.bool,
  enableLogging: PropTypes.bool
};

export default AnimatedDashboard;
