/**
 * NovaFuse Universal API Connector - Connector Configuration Service
 * 
 * This module provides a service for managing connector configurations.
 */

const { ValidationError, ResourceNotFoundError } = require('../../errors');
const connectorRegistryService = require('./connector-registry');

/**
 * Connector Configuration Service class
 */
class ConnectorConfigService {
  constructor() {
    this.configs = new Map();
  }

  /**
   * Validate configuration against schema
   * 
   * @param {Object} values - The configuration values
   * @param {Object} schema - The configuration schema
   * @returns {Object} - Validation result
   * @private
   */
  _validateConfig(values, schema) {
    const errors = [];
    
    // Check required properties
    if (schema.required) {
      for (const prop of schema.required) {
        if (values[prop] === undefined || values[prop] === null || values[prop] === '') {
          errors.push(`${prop} is required`);
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get all configurations
   * 
   * @param {Object} filters - Filters to apply
   * @returns {Array<Object>} - The configurations
   */
  async getAllConfigurations(filters = {}) {
    let configs = Array.from(this.configs.values());
    
    // Apply filters
    if (filters.connectorId) {
      configs = configs.filter(c => c.connectorId === filters.connectorId);
    }
    
    // Default sort by name
    configs.sort((a, b) => a.name.localeCompare(b.name));
    
    return configs;
  }

  /**
   * Get a configuration by ID
   * 
   * @param {string} id - The configuration ID
   * @returns {Object} - The configuration
   */
  async getConfiguration(id) {
    const config = this.configs.get(id);
    
    if (!config) {
      throw new ResourceNotFoundError('Configuration', id);
    }
    
    return config;
  }

  /**
   * Create a new configuration
   * 
   * @param {Object} data - The configuration data
   * @returns {Object} - The created configuration
   */
  async createConfiguration(data) {
    // Validate required fields
    if (!data.name) {
      throw new ValidationError('Name is required');
    }
    
    if (!data.connectorId) {
      throw new ValidationError('Connector ID is required');
    }
    
    // Get connector to validate configuration
    const connector = await connectorRegistryService.getConnector(data.connectorId);
    
    // Create configuration ID if not provided
    const id = data.id || `${data.connectorId}-${Date.now()}`;
    
    // Create configuration
    const config = {
      id,
      name: data.name,
      description: data.description || '',
      connectorId: data.connectorId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      values: data.values || {}
    };
    
    // Validate configuration against schema
    const validation = this._validateConfig(config.values, connector.configSchema);
    if (!validation.valid) {
      throw new ValidationError('Invalid configuration', { validationErrors: validation.errors });
    }
    
    // Save configuration
    this.configs.set(id, config);
    
    return config;
  }

  /**
   * Update a configuration
   * 
   * @param {string} id - The configuration ID
   * @param {Object} data - The data to update
   * @returns {Object} - The updated configuration
   */
  async updateConfiguration(id, data) {
    // Get configuration
    const config = await this.getConfiguration(id);
    
    // Update fields
    const updatedConfig = {
      ...config,
      ...data,
      id: config.id, // Ensure ID doesn't change
      connectorId: config.connectorId, // Ensure connector ID doesn't change
      updatedAt: new Date().toISOString(),
      values: data.values !== undefined ? data.values : config.values
    };
    
    // Get connector to validate configuration
    const connector = await connectorRegistryService.getConnector(config.connectorId);
    
    // Validate configuration against schema
    const validation = this._validateConfig(updatedConfig.values, connector.configSchema);
    if (!validation.valid) {
      throw new ValidationError('Invalid configuration', { validationErrors: validation.errors });
    }
    
    // Save configuration
    this.configs.set(id, updatedConfig);
    
    return updatedConfig;
  }

  /**
   * Delete a configuration
   * 
   * @param {string} id - The configuration ID
   * @returns {boolean} - Whether the configuration was deleted
   */
  async deleteConfiguration(id) {
    // Get configuration
    await this.getConfiguration(id);
    
    // Delete configuration
    return this.configs.delete(id);
  }

  /**
   * Get configurations for a connector
   * 
   * @param {string} connectorId - The connector ID
   * @returns {Array<Object>} - The configurations
   */
  async getConfigurationsForConnector(connectorId) {
    return this.getAllConfigurations({ connectorId });
  }
}

// Create singleton instance
const connectorConfigService = new ConnectorConfigService();

module.exports = connectorConfigService;
