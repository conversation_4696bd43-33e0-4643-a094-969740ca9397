/**
 * Authentication Middleware
 *
 * This middleware handles authentication and authorization.
 */

const AuthService = require('../services/AuthService');
const ApiKeyService = require('../services/ApiKeyService');
const RateLimitService = require('../services/RateLimitService');
const TeamService = require('../services/TeamService');
const RBACService = require('../services/RBACService');
const { AuthenticationError, AuthorizationError } = require('../utils/errors');
const logger = require('../../config/logger');

const authService = new AuthService();
const apiKeyService = new ApiKeyService();
const rateLimitService = new RateLimitService();
const teamService = new TeamService();
const rbacService = new RBACService();

/**
 * Authenticate user using JWT token
 */
const authenticate = async (req, res, next) => {
  try {
    // Check for API key
    const apiKey = req.headers['x-api-key'];

    if (apiKey) {
      // Authenticate using API key
      const apiKeyData = await apiKeyService.verifyApiKey(apiKey);

      // Check rate limit for API key
      const rateLimitResult = await rateLimitService.checkRateLimit(apiKey, 'apiKey');

      // Set rate limit headers
      res.set({
        'X-RateLimit-Limit': rateLimitResult.limit,
        'X-RateLimit-Remaining': rateLimitResult.remaining,
        'X-RateLimit-Reset': rateLimitResult.reset
      });

      // Get user associated with API key
      const user = await authService.getUserById(apiKeyData.userId);

      // Attach user and API key to request
      req.user = user;
      req.apiKey = apiKeyData;

      return next();
    }

    // Check for JWT token
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // No authentication provided, check rate limit for anonymous user
      const clientIp = req.ip || req.connection.remoteAddress;
      const rateLimitResult = await rateLimitService.checkRateLimit(clientIp, 'anonymous');

      // Set rate limit headers
      res.set({
        'X-RateLimit-Limit': rateLimitResult.limit,
        'X-RateLimit-Remaining': rateLimitResult.remaining,
        'X-RateLimit-Reset': rateLimitResult.reset
      });

      throw new AuthenticationError('Authentication required');
    }

    const token = authHeader.split(' ')[1];

    // Verify token
    const decoded = await authService.verifyToken(token);

    // Get user
    const user = await authService.getUserById(decoded.sub);

    // Check rate limit for authenticated user
    const rateLimitResult = await rateLimitService.checkRateLimit(user.id, 'authenticated');

    // Set rate limit headers
    res.set({
      'X-RateLimit-Limit': rateLimitResult.limit,
      'X-RateLimit-Remaining': rateLimitResult.remaining,
      'X-RateLimit-Reset': rateLimitResult.reset
    });

    // Attach user to request
    req.user = user;

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Optional authentication
 */
const optionalAuth = async (req, res, next) => {
  try {
    // Check for API key
    const apiKey = req.headers['x-api-key'];

    if (apiKey) {
      try {
        // Authenticate using API key
        const apiKeyData = await apiKeyService.verifyApiKey(apiKey);

        // Check rate limit for API key
        const rateLimitResult = await rateLimitService.checkRateLimit(apiKey, 'apiKey');

        // Set rate limit headers
        res.set({
          'X-RateLimit-Limit': rateLimitResult.limit,
          'X-RateLimit-Remaining': rateLimitResult.remaining,
          'X-RateLimit-Reset': rateLimitResult.reset
        });

        // Get user associated with API key
        const user = await authService.getUserById(apiKeyData.userId);

        // Attach user and API key to request
        req.user = user;
        req.apiKey = apiKeyData;

        return next();
      } catch (error) {
        // Ignore API key errors
      }
    }

    // Check for JWT token
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.split(' ')[1];

      try {
        // Verify token
        const decoded = await authService.verifyToken(token);

        // Get user
        const user = await authService.getUserById(decoded.sub);

        // Check rate limit for authenticated user
        const rateLimitResult = await rateLimitService.checkRateLimit(user.id, 'authenticated');

        // Set rate limit headers
        res.set({
          'X-RateLimit-Limit': rateLimitResult.limit,
          'X-RateLimit-Remaining': rateLimitResult.remaining,
          'X-RateLimit-Reset': rateLimitResult.reset
        });

        // Attach user to request
        req.user = user;

        return next();
      } catch (error) {
        // Ignore token errors
      }
    }

    // No authentication provided or authentication failed, check rate limit for anonymous user
    const clientIp = req.ip || req.connection.remoteAddress;
    const rateLimitResult = await rateLimitService.checkRateLimit(clientIp, 'anonymous');

    // Set rate limit headers
    res.set({
      'X-RateLimit-Limit': rateLimitResult.limit,
      'X-RateLimit-Remaining': rateLimitResult.remaining,
      'X-RateLimit-Reset': rateLimitResult.reset
    });

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Check if user has required role
 *
 * @param {string|string[]} role - Required role(s)
 * @param {Object} options - Options
 * @param {boolean} options.all - If true, user must have all roles (default: false)
 * @param {string} options.scope - Scope to check roles in (default: 'global')
 * @param {string} options.scopeIdParam - Request parameter to get scope ID from
 * @param {Function} options.getScopeId - Function to get scope ID from request
 * @returns {Function} - Express middleware
 */
const hasRole = (role, options = {}) => {
  return async (req, res, next) => {
    try {
      // Check if user exists
      if (!req.user) {
        return next(new AuthenticationError('Authentication required'));
      }

      const userId = req.user.id;

      // Get user roles
      const userRoles = await rbacService.getUserRoles(userId);
      const userRoleNames = userRoles.map(role => role.name);

      // Convert single role to array
      const roles = Array.isArray(role) ? role : [role];

      // Check if user has role(s)
      if (options.all) {
        // User must have all roles
        for (const roleName of roles) {
          if (!userRoleNames.includes(roleName)) {
            throw new AuthorizationError(`User does not have required role: ${roleName}`);
          }
        }
      } else {
        // User must have at least one role
        let hasAnyRole = false;

        for (const roleName of roles) {
          if (userRoleNames.includes(roleName)) {
            hasAnyRole = true;
            break;
          }
        }

        if (!hasAnyRole) {
          throw new AuthorizationError(`User does not have any of the required roles: ${roles.join(', ')}`);
        }
      }

      next();
    } catch (error) {
      logger.error('RBAC authorization error', {
        userId: req.user?.id,
        requiredRole: role,
        error: error.message
      });

      if (error instanceof AuthorizationError) {
        return res.status(403).json({
          error: 'Forbidden',
          message: error.message
        });
      }

      next(error);
    }
  };
};

/**
 * Check if user has required permission
 *
 * @param {string|string[]} permission - Required permission(s)
 * @param {Object} options - Options
 * @param {boolean} options.all - If true, user must have all permissions (default: false)
 * @param {string} options.scope - Scope to check permissions in (default: 'global')
 * @param {string} options.scopeIdParam - Request parameter to get scope ID from
 * @param {Function} options.getScopeId - Function to get scope ID from request
 * @returns {Function} - Express middleware
 */
const hasPermission = (permission, options = {}) => {
  return async (req, res, next) => {
    try {
      // Check if user exists
      if (!req.user) {
        return next(new AuthenticationError('Authentication required'));
      }

      const userId = req.user.id;

      // Check API key permissions if request was made with API key
      if (req.apiKey) {
        // Convert single permission to array
        const permissions = Array.isArray(permission) ? permission : [permission];

        if (options.all) {
          // API key must have all permissions
          for (const perm of permissions) {
            if (!apiKeyService.hasPermission(req.apiKey, perm)) {
              throw new AuthorizationError(`API key does not have required permission: ${perm}`);
            }
          }
        } else {
          // API key must have at least one permission
          let hasAnyPermission = false;

          for (const perm of permissions) {
            if (apiKeyService.hasPermission(req.apiKey, perm)) {
              hasAnyPermission = true;
              break;
            }
          }

          if (!hasAnyPermission) {
            throw new AuthorizationError(`API key does not have any of the required permissions: ${permissions.join(', ')}`);
          }
        }

        return next();
      }

      // Convert single permission to array
      const permissions = Array.isArray(permission) ? permission : [permission];

      // Check if user has permission(s)
      if (options.all) {
        // User must have all permissions
        for (const perm of permissions) {
          const hasPermission = await rbacService.hasPermission(userId, perm);

          if (!hasPermission) {
            throw new AuthorizationError(`User does not have required permission: ${perm}`);
          }
        }
      } else {
        // User must have at least one permission
        let hasAnyPermission = false;

        for (const perm of permissions) {
          const hasPermission = await rbacService.hasPermission(userId, perm);

          if (hasPermission) {
            hasAnyPermission = true;
            break;
          }
        }

        if (!hasAnyPermission) {
          throw new AuthorizationError(`User does not have any of the required permissions: ${permissions.join(', ')}`);
        }
      }

      next();
    } catch (error) {
      logger.error('RBAC authorization error', {
        userId: req.user?.id,
        requiredPermission: permission,
        error: error.message
      });

      if (error instanceof AuthorizationError) {
        return res.status(403).json({
          error: 'Forbidden',
          message: error.message
        });
      }

      next(error);
    }
  };
};

/**
 * Check if user is a member of a team
 */
const isTeamMember = (teamIdParam = 'id') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return next(new AuthenticationError('Authentication required'));
      }

      const teamId = req.params[teamIdParam];

      if (!teamId) {
        return next(new AuthorizationError(`Team ID parameter '${teamIdParam}' not found`));
      }

      // Check if user is a member of the team
      const isMember = await teamService.isUserTeamMember(teamId, req.user.id);

      if (!isMember) {
        return next(new AuthorizationError('Team membership required'));
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Check if user has a specific role in a team
 */
const hasTeamRole = (role, teamIdParam = 'id') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return next(new AuthenticationError('Authentication required'));
      }

      const teamId = req.params[teamIdParam];

      if (!teamId) {
        return next(new AuthorizationError(`Team ID parameter '${teamIdParam}' not found`));
      }

      // Get user's role in the team
      const userRole = await teamService.getUserRoleInTeam(teamId, req.user.id);

      if (!userRole) {
        return next(new AuthorizationError('Team membership required'));
      }

      // Check if user has the required role
      if (typeof role === 'string') {
        if (userRole !== role) {
          return next(new AuthorizationError(`Team role '${role}' required`));
        }
      } else if (Array.isArray(role)) {
        if (!role.includes(userRole)) {
          return next(new AuthorizationError(`One of team roles [${role.join(', ')}] required`));
        }
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Check if user is an admin
 */
const isAdmin = (req, res, next) => {
  return hasRole('Administrator')(req, res, next);
};

module.exports = {
  authenticate,
  optionalAuth,
  hasRole,
  hasPermission,
  isTeamMember,
  hasTeamRole,
  isAdmin
};
