apiVersion: apps/v1
kind: Deployment
metadata:
  name: novafuse-uac
  namespace: production
  labels:
    app: novafuse-uac
    environment: production
spec:
  replicas: 3
  selector:
    matchLabels:
      app: novafuse-uac
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: novafuse-uac
        environment: production
    spec:
      containers:
      - name: novafuse-uac
        image: gcr.io/PROJECT_ID/novafuse-uac:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3001
        resources:
          requests:
            cpu: 500m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3001"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: novafuse-uac-secrets
              key: mongodb-uri
        - name: REDIS_URI
          valueFrom:
            secretKeyRef:
              name: novafuse-uac-secrets
              key: redis-uri
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: novafuse-uac-secrets
              key: api-key
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: novafuse-uac-secrets
              key: jwt-secret
        - name: CLUSTER_ENABLED
          value: "true"
        - name: LOG_LEVEL
          value: "info"
        - name: MONITORING_ENABLED
          value: "true"
        - name: TRACING_ENABLED
          value: "true"
        - name: GCP_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: novafuse-uac-secrets
              key: gcp-project-id
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/var/secrets/google/key.json"
        - name: CORS_ORIGIN
          value: "https://api.novafuse.io,https://app.novafuse.io"
        - name: RATE_LIMIT_WINDOW_MS
          value: "60000"
        - name: RATE_LIMIT_MAX
          value: "100"
        - name: DB_OPTIMIZATION_ENABLED
          value: "true"
        - name: DB_OPTIMIZATION_INTERVAL
          value: "86400000"
        volumeMounts:
        - name: google-cloud-key
          mountPath: /var/secrets/google
        readinessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: google-cloud-key
        secret:
          secretName: novafuse-uac-gcp-key
---
apiVersion: v1
kind: Service
metadata:
  name: novafuse-uac
  namespace: production
  labels:
    app: novafuse-uac
    environment: production
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 3001
    protocol: TCP
    name: http
  selector:
    app: novafuse-uac
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: novafuse-uac
  namespace: production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: novafuse-uac
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: novafuse-uac
  namespace: production
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "novafuse-uac-production-ip"
    networking.gke.io/managed-certificates: "novafuse-uac-production-cert"
    networking.gke.io/v1beta1.FrontendConfig: "novafuse-uac-frontend-config"
spec:
  rules:
  - host: api.novafuse.io
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: novafuse-uac
            port:
              number: 80
---
apiVersion: networking.gke.io/v1beta1
kind: FrontendConfig
metadata:
  name: novafuse-uac-frontend-config
  namespace: production
spec:
  redirectToHttps:
    enabled: true
    responseCodeName: MOVED_PERMANENTLY_DEFAULT
---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: novafuse-uac-production-cert
  namespace: production
spec:
  domains:
  - api.novafuse.io
