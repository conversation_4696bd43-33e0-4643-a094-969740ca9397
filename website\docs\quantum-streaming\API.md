# Quantum Streaming API Reference

## Overview
This document provides detailed API documentation for the Quantum Streaming system, including core hooks, utilities, and integration patterns.

## Table of Contents
- [Core Hooks](#core-hooks)
- [Utilities](#utilities)
- [Integration](#integration)
- [Error Handling](#error-handling)
- [Performance Considerations](#performance-considerations)

## Core Hooks

### `useQuantumStream`
Manages WebSocket connection and message handling for quantum data streams.

```typescript
interface UseQuantumStreamOptions {
  streamId: string;
  autoConnect?: boolean;
  binaryMode?: boolean;
  deltaEncoding?: boolean;
  throttle?: number;
  onMessage?: (data: any, isBinary: boolean) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Error) => void;
}

interface StreamStats {
  messages: number;
  bytes: number;
  connected: boolean;
  reconnectAttempts: number;
  lastMessageTime: number;
  latency: number;
}

function useQuantumStream(options: UseQuantumStreamOptions): {
  data: any;
  send: (data: any) => void;
  connect: () => void;
  disconnect: () => void;
  stats: StreamStats;
  error: Error | null;
  isConnected: boolean;
  isConnecting: boolean;
}
```

### `useAdaptiveStream`
Manages adaptive quality settings based on system performance.

```typescript
interface AdaptiveStreamOptions {
  initialQuality?: 'low' | 'medium' | 'high';
  targetFps?: number;
  minFps?: number;
  onQualityChange?: (
    newQuality: string, 
    oldQuality: string, 
    metrics: PerformanceMetrics
  ) => void;
}

interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  memory: number;
  quality: string;
  timestamp: number;
}

function useAdaptiveStream(options: AdaptiveStreamOptions): {
  quality: string;
  profile: QualityProfile;
  setQuality: (quality: string) => void;
  getMetrics: () => PerformanceMetrics;
  recordFrame: (frameTime: number) => void;
}
```

## Utilities

### `quantumCodec`
Handles encoding and decoding of quantum data messages.

```typescript
// Encode a message for transmission
function encodeMessage(
  data: any, 
  options?: {
    delta?: boolean;
    previousState?: any;
    binary?: boolean;
  }
): ArrayBuffer | any;

// Decode a received message
function decodeMessage(
  data: ArrayBuffer | any, 
  options?: {
    delta?: boolean;
    previousState?: any;
  }
): any;
```

## Integration

### Dashboard Integration

```jsx
import { QuantumDashboardFusion } from '../integrations/dashboard-fusion';

function App() {
  return (
    <div className="app">
      <QuantumDashboardFusion
        streamId="quantum-governance"
        config={{
          visualization: {
            initialQuality: 'high',
            showStats: true
          }
        }}
        onStateUpdate={(data) => {
          // Handle state updates
        }}
        onError={(error) => {
          // Handle errors
        }}
      />
    </div>
  );
}
```

## Error Handling

### Error Types

| Error Code | Description | Recommended Action |
|------------|-------------|-------------------|
| QS_CONNECTION | Connection failed | Retry with backoff |
| QS_AUTH | Authentication failed | Re-authenticate |
| QS_PROTOCOL | Protocol error | Check version compatibility |
| QS_QUOTA | Rate limit exceeded | Implement backoff |
| QS_INTERNAL | Internal server error | Report and retry |

### Error Recovery

```js
function handleError(error) {
  switch (error.code) {
    case 'QS_CONNECTION':
      // Implement reconnection logic
      break;
    case 'QS_AUTH':
      // Handle authentication
      break;
    default:
      console.error('Unhandled error:', error);
  }
}
```

## Performance Considerations

### Message Size Optimization
- Use delta encoding for frequent updates
- Enable binary mode for large payloads
- Implement message batching when possible

### Rendering Optimization
- Use quality profiles to match device capabilities
- Implement level-of-detail (LOD) for complex visualizations
- Use Web Workers for heavy computations

### Memory Management
- Dispose of unused resources
- Implement object pooling for frequently created/destroyed objects
- Monitor memory usage and warn about leaks

## Best Practices

1. **Connection Management**
   - Implement exponential backoff for reconnections
   - Show connection status to users
   - Handle offline scenarios gracefully

2. **Data Handling**
   - Validate all incoming messages
   - Implement data versioning
   - Use TypeScript for type safety

3. **Performance**
   - Profile regularly
   - Use the performance API for measurements
   - Implement adaptive quality

4. **Security**
   - Use secure WebSockets (wss://)
   - Validate message signatures
   - Implement rate limiting

## Troubleshooting

### Common Issues

1. **High Latency**
   - Check network conditions
   - Reduce update frequency
   - Enable delta encoding

2. **High Memory Usage**
   - Check for memory leaks
   - Reduce retained state
   - Use object pooling

3. **Connection Drops**
   - Implement reconnection logic
   - Handle network changes
   - Check server status

## Examples

### Basic Usage

```jsx
import { useQuantumStream } from '../hooks/useQuantumStream';

function QuantumVisualizer() {
  const { data, isConnected } = useQuantumStream({
    streamId: 'quantum-data',
    onMessage: (data) => {
      console.log('Received data:', data);
    },
    onError: (error) => {
      console.error('Stream error:', error);
    }
  });

  return (
    <div>
      <div>Status: {isConnected ? 'Connected' : 'Disconnected'}</div>
      {/* Render quantum data */}
    </div>
  );
}
```

### With Adaptive Quality

```jsx
import { useQuantumStream } from '../hooks/useQuantumStream';
import { useAdaptiveStream } from '../hooks/useAdaptiveStream';

function QuantumViewer() {
  const { quality, profile } = useAdaptiveStream({
    initialQuality: 'high',
    targetFps: 60,
    onQualityChange: (newQuality, oldQuality) => {
      console.log(`Quality changed: ${oldQuality} -> ${newQuality}`);
    }
  });

  const { data } = useQuantumStream({
    streamId: 'quantum-data',
    throttle: 1000 / profile.updateRate
  });

  // Render based on quality
  return (
    <div className={`quality-${quality}`}>
      {/* Render content */}
    </div>
  );
}
```

## Versioning

| Version | Date | Changes |
|---------|------|---------|
| 1.0.0 | 2025-07-03 | Initial release |
| 1.1.0 | 2025-07-10 | Added delta encoding |
| 1.2.0 | 2025-07-17 | Performance improvements |

## License

This project is licensed under the NovaFuse Open Source License.
