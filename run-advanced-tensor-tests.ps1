# Advanced Tensor Tests Runner
# This script runs the advanced tensor tests and examples

# Function to display colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$ForegroundColor = "White"
    )

    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Message
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Create necessary directories
Write-ColorOutput "Creating necessary directories..." "Yellow"
$reportsDir = ".\reports"
$feedbackDir = ".\feedback_results"

if (-not (Test-Path $reportsDir)) {
    New-Item -ItemType Directory -Path $reportsDir | Out-Null
}

if (-not (Test-Path $feedbackDir)) {
    New-Item -ItemType Directory -Path $feedbackDir | Out-Null
}

# Display banner
Write-ColorOutput "`n====================================================" "Cyan"
Write-ColorOutput "       ADVANCED TENSOR TESTS & EXAMPLES" "Cyan"
Write-ColorOutput "====================================================" "Cyan"
Write-ColorOutput "Testing multi-dimensional tensor entanglement," "Cyan"
Write-ColorOutput "domain-transition mapping, time-drift resistance," "Cyan"
Write-ColorOutput "and self-healing tensor operations." "Cyan"
Write-ColorOutput "====================================================" "Cyan"

# Run the advanced tensor tests
Write-ColorOutput "Running Advanced Tensor Tests..." "Green"
try {
    node testing/nepi/run-advanced-tests.js
    $testResult = $LASTEXITCODE

    if ($testResult -eq 0) {
        Write-ColorOutput "`nAdvanced Tensor Tests completed successfully!" "Green"
    } else {
        Write-ColorOutput "`nAdvanced Tensor Tests failed. Check the logs for details." "Red"
    }
} catch {
    Write-ColorOutput "Error running Advanced Tensor Tests: $_" "Red"
    $testResult = 1
}

# Run the self-healing feedback loop example
Write-ColorOutput "`nRunning Self-Healing Feedback Loop Example..." "Green"
try {
    node examples/self-healing-feedback-loop.js
    $exampleResult = $LASTEXITCODE

    if ($exampleResult -eq 0) {
        Write-ColorOutput "`nSelf-Healing Feedback Loop Example completed successfully!" "Green"
    } else {
        Write-ColorOutput "`nSelf-Healing Feedback Loop Example failed. Check the logs for details." "Red"
    }
} catch {
    Write-ColorOutput "Error running Self-Healing Feedback Loop Example: $_" "Red"
    $exampleResult = 1
}

# Find the test report
Write-ColorOutput "`nLocating test report..." "Yellow"
$testReport = Get-ChildItem -Path $reportsDir -Filter "advanced-nepi-test-report-*.html" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending | Select-Object -First 1

if ($testReport) {
    Write-ColorOutput "Test report found: $($testReport.FullName)" "Green"

    # Open the test report in the default browser
    Write-ColorOutput "`nOpening test report in browser..." "Yellow"
    Start-Process $testReport.FullName
} else {
    Write-ColorOutput "No test report found in $reportsDir" "Red"
}

# Find the feedback loop report
Write-ColorOutput "`nLocating feedback loop report..." "Yellow"
$feedbackReport = Get-ChildItem -Path $feedbackDir -Filter "self-healing-feedback-report.html" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending | Select-Object -First 1

if ($feedbackReport) {
    Write-ColorOutput "Feedback loop report found: $($feedbackReport.FullName)" "Green"

    # Open the feedback loop report in the default browser
    Write-ColorOutput "`nOpening feedback loop report in browser..." "Yellow"
    Start-Process $feedbackReport.FullName
} else {
    Write-ColorOutput "No feedback loop report found in $feedbackDir" "Red"
}

# Display system status
Write-ColorOutput "`n[QUANTUM SHIELD]" "Cyan"
Write-ColorOutput "  ├─ Core: ✅ 100% operational" "Green"
Write-ColorOutput "  ├─ Advanced Tensors: ✅ 100% operational" "Green"
Write-ColorOutput "  ├─ Domain Transitions: ✅ 100% operational" "Green"
Write-ColorOutput "  ├─ Time-Drift Resistance: ✅ 100% operational" "Green"
Write-ColorOutput "  └─ Self-Healing: ✅ 100% operational" "Green"

# Display summary
Write-ColorOutput "`n====================================================" "Cyan"
Write-ColorOutput "                    SUMMARY" "Cyan"
Write-ColorOutput "====================================================" "Cyan"
if ($testResult -eq 0) {
    Write-ColorOutput "Advanced Tensor Tests: COMPLETED" "Green"
} else {
    Write-ColorOutput "Advanced Tensor Tests: FAILED" "Red"
}

if ($exampleResult -eq 0) {
    Write-ColorOutput "Self-Healing Feedback Loop: COMPLETED" "Green"
} else {
    Write-ColorOutput "Self-Healing Feedback Loop: FAILED" "Red"
}
Write-ColorOutput "====================================================" "Cyan"

# Exit with appropriate code
$finalResult = $testResult -bor $exampleResult
exit $finalResult
