const request = require('supertest');
const app = require('../../../server');

describe('Security Assessment API', () => {
  // Test GET /security/assessment/assessments
  describe('GET /security/assessment/assessments', () => {
    it('should return a list of security assessments', async () => {
      const response = await request(app)
        .get('/security/assessment/assessments')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
    
    it('should filter assessments by type', async () => {
      const response = await request(app)
        .get('/security/assessment/assessments?type=internal')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(assessment => assessment.type === 'internal')).toBe(true);
    });
    
    it('should filter assessments by status', async () => {
      const response = await request(app)
        .get('/security/assessment/assessments?status=completed')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(assessment => assessment.status === 'completed')).toBe(true);
    });
    
    it('should paginate results', async () => {
      const response = await request(app)
        .get('/security/assessment/assessments?page=1&limit=2')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeLessThanOrEqual(2);
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(2);
    });
  });
  
  // Test GET /security/assessment/assessments/:id
  describe('GET /security/assessment/assessments/:id', () => {
    it('should return a specific security assessment', async () => {
      const response = await request(app)
        .get('/security/assessment/assessments/sa-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('sa-001');
    });
    
    it('should return 404 for non-existent assessment', async () => {
      const response = await request(app)
        .get('/security/assessment/assessments/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test POST /security/assessment/assessments
  describe('POST /security/assessment/assessments', () => {
    it('should create a new security assessment', async () => {
      const newAssessment = {
        title: 'Test Security Assessment',
        description: 'This is a test security assessment',
        type: 'internal',
        status: 'planned',
        scope: 'Test scope',
        startDate: '2025-01-01',
        endDate: '2025-01-15',
        assignedTo: 'Test Team'
      };
      
      const response = await request(app)
        .post('/security/assessment/assessments')
        .set('apikey', 'test-api-key')
        .send(newAssessment);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.title).toBe(newAssessment.title);
      expect(response.body.data.type).toBe(newAssessment.type);
      expect(response.body.data).toHaveProperty('id');
    });
    
    it('should return 400 for invalid input', async () => {
      const invalidAssessment = {
        // Missing required fields
        description: 'This is an invalid security assessment'
      };
      
      const response = await request(app)
        .post('/security/assessment/assessments')
        .set('apikey', 'test-api-key')
        .send(invalidAssessment);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test PUT /security/assessment/assessments/:id
  describe('PUT /security/assessment/assessments/:id', () => {
    it('should update an existing security assessment', async () => {
      const updateData = {
        title: 'Updated Security Assessment',
        status: 'in-progress'
      };
      
      const response = await request(app)
        .put('/security/assessment/assessments/sa-001')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.title).toBe(updateData.title);
      expect(response.body.data.status).toBe(updateData.status);
    });
    
    it('should return 404 for non-existent assessment', async () => {
      const updateData = {
        title: 'Updated Security Assessment'
      };
      
      const response = await request(app)
        .put('/security/assessment/assessments/non-existent-id')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test DELETE /security/assessment/assessments/:id
  describe('DELETE /security/assessment/assessments/:id', () => {
    it('should delete an existing security assessment', async () => {
      const response = await request(app)
        .delete('/security/assessment/assessments/sa-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 404 for non-existent assessment', async () => {
      const response = await request(app)
        .delete('/security/assessment/assessments/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /security/assessment/assessments/:id/findings
  describe('GET /security/assessment/assessments/:id/findings', () => {
    it('should return findings for a specific assessment', async () => {
      const response = await request(app)
        .get('/security/assessment/assessments/sa-002/findings')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
    
    it('should return 404 for non-existent assessment', async () => {
      const response = await request(app)
        .get('/security/assessment/assessments/non-existent-id/findings')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test POST /security/assessment/assessments/:id/findings
  describe('POST /security/assessment/assessments/:id/findings', () => {
    it('should add a finding to an assessment', async () => {
      const newFinding = {
        title: 'Test Security Finding',
        description: 'This is a test security finding',
        severity: 'high',
        status: 'open',
        remediation: 'Test remediation steps',
        assignedTo: 'Test Team',
        dueDate: '2025-02-01'
      };
      
      const response = await request(app)
        .post('/security/assessment/assessments/sa-002/findings')
        .set('apikey', 'test-api-key')
        .send(newFinding);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.title).toBe(newFinding.title);
      expect(response.body.data.severity).toBe(newFinding.severity);
      expect(response.body.data).toHaveProperty('id');
    });
    
    it('should return 400 for invalid input', async () => {
      const invalidFinding = {
        // Missing required fields
        description: 'This is an invalid security finding'
      };
      
      const response = await request(app)
        .post('/security/assessment/assessments/sa-002/findings')
        .set('apikey', 'test-api-key')
        .send(invalidFinding);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 404 for non-existent assessment', async () => {
      const newFinding = {
        title: 'Test Security Finding',
        description: 'This is a test security finding',
        severity: 'high',
        status: 'open',
        remediation: 'Test remediation steps',
        assignedTo: 'Test Team',
        dueDate: '2025-02-01'
      };
      
      const response = await request(app)
        .post('/security/assessment/assessments/non-existent-id/findings')
        .set('apikey', 'test-api-key')
        .send(newFinding);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /security/assessment/findings
  describe('GET /security/assessment/findings', () => {
    it('should return a list of security findings', async () => {
      const response = await request(app)
        .get('/security/assessment/findings')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
    
    it('should filter findings by severity', async () => {
      const response = await request(app)
        .get('/security/assessment/findings?severity=high')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(finding => finding.severity === 'high')).toBe(true);
    });
    
    it('should filter findings by status', async () => {
      const response = await request(app)
        .get('/security/assessment/findings?status=open')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(finding => finding.status === 'open')).toBe(true);
    });
  });
  
  // Test GET /security/assessment/findings/:id
  describe('GET /security/assessment/findings/:id', () => {
    it('should return a specific security finding', async () => {
      const response = await request(app)
        .get('/security/assessment/findings/sf-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('sf-001');
    });
    
    it('should return 404 for non-existent finding', async () => {
      const response = await request(app)
        .get('/security/assessment/findings/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test PUT /security/assessment/findings/:id
  describe('PUT /security/assessment/findings/:id', () => {
    it('should update an existing security finding', async () => {
      const updateData = {
        title: 'Updated Security Finding',
        status: 'in-remediation'
      };
      
      const response = await request(app)
        .put('/security/assessment/findings/sf-001')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.title).toBe(updateData.title);
      expect(response.body.data.status).toBe(updateData.status);
    });
    
    it('should return 404 for non-existent finding', async () => {
      const updateData = {
        title: 'Updated Security Finding'
      };
      
      const response = await request(app)
        .put('/security/assessment/findings/non-existent-id')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /security/assessment/types
  describe('GET /security/assessment/types', () => {
    it('should return a list of assessment types', async () => {
      const response = await request(app)
        .get('/security/assessment/types')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0]).toHaveProperty('id');
      expect(response.body.data[0]).toHaveProperty('name');
      expect(response.body.data[0]).toHaveProperty('description');
    });
  });
});
