const { v4: uuidv4 } = require('uuid');
const models = require('./models');

/**
 * Get a list of compliance frameworks
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworks = (req, res) => {
  try {
    const { page = 1, limit = 10, category, sortBy = 'name', sortOrder = 'asc' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter frameworks based on query parameters
    let filteredFrameworks = [...models.complianceFrameworks];

    if (category) {
      filteredFrameworks = filteredFrameworks.filter(framework => framework.category === category);
    }

    // Sort frameworks
    filteredFrameworks.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedFrameworks = filteredFrameworks.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalFrameworks = filteredFrameworks.length;
    const totalPages = Math.ceil(totalFrameworks / limitNum);

    res.json({
      data: paginatedFrameworks,
      pagination: {
        total: totalFrameworks,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getFrameworks:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific compliance framework by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworkById = (req, res) => {
  try {
    const { id } = req.params;
    const framework = models.complianceFrameworks.find(f => f.id === id);

    if (!framework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance framework with ID ${id} not found`
      });
    }

    res.json({ data: framework });
  } catch (error) {
    console.error('Error in getFrameworkById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new compliance framework
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createFramework = (req, res) => {
  try {
    const { name, description, version, category, authority, website, applicability } = req.body;

    // Create a new framework with a unique ID
    const newFramework = {
      id: `cf-${uuidv4().substring(0, 8)}`,
      name,
      description,
      version,
      category,
      authority: authority || '',
      website: website || '',
      applicability: applicability || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new framework to the collection
    models.complianceFrameworks.push(newFramework);

    res.status(201).json({
      data: newFramework,
      message: 'Compliance framework created successfully'
    });
  } catch (error) {
    console.error('Error in createFramework:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing compliance framework
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateFramework = (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, version, category, authority, website, applicability } = req.body;

    // Find the framework to update
    const frameworkIndex = models.complianceFrameworks.findIndex(f => f.id === id);

    if (frameworkIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance framework with ID ${id} not found`
      });
    }

    // Update the framework
    const updatedFramework = {
      ...models.complianceFrameworks[frameworkIndex],
      name: name || models.complianceFrameworks[frameworkIndex].name,
      description: description || models.complianceFrameworks[frameworkIndex].description,
      version: version || models.complianceFrameworks[frameworkIndex].version,
      category: category || models.complianceFrameworks[frameworkIndex].category,
      authority: authority !== undefined ? authority : models.complianceFrameworks[frameworkIndex].authority,
      website: website !== undefined ? website : models.complianceFrameworks[frameworkIndex].website,
      applicability: applicability || models.complianceFrameworks[frameworkIndex].applicability,
      updatedAt: new Date().toISOString()
    };

    // Replace the old framework with the updated one
    models.complianceFrameworks[frameworkIndex] = updatedFramework;

    res.json({
      data: updatedFramework,
      message: 'Compliance framework updated successfully'
    });
  } catch (error) {
    console.error('Error in updateFramework:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a compliance framework
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteFramework = (req, res) => {
  try {
    const { id } = req.params;

    // Find the framework to delete
    const frameworkIndex = models.complianceFrameworks.findIndex(f => f.id === id);

    if (frameworkIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance framework with ID ${id} not found`
      });
    }

    // Check if there are any requirements for this framework
    const hasRequirements = models.complianceRequirements.some(r => r.frameworkId === id);

    if (hasRequirements) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot delete framework with ID ${id} because it has associated requirements`
      });
    }

    // Check if there are any assessments for this framework
    const hasAssessments = models.complianceAssessments.some(a => a.frameworkId === id);

    if (hasAssessments) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot delete framework with ID ${id} because it has associated assessments`
      });
    }

    // Remove the framework from the collection
    models.complianceFrameworks.splice(frameworkIndex, 1);

    res.json({
      message: 'Compliance framework deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteFramework:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get framework categories
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworkCategories = (req, res) => {
  try {
    res.json({ data: models.frameworkCategories });
  } catch (error) {
    console.error('Error in getFrameworkCategories:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get requirements for a specific framework
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworkRequirements = (req, res) => {
  try {
    const { frameworkId } = req.params;
    const { page = 1, limit = 10, category, priority, status, sortBy = 'code', sortOrder = 'asc' } = req.query;

    // Check if the framework exists
    const framework = models.complianceFrameworks.find(f => f.id === frameworkId);

    if (!framework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance framework with ID ${frameworkId} not found`
      });
    }

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter requirements based on query parameters
    let filteredRequirements = models.complianceRequirements.filter(r => r.frameworkId === frameworkId);

    if (category) {
      filteredRequirements = filteredRequirements.filter(r => r.category === category);
    }

    if (priority) {
      filteredRequirements = filteredRequirements.filter(r => r.priority === priority);
    }

    if (status) {
      filteredRequirements = filteredRequirements.filter(r => r.status === status);
    }

    // Sort requirements
    filteredRequirements.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedRequirements = filteredRequirements.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalRequirements = filteredRequirements.length;
    const totalPages = Math.ceil(totalRequirements / limitNum);

    res.json({
      data: paginatedRequirements,
      pagination: {
        total: totalRequirements,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getFrameworkRequirements:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific requirement by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getRequirementById = (req, res) => {
  try {
    const { id } = req.params;
    const requirement = models.complianceRequirements.find(r => r.id === id);

    if (!requirement) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance requirement with ID ${id} not found`
      });
    }

    res.json({ data: requirement });
  } catch (error) {
    console.error('Error in getRequirementById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new requirement for a framework
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createRequirement = (req, res) => {
  try {
    const { frameworkId } = req.params;
    const { code, title, description, category, priority, status } = req.body;

    // Check if the framework exists
    const framework = models.complianceFrameworks.find(f => f.id === frameworkId);

    if (!framework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance framework with ID ${frameworkId} not found`
      });
    }

    // Create a new requirement with a unique ID
    const newRequirement = {
      id: `cr-${uuidv4().substring(0, 8)}`,
      frameworkId,
      code,
      title,
      description,
      category: category || '',
      priority,
      status,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new requirement to the collection
    models.complianceRequirements.push(newRequirement);

    res.status(201).json({
      data: newRequirement,
      message: 'Compliance requirement created successfully'
    });
  } catch (error) {
    console.error('Error in createRequirement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing requirement
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateRequirement = (req, res) => {
  try {
    const { id } = req.params;
    const { code, title, description, category, priority, status } = req.body;

    // Find the requirement to update
    const requirementIndex = models.complianceRequirements.findIndex(r => r.id === id);

    if (requirementIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance requirement with ID ${id} not found`
      });
    }

    // Update the requirement
    const updatedRequirement = {
      ...models.complianceRequirements[requirementIndex],
      code: code || models.complianceRequirements[requirementIndex].code,
      title: title || models.complianceRequirements[requirementIndex].title,
      description: description || models.complianceRequirements[requirementIndex].description,
      category: category !== undefined ? category : models.complianceRequirements[requirementIndex].category,
      priority: priority || models.complianceRequirements[requirementIndex].priority,
      status: status || models.complianceRequirements[requirementIndex].status,
      updatedAt: new Date().toISOString()
    };

    // Replace the old requirement with the updated one
    models.complianceRequirements[requirementIndex] = updatedRequirement;

    res.json({
      data: updatedRequirement,
      message: 'Compliance requirement updated successfully'
    });
  } catch (error) {
    console.error('Error in updateRequirement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a requirement
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteRequirement = (req, res) => {
  try {
    const { id } = req.params;

    // Find the requirement to delete
    const requirementIndex = models.complianceRequirements.findIndex(r => r.id === id);

    if (requirementIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance requirement with ID ${id} not found`
      });
    }

    // Check if there are any controls linked to this requirement
    const hasControls = models.complianceControls.some(c =>
      c.relatedRequirements && c.relatedRequirements.includes(id)
    );

    if (hasControls) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot delete requirement with ID ${id} because it is linked to one or more controls`
      });
    }

    // Check if there are any assessment findings for this requirement
    const hasFindings = models.complianceAssessments.some(a =>
      a.findings && a.findings.some(f => f.requirementId === id)
    );

    if (hasFindings) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot delete requirement with ID ${id} because it has assessment findings`
      });
    }

    // Remove the requirement from the collection
    models.complianceRequirements.splice(requirementIndex, 1);

    res.json({
      message: 'Compliance requirement deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteRequirement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of all controls
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getControls = (req, res) => {
  try {
    const { page = 1, limit = 10, type, status, owner, sortBy = 'name', sortOrder = 'asc' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter controls based on query parameters
    let filteredControls = [...models.complianceControls];

    if (type) {
      filteredControls = filteredControls.filter(control => control.type === type);
    }

    if (status) {
      filteredControls = filteredControls.filter(control => control.status === status);
    }

    if (owner) {
      filteredControls = filteredControls.filter(control =>
        control.owner.toLowerCase().includes(owner.toLowerCase())
      );
    }

    // Sort controls
    filteredControls.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedControls = filteredControls.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalControls = filteredControls.length;
    const totalPages = Math.ceil(totalControls / limitNum);

    res.json({
      data: paginatedControls,
      pagination: {
        total: totalControls,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getControls:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific control by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getControlById = (req, res) => {
  try {
    const { id } = req.params;
    const control = models.complianceControls.find(c => c.id === id);

    if (!control) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance control with ID ${id} not found`
      });
    }

    res.json({ data: control });
  } catch (error) {
    console.error('Error in getControlById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createControl = (req, res) => {
  try {
    const {
      name,
      description,
      type,
      status,
      owner,
      implementationDetails,
      testProcedure,
      lastTestedDate,
      nextTestDate,
      relatedRequirements
    } = req.body;

    // Validate that all related requirements exist
    if (relatedRequirements && relatedRequirements.length > 0) {
      const invalidRequirements = relatedRequirements.filter(
        reqId => !models.complianceRequirements.some(r => r.id === reqId)
      );

      if (invalidRequirements.length > 0) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `The following requirement IDs do not exist: ${invalidRequirements.join(', ')}`
        });
      }
    }

    // Create a new control with a unique ID
    const newControl = {
      id: `cc-${uuidv4().substring(0, 8)}`,
      name,
      description,
      type,
      status,
      owner,
      implementationDetails: implementationDetails || '',
      testProcedure: testProcedure || '',
      lastTestedDate: lastTestedDate || '',
      nextTestDate: nextTestDate || '',
      relatedRequirements: relatedRequirements || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new control to the collection
    models.complianceControls.push(newControl);

    res.status(201).json({
      data: newControl,
      message: 'Compliance control created successfully'
    });
  } catch (error) {
    console.error('Error in createControl:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateControl = (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      type,
      status,
      owner,
      implementationDetails,
      testProcedure,
      lastTestedDate,
      nextTestDate,
      relatedRequirements
    } = req.body;

    // Find the control to update
    const controlIndex = models.complianceControls.findIndex(c => c.id === id);

    if (controlIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance control with ID ${id} not found`
      });
    }

    // Validate that all related requirements exist
    if (relatedRequirements && relatedRequirements.length > 0) {
      const invalidRequirements = relatedRequirements.filter(
        reqId => !models.complianceRequirements.some(r => r.id === reqId)
      );

      if (invalidRequirements.length > 0) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `The following requirement IDs do not exist: ${invalidRequirements.join(', ')}`
        });
      }
    }

    // Update the control
    const updatedControl = {
      ...models.complianceControls[controlIndex],
      name: name || models.complianceControls[controlIndex].name,
      description: description || models.complianceControls[controlIndex].description,
      type: type || models.complianceControls[controlIndex].type,
      status: status || models.complianceControls[controlIndex].status,
      owner: owner || models.complianceControls[controlIndex].owner,
      implementationDetails: implementationDetails !== undefined ? implementationDetails : models.complianceControls[controlIndex].implementationDetails,
      testProcedure: testProcedure !== undefined ? testProcedure : models.complianceControls[controlIndex].testProcedure,
      lastTestedDate: lastTestedDate !== undefined ? lastTestedDate : models.complianceControls[controlIndex].lastTestedDate,
      nextTestDate: nextTestDate !== undefined ? nextTestDate : models.complianceControls[controlIndex].nextTestDate,
      relatedRequirements: relatedRequirements || models.complianceControls[controlIndex].relatedRequirements,
      updatedAt: new Date().toISOString()
    };

    // Replace the old control with the updated one
    models.complianceControls[controlIndex] = updatedControl;

    res.json({
      data: updatedControl,
      message: 'Compliance control updated successfully'
    });
  } catch (error) {
    console.error('Error in updateControl:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteControl = (req, res) => {
  try {
    const { id } = req.params;

    // Find the control to delete
    const controlIndex = models.complianceControls.findIndex(c => c.id === id);

    if (controlIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance control with ID ${id} not found`
      });
    }

    // Check if there is any evidence linked to this control
    const hasEvidence = models.complianceEvidence.some(e => e.controlId === id);

    if (hasEvidence) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot delete control with ID ${id} because it has associated evidence`
      });
    }

    // Remove the control from the collection
    models.complianceControls.splice(controlIndex, 1);

    res.json({
      message: 'Compliance control deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteControl:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get controls for a specific requirement
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getRequirementControls = (req, res) => {
  try {
    const { requirementId } = req.params;
    const { page = 1, limit = 10, type, status, sortBy = 'name', sortOrder = 'asc' } = req.query;

    // Check if the requirement exists
    const requirement = models.complianceRequirements.find(r => r.id === requirementId);

    if (!requirement) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance requirement with ID ${requirementId} not found`
      });
    }

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter controls based on the requirement and query parameters
    let filteredControls = models.complianceControls.filter(control =>
      control.relatedRequirements && control.relatedRequirements.includes(requirementId)
    );

    if (type) {
      filteredControls = filteredControls.filter(control => control.type === type);
    }

    if (status) {
      filteredControls = filteredControls.filter(control => control.status === status);
    }

    // Sort controls
    filteredControls.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedControls = filteredControls.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalControls = filteredControls.length;
    const totalPages = Math.ceil(totalControls / limitNum);

    res.json({
      data: paginatedControls,
      pagination: {
        total: totalControls,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getRequirementControls:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Add a control to a requirement
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const addControlToRequirement = (req, res) => {
  try {
    const { requirementId } = req.params;
    const { controlId } = req.body;

    // Check if the requirement exists
    const requirement = models.complianceRequirements.find(r => r.id === requirementId);

    if (!requirement) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance requirement with ID ${requirementId} not found`
      });
    }

    // Check if the control exists
    const controlIndex = models.complianceControls.findIndex(c => c.id === controlId);

    if (controlIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance control with ID ${controlId} not found`
      });
    }

    // Check if the control is already linked to the requirement
    const control = models.complianceControls[controlIndex];

    if (control.relatedRequirements && control.relatedRequirements.includes(requirementId)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Control with ID ${controlId} is already linked to requirement with ID ${requirementId}`
      });
    }

    // Add the requirement to the control's relatedRequirements array
    const updatedControl = {
      ...control,
      relatedRequirements: [...(control.relatedRequirements || []), requirementId],
      updatedAt: new Date().toISOString()
    };

    // Update the control
    models.complianceControls[controlIndex] = updatedControl;

    res.json({
      data: updatedControl,
      message: `Control with ID ${controlId} successfully linked to requirement with ID ${requirementId}`
    });
  } catch (error) {
    console.error('Error in addControlToRequirement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get control types
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getControlTypes = (req, res) => {
  try {
    res.json({ data: models.controlTypes });
  } catch (error) {
    console.error('Error in getControlTypes:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of all assessments
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAssessments = (req, res) => {
  try {
    const { page = 1, limit = 10, frameworkId, assessor, status, sortBy = 'startDate', sortOrder = 'desc' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter assessments based on query parameters
    let filteredAssessments = [...models.complianceAssessments];

    if (frameworkId) {
      filteredAssessments = filteredAssessments.filter(assessment => assessment.frameworkId === frameworkId);
    }

    if (assessor) {
      filteredAssessments = filteredAssessments.filter(assessment =>
        assessment.assessor.toLowerCase().includes(assessor.toLowerCase())
      );
    }

    if (status) {
      filteredAssessments = filteredAssessments.filter(assessment => assessment.status === status);
    }

    // Sort assessments
    filteredAssessments.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedAssessments = filteredAssessments.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalAssessments = filteredAssessments.length;
    const totalPages = Math.ceil(totalAssessments / limitNum);

    res.json({
      data: paginatedAssessments,
      pagination: {
        total: totalAssessments,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getAssessments:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific assessment by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAssessmentById = (req, res) => {
  try {
    const { id } = req.params;
    const assessment = models.complianceAssessments.find(a => a.id === id);

    if (!assessment) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance assessment with ID ${id} not found`
      });
    }

    res.json({ data: assessment });
  } catch (error) {
    console.error('Error in getAssessmentById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createAssessment = (req, res) => {
  try {
    const {
      frameworkId,
      name,
      description,
      assessor,
      startDate,
      endDate,
      status,
      scope,
      findings
    } = req.body;

    // Check if the framework exists
    const framework = models.complianceFrameworks.find(f => f.id === frameworkId);

    if (!framework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance framework with ID ${frameworkId} not found`
      });
    }

    // Validate findings if provided
    if (findings && findings.length > 0) {
      // Check if all requirement IDs exist
      const invalidRequirements = findings.filter(
        finding => !models.complianceRequirements.some(r => r.id === finding.requirementId)
      ).map(finding => finding.requirementId);

      if (invalidRequirements.length > 0) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `The following requirement IDs do not exist: ${invalidRequirements.join(', ')}`
        });
      }
    }

    // Create a new assessment with a unique ID
    const newAssessment = {
      id: `ca-${uuidv4().substring(0, 8)}`,
      frameworkId,
      name,
      description: description || '',
      assessor,
      startDate,
      endDate: endDate || '',
      status,
      scope: scope || '',
      findings: findings ? findings.map(finding => ({
        id: `f-${uuidv4().substring(0, 8)}`,
        ...finding
      })) : [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new assessment to the collection
    models.complianceAssessments.push(newAssessment);

    res.status(201).json({
      data: newAssessment,
      message: 'Compliance assessment created successfully'
    });
  } catch (error) {
    console.error('Error in createAssessment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateAssessment = (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      assessor,
      startDate,
      endDate,
      status,
      scope,
      findings
    } = req.body;

    // Find the assessment to update
    const assessmentIndex = models.complianceAssessments.findIndex(a => a.id === id);

    if (assessmentIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance assessment with ID ${id} not found`
      });
    }

    // Validate findings if provided
    if (findings && findings.length > 0) {
      // Check if all requirement IDs exist
      const invalidRequirements = findings.filter(
        finding => !models.complianceRequirements.some(r => r.id === finding.requirementId)
      ).map(finding => finding.requirementId);

      if (invalidRequirements.length > 0) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `The following requirement IDs do not exist: ${invalidRequirements.join(', ')}`
        });
      }
    }

    // Update the assessment
    const currentAssessment = models.complianceAssessments[assessmentIndex];
    const updatedAssessment = {
      ...currentAssessment,
      name: name || currentAssessment.name,
      description: description !== undefined ? description : currentAssessment.description,
      assessor: assessor || currentAssessment.assessor,
      startDate: startDate || currentAssessment.startDate,
      endDate: endDate !== undefined ? endDate : currentAssessment.endDate,
      status: status || currentAssessment.status,
      scope: scope !== undefined ? scope : currentAssessment.scope,
      findings: findings ? findings.map(finding => {
        // If finding has an ID, it's an existing finding being updated
        if (finding.id) {
          return finding;
        }
        // Otherwise, it's a new finding
        return {
          id: `f-${uuidv4().substring(0, 8)}`,
          ...finding
        };
      }) : currentAssessment.findings,
      updatedAt: new Date().toISOString()
    };

    // Replace the old assessment with the updated one
    models.complianceAssessments[assessmentIndex] = updatedAssessment;

    res.json({
      data: updatedAssessment,
      message: 'Compliance assessment updated successfully'
    });
  } catch (error) {
    console.error('Error in updateAssessment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete an assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteAssessment = (req, res) => {
  try {
    const { id } = req.params;

    // Find the assessment to delete
    const assessmentIndex = models.complianceAssessments.findIndex(a => a.id === id);

    if (assessmentIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance assessment with ID ${id} not found`
      });
    }

    // Remove the assessment from the collection
    models.complianceAssessments.splice(assessmentIndex, 1);

    res.json({
      message: 'Compliance assessment deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteAssessment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Start an assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const startAssessment = (req, res) => {
  try {
    const { id } = req.params;
    const { assessor, assessorType, startDate = new Date().toISOString().split('T')[0] } = req.body;

    // Find the assessment to update
    const assessmentIndex = models.complianceAssessments.findIndex(a => a.id === id);

    if (assessmentIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance assessment with ID ${id} not found`
      });
    }

    const assessment = models.complianceAssessments[assessmentIndex];

    // Check if the assessment is in a state that can be started
    if (assessment.status !== 'planned') {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Assessment cannot be started because it is in '${assessment.status}' status. Only assessments in 'planned' status can be started.`
      });
    }

    // Update the assessment
    const updatedAssessment = {
      ...assessment,
      status: 'in-progress',
      startDate: startDate || new Date().toISOString().split('T')[0],
      assessor: assessor || assessment.assessor,
      assessorType: assessorType || assessment.assessorType,
      updatedAt: new Date().toISOString()
    };

    // Replace the old assessment with the updated one
    models.complianceAssessments[assessmentIndex] = updatedAssessment;

    res.json({
      data: updatedAssessment,
      message: 'Assessment started successfully'
    });
  } catch (error) {
    console.error('Error in startAssessment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Complete an assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const completeAssessment = (req, res) => {
  try {
    const { id } = req.params;
    const {
      endDate = new Date().toISOString().split('T')[0],
      overallScore,
      findings,
      recommendations
    } = req.body;

    // Find the assessment to update
    const assessmentIndex = models.complianceAssessments.findIndex(a => a.id === id);

    if (assessmentIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance assessment with ID ${id} not found`
      });
    }

    const assessment = models.complianceAssessments[assessmentIndex];

    // Check if the assessment is in a state that can be completed
    if (assessment.status !== 'in-progress') {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Assessment cannot be completed because it is in '${assessment.status}' status. Only assessments in 'in-progress' status can be completed.`
      });
    }

    // Update the assessment
    const updatedAssessment = {
      ...assessment,
      status: 'completed',
      endDate: endDate || new Date().toISOString().split('T')[0],
      overallScore: overallScore !== undefined ? overallScore : assessment.overallScore,
      findings: findings || assessment.findings,
      recommendations: recommendations || assessment.recommendations,
      updatedAt: new Date().toISOString()
    };

    // Replace the old assessment with the updated one
    models.complianceAssessments[assessmentIndex] = updatedAssessment;

    // If this is a framework assessment, update the framework's compliance score and last assessment date
    if (updatedAssessment.frameworkId) {
      const frameworkIndex = models.complianceFrameworks.findIndex(f => f.id === updatedAssessment.frameworkId);
      if (frameworkIndex !== -1) {
        models.complianceFrameworks[frameworkIndex] = {
          ...models.complianceFrameworks[frameworkIndex],
          complianceScore: overallScore !== undefined ? overallScore : models.complianceFrameworks[frameworkIndex].complianceScore,
          lastAssessmentDate: endDate || new Date().toISOString().split('T')[0],
          updatedAt: new Date().toISOString()
        };
      }
    }

    res.json({
      data: updatedAssessment,
      message: 'Assessment completed successfully'
    });
  } catch (error) {
    console.error('Error in completeAssessment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get assessment results
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAssessmentResults = (req, res) => {
  try {
    const { id } = req.params;

    // Find the assessment
    const assessment = models.complianceAssessments.find(a => a.id === id);

    if (!assessment) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance assessment with ID ${id} not found`
      });
    }

    // Check if the assessment is completed
    if (assessment.status !== 'completed') {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Assessment results are not available because the assessment is in '${assessment.status}' status. Only completed assessments have results.`
      });
    }

    // Get the framework details if this is a framework assessment
    let framework = null;
    if (assessment.frameworkId) {
      framework = models.complianceFrameworks.find(f => f.id === assessment.frameworkId);
    }

    // Calculate statistics
    const findingsBySeverity = {
      critical: assessment.findings?.filter(f => f.severity === 'critical').length || 0,
      high: assessment.findings?.filter(f => f.severity === 'high').length || 0,
      medium: assessment.findings?.filter(f => f.severity === 'medium').length || 0,
      low: assessment.findings?.filter(f => f.severity === 'low').length || 0
    };

    const findingsByStatus = {
      open: assessment.findings?.filter(f => f.status === 'open').length || 0,
      'in-remediation': assessment.findings?.filter(f => f.status === 'in-remediation').length || 0,
      closed: assessment.findings?.filter(f => f.status === 'closed').length || 0
    };

    // Generate results object
    const results = {
      assessmentId: assessment.id,
      assessmentName: assessment.name,
      status: assessment.status,
      startDate: assessment.startDate,
      endDate: assessment.endDate,
      assessor: assessment.assessor,
      assessorType: assessment.assessorType,
      overallScore: assessment.overallScore,
      framework: framework ? {
        id: framework.id,
        name: framework.name,
        version: framework.version
      } : null,
      summary: {
        totalFindings: assessment.findings?.length || 0,
        findingsBySeverity,
        findingsByStatus,
        totalRecommendations: assessment.recommendations?.length || 0
      },
      findings: assessment.findings || [],
      recommendations: assessment.recommendations || [],
      generatedAt: new Date().toISOString()
    };

    res.json({
      data: results
    });
  } catch (error) {
    console.error('Error in getAssessmentResults:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get assessments for a specific framework
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworkAssessments = (req, res) => {
  try {
    const { frameworkId } = req.params;
    const { page = 1, limit = 10, status, sortBy = 'startDate', sortOrder = 'desc' } = req.query;

    // Check if the framework exists
    const framework = models.complianceFrameworks.find(f => f.id === frameworkId);

    if (!framework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance framework with ID ${frameworkId} not found`
      });
    }

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter assessments based on the framework and query parameters
    let filteredAssessments = models.complianceAssessments.filter(a => a.frameworkId === frameworkId);

    if (status) {
      filteredAssessments = filteredAssessments.filter(a => a.status === status);
    }

    // Sort assessments
    filteredAssessments.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedAssessments = filteredAssessments.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalAssessments = filteredAssessments.length;
    const totalPages = Math.ceil(totalAssessments / limitNum);

    res.json({
      data: paginatedAssessments,
      pagination: {
        total: totalAssessments,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getFrameworkAssessments:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get evidence for a specific control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getControlEvidence = (req, res) => {
  try {
    const { controlId } = req.params;
    const { page = 1, limit = 10, type, status, sortBy = 'collectedAt', sortOrder = 'desc' } = req.query;

    // Check if the control exists
    const control = models.complianceControls.find(c => c.id === controlId);

    if (!control) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance control with ID ${controlId} not found`
      });
    }

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter evidence based on the control and query parameters
    let filteredEvidence = models.complianceEvidence.filter(e => e.controlId === controlId);

    if (type) {
      filteredEvidence = filteredEvidence.filter(e => e.type === type);
    }

    if (status) {
      filteredEvidence = filteredEvidence.filter(e => e.status === status);
    }

    // Sort evidence
    filteredEvidence.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedEvidence = filteredEvidence.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalEvidence = filteredEvidence.length;
    const totalPages = Math.ceil(totalEvidence / limitNum);

    res.json({
      data: paginatedEvidence,
      pagination: {
        total: totalEvidence,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getControlEvidence:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific evidence by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getEvidenceById = (req, res) => {
  try {
    const { id } = req.params;
    const evidence = models.complianceEvidence.find(e => e.id === id);

    if (!evidence) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance evidence with ID ${id} not found`
      });
    }

    res.json({ data: evidence });
  } catch (error) {
    console.error('Error in getEvidenceById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create new evidence for a control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createEvidence = (req, res) => {
  try {
    const { controlId } = req.params;
    const {
      name,
      description,
      type,
      location,
      collectedBy,
      collectedAt,
      expiresAt,
      status
    } = req.body;

    // Check if the control exists
    const control = models.complianceControls.find(c => c.id === controlId);

    if (!control) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance control with ID ${controlId} not found`
      });
    }

    // Create a new evidence with a unique ID
    const newEvidence = {
      id: `ce-${uuidv4().substring(0, 8)}`,
      controlId,
      name,
      description: description || '',
      type,
      location,
      collectedBy,
      collectedAt,
      expiresAt: expiresAt || '',
      status,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new evidence to the collection
    models.complianceEvidence.push(newEvidence);

    res.status(201).json({
      data: newEvidence,
      message: 'Compliance evidence created successfully'
    });
  } catch (error) {
    console.error('Error in createEvidence:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing evidence
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateEvidence = (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      type,
      location,
      collectedBy,
      collectedAt,
      expiresAt,
      status
    } = req.body;

    // Find the evidence to update
    const evidenceIndex = models.complianceEvidence.findIndex(e => e.id === id);

    if (evidenceIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance evidence with ID ${id} not found`
      });
    }

    // Update the evidence
    const currentEvidence = models.complianceEvidence[evidenceIndex];
    const updatedEvidence = {
      ...currentEvidence,
      name: name || currentEvidence.name,
      description: description !== undefined ? description : currentEvidence.description,
      type: type || currentEvidence.type,
      location: location || currentEvidence.location,
      collectedBy: collectedBy || currentEvidence.collectedBy,
      collectedAt: collectedAt || currentEvidence.collectedAt,
      expiresAt: expiresAt !== undefined ? expiresAt : currentEvidence.expiresAt,
      status: status || currentEvidence.status,
      updatedAt: new Date().toISOString()
    };

    // Replace the old evidence with the updated one
    models.complianceEvidence[evidenceIndex] = updatedEvidence;

    res.json({
      data: updatedEvidence,
      message: 'Compliance evidence updated successfully'
    });
  } catch (error) {
    console.error('Error in updateEvidence:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete an evidence
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteEvidence = (req, res) => {
  try {
    const { id } = req.params;

    // Find the evidence to delete
    const evidenceIndex = models.complianceEvidence.findIndex(e => e.id === id);

    if (evidenceIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Compliance evidence with ID ${id} not found`
      });
    }

    // Remove the evidence from the collection
    models.complianceEvidence.splice(evidenceIndex, 1);

    res.json({
      message: 'Compliance evidence deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteEvidence:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of automation rules
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAutomationRules = (req, res) => {
  try {
    const { page = 1, limit = 10, requirementId, triggerType, status, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter rules based on query parameters
    let filteredRules = [...models.complianceAutomationRules];

    if (requirementId) {
      filteredRules = filteredRules.filter(rule => rule.requirementId === requirementId);
    }

    if (triggerType) {
      filteredRules = filteredRules.filter(rule => rule.triggerType === triggerType);
    }

    if (status) {
      filteredRules = filteredRules.filter(rule => rule.status === status);
    }

    // Sort rules
    filteredRules.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedRules = filteredRules.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalRules = filteredRules.length;
    const totalPages = Math.ceil(totalRules / limitNum);

    res.json({
      data: paginatedRules,
      pagination: {
        total: totalRules,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getAutomationRules:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific automation rule by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAutomationRuleById = (req, res) => {
  try {
    const { id } = req.params;

    // Find the rule
    const rule = models.complianceAutomationRules.find(r => r.id === id);

    if (!rule) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Automation rule with ID ${id} not found`
      });
    }

    res.json({
      data: rule
    });
  } catch (error) {
    console.error('Error in getAutomationRuleById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new automation rule
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createAutomationRule = (req, res) => {
  try {
    const {
      name,
      description,
      requirementId,
      triggerType,
      triggerCondition,
      actions,
      schedule,
      frequency,
      status = 'active'
    } = req.body;

    // Validate that the requirement exists
    const requirement = models.complianceRequirements.find(r => r.id === requirementId);
    if (!requirement) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Requirement with ID ${requirementId} not found`
      });
    }

    // Validate actions
    if (!Array.isArray(actions) || actions.length === 0) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'At least one action must be specified'
      });
    }

    // For scheduled rules, ensure schedule is provided
    if (triggerType === 'scheduled' && !schedule) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Schedule is required for scheduled rules'
      });
    }

    // Generate next execution date for scheduled rules
    let nextExecutionDate = null;
    if (triggerType === 'scheduled') {
      // In a real implementation, this would calculate the next execution date based on the schedule
      // For now, we'll just set it to a future date
      const now = new Date();
      nextExecutionDate = new Date(now.setDate(now.getDate() + 7)).toISOString(); // 7 days from now
    }

    // Create a new rule with a unique ID
    const newRule = {
      id: `ar-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
      name,
      description,
      requirementId,
      triggerType,
      triggerCondition,
      actions,
      schedule: schedule || null,
      frequency: frequency || null,
      status,
      lastExecutionDate: null,
      nextExecutionDate,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new rule to the collection
    models.complianceAutomationRules.push(newRule);

    res.status(201).json({
      data: newRule,
      message: 'Automation rule created successfully'
    });
  } catch (error) {
    console.error('Error in createAutomationRule:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an automation rule
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateAutomationRule = (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      requirementId,
      triggerType,
      triggerCondition,
      actions,
      schedule,
      frequency,
      status
    } = req.body;

    // Find the rule to update
    const ruleIndex = models.complianceAutomationRules.findIndex(r => r.id === id);

    if (ruleIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Automation rule with ID ${id} not found`
      });
    }

    const currentRule = models.complianceAutomationRules[ruleIndex];

    // If requirementId is being updated, validate that the requirement exists
    if (requirementId && requirementId !== currentRule.requirementId) {
      const requirement = models.complianceRequirements.find(r => r.id === requirementId);
      if (!requirement) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `Requirement with ID ${requirementId} not found`
        });
      }
    }

    // If actions are being updated, validate them
    if (actions && (!Array.isArray(actions) || actions.length === 0)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'At least one action must be specified'
      });
    }

    // For scheduled rules, ensure schedule is provided
    if (triggerType === 'scheduled' && !schedule && !currentRule.schedule) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Schedule is required for scheduled rules'
      });
    }

    // Generate next execution date for scheduled rules if the schedule is being updated
    let nextExecutionDate = currentRule.nextExecutionDate;
    if (triggerType === 'scheduled' && schedule && schedule !== currentRule.schedule) {
      // In a real implementation, this would calculate the next execution date based on the schedule
      // For now, we'll just set it to a future date
      const now = new Date();
      nextExecutionDate = new Date(now.setDate(now.getDate() + 7)).toISOString(); // 7 days from now
    }

    // Update the rule
    const updatedRule = {
      ...currentRule,
      name: name || currentRule.name,
      description: description !== undefined ? description : currentRule.description,
      requirementId: requirementId || currentRule.requirementId,
      triggerType: triggerType || currentRule.triggerType,
      triggerCondition: triggerCondition !== undefined ? triggerCondition : currentRule.triggerCondition,
      actions: actions || currentRule.actions,
      schedule: schedule !== undefined ? schedule : currentRule.schedule,
      frequency: frequency !== undefined ? frequency : currentRule.frequency,
      status: status || currentRule.status,
      nextExecutionDate: nextExecutionDate,
      updatedAt: new Date().toISOString()
    };

    // Replace the old rule with the updated one
    models.complianceAutomationRules[ruleIndex] = updatedRule;

    res.json({
      data: updatedRule,
      message: 'Automation rule updated successfully'
    });
  } catch (error) {
    console.error('Error in updateAutomationRule:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete an automation rule
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteAutomationRule = (req, res) => {
  try {
    const { id } = req.params;

    // Find the rule to delete
    const ruleIndex = models.complianceAutomationRules.findIndex(r => r.id === id);

    if (ruleIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Automation rule with ID ${id} not found`
      });
    }

    // Remove the rule from the collection
    models.complianceAutomationRules.splice(ruleIndex, 1);

    res.json({
      message: 'Automation rule deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteAutomationRule:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Execute an automation rule
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const executeAutomationRule = (req, res) => {
  try {
    const { id } = req.params;

    // Find the rule to execute
    const ruleIndex = models.complianceAutomationRules.findIndex(r => r.id === id);

    if (ruleIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Automation rule with ID ${id} not found`
      });
    }

    const rule = models.complianceAutomationRules[ruleIndex];

    // Check if the rule is active
    if (rule.status !== 'active') {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Rule cannot be executed because it is in '${rule.status}' status. Only active rules can be executed.`
      });
    }

    // In a real implementation, this would execute the rule's actions
    // For now, we'll just update the lastExecutionDate and nextExecutionDate

    // Execute actions (simulated)
    const executionResults = [];
    for (const action of rule.actions) {
      // Simulate action execution
      let result = {
        type: action.type,
        status: 'success',
        message: `Action '${action.type}' executed successfully`
      };

      // Simulate different outcomes based on action type
      switch (action.type) {
        case 'collect-evidence':
          // Simulate evidence collection
          const evidenceId = `ce-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;
          result.details = {
            evidenceId,
            name: `Evidence from rule ${rule.name}`,
            type: action.parameters?.evidenceType || 'document',
            collectedAt: new Date().toISOString()
          };
          break;

        case 'notify':
          // Simulate notification
          result.details = {
            recipients: action.parameters?.recipients || [],
            sentAt: new Date().toISOString()
          };
          break;

        case 'update-status':
          // Simulate status update
          result.details = {
            requirementId: action.parameters?.requirementId || rule.requirementId,
            oldStatus: 'unknown',
            newStatus: action.parameters?.status || 'compliant'
          };
          break;

        case 'create-task':
          // Simulate task creation
          const taskId = `task-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;
          result.details = {
            taskId,
            title: action.parameters?.title || `Task from rule ${rule.name}`,
            assignee: action.parameters?.assignee || 'Compliance Team',
            dueDate: action.parameters?.dueDate || 'N/A'
          };
          break;

        default:
          result.status = 'warning';
          result.message = `Unknown action type '${action.type}'`;
      }

      executionResults.push(result);
    }

    // Calculate next execution date for scheduled rules
    let nextExecutionDate = null;
    if (rule.triggerType === 'scheduled') {
      // In a real implementation, this would calculate the next execution date based on the schedule
      // For now, we'll just set it to a future date
      const now = new Date();
      nextExecutionDate = new Date(now.setDate(now.getDate() + 7)).toISOString(); // 7 days from now
    }

    // Update the rule
    const updatedRule = {
      ...rule,
      lastExecutionDate: new Date().toISOString(),
      nextExecutionDate: nextExecutionDate,
      updatedAt: new Date().toISOString()
    };

    // Replace the old rule with the updated one
    models.complianceAutomationRules[ruleIndex] = updatedRule;

    res.json({
      data: {
        rule: updatedRule,
        executionResults
      },
      message: 'Automation rule executed successfully'
    });
  } catch (error) {
    console.error('Error in executeAutomationRule:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Enable an automation rule
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const enableAutomationRule = (req, res) => {
  try {
    const { id } = req.params;

    // Find the rule to enable
    const ruleIndex = models.complianceAutomationRules.findIndex(r => r.id === id);

    if (ruleIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Automation rule with ID ${id} not found`
      });
    }

    const rule = models.complianceAutomationRules[ruleIndex];

    // Check if the rule is already active
    if (rule.status === 'active') {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Rule is already active'
      });
    }

    // Update the rule
    const updatedRule = {
      ...rule,
      status: 'active',
      updatedAt: new Date().toISOString()
    };

    // Replace the old rule with the updated one
    models.complianceAutomationRules[ruleIndex] = updatedRule;

    res.json({
      data: updatedRule,
      message: 'Automation rule enabled successfully'
    });
  } catch (error) {
    console.error('Error in enableAutomationRule:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Disable an automation rule
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const disableAutomationRule = (req, res) => {
  try {
    const { id } = req.params;

    // Find the rule to disable
    const ruleIndex = models.complianceAutomationRules.findIndex(r => r.id === id);

    if (ruleIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Automation rule with ID ${id} not found`
      });
    }

    const rule = models.complianceAutomationRules[ruleIndex];

    // Check if the rule is already inactive
    if (rule.status === 'inactive') {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Rule is already inactive'
      });
    }

    // Update the rule
    const updatedRule = {
      ...rule,
      status: 'inactive',
      updatedAt: new Date().toISOString()
    };

    // Replace the old rule with the updated one
    models.complianceAutomationRules[ruleIndex] = updatedRule;

    res.json({
      data: updatedRule,
      message: 'Automation rule disabled successfully'
    });
  } catch (error) {
    console.error('Error in disableAutomationRule:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get automation rules for a specific requirement
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getRequirementAutomationRules = (req, res) => {
  try {
    const { requirementId } = req.params;
    const { page = 1, limit = 10, status, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

    // Check if the requirement exists
    const requirement = models.complianceRequirements.find(r => r.id === requirementId);
    if (!requirement) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Requirement with ID ${requirementId} not found`
      });
    }

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter rules for the specified requirement
    let filteredRules = models.complianceAutomationRules.filter(rule => rule.requirementId === requirementId);

    // Apply additional filters
    if (status) {
      filteredRules = filteredRules.filter(rule => rule.status === status);
    }

    // Sort rules
    filteredRules.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedRules = filteredRules.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalRules = filteredRules.length;
    const totalPages = Math.ceil(totalRules / limitNum);

    res.json({
      data: paginatedRules,
      pagination: {
        total: totalRules,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getRequirementAutomationRules:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get compliance dashboard data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getComplianceDashboard = (req, res) => {
  try {
    // Calculate framework compliance statistics
    const frameworkStats = {
      total: models.complianceFrameworks.length,
      compliant: 0,
      partiallyCompliant: 0,
      nonCompliant: 0,
      notAssessed: 0
    };

    models.complianceFrameworks.forEach(framework => {
      if (!framework.complianceScore) {
        frameworkStats.notAssessed++;
      } else if (framework.complianceScore >= 90) {
        frameworkStats.compliant++;
      } else if (framework.complianceScore >= 60) {
        frameworkStats.partiallyCompliant++;
      } else {
        frameworkStats.nonCompliant++;
      }
    });

    // Calculate requirement compliance statistics
    const requirementStats = {
      total: models.complianceRequirements.length,
      applicable: models.complianceRequirements.filter(r => r.status === 'applicable').length,
      notApplicable: models.complianceRequirements.filter(r => r.status === 'not-applicable').length,
      underReview: models.complianceRequirements.filter(r => r.status === 'under-review').length,
      byPriority: {
        critical: models.complianceRequirements.filter(r => r.priority === 'critical').length,
        high: models.complianceRequirements.filter(r => r.priority === 'high').length,
        medium: models.complianceRequirements.filter(r => r.priority === 'medium').length,
        low: models.complianceRequirements.filter(r => r.priority === 'low').length
      }
    };

    // Calculate control implementation statistics
    const controlStats = {
      total: models.complianceControls.length,
      implemented: models.complianceControls.filter(c => c.status === 'implemented').length,
      partiallyImplemented: models.complianceControls.filter(c => c.status === 'partially-implemented').length,
      notImplemented: models.complianceControls.filter(c => c.status === 'not-implemented').length,
      planned: models.complianceControls.filter(c => c.status === 'planned').length,
      byType: {
        preventive: models.complianceControls.filter(c => c.type === 'preventive').length,
        detective: models.complianceControls.filter(c => c.type === 'detective').length,
        corrective: models.complianceControls.filter(c => c.type === 'corrective').length,
        administrative: models.complianceControls.filter(c => c.type === 'administrative').length,
        technical: models.complianceControls.filter(c => c.type === 'technical').length,
        physical: models.complianceControls.filter(c => c.type === 'physical').length
      },
      automationStatus: {
        manual: models.complianceControls.filter(c => !c.automationDetails).length,
        semiAutomated: models.complianceControls.filter(c => c.automationDetails && c.automationStatus === 'semi-automated').length,
        fullyAutomated: models.complianceControls.filter(c => c.automationDetails && c.automationStatus === 'fully-automated').length
      }
    };

    // Calculate assessment statistics
    const assessmentStats = {
      total: models.complianceAssessments.length,
      planned: models.complianceAssessments.filter(a => a.status === 'planned').length,
      inProgress: models.complianceAssessments.filter(a => a.status === 'in-progress').length,
      completed: models.complianceAssessments.filter(a => a.status === 'completed').length,
      cancelled: models.complianceAssessments.filter(a => a.status === 'cancelled').length,
      averageScore: calculateAverageScore(models.complianceAssessments)
    };

    // Calculate automation rule statistics
    const automationStats = {
      total: models.complianceAutomationRules.length,
      active: models.complianceAutomationRules.filter(r => r.status === 'active').length,
      inactive: models.complianceAutomationRules.filter(r => r.status === 'inactive').length,
      draft: models.complianceAutomationRules.filter(r => r.status === 'draft').length,
      byTriggerType: {
        scheduled: models.complianceAutomationRules.filter(r => r.triggerType === 'scheduled').length,
        eventBased: models.complianceAutomationRules.filter(r => r.triggerType === 'event-based').length,
        manual: models.complianceAutomationRules.filter(r => r.triggerType === 'manual').length
      }
    };

    // Get recent assessments
    const recentAssessments = models.complianceAssessments
      .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
      .slice(0, 5)
      .map(assessment => ({
        id: assessment.id,
        name: assessment.name,
        status: assessment.status,
        frameworkId: assessment.frameworkId,
        startDate: assessment.startDate,
        endDate: assessment.endDate,
        overallScore: assessment.overallScore,
        updatedAt: assessment.updatedAt
      }));

    // Get upcoming scheduled rules
    const upcomingRules = models.complianceAutomationRules
      .filter(rule => rule.status === 'active' && rule.nextExecutionDate)
      .sort((a, b) => new Date(a.nextExecutionDate) - new Date(b.nextExecutionDate))
      .slice(0, 5)
      .map(rule => ({
        id: rule.id,
        name: rule.name,
        triggerType: rule.triggerType,
        nextExecutionDate: rule.nextExecutionDate
      }));

    // Compile dashboard data
    const dashboardData = {
      overallComplianceScore: calculateOverallComplianceScore(models.complianceFrameworks),
      frameworkStats,
      requirementStats,
      controlStats,
      assessmentStats,
      automationStats,
      recentAssessments,
      upcomingRules,
      generatedAt: new Date().toISOString()
    };

    res.json({
      data: dashboardData
    });
  } catch (error) {
    console.error('Error in getComplianceDashboard:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Calculate average score from assessments
 * @param {Array} assessments - Array of assessment objects
 * @returns {number} - Average score
 */
function calculateAverageScore(assessments) {
  const completedAssessments = assessments.filter(a => a.status === 'completed' && a.overallScore !== null && a.overallScore !== undefined);
  if (completedAssessments.length === 0) return 0;

  const sum = completedAssessments.reduce((total, assessment) => total + assessment.overallScore, 0);
  return parseFloat((sum / completedAssessments.length).toFixed(2));
}

/**
 * Calculate overall compliance score from frameworks
 * @param {Array} frameworks - Array of framework objects
 * @returns {number} - Overall compliance score
 */
function calculateOverallComplianceScore(frameworks) {
  const assessedFrameworks = frameworks.filter(f => f.complianceScore !== null && f.complianceScore !== undefined);
  if (assessedFrameworks.length === 0) return 0;

  const sum = assessedFrameworks.reduce((total, framework) => total + framework.complianceScore, 0);
  return parseFloat((sum / assessedFrameworks.length).toFixed(2));
}

/**
 * Get automation coverage report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAutomationCoverageReport = (req, res) => {
  try {
    const { frameworkId } = req.query;

    // Filter frameworks if frameworkId is provided
    let frameworks = models.complianceFrameworks;
    if (frameworkId) {
      const framework = models.complianceFrameworks.find(f => f.id === frameworkId);
      if (!framework) {
        return res.status(404).json({
          error: 'Not Found',
          message: `Framework with ID ${frameworkId} not found`
        });
      }
      frameworks = [framework];
    }

    // Generate report data for each framework
    const frameworksData = frameworks.map(framework => {
      // Get requirements for this framework
      const requirements = models.complianceRequirements.filter(r => r.frameworkId === framework.id);
      const requirementIds = requirements.map(r => r.id);

      // Get controls for these requirements
      const controls = models.complianceControls.filter(c =>
        c.relatedRequirements && c.relatedRequirements.some(rId => requirementIds.includes(rId))
      );

      // Calculate automation statistics
      const automationStats = {
        total: controls.length,
        manual: controls.filter(c => !c.automationDetails || !c.automationStatus).length,
        semiAutomated: controls.filter(c => c.automationStatus === 'semi-automated').length,
        fullyAutomated: controls.filter(c => c.automationStatus === 'fully-automated').length
      };

      // Calculate automation coverage percentage
      const automationCoverage = controls.length > 0 ?
        parseFloat((((automationStats.semiAutomated + automationStats.fullyAutomated) / controls.length) * 100).toFixed(2)) : 0;

      // Calculate automation by control type
      const controlTypes = ['preventive', 'detective', 'corrective', 'administrative', 'technical', 'physical'];
      const automationByType = {};

      controlTypes.forEach(type => {
        const controlsOfType = controls.filter(c => c.type === type);

        if (controlsOfType.length > 0) {
          automationByType[type] = {
            total: controlsOfType.length,
            manual: controlsOfType.filter(c => !c.automationDetails || !c.automationStatus).length,
            semiAutomated: controlsOfType.filter(c => c.automationStatus === 'semi-automated').length,
            fullyAutomated: controlsOfType.filter(c => c.automationStatus === 'fully-automated').length,
            automationCoverage: parseFloat((((controlsOfType.filter(c => c.automationStatus === 'semi-automated').length +
              controlsOfType.filter(c => c.automationStatus === 'fully-automated').length) / controlsOfType.length) * 100).toFixed(2))
          };
        }
      });

      // Get automation rules for this framework
      const automationRules = models.complianceAutomationRules.filter(rule => {
        const requirement = models.complianceRequirements.find(r => r.id === rule.requirementId);
        return requirement && requirement.frameworkId === framework.id;
      });

      // Calculate rule statistics
      const ruleStats = {
        total: automationRules.length,
        active: automationRules.filter(r => r.status === 'active').length,
        inactive: automationRules.filter(r => r.status === 'inactive').length,
        draft: automationRules.filter(r => r.status === 'draft').length,
        byTriggerType: {
          scheduled: automationRules.filter(r => r.triggerType === 'scheduled').length,
          eventBased: automationRules.filter(r => r.triggerType === 'event-based').length,
          manual: automationRules.filter(r => r.triggerType === 'manual').length
        }
      };

      // Calculate requirements with automation
      const requirementsWithAutomation = requirements.filter(req => {
        // Check if there are any automation rules for this requirement
        const hasRules = automationRules.some(rule => rule.requirementId === req.id);

        // Check if there are any automated controls for this requirement
        const hasAutomatedControls = controls.some(control =>
          control.relatedRequirements &&
          control.relatedRequirements.includes(req.id) &&
          (control.automationStatus === 'semi-automated' || control.automationStatus === 'fully-automated')
        );

        return hasRules || hasAutomatedControls;
      });

      // Calculate requirement automation coverage
      const requirementCoverage = requirements.length > 0 ?
        parseFloat(((requirementsWithAutomation.length / requirements.length) * 100).toFixed(2)) : 0;

      // Get top 5 automated controls
      const topAutomatedControls = controls
        .filter(c => c.automationStatus === 'fully-automated')
        .sort((a, b) => new Date(b.lastTestedDate || '1970-01-01') - new Date(a.lastTestedDate || '1970-01-01'))
        .slice(0, 5)
        .map(control => ({
          id: control.id,
          name: control.name,
          type: control.type,
          automationStatus: control.automationStatus,
          lastTestedDate: control.lastTestedDate
        }));

      // Get top 5 automation candidates (manual controls that could be automated)
      const automationCandidates = controls
        .filter(c => !c.automationDetails || !c.automationStatus)
        .sort((a, b) => {
          // Sort by priority: preventive and detective controls first
          const typeOrder = { 'preventive': 0, 'detective': 1, 'corrective': 2, 'administrative': 3, 'technical': 4, 'physical': 5 };
          return typeOrder[a.type] - typeOrder[b.type];
        })
        .slice(0, 5)
        .map(control => ({
          id: control.id,
          name: control.name,
          type: control.type,
          status: control.status,
          lastTestedDate: control.lastTestedDate
        }));

      return {
        id: framework.id,
        name: framework.name,
        version: framework.version,
        automationCoverage,
        automationStats,
        automationByType,
        ruleStats,
        requirementCoverage,
        requirementsWithAutomation: requirementsWithAutomation.length,
        totalRequirements: requirements.length,
        topAutomatedControls,
        automationCandidates
      };
    });

    // Calculate overall automation coverage
    const allControls = models.complianceControls;
    const overallAutomationStats = {
      total: allControls.length,
      manual: allControls.filter(c => !c.automationDetails || !c.automationStatus).length,
      semiAutomated: allControls.filter(c => c.automationStatus === 'semi-automated').length,
      fullyAutomated: allControls.filter(c => c.automationStatus === 'fully-automated').length
    };

    const overallAutomationCoverage = allControls.length > 0 ?
      parseFloat((((overallAutomationStats.semiAutomated + overallAutomationStats.fullyAutomated) / allControls.length) * 100).toFixed(2)) : 0;

    // Generate report data
    const reportData = {
      reportName: frameworkId ? `Automation Coverage Report for ${frameworks[0].name}` : 'Automation Coverage Report',
      generatedAt: new Date().toISOString(),
      overallAutomationCoverage,
      overallAutomationStats,
      frameworks: frameworksData
    };

    res.json({
      data: reportData
    });
  } catch (error) {
    console.error('Error in getAutomationCoverageReport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get control effectiveness report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getControlEffectivenessReport = (req, res) => {
  try {
    const { frameworkId, controlType, effectiveness } = req.query;

    // Filter controls based on query parameters
    let filteredControls = [...models.complianceControls];

    // Filter by framework if specified
    if (frameworkId) {
      // Get requirements for the specified framework
      const requirements = models.complianceRequirements.filter(r => r.frameworkId === frameworkId);
      if (requirements.length === 0) {
        return res.status(404).json({
          error: 'Not Found',
          message: `Framework with ID ${frameworkId} not found or has no requirements`
        });
      }

      // Get requirement IDs
      const requirementIds = requirements.map(r => r.id);

      // Filter controls that are related to these requirements
      filteredControls = filteredControls.filter(control =>
        control.relatedRequirements &&
        control.relatedRequirements.some(reqId => requirementIds.includes(reqId))
      );
    }

    // Filter by control type if specified
    if (controlType) {
      filteredControls = filteredControls.filter(control => control.type === controlType);
    }

    // Filter by effectiveness if specified
    if (effectiveness) {
      filteredControls = filteredControls.filter(control => control.effectiveness === effectiveness);
    }

    // Calculate effectiveness statistics
    const effectivenessStats = {
      total: filteredControls.length,
      effective: filteredControls.filter(c => c.effectiveness === 'effective').length,
      partiallyEffective: filteredControls.filter(c => c.effectiveness === 'partially-effective').length,
      ineffective: filteredControls.filter(c => c.effectiveness === 'ineffective').length,
      notAssessed: filteredControls.filter(c => c.effectiveness === 'not-assessed' || !c.effectiveness).length
    };

    // Calculate effectiveness by control type
    const controlTypes = ['preventive', 'detective', 'corrective', 'administrative', 'technical', 'physical'];
    const effectivenessByType = {};

    controlTypes.forEach(type => {
      const controlsOfType = filteredControls.filter(c => c.type === type);

      if (controlsOfType.length > 0) {
        effectivenessByType[type] = {
          total: controlsOfType.length,
          effective: controlsOfType.filter(c => c.effectiveness === 'effective').length,
          partiallyEffective: controlsOfType.filter(c => c.effectiveness === 'partially-effective').length,
          ineffective: controlsOfType.filter(c => c.effectiveness === 'ineffective').length,
          notAssessed: controlsOfType.filter(c => c.effectiveness === 'not-assessed' || !c.effectiveness).length,
          effectivenessRate: parseFloat(((controlsOfType.filter(c => c.effectiveness === 'effective').length / controlsOfType.length) * 100).toFixed(2))
        };
      }
    });

    // Calculate effectiveness by automation status
    const effectivenessByAutomation = {
      manual: {
        total: 0,
        effective: 0,
        partiallyEffective: 0,
        ineffective: 0,
        notAssessed: 0,
        effectivenessRate: 0
      },
      semiAutomated: {
        total: 0,
        effective: 0,
        partiallyEffective: 0,
        ineffective: 0,
        notAssessed: 0,
        effectivenessRate: 0
      },
      fullyAutomated: {
        total: 0,
        effective: 0,
        partiallyEffective: 0,
        ineffective: 0,
        notAssessed: 0,
        effectivenessRate: 0
      }
    };

    // Manual controls
    const manualControls = filteredControls.filter(c => !c.automationDetails || !c.automationStatus);
    if (manualControls.length > 0) {
      effectivenessByAutomation.manual = {
        total: manualControls.length,
        effective: manualControls.filter(c => c.effectiveness === 'effective').length,
        partiallyEffective: manualControls.filter(c => c.effectiveness === 'partially-effective').length,
        ineffective: manualControls.filter(c => c.effectiveness === 'ineffective').length,
        notAssessed: manualControls.filter(c => c.effectiveness === 'not-assessed' || !c.effectiveness).length,
        effectivenessRate: parseFloat(((manualControls.filter(c => c.effectiveness === 'effective').length / manualControls.length) * 100).toFixed(2))
      };
    }

    // Semi-automated controls
    const semiAutomatedControls = filteredControls.filter(c => c.automationStatus === 'semi-automated');
    if (semiAutomatedControls.length > 0) {
      effectivenessByAutomation.semiAutomated = {
        total: semiAutomatedControls.length,
        effective: semiAutomatedControls.filter(c => c.effectiveness === 'effective').length,
        partiallyEffective: semiAutomatedControls.filter(c => c.effectiveness === 'partially-effective').length,
        ineffective: semiAutomatedControls.filter(c => c.effectiveness === 'ineffective').length,
        notAssessed: semiAutomatedControls.filter(c => c.effectiveness === 'not-assessed' || !c.effectiveness).length,
        effectivenessRate: parseFloat(((semiAutomatedControls.filter(c => c.effectiveness === 'effective').length / semiAutomatedControls.length) * 100).toFixed(2))
      };
    }

    // Fully automated controls
    const fullyAutomatedControls = filteredControls.filter(c => c.automationStatus === 'fully-automated');
    if (fullyAutomatedControls.length > 0) {
      effectivenessByAutomation.fullyAutomated = {
        total: fullyAutomatedControls.length,
        effective: fullyAutomatedControls.filter(c => c.effectiveness === 'effective').length,
        partiallyEffective: fullyAutomatedControls.filter(c => c.effectiveness === 'partially-effective').length,
        ineffective: fullyAutomatedControls.filter(c => c.effectiveness === 'ineffective').length,
        notAssessed: fullyAutomatedControls.filter(c => c.effectiveness === 'not-assessed' || !c.effectiveness).length,
        effectivenessRate: parseFloat(((fullyAutomatedControls.filter(c => c.effectiveness === 'effective').length / fullyAutomatedControls.length) * 100).toFixed(2))
      };
    }

    // Get top 5 most effective controls
    const topEffectiveControls = filteredControls
      .filter(c => c.effectiveness === 'effective')
      .sort((a, b) => new Date(b.lastTestedDate || '1970-01-01') - new Date(a.lastTestedDate || '1970-01-01'))
      .slice(0, 5)
      .map(control => ({
        id: control.id,
        name: control.name,
        type: control.type,
        automationStatus: control.automationStatus || 'manual',
        lastTestedDate: control.lastTestedDate
      }));

    // Get top 5 least effective controls
    const topIneffectiveControls = filteredControls
      .filter(c => c.effectiveness === 'ineffective')
      .sort((a, b) => new Date(b.lastTestedDate || '1970-01-01') - new Date(a.lastTestedDate || '1970-01-01'))
      .slice(0, 5)
      .map(control => ({
        id: control.id,
        name: control.name,
        type: control.type,
        automationStatus: control.automationStatus || 'manual',
        lastTestedDate: control.lastTestedDate
      }));

    // Calculate overall effectiveness rate
    const overallEffectivenessRate = filteredControls.length > 0 ?
      parseFloat(((filteredControls.filter(c => c.effectiveness === 'effective').length / filteredControls.length) * 100).toFixed(2)) : 0;

    // Generate report data
    const reportData = {
      reportName: 'Control Effectiveness Report',
      generatedAt: new Date().toISOString(),
      filters: {
        frameworkId: frameworkId || 'All',
        controlType: controlType || 'All',
        effectiveness: effectiveness || 'All'
      },
      overallEffectivenessRate,
      effectivenessStats,
      effectivenessByType,
      effectivenessByAutomation,
      topEffectiveControls,
      topIneffectiveControls
    };

    res.json({
      data: reportData
    });
  } catch (error) {
    console.error('Error in getControlEffectivenessReport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get compliance status report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getComplianceStatusReport = (req, res) => {
  try {
    const { frameworkId, detailed = 'false' } = req.query;
    const isDetailed = detailed.toLowerCase() === 'true';

    // Filter frameworks if frameworkId is provided
    let frameworks = models.complianceFrameworks;
    if (frameworkId) {
      const framework = models.complianceFrameworks.find(f => f.id === frameworkId);
      if (!framework) {
        return res.status(404).json({
          error: 'Not Found',
          message: `Framework with ID ${frameworkId} not found`
        });
      }
      frameworks = [framework];
    }

    // Generate report data
    const reportData = {
      reportName: frameworkId ? `Compliance Status Report for ${frameworks[0].name}` : 'Compliance Status Report',
      generatedAt: new Date().toISOString(),
      overallComplianceScore: calculateOverallComplianceScore(frameworks),
      frameworks: frameworks.map(framework => {
        // Get requirements for this framework
        const requirements = models.complianceRequirements.filter(r => r.frameworkId === framework.id);

        // Calculate requirement statistics
        const requirementStats = {
          total: requirements.length,
          applicable: requirements.filter(r => r.status === 'applicable').length,
          notApplicable: requirements.filter(r => r.status === 'not-applicable').length,
          underReview: requirements.filter(r => r.status === 'under-review').length,
          byPriority: {
            critical: requirements.filter(r => r.priority === 'critical').length,
            high: requirements.filter(r => r.priority === 'high').length,
            medium: requirements.filter(r => r.priority === 'medium').length,
            low: requirements.filter(r => r.priority === 'low').length
          }
        };

        // Get controls for this framework's requirements
        const requirementIds = requirements.map(r => r.id);
        const controls = models.complianceControls.filter(c =>
          c.relatedRequirements && c.relatedRequirements.some(rId => requirementIds.includes(rId))
        );

        // Calculate control statistics
        const controlStats = {
          total: controls.length,
          implemented: controls.filter(c => c.status === 'implemented').length,
          partiallyImplemented: controls.filter(c => c.status === 'partially-implemented').length,
          notImplemented: controls.filter(c => c.status === 'not-implemented').length,
          planned: controls.filter(c => c.status === 'planned').length,
          automationStatus: {
            manual: controls.filter(c => !c.automationDetails).length,
            semiAutomated: controls.filter(c => c.automationDetails && c.automationStatus === 'semi-automated').length,
            fullyAutomated: controls.filter(c => c.automationDetails && c.automationStatus === 'fully-automated').length
          }
        };

        // Get the latest assessment for this framework
        const assessments = models.complianceAssessments
          .filter(a => a.frameworkId === framework.id)
          .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));

        const latestAssessment = assessments.length > 0 ? assessments[0] : null;

        // Build framework summary
        const frameworkSummary = {
          id: framework.id,
          name: framework.name,
          version: framework.version,
          complianceScore: framework.complianceScore || 0,
          lastAssessmentDate: framework.lastAssessmentDate || null,
          requirementStats,
          controlStats,
          latestAssessment: latestAssessment ? {
            id: latestAssessment.id,
            name: latestAssessment.name,
            status: latestAssessment.status,
            startDate: latestAssessment.startDate,
            endDate: latestAssessment.endDate,
            overallScore: latestAssessment.overallScore,
            assessor: latestAssessment.assessor,
            findingsCount: latestAssessment.findings ? latestAssessment.findings.length : 0
          } : null
        };

        // Add detailed information if requested
        if (isDetailed) {
          // Add requirement details
          frameworkSummary.requirements = requirements.map(req => ({
            id: req.id,
            name: req.name,
            description: req.description,
            status: req.status,
            priority: req.priority,
            controls: models.complianceControls
              .filter(c => c.relatedRequirements && c.relatedRequirements.includes(req.id))
              .map(c => ({
                id: c.id,
                name: c.name,
                status: c.status,
                type: c.type,
                automationStatus: c.automationStatus || 'manual'
              }))
          }));

          // Add findings from the latest assessment
          if (latestAssessment && latestAssessment.findings) {
            frameworkSummary.findings = latestAssessment.findings.map(finding => ({
              id: finding.id,
              requirementId: finding.requirementId,
              description: finding.description,
              severity: finding.severity,
              status: finding.status
            }));
          }
        }

        return frameworkSummary;
      })
    };

    res.json({
      data: reportData
    });
  } catch (error) {
    console.error('Error in getComplianceStatusReport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

module.exports = {
  getFrameworks,
  getFrameworkById,
  createFramework,
  updateFramework,
  deleteFramework,
  getFrameworkCategories,
  getFrameworkRequirements,
  getRequirementById,
  createRequirement,
  updateRequirement,
  deleteRequirement,
  getControls,
  getControlById,
  createControl,
  updateControl,
  deleteControl,
  getRequirementControls,
  addControlToRequirement,
  getControlTypes,
  getAssessments,
  getAssessmentById,
  createAssessment,
  updateAssessment,
  deleteAssessment,
  startAssessment,
  completeAssessment,
  getAssessmentResults,
  getFrameworkAssessments,
  getControlEvidence,
  getEvidenceById,
  createEvidence,
  updateEvidence,
  deleteEvidence,
  getAutomationRules,
  getAutomationRuleById,
  createAutomationRule,
  updateAutomationRule,
  deleteAutomationRule,
  executeAutomationRule,
  enableAutomationRule,
  disableAutomationRule,
  getRequirementAutomationRules,
  getComplianceDashboard,
  getComplianceStatusReport,
  getControlEffectivenessReport,
  getAutomationCoverageReport
};
