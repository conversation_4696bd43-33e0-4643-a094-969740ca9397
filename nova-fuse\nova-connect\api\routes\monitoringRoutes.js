/**
 * Monitoring Routes
 */

const express = require('express');
const router = express.Router();
const MonitoringController = require('../controllers/MonitoringController');

// Get health status of all connectors
router.get('/health', (req, res, next) => {
  MonitoringController.getHealthStatus(req, res, next);
});

// Get health status of a specific connector
router.get('/health/:id', (req, res, next) => {
  MonitoringController.getConnectorHealth(req, res, next);
});

// Run health check for a connector
router.post('/health/:id/check', (req, res, next) => {
  MonitoringController.runHealthCheck(req, res, next);
});

// Get all alerts
router.get('/alerts', (req, res, next) => {
  MonitoringController.getAllAlerts(req, res, next);
});

// Get alert by ID
router.get('/alerts/:id', (req, res, next) => {
  MonitoringController.getAlertById(req, res, next);
});

// Acknowledge alert
router.put('/alerts/:id/acknowledge', (req, res, next) => {
  MonitoringController.acknowledgeAlert(req, res, next);
});

// Resolve alert
router.put('/alerts/:id/resolve', (req, res, next) => {
  MonitoringController.resolveAlert(req, res, next);
});

// Add comment to alert
router.post('/alerts/:id/comments', (req, res, next) => {
  MonitoringController.addAlertComment(req, res, next);
});

// Get alert configuration
router.get('/config/alerts', (req, res, next) => {
  MonitoringController.getAlertConfig(req, res, next);
});

// Update alert configuration
router.put('/config/alerts', (req, res, next) => {
  MonitoringController.updateAlertConfig(req, res, next);
});

// Get all anomalies
router.get('/anomalies', (req, res, next) => {
  MonitoringController.getAllAnomalies(req, res, next);
});

// Get anomalies for a connector
router.get('/anomalies/:id', (req, res, next) => {
  MonitoringController.getConnectorAnomalies(req, res, next);
});

// Detect anomalies for a connector
router.post('/anomalies/:id/detect', (req, res, next) => {
  MonitoringController.detectAnomalies(req, res, next);
});

module.exports = router;
