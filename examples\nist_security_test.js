/**
 * NIST Security Test for NovaVision
 * 
 * This example tests the NIST security implementation for NovaVision.
 */

const { NovaVisionNISTSecurity } = require('../src/novavision/security/nist');

// Create a new NIST security instance
const nistSecurity = new NovaVisionNISTSecurity({
  enableCSP: true,
  enableXSS: true,
  enableSRI: true,
  enableHSTS: true,
  enableSecureCookies: true,
  enableInputValidation: true,
  enableOutputEncoding: true,
  enableAuditLogging: true,
  enableAccessControl: true,
  enableSecureDefaults: true,
  nistFrameworks: ['SP800-53', 'CSF', 'SP800-171']
});

// Example UI schema
const uiSchema = {
  id: 'test-schema',
  type: 'dashboard',
  title: 'Test Dashboard',
  components: [
    {
      id: 'test-component-1',
      type: 'input',
      label: 'Test Input'
    },
    {
      id: 'test-component-2',
      type: 'textarea',
      label: 'Test Textarea'
    }
  ],
  forms: [
    {
      id: 'test-form',
      title: 'Test Form'
    }
  ],
  resources: [
    {
      type: 'script',
      url: 'https://example.com/script.js',
      content: 'console.log("Test script")'
    },
    {
      type: 'stylesheet',
      url: 'https://example.com/style.css',
      content: 'body { color: red; }'
    }
  ]
};

// Example HTML content
const htmlContent = '<script>alert("XSS attack");</script><p>Test content</p>';

// Example object content
const objectContent = {
  name: 'Test Object',
  description: '<script>alert("XSS attack");</script>',
  properties: {
    property1: '<img src="x" onerror="alert(\'XSS\')">',
    property2: 'Safe property'
  }
};

// Example input
const input = {
  username: 'testuser',
  password: 'password123',
  email: '<EMAIL>',
  website: 'https://example.com',
  comment: '<script>alert("XSS");</script>'
};

// Example validation rules
const validationRules = {
  username: {
    required: true,
    type: 'string',
    pattern: '^[a-zA-Z0-9_]+$',
    sanitize: 'alphanumeric'
  },
  password: {
    required: true,
    type: 'string'
  },
  email: {
    required: true,
    type: 'string',
    pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'
  },
  website: {
    required: false,
    type: 'string',
    sanitize: 'url'
  },
  comment: {
    required: false,
    type: 'string',
    sanitize: 'html'
  }
};

console.log('=== NIST Security Test ===\n');

// Test securing UI schema
console.log('1. Securing UI Schema:');
const securedSchema = nistSecurity.secureUISchema(uiSchema);
console.log('- Original schema has security headers:', uiSchema.securityHeaders ? 'Yes' : 'No');
console.log('- Secured schema has security headers:', securedSchema.securityHeaders ? 'Yes' : 'No');
console.log('- CSP directives:', Object.keys(securedSchema.securityHeaders.contentSecurityPolicy).length);
console.log('- XSS Protection:', securedSchema.securityHeaders.xssProtection);
console.log('- Forms have CSRF protection:', securedSchema.forms[0].csrfProtection);

// Test securing HTML content
console.log('\n2. Securing HTML Content:');
const securedHtml = nistSecurity.secureRenderedContent(htmlContent);
console.log('- Original HTML:', htmlContent);
console.log('- Secured HTML:', securedHtml);

// Test securing object content
console.log('\n3. Securing Object Content:');
const securedObject = nistSecurity.secureRenderedContent(objectContent);
console.log('- Original description:', objectContent.description);
console.log('- Secured description:', securedObject.description);
console.log('- Original property1:', objectContent.properties.property1);
console.log('- Secured property1:', securedObject.properties.property1);

// Test input validation
console.log('\n4. Input Validation:');
const validationResult = nistSecurity.validateInput(input, validationRules);
console.log('- Validation result valid:', validationResult.valid);
console.log('- Validation violations:', validationResult.violations ? validationResult.violations.length : 0);
console.log('- Original comment:', input.comment);
console.log('- Sanitized comment:', validationResult.sanitizedInput.comment);

// Test NIST compliance report
console.log('\n5. NIST Compliance Report:');
const complianceReport = nistSecurity.getNISTComplianceReport();
console.log('- Frameworks:', complianceReport.frameworks.join(', '));
console.log('- SP800-53 compliance score:', complianceReport.compliance['SP800-53'].score.toFixed(2) + '%');
console.log('- CSF compliance score:', complianceReport.compliance['CSF'].score.toFixed(2) + '%');
console.log('- Audit log entries:', complianceReport.auditLog.length);

console.log('\n=== End of NIST Security Test ===');
