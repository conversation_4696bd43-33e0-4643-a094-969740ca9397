@echo off
REM NovaConnect π-Coherence Test Runner (Windows)
REM Quick and easy way to run the π-coherence A/B test

echo 🔱 NovaConnect π-Coherence Test Runner
echo ======================================

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is required but not installed
    pause
    exit /b 1
)

REM Set default URL if not provided
if "%NOVA_CONNECT_URL%"=="" set NOVA_CONNECT_URL=http://localhost:3001

REM Check if NovaConnect is running
echo 🔍 Checking if NovaConnect is running...
curl -s "%NOVA_CONNECT_URL%/health" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  NovaConnect not detected at %NOVA_CONNECT_URL%
    echo    Starting test anyway (will test error handling)
) else (
    echo ✅ NovaConnect is running at %NOVA_CONNECT_URL%
)

REM Install dependencies if needed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install axios
)

REM Create test results directory
if not exist "test-results" mkdir test-results

echo.
echo 🚀 Starting π-Coherence A/B Test...
echo    Target URL: %NOVA_CONNECT_URL%
echo    Test Type: Performance comparison (Standard vs π-timing)
echo    Expected Duration: ~2-3 minutes
echo.

REM Run the test
node pi-coherence-test.js

REM Check exit code
if errorlevel 1 (
    echo.
    echo ❌ Test failed - check error messages above
    echo 🔧 Troubleshooting:
    echo    - Ensure NovaConnect is running
    echo    - Check network connectivity
    echo    - Verify API endpoints are accessible
) else (
    echo.
    echo 🎉 Test completed successfully!
    echo 📊 Check the test-results/ directory for detailed reports
    echo.
    echo 🔍 Quick Analysis:
    echo    - Look for efficiency gains ≥ 2.0× (target: 3.142×)
    echo    - Check coherence score improvements
    echo    - Monitor error rate reductions
    echo.
    echo 📋 Next Steps:
    echo    1. Review detailed results in test-results/
    echo    2. If successful, consider production deployment
    echo    3. Expand testing to other NovaFuse systems
)

pause
