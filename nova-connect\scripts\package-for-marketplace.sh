#!/bin/bash
# Script to package NovaConnect UAC for Google Cloud Marketplace

# Set variables
PROJECT_ID=${1:-"novafuse-test"}
IMAGE_NAME=${2:-"novafuse-uac"}
IMAGE_TAG=${3:-"1.0.0"}
FULL_IMAGE_NAME="gcr.io/$PROJECT_ID/$IMAGE_NAME:$IMAGE_TAG"

# Install mpdev
echo "Installing mpdev..."
if ! command -v mpdev &> /dev/null; then
  echo "mpdev not found, installing..."
  curl -LO https://github.com/GoogleCloudPlatform/marketplace-tools/releases/download/v0.3.5/mpdev_linux_amd64
  chmod +x mpdev_linux_amd64
  sudo mv mpdev_linux_amd64 /usr/local/bin/mpdev
fi

# Update schema.yaml
echo "Updating schema.yaml..."
sed -i "s/publishedVersion: '.*'/publishedVersion: '$IMAGE_TAG'/g" marketplace/schema.yaml
sed -i "s|gcr.io/novafuse-production/novafuse-uac:.*|gcr.io/$PROJECT_ID/$IMAGE_NAME:$IMAGE_TAG|g" marketplace/schema.yaml

# Package for marketplace
echo "Packaging for marketplace..."
cd marketplace
mpdev pkg package .

# Verify the package
echo "Verifying the package..."
mpdev pkg verify

echo "Marketplace packaging complete!"
