/**
 * Brute Force Protection Service
 * 
 * This service handles brute force attack protection.
 */

const fs = require('fs').promises;
const path = require('path');
const { BruteForceError } = require('../utils/errors');

class BruteForceProtectionService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.attemptsFile = path.join(this.dataDir, 'login_attempts.json');
    this.config = {
      maxAttempts: 5, // Maximum number of failed attempts
      windowMs: 15 * 60 * 1000, // 15 minutes
      blockDuration: 30 * 60 * 1000, // 30 minutes
    };
    this.ensureDataDir();
    
    // Clean up expired attempts every 5 minutes
    setInterval(() => this.cleanupExpiredAttempts(), 5 * 60 * 1000);
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Load login attempts from file
   */
  async loadAttempts() {
    try {
      const data = await fs.readFile(this.attemptsFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty object
        return {};
      }
      console.error('Error loading login attempts:', error);
      throw error;
    }
  }

  /**
   * Save login attempts to file
   */
  async saveAttempts(attempts) {
    try {
      await fs.writeFile(this.attemptsFile, JSON.stringify(attempts, null, 2));
    } catch (error) {
      console.error('Error saving login attempts:', error);
      throw error;
    }
  }

  /**
   * Record a failed login attempt
   */
  async recordFailedAttempt(identifier) {
    const attempts = await this.loadAttempts();
    const now = Date.now();
    
    // Initialize attempts for this identifier if not exists
    if (!attempts[identifier]) {
      attempts[identifier] = {
        count: 0,
        firstAttempt: now,
        lastAttempt: now,
        blocked: false,
        blockedUntil: null
      };
    }
    
    // Update attempts
    attempts[identifier].count++;
    attempts[identifier].lastAttempt = now;
    
    // Check if account should be blocked
    if (attempts[identifier].count >= this.config.maxAttempts) {
      attempts[identifier].blocked = true;
      attempts[identifier].blockedUntil = now + this.config.blockDuration;
    }
    
    // Save attempts
    await this.saveAttempts(attempts);
    
    return attempts[identifier];
  }

  /**
   * Reset login attempts for an identifier
   */
  async resetAttempts(identifier) {
    const attempts = await this.loadAttempts();
    
    if (attempts[identifier]) {
      delete attempts[identifier];
      await this.saveAttempts(attempts);
    }
    
    return true;
  }

  /**
   * Check if an identifier is blocked
   */
  async isBlocked(identifier) {
    const attempts = await this.loadAttempts();
    const now = Date.now();
    
    if (!attempts[identifier]) {
      return false;
    }
    
    // Check if blocked and block duration has not expired
    if (attempts[identifier].blocked && attempts[identifier].blockedUntil > now) {
      const remainingTime = Math.ceil((attempts[identifier].blockedUntil - now) / 1000);
      return {
        blocked: true,
        remainingTime,
        attemptsCount: attempts[identifier].count,
        maxAttempts: this.config.maxAttempts
      };
    }
    
    // If block duration has expired, reset the block
    if (attempts[identifier].blocked && attempts[identifier].blockedUntil <= now) {
      attempts[identifier].blocked = false;
      attempts[identifier].blockedUntil = null;
      attempts[identifier].count = 0;
      await this.saveAttempts(attempts);
    }
    
    return false;
  }

  /**
   * Check login attempt before processing
   */
  async checkLoginAttempt(identifier) {
    // Check if identifier is blocked
    const blockStatus = await this.isBlocked(identifier);
    
    if (blockStatus && blockStatus.blocked) {
      throw new BruteForceError(
        `Too many failed login attempts. Account is temporarily blocked. Try again in ${blockStatus.remainingTime} seconds.`,
        blockStatus.remainingTime
      );
    }
    
    return true;
  }

  /**
   * Handle successful login
   */
  async handleSuccessfulLogin(identifier) {
    return this.resetAttempts(identifier);
  }

  /**
   * Handle failed login
   */
  async handleFailedLogin(identifier) {
    const attempt = await this.recordFailedAttempt(identifier);
    
    if (attempt.blocked) {
      const remainingTime = Math.ceil((attempt.blockedUntil - Date.now()) / 1000);
      throw new BruteForceError(
        `Too many failed login attempts. Account is temporarily blocked. Try again in ${remainingTime} seconds.`,
        remainingTime
      );
    }
    
    return {
      attemptsCount: attempt.count,
      maxAttempts: this.config.maxAttempts,
      remainingAttempts: this.config.maxAttempts - attempt.count
    };
  }

  /**
   * Clean up expired attempts
   */
  async cleanupExpiredAttempts() {
    try {
      const attempts = await this.loadAttempts();
      const now = Date.now();
      let modified = false;
      
      for (const identifier in attempts) {
        // Remove attempts that are older than the window
        if (now - attempts[identifier].lastAttempt > this.config.windowMs && !attempts[identifier].blocked) {
          delete attempts[identifier];
          modified = true;
        }
        
        // Reset blocks that have expired
        if (attempts[identifier] && attempts[identifier].blocked && attempts[identifier].blockedUntil <= now) {
          attempts[identifier].blocked = false;
          attempts[identifier].blockedUntil = null;
          attempts[identifier].count = 0;
          modified = true;
        }
      }
      
      if (modified) {
        await this.saveAttempts(attempts);
      }
    } catch (error) {
      console.error('Error cleaning up expired attempts:', error);
    }
  }

  /**
   * Update brute force protection configuration
   */
  async updateConfig(config) {
    this.config = {
      ...this.config,
      ...config
    };
    
    return this.config;
  }

  /**
   * Get brute force protection configuration
   */
  getConfig() {
    return this.config;
  }
}

module.exports = BruteForceProtectionService;
