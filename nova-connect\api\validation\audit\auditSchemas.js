/**
 * Audit Validation Schemas
 * 
 * This file contains validation schemas for audit-related endpoints.
 */

const Joi = require('joi');
const { commonSchemas } = require('../common/commonSchemas');

/**
 * Get audit logs schema
 */
const getAuditLogsSchema = {
  query: Joi.object({
    page: commonSchemas.page,
    limit: commonSchemas.limit,
    sortBy: Joi.string().valid('timestamp', 'userId', 'action', 'resourceType', 'resourceId', 'status').default('timestamp'),
    sortOrder: commonSchemas.sortOrder,
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    userId: commonSchemas.id.optional(),
    action: Joi.string().valid('CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT', 'EXECUTE').optional(),
    resourceType: Joi.string().optional(),
    resourceId: Joi.string().optional(),
    status: Joi.string().valid('success', 'failure').optional(),
    teamId: commonSchemas.id.optional(),
    environmentId: commonSchemas.id.optional(),
    tenantId: commonSchemas.id.optional(),
    search: Joi.string().allow('').optional()
  })
};

/**
 * Export audit logs schema
 */
const exportAuditLogsSchema = {
  query: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    userId: commonSchemas.id.optional(),
    action: Joi.string().valid('CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT', 'EXECUTE').optional(),
    resourceType: Joi.string().optional(),
    resourceId: Joi.string().optional(),
    status: Joi.string().valid('success', 'failure').optional(),
    teamId: commonSchemas.id.optional(),
    environmentId: commonSchemas.id.optional(),
    tenantId: commonSchemas.id.optional(),
    format: Joi.string().valid('csv', 'json', 'excel').default('csv')
  })
};

/**
 * Get audit log stats schema
 */
const getAuditLogStatsSchema = {
  query: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    userId: commonSchemas.id.optional(),
    teamId: commonSchemas.id.optional(),
    environmentId: commonSchemas.id.optional(),
    tenantId: commonSchemas.id.optional()
  })
};

/**
 * Get tenant audit logs schema
 */
const getTenantAuditLogsSchema = {
  params: Joi.object({
    tenantId: commonSchemas.id.required()
  }),
  query: Joi.object({
    page: commonSchemas.page,
    limit: commonSchemas.limit,
    sortBy: Joi.string().valid('timestamp', 'userId', 'action', 'resourceType', 'resourceId', 'status').default('timestamp'),
    sortOrder: commonSchemas.sortOrder,
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    userId: commonSchemas.id.optional(),
    action: Joi.string().valid('CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT', 'EXECUTE').optional(),
    resourceType: Joi.string().optional(),
    resourceId: Joi.string().optional(),
    status: Joi.string().valid('success', 'failure').optional(),
    search: Joi.string().allow('').optional()
  })
};

/**
 * Get user audit logs schema
 */
const getUserAuditLogsSchema = {
  params: Joi.object({
    userId: commonSchemas.id.required()
  }),
  query: Joi.object({
    page: commonSchemas.page,
    limit: commonSchemas.limit,
    sortBy: Joi.string().valid('timestamp', 'action', 'resourceType', 'resourceId', 'status').default('timestamp'),
    sortOrder: commonSchemas.sortOrder,
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    action: Joi.string().valid('CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT', 'EXECUTE').optional(),
    resourceType: Joi.string().optional(),
    resourceId: Joi.string().optional(),
    status: Joi.string().valid('success', 'failure').optional(),
    search: Joi.string().allow('').optional()
  })
};

/**
 * Get team audit logs schema
 */
const getTeamAuditLogsSchema = {
  params: Joi.object({
    teamId: commonSchemas.id.required()
  }),
  query: Joi.object({
    page: commonSchemas.page,
    limit: commonSchemas.limit,
    sortBy: Joi.string().valid('timestamp', 'userId', 'action', 'resourceType', 'resourceId', 'status').default('timestamp'),
    sortOrder: commonSchemas.sortOrder,
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    userId: commonSchemas.id.optional(),
    action: Joi.string().valid('CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT', 'EXECUTE').optional(),
    resourceType: Joi.string().optional(),
    resourceId: Joi.string().optional(),
    status: Joi.string().valid('success', 'failure').optional(),
    search: Joi.string().allow('').optional()
  })
};

/**
 * Get resource audit logs schema
 */
const getResourceAuditLogsSchema = {
  params: Joi.object({
    resourceType: Joi.string().required(),
    resourceId: Joi.string().required()
  }),
  query: Joi.object({
    page: commonSchemas.page,
    limit: commonSchemas.limit,
    sortBy: Joi.string().valid('timestamp', 'userId', 'action', 'status').default('timestamp'),
    sortOrder: commonSchemas.sortOrder,
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    userId: commonSchemas.id.optional(),
    action: Joi.string().valid('CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT', 'EXECUTE').optional(),
    status: Joi.string().valid('success', 'failure').optional(),
    search: Joi.string().allow('').optional()
  })
};

module.exports = {
  getAuditLogsSchema,
  exportAuditLogsSchema,
  getAuditLogStatsSchema,
  getTenantAuditLogsSchema,
  getUserAuditLogsSchema,
  getTeamAuditLogsSchema,
  getResourceAuditLogsSchema
};
