/**
 * CSME Example
 * 
 * This example demonstrates the usage of the CSME component.
 */

const {
  TelomereErosionPrediction,
  MTORActivationMonitoring,
  InflammationCascadeModeling,
  createCSMESystem,
  createEnhancedCSMESystem
} = require('../csme');

// Example 1: Using individual components
console.log('Example 1: Using individual components');

// Create components
const telomereErosionPrediction = new TelomereErosionPrediction();
const mtorActivationMonitoring = new MTORActivationMonitoring();
const inflammationCascadeModeling = new InflammationCascadeModeling();

// Start components
telomereErosionPrediction.start();
mtorActivationMonitoring.start();
inflammationCascadeModeling.start();

// Set telomere length
const telomereLength = telomereErosionPrediction.setTelomereLength(0.65);
console.log(`Telomere Length: ${telomereLength.toFixed(4)}`);

// Add erosion factor
const erosionFactor = telomereErosionPrediction.addErosionFactor({
  type: 'oxidative_stress',
  impact: 0.7,
  duration: 30,
  description: 'Oxidative stress from environmental factors'
});
console.log(`Added erosion factor: ${erosionFactor.id} (${erosionFactor.type})`);

// Set biological and chronological age
telomereErosionPrediction.setBiologicalAge(45);
telomereErosionPrediction.setChronologicalAge(40);
console.log(`Age Acceleration: ${telomereErosionPrediction.getState().ageAcceleration} years`);

// Set mTOR activation level
const activationLevel = mtorActivationMonitoring.setActivationLevel(0.6);
console.log(`mTOR Activation Level: ${activationLevel.toFixed(4)}`);

// Add mTOR modulator
const modulator = mtorActivationMonitoring.addModulator({
  type: 'nutrient',
  effect: 'activation',
  strength: 0.6,
  duration: 24,
  description: 'High protein meal'
});
console.log(`Added mTOR modulator: ${modulator.id} (${modulator.type})`);

// Get downstream effects
const downstreamEffects = mtorActivationMonitoring.getDownstreamEffects();
console.log('mTOR Downstream Effects:');
console.log(`- Protein Synthesis: ${downstreamEffects.proteinSynthesis.toFixed(4)}`);
console.log(`- Cell Growth: ${downstreamEffects.cellGrowth.toFixed(4)}`);
console.log(`- Autophagy: ${downstreamEffects.autophagy.toFixed(4)}`);
console.log(`- Metabolism: ${downstreamEffects.metabolism.toFixed(4)}`);

// Set inflammation level
const inflammationLevel = inflammationCascadeModeling.setInflammationLevel(0.4);
console.log(`Inflammation Level: ${inflammationLevel.toFixed(4)}`);

// Add inflammation trigger
const trigger = inflammationCascadeModeling.addTrigger({
  type: 'pathogen',
  strength: 0.5,
  duration: 48,
  description: 'Bacterial infection'
});
console.log(`Added inflammation trigger: ${trigger.id} (${trigger.type})`);

// Add inflammation mediator
const mediator = inflammationCascadeModeling.addMediator({
  type: 'cytokine',
  effect: 'pro-inflammatory',
  strength: 0.6,
  halfLife: 12,
  description: 'IL-6 cytokine'
});
console.log(`Added inflammation mediator: ${mediator.id} (${mediator.type})`);

// Get systemic effects
const systemicEffects = inflammationCascadeModeling.getSystemicEffects();
console.log('Inflammation Systemic Effects:');
console.log(`- Immune Activation: ${systemicEffects.immuneActivation.toFixed(4)}`);
console.log(`- Tissue Repair: ${systemicEffects.tissueRepair.toFixed(4)}`);
console.log(`- Oxidative Stress: ${systemicEffects.oxidativeStress.toFixed(4)}`);
console.log(`- Metabolic Disruption: ${systemicEffects.metabolicDisruption.toFixed(4)}`);

// Get affected systems
const affectedSystems = inflammationCascadeModeling.getAffectedSystems();
console.log('Affected Systems:');
for (const [system, impact] of Object.entries(affectedSystems)) {
  console.log(`- ${system}: ${impact.toFixed(4)}`);
}

// Stop components
telomereErosionPrediction.stop();
mtorActivationMonitoring.stop();
inflammationCascadeModeling.stop();

// Example 2: Using the basic CSME system
console.log('\nExample 2: Using the basic CSME system');

// Create CSME system
const csmeSystem = createCSMESystem({
  telomereErosionPredictionOptions: {
    enableLogging: true
  },
  mtorActivationMonitoringOptions: {
    enableLogging: true
  },
  inflammationCascadeModelingOptions: {
    enableLogging: true
  }
});

// Start components
csmeSystem.telomereErosionPrediction.start();
csmeSystem.mtorActivationMonitoring.start();
csmeSystem.inflammationCascadeModeling.start();

// Set telomere length
const telomereLength2 = csmeSystem.telomereErosionPrediction.setTelomereLength(0.7);
console.log(`Telomere Length: ${telomereLength2.toFixed(4)}`);

// Set mTOR activation level
const activationLevel2 = csmeSystem.mtorActivationMonitoring.setActivationLevel(0.5);
console.log(`mTOR Activation Level: ${activationLevel2.toFixed(4)}`);

// Set inflammation level
const inflammationLevel2 = csmeSystem.inflammationCascadeModeling.setInflammationLevel(0.3);
console.log(`Inflammation Level: ${inflammationLevel2.toFixed(4)}`);

// Stop components
csmeSystem.telomereErosionPrediction.stop();
csmeSystem.mtorActivationMonitoring.stop();
csmeSystem.inflammationCascadeModeling.stop();

// Example 3: Using the enhanced CSME system
console.log('\nExample 3: Using the enhanced CSME system');

// Create enhanced CSME system
const enhancedCSMESystem = createEnhancedCSMESystem({
  enableLogging: true
});

// Start system
enhancedCSMESystem.start();

// Process biological data
const biologicalData = {
  telomere: {
    length: 0.75,
    biologicalAge: 42,
    chronologicalAge: 40,
    erosionFactors: [
      {
        type: 'oxidative_stress',
        impact: 0.6,
        duration: 30,
        description: 'Oxidative stress from environmental factors'
      }
    ]
  },
  mtor: {
    activationLevel: 0.55,
    modulators: [
      {
        type: 'nutrient',
        effect: 'activation',
        strength: 0.5,
        duration: 24,
        description: 'High protein meal'
      }
    ]
  },
  inflammation: {
    level: 0.35,
    triggers: [
      {
        type: 'pathogen',
        strength: 0.4,
        duration: 48,
        description: 'Viral infection'
      }
    ],
    mediators: [
      {
        type: 'cytokine',
        effect: 'pro-inflammatory',
        strength: 0.5,
        halfLife: 12,
        description: 'IL-6 cytokine'
      }
    ]
  }
};

// Process data
const processingResult = enhancedCSMESystem.processBiologicalData(biologicalData);
console.log(`Biological Entropy (Ψₜ): ${processingResult.biologicalEntropy.toFixed(4)}`);

// Get biological health assessment
const healthAssessment = enhancedCSMESystem.getBiologicalHealthAssessment();
console.log('Biological Health Assessment:');
console.log(`- Biological Entropy: ${healthAssessment.biologicalEntropy.toFixed(4)}`);
console.log(`- Health Status: ${healthAssessment.healthStatus}`);
console.log(`- Health Score: ${healthAssessment.healthScore.toFixed(4)}`);
console.log(`- Age Acceleration: ${healthAssessment.ageAcceleration} years`);
console.log(`- Telomere Length: ${healthAssessment.telomereLength.toFixed(4)}`);
console.log(`- Telomere Status: ${healthAssessment.telomereStatus}`);
console.log(`- mTOR Activation: ${healthAssessment.mtorActivation.toFixed(4)}`);
console.log(`- mTOR Status: ${healthAssessment.mtorStatus}`);
console.log(`- Inflammation Level: ${healthAssessment.inflammationLevel.toFixed(4)}`);
console.log(`- Inflammation Status: ${healthAssessment.inflammationStatus}`);

// Predict health trajectory
const trajectory = enhancedCSMESystem.predictHealthTrajectory(5);
console.log('Health Trajectory Prediction:');
for (const prediction of trajectory.predictions) {
  console.log(`- Time Unit ${prediction.timeUnit}: Telomere Length ${prediction.telomereLength.toFixed(4)}, Biological Entropy ${prediction.biologicalEntropy.toFixed(4)}, Status: ${prediction.healthStatus}`);
}

// Get unified state
const unifiedState = enhancedCSMESystem.getUnifiedState();
console.log('Unified State Summary:');
console.log(`- Telomere Status: ${unifiedState.telomereErosionPrediction.telomereStatus}`);
console.log(`- mTOR Status: ${unifiedState.mtorActivationMonitoring.activationStatus}`);
console.log(`- Inflammation Status: ${unifiedState.inflammationCascadeModeling.inflammationStatus}`);

// Get unified metrics
const unifiedMetrics = enhancedCSMESystem.getUnifiedMetrics();
console.log('Unified Metrics Summary:');
console.log(`- Telomere Updates: ${unifiedMetrics.telomereErosionPrediction.totalUpdates}`);
console.log(`- mTOR Updates: ${unifiedMetrics.mtorActivationMonitoring.totalUpdates}`);
console.log(`- Inflammation Updates: ${unifiedMetrics.inflammationCascadeModeling.totalUpdates}`);

// Stop system
enhancedCSMESystem.stop();

console.log('\nCSME example completed successfully!');
