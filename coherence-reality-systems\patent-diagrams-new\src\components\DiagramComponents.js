import styled from 'styled-components';

// Main diagram container
export const DiagramFrame = styled.div`
  position: relative;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
  height: 550px; /* Increased height to contain all components */
  margin-bottom: 100px; /* Add space below the diagram */
  /* Patent-compliant styling - black and white only */
  color: black;
  background-color: white;
`;

// Component box
export const ComponentBox = styled.div`
  position: absolute;
  width: ${props => props.width || '120px'};
  height: ${props => props.height || '60px'};
  left: ${props => props.left || '0'};
  top: ${props => props.top || '0'};
  background-color: ${props => props.backgroundColor || 'white'};
  border: 2px solid #333;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 10px;
  box-sizing: border-box;
  font-size: ${props => props.fontSize || '14px'};
  line-height: 1.3;
  z-index: 1;
  overflow: ${props => props.overflow || 'visible'};
`;

// Component label
export const ComponentLabel = styled.div`
  font-weight: bold;
  margin-bottom: 4px;
  font-size: ${props => props.fontSize || '14px'};
  color: ${props => props.color || 'black'};
`;

// Component number
export const ComponentNumber = styled.div`
  position: absolute;
  top: -10px;
  left: -10px;
  width: 20px;
  height: 20px;
  background-color: #333; /* Changed from blue to dark gray/black */
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: bold;
`;

// Arrow
export const Arrow = styled.div`
  position: absolute;
  left: ${props => props.left || '0'};
  top: ${props => props.top || '0'};
  width: ${props => props.width || '100px'};
  height: ${props => props.height || '2px'};
  background-color: transparent;
  transform: ${props => props.transform || 'none'};
  z-index: 0;

  &:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: #333;
    top: 50%;
    transform: translateY(-50%);
  }

  &:after {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    border-top: 2px solid #333;
    border-right: 2px solid #333;
    right: 0;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
  }
`;

// Vertical Arrow
export const VerticalArrow = styled.div`
  position: absolute;
  left: ${props => props.left || '0'};
  top: ${props => props.top || '0'};
  width: ${props => props.width || '2px'};
  height: ${props => props.height || '100px'};
  background-color: #333;
  z-index: 0;

  &:after {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    border-top: 2px solid #333;
    border-right: 2px solid #333;
    bottom: ${props => props.direction === 'up' ? '100%' : 'auto'};
    top: ${props => props.direction === 'down' ? '100%' : 'auto'};
    left: 50%;
    transform: ${props => props.direction === 'up'
      ? 'translateX(-50%) translateY(5px) rotate(-45deg)'
      : 'translateX(-50%) translateY(-5px) rotate(135deg)'};
  }
`;

// Diagram title
export const DiagramTitle = styled.div`
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 30px;
`;

// Container box (for grouping components)
export const ContainerBox = styled.div`
  position: absolute;
  width: ${props => props.width || '400px'};
  height: ${props => props.height || '300px'};
  left: ${props => props.left || '0'};
  top: ${props => props.top || '0'};
  border: 2px solid #666;
  border-radius: 10px;
  padding: 30px 10px 10px;
  box-sizing: border-box;
  z-index: 0;
`;

// Container label
export const ContainerLabel = styled.div`
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  font-weight: bold;
  font-size: ${props => props.fontSize || '16px'};
  text-align: center;
  color: ${props => props.color || 'black'};
  max-width: 90%;
`;

// Reference number
export const ReferenceNumber = styled.div`
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #666;
`;

// Legend
export const Legend = styled.div`
  position: absolute;
  bottom: 10px;
  right: 10px;
  font-size: 12px;
  color: #666;
  text-align: right;
`;

// Curved Arrow
export const CurvedArrow = styled.svg`
  position: absolute;
  left: ${props => props.left || '0'};
  top: ${props => props.top || '0'};
  width: ${props => props.width || '100px'};
  height: ${props => props.height || '100px'};
  z-index: 0;
  overflow: visible;
`;

// Bidirectional Arrow
export const BidirectionalArrow = styled.div`
  position: absolute;
  left: ${props => props.left || '0'};
  top: ${props => props.top || '0'};
  width: ${props => props.width || '100px'};
  height: ${props => props.height || '2px'};
  background-color: transparent;
  transform: ${props => props.transform || 'none'};
  z-index: 0;

  &:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: #333;
    top: 50%;
    transform: translateY(-50%);
  }

  &:after, &:before {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    border-top: 2px solid #333;
    border-right: 2px solid #333;
    top: 50%;
  }

  &:after {
    right: 0;
    transform: translateY(-50%) rotate(45deg);
  }

  &:before {
    left: 0;
    transform: translateY(-50%) rotate(-135deg);
  }
`;

// Data Flow Label
export const DataFlowLabel = styled.div`
  position: absolute;
  left: ${props => props.left || '0'};
  top: ${props => props.top || '0'};
  font-size: 12px;
  color: #333;
  text-align: center;
  background-color: white;
  padding: 2px 5px;
  border-radius: 3px;
  z-index: 2;
`;

// Component Group
export const ComponentGroup = styled.div`
  position: absolute;
  left: ${props => props.left || '0'};
  top: ${props => props.top || '0'};
  width: ${props => props.width || 'auto'};
  height: ${props => props.height || 'auto'};
`;

// Diagram Legend Item
export const LegendItem = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  font-size: 12px;
`;

export const LegendColor = styled.div`
  width: 15px;
  height: 15px;
  background-color: ${props => props.color};
  margin-right: 5px;
  border: 1px solid #333;
`;

export const LegendText = styled.div`
  font-size: 12px;
`;

// Diagram Legend
export const DiagramLegend = styled.div`
  position: absolute;
  right: 10px;
  bottom: -90px; /* Position below the diagram instead of inside it */
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px;
  z-index: 10;
  width: 200px; /* Fixed width */
`;

// Inventor Label
export const InventorLabel = styled.div`
  position: absolute;
  left: 10px;
  bottom: -90px; /* Position below the diagram */
  font-size: 12px;
  font-style: italic;
  color: #333;
`;
