<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse UCECS Dashboard</title>
    <style>
        :root {
            --primary: #0A84FF;
            --primary-dark: #0064D1;
            --secondary: #121212;
            --secondary-light: #2A2A2A;
            --success: #10B981;
            --warning: #F59E0B;
            --danger: #EF4444;
            --info: #3B82F6;
            --white: #FFFFFF;
            --gray-100: #F3F4F6;
            --gray-200: #E5E7EB;
            --gray-300: #D1D5DB;
            --gray-400: #9CA3AF;
            --gray-500: #6B7280;
            --gray-600: #4B5563;
            --gray-700: #374151;
            --gray-800: #1F2937;
            --gray-900: #111827;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--gray-100);
            color: var(--gray-900);
        }

        .dark-mode {
            background-color: var(--secondary);
            color: var(--white);
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background-color: var(--secondary);
            color: var(--white);
            padding: 20px 0;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid var(--gray-700);
            margin-bottom: 20px;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            color: var(--gray-300);
            text-decoration: none;
            transition: all 0.3s;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: var(--primary);
            color: var(--white);
        }

        .sidebar-menu a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background-color: var(--white);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .dark-mode .header {
            background-color: var(--secondary-light);
        }

        .header-title {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .header-actions {
            display: flex;
            align-items: center;
        }

        .header-actions button {
            background: none;
            border: none;
            cursor: pointer;
            margin-left: 15px;
            color: var(--gray-600);
        }

        .dark-mode .header-actions button {
            color: var(--gray-300);
        }

        .card {
            background-color: var(--white);
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .dark-mode .card {
            background-color: var(--secondary-light);
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background-color: var(--white);
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            display: flex;
            align-items: center;
        }

        .dark-mode .stat-card {
            background-color: var(--secondary-light);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.5rem;
        }

        .stat-icon.blue {
            background-color: rgba(10, 132, 255, 0.1);
            color: var(--primary);
        }

        .stat-icon.green {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .stat-icon.purple {
            background-color: rgba(139, 92, 246, 0.1);
            color: #8B5CF6;
        }

        .stat-info h3 {
            margin: 0;
            font-size: 0.9rem;
            color: var(--gray-500);
        }

        .dark-mode .stat-info h3 {
            color: var(--gray-400);
        }

        .stat-info p {
            margin: 5px 0 0;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .chart-container {
            height: 300px;
            position: relative;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        thead th {
            text-align: left;
            padding: 12px;
            background-color: var(--gray-100);
            color: var(--gray-700);
            font-weight: 600;
        }

        .dark-mode thead th {
            background-color: var(--gray-800);
            color: var(--gray-300);
        }

        tbody td {
            padding: 12px;
            border-bottom: 1px solid var(--gray-200);
        }

        .dark-mode tbody td {
            border-bottom: 1px solid var(--gray-700);
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-badge.valid {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .dark-mode .status-badge.valid {
            background-color: rgba(16, 185, 129, 0.2);
        }

        .status-badge.invalid {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
        }

        .dark-mode .status-badge.invalid {
            background-color: rgba(239, 68, 68, 0.2);
        }

        .status-badge.pending {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning);
        }

        .dark-mode .status-badge.pending {
            background-color: rgba(245, 158, 11, 0.2);
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .action-button {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--primary);
        }

        .action-button:hover {
            color: var(--primary-dark);
        }

        .action-button.delete {
            color: var(--danger);
        }

        .action-button.delete:hover {
            color: #B91C1C;
        }

        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
        }

        .pagination-info {
            color: var(--gray-500);
        }

        .dark-mode .pagination-info {
            color: var(--gray-400);
        }

        .pagination-buttons {
            display: flex;
            gap: 10px;
        }

        .pagination-button {
            padding: 8px 16px;
            border: 1px solid var(--gray-300);
            background-color: var(--white);
            color: var(--gray-700);
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .dark-mode .pagination-button {
            background-color: var(--secondary-light);
            border-color: var(--gray-700);
            color: var(--gray-300);
        }

        .pagination-button:hover {
            background-color: var(--gray-100);
        }

        .dark-mode .pagination-button:hover {
            background-color: var(--gray-800);
        }

        /* Toggle Switch */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--gray-300);
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 10px 0;
            }

            .sidebar-header {
                padding: 0 10px 10px;
            }

            .charts-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>NovaFuse UCECS</h2>
            </div>
            <ul class="sidebar-menu">
                <li><a href="#" class="active"><i class="fas fa-home"></i> Dashboard</a></li>
                <li><a href="#"><i class="fas fa-folder"></i> Evidence</a></li>
                <li><a href="#"><i class="fas fa-check-square"></i> Requirements</a></li>
                <li><a href="#"><i class="fas fa-search"></i> Search</a></li>
                <li><a href="#"><i class="fas fa-file-alt"></i> Reports</a></li>
                <li><a href="#"><i class="fas fa-cog"></i> Settings</a></li>
            </ul>
        </div>
        <div class="main-content">
            <div class="header">
                <div class="header-title">Dashboard</div>
                <div class="header-actions">
                    <button id="notifications-btn"><i class="fas fa-bell"></i></button>
                    <button id="user-btn"><i class="fas fa-user-circle"></i></button>
                    <label class="switch">
                        <input type="checkbox" id="theme-toggle">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon blue">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Total Evidence</h3>
                        <p>256</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon green">
                        <i class="fas fa-check-square"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Total Requirements</h3>
                        <p>124</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon purple">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Compliance Rate</h3>
                        <p>78%</p>
                    </div>
                </div>
            </div>

            <div class="charts-grid">
                <div class="card">
                    <div class="card-title">Evidence by Category</div>
                    <div class="chart-container">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
                <div class="card">
                    <div class="card-title">Evidence by Status</div>
                    <div class="chart-container">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-title">Recent Evidence</div>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>ev-001</td>
                            <td>Password Policy</td>
                            <td>Policies</td>
                            <td><span class="status-badge valid">Valid</span></td>
                            <td>2023-10-15</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-button"><i class="fas fa-eye"></i></button>
                                    <button class="action-button"><i class="fas fa-edit"></i></button>
                                    <button class="action-button delete"><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>ev-002</td>
                            <td>Firewall Configuration</td>
                            <td>Configurations</td>
                            <td><span class="status-badge valid">Valid</span></td>
                            <td>2023-10-14</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-button"><i class="fas fa-eye"></i></button>
                                    <button class="action-button"><i class="fas fa-edit"></i></button>
                                    <button class="action-button delete"><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>ev-003</td>
                            <td>User Access Review</td>
                            <td>Reports</td>
                            <td><span class="status-badge pending">Pending</span></td>
                            <td>2023-10-13</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-button"><i class="fas fa-eye"></i></button>
                                    <button class="action-button"><i class="fas fa-edit"></i></button>
                                    <button class="action-button delete"><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>ev-004</td>
                            <td>Incident Response Plan</td>
                            <td>Procedures</td>
                            <td><span class="status-badge invalid">Invalid</span></td>
                            <td>2023-10-12</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-button"><i class="fas fa-eye"></i></button>
                                    <button class="action-button"><i class="fas fa-edit"></i></button>
                                    <button class="action-button delete"><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>ev-005</td>
                            <td>System Logs</td>
                            <td>Logs</td>
                            <td><span class="status-badge valid">Valid</span></td>
                            <td>2023-10-11</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-button"><i class="fas fa-eye"></i></button>
                                    <button class="action-button"><i class="fas fa-edit"></i></button>
                                    <button class="action-button delete"><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="pagination">
                    <div class="pagination-info">
                        Showing <span>5</span> of <span>256</span> evidence items
                    </div>
                    <div class="pagination-buttons">
                        <button class="pagination-button">Previous</button>
                        <button class="pagination-button">Next</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Theme Toggle
        const themeToggle = document.getElementById('theme-toggle');
        const body = document.body;

        // Check for saved theme preference
        if (localStorage.getItem('dark-mode') === 'true') {
            body.classList.add('dark-mode');
            themeToggle.checked = true;
        }

        themeToggle.addEventListener('change', () => {
            if (themeToggle.checked) {
                body.classList.add('dark-mode');
                localStorage.setItem('dark-mode', 'true');
            } else {
                body.classList.remove('dark-mode');
                localStorage.setItem('dark-mode', 'false');
            }
        });

        // Charts
        document.addEventListener('DOMContentLoaded', function() {
            // Evidence by Category Chart
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            const categoryChart = new Chart(categoryCtx, {
                type: 'bar',
                data: {
                    labels: ['Policies', 'Procedures', 'Configurations', 'Logs', 'Reports'],
                    datasets: [{
                        label: 'Evidence Count',
                        data: [42, 35, 68, 89, 22],
                        backgroundColor: [
                            'rgba(10, 132, 255, 0.6)',
                            'rgba(16, 185, 129, 0.6)',
                            'rgba(245, 158, 11, 0.6)',
                            'rgba(59, 130, 246, 0.6)',
                            'rgba(139, 92, 246, 0.6)'
                        ],
                        borderColor: [
                            'rgba(10, 132, 255, 1)',
                            'rgba(16, 185, 129, 1)',
                            'rgba(245, 158, 11, 1)',
                            'rgba(59, 130, 246, 1)',
                            'rgba(139, 92, 246, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Evidence by Status Chart
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            const statusChart = new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Valid', 'Invalid', 'Pending'],
                    datasets: [{
                        label: 'Evidence Count',
                        data: [189, 32, 35],
                        backgroundColor: [
                            'rgba(16, 185, 129, 0.6)',
                            'rgba(239, 68, 68, 0.6)',
                            'rgba(245, 158, 11, 0.6)'
                        ],
                        borderColor: [
                            'rgba(16, 185, 129, 1)',
                            'rgba(239, 68, 68, 1)',
                            'rgba(245, 158, 11, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Update charts when theme changes
            themeToggle.addEventListener('change', () => {
                updateChartsTheme();
            });

            function updateChartsTheme() {
                const isDark = body.classList.contains('dark-mode');
                const textColor = isDark ? '#D1D5DB' : '#374151';

                // Update category chart
                categoryChart.options.scales.x.ticks.color = textColor;
                categoryChart.options.scales.y.ticks.color = textColor;
                categoryChart.options.scales.x.grid.color = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
                categoryChart.options.scales.y.grid.color = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
                categoryChart.options.plugins.legend.labels.color = textColor;
                categoryChart.update();

                // Update status chart
                statusChart.options.plugins.legend.labels.color = textColor;
                statusChart.update();
            }

            // Initial theme setup for charts
            updateChartsTheme();
        });
    </script>
</body>
</html>
