import { useState } from 'react';
import CBEHeader from '../components/CBEHeader';
import ServiceCards from '../components/ServiceCards';
import BrowserFrame from '../components/BrowserFrame';
import ConsciousnessDashboard from '../components/ConsciousnessDashboard';

export default function CBEBrowser() {
  const [currentUrl, setCurrentUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showHomePage, setShowHomePage] = useState(true);
  const [cbeMode, setCbeMode] = useState(true);
  const [consciousnessData, setConsciousnessData] = useState({
    overall: 95.7,
    content: 94.2,
    intent: 97.1,
    coherence: 95.7,
    psiSnap: true,
    analysisTime: 11
  });

  const handleNavigate = (url) => {
    if (!url) return;
    
    setIsLoading(true);
    setCurrentUrl(url);
    setShowHomePage(false);
    
    // Simulate consciousness analysis
    setTimeout(() => {
      const mockData = {
        overall: Math.floor(Math.random() * 40) + 60,
        content: Math.floor(Math.random() * 30) + 70,
        intent: Math.floor(Math.random() * 30) + 70,
        coherence: Math.floor(Math.random() * 40) + 60,
        psiSnap: Math.random() > 0.3,
        analysisTime: Math.floor(Math.random() * 20) + 5
      };
      setConsciousnessData(mockData);
      setIsLoading(false);
    }, 1500);
  };

  const handleHome = () => {
    setShowHomePage(true);
    setCurrentUrl('');
    setConsciousnessData({
      overall: 95.7,
      content: 94.2,
      intent: 97.1,
      coherence: 95.7,
      psiSnap: true,
      analysisTime: 11
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Quantum Field Background */}
      <div className="fixed inset-0 opacity-30 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-transparent to-pink-600/10 animate-pulse" />
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-500/5 rounded-full blur-3xl animate-pulse" />
      </div>

      {/* Main Content */}
      <div className="relative z-10">
        <CBEHeader 
          currentUrl={currentUrl}
          onNavigate={handleNavigate}
          onHome={handleHome}
          cbeMode={cbeMode}
          setCbeMode={setCbeMode}
          consciousnessData={consciousnessData}
        />
        
        {showHomePage ? (
          <ServiceCards onNavigate={handleNavigate} />
        ) : (
          <div className="flex h-[calc(100vh-80px)]">
            <ConsciousnessDashboard consciousnessData={consciousnessData} />
            <BrowserFrame 
              url={currentUrl}
              isLoading={isLoading}
              consciousnessData={consciousnessData}
            />
          </div>
        )}
      </div>
    </div>
  );
}
