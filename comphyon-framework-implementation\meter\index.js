/**
 * Meter
 *
 * This module exports all components of the Meter system.
 * The Meter measures entropy across domains and manages thresholds and alerts.
 */

const UniversalEntropyMeasurement = require('./universal-entropy-measurement');
const ThresholdManagement = require('./threshold-management');
const AlertingSystem = require('./alerting-system');

/**
 * Create a basic Meter system
 * @param {Object} options - Configuration options
 * @returns {Object} - Meter system components
 */
function createMeterSystem(options = {}) {
  // Create components
  const universalEntropyMeasurement = new UniversalEntropyMeasurement(options.universalEntropyMeasurementOptions);
  const thresholdManagement = new ThresholdManagement(options.thresholdManagementOptions);
  const alertingSystem = new AlertingSystem(options.alertingSystemOptions);

  return {
    universalEntropyMeasurement,
    thresholdManagement,
    alertingSystem
  };
}

/**
 * Create an enhanced Meter system with integrated components
 * @param {Object} options - Configuration options
 * @param {Object} bridge - Bridge system
 * @returns {Object} - Enhanced Meter system
 */
function createEnhancedMeterSystem(options = {}, bridge = null) {
  // Create basic system
  const meterSystem = createMeterSystem(options);

  // Set up event listeners for integration

  // When entropy is updated, evaluate thresholds
  meterSystem.universalEntropyMeasurement.on('entropy-update', (data) => {
    // Evaluate universal entropy
    meterSystem.thresholdManagement.evaluateThreshold('universal', data.universalEntropy, {
      source: 'entropy-update',
      timestamp: data.timestamp
    });

    // Evaluate domain entropy
    for (const domain of ['cyber', 'financial', 'biological']) {
      meterSystem.thresholdManagement.evaluateThreshold(domain, data.domainEntropy[domain].overallEntropy, {
        source: 'entropy-update',
        timestamp: data.timestamp
      });
    }
  });

  // When a threshold is violated, process alert
  meterSystem.thresholdManagement.on('threshold-evaluation', (data) => {
    if (data.thresholdLevel !== 'normal') {
      meterSystem.alertingSystem.processAlert({
        id: `alert-${data.domain}-${data.thresholdLevel}-${Date.now()}`,
        domain: data.domain,
        level: data.thresholdLevel,
        entropyValue: data.entropyValue,
        threshold: data.violatedThreshold,
        deviation: data.entropyValue - data.violatedThreshold,
        timestamp: data.timestamp,
        metadata: data.metadata
      });
    }
  });

  // When an alert is resolved in threshold management, resolve it in alerting system
  meterSystem.thresholdManagement.on('alert-resolved', (data) => {
    try {
      meterSystem.alertingSystem.resolveAlert(data.id, {
        source: 'threshold-management',
        timestamp: data.resolvedAt
      });
    } catch (error) {
      if (options.enableLogging) {
        console.error(`Meter: Error resolving alert in alerting system: ${error.message}`);
      }
    }
  });

  // Set up bridge integration if provided
  if (bridge) {
    _integrateBridge(meterSystem, bridge, options);
  }

  // Add enhanced methods
  const enhancedSystem = {
    ...meterSystem,

    /**
     * Start all components
     * @returns {boolean} - Success status
     */
    start() {
      const uemStarted = meterSystem.universalEntropyMeasurement.start();
      const tmStarted = true; // ThresholdManagement doesn't have start method
      const asStarted = meterSystem.alertingSystem.start();

      return uemStarted && tmStarted && asStarted;
    },

    /**
     * Stop all components
     * @returns {boolean} - Success status
     */
    stop() {
      const uemStopped = meterSystem.universalEntropyMeasurement.stop();
      const tmStopped = true; // ThresholdManagement doesn't have stop method
      const asStopped = meterSystem.alertingSystem.stop();

      return uemStopped && tmStopped && asStopped;
    },

    /**
     * Get unified state from all components
     * @returns {Object} - Unified state
     */
    getUnifiedState() {
      return {
        universalEntropy: meterSystem.universalEntropyMeasurement.getUniversalEntropy(),
        domainEntropy: meterSystem.universalEntropyMeasurement.getDomainEntropy(),
        activeAlerts: meterSystem.alertingSystem.getActiveAlerts(),
        timestamp: Date.now()
      };
    },

    /**
     * Get unified metrics from all components
     * @returns {Object} - Unified metrics
     */
    getUnifiedMetrics() {
      return {
        universalEntropyMeasurement: meterSystem.universalEntropyMeasurement.getMetrics(),
        thresholdManagement: meterSystem.thresholdManagement.getMetrics(),
        alertingSystem: meterSystem.alertingSystem.getMetrics(),
        timestamp: Date.now()
      };
    },

    /**
     * Process domain data
     * @param {string} domain - Domain (cyber, financial, biological)
     * @param {string} metric - Metric name
     * @param {number} value - Metric value
     * @param {Object} metadata - Additional metadata
     * @returns {Object} - Processing result
     */
    processDomainData(domain, metric, value, metadata = {}) {
      // Update entropy measurement
      const entropyValue = meterSystem.universalEntropyMeasurement.updateDomainMetric(domain, metric, value, metadata);

      // Get domain entropy
      const domainEntropy = meterSystem.universalEntropyMeasurement.getDomainEntropy(domain);

      // Calculate Comphyon value
      const comphyonValue = meterSystem.universalEntropyMeasurement.calculateComphyon();

      return {
        domain,
        metric,
        value,
        entropyValue,
        domainEntropy,
        comphyonValue,
        timestamp: Date.now()
      };
    },

    /**
     * Get entropy dashboard data
     * @returns {Object} - Dashboard data
     */
    getEntropyDashboard() {
      // Get current entropy values
      const universalEntropy = meterSystem.universalEntropyMeasurement.getUniversalEntropy();
      const domainEntropy = meterSystem.universalEntropyMeasurement.getDomainEntropy();
      const entropyHistory = meterSystem.universalEntropyMeasurement.getEntropyHistory();

      // Get threshold data
      const thresholds = meterSystem.thresholdManagement.getThresholds();
      const thresholdViolations = meterSystem.thresholdManagement.getThresholdViolations();

      // Get alert data
      const activeAlerts = meterSystem.alertingSystem.getActiveAlerts();
      const alertHistory = meterSystem.alertingSystem.getAlertHistory();

      // Calculate Comphyon value
      const comphyonValue = meterSystem.universalEntropyMeasurement.calculateComphyon();

      return {
        universalEntropy,
        domainEntropy,
        entropyHistory,
        thresholds,
        thresholdViolations,
        activeAlerts,
        alertHistory,
        comphyonValue,
        timestamp: Date.now()
      };
    },

    /**
     * Configure alerting channels
     * @param {Object} channelConfig - Channel configuration
     * @returns {Object} - Updated configuration
     */
    configureAlertingChannels(channelConfig) {
      const result = {};

      for (const [channel, config] of Object.entries(channelConfig)) {
        result[channel] = meterSystem.alertingSystem.configureNotificationChannel(channel, config);
      }

      return result;
    },

    /**
     * Configure thresholds
     * @param {Object} thresholdConfig - Threshold configuration
     * @returns {Object} - Updated configuration
     */
    configureThresholds(thresholdConfig) {
      const result = {};

      for (const [domain, levels] of Object.entries(thresholdConfig)) {
        result[domain] = {};

        for (const [level, value] of Object.entries(levels)) {
          result[domain][level] = meterSystem.thresholdManagement.setThreshold(domain, level, value);
        }
      }

      return result;
    }
  };

  return enhancedSystem;
}

/**
 * Integrate Bridge with Meter
 * @param {Object} meterSystem - Meter system
 * @param {Object} bridge - Bridge system
 * @param {Object} options - Configuration options
 * @private
 */
function _integrateBridge(meterSystem, bridge, options = {}) {
  if (!bridge) return;

  try {
    // Listen for risk updates from Bridge
    if (bridge.unifiedRiskScoring && typeof bridge.unifiedRiskScoring.on === 'function') {
      bridge.unifiedRiskScoring.on('risk-update', (data) => {
        // Map risk scores to entropy metrics
        if (data.updatedDomain === 'cyber') {
          meterSystem.universalEntropyMeasurement.updateDomainMetric('cyber', 'policyEntropy', data.domainRiskScores.cyber, {
            source: 'bridge',
            timestamp: data.timestamp
          });
        } else if (data.updatedDomain === 'financial') {
          meterSystem.universalEntropyMeasurement.updateDomainMetric('financial', 'transactionEntropy', data.domainRiskScores.financial, {
            source: 'bridge',
            timestamp: data.timestamp
          });
        } else if (data.updatedDomain === 'biological') {
          meterSystem.universalEntropyMeasurement.updateDomainMetric('biological', 'inflammationLevel', data.domainRiskScores.biological, {
            source: 'bridge',
            timestamp: data.timestamp
          });
        }
      });
    }

    // Listen for cross-domain events from Bridge
    if (bridge.crossDomainIntegration && typeof bridge.crossDomainIntegration.on === 'function') {
      bridge.crossDomainIntegration.on('pattern-detected', (data) => {
        // Update entropy based on pattern
        const domains = data.domains || [];

        for (const domain of domains) {
          if (['cyber', 'financial', 'biological'].includes(domain)) {
            // Increase entropy based on pattern priority
            const entropyIncrease = data.priority === 'critical' ? 0.2 :
                                   data.priority === 'high' ? 0.15 :
                                   data.priority === 'medium' ? 0.1 : 0.05;

            // Get current entropy
            const currentEntropy = meterSystem.universalEntropyMeasurement.getDomainEntropy(domain).overallEntropy;

            // Update entropy (capped at 1.0)
            const newEntropy = Math.min(1.0, currentEntropy + entropyIncrease);

            // Update primary metric for domain
            if (domain === 'cyber') {
              meterSystem.universalEntropyMeasurement.updateDomainMetric(domain, 'policyEntropy', newEntropy, {
                source: 'pattern-detection',
                patternId: data.patternId,
                timestamp: data.timestamp
              });
            } else if (domain === 'financial') {
              meterSystem.universalEntropyMeasurement.updateDomainMetric(domain, 'transactionEntropy', newEntropy, {
                source: 'pattern-detection',
                patternId: data.patternId,
                timestamp: data.timestamp
              });
            } else if (domain === 'biological') {
              meterSystem.universalEntropyMeasurement.updateDomainMetric(domain, 'inflammationLevel', newEntropy, {
                source: 'pattern-detection',
                patternId: data.patternId,
                timestamp: data.timestamp
              });
            }
          }
        }
      });
    }

    // Listen for entropy updates from Meter
    meterSystem.universalEntropyMeasurement.on('entropy-update', (data) => {
      // If Bridge has processDomainData method, update it with entropy data
      // But only if the source is not from the bridge itself (to avoid circular updates)
      if (bridge.processDomainData &&
          typeof bridge.processDomainData === 'function' &&
          (!data.metadata || data.metadata.source !== 'bridge')) {

        if (data.updatedDomain === 'cyber') {
          bridge.processDomainData('cyber', 'policy_entropy', data.domainEntropy.cyber.policyEntropy, {
            source: 'meter',
            timestamp: data.timestamp,
            skipFeedback: true // Flag to prevent circular updates
          });
        } else if (data.updatedDomain === 'financial') {
          bridge.processDomainData('financial', 'transaction_entropy', data.domainEntropy.financial.transactionEntropy, {
            source: 'meter',
            timestamp: data.timestamp,
            skipFeedback: true // Flag to prevent circular updates
          });
        } else if (data.updatedDomain === 'biological') {
          bridge.processDomainData('biological', 'inflammation_level', data.domainEntropy.biological.inflammationLevel, {
            source: 'meter',
            timestamp: data.timestamp,
            skipFeedback: true // Flag to prevent circular updates
          });
        }
      }
    });

    // Listen for alerts from Meter
    meterSystem.alertingSystem.on('alert-processed', (alert) => {
      // If Bridge has processDomainEvent method, send alert as event
      if (bridge.processDomainEvent && typeof bridge.processDomainEvent === 'function') {
        bridge.processDomainEvent(alert.domain, {
          id: alert.id,
          type: 'entropy_threshold_violation',
          severity: alert.level,
          description: `${alert.domain} domain entropy threshold violation`,
          timestamp: alert.timestamp,
          details: {
            entropyValue: alert.entropyValue,
            threshold: alert.threshold,
            deviation: alert.deviation
          }
        });
      }
    });
  } catch (error) {
    if (options.enableLogging) {
      console.error(`Meter: Error integrating with Bridge: ${error.message}`);
    }
  }
}

module.exports = {
  UniversalEntropyMeasurement,
  ThresholdManagement,
  AlertingSystem,
  createMeterSystem,
  createEnhancedMeterSystem
};
