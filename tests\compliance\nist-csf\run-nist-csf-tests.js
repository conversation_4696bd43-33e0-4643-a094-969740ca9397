/**
 * Run NIST CSF Compliance Tests
 * 
 * This script runs the NIST CSF compliance tests and generates a report.
 */

const path = require('path');
const { createNistCsfFramework } = require('./nist-csf-framework');
const { generateHtmlReport } = require('../framework/compliance-test-framework');

/**
 * Run the NIST CSF compliance tests
 */
async function runNistCsfTests() {
  console.log('Running NIST CSF Compliance Tests...');
  
  // Create the NIST CSF framework
  const framework = createNistCsfFramework();
  
  // Run the tests
  const results = await framework.runTests();
  
  // Generate the report
  const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
  const reportPath = path.join(__dirname, '..', '..', '..', 'test-results', 'compliance', `nist-csf-report-${timestamp}.json`);
  await framework.saveReport(reportPath);
  
  // Generate the HTML report
  const htmlReportPath = path.join(__dirname, '..', '..', '..', 'test-results', 'compliance', `nist-csf-report-${timestamp}.html`);
  await generateHtmlReport(results, htmlReportPath);
  
  console.log('NIST CSF Compliance Tests completed.');
  console.log(`Report saved to: ${reportPath}`);
  console.log(`HTML Report saved to: ${htmlReportPath}`);
}

// Run the tests if this script is executed directly
if (require.main === module) {
  runNistCsfTests().catch(error => {
    console.error('Error running NIST CSF tests:', error);
    process.exit(1);
  });
}

module.exports = {
  runNistCsfTests
};
