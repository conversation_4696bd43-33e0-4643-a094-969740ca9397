{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "require", "RemediationEngine", "EncryptionService", "performance", "fs", "hasCredentials", "process", "env", "GOOGLE_APPLICATION_CREDENTIALS", "existsSync", "config", "projectId", "GCP_PROJECT_ID", "datasetId", "BQ_TEST_DATASET", "tableId", "BQ_TEST_TABLE", "location", "BQ_LOCATION", "describe", "big<PERSON>y", "remediationEngine", "encryptionService", "dataset", "table", "beforeAll", "<PERSON><PERSON>ey", "get", "error", "code", "createDataset", "console", "log", "schema", "name", "type", "mode", "createTable", "rows", "id", "email", "ssn", "created_at", "Date", "insert", "length", "registerAction", "parameters", "columnName", "keyId", "query", "encryptedRows", "Promise", "all", "map", "row", "encrypted", "encrypt", "JSON", "stringify", "delete", "resolve", "setTimeout", "newTable", "metadata", "success", "rowsEncrypted", "allowedRoles", "getMetadata", "newAccess", "role", "specialGroup", "access", "setMetadata", "updatedAccess", "afterAll", "conditionalTest", "it", "skip", "startTime", "now", "sensitiveColumns", "endTime", "duration", "toFixed", "expect", "Array", "isArray", "toBe", "toBeGreaterThan", "ssnColumn", "find", "col", "column_name", "toBeDefined", "keys", "from", "keyCache", "remediationScenario", "framework", "control", "severity", "resource", "provider", "remediationSteps", "action", "remediationResult", "executeRemediation", "toHaveProperty", "steps", "toContain", "toBeLessThan"], "sources": ["bigquery-live-integration.test.js"], "sourcesContent": ["/**\n * NovaConnect - BigQuery Live Integration Test\n * \n * This test connects to the actual Google BigQuery API\n * to validate data protection and remediation capabilities.\n * \n * NOTE: This test requires valid GCP credentials with BigQuery access.\n * Set the GOOGLE_APPLICATION_CREDENTIALS environment variable to point\n * to a service account key file with appropriate permissions.\n */\n\nconst { BigQuery } = require('@google-cloud/bigquery');\nconst { RemediationEngine } = require('../../../src/engines/remediation-engine');\nconst { EncryptionService } = require('../../../src/security/encryption-service');\nconst { performance } = require('perf_hooks');\nconst fs = require('fs');\n\n// Skip tests if credentials are not available\nconst hasCredentials = process.env.GOOGLE_APPLICATION_CREDENTIALS && \n  fs.existsSync(process.env.GOOGLE_APPLICATION_CREDENTIALS);\n\n// Test configuration\nconst config = {\n  projectId: process.env.GCP_PROJECT_ID || 'test-project',\n  datasetId: process.env.BQ_TEST_DATASET || 'test_dataset',\n  tableId: process.env.BQ_TEST_TABLE || 'test_table',\n  location: process.env.BQ_LOCATION || 'US'\n};\n\ndescribe('BigQuery Live Integration', () => {\n  let bigquery;\n  let remediationEngine;\n  let encryptionService;\n  let dataset;\n  let table;\n  \n  beforeAll(async () => {\n    // Initialize services\n    remediationEngine = new RemediationEngine();\n    encryptionService = new EncryptionService();\n    \n    // Generate encryption key\n    await encryptionService.generateKey();\n    \n    if (hasCredentials) {\n      try {\n        // Initialize BigQuery client\n        bigquery = new BigQuery({\n          projectId: config.projectId\n        });\n        \n        // Check if dataset exists, create if not\n        try {\n          [dataset] = await bigquery.dataset(config.datasetId).get();\n        } catch (error) {\n          if (error.code === 404) {\n            // Create dataset for testing\n            [dataset] = await bigquery.createDataset(config.datasetId, {\n              location: config.location\n            });\n            console.log(`Created dataset ${config.datasetId}`);\n          } else {\n            throw error;\n          }\n        }\n        \n        // Check if table exists, create if not\n        try {\n          [table] = await dataset.table(config.tableId).get();\n        } catch (error) {\n          if (error.code === 404) {\n            // Create table for testing\n            const schema = [\n              { name: 'id', type: 'STRING', mode: 'REQUIRED' },\n              { name: 'name', type: 'STRING' },\n              { name: 'email', type: 'STRING' },\n              { name: 'ssn', type: 'STRING' }, // Sensitive data\n              { name: 'created_at', type: 'TIMESTAMP' }\n            ];\n            \n            [table] = await dataset.createTable(config.tableId, { schema });\n            console.log(`Created table ${config.tableId}`);\n            \n            // Insert test data\n            const rows = [\n              { id: '1', name: 'John Doe', email: '<EMAIL>', ssn: '***********', created_at: new Date() },\n              { id: '2', name: 'Jane Smith', email: '<EMAIL>', ssn: '***********', created_at: new Date() }\n            ];\n            \n            await table.insert(rows);\n            console.log(`Inserted ${rows.length} rows`);\n          } else {\n            throw error;\n          }\n        }\n        \n        // Register remediation actions\n        remediationEngine.registerAction('encrypt-bigquery-column', async ({ parameters }) => {\n          const { projectId, datasetId, tableId, columnName, keyId } = parameters;\n          \n          // Get the table\n          const dataset = bigquery.dataset(datasetId);\n          const table = dataset.table(tableId);\n          \n          // Get the current data\n          const [rows] = await table.query(`SELECT * FROM \\`${projectId}.${datasetId}.${tableId}\\``);\n          \n          // Encrypt the sensitive column\n          const encryptedRows = await Promise.all(rows.map(async (row) => {\n            if (row[columnName]) {\n              const encrypted = await encryptionService.encrypt(row[columnName], keyId);\n              return {\n                ...row,\n                [columnName]: `ENCRYPTED:${JSON.stringify(encrypted)}`\n              };\n            }\n            return row;\n          }));\n          \n          // Update the table with encrypted data\n          // In a real implementation, this would use a more efficient approach\n          await table.delete();\n          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for deletion\n          \n          const [newTable] = await dataset.createTable(tableId, {\n            schema: table.metadata.schema\n          });\n          \n          await newTable.insert(encryptedRows);\n          \n          return { success: true, rowsEncrypted: encryptedRows.length };\n        });\n        \n        remediationEngine.registerAction('update-bigquery-access', async ({ parameters }) => {\n          const { datasetId, allowedRoles } = parameters;\n          \n          // Get the dataset\n          const dataset = bigquery.dataset(datasetId);\n          \n          // Get current access\n          const [metadata] = await dataset.getMetadata();\n          \n          // Update access controls\n          const newAccess = allowedRoles.map(role => ({\n            role: 'READER',\n            specialGroup: role\n          }));\n          \n          metadata.access = newAccess;\n          await dataset.setMetadata(metadata);\n          \n          return { success: true, updatedAccess: newAccess };\n        });\n      } catch (error) {\n        console.error('Error setting up BigQuery test:', error);\n      }\n    }\n  });\n  \n  afterAll(async () => {\n    if (hasCredentials && dataset) {\n      try {\n        // Clean up test resources\n        // Uncomment to actually delete the test dataset\n        // await dataset.delete({ force: true });\n        console.log(`Test cleanup complete`);\n      } catch (error) {\n        console.error('Error cleaning up:', error);\n      }\n    }\n  });\n  \n  // Skip tests if credentials are not available\n  const conditionalTest = hasCredentials ? it : it.skip;\n  \n  conditionalTest('should detect sensitive data in BigQuery', async () => {\n    // Skip if no credentials\n    if (!hasCredentials) {\n      return;\n    }\n    \n    const startTime = performance.now();\n    \n    // Query for sensitive data patterns\n    const query = `\n      SELECT column_name, data_type\n      FROM \\`${config.projectId}\\`.${config.datasetId}.INFORMATION_SCHEMA.COLUMNS\n      WHERE table_name = '${config.tableId}'\n      AND (\n        column_name LIKE '%ssn%' OR\n        column_name LIKE '%social%' OR\n        column_name LIKE '%tax%' OR\n        column_name LIKE '%passport%' OR\n        column_name LIKE '%credit%' OR\n        column_name LIKE '%card%' OR\n        column_name LIKE '%password%' OR\n        column_name LIKE '%secret%'\n      )\n    `;\n    \n    const [sensitiveColumns] = await bigquery.query(query);\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Log performance metrics\n    console.log(`Detected ${sensitiveColumns.length} sensitive columns in ${duration.toFixed(2)}ms`);\n    \n    // Verify sensitive data detection\n    expect(Array.isArray(sensitiveColumns)).toBe(true);\n    expect(sensitiveColumns.length).toBeGreaterThan(0);\n    \n    // Check if SSN column was detected\n    const ssnColumn = sensitiveColumns.find(col => col.column_name === 'ssn');\n    expect(ssnColumn).toBeDefined();\n  }, 30000);\n  \n  conditionalTest('should encrypt sensitive data in BigQuery', async () => {\n    // Skip if no credentials\n    if (!hasCredentials) {\n      return;\n    }\n    \n    // Get encryption key\n    const keys = Array.from(encryptionService.keyCache.keys());\n    const keyId = keys[0];\n    \n    // Create remediation scenario\n    const remediationScenario = {\n      id: `bq-remediation-${Date.now()}`,\n      type: 'compliance',\n      framework: 'HIPAA',\n      control: '164.312(a)(1)',\n      severity: 'high',\n      resource: {\n        id: config.tableId,\n        type: 'bigquery.table',\n        name: config.tableId,\n        provider: 'gcp',\n        projectId: config.projectId\n      },\n      remediationSteps: [\n        {\n          id: 'step-1',\n          action: 'encrypt-bigquery-column',\n          parameters: {\n            projectId: config.projectId,\n            datasetId: config.datasetId,\n            tableId: config.tableId,\n            columnName: 'ssn',\n            keyId\n          }\n        }\n      ]\n    };\n    \n    const startTime = performance.now();\n    \n    // Execute the remediation\n    const remediationResult = await remediationEngine.executeRemediation(remediationScenario);\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Log performance metrics\n    console.log(`Executed encryption remediation in ${duration.toFixed(2)}ms`);\n    \n    // Verify remediation result\n    expect(remediationResult).toHaveProperty('id');\n    expect(remediationResult).toHaveProperty('status');\n    expect(remediationResult).toHaveProperty('steps');\n    expect(remediationResult.steps.length).toBe(1);\n    expect(remediationResult.steps[0].success).toBe(true);\n    \n    // Verify data is encrypted\n    const [rows] = await table.query(`SELECT * FROM \\`${config.projectId}.${config.datasetId}.${config.tableId}\\` LIMIT 1`);\n    \n    if (rows.length > 0) {\n      const row = rows[0];\n      expect(row.ssn).toContain('ENCRYPTED:');\n    }\n    \n    // Verify remediation speed\n    expect(duration).toBeLessThan(8000); // Less than 8 seconds\n  }, 60000);\n  \n  conditionalTest('should update BigQuery access controls', async () => {\n    // Skip if no credentials\n    if (!hasCredentials) {\n      return;\n    }\n    \n    // Create remediation scenario\n    const remediationScenario = {\n      id: `bq-access-remediation-${Date.now()}`,\n      type: 'compliance',\n      framework: 'HIPAA',\n      control: '164.312(a)(1)',\n      severity: 'high',\n      resource: {\n        id: config.datasetId,\n        type: 'bigquery.dataset',\n        name: config.datasetId,\n        provider: 'gcp',\n        projectId: config.projectId\n      },\n      remediationSteps: [\n        {\n          id: 'step-1',\n          action: 'update-bigquery-access',\n          parameters: {\n            projectId: config.projectId,\n            datasetId: config.datasetId,\n            allowedRoles: ['projectOwners', 'projectReaders']\n          }\n        }\n      ]\n    };\n    \n    const startTime = performance.now();\n    \n    // Execute the remediation\n    const remediationResult = await remediationEngine.executeRemediation(remediationScenario);\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Log performance metrics\n    console.log(`Executed access control remediation in ${duration.toFixed(2)}ms`);\n    \n    // Verify remediation result\n    expect(remediationResult).toHaveProperty('id');\n    expect(remediationResult).toHaveProperty('status');\n    expect(remediationResult).toHaveProperty('steps');\n    expect(remediationResult.steps.length).toBe(1);\n    expect(remediationResult.steps[0].success).toBe(true);\n    \n    // Verify access controls are updated\n    const [metadata] = await dataset.getMetadata();\n    expect(metadata.access).toBeDefined();\n    \n    // Verify remediation speed\n    expect(duration).toBeLessThan(8000); // Less than 8 seconds\n  }, 30000);\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAS,CAAC,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AACtD,MAAM;EAAEC;AAAkB,CAAC,GAAGD,OAAO,CAAC,yCAAyC,CAAC;AAChF,MAAM;EAAEE;AAAkB,CAAC,GAAGF,OAAO,CAAC,0CAA0C,CAAC;AACjF,MAAM;EAAEG;AAAY,CAAC,GAAGH,OAAO,CAAC,YAAY,CAAC;AAC7C,MAAMI,EAAE,GAAGJ,OAAO,CAAC,IAAI,CAAC;;AAExB;AACA,MAAMK,cAAc,GAAGC,OAAO,CAACC,GAAG,CAACC,8BAA8B,IAC/DJ,EAAE,CAACK,UAAU,CAACH,OAAO,CAACC,GAAG,CAACC,8BAA8B,CAAC;;AAE3D;AACA,MAAME,MAAM,GAAG;EACbC,SAAS,EAAEL,OAAO,CAACC,GAAG,CAACK,cAAc,IAAI,cAAc;EACvDC,SAAS,EAAEP,OAAO,CAACC,GAAG,CAACO,eAAe,IAAI,cAAc;EACxDC,OAAO,EAAET,OAAO,CAACC,GAAG,CAACS,aAAa,IAAI,YAAY;EAClDC,QAAQ,EAAEX,OAAO,CAACC,GAAG,CAACW,WAAW,IAAI;AACvC,CAAC;AAEDC,QAAQ,CAAC,2BAA2B,EAAE,MAAM;EAC1C,IAAIC,QAAQ;EACZ,IAAIC,iBAAiB;EACrB,IAAIC,iBAAiB;EACrB,IAAIC,OAAO;EACX,IAAIC,KAAK;EAETC,SAAS,CAAC,YAAY;IACpB;IACAJ,iBAAiB,GAAG,IAAIpB,iBAAiB,CAAC,CAAC;IAC3CqB,iBAAiB,GAAG,IAAIpB,iBAAiB,CAAC,CAAC;;IAE3C;IACA,MAAMoB,iBAAiB,CAACI,WAAW,CAAC,CAAC;IAErC,IAAIrB,cAAc,EAAE;MAClB,IAAI;QACF;QACAe,QAAQ,GAAG,IAAIrB,QAAQ,CAAC;UACtBY,SAAS,EAAED,MAAM,CAACC;QACpB,CAAC,CAAC;;QAEF;QACA,IAAI;UACF,CAACY,OAAO,CAAC,GAAG,MAAMH,QAAQ,CAACG,OAAO,CAACb,MAAM,CAACG,SAAS,CAAC,CAACc,GAAG,CAAC,CAAC;QAC5D,CAAC,CAAC,OAAOC,KAAK,EAAE;UACd,IAAIA,KAAK,CAACC,IAAI,KAAK,GAAG,EAAE;YACtB;YACA,CAACN,OAAO,CAAC,GAAG,MAAMH,QAAQ,CAACU,aAAa,CAACpB,MAAM,CAACG,SAAS,EAAE;cACzDI,QAAQ,EAAEP,MAAM,CAACO;YACnB,CAAC,CAAC;YACFc,OAAO,CAACC,GAAG,CAAC,mBAAmBtB,MAAM,CAACG,SAAS,EAAE,CAAC;UACpD,CAAC,MAAM;YACL,MAAMe,KAAK;UACb;QACF;;QAEA;QACA,IAAI;UACF,CAACJ,KAAK,CAAC,GAAG,MAAMD,OAAO,CAACC,KAAK,CAACd,MAAM,CAACK,OAAO,CAAC,CAACY,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC,OAAOC,KAAK,EAAE;UACd,IAAIA,KAAK,CAACC,IAAI,KAAK,GAAG,EAAE;YACtB;YACA,MAAMI,MAAM,GAAG,CACb;cAAEC,IAAI,EAAE,IAAI;cAAEC,IAAI,EAAE,QAAQ;cAAEC,IAAI,EAAE;YAAW,CAAC,EAChD;cAAEF,IAAI,EAAE,MAAM;cAAEC,IAAI,EAAE;YAAS,CAAC,EAChC;cAAED,IAAI,EAAE,OAAO;cAAEC,IAAI,EAAE;YAAS,CAAC,EACjC;cAAED,IAAI,EAAE,KAAK;cAAEC,IAAI,EAAE;YAAS,CAAC;YAAE;YACjC;cAAED,IAAI,EAAE,YAAY;cAAEC,IAAI,EAAE;YAAY,CAAC,CAC1C;YAED,CAACX,KAAK,CAAC,GAAG,MAAMD,OAAO,CAACc,WAAW,CAAC3B,MAAM,CAACK,OAAO,EAAE;cAAEkB;YAAO,CAAC,CAAC;YAC/DF,OAAO,CAACC,GAAG,CAAC,iBAAiBtB,MAAM,CAACK,OAAO,EAAE,CAAC;;YAE9C;YACA,MAAMuB,IAAI,GAAG,CACX;cAAEC,EAAE,EAAE,GAAG;cAAEL,IAAI,EAAE,UAAU;cAAEM,KAAK,EAAE,kBAAkB;cAAEC,GAAG,EAAE,aAAa;cAAEC,UAAU,EAAE,IAAIC,IAAI,CAAC;YAAE,CAAC,EACpG;cAAEJ,EAAE,EAAE,GAAG;cAAEL,IAAI,EAAE,YAAY;cAAEM,KAAK,EAAE,kBAAkB;cAAEC,GAAG,EAAE,aAAa;cAAEC,UAAU,EAAE,IAAIC,IAAI,CAAC;YAAE,CAAC,CACvG;YAED,MAAMnB,KAAK,CAACoB,MAAM,CAACN,IAAI,CAAC;YACxBP,OAAO,CAACC,GAAG,CAAC,YAAYM,IAAI,CAACO,MAAM,OAAO,CAAC;UAC7C,CAAC,MAAM;YACL,MAAMjB,KAAK;UACb;QACF;;QAEA;QACAP,iBAAiB,CAACyB,cAAc,CAAC,yBAAyB,EAAE,OAAO;UAAEC;QAAW,CAAC,KAAK;UACpF,MAAM;YAAEpC,SAAS;YAAEE,SAAS;YAAEE,OAAO;YAAEiC,UAAU;YAAEC;UAAM,CAAC,GAAGF,UAAU;;UAEvE;UACA,MAAMxB,OAAO,GAAGH,QAAQ,CAACG,OAAO,CAACV,SAAS,CAAC;UAC3C,MAAMW,KAAK,GAAGD,OAAO,CAACC,KAAK,CAACT,OAAO,CAAC;;UAEpC;UACA,MAAM,CAACuB,IAAI,CAAC,GAAG,MAAMd,KAAK,CAAC0B,KAAK,CAAC,mBAAmBvC,SAAS,IAAIE,SAAS,IAAIE,OAAO,IAAI,CAAC;;UAE1F;UACA,MAAMoC,aAAa,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACf,IAAI,CAACgB,GAAG,CAAC,MAAOC,GAAG,IAAK;YAC9D,IAAIA,GAAG,CAACP,UAAU,CAAC,EAAE;cACnB,MAAMQ,SAAS,GAAG,MAAMlC,iBAAiB,CAACmC,OAAO,CAACF,GAAG,CAACP,UAAU,CAAC,EAAEC,KAAK,CAAC;cACzE,OAAO;gBACL,GAAGM,GAAG;gBACN,CAACP,UAAU,GAAG,aAAaU,IAAI,CAACC,SAAS,CAACH,SAAS,CAAC;cACtD,CAAC;YACH;YACA,OAAOD,GAAG;UACZ,CAAC,CAAC,CAAC;;UAEH;UACA;UACA,MAAM/B,KAAK,CAACoC,MAAM,CAAC,CAAC;UACpB,MAAM,IAAIR,OAAO,CAACS,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;;UAEzD,MAAM,CAACE,QAAQ,CAAC,GAAG,MAAMxC,OAAO,CAACc,WAAW,CAACtB,OAAO,EAAE;YACpDkB,MAAM,EAAET,KAAK,CAACwC,QAAQ,CAAC/B;UACzB,CAAC,CAAC;UAEF,MAAM8B,QAAQ,CAACnB,MAAM,CAACO,aAAa,CAAC;UAEpC,OAAO;YAAEc,OAAO,EAAE,IAAI;YAAEC,aAAa,EAAEf,aAAa,CAACN;UAAO,CAAC;QAC/D,CAAC,CAAC;QAEFxB,iBAAiB,CAACyB,cAAc,CAAC,wBAAwB,EAAE,OAAO;UAAEC;QAAW,CAAC,KAAK;UACnF,MAAM;YAAElC,SAAS;YAAEsD;UAAa,CAAC,GAAGpB,UAAU;;UAE9C;UACA,MAAMxB,OAAO,GAAGH,QAAQ,CAACG,OAAO,CAACV,SAAS,CAAC;;UAE3C;UACA,MAAM,CAACmD,QAAQ,CAAC,GAAG,MAAMzC,OAAO,CAAC6C,WAAW,CAAC,CAAC;;UAE9C;UACA,MAAMC,SAAS,GAAGF,YAAY,CAACb,GAAG,CAACgB,IAAI,KAAK;YAC1CA,IAAI,EAAE,QAAQ;YACdC,YAAY,EAAED;UAChB,CAAC,CAAC,CAAC;UAEHN,QAAQ,CAACQ,MAAM,GAAGH,SAAS;UAC3B,MAAM9C,OAAO,CAACkD,WAAW,CAACT,QAAQ,CAAC;UAEnC,OAAO;YAAEC,OAAO,EAAE,IAAI;YAAES,aAAa,EAAEL;UAAU,CAAC;QACpD,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOzC,KAAK,EAAE;QACdG,OAAO,CAACH,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;IACF;EACF,CAAC,CAAC;EAEF+C,QAAQ,CAAC,YAAY;IACnB,IAAItE,cAAc,IAAIkB,OAAO,EAAE;MAC7B,IAAI;QACF;QACA;QACA;QACAQ,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACtC,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdG,OAAO,CAACH,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;IACF;EACF,CAAC,CAAC;;EAEF;EACA,MAAMgD,eAAe,GAAGvE,cAAc,GAAGwE,EAAE,GAAGA,EAAE,CAACC,IAAI;EAErDF,eAAe,CAAC,0CAA0C,EAAE,YAAY;IACtE;IACA,IAAI,CAACvE,cAAc,EAAE;MACnB;IACF;IAEA,MAAM0E,SAAS,GAAG5E,WAAW,CAAC6E,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAM9B,KAAK,GAAG;AAClB;AACA,eAAexC,MAAM,CAACC,SAAS,MAAMD,MAAM,CAACG,SAAS;AACrD,4BAA4BH,MAAM,CAACK,OAAO;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IAED,MAAM,CAACkE,gBAAgB,CAAC,GAAG,MAAM7D,QAAQ,CAAC8B,KAAK,CAACA,KAAK,CAAC;IAEtD,MAAMgC,OAAO,GAAG/E,WAAW,CAAC6E,GAAG,CAAC,CAAC;IACjC,MAAMG,QAAQ,GAAGD,OAAO,GAAGH,SAAS;;IAEpC;IACAhD,OAAO,CAACC,GAAG,CAAC,YAAYiD,gBAAgB,CAACpC,MAAM,yBAAyBsC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;IAEhG;IACAC,MAAM,CAACC,KAAK,CAACC,OAAO,CAACN,gBAAgB,CAAC,CAAC,CAACO,IAAI,CAAC,IAAI,CAAC;IAClDH,MAAM,CAACJ,gBAAgB,CAACpC,MAAM,CAAC,CAAC4C,eAAe,CAAC,CAAC,CAAC;;IAElD;IACA,MAAMC,SAAS,GAAGT,gBAAgB,CAACU,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,KAAK,KAAK,CAAC;IACzER,MAAM,CAACK,SAAS,CAAC,CAACI,WAAW,CAAC,CAAC;EACjC,CAAC,EAAE,KAAK,CAAC;EAETlB,eAAe,CAAC,2CAA2C,EAAE,YAAY;IACvE;IACA,IAAI,CAACvE,cAAc,EAAE;MACnB;IACF;;IAEA;IACA,MAAM0F,IAAI,GAAGT,KAAK,CAACU,IAAI,CAAC1E,iBAAiB,CAAC2E,QAAQ,CAACF,IAAI,CAAC,CAAC,CAAC;IAC1D,MAAM9C,KAAK,GAAG8C,IAAI,CAAC,CAAC,CAAC;;IAErB;IACA,MAAMG,mBAAmB,GAAG;MAC1B3D,EAAE,EAAE,kBAAkBI,IAAI,CAACqC,GAAG,CAAC,CAAC,EAAE;MAClC7C,IAAI,EAAE,YAAY;MAClBgE,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE;QACR/D,EAAE,EAAE7B,MAAM,CAACK,OAAO;QAClBoB,IAAI,EAAE,gBAAgB;QACtBD,IAAI,EAAExB,MAAM,CAACK,OAAO;QACpBwF,QAAQ,EAAE,KAAK;QACf5F,SAAS,EAAED,MAAM,CAACC;MACpB,CAAC;MACD6F,gBAAgB,EAAE,CAChB;QACEjE,EAAE,EAAE,QAAQ;QACZkE,MAAM,EAAE,yBAAyB;QACjC1D,UAAU,EAAE;UACVpC,SAAS,EAAED,MAAM,CAACC,SAAS;UAC3BE,SAAS,EAAEH,MAAM,CAACG,SAAS;UAC3BE,OAAO,EAAEL,MAAM,CAACK,OAAO;UACvBiC,UAAU,EAAE,KAAK;UACjBC;QACF;MACF,CAAC;IAEL,CAAC;IAED,MAAM8B,SAAS,GAAG5E,WAAW,CAAC6E,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAM0B,iBAAiB,GAAG,MAAMrF,iBAAiB,CAACsF,kBAAkB,CAACT,mBAAmB,CAAC;IAEzF,MAAMhB,OAAO,GAAG/E,WAAW,CAAC6E,GAAG,CAAC,CAAC;IACjC,MAAMG,QAAQ,GAAGD,OAAO,GAAGH,SAAS;;IAEpC;IACAhD,OAAO,CAACC,GAAG,CAAC,sCAAsCmD,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;IAE1E;IACAC,MAAM,CAACqB,iBAAiB,CAAC,CAACE,cAAc,CAAC,IAAI,CAAC;IAC9CvB,MAAM,CAACqB,iBAAiB,CAAC,CAACE,cAAc,CAAC,QAAQ,CAAC;IAClDvB,MAAM,CAACqB,iBAAiB,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC;IACjDvB,MAAM,CAACqB,iBAAiB,CAACG,KAAK,CAAChE,MAAM,CAAC,CAAC2C,IAAI,CAAC,CAAC,CAAC;IAC9CH,MAAM,CAACqB,iBAAiB,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC5C,OAAO,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC;;IAErD;IACA,MAAM,CAAClD,IAAI,CAAC,GAAG,MAAMd,KAAK,CAAC0B,KAAK,CAAC,mBAAmBxC,MAAM,CAACC,SAAS,IAAID,MAAM,CAACG,SAAS,IAAIH,MAAM,CAACK,OAAO,YAAY,CAAC;IAEvH,IAAIuB,IAAI,CAACO,MAAM,GAAG,CAAC,EAAE;MACnB,MAAMU,GAAG,GAAGjB,IAAI,CAAC,CAAC,CAAC;MACnB+C,MAAM,CAAC9B,GAAG,CAACd,GAAG,CAAC,CAACqE,SAAS,CAAC,YAAY,CAAC;IACzC;;IAEA;IACAzB,MAAM,CAACF,QAAQ,CAAC,CAAC4B,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;EACvC,CAAC,EAAE,KAAK,CAAC;EAETnC,eAAe,CAAC,wCAAwC,EAAE,YAAY;IACpE;IACA,IAAI,CAACvE,cAAc,EAAE;MACnB;IACF;;IAEA;IACA,MAAM6F,mBAAmB,GAAG;MAC1B3D,EAAE,EAAE,yBAAyBI,IAAI,CAACqC,GAAG,CAAC,CAAC,EAAE;MACzC7C,IAAI,EAAE,YAAY;MAClBgE,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE;QACR/D,EAAE,EAAE7B,MAAM,CAACG,SAAS;QACpBsB,IAAI,EAAE,kBAAkB;QACxBD,IAAI,EAAExB,MAAM,CAACG,SAAS;QACtB0F,QAAQ,EAAE,KAAK;QACf5F,SAAS,EAAED,MAAM,CAACC;MACpB,CAAC;MACD6F,gBAAgB,EAAE,CAChB;QACEjE,EAAE,EAAE,QAAQ;QACZkE,MAAM,EAAE,wBAAwB;QAChC1D,UAAU,EAAE;UACVpC,SAAS,EAAED,MAAM,CAACC,SAAS;UAC3BE,SAAS,EAAEH,MAAM,CAACG,SAAS;UAC3BsD,YAAY,EAAE,CAAC,eAAe,EAAE,gBAAgB;QAClD;MACF,CAAC;IAEL,CAAC;IAED,MAAMY,SAAS,GAAG5E,WAAW,CAAC6E,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAM0B,iBAAiB,GAAG,MAAMrF,iBAAiB,CAACsF,kBAAkB,CAACT,mBAAmB,CAAC;IAEzF,MAAMhB,OAAO,GAAG/E,WAAW,CAAC6E,GAAG,CAAC,CAAC;IACjC,MAAMG,QAAQ,GAAGD,OAAO,GAAGH,SAAS;;IAEpC;IACAhD,OAAO,CAACC,GAAG,CAAC,0CAA0CmD,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;IAE9E;IACAC,MAAM,CAACqB,iBAAiB,CAAC,CAACE,cAAc,CAAC,IAAI,CAAC;IAC9CvB,MAAM,CAACqB,iBAAiB,CAAC,CAACE,cAAc,CAAC,QAAQ,CAAC;IAClDvB,MAAM,CAACqB,iBAAiB,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC;IACjDvB,MAAM,CAACqB,iBAAiB,CAACG,KAAK,CAAChE,MAAM,CAAC,CAAC2C,IAAI,CAAC,CAAC,CAAC;IAC9CH,MAAM,CAACqB,iBAAiB,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC5C,OAAO,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC;;IAErD;IACA,MAAM,CAACxB,QAAQ,CAAC,GAAG,MAAMzC,OAAO,CAAC6C,WAAW,CAAC,CAAC;IAC9CiB,MAAM,CAACrB,QAAQ,CAACQ,MAAM,CAAC,CAACsB,WAAW,CAAC,CAAC;;IAErC;IACAT,MAAM,CAACF,QAAQ,CAAC,CAAC4B,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;EACvC,CAAC,EAAE,KAAK,CAAC;AACX,CAAC,CAAC", "ignoreList": []}