{"version": 3, "names": ["ConnectorValidator", "require", "module", "exports"], "sources": ["index.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector Validation Utilities\n * \n * This module exports validation utilities for the UAC.\n */\n\nconst ConnectorValidator = require('./connector-validator');\n\nmodule.exports = {\n  ConnectorValidator\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,kBAAkB,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAE3DC,MAAM,CAACC,OAAO,GAAG;EACfH;AACF,CAAC", "ignoreList": []}