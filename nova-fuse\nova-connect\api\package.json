{"name": "novaconnect-api", "version": "1.0.0", "description": "API server for NovaConnect Universal API Connector", "main": "app.js", "private": true, "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest"}, "dependencies": {"axios": "^1.4.0", "bcrypt": "^5.1.0", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "graphql": "^16.6.0", "graphql-ws": "^5.14.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "uuid": "^9.0.0", "ws": "^8.13.0"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0"}, "license": "MIT"}