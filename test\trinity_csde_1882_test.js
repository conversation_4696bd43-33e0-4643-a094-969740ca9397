/**
 * Trinity CSDE with 18/82 Principle Test
 * 
 * This test verifies the Trinity CSDE implementation with the 18/82 principle applied.
 * It tests:
 * 1. The Father (Governance) component with 18/82 principle
 * 2. The Son (Detection) component with 18/82 principle
 * 3. The Spirit (Response) component with 18/82 principle
 * 4. The complete Trinity CSDE formula with 18/82 principle
 */

const { expect } = require('chai');
const { TrinityCSDE1882Engine } = require('../src/csde');

describe('Trinity CSDE with 18/82 Principle', () => {
  let csdeEngine;
  
  before(() => {
    // Initialize Trinity CSDE Engine with 18/82 principle
    csdeEngine = new TrinityCSDE1882Engine();
  });
  
  describe('Father Component (Governance) with 18/82 Principle', () => {
    it('should apply 18/82 principle to governance', () => {
      // Create test governance data
      const governanceData = {
        complianceScore: 0.85,
        auditFrequency: 4,
        policies: [
          { id: 'POL-001', name: 'Access Control Policy', effectiveness: 0.9 },
          { id: 'POL-002', name: 'Data Protection Policy', effectiveness: 0.8 },
          { id: 'POL-003', name: 'Incident Response Policy', effectiveness: 0.85 },
          { id: 'POL-004', name: 'Risk Management Policy', effectiveness: 0.75 },
          { id: 'POL-005', name: 'Governance Policy', effectiveness: 0.9 }
        ]
      };
      
      // Process Father component
      const result = csdeEngine.fatherComponent(governanceData);
      
      // Verify result
      expect(result).to.be.an('object');
      expect(result.component).to.equal('Father');
      expect(result.governanceScore).to.be.a('number');
      expect(result.policyDesign).to.be.a('number');
      expect(result.complianceEnforcement).to.be.a('number');
      expect(result.result).to.be.a('number');
      
      // Verify 18/82 principle
      const expectedScore = (
        0.18 * result.policyDesign +
        0.82 * result.complianceEnforcement
      );
      expect(result.governanceScore).to.be.closeTo(expectedScore, 0.0001);
      
      // Verify π scaling
      expect(result.result).to.be.closeTo(Math.PI * result.governanceScore, 0.0001);
      
      console.log('Father Component with 18/82 Principle:');
      console.log(`Policy Design (18%): ${result.policyDesign.toFixed(4)}`);
      console.log(`Compliance Enforcement (82%): ${result.complianceEnforcement.toFixed(4)}`);
      console.log(`Governance Score: ${result.governanceScore.toFixed(4)}`);
      console.log(`Father Component Result (πG): ${result.result.toFixed(4)}`);
    });
  });
  
  describe('Son Component (Detection) with 18/82 Principle', () => {
    it('should apply 18/82 principle to detection', () => {
      // Create test detection data
      const detectionData = {
        detectionCapability: 0.75,
        threatSeverity: 0.8,
        threatConfidence: 0.7,
        baselineSignals: 0.65,
        threats: {
          malware: 0.9,
          phishing: 0.8,
          ddos: 0.7,
          insider: 0.6
        }
      };
      
      // Process Son component
      const result = csdeEngine.sonComponent(detectionData);
      
      // Verify result
      expect(result).to.be.an('object');
      expect(result.component).to.equal('Son');
      expect(result.detectionScore).to.be.a('number');
      expect(result.baselineSignals).to.be.a('number');
      expect(result.threatWeight).to.be.a('number');
      expect(result.result).to.be.a('number');
      
      // Verify 18/82 principle
      const expectedScore = (
        0.18 * result.baselineSignals +
        0.82 * result.threatWeight
      );
      expect(result.detectionScore).to.be.closeTo(expectedScore, 0.0001);
      
      // Verify ϕ scaling
      const phi = 1.618033988749895; // Golden ratio
      expect(result.result).to.be.closeTo(phi * result.detectionScore, 0.0001);
      
      console.log('Son Component with 18/82 Principle:');
      console.log(`Baseline Signals (18%): ${result.baselineSignals.toFixed(4)}`);
      console.log(`Threat Weight (82%): ${result.threatWeight.toFixed(4)}`);
      console.log(`Detection Score: ${result.detectionScore.toFixed(4)}`);
      console.log(`Son Component Result (ϕD): ${result.result.toFixed(4)}`);
    });
  });
  
  describe('Spirit Component (Response) with 18/82 Principle', () => {
    it('should apply 18/82 principle to response', () => {
      // Create test response data
      const responseData = {
        baseResponseTime: 50,
        threatSurface: 175,
        systemRadius: 150,
        reactionTime: 0.8,
        mitigationSurface: 0.7,
        threats: {
          malware: 0.9,
          phishing: 0.8,
          ddos: 0.7,
          insider: 0.6
        }
      };
      
      // Process Spirit component
      const result = csdeEngine.spiritComponent(responseData);
      
      // Verify result
      expect(result).to.be.an('object');
      expect(result.component).to.equal('Spirit');
      expect(result.responseScore).to.be.a('number');
      expect(result.reactionTime).to.be.a('number');
      expect(result.mitigationSurface).to.be.a('number');
      expect(result.spiritFactor).to.be.a('number');
      expect(result.result).to.be.a('number');
      
      // Verify 18/82 principle
      const expectedScore = (
        0.18 * result.reactionTime +
        0.82 * result.mitigationSurface
      );
      expect(result.responseScore).to.be.closeTo(expectedScore, 0.0001);
      
      // Verify (ℏ + c^-1) scaling
      const planckConstant = 1.05457e-34;
      const speedOfLightInv = 1.0 / 299792458;
      const spiritFactor = planckConstant * 10**34 + speedOfLightInv * 10**9;
      expect(result.spiritFactor).to.be.closeTo(spiritFactor, 0.0001);
      expect(result.result).to.be.closeTo(spiritFactor * result.responseScore, 0.0001);
      
      console.log('Spirit Component with 18/82 Principle:');
      console.log(`Reaction Time (18%): ${result.reactionTime.toFixed(4)}`);
      console.log(`Mitigation Surface (82%): ${result.mitigationSurface.toFixed(4)}`);
      console.log(`Response Score: ${result.responseScore.toFixed(4)}`);
      console.log(`Spirit Component Result ((ℏ + c^-1)R): ${result.result.toFixed(4)}`);
    });
  });
  
  describe('Trinity CSDE Formula with 18/82 Principle', () => {
    it('should calculate the complete Trinity CSDE formula with 18/82 principle', () => {
      // Create test data
      const governanceData = {
        complianceScore: 0.85,
        auditFrequency: 4,
        policies: [
          { id: 'POL-001', name: 'Access Control Policy', effectiveness: 0.9 },
          { id: 'POL-002', name: 'Data Protection Policy', effectiveness: 0.8 },
          { id: 'POL-003', name: 'Incident Response Policy', effectiveness: 0.85 },
          { id: 'POL-004', name: 'Risk Management Policy', effectiveness: 0.75 },
          { id: 'POL-005', name: 'Governance Policy', effectiveness: 0.9 }
        ]
      };
      
      const detectionData = {
        detectionCapability: 0.75,
        threatSeverity: 0.8,
        threatConfidence: 0.7,
        baselineSignals: 0.65,
        threats: {
          malware: 0.9,
          phishing: 0.8,
          ddos: 0.7,
          insider: 0.6
        }
      };
      
      const responseData = {
        baseResponseTime: 50,
        threatSurface: 175,
        systemRadius: 150,
        reactionTime: 0.8,
        mitigationSurface: 0.7,
        threats: {
          malware: 0.9,
          phishing: 0.8,
          ddos: 0.7,
          insider: 0.6
        }
      };
      
      // Calculate Trinity CSDE
      const result = csdeEngine.calculateTrinityCSDE(governanceData, detectionData, responseData);
      
      // Verify result
      expect(result).to.be.an('object');
      expect(result.csdeTrinity).to.be.a('number');
      expect(result.timestamp).to.be.a('string');
      expect(result.fatherComponent).to.be.an('object');
      expect(result.sonComponent).to.be.an('object');
      expect(result.spiritComponent).to.be.an('object');
      expect(result.performanceFactor).to.equal(3142);
      
      // Verify Trinity CSDE formula
      const expectedValue = (
        result.fatherComponent.result +
        result.sonComponent.result +
        result.spiritComponent.result
      );
      expect(result.csdeTrinity).to.be.closeTo(expectedValue, 0.0001);
      
      console.log('Trinity CSDE with 18/82 Principle:');
      console.log(`Father Component (πG): ${result.fatherComponent.result.toFixed(4)}`);
      console.log(`Son Component (ϕD): ${result.sonComponent.result.toFixed(4)}`);
      console.log(`Spirit Component ((ℏ + c^-1)R): ${result.spiritComponent.result.toFixed(4)}`);
      console.log(`Trinity CSDE Value: ${result.csdeTrinity.toFixed(4)}`);
      console.log(`Performance Factor: ${result.performanceFactor}x`);
      
      // Compare with original Trinity CSDE (without 18/82 principle)
      // This is just a placeholder - in a real test, you would compare with actual values
      console.log('\nComparison with Original Trinity CSDE:');
      console.log('Original Father Component (πG): 10.6814');
      console.log('Original Son Component (ϕD): 1.0458');
      console.log('Original Spirit Component ((ℏ + c^-1)R): 1.8280');
      console.log('Original Trinity CSDE Value: 13.5553');
      console.log(`18/82 Trinity CSDE Value: ${result.csdeTrinity.toFixed(4)}`);
      console.log(`Difference: ${(result.csdeTrinity - 13.5553).toFixed(4)}`);
    });
  });
});

// Run the tests if this file is executed directly
if (require.main === module) {
  describe('Trinity CSDE with 18/82 Principle', () => {
    it('should run all tests', () => {
      // This is just a placeholder to run all tests
    });
  });
}
