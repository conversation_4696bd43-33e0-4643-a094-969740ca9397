/**
 * Change Request Controller
 * 
 * This controller handles API requests related to change requests.
 */

const ChangeRequestService = require('../services/ChangeRequestService');
const { ValidationError } = require('../utils/errors');

class ChangeRequestController {
  constructor() {
    this.changeRequestService = new ChangeRequestService();
  }

  /**
   * Get all change requests
   */
  async getAllChangeRequests(req, res, next) {
    try {
      const filters = req.query;
      const changeRequests = await this.changeRequestService.getAllChangeRequests(filters);
      res.json(changeRequests);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get change requests for current user
   */
  async getMyChangeRequests(req, res, next) {
    try {
      const filters = req.query;
      const changeRequests = await this.changeRequestService.getChangeRequestsForUser(req.user.id, filters);
      res.json(changeRequests);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get change requests for approval
   */
  async getChangeRequestsForApproval(req, res, next) {
    try {
      const filters = req.query;
      const changeRequests = await this.changeRequestService.getChangeRequestsForApproval(req.user.id, filters);
      res.json(changeRequests);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get change request by ID
   */
  async getChangeRequestById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Change request ID is required');
      }
      
      const changeRequest = await this.changeRequestService.getChangeRequestById(id);
      res.json(changeRequest);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new change request
   */
  async createChangeRequest(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Change request data is required');
      }
      
      const changeRequest = await this.changeRequestService.createChangeRequest(data, req.user.id);
      res.status(201).json(changeRequest);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a change request
   */
  async updateChangeRequest(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Change request ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Change request data is required');
      }
      
      const changeRequest = await this.changeRequestService.updateChangeRequest(id, data, req.user.id);
      res.json(changeRequest);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a change request
   */
  async deleteChangeRequest(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Change request ID is required');
      }
      
      const result = await this.changeRequestService.deleteChangeRequest(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get change request comments
   */
  async getChangeRequestComments(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Change request ID is required');
      }
      
      const comments = await this.changeRequestService.getChangeRequestComments(id);
      res.json(comments);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Add comment to change request
   */
  async addChangeRequestComment(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Change request ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Comment data is required');
      }
      
      const comment = await this.changeRequestService.addChangeRequestComment(id, data, req.user.id);
      res.status(201).json(comment);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update comment
   */
  async updateComment(req, res, next) {
    try {
      const { id, commentId } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Change request ID is required');
      }
      
      if (!commentId) {
        throw new ValidationError('Comment ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Comment data is required');
      }
      
      const comment = await this.changeRequestService.updateComment(commentId, data, req.user.id);
      res.json(comment);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete comment
   */
  async deleteComment(req, res, next) {
    try {
      const { id, commentId } = req.params;
      
      if (!id) {
        throw new ValidationError('Change request ID is required');
      }
      
      if (!commentId) {
        throw new ValidationError('Comment ID is required');
      }
      
      const result = await this.changeRequestService.deleteComment(commentId, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get change request approvals
   */
  async getChangeRequestApprovals(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Change request ID is required');
      }
      
      const approvals = await this.changeRequestService.getChangeRequestApprovals(id);
      res.json(approvals);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Approve change request
   */
  async approveChangeRequest(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body || {};
      
      if (!id) {
        throw new ValidationError('Change request ID is required');
      }
      
      const result = await this.changeRequestService.approveChangeRequest(id, data, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reject change request
   */
  async rejectChangeRequest(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body || {};
      
      if (!id) {
        throw new ValidationError('Change request ID is required');
      }
      
      const result = await this.changeRequestService.rejectChangeRequest(id, data, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Implement change request
   */
  async implementChangeRequest(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body || {};
      
      if (!id) {
        throw new ValidationError('Change request ID is required');
      }
      
      const result = await this.changeRequestService.implementChangeRequest(id, data, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new ChangeRequestController();
