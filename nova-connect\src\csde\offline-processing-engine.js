/**
 * Offline Processing Engine for CSDE
 * 
 * This module implements advanced offline processing capabilities for the CSDE integration.
 * It extends the UUFT Engine with:
 * 1. Batch processing for large datasets
 * 2. Persistent storage for offline results
 * 3. Synchronization mechanisms for offline/online data
 * 4. Prioritization algorithms for critical data
 * 5. Compression for efficient storage
 */

const { performance } = require('perf_hooks');
const fs = require('fs').promises;
const path = require('path');
const zlib = require('zlib');
const util = require('util');
const crypto = require('crypto');
const UUFTEngine = require('./uuft-engine');

// Promisify zlib functions
const gzipAsync = util.promisify(zlib.gzip);
const gunzipAsync = util.promisify(zlib.gunzip);

class OfflineProcessingEngine extends UUFTEngine {
  /**
   * Create a new Offline Processing Engine
   * @param {Object} options - Engine options
   */
  constructor(options = {}) {
    super(options);
    
    this.options = {
      ...this.options,
      storageDir: options.storageDir || path.join(process.cwd(), 'data', 'offline-processing'),
      maxBatchSize: options.maxBatchSize || 100,
      compressionLevel: options.compressionLevel || 6,
      retentionDays: options.retentionDays || 30,
      syncInterval: options.syncInterval || 3600000, // 1 hour in milliseconds
      priorityThreshold: options.priorityThreshold || 0.8,
      ...options
    };
    
    // Initialize storage
    this.initializeStorage();
    
    // Initialize batch processing queue
    this.batchQueue = [];
    this.processingBatch = false;
    
    // Initialize metrics
    this.metrics = {
      totalProcessed: 0,
      totalBatches: 0,
      totalStorageSize: 0,
      averageProcessingTime: 0,
      totalProcessingTime: 0,
      lastSyncTime: null,
      compressionRatio: 0
    };
    
    // Start sync interval if enabled
    if (this.options.enableAutoSync !== false) {
      this.syncInterval = setInterval(() => {
        this.syncOfflineResults().catch(err => {
          this.logger.error('Error syncing offline results', { error: err.message });
        });
      }, this.options.syncInterval);
    }
    
    this.logger.info('Offline Processing Engine initialized', {
      storageDir: this.options.storageDir,
      maxBatchSize: this.options.maxBatchSize,
      compressionLevel: this.options.compressionLevel,
      retentionDays: this.options.retentionDays
    });
  }
  
  /**
   * Initialize storage directory
   */
  async initializeStorage() {
    try {
      // Create storage directory if it doesn't exist
      await fs.mkdir(this.options.storageDir, { recursive: true });
      
      // Create subdirectories
      await fs.mkdir(path.join(this.options.storageDir, 'batches'), { recursive: true });
      await fs.mkdir(path.join(this.options.storageDir, 'results'), { recursive: true });
      await fs.mkdir(path.join(this.options.storageDir, 'sync'), { recursive: true });
      
      this.logger.debug('Storage directories created', {
        storageDir: this.options.storageDir
      });
    } catch (error) {
      this.logger.error('Error initializing storage', {
        error: error.message,
        storageDir: this.options.storageDir
      });
      
      throw error;
    }
  }
  
  /**
   * Process data offline
   * @param {Object} data - Data to process
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - Processing result
   */
  async processOffline(data, options = {}) {
    const startTime = performance.now();
    
    try {
      this.logger.debug('Processing data offline', {
        dataSize: JSON.stringify(data).length,
        options
      });
      
      // Generate unique ID for this processing request
      const processingId = options.processingId || crypto.randomUUID();
      
      // Determine if this is high priority
      const isPriority = options.priority === 'high' || 
                        (options.priorityScore && options.priorityScore >= this.options.priorityThreshold);
      
      // If this is a priority request, process immediately
      if (isPriority) {
        this.logger.debug('Processing high priority request immediately', {
          processingId
        });
        
        // Process using UUFT Engine
        const result = await this.processWithUUFT(data);
        
        // Store result
        await this.storeResult(processingId, result);
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        // Update metrics
        this.updateMetrics(duration, 1, JSON.stringify(result).length);
        
        return {
          processingId,
          status: 'completed',
          result,
          processingTime: duration
        };
      }
      
      // Add to batch queue
      this.batchQueue.push({
        processingId,
        data,
        options,
        timestamp: Date.now()
      });
      
      this.logger.debug('Added to batch queue', {
        processingId,
        queueLength: this.batchQueue.length
      });
      
      // Process batch if queue is full
      if (this.batchQueue.length >= this.options.maxBatchSize) {
        this.processBatch().catch(err => {
          this.logger.error('Error processing batch', { error: err.message });
        });
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      return {
        processingId,
        status: 'queued',
        position: this.batchQueue.length,
        estimatedCompletionTime: this.estimateCompletionTime(),
        queuedAt: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error processing data offline', {
        error: error.message,
        stack: error.stack
      });
      
      throw error;
    }
  }
  
  /**
   * Process data with UUFT Engine
   * @param {Object} data - Data to process
   * @returns {Promise<Object>} - Processing result
   */
  async processWithUUFT(data) {
    // Use the UUFT Engine's calculate method
    return super.calculate(data);
  }
  
  /**
   * Process a batch of data
   * @returns {Promise<void>}
   */
  async processBatch() {
    if (this.processingBatch || this.batchQueue.length === 0) {
      return;
    }
    
    this.processingBatch = true;
    const startTime = performance.now();
    
    try {
      // Get batch from queue
      const batchSize = Math.min(this.batchQueue.length, this.options.maxBatchSize);
      const batch = this.batchQueue.splice(0, batchSize);
      
      this.logger.debug('Processing batch', {
        batchSize,
        remainingInQueue: this.batchQueue.length
      });
      
      // Generate batch ID
      const batchId = `batch-${Date.now()}-${crypto.randomUUID()}`;
      
      // Process each item in batch
      const results = [];
      
      for (const item of batch) {
        try {
          // Process with UUFT Engine
          const result = await this.processWithUUFT(item.data);
          
          // Store individual result
          await this.storeResult(item.processingId, result);
          
          results.push({
            processingId: item.processingId,
            status: 'completed',
            result
          });
        } catch (error) {
          this.logger.error('Error processing batch item', {
            processingId: item.processingId,
            error: error.message
          });
          
          results.push({
            processingId: item.processingId,
            status: 'error',
            error: error.message
          });
        }
      }
      
      // Store batch results
      await this.storeBatch(batchId, results);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Update metrics
      this.updateMetrics(duration, batch.length, JSON.stringify(results).length);
      
      this.logger.debug('Batch processing complete', {
        batchId,
        processedItems: batch.length,
        duration: `${duration.toFixed(2)}ms`
      });
    } catch (error) {
      this.logger.error('Error processing batch', {
        error: error.message,
        stack: error.stack
      });
    } finally {
      this.processingBatch = false;
      
      // Process next batch if queue is not empty
      if (this.batchQueue.length >= this.options.maxBatchSize) {
        setImmediate(() => {
          this.processBatch().catch(err => {
            this.logger.error('Error processing next batch', { error: err.message });
          });
        });
      }
    }
  }
  
  /**
   * Store result
   * @param {string} processingId - Processing ID
   * @param {Object} result - Processing result
   * @returns {Promise<void>}
   */
  async storeResult(processingId, result) {
    try {
      // Compress result
      const resultString = JSON.stringify(result);
      const compressedResult = await gzipAsync(resultString, {
        level: this.options.compressionLevel
      });
      
      // Calculate compression ratio
      const compressionRatio = resultString.length / compressedResult.length;
      
      // Store compressed result
      const resultPath = path.join(this.options.storageDir, 'results', `${processingId}.json.gz`);
      await fs.writeFile(resultPath, compressedResult);
      
      this.logger.debug('Result stored', {
        processingId,
        size: compressedResult.length,
        compressionRatio
      });
      
      // Update metrics
      this.metrics.compressionRatio = (this.metrics.compressionRatio + compressionRatio) / 2;
    } catch (error) {
      this.logger.error('Error storing result', {
        processingId,
        error: error.message
      });
      
      throw error;
    }
  }
  
  /**
   * Store batch
   * @param {string} batchId - Batch ID
   * @param {Array} results - Batch results
   * @returns {Promise<void>}
   */
  async storeBatch(batchId, results) {
    try {
      // Compress batch
      const batchString = JSON.stringify(results);
      const compressedBatch = await gzipAsync(batchString, {
        level: this.options.compressionLevel
      });
      
      // Store compressed batch
      const batchPath = path.join(this.options.storageDir, 'batches', `${batchId}.json.gz`);
      await fs.writeFile(batchPath, compressedBatch);
      
      this.logger.debug('Batch stored', {
        batchId,
        size: compressedBatch.length,
        items: results.length
      });
    } catch (error) {
      this.logger.error('Error storing batch', {
        batchId,
        error: error.message
      });
      
      throw error;
    }
  }
  
  /**
   * Get result by processing ID
   * @param {string} processingId - Processing ID
   * @returns {Promise<Object>} - Processing result
   */
  async getResult(processingId) {
    try {
      // Check if result exists
      const resultPath = path.join(this.options.storageDir, 'results', `${processingId}.json.gz`);
      
      try {
        await fs.access(resultPath);
      } catch (error) {
        // Check if it's in the queue
        const queueItem = this.batchQueue.find(item => item.processingId === processingId);
        
        if (queueItem) {
          return {
            processingId,
            status: 'queued',
            position: this.batchQueue.indexOf(queueItem) + 1,
            estimatedCompletionTime: this.estimateCompletionTime(),
            queuedAt: new Date(queueItem.timestamp).toISOString()
          };
        }
        
        throw new Error(`Result not found for processing ID: ${processingId}`);
      }
      
      // Read and decompress result
      const compressedResult = await fs.readFile(resultPath);
      const resultString = await gunzipAsync(compressedResult);
      const result = JSON.parse(resultString);
      
      return {
        processingId,
        status: 'completed',
        result,
        retrievedAt: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error getting result', {
        processingId,
        error: error.message
      });
      
      throw error;
    }
  }
  
  /**
   * Sync offline results with online system
   * @returns {Promise<Object>} - Sync results
   */
  async syncOfflineResults() {
    const startTime = performance.now();
    
    try {
      this.logger.debug('Syncing offline results');
      
      // Get all result files
      const resultsDir = path.join(this.options.storageDir, 'results');
      const resultFiles = await fs.readdir(resultsDir);
      
      // Filter for unsynced results
      const syncDir = path.join(this.options.storageDir, 'sync');
      const syncedFiles = await fs.readdir(syncDir);
      
      const unsyncedFiles = resultFiles.filter(file => !syncedFiles.includes(file));
      
      this.logger.debug('Found unsynced results', {
        total: unsyncedFiles.length
      });
      
      // Sync each result
      const syncResults = [];
      
      for (const file of unsyncedFiles) {
        try {
          // Read and decompress result
          const resultPath = path.join(resultsDir, file);
          const compressedResult = await fs.readFile(resultPath);
          const resultString = await gunzipAsync(compressedResult);
          const result = JSON.parse(resultString);
          
          // TODO: Implement actual sync with online system
          // For now, just mark as synced
          
          // Mark as synced
          const syncPath = path.join(syncDir, file);
          await fs.writeFile(syncPath, '');
          
          syncResults.push({
            file,
            status: 'synced'
          });
        } catch (error) {
          this.logger.error('Error syncing result', {
            file,
            error: error.message
          });
          
          syncResults.push({
            file,
            status: 'error',
            error: error.message
          });
        }
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Update metrics
      this.metrics.lastSyncTime = new Date().toISOString();
      
      this.logger.debug('Sync complete', {
        synced: syncResults.filter(r => r.status === 'synced').length,
        failed: syncResults.filter(r => r.status === 'error').length,
        duration: `${duration.toFixed(2)}ms`
      });
      
      return {
        synced: syncResults.filter(r => r.status === 'synced').length,
        failed: syncResults.filter(r => r.status === 'error').length,
        results: syncResults,
        duration,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error syncing offline results', {
        error: error.message,
        stack: error.stack
      });
      
      throw error;
    }
  }
  
  /**
   * Clean up old results
   * @returns {Promise<Object>} - Cleanup results
   */
  async cleanupOldResults() {
    try {
      this.logger.debug('Cleaning up old results');
      
      // Calculate cutoff date
      const cutoffDate = Date.now() - (this.options.retentionDays * 24 * 60 * 60 * 1000);
      
      // Get all batch files
      const batchesDir = path.join(this.options.storageDir, 'batches');
      const batchFiles = await fs.readdir(batchesDir);
      
      // Filter for old batches
      const oldBatchFiles = batchFiles.filter(file => {
        const match = file.match(/batch-(\d+)-/);
        if (match) {
          const timestamp = parseInt(match[1], 10);
          return timestamp < cutoffDate;
        }
        return false;
      });
      
      // Delete old batches
      for (const file of oldBatchFiles) {
        await fs.unlink(path.join(batchesDir, file));
      }
      
      // Get all result files
      const resultsDir = path.join(this.options.storageDir, 'results');
      const resultFiles = await fs.readdir(resultsDir);
      
      // Get all sync files
      const syncDir = path.join(this.options.storageDir, 'sync');
      const syncFiles = await fs.readdir(syncDir);
      
      // Filter for old synced results
      const oldSyncedResults = resultFiles.filter(file => {
        // Only delete if it's been synced
        if (!syncFiles.includes(file)) {
          return false;
        }
        
        // Check file stats
        try {
          const stats = fs.statSync(path.join(resultsDir, file));
          return stats.mtime.getTime() < cutoffDate;
        } catch (error) {
          return false;
        }
      });
      
      // Delete old results and sync markers
      for (const file of oldSyncedResults) {
        await fs.unlink(path.join(resultsDir, file));
        await fs.unlink(path.join(syncDir, file));
      }
      
      return {
        deletedBatches: oldBatchFiles.length,
        deletedResults: oldSyncedResults.length,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error cleaning up old results', {
        error: error.message,
        stack: error.stack
      });
      
      throw error;
    }
  }
  
  /**
   * Estimate completion time for queued items
   * @returns {string} - Estimated completion time
   */
  estimateCompletionTime() {
    if (this.metrics.averageProcessingTime === 0 || this.batchQueue.length === 0) {
      return new Date(Date.now() + 60000).toISOString(); // Default to 1 minute
    }
    
    // Calculate estimated time based on queue length and average processing time
    const estimatedMs = (this.batchQueue.length / this.options.maxBatchSize) * 
                        this.metrics.averageProcessingTime;
    
    return new Date(Date.now() + estimatedMs).toISOString();
  }
  
  /**
   * Update metrics
   * @param {number} duration - Processing duration
   * @param {number} itemsProcessed - Number of items processed
   * @param {number} resultSize - Size of results in bytes
   */
  updateMetrics(duration, itemsProcessed, resultSize) {
    this.metrics.totalProcessed += itemsProcessed;
    this.metrics.totalBatches += 1;
    this.metrics.totalStorageSize += resultSize;
    this.metrics.totalProcessingTime += duration;
    this.metrics.averageProcessingTime = this.metrics.totalProcessingTime / this.metrics.totalBatches;
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Shutdown the engine
   */
  shutdown() {
    // Clear sync interval
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    // Process any remaining items in the queue
    if (this.batchQueue.length > 0) {
      this.processBatch().catch(err => {
        this.logger.error('Error processing final batch during shutdown', { error: err.message });
      });
    }
    
    this.logger.info('Offline Processing Engine shutdown');
  }
}

module.exports = OfflineProcessingEngine;
