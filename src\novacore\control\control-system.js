/**
 * control-system.js
 * 
 * This file implements the real-time control system for NovaCore.
 * The control system manages the execution of control loops, monitors system state,
 * and provides real-time feedback and control capabilities.
 */

const EventEmitter = require('events');
const { performance } = require('perf_hooks');

/**
 * Control loop status enum
 * @enum {string}
 */
const ControlLoopStatus = {
  IDLE: 'IDLE',
  RUNNING: 'RUNNING',
  PAUSED: 'PAUSED',
  ERROR: 'ERROR',
  TERMINATED: 'TERMINATED'
};

/**
 * Control loop priority enum
 * @enum {number}
 */
const ControlLoopPriority = {
  CRITICAL: 0,
  HIGH: 1,
  NORMAL: 2,
  LOW: 3,
  BACKGROUND: 4
};

/**
 * ControlLoop class representing a single control loop in the system
 */
class ControlLoop {
  /**
   * Create a new control loop
   * @param {Object} options - Control loop options
   */
  constructor(options = {}) {
    this.id = options.id || `control-loop-${Date.now()}`;
    this.name = options.name || this.id;
    this.description = options.description || '';
    this.priority = options.priority !== undefined ? options.priority : ControlLoopPriority.NORMAL;
    this.interval = options.interval || 1000; // Default: 1 second
    this.callback = options.callback || (() => {});
    this.status = ControlLoopStatus.IDLE;
    this.lastRunTime = null;
    this.lastRunDuration = null;
    this.errorCount = 0;
    this.successCount = 0;
    this.timerId = null;
    this.metadata = options.metadata || {};
    this.maxErrorCount = options.maxErrorCount || 10;
    this.errorHandler = options.errorHandler || null;
    this.dependencies = options.dependencies || [];
  }

  /**
   * Start the control loop
   * @returns {ControlLoop} - The control loop instance
   */
  start() {
    if (this.status === ControlLoopStatus.RUNNING) {
      return this;
    }

    this.status = ControlLoopStatus.RUNNING;
    this._scheduleNextRun();
    return this;
  }

  /**
   * Stop the control loop
   * @returns {ControlLoop} - The control loop instance
   */
  stop() {
    if (this.timerId) {
      clearTimeout(this.timerId);
      this.timerId = null;
    }
    this.status = ControlLoopStatus.TERMINATED;
    return this;
  }

  /**
   * Pause the control loop
   * @returns {ControlLoop} - The control loop instance
   */
  pause() {
    if (this.timerId) {
      clearTimeout(this.timerId);
      this.timerId = null;
    }
    this.status = ControlLoopStatus.PAUSED;
    return this;
  }

  /**
   * Resume the control loop
   * @returns {ControlLoop} - The control loop instance
   */
  resume() {
    if (this.status !== ControlLoopStatus.PAUSED) {
      return this;
    }
    this.status = ControlLoopStatus.RUNNING;
    this._scheduleNextRun();
    return this;
  }

  /**
   * Run the control loop once immediately
   * @returns {Promise<any>} - A promise that resolves to the result of the callback
   */
  async runOnce() {
    const startTime = performance.now();
    this.lastRunTime = new Date();
    
    try {
      const result = await this.callback(this);
      this.successCount++;
      this.lastRunDuration = performance.now() - startTime;
      return result;
    } catch (error) {
      this.errorCount++;
      this.lastRunDuration = performance.now() - startTime;
      
      if (this.errorHandler) {
        this.errorHandler(error, this);
      }
      
      if (this.errorCount >= this.maxErrorCount) {
        this.status = ControlLoopStatus.ERROR;
        if (this.timerId) {
          clearTimeout(this.timerId);
          this.timerId = null;
        }
      }
      
      throw error;
    }
  }

  /**
   * Schedule the next run of the control loop
   * @private
   */
  _scheduleNextRun() {
    if (this.status !== ControlLoopStatus.RUNNING) {
      return;
    }
    
    this.timerId = setTimeout(async () => {
      try {
        await this.runOnce();
      } catch (error) {
        // Error already handled in runOnce
      }
      
      if (this.status === ControlLoopStatus.RUNNING) {
        this._scheduleNextRun();
      }
    }, this.interval);
  }

  /**
   * Get the control loop state
   * @returns {Object} - The current state of the control loop
   */
  getState() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      priority: this.priority,
      interval: this.interval,
      status: this.status,
      lastRunTime: this.lastRunTime,
      lastRunDuration: this.lastRunDuration,
      errorCount: this.errorCount,
      successCount: this.successCount,
      metadata: this.metadata,
      dependencies: this.dependencies
    };
  }
}

/**
 * ControlSystem class for managing multiple control loops
 * @extends EventEmitter
 */
class ControlSystem extends EventEmitter {
  /**
   * Create a new control system
   * @param {Object} options - Control system options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      maxConcurrentLoops: options.maxConcurrentLoops || 10,
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      defaultErrorHandler: options.defaultErrorHandler || this._defaultErrorHandler.bind(this),
      ...options
    };
    
    this.controlLoops = new Map();
    this.activeLoops = 0;
    this.status = 'IDLE';
    this.startTime = null;
    
    this.log('ControlSystem initialized with options:', this.options);
  }
  
  /**
   * Log a message if logging is enabled
   * @param {...any} args - Arguments to log
   */
  log(...args) {
    if (this.options.enableLogging) {
      console.log(`[ControlSystem ${new Date().toISOString()}]`, ...args);
    }
  }
  
  /**
   * Default error handler for control loops
   * @param {Error} error - The error that occurred
   * @param {ControlLoop} controlLoop - The control loop that had the error
   * @private
   */
  _defaultErrorHandler(error, controlLoop) {
    this.log(`Error in control loop ${controlLoop.id}:`, error);
    this.emit('error', { error, controlLoop: controlLoop.getState() });
  }
  
  /**
   * Register a new control loop
   * @param {Object|ControlLoop} options - Control loop options or a ControlLoop instance
   * @returns {ControlLoop} - The registered control loop
   */
  registerControlLoop(options) {
    const controlLoop = options instanceof ControlLoop 
      ? options 
      : new ControlLoop({
          ...options,
          errorHandler: options.errorHandler || this.options.defaultErrorHandler
        });
    
    if (this.controlLoops.has(controlLoop.id)) {
      throw new Error(`Control loop with ID ${controlLoop.id} already exists`);
    }
    
    this.controlLoops.set(controlLoop.id, controlLoop);
    this.log(`Registered control loop: ${controlLoop.id}`);
    this.emit('controlLoopRegistered', { controlLoop: controlLoop.getState() });
    
    return controlLoop;
  }
  
  /**
   * Unregister a control loop
   * @param {string} id - The ID of the control loop to unregister
   * @returns {boolean} - Whether the control loop was successfully unregistered
   */
  unregisterControlLoop(id) {
    const controlLoop = this.controlLoops.get(id);
    
    if (!controlLoop) {
      return false;
    }
    
    controlLoop.stop();
    this.controlLoops.delete(id);
    this.log(`Unregistered control loop: ${id}`);
    this.emit('controlLoopUnregistered', { controlLoopId: id });
    
    return true;
  }
  
  /**
   * Start a control loop
   * @param {string} id - The ID of the control loop to start
   * @returns {ControlLoop|null} - The started control loop, or null if not found
   */
  startControlLoop(id) {
    const controlLoop = this.controlLoops.get(id);
    
    if (!controlLoop) {
      return null;
    }
    
    controlLoop.start();
    this.activeLoops++;
    this.log(`Started control loop: ${id}`);
    this.emit('controlLoopStarted', { controlLoop: controlLoop.getState() });
    
    return controlLoop;
  }
  
  /**
   * Stop a control loop
   * @param {string} id - The ID of the control loop to stop
   * @returns {ControlLoop|null} - The stopped control loop, or null if not found
   */
  stopControlLoop(id) {
    const controlLoop = this.controlLoops.get(id);
    
    if (!controlLoop) {
      return null;
    }
    
    controlLoop.stop();
    if (controlLoop.status === ControlLoopStatus.RUNNING) {
      this.activeLoops--;
    }
    this.log(`Stopped control loop: ${id}`);
    this.emit('controlLoopStopped', { controlLoop: controlLoop.getState() });
    
    return controlLoop;
  }
  
  /**
   * Start all control loops
   * @param {Object} options - Options for starting control loops
   * @returns {ControlSystem} - The control system instance
   */
  startAllControlLoops(options = {}) {
    const priorityOrder = options.priorityOrder || false;
    
    if (priorityOrder) {
      // Start control loops in priority order
      const sortedLoops = Array.from(this.controlLoops.values())
        .sort((a, b) => a.priority - b.priority);
      
      for (const loop of sortedLoops) {
        this.startControlLoop(loop.id);
      }
    } else {
      // Start all control loops without priority ordering
      for (const id of this.controlLoops.keys()) {
        this.startControlLoop(id);
      }
    }
    
    this.status = 'RUNNING';
    this.startTime = new Date();
    this.log('Started all control loops');
    this.emit('allControlLoopsStarted');
    
    return this;
  }
  
  /**
   * Stop all control loops
   * @returns {ControlSystem} - The control system instance
   */
  stopAllControlLoops() {
    for (const id of this.controlLoops.keys()) {
      this.stopControlLoop(id);
    }
    
    this.status = 'STOPPED';
    this.activeLoops = 0;
    this.log('Stopped all control loops');
    this.emit('allControlLoopsStopped');
    
    return this;
  }
  
  /**
   * Get a control loop by ID
   * @param {string} id - The ID of the control loop to get
   * @returns {ControlLoop|null} - The control loop, or null if not found
   */
  getControlLoop(id) {
    return this.controlLoops.get(id) || null;
  }
  
  /**
   * Get the state of all control loops
   * @returns {Array<Object>} - An array of control loop states
   */
  getAllControlLoopStates() {
    return Array.from(this.controlLoops.values()).map(loop => loop.getState());
  }
  
  /**
   * Get the system state
   * @returns {Object} - The current state of the control system
   */
  getSystemState() {
    return {
      status: this.status,
      activeLoops: this.activeLoops,
      totalLoops: this.controlLoops.size,
      startTime: this.startTime,
      uptime: this.startTime ? (new Date() - this.startTime) : 0,
      controlLoops: this.getAllControlLoopStates()
    };
  }
}

module.exports = {
  ControlSystem,
  ControlLoop,
  ControlLoopStatus,
  ControlLoopPriority
};
