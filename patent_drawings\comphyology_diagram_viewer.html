<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comphyology Diagram Viewer</title>
    <!-- Load Mermaid from CDN with CORS -->
    <script type="module" crossorigin="anonymous">
        // Import Mermaid from CDN with CORS support
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        
        // Configuration
        const config = {
            diagramDir: '.',  // Current directory
            filePattern: /\.mmd$/i,
            defaultTheme: 'default',
            themes: ['default', 'dark', 'neutral', 'forest']
        };
        
        // State
        let currentDiagram = null;
        
        // DOM Elements
        let diagramSelect, diagramContainer, statusEl, themeSelect;
        
        // Initialize the application
        async function init() {
            // Get DOM elements
            diagramSelect = document.getElementById('diagramSelector');
            diagramContainer = document.getElementById('diagram');
            statusEl = document.getElementById('status');
            themeSelect = document.getElementById('themeSelector');
            
            // Set up theme selector
            setupThemeSelector();
            
            // Load available diagrams
            await loadAvailableDiagrams();
            
            // Set up event listeners
            diagramSelect.addEventListener('change', loadSelectedDiagram);
            themeSelect.addEventListener('change', updateTheme);
            document.getElementById('downloadSvg').addEventListener('click', downloadCurrentDiagram);
            
            // Initialize Mermaid
            await mermaid.initialize({
                startOnLoad: false,
                theme: config.defaultTheme,
                securityLevel: 'loose',
                fontFamily: '"Arial", sans-serif',
                themeCSS: `
                    .node rect, .node circle, .node polygon {
                        stroke: #333;
                        stroke-width: 1.5px;
                    }
                    .label {
                        font-family: "Arial", sans-serif;
                    }
                `
            });
            
            // Load the first diagram by default if available
            if (diagramSelect.options.length > 1) {
                diagramSelect.selectedIndex = 1;
                loadSelectedDiagram();
            }
        }
        
        // Set up theme selector dropdown
        function setupThemeSelector() {
            themeSelect.innerHTML = config.themes
                .map(theme => `<option value="${theme}">${theme.charAt(0).toUpperCase() + theme.slice(1)}</option>`)
                .join('');
            themeSelect.value = config.defaultTheme;
        }
        
        // Update Mermaid theme
        function updateTheme() {
            const theme = themeSelect.value;
            mermaid.initialize({
                ...mermaid.mermaidAPI.getConfig(),
                theme: theme
            });
            
            // Re-render current diagram with new theme
            if (currentDiagram) {
                renderDiagram(currentDiagram.content);
            }
        }
        
        // Load available diagrams from the mermaid_diagrams directory
        async function loadAvailableDiagrams() {
            try {
                setStatus('Loading diagrams...', 'info');
                
                // List of available diagram files with their display names
                const diagramFiles = [
                    { file: 'alignment_architecture.mmd', name: 'Alignment Architecture' },
                    { file: 'consciousness_threshold.mmd', name: 'Consciousness Threshold' },
                    { file: 'cross_module_data_processing_pipeline.mmd', name: 'Cross Module Data Pipeline' },
                    { file: 'dark_field_classification.mmd', name: 'Dark Field Classification' },
                    { file: 'finite_universe_paradigm_visualization.mmd', name: 'Finite Universe Visualization' },
                    { file: 'finite_universe_principle.mmd', name: 'Finite Universe Principle' },
                    { file: 'healthcare_implementation.mmd', name: 'Healthcare Implementation' },
                    { file: 'nova_components.mmd', name: 'Nova Components' },
                    { file: 'principle_18_82.mmd', name: 'Principle 18/82' },
                    { file: 'protein_folding.mmd', name: 'Protein Folding' },
                    { file: 'three_body_problem_reframing.mmd', name: 'Three Body Problem' },
                    { file: 'uuft_core_architecture.mmd', name: 'UUFT Core Architecture' }
                ];
                
                // Clear and populate the dropdown
                diagramSelect.innerHTML = '<option value="">-- Select a Diagram --</option>';
                
                // Add all diagrams to the dropdown
                diagramFiles.forEach(diagram => {
                    const option = document.createElement('option');
                    option.value = diagram.file;
                    option.textContent = diagram.name;
                    diagramSelect.appendChild(option);
                });
                
                setStatus(`Found ${diagramFiles.length} diagrams`, 'success');
                
            } catch (error) {
                console.error('Error loading diagrams:', error);
                
                // Show helpful error message
                const errorMessage = `Error: ${error.message}`;
                setStatus(errorMessage, 'error');
                
                // Add a button to try loading diagrams again
                const retryButton = document.createElement('button');
                retryButton.textContent = 'Retry Loading Diagrams';
                retryButton.onclick = loadAvailableDiagrams;
                statusEl.appendChild(document.createElement('br'));
                statusEl.appendChild(retryButton);
            }
        }
        
        // Load the selected diagram
        async function loadSelectedDiagram() {
            const selectedFile = diagramSelect.value;
            if (!selectedFile) return;
            
            try {
                setStatus(`Loading diagram...`, 'info');
                
                // Clear previous diagram
                diagramContainer.innerHTML = '<div class="loading">Loading diagram...</div>';
                
                // Try to load the file content
                const response = await fetch(selectedFile);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const content = await response.text();
                
                if (!content.trim()) {
                    throw new Error('Diagram file is empty');
                }
                
                currentDiagram = { 
                    name: selectedFile.split('/').pop().replace(/\.mmd$/i, '').replace(/_/g, ' '), 
                    content 
                };
                
                await renderDiagram(content);
                setStatus(`Rendered: ${currentDiagram.name}`, 'success');
                
            } catch (error) {
                console.error('Error loading diagram:', error);
                
                // Show user-friendly error message
                let errorMessage = `
                    <h3>Error Loading Diagram</h3>
                    <p>${error.message || 'Unknown error occurred'}</p>
                `;
                
                if (error.message.includes('Failed to fetch') || 
                    error.message.includes('NetworkError') ||
                    error.message.includes('CORS') ||
                    error.message.includes('404')) {
                    errorMessage = `
                        <h3>Error Loading Diagram</h3>
                        <p>Could not load the diagram file. This might be because:</p>
                        <ul>
                            <li>The file path is incorrect</li>
                            <li>You're not using a local server</li>
                            <li>The file doesn't exist at the specified location</li>
                        </ul>
                        <p>To view diagrams, please:</p>
                        <ol>
                            <li>Open a terminal in the patent_drawings directory</li>
                            <li>Run: <code>python -m http.server 8000</code></li>
                            <li>Open: <a href="http://localhost:8000/comphyology_diagram_viewer.html" target="_blank">http://localhost:8000/comphyology_diagram_viewer.html</a></li>
                        </ol>
                        <p>Current file path: <code>${selectedFile}</code></p>
                    `;
                }
                
                diagramContainer.innerHTML = `
                    <div class="error">
                        ${errorMessage}
                        <p><button onclick="loadSelectedDiagram()">Try Again</button></p>
                    </div>
                `;
                
                setStatus('Error loading diagram', 'error');
            }
        }
        
        // Render a Mermaid diagram
        async function renderDiagram(code) {
            try {
                // Clear the container
                diagramContainer.innerHTML = '<div class="mermaid-loading">Rendering diagram...</div>';
                
                // Use requestAnimationFrame to ensure DOM is updated before rendering
                await new Promise(resolve => requestAnimationFrame(resolve));
                
                // Render the diagram
                const { svg } = await mermaid.render('mermaid-svg', code);
                diagramContainer.innerHTML = svg;
                
                // Make SVG responsive
                const svgElement = diagramContainer.querySelector('svg');
                if (svgElement) {
                    svgElement.style.maxWidth = '100%';
                    svgElement.style.height = 'auto';
                }
                
            } catch (error) {
                console.error('Error rendering diagram:', error);
                diagramContainer.innerHTML = `
                    <div class="error">
                        <h3>Error Rendering Diagram</h3>
                        <p>${error.message}</p>
                        <pre>${error.str || ''}</pre>
                    </div>
                `;
                throw error;
            }
        }
        
        // Download the current diagram as SVG
        function downloadCurrentDiagram() {
            if (!currentDiagram) {
                setStatus('No diagram loaded', 'warning');
                return;
            }
            
            const svgElement = diagramContainer.querySelector('svg');
            if (!svgElement) {
                setStatus('No SVG element found', 'error');
                return;
            }
            
            try {
                // Serialize SVG
                const serializer = new XMLSerializer();
                let svgString = serializer.serializeToString(svgElement);
                
                // Add XML declaration
                const svgBlob = new Blob([
                    '<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n',
                    '<!-- Created with Comphyology Diagram Viewer -->\n',
                    svgString
                ], { type: 'image/svg+xml;charset=utf-8' });
                
                // Create download link
                const url = URL.createObjectURL(svgBlob);
                const downloadLink = document.createElement('a');
                downloadLink.href = url;
                downloadLink.download = currentDiagram.name.replace(/\.mmd$/i, '.svg');
                
                // Trigger download
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                URL.revokeObjectURL(url);
                
                setStatus(`Downloaded ${downloadLink.download}`, 'success');
                
            } catch (error) {
                console.error('Error downloading SVG:', error);
                setStatus(`Error downloading: ${error.message}`, 'error');
            }
        }
        
        // Update status message
        function setStatus(message, type = 'info') {
            if (!statusEl) return;
            
            statusEl.textContent = message;
            statusEl.className = `status status-${type}`;
            
            // Auto-clear success messages after 3 seconds
            if (type === 'success') {
                setTimeout(() => {
                    if (statusEl.textContent === message) {
                        statusEl.textContent = '';
                        statusEl.className = 'status';
                    }
                }, 3000);
            }
        }
        
        // Initialize when DOM is loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
        } else {
            init();
        }
    </script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --error-color: #e74c3c;
            --bg-color: #f5f7fa;
            --text-color: #333;
            --border-color: #ddd;
            --card-bg: #fff;
            --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-color);
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        .subtitle {
            color: var(--secondary-color);
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
        }
        
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1.5rem;
            align-items: center;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            min-width: 250px;
            flex: 1;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--secondary-color);
        }
        
        select, button {
            padding: 0.6rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 1rem;
            background-color: var(--card-bg);
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.2s;
        }
        
        select {
            padding: 0.6rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 1rem;
            background-color: var(--card-bg);
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.2s;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23333%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
            background-repeat: no-repeat, repeat;
            background-position: right 0.7em top 50%, 0 0;
            background-size: 0.65em auto, 100%;
            padding-right: 2.5em;
        }
        
        button {
            background-color: var(--accent-color);
            color: white;
            border: none;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        button:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        button.secondary {
            background-color: var(--secondary-color);
        }
        
        #diagram {
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: var(--shadow);
            padding: 2rem;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: auto;
            border: 1px solid var(--border-color);
        }
        
        .mermaid-loading {
            color: var(--secondary-color);
            font-style: italic;
        }
        
        .error {
            color: var(--error-color);
            padding: 1rem;
            background-color: rgba(231, 76, 60, 0.1);
            border-radius: 4px;
            border-left: 4px solid var(--error-color);
        }
        
        .error h3 {
            margin-top: 0;
            margin-bottom: 0.5rem;
        }
        
        .error pre {
            background-color: rgba(0, 0, 0, 0.05);
            padding: 0.5rem;
            border-radius: 4px;
            overflow-x: auto;
            margin-top: 0.5rem;
            font-size: 0.9rem;
        }
        
        .status {
            margin-top: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            display: inline-block;
            font-size: 0.9rem;
        }
        
        .status-info {
            background-color: rgba(52, 152, 219, 0.1);
            color: var(--accent-color);
        }
        
        .status-success {
            background-color: rgba(46, 204, 113, 0.1);
            color: var(--success-color);
        }
        
        .status-warning {
            background-color: rgba(243, 156, 18, 0.1);
            color: var(--warning-color);
        }
        
        .status-error {
            background-color: rgba(231, 76, 60, 0.1);
            color: var(--error-color);
        }
        
        /* Dark theme */
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-color: #1a1a1a;
                --text-color: #f0f0f0;
                --border-color: #444;
                --card-bg: #2d2d2d;
                --primary-color: #5d9cec;
                --secondary-color: #a0a0a0;
                --shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            }
            
            select, button {
                border-color: #555;
                background-color: #3a3a3a;
                color: var(--text-color);
            }
            
            select {
                background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23a0a0a0%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
            }
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-group {
                width: 100%;
                margin-bottom: 1rem;
            }
            
            #diagram {
                padding: 1rem;
                min-height: 300px;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>Comphyology Diagram Viewer</h1>
        <p class="subtitle">View and export Mermaid diagrams for the Comphyology Patent</p>
    </header>
    
    <div class="controls">
        <div class="control-group">
            <label for="diagramSelector">Select Diagram</label>
            <select id="diagramSelector">
                <option value="">-- Loading diagrams... --</option>
            </select>
        </div>
        
        <div class="control-group">
            <label for="themeSelector">Theme</label>
            <select id="themeSelector">
                <!-- Populated by JavaScript -->
            </select>
        </div>
        
        <div class="control-group" style="justify-content: flex-end;">
            <button id="downloadSvg" title="Download as SVG">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                Download SVG
            </button>
        </div>
    </div>
    
    <div id="diagram">
        <p>Select a diagram from the dropdown above</p>
    </div>
    
    <div id="status" class="status"></div>
    
    <!-- Removed JavaScript requirement message -->
    <script>
        // This space intentionally left blank - initialization happens in the module script
    </script>
</body>
</html>
