/**
 * Notification System
 * 
 * This module provides functionality for generating and managing notifications
 * related to privacy management activities.
 */

const { v4: uuidv4 } = require('uuid');
const models = require('./models');

/**
 * Notification types
 */
const NOTIFICATION_TYPES = {
  DSR_RECEIVED: 'dsr-received',
  DSR_DUE_SOON: 'dsr-due-soon',
  DSR_OVERDUE: 'dsr-overdue',
  DSR_COMPLETED: 'dsr-completed',
  CONSENT_WITHDRAWN: 'consent-withdrawn',
  CONSENT_EXPIRED: 'consent-expired',
  DATA_BREACH_REPORTED: 'data-breach-reported',
  DATA_BREACH_NOTIFICATION_DUE: 'data-breach-notification-due',
  DPIA_REQUIRED: 'dpia-required',
  REGULATORY_CHANGE: 'regulatory-change',
  COMPLIANCE_ISSUE: 'compliance-issue'
};

/**
 * Notification priorities
 */
const NOTIFICATION_PRIORITIES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent'
};

/**
 * Notification channels
 */
const NOTIFICATION_CHANNELS = {
  EMAIL: 'email',
  SMS: 'sms',
  PUSH: 'push',
  IN_APP: 'in-app',
  WEBHOOK: 'webhook'
};

/**
 * Notification statuses
 */
const NOTIFICATION_STATUSES = {
  PENDING: 'pending',
  SENT: 'sent',
  DELIVERED: 'delivered',
  READ: 'read',
  FAILED: 'failed'
};

/**
 * In-memory storage for notifications
 */
const notifications = [];

/**
 * Create a notification
 * @param {Object} notificationData - The notification data
 * @returns {Object} - The created notification
 */
const createNotification = (notificationData) => {
  const {
    type,
    title,
    message,
    priority = NOTIFICATION_PRIORITIES.MEDIUM,
    recipients,
    channels = [NOTIFICATION_CHANNELS.IN_APP],
    relatedEntityType,
    relatedEntityId,
    metadata = {}
  } = notificationData;

  // Create a new notification with a unique ID
  const newNotification = {
    id: `not-${uuidv4().substring(0, 4)}`,
    type,
    title,
    message,
    priority,
    recipients,
    channels,
    relatedEntityType,
    relatedEntityId,
    metadata,
    status: NOTIFICATION_STATUSES.PENDING,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    sentAt: null,
    deliveredAt: null,
    readAt: null
  };

  // Add the new notification to the collection
  notifications.push(newNotification);

  return newNotification;
};

/**
 * Get notifications
 * @param {Object} filters - Filters for the notifications
 * @returns {Array} - The filtered notifications
 */
const getNotifications = (filters = {}) => {
  const {
    recipient,
    status,
    type,
    priority,
    relatedEntityType,
    relatedEntityId,
    startDate,
    endDate
  } = filters;

  let filteredNotifications = [...notifications];

  // Filter by recipient
  if (recipient) {
    filteredNotifications = filteredNotifications.filter(n => 
      n.recipients.includes(recipient)
    );
  }

  // Filter by status
  if (status) {
    filteredNotifications = filteredNotifications.filter(n => n.status === status);
  }

  // Filter by type
  if (type) {
    filteredNotifications = filteredNotifications.filter(n => n.type === type);
  }

  // Filter by priority
  if (priority) {
    filteredNotifications = filteredNotifications.filter(n => n.priority === priority);
  }

  // Filter by related entity type
  if (relatedEntityType) {
    filteredNotifications = filteredNotifications.filter(n => n.relatedEntityType === relatedEntityType);
  }

  // Filter by related entity ID
  if (relatedEntityId) {
    filteredNotifications = filteredNotifications.filter(n => n.relatedEntityId === relatedEntityId);
  }

  // Filter by date range
  if (startDate) {
    const start = new Date(startDate);
    filteredNotifications = filteredNotifications.filter(n => new Date(n.createdAt) >= start);
  }

  if (endDate) {
    const end = new Date(endDate);
    filteredNotifications = filteredNotifications.filter(n => new Date(n.createdAt) <= end);
  }

  return filteredNotifications;
};

/**
 * Get a notification by ID
 * @param {string} id - The notification ID
 * @returns {Object|null} - The notification or null if not found
 */
const getNotificationById = (id) => {
  return notifications.find(n => n.id === id) || null;
};

/**
 * Update notification status
 * @param {string} id - The notification ID
 * @param {string} status - The new status
 * @returns {Object|null} - The updated notification or null if not found
 */
const updateNotificationStatus = (id, status) => {
  const notificationIndex = notifications.findIndex(n => n.id === id);

  if (notificationIndex === -1) {
    return null;
  }

  const updatedNotification = {
    ...notifications[notificationIndex],
    status,
    updatedAt: new Date().toISOString()
  };

  // Update timestamps based on status
  if (status === NOTIFICATION_STATUSES.SENT && !updatedNotification.sentAt) {
    updatedNotification.sentAt = new Date().toISOString();
  } else if (status === NOTIFICATION_STATUSES.DELIVERED && !updatedNotification.deliveredAt) {
    updatedNotification.deliveredAt = new Date().toISOString();
  } else if (status === NOTIFICATION_STATUSES.READ && !updatedNotification.readAt) {
    updatedNotification.readAt = new Date().toISOString();
  }

  // Replace the old notification with the updated one
  notifications[notificationIndex] = updatedNotification;

  return updatedNotification;
};

/**
 * Mark notification as read
 * @param {string} id - The notification ID
 * @returns {Object|null} - The updated notification or null if not found
 */
const markNotificationAsRead = (id) => {
  return updateNotificationStatus(id, NOTIFICATION_STATUSES.READ);
};

/**
 * Send notification
 * @param {string} id - The notification ID
 * @returns {Object} - The result of the send operation
 */
const sendNotification = (id) => {
  const notification = getNotificationById(id);

  if (!notification) {
    return {
      success: false,
      message: `Notification with ID ${id} not found`
    };
  }

  if (notification.status !== NOTIFICATION_STATUSES.PENDING) {
    return {
      success: false,
      message: `Notification with ID ${id} is not in pending status`
    };
  }

  // In a real implementation, this would send the notification through the specified channels
  // For now, we'll just update the status
  const updatedNotification = updateNotificationStatus(id, NOTIFICATION_STATUSES.SENT);

  return {
    success: true,
    message: `Notification sent successfully`,
    notification: updatedNotification
  };
};

/**
 * Generate notifications for data subject requests
 * @returns {Array} - The generated notifications
 */
const generateDsrNotifications = () => {
  const generatedNotifications = [];
  const now = new Date();

  // Get all pending and in-progress DSRs
  const activeRequests = models.dataSubjectRequests.filter(r => 
    r.status === 'pending' || r.status === 'in-progress'
  );

  // Check for DSRs that are due soon (within 3 days)
  const threeDaysFromNow = new Date(now);
  threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);

  const dueSoonRequests = activeRequests.filter(r => {
    if (!r.dueDate) return false;
    const dueDate = new Date(r.dueDate);
    return dueDate > now && dueDate <= threeDaysFromNow;
  });

  // Check for overdue DSRs
  const overdueRequests = activeRequests.filter(r => {
    if (!r.dueDate) return false;
    const dueDate = new Date(r.dueDate);
    return dueDate < now;
  });

  // Generate notifications for DSRs due soon
  dueSoonRequests.forEach(request => {
    const dueDate = new Date(request.dueDate);
    const daysUntilDue = Math.ceil((dueDate - now) / (1000 * 60 * 60 * 24));

    const notification = createNotification({
      type: NOTIFICATION_TYPES.DSR_DUE_SOON,
      title: `Data Subject Request Due Soon`,
      message: `Request ${request.id} is due in ${daysUntilDue} day${daysUntilDue !== 1 ? 's' : ''}`,
      priority: NOTIFICATION_PRIORITIES.MEDIUM,
      recipients: [request.assignedTo || 'privacy-team'],
      channels: [NOTIFICATION_CHANNELS.EMAIL, NOTIFICATION_CHANNELS.IN_APP],
      relatedEntityType: 'data-subject-request',
      relatedEntityId: request.id,
      metadata: {
        requestType: request.requestType,
        dataSubjectName: request.dataSubjectName,
        dueDate: request.dueDate
      }
    });

    generatedNotifications.push(notification);
  });

  // Generate notifications for overdue DSRs
  overdueRequests.forEach(request => {
    const dueDate = new Date(request.dueDate);
    const daysOverdue = Math.ceil((now - dueDate) / (1000 * 60 * 60 * 24));

    const notification = createNotification({
      type: NOTIFICATION_TYPES.DSR_OVERDUE,
      title: `Data Subject Request Overdue`,
      message: `Request ${request.id} is overdue by ${daysOverdue} day${daysOverdue !== 1 ? 's' : ''}`,
      priority: NOTIFICATION_PRIORITIES.HIGH,
      recipients: [request.assignedTo || 'privacy-team', 'privacy-officer'],
      channels: [NOTIFICATION_CHANNELS.EMAIL, NOTIFICATION_CHANNELS.IN_APP, NOTIFICATION_CHANNELS.SMS],
      relatedEntityType: 'data-subject-request',
      relatedEntityId: request.id,
      metadata: {
        requestType: request.requestType,
        dataSubjectName: request.dataSubjectName,
        dueDate: request.dueDate
      }
    });

    generatedNotifications.push(notification);
  });

  return generatedNotifications;
};

/**
 * Generate notifications for data breaches
 * @returns {Array} - The generated notifications
 */
const generateDataBreachNotifications = () => {
  const generatedNotifications = [];
  const now = new Date();

  // Get all open data breaches
  const openBreaches = models.dataBreaches.filter(b => b.status === 'open');

  // Check for breaches that require notification to authorities
  const breachesRequiringNotification = openBreaches.filter(b => {
    // Check if notification to authorities is required but not completed
    return b.notificationStatus && 
           b.notificationStatus.authorities && 
           b.notificationStatus.authorities.required && 
           !b.notificationStatus.authorities.completed;
  });

  // Generate notifications for breaches requiring notification
  breachesRequiringNotification.forEach(breach => {
    // Calculate days since detection
    const detectionDate = new Date(breach.detectionDate);
    const daysSinceDetection = Math.ceil((now - detectionDate) / (1000 * 60 * 60 * 24));

    // In many jurisdictions, breaches must be reported within 72 hours
    const isUrgent = daysSinceDetection >= 2; // 48+ hours since detection

    const notification = createNotification({
      type: NOTIFICATION_TYPES.DATA_BREACH_NOTIFICATION_DUE,
      title: `Data Breach Notification Required`,
      message: isUrgent 
        ? `URGENT: Data breach ${breach.id} must be reported to authorities immediately (${daysSinceDetection} days since detection)`
        : `Data breach ${breach.id} requires notification to authorities (${daysSinceDetection} days since detection)`,
      priority: isUrgent ? NOTIFICATION_PRIORITIES.URGENT : NOTIFICATION_PRIORITIES.HIGH,
      recipients: ['privacy-officer', 'security-team', 'legal-team'],
      channels: isUrgent 
        ? [NOTIFICATION_CHANNELS.EMAIL, NOTIFICATION_CHANNELS.IN_APP, NOTIFICATION_CHANNELS.SMS]
        : [NOTIFICATION_CHANNELS.EMAIL, NOTIFICATION_CHANNELS.IN_APP],
      relatedEntityType: 'data-breach',
      relatedEntityId: breach.id,
      metadata: {
        breachType: breach.breachType,
        detectionDate: breach.detectionDate,
        potentialImpact: breach.potentialImpact,
        approximateSubjectsCount: breach.approximateSubjectsCount
      }
    });

    generatedNotifications.push(notification);
  });

  return generatedNotifications;
};

/**
 * Generate notifications for DPIAs
 * @returns {Array} - The generated notifications
 */
const generateDpiaNotifications = () => {
  const generatedNotifications = [];

  // Get all activities requiring DPIA but not completed
  const activitiesRequiringDpia = models.dataProcessingActivities.filter(a => 
    a.dpia && a.dpia.required && !a.dpia.completed && a.status === 'active'
  );

  // Generate notifications for activities requiring DPIA
  activitiesRequiringDpia.forEach(activity => {
    const notification = createNotification({
      type: NOTIFICATION_TYPES.DPIA_REQUIRED,
      title: `DPIA Required`,
      message: `Data processing activity "${activity.name}" requires a Data Protection Impact Assessment`,
      priority: NOTIFICATION_PRIORITIES.MEDIUM,
      recipients: ['privacy-team', 'privacy-officer'],
      channels: [NOTIFICATION_CHANNELS.EMAIL, NOTIFICATION_CHANNELS.IN_APP],
      relatedEntityType: 'data-processing-activity',
      relatedEntityId: activity.id,
      metadata: {
        activityName: activity.name,
        activityStatus: activity.status,
        dpiaStatus: 'required'
      }
    });

    generatedNotifications.push(notification);
  });

  return generatedNotifications;
};

/**
 * Generate all notifications
 * @returns {Array} - The generated notifications
 */
const generateAllNotifications = () => {
  const dsrNotifications = generateDsrNotifications();
  const dataBreachNotifications = generateDataBreachNotifications();
  const dpiaNotifications = generateDpiaNotifications();

  return [
    ...dsrNotifications,
    ...dataBreachNotifications,
    ...dpiaNotifications
  ];
};

/**
 * Send all pending notifications
 * @returns {Object} - The result of the send operation
 */
const sendAllPendingNotifications = () => {
  const pendingNotifications = getNotifications({ status: NOTIFICATION_STATUSES.PENDING });
  
  const results = {
    total: pendingNotifications.length,
    sent: 0,
    failed: 0,
    details: []
  };

  pendingNotifications.forEach(notification => {
    const result = sendNotification(notification.id);
    
    if (result.success) {
      results.sent++;
    } else {
      results.failed++;
    }
    
    results.details.push({
      notificationId: notification.id,
      success: result.success,
      message: result.message
    });
  });

  return results;
};

module.exports = {
  NOTIFICATION_TYPES,
  NOTIFICATION_PRIORITIES,
  NOTIFICATION_CHANNELS,
  NOTIFICATION_STATUSES,
  createNotification,
  getNotifications,
  getNotificationById,
  updateNotificationStatus,
  markNotificationAsRead,
  sendNotification,
  generateDsrNotifications,
  generateDataBreachNotifications,
  generateDpiaNotifications,
  generateAllNotifications,
  sendAllPendingNotifications
};
