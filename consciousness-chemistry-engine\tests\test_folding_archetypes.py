"""
Tests for folding archetypes in NovaFoldClient.
"""

import unittest
from typing import Dict, List, Tuple, Any
import numpy as np

# Import the module to test
from src.ConsciousNovaFold import NovaFoldClient

class TestFoldingArchetypes(unittest.TestCase):
    """Test cases for folding archetypes in NovaFoldClient."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.client = NovaFoldClient(enable_benchmark=False)
        self.test_sequence = "ACDEFGHIKLMNPQRSTVWY"  # 20 amino acids
    
    def test_list_archetypes(self):
        """Test listing available folding archetypes."""
        archetypes = self.client.list_archetypes()
        self.assertIsInstance(archetypes, list)
        self.assertGreater(len(archetypes), 0)
        self.assertIn('native', archetypes)
        self.assertIn('misfolded', archetypes)
        self.assertIn('amyloid', archetypes)
    
    def test_get_archetype_info(self):
        """Test getting information about a specific archetype."""
        # Test with valid archetype
        native_info = self.client.get_archetype_info('native')
        self.assertIsInstance(native_info, dict)
        self.assertEqual(native_info.get('description'), 'Native, functional protein fold')
        
        # Test with invalid archetype
        invalid_info = self.client.get_archetype_info('nonexistent')
        self.assertIn('error', invalid_info)
    
    def test_native_archetype(self):
        """Test the native folding archetype."""
        result = self.client.predict(
            sequence=self.test_sequence,
            folding_variant='native',
            validate_against=None
        )
        
        # Check metadata
        metadata = result['structure']['metadata']
        self.assertEqual(metadata['folding_archetype'], 'native')
        self.assertEqual(metadata['consciousness_impact'], 'neutral')
        
        # Check pLDDT scores are in expected range (0.8-1.0 for native)
        plddt_scores = result['structure']['plddt']
        self.assertTrue(all(0.8 <= score <= 1.0 for score in plddt_scores))
    
    def test_misfolded_archetype(self):
        """Test the misfolded archetype."""
        result = self.client.predict(
            sequence=self.test_sequence,
            folding_variant='misfolded',
            validate_against=None
        )
        
        metadata = result['structure']['metadata']
        self.assertEqual(metadata['folding_archetype'], 'misfolded')
        self.assertEqual(metadata['consciousness_impact'], 'reduced_consciousness')
        
        # Check pLDDT scores are reduced compared to native
        plddt_scores = result['structure']['plddt']
        self.assertTrue(all(score <= 0.8 for score in plddt_scores))
    
    def test_amyloid_archetype(self):
        """Test the amyloid archetype."""
        result = self.client.predict(
            sequence=self.test_sequence,
            folding_variant='amyloid',
            validate_against=None
        )
        
        metadata = result['structure']['metadata']
        self.assertEqual(metadata['folding_archetype'], 'amyloid')
        self.assertEqual(metadata['consciousness_impact'], 'pathological_consciousness')
        self.assertTrue(metadata.get('cross_beta_pattern', False))
        
        # Check for beta-sheet enrichment
        ss = result['structure']['secondary_structure']
        beta_content = ss.count('E') / len(ss)
        self.assertGreater(beta_content, 0.4)  # At least 40% beta-sheet
    
    def test_consciousness_enhanced_archetype(self):
        """Test the consciousness-enhanced archetype."""
        result = self.client.predict(
            sequence=self.test_sequence,
            folding_variant='consciousness_enhanced',
            validate_against=None
        )
        
        metadata = result['structure']['metadata']
        self.assertEqual(metadata['folding_archetype'], 'consciousness_enhanced')
        self.assertEqual(metadata['consciousness_impact'], 'enhanced_consciousness')
        
        # Check for improved stability metrics
        stability = result['structure']['stability_metrics']
        self.assertGreater(stability['ddg'], 1.0)  # More stable than native
    
    def test_quantum_entangled_archetype(self):
        """Test the quantum entangled archetype."""
        result = self.client.predict(
            sequence=self.test_sequence,
            folding_variant='entangled',
            validate_against=None
        )
        
        metadata = result['structure']['metadata']
        self.assertEqual(metadata['folding_archetype'], 'entangled')
        self.assertEqual(metadata['consciousness_impact'], 'quantum_consciousness')
        self.assertTrue(metadata.get('quantum_entangled', False))
        self.assertIn('quantum_entropy', metadata)

if __name__ == "__main__":
    unittest.main()
