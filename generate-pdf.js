const fs = require('fs');
const markdownpdf = require('markdown-pdf');
const path = require('path');

const inputFile = 'Comphyological_Dictionary_Final.md';
const outputFile = 'Comphyological_Dictionary_1st_Edition.pdf';

console.log(`Converting ${inputFile} to PDF...`);

markdownpdf()
  .from(inputFile)
  .to(outputFile, () => {
    console.log(`✅ Successfully created ${outputFile}`);
    console.log(`File saved to: ${path.resolve(process.cwd(), outputFile)}`);
  })
  .on('error', (err) => {
    console.error('Error generating PDF:', err);
  });
