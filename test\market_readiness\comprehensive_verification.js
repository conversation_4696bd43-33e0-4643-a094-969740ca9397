/**
 * Comprehensive Market Readiness Verification
 * 
 * This script performs a comprehensive verification of NovaFuse's market readiness,
 * focusing on the Quantum State Inference Layer and RBAC implementation.
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

// Configuration
const config = {
  apiUrl: process.env.API_URL || 'http://localhost:3002',
  prometheusUrl: process.env.PROMETHEUS_URL || 'http://localhost:9090',
  grafanaUrl: process.env.GRAFANA_URL || 'http://localhost:3003',
  testIterations: 100,
  testTimeout: 300000, // 5 minutes
  resultsDir: path.join(__dirname, '../../test-results')
};

// Ensure results directory exists
async function ensureResultsDir() {
  try {
    await mkdir(config.resultsDir, { recursive: true });
    console.log(`Results directory created: ${config.resultsDir}`);
  } catch (error) {
    if (error.code !== 'EEXIST') {
      console.error('Error creating results directory:', error);
      throw error;
    }
  }
}

// Wait for API to be ready
async function waitForApi() {
  console.log(`Waiting for API to be ready at ${config.apiUrl}...`);
  
  const startTime = Date.now();
  let isReady = false;
  
  while (!isReady && Date.now() - startTime < config.testTimeout) {
    try {
      const response = await axios.get(`${config.apiUrl}/health`);
      if (response.status === 200 && response.data.status === 'ok') {
        isReady = true;
        console.log('API is ready!');
      } else {
        console.log('API not ready yet, waiting...');
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    } catch (error) {
      console.log('API not ready yet, waiting...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  
  if (!isReady) {
    throw new Error('Timeout waiting for API to be ready');
  }
}

// Test Quantum State Inference
async function testQuantumInference() {
  console.log('\n=== Testing Quantum State Inference ===');
  
  const results = {
    iterations: config.testIterations,
    certaintyRates: [],
    inferenceTimes: [],
    averageCertaintyRate: 0,
    averageInferenceTime: 0,
    passed: false
  };
  
  // Generate test data
  const testData = generateTestData(50);
  
  // Run test iterations
  for (let i = 0; i < config.testIterations; i++) {
    try {
      console.log(`Running iteration ${i + 1}/${config.testIterations}...`);
      
      const startTime = Date.now();
      const response = await axios.post(`${config.apiUrl}/api/quantum/predict`, testData);
      const inferenceTime = Date.now() - startTime;
      
      if (response.status === 200 && response.data) {
        const certaintyRate = response.data.metrics.certaintyRate * 100;
        
        results.certaintyRates.push(certaintyRate);
        results.inferenceTimes.push(inferenceTime);
        
        console.log(`  Certainty Rate: ${certaintyRate.toFixed(2)}%, Inference Time: ${inferenceTime}ms`);
      } else {
        console.error(`  Error in iteration ${i + 1}: Unexpected response`);
      }
    } catch (error) {
      console.error(`  Error in iteration ${i + 1}:`, error.message);
    }
    
    // Small delay between iterations
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  // Calculate averages
  if (results.certaintyRates.length > 0) {
    results.averageCertaintyRate = results.certaintyRates.reduce((sum, rate) => sum + rate, 0) / results.certaintyRates.length;
    results.averageInferenceTime = results.inferenceTimes.reduce((sum, time) => sum + time, 0) / results.inferenceTimes.length;
    
    // Check if passed
    results.passed = results.averageCertaintyRate >= 30;
  }
  
  // Print results
  console.log('\nQuantum Inference Test Results:');
  console.log(`- Average Certainty Rate: ${results.averageCertaintyRate.toFixed(2)}%`);
  console.log(`- Average Inference Time: ${results.averageInferenceTime.toFixed(2)}ms`);
  console.log(`- Market Readiness: ${results.passed ? 'READY' : 'NOT READY'}`);
  
  return results;
}

// Test RBAC Implementation
async function testRbacImplementation() {
  console.log('\n=== Testing RBAC Implementation ===');
  
  const results = {
    testCases: [],
    passCount: 0,
    totalTests: 0,
    passRate: 0,
    passed: false
  };
  
  // Define test cases
  const testCases = [
    { userId: 'user-ciso', componentType: 'quantum_inference', action: 'view', expected: true },
    { userId: 'user-ciso', componentType: 'view:dashboard', action: 'view', expected: true },
    { userId: 'user-ciso', componentType: 'view:audit_logs', action: 'view', expected: true },
    
    { userId: 'user-analyst', componentType: 'quantum_inference', action: 'view', expected: true },
    { userId: 'user-analyst', componentType: 'view:dashboard', action: 'view', expected: true },
    { userId: 'user-analyst', componentType: 'view:audit_logs', action: 'view', expected: false },
    
    { userId: 'user-standard', componentType: 'quantum_inference', action: 'view', expected: false },
    { userId: 'user-standard', componentType: 'view:dashboard', action: 'view', expected: false },
    { userId: 'user-standard', componentType: 'view:audit_logs', action: 'view', expected: false },
    
    { userId: 'user-auditor', componentType: 'quantum_inference', action: 'view', expected: true },
    { userId: 'user-auditor', componentType: 'view:dashboard', action: 'view', expected: true },
    { userId: 'user-auditor', componentType: 'view:audit_logs', action: 'view', expected: true }
  ];
  
  // Run test cases
  for (const testCase of testCases) {
    results.totalTests++;
    
    try {
      const response = await axios.post(`${config.apiUrl}/api/security/validate-access`, {
        userId: testCase.userId,
        componentType: testCase.componentType,
        action: testCase.action
      });
      
      if (response.status === 200) {
        const accessResult = response.data;
        const passed = accessResult.allowed === testCase.expected;
        
        if (passed) {
          results.passCount++;
        }
        
        results.testCases.push({
          ...testCase,
          result: accessResult.allowed,
          passed,
          reason: accessResult.reason
        });
        
        console.log(`${testCase.userId} accessing ${testCase.componentType}: expected=${testCase.expected}, actual=${accessResult.allowed}, ${passed ? 'PASSED' : 'FAILED'}`);
      } else {
        console.error(`Error testing ${testCase.userId} accessing ${testCase.componentType}: Unexpected response`);
      }
    } catch (error) {
      console.error(`Error testing ${testCase.userId} accessing ${testCase.componentType}:`, error.message);
    }
  }
  
  // Calculate pass rate
  results.passRate = (results.passCount / results.totalTests) * 100;
  
  // Check if passed (require 100% pass rate)
  results.passed = results.passRate === 100;
  
  // Print results
  console.log('\nRBAC Test Results:');
  console.log(`- Tests Passed: ${results.passCount}/${results.totalTests}`);
  console.log(`- Pass Rate: ${results.passRate.toFixed(2)}%`);
  console.log(`- Market Readiness: ${results.passed ? 'READY' : 'NOT READY'}`);
  
  return results;
}

// Test Metrics Collection
async function testMetricsCollection() {
  console.log('\n=== Testing Metrics Collection ===');
  
  const results = {
    metricsFound: [],
    missingMetrics: [],
    totalMetrics: 0,
    foundMetrics: 0,
    passRate: 0,
    passed: false
  };
  
  // Define expected metrics
  const expectedMetrics = [
    'quantum_certainty_rate',
    'quantum_inference_time',
    'quantum_collapse_events',
    'quantum_actionable_intelligence',
    'trinity_csde_performance'
  ];
  
  results.totalMetrics = expectedMetrics.length;
  
  try {
    // Query Prometheus for metrics
    const response = await axios.get(`${config.prometheusUrl}/api/v1/metadata`);
    
    if (response.status === 200 && response.data && response.data.data) {
      const availableMetrics = Object.keys(response.data.data);
      
      // Check each expected metric
      for (const metric of expectedMetrics) {
        if (availableMetrics.includes(metric)) {
          results.metricsFound.push(metric);
          results.foundMetrics++;
          console.log(`Metric found: ${metric}`);
        } else {
          results.missingMetrics.push(metric);
          console.log(`Metric missing: ${metric}`);
        }
      }
    } else {
      console.error('Error querying Prometheus: Unexpected response');
    }
  } catch (error) {
    console.error('Error querying Prometheus:', error.message);
  }
  
  // Calculate pass rate
  results.passRate = (results.foundMetrics / results.totalMetrics) * 100;
  
  // Check if passed (require at least 80% of metrics)
  results.passed = results.passRate >= 80;
  
  // Print results
  console.log('\nMetrics Collection Test Results:');
  console.log(`- Metrics Found: ${results.foundMetrics}/${results.totalMetrics}`);
  console.log(`- Pass Rate: ${results.passRate.toFixed(2)}%`);
  console.log(`- Market Readiness: ${results.passed ? 'READY' : 'NOT READY'}`);
  
  return results;
}

// Generate test data for quantum inference
function generateTestData(stateCount) {
  // Generate threats
  const threats = {};
  for (let i = 0; i < stateCount / 3; i++) {
    const threatId = `threat-${i}`;
    threats[threatId] = {
      name: `Threat ${i}`,
      severity: Math.random() * 0.5 + 0.5, // 0.5 to 1.0
      confidence: Math.random() * 0.5 + 0.5 // 0.5 to 1.0
    };
  }
  
  // Generate detection data
  return {
    detectionCapability: Math.random() * 0.3 + 0.7, // 0.7 to 1.0
    threatSeverity: Math.random() * 0.5 + 0.5, // 0.5 to 1.0
    threatConfidence: Math.random() * 0.3 + 0.7, // 0.7 to 1.0
    baselineSignals: Math.random() * 0.3 + 0.7, // 0.7 to 1.0
    timestamp: new Date().toISOString(),
    source: 'test',
    confidence: Math.random() * 0.2 + 0.8, // 0.8 to 1.0
    threats
  };
}

// Run all tests
async function runAllTests() {
  console.log('=== NovaFuse Market Readiness Verification ===\n');
  console.log(`API URL: ${config.apiUrl}`);
  console.log(`Prometheus URL: ${config.prometheusUrl}`);
  console.log(`Grafana URL: ${config.grafanaUrl}`);
  console.log(`Test Iterations: ${config.testIterations}`);
  
  const startTime = Date.now();
  
  try {
    // Ensure results directory exists
    await ensureResultsDir();
    
    // Wait for API to be ready
    await waitForApi();
    
    // Run tests
    const quantumResults = await testQuantumInference();
    const rbacResults = await testRbacImplementation();
    const metricsResults = await testMetricsCollection();
    
    // Compile overall results
    const overallResults = {
      timestamp: new Date().toISOString(),
      duration: Date.now() - startTime,
      quantum: quantumResults,
      rbac: rbacResults,
      metrics: metricsResults,
      overallPassed: quantumResults.passed && rbacResults.passed && metricsResults.passed
    };
    
    // Save results
    const resultsPath = path.join(config.resultsDir, `market_readiness_${new Date().toISOString().replace(/:/g, '-')}.json`);
    await writeFile(resultsPath, JSON.stringify(overallResults, null, 2));
    console.log(`\nResults saved to: ${resultsPath}`);
    
    // Print overall summary
    console.log('\n=== Market Readiness Summary ===');
    console.log(`Quantum Inference: ${quantumResults.passed ? 'READY' : 'NOT READY'} (${quantumResults.averageCertaintyRate.toFixed(2)}%)`);
    console.log(`RBAC Implementation: ${rbacResults.passed ? 'READY' : 'NOT READY'} (${rbacResults.passRate.toFixed(2)}%)`);
    console.log(`Metrics Collection: ${metricsResults.passed ? 'READY' : 'NOT READY'} (${metricsResults.passRate.toFixed(2)}%)`);
    console.log(`\nOverall Market Readiness: ${overallResults.overallPassed ? 'READY' : 'NOT READY'}`);
    
    // Return exit code based on overall result
    process.exit(overallResults.overallPassed ? 0 : 1);
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run all tests
runAllTests();
