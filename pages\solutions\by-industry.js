import React from 'react';
import Link from 'next/link';
import PageWithSidebar from '../../components/PageWithSidebar';

const SolutionsByIndustry = () => {
  const sidebarItems = [
    { label: 'Healthcare', href: '#healthcare' },
    { label: 'Financial Services', href: '#financial-services' },
    { label: 'Technology', href: '#technology' },
    { label: 'Government', href: '#government' },
    { label: 'Education', href: '#education' },
    { label: 'Back to Solutions', href: '/solutions' },
  ];

  return (
    <PageWithSidebar title="Solutions by Industry" sidebarItems={sidebarItems}>
      <div className="space-y-12">
        {/* Hero Section */}
        <div className="bg-blue-900 p-8 rounded-lg shadow-lg">
          <h1 className="text-3xl font-bold mb-4">Industry-Specific Compliance Solutions</h1>
          <p className="text-xl mb-6">
            NovaFuse provides tailored compliance solutions for various industries, addressing their unique regulatory requirements and challenges.
          </p>
        </div>

        {/* Healthcare Section */}
        <div id="healthcare" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Healthcare</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-4">
              Our healthcare compliance solutions help organizations navigate complex regulations like HIPAA, HITECH, and other healthcare-specific requirements.
            </p>
            
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Key Regulations</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>HIPAA/HITECH</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>GDPR (health data)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>FDA Regulations</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>State Privacy Laws</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Key Features</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>PHI Data Mapping</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Business Associate Management</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Security Risk Assessments</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Breach Notification Workflows</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Financial Services Section */}
        <div id="financial-services" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Financial Services</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-4">
              Our financial services compliance solutions address the complex regulatory landscape facing banks, insurance companies, and other financial institutions.
            </p>
            
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Key Regulations</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>SOX</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>GLBA</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>PCI DSS</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>AML/KYC Requirements</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Key Features</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Financial Control Testing</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Transaction Monitoring</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Fraud Detection Integration</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Regulatory Reporting</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Technology Section */}
        <div id="technology" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Technology</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-4">
              Our technology sector compliance solutions help software companies, SaaS providers, and technology firms meet their security and privacy obligations.
            </p>
            
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Key Regulations</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>SOC 2</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>ISO 27001</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>GDPR</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>CCPA/CPRA</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Key Features</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>DevSecOps Integration</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>CI/CD Pipeline Controls</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Cloud Security Posture Management</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Privacy by Design Framework</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* More industries can be added here */}

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-900 to-purple-900 p-8 rounded-lg shadow-lg border border-blue-400">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Need a Customized Industry Solution?</h2>
            <p className="mb-6">
              Contact us to discuss how NovaFuse can address your industry-specific compliance challenges.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/contact" className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                Contact Us
              </Link>
              <Link href="/partner-empowerment" className="bg-white text-blue-700 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                Partner With Us
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
};

export default SolutionsByIndustry;
