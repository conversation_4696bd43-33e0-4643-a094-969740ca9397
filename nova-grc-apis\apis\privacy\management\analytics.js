/**
 * Advanced Analytics and Reporting
 * 
 * This module provides functionality for generating analytics and reports
 * related to privacy management activities.
 */

const models = require('./models');

/**
 * Time periods for analytics
 */
const TIME_PERIODS = {
  LAST_7_DAYS: 'last-7-days',
  LAST_30_DAYS: 'last-30-days',
  LAST_90_DAYS: 'last-90-days',
  LAST_12_MONTHS: 'last-12-months',
  YEAR_TO_DATE: 'year-to-date',
  CUSTOM: 'custom'
};

/**
 * Report types
 */
const REPORT_TYPES = {
  DSR_SUMMARY: 'dsr-summary',
  CONSENT_MANAGEMENT: 'consent-management',
  DATA_BREACH: 'data-breach',
  PROCESSING_ACTIVITIES: 'processing-activities',
  COMPLIANCE_STATUS: 'compliance-status'
};

/**
 * Get date range for a time period
 * @param {string} period - Time period
 * @param {string} startDate - Start date for custom period (ISO string)
 * @param {string} endDate - End date for custom period (ISO string)
 * @returns {Object} - Date range with start and end dates
 */
const getDateRange = (period, startDate = null, endDate = null) => {
  const now = new Date();
  const endOfDay = new Date(now);
  endOfDay.setHours(23, 59, 59, 999);
  
  let start = new Date(now);
  let end = new Date(endOfDay);
  
  switch (period) {
    case TIME_PERIODS.LAST_7_DAYS:
      start.setDate(start.getDate() - 7);
      break;
      
    case TIME_PERIODS.LAST_30_DAYS:
      start.setDate(start.getDate() - 30);
      break;
      
    case TIME_PERIODS.LAST_90_DAYS:
      start.setDate(start.getDate() - 90);
      break;
      
    case TIME_PERIODS.LAST_12_MONTHS:
      start.setMonth(start.getMonth() - 12);
      break;
      
    case TIME_PERIODS.YEAR_TO_DATE:
      start = new Date(now.getFullYear(), 0, 1); // January 1st of current year
      break;
      
    case TIME_PERIODS.CUSTOM:
      if (startDate && endDate) {
        start = new Date(startDate);
        end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
      } else {
        throw new Error('Start and end dates are required for custom period');
      }
      break;
      
    default:
      start.setDate(start.getDate() - 30); // Default to last 30 days
  }
  
  return {
    start: start.toISOString(),
    end: end.toISOString()
  };
};

/**
 * Filter items by date range
 * @param {Array} items - Array of items to filter
 * @param {Object} dateRange - Date range with start and end dates
 * @param {string} dateField - Field to use for date filtering
 * @returns {Array} - Filtered items
 */
const filterByDateRange = (items, dateRange, dateField = 'createdAt') => {
  const start = new Date(dateRange.start);
  const end = new Date(dateRange.end);
  
  return items.filter(item => {
    const itemDate = new Date(item[dateField]);
    return itemDate >= start && itemDate <= end;
  });
};

/**
 * Generate data subject requests summary
 * @param {Object} options - Options for the report
 * @returns {Object} - DSR summary report
 */
const generateDsrSummary = (options) => {
  const { period, startDate, endDate, groupBy = 'requestType' } = options;
  
  // Get date range
  const dateRange = getDateRange(period, startDate, endDate);
  
  // Filter requests by date range
  const requests = filterByDateRange(models.dataSubjectRequests, dateRange, 'requestDate');
  
  // Calculate metrics
  const totalRequests = requests.length;
  const completedRequests = requests.filter(r => r.status === 'completed').length;
  const pendingRequests = requests.filter(r => r.status === 'pending').length;
  const inProgressRequests = requests.filter(r => r.status === 'in-progress').length;
  const rejectedRequests = requests.filter(r => r.status === 'rejected').length;
  const withdrawnRequests = requests.filter(r => r.status === 'withdrawn').length;
  
  const completionRate = totalRequests > 0 ? (completedRequests / totalRequests) * 100 : 0;
  
  // Calculate average completion time (in days)
  let avgCompletionTime = 0;
  const completedRequestsWithTime = requests.filter(r => 
    r.status === 'completed' && r.completionDate && r.requestDate
  );
  
  if (completedRequestsWithTime.length > 0) {
    const totalCompletionTime = completedRequestsWithTime.reduce((sum, r) => {
      const requestDate = new Date(r.requestDate);
      const completionDate = new Date(r.completionDate);
      const timeDiff = completionDate - requestDate;
      const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
      return sum + daysDiff;
    }, 0);
    
    avgCompletionTime = totalCompletionTime / completedRequestsWithTime.length;
  }
  
  // Group requests
  const groupedRequests = {};
  
  requests.forEach(request => {
    const groupKey = request[groupBy] || 'unknown';
    
    if (!groupedRequests[groupKey]) {
      groupedRequests[groupKey] = {
        total: 0,
        completed: 0,
        pending: 0,
        inProgress: 0,
        rejected: 0,
        withdrawn: 0
      };
    }
    
    groupedRequests[groupKey].total++;
    
    switch (request.status) {
      case 'completed':
        groupedRequests[groupKey].completed++;
        break;
      case 'pending':
        groupedRequests[groupKey].pending++;
        break;
      case 'in-progress':
        groupedRequests[groupKey].inProgress++;
        break;
      case 'rejected':
        groupedRequests[groupKey].rejected++;
        break;
      case 'withdrawn':
        groupedRequests[groupKey].withdrawn++;
        break;
    }
  });
  
  // Calculate trends (compare with previous period)
  const previousDateRange = {
    start: new Date(new Date(dateRange.start).getTime() - (new Date(dateRange.end) - new Date(dateRange.start))).toISOString(),
    end: dateRange.start
  };
  
  const previousRequests = filterByDateRange(models.dataSubjectRequests, previousDateRange, 'requestDate');
  const previousTotalRequests = previousRequests.length;
  
  const requestsTrend = previousTotalRequests > 0 
    ? ((totalRequests - previousTotalRequests) / previousTotalRequests) * 100 
    : (totalRequests > 0 ? 100 : 0);
  
  return {
    reportType: REPORT_TYPES.DSR_SUMMARY,
    dateRange,
    metrics: {
      totalRequests,
      completedRequests,
      pendingRequests,
      inProgressRequests,
      rejectedRequests,
      withdrawnRequests,
      completionRate,
      avgCompletionTime
    },
    groupedBy: groupBy,
    groupedData: groupedRequests,
    trends: {
      requestsTrend
    },
    generatedAt: new Date().toISOString()
  };
};

/**
 * Generate consent management report
 * @param {Object} options - Options for the report
 * @returns {Object} - Consent management report
 */
const generateConsentReport = (options) => {
  const { period, startDate, endDate, groupBy = 'consentType' } = options;
  
  // Get date range
  const dateRange = getDateRange(period, startDate, endDate);
  
  // Filter consent records by date range
  const records = filterByDateRange(models.consentRecords, dateRange);
  
  // Calculate metrics
  const totalRecords = records.length;
  const activeConsents = records.filter(r => r.status === 'active').length;
  const withdrawnConsents = records.filter(r => r.status === 'withdrawn').length;
  const expiredConsents = records.filter(r => r.status === 'expired').length;
  const declinedConsents = records.filter(r => r.status === 'declined').length;
  
  const consentRate = totalRecords > 0 
    ? (activeConsents / totalRecords) * 100 
    : 0;
  
  const withdrawalRate = totalRecords > 0 
    ? (withdrawnConsents / totalRecords) * 100 
    : 0;
  
  // Group records
  const groupedRecords = {};
  
  records.forEach(record => {
    const groupKey = record[groupBy] || 'unknown';
    
    if (!groupedRecords[groupKey]) {
      groupedRecords[groupKey] = {
        total: 0,
        active: 0,
        withdrawn: 0,
        expired: 0,
        declined: 0
      };
    }
    
    groupedRecords[groupKey].total++;
    
    switch (record.status) {
      case 'active':
        groupedRecords[groupKey].active++;
        break;
      case 'withdrawn':
        groupedRecords[groupKey].withdrawn++;
        break;
      case 'expired':
        groupedRecords[groupKey].expired++;
        break;
      case 'declined':
        groupedRecords[groupKey].declined++;
        break;
    }
  });
  
  // Calculate consent rates for each group
  Object.keys(groupedRecords).forEach(key => {
    const group = groupedRecords[key];
    group.consentRate = group.total > 0 
      ? (group.active / group.total) * 100 
      : 0;
    
    group.withdrawalRate = group.total > 0 
      ? (group.withdrawn / group.total) * 100 
      : 0;
  });
  
  // Calculate trends (compare with previous period)
  const previousDateRange = {
    start: new Date(new Date(dateRange.start).getTime() - (new Date(dateRange.end) - new Date(dateRange.start))).toISOString(),
    end: dateRange.start
  };
  
  const previousRecords = filterByDateRange(models.consentRecords, previousDateRange);
  const previousTotalRecords = previousRecords.length;
  const previousActiveConsents = previousRecords.filter(r => r.status === 'active').length;
  
  const recordsTrend = previousTotalRecords > 0 
    ? ((totalRecords - previousTotalRecords) / previousTotalRecords) * 100 
    : (totalRecords > 0 ? 100 : 0);
  
  const consentRateTrend = previousTotalRecords > 0 
    ? (consentRate - (previousActiveConsents / previousTotalRecords) * 100) 
    : 0;
  
  return {
    reportType: REPORT_TYPES.CONSENT_MANAGEMENT,
    dateRange,
    metrics: {
      totalRecords,
      activeConsents,
      withdrawnConsents,
      expiredConsents,
      declinedConsents,
      consentRate,
      withdrawalRate
    },
    groupedBy: groupBy,
    groupedData: groupedRecords,
    trends: {
      recordsTrend,
      consentRateTrend
    },
    generatedAt: new Date().toISOString()
  };
};

/**
 * Generate data breach report
 * @param {Object} options - Options for the report
 * @returns {Object} - Data breach report
 */
const generateDataBreachReport = (options) => {
  const { period, startDate, endDate, groupBy = 'breachType' } = options;
  
  // Get date range
  const dateRange = getDateRange(period, startDate, endDate);
  
  // Filter data breaches by date range
  const breaches = filterByDateRange(models.dataBreaches, dateRange, 'detectionDate');
  
  // Calculate metrics
  const totalBreaches = breaches.length;
  const openBreaches = breaches.filter(b => b.status === 'open').length;
  const closedBreaches = breaches.filter(b => b.status === 'closed').length;
  
  const closureRate = totalBreaches > 0 
    ? (closedBreaches / totalBreaches) * 100 
    : 0;
  
  // Calculate average containment time (in hours)
  let avgContainmentTime = 0;
  const breachesWithContainment = breaches.filter(b => 
    b.containmentDate && b.occurrenceDate
  );
  
  if (breachesWithContainment.length > 0) {
    const totalContainmentTime = breachesWithContainment.reduce((sum, b) => {
      const occurrenceDate = new Date(b.occurrenceDate);
      const containmentDate = new Date(b.containmentDate);
      const timeDiff = containmentDate - occurrenceDate;
      const hoursDiff = timeDiff / (1000 * 60 * 60);
      return sum + hoursDiff;
    }, 0);
    
    avgContainmentTime = totalContainmentTime / breachesWithContainment.length;
  }
  
  // Calculate total affected data subjects
  const totalAffectedSubjects = breaches.reduce((sum, b) => 
    sum + (b.approximateSubjectsCount || 0), 0);
  
  // Group breaches
  const groupedBreaches = {};
  
  breaches.forEach(breach => {
    const groupKey = breach[groupBy] || 'unknown';
    
    if (!groupedBreaches[groupKey]) {
      groupedBreaches[groupKey] = {
        total: 0,
        open: 0,
        closed: 0,
        affectedSubjects: 0
      };
    }
    
    groupedBreaches[groupKey].total++;
    groupedBreaches[groupKey].affectedSubjects += (breach.approximateSubjectsCount || 0);
    
    if (breach.status === 'open') {
      groupedBreaches[groupKey].open++;
    } else if (breach.status === 'closed') {
      groupedBreaches[groupKey].closed++;
    }
  });
  
  // Calculate trends (compare with previous period)
  const previousDateRange = {
    start: new Date(new Date(dateRange.start).getTime() - (new Date(dateRange.end) - new Date(dateRange.start))).toISOString(),
    end: dateRange.start
  };
  
  const previousBreaches = filterByDateRange(models.dataBreaches, previousDateRange, 'detectionDate');
  const previousTotalBreaches = previousBreaches.length;
  
  const breachesTrend = previousTotalBreaches > 0 
    ? ((totalBreaches - previousTotalBreaches) / previousTotalBreaches) * 100 
    : (totalBreaches > 0 ? 100 : 0);
  
  return {
    reportType: REPORT_TYPES.DATA_BREACH,
    dateRange,
    metrics: {
      totalBreaches,
      openBreaches,
      closedBreaches,
      closureRate,
      avgContainmentTime,
      totalAffectedSubjects
    },
    groupedBy: groupBy,
    groupedData: groupedBreaches,
    trends: {
      breachesTrend
    },
    generatedAt: new Date().toISOString()
  };
};

/**
 * Generate processing activities report
 * @param {Object} options - Options for the report
 * @returns {Object} - Processing activities report
 */
const generateProcessingActivitiesReport = (options) => {
  const { period, startDate, endDate, groupBy = 'status' } = options;
  
  // Get date range
  const dateRange = getDateRange(period, startDate, endDate);
  
  // Filter activities by date range
  const activities = filterByDateRange(models.dataProcessingActivities, dateRange);
  
  // Calculate metrics
  const totalActivities = activities.length;
  const activeActivities = activities.filter(a => a.status === 'active').length;
  const inactiveActivities = activities.filter(a => a.status === 'inactive').length;
  const archivedActivities = activities.filter(a => a.status === 'archived').length;
  
  // Calculate DPIA metrics
  const activitiesRequiringDpia = activities.filter(a => a.dpia && a.dpia.required).length;
  const activitiesWithCompletedDpia = activities.filter(a => 
    a.dpia && a.dpia.required && a.dpia.completed
  ).length;
  
  const dpiaCompletionRate = activitiesRequiringDpia > 0 
    ? (activitiesWithCompletedDpia / activitiesRequiringDpia) * 100 
    : 0;
  
  // Group activities
  const groupedActivities = {};
  
  activities.forEach(activity => {
    const groupKey = activity[groupBy] || 'unknown';
    
    if (!groupedActivities[groupKey]) {
      groupedActivities[groupKey] = {
        total: 0,
        active: 0,
        inactive: 0,
        archived: 0,
        requiresDpia: 0,
        completedDpia: 0
      };
    }
    
    groupedActivities[groupKey].total++;
    
    switch (activity.status) {
      case 'active':
        groupedActivities[groupKey].active++;
        break;
      case 'inactive':
        groupedActivities[groupKey].inactive++;
        break;
      case 'archived':
        groupedActivities[groupKey].archived++;
        break;
    }
    
    if (activity.dpia && activity.dpia.required) {
      groupedActivities[groupKey].requiresDpia++;
      
      if (activity.dpia.completed) {
        groupedActivities[groupKey].completedDpia++;
      }
    }
  });
  
  // Calculate DPIA completion rates for each group
  Object.keys(groupedActivities).forEach(key => {
    const group = groupedActivities[key];
    group.dpiaCompletionRate = group.requiresDpia > 0 
      ? (group.completedDpia / group.requiresDpia) * 100 
      : 0;
  });
  
  // Calculate trends (compare with previous period)
  const previousDateRange = {
    start: new Date(new Date(dateRange.start).getTime() - (new Date(dateRange.end) - new Date(dateRange.start))).toISOString(),
    end: dateRange.start
  };
  
  const previousActivities = filterByDateRange(models.dataProcessingActivities, previousDateRange);
  const previousTotalActivities = previousActivities.length;
  
  const activitiesTrend = previousTotalActivities > 0 
    ? ((totalActivities - previousTotalActivities) / previousTotalActivities) * 100 
    : (totalActivities > 0 ? 100 : 0);
  
  return {
    reportType: REPORT_TYPES.PROCESSING_ACTIVITIES,
    dateRange,
    metrics: {
      totalActivities,
      activeActivities,
      inactiveActivities,
      archivedActivities,
      activitiesRequiringDpia,
      activitiesWithCompletedDpia,
      dpiaCompletionRate
    },
    groupedBy: groupBy,
    groupedData: groupedActivities,
    trends: {
      activitiesTrend
    },
    generatedAt: new Date().toISOString()
  };
};

/**
 * Generate compliance status report
 * @param {Object} options - Options for the report
 * @returns {Object} - Compliance status report
 */
const generateComplianceReport = (options) => {
  const { period, startDate, endDate } = options;
  
  // Get date range
  const dateRange = getDateRange(period, startDate, endDate);
  
  // Get all activities
  const activities = models.dataProcessingActivities;
  
  // Calculate overall compliance metrics
  const totalActivities = activities.length;
  
  // DPIA compliance
  const activitiesRequiringDpia = activities.filter(a => a.dpia && a.dpia.required).length;
  const activitiesWithCompletedDpia = activities.filter(a => 
    a.dpia && a.dpia.required && a.dpia.completed
  ).length;
  
  const dpiaComplianceRate = activitiesRequiringDpia > 0 
    ? (activitiesWithCompletedDpia / activitiesRequiringDpia) * 100 
    : 100;
  
  // Lawful basis compliance
  const activitiesWithLawfulBasis = activities.filter(a => a.legalBasis).length;
  const lawfulBasisComplianceRate = totalActivities > 0 
    ? (activitiesWithLawfulBasis / totalActivities) * 100 
    : 100;
  
  // Data retention compliance
  const activitiesWithRetentionPeriod = activities.filter(a => a.retentionPeriod).length;
  const retentionComplianceRate = totalActivities > 0 
    ? (activitiesWithRetentionPeriod / totalActivities) * 100 
    : 100;
  
  // Security measures compliance
  const activitiesWithSecurityMeasures = activities.filter(a => 
    a.securityMeasures && a.securityMeasures.length > 0
  ).length;
  const securityComplianceRate = totalActivities > 0 
    ? (activitiesWithSecurityMeasures / totalActivities) * 100 
    : 100;
  
  // Calculate overall compliance score
  const overallComplianceScore = (
    dpiaComplianceRate + 
    lawfulBasisComplianceRate + 
    retentionComplianceRate + 
    securityComplianceRate
  ) / 4;
  
  // Get recent data breaches
  const recentBreaches = filterByDateRange(models.dataBreaches, dateRange, 'detectionDate');
  
  // Get recent DSRs
  const recentRequests = filterByDateRange(models.dataSubjectRequests, dateRange, 'requestDate');
  const completedRequests = recentRequests.filter(r => r.status === 'completed').length;
  const dsrCompletionRate = recentRequests.length > 0 
    ? (completedRequests / recentRequests.length) * 100 
    : 100;
  
  // Identify compliance gaps
  const complianceGaps = [];
  
  if (dpiaComplianceRate < 100) {
    complianceGaps.push({
      area: 'DPIA',
      description: 'Some processing activities requiring DPIA do not have completed assessments',
      complianceRate: dpiaComplianceRate,
      affectedActivities: activitiesRequiringDpia - activitiesWithCompletedDpia
    });
  }
  
  if (lawfulBasisComplianceRate < 100) {
    complianceGaps.push({
      area: 'Lawful Basis',
      description: 'Some processing activities do not have a documented lawful basis',
      complianceRate: lawfulBasisComplianceRate,
      affectedActivities: totalActivities - activitiesWithLawfulBasis
    });
  }
  
  if (retentionComplianceRate < 100) {
    complianceGaps.push({
      area: 'Data Retention',
      description: 'Some processing activities do not have defined retention periods',
      complianceRate: retentionComplianceRate,
      affectedActivities: totalActivities - activitiesWithRetentionPeriod
    });
  }
  
  if (securityComplianceRate < 100) {
    complianceGaps.push({
      area: 'Security Measures',
      description: 'Some processing activities do not have documented security measures',
      complianceRate: securityComplianceRate,
      affectedActivities: totalActivities - activitiesWithSecurityMeasures
    });
  }
  
  if (dsrCompletionRate < 100) {
    complianceGaps.push({
      area: 'Data Subject Requests',
      description: 'Some data subject requests have not been completed',
      complianceRate: dsrCompletionRate,
      affectedRequests: recentRequests.length - completedRequests
    });
  }
  
  return {
    reportType: REPORT_TYPES.COMPLIANCE_STATUS,
    dateRange,
    metrics: {
      overallComplianceScore,
      dpiaComplianceRate,
      lawfulBasisComplianceRate,
      retentionComplianceRate,
      securityComplianceRate,
      dsrCompletionRate,
      recentBreachesCount: recentBreaches.length
    },
    complianceGaps,
    generatedAt: new Date().toISOString()
  };
};

/**
 * Generate a report
 * @param {string} reportType - Type of report to generate
 * @param {Object} options - Options for the report
 * @returns {Object} - Generated report
 */
const generateReport = (reportType, options) => {
  switch (reportType) {
    case REPORT_TYPES.DSR_SUMMARY:
      return generateDsrSummary(options);
      
    case REPORT_TYPES.CONSENT_MANAGEMENT:
      return generateConsentReport(options);
      
    case REPORT_TYPES.DATA_BREACH:
      return generateDataBreachReport(options);
      
    case REPORT_TYPES.PROCESSING_ACTIVITIES:
      return generateProcessingActivitiesReport(options);
      
    case REPORT_TYPES.COMPLIANCE_STATUS:
      return generateComplianceReport(options);
      
    default:
      throw new Error(`Unsupported report type: ${reportType}`);
  }
};

/**
 * Get dashboard metrics
 * @returns {Object} - Dashboard metrics
 */
const getDashboardMetrics = () => {
  // Get date ranges
  const last30Days = getDateRange(TIME_PERIODS.LAST_30_DAYS);
  const last90Days = getDateRange(TIME_PERIODS.LAST_90_DAYS);
  
  // DSR metrics
  const recentRequests = filterByDateRange(models.dataSubjectRequests, last30Days, 'requestDate');
  const pendingRequests = recentRequests.filter(r => r.status === 'pending').length;
  const completedRequests = recentRequests.filter(r => r.status === 'completed').length;
  
  // Consent metrics
  const recentConsents = filterByDateRange(models.consentRecords, last30Days);
  const activeConsents = recentConsents.filter(r => r.status === 'active').length;
  const withdrawnConsents = recentConsents.filter(r => r.status === 'withdrawn').length;
  
  // Data breach metrics
  const recentBreaches = filterByDateRange(models.dataBreaches, last90Days, 'detectionDate');
  const openBreaches = recentBreaches.filter(b => b.status === 'open').length;
  
  // Processing activities metrics
  const activities = models.dataProcessingActivities;
  const activitiesRequiringDpia = activities.filter(a => a.dpia && a.dpia.required).length;
  const activitiesWithCompletedDpia = activities.filter(a => 
    a.dpia && a.dpia.required && a.dpia.completed
  ).length;
  
  // Calculate compliance score
  const dpiaComplianceRate = activitiesRequiringDpia > 0 
    ? (activitiesWithCompletedDpia / activitiesRequiringDpia) * 100 
    : 100;
  
  const dsrCompletionRate = recentRequests.length > 0 
    ? (completedRequests / recentRequests.length) * 100 
    : 100;
  
  const overallComplianceScore = (dpiaComplianceRate + dsrCompletionRate) / 2;
  
  return {
    dsrMetrics: {
      pendingRequests,
      completedRequests,
      totalRequests: recentRequests.length,
      completionRate: dsrCompletionRate
    },
    consentMetrics: {
      activeConsents,
      withdrawnConsents,
      totalConsents: recentConsents.length,
      consentRate: recentConsents.length > 0 
        ? (activeConsents / recentConsents.length) * 100 
        : 0
    },
    dataBreachMetrics: {
      openBreaches,
      totalBreaches: recentBreaches.length
    },
    complianceMetrics: {
      overallComplianceScore,
      dpiaComplianceRate,
      activitiesRequiringDpia,
      activitiesWithCompletedDpia
    },
    dateRanges: {
      dsrAndConsent: last30Days,
      dataBreaches: last90Days
    },
    generatedAt: new Date().toISOString()
  };
};

module.exports = {
  TIME_PERIODS,
  REPORT_TYPES,
  getDateRange,
  filterByDateRange,
  generateReport,
  getDashboardMetrics
};
