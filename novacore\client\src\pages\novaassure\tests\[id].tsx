/**
 * Control Test Detail Page
 * 
 * This page displays the details of a specific control test and allows
 * users to run the test, view test history, and manage test configuration.
 */

import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { useAuth } from '../../../contexts/AuthContext';
import ProtectedRoute from '../../../components/ProtectedRoute';
import useNovaAssureApi from '../../../hooks/useNovaAssureApi';
import useFetch from '../../../hooks/useFetch';
import LoadingSpinner from '../../../components/ui/loading-spinner';
import ErrorDisplay from '../../../components/ui/error-display';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '../../../components/ui/tabs';
import { 
  ArrowLeft, 
  Play, 
  Clock, 
  Calendar, 
  FileText, 
  Settings, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Edit,
  Trash,
  Download,
  Upload,
  Code
} from 'lucide-react';
import { RecentTestRunsList } from '../../../components/novaassure/RecentTestRunsList';

export default function ControlTestDetailPage() {
  return (
    <ProtectedRoute>
      <ControlTestDetailContent />
    </ProtectedRoute>
  );
}

function ControlTestDetailContent() {
  const router = useRouter();
  const { id } = router.query;
  const { logout } = useAuth();
  const { api, callApi } = useNovaAssureApi();
  const [error, setError] = useState<Error | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isRunningTest, setIsRunningTest] = useState(false);
  
  // Fetch control test details
  const { 
    data: test, 
    loading: testLoading,
    error: testError,
    refetch: refetchTest
  } = useFetch(
    async () => await callApi(() => api.getControlTestById(id as string)),
    { 
      dependencies: [api, id],
      immediate: !!id // Only fetch when ID is available
    }
  );
  
  // Fetch test runs for this test
  const { 
    data: testRuns = [], 
    loading: testRunsLoading,
    error: testRunsError,
    refetch: refetchTestRuns
  } = useFetch(
    async () => await callApi(() => api.getTestRunsByTest(id as string)),
    { 
      dependencies: [api, id],
      immediate: !!id // Only fetch when ID is available
    }
  );
  
  // Combine all loading states
  const loading = testLoading || testRunsLoading;
  
  // Combine all errors
  React.useEffect(() => {
    const firstError = testError || testRunsError;
    if (firstError) {
      setError(firstError);
    } else {
      setError(null);
    }
  }, [testError, testRunsError]);
  
  const handleLogout = () => {
    logout();
    router.push('/login');
  };
  
  const handleDismissError = () => {
    setError(null);
  };
  
  const handleRunTest = async () => {
    if (!id) return;
    
    setIsRunningTest(true);
    try {
      await callApi(() => api.startTestRun(id as string));
      // Refetch test and test runs after starting a new test run
      await refetchTest();
      await refetchTestRuns();
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to start test run'));
    } finally {
      setIsRunningTest(false);
    }
  };
  
  // Format date to readable format
  const formatDate = (dateString: string | null): string => {
    if (!dateString) return 'Never';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    }) + ' ' + date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button 
            variant="outline" 
            size="sm" 
            className="mr-4"
            onClick={() => router.push('/novaassure')}
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">Control Test Details</h1>
        </div>
      </div>
      
      {error && (
        <div className="mb-6">
          <ErrorDisplay 
            message="Error loading test details" 
            details={error.message}
            severity="error"
            onDismiss={handleDismissError}
          />
        </div>
      )}
      
      {loading ? (
        <LoadingSpinner size="large" text="Loading test details..." fullPage />
      ) : !test ? (
        <div className="text-center py-12">
          <h2 className="text-xl font-medium text-gray-600">Test not found</h2>
          <p className="mt-2 text-gray-500">The requested test could not be found or you don't have permission to view it.</p>
          <Button 
            className="mt-6"
            onClick={() => router.push('/novaassure')}
          >
            Return to Dashboard
          </Button>
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm mb-6">
            <div className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <h2 className="text-xl font-bold">{test.name}</h2>
                  <p className="text-gray-600 mt-1">{test.description}</p>
                  
                  <div className="flex flex-wrap gap-2 mt-3">
                    <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                      {test.frameworkName}
                    </span>
                    <span className="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">
                      {test.controlName}
                    </span>
                    <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">
                      {test.type.charAt(0).toUpperCase() + test.type.slice(1)}
                    </span>
                    <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">
                      {test.frequency.charAt(0).toUpperCase() + test.frequency.slice(1)}
                    </span>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      test.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : test.status === 'draft'
                          ? 'bg-gray-100 text-gray-800'
                          : 'bg-red-100 text-red-800'
                    }`}>
                      {test.status.charAt(0).toUpperCase() + test.status.slice(1)}
                    </span>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => router.push(`/novaassure/tests/${id}/edit`)}
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  <Button 
                    variant="primary" 
                    size="sm"
                    onClick={handleRunTest}
                    disabled={isRunningTest || test.status !== 'active'}
                  >
                    {isRunningTest ? (
                      <>
                        <LoadingSpinner size="small" className="mr-1" />
                        Running...
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-1" />
                        Run Test
                      </>
                    )}
                  </Button>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div className="p-3 bg-gray-50 rounded-md">
                  <div className="text-sm text-gray-500">Last Run</div>
                  <div className="font-medium">{formatDate(test.lastRun)}</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-md">
                  <div className="text-sm text-gray-500">Next Run</div>
                  <div className="font-medium">{formatDate(test.nextRun)}</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-md">
                  <div className="text-sm text-gray-500">Owner</div>
                  <div className="font-medium">{test.owner}</div>
                </div>
              </div>
            </div>
          </div>
          
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="steps">Test Steps</TabsTrigger>
              <TabsTrigger value="history">Test History</TabsTrigger>
              <TabsTrigger value="evidence">Evidence Requirements</TabsTrigger>
              {test.type !== 'manual' && (
                <TabsTrigger value="automation">Automation</TabsTrigger>
              )}
            </TabsList>
            
            <TabsContent value="overview" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Test Overview</CardTitle>
                  <CardDescription>Details about this control test</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Description</h3>
                      <p className="mt-1">{test.description}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Framework</h3>
                      <p className="mt-1">{test.frameworkName}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Control</h3>
                      <p className="mt-1">{test.controlName}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Risk Level</h3>
                      <p className="mt-1">{test.riskLevel.charAt(0).toUpperCase() + test.riskLevel.slice(1)}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Tags</h3>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {test.tags.map((tag, index) => (
                          <span key={index} className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">
                            {tag}
                          </span>
                        ))}
                        {test.tags.length === 0 && <p className="text-gray-500">No tags</p>}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Recent Test Runs</CardTitle>
                  <CardDescription>Latest executions of this test</CardDescription>
                </CardHeader>
                <CardContent>
                  {testRunsLoading ? (
                    <div className="h-48 flex items-center justify-center">
                      <LoadingSpinner size="medium" text="Loading test runs..." />
                    </div>
                  ) : testRuns.length === 0 ? (
                    <div className="h-48 flex items-center justify-center text-gray-500">
                      No test runs available
                    </div>
                  ) : (
                    <RecentTestRunsList testRuns={testRuns.slice(0, 5)} />
                  )}
                </CardContent>
                <CardFooter className="justify-end">
                  <Link href={`/novaassure/tests/${id}/history`}>
                    <Button variant="outline" size="sm">
                      View All Test Runs
                      <ArrowLeft className="h-4 w-4 ml-1 rotate-180" />
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            </TabsContent>
            
            <TabsContent value="steps" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Test Steps</CardTitle>
                  <CardDescription>Steps to execute this test</CardDescription>
                </CardHeader>
                <CardContent>
                  {test.testSteps.length === 0 ? (
                    <div className="h-48 flex items-center justify-center text-gray-500">
                      No test steps defined
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {test.testSteps.map((step, index) => (
                        <div key={step.id} className="p-4 border rounded-md bg-gray-50">
                          <div className="flex items-start">
                            <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 text-blue-800 mr-3">
                              {index + 1}
                            </div>
                            <div className="flex-grow">
                              <h3 className="font-medium">{step.name}</h3>
                              <p className="text-sm text-gray-600 mt-1">{step.description}</p>
                              
                              <div className="mt-3">
                                <h4 className="text-xs font-medium text-gray-500">Expected Result</h4>
                                <p className="text-sm mt-1">{step.expectedResult}</p>
                              </div>
                              
                              {step.type === 'automated' && step.automationScript && (
                                <div className="mt-3">
                                  <h4 className="text-xs font-medium text-gray-500">Automation</h4>
                                  <div className="mt-1 p-2 bg-gray-100 rounded text-xs font-mono overflow-x-auto">
                                    {step.automationScript.length > 100 
                                      ? `${step.automationScript.substring(0, 100)}...` 
                                      : step.automationScript}
                                  </div>
                                </div>
                              )}
                            </div>
                            <div className="flex-shrink-0 ml-3">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                step.type === 'manual' 
                                  ? 'bg-yellow-100 text-yellow-800' 
                                  : 'bg-blue-100 text-blue-800'
                              }`}>
                                {step.type.charAt(0).toUpperCase() + step.type.slice(1)}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
                <CardFooter className="justify-end">
                  <Link href={`/novaassure/tests/${id}/edit-steps`}>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-1" />
                      Edit Steps
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            </TabsContent>
            
            <TabsContent value="history" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Test History</CardTitle>
                  <CardDescription>All executions of this test</CardDescription>
                </CardHeader>
                <CardContent>
                  {testRunsLoading ? (
                    <div className="h-48 flex items-center justify-center">
                      <LoadingSpinner size="medium" text="Loading test history..." />
                    </div>
                  ) : testRuns.length === 0 ? (
                    <div className="h-48 flex items-center justify-center text-gray-500">
                      No test history available
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {testRuns.map((testRun) => {
                        let statusIcon;
                        let statusColor;
                        
                        if (testRun.status === 'pending' || testRun.status === 'in_progress') {
                          statusIcon = <Clock className="h-4 w-4" />;
                          statusColor = 'text-blue-600';
                        } else if (testRun.status === 'cancelled') {
                          statusIcon = <AlertCircle className="h-4 w-4" />;
                          statusColor = 'text-gray-600';
                        } else if (testRun.result === 'pass') {
                          statusIcon = <CheckCircle className="h-4 w-4" />;
                          statusColor = 'text-green-600';
                        } else if (testRun.result === 'fail') {
                          statusIcon = <XCircle className="h-4 w-4" />;
                          statusColor = 'text-red-600';
                        } else {
                          statusIcon = <AlertCircle className="h-4 w-4" />;
                          statusColor = 'text-yellow-600';
                        }
                        
                        return (
                          <div key={testRun._id} className="p-4 border rounded-md bg-gray-50">
                            <div className="flex justify-between items-start">
                              <div>
                                <div className="text-sm text-gray-500">
                                  {formatDate(testRun.startTime)}
                                </div>
                                <div className="font-medium">
                                  {testRun.executedBy}
                                </div>
                              </div>
                              <div className={`flex items-center ${statusColor}`}>
                                {statusIcon}
                                <span className="ml-1 font-medium">
                                  {testRun.status === 'pending' ? 'Pending' :
                                   testRun.status === 'in_progress' ? 'In Progress' :
                                   testRun.status === 'cancelled' ? 'Cancelled' :
                                   testRun.result === 'pass' ? 'Passed' :
                                   testRun.result === 'fail' ? 'Failed' : 'Inconclusive'}
                                </span>
                              </div>
                            </div>
                            
                            <div className="mt-3 flex justify-between items-center">
                              <div className="text-sm">
                                {testRun.endTime ? (
                                  <span className="text-gray-500">
                                    Duration: {Math.round((new Date(testRun.endTime).getTime() - new Date(testRun.startTime).getTime()) / 1000)} seconds
                                  </span>
                                ) : (
                                  <span className="text-blue-500">In progress</span>
                                )}
                              </div>
                              <Link href={`/novaassure/test-runs/${testRun._id}`}>
                                <Button variant="outline" size="sm">
                                  View Details
                                </Button>
                              </Link>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="evidence" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Evidence Requirements</CardTitle>
                  <CardDescription>Required evidence for this test</CardDescription>
                </CardHeader>
                <CardContent>
                  {test.evidenceRequirements.length === 0 ? (
                    <div className="h-48 flex items-center justify-center text-gray-500">
                      No evidence requirements defined
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {test.evidenceRequirements.map((evidence) => (
                        <div key={evidence.id} className="p-4 border rounded-md bg-gray-50">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="font-medium">{evidence.name}</h3>
                              <p className="text-sm text-gray-600 mt-1">{evidence.description}</p>
                              
                              <div className="mt-3 grid grid-cols-2 gap-4">
                                <div>
                                  <h4 className="text-xs font-medium text-gray-500">Type</h4>
                                  <p className="text-sm mt-1">
                                    {evidence.type.charAt(0).toUpperCase() + evidence.type.slice(1)}
                                  </p>
                                </div>
                                <div>
                                  <h4 className="text-xs font-medium text-gray-500">Required</h4>
                                  <p className="text-sm mt-1">
                                    {evidence.required ? 'Yes' : 'No'}
                                  </p>
                                </div>
                              </div>
                              
                              <div className="mt-3">
                                <h4 className="text-xs font-medium text-gray-500">Accepted Formats</h4>
                                <div className="flex flex-wrap gap-2 mt-1">
                                  {evidence.format.map((format, index) => (
                                    <span key={index} className="px-2 py-1 text-xs rounded-full bg-gray-200 text-gray-800">
                                      {format}
                                    </span>
                                  ))}
                                </div>
                              </div>
                              
                              <div className="mt-3">
                                <h4 className="text-xs font-medium text-gray-500">Retention Period</h4>
                                <p className="text-sm mt-1">
                                  {evidence.retentionPeriod} days
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
                <CardFooter className="justify-end">
                  <Link href={`/novaassure/tests/${id}/edit-evidence`}>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-1" />
                      Edit Evidence Requirements
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            </TabsContent>
            
            {test.type !== 'manual' && (
              <TabsContent value="automation" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Automation Configuration</CardTitle>
                    <CardDescription>Automation settings for this test</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {!test.automationConfig ? (
                      <div className="h-48 flex items-center justify-center text-gray-500">
                        No automation configuration defined
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">Platform</h3>
                            <p className="mt-1">{test.automationConfig.platform}</p>
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">Script Type</h3>
                            <p className="mt-1">{test.automationConfig.scriptType}</p>
                          </div>
                        </div>
                        
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Script Content</h3>
                          <div className="mt-1 p-3 bg-gray-100 rounded font-mono text-sm overflow-x-auto">
                            <pre>{test.automationConfig.scriptContent}</pre>
                          </div>
                        </div>
                        
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Parameters</h3>
                          {Object.keys(test.automationConfig.parameters).length === 0 ? (
                            <p className="mt-1 text-gray-500">No parameters defined</p>
                          ) : (
                            <div className="mt-1 grid grid-cols-2 gap-2">
                              {Object.entries(test.automationConfig.parameters).map(([key, value]) => (
                                <div key={key} className="p-2 bg-gray-50 rounded">
                                  <span className="font-medium">{key}:</span> {value}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">Timeout</h3>
                            <p className="mt-1">{test.automationConfig.timeout} seconds</p>
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">Retry Count</h3>
                            <p className="mt-1">{test.automationConfig.retryCount}</p>
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">Retry Delay</h3>
                            <p className="mt-1">{test.automationConfig.retryDelay} seconds</p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">Success Criteria</h3>
                            <p className="mt-1">{test.automationConfig.successCriteria}</p>
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">Failure Criteria</h3>
                            <p className="mt-1">{test.automationConfig.failureCriteria}</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                  <CardFooter className="justify-end">
                    <Link href={`/novaassure/tests/${id}/edit-automation`}>
                      <Button variant="outline" size="sm">
                        <Code className="h-4 w-4 mr-1" />
                        Edit Automation
                      </Button>
                    </Link>
                  </CardFooter>
                </Card>
              </TabsContent>
            )}
          </Tabs>
        </>
      )}
    </div>
  );
}
