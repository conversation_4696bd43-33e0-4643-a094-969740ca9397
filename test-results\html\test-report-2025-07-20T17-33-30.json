{"numFailedTestSuites": 1, "numFailedTests": 0, "numPassedTestSuites": 1, "numPassedTests": 5, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 1, "numTodoTests": 0, "numTotalTestSuites": 2, "numTotalTests": 5, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1753032799947, "success": false, "testResults": [{"leaks": false, "numFailingTests": 0, "numPassingTests": 5, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1753032808680, "runtime": 4467, "slow": false, "start": 1753032804213}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\src\\novaascend\\simulation.test.js", "testResults": [{"ancestorTitles": [], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "Policy adaptation in NovaCortex", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "Policy adaptation in NovaCortex"}, {"ancestorTitles": [], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "System coherence under load", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "System coherence under load"}, {"ancestorTitles": [], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Performance scaling in NovaLift", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "Performance scaling in NovaLift"}, {"ancestorTitles": [], "duration": 15, "failureDetails": [], "failureMessages": [], "fullName": "Error handling robustness", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "Error handling robustness"}, {"ancestorTitles": [], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "E2E workflow", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "E2E workflow"}], "failureMessage": null}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    ReferenceError: TextEncoder is not defined\n\n    \u001b[0m \u001b[90m 2 |\u001b[39m \u001b[90m// Basic tests for NovaAscend API endpoints\u001b[39m\n     \u001b[90m 3 |\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 4 |\u001b[39m \u001b[36mconst\u001b[39m request \u001b[33m=\u001b[39m require(\u001b[32m'supertest'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m   |\u001b[39m                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 5 |\u001b[39m \u001b[36mconst\u001b[39m app \u001b[33m=\u001b[39m require(\u001b[32m'./throne-api'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 6 |\u001b[39m\n     \u001b[90m 7 |\u001b[39m describe(\u001b[32m'NovaAscend API'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\n\n      \u001b[2mat utf8ToBytes (\u001b[22msrc/novaascend/node_modules/@noble/hashes/src/utils.ts\u001b[2m:217:29)\u001b[22m\n      \u001b[2mat toBytes (\u001b[22msrc/novaascend/node_modules/@noble/hashes/src/utils.ts\u001b[2m:236:40)\u001b[22m\n      \u001b[2mat hashC (\u001b[22msrc/novaascend/node_modules/@noble/hashes/src/utils.ts\u001b[2m:342:63)\u001b[22m\n      \u001b[2mat hash (\u001b[22msrc/novaascend/node_modules/@paralleldrive/cuid2/src/index.js\u001b[2m:34:22)\u001b[22m\n      \u001b[2mat createFingerprint (\u001b[22msrc/novaascend/node_modules/@paralleldrive/cuid2/src/index.js\u001b[2m:63:10)\u001b[22m\n      \u001b[2mat init (\u001b[22msrc/novaascend/node_modules/@paralleldrive/cuid2/src/index.js\u001b[2m:81:17)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22msrc/novaascend/node_modules/@paralleldrive/cuid2/src/index.js\u001b[2m:101:18)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22msrc/novaascend/node_modules/@paralleldrive/cuid2/index.js\u001b[2m:1:152)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22msrc/novaascend/node_modules/formidable/dist/index.cjs\u001b[2m:8:13)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22msrc/novaascend/node_modules/superagent/src/node/index.js\u001b[2m:16:20)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[0m\u001b[36msrc/novaascend/node_modules/supertest/lib/test.js\u001b[39m\u001b[0m\u001b[2m:12:21)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22msrc/novaascend/node_modules/supertest/index.js\u001b[2m:13:14)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36msrc/novaascend/api.test.js\u001b[39m\u001b[0m\u001b[2m:4:17)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "TextEncoder is not defined", "testFilePath": "D:\\novafuse-api-superstore\\src\\novaascend\\api.test.js", "testResults": []}], "wasInterrupted": false}