/**
 * Performance Analytics Dashboard
 *
 * A comprehensive dashboard for monitoring and analyzing performance metrics.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import {
  DashboardCard,
  TabPanel,
  ResponsiveLayout,
  MetricsCard,
  ChartCard,
  StatusIndicator,
  PerformanceMonitorPanel,
  NetworkPerformancePanel,
  ThemeSelector,
  SkipLink
} from '../components';
import { ThemeProvider } from '../theme';
import { PreferencesProvider } from '../preferences';
import { OfflineProvider } from '../offline';
import { usePerformance, performanceMonitor } from '../performance';
import { useI18n } from '../i18n';
import { useAccessibility } from '../accessibility';

// Import chart components
import {
  LineChart,
  BarChart,
  AreaChart,
  ResponsiveContainer,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  Line,
  Bar,
  Area
} from 'recharts';

/**
 * Performance Analytics Dashboard component
 *
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Performance Analytics Dashboard component
 */
const PerformanceAnalyticsDashboard = ({
  novaConnect,
  novaShield,
  novaTrack,
  enableLogging = false
}) => {
  // Hooks
  const { translate } = useI18n();
  const { settings } = useAccessibility();
  const { measureOperation } = usePerformance('PerformanceAnalyticsDashboard');

  // State
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [metrics, setMetrics] = useState({
    renders: {},
    operations: {},
    resources: {},
    memory: []
  });
  const [activeTab, setActiveTab] = useState('overview');
  const [updateInterval, setUpdateInterval] = useState(1000);
  const [autoUpdate, setAutoUpdate] = useState(true);
  const [historicalData, setHistoricalData] = useState({
    renders: [],
    operations: [],
    memory: [],
    network: []
  });
  const [alerts, setAlerts] = useState([]);
  const [performanceScore, setPerformanceScore] = useState(100);

  // Start monitoring on mount
  useEffect(() => {
    handleStartMonitoring();

    return () => {
      handleStopMonitoring();
    };
  }, []);

  // Update metrics periodically
  useEffect(() => {
    if (!autoUpdate || !isMonitoring) return;

    const intervalId = setInterval(() => {
      updateMetricsNow();
    }, updateInterval);

    return () => {
      clearInterval(intervalId);
    };
  }, [autoUpdate, updateInterval, isMonitoring]);

  // Start monitoring
  const handleStartMonitoring = useCallback(() => {
    performanceMonitor.startMonitoring();
    setIsMonitoring(true);

    if (enableLogging) {
      console.log('Performance monitoring started');
    }
  }, [enableLogging]);

  // Stop monitoring
  const handleStopMonitoring = useCallback(() => {
    performanceMonitor.stopMonitoring();
    setIsMonitoring(false);

    if (enableLogging) {
      console.log('Performance monitoring stopped');
    }
  }, [enableLogging]);

  // Toggle monitoring
  const toggleMonitoring = useCallback(() => {
    if (isMonitoring) {
      handleStopMonitoring();
    } else {
      handleStartMonitoring();
    }
  }, [isMonitoring, handleStartMonitoring, handleStopMonitoring]);

  // Update metrics now
  const updateMetricsNow = useCallback(() => {
    const currentMetrics = performanceMonitor.getMetrics();
    setMetrics(currentMetrics);

    // Update historical data
    setHistoricalData(prevData => {
      const timestamp = Date.now();

      // Calculate total render time
      const totalRenderTime = Object.values(currentMetrics.renders).reduce(
        (sum, metric) => sum + metric.avgTime,
        0
      );

      // Calculate total operation time
      const totalOperationTime = Object.values(currentMetrics.operations).reduce(
        (sum, metric) => sum + metric.avgTime,
        0
      );

      // Get memory usage
      const memoryUsage = currentMetrics.memory.length > 0
        ? currentMetrics.memory[currentMetrics.memory.length - 1].usedPercentage
        : 0;

      // Add new data points
      return {
        renders: [...prevData.renders, { timestamp, value: totalRenderTime }].slice(-50),
        operations: [...prevData.operations, { timestamp, value: totalOperationTime }].slice(-50),
        memory: [...prevData.memory, { timestamp, value: memoryUsage }].slice(-50),
        network: prevData.network
      };
    });

    // Check for performance issues
    checkPerformanceIssues(currentMetrics);
  }, []);

  // Check for performance issues
  const checkPerformanceIssues = useCallback((currentMetrics) => {
    const newAlerts = [];
    let score = 100;

    // Check for slow components
    Object.entries(currentMetrics.renders).forEach(([componentName, metric]) => {
      if (metric.avgTime > 50) {
        newAlerts.push({
          id: `slow-component-${componentName}`,
          type: 'warning',
          message: `Slow component: ${componentName} (${metric.avgTime.toFixed(2)}ms)`,
          timestamp: Date.now()
        });
        score -= 5;
      }
    });

    // Check for slow operations
    Object.entries(currentMetrics.operations).forEach(([operationName, metric]) => {
      if (metric.avgTime > 100) {
        newAlerts.push({
          id: `slow-operation-${operationName}`,
          type: 'warning',
          message: `Slow operation: ${operationName} (${metric.avgTime.toFixed(2)}ms)`,
          timestamp: Date.now()
        });
        score -= 5;
      }
    });

    // Check for high memory usage
    if (currentMetrics.memory.length > 0) {
      const latestMemory = currentMetrics.memory[currentMetrics.memory.length - 1];
      if (latestMemory.usedPercentage > 80) {
        newAlerts.push({
          id: 'high-memory-usage',
          type: 'error',
          message: `High memory usage: ${latestMemory.usedPercentage.toFixed(2)}%`,
          timestamp: Date.now()
        });
        score -= 10;
      }
    }

    // Update alerts and score
    if (newAlerts.length > 0) {
      setAlerts(prevAlerts => {
        // Combine alerts, removing duplicates by id
        const combinedAlerts = [...prevAlerts, ...newAlerts];
        const uniqueAlerts = combinedAlerts.filter((alert, index, self) =>
          index === self.findIndex(a => a.id === alert.id)
        );

        // Sort by timestamp (newest first) and limit to 10
        return uniqueAlerts
          .sort((a, b) => b.timestamp - a.timestamp)
          .slice(0, 10);
      });
    }

    setPerformanceScore(Math.max(0, score));
  }, []);

  // Format timestamp
  const formatTimestamp = useCallback((timestamp) => {
    return new Date(timestamp).toLocaleTimeString();
  }, []);

  // Format time in ms
  const formatTime = useCallback((time) => {
    if (time === Infinity) return 'N/A';
    if (time === 0) return '0ms';

    if (time < 1) {
      return `${(time * 1000).toFixed(2)}μs`;
    } else if (time < 1000) {
      return `${time.toFixed(2)}ms`;
    } else {
      return `${(time / 1000).toFixed(2)}s`;
    }
  }, []);

  // Format bytes
  const formatBytes = useCallback((bytes) => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  }, []);

  // Prepare chart data
  const chartData = useMemo(() => {
    return historicalData.renders.map((render, index) => {
      const memory = historicalData.memory[index] || { value: 0 };
      const operation = historicalData.operations[index] || { value: 0 };

      return {
        name: formatTimestamp(render.timestamp),
        renderTime: render.value,
        operationTime: operation.value,
        memoryUsage: memory.value
      };
    });
  }, [historicalData, formatTimestamp]);

  // Render performance score color
  const getScoreColor = useCallback((score) => {
    if (score >= 90) return 'text-success';
    if (score >= 70) return 'text-warning';
    return 'text-error';
  }, []);

  // Render alert icon
  const getAlertIcon = useCallback((type) => {
    switch (type) {
      case 'error':
        return '🔴';
      case 'warning':
        return '🟠';
      case 'info':
        return '🔵';
      default:
        return '⚪';
    }
  }, []);

  return (
    <ThemeProvider>
      <PreferencesProvider>
        <OfflineProvider>
          <div className="performance-analytics-dashboard">
            {/* Skip link for accessibility */}
            <SkipLink targetId="main-content" />

            {/* Header */}
            <header className="p-4 bg-background border-b border-divider">
              <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold text-textPrimary">
                  {translate('performance.analyticsTitle', 'Performance Analytics Dashboard')}
                </h1>

                <div className="flex items-center space-x-4">
                  <ThemeSelector />

                  <button
                    className={`px-4 py-2 rounded-md ${isMonitoring ? 'bg-error text-white' : 'bg-success text-white'}`}
                    onClick={toggleMonitoring}
                    aria-label={isMonitoring ? 'Stop monitoring' : 'Start monitoring'}
                  >
                    {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
                  </button>
                </div>
              </div>
            </header>

            {/* Main content */}
            <main id="main-content" className="p-6" tabIndex="-1">
              {/* Performance overview */}
              <section className="mb-8">
                <h2 className="text-xl font-semibold mb-4 text-textPrimary">
                  {translate('performance.overviewTitle', 'Performance Overview')}
                </h2>

                <ResponsiveLayout
                  layouts={{
                    xs: 1,
                    sm: 2,
                    md: 4,
                    lg: 4,
                    xl: 4
                  }}
                  gap={4}
                >
                  {/* Performance Score */}
                  <MetricsCard
                    title={translate('performance.scoreTitle', 'Performance Score')}
                    metrics={[
                      {
                        label: translate('performance.overallScore', 'Overall Score'),
                        value: performanceScore,
                        suffix: '/100',
                        color: getScoreColor(performanceScore)
                      }
                    ]}
                  />

                  {/* Render Metrics */}
                  <MetricsCard
                    title={translate('performance.renderMetrics', 'Render Metrics')}
                    metrics={[
                      {
                        label: translate('performance.components', 'Components'),
                        value: Object.keys(metrics.renders).length
                      },
                      {
                        label: translate('performance.totalRenders', 'Total Renders'),
                        value: Object.values(metrics.renders).reduce((sum, metric) => sum + metric.count, 0)
                      }
                    ]}
                  />

                  {/* Operation Metrics */}
                  <MetricsCard
                    title={translate('performance.operationMetrics', 'Operation Metrics')}
                    metrics={[
                      {
                        label: translate('performance.operations', 'Operations'),
                        value: Object.keys(metrics.operations).length
                      },
                      {
                        label: translate('performance.totalOperations', 'Total Operations'),
                        value: Object.values(metrics.operations).reduce((sum, metric) => sum + metric.count, 0)
                      }
                    ]}
                  />

                  {/* Memory Metrics */}
                  <MetricsCard
                    title={translate('performance.memoryMetrics', 'Memory Metrics')}
                    metrics={[
                      {
                        label: translate('performance.memoryUsage', 'Memory Usage'),
                        value: metrics.memory.length > 0
                          ? metrics.memory[metrics.memory.length - 1].usedPercentage.toFixed(1)
                          : 0,
                        suffix: '%'
                      }
                    ]}
                  />
                </ResponsiveLayout>
              </section>

              {/* Performance charts */}
              <section className="mb-8">
                <h2 className="text-xl font-semibold mb-4 text-textPrimary">
                  {translate('performance.trendsTitle', 'Performance Trends')}
                </h2>

                <ChartCard
                  title={translate('performance.metricsOverTime', 'Performance Metrics Over Time')}
                  height={300}
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="name"
                        tick={{ fontSize: 12 }}
                        interval="preserveStartEnd"
                      />
                      <YAxis
                        yAxisId="left"
                        tick={{ fontSize: 12 }}
                        label={{
                          value: 'Time (ms)',
                          angle: -90,
                          position: 'insideLeft',
                          style: { textAnchor: 'middle' }
                        }}
                      />
                      <YAxis
                        yAxisId="right"
                        orientation="right"
                        domain={[0, 100]}
                        tick={{ fontSize: 12 }}
                        label={{
                          value: 'Memory (%)',
                          angle: 90,
                          position: 'insideRight',
                          style: { textAnchor: 'middle' }
                        }}
                      />
                      <Tooltip formatter={(value) => typeof value === 'number' ? value.toFixed(2) : value} />
                      <Legend />
                      <Line
                        yAxisId="left"
                        type="monotone"
                        dataKey="renderTime"
                        name="Render Time (ms)"
                        stroke="#8884d8"
                        activeDot={{ r: 8 }}
                      />
                      <Line
                        yAxisId="left"
                        type="monotone"
                        dataKey="operationTime"
                        name="Operation Time (ms)"
                        stroke="#82ca9d"
                      />
                      <Line
                        yAxisId="right"
                        type="monotone"
                        dataKey="memoryUsage"
                        name="Memory Usage (%)"
                        stroke="#ffc658"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartCard>
              </section>

              {/* Performance alerts */}
              <section className="mb-8">
                <h2 className="text-xl font-semibold mb-4 text-textPrimary">
                  {translate('performance.alertsTitle', 'Performance Alerts')}
                </h2>

                <DashboardCard>
                  {alerts.length > 0 ? (
                    <ul className="divide-y divide-divider">
                      {alerts.map((alert) => (
                        <li key={alert.id} className="py-3 flex items-start">
                          <span className="mr-2">{getAlertIcon(alert.type)}</span>
                          <div>
                            <p className="text-textPrimary">{alert.message}</p>
                            <p className="text-xs text-textSecondary">
                              {new Date(alert.timestamp).toLocaleString()}
                            </p>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-textSecondary p-4">
                      {translate('performance.noAlerts', 'No performance alerts detected')}
                    </p>
                  )}
                </DashboardCard>
              </section>

              {/* Network performance monitor */}
              <section className="mb-8">
                <h2 className="text-xl font-semibold mb-4 text-textPrimary">
                  {translate('performance.networkTitle', 'Network Performance Monitor')}
                </h2>

                <NetworkPerformancePanel startMonitoringOnMount={isMonitoring} />
              </section>

              {/* Detailed performance monitor */}
              <section>
                <h2 className="text-xl font-semibold mb-4 text-textPrimary">
                  {translate('performance.detailedTitle', 'Detailed Performance Monitor')}
                </h2>

                <PerformanceMonitorPanel startMonitoringOnMount={false} />
              </section>
            </main>
          </div>
        </OfflineProvider>
      </PreferencesProvider>
    </ThemeProvider>
  );
};

PerformanceAnalyticsDashboard.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  enableLogging: PropTypes.bool
};

export default PerformanceAnalyticsDashboard;
