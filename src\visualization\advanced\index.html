<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comphyological Tensor Core - Advanced Visualization</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background-color: #1a237e;
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            border-radius: 5px;
        }

        h1 {
            margin: 0;
            font-size: 24px;
        }

        .visualization-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .visualization-container {
                grid-template-columns: 1fr;
            }
        }

        .visualization-card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .visualization-header {
            background-color: #3f51b5;
            color: white;
            padding: 10px 15px;
            font-weight: bold;
        }

        .visualization-content {
            height: 400px;
            position: relative;
        }

        .controls {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .control-row {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }

        .control-row label {
            margin-bottom: 0;
            flex: 1;
        }

        input[type="range"] {
            flex: 3;
        }

        .value-display {
            flex: 1;
            text-align: right;
            font-family: monospace;
        }

        button {
            background-color: #3f51b5;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        button:hover {
            background-color: #303f9f;
        }

        .button-group {
            display: flex;
            gap: 10px;
        }

        .status {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .status-row {
            display: flex;
            margin-bottom: 10px;
        }

        .status-label {
            flex: 1;
            font-weight: bold;
        }

        .status-value {
            flex: 2;
            font-family: monospace;
        }

        .quantum-silence-yes {
            color: #4caf50;
            font-weight: bold;
        }

        .quantum-silence-no {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Comphyological Tensor Core - Advanced Visualization</h1>
        </header>

        <div class="status">
            <div class="status-row">
                <div class="status-label">Comphyon Value:</div>
                <div class="status-value" id="comphyon-value">0.000000</div>
            </div>
            <div class="status-row">
                <div class="status-label">Resonance Frequency:</div>
                <div class="status-value" id="resonance-frequency">396.000000 Hz</div>
            </div>
            <div class="status-row">
                <div class="status-label">Quantum Silence:</div>
                <div class="status-value quantum-silence-no" id="quantum-silence">No</div>
            </div>
            <div class="status-row">
                <div class="status-label">Dominant Engine:</div>
                <div class="status-value" id="dominant-engine">None</div>
            </div>
        </div>

        <div class="visualization-container">
            <div class="visualization-card">
                <div class="visualization-header">3D Tensor Visualization</div>
                <div class="visualization-content" id="tensor-visualization"></div>
            </div>
            <div class="visualization-card">
                <div class="visualization-header">Resonance Spectrogram</div>
                <div class="visualization-content" id="resonance-spectrogram"></div>
            </div>
        </div>

        <div class="visualization-container">
            <div class="visualization-card">
                <div class="visualization-header">Phase Space Visualization</div>
                <div class="visualization-content" id="phase-space"></div>
            </div>
            <div class="visualization-card">
                <div class="visualization-header">Harmonic Pattern Explorer</div>
                <div class="visualization-content" id="harmonic-explorer"></div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>Domain Parameters</label>

                <div class="control-row">
                    <label>CSDE Governance:</label>
                    <input type="range" id="csde-governance" min="0" max="1" step="0.01" value="0.618">
                    <div class="value-display" id="csde-governance-value">0.618</div>
                </div>

                <div class="control-row">
                    <label>CSDE Data Quality:</label>
                    <input type="range" id="csde-data" min="0" max="1" step="0.01" value="0.618">
                    <div class="value-display" id="csde-data-value">0.618</div>
                </div>

                <div class="control-row">
                    <label>CSFE Risk:</label>
                    <input type="range" id="csfe-risk" min="0" max="1" step="0.01" value="0.382">
                    <div class="value-display" id="csfe-risk-value">0.382</div>
                </div>

                <div class="control-row">
                    <label>CSFE Policy Compliance:</label>
                    <input type="range" id="csfe-finance" min="0" max="1" step="0.01" value="0.618">
                    <div class="value-display" id="csfe-finance-value">0.618</div>
                </div>

                <div class="control-row">
                    <label>CSME Trust Factor:</label>
                    <input type="range" id="csme-bio" min="0" max="1" step="0.01" value="0.618">
                    <div class="value-display" id="csme-bio-value">0.618</div>
                </div>

                <div class="control-row">
                    <label>CSME Integrity Factor:</label>
                    <input type="range" id="csme-med" min="0" max="1" step="0.01" value="0.618">
                    <div class="value-display" id="csme-med-value">0.618</div>
                </div>
            </div>

            <div class="button-group">
                <button id="process-button">Process Data</button>
                <button id="reset-button">Reset</button>
                <button id="auto-button">Auto Mode</button>
            </div>
        </div>
    </div>

    <!-- Load Three.js -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/controls/OrbitControls.js"></script>

    <!-- Load visualization scripts -->
    <script src="tensor_3d.js"></script>
    <script src="resonance_spectrogram.js"></script>
    <script src="phase_space.js"></script>
    <script src="harmonic_explorer.js"></script>

    <script>
        // Initialize visualizations when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize 3D Tensor Visualization
            const tensorContainer = document.getElementById('tensor-visualization');
            const tensorVisualizer = new TensorVisualizer(tensorContainer);

            // Initialize Phase Space Visualization
            const phaseSpaceContainer = document.getElementById('phase-space');
            const phaseSpaceVisualizer = new PhaseSpaceVisualizer(phaseSpaceContainer);

            // Initialize Harmonic Explorer
            const harmonicExplorerContainer = document.getElementById('harmonic-explorer');
            const harmonicExplorer = new HarmonicExplorer(harmonicExplorerContainer);

            // Initialize Resonance Spectrogram
            const spectrogramContainer = document.getElementById('resonance-spectrogram');
            const resonanceSpectrogram = new ResonanceSpectrogram(spectrogramContainer);

            // Start visualizations
            resonanceSpectrogram.start();

            // Initialize range input displays
            document.querySelectorAll('input[type="range"]').forEach(input => {
                const display = document.getElementById(`${input.id}-value`);

                input.addEventListener('input', function() {
                    display.textContent = this.value;
                });
            });

            // Process button click handler
            document.getElementById('process-button').addEventListener('click', function() {
                processData();
            });

            // Reset button click handler
            document.getElementById('reset-button').addEventListener('click', function() {
                resetData();
            });

            // Auto mode button click handler
            let autoModeInterval = null;
            document.getElementById('auto-button').addEventListener('click', function() {
                if (autoModeInterval) {
                    clearInterval(autoModeInterval);
                    autoModeInterval = null;
                    this.textContent = 'Auto Mode';
                } else {
                    autoModeInterval = setInterval(processData, 1000);
                    this.textContent = 'Stop Auto';
                }
            });

            // Process data function
            function processData() {
                // Get values from inputs
                const csdeGovernance = parseFloat(document.getElementById('csde-governance').value);
                const csdeData = parseFloat(document.getElementById('csde-data').value);
                const csfeRisk = parseFloat(document.getElementById('csfe-risk').value);
                const csfeFinance = parseFloat(document.getElementById('csfe-finance').value);
                const csmeBio = parseFloat(document.getElementById('csme-bio').value);
                const csmeMed = parseFloat(document.getElementById('csme-med').value);

                // Create tensors
                const csdeTensor = [csdeGovernance, csdeData, 0.5, 0.5];
                const csfeTensor = [csfeRisk, csfeFinance, 0.5, 0.5];
                const csmeTensor = [csmeBio, csmeMed, 0.5, 0.5];

                // Visualize tensors
                tensorVisualizer.visualizeCSDETensor(csdeTensor);
                tensorVisualizer.visualizeCSFETensor(csfeTensor);
                tensorVisualizer.visualizeCSMETensor(csmeTensor);

                // Calculate fused tensor (simplified)
                const fusedTensor = [
                    (csdeTensor[0] + csfeTensor[0] + csmeTensor[0]) / 3,
                    (csdeTensor[1] + csfeTensor[1] + csmeTensor[1]) / 3,
                    (csdeTensor[2] + csfeTensor[2] + csmeTensor[2]) / 3,
                    (csdeTensor[3] + csfeTensor[3] + csmeTensor[3]) / 3
                ];

                tensorVisualizer.visualizeFusedTensor(fusedTensor);

                // Update phase space visualization
                phaseSpaceVisualizer.addPoint({
                    csde: csdeValue,
                    csfe: csfeValue,
                    csme: csmeValue
                });

                // Calculate comphyon value (simplified)
                const comphyon = Math.abs(
                    (csdeGovernance - 0.618) * (csdeData - 0.618) +
                    (csfeRisk - 0.382) * (csfeFinance - 0.618) +
                    (csmeBio - 0.618) * (csmeMed - 0.618)
                );

                // Calculate resonance frequency (simplified)
                const frequency = 396 + comphyon * 100;

                // Calculate amplitude (simplified)
                const amplitude = 1 - comphyon;

                // Determine quantum silence
                const isQuantumSilence = comphyon < 0.01;

                // Determine dominant engine
                let dominantEngine = 'None';
                const csdeValue = (csdeGovernance + csdeData) / 2;
                const csfeValue = (csfeRisk + csfeFinance) / 2;
                const csmeValue = (csmeBio + csmeMed) / 2;

                if (csdeValue > csfeValue && csdeValue > csmeValue && csdeValue > 0.6) {
                    dominantEngine = 'CSDE';
                } else if (csfeValue > csdeValue && csfeValue > csmeValue && csfeValue > 0.6) {
                    dominantEngine = 'CSFE';
                } else if (csmeValue > csdeValue && csmeValue > csfeValue && csmeValue > 0.6) {
                    dominantEngine = 'CSME';
                }

                // Generate harmonics (simplified)
                const harmonics = [];

                if (amplitude > 0.5) {
                    harmonics.push({
                        frequency: frequency * 2,
                        amplitude: amplitude * 0.5,
                        ratio: 2
                    });

                    harmonics.push({
                        frequency: frequency * 3,
                        amplitude: amplitude * 0.3,
                        ratio: 3
                    });

                    harmonics.push({
                        frequency: frequency * 5,
                        amplitude: amplitude * 0.2,
                        ratio: 5
                    });
                }

                // Update resonance spectrogram
                resonanceSpectrogram.update({
                    frequency,
                    amplitude,
                    isQuantumSilence,
                    harmonics
                });

                // Update status display
                document.getElementById('comphyon-value').textContent = comphyon.toFixed(6);
                document.getElementById('resonance-frequency').textContent = `${frequency.toFixed(6)} Hz`;

                const quantumSilenceElement = document.getElementById('quantum-silence');
                quantumSilenceElement.textContent = isQuantumSilence ? 'Yes' : 'No';
                quantumSilenceElement.className = isQuantumSilence ?
                    'status-value quantum-silence-yes' :
                    'status-value quantum-silence-no';

                document.getElementById('dominant-engine').textContent = dominantEngine;
            }

            // Reset data function
            function resetData() {
                // Reset inputs to golden ratio values
                document.getElementById('csde-governance').value = 0.618;
                document.getElementById('csde-governance-value').textContent = 0.618;

                document.getElementById('csde-data').value = 0.618;
                document.getElementById('csde-data-value').textContent = 0.618;

                document.getElementById('csfe-risk').value = 0.382;
                document.getElementById('csfe-risk-value').textContent = 0.382;

                document.getElementById('csfe-finance').value = 0.618;
                document.getElementById('csfe-finance-value').textContent = 0.618;

                document.getElementById('csme-bio').value = 0.618;
                document.getElementById('csme-bio-value').textContent = 0.618;

                document.getElementById('csme-med').value = 0.618;
                document.getElementById('csme-med-value').textContent = 0.618;

                // Process data with reset values
                processData();
            }

            // Initial processing
            processData();
        });
    </script>
</body>
</html>
