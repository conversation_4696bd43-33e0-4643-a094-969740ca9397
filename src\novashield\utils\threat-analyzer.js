/**
 * NovaShield - Threat Analyzer
 * 
 * This utility provides threat analysis functions.
 */

const { createLogger } = require('../../utils/logger');

const logger = createLogger('threat-analyzer');

/**
 * Analyze threats
 * 
 * @param {Array} threats - List of threats
 * @returns {Promise<Array>} - Analyzed threats
 */
async function analyzeThreats(threats) {
  logger.debug('Analyzing threats', { threatCount: threats.length });
  
  if (!threats || threats.length === 0) {
    return [];
  }
  
  // Analyze each threat
  const analyzedThreats = await Promise.all(
    threats.map(async (threat) => {
      return {
        ...threat,
        analysis: await analyzeThreat(threat)
      };
    })
  );
  
  return analyzedThreats;
}

/**
 * Analyze a single threat
 * 
 * @param {Object} threat - Threat object
 * @returns {Promise<Object>} - Threat analysis
 * @private
 */
async function analyzeThreat(threat) {
  logger.debug('Analyzing threat', { threatId: threat.id });
  
  // Calculate risk score
  const riskScore = calculateThreatRiskScore(threat);
  
  // Determine business impact
  const businessImpact = determineThreatBusinessImpact(threat);
  
  // Generate recommended actions
  const recommendedActions = generateRecommendedActions(threat);
  
  return {
    riskScore,
    businessImpact,
    recommendedActions,
    analysisTimestamp: new Date().toISOString()
  };
}

/**
 * Calculate threat risk score
 * 
 * @param {Object} threat - Threat object
 * @returns {number} - Risk score (0-100)
 * @private
 */
function calculateThreatRiskScore(threat) {
  // Base score based on severity
  const severityScores = {
    'critical': 90,
    'high': 75,
    'medium': 50,
    'low': 25,
    'info': 10
  };
  
  let score = severityScores[threat.severity] || 50;
  
  // Adjust based on status
  const statusModifiers = {
    'active': 1.0,
    'investigating': 0.9,
    'mitigated': 0.5,
    'resolved': 0.2,
    'false-positive': 0.1
  };
  
  score *= statusModifiers[threat.status] || 1.0;
  
  // Adjust based on age (newer threats are more concerning)
  const threatAge = Date.now() - new Date(threat.detectedAt).getTime();
  const ageInDays = threatAge / (1000 * 60 * 60 * 24);
  
  if (ageInDays < 7) {
    // Less than a week old
    score *= 1.2;
  } else if (ageInDays > 90) {
    // More than 3 months old
    score *= 0.8;
  }
  
  // Cap score at 100
  return Math.min(Math.round(score), 100);
}

/**
 * Determine threat business impact
 * 
 * @param {Object} threat - Threat object
 * @returns {Object} - Business impact assessment
 * @private
 */
function determineThreatBusinessImpact(threat) {
  // Default impact levels
  const impact = {
    financial: 'low',
    operational: 'low',
    reputational: 'low',
    compliance: 'low'
  };
  
  // Adjust based on threat type
  switch (threat.type) {
    case 'data-breach':
      impact.financial = 'high';
      impact.reputational = 'high';
      impact.compliance = 'high';
      break;
    case 'vulnerability':
      impact.operational = 'medium';
      impact.financial = 'medium';
      break;
    case 'compliance':
      impact.compliance = 'high';
      impact.financial = 'medium';
      break;
    case 'financial':
      impact.financial = 'high';
      impact.operational = 'medium';
      break;
    case 'operational':
      impact.operational = 'high';
      break;
    case 'reputational':
      impact.reputational = 'high';
      break;
  }
  
  // Adjust based on severity
  if (threat.severity === 'critical' || threat.severity === 'high') {
    Object.keys(impact).forEach(key => {
      if (impact[key] === 'medium') {
        impact[key] = 'high';
      } else if (impact[key] === 'low') {
        impact[key] = 'medium';
      }
    });
  }
  
  return impact;
}

/**
 * Generate recommended actions for a threat
 * 
 * @param {Object} threat - Threat object
 * @returns {Array} - Recommended actions
 * @private
 */
function generateRecommendedActions(threat) {
  const actions = [];
  
  // Add default actions based on threat status
  if (threat.status === 'active' || threat.status === 'investigating') {
    actions.push({
      action: 'Investigate threat details',
      priority: 'high',
      description: 'Gather more information about the threat and its potential impact'
    });
    
    actions.push({
      action: 'Contact vendor security team',
      priority: 'high',
      description: 'Notify vendor about the threat and request their assessment'
    });
  }
  
  // Add type-specific actions
  switch (threat.type) {
    case 'data-breach':
      actions.push({
        action: 'Assess data exposure',
        priority: 'high',
        description: 'Determine what data may have been exposed and its sensitivity'
      });
      
      actions.push({
        action: 'Prepare incident response',
        priority: 'high',
        description: 'Activate incident response plan if breach is confirmed'
      });
      
      actions.push({
        action: 'Review legal obligations',
        priority: 'high',
        description: 'Determine if breach notification is required under applicable laws'
      });
      break;
      
    case 'vulnerability':
      actions.push({
        action: 'Apply security patches',
        priority: 'high',
        description: 'Work with vendor to apply security patches as soon as available'
      });
      
      actions.push({
        action: 'Implement compensating controls',
        priority: 'medium',
        description: 'Implement additional security controls to mitigate the vulnerability'
      });
      break;
      
    case 'compliance':
      actions.push({
        action: 'Review compliance requirements',
        priority: 'medium',
        description: 'Review applicable compliance requirements and vendor obligations'
      });
      
      actions.push({
        action: 'Update vendor agreements',
        priority: 'medium',
        description: 'Update vendor agreements to address compliance concerns'
      });
      break;
      
    case 'financial':
      actions.push({
        action: 'Assess business continuity impact',
        priority: 'medium',
        description: 'Determine impact on business continuity if vendor faces financial issues'
      });
      
      actions.push({
        action: 'Develop contingency plan',
        priority: 'medium',
        description: 'Develop contingency plan for vendor service disruption'
      });
      break;
      
    case 'operational':
      actions.push({
        action: 'Review service level agreements',
        priority: 'medium',
        description: 'Review SLAs and ensure vendor is meeting obligations'
      });
      
      actions.push({
        action: 'Enhance monitoring',
        priority: 'medium',
        description: 'Implement enhanced monitoring of vendor services'
      });
      break;
      
    case 'reputational':
      actions.push({
        action: 'Assess PR impact',
        priority: 'medium',
        description: 'Assess potential public relations impact of vendor issues'
      });
      
      actions.push({
        action: 'Prepare communications plan',
        priority: 'medium',
        description: 'Prepare communications plan to address stakeholder concerns'
      });
      break;
  }
  
  // Add general actions
  actions.push({
    action: 'Document threat response',
    priority: 'medium',
    description: 'Document all actions taken in response to the threat'
  });
  
  actions.push({
    action: 'Update risk assessment',
    priority: 'medium',
    description: 'Update vendor risk assessment based on threat information'
  });
  
  return actions;
}

module.exports = {
  analyzeThreats
};
