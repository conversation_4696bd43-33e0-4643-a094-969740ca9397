@echo off
REM Deploy UUFT/CSDE tests to Google Cloud Platform

REM Configuration
set PROJECT_ID=novafuse-csde-testing
set REGION=us-central1
set SERVICE_NAME=uuft-test
set IMAGE_NAME=gcr.io/%PROJECT_ID%/uuft-test

echo === Deploying UUFT/CSDE Tests to GCP ===
echo Project: %PROJECT_ID%
echo Region: %REGION%
echo Service: %SERVICE_NAME%
echo Image: %IMAGE_NAME%

REM Check if gcloud is installed
where gcloud >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: gcloud CLI not found. Please install Google Cloud SDK.
    exit /b 1
)

REM Check if user is logged in
gcloud auth list --filter=status:ACTIVE --format="value(account)" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo You are not logged in to gcloud. Please login:
    gcloud auth login
)

REM Set project
echo Setting project...
gcloud config set project %PROJECT_ID%

REM Enable required APIs
echo Enabling required APIs...
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable artifactregistry.googleapis.com
gcloud services enable cloudscheduler.googleapis.com

REM Build and push Docker image
echo Building and pushing Docker image...
gcloud builds submit --tag %IMAGE_NAME% .

REM Deploy to Cloud Run
echo Deploying to Cloud Run...
gcloud run deploy %SERVICE_NAME% ^
  --image %IMAGE_NAME% ^
  --platform managed ^
  --region %REGION% ^
  --memory 2Gi ^
  --cpu 2 ^
  --timeout 3600 ^
  --no-allow-unauthenticated ^
  --set-env-vars="GOOGLE_CLOUD_PROJECT=%PROJECT_ID%,UUFT_ENVIRONMENT=gcp"

echo Deployment complete!
echo You can manually trigger a test run with:
echo gcloud run services invoke %SERVICE_NAME% --region=%REGION% --data="{}"
echo.
echo View logs with:
echo gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=%SERVICE_NAME%" --limit=50
