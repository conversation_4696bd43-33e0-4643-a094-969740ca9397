1cb085682103d01d3b96958e7e944744
// Mock axios
_getJestObj().mock('axios');
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * NovaFuse Universal API Connector Secure Executor Tests
 * 
 * This module contains tests for the secure connector executor.
 */

const {
  SecureConnectorExecutor
} = require('../../src/executor');
const axios = require('axios');
const MockAdapter = require('axios-mock-adapter');
const mockAxios = new MockAdapter(axios);
describe('SecureConnectorExecutor', () => {
  let connectorRegistry;
  let executor;
  beforeEach(() => {
    // Mock connector registry
    connectorRegistry = {
      getConnector: jest.fn()
    };

    // Create executor
    executor = new SecureConnectorExecutor(connectorRegistry);

    // Reset axios mock
    mockAxios.reset();
  });
  describe('executeConnector', () => {
    test('should execute a connector endpoint successfully', async () => {
      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://api.example.com',
          headers: {
            'Content-Type': 'application/json'
          }
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              sensitive: true
            }
          }
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/test'
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Mock axios response
      mockAxios.onGet('https://api.example.com/test').reply(200, {
        data: 'test'
      });

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {
        auth: {
          apiKey: 'test-api-key'
        }
      });

      // Verify result
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        data: 'test'
      });
      expect(result.statusCode).toBe(200);

      // Verify connector registry was called
      expect(connectorRegistry.getConnector).toHaveBeenCalledWith('test-connector');
    });
    test('should handle connector not found', async () => {
      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(null);

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {});

      // Verify result
      expect(result.success).toBe(false);
      expect(result.error).toBe('Connector test-connector not found');
      expect(result.statusCode).toBe(500);
    });
    test('should handle endpoint not found', async () => {
      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://api.example.com'
        },
        authentication: {
          type: 'API_KEY',
          fields: {}
        },
        endpoints: [{
          id: 'other-endpoint',
          method: 'GET',
          path: '/other'
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {});

      // Verify result
      expect(result.success).toBe(false);
      expect(result.error).toBe('Endpoint test-endpoint not found in connector test-connector');
      expect(result.statusCode).toBe(500);
    });
    test('should handle API error', async () => {
      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://api.example.com',
          headers: {}
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              sensitive: true
            }
          }
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/test'
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Mock axios response
      mockAxios.onGet('https://api.example.com/test').reply(404, {
        error: 'Not found'
      });

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {
        auth: {
          apiKey: 'test-api-key'
        }
      });

      // Verify result
      expect(result.success).toBe(false);
      expect(result.statusCode).toBe(404);
    });
    test('should handle SSRF protection', async () => {
      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'http://localhost:3000',
          headers: {}
        },
        authentication: {
          type: 'NONE',
          fields: {}
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/test'
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {});

      // Verify result
      expect(result.success).toBe(false);
      expect(result.error).toContain('URL blocked by SSRF protection');
    });
    test('should handle path parameters', async () => {
      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://api.example.com',
          headers: {}
        },
        authentication: {
          type: 'NONE',
          fields: {}
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/users/{userId}/posts/{postId}',
          parameters: {
            path: {
              userId: {
                type: 'string',
                required: true
              },
              postId: {
                type: 'string',
                required: true
              }
            }
          }
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Mock axios response
      mockAxios.onGet('https://api.example.com/users/123/posts/456').reply(200, {
        data: 'test'
      });

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {
        path: {
          userId: '123',
          postId: '456'
        }
      });

      // Verify result
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        data: 'test'
      });
    });
    test('should handle query parameters', async () => {
      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://api.example.com',
          headers: {}
        },
        authentication: {
          type: 'NONE',
          fields: {}
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/users',
          parameters: {
            query: {
              page: {
                type: 'integer',
                default: 1
              },
              limit: {
                type: 'integer',
                default: 10
              }
            }
          }
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Mock axios response
      mockAxios.onGet('https://api.example.com/users').reply(config => {
        expect(config.params).toEqual({
          page: 2,
          limit: 20
        });
        return [200, {
          data: 'test'
        }];
      });

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {
        query: {
          page: 2,
          limit: 20
        }
      });

      // Verify result
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        data: 'test'
      });
    });
    test('should handle body parameters', async () => {
      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://api.example.com',
          headers: {}
        },
        authentication: {
          type: 'NONE',
          fields: {}
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'POST',
          path: '/users',
          parameters: {
            body: {
              required: true,
              properties: {
                name: {
                  type: 'string',
                  required: true
                },
                email: {
                  type: 'string',
                  required: true
                }
              }
            }
          }
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Mock axios response
      mockAxios.onPost('https://api.example.com/users').reply(config => {
        expect(JSON.parse(config.data)).toEqual({
          name: 'Test User',
          email: '<EMAIL>'
        });
        return [201, {
          id: 1,
          name: 'Test User',
          email: '<EMAIL>'
        }];
      });

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {
        body: {
          name: 'Test User',
          email: '<EMAIL>'
        }
      });

      // Verify result
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        id: 1,
        name: 'Test User',
        email: '<EMAIL>'
      });
      expect(result.statusCode).toBe(201);
    });
    test('should handle authentication headers', async () => {
      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://api.example.com',
          headers: {}
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              sensitive: true
            }
          }
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/test'
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Mock axios response
      mockAxios.onGet('https://api.example.com/test').reply(config => {
        expect(config.headers['Authorization']).toBe('Bearer test-api-key');
        return [200, {
          data: 'test'
        }];
      });

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {
        auth: {
          apiKey: 'test-api-key'
        }
      });

      // Verify result
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        data: 'test'
      });
    });
    test('should handle custom headers', async () => {
      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://api.example.com',
          headers: {
            'Content-Type': 'application/json'
          }
        },
        authentication: {
          type: 'NONE',
          fields: {}
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/test'
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Mock axios response
      mockAxios.onGet('https://api.example.com/test').reply(config => {
        expect(config.headers['Content-Type']).toBe('application/json');
        expect(config.headers['X-Custom-Header']).toBe('custom-value');
        return [200, {
          data: 'test'
        }];
      });

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {
        headers: {
          'X-Custom-Header': 'custom-value'
        }
      });

      // Verify result
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        data: 'test'
      });
    });
    test('should handle JSONPath data extraction', async () => {
      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://api.example.com',
          headers: {}
        },
        authentication: {
          type: 'NONE',
          fields: {}
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/test',
          response: {
            dataPath: '$.items'
          }
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Mock axios response
      mockAxios.onGet('https://api.example.com/test').reply(200, {
        items: [{
          id: 1,
          name: 'Item 1'
        }, {
          id: 2,
          name: 'Item 2'
        }],
        total: 2
      });

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {});

      // Verify result
      expect(result.success).toBe(true);
      expect(result.data).toEqual([{
        id: 1,
        name: 'Item 1'
      }, {
        id: 2,
        name: 'Item 2'
      }]);
    });
  });
  describe('validateParameters', () => {
    test('should validate parameters successfully', () => {
      // Create endpoint
      const endpoint = {
        parameters: {
          path: {
            userId: {
              type: 'string',
              required: true
            }
          },
          query: {
            page: {
              type: 'integer',
              required: true
            }
          },
          body: {
            required: true,
            properties: {
              name: {
                type: 'string',
                required: true
              }
            }
          }
        }
      };

      // Create parameters
      const params = {
        path: {
          userId: '123'
        },
        query: {
          page: 1
        },
        body: {
          name: 'Test User'
        }
      };

      // Validate parameters
      expect(() => executor.validateParameters(params, endpoint)).not.toThrow();
    });
    test('should throw error for missing required path parameter', () => {
      // Create endpoint
      const endpoint = {
        parameters: {
          path: {
            userId: {
              type: 'string',
              required: true
            }
          }
        }
      };

      // Create parameters
      const params = {
        path: {}
      };

      // Validate parameters
      expect(() => executor.validateParameters(params, endpoint)).toThrow('Required path parameter \'userId\' is missing');
    });
    test('should throw error for missing required query parameter', () => {
      // Create endpoint
      const endpoint = {
        parameters: {
          query: {
            page: {
              type: 'integer',
              required: true
            }
          }
        }
      };

      // Create parameters
      const params = {
        query: {}
      };

      // Validate parameters
      expect(() => executor.validateParameters(params, endpoint)).toThrow('Required query parameter \'page\' is missing');
    });
    test('should throw error for missing required body', () => {
      // Create endpoint
      const endpoint = {
        parameters: {
          body: {
            required: true
          }
        }
      };

      // Create parameters
      const params = {};

      // Validate parameters
      expect(() => executor.validateParameters(params, endpoint)).toThrow('Request body is required');
    });
  });
  describe('metrics', () => {
    test('should track metrics', async () => {
      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://api.example.com',
          headers: {}
        },
        authentication: {
          type: 'NONE',
          fields: {}
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/test'
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Mock axios response
      mockAxios.onGet('https://api.example.com/test').reply(200, {
        data: 'test'
      });

      // Execute connector
      await executor.executeConnector('test-connector', 'test-endpoint', {});

      // Get metrics
      const metrics = executor.getMetrics();

      // Verify metrics
      expect(metrics.totalRequests).toBe(1);
      expect(metrics.successfulRequests).toBe(1);
      expect(metrics.failedRequests).toBe(0);
      expect(metrics.blockedRequests).toBe(0);
      expect(metrics.totalRequestTime).toBeGreaterThan(0);
      expect(metrics.averageRequestTime).toBeGreaterThan(0);
    });
    test('should track failed requests', async () => {
      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://api.example.com',
          headers: {}
        },
        authentication: {
          type: 'NONE',
          fields: {}
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/test'
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Mock axios response
      mockAxios.onGet('https://api.example.com/test').reply(500, {
        error: 'Server error'
      });

      // Execute connector
      await executor.executeConnector('test-connector', 'test-endpoint', {});

      // Get metrics
      const metrics = executor.getMetrics();

      // Verify metrics
      expect(metrics.totalRequests).toBe(1);
      expect(metrics.successfulRequests).toBe(0);
      expect(metrics.failedRequests).toBe(1);
      expect(metrics.blockedRequests).toBe(0);
      expect(metrics.totalRequestTime).toBeGreaterThan(0);
      expect(metrics.averageRequestTime).toBeGreaterThan(0);
    });
    test('should track blocked requests', async () => {
      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'http://localhost:3000',
          headers: {}
        },
        authentication: {
          type: 'NONE',
          fields: {}
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/test'
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Execute connector
      await executor.executeConnector('test-connector', 'test-endpoint', {});

      // Get metrics
      const metrics = executor.getMetrics();

      // Verify metrics
      expect(metrics.totalRequests).toBe(1);
      expect(metrics.successfulRequests).toBe(0);
      expect(metrics.failedRequests).toBe(0);
      expect(metrics.blockedRequests).toBe(1);
    });
  });
  describe('SSRF protection', () => {
    test('should allow whitelisted domains', async () => {
      // Add allowed domain
      executor.addAllowedDomains('api.example.com');

      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://api.example.com',
          headers: {}
        },
        authentication: {
          type: 'NONE',
          fields: {}
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/test'
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Mock axios response
      mockAxios.onGet('https://api.example.com/test').reply(200, {
        data: 'test'
      });

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {});

      // Verify result
      expect(result.success).toBe(true);
    });
    test('should allow wildcard domains', async () => {
      // Add allowed domain with wildcard
      executor.addAllowedDomains('*.example.com');

      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://api.example.com',
          headers: {}
        },
        authentication: {
          type: 'NONE',
          fields: {}
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/test'
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Mock axios response
      mockAxios.onGet('https://api.example.com/test').reply(200, {
        data: 'test'
      });

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {});

      // Verify result
      expect(result.success).toBe(true);
    });
    test('should block non-whitelisted domains', async () => {
      // Add allowed domain
      executor.addAllowedDomains('api.example.com');

      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://api.other.com',
          headers: {}
        },
        authentication: {
          type: 'NONE',
          fields: {}
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/test'
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {});

      // Verify result
      expect(result.success).toBe(false);
      expect(result.error).toContain('URL blocked by SSRF protection');
    });
    test('should block private IP addresses', async () => {
      // Mock connector
      const connector = {
        configuration: {
          baseUrl: 'https://***********',
          headers: {}
        },
        authentication: {
          type: 'NONE',
          fields: {}
        },
        endpoints: [{
          id: 'test-endpoint',
          method: 'GET',
          path: '/test'
        }]
      };

      // Mock connector registry response
      connectorRegistry.getConnector.mockReturnValue(connector);

      // Execute connector
      const result = await executor.executeConnector('test-connector', 'test-endpoint', {});

      // Verify result
      expect(result.success).toBe(false);
      expect(result.error).toContain('URL blocked by SSRF protection');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************