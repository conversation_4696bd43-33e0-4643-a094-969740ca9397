const controllers = require('../../../../apis/esg/disclosures/controllers');
const models = require('../../../../apis/esg/disclosures/models');

// Mock the models
jest.mock('../../../../apis/esg/disclosures/models', () => ({
  esgDisclosures: [
    {
      id: 'esg-d-12345678',
      title: 'Annual Carbon Emissions Disclosure',
      description: 'Disclosure of annual carbon emissions across all operations',
      category: 'environmental',
      type: 'regulatory',
      framework: 'GRI',
      frameworkReference: 'GRI 305',
      status: 'published',
      period: {
        startDate: '2022-01-01',
        endDate: '2022-12-31'
      },
      publishDate: '2023-03-15',
      content: {
        summary: 'Total carbon emissions for 2022 were 8,500 metric tons, a 15% reduction from 2021.',
        metrics: [
          {
            name: 'Scope 1 Emissions',
            value: 3200,
            unit: 'metric-tons',
            previousValue: 3800,
            change: -15.8
          },
          {
            name: 'Scope 2 Emissions',
            value: 5300,
            unit: 'metric-tons',
            previousValue: 6200,
            change: -14.5
          }
        ],
        narrative: 'Our emissions reduction initiatives have resulted in significant progress...',
        attachments: [
          {
            id: 'att-12345',
            name: 'Detailed Emissions Report',
            type: 'pdf',
            url: 'https://example.com/reports/emissions-2022.pdf'
          }
        ]
      },
      owner: 'Sustainability Team',
      contributors: ['Operations', 'Facilities'],
      reviewers: ['Executive Board'],
      relatedTargets: ['esg-t-12345678'],
      createdAt: '2023-01-15T00:00:00Z',
      updatedAt: '2023-03-15T00:00:00Z'
    },
    {
      id: 'esg-d-87654321',
      title: 'Diversity and Inclusion Report',
      description: 'Annual disclosure of diversity and inclusion metrics and initiatives',
      category: 'social',
      type: 'voluntary',
      framework: 'SASB',
      frameworkReference: 'SASB SV-PS-330a.1',
      status: 'draft',
      period: {
        startDate: '2022-01-01',
        endDate: '2022-12-31'
      },
      content: {
        summary: 'Our workforce diversity increased across all categories in 2022.',
        metrics: [
          {
            name: 'Gender Diversity',
            value: 42,
            unit: 'percentage',
            previousValue: 38,
            change: 10.5
          },
          {
            name: 'Ethnic Diversity',
            value: 35,
            unit: 'percentage',
            previousValue: 30,
            change: 16.7
          }
        ],
        narrative: 'Our diversity and inclusion initiatives have focused on...',
        attachments: []
      },
      owner: 'HR Team',
      contributors: ['Diversity Council'],
      reviewers: [],
      relatedTargets: ['esg-t-87654321'],
      createdAt: '2023-02-01T00:00:00Z',
      updatedAt: '2023-02-15T00:00:00Z'
    }
  ],
  disclosureTemplates: [
    {
      id: 'template-12345',
      name: 'Carbon Emissions Disclosure Template',
      description: 'Template for annual carbon emissions disclosure',
      category: 'environmental',
      framework: 'GRI',
      frameworkReference: 'GRI 305',
      structure: {
        sections: [
          {
            title: 'Executive Summary',
            description: 'Brief summary of emissions performance',
            required: true,
            order: 1
          },
          {
            title: 'Emissions Data',
            description: 'Detailed breakdown of emissions by scope and source',
            required: true,
            order: 2
          },
          {
            title: 'Methodology',
            description: 'Description of measurement methodology and standards used',
            required: true,
            order: 3
          },
          {
            title: 'Reduction Initiatives',
            description: 'Description of emissions reduction initiatives',
            required: false,
            order: 4
          }
        ],
        metrics: [
          {
            name: 'Scope 1 Emissions',
            unit: 'metric-tons',
            required: true
          },
          {
            name: 'Scope 2 Emissions',
            unit: 'metric-tons',
            required: true
          },
          {
            name: 'Scope 3 Emissions',
            unit: 'metric-tons',
            required: false
          }
        ]
      },
      createdAt: '2022-12-01T00:00:00Z',
      updatedAt: '2022-12-01T00:00:00Z'
    }
  ]
}));

// Mock Express request and response
const mockRequest = (params = {}, query = {}, body = {}) => ({
  params,
  query,
  body
});

const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

describe('ESG Disclosures Controllers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getDisclosures', () => {
    it('should return all disclosures with default pagination', () => {
      const req = mockRequest({}, {});
      const res = mockResponse();

      controllers.getDisclosures(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgDisclosures,
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter disclosures by category', () => {
      const req = mockRequest({}, { category: 'environmental' });
      const res = mockResponse();

      controllers.getDisclosures(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.esgDisclosures[0]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter disclosures by status', () => {
      const req = mockRequest({}, { status: 'draft' });
      const res = mockResponse();

      controllers.getDisclosures(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.esgDisclosures[1]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter disclosures by framework', () => {
      const req = mockRequest({}, { framework: 'GRI' });
      const res = mockResponse();

      controllers.getDisclosures(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.esgDisclosures[0]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should handle errors', () => {
      const req = mockRequest();
      const res = mockResponse();

      // Force an error
      jest.spyOn(console, 'error').mockImplementation(() => {});
      jest.spyOn(Array.prototype, 'filter').mockImplementation(() => {
        throw new Error('Test error');
      });

      controllers.getDisclosures(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Internal Server Error',
        message: 'Test error'
      });

      // Restore console.error
      console.error.mockRestore();
    });
  });

  describe('getDisclosureById', () => {
    it('should return a specific disclosure by ID', () => {
      const req = mockRequest({ id: 'esg-d-12345678' });
      const res = mockResponse();

      controllers.getDisclosureById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgDisclosures[0]
      });
    });

    it('should return 404 if disclosure not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getDisclosureById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG disclosure with ID non-existent-id not found'
      });
    });
  });

  describe('createDisclosure', () => {
    it('should create a new disclosure', () => {
      const newDisclosure = {
        title: 'Water Usage Disclosure',
        description: 'Annual disclosure of water usage across all facilities',
        category: 'environmental',
        type: 'voluntary',
        framework: 'GRI',
        frameworkReference: 'GRI 303',
        status: 'draft',
        period: {
          startDate: '2022-01-01',
          endDate: '2022-12-31'
        },
        content: {
          summary: 'Total water usage for 2022 was 1.2 million gallons, a 5% reduction from 2021.',
          metrics: [
            {
              name: 'Total Water Usage',
              value: 1200000,
              unit: 'gallons',
              previousValue: 1260000,
              change: -4.8
            }
          ],
          narrative: 'Our water conservation initiatives have resulted in...'
        },
        owner: 'Facilities Team'
      };

      const req = mockRequest({}, {}, newDisclosure);
      const res = mockResponse();

      // Mock Date and UUID
      const originalDateNow = Date.now;
      Date.now = jest.fn(() => 1577836800000); // 2020-01-01T00:00:00Z
      global.Date = jest.fn(() => ({
        toISOString: () => '2020-01-01T00:00:00Z'
      }));
      jest.mock('uuid', () => ({
        v4: jest.fn(() => '00000000-0000-0000-0000-000000000000')
      }));

      controllers.createDisclosure(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          title: 'Water Usage Disclosure',
          category: 'environmental'
        }),
        message: 'ESG disclosure created successfully'
      }));

      // Restore Date
      Date.now = originalDateNow;
    });
  });

  describe('updateDisclosure', () => {
    it('should update an existing disclosure', () => {
      const updatedDisclosure = {
        title: 'Updated Disclosure Title',
        status: 'published',
        publishDate: '2023-04-01'
      };

      const req = mockRequest({ id: 'esg-d-12345678' }, {}, updatedDisclosure);
      const res = mockResponse();

      controllers.updateDisclosure(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'esg-d-12345678',
          title: 'Updated Disclosure Title',
          status: 'published',
          publishDate: '2023-04-01'
        }),
        message: 'ESG disclosure updated successfully'
      }));
    });

    it('should return 404 if disclosure not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { title: 'Updated Title' });
      const res = mockResponse();

      controllers.updateDisclosure(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG disclosure with ID non-existent-id not found'
      });
    });
  });

  describe('deleteDisclosure', () => {
    it('should delete an existing disclosure', () => {
      const req = mockRequest({ id: 'esg-d-12345678' });
      const res = mockResponse();

      controllers.deleteDisclosure(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'ESG disclosure deleted successfully'
      });
    });

    it('should return 404 if disclosure not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.deleteDisclosure(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG disclosure with ID non-existent-id not found'
      });
    });
  });

  describe('getDisclosureAttachments', () => {
    it('should return attachments for a specific disclosure', () => {
      const req = mockRequest({ id: 'esg-d-12345678' });
      const res = mockResponse();

      controllers.getDisclosureAttachments(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgDisclosures[0].content.attachments
      });
    });

    it('should return 404 if disclosure not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getDisclosureAttachments(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG disclosure with ID non-existent-id not found'
      });
    });
  });

  describe('addDisclosureAttachment', () => {
    it('should add a new attachment to a disclosure', () => {
      const newAttachment = {
        name: 'Supplementary Data',
        type: 'xlsx',
        url: 'https://example.com/reports/emissions-data-2022.xlsx'
      };

      const req = mockRequest({ id: 'esg-d-12345678' }, {}, newAttachment);
      const res = mockResponse();

      controllers.addDisclosureAttachment(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          name: 'Supplementary Data',
          type: 'xlsx'
        }),
        message: 'Attachment added successfully'
      }));
    });

    it('should return 404 if disclosure not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { name: 'New Attachment' });
      const res = mockResponse();

      controllers.addDisclosureAttachment(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG disclosure with ID non-existent-id not found'
      });
    });
  });

  describe('removeDisclosureAttachment', () => {
    it('should remove an attachment from a disclosure', () => {
      const req = mockRequest({ id: 'esg-d-12345678', attachmentId: 'att-12345' });
      const res = mockResponse();

      controllers.removeDisclosureAttachment(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'Attachment removed successfully'
      });
    });

    it('should return 404 if attachment not found', () => {
      const req = mockRequest({ id: 'esg-d-12345678', attachmentId: 'non-existent-id' });
      const res = mockResponse();

      controllers.removeDisclosureAttachment(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Attachment with ID non-existent-id not found'
      });
    });
  });

  describe('getDisclosureTemplates', () => {
    it('should return all disclosure templates', () => {
      const req = mockRequest();
      const res = mockResponse();

      controllers.getDisclosureTemplates(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.disclosureTemplates
      });
    });
  });

  describe('getDisclosureTemplateById', () => {
    it('should return a specific disclosure template by ID', () => {
      const req = mockRequest({ id: 'template-12345' });
      const res = mockResponse();

      controllers.getDisclosureTemplateById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.disclosureTemplates[0]
      });
    });

    it('should return 404 if template not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getDisclosureTemplateById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Disclosure template with ID non-existent-id not found'
      });
    });
  });

  describe('createDisclosureTemplate', () => {
    it('should create a new disclosure template', () => {
      const newTemplate = {
        name: 'Diversity and Inclusion Disclosure Template',
        description: 'Template for diversity and inclusion disclosure',
        category: 'social',
        framework: 'SASB',
        frameworkReference: 'SASB SV-PS-330a.1',
        structure: {
          sections: [
            {
              title: 'Executive Summary',
              description: 'Brief summary of diversity metrics',
              required: true,
              order: 1
            },
            {
              title: 'Diversity Data',
              description: 'Detailed breakdown of diversity metrics',
              required: true,
              order: 2
            }
          ],
          metrics: [
            {
              name: 'Gender Diversity',
              unit: 'percentage',
              required: true
            },
            {
              name: 'Ethnic Diversity',
              unit: 'percentage',
              required: true
            }
          ]
        }
      };

      const req = mockRequest({}, {}, newTemplate);
      const res = mockResponse();

      controllers.createDisclosureTemplate(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          name: 'Diversity and Inclusion Disclosure Template',
          category: 'social'
        }),
        message: 'Disclosure template created successfully'
      }));
    });
  });

  describe('updateDisclosureTemplate', () => {
    it('should update an existing disclosure template', () => {
      const updatedTemplate = {
        name: 'Updated Template Name',
        structure: {
          sections: [
            {
              title: 'New Section',
              description: 'New section description',
              required: true,
              order: 5
            }
          ]
        }
      };

      const req = mockRequest({ id: 'template-12345' }, {}, updatedTemplate);
      const res = mockResponse();

      controllers.updateDisclosureTemplate(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'template-12345',
          name: 'Updated Template Name'
        }),
        message: 'Disclosure template updated successfully'
      }));
    });

    it('should return 404 if template not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { name: 'Updated Name' });
      const res = mockResponse();

      controllers.updateDisclosureTemplate(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Disclosure template with ID non-existent-id not found'
      });
    });
  });

  describe('deleteDisclosureTemplate', () => {
    it('should delete an existing disclosure template', () => {
      const req = mockRequest({ id: 'template-12345' });
      const res = mockResponse();

      controllers.deleteDisclosureTemplate(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'Disclosure template deleted successfully'
      });
    });

    it('should return 404 if template not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.deleteDisclosureTemplate(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Disclosure template with ID non-existent-id not found'
      });
    });
  });
});
