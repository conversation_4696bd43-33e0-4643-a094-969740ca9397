/**
 * ESG Reporting API - Controllers
 * 
 * This file defines the controllers for the ESG Reporting API.
 */

const { ReportTemplate, Report } = require('./models');
const logger = require('../../../utils/logger');

/**
 * Report Template Controllers
 */
const reportTemplateController = {
  /**
   * Get all report templates
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllReportTemplates: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-createdAt', framework, status } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      // Build filter object
      const filter = {};
      if (framework) filter.framework = framework;
      if (status) filter.status = status;
      
      const reportTemplates = await ReportTemplate.find(filter)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit);
      
      const total = await ReportTemplate.countDocuments(filter);
      
      return res.status(200).json({
        success: true,
        data: reportTemplates,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting report templates: ${error.message}`, { service: 'esg-reporting-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting report templates'
      });
    }
  },
  
  /**
   * Get report template by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getReportTemplateById: async (req, res) => {
    try {
      const reportTemplate = await ReportTemplate.findById(req.params.id)
        .populate('sections.metrics.metric');
      
      if (!reportTemplate) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Report template not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: reportTemplate
      });
    } catch (error) {
      logger.error(`Error getting report template: ${error.message}`, { service: 'esg-reporting-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting the report template'
      });
    }
  },
  
  /**
   * Create a new report template
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createReportTemplate: async (req, res) => {
    try {
      const reportTemplate = new ReportTemplate({
        ...req.body,
        createdBy: req.user.id
      });
      
      await reportTemplate.save();
      
      return res.status(201).json({
        success: true,
        data: reportTemplate,
        message: 'Report template created successfully'
      });
    } catch (error) {
      logger.error(`Error creating report template: ${error.message}`, { service: 'esg-reporting-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while creating the report template'
      });
    }
  },
  
  /**
   * Update a report template
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateReportTemplate: async (req, res) => {
    try {
      const reportTemplate = await ReportTemplate.findByIdAndUpdate(
        req.params.id,
        { ...req.body, updatedAt: Date.now() },
        { new: true, runValidators: true }
      );
      
      if (!reportTemplate) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Report template not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: reportTemplate,
        message: 'Report template updated successfully'
      });
    } catch (error) {
      logger.error(`Error updating report template: ${error.message}`, { service: 'esg-reporting-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while updating the report template'
      });
    }
  },
  
  /**
   * Delete a report template
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteReportTemplate: async (req, res) => {
    try {
      const reportTemplate = await ReportTemplate.findByIdAndDelete(req.params.id);
      
      if (!reportTemplate) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Report template not found'
        });
      }
      
      // Check if there are any reports using this template
      const reportsUsingTemplate = await Report.countDocuments({ template: req.params.id });
      
      if (reportsUsingTemplate > 0) {
        return res.status(400).json({
          success: false,
          error: 'Bad Request',
          message: `Cannot delete template as it is used by ${reportsUsingTemplate} reports`
        });
      }
      
      return res.status(200).json({
        success: true,
        message: 'Report template deleted successfully'
      });
    } catch (error) {
      logger.error(`Error deleting report template: ${error.message}`, { service: 'esg-reporting-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while deleting the report template'
      });
    }
  }
};

/**
 * Report Controllers
 */
const reportController = {
  /**
   * Get all reports
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllReports: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-createdAt', status, template } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      // Build filter object
      const filter = {};
      if (status) filter.status = status;
      if (template) filter.template = template;
      
      const reports = await Report.find(filter)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit)
        .populate('template');
      
      const total = await Report.countDocuments(filter);
      
      return res.status(200).json({
        success: true,
        data: reports,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting reports: ${error.message}`, { service: 'esg-reporting-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting reports'
      });
    }
  },
  
  /**
   * Get report by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getReportById: async (req, res) => {
    try {
      const report = await Report.findById(req.params.id)
        .populate('template')
        .populate('sections.metrics.metric');
      
      if (!report) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Report not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: report
      });
    } catch (error) {
      logger.error(`Error getting report: ${error.message}`, { service: 'esg-reporting-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting the report'
      });
    }
  },
  
  /**
   * Create a new report
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createReport: async (req, res) => {
    try {
      // Check if template exists
      const template = await ReportTemplate.findById(req.body.template);
      
      if (!template) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Report template not found'
        });
      }
      
      // Create report with sections from template
      const report = new Report({
        ...req.body,
        sections: template.sections.map(section => ({
          title: section.title,
          description: section.description,
          content: '',
          metrics: section.metrics.map(metric => ({
            metric: metric.metric,
            value: null,
            notes: ''
          }))
        })),
        createdBy: req.user.id
      });
      
      await report.save();
      
      return res.status(201).json({
        success: true,
        data: report,
        message: 'Report created successfully'
      });
    } catch (error) {
      logger.error(`Error creating report: ${error.message}`, { service: 'esg-reporting-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while creating the report'
      });
    }
  },
  
  /**
   * Update a report
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateReport: async (req, res) => {
    try {
      const report = await Report.findByIdAndUpdate(
        req.params.id,
        { ...req.body, updatedAt: Date.now() },
        { new: true, runValidators: true }
      );
      
      if (!report) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Report not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: report,
        message: 'Report updated successfully'
      });
    } catch (error) {
      logger.error(`Error updating report: ${error.message}`, { service: 'esg-reporting-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while updating the report'
      });
    }
  },
  
  /**
   * Delete a report
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteReport: async (req, res) => {
    try {
      const report = await Report.findByIdAndDelete(req.params.id);
      
      if (!report) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Report not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        message: 'Report deleted successfully'
      });
    } catch (error) {
      logger.error(`Error deleting report: ${error.message}`, { service: 'esg-reporting-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while deleting the report'
      });
    }
  },
  
  /**
   * Submit a report for approval
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  submitReportForApproval: async (req, res) => {
    try {
      const { approvers } = req.body;
      
      if (!approvers || !Array.isArray(approvers) || approvers.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'Bad Request',
          message: 'At least one approver is required'
        });
      }
      
      const report = await Report.findById(req.params.id);
      
      if (!report) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Report not found'
        });
      }
      
      if (report.status !== 'Draft' && report.status !== 'In Progress') {
        return res.status(400).json({
          success: false,
          error: 'Bad Request',
          message: `Report cannot be submitted for approval in ${report.status} status`
        });
      }
      
      // Add approvers
      report.approvals = approvers.map(approver => ({
        approver,
        status: 'Pending',
        date: Date.now(),
        comments: ''
      }));
      
      report.status = 'In Progress';
      report.updatedAt = Date.now();
      
      await report.save();
      
      return res.status(200).json({
        success: true,
        data: report,
        message: 'Report submitted for approval successfully'
      });
    } catch (error) {
      logger.error(`Error submitting report for approval: ${error.message}`, { service: 'esg-reporting-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while submitting the report for approval'
      });
    }
  },
  
  /**
   * Approve or reject a report
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  approveOrRejectReport: async (req, res) => {
    try {
      const { status, comments } = req.body;
      
      if (status !== 'Approved' && status !== 'Rejected') {
        return res.status(400).json({
          success: false,
          error: 'Bad Request',
          message: 'Status must be either Approved or Rejected'
        });
      }
      
      const report = await Report.findById(req.params.id);
      
      if (!report) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Report not found'
        });
      }
      
      // Find the approval for the current user
      const approvalIndex = report.approvals.findIndex(
        approval => approval.approver === req.user.id && approval.status === 'Pending'
      );
      
      if (approvalIndex === -1) {
        return res.status(400).json({
          success: false,
          error: 'Bad Request',
          message: 'You are not an approver for this report or have already approved/rejected it'
        });
      }
      
      // Update the approval
      report.approvals[approvalIndex].status = status;
      report.approvals[approvalIndex].date = Date.now();
      report.approvals[approvalIndex].comments = comments || '';
      
      // Check if all approvals are complete
      const allApproved = report.approvals.every(approval => approval.status === 'Approved');
      const anyRejected = report.approvals.some(approval => approval.status === 'Rejected');
      const allCompleted = report.approvals.every(approval => approval.status !== 'Pending');
      
      if (allCompleted) {
        if (allApproved) {
          report.status = 'Completed';
        } else if (anyRejected) {
          report.status = 'In Progress';
        }
      }
      
      report.updatedAt = Date.now();
      
      await report.save();
      
      return res.status(200).json({
        success: true,
        data: report,
        message: `Report ${status.toLowerCase()} successfully`
      });
    } catch (error) {
      logger.error(`Error approving/rejecting report: ${error.message}`, { service: 'esg-reporting-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while approving/rejecting the report'
      });
    }
  },
  
  /**
   * Publish a report
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  publishReport: async (req, res) => {
    try {
      const report = await Report.findById(req.params.id);
      
      if (!report) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Report not found'
        });
      }
      
      if (report.status !== 'Completed') {
        return res.status(400).json({
          success: false,
          error: 'Bad Request',
          message: 'Only completed reports can be published'
        });
      }
      
      report.status = 'Published';
      report.publishDate = Date.now();
      report.updatedAt = Date.now();
      
      await report.save();
      
      return res.status(200).json({
        success: true,
        data: report,
        message: 'Report published successfully'
      });
    } catch (error) {
      logger.error(`Error publishing report: ${error.message}`, { service: 'esg-reporting-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while publishing the report'
      });
    }
  }
};

module.exports = {
  reportTemplateController,
  reportController
};
