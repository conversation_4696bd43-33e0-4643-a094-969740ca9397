# NovaCore Vendor Assessment Module

The Vendor Assessment Module provides comprehensive SaaS vendor assessment functionality as part of the NovaFuse Cyber-Safety Platform.

## Features

- **Vendor Management**: Manage vendors and their compliance status
- **Assessment Management**: Create and manage vendor assessments
- **Assessment Templates**: Create and manage assessment templates
- **Document Management**: Manage vendor documents with blockchain verification
- **Risk Scoring**: Calculate vendor risk scores based on assessment results
- **Cyber-Safety Certification**: Certify vendors that meet Cyber-Safety standards

## Architecture

The Vendor Assessment Module is built with a modular architecture:

- **Controllers**: Handle API requests and responses
- **Services**: Implement business logic for vendor assessment
- **Models**: Define data structures for vendors, assessments, templates, and documents

## API Endpoints

### Vendor Management

- `POST /api/vendor-assessment/vendors` - Create a new vendor
- `GET /api/vendor-assessment/organizations/:organizationId/vendors` - Get all vendors
- `GET /api/vendor-assessment/vendors/:id` - Get vendor by ID
- `PUT /api/vendor-assessment/vendors/:id` - Update vendor
- `DELETE /api/vendor-assessment/vendors/:id` - Delete vendor
- `GET /api/vendor-assessment/vendors/:id/assessments` - Get vendor assessments
- `GET /api/vendor-assessment/vendors/:id/documents` - Get vendor documents
- `PUT /api/vendor-assessment/vendors/:id/compliance` - Update vendor compliance status
- `PUT /api/vendor-assessment/vendors/:id/risk-score` - Update vendor risk score
- `PUT /api/vendor-assessment/vendors/:id/cyber-safety-certification` - Update vendor Cyber-Safety certification
- `GET /api/vendor-assessment/organizations/:organizationId/vendor-dashboard` - Get vendor dashboard

### Assessment Management

- `POST /api/vendor-assessment/assessments` - Create a new assessment
- `GET /api/vendor-assessment/organizations/:organizationId/assessments` - Get all assessments
- `GET /api/vendor-assessment/assessments/:id` - Get assessment by ID
- `PUT /api/vendor-assessment/assessments/:id` - Update assessment
- `DELETE /api/vendor-assessment/assessments/:id` - Delete assessment
- `POST /api/vendor-assessment/assessments/:id/answers` - Submit answer for assessment
- `POST /api/vendor-assessment/assessments/:id/submit` - Submit assessment for review
- `POST /api/vendor-assessment/assessments/:id/review` - Complete assessment review
- `POST /api/vendor-assessment/assessments/:id/findings` - Add finding to assessment
- `PUT /api/vendor-assessment/assessments/:id/findings/:findingId` - Update finding in assessment
- `GET /api/vendor-assessment/assessments/:id/documents` - Get assessment documents

### Assessment Template Management

- `POST /api/vendor-assessment/assessment-templates` - Create a new assessment template
- `GET /api/vendor-assessment/organizations/:organizationId/assessment-templates` - Get all assessment templates
- `GET /api/vendor-assessment/assessment-templates/:id` - Get assessment template by ID
- `PUT /api/vendor-assessment/assessment-templates/:id` - Update assessment template
- `DELETE /api/vendor-assessment/assessment-templates/:id` - Delete assessment template
- `POST /api/vendor-assessment/assessment-templates/:id/set-default` - Set template as default
- `POST /api/vendor-assessment/assessment-templates/:id/versions` - Create new version of template
- `GET /api/vendor-assessment/organizations/:organizationId/default-template` - Get default template
- `POST /api/vendor-assessment/organizations/:organizationId/import-template` - Import template from JSON

### Document Management

- `POST /api/vendor-assessment/documents` - Create a new document
- `GET /api/vendor-assessment/organizations/:organizationId/documents` - Get all documents
- `GET /api/vendor-assessment/documents/:id` - Get document by ID
- `GET /api/vendor-assessment/documents/:id/content` - Get document content
- `PUT /api/vendor-assessment/documents/:id` - Update document
- `DELETE /api/vendor-assessment/documents/:id` - Delete document
- `POST /api/vendor-assessment/documents/:id/verify` - Verify document with blockchain
- `GET /api/vendor-assessment/documents/:id/verification-status` - Check verification status for document

## Integration with NovaCore

The Vendor Assessment Module integrates with the NovaCore API to provide a comprehensive vendor assessment solution:

- Uses the Evidence API for evidence management
- Uses the Blockchain API for document verification
- Uses the Connector API for integration with external systems
- Uses the Cyber-Safety middleware for automatic risk assessment

## Usage

```javascript
// Example: Create a new vendor
const vendor = await fetch('/api/vendor-assessment/vendors', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: JSON.stringify({
    name: 'Example Vendor',
    description: 'Example SaaS vendor',
    organizationId: 'YOUR_ORGANIZATION_ID',
    website: 'https://example.com',
    industry: 'Technology',
    status: 'active',
    tier: 'medium'
  })
});

// Example: Create an assessment for a vendor
const assessment = await fetch('/api/vendor-assessment/assessments', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: JSON.stringify({
    name: 'Security Assessment',
    description: 'Annual security assessment',
    organizationId: 'YOUR_ORGANIZATION_ID',
    vendorId: 'VENDOR_ID',
    templateId: 'TEMPLATE_ID'
  })
});
```
