{"name": "test_end_to_end_workflow", "status": "broken", "statusDetails": {"message": "aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host localhost:3010 ssl:default [Connect call failed ('127.0.0.1', 3010)]", "trace": "self = <aiohttp.connector.TCPConnector object at 0x7f4f95ca1900>, addr_infos = []\nreq = <aiohttp.client_reqrep.ClientRequest object at 0x7f4f95ca1b70>\ntimeout = ClientTimeout(total=30, connect=None, sock_read=None, sock_connect=None, ceil_threshold=5)\nclient_error = <class 'aiohttp.client_exceptions.ClientConnectorError'>\nargs = (functools.partial(<class 'aiohttp.client_proto.ResponseHandler'>, loop=<_UnixSelectorEventLoop running=False closed=False debug=False>),)\nkwargs = {'server_hostname': None, 'ssl': None}\n\n    async def _wrap_create_connection(\n        self,\n        *args: Any,\n        addr_infos: List[AddrInfoType],\n        req: ClientRequest,\n        timeout: \"ClientTimeout\",\n        client_error: Type[Exception] = ClientConnectorError,\n        **kwargs: Any,\n    ) -> Tuple[asyncio.Transport, ResponseHandler]:\n        try:\n            async with ceil_timeout(\n                timeout.sock_connect, ceil_threshold=timeout.ceil_threshold\n            ):\n>               sock = await aiohappyeyeballs.start_connection(\n                    addr_infos=addr_infos,\n                    local_addr_infos=self._local_addr_infos,\n                    happy_eyeballs_delay=self._happy_eyeballs_delay,\n                    interleave=self._interleave,\n                    loop=self._loop,\n                    socket_factory=self._socket_factory,\n                )\n\n/usr/local/lib/python3.10/site-packages/aiohttp/connector.py:1268: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n/usr/local/lib/python3.10/site-packages/aiohappyeyeballs/impl.py:122: in start_connection\n    raise first_exception\n/usr/local/lib/python3.10/site-packages/aiohappyeyeballs/impl.py:73: in start_connection\n    sock = await _connect_sock(\n/usr/local/lib/python3.10/site-packages/aiohappyeyeballs/impl.py:208: in _connect_sock\n    await loop.sock_connect(sock, address)\n/usr/local/lib/python3.10/asyncio/selector_events.py:501: in sock_connect\n    return await fut\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <_UnixSelectorEventLoop running=False closed=False debug=False>, fut = None\nsock = <socket.socket [closed] fd=-1, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6>\naddress = ('127.0.0.1', 3010)\n\n    def _sock_connect_cb(self, fut, sock, address):\n        if fut.done():\n            return\n    \n        try:\n            err = sock.getsockopt(socket.SOL_SOCKET, socket.SO_ERROR)\n            if err != 0:\n                # Jump to any except clause below.\n>               raise OSError(err, f'Connect call failed {address}')\nE               ConnectionRefusedError: [Errno 111] Connect call failed ('127.0.0.1', 3010)\n\n/usr/local/lib/python3.10/asyncio/selector_events.py:541: ConnectionRefusedError\n\nThe above exception was the direct cause of the following exception:\n\ntest_config = NovaCortexTestConfig(base_url='http://localhost:3010', api_key='test-api-key', timeout=30)\n\n    @pytest.mark.asyncio\n    async def test_end_to_end_workflow(test_config):\n        \"\"\"Test a complete workflow through the NovaCortex system.\"\"\"\n        async with NovaCortexTestClient(test_config) as client:\n            # 1. Check initial coherence\n>           coherence = await client.test_coherence()\n\ntests/novacortex/test_integration.py:74: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \ntests/novacortex/test_client.py:50: in test_coherence\n    async with self.session.get(url) as response:\n/usr/local/lib/python3.10/site-packages/aiohttp/client.py:1488: in __aenter__\n    self._resp: _RetType = await self._coro\n/usr/local/lib/python3.10/site-packages/aiohttp/client.py:770: in _request\n    resp = await handler(req)\n/usr/local/lib/python3.10/site-packages/aiohttp/client.py:725: in _connect_and_send_request\n    conn = await self._connector.connect(\n/usr/local/lib/python3.10/site-packages/aiohttp/connector.py:642: in connect\n    proto = await self._create_connection(req, traces, timeout)\n/usr/local/lib/python3.10/site-packages/aiohttp/connector.py:1209: in _create_connection\n    _, proto = await self._create_direct_connection(req, traces, timeout)\n/usr/local/lib/python3.10/site-packages/aiohttp/connector.py:1581: in _create_direct_connection\n    raise last_exc\n/usr/local/lib/python3.10/site-packages/aiohttp/connector.py:1550: in _create_direct_connection\n    transp, proto = await self._wrap_create_connection(\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <aiohttp.connector.TCPConnector object at 0x7f4f95ca1900>, addr_infos = []\nreq = <aiohttp.client_reqrep.ClientRequest object at 0x7f4f95ca1b70>\ntimeout = ClientTimeout(total=30, connect=None, sock_read=None, sock_connect=None, ceil_threshold=5)\nclient_error = <class 'aiohttp.client_exceptions.ClientConnectorError'>\nargs = (functools.partial(<class 'aiohttp.client_proto.ResponseHandler'>, loop=<_UnixSelectorEventLoop running=False closed=False debug=False>),)\nkwargs = {'server_hostname': None, 'ssl': None}\n\n    async def _wrap_create_connection(\n        self,\n        *args: Any,\n        addr_infos: List[AddrInfoType],\n        req: ClientRequest,\n        timeout: \"ClientTimeout\",\n        client_error: Type[Exception] = ClientConnectorError,\n        **kwargs: Any,\n    ) -> Tuple[asyncio.Transport, ResponseHandler]:\n        try:\n            async with ceil_timeout(\n                timeout.sock_connect, ceil_threshold=timeout.ceil_threshold\n            ):\n                sock = await aiohappyeyeballs.start_connection(\n                    addr_infos=addr_infos,\n                    local_addr_infos=self._local_addr_infos,\n                    happy_eyeballs_delay=self._happy_eyeballs_delay,\n                    interleave=self._interleave,\n                    loop=self._loop,\n                    socket_factory=self._socket_factory,\n                )\n                # Add ssl_shutdown_timeout for Python 3.11+ when SSL is used\n                if (\n                    kwargs.get(\"ssl\")\n                    and self._ssl_shutdown_timeout\n                    and sys.version_info >= (3, 11)\n                ):\n                    kwargs[\"ssl_shutdown_timeout\"] = self._ssl_shutdown_timeout\n                return await self._loop.create_connection(*args, **kwargs, sock=sock)\n        except cert_errors as exc:\n            raise ClientConnectorCertificateError(req.connection_key, exc) from exc\n        except ssl_errors as exc:\n            raise ClientConnectorSSLError(req.connection_key, exc) from exc\n        except OSError as exc:\n            if exc.errno is None and isinstance(exc, asyncio.TimeoutError):\n                raise\n>           raise client_error(req.connection_key, exc) from exc\nE           aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host localhost:3010 ssl:default [Connect call failed ('127.0.0.1', 3010)]\n\n/usr/local/lib/python3.10/site-packages/aiohttp/connector.py:1291: ClientConnectorError"}, "description": "Test a complete workflow through the NovaCortex system.", "start": 1752966031143, "stop": 1752966031147, "uuid": "3467a30d-6e30-4c2e-8764-533a9a6e9d13", "historyId": "236f3645d2bf93d86ec818cc88690c7e", "testCaseId": "236f3645d2bf93d86ec818cc88690c7e", "fullName": "tests.novacortex.test_integration#test_end_to_end_workflow", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.novacortex"}, {"name": "suite", "value": "test_integration"}, {"name": "host", "value": "e7eb4a9a18dc"}, {"name": "thread", "value": "1-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.novacortex.test_integration"}]}