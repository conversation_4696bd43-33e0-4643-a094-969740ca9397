/**
 * User Testing Service
 *
 * This service provides functionality for conducting user testing on the Cyber-Safety visualizations.
 * It includes methods for recording user feedback, tracking test sessions, and analyzing results.
 */

import axios from 'axios';
import analyticsService from './visualizationAnalyticsService';
import { getTestScenario, TEST_TYPES, DIFFICULTY_LEVELS } from '../data/testScenarios';

// API base URL
const API_BASE_URL = '/api';

// Test session storage key
const TEST_SESSION_KEY = 'cyber_safety_test_session';

/**
 * Start a new test session
 * @param {Object} testConfig - Test configuration
 * @returns {Object} - Test session information
 */
export const startTestSession = async (testConfig) => {
  try {
    // Create test session
    const session = {
      id: `test_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      startTime: new Date().toISOString(),
      endTime: null,
      testType: testConfig.testType || TEST_TYPES.USABILITY,
      participantId: testConfig.participantId || `participant_${Math.random().toString(36).substring(2, 9)}`,
      participantInfo: testConfig.participantInfo || {},
      deviceInfo: getDeviceInfo(),
      tasks: testConfig.tasks || [],
      currentTaskIndex: 0,
      completedTasks: [],
      feedback: [],
      notes: testConfig.notes || ''
    };

    // Save session to local storage
    localStorage.setItem(TEST_SESSION_KEY, JSON.stringify(session));

    // Track test session start in analytics
    analyticsService.trackEvent('test_session_start', 'userTesting', {
      testType: session.testType,
      participantId: session.participantId,
      taskCount: session.tasks.length
    });

    // Return session information
    return session;
  } catch (error) {
    console.error('Error starting test session:', error);
    throw error;
  }
};

/**
 * Get current test session
 * @returns {Object|null} - Current test session or null if no session exists
 */
export const getCurrentTestSession = () => {
  try {
    // Get session from local storage
    const sessionJson = localStorage.getItem(TEST_SESSION_KEY);

    if (!sessionJson) {
      return null;
    }

    return JSON.parse(sessionJson);
  } catch (error) {
    console.error('Error getting test session:', error);
    return null;
  }
};

/**
 * Start a task in the current test session
 * @param {number} taskIndex - Index of the task to start
 * @returns {Object} - Task information
 */
export const startTask = (taskIndex) => {
  try {
    // Get current session
    const session = getCurrentTestSession();

    if (!session) {
      throw new Error('No active test session');
    }

    // Validate task index
    if (taskIndex < 0 || taskIndex >= session.tasks.length) {
      throw new Error(`Invalid task index: ${taskIndex}`);
    }

    // Get task
    const task = session.tasks[taskIndex];

    // Update task with start time
    task.startTime = new Date().toISOString();
    task.endTime = null;
    task.completed = false;
    task.success = null;
    task.timeSpent = 0;
    task.interactions = [];

    // Update session
    session.currentTaskIndex = taskIndex;

    // Save updated session
    localStorage.setItem(TEST_SESSION_KEY, JSON.stringify(session));

    // Track task start in analytics
    analyticsService.trackEvent('test_task_start', 'userTesting', {
      taskId: task.id,
      taskName: task.name,
      taskIndex,
      visualizationType: task.visualizationType
    });

    return task;
  } catch (error) {
    console.error('Error starting task:', error);
    throw error;
  }
};

/**
 * Complete a task in the current test session
 * @param {number} taskIndex - Index of the task to complete
 * @param {boolean} success - Whether the task was completed successfully
 * @param {Object} results - Task results
 * @returns {Object} - Updated task information
 */
export const completeTask = (taskIndex, success, results = {}) => {
  try {
    // Get current session
    const session = getCurrentTestSession();

    if (!session) {
      throw new Error('No active test session');
    }

    // Validate task index
    if (taskIndex < 0 || taskIndex >= session.tasks.length) {
      throw new Error(`Invalid task index: ${taskIndex}`);
    }

    // Get task
    const task = session.tasks[taskIndex];

    // Update task with completion information
    task.endTime = new Date().toISOString();
    task.completed = true;
    task.success = success;
    task.results = results;

    // Calculate time spent
    if (task.startTime) {
      const startTime = new Date(task.startTime).getTime();
      const endTime = new Date(task.endTime).getTime();
      task.timeSpent = Math.round((endTime - startTime) / 1000); // in seconds
    }

    // Add to completed tasks
    session.completedTasks.push({ ...task });

    // Move to next task if available
    if (taskIndex < session.tasks.length - 1) {
      session.currentTaskIndex = taskIndex + 1;
    }

    // Save updated session
    localStorage.setItem(TEST_SESSION_KEY, JSON.stringify(session));

    // Track task completion in analytics
    analyticsService.trackEvent('test_task_complete', 'userTesting', {
      taskId: task.id,
      taskName: task.name,
      taskIndex,
      visualizationType: task.visualizationType,
      success,
      timeSpent: task.timeSpent,
      interactionCount: task.interactions.length
    });

    return task;
  } catch (error) {
    console.error('Error completing task:', error);
    throw error;
  }
};

/**
 * Record an interaction during a task
 * @param {number} taskIndex - Index of the task
 * @param {string} interactionType - Type of interaction
 * @param {Object} details - Interaction details
 * @returns {Object} - Updated task information
 */
export const recordTaskInteraction = (taskIndex, interactionType, details = {}) => {
  try {
    // Get current session
    const session = getCurrentTestSession();

    if (!session) {
      throw new Error('No active test session');
    }

    // Validate task index
    if (taskIndex < 0 || taskIndex >= session.tasks.length) {
      throw new Error(`Invalid task index: ${taskIndex}`);
    }

    // Get task
    const task = session.tasks[taskIndex];

    // Create interaction record
    const interaction = {
      timestamp: new Date().toISOString(),
      type: interactionType,
      details
    };

    // Add interaction to task
    if (!task.interactions) {
      task.interactions = [];
    }

    task.interactions.push(interaction);

    // Save updated session
    localStorage.setItem(TEST_SESSION_KEY, JSON.stringify(session));

    return task;
  } catch (error) {
    console.error('Error recording task interaction:', error);
    throw error;
  }
};

/**
 * Add feedback to the current test session
 * @param {Object} feedback - Feedback data
 * @returns {Object} - Updated session information
 */
export const addFeedback = (feedback) => {
  try {
    // Get current session
    const session = getCurrentTestSession();

    if (!session) {
      throw new Error('No active test session');
    }

    // Add feedback with timestamp
    const feedbackWithTimestamp = {
      ...feedback,
      timestamp: new Date().toISOString()
    };

    session.feedback.push(feedbackWithTimestamp);

    // Save updated session
    localStorage.setItem(TEST_SESSION_KEY, JSON.stringify(session));

    // Track feedback in analytics
    analyticsService.trackEvent('test_feedback_added', 'userTesting', {
      feedbackType: feedback.type,
      visualizationType: feedback.visualizationType,
      rating: feedback.rating
    });

    return session;
  } catch (error) {
    console.error('Error adding feedback:', error);
    throw error;
  }
};

/**
 * End the current test session
 * @param {Object} finalFeedback - Final feedback for the session
 * @returns {Object} - Completed session information
 */
export const endTestSession = async (finalFeedback = {}) => {
  try {
    // Get current session
    const session = getCurrentTestSession();

    if (!session) {
      throw new Error('No active test session');
    }

    // Update session with end time and final feedback
    session.endTime = new Date().toISOString();
    session.finalFeedback = finalFeedback;

    // Calculate session duration
    const startTime = new Date(session.startTime).getTime();
    const endTime = new Date(session.endTime).getTime();
    session.duration = Math.round((endTime - startTime) / 1000); // in seconds

    // Calculate success rate
    const completedTasks = session.completedTasks.length;
    const successfulTasks = session.completedTasks.filter(task => task.success).length;
    session.successRate = completedTasks > 0 ? (successfulTasks / completedTasks) : 0;

    // Save updated session
    localStorage.setItem(TEST_SESSION_KEY, JSON.stringify(session));

    // Submit session results to server
    await submitTestResults(session);

    // Track test session end in analytics
    analyticsService.trackEvent('test_session_end', 'userTesting', {
      testType: session.testType,
      participantId: session.participantId,
      duration: session.duration,
      completedTasks,
      successfulTasks,
      successRate: session.successRate
    });

    // Clear session from local storage
    localStorage.removeItem(TEST_SESSION_KEY);

    return session;
  } catch (error) {
    console.error('Error ending test session:', error);
    throw error;
  }
};

/**
 * Submit test results to the server
 * @param {Object} session - Test session data
 * @returns {Object} - Server response
 */
const submitTestResults = async (session) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/user-testing/results`, {
      session
    });

    return response.data;
  } catch (error) {
    console.error('Error submitting test results:', error);

    // Store results locally if submission fails
    const failedSubmissions = JSON.parse(localStorage.getItem('failed_test_submissions') || '[]');
    failedSubmissions.push({
      session,
      timestamp: new Date().toISOString()
    });
    localStorage.setItem('failed_test_submissions', JSON.stringify(failedSubmissions));

    throw error;
  }
};

/**
 * Get device information
 * @returns {Object} - Device information
 */
const getDeviceInfo = () => {
  return {
    userAgent: navigator.userAgent,
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    viewportWidth: window.innerWidth,
    viewportHeight: window.innerHeight,
    devicePixelRatio: window.devicePixelRatio,
    language: navigator.language,
    platform: navigator.platform,
    touchSupport: 'ontouchstart' in window
  };
};

/**
 * Create a predefined test scenario
 * @param {string} visualizationType - Type of visualization to test
 * @param {string} testType - Type of test
 * @returns {Object} - Test configuration
 */
export const createTestScenario = (visualizationType, testType = TEST_TYPES.USABILITY) => {
  try {
    // Get test scenario from predefined scenarios
    const scenario = getTestScenario(visualizationType, testType);

    // Return test configuration
    return {
      testType: scenario.testType,
      tasks: scenario.tasks,
      notes: scenario.description
    };
  } catch (error) {
    console.error('Error creating test scenario:', error);

    // Fallback to basic test configuration
    return {
      testType,
      tasks: [
        {
          id: `${visualizationType}_explore`,
          name: 'Explore the visualization',
          description: 'Take a moment to explore the visualization and understand what it shows.',
          difficulty: DIFFICULTY_LEVELS.EASY,
          visualizationType,
          expectedTime: 60 // seconds
        }
      ],
      notes: `Basic test scenario for ${visualizationType} visualization`
    };
  }
};

// No longer needed as we're using predefined test scenarios

export default {
  startTestSession,
  getCurrentTestSession,
  startTask,
  completeTask,
  recordTaskInteraction,
  addFeedback,
  endTestSession,
  createTestScenario,
  TEST_TYPES,
  DIFFICULTY_LEVELS
};
