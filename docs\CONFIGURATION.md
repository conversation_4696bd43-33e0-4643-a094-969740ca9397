# NovaConnect UAC Configuration Guide

This document provides information on configuring NovaConnect Universal API Connector (UAC).

## Environment Variables

NovaConnect UAC can be configured using environment variables. The following environment variables are available:

### Core Configuration

| Variable | Description | Default | Required |
| --- | --- | --- | --- |
| `NODE_ENV` | Node.js environment | `development` | No |
| `PORT` | Port to listen on | `3001` | No |
| `MONGODB_URI` | MongoDB connection URI | `mongodb://localhost:27017/novafuse` | Yes |
| `REDIS_URI` | Redis connection URI | `redis://localhost:6379` | Yes |
| `API_KEY` | API key for authentication | - | Yes |
| `JWT_SECRET` | Secret for JWT tokens | - | Yes |

### Cluster Configuration

| Variable | Description | Default | Required |
| --- | --- | --- | --- |
| `CLUSTER_ENABLED` | Enable cluster mode | `false` | No |
| `CLUSTER_WORKERS` | Number of worker processes | Number of CPU cores | No |

### Logging Configuration

| Variable | Description | Default | Required |
| --- | --- | --- | --- |
| `LOG_LEVEL` | Log level (error, warn, info, debug, trace) | `info` | No |
| `LOG_FORMAT` | Log format (json, text) | `json` | No |
| `LOG_COLORIZE` | Colorize logs | `false` | No |
| `LOG_TIMESTAMP` | Include timestamps in logs | `true` | No |

### Monitoring Configuration

| Variable | Description | Default | Required |
| --- | --- | --- | --- |
| `MONITORING_ENABLED` | Enable monitoring | `true` | No |
| `PROMETHEUS_ENABLED` | Enable Prometheus metrics | `true` | No |
| `ALERTING_ENABLED` | Enable alerting | `true` | No |
| `ALERT_CHANNELS` | Alert channels (comma-separated) | `email,slack` | No |
| `ALERT_THRESHOLD_ERROR_RATE` | Error rate threshold for alerts | `0.05` | No |
| `ALERT_THRESHOLD_RESPONSE_TIME` | Response time threshold for alerts (ms) | `1000` | No |
| `ALERT_THRESHOLD_CPU_USAGE` | CPU usage threshold for alerts | `0.8` | No |
| `ALERT_THRESHOLD_MEMORY_USAGE` | Memory usage threshold for alerts | `0.8` | No |
| `ALERT_THRESHOLD_DISK_USAGE` | Disk usage threshold for alerts | `0.8` | No |
| `METRICS_INTERVAL` | Metrics collection interval (ms) | `60000` | No |

### Tracing Configuration

| Variable | Description | Default | Required |
| --- | --- | --- | --- |
| `TRACING_ENABLED` | Enable tracing | `false` | No |
| `TRACING_EXPORTER` | Tracing exporter (console, zipkin, gcp) | `console` | No |
| `ZIPKIN_URL` | Zipkin URL | `http://localhost:9411/api/v2/spans` | No |

### Google Cloud Configuration

| Variable | Description | Default | Required |
| --- | --- | --- | --- |
| `GCP_PROJECT_ID` | Google Cloud project ID | - | No |
| `GOOGLE_APPLICATION_CREDENTIALS` | Path to Google Cloud credentials file | - | No |
| `LOGGING_ENABLED` | Enable Google Cloud Logging | `false` | No |
| `ERROR_REPORTING_ENABLED` | Enable Google Cloud Error Reporting | `false` | No |
| `PROFILING_ENABLED` | Enable Google Cloud Profiler | `false` | No |
| `DEBUGGING_ENABLED` | Enable Google Cloud Debugger | `false` | No |

### Security Configuration

| Variable | Description | Default | Required |
| --- | --- | --- | --- |
| `CORS_ORIGIN` | CORS allowed origins (comma-separated) | `*` | No |
| `RATE_LIMIT_WINDOW_MS` | Rate limit window (ms) | `60000` | No |
| `RATE_LIMIT_MAX` | Maximum requests per window | `100` | No |
| `IP_FILTERING_ENABLED` | Enable IP filtering | `false` | No |
| `ALLOWED_IPS` | Allowed IP addresses (comma-separated) | - | No |

### Cache Configuration

| Variable | Description | Default | Required |
| --- | --- | --- | --- |
| `CACHE_ENABLED` | Enable caching | `true` | No |
| `CACHE_TTL` | Cache TTL (seconds) | `300` | No |
| `CACHE_MAX_SIZE` | Maximum cache size (items) | `1000` | No |

### Database Optimization Configuration

| Variable | Description | Default | Required |
| --- | --- | --- | --- |
| `DB_OPTIMIZATION_ENABLED` | Enable database optimization | `false` | No |
| `DB_OPTIMIZATION_INTERVAL` | Database optimization interval (ms) | `86400000` | No |
| `DB_OPTIMIZATION_INDEXES_ENABLED` | Enable index optimization | `true` | No |
| `DB_OPTIMIZATION_VACUUM_ENABLED` | Enable vacuum optimization | `true` | No |
| `DB_OPTIMIZATION_STATS_ENABLED` | Enable stats collection | `true` | No |
| `DB_OPTIMIZATION_MAX_QUERY_TIME` | Maximum query time (ms) | `1000` | No |
| `DB_OPTIMIZATION_SLOW_QUERY_THRESHOLD` | Slow query threshold (ms) | `500` | No |
| `DB_OPTIMIZATION_INDEX_THRESHOLD` | Index threshold (documents) | `1000` | No |

## Configuration File

In addition to environment variables, NovaConnect UAC can be configured using a configuration file. The configuration file is a JSON file that can be specified using the `CONFIG_FILE` environment variable.

Example configuration file:

```json
{
  "core": {
    "port": 3001,
    "mongodbUri": "mongodb://localhost:27017/novafuse",
    "redisUri": "redis://localhost:6379",
    "apiKey": "your-api-key",
    "jwtSecret": "your-jwt-secret"
  },
  "cluster": {
    "enabled": true,
    "workers": 4
  },
  "logging": {
    "level": "info",
    "format": "json",
    "colorize": false,
    "timestamp": true
  },
  "monitoring": {
    "enabled": true,
    "prometheusEnabled": true,
    "alertingEnabled": true,
    "alertChannels": ["email", "slack"],
    "alertThresholds": {
      "errorRate": 0.05,
      "responseTime": 1000,
      "cpuUsage": 0.8,
      "memoryUsage": 0.8,
      "diskUsage": 0.8
    },
    "metricsInterval": 60000
  },
  "tracing": {
    "enabled": false,
    "exporter": "console",
    "zipkinUrl": "http://localhost:9411/api/v2/spans"
  },
  "googleCloud": {
    "projectId": "your-gcp-project-id",
    "credentials": "/path/to/your/credentials.json",
    "loggingEnabled": false,
    "errorReportingEnabled": false,
    "profilingEnabled": false,
    "debuggingEnabled": false
  },
  "security": {
    "corsOrigin": "*",
    "rateLimitWindowMs": 60000,
    "rateLimitMax": 100,
    "ipFilteringEnabled": false,
    "allowedIps": []
  },
  "cache": {
    "enabled": true,
    "ttl": 300,
    "maxSize": 1000
  },
  "dbOptimization": {
    "enabled": false,
    "interval": 86400000,
    "indexesEnabled": true,
    "vacuumEnabled": true,
    "statsEnabled": true,
    "maxQueryTime": 1000,
    "slowQueryThreshold": 500,
    "indexThreshold": 1000
  }
}
```

## Feature Flags

NovaConnect UAC supports feature flags to enable or disable specific features. Feature flags can be configured using environment variables or the configuration file.

The following feature flags are available:

| Feature Flag | Description | Default |
| --- | --- | --- |
| `FEATURE_CONNECTOR_TEMPLATES` | Enable connector templates | `true` |
| `FEATURE_DATA_NORMALIZATION` | Enable data normalization | `true` |
| `FEATURE_WORKFLOWS` | Enable workflows | `true` |
| `FEATURE_MONITORING` | Enable monitoring | `true` |
| `FEATURE_ALERTING` | Enable alerting | `true` |
| `FEATURE_TRACING` | Enable tracing | `false` |
| `FEATURE_GOOGLE_CLOUD` | Enable Google Cloud integration | `false` |
| `FEATURE_CACHE` | Enable caching | `true` |
| `FEATURE_DB_OPTIMIZATION` | Enable database optimization | `false` |

## Secrets Management

NovaConnect UAC supports storing secrets in Google Cloud Secret Manager. To use Google Cloud Secret Manager, set the `SECRETS_MANAGER` environment variable to `gcp` and provide the `GCP_PROJECT_ID` and `GOOGLE_APPLICATION_CREDENTIALS` environment variables.

The following secrets are supported:

| Secret | Description |
| --- | --- |
| `novafuse-uac-mongodb-uri` | MongoDB connection URI |
| `novafuse-uac-redis-uri` | Redis connection URI |
| `novafuse-uac-api-key` | API key for authentication |
| `novafuse-uac-jwt-secret` | Secret for JWT tokens |

## Environment-Specific Configuration

NovaConnect UAC supports environment-specific configuration using the `NODE_ENV` environment variable. The following environments are supported:

- `development`: Development environment
- `test`: Test environment
- `staging`: Staging environment
- `production`: Production environment

Environment-specific configuration can be specified in the configuration file using the environment name as a key:

```json
{
  "core": {
    "port": 3001
  },
  "development": {
    "core": {
      "port": 3002
    }
  },
  "production": {
    "core": {
      "port": 3003
    }
  }
}
```

In this example, the port will be 3002 in the development environment, 3003 in the production environment, and 3001 in all other environments.

## Configuration Validation

NovaConnect UAC validates the configuration at startup. If the configuration is invalid, the application will log an error and exit.

To validate the configuration without starting the application, use the `validate-config` script:

```bash
npm run validate-config
```

## Configuration Examples

### Development Environment

```bash
NODE_ENV=development
PORT=3001
MONGODB_URI=mongodb://localhost:27017/novafuse
REDIS_URI=redis://localhost:6379
API_KEY=your-api-key
JWT_SECRET=your-jwt-secret
CLUSTER_ENABLED=false
LOG_LEVEL=debug
MONITORING_ENABLED=false
TRACING_ENABLED=false
```

### Production Environment

```bash
NODE_ENV=production
PORT=3001
MONGODB_URI=mongodb://mongodb:27017/novafuse
REDIS_URI=redis://redis:6379
API_KEY=your-api-key
JWT_SECRET=your-jwt-secret
CLUSTER_ENABLED=true
LOG_LEVEL=info
MONITORING_ENABLED=true
TRACING_ENABLED=true
GCP_PROJECT_ID=your-gcp-project-id
GOOGLE_APPLICATION_CREDENTIALS=/var/secrets/google/key.json
LOGGING_ENABLED=true
ERROR_REPORTING_ENABLED=true
PROFILING_ENABLED=true
DEBUGGING_ENABLED=true
CORS_ORIGIN=https://api.novafuse.io,https://app.novafuse.io
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100
DB_OPTIMIZATION_ENABLED=true
DB_OPTIMIZATION_INTERVAL=86400000
```

### Google Cloud Marketplace Environment

```bash
NODE_ENV=production
PORT=3001
SECRETS_MANAGER=gcp
GCP_PROJECT_ID=your-gcp-project-id
GOOGLE_APPLICATION_CREDENTIALS=/var/secrets/google/key.json
CLUSTER_ENABLED=true
LOG_LEVEL=info
MONITORING_ENABLED=true
TRACING_ENABLED=true
LOGGING_ENABLED=true
ERROR_REPORTING_ENABLED=true
PROFILING_ENABLED=true
DEBUGGING_ENABLED=true
CORS_ORIGIN=https://api.novafuse.io,https://app.novafuse.io
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100
DB_OPTIMIZATION_ENABLED=true
DB_OPTIMIZATION_INTERVAL=86400000
```
