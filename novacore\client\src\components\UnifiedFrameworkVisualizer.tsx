/**
 * Unified Framework Visualizer Component
 * 
 * This component visualizes relationships between different compliance frameworks.
 */

import React, { useEffect, useRef } from 'react';

interface UnifiedFrameworkVisualizerProps {
  frameworkIds: string[];
  showControls?: boolean;
  showMappings?: boolean;
  height?: string;
}

export const UnifiedFrameworkVisualizer: React.FC<UnifiedFrameworkVisualizerProps> = ({
  frameworkIds,
  showControls = true,
  showMappings = true,
  height = '400px',
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  // Mock data for frameworks
  const frameworks = {
    'framework1': { name: 'SOC 2', color: '#4299E1' },
    'framework2': { name: 'HIPAA', color: '#48BB78' },
    'framework3': { name: 'GDPR', color: '#9F7AEA' },
    'framework4': { name: 'ISO 27001', color: '#F6AD55' },
    'framework5': { name: 'NIST CSF', color: '#FC8181' },
  };
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Set canvas dimensions
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw visualization
    drawVisualization(ctx, canvas.width, canvas.height);
    
  }, [frameworkIds, showControls, showMappings]);
  
  const drawVisualization = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // Draw background
    ctx.fillStyle = '#F7FAFC';
    ctx.fillRect(0, 0, width, height);
    
    // Calculate positions
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) * 0.35;
    
    // Draw central node
    ctx.beginPath();
    ctx.arc(centerX, centerY, 40, 0, Math.PI * 2);
    ctx.fillStyle = '#3182CE';
    ctx.fill();
    
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('Unified', centerX, centerY - 5);
    ctx.fillText('Controls', centerX, centerY + 10);
    
    // Draw framework nodes
    const selectedFrameworks = frameworkIds
      .filter(id => frameworks[id as keyof typeof frameworks])
      .map(id => ({ 
        id, 
        ...frameworks[id as keyof typeof frameworks] 
      }));
    
    const angleStep = (Math.PI * 2) / selectedFrameworks.length;
    
    selectedFrameworks.forEach((framework, index) => {
      const angle = index * angleStep;
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;
      
      // Draw connection line
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(x, y);
      ctx.strokeStyle = framework.color;
      ctx.lineWidth = 2;
      ctx.stroke();
      
      // Draw framework node
      ctx.beginPath();
      ctx.arc(x, y, 30, 0, Math.PI * 2);
      ctx.fillStyle = framework.color;
      ctx.fill();
      
      // Draw framework name
      ctx.fillStyle = '#FFFFFF';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(framework.name, x, y);
      
      // Draw controls if enabled
      if (showControls) {
        const controlCount = 3; // Mock number of controls
        const controlAngleStep = (Math.PI / 2) / (controlCount + 1);
        const controlRadius = 15;
        
        for (let i = 1; i <= controlCount; i++) {
          const controlAngle = angle - Math.PI / 4 + i * controlAngleStep;
          const controlDistance = radius * 0.6;
          const controlX = centerX + Math.cos(controlAngle) * controlDistance;
          const controlY = centerY + Math.sin(controlAngle) * controlDistance;
          
          // Draw control node
          ctx.beginPath();
          ctx.arc(controlX, controlY, controlRadius, 0, Math.PI * 2);
          ctx.fillStyle = '#E2E8F0';
          ctx.fill();
          ctx.strokeStyle = framework.color;
          ctx.lineWidth = 2;
          ctx.stroke();
          
          // Draw connection to framework
          ctx.beginPath();
          ctx.moveTo(x, y);
          ctx.lineTo(controlX, controlY);
          ctx.strokeStyle = framework.color;
          ctx.lineWidth = 1;
          ctx.stroke();
          
          // Draw control label
          ctx.fillStyle = '#4A5568';
          ctx.font = '10px Arial';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(`C${i}`, controlX, controlY);
        }
      }
    });
    
    // Draw mappings between frameworks if enabled
    if (showMappings && selectedFrameworks.length > 1) {
      for (let i = 0; i < selectedFrameworks.length; i++) {
        for (let j = i + 1; j < selectedFrameworks.length; j++) {
          const angle1 = i * angleStep;
          const angle2 = j * angleStep;
          const x1 = centerX + Math.cos(angle1) * radius;
          const y1 = centerY + Math.sin(angle1) * radius;
          const x2 = centerX + Math.cos(angle2) * radius;
          const y2 = centerY + Math.sin(angle2) * radius;
          
          // Draw curved connection
          ctx.beginPath();
          const midX = (x1 + x2) / 2;
          const midY = (y1 + y2) / 2;
          const curveFactor = 0.3;
          const ctrlX = midX + (midY - centerY) * curveFactor;
          const ctrlY = midY - (midX - centerX) * curveFactor;
          
          ctx.moveTo(x1, y1);
          ctx.quadraticCurveTo(ctrlX, ctrlY, x2, y2);
          ctx.strokeStyle = '#A0AEC0';
          ctx.lineWidth = 1;
          ctx.setLineDash([5, 3]);
          ctx.stroke();
          ctx.setLineDash([]);
        }
      }
    }
  };
  
  return (
    <div className="relative" style={{ height }}>
      <canvas 
        ref={canvasRef} 
        className="w-full h-full"
      />
      {frameworkIds.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center text-gray-500">
          Select frameworks to visualize
        </div>
      )}
    </div>
  );
};

export default UnifiedFrameworkVisualizer;
