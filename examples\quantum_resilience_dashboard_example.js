/**
 * Quantum Resilience Dashboard Example
 *
 * This example demonstrates how to use the Quantum Resilience Dashboard
 * and Enhanced Metrics Dashboard to monitor and visualize quantum resilience metrics.
 */

// Import required modules
const { 
  EnhancedMetricsDashboard, 
  QuantumResilienceDashboard 
} = require('../src/comphyology/exports');
const { NovaVision } = require('../src/novavision');
const { NovaVisionSecurityManager } = require('../src/novavision/security');
const fs = require('fs');
const path = require('path');

// Example user IDs
const users = {
  ciso: 'user-123',
  securityAnalyst: 'user-456',
  standardUser: 'user-789'
};

// Create a NovaVision instance with security enabled
const novaVision = new NovaVision({
  theme: 'cyber-safety',
  enableSecurity: true,
  enableNIST: true,
  enableRBAC: true
});

// Assign roles to users
const securityManager = novaVision.securityManager;
securityManager.rbac.assignRole(users.ciso, 'CISO');
securityManager.rbac.assignRole(users.securityAnalyst, 'SECURITY_ANALYST');
securityManager.rbac.assignRole(users.standardUser, 'USER');

// Create dashboard instances
const quantumResilienceDashboard = new QuantumResilienceDashboard({
  theme: 'quantum',
  colorScheme: 'dark',
  refreshInterval: 15000, // 15 seconds
  enableRealTimeUpdates: true
});

const enhancedMetricsDashboard = new EnhancedMetricsDashboard({
  theme: 'cyber-safety',
  colorScheme: 'quantum',
  refreshInterval: 30000, // 30 seconds
  enableRealTimeUpdates: true
});

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../dashboard_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Simulate metrics updates
 */
function simulateMetricsUpdates() {
  console.log('Simulating metrics updates...');
  
  // Simulate π10³ constant metrics
  const piConstantMetrics = {
    piConstant: {
      value: Math.PI * Math.pow(10, 3) * (1 + (Math.random() - 0.5) * 0.0000001),
      manipulationAttempts: Math.random() < 0.1 ? 1 : 0
    }
  };
  
  // Simulate tensor integrity metrics
  const tensorIntegrityMetrics = {
    tensorIntegrity: {
      overall: 0.95 + (Math.random() - 0.5) * 0.05,
      byComponent: {
        'tensor-product': 0.96 + (Math.random() - 0.5) * 0.03,
        'tensor-fusion': 0.94 + (Math.random() - 0.5) * 0.04,
        'uuft-formula': 0.98 + (Math.random() - 0.5) * 0.02
      },
      manipulationAttempts: Math.random() < 0.1 ? 1 : 0
    }
  };
  
  // Simulate quantum entanglement metrics
  const quantumEntanglementMetrics = {
    quantumEntanglement: {
      coherence: 0.9 + (Math.random() - 0.5) * 0.1,
      byDomain: {
        cyber: 0.88 + (Math.random() - 0.5) * 0.08,
        financial: 0.92 + (Math.random() - 0.5) * 0.06,
        biological: 0.94 + (Math.random() - 0.5) * 0.04
      }
    }
  };
  
  // Simulate cross-domain metrics
  const crossDomainMetrics = {
    crossDomain: {
      coherence: 0.85 + (Math.random() - 0.5) * 0.1,
      entropyContainment: 0.015 + (Math.random() - 0.5) * 0.005
    }
  };
  
  // Simulate coherence metrics
  const coherenceMetrics = {
    coherence: {
      overall: 0.82 + (Math.random() - 0.5) * 0.1,
      byDomain: {
        cyber: 0.8 + (Math.random() - 0.5) * 0.08,
        financial: 0.84 + (Math.random() - 0.5) * 0.06,
        biological: 0.86 + (Math.random() - 0.5) * 0.04
      }
    }
  };
  
  // Simulate entropy containment metrics
  const entropyContainmentMetrics = {
    entropyContainment: {
      overall: 0.012 + (Math.random() - 0.5) * 0.005,
      byDomain: {
        cyber: 0.015 + (Math.random() - 0.5) * 0.004,
        financial: 0.01 + (Math.random() - 0.5) * 0.003,
        biological: 0.011 + (Math.random() - 0.5) * 0.002
      }
    }
  };
  
  // Update quantum resilience dashboard
  quantumResilienceDashboard.updateMetrics({
    ...piConstantMetrics,
    ...tensorIntegrityMetrics,
    ...quantumEntanglementMetrics,
    ...crossDomainMetrics
  });
  
  // Update enhanced metrics dashboard
  enhancedMetricsDashboard.updateMetrics({
    ...coherenceMetrics,
    ...entropyContainmentMetrics,
    ...tensorIntegrityMetrics
  });
  
  console.log('Metrics updated successfully');
}

/**
 * Generate dashboard schemas and save to files
 */
function generateAndSaveDashboards() {
  console.log('Generating dashboard schemas...');
  
  // Generate dashboard schemas
  const quantumResilienceSchema = quantumResilienceDashboard.generateDashboardSchema();
  const enhancedMetricsSchema = enhancedMetricsDashboard.generateDashboardSchema();
  
  // Save schemas to files
  fs.writeFileSync(
    path.join(RESULTS_DIR, 'quantum_resilience_dashboard.json'),
    JSON.stringify(quantumResilienceSchema, null, 2)
  );
  
  fs.writeFileSync(
    path.join(RESULTS_DIR, 'enhanced_metrics_dashboard.json'),
    JSON.stringify(enhancedMetricsSchema, null, 2)
  );
  
  console.log('Dashboard schemas saved to:');
  console.log(`- ${path.join(RESULTS_DIR, 'quantum_resilience_dashboard.json')}`);
  console.log(`- ${path.join(RESULTS_DIR, 'enhanced_metrics_dashboard.json')}`);
}

/**
 * Generate HTML preview of dashboards
 */
function generateHTMLPreview() {
  console.log('Generating HTML preview...');
  
  // Generate dashboard schemas
  const quantumResilienceSchema = quantumResilienceDashboard.generateDashboardSchema();
  const enhancedMetricsSchema = enhancedMetricsDashboard.generateDashboardSchema();
  
  // Generate HTML
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Quantum Resilience Dashboards</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .dashboard {
      background-color: #fff;
      border-radius: 5px;
      padding: 20px;
      margin-bottom: 30px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    .dashboard-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .dashboard-content {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }
    .card {
      background-color: #f9f9f9;
      border-radius: 5px;
      padding: 15px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    .card-title {
      font-weight: bold;
      margin-bottom: 10px;
      color: #0066cc;
    }
    .gauge {
      width: 100%;
      height: 150px;
      background-color: #eee;
      border-radius: 5px;
      position: relative;
      overflow: hidden;
    }
    .gauge-value {
      height: 100%;
      background-color: #4CAF50;
      position: absolute;
      top: 0;
      left: 0;
      transition: width 0.5s ease-in-out;
    }
    .gauge-label {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-weight: bold;
      color: #333;
    }
    .alert-list {
      margin-top: 20px;
    }
    .alert {
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 3px;
    }
    .alert.warning {
      background-color: #FFF3CD;
      border-left: 5px solid #FFD700;
    }
    .alert.critical {
      background-color: #F8D7DA;
      border-left: 5px solid #DC3545;
    }
  </style>
</head>
<body>
  <h1>Quantum Resilience Dashboards</h1>
  
  <div class="dashboard">
    <div class="dashboard-header">
      <h2>${quantumResilienceSchema.title}</h2>
      <span>Real-time monitoring of quantum resilience metrics</span>
    </div>
    
    <div class="dashboard-content">
      ${quantumResilienceSchema.components.map(component => `
        <div class="card">
          <div class="card-title">${component.title}</div>
          ${component.content.type === 'chart' && component.content.chartType === 'gauge' ? `
            <div class="gauge">
              <div class="gauge-value" style="width: ${component.content.data.value * 100}%;"></div>
              <div class="gauge-label">${(component.content.data.value * 100).toFixed(1)}%</div>
            </div>
          ` : ''}
          ${component.content.type === 'alert-list' ? `
            <div class="alert-list">
              ${component.content.data.alerts.map(alert => `
                <div class="alert ${alert.type}">
                  ${alert.message}
                </div>
              `).join('') || '<p>No alerts</p>'}
            </div>
          ` : ''}
          ${component.content.type === 'metrics' ? `
            <div class="metrics-list">
              ${component.content.data.metrics.map(metric => `
                <div class="metric">
                  <strong>${metric.name}:</strong> ${metric.value}
                </div>
              `).join('')}
            </div>
          ` : ''}
        </div>
      `).join('')}
    </div>
  </div>
  
  <div class="dashboard">
    <div class="dashboard-header">
      <h2>${enhancedMetricsSchema.title}</h2>
      <span>Comprehensive visualization of system metrics</span>
    </div>
    
    <div class="dashboard-content">
      ${enhancedMetricsSchema.components.map(component => `
        <div class="card">
          <div class="card-title">${component.title}</div>
          ${component.content.type === 'chart' && component.content.chartType === 'gauge' ? `
            <div class="gauge">
              <div class="gauge-value" style="width: ${component.content.data.value * 100}%;"></div>
              <div class="gauge-label">${(component.content.data.value * 100).toFixed(1)}%</div>
            </div>
          ` : ''}
          ${component.content.type === 'alert-list' ? `
            <div class="alert-list">
              ${component.content.data.alerts.map(alert => `
                <div class="alert ${alert.type}">
                  ${alert.message}
                </div>
              `).join('') || '<p>No alerts</p>'}
            </div>
          ` : ''}
        </div>
      `).join('')}
    </div>
  </div>
  
  <footer>
    <p>NovaFuse Quantum Resilience - Copyright © ${new Date().getFullYear()}</p>
  </footer>
</body>
</html>
  `;
  
  // Save HTML file
  fs.writeFileSync(
    path.join(RESULTS_DIR, 'dashboards_preview.html'),
    html
  );
  
  console.log('HTML preview saved to:');
  console.log(`- ${path.join(RESULTS_DIR, 'dashboards_preview.html')}`);
}

// Set up event listeners
quantumResilienceDashboard.on('metrics-updated', (metrics) => {
  console.log('Quantum Resilience metrics updated:', metrics.piConstant.deviation.toExponential(6));
});

quantumResilienceDashboard.on('alerts', (alerts) => {
  console.log('Quantum Resilience alerts:', alerts.length);
});

enhancedMetricsDashboard.on('metrics-updated', (metrics) => {
  console.log('Enhanced Metrics updated:', metrics.coherence.overall.toFixed(3));
});

enhancedMetricsDashboard.on('alerts', (alerts) => {
  console.log('Enhanced Metrics alerts:', alerts.length);
});

// Run the example
console.log('=== Quantum Resilience Dashboard Example ===');

// Simulate metrics updates
simulateMetricsUpdates();

// Generate and save dashboards
generateAndSaveDashboards();

// Generate HTML preview
generateHTMLPreview();

console.log('\nExample completed successfully!');
console.log('Check the dashboard_results directory for output files.');

// In a real application, we would keep the process running to continue receiving updates
console.log('\nIn a real application, these dashboards would be rendered in a web interface');
console.log('and would continuously update with real-time metrics.');
