/**
 * Coverage Analyzer for NovaConnect
 * 
 * This script analyzes Jest coverage reports to identify areas that need improvement
 * to reach the 96% coverage threshold.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const COVERAGE_THRESHOLD = 96;
const COVERAGE_SUMMARY_PATH = path.join(__dirname, 'coverage', 'coverage-summary.json');

/**
 * Analyzes coverage data and identifies areas that need improvement
 */
function analyzeCoverage() {
  console.log('\n📊 COVERAGE ANALYSIS');
  console.log('==================================================');
  
  // Check if coverage summary exists
  if (!fs.existsSync(COVERAGE_SUMMARY_PATH)) {
    console.error('❌ Coverage summary not found. Run tests with coverage first.');
    console.log('   Run: npm run test:coverage');
    return;
  }
  
  // Read coverage data
  const coverageData = JSON.parse(fs.readFileSync(COVERAGE_SUMMARY_PATH, 'utf8'));
  
  // Get total coverage
  const total = coverageData.total;
  
  console.log('OVERALL COVERAGE:');
  console.log(`Statements: ${total.statements.pct.toFixed(2)}%`);
  console.log(`Branches: ${total.branches.pct.toFixed(2)}%`);
  console.log(`Functions: ${total.functions.pct.toFixed(2)}%`);
  console.log(`Lines: ${total.lines.pct.toFixed(2)}%`);
  console.log('');
  
  // Check if we meet the threshold
  const belowThreshold = {
    statements: total.statements.pct < COVERAGE_THRESHOLD,
    branches: total.branches.pct < COVERAGE_THRESHOLD,
    functions: total.functions.pct < COVERAGE_THRESHOLD,
    lines: total.lines.pct < COVERAGE_THRESHOLD
  };
  
  const allPass = !Object.values(belowThreshold).some(Boolean);
  
  if (allPass) {
    console.log(`✅ All coverage metrics meet the ${COVERAGE_THRESHOLD}% threshold!`);
    return;
  }
  
  console.log(`❌ Some coverage metrics are below the ${COVERAGE_THRESHOLD}% threshold.`);
  console.log('');
  
  // Find files with lowest coverage
  const fileEntries = Object.entries(coverageData)
    .filter(([key]) => key !== 'total');
  
  // Analyze each metric that's below threshold
  if (belowThreshold.statements) {
    analyzeMetric(fileEntries, 'statements', COVERAGE_THRESHOLD - total.statements.pct);
  }
  
  if (belowThreshold.branches) {
    analyzeMetric(fileEntries, 'branches', COVERAGE_THRESHOLD - total.branches.pct);
  }
  
  if (belowThreshold.functions) {
    analyzeMetric(fileEntries, 'functions', COVERAGE_THRESHOLD - total.functions.pct);
  }
  
  if (belowThreshold.lines) {
    analyzeMetric(fileEntries, 'lines', COVERAGE_THRESHOLD - total.lines.pct);
  }
  
  console.log('\nRECOMMENDATIONS:');
  console.log('1. Focus on files with the lowest coverage first');
  console.log('2. Add tests for uncovered branches and edge cases');
  console.log('3. Ensure all exported functions have unit tests');
  console.log('4. Consider refactoring complex functions into smaller, more testable units');
  console.log('==================================================');
}

/**
 * Analyzes a specific coverage metric and identifies files that need improvement
 */
function analyzeMetric(fileEntries, metric, gap) {
  console.log(`\n${metric.toUpperCase()} COVERAGE (need ${gap.toFixed(2)}% more):`);
  
  // Sort files by coverage (ascending)
  const sortedFiles = fileEntries
    .filter(([_, data]) => data[metric] && data[metric].pct < COVERAGE_THRESHOLD)
    .sort(([_, a], [__, b]) => a[metric].pct - b[metric].pct);
  
  // Display the 5 files with lowest coverage
  const filesToShow = Math.min(5, sortedFiles.length);
  
  if (sortedFiles.length === 0) {
    console.log('  No individual files below threshold for this metric.');
    return;
  }
  
  console.log('  Files with lowest coverage:');
  for (let i = 0; i < filesToShow; i++) {
    const [file, data] = sortedFiles[i];
    const coverage = data[metric].pct.toFixed(2);
    const covered = data[metric].covered;
    const total = data[metric].total;
    console.log(`  ${i + 1}. ${file}: ${coverage}% (${covered}/${total})`);
  }
}

// Run the analysis
analyzeCoverage();
