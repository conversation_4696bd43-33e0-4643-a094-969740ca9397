import React, { useState } from 'react';

/**
 * Partner Network Role Selector Component
 *
 * This component allows users to explore and select collaborative roles from the NovaFuse Partner Network.
 * It can be used in demos to showcase how NovaFuse's solutions map to specific industry needs.
 */
const EcosystemSlotSelector = ({
  slots = [],
  selectedSlots = [],
  onSlotSelect = () => {},
  maxSelections = 3,
  showCategories = true
}) => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Extract unique categories from slots
  const categories = ['all', ...new Set(slots.map(slot => slot.category))];

  // Filter slots based on search term and active category
  const filteredSlots = slots.filter(slot => {
    const matchesSearch =
      slot.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      slot.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (slot.tags && slot.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())));

    const matchesCategory = activeCategory === 'all' || slot.category === activeCategory;

    return matchesSearch && matchesCategory;
  });

  // Handle slot selection
  const handleSlotSelect = (slotId) => {
    if (selectedSlots.includes(slotId)) {
      // If already selected, remove it
      onSlotSelect(selectedSlots.filter(id => id !== slotId));
    } else if (selectedSlots.length < maxSelections) {
      // If not selected and under max selections, add it
      onSlotSelect([...selectedSlots, slotId]);
    }
  };

  return (
    <div className="partner-network-role-selector">
      {/* Search and Filter */}
      <div className="mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          {/* Search */}
          <div className="w-full md:w-1/2">
            <div className="relative">
              <input
                type="text"
                placeholder="Search collaborative roles..."
                className="w-full bg-gray-800 border border-gray-700 rounded-lg py-2 px-4 pl-10 text-white"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <div className="absolute left-3 top-2.5 text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          {/* Category Filter */}
          {showCategories && (
            <div className="w-full md:w-auto">
              <div className="flex flex-wrap gap-2">
                {categories.map(category => (
                  <button
                    key={category}
                    className={`px-3 py-1 rounded-lg text-xs font-medium ${
                      activeCategory === category
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                    }`}
                    onClick={() => setActiveCategory(category)}
                  >
                    {category === 'all' ? 'All Categories' : category}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Selection Counter */}
      <div className="mb-4 text-sm text-gray-400">
        <span className="font-medium">{selectedSlots.length}</span> of <span className="font-medium">{maxSelections}</span> collaborative roles selected
      </div>

      {/* Collaborative Roles Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        {filteredSlots.map((slot) => (
          <div
            key={slot.id}
            className={`p-3 rounded-lg cursor-pointer border transition-all ${
              selectedSlots.includes(slot.id)
                ? 'border-blue-500 bg-blue-900 bg-opacity-30'
                : 'border-gray-700 hover:border-gray-500'
            }`}
            onClick={() => handleSlotSelect(slot.id)}
          >
            <div className="flex items-center mb-2">
              <span className="bg-blue-900 text-blue-300 text-xs font-bold px-2 py-1 rounded mr-2">
                {slot.id}
              </span>
              <span className="font-medium">{slot.name}</span>
            </div>
            <p className="text-sm text-gray-400">{slot.description}</p>

            {/* Tags */}
            {slot.tags && slot.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {slot.tags.map((tag, index) => (
                  <span key={index} className="bg-gray-800 text-gray-300 px-2 py-0.5 rounded text-xs">
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* No Results */}
      {filteredSlots.length === 0 && (
        <div className="bg-gray-800 rounded-lg p-6 text-center">
          <p className="text-gray-400 mb-2">No collaborative roles found matching your criteria.</p>
          <button
            className="text-blue-500 hover:text-blue-400 text-sm"
            onClick={() => {
              setSearchTerm('');
              setActiveCategory('all');
            }}
          >
            Clear filters
          </button>
        </div>
      )}
    </div>
  );
};

export default EcosystemSlotSelector;
