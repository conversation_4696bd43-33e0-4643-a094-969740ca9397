import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Stepper, 
  Step, 
  StepLabel, 
  Button,
  Divider
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import { useRouter } from 'next/router';
import AuthenticationConfig from '../../../components/partner-portal/authentication/AuthenticationConfig';

/**
 * Create New Connector Page
 * 
 * This page guides users through the process of creating a new connector.
 */
const CreateConnector = () => {
  const router = useRouter();
  const [activeStep, setActiveStep] = useState(0);
  const [connector, setConnector] = useState({
    metadata: {
      name: '',
      version: '1.0.0',
      category: '',
      description: '',
      author: 'NovaGRC',
      tags: [],
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    },
    authentication: {
      type: 'API_KEY',
      fields: {},
      testConnection: {
        endpoint: '/status',
        method: 'GET',
        expectedResponse: {
          status: 200
        }
      }
    },
    configuration: {
      baseUrl: '',
      headers: {},
      timeout: 30000,
      retryPolicy: {
        maxRetries: 3,
        backoffStrategy: 'exponential'
      }
    },
    endpoints: [],
    mappings: [],
    events: {
      webhooks: [],
      polling: []
    }
  });
  const [saving, setSaving] = useState(false);

  // Steps in the connector creation process
  const steps = [
    'Metadata',
    'Authentication',
    'Configuration',
    'Endpoints',
    'Mappings',
    'Events',
    'Review'
  ];

  // Handle next step
  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  // Handle previous step
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  // Handle saving the connector
  const handleSave = async () => {
    setSaving(true);
    
    try {
      // In a real implementation, this would call an API to save the connector
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Navigate to the connectors list
      router.push('/partner-portal/connectors');
    } catch (error) {
      console.error('Error saving connector:', error);
      setSaving(false);
    }
  };

  // Update connector data
  const updateConnector = (section, data) => {
    setConnector(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        ...data
      }
    }));
  };

  // Mock test connection function
  const testConnection = async (config) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Simulate success or failure based on credentials
    if (config.credentials && Object.keys(config.credentials).length > 0) {
      return {
        success: true,
        message: 'Connection successful!',
        data: {
          status: 200,
          statusText: 'OK',
          headers: {
            'content-type': 'application/json',
            'server': 'nginx'
          },
          data: {
            status: 'ok',
            version: '1.0.0'
          }
        }
      };
    } else {
      throw new Error('Invalid credentials');
    }
  };

  // Render the current step
  const renderStep = () => {
    switch (activeStep) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Metadata
            </Typography>
            <Typography variant="body1" paragraph>
              This step will be implemented next. For now, click Next to continue to Authentication.
            </Typography>
          </Box>
        );
      case 1:
        return (
          <AuthenticationConfig 
            connector={connector}
            updateConnector={updateConnector}
            testConnection={testConnection}
            isPartOfWizard={true}
            onNext={handleNext}
          />
        );
      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Configuration
            </Typography>
            <Typography variant="body1" paragraph>
              This step will be implemented next. For now, click Next to continue.
            </Typography>
          </Box>
        );
      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Endpoints
            </Typography>
            <Typography variant="body1" paragraph>
              This step will be implemented next. For now, click Next to continue.
            </Typography>
          </Box>
        );
      case 4:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Mappings
            </Typography>
            <Typography variant="body1" paragraph>
              This step will be implemented next. For now, click Next to continue.
            </Typography>
          </Box>
        );
      case 5:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Events
            </Typography>
            <Typography variant="body1" paragraph>
              This step will be implemented next. For now, click Next to continue.
            </Typography>
          </Box>
        );
      case 6:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review
            </Typography>
            <Typography variant="body1" paragraph>
              This step will be implemented next. For now, click Finish to save the connector.
            </Typography>
          </Box>
        );
      default:
        return 'Unknown step';
    }
  };

  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Button 
            startIcon={<ArrowBackIcon />} 
            sx={{ mr: 2 }}
            onClick={() => router.push('/partner-portal')}
          >
            Back
          </Button>
          <Typography variant="h4" component="h1">
            Create New Connector
          </Typography>
        </Box>
        <Button 
          variant="contained" 
          startIcon={<SaveIcon />}
          onClick={handleSave}
          disabled={saving || activeStep < steps.length - 1}
        >
          {saving ? 'Saving...' : 'Save Connector'}
        </Button>
      </Box>
      
      <Paper sx={{ p: 3, mb: 4, backgroundColor: 'background.paper' }}>
        <Stepper activeStep={activeStep} alternativeLabel>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </Paper>
      
      <Paper sx={{ p: 3, backgroundColor: 'background.paper' }}>
        {activeStep === steps.length ? (
          <Box>
            <Typography sx={{ mt: 2, mb: 1 }}>
              All steps completed - your connector is ready to use!
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
              <Box sx={{ flex: '1 1 auto' }} />
              <Button onClick={() => router.push('/partner-portal/connectors')}>
                Go to Connectors
              </Button>
            </Box>
          </Box>
        ) : (
          <Box>
            {renderStep()}
            
            {activeStep !== 1 && (
              <Box sx={{ display: 'flex', flexDirection: 'row', pt: 4 }}>
                <Button
                  color="inherit"
                  disabled={activeStep === 0}
                  onClick={handleBack}
                  sx={{ mr: 1 }}
                >
                  Back
                </Button>
                <Box sx={{ flex: '1 1 auto' }} />
                <Button onClick={handleNext}>
                  {activeStep === steps.length - 1 ? 'Finish' : 'Next'}
                </Button>
              </Box>
            )}
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default CreateConnector;
