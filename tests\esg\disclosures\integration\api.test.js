const request = require('supertest');
const express = require('express');
const router = require('../../../../apis/esg/disclosures/routes');
const models = require('../../../../apis/esg/disclosures/models');

// Mock the models
jest.mock('../../../../apis/esg/disclosures/models', () => ({
  esgDisclosures: [
    {
      id: 'esg-d-12345678',
      title: 'Annual Carbon Emissions Disclosure',
      description: 'Disclosure of annual carbon emissions across all operations',
      category: 'environmental',
      type: 'regulatory',
      framework: 'GRI',
      frameworkReference: 'GRI 305',
      status: 'published',
      period: {
        startDate: '2022-01-01',
        endDate: '2022-12-31'
      },
      publishDate: '2023-03-15',
      content: {
        summary: 'Total carbon emissions for 2022 were 8,500 metric tons, a 15% reduction from 2021.',
        metrics: [
          {
            name: 'Scope 1 Emissions',
            value: 3200,
            unit: 'metric-tons',
            previousValue: 3800,
            change: -15.8
          },
          {
            name: 'Scope 2 Emissions',
            value: 5300,
            unit: 'metric-tons',
            previousValue: 6200,
            change: -14.5
          }
        ],
        narrative: 'Our emissions reduction initiatives have resulted in significant progress...',
        attachments: [
          {
            id: 'att-12345',
            name: 'Detailed Emissions Report',
            type: 'pdf',
            url: 'https://example.com/reports/emissions-2022.pdf'
          }
        ]
      },
      owner: 'Sustainability Team',
      contributors: ['Operations', 'Facilities'],
      reviewers: ['Executive Board'],
      relatedTargets: ['esg-t-12345678'],
      createdAt: '2023-01-15T00:00:00Z',
      updatedAt: '2023-03-15T00:00:00Z'
    },
    {
      id: 'esg-d-87654321',
      title: 'Diversity and Inclusion Report',
      description: 'Annual disclosure of diversity and inclusion metrics and initiatives',
      category: 'social',
      type: 'voluntary',
      framework: 'SASB',
      frameworkReference: 'SASB SV-PS-330a.1',
      status: 'draft',
      period: {
        startDate: '2022-01-01',
        endDate: '2022-12-31'
      },
      content: {
        summary: 'Our workforce diversity increased across all categories in 2022.',
        metrics: [
          {
            name: 'Gender Diversity',
            value: 42,
            unit: 'percentage',
            previousValue: 38,
            change: 10.5
          },
          {
            name: 'Ethnic Diversity',
            value: 35,
            unit: 'percentage',
            previousValue: 30,
            change: 16.7
          }
        ],
        narrative: 'Our diversity and inclusion initiatives have focused on...',
        attachments: []
      },
      owner: 'HR Team',
      contributors: ['Diversity Council'],
      reviewers: [],
      relatedTargets: ['esg-t-87654321'],
      createdAt: '2023-02-01T00:00:00Z',
      updatedAt: '2023-02-15T00:00:00Z'
    }
  ],
  disclosureTemplates: [
    {
      id: 'template-12345',
      name: 'Carbon Emissions Disclosure Template',
      description: 'Template for annual carbon emissions disclosure',
      category: 'environmental',
      framework: 'GRI',
      frameworkReference: 'GRI 305',
      structure: {
        sections: [
          {
            title: 'Executive Summary',
            description: 'Brief summary of emissions performance',
            required: true,
            order: 1
          },
          {
            title: 'Emissions Data',
            description: 'Detailed breakdown of emissions by scope and source',
            required: true,
            order: 2
          },
          {
            title: 'Methodology',
            description: 'Description of measurement methodology and standards used',
            required: true,
            order: 3
          },
          {
            title: 'Reduction Initiatives',
            description: 'Description of emissions reduction initiatives',
            required: false,
            order: 4
          }
        ],
        metrics: [
          {
            name: 'Scope 1 Emissions',
            unit: 'metric-tons',
            required: true
          },
          {
            name: 'Scope 2 Emissions',
            unit: 'metric-tons',
            required: true
          },
          {
            name: 'Scope 3 Emissions',
            unit: 'metric-tons',
            required: false
          }
        ]
      },
      createdAt: '2022-12-01T00:00:00Z',
      updatedAt: '2022-12-01T00:00:00Z'
    }
  ]
}));

// Create a test Express app
const app = express();
app.use(express.json());
app.use('/governance/esg/disclosures', router);

describe('ESG Disclosures API Integration Tests', () => {
  describe('GET /governance/esg/disclosures', () => {
    it('should return all disclosures with default pagination', async () => {
      const response = await request(app).get('/governance/esg/disclosures');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination).toEqual({
        total: 2,
        page: 1,
        limit: 10,
        pages: 1
      });
    });

    it('should filter disclosures by category', async () => {
      const response = await request(app).get('/governance/esg/disclosures?category=environmental');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].category).toBe('environmental');
    });

    it('should filter disclosures by status', async () => {
      const response = await request(app).get('/governance/esg/disclosures?status=draft');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].status).toBe('draft');
    });

    it('should filter disclosures by framework', async () => {
      const response = await request(app).get('/governance/esg/disclosures?framework=GRI');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].framework).toBe('GRI');
    });
  });

  describe('GET /governance/esg/disclosures/:id', () => {
    it('should return a specific disclosure by ID', async () => {
      const response = await request(app).get('/governance/esg/disclosures/esg-d-12345678');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('esg-d-12345678');
      expect(response.body.data.title).toBe('Annual Carbon Emissions Disclosure');
    });

    it('should return 404 if disclosure not found', async () => {
      const response = await request(app).get('/governance/esg/disclosures/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /governance/esg/disclosures', () => {
    it('should create a new disclosure', async () => {
      const newDisclosure = {
        title: 'Water Usage Disclosure',
        description: 'Annual disclosure of water usage across all facilities',
        category: 'environmental',
        type: 'voluntary',
        framework: 'GRI',
        frameworkReference: 'GRI 303',
        status: 'draft',
        period: {
          startDate: '2022-01-01',
          endDate: '2022-12-31'
        },
        content: {
          summary: 'Total water usage for 2022 was 1.2 million gallons, a 5% reduction from 2021.',
          metrics: [
            {
              name: 'Total Water Usage',
              value: 1200000,
              unit: 'gallons',
              previousValue: 1260000,
              change: -4.8
            }
          ],
          narrative: 'Our water conservation initiatives have resulted in...'
        },
        owner: 'Facilities Team'
      };

      const response = await request(app)
        .post('/governance/esg/disclosures')
        .send(newDisclosure);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'ESG disclosure created successfully');
      expect(response.body.data.title).toBe('Water Usage Disclosure');
      expect(response.body.data.category).toBe('environmental');
    });

    it('should return 400 for invalid input', async () => {
      const invalidDisclosure = {
        // Missing required fields
        description: 'Invalid disclosure'
      };

      const response = await request(app)
        .post('/governance/esg/disclosures')
        .send(invalidDisclosure);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /governance/esg/disclosures/:id', () => {
    it('should update an existing disclosure', async () => {
      const updatedDisclosure = {
        title: 'Updated Disclosure Title',
        status: 'published',
        publishDate: '2023-04-01'
      };

      const response = await request(app)
        .put('/governance/esg/disclosures/esg-d-12345678')
        .send(updatedDisclosure);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'ESG disclosure updated successfully');
      expect(response.body.data.title).toBe('Updated Disclosure Title');
      expect(response.body.data.status).toBe('published');
      expect(response.body.data.publishDate).toBe('2023-04-01');
    });

    it('should return 404 if disclosure not found', async () => {
      const response = await request(app)
        .put('/governance/esg/disclosures/non-existent-id')
        .send({ title: 'Updated Title' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('DELETE /governance/esg/disclosures/:id', () => {
    it('should delete an existing disclosure', async () => {
      const response = await request(app).delete('/governance/esg/disclosures/esg-d-12345678');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'ESG disclosure deleted successfully');
    });

    it('should return 404 if disclosure not found', async () => {
      const response = await request(app).delete('/governance/esg/disclosures/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /governance/esg/disclosures/:id/attachments', () => {
    it('should return attachments for a specific disclosure', async () => {
      const response = await request(app).get('/governance/esg/disclosures/esg-d-12345678/attachments');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].id).toBe('att-12345');
    });

    it('should return 404 if disclosure not found', async () => {
      const response = await request(app).get('/governance/esg/disclosures/non-existent-id/attachments');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /governance/esg/disclosures/:id/attachments', () => {
    it('should add a new attachment to a disclosure', async () => {
      const newAttachment = {
        name: 'Supplementary Data',
        type: 'xlsx',
        url: 'https://example.com/reports/emissions-data-2022.xlsx'
      };

      const response = await request(app)
        .post('/governance/esg/disclosures/esg-d-12345678/attachments')
        .send(newAttachment);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Attachment added successfully');
      expect(response.body.data.name).toBe('Supplementary Data');
      expect(response.body.data.type).toBe('xlsx');
    });

    it('should return 400 for invalid input', async () => {
      const invalidAttachment = {
        // Missing required fields
        type: 'pdf'
      };

      const response = await request(app)
        .post('/governance/esg/disclosures/esg-d-12345678/attachments')
        .send(invalidAttachment);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('DELETE /governance/esg/disclosures/:id/attachments/:attachmentId', () => {
    it('should remove an attachment from a disclosure', async () => {
      const response = await request(app).delete('/governance/esg/disclosures/esg-d-12345678/attachments/att-12345');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Attachment removed successfully');
    });

    it('should return 404 if attachment not found', async () => {
      const response = await request(app).delete('/governance/esg/disclosures/esg-d-12345678/attachments/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /governance/esg/disclosures/templates', () => {
    it('should return all disclosure templates', async () => {
      const response = await request(app).get('/governance/esg/disclosures/templates');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].name).toBe('Carbon Emissions Disclosure Template');
    });
  });

  describe('GET /governance/esg/disclosures/templates/:id', () => {
    it('should return a specific disclosure template by ID', async () => {
      const response = await request(app).get('/governance/esg/disclosures/templates/template-12345');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('template-12345');
      expect(response.body.data.name).toBe('Carbon Emissions Disclosure Template');
    });

    it('should return 404 if template not found', async () => {
      const response = await request(app).get('/governance/esg/disclosures/templates/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /governance/esg/disclosures/templates', () => {
    it('should create a new disclosure template', async () => {
      const newTemplate = {
        name: 'Diversity and Inclusion Disclosure Template',
        description: 'Template for diversity and inclusion disclosure',
        category: 'social',
        framework: 'SASB',
        frameworkReference: 'SASB SV-PS-330a.1',
        structure: {
          sections: [
            {
              title: 'Executive Summary',
              description: 'Brief summary of diversity metrics',
              required: true,
              order: 1
            },
            {
              title: 'Diversity Data',
              description: 'Detailed breakdown of diversity metrics',
              required: true,
              order: 2
            }
          ],
          metrics: [
            {
              name: 'Gender Diversity',
              unit: 'percentage',
              required: true
            },
            {
              name: 'Ethnic Diversity',
              unit: 'percentage',
              required: true
            }
          ]
        }
      };

      const response = await request(app)
        .post('/governance/esg/disclosures/templates')
        .send(newTemplate);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Disclosure template created successfully');
      expect(response.body.data.name).toBe('Diversity and Inclusion Disclosure Template');
      expect(response.body.data.category).toBe('social');
    });

    it('should return 400 for invalid input', async () => {
      const invalidTemplate = {
        // Missing required fields
        description: 'Invalid template'
      };

      const response = await request(app)
        .post('/governance/esg/disclosures/templates')
        .send(invalidTemplate);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /governance/esg/disclosures/templates/:id', () => {
    it('should update an existing disclosure template', async () => {
      const updatedTemplate = {
        name: 'Updated Template Name',
        structure: {
          sections: [
            {
              title: 'New Section',
              description: 'New section description',
              required: true,
              order: 5
            }
          ]
        }
      };

      const response = await request(app)
        .put('/governance/esg/disclosures/templates/template-12345')
        .send(updatedTemplate);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Disclosure template updated successfully');
      expect(response.body.data.name).toBe('Updated Template Name');
    });

    it('should return 404 if template not found', async () => {
      const response = await request(app)
        .put('/governance/esg/disclosures/templates/non-existent-id')
        .send({ name: 'Updated Name' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('DELETE /governance/esg/disclosures/templates/:id', () => {
    it('should delete an existing disclosure template', async () => {
      const response = await request(app).delete('/governance/esg/disclosures/templates/template-12345');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Disclosure template deleted successfully');
    });

    it('should return 404 if template not found', async () => {
      const response = await request(app).delete('/governance/esg/disclosures/templates/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });
});
