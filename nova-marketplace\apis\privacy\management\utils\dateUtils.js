/**
 * Date Utilities
 * 
 * This file contains utility functions for working with dates.
 */

/**
 * Calculate the due date for a data subject request
 * @param {string} requestType - Type of request
 * @param {string} jurisdiction - Jurisdiction (e.g., 'gdpr', 'ccpa')
 * @returns {Date} Due date
 */
const calculateDsrDueDate = (requestType, jurisdiction = 'gdpr') => {
  const now = new Date();
  let daysToAdd = 30; // Default for GDPR
  
  // Adjust based on jurisdiction
  if (jurisdiction === 'ccpa') {
    daysToAdd = 45; // CCPA allows 45 days
  } else if (jurisdiction === 'lgpd') {
    daysToAdd = 15; // LGPD (Brazil) requires 15 days
  }
  
  // Adjust based on request type
  if (requestType === 'access' && jurisdiction === 'gdpr') {
    daysToAdd = 30; // GDPR requires 30 days for access requests
  } else if (requestType === 'erasure' && jurisdiction === 'gdpr') {
    daysToAdd = 30; // GDPR requires 30 days for erasure requests
  }
  
  const dueDate = new Date(now);
  dueDate.setDate(dueDate.getDate() + daysToAdd);
  
  return dueDate;
};

/**
 * Calculate the expiry date for a consent record
 * @param {string} consentType - Type of consent
 * @param {number} validityPeriod - Validity period in days (optional)
 * @returns {Date|null} Expiry date or null if consent doesn't expire
 */
const calculateConsentExpiryDate = (consentType, validityPeriod = null) => {
  // Some consent types might not expire
  if (consentType === 'research' || consentType === 'legal_obligation') {
    return null;
  }
  
  // If a validity period is provided, use it
  if (validityPeriod) {
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + validityPeriod);
    return expiryDate;
  }
  
  // Default expiry periods based on consent type
  const expiryPeriods = {
    'marketing': 730, // 2 years
    'analytics': 365, // 1 year
    'profiling': 365, // 1 year
    'third_party': 365, // 1 year
    'other': 365 // 1 year
  };
  
  const expiryDays = expiryPeriods[consentType] || 365;
  const expiryDate = new Date();
  expiryDate.setDate(expiryDate.getDate() + expiryDays);
  
  return expiryDate;
};

/**
 * Calculate the notification deadline for a data breach
 * @param {Date} detectionDate - Date when the breach was detected
 * @param {string} jurisdiction - Jurisdiction (e.g., 'gdpr', 'ccpa')
 * @returns {Date} Notification deadline
 */
const calculateBreachNotificationDeadline = (detectionDate, jurisdiction = 'gdpr') => {
  const detection = new Date(detectionDate);
  
  // GDPR requires notification within 72 hours
  if (jurisdiction === 'gdpr') {
    const deadline = new Date(detection);
    deadline.setHours(deadline.getHours() + 72);
    return deadline;
  }
  
  // CCPA doesn't specify a timeframe, but "expeditiously"
  if (jurisdiction === 'ccpa') {
    const deadline = new Date(detection);
    deadline.setDate(deadline.getDate() + 5); // Reasonable timeframe
    return deadline;
  }
  
  // Default to 72 hours
  const deadline = new Date(detection);
  deadline.setHours(deadline.getHours() + 72);
  return deadline;
};

/**
 * Format a date as ISO string without milliseconds
 * @param {Date} date - Date to format
 * @returns {string} Formatted date
 */
const formatIsoDate = (date) => {
  if (!date) return null;
  return new Date(date).toISOString().split('.')[0] + 'Z';
};

/**
 * Format a date as a human-readable string
 * @param {Date} date - Date to format
 * @returns {string} Formatted date
 */
const formatHumanDate = (date) => {
  if (!date) return null;
  
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZoneName: 'short'
  };
  
  return new Date(date).toLocaleDateString('en-US', options);
};

/**
 * Calculate the difference between two dates in days
 * @param {Date} date1 - First date
 * @param {Date} date2 - Second date
 * @returns {number} Difference in days
 */
const daysBetween = (date1, date2) => {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  const diffTime = Math.abs(d2 - d1);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

/**
 * Check if a date is in the past
 * @param {Date} date - Date to check
 * @returns {boolean} Whether the date is in the past
 */
const isInPast = (date) => {
  return new Date(date) < new Date();
};

/**
 * Check if a date is in the future
 * @param {Date} date - Date to check
 * @returns {boolean} Whether the date is in the future
 */
const isInFuture = (date) => {
  return new Date(date) > new Date();
};

/**
 * Get the start and end dates for a period
 * @param {string} period - Period (e.g., 'last-7-days', 'last-30-days', 'last-90-days', 'last-12-months', 'year-to-date')
 * @returns {Object} Start and end dates
 */
const getPeriodDates = (period) => {
  const endDate = new Date();
  let startDate;
  
  switch (period) {
    case 'last-7-days':
      startDate = new Date(endDate);
      startDate.setDate(endDate.getDate() - 7);
      break;
    case 'last-30-days':
      startDate = new Date(endDate);
      startDate.setDate(endDate.getDate() - 30);
      break;
    case 'last-90-days':
      startDate = new Date(endDate);
      startDate.setDate(endDate.getDate() - 90);
      break;
    case 'last-12-months':
      startDate = new Date(endDate);
      startDate.setMonth(endDate.getMonth() - 12);
      break;
    case 'year-to-date':
      startDate = new Date(endDate.getFullYear(), 0, 1); // January 1st of current year
      break;
    default:
      startDate = new Date(endDate);
      startDate.setDate(endDate.getDate() - 30); // Default to last 30 days
  }
  
  return { startDate, endDate };
};

module.exports = {
  calculateDsrDueDate,
  calculateConsentExpiryDate,
  calculateBreachNotificationDeadline,
  formatIsoDate,
  formatHumanDate,
  daysBetween,
  isInPast,
  isInFuture,
  getPeriodDates
};
