import React, { useState } from 'react';

const ConsentForm = ({ 
  agreementTitle, 
  agreementVersion, 
  onConsent,
  isAgreementRead = false,
  isSubmitting = false
}) => {
  const [isChecked, setIsChecked] = useState(false);
  
  const handleSubmit = (e) => {
    e.preventDefault();
    if (isChecked) {
      onConsent();
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="mt-6 max-w-4xl mx-auto">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="agreement-consent"
              type="checkbox"
              checked={isChecked}
              onChange={() => setIsChecked(!isChecked)}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              disabled={!isAgreementRead}
            />
          </div>
          <label htmlFor="agreement-consent" className="ml-3 text-sm font-medium text-gray-700">
            I have read and agree to the {agreementTitle} (v{agreementVersion})
          </label>
        </div>
        
        {!isAgreementRead && (
          <p className="mt-2 text-xs text-amber-600">
            Please review the entire agreement before accepting
          </p>
        )}
      </div>
      
      <div className="flex justify-end">
        <button
          type="submit"
          disabled={!isChecked || isSubmitting}
          className={`px-6 py-2 rounded-lg font-medium ${
            !isChecked || isSubmitting
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isSubmitting ? 'Processing...' : 'I Agree & Continue'}
        </button>
      </div>
      
      <div className="mt-4 text-xs text-gray-500 text-center">
        By clicking "I Agree & Continue", you acknowledge that you have read, understood, 
        and agree to be bound by the terms of this agreement.
      </div>
    </form>
  );
};

export default ConsentForm;
