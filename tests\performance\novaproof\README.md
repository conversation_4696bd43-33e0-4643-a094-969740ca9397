# NovaProof Performance Tests

This directory contains performance tests for the NovaProof component.

## Purpose

Performance tests ensure that NovaProof meets its performance requirements, including:

- Blockchain verification speed
- Evidence processing throughput
- Cryptographic operation efficiency
- Storage and retrieval performance
- Merkle tree operations

## Test Categories

1. **Blockchain Verification Performance**: Tests for blockchain verification operations
2. **Evidence Processing Performance**: Tests for evidence collection and processing
3. **Cryptographic Performance**: Tests for cryptographic operations
4. **Storage Performance**: Tests for evidence storage and retrieval
5. **Merkle Tree Performance**: Tests for Merkle tree operations

## Running Tests

```bash
# Run all NovaProof performance tests
npm run test:novaproof:performance

# Run specific test
npx jest tests/performance/novaproof/blockchain-verification.perf.test.js
```

## Performance Metrics

The tests measure the following metrics:

- **Verification Time**: Time taken to verify evidence on the blockchain
- **Throughput**: Number of verifications per second
- **Latency**: Time taken to respond to verification requests
- **Storage Efficiency**: Storage space used per evidence item
- **Cryptographic Operation Speed**: Time taken for cryptographic operations

## Performance Requirements

- Evidence verification: < 500ms per item
- Batch verification: < 5s for 100 items
- Merkle proof generation: < 100ms
- Merkle proof verification: < 50ms
- API response time: < 200ms for 95% of requests

## Adding New Tests

When adding new performance tests, follow these guidelines:

1. Use the `measureVerificationPerformance` utility from `tests/utils/novaproof-test-utils.js`
2. Include baseline measurements for comparison
3. Test with various load levels (single item, small batch, large batch)
4. Document performance expectations in test comments
5. Use descriptive test names that indicate what aspect of performance is being tested
