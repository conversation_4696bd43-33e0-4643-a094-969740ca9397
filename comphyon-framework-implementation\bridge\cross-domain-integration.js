/**
 * Cross-Domain Integration
 * 
 * This module implements the Cross-Domain Integration component of the Bridge.
 * It integrates data and events across cyber, financial, and biological domains.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * CrossDomainIntegration class
 */
class CrossDomainIntegration extends EventEmitter {
  /**
   * Create a new CrossDomainIntegration instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      enableMetrics: true,
      historySize: 100, // Number of historical data points to keep
      ...options
    };
    
    // Initialize state
    this.state = {
      domainData: {
        cyber: new Map(), // key -> data
        financial: new Map(), // key -> data
        biological: new Map() // key -> data
      },
      crossDomainPatterns: new Map(), // id -> pattern
      integrationHistory: [],
      isRunning: false,
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalUpdates: 0,
      dataPointsProcessed: 0,
      patternsDetected: 0,
      crossDomainEventsGenerated: 0
    };
    
    if (this.options.enableLogging) {
      console.log('CrossDomainIntegration initialized');
    }
  }
  
  /**
   * Start the cross-domain integration
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('CrossDomainIntegration is already running');
      }
      return false;
    }
    
    this.state.isRunning = true;
    
    if (this.options.enableLogging) {
      console.log('CrossDomainIntegration started');
    }
    
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the cross-domain integration
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('CrossDomainIntegration is not running');
      }
      return false;
    }
    
    this.state.isRunning = false;
    
    if (this.options.enableLogging) {
      console.log('CrossDomainIntegration stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Process domain data
   * @param {string} domain - Domain (cyber, financial, biological)
   * @param {string} key - Data key
   * @param {*} data - Data value
   * @param {Object} metadata - Additional metadata
   * @returns {Array} - Detected patterns
   */
  processDomainData(domain, key, data, metadata = {}) {
    const startTime = performance.now();
    
    // Validate domain
    if (!['cyber', 'financial', 'biological'].includes(domain)) {
      throw new Error(`Invalid domain: ${domain}`);
    }
    
    if (!key) {
      throw new Error('Data key is required');
    }
    
    // Store data
    this.state.domainData[domain].set(key, {
      value: data,
      timestamp: Date.now(),
      metadata
    });
    
    this.state.lastUpdateTime = Date.now();
    
    // Detect patterns
    const detectedPatterns = this._detectCrossDomainPatterns(domain, key, data);
    
    // Add to history
    this.state.integrationHistory.push({
      domain,
      key,
      timestamp: Date.now(),
      patternsDetected: detectedPatterns.length,
      metadata
    });
    
    // Limit history size
    if (this.state.integrationHistory.length > this.options.historySize) {
      this.state.integrationHistory.shift();
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.dataPointsProcessed++;
    
    // Emit update event
    this.emit('data-processed', {
      domain,
      key,
      data,
      detectedPatterns,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging && detectedPatterns.length > 0) {
      console.log(`CrossDomainIntegration: Detected ${detectedPatterns.length} patterns from ${domain} data`);
    }
    
    return detectedPatterns;
  }
  
  /**
   * Process domain event
   * @param {string} domain - Domain (cyber, financial, biological)
   * @param {Object} event - Domain event
   * @returns {Array} - Cross-domain events
   */
  processDomainEvent(domain, event) {
    const startTime = performance.now();
    
    // Validate domain
    if (!['cyber', 'financial', 'biological'].includes(domain)) {
      throw new Error(`Invalid domain: ${domain}`);
    }
    
    if (!event || typeof event !== 'object') {
      throw new Error('Event must be an object');
    }
    
    // Generate cross-domain events
    const crossDomainEvents = this._generateCrossDomainEvents(domain, event);
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.crossDomainEventsGenerated += crossDomainEvents.length;
    
    // Emit events
    for (const crossEvent of crossDomainEvents) {
      this.emit('cross-domain-event', crossEvent);
      
      if (this.options.enableLogging) {
        console.log(`CrossDomainIntegration: Generated cross-domain event from ${domain} event`);
      }
    }
    
    return crossDomainEvents;
  }
  
  /**
   * Register cross-domain pattern
   * @param {Object} pattern - Pattern definition
   * @returns {Object} - Registered pattern
   */
  registerPattern(pattern) {
    if (!pattern || typeof pattern !== 'object') {
      throw new Error('Pattern must be an object');
    }
    
    if (!pattern.id) {
      pattern.id = `pattern-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    
    // Set default values
    pattern = {
      name: 'Generic Pattern',
      description: 'Generic cross-domain pattern',
      domains: ['cyber', 'financial', 'biological'],
      conditions: [], // Array of condition objects
      action: 'detect', // detect, alert, mitigate
      priority: 'medium', // low, medium, high, critical
      registeredAt: Date.now(),
      ...pattern
    };
    
    // Add to state
    this.state.crossDomainPatterns.set(pattern.id, pattern);
    
    // Emit event
    this.emit('pattern-registered', pattern);
    
    if (this.options.enableLogging) {
      console.log(`CrossDomainIntegration: Registered pattern ${pattern.id} (${pattern.name})`);
    }
    
    return pattern;
  }
  
  /**
   * Unregister cross-domain pattern
   * @param {string} patternId - Pattern ID
   * @returns {boolean} - Success status
   */
  unregisterPattern(patternId) {
    if (!patternId || !this.state.crossDomainPatterns.has(patternId)) {
      return false;
    }
    
    // Get pattern
    const pattern = this.state.crossDomainPatterns.get(patternId);
    
    // Remove from state
    this.state.crossDomainPatterns.delete(patternId);
    
    // Emit event
    this.emit('pattern-unregistered', pattern);
    
    if (this.options.enableLogging) {
      console.log(`CrossDomainIntegration: Unregistered pattern ${patternId}`);
    }
    
    return true;
  }
  
  /**
   * Get domain data
   * @param {string} domain - Domain (cyber, financial, biological)
   * @param {string} key - Optional data key
   * @returns {*} - Domain data
   */
  getDomainData(domain, key) {
    // Validate domain
    if (!['cyber', 'financial', 'biological'].includes(domain)) {
      throw new Error(`Invalid domain: ${domain}`);
    }
    
    if (key) {
      return this.state.domainData[domain].get(key);
    }
    
    return Object.fromEntries(this.state.domainData[domain]);
  }
  
  /**
   * Get cross-domain patterns
   * @param {string} domain - Optional domain filter
   * @returns {Array} - Cross-domain patterns
   */
  getCrossDomainPatterns(domain) {
    const patterns = Array.from(this.state.crossDomainPatterns.values());
    
    if (domain) {
      return patterns.filter(p => p.domains.includes(domain));
    }
    
    return patterns;
  }
  
  /**
   * Get integration history
   * @param {number} limit - Maximum number of history items to return
   * @returns {Array} - Integration history
   */
  getIntegrationHistory(limit = 10) {
    return this.state.integrationHistory.slice(0, limit);
  }
  
  /**
   * Get current state
   * @returns {Object} - Current state
   */
  getState() {
    return {
      domainDataCounts: {
        cyber: this.state.domainData.cyber.size,
        financial: this.state.domainData.financial.size,
        biological: this.state.domainData.biological.size
      },
      patternCount: this.state.crossDomainPatterns.size,
      integrationHistory: [...this.state.integrationHistory],
      isRunning: this.state.isRunning,
      lastUpdateTime: this.state.lastUpdateTime
    };
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Detect cross-domain patterns
   * @param {string} domain - Source domain
   * @param {string} key - Data key
   * @param {*} data - Data value
   * @returns {Array} - Detected patterns
   * @private
   */
  _detectCrossDomainPatterns(domain, key, data) {
    const detectedPatterns = [];
    
    // Get all patterns that include this domain
    const relevantPatterns = Array.from(this.state.crossDomainPatterns.values())
      .filter(pattern => pattern.domains.includes(domain));
    
    for (const pattern of relevantPatterns) {
      // Check if pattern conditions are met
      const isPatternDetected = this._evaluatePatternConditions(pattern, domain, key, data);
      
      if (isPatternDetected) {
        // Create pattern detection result
        const detectionResult = {
          patternId: pattern.id,
          patternName: pattern.name,
          description: pattern.description,
          priority: pattern.priority,
          domains: pattern.domains,
          triggeringDomain: domain,
          triggeringKey: key,
          triggeringData: data,
          timestamp: Date.now()
        };
        
        detectedPatterns.push(detectionResult);
        this.metrics.patternsDetected++;
        
        // Emit pattern detection event
        this.emit('pattern-detected', detectionResult);
        
        // Execute pattern action
        this._executePatternAction(pattern, detectionResult);
      }
    }
    
    return detectedPatterns;
  }
  
  /**
   * Evaluate pattern conditions
   * @param {Object} pattern - Pattern definition
   * @param {string} domain - Source domain
   * @param {string} key - Data key
   * @param {*} data - Data value
   * @returns {boolean} - Whether conditions are met
   * @private
   */
  _evaluatePatternConditions(pattern, domain, key, data) {
    // If no conditions, pattern is not detected
    if (!pattern.conditions || pattern.conditions.length === 0) {
      return false;
    }
    
    // Check each condition
    for (const condition of pattern.conditions) {
      // Skip conditions for other domains if they don't match the current domain
      if (condition.domain && condition.domain !== domain) {
        continue;
      }
      
      // Skip conditions for other keys if they don't match the current key
      if (condition.key && condition.key !== key) {
        continue;
      }
      
      // Check value condition
      if (condition.operator && condition.value !== undefined) {
        const conditionMet = this._evaluateValueCondition(data, condition.operator, condition.value);
        
        if (conditionMet) {
          return true;
        }
      }
      
      // Check cross-domain condition
      if (condition.crossDomainKey && condition.crossDomain) {
        const crossDomainData = this.state.domainData[condition.crossDomain].get(condition.crossDomainKey);
        
        if (crossDomainData && condition.crossDomainOperator && condition.crossDomainValue !== undefined) {
          const conditionMet = this._evaluateValueCondition(
            crossDomainData.value,
            condition.crossDomainOperator,
            condition.crossDomainValue
          );
          
          if (conditionMet) {
            return true;
          }
        }
      }
    }
    
    return false;
  }
  
  /**
   * Evaluate value condition
   * @param {*} value - Value to check
   * @param {string} operator - Comparison operator
   * @param {*} threshold - Threshold value
   * @returns {boolean} - Whether condition is met
   * @private
   */
  _evaluateValueCondition(value, operator, threshold) {
    switch (operator) {
      case 'eq':
        return value === threshold;
        
      case 'ne':
        return value !== threshold;
        
      case 'gt':
        return value > threshold;
        
      case 'gte':
        return value >= threshold;
        
      case 'lt':
        return value < threshold;
        
      case 'lte':
        return value <= threshold;
        
      case 'contains':
        return value.includes(threshold);
        
      case 'startsWith':
        return value.startsWith(threshold);
        
      case 'endsWith':
        return value.endsWith(threshold);
        
      default:
        return false;
    }
  }
  
  /**
   * Execute pattern action
   * @param {Object} pattern - Pattern definition
   * @param {Object} detectionResult - Pattern detection result
   * @private
   */
  _executePatternAction(pattern, detectionResult) {
    switch (pattern.action) {
      case 'detect':
        // Just detect the pattern (already done)
        break;
        
      case 'alert':
        // Emit alert event
        this.emit('pattern-alert', {
          ...detectionResult,
          alertType: pattern.alertType || 'info',
          alertMessage: pattern.alertMessage || `Pattern ${pattern.name} detected`
        });
        break;
        
      case 'mitigate':
        // Emit mitigation event
        this.emit('pattern-mitigation', {
          ...detectionResult,
          mitigationType: pattern.mitigationType || 'generic',
          mitigationAction: pattern.mitigationAction || 'log'
        });
        break;
        
      default:
        // Unknown action, just log
        if (this.options.enableLogging) {
          console.log(`CrossDomainIntegration: Unknown action ${pattern.action} for pattern ${pattern.id}`);
        }
    }
  }
  
  /**
   * Generate cross-domain events
   * @param {string} sourceDomain - Source domain
   * @param {Object} sourceEvent - Source event
   * @returns {Array} - Cross-domain events
   * @private
   */
  _generateCrossDomainEvents(sourceDomain, sourceEvent) {
    const crossDomainEvents = [];
    
    // Get target domains
    const targetDomains = ['cyber', 'financial', 'biological'].filter(d => d !== sourceDomain);
    
    for (const targetDomain of targetDomains) {
      // Create cross-domain event
      const crossDomainEvent = {
        id: `cross-${sourceDomain}-${targetDomain}-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
        sourceDomain,
        targetDomain,
        sourceEvent,
        timestamp: Date.now(),
        impact: this._estimateEventImpact(sourceDomain, sourceEvent, targetDomain)
      };
      
      crossDomainEvents.push(crossDomainEvent);
    }
    
    return crossDomainEvents;
  }
  
  /**
   * Estimate event impact on target domain
   * @param {string} sourceDomain - Source domain
   * @param {Object} sourceEvent - Source event
   * @param {string} targetDomain - Target domain
   * @returns {Object} - Impact assessment
   * @private
   */
  _estimateEventImpact(sourceDomain, sourceEvent, targetDomain) {
    // Default impact
    const impact = {
      level: 'medium', // low, medium, high, critical
      confidence: 0.5, // 0-1
      description: `Estimated impact of ${sourceDomain} event on ${targetDomain} domain`
    };
    
    // Apply domain-specific logic
    switch (`${sourceDomain}-${targetDomain}`) {
      case 'cyber-financial':
        // Cyber events impact on financial domain
        if (sourceEvent.severity === 'critical') {
          impact.level = 'high';
          impact.confidence = 0.8;
          impact.description = 'Critical cyber event with high financial impact';
        } else if (sourceEvent.severity === 'high') {
          impact.level = 'medium';
          impact.confidence = 0.7;
          impact.description = 'High severity cyber event with medium financial impact';
        }
        break;
        
      case 'cyber-biological':
        // Cyber events impact on biological domain
        if (sourceEvent.severity === 'critical') {
          impact.level = 'medium';
          impact.confidence = 0.6;
          impact.description = 'Critical cyber event with medium biological impact';
        } else if (sourceEvent.severity === 'high') {
          impact.level = 'low';
          impact.confidence = 0.5;
          impact.description = 'High severity cyber event with low biological impact';
        }
        break;
        
      case 'financial-cyber':
        // Financial events impact on cyber domain
        if (sourceEvent.severity === 'critical') {
          impact.level = 'high';
          impact.confidence = 0.7;
          impact.description = 'Critical financial event with high cyber impact';
        } else if (sourceEvent.severity === 'high') {
          impact.level = 'medium';
          impact.confidence = 0.6;
          impact.description = 'High severity financial event with medium cyber impact';
        }
        break;
        
      case 'financial-biological':
        // Financial events impact on biological domain
        if (sourceEvent.severity === 'critical') {
          impact.level = 'medium';
          impact.confidence = 0.5;
          impact.description = 'Critical financial event with medium biological impact';
        } else if (sourceEvent.severity === 'high') {
          impact.level = 'low';
          impact.confidence = 0.4;
          impact.description = 'High severity financial event with low biological impact';
        }
        break;
        
      case 'biological-cyber':
        // Biological events impact on cyber domain
        if (sourceEvent.severity === 'critical') {
          impact.level = 'medium';
          impact.confidence = 0.5;
          impact.description = 'Critical biological event with medium cyber impact';
        } else if (sourceEvent.severity === 'high') {
          impact.level = 'low';
          impact.confidence = 0.4;
          impact.description = 'High severity biological event with low cyber impact';
        }
        break;
        
      case 'biological-financial':
        // Biological events impact on financial domain
        if (sourceEvent.severity === 'critical') {
          impact.level = 'medium';
          impact.confidence = 0.6;
          impact.description = 'Critical biological event with medium financial impact';
        } else if (sourceEvent.severity === 'high') {
          impact.level = 'low';
          impact.confidence = 0.5;
          impact.description = 'High severity biological event with low financial impact';
        }
        break;
        
      default:
        // Unknown domain combination
        impact.level = 'low';
        impact.confidence = 0.3;
        impact.description = `Unknown impact of ${sourceDomain} event on ${targetDomain} domain`;
    }
    
    return impact;
  }
}

module.exports = CrossDomainIntegration;
