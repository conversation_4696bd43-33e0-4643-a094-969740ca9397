/**
 * SIMPL<PERSON>IED CDAIE INTELLIGENCE GRID
 * Basic version to test functionality
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  TrendingUpIcon,
  TrendingDownIcon,
  BoltIcon,
  EyeIcon,
  StarIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

// Simple mock data
const SIMPLE_MOCK_DATA = [
  {
    symbol: 'GME',
    domain: 'STOCKS',
    coherence: 0.85,
    confidence: 0.78,
    prophetic_signal: 'Divine Momentum Building',
    action: 'LONG',
    current_price: 28.34,
    target_price: 45.67,
    time_window: '2-6 hours',
    sentiment_phase: 'OPTIMISM',
    fibonacci_level: '0.618',
    engines: ['NEPI', 'NEPE']
  },
  {
    symbol: 'ETH',
    domain: 'CRYPTO',
    coherence: 0.72,
    confidence: 0.84,
    prophetic_signal: 'Harmonic Convergence',
    action: 'LONG',
    current_price: 3245.67,
    target_price: 4123.89,
    time_window: '4-12 hours',
    sentiment_phase: 'BELIEF',
    fibonacci_level: '0.382',
    engines: ['NECO', 'NEEE']
  }
];

export default function SimpleCDAIE({ activePhase, selectedMarket, coherenceLevel, divineMode }) {
  const [processedData, setProcessedData] = useState([]);
  const [sortBy, setSortBy] = useState('coherence');
  const [filterThreshold, setFilterThreshold] = useState(0.5);

  useEffect(() => {
    // Simple data processing
    let filtered = SIMPLE_MOCK_DATA.filter(item => item.coherence >= filterThreshold);
    
    // Sort data
    filtered.sort((a, b) => {
      if (sortBy === 'coherence') return b.coherence - a.coherence;
      if (sortBy === 'confidence') return b.confidence - a.confidence;
      return a.symbol.localeCompare(b.symbol);
    });

    setProcessedData(filtered);
  }, [sortBy, filterThreshold, selectedMarket, activePhase]);

  const getCoherenceColor = (coherence) => {
    if (coherence >= 0.90) return 'text-purple-400';
    if (coherence >= 0.70) return 'text-green-400';
    if (coherence >= 0.50) return 'text-yellow-400';
    if (coherence >= 0.30) return 'text-orange-400';
    return 'text-red-400';
  };

  const getCoherenceBackground = (coherence) => {
    if (coherence >= 0.90) return 'bg-purple-500/20 border-purple-400 shadow-divine animate-pulse';
    if (coherence >= 0.70) return 'bg-green-500/20 border-green-400 shadow-coherence';
    if (coherence >= 0.50) return 'bg-yellow-500/20 border-yellow-400';
    if (coherence >= 0.30) return 'bg-orange-500/20 border-orange-400';
    return 'bg-red-500/20 border-red-400';
  };

  const getActionIcon = (action) => {
    if (action.toLowerCase().includes('long') || action.toLowerCase().includes('buy')) {
      return <TrendingUpIcon className="w-4 h-4 text-green-400" />;
    }
    if (action.toLowerCase().includes('short') || action.toLowerCase().includes('sell')) {
      return <TrendingDownIcon className="w-4 h-4 text-red-400" />;
    }
    return <EyeIcon className="w-4 h-4 text-blue-400" />;
  };

  return (
    <div className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-white flex items-center space-x-2">
            <StarIcon className="w-6 h-6 text-purple-400" />
            <span>CDAIE Intelligence Grid (Simplified)</span>
          </h2>
          <p className="text-sm text-gray-400 mt-1">
            {selectedMarket} • Phase: {activePhase} • {processedData.length} signals
          </p>
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-4">
          {/* Sort Selector */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded px-3 py-1 text-sm text-white"
          >
            <option value="coherence">Sort by Coherence</option>
            <option value="confidence">Sort by Confidence</option>
            <option value="symbol">Sort by Symbol</option>
          </select>

          {/* Filter Threshold */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Min Coherence:</span>
            <input
              type="range"
              min="0"
              max="1"
              step="0.05"
              value={filterThreshold}
              onChange={(e) => setFilterThreshold(parseFloat(e.target.value))}
              className="w-20"
            />
            <span className="text-sm text-white w-12">
              {(filterThreshold * 100).toFixed(0)}%
            </span>
          </div>
        </div>
      </div>

      {/* Intelligence Grid */}
      <div className="grid gap-4">
        {processedData.map((item, index) => (
          <motion.div
            key={item.symbol}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`p-4 rounded-lg border transition-all hover:shadow-lg ${getCoherenceBackground(item.coherence)}`}
          >
            <div className="grid grid-cols-12 gap-4 items-center">
              {/* Symbol & Domain */}
              <div className="col-span-2">
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-bold text-white">
                    {item.symbol}
                  </span>
                  <span className="text-xs px-2 py-1 rounded bg-gray-700 text-gray-300">
                    {item.domain}
                  </span>
                </div>
                <div className="text-sm text-gray-400">
                  Fib: {item.fibonacci_level}
                </div>
              </div>

              {/* Coherence */}
              <div className="col-span-1">
                <div className="text-center">
                  <div className={`text-lg font-bold ${getCoherenceColor(item.coherence)}`}>
                    {(item.coherence * 100).toFixed(0)}%
                  </div>
                  <div className="text-xs text-gray-400">Coherence</div>
                </div>
              </div>

              {/* Prophetic Signal */}
              <div className="col-span-3">
                <div className="flex items-center space-x-2">
                  <BoltIcon className="w-4 h-4 text-yellow-400" />
                  <span className="text-sm text-white font-medium">
                    {item.prophetic_signal}
                  </span>
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  Engines: {item.engines.join(', ')}
                </div>
              </div>

              {/* Action */}
              <div className="col-span-2">
                <div className="flex items-center space-x-2">
                  {getActionIcon(item.action)}
                  <span className="text-sm text-white">
                    {item.action}
                  </span>
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  {item.time_window}
                </div>
              </div>

              {/* Price & Target */}
              <div className="col-span-2">
                <div className="text-sm">
                  <div className="text-white">
                    ${item.current_price.toLocaleString()}
                  </div>
                  <div className="flex items-center space-x-1 text-gray-400">
                    <span>→</span>
                    <span>${item.target_price.toLocaleString()}</span>
                  </div>
                </div>
              </div>

              {/* Confidence & Sentiment */}
              <div className="col-span-1">
                <div className="text-center">
                  <div className="text-sm font-medium text-white">
                    {(item.confidence * 100).toFixed(0)}%
                  </div>
                  <div className="text-xs text-gray-400">
                    {item.sentiment_phase}
                  </div>
                </div>
              </div>

              {/* Action Button */}
              <div className="col-span-1">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded-lg transition-colors"
                  onClick={() => {
                    console.log('Execute action for:', item.symbol);
                  }}
                >
                  Execute
                </motion.button>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Summary Stats */}
      <div className="mt-6 pt-6 border-t border-gray-600">
        <div className="grid grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-purple-400">
              {processedData.length}
            </div>
            <div className="text-sm text-gray-400">Active Signals</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-400">
              {processedData.length > 0 ? (processedData.reduce((sum, item) => sum + item.coherence, 0) / processedData.length * 100).toFixed(0) : 0}%
            </div>
            <div className="text-sm text-gray-400">Avg Coherence</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-yellow-400">
              {processedData.length > 0 ? (processedData.reduce((sum, item) => sum + item.confidence, 0) / processedData.length * 100).toFixed(0) : 0}%
            </div>
            <div className="text-sm text-gray-400">Avg Confidence</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">
              {processedData.filter(item => item.coherence >= 0.8).length}
            </div>
            <div className="text-sm text-gray-400">High Coherence</div>
          </div>
        </div>
      </div>
    </div>
  );
}
