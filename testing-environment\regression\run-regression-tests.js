/**
 * Regression Testing Script for NovaConnect Universal API Connector
 * 
 * This script runs regression tests against the NovaConnect API endpoints.
 */

const axios = require('axios');
const SnapshotManager = require('./snapshot-manager');
const fs = require('fs');
const path = require('path');

// Create snapshot manager
const snapshotManager = new SnapshotManager(path.join(__dirname, '../snapshots'));

// Configuration
const BASE_URL = 'http://localhost:3000'; // Auth service
const REGISTRY_URL = 'http://localhost:3001'; // Connector registry
const EXECUTOR_URL = 'http://localhost:3002'; // Connector executor

// Ensure reports directory exists
const reportsDir = path.join(__dirname, '../reports');
if (!fs.existsSync(reportsDir)) {
  fs.mkdirSync(reportsDir, { recursive: true });
}

// Test data
const testData = {
  connector: {
    name: 'Test Connector',
    version: '1.0.0',
    category: 'Test',
    description: 'Test connector for regression testing',
    author: 'NovaGRC',
    tags: ['test', 'regression'],
    authentication: {
      type: 'API_KEY',
      fields: {
        apiKey: {
          type: 'string',
          description: 'API Key',
          required: true
        }
      }
    },
    configuration: {
      baseUrl: 'http://localhost:3005',
      headers: {
        'Content-Type': 'application/json'
      }
    },
    endpoints: [
      {
        id: 'getResource',
        name: 'Get Resource',
        path: '/resource',
        method: 'GET'
      }
    ],
    mappings: [
      {
        sourceEndpoint: 'getResource',
        targetEntity: 'Resource',
        transformations: [
          {
            source: '$.id',
            target: 'resourceId'
          },
          {
            source: '$.name',
            target: 'resourceName'
          }
        ]
      }
    ]
  },
  credential: {
    name: 'Test Credential',
    authType: 'API_KEY',
    credentials: {
      apiKey: 'test-api-key'
    },
    userId: 'test-user'
  },
  transformation: {
    name: 'testTransform',
    code: 'function transform(value) { return value.toUpperCase(); }',
    description: 'Test transformation'
  }
};

/**
 * Run a test and compare with snapshot
 * 
 * @param {string} testName - Name of the test
 * @param {Function} testFn - Test function
 * @param {Object} options - Options
 * @returns {Object} - Test result
 */
async function runTest(testName, testFn, options = {}) {
  console.log(`Running test: ${testName}`);
  
  try {
    // Run the test
    const result = await testFn();
    
    // Compare with snapshot
    const comparison = snapshotManager.compareWithSnapshot(testName, result);
    
    if (!comparison.snapshot || options.update) {
      // Create or update snapshot
      console.log(`  Creating snapshot for ${testName}`);
      snapshotManager.createSnapshot(testName, result, { update: options.update });
      return {
        testName,
        status: 'created',
        result
      };
    }
    
    if (comparison.matches) {
      console.log(`  ✅ Test passed: ${testName}`);
      return {
        testName,
        status: 'passed',
        result
      };
    } else {
      console.log(`  ❌ Test failed: ${testName}`);
      console.log('  Differences:');
      console.log(snapshotManager.formatDiff(comparison.diff));
      
      return {
        testName,
        status: 'failed',
        result,
        diff: comparison.diff
      };
    }
  } catch (error) {
    console.log(`  ❌ Test error: ${testName}`);
    console.log(`  Error: ${error.message}`);
    
    return {
      testName,
      status: 'error',
      error: error.message
    };
  }
}

/**
 * Run all regression tests
 * 
 * @param {Object} options - Options
 * @returns {Object} - Test results
 */
async function runRegressionTests(options = {}) {
  const results = {
    timestamp: new Date().toISOString(),
    tests: [],
    summary: {
      total: 0,
      passed: 0,
      failed: 0,
      error: 0,
      created: 0
    }
  };
  
  // Test 1: Create connector
  results.tests.push(await runTest('create-connector', async () => {
    const response = await axios.post(`${REGISTRY_URL}/connectors`, testData.connector);
    testData.connectorId = response.data.id;
    return response.data;
  }, options));
  
  // Test 2: Get connector
  results.tests.push(await runTest('get-connector', async () => {
    const response = await axios.get(`${REGISTRY_URL}/connectors/${testData.connectorId}`);
    return response.data;
  }, options));
  
  // Test 3: Create credential
  results.tests.push(await runTest('create-credential', async () => {
    const credential = {
      ...testData.credential,
      connectorId: testData.connectorId
    };
    const response = await axios.post(`${BASE_URL}/credentials`, credential);
    testData.credentialId = response.data.id;
    return response.data;
  }, options));
  
  // Test 4: Get credential
  results.tests.push(await runTest('get-credential', async () => {
    const response = await axios.get(`${BASE_URL}/credentials/${testData.credentialId}`);
    return response.data;
  }, options));
  
  // Test 5: Create transformation
  results.tests.push(await runTest('create-transformation', async () => {
    const response = await axios.post(`${REGISTRY_URL}/transformations`, testData.transformation);
    return response.data;
  }, options));
  
  // Test 6: Get transformation
  results.tests.push(await runTest('get-transformation', async () => {
    const response = await axios.get(`${REGISTRY_URL}/transformations/${testData.transformation.name}`);
    return response.data;
  }, options));
  
  // Test 7: List connectors
  results.tests.push(await runTest('list-connectors', async () => {
    const response = await axios.get(`${REGISTRY_URL}/connectors`);
    return response.data;
  }, options));
  
  // Test 8: List credentials
  results.tests.push(await runTest('list-credentials', async () => {
    const response = await axios.get(`${BASE_URL}/credentials?userId=${testData.credential.userId}`);
    return response.data;
  }, options));
  
  // Calculate summary
  results.summary.total = results.tests.length;
  results.summary.passed = results.tests.filter(test => test.status === 'passed').length;
  results.summary.failed = results.tests.filter(test => test.status === 'failed').length;
  results.summary.error = results.tests.filter(test => test.status === 'error').length;
  results.summary.created = results.tests.filter(test => test.status === 'created').length;
  
  // Save results to file
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportPath = path.join(reportsDir, `regression-test-report-${timestamp}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
  
  console.log('\nRegression testing complete.');
  console.log(`Summary: ${results.summary.passed} passed, ${results.summary.failed} failed, ${results.summary.error} errors, ${results.summary.created} created`);
  console.log(`Report saved to: ${reportPath}`);
  
  return results;
}

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  update: args.includes('--update')
};

// Run the tests
runRegressionTests(options)
  .then(results => {
    // Exit with appropriate code
    if (results.summary.failed > 0 || results.summary.error > 0) {
      process.exit(1);
    } else {
      process.exit(0);
    }
  })
  .catch(error => {
    console.error('Error running regression tests:', error.message);
    process.exit(1);
  });
