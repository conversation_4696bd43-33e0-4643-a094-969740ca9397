/**
 * Export/Import Controller
 * 
 * This controller handles API requests related to exporting and importing configuration data.
 */

const ExportImportService = require('../services/ExportImportService');
const { ValidationError } = require('../utils/errors');
const multer = require('multer');
const upload = multer({ storage: multer.memoryStorage() });

class ExportImportController {
  constructor() {
    this.exportImportService = new ExportImportService();
  }

  /**
   * Get all exports
   */
  async getAllExports(req, res, next) {
    try {
      const filters = req.query;
      const exports = await this.exportImportService.getAllExports(filters);
      res.json(exports);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get my exports
   */
  async getMyExports(req, res, next) {
    try {
      const filters = req.query;
      const exports = await this.exportImportService.getExportsForUser(req.user.id, filters);
      res.json(exports);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get export by ID
   */
  async getExportById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Export ID is required');
      }
      
      const exportItem = await this.exportImportService.getExportById(id);
      res.json(exportItem);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new export
   */
  async createExport(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Export data is required');
      }
      
      const exportItem = await this.exportImportService.createExport(data, req.user.id);
      res.status(201).json(exportItem);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete an export
   */
  async deleteExport(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Export ID is required');
      }
      
      const result = await this.exportImportService.deleteExport(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Download export data
   */
  async downloadExport(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Export ID is required');
      }
      
      const exportData = await this.exportImportService.downloadExport(id);
      
      // Set headers for download
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="export-${id}.json"`);
      
      res.json(exportData.data);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all imports
   */
  async getAllImports(req, res, next) {
    try {
      const filters = req.query;
      const imports = await this.exportImportService.getAllImports(filters);
      res.json(imports);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get my imports
   */
  async getMyImports(req, res, next) {
    try {
      const filters = req.query;
      const imports = await this.exportImportService.getImportsForUser(req.user.id, filters);
      res.json(imports);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get import by ID
   */
  async getImportById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Import ID is required');
      }
      
      const importItem = await this.exportImportService.getImportById(id);
      res.json(importItem);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new import
   */
  async createImport(req, res, next) {
    try {
      const { name, description, options } = req.body;
      
      if (!name) {
        throw new ValidationError('Import name is required');
      }
      
      if (!req.file) {
        throw new ValidationError('Import file is required');
      }
      
      // Parse import data from file
      let importData;
      try {
        importData = JSON.parse(req.file.buffer.toString('utf8'));
      } catch (error) {
        throw new ValidationError('Invalid JSON file');
      }
      
      const importItem = await this.exportImportService.createImport(
        { name, description, options },
        importData,
        req.user.id
      );
      
      res.status(201).json(importItem);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Process an import
   */
  async processImport(req, res, next) {
    try {
      const { id } = req.params;
      const options = req.body || {};
      
      if (!id) {
        throw new ValidationError('Import ID is required');
      }
      
      const result = await this.exportImportService.processImport(id, options, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete an import
   */
  async deleteImport(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Import ID is required');
      }
      
      const result = await this.exportImportService.deleteImport(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get file upload middleware
   */
  getFileUploadMiddleware() {
    return upload.single('file');
  }
}

module.exports = new ExportImportController();
