/**
 * Risk Scenarios Data Model
 * 
 * This file defines the data structure for risk scenarios in the NovaFuse GCP simulation.
 * It includes detailed risk information, affected services, and compliance impact.
 */

// Sample risk scenarios data
const riskScenarios = [
  {
    id: "risk-001",
    name: "Unauthorized Data Access",
    description: "Risk of unauthorized access to sensitive data stored in Google Cloud Storage buckets due to misconfigured IAM permissions or public access settings.",
    category: "Data Security",
    likelihood: "Medium",
    impact: "High",
    overallRisk: "High",
    status: "Active",
    affectedServices: [
      { name: "Cloud Storage", criticality: "High", vulnerability: "Public bucket access or overly permissive IAM roles" },
      { name: "Cloud IAM", criticality: "High", vulnerability: "Excessive permissions or inadequate access controls" }
    ],
    mitigationControls: [
      { name: "IAM Role Restrictions", implementation: "Implement least privilege principle for all IAM roles", status: "Partial" },
      { name: "VPC Service Controls", implementation: "Implement service perimeter around sensitive data resources", status: "Implemented" },
      { name: "Access Logging", implementation: "Enable detailed access logging for all sensitive data buckets", status: "Implemented" }
    ],
    complianceImpact: [
      { framework: "GDPR", controls: ["Art. 32 - Security of processing"], severity: "High" },
      { framework: "HIPAA", controls: ["164.312(a) - Access Control"], severity: "High" },
      { framework: "PCI-DSS", controls: ["Req. 7 - Restrict access to cardholder data"], severity: "High" }
    ],
    detectionMethods: [
      { name: "Security Command Center Findings", effectiveness: "High", implementation: "SCC Premium tier with real-time alerting" },
      { name: "Access Logs Analysis", effectiveness: "Medium", implementation: "Log-based metrics and anomaly detection" }
    ],
    incidentHistory: [
      { date: "2023-03-15T00:00:00Z", description: "Public access accidentally enabled on customer-data bucket", resolution: "Access removed and additional approval workflow implemented", impact: "No data exfiltration detected" }
    ],
    owner: "Security Team",
    reviewDate: "2023-09-01T00:00:00Z"
  },
  {
    id: "risk-002",
    name: "Misconfigured Security Groups",
    description: "Risk of security vulnerabilities due to misconfigured firewall rules and security groups, potentially allowing unauthorized network access to sensitive resources.",
    category: "Network Security",
    likelihood: "High",
    impact: "High",
    overallRisk: "Critical",
    status: "Active",
    affectedServices: [
      { name: "VPC", criticality: "High", vulnerability: "Overly permissive firewall rules" },
      { name: "Compute Engine", criticality: "High", vulnerability: "Exposed management interfaces" },
      { name: "Cloud Armor", criticality: "Medium", vulnerability: "Inadequate WAF rules" }
    ],
    mitigationControls: [
      { name: "Security Command Center Monitoring", implementation: "Continuous monitoring of firewall configurations", status: "Implemented" },
      { name: "Automated Remediation", implementation: "Automated correction of non-compliant firewall rules", status: "Partial" },
      { name: "Configuration Auditing", implementation: "Regular audits of network security configurations", status: "Implemented" }
    ],
    complianceImpact: [
      { framework: "SOC2", controls: ["CC6.1 - Logical Access Security"], severity: "High" },
      { framework: "PCI-DSS", controls: ["Req. 1 - Install and maintain a firewall configuration"], severity: "Critical" },
      { framework: "HIPAA", controls: ["164.308(a)(1)(ii)(B) - Risk management"], severity: "High" }
    ],
    detectionMethods: [
      { name: "Security Command Center Findings", effectiveness: "High", implementation: "SCC Premium tier with real-time alerting" },
      { name: "Network Flow Logs Analysis", effectiveness: "High", implementation: "VPC Flow Logs with anomaly detection" },
      { name: "Penetration Testing", effectiveness: "Medium", implementation: "Quarterly network penetration tests" }
    ],
    incidentHistory: [
      { date: "2023-02-10T00:00:00Z", description: "Overly permissive firewall rule allowed SSH access from any IP", resolution: "Rule corrected and approval workflow implemented", impact: "No unauthorized access detected" }
    ],
    owner: "Network Security Team",
    reviewDate: "2023-08-15T00:00:00Z"
  },
  {
    id: "risk-003",
    name: "Inadequate Encryption",
    description: "Risk of data exposure due to inadequate encryption of sensitive data at rest and in transit, potentially leading to unauthorized access to confidential information.",
    category: "Data Protection",
    likelihood: "Medium",
    impact: "Critical",
    overallRisk: "High",
    status: "Active",
    affectedServices: [
      { name: "Cloud Storage", criticality: "High", vulnerability: "Unencrypted or inadequately encrypted data" },
      { name: "Cloud SQL", criticality: "High", vulnerability: "Databases without encryption" },
      { name: "Cloud KMS", criticality: "High", vulnerability: "Improper key management" }
    ],
    mitigationControls: [
      { name: "Default Encryption", implementation: "Enable default encryption for all storage services", status: "Implemented" },
      { name: "Customer-Managed Encryption Keys", implementation: "Use CMEK for sensitive data", status: "Partial" },
      { name: "Key Rotation", implementation: "Implement automatic key rotation", status: "Partial" },
      { name: "TLS Enforcement", implementation: "Enforce TLS 1.2+ for all data in transit", status: "Implemented" }
    ],
    complianceImpact: [
      { framework: "GDPR", controls: ["Art. 32 - Security of processing"], severity: "High" },
      { framework: "HIPAA", controls: ["164.312(a)(2)(iv) - Encryption and decryption"], severity: "Critical" },
      { framework: "PCI-DSS", controls: ["Req. 3 - Protect stored cardholder data", "Req. 4 - Encrypt transmission of cardholder data"], severity: "Critical" }
    ],
    detectionMethods: [
      { name: "Security Command Center Findings", effectiveness: "Medium", implementation: "SCC Premium tier with encryption checks" },
      { name: "Configuration Audits", effectiveness: "High", implementation: "Regular audits of encryption configurations" },
      { name: "Data Discovery Scans", effectiveness: "Medium", implementation: "DLP scans to identify unencrypted sensitive data" }
    ],
    incidentHistory: [
      { date: "2023-04-05T00:00:00Z", description: "Cloud SQL database created without encryption", resolution: "Database recreated with encryption and policy controls implemented", impact: "No data exposure detected" }
    ],
    owner: "Security Team",
    reviewDate: "2023-08-20T00:00:00Z"
  },
  {
    id: "risk-004",
    name: "Insufficient Logging and Monitoring",
    description: "Risk of undetected security incidents due to insufficient logging and monitoring, potentially leading to delayed incident response and increased impact.",
    category: "Security Operations",
    likelihood: "Medium",
    impact: "High",
    overallRisk: "High",
    status: "Active",
    affectedServices: [
      { name: "Cloud Logging", criticality: "High", vulnerability: "Inadequate log collection or retention" },
      { name: "Cloud Monitoring", criticality: "High", vulnerability: "Insufficient alerting" },
      { name: "Security Command Center", criticality: "High", vulnerability: "Incomplete security monitoring" }
    ],
    mitigationControls: [
      { name: "Centralized Logging", implementation: "Implement centralized logging for all services", status: "Implemented" },
      { name: "Log-Based Alerting", implementation: "Configure alerts for security-relevant log events", status: "Partial" },
      { name: "Log Retention", implementation: "Implement appropriate log retention policies", status: "Implemented" },
      { name: "SIEM Integration", implementation: "Integrate logs with SIEM solution", status: "Planned" }
    ],
    complianceImpact: [
      { framework: "SOC2", controls: ["CC7.2 - Security Incident Identification and Response"], severity: "High" },
      { framework: "PCI-DSS", controls: ["Req. 10 - Track and monitor all access"], severity: "High" },
      { framework: "HIPAA", controls: ["164.312(b) - Audit controls"], severity: "High" }
    ],
    detectionMethods: [
      { name: "Log Coverage Analysis", effectiveness: "High", implementation: "Regular audits of log coverage" },
      { name: "Alert Testing", effectiveness: "Medium", implementation: "Periodic testing of security alerts" }
    ],
    incidentHistory: [
      { date: "2023-01-20T00:00:00Z", description: "Security incident detection delayed due to missing log data", resolution: "Logging coverage expanded and verified", impact: "Increased incident response time" }
    ],
    owner: "Security Operations Team",
    reviewDate: "2023-09-10T00:00:00Z"
  },
  {
    id: "risk-005",
    name: "Insecure API Endpoints",
    description: "Risk of unauthorized access or data exposure through insecure API endpoints, potentially leading to data breaches or service disruption.",
    category: "Application Security",
    likelihood: "High",
    impact: "High",
    overallRisk: "Critical",
    status: "Active",
    affectedServices: [
      { name: "Cloud Endpoints", criticality: "High", vulnerability: "Inadequate authentication or authorization" },
      { name: "API Gateway", criticality: "High", vulnerability: "Missing input validation or rate limiting" },
      { name: "Cloud Run", criticality: "Medium", vulnerability: "Exposed service endpoints" }
    ],
    mitigationControls: [
      { name: "API Authentication", implementation: "Implement strong authentication for all APIs", status: "Implemented" },
      { name: "Input Validation", implementation: "Implement comprehensive input validation", status: "Partial" },
      { name: "Rate Limiting", implementation: "Configure rate limiting for all API endpoints", status: "Implemented" },
      { name: "API Security Testing", implementation: "Regular security testing of API endpoints", status: "Partial" }
    ],
    complianceImpact: [
      { framework: "GDPR", controls: ["Art. 32 - Security of processing"], severity: "High" },
      { framework: "PCI-DSS", controls: ["Req. 6.5 - Address common coding vulnerabilities"], severity: "High" },
      { framework: "SOC2", controls: ["CC6.1 - Logical Access Security"], severity: "High" }
    ],
    detectionMethods: [
      { name: "API Security Scanning", effectiveness: "High", implementation: "Regular API security scans" },
      { name: "Anomaly Detection", effectiveness: "Medium", implementation: "Monitoring for unusual API usage patterns" },
      { name: "Penetration Testing", effectiveness: "High", implementation: "Regular penetration testing of API endpoints" }
    ],
    incidentHistory: [
      { date: "2023-05-12T00:00:00Z", description: "API endpoint found vulnerable to injection attack", resolution: "Input validation implemented and all endpoints reviewed", impact: "No exploitation detected" }
    ],
    owner: "Application Security Team",
    reviewDate: "2023-08-30T00:00:00Z"
  }
];

// Risk heatmap data for visualization
const riskHeatmapData = {
  timePoints: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
  categories: ["Data Security", "Network Security", "Data Protection", "Security Operations", "Application Security"],
  intensityValues: [
    [0.8, 0.7, 0.6, 0.5, 0.3, 0.2],  // Data Security
    [0.5, 0.6, 0.7, 0.4, 0.3, 0.2],  // Network Security
    [0.3, 0.4, 0.6, 0.7, 0.5, 0.3],  // Data Protection
    [0.4, 0.5, 0.3, 0.2, 0.1, 0.1],  // Security Operations
    [0.6, 0.5, 0.4, 0.5, 0.6, 0.4]   // Application Security
  ],
  complianceImpact: {
    "Data Security": ["GDPR", "HIPAA", "PCI-DSS"],
    "Network Security": ["PCI-DSS", "SOC2", "HIPAA"],
    "Data Protection": ["GDPR", "PCI-DSS", "HIPAA"],
    "Security Operations": ["SOC2", "PCI-DSS", "HIPAA"],
    "Application Security": ["GDPR", "PCI-DSS", "SOC2"]
  }
};

module.exports = { riskScenarios, riskHeatmapData };
