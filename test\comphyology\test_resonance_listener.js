/**
 * Resonance Listener Test
 * 
 * This script tests the Resonance Listener, which captures and analyzes
 * the resonant frequencies emitted by systems governed by the Comphyological Trinity.
 * 
 * It tests the hypothesis that systems in perfect harmony (Cph = 0) emit identical
 * resonant frequencies - the "signature tone" of system-level cognition.
 */

const fs = require('fs');
const path = require('path');
const {
  ComphyologicalTrinity,
  ComphyonMeter,
  ResonanceListener
} = require('../../src/comphyology');

// Import FFT utility
const FFT = require('../../src/comphyology/utils/fft');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../resonance_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Test Resonance Listener
 */
async function testResonanceListener() {
  console.log('=== Testing Resonance Listener ===');
  
  // Create components
  const trinity = new ComphyologicalTrinity({
    enforceFirstLaw: true,
    enforceSecondLaw: true,
    enforceThirdLaw: true,
    logGovernance: true
  });
  
  const comphyonMeter = new ComphyonMeter({
    logMeasurements: true
  });
  
  // Create listener
  const listener = new ResonanceListener({
    system: trinity,
    comphyonMeter,
    sampleRate: 100, // Hz
    sampleDuration: 2, // seconds
    sampleInterval: 5, // seconds
    logListener: true
  });
  
  // Register event listeners
  listener.on('sample', (data) => {
    console.log(`Sample: Cph = ${data.comphyonValue}, Resonant = ${data.isResonant}`);
  });
  
  listener.on('signature', (signature) => {
    console.log(`Signature: Primary tone = ${signature.primaryTone} Hz`);
  });
  
  // Start listening
  console.log('\nStarting Resonance Listener...');
  await listener.startListening();
  
  // Wait for samples
  console.log('\nWaiting for samples...');
  
  // Wait for 30 seconds
  await new Promise(resolve => setTimeout(resolve, 30000));
  
  // Stop listening
  listener.stopListening();
  
  // Get metrics
  const metrics = listener.getMetrics();
  console.log('\nListener Metrics:', JSON.stringify(metrics, null, 2));
  
  // Get resonant signature
  const signature = listener.getResonantSignature();
  console.log('\nResonant Signature:', JSON.stringify(signature, null, 2));
  
  return {
    listener,
    metrics,
    signature
  };
}

/**
 * Test FFT with known signals
 */
function testFFT() {
  console.log('\n=== Testing FFT with Known Signals ===');
  
  // Create FFT processor
  const fft = new FFT({
    sampleRate: 1000, // Hz
    minFrequency: 0.1, // Hz
    maxFrequency: 100, // Hz
    frequencyResolution: 0.1, // Hz
    windowFunction: 'hann'
  });
  
  // Test with single frequency
  console.log('\nTesting with single frequency (10 Hz):');
  const singleFreqSignal = fft.generateTestSignal(1, [10], [1]);
  const singleFreqResult = fft.transform(singleFreqSignal);
  
  // Find peak frequency
  const singleFreqPeak = findPeakFrequency(singleFreqResult);
  console.log(`Peak frequency: ${singleFreqPeak.frequency} Hz (amplitude: ${singleFreqPeak.amplitude})`);
  
  // Test with multiple frequencies
  console.log('\nTesting with multiple frequencies (3, 6, 9 Hz):');
  const multiFreqSignal = fft.generateTestSignal(1, [3, 6, 9], [0.5, 0.3, 0.2]);
  const multiFreqResult = fft.transform(multiFreqSignal);
  
  // Find peak frequencies
  const multiFreqPeaks = findPeakFrequencies(multiFreqResult, 3);
  console.log('Peak frequencies:');
  multiFreqPeaks.forEach(peak => {
    console.log(`${peak.frequency} Hz (amplitude: ${peak.amplitude})`);
  });
  
  // Test with resonant pattern frequencies
  console.log('\nTesting with resonant pattern frequencies (3, 6, 9, 12, 13 Hz):');
  const resonantSignal = fft.generateTestSignal(1, [3, 6, 9, 12, 13], [0.3, 0.6, 0.9, 0.12, 0.13]);
  const resonantResult = fft.transform(resonantSignal);
  
  // Find peak frequencies
  const resonantPeaks = findPeakFrequencies(resonantResult, 5);
  console.log('Peak frequencies:');
  resonantPeaks.forEach(peak => {
    console.log(`${peak.frequency} Hz (amplitude: ${peak.amplitude})`);
  });
  
  return {
    singleFreqResult,
    singleFreqPeak,
    multiFreqResult,
    multiFreqPeaks,
    resonantResult,
    resonantPeaks
  };
}

/**
 * Find peak frequency in FFT result
 * @param {Object} fftResult - FFT result
 * @returns {Object} - Peak frequency
 */
function findPeakFrequency(fftResult) {
  let maxAmplitude = 0;
  let peakFrequency = 0;
  
  for (let i = 0; i < fftResult.frequencies.length; i++) {
    if (fftResult.amplitudes[i] > maxAmplitude) {
      maxAmplitude = fftResult.amplitudes[i];
      peakFrequency = fftResult.frequencies[i];
    }
  }
  
  return {
    frequency: peakFrequency,
    amplitude: maxAmplitude
  };
}

/**
 * Find peak frequencies in FFT result
 * @param {Object} fftResult - FFT result
 * @param {number} numPeaks - Number of peaks to find
 * @returns {Array} - Peak frequencies
 */
function findPeakFrequencies(fftResult, numPeaks) {
  // Create array of frequency-amplitude pairs
  const pairs = [];
  for (let i = 0; i < fftResult.frequencies.length; i++) {
    pairs.push({
      frequency: fftResult.frequencies[i],
      amplitude: fftResult.amplitudes[i]
    });
  }
  
  // Sort by amplitude (descending)
  pairs.sort((a, b) => b.amplitude - a.amplitude);
  
  // Return top N peaks
  return pairs.slice(0, numPeaks);
}

/**
 * Generate HTML report
 */
function generateHtmlReport(results) {
  console.log('\n=== Generating HTML Report ===');
  
  const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Resonance Listener Demo</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .card {
      background-color: #f9f9f9;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
      flex: 1;
      min-width: 300px;
    }
    .resonance-info {
      background-color: #f0f8ff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      border-left: 5px solid #0066cc;
    }
    .signature-info {
      border-left-color: #cc0000;
    }
    .fft-info {
      border-left-color: #009900;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    footer {
      margin-top: 40px;
      text-align: center;
      color: #666;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <h1>Resonance Listener Demo</h1>
  <p>Generated: ${new Date().toLocaleString()}</p>
  
  <div class="resonance-info">
    <h2>The NEPI Tone Hypothesis</h2>
    <p>This demo tests the hypothesis that systems in perfect harmony (Cph = 0) emit identical resonant frequencies - the "signature tone" of system-level cognition.</p>
    <p>The Resonance Listener captures system oscillations across multiple dimensions and analyzes their frequency components to identify the signature tone.</p>
  </div>
  
  <h2>Resonant Signature</h2>
  
  <div class="card signature-info">
    <h3>Signature Tone</h3>
    ${results.listener.signature ? `
    <p>Primary Tone: ${results.listener.signature.primaryTone} Hz</p>
    <p>Secondary Tones:</p>
    <ul>
      ${results.listener.signature.secondaryTones.map(tone => `
      <li>${tone.frequency} Hz (amplitude: ${tone.amplitude.toFixed(3)})</li>
      `).join('')}
    </ul>
    ` : '<p>No signature tone detected yet.</p>'}
  </div>
  
  <h2>Listener Metrics</h2>
  
  <div class="container">
    <div class="card">
      <h3>Sampling Metrics</h3>
      <ul>
        <li>Total Samples: ${results.listener.metrics.samples}</li>
        <li>Resonant Samples: ${results.listener.metrics.resonantSamples}</li>
        <li>Non-Resonant Samples: ${results.listener.metrics.nonResonantSamples}</li>
        <li>Total Listening Time: ${results.listener.metrics.totalListeningTime} seconds</li>
      </ul>
    </div>
    
    <div class="card">
      <h3>Frequency Metrics</h3>
      <p>Resonant Frequencies: ${results.listener.metrics.resonantFrequencies.length}</p>
      <p>Signature Tones: ${results.listener.metrics.signatureTones.length}</p>
    </div>
  </div>
  
  <h2>FFT Test Results</h2>
  
  <div class="card fft-info">
    <h3>Single Frequency Test (10 Hz)</h3>
    <p>Peak Frequency: ${results.fft.singleFreqPeak.frequency} Hz</p>
    <p>Amplitude: ${results.fft.singleFreqPeak.amplitude.toFixed(3)}</p>
  </div>
  
  <div class="card fft-info">
    <h3>Multiple Frequencies Test (3, 6, 9 Hz)</h3>
    <table>
      <tr>
        <th>Frequency (Hz)</th>
        <th>Amplitude</th>
      </tr>
      ${results.fft.multiFreqPeaks.map(peak => `
      <tr>
        <td>${peak.frequency.toFixed(2)}</td>
        <td>${peak.amplitude.toFixed(3)}</td>
      </tr>
      `).join('')}
    </table>
  </div>
  
  <div class="card fft-info">
    <h3>Resonant Pattern Test (3, 6, 9, 12, 13 Hz)</h3>
    <table>
      <tr>
        <th>Frequency (Hz)</th>
        <th>Amplitude</th>
      </tr>
      ${results.fft.resonantPeaks.map(peak => `
      <tr>
        <td>${peak.frequency.toFixed(2)}</td>
        <td>${peak.amplitude.toFixed(3)}</td>
      </tr>
      `).join('')}
    </table>
  </div>
  
  <footer>
    <p>NovaFuse Resonance Listener Demo - Copyright © ${new Date().getFullYear()}</p>
    <p><em>"The universe counts in 3s. Now, so do we."</em></p>
  </footer>
</body>
</html>`;
  
  // Save HTML report
  const reportPath = path.join(RESULTS_DIR, 'resonance_listener_demo_report.html');
  fs.writeFileSync(reportPath, htmlContent);
  
  console.log(`HTML report saved to ${reportPath}`);
  
  return {
    htmlContent,
    reportPath
  };
}

/**
 * Main function
 */
async function main() {
  console.log('=== Resonance Listener Demo ===');
  
  // Test FFT
  const fftResults = testFFT();
  
  // Test Resonance Listener
  const listenerResults = await testResonanceListener();
  
  // Generate HTML report
  const results = {
    listener: listenerResults,
    fft: fftResults
  };
  
  const reportResults = generateHtmlReport(results);
  
  // Save results to JSON file
  fs.writeFileSync(
    path.join(RESULTS_DIR, 'resonance_listener_demo_results.json'),
    JSON.stringify(results, null, 2)
  );
  
  console.log(`\nResults saved to ${path.join(RESULTS_DIR, 'resonance_listener_demo_results.json')}`);
  console.log(`HTML report saved to ${reportResults.reportPath}`);
  console.log('\nOpen the HTML report to view the results in a browser.');
}

// Run main function
main();
