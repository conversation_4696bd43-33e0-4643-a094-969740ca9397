/**
 * Enhanced Analytics Service
 *
 * This service provides advanced analytics functionality including:
 * - User analytics
 * - Connector analytics
 * - Compliance analytics
 * - Predictive analytics
 * - Real-time analytics
 * - Custom analytics
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const AnalyticsService = require('./AnalyticsService');
const ConnectorService = require('./ConnectorService');
const UserService = require('./UserService');
const logger = require('../../config/logger');
const moment = require('moment');
const { createObjectCsvWriter } = require('csv-writer');
const ExcelJS = require('exceljs');

class EnhancedAnalyticsService extends AnalyticsService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    super(dataDir);
    
    // Additional directories for enhanced analytics
    this.enhancedAnalyticsDir = path.join(this.analyticsDir, 'enhanced');
    this.userAnalyticsFile = path.join(this.enhancedAnalyticsDir, 'user_analytics.json');
    this.connectorAnalyticsFile = path.join(this.enhancedAnalyticsDir, 'connector_analytics.json');
    this.complianceAnalyticsFile = path.join(this.enhancedAnalyticsDir, 'compliance_analytics.json');
    this.predictiveAnalyticsFile = path.join(this.enhancedAnalyticsDir, 'predictive_analytics.json');
    this.realTimeAnalyticsFile = path.join(this.enhancedAnalyticsDir, 'real_time_analytics.json');
    this.dashboardCacheFile = path.join(this.enhancedAnalyticsDir, 'dashboard_cache.json');
    this.exportDir = path.join(this.enhancedAnalyticsDir, 'exports');
    
    // Ensure directories exist
    this.ensureDirectories();
    
    // Initialize services
    this.connectorService = new ConnectorService(dataDir);
    this.userService = new UserService(dataDir);
    
    // Initialize real-time analytics
    this.initializeRealTimeAnalytics();
  }
  
  /**
   * Ensure all required directories exist
   */
  async ensureDirectories() {
    try {
      await fs.mkdir(this.enhancedAnalyticsDir, { recursive: true });
      await fs.mkdir(this.exportDir, { recursive: true });
    } catch (error) {
      logger.error('Error creating analytics directories', { error: error.message });
    }
  }
  
  /**
   * Initialize real-time analytics
   */
  initializeRealTimeAnalytics() {
    // Initialize real-time metrics
    this.realTimeMetrics = {
      active_users: 0,
      api_calls_per_minute: 0,
      error_rate: 0,
      response_time: 0,
      timestamp: new Date().toISOString()
    };
    
    // Update real-time metrics every minute
    setInterval(() => this.updateRealTimeMetrics(), 60 * 1000);
  }
  
  /**
   * Update real-time metrics
   */
  async updateRealTimeMetrics() {
    try {
      // Get usage data for the last minute
      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
      
      const usageData = await this.loadData(this.usageFile, []);
      const recentUsage = usageData.filter(record => new Date(record.timestamp) >= oneMinuteAgo);
      
      // Calculate metrics
      const activeUsers = new Set(recentUsage.map(record => record.userId)).size;
      const apiCalls = recentUsage.length;
      
      const errorData = await this.loadData(this.errorFile, []);
      const recentErrors = errorData.filter(record => new Date(record.timestamp) >= oneMinuteAgo);
      const errorRate = apiCalls > 0 ? recentErrors.length / apiCalls : 0;
      
      const performanceData = await this.loadData(this.performanceFile, []);
      const recentPerformance = performanceData.filter(record => new Date(record.timestamp) >= oneMinuteAgo);
      const avgResponseTime = recentPerformance.length > 0
        ? recentPerformance.reduce((sum, record) => sum + record.responseTime, 0) / recentPerformance.length
        : 0;
      
      // Update real-time metrics
      this.realTimeMetrics = {
        active_users: activeUsers,
        api_calls_per_minute: apiCalls,
        error_rate: errorRate,
        response_time: avgResponseTime,
        timestamp: now.toISOString()
      };
      
      // Save real-time metrics
      const realTimeAnalytics = await this.loadData(this.realTimeAnalyticsFile, []);
      realTimeAnalytics.push(this.realTimeMetrics);
      
      // Keep only the last 24 hours of data
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const filteredAnalytics = realTimeAnalytics.filter(record => new Date(record.timestamp) >= oneDayAgo);
      
      await this.saveData(this.realTimeAnalyticsFile, filteredAnalytics);
    } catch (error) {
      logger.error('Error updating real-time metrics', { error: error.message });
    }
  }
  
  /**
   * Get user analytics
   * 
   * @param {Object} options - Options for user analytics
   * @returns {Object} - User analytics
   */
  async getUserAnalytics(options) {
    const {
      startDate,
      endDate,
      userId,
      teamId,
      groupBy = 'day',
      metrics = ['logins', 'api_calls', 'active_time']
    } = options;
    
    // Get usage data
    const usageData = await this.loadData(this.usageFile, []);
    
    // Filter by date range
    let filteredData = usageData;
    
    if (startDate) {
      const startDateTime = new Date(startDate);
      filteredData = filteredData.filter(record => new Date(record.timestamp) >= startDateTime);
    }
    
    if (endDate) {
      const endDateTime = new Date(endDate);
      filteredData = filteredData.filter(record => new Date(record.timestamp) <= endDateTime);
    }
    
    // Filter by user ID
    if (userId) {
      filteredData = filteredData.filter(record => record.userId === userId);
    }
    
    // Filter by team ID
    if (teamId) {
      // Get users in the team
      const users = await this.userService.getUsersByTeam(teamId);
      const userIds = users.map(user => user.id);
      filteredData = filteredData.filter(record => userIds.includes(record.userId));
    }
    
    // Group by time period
    const groupedData = this.groupDataByTimePeriod(filteredData, groupBy);
    
    // Calculate metrics
    const result = {
      metrics: {},
      timePoints: Object.keys(groupedData).sort(),
      summary: {}
    };
    
    // Initialize metrics
    metrics.forEach(metric => {
      result.metrics[metric] = {};
      result.timePoints.forEach(timePoint => {
        result.metrics[metric][timePoint] = 0;
      });
    });
    
    // Calculate metrics for each time point
    result.timePoints.forEach(timePoint => {
      const records = groupedData[timePoint];
      
      if (metrics.includes('logins')) {
        const loginEvents = records.filter(record => record.action === 'login');
        result.metrics.logins[timePoint] = loginEvents.length;
      }
      
      if (metrics.includes('api_calls')) {
        result.metrics.api_calls[timePoint] = records.length;
      }
      
      if (metrics.includes('active_time')) {
        // Estimate active time based on API calls
        // This is a simplified approach; a more accurate approach would use actual session data
        const uniqueUsers = new Set(records.map(record => record.userId)).size;
        const estimatedActiveTimePerUser = records.length > 0 ? Math.min(8 * 60, records.length * 2) : 0; // Max 8 hours
        result.metrics.active_time[timePoint] = uniqueUsers * estimatedActiveTimePerUser; // In minutes
      }
    });
    
    // Calculate summary statistics
    metrics.forEach(metric => {
      const values = Object.values(result.metrics[metric]);
      result.summary[metric] = {
        total: values.reduce((sum, value) => sum + value, 0),
        average: values.length > 0 ? values.reduce((sum, value) => sum + value, 0) / values.length : 0,
        min: values.length > 0 ? Math.min(...values) : 0,
        max: values.length > 0 ? Math.max(...values) : 0
      };
    });
    
    return result;
  }
  
  /**
   * Get connector analytics
   * 
   * @param {Object} options - Options for connector analytics
   * @returns {Object} - Connector analytics
   */
  async getConnectorAnalytics(options) {
    const {
      startDate,
      endDate,
      connectorId,
      connectorType,
      groupBy = 'day',
      metrics = ['executions', 'success_rate', 'average_duration', 'error_rate']
    } = options;
    
    // Get connector execution data
    // In a real implementation, this would come from a database
    // For this example, we'll generate some sample data
    const connectorData = await this.generateSampleConnectorData(startDate, endDate, connectorId, connectorType);
    
    // Filter by date range
    let filteredData = connectorData;
    
    if (startDate) {
      const startDateTime = new Date(startDate);
      filteredData = filteredData.filter(record => new Date(record.timestamp) >= startDateTime);
    }
    
    if (endDate) {
      const endDateTime = new Date(endDate);
      filteredData = filteredData.filter(record => new Date(record.timestamp) <= endDateTime);
    }
    
    // Filter by connector ID
    if (connectorId) {
      filteredData = filteredData.filter(record => record.connectorId === connectorId);
    }
    
    // Filter by connector type
    if (connectorType) {
      filteredData = filteredData.filter(record => record.connectorType === connectorType);
    }
    
    // Group by time period
    const groupedData = this.groupDataByTimePeriod(filteredData, groupBy);
    
    // Calculate metrics
    const result = {
      metrics: {},
      timePoints: Object.keys(groupedData).sort(),
      summary: {}
    };
    
    // Initialize metrics
    metrics.forEach(metric => {
      result.metrics[metric] = {};
      result.timePoints.forEach(timePoint => {
        result.metrics[metric][timePoint] = 0;
      });
    });
    
    // Calculate metrics for each time point
    result.timePoints.forEach(timePoint => {
      const records = groupedData[timePoint];
      
      if (metrics.includes('executions')) {
        result.metrics.executions[timePoint] = records.length;
      }
      
      if (metrics.includes('success_rate')) {
        const successfulExecutions = records.filter(record => record.status === 'success');
        result.metrics.success_rate[timePoint] = records.length > 0 
          ? successfulExecutions.length / records.length 
          : 0;
      }
      
      if (metrics.includes('average_duration')) {
        result.metrics.average_duration[timePoint] = records.length > 0 
          ? records.reduce((sum, record) => sum + record.duration, 0) / records.length 
          : 0;
      }
      
      if (metrics.includes('error_rate')) {
        const failedExecutions = records.filter(record => record.status === 'error');
        result.metrics.error_rate[timePoint] = records.length > 0 
          ? failedExecutions.length / records.length 
          : 0;
      }
    });
    
    // Calculate summary statistics
    metrics.forEach(metric => {
      const values = Object.values(result.metrics[metric]);
      result.summary[metric] = {
        total: metric === 'executions' ? values.reduce((sum, value) => sum + value, 0) : null,
        average: values.length > 0 ? values.reduce((sum, value) => sum + value, 0) / values.length : 0,
        min: values.length > 0 ? Math.min(...values) : 0,
        max: values.length > 0 ? Math.max(...values) : 0
      };
    });
    
    return result;
  }
  
  /**
   * Generate sample connector data
   * 
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @param {string} connectorId - Connector ID
   * @param {string} connectorType - Connector type
   * @returns {Array} - Sample connector data
   */
  async generateSampleConnectorData(startDate, endDate, connectorId, connectorType) {
    // In a real implementation, this would come from a database
    // For this example, we'll generate some sample data
    
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();
    
    const data = [];
    const connectorTypes = ['aws', 'azure', 'gcp', 'github', 'jira', 'slack'];
    const connectorIds = ['conn-1', 'conn-2', 'conn-3', 'conn-4', 'conn-5'];
    
    // Generate data for each day
    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      // Generate 10-50 executions per day
      const executionsCount = Math.floor(Math.random() * 41) + 10;
      
      for (let i = 0; i < executionsCount; i++) {
        const timestamp = new Date(date);
        timestamp.setHours(Math.floor(Math.random() * 24));
        timestamp.setMinutes(Math.floor(Math.random() * 60));
        timestamp.setSeconds(Math.floor(Math.random() * 60));
        
        const record = {
          id: uuidv4(),
          timestamp: timestamp.toISOString(),
          connectorId: connectorId || connectorIds[Math.floor(Math.random() * connectorIds.length)],
          connectorType: connectorType || connectorTypes[Math.floor(Math.random() * connectorTypes.length)],
          status: Math.random() > 0.1 ? 'success' : 'error', // 90% success rate
          duration: Math.floor(Math.random() * 5000) + 100, // 100-5100ms
          dataProcessed: Math.floor(Math.random() * 1000) + 1, // 1-1000 records
          errorMessage: null
        };
        
        if (record.status === 'error') {
          record.errorMessage = 'Sample error message';
        }
        
        data.push(record);
      }
    }
    
    return data;
  }
  
  /**
   * Group data by time period
   * 
   * @param {Array} data - Data to group
   * @param {string} groupBy - Time period to group by
   * @returns {Object} - Grouped data
   */
  groupDataByTimePeriod(data, groupBy) {
    const groupedData = {};
    
    data.forEach(record => {
      const date = new Date(record.timestamp);
      let timePoint;
      
      switch (groupBy) {
        case 'hour':
          timePoint = moment(date).format('YYYY-MM-DD HH:00');
          break;
        case 'day':
          timePoint = moment(date).format('YYYY-MM-DD');
          break;
        case 'week':
          timePoint = moment(date).format('YYYY-[W]WW');
          break;
        case 'month':
          timePoint = moment(date).format('YYYY-MM');
          break;
        case 'quarter':
          timePoint = moment(date).format('YYYY-[Q]Q');
          break;
        case 'year':
          timePoint = moment(date).format('YYYY');
          break;
        default:
          timePoint = moment(date).format('YYYY-MM-DD');
      }
      
      if (!groupedData[timePoint]) {
        groupedData[timePoint] = [];
      }
      
      groupedData[timePoint].push(record);
    });
    
    return groupedData;
  }
}

module.exports = new EnhancedAnalyticsService();
