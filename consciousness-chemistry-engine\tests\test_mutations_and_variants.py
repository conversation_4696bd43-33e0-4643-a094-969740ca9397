"""
Tests for mutation and folding variant functionality in NovaFoldClient.
"""

import unittest
from typing import List, Tuple

# Import the module to test
from src.ConsciousNovaFold import NovaFoldClient

class TestMutationsAndVariants(unittest.TestCase):
    """Test cases for mutation and folding variant functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.client = NovaFoldClient(enable_benchmark=False)
        self.test_sequence = "ACDEFGHIKLMNPQRSTVWY"  # 20 amino acids
    
    def test_apply_mutation_valid(self):
        """Test applying a valid mutation."""
        # Test mutating position 1 (A) to G (Glycine)
        mutated = self.client.apply_mutation(self.test_sequence, 1, 'G')
        self.assertEqual(mutated[0], 'G')  # First character should be G now
        self.assertEqual(len(mutated), len(self.test_sequence))  # Length should be the same
        
        # Test mutating last position (Y) to W (Tryptophan)
        mutated = self.client.apply_mutation(self.test_sequence, 20, 'W')
        self.assertEqual(mutated[-1], 'W')  # Last character should be W now
    
    def test_apply_mutation_invalid(self):
        """Test applying invalid mutations."""
        # Position out of range (0)
        with self.assertRaises(ValueError):
            self.client.apply_mutation(self.test_sequence, 0, 'A')
            
        # Position out of range (21)
        with self.assertRaises(ValueError):
            self.client.apply_mutation(self.test_sequence, 21, 'A')
            
        # Invalid amino acid code
        with self.assertRaises(ValueError):
            self.client.apply_mutation(self.test_sequence, 1, 'X')
    
    def test_predict_with_mutations(self):
        """Test prediction with mutations."""
        # Define mutations: (position, new_residue)
        mutations = [
            (1, 'G'),  # A1G
            (5, 'P'),  # E5P
            (10, 'L')  # K10L
        ]
        
        # Get prediction with mutations
        result = self.client.predict(
            sequence=self.test_sequence,
            mutations=mutations,
            validate_against=None
        )
        
        # Check that mutations were applied correctly
        self.assertEqual(result['sequence'][0], 'G')  # A -> G
        self.assertEqual(result['sequence'][4], 'P')  # E -> P
        self.assertEqual(result['sequence'][9], 'L')  # K -> L
        
        # Check that metadata includes mutations
        self.assertEqual(
            result['structure']['metadata']['mutations_applied'],
            [(1, 'G'), (5, 'P'), (10, 'L')]
        )
    
    def test_misfolded_variant(self):
        """Test misfolded variant."""
        result = self.client.predict(
            sequence=self.test_sequence,
            folding_variant='misfolded',
            validate_against=None
        )
        
        # Check that pLDDT scores were reduced (on average)
        plddt_scores = result['structure']['plddt']
        self.assertLess(max(plddt_scores), 0.8)  # Should be reduced from ~0.9
        
        # Check that stability metrics were affected
        self.assertGreater(
            result['structure']['stability_metrics']['ddg'],
            1.0  # Less stable than native
        )
    
    def test_denatured_variant(self):
        """Test denatured variant."""
        result = self.client.predict(
            sequence=self.test_sequence,
            folding_variant='denatured',
            validate_against=None
        )
        
        # Check that secondary structure is all coils
        self.assertTrue(all(s == 'C' for s in result['structure']['secondary_structure']))
        
        # Check that pLDDT scores were significantly reduced
        plddt_scores = result['structure']['plddt']
        self.assertLess(max(plddt_scores), 0.6)
    
    def test_fibrous_variant(self):
        """Test fibrous variant."""
        result = self.client.predict(
            sequence=self.test_sequence,
            folding_variant='fibrous',
            validate_against=None
        )
        
        # Check that we have beta sheets in the structure
        self.assertIn('E', result['structure']['secondary_structure'])
        
        # Check that every other position is a beta sheet (approximately)
        ss = result['structure']['secondary_structure']
        beta_count = sum(1 for s in ss[::2] if s == 'E')
        self.assertGreaterEqual(beta_count, len(ss) // 3)  # At least 1/3 should be beta

if __name__ == "__main__":
    unittest.main()
