6177f95d27d3a89b16780165e4d1e464
/**
 * NovaConnect - Universal API Connector
 * Main Server Entry Point
 */

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const winston = require('winston');
const rateLimit = require('express-rate-limit');
const mongoose = require('mongoose');
const path = require('path');

// Import routes
const apiRoutes = require('./api/routes');
const metricsRoutes = require('./api/routes/metricsRoutes');
const featureRoutes = require('./api/routes/featureRoutes');
const optimizationRoutes = require('./api/routes/optimizationRoutes');
const errorDemoRoutes = require('./src/routes/error-demo');
const monitoringRoutes = require('./src/routes/monitoring-routes');
const connectorRoutes = require('./src/routes/connector-routes');
const performanceMetricsRoutes = require('./src/routes/metrics-routes');
const csdeDashboardRoutes = require('./src/routes/csde-dashboard-routes');
const csdeBatchRoutes = require('./src/routes/csde-batch-routes');
const csdeAdvancedRoutes = require('./src/routes/csde-advanced-routes');
const csdePerformanceRoutes = require('./src/routes/csde-performance-routes');

// Import middleware
const {
  metricsMiddleware,
  errorMetricsMiddleware
} = require('./api/middleware/metricsMiddleware');
const {
  tracingMiddleware,
  tracingErrorMiddleware
} = require('./api/middleware/tracingMiddleware');

// Import security middleware
const {
  auth,
  security,
  rateLimiter,
  sanitizer
} = require('./src/middleware');

// Import enhanced monitoring middleware
const httpMetricsMiddleware = require('./src/monitoring/http-metrics-middleware');
const errorMetricsMiddleware2 = require('./src/monitoring/error-metrics-middleware');
const {
  tracingMiddleware: enhancedTracingMiddleware,
  tracingErrorMiddleware: enhancedTracingErrorMiddleware
} = require('./src/monitoring/tracing-middleware');
const {
  requestLoggingMiddleware,
  errorLoggingMiddleware
} = require('./src/monitoring/logging-middleware');
const {
  errorHandler: legacyErrorHandler,
  notFoundHandler: legacyNotFoundHandler,
  asyncHandler,
  retryHandler,
  circuitBreakerHandler,
  timeoutHandler
} = require('./api/middleware/errorHandlingMiddleware');
const {
  errorHandler,
  notFoundHandler
} = require('./src/middleware/error-handler');
const {
  asyncHandler: enhancedAsyncHandler
} = require('./src/utils/async-handler');
const {
  clusterMiddleware,
  clusterHealth
} = require('./api/middleware/clusterMiddleware');
const {
  cacheMiddleware,
  cacheClearMiddleware,
  cacheMetrics,
  cacheClear
} = require('./api/middleware/cacheMiddleware');
const {
  requireFeature,
  checkFeatureLimit,
  getUserSubscription,
  getAvailableFeatures,
  getFeatureUsage,
  updateUserSubscription
} = require('./api/middleware/featureFlagMiddleware');
const {
  configureSecurityMiddleware
} = require('./api/middleware/securityMiddleware');
const {
  requestMonitoringMiddleware,
  metricsEndpoint,
  healthCheckEndpoint
} = require('./api/middleware/monitoringMiddleware');

// Import services
const prometheusMetrics = require('./api/services/PrometheusMetricsService');
const googleCloudMonitoring = require('./api/services/GoogleCloudMonitoringService');
const tracingService = require('./api/services/TracingService');
const errorHandlingService = require('./api/services/ErrorHandlingService');
const clusterService = require('./api/services/ClusterService');
const cacheService = require('./api/services/CacheService');

// Create Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
  defaultMeta: {
    service: 'nova-connect'
  },
  transports: [new winston.transports.Console(), new winston.transports.File({
    filename: 'logs/error.log',
    level: 'error'
  }), new winston.transports.File({
    filename: 'logs/combined.log'
  })]
});

// Middleware
app.use(express.json({
  limit: '10mb'
}));
app.use(express.urlencoded({
  extended: true,
  limit: '10mb'
}));
app.use(morgan('dev'));
app.use(express.static('public'));

// Configure security middleware
configureSecurityMiddleware(app);

// Add our enhanced security middleware
app.use(security.securityHeaders);
app.use(security.cors());
app.use(sanitizer.sanitize);
app.use(rateLimiter.rateLimiter({
  windowMs: 60 * 1000,
  // 1 minute
  max: 100 // 100 requests per minute
}));

// Add monitoring middleware
app.use(metricsMiddleware);
app.use(tracingMiddleware);
app.use(clusterMiddleware);
app.use(requestMonitoringMiddleware);

// Add enhanced monitoring middleware
app.use(httpMetricsMiddleware);
app.use(enhancedTracingMiddleware);
app.use(requestLoggingMiddleware);

// Rate limiting is now handled by the security middleware

// MongoDB connection
const connectToMongoDB = async () => {
  if (process.env.NODE_ENV === 'test') {
    logger.info('Skipping MongoDB connection in test environment');
    return;
  }
  try {
    const mongoOptions = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      family: 4
    };
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/nova-connect';
    await mongoose.connect(mongoURI, mongoOptions);
    logger.info('Connected to MongoDB successfully');
    mongoose.connection.on('error', err => {
      logger.error('MongoDB connection error:', {
        error: err.message
      });
    });
    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected. Attempting to reconnect...');
    });
    mongoose.connection.on('reconnected', () => {
      logger.info('Reconnected to MongoDB');
    });
    process.on('SIGINT', async () => {
      try {
        await mongoose.connection.close();
        logger.info('MongoDB connection closed through app termination');
        process.exit(0);
      } catch (err) {
        logger.error('Error closing MongoDB connection:', {
          error: err.message
        });
        process.exit(1);
      }
    });
  } catch (err) {
    logger.error('Failed to connect to MongoDB:', {
      error: err.message
    });
  }
};

// Initialize MongoDB connection
connectToMongoDB();

// Routes
app.get('/', (req, res) => {
  res.json({
    name: 'NovaConnect',
    version: '1.0.0',
    description: 'Universal API Connector for seamless API integration',
    cluster: req.cluster ? req.cluster.info : {
      mode: 'single'
    },
    links: [{
      name: 'Error Handling Demo',
      url: '/error-demo.html'
    }, {
      name: 'Monitoring Dashboard',
      url: '/monitoring-dashboard.html'
    }, {
      name: 'Connector Management',
      url: '/connector-management'
    }, {
      name: 'Health Check',
      url: '/health'
    }, {
      name: 'Metrics',
      url: '/metrics'
    }, {
      name: 'API Dashboard',
      url: '/monitoring/dashboard'
    }, {
      name: 'CSDE Dashboard',
      url: '/csde/dashboard'
    }, {
      name: 'CSDE Batch Processing',
      url: '/csde/batch'
    }, {
      name: 'CSDE Advanced Features',
      url: '/csde/advanced'
    }, {
      name: 'CSDE Performance',
      url: '/csde/performance'
    }]
  });
});

// Health and monitoring endpoints
app.get('/health', healthCheckEndpoint);
app.get('/metrics', metricsEndpoint);

// Cluster health endpoint
app.get('/cluster/health', clusterHealth);

// Cache endpoints
app.get('/cache/metrics', cacheMetrics);
app.post('/cache/clear', cacheClear);

// Feature flag endpoints
app.get('/subscription', getUserSubscription);
app.get('/features', getAvailableFeatures);
app.get('/features/usage', getFeatureUsage);
app.put('/subscription/:userId', updateUserSubscription);

// API Routes
app.use('/api', cacheMiddleware({
  ttl: 300,
  // 5 minutes
  methods: ['GET'],
  shouldCache: req => {
    // Don't cache requests with query parameters that affect data
    const noCacheParams = ['refresh', 'nocache', 'timestamp'];
    return !noCacheParams.some(param => req.query[param]);
  }
}), apiRoutes);

// Feature Routes
app.use('/features', featureRoutes);

// Optimization Routes
app.use('/optimization', optimizationRoutes);

// Clear cache for POST, PUT, DELETE requests
app.use('/api', cacheClearMiddleware({
  getKeys: req => {
    // Clear all cache for the resource
    const path = req.path.split('/');
    const resource = path[1]; // First part after /api
    return [`GET:/api/${resource}`];
  },
  shouldClear: req => ['POST', 'PUT', 'DELETE'].includes(req.method)
}));

// Metrics Routes
app.use('/', metricsRoutes);

// Error Demo Routes
app.use('/error-demo', errorDemoRoutes);

// Monitoring Routes
app.use('/monitoring', monitoringRoutes);

// Connector Routes
app.use('/connectors', auth.apiKeyAuth, connectorRoutes);

// Performance Metrics Routes
app.use('/api/metrics', auth.apiKeyAuth, performanceMetricsRoutes);

// CSDE Dashboard Routes
app.use('/csde/dashboard', csdeDashboardRoutes.router);

// CSDE Batch Processing Routes
app.use('/csde/batch', csdeBatchRoutes.router);

// CSDE Advanced Features Routes
app.use('/csde/advanced', csdeAdvancedRoutes.router);

// CSDE Performance Routes
app.use('/csde/performance', csdePerformanceRoutes.router);

// Serve connector management page
app.get('/connector-management', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'connector-management.html'));
});

// Serve performance dashboard
app.get('/performance-dashboard', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'performance-dashboard.html'));
});

// Serve CSDE dashboard page
app.get('/csde/dashboard', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'csde-dashboard.html'));
});

// Serve CSDE batch processing page
app.get('/csde/batch', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'csde-batch.html'));
});

// Serve CSDE advanced features page
app.get('/csde/advanced', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'csde-advanced.html'));
});

// Serve CSDE performance page
app.get('/csde/performance', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'csde-performance.html'));
});

// Serve Compliance App Store static files
app.use('/compliance-store/static', express.static(path.join(__dirname, 'public', 'compliance-store')));

// Serve Compliance App Store
app.get('/compliance-store', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'compliance-store', 'index.html'));
});

// Test route for Compliance App Store
app.get('/compliance-store-test', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'compliance-store', 'test.html'));
});

// Apply circuit breaker to API routes
app.use('/api', circuitBreakerHandler({
  resource: 'api',
  failureThreshold: 5,
  resetTimeout: 30000
}));

// Apply timeout to API routes
app.use('/api', timeoutHandler({
  timeoutMs: 30000
}));

// Apply retry handler to API routes
app.use('/api', retryHandler({
  retryAfter: 1
}));

// 404 handler
app.use(notFoundHandler);

// Error tracking middleware
app.use(errorMetricsMiddleware);
app.use(tracingErrorMiddleware);

// Enhanced error tracking middleware
app.use(errorMetricsMiddleware2);
app.use(enhancedTracingErrorMiddleware);
app.use(errorLoggingMiddleware);

// Enhanced error handler
app.use(errorHandler);

// Start the server
const server = app.listen(PORT, () => {
  logger.info(`NovaConnect server running on port ${PORT}`);
  console.log(`NovaConnect server running on http://localhost:${PORT}`);

  // Log startup to Google Cloud if enabled
  if (googleCloudMonitoring.enabled) {
    googleCloudMonitoring.writeLog('INFO', 'NovaConnect server started', {
      port: PORT,
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0'
    });

    // Create custom dashboard for NovaConnect
    googleCloudMonitoring.createDashboard('NovaConnect Dashboard', ['HTTP Request Rate', 'Error Rate', 'Response Time', 'Connector Health']);

    // Create alert policies
    googleCloudMonitoring.createAlertPolicy('High Error Rate', 'novafuse_api_errors_total', 'rate(5m) > 0.05', ['email']);
    googleCloudMonitoring.createAlertPolicy('High Response Time', 'novafuse_http_request_duration_seconds', 'avg(5m) > 1.0', ['email']);
  }

  // Update active connections metric
  prometheusMetrics.updateActiveConnections(0);
});

// Track connections
server.on('connection', () => {
  const count = server.connections || 1;
  prometheusMetrics.updateActiveConnections(count);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');

  // Log shutdown to Google Cloud if enabled
  if (googleCloudMonitoring.enabled) {
    googleCloudMonitoring.writeLog('INFO', 'NovaConnect server shutting down', {
      reason: 'SIGTERM'
    });
  }
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});
module.exports = app;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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