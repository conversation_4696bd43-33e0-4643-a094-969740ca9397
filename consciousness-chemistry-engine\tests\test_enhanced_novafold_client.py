"""
Tests for the enhanced NovaFoldClient with benchmark validation.
"""

import unittest
import os
from unittest.mock import patch, MagicMock, ANY
from pathlib import Path

# Import the module to test
from src.ConsciousNovaFold import NovaFoldClient, ProteinBenchmark

class TestEnhancedNovaFoldClient(unittest.TestCase):
    """Test cases for NovaFoldClient with benchmark validation."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a test client with benchmark validation enabled
        self.client = NovaFoldClient(enable_benchmark=True)
        
    @patch('src.ConsciousNovaFold.ProteinBenchmark')
    def test_predict_with_validation(self, mock_benchmark):
        """Test prediction with benchmark validation."""
        # Setup mock benchmark
        mock_instance = MagicMock()
        mock_instance.generate_benchmark_report.return_value = {
            'sequence_validation': {'is_valid': True},
            'secondary_structure_validation': {'is_valid': True},
            'metrics_validation': {'is_valid': True}
        }
        mock_benchmark.return_value = mock_instance
        
        # Create a test client with the mock benchmark
        client = NovaFoldClient(enable_benchmark=True, benchmark_class=mock_benchmark)
        
        # Test sequence
        sequence = "ACDEFGHIKLMNPQRSTVWY"
        pdb_id = "1TUP"
        
        # Make prediction with validation
        result = client.predict(sequence, validate_against=pdb_id)
        
        # Verify the result structure
        self.assertIn('validation', result)
        self.assertIn('sequence_validation', result['validation'])
        self.assertIn('secondary_structure_validation', result['validation'])
        self.assertIn('metrics_validation', result['validation'])
        
        # Verify the benchmark was called correctly
        mock_instance.generate_benchmark_report.assert_called_once()
        
    def test_predict_with_invalid_sequence(self):
        """Test prediction with invalid amino acid sequence."""
        # Test sequence with invalid amino acid
        sequence = "ACDEFGHIKLMNPQRSTVWYX"  # X is invalid
        
        # Make prediction
        result = self.client.predict(sequence)
        
        # Verify validation caught the invalid amino acid
        self.assertIn('validation', result)
        self.assertIn('invalid_amino_acids', result['validation'])
        self.assertIn('X', result['validation']['invalid_amino_acids'])
    
    @patch('src.ConsciousNovaFold.requests.get')
    def test_benchmark_integration(self, mock_get):
        """Test integration with the ProteinBenchmark class."""
        # Mock the PDB file download
        mock_pdb_response = MagicMock()
        mock_pdb_response.status_code = 200
        mock_pdb_response.text = """
ATOM      1  N   ALA A   1       1.000   1.000   1.000  1.00 10.00           N  
ATOM      2  CA  ALA A   1       2.000   2.000   2.000  1.00 10.00           C  
        """
        
        # Mock the API response for metrics
        mock_api_response = MagicMock()
        mock_api_response.status_code = 200
        mock_api_response.json.return_value = {
            "rcsb_id": "1ABC",
            "exptl": [{"method": "X-RAY DIFFRACTION"}],
            "rcsb_primary_citation": {"title": "Test Structure"}
        }
        
        # Set up the side effect to return different responses for different URLs
        mock_get.side_effect = [mock_pdb_response, mock_api_response]
        
        # Create a test client with real benchmark
        client = NovaFoldClient(enable_benchmark=True)
        
        # Test sequence
        sequence = "ACDEFGHIKLMNPQRSTVWY"
        
        # Make prediction with validation
        result = client.predict(sequence, validate_against="1ABC")
        
        # Verify the result contains validation data
        self.assertIn('validation', result)
        self.assertIn('sequence_validation', result['validation'])
        self.assertIn('secondary_structure_validation', result['validation'])
        self.assertIn('metrics_validation', result['validation'])

if __name__ == "__main__":
    unittest.main()
