/* Dark Theme for NovaFuse API Superstore */

:root {
  --bg-primary: #172030;
  --bg-secondary: #1e293b;
  --bg-tertiary: #263449;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --accent-primary: #2563eb;
  --accent-secondary: #3b82f6;
  --accent-tertiary: #60a5fa;
  --border-color: #334155;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #0ea5e9;
}

body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Header */
header {
  background-color: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border-color);
}

/* Cards */
.card, 
[class*="card"],
.bg-white,
[class*="bg-white"] {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary);
}

/* Buttons */
.btn-primary,
.bg-blue-600,
[class*="bg-blue-600"] {
  background-color: var(--accent-primary) !important;
  color: white !important;
}

.btn-secondary,
.border-blue-600,
[class*="border-blue-600"] {
  border-color: var(--accent-primary) !important;
  color: var(--accent-primary) !important;
}

/* Text Colors */
.text-gray-700,
.text-gray-800,
.text-gray-900,
[class*="text-gray-700"],
[class*="text-gray-800"],
[class*="text-gray-900"] {
  color: var(--text-primary) !important;
}

.text-gray-500,
.text-gray-600,
[class*="text-gray-500"],
[class*="text-gray-600"] {
  color: var(--text-secondary) !important;
}

.text-gray-400,
[class*="text-gray-400"] {
  color: var(--text-muted) !important;
}

/* Inputs */
input, select, textarea {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

/* Tables */
table {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

th {
  background-color: var(--bg-tertiary);
}

tr:nth-child(even) {
  background-color: var(--bg-tertiary);
}

/* Footer */
footer {
  background-color: var(--bg-secondary) !important;
  border-top: 1px solid var(--border-color);
}

/* Code blocks */
pre, code {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}
