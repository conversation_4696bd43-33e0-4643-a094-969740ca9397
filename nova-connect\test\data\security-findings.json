[{"id": "finding-123456", "source": "gcp-security-command-center", "severity": "HIGH", "category": "VULNERABILITY", "createTime": "2025-05-08T12:34:56.789Z", "updateTime": "2025-05-08T13:45:12.345Z", "resourceName": "projects/my-project/zones/us-central1-a/instances/my-instance", "resourceType": "google.compute.Instance", "state": "ACTIVE", "externalUri": "https://console.cloud.google.com/security/command-center/findings?project=my-project", "sourceProperties": {"scanConfigId": "scan-config-12345", "scanRunId": "scan-run-67890", "vulnerabilityType": "XSS", "vulnerabilityDetails": {"cvssScore": 8.5, "cvssVector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H", "cve": "CVE-2025-12345", "description": "Cross-site scripting vulnerability in web application", "references": ["https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2025-12345", "https://nvd.nist.gov/vuln/detail/CVE-2025-12345"], "fixAvailable": true, "exploitAvailable": true}}, "securityMarks": {"marks": {"criticality": "p0", "compliance": "pci-dss,hipaa,soc2", "dataClassification": "confidential"}}, "nextSteps": ["Update web application to latest version", "Apply security patch", "Implement input validation"], "complianceState": "NON_COMPLIANT"}, {"id": "finding-123457", "source": "gcp-security-command-center", "severity": "MEDIUM", "category": "MISCONFIGURATION", "createTime": "2025-05-07T10:22:33.456Z", "updateTime": "2025-05-08T09:11:22.333Z", "resourceName": "projects/my-project/global/firewalls/allow-all-ingress", "resourceType": "google.compute.Firewall", "state": "ACTIVE", "externalUri": "https://console.cloud.google.com/security/command-center/findings?project=my-project", "sourceProperties": {"scanConfigId": "scan-config-12345", "scanRunId": "scan-run-67891", "misconfigurationType": "OVERLY_PERMISSIVE_FIREWALL", "misconfigurationDetails": {"description": "Firewall rule allows unrestricted ingress traffic", "recommendation": "Restrict firewall rules to only allow necessary traffic", "impact": "Potential unauthorized access to resources", "remediation": "Update firewall rules to restrict traffic to specific IP ranges and ports"}}, "securityMarks": {"marks": {"criticality": "p1", "compliance": "pci-dss,soc2", "dataClassification": "internal"}}, "nextSteps": ["Review firewall rules", "Implement least privilege access", "Document exceptions"], "complianceState": "NON_COMPLIANT"}, {"id": "finding-123458", "source": "gcp-security-command-center", "severity": "CRITICAL", "category": "IAM", "createTime": "2025-05-06T08:15:30.123Z", "updateTime": "2025-05-08T11:22:33.444Z", "resourceName": "projects/my-project/serviceAccounts/<EMAIL>", "resourceType": "google.iam.ServiceAccount", "state": "ACTIVE", "externalUri": "https://console.cloud.google.com/security/command-center/findings?project=my-project", "sourceProperties": {"scanConfigId": "scan-config-12345", "scanRunId": "scan-run-67892", "iamType": "OVER_PRIVILEGED_SERVICE_ACCOUNT", "iamDetails": {"description": "Service account has owner permissions", "recommendation": "Apply principle of least privilege", "impact": "Potential for privilege escalation and unauthorized access", "remediation": "Reduce service account permissions to only what is necessary"}}, "securityMarks": {"marks": {"criticality": "p0", "compliance": "pci-dss,hipaa,soc2,gdpr", "dataClassification": "restricted"}}, "nextSteps": ["Review service account permissions", "Implement least privilege access", "Set up regular permission reviews"], "complianceState": "NON_COMPLIANT"}, {"id": "finding-123459", "source": "gcp-security-command-center", "severity": "LOW", "category": "STORAGE", "createTime": "2025-05-05T14:25:36.789Z", "updateTime": "2025-05-08T08:09:10.111Z", "resourceName": "projects/my-project/buckets/my-bucket", "resourceType": "google.storage.Bucket", "state": "ACTIVE", "externalUri": "https://console.cloud.google.com/security/command-center/findings?project=my-project", "sourceProperties": {"scanConfigId": "scan-config-12345", "scanRunId": "scan-run-67893", "storageType": "BUCKET_VERSIONING_DISABLED", "storageDetails": {"description": "Cloud Storage bucket versioning is disabled", "recommendation": "Enable bucket versioning", "impact": "Potential data loss or inability to recover from accidental deletion", "remediation": "Enable versioning on the bucket"}}, "securityMarks": {"marks": {"criticality": "p2", "compliance": "soc2", "dataClassification": "internal"}}, "nextSteps": ["Enable bucket versioning", "Set up object lifecycle management"], "complianceState": "NON_COMPLIANT"}, {"id": "finding-123460", "source": "gcp-security-command-center", "severity": "HIGH", "category": "NETWORK", "createTime": "2025-05-04T16:17:18.192Z", "updateTime": "2025-05-08T10:11:12.131Z", "resourceName": "projects/my-project/global/networks/default", "resourceType": "google.compute.Network", "state": "ACTIVE", "externalUri": "https://console.cloud.google.com/security/command-center/findings?project=my-project", "sourceProperties": {"scanConfigId": "scan-config-12345", "scanRunId": "scan-run-67894", "networkType": "EXPOSED_SENSITIVE_PORT", "networkDetails": {"description": "Sensitive ports exposed to the internet", "recommendation": "Restrict access to sensitive ports", "impact": "Potential unauthorized access to services", "remediation": "Configure firewall rules to restrict access to sensitive ports", "exposedPorts": [22, 3389, 1433]}}, "securityMarks": {"marks": {"criticality": "p1", "compliance": "pci-dss,hipaa,soc2", "dataClassification": "confidential"}}, "nextSteps": ["Configure firewall rules", "Implement VPC Service Controls", "Set up network monitoring"], "complianceState": "NON_COMPLIANT"}]