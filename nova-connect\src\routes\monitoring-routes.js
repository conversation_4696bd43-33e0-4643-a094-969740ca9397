/**
 * NovaFuse Universal API Connector - Monitoring Routes
 * 
 * This module provides routes for monitoring and observability.
 */

const express = require('express');
const router = express.Router();
const { asyncHandler } = require('../utils/async-handler');
const metricsService = require('../monitoring/metrics-service');
const healthService = require('../monitoring/health-service');
const dashboardService = require('../monitoring/dashboard-service');
const tracingService = require('../monitoring/tracing-service');

/**
 * @route GET /metrics
 * @description Get metrics in Prometheus format
 * @access Public
 */
router.get('/metrics', (req, res) => {
  const metrics = metricsService.getMetricsAsPrometheusFormat();
  res.set('Content-Type', 'text/plain');
  res.send(metrics);
});

/**
 * @route GET /health
 * @description Get health check status
 * @access Public
 */
router.get('/health', asyncHandler(async (req, res) => {
  const health = await healthService.runChecks();
  
  // Set status code based on health status
  if (health.status === 'error') {
    res.status(500);
  } else if (health.status === 'warn') {
    res.status(200);
  } else {
    res.status(200);
  }
  
  res.json(health);
}));

/**
 * @route GET /health/live
 * @description Get liveness check
 * @access Public
 */
router.get('/health/live', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

/**
 * @route GET /health/ready
 * @description Get readiness check
 * @access Public
 */
router.get('/health/ready', asyncHandler(async (req, res) => {
  const health = await healthService.runChecks();
  
  // Set status code based on health status
  if (health.status === 'error') {
    res.status(500);
  } else {
    res.status(200);
  }
  
  res.json({
    status: health.status,
    timestamp: health.timestamp,
    checks: health.checks
  });
}));

/**
 * @route GET /dashboard
 * @description Get monitoring dashboard data
 * @access Public
 */
router.get('/dashboard', (req, res) => {
  const dashboard = dashboardService.getDashboardData();
  res.json(dashboard);
});

/**
 * @route GET /dashboard/system
 * @description Get system information
 * @access Public
 */
router.get('/dashboard/system', (req, res) => {
  const { system } = dashboardService.getDashboardData();
  res.json(system);
});

/**
 * @route GET /dashboard/requests
 * @description Get request metrics
 * @access Public
 */
router.get('/dashboard/requests', (req, res) => {
  const { requests } = dashboardService.getDashboardData();
  res.json(requests);
});

/**
 * @route GET /dashboard/errors
 * @description Get error metrics
 * @access Public
 */
router.get('/dashboard/errors', (req, res) => {
  const { errors } = dashboardService.getDashboardData();
  res.json(errors);
});

/**
 * @route POST /traces
 * @description Submit a trace
 * @access Public
 */
router.post('/traces', asyncHandler(async (req, res) => {
  const { name, traceId, parentSpanId, sampled, attributes, events } = req.body;
  
  if (!name) {
    res.status(400).json({
      error: {
        message: 'Trace name is required',
        code: 'VALIDATION_ERROR'
      }
    });
    return;
  }
  
  // Create trace context
  const traceContext = tracingService.startTrace(name, {
    traceId,
    parentSpanId,
    sampled,
    attributes
  });
  
  // Add events
  if (events && Array.isArray(events)) {
    for (const event of events) {
      traceContext.addEvent(event.name, event.attributes);
    }
  }
  
  // End trace
  tracingService.endTrace(traceContext);
  
  res.status(201).json({
    success: true,
    traceId: traceContext.traceId,
    spanId: traceContext.spanId
  });
}));

module.exports = router;
