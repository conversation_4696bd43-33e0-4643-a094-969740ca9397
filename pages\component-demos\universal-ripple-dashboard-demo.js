import React, { useState } from 'react';
import Head from 'next/head';
import { motion } from 'framer-motion';

// Import layout components
import MainLayout from '../../components/layouts/MainLayout';
import PageHeader from '../../components/common/PageHeader';
import Section from '../../components/common/Section';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Tabs from '../../components/common/Tabs';

// Import the Universal Ripple Dashboard component
import UniversalRippleDashboard from '../../components/trinity/UniversalRippleDashboard';

/**
 * Universal Ripple Stack Dashboard Demo Page
 * 
 * This page showcases the Universal Ripple Stack Dashboard,
 * demonstrating the operational metrics and system health of the NovaFuse ecosystem.
 */
const UniversalRippleDashboardDemo = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  
  // Tab configuration
  const tabs = [
    { id: 'dashboard', label: 'Dashboard' },
    { id: 'components', label: 'Components' },
    { id: 'metrics', label: 'Metrics' },
    { id: 'integration', label: 'Integration' }
  ];
  
  return (
    <>
      <Head>
        <title>Universal Ripple Stack Dashboard | NovaFuse</title>
        <meta name="description" content="Experience the Universal Ripple Stack Dashboard, showing real-time metrics and system health of the NovaFuse ecosystem." />
      </Head>
      
      <MainLayout>
        <PageHeader
          title="Universal Ripple Stack Dashboard"
          subtitle="Real-time Monitoring of the NovaFuse Ecosystem"
          gradient="from-blue-500 via-purple-500 to-green-500"
        />
        
        <Section className="mb-8">
          <Card className="mb-6 p-6">
            <h2 className="text-2xl font-semibold mb-4">About the Universal Ripple Stack</h2>
            <p className="mb-4">
              The Universal Ripple Stack is the operational core of NovaFuse, implementing the nested 
              Trinity architecture and creating a full-spectrum Ripple Effect capability across all three layers.
            </p>
            <p>
              This dashboard provides real-time monitoring of the Universal Ripple Stack, showing system health, 
              component status, and operational metrics across the NovaFuse ecosystem.
            </p>
          </Card>
          
          <Tabs
            tabs={tabs}
            activeTab={activeTab}
            onChange={setActiveTab}
            className="mb-6"
          />
          
          {activeTab === 'dashboard' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-gray-900 rounded-lg overflow-hidden"
              style={{ height: '700px' }}
            >
              <UniversalRippleDashboard width="100%" height="100%" />
            </motion.div>
          )}
          
          {activeTab === 'components' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="mb-6 p-6">
                <h3 className="text-xl font-semibold mb-4">Universal Ripple Stack Components</h3>
                <p className="mb-4">
                  The Universal Ripple Stack consists of four key components that work together to create 
                  a full-spectrum Ripple Effect capability:
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                  <div className="bg-blue-100 dark:bg-blue-900/30 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h4 className="text-lg font-medium text-blue-700 dark:text-blue-400 mb-2">NovaConnect (Nova 10)</h4>
                    <p className="text-sm mb-3">The circulatory system of NovaFuse, providing integration and interoperability.</p>
                    <ul className="list-disc list-inside text-sm space-y-1">
                      <li><strong>Function:</strong> Connects all components and external systems</li>
                      <li><strong>Key Metrics:</strong> Active connections, data flow rate, latency</li>
                      <li><strong>Ripple Effect:</strong> Creates the pathways for energy to flow between systems</li>
                    </ul>
                  </div>
                  
                  <div className="bg-purple-100 dark:bg-purple-900/30 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
                    <h4 className="text-lg font-medium text-purple-700 dark:text-purple-400 mb-2">NovaThink (Nova 9)</h4>
                    <p className="text-sm mb-3">The brain of NovaFuse, providing AI-driven decision making.</p>
                    <ul className="list-disc list-inside text-sm space-y-1">
                      <li><strong>Function:</strong> Makes intelligent decisions based on data</li>
                      <li><strong>Key Metrics:</strong> Decision accuracy, confidence, ethical tensor</li>
                      <li><strong>Ripple Effect:</strong> Amplifies decision quality across connected systems</li>
                    </ul>
                  </div>
                  
                  <div className="bg-green-100 dark:bg-green-900/30 p-4 rounded-lg border border-green-200 dark:border-green-800">
                    <h4 className="text-lg font-medium text-green-700 dark:text-green-400 mb-2">NovaPulse+ (Nova 7)</h4>
                    <p className="text-sm mb-3">The voice of NovaFuse, providing real-time compliance monitoring.</p>
                    <ul className="list-disc list-inside text-sm space-y-1">
                      <li><strong>Function:</strong> Monitors compliance and generates alerts</li>
                      <li><strong>Key Metrics:</strong> Compliance rate, controls covered, alert accuracy</li>
                      <li><strong>Ripple Effect:</strong> Propagates compliance signals throughout the ecosystem</li>
                    </ul>
                  </div>
                  
                  <div className="bg-orange-100 dark:bg-orange-900/30 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
                    <h4 className="text-lg font-medium text-orange-700 dark:text-orange-400 mb-2">NovaFlowX (Nova 6)</h4>
                    <p className="text-sm mb-3">The hands of NovaFuse, providing workflow automation.</p>
                    <ul className="list-disc list-inside text-sm space-y-1">
                      <li><strong>Function:</strong> Automates workflows and processes</li>
                      <li><strong>Key Metrics:</strong> Automation rate, completion time, user satisfaction</li>
                      <li><strong>Ripple Effect:</strong> Extends operational efficiency to connected systems</li>
                    </ul>
                  </div>
                </div>
              </Card>
            </motion.div>
          )}
          
          {activeTab === 'metrics' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="mb-6 p-6">
                <h3 className="text-xl font-semibold mb-4">Universal Ripple Stack Metrics</h3>
                <p className="mb-4">
                  The Universal Ripple Stack Dashboard monitors several key metrics that provide insight 
                  into the health and performance of the NovaFuse ecosystem:
                </p>
                
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-gray-100 dark:bg-gray-800 rounded-lg">
                    <thead>
                      <tr>
                        <th className="px-4 py-2 text-left">Metric</th>
                        <th className="px-4 py-2 text-left">Description</th>
                        <th className="px-4 py-2 text-left">Target Range</th>
                        <th className="px-4 py-2 text-left">Impact</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border-t px-4 py-2">System Health</td>
                        <td className="border-t px-4 py-2">Overall health of the Universal Ripple Stack</td>
                        <td className="border-t px-4 py-2">90-100%</td>
                        <td className="border-t px-4 py-2">Affects all system operations</td>
                      </tr>
                      <tr>
                        <td className="border-t px-4 py-2">Trinity Balance</td>
                        <td className="border-t px-4 py-2">Balance between Governance, Detection, and Response</td>
                        <td className="border-t px-4 py-2">85-95%</td>
                        <td className="border-t px-4 py-2">Determines system resilience</td>
                      </tr>
                      <tr>
                        <td className="border-t px-4 py-2">Ripple Strength</td>
                        <td className="border-t px-4 py-2">Strength of the Ripple Effect across all three layers</td>
                        <td className="border-t px-4 py-2">75-90%</td>
                        <td className="border-t px-4 py-2">Determines enhancement capability</td>
                      </tr>
                      <tr>
                        <td className="border-t px-4 py-2">Active Connections</td>
                        <td className="border-t px-4 py-2">Number of active connections to external systems</td>
                        <td className="border-t px-4 py-2">18-82</td>
                        <td className="border-t px-4 py-2">Determines integration breadth</td>
                      </tr>
                      <tr>
                        <td className="border-t px-4 py-2">Prediction Confidence</td>
                        <td className="border-t px-4 py-2">Confidence level of AI predictions</td>
                        <td className="border-t px-4 py-2">80-95%</td>
                        <td className="border-t px-4 py-2">Determines decision quality</td>
                      </tr>
                      <tr>
                        <td className="border-t px-4 py-2">Response Time</td>
                        <td className="border-t px-4 py-2">Time to respond to events</td>
                        <td className="border-t px-4 py-2">≤18ms</td>
                        <td className="border-t px-4 py-2">Determines system responsiveness</td>
                      </tr>
                      <tr>
                        <td className="border-t px-4 py-2">Compliance Rate</td>
                        <td className="border-t px-4 py-2">Percentage of compliance requirements met</td>
                        <td className="border-t px-4 py-2">95-100%</td>
                        <td className="border-t px-4 py-2">Determines regulatory standing</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </Card>
            </motion.div>
          )}
          
          {activeTab === 'integration' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="mb-6 p-6">
                <h3 className="text-xl font-semibold mb-4">Integration with NovaFuse Components</h3>
                <p className="mb-4">
                  The Universal Ripple Stack Dashboard integrates with all NovaFuse components, 
                  providing a comprehensive view of the entire ecosystem:
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 className="text-lg font-medium mb-2">NovaCore Integration</h4>
                    <p className="text-sm">
                      The dashboard connects to NovaCore to monitor the foundational services of NovaFuse:
                    </p>
                    <ul className="list-disc list-inside text-sm mt-2">
                      <li>Authentication and authorization services</li>
                      <li>Data storage and retrieval</li>
                      <li>System configuration and management</li>
                    </ul>
                  </div>
                  
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 className="text-lg font-medium mb-2">NovaShield Integration</h4>
                    <p className="text-sm">
                      The dashboard connects to NovaShield to monitor security operations:
                    </p>
                    <ul className="list-disc list-inside text-sm mt-2">
                      <li>Threat detection and response</li>
                      <li>Vulnerability management</li>
                      <li>Security posture assessment</li>
                    </ul>
                  </div>
                  
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 className="text-lg font-medium mb-2">NovaTrack Integration</h4>
                    <p className="text-sm">
                      The dashboard connects to NovaTrack to monitor compliance operations:
                    </p>
                    <ul className="list-disc list-inside text-sm mt-2">
                      <li>Compliance status and gaps</li>
                      <li>Control effectiveness</li>
                      <li>Audit readiness</li>
                    </ul>
                  </div>
                  
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 className="text-lg font-medium mb-2">NovaStore Integration</h4>
                    <p className="text-sm">
                      The dashboard connects to NovaStore to monitor the enhancement ecosystem:
                    </p>
                    <ul className="list-disc list-inside text-sm mt-2">
                      <li>Partner integration status</li>
                      <li>Enhancement performance</li>
                      <li>Revenue sharing metrics</li>
                    </ul>
                  </div>
                </div>
                
                <div className="mt-8">
                  <h4 className="text-lg font-medium mb-4">API Integration</h4>
                  <p className="mb-4">
                    The Universal Ripple Stack Dashboard provides a comprehensive API for integration with external systems:
                  </p>
                  <pre className="bg-gray-200 dark:bg-gray-900 p-3 rounded text-xs overflow-x-auto">
                    {`// Example API call to get system health
fetch('https://api.novafuse.com/v1/universal-ripple/health', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer ${token}',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  console.log('System Health:', data);
})
.catch(error => {
  console.error('Error:', error);
});`}
                  </pre>
                </div>
              </Card>
              
              <div className="flex justify-center mt-8">
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => setActiveTab('dashboard')}
                >
                  Experience the Dashboard
                </Button>
              </div>
            </motion.div>
          )}
        </Section>
      </MainLayout>
    </>
  );
};

export default UniversalRippleDashboardDemo;
