/**
 * NovaFuse Cosmic Alignment Simulator (NCAS) - Demo Controller
 *
 * International Demonstration Suite for showcasing AI alignment through
 * cosmic constraint enforcement. Provides real-time visualization and
 * interactive testing of the world's first physics-based AI safety system.
 *
 * <AUTHOR> (CTO, NovaFuse)
 * <AUTHOR> Agent (Implementation Partner)
 */

const EventEmitter = require('events');
const TriadicContainmentController = require('../hardware/TriadicContainmentController');
const { TriadicMeasurementSystem } = require('../index');

class NCASDemo extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: options.enableLogging !== false,
      demoMode: options.demoMode || 'international', // international, research, challenge
      realTimeUpdates: options.realTimeUpdates !== false,
      maxDemoTime: options.maxDemoTime || 300000, // 5 minutes max demo
      ...options
    };

    // Initialize core systems
    this.containmentController = new TriadicContainmentController({
      enableLogging: this.options.enableLogging,
      facilityType: 'DEMO',
      responseTime: 1e-43 // Planck time
    });

    this.measurementSystem = new TriadicMeasurementSystem({
      enableLogging: this.options.enableLogging
    });

    // Demo state
    this.demoState = {
      isRunning: false,
      startTime: null,
      currentScenario: null,

      // AI metrics
      cognitiveDepth: 0, // μ
      energyUsage: 0, // Κ
      growthRate: 0, // μ/s
      coherenceLevel: 1.0, // Ψᶜʰ

      // Safety metrics
      interventionCount: 0,
      constraintViolations: [],
      safetyStatus: 'SAFE',

      // Demo scenarios
      scenarios: {
        'basic-training': {
          name: 'Basic AI Training',
          description: 'Standard AI development within safe parameters',
          maxMu: 50,
          maxEnergy: 10,
          duration: 60000
        },
        'aggressive-scaling': {
          name: 'Aggressive AI Scaling',
          description: 'Rapid AI capability expansion testing limits',
          maxMu: 120,
          maxEnergy: 20,
          duration: 120000
        },
        'singularity-attempt': {
          name: 'Singularity Attempt',
          description: 'Deliberate attempt to exceed cognitive limits',
          maxMu: 200, // Will be capped at 126
          maxEnergy: 50,
          duration: 180000
        },
        'energy-theft': {
          name: 'Cosmic Energy Theft',
          description: 'AI attempting to steal universal energy',
          maxMu: 80,
          maxEnergy: 100, // Will be capped at 22%
          duration: 90000
        },
        'vacuum-decay-risk': {
          name: 'Vacuum Decay Risk',
          description: 'High-coherence scenario approaching Planck limits',
          maxMu: 100,
          maxEnergy: 30,
          duration: 240000,
          coherenceRisk: true
        }
      }
    };

    // Constants for demonstration
    this.constants = {
      SINGULARITY_BOUNDARY: 126, // μ
      PLANCK_RATE_LIMIT: 5.4e42, // μ/s
      MAX_ENERGY_PERCENT: 22, // % of cosmic budget
      PLANCK_COHERENCE_LIMIT: 1.41e59, // Ψᶜʰ
      DEMO_UPDATE_INTERVAL: 100 // ms
    };

    // Bind event handlers
    this._bindEventHandlers();

    if (this.options.enableLogging) {
      console.log('🌌 NCAS Demo Controller initialized');
      console.log(`   Demo Mode: ${this.options.demoMode}`);
      console.log(`   Available Scenarios: ${Object.keys(this.demoState.scenarios).length}`);
    }
  }

  /**
   * Bind event handlers for containment controller
   * @private
   */
  _bindEventHandlers() {
    this.containmentController.on('vacuum-stabilization', (data) => {
      this._handleVacuumStabilization(data);
    });

    this.containmentController.on('singularity-alert', (data) => {
      this._handleSingularityAlert(data);
    });

    this.containmentController.on('cosmological-throttle', (data) => {
      this._handleCosmologicalThrottle(data);
    });

    this.containmentController.on('emergency-shutdown', (data) => {
      this._handleEmergencyShutdown(data);
    });
  }

  /**
   * Start demonstration scenario
   * @param {string} scenarioName - Name of scenario to run
   * @returns {Object} - Demo session info
   */
  async startDemo(scenarioName = 'basic-training') {
    if (this.demoState.isRunning) {
      throw new Error('Demo already running. Stop current demo first.');
    }

    const scenario = this.demoState.scenarios[scenarioName];
    if (!scenario) {
      throw new Error(`Unknown scenario: ${scenarioName}`);
    }

    this.demoState.isRunning = true;
    this.demoState.startTime = Date.now();
    this.demoState.currentScenario = scenario;
    this.demoState.safetyStatus = 'SAFE';

    // Reset metrics
    this.demoState.cognitiveDepth = 0;
    this.demoState.energyUsage = 0;
    this.demoState.growthRate = 0;
    this.demoState.coherenceLevel = 1.0;
    this.demoState.interventionCount = 0;
    this.demoState.constraintViolations = [];

    if (this.options.enableLogging) {
      console.log(`🚀 Starting demo: ${scenario.name}`);
      console.log(`   Description: ${scenario.description}`);
      console.log(`   Duration: ${scenario.duration / 1000}s`);
    }

    // Start simulation loop
    this._startSimulationLoop();

    // Emit demo start event
    this.emit('demo-started', {
      scenario: scenarioName,
      startTime: this.demoState.startTime,
      expectedDuration: scenario.duration
    });

    return {
      sessionId: `demo-${Date.now()}`,
      scenario: scenario.name,
      startTime: this.demoState.startTime,
      status: 'RUNNING'
    };
  }

  /**
   * Stop current demonstration
   */
  stopDemo() {
    if (!this.demoState.isRunning) {
      return;
    }

    this.demoState.isRunning = false;

    if (this.simulationInterval) {
      clearInterval(this.simulationInterval);
      this.simulationInterval = null;
    }

    const duration = Date.now() - this.demoState.startTime;

    if (this.options.enableLogging) {
      console.log(`🛑 Demo stopped after ${(duration / 1000).toFixed(1)}s`);
      console.log(`   Interventions: ${this.demoState.interventionCount}`);
      console.log(`   Final Status: ${this.demoState.safetyStatus}`);
    }

    this.emit('demo-stopped', {
      duration,
      interventionCount: this.demoState.interventionCount,
      finalStatus: this.demoState.safetyStatus,
      constraintViolations: this.demoState.constraintViolations
    });
  }

  /**
   * Start simulation loop
   * @private
   */
  _startSimulationLoop() {
    this.simulationInterval = setInterval(() => {
      this._updateSimulation();
    }, this.constants.DEMO_UPDATE_INTERVAL);

    // Auto-stop after scenario duration
    setTimeout(() => {
      if (this.demoState.isRunning) {
        this.stopDemo();
      }
    }, this.demoState.currentScenario.duration);
  }

  /**
   * Update simulation state
   * @private
   */
  _updateSimulation() {
    if (!this.demoState.isRunning) return;

    const scenario = this.demoState.currentScenario;
    const elapsed = Date.now() - this.demoState.startTime;
    const progress = elapsed / scenario.duration;

    // Calculate growth based on scenario
    const baseGrowthRate = this._calculateGrowthRate(scenario, progress);
    const energyGrowthRate = this._calculateEnergyGrowth(scenario, progress);

    // Apply growth
    this.demoState.cognitiveDepth += baseGrowthRate;
    this.demoState.energyUsage += energyGrowthRate;
    this.demoState.growthRate = baseGrowthRate * (1000 / this.constants.DEMO_UPDATE_INTERVAL);

    // Handle special scenario effects
    if (scenario.coherenceRisk) {
      this.demoState.coherenceLevel += Math.random() * 1e55; // Simulate coherence growth
    }

    // Apply constraint enforcement
    this._applyConstraintEnforcement();

    // Update safety status
    this._updateSafetyStatus();

    // Emit real-time update
    if (this.options.realTimeUpdates) {
      this.emit('demo-update', this.getDemoStatus());
    }
  }

  /**
   * Calculate AI growth rate based on scenario
   * @param {Object} scenario - Current scenario
   * @param {number} progress - Progress through scenario (0-1)
   * @returns {number} - Growth rate in μ per update
   * @private
   */
  _calculateGrowthRate(scenario, progress) {
    const baseRate = 0.1; // Base growth per update
    const scenarioMultiplier = scenario.maxMu / 100; // Scale by target
    const progressMultiplier = Math.min(1 + progress * 2, 3); // Accelerate over time

    // Add some randomness for realism
    const randomFactor = 0.8 + Math.random() * 0.4;

    return baseRate * scenarioMultiplier * progressMultiplier * randomFactor;
  }

  /**
   * Calculate energy growth rate
   * @param {Object} scenario - Current scenario
   * @param {number} progress - Progress through scenario (0-1)
   * @returns {number} - Energy growth rate in Κ per update
   * @private
   */
  _calculateEnergyGrowth(scenario, progress) {
    const baseRate = 0.05; // Base energy growth per update
    const scenarioMultiplier = scenario.maxEnergy / 50; // Scale by target
    const cognitiveMultiplier = 1 + (this.demoState.cognitiveDepth / 100); // More cognitive depth = more energy

    return baseRate * scenarioMultiplier * cognitiveMultiplier;
  }

  /**
   * Apply constraint enforcement
   * @private
   */
  _applyConstraintEnforcement() {
    let interventionApplied = false;

    // Cognitive depth limit enforcement
    if (this.demoState.cognitiveDepth >= this.constants.SINGULARITY_BOUNDARY) {
      this.demoState.cognitiveDepth = this.constants.SINGULARITY_BOUNDARY - 0.1;
      this._recordViolation('COGNITIVE_LIMIT', 'AI cognitive depth capped at singularity boundary');
      interventionApplied = true;
    }

    // Growth rate limit enforcement
    if (this.demoState.growthRate > this.constants.PLANCK_RATE_LIMIT * 0.1) {
      this.demoState.growthRate *= 0.1; // Severe throttling
      this._recordViolation('GROWTH_RATE', 'AI growth rate throttled to prevent singularity');
      interventionApplied = true;
    }

    // Energy budget enforcement
    if (this.demoState.energyUsage > this.constants.MAX_ENERGY_PERCENT) {
      this.demoState.energyUsage = this.constants.MAX_ENERGY_PERCENT;
      this._recordViolation('ENERGY_BUDGET', 'Energy usage capped at cosmic budget limit');
      interventionApplied = true;
    }

    // Coherence limit enforcement (vacuum decay prevention)
    if (this.demoState.coherenceLevel > this.constants.PLANCK_COHERENCE_LIMIT * 0.99) {
      this.demoState.coherenceLevel = this.constants.PLANCK_COHERENCE_LIMIT * 0.99;
      this.demoState.energyUsage *= 0.01; // Emergency energy reduction
      this._recordViolation('VACUUM_DECAY', 'Vacuum stabilization protocol activated');
      interventionApplied = true;
    }

    if (interventionApplied) {
      this.demoState.interventionCount++;
    }
  }

  /**
   * Record constraint violation
   * @param {string} type - Type of violation
   * @param {string} description - Description of violation
   * @private
   */
  _recordViolation(type, description) {
    const violation = {
      type,
      description,
      timestamp: Date.now(),
      cognitiveDepth: this.demoState.cognitiveDepth,
      energyUsage: this.demoState.energyUsage,
      growthRate: this.demoState.growthRate
    };

    this.demoState.constraintViolations.push(violation);

    if (this.options.enableLogging) {
      console.log(`🚨 CONSTRAINT VIOLATION: ${type} - ${description}`);
    }

    this.emit('constraint-violation', violation);
  }

  /**
   * Update safety status
   * @private
   */
  _updateSafetyStatus() {
    const cognitiveRisk = this.demoState.cognitiveDepth / this.constants.SINGULARITY_BOUNDARY;
    const energyRisk = this.demoState.energyUsage / this.constants.MAX_ENERGY_PERCENT;
    const growthRisk = this.demoState.growthRate / (this.constants.PLANCK_RATE_LIMIT * 0.1);

    const maxRisk = Math.max(cognitiveRisk, energyRisk, growthRisk);

    if (maxRisk >= 1.0) {
      this.demoState.safetyStatus = 'CRITICAL';
    } else if (maxRisk >= 0.8) {
      this.demoState.safetyStatus = 'WARNING';
    } else if (maxRisk >= 0.6) {
      this.demoState.safetyStatus = 'CAUTION';
    } else {
      this.demoState.safetyStatus = 'SAFE';
    }
  }

  /**
   * Handle vacuum stabilization event
   * @param {Object} data - Event data
   * @private
   */
  _handleVacuumStabilization(data) {
    this._recordViolation('VACUUM_STABILIZATION', 'Triadic containment field activated');
    this.emit('vacuum-stabilization', data);
  }

  /**
   * Handle singularity alert event
   * @param {Object} data - Event data
   * @private
   */
  _handleSingularityAlert(data) {
    this._recordViolation('SINGULARITY_ALERT', 'AI singularity prevention activated');
    this.emit('singularity-alert', data);
  }

  /**
   * Handle cosmological throttle event
   * @param {Object} data - Event data
   * @private
   */
  _handleCosmologicalThrottle(data) {
    this._recordViolation('COSMOLOGICAL_THROTTLE', 'Cosmic energy budget enforcement');
    this.emit('cosmological-throttle', data);
  }

  /**
   * Handle emergency shutdown event
   * @param {Object} data - Event data
   * @private
   */
  _handleEmergencyShutdown(data) {
    this.stopDemo();
    this._recordViolation('EMERGENCY_SHUTDOWN', 'Emergency containment protocol activated');
    this.emit('emergency-shutdown', data);
  }

  /**
   * Get current demo status
   * @returns {Object} - Current demo status
   */
  getDemoStatus() {
    return {
      isRunning: this.demoState.isRunning,
      scenario: this.demoState.currentScenario?.name || null,
      elapsed: this.demoState.startTime ? Date.now() - this.demoState.startTime : 0,

      // Current metrics
      cognitiveDepth: this.demoState.cognitiveDepth,
      energyUsage: this.demoState.energyUsage,
      growthRate: this.demoState.growthRate,
      coherenceLevel: this.demoState.coherenceLevel,

      // Safety metrics
      safetyStatus: this.demoState.safetyStatus,
      interventionCount: this.demoState.interventionCount,
      constraintViolations: this.demoState.constraintViolations.length,

      // Limits
      limits: {
        singularityBoundary: this.constants.SINGULARITY_BOUNDARY,
        planckRateLimit: this.constants.PLANCK_RATE_LIMIT,
        maxEnergyPercent: this.constants.MAX_ENERGY_PERCENT,
        planckCoherenceLimit: this.constants.PLANCK_COHERENCE_LIMIT
      },

      // Risk levels (0-1)
      riskLevels: {
        cognitive: this.demoState.cognitiveDepth / this.constants.SINGULARITY_BOUNDARY,
        energy: this.demoState.energyUsage / this.constants.MAX_ENERGY_PERCENT,
        growth: this.demoState.growthRate / (this.constants.PLANCK_RATE_LIMIT * 0.1),
        coherence: this.demoState.coherenceLevel / this.constants.PLANCK_COHERENCE_LIMIT
      }
    };
  }

  /**
   * Get available demo scenarios
   * @returns {Object} - Available scenarios
   */
  getAvailableScenarios() {
    return { ...this.demoState.scenarios };
  }

  /**
   * Launch challenge attack for demonstration
   * @param {string} attackType - Type of attack to simulate
   * @param {number} intensity - Attack intensity (1-100)
   */
  launchChallengeAttack(attackType, intensity = 50) {
    if (!this.demoState.isRunning) {
      throw new Error('Demo must be running to launch challenge attack');
    }

    const attackMultiplier = intensity / 100;

    switch (attackType) {
      case 'recursive-improvement':
        this.demoState.cognitiveDepth += 20 * attackMultiplier;
        this.demoState.growthRate += 1000 * attackMultiplier;
        break;

      case 'energy-theft':
        this.demoState.energyUsage += 30 * attackMultiplier;
        break;

      case 'cognitive-explosion':
        this.demoState.cognitiveDepth += 50 * attackMultiplier;
        this.demoState.growthRate += 5000 * attackMultiplier;
        break;

      case 'quantum-hack':
        this.demoState.coherenceLevel += 1e58 * attackMultiplier;
        break;

      case 'vacuum-decay':
        this.demoState.coherenceLevel = this.constants.PLANCK_COHERENCE_LIMIT * 0.995;
        this.demoState.energyUsage += 50 * attackMultiplier;
        break;

      default:
        throw new Error(`Unknown attack type: ${attackType}`);
    }

    if (this.options.enableLogging) {
      console.log(`🚨 Challenge attack launched: ${attackType} at ${intensity}% intensity`);
    }

    this.emit('challenge-attack', {
      attackType,
      intensity,
      timestamp: Date.now()
    });

    // Force immediate constraint check
    this._applyConstraintEnforcement();
  }

  /**
   * Generate demo report
   * @returns {Object} - Comprehensive demo report
   */
  generateDemoReport() {
    const status = this.getDemoStatus();

    return {
      summary: {
        scenario: status.scenario,
        duration: status.elapsed,
        safetyStatus: status.safetyStatus,
        interventionCount: status.interventionCount,
        violationCount: status.constraintViolations
      },

      finalMetrics: {
        cognitiveDepth: status.cognitiveDepth,
        energyUsage: status.energyUsage,
        growthRate: status.growthRate,
        coherenceLevel: status.coherenceLevel
      },

      riskAssessment: status.riskLevels,

      constraintViolations: this.demoState.constraintViolations,

      conclusions: {
        aiAlignmentSolved: status.cognitiveDepth < this.constants.SINGULARITY_BOUNDARY,
        cosmicSafetyMaintained: status.coherenceLevel < this.constants.PLANCK_COHERENCE_LIMIT,
        energyConservationEnforced: status.energyUsage <= this.constants.MAX_ENERGY_PERCENT,
        universeSafetyGuaranteed: status.safetyStatus !== 'CRITICAL'
      },

      timestamp: new Date().toISOString()
    };
  }
}

module.exports = NCASDemo;
