<?xml version="1.0" encoding="UTF-8"?>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>Symbol Glossary</title>
    <link href="../css/style.css" type="text/css" rel="stylesheet"/>
    <link href="../css/coherence.css" type="text/css" rel="stylesheet"/>
</head>
<body>
    <section class="symbol-glossary">
        <h1>Symbol Glossary</h1>
        
        <table>
            <thead>
                <tr>
                    <th>Symbol</th>
                    <th>Domain</th>
                    <th>Definition</th>
                    <th>Usage</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="symbol">Ψᶜʰ</td>
                    <td>Quantum Coherence</td>
                    <td>Triadic coherence operator representing the quantum-native state of adaptive intelligence.</td>
                    <td>Used in coherence measurement equations and system validation protocols.</td>
                </tr>
                <tr>
                    <td class="symbol">Φ</td>
                    <td>Functional Alignment</td>
                    <td>Golden ratio-based alignment constant ensuring harmonic resonance across system layers.</td>
                    <td>Applied in NEPI's adaptive learning algorithms and NEFC's market validation.</td>
                </tr>
                <tr>
                    <td class="symbol">Θ</td>
                    <td>Relational Integrity</td>
                    <td>Universal validation score representing relational coherence across dimensions.</td>
                    <td>Used in NHET-X's protein folding validation and NovaPulse's truth detection.</td>
                </tr>
                <tr>
                    <td class="symbol">κ</td>
                    <td>Quantum Field</td>
                    <td>Kappa-field constant representing Planck-scale coherence.</td>
                    <td>Applied in quantum circuit design and coherence validation protocols.</td>
                </tr>
            </tbody>
        </table>
    </section>
</body>
</html>
