/**
 * Cache Module
 * 
 * This module provides caching functionality for the Privacy Management API.
 * It uses an in-memory cache with optional Redis support for distributed environments.
 */

const NodeCache = require('node-cache');
const Redis = require('ioredis');
const logger = require('../logging');

// Default cache options
const DEFAULT_TTL = 60; // 60 seconds
const DEFAULT_CHECK_PERIOD = 120; // 120 seconds

// Cache instances
let memoryCache;
let redisCache;

/**
 * Initialize the cache
 * @param {Object} options - Cache options
 * @param {boolean} options.useRedis - Whether to use Redis for caching
 * @param {string} options.redisUrl - Redis connection URL
 * @param {number} options.defaultTtl - Default TTL in seconds
 * @param {number} options.checkPeriod - Check period in seconds
 */
function initCache(options = {}) {
  const {
    useRedis = process.env.USE_REDIS === 'true',
    redisUrl = process.env.REDIS_URL,
    defaultTtl = parseInt(process.env.CACHE_DEFAULT_TTL) || DEFAULT_TTL,
    checkPeriod = parseInt(process.env.CACHE_CHECK_PERIOD) || DEFAULT_CHECK_PERIOD
  } = options;
  
  // Initialize memory cache
  memoryCache = new NodeCache({
    stdTTL: defaultTtl,
    checkperiod: checkPeriod,
    useClones: false
  });
  
  logger.info('Memory cache initialized', { defaultTtl, checkPeriod });
  
  // Initialize Redis cache if enabled
  if (useRedis && redisUrl) {
    try {
      redisCache = new Redis(redisUrl);
      
      redisCache.on('connect', () => {
        logger.info('Redis cache connected', { redisUrl });
      });
      
      redisCache.on('error', (error) => {
        logger.error('Redis cache error', { error: error.message });
      });
    } catch (error) {
      logger.error('Failed to initialize Redis cache', { error: error.message });
    }
  }
}

/**
 * Get a value from the cache
 * @param {string} key - Cache key
 * @returns {Promise<*>} - Cached value or null if not found
 */
async function get(key) {
  try {
    // Try to get from memory cache first
    const memoryValue = memoryCache.get(key);
    if (memoryValue !== undefined) {
      logger.debug('Cache hit (memory)', { key });
      return memoryValue;
    }
    
    // Try to get from Redis cache if available
    if (redisCache) {
      const redisValue = await redisCache.get(key);
      if (redisValue) {
        try {
          const parsedValue = JSON.parse(redisValue);
          // Store in memory cache for faster access next time
          memoryCache.set(key, parsedValue);
          logger.debug('Cache hit (redis)', { key });
          return parsedValue;
        } catch (error) {
          logger.error('Failed to parse Redis cache value', { key, error: error.message });
          return null;
        }
      }
    }
    
    logger.debug('Cache miss', { key });
    return null;
  } catch (error) {
    logger.error('Cache get error', { key, error: error.message });
    return null;
  }
}

/**
 * Set a value in the cache
 * @param {string} key - Cache key
 * @param {*} value - Value to cache
 * @param {number} ttl - TTL in seconds (optional)
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
async function set(key, value, ttl) {
  try {
    // Set in memory cache
    memoryCache.set(key, value, ttl);
    
    // Set in Redis cache if available
    if (redisCache) {
      await redisCache.set(
        key,
        JSON.stringify(value),
        'EX',
        ttl || DEFAULT_TTL
      );
    }
    
    logger.debug('Cache set', { key, ttl });
    return true;
  } catch (error) {
    logger.error('Cache set error', { key, error: error.message });
    return false;
  }
}

/**
 * Delete a value from the cache
 * @param {string} key - Cache key
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
async function del(key) {
  try {
    // Delete from memory cache
    memoryCache.del(key);
    
    // Delete from Redis cache if available
    if (redisCache) {
      await redisCache.del(key);
    }
    
    logger.debug('Cache delete', { key });
    return true;
  } catch (error) {
    logger.error('Cache delete error', { key, error: error.message });
    return false;
  }
}

/**
 * Clear the entire cache
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
async function clear() {
  try {
    // Clear memory cache
    memoryCache.flushAll();
    
    // Clear Redis cache if available
    if (redisCache) {
      await redisCache.flushall();
    }
    
    logger.info('Cache cleared');
    return true;
  } catch (error) {
    logger.error('Cache clear error', { error: error.message });
    return false;
  }
}

/**
 * Get cache statistics
 * @returns {Promise<Object>} - Cache statistics
 */
async function getStats() {
  try {
    const memoryStats = memoryCache.getStats();
    
    let redisStats = null;
    if (redisCache) {
      const info = await redisCache.info();
      redisStats = {
        info
      };
    }
    
    return {
      memory: memoryStats,
      redis: redisStats
    };
  } catch (error) {
    logger.error('Cache stats error', { error: error.message });
    return null;
  }
}

/**
 * Cache middleware for Express
 * @param {number} ttl - TTL in seconds (optional)
 * @returns {Function} - Express middleware
 */
function cacheMiddleware(ttl) {
  return async (req, res, next) => {
    // Only cache GET requests
    if (req.method !== 'GET') {
      return next();
    }
    
    // Generate cache key from request URL
    const key = `cache:${req.originalUrl}`;
    
    try {
      // Try to get from cache
      const cachedData = await get(key);
      if (cachedData) {
        // Return cached response
        return res.json(cachedData);
      }
      
      // Store original res.json method
      const originalJson = res.json;
      
      // Override res.json method to cache the response
      res.json = function(data) {
        // Cache the response
        set(key, data, ttl);
        
        // Call the original method
        return originalJson.call(this, data);
      };
      
      next();
    } catch (error) {
      logger.error('Cache middleware error', { key, error: error.message });
      next();
    }
  };
}

// Initialize cache with default options
initCache();

module.exports = {
  initCache,
  get,
  set,
  del,
  clear,
  getStats,
  cacheMiddleware
};
