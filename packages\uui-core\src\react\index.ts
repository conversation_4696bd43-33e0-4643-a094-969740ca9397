/**
 * NovaFuse Universal UI Connector (UUIC) React Components
 * 
 * This module exports all the React components for NovaVision.
 */

// Export core components
export { default as UUICProvider, useUUICContext } from './UUICProvider';
export { default as useUUIC, useUUICConfig, useUUICData } from './useUUIC';
export { 
  default as U<PERSON><PERSON>ender<PERSON>,
  UUICSchemaRenderer,
  FormRenderer,
  DashboardRenderer,
  ReportRenderer
} from './UUICRenderer';
export { default as UUICBridge } from './UUICBridge';
export { default as UUICComponentRegistry } from './UUICComponentRegistry';
export { default as RegulatoryContextProvider, useRegulatoryContext } from './RegulatoryContextProvider';

// Export configuration
export { uuicConfig } from './uuicConfig';

// Export a convenience function for rendering UI schemas
export const renderUISchema = (schema: any, data: any, options: any) => {
  return {
    type: 'UUICBridge',
    props: {
      schema,
      data,
      ...options
    }
  };
};
