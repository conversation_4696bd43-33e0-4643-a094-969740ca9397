/**
 * Integration Layer Example
 * 
 * This example demonstrates the usage of the Comphyon Integration Layer.
 */

const { ComphyonIntegrationLayer } = require('../comphyon-core');

// Create mock components
const mockCSDE = {
  name: 'CSDE',
  domain: 'cyber',
  processData: (data) => {
    console.log(`[CSDE] Processing data: ${JSON.stringify(data)}`);
    return { success: true, domain: 'cyber', result: data };
  }
};

const mockCSFE = {
  name: 'CSFE',
  domain: 'financial',
  processData: (data) => {
    console.log(`[CSFE] Processing data: ${JSON.stringify(data)}`);
    return { success: true, domain: 'financial', result: data };
  }
};

const mockCSME = {
  name: 'CSME',
  domain: 'biological',
  processData: (data) => {
    console.log(`[CSME] Processing data: ${JSON.stringify(data)}`);
    return { success: true, domain: 'biological', result: data };
  }
};

const mockBridge = {
  name: 'Bridge',
  domain: 'universal',
  translateData: (sourceDomain, targetDomain, data) => {
    console.log(`[Bridge] Translating data from ${sourceDomain} to ${targetDomain}: ${JSON.stringify(data)}`);
    return { success: true, sourceDomain, targetDomain, result: data };
  }
};

// Create integration layer
const integrationLayer = new ComphyonIntegrationLayer({
  enableLogging: true,
  enableMetrics: true
});

// Run example
async function runExample() {
  try {
    console.log('Starting Integration Layer example...');
    
    // Register components
    console.log('\nRegistering components...');
    integrationLayer.registerComponent('csde', mockCSDE, {
      type: 'engine',
      domain: 'cyber',
      version: '1.0.0',
      interfaces: ['processData']
    });
    console.log('CSDE registered');
    
    integrationLayer.registerComponent('csfe', mockCSFE, {
      type: 'engine',
      domain: 'financial',
      version: '1.0.0',
      interfaces: ['processData']
    });
    console.log('CSFE registered');
    
    integrationLayer.registerComponent('csme', mockCSME, {
      type: 'engine',
      domain: 'biological',
      version: '1.0.0',
      interfaces: ['processData']
    });
    console.log('CSME registered');
    
    integrationLayer.registerComponent('bridge', mockBridge, {
      type: 'integration',
      domain: 'universal',
      version: '1.0.0',
      interfaces: ['translateData'],
      dependencies: ['csde', 'csfe', 'csme']
    });
    console.log('Bridge registered');
    
    // Create connections
    console.log('\nCreating connections...');
    const csdeToBridgeConnection = integrationLayer.createConnection('csde', 'bridge', {
      type: 'bidirectional',
      protocol: 'method',
      metadata: {
        description: 'CSDE to Bridge connection'
      }
    });
    console.log(`CSDE to Bridge connection created: ${csdeToBridgeConnection}`);
    
    const csfeToBridgeConnection = integrationLayer.createConnection('csfe', 'bridge', {
      type: 'bidirectional',
      protocol: 'method',
      metadata: {
        description: 'CSFE to Bridge connection'
      }
    });
    console.log(`CSFE to Bridge connection created: ${csfeToBridgeConnection}`);
    
    const csmeToBridgeConnection = integrationLayer.createConnection('csme', 'bridge', {
      type: 'bidirectional',
      protocol: 'method',
      metadata: {
        description: 'CSME to Bridge connection'
      }
    });
    console.log(`CSME to Bridge connection created: ${csmeToBridgeConnection}`);
    
    // Create data flows
    console.log('\nCreating data flows...');
    const csdeDataFlow = integrationLayer.createDataFlow('csde', 'bridge', {
      dataType: 'cyber',
      direction: 'forward',
      priority: 'high',
      transformations: [
        (data) => {
          console.log('[Transformation] Applying cyber transformation');
          return { ...data, transformed: true, domain: 'cyber' };
        }
      ],
      filters: [
        (data) => {
          console.log('[Filter] Applying cyber filter');
          return data.value > 0.5; // Only pass data with value > 0.5
        }
      ],
      metadata: {
        description: 'CSDE to Bridge data flow'
      }
    });
    console.log(`CSDE data flow created: ${csdeDataFlow}`);
    
    const csfeDataFlow = integrationLayer.createDataFlow('csfe', 'bridge', {
      dataType: 'financial',
      direction: 'forward',
      priority: 'high',
      transformations: [
        (data) => {
          console.log('[Transformation] Applying financial transformation');
          return { ...data, transformed: true, domain: 'financial' };
        }
      ],
      filters: [
        (data) => {
          console.log('[Filter] Applying financial filter');
          return data.value > 0.6; // Only pass data with value > 0.6
        }
      ],
      metadata: {
        description: 'CSFE to Bridge data flow'
      }
    });
    console.log(`CSFE data flow created: ${csfeDataFlow}`);
    
    const csmeDataFlow = integrationLayer.createDataFlow('csme', 'bridge', {
      dataType: 'biological',
      direction: 'forward',
      priority: 'high',
      transformations: [
        (data) => {
          console.log('[Transformation] Applying biological transformation');
          return { ...data, transformed: true, domain: 'biological' };
        }
      ],
      filters: [
        (data) => {
          console.log('[Filter] Applying biological filter');
          return data.value > 0.4; // Only pass data with value > 0.4
        }
      ],
      metadata: {
        description: 'CSME to Bridge data flow'
      }
    });
    console.log(`CSME data flow created: ${csmeDataFlow}`);
    
    // Process data through flows
    console.log('\nProcessing data through flows...');
    
    // Process cyber data
    console.log('\nProcessing cyber data...');
    const cyberData = { type: 'policy_entropy', value: 0.7 };
    const processedCyberData = integrationLayer.processDataFlow(csdeDataFlow, cyberData);
    console.log(`Processed cyber data: ${JSON.stringify(processedCyberData)}`);
    
    // Process financial data
    console.log('\nProcessing financial data...');
    const financialData = { type: 'transaction_entropy', value: 0.8 };
    const processedFinancialData = integrationLayer.processDataFlow(csfeDataFlow, financialData);
    console.log(`Processed financial data: ${JSON.stringify(processedFinancialData)}`);
    
    // Process biological data
    console.log('\nProcessing biological data...');
    const biologicalData = { type: 'inflammation_level', value: 0.6 };
    const processedBiologicalData = integrationLayer.processDataFlow(csmeDataFlow, biologicalData);
    console.log(`Processed biological data: ${JSON.stringify(processedBiologicalData)}`);
    
    // Process filtered out data
    console.log('\nProcessing filtered out data...');
    const filteredCyberData = { type: 'policy_entropy', value: 0.3 }; // Will be filtered out
    const processedFilteredCyberData = integrationLayer.processDataFlow(csdeDataFlow, filteredCyberData);
    console.log(`Processed filtered cyber data: ${JSON.stringify(processedFilteredCyberData)}`);
    
    // Apply tensor operations
    console.log('\nApplying tensor operations...');
    
    // Tensor product
    const tensorA = [0.5, 0.6, 0.7];
    const tensorB = [0.8, 0.9];
    const tensorProduct = integrationLayer.applyTensorOperation(tensorA, tensorB, 'product');
    console.log(`Tensor product: ${JSON.stringify(tensorProduct)}`);
    
    // Tensor sum
    const tensorSum = integrationLayer.applyTensorOperation(tensorA, tensorB, 'sum');
    console.log(`Tensor sum: ${JSON.stringify(tensorSum)}`);
    
    // Tensor fusion
    const tensorFusion = integrationLayer.applyTensorOperation(tensorA, tensorB, 'fusion');
    console.log(`Tensor fusion: ${JSON.stringify(tensorFusion)}`);
    
    // Apply UUFT formula
    console.log('\nApplying UUFT formula...');
    const tensorC = [0.4, 0.5, 0.6];
    const uuftResult = integrationLayer.applyUUFTFormula(tensorA, tensorB, tensorC);
    console.log(`UUFT formula result: ${JSON.stringify(uuftResult)}`);
    
    // Get metrics
    console.log('\nGetting metrics...');
    const metrics = integrationLayer.getMetrics();
    console.log('Integration Layer metrics:');
    console.log(`- Processing Time: ${metrics.processingTimeMs.toFixed(2)} ms`);
    console.log(`- Components Registered: ${metrics.componentsRegistered}`);
    console.log(`- Connections Established: ${metrics.connectionsEstablished}`);
    console.log(`- Data Flows Processed: ${metrics.dataFlowsProcessed}`);
    console.log(`- Tensor Operations: ${metrics.tensorOperations}`);
    console.log(`- Fusion Operations: ${metrics.fusionOperations}`);
    
    console.log('\nIntegration Layer example completed successfully!');
  } catch (error) {
    console.error('Error running Integration Layer example:', error);
  }
}

// Run the example
runExample();
