# Enhanced Analytics

NovaConnect provides comprehensive analytics capabilities to gain insights into system usage, performance, and compliance. This document describes the enhanced analytics features and how to use them.

## Overview

The enhanced analytics system provides the following features:

- **User Analytics**: Analyze user activity and behavior
- **Connector Analytics**: Analyze connector usage and performance
- **Compliance Analytics**: Analyze compliance status and trends
- **Predictive Analytics**: Predict future trends and anomalies
- **Real-Time Analytics**: Monitor system activity in real-time
- **Custom Analytics**: Create custom analytics queries

## API Endpoints

### User Analytics

Get analytics data about user activity.

```
GET /api/enhanced-analytics/user
```

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| startDate | ISO Date | Start date for the analytics period |
| endDate | ISO Date | End date for the analytics period |
| userId | String | Filter by user ID |
| teamId | String | Filter by team ID |
| groupBy | String | Group by time period (hour, day, week, month) |
| metrics | Array | Metrics to include (logins, api_calls, active_time) |

#### Response

```json
{
  "metrics": {
    "logins": {
      "2023-05-01": 10,
      "2023-05-02": 15,
      "2023-05-03": 12
    },
    "api_calls": {
      "2023-05-01": 150,
      "2023-05-02": 200,
      "2023-05-03": 180
    },
    "active_time": {
      "2023-05-01": 120,
      "2023-05-02": 180,
      "2023-05-03": 150
    }
  },
  "timePoints": ["2023-05-01", "2023-05-02", "2023-05-03"],
  "summary": {
    "logins": {
      "total": 37,
      "average": 12.33,
      "min": 10,
      "max": 15
    },
    "api_calls": {
      "total": 530,
      "average": 176.67,
      "min": 150,
      "max": 200
    },
    "active_time": {
      "total": 450,
      "average": 150,
      "min": 120,
      "max": 180
    }
  }
}
```

### Connector Analytics

Get analytics data about connector usage and performance.

```
GET /api/enhanced-analytics/connector
```

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| startDate | ISO Date | Start date for the analytics period |
| endDate | ISO Date | End date for the analytics period |
| connectorId | String | Filter by connector ID |
| connectorType | String | Filter by connector type |
| groupBy | String | Group by time period (hour, day, week, month) |
| metrics | Array | Metrics to include (executions, success_rate, average_duration, error_rate) |

#### Response

```json
{
  "metrics": {
    "executions": {
      "2023-05-01": 50,
      "2023-05-02": 60,
      "2023-05-03": 55
    },
    "success_rate": {
      "2023-05-01": 0.92,
      "2023-05-02": 0.95,
      "2023-05-03": 0.94
    },
    "average_duration": {
      "2023-05-01": 250,
      "2023-05-02": 230,
      "2023-05-03": 240
    },
    "error_rate": {
      "2023-05-01": 0.08,
      "2023-05-02": 0.05,
      "2023-05-03": 0.06
    }
  },
  "timePoints": ["2023-05-01", "2023-05-02", "2023-05-03"],
  "summary": {
    "executions": {
      "total": 165,
      "average": 55,
      "min": 50,
      "max": 60
    },
    "success_rate": {
      "total": null,
      "average": 0.94,
      "min": 0.92,
      "max": 0.95
    },
    "average_duration": {
      "total": null,
      "average": 240,
      "min": 230,
      "max": 250
    },
    "error_rate": {
      "total": null,
      "average": 0.06,
      "min": 0.05,
      "max": 0.08
    }
  }
}
```

### Compliance Analytics

Get analytics data about compliance status and trends.

```
GET /api/enhanced-analytics/compliance
```

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| startDate | ISO Date | Start date for the analytics period |
| endDate | ISO Date | End date for the analytics period |
| framework | String | Filter by compliance framework |
| controlId | String | Filter by control ID |
| groupBy | String | Group by time period (day, week, month, quarter) |
| metrics | Array | Metrics to include (compliance_score, findings, remediation_rate) |

#### Response

```json
{
  "metrics": {
    "compliance_score": {
      "2023-05-01": 0.85,
      "2023-05-02": 0.87,
      "2023-05-03": 0.90
    },
    "findings": {
      "2023-05-01": 15,
      "2023-05-02": 12,
      "2023-05-03": 10
    },
    "remediation_rate": {
      "2023-05-01": 0.60,
      "2023-05-02": 0.70,
      "2023-05-03": 0.80
    }
  },
  "timePoints": ["2023-05-01", "2023-05-02", "2023-05-03"],
  "summary": {
    "compliance_score": {
      "total": null,
      "average": 0.87,
      "min": 0.85,
      "max": 0.90
    },
    "findings": {
      "total": 37,
      "average": 12.33,
      "min": 10,
      "max": 15
    },
    "remediation_rate": {
      "total": null,
      "average": 0.70,
      "min": 0.60,
      "max": 0.80
    }
  }
}
```

### Predictive Analytics

Get predictive analytics for a specific metric.

```
GET /api/enhanced-analytics/predictive
```

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| metric | String | Metric to predict |
| horizon | Number | Number of time periods to predict |
| confidenceInterval | Number | Confidence interval (0-1) |
| modelType | String | Prediction model type (auto, arima, exponential_smoothing, prophet, neural_network) |

#### Response

```json
{
  "metric": "api_calls",
  "horizon": 7,
  "confidenceInterval": 0.95,
  "modelType": "auto",
  "predictions": [
    {
      "date": "2023-05-04",
      "value": 190,
      "lowerBound": 170,
      "upperBound": 210
    },
    {
      "date": "2023-05-05",
      "value": 195,
      "lowerBound": 175,
      "upperBound": 215
    },
    {
      "date": "2023-05-06",
      "value": 200,
      "lowerBound": 180,
      "upperBound": 220
    },
    {
      "date": "2023-05-07",
      "value": 205,
      "lowerBound": 185,
      "upperBound": 225
    },
    {
      "date": "2023-05-08",
      "value": 210,
      "lowerBound": 190,
      "upperBound": 230
    },
    {
      "date": "2023-05-09",
      "value": 215,
      "lowerBound": 195,
      "upperBound": 235
    },
    {
      "date": "2023-05-10",
      "value": 220,
      "lowerBound": 200,
      "upperBound": 240
    }
  ],
  "model": {
    "type": "arima",
    "parameters": {
      "p": 1,
      "d": 1,
      "q": 1
    },
    "accuracy": {
      "mape": 0.05,
      "rmse": 10.5
    }
  }
}
```

### Real-Time Analytics

Get real-time analytics data.

```
GET /api/enhanced-analytics/real-time
```

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| metrics | Array | Metrics to include (active_users, api_calls_per_minute, error_rate, response_time) |

#### Response

```json
{
  "timestamp": "2023-05-01T12:34:56.789Z",
  "metrics": {
    "active_users": 25,
    "api_calls_per_minute": 150,
    "error_rate": 0.02,
    "response_time": 120
  },
  "history": [
    {
      "timestamp": "2023-05-01T12:33:56.789Z",
      "active_users": 23,
      "api_calls_per_minute": 145,
      "error_rate": 0.03,
      "response_time": 125
    },
    {
      "timestamp": "2023-05-01T12:32:56.789Z",
      "active_users": 22,
      "api_calls_per_minute": 140,
      "error_rate": 0.02,
      "response_time": 130
    }
  ]
}
```

### Custom Analytics

Get custom analytics data.

```
POST /api/enhanced-analytics/custom
```

#### Request Body

```json
{
  "metrics": ["api_calls", "response_time", "error_rate"],
  "dimensions": ["user_id", "endpoint", "method"],
  "filters": {
    "endpoint": "/api/connectors",
    "method": "GET"
  },
  "startDate": "2023-05-01T00:00:00.000Z",
  "endDate": "2023-05-31T23:59:59.999Z",
  "groupBy": "day",
  "limit": 1000,
  "sort": {
    "api_calls": "desc"
  }
}
```

#### Response

```json
{
  "results": [
    {
      "user_id": "user-123",
      "endpoint": "/api/connectors",
      "method": "GET",
      "api_calls": 150,
      "response_time": 120,
      "error_rate": 0.02,
      "date": "2023-05-01"
    },
    {
      "user_id": "user-456",
      "endpoint": "/api/connectors",
      "method": "GET",
      "api_calls": 120,
      "response_time": 130,
      "error_rate": 0.03,
      "date": "2023-05-01"
    }
  ],
  "summary": {
    "api_calls": {
      "total": 270,
      "average": 135,
      "min": 120,
      "max": 150
    },
    "response_time": {
      "total": null,
      "average": 125,
      "min": 120,
      "max": 130
    },
    "error_rate": {
      "total": null,
      "average": 0.025,
      "min": 0.02,
      "max": 0.03
    }
  },
  "pagination": {
    "total": 2,
    "limit": 1000,
    "offset": 0
  }
}
```

### Analytics Dashboard

Get a pre-configured analytics dashboard.

```
GET /api/enhanced-analytics/dashboard
```

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| startDate | ISO Date | Start date for the analytics period |
| endDate | ISO Date | End date for the analytics period |
| refresh | Boolean | Whether to refresh the dashboard cache |

#### Response

```json
{
  "timestamp": "2023-05-01T12:34:56.789Z",
  "summary": {
    "active_users": 100,
    "api_calls": 10000,
    "connectors": 50,
    "compliance_score": 0.85
  },
  "charts": {
    "user_activity": {
      "type": "line",
      "data": {
        "labels": ["2023-05-01", "2023-05-02", "2023-05-03"],
        "datasets": [
          {
            "label": "Active Users",
            "data": [95, 100, 105]
          },
          {
            "label": "API Calls",
            "data": [9500, 10000, 10500]
          }
        ]
      }
    },
    "connector_performance": {
      "type": "bar",
      "data": {
        "labels": ["AWS", "Azure", "GCP", "GitHub", "Jira"],
        "datasets": [
          {
            "label": "Success Rate",
            "data": [0.95, 0.92, 0.94, 0.98, 0.96]
          },
          {
            "label": "Average Duration (ms)",
            "data": [250, 300, 280, 150, 200]
          }
        ]
      }
    },
    "compliance_trends": {
      "type": "line",
      "data": {
        "labels": ["2023-05-01", "2023-05-02", "2023-05-03"],
        "datasets": [
          {
            "label": "Compliance Score",
            "data": [0.82, 0.85, 0.87]
          },
          {
            "label": "Findings",
            "data": [18, 15, 13]
          }
        ]
      }
    }
  }
}
```

### Export Analytics

Export analytics data in various formats.

```
GET /api/enhanced-analytics/export
```

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| type | String | Type of analytics to export (user, connector, compliance, custom) |
| format | String | Export format (csv, json, excel) |
| startDate | ISO Date | Start date for the analytics period |
| endDate | ISO Date | End date for the analytics period |
| ... | ... | Other parameters specific to the analytics type |

#### Response

The response is a file download with the appropriate content type.

## Security Considerations

- Analytics data may contain sensitive information and should be protected accordingly
- Access to analytics should be restricted to authorized users
- Analytics data should be anonymized where possible
- Analytics data should be retained for a period consistent with compliance requirements
- Analytics data should be backed up regularly
