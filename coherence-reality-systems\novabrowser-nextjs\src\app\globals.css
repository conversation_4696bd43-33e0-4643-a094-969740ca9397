@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 10, 10, 15;
  --background-end-rgb: 22, 33, 62;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', system-ui, sans-serif;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      135deg,
      rgb(var(--background-start-rgb)) 0%,
      rgb(26, 26, 46) 50%,
      rgb(var(--background-end-rgb)) 100%
    );
  min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Glass morphism utility */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-hover:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(99, 102, 241, 0.5);
}

/* Animations */
@keyframes pulse-glow {
  0%, 100% { 
    opacity: 1; 
    box-shadow: 0 0 8px currentColor;
  }
  50% { 
    opacity: 0.6; 
    box-shadow: 0 0 12px currentColor;
  }
}

.pulse-glow {
  animation: pulse-glow 2s infinite;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-up {
  animation: slideInUp 0.6s ease-out forwards;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #6366f1, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
