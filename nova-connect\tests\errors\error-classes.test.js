/**
 * NovaFuse Universal API Connector - Error Classes Tests
 * 
 * This module tests the error classes for the UAC.
 */

const {
  UAConnectorError,
  AuthenticationError,
  MissingCredentialsError,
  ConnectionError,
  TimeoutError,
  ValidationError,
  MissingRequiredFieldError,
  ApiError,
  RateLimitExceededError,
  ResourceNotFoundError,
  TransformationError,
  ConnectorError,
  ConnectorNotFoundError
} = require('../../src/errors');

describe('Error Classes', () => {
  describe('UAConnectorError', () => {
    it('should create a base error with default values', () => {
      const error = new UAConnectorError('Test error');
      
      expect(error).toBeInstanceOf(Error);
      expect(error.name).toBe('UAConnectorError');
      expect(error.message).toBe('Test error');
      expect(error.code).toBe('UAC_ERROR');
      expect(error.severity).toBe('error');
      expect(error.context).toEqual({});
      expect(error.cause).toBeUndefined();
      expect(error.timestamp).toBeDefined();
      expect(error.errorId).toBeDefined();
    });
    
    it('should create a base error with custom values', () => {
      const cause = new Error('Original error');
      const error = new UAConnectorError('Test error', {
        code: 'CUSTOM_CODE',
        severity: 'warning',
        context: { foo: 'bar' },
        cause
      });
      
      expect(error.code).toBe('CUSTOM_CODE');
      expect(error.severity).toBe('warning');
      expect(error.context).toEqual({ foo: 'bar' });
      expect(error.cause).toBe(cause);
    });
    
    it('should convert to JSON', () => {
      const error = new UAConnectorError('Test error', {
        code: 'CUSTOM_CODE',
        context: { foo: 'bar' }
      });
      
      const json = error.toJSON();
      
      expect(json.errorId).toBe(error.errorId);
      expect(json.name).toBe('UAConnectorError');
      expect(json.message).toBe('Test error');
      expect(json.code).toBe('CUSTOM_CODE');
      expect(json.severity).toBe('error');
      expect(json.context).toEqual({ foo: 'bar' });
      expect(json.stack).toBeUndefined();
    });
    
    it('should convert to JSON with stack trace', () => {
      const error = new UAConnectorError('Test error');
      
      const json = error.toJSON(true);
      
      expect(json.stack).toBeDefined();
    });
    
    it('should provide user and developer messages', () => {
      const error = new UAConnectorError('Test error', {
        code: 'CUSTOM_CODE'
      });
      
      expect(error.getUserMessage()).toBe('Test error');
      expect(error.getDeveloperMessage()).toBe('[CUSTOM_CODE] Test error');
    });
  });
  
  describe('AuthenticationError', () => {
    it('should create an authentication error', () => {
      const error = new AuthenticationError('Auth failed');
      
      expect(error).toBeInstanceOf(UAConnectorError);
      expect(error.name).toBe('AuthenticationError');
      expect(error.code).toBe('AUTH_ERROR');
      expect(error.getUserMessage()).toBe('Authentication failed. Please check your credentials and try again.');
    });
    
    it('should create a missing credentials error', () => {
      const error = new MissingCredentialsError();
      
      expect(error).toBeInstanceOf(AuthenticationError);
      expect(error.name).toBe('MissingCredentialsError');
      expect(error.code).toBe('AUTH_MISSING_CREDENTIALS');
      expect(error.getUserMessage()).toBe('Authentication failed. Required credentials are missing.');
    });
  });
  
  describe('ConnectionError', () => {
    it('should create a connection error', () => {
      const error = new ConnectionError('Connection failed');
      
      expect(error).toBeInstanceOf(UAConnectorError);
      expect(error.name).toBe('ConnectionError');
      expect(error.code).toBe('CONNECTION_ERROR');
      expect(error.getUserMessage()).toBe('Failed to connect to the service. Please check your network connection and try again.');
    });
    
    it('should create a timeout error', () => {
      const error = new TimeoutError();
      
      expect(error).toBeInstanceOf(ConnectionError);
      expect(error.name).toBe('TimeoutError');
      expect(error.code).toBe('CONNECTION_TIMEOUT');
      expect(error.getUserMessage()).toBe('The request timed out. Please try again later or contact support if the issue persists.');
    });
  });
  
  describe('ValidationError', () => {
    it('should create a validation error', () => {
      const error = new ValidationError('Validation failed');
      
      expect(error).toBeInstanceOf(UAConnectorError);
      expect(error.name).toBe('ValidationError');
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.getUserMessage()).toBe('The provided data is invalid. Please check your input and try again.');
    });
    
    it('should create a validation error with validation errors', () => {
      const validationErrors = [
        { field: 'name', message: 'Name is required' },
        { field: 'email', message: 'Email is invalid' }
      ];
      
      const error = new ValidationError('Validation failed', {
        validationErrors
      });
      
      expect(error.validationErrors).toEqual(validationErrors);
      expect(error.getUserMessage()).toBe('Validation failed: Name is required; Email is invalid');
    });
    
    it('should create a missing required field error', () => {
      const error = new MissingRequiredFieldError('name');
      
      expect(error).toBeInstanceOf(ValidationError);
      expect(error.name).toBe('MissingRequiredFieldError');
      expect(error.code).toBe('VALIDATION_MISSING_REQUIRED_FIELD');
      expect(error.getUserMessage()).toBe('Validation failed: Missing required field: name');
    });
    
    it('should create a missing required field error with multiple fields', () => {
      const error = new MissingRequiredFieldError(['name', 'email']);
      
      expect(error.validationErrors.length).toBe(2);
      expect(error.getUserMessage()).toBe('Validation failed: Missing required field: name; Missing required field: email');
    });
  });
  
  describe('ApiError', () => {
    it('should create an API error', () => {
      const error = new ApiError('API error');
      
      expect(error).toBeInstanceOf(UAConnectorError);
      expect(error.name).toBe('ApiError');
      expect(error.code).toBe('API_ERROR');
      expect(error.getUserMessage()).toBe('An error occurred while communicating with the external service. Please try again later.');
    });
    
    it('should create an API error with status code and response', () => {
      const error = new ApiError('API error', {
        statusCode: 400,
        response: { message: 'Bad request' }
      });
      
      expect(error.statusCode).toBe(400);
      expect(error.response).toEqual({ message: 'Bad request' });
      
      const json = error.toJSON();
      expect(json.statusCode).toBe(400);
      expect(json.response).toEqual({ message: 'Bad request' });
    });
    
    it('should create a rate limit exceeded error', () => {
      const error = new RateLimitExceededError('Rate limit exceeded', {
        retryAfter: 60
      });
      
      expect(error).toBeInstanceOf(ApiError);
      expect(error.name).toBe('RateLimitExceededError');
      expect(error.code).toBe('API_RATE_LIMIT_EXCEEDED');
      expect(error.retryAfter).toBe(60);
      expect(error.getUserMessage()).toBe('Rate limit exceeded. Please try again after 60 seconds.');
    });
    
    it('should create a resource not found error', () => {
      const error = new ResourceNotFoundError('User', '123');
      
      expect(error).toBeInstanceOf(ApiError);
      expect(error.name).toBe('ResourceNotFoundError');
      expect(error.code).toBe('API_RESOURCE_NOT_FOUND');
      expect(error.resourceType).toBe('User');
      expect(error.resourceId).toBe('123');
      expect(error.getUserMessage()).toBe('The requested user could not be found.');
    });
  });
  
  describe('TransformationError', () => {
    it('should create a transformation error', () => {
      const error = new TransformationError('Transformation failed');
      
      expect(error).toBeInstanceOf(UAConnectorError);
      expect(error.name).toBe('TransformationError');
      expect(error.code).toBe('TRANSFORMATION_ERROR');
      expect(error.getUserMessage()).toBe('An error occurred while processing the data. Please contact support if the issue persists.');
    });
  });
  
  describe('ConnectorError', () => {
    it('should create a connector error', () => {
      const error = new ConnectorError('Connector error', {
        connectorId: 'test-connector'
      });
      
      expect(error).toBeInstanceOf(UAConnectorError);
      expect(error.name).toBe('ConnectorError');
      expect(error.code).toBe('CONNECTOR_ERROR');
      expect(error.connectorId).toBe('test-connector');
      expect(error.getUserMessage()).toBe('An error occurred with the connector. Please try again later or contact support if the issue persists.');
    });
    
    it('should create a connector not found error', () => {
      const error = new ConnectorNotFoundError('test-connector');
      
      expect(error).toBeInstanceOf(ConnectorError);
      expect(error.name).toBe('ConnectorNotFoundError');
      expect(error.code).toBe('CONNECTOR_NOT_FOUND');
      expect(error.connectorId).toBe('test-connector');
      expect(error.getUserMessage()).toBe('The connector "test-connector" was not found. Please check the connector ID and try again.');
    });
  });
});
