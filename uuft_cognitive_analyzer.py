#!/usr/bin/env python3
"""
UUFT Cognitive Systems Analyzer

This module analyzes neural networks and language models for 18/82 patterns
and π-related relationships in their weight distributions, activation patterns,
and information flow characteristics.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
import logging
import json
import torch
import torch.nn as nn
from collections import defaultdict
import math

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uuft_cognitive.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UUFT_Cognitive')

# Constants
PATTERN_1882_RATIO = 18 / 82
PI = np.pi
PI_10_CUBED = PI * 10**3
RESULTS_DIR = "uuft_results/cognitive"
os.makedirs(RESULTS_DIR, exist_ok=True)

class UUFTCognitiveAnalyzer:
    """Analyzer for detecting and measuring UUFT patterns in neural networks and language models."""

    def __init__(self, pattern_threshold=0.05):
        """
        Initialize the cognitive analyzer.

        Args:
            pattern_threshold: Threshold for considering a match to the 18/82 pattern (0-1)
        """
        self.pattern_threshold = pattern_threshold
        logger.info(f"Initialized cognitive analyzer with pattern threshold {pattern_threshold}")

    def analyze_neural_network(self, model, input_shape=None, sample_input=None, model_name="unnamed_model"):
        """
        Analyze a neural network for 18/82 patterns in weights and activations.

        Args:
            model: PyTorch neural network model
            input_shape: Shape of input tensor for activation analysis (optional)
            sample_input: Sample input tensor for activation analysis (optional)
            model_name: Name of the model for reporting

        Returns:
            Dict with analysis results
        """
        logger.info(f"Analyzing neural network: {model_name}")

        # Analyze model architecture
        architecture_info = self._analyze_architecture(model)

        # Analyze weight distributions
        weight_analysis = self._analyze_weights(model)

        # Analyze activations if input shape or sample input is provided
        activation_analysis = None
        if input_shape is not None or sample_input is not None:
            activation_analysis = self._analyze_activations(model, input_shape, sample_input)

        # Combine results
        result = {
            "model_name": model_name,
            "architecture": architecture_info,
            "weight_analysis": weight_analysis,
            "activation_analysis": activation_analysis,
            "overall_1882_pattern_score": weight_analysis["overall_1882_pattern_score"],
            "overall_pi_relationship_score": weight_analysis["overall_pi_relationship_score"]
        }

        # Save results
        base_name = model_name.replace(" ", "_").lower()
        with open(os.path.join(RESULTS_DIR, f"{base_name}_analysis.json"), 'w', encoding='utf-8') as f:
            # Convert any non-serializable values to strings
            json_result = json.loads(json.dumps(result, default=str))
            json.dump(json_result, f, indent=2)

        logger.info(f"Analysis completed for neural network: {model_name}")

        return result

    def _analyze_architecture(self, model):
        """
        Analyze the architecture of a neural network.

        Args:
            model: PyTorch neural network model

        Returns:
            Dict with architecture information
        """
        # Count layers by type
        layer_counts = defaultdict(int)
        total_params = 0

        for name, module in model.named_modules():
            if len(list(module.children())) == 0:  # Only count leaf modules
                layer_type = module.__class__.__name__
                layer_counts[layer_type] += 1

                # Count parameters
                params = sum(p.numel() for p in module.parameters() if p.requires_grad)
                total_params += params

        # Check for 18/82 pattern in layer distribution
        layer_types = list(layer_counts.keys())
        layer_counts_values = list(layer_counts.values())

        # Calculate 18/82 split for layer types
        layer_type_result = self._calculate_1882_split(layer_counts_values)

        # Calculate parameter distribution across layer types
        param_counts = {}
        for name, module in model.named_modules():
            if len(list(module.children())) == 0:  # Only count leaf modules
                layer_type = module.__class__.__name__
                params = sum(p.numel() for p in module.parameters() if p.requires_grad)
                if layer_type not in param_counts:
                    param_counts[layer_type] = 0
                param_counts[layer_type] += params

        param_counts_values = list(param_counts.values())
        param_distribution_result = self._calculate_1882_split(param_counts_values)

        # Check for π-related patterns in architecture
        pi_relationships = []

        # Check layer count ratios
        for i in range(len(layer_types)):
            for j in range(i+1, len(layer_types)):
                if layer_counts_values[i] > 0 and layer_counts_values[j] > 0:
                    ratio = layer_counts_values[i] / layer_counts_values[j]
                    if abs(ratio - PI) / PI < 0.05:
                        pi_relationships.append({
                            "type": "layer_count_ratio",
                            "layers": [layer_types[i], layer_types[j]],
                            "ratio": float(ratio),
                            "proximity_to_pi": float(abs(ratio - PI) / PI)
                        })

        # Check for π in total parameter count
        if abs(total_params / 1e6 - PI) / PI < 0.1:
            pi_relationships.append({
                "type": "total_params_millions",
                "value": float(total_params / 1e6),
                "proximity_to_pi": float(abs(total_params / 1e6 - PI) / PI)
            })

        # Check for π×10³ in total parameter count
        if abs(total_params / 1e3 - PI_10_CUBED) / PI_10_CUBED < 0.1:
            pi_relationships.append({
                "type": "total_params_thousands",
                "value": float(total_params / 1e3),
                "proximity_to_pi_10_cubed": float(abs(total_params / 1e3 - PI_10_CUBED) / PI_10_CUBED)
            })

        return {
            "layer_counts": dict(layer_counts),
            "total_params": total_params,
            "layer_type_1882_pattern": layer_type_result,
            "param_distribution_1882_pattern": param_distribution_result,
            "pi_relationships": pi_relationships
        }

    def _analyze_weights(self, model):
        """
        Analyze the weight distributions of a neural network.

        Args:
            model: PyTorch neural network model

        Returns:
            Dict with weight analysis results
        """
        # Collect all weights
        all_weights = []
        weight_tensors = {}

        for name, param in model.named_parameters():
            if 'weight' in name:
                weights = param.data.cpu().numpy().flatten()
                all_weights.extend(weights)
                weight_tensors[name] = weights

        # Analyze overall weight distribution
        overall_result = self._calculate_1882_split(all_weights)

        # Analyze individual weight tensors
        tensor_results = {}
        pattern_scores = []

        for name, weights in weight_tensors.items():
            if len(weights) > 10:  # Only analyze tensors with enough weights
                result = self._calculate_1882_split(weights)
                tensor_results[name] = result
                pattern_scores.append(result["is_1882_pattern"])

        # Calculate overall pattern score (percentage of weight tensors with 18/82 pattern)
        overall_pattern_score = sum(pattern_scores) / len(pattern_scores) if pattern_scores else 0

        # Check for π-related patterns in weights
        pi_relationships = self._find_pi_relationships(all_weights)

        # Calculate overall π relationship score
        pi_relationship_score = min(1.0, len(pi_relationships) / 10)  # Cap at 1.0

        return {
            "overall_weight_distribution": overall_result,
            "tensor_results": tensor_results,
            "overall_1882_pattern_score": float(overall_pattern_score),
            "pi_relationships": pi_relationships[:10],  # Limit to top 10
            "pi_relationships_count": len(pi_relationships),
            "overall_pi_relationship_score": float(pi_relationship_score)
        }

    def _analyze_activations(self, model, input_shape=None, sample_input=None):
        """
        Analyze the activation patterns of a neural network.

        Args:
            model: PyTorch neural network model
            input_shape: Shape of input tensor for activation analysis
            sample_input: Sample input tensor for activation analysis

        Returns:
            Dict with activation analysis results
        """
        # Create hooks to capture activations
        activations = {}

        def hook_fn(name):
            def hook(module, input, output):
                activations[name] = output.detach().cpu().numpy()
            return hook

        hooks = []
        for name, module in model.named_modules():
            if len(list(module.children())) == 0:  # Only hook leaf modules
                hooks.append(module.register_forward_hook(hook_fn(name)))

        # Generate input if not provided
        if sample_input is None and input_shape is not None:
            sample_input = torch.randn(input_shape)

        if sample_input is None:
            logger.warning("No input provided for activation analysis")
            for hook in hooks:
                hook.remove()
            return None

        # Forward pass to collect activations
        model.eval()
        with torch.no_grad():
            _ = model(sample_input)

        # Remove hooks
        for hook in hooks:
            hook.remove()

        # Analyze activations
        activation_results = {}
        pattern_scores = []

        for name, activation in activations.items():
            # Flatten activation
            flat_activation = activation.flatten()

            if len(flat_activation) > 10:  # Only analyze activations with enough values
                result = self._calculate_1882_split(flat_activation)
                activation_results[name] = result
                pattern_scores.append(result["is_1882_pattern"])

        # Calculate overall pattern score
        overall_pattern_score = sum(pattern_scores) / len(pattern_scores) if pattern_scores else 0

        # Collect all activations for π analysis
        all_activations = np.concatenate([act.flatten() for act in activations.values()])
        pi_relationships = self._find_pi_relationships(all_activations)

        # Calculate overall π relationship score
        pi_relationship_score = min(1.0, len(pi_relationships) / 10)  # Cap at 1.0

        return {
            "activation_results": activation_results,
            "overall_1882_pattern_score": float(overall_pattern_score),
            "pi_relationships": pi_relationships[:10],  # Limit to top 10
            "pi_relationships_count": len(pi_relationships),
            "overall_pi_relationship_score": float(pi_relationship_score)
        }

    def _calculate_1882_split(self, values):
        """
        Calculate the best 18/82 split for a set of values.

        Args:
            values: Array of values to analyze

        Returns:
            Dict with split information
        """
        # Convert to numpy array if not already
        values = np.array(values)

        # Sort the values
        sorted_values = np.sort(values)
        total_sum = np.sum(sorted_values)

        if len(values) < 10 or total_sum == 0:
            return {
                "total_data_points": len(values),
                "is_1882_pattern": False,
                "proximity_to_1882_percent": 100.0,
                "insufficient_data": True
            }

        # Find the best 18/82 split
        best_split_idx = None
        best_proximity = float('inf')

        for i in range(1, len(sorted_values)):
            lower_sum = np.sum(sorted_values[:i])
            upper_sum = np.sum(sorted_values[i:])

            if upper_sum == 0:
                continue

            lower_ratio = lower_sum / total_sum
            upper_ratio = upper_sum / total_sum

            # Calculate proximity to 18/82 ratio
            proximity_to_1882 = abs((lower_ratio / upper_ratio) - PATTERN_1882_RATIO)

            if proximity_to_1882 < best_proximity:
                best_proximity = proximity_to_1882
                best_split_idx = i

        if best_split_idx is None:
            return {
                "total_data_points": len(values),
                "is_1882_pattern": False,
                "proximity_to_1882_percent": 100.0,
                "error": "Could not find valid split"
            }

        # Calculate the actual ratios
        lower_sum = np.sum(sorted_values[:best_split_idx])
        upper_sum = np.sum(sorted_values[best_split_idx:])

        lower_ratio = lower_sum / total_sum
        upper_ratio = upper_sum / total_sum

        # Calculate proximity to 18/82
        proximity_percent = abs((lower_ratio / upper_ratio) - PATTERN_1882_RATIO) / PATTERN_1882_RATIO * 100
        is_1882_pattern = proximity_percent <= self.pattern_threshold * 100

        return {
            "total_data_points": len(values),
            "split_index": int(best_split_idx),
            "split_percentile": float(best_split_idx / len(values) * 100),
            "lower_sum": float(lower_sum),
            "upper_sum": float(upper_sum),
            "lower_ratio": float(lower_ratio),
            "upper_ratio": float(upper_ratio),
            "proximity_to_1882_percent": float(proximity_percent),
            "is_1882_pattern": bool(is_1882_pattern)
        }

    def _find_pi_relationships(self, values):
        """
        Find π-related relationships in a set of values.

        Args:
            values: Array of values to analyze

        Returns:
            List of π relationships
        """
        pi_relationships = []

        # Sample values to reduce computation
        if len(values) > 10000:
            indices = np.random.choice(len(values), 10000, replace=False)
            sampled_values = np.array([values[i] for i in indices])
        else:
            sampled_values = np.array(values)

        # Check for values close to π
        for i, value in enumerate(sampled_values):
            if abs(value - PI) / PI < 0.05:
                pi_relationships.append({
                    "type": "value_equals_pi",
                    "value": float(value),
                    "proximity_to_pi": float(abs(value - PI) / PI)
                })

        # Check for values close to π×10³
        for i, value in enumerate(sampled_values):
            if abs(value - PI_10_CUBED) / PI_10_CUBED < 0.05:
                pi_relationships.append({
                    "type": "value_equals_pi_10_cubed",
                    "value": float(value),
                    "proximity_to_pi_10_cubed": float(abs(value - PI_10_CUBED) / PI_10_CUBED)
                })

        # Check for ratios close to π
        # Sample pairs to reduce computation
        if len(sampled_values) > 1000:
            # Generate 1000 random pairs of indices
            idx1 = np.random.randint(0, len(sampled_values), 1000)
            idx2 = np.random.randint(0, len(sampled_values), 1000)
            pairs = list(zip(idx1, idx2))
        else:
            pairs = [(i, j) for i in range(len(sampled_values)) for j in range(i+1, len(sampled_values))]

        for i, j in pairs:
            if sampled_values[i] == 0 or sampled_values[j] == 0:
                continue

            ratio = abs(sampled_values[i] / sampled_values[j])
            if abs(ratio - PI) / PI < 0.05:
                pi_relationships.append({
                    "type": "value_ratio_equals_pi",
                    "values": [float(sampled_values[i]), float(sampled_values[j])],
                    "ratio": float(ratio),
                    "proximity_to_pi": float(abs(ratio - PI) / PI)
                })

        return pi_relationships

    def visualize_weight_distribution(self, model, model_name="unnamed_model", save_path=None):
        """
        Visualize the weight distribution of a neural network with 18/82 pattern highlight.

        Args:
            model: PyTorch neural network model
            model_name: Name of the model for the plot title
            save_path: Path to save the visualization
        """
        # Collect all weights
        all_weights = []

        for name, param in model.named_parameters():
            if 'weight' in name:
                weights = param.data.cpu().numpy().flatten()
                all_weights.extend(weights)

        all_weights = np.array(all_weights)

        # Calculate 18/82 split
        result = self._calculate_1882_split(all_weights)

        # Create histogram
        plt.figure(figsize=(12, 8))

        # Plot histogram
        n, bins, patches = plt.hist(all_weights, bins=100, alpha=0.7, color='blue')

        # Highlight 18/82 split if valid
        if "split_index" in result:
            split_value = np.sort(all_weights)[result["split_index"]]
            plt.axvline(x=split_value, color='red', linestyle='--',
                       label=f'18/82 Split (Proximity: {result["proximity_to_1882_percent"]:.2f}%)')

            # Add pattern match indicator
            pattern_text = "18/82 Pattern Detected" if result["is_1882_pattern"] else "No 18/82 Pattern"
            plt.text(0.02, 0.95, pattern_text,
                    transform=plt.gca().transAxes, fontsize=12,
                    bbox=dict(facecolor='white', alpha=0.8))

        plt.title(f"Weight Distribution for {model_name}")
        plt.xlabel("Weight Value")
        plt.ylabel("Frequency")
        plt.legend()
        plt.grid(True, alpha=0.3)

        if save_path:
            plt.savefig(save_path, dpi=300)
            logger.info(f"Visualization saved to {save_path}")

        plt.close()

    def analyze_language_model(self, model, tokenizer, sample_text="The Universal Unified Field Theory combines patterns across domains.", model_name="unnamed_lm"):
        """
        Analyze a language model for 18/82 patterns in token probabilities and attention.

        Args:
            model: Transformer language model
            tokenizer: Tokenizer for the model
            sample_text: Sample text for analysis
            model_name: Name of the model for reporting

        Returns:
            Dict with analysis results
        """
        logger.info(f"Analyzing language model: {model_name}")

        # Analyze model architecture
        architecture_info = self._analyze_architecture(model)

        # Analyze weight distributions
        weight_analysis = self._analyze_weights(model)

        # Analyze token probabilities
        token_analysis = self._analyze_token_probabilities(model, tokenizer, sample_text)

        # Analyze attention patterns if available
        attention_analysis = self._analyze_attention_patterns(model, tokenizer, sample_text)

        # Combine results
        result = {
            "model_name": model_name,
            "architecture": architecture_info,
            "weight_analysis": weight_analysis,
            "token_analysis": token_analysis,
            "attention_analysis": attention_analysis,
            "overall_1882_pattern_score": (
                weight_analysis["overall_1882_pattern_score"] +
                (token_analysis["overall_1882_pattern_score"] if token_analysis else 0) +
                (attention_analysis["overall_1882_pattern_score"] if attention_analysis else 0)
            ) / (1 + (1 if token_analysis else 0) + (1 if attention_analysis else 0)),
            "overall_pi_relationship_score": (
                weight_analysis["overall_pi_relationship_score"] +
                (token_analysis["overall_pi_relationship_score"] if token_analysis else 0) +
                (attention_analysis["overall_pi_relationship_score"] if attention_analysis else 0)
            ) / (1 + (1 if token_analysis else 0) + (1 if attention_analysis else 0))
        }

        # Save results
        base_name = model_name.replace(" ", "_").lower()
        with open(os.path.join(RESULTS_DIR, f"{base_name}_lm_analysis.json"), 'w', encoding='utf-8') as f:
            # Convert any non-serializable values to strings
            json_result = json.loads(json.dumps(result, default=str))
            json.dump(json_result, f, indent=2)

        logger.info(f"Analysis completed for language model: {model_name}")

        return result

    def _analyze_token_probabilities(self, model, tokenizer, sample_text):
        """
        Analyze token probabilities for 18/82 patterns.

        Args:
            model: Transformer language model
            tokenizer: Tokenizer for the model
            sample_text: Sample text for analysis

        Returns:
            Dict with token probability analysis results
        """
        try:
            # Tokenize input
            inputs = tokenizer(sample_text, return_tensors="pt")

            # Get model predictions
            model.eval()
            with torch.no_grad():
                outputs = model(**inputs)

            # Get logits and convert to probabilities
            logits = outputs.logits
            probs = torch.nn.functional.softmax(logits, dim=-1)

            # Analyze token probability distributions
            token_results = {}
            pattern_scores = []

            for i in range(logits.shape[1]):
                # Get probabilities for this position
                position_probs = probs[0, i, :].cpu().numpy()

                # Calculate 18/82 split
                result = self._calculate_1882_split(position_probs)
                token_results[f"position_{i}"] = result
                pattern_scores.append(result["is_1882_pattern"])

            # Calculate overall pattern score
            overall_pattern_score = sum(pattern_scores) / len(pattern_scores) if pattern_scores else 0

            # Check for π-related patterns in probabilities
            all_probs = probs.cpu().numpy().flatten()
            pi_relationships = self._find_pi_relationships(all_probs)

            # Calculate overall π relationship score
            pi_relationship_score = min(1.0, len(pi_relationships) / 10)  # Cap at 1.0

            # Analyze entropy distribution
            entropies = []
            for i in range(logits.shape[1]):
                position_probs = probs[0, i, :].cpu().numpy()
                # Calculate entropy: -sum(p * log(p))
                entropy = -np.sum(position_probs * np.log(position_probs + 1e-10))
                entropies.append(entropy)

            entropy_result = self._calculate_1882_split(entropies)

            return {
                "token_position_results": token_results,
                "entropy_distribution": entropy_result,
                "overall_1882_pattern_score": float(overall_pattern_score),
                "pi_relationships": pi_relationships[:10],  # Limit to top 10
                "pi_relationships_count": len(pi_relationships),
                "overall_pi_relationship_score": float(pi_relationship_score)
            }
        except Exception as e:
            logger.error(f"Error analyzing token probabilities: {str(e)}")
            return None

    def _analyze_attention_patterns(self, model, tokenizer, sample_text):
        """
        Analyze attention patterns for 18/82 patterns.

        Args:
            model: Transformer language model
            tokenizer: Tokenizer for the model
            sample_text: Sample text for analysis

        Returns:
            Dict with attention pattern analysis results
        """
        try:
            # Check if model has attention masks
            if not hasattr(model, "config") or not hasattr(model.config, "is_decoder"):
                logger.warning("Model does not appear to be a transformer with attention")
                return None

            # Tokenize input
            inputs = tokenizer(sample_text, return_tensors="pt")

            # Get model predictions with attention outputs
            model.eval()
            with torch.no_grad():
                outputs = model(**inputs, output_attentions=True)

            # Get attention weights
            attentions = outputs.attentions

            if attentions is None or len(attentions) == 0:
                logger.warning("No attention weights available")
                return None

            # Analyze attention distributions
            layer_results = {}
            head_results = {}
            pattern_scores = []

            # Analyze by layer
            for layer_idx, layer_attention in enumerate(attentions):
                # Average across heads and positions
                layer_avg = layer_attention.mean(dim=(0, 1)).cpu().numpy()
                result = self._calculate_1882_split(layer_avg)
                layer_results[f"layer_{layer_idx}"] = result
                pattern_scores.append(result["is_1882_pattern"])

                # Analyze individual heads
                for head_idx in range(layer_attention.shape[1]):
                    head_avg = layer_attention[0, head_idx, :, :].cpu().numpy().flatten()
                    result = self._calculate_1882_split(head_avg)
                    head_results[f"layer_{layer_idx}_head_{head_idx}"] = result
                    pattern_scores.append(result["is_1882_pattern"])

            # Calculate overall pattern score
            overall_pattern_score = sum(pattern_scores) / len(pattern_scores) if pattern_scores else 0

            # Check for π-related patterns in attention weights
            all_attention = np.concatenate([att.cpu().numpy().flatten() for att in attentions])
            pi_relationships = self._find_pi_relationships(all_attention)

            # Calculate overall π relationship score
            pi_relationship_score = min(1.0, len(pi_relationships) / 10)  # Cap at 1.0

            return {
                "layer_results": layer_results,
                "head_results": head_results,
                "overall_1882_pattern_score": float(overall_pattern_score),
                "pi_relationships": pi_relationships[:10],  # Limit to top 10
                "pi_relationships_count": len(pi_relationships),
                "overall_pi_relationship_score": float(pi_relationship_score)
            }
        except Exception as e:
            logger.error(f"Error analyzing attention patterns: {str(e)}")
            return None

    def visualize_attention_patterns(self, model, tokenizer, sample_text, model_name="unnamed_lm", save_path=None):
        """
        Visualize attention patterns with 18/82 pattern highlight.

        Args:
            model: Transformer language model
            tokenizer: Tokenizer for the model
            sample_text: Sample text for visualization
            model_name: Name of the model for the plot title
            save_path: Path to save the visualization
        """
        try:
            # Tokenize input
            inputs = tokenizer(sample_text, return_tensors="pt")
            tokens = tokenizer.convert_ids_to_tokens(inputs["input_ids"][0])

            # Get model predictions with attention outputs
            model.eval()
            with torch.no_grad():
                outputs = model(**inputs, output_attentions=True)

            # Get attention weights
            attentions = outputs.attentions

            if attentions is None or len(attentions) == 0:
                logger.warning("No attention weights available for visualization")
                return

            # Select a middle layer for visualization
            middle_layer = len(attentions) // 2
            layer_attention = attentions[middle_layer][0]  # First batch

            # Average across heads
            avg_attention = layer_attention.mean(dim=0).cpu().numpy()

            # Calculate 18/82 split
            result = self._calculate_1882_split(avg_attention.flatten())

            # Create heatmap
            plt.figure(figsize=(12, 10))
            plt.imshow(avg_attention, cmap="viridis")

            # Add token labels
            plt.xticks(np.arange(len(tokens)), tokens, rotation=90)
            plt.yticks(np.arange(len(tokens)), tokens)

            plt.title(f"Attention Pattern for {model_name} (Layer {middle_layer})")
            plt.colorbar(label="Attention Weight")

            # Add pattern match indicator
            pattern_text = "18/82 Pattern Detected" if result["is_1882_pattern"] else "No 18/82 Pattern"
            proximity_text = f"Proximity: {result['proximity_to_1882_percent']:.2f}%"
            plt.text(0.02, 0.98, pattern_text + "\n" + proximity_text,
                    transform=plt.gca().transAxes, fontsize=12,
                    bbox=dict(facecolor='white', alpha=0.8))

            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300)
                logger.info(f"Attention visualization saved to {save_path}")

            plt.close()
        except Exception as e:
            logger.error(f"Error visualizing attention patterns: {str(e)}")

    def visualize_token_probabilities(self, model, tokenizer, sample_text, model_name="unnamed_lm", save_path=None):
        """
        Visualize token probability distributions with 18/82 pattern highlight.

        Args:
            model: Transformer language model
            tokenizer: Tokenizer for the model
            sample_text: Sample text for visualization
            model_name: Name of the model for the plot title
            save_path: Path to save the visualization
        """
        try:
            # Tokenize input
            inputs = tokenizer(sample_text, return_tensors="pt")
            tokens = tokenizer.convert_ids_to_tokens(inputs["input_ids"][0])

            # Get model predictions
            model.eval()
            with torch.no_grad():
                outputs = model(**inputs)

            # Get logits and convert to probabilities
            logits = outputs.logits
            probs = torch.nn.functional.softmax(logits, dim=-1)

            # Calculate entropy for each position
            entropies = []
            for i in range(logits.shape[1]):
                position_probs = probs[0, i, :].cpu().numpy()
                # Calculate entropy: -sum(p * log(p))
                entropy = -np.sum(position_probs * np.log(position_probs + 1e-10))
                entropies.append(entropy)

            # Calculate 18/82 split for entropies
            result = self._calculate_1882_split(entropies)

            # Create bar chart
            plt.figure(figsize=(12, 8))

            # Plot entropies
            plt.bar(range(len(entropies)), entropies, alpha=0.7, color='blue')

            # Highlight 18/82 split if valid
            if "split_index" in result:
                plt.axvline(x=result["split_index"], color='red', linestyle='--',
                           label=f'18/82 Split (Proximity: {result["proximity_to_1882_percent"]:.2f}%)')

                # Add pattern match indicator
                pattern_text = "18/82 Pattern Detected" if result["is_1882_pattern"] else "No 18/82 Pattern"
                plt.text(0.02, 0.95, pattern_text,
                        transform=plt.gca().transAxes, fontsize=12,
                        bbox=dict(facecolor='white', alpha=0.8))

            # Add token labels
            plt.xticks(range(len(tokens)), tokens, rotation=90)

            plt.title(f"Token Entropy Distribution for {model_name}")
            plt.xlabel("Token Position")
            plt.ylabel("Entropy")
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300)
                logger.info(f"Token probability visualization saved to {save_path}")

            plt.close()
        except Exception as e:
            logger.error(f"Error visualizing token probabilities: {str(e)}")

    def create_cognitive_report(self, analysis_results, report_path=None):
        """
        Create a comprehensive report of cognitive analysis results.

        Args:
            analysis_results: List of analysis result dictionaries
            report_path: Path to save the report

        Returns:
            Dict with report summary
        """
        logger.info("Creating comprehensive cognitive analysis report")

        # Create summary statistics
        summary = {
            "total_models_analyzed": len(analysis_results),
            "models_with_1882_pattern": sum(1 for r in analysis_results if r.get("overall_1882_pattern_score", 0) > 0.5),
            "average_1882_pattern_score": np.mean([r.get("overall_1882_pattern_score", 0) for r in analysis_results]),
            "average_pi_relationship_score": np.mean([r.get("overall_pi_relationship_score", 0) for r in analysis_results]),
            "model_details": []
        }

        # Add details for each model
        for result in analysis_results:
            model_detail = {
                "name": result["model_name"],
                "overall_1882_pattern_score": result.get("overall_1882_pattern_score", 0),
                "overall_pi_relationship_score": result.get("overall_pi_relationship_score", 0),
                "weight_1882_pattern_score": result.get("weight_analysis", {}).get("overall_1882_pattern_score", 0),
                "weight_pi_relationship_score": result.get("weight_analysis", {}).get("overall_pi_relationship_score", 0)
            }

            # Add token analysis if available
            if "token_analysis" in result and result["token_analysis"]:
                model_detail["token_1882_pattern_score"] = result["token_analysis"].get("overall_1882_pattern_score", 0)
                model_detail["token_pi_relationship_score"] = result["token_analysis"].get("overall_pi_relationship_score", 0)

            # Add attention analysis if available
            if "attention_analysis" in result and result["attention_analysis"]:
                model_detail["attention_1882_pattern_score"] = result["attention_analysis"].get("overall_1882_pattern_score", 0)
                model_detail["attention_pi_relationship_score"] = result["attention_analysis"].get("overall_pi_relationship_score", 0)

            summary["model_details"].append(model_detail)

        # Save summary
        if report_path:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2)
            logger.info(f"Cognitive analysis report saved to {report_path}")

        return summary