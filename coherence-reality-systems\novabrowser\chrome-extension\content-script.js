// NovaBrowser Chrome Extension - Content Script
// Injects coherence analysis into any webpage

class NovaBrowserExtension {
    constructor() {
        this.backendUrl = 'http://localhost:8090';
        this.analysisData = {};
        this.overlay = null;
        this.init();
    }

    async init() {
        console.log('🌐 NovaBrowser Extension loaded on:', window.location.href);
        
        // Wait for page to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.startAnalysis());
        } else {
            this.startAnalysis();
        }
    }

    async startAnalysis() {
        console.log('🧬 Starting coherence analysis...');
        
        try {
            // Run analysis
            const results = await this.runFullAnalysis();
            
            // Create overlay
            this.createCoherenceOverlay(results);
            
            // Send results to popup
            chrome.runtime.sendMessage({
                type: 'ANALYSIS_COMPLETE',
                data: results,
                url: window.location.href
            });
            
        } catch (error) {
            console.error('NovaBrowser analysis failed:', error);
        }
    }

    async runFullAnalysis() {
        const startTime = performance.now();
        
        // Get backend coherence
        let backendCoherence = 0.5;
        try {
            const response = await fetch(`${this.backendUrl}/status`, {
                method: 'GET',
                mode: 'cors'
            });
            if (response.ok) {
                const data = await response.json();
                backendCoherence = data.coherence || 0.5;
            }
        } catch (error) {
            console.warn('Backend unavailable, using local analysis');
        }

        // Analyze current page
        const pageAnalysis = this.analyzeCurrentPage();
        
        // Combine results
        const results = {
            coherence: {
                overall: Math.round((pageAnalysis.structural + pageAnalysis.functional + backendCoherence) / 3 * 100),
                structural: Math.round(pageAnalysis.structural * 100),
                functional: Math.round(pageAnalysis.functional * 100),
                relational: Math.round(backendCoherence * 100)
            },
            accessibility: pageAnalysis.accessibility,
            security: pageAnalysis.security,
            performance: {
                analysisTime: Math.round(performance.now() - startTime)
            },
            url: window.location.href,
            timestamp: new Date().toISOString()
        };

        console.log('📊 Analysis complete:', results);
        return results;
    }

    analyzeCurrentPage() {
        // Real DOM analysis
        const elements = document.querySelectorAll('*');
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        const paragraphs = document.querySelectorAll('p');
        const links = document.querySelectorAll('a');
        const buttons = document.querySelectorAll('button');
        const images = document.querySelectorAll('img');
        const forms = document.querySelectorAll('form');

        console.log(`📊 DOM Analysis: ${elements.length} elements, ${headings.length} headings, ${images.length} images`);

        // Structural coherence
        const headingRatio = headings.length / Math.max(1, paragraphs.length);
        const structural = Math.min(1, headingRatio * 2);

        // Functional coherence  
        const interactiveElements = links.length + buttons.length + forms.length;
        const functional = Math.min(1, interactiveElements / 10);

        // Accessibility analysis
        const accessibility = this.analyzeAccessibility();

        // Security analysis
        const security = this.analyzeSecurity();

        return {
            structural,
            functional,
            accessibility,
            security
        };
    }

    analyzeAccessibility() {
        const violations = [];
        let score = 100;

        // Check images without alt text
        const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
        if (imagesWithoutAlt.length > 0) {
            violations.push(`${imagesWithoutAlt.length} images missing alt text`);
            score -= 20;
        }

        // Check heading structure
        const h1s = document.querySelectorAll('h1');
        if (h1s.length !== 1) {
            violations.push(`Found ${h1s.length} H1 elements (should be 1)`);
            score -= 15;
        }

        // Check form labels
        const unlabeledInputs = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
        const unlabeled = Array.from(unlabeledInputs).filter(input => {
            const id = input.getAttribute('id');
            return !id || !document.querySelector(`label[for="${id}"]`);
        });
        if (unlabeled.length > 0) {
            violations.push(`${unlabeled.length} form inputs without labels`);
            score -= 15;
        }

        // Check color contrast (simplified)
        const lowContrastElements = this.findLowContrastElements();
        if (lowContrastElements.length > 0) {
            violations.push(`${lowContrastElements.length} elements with poor contrast`);
            score -= 10;
        }

        return {
            score: Math.max(0, score),
            violations: violations
        };
    }

    findLowContrastElements() {
        // Simplified contrast checking
        const elements = document.querySelectorAll('*');
        const lowContrast = [];
        
        for (let element of elements) {
            const style = window.getComputedStyle(element);
            const bgColor = style.backgroundColor;
            const textColor = style.color;
            
            // Simple heuristic for obvious low contrast
            if (bgColor.includes('255, 255, 0') && textColor.includes('255, 255, 255')) {
                lowContrast.push(element);
            }
        }
        
        return lowContrast;
    }

    analyzeSecurity() {
        const threats = [];
        let level = 'LOW';

        // Check protocol
        if (location.protocol === 'http:') {
            threats.push('Insecure HTTP connection');
            level = 'MEDIUM';
        }

        // Check for external scripts
        const externalScripts = document.querySelectorAll('script[src]');
        let externalCount = 0;
        externalScripts.forEach(script => {
            const src = script.getAttribute('src');
            if (src && !src.includes(location.hostname) && !src.startsWith('/')) {
                externalCount++;
            }
        });

        if (externalCount > 5) {
            threats.push(`${externalCount} external scripts detected`);
            if (level === 'LOW') level = 'MEDIUM';
        }

        return {
            level: level,
            threats: threats.length,
            details: threats
        };
    }

    createCoherenceOverlay(results) {
        // Remove existing overlay
        if (this.overlay) {
            this.overlay.remove();
        }

        // Create overlay
        this.overlay = document.createElement('div');
        this.overlay.id = 'novabrowser-overlay';
        this.overlay.innerHTML = `
            <div class="nova-header">
                <span class="nova-logo">🧬</span>
                <span class="nova-title">NovaBrowser</span>
                <button class="nova-close" onclick="this.parentElement.parentElement.style.display='none'">×</button>
            </div>
            <div class="nova-metrics">
                <div class="nova-metric">
                    <span class="nova-label">Coherence:</span>
                    <span class="nova-value ${results.coherence.overall >= 82 ? 'high' : results.coherence.overall >= 60 ? 'medium' : 'low'}">
                        ${results.coherence.overall}%
                    </span>
                </div>
                <div class="nova-metric">
                    <span class="nova-label">Accessibility:</span>
                    <span class="nova-value ${results.accessibility.score >= 90 ? 'high' : results.accessibility.score >= 70 ? 'medium' : 'low'}">
                        ${results.accessibility.score}%
                    </span>
                </div>
                <div class="nova-metric">
                    <span class="nova-label">Security:</span>
                    <span class="nova-value ${results.security.level === 'LOW' ? 'high' : results.security.level === 'MEDIUM' ? 'medium' : 'low'}">
                        ${results.security.level}
                    </span>
                </div>
            </div>
            <div class="nova-status">
                ${results.coherence.overall >= 82 ? '⚡ Ψ-Snap ACTIVE' : '⚠️ Below Ψ-Snap threshold'}
            </div>
            ${results.accessibility.violations.length > 0 ? `
                <div class="nova-violations">
                    <div class="nova-violations-header">Violations Found:</div>
                    ${results.accessibility.violations.map(v => `<div class="nova-violation">• ${v}</div>`).join('')}
                    <button class="nova-autofix" onclick="novaBrowserExt.autoFixViolations()">🔧 Auto-Fix All</button>
                </div>
            ` : ''}
        `;

        document.body.appendChild(this.overlay);

        // Make overlay draggable
        this.makeDraggable(this.overlay);
    }

    makeDraggable(element) {
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
        const header = element.querySelector('.nova-header');
        
        header.onmousedown = dragMouseDown;

        function dragMouseDown(e) {
            e = e || window.event;
            e.preventDefault();
            pos3 = e.clientX;
            pos4 = e.clientY;
            document.onmouseup = closeDragElement;
            document.onmousemove = elementDrag;
        }

        function elementDrag(e) {
            e = e || window.event;
            e.preventDefault();
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;
            element.style.top = (element.offsetTop - pos2) + "px";
            element.style.left = (element.offsetLeft - pos1) + "px";
        }

        function closeDragElement() {
            document.onmouseup = null;
            document.onmousemove = null;
        }
    }

    autoFixViolations() {
        console.log('🔧 Starting auto-fix...');
        let fixed = 0;

        // Fix images without alt text
        const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
        imagesWithoutAlt.forEach((img, index) => {
            img.setAttribute('alt', `Image ${index + 1}`);
            fixed++;
        });

        // Fix unlabeled inputs
        const unlabeledInputs = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
        unlabeledInputs.forEach(input => {
            const placeholder = input.getAttribute('placeholder') || 'Input field';
            input.setAttribute('aria-label', placeholder);
            fixed++;
        });

        console.log(`✅ Auto-fixed ${fixed} violations`);
        
        // Re-run analysis
        setTimeout(() => this.startAnalysis(), 500);
    }
}

// Initialize extension
const novaBrowserExt = new NovaBrowserExtension();

// Make globally available for onclick handlers
window.novaBrowserExt = novaBrowserExt;
