#!/usr/bin/env python3
"""
Download the Computer Network Traffic dataset from Kaggle.
"""

import os
import sys
import logging
import subprocess
import pandas as pd

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('kaggle_download.log')
    ]
)
logger = logging.getLogger('Kaggle_Download')

def download_dataset():
    """Download the Computer Network Traffic dataset from Kaggle."""
    logger.info("Downloading Computer Network Traffic dataset from Kaggle...")
    
    # Create a directory for the dataset
    os.makedirs("kaggle_network_traffic", exist_ok=True)
    
    try:
        # Download the dataset using the Kaggle API
        subprocess.run(
            ["kaggle", "datasets", "download", "crawford/computer-network-traffic", "-p", "kaggle_network_traffic"],
            check=True
        )
        logger.info("Dataset downloaded successfully.")
        
        # Extract the dataset
        subprocess.run(
            ["unzip", "kaggle_network_traffic/computer-network-traffic.zip", "-d", "kaggle_network_traffic"],
            check=True
        )
        logger.info("Dataset extracted successfully.")
        
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Error downloading dataset: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return False

def load_dataset():
    """Load the Computer Network Traffic dataset."""
    logger.info("Loading Computer Network Traffic dataset...")
    
    try:
        # Load the dataset
        df = pd.read_csv("kaggle_network_traffic/traffic.csv")
        logger.info(f"Dataset loaded successfully with {len(df)} rows and {len(df.columns)} columns.")
        
        # Display the first few rows
        logger.info("First few rows of the dataset:")
        logger.info(df.head())
        
        # Display dataset information
        logger.info("Dataset information:")
        logger.info(df.info())
        
        # Display dataset statistics
        logger.info("Dataset statistics:")
        logger.info(df.describe())
        
        return df
    except Exception as e:
        logger.error(f"Error loading dataset: {e}")
        return None

def main():
    """Main function to download and load the dataset."""
    logger.info("Starting Kaggle dataset download and loading...")
    
    # Download the dataset
    if download_dataset():
        # Load the dataset
        df = load_dataset()
        
        if df is not None:
            logger.info("Dataset downloaded and loaded successfully.")
            return df
    
    logger.error("Failed to download or load the dataset.")
    return None

if __name__ == "__main__":
    main()
