{"metadata": {"name": "Rate Limiting Connector", "version": "1.0.0", "category": "Test", "description": "Test connector for rate limiting", "author": "NovaGRC", "tags": ["test", "rate-limiting"]}, "authentication": {"type": "API_KEY", "fields": {"apiKey": {"type": "string", "description": "API Key", "required": true, "sensitive": true}, "headerName": {"type": "string", "description": "Header name for the API key", "required": true, "default": "X-API-Key"}}, "testConnection": {"endpoint": "/health", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "http://localhost:3005", "headers": {"Content-Type": "application/json"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}, "rateLimit": {"requests": 5, "period": "1m"}}, "endpoints": [{"id": "rateLimitedEndpoint", "name": "Rate Limited Endpoint", "path": "/rate-limited", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}], "mappings": [{"sourceEndpoint": "rateLimitedEndpoint", "targetSystem": "NovaGRC", "targetEntity": "RateLimitTest", "transformations": [{"source": "$.count", "target": "requestCount", "transform": "identity"}, {"source": "$.message", "target": "message", "transform": "identity"}]}], "events": {"webhooks": [], "polling": []}}