/**
 * OAuth2 Authentication Routes
 */

const express = require('express');
const router = express.Router();
const OAuth2Controller = require('../controllers/OAuth2Controller');
const { authenticate } = require('../middleware/authMiddleware');

// Public routes
router.get('/providers', (req, res, next) => {
  OAuth2Controller.getProviders(req, res, next);
});

router.get('/providers/:id', (req, res, next) => {
  OAuth2Controller.getProviderById(req, res, next);
});

router.get('/authorize/:providerId', (req, res, next) => {
  OAuth2Controller.initiateAuth(req, res, next);
});

router.post('/callback', (req, res, next) => {
  OAuth2Controller.processCallback(req, res, next);
});

module.exports = router;
