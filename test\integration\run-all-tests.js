/**
 * NovaFuse Universal API Connector - Integration Test Runner
 * 
 * This module runs all integration tests.
 */

const { createLogger } = require('../../src/utils/logger');
const connectorRegistryTests = require('./connector-registry-test');
const connectorConfigTests = require('./connector-config-test');
const connectorRuntimeTests = require('./connector-runtime-test');

const logger = createLogger('integration-test-runner');

/**
 * Run all integration tests
 */
async function runAllTests() {
  logger.info('Running all integration tests...');
  
  const startTime = Date.now();
  
  const results = {
    passed: 0,
    failed: 0,
    skipped: 0,
    total: 0,
    suites: []
  };
  
  try {
    // Run connector registry tests
    logger.info('Running connector registry tests...');
    const registryResults = await connectorRegistryTests.suite.run();
    results.suites.push(registryResults);
    results.passed += registryResults.passed;
    results.failed += registryResults.failed;
    results.skipped += registryResults.skipped;
    results.total += registryResults.total;
    
    // Run connector config tests
    logger.info('Running connector config tests...');
    const configResults = await connectorConfigTests.suite.run();
    results.suites.push(configResults);
    results.passed += configResults.passed;
    results.failed += configResults.failed;
    results.skipped += configResults.skipped;
    results.total += configResults.total;
    
    // Run connector runtime tests
    logger.info('Running connector runtime tests...');
    const runtimeResults = await connectorRuntimeTests.suite.run();
    results.suites.push(runtimeResults);
    results.passed += runtimeResults.passed;
    results.failed += runtimeResults.failed;
    results.skipped += runtimeResults.skipped;
    results.total += runtimeResults.total;
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Log results
    logger.info(`All integration tests completed in ${duration}ms`, {
      passed: results.passed,
      failed: results.failed,
      skipped: results.skipped,
      total: results.total
    });
    
    if (results.failed > 0) {
      logger.error(`${results.failed} tests failed`);
      process.exit(1);
    } else {
      logger.info(`All ${results.passed} tests passed`);
    }
  } catch (error) {
    logger.error('Error running integration tests', { error });
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests
};
