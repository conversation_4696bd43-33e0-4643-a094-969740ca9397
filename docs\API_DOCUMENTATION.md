# NovaConnect API Documentation

This document provides comprehensive documentation for the NovaConnect API endpoints.

## Base URL

All API endpoints are relative to the base URL:

```
/api/v1
```

## Table of Contents

1. [Authentication](#authentication)
2. [Rate Limiting](#rate-limiting)
3. [Error Handling](#error-handling)
4. [API Endpoints](#api-endpoints)
   - [Connector Management](#connector-management)
   - [Credential Management](#credential-management)
   - [Execution](#execution)
   - [CSDE Integration](#csde-integration)
   - [Role-Based Access Control (RBAC)](#role-based-access-control-rbac)
   - [Health and Metrics](#health-and-metrics)

## Authentication

Most endpoints require authentication. The API supports the following authentication methods:

1. **JWT Token**: Include a Bearer token in the Authorization header
   ```
   Authorization: Bearer <token>
   ```

2. **API Key**: Include an API key in the X-API-Key header
   ```
   X-API-Key: <api-key>
   ```

### Authentication Endpoints

#### Register

```
POST /auth/register
```

**Request Body:**

```json
{
  "username": "user123",
  "password": "securepassword",
  "email": "<EMAIL>"
}
```

**Response:**

```json
{
  "id": "user-id",
  "username": "user123",
  "email": "<EMAIL>",
  "role": "user",
  "permissions": ["read"],
  "created": "2023-01-01T00:00:00Z"
}
```

#### Login

```
POST /auth/login
```

**Request Body:**

```json
{
  "username": "user123",
  "password": "securepassword"
}
```

**Response (without 2FA):**

```json
{
  "user": {
    "id": "user-id",
    "username": "user123",
    "email": "<EMAIL>",
    "role": "user",
    "permissions": ["read"],
    "lastLogin": "2023-01-01T00:00:00Z"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": "24h",
  "authMethod": "password"
}
```

**Response (with 2FA enabled):**

```json
{
  "requiresTwoFactor": true,
  "userId": "user-id",
  "user": {
    "id": "user-id",
    "username": "user123",
    "email": "<EMAIL>"
  }
}
```

#### Refresh Token

```
POST /auth/refresh-token
```

**Request Body:**

```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user-id",
    "username": "user123",
    "email": "<EMAIL>",
    "role": "user",
    "permissions": ["read"]
  }
}
```

#### OAuth2 Authentication

##### Get OAuth2 Providers

```
GET /oauth2/providers
```

**Response:**

```json
[
  {
    "id": "google",
    "name": "Google",
    "enabled": true,
    "authorizationUrl": "https://accounts.google.com/o/oauth2/v2/auth",
    "scope": "openid profile email"
  },
  {
    "id": "github",
    "name": "GitHub",
    "enabled": true,
    "authorizationUrl": "https://github.com/login/oauth/authorize",
    "scope": "read:user user:email"
  }
]
```

##### Get OAuth2 Provider by ID

```
GET /oauth2/providers/:id
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| id | string | path | Provider ID |

**Response:**

```json
{
  "id": "google",
  "name": "Google",
  "enabled": true,
  "authorizationUrl": "https://accounts.google.com/o/oauth2/v2/auth",
  "scope": "openid profile email"
}
```

##### Initiate OAuth2 Authentication

```
GET /oauth2/authorize/:providerId
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| providerId | string | path | Provider ID |
| redirectUri | string | query | Redirect URI after authentication |

**Response:**

```json
{
  "providerId": "google",
  "authUrl": "https://accounts.google.com/o/oauth2/v2/auth?client_id=your-client-id&response_type=code&scope=openid%20profile%20email&redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Fcallback&state=random-state-string",
  "state": "random-state-string"
}
```

##### Process OAuth2 Callback

```
POST /oauth2/callback
```

**Query Parameters:**

| Name | Type | Description |
|------|------|------------|
| code | string | Authorization code from OAuth2 provider |
| state | string | State parameter from authorization request |

**Request Body:**

```json
{
  "redirectUri": "http://localhost:3000/callback"
}
```

**Response:**

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user-id",
    "username": "user123",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "user",
    "permissions": ["read"]
  },
  "authMethod": "oauth2",
  "provider": {
    "id": "google"
  },
  "expiresIn": "24h"
}
```

#### Logout

```
POST /auth/logout
```

**Headers:**

```
Authorization: Bearer <token>
```

**Response:**

```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

### Two-Factor Authentication

#### Verify 2FA Token (Login)

```
POST /auth/2fa/verify
```

**Request Body:**

```json
{
  "userId": "user-id",
  "token": "123456"
}
```

**Response:**

```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user-id",
    "username": "user123",
    "email": "<EMAIL>",
    "role": "user",
    "permissions": ["read"]
  },
  "expiresIn": "24h"
}
```

#### Generate 2FA Secret

```
POST /2fa/generate
```

**Headers:**

```
Authorization: Bearer <token>
```

**Response:**

```json
{
  "success": true,
  "data": {
    "secret": "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
    "qrCode": "data:image/png;base64,..."
  }
}
```

#### Verify and Enable 2FA

```
POST /2fa/verify
```

**Headers:**

```
Authorization: Bearer <token>
```

**Request Body:**

```json
{
  "token": "123456"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Two-factor authentication enabled successfully"
}
```

#### Disable 2FA

```
POST /2fa/disable
```

**Headers:**

```
Authorization: Bearer <token>
```

**Response:**

```json
{
  "success": true,
  "message": "Two-factor authentication disabled successfully"
}
```

#### Check 2FA Status

```
GET /2fa/status
```

**Headers:**

```
Authorization: Bearer <token>
```

**Response:**

```json
{
  "success": true,
  "enabled": true
}
```

## Rate Limiting

All endpoints are subject to rate limiting. The API returns the following headers:

- `X-RateLimit-Limit`: The maximum number of requests allowed in the current time window
- `X-RateLimit-Remaining`: The number of requests remaining in the current time window
- `X-RateLimit-Reset`: The time when the current rate limit window resets (Unix timestamp)

## Brute Force Protection

The API includes protection against brute force attacks on authentication endpoints. After a certain number of failed login attempts, the account will be temporarily blocked.

## Authentication Audit Logging

The API includes comprehensive audit logging for authentication events. This helps track and monitor authentication-related activities for security and compliance purposes.

### Authentication Audit Endpoints

#### Get Authentication Audit Logs

```
GET /auth/audit
```

**Headers:**

```
Authorization: Bearer <token>
```

**Query Parameters:**

| Name | Type | Description |
|------|------|------------|
| startDate | string | Start date for filtering logs (ISO format) |
| endDate | string | End date for filtering logs (ISO format) |
| userId | string | Filter logs by user ID |
| action | string | Filter logs by action (LOGIN, LOGOUT, REGISTER, etc.) |
| status | string | Filter logs by status (success, failure) |
| page | number | Page number for pagination |
| limit | number | Number of logs per page |

**Response:**

```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "log-id",
        "timestamp": "2023-01-01T00:00:00Z",
        "userId": "user-id",
        "action": "LOGIN",
        "resourceType": "auth",
        "resourceId": "<EMAIL>",
        "details": {
          "method": "password",
          "success": true
        },
        "ip": "***********",
        "userAgent": "Mozilla/5.0...",
        "status": "success"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 10
  }
}
```

#### Get User Authentication Audit Logs

```
GET /auth/audit/user/:userId
```

**Headers:**

```
Authorization: Bearer <token>
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| userId | string | path | User ID |

**Query Parameters:**

| Name | Type | Description |
|------|------|------------|
| startDate | string | Start date for filtering logs (ISO format) |
| endDate | string | End date for filtering logs (ISO format) |
| action | string | Filter logs by action (LOGIN, LOGOUT, REGISTER, etc.) |
| status | string | Filter logs by status (success, failure) |
| page | number | Page number for pagination |
| limit | number | Number of logs per page |

**Response:**

```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "log-id",
        "timestamp": "2023-01-01T00:00:00Z",
        "userId": "user-id",
        "action": "LOGIN",
        "resourceType": "auth",
        "resourceId": "<EMAIL>",
        "details": {
          "method": "password",
          "success": true
        },
        "ip": "***********",
        "userAgent": "Mozilla/5.0...",
        "status": "success"
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 10
  }
}
```

#### Get User Login History

```
GET /auth/audit/user/:userId/login-history
```

**Headers:**

```
Authorization: Bearer <token>
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| userId | string | path | User ID |

**Query Parameters:**

| Name | Type | Description |
|------|------|------------|
| page | number | Page number for pagination |
| limit | number | Number of logs per page |

**Response:**

```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "log-id",
        "timestamp": "2023-01-01T00:00:00Z",
        "userId": "user-id",
        "action": "LOGIN",
        "resourceType": "auth",
        "resourceId": "<EMAIL>",
        "details": {
          "method": "password",
          "success": true
        },
        "ip": "***********",
        "userAgent": "Mozilla/5.0...",
        "status": "success"
      }
    ],
    "total": 20,
    "page": 1,
    "limit": 10
  }
}
```

#### Get Failed Login Attempts

```
GET /auth/audit/failed-logins
```

**Headers:**

```
Authorization: Bearer <token>
```

**Query Parameters:**

| Name | Type | Description |
|------|------|------------|
| startDate | string | Start date for filtering logs (ISO format) |
| endDate | string | End date for filtering logs (ISO format) |
| page | number | Page number for pagination |
| limit | number | Number of logs per page |

**Response:**

```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "log-id",
        "timestamp": "2023-01-01T00:00:00Z",
        "userId": null,
        "action": "LOGIN",
        "resourceType": "auth",
        "resourceId": "<EMAIL>",
        "details": {
          "method": "password",
          "success": false,
          "reason": "Invalid username or password"
        },
        "ip": "***********",
        "userAgent": "Mozilla/5.0...",
        "status": "failure"
      }
    ],
    "total": 10,
    "page": 1,
    "limit": 10
  }
}
```

#### Get Authentication Activity Summary

```
GET /auth/audit/summary
```

**Headers:**

```
Authorization: Bearer <token>
```

**Query Parameters:**

| Name | Type | Description |
|------|------|------------|
| startDate | string | Start date for summary (ISO format) |
| endDate | string | End date for summary (ISO format) |

**Response:**

```json
{
  "success": true,
  "data": {
    "totalEvents": 1000,
    "successfulLogins": 800,
    "failedLogins": 50,
    "registrations": 20,
    "logouts": 100,
    "tokenRefreshes": 200,
    "twoFactorEvents": 30,
    "startDate": "2023-01-01T00:00:00Z",
    "endDate": "2023-01-31T23:59:59Z"
  }
}
```

## IP Restrictions

The API supports IP-based access restrictions. You can configure allowlists, blocklists, and custom rules to control access to the API.

### IP Restriction Configuration

#### Get IP Restrictions

```
GET /ip-restrictions
```

**Headers:**

```
Authorization: Bearer <token>
```

**Response:**

```json
{
  "success": true,
  "data": {
    "enabled": true,
    "mode": "allowlist",
    "allowlist": [
      "***********",
      "10.0.0.0/24"
    ],
    "blocklist": [
      "*******"
    ],
    "rules": [
      {
        "name": "Allow Office Network",
        "action": "allow",
        "ipRange": "***********/16"
      },
      {
        "name": "Block Known Attacker",
        "action": "block",
        "ip": "*******"
      }
    ]
  }
}
```

#### Update IP Restriction Configuration

```
PUT /ip-restrictions/config
```

**Headers:**

```
Authorization: Bearer <token>
```

**Request Body:**

```json
{
  "enabled": true,
  "mode": "allowlist"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "enabled": true,
    "mode": "allowlist",
    "allowlist": [],
    "blocklist": [],
    "rules": []
  }
}
```

#### Add IP to Allowlist

```
POST /ip-restrictions/allowlist
```

**Headers:**

```
Authorization: Bearer <token>
```

**Request Body:**

```json
{
  "ip": "***********"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "enabled": true,
    "mode": "allowlist",
    "allowlist": [
      "***********"
    ],
    "blocklist": [],
    "rules": []
  }
}
```

#### Remove IP from Allowlist

```
DELETE /ip-restrictions/allowlist
```

**Headers:**

```
Authorization: Bearer <token>
```

**Request Body:**

```json
{
  "ip": "***********"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "enabled": true,
    "mode": "allowlist",
    "allowlist": [],
    "blocklist": [],
    "rules": []
  }
}
```

#### Add IP to Blocklist

```
POST /ip-restrictions/blocklist
```

**Headers:**

```
Authorization: Bearer <token>
```

**Request Body:**

```json
{
  "ip": "*******"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "enabled": true,
    "mode": "allowlist",
    "allowlist": [],
    "blocklist": [
      "*******"
    ],
    "rules": []
  }
}
```

#### Remove IP from Blocklist

```
DELETE /ip-restrictions/blocklist
```

**Headers:**

```
Authorization: Bearer <token>
```

**Request Body:**

```json
{
  "ip": "*******"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "enabled": true,
    "mode": "allowlist",
    "allowlist": [],
    "blocklist": [],
    "rules": []
  }
}
```

#### Add Rule

```
POST /ip-restrictions/rules
```

**Headers:**

```
Authorization: Bearer <token>
```

**Request Body:**

```json
{
  "rule": {
    "name": "Allow Office Network",
    "action": "allow",
    "ipRange": "***********/16"
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "enabled": true,
    "mode": "allowlist",
    "allowlist": [],
    "blocklist": [],
    "rules": [
      {
        "name": "Allow Office Network",
        "action": "allow",
        "ipRange": "***********/16"
      }
    ]
  }
}
```

#### Remove Rule

```
DELETE /ip-restrictions/rules
```

**Headers:**

```
Authorization: Bearer <token>
```

**Request Body:**

```json
{
  "name": "Allow Office Network"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "enabled": true,
    "mode": "allowlist",
    "allowlist": [],
    "blocklist": [],
    "rules": []
  }
}
```

#### Reset to Default

```
POST /ip-restrictions/reset
```

**Headers:**

```
Authorization: Bearer <token>
```

**Response:**

```json
{
  "success": true,
  "data": {
    "enabled": false,
    "mode": "allowlist",
    "allowlist": [],
    "blocklist": [],
    "rules": []
  }
}
```

### Brute Force Protection Configuration

#### Get Brute Force Protection Configuration

```
GET /brute-force/config
```

**Headers:**

```
Authorization: Bearer <token>
```

**Response:**

```json
{
  "success": true,
  "data": {
    "maxAttempts": 5,
    "windowMs": 900000,
    "blockDuration": 1800000
  }
}
```

#### Update Brute Force Protection Configuration

```
PUT /brute-force/config
```

**Headers:**

```
Authorization: Bearer <token>
```

**Request Body:**

```json
{
  "maxAttempts": 3,
  "windowMs": 600000,
  "blockDuration": 3600000
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "maxAttempts": 3,
    "windowMs": 600000,
    "blockDuration": 3600000
  }
}
```

#### Reset Brute Force Protection for a User

```
POST /brute-force/reset
```

**Headers:**

```
Authorization: Bearer <token>
```

**Request Body:**

```json
{
  "identifier": "<EMAIL>"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Brute force protection <NAME_EMAIL>"
}
```

### Rate Limit Configuration

#### Get Rate Limits

```
GET /rate-limits
```

**Headers:**

```
Authorization: Bearer <token>
```

**Response:**

```json
{
  "success": true,
  "data": {
    "anonymous": {
      "requests": 60,
      "period": 60
    },
    "authenticated": {
      "requests": 300,
      "period": 60
    },
    "apiKey": {
      "requests": 600,
      "period": 60
    }
  }
}
```

#### Update Rate Limits

```
PUT /rate-limits
```

**Headers:**

```
Authorization: Bearer <token>
```

**Request Body:**

```json
{
  "rateLimits": {
    "anonymous": {
      "requests": 30,
      "period": 60
    },
    "authenticated": {
      "requests": 150,
      "period": 60
    },
    "apiKey": {
      "requests": 300,
      "period": 60
    }
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "anonymous": {
      "requests": 30,
      "period": 60
    },
    "authenticated": {
      "requests": 150,
      "period": 60
    },
    "apiKey": {
      "requests": 300,
      "period": 60
    }
  }
}
```

#### Reset Rate Limits

```
POST /rate-limits/reset
```

**Headers:**

```
Authorization: Bearer <token>
```

**Response:**

```json
{
  "success": true,
  "data": {
    "anonymous": {
      "requests": 60,
      "period": 60
    },
    "authenticated": {
      "requests": 300,
      "period": 60
    },
    "apiKey": {
      "requests": 600,
      "period": 60
    }
  }
}
```

## Error Handling

The API returns consistent error responses in the following format:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error message",
    "details": {}
  }
}
```

## API Endpoints

### Connector Management

#### Get All Connectors

```
GET /connectors
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| page | integer | query | Page number (default: 1) |
| limit | integer | query | Items per page (default: 20) |
| status | string | query | Filter by status (active, inactive, draft) |
| category | string | query | Filter by category |
| search | string | query | Search term |

**Response:**

```json
{
  "success": true,
  "data": {
    "connectors": [
      {
        "id": "connector-id",
        "name": "Connector Name",
        "description": "Connector description",
        "version": "1.0.0",
        "status": "active",
        "category": "category",
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

#### Get Connector by ID

```
GET /connectors/:id
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| id | string | path | Connector ID |

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "connector-id",
    "name": "Connector Name",
    "description": "Connector description",
    "version": "1.0.0",
    "status": "active",
    "category": "category",
    "endpoints": [
      {
        "id": "endpoint-id",
        "name": "Endpoint Name",
        "description": "Endpoint description",
        "path": "/path",
        "method": "GET",
        "parameters": []
      }
    ],
    "authentication": {
      "type": "API_KEY",
      "config": {}
    },
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### Create Connector

```
POST /connectors
```

**Request Body:**

```json
{
  "name": "Connector Name",
  "description": "Connector description",
  "version": "1.0.0",
  "category": "category",
  "endpoints": [
    {
      "name": "Endpoint Name",
      "description": "Endpoint description",
      "path": "/path",
      "method": "GET",
      "parameters": []
    }
  ],
  "authentication": {
    "type": "API_KEY",
    "config": {}
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "connector-id",
    "name": "Connector Name",
    "description": "Connector description",
    "version": "1.0.0",
    "status": "draft",
    "category": "category",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### Update Connector

```
PUT /connectors/:id
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| id | string | path | Connector ID |

**Request Body:**

```json
{
  "name": "Updated Connector Name",
  "description": "Updated connector description",
  "version": "1.0.1",
  "status": "active",
  "category": "updated-category",
  "endpoints": [
    {
      "name": "Updated Endpoint Name",
      "description": "Updated endpoint description",
      "path": "/updated-path",
      "method": "POST",
      "parameters": []
    }
  ],
  "authentication": {
    "type": "OAUTH2",
    "config": {}
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "connector-id",
    "name": "Updated Connector Name",
    "description": "Updated connector description",
    "version": "1.0.1",
    "status": "active",
    "category": "updated-category",
    "updatedAt": "2023-01-02T00:00:00Z"
  }
}
```

#### Delete Connector

```
DELETE /connectors/:id
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| id | string | path | Connector ID |

**Response:**

```json
{
  "success": true,
  "data": {
    "message": "Connector deleted successfully"
  }
}
```

### Credential Management

#### Store Credentials

```
POST /credentials
```

**Request Body:**

```json
{
  "connectorId": "connector-id",
  "name": "Credential Name",
  "type": "API_KEY",
  "credentials": {
    "apiKey": "your-api-key"
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "credential-id",
    "connectorId": "connector-id",
    "name": "Credential Name",
    "type": "API_KEY",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### Delete Credentials

```
DELETE /credentials/:id
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| id | string | path | Credential ID |

**Response:**

```json
{
  "success": true,
  "data": {
    "message": "Credentials deleted successfully"
  }
}
```

#### Test Connection

```
POST /credentials/:id/test
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| id | string | path | Credential ID |

**Response:**

```json
{
  "success": true,
  "data": {
    "status": "success",
    "message": "Connection successful",
    "details": {
      "responseTime": 123,
      "statusCode": 200
    }
  }
}
```

### Execution

#### Execute Endpoint

```
POST /execute/:connectorId/:endpointId
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| connectorId | string | path | Connector ID |
| endpointId | string | path | Endpoint ID |

**Request Body:**

```json
{
  "credentialId": "credential-id",
  "parameters": {
    "param1": "value1",
    "param2": "value2"
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "result": {},
    "metadata": {
      "responseTime": 123,
      "statusCode": 200
    }
  }
}
```

### CSDE Integration

#### Get CSDE Dashboard

```
GET /csde/dashboard
```

**Response:**

```json
{
  "success": true,
  "dashboard": {
    "schema": {},
    "data": {}
  }
}
```

#### Get CSDE Dashboard Data

```
GET /csde/dashboard/data
```

**Response:**

```json
{
  "success": true,
  "data": {
    "metrics": {},
    "charts": {}
  }
}
```

#### Update CSDE Configuration

```
PUT /csde/dashboard/config
```

**Request Body:**

```json
{
  "csdeApiUrl": "http://localhost:3010",
  "enableCaching": true,
  "cacheSize": 1000,
  "enableMetrics": true
}
```

**Response:**

```json
{
  "success": true,
  "message": "Configuration updated successfully"
}
```

### Health and Metrics

#### Get Health Status

```
GET /health
```

**Response:**

```json
{
  "status": "ok",
  "service": "nova-connect-api",
  "timestamp": "2023-01-01T00:00:00Z",
  "version": "1.0.0"
}
```

#### Get Detailed Health Status

```
GET /health/detailed
```

**Response:**

```json
{
  "status": "ok",
  "service": "nova-connect-api",
  "timestamp": "2023-01-01T00:00:00Z",
  "version": "1.0.0",
  "components": {
    "database": {
      "status": "ok",
      "latency": 5
    },
    "cache": {
      "status": "ok",
      "latency": 2
    },
    "csde": {
      "status": "ok",
      "latency": 10
    }
  }
}
```

#### Get Prometheus Metrics

```
GET /metrics
```

**Response:**

Prometheus-formatted metrics

### Role-Based Access Control (RBAC)

#### Get All Roles

```
GET /rbac/roles
```

**Response:**

```json
[
  {
    "_id": "role-id",
    "name": "Administrator",
    "description": "Full access to all resources",
    "permissions": ["*"],
    "inheritsFrom": [],
    "isSystem": true,
    "isDefault": false,
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
]
```

#### Get All Roles with Permissions

```
GET /rbac/roles/with-permissions
```

**Response:**

```json
[
  {
    "_id": "role-id",
    "name": "Administrator",
    "description": "Full access to all resources",
    "permissions": ["*"],
    "inheritsFrom": [],
    "isSystem": true,
    "isDefault": false,
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z",
    "effectivePermissions": [
      "connector:view",
      "connector:create",
      "connector:edit",
      "connector:delete",
      "connector:use"
    ]
  }
]
```

#### Get Role by ID

```
GET /rbac/roles/:id
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| id | string | path | Role ID |

**Response:**

```json
{
  "_id": "role-id",
  "name": "Administrator",
  "description": "Full access to all resources",
  "permissions": ["*"],
  "inheritsFrom": [],
  "isSystem": true,
  "isDefault": false,
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

#### Get Role Permissions

```
GET /rbac/roles/:id/permissions
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| id | string | path | Role ID |

**Response:**

```json
[
  "connector:view",
  "connector:create",
  "connector:edit",
  "connector:delete",
  "connector:use"
]
```

#### Get Role Inheritance Tree

```
GET /rbac/roles/:id/inheritance-tree
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| id | string | path | Role ID |

**Response:**

```json
{
  "_id": "role-id",
  "name": "Manager",
  "description": "Access to most resources",
  "isSystem": true,
  "isDefault": false,
  "inheritsFrom": [
    {
      "_id": "parent-role-id",
      "name": "User",
      "description": "Basic user access",
      "isSystem": true,
      "isDefault": true
    }
  ]
}
```

#### Create Role

```
POST /rbac/roles
```

**Request Body:**

```json
{
  "name": "Custom Role",
  "description": "Custom role with specific permissions",
  "permissions": [
    "connector:view",
    "connector:use",
    "workflow:view",
    "workflow:use"
  ],
  "inheritsFrom": ["user-role-id"],
  "isDefault": false
}
```

**Response:**

```json
{
  "_id": "role-id",
  "name": "Custom Role",
  "description": "Custom role with specific permissions",
  "permissions": [
    "connector:view",
    "connector:use",
    "workflow:view",
    "workflow:use"
  ],
  "inheritsFrom": ["user-role-id"],
  "isSystem": false,
  "isDefault": false,
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

#### Update Role

```
PUT /rbac/roles/:id
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| id | string | path | Role ID |

**Request Body:**

```json
{
  "name": "Updated Custom Role",
  "description": "Updated description",
  "permissions": [
    "connector:view",
    "connector:use",
    "workflow:view",
    "workflow:use",
    "monitoring:view"
  ],
  "inheritsFrom": ["user-role-id"],
  "isDefault": false
}
```

**Response:**

```json
{
  "_id": "role-id",
  "name": "Updated Custom Role",
  "description": "Updated description",
  "permissions": [
    "connector:view",
    "connector:use",
    "workflow:view",
    "workflow:use",
    "monitoring:view"
  ],
  "inheritsFrom": ["user-role-id"],
  "isSystem": false,
  "isDefault": false,
  "updatedAt": "2023-01-02T00:00:00Z"
}
```

#### Delete Role

```
DELETE /rbac/roles/:id
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| id | string | path | Role ID |

**Response:**

```json
{
  "success": true,
  "message": "Role deleted"
}
```

#### Get All Permissions

```
GET /rbac/permissions
```

**Response:**

```json
[
  {
    "_id": "permission-id",
    "name": "View Connectors",
    "description": "View connectors and their configurations",
    "resource": "connector",
    "action": "view",
    "isSystem": true,
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
]
```

#### Get Permission by ID

```
GET /rbac/permissions/:id
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| id | string | path | Permission ID |

**Response:**

```json
{
  "_id": "permission-id",
  "name": "View Connectors",
  "description": "View connectors and their configurations",
  "resource": "connector",
  "action": "view",
  "isSystem": true,
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

#### Create Permission

```
POST /rbac/permissions
```

**Request Body:**

```json
{
  "name": "Custom Permission",
  "description": "Custom permission for a specific resource",
  "resource": "custom",
  "action": "view"
}
```

**Response:**

```json
{
  "_id": "permission-id",
  "name": "Custom Permission",
  "description": "Custom permission for a specific resource",
  "resource": "custom",
  "action": "view",
  "isSystem": false,
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

#### Update Permission

```
PUT /rbac/permissions/:id
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| id | string | path | Permission ID |

**Request Body:**

```json
{
  "name": "Updated Custom Permission",
  "description": "Updated description",
  "resource": "custom",
  "action": "edit"
}
```

**Response:**

```json
{
  "_id": "permission-id",
  "name": "Updated Custom Permission",
  "description": "Updated description",
  "resource": "custom",
  "action": "edit",
  "isSystem": false,
  "updatedAt": "2023-01-02T00:00:00Z"
}
```

#### Delete Permission

```
DELETE /rbac/permissions/:id
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| id | string | path | Permission ID |

**Response:**

```json
{
  "success": true,
  "message": "Permission deleted"
}
```

#### Get User Roles

```
GET /rbac/users/:userId/roles
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| userId | string | path | User ID |

**Response:**

```json
[
  {
    "_id": "role-id",
    "name": "Administrator",
    "description": "Full access to all resources",
    "permissions": ["*"],
    "inheritsFrom": [],
    "isSystem": true,
    "isDefault": false,
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
]
```

#### Assign Role to User

```
POST /rbac/users/:userId/roles
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| userId | string | path | User ID |

**Request Body:**

```json
{
  "roleId": "role-id",
  "scope": "global",
  "scopeId": null
}
```

**Response:**

```json
{
  "success": true,
  "message": "Role assigned to user"
}
```

#### Remove Role from User

```
DELETE /rbac/users/:userId/roles/:roleId
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| userId | string | path | User ID |
| roleId | string | path | Role ID |

**Response:**

```json
{
  "success": true,
  "message": "Role removed from user"
}
```

#### Get User Permissions

```
GET /rbac/users/:userId/permissions
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| userId | string | path | User ID |

**Response:**

```json
[
  "connector:view",
  "connector:create",
  "connector:edit",
  "connector:delete",
  "connector:use"
]
```

#### Check User Permission

```
GET /rbac/users/:userId/permissions/:permissionId
```

**Parameters:**

| Name | Type | In | Description |
|------|------|-------|------------|
| userId | string | path | User ID |
| permissionId | string | path | Permission ID or resource:action string |

**Response:**

```json
{
  "hasPermission": true
}
```
