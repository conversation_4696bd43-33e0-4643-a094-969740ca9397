import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText
} from '../../components/DiagramComponents';

const CrossDomainIntelligence = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>CROSS-DOMAIN INTELLIGENCE ENGINE</ContainerLabel>
      </ContainerBox>
      
      {/* Central Intelligence Engine */}
      <ContainerBox width="200px" height="200px" left="300px" top="120px" style={{ borderRadius: '50%' }}>
        <ContainerLabel style={{ top: '90px' }}>INTELLIGENCE ENGINE</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="340px" top="160px" width="120px" height="40px">
        <ComponentNumber>401</ComponentNumber>
        <ComponentLabel>Cross-Domain</ComponentLabel>
        Correlation
      </ComponentBox>
      
      <ComponentBox left="340px" top="210px" width="120px" height="40px">
        <ComponentNumber>402</ComponentNumber>
        <ComponentLabel>Predictive</ComponentLabel>
        Analytics
      </ComponentBox>
      
      {/* GRC Domain */}
      <ContainerBox width="150px" height="150px" left="100px" top="80px">
        <ContainerLabel>GRC DOMAIN</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="115px" top="120px" width="120px" height="40px">
        <ComponentNumber>403</ComponentNumber>
        <ComponentLabel>Compliance</ComponentLabel>
        Data
      </ComponentBox>
      
      <ComponentBox left="115px" top="170px" width="120px" height="40px">
        <ComponentNumber>404</ComponentNumber>
        <ComponentLabel>Risk</ComponentLabel>
        Metrics
      </ComponentBox>
      
      {/* IT Domain */}
      <ContainerBox width="150px" height="150px" left="100px" top="260px">
        <ContainerLabel>IT DOMAIN</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="115px" top="300px" width="120px" height="40px">
        <ComponentNumber>405</ComponentNumber>
        <ComponentLabel>System</ComponentLabel>
        Telemetry
      </ComponentBox>
      
      <ComponentBox left="115px" top="350px" width="120px" height="40px">
        <ComponentNumber>406</ComponentNumber>
        <ComponentLabel>Infrastructure</ComponentLabel>
        Data
      </ComponentBox>
      
      {/* Cybersecurity Domain */}
      <ContainerBox width="150px" height="150px" left="550px" top="80px">
        <ContainerLabel>CYBERSECURITY DOMAIN</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="565px" top="120px" width="120px" height="40px">
        <ComponentNumber>407</ComponentNumber>
        <ComponentLabel>Threat</ComponentLabel>
        Intelligence
      </ComponentBox>
      
      <ComponentBox left="565px" top="170px" width="120px" height="40px">
        <ComponentNumber>408</ComponentNumber>
        <ComponentLabel>Security</ComponentLabel>
        Events
      </ComponentBox>
      
      {/* External Data Domain */}
      <ContainerBox width="150px" height="150px" left="550px" top="260px">
        <ContainerLabel>EXTERNAL DATA</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="565px" top="300px" width="120px" height="40px">
        <ComponentNumber>409</ComponentNumber>
        <ComponentLabel>Regulatory</ComponentLabel>
        Updates
      </ComponentBox>
      
      <ComponentBox left="565px" top="350px" width="120px" height="40px">
        <ComponentNumber>410</ComponentNumber>
        <ComponentLabel>Industry</ComponentLabel>
        Intelligence
      </ComponentBox>
      
      {/* Connecting Arrows */}
      <CurvedArrow width="100" height="60" left="235" top="140">
        <path
          d="M 0,0 Q 30,0 65,30"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="65,30 55,22 58,32"
          fill="#333"
        />
      </CurvedArrow>
      
      <CurvedArrow width="100" height="60" left="235" top="170">
        <path
          d="M 0,0 Q 30,0 65,-30"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="65,-30 55,-22 58,-32"
          fill="#333"
        />
      </CurvedArrow>
      
      <CurvedArrow width="100" height="60" left="235" top="320">
        <path
          d="M 0,0 Q 30,-30 65,-110"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="65,-110 55,-102 58,-112"
          fill="#333"
        />
      </CurvedArrow>
      
      <CurvedArrow width="100" height="60" left="235" top="350">
        <path
          d="M 0,0 Q 30,-60 65,-140"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="65,-140 55,-132 58,-142"
          fill="#333"
        />
      </CurvedArrow>
      
      <CurvedArrow width="100" height="60" left="500" top="140">
        <path
          d="M 65,0 Q 35,0 0,30"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="0,30 10,22 7,32"
          fill="#333"
        />
      </CurvedArrow>
      
      <CurvedArrow width="100" height="60" left="500" top="170">
        <path
          d="M 65,0 Q 35,0 0,-30"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="0,-30 10,-22 7,-32"
          fill="#333"
        />
      </CurvedArrow>
      
      <CurvedArrow width="100" height="60" left="500" top="320">
        <path
          d="M 65,0 Q 35,-30 0,-110"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="0,-110 10,-102 7,-112"
          fill="#333"
        />
      </CurvedArrow>
      
      <CurvedArrow width="100" height="60" left="500" top="350">
        <path
          d="M 65,0 Q 35,-60 0,-140"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="0,-140 10,-132 7,-142"
          fill="#333"
        />
      </CurvedArrow>
      
      {/* Output Arrows */}
      <Arrow left="400px" top="320px" width="2px" height="80px" />
      
      <ComponentBox left="340px" top="400px" width="120px" height="40px">
        <ComponentNumber>411</ComponentNumber>
        <ComponentLabel>Actionable</ComponentLabel>
        Intelligence
      </ComponentBox>
      
      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Intelligence Engine</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Domain Data</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Data Flow</LegendText>
        </LegendItem>
      </DiagramLegend>
    </DiagramFrame>
  );
};

export default CrossDomainIntelligence;
