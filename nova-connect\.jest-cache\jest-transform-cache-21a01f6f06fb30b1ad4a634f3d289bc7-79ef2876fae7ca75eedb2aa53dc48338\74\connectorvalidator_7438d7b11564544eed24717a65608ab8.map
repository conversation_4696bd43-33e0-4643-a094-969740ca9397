{"version": 3, "names": ["ConnectorValidator", "validateConnector", "connector", "errors", "result", "<PERSON><PERSON><PERSON><PERSON>", "push", "validateMetadata", "validateAuthentication", "validateConfiguration", "validateEndpoints", "length", "metadata", "name", "version", "isValidVersion", "category", "description", "author", "tags", "Array", "isArray", "tag", "icon", "documentationUrl", "authentication", "type", "includes", "fields", "key", "field", "Object", "entries", "required", "undefined", "sensitive", "oauth2Config", "validateOAuth2Config", "tokenUrl", "authorizationUrl", "scopes", "scope", "grantType", "configuration", "baseUrl", "isValidUrl", "headers", "timeout", "rateLimit", "requests", "period", "retryPolicy", "maxRetries", "backoffStrategy", "endpoints", "i", "endpoint", "id", "path", "method", "parameters", "validateEndpointParameters", "response", "validateEndpointResponse", "pagination", "validateEndpointPagination", "endpointIds", "map", "e", "duplicateIds", "filter", "index", "indexOf", "join", "endpointIndex", "param", "validateParameter", "query", "body", "properties", "successCode", "dataPath", "schema", "limit", "offset", "nextPageToken", "pageToken", "page", "pageSize", "cursor", "url", "URL", "error", "semverRegex", "test", "module", "exports"], "sources": ["connector-validator.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector Validator\n * \n * This module provides validation utilities for connector templates.\n */\n\n/**\n * Connector Validator\n * \n * Validates connector templates against schema requirements.\n */\nclass ConnectorValidator {\n  /**\n   * Validate a connector template\n   * @param {Object} connector - Connector template to validate\n   * @returns {Object} - Validation result with isValid and errors properties\n   */\n  static validateConnector(connector) {\n    const errors = [];\n    const result = { isValid: true, errors };\n    \n    if (!connector) {\n      result.isValid = false;\n      errors.push('Connector template is required');\n      return result;\n    }\n    \n    // Validate metadata\n    this.validateMetadata(connector, errors);\n    \n    // Validate authentication\n    this.validateAuthentication(connector, errors);\n    \n    // Validate configuration\n    this.validateConfiguration(connector, errors);\n    \n    // Validate endpoints\n    this.validateEndpoints(connector, errors);\n    \n    // Update isValid based on errors\n    result.isValid = errors.length === 0;\n    \n    return result;\n  }\n\n  /**\n   * Validate connector metadata\n   * @param {Object} connector - Connector template\n   * @param {Array} errors - Array to add errors to\n   */\n  static validateMetadata(connector, errors) {\n    if (!connector.metadata) {\n      errors.push('Connector metadata is required');\n      return;\n    }\n    \n    const { metadata } = connector;\n    \n    // Check required fields\n    if (!metadata.name) {\n      errors.push('Connector name is required');\n    } else if (typeof metadata.name !== 'string') {\n      errors.push('Connector name must be a string');\n    }\n    \n    if (!metadata.version) {\n      errors.push('Connector version is required');\n    } else if (typeof metadata.version !== 'string') {\n      errors.push('Connector version must be a string');\n    } else if (!this.isValidVersion(metadata.version)) {\n      errors.push('Connector version must be in semver format (e.g., 1.0.0)');\n    }\n    \n    if (!metadata.category) {\n      errors.push('Connector category is required');\n    } else if (typeof metadata.category !== 'string') {\n      errors.push('Connector category must be a string');\n    }\n    \n    if (!metadata.description) {\n      errors.push('Connector description is required');\n    } else if (typeof metadata.description !== 'string') {\n      errors.push('Connector description must be a string');\n    }\n    \n    // Check optional fields\n    if (metadata.author && typeof metadata.author !== 'string') {\n      errors.push('Connector author must be a string');\n    }\n    \n    if (metadata.tags) {\n      if (!Array.isArray(metadata.tags)) {\n        errors.push('Connector tags must be an array');\n      } else {\n        for (const tag of metadata.tags) {\n          if (typeof tag !== 'string') {\n            errors.push('Connector tags must be strings');\n            break;\n          }\n        }\n      }\n    }\n    \n    if (metadata.icon && typeof metadata.icon !== 'string') {\n      errors.push('Connector icon must be a string URL');\n    }\n    \n    if (metadata.documentationUrl && typeof metadata.documentationUrl !== 'string') {\n      errors.push('Connector documentation URL must be a string');\n    }\n  }\n\n  /**\n   * Validate connector authentication\n   * @param {Object} connector - Connector template\n   * @param {Array} errors - Array to add errors to\n   */\n  static validateAuthentication(connector, errors) {\n    if (!connector.authentication) {\n      errors.push('Connector authentication is required');\n      return;\n    }\n    \n    const { authentication } = connector;\n    \n    // Check authentication type\n    if (!authentication.type) {\n      errors.push('Authentication type is required');\n    } else if (typeof authentication.type !== 'string') {\n      errors.push('Authentication type must be a string');\n    } else if (!['API_KEY', 'BASIC', 'OAUTH2', 'CUSTOM', 'NONE'].includes(authentication.type)) {\n      errors.push('Authentication type must be one of: API_KEY, BASIC, OAUTH2, CUSTOM, NONE');\n    }\n    \n    // Check authentication fields\n    if (!authentication.fields) {\n      errors.push('Authentication fields are required');\n      return;\n    }\n    \n    if (typeof authentication.fields !== 'object') {\n      errors.push('Authentication fields must be an object');\n      return;\n    }\n    \n    // Validate each field\n    for (const [key, field] of Object.entries(authentication.fields)) {\n      if (typeof field !== 'object') {\n        errors.push(`Authentication field '${key}' must be an object`);\n        continue;\n      }\n      \n      if (!field.type) {\n        errors.push(`Authentication field '${key}' must have a type`);\n      } else if (typeof field.type !== 'string') {\n        errors.push(`Authentication field '${key}' type must be a string`);\n      } else if (!['string', 'number', 'boolean', 'object', 'array'].includes(field.type)) {\n        errors.push(`Authentication field '${key}' type must be one of: string, number, boolean, object, array`);\n      }\n      \n      if (field.required !== undefined && typeof field.required !== 'boolean') {\n        errors.push(`Authentication field '${key}' required must be a boolean`);\n      }\n      \n      if (field.sensitive !== undefined && typeof field.sensitive !== 'boolean') {\n        errors.push(`Authentication field '${key}' sensitive must be a boolean`);\n      }\n      \n      if (field.description && typeof field.description !== 'string') {\n        errors.push(`Authentication field '${key}' description must be a string`);\n      }\n    }\n    \n    // Validate OAuth2 config if applicable\n    if (authentication.type === 'OAUTH2' && authentication.oauth2Config) {\n      this.validateOAuth2Config(authentication.oauth2Config, errors);\n    }\n  }\n\n  /**\n   * Validate OAuth2 configuration\n   * @param {Object} oauth2Config - OAuth2 configuration\n   * @param {Array} errors - Array to add errors to\n   */\n  static validateOAuth2Config(oauth2Config, errors) {\n    if (typeof oauth2Config !== 'object') {\n      errors.push('OAuth2 configuration must be an object');\n      return;\n    }\n    \n    // Check required fields\n    if (!oauth2Config.tokenUrl) {\n      errors.push('OAuth2 token URL is required');\n    } else if (typeof oauth2Config.tokenUrl !== 'string') {\n      errors.push('OAuth2 token URL must be a string');\n    }\n    \n    // Check optional fields\n    if (oauth2Config.authorizationUrl && typeof oauth2Config.authorizationUrl !== 'string') {\n      errors.push('OAuth2 authorization URL must be a string');\n    }\n    \n    if (oauth2Config.scopes) {\n      if (!Array.isArray(oauth2Config.scopes)) {\n        errors.push('OAuth2 scopes must be an array');\n      } else {\n        for (const scope of oauth2Config.scopes) {\n          if (typeof scope !== 'string') {\n            errors.push('OAuth2 scopes must be strings');\n            break;\n          }\n        }\n      }\n    }\n    \n    if (oauth2Config.grantType && typeof oauth2Config.grantType !== 'string') {\n      errors.push('OAuth2 grant type must be a string');\n    }\n  }\n\n  /**\n   * Validate connector configuration\n   * @param {Object} connector - Connector template\n   * @param {Array} errors - Array to add errors to\n   */\n  static validateConfiguration(connector, errors) {\n    if (!connector.configuration) {\n      errors.push('Connector configuration is required');\n      return;\n    }\n    \n    const { configuration } = connector;\n    \n    // Check required fields\n    if (!configuration.baseUrl) {\n      errors.push('Configuration base URL is required');\n    } else if (typeof configuration.baseUrl !== 'string') {\n      errors.push('Configuration base URL must be a string');\n    } else if (!this.isValidUrl(configuration.baseUrl)) {\n      errors.push('Configuration base URL must be a valid URL');\n    }\n    \n    // Check optional fields\n    if (configuration.headers && typeof configuration.headers !== 'object') {\n      errors.push('Configuration headers must be an object');\n    }\n    \n    if (configuration.timeout !== undefined) {\n      if (typeof configuration.timeout !== 'number') {\n        errors.push('Configuration timeout must be a number');\n      } else if (configuration.timeout <= 0) {\n        errors.push('Configuration timeout must be greater than 0');\n      }\n    }\n    \n    if (configuration.rateLimit) {\n      if (typeof configuration.rateLimit !== 'object') {\n        errors.push('Configuration rate limit must be an object');\n      } else {\n        if (configuration.rateLimit.requests !== undefined) {\n          if (typeof configuration.rateLimit.requests !== 'number') {\n            errors.push('Configuration rate limit requests must be a number');\n          } else if (configuration.rateLimit.requests <= 0) {\n            errors.push('Configuration rate limit requests must be greater than 0');\n          }\n        }\n        \n        if (configuration.rateLimit.period !== undefined && typeof configuration.rateLimit.period !== 'string') {\n          errors.push('Configuration rate limit period must be a string');\n        }\n      }\n    }\n    \n    if (configuration.retryPolicy) {\n      if (typeof configuration.retryPolicy !== 'object') {\n        errors.push('Configuration retry policy must be an object');\n      } else {\n        if (configuration.retryPolicy.maxRetries !== undefined) {\n          if (typeof configuration.retryPolicy.maxRetries !== 'number') {\n            errors.push('Configuration retry policy max retries must be a number');\n          } else if (configuration.retryPolicy.maxRetries < 0) {\n            errors.push('Configuration retry policy max retries must be greater than or equal to 0');\n          }\n        }\n        \n        if (configuration.retryPolicy.backoffStrategy !== undefined && typeof configuration.retryPolicy.backoffStrategy !== 'string') {\n          errors.push('Configuration retry policy backoff strategy must be a string');\n        }\n      }\n    }\n  }\n\n  /**\n   * Validate connector endpoints\n   * @param {Object} connector - Connector template\n   * @param {Array} errors - Array to add errors to\n   */\n  static validateEndpoints(connector, errors) {\n    if (!connector.endpoints) {\n      errors.push('Connector endpoints are required');\n      return;\n    }\n    \n    if (!Array.isArray(connector.endpoints)) {\n      errors.push('Connector endpoints must be an array');\n      return;\n    }\n    \n    if (connector.endpoints.length === 0) {\n      errors.push('Connector must have at least one endpoint');\n      return;\n    }\n    \n    // Validate each endpoint\n    for (let i = 0; i < connector.endpoints.length; i++) {\n      const endpoint = connector.endpoints[i];\n      \n      if (typeof endpoint !== 'object') {\n        errors.push(`Endpoint at index ${i} must be an object`);\n        continue;\n      }\n      \n      // Check required fields\n      if (!endpoint.id) {\n        errors.push(`Endpoint at index ${i} must have an ID`);\n      } else if (typeof endpoint.id !== 'string') {\n        errors.push(`Endpoint at index ${i} ID must be a string`);\n      }\n      \n      if (!endpoint.name) {\n        errors.push(`Endpoint at index ${i} must have a name`);\n      } else if (typeof endpoint.name !== 'string') {\n        errors.push(`Endpoint at index ${i} name must be a string`);\n      }\n      \n      if (!endpoint.path) {\n        errors.push(`Endpoint at index ${i} must have a path`);\n      } else if (typeof endpoint.path !== 'string') {\n        errors.push(`Endpoint at index ${i} path must be a string`);\n      }\n      \n      if (!endpoint.method) {\n        errors.push(`Endpoint at index ${i} must have a method`);\n      } else if (typeof endpoint.method !== 'string') {\n        errors.push(`Endpoint at index ${i} method must be a string`);\n      } else if (!['GET', 'POST', 'PUT', 'PATCH', 'DELETE'].includes(endpoint.method)) {\n        errors.push(`Endpoint at index ${i} method must be one of: GET, POST, PUT, PATCH, DELETE`);\n      }\n      \n      // Validate parameters if present\n      if (endpoint.parameters) {\n        this.validateEndpointParameters(endpoint.parameters, i, errors);\n      }\n      \n      // Validate response if present\n      if (endpoint.response) {\n        this.validateEndpointResponse(endpoint.response, i, errors);\n      }\n      \n      // Validate pagination if present\n      if (endpoint.pagination) {\n        this.validateEndpointPagination(endpoint.pagination, i, errors);\n      }\n    }\n    \n    // Check for duplicate endpoint IDs\n    const endpointIds = connector.endpoints.map(e => e.id);\n    const duplicateIds = endpointIds.filter((id, index) => endpointIds.indexOf(id) !== index);\n    \n    if (duplicateIds.length > 0) {\n      errors.push(`Duplicate endpoint IDs found: ${duplicateIds.join(', ')}`);\n    }\n  }\n\n  /**\n   * Validate endpoint parameters\n   * @param {Object} parameters - Endpoint parameters\n   * @param {number} endpointIndex - Index of the endpoint\n   * @param {Array} errors - Array to add errors to\n   */\n  static validateEndpointParameters(parameters, endpointIndex, errors) {\n    if (typeof parameters !== 'object') {\n      errors.push(`Endpoint at index ${endpointIndex} parameters must be an object`);\n      return;\n    }\n    \n    // Validate path parameters\n    if (parameters.path) {\n      if (typeof parameters.path !== 'object') {\n        errors.push(`Endpoint at index ${endpointIndex} path parameters must be an object`);\n      } else {\n        for (const [key, param] of Object.entries(parameters.path)) {\n          this.validateParameter(param, key, 'path', endpointIndex, errors);\n        }\n      }\n    }\n    \n    // Validate query parameters\n    if (parameters.query) {\n      if (typeof parameters.query !== 'object') {\n        errors.push(`Endpoint at index ${endpointIndex} query parameters must be an object`);\n      } else {\n        for (const [key, param] of Object.entries(parameters.query)) {\n          this.validateParameter(param, key, 'query', endpointIndex, errors);\n        }\n      }\n    }\n    \n    // Validate body parameters\n    if (parameters.body) {\n      if (typeof parameters.body !== 'object') {\n        errors.push(`Endpoint at index ${endpointIndex} body parameters must be an object`);\n      } else {\n        if (parameters.body.required !== undefined && typeof parameters.body.required !== 'boolean') {\n          errors.push(`Endpoint at index ${endpointIndex} body required must be a boolean`);\n        }\n        \n        if (parameters.body.properties && typeof parameters.body.properties !== 'object') {\n          errors.push(`Endpoint at index ${endpointIndex} body properties must be an object`);\n        }\n      }\n    }\n  }\n\n  /**\n   * Validate a parameter\n   * @param {Object} param - Parameter to validate\n   * @param {string} key - Parameter key\n   * @param {string} type - Parameter type (path, query, body)\n   * @param {number} endpointIndex - Index of the endpoint\n   * @param {Array} errors - Array to add errors to\n   */\n  static validateParameter(param, key, type, endpointIndex, errors) {\n    if (typeof param !== 'object') {\n      errors.push(`Endpoint at index ${endpointIndex} ${type} parameter '${key}' must be an object`);\n      return;\n    }\n    \n    if (param.type && typeof param.type !== 'string') {\n      errors.push(`Endpoint at index ${endpointIndex} ${type} parameter '${key}' type must be a string`);\n    }\n    \n    if (param.required !== undefined && typeof param.required !== 'boolean') {\n      errors.push(`Endpoint at index ${endpointIndex} ${type} parameter '${key}' required must be a boolean`);\n    }\n    \n    if (param.description && typeof param.description !== 'string') {\n      errors.push(`Endpoint at index ${endpointIndex} ${type} parameter '${key}' description must be a string`);\n    }\n  }\n\n  /**\n   * Validate endpoint response\n   * @param {Object} response - Endpoint response\n   * @param {number} endpointIndex - Index of the endpoint\n   * @param {Array} errors - Array to add errors to\n   */\n  static validateEndpointResponse(response, endpointIndex, errors) {\n    if (typeof response !== 'object') {\n      errors.push(`Endpoint at index ${endpointIndex} response must be an object`);\n      return;\n    }\n    \n    if (response.successCode !== undefined) {\n      if (typeof response.successCode !== 'number') {\n        errors.push(`Endpoint at index ${endpointIndex} response success code must be a number`);\n      } else if (response.successCode < 200 || response.successCode >= 300) {\n        errors.push(`Endpoint at index ${endpointIndex} response success code must be in the 2xx range`);\n      }\n    }\n    \n    if (response.dataPath && typeof response.dataPath !== 'string') {\n      errors.push(`Endpoint at index ${endpointIndex} response data path must be a string`);\n    }\n    \n    if (response.schema && typeof response.schema !== 'object') {\n      errors.push(`Endpoint at index ${endpointIndex} response schema must be an object`);\n    }\n  }\n\n  /**\n   * Validate endpoint pagination\n   * @param {Object} pagination - Endpoint pagination\n   * @param {number} endpointIndex - Index of the endpoint\n   * @param {Array} errors - Array to add errors to\n   */\n  static validateEndpointPagination(pagination, endpointIndex, errors) {\n    if (typeof pagination !== 'object') {\n      errors.push(`Endpoint at index ${endpointIndex} pagination must be an object`);\n      return;\n    }\n    \n    if (!pagination.type) {\n      errors.push(`Endpoint at index ${endpointIndex} pagination type is required`);\n    } else if (typeof pagination.type !== 'string') {\n      errors.push(`Endpoint at index ${endpointIndex} pagination type must be a string`);\n    } else if (!['offset', 'token', 'page', 'cursor'].includes(pagination.type)) {\n      errors.push(`Endpoint at index ${endpointIndex} pagination type must be one of: offset, token, page, cursor`);\n    }\n    \n    if (!pagination.parameters) {\n      errors.push(`Endpoint at index ${endpointIndex} pagination parameters are required`);\n    } else if (typeof pagination.parameters !== 'object') {\n      errors.push(`Endpoint at index ${endpointIndex} pagination parameters must be an object`);\n    } else {\n      // Validate based on pagination type\n      switch (pagination.type) {\n        case 'offset':\n          if (!pagination.parameters.limit) {\n            errors.push(`Endpoint at index ${endpointIndex} pagination offset requires a limit parameter`);\n          }\n          if (!pagination.parameters.offset) {\n            errors.push(`Endpoint at index ${endpointIndex} pagination offset requires an offset parameter`);\n          }\n          break;\n        case 'token':\n          if (!pagination.parameters.nextPageToken) {\n            errors.push(`Endpoint at index ${endpointIndex} pagination token requires a nextPageToken parameter`);\n          }\n          if (!pagination.parameters.pageToken) {\n            errors.push(`Endpoint at index ${endpointIndex} pagination token requires a pageToken parameter`);\n          }\n          break;\n        case 'page':\n          if (!pagination.parameters.page) {\n            errors.push(`Endpoint at index ${endpointIndex} pagination page requires a page parameter`);\n          }\n          if (!pagination.parameters.pageSize) {\n            errors.push(`Endpoint at index ${endpointIndex} pagination page requires a pageSize parameter`);\n          }\n          break;\n        case 'cursor':\n          if (!pagination.parameters.cursor) {\n            errors.push(`Endpoint at index ${endpointIndex} pagination cursor requires a cursor parameter`);\n          }\n          break;\n      }\n    }\n  }\n\n  /**\n   * Check if a string is a valid URL\n   * @param {string} url - URL to check\n   * @returns {boolean} - Whether the URL is valid\n   */\n  static isValidUrl(url) {\n    try {\n      new URL(url);\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Check if a string is a valid semver version\n   * @param {string} version - Version to check\n   * @returns {boolean} - Whether the version is valid\n   */\n  static isValidVersion(version) {\n    const semverRegex = /^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$/;\n    return semverRegex.test(version);\n  }\n}\n\nmodule.exports = ConnectorValidator;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMA,kBAAkB,CAAC;EACvB;AACF;AACA;AACA;AACA;EACE,OAAOC,iBAAiBA,CAACC,SAAS,EAAE;IAClC,MAAMC,MAAM,GAAG,EAAE;IACjB,MAAMC,MAAM,GAAG;MAAEC,OAAO,EAAE,IAAI;MAAEF;IAAO,CAAC;IAExC,IAAI,CAACD,SAAS,EAAE;MACdE,MAAM,CAACC,OAAO,GAAG,KAAK;MACtBF,MAAM,CAACG,IAAI,CAAC,gCAAgC,CAAC;MAC7C,OAAOF,MAAM;IACf;;IAEA;IACA,IAAI,CAACG,gBAAgB,CAACL,SAAS,EAAEC,MAAM,CAAC;;IAExC;IACA,IAAI,CAACK,sBAAsB,CAACN,SAAS,EAAEC,MAAM,CAAC;;IAE9C;IACA,IAAI,CAACM,qBAAqB,CAACP,SAAS,EAAEC,MAAM,CAAC;;IAE7C;IACA,IAAI,CAACO,iBAAiB,CAACR,SAAS,EAAEC,MAAM,CAAC;;IAEzC;IACAC,MAAM,CAACC,OAAO,GAAGF,MAAM,CAACQ,MAAM,KAAK,CAAC;IAEpC,OAAOP,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOG,gBAAgBA,CAACL,SAAS,EAAEC,MAAM,EAAE;IACzC,IAAI,CAACD,SAAS,CAACU,QAAQ,EAAE;MACvBT,MAAM,CAACG,IAAI,CAAC,gCAAgC,CAAC;MAC7C;IACF;IAEA,MAAM;MAAEM;IAAS,CAAC,GAAGV,SAAS;;IAE9B;IACA,IAAI,CAACU,QAAQ,CAACC,IAAI,EAAE;MAClBV,MAAM,CAACG,IAAI,CAAC,4BAA4B,CAAC;IAC3C,CAAC,MAAM,IAAI,OAAOM,QAAQ,CAACC,IAAI,KAAK,QAAQ,EAAE;MAC5CV,MAAM,CAACG,IAAI,CAAC,iCAAiC,CAAC;IAChD;IAEA,IAAI,CAACM,QAAQ,CAACE,OAAO,EAAE;MACrBX,MAAM,CAACG,IAAI,CAAC,+BAA+B,CAAC;IAC9C,CAAC,MAAM,IAAI,OAAOM,QAAQ,CAACE,OAAO,KAAK,QAAQ,EAAE;MAC/CX,MAAM,CAACG,IAAI,CAAC,oCAAoC,CAAC;IACnD,CAAC,MAAM,IAAI,CAAC,IAAI,CAACS,cAAc,CAACH,QAAQ,CAACE,OAAO,CAAC,EAAE;MACjDX,MAAM,CAACG,IAAI,CAAC,0DAA0D,CAAC;IACzE;IAEA,IAAI,CAACM,QAAQ,CAACI,QAAQ,EAAE;MACtBb,MAAM,CAACG,IAAI,CAAC,gCAAgC,CAAC;IAC/C,CAAC,MAAM,IAAI,OAAOM,QAAQ,CAACI,QAAQ,KAAK,QAAQ,EAAE;MAChDb,MAAM,CAACG,IAAI,CAAC,qCAAqC,CAAC;IACpD;IAEA,IAAI,CAACM,QAAQ,CAACK,WAAW,EAAE;MACzBd,MAAM,CAACG,IAAI,CAAC,mCAAmC,CAAC;IAClD,CAAC,MAAM,IAAI,OAAOM,QAAQ,CAACK,WAAW,KAAK,QAAQ,EAAE;MACnDd,MAAM,CAACG,IAAI,CAAC,wCAAwC,CAAC;IACvD;;IAEA;IACA,IAAIM,QAAQ,CAACM,MAAM,IAAI,OAAON,QAAQ,CAACM,MAAM,KAAK,QAAQ,EAAE;MAC1Df,MAAM,CAACG,IAAI,CAAC,mCAAmC,CAAC;IAClD;IAEA,IAAIM,QAAQ,CAACO,IAAI,EAAE;MACjB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACT,QAAQ,CAACO,IAAI,CAAC,EAAE;QACjChB,MAAM,CAACG,IAAI,CAAC,iCAAiC,CAAC;MAChD,CAAC,MAAM;QACL,KAAK,MAAMgB,GAAG,IAAIV,QAAQ,CAACO,IAAI,EAAE;UAC/B,IAAI,OAAOG,GAAG,KAAK,QAAQ,EAAE;YAC3BnB,MAAM,CAACG,IAAI,CAAC,gCAAgC,CAAC;YAC7C;UACF;QACF;MACF;IACF;IAEA,IAAIM,QAAQ,CAACW,IAAI,IAAI,OAAOX,QAAQ,CAACW,IAAI,KAAK,QAAQ,EAAE;MACtDpB,MAAM,CAACG,IAAI,CAAC,qCAAqC,CAAC;IACpD;IAEA,IAAIM,QAAQ,CAACY,gBAAgB,IAAI,OAAOZ,QAAQ,CAACY,gBAAgB,KAAK,QAAQ,EAAE;MAC9ErB,MAAM,CAACG,IAAI,CAAC,8CAA8C,CAAC;IAC7D;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOE,sBAAsBA,CAACN,SAAS,EAAEC,MAAM,EAAE;IAC/C,IAAI,CAACD,SAAS,CAACuB,cAAc,EAAE;MAC7BtB,MAAM,CAACG,IAAI,CAAC,sCAAsC,CAAC;MACnD;IACF;IAEA,MAAM;MAAEmB;IAAe,CAAC,GAAGvB,SAAS;;IAEpC;IACA,IAAI,CAACuB,cAAc,CAACC,IAAI,EAAE;MACxBvB,MAAM,CAACG,IAAI,CAAC,iCAAiC,CAAC;IAChD,CAAC,MAAM,IAAI,OAAOmB,cAAc,CAACC,IAAI,KAAK,QAAQ,EAAE;MAClDvB,MAAM,CAACG,IAAI,CAAC,sCAAsC,CAAC;IACrD,CAAC,MAAM,IAAI,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAACqB,QAAQ,CAACF,cAAc,CAACC,IAAI,CAAC,EAAE;MAC1FvB,MAAM,CAACG,IAAI,CAAC,0EAA0E,CAAC;IACzF;;IAEA;IACA,IAAI,CAACmB,cAAc,CAACG,MAAM,EAAE;MAC1BzB,MAAM,CAACG,IAAI,CAAC,oCAAoC,CAAC;MACjD;IACF;IAEA,IAAI,OAAOmB,cAAc,CAACG,MAAM,KAAK,QAAQ,EAAE;MAC7CzB,MAAM,CAACG,IAAI,CAAC,yCAAyC,CAAC;MACtD;IACF;;IAEA;IACA,KAAK,MAAM,CAACuB,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACP,cAAc,CAACG,MAAM,CAAC,EAAE;MAChE,IAAI,OAAOE,KAAK,KAAK,QAAQ,EAAE;QAC7B3B,MAAM,CAACG,IAAI,CAAC,yBAAyBuB,GAAG,qBAAqB,CAAC;QAC9D;MACF;MAEA,IAAI,CAACC,KAAK,CAACJ,IAAI,EAAE;QACfvB,MAAM,CAACG,IAAI,CAAC,yBAAyBuB,GAAG,oBAAoB,CAAC;MAC/D,CAAC,MAAM,IAAI,OAAOC,KAAK,CAACJ,IAAI,KAAK,QAAQ,EAAE;QACzCvB,MAAM,CAACG,IAAI,CAAC,yBAAyBuB,GAAG,yBAAyB,CAAC;MACpE,CAAC,MAAM,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACF,QAAQ,CAACG,KAAK,CAACJ,IAAI,CAAC,EAAE;QACnFvB,MAAM,CAACG,IAAI,CAAC,yBAAyBuB,GAAG,+DAA+D,CAAC;MAC1G;MAEA,IAAIC,KAAK,CAACG,QAAQ,KAAKC,SAAS,IAAI,OAAOJ,KAAK,CAACG,QAAQ,KAAK,SAAS,EAAE;QACvE9B,MAAM,CAACG,IAAI,CAAC,yBAAyBuB,GAAG,8BAA8B,CAAC;MACzE;MAEA,IAAIC,KAAK,CAACK,SAAS,KAAKD,SAAS,IAAI,OAAOJ,KAAK,CAACK,SAAS,KAAK,SAAS,EAAE;QACzEhC,MAAM,CAACG,IAAI,CAAC,yBAAyBuB,GAAG,+BAA+B,CAAC;MAC1E;MAEA,IAAIC,KAAK,CAACb,WAAW,IAAI,OAAOa,KAAK,CAACb,WAAW,KAAK,QAAQ,EAAE;QAC9Dd,MAAM,CAACG,IAAI,CAAC,yBAAyBuB,GAAG,gCAAgC,CAAC;MAC3E;IACF;;IAEA;IACA,IAAIJ,cAAc,CAACC,IAAI,KAAK,QAAQ,IAAID,cAAc,CAACW,YAAY,EAAE;MACnE,IAAI,CAACC,oBAAoB,CAACZ,cAAc,CAACW,YAAY,EAAEjC,MAAM,CAAC;IAChE;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOkC,oBAAoBA,CAACD,YAAY,EAAEjC,MAAM,EAAE;IAChD,IAAI,OAAOiC,YAAY,KAAK,QAAQ,EAAE;MACpCjC,MAAM,CAACG,IAAI,CAAC,wCAAwC,CAAC;MACrD;IACF;;IAEA;IACA,IAAI,CAAC8B,YAAY,CAACE,QAAQ,EAAE;MAC1BnC,MAAM,CAACG,IAAI,CAAC,8BAA8B,CAAC;IAC7C,CAAC,MAAM,IAAI,OAAO8B,YAAY,CAACE,QAAQ,KAAK,QAAQ,EAAE;MACpDnC,MAAM,CAACG,IAAI,CAAC,mCAAmC,CAAC;IAClD;;IAEA;IACA,IAAI8B,YAAY,CAACG,gBAAgB,IAAI,OAAOH,YAAY,CAACG,gBAAgB,KAAK,QAAQ,EAAE;MACtFpC,MAAM,CAACG,IAAI,CAAC,2CAA2C,CAAC;IAC1D;IAEA,IAAI8B,YAAY,CAACI,MAAM,EAAE;MACvB,IAAI,CAACpB,KAAK,CAACC,OAAO,CAACe,YAAY,CAACI,MAAM,CAAC,EAAE;QACvCrC,MAAM,CAACG,IAAI,CAAC,gCAAgC,CAAC;MAC/C,CAAC,MAAM;QACL,KAAK,MAAMmC,KAAK,IAAIL,YAAY,CAACI,MAAM,EAAE;UACvC,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;YAC7BtC,MAAM,CAACG,IAAI,CAAC,+BAA+B,CAAC;YAC5C;UACF;QACF;MACF;IACF;IAEA,IAAI8B,YAAY,CAACM,SAAS,IAAI,OAAON,YAAY,CAACM,SAAS,KAAK,QAAQ,EAAE;MACxEvC,MAAM,CAACG,IAAI,CAAC,oCAAoC,CAAC;IACnD;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOG,qBAAqBA,CAACP,SAAS,EAAEC,MAAM,EAAE;IAC9C,IAAI,CAACD,SAAS,CAACyC,aAAa,EAAE;MAC5BxC,MAAM,CAACG,IAAI,CAAC,qCAAqC,CAAC;MAClD;IACF;IAEA,MAAM;MAAEqC;IAAc,CAAC,GAAGzC,SAAS;;IAEnC;IACA,IAAI,CAACyC,aAAa,CAACC,OAAO,EAAE;MAC1BzC,MAAM,CAACG,IAAI,CAAC,oCAAoC,CAAC;IACnD,CAAC,MAAM,IAAI,OAAOqC,aAAa,CAACC,OAAO,KAAK,QAAQ,EAAE;MACpDzC,MAAM,CAACG,IAAI,CAAC,yCAAyC,CAAC;IACxD,CAAC,MAAM,IAAI,CAAC,IAAI,CAACuC,UAAU,CAACF,aAAa,CAACC,OAAO,CAAC,EAAE;MAClDzC,MAAM,CAACG,IAAI,CAAC,4CAA4C,CAAC;IAC3D;;IAEA;IACA,IAAIqC,aAAa,CAACG,OAAO,IAAI,OAAOH,aAAa,CAACG,OAAO,KAAK,QAAQ,EAAE;MACtE3C,MAAM,CAACG,IAAI,CAAC,yCAAyC,CAAC;IACxD;IAEA,IAAIqC,aAAa,CAACI,OAAO,KAAKb,SAAS,EAAE;MACvC,IAAI,OAAOS,aAAa,CAACI,OAAO,KAAK,QAAQ,EAAE;QAC7C5C,MAAM,CAACG,IAAI,CAAC,wCAAwC,CAAC;MACvD,CAAC,MAAM,IAAIqC,aAAa,CAACI,OAAO,IAAI,CAAC,EAAE;QACrC5C,MAAM,CAACG,IAAI,CAAC,8CAA8C,CAAC;MAC7D;IACF;IAEA,IAAIqC,aAAa,CAACK,SAAS,EAAE;MAC3B,IAAI,OAAOL,aAAa,CAACK,SAAS,KAAK,QAAQ,EAAE;QAC/C7C,MAAM,CAACG,IAAI,CAAC,4CAA4C,CAAC;MAC3D,CAAC,MAAM;QACL,IAAIqC,aAAa,CAACK,SAAS,CAACC,QAAQ,KAAKf,SAAS,EAAE;UAClD,IAAI,OAAOS,aAAa,CAACK,SAAS,CAACC,QAAQ,KAAK,QAAQ,EAAE;YACxD9C,MAAM,CAACG,IAAI,CAAC,oDAAoD,CAAC;UACnE,CAAC,MAAM,IAAIqC,aAAa,CAACK,SAAS,CAACC,QAAQ,IAAI,CAAC,EAAE;YAChD9C,MAAM,CAACG,IAAI,CAAC,0DAA0D,CAAC;UACzE;QACF;QAEA,IAAIqC,aAAa,CAACK,SAAS,CAACE,MAAM,KAAKhB,SAAS,IAAI,OAAOS,aAAa,CAACK,SAAS,CAACE,MAAM,KAAK,QAAQ,EAAE;UACtG/C,MAAM,CAACG,IAAI,CAAC,kDAAkD,CAAC;QACjE;MACF;IACF;IAEA,IAAIqC,aAAa,CAACQ,WAAW,EAAE;MAC7B,IAAI,OAAOR,aAAa,CAACQ,WAAW,KAAK,QAAQ,EAAE;QACjDhD,MAAM,CAACG,IAAI,CAAC,8CAA8C,CAAC;MAC7D,CAAC,MAAM;QACL,IAAIqC,aAAa,CAACQ,WAAW,CAACC,UAAU,KAAKlB,SAAS,EAAE;UACtD,IAAI,OAAOS,aAAa,CAACQ,WAAW,CAACC,UAAU,KAAK,QAAQ,EAAE;YAC5DjD,MAAM,CAACG,IAAI,CAAC,yDAAyD,CAAC;UACxE,CAAC,MAAM,IAAIqC,aAAa,CAACQ,WAAW,CAACC,UAAU,GAAG,CAAC,EAAE;YACnDjD,MAAM,CAACG,IAAI,CAAC,2EAA2E,CAAC;UAC1F;QACF;QAEA,IAAIqC,aAAa,CAACQ,WAAW,CAACE,eAAe,KAAKnB,SAAS,IAAI,OAAOS,aAAa,CAACQ,WAAW,CAACE,eAAe,KAAK,QAAQ,EAAE;UAC5HlD,MAAM,CAACG,IAAI,CAAC,8DAA8D,CAAC;QAC7E;MACF;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOI,iBAAiBA,CAACR,SAAS,EAAEC,MAAM,EAAE;IAC1C,IAAI,CAACD,SAAS,CAACoD,SAAS,EAAE;MACxBnD,MAAM,CAACG,IAAI,CAAC,kCAAkC,CAAC;MAC/C;IACF;IAEA,IAAI,CAACc,KAAK,CAACC,OAAO,CAACnB,SAAS,CAACoD,SAAS,CAAC,EAAE;MACvCnD,MAAM,CAACG,IAAI,CAAC,sCAAsC,CAAC;MACnD;IACF;IAEA,IAAIJ,SAAS,CAACoD,SAAS,CAAC3C,MAAM,KAAK,CAAC,EAAE;MACpCR,MAAM,CAACG,IAAI,CAAC,2CAA2C,CAAC;MACxD;IACF;;IAEA;IACA,KAAK,IAAIiD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrD,SAAS,CAACoD,SAAS,CAAC3C,MAAM,EAAE4C,CAAC,EAAE,EAAE;MACnD,MAAMC,QAAQ,GAAGtD,SAAS,CAACoD,SAAS,CAACC,CAAC,CAAC;MAEvC,IAAI,OAAOC,QAAQ,KAAK,QAAQ,EAAE;QAChCrD,MAAM,CAACG,IAAI,CAAC,qBAAqBiD,CAAC,oBAAoB,CAAC;QACvD;MACF;;MAEA;MACA,IAAI,CAACC,QAAQ,CAACC,EAAE,EAAE;QAChBtD,MAAM,CAACG,IAAI,CAAC,qBAAqBiD,CAAC,kBAAkB,CAAC;MACvD,CAAC,MAAM,IAAI,OAAOC,QAAQ,CAACC,EAAE,KAAK,QAAQ,EAAE;QAC1CtD,MAAM,CAACG,IAAI,CAAC,qBAAqBiD,CAAC,sBAAsB,CAAC;MAC3D;MAEA,IAAI,CAACC,QAAQ,CAAC3C,IAAI,EAAE;QAClBV,MAAM,CAACG,IAAI,CAAC,qBAAqBiD,CAAC,mBAAmB,CAAC;MACxD,CAAC,MAAM,IAAI,OAAOC,QAAQ,CAAC3C,IAAI,KAAK,QAAQ,EAAE;QAC5CV,MAAM,CAACG,IAAI,CAAC,qBAAqBiD,CAAC,wBAAwB,CAAC;MAC7D;MAEA,IAAI,CAACC,QAAQ,CAACE,IAAI,EAAE;QAClBvD,MAAM,CAACG,IAAI,CAAC,qBAAqBiD,CAAC,mBAAmB,CAAC;MACxD,CAAC,MAAM,IAAI,OAAOC,QAAQ,CAACE,IAAI,KAAK,QAAQ,EAAE;QAC5CvD,MAAM,CAACG,IAAI,CAAC,qBAAqBiD,CAAC,wBAAwB,CAAC;MAC7D;MAEA,IAAI,CAACC,QAAQ,CAACG,MAAM,EAAE;QACpBxD,MAAM,CAACG,IAAI,CAAC,qBAAqBiD,CAAC,qBAAqB,CAAC;MAC1D,CAAC,MAAM,IAAI,OAAOC,QAAQ,CAACG,MAAM,KAAK,QAAQ,EAAE;QAC9CxD,MAAM,CAACG,IAAI,CAAC,qBAAqBiD,CAAC,0BAA0B,CAAC;MAC/D,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC5B,QAAQ,CAAC6B,QAAQ,CAACG,MAAM,CAAC,EAAE;QAC/ExD,MAAM,CAACG,IAAI,CAAC,qBAAqBiD,CAAC,uDAAuD,CAAC;MAC5F;;MAEA;MACA,IAAIC,QAAQ,CAACI,UAAU,EAAE;QACvB,IAAI,CAACC,0BAA0B,CAACL,QAAQ,CAACI,UAAU,EAAEL,CAAC,EAAEpD,MAAM,CAAC;MACjE;;MAEA;MACA,IAAIqD,QAAQ,CAACM,QAAQ,EAAE;QACrB,IAAI,CAACC,wBAAwB,CAACP,QAAQ,CAACM,QAAQ,EAAEP,CAAC,EAAEpD,MAAM,CAAC;MAC7D;;MAEA;MACA,IAAIqD,QAAQ,CAACQ,UAAU,EAAE;QACvB,IAAI,CAACC,0BAA0B,CAACT,QAAQ,CAACQ,UAAU,EAAET,CAAC,EAAEpD,MAAM,CAAC;MACjE;IACF;;IAEA;IACA,MAAM+D,WAAW,GAAGhE,SAAS,CAACoD,SAAS,CAACa,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACX,EAAE,CAAC;IACtD,MAAMY,YAAY,GAAGH,WAAW,CAACI,MAAM,CAAC,CAACb,EAAE,EAAEc,KAAK,KAAKL,WAAW,CAACM,OAAO,CAACf,EAAE,CAAC,KAAKc,KAAK,CAAC;IAEzF,IAAIF,YAAY,CAAC1D,MAAM,GAAG,CAAC,EAAE;MAC3BR,MAAM,CAACG,IAAI,CAAC,iCAAiC+D,YAAY,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACzE;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOZ,0BAA0BA,CAACD,UAAU,EAAEc,aAAa,EAAEvE,MAAM,EAAE;IACnE,IAAI,OAAOyD,UAAU,KAAK,QAAQ,EAAE;MAClCzD,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,+BAA+B,CAAC;MAC9E;IACF;;IAEA;IACA,IAAId,UAAU,CAACF,IAAI,EAAE;MACnB,IAAI,OAAOE,UAAU,CAACF,IAAI,KAAK,QAAQ,EAAE;QACvCvD,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,oCAAoC,CAAC;MACrF,CAAC,MAAM;QACL,KAAK,MAAM,CAAC7C,GAAG,EAAE8C,KAAK,CAAC,IAAI5C,MAAM,CAACC,OAAO,CAAC4B,UAAU,CAACF,IAAI,CAAC,EAAE;UAC1D,IAAI,CAACkB,iBAAiB,CAACD,KAAK,EAAE9C,GAAG,EAAE,MAAM,EAAE6C,aAAa,EAAEvE,MAAM,CAAC;QACnE;MACF;IACF;;IAEA;IACA,IAAIyD,UAAU,CAACiB,KAAK,EAAE;MACpB,IAAI,OAAOjB,UAAU,CAACiB,KAAK,KAAK,QAAQ,EAAE;QACxC1E,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,qCAAqC,CAAC;MACtF,CAAC,MAAM;QACL,KAAK,MAAM,CAAC7C,GAAG,EAAE8C,KAAK,CAAC,IAAI5C,MAAM,CAACC,OAAO,CAAC4B,UAAU,CAACiB,KAAK,CAAC,EAAE;UAC3D,IAAI,CAACD,iBAAiB,CAACD,KAAK,EAAE9C,GAAG,EAAE,OAAO,EAAE6C,aAAa,EAAEvE,MAAM,CAAC;QACpE;MACF;IACF;;IAEA;IACA,IAAIyD,UAAU,CAACkB,IAAI,EAAE;MACnB,IAAI,OAAOlB,UAAU,CAACkB,IAAI,KAAK,QAAQ,EAAE;QACvC3E,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,oCAAoC,CAAC;MACrF,CAAC,MAAM;QACL,IAAId,UAAU,CAACkB,IAAI,CAAC7C,QAAQ,KAAKC,SAAS,IAAI,OAAO0B,UAAU,CAACkB,IAAI,CAAC7C,QAAQ,KAAK,SAAS,EAAE;UAC3F9B,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,kCAAkC,CAAC;QACnF;QAEA,IAAId,UAAU,CAACkB,IAAI,CAACC,UAAU,IAAI,OAAOnB,UAAU,CAACkB,IAAI,CAACC,UAAU,KAAK,QAAQ,EAAE;UAChF5E,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,oCAAoC,CAAC;QACrF;MACF;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOE,iBAAiBA,CAACD,KAAK,EAAE9C,GAAG,EAAEH,IAAI,EAAEgD,aAAa,EAAEvE,MAAM,EAAE;IAChE,IAAI,OAAOwE,KAAK,KAAK,QAAQ,EAAE;MAC7BxE,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,IAAIhD,IAAI,eAAeG,GAAG,qBAAqB,CAAC;MAC9F;IACF;IAEA,IAAI8C,KAAK,CAACjD,IAAI,IAAI,OAAOiD,KAAK,CAACjD,IAAI,KAAK,QAAQ,EAAE;MAChDvB,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,IAAIhD,IAAI,eAAeG,GAAG,yBAAyB,CAAC;IACpG;IAEA,IAAI8C,KAAK,CAAC1C,QAAQ,KAAKC,SAAS,IAAI,OAAOyC,KAAK,CAAC1C,QAAQ,KAAK,SAAS,EAAE;MACvE9B,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,IAAIhD,IAAI,eAAeG,GAAG,8BAA8B,CAAC;IACzG;IAEA,IAAI8C,KAAK,CAAC1D,WAAW,IAAI,OAAO0D,KAAK,CAAC1D,WAAW,KAAK,QAAQ,EAAE;MAC9Dd,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,IAAIhD,IAAI,eAAeG,GAAG,gCAAgC,CAAC;IAC3G;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOkC,wBAAwBA,CAACD,QAAQ,EAAEY,aAAa,EAAEvE,MAAM,EAAE;IAC/D,IAAI,OAAO2D,QAAQ,KAAK,QAAQ,EAAE;MAChC3D,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,6BAA6B,CAAC;MAC5E;IACF;IAEA,IAAIZ,QAAQ,CAACkB,WAAW,KAAK9C,SAAS,EAAE;MACtC,IAAI,OAAO4B,QAAQ,CAACkB,WAAW,KAAK,QAAQ,EAAE;QAC5C7E,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,yCAAyC,CAAC;MAC1F,CAAC,MAAM,IAAIZ,QAAQ,CAACkB,WAAW,GAAG,GAAG,IAAIlB,QAAQ,CAACkB,WAAW,IAAI,GAAG,EAAE;QACpE7E,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,iDAAiD,CAAC;MAClG;IACF;IAEA,IAAIZ,QAAQ,CAACmB,QAAQ,IAAI,OAAOnB,QAAQ,CAACmB,QAAQ,KAAK,QAAQ,EAAE;MAC9D9E,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,sCAAsC,CAAC;IACvF;IAEA,IAAIZ,QAAQ,CAACoB,MAAM,IAAI,OAAOpB,QAAQ,CAACoB,MAAM,KAAK,QAAQ,EAAE;MAC1D/E,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,oCAAoC,CAAC;IACrF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOT,0BAA0BA,CAACD,UAAU,EAAEU,aAAa,EAAEvE,MAAM,EAAE;IACnE,IAAI,OAAO6D,UAAU,KAAK,QAAQ,EAAE;MAClC7D,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,+BAA+B,CAAC;MAC9E;IACF;IAEA,IAAI,CAACV,UAAU,CAACtC,IAAI,EAAE;MACpBvB,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,8BAA8B,CAAC;IAC/E,CAAC,MAAM,IAAI,OAAOV,UAAU,CAACtC,IAAI,KAAK,QAAQ,EAAE;MAC9CvB,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,mCAAmC,CAAC;IACpF,CAAC,MAAM,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC/C,QAAQ,CAACqC,UAAU,CAACtC,IAAI,CAAC,EAAE;MAC3EvB,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,8DAA8D,CAAC;IAC/G;IAEA,IAAI,CAACV,UAAU,CAACJ,UAAU,EAAE;MAC1BzD,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,qCAAqC,CAAC;IACtF,CAAC,MAAM,IAAI,OAAOV,UAAU,CAACJ,UAAU,KAAK,QAAQ,EAAE;MACpDzD,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,0CAA0C,CAAC;IAC3F,CAAC,MAAM;MACL;MACA,QAAQV,UAAU,CAACtC,IAAI;QACrB,KAAK,QAAQ;UACX,IAAI,CAACsC,UAAU,CAACJ,UAAU,CAACuB,KAAK,EAAE;YAChChF,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,+CAA+C,CAAC;UAChG;UACA,IAAI,CAACV,UAAU,CAACJ,UAAU,CAACwB,MAAM,EAAE;YACjCjF,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,iDAAiD,CAAC;UAClG;UACA;QACF,KAAK,OAAO;UACV,IAAI,CAACV,UAAU,CAACJ,UAAU,CAACyB,aAAa,EAAE;YACxClF,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,sDAAsD,CAAC;UACvG;UACA,IAAI,CAACV,UAAU,CAACJ,UAAU,CAAC0B,SAAS,EAAE;YACpCnF,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,kDAAkD,CAAC;UACnG;UACA;QACF,KAAK,MAAM;UACT,IAAI,CAACV,UAAU,CAACJ,UAAU,CAAC2B,IAAI,EAAE;YAC/BpF,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,4CAA4C,CAAC;UAC7F;UACA,IAAI,CAACV,UAAU,CAACJ,UAAU,CAAC4B,QAAQ,EAAE;YACnCrF,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,gDAAgD,CAAC;UACjG;UACA;QACF,KAAK,QAAQ;UACX,IAAI,CAACV,UAAU,CAACJ,UAAU,CAAC6B,MAAM,EAAE;YACjCtF,MAAM,CAACG,IAAI,CAAC,qBAAqBoE,aAAa,gDAAgD,CAAC;UACjG;UACA;MACJ;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAO7B,UAAUA,CAAC6C,GAAG,EAAE;IACrB,IAAI;MACF,IAAIC,GAAG,CAACD,GAAG,CAAC;MACZ,OAAO,IAAI;IACb,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAO7E,cAAcA,CAACD,OAAO,EAAE;IAC7B,MAAM+E,WAAW,GAAG,qLAAqL;IACzM,OAAOA,WAAW,CAACC,IAAI,CAAChF,OAAO,CAAC;EAClC;AACF;AAEAiF,MAAM,CAACC,OAAO,GAAGhG,kBAAkB", "ignoreList": []}