#!/usr/bin/env python3
"""
Comphyon Energy Test

This script demonstrates the refined Comphyon calculation based on domain-specific
energies and their gradients.
"""

import numpy as np
import math
import json
import time
import matplotlib.pyplot as plt

# Constants
PI_103 = 3141.59  # π103 scaling factor
NORMALIZATION_CONSTANT = 166000  # Normalization constant (π10³ * scale factor)

class EnergyCalculator:
    """
    Calculates domain-specific energies and their gradients for the Ψ Tensor Core.
    """

    def __init__(self):
        """
        Initialize the Energy Calculator.
        """
        # Initialize history
        self.history = {
            "CSDE": {
                "timestamps": [],
                "energies": []
            },
            "CSFE": {
                "timestamps": [],
                "energies": []
            },
            "CSME": {
                "timestamps": [],
                "energies": []
            }
        }

        # Initialize last update time
        self.last_update_time = time.time()

    def calculate_csde_energy(self, csde_tensor):
        """
        Calculate the domain-specific energy for CSDE.

        E_CSDE = A₁ × D (Risk × Data relevance)
        """
        # Extract components
        G = csde_tensor[0]  # Governance input signal
        D = csde_tensor[1]  # Data relevance signal
        A1 = csde_tensor[2]  # Risk classification or threat vector
        c1 = csde_tensor[3]  # Contextual accuracy or confidence

        # Calculate energy
        E_CSDE = A1 * D

        return E_CSDE

    def calculate_csfe_energy(self, csfe_tensor):
        """
        Calculate the domain-specific energy for CSFE.

        E_CSFE = A₂ × P (Alignment accuracy × Policy relevance)
        """
        # Extract components
        F1 = csfe_tensor[0]  # Framework signal (renamed from R in your description)
        P = csfe_tensor[1]   # Policy relevance (renamed from φ in your description)
        A2 = csfe_tensor[2]  # Alignment accuracy
        c2 = csfe_tensor[3]  # Confidence

        # Calculate energy
        E_CSFE = A2 * P

        return E_CSFE

    def calculate_csme_energy(self, csme_tensor):
        """
        Calculate the domain-specific energy for CSME.

        E_CSME = T × I (Trust × Integrity)
        """
        # Extract components
        T = csme_tensor[0]  # Trust (renamed from B in your description)
        I = csme_tensor[1]  # Integrity (renamed from Γ in your description)
        E = csme_tensor[2]  # Ethical component
        c3 = csme_tensor[3]  # Confidence

        # Calculate energy
        E_CSME = T * I

        return E_CSME

    def calculate_gradient(self, current_energy, engine_type, timestamp=None):
        """
        Calculate the gradient (time derivative) of energy.

        ∇E_x = dE_x/dt ≈ (E_x[t] - E_x[t-1]) / Δt
        """
        # Use current time if timestamp is not provided
        if timestamp is None:
            timestamp = time.time()

        # Get history for the specified engine
        engine_history = self.history[engine_type]

        # Add current energy to history
        engine_history["timestamps"].append(timestamp)
        engine_history["energies"].append(current_energy)

        # Calculate gradient
        if len(engine_history["energies"]) < 2:
            # Not enough history to calculate gradient
            gradient = 0.0
        else:
            # Calculate time difference
            delta_time = engine_history["timestamps"][-1] - engine_history["timestamps"][-2]

            # Ensure delta_time is not zero
            if delta_time < 1e-6:
                delta_time = 1e-6

            # Calculate energy difference
            delta_energy = engine_history["energies"][-1] - engine_history["energies"][-2]

            # Calculate gradient
            gradient = delta_energy / delta_time

        return gradient

    def calculate_comphyon(self, csde_tensor, csfe_tensor, csme_tensor, timestamp=None):
        """
        Calculate the Comphyon value based on domain-specific energies and their gradients.

        Cph = ((∇E_CSDE ∘ ∇E_CSFE) × log(E_CSME)) / 166000
        """
        # Use current time if timestamp is not provided
        if timestamp is None:
            timestamp = time.time()

        # Calculate domain-specific energies
        E_CSDE = self.calculate_csde_energy(csde_tensor)
        E_CSFE = self.calculate_csfe_energy(csfe_tensor)
        E_CSME = self.calculate_csme_energy(csme_tensor)

        # Calculate gradients
        dE_CSDE = self.calculate_gradient(E_CSDE, "CSDE", timestamp)
        dE_CSFE = self.calculate_gradient(E_CSFE, "CSFE", timestamp)
        dE_CSME = self.calculate_gradient(E_CSME, "CSME", timestamp)

        # Calculate Comphyon values using both formulas
        # Avoid log(0) by adding a small epsilon
        epsilon = 1e-5

        # 1. Original Formula (Theoretical Rigor) - "Emergent Intelligence Acceleration"
        # Preserves sign information and interaction effects (Hadamard product)
        # ∇²E_unified = (∇E_CSDE * ∇E_CSFE) * log(E_CSME)
        cph_acceleration = (dE_CSDE * dE_CSFE) * math.log(abs(E_CSME) + epsilon)

        # Apply scaling factor derived from π10³
        cph_acceleration = cph_acceleration * 3142

        # 2. Simplified Formula (Operational Metric) - "Domain Energy Flux"
        # Uses absolute values for more intuitive interpretation
        # Represents the combined velocity of domain energies
        cph_velocity = (abs(dE_CSDE) + abs(dE_CSFE)) * math.log(abs(E_CSME) + epsilon)

        # Apply scaling if needed
        if abs(cph_velocity) < 0.001:
            cph_velocity = cph_velocity * 1000

        # Update last update time
        self.last_update_time = timestamp

        # Create result with both Comphyon metrics
        result = {
            "Comphyon": {
                "acceleration": cph_acceleration,  # Original formula (theoretical rigor)
                "velocity": cph_velocity           # Simplified formula (operational metric)
            },
            "Energies": {
                "CSDE": E_CSDE,
                "CSFE": E_CSFE,
                "CSME": E_CSME
            },
            "Gradients": {
                "CSDE": dE_CSDE,
                "CSFE": dE_CSFE,
                "CSME": dE_CSME
            },
            "Timestamp": timestamp
        }

        return result

def create_sample_data():
    """Create sample data for CSDE, CSFE, and CSME engines."""
    # Sample CSDE data [G, D, A₁, c₁]
    csde_tensor = np.array([
        0.75,  # G: Governance input signal
        0.85,  # D: Data relevance signal
        0.65,  # A₁: Risk classification or threat vector
        0.90   # c₁: Contextual accuracy or confidence
    ])

    # Sample CSFE data [F₁, P, A₂, c₂]
    csfe_tensor = np.array([
        0.65,  # F₁: Framework signal
        0.70,  # P: Policy relevance
        0.80,  # A₂: Alignment accuracy
        0.80   # c₂: Confidence
    ])

    # Sample CSME data [T, I, E, c₃]
    csme_tensor = np.array([
        0.70,  # T: Trust
        0.90,  # I: Integrity
        0.60,  # E: Ethical component
        0.85   # c₃: Confidence
    ])

    return csde_tensor, csfe_tensor, csme_tensor

def create_time_series_data(num_samples=10, time_interval=1.0):
    """Create time series data for testing gradients."""
    time_series = []

    # Base tensors
    base_csde = np.array([0.75, 0.85, 0.65, 0.90])
    base_csfe = np.array([0.65, 0.70, 0.80, 0.80])
    base_csme = np.array([0.70, 0.90, 0.60, 0.85])

    # Create time series with small variations
    for i in range(num_samples):
        # Add some random variation
        csde_variation = np.random.uniform(-0.05, 0.05, 4)
        csfe_variation = np.random.uniform(-0.05, 0.05, 4)
        csme_variation = np.random.uniform(-0.05, 0.05, 4)

        # Add some trend (increasing risk over time)
        trend_factor = i / (num_samples * 2)
        csde_trend = np.array([0.0, 0.0, trend_factor, 0.0])

        # Combine base, variation, and trend
        csde_tensor = np.clip(base_csde + csde_variation + csde_trend, 0.1, 0.99)
        csfe_tensor = np.clip(base_csfe + csfe_variation, 0.1, 0.99)
        csme_tensor = np.clip(base_csme + csme_variation, 0.1, 0.99)

        # Create timestamp
        timestamp = time.time() + i * time_interval

        time_series.append({
            "csde_tensor": csde_tensor,
            "csfe_tensor": csfe_tensor,
            "csme_tensor": csme_tensor,
            "timestamp": timestamp
        })

    return time_series

def plot_results(results):
    """Plot the results of the Comphyon calculation."""
    # Extract data
    timestamps = [result["Timestamp"] for result in results]
    relative_timestamps = [t - timestamps[0] for t in timestamps]

    # Extract both Comphyon metrics
    cph_acceleration = [result["Comphyon"]["acceleration"] for result in results]
    cph_velocity = [result["Comphyon"]["velocity"] for result in results]

    csde_energies = [result["Energies"]["CSDE"] for result in results]
    csfe_energies = [result["Energies"]["CSFE"] for result in results]
    csme_energies = [result["Energies"]["CSME"] for result in results]

    csde_gradients = [result["Gradients"]["CSDE"] for result in results]
    csfe_gradients = [result["Gradients"]["CSFE"] for result in results]
    csme_gradients = [result["Gradients"]["CSME"] for result in results]

    # Create figure with subplots
    fig, axs = plt.subplots(4, 1, figsize=(10, 16))

    # Plot Comphyon Acceleration (Original Formula)
    axs[0].plot(relative_timestamps, cph_acceleration, 'o-', label='Comphyon Acceleration')
    axs[0].set_title('Comphyon Acceleration (Emergent Intelligence Acceleration)')
    axs[0].set_xlabel('Time (s)')
    axs[0].set_ylabel('Cph')
    axs[0].grid(True)
    axs[0].legend()

    # Plot Comphyon Velocity (Simplified Formula)
    axs[1].plot(relative_timestamps, cph_velocity, 'o-', label='Comphyon Velocity')
    axs[1].set_title('Comphyon Velocity (Domain Energy Flux)')
    axs[1].set_xlabel('Time (s)')
    axs[1].set_ylabel('Cph')
    axs[1].grid(True)
    axs[1].legend()

    # Plot energies
    axs[2].plot(relative_timestamps, csde_energies, 'o-', label='E_CSDE')
    axs[2].plot(relative_timestamps, csfe_energies, 'o-', label='E_CSFE')
    axs[2].plot(relative_timestamps, csme_energies, 'o-', label='E_CSME')
    axs[2].set_title('Domain-Specific Energies')
    axs[2].set_xlabel('Time (s)')
    axs[2].set_ylabel('Energy')
    axs[2].grid(True)
    axs[2].legend()

    # Plot gradients
    axs[3].plot(relative_timestamps, csde_gradients, 'o-', label='∇E_CSDE')
    axs[3].plot(relative_timestamps, csfe_gradients, 'o-', label='∇E_CSFE')
    axs[3].plot(relative_timestamps, csme_gradients, 'o-', label='∇E_CSME')
    axs[3].set_title('Energy Gradients')
    axs[3].set_xlabel('Time (s)')
    axs[3].set_ylabel('Gradient (dE/dt)')
    axs[3].grid(True)
    axs[3].legend()

    # Adjust layout
    plt.tight_layout()

    # Save figure
    plt.savefig('comphyon_results.png')
    print("Results plot saved to 'comphyon_results.png'")

def main():
    """Main function."""
    print("=== Refined Comphyon Calculation Test ===")

    try:
        # Initialize Energy Calculator
        energy_calculator = EnergyCalculator()

        # Test with single sample
        print("\n1. Testing with single sample...")
        csde_tensor, csfe_tensor, csme_tensor = create_sample_data()

        print(f"CSDE Tensor: {csde_tensor}")
        print(f"CSFE Tensor: {csfe_tensor}")
        print(f"CSME Tensor: {csme_tensor}")

        # Calculate domain-specific energies
        E_CSDE = energy_calculator.calculate_csde_energy(csde_tensor)
        E_CSFE = energy_calculator.calculate_csfe_energy(csfe_tensor)
        E_CSME = energy_calculator.calculate_csme_energy(csme_tensor)

        print(f"E_CSDE = A₁ × D = {csde_tensor[2]:.2f} × {csde_tensor[1]:.2f} = {E_CSDE:.4f}")
        print(f"E_CSFE = A₂ × P = {csfe_tensor[2]:.2f} × {csfe_tensor[1]:.2f} = {E_CSFE:.4f}")
        print(f"E_CSME = T × I = {csme_tensor[0]:.2f} × {csme_tensor[1]:.2f} = {E_CSME:.4f}")

        # Calculate Comphyon value
        result = energy_calculator.calculate_comphyon(csde_tensor, csfe_tensor, csme_tensor)

        print(f"Initial Comphyon Acceleration: {result['Comphyon']['acceleration']:.6f} Cph")
        print(f"Initial Comphyon Velocity: {result['Comphyon']['velocity']:.6f} Cph")
        print(f"Note: Gradients are zero for the first sample")

        # Test with time series data
        print("\n2. Testing with time series data...")
        time_series = create_time_series_data(num_samples=10, time_interval=1.0)

        results = []
        for i, sample in enumerate(time_series):
            result = energy_calculator.calculate_comphyon(
                sample["csde_tensor"],
                sample["csfe_tensor"],
                sample["csme_tensor"],
                sample["timestamp"]
            )

            results.append(result)

            print(f"Sample {i+1}:")
            print(f"  Timestamp: {result['Timestamp']:.2f}")
            print(f"  Energies: CSDE={result['Energies']['CSDE']:.4f}, CSFE={result['Energies']['CSFE']:.4f}, CSME={result['Energies']['CSME']:.4f}")
            print(f"  Gradients: CSDE={result['Gradients']['CSDE']:.4f}, CSFE={result['Gradients']['CSFE']:.4f}, CSME={result['Gradients']['CSME']:.4f}")
            print(f"  Comphyon Acceleration: {result['Comphyon']['acceleration']:.6f} Cph")
            print(f"  Comphyon Velocity: {result['Comphyon']['velocity']:.6f} Cph")

        # Plot results
        plot_results(results)

        print("\n=== Test Complete ===")

    except Exception as e:
        print(f"\nError during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
