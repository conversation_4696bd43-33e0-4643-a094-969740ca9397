/**
 * CSFE Engine Test
 * 
 * This script tests the CSFE engine with sample data.
 */

const { CSFEEngine } = require('./index');

// Create CSFE Engine instance
const csfeEngine = new CSFEEngine();

// Sample market data
const marketData = {
  price: {
    current: 100,
    moving_average: 95,
    history: [90, 92, 95, 98, 100]
  },
  volume: {
    current: 1000000,
    average: 900000,
    trend: 'increasing'
  },
  liquidity: {
    value: 0.8,
    trend: 'stable'
  },
  volatility: {
    value: 15,
    trend: 'decreasing'
  },
  depth: {
    value: 0.7,
    trend: 'increasing'
  },
  spread: {
    value: 0.5,
    trend: 'decreasing'
  }
};

// Sample economic data
const economicData = {
  gdp: {
    value: 21000,
    growth: 2.5,
    trend: 'increasing'
  },
  inflation: {
    rate: 2.1,
    core: 1.8,
    trend: 'stable'
  },
  unemployment: {
    rate: 3.8,
    trend: 'decreasing'
  },
  interestRates: {
    fed_funds: 0.25,
    ten_year: 1.5,
    trend: 'stable'
  },
  pmi: {
    value: 53.5,
    trend: 'increasing'
  },
  consumerConfidence: {
    value: 110,
    trend: 'increasing'
  },
  buildingPermits: {
    value: 1800000,
    growth: 3.2,
    trend: 'increasing'
  }
};

// Sample sentiment data
const sentimentData = {
  retail: {
    bullishPercentage: 65,
    bearishPercentage: 35,
    trend: 'increasing'
  },
  institutional: {
    bullishPercentage: 55,
    bearishPercentage: 45,
    netPositioning: 10,
    trend: 'stable'
  },
  media: {
    sentiment: 0.6,
    volume: 1000,
    trend: 'increasing'
  },
  social: {
    sentiment: 0.7,
    volume: 5000,
    trend: 'increasing'
  },
  futures: {
    commercialNetPositioning: 15,
    nonCommercialNetPositioning: -5,
    trend: 'increasing'
  }
};

// Calculate CSFE value
console.log('Calculating CSFE value...');
const result = csfeEngine.calculate(marketData, economicData, sentimentData);

// Display result
console.log('\nCSFE Result:');
console.log(`CSFE Value: ${result.csfeValue}`);
console.log(`Performance Factor: ${result.performanceFactor}x`);
console.log(`Calculated At: ${result.calculatedAt}`);

// Display market component
console.log('\nMarket Component:');
console.log(`Technical Indicators: ${result.marketComponent.technicalIndicators}`);
console.log(`Market Structure: ${result.marketComponent.marketStructure}`);
console.log(`Processed Value: ${result.marketComponent.processedValue}`);

// Display economic component
console.log('\nEconomic Component:');
console.log(`Leading Indicators: ${result.economicComponent.leadingIndicators}`);
console.log(`Lagging Indicators: ${result.economicComponent.laggingIndicators}`);
console.log(`Processed Value: ${result.economicComponent.processedValue}`);

// Display sentiment component
console.log('\nSentiment Component:');
console.log(`Retail Sentiment: ${result.sentimentComponent.retailSentiment}`);
console.log(`Institutional Positioning: ${result.sentimentComponent.institutionalPositioning}`);
console.log(`Processed Value: ${result.sentimentComponent.processedValue}`);

// Display tensor product
console.log('\nTensor Product:');
console.log(`Tensor Value: ${result.tensorProduct.tensorValue}`);
console.log(`Normalized Value: ${result.tensorProduct.normalizedValue}`);

// Display fusion result
console.log('\nFusion Result:');
console.log(`Linear Combination: ${result.fusionResult.linearCombination}`);
console.log(`Non-Linear Synergy: ${result.fusionResult.nonLinearSynergy}`);
console.log(`Synergistic Value: ${result.fusionResult.synergisticValue}`);
console.log(`Fusion Value: ${result.fusionResult.fusionValue}`);

// Display financial predictions
console.log('\nFinancial Predictions:');
console.log(`Market Direction: ${result.financialPredictions.marketPredictions.overall.direction}`);
console.log(`Market Strength: ${result.financialPredictions.marketPredictions.overall.strength}`);

// Display asset allocation
console.log('\nAsset Allocation:');
console.log(`Risk Level: ${result.financialPredictions.assetAllocation.riskLevel}`);
console.log('Allocation:');
Object.entries(result.financialPredictions.assetAllocation.allocation).forEach(([asset, percentage]) => {
  console.log(`  ${asset}: ${percentage}%`);
});

// Display risk assessment
console.log('\nRisk Assessment:');
console.log(`Overall Risk: ${result.financialPredictions.riskAssessment.overallRisk}`);
console.log('Risk Factors:');
result.financialPredictions.riskAssessment.riskFactors.forEach(factor => {
  console.log(`  ${factor.name}: ${factor.level} (${factor.impact} impact)`);
});

// Display opportunities
console.log('\nInvestment Opportunities:');
result.financialPredictions.opportunities.forEach(opportunity => {
  console.log(`  ${opportunity.type}: ${opportunity.name}`);
  console.log(`    Rationale: ${opportunity.rationale}`);
  console.log(`    Time Horizon: ${opportunity.timeHorizon}`);
  console.log(`    Expected Return: ${opportunity.expectedReturn}%`);
});

// Display timeline predictions
console.log('\nTimeline Predictions:');
Object.entries(result.financialPredictions.timelinePredictions).forEach(([timeframe, prediction]) => {
  console.log(`  ${timeframe} (${prediction.date}):`);
  console.log(`    CSFE Projection: ${prediction.csfeProjection}`);
  console.log(`    Direction: ${prediction.marketProjection.direction}`);
  console.log(`    Strength: ${prediction.marketProjection.strength}`);
  console.log(`    Confidence: ${prediction.confidence}`);
});

console.log('\nTest completed successfully.');
