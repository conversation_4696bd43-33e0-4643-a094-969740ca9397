/**
 * CHAEONIX COMPHYOLOGICAL FEEDBACK LOOPS ENGINE
 * Learning system for NEPE prophecy calibration and NEEE emotional optimization
 * Adaptive amplification based on prediction accuracy
 */

// FEEDBACK CATEGORIES
const FEEDBACK_TYPES = {
  NEPE_PROPHECY: {
    name: 'Prophecy Calibration',
    description: 'NEPE prediction accuracy feedback',
    adjustment_range: [-0.3, 0.3],
    learning_rate: 0.1
  },
  NEEE_EMOTIONAL: {
    name: 'Emotional Amplification',
    description: 'NEEE sentiment optimization',
    adjustment_range: [-0.2, 0.2],
    learning_rate: 0.15
  },
  NEFC_LIQUIDITY: {
    name: 'Liquidity Prediction',
    description: 'NEFC market timing accuracy',
    adjustment_range: [-0.25, 0.25],
    learning_rate: 0.12
  },
  NERS_RISK: {
    name: 'Risk Assessment',
    description: 'NERS risk model calibration',
    adjustment_range: [-0.2, 0.2],
    learning_rate: 0.08
  }
};

class FeedbackLoopsEngine {
  constructor() {
    this.feedback_history = [];
    this.engine_adjustments = new Map();
    this.learning_metrics = new Map();
    this.calibration_scores = new Map();
    
    // Initialize engine adjustments
    this.initializeEngineAdjustments();
  }

  // INITIALIZE ENGINE ADJUSTMENTS
  initializeEngineAdjustments() {
    const engines = ['NEPE', 'NEEE', 'NEFC', 'NERS', 'NEPI', 'NECO', 'NEBE', 'NECE', 'NERE'];
    
    engines.forEach(engine => {
      this.engine_adjustments.set(engine, {
        amplification_factor: 1.0,
        confidence_modifier: 0.0,
        accuracy_score: 0.75,
        total_predictions: 0,
        correct_predictions: 0,
        last_adjustment: new Date()
      });
      
      this.learning_metrics.set(engine, {
        prediction_accuracy: 0.75,
        improvement_trend: 0.0,
        volatility_score: 0.5,
        consistency_rating: 0.7
      });
    });
  }

  // PROCESS TRADE RESULT FEEDBACK
  processTradeFeedback(trade_result) {
    const feedback = {
      id: `FB_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      trade_id: trade_result.trade_id,
      symbol: trade_result.symbol,
      predicted_move: trade_result.predicted_move,
      actual_move: trade_result.actual_move,
      prediction_accuracy: this.calculatePredictionAccuracy(trade_result),
      engines_involved: trade_result.engines_involved || ['NEPE', 'NEFC', 'NERS'],
      feedback_type: this.determineFeedbackType(trade_result)
    };
    
    this.feedback_history.push(feedback);
    this.applyFeedbackAdjustments(feedback);
    
    // Keep only last 1000 feedback entries
    if (this.feedback_history.length > 1000) {
      this.feedback_history = this.feedback_history.slice(-1000);
    }
    
    return feedback;
  }

  // CALCULATE PREDICTION ACCURACY
  calculatePredictionAccuracy(trade_result) {
    if (!trade_result.predicted_move || !trade_result.actual_move) {
      return 0.5; // Neutral if no prediction data
    }
    
    const predicted = trade_result.predicted_move;
    const actual = trade_result.actual_move;
    
    // Direction accuracy (50% weight)
    const direction_correct = (predicted > 0 && actual > 0) || (predicted < 0 && actual < 0);
    const direction_score = direction_correct ? 1.0 : 0.0;
    
    // Magnitude accuracy (50% weight)
    const magnitude_ratio = Math.min(Math.abs(actual), Math.abs(predicted)) / Math.max(Math.abs(actual), Math.abs(predicted));
    const magnitude_score = magnitude_ratio;
    
    return (direction_score * 0.5) + (magnitude_score * 0.5);
  }

  // DETERMINE FEEDBACK TYPE
  determineFeedbackType(trade_result) {
    // Determine primary feedback type based on trade characteristics
    if (trade_result.prophecy_driven) return 'NEPE_PROPHECY';
    if (trade_result.sentiment_driven) return 'NEEE_EMOTIONAL';
    if (trade_result.liquidity_driven) return 'NEFC_LIQUIDITY';
    if (trade_result.risk_driven) return 'NERS_RISK';
    
    return 'NEPE_PROPHECY'; // Default
  }

  // APPLY FEEDBACK ADJUSTMENTS
  applyFeedbackAdjustments(feedback) {
    feedback.engines_involved.forEach(engine => {
      const current_adj = this.engine_adjustments.get(engine);
      const feedback_type = FEEDBACK_TYPES[feedback.feedback_type];
      
      if (!current_adj || !feedback_type) return;
      
      // Calculate adjustment delta
      const accuracy_delta = feedback.prediction_accuracy - 0.75; // Target 75% accuracy
      const adjustment_delta = accuracy_delta * feedback_type.learning_rate;
      
      // Apply adjustment with bounds
      const new_amplification = Math.max(0.5, Math.min(2.0, 
        current_adj.amplification_factor + adjustment_delta
      ));
      
      // Update engine adjustments
      current_adj.amplification_factor = new_amplification;
      current_adj.total_predictions += 1;
      if (feedback.prediction_accuracy > 0.6) {
        current_adj.correct_predictions += 1;
      }
      current_adj.accuracy_score = current_adj.correct_predictions / current_adj.total_predictions;
      current_adj.last_adjustment = new Date();
      
      // Update learning metrics
      this.updateLearningMetrics(engine, feedback);
      
      console.log(`🔄 ${engine} Adjustment: ${current_adj.amplification_factor.toFixed(3)}x (Accuracy: ${(current_adj.accuracy_score * 100).toFixed(1)}%)`);
    });
  }

  // UPDATE LEARNING METRICS
  updateLearningMetrics(engine, feedback) {
    const metrics = this.learning_metrics.get(engine);
    if (!metrics) return;
    
    // Update prediction accuracy (exponential moving average)
    metrics.prediction_accuracy = (metrics.prediction_accuracy * 0.9) + (feedback.prediction_accuracy * 0.1);
    
    // Calculate improvement trend
    const recent_feedback = this.feedback_history
      .filter(f => f.engines_involved.includes(engine))
      .slice(-10);
    
    if (recent_feedback.length >= 5) {
      const recent_avg = recent_feedback.slice(-5).reduce((sum, f) => sum + f.prediction_accuracy, 0) / 5;
      const older_avg = recent_feedback.slice(0, 5).reduce((sum, f) => sum + f.prediction_accuracy, 0) / 5;
      metrics.improvement_trend = recent_avg - older_avg;
    }
    
    // Update volatility score
    if (recent_feedback.length >= 3) {
      const accuracies = recent_feedback.map(f => f.prediction_accuracy);
      const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
      const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
      metrics.volatility_score = Math.sqrt(variance);
    }
    
    // Update consistency rating
    metrics.consistency_rating = Math.max(0, 1 - metrics.volatility_score);
  }

  // GET ENGINE AMPLIFICATION
  getEngineAmplification(engine) {
    const adjustment = this.engine_adjustments.get(engine);
    return adjustment ? adjustment.amplification_factor : 1.0;
  }

  // GET LEARNING INSIGHTS
  getLearningInsights() {
    const insights = [];
    
    for (const [engine, metrics] of this.learning_metrics.entries()) {
      const adjustment = this.engine_adjustments.get(engine);
      
      let insight_type = 'STABLE';
      let message = `${engine} performing within normal parameters`;
      
      if (metrics.prediction_accuracy > 0.85) {
        insight_type = 'EXCELLENT';
        message = `${engine} showing excellent prediction accuracy (${(metrics.prediction_accuracy * 100).toFixed(1)}%)`;
      } else if (metrics.prediction_accuracy < 0.6) {
        insight_type = 'NEEDS_ATTENTION';
        message = `${engine} accuracy below target (${(metrics.prediction_accuracy * 100).toFixed(1)}%) - adjusting amplification`;
      } else if (metrics.improvement_trend > 0.1) {
        insight_type = 'IMPROVING';
        message = `${engine} showing strong improvement trend (+${(metrics.improvement_trend * 100).toFixed(1)}%)`;
      } else if (metrics.improvement_trend < -0.1) {
        insight_type = 'DECLINING';
        message = `${engine} performance declining (${(metrics.improvement_trend * 100).toFixed(1)}%) - recalibrating`;
      }
      
      insights.push({
        engine: engine,
        type: insight_type,
        message: message,
        accuracy: metrics.prediction_accuracy,
        amplification: adjustment.amplification_factor,
        consistency: metrics.consistency_rating,
        trend: metrics.improvement_trend
      });
    }
    
    return insights.sort((a, b) => b.accuracy - a.accuracy);
  }

  // GET CALIBRATION RECOMMENDATIONS
  getCalibrationRecommendations() {
    const recommendations = [];
    
    for (const [engine, metrics] of this.learning_metrics.entries()) {
      const adjustment = this.engine_adjustments.get(engine);
      
      if (metrics.prediction_accuracy < 0.6) {
        recommendations.push({
          engine: engine,
          priority: 'HIGH',
          action: 'REDUCE_AMPLIFICATION',
          reason: 'Low prediction accuracy',
          suggested_change: -0.2,
          current_amplification: adjustment.amplification_factor
        });
      } else if (metrics.prediction_accuracy > 0.9 && adjustment.amplification_factor < 1.5) {
        recommendations.push({
          engine: engine,
          priority: 'MEDIUM',
          action: 'INCREASE_AMPLIFICATION',
          reason: 'Excellent accuracy, can handle more influence',
          suggested_change: 0.1,
          current_amplification: adjustment.amplification_factor
        });
      } else if (metrics.volatility_score > 0.3) {
        recommendations.push({
          engine: engine,
          priority: 'MEDIUM',
          action: 'STABILIZE',
          reason: 'High prediction volatility',
          suggested_change: 0.0,
          current_amplification: adjustment.amplification_factor
        });
      }
    }
    
    return recommendations.sort((a, b) => {
      const priority_order = { 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
      return priority_order[b.priority] - priority_order[a.priority];
    });
  }

  // GET CURRENT STATUS
  getCurrentStatus() {
    const total_feedback = this.feedback_history.length;
    const recent_feedback = this.feedback_history.slice(-50);
    const avg_accuracy = recent_feedback.length > 0 ? 
      recent_feedback.reduce((sum, f) => sum + f.prediction_accuracy, 0) / recent_feedback.length : 0.75;
    
    return {
      total_feedback_entries: total_feedback,
      recent_average_accuracy: avg_accuracy,
      engine_adjustments: Object.fromEntries(this.engine_adjustments),
      learning_metrics: Object.fromEntries(this.learning_metrics),
      learning_insights: this.getLearningInsights(),
      calibration_recommendations: this.getCalibrationRecommendations(),
      last_feedback: this.feedback_history[this.feedback_history.length - 1] || null
    };
  }
}

// Export singleton instance
const feedbackLoopsEngine = new FeedbackLoopsEngine();

export default function handler(req, res) {
  if (req.method === 'GET') {
    const status = feedbackLoopsEngine.getCurrentStatus();
    
    res.status(200).json({
      success: true,
      feedback_loops_engine: 'CHAEONIX Comphyological Learning System',
      current_status: status,
      feedback_types: FEEDBACK_TYPES,
      timestamp: new Date().toISOString()
    });
    
  } else if (req.method === 'POST') {
    const { action, trade_result } = req.body;
    
    if (action === 'PROCESS_FEEDBACK') {
      const feedback = feedbackLoopsEngine.processTradeFeedback(trade_result);
      res.status(200).json({
        success: true,
        message: 'Trade feedback processed',
        feedback: feedback
      });
      
    } else if (action === 'GET_AMPLIFICATION') {
      const { engine } = req.body;
      const amplification = feedbackLoopsEngine.getEngineAmplification(engine);
      res.status(200).json({
        success: true,
        engine: engine,
        amplification_factor: amplification
      });
      
    } else {
      res.status(400).json({ error: 'Invalid action' });
    }
    
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
