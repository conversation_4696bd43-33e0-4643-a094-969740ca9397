/**
 * CSDE (Cyber-Safety Dominance Equation) Engine
 *
 * This module exports all components of the CSDE engine.
 *
 * The Trinity CSDE formula is: CSDE_Trinity = πG + ϕD + (ℏ + c^-1)R
 *
 * Where:
 * - G = Governance logic (π-aligned audits & policies) - Father component
 * - D = Detection engine (ϕ-weighted fusion of threat factors) - Son component
 * - R = Response logic (entropy-restrained, speed-limited to c) - Spirit component
 * - π = Pi constant (3.14159...)
 * - ϕ = Golden ratio (1.618...)
 * - ℏ = Plan<PERSON>'s constant
 * - c = Speed of light constant
 *
 * With 18/82 principle applied:
 * - πG = (0.18 × Policy Design) + (0.82 × Compliance Enforcement)
 * - ϕD = (0.18 × Baseline Signals) + (0.82 × Threat Weight)
 * - (ℏ + c^-1)R = (0.18 × Reaction Time) + (0.82 × Mitigation Surface)
 *
 * UUFT Data Quality Framework:
 * - DQFramework = (S ⊗ V ⊕ C) × π10³
 * - S = Source data characteristics (completeness, timeliness, provenance)
 * - V = Validation metrics (consistency, accuracy, precision)
 * - C = Contextual relevance (domain appropriateness, application fit)
 *
 * Enhanced CSDE now includes:
 * - Entropic GRC Control System for dynamic compliance controls
 * - Human-System Coherence Interface for UI safety optimization
 * - Cross-Domain Entropy Bridge for unified risk scoring
 */

// Original CSDE components
const CSDEEngine = require('./core/csde_engine');
const TensorOperator = require('./tensor/tensor_operator');
const FusionOperator = require('./tensor/fusion_operator');
const CircularTrustTopology = require('./circular_trust/circular_trust_topology');
const NovaFlowXEngine = require('./novaflowx/novaflowx_engine');

// Trinity CSDE components
const TrinityCSDEEngine = require('./trinity/trinity_csde_engine');
const TrinityCSDE1882Engine = require('./trinity/trinity_csde_1882_engine');
const TrinityCSDE1882DQEngine = require('./trinity/trinity_csde_1882_dq_engine');
const AdaptiveTrinityCSDE = require('./trinity/adaptive_trinity_csde_engine');

// Enhanced CSDE components
const EntropicGRCControlSystem = require('./entropic_grc/entropic_grc_control_system');
const HumanSystemCoherenceInterface = require('./human_system/human_system_coherence_interface');
const CrossDomainEntropyBridge = require('./cross_domain/cross_domain_entropy_bridge');

/**
 * Create a fully configured enhanced CSDE system
 * @param {Object} options - Configuration options
 * @returns {Object} - Enhanced CSDE system components
 */
function createEnhancedCSDESystem(options = {}) {
  // Create core components
  const csdeEngine = new CSDEEngine(options.csdeEngineOptions);
  const novaFlowXEngine = new NovaFlowXEngine(options.novaFlowXEngineOptions);

  // Create enhanced components
  const entropicGRCControlSystem = new EntropicGRCControlSystem(options.entropicGRCControlSystemOptions);
  const humanSystemCoherenceInterface = new HumanSystemCoherenceInterface(options.humanSystemCoherenceInterfaceOptions);
  const crossDomainEntropyBridge = new CrossDomainEntropyBridge(options.crossDomainEntropyBridgeOptions);

  return {
    csdeEngine,
    novaFlowXEngine,
    entropicGRCControlSystem,
    humanSystemCoherenceInterface,
    crossDomainEntropyBridge
  };
}

module.exports = {
  // Original CSDE components
  CSDEEngine,
  TensorOperator,
  FusionOperator,
  CircularTrustTopology,
  NovaFlowXEngine,

  // Trinity CSDE components
  TrinityCSDEEngine,
  TrinityCSDE1882Engine,
  TrinityCSDE1882DQEngine,
  AdaptiveTrinityCSDE,

  // Enhanced CSDE components
  EntropicGRCControlSystem,
  HumanSystemCoherenceInterface,
  CrossDomainEntropyBridge,

  // Factory function
  createEnhancedCSDESystem
};
