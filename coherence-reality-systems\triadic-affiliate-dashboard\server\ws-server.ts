import * as WebSocket from 'ws';
import { Conversion, Product, Network } from '../src/types/websocket';

const wss = new WebSocket.Server({ port: 3001 });

interface ClientData {
  ws: WebSocket;
  userId: string;
}

const clients: Map<string, ClientData> = new Map();

wss.on('connection', (ws: WebSocket, request: any) => {
  const userId = request.url.split('?')[1]?.split('=')[1];
  
  if (!userId) {
    ws.close();
    return;
  }

  clients.set(userId, { ws, userId });
  
  console.log(`Client connected: ${userId}`);

  ws.on('message', (message: string) => {
    try {
      const data = JSON.parse(message);
      
      // Handle incoming messages
      switch (data.type) {
        case 'conversion':
          broadcastConversion(data.data as Conversion);
          break;
        case 'product':
          broadcastProduct(data.data as Product);
          break;
        case 'network':
          broadcastNetwork(data.data as Network);
          break;
      }
    } catch (error) {
      console.error('Error processing message:', error);
    }
  });

  ws.on('close', () => {
    clients.delete(userId);
    console.log(`Client disconnected: ${userId}`);
  });
});

function broadcastConversion(conversion: Conversion) {
  clients.forEach(({ ws }) => {
    ws.send(JSON.stringify({
      type: 'conversion',
      data: conversion
    }));
  });
}

function broadcastProduct(product: Product) {
  clients.forEach(({ ws }) => {
    ws.send(JSON.stringify({
      type: 'product',
      data: product
    }));
  });
}

function broadcastNetwork(network: Network) {
  clients.forEach(({ ws }) => {
    ws.send(JSON.stringify({
      type: 'network',
      data: network
    }));
  });
}

console.log('WebSocket server started on port 3001');
