/**
 * NovaFuse API Superstore - API Gateway Configuration
 *
 * This file contains the configuration for the API Gateway.
 */

module.exports = {
  // Server configuration
  server: {
    port: process.env.PORT || 3000,
    env: process.env.NODE_ENV || 'development'
  },

  // Service URLs
  services: {
    novaconnect: {
      url: process.env.NOVACONNECT_URL || 'http://localhost:3001',
      path: '/api/novaconnect'
    },
    privacyManagement: {
      url: process.env.PRIVACY_MANAGEMENT_URL || 'http://localhost:3002',
      path: '/api/privacy/management'
    },
    regulatoryCompliance: {
      url: process.env.REGULATORY_COMPLIANCE_URL || 'http://localhost:3003',
      path: '/api/compliance'
    },
    securityAssessment: {
      url: process.env.SECURITY_ASSESSMENT_URL || 'http://localhost:3004',
      path: '/api/security/assessment'
    },
    controlTesting: {
      url: process.env.CONTROL_TESTING_URL || 'http://localhost:3005',
      path: '/api/control/testing'
    },
    esg: {
      url: process.env.ESG_URL || 'http://localhost:3006',
      path: '/api/esg'
    },
    complianceAutomation: {
      url: process.env.COMPLIANCE_AUTOMATION_URL || 'http://localhost:3007',
      path: '/api/compliance/automation'
    }
  },

  // Rate limiting configuration
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: process.env.RATE_LIMIT_MAX || 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again after 15 minutes'
  },

  // CORS configuration
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
  },

  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json',
    directory: process.env.LOG_DIRECTORY || 'logs'
  },

  // Authentication configuration
  auth: {
    jwtSecret: process.env.JWT_SECRET || 'novafuse-api-superstore-secret',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1h',
    apiKeyHeader: process.env.API_KEY_HEADER || 'X-API-Key'
  }
};
