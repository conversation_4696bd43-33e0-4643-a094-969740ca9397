/**
 * Tabs Components
 * 
 * These components provide a consistent tabbed interface across the application.
 */

import React from 'react';

interface TabsProps {
  value: string;
  onValueChange: (value: string) => void;
  className?: string;
  children: React.ReactNode;
}

export const Tabs: React.FC<TabsProps> = ({
  value,
  onValueChange,
  className = '',
  children,
}) => {
  return (
    <div className={`space-y-4 ${className}`} data-value={value}>
      {children}
    </div>
  );
};

interface TabsListProps {
  className?: string;
  children: React.ReactNode;
}

export const TabsList: React.FC<TabsListProps> = ({
  className = '',
  children,
}) => {
  return (
    <div className={`flex space-x-1 border-b border-gray-200 ${className}`}>
      {children}
    </div>
  );
};

interface TabsTriggerProps {
  value: string;
  className?: string;
  children: React.ReactNode;
}

export const TabsTrigger: React.FC<TabsTriggerProps> = ({
  value,
  className = '',
  children,
}) => {
  const tabs = React.useContext(TabsContext);
  
  if (!tabs) {
    throw new Error('TabsTrigger must be used within a Tabs component');
  }
  
  const isActive = tabs.value === value;
  
  return (
    <button
      className={`px-4 py-2 text-sm font-medium border-b-2 -mb-px ${
        isActive
          ? 'border-blue-600 text-blue-600'
          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
      } ${className}`}
      onClick={() => tabs.onValueChange(value)}
      data-state={isActive ? 'active' : 'inactive'}
    >
      {children}
    </button>
  );
};

interface TabsContentProps {
  value: string;
  className?: string;
  children: React.ReactNode;
}

export const TabsContent: React.FC<TabsContentProps> = ({
  value,
  className = '',
  children,
}) => {
  const tabs = React.useContext(TabsContext);
  
  if (!tabs) {
    throw new Error('TabsContent must be used within a Tabs component');
  }
  
  const isActive = tabs.value === value;
  
  if (!isActive) {
    return null;
  }
  
  return (
    <div
      className={`mt-4 ${className}`}
      data-state={isActive ? 'active' : 'inactive'}
    >
      {children}
    </div>
  );
};

// Create context for tabs
interface TabsContextType {
  value: string;
  onValueChange: (value: string) => void;
}

const TabsContext = React.createContext<TabsContextType | null>(null);

// Wrap the Tabs component to provide context
const TabsWithContext: React.FC<TabsProps> = (props) => {
  return (
    <TabsContext.Provider value={{ value: props.value, onValueChange: props.onValueChange }}>
      <Tabs {...props} />
    </TabsContext.Provider>
  );
};

export { TabsWithContext as Tabs };

export default {
  Tabs: TabsWithContext,
  TabsList,
  TabsTrigger,
  TabsContent,
};
