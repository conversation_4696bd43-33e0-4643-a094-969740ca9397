<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detailed Data Flow Diagram</title>
    <style>

        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }

        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 800px;
            height: 70px; /* Extended height for adequate space */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: visible; /* Prevents content from spilling out */
        }

        /* Ensure Universal Pattern Elements stay within main container */
        .universal-pattern-elements {
            position: relative;
            width: 650px;
            margin-top: 450px; /* Position it near the bottom of the main container */
            margin-left: 50px; /* Center it within the main container */
            z-index: 1;
        }

        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: visible; /* Allow content to be fully visible */
            min-height: 100px; /* Minimum height */
            min-width: 200px; /* Minimum width */
        }

        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Prevents content from spilling out */
            text-overflow: ellipsis; /* Shows ellipsis for overflowing text */
        }

        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically */
            align-items: center;
            text-align: center;
            padding: 5px; /* Reduced padding */
            overflow: visible; /* Allow content to be fully visible */
            border-top-left-radius: 0; /* Flat corner for number */
            min-height: 50px; /* Minimum height */
            min-width: 100px; /* Minimum width */
        }

        /* Component Number Styles - Integrated into corner */

        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 0 0 6px 0; /* Rounded on bottom-right corner only */
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px; /* Smaller font */
            font-weight: bold;
            z-index: 2;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }

        .component-label {
            font-weight: bold;
            margin-top: 10px; /* Reduced margin */
            margin-bottom: 2px; /* Reduced margin */
            font-size: 12px; /* Smaller font */
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }

        .component-content {
            font-size: 10px; /* Smaller font */
            text-align: center;
            width: 100%;
            padding: 0;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            line-height: 1.2; /* Tighter line spacing */
        }

        /* Arrow Styles */
        .arrow-line {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            z-index: 0;
        }

        .arrow-head {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 0;
        }

        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }

        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }

        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }

        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }

        /* SVG Styles */
        svg {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 0;
        }

        /* Equation Styles */
        .equation {
            font-weight: bold;
            font-size: 14px;
            margin-top: 5px;
            text-align: center;
        }
    
    </style>
</head>
<body>
    <h1>FIG. 6: Detailed Data Flow Diagram (Cross-Module Processing)</h1>

    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 580px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">CROSS-MODULE DATA FLOW: CYBER-SAFETY IMPLEMENTATION</div>
        </div>

        <!-- Data Ingestion -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 50px; top: 100px; width: 150px; height: 100px;">
            <div class="component-number-inside">801</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Data Ingestion</div>
            <div style="font-size: 12px; text-align: center;">
                Multi-source Input Collection
            </div>
        </div>

        <!-- NovaCore -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 300px; top: 100px; width: 150px; height: 100px;">
            <div class="component-number-inside">802</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">NovaCore (UUFT)</div>
            <div style="font-size: 12px; text-align: center;">
                (A⊗B⊕C)×π10³
            </div>
        </div>

        <!-- NovaShield -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 550px; top: 100px; width: 150px; height: 100px;">
            <div class="component-number-inside">803</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">NovaShield (Trinity)</div>
            <div style="font-size: 12px; text-align: center;">
                Threat Pattern Detection
            </div>
        </div>

        <!-- NovaTrack -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 550px; top: 230px; width: 150px; height: 100px;">
            <div class="component-number-inside">804</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">NovaTrack (Data Purity)</div>
            <div style="font-size: 12px; text-align: center;">
                Compliance Verification
            </div>
        </div>

        <!-- NovaLearn -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 300px; top: 230px; width: 150px; height: 100px;">
            <div class="component-number-inside">805</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">NovaLearn (Adaptive)</div>
            <div style="font-size: 12px; text-align: center;">
                Pattern Optimization
            </div>
        </div>

        <!-- NovaFlowX -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 50px; top: 230px; width: 150px; height: 100px;">
            <div class="component-number-inside">806</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">NovaFlowX (Automation)</div>
            <div style="font-size: 12px; text-align: center;">
                Workflow Orchestration
            </div>
        </div>

        <!-- NovaView -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 50px; top: 360px; width: 150px; height: 100px;">
            <div class="component-number-inside">807</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">NovaView (Visualization)</div>
            <div style="font-size: 12px; text-align: center;">
                Interactive Dashboards
            </div>
        </div>

        <!-- NovaThink -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 300px; top: 360px; width: 150px; height: 100px;">
            <div class="component-number-inside">808</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">NovaThink (Intelligence)</div>
            <div style="font-size: 12px; text-align: center;">
                Decision Support
            </div>
        </div>

        <!-- NovaConnect -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 550px; top: 360px; width: 150px; height: 100px;">
            <div class="component-number-inside">809</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">NovaConnect (Integration)</div>
            <div style="font-size: 12px; text-align: center;">
                External System Interface
            </div>
        </div>

        <!-- Action Layer -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 300px; top: 490px; width: 150px; height: 100px;">
            <div class="component-number-inside">810</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Automated Response</div>
            <div style="font-size: 12px; text-align: center;">
                Cyber-Safety Actions
            </div>
        </div>

        <!-- Arrows -->
        <!-- Data Ingestion to NovaCore -->
        <div class="arrow-line" style="left: 200px; top: 140px; width: 100px; height: 580px;"></div>
        <div class="arrow-head" style="left: 300px; top: 136px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #555555;"></div>
        <div class="data-label" style="left: 220px; top: 120px;">Raw Data</div>

        <!-- NovaCore to NovaShield -->
        <div class="arrow-line" style="left: 450px; top: 140px; width: 100px; height: 580px;"></div>
        <div class="arrow-head" style="left: 550px; top: 136px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #555555;"></div>
        <div class="data-label" style="left: 470px; top: 120px;">Tensor Product</div>

        <!-- NovaShield to NovaTrack -->
        <div class="arrow-line" style="left: 625px; top: 180px; width: 2px; height: 580px;"></div>
        <div class="arrow-head" style="left: 621px; top: 230px; border-width: 8px 4px 0 4px; border-color: #555555 transparent transparent transparent;"></div>
        <div class="data-label" style="left: 635px; top: 200px;">Threat Data</div>

        <!-- NovaTrack to NovaLearn -->
        <div class="arrow-line" style="left: 550px; top: 270px; width: 100px; height: 580px;"></div>
        <div class="arrow-head" style="left: 550px; top: 266px; border-width: 4px 8px 4px 0; border-color: transparent #555555 transparent transparent;"></div>
        <div class="data-label" style="left: 470px; top: 250px;">Compliance Data</div>

        <!-- NovaLearn to NovaFlowX -->
        <div class="arrow-line" style="left: 300px; top: 270px; width: 100px; height: 580px;"></div>
        <div class="arrow-head" style="left: 300px; top: 266px; border-width: 4px 8px 4px 0; border-color: transparent #555555 transparent transparent;"></div>
        <div class="data-label" style="left: 220px; top: 250px;">Optimized Patterns</div>

        <!-- NovaFlowX to NovaView -->
        <div class="arrow-line" style="left: 125px; top: 310px; width: 2px; height: 580px;"></div>
        <div class="arrow-head" style="left: 121px; top: 360px; border-width: 8px 4px 0 4px; border-color: #555555 transparent transparent transparent;"></div>
        <div class="data-label" style="left: 135px; top: 330px;">Workflow Data</div>

        <!-- NovaView to NovaThink -->
        <div class="arrow-line" style="left: 200px; top: 400px; width: 100px; height: 580px;"></div>
        <div class="arrow-head" style="left: 300px; top: 396px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #555555;"></div>
        <div class="data-label" style="left: 220px; top: 380px;">Visualization Data</div>

        <!-- NovaThink to NovaConnect -->
        <div class="arrow-line" style="left: 450px; top: 400px; width: 100px; height: 580px;"></div>
        <div class="arrow-head" style="left: 550px; top: 396px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #555555;"></div>
        <div class="data-label" style="left: 470px; top: 380px;">Decision Data</div>

        <!-- NovaConnect to Action -->
        <div class="arrow-line" style="left: 550px; top: 400px; width: 2px; height: 580px; transform: translate(75px, 0) rotate(45deg); transform-origin: top left;"></div>
        <div class="arrow-head" style="left: 375px; top: 490px; border-width: 8px 4px 0 4px; border-color: #555555 transparent transparent transparent;"></div>
        <div class="data-label" style="left: 470px; top: 450px;">Integration Data</div>

        <!-- NovaThink to Action -->
        <div class="arrow-line" style="left: 375px; top: 440px; width: 2px; height: 580px;"></div>
        <div class="arrow-head" style="left: 371px; top: 490px; border-width: 8px 4px 0 4px; border-color: #555555 transparent transparent transparent;"></div>
        <div class="data-label" style="left: 385px; top: 460px;">Action Commands</div>

        <!-- Legend -->
        <div class="legend" style="position: absolute; right: 10px; bottom: 10px; background-color: white; border: 1px solid #ddd; border-radius: 4px; padding: 5px; z-index: 10; width: 200px;">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Processing Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff; border: 1px dashed #333;"></div>
                <div>Data Flow</div>
            </div>
        </div>

        <div class="inventor-label" style="position: absolute; left: 10px; bottom: 10px; font-size: 12px; font-style: italic; color: #333;">Inventor: David Nigel Irvin</div>
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
</div>
</body>
</html>





