<!DOCTYPE html>
<html>
<head>
    <title>Batch Mermaid to SVG Converter</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .diagram { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .controls { margin: 20px 0; }
        button { padding: 8px 16px; margin-right: 10px; cursor: pointer; }
        #status { margin-top: 20px; padding: 10px; border: 1px solid #ccc; min-height: 100px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Batch Mermaid to SVG Converter</h1>
        <div class="controls">
            <button id="startConversion">Start Conversion</button>
            <button id="downloadAll">Download All SVGs</button>
        </div>
        <div id="diagrams"></div>
        <div id="status">Ready to convert Mermaid diagrams...</div>
    </div>

    <script>
        // Mermaid configuration
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'Arial, sans-serif'
        });

        // List of Mermaid files to convert
        const mermaidFiles = [
            'alignment_architecture.mmd',
            'consciousness_threshold.mmd',
            'cross_module_data_processing_pipeline.mmd',
            'dark_field_classification.mmd',
            'finite_universe_paradigm_visualization.mmd',
            'finite_universe_principle.mmd',
            'healthcare_implementation.mmd',
            'nova_components.mmd',
            'principle_18_82.mmd',
            'protein_folding.mmd',
            'three_body_problem_reframing.mmd',
            'uuft_core_architecture.mmd'
        ];

        const diagrams = [];
        const basePath = '../patent_drawings/mermaid_diagrams/';
        const statusDiv = document.getElementById('status');
        let processedCount = 0;

        // Update status
        function updateStatus(message, isError = false) {
            const p = document.createElement('p');
            p.className = isError ? 'error' : 'success';
            p.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            statusDiv.prepend(p);
            console.log(message);
        }

        // Load Mermaid file
        async function loadMermaidFile(filename) {
            try {
                const response = await fetch(`${basePath}${filename}`);
                if (!response.ok) throw new Error(`Failed to load ${filename}`);
                return await response.text();
            } catch (error) {
                updateStatus(`Error loading ${filename}: ${error.message}`, true);
                return null;
            }
        }

        // Render Mermaid diagram
        async function renderMermaid(containerId, mermaidCode) {
            try {
                const { svg } = await mermaid.render(containerId, mermaidCode);
                return svg;
            } catch (error) {
                updateStatus(`Error rendering diagram: ${error.message}`, true);
                return null;
            }
        }

        // Process all diagrams
        async function processDiagrams() {
            statusDiv.innerHTML = '';
            diagrams.length = 0;
            processedCount = 0;
            
            updateStatus('Starting conversion process...');
            
            for (const filename of mermaidFiles) {
                updateStatus(`Processing ${filename}...`);
                
                const mermaidCode = await loadMermaidFile(filename);
                if (!mermaidCode) continue;
                
                const containerId = `diagram-${processedCount}`;
                const svg = await renderMermaid(containerId, mermaidCode);
                
                if (svg) {
                    diagrams.push({
                        filename: filename.replace(/\.mmd$/, '.svg'),
                        svg: svg
                    });
                    updateStatus(`✓ Successfully processed ${filename}`);
                }
                
                processedCount++;
            }
            
            updateStatus(`\nConversion complete! Processed ${diagrams.length} of ${mermaidFiles.length} files.`);
            
            if (diagrams.length > 0) {
                updateStatus('Click "Download All SVGs" to save all diagrams.');
            }
        }

        // Download all SVGs
        function downloadAllSVGs() {
            if (diagrams.length === 0) {
                updateStatus('No diagrams to download. Please run the conversion first.', true);
                return;
            }
            
            updateStatus('Preparing downloads...');
            
            diagrams.forEach(({ filename, svg }) => {
                const blob = new Blob([svg], { type: 'image/svg+xml' });
                saveAs(blob, filename);
                updateStatus(`Downloaded ${filename}`);
            });
            
            updateStatus('All downloads started! Check your downloads folder.');
        }

        // Event listeners
        document.getElementById('startConversion').addEventListener('click', processDiagrams);
        document.getElementById('downloadAll').addEventListener('click', downloadAllSVGs);
    </script>
</body>
</html>
