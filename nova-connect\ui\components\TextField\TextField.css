/**
 * TextField Component Styles
 */

@import '../../design-system/variables.css';

.nova-text-field {
  display: inline-flex;
  flex-direction: column;
  margin-bottom: var(--nova-spacing-md);
}

.nova-text-field--full-width {
  display: flex;
  width: 100%;
}

.nova-text-field__label {
  display: block;
  margin-bottom: var(--nova-spacing-xs);
  font-family: var(--nova-font-family);
  font-size: var(--nova-font-size-body-2);
  font-weight: var(--nova-font-weight-medium);
  color: var(--nova-gray-800);
}

.nova-text-field__required {
  margin-left: var(--nova-spacing-xs);
  color: var(--nova-danger);
}

.nova-text-field__input {
  display: block;
  width: 100%;
  height: var(--nova-input-height);
  padding: var(--nova-input-padding-y) var(--nova-input-padding-x);
  font-family: var(--nova-font-family);
  font-size: var(--nova-font-size-body-1);
  font-weight: var(--nova-font-weight-regular);
  line-height: var(--nova-line-height-body);
  color: var(--nova-gray-900);
  background-color: var(--nova-white);
  background-clip: padding-box;
  border: var(--nova-input-border-width) solid var(--nova-input-border-color);
  border-radius: var(--nova-input-border-radius);
  transition: border-color var(--nova-transition-normal) var(--nova-transition-timing-function),
              box-shadow var(--nova-transition-normal) var(--nova-transition-timing-function);
}

.nova-text-field__input:focus {
  border-color: var(--nova-input-focus-border-color);
  outline: 0;
  box-shadow: var(--nova-input-focus-box-shadow);
}

.nova-text-field__input::placeholder {
  color: var(--nova-gray-500);
  opacity: 1;
}

.nova-text-field__input:disabled,
.nova-text-field__input[readonly] {
  background-color: var(--nova-gray-200);
  opacity: 1;
}

.nova-text-field__helper-text {
  margin-top: var(--nova-spacing-xs);
  font-family: var(--nova-font-family);
  font-size: var(--nova-font-size-caption);
  color: var(--nova-gray-600);
}

.nova-text-field__helper-text--error {
  color: var(--nova-danger);
}

.nova-text-field--error .nova-text-field__input {
  border-color: var(--nova-danger);
}

.nova-text-field--error .nova-text-field__input:focus {
  box-shadow: 0 0 0 0.2rem rgba(var(--nova-danger-rgb), 0.25);
}

.nova-text-field--disabled .nova-text-field__label {
  color: var(--nova-gray-600);
}

/* Dark mode */
[data-theme="dark"] .nova-text-field__label {
  color: var(--nova-dark-text-secondary);
}

[data-theme="dark"] .nova-text-field__input {
  color: var(--nova-dark-text-primary);
  background-color: var(--nova-dark-surface);
  border-color: var(--nova-dark-border);
}

[data-theme="dark"] .nova-text-field__input:focus {
  border-color: var(--nova-primary);
}

[data-theme="dark"] .nova-text-field__input::placeholder {
  color: var(--nova-dark-text-disabled);
}

[data-theme="dark"] .nova-text-field__input:disabled,
[data-theme="dark"] .nova-text-field__input[readonly] {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .nova-text-field__helper-text {
  color: var(--nova-dark-text-secondary);
}
