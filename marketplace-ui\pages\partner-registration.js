import { useState } from "react";
import Head from "next/head";
import { useRouter } from "next/router";

export default function PartnerRegistration() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    companyName: "",
    website: "",
    contactName: "",
    email: "",
    phone: "",
    category: "",
    description: "",
    tier: "professional",
    acceptTerms: false
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [step, setStep] = useState(1);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value
    });
    
    // Clear error when field is changed
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (step === 1) {
      if (!formData.companyName) newErrors.companyName = "Company name is required";
      if (!formData.website) newErrors.website = "Website is required";
      if (!formData.contactName) newErrors.contactName = "Contact name is required";
      if (!formData.email) newErrors.email = "Email is required";
      if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = "Email is invalid";
      if (!formData.phone) newErrors.phone = "Phone number is required";
    } else if (step === 2) {
      if (!formData.category) newErrors.category = "Category is required";
      if (!formData.description) newErrors.description = "Description is required";
      if (formData.description && formData.description.length < 50) newErrors.description = "Description must be at least 50 characters";
    } else if (step === 3) {
      if (!formData.acceptTerms) newErrors.acceptTerms = "You must accept the terms and conditions";
    }
    
    return newErrors;
  };

  const handleNextStep = () => {
    const newErrors = validateForm();
    if (Object.keys(newErrors).length === 0) {
      setStep(step + 1);
    } else {
      setErrors(newErrors);
    }
  };

  const handlePrevStep = () => {
    setStep(step - 1);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const newErrors = validateForm();
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    setIsSubmitting(true);
    
    // In a real implementation, this would submit to an API
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Redirect to success page
      router.push("/partner-registration-success");
    } catch (error) {
      console.error("Error submitting form:", error);
      setErrors({
        submit: "There was an error submitting your application. Please try again."
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const tiers = [
    {
      id: "founding",
      name: "Founding Partner",
      description: "90% revenue share, featured placement, co-marketing opportunities",
      revShare: "90%",
      featured: true,
      support: "24/7 dedicated support",
      requirements: "Integration within 30 days, joint marketing"
    },
    {
      id: "premium",
      name: "Premium Partner",
      description: "85% revenue share, enhanced support, secondary placement",
      revShare: "85%",
      featured: false,
      support: "Priority support",
      requirements: "Integration within 60 days"
    },
    {
      id: "professional",
      name: "Professional Partner",
      description: "80% revenue share, standard support, standard placement",
      revShare: "80%",
      featured: false,
      support: "Standard support",
      requirements: "Self-service integration"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>NovaFuse Partner Registration</title>
        <meta name="description" content="Register as a NovaFuse API Superstore partner" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8 flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">Partner Registration</h1>
          <button 
            onClick={() => router.push("/")}
            className="border border-blue-600 text-blue-600 px-4 py-2 rounded hover:bg-blue-50"
          >
            Back to Marketplace
          </button>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                step >= 1 ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"
              }`}>
                1
              </div>
              <div className={`ml-2 text-sm font-medium ${
                step >= 1 ? "text-blue-600" : "text-gray-500"
              }`}>
                Company Information
              </div>
            </div>
            <div className={`flex-1 h-1 mx-4 ${step >= 2 ? "bg-blue-600" : "bg-gray-200"}`}></div>
            <div className="flex items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                step >= 2 ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"
              }`}>
                2
              </div>
              <div className={`ml-2 text-sm font-medium ${
                step >= 2 ? "text-blue-600" : "text-gray-500"
              }`}>
                Integration Details
              </div>
            </div>
            <div className={`flex-1 h-1 mx-4 ${step >= 3 ? "bg-blue-600" : "bg-gray-200"}`}></div>
            <div className="flex items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                step >= 3 ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"
              }`}>
                3
              </div>
              <div className={`ml-2 text-sm font-medium ${
                step >= 3 ? "text-blue-600" : "text-gray-500"
              }`}>
                Partnership Terms
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-6 mb-8">
          <form onSubmit={handleSubmit}>
            {/* Step 1: Company Information */}
            {step === 1 && (
              <div>
                <h2 className="text-xl font-semibold mb-6">Company Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label className="block text-gray-700 mb-2" htmlFor="companyName">
                      Company Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="companyName"
                      name="companyName"
                      value={formData.companyName}
                      onChange={handleChange}
                      className={`w-full p-2 border rounded ${
                        errors.companyName ? "border-red-500" : "border-gray-300"
                      }`}
                      placeholder="Your company name"
                    />
                    {errors.companyName && (
                      <p className="text-red-500 text-sm mt-1">{errors.companyName}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-gray-700 mb-2" htmlFor="website">
                      Website <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="url"
                      id="website"
                      name="website"
                      value={formData.website}
                      onChange={handleChange}
                      className={`w-full p-2 border rounded ${
                        errors.website ? "border-red-500" : "border-gray-300"
                      }`}
                      placeholder="https://example.com"
                    />
                    {errors.website && (
                      <p className="text-red-500 text-sm mt-1">{errors.website}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-gray-700 mb-2" htmlFor="contactName">
                      Contact Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="contactName"
                      name="contactName"
                      value={formData.contactName}
                      onChange={handleChange}
                      className={`w-full p-2 border rounded ${
                        errors.contactName ? "border-red-500" : "border-gray-300"
                      }`}
                      placeholder="Your name"
                    />
                    {errors.contactName && (
                      <p className="text-red-500 text-sm mt-1">{errors.contactName}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-gray-700 mb-2" htmlFor="email">
                      Email <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className={`w-full p-2 border rounded ${
                        errors.email ? "border-red-500" : "border-gray-300"
                      }`}
                      placeholder="<EMAIL>"
                    />
                    {errors.email && (
                      <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-gray-700 mb-2" htmlFor="phone">
                      Phone <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className={`w-full p-2 border rounded ${
                        errors.phone ? "border-red-500" : "border-gray-300"
                      }`}
                      placeholder="(*************"
                    />
                    {errors.phone && (
                      <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Integration Details */}
            {step === 2 && (
              <div>
                <h2 className="text-xl font-semibold mb-6">Integration Details</h2>
                <div className="mb-6">
                  <label className="block text-gray-700 mb-2" htmlFor="category">
                    Integration Category <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    className={`w-full p-2 border rounded ${
                      errors.category ? "border-red-500" : "border-gray-300"
                    }`}
                  >
                    <option value="">Select a category</option>
                    <option value="governance">Governance & Board Compliance</option>
                    <option value="security">Security</option>
                    <option value="apis">APIs & Developer Tools</option>
                    <option value="risk">Risk & Audit</option>
                    <option value="contracts">Contracts & Policy Lifecycle</option>
                    <option value="certifications">Certifications & Accreditation</option>
                  </select>
                  {errors.category && (
                    <p className="text-red-500 text-sm mt-1">{errors.category}</p>
                  )}
                </div>
                <div className="mb-6">
                  <label className="block text-gray-700 mb-2" htmlFor="description">
                    Integration Description <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    className={`w-full p-2 border rounded ${
                      errors.description ? "border-red-500" : "border-gray-300"
                    }`}
                    rows="4"
                    placeholder="Describe your integration and how it will benefit NovaFuse customers"
                  ></textarea>
                  {errors.description && (
                    <p className="text-red-500 text-sm mt-1">{errors.description}</p>
                  )}
                  <p className="text-sm text-gray-500 mt-1">
                    Minimum 50 characters. {formData.description.length}/50 characters.
                  </p>
                </div>
              </div>
            )}

            {/* Step 3: Partnership Terms */}
            {step === 3 && (
              <div>
                <h2 className="text-xl font-semibold mb-6">Partnership Terms</h2>
                
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-4">Select Partnership Tier</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {tiers.map((tier) => (
                      <div 
                        key={tier.id}
                        className={`border rounded-lg p-4 cursor-pointer ${
                          formData.tier === tier.id 
                            ? "border-blue-500 bg-blue-50" 
                            : "border-gray-300 hover:border-blue-300"
                        }`}
                        onClick={() => setFormData({...formData, tier: tier.id})}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-semibold">{tier.name}</h4>
                          {tier.id === formData.tier && (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{tier.description}</p>
                        <div className="text-sm">
                          <div className="flex justify-between mb-1">
                            <span className="text-gray-500">Revenue Share:</span>
                            <span className="font-medium">{tier.revShare}</span>
                          </div>
                          <div className="flex justify-between mb-1">
                            <span className="text-gray-500">Featured:</span>
                            <span className="font-medium">{tier.featured ? "Yes" : "No"}</span>
                          </div>
                          <div className="flex justify-between mb-1">
                            <span className="text-gray-500">Support:</span>
                            <span className="font-medium">{tier.support}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-500">Requirements:</span>
                            <span className="font-medium text-right">{tier.requirements}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-4">Terms and Conditions</h3>
                  <div className="border border-gray-300 rounded p-4 mb-4 h-40 overflow-y-auto bg-gray-50">
                    <p className="text-sm text-gray-700">
                      <strong>NovaFuse API Superstore Partner Agreement</strong><br /><br />
                      
                      This Partner Agreement ("Agreement") is entered into between NovaFuse, Inc. ("NovaFuse") and the partner company ("Partner") identified in the registration form.<br /><br />
                      
                      <strong>1. Partnership Terms</strong><br />
                      Partner agrees to integrate their services with the NovaFuse API Superstore according to the technical specifications provided by NovaFuse. Partner will maintain the integration in good working order and promptly address any technical issues.<br /><br />
                      
                      <strong>2. Revenue Sharing</strong><br />
                      NovaFuse will share revenue generated through the Partner's integration according to the selected partnership tier. Revenue share percentages are subject to the terms of the selected tier and may be adjusted with 30 days notice.<br /><br />
                      
                      <strong>3. Intellectual Property</strong><br />
                      Each party retains all rights to their respective intellectual property. Partner grants NovaFuse a limited license to use Partner's name, logo, and service description for marketing purposes.<br /><br />
                      
                      <strong>4. Term and Termination</strong><br />
                      This Agreement commences on the date of acceptance and continues until terminated by either party with 30 days written notice.<br /><br />
                      
                      <strong>5. Confidentiality</strong><br />
                      Both parties agree to maintain the confidentiality of any proprietary information shared during the partnership.<br /><br />
                      
                      <strong>6. Limitation of Liability</strong><br />
                      Neither party shall be liable for any indirect, incidental, special, or consequential damages arising out of this Agreement.<br /><br />
                      
                      By checking the box below, Partner acknowledges that they have read, understood, and agree to be bound by the terms of this Agreement.
                    </p>
                  </div>
                  <div className="flex items-start">
                    <input
                      type="checkbox"
                      id="acceptTerms"
                      name="acceptTerms"
                      checked={formData.acceptTerms}
                      onChange={handleChange}
                      className="mt-1 mr-2"
                    />
                    <label htmlFor="acceptTerms" className="text-gray-700">
                      I agree to the NovaFuse Partner Agreement and Terms of Service
                    </label>
                  </div>
                  {errors.acceptTerms && (
                    <p className="text-red-500 text-sm mt-1">{errors.acceptTerms}</p>
                  )}
                </div>
                
                {errors.submit && (
                  <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {errors.submit}
                  </div>
                )}
              </div>
            )}

            <div className="flex justify-between mt-8">
              {step > 1 ? (
                <button
                  type="button"
                  onClick={handlePrevStep}
                  className="bg-gray-200 text-gray-700 px-6 py-2 rounded hover:bg-gray-300"
                >
                  Previous
                </button>
              ) : (
                <div></div>
              )}
              
              {step < 3 ? (
                <button
                  type="button"
                  onClick={handleNextStep}
                  className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700"
                >
                  Next
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`bg-blue-600 text-white px-6 py-2 rounded ${
                    isSubmitting ? "opacity-50 cursor-not-allowed" : "hover:bg-blue-700"
                  }`}
                >
                  {isSubmitting ? "Submitting..." : "Submit Application"}
                </button>
              )}
            </div>
          </form>
        </div>
      </main>

      <footer className="bg-gray-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} NovaFuse. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
