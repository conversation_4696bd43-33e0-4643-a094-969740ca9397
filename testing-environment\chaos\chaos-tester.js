/**
 * Chaos Testing Framework for NovaConnect Universal API Connector
 * 
 * This module provides chaos testing capabilities to test system resilience.
 */

const axios = require('axios');
const { promisify } = require('util');
const fs = require('fs');
const path = require('path');
const sleep = promisify(setTimeout);

// Ensure reports directory exists
const reportsDir = path.join(__dirname, '../reports');
if (!fs.existsSync(reportsDir)) {
  fs.mkdirSync(reportsDir, { recursive: true });
}

class ChaosTester {
  /**
   * Create a new ChaosTester
   * 
   * @param {Object} options - Chaos testing options
   */
  constructor(options = {}) {
    this.options = {
      networkFailureRate: options.networkFailureRate || 0.2, // 20% chance of network failure
      malformedResponseRate: options.malformedResponseRate || 0.2, // 20% chance of malformed response
      delayRate: options.delayRate || 0.3, // 30% chance of delay
      maxDelay: options.maxDelay || 5000, // Maximum delay in ms
      originalAxios: axios.create() // Preserve original axios for cleanup
    };
    
    this.active = false;
    this.interceptors = [];
    this.chaosLog = [];
  }

  /**
   * Start chaos testing
   */
  start() {
    if (this.active) return;
    
    this.active = true;
    
    // Intercept axios requests
    this.interceptors.push(axios.interceptors.request.use(
      async (config) => {
        if (!this.active) return config;
        
        // Log the request
        const requestId = `req-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
        
        const requestLog = {
          id: requestId,
          timestamp: new Date().toISOString(),
          type: 'request',
          url: config.url,
          method: config.method,
          chaos: {}
        };
        
        // Simulate network delay
        if (Math.random() < this.options.delayRate) {
          const delay = Math.random() * this.options.maxDelay;
          requestLog.chaos.delay = delay;
          
          await sleep(delay);
        }
        
        // Simulate network failure
        if (Math.random() < this.options.networkFailureRate) {
          requestLog.chaos.networkFailure = true;
          this.chaosLog.push(requestLog);
          
          throw {
            message: 'CHAOS_TEST: Simulated network failure',
            chaosTest: true,
            requestId
          };
        }
        
        // Add request ID to headers for tracking
        config.headers = config.headers || {};
        config.headers['X-Chaos-Request-ID'] = requestId;
        
        this.chaosLog.push(requestLog);
        return config;
      },
      error => Promise.reject(error)
    ));
    
    // Intercept axios responses
    this.interceptors.push(axios.interceptors.response.use(
      response => {
        if (!this.active) return response;
        
        // Get request ID from headers
        const requestId = response.config.headers['X-Chaos-Request-ID'];
        
        const responseLog = {
          id: `res-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
          requestId,
          timestamp: new Date().toISOString(),
          type: 'response',
          status: response.status,
          chaos: {}
        };
        
        // Simulate malformed response
        if (Math.random() < this.options.malformedResponseRate) {
          responseLog.chaos.malformedResponse = true;
          response.data = this.generateMalformedResponse(response.data);
        }
        
        this.chaosLog.push(responseLog);
        return response;
      },
      error => {
        if (this.active && error.config) {
          // Get request ID from headers
          const requestId = error.config.headers ? error.config.headers['X-Chaos-Request-ID'] : null;
          
          const errorLog = {
            id: `err-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
            requestId,
            timestamp: new Date().toISOString(),
            type: 'error',
            message: error.message,
            chaos: {}
          };
          
          if (error.response) {
            errorLog.status = error.response.status;
          }
          
          this.chaosLog.push(errorLog);
        }
        
        return Promise.reject(error);
      }
    ));
    
    console.log('Chaos testing activated');
  }

  /**
   * Stop chaos testing
   */
  stop() {
    if (!this.active) return;
    
    this.active = false;
    
    // Remove interceptors
    this.interceptors.forEach(id => axios.interceptors.request.eject(id));
    this.interceptors = [];
    
    console.log('Chaos testing deactivated');
    
    // Save chaos log
    this.saveChaosLog();
  }

  /**
   * Generate a malformed response
   * 
   * @param {*} originalData - Original response data
   * @returns {*} - Malformed response data
   */
  generateMalformedResponse(originalData) {
    // Choose a chaos strategy
    const strategy = Math.floor(Math.random() * 5);
    
    switch (strategy) {
      case 0: // Return null
        return null;
      
      case 1: // Return empty object/array
        return Array.isArray(originalData) ? [] : {};
      
      case 2: // Return malformed JSON
        return { __malformed: true, data: '{invalid:json' };
      
      case 3: // Return partial data
        if (typeof originalData === 'object' && originalData !== null) {
          const keys = Object.keys(originalData);
          if (keys.length > 0) {
            const partialData = {};
            const keepKeys = keys.slice(0, Math.ceil(keys.length / 2));
            
            for (const key of keepKeys) {
              partialData[key] = originalData[key];
            }
            
            return partialData;
          }
        }
        return originalData;
      
      case 4: // Add unexpected fields
        if (typeof originalData === 'object' && originalData !== null) {
          return {
            ...originalData,
            __chaos_field: 'unexpected value',
            error: { message: 'This is not a real error' }
          };
        }
        return originalData;
      
      default:
        return originalData;
    }
  }

  /**
   * Save chaos log to file
   * 
   * @returns {string} - Path to the log file
   */
  saveChaosLog() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const logPath = path.join(reportsDir, `chaos-log-${timestamp}.json`);
    
    const log = {
      timestamp,
      options: this.options,
      events: this.chaosLog
    };
    
    fs.writeFileSync(logPath, JSON.stringify(log, null, 2));
    
    return logPath;
  }

  /**
   * Test system resilience
   * 
   * @param {Function} testFn - Test function
   * @param {number} iterations - Number of iterations
   * @returns {Object} - Test results
   */
  async testSystemResilience(testFn, iterations = 10) {
    if (!this.active) {
      this.start();
    }
    
    const results = {
      timestamp: new Date().toISOString(),
      total: iterations,
      successful: 0,
      failed: 0,
      errors: []
    };
    
    for (let i = 0; i < iterations; i++) {
      try {
        console.log(`Running iteration ${i + 1}/${iterations}...`);
        await testFn();
        results.successful++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          iteration: i,
          message: error.message,
          chaosTest: error.chaosTest || false
        });
      }
    }
    
    results.successRate = (results.successful / results.total) * 100;
    
    // Save results
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const resultsPath = path.join(reportsDir, `chaos-results-${timestamp}.json`);
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
    
    this.stop();
    
    return results;
  }

  /**
   * Generate an HTML report
   * 
   * @param {Object} results - Test results
   * @returns {string} - Path to the HTML report
   */
  generateHtmlReport(results) {
    const html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>NovaConnect Chaos Test Report</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
          }
          h1, h2, h3 {
            color: #0066cc;
          }
          .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
          }
          .summary-card {
            background: #f5f5f5;
            border-radius: 5px;
            padding: 15px;
            width: 30%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .summary-card h3 {
            margin-top: 0;
          }
          .summary-card.success { background: #e6ffe6; }
          .summary-card.failure { background: #ffe6e6; }
          .summary-card.rate { background: #e6f7ff; }
          
          .error-details {
            margin-top: 30px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
          }
          th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
          }
          th {
            background: #f2f2f2;
          }
          tr.chaos {
            background: #e6f7ff;
          }
          .timestamp {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 20px;
          }
          .resilience-score {
            font-size: 1.5em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
          }
          .score-high {
            background: #e6ffe6;
            color: #006600;
          }
          .score-medium {
            background: #fff9e6;
            color: #cc6600;
          }
          .score-low {
            background: #ffe6e6;
            color: #cc0000;
          }
        </style>
      </head>
      <body>
        <h1>NovaConnect Chaos Test Report</h1>
        <div class="timestamp">Generated on: ${new Date().toLocaleString()}</div>
        
        <div class="summary">
          <div class="summary-card success">
            <h3>Successful Tests</h3>
            <div>${results.successful} / ${results.total}</div>
          </div>
          <div class="summary-card failure">
            <h3>Failed Tests</h3>
            <div>${results.failed} / ${results.total}</div>
          </div>
          <div class="summary-card rate">
            <h3>Success Rate</h3>
            <div>${results.successRate.toFixed(2)}%</div>
          </div>
        </div>
        
        <div class="resilience-score ${
          results.successRate >= 80 ? 'score-high' : 
          results.successRate >= 60 ? 'score-medium' : 
          'score-low'
        }">
          System Resilience Score: ${results.successRate.toFixed(2)}%
        </div>
        
        <div class="error-details">
          <h2>Error Details</h2>
          ${results.errors.length === 0 ? '<p>No errors occurred during testing.</p>' : `
            <table>
              <thead>
                <tr>
                  <th>Iteration</th>
                  <th>Error Type</th>
                  <th>Message</th>
                </tr>
              </thead>
              <tbody>
                ${results.errors.map(error => `
                  <tr class="${error.chaosTest ? 'chaos' : ''}">
                    <td>${error.iteration}</td>
                    <td>${error.chaosTest ? 'Chaos-Induced' : 'System'}</td>
                    <td>${error.message}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          `}
        </div>
        
        <div class="chaos-config">
          <h2>Chaos Test Configuration</h2>
          <ul>
            <li>Network Failure Rate: ${this.options.networkFailureRate * 100}%</li>
            <li>Malformed Response Rate: ${this.options.malformedResponseRate * 100}%</li>
            <li>Delay Rate: ${this.options.delayRate * 100}%</li>
            <li>Maximum Delay: ${this.options.maxDelay}ms</li>
          </ul>
        </div>
      </body>
      </html>
    `;
    
    const reportPath = path.join(reportsDir, 'chaos-test-report.html');
    fs.writeFileSync(reportPath, html);
    
    return reportPath;
  }
}

module.exports = ChaosTester;
