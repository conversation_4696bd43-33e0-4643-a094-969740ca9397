/**
 * Coherence Enforcer
 * 
 * This module implements the Second Law of Comphyological Governance:
 * 
 * "A system shall sustain resonance through self-similar, energy-minimizing transitions."
 * 
 * It ensures that all state transitions maintain internal coherence by favoring
 * self-similar, energy-efficient transitions that reinforce harmonic states.
 */

const EventEmitter = require('events');

/**
 * Coherence Enforcer class
 */
class CoherenceEnforcer extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      // Resonance set - values that are considered resonant
      resonanceSet: [0.03, 0.06, 0.09, 0.12, 0.13, 0.3, 0.6, 0.9, 3, 6, 9, 12, 13],
      
      // Whether to minimize energy during transitions
      energyMinimization: true,
      
      // Whether to preserve self-similarity during transitions
      selfSimilarityPreservation: true,
      
      // Energy minimization factor (higher values prioritize energy efficiency)
      energyFactor: 0.6, // Aligned with 3-6-9 pattern (0.6)
      
      // Self-similarity factor (higher values prioritize self-similarity)
      selfSimilarityFactor: 0.3, // Aligned with 3-6-9 pattern (0.3)
      
      // Whether to log optimization details
      logOptimization: false,
      
      ...options
    };
    
    // Initialize metrics
    this.metrics = {
      transitions: 0,
      energySaved: 0,
      totalEnergy: 0,
      averageEnergySaved: 0,
      selfSimilarityScore: 0,
      resonanceImprovements: 0,
      resonanceDegradations: 0,
      optimalTransitions: 0
    };
  }
  
  /**
   * Optimize a state according to the Second Law
   * @param {number|Object} state - State to optimize
   * @param {Object} context - Additional context information
   * @returns {number|Object} - Optimized state
   */
  optimize(state, context = {}) {
    // Handle different types of state
    if (typeof state === 'object' && state !== null) {
      return this._optimizeObject(state, context);
    } else if (typeof state === 'number') {
      return this._optimizeNumber(state, context);
    } else {
      throw new Error(`Unsupported state type: ${typeof state}`);
    }
  }
  
  /**
   * Optimize a numeric state
   * @param {number} state - Numeric state to optimize
   * @param {Object} context - Additional context information
   * @returns {number} - Optimized state
   * @private
   */
  _optimizeNumber(state, context = {}) {
    // Calculate initial energy and resonance
    const initialEnergy = this._calculateEnergy(state);
    const initialResonance = this._calculateResonance(state);
    
    // Find the optimal resonant state
    const resonantState = this._findOptimalResonantState(state);
    
    // Calculate final energy and resonance
    const finalEnergy = this._calculateEnergy(resonantState);
    const finalResonance = this._calculateResonance(resonantState);
    
    // Calculate energy saved and self-similarity
    const energySaved = initialEnergy - finalEnergy;
    const selfSimilarity = this._calculateSelfSimilarity(state, resonantState);
    
    // Update metrics
    this.metrics.transitions++;
    this.metrics.energySaved += energySaved;
    this.metrics.totalEnergy += initialEnergy;
    this.metrics.averageEnergySaved = this.metrics.energySaved / this.metrics.transitions;
    this.metrics.selfSimilarityScore = (this.metrics.selfSimilarityScore * (this.metrics.transitions - 1) + selfSimilarity) / this.metrics.transitions;
    
    if (finalResonance > initialResonance) {
      this.metrics.resonanceImprovements++;
    } else if (finalResonance < initialResonance) {
      this.metrics.resonanceDegradations++;
    }
    
    if (energySaved > 0 && selfSimilarity > 0.8) {
      this.metrics.optimalTransitions++;
    }
    
    // Create optimization result
    const optimizationResult = {
      originalState: state,
      optimizedState: resonantState,
      initialEnergy,
      finalEnergy,
      energySaved,
      selfSimilarity,
      initialResonance,
      finalResonance,
      context
    };
    
    // Emit optimization event
    this.emit('state-optimized', optimizationResult);
    
    // Log optimization details if enabled
    if (this.options.logOptimization) {
      console.log(`State optimized: ${state} -> ${resonantState} (energy saved: ${energySaved.toFixed(3)}, self-similarity: ${selfSimilarity.toFixed(3)})`);
    }
    
    return resonantState;
  }
  
  /**
   * Optimize an object state
   * @param {Object} state - Object state to optimize
   * @param {Object} context - Additional context information
   * @returns {Object} - Optimized state
   * @private
   */
  _optimizeObject(state, context = {}) {
    // Create a copy of the state to avoid modifying the original
    const optimizedState = { ...state };
    
    // Optimize each numeric property
    for (const key in optimizedState) {
      if (typeof optimizedState[key] === 'number') {
        optimizedState[key] = this._optimizeNumber(optimizedState[key], {
          ...context,
          property: key
        });
      } else if (typeof optimizedState[key] === 'object' && optimizedState[key] !== null) {
        optimizedState[key] = this._optimizeObject(optimizedState[key], {
          ...context,
          property: key
        });
      }
    }
    
    return optimizedState;
  }
  
  /**
   * Calculate the energy of a state
   * @param {number} state - State to calculate energy for
   * @returns {number} - Energy of the state
   * @private
   */
  _calculateEnergy(state) {
    // Energy is inversely proportional to resonance
    const resonanceDistance = Math.min(...this.options.resonanceSet.map(r => Math.abs(state - r)));
    
    // Use logarithmic energy function: E ∝ 1/log(1 + 1/distance)
    return 1 / (1 + Math.log(1 + 1/Math.max(resonanceDistance, 0.001)));
  }
  
  /**
   * Calculate the resonance of a state
   * @param {number} state - State to calculate resonance for
   * @returns {number} - Resonance of the state (0-1)
   * @private
   */
  _calculateResonance(state) {
    // Resonance is inversely proportional to distance from nearest resonant value
    const resonanceDistance = Math.min(...this.options.resonanceSet.map(r => Math.abs(state - r)));
    
    // Normalize to 0-1 range (1 = perfect resonance)
    return 1 / (1 + resonanceDistance);
  }
  
  /**
   * Calculate self-similarity between two states
   * @param {number} originalState - Original state
   * @param {number} resonantState - Resonant state
   * @returns {number} - Self-similarity score (0-1)
   * @private
   */
  _calculateSelfSimilarity(originalState, resonantState) {
    // Avoid division by zero
    if (originalState === 0 || resonantState === 0) {
      return originalState === resonantState ? 1 : 0;
    }
    
    // Self-similarity is the ratio of the smaller to the larger value
    return Math.min(Math.abs(originalState / resonantState), Math.abs(resonantState / originalState));
  }
  
  /**
   * Find the optimal resonant state for a given state
   * @param {number} state - State to find optimal resonant state for
   * @returns {number} - Optimal resonant state
   * @private
   */
  _findOptimalResonantState(state) {
    // If energy minimization is disabled, just find the nearest resonant state
    if (!this.options.energyMinimization) {
      return this._findNearestResonantState(state);
    }
    
    // Calculate scores for each resonant value
    const scores = this.options.resonanceSet.map(resonantValue => {
      // Calculate energy efficiency score
      const energyEfficiency = 1 / (1 + Math.abs(state - resonantValue));
      
      // Calculate self-similarity score
      const selfSimilarity = this._calculateSelfSimilarity(state, resonantValue);
      
      // Calculate combined score
      const score = (energyEfficiency * this.options.energyFactor) + 
                    (selfSimilarity * this.options.selfSimilarityFactor);
      
      return { resonantValue, score };
    });
    
    // Find the resonant value with the highest score
    const optimalResonantState = scores.reduce((best, current) => {
      return current.score > best.score ? current : best;
    }, { resonantValue: this.options.resonanceSet[0], score: 0 });
    
    return optimalResonantState.resonantValue;
  }
  
  /**
   * Find the nearest resonant state for a given state
   * @param {number} state - State to find nearest resonant state for
   * @returns {number} - Nearest resonant state
   * @private
   */
  _findNearestResonantState(state) {
    // Find the resonant value with the minimum distance
    return this.options.resonanceSet.reduce((nearest, resonantValue) => {
      return Math.abs(state - resonantValue) < Math.abs(state - nearest) ? 
        resonantValue : nearest;
    }, this.options.resonanceSet[0]);
  }
  
  /**
   * Get metrics
   * @returns {Object} - Current metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      transitions: 0,
      energySaved: 0,
      totalEnergy: 0,
      averageEnergySaved: 0,
      selfSimilarityScore: 0,
      resonanceImprovements: 0,
      resonanceDegradations: 0,
      optimalTransitions: 0
    };
  }
}

module.exports = CoherenceEnforcer;
