/**
 * Health Status Chart Component
 * 
 * This component displays a chart of connector health status over time.
 */

import React from 'react';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { Box, Typography } from '@mui/material';

const HealthStatusChart = ({ data }) => {
  // Process data for the chart
  const processChartData = () => {
    // Create an array of the last 24 hours
    const hours = Array(24).fill(0).map((_, i) => {
      const date = new Date();
      date.setHours(date.getHours() - 23 + i);
      return date.getHours();
    });
    
    // Initialize counts for each hour
    const chartData = hours.map(hour => ({
      hour: `${hour}:00`,
      healthy: 0,
      warning: 0,
      error: 0
    }));
    
    // Count statuses for each hour
    data.forEach(connector => {
      if (connector.history && connector.history.status) {
        connector.history.status.forEach((status, i) => {
          if (status === 'healthy') {
            chartData[i].healthy += 1;
          } else if (status === 'warning') {
            chartData[i].warning += 1;
          } else if (status === 'error') {
            chartData[i].error += 1;
          }
        });
      }
    });
    
    return chartData;
  };
  
  const chartData = processChartData();
  
  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const total = payload.reduce((sum, entry) => sum + entry.value, 0);
      
      return (
        <Box sx={{ bgcolor: 'background.paper', p: 2, border: '1px solid #ccc', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            {label}
          </Typography>
          {payload.map((entry, index) => (
            <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <Box 
                sx={{ 
                  width: 12, 
                  height: 12, 
                  bgcolor: entry.color, 
                  mr: 1, 
                  borderRadius: '50%' 
                }} 
              />
              <Typography variant="body2">
                {entry.name}: {entry.value} ({Math.round(entry.value / total * 100)}%)
              </Typography>
            </Box>
          ))}
          <Typography variant="body2" sx={{ mt: 1 }}>
            Total: {total}
          </Typography>
        </Box>
      );
    }
    
    return null;
  };
  
  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart
        data={chartData}
        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        stackOffset="expand"
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="hour" />
        <YAxis />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Bar dataKey="healthy" stackId="status" name="Healthy" fill="#4caf50" />
        <Bar dataKey="warning" stackId="status" name="Warning" fill="#ff9800" />
        <Bar dataKey="error" stackId="status" name="Error" fill="#f44336" />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default HealthStatusChart;
