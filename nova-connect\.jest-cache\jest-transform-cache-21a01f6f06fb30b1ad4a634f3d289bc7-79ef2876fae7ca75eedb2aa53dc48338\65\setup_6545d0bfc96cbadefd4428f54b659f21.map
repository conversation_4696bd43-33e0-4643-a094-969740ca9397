{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "jest", "require", "process", "env", "NODE_ENV", "PORT", "LOG_LEVEL", "TEMPLATES_DIR", "CREDENTIALS_ENCRYPTION_KEY", "JWT_SECRET", "JWT_EXPIRATION", "RATE_LIMIT_WINDOW_MS", "RATE_LIMIT_MAX_REQUESTS", "ENABLE_METRICS", "ENABLE_TRACING", "ENABLE_CACHE", "ENABLE_RATE_LIMIT", "ENABLE_SSRF_PROTECTION", "ENABLE_INPUT_VALIDATION", "global", "testUtils", "createMockRequest", "options", "ip", "method", "path", "headers", "query", "params", "body", "createMockResponse", "res", "statusCode", "status", "code", "json", "data", "send", "<PERSON><PERSON><PERSON><PERSON>", "name", "value", "createMockConnector", "metadata", "version", "category", "description", "author", "tags", "authentication", "type", "fields", "<PERSON><PERSON><PERSON><PERSON>", "required", "sensitive", "configuration", "baseUrl", "endpoints", "id"], "sources": ["setup.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector Test Setup\n * \n * This file is run before each test file.\n */\n\n// Set environment variables for testing\nprocess.env.NODE_ENV = 'test';\nprocess.env.PORT = '3010';\nprocess.env.LOG_LEVEL = 'error';\nprocess.env.TEMPLATES_DIR = './templates';\nprocess.env.CREDENTIALS_ENCRYPTION_KEY = 'test-encryption-key';\nprocess.env.JWT_SECRET = 'test-jwt-secret';\nprocess.env.JWT_EXPIRATION = '1h';\nprocess.env.RATE_LIMIT_WINDOW_MS = '60000';\nprocess.env.RATE_LIMIT_MAX_REQUESTS = '100';\nprocess.env.ENABLE_METRICS = 'true';\nprocess.env.ENABLE_TRACING = 'false';\nprocess.env.ENABLE_CACHE = 'true';\nprocess.env.ENABLE_RATE_LIMIT = 'true';\nprocess.env.ENABLE_SSRF_PROTECTION = 'true';\nprocess.env.ENABLE_INPUT_VALIDATION = 'true';\n\n// Mock external dependencies\njest.mock('axios');\njest.mock('jsonwebtoken');\njest.mock('mongoose');\njest.mock('redis');\n\n// Global test utilities\nglobal.testUtils = {\n  // Create a mock request object\n  createMockRequest: (options = {}) => {\n    return {\n      ip: '127.0.0.1',\n      method: 'GET',\n      path: '/',\n      headers: {},\n      query: {},\n      params: {},\n      body: {},\n      ...options\n    };\n  },\n  \n  // Create a mock response object\n  createMockResponse: () => {\n    const res = {\n      statusCode: 200,\n      headers: {},\n      body: null,\n      status: function(code) {\n        this.statusCode = code;\n        return this;\n      },\n      json: function(data) {\n        this.body = data;\n        return this;\n      },\n      send: function(data) {\n        this.body = data;\n        return this;\n      },\n      setHeader: function(name, value) {\n        this.headers[name] = value;\n        return this;\n      }\n    };\n    \n    return res;\n  },\n  \n  // Create a mock connector\n  createMockConnector: (options = {}) => {\n    return {\n      metadata: {\n        name: 'Test Connector',\n        version: '1.0.0',\n        category: 'Test',\n        description: 'Test connector',\n        author: 'NovaFuse',\n        tags: ['test']\n      },\n      authentication: {\n        type: 'API_KEY',\n        fields: {\n          apiKey: {\n            type: 'string',\n            description: 'API Key',\n            required: true,\n            sensitive: true\n          }\n        }\n      },\n      configuration: {\n        baseUrl: 'https://api.example.com',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      },\n      endpoints: [\n        {\n          id: 'test-endpoint',\n          name: 'Test Endpoint',\n          path: '/test',\n          method: 'GET'\n        }\n      ],\n      ...options\n    };\n  }\n};\n"], "mappings": "AAuBA;AACAA,WAAA,GAAKC,IAAI,CAAC,OAAO,CAAC;AAClBD,WAAA,GAAKC,IAAI,CAAC,cAAc,CAAC;AACzBD,WAAA,GAAKC,IAAI,CAAC,UAAU,CAAC;AACrBD,WAAA,GAAKC,IAAI,CAAC,OAAO,CAAC;;AAElB;AAAA,SAAAD,YAAA;EAAA;IAAAE;EAAA,IAAAC,OAAA;EAAAH,WAAA,GAAAA,CAAA,KAAAE,IAAA;EAAA,OAAAA,IAAA;AAAA;AA7BA;AACA;AACA;AACA;AACA;;AAEA;AACAE,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAG,MAAM;AAC7BF,OAAO,CAACC,GAAG,CAACE,IAAI,GAAG,MAAM;AACzBH,OAAO,CAACC,GAAG,CAACG,SAAS,GAAG,OAAO;AAC/BJ,OAAO,CAACC,GAAG,CAACI,aAAa,GAAG,aAAa;AACzCL,OAAO,CAACC,GAAG,CAACK,0BAA0B,GAAG,qBAAqB;AAC9DN,OAAO,CAACC,GAAG,CAACM,UAAU,GAAG,iBAAiB;AAC1CP,OAAO,CAACC,GAAG,CAACO,cAAc,GAAG,IAAI;AACjCR,OAAO,CAACC,GAAG,CAACQ,oBAAoB,GAAG,OAAO;AAC1CT,OAAO,CAACC,GAAG,CAACS,uBAAuB,GAAG,KAAK;AAC3CV,OAAO,CAACC,GAAG,CAACU,cAAc,GAAG,MAAM;AACnCX,OAAO,CAACC,GAAG,CAACW,cAAc,GAAG,OAAO;AACpCZ,OAAO,CAACC,GAAG,CAACY,YAAY,GAAG,MAAM;AACjCb,OAAO,CAACC,GAAG,CAACa,iBAAiB,GAAG,MAAM;AACtCd,OAAO,CAACC,GAAG,CAACc,sBAAsB,GAAG,MAAM;AAC3Cf,OAAO,CAACC,GAAG,CAACe,uBAAuB,GAAG,MAAM;AAS5CC,MAAM,CAACC,SAAS,GAAG;EACjB;EACAC,iBAAiB,EAAEA,CAACC,OAAO,GAAG,CAAC,CAAC,KAAK;IACnC,OAAO;MACLC,EAAE,EAAE,WAAW;MACfC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,GAAG;MACTC,OAAO,EAAE,CAAC,CAAC;MACXC,KAAK,EAAE,CAAC,CAAC;MACTC,MAAM,EAAE,CAAC,CAAC;MACVC,IAAI,EAAE,CAAC,CAAC;MACR,GAAGP;IACL,CAAC;EACH,CAAC;EAED;EACAQ,kBAAkB,EAAEA,CAAA,KAAM;IACxB,MAAMC,GAAG,GAAG;MACVC,UAAU,EAAE,GAAG;MACfN,OAAO,EAAE,CAAC,CAAC;MACXG,IAAI,EAAE,IAAI;MACVI,MAAM,EAAE,SAAAA,CAASC,IAAI,EAAE;QACrB,IAAI,CAACF,UAAU,GAAGE,IAAI;QACtB,OAAO,IAAI;MACb,CAAC;MACDC,IAAI,EAAE,SAAAA,CAASC,IAAI,EAAE;QACnB,IAAI,CAACP,IAAI,GAAGO,IAAI;QAChB,OAAO,IAAI;MACb,CAAC;MACDC,IAAI,EAAE,SAAAA,CAASD,IAAI,EAAE;QACnB,IAAI,CAACP,IAAI,GAAGO,IAAI;QAChB,OAAO,IAAI;MACb,CAAC;MACDE,SAAS,EAAE,SAAAA,CAASC,IAAI,EAAEC,KAAK,EAAE;QAC/B,IAAI,CAACd,OAAO,CAACa,IAAI,CAAC,GAAGC,KAAK;QAC1B,OAAO,IAAI;MACb;IACF,CAAC;IAED,OAAOT,GAAG;EACZ,CAAC;EAED;EACAU,mBAAmB,EAAEA,CAACnB,OAAO,GAAG,CAAC,CAAC,KAAK;IACrC,OAAO;MACLoB,QAAQ,EAAE;QACRH,IAAI,EAAE,gBAAgB;QACtBI,OAAO,EAAE,OAAO;QAChBC,QAAQ,EAAE,MAAM;QAChBC,WAAW,EAAE,gBAAgB;QAC7BC,MAAM,EAAE,UAAU;QAClBC,IAAI,EAAE,CAAC,MAAM;MACf,CAAC;MACDC,cAAc,EAAE;QACdC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;UACNC,MAAM,EAAE;YACNF,IAAI,EAAE,QAAQ;YACdJ,WAAW,EAAE,SAAS;YACtBO,QAAQ,EAAE,IAAI;YACdC,SAAS,EAAE;UACb;QACF;MACF,CAAC;MACDC,aAAa,EAAE;QACbC,OAAO,EAAE,yBAAyB;QAClC7B,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC;MACD8B,SAAS,EAAE,CACT;QACEC,EAAE,EAAE,eAAe;QACnBlB,IAAI,EAAE,eAAe;QACrBd,IAAI,EAAE,OAAO;QACbD,MAAM,EAAE;MACV,CAAC,CACF;MACD,GAAGF;IACL,CAAC;EACH;AACF,CAAC", "ignoreList": []}