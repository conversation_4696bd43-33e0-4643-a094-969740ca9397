/**
 * Resonance Lock Demo
 * 
 * This script demonstrates the 3-6-9-12-13 resonance pattern in action,
 * showing how it enforces the Law of Resonant Encapsulation:
 * 
 * "No system shall externalize what its resonance core has not first harmonized."
 */

const fs = require('fs');
const path = require('path');
const { ComphyologyCore, ResonanceValidator } = require('../src/comphyology');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../resonance_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Demonstrate resonance validation
 */
function demonstrateResonanceValidation() {
  console.log('=== Demonstrating Resonance Validation ===');
  
  // Create resonance validator
  const validator = new ResonanceValidator({
    strictMode: false,
    logValidation: true,
    resonanceLock: true
  });
  
  // Test values
  const testValues = [
    0.3, 0.6, 0.9,  // Resonant thresholds
    0.03, 0.06, 0.09, 0.12, 0.13,  // Resonant decay rates
    3, 6, 9, 12,  // Resonant cycles
    0.7, 0.8, 0.4, 0.07  // Non-resonant values
  ];
  
  // Validate values
  console.log('\nValidating values:');
  const results = testValues.map(value => {
    const result = validator.validate(value);
    console.log(`Value ${value} -> ${result.isResonant ? 'Resonant' : 'Non-resonant'}${result.wasHarmonized ? ` (Harmonized to ${result.harmonizedValue})` : ''}`);
    return result;
  });
  
  // Calculate statistics
  const resonantCount = results.filter(r => r.isResonant).length;
  const harmonizedCount = results.filter(r => r.wasHarmonized).length;
  
  console.log(`\nResonant values: ${resonantCount}/${results.length} (${(resonantCount/results.length*100).toFixed(2)}%)`);
  console.log(`Harmonized values: ${harmonizedCount}/${results.length} (${(harmonizedCount/results.length*100).toFixed(2)}%)`);
  
  return {
    validator,
    testValues,
    results,
    resonantCount,
    harmonizedCount
  };
}

/**
 * Demonstrate resonance-locked Comphyology Core
 */
function demonstrateResonanceLockedComphyologyCore() {
  console.log('\n=== Demonstrating Resonance-Locked Comphyology Core ===');
  
  // Create Comphyology Core with resonance lock enabled
  const comphyologyCore = new ComphyologyCore({
    enableLogging: true,
    enableCaching: false,
    resonanceLock: true,
    strictMode: false
  });
  
  // Create a system state
  const systemState = {
    structure: {
      complexity: 0.6,
      adaptability: 0.6,
      resilience: 0.6
    },
    environment: {
      volatility: 0.3,
      uncertainty: 0.3,
      complexity: 0.3,
      ambiguity: 0.3
    },
    quantum: {
      entropy: {
        value: 0.3,
        gradient: 0.1,
        threshold: 0.3
      },
      phase: {
        value: 0.6,
        coherence: 0.6,
        stability: 0.6
      }
    },
    ethics: {
      fairness: 0.9,
      transparency: 0.9,
      accountability: 0.9
    }
  };
  
  // Calculate Comphyology value with resonance lock enabled
  console.log('\nCalculating with resonance lock enabled:');
  const resultWithLock = comphyologyCore.calculate(systemState);
  console.log(`Comphyology value: ${resultWithLock.comphyologyValue}`);
  console.log(`Resonance: ${JSON.stringify(resultWithLock.resonance)}`);
  
  // Calculate Comphyology value with resonance lock disabled
  console.log('\nCalculating with resonance lock disabled:');
  comphyologyCore.setResonanceLock(false);
  const resultWithoutLock = comphyologyCore.calculate(systemState);
  console.log(`Comphyology value: ${resultWithoutLock.comphyologyValue}`);
  console.log(`Resonance: ${JSON.stringify(resultWithoutLock.resonance)}`);
  
  // Compare results
  console.log('\nComparing results:');
  console.log(`With lock: ${resultWithLock.comphyologyValue}`);
  console.log(`Without lock: ${resultWithoutLock.comphyologyValue}`);
  console.log(`Difference: ${Math.abs(resultWithLock.comphyologyValue - resultWithoutLock.comphyologyValue)}`);
  
  // Re-enable resonance lock
  comphyologyCore.setResonanceLock(true);
  
  // Test with variations
  console.log('\nTesting with variations:');
  
  const variations = [];
  
  for (let i = 0; i < 10; i++) {
    // Create a variation of the system state
    const variationState = JSON.parse(JSON.stringify(systemState));
    
    // Modify values to create variations
    variationState.structure.complexity = 0.3 + (i * 0.03);
    variationState.environment.volatility = 0.6 + (i * 0.03);
    variationState.quantum.entropy.value = 0.9 - (i * 0.03);
    
    // Calculate with resonance lock enabled
    const resultWithLock = comphyologyCore.calculate(variationState);
    
    // Calculate with resonance lock disabled
    comphyologyCore.setResonanceLock(false);
    const resultWithoutLock = comphyologyCore.calculate(variationState);
    comphyologyCore.setResonanceLock(true);
    
    // Store results
    variations.push({
      variation: i,
      withLock: resultWithLock.comphyologyValue,
      withoutLock: resultWithoutLock.comphyologyValue,
      difference: Math.abs(resultWithLock.comphyologyValue - resultWithoutLock.comphyologyValue),
      isResonant: resultWithLock.resonance.isResonant,
      wasHarmonized: resultWithLock.resonance.wasHarmonized
    });
    
    console.log(`Variation ${i}: With lock = ${resultWithLock.comphyologyValue}, Without lock = ${resultWithoutLock.comphyologyValue}, Difference = ${variations[i].difference}`);
  }
  
  // Calculate statistics
  const totalDifference = variations.reduce((sum, v) => sum + v.difference, 0);
  const averageDifference = totalDifference / variations.length;
  const resonantCount = variations.filter(v => v.isResonant).length;
  const harmonizedCount = variations.filter(v => v.wasHarmonized).length;
  
  console.log(`\nAverage difference: ${averageDifference}`);
  console.log(`Resonant values: ${resonantCount}/${variations.length} (${(resonantCount/variations.length*100).toFixed(2)}%)`);
  console.log(`Harmonized values: ${harmonizedCount}/${variations.length} (${(harmonizedCount/variations.length*100).toFixed(2)}%)`);
  
  return {
    comphyologyCore,
    systemState,
    resultWithLock,
    resultWithoutLock,
    variations,
    averageDifference,
    resonantCount,
    harmonizedCount
  };
}

/**
 * Generate HTML report
 */
function generateHtmlReport(validationResults, coreResults) {
  console.log('\n=== Generating HTML Report ===');
  
  const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>3-6-9-12-13 Resonance Pattern Demo</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .card {
      background-color: #f9f9f9;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
      flex: 1;
      min-width: 300px;
    }
    .resonance-info {
      background-color: #f0f8ff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      border-left: 5px solid #0066cc;
    }
    .chart {
      width: 100%;
      height: 300px;
      margin-top: 20px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .resonant {
      color: #009900;
      font-weight: bold;
    }
    .non-resonant {
      color: #cc0000;
    }
    .harmonized {
      color: #0066cc;
      font-style: italic;
    }
    footer {
      margin-top: 40px;
      text-align: center;
      color: #666;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <h1>3-6-9-12-13 Resonance Pattern Demo</h1>
  <p>Generated: ${new Date().toLocaleString()}</p>
  
  <div class="resonance-info">
    <h2>The Law of Resonant Encapsulation</h2>
    <p><em>"No system shall externalize what its resonance core has not first harmonized."</em></p>
    <p>This demo demonstrates the 3-6-9-12-13 resonance pattern in action, showing how it enforces the Law of Resonant Encapsulation.</p>
  </div>
  
  <h2>Resonance Validation Results</h2>
  
  <div class="card">
    <h3>Validation Statistics</h3>
    <p>Resonant values: ${validationResults.resonantCount}/${validationResults.results.length} (${(validationResults.resonantCount/validationResults.results.length*100).toFixed(2)}%)</p>
    <p>Harmonized values: ${validationResults.harmonizedCount}/${validationResults.results.length} (${(validationResults.harmonizedCount/validationResults.results.length*100).toFixed(2)}%)</p>
    
    <h3>Validation Details</h3>
    <table>
      <tr>
        <th>Value</th>
        <th>Resonant</th>
        <th>Harmonized</th>
        <th>Harmonized Value</th>
        <th>Drift</th>
      </tr>
      ${validationResults.results.map(result => `
      <tr>
        <td>${result.originalValue}</td>
        <td class="${result.isResonant ? 'resonant' : 'non-resonant'}">${result.isResonant ? 'Yes' : 'No'}</td>
        <td>${result.wasHarmonized ? 'Yes' : 'No'}</td>
        <td class="${result.wasHarmonized ? 'harmonized' : ''}">${result.wasHarmonized ? result.harmonizedValue : '-'}</td>
        <td>${result.wasHarmonized ? result.resonanceDrift : '-'}</td>
      </tr>
      `).join('')}
    </table>
  </div>
  
  <h2>Resonance-Locked Comphyology Core Results</h2>
  
  <div class="container">
    <div class="card">
      <h3>With Resonance Lock</h3>
      <p>Comphyology Value: ${coreResults.resultWithLock.comphyologyValue}</p>
      <p>Resonant: ${coreResults.resultWithLock.resonance.isResonant ? 'Yes' : 'No'}</p>
      <p>Harmonized: ${coreResults.resultWithLock.resonance.wasHarmonized ? 'Yes' : 'No'}</p>
      <p>Drift: ${coreResults.resultWithLock.resonance.resonanceDrift}</p>
    </div>
    
    <div class="card">
      <h3>Without Resonance Lock</h3>
      <p>Comphyology Value: ${coreResults.resultWithoutLock.comphyologyValue}</p>
      <p>Difference: ${Math.abs(coreResults.resultWithLock.comphyologyValue - coreResults.resultWithoutLock.comphyologyValue)}</p>
    </div>
  </div>
  
  <h3>Variation Results</h3>
  <p>Average difference: ${coreResults.averageDifference}</p>
  <p>Resonant values: ${coreResults.resonantCount}/${coreResults.variations.length} (${(coreResults.resonantCount/coreResults.variations.length*100).toFixed(2)}%)</p>
  <p>Harmonized values: ${coreResults.harmonizedCount}/${coreResults.variations.length} (${(coreResults.harmonizedCount/coreResults.variations.length*100).toFixed(2)}%)</p>
  
  <table>
    <tr>
      <th>Variation</th>
      <th>With Lock</th>
      <th>Without Lock</th>
      <th>Difference</th>
      <th>Resonant</th>
      <th>Harmonized</th>
    </tr>
    ${coreResults.variations.map(variation => `
    <tr>
      <td>${variation.variation}</td>
      <td>${variation.withLock}</td>
      <td>${variation.withoutLock}</td>
      <td>${variation.difference}</td>
      <td class="${variation.isResonant ? 'resonant' : 'non-resonant'}">${variation.isResonant ? 'Yes' : 'No'}</td>
      <td class="${variation.wasHarmonized ? 'harmonized' : ''}">${variation.wasHarmonized ? 'Yes' : 'No'}</td>
    </tr>
    `).join('')}
  </table>
  
  <footer>
    <p>NovaFuse Resonance Lock Demo - Copyright © ${new Date().getFullYear()}</p>
    <p><em>"The universe counts in 3s. Now, so do we."</em></p>
  </footer>
</body>
</html>`;
  
  // Save HTML report
  const reportPath = path.join(RESULTS_DIR, 'resonance_lock_demo_report.html');
  fs.writeFileSync(reportPath, htmlContent);
  
  console.log(`HTML report saved to ${reportPath}`);
  
  return {
    htmlContent,
    reportPath
  };
}

/**
 * Main function
 */
function main() {
  console.log('=== 3-6-9-12-13 Resonance Pattern Demo ===');
  
  // Run demonstrations
  const validationResults = demonstrateResonanceValidation();
  const coreResults = demonstrateResonanceLockedComphyologyCore();
  
  // Generate HTML report
  const reportResults = generateHtmlReport(validationResults, coreResults);
  
  // Save results to JSON file
  const results = {
    validationResults,
    coreResults,
    reportPath: reportResults.reportPath
  };
  
  fs.writeFileSync(
    path.join(RESULTS_DIR, 'resonance_lock_demo_results.json'),
    JSON.stringify(results, null, 2)
  );
  
  console.log(`\nResults saved to ${path.join(RESULTS_DIR, 'resonance_lock_demo_results.json')}`);
  console.log(`HTML report saved to ${reportResults.reportPath}`);
  console.log('\nOpen the HTML report to view the results in a browser.');
}

// Run main function
main();
