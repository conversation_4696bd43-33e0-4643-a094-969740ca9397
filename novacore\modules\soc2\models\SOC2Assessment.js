/**
 * NovaCore SOC 2 Assessment Model
 * 
 * This model defines the schema for SOC 2 assessments.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define control assessment schema
const controlAssessmentSchema = new Schema({
  controlId: { 
    type: String, 
    required: true, 
    trim: true 
  },
  status: { 
    type: String, 
    enum: ['not_assessed', 'non_compliant', 'partially_compliant', 'compliant', 'not_applicable'], 
    default: 'not_assessed' 
  },
  score: { 
    type: Number, 
    min: 0, 
    max: 100 
  },
  findings: [{ 
    type: String, 
    trim: true 
  }],
  recommendations: [{ 
    type: String, 
    trim: true 
  }],
  evidenceIds: [{ 
    type: Schema.Types.ObjectId, 
    ref: 'SOC2Evidence' 
  }],
  notes: { 
    type: String, 
    trim: true 
  },
  assessedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  assessedAt: { 
    type: Date, 
    default: Date.now 
  }
}, { _id: false });

// Define category assessment schema
const categoryAssessmentSchema = new Schema({
  category: { 
    type: String, 
    required: true, 
    enum: [
      'control_environment', 
      'communication_information', 
      'risk_assessment', 
      'monitoring_activities', 
      'control_activities', 
      'logical_physical_access', 
      'system_operations', 
      'change_management', 
      'risk_mitigation'
    ]
  },
  status: { 
    type: String, 
    enum: ['not_assessed', 'non_compliant', 'partially_compliant', 'compliant'], 
    default: 'not_assessed' 
  },
  score: { 
    type: Number, 
    min: 0, 
    max: 100 
  },
  controlCount: { 
    type: Number, 
    default: 0 
  },
  compliantCount: { 
    type: Number, 
    default: 0 
  },
  partiallyCompliantCount: { 
    type: Number, 
    default: 0 
  },
  nonCompliantCount: { 
    type: Number, 
    default: 0 
  },
  notApplicableCount: { 
    type: Number, 
    default: 0 
  },
  notAssessedCount: { 
    type: Number, 
    default: 0 
  }
}, { _id: false });

// Define criteria assessment schema
const criteriaAssessmentSchema = new Schema({
  criteria: { 
    type: String, 
    required: true, 
    enum: ['security', 'availability', 'processing_integrity', 'confidentiality', 'privacy']
  },
  status: { 
    type: String, 
    enum: ['not_assessed', 'non_compliant', 'partially_compliant', 'compliant'], 
    default: 'not_assessed' 
  },
  score: { 
    type: Number, 
    min: 0, 
    max: 100 
  },
  controlCount: { 
    type: Number, 
    default: 0 
  },
  compliantCount: { 
    type: Number, 
    default: 0 
  },
  partiallyCompliantCount: { 
    type: Number, 
    default: 0 
  },
  nonCompliantCount: { 
    type: Number, 
    default: 0 
  },
  notApplicableCount: { 
    type: Number, 
    default: 0 
  },
  notAssessedCount: { 
    type: Number, 
    default: 0 
  }
}, { _id: false });

// Define SOC 2 assessment schema
const soc2AssessmentSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  organizationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Organization', 
    required: true 
  },
  type: { 
    type: String, 
    enum: ['readiness', 'gap', 'internal', 'external'], 
    default: 'readiness' 
  },
  status: { 
    type: String, 
    enum: ['draft', 'in_progress', 'completed', 'archived'], 
    default: 'draft' 
  },
  startDate: { 
    type: Date 
  },
  endDate: { 
    type: Date 
  },
  dueDate: { 
    type: Date 
  },
  overallScore: { 
    type: Number, 
    min: 0, 
    max: 100 
  },
  trustServiceCriteria: [{ 
    type: String, 
    enum: ['security', 'availability', 'processing_integrity', 'confidentiality', 'privacy'], 
    default: ['security'] 
  }],
  controlAssessments: [controlAssessmentSchema],
  categoryAssessments: [categoryAssessmentSchema],
  criteriaAssessments: [criteriaAssessmentSchema],
  findings: [{
    id: { 
      type: String, 
      required: true, 
      trim: true 
    },
    title: { 
      type: String, 
      required: true, 
      trim: true 
    },
    description: { 
      type: String, 
      required: true, 
      trim: true 
    },
    severity: { 
      type: String, 
      enum: ['critical', 'high', 'medium', 'low', 'informational'], 
      default: 'medium' 
    },
    relatedControls: [{ 
      type: String, 
      trim: true 
    }],
    remediation: { 
      type: String, 
      trim: true 
    },
    status: { 
      type: String, 
      enum: ['open', 'in_progress', 'remediated', 'accepted'], 
      default: 'open' 
    }
  }],
  recommendations: [{
    id: { 
      type: String, 
      required: true, 
      trim: true 
    },
    title: { 
      type: String, 
      required: true, 
      trim: true 
    },
    description: { 
      type: String, 
      required: true, 
      trim: true 
    },
    priority: { 
      type: String, 
      enum: ['high', 'medium', 'low'], 
      default: 'medium' 
    },
    relatedControls: [{ 
      type: String, 
      trim: true 
    }],
    implementationSteps: [{ 
      type: String, 
      trim: true 
    }],
    status: { 
      type: String, 
      enum: ['pending', 'in_progress', 'implemented', 'rejected'], 
      default: 'pending' 
    }
  }],
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
soc2AssessmentSchema.index({ organizationId: 1 });
soc2AssessmentSchema.index({ status: 1 });
soc2AssessmentSchema.index({ type: 1 });
soc2AssessmentSchema.index({ trustServiceCriteria: 1 });
soc2AssessmentSchema.index({ startDate: 1 });
soc2AssessmentSchema.index({ endDate: 1 });
soc2AssessmentSchema.index({ dueDate: 1 });
soc2AssessmentSchema.index({ createdAt: 1 });

// Add methods
soc2AssessmentSchema.methods.isCompleted = function() {
  return this.status === 'completed';
};

soc2AssessmentSchema.methods.getCompliancePercentage = function() {
  if (!this.controlAssessments || this.controlAssessments.length === 0) {
    return 0;
  }
  
  const totalControls = this.controlAssessments.length;
  const compliantControls = this.controlAssessments.filter(
    assessment => assessment.status === 'compliant'
  ).length;
  
  return (compliantControls / totalControls) * 100;
};

soc2AssessmentSchema.methods.getControlAssessment = function(controlId) {
  return this.controlAssessments.find(
    assessment => assessment.controlId === controlId
  );
};

// Add statics
soc2AssessmentSchema.statics.findByOrganization = function(organizationId) {
  return this.find({ organizationId });
};

soc2AssessmentSchema.statics.findActive = function(organizationId) {
  return this.find({ 
    organizationId, 
    status: { $in: ['draft', 'in_progress'] } 
  });
};

soc2AssessmentSchema.statics.findCompleted = function(organizationId) {
  return this.find({ 
    organizationId, 
    status: 'completed' 
  });
};

// Create model
const SOC2Assessment = mongoose.model('SOC2Assessment', soc2AssessmentSchema);

module.exports = SOC2Assessment;
