/**
 * Comprehensive Integration Tests for NovaTrack API
 * 
 * These tests verify the NovaTrack API endpoints function correctly
 */

const request = require('supertest');
const { describe, it, beforeAll, afterAll, expect } = require('@jest/globals');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import the Express app
const app = require('../../../server');

describe('NovaTrack API - Integration Tests', () => {
  let tempDir;
  let testRequirementId;
  let testActivityId;
  
  beforeAll(async () => {
    // Create a temporary directory for test data
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'novatrack-api-test-'));
    
    // Configure the app to use the temporary directory
    process.env.NOVATRACK_DATA_DIR = tempDir;
    
    // Wait for the app to initialize
    await new Promise(resolve => setTimeout(resolve, 1000));
  });
  
  afterAll(async () => {
    // Clean up the temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
    
    // Reset the environment variable
    delete process.env.NOVATRACK_DATA_DIR;
    
    // Close the server
    await new Promise(resolve => {
      if (app.server) {
        app.server.close(resolve);
      } else {
        resolve();
      }
    });
  });
  
  describe('Requirements API', () => {
    it('should create a requirement', async () => {
      const response = await request(app)
        .post('/api/novatrack/requirements')
        .send({
          name: 'Test Requirement',
          description: 'Test description',
          framework: 'Test framework',
          category: 'Test category',
          priority: 'high',
          status: 'in_progress',
          due_date: '2023-12-31',
          assigned_to: 'test_user',
          tags: ['test', 'requirement']
        });
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe('Test Requirement');
      expect(response.body.description).toBe('Test description');
      expect(response.body.framework).toBe('Test framework');
      expect(response.body.category).toBe('Test category');
      expect(response.body.priority).toBe('high');
      expect(response.body.status).toBe('in_progress');
      expect(response.body.due_date).toBe('2023-12-31');
      expect(response.body.assigned_to).toBe('test_user');
      expect(response.body.tags).toEqual(['test', 'requirement']);
      
      // Save the requirement ID for later tests
      testRequirementId = response.body.id;
    });
    
    it('should get all requirements', async () => {
      const response = await request(app)
        .get('/api/novatrack/requirements');
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0]).toHaveProperty('id');
      expect(response.body[0]).toHaveProperty('name');
    });
    
    it('should get a requirement by ID', async () => {
      const response = await request(app)
        .get(`/api/novatrack/requirements/${testRequirementId}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', testRequirementId);
      expect(response.body.name).toBe('Test Requirement');
    });
    
    it('should update a requirement', async () => {
      const response = await request(app)
        .put(`/api/novatrack/requirements/${testRequirementId}`)
        .send({
          name: 'Updated Requirement',
          priority: 'medium',
          status: 'completed'
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', testRequirementId);
      expect(response.body.name).toBe('Updated Requirement');
      expect(response.body.priority).toBe('medium');
      expect(response.body.status).toBe('completed');
    });
    
    it('should filter requirements by framework', async () => {
      // Create another requirement with a different framework
      await request(app)
        .post('/api/novatrack/requirements')
        .send({
          name: 'Another Requirement',
          framework: 'Another framework'
        });
      
      const response = await request(app)
        .get('/api/novatrack/requirements')
        .query({ framework: 'Test framework' });
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].framework).toBe('Test framework');
    });
    
    it('should filter requirements by status', async () => {
      const response = await request(app)
        .get('/api/novatrack/requirements')
        .query({ status: 'completed' });
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].status).toBe('completed');
    });
    
    it('should filter requirements by priority', async () => {
      const response = await request(app)
        .get('/api/novatrack/requirements')
        .query({ priority: 'medium' });
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].priority).toBe('medium');
    });
    
    it('should filter requirements by assigned_to', async () => {
      const response = await request(app)
        .get('/api/novatrack/requirements')
        .query({ assigned_to: 'test_user' });
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].assigned_to).toBe('test_user');
    });
    
    it('should filter requirements by tag', async () => {
      const response = await request(app)
        .get('/api/novatrack/requirements')
        .query({ tag: 'test' });
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].tags).toContain('test');
    });
  });
  
  describe('Activities API', () => {
    it('should create an activity', async () => {
      const response = await request(app)
        .post('/api/novatrack/activities')
        .send({
          name: 'Test Activity',
          description: 'Test description',
          requirement_id: testRequirementId,
          type: 'task',
          status: 'in_progress',
          start_date: '2023-01-01',
          end_date: '2023-01-31',
          assigned_to: 'test_user',
          notes: 'Test notes'
        });
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe('Test Activity');
      expect(response.body.description).toBe('Test description');
      expect(response.body.requirement_id).toBe(testRequirementId);
      expect(response.body.type).toBe('task');
      expect(response.body.status).toBe('in_progress');
      expect(response.body.start_date).toBe('2023-01-01');
      expect(response.body.end_date).toBe('2023-01-31');
      expect(response.body.assigned_to).toBe('test_user');
      expect(response.body.notes).toBe('Test notes');
      
      // Save the activity ID for later tests
      testActivityId = response.body.id;
    });
    
    it('should get all activities', async () => {
      const response = await request(app)
        .get('/api/novatrack/activities');
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0]).toHaveProperty('id');
      expect(response.body[0]).toHaveProperty('name');
    });
    
    it('should get an activity by ID', async () => {
      const response = await request(app)
        .get(`/api/novatrack/activities/${testActivityId}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', testActivityId);
      expect(response.body.name).toBe('Test Activity');
    });
    
    it('should update an activity', async () => {
      const response = await request(app)
        .put(`/api/novatrack/activities/${testActivityId}`)
        .send({
          name: 'Updated Activity',
          type: 'meeting',
          status: 'completed'
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', testActivityId);
      expect(response.body.name).toBe('Updated Activity');
      expect(response.body.type).toBe('meeting');
      expect(response.body.status).toBe('completed');
    });
    
    it('should get activities for a requirement', async () => {
      const response = await request(app)
        .get(`/api/novatrack/requirements/${testRequirementId}/activities`);
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].requirement_id).toBe(testRequirementId);
    });
    
    it('should filter activities by type', async () => {
      const response = await request(app)
        .get('/api/novatrack/activities')
        .query({ type: 'meeting' });
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].type).toBe('meeting');
    });
    
    it('should filter activities by status', async () => {
      const response = await request(app)
        .get('/api/novatrack/activities')
        .query({ status: 'completed' });
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].status).toBe('completed');
    });
    
    it('should filter activities by assigned_to', async () => {
      const response = await request(app)
        .get('/api/novatrack/activities')
        .query({ assigned_to: 'test_user' });
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].assigned_to).toBe('test_user');
    });
  });
  
  describe('Error Handling', () => {
    it('should return 400 when creating a requirement with invalid data', async () => {
      const response = await request(app)
        .post('/api/novatrack/requirements')
        .send({
          // Missing name
          priority: 'invalid_priority'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
    
    it('should return 400 when creating an activity with invalid data', async () => {
      const response = await request(app)
        .post('/api/novatrack/activities')
        .send({
          // Missing name
          type: 'invalid_type'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
    
    it('should return 404 when getting a non-existent requirement', async () => {
      const response = await request(app)
        .get('/api/novatrack/requirements/non_existent_id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
    
    it('should return 404 when getting a non-existent activity', async () => {
      const response = await request(app)
        .get('/api/novatrack/activities/non_existent_id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
    
    it('should return 404 when updating a non-existent requirement', async () => {
      const response = await request(app)
        .put('/api/novatrack/requirements/non_existent_id')
        .send({
          name: 'Updated Requirement'
        });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
    
    it('should return 404 when updating a non-existent activity', async () => {
      const response = await request(app)
        .put('/api/novatrack/activities/non_existent_id')
        .send({
          name: 'Updated Activity'
        });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
    
    it('should return 404 when deleting a non-existent requirement', async () => {
      const response = await request(app)
        .delete('/api/novatrack/requirements/non_existent_id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
    
    it('should return 404 when deleting a non-existent activity', async () => {
      const response = await request(app)
        .delete('/api/novatrack/activities/non_existent_id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
  });
  
  describe('Cleanup', () => {
    it('should delete an activity', async () => {
      const response = await request(app)
        .delete(`/api/novatrack/activities/${testActivityId}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
    });
    
    it('should delete a requirement', async () => {
      const response = await request(app)
        .delete(`/api/novatrack/requirements/${testRequirementId}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
    });
  });
});
