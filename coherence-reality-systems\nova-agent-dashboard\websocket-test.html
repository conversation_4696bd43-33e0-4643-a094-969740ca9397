<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test - Nova Agent</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        .log { background: #000; padding: 10px; border-radius: 5px; height: 300px; overflow-y: auto; font-family: monospace; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #2d5a2d; }
        .error { background: #5a2d2d; }
        .info { background: #2d2d5a; }
        button { padding: 10px 20px; margin: 5px; background: #4a4a4a; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #6a6a6a; }
    </style>
</head>
<body>
    <h1>🧪 Nova Agent WebSocket Test</h1>
    
    <div id="status" class="status info">Testing connection...</div>
    
    <div>
        <button onclick="testAPI()">Test API</button>
        <button onclick="testWebSocket()">Test WebSocket</button>
        <button onclick="testCommand()">Test Command</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <h3>Test Log:</h3>
    <div id="log" class="log"></div>

    <script>
        let ws = null;
        
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.innerHTML = `[${timestamp}] ${message}`;
            entry.style.color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#74c0fc';
            logEl.appendChild(entry);
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        async function testAPI() {
            log('🔍 Testing API connection...');
            try {
                const response = await fetch('http://localhost:8090/status');
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ API Success: Status=${data.status}, Coherence=${(data.coherence*100).toFixed(1)}%`, 'success');
                    updateStatus('API Connection: SUCCESS', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ API Failed: ${error.message}`, 'error');
                updateStatus('API Connection: FAILED', 'error');
            }
        }
        
        function testWebSocket() {
            log('🔌 Testing WebSocket connection...');
            
            if (ws) {
                ws.close();
            }
            
            try {
                ws = new WebSocket('ws://localhost:8090/ws');
                
                ws.onopen = () => {
                    log('✅ WebSocket connected successfully!', 'success');
                    updateStatus('WebSocket: CONNECTED', 'success');
                };
                
                ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    log(`📨 WebSocket message: ${JSON.stringify(data)}`, 'success');
                };
                
                ws.onclose = (event) => {
                    log(`🔌 WebSocket closed: Code=${event.code}, Reason=${event.reason}`, 'error');
                    updateStatus('WebSocket: DISCONNECTED', 'error');
                };
                
                ws.onerror = (error) => {
                    log(`❌ WebSocket error: ${error}`, 'error');
                    updateStatus('WebSocket: ERROR', 'error');
                };
                
                // Timeout test
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        log('⏰ WebSocket connection timeout', 'error');
                        updateStatus('WebSocket: TIMEOUT', 'error');
                    }
                }, 5000);
                
            } catch (error) {
                log(`❌ WebSocket creation failed: ${error.message}`, 'error');
                updateStatus('WebSocket: FAILED', 'error');
            }
        }
        
        async function testCommand() {
            log('⚡ Testing command execution...');
            try {
                const response = await fetch('http://localhost:8090/command', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: 'scan_vendor', payload: {} })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    log(`✅ Command Success: ${result.message}`, 'success');
                    if (result.data) {
                        log(`📄 Command Data: ${JSON.stringify(result.data)}`, 'success');
                    }
                    updateStatus('Command Execution: SUCCESS', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ Command Failed: ${error.message}`, 'error');
                updateStatus('Command Execution: FAILED', 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Auto-test on load
        window.onload = () => {
            log('🚀 Nova Agent Integration Test Started');
            setTimeout(testAPI, 1000);
            setTimeout(testWebSocket, 2000);
        };
    </script>
</body>
</html>
