/**
 * Feature Flag Service Tests
 */

const FeatureFlagService = require('../../../api/services/FeatureFlagService');
const PackageConfigRegistry = require('../../../api/services/PackageConfigRegistry');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

// Mock the PackageConfigRegistry
jest.mock('../../../api/services/PackageConfigRegistry');

describe('FeatureFlagService', () => {
  let featureFlagService;
  let testDir;
  
  beforeEach(async () => {
    // Create a temporary directory for testing
    testDir = path.join(os.tmpdir(), `nova-connect-test-${Date.now()}`);
    await fs.mkdir(testDir, { recursive: true });
    
    // Mock PackageConfigRegistry methods
    PackageConfigRegistry.mockImplementation(() => ({
      hasTenantFeatureAccess: jest.fn().mockResolvedValue(true),
      getTenantFeatureLimit: jest.fn().mockResolvedValue(100),
      getTenantAvailableFeatures: jest.fn().mockResolvedValue(['feature1', 'feature2']),
      getTenantMapping: jest.fn().mockResolvedValue({
        tenantId: 'test-tenant',
        packageId: 'enterprise',
        customFeatures: ['custom.feature1'],
        customLimits: { connections: 200 }
      }),
      getPackageById: jest.fn().mockResolvedValue({
        id: 'enterprise',
        name: 'Enterprise Package',
        features: ['feature1', 'feature2', 'feature3'],
        limits: { connections: 100 }
      }),
      getAllPackages: jest.fn().mockResolvedValue([
        { id: 'core', name: 'Core Package' },
        { id: 'enterprise', name: 'Enterprise Package' }
      ]),
      setTenantMapping: jest.fn().mockResolvedValue({
        tenantId: 'test-tenant',
        packageId: 'enterprise',
        customFeatures: ['custom.feature1'],
        customLimits: { connections: 200 }
      }),
      clearCache: jest.fn()
    }));
    
    // Initialize the feature flag service with the test directory
    featureFlagService = new FeatureFlagService(testDir);
    
    // Mock methods to avoid file system operations
    featureFlagService.loadData = jest.fn().mockResolvedValue([]);
    featureFlagService.saveData = jest.fn().mockResolvedValue();
    
    // Mock methods for user entitlements and subscription tiers
    featureFlagService.getUserEntitlement = jest.fn().mockResolvedValue({
      userId: 'test-user',
      tierId: 'professional',
      customFeatures: ['custom.user.feature'],
      customLimits: {}
    });
    
    featureFlagService.getSubscriptionTierById = jest.fn().mockResolvedValue({
      id: 'professional',
      name: 'Professional',
      features: ['feature1', 'feature2']
    });
    
    featureFlagService.getFeatureFlagById = jest.fn().mockResolvedValue({
      id: 'feature1',
      name: 'Feature 1',
      enabled: true,
      limits: {
        professional: {
          requests_per_day: 1000
        }
      }
    });
    
    featureFlagService.getAllFeatureFlags = jest.fn().mockResolvedValue([
      {
        id: 'feature1',
        name: 'Feature 1',
        enabled: true
      },
      {
        id: 'feature2',
        name: 'Feature 2',
        enabled: true
      },
      {
        id: 'feature3',
        name: 'Feature 3',
        enabled: false
      }
    ]);
  });
  
  afterEach(async () => {
    // Clean up the test directory
    try {
      await fs.rm(testDir, { recursive: true, force: true });
    } catch (error) {
      console.error('Error cleaning up test directory:', error);
    }
    
    // Clear all mocks
    jest.clearAllMocks();
  });
  
  test('should check if user has feature access with tenant ID', async () => {
    // Test with tenant ID
    const hasAccessWithTenant = await featureFlagService.hasFeatureAccess('test-user', 'feature1', 'test-tenant');
    
    // Should check tenant access first
    expect(featureFlagService.packageRegistry.hasTenantFeatureAccess).toHaveBeenCalledWith('test-tenant', 'feature1');
    expect(hasAccessWithTenant).toBe(true);
    
    // Mock tenant access to return false
    featureFlagService.packageRegistry.hasTenantFeatureAccess.mockResolvedValueOnce(false);
    
    // Should fall back to user access
    const hasAccessWithTenantFallback = await featureFlagService.hasFeatureAccess('test-user', 'feature1', 'test-tenant');
    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');
    expect(featureFlagService.getSubscriptionTierById).toHaveBeenCalledWith('professional');
    expect(hasAccessWithTenantFallback).toBe(true);
  });
  
  test('should check if user has feature access without tenant ID', async () => {
    // Test without tenant ID
    const hasAccess = await featureFlagService.hasFeatureAccess('test-user', 'feature1');
    
    // Should not check tenant access
    expect(featureFlagService.packageRegistry.hasTenantFeatureAccess).not.toHaveBeenCalled();
    
    // Should check user access
    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');
    expect(featureFlagService.getSubscriptionTierById).toHaveBeenCalledWith('professional');
    expect(hasAccess).toBe(true);
  });
  
  test('should get feature limit with tenant ID', async () => {
    // Test with tenant ID
    const limitWithTenant = await featureFlagService.getFeatureLimit('test-user', 'feature1', 'connections', 'test-tenant');
    
    // Should check tenant limit first
    expect(featureFlagService.packageRegistry.getTenantFeatureLimit).toHaveBeenCalledWith('test-tenant', 'connections');
    expect(limitWithTenant).toBe(100);
    
    // Mock tenant limit to return null
    featureFlagService.packageRegistry.getTenantFeatureLimit.mockResolvedValueOnce(null);
    
    // Should fall back to user limit
    const limitWithTenantFallback = await featureFlagService.getFeatureLimit('test-user', 'feature1', 'requests_per_day', 'test-tenant');
    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');
    expect(featureFlagService.getFeatureFlagById).toHaveBeenCalledWith('feature1');
    expect(limitWithTenantFallback).toBe(1000);
  });
  
  test('should get feature limit without tenant ID', async () => {
    // Test without tenant ID
    const limit = await featureFlagService.getFeatureLimit('test-user', 'feature1', 'requests_per_day');
    
    // Should not check tenant limit
    expect(featureFlagService.packageRegistry.getTenantFeatureLimit).not.toHaveBeenCalled();
    
    // Should check user limit
    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');
    expect(featureFlagService.getFeatureFlagById).toHaveBeenCalledWith('feature1');
    expect(limit).toBe(1000);
  });
  
  test('should get user available features with tenant ID', async () => {
    // Test with tenant ID
    const featuresWithTenant = await featureFlagService.getUserAvailableFeatures('test-user', 'test-tenant');
    
    // Should get tenant features
    expect(featureFlagService.packageRegistry.getTenantAvailableFeatures).toHaveBeenCalledWith('test-tenant');
    
    // Should also get user features
    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');
    expect(featureFlagService.getSubscriptionTierById).toHaveBeenCalledWith('professional');
    expect(featureFlagService.getAllFeatureFlags).toHaveBeenCalled();
    
    // Should return enabled features that are in tenant features, user features, or subscription features
    expect(featuresWithTenant).toHaveLength(2);
  });
  
  test('should get user available features without tenant ID', async () => {
    // Test without tenant ID
    const features = await featureFlagService.getUserAvailableFeatures('test-user');
    
    // Should not get tenant features
    expect(featureFlagService.packageRegistry.getTenantAvailableFeatures).not.toHaveBeenCalled();
    
    // Should get user features
    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');
    expect(featureFlagService.getSubscriptionTierById).toHaveBeenCalledWith('professional');
    expect(featureFlagService.getAllFeatureFlags).toHaveBeenCalled();
    
    // Should return enabled features that are in user features or subscription features
    expect(features).toHaveLength(2);
  });
  
  test('should get tenant package', async () => {
    // Test getting tenant package
    const pkg = await featureFlagService.getTenantPackage('test-tenant');
    
    // Should get tenant mapping
    expect(featureFlagService.packageRegistry.getTenantMapping).toHaveBeenCalledWith('test-tenant');
    
    // Should get package details
    expect(featureFlagService.packageRegistry.getPackageById).toHaveBeenCalledWith('enterprise');
    
    // Should return package details
    expect(pkg.id).toBe('enterprise');
    expect(pkg.name).toBe('Enterprise Package');
  });
  
  test('should set tenant package', async () => {
    // Test setting tenant package
    const mapping = await featureFlagService.setTenantPackage('test-tenant', 'enterprise', ['custom.feature1'], { connections: 200 });
    
    // Should set tenant mapping
    expect(featureFlagService.packageRegistry.setTenantMapping).toHaveBeenCalledWith('test-tenant', 'enterprise', ['custom.feature1'], { connections: 200 });
    
    // Should return mapping
    expect(mapping.tenantId).toBe('test-tenant');
    expect(mapping.packageId).toBe('enterprise');
  });
  
  test('should get all packages', async () => {
    // Test getting all packages
    const packages = await featureFlagService.getAllPackages();
    
    // Should get all packages
    expect(featureFlagService.packageRegistry.getAllPackages).toHaveBeenCalled();
    
    // Should return packages
    expect(packages).toHaveLength(2);
    expect(packages[0].id).toBe('core');
    expect(packages[1].id).toBe('enterprise');
  });
  
  test('should get package by ID', async () => {
    // Test getting package by ID
    const pkg = await featureFlagService.getPackageById('enterprise');
    
    // Should get package by ID
    expect(featureFlagService.packageRegistry.getPackageById).toHaveBeenCalledWith('enterprise');
    
    // Should return package
    expect(pkg.id).toBe('enterprise');
    expect(pkg.name).toBe('Enterprise Package');
  });
  
  test('should clear cache', async () => {
    // Test clearing cache
    featureFlagService.clearCache();
    
    // Should clear package registry cache
    expect(featureFlagService.packageRegistry.clearCache).toHaveBeenCalled();
  });
});
