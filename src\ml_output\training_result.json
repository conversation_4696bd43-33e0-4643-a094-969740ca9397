{"model_id": "compliance_gap_random_forest_90dc7af1-b48d-4d23-9694-f9edc3c1a837", "model_type": "compliance_gap", "algorithm": "random_forest", "metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "f1": 1.0}, "feature_names": ["requirement_priority", "requirement_status", "requirement_days_until_due", "activity_avg_status", "activity_avg_days_until_due", "requirement_priority", "requirement_status", "requirement_days_until_due", "activity_avg_status", "activity_avg_days_until_due", "requirement_priority", "requirement_status", "requirement_days_until_due", "activity_avg_status", "activity_avg_days_until_due", "requirement_priority", "requirement_status", "requirement_days_until_due", "activity_avg_status", "activity_avg_days_until_due", "requirement_priority", "requirement_status", "requirement_days_until_due", "activity_avg_status", "activity_avg_days_until_due", "requirement_priority", "requirement_status", "requirement_days_until_due", "activity_avg_status", "activity_avg_days_until_due"], "training_data_size": 6}