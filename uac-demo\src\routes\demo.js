/**
 * Demo Routes
 *
 * This module defines demo routes that showcase the UAC's capabilities.
 */

const express = require('express');
const router = express.Router();
const { getLogger } = require('../core/logger');
const { hipaaSamples, gdprSamples, pciSamples, multiApiSamples } = require('./sample-data');

const logger = getLogger('demo-routes');

// Middleware to check authentication
const checkAuth = (req, res, next) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = req.uac.authManager.verifyToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    logger.error('Authentication failed', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};

// Initialize demo data
router.post('/init', checkAuth, (req, res) => {
  // Only admins can initialize demo data
  if (req.user.role !== req.uac.authManager.ROLES.ADMIN) {
    return res.status(403).json({ error: 'Forbidden' });
  }

  try {
    // Register demo APIs
    req.uac.connector.registerApi('weather-api', {
      baseUrl: 'https://api.openweathermap.org/data/2.5',
      type: req.uac.connector.API_TYPES.REST,
      authMethod: req.uac.connector.AUTH_METHODS.API_KEY,
      dataFormat: req.uac.connector.DATA_FORMATS.JSON,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    req.uac.connector.registerApi('github-api', {
      baseUrl: 'https://api.github.com',
      type: req.uac.connector.API_TYPES.REST,
      authMethod: req.uac.connector.AUTH_METHODS.OAUTH2,
      dataFormat: req.uac.connector.DATA_FORMATS.JSON,
      headers: {
        'Accept': 'application/vnd.github.v3+json',
        'Content-Type': 'application/json'
      }
    });

    req.uac.connector.registerApi('hipaa-health-api', {
      baseUrl: 'https://api.example.com/health',
      type: req.uac.connector.API_TYPES.REST,
      authMethod: req.uac.connector.AUTH_METHODS.JWT,
      dataFormat: req.uac.connector.DATA_FORMATS.JSON,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Register demo compliance rules

    // HIPAA Compliance Rules
    req.uac.complianceEngine.registerComplianceRule('hipaa-phi', {
      framework: req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.HIPAA,
      description: 'Check for Protected Health Information (PHI)',
      check: (data) => {
        // Simple check for PHI identifiers
        const phiPatterns = [
          /patient/i,
          /medical record/i,
          /diagnosis/i,
          /treatment/i,
          /ssn/i,
          /social security/i,
          /dob/i,
          /date of birth/i
        ];

        const dataStr = JSON.stringify(data).toLowerCase();
        const containsPhi = phiPatterns.some(pattern => pattern.test(dataStr));

        return {
          compliant: !containsPhi,
          details: {
            message: containsPhi ? 'Data contains PHI identifiers' : 'No PHI identifiers found',
            severity: containsPhi ? 'high' : 'low'
          }
        };
      },
      logData: false
    });

    req.uac.complianceEngine.registerComplianceRule('hipaa-encryption', {
      framework: req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.HIPAA,
      description: 'Check if sensitive data is encrypted',
      check: (data) => {
        // Check if data has encryption indicators
        const dataStr = JSON.stringify(data).toLowerCase();
        const hasEncryption = dataStr.includes('encrypted') || dataStr.includes('encryption');

        return {
          compliant: hasEncryption,
          details: {
            message: hasEncryption ? 'Data appears to be encrypted' : 'No encryption indicators found',
            severity: hasEncryption ? 'low' : 'high',
            recommendation: hasEncryption ? null : 'Implement encryption for all PHI data'
          }
        };
      },
      logData: false
    });

    req.uac.complianceEngine.registerComplianceRule('hipaa-authorization', {
      framework: req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.HIPAA,
      description: 'Check for patient authorization',
      check: (data) => {
        // Check if data has authorization indicators
        const hasAuthorization = data.authorization === true ||
                               (data.consent && data.consent.status === 'approved');

        return {
          compliant: hasAuthorization,
          details: {
            message: hasAuthorization ? 'Patient authorization verified' : 'No patient authorization found',
            severity: hasAuthorization ? 'low' : 'high',
            recommendation: hasAuthorization ? null : 'Obtain patient authorization before processing'
          }
        };
      },
      logData: false
    });

    // GDPR Compliance Rules
    req.uac.complianceEngine.registerComplianceRule('gdpr-pii', {
      framework: req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.GDPR,
      description: 'Check for Personally Identifiable Information (PII)',
      check: (data) => {
        // Simple check for PII identifiers
        const piiPatterns = [
          /email/i,
          /address/i,
          /phone/i,
          /passport/i,
          /id card/i,
          /credit card/i,
          /name/i
        ];

        const dataStr = JSON.stringify(data).toLowerCase();
        const containsPii = piiPatterns.some(pattern => pattern.test(dataStr));

        return {
          compliant: !containsPii,
          details: {
            message: containsPii ? 'Data contains PII identifiers' : 'No PII identifiers found',
            severity: containsPii ? 'medium' : 'low'
          }
        };
      },
      logData: false
    });

    req.uac.complianceEngine.registerComplianceRule('gdpr-location-anonymization', {
      framework: req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.GDPR,
      description: 'Check if location data is anonymized',
      check: (data) => {
        // Check if location data exists and is anonymized
        const hasLocation = data.location !== undefined ||
                          (data.address !== undefined) ||
                          JSON.stringify(data).toLowerCase().includes('location');

        const isAnonymized = data.anonymized === true ||
                           (data.location && data.location.includes('anonymized')) ||
                           JSON.stringify(data).toLowerCase().includes('anonymized');

        // If no location data, it's compliant
        if (!hasLocation) {
          return {
            compliant: true,
            details: {
              message: 'No location data found',
              severity: 'low'
            }
          };
        }

        return {
          compliant: isAnonymized,
          details: {
            message: isAnonymized ? 'Location data is anonymized' : 'Location data is not anonymized',
            severity: isAnonymized ? 'low' : 'high',
            recommendation: isAnonymized ? null : 'Anonymize location data before processing'
          }
        };
      },
      logData: false
    });

    req.uac.complianceEngine.registerComplianceRule('gdpr-consent', {
      framework: req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.GDPR,
      description: 'Check for user consent',
      check: (data) => {
        // Check if data has consent indicators
        const hasConsent = data.consent === true ||
                         (data.preferences && data.preferences.marketing !== undefined) ||
                         (data.lastConsent && new Date(data.lastConsent) > new Date(Date.now() - 365 * 24 * 60 * 60 * 1000));

        return {
          compliant: hasConsent,
          details: {
            message: hasConsent ? 'User consent verified' : 'No user consent found or consent expired',
            severity: hasConsent ? 'low' : 'high',
            recommendation: hasConsent ? null : 'Obtain user consent before processing data'
          }
        };
      },
      logData: false
    });

    req.uac.complianceEngine.registerComplianceRule('gdpr-data-retention', {
      framework: req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.GDPR,
      description: 'Check data retention period',
      check: (data) => {
        // Check if lastVisit is within 12 months
        if (data.lastVisit) {
          const lastVisitDate = new Date(data.lastVisit);
          const twelveMonthsAgo = new Date();
          twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

          const isWithinRetention = lastVisitDate > twelveMonthsAgo;

          return {
            compliant: isWithinRetention,
            details: {
              message: isWithinRetention ? 'Data is within retention period' : 'Data retention period exceeded',
              severity: isWithinRetention ? 'low' : 'medium',
              recommendation: isWithinRetention ? null : 'Delete or anonymize data older than 12 months'
            }
          };
        }

        // If no lastVisit data, we can't determine
        return {
          compliant: true,
          details: {
            message: 'No timestamp data found to evaluate retention',
            severity: 'low'
          }
        };
      },
      logData: false
    });

    // PCI DSS Compliance Rules
    req.uac.complianceEngine.registerComplianceRule('pci-dss-card-data', {
      framework: req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.PCI_DSS,
      description: 'Check for credit card data',
      check: (data) => {
        // Simple check for credit card patterns
        const cardPatterns = [
          /card number/i,
          /cvv/i,
          /cvc/i,
          /expiration/i,
          /\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}/
        ];

        const dataStr = JSON.stringify(data).toLowerCase();
        const containsCardData = cardPatterns.some(pattern => pattern.test(dataStr));

        return {
          compliant: !containsCardData,
          details: {
            message: containsCardData ? 'Data contains credit card information' : 'No credit card information found',
            severity: containsCardData ? 'critical' : 'low'
          }
        };
      },
      logData: false
    });

    req.uac.complianceEngine.registerComplianceRule('pci-dss-encryption', {
      framework: req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.PCI_DSS,
      description: 'Check for encryption of payment data',
      check: (data) => {
        // Check if payment data is encrypted
        const hasPaymentData = data.cardNumber !== undefined ||
                             data.cvv !== undefined ||
                             JSON.stringify(data).toLowerCase().includes('card');

        const isEncrypted = data.encrypted === true ||
                          (data.cardNumber && data.cardNumber.startsWith('****')) ||
                          JSON.stringify(data).toLowerCase().includes('encrypted');

        // If no payment data, it's compliant
        if (!hasPaymentData) {
          return {
            compliant: true,
            details: {
              message: 'No payment data found',
              severity: 'low'
            }
          };
        }

        return {
          compliant: isEncrypted,
          details: {
            message: isEncrypted ? 'Payment data is encrypted' : 'Payment data is not encrypted',
            severity: isEncrypted ? 'low' : 'critical',
            recommendation: isEncrypted ? null : 'Encrypt all payment data using PCI-approved methods'
          }
        };
      },
      logData: false
    });

    req.uac.complianceEngine.registerComplianceRule('pci-dss-cvv-storage', {
      framework: req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.PCI_DSS,
      description: 'Check for CVV storage',
      check: (data) => {
        // Check if CVV is being stored
        const hasCvv = data.cvv !== undefined ||
                     JSON.stringify(data).toLowerCase().includes('cvv') ||
                     JSON.stringify(data).toLowerCase().includes('cvc');

        return {
          compliant: !hasCvv,
          details: {
            message: hasCvv ? 'CVV data is being stored' : 'No CVV data found',
            severity: hasCvv ? 'critical' : 'low',
            recommendation: hasCvv ? 'Never store CVV data after authorization' : null
          }
        };
      },
      logData: false
    });

    res.json({ message: 'Demo data initialized successfully' });
  } catch (error) {
    logger.error('Demo initialization failed', error);
    res.status(500).json({ error: error.message });
  }
});

// HIPAA compliance demo
router.post('/hipaa-check', checkAuth, (req, res) => {
  const { patientData } = req.body;

  if (!patientData) {
    return res.status(400).json({ error: 'Patient data is required' });
  }

  try {
    // Check HIPAA compliance
    const result = req.uac.complianceEngine.checkFrameworkCompliance(
      req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.HIPAA,
      patientData
    );

    res.json({
      data: patientData,
      compliance: result
    });
  } catch (error) {
    logger.error('HIPAA compliance check failed', error);
    res.status(500).json({ error: error.message });
  }
});

// GDPR compliance demo
router.post('/gdpr-check', checkAuth, (req, res) => {
  const { userData } = req.body;

  if (!userData) {
    return res.status(400).json({ error: 'User data is required' });
  }

  try {
    // Check GDPR compliance
    const result = req.uac.complianceEngine.checkFrameworkCompliance(
      req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.GDPR,
      userData
    );

    res.json({
      data: userData,
      compliance: result
    });
  } catch (error) {
    logger.error('GDPR compliance check failed', error);
    res.status(500).json({ error: error.message });
  }
});

// PCI DSS compliance demo
router.post('/pci-check', checkAuth, (req, res) => {
  const { paymentData } = req.body;

  if (!paymentData) {
    return res.status(400).json({ error: 'Payment data is required' });
  }

  try {
    // Check PCI DSS compliance
    const result = req.uac.complianceEngine.checkFrameworkCompliance(
      req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.PCI_DSS,
      paymentData
    );

    res.json({
      data: paymentData,
      compliance: result
    });
  } catch (error) {
    logger.error('PCI DSS compliance check failed', error);
    res.status(500).json({ error: error.message });
  }
});

// Sample data routes
router.get('/samples/hipaa/:type', checkAuth, (req, res) => {
  const { type } = req.params;

  if (!hipaaSamples[type]) {
    return res.status(404).json({ error: `Sample type '${type}' not found` });
  }

  res.json(hipaaSamples[type]);
});

router.get('/samples/gdpr/:type', checkAuth, (req, res) => {
  const { type } = req.params;

  if (!gdprSamples[type]) {
    return res.status(404).json({ error: `Sample type '${type}' not found` });
  }

  res.json(gdprSamples[type]);
});

router.get('/samples/pci/:type', checkAuth, (req, res) => {
  const { type } = req.params;

  if (!pciSamples[type]) {
    return res.status(404).json({ error: `Sample type '${type}' not found` });
  }

  res.json(pciSamples[type]);
});

router.get('/samples/multi-api/:type', checkAuth, (req, res) => {
  const { type } = req.params;

  if (!multiApiSamples[type]) {
    return res.status(404).json({ error: `Sample type '${type}' not found` });
  }

  res.json(multiApiSamples[type]);
});

// Multi-API demo
router.post('/multi-api', checkAuth, async (req, res) => {
  try {
    // Get data from multiple APIs
    const results = {};

    // This is a simulated call - in a real implementation, we would make actual API calls
    results.weather = {
      status: 200,
      data: {
        location: 'New York',
        temperature: 72,
        conditions: 'Sunny'
      }
    };

    results.github = {
      status: 200,
      data: {
        repositories: 120,
        stars: 1500,
        followers: 300
      }
    };

    results.health = {
      status: 200,
      data: {
        appointments: 5,
        prescriptions: 2,
        lastVisit: '2023-01-15'
      }
    };

    // Apply compliance checks to the results
    const complianceResults = {};

    complianceResults.weather = req.uac.complianceEngine.checkFrameworkCompliance(
      req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.GDPR,
      results.weather.data
    );

    complianceResults.github = req.uac.complianceEngine.checkFrameworkCompliance(
      req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.GDPR,
      results.github.data
    );

    complianceResults.health = req.uac.complianceEngine.checkFrameworkCompliance(
      req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS.HIPAA,
      results.health.data
    );

    res.json({
      apiResults: results,
      complianceResults
    });
  } catch (error) {
    logger.error('Multi-API demo failed', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
