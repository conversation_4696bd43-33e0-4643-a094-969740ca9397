#!/usr/bin/env python3
"""
Metrics Logger for π-Coherence Benchmarking
Logs and tracks real AI performance improvements with π-coherence optimization
"""

import json
import os
import time
from datetime import datetime
from typing import Dict, Any, List

def log_result(benchmark_name: str, result: Dict[str, Any]):
    """
    Log benchmark result to JSON file
    
    Args:
        benchmark_name: Name of the benchmark
        result: Dictionary containing benchmark results
    """
    # Create output directory
    os.makedirs("output", exist_ok=True)
    
    # Add timestamp to result
    result['timestamp'] = datetime.now().isoformat()
    result['unix_timestamp'] = time.time()
    
    # Load existing results
    results_path = "output/benchmark_results.json"
    try:
        with open(results_path, "r") as f:
            data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        data = {}
    
    # Add new result
    if benchmark_name not in data:
        data[benchmark_name] = []
    
    data[benchmark_name].append(result)
    
    # Save updated results
    with open(results_path, "w") as f:
        json.dump(data, f, indent=4)
    
    # Print result to console
    print(f"[{benchmark_name.upper()}] {result}")
    
    # Also save individual result file
    individual_path = f"output/{benchmark_name}_{int(time.time())}.json"
    with open(individual_path, "w") as f:
        json.dump({benchmark_name: result}, f, indent=4)

def log_summary(summary: Dict[str, Any]):
    """
    Log benchmark summary
    
    Args:
        summary: Dictionary containing summary statistics
    """
    os.makedirs("output", exist_ok=True)
    
    summary['timestamp'] = datetime.now().isoformat()
    summary['unix_timestamp'] = time.time()
    
    summary_path = "output/benchmark_summary.json"
    with open(summary_path, "w") as f:
        json.dump(summary, f, indent=4)
    
    print(f"[SUMMARY] {summary}")

def get_all_results() -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all benchmark results
    
    Returns:
        Dictionary containing all benchmark results
    """
    results_path = "output/benchmark_results.json"
    try:
        with open(results_path, "r") as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def calculate_performance_summary() -> Dict[str, Any]:
    """
    Calculate performance summary from all results
    
    Returns:
        Dictionary containing performance summary statistics
    """
    all_results = get_all_results()
    
    if not all_results:
        return {"status": "no_results"}
    
    total_benchmarks = 0
    total_gain = 0
    positive_gains = 0
    benchmark_summaries = {}
    
    for benchmark_name, results in all_results.items():
        if not results:
            continue
            
        # Calculate stats for this benchmark
        gains = [r.get('gain_percent', 0) for r in results]
        avg_gain = sum(gains) / len(gains) if gains else 0
        max_gain = max(gains) if gains else 0
        min_gain = min(gains) if gains else 0
        
        benchmark_summaries[benchmark_name] = {
            'runs': len(results),
            'average_gain_percent': round(avg_gain, 2),
            'max_gain_percent': round(max_gain, 2),
            'min_gain_percent': round(min_gain, 2),
            'positive_gains': sum(1 for g in gains if g > 0)
        }
        
        total_benchmarks += len(results)
        total_gain += sum(gains)
        positive_gains += sum(1 for g in gains if g > 0)
    
    overall_avg_gain = total_gain / total_benchmarks if total_benchmarks > 0 else 0
    success_rate = (positive_gains / total_benchmarks * 100) if total_benchmarks > 0 else 0
    
    summary = {
        'total_benchmark_runs': total_benchmarks,
        'overall_average_gain_percent': round(overall_avg_gain, 2),
        'positive_gain_runs': positive_gains,
        'success_rate_percent': round(success_rate, 2),
        'benchmark_details': benchmark_summaries,
        'pi_coherence_effective': overall_avg_gain > 0
    }
    
    return summary

def print_performance_report():
    """Print a formatted performance report"""
    summary = calculate_performance_summary()
    
    if summary.get('status') == 'no_results':
        print("📊 No benchmark results available")
        return
    
    print("\n" + "="*60)
    print("📊 π-COHERENCE PERFORMANCE REPORT")
    print("="*60)
    print(f"Total Benchmark Runs: {summary['total_benchmark_runs']}")
    print(f"Overall Average Gain: {summary['overall_average_gain_percent']:+.2f}%")
    print(f"Positive Gain Runs: {summary['positive_gain_runs']}")
    print(f"Success Rate: {summary['success_rate_percent']:.1f}%")
    print(f"π-Coherence Effective: {'✅ YES' if summary['pi_coherence_effective'] else '❌ NO'}")
    
    print("\n📋 BENCHMARK DETAILS:")
    for benchmark_name, details in summary['benchmark_details'].items():
        print(f"  {benchmark_name}:")
        print(f"    Runs: {details['runs']}")
        print(f"    Avg Gain: {details['average_gain_percent']:+.2f}%")
        print(f"    Max Gain: {details['max_gain_percent']:+.2f}%")
        print(f"    Positive Gains: {details['positive_gains']}/{details['runs']}")
    
    print("="*60)

if __name__ == "__main__":
    # Test the logger
    print("🧪 Testing Metrics Logger")
    
    # Test logging a result
    test_result = {
        "control_latency_ms": 45.2,
        "pi_latency_ms": 42.8,
        "gain_percent": 5.3,
        "model": "test_model"
    }
    
    log_result("test_benchmark", test_result)
    
    # Test summary calculation
    summary = calculate_performance_summary()
    print(f"Summary: {summary}")
    
    # Test performance report
    print_performance_report()
