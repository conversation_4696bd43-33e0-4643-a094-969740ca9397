[{"id": "free", "name": "Free", "description": "Basic functionality for small projects", "price": 0, "features": ["core.basic_connectors", "core.manual_execution", "core.basic_monitoring", "workflow.basic", "security.basic"], "limits": {"connections": 3, "operations_per_day": 50, "workflows": 1, "actions_per_workflow": 5}}, {"id": "standard", "name": "Standard", "description": "Standard functionality for growing teams", "price": 49, "features": ["core.basic_connectors", "core.manual_execution", "core.basic_monitoring", "workflow.basic", "workflow.scheduled", "export_import.basic", "security.basic", "monitoring.advanced", "analytics.basic"], "limits": {"connections": 10, "operations_per_day": 500, "workflows": 5, "actions_per_workflow": 10, "scheduled_workflows": 2}}, {"id": "professional", "name": "Professional", "description": "Advanced functionality for professional teams", "price": 149, "features": ["core.basic_connectors", "core.manual_execution", "core.basic_monitoring", "workflow.basic", "workflow.advanced", "workflow.scheduled", "workflow.event_triggered", "export_import.basic", "export_import.advanced", "security.basic", "security.advanced", "security.ip_restrictions", "security.encryption", "monitoring.advanced", "monitoring.alerting", "monitoring.anomaly_detection", "analytics.basic", "analytics.advanced", "analytics.export_formats", "ai.connector_generation", "ai.natural_language", "ai.error_resolution", "governance.approvals", "governance.compliance"], "limits": {"connections": 50, "operations_per_day": 5000, "workflows": 20, "actions_per_workflow": 50, "scheduled_workflows": 10, "alerts": 10}}, {"id": "enterprise", "name": "Enterprise", "description": "Enterprise-grade functionality for large organizations", "price": 499, "features": ["core.basic_connectors", "core.manual_execution", "core.basic_monitoring", "workflow.basic", "workflow.advanced", "workflow.scheduled", "workflow.event_triggered", "export_import.basic", "export_import.advanced", "security.basic", "security.advanced", "security.enterprise", "security.ip_restrictions", "security.encryption", "security.policies", "monitoring.advanced", "monitoring.alerting", "monitoring.anomaly_detection", "analytics.basic", "analytics.advanced", "analytics.custom_reports", "analytics.scheduled_reports", "analytics.export_formats", "ai.connector_generation", "ai.natural_language", "ai.error_resolution", "ai.workflow_optimization", "governance.approvals", "governance.compliance", "governance.data_lineage"], "limits": {"connections": -1, "operations_per_day": -1, "workflows": -1, "actions_per_workflow": -1, "scheduled_workflows": -1, "alerts": -1}}]