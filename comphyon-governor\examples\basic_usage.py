"""
Basic usage example for the ComphyonΨᶜ Governor.
"""

import sys
import os
import time
import numpy as np

# Add the parent directory to the path so we can import the package
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.comphyon_governor import ComphyonGovernor

# For this example, we'll simulate the ComphyonMeter
class MockComphyonMeter:
    """Mock ComphyonMeter for demonstration purposes."""
    
    def __init__(self):
        """Initialize the mock meter."""
        self.time_start = time.time()
    
    def calculate(self, csde_tensor, csfe_tensor, csme_tensor):
        """
        Calculate mock ComphyonΨᶜ metrics.
        
        Returns:
            dict: Mock metrics
        """
        # Calculate time since start
        t = time.time() - self.time_start
        
        # Simulate increasing acceleration over time
        base_acceleration = 0.5 + (t / 60)  # Increases by 1.0 every minute
        
        # Add some oscillation
        oscillation = 0.2 * np.sin(t / 5)
        
        # Calculate metrics
        acceleration = base_acceleration + oscillation
        velocity = 50 + (10 * np.sin(t / 10))
        
        return {
            'timestamp': time.time(),
            'acceleration': acceleration,
            'velocity': velocity,
            'domain_energies': {
                'E_CSDE': 0.75,
                'E_CSFE': 0.65,
                'E_CSME': 0.70
            }
        }

def main():
    """
    Demonstrate basic usage of the ComphyonΨᶜ Governor.
    """
    print("ComphyonΨᶜ Governor - Basic Usage Example")
    print("=========================================")
    
    # Initialize the mock meter
    meter = MockComphyonMeter()
    
    # Initialize the governor with custom thresholds
    governor = ComphyonGovernor(
        thresholds={
            'acceleration': 1.5,  # Trigger control at 1.5 Cph
            'velocity': 60.0      # Trigger control at 60.0 Cph-Flux
        }
    )
    
    print(f"Thresholds: Acceleration = {governor.thresholds['acceleration']} Cph, "
          f"Velocity = {governor.thresholds['velocity']} Cph-Flux")
    
    # Monitoring and control loop
    print("\nStarting monitoring and control loop...")
    print("(Press Ctrl+C to exit)")
    print("\n{:<10} {:<15} {:<15} {:<20} {:<20}".format(
        "Time (s)", "Acceleration", "Velocity", "Control Action", "Reason"))
    print("-" * 80)
    
    start_time = time.time()
    
    try:
        while True:
            # Generate mock tensor data
            csde_tensor = [0.75, 0.85, 0.65, 0.90]
            csfe_tensor = [0.65, 0.70, 0.80, 0.80]
            csme_tensor = [0.70, 0.90, 0.60, 0.85]
            
            # Calculate metrics
            metrics = meter.calculate(csde_tensor, csfe_tensor, csme_tensor)
            
            # Apply control actions if needed
            control_actions = governor.regulate(metrics)
            
            # Display status
            elapsed = time.time() - start_time
            print("{:<10.1f} {:<15.4f} {:<15.4f} {:<20} {:<20}".format(
                elapsed,
                metrics['acceleration'],
                metrics['velocity'],
                control_actions['type'],
                control_actions.get('reason', 'N/A')[:20]
            ))
            
            # Sleep for a bit
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user.")
    
    # Display control history
    print("\nControl History:")
    print("-" * 80)
    
    for i, event in enumerate(governor.control_history[-5:]):
        print(f"Event {i+1}:")
        print(f"  Timestamp: {time.ctime(event['timestamp'])}")
        print(f"  Metrics: Acceleration = {event['metrics']['acceleration']:.4f}, "
              f"Velocity = {event['metrics']['velocity']:.4f}")
        print(f"  Control Type: {event['control_actions']['type']}")
        print(f"  Reason: {event['control_actions'].get('reason', 'N/A')}")
        print(f"  Actions: {len(event['control_actions']['actions'])} actions applied")
        print()
    
    print("Example completed successfully.")

if __name__ == "__main__":
    main()
