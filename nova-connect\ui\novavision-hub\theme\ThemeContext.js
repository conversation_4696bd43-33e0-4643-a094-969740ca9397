/**
 * Theme Context
 * 
 * This module provides a context for theme management in the NovaVision Hub.
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import { defaultTheme } from './themes';

// Create theme context
const ThemeContext = createContext();

/**
 * Theme Provider component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Object} [props.initialTheme] - Initial theme
 * @param {boolean} [props.enableSystemPreference=true] - Whether to enable system preference detection
 * @returns {React.ReactElement} Theme Provider component
 */
export const ThemeProvider = ({ 
  children, 
  initialTheme = defaultTheme,
  enableSystemPreference = true
}) => {
  // State for current theme
  const [theme, setTheme] = useState(initialTheme);
  
  // State for color mode (light or dark)
  const [colorMode, setColorMode] = useState('light');
  
  // Effect to detect system color scheme preference
  useEffect(() => {
    if (enableSystemPreference) {
      // Check if the browser supports prefers-color-scheme
      if (window.matchMedia) {
        // Get system preference
        const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        // Set initial color mode based on system preference
        setColorMode(prefersDarkMode ? 'dark' : 'light');
        
        // Create media query list
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        
        // Add listener for changes
        const handleChange = (e) => {
          setColorMode(e.matches ? 'dark' : 'light');
        };
        
        // Add event listener
        mediaQuery.addEventListener('change', handleChange);
        
        // Cleanup
        return () => {
          mediaQuery.removeEventListener('change', handleChange);
        };
      }
    }
  }, [enableSystemPreference]);
  
  // Update theme when color mode changes
  useEffect(() => {
    if (theme.modes && theme.modes[colorMode]) {
      setTheme(prevTheme => ({
        ...prevTheme,
        colors: {
          ...prevTheme.colors,
          ...theme.modes[colorMode].colors
        }
      }));
    }
  }, [colorMode, theme.modes]);
  
  /**
   * Set theme
   * 
   * @param {Object|Function} newTheme - New theme or function to update theme
   */
  const setThemeValue = (newTheme) => {
    if (typeof newTheme === 'function') {
      setTheme(prevTheme => {
        const updatedTheme = newTheme(prevTheme);
        return updatedTheme;
      });
    } else {
      setTheme(newTheme);
    }
  };
  
  /**
   * Update theme
   * 
   * @param {Object} themeUpdates - Theme updates
   */
  const updateTheme = (themeUpdates) => {
    setTheme(prevTheme => ({
      ...prevTheme,
      ...themeUpdates,
      colors: {
        ...prevTheme.colors,
        ...(themeUpdates.colors || {})
      },
      typography: {
        ...prevTheme.typography,
        ...(themeUpdates.typography || {})
      },
      spacing: {
        ...prevTheme.spacing,
        ...(themeUpdates.spacing || {})
      },
      breakpoints: {
        ...prevTheme.breakpoints,
        ...(themeUpdates.breakpoints || {})
      },
      shadows: {
        ...prevTheme.shadows,
        ...(themeUpdates.shadows || {})
      },
      radii: {
        ...prevTheme.radii,
        ...(themeUpdates.radii || {})
      },
      zIndices: {
        ...prevTheme.zIndices,
        ...(themeUpdates.zIndices || {})
      }
    }));
  };
  
  /**
   * Toggle color mode
   */
  const toggleColorMode = () => {
    setColorMode(prevMode => prevMode === 'light' ? 'dark' : 'light');
  };
  
  /**
   * Set color mode
   * 
   * @param {string} mode - Color mode (light or dark)
   */
  const setColorModeValue = (mode) => {
    if (mode === 'light' || mode === 'dark') {
      setColorMode(mode);
    } else {
      console.warn(`Invalid color mode: ${mode}. Expected 'light' or 'dark'.`);
    }
  };
  
  // Create context value
  const contextValue = {
    theme,
    setTheme: setThemeValue,
    updateTheme,
    colorMode,
    toggleColorMode,
    setColorMode: setColorModeValue
  };
  
  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Use theme hook
 * 
 * @returns {Object} Theme context value
 */
export const useTheme = () => {
  const context = useContext(ThemeContext);
  
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  return context;
};

export default ThemeContext;
