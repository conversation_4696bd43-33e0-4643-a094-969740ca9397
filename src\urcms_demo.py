"""
Demo script for the Universal Regulatory Change Management System (URCMS).

This script demonstrates how to use the URCMS to monitor, analyze, and respond
to regulatory changes.
"""

import os
import sys
import json
import logging
import time
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the URCMS
from urcms import ChangeManager

def custom_change_handler(change: Dict[str, Any], impact: Dict[str, Any]) -> None:
    """
    Custom handler for regulatory changes.
    
    Args:
        change: The regulatory change
        impact: The impact analysis
    """
    logger.info(f"Custom change handler: {change.get('id')}")
    logger.info(f"Impact level: {impact.get('impact_level')}")
    logger.info(f"Required actions: {len(impact.get('required_actions', []))}")

def main():
    """Run the URCMS demo."""
    logger.info("Starting URCMS demo")
    
    # Initialize the Change Manager
    manager = ChangeManager()
    
    # Register a custom change handler for GDPR changes
    manager.register_change_handler('gdpr', custom_change_handler)
    
    # Create output directory if it doesn't exist
    output_dir = os.path.join(os.path.dirname(__file__), 'urcms_output')
    os.makedirs(output_dir, exist_ok=True)
    
    # Get available regulation types
    regulation_types = manager.get_regulation_types()
    logger.info(f"Available regulation types: {regulation_types}")
    
    # Get regulations for each type
    for regulation_type in regulation_types:
        regulations = manager.get_regulations(regulation_type)
        logger.info(f"Found {len(regulations)} regulations for type: {regulation_type}")
        
        # Save regulations to files
        for regulation in regulations:
            regulation_id = regulation['id']
            regulation_path = os.path.join(output_dir, f"{regulation_id}.json")
            
            with open(regulation_path, 'w', encoding='utf-8') as f:
                json.dump(regulation, f, indent=2)
            
            logger.info(f"Saved regulation {regulation_id} to {regulation_path}")
    
    # Start monitoring for regulatory changes
    logger.info("Starting regulatory change monitoring")
    manager.start_monitoring()
    
    # Wait for some changes to be detected
    logger.info("Waiting for regulatory changes...")
    time.sleep(5)
    
    # Check for changes
    changes = manager.check_for_changes()
    logger.info(f"Found {len(changes)} regulatory changes")
    
    # Process each change
    for change in changes:
        change_id = change['id']
        logger.info(f"Processing regulatory change: {change_id}")
        
        # Get the impact analysis
        impact = manager.get_impact_analysis(change_id)
        logger.info(f"Impact level: {impact.get('impact_level')}")
        
        # Save the change and impact to files
        change_path = os.path.join(output_dir, f"{change_id}.json")
        impact_path = os.path.join(output_dir, f"{change_id}_impact.json")
        
        with open(change_path, 'w', encoding='utf-8') as f:
            json.dump(change, f, indent=2)
        
        with open(impact_path, 'w', encoding='utf-8') as f:
            json.dump(impact, f, indent=2)
        
        logger.info(f"Saved change {change_id} to {change_path}")
        logger.info(f"Saved impact analysis to {impact_path}")
    
    # Stop monitoring
    logger.info("Stopping regulatory change monitoring")
    manager.stop_monitoring()
    
    logger.info("URCMS demo completed successfully")

if __name__ == "__main__":
    main()
