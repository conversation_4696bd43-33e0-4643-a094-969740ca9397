/**
 * NovaFuse API Superstore Encryption Utility
 * 
 * This module provides comprehensive encryption capabilities for the NovaFuse API Superstore.
 * It implements industry-standard encryption algorithms and best practices for securing
 * sensitive data in transit and at rest.
 * 
 * Features:
 * - Symmetric encryption (AES-256-GCM)
 * - Asymmetric encryption (RSA)
 * - Key derivation (PBKDF2)
 * - Secure hashing (SHA-256, SHA-512)
 * - JWT token encryption
 * - API key encryption
 * - Secure random generation
 * - Data masking
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  // Symmetric encryption settings
  symmetric: {
    algorithm: 'aes-256-gcm',
    keyLength: 32, // 256 bits
    ivLength: 16, // 128 bits
    tagLength: 16, // 128 bits
    saltLength: 64 // 512 bits
  },
  // Asymmetric encryption settings
  asymmetric: {
    algorithm: 'rsa',
    modulusLength: 4096,
    publicExponent: 0x10001, // 65537
    hashAlgorithm: 'sha512',
    saltLength: 64 // 512 bits
  },
  // Key derivation settings
  keyDerivation: {
    algorithm: 'pbkdf2',
    iterations: 310000, // OWASP recommended minimum
    hashAlgorithm: 'sha512',
    saltLength: 64 // 512 bits
  },
  // Hashing settings
  hashing: {
    algorithm: 'sha512',
    saltLength: 64 // 512 bits
  },
  // JWT settings
  jwt: {
    algorithm: 'HS512',
    expiresIn: '1h'
  }
};

/**
 * Generates a cryptographically secure random buffer of specified length
 * 
 * @param {number} length - Length of the random buffer in bytes
 * @returns {Buffer} - Random buffer
 */
function generateRandomBytes(length) {
  return crypto.randomBytes(length);
}

/**
 * Generates a cryptographically secure random string of specified length
 * 
 * @param {number} length - Length of the random string
 * @returns {string} - Random string in hex format
 */
function generateRandomString(length) {
  return crypto.randomBytes(Math.ceil(length / 2)).toString('hex').slice(0, length);
}

/**
 * Derives a key from a password using PBKDF2
 * 
 * @param {string} password - Password to derive key from
 * @param {Buffer} salt - Salt for key derivation (if null, a new salt is generated)
 * @param {number} keyLength - Length of the derived key in bytes
 * @returns {Object} - Object containing the derived key and salt
 */
function deriveKey(password, salt = null, keyLength = CONFIG.symmetric.keyLength) {
  if (!salt) {
    salt = generateRandomBytes(CONFIG.keyDerivation.saltLength);
  }
  
  const key = crypto.pbkdf2Sync(
    password,
    salt,
    CONFIG.keyDerivation.iterations,
    keyLength,
    CONFIG.keyDerivation.hashAlgorithm
  );
  
  return {
    key,
    salt
  };
}

/**
 * Creates a secure hash of data using SHA-512 with salt
 * 
 * @param {string|Buffer} data - Data to hash
 * @param {Buffer} salt - Salt for hashing (if null, a new salt is generated)
 * @returns {Object} - Object containing the hash and salt
 */
function createHash(data, salt = null) {
  if (!salt) {
    salt = generateRandomBytes(CONFIG.hashing.saltLength);
  }
  
  const hash = crypto.createHash(CONFIG.hashing.algorithm)
    .update(Buffer.concat([salt, Buffer.from(data)]))
    .digest();
  
  return {
    hash: hash.toString('hex'),
    salt: salt.toString('hex')
  };
}

/**
 * Verifies a hash against data
 * 
 * @param {string|Buffer} data - Data to verify
 * @param {string} hash - Hash to verify against
 * @param {string} salt - Salt used for hashing
 * @returns {boolean} - True if hash matches, false otherwise
 */
function verifyHash(data, hash, salt) {
  const saltBuffer = Buffer.from(salt, 'hex');
  const computedHash = crypto.createHash(CONFIG.hashing.algorithm)
    .update(Buffer.concat([saltBuffer, Buffer.from(data)]))
    .digest()
    .toString('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(computedHash, 'hex'),
    Buffer.from(hash, 'hex')
  );
}

/**
 * Encrypts data using AES-256-GCM
 * 
 * @param {string|Buffer} data - Data to encrypt
 * @param {Buffer|string} key - Encryption key
 * @returns {Object} - Object containing encrypted data, IV, and auth tag
 */
function encryptSymmetric(data, key) {
  if (typeof key === 'string') {
    // Derive key from password
    const derivedKey = deriveKey(key);
    key = derivedKey.key;
  }
  
  const iv = generateRandomBytes(CONFIG.symmetric.ivLength);
  const cipher = crypto.createCipheriv(CONFIG.symmetric.algorithm, key, iv);
  
  let encrypted;
  if (Buffer.isBuffer(data)) {
    encrypted = Buffer.concat([cipher.update(data), cipher.final()]);
  } else {
    encrypted = Buffer.concat([cipher.update(data, 'utf8'), cipher.final()]);
  }
  
  const authTag = cipher.getAuthTag();
  
  return {
    encrypted: encrypted.toString('base64'),
    iv: iv.toString('base64'),
    authTag: authTag.toString('base64')
  };
}

/**
 * Decrypts data using AES-256-GCM
 * 
 * @param {string} encryptedData - Encrypted data in base64
 * @param {string} iv - Initialization vector in base64
 * @param {string} authTag - Authentication tag in base64
 * @param {Buffer|string} key - Decryption key
 * @param {string} outputEncoding - Output encoding (default: 'utf8')
 * @returns {string|Buffer} - Decrypted data
 */
function decryptSymmetric(encryptedData, iv, authTag, key, outputEncoding = 'utf8') {
  if (typeof key === 'string') {
    // Derive key from password
    const derivedKey = deriveKey(key);
    key = derivedKey.key;
  }
  
  const decipher = crypto.createDecipheriv(
    CONFIG.symmetric.algorithm,
    key,
    Buffer.from(iv, 'base64')
  );
  
  decipher.setAuthTag(Buffer.from(authTag, 'base64'));
  
  const encrypted = Buffer.from(encryptedData, 'base64');
  const decrypted = Buffer.concat([decipher.update(encrypted), decipher.final()]);
  
  return outputEncoding ? decrypted.toString(outputEncoding) : decrypted;
}

/**
 * Generates an RSA key pair
 * 
 * @returns {Object} - Object containing public and private keys in PEM format
 */
function generateKeyPair() {
  const { publicKey, privateKey } = crypto.generateKeyPairSync(CONFIG.asymmetric.algorithm, {
    modulusLength: CONFIG.asymmetric.modulusLength,
    publicExponent: CONFIG.asymmetric.publicExponent,
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem'
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem'
    }
  });
  
  return {
    publicKey,
    privateKey
  };
}

/**
 * Encrypts data using RSA
 * 
 * @param {string|Buffer} data - Data to encrypt
 * @param {string} publicKey - Public key in PEM format
 * @returns {string} - Encrypted data in base64
 */
function encryptAsymmetric(data, publicKey) {
  const encrypted = crypto.publicEncrypt(
    {
      key: publicKey,
      padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
      oaepHash: CONFIG.asymmetric.hashAlgorithm
    },
    Buffer.isBuffer(data) ? data : Buffer.from(data)
  );
  
  return encrypted.toString('base64');
}

/**
 * Decrypts data using RSA
 * 
 * @param {string} encryptedData - Encrypted data in base64
 * @param {string} privateKey - Private key in PEM format
 * @param {string} outputEncoding - Output encoding (default: 'utf8')
 * @returns {string|Buffer} - Decrypted data
 */
function decryptAsymmetric(encryptedData, privateKey, outputEncoding = 'utf8') {
  const decrypted = crypto.privateDecrypt(
    {
      key: privateKey,
      padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
      oaepHash: CONFIG.asymmetric.hashAlgorithm
    },
    Buffer.from(encryptedData, 'base64')
  );
  
  return outputEncoding ? decrypted.toString(outputEncoding) : decrypted;
}

/**
 * Creates a digital signature for data
 * 
 * @param {string|Buffer} data - Data to sign
 * @param {string} privateKey - Private key in PEM format
 * @returns {string} - Signature in base64
 */
function createSignature(data, privateKey) {
  const sign = crypto.createSign(CONFIG.asymmetric.hashAlgorithm);
  sign.update(Buffer.isBuffer(data) ? data : Buffer.from(data));
  return sign.sign(privateKey, 'base64');
}

/**
 * Verifies a digital signature
 * 
 * @param {string|Buffer} data - Data to verify
 * @param {string} signature - Signature in base64
 * @param {string} publicKey - Public key in PEM format
 * @returns {boolean} - True if signature is valid, false otherwise
 */
function verifySignature(data, signature, publicKey) {
  const verify = crypto.createVerify(CONFIG.asymmetric.hashAlgorithm);
  verify.update(Buffer.isBuffer(data) ? data : Buffer.from(data));
  return verify.verify(publicKey, Buffer.from(signature, 'base64'));
}

/**
 * Encrypts an API key
 * 
 * @param {string} apiKey - API key to encrypt
 * @param {string} masterKey - Master key for encryption
 * @returns {string} - Encrypted API key
 */
function encryptApiKey(apiKey, masterKey) {
  const { encrypted, iv, authTag } = encryptSymmetric(apiKey, masterKey);
  return `${encrypted}:${iv}:${authTag}`;
}

/**
 * Decrypts an API key
 * 
 * @param {string} encryptedApiKey - Encrypted API key
 * @param {string} masterKey - Master key for decryption
 * @returns {string} - Decrypted API key
 */
function decryptApiKey(encryptedApiKey, masterKey) {
  const [encrypted, iv, authTag] = encryptedApiKey.split(':');
  return decryptSymmetric(encrypted, iv, authTag, masterKey);
}

/**
 * Masks sensitive data for logging or display
 * 
 * @param {string} data - Data to mask
 * @param {number} visibleChars - Number of characters to leave visible at start and end
 * @returns {string} - Masked data
 */
function maskSensitiveData(data, visibleChars = 4) {
  if (!data || data.length <= visibleChars * 2) {
    return '****';
  }
  
  const start = data.substring(0, visibleChars);
  const end = data.substring(data.length - visibleChars);
  const masked = '*'.repeat(Math.min(data.length - (visibleChars * 2), 8));
  
  return `${start}${masked}${end}`;
}

/**
 * Encrypts a file
 * 
 * @param {string} inputPath - Path to input file
 * @param {string} outputPath - Path to output file
 * @param {Buffer|string} key - Encryption key
 * @returns {Promise<void>}
 */
async function encryptFile(inputPath, outputPath, key) {
  return new Promise((resolve, reject) => {
    try {
      const input = fs.readFileSync(inputPath);
      const { encrypted, iv, authTag } = encryptSymmetric(input, key);
      
      const output = JSON.stringify({
        encrypted,
        iv,
        authTag,
        metadata: {
          originalName: path.basename(inputPath),
          encryptedAt: new Date().toISOString(),
          algorithm: CONFIG.symmetric.algorithm
        }
      });
      
      fs.writeFileSync(outputPath, output);
      resolve();
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Decrypts a file
 * 
 * @param {string} inputPath - Path to encrypted file
 * @param {string} outputPath - Path to output file
 * @param {Buffer|string} key - Decryption key
 * @returns {Promise<void>}
 */
async function decryptFile(inputPath, outputPath, key) {
  return new Promise((resolve, reject) => {
    try {
      const input = JSON.parse(fs.readFileSync(inputPath, 'utf8'));
      const { encrypted, iv, authTag } = input;
      
      const decrypted = decryptSymmetric(encrypted, iv, authTag, key, null);
      fs.writeFileSync(outputPath, decrypted);
      resolve();
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Securely compares two strings in constant time
 * 
 * @param {string} a - First string
 * @param {string} b - Second string
 * @returns {boolean} - True if strings are equal, false otherwise
 */
function secureCompare(a, b) {
  if (typeof a !== 'string' || typeof b !== 'string') {
    return false;
  }
  
  if (a.length !== b.length) {
    return false;
  }
  
  return crypto.timingSafeEqual(Buffer.from(a), Buffer.from(b));
}

module.exports = {
  // Configuration
  CONFIG,
  
  // Core functions
  generateRandomBytes,
  generateRandomString,
  deriveKey,
  createHash,
  verifyHash,
  
  // Symmetric encryption
  encryptSymmetric,
  decryptSymmetric,
  
  // Asymmetric encryption
  generateKeyPair,
  encryptAsymmetric,
  decryptAsymmetric,
  createSignature,
  verifySignature,
  
  // API key functions
  encryptApiKey,
  decryptApiKey,
  
  // Utility functions
  maskSensitiveData,
  encryptFile,
  decryptFile,
  secureCompare
};
