version: '3.8'

services:
  # NovaFuse API service to test
  novafuse-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=test
      - PORT=3000
      - MONGODB_URI=mongodb://mongo:27017/novafuse-test
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=test-jwt-secret
      - API_KEY=test-api-key
      - LOG_LEVEL=info
    depends_on:
      - mongo
      - redis
    networks:
      - pentest-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

  # MongoDB for test data
  mongo:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    networks:
      - pentest-network

  # Redis for caching
  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    networks:
      - pentest-network

  # Kali Linux for penetration testing
  kali-pentest:
    build:
      context: .
      dockerfile: Dockerfile.kali-pentest
    volumes:
      - ./security-reports:/pentest/reports
      - ./api-specs:/pentest/api-specs
      - ./tools/security-testing/pentest-scripts:/pentest/scripts
    depends_on:
      - novafuse-api
    networks:
      - pentest-network
    tty: true
    stdin_open: true
    command: -c "echo 'Kali Linux Penetration Testing Container is ready. Run scripts from /pentest/scripts/'; bash"

  # OWASP ZAP for security scanning
  zap:
    image: owasp/zap2docker-stable
    command: zap-baseline.py -t http://novafuse-api:3000 -r /zap/wrk/zap-report.html
    volumes:
      - ./security-reports:/zap/wrk
    depends_on:
      - novafuse-api
    networks:
      - pentest-network

  # Semgrep for static code analysis
  semgrep:
    image: returntocorp/semgrep
    volumes:
      - .:/src
    command: --config=p/owasp-top-ten --config=p/javascript --json > /src/security-reports/semgrep-results.json
    networks:
      - pentest-network

  # Trivy for container scanning
  trivy:
    image: aquasec/trivy
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./security-reports:/reports
    command: image --format json --output /reports/trivy-results.json novafuse-api
    depends_on:
      - novafuse-api
    networks:
      - pentest-network

networks:
  pentest-network:
    driver: bridge

volumes:
  mongo-data:
