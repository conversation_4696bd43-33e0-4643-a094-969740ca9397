/**
 * NovaFuse Universal API Connector Database Initialization
 * 
 * This script initializes the database with default data.
 */

const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const databaseManager = require('./database');
const logger = require('./logger');
const { User, Team, Environment } = require('../models');

/**
 * Initialize database with default data
 * @returns {Promise<void>}
 */
async function initializeDatabase() {
  try {
    logger.info('Initializing database...');
    
    // Connect to database
    await databaseManager.connect();
    
    // Check if admin user exists
    const adminExists = await User.findOne({ username: 'admin' });
    
    if (!adminExists) {
      logger.info('Creating admin user...');
      
      // Create admin user
      const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
      const hashedPassword = await bcrypt.hash(adminPassword, 10);
      
      const adminUser = new User({
        username: 'admin',
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        status: 'active',
        emailVerified: true,
        permissions: ['*']
      });
      
      await adminUser.save();
      logger.info('Admin user created successfully');
      
      // Create default team
      logger.info('Creating default team...');
      
      const defaultTeam = new Team({
        name: 'NovaFuse Team',
        description: 'Default team for NovaFuse UAC',
        slug: 'novafuse-team',
        members: [{
          userId: adminUser._id,
          role: 'owner',
          addedAt: new Date()
        }],
        settings: {
          defaultRole: 'member',
          allowPublicConnectors: false,
          requireApprovalForConnectors: true,
          requireApprovalForCredentials: true,
          allowMemberInvitations: false
        },
        status: 'active',
        createdBy: adminUser._id
      });
      
      await defaultTeam.save();
      logger.info('Default team created successfully');
      
      // Create default environments
      logger.info('Creating default environments...');
      
      const environments = [
        {
          name: 'Development',
          description: 'Development environment for testing and development',
          type: 'development',
          color: '#3498db',
          icon: 'code',
          variables: [
            {
              name: 'API_URL',
              value: 'https://api-dev.example.com',
              isSecret: false,
              description: 'API base URL for development'
            }
          ],
          baseUrls: [
            {
              name: 'default',
              url: 'https://api-dev.example.com',
              description: 'Default base URL for development'
            }
          ],
          status: 'active',
          createdBy: adminUser._id,
          teamId: defaultTeam._id
        },
        {
          name: 'Production',
          description: 'Production environment for live services',
          type: 'production',
          color: '#27ae60',
          icon: 'cloud',
          variables: [
            {
              name: 'API_URL',
              value: 'https://api.example.com',
              isSecret: false,
              description: 'API base URL for production'
            }
          ],
          baseUrls: [
            {
              name: 'default',
              url: 'https://api.example.com',
              description: 'Default base URL for production'
            }
          ],
          status: 'active',
          createdBy: adminUser._id,
          teamId: defaultTeam._id
        }
      ];
      
      for (const env of environments) {
        const environment = new Environment(env);
        await environment.save();
      }
      
      logger.info('Default environments created successfully');
      
      // Update team with default environment
      const devEnvironment = await Environment.findOne({ name: 'Development', teamId: defaultTeam._id });
      
      if (devEnvironment) {
        defaultTeam.settings.defaultEnvironment = devEnvironment._id;
        await defaultTeam.save();
      }
    } else {
      logger.info('Admin user already exists, skipping initialization');
    }
    
    logger.info('Database initialization completed successfully');
  } catch (error) {
    logger.error('Failed to initialize database:', { error: error.message });
    throw error;
  }
}

module.exports = initializeDatabase;
