/**
 * Encryption Utility for NovaConnect Universal API Connector
 * 
 * This module provides strong encryption capabilities for sensitive data.
 * It uses AES-256-GCM for encryption, which provides both confidentiality and integrity.
 */

const crypto = require('crypto');

// Constants for encryption
const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16; // For AES, this is always 16 bytes
const KEY_LENGTH = 32; // 256 bits
const AUTH_TAG_LENGTH = 16;
const SALT_LENGTH = 64;
const KEY_ITERATIONS = 100000; // PBKDF2 iterations (higher is more secure but slower)
const KEY_DIGEST = 'sha512';

/**
 * Generate a secure encryption key from a password/secret
 * 
 * @param {string} secret - The secret to derive the key from
 * @param {Buffer} salt - The salt to use for key derivation
 * @returns {Buffer} - The derived key
 */
function deriveKey(secret, salt) {
  return crypto.pbkdf2Sync(
    secret,
    salt,
    KEY_ITERATIONS,
    KEY_LENGTH,
    KEY_DIGEST
  );
}

/**
 * Generate a random encryption key
 * 
 * @returns {Buffer} - A random encryption key
 */
function generateEncryptionKey() {
  return crypto.randomBytes(KEY_LENGTH);
}

/**
 * Encrypt data using AES-256-GCM
 * 
 * @param {string|Object} data - The data to encrypt (objects will be JSON stringified)
 * @param {string|Buffer} key - The encryption key or secret
 * @param {Object} options - Additional options
 * @param {boolean} options.deriveKey - Whether to derive a key from the provided secret
 * @param {Buffer} options.associatedData - Additional authenticated data (AAD)
 * @returns {Object} - The encrypted data with metadata needed for decryption
 */
function encrypt(data, key, options = {}) {
  // Convert object to string if necessary
  const dataString = typeof data === 'object' ? JSON.stringify(data) : data;
  
  // Generate a random IV for each encryption operation
  const iv = crypto.randomBytes(IV_LENGTH);
  
  // Generate a random salt for key derivation
  const salt = crypto.randomBytes(SALT_LENGTH);
  
  // Derive key if needed
  let encryptionKey;
  if (options.deriveKey) {
    encryptionKey = deriveKey(key, salt);
  } else if (typeof key === 'string') {
    // If key is a string but deriveKey is false, treat it as a hex string
    encryptionKey = Buffer.from(key, 'hex');
  } else {
    // Otherwise, use the key as is
    encryptionKey = key;
  }
  
  // Create cipher
  const cipher = crypto.createCipheriv(ALGORITHM, encryptionKey, iv);
  
  // Add additional authenticated data if provided
  if (options.associatedData) {
    cipher.setAAD(Buffer.from(options.associatedData));
  }
  
  // Encrypt the data
  let encrypted = cipher.update(dataString, 'utf8', 'base64');
  encrypted += cipher.final('base64');
  
  // Get the authentication tag
  const authTag = cipher.getAuthTag().toString('base64');
  
  // Return the encrypted data with metadata needed for decryption
  return {
    encrypted,
    iv: iv.toString('base64'),
    authTag,
    salt: salt.toString('base64'),
    algorithm: ALGORITHM,
    keyDerivation: options.deriveKey ? 'pbkdf2' : null,
    keyIterations: options.deriveKey ? KEY_ITERATIONS : null,
    keyDigest: options.deriveKey ? KEY_DIGEST : null
  };
}

/**
 * Decrypt data using AES-256-GCM
 * 
 * @param {Object} encryptedData - The encrypted data with metadata
 * @param {string} encryptedData.encrypted - The encrypted data
 * @param {string} encryptedData.iv - The initialization vector (base64)
 * @param {string} encryptedData.authTag - The authentication tag (base64)
 * @param {string} encryptedData.salt - The salt used for key derivation (base64)
 * @param {string} encryptedData.algorithm - The encryption algorithm
 * @param {string|Buffer} key - The encryption key or secret
 * @param {Object} options - Additional options
 * @param {boolean} options.deriveKey - Whether to derive a key from the provided secret
 * @param {Buffer} options.associatedData - Additional authenticated data (AAD)
 * @param {boolean} options.parseJson - Whether to parse the decrypted data as JSON
 * @returns {string|Object} - The decrypted data
 */
function decrypt(encryptedData, key, options = {}) {
  // Verify that we have all required data
  if (!encryptedData.encrypted || !encryptedData.iv || !encryptedData.authTag) {
    throw new Error('Invalid encrypted data format');
  }
  
  // Convert base64 strings back to buffers
  const iv = Buffer.from(encryptedData.iv, 'base64');
  const authTag = Buffer.from(encryptedData.authTag, 'base64');
  const salt = encryptedData.salt ? Buffer.from(encryptedData.salt, 'base64') : null;
  
  // Derive key if needed
  let decryptionKey;
  if (options.deriveKey && salt) {
    decryptionKey = deriveKey(key, salt);
  } else if (typeof key === 'string') {
    // If key is a string but deriveKey is false, treat it as a hex string
    decryptionKey = Buffer.from(key, 'hex');
  } else {
    // Otherwise, use the key as is
    decryptionKey = key;
  }
  
  // Create decipher
  const decipher = crypto.createDecipheriv(
    encryptedData.algorithm || ALGORITHM,
    decryptionKey,
    iv
  );
  
  // Set auth tag
  decipher.setAuthTag(authTag);
  
  // Add additional authenticated data if provided
  if (options.associatedData) {
    decipher.setAAD(Buffer.from(options.associatedData));
  }
  
  // Decrypt the data
  let decrypted;
  try {
    decrypted = decipher.update(encryptedData.encrypted, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
  } catch (error) {
    throw new Error(`Decryption failed: ${error.message}`);
  }
  
  // Parse JSON if requested
  if (options.parseJson) {
    try {
      return JSON.parse(decrypted);
    } catch (error) {
      throw new Error(`JSON parsing failed: ${error.message}`);
    }
  }
  
  return decrypted;
}

/**
 * Hash a password or sensitive data using a secure algorithm
 * 
 * @param {string} data - The data to hash
 * @param {string} salt - Optional salt (if not provided, a random one will be generated)
 * @returns {Object} - The hash and salt
 */
function hashData(data, salt = null) {
  // Generate a random salt if not provided
  const useSalt = salt || crypto.randomBytes(SALT_LENGTH).toString('hex');
  
  // Hash the data
  const hash = crypto.pbkdf2Sync(
    data,
    useSalt,
    KEY_ITERATIONS,
    64,
    KEY_DIGEST
  ).toString('hex');
  
  return {
    hash,
    salt: useSalt
  };
}

/**
 * Verify a hash against the original data
 * 
 * @param {string} data - The data to verify
 * @param {string} hash - The hash to verify against
 * @param {string} salt - The salt used for hashing
 * @returns {boolean} - Whether the hash matches
 */
function verifyHash(data, hash, salt) {
  const { hash: newHash } = hashData(data, salt);
  return newHash === hash;
}

/**
 * Generate a secure random token
 * 
 * @param {number} length - The length of the token in bytes
 * @returns {string} - The token as a hex string
 */
function generateToken(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

module.exports = {
  encrypt,
  decrypt,
  generateEncryptionKey,
  hashData,
  verifyHash,
  generateToken
};
