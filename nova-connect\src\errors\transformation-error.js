/**
 * NovaFuse Universal API Connector - Transformation Error
 * 
 * This module defines data transformation-related errors for the UAC.
 */

const UAConnectorError = require('./base-error');

/**
 * Error class for data transformation failures
 * @class TransformationError
 * @extends UAConnectorError
 */
class TransformationError extends UAConnectorError {
  /**
   * Create a new TransformationError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {string} options.severity - Error severity
   * @param {Object} options.context - Additional context for the error
   * @param {Error} options.cause - The error that caused this error
   * @param {string} options.transformationId - ID of the transformation
   * @param {string} options.sourceData - Source data that failed transformation
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'TRANSFORMATION_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
    
    this.transformationId = options.transformationId;
    this.sourceData = options.sourceData;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'An error occurred while processing the data. Please contact support if the issue persists.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    
    if (this.transformationId) {
      json.transformationId = this.transformationId;
    }
    
    // Only include source data in developer mode to avoid leaking sensitive information
    if (process.env.NODE_ENV === 'development' && this.sourceData) {
      json.sourceData = this.sourceData;
    }
    
    return json;
  }
}

/**
 * Error class for missing source field errors
 * @class MissingSourceFieldError
 * @extends TransformationError
 */
class MissingSourceFieldError extends TransformationError {
  /**
   * Create a new MissingSourceFieldError
   * 
   * @param {string} fieldPath - The path to the missing field
   * @param {Object} options - Error options
   */
  constructor(fieldPath, options = {}) {
    const message = `Missing source field: ${fieldPath}`;
    
    super(message, {
      code: options.code || 'TRANSFORMATION_MISSING_SOURCE_FIELD',
      severity: options.severity || 'error',
      context: { ...options.context, fieldPath },
      cause: options.cause,
      transformationId: options.transformationId,
      sourceData: options.sourceData
    });
    
    this.fieldPath = fieldPath;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'An error occurred while processing the data. A required field is missing.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.fieldPath = this.fieldPath;
    return json;
  }
}

/**
 * Error class for invalid transformation function errors
 * @class InvalidTransformationFunctionError
 * @extends TransformationError
 */
class InvalidTransformationFunctionError extends TransformationError {
  /**
   * Create a new InvalidTransformationFunctionError
   * 
   * @param {string} functionName - The name of the invalid function
   * @param {Object} options - Error options
   */
  constructor(functionName, options = {}) {
    const message = `Invalid transformation function: ${functionName}`;
    
    super(message, {
      code: options.code || 'TRANSFORMATION_INVALID_FUNCTION',
      severity: options.severity || 'error',
      context: { ...options.context, functionName },
      cause: options.cause,
      transformationId: options.transformationId,
      sourceData: options.sourceData
    });
    
    this.functionName = functionName;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'An error occurred while processing the data. The transformation configuration is invalid.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.functionName = this.functionName;
    return json;
  }
}

/**
 * Error class for data type conversion errors
 * @class DataTypeConversionError
 * @extends TransformationError
 */
class DataTypeConversionError extends TransformationError {
  /**
   * Create a new DataTypeConversionError
   * 
   * @param {string} fieldPath - The path to the field
   * @param {string} sourceType - The source data type
   * @param {string} targetType - The target data type
   * @param {Object} options - Error options
   */
  constructor(fieldPath, sourceType, targetType, options = {}) {
    const message = `Cannot convert field ${fieldPath} from ${sourceType} to ${targetType}`;
    
    super(message, {
      code: options.code || 'TRANSFORMATION_DATA_TYPE_CONVERSION',
      severity: options.severity || 'error',
      context: { ...options.context, fieldPath, sourceType, targetType },
      cause: options.cause,
      transformationId: options.transformationId,
      sourceData: options.sourceData
    });
    
    this.fieldPath = fieldPath;
    this.sourceType = sourceType;
    this.targetType = targetType;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'An error occurred while processing the data. A data type conversion failed.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.fieldPath = this.fieldPath;
    json.sourceType = this.sourceType;
    json.targetType = this.targetType;
    return json;
  }
}

module.exports = {
  TransformationError,
  MissingSourceFieldError,
  InvalidTransformationFunctionError,
  DataTypeConversionError
};
