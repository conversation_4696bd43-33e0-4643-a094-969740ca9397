/**
 * Rate Limiting Middleware
 * 
 * This middleware provides rate limiting for API endpoints.
 */

const rateLimit = require('express-rate-limit');
const Redis = require('ioredis');
const { RateLimitRedisStore } = require('rate-limit-redis');
const RateLimitService = require('../services/RateLimitService');

// Initialize Redis client if enabled
let redisClient = null;
if (process.env.USE_REDIS === 'true') {
  try {
    redisClient = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || '',
      db: process.env.REDIS_DB || 0
    });
    
    redisClient.on('error', (error) => {
      console.error('Redis error:', error);
      redisClient = null;
    });
  } catch (error) {
    console.error('Error initializing Redis:', error);
  }
}

// Create Redis store if Redis is available
const redisStore = redisClient ? new RateLimitRedisStore({
  sendCommand: (...args) => redisClient.call(...args)
}) : null;

// Global rate limit (applies to all routes)
const globalLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 100, // 100 requests per minute
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    status: 429,
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests, please try again later.'
    }
  },
  store: redisStore
});

// Authentication rate limit (applies to auth routes)
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 login attempts per 15 minutes
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    status: 429,
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many login attempts, please try again later.'
    }
  },
  store: redisStore
});

// API rate limit (applies to API routes)
const apiLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // 60 requests per minute
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    status: 429,
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many API requests, please try again later.'
    }
  },
  store: redisStore
});

// Dynamic rate limit based on user role
const createDynamicLimiter = (options = {}) => {
  const rateLimitService = new RateLimitService();
  
  return async (req, res, next) => {
    try {
      // Skip rate limiting for admin users
      if (req.user && req.user.role === 'admin') {
        return next();
      }
      
      // Determine rate limit type based on authentication
      let type = 'anonymous';
      if (req.user) {
        type = 'authenticated';
      } else if (req.apiKey) {
        type = 'apiKey';
      }
      
      // Get client IP address
      const ip = req.ip || req.connection.remoteAddress;
      
      // Check rate limit
      const identifier = req.user ? req.user.id : (req.apiKey ? req.apiKey.id : ip);
      const result = await rateLimitService.checkRateLimit(identifier, type);
      
      // Set rate limit headers
      res.setHeader('X-RateLimit-Limit', result.limit);
      res.setHeader('X-RateLimit-Remaining', result.remaining);
      res.setHeader('X-RateLimit-Reset', result.reset);
      
      next();
    } catch (error) {
      if (error.name === 'RateLimitError') {
        res.setHeader('Retry-After', error.retryAfter);
        res.status(429).json({
          status: 429,
          success: false,
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: error.message
          }
        });
      } else {
        next(error);
      }
    }
  };
};

module.exports = {
  globalLimiter,
  authLimiter,
  apiLimiter,
  createDynamicLimiter
};
