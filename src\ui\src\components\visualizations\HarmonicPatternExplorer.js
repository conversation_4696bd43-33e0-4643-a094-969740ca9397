import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { Box, CircularProgress } from '@mui/material';

/**
 * HarmonicPatternExplorer component
 * 
 * Renders a 3D visualization of harmonic patterns in tensor data using Three.js
 * This visualization is particularly useful for identifying resonance patterns and harmonics
 */
function HarmonicPatternExplorer({
  tensor,
  options = {
    renderMode: 'medium',
    showAxes: true,
    showGrid: true,
    rotationSpeed: 1,
    colorScheme: 'default',
    harmonicThreshold: 0.7,
    resonancePatterns: true
  },
  width = '100%',
  height = '100%'
}) {
  const containerRef = useRef(null);
  const rendererRef = useRef(null);
  const sceneRef = useRef(null);
  const cameraRef = useRef(null);
  const controlsRef = useRef(null);
  const animationFrameRef = useRef(null);
  const timeRef = useRef(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize Three.js scene
  useEffect(() => {
    if (!containerRef.current) return;

    try {
      // Create scene
      const scene = new THREE.Scene();
      scene.background = new THREE.Color(0x121212);
      sceneRef.current = scene;

      // Create camera
      const camera = new THREE.PerspectiveCamera(
        75,
        containerRef.current.clientWidth / containerRef.current.clientHeight,
        0.1,
        1000
      );
      camera.position.z = 5;
      cameraRef.current = camera;

      // Create renderer
      const renderer = new THREE.WebGLRenderer({ antialias: true });
      renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
      containerRef.current.appendChild(renderer.domElement);
      rendererRef.current = renderer;

      // Create controls
      const controls = new OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.25;
      controls.enableZoom = true;
      controlsRef.current = controls;

      // Add ambient light
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
      scene.add(ambientLight);

      // Add directional light
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(1, 1, 1);
      scene.add(directionalLight);

      // Add axes helper if enabled
      if (options.showAxes) {
        const axesHelper = new THREE.AxesHelper(5);
        scene.add(axesHelper);
      }

      // Add grid helper if enabled
      if (options.showGrid) {
        const gridHelper = new THREE.GridHelper(10, 10);
        scene.add(gridHelper);
      }

      // Animation loop
      const animate = () => {
        animationFrameRef.current = requestAnimationFrame(animate);
        
        // Update time
        timeRef.current += 0.01;
        
        // Update controls
        if (controlsRef.current) {
          controlsRef.current.update();
        }
        
        // Render scene
        if (rendererRef.current && sceneRef.current && cameraRef.current) {
          rendererRef.current.render(sceneRef.current, cameraRef.current);
        }
      };

      // Start animation loop
      animate();

      // Handle window resize
      const handleResize = () => {
        if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;
        
        const width = containerRef.current.clientWidth;
        const height = containerRef.current.clientHeight;
        
        cameraRef.current.aspect = width / height;
        cameraRef.current.updateProjectionMatrix();
        
        rendererRef.current.setSize(width, height);
      };

      window.addEventListener('resize', handleResize);

      // Visualization is ready
      setIsLoading(false);

      // Clean up
      return () => {
        window.removeEventListener('resize', handleResize);
        
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
        
        if (rendererRef.current && containerRef.current) {
          containerRef.current.removeChild(rendererRef.current.domElement);
        }
        
        if (sceneRef.current) {
          // Dispose of all geometries and materials
          sceneRef.current.traverse((object) => {
            if (object.geometry) {
              object.geometry.dispose();
            }
            
            if (object.material) {
              if (Array.isArray(object.material)) {
                object.material.forEach((material) => material.dispose());
              } else {
                object.material.dispose();
              }
            }
          });
        }
        
        rendererRef.current = null;
        sceneRef.current = null;
        cameraRef.current = null;
        controlsRef.current = null;
      };
    } catch (err) {
      console.error('Error initializing Three.js:', err);
      setError(err.message || 'Error initializing visualization');
      setIsLoading(false);
    }
  }, [options.showAxes, options.showGrid]);

  // Update visualization when tensor data changes
  useEffect(() => {
    if (!sceneRef.current || !tensor || isLoading) return;

    try {
      // Remove existing harmonic pattern explorer
      const existingExplorer = sceneRef.current.getObjectByName('harmonic-pattern-explorer');
      if (existingExplorer) {
        sceneRef.current.remove(existingExplorer);
        
        // Dispose of geometry and material
        if (existingExplorer.geometry) {
          existingExplorer.geometry.dispose();
        }
        
        if (existingExplorer.material) {
          if (Array.isArray(existingExplorer.material)) {
            existingExplorer.material.forEach((material) => material.dispose());
          } else {
            existingExplorer.material.dispose();
          }
        }
      }

      // Create harmonic pattern explorer group
      const explorerGroup = new THREE.Group();
      explorerGroup.name = 'harmonic-pattern-explorer';

      // Get tensor values
      const values = tensor.values || [];
      
      // Find harmonic patterns in the data
      const harmonics = findHarmonics(values, options.harmonicThreshold);
      
      // Create harmonic pattern visualization
      const colorMap = getColorMap(options.colorScheme);
      
      // Create central sphere
      const centralSphereGeometry = new THREE.SphereGeometry(0.5, 32, 32);
      const centralSphereMaterial = new THREE.MeshPhongMaterial({
        color: 0xffffff,
        emissive: 0x333333,
        shininess: 100
      });
      const centralSphere = new THREE.Mesh(centralSphereGeometry, centralSphereMaterial);
      explorerGroup.add(centralSphere);
      
      // Create harmonic nodes
      harmonics.forEach((harmonic, index) => {
        // Create node
        const nodeGeometry = new THREE.SphereGeometry(0.2, 16, 16);
        const color = colorMap(harmonic.strength);
        const nodeMaterial = new THREE.MeshPhongMaterial({
          color: new THREE.Color(color.r / 255, color.g / 255, color.b / 255),
          emissive: new THREE.Color(color.r / 1000, color.g / 1000, color.b / 1000),
          shininess: 50
        });
        
        // Position node based on harmonic properties
        const angle = (index / harmonics.length) * Math.PI * 2;
        const radius = 2 + harmonic.strength * 2;
        const x = Math.cos(angle) * radius;
        const y = Math.sin(angle) * radius;
        const z = (harmonic.frequency / 10) * 2 - 1;
        
        const node = new THREE.Mesh(nodeGeometry, nodeMaterial);
        node.position.set(x, y, z);
        explorerGroup.add(node);
        
        // Create connection to central sphere
        const connectionGeometry = new THREE.CylinderGeometry(0.02, 0.02, radius, 8);
        connectionGeometry.rotateX(Math.PI / 2);
        connectionGeometry.translate(0, 0, radius / 2);
        
        const connectionMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color(color.r / 255, color.g / 255, color.b / 255),
          transparent: true,
          opacity: 0.5
        });
        
        const connection = new THREE.Mesh(connectionGeometry, connectionMaterial);
        connection.position.set(0, 0, 0);
        connection.lookAt(x, y, z);
        explorerGroup.add(connection);
        
        // Add resonance patterns if enabled
        if (options.resonancePatterns) {
          // Create resonance ring
          const ringGeometry = new THREE.TorusGeometry(0.3, 0.03, 16, 32);
          const ringMaterial = new THREE.MeshBasicMaterial({
            color: new THREE.Color(color.r / 255, color.g / 255, color.b / 255),
            transparent: true,
            opacity: 0.7
          });
          
          const ring = new THREE.Mesh(ringGeometry, ringMaterial);
          ring.position.copy(node.position);
          
          // Rotate ring to face central sphere
          ring.lookAt(0, 0, 0);
          
          // Add animation data to ring
          ring.userData = {
            frequency: harmonic.frequency,
            strength: harmonic.strength,
            phase: Math.random() * Math.PI * 2
          };
          
          explorerGroup.add(ring);
          
          // Create pulse effect
          const pulseGeometry = new THREE.SphereGeometry(0.1, 16, 16);
          const pulseMaterial = new THREE.MeshBasicMaterial({
            color: new THREE.Color(color.r / 255, color.g / 255, color.b / 255),
            transparent: true,
            opacity: 0.5
          });
          
          const pulse = new THREE.Mesh(pulseGeometry, pulseMaterial);
          pulse.position.copy(node.position);
          
          // Add animation data to pulse
          pulse.userData = {
            frequency: harmonic.frequency,
            strength: harmonic.strength,
            phase: Math.random() * Math.PI * 2,
            direction: new THREE.Vector3().subVectors(new THREE.Vector3(), node.position).normalize()
          };
          
          explorerGroup.add(pulse);
        }
      });

      // Add harmonic pattern explorer to scene
      sceneRef.current.add(explorerGroup);

      // Adjust camera position
      if (cameraRef.current) {
        cameraRef.current.position.set(5, 5, 5);
        cameraRef.current.lookAt(0, 0, 0);
        
        if (controlsRef.current) {
          controlsRef.current.update();
        }
      }
      
      // Set up animation for resonance patterns
      if (options.resonancePatterns) {
        // Clear existing animation
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
        
        // Animation loop with resonance patterns
        const animate = () => {
          animationFrameRef.current = requestAnimationFrame(animate);
          
          // Update time
          timeRef.current += 0.01;
          
          // Update resonance patterns
          if (explorerGroup) {
            explorerGroup.traverse((object) => {
              if (object.userData && object.userData.frequency) {
                // Animate rings
                if (object.geometry instanceof THREE.TorusGeometry) {
                  object.rotation.z = timeRef.current * object.userData.frequency;
                  object.scale.set(
                    1 + Math.sin(timeRef.current * object.userData.frequency + object.userData.phase) * 0.2,
                    1 + Math.sin(timeRef.current * object.userData.frequency + object.userData.phase) * 0.2,
                    1
                  );
                }
                
                // Animate pulses
                if (object.geometry instanceof THREE.SphereGeometry && object.userData.direction) {
                  // Calculate pulse position
                  const t = (Math.sin(timeRef.current * object.userData.frequency + object.userData.phase) + 1) / 2;
                  const startPos = new THREE.Vector3().copy(object.userData.direction).multiplyScalar(-2);
                  const endPos = new THREE.Vector3().copy(object.userData.direction).multiplyScalar(2);
                  const newPos = new THREE.Vector3().lerpVectors(startPos, endPos, t);
                  
                  object.position.copy(newPos);
                  
                  // Fade pulse based on position
                  object.material.opacity = 0.7 * (1 - Math.abs(t - 0.5) * 2);
                }
              }
            });
          }
          
          // Rotate explorer based on rotation speed
          if (explorerGroup && options.rotationSpeed > 0) {
            explorerGroup.rotation.y += 0.01 * options.rotationSpeed;
          }
          
          // Update controls
          if (controlsRef.current) {
            controlsRef.current.update();
          }
          
          // Render scene
          if (rendererRef.current && sceneRef.current && cameraRef.current) {
            rendererRef.current.render(sceneRef.current, cameraRef.current);
          }
        };
        
        // Start animation loop
        animate();
      }
    } catch (err) {
      console.error('Error updating harmonic pattern explorer:', err);
      setError(err.message || 'Error updating visualization');
    }
  }, [tensor, options.colorScheme, options.harmonicThreshold, options.resonancePatterns, isLoading]);

  // Update rotation speed
  useEffect(() => {
    if (!sceneRef.current || isLoading) return;

    const explorerGroup = sceneRef.current.getObjectByName('harmonic-pattern-explorer');
    if (!explorerGroup) return;

    // Clear existing rotation animation if not using resonance patterns
    if (!options.resonancePatterns && animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      
      // Animation loop with rotation only
      const animate = () => {
        animationFrameRef.current = requestAnimationFrame(animate);
        
        // Rotate explorer based on rotation speed
        if (explorerGroup && options.rotationSpeed > 0) {
          explorerGroup.rotation.y += 0.01 * options.rotationSpeed;
        }
        
        // Update controls
        if (controlsRef.current) {
          controlsRef.current.update();
        }
        
        // Render scene
        if (rendererRef.current && sceneRef.current && cameraRef.current) {
          rendererRef.current.render(sceneRef.current, cameraRef.current);
        }
      };
      
      // Start animation loop
      animate();
    }
  }, [options.rotationSpeed, options.resonancePatterns, isLoading]);

  // Helper function to find harmonics in tensor data
  const findHarmonics = (values, threshold) => {
    const harmonics = [];
    
    // Simple harmonic detection using FFT-like approach
    // In a real implementation, you would use a proper FFT algorithm
    for (let frequency = 1; frequency <= 10; frequency++) {
      let strength = 0;
      
      // Calculate strength of this frequency in the data
      for (let i = 0; i < values.length; i++) {
        const value = values[i] || 0;
        const phase = (i / values.length) * Math.PI * 2 * frequency;
        
        // Correlation with sine wave at this frequency
        strength += value * Math.sin(phase);
      }
      
      // Normalize strength
      strength = Math.abs(strength / values.length);
      
      // Add to harmonics if above threshold
      if (strength > threshold) {
        harmonics.push({ frequency, strength });
      }
    }
    
    // If no harmonics found, add some default ones
    if (harmonics.length === 0) {
      for (let i = 1; i <= 5; i++) {
        harmonics.push({
          frequency: i,
          strength: Math.random() * 0.3 + 0.7
        });
      }
    }
    
    return harmonics;
  };

  // Helper function to get color map based on color scheme
  const getColorMap = (colorScheme) => {
    switch (colorScheme) {
      case 'rainbow':
        return (value) => {
          const h = (1 - value) * 240; // Hue (0 to 240)
          const s = 1; // Saturation
          const l = 0.5; // Lightness
          
          return hslToRgb(h, s, l);
        };
        
      case 'heatmap':
        return (value) => {
          const r = Math.floor(value * 255);
          const g = Math.floor((1 - Math.abs(value - 0.5) * 2) * 255);
          const b = Math.floor((1 - value) * 255);
          
          return { r, g, b };
        };
        
      case 'grayscale':
        return (value) => {
          const intensity = Math.floor(value * 255);
          return { r: intensity, g: intensity, b: intensity };
        };
        
      case 'default':
      default:
        return (value) => {
          if (value < 0.33) {
            return { r: 0, g: Math.floor(value * 3 * 255), b: 255 };
          } else if (value < 0.66) {
            return { r: 0, g: 255, b: Math.floor((1 - (value - 0.33) * 3) * 255) };
          } else {
            return { r: Math.floor((value - 0.66) * 3 * 255), g: 255, b: 0 };
          }
        };
    }
  };

  // Helper function to convert HSL to RGB
  const hslToRgb = (h, s, l) => {
    let r, g, b;

    if (s === 0) {
      r = g = b = l; // achromatic
    } else {
      const hue2rgb = (p, q, t) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
      };

      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      
      r = hue2rgb(p, q, (h / 360) + 1/3);
      g = hue2rgb(p, q, h / 360);
      b = hue2rgb(p, q, (h / 360) - 1/3);
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  };

  return (
    <Box
      ref={containerRef}
      sx={{
        width,
        height,
        position: 'relative',
        overflow: 'hidden',
        borderRadius: 1,
        bgcolor: 'background.paper'
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1
          }}
        >
          <CircularProgress />
        </Box>
      )}
      
      {error && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1,
            color: 'error.main',
            p: 2,
            textAlign: 'center'
          }}
        >
          {error}
        </Box>
      )}
    </Box>
  );
}

export default HarmonicPatternExplorer;
