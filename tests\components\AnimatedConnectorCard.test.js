import React from 'react';
import { render, screen } from '@testing-library/react';
import AnimatedConnectorCard from '../../components/AnimatedConnectorCard';

// Mock connector data
const mockConnector = {
  id: 'test-connector',
  name: 'Test Connector',
  vendor: 'Test Vendor',
  description: 'This is a test connector for unit testing',
  category: 'security',
  framework: 'gdpr',
  rating: 4.5,
  price: '$99/mo',
  popularity: 85,
  dateAdded: '2023-01-01'
};

describe('AnimatedConnectorCard', () => {
  it('renders connector information correctly', () => {
    render(<AnimatedConnectorCard connector={mockConnector} />);

    // Check if connector name is displayed
    expect(screen.getByText('Test Connector')).toBeInTheDocument();

    // Check if vendor is displayed (it's part of a text that includes 'by')
    expect(screen.getByText(/Test Vendor/i)).toBeInTheDocument();

    // Check if description is displayed
    expect(screen.getByText('This is a test connector for unit testing')).toBeInTheDocument();

    // Check if price is displayed
    expect(screen.getByText('$99/mo')).toBeInTheDocument();

    // Check if "View Details" button is present
    expect(screen.getByText('View Details')).toBeInTheDocument();
  });

  it('displays the correct category and framework', () => {
    render(<AnimatedConnectorCard connector={mockConnector} />);

    // Check if category is displayed (lowercase in the component)
    expect(screen.getByText('security')).toBeInTheDocument();

    // Check if framework is displayed
    expect(screen.getByText('GDPR')).toBeInTheDocument();
  });

  it('displays the correct rating', () => {
    render(<AnimatedConnectorCard connector={mockConnector} />);

    // Check if rating is displayed
    expect(screen.getByText('4.5')).toBeInTheDocument();
  });
});
