/* NovaFuse Website Styles */

/* Base Colors - Using Global Vision Section Colors */
:root {
    --primary-bg: #0f172a; /* Darker blue from Global Vision */
    --secondary-bg: #1e293b;
    --primary-text: #f8fafc;
    --secondary-text: #cbd5e1;
    --accent-color: #60a5fa; /* Light blue from Global Vision */
    --accent-hover: #1e3a8a; /* Deep blue from Global Vision */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --border-color: #334155;
    --global-gradient: linear-gradient(135deg, #0f172a 0%, #1e3a8a 100%);
}

/* Global Styles */
body {
    font-family: 'Inter', sans-serif;
    background-color: var(--primary-bg);
    color: var(--primary-text);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    box-sizing: border-box;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    font-weight: 700;
    line-height: 1.2;
}

a {
    color: var(--accent-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--accent-hover);
}

/* Header */
header {
    background-color: var(--secondary-bg);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    padding: 1rem 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

/* Navigation */
.main-nav {
    background-color: var(--secondary-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 0.5rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    margin-right: 1.5rem;
}

.nav-menu li:last-child {
    margin-right: 0;
}

.nav-menu a {
    color: var(--secondary-text);
    font-weight: 500;
    padding: 0.5rem 0;
    transition: color 0.3s ease;
}

.nav-menu a:hover, .nav-menu a.active {
    color: var(--primary-text);
    border-bottom: 2px solid var(--accent-color);
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--primary-text);
    font-size: 1.5rem;
    cursor: pointer;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background-color: var(--secondary-bg);
    height: 100%;
    position: fixed;
    top: 0;
    left: -250px; /* Hide sidebar by default on mobile */
    overflow-y: auto;
    transition: transform 0.3s ease;
    z-index: 1000;
    padding-top: 1rem;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

/* Show sidebar by default on larger screens */
@media (min-width: 1024px) {
    body {
        padding-left: 250px; /* Make room for the sidebar */
    }

    .sidebar {
        left: 0;
        top: 0;
        padding-top: 120px; /* Adjust based on header + nav height */
    }

    header, .main-nav, footer {
        width: 100%;
        left: 0;
    }

    .sidebar-toggle {
        display: none;
    }
}

.sidebar-visible {
    transform: translateX(250px);
}

.sidebar-toggle {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background: var(--accent-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

@media (max-width: 1023px) {
    .sidebar-toggle {
        top: 4.5rem; /* Position below header on mobile */
    }
}

.sidebar-logo {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin-bottom: 0.5rem;
}

.sidebar-menu a {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--secondary-text);
    transition: all 0.3s ease;
}

.sidebar-menu a:hover, .sidebar-menu a.active {
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary-text);
    border-left: 3px solid var(--accent-color);
}

.sidebar-submenu {
    list-style: none;
    padding-left: 1rem;
    margin: 0.5rem 0;
    display: none;
}

.sidebar-submenu.open {
    display: block;
}

.sidebar-submenu a {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.content-with-sidebar {
    margin-left: 0;
    transition: margin-left 0.3s ease;
    width: 100%;
    box-sizing: border-box;
}

.content-with-sidebar.sidebar-shifted {
    margin-left: 250px;
}

.content-full {
    margin-left: 0;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--accent-color);
    color: white;
    border: none;
}

.btn-primary:hover {
    background-color: var(--accent-hover);
    color: white;
}

.btn-outline {
    background-color: transparent;
    color: var(--accent-color);
    border: 1px solid var(--accent-color);
}

.btn-outline:hover {
    background-color: rgba(37, 99, 235, 0.1);
}

/* Cards */
.card {
    background-color: var(--secondary-bg);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.card-title {
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
}

.card-content {
    color: var(--secondary-text);
    margin-bottom: 1rem;
}

.partner-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.partner-card .btn {
    margin-top: auto;
}

/* Tags */
.tag {
    display: inline-block;
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--accent-color);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Hero Section */
.hero {
    background: var(--global-gradient);
    border-radius: 0.5rem;
    padding: 3rem 2rem;
    margin: 2rem 0;
    text-align: center;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
}

.hero p {
    font-size: 1.25rem;
    color: var(--secondary-text);
    max-width: 800px;
    margin: 0 auto 2rem auto;
}

/* Value Promise Grid */
.value-promise-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 2.5rem;
}

.value-promise-card {
    background-color: rgba(30, 58, 138, 0.3);
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(96, 165, 250, 0.2);
}

.value-promise-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
    border-color: var(--accent-color);
}

.value-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--accent-color);
}

.value-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--primary-text);
}

.value-description {
    color: var(--secondary-text);
    font-size: 1.1rem;
    line-height: 1.6;
}

.hero-tagline {
    font-size: 1.5rem;
    font-style: italic;
    color: var(--primary-text);
    max-width: 900px;
    margin: 0 auto 2rem auto;
    line-height: 1.6;
    font-weight: 500;
}

.hero-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* What It Means Section */
.what-it-means-section {
    background-color: rgba(30, 58, 138, 0.3);
    border-radius: 0.5rem;
    padding: 1.5rem;
    max-width: 900px;
    margin: 0 auto;
    border: 1px solid rgba(96, 165, 250, 0.2);
}

.what-it-means-item {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    padding: 1rem;
    height: 100%;
}

.what-it-means-item p {
    color: var(--secondary-text);
    font-size: 0.95rem;
    line-height: 1.5;
    margin: 0;
}

/* Footer */
footer {
    background-color: var(--secondary-bg);
    padding: 3rem 0 1.5rem;
    margin-top: 3rem;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-heading {
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--secondary-text);
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-text);
}

.footer-bottom {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
    color: var(--secondary-text);
}

/* Breadcrumbs */
.breadcrumbs {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 1rem 0;
    font-size: 0.875rem;
}

.breadcrumbs li {
    display: flex;
    align-items: center;
}

.breadcrumbs li:not(:last-child)::after {
    content: "/";
    margin: 0 0.5rem;
    color: var(--secondary-text);
}

.breadcrumbs a {
    color: var(--secondary-text);
}

.breadcrumbs a:hover {
    color: var(--accent-color);
}

.breadcrumbs li:last-child a {
    color: var(--primary-text);
    pointer-events: none;
}

/* Back Button */
.back-button {
    display: inline-flex;
    align-items: center;
    color: var(--secondary-text);
    margin-bottom: 1rem;
    transition: color 0.3s ease;
}

.back-button:hover {
    color: var(--accent-color);
}

.back-button svg {
    margin-right: 0.5rem;
}

/* Benefits Section */
.benefits-section {
    margin: 3rem 0;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.benefit-card {
    background-color: var(--secondary-bg);
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    transition: transform 0.3s ease;
}

.benefit-card:hover {
    transform: translateY(-5px);
}

.benefit-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--accent-color);
}

.benefit-title {
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
}

.benefit-description {
    color: var(--secondary-text);
}

/* Use Case Carousel */
.carousel {
    position: relative;
    margin: 3rem 0;
}

.carousel-inner {
    overflow: hidden;
}

.carousel-item {
    display: none;
    padding: 2rem;
    background-color: var(--secondary-bg);
    border-radius: 0.5rem;
}

.carousel-item.active {
    display: block;
}

.carousel-controls {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
}

.carousel-control {
    background: none;
    border: none;
    color: var(--secondary-text);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    margin: 0 0.5rem;
    transition: color 0.3s ease;
}

.carousel-control:hover {
    color: var(--primary-text);
}

.carousel-indicators {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
}

.carousel-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--secondary-text);
    margin: 0 0.25rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.carousel-indicator.active {
    background-color: var(--accent-color);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .menu-toggle {
        display: block;
    }

    .nav-menu {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: var(--primary-bg);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: 1000;
    }

    .nav-menu.open {
        transform: translateX(0);
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .content-with-sidebar {
        margin-left: 0;
    }

    .hero h2 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .footer-grid {
        grid-template-columns: 1fr;
    }
}

/* Global Vision Section Colors */
.global-vision {
    background: var(--global-gradient);
    color: #f8fafc;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.2);
}

.global-vision-accent {
    color: var(--accent-color);
}

/* Value Proposition Box */
.value-proposition-box {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    padding: 1.5rem;
    border-left: 4px solid var(--accent-color);
    max-width: 900px;
    margin: 0 auto;
}

.value-proposition-text {
    font-size: 1.25rem;
    font-style: italic;
    line-height: 1.6;
    color: white;
    font-weight: 500;
    text-align: center;
    margin: 0;
}

/* Apply Global Vision colors to various elements */
.card:hover {
    border-color: var(--accent-color);
}

.benefit-card {
    border-top: 3px solid var(--accent-color);
}

.use-case-card {
    border-left: 3px solid var(--accent-color);
}

/* Add Global Vision gradient to more elements */
.section-header {
    background: var(--global-gradient);
    padding: 2rem;
    border-radius: 0.5rem;
    margin-bottom: 2rem;
    color: white;
    text-align: center;
}

/* Core Innovations Section */
.innovations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin: 2rem 0 3rem 0;
}

.innovation-card {
    background-color: var(--secondary-bg);
    border-radius: 0.5rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    border-top: 4px solid var(--accent-color);
}

.innovation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
}

.innovation-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--accent-color);
    text-align: center;
}

.innovation-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--primary-text);
    text-align: center;
}

.innovation-description {
    color: var(--secondary-text);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.innovation-features {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
}

.innovation-features li {
    margin-bottom: 0.75rem;
    color: var(--secondary-text);
    display: flex;
    align-items: center;
}

.innovation-features li i {
    color: var(--success-color);
    margin-right: 0.75rem;
    font-size: 0.875rem;
}

.innovation-card .btn {
    margin-top: auto;
}

/* Enterprise Features Section */
.enterprise-features {
    background-color: #0f172a; /* Dark blue as requested */
    padding: 3rem 0;
}

.enterprise-feature-card {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    padding: 1.5rem;
    height: 100%;
    transition: transform 0.3s ease;
}

.enterprise-feature-card:hover {
    transform: translateY(-5px);
    background-color: rgba(255, 255, 255, 0.1);
}

/* White text for Explore Integrations button */
.explore-integrations-btn {
    color: white !important;
}
