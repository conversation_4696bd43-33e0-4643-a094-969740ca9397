/**
 * UserProfile Component
 * 
 * A component for displaying and editing user profile information.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useAuth } from '../auth/AuthContext';
import { useTheme } from '../theme/ThemeContext';
import { Animated } from './Animated';

/**
 * UserProfile component
 * 
 * @param {Object} props - Component props
 * @param {boolean} [props.editable=true] - Whether the profile is editable
 * @param {boolean} [props.showPassword=true] - Whether to show password change form
 * @param {Function} [props.onUpdate] - Callback when profile is updated
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} UserProfile component
 */
const UserProfile = ({
  editable = true,
  showPassword = true,
  onUpdate,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const { user, updateProfile, changePassword, signOut } = useAuth();
  
  // State
  const [isEditing, setIsEditing] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [profileData, setProfileData] = useState({
    displayName: user?.displayName || '',
    photoURL: user?.photoURL || '',
    email: user?.email || ''
  });
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  
  // Handle profile update
  const handleProfileUpdate = async (e) => {
    e.preventDefault();
    
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);
    
    try {
      const updatedUser = await updateProfile(profileData);
      
      setSuccessMessage('Profile updated successfully');
      setIsEditing(false);
      
      if (onUpdate) {
        onUpdate(updatedUser);
      }
    } catch (err) {
      console.error('Profile update error:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle password change
  const handlePasswordChange = async (e) => {
    e.preventDefault();
    
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);
    
    try {
      // Validate passwords
      if (passwordData.newPassword !== passwordData.confirmPassword) {
        throw new Error('New passwords do not match');
      }
      
      await changePassword(passwordData.currentPassword, passwordData.newPassword);
      
      setSuccessMessage('Password changed successfully');
      setIsChangingPassword(false);
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (err) {
      console.error('Password change error:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle sign out
  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (err) {
      console.error('Sign out error:', err);
    }
  };
  
  // Handle profile data change
  const handleProfileDataChange = (e) => {
    const { name, value } = e.target;
    setProfileData(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle password data change
  const handlePasswordDataChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({ ...prev, [name]: value }));
  };
  
  if (!user) {
    return (
      <div className="text-textSecondary">
        Not signed in
      </div>
    );
  }
  
  return (
    <Animated
      animation="fadeIn"
      className={`bg-surface p-6 rounded-lg shadow-lg ${className}`}
      style={style}
    >
      {/* Error message */}
      {error && (
        <div className="bg-error bg-opacity-10 border border-error text-error px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {/* Success message */}
      {successMessage && (
        <div className="bg-success bg-opacity-10 border border-success text-success px-4 py-3 rounded mb-4">
          {successMessage}
        </div>
      )}
      
      {/* Profile header */}
      <div className="flex items-center mb-6">
        <div className="mr-4">
          <img
            src={user.photoURL || 'https://via.placeholder.com/150'}
            alt={user.displayName || 'User'}
            className="w-16 h-16 rounded-full object-cover"
          />
        </div>
        <div>
          <h2 className="text-xl font-bold text-textPrimary">
            {user.displayName || 'User'}
          </h2>
          <p className="text-textSecondary">
            {user.email}
          </p>
          {user.role && (
            <div className="mt-1">
              <span className="inline-block bg-primary bg-opacity-10 text-primary text-xs px-2 py-1 rounded">
                {user.role}
              </span>
            </div>
          )}
        </div>
      </div>
      
      {/* Profile content */}
      <div className="space-y-6">
        {/* Profile form */}
        {isEditing ? (
          <form onSubmit={handleProfileUpdate}>
            <div className="space-y-4">
              <div>
                <label htmlFor="displayName" className="block text-textPrimary font-medium mb-1">
                  Display Name
                </label>
                <input
                  id="displayName"
                  name="displayName"
                  type="text"
                  value={profileData.displayName}
                  onChange={handleProfileDataChange}
                  className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                  placeholder="Enter your name"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="photoURL" className="block text-textPrimary font-medium mb-1">
                  Photo URL
                </label>
                <input
                  id="photoURL"
                  name="photoURL"
                  type="url"
                  value={profileData.photoURL}
                  onChange={handleProfileDataChange}
                  className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                  placeholder="Enter photo URL"
                />
              </div>
              
              <div>
                <label htmlFor="email" className="block text-textPrimary font-medium mb-1">
                  Email
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={profileData.email}
                  onChange={handleProfileDataChange}
                  className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                  placeholder="Enter your email"
                  required
                  disabled
                />
                <p className="text-xs text-textSecondary mt-1">
                  Email cannot be changed
                </p>
              </div>
              
              <div className="flex space-x-2">
                <button
                  type="submit"
                  className="bg-primary text-primaryContrast py-2 px-4 rounded-md hover:bg-primaryDark transition-colors duration-200"
                  disabled={isLoading}
                >
                  {isLoading ? 'Saving...' : 'Save Changes'}
                </button>
                
                <button
                  type="button"
                  className="bg-surface border border-divider text-textPrimary py-2 px-4 rounded-md hover:bg-background transition-colors duration-200"
                  onClick={() => setIsEditing(false)}
                  disabled={isLoading}
                >
                  Cancel
                </button>
              </div>
            </div>
          </form>
        ) : (
          <div>
            {editable && (
              <button
                type="button"
                className="bg-primary text-primaryContrast py-2 px-4 rounded-md hover:bg-primaryDark transition-colors duration-200"
                onClick={() => setIsEditing(true)}
              >
                Edit Profile
              </button>
            )}
          </div>
        )}
        
        {/* Password change form */}
        {showPassword && (
          <div className="pt-4 border-t border-divider">
            {isChangingPassword ? (
              <form onSubmit={handlePasswordChange}>
                <h3 className="text-lg font-medium text-textPrimary mb-4">
                  Change Password
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label htmlFor="currentPassword" className="block text-textPrimary font-medium mb-1">
                      Current Password
                    </label>
                    <input
                      id="currentPassword"
                      name="currentPassword"
                      type="password"
                      value={passwordData.currentPassword}
                      onChange={handlePasswordDataChange}
                      className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                      placeholder="Enter current password"
                      required
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="newPassword" className="block text-textPrimary font-medium mb-1">
                      New Password
                    </label>
                    <input
                      id="newPassword"
                      name="newPassword"
                      type="password"
                      value={passwordData.newPassword}
                      onChange={handlePasswordDataChange}
                      className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                      placeholder="Enter new password"
                      required
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="confirmPassword" className="block text-textPrimary font-medium mb-1">
                      Confirm New Password
                    </label>
                    <input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      value={passwordData.confirmPassword}
                      onChange={handlePasswordDataChange}
                      className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                      placeholder="Confirm new password"
                      required
                    />
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      type="submit"
                      className="bg-primary text-primaryContrast py-2 px-4 rounded-md hover:bg-primaryDark transition-colors duration-200"
                      disabled={isLoading}
                    >
                      {isLoading ? 'Changing...' : 'Change Password'}
                    </button>
                    
                    <button
                      type="button"
                      className="bg-surface border border-divider text-textPrimary py-2 px-4 rounded-md hover:bg-background transition-colors duration-200"
                      onClick={() => setIsChangingPassword(false)}
                      disabled={isLoading}
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </form>
            ) : (
              <button
                type="button"
                className="text-primary hover:underline"
                onClick={() => setIsChangingPassword(true)}
              >
                Change Password
              </button>
            )}
          </div>
        )}
        
        {/* Sign out */}
        <div className="pt-4 border-t border-divider">
          <button
            type="button"
            className="text-error hover:underline"
            onClick={handleSignOut}
          >
            Sign Out
          </button>
        </div>
      </div>
    </Animated>
  );
};

UserProfile.propTypes = {
  editable: PropTypes.bool,
  showPassword: PropTypes.bool,
  onUpdate: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default UserProfile;
