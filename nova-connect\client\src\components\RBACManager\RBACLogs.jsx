import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useSnackbar } from 'notistack';
import api from '../../services/api';

// Format date for display
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleString();
};

// Get action color
const getActionColor = (action) => {
  switch (action) {
    case 'CREATE':
      return 'success';
    case 'UPDATE':
      return 'info';
    case 'DELETE':
      return 'error';
    case 'ASSIGN_ROLE':
      return 'primary';
    case 'REMOVE_ROLE':
      return 'warning';
    default:
      return 'default';
  }
};

// Main RBAC Logs component
const RBACLogs = () => {
  // State
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalLogs, setTotalLogs] = useState(0);
  const [filters, setFilters] = useState({
    startDate: null,
    endDate: null,
    userId: '',
    action: '',
    resourceType: ''
  });
  const [showFilters, setShowFilters] = useState(false);

  const { enqueueSnackbar } = useSnackbar();

  // Fetch logs on component mount and when filters change
  useEffect(() => {
    fetchLogs();
  }, [page, rowsPerPage, filters]);

  // Fetch logs from API
  const fetchLogs = async () => {
    try {
      setLoading(true);
      
      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', page + 1);
      params.append('limit', rowsPerPage);
      
      if (filters.startDate) {
        params.append('startDate', filters.startDate.toISOString());
      }
      
      if (filters.endDate) {
        params.append('endDate', filters.endDate.toISOString());
      }
      
      if (filters.userId) {
        params.append('userId', filters.userId);
      }
      
      if (filters.action) {
        params.append('action', filters.action);
      }
      
      if (filters.resourceType) {
        params.append('resourceType', filters.resourceType);
      }
      
      const response = await api.get(`/api/audit/logs?${params.toString()}`);
      setLogs(response.data.logs);
      setTotalLogs(response.data.total);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      enqueueSnackbar('Failed to fetch audit logs', { variant: 'error' });
      setLoading(false);
    }
  };

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle filter change
  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters({ ...filters, [name]: value });
  };

  // Handle date filter change
  const handleDateChange = (name, date) => {
    setFilters({ ...filters, [name]: date });
  };

  // Clear filters
  const handleClearFilters = () => {
    setFilters({
      startDate: null,
      endDate: null,
      userId: '',
      action: '',
      resourceType: ''
    });
    setPage(0);
  };

  // Toggle filters visibility
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        <SecurityIcon fontSize="large" style={{ verticalAlign: 'middle', marginRight: 8 }} />
        RBAC Audit Logs
      </Typography>
      
      <Card>
        <CardHeader
          title="Audit Logs"
          action={
            <Box>
              <IconButton onClick={toggleFilters}>
                <FilterIcon />
              </IconButton>
              <IconButton onClick={fetchLogs}>
                <RefreshIcon />
              </IconButton>
            </Box>
          }
        />
        
        {showFilters && (
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="Start Date"
                    value={filters.startDate}
                    onChange={(date) => handleDateChange('startDate', date)}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </LocalizationProvider>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="End Date"
                    value={filters.endDate}
                    onChange={(date) => handleDateChange('endDate', date)}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </LocalizationProvider>
              </Grid>
              
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  name="userId"
                  label="User ID"
                  value={filters.userId}
                  onChange={handleFilterChange}
                  fullWidth
                />
              </Grid>
              
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Action</InputLabel>
                  <Select
                    name="action"
                    value={filters.action}
                    onChange={handleFilterChange}
                    label="Action"
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="CREATE">Create</MenuItem>
                    <MenuItem value="UPDATE">Update</MenuItem>
                    <MenuItem value="DELETE">Delete</MenuItem>
                    <MenuItem value="ASSIGN_ROLE">Assign Role</MenuItem>
                    <MenuItem value="REMOVE_ROLE">Remove Role</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Resource Type</InputLabel>
                  <Select
                    name="resourceType"
                    value={filters.resourceType}
                    onChange={handleFilterChange}
                    label="Resource Type"
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="role">Role</MenuItem>
                    <MenuItem value="permission">Permission</MenuItem>
                    <MenuItem value="user_role">User Role</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6} md={2}>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={handleClearFilters}
                  startIcon={<ClearIcon />}
                  fullWidth
                >
                  Clear Filters
                </Button>
              </Grid>
              
              <Grid item xs={12} sm={6} md={2}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={fetchLogs}
                  startIcon={<SearchIcon />}
                  fullWidth
                >
                  Search
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        )}
        
        <Divider />
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Timestamp</TableCell>
                <TableCell>User</TableCell>
                <TableCell>Action</TableCell>
                <TableCell>Resource Type</TableCell>
                <TableCell>Resource ID</TableCell>
                <TableCell>Details</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : logs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    No audit logs found
                  </TableCell>
                </TableRow>
              ) : (
                logs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>{formatDate(log.timestamp)}</TableCell>
                    <TableCell>{log.userId}</TableCell>
                    <TableCell>
                      <Chip
                        label={log.action}
                        color={getActionColor(log.action)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{log.resourceType}</TableCell>
                    <TableCell>{log.resourceId}</TableCell>
                    <TableCell>
                      {log.details ? (
                        <pre style={{ margin: 0, fontSize: '0.8rem' }}>
                          {JSON.stringify(log.details, null, 2)}
                        </pre>
                      ) : (
                        'No details'
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalLogs}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Card>
    </Box>
  );
};

export default RBACLogs;
