#!/bin/bash

# Wait for <PERSON> to be ready
echo "Waiting for Kong to be ready..."
until curl -s http://localhost:8001 > /dev/null; do
  sleep 5
done
echo "Kong is ready!"

# Create services in Kong
echo "Creating services in Kong..."
curl -s -X POST http://localhost:8001/services \
  --data name=governance-service \
  --data url=http://governance-api:3000

curl -s -X POST http://localhost:8001/services \
  --data name=security-service \
  --data url=http://security-api:3000

curl -s -X POST http://localhost:8001/services \
  --data name=apis-service \
  --data url=http://apis-api:3000

# Create routes for the services
echo "Creating routes for the services..."
curl -s -X POST http://localhost:8001/services/governance-service/routes \
  --data "paths[]=/governance" \
  --data name=governance-route

curl -s -X POST http://localhost:8001/services/security-service/routes \
  --data "paths[]=/security" \
  --data name=security-route

curl -s -X POST http://localhost:8001/services/apis-service/routes \
  --data "paths[]=/apis" \
  --data name=apis-route

# Add key-auth plugin to all services
echo "Adding key-auth plugin..."
curl -s -X POST http://localhost:8001/plugins \
  --data name=key-auth

# Create a consumer
echo "Creating consumer..."
curl -s -X POST http://localhost:8001/consumers \
  --data username=partner-app

# Provision key credentials for the consumer
echo "Provisioning API key for consumer..."
curl -s -X POST http://localhost:8001/consumers/partner-app/key-auth \
  --data key=test-api-key

# Add rate limiting plugin
echo "Adding rate limiting plugin..."
curl -s -X POST http://localhost:8001/plugins \
  --data name=rate-limiting \
  --data config.minute=100

# Add NovaFuse usage tracking plugin
echo "Adding NovaFuse usage tracking plugin..."
curl -s -X POST http://localhost:8001/plugins \
  --data name=novafuse-usage-tracker

echo "Kong API Gateway setup complete!"
echo "You can now test the API with:"
echo "curl -i -X GET http://localhost:8000/governance/board/meetings -H \"apikey: test-api-key\""
