/**
 * Integration with the Regulatory Compliance API
 */

const regulatoryModels = require('../../compliance/regulatory/models');

/**
 * Get all regulatory frameworks
 * @returns {Array} - Array of regulatory frameworks
 */
const getAllFrameworks = () => {
  return regulatoryModels.frameworks;
};

/**
 * Get a specific regulatory framework by ID
 * @param {string} id - Framework ID
 * @returns {Object|null} - The framework or null if not found
 */
const getFrameworkById = (id) => {
  return regulatoryModels.frameworks.find(framework => framework.id === id) || null;
};

/**
 * Get all regulatory requirements
 * @returns {Array} - Array of regulatory requirements
 */
const getAllRequirements = () => {
  return regulatoryModels.requirements;
};

/**
 * Get regulatory requirements by framework ID
 * @param {string} frameworkId - Framework ID
 * @returns {Array} - Array of regulatory requirements for the specified framework
 */
const getRequirementsByFramework = (frameworkId) => {
  return regulatoryModels.requirements.filter(req => req.frameworkId === frameworkId);
};

/**
 * Get a specific regulatory requirement by ID
 * @param {string} id - Requirement ID
 * @returns {Object|null} - The requirement or null if not found
 */
const getRequirementById = (id) => {
  return regulatoryModels.requirements.find(req => req.id === id) || null;
};

/**
 * Map a data processing activity to relevant regulatory requirements
 * @param {Object} activity - The data processing activity
 * @returns {Array} - Array of relevant regulatory requirements
 */
const mapActivityToRequirements = (activity) => {
  const relevantRequirements = [];
  
  // Get all requirements
  const allRequirements = getAllRequirements();
  
  // Map based on legal basis
  if (activity.legalBasis === 'consent') {
    // Find consent-related requirements
    const consentRequirements = allRequirements.filter(req => 
      req.keywords && (
        req.keywords.includes('consent') || 
        req.keywords.includes('opt-in') || 
        req.keywords.includes('permission')
      )
    );
    relevantRequirements.push(...consentRequirements);
  }
  
  // Map based on data categories
  if (activity.dataCategories) {
    // Check for special category data
    const hasSpecialCategoryData = activity.dataCategories.some(category => 
      category.toLowerCase().includes('health') || 
      category.toLowerCase().includes('biometric') || 
      category.toLowerCase().includes('genetic') || 
      category.toLowerCase().includes('racial') || 
      category.toLowerCase().includes('ethnic') || 
      category.toLowerCase().includes('political') || 
      category.toLowerCase().includes('religious') || 
      category.toLowerCase().includes('sexual')
    );
    
    if (hasSpecialCategoryData) {
      // Find special category data requirements
      const specialCategoryRequirements = allRequirements.filter(req => 
        req.keywords && (
          req.keywords.includes('special category') || 
          req.keywords.includes('sensitive data') || 
          req.keywords.includes('special data')
        )
      );
      relevantRequirements.push(...specialCategoryRequirements);
    }
  }
  
  // Map based on cross-border transfers
  if (activity.crossBorderTransfers && activity.crossBorderTransfers.length > 0) {
    // Find cross-border transfer requirements
    const transferRequirements = allRequirements.filter(req => 
      req.keywords && (
        req.keywords.includes('transfer') || 
        req.keywords.includes('cross-border') || 
        req.keywords.includes('international')
      )
    );
    relevantRequirements.push(...transferRequirements);
  }
  
  // Map based on DPIA
  if (activity.dpia && activity.dpia.required) {
    // Find DPIA requirements
    const dpiaRequirements = allRequirements.filter(req => 
      req.keywords && (
        req.keywords.includes('dpia') || 
        req.keywords.includes('data protection impact assessment') || 
        req.keywords.includes('impact assessment')
      )
    );
    relevantRequirements.push(...dpiaRequirements);
  }
  
  // Remove duplicates
  const uniqueRequirements = [...new Map(relevantRequirements.map(req => [req.id, req])).values()];
  
  return uniqueRequirements;
};

/**
 * Get compliance status for a data processing activity
 * @param {Object} activity - The data processing activity
 * @returns {Object} - Compliance status object
 */
const getActivityComplianceStatus = (activity) => {
  // Get relevant requirements
  const relevantRequirements = mapActivityToRequirements(activity);
  
  // Calculate compliance status
  const totalRequirements = relevantRequirements.length;
  let compliantCount = 0;
  let partiallyCompliantCount = 0;
  let nonCompliantCount = 0;
  let notAssessedCount = 0;
  
  // In a real implementation, this would check actual compliance status
  // For now, we'll use a simple heuristic based on the activity properties
  
  // Check for consent compliance
  if (activity.legalBasis === 'consent') {
    if (activity.consentRecords && activity.consentRecords.length > 0) {
      compliantCount++;
    } else {
      nonCompliantCount++;
    }
  }
  
  // Check for DPIA compliance
  if (activity.dpia && activity.dpia.required) {
    if (activity.dpia.completed) {
      compliantCount++;
    } else {
      nonCompliantCount++;
    }
  }
  
  // Check for cross-border transfer compliance
  if (activity.crossBorderTransfers && activity.crossBorderTransfers.length > 0) {
    const compliantTransfers = activity.crossBorderTransfers.filter(transfer => 
      transfer.adequacyDecision || transfer.mechanism
    );
    
    if (compliantTransfers.length === activity.crossBorderTransfers.length) {
      compliantCount++;
    } else if (compliantTransfers.length > 0) {
      partiallyCompliantCount++;
    } else {
      nonCompliantCount++;
    }
  }
  
  // Remaining requirements are considered not assessed
  notAssessedCount = totalRequirements - compliantCount - partiallyCompliantCount - nonCompliantCount;
  
  return {
    totalRequirements,
    compliantCount,
    partiallyCompliantCount,
    nonCompliantCount,
    notAssessedCount,
    complianceScore: totalRequirements > 0 
      ? Math.round((compliantCount + (partiallyCompliantCount * 0.5)) / totalRequirements * 100) 
      : 100,
    relevantRequirements
  };
};

/**
 * Get regulatory changes that may affect a data processing activity
 * @param {Object} activity - The data processing activity
 * @returns {Array} - Array of relevant regulatory changes
 */
const getRelevantRegulatoryChanges = (activity) => {
  // Get all regulatory changes
  const allChanges = regulatoryModels.changes;
  
  // Filter changes based on activity properties
  const relevantChanges = allChanges.filter(change => {
    // Check if the change affects the activity's legal basis
    if (change.affectedAreas && change.affectedAreas.includes(activity.legalBasis)) {
      return true;
    }
    
    // Check if the change affects any of the activity's data categories
    if (change.affectedDataCategories && activity.dataCategories) {
      for (const category of activity.dataCategories) {
        if (change.affectedDataCategories.includes(category)) {
          return true;
        }
      }
    }
    
    // Check if the change affects cross-border transfers
    if (change.affectedAreas && change.affectedAreas.includes('cross-border-transfers') && 
        activity.crossBorderTransfers && activity.crossBorderTransfers.length > 0) {
      return true;
    }
    
    return false;
  });
  
  return relevantChanges;
};

module.exports = {
  getAllFrameworks,
  getFrameworkById,
  getAllRequirements,
  getRequirementsByFramework,
  getRequirementById,
  mapActivityToRequirements,
  getActivityComplianceStatus,
  getRelevantRegulatoryChanges
};
