import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'NHET-X Quantum Intelligence Platform',
  description: 'Reality Studio for the Post-Human Era - We don\'t predict markets, we compile realities.',
  keywords: ['consciousness', 'reality-programming', 'quantum-intelligence', 'NHET-X', 'HOD-patent'],
  authors: [{ name: '<PERSON>', email: '<EMAIL>' }],
  creator: 'NovaFuse Technologies',
  publisher: 'NHET-X Platform',
  robots: 'index, follow',
  openGraph: {
    title: 'NHET-X Quantum Intelligence Platform',
    description: 'The first programmable reality interface ever conceived',
    url: 'https://nhetx.ai',
    siteName: 'NHET-X Platform',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'NHET-X Consciousness Technology',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'NHET-X Quantum Intelligence Platform',
    description: 'Reality Studio for the Post-Human Era',
    images: ['/twitter-image.png'],
    creator: '@nhetx_ai',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.className} bg-gradient-to-br from-black via-gray-900 to-black min-h-screen`}>
        <div className="fixed top-0 left-0 w-full h-full bg-[url('/consciousness-field.svg')] opacity-10 pointer-events-none" />
        
        {/* HOD Patent Badge */}
        <div className="fixed top-4 right-4 z-50 bg-black/40 backdrop-blur-md border border-cyan-500/50 rounded-full px-4 py-2 text-cyan-300 text-sm font-medium">
          🏛️ HOD Patent Protected
        </div>

        {/* Consciousness Field Indicator */}
        <div className="fixed bottom-4 left-4 z-50 bg-black/40 backdrop-blur-md border border-purple-500/50 rounded-lg px-3 py-2">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            <span className="text-green-300 text-xs font-medium">Consciousness Field: ACTIVE</span>
          </div>
        </div>

        {/* κ-Token Balance */}
        <div className="fixed bottom-4 right-4 z-50 bg-black/40 backdrop-blur-md border border-yellow-500/50 rounded-lg px-3 py-2">
          <div className="flex items-center space-x-2">
            <span className="text-yellow-300 text-xs font-medium">κ Balance:</span>
            <span className="text-yellow-200 text-xs font-bold">1,337.314</span>
          </div>
        </div>

        {/* NovaFuse Technologies Branding */}
        <div className="fixed bottom-4 center z-50 left-1/2 transform -translate-x-1/2 bg-black/40 backdrop-blur-md border border-cyan-500/50 rounded-lg px-4 py-2">
          <div className="text-center">
            <div className="text-cyan-300 text-xs font-medium">Created by</div>
            <div className="text-cyan-200 text-sm font-bold">NovaFuse Technologies</div>
            <div className="text-cyan-400 text-xs">A Comphyology-based company</div>
          </div>
        </div>

        <main className="relative z-10">
          {children}
        </main>

        {/* Consciousness Particles Animation */}
        <div className="fixed inset-0 pointer-events-none overflow-hidden">
          {[...Array(30)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-cyan-300/70 rounded-full animate-float shadow-lg shadow-cyan-400/50"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 10}s`,
                animationDuration: `${10 + Math.random() * 20}s`,
              }}
            />
          ))}
          {[...Array(15)].map((_, i) => (
            <div
              key={`star-${i}`}
              className="absolute w-0.5 h-0.5 bg-white/90 rounded-full animate-pulse"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${2 + Math.random() * 4}s`,
              }}
            />
          ))}
        </div>
      </body>
    </html>
  )
}
