const request = require('supertest');
const express = require('express');
const router = require('../../../../apis/esg/frameworks/routes');
const models = require('../../../../apis/esg/frameworks/models');

// Create a test Express app
const app = express();
app.use(express.json());
app.use('/governance/esg/frameworks', router);

// Generate a large dataset for performance testing
const generateLargeFrameworkDataset = (count) => {
  const frameworks = [];
  for (let i = 0; i < count; i++) {
    frameworks.push({
      id: `esg-f-${i.toString().padStart(8, '0')}`,
      name: `Test Framework ${i}`,
      description: `Description for test framework ${i}`,
      version: `202${i % 5}`,
      category: i % 3 === 0 ? 'reporting' : i % 3 === 1 ? 'assessment' : 'management',
      website: `https://example.com/framework-${i}`,
      status: i % 2 === 0 ? 'active' : 'inactive',
      elements: generateElements(i, 5 + (i % 10)), // Each framework has 5-14 elements
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }
  return frameworks;
};

// Generate elements for a framework
const generateElements = (frameworkIndex, count) => {
  const elements = [];
  for (let i = 0; i < count; i++) {
    elements.push({
      id: `element-${frameworkIndex}-${i}`,
      code: `CODE-${frameworkIndex}-${i}`,
      name: `Element ${i} of Framework ${frameworkIndex}`,
      description: `Description for element ${i} of framework ${frameworkIndex}`,
      category: i % 4 === 0 ? 'environmental' : i % 4 === 1 ? 'social' : i % 4 === 2 ? 'governance' : 'economic',
      metrics: [`metric-${i}-1`, `metric-${i}-2`, `metric-${i}-3`]
    });
  }
  return elements;
};

// Generate mappings between frameworks
const generateMappings = (frameworkCount, mappingCount) => {
  const mappings = [];
  for (let i = 0; i < mappingCount; i++) {
    const sourceFrameworkIndex = i % frameworkCount;
    const targetFrameworkIndex = (i + 1) % frameworkCount;
    const sourceElementIndex = i % 5;
    const targetElementIndex = (i + 1) % 5;
    
    mappings.push({
      id: `mapping-${i.toString().padStart(5, '0')}`,
      sourceFrameworkId: `esg-f-${sourceFrameworkIndex.toString().padStart(8, '0')}`,
      sourceElementId: `element-${sourceFrameworkIndex}-${sourceElementIndex}`,
      targetFrameworkId: `esg-f-${targetFrameworkIndex.toString().padStart(8, '0')}`,
      targetElementId: `element-${targetFrameworkIndex}-${targetElementIndex}`,
      mappingType: i % 3 === 0 ? 'equivalent' : i % 3 === 1 ? 'related' : 'partial',
      description: `Mapping ${i} between frameworks`,
      confidence: i % 3 === 0 ? 'high' : i % 3 === 1 ? 'medium' : 'low',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }
  return mappings;
};

// Mock the models with a large dataset
const FRAMEWORK_COUNT = 100;
const MAPPING_COUNT = 500;

jest.mock('../../../../apis/esg/frameworks/models', () => {
  const frameworks = generateLargeFrameworkDataset(FRAMEWORK_COUNT);
  const mappings = generateMappings(FRAMEWORK_COUNT, MAPPING_COUNT);
  return {
    esgFrameworks: frameworks,
    frameworkMappings: mappings
  };
});

describe('ESG Frameworks API Performance Tests', () => {
  // Set a longer timeout for performance tests
  jest.setTimeout(30000);

  describe('GET /governance/esg/frameworks', () => {
    it('should handle pagination efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/governance/esg/frameworks?page=1&limit=20');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(20);
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
      console.log(`Pagination response time: ${responseTime}ms`);
    });

    it('should handle filtering efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/governance/esg/frameworks?category=reporting&status=active');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
      console.log(`Filtering response time: ${responseTime}ms`);
    });
  });

  describe('GET /governance/esg/frameworks/:id', () => {
    it('should retrieve a specific framework efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/governance/esg/frameworks/esg-f-00000050');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('esg-f-00000050');
      
      // Response time should be under 100ms
      expect(responseTime).toBeLessThan(100);
      console.log(`Get framework response time: ${responseTime}ms`);
    });
  });

  describe('GET /governance/esg/frameworks/:id/elements', () => {
    it('should retrieve elements for a framework efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/governance/esg/frameworks/esg-f-00000025/elements');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 100ms
      expect(responseTime).toBeLessThan(100);
      console.log(`Get elements response time: ${responseTime}ms`);
    });
  });

  describe('GET /governance/esg/frameworks/:id/mappings', () => {
    it('should retrieve mappings for a framework efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/governance/esg/frameworks/esg-f-00000010/mappings');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Get mappings response time: ${responseTime}ms`);
    });
  });

  describe('POST /governance/esg/frameworks', () => {
    it('should create a new framework efficiently', async () => {
      const newFramework = {
        name: 'Performance Test Framework',
        description: 'Framework for performance testing',
        version: '2023',
        category: 'reporting',
        website: 'https://example.com/performance-test',
        status: 'active',
        elements: [
          {
            code: 'PERF-1',
            name: 'Performance Element 1',
            description: 'Description for performance element 1',
            category: 'environmental',
            metrics: ['metric-1', 'metric-2']
          }
        ]
      };

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/governance/esg/frameworks')
        .send(newFramework);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Create framework response time: ${responseTime}ms`);
    });
  });

  describe('POST /governance/esg/frameworks/:id/elements', () => {
    it('should add a new element efficiently', async () => {
      const newElement = {
        code: 'PERF-E-1',
        name: 'Performance Test Element',
        description: 'Element for performance testing',
        category: 'social',
        metrics: ['metric-a', 'metric-b']
      };

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/governance/esg/frameworks/esg-f-00000015/elements')
        .send(newElement);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Add element response time: ${responseTime}ms`);
    });
  });

  describe('POST /governance/esg/frameworks/mappings', () => {
    it('should create a new mapping efficiently', async () => {
      const newMapping = {
        sourceFrameworkId: 'esg-f-00000020',
        sourceElementId: 'element-20-1',
        targetFrameworkId: 'esg-f-00000030',
        targetElementId: 'element-30-1',
        mappingType: 'equivalent',
        description: 'Performance test mapping',
        confidence: 'high'
      };

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/governance/esg/frameworks/mappings')
        .send(newMapping);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Create mapping response time: ${responseTime}ms`);
    });
  });

  describe('Concurrent requests', () => {
    it('should handle multiple concurrent requests efficiently', async () => {
      const startTime = Date.now();
      
      // Make 10 concurrent requests
      const requests = [];
      for (let i = 0; i < 10; i++) {
        requests.push(request(app).get(`/governance/esg/frameworks?page=${i+1}&limit=10`));
      }
      
      const responses = await Promise.all(requests);
      
      const endTime = Date.now();
      const totalResponseTime = endTime - startTime;
      
      // All responses should be successful
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
      });
      
      // Total response time for 10 concurrent requests should be under 1000ms
      expect(totalResponseTime).toBeLessThan(1000);
      console.log(`Concurrent requests response time: ${totalResponseTime}ms`);
    });

    it('should handle concurrent read and write operations efficiently', async () => {
      const startTime = Date.now();
      
      // Create a mix of read and write operations
      const requests = [];
      
      // Read operations
      requests.push(request(app).get('/governance/esg/frameworks?page=1&limit=10'));
      requests.push(request(app).get('/governance/esg/frameworks/esg-f-00000005'));
      requests.push(request(app).get('/governance/esg/frameworks/esg-f-00000005/elements'));
      requests.push(request(app).get('/governance/esg/frameworks/esg-f-00000005/mappings'));
      
      // Write operations
      const newFramework = {
        name: 'Concurrent Test Framework',
        description: 'Framework for concurrent testing',
        version: '2023',
        category: 'reporting',
        website: 'https://example.com/concurrent-test',
        status: 'active',
        elements: []
      };
      
      const newElement = {
        code: 'CONC-1',
        name: 'Concurrent Test Element',
        description: 'Element for concurrent testing',
        category: 'governance',
        metrics: ['metric-x', 'metric-y']
      };
      
      requests.push(request(app).post('/governance/esg/frameworks').send(newFramework));
      requests.push(request(app).post('/governance/esg/frameworks/esg-f-00000010/elements').send(newElement));
      
      const responses = await Promise.all(requests);
      
      const endTime = Date.now();
      const totalResponseTime = endTime - startTime;
      
      // All responses should be successful
      responses.forEach(response => {
        expect(response.status).toBe(200) || expect(response.status).toBe(201);
      });
      
      // Total response time for mixed operations should be under 1000ms
      expect(totalResponseTime).toBeLessThan(1000);
      console.log(`Mixed operations response time: ${totalResponseTime}ms`);
    });
  });

  describe('Load testing', () => {
    it('should handle a large number of sequential requests', async () => {
      const requestCount = 50;
      const startTime = Date.now();
      
      // Make sequential requests
      for (let i = 0; i < requestCount; i++) {
        const response = await request(app).get(`/governance/esg/frameworks?page=1&limit=5`);
        expect(response.status).toBe(200);
      }
      
      const endTime = Date.now();
      const totalResponseTime = endTime - startTime;
      const averageResponseTime = totalResponseTime / requestCount;
      
      // Average response time should be under 20ms
      expect(averageResponseTime).toBeLessThan(20);
      console.log(`Sequential requests average response time: ${averageResponseTime}ms`);
    });
  });

  describe('Performance with large datasets', () => {
    it('should handle retrieving a framework with many elements efficiently', async () => {
      // Find a framework with many elements
      const frameworkWithManyElements = models.esgFrameworks.find(f => f.elements.length > 10);
      
      const startTime = Date.now();
      
      const response = await request(app).get(`/governance/esg/frameworks/${frameworkWithManyElements.id}`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.elements.length).toBeGreaterThan(10);
      
      // Response time should be under 150ms even with many elements
      expect(responseTime).toBeLessThan(150);
      console.log(`Large framework response time: ${responseTime}ms`);
    });

    it('should handle retrieving a framework with many mappings efficiently', async () => {
      // Find a framework with many mappings
      const frameworkId = 'esg-f-00000000'; // First framework likely has many mappings
      
      const startTime = Date.now();
      
      const response = await request(app).get(`/governance/esg/frameworks/${frameworkId}/mappings`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 200ms even with many mappings
      expect(responseTime).toBeLessThan(200);
      console.log(`Framework with many mappings response time: ${responseTime}ms`);
    });
  });
});
