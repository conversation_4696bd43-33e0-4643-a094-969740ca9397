# Financial Services Continuance Patent Diagrams

This React application provides professional, patent-ready diagrams for the Financial Services Continuance Patent. The diagrams are designed to be clean, clear, and suitable for inclusion in patent applications.

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

1. Navigate to the project directory:
   ```
   cd patent-diagrams
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm start
   ```

4. Open your browser to [http://localhost:3000](http://localhost:3000)

## Taking Screenshots

1. Select the diagram you want to capture using the buttons at the top of the page
2. Use your operating system's screenshot tool to capture the diagram:
   - **Windows**: Use Windows+Shift+S to open the snipping tool
   - **Mac**: Use Command+Shift+4 to capture a selected area
3. Save the screenshot with the figure number and name (e.g., "FIG. 2 - Automated Audit Trail Generation.png")
4. Repeat for all diagrams

## Diagram Descriptions

The application includes the following diagrams:

1. **FIG. 1: Financial Services System Architecture**
   - Overall architecture of the financial services-specific implementation of the Cyber-Safety Protocol

2. **FIG. 2: Automated Audit Trail Generation**
   - Detailed diagram of the automated audit trail generation process

3. **FIG. 3: Explainable AI with Rule Attribution**
   - Architecture of the explainable AI system for fraud prediction

4. **FIG. 4: DeFi Smart Contract Compliance Layer**
   - Detailed diagram of the DeFi compliance layer

5. **FIG. 5: IoT Payment Device PCI-DSS Validation**
   - Architecture of the IoT payment security system

6. **FIG. 6: Regulatory Kill Switch**
   - Detailed diagram of the regulatory kill switch mechanism

7. **FIG. 7: Dynamic Risk Scoring Engine**
   - Architecture of the dynamic risk scoring system

8. **FIG. 8: Self-Learning Fraud System with Adaptive Thresholds**
   - Detailed diagram of the self-learning system

9. **FIG. 9: Cross-Border Transaction Compliance Overlays**
   - Architecture of the cross-border compliance system

10. **FIG. 10: Fraud-to-Compliance Bridge API**
    - Detailed diagram of the unified API bridge

## Patent Compliance

These diagrams are designed to meet USPTO requirements for patent drawings:
- Clean, well-defined lines
- Proper labeling of all components
- Reference numbers for all components
- Consistent style across all figures

## Customization

If you need to customize any of the diagrams:

1. Navigate to the corresponding diagram file in the `src/diagrams` directory
2. Modify the component positions, labels, or connections as needed
3. Save the file and the changes will be reflected in the application

## Troubleshooting

If you encounter any issues:

- Make sure all dependencies are installed (`npm install`)
- Check the browser console for any errors
- Try restarting the development server (`npm start`)
