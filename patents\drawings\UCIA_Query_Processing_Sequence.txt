```
+-------------------------------------------------------------------------------------------------------------+
|                                QUERY PROCESSING SEQUENCE                                                    |
+-------------------------------------------------------------------------------------------------------------+

+----------+          +-----------+          +------------+          +------------+          +------------+
|          |          |           |          |            |          |            |          |            |
|  USER    |          |  CORE     |          |  MODULE    |          |  KNOWLEDGE |          |  RESPONSE  |
|          |          |  ENGINE   |          |  REGISTRY  |          |  DISTILL.  |          |  GENERATOR |
|          |          |           |          |            |          |            |          |            |
+----+-----+          +-----+-----+          +-----+------+          +-----+------+          +-----+------+
     |                      |                      |                       |                       |
     | 1. Submit Query      |                      |                       |                       |
     |--------------------->|                      |                       |                       |
     |                      |                      |                       |                       |
     |                      | 2. Extract Context   |                       |                       |
     |                      |-------------------+  |                       |                       |
     |                      |                  |   |                       |                       |
     |                      |<------------------+  |                       |                       |
     |                      |                      |                       |                       |
     |                      | 3. Interpret Query   |                       |                       |
     |                      |-------------------+  |                       |                       |
     |                      |                  |   |                       |                       |
     |                      |<------------------+  |                       |                       |
     |                      |                      |                       |                       |
     |                      | 4. Detect Frameworks |                       |                       |
     |                      |--------------------->|                       |                       |
     |                      |                      |                       |                       |
     |                      |                      | 5. Activate Modules   |                       |
     |                      |                      |-------------------+   |                       |
     |                      |                      |                  |    |                       |
     |                      |                      |<------------------+   |                       |
     |                      |                      |                       |                       |
     |                      | 6. Return Active Modules                     |                       |
     |                      |<---------------------------------------------|                       |
     |                      |                      |                       |                       |
     |                      | 7. Process Query     |                       |                       |
     |                      |--------------------->|                       |                       |
     |                      |                      |                       |                       |
     |                      |                      | 8. Framework-Specific |                       |
     |                      |                      | Processing            |                       |
     |                      |                      |-------------------+   |                       |
     |                      |                      |                  |    |                       |
     |                      |                      |<------------------+   |                       |
     |                      |                      |                       |                       |
     |                      | 9. Return Framework-Specific Results         |                       |
     |                      |<---------------------------------------------|                       |
     |                      |                      |                       |                       |
     |                      | 10. Distill Knowledge|                       |                       |
     |                      |---------------------------------------------->                       |
     |                      |                      |                       |                       |
     |                      |                      |                       | 11. Cross-Framework   |
     |                      |                      |                       | Analysis              |
     |                      |                      |                       |-------------------+   |
     |                      |                      |                       |                  |    |
     |                      |                      |                       |<------------------+   |
     |                      |                      |                       |                       |
     |                      | 12. Return Unified Knowledge                 |                       |
     |                      |<----------------------------------------------|                      |
     |                      |                      |                       |                       |
     |                      | 13. Generate Response|                       |                       |
     |                      |-------------------------------------------------------------->       |
     |                      |                      |                       |                       |
     |                      |                      |                       |                       | 14. Format
     |                      |                      |                       |                       | Response
     |                      |                      |                       |                       |----------+
     |                      |                      |                       |                       |         |
     |                      |                      |                       |                       |<---------+
     |                      |                      |                       |                       |
     |                      | 15. Return Formatted Response                |                       |
     |                      |<--------------------------------------------------------------|       |
     |                      |                      |                       |                       |
     | 16. Deliver Response |                      |                       |                       |
     |<---------------------|                      |                       |                       |
     |                      |                      |                       |                       |
     | 17. Provide Feedback |                      |                       |                       |
     |--------------------->|                      |                       |                       |
     |                      |                      |                       |                       |
     |                      | 18. Update Models    |                       |                       |
     |                      |-------------------+  |                       |                       |
     |                      |                  |   |                       |                       |
     |                      |<------------------+  |                       |                       |
     |                      |                      |                       |                       |

FIG. 9 - Query Processing Sequence Diagram
```
