/**
 * ProviderConnector.js
 * 
 * This module provides specific connectors for major healthcare providers.
 * It extends the NovaConnectAdapter with provider-specific functionality.
 */

const NovaConnectAdapter = require('../NovaConnectAdapter');

/**
 * ProviderConnector class for connecting to specific healthcare providers
 */
class ProviderConnector {
  constructor(options = {}) {
    this.novaConnectAdapter = options.novaConnectAdapter || new NovaConnectAdapter(options);
    this.providerConnections = new Map();
    this.connectionStatus = new Map();
  }

  /**
   * Connect to Epic EHR system
   * @param {Object} credentials - The credentials for Epic
   * @returns {Promise<Object>} - The connection result
   */
  async connectToEpic(credentials) {
    try {
      // Validate credentials
      if (!credentials.clientId || !credentials.clientSecret) {
        throw new Error('Epic credentials must include clientId and clientSecret');
      }
      
      // Connect to Epic using NovaConnectAdapter
      const result = await this.novaConnectAdapter.connectToEHR('Epic', {
        clientId: credentials.clientId,
        clientSecret: credentials.clientSecret,
        redirectUri: credentials.redirectUri,
        scope: credentials.scope || 'patient/*.read'
      });
      
      // Store connection
      this.providerConnections.set('Epic', result);
      this.connectionStatus.set('Epic', {
        status: 'CONNECTED',
        connectedAt: new Date().toISOString(),
        lastChecked: new Date().toISOString()
      });
      
      return result;
    } catch (error) {
      this.connectionStatus.set('Epic', {
        status: 'ERROR',
        error: error.message,
        lastChecked: new Date().toISOString()
      });
      
      throw error;
    }
  }

  /**
   * Connect to Cerner EHR system
   * @param {Object} credentials - The credentials for Cerner
   * @returns {Promise<Object>} - The connection result
   */
  async connectToCerner(credentials) {
    try {
      // Validate credentials
      if (!credentials.clientId || !credentials.clientSecret) {
        throw new Error('Cerner credentials must include clientId and clientSecret');
      }
      
      // Connect to Cerner using NovaConnectAdapter
      const result = await this.novaConnectAdapter.connectToEHR('Cerner', {
        clientId: credentials.clientId,
        clientSecret: credentials.clientSecret,
        redirectUri: credentials.redirectUri,
        scope: credentials.scope || 'patient/*.read'
      });
      
      // Store connection
      this.providerConnections.set('Cerner', result);
      this.connectionStatus.set('Cerner', {
        status: 'CONNECTED',
        connectedAt: new Date().toISOString(),
        lastChecked: new Date().toISOString()
      });
      
      return result;
    } catch (error) {
      this.connectionStatus.set('Cerner', {
        status: 'ERROR',
        error: error.message,
        lastChecked: new Date().toISOString()
      });
      
      throw error;
    }
  }

  /**
   * Fetch patient data from a specific provider
   * @param {String} provider - The provider name (Epic, Cerner, etc.)
   * @param {String} patientId - The patient ID
   * @param {Object} options - Options for fetching data
   * @returns {Promise<Object>} - The patient data
   */
  async fetchPatientDataFromProvider(provider, patientId, options = {}) {
    // Check if we have a connection to this provider
    const connection = this.providerConnections.get(provider);
    
    if (!connection) {
      throw new Error(`Not connected to ${provider}`);
    }
    
    try {
      // Fetch patient data using NovaConnectAdapter
      const patientData = await this.novaConnectAdapter.fetchPatientData(
        connection.connectionId,
        patientId,
        options
      );
      
      // Update connection status
      this.connectionStatus.set(provider, {
        ...this.connectionStatus.get(provider),
        lastChecked: new Date().toISOString(),
        lastSuccess: new Date().toISOString()
      });
      
      return patientData;
    } catch (error) {
      // Update connection status
      this.connectionStatus.set(provider, {
        ...this.connectionStatus.get(provider),
        lastChecked: new Date().toISOString(),
        lastError: error.message
      });
      
      throw error;
    }
  }

  /**
   * Check connection status for all providers
   * @returns {Promise<Object>} - The connection status for all providers
   */
  async checkConnectionStatus() {
    const status = {};
    
    for (const [provider, connection] of this.providerConnections.entries()) {
      try {
        // Make a simple API call to check connection
        await this.novaConnectAdapter.client.get(`/ehr/${connection.connectionId}/status`);
        
        // Update connection status
        this.connectionStatus.set(provider, {
          ...this.connectionStatus.get(provider),
          status: 'CONNECTED',
          lastChecked: new Date().toISOString()
        });
        
        status[provider] = {
          status: 'CONNECTED',
          lastChecked: new Date().toISOString()
        };
      } catch (error) {
        // Update connection status
        this.connectionStatus.set(provider, {
          ...this.connectionStatus.get(provider),
          status: 'ERROR',
          lastChecked: new Date().toISOString(),
          lastError: error.message
        });
        
        status[provider] = {
          status: 'ERROR',
          lastChecked: new Date().toISOString(),
          error: error.message
        };
      }
    }
    
    return status;
  }

  /**
   * Get connection status for all providers
   * @returns {Object} - The connection status
   */
  getConnectionStatus() {
    const status = {};
    
    for (const [provider, connectionStatus] of this.connectionStatus.entries()) {
      status[provider] = connectionStatus;
    }
    
    return status;
  }

  /**
   * Disconnect from a provider
   * @param {String} provider - The provider to disconnect from
   * @returns {Promise<Boolean>} - Whether the disconnection was successful
   */
  async disconnectFromProvider(provider) {
    const connection = this.providerConnections.get(provider);
    
    if (!connection) {
      return false;
    }
    
    try {
      // Disconnect from provider
      await this.novaConnectAdapter.client.post(`/ehr/${connection.connectionId}/disconnect`);
      
      // Remove connection
      this.providerConnections.delete(provider);
      
      // Update connection status
      this.connectionStatus.set(provider, {
        status: 'DISCONNECTED',
        disconnectedAt: new Date().toISOString(),
        lastChecked: new Date().toISOString()
      });
      
      return true;
    } catch (error) {
      // Update connection status
      this.connectionStatus.set(provider, {
        ...this.connectionStatus.get(provider),
        lastChecked: new Date().toISOString(),
        lastError: error.message
      });
      
      return false;
    }
  }
}

module.exports = ProviderConnector;
