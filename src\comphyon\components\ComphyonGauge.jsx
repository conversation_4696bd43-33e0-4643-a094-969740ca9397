import React from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CircularProgress, Typography } from '@mui/material';

/**
 * ComphyonGauge Component
 * 
 * Displays a circular gauge showing the Comphyon (Cph) value with color-coded indicators.
 * 
 * @param {Object} props - Component props
 * @param {number} props.value - Comphyon value
 * @param {string} props.title - Title for the gauge
 * @param {string} props.subtitle - Subtitle for the gauge
 * @param {Object} props.thresholds - Value thresholds for color coding
 * @param {number} props.thresholds.low - Threshold for low value (red)
 * @param {number} props.thresholds.medium - Threshold for medium value (yellow)
 * @param {number} props.thresholds.high - Threshold for high value (green)
 * @param {number} props.size - Size of the gauge in pixels
 * @param {Object} props.metrics - Additional metrics to display
 */
const ComphyonGauge = ({ 
  value = 0, 
  title = 'Comphyon (Cph)', 
  subtitle = 'Emergent intelligence metric',
  thresholds = { low: 0.5, medium: 1.0, high: 2.0 },
  size = 200,
  metrics = {}
}) => {
  // Ensure value is non-negative
  const normalizedValue = Math.max(0, value);
  
  // Calculate percentage for circular progress (max 100%)
  const progressPercentage = Math.min(normalizedValue * 33.33, 100);
  
  // Determine color based on value thresholds
  const getValueColor = (value) => {
    if (value >= thresholds.high) return 'success.main';
    if (value >= thresholds.medium) return 'warning.main';
    return 'error.main';
  };
  
  // Get status text based on value
  const getStatusText = (value) => {
    if (value >= thresholds.high) return 'High Emergence';
    if (value >= thresholds.medium) return 'Moderate Emergence';
    return 'Low Emergence';
  };
  
  const color = getValueColor(normalizedValue);
  const statusText = getStatusText(normalizedValue);
  
  return (
    <Card sx={{ maxWidth: size + 50 }}>
      <CardHeader
        title={title}
        subheader={subtitle}
        titleTypographyProps={{ variant: 'h6' }}
        subheaderTypographyProps={{ variant: 'body2' }}
      />
      <CardContent sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Box sx={{ position: 'relative', display: 'inline-flex', mb: 2 }}>
          <CircularProgress
            variant="determinate"
            value={100}
            size={size}
            thickness={4}
            sx={{ color: 'grey.200' }}
          />
          <CircularProgress
            variant="determinate"
            value={progressPercentage}
            size={size}
            thickness={4}
            sx={{ 
              color: color,
              position: 'absolute',
              left: 0,
            }}
          />
          <Box
            sx={{
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              position: 'absolute',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column'
            }}
          >
            <Typography variant="h4" component="div" color="text.primary">
              {normalizedValue.toFixed(2)}
            </Typography>
            <Typography variant="body2" component="div" color="text.secondary">
              Cph
            </Typography>
          </Box>
        </Box>
        
        <Typography variant="body1" color={color} sx={{ mb: 2 }}>
          {statusText}
        </Typography>
        
        {/* Additional metrics */}
        {Object.keys(metrics).length > 0 && (
          <Box sx={{ width: '100%', mt: 2 }}>
            {Object.entries(metrics).map(([key, value]) => (
              <Box key={key} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  {key}:
                </Typography>
                <Typography variant="body2" color="text.primary">
                  {typeof value === 'number' ? value.toFixed(2) : value}
                </Typography>
              </Box>
            ))}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default ComphyonGauge;
