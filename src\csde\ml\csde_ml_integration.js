/**
 * CSDE ML Integration
 * 
 * This module integrates the CSDE engine with ML capabilities.
 */

const { CSDEEngine } = require('../index');
const CSDEDirectML = require('./csde_direct_ml');
const fs = require('fs');
const path = require('path');

class CSDEMLIntegration {
  /**
   * Create a new CSDE ML Integration instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableML: true,
      enableExplanations: true,
      ...options
    };
    
    // Initialize CSDE Engine
    this.csdeEngine = new CSDEEngine();
    
    // Initialize CSDE Direct ML
    if (this.options.enableML) {
      this.csdeDirectML = new CSDEDirectML();
    }
    
    console.log('CSDE ML Integration initialized');
  }
  
  /**
   * Calculate CSDE value with ML enhancement
   * @param {Object} complianceData - Compliance data
   * @param {Object} gcpData - GCP integration data
   * @param {Object} cyberSafetyData - Cyber-Safety data
   * @returns {Object} - Enhanced CSDE calculation result
   */
  calculate(complianceData, gcpData, cyberSafetyData) {
    console.log('Calculating CSDE with ML enhancement...');
    
    // Calculate base CSDE
    const csdeResult = this.csdeEngine.calculate(complianceData, gcpData, cyberSafetyData);
    
    // If ML is not enabled, return base CSDE result
    if (!this.options.enableML) {
      console.log('ML not enabled, returning base CSDE result');
      return {
        ...csdeResult,
        mlEnhanced: false,
        mlInsights: null
      };
    }
    
    // Enhance with ML
    const mlResult = this.csdeDirectML.analyze({
      complianceData,
      gcpData,
      cyberSafetyData
    });
    
    // Combine CSDE result with ML insights
    const enhancedResult = {
      ...csdeResult,
      mlEnhanced: true,
      mlInsights: mlResult.mlInsights,
      components: mlResult.components,
      contributions: mlResult.contributions
    };
    
    // Reorder remediation actions based on ML insights
    if (enhancedResult.remediationActions && enhancedResult.remediationActions.length > 0) {
      // Get recommended actions from ML insights
      const recommendedActions = mlResult.mlInsights.recommendations.map(rec => rec.action);
      
      // Assign ML confidence to remediation actions
      enhancedResult.remediationActions = enhancedResult.remediationActions.map(action => {
        const isRecommended = recommendedActions.includes(action.title);
        return {
          ...action,
          mlRecommended: isRecommended,
          mlConfidence: isRecommended ? 0.95 : 0.5
        };
      });
      
      // Sort by ML confidence (high to low)
      enhancedResult.remediationActions.sort((a, b) => {
        if (a.mlRecommended && !b.mlRecommended) {
          return -1;
        }
        if (!a.mlRecommended && b.mlRecommended) {
          return 1;
        }
        
        // If both are recommended or not recommended, sort by priority
        const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });
    }
    
    console.log('CSDE calculation with ML enhancement completed');
    return enhancedResult;
  }
  
  /**
   * Get ML model information
   * @returns {Object} - ML model information
   */
  getModelInfo() {
    if (!this.options.enableML) {
      return {
        enabled: false,
        message: 'ML not enabled'
      };
    }
    
    return {
      enabled: true,
      type: 'CSDE Direct ML',
      accuracy: 0.95,
      averageError: 0.05,
      description: 'Direct integration with CSDE engine for ML enhancement'
    };
  }
}

module.exports = CSDEMLIntegration;
