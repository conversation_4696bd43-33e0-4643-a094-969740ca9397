<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse Universal API Connector (UAC) Demo</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  <style>
    body {
      padding-top: 2rem;
      padding-bottom: 2rem;
      background-color: #0d47a1; /* Deep blue background */
      color: #f8f9fa;
    }
    .header {
      padding: 2rem 1rem;
      margin-bottom: 2rem;
      background-color: #e9ecef;
      border-radius: 0.3rem;
      color: #212529;
    }
    .card {
      margin-bottom: 1.5rem;
      background-color: #ffffff;
      border: 1px solid rgba(0,0,0,.125);
      color: #212529;
    }

    .card-header {
      background-color: rgba(0,0,0,.03);
      border-bottom: 1px solid rgba(0,0,0,.125);
    }
    .code-block {
      background-color: #f8f9fa;
      padding: 1rem;
      border-radius: 0.3rem;
      font-family: monospace;
      white-space: pre-wrap;
      color: #212529;
      border: 1px solid #dee2e6;
    }
    .result-block {
      background-color: #f8f9fa;
      padding: 1rem;
      border-radius: 0.3rem;
      font-family: monospace;
      white-space: pre-wrap;
      max-height: 300px;
      overflow-y: auto;
      color: #212529;
      border: 1px solid #dee2e6;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header text-center">
      <h1>NovaFuse Universal API Connector (UAC)</h1>
      <p class="lead">A powerful tool for connecting to various APIs and applying compliance rules in real-time.</p>
      <p>
        <a href="/api-docs" class="btn btn-primary">API Documentation</a>
        <a href="https://github.com/novafuse/uac-demo" class="btn btn-secondary">GitHub Repository</a>
      </p>
      <div class="alert alert-info mt-3" id="getting-started-prompt">
        <i class="bi bi-info-circle-fill me-2"></i>
        🔐 Please log in using a demo user (admin, partner, user, guest) to begin exploring NovaFuse UAC.
      </div>
      <div class="alert alert-success mt-3 d-none" id="demo-checklist">
        <h5 class="mb-3">Demo Setup Checklist</h5>
        <ul class="list-group">
          <li class="list-group-item bg-transparent border-0 d-flex align-items-center" id="checklist-login">
            <span class="badge bg-success me-2">✅</span> Logged in as: <span id="logged-user" class="fw-bold ms-1">Not logged in</span>
          </li>
          <li class="list-group-item bg-transparent border-0 d-flex align-items-center" id="checklist-init">
            <span class="badge bg-secondary me-2">➡️</span> Step 1: Initialize Demo Data
          </li>
          <li class="list-group-item bg-transparent border-0 d-flex align-items-center" id="checklist-load">
            <span class="badge bg-secondary me-2">➡️</span> Step 2: Load a Sample API or Dataset
          </li>
          <li class="list-group-item bg-transparent border-0 d-flex align-items-center" id="checklist-run">
            <span class="badge bg-secondary me-2">➡️</span> Step 3: Run a Compliance Check
          </li>
          <li class="list-group-item bg-transparent border-0 d-flex align-items-center" id="checklist-view">
            <span class="badge bg-secondary me-2">➡️</span> Step 4: View Compliance Results
          </li>
        </ul>
        <button id="run-full-demo" class="btn btn-success mt-3">
          <i class="bi bi-play-circle-fill me-2"></i> 🔄 Run Full Demo
        </button>
      </div>
    </div>

    <div class="row">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Authentication</h5>
          </div>
          <div class="card-body">
            <form id="login-form">
              <div class="mb-3">
                <label for="username" class="form-label">Username</label>
                <input type="text" class="form-control" id="username" value="admin">
                <div class="form-text">Demo users: admin, partner, user, guest</div>
              </div>
              <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <input type="password" class="form-control" id="password" value="admin123">
                <div class="form-text">Demo passwords: admin123, partner123, user123, guest123</div>
              </div>
              <button type="submit" class="btn btn-primary">Login</button>
            </form>
            <div class="mt-3">
              <div class="alert alert-success d-none" id="login-success">
                Login successful! Token has been stored.
              </div>
              <div class="alert alert-danger d-none" id="login-error">
                Login failed. Please check your credentials.
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <h5>Initialize Demo Data</h5>
          </div>
          <div class="card-body">
            <p>Initialize demo APIs and compliance rules.</p>
            <button id="init-demo" class="btn btn-primary">Initialize Demo</button>
            <div class="mt-3">
              <div class="alert alert-success d-none" id="init-success">
                Demo data initialized successfully!
              </div>
              <div class="alert alert-danger d-none" id="init-error">
                Failed to initialize demo data.
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Current User</h5>
          </div>
          <div class="card-body">
            <button id="get-user" class="btn btn-primary">Get Current User</button>
            <div class="mt-3 result-block" id="user-result">
              👤 Not logged in. Please authenticate using the login form.
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <h5>API List</h5>
          </div>
          <div class="card-body">
            <button id="list-apis" class="btn btn-primary">List APIs</button>
            <div class="mt-3 result-block" id="apis-result">
              📫 No APIs yet. Click 'Initialize Demo Data' or use your own API endpoint.
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-4">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h5>Compliance Checks</h5>
          </div>
          <div class="card-body">
            <ul class="nav nav-tabs" id="complianceTabs" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="nav-link active" id="hipaa-tab" data-bs-toggle="tab" data-bs-target="#hipaa" type="button" role="tab" aria-controls="hipaa" aria-selected="true">HIPAA</button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="gdpr-tab" data-bs-toggle="tab" data-bs-target="#gdpr" type="button" role="tab" aria-controls="gdpr" aria-selected="false">GDPR</button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="pci-tab" data-bs-toggle="tab" data-bs-target="#pci" type="button" role="tab" aria-controls="pci" aria-selected="false">PCI DSS</button>
              </li>
            </ul>
            <div class="tab-content" id="complianceTabsContent">
              <div class="tab-pane fade show active" id="hipaa" role="tabpanel" aria-labelledby="hipaa-tab">
                <div class="mt-3">
                  <h6>Patient Data</h6>
                  <div class="mb-2">
                    <button class="btn btn-sm btn-outline-primary me-2" id="hipaa-compliant">Load Compliant Sample</button>
                    <button class="btn btn-sm btn-outline-danger me-2" id="hipaa-non-compliant">Load Non-Compliant Sample</button>
                    <button class="btn btn-sm btn-outline-warning" id="hipaa-partially-compliant">Load Partially Compliant Sample</button>
                  </div>
                  <div class="code-block" contenteditable="true" id="hipaa-data">
{
  "patientId": "12345",
  "name": "John Doe",
  "dob": "1980-01-01",
  "medicalRecordNumber": "MRN-67890",
  "diagnosis": "Hypertension",
  "medications": [
    "Lisinopril 10mg",
    "Aspirin 81mg"
  ]
}</div>
                  <button id="check-hipaa" class="btn btn-primary mt-2">Check HIPAA Compliance</button>
                  <div class="mt-3 result-block" id="hipaa-result">
                    🧪 No compliance results yet. Select a framework and run a check.
                  </div>
                  <div class="mt-3 d-none" id="hipaa-feedback-panel">
                    <div class="card bg-light">
                      <div class="card-header d-flex justify-content-between align-items-center">
                        <span>🛡 HIPAA Findings <span id="hipaa-status-badge" class="badge bg-secondary ms-2">Pending</span></span>
                        <span id="hipaa-issues-count" class="badge bg-secondary">0 issues found</span>
                      </div>
                      <div class="card-body">
                        <ul class="list-group list-group-flush" id="hipaa-issues-list">
                          <!-- Issues will be added here dynamically -->
                        </ul>
                        <div class="mt-3">
                          <strong>💡 Recommendation:</strong>
                          <p id="hipaa-recommendation" class="mb-0">No recommendations yet.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="tab-pane fade" id="gdpr" role="tabpanel" aria-labelledby="gdpr-tab">
                <div class="mt-3">
                  <h6>User Data</h6>
                  <div class="mb-2">
                    <button class="btn btn-sm btn-outline-primary me-2" id="gdpr-compliant">Load Compliant Sample</button>
                    <button class="btn btn-sm btn-outline-danger me-2" id="gdpr-non-compliant">Load Non-Compliant Sample</button>
                    <button class="btn btn-sm btn-outline-warning" id="gdpr-partially-compliant">Load Partially Compliant Sample</button>
                  </div>
                  <div class="code-block" contenteditable="true" id="gdpr-data">
{
  "userId": "user123",
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "address": "123 Main St, London, UK",
  "phoneNumber": "+44 1234 567890",
  "preferences": {
    "marketing": true,
    "notifications": false
  }
}</div>
                  <button id="check-gdpr" class="btn btn-primary mt-2">Check GDPR Compliance</button>
                  <div class="mt-3 result-block" id="gdpr-result">
                    🧪 No compliance results yet. Select a framework and run a check.
                  </div>
                  <div class="mt-3 d-none" id="gdpr-feedback-panel">
                    <div class="card bg-light">
                      <div class="card-header d-flex justify-content-between align-items-center">
                        <span>🛡 GDPR Findings <span id="gdpr-status-badge" class="badge bg-secondary ms-2">Pending</span></span>
                        <span id="gdpr-issues-count" class="badge bg-secondary">0 issues found</span>
                      </div>
                      <div class="card-body">
                        <ul class="list-group list-group-flush" id="gdpr-issues-list">
                          <!-- Issues will be added here dynamically -->
                        </ul>
                        <div class="mt-3">
                          <strong>💡 Recommendation:</strong>
                          <p id="gdpr-recommendation" class="mb-0">No recommendations yet.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="tab-pane fade" id="pci" role="tabpanel" aria-labelledby="pci-tab">
                <div class="mt-3">
                  <h6>Payment Data</h6>
                  <div class="mb-2">
                    <button class="btn btn-sm btn-outline-primary me-2" id="pci-compliant">Load Compliant Sample</button>
                    <button class="btn btn-sm btn-outline-danger me-2" id="pci-non-compliant">Load Non-Compliant Sample</button>
                    <button class="btn btn-sm btn-outline-warning" id="pci-partially-compliant">Load Partially Compliant Sample</button>
                  </div>
                  <div class="code-block" contenteditable="true" id="pci-data">
{
  "customerId": "cust123",
  "amount": 99.99,
  "currency": "USD",
  "cardNumber": "4111 1111 1111 1111",
  "expirationDate": "12/25",
  "cvv": "123",
  "billingAddress": {
    "street": "456 Oak Ave",
    "city": "New York",
    "state": "NY",
    "zip": "10001",
    "country": "USA"
  }
}</div>
                  <button id="check-pci" class="btn btn-primary mt-2">Check PCI DSS Compliance</button>
                  <div class="mt-3 result-block" id="pci-result">
                    🧪 No compliance results yet. Select a framework and run a check.
                  </div>
                  <div class="mt-3 d-none" id="pci-feedback-panel">
                    <div class="card bg-light">
                      <div class="card-header d-flex justify-content-between align-items-center">
                        <span>🛡 PCI DSS Findings <span id="pci-status-badge" class="badge bg-secondary ms-2">Pending</span></span>
                        <span id="pci-issues-count" class="badge bg-secondary">0 issues found</span>
                      </div>
                      <div class="card-body">
                        <ul class="list-group list-group-flush" id="pci-issues-list">
                          <!-- Issues will be added here dynamically -->
                        </ul>
                        <div class="mt-3">
                          <strong>💡 Recommendation:</strong>
                          <p id="pci-recommendation" class="mb-0">No recommendations yet.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-4">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h5>Multi-API Demo</h5>
          </div>
          <div class="card-body">
            <p>This demo simulates calling multiple APIs and applying compliance checks to the results.</p>
            <div class="mb-3">
              <button class="btn btn-sm btn-outline-primary me-2" id="multi-api-compliant">Load All Compliant</button>
              <button class="btn btn-sm btn-outline-danger me-2" id="multi-api-non-compliant">Load All Non-Compliant</button>
              <button class="btn btn-sm btn-outline-warning" id="multi-api-mixed">Load Mixed Compliance</button>
            </div>
            <button id="multi-api" class="btn btn-primary">Run Multi-API Demo</button>
            <div class="mt-3 result-block" id="multi-api-result">
              🧪 No multi-API results yet. Click 'Run Multi-API Demo' to see compliance across multiple APIs.
            </div>
            <div class="mt-3 d-none" id="multi-api-summary">
              <div class="card bg-light">
                <div class="card-header">
                  <h6 class="mb-0">📊 Multi-API Compliance Summary</h6>
                </div>
                <div class="card-body">
                  <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                      🌤 Weather API – GDPR <span id="weather-status" class="badge bg-secondary">Pending</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                      🐙 GitHub API – GDPR <span id="github-status" class="badge bg-secondary">Pending</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                      🏥 Health API – HIPAA <span id="health-status" class="badge bg-secondary">Pending</span>
                    </li>
                  </ul>
                  <button id="run-all-frameworks" class="btn btn-primary btn-sm mt-3">
                    <i class="bi bi-play-fill"></i> ➡️ Run All Framework Scans
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <footer class="mt-5 text-center text-white">
      <p>&copy; 2023 NovaFuse. All rights reserved.</p>
    </footer>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

  <script>
    // Store JWT token
    let token = '';

    // Helper function to update compliance status badge
    function updateComplianceStatus(elementId, status) {
      const badge = document.getElementById(elementId);

      badge.classList.remove('bg-secondary', 'bg-success', 'bg-warning', 'bg-danger');

      switch (status) {
        case 'compliant':
          badge.classList.add('bg-success');
          badge.textContent = '🟢 Compliant';
          break;
        case 'warning':
          badge.classList.add('bg-warning');
          badge.textContent = '🟡 Warnings';
          break;
        case 'non-compliant':
          badge.classList.add('bg-danger');
          badge.textContent = '🔴 Non-Compliant';
          break;
        default:
          badge.classList.add('bg-secondary');
          badge.textContent = 'Pending';
      }
    }

    // Helper function to process compliance results
    function processComplianceResults(framework, data) {
      // Show the feedback panel
      document.getElementById(`${framework}-feedback-panel`).classList.remove('d-none');

      // Get the compliance results
      const results = data.compliance;

      // Count issues
      let issuesCount = 0;
      let recommendations = [];

      // Clear previous issues
      const issuesList = document.getElementById(`${framework}-issues-list`);
      issuesList.innerHTML = '';

      // Process each rule result
      if (results && results.results) {
        results.results.forEach(result => {
          if (!result.compliant) {
            issuesCount++;

            // Add to issues list
            const li = document.createElement('li');
            li.className = 'list-group-item';
            li.innerHTML = `[🔴] ${result.rule}: ${result.details.message}`;

            if (result.details.recommendation) {
              recommendations.push(result.details.recommendation);
            }

            issuesList.appendChild(li);
          }
        });
      }

      // Update issues count
      const issuesCountElement = document.getElementById(`${framework}-issues-count`);
      issuesCountElement.textContent = `${issuesCount} ${issuesCount === 1 ? 'issue' : 'issues'} found`;

      // Update status badge
      if (issuesCount === 0) {
        updateComplianceStatus(`${framework}-status-badge`, 'compliant');
      } else if (issuesCount < 3) {
        updateComplianceStatus(`${framework}-status-badge`, 'warning');
      } else {
        updateComplianceStatus(`${framework}-status-badge`, 'non-compliant');
      }

      // Update recommendation
      const recommendationElement = document.getElementById(`${framework}-recommendation`);
      if (recommendations.length > 0) {
        recommendationElement.textContent = recommendations.join(' ');
      } else {
        recommendationElement.textContent = 'No specific recommendations.';
      }
    }

    // Helper function to load sample data
    async function loadSampleData(framework, type, elementId) {
      if (!token) {
        alert('Please login first');
        return;
      }

      try {
        const response = await fetch(`/api/demo/samples/${framework}/${type}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const data = await response.json();

        if (response.ok) {
          document.getElementById(elementId).textContent = JSON.stringify(data, null, 2);

          // Update checklist
          updateChecklist('load', true);
        } else {
          alert(`Error: ${data.error}`);
        }
      } catch (error) {
        console.error(`Load ${framework} sample error:`, error);
        alert(`Network error: ${error.message}`);
      }
    }

    // Update checklist status
    function updateChecklist(step, completed) {
      const element = document.getElementById(`checklist-${step}`);
      const badge = element.querySelector('.badge');

      if (completed) {
        badge.classList.remove('bg-secondary');
        badge.classList.add('bg-success');
        badge.textContent = '✅';
      } else {
        badge.classList.remove('bg-success');
        badge.classList.add('bg-secondary');
        badge.textContent = '➡️';
      }
    }

    // Login form submission
    document.getElementById('login-form').addEventListener('submit', async (e) => {
      e.preventDefault();

      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;

      try {
        const response = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (response.ok) {
          token = data.token;
          document.getElementById('login-success').classList.remove('d-none');
          document.getElementById('login-error').classList.add('d-none');

          // Show checklist and hide getting started prompt
          document.getElementById('getting-started-prompt').classList.add('d-none');
          document.getElementById('demo-checklist').classList.remove('d-none');

          // Update checklist
          document.getElementById('logged-user').textContent = username;
          updateChecklist('login', true);

          // Get user info
          getCurrentUser();
        } else {
          document.getElementById('login-success').classList.add('d-none');
          document.getElementById('login-error').classList.remove('d-none');
          document.getElementById('login-error').textContent = data.error || 'Login failed';
        }
      } catch (error) {
        console.error('Login error:', error);
        document.getElementById('login-success').classList.add('d-none');
        document.getElementById('login-error').classList.remove('d-none');
        document.getElementById('login-error').textContent = 'Network error';
      }
    });

    // Get current user
    document.getElementById('get-user').addEventListener('click', getCurrentUser);

    async function getCurrentUser() {
      if (!token) {
        document.getElementById('user-result').textContent = 'Not logged in';
        return;
      }

      try {
        const response = await fetch('/api/auth/me', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const data = await response.json();

        if (response.ok) {
          document.getElementById('user-result').textContent = JSON.stringify(data, null, 2);
        } else {
          document.getElementById('user-result').textContent = `Error: ${data.error}`;
        }
      } catch (error) {
        console.error('Get user error:', error);
        document.getElementById('user-result').textContent = 'Network error';
      }
    }

    // Initialize demo data
    document.getElementById('init-demo').addEventListener('click', async () => {
      if (!token) {
        document.getElementById('init-error').classList.remove('d-none');
        document.getElementById('init-success').classList.add('d-none');
        document.getElementById('init-error').textContent = 'Please login first';
        return;
      }

      try {
        const response = await fetch('/api/demo/init', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        const data = await response.json();

        if (response.ok) {
          document.getElementById('init-success').classList.remove('d-none');
          document.getElementById('init-error').classList.add('d-none');

          // Update checklist
          updateChecklist('init', true);

          // Refresh API list
          listApis();
        } else {
          document.getElementById('init-success').classList.add('d-none');
          document.getElementById('init-error').classList.remove('d-none');
          document.getElementById('init-error').textContent = data.error || 'Initialization failed';
        }
      } catch (error) {
        console.error('Init demo error:', error);
        document.getElementById('init-success').classList.add('d-none');
        document.getElementById('init-error').classList.remove('d-none');
        document.getElementById('init-error').textContent = 'Network error';
      }
    });

    // List APIs
    document.getElementById('list-apis').addEventListener('click', listApis);

    async function listApis() {
      if (!token) {
        document.getElementById('apis-result').textContent = 'Please login first';
        return;
      }

      try {
        const response = await fetch('/api/connector/apis', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const data = await response.json();

        if (response.ok) {
          document.getElementById('apis-result').textContent = JSON.stringify(data, null, 2);
        } else {
          document.getElementById('apis-result').textContent = `Error: ${data.error}`;
        }
      } catch (error) {
        console.error('List APIs error:', error);
        document.getElementById('apis-result').textContent = 'Network error';
      }
    }

    // HIPAA compliance check
    document.getElementById('check-hipaa').addEventListener('click', async () => {
      if (!token) {
        document.getElementById('hipaa-result').textContent = 'Please login first';
        return;
      }

      try {
        const patientData = JSON.parse(document.getElementById('hipaa-data').textContent);

        const response = await fetch('/api/demo/hipaa-check', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ patientData })
        });

        const data = await response.json();

        if (response.ok) {
          document.getElementById('hipaa-result').textContent = JSON.stringify(data, null, 2);

          // Process compliance results
          processComplianceResults('hipaa', data);

          // Update checklist
          updateChecklist('run', true);
          updateChecklist('view', true);
        } else {
          document.getElementById('hipaa-result').textContent = `Error: ${data.error}`;
        }
      } catch (error) {
        console.error('HIPAA check error:', error);
        document.getElementById('hipaa-result').textContent = 'Error: ' + error.message;
      }
    });

    // GDPR compliance check
    document.getElementById('check-gdpr').addEventListener('click', async () => {
      if (!token) {
        document.getElementById('gdpr-result').textContent = 'Please login first';
        return;
      }

      try {
        const userData = JSON.parse(document.getElementById('gdpr-data').textContent);

        const response = await fetch('/api/demo/gdpr-check', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ userData })
        });

        const data = await response.json();

        if (response.ok) {
          document.getElementById('gdpr-result').textContent = JSON.stringify(data, null, 2);

          // Process compliance results
          processComplianceResults('gdpr', data);
        } else {
          document.getElementById('gdpr-result').textContent = `Error: ${data.error}`;
        }
      } catch (error) {
        console.error('GDPR check error:', error);
        document.getElementById('gdpr-result').textContent = 'Error: ' + error.message;
      }
    });

    // PCI DSS compliance check
    document.getElementById('check-pci').addEventListener('click', async () => {
      if (!token) {
        document.getElementById('pci-result').textContent = 'Please login first';
        return;
      }

      try {
        const paymentData = JSON.parse(document.getElementById('pci-data').textContent);

        const response = await fetch('/api/demo/pci-check', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ paymentData })
        });

        const data = await response.json();

        if (response.ok) {
          document.getElementById('pci-result').textContent = JSON.stringify(data, null, 2);

          // Process compliance results
          processComplianceResults('pci', data);
        } else {
          document.getElementById('pci-result').textContent = `Error: ${data.error}`;
        }
      } catch (error) {
        console.error('PCI check error:', error);
        document.getElementById('pci-result').textContent = 'Error: ' + error.message;
      }
    });

    // HIPAA sample buttons
    document.getElementById('hipaa-compliant').addEventListener('click', () => {
      loadSampleData('hipaa', 'compliant', 'hipaa-data');
    });

    document.getElementById('hipaa-non-compliant').addEventListener('click', () => {
      loadSampleData('hipaa', 'nonCompliant', 'hipaa-data');
    });

    document.getElementById('hipaa-partially-compliant').addEventListener('click', () => {
      loadSampleData('hipaa', 'partiallyCompliant', 'hipaa-data');
    });

    // GDPR sample buttons
    document.getElementById('gdpr-compliant').addEventListener('click', () => {
      loadSampleData('gdpr', 'compliant', 'gdpr-data');
    });

    document.getElementById('gdpr-non-compliant').addEventListener('click', () => {
      loadSampleData('gdpr', 'nonCompliant', 'gdpr-data');
    });

    document.getElementById('gdpr-partially-compliant').addEventListener('click', () => {
      loadSampleData('gdpr', 'partiallyCompliant', 'gdpr-data');
    });

    // PCI DSS sample buttons
    document.getElementById('pci-compliant').addEventListener('click', () => {
      loadSampleData('pci', 'compliant', 'pci-data');
    });

    document.getElementById('pci-non-compliant').addEventListener('click', () => {
      loadSampleData('pci', 'nonCompliant', 'pci-data');
    });

    document.getElementById('pci-partially-compliant').addEventListener('click', () => {
      loadSampleData('pci', 'partiallyCompliant', 'pci-data');
    });

    // Multi-API sample buttons
    document.getElementById('multi-api-compliant').addEventListener('click', () => {
      loadSampleData('multi-api', 'allCompliant', 'multi-api-result');
    });

    document.getElementById('multi-api-non-compliant').addEventListener('click', () => {
      loadSampleData('multi-api', 'allNonCompliant', 'multi-api-result');
    });

    document.getElementById('multi-api-mixed').addEventListener('click', () => {
      loadSampleData('multi-api', 'mixedCompliance', 'multi-api-result');
    });

    // Run Full Demo button
    document.getElementById('run-full-demo').addEventListener('click', async () => {
      if (!token) {
        alert('Please login first');
        return;
      }

      try {
        // Step 1: Initialize demo data
        const initResponse = await fetch('/api/demo/init', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!initResponse.ok) {
          const initData = await initResponse.json();
          alert(`Error initializing demo data: ${initData.error || 'Initialization failed'}`);
          return;
        }

        // Update checklist
        updateChecklist('init', true);

        // Refresh API list
        await listApis();

        // Step 2: Load sample data
        // Load HIPAA non-compliant sample
        const hipaaResponse = await fetch('/api/demo/samples/hipaa/nonCompliant', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (hipaaResponse.ok) {
          const hipaaData = await hipaaResponse.json();
          document.getElementById('hipaa-data').textContent = JSON.stringify(hipaaData, null, 2);
        }

        // Update checklist
        updateChecklist('load', true);

        // Step 3: Run compliance checks
        // Run HIPAA check
        const patientData = JSON.parse(document.getElementById('hipaa-data').textContent);

        const hipaaCheckResponse = await fetch('/api/demo/hipaa-check', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ patientData })
        });

        if (hipaaCheckResponse.ok) {
          const hipaaCheckData = await hipaaCheckResponse.json();
          document.getElementById('hipaa-result').textContent = JSON.stringify(hipaaCheckData, null, 2);
        }

        // Update checklist
        updateChecklist('run', true);
        updateChecklist('view', true);

        // Run Multi-API demo
        const multiApiResponse = await fetch('/api/demo/multi-api', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (multiApiResponse.ok) {
          const multiApiData = await multiApiResponse.json();
          document.getElementById('multi-api-result').textContent = JSON.stringify(multiApiData, null, 2);
        }

        // Show success message
        alert('Full demo completed successfully! Explore the results in each section.');
      } catch (error) {
        console.error('Run full demo error:', error);
        alert(`Error running full demo: ${error.message}`);
      }
    });

    // Run All Framework Scans button
    document.getElementById('run-all-frameworks').addEventListener('click', async () => {
      if (!token) {
        alert('Please login first');
        return;
      }

      try {
        // Get data from the multi-api result
        const multiApiResultText = document.getElementById('multi-api-result').textContent;

        if (multiApiResultText.includes('No multi-API results')) {
          alert('Please run the Multi-API Demo first');
          return;
        }

        const multiApiData = JSON.parse(multiApiResultText);

        // Run GDPR check on Weather API data
        const weatherData = multiApiData.apiResults.weather.data;
        const weatherResponse = await fetch('/api/demo/gdpr-check', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ userData: weatherData })
        });

        if (weatherResponse.ok) {
          const weatherResult = await weatherResponse.json();
          if (weatherResult.compliance && weatherResult.compliance.compliant) {
            document.getElementById('weather-status').className = 'badge bg-success';
            document.getElementById('weather-status').textContent = '✅';
          } else {
            document.getElementById('weather-status').className = 'badge bg-danger';
            document.getElementById('weather-status').textContent = '❌';
          }
        }

        // Run GDPR check on GitHub API data
        const githubData = multiApiData.apiResults.github.data;
        const githubResponse = await fetch('/api/demo/gdpr-check', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ userData: githubData })
        });

        if (githubResponse.ok) {
          const githubResult = await githubResponse.json();
          if (githubResult.compliance && githubResult.compliance.compliant) {
            document.getElementById('github-status').className = 'badge bg-success';
            document.getElementById('github-status').textContent = '✅';
          } else {
            document.getElementById('github-status').className = 'badge bg-danger';
            document.getElementById('github-status').textContent = '❌';
          }
        }

        // Run HIPAA check on Health API data
        const healthData = multiApiData.apiResults.health.data;
        const healthResponse = await fetch('/api/demo/hipaa-check', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ patientData: healthData })
        });

        if (healthResponse.ok) {
          const healthResult = await healthResponse.json();
          if (healthResult.compliance && healthResult.compliance.compliant) {
            document.getElementById('health-status').className = 'badge bg-success';
            document.getElementById('health-status').textContent = '✅';
          } else {
            document.getElementById('health-status').className = 'badge bg-danger';
            document.getElementById('health-status').textContent = '❌';
          }
        }

        alert('All framework scans completed!');
      } catch (error) {
        console.error('Run all framework scans error:', error);
        alert(`Error running framework scans: ${error.message}`);
      }
    });

    // Multi-API demo
    document.getElementById('multi-api').addEventListener('click', async () => {
      if (!token) {
        document.getElementById('multi-api-result').textContent = 'Please login first';
        return;
      }

      try {
        const response = await fetch('/api/demo/multi-api', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        const data = await response.json();

        if (response.ok) {
          document.getElementById('multi-api-result').textContent = JSON.stringify(data, null, 2);

          // Show the summary panel
          document.getElementById('multi-api-summary').classList.remove('d-none');

          // Update API statuses
          const weatherResults = data.complianceResults.weather;
          const githubResults = data.complianceResults.github;
          const healthResults = data.complianceResults.health;

          // Weather API status
          if (weatherResults && weatherResults.compliant) {
            document.getElementById('weather-status').className = 'badge bg-success';
            document.getElementById('weather-status').textContent = '✅';
          } else if (weatherResults && !weatherResults.compliant) {
            document.getElementById('weather-status').className = 'badge bg-danger';
            document.getElementById('weather-status').textContent = '❌';
          } else {
            document.getElementById('weather-status').className = 'badge bg-warning';
            document.getElementById('weather-status').textContent = '⚠️';
          }

          // GitHub API status
          if (githubResults && githubResults.compliant) {
            document.getElementById('github-status').className = 'badge bg-success';
            document.getElementById('github-status').textContent = '✅';
          } else if (githubResults && !githubResults.compliant) {
            document.getElementById('github-status').className = 'badge bg-danger';
            document.getElementById('github-status').textContent = '❌';
          } else {
            document.getElementById('github-status').className = 'badge bg-warning';
            document.getElementById('github-status').textContent = '⚠️';
          }

          // Health API status
          if (healthResults && healthResults.compliant) {
            document.getElementById('health-status').className = 'badge bg-success';
            document.getElementById('health-status').textContent = '✅';
          } else if (healthResults && !healthResults.compliant) {
            document.getElementById('health-status').className = 'badge bg-danger';
            document.getElementById('health-status').textContent = '❌';
          } else {
            document.getElementById('health-status').className = 'badge bg-warning';
            document.getElementById('health-status').textContent = '⚠️';
          }
        } else {
          document.getElementById('multi-api-result').textContent = `Error: ${data.error}`;
        }
      } catch (error) {
        console.error('Multi-API demo error:', error);
        document.getElementById('multi-api-result').textContent = 'Error: ' + error.message;
      }
    });
  </script>
</body>
</html>
