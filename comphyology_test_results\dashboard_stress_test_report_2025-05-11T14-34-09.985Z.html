
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comphyology Dashboard Stress Test Report</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 20px;
    }

    h1, h2 {
      color: #333;
    }

    .chart-container {
      height: 400px;
      margin-bottom: 30px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }

    th, td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }

    th {
      background-color: #f2f2f2;
    }

    tr:hover {
      background-color: #f9f9f9;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Comphyology Dashboard Stress Test Report</h1>
    <p>Generated on 5/11/2025, 9:34:09 AM</p>

    <h2>Frame Rate vs. Message Rate</h2>
    <div class="chart-container">
      <canvas id="frameRateChart"></canvas>
    </div>

    <h2>Update Time vs. Message Rate</h2>
    <div class="chart-container">
      <canvas id="updateTimeChart"></canvas>
    </div>

    <h2>Efficiency vs. Message Rate</h2>
    <div class="chart-container">
      <canvas id="efficiencyChart"></canvas>
    </div>

    <h2>Detailed Results</h2>
    <table>
      <thead>
        <tr>
          <th>Message Rate (msg/s)</th>
          <th>Frame Rate (fps)</th>
          <th>Update Time (ms)</th>
          <th>Efficiency (%)</th>
          <th>Data Points</th>
          <th>Update Count</th>
        </tr>
      </thead>
      <tbody>
        
          <tr>
            <td>1</td>
            <td>41666.67</td>
            <td>0.03</td>
            <td>4166666.67</td>
            <td>6</td>
            <td>6</td>
          </tr>
        
          <tr>
            <td>5</td>
            <td>230138.34</td>
            <td>0.00</td>
            <td>4602766.80</td>
            <td>54</td>
            <td>60</td>
          </tr>
        
          <tr>
            <td>10</td>
            <td>140096.33</td>
            <td>0.00</td>
            <td>1400963.32</td>
            <td>108</td>
            <td>168</td>
          </tr>
        
          <tr>
            <td>20</td>
            <td>224807.65</td>
            <td>0.00</td>
            <td>1124038.27</td>
            <td>192</td>
            <td>360</td>
          </tr>
        
      </tbody>
    </table>
  </div>

  <script>
    // Frame Rate Chart
    const frameRateCtx = document.getElementById('frameRateChart').getContext('2d');
    new Chart(frameRateCtx, {
      type: 'line',
      data: {
        labels: [1,5,10,20],
        datasets: [{
          label: 'Frame Rate (fps)',
          data: [41666.66666686245,230138.33992178258,140096.33153180074,224807.65339537026],
          borderColor: '#4CAF50',
          backgroundColor: 'rgba(76, 175, 80, 0.1)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            title: {
              display: true,
              text: 'Message Rate (msg/s)'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Frame Rate (fps)'
            },
            beginAtZero: true
          }
        }
      }
    });

    // Update Time Chart
    const updateTimeCtx = document.getElementById('updateTimeChart').getContext('2d');
    new Chart(updateTimeCtx, {
      type: 'line',
      data: {
        labels: [1,5,10,20],
        datasets: [{
          label: 'Update Time (ms)',
          data: [0.03263333333325136,0.00369999999998914,0.004386999999969703,0.003731000000043423],
          borderColor: '#2196F3',
          backgroundColor: 'rgba(33, 150, 243, 0.1)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            title: {
              display: true,
              text: 'Message Rate (msg/s)'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Update Time (ms)'
            },
            beginAtZero: true
          }
        }
      }
    });

    // Efficiency Chart
    const efficiencyCtx = document.getElementById('efficiencyChart').getContext('2d');
    new Chart(efficiencyCtx, {
      type: 'line',
      data: {
        labels: [1,5,10,20],
        datasets: [{
          label: 'Efficiency (%)',
          data: [4166666.6666862452,4602766.798435652,1400963.3153180073,1124038.2669768513],
          borderColor: '#FF9800',
          backgroundColor: 'rgba(255, 152, 0, 0.1)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            title: {
              display: true,
              text: 'Message Rate (msg/s)'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Efficiency (%)'
            },
            beginAtZero: true
          }
        }
      }
    });
  </script>
</body>
</html>
  