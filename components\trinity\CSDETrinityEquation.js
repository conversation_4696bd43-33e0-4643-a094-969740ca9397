import React, { useState, useEffect, useRef } from 'react';

/**
 * CSDE Trinity Equation Visualization
 * 
 * This component visualizes the CSDE Trinity Equation:
 * CSDE_Trinity = πG + ϕD + (ℏ+c^-1)R
 * 
 * Where:
 * - G = Governance (Father)
 * - D = Detection (Son)
 * - R = Response (Spirit)
 * - π, ϕ, ℏ, c = Universal constants
 */
const CSDETrinityEquation = ({ width = '100%', height = '500px' }) => {
  // State for equation parameters
  const [governance, setGovernance] = useState(0.7);
  const [detection, setDetection] = useState(0.6);
  const [response, setResponse] = useState(0.5);
  const [showDetails, setShowDetails] = useState(false);
  
  // Constants
  const PI = Math.PI;
  const PHI = 0.618033988749895;
  const PLANCK = 6.62607015e-34;
  const SPEED_OF_LIGHT = 299792458;
  
  // Simplified constants for visualization
  const PLANCK_SIMPLIFIED = 0.662607015;
  const SPEED_OF_LIGHT_INVERSE = 1 / SPEED_OF_LIGHT;
  const SPEED_OF_LIGHT_INVERSE_SIMPLIFIED = 0.333564095;
  
  // Calculate CSDE Trinity value
  const calculateCSDETrinity = () => {
    const governanceTerm = PI * governance;
    const detectionTerm = PHI * detection;
    const responseTerm = (PLANCK_SIMPLIFIED + SPEED_OF_LIGHT_INVERSE_SIMPLIFIED) * response;
    
    return governanceTerm + detectionTerm + responseTerm;
  };
  
  // Calculate individual terms
  const governanceTerm = PI * governance;
  const detectionTerm = PHI * detection;
  const responseTerm = (PLANCK_SIMPLIFIED + SPEED_OF_LIGHT_INVERSE_SIMPLIFIED) * response;
  const csdeValue = calculateCSDETrinity();
  
  // Calculate normalized values for visualization (0-1 range)
  const maxPossibleValue = PI + PHI + (PLANCK_SIMPLIFIED + SPEED_OF_LIGHT_INVERSE_SIMPLIFIED);
  const normalizedValue = csdeValue / maxPossibleValue;
  
  // Canvas reference for visualization
  const canvasRef = useRef(null);
  
  // Colors
  const COLORS = {
    BACKGROUND: '#111133',
    GOVERNANCE: '#4CAF50', // Green (Father)
    DETECTION: '#2196F3',  // Blue (Son)
    RESPONSE: '#9C27B0',   // Purple (Spirit)
    TEXT: '#FFFFFF',
    HIGHLIGHT: '#FFC107'
  };
  
  // Initialize and animate the canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const container = canvas.parentElement;
    
    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;
    };
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    // Animation variables
    let animationFrame;
    let time = 0;
    
    // Animation function
    const animate = () => {
      time += 0.01;
      
      // Clear canvas
      ctx.fillStyle = COLORS.BACKGROUND;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Draw equation visualization
      drawEquationVisualization(ctx, canvas.width, canvas.height, time);
      
      // Continue animation
      animationFrame = requestAnimationFrame(animate);
    };
    
    animate();
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationFrame);
    };
  }, [governance, detection, response]);
  
  // Draw the equation visualization
  const drawEquationVisualization = (ctx, width, height, time) => {
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) * 0.35;
    
    // Draw Trinity circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // Draw filled portion based on CSDE value
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.arc(centerX, centerY, radius, -Math.PI / 2, -Math.PI / 2 + normalizedValue * Math.PI * 2);
    ctx.lineTo(centerX, centerY);
    
    // Create gradient fill
    const gradient = ctx.createConicGradient(-Math.PI / 2, centerX, centerY);
    gradient.addColorStop(0, COLORS.GOVERNANCE);
    gradient.addColorStop(governanceTerm / csdeValue, COLORS.GOVERNANCE);
    gradient.addColorStop(governanceTerm / csdeValue, COLORS.DETECTION);
    gradient.addColorStop((governanceTerm + detectionTerm) / csdeValue, COLORS.DETECTION);
    gradient.addColorStop((governanceTerm + detectionTerm) / csdeValue, COLORS.RESPONSE);
    gradient.addColorStop(1, COLORS.RESPONSE);
    
    ctx.fillStyle = gradient;
    ctx.fill();
    
    // Draw pulsing circle at the end of the arc
    const angle = -Math.PI / 2 + normalizedValue * Math.PI * 2;
    const pulseSize = 5 + Math.sin(time * 5) * 2;
    const x = centerX + Math.cos(angle) * radius;
    const y = centerY + Math.sin(angle) * radius;
    
    ctx.beginPath();
    ctx.arc(x, y, pulseSize, 0, Math.PI * 2);
    ctx.fillStyle = COLORS.HIGHLIGHT;
    ctx.fill();
    
    // Draw component lines
    // Governance line
    const governanceAngle = -Math.PI / 2 + (governanceTerm / csdeValue) * normalizedValue * Math.PI * 2;
    const governanceX = centerX + Math.cos(governanceAngle) * radius * 0.5;
    const governanceY = centerY + Math.sin(governanceAngle) * radius * 0.5;
    
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.lineTo(governanceX, governanceY);
    ctx.strokeStyle = COLORS.GOVERNANCE;
    ctx.lineWidth = 3;
    ctx.stroke();
    
    // Detection line
    const detectionAngle = -Math.PI / 2 + ((governanceTerm + detectionTerm) / csdeValue) * normalizedValue * Math.PI * 2;
    const detectionX = centerX + Math.cos(detectionAngle) * radius * 0.7;
    const detectionY = centerY + Math.sin(detectionAngle) * radius * 0.7;
    
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.lineTo(detectionX, detectionY);
    ctx.strokeStyle = COLORS.DETECTION;
    ctx.lineWidth = 3;
    ctx.stroke();
    
    // Response line
    const responseAngle = -Math.PI / 2 + normalizedValue * Math.PI * 2;
    const responseX = centerX + Math.cos(responseAngle) * radius * 0.9;
    const responseY = centerY + Math.sin(responseAngle) * radius * 0.9;
    
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.lineTo(responseX, responseY);
    ctx.strokeStyle = COLORS.RESPONSE;
    ctx.lineWidth = 3;
    ctx.stroke();
    
    // Draw labels
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // Governance label
    ctx.fillStyle = COLORS.GOVERNANCE;
    ctx.fillText('πG', centerX + Math.cos(-Math.PI / 4) * radius * 0.3, centerY + Math.sin(-Math.PI / 4) * radius * 0.3);
    
    // Detection label
    ctx.fillStyle = COLORS.DETECTION;
    ctx.fillText('ϕD', centerX + Math.cos(Math.PI / 4) * radius * 0.3, centerY + Math.sin(Math.PI / 4) * radius * 0.3);
    
    // Response label
    ctx.fillStyle = COLORS.RESPONSE;
    ctx.fillText('(ℏ+c⁻¹)R', centerX + Math.cos(Math.PI * 3/4) * radius * 0.3, centerY + Math.sin(Math.PI * 3/4) * radius * 0.3);
    
    // Draw CSDE value
    ctx.font = 'bold 24px Arial';
    ctx.fillStyle = COLORS.TEXT;
    ctx.fillText(`CSDE Trinity: ${csdeValue.toFixed(2)}`, centerX, centerY - radius - 30);
    
    // Draw component values
    ctx.font = '16px Arial';
    ctx.fillStyle = COLORS.GOVERNANCE;
    ctx.fillText(`πG: ${governanceTerm.toFixed(2)}`, centerX - radius, centerY - radius - 10);
    
    ctx.fillStyle = COLORS.DETECTION;
    ctx.fillText(`ϕD: ${detectionTerm.toFixed(2)}`, centerX, centerY - radius - 10);
    
    ctx.fillStyle = COLORS.RESPONSE;
    ctx.fillText(`(ℏ+c⁻¹)R: ${responseTerm.toFixed(2)}`, centerX + radius, centerY - radius - 10);
    
    // Draw particles flowing along the arc
    drawParticles(ctx, centerX, centerY, radius, time, normalizedValue);
  };
  
  // Draw particles flowing along the arc
  const drawParticles = (ctx, centerX, centerY, radius, time, normalizedValue) => {
    const particleCount = 20;
    
    for (let i = 0; i < particleCount; i++) {
      const particleTime = (time + i / particleCount) % 1;
      const particleAngle = -Math.PI / 2 + particleTime * normalizedValue * Math.PI * 2;
      
      // Determine particle color based on position
      let particleColor;
      const position = particleTime / normalizedValue;
      
      if (position < governanceTerm / csdeValue) {
        particleColor = COLORS.GOVERNANCE;
      } else if (position < (governanceTerm + detectionTerm) / csdeValue) {
        particleColor = COLORS.DETECTION;
      } else {
        particleColor = COLORS.RESPONSE;
      }
      
      // Draw particle
      const x = centerX + Math.cos(particleAngle) * radius;
      const y = centerY + Math.sin(particleAngle) * radius;
      const size = 3 + Math.sin(time * 10 + i) * 1;
      
      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      ctx.fillStyle = particleColor;
      ctx.fill();
      
      // Draw particle trail
      ctx.beginPath();
      ctx.moveTo(x, y);
      
      const trailLength = 10;
      for (let j = 1; j <= trailLength; j++) {
        const trailTime = (particleTime - j * 0.01) % 1;
        if (trailTime < 0) continue;
        
        const trailAngle = -Math.PI / 2 + trailTime * normalizedValue * Math.PI * 2;
        const trailX = centerX + Math.cos(trailAngle) * radius;
        const trailY = centerY + Math.sin(trailAngle) * radius;
        
        ctx.lineTo(trailX, trailY);
      }
      
      ctx.strokeStyle = particleColor;
      ctx.globalAlpha = 0.3;
      ctx.lineWidth = 2;
      ctx.stroke();
      ctx.globalAlpha = 1.0;
    }
  };
  
  return (
    <div className="csde-trinity-equation" style={{ width, height, position: 'relative' }}>
      <div className="csde-visualization" style={{ 
        width: '100%', 
        height: '70%', 
        backgroundColor: COLORS.BACKGROUND,
        borderRadius: '8px',
        overflow: 'hidden'
      }}>
        <canvas ref={canvasRef} style={{ width: '100%', height: '100%' }} />
      </div>
      
      <div className="csde-controls" style={{ 
        width: '100%', 
        height: '30%', 
        padding: '15px',
        backgroundColor: '#1a1a2e',
        borderRadius: '8px',
        marginTop: '10px'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
          <h3 style={{ margin: 0, color: 'white', fontSize: '16px' }}>CSDE Trinity Equation Controls</h3>
          <button 
            onClick={() => setShowDetails(!showDetails)}
            style={{
              background: 'none',
              border: '1px solid rgba(255,255,255,0.3)',
              color: 'white',
              padding: '5px 10px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            {showDetails ? 'Hide Details' : 'Show Details'}
          </button>
        </div>
        
        {showDetails && (
          <div style={{ color: 'white', fontSize: '14px', marginBottom: '10px', padding: '10px', backgroundColor: 'rgba(0,0,0,0.2)', borderRadius: '4px' }}>
            <p style={{ margin: '0 0 8px 0' }}>
              The CSDE Trinity Equation: <strong>CSDE_Trinity = πG + ϕD + (ℏ+c⁻¹)R</strong>
            </p>
            <ul style={{ margin: 0, paddingLeft: '20px' }}>
              <li><strong>G (Governance)</strong>: Weighted by π (3.14159...) - The Father</li>
              <li><strong>D (Detection)</strong>: Weighted by ϕ (0.61803...) - The Son</li>
              <li><strong>R (Response)</strong>: Weighted by (ℏ+c⁻¹) - The Spirit</li>
            </ul>
          </div>
        )}
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
          <div>
            <label style={{ display: 'flex', justifyContent: 'space-between', color: COLORS.GOVERNANCE, marginBottom: '5px' }}>
              <span>Governance (G):</span>
              <span>{governance.toFixed(2)}</span>
            </label>
            <input 
              type="range" 
              min="0" 
              max="1" 
              step="0.01" 
              value={governance} 
              onChange={(e) => setGovernance(parseFloat(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>
          
          <div>
            <label style={{ display: 'flex', justifyContent: 'space-between', color: COLORS.DETECTION, marginBottom: '5px' }}>
              <span>Detection (D):</span>
              <span>{detection.toFixed(2)}</span>
            </label>
            <input 
              type="range" 
              min="0" 
              max="1" 
              step="0.01" 
              value={detection} 
              onChange={(e) => setDetection(parseFloat(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>
          
          <div>
            <label style={{ display: 'flex', justifyContent: 'space-between', color: COLORS.RESPONSE, marginBottom: '5px' }}>
              <span>Response (R):</span>
              <span>{response.toFixed(2)}</span>
            </label>
            <input 
              type="range" 
              min="0" 
              max="1" 
              step="0.01" 
              value={response} 
              onChange={(e) => setResponse(parseFloat(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CSDETrinityEquation;
