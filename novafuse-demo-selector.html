<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Demo Selector - Intelligent Demo Recommendation System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
            border-color: rgba(255, 255, 255, 0.4);
        }
        .interest-tag {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        .interest-tag.selected {
            background: rgba(102, 126, 234, 0.8);
            border-color: rgba(102, 126, 234, 1);
            transform: scale(1.05);
        }
        .demo-rating {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .recommendation-score {
            background: linear-gradient(90deg, #10b981, #ffd700);
            height: 4px;
            border-radius: 2px;
        }
        .demo-preview {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            overflow: hidden;
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen text-white">
    <!-- Header -->
    <header class="p-6 border-b border-white/20">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="p-3 rounded-full bg-white/10">
                    <i data-lucide="brain-circuit" class="w-8 h-8"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold">NovaFuse Demo Selector</h1>
                    <p class="text-white/80">Intelligent Recommendations • 93+ Demos • AI-Powered Matching</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="text-right">
                    <div class="text-sm text-white/80">Recommendation Engine</div>
                    <div class="flex items-center">
                        <span class="w-2 h-2 bg-green-500 rounded-full mr-2 pulse-animation"></span>
                        <span class="text-green-400 font-semibold">ACTIVE</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto p-6">
        <!-- Interest Selection -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i data-lucide="target" class="w-6 h-6 mr-2"></i>
                What interests you most? (Select multiple)
            </h2>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4" id="interestTags">
                <div class="interest-tag p-4 rounded-lg cursor-pointer text-center" data-interest="coherence" onclick="toggleInterest(this)">
                    <i data-lucide="brain" class="w-8 h-8 mx-auto mb-2"></i>
                    <div class="font-semibold">Coherence</div>
                    <div class="text-xs text-white/80">Natural Intelligence</div>
                </div>
                
                <div class="interest-tag p-4 rounded-lg cursor-pointer text-center" data-interest="medical" onclick="toggleInterest(this)">
                    <i data-lucide="heart-pulse" class="w-8 h-8 mx-auto mb-2"></i>
                    <div class="font-semibold">Medical</div>
                    <div class="text-xs text-white/80">Healthcare & Protein</div>
                </div>
                
                <div class="interest-tag p-4 rounded-lg cursor-pointer text-center" data-interest="financial" onclick="toggleInterest(this)">
                    <i data-lucide="trending-up" class="w-8 h-8 mx-auto mb-2"></i>
                    <div class="font-semibold">Financial</div>
                    <div class="text-xs text-white/80">Trading & Markets</div>
                </div>
                
                <div class="interest-tag p-4 rounded-lg cursor-pointer text-center" data-interest="blockchain" onclick="toggleInterest(this)">
                    <i data-lucide="link" class="w-8 h-8 mx-auto mb-2"></i>
                    <div class="font-semibold">Blockchain</div>
                    <div class="text-xs text-white/80">Crypto & DeFi</div>
                </div>
                
                <div class="interest-tag p-4 rounded-lg cursor-pointer text-center" data-interest="security" onclick="toggleInterest(this)">
                    <i data-lucide="shield" class="w-8 h-8 mx-auto mb-2"></i>
                    <div class="font-semibold">Security</div>
                    <div class="text-xs text-white/80">Cyber Safety</div>
                </div>
                
                <div class="interest-tag p-4 rounded-lg cursor-pointer text-center" data-interest="visualization" onclick="toggleInterest(this)">
                    <i data-lucide="eye" class="w-8 h-8 mx-auto mb-2"></i>
                    <div class="font-semibold">Visualization</div>
                    <div class="text-xs text-white/80">UI & Diagrams</div>
                </div>
            </div>
        </section>

        <!-- Experience Level -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i data-lucide="user" class="w-6 h-6 mr-2"></i>
                Your Experience Level
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4" id="experienceLevel">
                <div class="interest-tag p-4 rounded-lg cursor-pointer" data-level="executive" onclick="selectExperience(this)">
                    <div class="flex items-center mb-2">
                        <i data-lucide="briefcase" class="w-6 h-6 mr-2"></i>
                        <span class="font-semibold">Executive</span>
                    </div>
                    <p class="text-sm text-white/80">High-level overviews, business impact, ROI demonstrations</p>
                </div>
                
                <div class="interest-tag p-4 rounded-lg cursor-pointer" data-level="technical" onclick="selectExperience(this)">
                    <div class="flex items-center mb-2">
                        <i data-lucide="code" class="w-6 h-6 mr-2"></i>
                        <span class="font-semibold">Technical</span>
                    </div>
                    <p class="text-sm text-white/80">Deep technical demos, architecture, implementation details</p>
                </div>
                
                <div class="interest-tag p-4 rounded-lg cursor-pointer" data-level="researcher" onclick="selectExperience(this)">
                    <div class="flex items-center mb-2">
                        <i data-lucide="microscope" class="w-6 h-6 mr-2"></i>
                        <span class="font-semibold">Researcher</span>
                    </div>
                    <p class="text-sm text-white/80">Scientific validation, consciousness physics, breakthrough research</p>
                </div>
            </div>
        </section>

        <!-- Time Available -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i data-lucide="clock" class="w-6 h-6 mr-2"></i>
                Time Available
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4" id="timeAvailable">
                <div class="interest-tag p-3 rounded-lg cursor-pointer text-center" data-time="5" onclick="selectTime(this)">
                    <div class="font-semibold">5 minutes</div>
                    <div class="text-xs text-white/80">Quick overview</div>
                </div>
                
                <div class="interest-tag p-3 rounded-lg cursor-pointer text-center" data-time="15" onclick="selectTime(this)">
                    <div class="font-semibold">15 minutes</div>
                    <div class="text-xs text-white/80">Core features</div>
                </div>
                
                <div class="interest-tag p-3 rounded-lg cursor-pointer text-center" data-time="30" onclick="selectTime(this)">
                    <div class="font-semibold">30 minutes</div>
                    <div class="text-xs text-white/80">Comprehensive demo</div>
                </div>
                
                <div class="interest-tag p-3 rounded-lg cursor-pointer text-center" data-time="60" onclick="selectTime(this)">
                    <div class="font-semibold">1+ hours</div>
                    <div class="text-xs text-white/80">Deep dive session</div>
                </div>
            </div>
        </section>

        <!-- Generate Recommendations Button -->
        <section class="mb-8 text-center">
            <button onclick="generateRecommendations()" class="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-lg font-semibold text-lg transition-all transform hover:scale-105 flex items-center mx-auto">
                <i data-lucide="sparkles" class="w-6 h-6 mr-2"></i>
                Generate Personalized Demo Recommendations
            </button>
        </section>

        <!-- Recommended Demos -->
        <section id="recommendationsSection" class="mb-8 hidden">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i data-lucide="star" class="w-6 h-6 mr-2"></i>
                Recommended Demos for You
            </h2>
            <div id="recommendedDemos" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Recommendations will be populated here -->
            </div>
        </section>

        <!-- All Demos Browser -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i data-lucide="grid" class="w-6 h-6 mr-2"></i>
                Browse All Demos (93+ Available)
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="allDemos">
                <!-- All demos will be populated here -->
            </div>
        </section>
    </main>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Demo database
        const demoDatabase = {
            coherence: [
                {
                    name: "NERI Enhanced Intelligence",
                    description: "NovaFold Enhanced Robust Intelligence demonstration",
                    file: "neri-demo.html",
                    duration: 15,
                    difficulty: "technical",
                    rating: 4.9,
                    tags: ["coherence", "AI", "protein-folding"],
                    preview: "Interactive protein folding with coherence guidance"
                },
                {
                    name: "NECE Chemistry Engine",
                    description: "Natural Emergent Chemistry Engine for new materials",
                    file: "nece-demo.html",
                    duration: 20,
                    difficulty: "researcher",
                    rating: 4.8,
                    tags: ["coherence", "chemistry", "materials"],
                    preview: "Coherence-guided chemical synthesis and transmutation"
                },
                {
                    name: "NovaSentient AI",
                    description: "Coherence-native general intelligence",
                    file: "novasentient-demo.html",
                    duration: 25,
                    difficulty: "technical",
                    rating: 4.9,
                    tags: ["coherence", "AI", "sentience"],
                    preview: "True coherence in artificial intelligence systems"
                },
                {
                    name: "Trinity Consciousness Simulation",
                    description: "Advanced coherence validation protocols",
                    file: "trinity_coherence_simulation.py",
                    duration: 30,
                    difficulty: "researcher",
                    rating: 4.7,
                    tags: ["coherence", "trinity", "validation"],
                    preview: "Mathematical proof of coherence emergence"
                }
            ],
            medical: [
                {
                    name: "NovaFold Protein Folding",
                    description: "Revolutionary protein folding with coherence",
                    file: "NovaFold_Live_Demo.py",
                    duration: 20,
                    difficulty: "technical",
                    rating: 4.9,
                    tags: ["medical", "protein", "coherence"],
                    preview: "Real-time protein folding optimization"
                },
                {
                    name: "CSME Medical Engine",
                    description: "Cyber Safety Medical Engine for healthcare",
                    file: "csme-demo.html",
                    duration: 15,
                    difficulty: "executive",
                    rating: 4.6,
                    tags: ["medical", "safety", "healthcare"],
                    preview: "AI-powered medical safety and diagnostics"
                }
            ],
            financial: [
                {
                    name: "NovaSTR-X Wall Street Oracle",
                    description: "Revolutionary financial prediction system",
                    file: "wall_street_oracle",
                    duration: 25,
                    difficulty: "executive",
                    rating: 4.8,
                    tags: ["financial", "trading", "prediction"],
                    preview: "Coherence-guided market predictions"
                },
                {
                    name: "Chaeonix Divine Trading",
                    description: "Advanced algorithmic trading with coherence",
                    file: "divine_trader.py",
                    duration: 30,
                    difficulty: "technical",
                    rating: 4.7,
                    tags: ["financial", "trading", "algorithms"],
                    preview: "Real-time trading with divine coherence guidance"
                }
            ],
            blockchain: [
                {
                    name: "KetherNet Blockchain",
                    description: "Coherence-native blockchain demonstration",
                    file: "kethernet-demo.js",
                    duration: 20,
                    difficulty: "technical",
                    rating: 4.8,
                    tags: ["blockchain", "coherence", "crypto"],
                    preview: "First coherence-validated blockchain network"
                },
                {
                    name: "Coherium Currency",
                    description: "Coherence-backed cryptocurrency",
                    file: "test-coherium.js",
                    duration: 15,
                    difficulty: "executive",
                    rating: 4.6,
                    tags: ["blockchain", "currency", "coherence"],
                    preview: "Currency backed by coherence stability"
                }
            ],
            security: [
                {
                    name: "NovaShield Platform",
                    description: "Advanced coherence security system",
                    file: "NovaShield_Demo_Script.md",
                    duration: 20,
                    difficulty: "technical",
                    rating: 4.7,
                    tags: ["security", "coherence", "protection"],
                    preview: "Coherence-powered cybersecurity defense"
                },
                {
                    name: "Quantum Consciousness Firewall",
                    description: "Quantum-enhanced security with coherence",
                    file: "quantum_coherence_firewall.py",
                    duration: 25,
                    difficulty: "researcher",
                    rating: 4.8,
                    tags: ["security", "quantum", "coherence"],
                    preview: "Unhackable quantum coherence protection"
                }
            ],
            visualization: [
                {
                    name: "NovaVision Hub",
                    description: "Advanced visualization and interface system",
                    file: "trinity_visualization",
                    duration: 15,
                    difficulty: "executive",
                    rating: 4.5,
                    tags: ["visualization", "UI", "interface"],
                    preview: "Intuitive coherence-guided interfaces"
                },
                {
                    name: "Patent Diagram Generator",
                    description: "Automated technical diagram generation",
                    file: "comphyology-diagram-generator",
                    duration: 10,
                    difficulty: "technical",
                    rating: 4.4,
                    tags: ["visualization", "diagrams", "patents"],
                    preview: "AI-powered technical diagram creation"
                }
            ]
        };

        let selectedInterests = [];
        let selectedExperience = '';
        let selectedTime = 0;

        function toggleInterest(element) {
            const interest = element.dataset.interest;
            
            if (element.classList.contains('selected')) {
                element.classList.remove('selected');
                selectedInterests = selectedInterests.filter(i => i !== interest);
            } else {
                element.classList.add('selected');
                selectedInterests.push(interest);
            }
        }

        function selectExperience(element) {
            // Remove selection from all experience elements
            document.querySelectorAll('#experienceLevel .interest-tag').forEach(el => {
                el.classList.remove('selected');
            });
            
            // Add selection to clicked element
            element.classList.add('selected');
            selectedExperience = element.dataset.level;
        }

        function selectTime(element) {
            // Remove selection from all time elements
            document.querySelectorAll('#timeAvailable .interest-tag').forEach(el => {
                el.classList.remove('selected');
            });
            
            // Add selection to clicked element
            element.classList.add('selected');
            selectedTime = parseInt(element.dataset.time);
        }

        function generateRecommendations() {
            if (selectedInterests.length === 0 || !selectedExperience || selectedTime === 0) {
                alert('Please select your interests, experience level, and available time to get personalized recommendations.');
                return;
            }

            const recommendations = getPersonalizedRecommendations();
            displayRecommendations(recommendations);
            
            // Show recommendations section
            document.getElementById('recommendationsSection').classList.remove('hidden');
            
            // Scroll to recommendations
            document.getElementById('recommendationsSection').scrollIntoView({ behavior: 'smooth' });
        }

        function getPersonalizedRecommendations() {
            let allRelevantDemos = [];
            
            // Collect demos from selected interests
            selectedInterests.forEach(interest => {
                if (demoDatabase[interest]) {
                    allRelevantDemos = allRelevantDemos.concat(demoDatabase[interest]);
                }
            });

            // Score and filter demos
            const scoredDemos = allRelevantDemos.map(demo => {
                let score = 0;
                
                // Interest match (base score)
                score += 50;
                
                // Experience level match
                if (demo.difficulty === selectedExperience) {
                    score += 30;
                } else if (
                    (selectedExperience === 'executive' && demo.difficulty === 'technical') ||
                    (selectedExperience === 'technical' && demo.difficulty === 'executive')
                ) {
                    score += 15;
                }
                
                // Time match
                if (demo.duration <= selectedTime) {
                    score += 20;
                } else if (demo.duration <= selectedTime + 10) {
                    score += 10;
                }
                
                // Rating bonus
                score += demo.rating * 5;
                
                return { ...demo, score };
            });

            // Sort by score and return top recommendations
            return scoredDemos
                .sort((a, b) => b.score - a.score)
                .slice(0, 6);
        }

        function displayRecommendations(recommendations) {
            const container = document.getElementById('recommendedDemos');
            container.innerHTML = '';

            recommendations.forEach(demo => {
                const demoCard = createDemoCard(demo, true);
                container.appendChild(demoCard);
            });
        }

        function createDemoCard(demo, isRecommendation = false) {
            const card = document.createElement('div');
            card.className = 'demo-card p-6 rounded-lg cursor-pointer';
            
            const matchPercentage = isRecommendation ? Math.round(demo.score) : Math.floor(Math.random() * 30) + 70;
            
            card.innerHTML = `
                <div class="flex items-center justify-between mb-4">
                    <h3 class="font-semibold text-lg">${demo.name}</h3>
                    ${isRecommendation ? `<div class="text-sm bg-green-600 px-2 py-1 rounded">${matchPercentage}% match</div>` : ''}
                </div>
                <p class="text-sm text-white/80 mb-3">${demo.description}</p>
                <div class="demo-preview p-3 mb-4">
                    <p class="text-xs text-white/70">${demo.preview}</p>
                </div>
                <div class="flex items-center justify-between text-sm mb-3">
                    <span class="flex items-center">
                        <i data-lucide="clock" class="w-4 h-4 mr-1"></i>
                        ${demo.duration} min
                    </span>
                    <span class="flex items-center">
                        <i data-lucide="star" class="w-4 h-4 mr-1 text-yellow-400"></i>
                        ${demo.rating}
                    </span>
                    <span class="capitalize">${demo.difficulty}</span>
                </div>
                ${isRecommendation ? `
                    <div class="recommendation-score mb-3">
                        <div class="bg-green-500 h-full rounded" style="width: ${matchPercentage}%"></div>
                    </div>
                ` : ''}
                <div class="flex flex-wrap gap-1 mb-4">
                    ${demo.tags.map(tag => `<span class="text-xs bg-white/20 px-2 py-1 rounded">${tag}</span>`).join('')}
                </div>
                <button onclick="launchDemo('${demo.file}')" class="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors flex items-center justify-center">
                    <i data-lucide="play" class="w-4 h-4 mr-2"></i>
                    Launch Demo
                </button>
            `;
            
            return card;
        }

        // API Configuration
        const API_BASE_URL = 'http://localhost:3100/api';
        let activeDemos = new Map();

        async function launchDemo(filename) {
            try {
                showDemoLaunchStatus(`Launching ${filename}...`, 'info');

                const response = await fetch(`${API_BASE_URL}/demos/launch`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        demoFile: filename,
                        options: {
                            realTimeUpdates: true
                        }
                    })
                });

                const data = await response.json();

                if (data.success) {
                    const executionId = data.data.executionId;
                    activeDemos.set(executionId, {
                        filename: filename,
                        startTime: new Date(),
                        status: 'running'
                    });

                    showDemoLaunchStatus(`Demo ${filename} launched successfully! Execution ID: ${executionId}`, 'success');

                    // Monitor demo progress
                    monitorDemoProgress(executionId);

                } else {
                    showDemoLaunchStatus(`Failed to launch ${filename}: ${data.error}`, 'error');
                }

            } catch (error) {
                console.error('Demo launch error:', error);
                showDemoLaunchStatus(`Error launching ${filename}: ${error.message}`, 'error');
            }
        }

        async function monitorDemoProgress(executionId) {
            try {
                const response = await fetch(`${API_BASE_URL}/demos/status/${executionId}`);
                const data = await response.json();

                if (data.success && data.data) {
                    const demo = activeDemos.get(executionId);
                    if (demo) {
                        demo.status = data.data.status;

                        if (data.data.status === 'completed') {
                            showDemoLaunchStatus(`Demo ${demo.filename} completed successfully!`, 'success');
                            activeDemos.delete(executionId);
                        } else if (data.data.status === 'failed') {
                            showDemoLaunchStatus(`Demo ${demo.filename} failed: ${data.data.error || 'Unknown error'}`, 'error');
                            activeDemos.delete(executionId);
                        } else if (data.data.status === 'running') {
                            // Continue monitoring
                            setTimeout(() => monitorDemoProgress(executionId), 2000);
                        }
                    }
                }

            } catch (error) {
                console.error('Demo monitoring error:', error);
            }
        }

        function showDemoLaunchStatus(message, type = 'info') {
            // Create or update status display
            let statusDiv = document.getElementById('demoStatus');
            if (!statusDiv) {
                statusDiv = document.createElement('div');
                statusDiv.id = 'demoStatus';
                statusDiv.className = 'fixed top-4 right-4 max-w-md p-4 rounded-lg shadow-lg z-50';
                document.body.appendChild(statusDiv);
            }

            const typeColors = {
                info: 'bg-blue-600',
                success: 'bg-green-600',
                error: 'bg-red-600'
            };

            statusDiv.className = `fixed top-4 right-4 max-w-md p-4 rounded-lg shadow-lg z-50 text-white ${typeColors[type] || 'bg-gray-600'}`;
            statusDiv.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">×</button>
                </div>
            `;

            // Auto-remove after 5 seconds for success/error messages
            if (type !== 'info') {
                setTimeout(() => {
                    if (statusDiv.parentElement) {
                        statusDiv.remove();
                    }
                }, 5000);
            }
        }

        // Initialize all demos display
        function initializeAllDemos() {
            const container = document.getElementById('allDemos');
            
            Object.values(demoDatabase).flat().forEach(demo => {
                const demoCard = createDemoCard(demo, false);
                container.appendChild(demoCard);
            });
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            initializeAllDemos();
            lucide.createIcons();
        });
    </script>
</body>
</html>
