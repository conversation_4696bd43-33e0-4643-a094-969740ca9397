/**
 * Regulatory API Configuration
 *
 * This file contains configuration for the regulatory API integration.
 */

const config = {
  // Base URL for the regulatory API
  baseUrl: process.env.REGULATORY_API_URL || 'https://api.novagrc.com/regulatory',
  
  // API version
  apiVersion: process.env.REGULATORY_API_VERSION || 'v1',
  
  // API key for authentication
  apiKey: process.env.REGULATORY_API_KEY,
  
  // Request timeout in milliseconds
  timeout: parseInt(process.env.REGULATORY_API_TIMEOUT || '5000', 10),
  
  // Cache TTL in seconds
  cacheTtl: {
    regulations: parseInt(process.env.REGULATORY_CACHE_TTL_REGULATIONS || '86400', 10), // 24 hours
    requirements: parseInt(process.env.REGULATORY_CACHE_TTL_REQUIREMENTS || '86400', 10), // 24 hours
    jurisdictions: parseInt(process.env.REGULATORY_CACHE_TTL_JURISDICTIONS || '86400', 10), // 24 hours
    updates: parseInt(process.env.REGULATORY_CACHE_TTL_UPDATES || '3600', 10) // 1 hour
  },
  
  // Retry configuration
  retry: {
    maxRetries: parseInt(process.env.REGULATORY_API_MAX_RETRIES || '3', 10),
    retryDelay: parseInt(process.env.REGULATORY_API_RETRY_DELAY || '1000', 10) // 1 second
  }
};

module.exports = config;
