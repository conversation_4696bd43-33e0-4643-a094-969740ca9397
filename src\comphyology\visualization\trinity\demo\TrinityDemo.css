.trinity-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: '<PERSON><PERSON>', 'Segoe UI', <PERSON>l, sans-serif;
}

.trinity-demo-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #1a1a2e;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: #2196F3;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.trinity-demo-error {
  max-width: 600px;
  margin: 100px auto;
  padding: 20px;
  background-color: #f44336;
  color: white;
  border-radius: 8px;
  text-align: center;
}

.trinity-demo-controls {
  margin-top: 30px;
  padding: 20px;
  background-color: #1a1a2e;
  border-radius: 8px;
  color: white;
}

.trinity-demo-controls h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 500;
  color: #2196F3;
}

.trinity-demo-controls button {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 10px 15px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.trinity-demo-controls button:hover {
  background-color: #1976D2;
}

.trinity-demo-info {
  margin-top: 30px;
  padding: 20px;
  background-color: #1a1a2e;
  border-radius: 8px;
  color: white;
}

.trinity-demo-info h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 500;
  color: #2196F3;
}

.trinity-demo-info p {
  margin-bottom: 15px;
  line-height: 1.5;
}

.trinity-demo-info ul {
  margin-bottom: 15px;
  padding-left: 20px;
}

.trinity-demo-info li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.trinity-demo-info strong {
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .trinity-demo {
    padding: 10px;
  }
  
  .trinity-demo-controls button {
    width: 100%;
    margin-right: 0;
  }
}
