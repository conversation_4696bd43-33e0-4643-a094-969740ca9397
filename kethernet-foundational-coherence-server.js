const express = require('express');
const cors = require('cors');
const app = express();

app.use(cors());
app.use(express.json());

// Divine=Foundational & Consciousness=Coherence validation middleware
app.use((req, res, next) => {
  // Support both old and new headers for backward compatibility
  const coherenceLevel = parseFloat(req.headers['x-coherence-level'] || req.headers['x-consciousness-level'] || '0');
  
  req.coherence = {
    level: coherenceLevel,
    foundational_score: coherenceLevel * 0.618, // φ-based foundational calculation
    kappa_units: Math.floor(coherenceLevel * 1000),
    divine_foundational_consensus: coherenceLevel >= 3.0, // Divine = Foundational threshold
    coherent_consensus: coherenceLevel >= 2.0,
    coherent_status: coherenceLevel >= 0.618 ? 'COHERENT' : 'INCOHERENT'
  };
  
  console.log('⚛️ Coherence Level:', coherenceLevel, 'Divine Foundational:', req.coherence.divine_foundational_consensus);
  console.log('🌟 Foundational Score:', req.coherence.foundational_score, 'Status:', req.coherence.coherent_status);
  
  next();
});

app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'KetherNet Foundational Coherent Blockchain',
    coherence_threshold: 0.618,
    divine_foundational_threshold: 3.0,
    coherium_enabled: true,
    divine_foundational_consensus: true,
    foundational_architecture: true,
    coherent_validation: true,
    
    // Backward compatibility
    consciousness_threshold: 0.618,
    crown_consensus: true,
    
    timestamp: new Date().toISOString()
  });
});

app.get('/consensus', (req, res) => {
  const { coherence } = req;
  res.json({
    consensus: coherence.divine_foundational_consensus ? 'divine_foundational_achieved' : 
               coherence.coherent_consensus ? 'coherent_achieved' : 'pending',
    consensus_type: coherence.divine_foundational_consensus ? 'DIVINE_FOUNDATIONAL' :
                   coherence.coherent_consensus ? 'HIGHLY_COHERENT' : 'BASIC_COHERENT',
    kappa_units: coherence.kappa_units,
    coherium_balance: coherence.divine_foundational_consensus ? 3089.78 : 
                     coherence.coherent_consensus ? 1089.78 : 0,
    coherence_level: coherence.level,
    foundational_score: coherence.foundational_score,
    coherent_status: coherence.coherent_status,
    
    // Backward compatibility
    consciousness_level: coherence.level,
    crown_consensus: coherence.divine_foundational_consensus,
    
    timestamp: new Date().toISOString()
  });
});

app.post('/validate', (req, res) => {
  const { coherence } = req;
  
  if (coherence.level < 0.618) {
    return res.status(403).json({
      error: 'COHERENCE_THRESHOLD_VIOLATION',
      message: 'Foundational coherence threshold not met',
      required_minimum_coherence: 0.618,
      provided_coherence: coherence.level,
      status: 'INCOHERENT_REJECTED',
      foundational_access: false,
      
      // Backward compatibility
      consciousness_level: coherence.level,
      required_minimum: 0.618
    });
  }
  
  res.json({
    validation: 'coherent_passed',
    validation_type: coherence.divine_foundational_consensus ? 'DIVINE_FOUNDATIONAL' :
                    coherence.coherent_consensus ? 'HIGHLY_COHERENT' : 'COHERENT',
    coherence_level: coherence.level,
    foundational_score: coherence.foundational_score,
    kappa_units: coherence.kappa_units,
    coherium_reward: coherence.divine_foundational_consensus ? 30.89 : 
                    coherence.coherent_consensus ? 10.89 : 1.0,
    coherent_status: coherence.coherent_status,
    foundational_access: true,
    
    // Backward compatibility
    consciousness_level: coherence.level,
    crown_consensus: coherence.divine_foundational_consensus,
    
    timestamp: new Date().toISOString()
  });
});

// New Divine Foundational endpoint
app.get('/divine-foundational', (req, res) => {
  const { coherence } = req;
  
  if (!coherence.divine_foundational_consensus) {
    return res.status(403).json({
      error: 'DIVINE_FOUNDATIONAL_ACCESS_REQUIRED',
      message: 'Divine foundational coherence level required (Ψ ≥ 3.0)',
      required_minimum: 3.0,
      provided_coherence: coherence.level
    });
  }
  
  res.json({
    divine_foundational_access: 'GRANTED',
    coherence_level: coherence.level,
    foundational_score: coherence.foundational_score,
    divine_foundational_status: 'ACTIVE',
    foundational_privileges: [
      'Maximum priority processing',
      'Divine consensus participation',
      'Foundational architecture access',
      'Enhanced coherium rewards'
    ],
    timestamp: new Date().toISOString()
  });
});

// Enhanced coherence metrics endpoint
app.get('/coherence-metrics', (req, res) => {
  res.json({
    coherence_framework: 'Divine=Foundational & Consciousness=Coherence',
    thresholds: {
      incoherent: '0.000 - 0.617',
      coherent: '0.618 - 1.999', 
      highly_coherent: '2.000 - 2.999',
      divine_foundational: '3.000+'
    },
    foundational_calculation: 'coherence_level × 0.618 (φ)',
    kappa_units_calculation: 'Math.floor(coherence_level × 1000)',
    coherium_rewards: {
      coherent: 1.0,
      highly_coherent: 10.89,
      divine_foundational: 30.89
    },
    backward_compatibility: 'consciousness headers supported',
    timestamp: new Date().toISOString()
  });
});

const PORT = process.env.PORT || 8080;
app.listen(PORT, '0.0.0.0', () => {
  console.log('🔗 KetherNet Foundational Coherent Blockchain running on port', PORT);
  console.log('💎 Coherium validation active');
  console.log('⚛️ Divine=Foundational & Consciousness=Coherence framework enabled');
  console.log('🌟 Divine Foundational Consensus enabled (Ψ ≥ 3.0)');
  console.log('🔄 Backward compatibility maintained');
});
