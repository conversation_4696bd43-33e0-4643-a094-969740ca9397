/**
 * Test script for authentication service
 */

const AuthService = require('./services/AuthService');
const ApiKeyService = require('./services/ApiKeyService');

// Create instances of the services
const authService = new AuthService();
const apiKeyService = new ApiKeyService();

// Test user data
const testUser = {
  username: 'testuser',
  password: 'password123',
  email: '<EMAIL>'
};

// Test authentication flow
async function testAuthFlow() {
  try {
    console.log('Testing authentication flow...');
    
    // Register user
    console.log('\n1. Registering user...');
    const user = await authService.register(testUser);
    console.log('User registered:', user);
    
    // Login
    console.log('\n2. Logging in...');
    const loginResult = await authService.login(testUser.username, testUser.password);
    console.log('Login successful:', loginResult.user);
    console.log('Token:', loginResult.token);
    
    // Verify token
    console.log('\n3. Verifying token...');
    const decoded = await authService.verifyToken(loginResult.token);
    console.log('Token verified:', decoded);
    
    // Create API key
    console.log('\n4. Creating API key...');
    const apiKey = await apiKeyService.createApiKey('Test API Key', user.id, ['read', 'write']);
    console.log('API key created:', apiKey);
    
    // Verify API key
    console.log('\n5. Verifying API key...');
    const verifiedApiKey = await apiKeyService.verifyApiKey(apiKey.key);
    console.log('API key verified:', verifiedApiKey);
    
    // Check permissions
    console.log('\n6. Checking permissions...');
    console.log('Has read permission:', apiKeyService.hasPermission(verifiedApiKey, 'read'));
    console.log('Has write permission:', apiKeyService.hasPermission(verifiedApiKey, 'write'));
    console.log('Has delete permission:', apiKeyService.hasPermission(verifiedApiKey, 'delete'));
    
    // Logout
    console.log('\n7. Logging out...');
    const logoutResult = await authService.logout(loginResult.token);
    console.log('Logout result:', logoutResult);
    
    // Try to verify token after logout
    console.log('\n8. Trying to verify token after logout...');
    try {
      await authService.verifyToken(loginResult.token);
      console.log('Token should not be valid!');
    } catch (error) {
      console.log('Token verification failed as expected:', error.message);
    }
    
    // Clean up
    console.log('\n9. Cleaning up...');
    await authService.deleteUser(user.id);
    console.log('User deleted');
    
    console.log('\nAuthentication flow test completed successfully!');
  } catch (error) {
    console.error('Error testing authentication flow:', error);
  }
}

// Run the test
testAuthFlow();
