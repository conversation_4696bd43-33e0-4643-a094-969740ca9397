/**
 * NovaFuse Universal API Connector - Validation Middleware
 * 
 * This module provides middleware for validating request data.
 */

const { createLogger } = require('../utils/logger');

const logger = createLogger('validator');

/**
 * Validation middleware factory
 * 
 * @param {Object} schema - Joi schema for validation
 * @param {string} property - Request property to validate (body, params, query)
 * @returns {Function} - The validation middleware
 */
function validate(schema, property = 'body') {
  return (req, res, next) => {
    try {
      if (!schema) {
        return next();
      }
      
      const data = req[property];
      
      // Simple validation for demonstration
      // In a real implementation, we would use a validation library like Joi
      const errors = [];
      
      // Check required fields
      if (schema.required) {
        for (const field of schema.required) {
          if (data[field] === undefined || data[field] === null || data[field] === '') {
            errors.push(`${field} is required`);
          }
        }
      }
      
      // Check field types
      if (schema.properties) {
        for (const [field, fieldSchema] of Object.entries(schema.properties)) {
          if (data[field] !== undefined && data[field] !== null) {
            // Check type
            if (fieldSchema.type === 'string' && typeof data[field] !== 'string') {
              errors.push(`${field} must be a string`);
            } else if (fieldSchema.type === 'number' && typeof data[field] !== 'number') {
              errors.push(`${field} must be a number`);
            } else if (fieldSchema.type === 'boolean' && typeof data[field] !== 'boolean') {
              errors.push(`${field} must be a boolean`);
            } else if (fieldSchema.type === 'object' && (typeof data[field] !== 'object' || Array.isArray(data[field]))) {
              errors.push(`${field} must be an object`);
            } else if (fieldSchema.type === 'array' && !Array.isArray(data[field])) {
              errors.push(`${field} must be an array`);
            }
            
            // Check enum
            if (fieldSchema.enum && !fieldSchema.enum.includes(data[field])) {
              errors.push(`${field} must be one of: ${fieldSchema.enum.join(', ')}`);
            }
          }
        }
      }
      
      if (errors.length > 0) {
        logger.warn('Validation error:', { errors });
        
        return res.status(400).json({
          error: {
            message: 'Validation error',
            code: 'VALIDATION_ERROR',
            details: errors
          }
        });
      }
      
      next();
    } catch (error) {
      logger.error('Validation error:', { error });
      
      return res.status(500).json({
        error: {
          message: 'Validation error',
          code: 'VALIDATION_ERROR'
        }
      });
    }
  };
}

module.exports = {
  validate
};
