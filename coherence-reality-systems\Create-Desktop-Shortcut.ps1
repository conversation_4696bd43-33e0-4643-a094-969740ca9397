# CHAEONIX Desktop Shortcut Creator
# Creates a desktop shortcut for instant access to the trading system

Write-Host "========================================================" -ForegroundColor Green
Write-Host "    CHAEONIX DESKTOP SHORTCUT CREATOR" -ForegroundColor Green
Write-Host "    15-Engine Comphyological Money Making Machine" -ForegroundColor Green
Write-Host "========================================================" -ForegroundColor Green
Write-Host ""

# Get the current directory (where the script is located)
$ScriptDirectory = Split-Path -Parent $MyInvocation.MyCommand.Path
$BatchFilePath = Join-Path $ScriptDirectory "CHAEONIX-START.bat"

# Get the desktop path
$DesktopPath = [Environment]::GetFolderPath("Desktop")
$ShortcutPath = Join-Path $DesktopPath "CHAEONIX Trading System.lnk"

# Check if batch file exists
if (-not (Test-Path $BatchFilePath)) {
    Write-Host "ERROR: CHAEONIX-START.bat not found!" -ForegroundColor Red
    Write-Host "Expected location: $BatchFilePath" -ForegroundColor Yellow
    pause
    exit 1
}

try {
    # Create WScript Shell object
    $WshShell = New-Object -ComObject WScript.Shell

    # Create the shortcut
    $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
    $Shortcut.TargetPath = $BatchFilePath
    $Shortcut.WorkingDirectory = $ScriptDirectory
    $Shortcut.Description = "CHAEONIX - 15-Engine Comphyological Money Making Machine (86% Health, $4,097/hr)"
    $Shortcut.IconLocation = "shell32.dll,137"  # Money/dollar icon
    $Shortcut.WindowStyle = 1  # Normal window
    $Shortcut.Save()

    Write-Host "SUCCESS: Desktop shortcut created!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Shortcut Details:" -ForegroundColor Cyan
    Write-Host "  Name: CHAEONIX Trading System" -ForegroundColor White
    Write-Host "  Location: $ShortcutPath" -ForegroundColor White
    Write-Host "  Target: $BatchFilePath" -ForegroundColor White
    Write-Host ""
    Write-Host "========================================================" -ForegroundColor Green
    Write-Host "    USAGE INSTRUCTIONS:" -ForegroundColor Green
    Write-Host "========================================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "1. Double-click the desktop shortcut" -ForegroundColor Yellow
    Write-Host "2. Dashboard will open automatically in your browser" -ForegroundColor Yellow
    Write-Host "3. Monitor your 15-engine trading system" -ForegroundColor Yellow
    Write-Host "4. Use 'Deploy Live Bot' for trading" -ForegroundColor Yellow
    Write-Host "5. Reset at 6PM for fresh performance tracking" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Dashboard URL: http://localhost:3141" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "CHAEONIX System Status: EXCELLENT (86% Health)" -ForegroundColor Green
    Write-Host "Profit Potential: $4,097/hr" -ForegroundColor Green
    Write-Host "All 15 Engines: OPERATIONAL" -ForegroundColor Green
    Write-Host ""

} catch {
    Write-Host "ERROR: Failed to create desktop shortcut!" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Manual Instructions:" -ForegroundColor Cyan
    Write-Host "1. Right-click on desktop" -ForegroundColor White
    Write-Host "2. Select 'New' > 'Shortcut'" -ForegroundColor White
    Write-Host "3. Browse to: $BatchFilePath" -ForegroundColor White
    Write-Host "4. Name it: CHAEONIX Trading System" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
