/**
 * Security Features Integration Tests
 */

const request = require('supertest');
const app = require('../../../api/app');
const fs = require('fs').promises;
const path = require('path');

// Mock services
jest.mock('../../../api/services/RateLimitService');
jest.mock('../../../api/services/BruteForceProtectionService');
jest.mock('../../../api/services/IpRestrictionService');
jest.mock('../../../api/services/AuthAuditService');
jest.mock('../../../api/services/AuthService');

// Import mocked services
const RateLimitService = require('../../../api/services/RateLimitService');
const BruteForceProtectionService = require('../../../api/services/BruteForceProtectionService');
const IpRestrictionService = require('../../../api/services/IpRestrictionService');
const AuthAuditService = require('../../../api/services/AuthAuditService');
const AuthService = require('../../../api/services/AuthService');

// Mock JWT token verification
jest.mock('jsonwebtoken', () => ({
  verify: jest.fn().mockImplementation((token, secret) => {
    if (token === 'valid-admin-token') {
      return { sub: 'admin-user-id', role: 'admin' };
    } else if (token === 'valid-user-token') {
      return { sub: 'regular-user-id', role: 'user' };
    } else {
      throw new Error('Invalid token');
    }
  }),
  sign: jest.fn().mockReturnValue('new-token')
}));

describe('Security Features Integration Tests', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock RateLimitService
    RateLimitService.mockImplementation(() => ({
      loadConfig: jest.fn().mockResolvedValue({
        global: { enabled: true, windowMs: 60000, max: 100 },
        auth: { enabled: true, windowMs: 900000, max: 10 },
        api: { enabled: true, windowMs: 60000, max: 60 }
      }),
      createLimiter: jest.fn().mockReturnValue((req, res, next) => next()),
      updateConfig: jest.fn().mockResolvedValue({})
    }));
    
    // Mock BruteForceProtectionService
    BruteForceProtectionService.mockImplementation(() => ({
      checkLoginAttempt: jest.fn().mockResolvedValue(true),
      handleSuccessfulLogin: jest.fn().mockResolvedValue(true),
      handleFailedLogin: jest.fn().mockResolvedValue({
        attemptsCount: 1,
        maxAttempts: 5,
        remainingAttempts: 4
      }),
      getConfig: jest.fn().mockReturnValue({
        maxAttempts: 5,
        windowMs: 900000,
        blockDuration: 1800000
      }),
      updateConfig: jest.fn().mockResolvedValue({})
    }));
    
    // Mock IpRestrictionService
    IpRestrictionService.mockImplementation(() => ({
      isAllowed: jest.fn().mockResolvedValue(true),
      loadRestrictions: jest.fn().mockResolvedValue({
        enabled: false,
        mode: 'allowlist',
        allowlist: [],
        blocklist: [],
        rules: []
      }),
      addToAllowlist: jest.fn().mockResolvedValue({}),
      addToBlocklist: jest.fn().mockResolvedValue({}),
      updateConfig: jest.fn().mockResolvedValue({})
    }));
    
    // Mock AuthAuditService
    AuthAuditService.mockImplementation(() => ({
      logLoginAttempt: jest.fn().mockResolvedValue({ id: 'log-id' }),
      logLogout: jest.fn().mockResolvedValue({ id: 'log-id' }),
      logRegistration: jest.fn().mockResolvedValue({ id: 'log-id' }),
      logTwoFactorAuth: jest.fn().mockResolvedValue({ id: 'log-id' }),
      getAuthAuditLogs: jest.fn().mockResolvedValue({
        logs: [],
        total: 0,
        page: 1,
        limit: 10
      })
    }));
    
    // Mock AuthService
    AuthService.mockImplementation(() => ({
      login: jest.fn().mockResolvedValue({
        user: {
          id: 'user-id',
          username: 'testuser',
          email: '<EMAIL>',
          role: 'user'
        },
        token: 'valid-user-token',
        refreshToken: 'refresh-token',
        expiresIn: '24h'
      }),
      logout: jest.fn().mockResolvedValue({
        success: true,
        message: 'Logged out successfully'
      }),
      register: jest.fn().mockResolvedValue({
        id: 'user-id',
        username: 'testuser',
        email: '<EMAIL>',
        role: 'user'
      })
    }));
  });
  
  describe('Rate Limiting', () => {
    it('should allow access to rate limit configuration for admin users', async () => {
      const response = await request(app)
        .get('/api/rate-limits')
        .set('Authorization', 'Bearer valid-admin-token');
      
      expect(response.status).toBe(200);
    });
    
    it('should deny access to rate limit configuration for non-admin users', async () => {
      const response = await request(app)
        .get('/api/rate-limits')
        .set('Authorization', 'Bearer valid-user-token');
      
      expect(response.status).toBe(403);
    });
    
    it('should allow updating rate limit configuration for admin users', async () => {
      const response = await request(app)
        .put('/api/rate-limits')
        .set('Authorization', 'Bearer valid-admin-token')
        .send({
          rateLimits: {
            anonymous: {
              requests: 30,
              period: 60
            }
          }
        });
      
      expect(response.status).toBe(200);
    });
  });
  
  describe('Brute Force Protection', () => {
    it('should allow access to brute force protection configuration for admin users', async () => {
      const response = await request(app)
        .get('/api/brute-force/config')
        .set('Authorization', 'Bearer valid-admin-token');
      
      expect(response.status).toBe(200);
    });
    
    it('should deny access to brute force protection configuration for non-admin users', async () => {
      const response = await request(app)
        .get('/api/brute-force/config')
        .set('Authorization', 'Bearer valid-user-token');
      
      expect(response.status).toBe(403);
    });
    
    it('should allow updating brute force protection configuration for admin users', async () => {
      const response = await request(app)
        .put('/api/brute-force/config')
        .set('Authorization', 'Bearer valid-admin-token')
        .send({
          maxAttempts: 3,
          windowMs: 600000,
          blockDuration: 3600000
        });
      
      expect(response.status).toBe(200);
    });
  });
  
  describe('IP Restrictions', () => {
    it('should allow access to IP restrictions configuration for admin users', async () => {
      const response = await request(app)
        .get('/api/ip-restrictions')
        .set('Authorization', 'Bearer valid-admin-token');
      
      expect(response.status).toBe(200);
    });
    
    it('should deny access to IP restrictions configuration for non-admin users', async () => {
      const response = await request(app)
        .get('/api/ip-restrictions')
        .set('Authorization', 'Bearer valid-user-token');
      
      expect(response.status).toBe(403);
    });
    
    it('should allow adding IP to allowlist for admin users', async () => {
      const response = await request(app)
        .post('/api/ip-restrictions/allowlist')
        .set('Authorization', 'Bearer valid-admin-token')
        .send({
          ip: '***********'
        });
      
      expect(response.status).toBe(200);
    });
  });
  
  describe('Authentication Audit Logging', () => {
    it('should allow access to auth audit logs for admin users', async () => {
      const response = await request(app)
        .get('/api/auth/audit')
        .set('Authorization', 'Bearer valid-admin-token');
      
      expect(response.status).toBe(200);
    });
    
    it('should deny access to auth audit logs for non-admin users', async () => {
      const response = await request(app)
        .get('/api/auth/audit')
        .set('Authorization', 'Bearer valid-user-token');
      
      expect(response.status).toBe(403);
    });
    
    it('should allow access to user\'s own login history', async () => {
      const response = await request(app)
        .get('/api/auth/audit/user/regular-user-id/login-history')
        .set('Authorization', 'Bearer valid-user-token');
      
      expect(response.status).toBe(200);
    });
    
    it('should deny access to another user\'s login history for non-admin users', async () => {
      const response = await request(app)
        .get('/api/auth/audit/user/admin-user-id/login-history')
        .set('Authorization', 'Bearer valid-user-token');
      
      expect(response.status).toBe(403);
    });
  });
  
  describe('Authentication with Security Features', () => {
    it('should log successful login attempts', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'password123'
        });
      
      expect(response.status).toBe(200);
      
      // Verify that login attempt was logged
      const authAuditServiceInstance = AuthAuditService.mock.instances[0];
      expect(authAuditServiceInstance.logLoginAttempt).toHaveBeenCalled();
      
      // Verify that brute force protection was checked
      const bruteForceServiceInstance = BruteForceProtectionService.mock.instances[0];
      expect(bruteForceServiceInstance.checkLoginAttempt).toHaveBeenCalled();
      expect(bruteForceServiceInstance.handleSuccessfulLogin).toHaveBeenCalled();
    });
    
    it('should log failed login attempts', async () => {
      // Mock AuthService to simulate failed login
      AuthService.mockImplementationOnce(() => ({
        login: jest.fn().mockRejectedValue(new Error('Invalid username or password'))
      }));
      
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'wrongpassword'
        });
      
      expect(response.status).toBe(500); // In a real app, this would be 401
      
      // Verify that failed login attempt was handled by brute force protection
      const bruteForceServiceInstance = BruteForceProtectionService.mock.instances[0];
      expect(bruteForceServiceInstance.checkLoginAttempt).toHaveBeenCalled();
      expect(bruteForceServiceInstance.handleFailedLogin).toHaveBeenCalled();
    });
  });
});
