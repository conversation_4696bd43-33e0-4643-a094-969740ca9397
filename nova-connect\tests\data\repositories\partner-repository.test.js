/**
 * NovaFuse Universal API Connector - Partner Repository Tests
 */

const { partnerRepository } = require('../../../src/data/repositories');
const Partner = require('../../../src/data/models/partner');

// Mock the Partner model
jest.mock('../../../src/data/models/partner', () => {
  return {
    findOne: jest.fn(),
    find: jest.fn().mockReturnThis(),
    countDocuments: jest.fn(),
    findOneAndUpdate: jest.fn(),
    deleteOne: jest.fn(),
    sort: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    validateApiCredentials: jest.fn()
  };
});

describe('Partner Repository', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  const mockPartner = {
    id: 'partner-123',
    name: 'Test Partner',
    description: 'Test partner for unit tests',
    website: 'https://example.com',
    logo: 'https://example.com/logo.png',
    apiKey: 'api-key-123',
    apiSecret: 'api-secret-123',
    status: 'active',
    tier: 'pro',
    contactInfo: {
      primaryEmail: '<EMAIL>',
      technicalEmail: '<EMAIL>',
      billingEmail: '<EMAIL>',
      phone: '************'
    },
    limits: {
      maxRequests: 10000,
      maxConcurrentRequests: 10,
      maxConnectors: 5,
      maxCredentials: 10
    },
    regenerateApiCredentials: jest.fn().mockResolvedValue({
      id: 'partner-123',
      apiKey: 'new-api-key',
      apiSecret: 'new-api-secret'
    }),
    checkLimits: jest.fn().mockResolvedValue(true)
  };
  
  it('should get a partner by ID', async () => {
    Partner.findOne.mockResolvedValue(mockPartner);
    
    const partner = await partnerRepository.getPartnerById('partner-123');
    
    expect(Partner.findOne).toHaveBeenCalledWith({ id: 'partner-123', status: { $ne: 'suspended' } });
    expect(partner).toEqual(mockPartner);
  });
  
  it('should get a partner by API key', async () => {
    Partner.findOne.mockResolvedValue(mockPartner);
    
    const partner = await partnerRepository.getPartnerByApiKey('api-key-123');
    
    expect(Partner.findOne).toHaveBeenCalledWith({ apiKey: 'api-key-123', status: 'active' });
    expect(partner).toEqual(mockPartner);
  });
  
  it('should validate API credentials', async () => {
    Partner.validateApiCredentials.mockResolvedValue(mockPartner);
    
    const partner = await partnerRepository.validateApiCredentials('api-key-123', 'api-secret-123');
    
    expect(Partner.validateApiCredentials).toHaveBeenCalledWith('api-key-123', 'api-secret-123');
    expect(partner).toEqual(mockPartner);
  });
  
  it('should get all partners', async () => {
    Partner.find.mockImplementation(() => ({
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockResolvedValue([mockPartner])
    }));
    
    const partners = await partnerRepository.getAllPartners();
    
    expect(Partner.find).toHaveBeenCalledWith({ status: { $ne: 'suspended' } });
    expect(partners).toEqual([mockPartner]);
  });
  
  it('should create a partner', async () => {
    const mockPartnerModel = {
      save: jest.fn().mockResolvedValue(mockPartner)
    };
    
    jest.spyOn(global, 'Object').mockImplementationOnce(() => mockPartnerModel);
    
    const partner = await partnerRepository.createPartner(mockPartner);
    
    expect(mockPartnerModel.save).toHaveBeenCalled();
    expect(partner).toEqual(mockPartner);
    
    global.Object.mockRestore();
  });
  
  it('should update a partner', async () => {
    Partner.findOneAndUpdate.mockResolvedValue(mockPartner);
    
    const partner = await partnerRepository.updatePartner('partner-123', { name: 'Updated Partner' });
    
    expect(Partner.findOneAndUpdate).toHaveBeenCalled();
    expect(partner).toEqual(mockPartner);
  });
  
  it('should delete a partner', async () => {
    Partner.findOneAndUpdate.mockResolvedValue(mockPartner);
    
    const result = await partnerRepository.deletePartner('partner-123');
    
    expect(Partner.findOneAndUpdate).toHaveBeenCalled();
    expect(result).toBe(true);
  });
  
  it('should regenerate API credentials', async () => {
    Partner.findOne.mockResolvedValue(mockPartner);
    
    const partner = await partnerRepository.regenerateApiCredentials('partner-123');
    
    expect(Partner.findOne).toHaveBeenCalledWith({ id: 'partner-123' });
    expect(mockPartner.regenerateApiCredentials).toHaveBeenCalled();
    expect(partner).toEqual({
      id: 'partner-123',
      apiKey: 'new-api-key',
      apiSecret: 'new-api-secret'
    });
  });
  
  it('should check partner limits', async () => {
    Partner.findOne.mockResolvedValue(mockPartner);
    
    const result = await partnerRepository.checkPartnerLimits('partner-123', 'requests', 5000);
    
    expect(Partner.findOne).toHaveBeenCalledWith({ id: 'partner-123', status: 'active' });
    expect(mockPartner.checkLimits).toHaveBeenCalledWith('requests', 5000);
    expect(result).toBe(true);
  });
  
  it('should update partner limits', async () => {
    Partner.findOneAndUpdate.mockResolvedValue(mockPartner);
    
    const limits = {
      maxRequests: 20000,
      maxConcurrentRequests: 20,
      maxConnectors: 10,
      maxCredentials: 20
    };
    
    const partner = await partnerRepository.updatePartnerLimits('partner-123', limits);
    
    expect(Partner.findOneAndUpdate).toHaveBeenCalled();
    expect(partner).toEqual(mockPartner);
  });
  
  it('should count partners', async () => {
    Partner.countDocuments.mockResolvedValue(5);
    
    const count = await partnerRepository.countPartners();
    
    expect(Partner.countDocuments).toHaveBeenCalledWith({});
    expect(count).toBe(5);
  });
  
  it('should search partners', async () => {
    Partner.find.mockImplementation(() => ({
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockResolvedValue([mockPartner])
    }));
    
    const partners = await partnerRepository.searchPartners('test');
    
    expect(Partner.find).toHaveBeenCalled();
    expect(partners).toEqual([mockPartner]);
  });
});
