/**
 * Regulatory Compliance Service
 * 
 * This service provides integration with regulatory compliance frameworks and requirements.
 * It handles mapping between different regulations, tracking compliance status, and
 * generating compliance reports.
 */

const mongoose = require('mongoose');
const logger = require('../config/logger');
const { RegulatoryFramework, ComplianceRequirement, ComplianceStatus } = require('../models');

class RegulatoryComplianceService {
  constructor() {
    this.regulationCache = new Map();
    this.requirementCache = new Map();
  }

  /**
   * Initialize the service
   */
  async initialize() {
    try {
      // Load regulations into cache
      const regulations = await RegulatoryFramework.find({ status: 'active' });
      
      for (const regulation of regulations) {
        this.regulationCache.set(regulation.code, regulation);
      }
      
      logger.info(`Loaded ${regulations.length} regulatory frameworks into cache`);
      
      return true;
    } catch (error) {
      logger.error('Failed to initialize regulatory compliance service:', error);
      throw error;
    }
  }

  /**
   * Get all regulatory frameworks
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options
   * @returns {Promise<Array>} - List of regulatory frameworks
   */
  async getAllFrameworks(filter = {}, options = {}) {
    try {
      const { 
        status = 'active', 
        region, 
        category, 
        search,
        page = 1,
        limit = 20,
        sort = { name: 1 }
      } = filter;
      
      const query = { status };
      
      if (region) {
        query.regions = region;
      }
      
      if (category) {
        query.category = category;
      }
      
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { code: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }
      
      const skip = (page - 1) * limit;
      
      const frameworks = await RegulatoryFramework.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit);
      
      const total = await RegulatoryFramework.countDocuments(query);
      
      return {
        data: frameworks,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Failed to get regulatory frameworks:', error);
      throw error;
    }
  }

  /**
   * Get regulatory framework by ID
   * @param {string} id - Framework ID
   * @returns {Promise<Object>} - Regulatory framework
   */
  async getFrameworkById(id) {
    try {
      const framework = await RegulatoryFramework.findById(id);
      
      if (!framework) {
        throw new Error(`Regulatory framework with ID ${id} not found`);
      }
      
      return framework;
    } catch (error) {
      logger.error(`Failed to get regulatory framework with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get regulatory framework by code
   * @param {string} code - Framework code
   * @returns {Promise<Object>} - Regulatory framework
   */
  async getFrameworkByCode(code) {
    try {
      // Check cache first
      if (this.regulationCache.has(code)) {
        return this.regulationCache.get(code);
      }
      
      const framework = await RegulatoryFramework.findOne({ code });
      
      if (!framework) {
        throw new Error(`Regulatory framework with code ${code} not found`);
      }
      
      // Update cache
      this.regulationCache.set(code, framework);
      
      return framework;
    } catch (error) {
      logger.error(`Failed to get regulatory framework with code ${code}:`, error);
      throw error;
    }
  }

  /**
   * Get compliance requirements for a framework
   * @param {string} frameworkId - Framework ID
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options
   * @returns {Promise<Array>} - List of compliance requirements
   */
  async getRequirementsByFramework(frameworkId, filter = {}, options = {}) {
    try {
      const { 
        status = 'active', 
        category, 
        search,
        page = 1,
        limit = 50,
        sort = { section: 1, number: 1 }
      } = filter;
      
      const query = { 
        framework: frameworkId,
        status
      };
      
      if (category) {
        query.category = category;
      }
      
      if (search) {
        query.$or = [
          { title: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } },
          { section: { $regex: search, $options: 'i' } },
          { number: { $regex: search, $options: 'i' } }
        ];
      }
      
      const skip = (page - 1) * limit;
      
      const requirements = await ComplianceRequirement.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit);
      
      const total = await ComplianceRequirement.countDocuments(query);
      
      return {
        data: requirements,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error(`Failed to get compliance requirements for framework ${frameworkId}:`, error);
      throw error;
    }
  }

  /**
   * Get compliance requirement by ID
   * @param {string} id - Requirement ID
   * @returns {Promise<Object>} - Compliance requirement
   */
  async getRequirementById(id) {
    try {
      const requirement = await ComplianceRequirement.findById(id);
      
      if (!requirement) {
        throw new Error(`Compliance requirement with ID ${id} not found`);
      }
      
      return requirement;
    } catch (error) {
      logger.error(`Failed to get compliance requirement with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get compliance status for an entity
   * @param {string} entityType - Entity type (e.g., 'organization', 'system', 'process')
   * @param {string} entityId - Entity ID
   * @param {string} frameworkId - Framework ID
   * @returns {Promise<Object>} - Compliance status
   */
  async getComplianceStatus(entityType, entityId, frameworkId) {
    try {
      const status = await ComplianceStatus.findOne({
        entityType,
        entityId,
        framework: frameworkId
      });
      
      if (!status) {
        // Create a new status record if one doesn't exist
        const framework = await this.getFrameworkById(frameworkId);
        const requirements = await ComplianceRequirement.find({ 
          framework: frameworkId,
          status: 'active'
        });
        
        const newStatus = new ComplianceStatus({
          entityType,
          entityId,
          framework: frameworkId,
          frameworkName: framework.name,
          frameworkCode: framework.code,
          frameworkVersion: framework.version,
          status: 'not-started',
          progress: 0,
          requirementStatuses: requirements.map(req => ({
            requirement: req._id,
            requirementCode: `${req.section}.${req.number}`,
            requirementTitle: req.title,
            status: 'not-started',
            evidence: [],
            notes: '',
            assignedTo: null,
            dueDate: null
          })),
          lastAssessment: null,
          nextAssessment: null
        });
        
        await newStatus.save();
        return newStatus;
      }
      
      return status;
    } catch (error) {
      logger.error(`Failed to get compliance status for ${entityType} ${entityId} and framework ${frameworkId}:`, error);
      throw error;
    }
  }

  /**
   * Update compliance status for a requirement
   * @param {string} statusId - Compliance status ID
   * @param {string} requirementId - Requirement ID
   * @param {Object} updates - Updates to apply
   * @returns {Promise<Object>} - Updated compliance status
   */
  async updateRequirementStatus(statusId, requirementId, updates) {
    try {
      const status = await ComplianceStatus.findById(statusId);
      
      if (!status) {
        throw new Error(`Compliance status with ID ${statusId} not found`);
      }
      
      const requirementIndex = status.requirementStatuses.findIndex(
        rs => rs.requirement.toString() === requirementId
      );
      
      if (requirementIndex === -1) {
        throw new Error(`Requirement with ID ${requirementId} not found in compliance status`);
      }
      
      // Update the requirement status
      Object.assign(status.requirementStatuses[requirementIndex], updates);
      
      // Recalculate overall progress
      const totalRequirements = status.requirementStatuses.length;
      const completedRequirements = status.requirementStatuses.filter(
        rs => rs.status === 'compliant'
      ).length;
      
      status.progress = Math.round((completedRequirements / totalRequirements) * 100);
      
      // Update overall status
      if (status.progress === 100) {
        status.status = 'compliant';
      } else if (status.progress > 0) {
        status.status = 'in-progress';
      } else {
        status.status = 'not-started';
      }
      
      // Update last assessment date
      status.lastAssessment = new Date();
      
      await status.save();
      return status;
    } catch (error) {
      logger.error(`Failed to update requirement status for compliance status ${statusId} and requirement ${requirementId}:`, error);
      throw error;
    }
  }

  /**
   * Generate compliance report
   * @param {string} entityType - Entity type
   * @param {string} entityId - Entity ID
   * @param {string} frameworkId - Framework ID
   * @returns {Promise<Object>} - Compliance report
   */
  async generateComplianceReport(entityType, entityId, frameworkId) {
    try {
      const status = await this.getComplianceStatus(entityType, entityId, frameworkId);
      const framework = await this.getFrameworkById(frameworkId);
      
      // Group requirements by section
      const requirementsBySection = {};
      
      for (const reqStatus of status.requirementStatuses) {
        const section = reqStatus.requirementCode.split('.')[0];
        
        if (!requirementsBySection[section]) {
          requirementsBySection[section] = [];
        }
        
        requirementsBySection[section].push(reqStatus);
      }
      
      // Calculate section compliance
      const sectionCompliance = {};
      
      for (const section in requirementsBySection) {
        const requirements = requirementsBySection[section];
        const totalRequirements = requirements.length;
        const compliantRequirements = requirements.filter(r => r.status === 'compliant').length;
        
        sectionCompliance[section] = {
          total: totalRequirements,
          compliant: compliantRequirements,
          progress: Math.round((compliantRequirements / totalRequirements) * 100)
        };
      }
      
      // Generate report
      const report = {
        entityType,
        entityId,
        framework: {
          id: framework._id,
          name: framework.name,
          code: framework.code,
          version: framework.version,
          description: framework.description
        },
        status: status.status,
        progress: status.progress,
        lastAssessment: status.lastAssessment,
        nextAssessment: status.nextAssessment,
        sectionCompliance,
        requirementsBySection,
        summary: {
          total: status.requirementStatuses.length,
          compliant: status.requirementStatuses.filter(r => r.status === 'compliant').length,
          nonCompliant: status.requirementStatuses.filter(r => r.status === 'non-compliant').length,
          inProgress: status.requirementStatuses.filter(r => r.status === 'in-progress').length,
          notStarted: status.requirementStatuses.filter(r => r.status === 'not-started').length,
          notApplicable: status.requirementStatuses.filter(r => r.status === 'not-applicable').length
        },
        generatedAt: new Date()
      };
      
      return report;
    } catch (error) {
      logger.error(`Failed to generate compliance report for ${entityType} ${entityId} and framework ${frameworkId}:`, error);
      throw error;
    }
  }

  /**
   * Map requirements between frameworks
   * @param {string} sourceFrameworkId - Source framework ID
   * @param {string} targetFrameworkId - Target framework ID
   * @returns {Promise<Object>} - Mapping between requirements
   */
  async mapRequirementsBetweenFrameworks(sourceFrameworkId, targetFrameworkId) {
    try {
      const sourceFramework = await this.getFrameworkById(sourceFrameworkId);
      const targetFramework = await this.getFrameworkById(targetFrameworkId);
      
      const sourceRequirements = await ComplianceRequirement.find({
        framework: sourceFrameworkId,
        status: 'active'
      });
      
      const targetRequirements = await ComplianceRequirement.find({
        framework: targetFrameworkId,
        status: 'active'
      });
      
      // Build mapping based on related requirements
      const mapping = {};
      
      for (const sourceReq of sourceRequirements) {
        const sourceKey = `${sourceReq.section}.${sourceReq.number}`;
        mapping[sourceKey] = [];
        
        // Check for direct mappings in related requirements
        for (const relatedReq of sourceReq.relatedRequirements) {
          if (relatedReq.framework.toString() === targetFrameworkId) {
            mapping[sourceKey].push({
              id: relatedReq.requirement,
              code: relatedReq.requirementCode,
              title: relatedReq.requirementTitle,
              mappingStrength: relatedReq.mappingStrength
            });
          }
        }
        
        // If no direct mappings, look for keyword matches
        if (mapping[sourceKey].length === 0) {
          const keywords = sourceReq.keywords || [];
          
          for (const targetReq of targetRequirements) {
            const targetKeywords = targetReq.keywords || [];
            const matchingKeywords = keywords.filter(k => targetKeywords.includes(k));
            
            if (matchingKeywords.length > 0) {
              mapping[sourceKey].push({
                id: targetReq._id,
                code: `${targetReq.section}.${targetReq.number}`,
                title: targetReq.title,
                mappingStrength: 'medium',
                matchedKeywords: matchingKeywords
              });
            }
          }
        }
      }
      
      return {
        sourceFramework: {
          id: sourceFramework._id,
          name: sourceFramework.name,
          code: sourceFramework.code,
          version: sourceFramework.version
        },
        targetFramework: {
          id: targetFramework._id,
          name: targetFramework.name,
          code: targetFramework.code,
          version: targetFramework.version
        },
        mapping,
        generatedAt: new Date()
      };
    } catch (error) {
      logger.error(`Failed to map requirements between frameworks ${sourceFrameworkId} and ${targetFrameworkId}:`, error);
      throw error;
    }
  }

  /**
   * Get regulatory updates
   * @param {Object} filter - Filter criteria
   * @returns {Promise<Array>} - List of regulatory updates
   */
  async getRegulatoryUpdates(filter = {}) {
    try {
      const { 
        region, 
        framework,
        since,
        page = 1,
        limit = 20
      } = filter;
      
      const query = { status: 'active' };
      
      if (region) {
        query.regions = region;
      }
      
      if (framework) {
        query.framework = framework;
      }
      
      if (since) {
        query.publishedDate = { $gte: new Date(since) };
      }
      
      const skip = (page - 1) * limit;
      
      // In a real implementation, this would query a regulatory updates collection
      // For now, we'll return a mock response
      
      return {
        data: [
          {
            id: 'update-1',
            title: 'GDPR Enforcement Update',
            description: 'New guidelines on GDPR enforcement for AI systems',
            framework: 'GDPR',
            region: 'EU',
            publishedDate: new Date('2023-06-15'),
            effectiveDate: new Date('2023-09-01'),
            url: 'https://example.com/gdpr-ai-guidelines',
            severity: 'medium',
            impactedRequirements: [
              { code: 'Art.22', description: 'Automated decision-making' },
              { code: 'Art.35', description: 'Data protection impact assessment' }
            ]
          },
          {
            id: 'update-2',
            title: 'CCPA Amendment',
            description: 'California legislature passes amendment to CCPA regarding data retention',
            framework: 'CCPA',
            region: 'US-CA',
            publishedDate: new Date('2023-07-10'),
            effectiveDate: new Date('2024-01-01'),
            url: 'https://example.com/ccpa-amendment',
            severity: 'high',
            impactedRequirements: [
              { code: '1798.100', description: 'Data retention requirements' }
            ]
          }
        ],
        pagination: {
          page,
          limit,
          total: 2,
          pages: 1
        }
      };
    } catch (error) {
      logger.error('Failed to get regulatory updates:', error);
      throw error;
    }
  }
}

module.exports = new RegulatoryComplianceService();
