/**
 * Feedback Controller
 * 
 * This controller handles API requests related to user feedback.
 */

const FeedbackService = require('../services/FeedbackService');
const AuditService = require('../services/AuditService');
const { ValidationError } = require('../utils/errors');
const logger = require('../utils/logger');

class FeedbackController {
  constructor() {
    this.feedbackService = new FeedbackService();
    this.auditService = new AuditService();
  }

  /**
   * Submit feedback
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  async submitFeedback(req, res, next) {
    try {
      const { type, component, rating, title, description, metadata, tags } = req.body;
      const userId = req.user.id;

      // Validate required fields
      if (!type || !component || !rating || !title || !description) {
        throw new ValidationError('Missing required fields');
      }

      // Validate rating
      if (rating < 1 || rating > 5) {
        throw new ValidationError('Rating must be between 1 and 5');
      }

      // Create feedback data
      const feedbackData = {
        userId,
        type,
        component,
        rating,
        title,
        description,
        metadata: metadata || {},
        tags: tags || []
      };

      // Submit feedback
      const feedback = await this.feedbackService.submitFeedback(feedbackData);

      // Log audit event
      await this.auditService.logEvent({
        userId,
        action: 'CREATE',
        resourceType: 'feedback',
        resourceId: feedback._id.toString(),
        details: {
          type,
          component,
          rating
        },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });

      res.status(201).json({
        success: true,
        message: 'Feedback submitted successfully',
        data: feedback
      });
    } catch (error) {
      logger.error('Error submitting feedback', error);
      next(error);
    }
  }

  /**
   * Get feedback by ID
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  async getFeedbackById(req, res, next) {
    try {
      const { id } = req.params;

      // Get feedback
      const feedback = await this.feedbackService.getFeedbackById(id);

      // Check if user has permission to view this feedback
      const isAdmin = req.user.role === 'admin';
      const isOwner = feedback.userId.toString() === req.user.id;

      if (!isAdmin && !isOwner) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to view this feedback'
        });
      }

      res.json({
        success: true,
        data: feedback
      });
    } catch (error) {
      logger.error(`Error getting feedback with ID ${req.params.id}`, error);
      next(error);
    }
  }

  /**
   * Get my feedback
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  async getMyFeedback(req, res, next) {
    try {
      const userId = req.user.id;
      const options = req.query;

      // Get feedback
      const result = await this.feedbackService.getFeedbackByUser(userId, options);

      res.json({
        success: true,
        data: result.feedback,
        pagination: result.pagination
      });
    } catch (error) {
      logger.error(`Error getting feedback for user ${req.user.id}`, error);
      next(error);
    }
  }

  /**
   * Get all feedback (admin only)
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  async getAllFeedback(req, res, next) {
    try {
      const options = req.query;

      // Get feedback
      const result = await this.feedbackService.getAllFeedback(options);

      res.json({
        success: true,
        data: result.feedback,
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Error getting all feedback', error);
      next(error);
    }
  }

  /**
   * Update feedback status (admin only)
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  async updateFeedbackStatus(req, res, next) {
    try {
      const { id } = req.params;
      const { status, response } = req.body;

      // Validate status
      if (!status) {
        throw new ValidationError('Status is required');
      }

      // Update feedback
      const feedback = await this.feedbackService.updateFeedbackStatus(id, { status, response });

      // Log audit event
      await this.auditService.logEvent({
        userId: req.user.id,
        action: 'UPDATE',
        resourceType: 'feedback',
        resourceId: id,
        details: {
          status,
          hasResponse: !!response
        },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });

      res.json({
        success: true,
        message: 'Feedback status updated successfully',
        data: feedback
      });
    } catch (error) {
      logger.error(`Error updating feedback status for ID ${req.params.id}`, error);
      next(error);
    }
  }

  /**
   * Get feedback statistics (admin only)
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  async getFeedbackStatistics(req, res, next) {
    try {
      // Get statistics
      const statistics = await this.feedbackService.getFeedbackStatistics();

      res.json({
        success: true,
        data: statistics
      });
    } catch (error) {
      logger.error('Error getting feedback statistics', error);
      next(error);
    }
  }
}

module.exports = FeedbackController;
