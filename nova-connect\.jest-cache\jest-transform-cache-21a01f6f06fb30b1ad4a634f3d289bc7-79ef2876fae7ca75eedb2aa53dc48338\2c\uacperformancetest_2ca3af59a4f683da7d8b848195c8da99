e1255832358535d1b26a0659a06b96da
/**
 * NovaConnect UAC Performance Tests
 * 
 * This test suite validates the performance of the Universal API Connector.
 */

const {
  performance
} = require('perf_hooks');
const {
  TransformationEngine
} = require('../../src/engines/transformation-engine');
const {
  RemediationEngine
} = require('../../src/engines/remediation-engine');
const {
  ConnectorRegistry
} = require('../../src/registry/connector-registry');

// Performance thresholds
const THRESHOLDS = {
  transformation: {
    singleTransform: 1,
    // ms
    batchTransform: 5,
    // ms
    complexTransform: 10 // ms
  },
  remediation: {
    simpleRemediation: 50,
    // ms
    complexRemediation: 200 // ms
  },
  connector: {
    registration: 5,
    // ms
    execution: 20 // ms
  }
};

// Test data
const sampleData = {
  id: '12345',
  type: 'user',
  attributes: {
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-02T00:00:00Z',
    status: 'active',
    roles: ['user', 'admin'],
    preferences: {
      theme: 'dark',
      notifications: {
        email: true,
        push: false
      }
    }
  },
  relationships: {
    organization: {
      data: {
        id: '67890',
        type: 'organization'
      }
    }
  }
};

// Generate large batch data
function generateBatchData(count) {
  const items = [];
  for (let i = 0; i < count; i++) {
    items.push({
      ...sampleData,
      id: `user-${i}`,
      attributes: {
        ...sampleData.attributes,
        firstName: `User${i}`,
        email: `user${i}@example.com`
      }
    });
  }
  return {
    data: items
  };
}
describe('NovaConnect UAC Performance Tests', () => {
  describe('TransformationEngine Performance', () => {
    let transformationEngine;
    beforeEach(() => {
      transformationEngine = new TransformationEngine({
        enableMetrics: true,
        enableCaching: true
      });
    });
    test('Single transformation performance', () => {
      const rules = [{
        source: 'id',
        target: 'userId',
        transform: 'toString'
      }, {
        source: 'attributes.firstName',
        target: 'firstName',
        transform: 'uppercase'
      }, {
        source: 'attributes.lastName',
        target: 'lastName',
        transform: 'uppercase'
      }, {
        source: 'attributes.email',
        target: 'email',
        transform: 'lowercase'
      }, {
        source: 'attributes.createdAt',
        target: 'createdTimestamp',
        transform: 'isoToUnix'
      }];
      const startTime = performance.now();

      // Run transformation multiple times to get average
      for (let i = 0; i < 1000; i++) {
        transformationEngine.transform(sampleData, rules);
      }
      const endTime = performance.now();
      const averageTime = (endTime - startTime) / 1000;
      console.log(`Single transformation average time: ${averageTime.toFixed(3)}ms`);
      expect(averageTime).toBeLessThan(THRESHOLDS.transformation.singleTransform);
    });
    test('Batch transformation performance', () => {
      const batchData = generateBatchData(100);
      const rules = [{
        source: 'data[*].id',
        target: 'users[*].userId',
        transform: 'toString'
      }, {
        source: 'data[*].attributes.firstName',
        target: 'users[*].firstName',
        transform: 'uppercase'
      }, {
        source: 'data[*].attributes.lastName',
        target: 'users[*].lastName',
        transform: 'uppercase'
      }, {
        source: 'data[*].attributes.email',
        target: 'users[*].email',
        transform: 'lowercase'
      }, {
        source: 'data[*].attributes.createdAt',
        target: 'users[*].createdTimestamp',
        transform: 'isoToUnix'
      }];
      const startTime = performance.now();

      // Run batch transformation
      transformationEngine.batchTransform(batchData, rules);
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      console.log(`Batch transformation time (100 items): ${totalTime.toFixed(3)}ms`);
      expect(totalTime).toBeLessThan(THRESHOLDS.transformation.batchTransform);
    });
    test('Complex transformation performance', () => {
      const complexRules = [{
        source: 'id',
        target: 'userId',
        transform: 'toString'
      }, {
        source: 'attributes.firstName',
        target: 'name.first',
        transform: 'uppercase'
      }, {
        source: 'attributes.lastName',
        target: 'name.last',
        transform: 'uppercase'
      }, {
        source: 'attributes.email',
        target: 'contact.email',
        transform: 'lowercase'
      }, {
        source: 'attributes.createdAt',
        target: 'timestamps.created',
        transform: 'isoToUnix'
      }, {
        source: 'attributes.updatedAt',
        target: 'timestamps.updated',
        transform: 'isoToUnix'
      }, {
        source: 'attributes.roles',
        target: 'security.roles',
        transform: 'join'
      }, {
        source: 'attributes.preferences',
        target: 'settings',
        transform: 'toJson'
      }, {
        source: 'relationships.organization.data.id',
        target: 'organization.id',
        transform: 'toString'
      }, {
        source: 'relationships.organization.data.type',
        target: 'organization.type',
        transform: 'lowercase'
      }];
      const startTime = performance.now();

      // Run complex transformation multiple times
      for (let i = 0; i < 100; i++) {
        transformationEngine.transform(sampleData, complexRules);
      }
      const endTime = performance.now();
      const averageTime = (endTime - startTime) / 100;
      console.log(`Complex transformation average time: ${averageTime.toFixed(3)}ms`);
      expect(averageTime).toBeLessThan(THRESHOLDS.transformation.complexTransform);
    });
    test('Transformation cache performance', () => {
      const rules = [{
        source: 'id',
        target: 'userId',
        transform: 'toString'
      }, {
        source: 'attributes.firstName',
        target: 'firstName',
        transform: 'uppercase'
      }, {
        source: 'attributes.lastName',
        target: 'lastName',
        transform: 'uppercase'
      }];

      // First transformation (cache miss)
      const startTimeMiss = performance.now();
      transformationEngine.transform(sampleData, rules);
      const endTimeMiss = performance.now();
      const missTime = endTimeMiss - startTimeMiss;

      // Second transformation (cache hit)
      const startTimeHit = performance.now();
      transformationEngine.transform(sampleData, rules);
      const endTimeHit = performance.now();
      const hitTime = endTimeHit - startTimeHit;
      console.log(`Transformation cache miss time: ${missTime.toFixed(3)}ms`);
      console.log(`Transformation cache hit time: ${hitTime.toFixed(3)}ms`);

      // Cache hit should be significantly faster than miss
      expect(hitTime).toBeLessThan(missTime * 0.5);
    });
  });
  describe('RemediationEngine Performance', () => {
    let remediationEngine;
    beforeEach(() => {
      remediationEngine = new RemediationEngine({
        enableMetrics: true,
        maxConcurrentRemediations: 10
      });

      // Register test actions
      remediationEngine.registerAction('test-action', async ({
        parameters
      }) => {
        return {
          success: true,
          parameters
        };
      });
      remediationEngine.registerAction('delay-action', async ({
        parameters
      }) => {
        await new Promise(resolve => setTimeout(resolve, parameters.delay || 10));
        return {
          success: true,
          parameters
        };
      });
    });
    test('Simple remediation performance', async () => {
      const scenario = {
        id: 'test-remediation',
        framework: 'test-framework',
        control: 'test-control',
        resource: {
          id: 'resource-123',
          type: 'test-resource'
        },
        finding: {
          id: 'finding-123',
          severity: 'medium'
        },
        remediationSteps: [{
          id: 'step-1',
          action: 'test-action',
          parameters: {
            param1: 'value1',
            param2: 'value2'
          }
        }]
      };
      const startTime = performance.now();
      await remediationEngine.executeRemediation(scenario);
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      console.log(`Simple remediation time: ${totalTime.toFixed(3)}ms`);
      expect(totalTime).toBeLessThan(THRESHOLDS.remediation.simpleRemediation);
    });
    test('Complex remediation performance', async () => {
      const scenario = {
        id: 'complex-remediation',
        framework: 'test-framework',
        control: 'test-control',
        resource: {
          id: 'resource-123',
          type: 'test-resource'
        },
        finding: {
          id: 'finding-123',
          severity: 'high'
        },
        remediationSteps: [{
          id: 'step-1',
          action: 'test-action',
          parameters: {
            param1: 'value1'
          }
        }, {
          id: 'step-2',
          action: 'delay-action',
          parameters: {
            delay: 50
          }
        }, {
          id: 'step-3',
          action: 'test-action',
          parameters: {
            param2: 'value2'
          }
        }, {
          id: 'step-4',
          action: 'delay-action',
          parameters: {
            delay: 50
          }
        }, {
          id: 'step-5',
          action: 'test-action',
          parameters: {
            param3: 'value3'
          }
        }]
      };
      const startTime = performance.now();
      await remediationEngine.executeRemediation(scenario);
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      console.log(`Complex remediation time: ${totalTime.toFixed(3)}ms`);
      expect(totalTime).toBeLessThan(THRESHOLDS.remediation.complexRemediation);
    });
  });
  describe('ConnectorRegistry Performance', () => {
    let connectorRegistry;
    beforeEach(() => {
      connectorRegistry = new ConnectorRegistry();

      // Register test connector
      connectorRegistry.registerConnector('test-connector', {
        execute: async (operation, params) => {
          return {
            operation,
            params,
            result: 'success'
          };
        }
      });
    });
    test('Connector registration performance', () => {
      const startTime = performance.now();

      // Register multiple connectors
      for (let i = 0; i < 100; i++) {
        connectorRegistry.registerConnector(`connector-${i}`, {
          execute: async (operation, params) => {
            return {
              operation,
              params,
              result: `success-${i}`
            };
          }
        });
      }
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      const averageTime = totalTime / 100;
      console.log(`Connector registration average time: ${averageTime.toFixed(3)}ms`);
      expect(averageTime).toBeLessThan(THRESHOLDS.connector.registration);
    });
    test('Connector execution performance', async () => {
      const startTime = performance.now();

      // Execute connector multiple times
      for (let i = 0; i < 100; i++) {
        await connectorRegistry.executeConnector('test-connector', 'GET', {
          path: `/resource/${i}`,
          params: {
            filter: 'active'
          }
        });
      }
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      const averageTime = totalTime / 100;
      console.log(`Connector execution average time: ${averageTime.toFixed(3)}ms`);
      expect(averageTime).toBeLessThan(THRESHOLDS.connector.execution);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************