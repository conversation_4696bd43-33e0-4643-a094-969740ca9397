#!/usr/bin/env python3
"""
UUFT Temporal Pattern Generator

This module generates synthetic time series data with embedded 18/82 patterns
and π10³ relationships across multiple time scales for testing UUFT pattern
detection algorithms.

The generator creates datasets that exhibit temporal stability or instability
of these patterns, allowing for testing of pattern resilience over time.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import os
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uuft_temporal.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UUFT_Temporal')

# Constants
PATTERN_1882_RATIO = 18 / 82
PI = np.pi
PI_10_CUBED = PI * 10**3
RESULTS_DIR = "uuft_results/temporal"
os.makedirs(RESULTS_DIR, exist_ok=True)

class UUFTTemporalGenerator:
    """Generator for time series data with embedded UUFT patterns."""

    def __init__(self, start_date=None, end_date=None, frequency='D', seed=None):
        """
        Initialize the temporal generator.

        Args:
            start_date: Starting date for the time series
            end_date: Ending date for the time series
            frequency: Pandas frequency string ('D' for daily, 'H' for hourly, etc.)
            seed: Random seed for reproducibility
        """
        self.start_date = start_date or datetime.now() - timedelta(days=365*5)
        self.end_date = end_date or datetime.now()
        self.frequency = frequency

        if seed is not None:
            np.random.seed(seed)

        self.date_range = pd.date_range(start=self.start_date, end=self.end_date, freq=self.frequency)
        logger.info(f"Initialized temporal generator with {len(self.date_range)} time points")

    def generate_1882_stable_series(self, base_value=100, noise_level=0.1, stability=0.95):
        """
        Generate a time series with stable 18/82 pattern distribution.

        Args:
            base_value: Base value for the time series
            noise_level: Level of random noise (0-1)
            stability: How stable the 18/82 pattern remains (0-1)

        Returns:
            DataFrame with time series data
        """
        logger.info(f"Generating 18/82 stable series with stability {stability}")

        # Create time points
        n_points = len(self.date_range)

        # Generate base series with 18/82 pattern
        # 18% of points will be higher values, 82% will be lower values
        is_high = np.random.random(n_points) < 0.18

        # Create values with 18/82 ratio in their distribution
        values = np.zeros(n_points)
        for i in range(n_points):
            if is_high[i]:
                # Higher values (18% of points)
                values[i] = base_value * 4.55  # This creates an 18/82 ratio in total sum
            else:
                # Lower values (82% of points)
                values[i] = base_value

        # Add temporal drift that might destabilize the pattern
        temporal_drift = np.zeros(n_points)
        drift_direction = 1

        for i in range(1, n_points):
            # Occasionally change drift direction
            if np.random.random() < 0.01:
                drift_direction *= -1

            # Calculate drift with stability factor
            # Higher stability means less drift
            drift_factor = (1 - stability) * 0.01
            temporal_drift[i] = temporal_drift[i-1] + drift_direction * drift_factor * base_value

            # Apply correction to maintain pattern if stability is high
            if stability > 0.8 and i % 30 == 0:  # Monthly correction
                # Calculate current ratio
                high_sum = np.sum(values[:i][is_high[:i]]) + np.sum(temporal_drift[:i][is_high[:i]])
                low_sum = np.sum(values[:i][~is_high[:i]]) + np.sum(temporal_drift[:i][~is_high[:i]])

                # Avoid division by zero
                if low_sum > 0:
                    current_ratio = high_sum / low_sum
                    target_ratio = PATTERN_1882_RATIO

                    # Apply correction factor to drift
                    correction = (target_ratio / current_ratio - 1) * stability
                    temporal_drift[i:] += correction * base_value

        # Add noise
        noise = np.random.normal(0, noise_level * base_value, n_points)

        # Combine components
        final_values = values + temporal_drift + noise

        # Create DataFrame
        df = pd.DataFrame({
            'timestamp': self.date_range,
            'value': final_values,
            'is_high_group': is_high
        })

        # Calculate and log the actual ratio
        high_sum = df[df['is_high_group']]['value'].sum()
        low_sum = df[~df['is_high_group']]['value'].sum()
        actual_ratio = high_sum / low_sum
        logger.info(f"Generated series with actual ratio: {actual_ratio:.4f} (target: {PATTERN_1882_RATIO:.4f})")

        return df

    def generate_pi_related_series(self, base_value=100, noise_level=0.1, stability=0.95):
        """
        Generate a time series with π-related patterns.

        Args:
            base_value: Base value for the time series
            noise_level: Level of random noise (0-1)
            stability: How stable the π pattern remains (0-1)

        Returns:
            DataFrame with time series data
        """
        logger.info(f"Generating π-related series with stability {stability}")

        # Create time points
        n_points = len(self.date_range)
        t = np.linspace(0, 10*np.pi, n_points)  # 10 pi cycles over the time period

        # Generate base series with π-related pattern
        # Using sin(t) + π/sin(t/π) creates interesting π-related patterns
        values = base_value * (np.sin(t) + PI/np.sin(np.maximum(0.1, t/PI)))

        # Add π*10³ amplitude modulation
        modulation = np.sin(t / PI_10_CUBED) * base_value * 0.5

        # Add temporal drift that might destabilize the pattern
        temporal_drift = np.zeros(n_points)
        drift_direction = 1

        for i in range(1, n_points):
            # Occasionally change drift direction
            if np.random.random() < 0.01:
                drift_direction *= -1

            # Calculate drift with stability factor
            drift_factor = (1 - stability) * 0.01
            temporal_drift[i] = temporal_drift[i-1] + drift_direction * drift_factor * base_value

            # Apply correction to maintain pattern if stability is high
            if stability > 0.8 and i % 30 == 0:  # Monthly correction
                # Calculate current π-correlation
                current_series = values[:i] + temporal_drift[:i]
                pi_correlation = np.corrcoef(current_series, np.sin(t[:i] / PI))[0, 1]

                # Apply correction factor to drift
                correction = (0.8 - pi_correlation) * stability  # Target correlation of 0.8
                if not np.isnan(correction):
                    temporal_drift[i:] += correction * base_value

        # Add noise
        noise = np.random.normal(0, noise_level * base_value, n_points)

        # Combine components
        final_values = values + modulation + temporal_drift + noise

        # Create DataFrame
        df = pd.DataFrame({
            'timestamp': self.date_range,
            'value': final_values,
            'pi_modulation': modulation
        })

        # Calculate and log the π-correlation
        pi_correlation = np.corrcoef(df['value'], np.sin(t / PI))[0, 1]
        logger.info(f"Generated series with π-correlation: {pi_correlation:.4f}")

        return df

    def generate_combined_series(self, base_value=100, noise_level=0.1, stability=0.95):
        """
        Generate a time series with both 18/82 and π-related patterns.

        Args:
            base_value: Base value for the time series
            noise_level: Level of random noise (0-1)
            stability: How stable the patterns remain (0-1)

        Returns:
            DataFrame with time series data
        """
        logger.info(f"Generating combined 18/82 and π-related series with stability {stability}")

        # Generate individual series
        df_1882 = self.generate_1882_stable_series(base_value, noise_level/2, stability)
        df_pi = self.generate_pi_related_series(base_value, noise_level/2, stability)

        # Combine the series
        df = pd.DataFrame({
            'timestamp': self.date_range,
            'value_1882': df_1882['value'],
            'value_pi': df_pi['value'],
            'is_high_group': df_1882['is_high_group'],
            'pi_modulation': df_pi['pi_modulation']
        })

        # Create combined value
        df['value'] = (df['value_1882'] + df['value_pi']) / 2

        return df

    def generate_multi_scale_series(self, scales=['D', 'W', 'M'], base_value=100, noise_level=0.1):
        """
        Generate multiple time series at different time scales, all with 18/82 patterns.

        Args:
            scales: List of pandas frequency strings for different scales
            base_value: Base value for the time series
            noise_level: Level of random noise (0-1)

        Returns:
            Dict of DataFrames with time series data at different scales
        """
        logger.info(f"Generating multi-scale series with scales {scales}")

        result = {}

        for scale in scales:
            # Create a new generator with the appropriate frequency
            temp_gen = UUFTTemporalGenerator(
                start_date=self.start_date,
                end_date=self.end_date,
                frequency=scale
            )

            # Generate series with varying stability based on scale
            # Longer time scales tend to be more stable
            if scale == 'D':  # Daily
                stability = 0.85
            elif scale == 'W':  # Weekly
                stability = 0.9
            elif scale == 'M':  # Monthly
                stability = 0.95
            else:
                stability = 0.9

            # Generate the series
            df = temp_gen.generate_1882_stable_series(
                base_value=base_value,
                noise_level=noise_level,
                stability=stability
            )

            result[scale] = df

        return result

    def visualize_series(self, df, title="UUFT Temporal Pattern", save_path=None):
        """
        Visualize a time series with UUFT patterns.

        Args:
            df: DataFrame with time series data
            title: Plot title
            save_path: Path to save the visualization
        """
        plt.figure(figsize=(12, 8))

        # Plot the main series
        plt.plot(df['timestamp'], df['value'], label='Value', color='blue', alpha=0.7)

        # If it's an 18/82 series, highlight the high group
        if 'is_high_group' in df.columns:
            high_group = df[df['is_high_group']]
            plt.scatter(high_group['timestamp'], high_group['value'],
                       color='red', alpha=0.5, label='High Group (18%)')

        # If it's a π-related series, show the modulation
        if 'pi_modulation' in df.columns:
            plt.plot(df['timestamp'], df['pi_modulation'],
                    color='green', alpha=0.3, label='π Modulation')

        plt.title(title)
        plt.xlabel('Time')
        plt.ylabel('Value')
        plt.legend()
        plt.grid(True, alpha=0.3)

        if save_path:
            plt.savefig(save_path, dpi=300)
            logger.info(f"Visualization saved to {save_path}")

        plt.close()

def generate_example_datasets():
    """Generate example datasets for testing."""
    logger.info("Generating example datasets")

    # Create generator
    gen = UUFTTemporalGenerator(
        start_date=datetime(2015, 1, 1),
        end_date=datetime(2023, 12, 31),
        frequency='D'
    )

    # Generate stable 18/82 series
    df_stable = gen.generate_1882_stable_series(stability=0.95)
    df_stable.to_csv(os.path.join(RESULTS_DIR, "stable_1882_series.csv"), index=False)
    gen.visualize_series(
        df_stable,
        title="Stable 18/82 Pattern Time Series",
        save_path=os.path.join(RESULTS_DIR, "stable_1882_series.png")
    )

    # Generate unstable 18/82 series
    df_unstable = gen.generate_1882_stable_series(stability=0.6)
    df_unstable.to_csv(os.path.join(RESULTS_DIR, "unstable_1882_series.csv"), index=False)
    gen.visualize_series(
        df_unstable,
        title="Unstable 18/82 Pattern Time Series",
        save_path=os.path.join(RESULTS_DIR, "unstable_1882_series.png")
    )

    # Generate π-related series
    df_pi = gen.generate_pi_related_series()
    df_pi.to_csv(os.path.join(RESULTS_DIR, "pi_related_series.csv"), index=False)
    gen.visualize_series(
        df_pi,
        title="π-Related Pattern Time Series",
        save_path=os.path.join(RESULTS_DIR, "pi_related_series.png")
    )

    # Generate combined series
    df_combined = gen.generate_combined_series()
    df_combined.to_csv(os.path.join(RESULTS_DIR, "combined_series.csv"), index=False)
    gen.visualize_series(
        df_combined,
        title="Combined 18/82 and π-Related Patterns",
        save_path=os.path.join(RESULTS_DIR, "combined_series.png")
    )

    # Generate multi-scale series
    multi_scale = gen.generate_multi_scale_series()
    for scale, df in multi_scale.items():
        df.to_csv(os.path.join(RESULTS_DIR, f"multi_scale_{scale}_series.csv"), index=False)
        gen.visualize_series(
            df,
            title=f"18/82 Pattern at {scale} Scale",
            save_path=os.path.join(RESULTS_DIR, f"multi_scale_{scale}_series.png")
        )

    logger.info("Example datasets generated successfully")

if __name__ == "__main__":
    generate_example_datasets()
