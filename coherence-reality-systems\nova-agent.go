﻿package main

import (
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "time"
    "github.com/gorilla/websocket"
)

type AgentStatus struct {
    Status      string  `json:"status"`
    Version     string  `json:"version"`
    Coherence   float64 `json:"coherence"`
    Uptime      string  `json:"uptime"`
    PsiSnap     bool    `json:"psi_snap"`
    Timestamp   string  `json:"timestamp"`
}

var startTime = time.Now()
var upgrader = websocket.Upgrader{
    CheckOrigin: func(r *http.Request) bool {
        return true // Allow all origins for development
    },
}

type Command struct {
    Type    string      `json:"type"`
    Payload interface{} `json:"payload"`
}

type CommandResponse struct {
    Success bool        `json:"success"`
    Message string      `json:"message"`
    Data    interface{} `json:"data"`
}

func main() {
    fmt.Println("🚀 Nova Agent Booted Successfully!")
    fmt.Println("NovaFuse Coherence Operating System")
    fmt.Println("Version: 1.0.0")
    fmt.Println("Status: Ready for Integration")
    fmt.Println("API Server: Starting on port 8090...")

    go startAPIServer()

    // Keep main thread alive
    select {}
}

func startAPIServer() {
    http.HandleFunc("/status", statusHandler)
    http.HandleFunc("/coherence", coherenceHandler)
    http.HandleFunc("/health", healthHandler)
    http.HandleFunc("/ws", websocketHandler)
    http.HandleFunc("/command", commandHandler)
    // Add missing relations endpoint for NovaBrowser integration
    http.HandleFunc("/api/v1/coherence/relations", relationsHandler)

    fmt.Println("✅ API Server running on http://localhost:8090")
    fmt.Println("✅ WebSocket available at ws://localhost:8090/ws")
    log.Fatal(http.ListenAndServe(":8090", enableCORS(http.DefaultServeMux)))
}

func statusHandler(w http.ResponseWriter, r *http.Request) {
    status := AgentStatus{
        Status:      "coherent",
        Version:     "1.0.0",
        Coherence:   calculateCoherence(),
        Uptime:      time.Since(startTime).String(),
        PsiSnap:     checkPsiSnap(),
        Timestamp:   time.Now().Format(time.RFC3339),
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(status)
}

func coherenceHandler(w http.ResponseWriter, r *http.Request) {
    coherence := map[string]interface{}{
        "psi_level":     calculateCoherence(),
        "snap_point":    0.82,
        "current_phase": getCurrentPhase(),
        "metrics": map[string]float64{
            "structural_coherence":  0.85,
            "functional_alignment":  0.78,
            "relational_integrity":  0.91,
        },
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(coherence)
}

func healthHandler(w http.ResponseWriter, r *http.Request) {
    health := map[string]string{
        "status": "healthy",
        "service": "nova-agent",
        "timestamp": time.Now().Format(time.RFC3339),
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(health)
}

func relationsHandler(w http.ResponseWriter, r *http.Request) {
    relations := map[string]interface{}{
        "status": "active",
        "endpoints": []string{"/validate", "/scan"},
        "relational_coherence": map[string]float64{
            "api_connectivity": 1.0,
            "endpoint_health": 0.95,
            "data_flow": 0.88,
        },
        "active_connections": 3,
        "timestamp": time.Now().Format(time.RFC3339),
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(relations)
}

func calculateCoherence() float64 {
    // Simulate coherence calculation based on golden ratio
    baseCoherence := 0.618 // Golden ratio base
    timeBonus := float64(time.Since(startTime).Minutes()) * 0.001
    return baseCoherence + timeBonus
}

func checkPsiSnap() bool {
    return calculateCoherence() >= 0.82
}

func getCurrentPhase() string {
    coherence := calculateCoherence()
    if coherence >= 0.82 {
        return "externalization"
    }
    return "internal_coherence"
}

func enableCORS(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        w.Header().Set("Access-Control-Allow-Origin", "*")
        w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
        w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

        if r.Method == "OPTIONS" {
            w.WriteHeader(http.StatusOK)
            return
        }

        next.ServeHTTP(w, r)
    })
}

func websocketHandler(w http.ResponseWriter, r *http.Request) {
    conn, err := upgrader.Upgrade(w, r, nil)
    if err != nil {
        log.Printf("WebSocket upgrade failed: %v", err)
        return
    }
    defer conn.Close()

    fmt.Println("🔌 WebSocket client connected")

    // Send initial status
    status := AgentStatus{
        Status:      "coherent",
        Version:     "1.0.0",
        Coherence:   calculateCoherence(),
        Uptime:      time.Since(startTime).String(),
        PsiSnap:     checkPsiSnap(),
        Timestamp:   time.Now().Format(time.RFC3339),
    }

    if err := conn.WriteJSON(status); err != nil {
        log.Printf("WebSocket write error: %v", err)
        return
    }

    // Listen for commands
    for {
        var cmd Command
        err := conn.ReadJSON(&cmd)
        if err != nil {
            log.Printf("WebSocket read error: %v", err)
            break
        }

        response := processCommand(cmd)
        if err := conn.WriteJSON(response); err != nil {
            log.Printf("WebSocket write error: %v", err)
            break
        }
    }
}

func commandHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != "POST" {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    var cmd Command
    if err := json.NewDecoder(r.Body).Decode(&cmd); err != nil {
        http.Error(w, "Invalid JSON", http.StatusBadRequest)
        return
    }

    response := processCommand(cmd)

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(response)
}

func processCommand(cmd Command) CommandResponse {
    fmt.Printf("📨 Processing command: %s\n", cmd.Type)

    switch cmd.Type {
    case "restart_service":
        return CommandResponse{
            Success: true,
            Message: "Service restart initiated",
            Data:    map[string]string{"status": "restarting"},
        }
    case "scan_vendor":
        return CommandResponse{
            Success: true,
            Message: "Vendor scan completed",
            Data:    map[string]interface{}{
                "vendors_found": 3,
                "scan_time": "2.3s",
            },
        }
    case "update_module":
        return CommandResponse{
            Success: true,
            Message: "Module update successful",
            Data:    map[string]string{"version": "1.0.1"},
        }
    case "get_logs":
        return CommandResponse{
            Success: true,
            Message: "Logs retrieved",
            Data: []string{
                "2024-01-01 12:00:00 - Nova Agent started",
                "2024-01-01 12:00:01 - API server initialized",
                "2024-01-01 12:00:02 - Coherence engine online",
            },
        }
    default:
        return CommandResponse{
            Success: false,
            Message: "Unknown command type",
            Data:    nil,
        }
    }
}
