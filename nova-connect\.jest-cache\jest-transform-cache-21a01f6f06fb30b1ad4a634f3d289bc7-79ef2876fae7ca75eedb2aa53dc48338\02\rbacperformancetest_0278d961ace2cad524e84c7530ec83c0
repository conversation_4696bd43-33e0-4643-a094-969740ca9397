6d215c44eb4b71f3ea65af423bc89268
/**
 * RBAC Performance Tests
 * 
 * This file contains performance tests for the RBAC system, focusing on caching.
 */

const mongoose = require('mongoose');
const RBACService = require('../../api/services/RBACService');
const CacheService = require('../../api/services/CacheService');
const {
  setupTestEnvironment,
  clearDatabase,
  disconnectFromDatabase,
  getTestData
} = require('../setup/rbac-test-setup');

// Initialize services
const rbacService = new RBACService();

// Test data
let adminUser;
let regularUser;
let viewerUser;
let testRole;
let testPermission;

// Setup and teardown
beforeAll(async () => {
  try {
    const testData = await setupTestEnvironment();
    adminUser = testData.adminUser;
    regularUser = testData.regularUser;
    viewerUser = testData.viewerUser;
    testRole = testData.testRole;
    testPermission = testData.testPermission;
  } catch (error) {
    console.error('Error in beforeAll:', error);
    throw error;
  }
});
afterAll(async () => {
  await disconnectFromDatabase();
});

// Helper function to measure execution time
const measureExecutionTime = async fn => {
  const start = process.hrtime.bigint();
  const result = await fn();
  const end = process.hrtime.bigint();
  const duration = Number(end - start) / 1_000_000; // Convert to milliseconds
  return {
    result,
    duration
  };
};

// Test suites
describe('RBAC Performance', () => {
  describe('Permission Caching', () => {
    test('hasPermission should be faster with caching', async () => {
      // Clear cache before test
      await CacheService.flush('rbac');

      // First call (no cache)
      const {
        duration: firstDuration
      } = await measureExecutionTime(() => rbacService.hasPermission(regularUser._id, 'resource:view'));

      // Second call (with cache)
      const {
        duration: secondDuration
      } = await measureExecutionTime(() => rbacService.hasPermission(regularUser._id, 'resource:view'));
      console.log(`First call (no cache): ${firstDuration.toFixed(2)}ms`);
      console.log(`Second call (with cache): ${secondDuration.toFixed(2)}ms`);

      // The second call should be significantly faster
      expect(secondDuration).toBeLessThan(firstDuration * 0.5);
    });
    test('getUserPermissions should be faster with caching', async () => {
      // Clear cache before test
      await CacheService.flush('rbac');

      // First call (no cache)
      const {
        duration: firstDuration
      } = await measureExecutionTime(() => rbacService.getUserPermissions(regularUser._id));

      // Second call (with cache)
      const {
        duration: secondDuration
      } = await measureExecutionTime(() => rbacService.getUserPermissions(regularUser._id));
      console.log(`First call (no cache): ${firstDuration.toFixed(2)}ms`);
      console.log(`Second call (with cache): ${secondDuration.toFixed(2)}ms`);

      // The second call should be significantly faster
      expect(secondDuration).toBeLessThan(firstDuration * 0.5);
    });
    test('getAllRoles should be faster with caching', async () => {
      // Clear cache before test
      await CacheService.flush('rbac');

      // First call (no cache)
      const {
        duration: firstDuration
      } = await measureExecutionTime(() => rbacService.getAllRoles());

      // Second call (with cache)
      const {
        duration: secondDuration
      } = await measureExecutionTime(() => rbacService.getAllRoles());
      console.log(`First call (no cache): ${firstDuration.toFixed(2)}ms`);
      console.log(`Second call (with cache): ${secondDuration.toFixed(2)}ms`);

      // The second call should be significantly faster
      expect(secondDuration).toBeLessThan(firstDuration * 0.5);
    });
    test('getAllPermissions should be faster with caching', async () => {
      // Clear cache before test
      await CacheService.flush('rbac');

      // First call (no cache)
      const {
        duration: firstDuration
      } = await measureExecutionTime(() => rbacService.getAllPermissions());

      // Second call (with cache)
      const {
        duration: secondDuration
      } = await measureExecutionTime(() => rbacService.getAllPermissions());
      console.log(`First call (no cache): ${firstDuration.toFixed(2)}ms`);
      console.log(`Second call (with cache): ${secondDuration.toFixed(2)}ms`);

      // The second call should be significantly faster
      expect(secondDuration).toBeLessThan(firstDuration * 0.5);
    });
  });
  describe('Cache Invalidation', () => {
    test('createRole should invalidate role cache', async () => {
      // First, populate the cache
      await rbacService.getAllRoles();

      // Create a new role
      const newRole = {
        name: 'Cache Test Role',
        description: 'Role for testing cache invalidation',
        permissions: [testPermission._id]
      };
      await rbacService.createRole(newRole);

      // Get roles again and verify the new role is included
      const roles = await rbacService.getAllRoles();
      const hasNewRole = roles.some(role => role.name === newRole.name);
      expect(hasNewRole).toBe(true);

      // Clean up
      const createdRole = roles.find(role => role.name === newRole.name);
      if (createdRole) {
        await rbacService.deleteRole(createdRole._id);
      }
    });
    test('assignRoleToUser should invalidate user permission cache', async () => {
      // First, populate the cache
      await rbacService.getUserPermissions(viewerUser._id);

      // Get initial permissions count
      const initialPermissions = await rbacService.getUserPermissions(viewerUser._id);
      const initialCount = initialPermissions.length;

      // Assign a role with more permissions
      await rbacService.assignRoleToUser(viewerUser._id, testRole._id);

      // Get permissions again and verify they've changed
      const updatedPermissions = await rbacService.getUserPermissions(viewerUser._id);
      const updatedCount = updatedPermissions.length;
      expect(updatedCount).toBeGreaterThan(initialCount);

      // Clean up
      await rbacService.removeRoleFromUser(viewerUser._id, testRole._id);
    });
  });
  describe('Performance Under Load', () => {
    test('hasPermission should handle multiple concurrent requests', async () => {
      // Clear cache before test
      await CacheService.flush('rbac');

      // Create an array of promises for concurrent permission checks
      const concurrentChecks = 100;
      const promises = [];
      for (let i = 0; i < concurrentChecks; i++) {
        promises.push(rbacService.hasPermission(regularUser._id, 'resource:view'));
      }

      // Measure execution time for all concurrent checks
      const {
        duration
      } = await measureExecutionTime(() => Promise.all(promises));
      console.log(`${concurrentChecks} concurrent permission checks: ${duration.toFixed(2)}ms`);
      console.log(`Average time per check: ${(duration / concurrentChecks).toFixed(2)}ms`);

      // The average time should be reasonable
      expect(duration / concurrentChecks).toBeLessThan(10); // Less than 10ms per check on average
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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