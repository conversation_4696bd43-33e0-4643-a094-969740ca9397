# ConsciousNovaFold User Guide

## Table of Contents
- [Installation](#installation)
- [Quick Start](#quick-start)
- [Configuration](#configuration)
- [Basic Usage](#basic-usage)
- [Advanced Features](#advanced-features)
- [Troubleshooting](#troubleshooting)

## Installation

### Prerequisites
- Python 3.8+
- pip (Python package manager)
- Git

### Step 1: <PERSON>lone the Repository
```bash
git clone https://github.com/your-org/consciousness-chemistry-engine.git
cd consciousness-chemistry-engine
```

### Step 2: Create a Virtual Environment (Recommended)
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### Step 3: Install Dependencies
```bash
pip install -r requirements.txt
```

## Quick Start

### Basic Protein Folding
```python
from ConsciousNovaFold import ConsciousNovaFold, NovaFoldClient

# Initialize the client and folder
novafold = NovaFoldClient()
conscious_folder = ConsciousNovaFold(novafold)

# Fold a protein sequence
sequence = "ACDEFGHIKLMNPQRSTVWY"
result = conscious_folder.fold(sequence)

# View results
print(f"Structure: {result['structure']}")
print(f"Metrics: {result['metrics']}")
```

## Configuration

### Configuration Files
Create a `config.yaml` file in your project root:

```yaml
novafold:
  model: 'default'
  num_models: 5
  
consciousness:
  enable_quantum_effects: true
  fibonacci_threshold: 0.7
  
output:
  save_reports: true
  report_dir: './reports'
  format: 'both'  # 'json', 'markdown', or 'both'
```

### Environment Variables
```bash
export NOVAFOLD_MODEL=default
export ENABLE_QUANTUM_EFFECTS=true
```

## Basic Usage

### Folding a Single Sequence
```python
result = conscious_folder.fold("ACDEFGHIKLMNPQRSTVWY")
```

### Batch Processing
```python
sequences = ["ACDEFGHIKL", "MNOPQRSTUV", "WXYZABCDEF"]
results = [conscious_folder.fold(seq) for seq in sequences]
```

### Saving Results
```python
# Save to file
conscious_folder.save_results(result, 'my_protein_results.json')

# Generate reports
conscious_folder.generate_reports(result, output_dir='./reports')
```

## Advanced Features

### Custom Metrics
```python
from metrics import CustomMetric

class MyCustomMetric:
    def calculate(self, structure):
        # Your custom calculation
        return {"custom_score": 0.95}

# Register custom metric
conscious_folder.add_metric('custom', MyCustomMetric())
```

### Parallel Processing
```python
from concurrent.futures import ThreadPoolExecutor

def process_sequence(seq):
    return conscious_folder.fold(seq)

with ThreadPoolExecutor(max_workers=4) as executor:
    results = list(executor.map(process_sequence, sequences))
```

## Troubleshooting

### Common Issues

#### Missing Dependencies
```bash
# If you encounter missing packages
pip install -r requirements.txt
```

#### Memory Issues
- Reduce `num_models` in config
- Use smaller batch sizes
- Increase system swap space

#### Performance Optimization
- Enable GPU acceleration if available
- Use smaller models for development
- Increase `max_workers` for batch processing

### Getting Help
- Check the [FAQ](faq.md)
- Open an issue on GitHub
- Join our community forum

## Next Steps
- Learn about [Advanced Configuration](advanced_configuration.md)
- Read the [API Reference](../api/)
- Explore [Example Notebooks](../examples/)
