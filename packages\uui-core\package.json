{"name": "@novafuse/uui-core", "version": "1.0.0", "description": "NovaFuse Universal UI Connector (UUIC) - Dynamic compliance-aware UI framework", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"build": "rollup -c", "test": "jest", "lint": "eslint src", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook"}, "keywords": ["novafuse", "ui", "compliance", "regulatory", "dynamic", "uuic"], "author": "NovaFuse", "license": "UNLICENSED", "private": true, "peerDependencies": {"react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "dependencies": {"eventemitter3": "^4.0.7", "uuid": "^8.3.2"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/preset-env": "^7.18.6", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^22.0.1", "@rollup/plugin-node-resolve": "^13.3.0", "@storybook/addon-actions": "^6.5.9", "@storybook/addon-essentials": "^6.5.9", "@storybook/addon-interactions": "^6.5.9", "@storybook/addon-links": "^6.5.9", "@storybook/builder-webpack5": "^6.5.9", "@storybook/manager-webpack5": "^6.5.9", "@storybook/react": "^6.5.9", "@storybook/testing-library": "^0.0.13", "@types/jest": "^28.1.4", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "@typescript-eslint/eslint-plugin": "^5.30.5", "@typescript-eslint/parser": "^5.30.5", "babel-loader": "^8.2.5", "eslint": "^8.19.0", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.5.13", "jest": "^28.1.2", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^2.75.7", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.32.1", "typescript": "^4.7.4"}}