/**
 * NovaFuse Privacy Management API Server
 *
 * This file initializes the server and connects to the database.
 */

const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const { connect } = require('./config/database');
const logger = require('./config/logger');
const { httpLogger, requestLogger, responseLogger, errorLogger } = require('./middleware/logger');
const { setupSwagger } = require('./config/swagger');
const privacyManagementApi = require('./index');

// Create Express app
const app = express();

// Apply middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(httpLogger);

// Add request start time
app.use((req, res, next) => {
  req.startTime = Date.now();
  next();
});

// Apply request and response logging
app.use(requestLogger);

// Set up Swagger documentation
setupSwagger(app);

// Mount the Privacy Management API
app.use('/api/privacy/management', privacyManagementApi);

// Apply response logging
app.use(responseLogger);

// Error handling for unhandled routes
app.use((req, res, next) => {
  const error = new Error(`Not Found - ${req.originalUrl}`);
  error.status = 404;
  next(error);
});

// Error logging
app.use(errorLogger);

// Global error handler
app.use((err, req, res, next) => {
  const statusCode = err.status || 500;
  res.status(statusCode).json({
    error: {
      message: err.message,
      status: statusCode,
      stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
    }
  });
});

// Start the server
const PORT = process.env.PORT || 3000;

const startServer = async () => {
  try {
    // Connect to MongoDB
    await connect();

    // Start the server
    app.listen(PORT, () => {
      logger.info(`Server running on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
    });
  } catch (error) {
    logger.error('Failed to start server', error);
    process.exit(1);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Promise Rejection:', err);
  // Close server & exit process
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  // Close server & exit process
  process.exit(1);
});

// Start the server
if (require.main === module) {
  startServer();
}

module.exports = {
  app,
  startServer
};
