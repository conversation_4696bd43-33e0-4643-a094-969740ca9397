const { v4: uuidv4 } = require('uuid');
const models = require('./models');

/**
 * Get a list of ESG targets
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getTargets = (req, res) => {
  try {
    const { page = 1, limit = 10, category, status, metricId, targetDateBefore, targetDateAfter, sortBy = 'targetDate', sortOrder = 'asc' } = req.query;
    
    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    
    // Filter targets based on query parameters
    let filteredTargets = [...models.esgTargets];
    
    if (category) {
      filteredTargets = filteredTargets.filter(target => target.category === category);
    }
    
    if (status) {
      filteredTargets = filteredTargets.filter(target => target.status === status);
    }
    
    if (metricId) {
      filteredTargets = filteredTargets.filter(target => target.metricId === metricId);
    }
    
    if (targetDateBefore) {
      filteredTargets = filteredTargets.filter(target => target.targetDate <= targetDateBefore);
    }
    
    if (targetDateAfter) {
      filteredTargets = filteredTargets.filter(target => target.targetDate >= targetDateAfter);
    }
    
    // Sort targets
    filteredTargets.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });
    
    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedTargets = filteredTargets.slice(startIndex, endIndex);
    
    // Calculate pagination info
    const totalTargets = filteredTargets.length;
    const totalPages = Math.ceil(totalTargets / limitNum);
    
    res.json({
      data: paginatedTargets,
      pagination: {
        total: totalTargets,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getTargets:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific ESG target by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getTargetById = (req, res) => {
  try {
    const { id } = req.params;
    const target = models.esgTargets.find(t => t.id === id);
    
    if (!target) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG target with ID ${id} not found`
      });
    }
    
    res.json({ data: target });
  } catch (error) {
    console.error('Error in getTargetById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new ESG target
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createTarget = (req, res) => {
  try {
    const { 
      name, 
      description, 
      category, 
      subcategory, 
      metricId, 
      targetValue, 
      baselineValue, 
      baselineDate, 
      targetDate, 
      unit, 
      status, 
      owner, 
      initiatives, 
      milestones 
    } = req.body;
    
    // Create a new target with a unique ID
    const newTarget = {
      id: `tgt-${uuidv4().substring(0, 8)}`,
      name,
      description,
      category,
      subcategory: subcategory || '',
      metricId: metricId || null,
      targetValue,
      baselineValue: baselineValue || null,
      baselineDate: baselineDate || null,
      targetDate,
      unit: unit || '',
      status,
      owner: owner || '',
      initiatives: initiatives || [],
      milestones: milestones ? milestones.map(milestone => ({
        id: `mil-${uuidv4().substring(0, 8)}`,
        ...milestone
      })) : [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Add the new target to the collection
    models.esgTargets.push(newTarget);
    
    res.status(201).json({
      data: newTarget,
      message: 'ESG target created successfully'
    });
  } catch (error) {
    console.error('Error in createTarget:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing ESG target
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateTarget = (req, res) => {
  try {
    const { id } = req.params;
    const { 
      name, 
      description, 
      category, 
      subcategory, 
      metricId, 
      targetValue, 
      baselineValue, 
      baselineDate, 
      targetDate, 
      unit, 
      status, 
      owner, 
      initiatives, 
      milestones 
    } = req.body;
    
    // Find the target to update
    const targetIndex = models.esgTargets.findIndex(t => t.id === id);
    
    if (targetIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG target with ID ${id} not found`
      });
    }
    
    const currentTarget = models.esgTargets[targetIndex];
    
    // Process milestones if provided
    let updatedMilestones = currentTarget.milestones;
    if (milestones) {
      updatedMilestones = milestones.map(milestone => {
        if (milestone.id) {
          // Update existing milestone
          const existingIndex = currentTarget.milestones.findIndex(m => m.id === milestone.id);
          if (existingIndex !== -1) {
            return {
              ...currentTarget.milestones[existingIndex],
              ...milestone
            };
          }
        }
        // Add new milestone
        return {
          id: `mil-${uuidv4().substring(0, 8)}`,
          ...milestone
        };
      });
    }
    
    // Update the target
    const updatedTarget = {
      ...currentTarget,
      name: name || currentTarget.name,
      description: description || currentTarget.description,
      category: category || currentTarget.category,
      subcategory: subcategory !== undefined ? subcategory : currentTarget.subcategory,
      metricId: metricId !== undefined ? metricId : currentTarget.metricId,
      targetValue: targetValue || currentTarget.targetValue,
      baselineValue: baselineValue !== undefined ? baselineValue : currentTarget.baselineValue,
      baselineDate: baselineDate !== undefined ? baselineDate : currentTarget.baselineDate,
      targetDate: targetDate || currentTarget.targetDate,
      unit: unit !== undefined ? unit : currentTarget.unit,
      status: status || currentTarget.status,
      owner: owner !== undefined ? owner : currentTarget.owner,
      initiatives: initiatives || currentTarget.initiatives,
      milestones: updatedMilestones,
      updatedAt: new Date().toISOString()
    };
    
    // Replace the old target with the updated one
    models.esgTargets[targetIndex] = updatedTarget;
    
    res.json({
      data: updatedTarget,
      message: 'ESG target updated successfully'
    });
  } catch (error) {
    console.error('Error in updateTarget:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete an ESG target
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteTarget = (req, res) => {
  try {
    const { id } = req.params;
    
    // Find the target to delete
    const targetIndex = models.esgTargets.findIndex(t => t.id === id);
    
    if (targetIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG target with ID ${id} not found`
      });
    }
    
    // Check if there are any progress records for this target
    const hasProgress = models.targetProgress.some(p => p.targetId === id);
    
    if (hasProgress) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot delete target with ID ${id} because it has associated progress records`
      });
    }
    
    // Remove the target from the collection
    models.esgTargets.splice(targetIndex, 1);
    
    res.json({
      message: 'ESG target deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteTarget:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get progress records for a specific target
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getTargetProgress = (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10, sortBy = 'date', sortOrder = 'desc' } = req.query;
    
    // Check if the target exists
    const target = models.esgTargets.find(t => t.id === id);
    
    if (!target) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG target with ID ${id} not found`
      });
    }
    
    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    
    // Get progress records for the target
    let progressRecords = models.targetProgress.filter(p => p.targetId === id);
    
    // Sort progress records
    progressRecords.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });
    
    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedRecords = progressRecords.slice(startIndex, endIndex);
    
    // Calculate pagination info
    const totalRecords = progressRecords.length;
    const totalPages = Math.ceil(totalRecords / limitNum);
    
    res.json({
      data: paginatedRecords,
      pagination: {
        total: totalRecords,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getTargetProgress:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Add a progress record to a target
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const addProgressRecord = (req, res) => {
  try {
    const { id } = req.params;
    const { date, value, percentComplete, notes } = req.body;
    
    // Check if the target exists
    const target = models.esgTargets.find(t => t.id === id);
    
    if (!target) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG target with ID ${id} not found`
      });
    }
    
    // Check if a progress record for this date already exists
    const existingRecord = models.targetProgress.find(p => p.targetId === id && p.date === date);
    
    if (existingRecord) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `A progress record for date ${date} already exists`
      });
    }
    
    // Create a new progress record
    const newRecord = {
      id: `prg-${uuidv4().substring(0, 8)}`,
      targetId: id,
      date,
      value,
      percentComplete: percentComplete || calculatePercentComplete(target, value),
      notes: notes || '',
      createdBy: req.user?.id || 'system', // Assuming user info is available in req.user
      createdAt: new Date().toISOString()
    };
    
    // Add the new record to the collection
    models.targetProgress.push(newRecord);
    
    // Update target status if needed
    updateTargetStatus(id);
    
    res.status(201).json({
      data: newRecord,
      message: 'Progress record added successfully'
    });
  } catch (error) {
    console.error('Error in addProgressRecord:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update a progress record
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateProgressRecord = (req, res) => {
  try {
    const { id, progressId } = req.params;
    const { date, value, percentComplete, notes } = req.body;
    
    // Check if the target exists
    const target = models.esgTargets.find(t => t.id === id);
    
    if (!target) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG target with ID ${id} not found`
      });
    }
    
    // Find the progress record to update
    const recordIndex = models.targetProgress.findIndex(p => p.id === progressId && p.targetId === id);
    
    if (recordIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Progress record with ID ${progressId} not found for target ${id}`
      });
    }
    
    const currentRecord = models.targetProgress[recordIndex];
    
    // If date is being updated, check if a record for the new date already exists
    if (date && date !== currentRecord.date) {
      const existingRecord = models.targetProgress.find(p => 
        p.targetId === id && p.date === date && p.id !== progressId
      );
      
      if (existingRecord) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `A progress record for date ${date} already exists`
        });
      }
    }
    
    // Calculate percent complete if value is provided but percentComplete is not
    let calculatedPercentComplete = percentComplete;
    if (value && percentComplete === undefined) {
      calculatedPercentComplete = calculatePercentComplete(target, value);
    }
    
    // Update the progress record
    const updatedRecord = {
      ...currentRecord,
      date: date || currentRecord.date,
      value: value || currentRecord.value,
      percentComplete: calculatedPercentComplete !== undefined ? calculatedPercentComplete : currentRecord.percentComplete,
      notes: notes !== undefined ? notes : currentRecord.notes
    };
    
    // Replace the old record with the updated one
    models.targetProgress[recordIndex] = updatedRecord;
    
    // Update target status if needed
    updateTargetStatus(id);
    
    res.json({
      data: updatedRecord,
      message: 'Progress record updated successfully'
    });
  } catch (error) {
    console.error('Error in updateProgressRecord:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a progress record
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteProgressRecord = (req, res) => {
  try {
    const { id, progressId } = req.params;
    
    // Check if the target exists
    const target = models.esgTargets.find(t => t.id === id);
    
    if (!target) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG target with ID ${id} not found`
      });
    }
    
    // Find the progress record to delete
    const recordIndex = models.targetProgress.findIndex(p => p.id === progressId && p.targetId === id);
    
    if (recordIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Progress record with ID ${progressId} not found for target ${id}`
      });
    }
    
    // Remove the progress record from the collection
    models.targetProgress.splice(recordIndex, 1);
    
    // Update target status if needed
    updateTargetStatus(id);
    
    res.json({
      message: 'Progress record deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteProgressRecord:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Calculate percent complete based on target and current value
 * @param {Object} target - The ESG target
 * @param {string} currentValue - The current value
 * @returns {number} - The calculated percent complete
 */
const calculatePercentComplete = (target, currentValue) => {
  // Convert values to numbers
  const targetValueNum = parseFloat(target.targetValue);
  const currentValueNum = parseFloat(currentValue);
  const baselineValueNum = target.baselineValue ? parseFloat(target.baselineValue) : 0;
  
  // If target is to increase a value
  if (targetValueNum > baselineValueNum) {
    if (currentValueNum >= targetValueNum) {
      return 100;
    }
    const range = targetValueNum - baselineValueNum;
    const progress = currentValueNum - baselineValueNum;
    return Math.round((progress / range) * 100);
  }
  // If target is to decrease a value
  else if (targetValueNum < baselineValueNum) {
    if (currentValueNum <= targetValueNum) {
      return 100;
    }
    const range = baselineValueNum - targetValueNum;
    const progress = baselineValueNum - currentValueNum;
    return Math.round((progress / range) * 100);
  }
  // If target is already met
  else {
    return 100;
  }
};

/**
 * Update target status based on progress
 * @param {string} targetId - The target ID
 */
const updateTargetStatus = (targetId) => {
  const targetIndex = models.esgTargets.findIndex(t => t.id === targetId);
  if (targetIndex === -1) return;
  
  const target = models.esgTargets[targetIndex];
  const progressRecords = models.targetProgress.filter(p => p.targetId === targetId);
  
  if (progressRecords.length === 0) return;
  
  // Sort progress records by date (newest first)
  progressRecords.sort((a, b) => new Date(b.date) - new Date(a.date));
  const latestRecord = progressRecords[0];
  
  // Check if target date has passed
  const now = new Date();
  const targetDate = new Date(target.targetDate);
  
  let newStatus = target.status;
  
  if (latestRecord.percentComplete >= 100) {
    newStatus = 'achieved';
  } else if (targetDate < now && target.status !== 'achieved') {
    newStatus = 'missed';
  } else if (target.status === 'planned' && latestRecord.percentComplete > 0) {
    newStatus = 'in-progress';
  }
  
  if (newStatus !== target.status) {
    models.esgTargets[targetIndex] = {
      ...target,
      status: newStatus,
      updatedAt: new Date().toISOString()
    };
  }
};

module.exports = {
  getTargets,
  getTargetById,
  createTarget,
  updateTarget,
  deleteTarget,
  getTargetProgress,
  addProgressRecord,
  updateProgressRecord,
  deleteProgressRecord
};
