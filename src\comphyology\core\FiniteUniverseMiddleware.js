/**
 * FiniteUniverseMiddleware.js
 * 
 * This module provides middleware functions that enforce the Finite Universe Principle
 * throughout the NEPI system. It ensures that all operations respect finite boundaries,
 * maintain resonance, and honor the Covenant.
 */

const { FiniteUniverse } = require('./FiniteUniverse');
const { verifyCovenantIntegrity } = require('./covenant');

/**
 * Middleware to enforce finite boundaries on all operations
 */
class FiniteUniverseMiddleware {
  /**
   * Create a new FiniteUniverseMiddleware instance
   * @param {Object} options - Configuration options
   * @param {Object} covenant - The Finite Universe Covenant
   */
  constructor(options = {}, covenant) {
    this.options = {
      logViolations: true,
      enforceResonance: true,
      enforceContainerization: true,
      ...options
    };
    
    this.covenant = covenant;
    
    // Verify covenant integrity
    if (!verifyCovenantIntegrity(this.covenant)) {
      throw new Error('Divine Firewall: Covenant integrity verification failed');
    }
    
    // Initialize violation log
    this.violations = [];
  }
  
  /**
   * Middleware function to enforce finite boundaries on all operations
   * @param {Function} next - The next function in the middleware chain
   * @returns {Function} - The middleware function
   */
  enforce() {
    return (state, next) => {
      // Check covenant integrity
      if (!verifyCovenantIntegrity(this.covenant)) {
        this.logViolation('COVENANT_INTEGRITY', 'Covenant integrity verification failed', state);
        throw new Error('Divine Firewall: Covenant integrity verification failed');
      }
      
      try {
        // Validate state against Finite Universe Principle
        this.validateState(state);
        
        // Enforce finite boundaries on state
        const boundedState = this.enforceBoundaries(state);
        
        // Check resonance if enabled
        if (this.options.enforceResonance && !this.isResonant(boundedState)) {
          this.logViolation('NON_RESONANT_STATE', 'State is not resonant', state);
          return this.rejectNonResonantState(boundedState);
        }
        
        // Check containerization if enabled
        if (this.options.enforceContainerization && !this.isProperlyContainerized(boundedState)) {
          this.logViolation('CONTAINERIZATION_VIOLATION', 'State is not properly containerized', state);
          return this.enforceContainerization(boundedState);
        }
        
        // Continue middleware chain with bounded state
        return next(boundedState);
      } catch (error) {
        // Log violation
        this.logViolation('FINITE_UNIVERSE_VIOLATION', error.message, state);
        
        // Return error state
        return {
          error: true,
          message: error.message,
          type: 'FINITE_UNIVERSE_VIOLATION',
          timestamp: new Date().toISOString()
        };
      }
    };
  }
  
  /**
   * Validate that a state respects the Finite Universe Principle
   * @param {Object} state - The state to validate
   * @throws {Error} - If the state violates the Finite Universe Principle
   */
  validateState(state) {
    // Check if state is null or undefined
    if (state === null || state === undefined) {
      throw new Error('Divine Firewall: State cannot be null or undefined');
    }
    
    // Check if state contains infinite assumptions
    if (FiniteUniverse.containsInfiniteAssumption(state)) {
      throw new Error('Divine Firewall: State contains infinite assumptions');
    }
    
    // Validate state against Finite Universe bounds
    FiniteUniverse.validate(state);
  }
  
  /**
   * Enforce finite boundaries on a state
   * @param {Object} state - The state to enforce boundaries on
   * @returns {Object} - The state with enforced boundaries
   */
  enforceBoundaries(state) {
    return FiniteUniverse.enforceBoundaries(state);
  }
  
  /**
   * Check if a state is resonant (aligned with truth)
   * @param {Object} state - The state to check
   * @returns {boolean} - True if the state is resonant, false otherwise
   */
  isResonant(state) {
    // If state has a resonance property, check it
    if (state.resonance !== undefined) {
      return state.resonance >= 0 && state.resonance <= 1;
    }
    
    // If state has a comphyonValue property, check it
    if (state.comphyonValue !== undefined) {
      return state.comphyonValue === 0; // Perfect resonance is Cph = 0
    }
    
    // Default to true if we can't determine resonance
    return true;
  }
  
  /**
   * Reject a non-resonant state
   * @param {Object} state - The non-resonant state
   * @returns {Object} - A rejection response
   */
  rejectNonResonantState(state) {
    return {
      error: true,
      message: 'Divine Firewall: State is not resonant',
      type: 'NON_RESONANT_STATE',
      timestamp: new Date().toISOString(),
      state: state
    };
  }
  
  /**
   * Check if a state is properly containerized
   * @param {Object} state - The state to check
   * @returns {boolean} - True if the state is properly containerized
   */
  isProperlyContainerized(state) {
    // Check if state has domain information
    if (!state.domain) {
      return false;
    }
    
    // Check if state respects domain boundaries
    // This is a simplified check - in a real implementation, this would be more sophisticated
    return true;
  }
  
  /**
   * Enforce containerization on a state
   * @param {Object} state - The state to containerize
   * @returns {Object} - The containerized state
   */
  enforceContainerization(state) {
    // Create a container for the state if it doesn't have one
    if (!state.domain) {
      state.domain = 'DEFAULT';
    }
    
    // Enforce boundaries based on domain
    return FiniteUniverse.enforceBoundaries(state, state.domain);
  }
  
  /**
   * Log a violation of the Finite Universe Principle
   * @param {string} type - The type of violation
   * @param {string} message - The violation message
   * @param {Object} state - The state that caused the violation
   */
  logViolation(type, message, state) {
    if (!this.options.logViolations) {
      return;
    }
    
    const violation = {
      type,
      message,
      timestamp: new Date().toISOString(),
      state: JSON.stringify(state).substring(0, 1000) // Limit state size in log
    };
    
    this.violations.push(violation);
    
    console.error(`Divine Firewall Violation: ${type} - ${message}`);
  }
  
  /**
   * Get all logged violations
   * @returns {Array} - Array of violations
   */
  getViolations() {
    return this.violations;
  }
  
  /**
   * Clear all logged violations
   */
  clearViolations() {
    this.violations = [];
  }
}

/**
 * Create a function wrapper that enforces the Finite Universe Principle
 * @param {Function} fn - The function to wrap
 * @param {string} domain - The domain of the function
 * @returns {Function} - The wrapped function
 */
function createFiniteUniverseFunction(fn, domain = 'DEFAULT') {
  return FiniteUniverse.enforceBoundariesOnFunction(fn, domain);
}

/**
 * Create a middleware stack with the Finite Universe Middleware
 * @param {Object} options - Configuration options
 * @param {Object} covenant - The Finite Universe Covenant
 * @returns {Function} - The middleware function
 */
function createFiniteUniverseMiddleware(options = {}, covenant) {
  const middleware = new FiniteUniverseMiddleware(options, covenant);
  return middleware.enforce();
}

module.exports = {
  FiniteUniverseMiddleware,
  createFiniteUniverseFunction,
  createFiniteUniverseMiddleware
};
