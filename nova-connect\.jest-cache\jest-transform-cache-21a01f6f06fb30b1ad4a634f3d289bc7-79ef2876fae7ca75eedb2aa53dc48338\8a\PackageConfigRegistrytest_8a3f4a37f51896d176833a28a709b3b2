1922a3ca7a0c91219c94121b6cc0f13c
/**
 * Package Configuration Registry Tests
 */

const PackageConfigRegistry = require('../../../api/services/PackageConfigRegistry');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');
describe('PackageConfigRegistry', () => {
  let packageRegistry;
  let testDir;
  beforeEach(async () => {
    // Create a temporary directory for testing
    testDir = path.join(os.tmpdir(), `nova-connect-test-${Date.now()}`);
    await fs.mkdir(testDir, {
      recursive: true
    });

    // Initialize the package registry with the test directory
    packageRegistry = new PackageConfigRegistry(testDir);

    // Wait for the data directory to be created
    await new Promise(resolve => setTimeout(resolve, 100));
  });
  afterEach(async () => {
    // Clean up the test directory
    try {
      await fs.rm(testDir, {
        recursive: true,
        force: true
      });
    } catch (error) {
      console.error('Error cleaning up test directory:', error);
    }
  });
  test('should initialize with default packages', async () => {
    const packages = await packageRegistry.getAllPackages();
    expect(packages).toHaveLength(4);
    expect(packages[0].id).toBe('core');
    expect(packages[1].id).toBe('secure');
    expect(packages[2].id).toBe('enterprise');
    expect(packages[3].id).toBe('ai_boost');
  });
  test('should get package by ID', async () => {
    const pkg = await packageRegistry.getPackageById('core');
    expect(pkg).toBeDefined();
    expect(pkg.id).toBe('core');
    expect(pkg.name).toBe('NovaConnect Core');
    expect(pkg.features).toContain('core.basic_connectors');
  });
  test('should create a new package', async () => {
    const newPackage = {
      id: 'test-package',
      name: 'Test Package',
      description: 'A test package',
      tier: 'test',
      features: ['test.feature1', 'test.feature2'],
      limits: {
        connections: 5,
        operations_per_day: 500
      }
    };
    await packageRegistry.createPackage(newPackage);
    const pkg = await packageRegistry.getPackageById('test-package');
    expect(pkg).toBeDefined();
    expect(pkg.id).toBe('test-package');
    expect(pkg.name).toBe('Test Package');
    expect(pkg.features).toContain('test.feature1');
  });
  test('should update a package', async () => {
    const updateData = {
      name: 'Updated Core Package',
      description: 'Updated description'
    };
    await packageRegistry.updatePackage('core', updateData);
    const pkg = await packageRegistry.getPackageById('core');
    expect(pkg).toBeDefined();
    expect(pkg.name).toBe('Updated Core Package');
    expect(pkg.description).toBe('Updated description');
  });
  test('should delete a package', async () => {
    await packageRegistry.deletePackage('core');
    await expect(packageRegistry.getPackageById('core')).rejects.toThrow('Package with ID core not found');
  });
  test('should set and get tenant mapping', async () => {
    await packageRegistry.setTenantMapping('test-tenant', 'enterprise', ['custom.feature1'], {
      connections: 200
    });
    const mapping = await packageRegistry.getTenantMapping('test-tenant');
    expect(mapping).toBeDefined();
    expect(mapping.tenantId).toBe('test-tenant');
    expect(mapping.packageId).toBe('enterprise');
    expect(mapping.customFeatures).toContain('custom.feature1');
    expect(mapping.customLimits.connections).toBe(200);
  });
  test('should check if tenant has feature access', async () => {
    // Set tenant mapping
    await packageRegistry.setTenantMapping('test-tenant', 'secure', ['custom.feature1'], {});

    // Check access to package feature
    const hasAccessToPackageFeature = await packageRegistry.hasTenantFeatureAccess('test-tenant', 'security.encryption');
    expect(hasAccessToPackageFeature).toBe(true);

    // Check access to custom feature
    const hasAccessToCustomFeature = await packageRegistry.hasTenantFeatureAccess('test-tenant', 'custom.feature1');
    expect(hasAccessToCustomFeature).toBe(true);

    // Check access to feature not in package or custom features
    const hasAccessToOtherFeature = await packageRegistry.hasTenantFeatureAccess('test-tenant', 'enterprise.advanced_connectors');
    expect(hasAccessToOtherFeature).toBe(false);
  });
  test('should get tenant feature limit', async () => {
    // Set tenant mapping
    await packageRegistry.setTenantMapping('test-tenant', 'secure', [], {
      connections: 200
    });

    // Check custom limit
    const customLimit = await packageRegistry.getTenantFeatureLimit('test-tenant', 'connections');
    expect(customLimit).toBe(200);

    // Check package limit
    const packageLimit = await packageRegistry.getTenantFeatureLimit('test-tenant', 'operations_per_day');
    expect(packageLimit).toBe(5000);

    // Check non-existent limit
    const nonExistentLimit = await packageRegistry.getTenantFeatureLimit('test-tenant', 'non_existent_limit');
    expect(nonExistentLimit).toBeNull();
  });
  test('should get tenant available features', async () => {
    // Set tenant mapping
    await packageRegistry.setTenantMapping('test-tenant', 'secure', ['custom.feature1'], {});

    // Get available features
    const features = await packageRegistry.getTenantAvailableFeatures('test-tenant');
    expect(features).toContain('core.basic_connectors');
    expect(features).toContain('security.encryption');
    expect(features).toContain('custom.feature1');
    expect(features).not.toContain('enterprise.advanced_connectors');
  });
  test('should use cache for repeated calls', async () => {
    // Mock the loadData method to track calls
    const originalLoadData = packageRegistry.loadData;
    const loadDataMock = jest.fn().mockImplementation(originalLoadData);
    packageRegistry.loadData = loadDataMock;

    // First call should hit the file system
    await packageRegistry.getAllPackages();
    expect(loadDataMock).toHaveBeenCalledTimes(1);

    // Second call should use cache
    await packageRegistry.getAllPackages();
    expect(loadDataMock).toHaveBeenCalledTimes(1);

    // Clear cache
    packageRegistry.clearCache();

    // After clearing cache, should hit file system again
    await packageRegistry.getAllPackages();
    expect(loadDataMock).toHaveBeenCalledTimes(2);

    // Restore original method
    packageRegistry.loadData = originalLoadData;
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJQYWNrYWdlQ29uZmlnUmVnaXN0cnkiLCJyZXF1aXJlIiwiZnMiLCJwcm9taXNlcyIsInBhdGgiLCJvcyIsImRlc2NyaWJlIiwicGFja2FnZVJlZ2lzdHJ5IiwidGVzdERpciIsImJlZm9yZUVhY2giLCJqb2luIiwidG1wZGlyIiwiRGF0ZSIsIm5vdyIsIm1rZGlyIiwicmVjdXJzaXZlIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiYWZ0ZXJFYWNoIiwicm0iLCJmb3JjZSIsImVycm9yIiwiY29uc29sZSIsInRlc3QiLCJwYWNrYWdlcyIsImdldEFsbFBhY2thZ2VzIiwiZXhwZWN0IiwidG9IYXZlTGVuZ3RoIiwiaWQiLCJ0b0JlIiwicGtnIiwiZ2V0UGFja2FnZUJ5SWQiLCJ0b0JlRGVmaW5lZCIsIm5hbWUiLCJmZWF0dXJlcyIsInRvQ29udGFpbiIsIm5ld1BhY2thZ2UiLCJkZXNjcmlwdGlvbiIsInRpZXIiLCJsaW1pdHMiLCJjb25uZWN0aW9ucyIsIm9wZXJhdGlvbnNfcGVyX2RheSIsImNyZWF0ZVBhY2thZ2UiLCJ1cGRhdGVEYXRhIiwidXBkYXRlUGFja2FnZSIsImRlbGV0ZVBhY2thZ2UiLCJyZWplY3RzIiwidG9UaHJvdyIsInNldFRlbmFudE1hcHBpbmciLCJtYXBwaW5nIiwiZ2V0VGVuYW50TWFwcGluZyIsInRlbmFudElkIiwicGFja2FnZUlkIiwiY3VzdG9tRmVhdHVyZXMiLCJjdXN0b21MaW1pdHMiLCJoYXNBY2Nlc3NUb1BhY2thZ2VGZWF0dXJlIiwiaGFzVGVuYW50RmVhdHVyZUFjY2VzcyIsImhhc0FjY2Vzc1RvQ3VzdG9tRmVhdHVyZSIsImhhc0FjY2Vzc1RvT3RoZXJGZWF0dXJlIiwiY3VzdG9tTGltaXQiLCJnZXRUZW5hbnRGZWF0dXJlTGltaXQiLCJwYWNrYWdlTGltaXQiLCJub25FeGlzdGVudExpbWl0IiwidG9CZU51bGwiLCJnZXRUZW5hbnRBdmFpbGFibGVGZWF0dXJlcyIsIm5vdCIsIm9yaWdpbmFsTG9hZERhdGEiLCJsb2FkRGF0YSIsImxvYWREYXRhTW9jayIsImplc3QiLCJmbiIsIm1vY2tJbXBsZW1lbnRhdGlvbiIsInRvSGF2ZUJlZW5DYWxsZWRUaW1lcyIsImNsZWFyQ2FjaGUiXSwic291cmNlcyI6WyJQYWNrYWdlQ29uZmlnUmVnaXN0cnkudGVzdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFBhY2thZ2UgQ29uZmlndXJhdGlvbiBSZWdpc3RyeSBUZXN0c1xuICovXG5cbmNvbnN0IFBhY2thZ2VDb25maWdSZWdpc3RyeSA9IHJlcXVpcmUoJy4uLy4uLy4uL2FwaS9zZXJ2aWNlcy9QYWNrYWdlQ29uZmlnUmVnaXN0cnknKTtcbmNvbnN0IGZzID0gcmVxdWlyZSgnZnMnKS5wcm9taXNlcztcbmNvbnN0IHBhdGggPSByZXF1aXJlKCdwYXRoJyk7XG5jb25zdCBvcyA9IHJlcXVpcmUoJ29zJyk7XG5cbmRlc2NyaWJlKCdQYWNrYWdlQ29uZmlnUmVnaXN0cnknLCAoKSA9PiB7XG4gIGxldCBwYWNrYWdlUmVnaXN0cnk7XG4gIGxldCB0ZXN0RGlyO1xuICBcbiAgYmVmb3JlRWFjaChhc3luYyAoKSA9PiB7XG4gICAgLy8gQ3JlYXRlIGEgdGVtcG9yYXJ5IGRpcmVjdG9yeSBmb3IgdGVzdGluZ1xuICAgIHRlc3REaXIgPSBwYXRoLmpvaW4ob3MudG1wZGlyKCksIGBub3ZhLWNvbm5lY3QtdGVzdC0ke0RhdGUubm93KCl9YCk7XG4gICAgYXdhaXQgZnMubWtkaXIodGVzdERpciwgeyByZWN1cnNpdmU6IHRydWUgfSk7XG4gICAgXG4gICAgLy8gSW5pdGlhbGl6ZSB0aGUgcGFja2FnZSByZWdpc3RyeSB3aXRoIHRoZSB0ZXN0IGRpcmVjdG9yeVxuICAgIHBhY2thZ2VSZWdpc3RyeSA9IG5ldyBQYWNrYWdlQ29uZmlnUmVnaXN0cnkodGVzdERpcik7XG4gICAgXG4gICAgLy8gV2FpdCBmb3IgdGhlIGRhdGEgZGlyZWN0b3J5IHRvIGJlIGNyZWF0ZWRcbiAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwKSk7XG4gIH0pO1xuICBcbiAgYWZ0ZXJFYWNoKGFzeW5jICgpID0+IHtcbiAgICAvLyBDbGVhbiB1cCB0aGUgdGVzdCBkaXJlY3RvcnlcbiAgICB0cnkge1xuICAgICAgYXdhaXQgZnMucm0odGVzdERpciwgeyByZWN1cnNpdmU6IHRydWUsIGZvcmNlOiB0cnVlIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjbGVhbmluZyB1cCB0ZXN0IGRpcmVjdG9yeTonLCBlcnJvcik7XG4gICAgfVxuICB9KTtcbiAgXG4gIHRlc3QoJ3Nob3VsZCBpbml0aWFsaXplIHdpdGggZGVmYXVsdCBwYWNrYWdlcycsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBwYWNrYWdlcyA9IGF3YWl0IHBhY2thZ2VSZWdpc3RyeS5nZXRBbGxQYWNrYWdlcygpO1xuICAgIFxuICAgIGV4cGVjdChwYWNrYWdlcykudG9IYXZlTGVuZ3RoKDQpO1xuICAgIGV4cGVjdChwYWNrYWdlc1swXS5pZCkudG9CZSgnY29yZScpO1xuICAgIGV4cGVjdChwYWNrYWdlc1sxXS5pZCkudG9CZSgnc2VjdXJlJyk7XG4gICAgZXhwZWN0KHBhY2thZ2VzWzJdLmlkKS50b0JlKCdlbnRlcnByaXNlJyk7XG4gICAgZXhwZWN0KHBhY2thZ2VzWzNdLmlkKS50b0JlKCdhaV9ib29zdCcpO1xuICB9KTtcbiAgXG4gIHRlc3QoJ3Nob3VsZCBnZXQgcGFja2FnZSBieSBJRCcsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBwa2cgPSBhd2FpdCBwYWNrYWdlUmVnaXN0cnkuZ2V0UGFja2FnZUJ5SWQoJ2NvcmUnKTtcbiAgICBcbiAgICBleHBlY3QocGtnKS50b0JlRGVmaW5lZCgpO1xuICAgIGV4cGVjdChwa2cuaWQpLnRvQmUoJ2NvcmUnKTtcbiAgICBleHBlY3QocGtnLm5hbWUpLnRvQmUoJ05vdmFDb25uZWN0IENvcmUnKTtcbiAgICBleHBlY3QocGtnLmZlYXR1cmVzKS50b0NvbnRhaW4oJ2NvcmUuYmFzaWNfY29ubmVjdG9ycycpO1xuICB9KTtcbiAgXG4gIHRlc3QoJ3Nob3VsZCBjcmVhdGUgYSBuZXcgcGFja2FnZScsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBuZXdQYWNrYWdlID0ge1xuICAgICAgaWQ6ICd0ZXN0LXBhY2thZ2UnLFxuICAgICAgbmFtZTogJ1Rlc3QgUGFja2FnZScsXG4gICAgICBkZXNjcmlwdGlvbjogJ0EgdGVzdCBwYWNrYWdlJyxcbiAgICAgIHRpZXI6ICd0ZXN0JyxcbiAgICAgIGZlYXR1cmVzOiBbJ3Rlc3QuZmVhdHVyZTEnLCAndGVzdC5mZWF0dXJlMiddLFxuICAgICAgbGltaXRzOiB7XG4gICAgICAgIGNvbm5lY3Rpb25zOiA1LFxuICAgICAgICBvcGVyYXRpb25zX3Blcl9kYXk6IDUwMFxuICAgICAgfVxuICAgIH07XG4gICAgXG4gICAgYXdhaXQgcGFja2FnZVJlZ2lzdHJ5LmNyZWF0ZVBhY2thZ2UobmV3UGFja2FnZSk7XG4gICAgXG4gICAgY29uc3QgcGtnID0gYXdhaXQgcGFja2FnZVJlZ2lzdHJ5LmdldFBhY2thZ2VCeUlkKCd0ZXN0LXBhY2thZ2UnKTtcbiAgICBcbiAgICBleHBlY3QocGtnKS50b0JlRGVmaW5lZCgpO1xuICAgIGV4cGVjdChwa2cuaWQpLnRvQmUoJ3Rlc3QtcGFja2FnZScpO1xuICAgIGV4cGVjdChwa2cubmFtZSkudG9CZSgnVGVzdCBQYWNrYWdlJyk7XG4gICAgZXhwZWN0KHBrZy5mZWF0dXJlcykudG9Db250YWluKCd0ZXN0LmZlYXR1cmUxJyk7XG4gIH0pO1xuICBcbiAgdGVzdCgnc2hvdWxkIHVwZGF0ZSBhIHBhY2thZ2UnLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlRGF0YSA9IHtcbiAgICAgIG5hbWU6ICdVcGRhdGVkIENvcmUgUGFja2FnZScsXG4gICAgICBkZXNjcmlwdGlvbjogJ1VwZGF0ZWQgZGVzY3JpcHRpb24nXG4gICAgfTtcbiAgICBcbiAgICBhd2FpdCBwYWNrYWdlUmVnaXN0cnkudXBkYXRlUGFja2FnZSgnY29yZScsIHVwZGF0ZURhdGEpO1xuICAgIFxuICAgIGNvbnN0IHBrZyA9IGF3YWl0IHBhY2thZ2VSZWdpc3RyeS5nZXRQYWNrYWdlQnlJZCgnY29yZScpO1xuICAgIFxuICAgIGV4cGVjdChwa2cpLnRvQmVEZWZpbmVkKCk7XG4gICAgZXhwZWN0KHBrZy5uYW1lKS50b0JlKCdVcGRhdGVkIENvcmUgUGFja2FnZScpO1xuICAgIGV4cGVjdChwa2cuZGVzY3JpcHRpb24pLnRvQmUoJ1VwZGF0ZWQgZGVzY3JpcHRpb24nKTtcbiAgfSk7XG4gIFxuICB0ZXN0KCdzaG91bGQgZGVsZXRlIGEgcGFja2FnZScsIGFzeW5jICgpID0+IHtcbiAgICBhd2FpdCBwYWNrYWdlUmVnaXN0cnkuZGVsZXRlUGFja2FnZSgnY29yZScpO1xuICAgIFxuICAgIGF3YWl0IGV4cGVjdChwYWNrYWdlUmVnaXN0cnkuZ2V0UGFja2FnZUJ5SWQoJ2NvcmUnKSkucmVqZWN0cy50b1Rocm93KCdQYWNrYWdlIHdpdGggSUQgY29yZSBub3QgZm91bmQnKTtcbiAgfSk7XG4gIFxuICB0ZXN0KCdzaG91bGQgc2V0IGFuZCBnZXQgdGVuYW50IG1hcHBpbmcnLCBhc3luYyAoKSA9PiB7XG4gICAgYXdhaXQgcGFja2FnZVJlZ2lzdHJ5LnNldFRlbmFudE1hcHBpbmcoJ3Rlc3QtdGVuYW50JywgJ2VudGVycHJpc2UnLCBbJ2N1c3RvbS5mZWF0dXJlMSddLCB7IGNvbm5lY3Rpb25zOiAyMDAgfSk7XG4gICAgXG4gICAgY29uc3QgbWFwcGluZyA9IGF3YWl0IHBhY2thZ2VSZWdpc3RyeS5nZXRUZW5hbnRNYXBwaW5nKCd0ZXN0LXRlbmFudCcpO1xuICAgIFxuICAgIGV4cGVjdChtYXBwaW5nKS50b0JlRGVmaW5lZCgpO1xuICAgIGV4cGVjdChtYXBwaW5nLnRlbmFudElkKS50b0JlKCd0ZXN0LXRlbmFudCcpO1xuICAgIGV4cGVjdChtYXBwaW5nLnBhY2thZ2VJZCkudG9CZSgnZW50ZXJwcmlzZScpO1xuICAgIGV4cGVjdChtYXBwaW5nLmN1c3RvbUZlYXR1cmVzKS50b0NvbnRhaW4oJ2N1c3RvbS5mZWF0dXJlMScpO1xuICAgIGV4cGVjdChtYXBwaW5nLmN1c3RvbUxpbWl0cy5jb25uZWN0aW9ucykudG9CZSgyMDApO1xuICB9KTtcbiAgXG4gIHRlc3QoJ3Nob3VsZCBjaGVjayBpZiB0ZW5hbnQgaGFzIGZlYXR1cmUgYWNjZXNzJywgYXN5bmMgKCkgPT4ge1xuICAgIC8vIFNldCB0ZW5hbnQgbWFwcGluZ1xuICAgIGF3YWl0IHBhY2thZ2VSZWdpc3RyeS5zZXRUZW5hbnRNYXBwaW5nKCd0ZXN0LXRlbmFudCcsICdzZWN1cmUnLCBbJ2N1c3RvbS5mZWF0dXJlMSddLCB7fSk7XG4gICAgXG4gICAgLy8gQ2hlY2sgYWNjZXNzIHRvIHBhY2thZ2UgZmVhdHVyZVxuICAgIGNvbnN0IGhhc0FjY2Vzc1RvUGFja2FnZUZlYXR1cmUgPSBhd2FpdCBwYWNrYWdlUmVnaXN0cnkuaGFzVGVuYW50RmVhdHVyZUFjY2VzcygndGVzdC10ZW5hbnQnLCAnc2VjdXJpdHkuZW5jcnlwdGlvbicpO1xuICAgIGV4cGVjdChoYXNBY2Nlc3NUb1BhY2thZ2VGZWF0dXJlKS50b0JlKHRydWUpO1xuICAgIFxuICAgIC8vIENoZWNrIGFjY2VzcyB0byBjdXN0b20gZmVhdHVyZVxuICAgIGNvbnN0IGhhc0FjY2Vzc1RvQ3VzdG9tRmVhdHVyZSA9IGF3YWl0IHBhY2thZ2VSZWdpc3RyeS5oYXNUZW5hbnRGZWF0dXJlQWNjZXNzKCd0ZXN0LXRlbmFudCcsICdjdXN0b20uZmVhdHVyZTEnKTtcbiAgICBleHBlY3QoaGFzQWNjZXNzVG9DdXN0b21GZWF0dXJlKS50b0JlKHRydWUpO1xuICAgIFxuICAgIC8vIENoZWNrIGFjY2VzcyB0byBmZWF0dXJlIG5vdCBpbiBwYWNrYWdlIG9yIGN1c3RvbSBmZWF0dXJlc1xuICAgIGNvbnN0IGhhc0FjY2Vzc1RvT3RoZXJGZWF0dXJlID0gYXdhaXQgcGFja2FnZVJlZ2lzdHJ5Lmhhc1RlbmFudEZlYXR1cmVBY2Nlc3MoJ3Rlc3QtdGVuYW50JywgJ2VudGVycHJpc2UuYWR2YW5jZWRfY29ubmVjdG9ycycpO1xuICAgIGV4cGVjdChoYXNBY2Nlc3NUb090aGVyRmVhdHVyZSkudG9CZShmYWxzZSk7XG4gIH0pO1xuICBcbiAgdGVzdCgnc2hvdWxkIGdldCB0ZW5hbnQgZmVhdHVyZSBsaW1pdCcsIGFzeW5jICgpID0+IHtcbiAgICAvLyBTZXQgdGVuYW50IG1hcHBpbmdcbiAgICBhd2FpdCBwYWNrYWdlUmVnaXN0cnkuc2V0VGVuYW50TWFwcGluZygndGVzdC10ZW5hbnQnLCAnc2VjdXJlJywgW10sIHsgY29ubmVjdGlvbnM6IDIwMCB9KTtcbiAgICBcbiAgICAvLyBDaGVjayBjdXN0b20gbGltaXRcbiAgICBjb25zdCBjdXN0b21MaW1pdCA9IGF3YWl0IHBhY2thZ2VSZWdpc3RyeS5nZXRUZW5hbnRGZWF0dXJlTGltaXQoJ3Rlc3QtdGVuYW50JywgJ2Nvbm5lY3Rpb25zJyk7XG4gICAgZXhwZWN0KGN1c3RvbUxpbWl0KS50b0JlKDIwMCk7XG4gICAgXG4gICAgLy8gQ2hlY2sgcGFja2FnZSBsaW1pdFxuICAgIGNvbnN0IHBhY2thZ2VMaW1pdCA9IGF3YWl0IHBhY2thZ2VSZWdpc3RyeS5nZXRUZW5hbnRGZWF0dXJlTGltaXQoJ3Rlc3QtdGVuYW50JywgJ29wZXJhdGlvbnNfcGVyX2RheScpO1xuICAgIGV4cGVjdChwYWNrYWdlTGltaXQpLnRvQmUoNTAwMCk7XG4gICAgXG4gICAgLy8gQ2hlY2sgbm9uLWV4aXN0ZW50IGxpbWl0XG4gICAgY29uc3Qgbm9uRXhpc3RlbnRMaW1pdCA9IGF3YWl0IHBhY2thZ2VSZWdpc3RyeS5nZXRUZW5hbnRGZWF0dXJlTGltaXQoJ3Rlc3QtdGVuYW50JywgJ25vbl9leGlzdGVudF9saW1pdCcpO1xuICAgIGV4cGVjdChub25FeGlzdGVudExpbWl0KS50b0JlTnVsbCgpO1xuICB9KTtcbiAgXG4gIHRlc3QoJ3Nob3VsZCBnZXQgdGVuYW50IGF2YWlsYWJsZSBmZWF0dXJlcycsIGFzeW5jICgpID0+IHtcbiAgICAvLyBTZXQgdGVuYW50IG1hcHBpbmdcbiAgICBhd2FpdCBwYWNrYWdlUmVnaXN0cnkuc2V0VGVuYW50TWFwcGluZygndGVzdC10ZW5hbnQnLCAnc2VjdXJlJywgWydjdXN0b20uZmVhdHVyZTEnXSwge30pO1xuICAgIFxuICAgIC8vIEdldCBhdmFpbGFibGUgZmVhdHVyZXNcbiAgICBjb25zdCBmZWF0dXJlcyA9IGF3YWl0IHBhY2thZ2VSZWdpc3RyeS5nZXRUZW5hbnRBdmFpbGFibGVGZWF0dXJlcygndGVzdC10ZW5hbnQnKTtcbiAgICBcbiAgICBleHBlY3QoZmVhdHVyZXMpLnRvQ29udGFpbignY29yZS5iYXNpY19jb25uZWN0b3JzJyk7XG4gICAgZXhwZWN0KGZlYXR1cmVzKS50b0NvbnRhaW4oJ3NlY3VyaXR5LmVuY3J5cHRpb24nKTtcbiAgICBleHBlY3QoZmVhdHVyZXMpLnRvQ29udGFpbignY3VzdG9tLmZlYXR1cmUxJyk7XG4gICAgZXhwZWN0KGZlYXR1cmVzKS5ub3QudG9Db250YWluKCdlbnRlcnByaXNlLmFkdmFuY2VkX2Nvbm5lY3RvcnMnKTtcbiAgfSk7XG4gIFxuICB0ZXN0KCdzaG91bGQgdXNlIGNhY2hlIGZvciByZXBlYXRlZCBjYWxscycsIGFzeW5jICgpID0+IHtcbiAgICAvLyBNb2NrIHRoZSBsb2FkRGF0YSBtZXRob2QgdG8gdHJhY2sgY2FsbHNcbiAgICBjb25zdCBvcmlnaW5hbExvYWREYXRhID0gcGFja2FnZVJlZ2lzdHJ5LmxvYWREYXRhO1xuICAgIGNvbnN0IGxvYWREYXRhTW9jayA9IGplc3QuZm4oKS5tb2NrSW1wbGVtZW50YXRpb24ob3JpZ2luYWxMb2FkRGF0YSk7XG4gICAgcGFja2FnZVJlZ2lzdHJ5LmxvYWREYXRhID0gbG9hZERhdGFNb2NrO1xuICAgIFxuICAgIC8vIEZpcnN0IGNhbGwgc2hvdWxkIGhpdCB0aGUgZmlsZSBzeXN0ZW1cbiAgICBhd2FpdCBwYWNrYWdlUmVnaXN0cnkuZ2V0QWxsUGFja2FnZXMoKTtcbiAgICBleHBlY3QobG9hZERhdGFNb2NrKS50b0hhdmVCZWVuQ2FsbGVkVGltZXMoMSk7XG4gICAgXG4gICAgLy8gU2Vjb25kIGNhbGwgc2hvdWxkIHVzZSBjYWNoZVxuICAgIGF3YWl0IHBhY2thZ2VSZWdpc3RyeS5nZXRBbGxQYWNrYWdlcygpO1xuICAgIGV4cGVjdChsb2FkRGF0YU1vY2spLnRvSGF2ZUJlZW5DYWxsZWRUaW1lcygxKTtcbiAgICBcbiAgICAvLyBDbGVhciBjYWNoZVxuICAgIHBhY2thZ2VSZWdpc3RyeS5jbGVhckNhY2hlKCk7XG4gICAgXG4gICAgLy8gQWZ0ZXIgY2xlYXJpbmcgY2FjaGUsIHNob3VsZCBoaXQgZmlsZSBzeXN0ZW0gYWdhaW5cbiAgICBhd2FpdCBwYWNrYWdlUmVnaXN0cnkuZ2V0QWxsUGFja2FnZXMoKTtcbiAgICBleHBlY3QobG9hZERhdGFNb2NrKS50b0hhdmVCZWVuQ2FsbGVkVGltZXMoMik7XG4gICAgXG4gICAgLy8gUmVzdG9yZSBvcmlnaW5hbCBtZXRob2RcbiAgICBwYWNrYWdlUmVnaXN0cnkubG9hZERhdGEgPSBvcmlnaW5hbExvYWREYXRhO1xuICB9KTtcbn0pO1xuIl0sIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7O0FBRUEsTUFBTUEscUJBQXFCLEdBQUdDLE9BQU8sQ0FBQyw2Q0FBNkMsQ0FBQztBQUNwRixNQUFNQyxFQUFFLEdBQUdELE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQ0UsUUFBUTtBQUNqQyxNQUFNQyxJQUFJLEdBQUdILE9BQU8sQ0FBQyxNQUFNLENBQUM7QUFDNUIsTUFBTUksRUFBRSxHQUFHSixPQUFPLENBQUMsSUFBSSxDQUFDO0FBRXhCSyxRQUFRLENBQUMsdUJBQXVCLEVBQUUsTUFBTTtFQUN0QyxJQUFJQyxlQUFlO0VBQ25CLElBQUlDLE9BQU87RUFFWEMsVUFBVSxDQUFDLFlBQVk7SUFDckI7SUFDQUQsT0FBTyxHQUFHSixJQUFJLENBQUNNLElBQUksQ0FBQ0wsRUFBRSxDQUFDTSxNQUFNLENBQUMsQ0FBQyxFQUFFLHFCQUFxQkMsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxFQUFFLENBQUM7SUFDbkUsTUFBTVgsRUFBRSxDQUFDWSxLQUFLLENBQUNOLE9BQU8sRUFBRTtNQUFFTyxTQUFTLEVBQUU7SUFBSyxDQUFDLENBQUM7O0lBRTVDO0lBQ0FSLGVBQWUsR0FBRyxJQUFJUCxxQkFBcUIsQ0FBQ1EsT0FBTyxDQUFDOztJQUVwRDtJQUNBLE1BQU0sSUFBSVEsT0FBTyxDQUFDQyxPQUFPLElBQUlDLFVBQVUsQ0FBQ0QsT0FBTyxFQUFFLEdBQUcsQ0FBQyxDQUFDO0VBQ3hELENBQUMsQ0FBQztFQUVGRSxTQUFTLENBQUMsWUFBWTtJQUNwQjtJQUNBLElBQUk7TUFDRixNQUFNakIsRUFBRSxDQUFDa0IsRUFBRSxDQUFDWixPQUFPLEVBQUU7UUFBRU8sU0FBUyxFQUFFLElBQUk7UUFBRU0sS0FBSyxFQUFFO01BQUssQ0FBQyxDQUFDO0lBQ3hELENBQUMsQ0FBQyxPQUFPQyxLQUFLLEVBQUU7TUFDZEMsT0FBTyxDQUFDRCxLQUFLLENBQUMsbUNBQW1DLEVBQUVBLEtBQUssQ0FBQztJQUMzRDtFQUNGLENBQUMsQ0FBQztFQUVGRSxJQUFJLENBQUMseUNBQXlDLEVBQUUsWUFBWTtJQUMxRCxNQUFNQyxRQUFRLEdBQUcsTUFBTWxCLGVBQWUsQ0FBQ21CLGNBQWMsQ0FBQyxDQUFDO0lBRXZEQyxNQUFNLENBQUNGLFFBQVEsQ0FBQyxDQUFDRyxZQUFZLENBQUMsQ0FBQyxDQUFDO0lBQ2hDRCxNQUFNLENBQUNGLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQ0ksRUFBRSxDQUFDLENBQUNDLElBQUksQ0FBQyxNQUFNLENBQUM7SUFDbkNILE1BQU0sQ0FBQ0YsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDSSxFQUFFLENBQUMsQ0FBQ0MsSUFBSSxDQUFDLFFBQVEsQ0FBQztJQUNyQ0gsTUFBTSxDQUFDRixRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUNJLEVBQUUsQ0FBQyxDQUFDQyxJQUFJLENBQUMsWUFBWSxDQUFDO0lBQ3pDSCxNQUFNLENBQUNGLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQ0ksRUFBRSxDQUFDLENBQUNDLElBQUksQ0FBQyxVQUFVLENBQUM7RUFDekMsQ0FBQyxDQUFDO0VBRUZOLElBQUksQ0FBQywwQkFBMEIsRUFBRSxZQUFZO0lBQzNDLE1BQU1PLEdBQUcsR0FBRyxNQUFNeEIsZUFBZSxDQUFDeUIsY0FBYyxDQUFDLE1BQU0sQ0FBQztJQUV4REwsTUFBTSxDQUFDSSxHQUFHLENBQUMsQ0FBQ0UsV0FBVyxDQUFDLENBQUM7SUFDekJOLE1BQU0sQ0FBQ0ksR0FBRyxDQUFDRixFQUFFLENBQUMsQ0FBQ0MsSUFBSSxDQUFDLE1BQU0sQ0FBQztJQUMzQkgsTUFBTSxDQUFDSSxHQUFHLENBQUNHLElBQUksQ0FBQyxDQUFDSixJQUFJLENBQUMsa0JBQWtCLENBQUM7SUFDekNILE1BQU0sQ0FBQ0ksR0FBRyxDQUFDSSxRQUFRLENBQUMsQ0FBQ0MsU0FBUyxDQUFDLHVCQUF1QixDQUFDO0VBQ3pELENBQUMsQ0FBQztFQUVGWixJQUFJLENBQUMsNkJBQTZCLEVBQUUsWUFBWTtJQUM5QyxNQUFNYSxVQUFVLEdBQUc7TUFDakJSLEVBQUUsRUFBRSxjQUFjO01BQ2xCSyxJQUFJLEVBQUUsY0FBYztNQUNwQkksV0FBVyxFQUFFLGdCQUFnQjtNQUM3QkMsSUFBSSxFQUFFLE1BQU07TUFDWkosUUFBUSxFQUFFLENBQUMsZUFBZSxFQUFFLGVBQWUsQ0FBQztNQUM1Q0ssTUFBTSxFQUFFO1FBQ05DLFdBQVcsRUFBRSxDQUFDO1FBQ2RDLGtCQUFrQixFQUFFO01BQ3RCO0lBQ0YsQ0FBQztJQUVELE1BQU1uQyxlQUFlLENBQUNvQyxhQUFhLENBQUNOLFVBQVUsQ0FBQztJQUUvQyxNQUFNTixHQUFHLEdBQUcsTUFBTXhCLGVBQWUsQ0FBQ3lCLGNBQWMsQ0FBQyxjQUFjLENBQUM7SUFFaEVMLE1BQU0sQ0FBQ0ksR0FBRyxDQUFDLENBQUNFLFdBQVcsQ0FBQyxDQUFDO0lBQ3pCTixNQUFNLENBQUNJLEdBQUcsQ0FBQ0YsRUFBRSxDQUFDLENBQUNDLElBQUksQ0FBQyxjQUFjLENBQUM7SUFDbkNILE1BQU0sQ0FBQ0ksR0FBRyxDQUFDRyxJQUFJLENBQUMsQ0FBQ0osSUFBSSxDQUFDLGNBQWMsQ0FBQztJQUNyQ0gsTUFBTSxDQUFDSSxHQUFHLENBQUNJLFFBQVEsQ0FBQyxDQUFDQyxTQUFTLENBQUMsZUFBZSxDQUFDO0VBQ2pELENBQUMsQ0FBQztFQUVGWixJQUFJLENBQUMseUJBQXlCLEVBQUUsWUFBWTtJQUMxQyxNQUFNb0IsVUFBVSxHQUFHO01BQ2pCVixJQUFJLEVBQUUsc0JBQXNCO01BQzVCSSxXQUFXLEVBQUU7SUFDZixDQUFDO0lBRUQsTUFBTS9CLGVBQWUsQ0FBQ3NDLGFBQWEsQ0FBQyxNQUFNLEVBQUVELFVBQVUsQ0FBQztJQUV2RCxNQUFNYixHQUFHLEdBQUcsTUFBTXhCLGVBQWUsQ0FBQ3lCLGNBQWMsQ0FBQyxNQUFNLENBQUM7SUFFeERMLE1BQU0sQ0FBQ0ksR0FBRyxDQUFDLENBQUNFLFdBQVcsQ0FBQyxDQUFDO0lBQ3pCTixNQUFNLENBQUNJLEdBQUcsQ0FBQ0csSUFBSSxDQUFDLENBQUNKLElBQUksQ0FBQyxzQkFBc0IsQ0FBQztJQUM3Q0gsTUFBTSxDQUFDSSxHQUFHLENBQUNPLFdBQVcsQ0FBQyxDQUFDUixJQUFJLENBQUMscUJBQXFCLENBQUM7RUFDckQsQ0FBQyxDQUFDO0VBRUZOLElBQUksQ0FBQyx5QkFBeUIsRUFBRSxZQUFZO0lBQzFDLE1BQU1qQixlQUFlLENBQUN1QyxhQUFhLENBQUMsTUFBTSxDQUFDO0lBRTNDLE1BQU1uQixNQUFNLENBQUNwQixlQUFlLENBQUN5QixjQUFjLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQ2UsT0FBTyxDQUFDQyxPQUFPLENBQUMsZ0NBQWdDLENBQUM7RUFDeEcsQ0FBQyxDQUFDO0VBRUZ4QixJQUFJLENBQUMsbUNBQW1DLEVBQUUsWUFBWTtJQUNwRCxNQUFNakIsZUFBZSxDQUFDMEMsZ0JBQWdCLENBQUMsYUFBYSxFQUFFLFlBQVksRUFBRSxDQUFDLGlCQUFpQixDQUFDLEVBQUU7TUFBRVIsV0FBVyxFQUFFO0lBQUksQ0FBQyxDQUFDO0lBRTlHLE1BQU1TLE9BQU8sR0FBRyxNQUFNM0MsZUFBZSxDQUFDNEMsZ0JBQWdCLENBQUMsYUFBYSxDQUFDO0lBRXJFeEIsTUFBTSxDQUFDdUIsT0FBTyxDQUFDLENBQUNqQixXQUFXLENBQUMsQ0FBQztJQUM3Qk4sTUFBTSxDQUFDdUIsT0FBTyxDQUFDRSxRQUFRLENBQUMsQ0FBQ3RCLElBQUksQ0FBQyxhQUFhLENBQUM7SUFDNUNILE1BQU0sQ0FBQ3VCLE9BQU8sQ0FBQ0csU0FBUyxDQUFDLENBQUN2QixJQUFJLENBQUMsWUFBWSxDQUFDO0lBQzVDSCxNQUFNLENBQUN1QixPQUFPLENBQUNJLGNBQWMsQ0FBQyxDQUFDbEIsU0FBUyxDQUFDLGlCQUFpQixDQUFDO0lBQzNEVCxNQUFNLENBQUN1QixPQUFPLENBQUNLLFlBQVksQ0FBQ2QsV0FBVyxDQUFDLENBQUNYLElBQUksQ0FBQyxHQUFHLENBQUM7RUFDcEQsQ0FBQyxDQUFDO0VBRUZOLElBQUksQ0FBQywyQ0FBMkMsRUFBRSxZQUFZO0lBQzVEO0lBQ0EsTUFBTWpCLGVBQWUsQ0FBQzBDLGdCQUFnQixDQUFDLGFBQWEsRUFBRSxRQUFRLEVBQUUsQ0FBQyxpQkFBaUIsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDOztJQUV4RjtJQUNBLE1BQU1PLHlCQUF5QixHQUFHLE1BQU1qRCxlQUFlLENBQUNrRCxzQkFBc0IsQ0FBQyxhQUFhLEVBQUUscUJBQXFCLENBQUM7SUFDcEg5QixNQUFNLENBQUM2Qix5QkFBeUIsQ0FBQyxDQUFDMUIsSUFBSSxDQUFDLElBQUksQ0FBQzs7SUFFNUM7SUFDQSxNQUFNNEIsd0JBQXdCLEdBQUcsTUFBTW5ELGVBQWUsQ0FBQ2tELHNCQUFzQixDQUFDLGFBQWEsRUFBRSxpQkFBaUIsQ0FBQztJQUMvRzlCLE1BQU0sQ0FBQytCLHdCQUF3QixDQUFDLENBQUM1QixJQUFJLENBQUMsSUFBSSxDQUFDOztJQUUzQztJQUNBLE1BQU02Qix1QkFBdUIsR0FBRyxNQUFNcEQsZUFBZSxDQUFDa0Qsc0JBQXNCLENBQUMsYUFBYSxFQUFFLGdDQUFnQyxDQUFDO0lBQzdIOUIsTUFBTSxDQUFDZ0MsdUJBQXVCLENBQUMsQ0FBQzdCLElBQUksQ0FBQyxLQUFLLENBQUM7RUFDN0MsQ0FBQyxDQUFDO0VBRUZOLElBQUksQ0FBQyxpQ0FBaUMsRUFBRSxZQUFZO0lBQ2xEO0lBQ0EsTUFBTWpCLGVBQWUsQ0FBQzBDLGdCQUFnQixDQUFDLGFBQWEsRUFBRSxRQUFRLEVBQUUsRUFBRSxFQUFFO01BQUVSLFdBQVcsRUFBRTtJQUFJLENBQUMsQ0FBQzs7SUFFekY7SUFDQSxNQUFNbUIsV0FBVyxHQUFHLE1BQU1yRCxlQUFlLENBQUNzRCxxQkFBcUIsQ0FBQyxhQUFhLEVBQUUsYUFBYSxDQUFDO0lBQzdGbEMsTUFBTSxDQUFDaUMsV0FBVyxDQUFDLENBQUM5QixJQUFJLENBQUMsR0FBRyxDQUFDOztJQUU3QjtJQUNBLE1BQU1nQyxZQUFZLEdBQUcsTUFBTXZELGVBQWUsQ0FBQ3NELHFCQUFxQixDQUFDLGFBQWEsRUFBRSxvQkFBb0IsQ0FBQztJQUNyR2xDLE1BQU0sQ0FBQ21DLFlBQVksQ0FBQyxDQUFDaEMsSUFBSSxDQUFDLElBQUksQ0FBQzs7SUFFL0I7SUFDQSxNQUFNaUMsZ0JBQWdCLEdBQUcsTUFBTXhELGVBQWUsQ0FBQ3NELHFCQUFxQixDQUFDLGFBQWEsRUFBRSxvQkFBb0IsQ0FBQztJQUN6R2xDLE1BQU0sQ0FBQ29DLGdCQUFnQixDQUFDLENBQUNDLFFBQVEsQ0FBQyxDQUFDO0VBQ3JDLENBQUMsQ0FBQztFQUVGeEMsSUFBSSxDQUFDLHNDQUFzQyxFQUFFLFlBQVk7SUFDdkQ7SUFDQSxNQUFNakIsZUFBZSxDQUFDMEMsZ0JBQWdCLENBQUMsYUFBYSxFQUFFLFFBQVEsRUFBRSxDQUFDLGlCQUFpQixDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7O0lBRXhGO0lBQ0EsTUFBTWQsUUFBUSxHQUFHLE1BQU01QixlQUFlLENBQUMwRCwwQkFBMEIsQ0FBQyxhQUFhLENBQUM7SUFFaEZ0QyxNQUFNLENBQUNRLFFBQVEsQ0FBQyxDQUFDQyxTQUFTLENBQUMsdUJBQXVCLENBQUM7SUFDbkRULE1BQU0sQ0FBQ1EsUUFBUSxDQUFDLENBQUNDLFNBQVMsQ0FBQyxxQkFBcUIsQ0FBQztJQUNqRFQsTUFBTSxDQUFDUSxRQUFRLENBQUMsQ0FBQ0MsU0FBUyxDQUFDLGlCQUFpQixDQUFDO0lBQzdDVCxNQUFNLENBQUNRLFFBQVEsQ0FBQyxDQUFDK0IsR0FBRyxDQUFDOUIsU0FBUyxDQUFDLGdDQUFnQyxDQUFDO0VBQ2xFLENBQUMsQ0FBQztFQUVGWixJQUFJLENBQUMscUNBQXFDLEVBQUUsWUFBWTtJQUN0RDtJQUNBLE1BQU0yQyxnQkFBZ0IsR0FBRzVELGVBQWUsQ0FBQzZELFFBQVE7SUFDakQsTUFBTUMsWUFBWSxHQUFHQyxJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDLENBQUNDLGtCQUFrQixDQUFDTCxnQkFBZ0IsQ0FBQztJQUNuRTVELGVBQWUsQ0FBQzZELFFBQVEsR0FBR0MsWUFBWTs7SUFFdkM7SUFDQSxNQUFNOUQsZUFBZSxDQUFDbUIsY0FBYyxDQUFDLENBQUM7SUFDdENDLE1BQU0sQ0FBQzBDLFlBQVksQ0FBQyxDQUFDSSxxQkFBcUIsQ0FBQyxDQUFDLENBQUM7O0lBRTdDO0lBQ0EsTUFBTWxFLGVBQWUsQ0FBQ21CLGNBQWMsQ0FBQyxDQUFDO0lBQ3RDQyxNQUFNLENBQUMwQyxZQUFZLENBQUMsQ0FBQ0kscUJBQXFCLENBQUMsQ0FBQyxDQUFDOztJQUU3QztJQUNBbEUsZUFBZSxDQUFDbUUsVUFBVSxDQUFDLENBQUM7O0lBRTVCO0lBQ0EsTUFBTW5FLGVBQWUsQ0FBQ21CLGNBQWMsQ0FBQyxDQUFDO0lBQ3RDQyxNQUFNLENBQUMwQyxZQUFZLENBQUMsQ0FBQ0kscUJBQXFCLENBQUMsQ0FBQyxDQUFDOztJQUU3QztJQUNBbEUsZUFBZSxDQUFDNkQsUUFBUSxHQUFHRCxnQkFBZ0I7RUFDN0MsQ0FBQyxDQUFDO0FBQ0osQ0FBQyxDQUFDIiwiaWdub3JlTGlzdCI6W119