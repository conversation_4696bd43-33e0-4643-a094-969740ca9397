/**
 * Comphyological Analytics Module
 * 
 * This module exports the analytics components for the Comphyological Tensor Core.
 */

const { AnalyticsCore } = require('./core');

/**
 * Create analytics core
 * @param {Object} options - Configuration options
 * @returns {AnalyticsCore} Analytics core instance
 */
function createAnalyticsCore(options = {}) {
    return new AnalyticsCore(options);
}

/**
 * Open analytics dashboard
 */
function openAnalyticsDashboard() {
    // In a browser environment, this would open the dashboard in a new window
    // In Node.js, we'll just log a message
    console.log('Opening analytics dashboard...');
    
    // If running in a browser, open the dashboard
    if (typeof window !== 'undefined') {
        window.open('./analytics/dashboard/index.html', '_blank');
    } else {
        console.log('Dashboard URL: ./analytics/dashboard/index.html');
    }
}

// Export
module.exports = {
    AnalyticsCore,
    createAnalyticsCore,
    openAnalyticsDashboard
};
