# NovaGateway

NovaGateway is the central API Gateway for the NovaFuse platform, providing a unified entry point for all NovaFuse APIs.

## Overview

NovaGateway is a key component of the NovaFuse platform, providing a unified entry point for all NovaFuse APIs. It handles routing, authentication, rate limiting, and other cross-cutting concerns.

## Features

- **API Routing**: Routes requests to the appropriate backend services
- **Authentication**: Supports JWT, API keys, and OAuth 2.0
- **Rate Limiting**: Prevents abuse by limiting request rates
- **Request Logging**: Logs all API requests for auditing and debugging
- **Error Handling**: Provides consistent error responses
- **CORS Support**: Configurable CORS settings
- **API Documentation**: Swagger UI-based API documentation
- **Health Checks**: Monitors the health of backend services
- **Circuit Breaking**: Prevents cascading failures
- **Request Transformation**: Modifies requests before forwarding to backend services
- **Response Transformation**: Modifies responses before returning to clients

## Architecture

NovaGateway is built using a modern API Gateway architecture with the following components:

- **Express.js**: Web framework for Node.js
- **http-proxy-middleware**: Proxy middleware for Express.js
- **JWT**: JSON Web Token authentication
- **Helmet**: Security middleware
- **Winston**: Logging library
- **Express Rate Limit**: Rate limiting middleware

## Service Routes

NovaGateway routes requests to the following services:

- **NovaConnect**: `/api/novaconnect/*`
- **Privacy Management API**: `/api/privacy/management/*`
- **Regulatory Compliance API**: `/api/compliance/*`
- **Security Assessment API**: `/api/security/assessment/*`
- **Control Testing API**: `/api/control/testing/*`
- **ESG API**: `/api/esg/*`
- **Compliance Automation API**: `/api/compliance/automation/*`

## Authentication

NovaGateway supports multiple authentication methods:

- **JWT**: For user authentication
- **API Keys**: For service-to-service authentication
- **OAuth 2.0**: For third-party integrations

### JWT Authentication

JWT authentication is used for user authentication. The JWT token is passed in the `Authorization` header as a Bearer token.

```
Authorization: Bearer <token>
```

### API Key Authentication

API key authentication is used for service-to-service authentication. The API key is passed in the `X-API-Key` header.

```
X-API-Key: <api-key>
```

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/Dartan1983/nova-gateway.git
   cd nova-gateway
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Configure environment variables:
   ```
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Start the service:
   ```
   npm start
   ```

### Development

1. Start in development mode:
   ```
   npm run dev
   ```

2. Run tests:
   ```
   npm test
   ```

3. Run with test coverage:
   ```
   npm run test:coverage
   ```

## Configuration

NovaGateway is configured using environment variables and a configuration file.

### Environment Variables

- `PORT`: The port to listen on (default: 3000)
- `NODE_ENV`: The environment (development, test, production)
- `JWT_SECRET`: The secret key for JWT authentication
- `JWT_EXPIRES_IN`: The JWT expiration time (default: 1h)
- `API_KEY_HEADER`: The header name for API keys (default: X-API-Key)
- `RATE_LIMIT_MAX`: The maximum number of requests per window (default: 100)
- `CORS_ORIGIN`: The allowed CORS origins (default: *)
- `LOG_LEVEL`: The logging level (default: info)
- `LOG_FORMAT`: The logging format (default: json)
- `LOG_DIRECTORY`: The directory for log files (default: logs)

### Service Configuration

Services are configured in the `config.js` file:

```javascript
services: {
  novaconnect: {
    url: process.env.NOVACONNECT_URL || 'http://localhost:3001',
    path: '/api/novaconnect'
  },
  privacyManagement: {
    url: process.env.PRIVACY_MANAGEMENT_URL || 'http://localhost:3002',
    path: '/api/privacy/management'
  },
  // ... other services
}
```

## Folder Structure

```
nova-gateway/
├── config/                 # Configuration files
│   └── index.js            # Main configuration
├── middleware/             # Express middleware
│   ├── auth.js             # Authentication middleware
│   ├── errorHandler.js     # Error handling middleware
│   └── logger.js           # Logging middleware
├── public/                 # Static assets
│   └── api-docs/           # API documentation
├── routes/                 # API routes
│   └── index.js            # Main routes
├── services/               # Service definitions
│   └── index.js            # Service registry
├── utils/                  # Utility functions
├── .env.example            # Example environment variables
├── package.json            # Package configuration
├── server.js               # Main entry point
└── README.md               # Documentation
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

For more information, contact the NovaFuse <NAME_EMAIL>.
