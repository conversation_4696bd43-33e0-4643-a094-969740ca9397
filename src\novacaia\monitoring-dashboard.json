{"dashboard": {"id": null, "title": "NovaCaia Enterprise - AI Governance Monitoring", "tags": ["novacaia", "ai-governance", "enterprise"], "timezone": "browser", "panels": [{"id": 1, "title": "System Overview", "type": "stat", "targets": [{"expr": "up{job=\"novacaia\"}", "legendFormat": "System Status"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Consciousness Score", "type": "gauge", "targets": [{"expr": "nova<PERSON><PERSON>_consciousness_score", "legendFormat": "Consciousness Score"}], "fieldConfig": {"defaults": {"min": 0, "max": 1, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.7}, {"color": "green", "value": 0.91}]}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Processing Time", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, novacaia_processing_duration_seconds_bucket)", "legendFormat": "95th Percentile"}], "fieldConfig": {"defaults": {"unit": "ms", "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 300}, {"color": "red", "value": 500}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Platform Allocation", "type": "stat", "targets": [{"expr": "novacaia_platform_allocation_percentage", "legendFormat": "Platform %"}], "fieldConfig": {"defaults": {"unit": "percent", "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 18}]}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "Request Rate", "type": "graph", "targets": [{"expr": "rate(novacaia_requests_total[5m])", "legendFormat": "Requests/sec"}], "yAxes": [{"label": "Requests/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "Truth Coherence Distribution", "type": "heatmap", "targets": [{"expr": "increase(novacaia_truth_coherence_bucket[5m])", "legendFormat": "{{le}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "Boundary Violations", "type": "graph", "targets": [{"expr": "increase(novacaia_boundary_violations_total[5m])", "legendFormat": "Violations"}], "alert": {"conditions": [{"query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "last"}, "evaluator": {"params": [0], "type": "gt"}}], "executionErrorState": "alerting", "for": "5m", "frequency": "10s", "handler": 1, "name": "Boundary Violations Alert", "noDataState": "no_data", "notifications": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 8, "title": "False Authority Detections", "type": "graph", "targets": [{"expr": "increase(novacaia_false_authority_detections_total[5m])", "legendFormat": "Detections"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 9, "title": "Financial Metrics", "type": "table", "targets": [{"expr": "novacaia_platform_allocation_total", "legendFormat": "Platform Allocation"}, {"expr": "novacaia_enterprise_retention_total", "legendFormat": "Enterprise Retention"}, {"expr": "novacaia_optimization_factor", "legendFormat": "Optimization Factor"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}, {"id": 10, "title": "Pod Resource Usage", "type": "graph", "targets": [{"expr": "rate(container_cpu_usage_seconds_total{pod=~\"novacaia-.*\"}[5m])", "legendFormat": "CPU Usage - {{pod}}"}, {"expr": "container_memory_usage_bytes{pod=~\"novacaia-.*\"} / 1024 / 1024", "legendFormat": "Memory Usage MB - {{pod}}"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s"}}