/**
 * Comphyology Visualization Demo
 * 
 * This script generates a demo HTML page that showcases Comphyology visualizations.
 */

const fs = require('fs');
const path = require('path');
const ComphyologyVisualization = require('./visualization');
const { generateVisualizationHTML, generateVisualizationCSS } = require('./visualization_renderer');

/**
 * Generate a demo HTML page with Comphyology visualizations
 * 
 * @param {string} outputPath - Path to save the HTML file
 */
function generateDemoPage(outputPath) {
  // Initialize visualization generator
  const visualizer = new ComphyologyVisualization({ enableLogging: true });
  
  // Generate visualization data
  const morphologicalData = visualizer.generateMorphologicalResonanceField();
  const quantumData = visualizer.generateQuantumPhaseSpaceMap();
  const ethicalData = visualizer.generateEthicalTensorProjection();
  const trinityData = visualizer.generateTrinityIntegrationDiagram();
  
  // Generate visualization HTML
  const morphologicalHTML = generateVisualizationHTML(morphologicalData);
  const quantumHTML = generateVisualizationHTML(quantumData);
  const ethicalHTML = generateVisualizationHTML(ethicalData);
  const trinityHTML = generateVisualizationHTML(trinityData);
  
  // Generate CSS
  const css = generateVisualizationCSS();
  
  // Generate full HTML page
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comphyology (Ψᶜ) Visualization Demo</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      text-align: center;
      margin-bottom: 40px;
    }
    
    h1 {
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    .subtitle {
      color: #7f8c8d;
      font-size: 1.2em;
      margin-bottom: 20px;
    }
    
    .intro {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 30px;
      border-left: 5px solid #3498db;
    }
    
    .visualizations {
      display: grid;
      grid-template-columns: 1fr;
      gap: 30px;
    }
    
    .visualization-section {
      margin-bottom: 40px;
    }
    
    footer {
      margin-top: 50px;
      text-align: center;
      color: #7f8c8d;
      font-size: 0.9em;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }
    
    /* D3.js would be included in a real implementation */
    .d3-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      background-color: #f0f0f0;
      color: #666;
      font-style: italic;
    }
    
    ${css}
  </style>
  <!-- D3.js would be included in a real implementation -->
  <script>
    // Placeholder for D3.js
    window.onload = function() {
      const containers = document.querySelectorAll('.chart-container');
      containers.forEach(container => {
        const placeholder = document.createElement('div');
        placeholder.className = 'd3-placeholder';
        placeholder.textContent = 'D3.js visualization would render here';
        container.appendChild(placeholder);
      });
    };
  </script>
</head>
<body>
  <header>
    <h1>Comphyology (Ψᶜ) Visualization Demo</h1>
    <div class="subtitle">A synthetic mathematical and philosophical framework</div>
  </header>
  
  <div class="intro">
    <p>
      Comphyology (Ψᶜ) is a synthetic mathematical and philosophical framework developed by NovaFuse that blends 
      computational morphology, quantum-inspired tensor dynamics, and emergent logic modeling to describe complex systems.
    </p>
    <p>
      This demo showcases visualizations of key Comphyology concepts, demonstrating how they can be used to understand 
      and analyze complex systems in cybersecurity and beyond.
    </p>
  </div>
  
  <div class="visualizations">
    <section class="visualization-section">
      <h2>1. Morphological Resonance Field</h2>
      ${morphologicalHTML}
    </section>
    
    <section class="visualization-section">
      <h2>2. Quantum Phase Space Map</h2>
      ${quantumHTML}
    </section>
    
    <section class="visualization-section">
      <h2>3. Ethical Tensor Projection</h2>
      ${ethicalHTML}
    </section>
    
    <section class="visualization-section">
      <h2>4. Trinity Integration Diagram</h2>
      ${trinityHTML}
    </section>
  </div>
  
  <footer>
    <p>NovaFuse Comphyology (Ψᶜ) Framework - Copyright © ${new Date().getFullYear()}</p>
  </footer>
</body>
</html>
  `;
  
  // Save HTML file
  fs.writeFileSync(outputPath, html);
  
  console.log(`Demo page generated at: ${outputPath}`);
}

/**
 * Run the demo generator
 */
function runDemo() {
  const outputDir = path.join(__dirname, '../../comphyology_demo');
  
  // Create output directory if it doesn't exist
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  const outputPath = path.join(outputDir, 'index.html');
  generateDemoPage(outputPath);
}

// If this script is run directly, generate the demo
if (require.main === module) {
  runDemo();
}

module.exports = {
  generateDemoPage,
  runDemo
};
