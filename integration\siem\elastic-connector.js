/**
 * NovaFuse Elastic Connector
 * 
 * This module provides integration between NovaFuse and Elasticsearch/Kibana,
 * allowing NovaFuse events to be sent to Elastic for monitoring and analysis.
 */

const { Client } = require('@elastic/elasticsearch');

class ElasticConnector {
  /**
   * Create a new Elastic connector
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      node: options.node || 'http://localhost:9200',
      auth: options.auth || null,
      index: options.index || 'novafuse',
      batchSize: options.batchSize || 100,
      batchInterval: options.batchInterval || 5000, // 5 seconds
      retryCount: options.retryCount || 3,
      retryInterval: options.retryInterval || 1000, // 1 second
      logLevel: options.logLevel || 'info',
      ...options
    };
    
    // Create Elasticsearch client
    this.client = new Client({
      node: this.options.node,
      auth: this.options.auth,
      maxRetries: this.options.retryCount,
      requestTimeout: 30000
    });
    
    this.eventQueue = [];
    this.isSending = false;
    this.batchTimer = null;
    
    // Start batch timer
    this._startBatchTimer();
    
    console.log(`Elastic connector initialized with index: ${this.options.index}`);
  }
  
  /**
   * Send an event to Elasticsearch
   * @param {Object} event - Event data
   * @param {Object} [metadata] - Additional metadata
   * @returns {Promise<boolean>} - Whether the event was queued successfully
   */
  async sendEvent(event, metadata = {}) {
    try {
      // Add metadata
      const enrichedEvent = {
        ...event,
        '@timestamp': new Date().toISOString(),
        metadata: {
          source: 'novafuse',
          ...metadata
        }
      };
      
      // Add to queue
      this.eventQueue.push(enrichedEvent);
      
      // Send batch if queue is full
      if (this.eventQueue.length >= this.options.batchSize) {
        this._sendBatch();
      }
      
      return true;
    } catch (error) {
      console.error('Error queueing event for Elasticsearch:', error);
      return false;
    }
  }
  
  /**
   * Send Trinity CSDE event to Elasticsearch
   * @param {Object} trinityEvent - Trinity CSDE event data
   * @returns {Promise<boolean>} - Whether the event was queued successfully
   */
  async sendTrinityEvent(trinityEvent) {
    return this.sendEvent(trinityEvent, {
      type: 'trinity_csde',
      component: 'trinity_csde'
    });
  }
  
  /**
   * Send Quantum Inference event to Elasticsearch
   * @param {Object} quantumEvent - Quantum Inference event data
   * @returns {Promise<boolean>} - Whether the event was queued successfully
   */
  async sendQuantumEvent(quantumEvent) {
    return this.sendEvent(quantumEvent, {
      type: 'quantum_inference',
      component: 'quantum_inference'
    });
  }
  
  /**
   * Send security event to Elasticsearch
   * @param {Object} securityEvent - Security event data
   * @returns {Promise<boolean>} - Whether the event was queued successfully
   */
  async sendSecurityEvent(securityEvent) {
    return this.sendEvent(securityEvent, {
      type: 'security',
      component: 'security'
    });
  }
  
  /**
   * Flush all queued events
   * @returns {Promise<boolean>} - Whether the flush was successful
   */
  async flush() {
    if (this.eventQueue.length === 0) {
      return true;
    }
    
    return this._sendBatch();
  }
  
  /**
   * Start batch timer
   * @private
   */
  _startBatchTimer() {
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
    }
    
    this.batchTimer = setInterval(() => {
      if (this.eventQueue.length > 0 && !this.isSending) {
        this._sendBatch();
      }
    }, this.options.batchInterval);
  }
  
  /**
   * Send batch of events to Elasticsearch
   * @private
   * @returns {Promise<boolean>} - Whether the batch was sent successfully
   */
  async _sendBatch() {
    if (this.isSending || this.eventQueue.length === 0) {
      return false;
    }
    
    this.isSending = true;
    
    try {
      // Get events to send
      const events = this.eventQueue.splice(0, this.options.batchSize);
      
      // Prepare bulk operations
      const operations = events.flatMap(doc => [
        { index: { _index: this.options.index } },
        doc
      ]);
      
      // Send to Elasticsearch
      const response = await this.client.bulk({ operations });
      
      if (response.errors) {
        const errorItems = response.items.filter(item => item.index.error);
        console.error(`Elasticsearch bulk operation had ${errorItems.length} errors:`, 
          errorItems.map(item => item.index.error));
      }
      
      this.isSending = false;
      return !response.errors;
    } catch (error) {
      console.error('Error sending batch to Elasticsearch:', error);
      this.isSending = false;
      return false;
    }
  }
  
  /**
   * Create index template for NovaFuse data
   * @returns {Promise<boolean>} - Whether the template was created successfully
   */
  async createIndexTemplate() {
    try {
      const templateName = 'novafuse_template';
      
      const response = await this.client.indices.putIndexTemplate({
        name: templateName,
        body: {
          index_patterns: [`${this.options.index}*`],
          template: {
            settings: {
              number_of_shards: 1,
              number_of_replicas: 1
            },
            mappings: {
              properties: {
                '@timestamp': { type: 'date' },
                'metadata.source': { type: 'keyword' },
                'metadata.type': { type: 'keyword' },
                'metadata.component': { type: 'keyword' },
                'certaintyRate': { type: 'float' },
                'inferenceTime': { type: 'float' },
                'governanceScore': { type: 'float' },
                'detectionScore': { type: 'float' },
                'responseScore': { type: 'float' },
                'trinityScore': { type: 'float' },
                'userId': { type: 'keyword' },
                'role': { type: 'keyword' },
                'operation': { type: 'keyword' },
                'allowed': { type: 'boolean' },
                'reason': { type: 'keyword' }
              }
            }
          }
        }
      });
      
      console.log(`Elasticsearch index template created: ${templateName}`);
      return true;
    } catch (error) {
      console.error('Error creating Elasticsearch index template:', error);
      return false;
    }
  }
  
  /**
   * Close the connector
   */
  async close() {
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
      this.batchTimer = null;
    }
    
    // Flush remaining events
    if (this.eventQueue.length > 0) {
      await this._sendBatch().catch(error => {
        console.error('Error flushing events on close:', error);
      });
    }
    
    // Close Elasticsearch client
    await this.client.close();
  }
}

module.exports = ElasticConnector;
