/**
 * CSFE Example
 *
 * This example demonstrates the usage of the CSFE component.
 */

const {
  TransactionEntropy,
  AttackSurfaceCoherence,
  MarketStressInfusion,
  createCSFESystem,
  createEnhancedCSFESystem
} = require('../csfe');

// Example 1: Using individual components
console.log('Example 1: Using individual components');

// Create components
const transactionEntropy = new TransactionEntropy();
const attackSurfaceCoherence = new AttackSurfaceCoherence();
const marketStressInfusion = new MarketStressInfusion();

// Start components
transactionEntropy.start();
attackSurfaceCoherence.start();
marketStressInfusion.start();

// Example transactions
const transactions = [
  {
    id: 'tx-1',
    source: 'account-1',
    destination: 'account-2',
    amount: 500,
    timestamp: Date.now() - 5000
  },
  {
    id: 'tx-2',
    source: 'account-2',
    destination: 'account-3',
    amount: 750,
    timestamp: Date.now() - 4000
  },
  {
    id: 'tx-3',
    source: 'account-1',
    destination: 'account-4',
    amount: 1200,
    timestamp: Date.now() - 3000
  },
  {
    id: 'tx-4',
    source: 'account-3',
    destination: 'account-1',
    amount: 300,
    timestamp: Date.now() - 2000
  },
  {
    id: 'tx-5',
    source: 'account-4',
    destination: 'account-2',
    amount: 900,
    timestamp: Date.now() - 1000
  }
];

// Process transactions
const entropyResult = transactionEntropy.processTransactions(transactions);
console.log(`Transaction Entropy (Tᵋ): ${entropyResult.toFixed(4)}`);

// Add vulnerabilities
const vulnerability = attackSurfaceCoherence.addVulnerability({
  severity: 'high',
  impact: 0.8,
  exploitability: 0.7,
  description: 'Unpatched API vulnerability'
});
console.log(`Added vulnerability: ${vulnerability.id} (${vulnerability.severity})`);

// Get coherence score
const coherenceScore = attackSurfaceCoherence.getCoherenceScore();
console.log(`Attack Surface Coherence (Ψₐ): ${coherenceScore.toFixed(4)}`);

// Initialize market graph
const node1 = marketStressInfusion.addNode({
  id: 'bank-1',
  type: 'bank',
  size: 0.8,
  resilience: 0.6
});

const node2 = marketStressInfusion.addNode({
  id: 'exchange-1',
  type: 'exchange',
  size: 0.7,
  resilience: 0.5
});

const edge = marketStressInfusion.addEdge({
  source: 'bank-1',
  target: 'exchange-1',
  type: 'trading',
  weight: 0.7,
  bidirectional: true
});

// Inject stress
const stressEvent = marketStressInfusion.injectStress('bank-1', 0.6, {
  type: 'liquidity_shock',
  description: 'Sudden liquidity shortage'
});
console.log(`Injected stress: ${stressEvent.stressAmount.toFixed(2)} at ${stressEvent.nodeId}`);

// Get market stress
const marketStress = marketStressInfusion.getMarketStress();
console.log(`Market Stress Infusion (ΔΨₘ): ${marketStress.toFixed(4)}`);

// Stop components
transactionEntropy.stop();
attackSurfaceCoherence.stop();
marketStressInfusion.stop();

// Example 2: Using the basic CSFE system
console.log('\nExample 2: Using the basic CSFE system');

// Create CSFE system
const csfeSystem = createCSFESystem({
  transactionEntropyOptions: {
    enableLogging: true
  },
  attackSurfaceCoherenceOptions: {
    enableLogging: true
  },
  marketStressInfusionOptions: {
    enableLogging: true
  }
});

// Start components
csfeSystem.transactionEntropy.start();
csfeSystem.attackSurfaceCoherence.start();
csfeSystem.marketStressInfusion.start();

// Process transactions
const entropyResult2 = csfeSystem.transactionEntropy.processTransactions(transactions);
console.log(`Transaction Entropy (Tᵋ): ${entropyResult2.toFixed(4)}`);

// Add vulnerabilities
const vulnerability2 = csfeSystem.attackSurfaceCoherence.addVulnerability({
  severity: 'high',
  impact: 0.8,
  exploitability: 0.7,
  description: 'Unpatched API vulnerability'
});
console.log(`Added vulnerability: ${vulnerability2.id} (${vulnerability2.severity})`);

// Initialize market graph
csfeSystem.marketStressInfusion.addNode({
  id: 'bank-1',
  type: 'bank',
  size: 0.8,
  resilience: 0.6
});

csfeSystem.marketStressInfusion.addNode({
  id: 'exchange-1',
  type: 'exchange',
  size: 0.7,
  resilience: 0.5
});

csfeSystem.marketStressInfusion.addEdge({
  source: 'bank-1',
  target: 'exchange-1',
  type: 'trading',
  weight: 0.7,
  bidirectional: true
});

// Inject stress
const stressEvent2 = csfeSystem.marketStressInfusion.injectStress('bank-1', 0.6, {
  type: 'liquidity_shock',
  description: 'Sudden liquidity shortage'
});
console.log(`Injected stress: ${stressEvent2.stressAmount.toFixed(2)} at ${stressEvent2.nodeId}`);

// Stop components
csfeSystem.transactionEntropy.stop();
csfeSystem.attackSurfaceCoherence.stop();
csfeSystem.marketStressInfusion.stop();

// Example 3: Using the enhanced CSFE system
console.log('\nExample 3: Using the enhanced CSFE system');

// Create enhanced CSFE system
const enhancedCSFESystem = createEnhancedCSFESystem({
  enableLogging: true
});

// Start system
enhancedCSFESystem.start();

// Process financial data
const financialData = {
  transactions: transactions,
  vulnerabilities: [
    {
      severity: 'high',
      impact: 0.8,
      exploitability: 0.7,
      description: 'Unpatched API vulnerability'
    },
    {
      severity: 'medium',
      impact: 0.5,
      exploitability: 0.6,
      description: 'Weak encryption in payment system'
    }
  ],
  marketEvents: []
};

// Process data
const processingResult = enhancedCSFESystem.processFinancialData(financialData);
console.log(`Financial Entropy (Ψₜᶠ): ${processingResult.financialEntropy.toFixed(4)}`);

// Get financial risk assessment
const riskAssessment = enhancedCSFESystem.getFinancialRiskAssessment();
console.log('Financial Risk Assessment:');
console.log(`- Financial Entropy: ${riskAssessment.financialEntropy.toFixed(4)}`);
console.log(`- Risk Status: ${riskAssessment.riskStatus}`);
console.log(`- Transaction Entropy: ${riskAssessment.transactionEntropy.toFixed(4)}`);
console.log(`- Attack Surface Coherence: ${riskAssessment.attackSurfaceCoherence.toFixed(4)}`);
console.log(`- Market Stress: ${riskAssessment.marketStress.toFixed(4)}`);

// Get unified state
const unifiedState = enhancedCSFESystem.getUnifiedState();
console.log('Unified State Summary:');
console.log(`- Transaction Entropy Status: ${unifiedState.transactionEntropy.transactionEntropy.toFixed(4)}`);
console.log(`- Attack Surface Coherence Status: ${unifiedState.attackSurfaceCoherence.coherenceStatus}`);
console.log(`- Market Stress Status: ${unifiedState.marketStressInfusion.stressStatus}`);

// Get unified metrics
const unifiedMetrics = enhancedCSFESystem.getUnifiedMetrics();
console.log('Unified Metrics Summary:');
console.log(`- Transactions Processed: ${unifiedMetrics.transactionEntropy.transactionsProcessed}`);
console.log(`- Vulnerabilities Added: ${unifiedMetrics.attackSurfaceCoherence.vulnerabilitiesAdded}`);
console.log(`- Stress Events Detected: ${unifiedMetrics.marketStressInfusion.stressEventsDetected}`);

// Stop system
enhancedCSFESystem.stop();

console.log('\nCSFE example completed successfully!');
