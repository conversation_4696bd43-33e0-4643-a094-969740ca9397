# NovaFuse Universal API Connector - Authentication Manager

This directory contains the authentication manager for the NovaFuse Universal API Connector (UAC). The authentication manager is responsible for handling authentication for API connectors, supporting various authentication methods including API Key, Basic Auth, OAuth2, JWT, and AWS Signature V4.

## Structure

- `authentication-manager.js` - Main authentication manager class

## Authentication Types

The authentication manager supports the following authentication types:

- `API_KEY` - API key authentication
- `BASIC` - Basic authentication (username/password)
- `OAUTH2` - OAuth 2.0 authentication
- `JWT` - JWT token authentication
- `AWS_SIG_V4` - AWS Signature V4 authentication
- `CUSTOM` - Custom authentication
- `NONE` - No authentication

## Usage

### Initializing the Authentication Manager

```javascript
const authenticationManager = require('./auth/authentication-manager');

// Initialize the authentication manager
await authenticationManager.initialize();
```

### Storing Credentials

```javascript
// Store credentials for a connector
const credential = await authenticationManager.storeCredentials(
  'google-cloud-security-1.0', // Connector ID
  { apiKey: 'my-api-key' },    // Credentials
  'user-123',                  // Owner ID
  'Google Cloud Credentials',  // Credential name
  'Credentials for Google Cloud Security' // Description
);
```

### Retrieving Credentials

```javascript
// Get credentials by ID
const credentials = await authenticationManager.getCredentials('cred-12345');
```

### Deleting Credentials

```javascript
// Delete credentials by ID
await authenticationManager.deleteCredentials('cred-12345');
```

### Authenticating Requests

```javascript
// Get connector from registry
const connector = connectorRegistry.getConnector('google-cloud-security-1.0');

// Get credentials
const credentials = await authenticationManager.getCredentials('cred-12345');

// Create request
const request = {
  method: 'GET',
  url: 'https://api.example.com/data',
  headers: {
    'Content-Type': 'application/json'
  }
};

// Authenticate request
const authenticatedRequest = await authenticationManager.authenticateRequest(
  connector,
  credentials,
  request
);

// Send authenticated request
const response = await axios(authenticatedRequest);
```

### Testing Connections

```javascript
// Get connector from registry
const connector = connectorRegistry.getConnector('google-cloud-security-1.0');

// Get credentials
const credentials = await authenticationManager.getCredentials('cred-12345');

// Test connection
const testResult = await authenticationManager.testConnection(
  connector,
  credentials
);

if (testResult.success) {
  console.log('Connection test successful');
} else {
  console.error('Connection test failed:', testResult.message);
}
```

## Authentication Handlers

### API Key Authentication

The API key authentication handler supports the following placement options:

- `header` - Place the API key in a header (default)
- `query` - Place the API key in a query parameter
- `bearer` - Place the API key in the Authorization header as a Bearer token

Example connector configuration:

```javascript
{
  authentication: {
    type: 'API_KEY',
    fields: {
      apiKey: {
        type: 'string',
        description: 'API Key',
        required: true,
        sensitive: true
      }
    },
    placement: 'header',
    paramName: 'x-api-key'
  }
}
```

### Basic Authentication

Example connector configuration:

```javascript
{
  authentication: {
    type: 'BASIC',
    fields: {
      username: {
        type: 'string',
        description: 'Username',
        required: true
      },
      password: {
        type: 'string',
        description: 'Password',
        required: true,
        sensitive: true
      }
    }
  }
}
```

### OAuth2 Authentication

The OAuth2 authentication handler supports the following grant types:

- `client_credentials` - Client credentials grant
- `password` - Resource owner password credentials grant

Example connector configuration:

```javascript
{
  authentication: {
    type: 'OAUTH2',
    fields: {
      client_id: {
        type: 'string',
        description: 'Client ID',
        required: true
      },
      client_secret: {
        type: 'string',
        description: 'Client Secret',
        required: true,
        sensitive: true
      }
    },
    oauth2Config: {
      tokenUrl: 'https://api.example.com/oauth2/token',
      authorizationUrl: 'https://api.example.com/oauth2/authorize',
      scopes: ['read', 'write'],
      grantType: 'client_credentials'
    }
  }
}
```

### JWT Authentication

Example connector configuration:

```javascript
{
  authentication: {
    type: 'JWT',
    fields: {
      token: {
        type: 'string',
        description: 'JWT Token',
        required: true,
        sensitive: true
      }
    }
  }
}
```

### AWS Signature V4 Authentication

Example connector configuration:

```javascript
{
  authentication: {
    type: 'AWS_SIG_V4',
    fields: {
      accessKeyId: {
        type: 'string',
        description: 'AWS Access Key ID',
        required: true
      },
      secretAccessKey: {
        type: 'string',
        description: 'AWS Secret Access Key',
        required: true,
        sensitive: true
      },
      region: {
        type: 'string',
        description: 'AWS Region',
        required: true
      },
      service: {
        type: 'string',
        description: 'AWS Service',
        required: false,
        default: 'execute-api'
      }
    }
  }
}
```

### No Authentication

Example connector configuration:

```javascript
{
  authentication: {
    type: 'NONE'
  }
}
```

## Environment Variables

- `CREDENTIALS_ENCRYPTION_KEY` - Key for encrypting sensitive credential data
