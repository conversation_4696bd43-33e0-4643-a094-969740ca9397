/**
 * SkipLink Component
 * 
 * A component that allows keyboard users to skip navigation and jump directly to the main content.
 */

import React from 'react';
import PropTypes from 'prop-types';

/**
 * SkipLink component
 * 
 * @param {Object} props - Component props
 * @param {string} props.targetId - ID of the element to skip to
 * @param {string} [props.label='Skip to main content'] - Link text
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} SkipLink component
 */
const SkipLink = ({
  targetId,
  label = 'Skip to main content',
  className = '',
  style = {}
}) => {
  // Handle click event
  const handleClick = (event) => {
    event.preventDefault();
    
    // Find target element
    const targetElement = document.getElementById(targetId);
    
    if (targetElement) {
      // Focus the target element
      targetElement.setAttribute('tabindex', '-1');
      targetElement.focus();
      
      // Scroll to the target element
      targetElement.scrollIntoView({ behavior: 'smooth' });
    }
  };
  
  return (
    <a
      href={`#${targetId}`}
      onClick={handleClick}
      className={`
        fixed top-0 left-0 z-50 p-3 bg-blue-600 text-white font-medium
        transform -translate-y-full focus:translate-y-0
        transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400
        ${className}
      `}
      style={style}
      data-testid="skip-link"
    >
      {label}
    </a>
  );
};

SkipLink.propTypes = {
  targetId: PropTypes.string.isRequired,
  label: PropTypes.string,
  className: PropTypes.string,
  style: PropTypes.object
};

export default SkipLink;
