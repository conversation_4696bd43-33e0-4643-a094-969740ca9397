import React from 'react';
import DiagramTemplate from '../DiagramTemplate';

// Define the diagram data
const elements = [
  {
    id: 'finite-universe',
    top: 50,
    left: 250,
    width: 300,
    text: 'Finite Universe Paradigm',
    number: '1',
    bold: true,
    fontSize: '20px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'boundary-condition',
    top: 150,
    left: 100,
    width: 200,
    text: 'All real-world systems operate within finite boundaries',
    number: '2',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'nested-constraints',
    top: 150,
    left: 500,
    width: 200,
    text: 'These boundaries create nested constraint structures',
    number: '3',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'stability-patterns',
    top: 250,
    left: 100,
    width: 200,
    text: 'Nested constraints produce emergent stability patterns',
    number: '4',
    fontSize: '14px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'optimization',
    top: 250,
    left: 500,
    width: 200,
    text: 'Stability patterns can be detected, predicted, and optimized',
    number: '5',
    fontSize: '14px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'boundary-detector',
    top: 350,
    left: 50,
    width: 150,
    text: 'Domain Boundary Detector',
    number: '6',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'constraint-mapper',
    top: 350,
    left: 250,
    width: 150,
    text: 'Constraint Hierarchy Mapper',
    number: '7',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'pattern-detector',
    top: 350,
    left: 450,
    width: 150,
    text: 'Stability Pattern Detector',
    number: '8',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'optimization-engine',
    top: 350,
    left: 650,
    width: 150,
    text: 'Optimization Engine',
    number: '9',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'implementation',
    top: 450,
    left: 250,
    width: 300,
    text: 'Technical Implementation: Boundary Condition System',
    number: '10',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fffbe6'
  }
];

const connections = [
  // Connect Finite Universe to principles
  {
    start: { x: 300, y: 100 },
    end: { x: 200, y: 150 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 100 },
    end: { x: 600, y: 150 },
    type: 'arrow'
  },
  // Connect principles to outcomes
  {
    start: { x: 200, y: 200 },
    end: { x: 200, y: 250 },
    type: 'arrow'
  },
  {
    start: { x: 600, y: 200 },
    end: { x: 600, y: 250 },
    type: 'arrow'
  },
  // Connect outcomes to implementation components
  {
    start: { x: 125, y: 300 },
    end: { x: 125, y: 350 },
    type: 'arrow'
  },
  {
    start: { x: 200, y: 300 },
    end: { x: 325, y: 350 },
    type: 'arrow'
  },
  {
    start: { x: 600, y: 300 },
    end: { x: 525, y: 350 },
    type: 'arrow'
  },
  {
    start: { x: 675, y: 300 },
    end: { x: 725, y: 350 },
    type: 'arrow'
  },
  // Connect implementation components to implementation label
  {
    start: { x: 125, y: 400 },
    end: { x: 250, y: 450 },
    type: 'line'
  },
  {
    start: { x: 325, y: 400 },
    end: { x: 350, y: 450 },
    type: 'line'
  },
  {
    start: { x: 525, y: 400 },
    end: { x: 450, y: 450 },
    type: 'line'
  },
  {
    start: { x: 725, y: 400 },
    end: { x: 550, y: 450 },
    type: 'line'
  }
];

const FiniteUniverseParadigm: React.FC = () => {
  return (
    <DiagramTemplate 
      elements={elements} 
      connections={connections} 
      width="850px" 
      height="550px" 
    />
  );
};

export default FiniteUniverseParadigm;
