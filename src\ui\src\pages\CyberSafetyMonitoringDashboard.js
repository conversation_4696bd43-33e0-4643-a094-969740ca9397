import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Grid, 
  Paper, 
  Typography, 
  CircularProgress, 
  Button, 
  Tabs, 
  Tab, 
  IconButton, 
  Menu, 
  MenuItem,
  Snackbar,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Select,
  FormControl,
  InputLabel,
  TextField
} from '@mui/material';
import { 
  Refresh as RefreshIcon, 
  MoreVert as MoreVertIcon, 
  Download as DownloadIcon,
  Settings as SettingsIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import axios from 'axios';

// API base URL
const API_BASE_URL = '/api';

/**
 * CyberSafetyMonitoringDashboard
 * 
 * This component displays a dashboard for monitoring the Cyber-Safety visualization API.
 */
function CyberSafetyMonitoringDashboard() {
  // State for metrics data
  const [metricsData, setMetricsData] = useState({
    requestCount: [],
    responseTime: [],
    errorCount: [],
    dataSize: [],
    cacheHitRate: []
  });
  
  // State for alerts data
  const [alertsData, setAlertsData] = useState([]);
  
  // State for loading status
  const [loading, setLoading] = useState({
    metrics: false,
    alerts: false
  });
  
  // State for errors
  const [errors, setErrors] = useState({
    metrics: null,
    alerts: null
  });
  
  // State for active tab
  const [activeTab, setActiveTab] = useState(0);
  
  // State for time range
  const [timeRange, setTimeRange] = useState('1h');
  
  // State for visualization type filter
  const [visualizationTypeFilter, setVisualizationTypeFilter] = useState('all');
  
  // State for snackbar
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  
  // Load data on component mount and when time range or filter changes
  useEffect(() => {
    loadMetricsData();
    loadAlertsData();
    
    // Set up polling interval
    const interval = setInterval(() => {
      loadMetricsData();
      loadAlertsData();
    }, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(interval);
  }, [timeRange, visualizationTypeFilter]);
  
  // Load metrics data
  const loadMetricsData = async () => {
    try {
      setLoading(prev => ({ ...prev, metrics: true }));
      setErrors(prev => ({ ...prev, metrics: null }));
      
      const response = await axios.get(`${API_BASE_URL}/monitoring/metrics`, {
        params: {
          timeRange,
          visualizationType: visualizationTypeFilter !== 'all' ? visualizationTypeFilter : undefined
        }
      });
      
      setMetricsData(response.data);
      setLoading(prev => ({ ...prev, metrics: false }));
    } catch (error) {
      console.error('Error loading metrics data:', error);
      setErrors(prev => ({ ...prev, metrics: error.message }));
      setLoading(prev => ({ ...prev, metrics: false }));
    }
  };
  
  // Load alerts data
  const loadAlertsData = async () => {
    try {
      setLoading(prev => ({ ...prev, alerts: true }));
      setErrors(prev => ({ ...prev, alerts: null }));
      
      const response = await axios.get(`${API_BASE_URL}/monitoring/alerts`, {
        params: {
          timeRange,
          visualizationType: visualizationTypeFilter !== 'all' ? visualizationTypeFilter : undefined
        }
      });
      
      setAlertsData(response.data);
      setLoading(prev => ({ ...prev, alerts: false }));
    } catch (error) {
      console.error('Error loading alerts data:', error);
      setErrors(prev => ({ ...prev, alerts: error.message }));
      setLoading(prev => ({ ...prev, alerts: false }));
    }
  };
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  // Handle time range change
  const handleTimeRangeChange = (event) => {
    setTimeRange(event.target.value);
  };
  
  // Handle visualization type filter change
  const handleVisualizationTypeFilterChange = (event) => {
    setVisualizationTypeFilter(event.target.value);
  };
  
  // Handle refresh
  const handleRefresh = () => {
    loadMetricsData();
    loadAlertsData();
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };
  
  // Get status icon based on severity
  const getStatusIcon = (severity) => {
    switch (severity) {
      case 'critical':
        return <ErrorIcon color="error" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'info':
        return <InfoIcon color="info" />;
      default:
        return <CheckCircleIcon color="success" />;
    }
  };
  
  // Get status chip based on status
  const getStatusChip = (status) => {
    switch (status) {
      case 'active':
        return <Chip label="Active" color="error" size="small" />;
      case 'resolved':
        return <Chip label="Resolved" color="success" size="small" />;
      default:
        return <Chip label={status} color="default" size="small" />;
    }
  };
  
  // Render metrics dashboard
  const renderMetricsDashboard = () => {
    const { requestCount, responseTime, errorCount, dataSize, cacheHitRate } = metricsData;
    
    return (
      <Grid container spacing={2}>
        {/* Request Count */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>Request Count</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={requestCount}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="timestamp" tickFormatter={(timestamp) => new Date(timestamp).toLocaleTimeString()} />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [value, name]}
                  labelFormatter={(timestamp) => new Date(timestamp).toLocaleString()}
                />
                <Legend />
                <Line type="monotone" dataKey="triDomainTensor" stroke="#8884d8" name="Tri-Domain Tensor" />
                <Line type="monotone" dataKey="harmonyIndex" stroke="#82ca9d" name="Harmony Index" />
                <Line type="monotone" dataKey="riskControlFusion" stroke="#ffc658" name="Risk-Control Fusion" />
                <Line type="monotone" dataKey="resonanceSpectrogram" stroke="#ff8042" name="Resonance Spectrogram" />
                <Line type="monotone" dataKey="unifiedComplianceSecurity" stroke="#0088fe" name="Compliance-Security" />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        
        {/* Response Time */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>Response Time (ms)</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={responseTime}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="timestamp" tickFormatter={(timestamp) => new Date(timestamp).toLocaleTimeString()} />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [value, name]}
                  labelFormatter={(timestamp) => new Date(timestamp).toLocaleString()}
                />
                <Legend />
                <Line type="monotone" dataKey="triDomainTensor" stroke="#8884d8" name="Tri-Domain Tensor" />
                <Line type="monotone" dataKey="harmonyIndex" stroke="#82ca9d" name="Harmony Index" />
                <Line type="monotone" dataKey="riskControlFusion" stroke="#ffc658" name="Risk-Control Fusion" />
                <Line type="monotone" dataKey="resonanceSpectrogram" stroke="#ff8042" name="Resonance Spectrogram" />
                <Line type="monotone" dataKey="unifiedComplianceSecurity" stroke="#0088fe" name="Compliance-Security" />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        
        {/* Error Count */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>Error Count</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={errorCount}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="timestamp" tickFormatter={(timestamp) => new Date(timestamp).toLocaleTimeString()} />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [value, name]}
                  labelFormatter={(timestamp) => new Date(timestamp).toLocaleString()}
                />
                <Legend />
                <Bar dataKey="triDomainTensor" fill="#8884d8" name="Tri-Domain Tensor" />
                <Bar dataKey="harmonyIndex" fill="#82ca9d" name="Harmony Index" />
                <Bar dataKey="riskControlFusion" fill="#ffc658" name="Risk-Control Fusion" />
                <Bar dataKey="resonanceSpectrogram" fill="#ff8042" name="Resonance Spectrogram" />
                <Bar dataKey="unifiedComplianceSecurity" fill="#0088fe" name="Compliance-Security" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        
        {/* Cache Hit Rate */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>Cache Hit Rate</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={cacheHitRate}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {cacheHitRate.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.name === 'Hit' ? '#82ca9d' : '#ff8042'} />
                  ))}
                </Pie>
                <Tooltip formatter={(value, name) => [`${(value * 100).toFixed(2)}%`, name]} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>
    );
  };
  
  // Render alerts dashboard
  const renderAlertsDashboard = () => {
    return (
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>Alerts</Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Status</TableCell>
                <TableCell>Severity</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Time</TableCell>
                <TableCell>Details</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {alertsData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography variant="body2" color="textSecondary">
                      No alerts found
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                alertsData.map((alert, index) => (
                  <TableRow key={index}>
                    <TableCell>{getStatusChip(alert.status)}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {getStatusIcon(alert.severity)}
                        <Typography variant="body2" sx={{ ml: 1 }}>
                          {alert.severity}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>{alert.name}</TableCell>
                    <TableCell>{alert.description}</TableCell>
                    <TableCell>{new Date(alert.timestamp).toLocaleString()}</TableCell>
                    <TableCell>
                      <Button
                        size="small"
                        onClick={() => {
                          setSnackbar({
                            open: true,
                            message: JSON.stringify(alert.data, null, 2),
                            severity: 'info'
                          });
                        }}
                      >
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    );
  };
  
  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h4">Cyber-Safety Monitoring Dashboard</Typography>
        <Button 
          variant="contained" 
          startIcon={<RefreshIcon />}
          onClick={handleRefresh}
          disabled={loading.metrics || loading.alerts}
        >
          {loading.metrics || loading.alerts ? 'Refreshing...' : 'Refresh'}
        </Button>
      </Box>
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="Metrics" />
          <Tab label="Alerts" />
        </Tabs>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl variant="outlined" size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              onChange={handleTimeRangeChange}
              label="Time Range"
            >
              <MenuItem value="1h">Last Hour</MenuItem>
              <MenuItem value="6h">Last 6 Hours</MenuItem>
              <MenuItem value="24h">Last 24 Hours</MenuItem>
              <MenuItem value="7d">Last 7 Days</MenuItem>
              <MenuItem value="30d">Last 30 Days</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl variant="outlined" size="small" sx={{ minWidth: 180 }}>
            <InputLabel>Visualization Type</InputLabel>
            <Select
              value={visualizationTypeFilter}
              onChange={handleVisualizationTypeFilterChange}
              label="Visualization Type"
            >
              <MenuItem value="all">All Types</MenuItem>
              <MenuItem value="triDomainTensor">Tri-Domain Tensor</MenuItem>
              <MenuItem value="harmonyIndex">Harmony Index</MenuItem>
              <MenuItem value="riskControlFusion">Risk-Control Fusion</MenuItem>
              <MenuItem value="resonanceSpectrogram">Resonance Spectrogram</MenuItem>
              <MenuItem value="unifiedComplianceSecurity">Compliance-Security</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>
      
      {/* Show loading indicator or error */}
      {(loading.metrics || loading.alerts) && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}
      
      {(errors.metrics || errors.alerts) && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errors.metrics || errors.alerts}
        </Alert>
      )}
      
      {/* Show active tab content */}
      {!loading.metrics && !errors.metrics && activeTab === 0 && renderMetricsDashboard()}
      {!loading.alerts && !errors.alerts && activeTab === 1 && renderAlertsDashboard()}
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
            {snackbar.message}
          </pre>
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default CyberSafetyMonitoringDashboard;
