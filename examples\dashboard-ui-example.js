/**
 * Dashboard UI Example
 *
 * This example demonstrates how to use the Dashboard UI for the Finite Universe
 * Principle defense system. It creates a web-based UI for monitoring the system
 * in real-time.
 */

const {
  // Complete defense system
  createCompleteDefenseSystem,
  
  // Dashboard UI
  createDashboardUI
} = require('../src/quantum/finite-universe-principle');

/**
 * Start the dashboard UI
 */
async function startDashboardUI() {
  console.log('Starting Dashboard UI Example...');

  // Create complete defense system
  const defenseSystem = createCompleteDefenseSystem({
    enableLogging: true,
    enableMonitoring: true,
    strictMode: true
  });

  // Create dashboard UI
  const dashboardUI = createDashboardUI({
    port: 3000,
    updateInterval: 1000,
    enableLogging: true,
    enableRealTimeUpdates: true,
    dashboard: defenseSystem.monitoringDashboard
  });

  // Start the dashboard UI
  await dashboardUI.start();
  console.log(`Dashboard UI started on http://localhost:${dashboardUI.options.port}`);

  // Create mock NovaFuse components
  const mockNovaComponents = {
    novaConnect: createMockNovaConnect(),
    novaVision: createMockNovaVision(),
    novaDNA: createMockNovaDNA(),
    novaShield: createMockNovaShield()
  };

  // Register NovaFuse components
  defenseSystem.registerNovaComponents(mockNovaComponents);

  // Generate some test data
  generateTestData(defenseSystem);

  // Handle process termination
  process.on('SIGINT', () => {
    console.log('Stopping Dashboard UI...');
    dashboardUI.stop();
    defenseSystem.dispose();
    process.exit();
  });
}

/**
 * Generate test data for the dashboard
 * @param {Object} defenseSystem - Complete defense system
 */
function generateTestData(defenseSystem) {
  // Generate data at regular intervals
  setInterval(async () => {
    // Process cyber domain data
    await defenseSystem.processData({
      securityScore: Math.random() * 10,
      threatLevel: Math.random() * 5,
      encryptionStrength: Math.random() > 0.9 ? Infinity : Math.random() * 1000
    }, 'cyber');
    
    // Process financial domain data
    await defenseSystem.processData({
      balance: Math.random() * 10000,
      interestRate: Math.random() * 1.5,
      transactionVolume: Math.random() > 0.9 ? 1e20 : Math.random() * 1000000
    }, 'financial');
    
    // Process medical domain data
    await defenseSystem.processData({
      heartRate: Math.random() * 200,
      bloodPressure: Math.random() > 0.95 ? -50 : Math.random() * 200,
      temperature: Math.random() > 0.95 ? 60 : 36 + Math.random() * 5,
      patientName: 'John Doe',
      dateOfBirth: '1980-01-01'
    }, 'medical');
  }, 2000);
}

/**
 * Create a mock NovaConnect component
 * @returns {Object} - Mock NovaConnect component
 */
function createMockNovaConnect() {
  return {
    connect: async (config) => {
      console.log('NovaConnect.connect called');
      return { status: 'connected', config };
    },
    processPolicy: async (policy) => {
      console.log('NovaConnect.processPolicy called');
      return { status: 'processed', policy };
    }
  };
}

/**
 * Create a mock NovaVision component
 * @returns {Object} - Mock NovaVision component
 */
function createMockNovaVision() {
  return {
    analyze: async (data) => {
      console.log('NovaVision.analyze called');
      return { status: 'analyzed', data };
    }
  };
}

/**
 * Create a mock NovaDNA component
 * @returns {Object} - Mock NovaDNA component
 */
function createMockNovaDNA() {
  return {
    process: async (data) => {
      console.log('NovaDNA.process called');
      return { status: 'processed', data };
    }
  };
}

/**
 * Create a mock NovaShield component
 * @returns {Object} - Mock NovaShield component
 */
function createMockNovaShield() {
  return {
    protect: async (data) => {
      console.log('NovaShield.protect called');
      return { status: 'protected', data };
    }
  };
}

// Start the dashboard UI
startDashboardUI().catch(error => {
  console.error('Error starting Dashboard UI:', error);
});
