<form id="qaoa-form" onsubmit="submitQAOAForm(event)">
    <input type="hidden" name="type" value="qaoa">
    
    <div class="mb-3">
        <label for="qaoa-name" class="form-label">Experiment Name</label>
        <input type="text" class="form-control" id="qaoa-name" name="name" value="QAOA Protein Folding" required>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="qaoa-sequence" class="form-label">Protein Sequence</label>
                <input type="text" class="form-control" id="qaoa-sequence" name="sequence" value="HHPPHPPHPPHPPHPPHPPHPPHH" required>
                <div class="form-text">Use 'H' for hydrophobic and 'P' for polar amino acids</div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <label for="qaoa-backend" class="form-label">Quantum Backend</label>
                <select class="form-select" id="qaoa-backend" name="quantum_backend" required>
                    <option value="qiskit">Qiskit (local simulator)</option>
                    <option value="pennylane">PennyLane</option>
                    <option value="braket">Amazon Braket</option>
                </select>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-4">
            <div class="mb-3">
                <label for="qaoa-qubits" class="form-label">Number of Qubits</label>
                <input type="number" class="form-control" id="qaoa-qubits" name="num_qubits" min="2" max="20" value="4" required>
            </div>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                <label for="qaoa-layers" class="form-label">QAOA Layers (p)</label>
                <input type="number" class="form-control" id="qaoa-layers" name="p" min="1" max="10" value="1" required>
            </div>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                <label for="qaoa-optimizer" class="form-label">Optimizer</label>
                <select class="form-select" id="qaoa-optimizer" name="optimizer" required>
                    <option value="COBYLA">COBYLA</option>
                    <option value="SPSA">SPSA</option>
                    <option value="ADAM">ADAM</option>
                </select>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-4">
            <div class="mb-3">
                <label for="qaoa-shots" class="form-label">Shots</label>
                <input type="number" class="form-control" id="qaoa-shots" name="shots" min="100" step="100" value="1000" required>
            </div>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                <label for="qaoa-maxiter" class="form-label">Max Iterations</label>
                <input type="number" class="form-control" id="qaoa-maxiter" name="maxiter" min="10" max="1000" value="50" required>
            </div>
        </div>
    </div>
    
    <div class="d-flex justify-content-end">
        <button type="button" class="btn btn-outline-secondary me-2" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary" id="qaoa-submit">
            <span class="spinner-border spinner-border-sm d-none" id="qaoa-submit-spinner" role="status" aria-hidden="true"></span>
            <span id="qaoa-submit-text">Run Experiment</span>
        </button>
    </div>
</form>

<script>
function submitQAOAForm(event) {
    event.preventDefault();
    
    const form = event.target;
    const submitButton = form.querySelector('#qaoa-submit');
    const spinner = form.querySelector('#qaoa-submit-spinner');
    const buttonText = form.querySelector('#qaoa-submit-text');
    
    // Show loading state
    submitButton.disabled = true;
    spinner.classList.remove('d-none');
    buttonText.textContent = 'Running...';
    
    // Prepare form data
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    // Convert numeric fields to numbers
    data.num_qubits = parseInt(data.num_qubits);
    data.p = parseInt(data.p);
    data.shots = parseInt(data.shots);
    data.maxiter = parseInt(data.maxiter);
    
    // Submit via fetch
    fetch('/api/experiments', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        // Close modal on success
        const modal = bootstrap.Modal.getInstance(document.getElementById('newExperimentModal'));
        modal.hide();
        
        // Show success message
        showAlert('Experiment started successfully!', 'success');
        
        // Reset form
        form.reset();
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert(`Error: ${error.message}`, 'danger');
    })
    .finally(() => {
        // Reset button state
        submitButton.disabled = false;
        spinner.classList.add('d-none');
        buttonText.textContent = 'Run Experiment';
    });
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    const container = document.querySelector('.main-content');
    container.prepend(alertDiv);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = bootstrap.Alert.getOrCreateInstance(alertDiv);
        alert.close();
    }, 5000);
}
</script>
