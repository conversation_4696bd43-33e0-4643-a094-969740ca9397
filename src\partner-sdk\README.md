# NovaFuse Partner SDK

The NovaFuse Partner SDK enables partners to integrate NovaFuse's revolutionary Cyber-Safety technology into their existing solutions. This SDK provides a simple, consistent API for analyzing and remediating compliance, security, and Cyber-Safety issues using our advanced ML-enhanced CSDE engine.

## Features

- **ML-Enhanced CSDE Analysis**: Analyze compliance, GCP, and Cyber-Safety data using our ML-enhanced Cyber-Safety Dominance Equation (CSDE) engine with 95% accuracy (vs. 6% for traditional ML)
- **NovaFlowX ML Remediation**: Automatically remediate issues using the NovaFlowX ML engine with 95%+ success rate
- **Advanced ML Insights**: Get ML-enhanced insights with 5% average error (vs. 221.55% for traditional ML)
- **Intelligent Prioritization**: ML-driven prioritization of remediation actions based on impact and feasibility
- **Revenue Sharing**: Built-in 18/82 revenue sharing model (82% to partners)
- **White-Label UI Components**: Customizable UI components for embedding in partner applications
- **Comprehensive Reporting**: Generate detailed reports for clients

## Installation

```bash
npm install novafuse-partner-sdk
```

## Quick Start

```javascript
const NovaFusePartnerSDK = require('novafuse-partner-sdk');

// Initialize SDK
const sdk = new NovaFusePartnerSDK({
  partnerId: 'YOUR_PARTNER_ID',
  partnerKey: 'YOUR_PARTNER_KEY',
  partnerName: 'Your Company Name',
  environment: 'production' // or 'sandbox' for testing
});

// Analyze client environment
const analysisResult = await sdk.analyzeEnvironment({
  complianceData: { ... },
  gcpData: { ... },
  cyberSafetyData: { ... },
  metadata: {
    clientId: 'CLIENT_ID',
    scope: 'medium' // small, medium, large, enterprise
  }
});

// Remediate issues
const remediationResult = await sdk.remediateIssues({
  complianceData: { ... },
  gcpData: { ... },
  cyberSafetyData: { ... },
  metadata: {
    clientId: 'CLIENT_ID',
    scope: 'medium'
  }
}, {
  confidenceThreshold: 0.7,
  automationLevels: ['high', 'medium'],
  priorityLevels: ['critical', 'high', 'medium'],
  dryRun: false
});

// Generate report
const reportResult = await sdk.generateReport({
  metadata: {
    clientId: 'CLIENT_ID'
  }
}, {
  format: 'html',
  template: 'standard',
  includeRemediation: true
});

// Get revenue information
const revenueInfo = await sdk.getRevenueInfo({
  startDate: '2023-01-01',
  endDate: '2023-12-31'
});

// Get usage information
const usageInfo = await sdk.getUsageInfo({
  startDate: '2023-01-01',
  endDate: '2023-12-31'
});
```

## API Reference

### NovaFusePartnerSDK

#### Constructor

```javascript
const sdk = new NovaFusePartnerSDK({
  partnerId: 'YOUR_PARTNER_ID',
  partnerKey: 'YOUR_PARTNER_KEY',
  partnerName: 'Your Company Name',
  environment: 'production' // or 'sandbox' for testing
});
```

#### Methods

##### analyzeEnvironment(clientData)

Analyzes the client environment using the ML-enhanced CSDE engine, providing 95% accuracy and detailed insights.

```javascript
const analysisResult = await sdk.analyzeEnvironment({
  complianceData: { ... },
  gcpData: { ... },
  cyberSafetyData: { ... },
  metadata: {
    clientId: 'CLIENT_ID',
    scope: 'medium' // small, medium, large, enterprise
  }
});
```

##### remediateIssues(clientData, options)

Remediates issues in the client environment using the NovaFlowX ML engine, with 95%+ success rate and intelligent prioritization of actions.

```javascript
const remediationResult = await sdk.remediateIssues({
  complianceData: { ... },
  gcpData: { ... },
  cyberSafetyData: { ... },
  metadata: {
    clientId: 'CLIENT_ID',
    scope: 'medium'
  }
}, {
  confidenceThreshold: 0.7,
  automationLevels: ['high', 'medium'],
  priorityLevels: ['critical', 'high', 'medium'],
  dryRun: false
});
```

##### generateReport(clientData, options)

Generates a report for the client.

```javascript
const reportResult = await sdk.generateReport({
  metadata: {
    clientId: 'CLIENT_ID'
  }
}, {
  format: 'html',
  template: 'standard',
  includeRemediation: true
});
```

##### getRevenueInfo(options)

Gets revenue information for the partner.

```javascript
const revenueInfo = await sdk.getRevenueInfo({
  startDate: '2023-01-01',
  endDate: '2023-12-31',
  clientId: 'CLIENT_ID', // optional
  operation: 'analyze' // optional
});
```

##### getUsageInfo(options)

Gets usage information for the partner.

```javascript
const usageInfo = await sdk.getUsageInfo({
  startDate: '2023-01-01',
  endDate: '2023-12-31',
  clientId: 'CLIENT_ID', // optional
  operation: 'analyze' // optional
});
```

## UI Components

The NovaFuse Partner SDK includes a set of React components that can be embedded in partner applications:

```javascript
import {
  NovaFuseDashboard,
  NovaFuseRemediationPanel,
  NovaFuseReportGenerator,
  NovaFuseComplianceTracker
} from 'novafuse-partner-sdk/ui';

// Use in your React application
function App() {
  return (
    <div>
      <NovaFuseDashboard partnerId="YOUR_PARTNER_ID" clientId="CLIENT_ID" />
      <NovaFuseRemediationPanel partnerId="YOUR_PARTNER_ID" clientId="CLIENT_ID" />
      <NovaFuseReportGenerator partnerId="YOUR_PARTNER_ID" clientId="CLIENT_ID" />
      <NovaFuseComplianceTracker partnerId="YOUR_PARTNER_ID" clientId="CLIENT_ID" />
    </div>
  );
}
```

## Revenue Sharing

The NovaFuse Partner SDK includes a built-in 18/82 revenue sharing model, where 82% of the revenue goes to the partner and 18% goes to NovaFuse. The revenue is calculated based on the operations performed and the scope of the operations.

### Pricing Tiers

| Operation | Small | Medium | Large | Enterprise |
|-----------|-------|--------|-------|------------|
| Analyze   | $10   | $25    | $50   | $100       |
| Remediate | $25   | $50    | $100  | $200       |
| Report    | $5    | $5     | $5    | $5         |

### Revenue Calculation

The revenue is calculated as follows:

```
basePrice = pricingTier[operation][scope]
complexityMultiplier = 1.0 (adjusted based on complexity)
totalPrice = basePrice * complexityMultiplier
partnerRevenue = totalPrice * 0.82
novaFuseRevenue = totalPrice * 0.18
```

## Testing

To run the test script:

```bash
node test.js
```

This will generate test output in the `test-output` directory, including:

- `analysis_result.json`: Result of the analyzeEnvironment method
- `remediation_result.json`: Result of the remediateIssues method
- `report_result.json`: Result of the generateReport method
- `revenue_info.json`: Result of the getRevenueInfo method
- `usage_info.json`: Result of the getUsageInfo method
- `test_report.html`: HTML report of the test results

## License

NovaFuse Proprietary
