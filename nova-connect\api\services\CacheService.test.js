/**
 * Simplified Cache Service for Testing
 * 
 * This is a simplified version of the CacheService that only uses in-memory caching
 * for testing purposes.
 */

const NodeCache = require('node-cache');

class CacheService {
  constructor() {
    // Default configuration
    this.config = {
      defaultTtl: 300, // 5 minutes
      checkPeriod: 60, // 1 minute
      maxKeys: 10000,
      namespace: 'novafuse:',
      enabled: true
    };
    
    // Initialize memory cache
    this.memoryCache = new NodeCache({
      stdTTL: this.config.defaultTtl,
      checkperiod: this.config.checkPeriod,
      maxKeys: this.config.maxKeys,
      useClones: false
    });
    
    // Cache metrics
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0
    };
  }
  
  /**
   * Get a value from cache
   * @param {string} key - Cache key
   * @returns {Promise<any>} - Cached value or null
   */
  async get(key) {
    if (!this.config.enabled) {
      return null;
    }
    
    try {
      const namespacedKey = this._getNamespacedKey(key);
      
      // Get from memory cache
      const value = this.memoryCache.get(namespacedKey);
      
      // Update metrics
      if (value !== undefined && value !== null) {
        this.metrics.hits++;
        return value;
      } else {
        this.metrics.misses++;
        return null;
      }
    } catch (error) {
      console.error('Cache get error', { error, key });
      this.metrics.errors++;
      return null;
    }
  }
  
  /**
   * Set a value in cache
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in seconds (optional)
   * @returns {Promise<boolean>} - Whether the value was set
   */
  async set(key, value, ttl = this.config.defaultTtl) {
    if (!this.config.enabled) {
      return false;
    }
    
    try {
      const namespacedKey = this._getNamespacedKey(key);
      
      // Set in memory cache
      this.memoryCache.set(namespacedKey, value, ttl);
      
      // Update metrics
      this.metrics.sets++;
      return true;
    } catch (error) {
      console.error('Cache set error', { error, key });
      this.metrics.errors++;
      return false;
    }
  }
  
  /**
   * Delete a value from cache
   * @param {string} key - Cache key
   * @returns {Promise<boolean>} - Whether the value was deleted
   */
  async delete(key) {
    if (!this.config.enabled) {
      return false;
    }
    
    try {
      const namespacedKey = this._getNamespacedKey(key);
      
      // Delete from memory cache
      this.memoryCache.del(namespacedKey);
      
      // Update metrics
      this.metrics.deletes++;
      return true;
    } catch (error) {
      console.error('Cache delete error', { error, key });
      this.metrics.errors++;
      return false;
    }
  }
  
  /**
   * Get all keys matching a pattern
   * @param {string} pattern - The key pattern to match
   * @returns {Promise<string[]>} - Array of matching keys
   */
  async keys(pattern) {
    if (!this.config.enabled) {
      return [];
    }
    
    try {
      const allKeys = this.memoryCache.keys();
      const regex = new RegExp(pattern.replace('*', '.*'));
      return allKeys.filter(key => regex.test(key));
    } catch (error) {
      console.error('Cache keys error', { error, pattern });
      this.metrics.errors++;
      return [];
    }
  }
  
  /**
   * Flush all keys with a specific prefix
   * @param {string} prefix - The key prefix to flush
   * @returns {Promise<boolean>} - True if successful
   */
  async flush(prefix) {
    if (!this.config.enabled) {
      return false;
    }
    
    try {
      const keys = this.memoryCache.keys();
      const keysToDelete = keys.filter(key => key.startsWith(prefix));
      this.memoryCache.del(keysToDelete);
      return true;
    } catch (error) {
      console.error('Cache flush error', { error, prefix });
      this.metrics.errors++;
      return false;
    }
  }
  
  /**
   * Clear all cache
   * @returns {Promise<boolean>} - Whether the cache was cleared
   */
  async clear() {
    if (!this.config.enabled) {
      return false;
    }
    
    try {
      this.memoryCache.flushAll();
      return true;
    } catch (error) {
      console.error('Cache clear error', { error });
      this.metrics.errors++;
      return false;
    }
  }
  
  /**
   * Get namespaced key
   * @param {string} key - Original key
   * @returns {string} - Namespaced key
   * @private
   */
  _getNamespacedKey(key) {
    return `${this.config.namespace}${key}`;
  }
}

// Export a singleton instance
module.exports = new CacheService();
