/**
 * Control Action Execution
 * 
 * This module implements the Control Action Execution component of the Governor.
 * It executes control actions to respond to entropy changes and threshold violations.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * ControlActionExecution class
 */
class ControlActionExecution extends EventEmitter {
  /**
   * Create a new ControlActionExecution instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      enableMetrics: true,
      historySize: 100, // Number of historical data points to keep
      executionTimeout: 30000, // ms
      maxConcurrentActions: 5,
      defaultPriority: 'medium', // low, medium, high, critical
      ...options
    };
    
    // Initialize state
    this.state = {
      registeredActions: new Map(), // id -> action
      actionQueue: [], // Queued actions
      runningActions: new Map(), // id -> action
      completedActions: [],
      actionHistory: [],
      isRunning: false,
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      actionsRegistered: 0,
      actionsExecuted: 0,
      actionsSucceeded: 0,
      actionsFailed: 0,
      actionsTimedOut: 0,
      averageExecutionTimeMs: 0
    };
    
    if (this.options.enableLogging) {
      console.log('ControlActionExecution initialized');
    }
  }
  
  /**
   * Start the control action execution
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('ControlActionExecution is already running');
      }
      return false;
    }
    
    this.state.isRunning = true;
    this._startQueueProcessor();
    
    if (this.options.enableLogging) {
      console.log('ControlActionExecution started');
    }
    
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the control action execution
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('ControlActionExecution is not running');
      }
      return false;
    }
    
    this.state.isRunning = false;
    this._stopQueueProcessor();
    
    // Cancel all running actions
    for (const [actionId, action] of this.state.runningActions.entries()) {
      this._cancelAction(actionId, 'system_shutdown');
    }
    
    if (this.options.enableLogging) {
      console.log('ControlActionExecution stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Register control action
   * @param {Object} action - Action definition
   * @returns {Object} - Registered action
   */
  registerAction(action) {
    const startTime = performance.now();
    
    if (!action || typeof action !== 'object') {
      throw new Error('Action must be an object');
    }
    
    if (!action.id) {
      action.id = `action-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    
    if (!action.name) {
      throw new Error('Action must have a name');
    }
    
    if (!action.handler || typeof action.handler !== 'function') {
      throw new Error('Action must have a handler function');
    }
    
    // Set default values
    action = {
      domains: ['universal'], // universal, cyber, financial, biological
      triggers: [], // Conditions that trigger this action
      priority: this.options.defaultPriority,
      description: `Control action: ${action.name}`,
      parameters: {},
      timeout: this.options.executionTimeout,
      registeredAt: Date.now(),
      ...action
    };
    
    // Add to registered actions
    this.state.registeredActions.set(action.id, action);
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.actionsRegistered++;
    
    // Emit event
    this.emit('action-registered', {
      actionId: action.id,
      name: action.name,
      domains: action.domains,
      priority: action.priority,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ControlActionExecution: Registered action ${action.id} (${action.name})`);
    }
    
    return action;
  }
  
  /**
   * Unregister control action
   * @param {string} actionId - Action ID
   * @returns {boolean} - Success status
   */
  unregisterAction(actionId) {
    if (!actionId || !this.state.registeredActions.has(actionId)) {
      return false;
    }
    
    // Get action
    const action = this.state.registeredActions.get(actionId);
    
    // Remove from registered actions
    this.state.registeredActions.delete(actionId);
    
    // Emit event
    this.emit('action-unregistered', {
      actionId: action.id,
      name: action.name,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ControlActionExecution: Unregistered action ${action.id} (${action.name})`);
    }
    
    return true;
  }
  
  /**
   * Execute control action
   * @param {string} actionId - Action ID
   * @param {Object} parameters - Action parameters
   * @param {Object} context - Execution context
   * @returns {string} - Execution ID
   */
  executeAction(actionId, parameters = {}, context = {}) {
    const startTime = performance.now();
    
    if (!this.state.isRunning) {
      throw new Error('ControlActionExecution is not running');
    }
    
    if (!actionId || !this.state.registeredActions.has(actionId)) {
      throw new Error(`Action ${actionId} not found`);
    }
    
    // Get action
    const action = this.state.registeredActions.get(actionId);
    
    // Create execution context
    const executionId = `exec-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const executionContext = {
      executionId,
      actionId,
      name: action.name,
      parameters: {
        ...action.parameters,
        ...parameters
      },
      priority: action.priority,
      context: {
        ...context,
        timestamp: Date.now()
      },
      status: 'queued',
      queuedAt: Date.now(),
      timeout: action.timeout || this.options.executionTimeout
    };
    
    // Add to queue
    this.state.actionQueue.push(executionContext);
    
    // Sort queue by priority
    this._sortQueue();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    // Emit event
    this.emit('action-queued', {
      executionId,
      actionId,
      name: action.name,
      priority: action.priority,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ControlActionExecution: Queued action ${action.id} (${action.name})`);
    }
    
    return executionId;
  }
  
  /**
   * Cancel control action execution
   * @param {string} executionId - Execution ID
   * @param {string} reason - Cancellation reason
   * @returns {boolean} - Success status
   */
  cancelExecution(executionId, reason = 'user_request') {
    // Check if action is in queue
    const queueIndex = this.state.actionQueue.findIndex(a => a.executionId === executionId);
    
    if (queueIndex >= 0) {
      // Remove from queue
      const action = this.state.actionQueue.splice(queueIndex, 1)[0];
      
      // Update action status
      action.status = 'cancelled';
      action.cancelledAt = Date.now();
      action.cancelReason = reason;
      
      // Add to history
      this.state.actionHistory.push(action);
      
      // Limit history size
      if (this.state.actionHistory.length > this.options.historySize) {
        this.state.actionHistory.shift();
      }
      
      // Emit event
      this.emit('action-cancelled', {
        executionId,
        actionId: action.actionId,
        name: action.name,
        reason,
        timestamp: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`ControlActionExecution: Cancelled queued action ${action.actionId} (${action.name})`);
      }
      
      return true;
    }
    
    // Check if action is running
    if (this.state.runningActions.has(executionId)) {
      return this._cancelAction(executionId, reason);
    }
    
    return false;
  }
  
  /**
   * Get registered actions
   * @param {string} domain - Domain filter (all domains if not specified)
   * @returns {Array} - Registered actions
   */
  getRegisteredActions(domain) {
    const actions = Array.from(this.state.registeredActions.values());
    
    if (domain) {
      return actions.filter(a => a.domains.includes(domain));
    }
    
    return actions;
  }
  
  /**
   * Get action queue
   * @returns {Array} - Action queue
   */
  getActionQueue() {
    return [...this.state.actionQueue];
  }
  
  /**
   * Get running actions
   * @returns {Array} - Running actions
   */
  getRunningActions() {
    return Array.from(this.state.runningActions.values());
  }
  
  /**
   * Get completed actions
   * @param {number} limit - Maximum number of actions to return
   * @returns {Array} - Completed actions
   */
  getCompletedActions(limit = 10) {
    return this.state.completedActions.slice(0, limit);
  }
  
  /**
   * Get action history
   * @param {number} limit - Maximum number of history items to return
   * @returns {Array} - Action history
   */
  getActionHistory(limit = 10) {
    return this.state.actionHistory.slice(0, limit);
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Start queue processor
   * @private
   */
  _startQueueProcessor() {
    if (this._queueInterval) {
      clearInterval(this._queueInterval);
    }
    
    this._queueInterval = setInterval(() => {
      if (this.state.isRunning) {
        this._processQueue();
      }
    }, 100); // Check queue every 100ms
  }
  
  /**
   * Stop queue processor
   * @private
   */
  _stopQueueProcessor() {
    if (this._queueInterval) {
      clearInterval(this._queueInterval);
      this._queueInterval = null;
    }
  }
  
  /**
   * Process action queue
   * @private
   */
  _processQueue() {
    // If queue is empty or max concurrent actions reached, do nothing
    if (
      this.state.actionQueue.length === 0 ||
      this.state.runningActions.size >= this.options.maxConcurrentActions
    ) {
      return;
    }
    
    // Get next action from queue
    const action = this.state.actionQueue.shift();
    
    // Execute action
    this._executeAction(action);
  }
  
  /**
   * Execute action
   * @param {Object} action - Action to execute
   * @private
   */
  _executeAction(action) {
    const startTime = performance.now();
    
    // Get registered action
    const registeredAction = this.state.registeredActions.get(action.actionId);
    
    if (!registeredAction) {
      // Action was unregistered while in queue
      action.status = 'failed';
      action.error = 'Action was unregistered';
      action.completedAt = Date.now();
      
      // Add to history
      this.state.actionHistory.push(action);
      
      // Limit history size
      if (this.state.actionHistory.length > this.options.historySize) {
        this.state.actionHistory.shift();
      }
      
      // Update metrics
      this.metrics.actionsFailed++;
      
      // Emit event
      this.emit('action-failed', {
        executionId: action.executionId,
        actionId: action.actionId,
        name: action.name,
        error: action.error,
        timestamp: Date.now()
      });
      
      return;
    }
    
    // Update action status
    action.status = 'running';
    action.startedAt = Date.now();
    
    // Add to running actions
    this.state.runningActions.set(action.executionId, action);
    
    // Set timeout
    const timeoutId = setTimeout(() => {
      this._handleActionTimeout(action.executionId);
    }, action.timeout);
    
    // Emit event
    this.emit('action-started', {
      executionId: action.executionId,
      actionId: action.actionId,
      name: action.name,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ControlActionExecution: Started action ${action.actionId} (${action.name})`);
    }
    
    // Execute action handler
    try {
      const handlerPromise = Promise.resolve(registeredAction.handler(action.parameters, action.context));
      
      handlerPromise
        .then(result => {
          // Clear timeout
          clearTimeout(timeoutId);
          
          // Handle success
          this._handleActionSuccess(action.executionId, result);
        })
        .catch(error => {
          // Clear timeout
          clearTimeout(timeoutId);
          
          // Handle error
          this._handleActionError(action.executionId, error);
        });
    } catch (error) {
      // Clear timeout
      clearTimeout(timeoutId);
      
      // Handle synchronous error
      this._handleActionError(action.executionId, error);
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.actionsExecuted++;
  }
  
  /**
   * Handle action success
   * @param {string} executionId - Execution ID
   * @param {*} result - Action result
   * @private
   */
  _handleActionSuccess(executionId, result) {
    // Check if action is still running
    if (!this.state.runningActions.has(executionId)) {
      return;
    }
    
    // Get action
    const action = this.state.runningActions.get(executionId);
    
    // Update action status
    action.status = 'completed';
    action.completedAt = Date.now();
    action.result = result;
    action.executionTimeMs = action.completedAt - action.startedAt;
    
    // Remove from running actions
    this.state.runningActions.delete(executionId);
    
    // Add to completed actions
    this.state.completedActions.unshift(action);
    
    // Limit completed actions
    if (this.state.completedActions.length > this.options.historySize) {
      this.state.completedActions.pop();
    }
    
    // Add to history
    this.state.actionHistory.push(action);
    
    // Limit history size
    if (this.state.actionHistory.length > this.options.historySize) {
      this.state.actionHistory.shift();
    }
    
    // Update metrics
    this.metrics.actionsSucceeded++;
    this._updateAverageExecutionTime(action.executionTimeMs);
    
    // Emit event
    this.emit('action-completed', {
      executionId,
      actionId: action.actionId,
      name: action.name,
      result,
      executionTimeMs: action.executionTimeMs,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ControlActionExecution: Completed action ${action.actionId} (${action.name}) in ${action.executionTimeMs}ms`);
    }
  }
  
  /**
   * Handle action error
   * @param {string} executionId - Execution ID
   * @param {Error} error - Error object
   * @private
   */
  _handleActionError(executionId, error) {
    // Check if action is still running
    if (!this.state.runningActions.has(executionId)) {
      return;
    }
    
    // Get action
    const action = this.state.runningActions.get(executionId);
    
    // Update action status
    action.status = 'failed';
    action.completedAt = Date.now();
    action.error = error.message || 'Unknown error';
    action.executionTimeMs = action.completedAt - action.startedAt;
    
    // Remove from running actions
    this.state.runningActions.delete(executionId);
    
    // Add to completed actions
    this.state.completedActions.unshift(action);
    
    // Limit completed actions
    if (this.state.completedActions.length > this.options.historySize) {
      this.state.completedActions.pop();
    }
    
    // Add to history
    this.state.actionHistory.push(action);
    
    // Limit history size
    if (this.state.actionHistory.length > this.options.historySize) {
      this.state.actionHistory.shift();
    }
    
    // Update metrics
    this.metrics.actionsFailed++;
    this._updateAverageExecutionTime(action.executionTimeMs);
    
    // Emit event
    this.emit('action-failed', {
      executionId,
      actionId: action.actionId,
      name: action.name,
      error: action.error,
      executionTimeMs: action.executionTimeMs,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ControlActionExecution: Failed action ${action.actionId} (${action.name}): ${action.error}`);
    }
  }
  
  /**
   * Handle action timeout
   * @param {string} executionId - Execution ID
   * @private
   */
  _handleActionTimeout(executionId) {
    // Check if action is still running
    if (!this.state.runningActions.has(executionId)) {
      return;
    }
    
    // Get action
    const action = this.state.runningActions.get(executionId);
    
    // Update action status
    action.status = 'timeout';
    action.completedAt = Date.now();
    action.error = 'Action timed out';
    action.executionTimeMs = action.completedAt - action.startedAt;
    
    // Remove from running actions
    this.state.runningActions.delete(executionId);
    
    // Add to completed actions
    this.state.completedActions.unshift(action);
    
    // Limit completed actions
    if (this.state.completedActions.length > this.options.historySize) {
      this.state.completedActions.pop();
    }
    
    // Add to history
    this.state.actionHistory.push(action);
    
    // Limit history size
    if (this.state.actionHistory.length > this.options.historySize) {
      this.state.actionHistory.shift();
    }
    
    // Update metrics
    this.metrics.actionsTimedOut++;
    this._updateAverageExecutionTime(action.executionTimeMs);
    
    // Emit event
    this.emit('action-timeout', {
      executionId,
      actionId: action.actionId,
      name: action.name,
      executionTimeMs: action.executionTimeMs,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ControlActionExecution: Timed out action ${action.actionId} (${action.name}) after ${action.executionTimeMs}ms`);
    }
  }
  
  /**
   * Cancel running action
   * @param {string} executionId - Execution ID
   * @param {string} reason - Cancellation reason
   * @returns {boolean} - Success status
   * @private
   */
  _cancelAction(executionId, reason) {
    // Check if action is running
    if (!this.state.runningActions.has(executionId)) {
      return false;
    }
    
    // Get action
    const action = this.state.runningActions.get(executionId);
    
    // Update action status
    action.status = 'cancelled';
    action.completedAt = Date.now();
    action.cancelReason = reason;
    action.executionTimeMs = action.completedAt - action.startedAt;
    
    // Remove from running actions
    this.state.runningActions.delete(executionId);
    
    // Add to completed actions
    this.state.completedActions.unshift(action);
    
    // Limit completed actions
    if (this.state.completedActions.length > this.options.historySize) {
      this.state.completedActions.pop();
    }
    
    // Add to history
    this.state.actionHistory.push(action);
    
    // Limit history size
    if (this.state.actionHistory.length > this.options.historySize) {
      this.state.actionHistory.shift();
    }
    
    // Update metrics
    this._updateAverageExecutionTime(action.executionTimeMs);
    
    // Emit event
    this.emit('action-cancelled', {
      executionId,
      actionId: action.actionId,
      name: action.name,
      reason,
      executionTimeMs: action.executionTimeMs,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ControlActionExecution: Cancelled running action ${action.actionId} (${action.name}): ${reason}`);
    }
    
    return true;
  }
  
  /**
   * Sort action queue by priority
   * @private
   */
  _sortQueue() {
    const priorityValues = {
      low: 0,
      medium: 1,
      high: 2,
      critical: 3
    };
    
    this.state.actionQueue.sort((a, b) => {
      // Sort by priority (higher priority first)
      const priorityDiff = priorityValues[b.priority] - priorityValues[a.priority];
      
      if (priorityDiff !== 0) {
        return priorityDiff;
      }
      
      // If same priority, sort by queue time (older first)
      return a.queuedAt - b.queuedAt;
    });
  }
  
  /**
   * Update average execution time
   * @param {number} executionTimeMs - Execution time in milliseconds
   * @private
   */
  _updateAverageExecutionTime(executionTimeMs) {
    const totalExecutions = this.metrics.actionsSucceeded + this.metrics.actionsFailed + this.metrics.actionsTimedOut;
    
    if (totalExecutions === 1) {
      // First execution
      this.metrics.averageExecutionTimeMs = executionTimeMs;
    } else {
      // Update running average
      this.metrics.averageExecutionTimeMs = (
        (this.metrics.averageExecutionTimeMs * (totalExecutions - 1)) + executionTimeMs
      ) / totalExecutions;
    }
  }
}

module.exports = ControlActionExecution;
