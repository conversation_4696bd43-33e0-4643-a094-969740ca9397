config:
  target: "http://localhost:3003"
  phases:
    - duration: 60
      arrivalRate: 5
      rampTo: 50
      name: "Warm up phase"
    - duration: 120
      arrivalRate: 50
      name: "Sustained load phase"
    - duration: 60
      arrivalRate: 50
      rampTo: 100
      name: "High load phase"
  processor: "./load-test-functions.js"

scenarios:
  - name: "Execute AWS Security Hub connector"
    weight: 40
    flow:
      - function: "generateCredentials"
      - post:
          url: "/execute/{{ connectorId }}/getFindings"
          json:
            credentialId: "{{ credentialId }}"
            parameters: {}
            userId: "{{ userId }}"
          capture:
            - json: "$.data.data.data.findingIds[0]"
              as: "findingId"
      - think: 1

  - name: "Execute Okta connector"
    weight: 30
    flow:
      - function: "generateCredentials"
      - post:
          url: "/execute/{{ connectorId }}/getUsers"
          json:
            credentialId: "{{ credentialId }}"
            parameters: {}
            userId: "{{ userId }}"
      - think: 1

  - name: "Execute Jira connector"
    weight: 30
    flow:
      - function: "generateCredentials"
      - post:
          url: "/execute/{{ connectorId }}/getIssues"
          json:
            credentialId: "{{ credentialId }}"
            parameters: {}
            userId: "{{ userId }}"
      - think: 1
