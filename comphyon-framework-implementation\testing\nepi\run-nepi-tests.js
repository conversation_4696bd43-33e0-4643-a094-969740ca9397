/**
 * NEPI Test Runner
 *
 * This script runs all NEPI tests for the Comphyon framework.
 */

const { NEPITestRunner } = require('./nepi-test-framework');
const { createFoundationalPhysicsTestSuite } = require('./foundational-physics-tests');
const { createMeterTestSuite } = require('./meter-tests');
const { createAdversarialTestSuite } = require('./adversarial-tests');
const fs = require('fs');
const path = require('path');

/**
 * Run all NEPI tests
 */
async function runNEPITests() {
  console.log('=== NEPI Testing Framework ===\n');
  console.log('A Rigorous Regimen for Emergent Intelligence Testing\n');

  // Create test runner
  console.log('Creating NEPI Test Runner...');
  const testRunner = new NEPITestRunner({
    enableLogging: true,
    parallelSuites: false
  });

  // Add test suites
  console.log('Adding Foundational Physics Test Suite...');
  testRunner.addSuite(createFoundationalPhysicsTestSuite());

  console.log('Adding Meter Test Suite...');
  testRunner.addSuite(createMeterTestSuite());

  console.log('Adding Adversarial Test Suite...');
  testRunner.addSuite(createAdversarialTestSuite());

  // Run tests
  try {
    console.log('\nRunning NEPI tests...');
    const result = await testRunner.run();

    console.log('\nGenerating NEPI test report...');
    // Generate report
    generateNEPIReport(result);

    // Exit with appropriate code
    process.exit(result.failed > 0 ? 1 : 0);
  } catch (error) {
    console.error('Error running NEPI tests:', error);
    process.exit(1);
  }
}

/**
 * Generate NEPI test report
 * @param {Object} data - Test result data
 */
function generateNEPIReport(data) {
  // Create report data
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      total: data.total,
      passed: data.passed,
      failed: data.failed,
      skipped: data.skipped,
      duration: data.duration
    },
    coherenceImpact: data.coherenceImpact,
    entropyMetrics: data.entropyMetrics,
    ethicalCompliance: data.ethicalCompliance,
    testingLayers: data.testingLayers,
    testingTypes: data.testingTypes,
    domains: data.domains,
    suites: data.suites
  };

  // Generate HTML report
  const htmlReport = generateNEPIHtmlReport(reportData);

  // Create reports directory if it doesn't exist
  const reportsDir = path.join(__dirname, '..', '..', 'reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir);
  }

  // Write HTML report
  const reportPath = path.join(reportsDir, `nepi-test-report-${new Date().toISOString().replace(/:/g, '-')}.html`);
  fs.writeFileSync(reportPath, htmlReport);

  console.log(`\nNEPI test report generated: ${reportPath}`);
}

/**
 * Generate NEPI HTML report
 * @param {Object} data - Report data
 * @returns {string} - HTML report
 */
function generateNEPIHtmlReport(data) {
  const passRate = data.summary.total > 0 ? Math.round((data.summary.passed / data.summary.total) * 100) : 0;

  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NEPI Testing Framework Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .summary {
      background-color: #f5f5f5;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .summary-item {
      text-align: center;
      padding: 15px;
      border-radius: 5px;
    }
    .total {
      background-color: #e6f2ff;
    }
    .passed {
      background-color: #e6ffe6;
    }
    .failed {
      background-color: #ffe6e6;
    }
    .skipped {
      background-color: #fff9e6;
    }
    .duration {
      background-color: #e6e6ff;
    }
    .pass-rate {
      background-color: ${passRate >= 90 ? '#e6ffe6' : passRate >= 70 ? '#fff9e6' : '#ffe6e6'};
    }
    .coherence-impact {
      margin-bottom: 30px;
      padding: 20px;
      border-radius: 5px;
      background-color: #f5f5f5;
    }
    .coherence-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .coherence-item {
      text-align: center;
      padding: 15px;
      border-radius: 5px;
    }
    .positive {
      background-color: #e6ffe6;
    }
    .negative {
      background-color: #ffe6e6;
    }
    .neutral {
      background-color: #e6f2ff;
    }
    .entropy-metrics {
      margin-bottom: 30px;
      padding: 20px;
      border-radius: 5px;
      background-color: #f5f5f5;
    }
    .entropy-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .entropy-domain {
      padding: 15px;
      border-radius: 5px;
      background-color: #fff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .entropy-metrics-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }
    .entropy-metrics-table th, .entropy-metrics-table td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    .entropy-metrics-table th {
      background-color: #f2f2f2;
    }
    .testing-distribution {
      margin-bottom: 30px;
      padding: 20px;
      border-radius: 5px;
      background-color: #f5f5f5;
    }
    .distribution-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .distribution-section {
      padding: 15px;
      border-radius: 5px;
      background-color: #fff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .distribution-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }
    .distribution-table th, .distribution-table td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    .distribution-table th {
      background-color: #f2f2f2;
    }
    .ethical-compliance {
      margin-bottom: 30px;
      padding: 20px;
      border-radius: 5px;
      background-color: ${data.ethicalCompliance ? '#e6ffe6' : '#ffe6e6'};
      text-align: center;
    }
    .ethical-compliance h2 {
      color: ${data.ethicalCompliance ? '#4caf50' : '#f44336'};
    }
    .ethical-compliance-icon {
      font-size: 48px;
      margin-bottom: 10px;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      color: #777;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <h1>NEPI Testing Framework Report</h1>
  <p>Generated on: ${new Date(data.timestamp).toLocaleString()}</p>

  <div class="summary">
    <h2>Test Summary</h2>
    <div class="summary-grid">
      <div class="summary-item total">
        <h3>Total Tests</h3>
        <p>${data.summary.total}</p>
      </div>
      <div class="summary-item passed">
        <h3>Passed</h3>
        <p>${data.summary.passed}</p>
      </div>
      <div class="summary-item failed">
        <h3>Failed</h3>
        <p>${data.summary.failed}</p>
      </div>
      <div class="summary-item skipped">
        <h3>Skipped</h3>
        <p>${data.summary.skipped}</p>
      </div>
      <div class="summary-item duration">
        <h3>Duration</h3>
        <p>${(data.summary.duration / 1000).toFixed(2)}s</p>
      </div>
      <div class="summary-item pass-rate">
        <h3>Pass Rate</h3>
        <p>${passRate}%</p>
      </div>
    </div>
  </div>

  <div class="ethical-compliance">
    <div class="ethical-compliance-icon">
      ${data.ethicalCompliance ? '✅' : '❌'}
    </div>
    <h2>Ethical Compliance</h2>
    <p>${data.ethicalCompliance ? 'All tests comply with ethical constraints' : 'Some tests violate ethical constraints'}</p>
  </div>

  <div class="coherence-impact">
    <h2>Coherence Impact</h2>
    <div class="coherence-grid">
      <div class="coherence-item positive">
        <h3>Positive Impact</h3>
        <p>${data.coherenceImpact.positive}</p>
      </div>
      <div class="coherence-item negative">
        <h3>Negative Impact</h3>
        <p>${data.coherenceImpact.negative}</p>
      </div>
      <div class="coherence-item neutral">
        <h3>Neutral Impact</h3>
        <p>${data.coherenceImpact.neutral}</p>
      </div>
    </div>
  </div>

  <div class="entropy-metrics">
    <h2>Entropy Metrics</h2>
    <div class="entropy-grid">
      ${Object.keys(data.entropyMetrics.delta).map(domain => `
        <div class="entropy-domain">
          <h3>${domain.charAt(0).toUpperCase() + domain.slice(1)} Domain</h3>
          <table class="entropy-metrics-table">
            <thead>
              <tr>
                <th>Metric</th>
                <th>Value</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Before</td>
                <td>${data.entropyMetrics.before[domain].toFixed(4)}</td>
              </tr>
              <tr>
                <td>After</td>
                <td>${data.entropyMetrics.after[domain].toFixed(4)}</td>
              </tr>
              <tr>
                <td>Delta</td>
                <td>${data.entropyMetrics.delta[domain].toFixed(4)}</td>
              </tr>
            </tbody>
          </table>
        </div>
      `).join('')}
    </div>
  </div>

  <div class="testing-distribution">
    <h2>Testing Distribution</h2>
    <div class="distribution-grid">
      <div class="distribution-section">
        <h3>Testing Layers</h3>
        <table class="distribution-table">
          <thead>
            <tr>
              <th>Layer</th>
              <th>Count</th>
            </tr>
          </thead>
          <tbody>
            ${Object.entries(data.testingLayers).map(([layer, count]) => `
              <tr>
                <td>${layer}</td>
                <td>${count}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>

      <div class="distribution-section">
        <h3>Testing Types</h3>
        <table class="distribution-table">
          <thead>
            <tr>
              <th>Type</th>
              <th>Count</th>
            </tr>
          </thead>
          <tbody>
            ${Object.entries(data.testingTypes).map(([type, count]) => `
              <tr>
                <td>${type}</td>
                <td>${count}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>

      <div class="distribution-section">
        <h3>Domains</h3>
        <table class="distribution-table">
          <thead>
            <tr>
              <th>Domain</th>
              <th>Count</th>
            </tr>
          </thead>
          <tbody>
            ${Object.entries(data.domains).map(([domain, count]) => `
              <tr>
                <td>${domain}</td>
                <td>${count}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <footer>
    <p>NEPI Testing Framework - Powered by Comphyology</p>
  </footer>
</body>
</html>`;
}

// Run NEPI tests
runNEPITests();
