/**
 * NovaVision Hub Service Worker
 * 
 * This service worker provides offline support for the NovaVision Hub.
 */

// Cache name
const CACHE_NAME = 'novavision-hub-cache-v1';

// Assets to cache
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/static/js/main.js',
  '/static/css/main.css',
  '/static/media/logo.png',
  '/manifest.json'
];

// API endpoints to cache
const API_CACHE_CONFIG = [
  {
    urlPattern: /\/api\/dashboard/,
    strategy: 'staleWhileRevalidate',
    cacheName: 'dashboard-data-cache',
    expiration: {
      maxAgeSeconds: 60 * 60 // 1 hour
    }
  },
  {
    urlPattern: /\/api\/metrics/,
    strategy: 'networkFirst',
    cacheName: 'metrics-data-cache',
    expiration: {
      maxAgeSeconds: 60 * 5 // 5 minutes
    }
  },
  {
    urlPattern: /\/api\/alerts/,
    strategy: 'networkFirst',
    cacheName: 'alerts-data-cache',
    expiration: {
      maxAgeSeconds: 60 * 5 // 5 minutes
    }
  },
  {
    urlPattern: /\/api\/settings/,
    strategy: 'staleWhileRevalidate',
    cacheName: 'settings-cache',
    expiration: {
      maxAgeSeconds: 60 * 60 * 24 // 24 hours
    }
  }
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Opened cache');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => self.skipWaiting())
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  const cacheWhitelist = [CACHE_NAME];
  
  // Add API cache names to whitelist
  API_CACHE_CONFIG.forEach((config) => {
    cacheWhitelist.push(config.cacheName);
  });
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (!cacheWhitelist.includes(cacheName)) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Fetch event - handle network requests
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);
  
  // Skip cross-origin requests
  if (url.origin !== self.location.origin) {
    return;
  }
  
  // Handle API requests
  for (const config of API_CACHE_CONFIG) {
    if (config.urlPattern.test(url.pathname)) {
      if (config.strategy === 'staleWhileRevalidate') {
        event.respondWith(staleWhileRevalidate(event.request, config));
        return;
      } else if (config.strategy === 'networkFirst') {
        event.respondWith(networkFirst(event.request, config));
        return;
      } else if (config.strategy === 'cacheFirst') {
        event.respondWith(cacheFirst(event.request, config));
        return;
      }
    }
  }
  
  // Handle static assets
  if (isStaticAsset(event.request.url)) {
    event.respondWith(cacheFirst(event.request, { cacheName: CACHE_NAME }));
    return;
  }
  
  // Default to network with cache fallback
  event.respondWith(networkWithCacheFallback(event.request));
});

// Check if URL is a static asset
function isStaticAsset(url) {
  return STATIC_ASSETS.some(asset => url.endsWith(asset));
}

// Stale While Revalidate strategy
async function staleWhileRevalidate(request, config) {
  const cache = await caches.open(config.cacheName);
  
  // Try to get from cache
  const cachedResponse = await cache.match(request);
  
  // Clone the request for fetching
  const fetchPromise = fetch(request)
    .then((response) => {
      // Check if we received a valid response
      if (!response || response.status !== 200 || response.type !== 'basic') {
        return response;
      }
      
      // Clone the response
      const responseToCache = response.clone();
      
      // Update the cache
      cache.put(request, responseToCache);
      
      return response;
    })
    .catch((error) => {
      console.error('Fetch failed:', error);
      // Return null to indicate fetch failed
      return null;
    });
  
  // Return cached response immediately, then update cache in background
  return cachedResponse || fetchPromise || new Response(
    JSON.stringify({ error: 'You are offline and no cached data is available' }),
    { 
      headers: { 'Content-Type': 'application/json' },
      status: 503,
      statusText: 'Service Unavailable'
    }
  );
}

// Network First strategy
async function networkFirst(request, config) {
  const cache = await caches.open(config.cacheName);
  
  try {
    // Try to get from network
    const response = await fetch(request);
    
    // Check if we received a valid response
    if (response && response.status === 200) {
      // Clone the response
      const responseToCache = response.clone();
      
      // Update the cache
      cache.put(request, responseToCache);
      
      return response;
    }
  } catch (error) {
    console.log('Network request failed, falling back to cache', error);
  }
  
  // If network fails, try to get from cache
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  // If both network and cache fail, return offline response
  return new Response(
    JSON.stringify({ error: 'You are offline and no cached data is available' }),
    { 
      headers: { 'Content-Type': 'application/json' },
      status: 503,
      statusText: 'Service Unavailable'
    }
  );
}

// Cache First strategy
async function cacheFirst(request, config) {
  const cache = await caches.open(config.cacheName);
  
  // Try to get from cache
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  // If not in cache, try to get from network
  try {
    const response = await fetch(request);
    
    // Check if we received a valid response
    if (response && response.status === 200) {
      // Clone the response
      const responseToCache = response.clone();
      
      // Update the cache
      cache.put(request, responseToCache);
      
      return response;
    }
  } catch (error) {
    console.log('Network request failed', error);
  }
  
  // If both cache and network fail, return offline response
  return new Response(
    JSON.stringify({ error: 'You are offline and no cached data is available' }),
    { 
      headers: { 'Content-Type': 'application/json' },
      status: 503,
      statusText: 'Service Unavailable'
    }
  );
}

// Network with Cache Fallback strategy
async function networkWithCacheFallback(request) {
  try {
    // Try to get from network
    return await fetch(request);
  } catch (error) {
    console.log('Network request failed, falling back to cache', error);
    
    // If network fails, try to get from cache
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // If both network and cache fail, return offline response
    return new Response(
      JSON.stringify({ error: 'You are offline and no cached data is available' }),
      { 
        headers: { 'Content-Type': 'application/json' },
        status: 503,
        statusText: 'Service Unavailable'
      }
    );
  }
}

// Background sync event
self.addEventListener('sync', (event) => {
  if (event.tag === 'sync-pending-requests') {
    event.waitUntil(syncPendingRequests());
  }
});

// Sync pending requests
async function syncPendingRequests() {
  try {
    const db = await openDatabase();
    const pendingRequests = await db.getAll('pendingRequests');
    
    for (const request of pendingRequests) {
      try {
        const response = await fetch(request.url, {
          method: request.method,
          headers: request.headers,
          body: request.body
        });
        
        if (response.ok) {
          await db.delete('pendingRequests', request.id);
        }
      } catch (error) {
        console.error('Failed to sync request:', error);
      }
    }
  } catch (error) {
    console.error('Failed to sync pending requests:', error);
  }
}

// Open IndexedDB database
function openDatabase() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('NovaVisionOfflineDB', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      
      // Create object store for pending requests
      if (!db.objectStoreNames.contains('pendingRequests')) {
        db.createObjectStore('pendingRequests', { keyPath: 'id', autoIncrement: true });
      }
      
      // Create object store for offline data
      if (!db.objectStoreNames.contains('offlineData')) {
        db.createObjectStore('offlineData', { keyPath: 'key' });
      }
    };
  });
}
