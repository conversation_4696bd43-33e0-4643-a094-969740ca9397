const request = require('supertest');
const app = require('../../../server');

describe('ESG Metrics API', () => {
  // Test GET /esg/metrics/metrics
  describe('GET /esg/metrics/metrics', () => {
    it('should return a list of ESG metrics', async () => {
      const response = await request(app)
        .get('/esg/metrics/metrics')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
    
    it('should filter metrics by category', async () => {
      const response = await request(app)
        .get('/esg/metrics/metrics?category=environmental')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(metric => metric.category === 'environmental')).toBe(true);
    });
    
    it('should filter metrics by status', async () => {
      const response = await request(app)
        .get('/esg/metrics/metrics?status=active')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(metric => metric.status === 'active')).toBe(true);
    });
    
    it('should paginate results', async () => {
      const response = await request(app)
        .get('/esg/metrics/metrics?page=1&limit=2')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeLessThanOrEqual(2);
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(2);
    });
  });
  
  // Test GET /esg/metrics/metrics/:id
  describe('GET /esg/metrics/metrics/:id', () => {
    it('should return a specific ESG metric', async () => {
      const response = await request(app)
        .get('/esg/metrics/metrics/esg-m-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('esg-m-001');
    });
    
    it('should return 404 for non-existent metric', async () => {
      const response = await request(app)
        .get('/esg/metrics/metrics/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test POST /esg/metrics/metrics
  describe('POST /esg/metrics/metrics', () => {
    it('should create a new ESG metric', async () => {
      const newMetric = {
        name: 'Test Metric',
        description: 'This is a test metric',
        category: 'environmental',
        subcategory: 'waste',
        unit: 'kg',
        dataType: 'numeric',
        framework: 'GRI 306',
        targetValue: '1000',
        targetDate: '2025-12-31',
        owner: 'Test Team',
        status: 'active'
      };
      
      const response = await request(app)
        .post('/esg/metrics/metrics')
        .set('apikey', 'test-api-key')
        .send(newMetric);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.name).toBe(newMetric.name);
      expect(response.body.data.category).toBe(newMetric.category);
      expect(response.body.data).toHaveProperty('id');
    });
    
    it('should return 400 for invalid input', async () => {
      const invalidMetric = {
        // Missing required fields
        description: 'This is an invalid metric'
      };
      
      const response = await request(app)
        .post('/esg/metrics/metrics')
        .set('apikey', 'test-api-key')
        .send(invalidMetric);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test PUT /esg/metrics/metrics/:id
  describe('PUT /esg/metrics/metrics/:id', () => {
    it('should update an existing ESG metric', async () => {
      const updateData = {
        name: 'Updated Metric',
        targetValue: '2000',
        status: 'inactive'
      };
      
      const response = await request(app)
        .put('/esg/metrics/metrics/esg-m-001')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.name).toBe(updateData.name);
      expect(response.body.data.targetValue).toBe(updateData.targetValue);
      expect(response.body.data.status).toBe(updateData.status);
    });
    
    it('should return 404 for non-existent metric', async () => {
      const updateData = {
        name: 'Updated Metric'
      };
      
      const response = await request(app)
        .put('/esg/metrics/metrics/non-existent-id')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test DELETE /esg/metrics/metrics/:id
  describe('DELETE /esg/metrics/metrics/:id', () => {
    it('should delete an existing ESG metric', async () => {
      // First, create a metric to delete
      const newMetric = {
        name: 'Metric to Delete',
        category: 'environmental',
        dataType: 'numeric',
        status: 'active'
      };
      
      const createResponse = await request(app)
        .post('/esg/metrics/metrics')
        .set('apikey', 'test-api-key')
        .send(newMetric);
      
      const metricId = createResponse.body.data.id;
      
      const response = await request(app)
        .delete(`/esg/metrics/metrics/${metricId}`)
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 404 for non-existent metric', async () => {
      const response = await request(app)
        .delete('/esg/metrics/metrics/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /esg/metrics/metrics/:id/data-points
  describe('GET /esg/metrics/metrics/:id/data-points', () => {
    it('should return data points for a specific ESG metric', async () => {
      const response = await request(app)
        .get('/esg/metrics/metrics/esg-m-001/data-points')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.every(dp => dp.metricId === 'esg-m-001')).toBe(true);
    });
    
    it('should filter data points by period', async () => {
      const response = await request(app)
        .get('/esg/metrics/metrics/esg-m-001/data-points?period=annually')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(dp => dp.period === 'annually')).toBe(true);
    });
    
    it('should return 404 for non-existent metric', async () => {
      const response = await request(app)
        .get('/esg/metrics/metrics/non-existent-id/data-points')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test POST /esg/metrics/metrics/:id/data-points
  describe('POST /esg/metrics/metrics/:id/data-points', () => {
    it('should add a data point to an ESG metric', async () => {
      const newDataPoint = {
        value: '7000',
        date: '2024-01-31',
        period: 'monthly',
        source: 'Test Source',
        notes: 'Test notes',
        verificationStatus: 'unverified'
      };
      
      const response = await request(app)
        .post('/esg/metrics/metrics/esg-m-001/data-points')
        .set('apikey', 'test-api-key')
        .send(newDataPoint);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.value).toBe(newDataPoint.value);
      expect(response.body.data.date).toBe(newDataPoint.date);
      expect(response.body.data.metricId).toBe('esg-m-001');
      expect(response.body.data).toHaveProperty('id');
    });
    
    it('should return 400 for invalid input', async () => {
      const invalidDataPoint = {
        // Missing required fields
        notes: 'Invalid data point'
      };
      
      const response = await request(app)
        .post('/esg/metrics/metrics/esg-m-001/data-points')
        .set('apikey', 'test-api-key')
        .send(invalidDataPoint);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 404 for non-existent metric', async () => {
      const newDataPoint = {
        value: '7000',
        date: '2024-01-31',
        period: 'monthly',
        verificationStatus: 'unverified'
      };
      
      const response = await request(app)
        .post('/esg/metrics/metrics/non-existent-id/data-points')
        .set('apikey', 'test-api-key')
        .send(newDataPoint);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /esg/metrics/initiatives
  describe('GET /esg/metrics/initiatives', () => {
    it('should return a list of ESG initiatives', async () => {
      const response = await request(app)
        .get('/esg/metrics/initiatives')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
    
    it('should filter initiatives by category', async () => {
      const response = await request(app)
        .get('/esg/metrics/initiatives?category=environmental')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(initiative => initiative.category === 'environmental')).toBe(true);
    });
    
    it('should filter initiatives by status', async () => {
      const response = await request(app)
        .get('/esg/metrics/initiatives?status=in-progress')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(initiative => initiative.status === 'in-progress')).toBe(true);
    });
  });
  
  // Test GET /esg/metrics/initiatives/:id
  describe('GET /esg/metrics/initiatives/:id', () => {
    it('should return a specific ESG initiative', async () => {
      const response = await request(app)
        .get('/esg/metrics/initiatives/esg-i-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('esg-i-001');
    });
    
    it('should return 404 for non-existent initiative', async () => {
      const response = await request(app)
        .get('/esg/metrics/initiatives/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test POST /esg/metrics/initiatives
  describe('POST /esg/metrics/initiatives', () => {
    it('should create a new ESG initiative', async () => {
      const newInitiative = {
        name: 'Test Initiative',
        description: 'This is a test initiative',
        category: 'environmental',
        startDate: '2024-01-01',
        endDate: '2025-12-31',
        status: 'planned',
        owner: 'Test Team',
        budget: 100000,
        metrics: ['esg-m-001', 'esg-m-002'],
        goals: [
          {
            description: 'Test goal',
            targetDate: '2024-12-31',
            status: 'not-started'
          }
        ]
      };
      
      const response = await request(app)
        .post('/esg/metrics/initiatives')
        .set('apikey', 'test-api-key')
        .send(newInitiative);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.name).toBe(newInitiative.name);
      expect(response.body.data.category).toBe(newInitiative.category);
      expect(response.body.data).toHaveProperty('id');
    });
    
    it('should return 400 for invalid input', async () => {
      const invalidInitiative = {
        // Missing required fields
        description: 'This is an invalid initiative'
      };
      
      const response = await request(app)
        .post('/esg/metrics/initiatives')
        .set('apikey', 'test-api-key')
        .send(invalidInitiative);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 400 for non-existent metrics', async () => {
      const invalidInitiative = {
        name: 'Invalid Initiative',
        category: 'environmental',
        status: 'planned',
        metrics: ['non-existent-metric']
      };
      
      const response = await request(app)
        .post('/esg/metrics/initiatives')
        .set('apikey', 'test-api-key')
        .send(invalidInitiative);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test PUT /esg/metrics/initiatives/:id
  describe('PUT /esg/metrics/initiatives/:id', () => {
    it('should update an existing ESG initiative', async () => {
      const updateData = {
        name: 'Updated Initiative',
        status: 'in-progress',
        budget: 150000
      };
      
      const response = await request(app)
        .put('/esg/metrics/initiatives/esg-i-001')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.name).toBe(updateData.name);
      expect(response.body.data.status).toBe(updateData.status);
      expect(response.body.data.budget).toBe(updateData.budget);
    });
    
    it('should return 404 for non-existent initiative', async () => {
      const updateData = {
        name: 'Updated Initiative'
      };
      
      const response = await request(app)
        .put('/esg/metrics/initiatives/non-existent-id')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test DELETE /esg/metrics/initiatives/:id
  describe('DELETE /esg/metrics/initiatives/:id', () => {
    it('should delete an existing ESG initiative', async () => {
      // First, create an initiative to delete
      const newInitiative = {
        name: 'Initiative to Delete',
        category: 'environmental',
        status: 'planned'
      };
      
      const createResponse = await request(app)
        .post('/esg/metrics/initiatives')
        .set('apikey', 'test-api-key')
        .send(newInitiative);
      
      const initiativeId = createResponse.body.data.id;
      
      const response = await request(app)
        .delete(`/esg/metrics/initiatives/${initiativeId}`)
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 404 for non-existent initiative', async () => {
      const response = await request(app)
        .delete('/esg/metrics/initiatives/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /esg/metrics/categories
  describe('GET /esg/metrics/categories', () => {
    it('should return a list of ESG categories', async () => {
      const response = await request(app)
        .get('/esg/metrics/categories')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0]).toHaveProperty('id');
      expect(response.body.data[0]).toHaveProperty('name');
      expect(response.body.data[0]).toHaveProperty('description');
      expect(response.body.data[0]).toHaveProperty('subcategories');
    });
  });
  
  // Test GET /esg/metrics/frameworks
  describe('GET /esg/metrics/frameworks', () => {
    it('should return a list of ESG frameworks', async () => {
      const response = await request(app)
        .get('/esg/metrics/frameworks')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0]).toHaveProperty('id');
      expect(response.body.data[0]).toHaveProperty('name');
      expect(response.body.data[0]).toHaveProperty('description');
    });
  });
});
