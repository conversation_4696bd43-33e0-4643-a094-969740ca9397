import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Paper,
  Select,
  Tab,
  Tabs,
  TextField,
  Typography,
  Backdrop
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Security as SecurityIcon,
  Person as PersonIcon,
  VpnKey as VpnKeyIcon,
  History as HistoryIcon
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import api from '../../services/api';
import RBACLogs from './RBACLogs';
import RoleInheritanceManager from './RoleInheritanceManager';

// TabPanel component for tab content
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`rbac-tabpanel-${index}`}
      aria-labelledby={`rbac-tab-${index}`}
      {...other}
    >
      {value === index && <Box p={3}>{children}</Box>}
    </div>
  );
}

// Main RBAC Manager component
const RBACManager = () => {
  // State
  const [tabValue, setTabValue] = useState(0);
  const [roles, setRoles] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [users, setUsers] = useState([]);
  const [selectedRole, setSelectedRole] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [roleDialogOpen, setRoleDialogOpen] = useState(false);
  const [permissionDialogOpen, setPermissionDialogOpen] = useState(false);
  const [assignRoleDialogOpen, setAssignRoleDialogOpen] = useState(false);
  const [newRole, setNewRole] = useState({
    name: '',
    description: '',
    permissions: [],
    inheritsFrom: []
  });
  const [newPermission, setNewPermission] = useState({ name: '', description: '', resource: '', action: '' });
  const [selectedRoleForUser, setSelectedRoleForUser] = useState('');
  const [loading, setLoading] = useState(false);

  const { enqueueSnackbar } = useSnackbar();

  // Fetch data on component mount
  useEffect(() => {
    fetchRoles();
    fetchPermissions();
    fetchUsers();
  }, []);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Fetch roles from API
  const fetchRoles = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/rbac/roles');
      setRoles(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching roles:', error);
      enqueueSnackbar('Failed to fetch roles', { variant: 'error' });
      setLoading(false);
    }
  };

  // Fetch permissions from API
  const fetchPermissions = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/rbac/permissions');
      setPermissions(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching permissions:', error);
      enqueueSnackbar('Failed to fetch permissions', { variant: 'error' });
      setLoading(false);
    }
  };

  // Fetch users from API
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/users');
      setUsers(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching users:', error);
      enqueueSnackbar('Failed to fetch users', { variant: 'error' });
      setLoading(false);
    }
  };

  // Handle role selection
  const handleRoleSelect = async (role) => {
    try {
      setLoading(true);
      const response = await api.get(`/api/rbac/roles/${role._id}`);
      setSelectedRole(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching role details:', error);
      enqueueSnackbar('Failed to fetch role details', { variant: 'error' });
      setLoading(false);
    }
  };

  // Handle user selection
  const handleUserSelect = async (user) => {
    try {
      setLoading(true);
      const response = await api.get(`/api/rbac/users/${user._id}/roles`);
      setSelectedUser({ ...user, roles: response.data });
      setLoading(false);
    } catch (error) {
      console.error('Error fetching user roles:', error);
      enqueueSnackbar('Failed to fetch user roles', { variant: 'error' });
      setLoading(false);
    }
  };

  // Handle role dialog open
  const handleRoleDialogOpen = () => {
    setNewRole({
      name: '',
      description: '',
      permissions: [],
      inheritsFrom: []
    });
    setRoleDialogOpen(true);
  };

  // Handle role dialog close
  const handleRoleDialogClose = () => {
    setRoleDialogOpen(false);
  };

  // Handle permission dialog open
  const handlePermissionDialogOpen = () => {
    setNewPermission({ name: '', description: '', resource: '', action: '' });
    setPermissionDialogOpen(true);
  };

  // Handle permission dialog close
  const handlePermissionDialogClose = () => {
    setPermissionDialogOpen(false);
  };

  // Handle assign role dialog open
  const handleAssignRoleDialogOpen = () => {
    setSelectedRoleForUser('');
    setAssignRoleDialogOpen(true);
  };

  // Handle assign role dialog close
  const handleAssignRoleDialogClose = () => {
    setAssignRoleDialogOpen(false);
  };

  // Handle role input change
  const handleRoleInputChange = (e) => {
    const { name, value } = e.target;
    setNewRole({ ...newRole, [name]: value });
  };

  // Handle permission input change
  const handlePermissionInputChange = (e) => {
    const { name, value } = e.target;
    setNewPermission({ ...newPermission, [name]: value });
  };

  // Handle permission checkbox change
  const handlePermissionCheckboxChange = (permissionId) => {
    const permissions = [...newRole.permissions];
    const index = permissions.indexOf(permissionId);

    if (index === -1) {
      permissions.push(permissionId);
    } else {
      permissions.splice(index, 1);
    }

    setNewRole({ ...newRole, permissions });
  };

  // Handle role inheritance checkbox change
  const handleInheritanceCheckboxChange = (roleId) => {
    const inheritsFrom = [...newRole.inheritsFrom];
    const index = inheritsFrom.indexOf(roleId);

    if (index === -1) {
      inheritsFrom.push(roleId);
    } else {
      inheritsFrom.splice(index, 1);
    }

    setNewRole({ ...newRole, inheritsFrom });
  };

  // Create new role
  const createRole = async () => {
    try {
      setLoading(true);
      await api.post('/api/rbac/roles', newRole);
      enqueueSnackbar('Role created successfully', { variant: 'success' });
      setRoleDialogOpen(false);
      fetchRoles();
      setLoading(false);
    } catch (error) {
      console.error('Error creating role:', error);
      const errorMessage = error.response?.data?.message || 'Failed to create role';
      enqueueSnackbar(errorMessage, {
        variant: 'error',
        autoHideDuration: 5000
      });
      setLoading(false);
    }
  };

  // Create new permission
  const createPermission = async () => {
    try {
      setLoading(true);
      await api.post('/api/rbac/permissions', newPermission);
      enqueueSnackbar('Permission created successfully', { variant: 'success' });
      setPermissionDialogOpen(false);
      fetchPermissions();
      setLoading(false);
    } catch (error) {
      console.error('Error creating permission:', error);
      const errorMessage = error.response?.data?.message || 'Failed to create permission';
      enqueueSnackbar(errorMessage, {
        variant: 'error',
        autoHideDuration: 5000
      });
      setLoading(false);
    }
  };

  // Assign role to user
  const assignRoleToUser = async () => {
    if (!selectedUser || !selectedRoleForUser) {
      enqueueSnackbar('Please select a user and a role', { variant: 'warning' });
      return;
    }

    try {
      setLoading(true);
      await api.post(`/api/rbac/users/${selectedUser._id}/roles`, { roleId: selectedRoleForUser });
      enqueueSnackbar('Role assigned successfully', { variant: 'success' });
      setAssignRoleDialogOpen(false);
      handleUserSelect(selectedUser);
      setLoading(false);
    } catch (error) {
      console.error('Error assigning role:', error);
      const errorMessage = error.response?.data?.message || 'Failed to assign role';
      enqueueSnackbar(errorMessage, {
        variant: 'error',
        autoHideDuration: 5000
      });
      setLoading(false);
    }
  };

  // Remove role from user
  const removeRoleFromUser = async (roleId) => {
    if (!selectedUser) {
      enqueueSnackbar('No user selected', { variant: 'warning' });
      return;
    }

    try {
      setLoading(true);
      await api.delete(`/api/rbac/users/${selectedUser._id}/roles/${roleId}`);
      enqueueSnackbar('Role removed successfully', { variant: 'success' });
      handleUserSelect(selectedUser);
      setLoading(false);
    } catch (error) {
      console.error('Error removing role:', error);
      const errorMessage = error.response?.data?.message || 'Failed to remove role';
      enqueueSnackbar(errorMessage, {
        variant: 'error',
        autoHideDuration: 5000
      });
      setLoading(false);
    }
  };

  return (
    <Box>
      {/* Loading backdrop */}
      <Backdrop
        sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={loading}
      >
        <CircularProgress color="inherit" />
      </Backdrop>

      <Typography variant="h4" gutterBottom>
        <SecurityIcon fontSize="large" style={{ verticalAlign: 'middle', marginRight: 8 }} />
        RBAC Manager
      </Typography>

      <Paper>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          centered
        >
          <Tab label="Roles" icon={<VpnKeyIcon />} />
          <Tab label="Permissions" icon={<SecurityIcon />} />
          <Tab label="Users" icon={<PersonIcon />} />
          <Tab label="Audit Logs" icon={<HistoryIcon />} />
        </Tabs>

        {/* Roles Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Card>
                <CardHeader
                  title="Roles"
                  action={
                    <IconButton onClick={handleRoleDialogOpen}>
                      <AddIcon />
                    </IconButton>
                  }
                />
                <Divider />
                <CardContent>
                  {loading ? (
                    <Box display="flex" justifyContent="center" p={2}>
                      <CircularProgress />
                    </Box>
                  ) : roles.length === 0 ? (
                    <Typography variant="body2" color="textSecondary" align="center">
                      No roles found
                    </Typography>
                  ) : (
                    <List>
                      {roles.map((role) => (
                        <ListItem
                          button
                          key={role._id}
                          selected={selectedRole && selectedRole._id === role._id}
                          onClick={() => handleRoleSelect(role)}
                        >
                          <ListItemIcon>
                            <VpnKeyIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={role.name}
                            secondary={role.description}
                          />
                        </ListItem>
                      ))}
                    </List>
                  )}
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={8}>
              {selectedRole ? (
                <Card>
                  <CardHeader title={`Role: ${selectedRole.name}`} />
                  <Divider />
                  <CardContent>
                    <Typography variant="subtitle1">Description:</Typography>
                    <Typography paragraph>{selectedRole.description}</Typography>

                    {/* Role Inheritance Manager */}
                    {!selectedRole.isSystem && (
                      <RoleInheritanceManager
                        role={selectedRole}
                        onUpdate={() => handleRoleSelect(selectedRole)}
                      />
                    )}

                    <Typography variant="subtitle1">Permissions:</Typography>
                    <List>
                      {selectedRole.permissions.map((permission) => (
                        <ListItem key={permission}>
                          <ListItemIcon>
                            <SecurityIcon />
                          </ListItemIcon>
                          <ListItemText primary={permission} />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              ) : (
                <Typography variant="subtitle1">Select a role to view details</Typography>
              )}
            </Grid>
          </Grid>
        </TabPanel>

        {/* Permissions Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box display="flex" justifyContent="flex-end" mb={2}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handlePermissionDialogOpen}
            >
              Add Permission
            </Button>
          </Box>

          <Grid container spacing={2}>
            {permissions.map((permission) => (
              <Grid item xs={12} sm={6} md={4} key={permission._id}>
                <Card>
                  <CardHeader title={permission.name} />
                  <Divider />
                  <CardContent>
                    <Typography variant="body2" color="textSecondary">
                      Resource: {permission.resource}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Action: {permission.action}
                    </Typography>
                    <Typography variant="body2">
                      {permission.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Users Tab */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Card>
                <CardHeader title="Users" />
                <Divider />
                <CardContent>
                  {loading ? (
                    <Box display="flex" justifyContent="center" p={2}>
                      <CircularProgress />
                    </Box>
                  ) : users.length === 0 ? (
                    <Typography variant="body2" color="textSecondary" align="center">
                      No users found
                    </Typography>
                  ) : (
                    <List>
                      {users.map((user) => (
                        <ListItem
                          button
                          key={user._id}
                          selected={selectedUser && selectedUser._id === user._id}
                          onClick={() => handleUserSelect(user)}
                        >
                          <ListItemIcon>
                            <PersonIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={`${user.firstName} ${user.lastName}`}
                            secondary={user.email}
                          />
                        </ListItem>
                      ))}
                    </List>
                  )}
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={8}>
              {selectedUser ? (
                <Card>
                  <CardHeader
                    title={`User: ${selectedUser.firstName} ${selectedUser.lastName}`}
                    action={
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={<AddIcon />}
                        onClick={handleAssignRoleDialogOpen}
                      >
                        Assign Role
                      </Button>
                    }
                  />
                  <Divider />
                  <CardContent>
                    <Typography variant="subtitle1">Roles:</Typography>
                    {selectedUser.roles && selectedUser.roles.length > 0 ? (
                      <List>
                        {selectedUser.roles.map((role) => (
                          <ListItem key={role._id}>
                            <ListItemIcon>
                              <VpnKeyIcon />
                            </ListItemIcon>
                            <ListItemText
                              primary={role.name}
                              secondary={role.description}
                            />
                            <ListItemSecondaryAction>
                              <IconButton
                                edge="end"
                                onClick={() => removeRoleFromUser(role._id)}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </ListItemSecondaryAction>
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Typography variant="body2">No roles assigned</Typography>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <Typography variant="subtitle1">Select a user to view details</Typography>
              )}
            </Grid>
          </Grid>
        </TabPanel>

        {/* Audit Logs Tab */}
        <TabPanel value={tabValue} index={3}>
          <RBACLogs />
        </TabPanel>
      </Paper>

      {/* Create Role Dialog */}
      <Dialog open={roleDialogOpen} onClose={handleRoleDialogClose} maxWidth="md" fullWidth>
        <DialogTitle>Create New Role</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="name"
            label="Role Name"
            type="text"
            fullWidth
            value={newRole.name}
            onChange={handleRoleInputChange}
          />
          <TextField
            margin="dense"
            name="description"
            label="Description"
            type="text"
            fullWidth
            multiline
            rows={3}
            value={newRole.description}
            onChange={handleRoleInputChange}
          />

          <Typography variant="subtitle1" style={{ marginTop: 16 }}>
            Inherit From:
          </Typography>
          <Typography variant="body2" color="textSecondary" gutterBottom>
            Select roles that this role should inherit permissions from
          </Typography>
          <Grid container spacing={2}>
            {roles.filter(role => !role.isSystem).map((role) => (
              <Grid item xs={12} sm={6} key={role._id}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={newRole.inheritsFrom.includes(role._id)}
                      onChange={() => handleInheritanceCheckboxChange(role._id)}
                      color="primary"
                    />
                  }
                  label={role.name}
                />
              </Grid>
            ))}
          </Grid>

          <Typography variant="subtitle1" style={{ marginTop: 16 }}>
            Permissions:
          </Typography>
          <Grid container spacing={2}>
            {permissions.map((permission) => (
              <Grid item xs={12} sm={6} key={permission._id}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={newRole.permissions.includes(permission._id)}
                      onChange={() => handlePermissionCheckboxChange(permission._id)}
                      color="primary"
                    />
                  }
                  label={`${permission.name} (${permission.resource}:${permission.action})`}
                />
              </Grid>
            ))}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleRoleDialogClose} color="primary" disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={createRole}
            color="primary"
            disabled={!newRole.name || loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'Creating...' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Permission Dialog */}
      <Dialog open={permissionDialogOpen} onClose={handlePermissionDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Permission</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="name"
            label="Permission Name"
            type="text"
            fullWidth
            value={newPermission.name}
            onChange={handlePermissionInputChange}
          />
          <TextField
            margin="dense"
            name="description"
            label="Description"
            type="text"
            fullWidth
            multiline
            rows={2}
            value={newPermission.description}
            onChange={handlePermissionInputChange}
          />
          <TextField
            margin="dense"
            name="resource"
            label="Resource"
            type="text"
            fullWidth
            value={newPermission.resource}
            onChange={handlePermissionInputChange}
          />
          <TextField
            margin="dense"
            name="action"
            label="Action"
            type="text"
            fullWidth
            value={newPermission.action}
            onChange={handlePermissionInputChange}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handlePermissionDialogClose} color="primary" disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={createPermission}
            color="primary"
            disabled={!newPermission.name || !newPermission.resource || !newPermission.action || loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'Creating...' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Assign Role Dialog */}
      <Dialog open={assignRoleDialogOpen} onClose={handleAssignRoleDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle>Assign Role to User</DialogTitle>
        <DialogContent>
          <FormControl fullWidth margin="dense">
            <InputLabel id="role-select-label">Role</InputLabel>
            <Select
              labelId="role-select-label"
              value={selectedRoleForUser}
              onChange={(e) => setSelectedRoleForUser(e.target.value)}
            >
              {roles.map((role) => (
                <MenuItem key={role._id} value={role._id}>
                  {role.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleAssignRoleDialogClose} color="primary" disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={assignRoleToUser}
            color="primary"
            disabled={!selectedRoleForUser || loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'Assigning...' : 'Assign'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RBACManager;
