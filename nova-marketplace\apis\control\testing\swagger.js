/**
 * Control Testing API - Swagger Documentation
 * 
 * This file defines the Swagger documentation for the Control Testing API.
 */

/**
 * @swagger
 * tags:
 *   - name: Controls
 *     description: Control management
 *   - name: Test Plans
 *     description: Test plan management
 *   - name: Test Results
 *     description: Test result management
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Control:
 *       type: object
 *       required:
 *         - name
 *         - description
 *         - type
 *         - category
 *         - owner
 *         - implementationDate
 *       properties:
 *         _id:
 *           type: string
 *           description: The auto-generated ID of the control
 *         name:
 *           type: string
 *           description: The name of the control
 *         description:
 *           type: string
 *           description: The description of the control
 *         type:
 *           type: string
 *           enum: [Preventive, Detective, Corrective, Directive]
 *           description: The type of the control
 *         category:
 *           type: string
 *           enum: [Administrative, Technical, Physical]
 *           description: The category of the control
 *         status:
 *           type: string
 *           enum: [Active, Inactive, Deprecated]
 *           default: Active
 *           description: The status of the control
 *         owner:
 *           type: string
 *           description: The owner of the control
 *         implementationDate:
 *           type: string
 *           format: date-time
 *           description: The implementation date of the control
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the control was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the control was last updated
 *       example:
 *         _id: 60d21b4667d0d8992e610c85
 *         name: Access Control
 *         description: Control access to systems and data
 *         type: Preventive
 *         category: Technical
 *         status: Active
 *         owner: Security Team
 *         implementationDate: 2023-01-01T00:00:00.000Z
 *         createdAt: 2023-01-01T00:00:00.000Z
 *         updatedAt: 2023-01-01T00:00:00.000Z
 *     
 *     TestPlan:
 *       type: object
 *       required:
 *         - name
 *         - description
 *         - owner
 *         - startDate
 *         - endDate
 *       properties:
 *         _id:
 *           type: string
 *           description: The auto-generated ID of the test plan
 *         name:
 *           type: string
 *           description: The name of the test plan
 *         description:
 *           type: string
 *           description: The description of the test plan
 *         status:
 *           type: string
 *           enum: [Draft, Active, Completed, Archived]
 *           default: Draft
 *           description: The status of the test plan
 *         owner:
 *           type: string
 *           description: The owner of the test plan
 *         startDate:
 *           type: string
 *           format: date-time
 *           description: The start date of the test plan
 *         endDate:
 *           type: string
 *           format: date-time
 *           description: The end date of the test plan
 *         controls:
 *           type: array
 *           description: The controls included in the test plan
 *           items:
 *             type: object
 *             properties:
 *               control:
 *                 type: string
 *                 description: The ID of the control
 *               testProcedure:
 *                 type: string
 *                 description: The test procedure for the control
 *               expectedResults:
 *                 type: string
 *                 description: The expected results of the test
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the test plan was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the test plan was last updated
 *       example:
 *         _id: 60d21b4667d0d8992e610c86
 *         name: Q1 2023 Security Controls Testing
 *         description: Test security controls for Q1 2023
 *         status: Active
 *         owner: Compliance Team
 *         startDate: 2023-01-01T00:00:00.000Z
 *         endDate: 2023-03-31T00:00:00.000Z
 *         controls:
 *           - control: 60d21b4667d0d8992e610c85
 *             testProcedure: Verify access controls are properly implemented
 *             expectedResults: All access controls are properly implemented
 *         createdAt: 2023-01-01T00:00:00.000Z
 *         updatedAt: 2023-01-01T00:00:00.000Z
 *     
 *     TestResult:
 *       type: object
 *       required:
 *         - testPlan
 *         - control
 *         - tester
 *         - result
 *       properties:
 *         _id:
 *           type: string
 *           description: The auto-generated ID of the test result
 *         testPlan:
 *           type: string
 *           description: The ID of the test plan
 *         control:
 *           type: string
 *           description: The ID of the control
 *         tester:
 *           type: string
 *           description: The person who performed the test
 *         testDate:
 *           type: string
 *           format: date-time
 *           description: The date the test was performed
 *         status:
 *           type: string
 *           enum: [Passed, Failed, Inconclusive, Not Tested]
 *           default: Not Tested
 *           description: The status of the test
 *         result:
 *           type: string
 *           description: The result of the test
 *         notes:
 *           type: string
 *           description: Additional notes about the test
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the test result was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the test result was last updated
 *       example:
 *         _id: 60d21b4667d0d8992e610c87
 *         testPlan: 60d21b4667d0d8992e610c86
 *         control: 60d21b4667d0d8992e610c85
 *         tester: John Doe
 *         testDate: 2023-01-15T00:00:00.000Z
 *         status: Passed
 *         result: All access controls are properly implemented
 *         notes: No issues found
 *         createdAt: 2023-01-15T00:00:00.000Z
 *         updatedAt: 2023-01-15T00:00:00.000Z
 */

/**
 * @swagger
 * /api/control/testing/controls:
 *   get:
 *     summary: Get all controls
 *     tags: [Controls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: The page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: The number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           default: -createdAt
 *         description: The sort order
 *     responses:
 *       200:
 *         description: A list of controls
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Control'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 1
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     limit:
 *                       type: integer
 *                       example: 10
 *                     pages:
 *                       type: integer
 *                       example: 1
 *       500:
 *         description: Server error
 *   post:
 *     summary: Create a new control
 *     tags: [Controls]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - description
 *               - type
 *               - category
 *               - owner
 *               - implementationDate
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [Preventive, Detective, Corrective, Directive]
 *               category:
 *                 type: string
 *                 enum: [Administrative, Technical, Physical]
 *               status:
 *                 type: string
 *                 enum: [Active, Inactive, Deprecated]
 *                 default: Active
 *               owner:
 *                 type: string
 *               implementationDate:
 *                 type: string
 *                 format: date-time
 *     responses:
 *       201:
 *         description: Control created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Control'
 *                 message:
 *                   type: string
 *                   example: Control created successfully
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/control/testing/controls/{id}:
 *   get:
 *     summary: Get a control by ID
 *     tags: [Controls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: The control ID
 *     responses:
 *       200:
 *         description: The control
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Control'
 *       404:
 *         description: Control not found
 *       500:
 *         description: Server error
 *   put:
 *     summary: Update a control
 *     tags: [Controls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: The control ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [Preventive, Detective, Corrective, Directive]
 *               category:
 *                 type: string
 *                 enum: [Administrative, Technical, Physical]
 *               status:
 *                 type: string
 *                 enum: [Active, Inactive, Deprecated]
 *               owner:
 *                 type: string
 *               implementationDate:
 *                 type: string
 *                 format: date-time
 *     responses:
 *       200:
 *         description: Control updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Control'
 *                 message:
 *                   type: string
 *                   example: Control updated successfully
 *       404:
 *         description: Control not found
 *       500:
 *         description: Server error
 *   delete:
 *     summary: Delete a control
 *     tags: [Controls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: The control ID
 *     responses:
 *       200:
 *         description: Control deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Control deleted successfully
 *       404:
 *         description: Control not found
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/control/testing/test-plans:
 *   get:
 *     summary: Get all test plans
 *     tags: [Test Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: The page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: The number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           default: -createdAt
 *         description: The sort order
 *     responses:
 *       200:
 *         description: A list of test plans
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/TestPlan'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 1
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     limit:
 *                       type: integer
 *                       example: 10
 *                     pages:
 *                       type: integer
 *                       example: 1
 *       500:
 *         description: Server error
 *   post:
 *     summary: Create a new test plan
 *     tags: [Test Plans]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - description
 *               - owner
 *               - startDate
 *               - endDate
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [Draft, Active, Completed, Archived]
 *                 default: Draft
 *               owner:
 *                 type: string
 *               startDate:
 *                 type: string
 *                 format: date-time
 *               endDate:
 *                 type: string
 *                 format: date-time
 *               controls:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     control:
 *                       type: string
 *                     testProcedure:
 *                       type: string
 *                     expectedResults:
 *                       type: string
 *     responses:
 *       201:
 *         description: Test plan created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/TestPlan'
 *                 message:
 *                   type: string
 *                   example: Test plan created successfully
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/control/testing/test-results:
 *   get:
 *     summary: Get all test results
 *     tags: [Test Results]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: The page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: The number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           default: -createdAt
 *         description: The sort order
 *     responses:
 *       200:
 *         description: A list of test results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/TestResult'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 1
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     limit:
 *                       type: integer
 *                       example: 10
 *                     pages:
 *                       type: integer
 *                       example: 1
 *       500:
 *         description: Server error
 *   post:
 *     summary: Create a new test result
 *     tags: [Test Results]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - testPlan
 *               - control
 *               - tester
 *               - result
 *             properties:
 *               testPlan:
 *                 type: string
 *               control:
 *                 type: string
 *               tester:
 *                 type: string
 *               testDate:
 *                 type: string
 *                 format: date-time
 *               status:
 *                 type: string
 *                 enum: [Passed, Failed, Inconclusive, Not Tested]
 *                 default: Not Tested
 *               result:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Test result created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/TestResult'
 *                 message:
 *                   type: string
 *                   example: Test result created successfully
 *       500:
 *         description: Server error
 */

module.exports = {
  basePath: '/api/control/testing',
  apis: ['./routes.js', './swagger.js'],
  tags: [
    {
      name: 'Controls',
      description: 'Control management'
    },
    {
      name: 'Test Plans',
      description: 'Test plan management'
    },
    {
      name: 'Test Results',
      description: 'Test result management'
    }
  ]
};
