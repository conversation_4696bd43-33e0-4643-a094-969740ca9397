/**
 * Compliance Event Trigger
 * 
 * This trigger fires when a new compliance event occurs.
 */

// Define the trigger
module.exports = {
  key: 'compliance_event',
  noun: 'Compliance Event',
  
  // Display information
  display: {
    label: 'New Compliance Event',
    description: 'Triggers when a new compliance event occurs.',
    important: true
  },
  
  // Operation
  operation: {
    // Polling operation
    type: 'polling',
    
    // Perform the operation
    perform: {
      url: '{{process.env.API_BASE_URL}}/api/zapier/triggers/compliance-event',
      headers: {
        Authorization: 'Bearer {{bundle.authData.access_token}}'
      }
    },
    
    // Sample data
    sample: {
      id: 'evt-123',
      type: 'compliance.violation',
      severity: 'high',
      resource: 'storage-bucket-123',
      details: 'Public access detected',
      timestamp: '2023-01-01T00:00:00Z'
    },
    
    // Output fields
    outputFields: [
      { key: 'id', label: 'ID' },
      { key: 'type', label: 'Type' },
      { key: 'severity', label: 'Severity' },
      { key: 'resource', label: 'Resource' },
      { key: 'details', label: 'Details' },
      { key: 'timestamp', label: 'Timestamp' }
    ]
  }
};
