<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nova Agent Platform Console - Test Integration</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .metric {
            background: rgba(0, 255, 150, 0.1);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #00ff96;
        }
        .metric h3 {
            margin: 0 0 10px 0;
            color: #00ff96;
        }
        .metric .value {
            font-size: 24px;
            font-weight: bold;
        }
        .console {
            background: #000;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            height: 200px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        .command-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .cmd-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }
        .cmd-btn:hover {
            transform: translateY(-2px);
        }
        .connection-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .connected { background: #00ff96; }
        .disconnected { background: #ff4757; }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #00ff96;
            padding-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Nova Agent Platform Console</h1>
            <p>NovaFuse Coherence Operating System - Integration Test</p>
            <div>
                <span class="connection-status" id="connectionStatus"></span>
                <span id="connectionText">Connecting...</span>
            </div>
        </div>

        <div class="status-card">
            <h2>System Status</h2>
            <div class="status-grid">
                <div class="metric">
                    <h3>Agent Status</h3>
                    <div class="value" id="agentStatus">Loading...</div>
                </div>
                <div class="metric">
                    <h3>Coherence Level</h3>
                    <div class="value" id="coherenceLevel">Loading...</div>
                </div>
                <div class="metric">
                    <h3>Ψ-Snap Status</h3>
                    <div class="value" id="psiSnap">Loading...</div>
                </div>
                <div class="metric">
                    <h3>Uptime</h3>
                    <div class="value" id="uptime">Loading...</div>
                </div>
            </div>
        </div>

        <div class="status-card">
            <h2>Command Console</h2>
            <div class="command-buttons">
                <button class="cmd-btn" onclick="sendCommand('restart_service')">Restart Service</button>
                <button class="cmd-btn" onclick="sendCommand('scan_vendor')">Scan Vendor</button>
                <button class="cmd-btn" onclick="sendCommand('update_module')">Update Module</button>
                <button class="cmd-btn" onclick="sendCommand('get_logs')">Get Logs</button>
            </div>
            <div class="console" id="console">
                <div class="log-entry">Console ready. Connecting to Nova Agent...</div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        // API endpoints
        const API_BASE = 'http://localhost:8090';
        const WS_URL = 'ws://localhost:8090/ws';

        // Initialize connection
        async function init() {
            await fetchStatus();
            connectWebSocket();
            
            // Update status every 5 seconds
            setInterval(fetchStatus, 5000);
        }

        // Fetch status from API
        async function fetchStatus() {
            try {
                const response = await fetch(`${API_BASE}/status`);
                const data = await response.json();
                
                document.getElementById('agentStatus').textContent = data.status.toUpperCase();
                document.getElementById('coherenceLevel').textContent = `${(data.coherence * 100).toFixed(1)}%`;
                document.getElementById('psiSnap').textContent = data.psi_snap ? '⚡ ACTIVE' : '🔄 BUILDING';
                document.getElementById('uptime').textContent = data.uptime;
                
                updateConnectionStatus(true);
            } catch (error) {
                console.error('Failed to fetch status:', error);
                updateConnectionStatus(false);
            }
        }

        // WebSocket connection
        function connectWebSocket() {
            try {
                ws = new WebSocket(WS_URL);
                
                ws.onopen = () => {
                    addLog('🔌 WebSocket connected');
                    updateConnectionStatus(true);
                };
                
                ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    addLog(`📊 Status update: ${data.status} | Coherence: ${(data.coherence * 100).toFixed(1)}%`);
                };
                
                ws.onclose = () => {
                    addLog('🔌 WebSocket disconnected');
                    updateConnectionStatus(false);
                    
                    // Reconnect after 5 seconds
                    setTimeout(connectWebSocket, 5000);
                };
                
                ws.onerror = (error) => {
                    addLog('❌ WebSocket error');
                    updateConnectionStatus(false);
                };
            } catch (error) {
                addLog('❌ Failed to connect WebSocket');
                updateConnectionStatus(false);
            }
        }

        // Send command
        async function sendCommand(commandType) {
            addLog(`⚡ Executing: ${commandType}`);
            
            try {
                const response = await fetch(`${API_BASE}/command`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: commandType, payload: {} })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog(`✅ ${result.message}`);
                    if (result.data) {
                        addLog(`📄 Data: ${JSON.stringify(result.data)}`);
                    }
                } else {
                    addLog(`❌ ${result.message}`);
                }
            } catch (error) {
                addLog(`❌ Command failed: ${error.message}`);
            }
        }

        // Update connection status
        function updateConnectionStatus(connected) {
            isConnected = connected;
            const statusEl = document.getElementById('connectionStatus');
            const textEl = document.getElementById('connectionText');
            
            if (connected) {
                statusEl.className = 'connection-status connected';
                textEl.textContent = 'Connected';
            } else {
                statusEl.className = 'connection-status disconnected';
                textEl.textContent = 'Disconnected';
            }
        }

        // Add log entry
        function addLog(message) {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            console.appendChild(logEntry);
            console.scrollTop = console.scrollHeight;
        }

        // Start the application
        init();
    </script>
</body>
</html>
