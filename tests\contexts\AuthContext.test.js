import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { AuthProvider, useAuth } from '../../contexts/AuthContext';

// Mock the authService
jest.mock('../../services/authService', () => ({
  login: jest.fn().mockResolvedValue({ user: { id: '123', name: 'Test User', email: '<EMAIL>' }, token: 'test-token' }),
  register: jest.fn().mockResolvedValue({ user: { id: '123', name: 'New User', email: '<EMAIL>' }, token: 'new-token' }),
  logout: jest.fn().mockResolvedValue(true),
  getCurrentUser: jest.fn().mockResolvedValue({ id: '123', name: 'Test User', email: '<EMAIL>' }),
  forgotPassword: jest.fn().mockResolvedValue({ success: true }),
  resetPassword: jest.fn().mockResolvedValue({ success: true })
}));

// Test component that uses the auth context
const TestComponent = () => {
  const { 
    isAuthenticated, 
    user, 
    loading, 
    login, 
    logout, 
    register, 
    forgotPassword, 
    resetPassword 
  } = useAuth();
  
  return (
    <div>
      <div data-testid="auth-status">
        {loading ? 'Loading' : isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
      </div>
      
      {user && (
        <div data-testid="user-info">
          {user.name} ({user.email})
        </div>
      )}
      
      <button onClick={() => login('<EMAIL>', 'password')}>Login</button>
      <button onClick={() => register('New User', '<EMAIL>', 'password')}>Register</button>
      <button onClick={() => logout()}>Logout</button>
      <button onClick={() => forgotPassword('<EMAIL>')}>Forgot Password</button>
      <button onClick={() => resetPassword('token', 'newpassword')}>Reset Password</button>
    </div>
  );
};

describe('AuthContext', () => {
  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
    
    // Clear localStorage
    localStorage.clear();
  });
  
  it('provides authentication state to components', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    // Initially should show loading
    expect(screen.getByTestId('auth-status').textContent).toBe('Loading');
    
    // After checking current user, should show authenticated status
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('Authenticated');
    });
    
    // User info should be displayed
    expect(screen.getByTestId('user-info').textContent).toBe('Test User (<EMAIL>)');
  });
  
  it('handles login correctly', async () => {
    const { login } = require('../../services/authService');
    
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    // Wait for initial loading to complete
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('Authenticated');
    });
    
    // Mock logout first to test login
    login.mockRejectedValueOnce(new Error('Not logged in'));
    
    // Click logout button
    fireEvent.click(screen.getByText('Logout'));
    
    // Wait for logout to complete
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('Not Authenticated');
    });
    
    // Reset mock to resolve with user
    login.mockResolvedValueOnce({ user: { id: '123', name: 'Test User', email: '<EMAIL>' }, token: 'test-token' });
    
    // Click login button
    fireEvent.click(screen.getByText('Login'));
    
    // Wait for login to complete
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('Authenticated');
    });
    
    // Check if login was called with correct parameters
    expect(login).toHaveBeenCalledWith('<EMAIL>', 'password');
    
    // User info should be displayed
    expect(screen.getByTestId('user-info').textContent).toBe('Test User (<EMAIL>)');
  });
  
  it('handles register correctly', async () => {
    const { register } = require('../../services/authService');
    
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    // Wait for initial loading to complete
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('Authenticated');
    });
    
    // Click logout button to reset state
    fireEvent.click(screen.getByText('Logout'));
    
    // Wait for logout to complete
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('Not Authenticated');
    });
    
    // Click register button
    fireEvent.click(screen.getByText('Register'));
    
    // Wait for register to complete
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('Authenticated');
    });
    
    // Check if register was called with correct parameters
    expect(register).toHaveBeenCalledWith('New User', '<EMAIL>', 'password');
    
    // User info should be displayed
    expect(screen.getByTestId('user-info').textContent).toBe('New User (<EMAIL>)');
  });
  
  it('handles forgot password correctly', async () => {
    const { forgotPassword } = require('../../services/authService');
    
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    // Wait for initial loading to complete
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('Authenticated');
    });
    
    // Click forgot password button
    fireEvent.click(screen.getByText('Forgot Password'));
    
    // Wait for forgot password to complete
    await waitFor(() => {
      expect(forgotPassword).toHaveBeenCalledWith('<EMAIL>');
    });
  });
  
  it('handles reset password correctly', async () => {
    const { resetPassword } = require('../../services/authService');
    
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    // Wait for initial loading to complete
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('Authenticated');
    });
    
    // Click reset password button
    fireEvent.click(screen.getByText('Reset Password'));
    
    // Wait for reset password to complete
    await waitFor(() => {
      expect(resetPassword).toHaveBeenCalledWith('token', 'newpassword');
    });
  });
});
