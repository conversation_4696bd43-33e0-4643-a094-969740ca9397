/**
 * NovaCore SOC 2 Control Service
 * 
 * This service provides functionality for managing SOC 2 controls.
 */

const { SOC2Control, SOC2ControlImplementation } = require('../models');
const logger = require('../../../config/logger');
const { ValidationError, NotFoundError } = require('../../../api/utils/errors');
const fs = require('fs').promises;
const path = require('path');

class SOC2ControlService {
  /**
   * Initialize SOC 2 controls from data file
   * @returns {Promise<Object>} - Initialization result
   */
  async initializeControls() {
    try {
      logger.info('Initializing SOC 2 controls');
      
      // Load requirements from data file
      const requirementsPath = path.join(__dirname, '../data/requirements.json');
      const requirementsData = await fs.readFile(requirementsPath, 'utf8');
      const requirements = JSON.parse(requirementsData).requirements;
      
      // Load mappings from data file
      const mappingsPath = path.join(__dirname, '../data/mappings.json');
      const mappingsData = await fs.readFile(mappingsPath, 'utf8');
      const mappings = JSON.parse(mappingsData).mappings;
      
      // Count of created and updated controls
      let created = 0;
      let updated = 0;
      
      // Process each requirement
      for (const req of requirements) {
        // Determine category based on reference
        const category = this._getCategoryFromReference(req.reference);
        
        // Determine trust service criteria based on categories
        const trustServiceCriteria = this._getTrustServiceCriteriaFromCategories(req.categories);
        
        // Get related frameworks
        const frameworks = [];
        
        for (const [framework, controlMappings] of Object.entries(mappings)) {
          const controls = [];
          
          for (const [soc2Id, frameworkControls] of Object.entries(controlMappings)) {
            if (soc2Id === req.id) {
              controls.push(...frameworkControls);
            }
          }
          
          if (controls.length > 0) {
            frameworks.push({
              framework,
              controls
            });
          }
        }
        
        // Check if control already exists
        const existingControl = await SOC2Control.findOne({ id: req.id });
        
        if (existingControl) {
          // Update existing control
          existingControl.title = req.title;
          existingControl.description = req.text;
          existingControl.category = category;
          existingControl.trustServiceCriteria = trustServiceCriteria;
          existingControl.frameworks = frameworks;
          
          await existingControl.save();
          updated++;
        } else {
          // Create new control
          const control = new SOC2Control({
            id: req.id,
            reference: req.reference,
            title: req.title,
            description: req.text,
            category,
            trustServiceCriteria,
            frameworks,
            evidenceRequirements: this._getDefaultEvidenceRequirements(req.reference, category)
          });
          
          await control.save();
          created++;
        }
      }
      
      logger.info('SOC 2 controls initialized', { created, updated });
      
      return {
        success: true,
        created,
        updated,
        total: created + updated
      };
    } catch (error) {
      logger.error('Error initializing SOC 2 controls', { error });
      throw error;
    }
  }
  
  /**
   * Get all SOC 2 controls
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Controls with pagination info
   */
  async getAllControls(filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { reference: 1 } } = options;
      
      // Build query
      const query = { status: 'active' };
      
      // Apply filters
      if (filter.category) {
        query.category = filter.category;
      }
      
      if (filter.trustServiceCriteria) {
        query.trustServiceCriteria = filter.trustServiceCriteria;
      }
      
      if (filter.reference) {
        query.reference = { $regex: filter.reference, $options: 'i' };
      }
      
      if (filter.search) {
        query.$or = [
          { title: { $regex: filter.search, $options: 'i' } },
          { description: { $regex: filter.search, $options: 'i' } }
        ];
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [controls, total] = await Promise.all([
        SOC2Control.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        SOC2Control.countDocuments(query)
      ]);
      
      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;
      
      return {
        data: controls,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting SOC 2 controls', { error });
      throw error;
    }
  }
  
  /**
   * Get SOC 2 control by ID
   * @param {string} id - Control ID
   * @returns {Promise<Object>} - Control
   */
  async getControlById(id) {
    try {
      const control = await SOC2Control.findOne({ id });
      
      if (!control) {
        throw new NotFoundError(`SOC 2 control with ID ${id} not found`);
      }
      
      return control;
    } catch (error) {
      logger.error('Error getting SOC 2 control by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Get SOC 2 controls by category
   * @param {string} category - Category
   * @returns {Promise<Array>} - Controls
   */
  async getControlsByCategory(category) {
    try {
      return await SOC2Control.findByCategory(category);
    } catch (error) {
      logger.error('Error getting SOC 2 controls by category', { category, error });
      throw error;
    }
  }
  
  /**
   * Get SOC 2 controls by trust service criteria
   * @param {string} criteria - Trust service criteria
   * @returns {Promise<Array>} - Controls
   */
  async getControlsByTrustServiceCriteria(criteria) {
    try {
      return await SOC2Control.findByTrustServiceCriteria(criteria);
    } catch (error) {
      logger.error('Error getting SOC 2 controls by trust service criteria', { criteria, error });
      throw error;
    }
  }
  
  /**
   * Get SOC 2 control implementation for an organization
   * @param {string} organizationId - Organization ID
   * @param {string} controlId - Control ID
   * @returns {Promise<Object>} - Control implementation
   */
  async getControlImplementation(organizationId, controlId) {
    try {
      // Get control
      const control = await this.getControlById(controlId);
      
      // Get implementation
      let implementation = await SOC2ControlImplementation.findOne({
        organizationId,
        controlId
      });
      
      if (!implementation) {
        // Create default implementation
        implementation = new SOC2ControlImplementation({
          organizationId,
          controlId,
          implementationStatus: {
            status: 'not_implemented',
            completeness: 0
          }
        });
        
        await implementation.save();
      }
      
      return {
        control,
        implementation
      };
    } catch (error) {
      logger.error('Error getting SOC 2 control implementation', { organizationId, controlId, error });
      throw error;
    }
  }
  
  /**
   * Update SOC 2 control implementation for an organization
   * @param {string} organizationId - Organization ID
   * @param {string} controlId - Control ID
   * @param {Object} data - Implementation data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated implementation
   */
  async updateControlImplementation(organizationId, controlId, data, userId) {
    try {
      // Get control
      await this.getControlById(controlId);
      
      // Get implementation
      let implementation = await SOC2ControlImplementation.findOne({
        organizationId,
        controlId
      });
      
      if (!implementation) {
        // Create new implementation
        implementation = new SOC2ControlImplementation({
          organizationId,
          controlId
        });
      }
      
      // Update implementation
      if (data.implementationStatus) {
        implementation.implementationStatus = {
          ...implementation.implementationStatus,
          ...data.implementationStatus,
          lastAssessedAt: new Date(),
          lastAssessedBy: userId
        };
      }
      
      if (data.responsibleParty) {
        implementation.responsibleParty = data.responsibleParty;
      }
      
      if (data.customPolicies) {
        implementation.customPolicies = data.customPolicies;
      }
      
      if (data.customProcedures) {
        implementation.customProcedures = data.customProcedures;
      }
      
      if (data.notes) {
        implementation.notes = data.notes;
      }
      
      implementation.lastUpdatedBy = userId;
      
      await implementation.save();
      
      logger.info('SOC 2 control implementation updated', { organizationId, controlId });
      
      return implementation;
    } catch (error) {
      logger.error('Error updating SOC 2 control implementation', { organizationId, controlId, error });
      throw error;
    }
  }
  
  /**
   * Get all SOC 2 control implementations for an organization
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Array>} - Control implementations
   */
  async getAllControlImplementations(organizationId) {
    try {
      // Get all controls
      const controls = await SOC2Control.find({ status: 'active' });
      
      // Get all implementations
      const implementations = await SOC2ControlImplementation.findByOrganization(organizationId);
      
      // Map implementations to controls
      const result = await Promise.all(
        controls.map(async (control) => {
          const implementation = implementations.find(impl => impl.controlId === control.id);
          
          if (implementation) {
            return {
              control,
              implementation
            };
          } else {
            // Create default implementation
            const newImplementation = new SOC2ControlImplementation({
              organizationId,
              controlId: control.id,
              implementationStatus: {
                status: 'not_implemented',
                completeness: 0
              }
            });
            
            await newImplementation.save();
            
            return {
              control,
              implementation: newImplementation
            };
          }
        })
      );
      
      return result;
    } catch (error) {
      logger.error('Error getting all SOC 2 control implementations', { organizationId, error });
      throw error;
    }
  }
  
  /**
   * Get SOC 2 control implementation summary for an organization
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Object>} - Implementation summary
   */
  async getImplementationSummary(organizationId) {
    try {
      // Get all implementations
      const implementations = await SOC2ControlImplementation.findByOrganization(organizationId);
      
      // Get all controls
      const controls = await SOC2Control.find({ status: 'active' });
      
      // Calculate summary
      const totalControls = controls.length;
      const implementedControls = implementations.filter(impl => impl.implementationStatus.status === 'implemented').length;
      const partiallyImplementedControls = implementations.filter(impl => impl.implementationStatus.status === 'partially_implemented').length;
      const notImplementedControls = totalControls - implementedControls - partiallyImplementedControls;
      
      // Calculate by category
      const categorySummary = {};
      
      for (const control of controls) {
        const implementation = implementations.find(impl => impl.controlId === control.id);
        const status = implementation ? implementation.implementationStatus.status : 'not_implemented';
        
        if (!categorySummary[control.category]) {
          categorySummary[control.category] = {
            total: 0,
            implemented: 0,
            partiallyImplemented: 0,
            notImplemented: 0
          };
        }
        
        categorySummary[control.category].total++;
        
        if (status === 'implemented') {
          categorySummary[control.category].implemented++;
        } else if (status === 'partially_implemented') {
          categorySummary[control.category].partiallyImplemented++;
        } else {
          categorySummary[control.category].notImplemented++;
        }
      }
      
      // Calculate by trust service criteria
      const criteriaSummary = {};
      
      for (const control of controls) {
        const implementation = implementations.find(impl => impl.controlId === control.id);
        const status = implementation ? implementation.implementationStatus.status : 'not_implemented';
        
        for (const criteria of control.trustServiceCriteria) {
          if (!criteriaSummary[criteria]) {
            criteriaSummary[criteria] = {
              total: 0,
              implemented: 0,
              partiallyImplemented: 0,
              notImplemented: 0
            };
          }
          
          criteriaSummary[criteria].total++;
          
          if (status === 'implemented') {
            criteriaSummary[criteria].implemented++;
          } else if (status === 'partially_implemented') {
            criteriaSummary[criteria].partiallyImplemented++;
          } else {
            criteriaSummary[criteria].notImplemented++;
          }
        }
      }
      
      // Calculate overall completeness
      const overallCompleteness = implementations.reduce((sum, impl) => {
        return sum + (impl.implementationStatus.completeness || 0);
      }, 0) / (implementations.length || 1);
      
      return {
        totalControls,
        implementedControls,
        partiallyImplementedControls,
        notImplementedControls,
        overallCompleteness: Math.round(overallCompleteness),
        categorySummary,
        criteriaSummary
      };
    } catch (error) {
      logger.error('Error getting SOC 2 implementation summary', { organizationId, error });
      throw error;
    }
  }
  
  /**
   * Get category from reference
   * @param {string} reference - Control reference
   * @returns {string} - Category
   * @private
   */
  _getCategoryFromReference(reference) {
    if (reference.startsWith('CC1')) {
      return 'control_environment';
    } else if (reference.startsWith('CC2')) {
      return 'communication_information';
    } else if (reference.startsWith('CC3')) {
      return 'risk_assessment';
    } else if (reference.startsWith('CC4')) {
      return 'monitoring_activities';
    } else if (reference.startsWith('CC5')) {
      return 'control_activities';
    } else if (reference.startsWith('CC6')) {
      return 'logical_physical_access';
    } else if (reference.startsWith('CC7')) {
      return 'system_operations';
    } else if (reference.startsWith('CC8')) {
      return 'change_management';
    } else if (reference.startsWith('CC9')) {
      return 'risk_mitigation';
    } else {
      return 'control_activities';
    }
  }
  
  /**
   * Get trust service criteria from categories
   * @param {Array<string>} categories - Control categories
   * @returns {Array<string>} - Trust service criteria
   * @private
   */
  _getTrustServiceCriteriaFromCategories(categories) {
    const criteriaMap = {
      'security': ['security'],
      'availability': ['availability'],
      'processing_integrity': ['processing_integrity'],
      'confidentiality': ['confidentiality'],
      'privacy': ['privacy']
    };
    
    const result = new Set(['security']); // Security is always included
    
    if (categories) {
      for (const category of categories) {
        const criteria = criteriaMap[category];
        if (criteria) {
          for (const c of criteria) {
            result.add(c);
          }
        }
      }
    }
    
    return Array.from(result);
  }
  
  /**
   * Get default evidence requirements for a control
   * @param {string} reference - Control reference
   * @param {string} category - Control category
   * @returns {Array<Object>} - Evidence requirements
   * @private
   */
  _getDefaultEvidenceRequirements(reference, category) {
    const requirements = [];
    
    // Add policy document requirement
    requirements.push({
      name: 'Policy Document',
      description: `Policy document addressing ${reference} requirements`,
      type: 'document',
      required: true,
      frequency: 'one_time',
      automationPossible: false
    });
    
    // Add procedure document requirement
    requirements.push({
      name: 'Procedure Document',
      description: `Procedure document for implementing ${reference}`,
      type: 'document',
      required: true,
      frequency: 'one_time',
      automationPossible: false
    });
    
    // Add category-specific requirements
    if (category === 'logical_physical_access') {
      requirements.push({
        name: 'Access Control Configuration',
        description: 'Configuration of access controls in systems',
        type: 'configuration',
        required: true,
        frequency: 'quarterly',
        automationPossible: true,
        automationSource: 'aws'
      });
    } else if (category === 'system_operations') {
      requirements.push({
        name: 'System Logs',
        description: 'System logs showing monitoring and operations',
        type: 'log',
        required: true,
        frequency: 'monthly',
        automationPossible: true,
        automationSource: 'aws'
      });
    } else if (category === 'change_management') {
      requirements.push({
        name: 'Change Records',
        description: 'Records of changes made to systems',
        type: 'report',
        required: true,
        frequency: 'monthly',
        automationPossible: true,
        automationSource: 'github'
      });
    }
    
    return requirements;
  }
}

module.exports = new SOC2ControlService();
