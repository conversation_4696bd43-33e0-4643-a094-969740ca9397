# Default values for NovaConnect UAC
# This is a YAML-formatted file.

replicaCount: 3

image:
  repository: gcr.io/novafuse-production/novafuse-uac
  tag: latest
  pullPolicy: Always

nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "3001"
  prometheus.io/path: "/metrics"

podSecurityContext: {}

securityContext: {}

service:
  type: ClusterIP
  port: 80
  targetPort: 3001

ingress:
  enabled: true
  className: "gce"
  annotations:
    kubernetes.io/ingress.global-static-ip-name: "novafuse-ip"
    networking.gke.io/managed-certificates: "novafuse-cert"
    networking.gke.io/v1beta1.FrontendConfig: "novafuse-frontend-config"
  hosts:
    - host: api.novafuse.io
      paths:
        - path: /
          pathType: Prefix

resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 200m
    memory: 256Mi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app
            operator: In
            values:
            - novafuse-uac
        topologyKey: "kubernetes.io/hostname"

# Application configuration
config:
  # Environment
  NODE_ENV: "production"
  PORT: "3001"
  LOG_LEVEL: "info"
  
  # Features
  METRICS_ENABLED: "true"
  TRACING_ENABLED: "true"
  TRACING_EXPORTER: "gcp"
  CLUSTER_ENABLED: "true"
  MAX_WORKERS: "4"
  CACHE_ENABLED: "true"
  CACHE_PROVIDER: "redis"
  CACHE_DEFAULT_TTL: "300"
  FEATURE_FLAG_SOURCE: "file"
  DEFAULT_TIER: "core"
  
  # Security
  RATE_LIMIT_WINDOW_MS: "60000"
  RATE_LIMIT_MAX: "100"
  CORS_ORIGIN: "https://novafuse.io"
  SENTRY_ENABLED: "true"

# Secrets
secrets:
  # These are placeholder values - real secrets should be managed by a secret management system
  MONGODB_URI: "mongodb://mongodb:27017/novafuse"
  REDIS_URI: "redis://redis:6379"
  JWT_SECRET: "jwt-secret-key"
  SENTRY_DSN: "https://<EMAIL>/novafuse"

# Dependencies
mongodb:
  enabled: true
  auth:
    enabled: true
    rootPassword: "root-password"
    username: "novafuse"
    password: "novafuse-password"
    database: "novafuse"
  persistence:
    enabled: true
    size: 10Gi

redis:
  enabled: true
  auth:
    enabled: true
    password: "redis-password"
  persistence:
    enabled: true
    size: 5Gi
