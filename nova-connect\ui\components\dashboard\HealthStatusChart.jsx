import React from 'react';
import { Box, Typography, useTheme } from '@mui/material';
import { 
  RadarChart, 
  PolarGrid, 
  PolarAngleAxis, 
  PolarRadiusAxis, 
  Radar, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts';

/**
 * Health Status Chart Component
 * 
 * Displays charts for health status metrics.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.health - Health status data
 */
const HealthStatusChart = ({ health }) => {
  const theme = useTheme();
  
  // If no health data is available, show a message
  if (!health) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%' 
      }}>
        <Typography variant="body2" color="text.secondary">
          No health status data available
        </Typography>
      </Box>
    );
  }
  
  // Prepare data for radar chart
  const radarData = [
    {
      subject: 'Offline Processing',
      A: health.metrics.offlineProcessing.successRate * 100,
      fullMark: 100
    },
    {
      subject: 'Cross-Domain',
      A: health.metrics.crossDomainPrediction.successRate * 100,
      fullMark: 100
    },
    {
      subject: 'Compliance',
      A: health.metrics.complianceMapping.successRate * 100,
      fullMark: 100
    },
    {
      subject: 'Overall',
      A: health.metrics.overallSuccessRate * 100,
      fullMark: 100
    }
  ];
  
  // Prepare data for latency pie chart
  const latencyData = [
    {
      name: 'Offline Processing',
      value: health.metrics.offlineProcessing.averageLatency,
      color: theme.palette.primary.main
    },
    {
      name: 'Cross-Domain',
      value: health.metrics.crossDomainPrediction.averageLatency,
      color: theme.palette.secondary.main
    },
    {
      name: 'Compliance',
      value: health.metrics.complianceMapping.averageLatency,
      color: theme.palette.info.main
    }
  ];
  
  // Prepare data for requests distribution
  const requestsData = [
    {
      name: 'Successful',
      value: health.metrics.successfulRequests,
      color: theme.palette.success.main
    },
    {
      name: 'Failed',
      value: health.metrics.totalRequests - health.metrics.successfulRequests,
      color: theme.palette.error.main
    }
  ];
  
  // Get health status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy':
        return theme.palette.success.main;
      case 'degraded':
        return theme.palette.warning.main;
      case 'unhealthy':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };
  
  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };
  
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      {/* Health Status Header */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        mb: 1
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box 
            sx={{ 
              width: 12, 
              height: 12, 
              borderRadius: '50%', 
              bgcolor: getStatusColor(health.status),
              mr: 1
            }} 
          />
          <Typography variant="body2" fontWeight="bold">
            Status: {health.status.toUpperCase()}
          </Typography>
        </Box>
        <Typography variant="caption" color="text.secondary">
          Last Updated: {formatTimestamp(health.timestamp)}
        </Typography>
      </Box>
      
      {/* Charts */}
      <Box sx={{ display: 'flex', flexGrow: 1 }}>
        {/* Success Rate Radar Chart */}
        <Box sx={{ width: '60%', height: '100%' }}>
          <Typography variant="caption" color="text.secondary" align="center" sx={{ display: 'block' }}>
            Success Rate by Feature
          </Typography>
          <ResponsiveContainer width="100%" height="100%">
            <RadarChart 
              cx="50%" 
              cy="50%" 
              outerRadius="80%" 
              data={radarData}
            >
              <PolarGrid />
              <PolarAngleAxis dataKey="subject" tick={{ fontSize: 10 }} />
              <PolarRadiusAxis 
                angle={30} 
                domain={[0, 100]} 
                tick={{ fontSize: 10 }}
                tickFormatter={(value) => `${value}%`}
              />
              <Radar 
                name="Success Rate" 
                dataKey="A" 
                stroke={theme.palette.primary.main} 
                fill={theme.palette.primary.main} 
                fillOpacity={0.6} 
              />
              <Tooltip 
                formatter={(value) => [`${value.toFixed(1)}%`, 'Success Rate']}
              />
              <Legend />
            </RadarChart>
          </ResponsiveContainer>
        </Box>
        
        {/* Pie Charts */}
        <Box sx={{ width: '40%', height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* Latency Distribution */}
          <Box sx={{ height: '50%' }}>
            <Typography variant="caption" color="text.secondary" align="center" sx={{ display: 'block' }}>
              Latency Distribution
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <PieChart>
                <Pie
                  data={latencyData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={60}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {latencyData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value) => [`${value.toFixed(2)}ms`, 'Avg. Latency']}
                />
              </PieChart>
            </ResponsiveContainer>
          </Box>
          
          {/* Requests Distribution */}
          <Box sx={{ height: '50%' }}>
            <Typography variant="caption" color="text.secondary" align="center" sx={{ display: 'block' }}>
              Requests Distribution
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <PieChart>
                <Pie
                  data={requestsData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={60}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {requestsData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value) => [value, 'Requests']}
                />
              </PieChart>
            </ResponsiveContainer>
          </Box>
        </Box>
      </Box>
      
      {/* Health Metrics Summary */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-around', 
        mt: 2,
        p: 1,
        bgcolor: theme.palette.background.default,
        borderRadius: 1
      }}>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Total Requests
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {health.metrics.totalRequests}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Success Rate
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {(health.metrics.overallSuccessRate * 100).toFixed(1)}%
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Offline Latency
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {health.metrics.offlineProcessing.averageLatency.toFixed(2)}ms
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Prediction Latency
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {health.metrics.crossDomainPrediction.averageLatency.toFixed(2)}ms
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default HealthStatusChart;
