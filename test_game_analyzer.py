#!/usr/bin/env python3
"""
Test script for UUFT Game Theory Analyzer
"""

import os
import logging
import numpy as np
import matplotlib.pyplot as plt
from uuft_game_analyzer import UUFTGameScenario

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_game.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('Test_Game')

# Constants
RESULTS_DIR = "uuft_results/game"
os.makedirs(RESULTS_DIR, exist_ok=True)

def test_game_scenario():
    """Test the game theory scenario generation."""
    logger.info("Testing game theory scenario generation")
    
    # Create a Prisoner's Dilemma game
    game = UUFTGameScenario(
        game_type="prisoners_dilemma",
        num_players=2,
        num_strategies=2,
        uuft_bias=0.0
    )
    
    # Print payoff matrix
    logger.info(f"Payoff matrix:\n{game.payoff_matrix}")
    
    # Find Nash equilibria
    equilibria = game.find_nash_equilibria()
    logger.info(f"Nash equilibria: {equilibria}")
    
    # Calculate mixed equilibrium
    mixed_eq = game.calculate_mixed_equilibrium()
    logger.info(f"Mixed equilibrium: {mixed_eq}")
    
    # Visualize payoff matrix
    game.visualize_payoff_matrix(
        save_path=os.path.join(RESULTS_DIR, "test_payoff_matrix.png")
    )
    
    # Test with UUFT bias
    logger.info("Testing game with UUFT bias")
    game_with_bias = UUFTGameScenario(
        game_type="prisoners_dilemma",
        num_players=2,
        num_strategies=2,
        uuft_bias=0.7
    )
    
    # Print payoff matrix with bias
    logger.info(f"Payoff matrix with UUFT bias:\n{game_with_bias.payoff_matrix}")
    
    # Find Nash equilibria with bias
    equilibria_with_bias = game_with_bias.find_nash_equilibria()
    logger.info(f"Nash equilibria with UUFT bias: {equilibria_with_bias}")
    
    # Visualize payoff matrix with bias
    game_with_bias.visualize_payoff_matrix(
        save_path=os.path.join(RESULTS_DIR, "test_payoff_matrix_with_bias.png")
    )
    
    logger.info("Game theory scenario test completed successfully")

if __name__ == "__main__":
    test_game_scenario()
