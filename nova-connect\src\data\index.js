/**
 * NovaFuse Universal API Connector - Data Layer
 * 
 * This module exports the data layer components.
 */

const dbConnection = require('./db-connection');
const models = {
  Connector: require('./models/connector'),
  Credential: require('./models/credential'),
  ApiUsage: require('./models/api-usage'),
  Partner: require('./models/partner')
};
const repositories = require('./repositories');

module.exports = {
  dbConnection,
  models,
  repositories
};
