/**
 * @swagger
 * components:
 *   schemas:
 *     ESGMetric:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the ESG metric
 *         name:
 *           type: string
 *           description: Name of the ESG metric
 *         description:
 *           type: string
 *           description: Description of the ESG metric
 *         category:
 *           type: string
 *           enum: [environmental, social, governance]
 *           description: Category of the ESG metric
 *         subcategory:
 *           type: string
 *           description: Subcategory of the ESG metric
 *         unit:
 *           type: string
 *           description: Unit of measurement for the metric
 *         dataType:
 *           type: string
 *           enum: [numeric, percentage, boolean, text, date]
 *           description: Data type of the metric value
 *         isStandardized:
 *           type: boolean
 *           description: Whether this is a standardized industry metric or custom
 *         standardId:
 *           type: string
 *           description: Identifier in a standard framework (e.g., GRI 305-1)
 *         frameworkMappings:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               frameworkId:
 *                 type: string
 *                 description: ID of the framework
 *               elementId:
 *                 type: string
 *                 description: ID of the framework element
 *               version:
 *                 type: string
 *                 description: Version of the framework
 *         calculationMethod:
 *           type: string
 *           description: Method used to calculate the metric
 *         calculationFormula:
 *           type: string
 *           description: Formula used to calculate the metric
 *         dataCollectionFrequency:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, annually, custom]
 *           description: Frequency of data collection
 *         verificationRequired:
 *           type: boolean
 *           description: Whether verification is required for data points
 *         verificationProcess:
 *           type: string
 *           description: Description of the verification process
 *         benchmarkValue:
 *           type: string
 *           description: Industry benchmark value for comparison
 *         benchmarkSource:
 *           type: string
 *           description: Source of the benchmark value
 *         targetValue:
 *           type: string
 *           description: Target value for the metric
 *         targetDate:
 *           type: string
 *           format: date
 *           description: Target date for achieving the target value
 *         owner:
 *           type: string
 *           description: Owner of the metric
 *         status:
 *           type: string
 *           enum: [active, inactive, archived]
 *           description: Status of the metric
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           description: Tags for categorizing and searching metrics
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the metric was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the metric was last updated
 *       required:
 *         - id
 *         - name
 *         - category
 *         - dataType
 *         - status
 *         - createdAt
 *         - updatedAt
 *
 *     ESGMetricInput:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Name of the ESG metric
 *         description:
 *           type: string
 *           description: Description of the ESG metric
 *         category:
 *           type: string
 *           enum: [environmental, social, governance]
 *           description: Category of the ESG metric
 *         subcategory:
 *           type: string
 *           description: Subcategory of the ESG metric
 *         unit:
 *           type: string
 *           description: Unit of measurement for the metric
 *         dataType:
 *           type: string
 *           enum: [numeric, percentage, boolean, text, date]
 *           description: Data type of the metric value
 *         isStandardized:
 *           type: boolean
 *           description: Whether this is a standardized industry metric or custom
 *         standardId:
 *           type: string
 *           description: Identifier in a standard framework (e.g., GRI 305-1)
 *         frameworkMappings:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               frameworkId:
 *                 type: string
 *                 description: ID of the framework
 *               elementId:
 *                 type: string
 *                 description: ID of the framework element
 *               version:
 *                 type: string
 *                 description: Version of the framework
 *         calculationMethod:
 *           type: string
 *           description: Method used to calculate the metric
 *         calculationFormula:
 *           type: string
 *           description: Formula used to calculate the metric
 *         dataCollectionFrequency:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, annually, custom]
 *           description: Frequency of data collection
 *         verificationRequired:
 *           type: boolean
 *           description: Whether verification is required for data points
 *         verificationProcess:
 *           type: string
 *           description: Description of the verification process
 *         benchmarkValue:
 *           type: string
 *           description: Industry benchmark value for comparison
 *         benchmarkSource:
 *           type: string
 *           description: Source of the benchmark value
 *         targetValue:
 *           type: string
 *           description: Target value for the metric
 *         targetDate:
 *           type: string
 *           format: date
 *           description: Target date for achieving the target value
 *         owner:
 *           type: string
 *           description: Owner of the metric
 *         status:
 *           type: string
 *           enum: [active, inactive, archived]
 *           description: Status of the metric
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           description: Tags for categorizing and searching metrics
 *       required:
 *         - name
 *         - category
 *         - dataType
 *         - status
 *
 *     ESGDataPoint:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the data point
 *         metricId:
 *           type: string
 *           description: ID of the ESG metric this data point belongs to
 *         value:
 *           type: string
 *           description: Value of the data point
 *         date:
 *           type: string
 *           format: date
 *           description: Date the data point was recorded for
 *         period:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, annually]
 *           description: Reporting period for the data point
 *         source:
 *           type: string
 *           description: Source of the data
 *         notes:
 *           type: string
 *           description: Additional notes about the data point
 *         verificationStatus:
 *           type: string
 *           enum: [unverified, verified, rejected]
 *           description: Verification status of the data point
 *         verifiedBy:
 *           type: string
 *           description: Person who verified the data point
 *         verifiedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the data point was verified
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the data point was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the data point was last updated
 *       required:
 *         - id
 *         - metricId
 *         - value
 *         - date
 *         - period
 *         - verificationStatus
 *         - createdAt
 *         - updatedAt
 *
 *     ESGDataPointInput:
 *       type: object
 *       properties:
 *         value:
 *           type: string
 *           description: Value of the data point
 *         date:
 *           type: string
 *           format: date
 *           description: Date the data point was recorded for
 *         period:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, annually]
 *           description: Reporting period for the data point
 *         source:
 *           type: string
 *           description: Source of the data
 *         notes:
 *           type: string
 *           description: Additional notes about the data point
 *         verificationStatus:
 *           type: string
 *           enum: [unverified, verified, rejected]
 *           description: Verification status of the data point
 *         verifiedBy:
 *           type: string
 *           description: Person who verified the data point
 *         verifiedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the data point was verified
 *       required:
 *         - value
 *         - date
 *         - period
 *         - verificationStatus
 *
 *     ESGInitiative:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the ESG initiative
 *         name:
 *           type: string
 *           description: Name of the ESG initiative
 *         description:
 *           type: string
 *           description: Description of the ESG initiative
 *         category:
 *           type: string
 *           enum: [environmental, social, governance]
 *           description: Category of the ESG initiative
 *         startDate:
 *           type: string
 *           format: date
 *           description: Start date of the initiative
 *         endDate:
 *           type: string
 *           format: date
 *           description: End date of the initiative
 *         status:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *           description: Status of the initiative
 *         owner:
 *           type: string
 *           description: Owner of the initiative
 *         budget:
 *           type: number
 *           description: Budget allocated for the initiative
 *         metrics:
 *           type: array
 *           items:
 *             type: string
 *           description: IDs of ESG metrics associated with this initiative
 *         goals:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               description:
 *                 type: string
 *               targetDate:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [not-started, in-progress, completed, cancelled]
 *           description: Goals of the initiative
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the initiative was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the initiative was last updated
 *       required:
 *         - id
 *         - name
 *         - category
 *         - status
 *         - createdAt
 *         - updatedAt
 *
 *     ESGInitiativeInput:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Name of the ESG initiative
 *         description:
 *           type: string
 *           description: Description of the ESG initiative
 *         category:
 *           type: string
 *           enum: [environmental, social, governance]
 *           description: Category of the ESG initiative
 *         startDate:
 *           type: string
 *           format: date
 *           description: Start date of the initiative
 *         endDate:
 *           type: string
 *           format: date
 *           description: End date of the initiative
 *         status:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *           description: Status of the initiative
 *         owner:
 *           type: string
 *           description: Owner of the initiative
 *         budget:
 *           type: number
 *           description: Budget allocated for the initiative
 *         metrics:
 *           type: array
 *           items:
 *             type: string
 *           description: IDs of ESG metrics associated with this initiative
 *         goals:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               description:
 *                 type: string
 *               targetDate:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [not-started, in-progress, completed, cancelled]
 *           description: Goals of the initiative
 *       required:
 *         - name
 *         - category
 *         - status
 */

// Sample ESG metrics
const esgMetrics = [
  {
    id: 'esg-m-001',
    name: 'Carbon Emissions',
    description: 'Total greenhouse gas emissions in metric tons of CO2 equivalent',
    category: 'environmental',
    subcategory: 'climate change',
    unit: 'tCO2e',
    dataType: 'numeric',
    isStandardized: true,
    standardId: 'GRI 305-1',
    frameworkMappings: [
      {
        frameworkId: 'frm-001', // GRI
        elementId: 'ele-010', // GRI 305-1
        version: '2021'
      },
      {
        frameworkId: 'frm-003', // TCFD
        elementId: 'ele-020', // Metrics and Targets
        version: '2017'
      }
    ],
    calculationMethod: 'Direct measurement and calculation based on emission factors',
    calculationFormula: 'Fuel consumption * Emission factor',
    dataCollectionFrequency: 'monthly',
    verificationRequired: true,
    verificationProcess: 'Third-party verification by accredited verifier',
    benchmarkValue: '5500',
    benchmarkSource: 'Industry average for similar sized companies in the sector',
    targetValue: '5000',
    targetDate: '2025-12-31',
    owner: 'Sustainability Team',
    status: 'active',
    tags: ['climate', 'emissions', 'scope1', 'carbon'],
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-10T09:00:00Z'
  },
  {
    id: 'esg-m-002',
    name: 'Energy Consumption',
    description: 'Total energy consumption within the organization in megawatt hours',
    category: 'environmental',
    subcategory: 'energy',
    unit: 'MWh',
    dataType: 'numeric',
    isStandardized: true,
    standardId: 'GRI 302-1',
    frameworkMappings: [
      {
        frameworkId: 'frm-001', // GRI
        elementId: 'ele-005', // GRI 302-1
        version: '2021'
      },
      {
        frameworkId: 'frm-002', // SASB
        elementId: 'ele-015', // Energy Management
        version: '2018'
      }
    ],
    calculationMethod: 'Direct measurement from utility bills and meters',
    calculationFormula: 'Sum of all energy sources converted to MWh',
    dataCollectionFrequency: 'monthly',
    verificationRequired: true,
    verificationProcess: 'Internal audit with spot checks',
    benchmarkValue: '12000',
    benchmarkSource: 'Industry average for similar sized companies in the sector',
    targetValue: '10000',
    targetDate: '2025-12-31',
    owner: 'Facilities Management',
    status: 'active',
    tags: ['energy', 'consumption', 'efficiency'],
    createdAt: '2024-01-10T09:15:00Z',
    updatedAt: '2024-01-10T09:15:00Z'
  },
  {
    id: 'esg-m-003',
    name: 'Gender Diversity',
    description: 'Percentage of women in the workforce',
    category: 'social',
    subcategory: 'diversity and inclusion',
    unit: '%',
    dataType: 'percentage',
    framework: 'GRI 405',
    targetValue: '50',
    targetDate: '2026-12-31',
    owner: 'Human Resources',
    status: 'active',
    createdAt: '2024-01-10T09:30:00Z',
    updatedAt: '2024-01-10T09:30:00Z'
  },
  {
    id: 'esg-m-004',
    name: 'Board Independence',
    description: 'Percentage of independent directors on the board',
    category: 'governance',
    subcategory: 'board structure',
    unit: '%',
    dataType: 'percentage',
    framework: 'SASB',
    targetValue: '75',
    targetDate: '2024-12-31',
    owner: 'Corporate Secretary',
    status: 'active',
    createdAt: '2024-01-10T09:45:00Z',
    updatedAt: '2024-01-10T09:45:00Z'
  },
  {
    id: 'esg-m-005',
    name: 'Water Consumption',
    description: 'Total water consumption in cubic meters',
    category: 'environmental',
    subcategory: 'water',
    unit: 'm³',
    dataType: 'numeric',
    framework: 'GRI 303',
    targetValue: '50000',
    targetDate: '2025-12-31',
    owner: 'Sustainability Team',
    status: 'active',
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-10T10:00:00Z'
  }
];

// Sample ESG data points
const esgDataPoints = [
  {
    id: 'esg-d-001',
    metricId: 'esg-m-001',
    value: '6500',
    date: '2023-12-31',
    period: 'annually',
    source: 'Energy Management System',
    notes: 'Verified by third-party auditor',
    verificationStatus: 'verified',
    verifiedBy: 'EcoAudit Inc.',
    verifiedAt: '2024-01-15T14:30:00Z',
    createdAt: '2024-01-05T11:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z'
  },
  {
    id: 'esg-d-002',
    metricId: 'esg-m-002',
    value: '12500',
    date: '2023-12-31',
    period: 'annually',
    source: 'Utility Bills',
    notes: 'Includes all facilities',
    verificationStatus: 'verified',
    verifiedBy: 'Internal Audit',
    verifiedAt: '2024-01-20T10:15:00Z',
    createdAt: '2024-01-05T11:15:00Z',
    updatedAt: '2024-01-20T10:15:00Z'
  },
  {
    id: 'esg-d-003',
    metricId: 'esg-m-003',
    value: '42',
    date: '2023-12-31',
    period: 'annually',
    source: 'HR Information System',
    notes: 'Includes all full-time employees',
    verificationStatus: 'verified',
    verifiedBy: 'HR Director',
    verifiedAt: '2024-01-10T16:45:00Z',
    createdAt: '2024-01-05T11:30:00Z',
    updatedAt: '2024-01-10T16:45:00Z'
  },
  {
    id: 'esg-d-004',
    metricId: 'esg-m-004',
    value: '70',
    date: '2023-12-31',
    period: 'annually',
    source: 'Corporate Governance Report',
    notes: '7 out of 10 directors are independent',
    verificationStatus: 'verified',
    verifiedBy: 'Corporate Secretary',
    verifiedAt: '2024-01-12T09:30:00Z',
    createdAt: '2024-01-05T11:45:00Z',
    updatedAt: '2024-01-12T09:30:00Z'
  },
  {
    id: 'esg-d-005',
    metricId: 'esg-m-005',
    value: '58000',
    date: '2023-12-31',
    period: 'annually',
    source: 'Water Utility Bills',
    notes: 'Includes all facilities',
    verificationStatus: 'unverified',
    verifiedBy: '',
    verifiedAt: null,
    createdAt: '2024-01-05T12:00:00Z',
    updatedAt: '2024-01-05T12:00:00Z'
  }
];

// Sample ESG initiatives
const esgInitiatives = [
  {
    id: 'esg-i-001',
    name: 'Carbon Neutrality Program',
    description: 'Initiative to achieve carbon neutrality by 2030',
    category: 'environmental',
    startDate: '2024-01-01',
    endDate: '2030-12-31',
    status: 'in-progress',
    owner: 'Sustainability Director',
    budget: 500000,
    metrics: ['esg-m-001', 'esg-m-002'],
    goals: [
      {
        description: 'Reduce carbon emissions by 20%',
        targetDate: '2025-12-31',
        status: 'in-progress'
      },
      {
        description: 'Implement renewable energy at all major facilities',
        targetDate: '2027-12-31',
        status: 'not-started'
      }
    ],
    createdAt: '2023-12-15T10:00:00Z',
    updatedAt: '2024-01-05T14:30:00Z'
  },
  {
    id: 'esg-i-002',
    name: 'Diversity and Inclusion Program',
    description: 'Initiative to improve diversity and inclusion across the organization',
    category: 'social',
    startDate: '2024-01-01',
    endDate: '2026-12-31',
    status: 'in-progress',
    owner: 'Chief People Officer',
    budget: 300000,
    metrics: ['esg-m-003'],
    goals: [
      {
        description: 'Achieve 45% women in workforce',
        targetDate: '2025-12-31',
        status: 'in-progress'
      },
      {
        description: 'Implement unconscious bias training for all employees',
        targetDate: '2024-06-30',
        status: 'in-progress'
      }
    ],
    createdAt: '2023-12-15T10:30:00Z',
    updatedAt: '2024-01-05T14:45:00Z'
  },
  {
    id: 'esg-i-003',
    name: 'Water Conservation Project',
    description: 'Initiative to reduce water consumption across all facilities',
    category: 'environmental',
    startDate: '2024-03-01',
    endDate: '2025-12-31',
    status: 'planned',
    owner: 'Facilities Manager',
    budget: 150000,
    metrics: ['esg-m-005'],
    goals: [
      {
        description: 'Reduce water consumption by 15%',
        targetDate: '2025-12-31',
        status: 'not-started'
      },
      {
        description: 'Install water-efficient fixtures in all facilities',
        targetDate: '2024-12-31',
        status: 'not-started'
      }
    ],
    createdAt: '2024-01-15T11:00:00Z',
    updatedAt: '2024-01-15T11:00:00Z'
  }
];

// ESG categories for reference
const esgCategories = [
  {
    id: 'environmental',
    name: 'Environmental',
    description: 'Metrics related to environmental impact and sustainability',
    subcategories: [
      'climate change',
      'energy',
      'water',
      'waste',
      'biodiversity',
      'emissions',
      'resource use'
    ]
  },
  {
    id: 'social',
    name: 'Social',
    description: 'Metrics related to social impact, employees, and communities',
    subcategories: [
      'diversity and inclusion',
      'human rights',
      'labor practices',
      'employee health and safety',
      'community engagement',
      'data privacy',
      'product responsibility'
    ]
  },
  {
    id: 'governance',
    name: 'Governance',
    description: 'Metrics related to corporate governance and ethical business practices',
    subcategories: [
      'board structure',
      'executive compensation',
      'business ethics',
      'risk management',
      'transparency',
      'anti-corruption',
      'tax strategy'
    ]
  }
];

// ESG frameworks for reference
const esgFrameworks = [
  {
    id: 'gri',
    name: 'Global Reporting Initiative (GRI)',
    description: 'Standards for sustainability reporting on environmental, social, and governance impacts'
  },
  {
    id: 'sasb',
    name: 'Sustainability Accounting Standards Board (SASB)',
    description: 'Industry-specific standards for disclosure of financially material sustainability information'
  },
  {
    id: 'tcfd',
    name: 'Task Force on Climate-related Financial Disclosures (TCFD)',
    description: 'Framework for climate-related financial risk disclosures'
  },
  {
    id: 'cdp',
    name: 'Carbon Disclosure Project (CDP)',
    description: 'Framework for environmental reporting with focus on climate change, water, and forests'
  },
  {
    id: 'sdg',
    name: 'Sustainable Development Goals (SDGs)',
    description: 'United Nations framework of 17 goals for sustainable development'
  }
];

module.exports = {
  esgMetrics,
  esgDataPoints,
  esgInitiatives,
  esgCategories,
  esgFrameworks
};
