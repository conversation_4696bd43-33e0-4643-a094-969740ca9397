/**
 * NovaFuse Universal API Connector - Connection Error
 * 
 * This module defines connection-related errors for the UAC.
 */

const UAConnectorError = require('./base-error');

/**
 * Error class for connection failures
 * @class ConnectionError
 * @extends UAConnectorError
 */
class ConnectionError extends UAConnectorError {
  /**
   * Create a new ConnectionError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {string} options.severity - Error severity
   * @param {Object} options.context - Additional context for the error
   * @param {Error} options.cause - The error that caused this error
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'CONNECTION_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'Failed to connect to the service. Please check your network connection and try again.';
  }
}

/**
 * Error class for timeout errors
 * @class TimeoutError
 * @extends ConnectionError
 */
class TimeoutError extends ConnectionError {
  /**
   * Create a new TimeoutError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'The request timed out', options = {}) {
    super(message, {
      code: options.code || 'CONNECTION_TIMEOUT',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'The request timed out. Please try again later or contact support if the issue persists.';
  }
}

/**
 * Error class for network errors
 * @class NetworkError
 * @extends ConnectionError
 */
class NetworkError extends ConnectionError {
  /**
   * Create a new NetworkError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'A network error occurred', options = {}) {
    super(message, {
      code: options.code || 'CONNECTION_NETWORK_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'A network error occurred. Please check your internet connection and try again.';
  }
}

/**
 * Error class for service unavailable errors
 * @class ServiceUnavailableError
 * @extends ConnectionError
 */
class ServiceUnavailableError extends ConnectionError {
  /**
   * Create a new ServiceUnavailableError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'The service is currently unavailable', options = {}) {
    super(message, {
      code: options.code || 'CONNECTION_SERVICE_UNAVAILABLE',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'The service is currently unavailable. Please try again later.';
  }
}

/**
 * Error class for DNS resolution errors
 * @class DnsResolutionError
 * @extends ConnectionError
 */
class DnsResolutionError extends ConnectionError {
  /**
   * Create a new DnsResolutionError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'Failed to resolve the hostname', options = {}) {
    super(message, {
      code: options.code || 'CONNECTION_DNS_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'Failed to connect to the service. The hostname could not be resolved.';
  }
}

module.exports = {
  ConnectionError,
  TimeoutError,
  NetworkError,
  ServiceUnavailableError,
  DnsResolutionError
};
