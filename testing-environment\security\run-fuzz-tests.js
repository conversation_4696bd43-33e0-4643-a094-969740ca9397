/**
 * Fuzz Testing Script for NovaConnect Universal API Connector
 * 
 * This script runs fuzz tests against the NovaConnect API endpoints.
 */

const { runFuzzTestSuite } = require('./fuzzer');

// Configuration
const BASE_URL = 'http://localhost:3000'; // Auth service
const REGISTRY_URL = 'http://localhost:3001'; // Connector registry
const EXECUTOR_URL = 'http://localhost:3002'; // Connector executor

// Define endpoints to test
const endpoints = [
  // Auth Service Endpoints
  {
    url: `${BASE_URL}/credentials`,
    method: 'POST',
    template: {
      name: 'string',
      connectorId: 'string',
      authType: 'string',
      credentials: 'object',
      userId: 'string'
    },
    iterations: 50
  },
  {
    url: `${BASE_URL}/credentials/12345`,
    method: 'PUT',
    template: {
      name: 'string',
      credentials: 'object'
    },
    iterations: 50
  },
  
  // Connector Registry Endpoints
  {
    url: `${REGISTRY_URL}/connectors`,
    method: 'POST',
    template: {
      name: 'string',
      version: 'string',
      category: 'string',
      description: 'string',
      author: 'string',
      tags: 'array',
      authentication: 'object',
      configuration: 'object',
      endpoints: 'array',
      mappings: 'array'
    },
    iterations: 50
  },
  {
    url: `${REGISTRY_URL}/transformations`,
    method: 'POST',
    template: {
      name: 'string',
      code: 'string',
      description: 'string'
    },
    iterations: 50
  },
  
  // Connector Executor Endpoints
  {
    url: `${EXECUTOR_URL}/execute/12345/getResource`,
    method: 'POST',
    template: {
      credentialId: 'string',
      parameters: 'object',
      userId: 'string'
    },
    iterations: 50
  }
];

// Global options
const options = {
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'NovaConnect-Fuzz-Tester/1.0'
  },
  timeout: 10000 // 10 seconds
};

// Run the fuzz test suite
async function runTests() {
  console.log('Starting NovaConnect API Fuzz Testing...');
  
  try {
    const results = await runFuzzTestSuite(endpoints, options);
    
    // Check if all endpoints passed
    if (results.summary.failed === 0) {
      console.log('\n✅ All endpoints passed fuzz testing!');
      process.exit(0);
    } else {
      console.log(`\n❌ ${results.summary.failed} endpoints failed fuzz testing.`);
      
      // Print details of failed endpoints
      results.endpoints
        .filter(endpoint => endpoint.summary.serverErrors > 0)
        .forEach(endpoint => {
          console.log(`\nEndpoint: ${endpoint.method} ${endpoint.url}`);
          console.log(`Server Errors: ${endpoint.summary.serverErrors}`);
          console.log('First few errors:');
          endpoint.serverErrors.slice(0, 3).forEach((error, index) => {
            console.log(`  ${index + 1}. Status: ${error.status}, Message: ${error.statusText}`);
          });
        });
      
      process.exit(1);
    }
  } catch (error) {
    console.error('Error running fuzz tests:', error.message);
    process.exit(1);
  }
}

// Run the tests
runTests();
