import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const router = useRouter();

  const isActive = (path) => {
    return router.pathname === path ? 'text-blue-400' : 'text-gray-300 hover:text-white';
  };

  return (
    <nav className="bg-secondary py-4">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center">
          <Link href="/" className="text-2xl font-bold">
            NovaFuse API Superstore
          </Link>

          {/* Mobile menu button */}
          <button
            className="md:hidden text-gray-300 hover:text-white"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              {isMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>

          {/* Desktop menu */}
          <div className="hidden md:flex space-x-6">
            <Link href="/" className={isActive('/')}>
              Home
            </Link>
            <Link href="/api-docs" className={isActive('/api-docs')}>
              API Docs
            </Link>
            <div className="relative group">
              <button className={`flex items-center ${isActive('/partner-ecosystem') || isActive('/partner-empowerment') || isActive('/partner-onboarding') ? 'text-blue-400' : 'text-gray-300 hover:text-white'}`}>
                Partners
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute left-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg z-10 hidden group-hover:block">
                <div className="py-1">
                  <Link href="/partner-ecosystem" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                    Partner Ecosystem
                  </Link>
                  <Link href="/partner-empowerment" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                    Partner Empowerment
                  </Link>
                  <Link href="/partner-onboarding" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                    Partner Onboarding
                  </Link>
                  <Link href="/partner-knowledge-base" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                    Partner Knowledge Base
                  </Link>
                  <Link href="/google-partnership-portal" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                    Google Partnership
                  </Link>
                </div>
              </div>
            </div>
            <div className="relative group">
              <button className={`flex items-center ${isActive('/gamification-apis') || isActive('/gamification-apis-simple') || isActive('/gamification-suite') ? 'text-blue-400' : 'text-gray-300 hover:text-white'}`}>
                Gamification
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute left-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg z-10 hidden group-hover:block">
                <div className="py-1">
                  <Link href="/gamification-apis" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                    Gamification APIs
                  </Link>
                  <Link href="/gamification-apis-simple" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                    Simple APIs
                  </Link>
                  <Link href="/gamification-suite" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                    Gamification Suite
                  </Link>
                </div>
              </div>
            </div>
            <Link href="/novaconcierge" className={isActive('/novaconcierge')}>
              NovaConcierge
            </Link>
            <Link href="/uac-demo" className={isActive('/uac-demo')}>
              UAC Demo
            </Link>
            <Link href="/nova-ui-components" className={isActive('/nova-ui-components')}>
              NovaUI
            </Link>
            <div className="relative group">
              <button className={`flex items-center ${isActive('/legacy-vs-novafuse') || isActive('/competitor-matrix') || isActive('/tam-analysis') || isActive('/strategic-independence-plan') ? 'text-blue-400' : 'text-gray-300 hover:text-white'}`}>
                Why NovaFuse
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute left-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg z-10 hidden group-hover:block">
                <div className="py-1">
                  <Link href="/legacy-vs-novafuse" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                    Legacy vs NovaFuse
                  </Link>
                  <Link href="/competitor-matrix" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                    Competitor Matrix
                  </Link>
                  <Link href="/tam-analysis" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                    TAM Analysis
                  </Link>
                  <Link href="/strategic-independence-plan" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                    Strategic Plan
                  </Link>
                </div>
              </div>
            </div>
            <Link href="/technology-roadmap" className={isActive('/technology-roadmap')}>
              Roadmap
            </Link>
            <Link href="/early-access-program" className={isActive('/early-access-program')}>
              Early Access
            </Link>
          </div>

          {/* Desktop CTA buttons */}
          <div className="hidden md:flex space-x-4">
            <Link href="/partner-login" className="accent-bg text-white px-4 py-2 rounded hover:bg-blue-700">
              Partner Login
            </Link>
            <Link href="/become-partner" className="bg-white text-blue-700 px-4 py-2 rounded hover:bg-gray-100 border border-blue-400">
              Become a Partner
            </Link>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden mt-4 space-y-4">
            <div className="flex flex-col space-y-3">
              <Link href="/" className={`${isActive('/')} py-2`}>
                Home
              </Link>
              <Link href="/api-docs" className={`${isActive('/api-docs')} py-2`}>
                API Docs
              </Link>

              {/* Partners Section */}
              <div className="py-2 border-t border-gray-700">
                <h3 className="text-gray-400 font-semibold mb-2">Partners</h3>
                <div className="pl-4 flex flex-col space-y-2">
                  <Link href="/partner-ecosystem" className={`${isActive('/partner-ecosystem')} py-1`}>
                    Partner Ecosystem
                  </Link>
                  <Link href="/partner-empowerment" className={`${isActive('/partner-empowerment')} py-1`}>
                    Partner Empowerment
                  </Link>
                  <Link href="/partner-onboarding" className={`${isActive('/partner-onboarding')} py-1`}>
                    Partner Onboarding
                  </Link>
                  <Link href="/partner-knowledge-base" className={`${isActive('/partner-knowledge-base')} py-1`}>
                    Partner Knowledge Base
                  </Link>
                  <Link href="/google-partnership-portal" className={`${isActive('/google-partnership-portal')} py-1`}>
                    Google Partnership
                  </Link>
                </div>
              </div>

              {/* Gamification Section */}
              <div className="py-2 border-t border-gray-700">
                <h3 className="text-gray-400 font-semibold mb-2">Gamification</h3>
                <div className="pl-4 flex flex-col space-y-2">
                  <Link href="/gamification-apis" className={`${isActive('/gamification-apis')} py-1`}>
                    Gamification APIs
                  </Link>
                  <Link href="/gamification-apis-simple" className={`${isActive('/gamification-apis-simple')} py-1`}>
                    Simple APIs
                  </Link>
                  <Link href="/gamification-suite" className={`${isActive('/gamification-suite')} py-1`}>
                    Gamification Suite
                  </Link>
                </div>
              </div>

              <Link href="/novaconcierge" className={`${isActive('/novaconcierge')} py-2`}>
                NovaConcierge
              </Link>
              <Link href="/uac-demo" className={`${isActive('/uac-demo')} py-2`}>
                UAC Demo
              </Link>
              <Link href="/nova-ui-components" className={`${isActive('/nova-ui-components')} py-2`}>
                NovaUI
              </Link>

              {/* Why NovaFuse Section */}
              <div className="py-2 border-t border-gray-700">
                <h3 className="text-gray-400 font-semibold mb-2">Why NovaFuse</h3>
                <div className="pl-4 flex flex-col space-y-2">
                  <Link href="/legacy-vs-novafuse" className={`${isActive('/legacy-vs-novafuse')} py-1`}>
                    Legacy vs NovaFuse
                  </Link>
                  <Link href="/competitor-matrix" className={`${isActive('/competitor-matrix')} py-1`}>
                    Competitor Matrix
                  </Link>
                  <Link href="/tam-analysis" className={`${isActive('/tam-analysis')} py-1`}>
                    TAM Analysis
                  </Link>
                  <Link href="/strategic-independence-plan" className={`${isActive('/strategic-independence-plan')} py-1`}>
                    Strategic Plan
                  </Link>
                </div>
              </div>

              <Link href="/technology-roadmap" className={`${isActive('/technology-roadmap')} py-2`}>
                Roadmap
              </Link>
              <Link href="/early-access-program" className={`${isActive('/early-access-program')} py-2`}>
                Early Access
              </Link>
            </div>

            <div className="flex flex-col space-y-3 pt-3 border-t border-gray-700">
              <Link href="/partner-login" className="accent-bg text-white px-4 py-2 rounded hover:bg-blue-700 text-center">
                Partner Login
              </Link>
              <Link href="/become-partner" className="bg-white text-blue-700 px-4 py-2 rounded hover:bg-gray-100 border border-blue-400 text-center">
                Become a Partner
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
