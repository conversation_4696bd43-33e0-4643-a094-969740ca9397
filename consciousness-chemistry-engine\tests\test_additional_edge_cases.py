"""
Additional edge case tests for the ConsciousNovaFold protein folding system.

This module contains tests for boundary conditions, error handling, and other
edge cases not covered in the main test suite.
"""

import unittest
import tempfile
import os
import numpy as np

# Import the modules to be tested
from src.conscious_novafold import ConsciousNovaFold
from src.novafold_client import NovaFoldClient

class TestAdditionalEdgeCases(unittest.TestCase):
    """Test cases for additional edge cases in ConsciousNovaFold."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.novafold = NovaFoldClient()
        self.output_dir = tempfile.mkdtemp(prefix="conscious_novafold_test_")
        self.folder = ConsciousNovaFold(
            self.novafold,
            output_dir=self.output_dir,
            enable_caching=False
        )
    
    def tearDown(self):
        """Clean up test fixtures."""
        if os.path.exists(self.output_dir):
            for root, dirs, files in os.walk(self.output_dir, topdown=False):
                for name in files:
                    os.remove(os.path.join(root, name))
                for name in dirs:
                    os.rmdir(os.path.join(root, name))
            os.rmdir(self.output_dir)
    
    def test_boundary_condition_ners(self):
        """Test sequences with NERS scores near the acceptance threshold."""
        # Test with a sequence that should be just above the NERS threshold
        sequence = "ACDEFGHIKLMNPQRSTVWY" * 2  # A simple repeated sequence
        result = self.folder.fold(sequence)
        
        # Get validation results
        validation = result['metrics']['trinity_report']['validation']
        ners_score = validation['ners']['score']
        
        print(f"\nNERS score for boundary test: {ners_score}")
        self.assertGreaterEqual(ners_score, 0.7,
                              f"NERS score ({ners_score}) should be ≥ 0.7 for valid sequences")
    
    def test_modified_amino_acids(self):
        """Test sequences containing modified amino acids."""
        # Test with a sequence containing modified amino acids (using X as placeholder)
        # In a real scenario, these would be actual modified amino acid codes
        sequence = "ACDXEFGHIKLMNPQRSTVWY"
        result = self.folder.fold(sequence)
        
        # Verify the structure was generated
        self.assertIn('structure', result)
        self.assertIsNotNone(result['structure'])
        
        # Check that the sequence was processed correctly
        self.assertEqual(len(result['structure']), len(sequence))
    
    def test_extremely_long_sequence(self):
        """Test with an extremely long sequence."""
        # Use a repeated sequence to create a long sequence
        base_sequence = "ACDEFGHIKLMNPQRSTVWY"
        long_sequence = base_sequence * 100  # 2000 amino acids
        
        result = self.folder.fold(long_sequence)
        
        # Verify the structure was generated
        self.assertIn('structure', result)
        self.assertIsNotNone(result['structure'])
        self.assertEqual(len(result['structure']), len(long_sequence))
        
        # Verify validation metrics were calculated
        validation = result['metrics']['trinity_report']['validation']
        self.assertIn('ners', validation)
        self.assertIn('nepi', validation)
        self.assertIn('nefc', validation)
    
    def test_malformed_sequence(self):
        """Test handling of malformed sequence input."""
        # Test with empty sequence
        with self.assertRaises(ValueError):
            self.folder.fold("")
        
        # Test with invalid characters
        with self.assertRaises(ValueError):
            self.folder.fold("ACDEF123GHI")
        
        # Test with whitespace
        with self.assertRaises(ValueError):
            self.folder.fold("ACD EFG HI")
    
    def test_special_characters(self):
        """Test handling of special characters in sequence."""
        # Test with various special characters that might be used in sequence files
        test_cases = [
            "ACDEF*GHI",  # Common stop codon
            "ACDEF-GHI",  # Gap character
            "ACDEF.GHI",  # Sometimes used for gaps
        ]
        
        for seq in test_cases:
            with self.subTest(sequence=seq):
                with self.assertRaises(ValueError):
                    self.folder.fold(seq)

if __name__ == '__main__':
    unittest.main()
