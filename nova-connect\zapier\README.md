# NovaConnect UAC Zapier Integration

This directory contains the Zapier integration for NovaConnect UAC. The integration enables no-code automation with 5,000+ apps on Zapier.

## Overview

The Zapier integration provides:

- **OAuth Authentication**: Secure authentication with Zapier
- **Triggers**: Events that can trigger Zaps
- **Actions**: Operations that can be performed by Zaps
- **Pre-built Zaps**: Ready-to-use Zap templates for common use cases

## Directory Structure

```
zapier/
├── README.md                 # This file
├── authentication.js         # OAuth authentication for Zapier
├── index.js                  # Entry point for Zapier integration
├── package.json              # Package configuration for Zapier CLI
├── triggers/                 # Zapier triggers
│   ├── new_connector.js      # New connector trigger
│   ├── new_workflow.js       # New workflow trigger
│   └── compliance_event.js   # Compliance event trigger
├── actions/                  # Zapier actions
│   ├── create_connector.js   # Create connector action
│   ├── execute_workflow.js   # Execute workflow action
│   └── create_evidence.js    # Create compliance evidence action
└── test/                     # Tests for Zapier integration
    ├── authentication.js     # Tests for authentication
    ├── triggers/             # Tests for triggers
    └── actions/              # Tests for actions
```

## Getting Started

### Prerequisites

- [Zapier CLI](https://github.com/zapier/zapier-platform-cli)
- Node.js 14+
- NovaConnect UAC API access

### Installation

1. Install Zapier CLI:

```bash
npm install -g zapier-platform-cli
```

2. Login to Zapier:

```bash
zapier login
```

3. Install dependencies:

```bash
cd zapier
npm install
```

### Development

1. Update the app definition in `index.js`
2. Update the authentication in `authentication.js`
3. Update the triggers in `triggers/`
4. Update the actions in `actions/`

### Testing

1. Run tests:

```bash
npm test
```

2. Validate the app:

```bash
zapier validate
```

3. Push the app to Zapier:

```bash
zapier push
```

## Triggers

### New Connector

Triggers when a new connector is created.

```javascript
// Example trigger usage
const trigger = {
  key: 'new_connector',
  noun: 'Connector',
  display: {
    label: 'New Connector',
    description: 'Triggers when a new connector is created.'
  },
  operation: {
    perform: {
      url: 'https://api.nova-connect.io/api/zapier/triggers/new-connector'
    }
  }
};
```

### New Workflow

Triggers when a new workflow is created.

```javascript
// Example trigger usage
const trigger = {
  key: 'new_workflow',
  noun: 'Workflow',
  display: {
    label: 'New Workflow',
    description: 'Triggers when a new workflow is created.'
  },
  operation: {
    perform: {
      url: 'https://api.nova-connect.io/api/zapier/triggers/new-workflow'
    }
  }
};
```

### Compliance Event

Triggers when a new compliance event occurs.

```javascript
// Example trigger usage
const trigger = {
  key: 'compliance_event',
  noun: 'Compliance Event',
  display: {
    label: 'New Compliance Event',
    description: 'Triggers when a new compliance event occurs.'
  },
  operation: {
    perform: {
      url: 'https://api.nova-connect.io/api/zapier/triggers/compliance-event'
    }
  }
};
```

## Actions

### Create Connector

Creates a new connector.

```javascript
// Example action usage
const action = {
  key: 'create_connector',
  noun: 'Connector',
  display: {
    label: 'Create Connector',
    description: 'Creates a new connector.'
  },
  operation: {
    perform: {
      url: 'https://api.nova-connect.io/api/zapier/actions/create-connector',
      method: 'POST',
      body: {
        name: '{{bundle.inputData.name}}',
        type: '{{bundle.inputData.type}}',
        config: '{{bundle.inputData.config}}'
      }
    },
    inputFields: [
      {
        key: 'name',
        label: 'Name',
        type: 'string',
        required: true
      },
      {
        key: 'type',
        label: 'Type',
        type: 'string',
        required: true,
        choices: {
          api: 'API',
          database: 'Database',
          file: 'File'
        }
      },
      {
        key: 'config',
        label: 'Configuration',
        type: 'text',
        required: true
      }
    ]
  }
};
```

### Execute Workflow

Executes a workflow.

```javascript
// Example action usage
const action = {
  key: 'execute_workflow',
  noun: 'Workflow',
  display: {
    label: 'Execute Workflow',
    description: 'Executes a workflow.'
  },
  operation: {
    perform: {
      url: 'https://api.nova-connect.io/api/zapier/actions/execute-workflow',
      method: 'POST',
      body: {
        workflowId: '{{bundle.inputData.workflowId}}',
        inputs: '{{bundle.inputData.inputs}}'
      }
    },
    inputFields: [
      {
        key: 'workflowId',
        label: 'Workflow ID',
        type: 'string',
        required: true
      },
      {
        key: 'inputs',
        label: 'Inputs',
        type: 'text',
        required: false
      }
    ]
  }
};
```

### Create Compliance Evidence

Creates a new compliance evidence record.

```javascript
// Example action usage
const action = {
  key: 'create_compliance_evidence',
  noun: 'Compliance Evidence',
  display: {
    label: 'Create Compliance Evidence',
    description: 'Creates a new compliance evidence record.'
  },
  operation: {
    perform: {
      url: 'https://api.nova-connect.io/api/zapier/actions/create-compliance-evidence',
      method: 'POST',
      body: {
        controlId: '{{bundle.inputData.controlId}}',
        evidenceType: '{{bundle.inputData.evidenceType}}',
        description: '{{bundle.inputData.description}}',
        data: '{{bundle.inputData.data}}'
      }
    },
    inputFields: [
      {
        key: 'controlId',
        label: 'Control ID',
        type: 'string',
        required: true
      },
      {
        key: 'evidenceType',
        label: 'Evidence Type',
        type: 'string',
        required: true,
        choices: {
          document: 'Document',
          screenshot: 'Screenshot',
          log: 'Log',
          test_result: 'Test Result',
          attestation: 'Attestation'
        }
      },
      {
        key: 'description',
        label: 'Description',
        type: 'text',
        required: true
      },
      {
        key: 'data',
        label: 'Data',
        type: 'text',
        required: false
      }
    ]
  }
};
```

## Authentication

The Zapier integration uses OAuth 2.0 for authentication:

```javascript
// Example authentication configuration
const authentication = {
  type: 'oauth2',
  oauth2Config: {
    authorizeUrl: 'https://api.nova-connect.io/api/zapier/oauth/authorize',
    tokenUrl: 'https://api.nova-connect.io/api/zapier/oauth/token',
    refreshUrl: 'https://api.nova-connect.io/api/zapier/oauth/refresh',
    autoRefresh: true,
    scope: 'read write'
  },
  connectionLabel: '{{bundle.authData.username}}'
};
```

## Pre-built Zaps

NovaConnect UAC provides pre-built Zap templates for common use cases:

- **Compliance Notification**: Sends a notification when a compliance event occurs
- **Connector Creation**: Creates a connector when a new record is added to a spreadsheet
- **Workflow Execution**: Executes a workflow when a form is submitted
- **Evidence Collection**: Creates compliance evidence when a document is uploaded

## API Endpoints

The Zapier integration uses the following API endpoints:

- **App Definition**: `GET /api/zapier/app-definition`
- **Triggers**: `GET /api/zapier/triggers`
- **Actions**: `GET /api/zapier/actions`
- **OAuth**: `GET /api/zapier/oauth/authorize`, `POST /api/zapier/oauth/token`
- **Trigger Endpoints**: `GET /api/zapier/triggers/new-connector`, etc.
- **Action Endpoints**: `POST /api/zapier/actions/create-connector`, etc.

## Configuration

The following environment variables are used for Zapier integration:

- `API_BASE_URL`: Base URL for the API
- `JWT_SECRET`: Secret for JWT tokens
- `ZAPIER_JWT_EXPIRES_IN`: Expiration time for JWT tokens
- `ZAPIER_CLIENT_ID`: Client ID for Zapier OAuth
- `ZAPIER_CLIENT_SECRET`: Client secret for Zapier OAuth
- `ZAPIER_REDIRECT_URI`: Redirect URI for Zapier OAuth

## Resources

- [Zapier Platform CLI Documentation](https://github.com/zapier/zapier-platform/blob/main/packages/cli/README.md)
- [Zapier Platform Schema](https://github.com/zapier/zapier-platform/blob/main/packages/schema/docs/build/schema.md)
- [Zapier OAuth Documentation](https://platform.zapier.com/docs/oauth)
- [NovaConnect UAC API Documentation](https://api.nova-connect.io/docs)
