import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import './App.css';
import './print-styles.css';
import styled from 'styled-components';

// Import Navigation
import Navigation from './components/Navigation';

// Import God Patent Diagrams
import CyberSafetyArchitecture from './diagrams/god-patent/CyberSafetyArchitecture';
import TwelvePillars from './diagrams/god-patent/TwelvePillars';
import TwelveNovas from './diagrams/god-patent/TwelveNovas';
import NineContinuances from './diagrams/god-patent/NineContinuances';
import AlignmentArchitecture from './diagrams/god-patent/AlignmentArchitecture';
import NovaStore from './diagrams/god-patent/NovaStore';
import UnifiedFieldTheory from './diagrams/god-patent/UnifiedFieldTheory';
import CrossDomainApplications from './diagrams/god-patent/CrossDomainApplications';
import NovaFuseComponentsDiagram from './diagrams/god-patent/NovaFuseComponentsDiagram';

// Import White Paper Diagrams
import AlignmentArchitectureWhitepaper from './diagrams/white-paper/AlignmentArchitectureWhitepaper';

// Import Financial Services Diagrams
import FinancialServicesArchitecture from './diagrams/financial/FinancialServicesArchitecture';
import AutomatedAuditTrail from './diagrams/financial/AutomatedAuditTrail';

// Import Healthcare Diagrams
import HealthcareArchitecture from './diagrams/healthcare/HealthcareArchitecture';

// Import Universal Components Diagrams
import NovaConnect from './diagrams/universal-components/NovaConnect';
import NovaDNA from './diagrams/universal-components/NovaDNA';
import NovaVision from './diagrams/universal-components/NovaVision';

// Import Strategic Framework Diagrams
import TrinityFramework from './diagrams/strategic-framework/TrinityFramework';
import PerformanceVisualization from './diagrams/strategic-framework/PerformanceVisualization';
import PartnerEmpowermentFlywheel from './diagrams/strategic-framework/PartnerEmpowermentFlywheel';
import GoogleOnlyApproach from './diagrams/strategic-framework/GoogleOnlyApproach';
import EnterpriseBeforeAfter from './diagrams/strategic-framework/EnterpriseBeforeAfter';
import PatentShield from './diagrams/strategic-framework/PatentShield';
import MathematicsClassification from './diagrams/strategic-framework/MathematicsClassification';
import PerformanceEquation from './diagrams/strategic-framework/PerformanceEquation';
import PatentClaims from './diagrams/strategic-framework/PatentClaims';
import ConsolidatedView from './diagrams/strategic-framework/ConsolidatedView';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 30px;
`;

const Title = styled.h1`
  font-size: 24px;
  color: #333;
`;

const DiagramContainer = styled.div`
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 30px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
  min-height: 600px; /* Ensure enough space for the diagram */
`;

const DiagramTitle = styled.h2`
  font-size: 20px;
  color: #333;
  text-align: center;
  margin-bottom: 30px;
`;

const Instructions = styled.div`
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-top: 30px;
`;

function App() {
  return (
    <Router>
      <Navigation />
      <Container>
        <Header>
          <Title>NovaFuse Strategic Framework & Patent Diagrams</Title>
        </Header>

        <Routes>
          {/* Default route */}
          <Route path="/" element={<Navigate to="/strategic-framework/trinity" />} />

          {/* God Patent Routes */}
          <Route path="/god-patent/architecture" element={
            <DiagramPage
              title="FIG. 1: Cyber-Safety Protocol Architecture"
              component={<CyberSafetyArchitecture />}
            />
          } />
          <Route path="/god-patent/pillars" element={
            <DiagramPage
              title="FIG. 2: 12 Pillars of Cyber-Safety Framework"
              component={<TwelvePillars />}
            />
          } />
          <Route path="/god-patent/novas" element={
            <DiagramPage
              title="FIG. 3: 12+1 Universal Novas"
              component={<TwelveNovas />}
            />
          } />
          <Route path="/god-patent/continuances" element={
            <DiagramPage
              title="FIG. 4: 9 Industry Focus"
              component={<NineContinuances />}
            />
          } />
          <Route path="/god-patent/alignment" element={
            <DiagramPage
              title="FIG. 5: 3-6-9-12-13 Alignment Architecture"
              component={<AlignmentArchitecture />}
            />
          } />
          <Route path="/god-patent/novastore" element={
            <DiagramPage
              title="FIG. 6: NovaStore Partner Empowerment Model"
              component={<NovaStore />}
            />
          } />
          <Route path="/god-patent/unified-field-theory" element={
            <DiagramPage
              title="FIG. 7: Unified Field Theory Architecture"
              component={<UnifiedFieldTheory />}
            />
          } />
          <Route path="/god-patent/cross-domain-applications" element={
            <DiagramPage
              title="FIG. 8: Cross-Domain Applications"
              component={<CrossDomainApplications />}
            />
          } />
          <Route path="/god-patent/novafuse-components" element={
            <DiagramPage
              title="FIG. 9: NovaFuse 13 Universal Components - Comphyology (Ψᶜ) Framework"
              component={<NovaFuseComponentsDiagram />}
            />
          } />

          {/* White Paper Routes */}
          <Route path="/white-paper/alignment-architecture" element={
            <DiagramPage
              title="3–6–9–12–13 Alignment Architecture"
              component={<AlignmentArchitectureWhitepaper />}
            />
          } />

          {/* Financial Services Routes */}
          <Route path="/financial/architecture" element={
            <DiagramPage
              title="FIG. 1: Financial Services System Architecture"
              component={<FinancialServicesArchitecture />}
            />
          } />
          <Route path="/financial/audit-trail" element={
            <DiagramPage
              title="FIG. 2: Automated Audit Trail Generation"
              component={<AutomatedAuditTrail />}
            />
          } />

          {/* Healthcare Routes */}
          <Route path="/healthcare/architecture" element={
            <DiagramPage
              title="FIG. 1: Healthcare System Architecture"
              component={<HealthcareArchitecture />}
            />
          } />

          {/* Universal Components Routes */}
          <Route path="/universal-components/nova-connect" element={
            <DiagramPage
              title="NovaConnect - Universal API Connector"
              component={<NovaConnect />}
            />
          } />
          <Route path="/universal-components/nova-dna" element={
            <DiagramPage
              title="NovaDNA - Universal Identity Verification System"
              component={<NovaDNA />}
            />
          } />
          <Route path="/universal-components/nova-vision" element={
            <DiagramPage
              title="NovaVision - Universal UI Framework"
              component={<NovaVision />}
            />
          } />

          {/* Strategic Framework Routes */}
          <Route path="/strategic-framework/trinity" element={
            <DiagramPage
              title="FIG. 1: The Cyber-Safety Dominance Framework - Strategic Trinity"
              component={<TrinityFramework />}
            />
          } />
          <Route path="/strategic-framework/performance" element={
            <DiagramPage
              title="FIG. 2: 3,142x Performance Visualization"
              component={<PerformanceVisualization />}
            />
          } />
          <Route path="/strategic-framework/partner-empowerment" element={
            <DiagramPage
              title="FIG. 3: Partner Empowerment Flywheel"
              component={<PartnerEmpowermentFlywheel />}
            />
          } />
          <Route path="/strategic-framework/google-approach" element={
            <DiagramPage
              title="FIG. 4: Google-Only Approach with Wiz Enhancement"
              component={<GoogleOnlyApproach />}
            />
          } />
          <Route path="/strategic-framework/enterprise-case" element={
            <DiagramPage
              title="FIG. 5: Enterprise Case Study - Before & After"
              component={<EnterpriseBeforeAfter />}
            />
          } />
          <Route path="/strategic-framework/patent-shield" element={
            <DiagramPage
              title="FIG. 6: Patent Shield & Strategic Moat"
              component={<PatentShield />}
            />
          } />
          <Route path="/strategic-framework/mathematics" element={
            <DiagramPage
              title="FIG. 7: The Mathematics Behind NovaFuse - A Formal Classification"
              component={<MathematicsClassification />}
            />
          } />
          <Route path="/strategic-framework/performance-equation" element={
            <DiagramPage
              title="FIG. 8: NovaFuse Performance Equation (The 3,142x Formula)"
              component={<PerformanceEquation />}
            />
          } />
          <Route path="/strategic-framework/patent-claims" element={
            <DiagramPage
              title="Systems and Methods for AI-Driven Compliance Enforcement Using Tensor-NIST Fusion"
              component={<PatentClaims />}
            />
          } />
          <Route path="/strategic-framework/consolidated" element={<ConsolidatedView />} />

          {/* Add routes for other diagrams as they are developed */}
        </Routes>

        <Instructions>
          <h3>Strategic Framework & Patent Diagrams</h3>
          <p>
            This application contains two main sections:
          </p>
          <ol>
            <li><strong>Strategic Framework</strong> - Diagrams for "The Cyber-Safety Dominance Framework" presentation to Google and NIST</li>
            <li><strong>Patent Diagrams</strong> - Diagrams for NovaFuse patent applications</li>
          </ol>

          <h3>Strategic Framework Features:</h3>
          <ul>
            <li>View individual diagrams for the strategic framework</li>
            <li>See the "Complete Presentation" view with all diagrams</li>
            <li>Export the presentation as a PDF</li>
          </ul>

          <h3>Instructions for Taking Screenshots:</h3>
          <ol>
            <li>Navigate to the diagram you want to capture using the navigation above</li>
            <li>Use your operating system's screenshot tool to capture the diagram</li>
            <li>For Windows: Use Windows+Shift+S to open the snipping tool</li>
            <li>For Mac: Use Command+Shift+4 to capture a selected area</li>
            <li>Save the screenshot with the figure number and name</li>
          </ol>
          <p>These diagrams are designed to be patent-compliant with clear component relationships and data flows.</p>
        </Instructions>
      </Container>
    </Router>
  );
}

// DiagramPage component to standardize diagram display
function DiagramPage({ title, component }) {
  return (
    <DiagramContainer>
      <DiagramTitle>{title}</DiagramTitle>
      {component}
    </DiagramContainer>
  );
}

export default App;
