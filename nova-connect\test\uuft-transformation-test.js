/**
 * UUFT-Enhanced Transformation Engine Test
 * 
 * This test demonstrates how the UUFT equation enhances NovaConnect's data transformation capabilities
 * by comparing the standard transformation engine with the UUFT-enhanced version.
 */

const UUFTEnhancedTransformationEngine = require('../src/engines/uuft-enhanced-transformation-engine');

// Sample compliance data from a security finding
const securityFinding = {
  id: 'finding-123456',
  source: 'gcp-security-command-center',
  severity: 'HIGH',
  category: 'VULNERABILITY',
  createTime: '2025-05-08T12:34:56.789Z',
  updateTime: '2025-05-08T13:45:12.345Z',
  resourceName: 'projects/my-project/zones/us-central1-a/instances/my-instance',
  resourceType: 'google.compute.Instance',
  state: 'ACTIVE',
  externalUri: 'https://console.cloud.google.com/security/command-center/findings?project=my-project',
  sourceProperties: {
    scanConfigId: 'scan-config-12345',
    scanRunId: 'scan-run-67890',
    vulnerabilityType: 'XSS',
    vulnerabilityDetails: {
      cvssScore: 8.5,
      cvssVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H',
      cve: 'CVE-2025-12345',
      description: 'Cross-site scripting vulnerability in web application',
      references: [
        'https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2025-12345',
        'https://nvd.nist.gov/vuln/detail/CVE-2025-12345'
      ],
      fixAvailable: true,
      exploitAvailable: true
    }
  },
  securityMarks: {
    marks: {
      criticality: 'p0',
      compliance: 'pci-dss,hipaa,soc2',
      dataClassification: 'confidential'
    }
  },
  nextSteps: [
    'Update web application to latest version',
    'Apply security patch',
    'Implement input validation'
  ],
  complianceState: 'NON_COMPLIANT'
};

// Transformation rules to normalize the security finding
const transformationRules = [
  {
    source: 'id',
    target: 'finding_id',
    transform: 'uppercase',
    sourceReliability: 1.0,
    validationScore: 0.9
  },
  {
    source: 'source',
    target: 'source_system',
    transform: 'lowercase',
    sourceReliability: 0.9,
    validationScore: 0.8
  },
  {
    source: 'severity',
    target: 'risk_level',
    transform: ['uppercase', 'trim'],
    sourceReliability: 0.95,
    validationScore: 0.9
  },
  {
    source: 'category',
    target: 'finding_type',
    transform: 'lowercase',
    sourceReliability: 0.85,
    validationScore: 0.7
  },
  {
    source: 'createTime',
    target: 'created_at',
    transform: 'isoToUnix',
    sourceReliability: 1.0,
    validationScore: 1.0
  },
  {
    source: 'updateTime',
    target: 'updated_at',
    transform: 'isoToUnix',
    sourceReliability: 1.0,
    validationScore: 1.0
  },
  {
    source: 'resourceName',
    target: 'asset.full_path',
    sourceReliability: 0.9,
    validationScore: 0.8
  },
  {
    source: 'resourceType',
    target: 'asset.type',
    transform: 'split',
    transformParams: '.',
    sourceReliability: 0.9,
    validationScore: 0.8
  },
  {
    source: 'state',
    target: 'status',
    transform: 'lowercase',
    sourceReliability: 0.8,
    validationScore: 0.7
  },
  {
    source: 'externalUri',
    target: 'links.console',
    sourceReliability: 0.7,
    validationScore: 0.6
  },
  {
    source: 'sourceProperties.vulnerabilityType',
    target: 'details.vulnerability_type',
    transform: 'lowercase',
    sourceReliability: 0.9,
    validationScore: 0.8
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.cvssScore',
    target: 'details.cvss_score',
    transform: 'toNumber',
    sourceReliability: 0.95,
    validationScore: 0.9
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.cvssVector',
    target: 'details.cvss_vector',
    sourceReliability: 0.9,
    validationScore: 0.8
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.cve',
    target: 'details.cve_id',
    sourceReliability: 0.95,
    validationScore: 0.9
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.description',
    target: 'details.description',
    sourceReliability: 0.8,
    validationScore: 0.7
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.references',
    target: 'details.references',
    sourceReliability: 0.7,
    validationScore: 0.6
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.fixAvailable',
    target: 'details.fix_available',
    sourceReliability: 0.9,
    validationScore: 0.8
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.exploitAvailable',
    target: 'details.exploit_available',
    sourceReliability: 0.9,
    validationScore: 0.8
  },
  {
    source: 'securityMarks.marks.criticality',
    target: 'metadata.criticality',
    sourceReliability: 0.8,
    validationScore: 0.7
  },
  {
    source: 'securityMarks.marks.compliance',
    target: 'metadata.compliance_frameworks',
    transform: 'split',
    transformParams: ',',
    sourceReliability: 0.9,
    validationScore: 0.8
  },
  {
    source: 'securityMarks.marks.dataClassification',
    target: 'metadata.data_classification',
    sourceReliability: 0.9,
    validationScore: 0.8
  },
  {
    source: 'nextSteps',
    target: 'remediation.steps',
    sourceReliability: 0.7,
    validationScore: 0.6
  },
  {
    source: 'complianceState',
    target: 'compliance_status',
    transform: 'lowercase',
    sourceReliability: 0.9,
    validationScore: 0.8
  }
];

// Run the test
function runTest() {
  console.log('UUFT-Enhanced Transformation Engine Test');
  console.log('=======================================');
  
  // Create transformation engines
  const standardEngine = new UUFTEnhancedTransformationEngine({ enableUUFT: false });
  const uuftEngine = new UUFTEnhancedTransformationEngine({ enableUUFT: true });
  
  console.log('\nRunning standard transformation...');
  const startTimeStandard = performance.now();
  const standardResult = standardEngine.transform(securityFinding, transformationRules);
  const endTimeStandard = performance.now();
  const durationStandard = endTimeStandard - startTimeStandard;
  
  console.log('\nRunning UUFT-enhanced transformation...');
  const startTimeUUFT = performance.now();
  const uuftResult = uuftEngine.transform(securityFinding, transformationRules);
  const endTimeUUFT = performance.now();
  const durationUUFT = endTimeUUFT - startTimeUUFT;
  
  // Calculate performance improvement
  const performanceImprovement = durationStandard / durationUUFT;
  
  // Print results
  console.log('\nPerformance Results:');
  console.log(`Standard Transformation: ${durationStandard.toFixed(3)} ms`);
  console.log(`UUFT-Enhanced Transformation: ${durationUUFT.toFixed(3)} ms`);
  console.log(`Performance Improvement: ${performanceImprovement.toFixed(2)}x`);
  
  // Print UUFT metrics
  console.log('\nUUFT Metrics:');
  const metrics = uuftEngine.getMetrics();
  console.log(`Total Transformations: ${metrics.transformations}`);
  console.log(`UUFT Optimizations: ${metrics.uuftOptimizations}`);
  console.log(`Average Duration: ${metrics.averageDuration.toFixed(3)} ms`);
  
  // Analyze the 18/82 principle application
  const importantPaths = uuftEngine.apply1882Principle(securityFinding);
  console.log('\n18/82 Principle Application:');
  console.log(`Total Paths: ${uuftEngine._extractPaths(securityFinding).length}`);
  console.log(`Important Paths (18%): ${importantPaths.length}`);
  console.log('Important Paths:');
  importantPaths.forEach(path => console.log(`- ${path}`));
  
  // Compare transformation results
  console.log('\nTransformation Results Comparison:');
  console.log('Standard Result:');
  console.log(JSON.stringify(standardResult, null, 2));
  console.log('\nUUFT-Enhanced Result:');
  console.log(JSON.stringify(uuftResult, null, 2));
  
  return {
    standardDuration: durationStandard,
    uuftDuration: durationUUFT,
    performanceImprovement,
    standardResult,
    uuftResult,
    metrics,
    importantPaths
  };
}

// Run the test
const results = runTest();

// Export results for potential further analysis
module.exports = {
  results,
  securityFinding,
  transformationRules
};
