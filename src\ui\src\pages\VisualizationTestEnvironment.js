import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Card,
  CardContent,
  CardActions,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Settings as SettingsIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';

// Import visualizations
import TriDomainTensorVisualization from '../components/visualizations/TriDomainTensorVisualization';
import HarmonyIndexVisualization from '../components/visualizations/HarmonyIndexVisualization';
// Import other visualizations as needed

// Import sample data
import { allVisualizationData } from '../data/sampleTestData';

// Import user testing components
import UserTestingWizard from '../components/testing/UserTestingWizard';

/**
 * Visualization Test Environment
 *
 * This page provides an environment for testing the Cyber-Safety visualizations.
 */
function VisualizationTestEnvironment() {
  // State for selected visualization
  const [selectedVisualization, setSelectedVisualization] = useState('triDomainTensor');

  // State for test mode
  const [testMode, setTestMode] = useState(false);

  // State for visualization options
  const [visualizationOptions, setVisualizationOptions] = useState({
    triDomainTensor: {
      renderMode: 'medium',
      showAxes: true,
      showGrid: true,
      rotationSpeed: 1,
      showLabels: true,
      highlightFusionPoints: true
    },
    harmonyIndex: {
      showDomainScores: true,
      showCrossDomainHarmony: true,
      showResonanceFactors: true,
      timeRange: 'all',
      animateTransitions: true
    }
  });

  // Handle visualization selection
  const handleVisualizationChange = (event) => {
    setSelectedVisualization(event.target.value);
  };

  // Handle option change
  const handleOptionChange = (option, value) => {
    setVisualizationOptions(prevOptions => ({
      ...prevOptions,
      [selectedVisualization]: {
        ...prevOptions[selectedVisualization],
        [option]: value
      }
    }));
  };

  // Handle toggle option
  const handleToggleOption = (option) => (event) => {
    handleOptionChange(option, event.target.checked);
  };

  // Handle test mode toggle
  const handleTestModeToggle = () => {
    setTestMode(prevMode => !prevMode);
  };

  // Handle test completion
  const handleTestComplete = () => {
    setTestMode(false);
  };

  // Render visualization
  const renderVisualization = () => {
    const data = allVisualizationData[selectedVisualization];
    const options = visualizationOptions[selectedVisualization] || {};

    switch (selectedVisualization) {
      case 'triDomainTensor':
        return (
          <TriDomainTensorVisualization
            domainData={data}
            options={options}
            width="100%"
            height={500}
          />
        );
      case 'harmonyIndex':
        return (
          <HarmonyIndexVisualization
            data={data}
            layoutOptions={options}
            width="100%"
            height={500}
            onInteraction={(type, data) => {
              console.log('Harmony Index Interaction:', type, data);
            }}
          />
        );
      // Add cases for other visualizations
      default:
        return (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1">
              Visualization type not supported: {selectedVisualization}
            </Typography>
          </Box>
        );
    }
  };

  // Render visualization options
  const renderVisualizationOptions = () => {
    const options = visualizationOptions[selectedVisualization] || {};

    switch (selectedVisualization) {
      case 'triDomainTensor':
        return (
          <Box>
            <FormControlLabel
              control={
                <Switch
                  checked={options.showAxes || false}
                  onChange={handleToggleOption('showAxes')}
                />
              }
              label="Show Axes"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.showGrid || false}
                  onChange={handleToggleOption('showGrid')}
                />
              }
              label="Show Grid"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.showLabels || false}
                  onChange={handleToggleOption('showLabels')}
                />
              }
              label="Show Labels"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.highlightFusionPoints || false}
                  onChange={handleToggleOption('highlightFusionPoints')}
                />
              }
              label="Highlight Fusion Points"
            />
          </Box>
        );
      case 'harmonyIndex':
        return (
          <Box>
            <FormControlLabel
              control={
                <Switch
                  checked={options.showDomainScores || false}
                  onChange={handleToggleOption('showDomainScores')}
                />
              }
              label="Show Domain Scores"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.showCrossDomainHarmony || false}
                  onChange={handleToggleOption('showCrossDomainHarmony')}
                />
              }
              label="Show Cross-Domain Harmony"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.showResonanceFactors || false}
                  onChange={handleToggleOption('showResonanceFactors')}
                />
              }
              label="Show Resonance Factors"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.animateTransitions || false}
                  onChange={handleToggleOption('animateTransitions')}
                />
              }
              label="Animate Transitions"
            />
          </Box>
        );
      // Add cases for other visualizations
      default:
        return (
          <Typography variant="body2">
            No options available for this visualization type.
          </Typography>
        );
    }
  };

  // If in test mode, render the user testing wizard
  if (testMode) {
    return (
      <UserTestingWizard
        visualizationType={selectedVisualization}
        testType="usability"
        onComplete={handleTestComplete}
      />
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h4" gutterBottom>
        Cyber-Safety Visualization Test Environment
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Visualization Selection
            </Typography>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Visualization Type</InputLabel>
              <Select
                value={selectedVisualization}
                onChange={handleVisualizationChange}
                label="Visualization Type"
              >
                <MenuItem value="triDomainTensor">Tri-Domain Tensor</MenuItem>
                <MenuItem value="harmonyIndex">Harmony Index</MenuItem>
                <MenuItem value="riskControlFusion">Risk-Control Fusion</MenuItem>
                <MenuItem value="resonanceSpectrogram">Resonance Spectrogram</MenuItem>
                <MenuItem value="unifiedComplianceSecurity">Unified Compliance-Security</MenuItem>
              </Select>
            </FormControl>

            <Divider sx={{ my: 2 }} />

            <Typography variant="h6" gutterBottom>
              Visualization Options
            </Typography>

            {renderVisualizationOptions()}

            <Divider sx={{ my: 2 }} />

            <Typography variant="h6" gutterBottom>
              Testing
            </Typography>

            <Button
              variant="contained"
              color="primary"
              startIcon={<PlayIcon />}
              onClick={handleTestModeToggle}
              fullWidth
              sx={{ mb: 2 }}
            >
              Start User Testing
            </Button>

            <Button
              variant="outlined"
              startIcon={<AssessmentIcon />}
              fullWidth
            >
              View Test Results
            </Button>
          </Paper>
        </Grid>

        <Grid item xs={12} md={9}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              {selectedVisualization.replace(/([A-Z])/g, ' $1').trim()} Visualization
            </Typography>

            <Box sx={{ mt: 2 }}>
              {renderVisualization()}
            </Box>
          </Paper>

          <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Visualization Description
                  </Typography>

                  <Typography variant="body2">
                    {selectedVisualization === 'triDomainTensor' && (
                      'The Tri-Domain Tensor visualization shows the relationships between GRC, IT, and Cybersecurity domains. It highlights the connections and fusion points between these domains, providing a 3D representation of their interactions.'
                    )}
                    {selectedVisualization === 'harmonyIndex' && (
                      'The Harmony Index visualization displays the alignment between domains over time. It shows how well the domains are working together and identifies areas where alignment can be improved.'
                    )}
                    {selectedVisualization === 'riskControlFusion' && (
                      'The Risk-Control Fusion visualization maps risks against controls across domains. It helps identify gaps in control coverage and areas where risks are not adequately addressed.'
                    )}
                    {selectedVisualization === 'resonanceSpectrogram' && (
                      'The Resonance Spectrogram visualization shows patterns of resonance between domains over time. It helps predict potential dissonance and identify critical points where intervention may be needed.'
                    )}
                    {selectedVisualization === 'unifiedComplianceSecurity' && (
                      'The Unified Compliance-Security visualization maps compliance requirements to security controls and their implementations. It helps identify gaps in compliance coverage and areas for improvement.'
                    )}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Testing Instructions
                  </Typography>

                  <Typography variant="body2" paragraph>
                    To test this visualization:
                  </Typography>

                  <Typography variant="body2" component="div">
                    <ol>
                      <li>Click "Start User Testing" to begin the testing process.</li>
                      <li>Follow the instructions in the testing wizard.</li>
                      <li>Complete the tasks and provide feedback.</li>
                      <li>Submit your results at the end of the test.</li>
                    </ol>
                  </Typography>
                </CardContent>

                <CardActions>
                  <Button
                    size="small"
                    startIcon={<SettingsIcon />}
                  >
                    Advanced Settings
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
}

export default VisualizationTestEnvironment;
