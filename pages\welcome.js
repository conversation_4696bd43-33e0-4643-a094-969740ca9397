import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';

export default function Welcome() {
  const [user, setUser] = useState(null);
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in
    const storedUser = localStorage.getItem('user') || sessionStorage.getItem('user');
    
    if (!storedUser) {
      // Redirect to sign-in if not logged in
      router.push('/sign-in');
      return;
    }
    
    try {
      setUser(JSON.parse(storedUser));
    } catch (error) {
      console.error('Error parsing user data:', error);
      router.push('/sign-in');
    }
  }, [router]);

  if (!user) {
    return <div className="min-h-screen flex items-center justify-center bg-primary">
      <div className="animate-pulse text-white text-xl">Loading...</div>
    </div>;
  }

  return (
    <>
      <Head>
        <title>Welcome to NovaFuse - Get Started</title>
        <meta name="description" content="Welcome to NovaFuse! Get started with our API Superstore and explore our products and solutions." />
        <meta name="keywords" content="NovaFuse, welcome, get started, onboarding, API Superstore" />
        <link rel="canonical" href="https://novafuse.io/welcome" />
      </Head>

      <div className="min-h-screen bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl">
              Welcome to NovaFuse, {user.firstName}!
            </h1>
            <p className="mt-5 max-w-xl mx-auto text-xl text-gray-300">
              Your journey to simplified compliance and seamless API integration starts here.
            </p>
          </div>

          <div className="bg-secondary rounded-lg shadow-xl overflow-hidden">
            <div className="px-6 py-8 sm:p-10">
              <div className="grid grid-cols-1 gap-y-12 sm:grid-cols-2 sm:gap-x-8">
                <div>
                  <h2 className="text-2xl font-bold text-white">Getting Started</h2>
                  <div className="mt-6 space-y-6">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <div className="flex items-center justify-center h-8 w-8 rounded-full bg-gradient-to-r from-blue-700 to-purple-700 text-white">
                          1
                        </div>
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-white">Complete your profile</h3>
                        <p className="mt-2 text-gray-400">
                          Add your company details and preferences to personalize your experience.
                        </p>
                        <div className="mt-3">
                          <Link href="/profile" className="text-blue-400 hover:text-blue-300 font-medium">
                            Go to profile →
                          </Link>
                        </div>
                      </div>
                    </div>

                    <div className="flex">
                      <div className="flex-shrink-0">
                        <div className="flex items-center justify-center h-8 w-8 rounded-full bg-gradient-to-r from-blue-700 to-purple-700 text-white">
                          2
                        </div>
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-white">Explore our products</h3>
                        <p className="mt-2 text-gray-400">
                          Learn about NovaConnect UAC, NovaGRC Suite, and our Partner Empowerment model.
                        </p>
                        <div className="mt-3">
                          <Link href="/novaconnect-uac" className="text-blue-400 hover:text-blue-300 font-medium">
                            Explore products →
                          </Link>
                        </div>
                      </div>
                    </div>

                    <div className="flex">
                      <div className="flex-shrink-0">
                        <div className="flex items-center justify-center h-8 w-8 rounded-full bg-gradient-to-r from-blue-700 to-purple-700 text-white">
                          3
                        </div>
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-white">Try the UAC demo</h3>
                        <p className="mt-2 text-gray-400">
                          Experience the power of our Universal API Connector with our interactive demo.
                        </p>
                        <div className="mt-3">
                          <Link href="/uac-demo" className="text-blue-400 hover:text-blue-300 font-medium">
                            Launch demo →
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h2 className="text-2xl font-bold text-white">Resources</h2>
                  <div className="mt-6 space-y-6">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-8 w-8 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-white">Documentation</h3>
                        <p className="mt-2 text-gray-400">
                          Comprehensive guides and API references to help you get the most out of NovaFuse.
                        </p>
                        <div className="mt-3">
                          <Link href="/api-docs" className="text-blue-400 hover:text-blue-300 font-medium">
                            View documentation →
                          </Link>
                        </div>
                      </div>
                    </div>

                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-8 w-8 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                        </svg>
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-white">White Papers</h3>
                        <p className="mt-2 text-gray-400">
                          In-depth research and insights on compliance, API integration, and partner empowerment.
                        </p>
                        <div className="mt-3">
                          <Link href="/resources/white-papers" className="text-blue-400 hover:text-blue-300 font-medium">
                            Download white papers →
                          </Link>
                        </div>
                      </div>
                    </div>

                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-8 w-8 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-white">Webinars & Events</h3>
                        <p className="mt-2 text-gray-400">
                          Join our upcoming webinars and events to learn more about NovaFuse and connect with our team.
                        </p>
                        <div className="mt-3">
                          <Link href="/events" className="text-blue-400 hover:text-blue-300 font-medium">
                            View schedule →
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-blue-900 px-6 py-8 sm:px-10 sm:py-10">
              <div className="flex flex-col sm:flex-row items-center justify-between">
                <div className="text-center sm:text-left mb-6 sm:mb-0">
                  <h3 className="text-xl font-bold text-white">Ready to become a partner?</h3>
                  <p className="mt-2 text-gray-300">
                    Join our partner ecosystem and unlock new revenue opportunities.
                  </p>
                </div>
                <Link
                  href="/partner-onboarding"
                  className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400"
                >
                  Become a Partner
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
