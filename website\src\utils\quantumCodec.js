/**
 * Quantum Message Format:
 * 
 * Full State Update:
 * {
 *   type: 'full',
 *   timestamp: number,
 *   sequence: number,
 *   state: {
 *     qubits: Array<{ id: string, state: Array<number> }>,
 *     entanglement: Array<{ from: string, to: string, strength: number }>
 *   }
 * }
 * 
 * Delta Update:
 * {
 *   type: 'delta',
 *   timestamp: number,
 *   sequence: number,
 *   changes: {
 *     qubits: Array<{ id: string, state?: Array<number> }>,
 *     entanglement: Array<{ from: string, to: string, strength?: number }>
 *   }
 * }
 */

/**
 * Encode a quantum state update for efficient transmission
 * @param {Object} state - The quantum state to encode
 * @param {Object} [options] - Encoding options
 * @param {boolean} [options.useDelta=true] - Whether to use delta encoding
 * @param {Object} [options.previousState] - Previous state for delta encoding
 * @returns {Object} Encoded message
 */
export function encodeQuantumMessage(state, options = {}) {
  const {
    useDelta = true,
    previousState = null,
    sequence = 0,
    timestamp = Date.now()
  } = options;

  // If not using delta or no previous state, send full state
  if (!useDelta || !previousState) {
    return {
      type: 'full',
      timestamp,
      sequence,
      state: {
        qubits: state.qubits.map(qubit => ({
          id: qubit.id,
          state: qubit.state
        })),
        entanglement: state.entanglement.map(ent => ({
          from: ent.from,
          to: ent.to,
          strength: ent.strength
        }))
      }
    };
  }

  // Delta encoding - only send changed qubits and entanglement
  const delta = {
    type: 'delta',
    timestamp,
    sequence,
    changes: {
      qubits: [],
      entanglement: []
    }
  };

  // Find changed qubits
  const previousQubits = new Map(previousState.qubits.map(q => [q.id, q]));
  for (const qubit of state.qubits) {
    const prevQubit = previousQubits.get(qubit.id);
    if (!prevQubit || !arraysEqual(qubit.state, prevQubit.state)) {
      delta.changes.qubits.push({
        id: qubit.id,
        state: qubit.state
      });
    }
  }

  // Find changed entanglement
  const previousEntanglements = new Set(
    previousState.entanglement.map(e => `${e.from}-${e.to}`)
  );
  
  for (const ent of state.entanglement) {
    const prevEnt = previousState.entanglement.find(
      e => e.from === ent.from && e.to === ent.to
    );
    
    if (!prevEnt || prevEnt.strength !== ent.strength) {
      delta.changes.entanglement.push({
        from: ent.from,
        to: ent.to,
        strength: ent.strength
      });
    }
  }

  // If delta is larger than full state, send full state instead
  const deltaSize = JSON.stringify(delta).length;
  const fullSize = JSON.stringify({
    type: 'full',
    timestamp,
    sequence,
    state: {
      qubits: state.qubits,
      entanglement: state.entanglement
    }
  }).length;

  if (deltaSize >= fullSize * 0.8) {
    return encodeQuantumMessage(state, { ...options, useDelta: false });
  }

  return delta;
}

/**
 * Decode a quantum message and apply it to the current state
 * @param {Object} message - The encoded message
 * @param {Object} [currentState] - Current state to apply delta to
 * @returns {Object} New state
 */
export function decodeQuantumMessage(message, currentState = null) {
  if (!message || typeof message !== 'object') {
    throw new Error('Invalid message format');
  }

  // Handle full state update
  if (message.type === 'full') {
    return {
      qubits: message.state.qubits.map(q => ({
        id: q.id,
        state: [...q.state]
      })),
      entanglement: message.state.entanglement.map(e => ({
        from: e.from,
        to: e.to,
        strength: e.strength
      })),
      timestamp: message.timestamp,
      sequence: message.sequence
    };
  }

  // Handle delta update
  if (message.type === 'delta') {
    if (!currentState) {
      throw new Error('Cannot apply delta without current state');
    }

    const newState = {
      qubits: [...currentState.qubits],
      entanglement: [...currentState.entanglement],
      timestamp: message.timestamp,
      sequence: message.sequence
    };

    // Apply qubit updates
    const qubitMap = new Map(newState.qubits.map(q => [q.id, q]));
    for (const update of message.changes.qubits) {
      const qubit = qubitMap.get(update.id);
      if (qubit) {
        qubit.state = [...update.state];
      } else {
        newState.qubits.push({
          id: update.id,
          state: [...update.state]
        });
      }
    }

    // Apply entanglement updates
    for (const update of message.changes.entanglement) {
      const index = newState.entanglement.findIndex(
        e => e.from === update.from && e.to === update.to
      );

      if (index >= 0) {
        if ('strength' in update) {
          newState.entanglement[index].strength = update.strength;
        } else {
          // Remove if no strength provided (indicates removal)
          newState.entanglement.splice(index, 1);
        }
      } else if ('strength' in update) {
        // Add new entanglement
        newState.entanglement.push({
          from: update.from,
          to: update.to,
          strength: update.strength
        });
      }
    }

    return newState;
  }

  throw new Error(`Unknown message type: ${message.type}`);
}

/**
 * Compress a quantum message using a binary format
 * @param {Object} message - The message to compress
 * @returns {ArrayBuffer} Compressed binary data
 */
export function compressQuantumMessage(message) {
  // In a real implementation, this would convert the message to a binary format
  // For now, we'll just use JSON as a placeholder
  const json = JSON.stringify(message);
  const encoder = new TextEncoder();
  return encoder.encode(json).buffer;
}

/**
 * Decompress a binary quantum message
 * @param {ArrayBuffer} buffer - Compressed binary data
 * @returns {Object} Decompressed message
 */
export function decompressQuantumMessage(buffer) {
  // In a real implementation, this would parse the binary format
  // For now, we'll just parse the JSON
  const decoder = new TextDecoder();
  const json = decoder.decode(buffer);
  return JSON.parse(json);
}

/**
 * Helper function to compare two arrays for equality
 * @private
 */
function arraysEqual(a, b) {
  if (a === b) return true;
  if (a == null || b == null) return false;
  if (a.length !== b.length) return false;

  for (let i = 0; i < a.length; ++i) {
    if (a[i] !== b[i]) return false;
  }
  return true;
}

/**
 * Create a quantum state diff between two states
 * @param {Object} newState - The new state
 * @param {Object} oldState - The old state
 * @returns {Object} Object containing only the differences
 */
export function createStateDiff(newState, oldState) {
  if (!oldState) {
    return newState;
  }

  const diff = {
    qubits: [],
    entanglement: []
  };

  // Find changed qubits
  const oldQubits = new Map(oldState.qubits.map(q => [q.id, q]));
  for (const qubit of newState.qubits) {
    const oldQubit = oldQubits.get(qubit.id);
    if (!oldQubit || !arraysEqual(qubit.state, oldQubit.state)) {
      diff.qubits.push(qubit);
    }
  }

  // Find changed entanglement
  const oldEntanglements = new Map(
    oldState.entanglement.map(e => [`${e.from}-${e.to}`, e])
  );
  
  for (const ent of newState.entanglement) {
    const key = `${ent.from}-${ent.to}`;
    const oldEnt = oldEntanglements.get(key);
    
    if (!oldEnt || oldEnt.strength !== ent.strength) {
      diff.entanglement.push(ent);
    }
  }

  return diff;
}

/**
 * Apply a diff to a state
 * @param {Object} state - The base state
 * @param {Object} diff - The diff to apply
 * @returns {Object} New state with diff applied
 */
export function applyStateDiff(state, diff) {
  const newState = {
    qubits: [...state.qubits],
    entanglement: [...state.entanglement]
  };

  // Apply qubit updates
  const qubitMap = new Map(newState.qubits.map(q => [q.id, q]));
  for (const update of diff.qubits) {
    const qubit = qubitMap.get(update.id);
    if (qubit) {
      qubit.state = [...update.state];
    } else {
      newState.qubits.push({
        id: update.id,
        state: [...update.state]
      });
    }
  }

  // Apply entanglement updates
  for (const update of diff.entanglement) {
    const index = newState.entanglement.findIndex(
      e => e.from === update.from && e.to === update.to
    );

    if (index >= 0) {
      newState.entanglement[index].strength = update.strength;
    } else {
      newState.entanglement.push({
        from: update.from,
        to: update.to,
        strength: update.strength
      });
    }
  }

  return newState;
}
