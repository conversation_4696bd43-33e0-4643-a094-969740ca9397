"""
Validator Manager for the Universal Compliance Evidence Collection System.

This module provides functionality for managing evidence validators, including
validation chaining, custom validation scripts, and validation result scoring.
"""

import os
import json
import importlib
import logging
import datetime
import re
import yaml
import threading
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Union, Tuple, Set

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ValidationMode(Enum):
    """Validation execution modes."""
    SEQUENTIAL = "sequential"  # Run validators in sequence, stop on first failure
    PARALLEL = "parallel"      # Run all validators in parallel
    ALL = "all"                # Run all validators sequentially, even if some fail


class ValidationLevel(Enum):
    """Validation confidence/severity levels."""
    CRITICAL = 1   # Critical validation - must pass
    HIGH = 2       # High importance validation
    MEDIUM = 3     # Medium importance validation
    LOW = 4        # Low importance validation
    INFO = 5       # Informational validation


class ValidationResult:
    """
    Represents the result of a validation operation.

    This class provides a standardized structure for validation results,
    including support for confidence levels, scores, and detailed error information.
    """

    def __init__(self,
                validator_id: str,
                is_valid: bool,
                confidence_level: ValidationLevel = ValidationLevel.MEDIUM,
                score: float = None,
                details: Dict[str, Any] = None,
                errors: List[str] = None,
                warnings: List[str] = None,
                execution_time: float = None):
        """
        Initialize a validation result.

        Args:
            validator_id: ID of the validator that produced this result
            is_valid: Whether the validation passed
            confidence_level: Confidence level of the validation
            score: Numeric score for the validation (0-100)
            details: Additional details about the validation
            errors: List of error messages if validation failed
            warnings: List of warning messages
            execution_time: Time taken to execute the validation in seconds
        """
        self.validator_id = validator_id
        self.is_valid = is_valid
        self.confidence_level = confidence_level
        self.score = score if score is not None else (100 if is_valid else 0)
        self.details = details or {}
        self.errors = errors or []
        self.warnings = warnings or []
        self.execution_time = execution_time
        self.timestamp = datetime.datetime.now(datetime.timezone.utc).isoformat()

    def to_dict(self) -> Dict[str, Any]:
        """Convert the validation result to a dictionary."""
        return {
            'validator_id': self.validator_id,
            'is_valid': self.is_valid,
            'confidence_level': self.confidence_level.name,
            'score': self.score,
            'details': self.details,
            'errors': self.errors,
            'warnings': self.warnings,
            'execution_time': self.execution_time,
            'timestamp': self.timestamp
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ValidationResult':
        """Create a validation result from a dictionary."""
        confidence_level = ValidationLevel[data.get('confidence_level', 'MEDIUM')]
        return cls(
            validator_id=data.get('validator_id', 'unknown'),
            is_valid=data.get('is_valid', False),
            confidence_level=confidence_level,
            score=data.get('score'),
            details=data.get('details'),
            errors=data.get('errors'),
            warnings=data.get('warnings'),
            execution_time=data.get('execution_time')
        )


class ValidationChain:
    """
    Represents a chain of validators to be executed in sequence or parallel.

    This class allows for defining complex validation workflows with
    dependencies between validators.
    """

    def __init__(self,
                chain_id: str,
                name: str,
                description: str = None,
                validators: List[Dict[str, Any]] = None,
                mode: ValidationMode = ValidationMode.SEQUENTIAL,
                required_score: float = 70.0,
                required_level: ValidationLevel = ValidationLevel.MEDIUM):
        """
        Initialize a validation chain.

        Args:
            chain_id: Unique identifier for the chain
            name: Human-readable name for the chain
            description: Description of the chain's purpose
            validators: List of validator configurations
            mode: Execution mode for the chain
            required_score: Minimum score required for the chain to pass
            required_level: Maximum validation level required to pass
        """
        self.chain_id = chain_id
        self.name = name
        self.description = description or ""
        self.validators = validators or []
        self.mode = mode
        self.required_score = required_score
        self.required_level = required_level

    def to_dict(self) -> Dict[str, Any]:
        """Convert the validation chain to a dictionary."""
        return {
            'chain_id': self.chain_id,
            'name': self.name,
            'description': self.description,
            'validators': self.validators,
            'mode': self.mode.value,
            'required_score': self.required_score,
            'required_level': self.required_level.name
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ValidationChain':
        """Create a validation chain from a dictionary."""
        mode = ValidationMode(data.get('mode', 'sequential'))
        required_level = ValidationLevel[data.get('required_level', 'MEDIUM')]
        return cls(
            chain_id=data.get('chain_id', 'unknown'),
            name=data.get('name', 'Unnamed Chain'),
            description=data.get('description', ''),
            validators=data.get('validators', []),
            mode=mode,
            required_score=data.get('required_score', 70.0),
            required_level=required_level
        )

class ValidatorManager:
    """
    Manager for evidence validators.

    This class is responsible for registering, managing, and executing
    evidence validators, with support for validation chaining, custom
    validation scripts, and validation result scoring.
    """

    def __init__(self,
                validators_dir: Optional[str] = None,
                chains_dir: Optional[str] = None,
                scripts_dir: Optional[str] = None):
        """
        Initialize the Validator Manager.

        Args:
            validators_dir: Path to a directory containing validator implementations
            chains_dir: Path to a directory containing validation chain definitions
            scripts_dir: Path to a directory containing custom validation scripts
        """
        logger.info("Initializing Validator Manager")

        # Initialize the validators dictionary
        self.validators: Dict[str, Callable] = {}

        # Initialize the validation chains dictionary
        self.chains: Dict[str, ValidationChain] = {}

        # Initialize the validation scripts dictionary
        self.scripts: Dict[str, Dict[str, Any]] = {}

        # Set the chains directory
        self.chains_dir = chains_dir or os.path.join(os.getcwd(), 'validation_chains')

        # Create the chains directory if it doesn't exist
        os.makedirs(self.chains_dir, exist_ok=True)

        # Set the scripts directory
        self.scripts_dir = scripts_dir or os.path.join(os.getcwd(), 'validation_scripts')

        # Create the scripts directory if it doesn't exist
        os.makedirs(self.scripts_dir, exist_ok=True)

        # Register default validators
        self._register_default_validators()

        # Load custom validators if provided
        if validators_dir and os.path.exists(validators_dir):
            self._load_validators_from_directory(validators_dir)

        # Load validation chains
        self._load_validation_chains()

        # Load validation scripts
        self._load_validation_scripts()

        logger.info(f"Validator Manager initialized with {len(self.validators)} validators, {len(self.chains)} chains, and {len(self.scripts)} scripts")

    def _register_default_validators(self) -> None:
        """Register default validator implementations."""
        # File validators
        self.register_validator('file_exists', self._validate_file_exists)
        self.register_validator('file_content', self._validate_file_content)
        self.register_validator('file_metadata', self._validate_file_metadata)

        # Database validators
        self.register_validator('database_results', self._validate_database_results)
        self.register_validator('database_schema', self._validate_database_schema)

        # API validators
        self.register_validator('api_response', self._validate_api_response)
        self.register_validator('api_status', self._validate_api_status)

        # Cloud service validators
        self.register_validator('cloud_resource', self._validate_cloud_resource)
        self.register_validator('cloud_policy', self._validate_cloud_policy)

        # Log validators
        self.register_validator('log_entries', self._validate_log_entries)
        self.register_validator('log_patterns', self._validate_log_patterns)

        # Config validators
        self.register_validator('config_settings', self._validate_config_settings)
        self.register_validator('config_compliance', self._validate_config_compliance)

    def _load_validators_from_directory(self, directory: str) -> None:
        """
        Load validator implementations from a directory.

        Args:
            directory: Path to the directory containing validator modules
        """
        try:
            # Get all Python files in the directory
            validator_files = [f for f in os.listdir(directory) if f.endswith('.py') and not f.startswith('__')]

            for validator_file in validator_files:
                try:
                    # Import the module
                    module_name = validator_file[:-3]  # Remove .py extension
                    module_path = os.path.join(directory, validator_file)
                    spec = importlib.util.spec_from_file_location(module_name, module_path)
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)

                    # Find all validator functions in the module
                    for attr_name in dir(module):
                        attr = getattr(module, attr_name)
                        if callable(attr) and attr_name.startswith('validate_'):
                            # Register the validator
                            validator_id = attr_name[9:]  # Remove 'validate_' prefix
                            self.register_validator(validator_id, attr)
                            logger.info(f"Loaded validator {validator_id} from {validator_file}")

                except Exception as e:
                    logger.error(f"Failed to load validators from {validator_file}: {e}")

        except Exception as e:
            logger.error(f"Failed to load validators from directory {directory}: {e}")

    def register_validator(self, validator_id: str, validator_func: Callable) -> None:
        """
        Register a validator implementation.

        Args:
            validator_id: The ID of the validator
            validator_func: The validator implementation function
        """
        self.validators[validator_id] = validator_func
        logger.info(f"Registered validator: {validator_id}")

    def _load_validation_chains(self) -> None:
        """Load validation chains from disk."""
        try:
            # Get all JSON files in the chains directory
            chain_files = [f for f in os.listdir(self.chains_dir) if f.endswith('.json')]

            for chain_file in chain_files:
                try:
                    # Load the chain
                    file_path = os.path.join(self.chains_dir, chain_file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        chain_data = json.load(f)

                    # Get the chain ID from the filename (without extension)
                    chain_id = os.path.splitext(chain_file)[0]

                    # Create the validation chain
                    chain = ValidationChain.from_dict(chain_data)

                    # Add the chain to the in-memory dictionary
                    self.chains[chain_id] = chain

                    logger.info(f"Loaded validation chain: {chain_id}")

                except Exception as e:
                    logger.error(f"Failed to load validation chain from {chain_file}: {e}")

            logger.info(f"Loaded {len(self.chains)} validation chains")

        except Exception as e:
            logger.error(f"Failed to load validation chains from directory {self.chains_dir}: {e}")

    def _save_validation_chain(self, chain_id: str) -> None:
        """
        Save a validation chain to disk.

        Args:
            chain_id: The ID of the chain
        """
        try:
            # Get the chain
            chain = self.chains.get(chain_id)

            if not chain:
                logger.warning(f"No chain found with ID: {chain_id}")
                return

            # Save the chain to disk
            file_path = os.path.join(self.chains_dir, f"{chain_id}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(chain.to_dict(), f, indent=2)

            logger.info(f"Saved validation chain: {chain_id}")

        except Exception as e:
            logger.error(f"Failed to save validation chain {chain_id}: {e}")

    def _load_validation_scripts(self) -> None:
        """Load validation scripts from disk."""
        try:
            # Get all Python files in the scripts directory
            script_files = [f for f in os.listdir(self.scripts_dir) if f.endswith('.py') and not f.startswith('__')]

            for script_file in script_files:
                try:
                    # Get the script ID from the filename (without extension)
                    script_id = os.path.splitext(script_file)[0]

                    # Load the script
                    file_path = os.path.join(self.scripts_dir, script_file)

                    # Read the script content
                    with open(file_path, 'r', encoding='utf-8') as f:
                        script_content = f.read()

                    # Extract metadata from script comments
                    metadata = self._extract_script_metadata(script_content)

                    # Add the script to the in-memory dictionary
                    self.scripts[script_id] = {
                        'id': script_id,
                        'path': file_path,
                        'content': script_content,
                        'metadata': metadata
                    }

                    logger.info(f"Loaded validation script: {script_id}")

                except Exception as e:
                    logger.error(f"Failed to load validation script from {script_file}: {e}")

            logger.info(f"Loaded {len(self.scripts)} validation scripts")

        except Exception as e:
            logger.error(f"Failed to load validation scripts from directory {self.scripts_dir}: {e}")

    def _extract_script_metadata(self, script_content: str) -> Dict[str, Any]:
        """
        Extract metadata from script comments.

        Args:
            script_content: The content of the script

        Returns:
            Dictionary of metadata
        """
        metadata = {
            'name': 'Unnamed Script',
            'description': '',
            'author': '',
            'version': '1.0.0',
            'confidence_level': 'MEDIUM',
            'tags': []
        }

        # Extract metadata from docstring
        docstring_match = re.search(r'"""(.*?)"""', script_content, re.DOTALL)
        if docstring_match:
            docstring = docstring_match.group(1).strip()

            # Extract name
            name_match = re.search(r'^(.+?)(?:\n|$)', docstring)
            if name_match:
                metadata['name'] = name_match.group(1).strip()

            # Extract description
            desc_lines = []
            for line in docstring.split('\n')[1:]:
                if line.strip() and not line.strip().startswith('@'):
                    desc_lines.append(line.strip())
                elif line.strip().startswith('@'):
                    break

            if desc_lines:
                metadata['description'] = '\n'.join(desc_lines)

            # Extract other metadata
            author_match = re.search(r'@author\s+(.+?)(?:\n|$)', docstring)
            if author_match:
                metadata['author'] = author_match.group(1).strip()

            version_match = re.search(r'@version\s+(.+?)(?:\n|$)', docstring)
            if version_match:
                metadata['version'] = version_match.group(1).strip()

            level_match = re.search(r'@confidence_level\s+(.+?)(?:\n|$)', docstring)
            if level_match:
                metadata['confidence_level'] = level_match.group(1).strip()

            tags_match = re.search(r'@tags\s+(.+?)(?:\n|$)', docstring)
            if tags_match:
                tags = tags_match.group(1).strip()
                metadata['tags'] = [tag.strip() for tag in tags.split(',')]

        return metadata

    def create_validation_chain(self,
                              chain_id: str,
                              name: str,
                              description: str = None,
                              validators: List[Dict[str, Any]] = None,
                              mode: ValidationMode = ValidationMode.SEQUENTIAL,
                              required_score: float = 70.0,
                              required_level: ValidationLevel = ValidationLevel.MEDIUM) -> ValidationChain:
        """
        Create a new validation chain.

        Args:
            chain_id: Unique identifier for the chain
            name: Human-readable name for the chain
            description: Description of the chain's purpose
            validators: List of validator configurations
            mode: Execution mode for the chain
            required_score: Minimum score required for the chain to pass
            required_level: Maximum validation level required to pass

        Returns:
            The created validation chain

        Raises:
            ValueError: If the chain ID already exists
        """
        # Check if the chain ID already exists
        if chain_id in self.chains:
            raise ValueError(f"Validation chain ID already exists: {chain_id}")

        # Create the validation chain
        chain = ValidationChain(
            chain_id=chain_id,
            name=name,
            description=description,
            validators=validators or [],
            mode=mode,
            required_score=required_score,
            required_level=required_level
        )

        # Add the chain to the in-memory dictionary
        self.chains[chain_id] = chain

        # Save the chain to disk
        self._save_validation_chain(chain_id)

        logger.info(f"Created validation chain: {chain_id}")

        return chain

    def update_validation_chain(self,
                              chain_id: str,
                              name: Optional[str] = None,
                              description: Optional[str] = None,
                              validators: Optional[List[Dict[str, Any]]] = None,
                              mode: Optional[ValidationMode] = None,
                              required_score: Optional[float] = None,
                              required_level: Optional[ValidationLevel] = None) -> ValidationChain:
        """
        Update an existing validation chain.

        Args:
            chain_id: The ID of the chain to update
            name: New name for the chain
            description: New description for the chain
            validators: New list of validator configurations
            mode: New execution mode for the chain
            required_score: New minimum score required for the chain to pass
            required_level: New maximum validation level required to pass

        Returns:
            The updated validation chain

        Raises:
            ValueError: If the chain does not exist
        """
        # Check if the chain exists
        if chain_id not in self.chains:
            raise ValueError(f"Validation chain not found: {chain_id}")

        # Get the chain
        chain = self.chains[chain_id]

        # Update the chain
        if name is not None:
            chain.name = name

        if description is not None:
            chain.description = description

        if validators is not None:
            chain.validators = validators

        if mode is not None:
            chain.mode = mode

        if required_score is not None:
            chain.required_score = required_score

        if required_level is not None:
            chain.required_level = required_level

        # Save the updated chain to disk
        self._save_validation_chain(chain_id)

        logger.info(f"Updated validation chain: {chain_id}")

        return chain

    def delete_validation_chain(self, chain_id: str) -> None:
        """
        Delete a validation chain.

        Args:
            chain_id: The ID of the chain to delete

        Raises:
            ValueError: If the chain does not exist
        """
        # Check if the chain exists
        if chain_id not in self.chains:
            raise ValueError(f"Validation chain not found: {chain_id}")

        # Remove the chain from the in-memory dictionary
        del self.chains[chain_id]

        # Remove the chain file from disk
        file_path = os.path.join(self.chains_dir, f"{chain_id}.json")
        if os.path.exists(file_path):
            os.remove(file_path)

        logger.info(f"Deleted validation chain: {chain_id}")

    def get_validation_chain(self, chain_id: str) -> ValidationChain:
        """
        Get a validation chain.

        Args:
            chain_id: The ID of the chain

        Returns:
            The validation chain

        Raises:
            ValueError: If the chain does not exist
        """
        # Check if the chain exists
        if chain_id not in self.chains:
            raise ValueError(f"Validation chain not found: {chain_id}")

        return self.chains[chain_id]

    def get_validation_chains(self) -> List[ValidationChain]:
        """
        Get all validation chains.

        Returns:
            List of validation chains
        """
        return list(self.chains.values())

    def create_validation_script(self,
                               script_id: str,
                               content: str,
                               name: str = None,
                               description: str = None,
                               author: str = None,
                               version: str = None,
                               confidence_level: str = None,
                               tags: List[str] = None) -> Dict[str, Any]:
        """
        Create a new validation script.

        Args:
            script_id: Unique identifier for the script
            content: The Python script content
            name: Human-readable name for the script
            description: Description of the script's purpose
            author: Author of the script
            version: Version of the script
            confidence_level: Confidence level of the script
            tags: List of tags for the script

        Returns:
            The created script metadata

        Raises:
            ValueError: If the script ID already exists
        """
        # Check if the script ID already exists
        if script_id in self.scripts:
            raise ValueError(f"Validation script ID already exists: {script_id}")

        # Update the script content with metadata
        if name or description or author or version or confidence_level or tags:
            # Create a docstring with metadata
            docstring = f'"""{name or "Unnamed Script"}\n\n'

            if description:
                docstring += f"{description}\n\n"

            if author:
                docstring += f"<AUTHOR>

            if version:
                docstring += f"@version {version}\n"

            if confidence_level:
                docstring += f"@confidence_level {confidence_level}\n"

            if tags:
                docstring += f"@tags {', '.join(tags)}\n"

            docstring += '"""\n\n'

            # Add the docstring to the script content
            content = docstring + content

        # Save the script to disk
        file_path = os.path.join(self.scripts_dir, f"{script_id}.py")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        # Extract metadata from the script
        metadata = self._extract_script_metadata(content)

        # Add the script to the in-memory dictionary
        script = {
            'id': script_id,
            'path': file_path,
            'content': content,
            'metadata': metadata
        }

        self.scripts[script_id] = script

        logger.info(f"Created validation script: {script_id}")

        return script

    def update_validation_script(self,
                               script_id: str,
                               content: Optional[str] = None,
                               name: Optional[str] = None,
                               description: Optional[str] = None,
                               author: Optional[str] = None,
                               version: Optional[str] = None,
                               confidence_level: Optional[str] = None,
                               tags: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Update an existing validation script.

        Args:
            script_id: The ID of the script to update
            content: New script content
            name: New name for the script
            description: New description for the script
            author: New author for the script
            version: New version for the script
            confidence_level: New confidence level for the script
            tags: New tags for the script

        Returns:
            The updated script metadata

        Raises:
            ValueError: If the script does not exist
        """
        # Check if the script exists
        if script_id not in self.scripts:
            raise ValueError(f"Validation script not found: {script_id}")

        # Get the script
        script = self.scripts[script_id]

        # If content is provided, update it
        if content is not None:
            script['content'] = content

        # If metadata is provided, update the script content with the new metadata
        if name is not None or description is not None or author is not None or version is not None or confidence_level is not None or tags is not None:
            # Extract current metadata
            current_metadata = script['metadata']

            # Create a docstring with updated metadata
            docstring = f'"""{name or current_metadata["name"]}\n\n'

            if description is not None:
                docstring += f"{description}\n\n"
            elif current_metadata.get('description'):
                docstring += f"{current_metadata['description']}\n\n"

            if author is not None:
                docstring += f"<AUTHOR>
            elif current_metadata.get('author'):
                docstring += f"<AUTHOR>

            if version is not None:
                docstring += f"@version {version}\n"
            elif current_metadata.get('version'):
                docstring += f"@version {current_metadata['version']}\n"

            if confidence_level is not None:
                docstring += f"@confidence_level {confidence_level}\n"
            elif current_metadata.get('confidence_level'):
                docstring += f"@confidence_level {current_metadata['confidence_level']}\n"

            if tags is not None:
                docstring += f"@tags {', '.join(tags)}\n"
            elif current_metadata.get('tags'):
                docstring += f"@tags {', '.join(current_metadata['tags'])}\n"

            docstring += '"""\n\n'

            # Remove existing docstring if present
            content = script['content']
            docstring_match = re.search(r'"""(.*?)"""', content, re.DOTALL)
            if docstring_match:
                content = content[docstring_match.end():].strip()

            # Add the new docstring to the script content
            script['content'] = docstring + content

        # Save the updated script to disk
        with open(script['path'], 'w', encoding='utf-8') as f:
            f.write(script['content'])

        # Extract metadata from the updated script
        script['metadata'] = self._extract_script_metadata(script['content'])

        logger.info(f"Updated validation script: {script_id}")

        return script

    def delete_validation_script(self, script_id: str) -> None:
        """
        Delete a validation script.

        Args:
            script_id: The ID of the script to delete

        Raises:
            ValueError: If the script does not exist
        """
        # Check if the script exists
        if script_id not in self.scripts:
            raise ValueError(f"Validation script not found: {script_id}")

        # Get the script path
        script_path = self.scripts[script_id]['path']

        # Remove the script from the in-memory dictionary
        del self.scripts[script_id]

        # Remove the script file from disk
        if os.path.exists(script_path):
            os.remove(script_path)

        logger.info(f"Deleted validation script: {script_id}")

    def get_validation_script(self, script_id: str) -> Dict[str, Any]:
        """
        Get a validation script.

        Args:
            script_id: The ID of the script

        Returns:
            The script metadata

        Raises:
            ValueError: If the script does not exist
        """
        # Check if the script exists
        if script_id not in self.scripts:
            raise ValueError(f"Validation script not found: {script_id}")

        return self.scripts[script_id]

    def get_validation_scripts(self) -> List[Dict[str, Any]]:
        """
        Get all validation scripts.

        Returns:
            List of script metadata
        """
        return list(self.scripts.values())

    def validate(self, validator_id: str, evidence: Dict[str, Any]) -> ValidationResult:
        """
        Validate evidence using a specific validator.

        Args:
            validator_id: The ID of the validator
            evidence: The evidence to validate

        Returns:
            The validation result

        Raises:
            ValueError: If the validator does not exist
        """
        logger.info(f"Validating evidence using validator: {validator_id}")

        # Check if the validator is a script
        if validator_id in self.scripts:
            return self._validate_with_script(validator_id, evidence)

        # Check if the validator is a chain
        if validator_id in self.chains:
            return self._validate_with_chain(validator_id, evidence)

        # Check if the validator is a built-in validator
        if validator_id not in self.validators:
            raise ValueError(f"Validator not found: {validator_id}")

        try:
            # Record the start time
            start_time = datetime.datetime.now()

            # Execute the validator
            validator_func = self.validators[validator_id]
            validation_results = validator_func(evidence)

            # Record the end time
            end_time = datetime.datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            # Convert the legacy validation results to a ValidationResult object
            is_valid = validation_results.get('is_valid', False)
            details = validation_results.get('details', {})
            errors = validation_results.get('errors', [])

            result = ValidationResult(
                validator_id=validator_id,
                is_valid=is_valid,
                confidence_level=ValidationLevel.MEDIUM,
                score=100 if is_valid else 0,
                details=details,
                errors=errors,
                execution_time=execution_time
            )

            logger.info(f"Evidence validated successfully using validator: {validator_id}")

            return result
        except Exception as e:
            logger.error(f"Failed to validate evidence using validator {validator_id}: {e}")

            # Create a validation result for the error
            result = ValidationResult(
                validator_id=validator_id,
                is_valid=False,
                confidence_level=ValidationLevel.MEDIUM,
                score=0,
                errors=[str(e)]
            )

            return result

    def _validate_with_script(self, script_id: str, evidence: Dict[str, Any]) -> ValidationResult:
        """
        Validate evidence using a custom script.

        Args:
            script_id: The ID of the script
            evidence: The evidence to validate

        Returns:
            The validation result
        """
        logger.info(f"Validating evidence using script: {script_id}")

        try:
            # Get the script
            script = self.scripts[script_id]

            # Record the start time
            start_time = datetime.datetime.now()

            # Create a namespace for the script
            namespace = {
                'evidence': evidence,
                'ValidationResult': ValidationResult,
                'ValidationLevel': ValidationLevel,
                'logger': logger
            }

            # Execute the script
            exec(script['content'], namespace)

            # Get the validation result
            if 'result' in namespace:
                result = namespace['result']
            else:
                # If the script doesn't set a result, create a default one
                result = ValidationResult(
                    validator_id=script_id,
                    is_valid=False,
                    errors=["Script did not return a validation result"]
                )

            # Record the end time
            end_time = datetime.datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            # Update the execution time
            result.execution_time = execution_time

            logger.info(f"Evidence validated successfully using script: {script_id}")

            return result
        except Exception as e:
            logger.error(f"Failed to validate evidence using script {script_id}: {e}")

            # Create a validation result for the error
            result = ValidationResult(
                validator_id=script_id,
                is_valid=False,
                confidence_level=ValidationLevel.MEDIUM,
                score=0,
                errors=[str(e)]
            )

            return result

    def _validate_with_chain(self, chain_id: str, evidence: Dict[str, Any]) -> ValidationResult:
        """
        Validate evidence using a validation chain.

        Args:
            chain_id: The ID of the chain
            evidence: The evidence to validate

        Returns:
            The validation result
        """
        logger.info(f"Validating evidence using chain: {chain_id}")

        try:
            # Get the chain
            chain = self.chains[chain_id]

            # Record the start time
            start_time = datetime.datetime.now()

            # Initialize the results
            results = []
            is_valid = True
            total_score = 0
            all_errors = []
            all_warnings = []

            # Execute the validators in the chain
            if chain.mode == ValidationMode.PARALLEL:
                # Execute all validators in parallel
                import concurrent.futures

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    # Submit all validation tasks
                    future_to_validator = {
                        executor.submit(self.validate, validator['id'], evidence): validator
                        for validator in chain.validators
                    }

                    # Process the results as they complete
                    for future in concurrent.futures.as_completed(future_to_validator):
                        validator = future_to_validator[future]
                        try:
                            result = future.result()
                            results.append(result)
                        except Exception as e:
                            logger.error(f"Error executing validator {validator['id']}: {e}")
                            # Create a validation result for the error
                            result = ValidationResult(
                                validator_id=validator['id'],
                                is_valid=False,
                                confidence_level=ValidationLevel.MEDIUM,
                                score=0,
                                errors=[str(e)]
                            )
                            results.append(result)
            else:
                # Execute validators sequentially
                for validator in chain.validators:
                    result = self.validate(validator['id'], evidence)
                    results.append(result)

                    # If the validator failed and we're in SEQUENTIAL mode, stop
                    if not result.is_valid and chain.mode == ValidationMode.SEQUENTIAL:
                        break

            # Calculate the overall result
            for result in results:
                # Check if the result is valid based on the required level
                if not result.is_valid and result.confidence_level.value <= chain.required_level.value:
                    is_valid = False

                # Add to the total score
                total_score += result.score

                # Collect errors and warnings
                all_errors.extend(result.errors)
                all_warnings.extend(result.warnings)

            # Calculate the average score
            avg_score = total_score / len(results) if results else 0

            # Check if the average score meets the required score
            if avg_score < chain.required_score:
                is_valid = False

            # Record the end time
            end_time = datetime.datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            # Create the overall validation result
            result = ValidationResult(
                validator_id=chain_id,
                is_valid=is_valid,
                confidence_level=chain.required_level,
                score=avg_score,
                details={
                    'chain_id': chain_id,
                    'chain_name': chain.name,
                    'mode': chain.mode.value,
                    'required_score': chain.required_score,
                    'required_level': chain.required_level.name,
                    'validator_results': [r.to_dict() for r in results]
                },
                errors=all_errors,
                warnings=all_warnings,
                execution_time=execution_time
            )

            logger.info(f"Evidence validated using chain: {chain_id}, result: {is_valid}")

            return result
        except Exception as e:
            logger.error(f"Failed to validate evidence using chain {chain_id}: {e}")

            # Create a validation result for the error
            result = ValidationResult(
                validator_id=chain_id,
                is_valid=False,
                confidence_level=ValidationLevel.MEDIUM,
                score=0,
                errors=[str(e)]
            )

            return result

    def get_validator_types(self) -> List[str]:
        """
        Get all registered validator IDs.

        Returns:
            List of validator IDs
        """
        # Combine built-in validators, scripts, and chains
        return list(self.validators.keys()) + list(self.scripts.keys()) + list(self.chains.keys())

    # Default validator implementations

    def _validate_file_exists(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate that a file exists.

        Args:
            evidence: The evidence to validate

        Returns:
            The validation results
        """
        logger.info("Validating file exists")

        # Get the file path from the evidence
        file_path = evidence.get('data', {}).get('file_path')

        if not file_path:
            return {
                'is_valid': False,
                'errors': ['Missing file path in evidence']
            }

        # In a real implementation, this would check if the file exists
        # For now, assume the file exists
        return {
            'is_valid': True,
            'details': {
                'file_path': file_path,
                'exists': True
            }
        }

    def _validate_file_content(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate file content.

        Args:
            evidence: The evidence to validate

        Returns:
            The validation results
        """
        logger.info("Validating file content")

        # Get the file content from the evidence
        file_content = evidence.get('data', {}).get('content')

        if not file_content:
            return {
                'is_valid': False,
                'errors': ['Missing file content in evidence']
            }

        # In a real implementation, this would validate the file content
        # For now, assume the content is valid
        return {
            'is_valid': True,
            'details': {
                'content_length': len(file_content),
                'content_valid': True
            }
        }

    def _validate_file_metadata(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate file metadata.

        Args:
            evidence: The evidence to validate

        Returns:
            The validation results
        """
        logger.info("Validating file metadata")

        # Get the file metadata from the evidence
        metadata = evidence.get('data', {}).get('metadata')

        if not metadata:
            return {
                'is_valid': False,
                'errors': ['Missing file metadata in evidence']
            }

        # In a real implementation, this would validate the file metadata
        # For now, assume the metadata is valid
        return {
            'is_valid': True,
            'details': {
                'metadata_fields': list(metadata.keys()),
                'metadata_valid': True
            }
        }

    def _validate_database_results(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate database results.

        Args:
            evidence: The evidence to validate

        Returns:
            The validation results
        """
        logger.info("Validating database results")

        # Get the database results from the evidence
        results = evidence.get('data', {}).get('results')

        if not results:
            return {
                'is_valid': False,
                'errors': ['Missing database results in evidence']
            }

        # In a real implementation, this would validate the database results
        # For now, assume the results are valid
        return {
            'is_valid': True,
            'details': {
                'results_count': len(results),
                'results_valid': True
            }
        }

    def _validate_database_schema(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate database schema.

        Args:
            evidence: The evidence to validate

        Returns:
            The validation results
        """
        logger.info("Validating database schema")

        # Get the database results from the evidence
        results = evidence.get('data', {}).get('results')

        if not results or not isinstance(results, list) or not results:
            return {
                'is_valid': False,
                'errors': ['Missing or invalid database results in evidence']
            }

        # In a real implementation, this would validate the database schema
        # For now, assume the schema is valid
        return {
            'is_valid': True,
            'details': {
                'schema_fields': list(results[0].keys()),
                'schema_valid': True
            }
        }

    def _validate_api_response(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate API response.

        Args:
            evidence: The evidence to validate

        Returns:
            The validation results
        """
        logger.info("Validating API response")

        # Get the API response from the evidence
        response = evidence.get('data', {}).get('response')

        if not response:
            return {
                'is_valid': False,
                'errors': ['Missing API response in evidence']
            }

        # In a real implementation, this would validate the API response
        # For now, assume the response is valid
        return {
            'is_valid': True,
            'details': {
                'response_fields': list(response.keys()),
                'response_valid': True
            }
        }

    def _validate_api_status(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate API status.

        Args:
            evidence: The evidence to validate

        Returns:
            The validation results
        """
        logger.info("Validating API status")

        # Get the API response from the evidence
        response = evidence.get('data', {}).get('response')

        if not response:
            return {
                'is_valid': False,
                'errors': ['Missing API response in evidence']
            }

        # Get the status code
        status_code = response.get('status_code')

        if not status_code:
            return {
                'is_valid': False,
                'errors': ['Missing status code in API response']
            }

        # In a real implementation, this would validate the API status
        # For now, assume the status is valid if it's 200
        is_valid = status_code == 200

        return {
            'is_valid': is_valid,
            'details': {
                'status_code': status_code,
                'status_valid': is_valid
            }
        }

    def _validate_cloud_resource(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate cloud resource.

        Args:
            evidence: The evidence to validate

        Returns:
            The validation results
        """
        logger.info("Validating cloud resource")

        # Get the cloud response from the evidence
        response = evidence.get('data', {}).get('response')

        if not response:
            return {
                'is_valid': False,
                'errors': ['Missing cloud response in evidence']
            }

        # In a real implementation, this would validate the cloud resource
        # For now, assume the resource is valid
        return {
            'is_valid': True,
            'details': {
                'resource_fields': list(response.get('data', {}).keys()),
                'resource_valid': True
            }
        }

    def _validate_cloud_policy(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate cloud policy.

        Args:
            evidence: The evidence to validate

        Returns:
            The validation results
        """
        logger.info("Validating cloud policy")

        # Get the cloud response from the evidence
        response = evidence.get('data', {}).get('response')

        if not response:
            return {
                'is_valid': False,
                'errors': ['Missing cloud response in evidence']
            }

        # In a real implementation, this would validate the cloud policy
        # For now, assume the policy is valid
        return {
            'is_valid': True,
            'details': {
                'policy_valid': True
            }
        }

    def _validate_log_entries(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate log entries.

        Args:
            evidence: The evidence to validate

        Returns:
            The validation results
        """
        logger.info("Validating log entries")

        # Get the log entries from the evidence
        entries = evidence.get('data', {}).get('entries')

        if not entries:
            return {
                'is_valid': False,
                'errors': ['Missing log entries in evidence']
            }

        # In a real implementation, this would validate the log entries
        # For now, assume the entries are valid
        return {
            'is_valid': True,
            'details': {
                'entries_count': len(entries),
                'entries_valid': True
            }
        }

    def _validate_log_patterns(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate log patterns.

        Args:
            evidence: The evidence to validate

        Returns:
            The validation results
        """
        logger.info("Validating log patterns")

        # Get the log entries from the evidence
        entries = evidence.get('data', {}).get('entries')

        if not entries:
            return {
                'is_valid': False,
                'errors': ['Missing log entries in evidence']
            }

        # Get the filter pattern
        filter_pattern = evidence.get('data', {}).get('filter_pattern')

        # In a real implementation, this would validate the log patterns
        # For now, assume the patterns are valid
        return {
            'is_valid': True,
            'details': {
                'pattern': filter_pattern,
                'pattern_matches': len(entries),
                'patterns_valid': True
            }
        }

    def _validate_config_settings(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate configuration settings.

        Args:
            evidence: The evidence to validate

        Returns:
            The validation results
        """
        logger.info("Validating configuration settings")

        # Get the configuration from the evidence
        config = evidence.get('data', {}).get('config')

        if not config:
            return {
                'is_valid': False,
                'errors': ['Missing configuration in evidence']
            }

        # In a real implementation, this would validate the configuration settings
        # For now, assume the settings are valid
        return {
            'is_valid': True,
            'details': {
                'settings_count': len(config),
                'settings_valid': True
            }
        }

    def _validate_config_compliance(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate configuration compliance.

        Args:
            evidence: The evidence to validate

        Returns:
            The validation results
        """
        logger.info("Validating configuration compliance")

        # Get the configuration from the evidence
        config = evidence.get('data', {}).get('config')

        if not config:
            return {
                'is_valid': False,
                'errors': ['Missing configuration in evidence']
            }

        # In a real implementation, this would validate the configuration compliance
        # For now, assume the configuration is compliant
        return {
            'is_valid': True,
            'details': {
                'compliance_valid': True
            }
        }
