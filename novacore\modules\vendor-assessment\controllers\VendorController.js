/**
 * NovaCore Vendor Controller
 * 
 * This controller handles API requests related to vendors.
 */

const { VendorService } = require('../services');
const logger = require('../../../config/logger');

class VendorController {
  /**
   * Create a new vendor
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createVendor(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const vendor = await VendorService.createVendor(req.body, userId);
      
      res.status(201).json({
        success: true,
        data: vendor
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get all vendors
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllVendors(req, res, next) {
    try {
      const { organizationId } = req.params;
      
      // Extract filter parameters from query
      const filter = {};
      
      if (req.query.status) filter.status = req.query.status;
      if (req.query.tier) filter.tier = req.query.tier;
      if (req.query.tags) filter.tags = req.query.tags.split(',');
      if (req.query.cyberSafetyCertified) filter.cyberSafetyCertified = req.query.cyberSafetyCertified;
      if (req.query.search) filter.search = req.query.search;
      
      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };
      
      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }
      
      const result = await VendorService.getAllVendors(organizationId, filter, options);
      
      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get vendor by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getVendorById(req, res, next) {
    try {
      const vendor = await VendorService.getVendorById(req.params.id);
      
      res.status(200).json({
        success: true,
        data: vendor
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update vendor
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateVendor(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const vendor = await VendorService.updateVendor(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: vendor
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Delete vendor
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async deleteVendor(req, res, next) {
    try {
      await VendorService.deleteVendor(req.params.id);
      
      res.status(200).json({
        success: true,
        message: 'Vendor deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get vendor assessments
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getVendorAssessments(req, res, next) {
    try {
      const assessments = await VendorService.getVendorAssessments(req.params.id);
      
      res.status(200).json({
        success: true,
        data: assessments
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get vendor documents
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getVendorDocuments(req, res, next) {
    try {
      const documents = await VendorService.getVendorDocuments(req.params.id);
      
      res.status(200).json({
        success: true,
        data: documents
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update vendor compliance status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateComplianceStatus(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const vendor = await VendorService.updateComplianceStatus(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: vendor
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update vendor risk score
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateRiskScore(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const vendor = await VendorService.updateRiskScore(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: vendor
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update vendor Cyber-Safety certification
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateCyberSafetyCertification(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const vendor = await VendorService.updateCyberSafetyCertification(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: vendor
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get vendor dashboard
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getVendorDashboard(req, res, next) {
    try {
      const { organizationId } = req.params;
      const dashboard = await VendorService.getVendorDashboard(organizationId);
      
      res.status(200).json({
        success: true,
        data: dashboard
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new VendorController();
