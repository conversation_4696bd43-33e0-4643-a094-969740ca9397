#!/bin/bash
# Script to deploy NovaConnect UAC to GKE

# Set variables
PROJECT_ID=${1:-"novafuse-test"}
CLUSTER_NAME=${2:-"novafuse-test-cluster"}
ZONE=${3:-"us-central1-a"}
NAMESPACE=${4:-"novafuse-test"}

# Get credentials for the cluster
echo "Getting credentials for the cluster..."
gcloud container clusters get-credentials $CLUSTER_NAME --zone $ZONE --project $PROJECT_ID

# Create the service account
echo "Creating service account..."
kubectl apply -f k8s/marketplace/test/service.yaml

# Create the configmap
echo "Creating configmap..."
kubectl apply -f k8s/marketplace/test/configmap.yaml

# Create the secret
echo "Creating secret..."
kubectl apply -f k8s/marketplace/test/secret.yaml

# Create the deployment
echo "Creating deployment..."
kubectl apply -f k8s/marketplace/test/deployment.yaml

# Wait for the deployment to be ready
echo "Waiting for deployment to be ready..."
kubectl rollout status deployment/novafuse-uac -n $NAMESPACE

# Get the service URL
echo "Getting service URL..."
SERVICE_IP=$(kubectl get service novafuse-uac -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
if [ -z "$SERVICE_IP" ]; then
  echo "Service is not exposed externally. Creating a port-forward..."
  kubectl port-forward service/novafuse-uac -n $NAMESPACE 8080:80 &
  SERVICE_URL="http://localhost:8080"
  echo "Service available at $SERVICE_URL"
else
  SERVICE_URL="http://$SERVICE_IP"
  echo "Service available at $SERVICE_URL"
fi

echo "Deployment to GKE complete!"
