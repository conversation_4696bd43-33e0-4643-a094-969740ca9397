{"version": 3, "names": ["axios", "require", "jsonpath", "performance", "v4", "uuidv4", "SSRFProtection", "InputValidator", "SecureConnectorExecutor", "constructor", "connectorRegistry", "options", "enableMetrics", "enableCaching", "cacheTTL", "defaultTimeout", "maxConcurrentRequests", "ssrfProtection", "allowedProtocols", "allowPrivateIPs", "metrics", "totalRequests", "successfulRequests", "failedRequests", "blockedRequests", "totalRequestTime", "averageRequestTime", "activeRequests", "Set", "executeConnector", "connectorId", "endpointId", "params", "startTime", "now", "requestId", "size", "Error", "add", "connector", "getConnector", "endpoint", "endpoints", "find", "e", "id", "validateParameters", "url", "configuration", "baseUrl", "path", "key", "value", "Object", "entries", "isXssSafe", "isCommandSafe", "replace", "encodeURIComponent", "endsWith", "startsWith", "substring", "isSafe", "isSafeUrl", "headers", "authentication", "type", "apiKeyField", "keys", "fields", "f", "sensitive", "auth", "username", "password", "<PERSON><PERSON><PERSON>", "from", "toString", "token", "requestConfig", "method", "query", "data", "body", "timeout", "response", "result", "dataPath", "endTime", "duration", "delete", "success", "statusCode", "status", "error", "message", "parameters", "schema", "required", "undefined", "properties", "validateObject", "<PERSON><PERSON><PERSON><PERSON>", "errors", "join", "getMetrics", "getActiveRequests", "Array", "addAllowedHosts", "hosts", "addAllowedDomains", "domains", "module", "exports"], "sources": ["secure-connector-executor.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector Secure Executor\n * \n * This module provides a secure implementation of the connector executor\n * with SSRF protection, input validation, and rate limiting.\n */\n\nconst axios = require('axios');\nconst jsonpath = require('jsonpath');\nconst { performance } = require('perf_hooks');\nconst { v4: uuidv4 } = require('uuid');\nconst { SSRFProtection, InputValidator } = require('../security');\n\n/**\n * Secure Connector Executor\n * \n * Executes API connectors with security protections.\n */\nclass SecureConnectorExecutor {\n  constructor(connectorRegistry, options = {}) {\n    this.connectorRegistry = connectorRegistry;\n    this.options = {\n      enableMetrics: true,\n      enableCaching: true,\n      cacheTTL: 3600, // 1 hour\n      defaultTimeout: 30000, // 30 seconds\n      maxConcurrentRequests: 50,\n      ...options\n    };\n    \n    // Initialize SSRF protection\n    this.ssrfProtection = new SSRFProtection({\n      allowedProtocols: ['https:'],\n      allowPrivateIPs: false\n    });\n    \n    // Initialize metrics\n    this.metrics = {\n      totalRequests: 0,\n      successfulRequests: 0,\n      failedRequests: 0,\n      blockedRequests: 0,\n      totalRequestTime: 0,\n      averageRequestTime: 0\n    };\n    \n    // Initialize active requests tracking\n    this.activeRequests = new Set();\n  }\n\n  /**\n   * Execute a connector endpoint\n   * @param {string} connectorId - Connector ID\n   * @param {string} endpointId - Endpoint ID\n   * @param {Object} params - Parameters for the endpoint\n   * @returns {Promise<Object>} - Execution result\n   */\n  async executeConnector(connectorId, endpointId, params = {}) {\n    const startTime = this.options.enableMetrics ? performance.now() : 0;\n    const requestId = uuidv4();\n    \n    try {\n      // Check if we're at the concurrent request limit\n      if (this.activeRequests.size >= this.options.maxConcurrentRequests) {\n        throw new Error('Maximum concurrent requests limit reached');\n      }\n      \n      // Add to active requests\n      this.activeRequests.add(requestId);\n      \n      // Get connector\n      const connector = this.connectorRegistry.getConnector(connectorId);\n      if (!connector) {\n        throw new Error(`Connector ${connectorId} not found`);\n      }\n      \n      // Find endpoint\n      const endpoint = connector.endpoints.find(e => e.id === endpointId);\n      if (!endpoint) {\n        throw new Error(`Endpoint ${endpointId} not found in connector ${connectorId}`);\n      }\n      \n      // Validate parameters\n      this.validateParameters(params, endpoint);\n      \n      // Build request URL\n      let url = connector.configuration.baseUrl;\n      let path = endpoint.path;\n      \n      // Replace path parameters\n      if (params.path) {\n        for (const [key, value] of Object.entries(params.path)) {\n          // Validate path parameter\n          if (!InputValidator.isXssSafe(value) || !InputValidator.isCommandSafe(value)) {\n            throw new Error(`Invalid path parameter: ${key}`);\n          }\n          \n          path = path.replace(`{${key}}`, encodeURIComponent(value));\n        }\n      }\n      \n      url = url.endsWith('/') ? `${url}${path.startsWith('/') ? path.substring(1) : path}` : `${url}${path.startsWith('/') ? path : `/${path}`}`;\n      \n      // Check URL for SSRF\n      const isSafe = await this.ssrfProtection.isSafeUrl(url);\n      if (!isSafe) {\n        if (this.options.enableMetrics) {\n          this.metrics.totalRequests++;\n          this.metrics.blockedRequests++;\n        }\n        \n        throw new Error(`URL blocked by SSRF protection: ${url}`);\n      }\n      \n      // Build request headers\n      const headers = {\n        ...connector.configuration.headers\n      };\n      \n      // Add authentication headers\n      if (connector.authentication.type === 'API_KEY') {\n        const apiKeyField = Object.keys(connector.authentication.fields).find(f => \n          connector.authentication.fields[f].type === 'string' && \n          connector.authentication.fields[f].sensitive === true\n        );\n        \n        if (apiKeyField && params.auth && params.auth[apiKeyField]) {\n          headers['Authorization'] = `Bearer ${params.auth[apiKeyField]}`;\n        }\n      } else if (connector.authentication.type === 'BASIC') {\n        if (params.auth && params.auth.username && params.auth.password) {\n          const auth = Buffer.from(`${params.auth.username}:${params.auth.password}`).toString('base64');\n          headers['Authorization'] = `Basic ${auth}`;\n        }\n      } else if (connector.authentication.type === 'OAUTH2') {\n        if (params.auth && params.auth.token) {\n          headers['Authorization'] = `Bearer ${params.auth.token}`;\n        }\n      }\n      \n      // Add custom headers from parameters\n      if (params.headers) {\n        // Validate headers\n        for (const [key, value] of Object.entries(params.headers)) {\n          if (!InputValidator.isXssSafe(value)) {\n            throw new Error(`Invalid header value for ${key}`);\n          }\n          \n          headers[key] = value;\n        }\n      }\n      \n      // Build request config\n      const requestConfig = {\n        url,\n        method: endpoint.method,\n        headers,\n        params: params.query,\n        data: params.body,\n        timeout: params.timeout || connector.configuration.timeout || this.options.defaultTimeout\n      };\n      \n      // Execute request\n      const response = await axios(requestConfig);\n      \n      // Extract data using JSONPath if specified\n      let result = response.data;\n      \n      if (endpoint.response && endpoint.response.dataPath) {\n        result = jsonpath.query(response.data, endpoint.response.dataPath);\n      }\n      \n      // Update metrics\n      if (this.options.enableMetrics) {\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n        \n        this.metrics.totalRequests++;\n        this.metrics.successfulRequests++;\n        this.metrics.totalRequestTime += duration;\n        this.metrics.averageRequestTime = this.metrics.totalRequestTime / \n          (this.metrics.successfulRequests + this.metrics.failedRequests);\n      }\n      \n      // Remove from active requests\n      this.activeRequests.delete(requestId);\n      \n      return {\n        success: true,\n        data: result,\n        statusCode: response.status\n      };\n    } catch (error) {\n      // Update metrics\n      if (this.options.enableMetrics) {\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n        \n        this.metrics.totalRequests++;\n        this.metrics.failedRequests++;\n        \n        if (this.metrics.successfulRequests > 0) {\n          this.metrics.totalRequestTime += duration;\n          this.metrics.averageRequestTime = this.metrics.totalRequestTime / \n            (this.metrics.successfulRequests + this.metrics.failedRequests);\n        }\n      }\n      \n      // Remove from active requests\n      this.activeRequests.delete(requestId);\n      \n      return {\n        success: false,\n        error: error.message,\n        statusCode: error.response ? error.response.status : 500\n      };\n    }\n  }\n\n  /**\n   * Validate parameters against endpoint schema\n   * @param {Object} params - Parameters to validate\n   * @param {Object} endpoint - Endpoint definition\n   * @throws {Error} - If parameters are invalid\n   */\n  validateParameters(params, endpoint) {\n    // Validate path parameters\n    if (endpoint.parameters && endpoint.parameters.path) {\n      for (const [key, schema] of Object.entries(endpoint.parameters.path)) {\n        if (schema.required && (!params.path || params.path[key] === undefined)) {\n          throw new Error(`Required path parameter '${key}' is missing`);\n        }\n      }\n    }\n    \n    // Validate query parameters\n    if (endpoint.parameters && endpoint.parameters.query) {\n      for (const [key, schema] of Object.entries(endpoint.parameters.query)) {\n        if (schema.required && (!params.query || params.query[key] === undefined)) {\n          throw new Error(`Required query parameter '${key}' is missing`);\n        }\n      }\n    }\n    \n    // Validate body parameters\n    if (endpoint.parameters && endpoint.parameters.body && endpoint.parameters.body.required) {\n      if (!params.body) {\n        throw new Error('Request body is required');\n      }\n      \n      // Validate body against schema if available\n      if (endpoint.parameters.body.properties) {\n        const result = InputValidator.validateObject(params.body, endpoint.parameters.body);\n        if (!result.isValid) {\n          throw new Error(`Invalid request body: ${result.errors.join(', ')}`);\n        }\n      }\n    }\n  }\n\n  /**\n   * Get metrics for the connector executor\n   * @returns {Object} - Metrics\n   */\n  getMetrics() {\n    return this.metrics;\n  }\n\n  /**\n   * Get active requests\n   * @returns {Array} - Active requests\n   */\n  getActiveRequests() {\n    return Array.from(this.activeRequests);\n  }\n\n  /**\n   * Add allowed hosts to the SSRF protection whitelist\n   * @param {string|string[]} hosts - Host(s) to add\n   */\n  addAllowedHosts(hosts) {\n    this.ssrfProtection.addAllowedHosts(hosts);\n  }\n\n  /**\n   * Add allowed domains to the SSRF protection whitelist\n   * @param {string|string[]} domains - Domain(s) to add\n   */\n  addAllowedDomains(domains) {\n    this.ssrfProtection.addAllowedDomains(domains);\n  }\n}\n\nmodule.exports = SecureConnectorExecutor;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC9B,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;AACpC,MAAM;EAAEE;AAAY,CAAC,GAAGF,OAAO,CAAC,YAAY,CAAC;AAC7C,MAAM;EAAEG,EAAE,EAAEC;AAAO,CAAC,GAAGJ,OAAO,CAAC,MAAM,CAAC;AACtC,MAAM;EAAEK,cAAc;EAAEC;AAAe,CAAC,GAAGN,OAAO,CAAC,aAAa,CAAC;;AAEjE;AACA;AACA;AACA;AACA;AACA,MAAMO,uBAAuB,CAAC;EAC5BC,WAAWA,CAACC,iBAAiB,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAACD,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,OAAO,GAAG;MACbC,aAAa,EAAE,IAAI;MACnBC,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,IAAI;MAAE;MAChBC,cAAc,EAAE,KAAK;MAAE;MACvBC,qBAAqB,EAAE,EAAE;MACzB,GAAGL;IACL,CAAC;;IAED;IACA,IAAI,CAACM,cAAc,GAAG,IAAIX,cAAc,CAAC;MACvCY,gBAAgB,EAAE,CAAC,QAAQ,CAAC;MAC5BC,eAAe,EAAE;IACnB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACC,OAAO,GAAG;MACbC,aAAa,EAAE,CAAC;MAChBC,kBAAkB,EAAE,CAAC;MACrBC,cAAc,EAAE,CAAC;MACjBC,eAAe,EAAE,CAAC;MAClBC,gBAAgB,EAAE,CAAC;MACnBC,kBAAkB,EAAE;IACtB,CAAC;;IAED;IACA,IAAI,CAACC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,gBAAgBA,CAACC,WAAW,EAAEC,UAAU,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE;IAC3D,MAAMC,SAAS,GAAG,IAAI,CAACtB,OAAO,CAACC,aAAa,GAAGT,WAAW,CAAC+B,GAAG,CAAC,CAAC,GAAG,CAAC;IACpE,MAAMC,SAAS,GAAG9B,MAAM,CAAC,CAAC;IAE1B,IAAI;MACF;MACA,IAAI,IAAI,CAACsB,cAAc,CAACS,IAAI,IAAI,IAAI,CAACzB,OAAO,CAACK,qBAAqB,EAAE;QAClE,MAAM,IAAIqB,KAAK,CAAC,2CAA2C,CAAC;MAC9D;;MAEA;MACA,IAAI,CAACV,cAAc,CAACW,GAAG,CAACH,SAAS,CAAC;;MAElC;MACA,MAAMI,SAAS,GAAG,IAAI,CAAC7B,iBAAiB,CAAC8B,YAAY,CAACV,WAAW,CAAC;MAClE,IAAI,CAACS,SAAS,EAAE;QACd,MAAM,IAAIF,KAAK,CAAC,aAAaP,WAAW,YAAY,CAAC;MACvD;;MAEA;MACA,MAAMW,QAAQ,GAAGF,SAAS,CAACG,SAAS,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKd,UAAU,CAAC;MACnE,IAAI,CAACU,QAAQ,EAAE;QACb,MAAM,IAAIJ,KAAK,CAAC,YAAYN,UAAU,2BAA2BD,WAAW,EAAE,CAAC;MACjF;;MAEA;MACA,IAAI,CAACgB,kBAAkB,CAACd,MAAM,EAAES,QAAQ,CAAC;;MAEzC;MACA,IAAIM,GAAG,GAAGR,SAAS,CAACS,aAAa,CAACC,OAAO;MACzC,IAAIC,IAAI,GAAGT,QAAQ,CAACS,IAAI;;MAExB;MACA,IAAIlB,MAAM,CAACkB,IAAI,EAAE;QACf,KAAK,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACtB,MAAM,CAACkB,IAAI,CAAC,EAAE;UACtD;UACA,IAAI,CAAC3C,cAAc,CAACgD,SAAS,CAACH,KAAK,CAAC,IAAI,CAAC7C,cAAc,CAACiD,aAAa,CAACJ,KAAK,CAAC,EAAE;YAC5E,MAAM,IAAIf,KAAK,CAAC,2BAA2Bc,GAAG,EAAE,CAAC;UACnD;UAEAD,IAAI,GAAGA,IAAI,CAACO,OAAO,CAAC,IAAIN,GAAG,GAAG,EAAEO,kBAAkB,CAACN,KAAK,CAAC,CAAC;QAC5D;MACF;MAEAL,GAAG,GAAGA,GAAG,CAACY,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAGZ,GAAG,GAAGG,IAAI,CAACU,UAAU,CAAC,GAAG,CAAC,GAAGV,IAAI,CAACW,SAAS,CAAC,CAAC,CAAC,GAAGX,IAAI,EAAE,GAAG,GAAGH,GAAG,GAAGG,IAAI,CAACU,UAAU,CAAC,GAAG,CAAC,GAAGV,IAAI,GAAG,IAAIA,IAAI,EAAE,EAAE;;MAE1I;MACA,MAAMY,MAAM,GAAG,MAAM,IAAI,CAAC7C,cAAc,CAAC8C,SAAS,CAAChB,GAAG,CAAC;MACvD,IAAI,CAACe,MAAM,EAAE;QACX,IAAI,IAAI,CAACnD,OAAO,CAACC,aAAa,EAAE;UAC9B,IAAI,CAACQ,OAAO,CAACC,aAAa,EAAE;UAC5B,IAAI,CAACD,OAAO,CAACI,eAAe,EAAE;QAChC;QAEA,MAAM,IAAIa,KAAK,CAAC,mCAAmCU,GAAG,EAAE,CAAC;MAC3D;;MAEA;MACA,MAAMiB,OAAO,GAAG;QACd,GAAGzB,SAAS,CAACS,aAAa,CAACgB;MAC7B,CAAC;;MAED;MACA,IAAIzB,SAAS,CAAC0B,cAAc,CAACC,IAAI,KAAK,SAAS,EAAE;QAC/C,MAAMC,WAAW,GAAGd,MAAM,CAACe,IAAI,CAAC7B,SAAS,CAAC0B,cAAc,CAACI,MAAM,CAAC,CAAC1B,IAAI,CAAC2B,CAAC,IACrE/B,SAAS,CAAC0B,cAAc,CAACI,MAAM,CAACC,CAAC,CAAC,CAACJ,IAAI,KAAK,QAAQ,IACpD3B,SAAS,CAAC0B,cAAc,CAACI,MAAM,CAACC,CAAC,CAAC,CAACC,SAAS,KAAK,IACnD,CAAC;QAED,IAAIJ,WAAW,IAAInC,MAAM,CAACwC,IAAI,IAAIxC,MAAM,CAACwC,IAAI,CAACL,WAAW,CAAC,EAAE;UAC1DH,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUhC,MAAM,CAACwC,IAAI,CAACL,WAAW,CAAC,EAAE;QACjE;MACF,CAAC,MAAM,IAAI5B,SAAS,CAAC0B,cAAc,CAACC,IAAI,KAAK,OAAO,EAAE;QACpD,IAAIlC,MAAM,CAACwC,IAAI,IAAIxC,MAAM,CAACwC,IAAI,CAACC,QAAQ,IAAIzC,MAAM,CAACwC,IAAI,CAACE,QAAQ,EAAE;UAC/D,MAAMF,IAAI,GAAGG,MAAM,CAACC,IAAI,CAAC,GAAG5C,MAAM,CAACwC,IAAI,CAACC,QAAQ,IAAIzC,MAAM,CAACwC,IAAI,CAACE,QAAQ,EAAE,CAAC,CAACG,QAAQ,CAAC,QAAQ,CAAC;UAC9Fb,OAAO,CAAC,eAAe,CAAC,GAAG,SAASQ,IAAI,EAAE;QAC5C;MACF,CAAC,MAAM,IAAIjC,SAAS,CAAC0B,cAAc,CAACC,IAAI,KAAK,QAAQ,EAAE;QACrD,IAAIlC,MAAM,CAACwC,IAAI,IAAIxC,MAAM,CAACwC,IAAI,CAACM,KAAK,EAAE;UACpCd,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUhC,MAAM,CAACwC,IAAI,CAACM,KAAK,EAAE;QAC1D;MACF;;MAEA;MACA,IAAI9C,MAAM,CAACgC,OAAO,EAAE;QAClB;QACA,KAAK,MAAM,CAACb,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACtB,MAAM,CAACgC,OAAO,CAAC,EAAE;UACzD,IAAI,CAACzD,cAAc,CAACgD,SAAS,CAACH,KAAK,CAAC,EAAE;YACpC,MAAM,IAAIf,KAAK,CAAC,4BAA4Bc,GAAG,EAAE,CAAC;UACpD;UAEAa,OAAO,CAACb,GAAG,CAAC,GAAGC,KAAK;QACtB;MACF;;MAEA;MACA,MAAM2B,aAAa,GAAG;QACpBhC,GAAG;QACHiC,MAAM,EAAEvC,QAAQ,CAACuC,MAAM;QACvBhB,OAAO;QACPhC,MAAM,EAAEA,MAAM,CAACiD,KAAK;QACpBC,IAAI,EAAElD,MAAM,CAACmD,IAAI;QACjBC,OAAO,EAAEpD,MAAM,CAACoD,OAAO,IAAI7C,SAAS,CAACS,aAAa,CAACoC,OAAO,IAAI,IAAI,CAACzE,OAAO,CAACI;MAC7E,CAAC;;MAED;MACA,MAAMsE,QAAQ,GAAG,MAAMrF,KAAK,CAAC+E,aAAa,CAAC;;MAE3C;MACA,IAAIO,MAAM,GAAGD,QAAQ,CAACH,IAAI;MAE1B,IAAIzC,QAAQ,CAAC4C,QAAQ,IAAI5C,QAAQ,CAAC4C,QAAQ,CAACE,QAAQ,EAAE;QACnDD,MAAM,GAAGpF,QAAQ,CAAC+E,KAAK,CAACI,QAAQ,CAACH,IAAI,EAAEzC,QAAQ,CAAC4C,QAAQ,CAACE,QAAQ,CAAC;MACpE;;MAEA;MACA,IAAI,IAAI,CAAC5E,OAAO,CAACC,aAAa,EAAE;QAC9B,MAAM4E,OAAO,GAAGrF,WAAW,CAAC+B,GAAG,CAAC,CAAC;QACjC,MAAMuD,QAAQ,GAAGD,OAAO,GAAGvD,SAAS;QAEpC,IAAI,CAACb,OAAO,CAACC,aAAa,EAAE;QAC5B,IAAI,CAACD,OAAO,CAACE,kBAAkB,EAAE;QACjC,IAAI,CAACF,OAAO,CAACK,gBAAgB,IAAIgE,QAAQ;QACzC,IAAI,CAACrE,OAAO,CAACM,kBAAkB,GAAG,IAAI,CAACN,OAAO,CAACK,gBAAgB,IAC5D,IAAI,CAACL,OAAO,CAACE,kBAAkB,GAAG,IAAI,CAACF,OAAO,CAACG,cAAc,CAAC;MACnE;;MAEA;MACA,IAAI,CAACI,cAAc,CAAC+D,MAAM,CAACvD,SAAS,CAAC;MAErC,OAAO;QACLwD,OAAO,EAAE,IAAI;QACbT,IAAI,EAAEI,MAAM;QACZM,UAAU,EAAEP,QAAQ,CAACQ;MACvB,CAAC;IACH,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd;MACA,IAAI,IAAI,CAACnF,OAAO,CAACC,aAAa,EAAE;QAC9B,MAAM4E,OAAO,GAAGrF,WAAW,CAAC+B,GAAG,CAAC,CAAC;QACjC,MAAMuD,QAAQ,GAAGD,OAAO,GAAGvD,SAAS;QAEpC,IAAI,CAACb,OAAO,CAACC,aAAa,EAAE;QAC5B,IAAI,CAACD,OAAO,CAACG,cAAc,EAAE;QAE7B,IAAI,IAAI,CAACH,OAAO,CAACE,kBAAkB,GAAG,CAAC,EAAE;UACvC,IAAI,CAACF,OAAO,CAACK,gBAAgB,IAAIgE,QAAQ;UACzC,IAAI,CAACrE,OAAO,CAACM,kBAAkB,GAAG,IAAI,CAACN,OAAO,CAACK,gBAAgB,IAC5D,IAAI,CAACL,OAAO,CAACE,kBAAkB,GAAG,IAAI,CAACF,OAAO,CAACG,cAAc,CAAC;QACnE;MACF;;MAEA;MACA,IAAI,CAACI,cAAc,CAAC+D,MAAM,CAACvD,SAAS,CAAC;MAErC,OAAO;QACLwD,OAAO,EAAE,KAAK;QACdG,KAAK,EAAEA,KAAK,CAACC,OAAO;QACpBH,UAAU,EAAEE,KAAK,CAACT,QAAQ,GAAGS,KAAK,CAACT,QAAQ,CAACQ,MAAM,GAAG;MACvD,CAAC;IACH;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE/C,kBAAkBA,CAACd,MAAM,EAAES,QAAQ,EAAE;IACnC;IACA,IAAIA,QAAQ,CAACuD,UAAU,IAAIvD,QAAQ,CAACuD,UAAU,CAAC9C,IAAI,EAAE;MACnD,KAAK,MAAM,CAACC,GAAG,EAAE8C,MAAM,CAAC,IAAI5C,MAAM,CAACC,OAAO,CAACb,QAAQ,CAACuD,UAAU,CAAC9C,IAAI,CAAC,EAAE;QACpE,IAAI+C,MAAM,CAACC,QAAQ,KAAK,CAAClE,MAAM,CAACkB,IAAI,IAAIlB,MAAM,CAACkB,IAAI,CAACC,GAAG,CAAC,KAAKgD,SAAS,CAAC,EAAE;UACvE,MAAM,IAAI9D,KAAK,CAAC,4BAA4Bc,GAAG,cAAc,CAAC;QAChE;MACF;IACF;;IAEA;IACA,IAAIV,QAAQ,CAACuD,UAAU,IAAIvD,QAAQ,CAACuD,UAAU,CAACf,KAAK,EAAE;MACpD,KAAK,MAAM,CAAC9B,GAAG,EAAE8C,MAAM,CAAC,IAAI5C,MAAM,CAACC,OAAO,CAACb,QAAQ,CAACuD,UAAU,CAACf,KAAK,CAAC,EAAE;QACrE,IAAIgB,MAAM,CAACC,QAAQ,KAAK,CAAClE,MAAM,CAACiD,KAAK,IAAIjD,MAAM,CAACiD,KAAK,CAAC9B,GAAG,CAAC,KAAKgD,SAAS,CAAC,EAAE;UACzE,MAAM,IAAI9D,KAAK,CAAC,6BAA6Bc,GAAG,cAAc,CAAC;QACjE;MACF;IACF;;IAEA;IACA,IAAIV,QAAQ,CAACuD,UAAU,IAAIvD,QAAQ,CAACuD,UAAU,CAACb,IAAI,IAAI1C,QAAQ,CAACuD,UAAU,CAACb,IAAI,CAACe,QAAQ,EAAE;MACxF,IAAI,CAAClE,MAAM,CAACmD,IAAI,EAAE;QAChB,MAAM,IAAI9C,KAAK,CAAC,0BAA0B,CAAC;MAC7C;;MAEA;MACA,IAAII,QAAQ,CAACuD,UAAU,CAACb,IAAI,CAACiB,UAAU,EAAE;QACvC,MAAMd,MAAM,GAAG/E,cAAc,CAAC8F,cAAc,CAACrE,MAAM,CAACmD,IAAI,EAAE1C,QAAQ,CAACuD,UAAU,CAACb,IAAI,CAAC;QACnF,IAAI,CAACG,MAAM,CAACgB,OAAO,EAAE;UACnB,MAAM,IAAIjE,KAAK,CAAC,yBAAyBiD,MAAM,CAACiB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACtE;MACF;IACF;EACF;;EAEA;AACF;AACA;AACA;EACEC,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI,CAACrF,OAAO;EACrB;;EAEA;AACF;AACA;AACA;EACEsF,iBAAiBA,CAAA,EAAG;IAClB,OAAOC,KAAK,CAAC/B,IAAI,CAAC,IAAI,CAACjD,cAAc,CAAC;EACxC;;EAEA;AACF;AACA;AACA;EACEiF,eAAeA,CAACC,KAAK,EAAE;IACrB,IAAI,CAAC5F,cAAc,CAAC2F,eAAe,CAACC,KAAK,CAAC;EAC5C;;EAEA;AACF;AACA;AACA;EACEC,iBAAiBA,CAACC,OAAO,EAAE;IACzB,IAAI,CAAC9F,cAAc,CAAC6F,iBAAiB,CAACC,OAAO,CAAC;EAChD;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGzG,uBAAuB", "ignoreList": []}