const request = require('supertest');
const app = require('../../server');

describe('ESG Reporting API', () => {
  // Test GET /governance/esg/reports
  describe('GET /governance/esg/reports', () => {
    it('should return a list of ESG reports', async () => {
      const response = await request(app)
        .get('/governance/esg/reports')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
    
    it('should filter reports by framework', async () => {
      const response = await request(app)
        .get('/governance/esg/reports?framework=gri')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(report => report.framework === 'gri')).toBe(true);
    });
    
    it('should filter reports by status', async () => {
      const response = await request(app)
        .get('/governance/esg/reports?status=published')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(report => report.status === 'published')).toBe(true);
    });
    
    it('should paginate results', async () => {
      const response = await request(app)
        .get('/governance/esg/reports?page=1&limit=2')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeLessThanOrEqual(2);
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(2);
    });
  });
  
  // Test GET /governance/esg/reports/:id
  describe('GET /governance/esg/reports/:id', () => {
    it('should return a specific ESG report', async () => {
      const response = await request(app)
        .get('/governance/esg/reports/esg-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('esg-001');
    });
    
    it('should return 404 for non-existent report', async () => {
      const response = await request(app)
        .get('/governance/esg/reports/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test POST /governance/esg/reports
  describe('POST /governance/esg/reports', () => {
    it('should create a new ESG report', async () => {
      const newReport = {
        title: 'Test ESG Report',
        description: 'This is a test ESG report',
        framework: 'gri',
        reportingYear: 2025,
        status: 'draft',
        metrics: [
          {
            category: 'environmental',
            name: 'Test Metric',
            value: 100,
            unit: 'units'
          }
        ]
      };
      
      const response = await request(app)
        .post('/governance/esg/reports')
        .set('apikey', 'test-api-key')
        .send(newReport);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.title).toBe(newReport.title);
      expect(response.body.data.framework).toBe(newReport.framework);
      expect(response.body.data).toHaveProperty('id');
    });
    
    it('should return 400 for invalid input', async () => {
      const invalidReport = {
        // Missing required fields
        description: 'This is an invalid ESG report'
      };
      
      const response = await request(app)
        .post('/governance/esg/reports')
        .set('apikey', 'test-api-key')
        .send(invalidReport);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test PUT /governance/esg/reports/:id
  describe('PUT /governance/esg/reports/:id', () => {
    it('should update an existing ESG report', async () => {
      const updateData = {
        title: 'Updated ESG Report',
        status: 'completed'
      };
      
      const response = await request(app)
        .put('/governance/esg/reports/esg-001')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.title).toBe(updateData.title);
      expect(response.body.data.status).toBe(updateData.status);
    });
    
    it('should return 404 for non-existent report', async () => {
      const updateData = {
        title: 'Updated ESG Report'
      };
      
      const response = await request(app)
        .put('/governance/esg/reports/non-existent-id')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test DELETE /governance/esg/reports/:id
  describe('DELETE /governance/esg/reports/:id', () => {
    it('should delete an existing ESG report', async () => {
      const response = await request(app)
        .delete('/governance/esg/reports/esg-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 404 for non-existent report', async () => {
      const response = await request(app)
        .delete('/governance/esg/reports/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /governance/esg/metrics
  describe('GET /governance/esg/metrics', () => {
    it('should return ESG metrics', async () => {
      const response = await request(app)
        .get('/governance/esg/metrics')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('environmental');
      expect(response.body.data).toHaveProperty('social');
      expect(response.body.data).toHaveProperty('governance');
    });
    
    it('should filter metrics by category', async () => {
      const response = await request(app)
        .get('/governance/esg/metrics?category=environmental')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('environmental');
      expect(response.body.data).not.toHaveProperty('social');
      expect(response.body.data).not.toHaveProperty('governance');
    });
  });
  
  // Test GET /governance/esg/frameworks
  describe('GET /governance/esg/frameworks', () => {
    it('should return a list of ESG frameworks', async () => {
      const response = await request(app)
        .get('/governance/esg/frameworks')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });
  
  // Test GET /governance/esg/frameworks/:id
  describe('GET /governance/esg/frameworks/:id', () => {
    it('should return a specific ESG framework', async () => {
      const response = await request(app)
        .get('/governance/esg/frameworks/gri')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('gri');
    });
    
    it('should return 404 for non-existent framework', async () => {
      const response = await request(app)
        .get('/governance/esg/frameworks/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
});
