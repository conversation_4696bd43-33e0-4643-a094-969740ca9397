#!/bin/bash

# NovaConnect π-Coherence Test Runner
# Quick and easy way to run the π-coherence A/B test

echo "🔱 NovaConnect π-Coherence Test Runner"
echo "======================================"

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required but not installed"
    exit 1
fi

# Check if NovaConnect is running
echo "🔍 Checking if NovaConnect is running..."
NOVA_CONNECT_URL=${NOVA_CONNECT_URL:-"http://localhost:3001"}

if curl -s "$NOVA_CONNECT_URL/health" > /dev/null 2>&1; then
    echo "✅ NovaConnect is running at $NOVA_CONNECT_URL"
else
    echo "⚠️  NovaConnect not detected at $NOVA_CONNECT_URL"
    echo "   Starting test anyway (will test error handling)"
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install axios
fi

# Create test results directory
mkdir -p test-results

# Set environment variables
export NOVA_CONNECT_URL=${NOVA_CONNECT_URL:-"http://localhost:3001"}

echo ""
echo "🚀 Starting π-Coherence A/B Test..."
echo "   Target URL: $NOVA_CONNECT_URL"
echo "   Test Type: Performance comparison (Standard vs π-timing)"
echo "   Expected Duration: ~2-3 minutes"
echo ""

# Run the test
node pi-coherence-test.js

# Check exit code
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Test completed successfully!"
    echo "📊 Check the test-results/ directory for detailed reports"
    echo ""
    echo "🔍 Quick Analysis:"
    echo "   - Look for efficiency gains ≥ 2.0× (target: 3.142×)"
    echo "   - Check coherence score improvements"
    echo "   - Monitor error rate reductions"
    echo ""
    echo "📋 Next Steps:"
    echo "   1. Review detailed results in test-results/"
    echo "   2. If successful, consider production deployment"
    echo "   3. Expand testing to other NovaFuse systems"
else
    echo ""
    echo "❌ Test failed - check error messages above"
    echo "🔧 Troubleshooting:"
    echo "   - Ensure NovaConnect is running"
    echo "   - Check network connectivity"
    echo "   - Verify API endpoints are accessible"
fi
