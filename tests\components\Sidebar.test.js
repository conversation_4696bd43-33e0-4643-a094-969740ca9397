import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Sidebar from '../../components/Sidebar';

// Mock the Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn().mockImplementation(() => ({
    pathname: '/'
  }))
}));

// Mock the Next.js Link component
jest.mock('next/link', () => {
  return ({ children, href, className }) => {
    return (
      <a href={href} className={className} data-testid="mock-link">
        {children}
      </a>
    );
  };
});

describe('Sidebar', () => {
  const sidebarItems = [
    { label: 'Item 1', href: '/item1' },
    { label: 'Item 2', href: '/item2' },
    { label: 'Item 3', href: '/item3' }
  ];

  it('renders sidebar items correctly', () => {
    render(<Sidebar items={sidebarItems} />);
    
    // Check if the title is rendered
    expect(screen.getByText('Navigation')).toBeInTheDocument();
    
    // Check if all items are rendered
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
    expect(screen.getByText('Item 3')).toBeInTheDocument();
    
    // Check if links have correct hrefs
    const links = screen.getAllByTestId('mock-link');
    expect(links[0]).toHaveAttribute('href', '/item1');
    expect(links[1]).toHaveAttribute('href', '/item2');
    expect(links[2]).toHaveAttribute('href', '/item3');
  });
  
  it('renders with custom title', () => {
    render(<Sidebar items={sidebarItems} title="Custom Title" />);
    
    // Check if the custom title is rendered
    expect(screen.getByText('Custom Title')).toBeInTheDocument();
  });
  
  it('toggles sidebar visibility when collapse button is clicked', () => {
    render(<Sidebar items={sidebarItems} />);
    
    // Sidebar content should be visible initially
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    
    // Find and click the collapse button
    const collapseButton = screen.getByLabelText('Collapse sidebar');
    fireEvent.click(collapseButton);
    
    // Sidebar content should be hidden
    expect(screen.queryByText('Item 1')).not.toBeInTheDocument();
    
    // Button should now be "Expand sidebar"
    const expandButton = screen.getByLabelText('Expand sidebar');
    fireEvent.click(expandButton);
    
    // Sidebar content should be visible again
    expect(screen.getByText('Item 1')).toBeInTheDocument();
  });
  
  it('applies active styles to the current route', () => {
    // Update the mock router to return a specific path
    const useRouter = jest.requireMock('next/router').useRouter;
    useRouter.mockImplementation(() => ({
      pathname: '/item2'
    }));
    
    render(<Sidebar items={sidebarItems} />);
    
    // Get all links
    const links = screen.getAllByTestId('mock-link');
    
    // The second link should have the active class
    expect(links[1].className).toContain('text-blue-400');
    
    // Other links should not have the active class
    expect(links[0].className).not.toContain('text-blue-400');
    expect(links[2].className).not.toContain('text-blue-400');
  });
  
  it('renders nested category items correctly', () => {
    const nestedItems = [
      { 
        type: 'category',
        label: 'Category 1', 
        items: [
          { label: 'Sub Item 1', href: '/sub1' },
          { label: 'Sub Item 2', href: '/sub2' }
        ]
      },
      { label: 'Regular Item', href: '/regular' }
    ];
    
    render(<Sidebar items={nestedItems} />);
    
    // Check if the category label is rendered
    expect(screen.getByText('Category 1')).toBeInTheDocument();
    
    // Check if the sub-items are rendered
    expect(screen.getByText('Sub Item 1')).toBeInTheDocument();
    expect(screen.getByText('Sub Item 2')).toBeInTheDocument();
    
    // Check if the regular item is rendered
    expect(screen.getByText('Regular Item')).toBeInTheDocument();
    
    // Check if links have correct hrefs
    const links = screen.getAllByTestId('mock-link');
    
    // Find the sub-item links
    const subItem1Link = Array.from(links).find(link => link.textContent === 'Sub Item 1');
    const subItem2Link = Array.from(links).find(link => link.textContent === 'Sub Item 2');
    const regularItemLink = Array.from(links).find(link => link.textContent === 'Regular Item');
    
    expect(subItem1Link).toHaveAttribute('href', '/sub1');
    expect(subItem2Link).toHaveAttribute('href', '/sub2');
    expect(regularItemLink).toHaveAttribute('href', '/regular');
  });
});
