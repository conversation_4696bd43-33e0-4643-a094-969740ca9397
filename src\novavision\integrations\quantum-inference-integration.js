/**
 * NovaVision Quantum State Inference Integration
 *
 * This module integrates the Quantum State Inference Layer with NovaVision,
 * providing visualizations and UI components for quantum state inference.
 */

const { createLogger } = require('../../utils/logger');
const QuantumVisualization = require('../components/quantum-visualization');
const { NovaVisionSecurityManager, PERMISSIONS } = require('../security');

const logger = createLogger('quantum-inference-integration');

/**
 * NovaVision Quantum State Inference Integration
 */
class QuantumInferenceIntegration {
  /**
   * Create a new Quantum Inference Integration
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      theme: options.theme || 'cyber-safety',
      colorScheme: options.colorScheme || 'quantum',
      detailLevel: options.detailLevel || 'standard',
      enableSecurity: options.enableSecurity !== false,
      enableRBAC: options.enableRBAC !== false,
      ...options
    };

    // Initialize quantum visualization component
    this.quantumVisualization = new QuantumVisualization({
      theme: this.options.theme,
      colorScheme: this.options.colorScheme,
      detailLevel: this.options.detailLevel,
      animateTransitions: true,
      showProbabilities: true,
      showBayesianNetwork: true,
      showActionableIntelligence: true
    });

    // Use provided security manager or initialize a new one
    if (options.securityManager) {
      this.securityManager = options.securityManager;
      console.log('Using provided security manager');
    } else if (this.options.enableSecurity) {
      this.securityManager = new NovaVisionSecurityManager({
        enableRBAC: this.options.enableRBAC,
        enableNIST: true,
        rbacOptions: options.rbacOptions || {},
        nistOptions: options.nistOptions || {}
      });
      console.log('Created new security manager');
    }

    logger.info('Quantum Inference Integration initialized', {
      theme: this.options.theme,
      colorScheme: this.options.colorScheme,
      detailLevel: this.options.detailLevel,
      enableSecurity: this.options.enableSecurity,
      enableRBAC: this.options.enableRBAC
    });
  }

  /**
   * Generate dashboard schema for quantum inference visualization
   * @param {Object} quantumData - Quantum inference data
   * @returns {Object} - Dashboard schema
   */
  generateQuantumDashboardSchema(quantumData) {
    logger.info('Generating quantum dashboard schema');

    try {
      // Create base dashboard schema
      const schema = {
        id: 'quantum-inference-dashboard',
        type: 'dashboard',
        title: 'Quantum State Inference Dashboard',
        description: 'Visualization of quantum states and threat predictions',
        contentType: 'quantum_inference',
        theme: this.options.theme,
        sections: []
      };

      // Add overview section
      schema.sections.push(this._createOverviewSection(quantumData));

      // Add quantum visualization section
      if (quantumData && (quantumData.quantumStates || quantumData.collapsedStates)) {
        const visualizationSchema = this.quantumVisualization.generateQuantumVisualization(quantumData);
        if (visualizationSchema && visualizationSchema.sections) {
          schema.sections.push(...visualizationSchema.sections);
        }
      }

      // Add documentation section
      schema.sections.push(this._createDocumentationSection());

      return schema;
    } catch (error) {
      logger.error('Error generating quantum dashboard schema', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Render quantum inference dashboard
   * @param {Object} quantumData - Quantum inference data
   * @param {Object} options - Rendering options
   * @param {string} [options.userId] - User ID for RBAC
   * @returns {Object} - Rendered dashboard
   */
  renderQuantumDashboard(quantumData, options = {}) {
    logger.info('Rendering quantum inference dashboard', { userId: options.userId });

    try {
      // Check if user has permission to view quantum inference
      if (this.options.enableSecurity && this.securityManager && options.userId) {
        // Check direct component permission
        const hasComponentPermission = this.securityManager.validateAccess(
          options.userId,
          'quantum_inference'
        );

        // Check specific permission
        const hasSpecificPermission = this.securityManager.rbac.hasPermission(
          options.userId,
          'view:quantum_inference'
        );

        // Check if user has CISO or Security Analyst role
        const hasCISORole = this.securityManager.rbac.hasRole(options.userId, 'CISO');
        const hasAnalystRole = this.securityManager.rbac.hasRole(options.userId, 'SECURITY_ANALYST');
        const hasAdminRole = this.securityManager.rbac.hasRole(options.userId, 'ADMIN');

        // Debug role assignments
        console.log(`DEBUG - User ${options.userId} roles:`, this.securityManager.rbac.getUserRoles(options.userId));

        // Determine final access decision
        const hasPermission = hasComponentPermission || hasSpecificPermission ||
                             hasCISORole || hasAnalystRole || hasAdminRole;

        logger.info('Access check for quantum inference dashboard', {
          userId: options.userId,
          hasComponentPermission,
          hasSpecificPermission,
          hasCISORole,
          hasAnalystRole,
          hasAdminRole,
          finalDecision: hasPermission
        });

        if (!hasPermission) {
          return {
            id: 'quantum-inference-dashboard',
            type: 'dashboard',
            title: 'Access Restricted',
            description: 'You do not have permission to view the Quantum State Inference Dashboard',
            sections: [{
              id: 'access-restricted',
              title: 'Access Restricted',
              components: [{
                id: 'access-restricted-message',
                type: 'message',
                messageType: 'warning',
                title: 'Access Restricted',
                message: 'You do not have permission to view the Quantum State Inference Dashboard. Please contact your administrator for access.'
              }]
            }]
          };
        }
      }

      // Generate dashboard schema
      const dashboardSchema = this.generateQuantumDashboardSchema(quantumData);

      // Apply security if enabled
      let securedSchema = dashboardSchema;
      if (this.options.enableSecurity && this.securityManager && options.userId) {
        securedSchema = this.securityManager.secureUISchema(dashboardSchema, options.userId);
      }

      // Return secured schema
      return securedSchema;
    } catch (error) {
      logger.error('Error rendering quantum dashboard', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Create overview section
   * @param {Object} quantumData - Quantum inference data
   * @returns {Object} - Section schema
   * @private
   */
  _createOverviewSection(quantumData) {
    // Extract metrics
    const metrics = quantumData.metrics || {};
    const collapsedStates = quantumData.collapsedStates || [];
    const actionableIntelligence = quantumData.actionableIntelligence || [];

    // Create components
    const components = [];

    // Add header
    components.push({
      id: 'quantum-inference-header',
      type: 'header',
      title: 'Quantum State Inference',
      subtitle: 'Threat prediction using quantum-inspired algorithms',
      icon: 'quantum'
    });

    // Add summary metrics
    components.push({
      id: 'quantum-summary-metrics',
      type: 'metric-cards',
      title: 'Summary',
      metrics: [
        {
          id: 'quantum-states',
          title: 'Quantum States',
          value: quantumData.quantumStates ? quantumData.quantumStates.length : 0,
          icon: 'quantum'
        },
        {
          id: 'collapsed-states',
          title: 'Collapsed States',
          value: collapsedStates.length,
          icon: 'collapse'
        },
        {
          id: 'actionable-intelligence',
          title: 'Actionable Intelligence',
          value: actionableIntelligence.length,
          icon: 'intelligence'
        },
        {
          id: 'certainty-rate',
          title: 'Certainty Rate',
          value: metrics.certaintyRate ? `${(metrics.certaintyRate * 100).toFixed(2)}%` : 'N/A',
          icon: 'certainty'
        }
      ]
    });

    // Add high-priority actions if available
    if (actionableIntelligence.length > 0) {
      // Sort by priority (descending)
      const sortedActions = [...actionableIntelligence].sort(
        (a, b) => b.action.priority - a.action.priority
      );

      // Take top 3 actions
      const topActions = sortedActions.slice(0, 3);

      components.push({
        id: 'high-priority-actions',
        type: 'action-cards',
        title: 'High Priority Actions',
        actions: topActions.map(item => ({
          id: item.stateId,
          title: item.action.type,
          description: item.action.description,
          priority: item.action.priority,
          confidence: item.confidence,
          icon: this._getActionIcon(item.action.type)
        }))
      });
    }

    // Create section schema
    return {
      id: 'overview-section',
      title: 'Overview',
      description: 'Overview of quantum state inference results',
      components,
      requiredPermission: PERMISSIONS.VIEW_SUMMARY
    };
  }

  /**
   * Create documentation section
   * @returns {Object} - Section schema
   * @private
   */
  _createDocumentationSection() {
    // Create components
    const components = [];

    // Add documentation
    components.push({
      id: 'quantum-inference-documentation',
      type: 'markdown',
      title: 'Quantum State Inference Documentation',
      content: `
        # Quantum State Inference Layer

        The Quantum State Inference Layer enhances the Trinity CSDE by representing threats as quantum states in superposition, using quantum-inspired collapse functions to determine actionable intelligence, and applying Bayesian inference for threat probability calculations.

        ## Key Concepts

        ### Quantum States

        Threats are represented as quantum states with:

        - **Amplitude**: The strength of the threat signal
        - **Phase**: The type or category of the threat
        - **Probability**: The square of the amplitude (|amplitude|²)
        - **Entropy**: The uncertainty associated with the threat

        ### Superposition

        Threats exist in superposition until they are observed or measured. This allows the system to represent multiple potential threat scenarios simultaneously.

        ### Collapse

        When a threat's entropy falls below a certain threshold, the quantum state collapses into a definite state, generating actionable intelligence.

        ### Bayesian Inference

        Bayesian inference is used to calculate the probability of threats based on prior knowledge and new evidence.

        ## Integration with Trinity CSDE

        The Quantum State Inference Layer integrates with the Trinity CSDE formula:

        CSDE_Trinity = πG + ϕD + (ℏ+c^-1)R

        Where:
        - G: Governance logic (Father)
        - D: Detection engine (Son)
        - R: Response logic (Spirit)

        The Quantum State Inference Layer enhances the Detection (D) component by providing more accurate threat predictions.
      `,
      requiredPermission: PERMISSIONS.VIEW_DETAILED
    });

    // Create section schema
    return {
      id: 'documentation-section',
      title: 'Documentation',
      description: 'Documentation for the Quantum State Inference Layer',
      components,
      requiredPermission: PERMISSIONS.VIEW_DETAILED
    };
  }

  /**
   * Get action icon based on action type
   * @param {string} actionType - Action type
   * @returns {string} - Icon name
   * @private
   */
  _getActionIcon(actionType) {
    switch (actionType) {
      case 'enhance_detection':
        return 'enhance';
      case 'escalate':
        return 'escalate';
      case 'adjust_baseline':
        return 'adjust';
      case 'mitigate_threat':
        return 'mitigate';
      case 'investigate':
        return 'investigate';
      default:
        return 'action';
    }
  }
}

module.exports = QuantumInferenceIntegration;
