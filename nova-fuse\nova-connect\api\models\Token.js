/**
 * NovaFuse Universal API Connector Token Model
 * 
 * This model defines the schema for authentication tokens in the NovaConnect UAC.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const tokenSchema = new Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  token: { 
    type: String, 
    required: true 
  },
  type: { 
    type: String, 
    enum: ['access', 'refresh', 'reset', 'verification'], 
    required: true 
  },
  expiresAt: { 
    type: Date, 
    required: true 
  },
  revokedAt: { 
    type: Date 
  },
  revokedReason: { 
    type: String 
  },
  clientInfo: {
    ip: { type: String },
    userAgent: { type: String },
    device: { type: String },
    os: { type: String },
    browser: { type: String }
  },
  metadata: { 
    type: Object 
  }
}, {
  timestamps: true
});

// Add index for token expiration
tokenSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Add index for token lookup
tokenSchema.index({ token: 1 });

// Add index for user tokens
tokenSchema.index({ userId: 1, type: 1 });

// Add index for revoked tokens
tokenSchema.index({ userId: 1, type: 1, revokedAt: 1 });

// Add method to check if token is expired
tokenSchema.methods.isExpired = function() {
  return this.expiresAt < new Date();
};

// Add method to check if token is revoked
tokenSchema.methods.isRevoked = function() {
  return !!this.revokedAt;
};

// Add method to check if token is valid
tokenSchema.methods.isValid = function() {
  return !this.isExpired() && !this.isRevoked();
};

// Add method to revoke token
tokenSchema.methods.revoke = function(reason = 'Revoked') {
  this.revokedAt = new Date();
  this.revokedReason = reason;
  return this.save();
};

// Create model
const Token = mongoose.model('Token', tokenSchema);

module.exports = Token;
