/**
 * NovaGateway Configuration
 * 
 * This file contains the configuration for the NovaGateway API Gateway.
 */

require('dotenv').config();

const config = {
  // Server configuration
  server: {
    port: process.env.PORT || 3000,
    env: process.env.NODE_ENV || 'development',
    cors: {
      origin: process.env.CORS_ORIGIN || '*',
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
    }
  },
  
  // Authentication configuration
  auth: {
    jwt: {
      secret: process.env.JWT_SECRET || 'your-secret-key-change-in-production',
      expiresIn: process.env.JWT_EXPIRES_IN || '1h'
    },
    apiKey: {
      header: process.env.API_KEY_HEADER || 'X-API-Key'
    }
  },
  
  // Rate limiting configuration
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: process.env.RATE_LIMIT_MAX || 100, // limit each IP to 100 requests per windowMs
    standardHeaders: true,
    legacyHeaders: false,
    message: {
      error: 'Too Many Requests',
      message: 'You have exceeded the rate limit. Please try again later.'
    }
  },
  
  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json',
    directory: process.env.LOG_DIRECTORY || 'logs'
  },
  
  // Service configuration
  services: {
    // NovaConnect API
    novaconnect: {
      url: process.env.NOVACONNECT_URL || 'http://localhost:3001',
      path: '/api/novaconnect'
    },
    
    // Privacy Management API
    privacyManagement: {
      url: process.env.PRIVACY_MANAGEMENT_URL || 'http://localhost:3002',
      path: '/api/privacy/management'
    },
    
    // Regulatory Compliance API
    regulatoryCompliance: {
      url: process.env.REGULATORY_COMPLIANCE_URL || 'http://localhost:3003',
      path: '/api/compliance'
    },
    
    // Security Assessment API
    securityAssessment: {
      url: process.env.SECURITY_ASSESSMENT_URL || 'http://localhost:3004',
      path: '/api/security/assessment'
    },
    
    // Control Testing API
    controlTesting: {
      url: process.env.CONTROL_TESTING_URL || 'http://localhost:3005',
      path: '/api/control/testing'
    },
    
    // ESG API
    esg: {
      url: process.env.ESG_URL || 'http://localhost:3006',
      path: '/api/esg'
    },
    
    // Compliance Automation API
    complianceAutomation: {
      url: process.env.COMPLIANCE_AUTOMATION_URL || 'http://localhost:3007',
      path: '/api/compliance/automation'
    }
  },
  
  // Health check configuration
  healthCheck: {
    path: '/health',
    includeServices: true
  },
  
  // Documentation configuration
  documentation: {
    path: '/api-docs',
    title: 'NovaGateway API Documentation',
    version: '1.0.0',
    description: 'API documentation for the NovaGateway API Gateway'
  }
};

module.exports = config;
