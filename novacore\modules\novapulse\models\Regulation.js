/**
 * NovaCore Regulation Model
 * 
 * This model defines the schema for regulations in the NovaPulse module.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define citation schema
const citationSchema = new Schema({
  text: { 
    type: String, 
    required: true, 
    trim: true 
  },
  source: { 
    type: String, 
    required: true, 
    trim: true 
  },
  url: { 
    type: String, 
    trim: true 
  },
  page: { 
    type: String, 
    trim: true 
  },
  section: { 
    type: String, 
    trim: true 
  }
}, { _id: false });

// Define requirement schema
const requirementSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  title: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    required: true, 
    trim: true 
  },
  text: { 
    type: String, 
    required: true 
  },
  section: { 
    type: String, 
    trim: true 
  },
  article: { 
    type: String, 
    trim: true 
  },
  paragraph: { 
    type: String, 
    trim: true 
  },
  citations: [citationSchema],
  applicability: {
    industries: [{ 
      type: String, 
      trim: true 
    }],
    regions: [{ 
      type: String, 
      trim: true 
    }],
    organizationTypes: [{ 
      type: String, 
      trim: true 
    }],
    dataTypes: [{ 
      type: String, 
      trim: true 
    }]
  },
  controlMappings: [{
    frameworkId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Framework' 
    },
    controlId: { 
      type: String, 
      trim: true 
    }
  }],
  tags: [{ 
    type: String, 
    trim: true 
  }],
  metadata: {
    type: Map,
    of: Schema.Types.Mixed
  }
}, { _id: false });

// Define change schema
const changeSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  type: { 
    type: String, 
    enum: ['addition', 'modification', 'removal', 'clarification'], 
    required: true 
  },
  description: { 
    type: String, 
    required: true, 
    trim: true 
  },
  requirementId: { 
    type: String, 
    trim: true 
  },
  previousText: { 
    type: String 
  },
  newText: { 
    type: String 
  },
  effectiveDate: { 
    type: Date 
  },
  source: { 
    type: String, 
    trim: true 
  },
  sourceUrl: { 
    type: String, 
    trim: true 
  },
  impact: { 
    type: String, 
    enum: ['low', 'medium', 'high', 'critical'], 
    default: 'medium' 
  },
  affectedControls: [{
    frameworkId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Framework' 
    },
    controlId: { 
      type: String, 
      trim: true 
    }
  }]
}, { _id: false });

// Define version schema
const versionSchema = new Schema({
  versionNumber: { 
    type: String, 
    required: true, 
    trim: true 
  },
  name: { 
    type: String, 
    trim: true 
  },
  releaseDate: { 
    type: Date, 
    required: true 
  },
  effectiveDate: { 
    type: Date 
  },
  endDate: { 
    type: Date 
  },
  status: { 
    type: String, 
    enum: ['draft', 'published', 'effective', 'superseded', 'archived'], 
    default: 'draft' 
  },
  changes: [changeSchema],
  sourceUrl: { 
    type: String, 
    trim: true 
  },
  documentUrl: { 
    type: String, 
    trim: true 
  }
}, { _id: false });

// Define regulation schema
const regulationSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  shortName: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    required: true 
  },
  type: { 
    type: String, 
    enum: ['law', 'regulation', 'directive', 'standard', 'framework', 'guideline'], 
    required: true 
  },
  jurisdiction: {
    country: { 
      type: String, 
      trim: true 
    },
    region: { 
      type: String, 
      trim: true 
    },
    state: { 
      type: String, 
      trim: true 
    },
    isGlobal: { 
      type: Boolean, 
      default: false 
    }
  },
  category: { 
    type: String, 
    enum: [
      'privacy', 
      'security', 
      'financial', 
      'healthcare', 
      'environmental', 
      'industry-specific', 
      'general'
    ], 
    required: true 
  },
  authority: {
    name: { 
      type: String, 
      required: true, 
      trim: true 
    },
    website: { 
      type: String, 
      trim: true 
    },
    contactInfo: { 
      type: String, 
      trim: true 
    }
  },
  currentVersion: { 
    type: String, 
    trim: true 
  },
  versions: [versionSchema],
  requirements: [requirementSchema],
  applicability: {
    industries: [{ 
      type: String, 
      trim: true 
    }],
    regions: [{ 
      type: String, 
      trim: true 
    }],
    organizationTypes: [{ 
      type: String, 
      trim: true 
    }],
    dataTypes: [{ 
      type: String, 
      trim: true 
    }],
    thresholds: [{
      type: { 
        type: String, 
        trim: true 
      },
      value: { 
        type: Schema.Types.Mixed 
      },
      description: { 
        type: String, 
        trim: true 
      }
    }]
  },
  relatedRegulations: [{
    regulationId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Regulation' 
    },
    relationship: { 
      type: String, 
      enum: ['supersedes', 'supplements', 'implements', 'conflicts'], 
      required: true 
    },
    description: { 
      type: String, 
      trim: true 
    }
  }],
  tags: [{ 
    type: String, 
    trim: true 
  }],
  status: { 
    type: String, 
    enum: ['active', 'pending', 'superseded', 'repealed'], 
    default: 'active' 
  },
  metadata: {
    type: Map,
    of: Schema.Types.Mixed
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
regulationSchema.index({ name: 1 });
regulationSchema.index({ shortName: 1 });
regulationSchema.index({ type: 1 });
regulationSchema.index({ 'jurisdiction.country': 1 });
regulationSchema.index({ 'jurisdiction.region': 1 });
regulationSchema.index({ category: 1 });
regulationSchema.index({ status: 1 });
regulationSchema.index({ 'applicability.industries': 1 });
regulationSchema.index({ 'applicability.regions': 1 });
regulationSchema.index({ 'applicability.organizationTypes': 1 });
regulationSchema.index({ 'applicability.dataTypes': 1 });
regulationSchema.index({ tags: 1 });
regulationSchema.index({ 'requirements.id': 1 });
regulationSchema.index({ 'versions.versionNumber': 1 });
regulationSchema.index({ 'versions.status': 1 });
regulationSchema.index({ createdAt: 1 });
regulationSchema.index({ updatedAt: 1 });

// Add methods
regulationSchema.methods.getCurrentVersion = function() {
  if (!this.currentVersion || !this.versions || this.versions.length === 0) {
    return null;
  }
  
  return this.versions.find(version => version.versionNumber === this.currentVersion);
};

regulationSchema.methods.getRequirement = function(requirementId) {
  if (!this.requirements || this.requirements.length === 0) {
    return null;
  }
  
  return this.requirements.find(req => req.id === requirementId);
};

regulationSchema.methods.getLatestVersion = function() {
  if (!this.versions || this.versions.length === 0) {
    return null;
  }
  
  return this.versions.sort((a, b) => new Date(b.releaseDate) - new Date(a.releaseDate))[0];
};

regulationSchema.methods.getEffectiveVersion = function(date = new Date()) {
  if (!this.versions || this.versions.length === 0) {
    return null;
  }
  
  return this.versions.find(version => {
    const effectiveDate = version.effectiveDate || version.releaseDate;
    const endDate = version.endDate || new Date('9999-12-31');
    
    return date >= effectiveDate && date <= endDate;
  });
};

regulationSchema.methods.isApplicable = function(criteria) {
  if (!criteria) {
    return true;
  }
  
  const { industry, region, organizationType, dataTypes } = criteria;
  
  // Check industry applicability
  if (industry && this.applicability.industries && this.applicability.industries.length > 0) {
    if (!this.applicability.industries.includes(industry)) {
      return false;
    }
  }
  
  // Check region applicability
  if (region && this.applicability.regions && this.applicability.regions.length > 0) {
    if (!this.applicability.regions.includes(region) && !this.jurisdiction.isGlobal) {
      return false;
    }
  }
  
  // Check organization type applicability
  if (organizationType && this.applicability.organizationTypes && this.applicability.organizationTypes.length > 0) {
    if (!this.applicability.organizationTypes.includes(organizationType)) {
      return false;
    }
  }
  
  // Check data type applicability
  if (dataTypes && dataTypes.length > 0 && this.applicability.dataTypes && this.applicability.dataTypes.length > 0) {
    const hasMatchingDataType = dataTypes.some(dataType => 
      this.applicability.dataTypes.includes(dataType)
    );
    
    if (!hasMatchingDataType) {
      return false;
    }
  }
  
  return true;
};

// Add statics
regulationSchema.statics.findByJurisdiction = function(country, region) {
  const query = {};
  
  if (country) {
    query['$or'] = [
      { 'jurisdiction.country': country },
      { 'jurisdiction.isGlobal': true }
    ];
  }
  
  if (region) {
    query['$or'] = [
      { 'jurisdiction.region': region },
      { 'jurisdiction.isGlobal': true }
    ];
  }
  
  return this.find(query);
};

regulationSchema.statics.findByCategory = function(category) {
  return this.find({ category });
};

regulationSchema.statics.findByApplicability = function(criteria) {
  const query = { status: 'active' };
  
  if (criteria.industry) {
    query['applicability.industries'] = criteria.industry;
  }
  
  if (criteria.region) {
    query['$or'] = [
      { 'applicability.regions': criteria.region },
      { 'jurisdiction.isGlobal': true }
    ];
  }
  
  if (criteria.organizationType) {
    query['applicability.organizationTypes'] = criteria.organizationType;
  }
  
  if (criteria.dataTypes && criteria.dataTypes.length > 0) {
    query['applicability.dataTypes'] = { $in: criteria.dataTypes };
  }
  
  return this.find(query);
};

regulationSchema.statics.findByRequirementId = function(requirementId) {
  return this.find({ 'requirements.id': requirementId });
};

regulationSchema.statics.findByControlMapping = function(frameworkId, controlId) {
  return this.find({
    'requirements.controlMappings': {
      $elemMatch: {
        frameworkId,
        controlId
      }
    }
  });
};

// Create model
const Regulation = mongoose.model('Regulation', regulationSchema);

module.exports = Regulation;
