"""
Demo script for NovaProof (NUCE) - Universal Compliance Evidence System.

This script demonstrates how to use NovaProof to collect, verify, and manage compliance evidence.
"""

import os
import sys
import json
import uuid
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import NovaTrack and NovaProof components
from novatrack import TrackingManager
from novaproof import (
    EvidenceManager,
    VerificationManager,
    AuditTrailManager,
    BlockchainManager,
    ReportingManager
)

def create_sample_evidence():
    """Create sample evidence for demonstration purposes."""
    logger.info("Creating sample evidence")

    # Initialize the Tracking Manager
    tracking_manager = TrackingManager()

    # Create sample requirements
    requirements = []

    # GDPR requirements
    requirements.append(tracking_manager.create_requirement({
        'name': 'Data Subject Rights',
        'description': 'Implement processes for handling data subject rights requests',
        'framework': 'GDPR',
        'category': 'privacy',
        'priority': 'high',
        'status': 'in_progress',
        'due_date': (datetime.now() + timedelta(days=15)).isoformat(),
        'assigned_to': 'privacy_officer',
        'tags': ['gdpr', 'data_subject_rights', 'privacy']
    }))

    # SOC 2 requirements
    requirements.append(tracking_manager.create_requirement({
        'name': 'Access Control',
        'description': 'Implement access controls to restrict access to information assets',
        'framework': 'SOC 2',
        'category': 'access_control',
        'priority': 'high',
        'status': 'completed',
        'due_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'assigned_to': 'security_officer',
        'tags': ['soc2', 'access_control', 'security']
    }))

    # Create sample evidence
    evidence = []

    # Evidence for Data Subject Rights
    evidence.append({
        'id': f"evidence-{uuid.uuid4()}",
        'name': 'Data Subject Rights Process Documentation',
        'description': 'Documentation of the process for handling data subject rights requests',
        'requirement_id': requirements[0]['id'],
        'type': 'document',
        'format': 'pdf',
        'location': 'https://example.com/dsr-process.pdf',
        'hash': 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855',
        'collected_at': datetime.now().isoformat(),
        'collected_by': 'privacy_officer',
        'verified': False,
        'verification_status': 'pending',
        'tags': ['gdpr', 'data_subject_rights', 'process', 'documentation']
    })

    evidence.append({
        'id': f"evidence-{uuid.uuid4()}",
        'name': 'Data Subject Rights Request Form',
        'description': 'Form used for data subject rights requests',
        'requirement_id': requirements[0]['id'],
        'type': 'document',
        'format': 'pdf',
        'location': 'https://example.com/dsr-form.pdf',
        'hash': 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2',
        'collected_at': datetime.now().isoformat(),
        'collected_by': 'privacy_officer',
        'verified': False,
        'verification_status': 'pending',
        'tags': ['gdpr', 'data_subject_rights', 'form']
    })

    # Evidence for Access Control
    evidence.append({
        'id': f"evidence-{uuid.uuid4()}",
        'name': 'Access Control Policy',
        'description': 'Policy document for access control',
        'requirement_id': requirements[1]['id'],
        'type': 'document',
        'format': 'pdf',
        'location': 'https://example.com/access-control-policy.pdf',
        'hash': 'f1e2d3c4b5a6978685746352413f2e1d0c9b8a7654321f0e1d2c3b4a5968778',
        'collected_at': datetime.now().isoformat(),
        'collected_by': 'security_officer',
        'verified': True,
        'verification_status': 'verified',
        'verified_at': datetime.now().isoformat(),
        'verified_by': 'auditor',
        'tags': ['soc2', 'access_control', 'policy', 'documentation']
    })

    evidence.append({
        'id': f"evidence-{uuid.uuid4()}",
        'name': 'User Access Review',
        'description': 'Documentation of user access review',
        'requirement_id': requirements[1]['id'],
        'type': 'document',
        'format': 'xlsx',
        'location': 'https://example.com/user-access-review.xlsx',
        'hash': 'a9b8c7d6e5f4g3h2i1j0k9l8m7n6o5p4q3r2s1t0u9v8w7x6y5z4a3b2c1d0e9',
        'collected_at': datetime.now().isoformat(),
        'collected_by': 'security_officer',
        'verified': True,
        'verification_status': 'verified',
        'verified_at': datetime.now().isoformat(),
        'verified_by': 'auditor',
        'tags': ['soc2', 'access_control', 'review', 'documentation']
    })

    return requirements, evidence

def demonstrate_evidence_management():
    """Demonstrate NovaProof (NUCE) - Universal Compliance Evidence System."""
    logger.info("Demonstrating NovaProof (NUCE) - Universal Compliance Evidence System")

    # Initialize the output directory
    output_dir = os.path.join(os.path.dirname(__file__), 'evidence_output')
    os.makedirs(output_dir, exist_ok=True)

    # Step 1: Initialize the Evidence Manager
    logger.info("Step 1: Initializing the Evidence Manager")

    evidence_manager = EvidenceManager(
        data_dir=output_dir
    )

    # Step 2: Initialize the Verification Manager
    logger.info("Step 2: Initializing the Verification Manager")

    verification_manager = VerificationManager(
        evidence_manager=evidence_manager
    )

    # Step 3: Initialize the Audit Trail Manager
    logger.info("Step 3: Initializing the Audit Trail Manager")

    audit_trail_manager = AuditTrailManager(
        evidence_manager=evidence_manager,
        verification_manager=verification_manager
    )

    # Step 4: Initialize the Blockchain Manager
    logger.info("Step 4: Initializing the Blockchain Manager")

    blockchain_manager = BlockchainManager(
        audit_trail_manager=audit_trail_manager
    )

    # Step 5: Initialize the Reporting Manager
    logger.info("Step 5: Initializing the Reporting Manager")

    reporting_manager = ReportingManager(
        evidence_manager=evidence_manager,
        verification_manager=verification_manager,
        audit_trail_manager=audit_trail_manager
    )

    # Step 6: Create sample evidence
    logger.info("Step 6: Creating sample evidence")

    requirements, evidence_items = create_sample_evidence()

    # Step 7: Register evidence
    logger.info("Step 7: Registering evidence")

    registered_evidence = []

    for evidence_item in evidence_items:
        registered_item = evidence_manager.register_evidence(evidence_item)
        registered_evidence.append(registered_item)

    # Save registered evidence to a file
    with open(os.path.join(output_dir, 'registered_evidence.json'), 'w', encoding='utf-8') as f:
        json.dump(registered_evidence, f, indent=2)

    # Step 8: Verify evidence
    logger.info("Step 8: Verifying evidence")

    verified_evidence = []

    for evidence_item in registered_evidence:
        if evidence_item['verification_status'] == 'pending':
            verified_item = verification_manager.verify_evidence(
                evidence_item['id'],
                {
                    'verified_by': 'auditor',
                    'verified_at': datetime.now().isoformat(),
                    'verification_method': 'manual',
                    'verification_notes': 'Evidence verified manually by auditor'
                }
            )
            verified_evidence.append(verified_item)

    # Save verified evidence to a file
    with open(os.path.join(output_dir, 'verified_evidence.json'), 'w', encoding='utf-8') as f:
        json.dump(verified_evidence, f, indent=2)

    # Step 9: Get audit trail
    logger.info("Step 9: Getting audit trail")

    audit_trail = audit_trail_manager.get_audit_trail()

    # Save audit trail to a file
    with open(os.path.join(output_dir, 'audit_trail.json'), 'w', encoding='utf-8') as f:
        json.dump(audit_trail, f, indent=2)

    # Step 10: Record audit trail on blockchain
    logger.info("Step 10: Recording audit trail on blockchain")

    blockchain_record = blockchain_manager.record_audit_trail(audit_trail)

    # Save blockchain record to a file
    with open(os.path.join(output_dir, 'blockchain_record.json'), 'w', encoding='utf-8') as f:
        json.dump(blockchain_record, f, indent=2)

    # Step 11: Generate evidence report
    logger.info("Step 11: Generating evidence report")

    evidence_report = reporting_manager.generate_evidence_report(
        requirements[0]['id'],
        {
            'report_type': 'requirement',
            'include_verification_details': True,
            'include_audit_trail': True
        }
    )

    # Save evidence report to a file
    with open(os.path.join(output_dir, 'evidence_report.json'), 'w', encoding='utf-8') as f:
        json.dump(evidence_report, f, indent=2)

    # Step 12: Generate a summary report
    logger.info("Step 12: Generating a summary report")

    summary_report = {
        'timestamp': datetime.now().isoformat(),
        'requirements': len(requirements),
        'evidence_items': len(registered_evidence),
        'verified_items': len(verified_evidence),
        'audit_trail_entries': len(audit_trail),
        'blockchain_records': 1,
        'output_directory': output_dir
    }

    with open(os.path.join(output_dir, 'summary_report.json'), 'w', encoding='utf-8') as f:
        json.dump(summary_report, f, indent=2)

    logger.info("Evidence management demonstration completed")
    logger.info(f"Output files saved to: {output_dir}")

    return summary_report

def main():
    """Main function."""
    logger.info("Starting NovaProof (NUCE) - Universal Compliance Evidence System demo")

    try:
        # Demonstrate the evidence management system
        summary_report = demonstrate_evidence_management()

        logger.info("NovaProof demo completed successfully")
        logger.info(f"Summary report: {summary_report}")
        logger.info(f"All output files are in: {os.path.join(os.path.dirname(os.path.abspath(__file__)), 'evidence_output')}")

    except Exception as e:
        logger.error(f"Demo failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
