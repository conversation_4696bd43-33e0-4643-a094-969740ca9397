/**
 * NovaCore SOC 2 Module
 * 
 * This module provides SOC 2 compliance automation functionality.
 */

const routes = require('./routes');
const { SOC2Controller } = require('./controllers');
const { 
  SOC2EvidenceService, 
  SOC2ControlService, 
  SOC2AssessmentService 
} = require('./services');
const { 
  SOC2Evidence, 
  SOC2Control, 
  SOC2ControlImplementation, 
  SOC2Assessment 
} = require('./models');

module.exports = {
  routes,
  controllers: {
    SOC2Controller
  },
  services: {
    SOC2EvidenceService,
    SOC2ControlService,
    SOC2AssessmentService
  },
  models: {
    SOC2Evidence,
    SOC2Control,
    SOC2ControlImplementation,
    SOC2Assessment
  }
};
