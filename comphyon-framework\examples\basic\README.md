# Basic Integration Examples

This directory contains basic examples of integrating the various components of the ComphyonΨᶜ Framework.

## Examples

### 1. Simple Integration

[simple_integration.py](simple_integration.py) demonstrates the basic integration of the ComphyonΨᶜ Meter and ComphyonΨᶜ Governor.

Key concepts demonstrated:
- Initializing the ComphyonΨᶜ Meter
- Initializing the ComphyonΨᶜ Governor
- Setting up thresholds
- Monitoring ComphyonΨᶜ metrics
- Applying control actions when thresholds are exceeded

### 2. Basic Monitoring

[basic_monitoring.py](basic_monitoring.py) demonstrates how to set up a basic monitoring system using the ComphyonΨᶜ Meter.

Key concepts demonstrated:
- Collecting data from system components
- Calculating ComphyonΨᶜ metrics
- Visualizing metrics in real-time
- Storing metrics history

## Getting Started

To run these examples, you'll need to have the ComphyonΨᶜ Meter and ComphyonΨᶜ Governor packages installed:

```bash
# Clone the repositories
git clone https://github.com/Dartan1983/comphyon-meter.git
git clone https://github.com/Dartan1983/comphyon-governor.git

# Install the packages
cd comphyon-meter
pip install -e .
cd ../comphyon-governor
pip install -e .
```

Then you can run the examples:

```bash
cd comphyon-framework/examples/basic
python simple_integration.py
```
