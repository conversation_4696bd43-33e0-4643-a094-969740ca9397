# Role-Based Access Control (RBAC)

NovaConnect implements a comprehensive role-based access control (RBAC) system that enables fine-grained control over who can access what resources and perform what actions.

## Overview

The RBAC system consists of the following components:

- **Users**: Individuals who access NovaConnect
- **Roles**: Collections of permissions assigned to users
- **Permissions**: Authorizations to perform specific actions on specific resources
- **Resources**: Objects that can be accessed or manipulated
- **Actions**: Operations that can be performed on resources

## Default Roles

NovaConnect comes with the following default roles:

### Administrator

The Administrator role has full access to all resources and actions in NovaConnect.

**Permissions**:
- `*` (wildcard permission that grants access to all resources and actions)

### Manager

The Manager role has access to most resources and actions, but cannot manage users or system settings.

**Permissions**:
- `connector:*` (all connector operations)
- `workflow:*` (all workflow operations)
- `normalization:*` (all normalization operations)
- `monitoring:*` (all monitoring operations)
- `team:view` (view teams)
- `team:edit` (edit teams)
- `user:view` (view users)

### User

The User role has access to connectors, data normalization, and workflows.

**Permissions**:
- `connector:view` (view connectors)
- `connector:use` (use connectors)
- `workflow:view` (view workflows)
- `workflow:use` (use workflows)
- `normalization:view` (view normalization configurations)
- `normalization:use` (use normalization)
- `monitoring:view` (view monitoring data)

### Viewer

The Viewer role has read-only access to connectors, data normalization, and workflows.

**Permissions**:
- `connector:view` (view connectors)
- `workflow:view` (view workflows)
- `normalization:view` (view normalization configurations)
- `monitoring:view` (view monitoring data)

## Custom Roles

You can create custom roles with specific permissions to meet your organization's needs.

### Creating a Custom Role

You can create a custom role using the API or the UI.

#### Using the API

```javascript
const response = await fetch('/api/rbac/roles', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    name: 'Custom Role',
    description: 'A custom role with specific permissions',
    permissions: [
      'connector:view',
      'connector:use',
      'workflow:view',
      'workflow:use'
    ]
  })
});

const role = await response.json();
```

#### Using GraphQL

```graphql
mutation CreateRole {
  createRole(input: {
    name: "Custom Role",
    description: "A custom role with specific permissions",
    permissions: [
      "connector:view",
      "connector:use",
      "workflow:view",
      "workflow:use"
    ]
  }) {
    id
    name
    description
    permissions
  }
}
```

#### Using the UI

1. Go to the "Roles" page
2. Click "Create Role"
3. Enter a name and description
4. Select the permissions
5. Click "Create"

### Assigning a Role to a User

You can assign a role to a user using the API or the UI.

#### Using the API

```javascript
const response = await fetch(`/api/rbac/users/${userId}/roles`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    roleId: 'role-123'
  })
});

const result = await response.json();
```

#### Using GraphQL

```graphql
mutation AssignRoleToUser {
  assignRoleToUser(userId: "user-123", roleId: "role-123")
}
```

#### Using the UI

1. Go to the "Users" page
2. Select a user
3. Click "Assign Role"
4. Select a role
5. Click "Assign"

## Permissions

Permissions in NovaConnect follow a resource-action pattern:

```
resource:action
```

For example:
- `connector:view`: Permission to view connectors
- `workflow:create`: Permission to create workflows
- `user:delete`: Permission to delete users

### Wildcard Permissions

You can use wildcards in permissions:

- `*`: All resources and actions
- `connector:*`: All actions on connectors
- `*:view`: View action on all resources

### Available Resources

- `connector`: API connectors
- `workflow`: Workflows
- `normalization`: Data normalization
- `monitoring`: Monitoring
- `user`: Users
- `team`: Teams
- `admin`: Administrative functions

### Available Actions

- `view`: View a resource
- `create`: Create a resource
- `edit`: Edit a resource
- `delete`: Delete a resource
- `use`: Use a resource
- `configure`: Configure a resource
- `system`: System-level actions

## Checking Permissions

You can check if a user has a specific permission using the API or the UI.

### Using the API

```javascript
const response = await fetch(`/api/rbac/users/${userId}/permissions/${permissionId}`, {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const result = await response.json();
```

### Using GraphQL

```graphql
query HasPermission {
  hasPermission(userId: "user-123", permissionId: "connector:view")
}
```

### Using the UI

1. Go to the "Users" page
2. Select a user
3. Click "Permissions"
4. View the user's permissions

## Best Practices

### Principle of Least Privilege

Assign users the minimum permissions they need to perform their job functions.

### Role Hierarchy

Create a hierarchy of roles with increasing levels of access.

### Regular Auditing

Regularly audit user roles and permissions to ensure they are appropriate.

### Separation of Duties

Ensure that sensitive operations require multiple users with different roles.

## API Reference

### REST API

- `GET /api/rbac/roles`: Get all roles
- `GET /api/rbac/roles/:id`: Get a role by ID
- `POST /api/rbac/roles`: Create a new role
- `PUT /api/rbac/roles/:id`: Update a role
- `DELETE /api/rbac/roles/:id`: Delete a role
- `GET /api/rbac/permissions`: Get all permissions
- `GET /api/rbac/permissions/:id`: Get a permission by ID
- `POST /api/rbac/permissions`: Create a new permission
- `PUT /api/rbac/permissions/:id`: Update a permission
- `DELETE /api/rbac/permissions/:id`: Delete a permission
- `GET /api/rbac/users/:userId/roles`: Get user roles
- `POST /api/rbac/users/:userId/roles`: Assign role to user
- `DELETE /api/rbac/users/:userId/roles/:roleId`: Remove role from user
- `GET /api/rbac/users/:userId/permissions`: Get user permissions
- `GET /api/rbac/users/:userId/permissions/:permissionId`: Check if user has permission

### GraphQL API

See the [GraphQL API Reference](GRAPHQL_API_REFERENCE.md) for RBAC-related queries and mutations.
