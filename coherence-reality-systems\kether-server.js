const express = require('express');
const cors = require('cors');
const crypto = require('crypto');
const BN = require('bn.js');

const app = express();
app.use(cors());
app.use(express.json());

// In-memory stores
const aetheriumAccounts = new Map();
const pendingTransactions = [];

// Initialize genesis account
aetheriumAccounts.set('******************************************', {
  balance: new BN('**********000000000000000'), // 1M AE (18 decimals)
  nonce: 0
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    accounts: aetheriumAccounts.size,
    pendingTxs: pendingTransactions.length
  });
});

// Aetherium faucet endpoint
app.post('/aetherium/faucet', express.json(), (req, res) => {
  try {
    const { address } = req.body;
    
    if (!address || typeof address !== 'string' || !address.startsWith('0x')) {
      return res.status(400).json({ error: 'Invalid address format' });
    }
    
    // Initialize account if it doesn't exist
    if (!aetheriumAccounts.has(address)) {
      aetheriumAccounts.set(address, {
        balance: new BN(0),
        nonce: 0
      });
    }
    
    // Fund the account (1 AE = 1e18 wei)
    const amount = new BN('**********000000000'); // 1 AE
    aetheriumAccounts.get(address).balance.iadd(amount);
    
    // Deduct from genesis
    aetheriumAccounts.get('******************************************').balance.isub(amount);
    
    res.json({
      success: true,
      address,
      amount: amount.toString(),
      newBalance: aetheriumAccounts.get(address).balance.toString()
    });
  } catch (error) {
    console.error('Faucet error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Send Aetherium transaction
app.post('/aetherium/send', express.json(), (req, res) => {
  try {
    const { from, to, value, maxFeePerGas = '**********', maxPriorityFeePerGas, data } = req.body;
    const nodeId = req.headers['x-node-id'] || 'unknown';
    
    // Validate required fields
    if (!from || !to || !value) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Initialize accounts if they don't exist
    if (!aetheriumAccounts.has(from)) {
      aetheriumAccounts.set(from, { balance: new BN(0), nonce: 0 });
    }
    if (!aetheriumAccounts.has(to)) {
      aetheriumAccounts.set(to, { balance: new BN(0), nonce: 0 });
    }
    
    const fromAccount = aetheriumAccounts.get(from);
    const toAccount = aetheriumAccounts.get(to);
    
    // Convert values to BN for calculations
    const amount = new BN(value);
    const fee = new BN(maxFeePerGas);
    
    // Check balance (amount + fee)
    const totalCost = amount.add(fee);
    if (fromAccount.balance.lt(totalCost)) {
      return res.status(400).json({ 
        error: 'Insufficient balance', 
        required: totalCost.toString(),
        available: fromAccount.balance.toString()
      });
    }
    
    // Process transaction
    fromAccount.balance.isub(totalCost);
    toAccount.balance.iadd(amount);
    
    // Update nonce
    fromAccount.nonce++;
    
    // Log transaction
    const txHash = '0x' + crypto.randomBytes(32).toString('hex');
    
    res.json({
      success: true,
      txHash,
      from,
      to,
      value: amount.toString(),
      fee: fee.toString(),
      nonce: fromAccount.nonce - 1
    });
    
    console.log(`[${new Date().toISOString()}] TX ${txHash} | From: ${from} | To: ${to} | Value: ${amount} | Fee: ${fee}`);
  } catch (error) {
    console.error('Send error:', error);
    res.status(500).json({ 
      error: 'Transaction failed',
      details: error.message 
    });
  }
});

// Get account balance
app.get('/aetherium/balance/:address', (req, res) => {
  try {
    const { address } = req.params;
    const account = aetheriumAccounts.get(address) || { balance: new BN(0) };
    res.json({
      address,
      balance: account.balance.toString(),
      nonce: account.nonce || 0
    });
  } catch (error) {
    console.error('Balance error:', error);
    res.status(500).json({ error: 'Failed to get balance' });
  }
});

// Start the server
const PORT = process.env.PORT || 8080;
const HOST = '0.0.0.0';

const server = app.listen(PORT, HOST, () => {
  console.log(`\n🚀 KetherNet Server started on http://${HOST}:${PORT}`);
  console.log('💎 Aetherium transactions enabled');
  console.log(`💰 Genesis balance: ${aetheriumAccounts.get('******************************************').balance.toString()}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down KetherNet server...');
  server.close(() => {
    console.log('✅ Server stopped');
    process.exit(0);
  });
  
  // Force shutdown after 5 seconds
  setTimeout(() => {
    console.error('⚠️ Forcing shutdown...');
    process.exit(1);
  }, 5000);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});
