/**
 * <PERSON>ert Details Component
 * 
 * This component displays detailed information about a specific alert.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Chip, 
  Divider, 
  Grid, 
  Paper, 
  TextField, 
  Typography 
} from '@mui/material';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import InfoIcon from '@mui/icons-material/Info';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import NotificationsOffIcon from '@mui/icons-material/NotificationsOff';
import AssignmentTurnedInIcon from '@mui/icons-material/AssignmentTurnedIn';
import { useRouter } from 'next/router';

const AlertDetails = ({ alert }) => {
  const router = useRouter();
  const [comment, setComment] = useState('');
  
  const handleBack = () => {
    router.back();
  };
  
  const handleAcknowledge = () => {
    // In a real implementation, this would call an API to acknowledge the alert
    alert('Alert acknowledged');
  };
  
  const handleResolve = () => {
    // In a real implementation, this would call an API to resolve the alert
    alert('Alert resolved');
  };
  
  const handleAddComment = () => {
    // In a real implementation, this would call an API to add a comment
    if (comment.trim()) {
      alert(`Comment added: ${comment}`);
      setComment('');
    }
  };
  
  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical':
        return <ErrorIcon color="error" fontSize="large" />;
      case 'high':
        return <ErrorIcon sx={{ color: '#ff5722' }} fontSize="large" />;
      case 'medium':
        return <WarningIcon color="warning" fontSize="large" />;
      case 'low':
        return <InfoIcon color="info" fontSize="large" />;
      default:
        return null;
    }
  };
  
  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical':
        return 'error';
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'default';
    }
  };
  
  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <ErrorIcon color="error" />;
      case 'acknowledged':
        return <WarningIcon color="warning" />;
      case 'resolved':
        return <CheckCircleIcon color="success" />;
      default:
        return null;
    }
  };
  
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'error';
      case 'acknowledged':
        return 'warning';
      case 'resolved':
        return 'success';
      default:
        return 'default';
    }
  };
  
  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{ mr: 2 }}
        >
          Back to Alerts
        </Button>
        
        <Typography variant="h5" component="h1" sx={{ flexGrow: 1 }}>
          Alert Details
        </Typography>
        
        {alert.status === 'active' && (
          <Button
            variant="outlined"
            startIcon={<AssignmentTurnedInIcon />}
            onClick={handleAcknowledge}
            sx={{ mr: 2 }}
          >
            Acknowledge
          </Button>
        )}
        
        {(alert.status === 'active' || alert.status === 'acknowledged') && (
          <Button
            variant="contained"
            color="success"
            startIcon={<CheckCircleIcon />}
            onClick={handleResolve}
          >
            Resolve
          </Button>
        )}
      </Box>
      
      <Card variant="outlined" sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={1}>
              <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                {getSeverityIcon(alert.severity)}
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={11}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Typography variant="h5" component="h2" sx={{ flexGrow: 1 }}>
                  {alert.message}
                </Typography>
                
                <Chip 
                  label={alert.status.charAt(0).toUpperCase() + alert.status.slice(1)} 
                  color={getStatusColor(alert.status)} 
                />
              </Box>
              
              <Box sx={{ display: 'flex', mb: 1 }}>
                <Typography variant="body2" color="textSecondary" sx={{ mr: 4 }}>
                  <strong>Alert ID:</strong> {alert.id}
                </Typography>
                
                <Typography variant="body2" color="textSecondary" sx={{ mr: 4 }}>
                  <strong>Severity:</strong> {alert.severity.charAt(0).toUpperCase() + alert.severity.slice(1)}
                </Typography>
                
                <Typography variant="body2" color="textSecondary">
                  <strong>Time:</strong> {new Date(alert.timestamp).toLocaleString()}
                </Typography>
              </Box>
              
              <Typography variant="body2" color="textSecondary">
                <strong>Connector:</strong> {alert.connector.name}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card variant="outlined" sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Alert Details
              </Typography>
              
              <Typography variant="body1" paragraph>
                {alert.description || alert.message}
              </Typography>
              
              {alert.details && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Additional Details:
                  </Typography>
                  
                  <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.default' }}>
                    <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                      {typeof alert.details === 'object' 
                        ? JSON.stringify(alert.details, null, 2) 
                        : alert.details}
                    </pre>
                  </Paper>
                </Box>
              )}
            </CardContent>
          </Card>
          
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Comments & Activity
              </Typography>
              
              {alert.comments && alert.comments.length > 0 ? (
                <Box>
                  {alert.comments.map((comment, index) => (
                    <Paper 
                      key={index} 
                      variant="outlined" 
                      sx={{ p: 2, mb: 2 }}
                    >
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="subtitle2">
                          {comment.user}
                        </Typography>
                        
                        <Typography variant="body2" color="textSecondary">
                          {new Date(comment.timestamp).toLocaleString()}
                        </Typography>
                      </Box>
                      
                      <Typography variant="body2">
                        {comment.text}
                      </Typography>
                    </Paper>
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
                  No comments yet.
                </Typography>
              )}
              
              <Divider sx={{ my: 2 }} />
              
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Add Comment
                </Typography>
                
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Enter your comment here..."
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  sx={{ mb: 2 }}
                />
                
                <Button
                  variant="contained"
                  onClick={handleAddComment}
                  disabled={!comment.trim()}
                >
                  Add Comment
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card variant="outlined" sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Connector Information
              </Typography>
              
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Name:</strong> {alert.connector.name}
              </Typography>
              
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Version:</strong> {alert.connector.version}
              </Typography>
              
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Category:</strong> {alert.connector.category}
              </Typography>
              
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Status:</strong> {alert.connector.status}
              </Typography>
              
              <Box sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  onClick={() => router.push(`/connectors/${alert.connector.id}`)}
                >
                  View Connector
                </Button>
              </Box>
            </CardContent>
          </Card>
          
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Alert Timeline
              </Typography>
              
              {alert.timeline && alert.timeline.length > 0 ? (
                <Box>
                  {alert.timeline.map((event, index) => (
                    <Box key={index} sx={{ display: 'flex', mb: 2 }}>
                      <Box sx={{ mr: 2, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                        <Box 
                          sx={{ 
                            width: 12, 
                            height: 12, 
                            borderRadius: '50%', 
                            bgcolor: 'primary.main',
                            mb: 0.5
                          }} 
                        />
                        {index < alert.timeline.length - 1 && (
                          <Box 
                            sx={{ 
                              width: 2, 
                              flexGrow: 1, 
                              bgcolor: 'divider' 
                            }} 
                          />
                        )}
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" color="textSecondary">
                          {new Date(event.timestamp).toLocaleString()}
                        </Typography>
                        
                        <Typography variant="body2">
                          {event.message}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="textSecondary">
                  No timeline events available.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AlertDetails;
