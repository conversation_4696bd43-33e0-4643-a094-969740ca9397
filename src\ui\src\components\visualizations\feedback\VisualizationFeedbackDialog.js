import React, { useState } from 'react';
import { 
  <PERSON>alog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  TextField, 
  Typography, 
  Rating, 
  Box, 
  FormControl, 
  FormLabel, 
  RadioGroup, 
  FormControlLabel, 
  Radio, 
  Checkbox, 
  Snackbar, 
  Alert, 
  CircularProgress 
} from '@mui/material';
import { submitVisualizationFeedback } from '../../../services/cyberSafetyDataService';

/**
 * VisualizationFeedbackDialog component
 * 
 * A dialog for collecting user feedback on visualizations.
 * This component allows users to rate visualizations, provide comments,
 * and suggest improvements.
 */
function VisualizationFeedbackDialog({ 
  open, 
  onClose, 
  visualizationType, 
  visualizationName 
}) {
  // State for feedback form
  const [feedback, setFeedback] = useState({
    rating: 3,
    clarity: 'good',
    usefulness: 'good',
    performance: 'good',
    improvements: {
      colors: false,
      labels: false,
      interactivity: false,
      dataDisplay: false,
      performance: false,
      other: false
    },
    comments: '',
    email: ''
  });
  
  // State for form submission
  const [submitting, setSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState(null);
  
  // Handle form field changes
  const handleChange = (field, value) => {
    setFeedback(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  // Handle improvement checkbox changes
  const handleImprovementChange = (improvement, checked) => {
    setFeedback(prev => ({
      ...prev,
      improvements: {
        ...prev.improvements,
        [improvement]: checked
      }
    }));
  };
  
  // Handle form submission
  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      setSubmitError(null);
      
      // Convert improvements object to array of selected improvements
      const selectedImprovements = Object.entries(feedback.improvements)
        .filter(([_, selected]) => selected)
        .map(([improvement]) => improvement);
      
      // Prepare feedback data
      const feedbackData = {
        visualizationType,
        rating: feedback.rating,
        clarity: feedback.clarity,
        usefulness: feedback.usefulness,
        performance: feedback.performance,
        improvements: selectedImprovements,
        comments: feedback.comments,
        email: feedback.email || null,
        timestamp: new Date().toISOString()
      };
      
      // Submit feedback
      await submitVisualizationFeedback(visualizationType, feedbackData);
      
      // Show success message
      setSubmitSuccess(true);
      
      // Reset form after successful submission
      setFeedback({
        rating: 3,
        clarity: 'good',
        usefulness: 'good',
        performance: 'good',
        improvements: {
          colors: false,
          labels: false,
          interactivity: false,
          dataDisplay: false,
          performance: false,
          other: false
        },
        comments: '',
        email: ''
      });
      
      // Close dialog after a delay
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error) {
      console.error('Error submitting feedback:', error);
      setSubmitError(error.message || 'Error submitting feedback');
    } finally {
      setSubmitting(false);
    }
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSubmitSuccess(false);
    setSubmitError(null);
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>
          Feedback: {visualizationName || visualizationType}
        </DialogTitle>
        
        <DialogContent dividers>
          <Typography variant="subtitle1" gutterBottom>
            Your feedback helps us improve our visualizations. Please take a moment to share your thoughts.
          </Typography>
          
          {/* Overall Rating */}
          <Box sx={{ mb: 3, mt: 2 }}>
            <Typography component="legend">Overall Rating</Typography>
            <Rating
              name="rating"
              value={feedback.rating}
              onChange={(event, newValue) => {
                handleChange('rating', newValue);
              }}
              size="large"
            />
          </Box>
          
          {/* Clarity Rating */}
          <FormControl component="fieldset" sx={{ mb: 3 }}>
            <FormLabel component="legend">How clear and understandable is this visualization?</FormLabel>
            <RadioGroup
              row
              name="clarity"
              value={feedback.clarity}
              onChange={(e) => handleChange('clarity', e.target.value)}
            >
              <FormControlLabel value="poor" control={<Radio />} label="Poor" />
              <FormControlLabel value="fair" control={<Radio />} label="Fair" />
              <FormControlLabel value="good" control={<Radio />} label="Good" />
              <FormControlLabel value="excellent" control={<Radio />} label="Excellent" />
            </RadioGroup>
          </FormControl>
          
          {/* Usefulness Rating */}
          <FormControl component="fieldset" sx={{ mb: 3 }}>
            <FormLabel component="legend">How useful is this visualization for understanding Cyber-Safety fusion?</FormLabel>
            <RadioGroup
              row
              name="usefulness"
              value={feedback.usefulness}
              onChange={(e) => handleChange('usefulness', e.target.value)}
            >
              <FormControlLabel value="poor" control={<Radio />} label="Poor" />
              <FormControlLabel value="fair" control={<Radio />} label="Fair" />
              <FormControlLabel value="good" control={<Radio />} label="Good" />
              <FormControlLabel value="excellent" control={<Radio />} label="Excellent" />
            </RadioGroup>
          </FormControl>
          
          {/* Performance Rating */}
          <FormControl component="fieldset" sx={{ mb: 3 }}>
            <FormLabel component="legend">How would you rate the performance of this visualization?</FormLabel>
            <RadioGroup
              row
              name="performance"
              value={feedback.performance}
              onChange={(e) => handleChange('performance', e.target.value)}
            >
              <FormControlLabel value="poor" control={<Radio />} label="Poor" />
              <FormControlLabel value="fair" control={<Radio />} label="Fair" />
              <FormControlLabel value="good" control={<Radio />} label="Good" />
              <FormControlLabel value="excellent" control={<Radio />} label="Excellent" />
            </RadioGroup>
          </FormControl>
          
          {/* Suggested Improvements */}
          <Box sx={{ mb: 3 }}>
            <Typography component="legend" gutterBottom>
              What aspects of this visualization could be improved? (Select all that apply)
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap' }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={feedback.improvements.colors}
                    onChange={(e) => handleImprovementChange('colors', e.target.checked)}
                  />
                }
                label="Color scheme"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={feedback.improvements.labels}
                    onChange={(e) => handleImprovementChange('labels', e.target.checked)}
                  />
                }
                label="Labels and annotations"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={feedback.improvements.interactivity}
                    onChange={(e) => handleImprovementChange('interactivity', e.target.checked)}
                  />
                }
                label="Interactivity"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={feedback.improvements.dataDisplay}
                    onChange={(e) => handleImprovementChange('dataDisplay', e.target.checked)}
                  />
                }
                label="Data display"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={feedback.improvements.performance}
                    onChange={(e) => handleImprovementChange('performance', e.target.checked)}
                  />
                }
                label="Performance"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={feedback.improvements.other}
                    onChange={(e) => handleImprovementChange('other', e.target.checked)}
                  />
                }
                label="Other"
              />
            </Box>
          </Box>
          
          {/* Comments */}
          <TextField
            label="Additional Comments"
            multiline
            rows={4}
            value={feedback.comments}
            onChange={(e) => handleChange('comments', e.target.value)}
            fullWidth
            variant="outlined"
            placeholder="Please share any additional feedback or suggestions for improvement..."
            sx={{ mb: 3 }}
          />
          
          {/* Email (Optional) */}
          <TextField
            label="Email (Optional)"
            type="email"
            value={feedback.email}
            onChange={(e) => handleChange('email', e.target.value)}
            fullWidth
            variant="outlined"
            placeholder="Your email if you'd like us to follow up with you..."
            helperText="We'll only use your email to follow up on your feedback if necessary."
          />
        </DialogContent>
        
        <DialogActions>
          <Button onClick={onClose} disabled={submitting}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained" 
            color="primary"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : null}
          >
            {submitting ? 'Submitting...' : 'Submit Feedback'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Success Snackbar */}
      <Snackbar
        open={submitSuccess}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleSnackbarClose} severity="success" sx={{ width: '100%' }}>
          Thank you for your feedback!
        </Alert>
      </Snackbar>
      
      {/* Error Snackbar */}
      <Snackbar
        open={!!submitError}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleSnackbarClose} severity="error" sx={{ width: '100%' }}>
          {submitError}
        </Alert>
      </Snackbar>
    </>
  );
}

export default VisualizationFeedbackDialog;
