import React, { useState, useEffect, useRef } from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  Button, 
  CircularProgress, 
  Alert,
  Divider
} from '@mui/material';
import userTestingService from '../../services/userTestingService';
import analyticsService from '../../services/visualizationAnalyticsService';

// Import visualization components
import TriDomainTensorVisualization from '../visualizations/TriDomainTensorVisualization';
import HarmonyIndexVisualization from '../visualizations/HarmonyIndexVisualization';
import RiskControlFusionVisualization from '../visualizations/RiskControlFusionVisualization';
import ResonanceSpectrogramVisualization from '../visualizations/ResonanceSpectrogramVisualization';
import UnifiedComplianceSecurityVisualization from '../visualizations/UnifiedComplianceSecurityVisualization';

/**
 * Visualization Test Container
 * 
 * This component integrates the user testing framework with the actual visualizations.
 * It renders the appropriate visualization based on the test session and records user interactions.
 */
function VisualizationTestContainer({ 
  visualizationType, 
  testSession, 
  currentTask, 
  onInteraction, 
  onTaskComplete,
  onNeedHelp
}) {
  // State for visualization data
  const [visualizationData, setVisualizationData] = useState(null);
  
  // State for loading
  const [loading, setLoading] = useState(false);
  
  // State for error
  const [error, setError] = useState(null);
  
  // Reference to the visualization component
  const visualizationRef = useRef(null);
  
  // Load visualization data on component mount
  useEffect(() => {
    loadVisualizationData();
  }, [visualizationType]);
  
  // Load visualization data
  const loadVisualizationData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Simulate API call to get visualization data
      // In a real implementation, this would fetch data from the API
      const data = await simulateDataFetch(visualizationType);
      
      setVisualizationData(data);
    } catch (error) {
      console.error('Error loading visualization data:', error);
      setError('Failed to load visualization data. Please try again.');
      
      // Record error in analytics
      analyticsService.trackError(visualizationType, error.message, {
        context: 'user_testing',
        taskId: currentTask?.id
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Simulate data fetch for testing
  const simulateDataFetch = (type) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Return mock data based on visualization type
        switch (type) {
          case 'triDomainTensor':
            resolve({
              grc: { health: 0.75, entropyContainment: 0.8 },
              it: { health: 0.65, entropyContainment: 0.7 },
              cybersecurity: { health: 0.85, entropyContainment: 0.9 },
              connections: [
                { source: 'grc', target: 'it', strength: 0.6 },
                { source: 'grc', target: 'cybersecurity', strength: 0.8 },
                { source: 'it', target: 'cybersecurity', strength: 0.7 }
              ]
            });
            break;
          
          case 'harmonyIndex':
            resolve({
              domainData: {
                grc: { score: 0.75, trend: 'increasing' },
                it: { score: 0.65, trend: 'stable' },
                cybersecurity: { score: 0.85, trend: 'increasing' }
              },
              harmonyHistory: [0.65, 0.67, 0.7, 0.72, 0.75, 0.78]
            });
            break;
          
          case 'riskControlFusion':
            resolve({
              riskData: {
                grc: { compliance: 0.3, governance: 0.4, reporting: 0.5 },
                it: { infrastructure: 0.6, applications: 0.4, data: 0.5 },
                cybersecurity: { perimeter: 0.7, endpoint: 0.5, network: 0.6 }
              },
              controlData: {
                grc: { compliance: 0.6, governance: 0.7, reporting: 0.5 },
                it: { infrastructure: 0.5, applications: 0.6, data: 0.7 },
                cybersecurity: { perimeter: 0.8, endpoint: 0.6, network: 0.7 }
              }
            });
            break;
          
          case 'resonanceSpectrogram':
            resolve({
              domainData: {
                grc: { frequency: 0.3, amplitude: 0.7, phase: 0.2 },
                it: { frequency: 0.5, amplitude: 0.6, phase: 0.4 },
                cybersecurity: { frequency: 0.7, amplitude: 0.8, phase: 0.6 },
                crossDomainFlows: [
                  { source: 'grc', target: 'it', flow: 0.4 },
                  { source: 'grc', target: 'cybersecurity', flow: 0.6 },
                  { source: 'it', target: 'cybersecurity', flow: 0.5 }
                ]
              },
              spectrogramData: Array(50).fill().map((_, i) => ({
                time: i,
                grc: Math.sin(i * 0.1) * 0.5 + 0.5,
                it: Math.sin(i * 0.15 + 1) * 0.5 + 0.5,
                cybersecurity: Math.sin(i * 0.2 + 2) * 0.5 + 0.5
              })),
              predictionData: {
                dissonanceProbability: 0.3,
                criticalPoints: [
                  { timeStep: 12, severity: 0.7, domains: ['grc', 'it'] },
                  { timeStep: 28, severity: 0.5, domains: ['it', 'cybersecurity'] },
                  { timeStep: 42, severity: 0.8, domains: ['grc', 'cybersecurity'] }
                ]
              }
            });
            break;
          
          case 'unifiedComplianceSecurity':
            resolve({
              complianceData: {
                requirements: [
                  { id: 'req_1', name: 'Data Protection', category: 'Privacy' },
                  { id: 'req_2', name: 'Access Control', category: 'Security' },
                  { id: 'req_3', name: 'Incident Response', category: 'Operations' }
                ],
                controls: [
                  { id: 'ctrl_1', name: 'Encryption', category: 'Technical' },
                  { id: 'ctrl_2', name: 'Authentication', category: 'Technical' },
                  { id: 'ctrl_3', name: 'Monitoring', category: 'Operational' }
                ],
                implementations: [
                  { id: 'impl_1', name: 'Database Encryption', status: 'Implemented' },
                  { id: 'impl_2', name: 'MFA', status: 'Implemented' },
                  { id: 'impl_3', name: 'SIEM', status: 'Partial' }
                ],
                links: [
                  { source: 'req_1', target: 'ctrl_1', strength: 0.9 },
                  { source: 'req_2', target: 'ctrl_2', strength: 0.8 },
                  { source: 'req_3', target: 'ctrl_3', strength: 0.7 },
                  { source: 'ctrl_1', target: 'impl_1', strength: 0.9 },
                  { source: 'ctrl_2', target: 'impl_2', strength: 0.8 },
                  { source: 'ctrl_3', target: 'impl_3', strength: 0.6 }
                ]
              },
              impactAnalysis: {
                overallCompliance: 0.75,
                gapAnalysis: {
                  gaps: [
                    { id: 'gap_1', requirement: 'req_3', control: 'ctrl_3', implementation: 'impl_3', severity: 'Medium' }
                  ]
                },
                proposedChanges: [
                  { id: 'change_1', description: 'Enhance SIEM implementation', impact: 0.15, effort: 'Medium' },
                  { id: 'change_2', description: 'Add Data Loss Prevention', impact: 0.2, effort: 'High' }
                ]
              }
            });
            break;
          
          default:
            resolve(null);
        }
      }, 1000); // Simulate network delay
    });
  };
  
  // Handle visualization interaction
  const handleInteraction = (interactionType, details = {}) => {
    // Record interaction in user testing service
    if (currentTask) {
      userTestingService.recordTaskInteraction(
        testSession.currentTaskIndex,
        interactionType,
        details
      );
    }
    
    // Track interaction in analytics
    analyticsService.trackInteraction(interactionType, visualizationType, {
      ...details,
      context: 'user_testing',
      taskId: currentTask?.id
    });
    
    // Call onInteraction callback if provided
    if (onInteraction) {
      onInteraction(interactionType, details);
    }
  };
  
  // Render the appropriate visualization based on type
  const renderVisualization = () => {
    if (!visualizationData) return null;
    
    const commonProps = {
      data: visualizationData,
      width: '100%',
      height: 400,
      ref: visualizationRef,
      onInteraction: handleInteraction
    };
    
    switch (visualizationType) {
      case 'triDomainTensor':
        return <TriDomainTensorVisualization {...commonProps} />;
      
      case 'harmonyIndex':
        return <HarmonyIndexVisualization {...commonProps} />;
      
      case 'riskControlFusion':
        return <RiskControlFusionVisualization {...commonProps} />;
      
      case 'resonanceSpectrogram':
        return <ResonanceSpectrogramVisualization {...commonProps} />;
      
      case 'unifiedComplianceSecurity':
        return <UnifiedComplianceSecurityVisualization {...commonProps} />;
      
      default:
        return (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1">
              Visualization type not supported: {visualizationType}
            </Typography>
          </Box>
        );
    }
  };
  
  return (
    <Paper sx={{ p: 2, mb: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          {visualizationType} Visualization
        </Typography>
        
        <Box>
          <Button
            variant="outlined"
            color="primary"
            onClick={onNeedHelp}
            sx={{ mr: 1 }}
          >
            Need Help
          </Button>
          
          <Button
            variant="contained"
            color="primary"
            onClick={onTaskComplete}
          >
            Complete Task
          </Button>
        </Box>
      </Box>
      
      <Divider sx={{ mb: 2 }} />
      
      {currentTask && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Task: {currentTask.name}
          </Typography>
          
          <Typography variant="body2">
            {currentTask.description}
          </Typography>
        </Box>
      )}
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      ) : (
        renderVisualization()
      )}
    </Paper>
  );
}

export default VisualizationTestContainer;
