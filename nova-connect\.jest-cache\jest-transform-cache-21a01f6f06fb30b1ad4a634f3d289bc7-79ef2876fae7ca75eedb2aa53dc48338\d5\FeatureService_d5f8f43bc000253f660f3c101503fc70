096c6c08eb237fb3bc4e4223388adc28
/**
 * NovaConnect UAC Feature Service
 * 
 * This service handles feature toggling based on customer entitlements.
 */

const fs = require('fs').promises;
const path = require('path');
const logger = require('../../config/logger');
class FeatureService {
  constructor() {
    this.dataDir = path.join(__dirname, '../../data/features');
    this.featuresFile = path.join(this.dataDir, 'features.json');
    this.customerFeaturesFile = path.join(this.dataDir, 'customer_features.json');

    // Define tier features
    this.tierFeatures = {
      core: ['basic_api_access', 'standard_connectors', 'basic_normalization', 'basic_monitoring'],
      secure: ['basic_api_access', 'standard_connectors', 'advanced_connectors', 'basic_normalization', 'advanced_normalization', 'basic_monitoring', 'advanced_monitoring', 'basic_security'],
      enterprise: ['basic_api_access', 'standard_connectors', 'advanced_connectors', 'enterprise_connectors', 'basic_normalization', 'advanced_normalization', 'enterprise_normalization', 'basic_monitoring', 'advanced_monitoring', 'enterprise_monitoring', 'basic_security', 'advanced_security'],
      ai_boost: ['basic_api_access', 'standard_connectors', 'advanced_connectors', 'enterprise_connectors', 'ai_connectors', 'basic_normalization', 'advanced_normalization', 'enterprise_normalization', 'ai_normalization', 'basic_monitoring', 'advanced_monitoring', 'enterprise_monitoring', 'ai_monitoring', 'basic_security', 'advanced_security', 'ai_security']
    };
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, {
        recursive: true
      });

      // Initialize features file if it doesn't exist
      try {
        await fs.access(this.featuresFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.featuresFile, JSON.stringify(this.tierFeatures));
        } else {
          throw error;
        }
      }

      // Initialize customer features file if it doesn't exist
      try {
        await fs.access(this.customerFeaturesFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.customerFeaturesFile, JSON.stringify({}));
        } else {
          throw error;
        }
      }
    } catch (error) {
      logger.error('Error ensuring feature data directory', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Load features from file
   */
  async loadFeatures() {
    try {
      const data = await fs.readFile(this.featuresFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      logger.error('Error loading features', {
        error: error.message
      });
      return this.tierFeatures;
    }
  }

  /**
   * Load customer features from file
   */
  async loadCustomerFeatures() {
    try {
      const data = await fs.readFile(this.customerFeaturesFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      logger.error('Error loading customer features', {
        error: error.message
      });
      return {};
    }
  }

  /**
   * Save customer features to file
   */
  async saveCustomerFeatures(customerFeatures) {
    try {
      await fs.writeFile(this.customerFeaturesFile, JSON.stringify(customerFeatures, null, 2));
    } catch (error) {
      logger.error('Error saving customer features', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Enable features for a customer based on tier
   */
  async enableFeaturesForTier(customerId, tier) {
    try {
      logger.info('Enabling features for tier', {
        customerId,
        tier
      });

      // Load features
      const features = await this.loadFeatures();

      // Get features for tier
      const tierFeatures = features[tier] || this.tierFeatures[tier] || [];
      if (tierFeatures.length === 0) {
        logger.warn('No features found for tier', {
          tier
        });
      }

      // Load customer features
      const customerFeatures = await this.loadCustomerFeatures();

      // Update customer features
      customerFeatures[customerId] = {
        tier,
        features: tierFeatures,
        status: 'ACTIVE',
        updatedAt: new Date().toISOString()
      };

      // Save customer features
      await this.saveCustomerFeatures(customerFeatures);
      logger.info('Features enabled for tier', {
        customerId,
        tier,
        featureCount: tierFeatures.length
      });
      return tierFeatures;
    } catch (error) {
      logger.error('Error enabling features for tier', {
        error: error.message,
        customerId,
        tier
      });
      throw error;
    }
  }

  /**
   * Disable features for a customer
   */
  async disableFeatures(customerId) {
    try {
      logger.info('Disabling features', {
        customerId
      });

      // Load customer features
      const customerFeatures = await this.loadCustomerFeatures();

      // Check if customer has features
      if (!customerFeatures[customerId]) {
        logger.warn('No features found for customer', {
          customerId
        });
        return;
      }

      // Update customer features
      customerFeatures[customerId] = {
        ...customerFeatures[customerId],
        status: 'DISABLED',
        updatedAt: new Date().toISOString(),
        disabledAt: new Date().toISOString()
      };

      // Save customer features
      await this.saveCustomerFeatures(customerFeatures);
      logger.info('Features disabled', {
        customerId
      });
    } catch (error) {
      logger.error('Error disabling features', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }

  /**
   * Suspend features for a customer
   */
  async suspendFeatures(customerId) {
    try {
      logger.info('Suspending features', {
        customerId
      });

      // Load customer features
      const customerFeatures = await this.loadCustomerFeatures();

      // Check if customer has features
      if (!customerFeatures[customerId]) {
        logger.warn('No features found for customer', {
          customerId
        });
        return;
      }

      // Update customer features
      customerFeatures[customerId] = {
        ...customerFeatures[customerId],
        status: 'SUSPENDED',
        updatedAt: new Date().toISOString(),
        suspendedAt: new Date().toISOString()
      };

      // Save customer features
      await this.saveCustomerFeatures(customerFeatures);
      logger.info('Features suspended', {
        customerId
      });
    } catch (error) {
      logger.error('Error suspending features', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }

  /**
   * Check if a feature is enabled for a customer
   */
  async isFeatureEnabled(customerId, featureName) {
    try {
      // Load customer features
      const customerFeatures = await this.loadCustomerFeatures();

      // Check if customer has features
      if (!customerFeatures[customerId]) {
        logger.debug('No features found for customer', {
          customerId
        });
        return false;
      }

      // Check if customer features are active
      if (customerFeatures[customerId].status !== 'ACTIVE') {
        logger.debug('Customer features are not active', {
          customerId,
          status: customerFeatures[customerId].status
        });
        return false;
      }

      // Check if feature is enabled
      const isEnabled = customerFeatures[customerId].features.includes(featureName);
      return isEnabled;
    } catch (error) {
      logger.error('Error checking if feature is enabled', {
        error: error.message,
        customerId,
        featureName
      });
      return false;
    }
  }

  /**
   * Get customer features
   */
  async getCustomerFeatures(customerId) {
    try {
      // Load customer features
      const customerFeatures = await this.loadCustomerFeatures();

      // Return customer features or empty object
      return customerFeatures[customerId] || {
        status: 'NOT_FOUND'
      };
    } catch (error) {
      logger.error('Error getting customer features', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }
}
module.exports = FeatureService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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