{"numFailedTestSuites": 1, "numFailedTests": 1, "numPassedTestSuites": 0, "numPassedTests": 15, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 16, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1745912158946, "success": false, "testResults": [{"leaks": false, "numFailingTests": 1, "numPassingTests": 15, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1745912162392, "runtime": 2452, "slow": false, "start": 1745912159940}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\unit\\utils\\cache.test.js", "testResults": [{"ancestorTitles": ["<PERSON><PERSON> Utility", "set"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility set should set a value in the cache", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should set a value in the cache"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "set"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility set should set a value with custom TTL", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should set a value with custom TTL"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "set"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility set should update an existing value", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should update an existing value"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "get"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility get should return undefined for non-existent key", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return undefined for non-existent key"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "get"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility get should return the cached value for existing key", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return the cached value for existing key"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "get"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility get should return undefined for expired key", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return undefined for expired key"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "get"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility get should update lastAccessed time on get", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should update lastAccessed time on get"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "has"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility has should return false for non-existent key", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return false for non-existent key"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "has"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility has should return true for existing key", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return true for existing key"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "has"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility has should return false for expired key", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return false for expired key"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "delete"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility delete should delete a key from the cache", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should delete a key from the cache"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "delete"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility delete should return false when deleting non-existent key", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return false when deleting non-existent key"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "clear"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility clear should clear all keys from the cache", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should clear all keys from the cache"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "cleanup"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility cleanup should remove expired items during cleanup", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should remove expired items during cleanup"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "evict"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Cache Utility evict should evict least recently used items when cache is full", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should evict least recently used items when cache is full"}, {"ancestorTitles": ["<PERSON><PERSON> Utility", "getStats"], "duration": 5, "failureDetails": [{"matcherResult": {"actual": 5, "expected": 2, "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m5\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m5\u001b[39m\n    at Object.toBe (D:\\novafuse-api-superstore\\tests\\unit\\utils\\cache.test.js:232:26)\n    at Promise.then.completed (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:444:34)"], "fullName": "Cache Utility getStats should return cache statistics", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "should return cache statistics"}], "failureMessage": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCache Utility › getStats › should return cache statistics\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m2\u001b[39m\n    Received: \u001b[31m5\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 230 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 231 |\u001b[39m       expect(stats\u001b[33m.\u001b[39msize)\u001b[33m.\u001b[39mtoBe(\u001b[35m2\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 232 |\u001b[39m       expect(stats\u001b[33m.\u001b[39mhits)\u001b[33m.\u001b[39mtoBe(\u001b[35m2\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 233 |\u001b[39m       expect(stats\u001b[33m.\u001b[39mmisses)\u001b[33m.\u001b[39mtoBe(\u001b[35m1\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 234 |\u001b[39m       expect(stats\u001b[33m.\u001b[39msets)\u001b[33m.\u001b[39mtoBe(\u001b[35m2\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 235 |\u001b[39m       expect(stats\u001b[33m.\u001b[39mhitRate)\u001b[33m.\u001b[39mtoBeCloseTo(\u001b[35m2\u001b[39m\u001b[35m/3, 2); /\u001b[39m\u001b[33m/\u001b[39m \u001b[35m2\u001b[39m hits out \u001b[36mof\u001b[39m \u001b[35m3\u001b[39m attempts\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.toBe (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/unit/utils/cache.test.js\u001b[39m\u001b[0m\u001b[2m:232:26)\u001b[22m\u001b[2m\u001b[22m\n"}], "wasInterrupted": false}