/**
 * Authentication Routes
 */

const express = require('express');
const router = express.Router();
const { authenticateUser } = require('./auth');
const { validateRequest, schemas } = require('./validation');

/**
 * @swagger
 * /privacy/management/auth/login:
 *   post:
 *     summary: Login to the API
 *     description: Authenticates a user and returns a JWT token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               username:
 *                 type: string
 *               password:
 *                 type: string
 *             required:
 *               - username
 *               - password
 *     responses:
 *       200:
 *         description: Authentication successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 token:
 *                   type: string
 *                 user:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     username:
 *                       type: string
 *                     role:
 *                       type: string
 *       401:
 *         description: Authentication failed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 message:
 *                   type: string
 */
router.post('/login', validateRequest('auth'), async (req, res) => {
  try {
    const { username, password } = req.body;
    const result = await authenticateUser(username, password);

    if (!result.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: result.message
      });
    }

    res.json({
      token: result.token,
      user: result.user
    });
  } catch (error) {
    console.error('Error in login:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
});

module.exports = router;
