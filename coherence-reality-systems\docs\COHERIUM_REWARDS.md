# Coherium Rewards System

## Overview
The Coherium (κ) token is the native cryptocurrency of the KetherNet blockchain, designed to incentivize and reward network participants. This document outlines the technical specifications, reward mechanisms, and API endpoints for the Coherium rewards system.

## Tokenomics

### Key Parameters
- **Total Supply**: 144,000,000 κ (144 million)
- **Block Reward**: 100 κ (halves every 4 years)
- **Genesis Supply**: 1,000,000 κ
- **Block Time**: 15 seconds (adjustable)
- **Consensus**: Proof of Consciousness (PoC)

### Reward Distribution
Rewards are distributed to Crown Nodes based on:
1. **Consciousness Score** (50% weight)
   - Based on UUFT (Unified Field Tensor)
   - Scaled to 0-1 range

2. **Stability Bonus** (up to 2x multiplier)
   - Consecutive validations
   - Maximum 2x multiplier at 10+ consecutive validations

3. **Time Bonus** (up to 1x multiplier)
   - Decays over 1 hour
   - Encourages consistent participation

## API Endpoints

### 1. Get Node Balance
```
GET /coherium/balance
```
**Headers:**
- `x-node-id`: Node identifier

**Response:**
```json
{
  "success": true,
  "address": "node_address_hex",
  "balance": 1500,
  "pending": 250,
  "totalEarned": 1750,
  "lastClaim": 1624440000000,
  "network": {
    "total_supply": 1000500,
    "max_supply": 144000000,
    "block_height": 150,
    "last_reward": 1624440015000
  }
}
```

### 2. Claim Rewards
```
POST /coherium/claim
```
**Headers:**
- `x-node-id`: Node identifier

**Response:**
```json
{
  "success": true,
  "message": "Rewards claimed successfully",
  "amount": 250,
  "newBalance": 1750,
  "address": "node_address_hex",
  "timestamp": 1624440015000
}
```

### 3. Network Statistics
```
GET /coherium/network
```
**Response:**
```json
{
  "success": true,
  "stats": {
    "total_supply": 1000500,
    "max_supply": 144000000,
    "remaining_supply": 142999500,
    "block_height": 150,
    "block_reward": 100,
    "crown_nodes": 5,
    "active_nodes": 3,
    "last_reward": 1624440015000,
    "timestamp": 1624440030000
  }
}
```

## Testing

### Prerequisites
- Node.js v14+
- KetherNet server running on `localhost:8080`

### Running Tests
1. Start the KetherNet server:
   ```bash
   node kethernet-server.js
   ```

2. Run the test script:
   ```bash
   node test-coherium.js
   ```

### Test Coverage
- Node registration and validation
- Reward distribution
- Balance tracking
- Reward claiming
- Network statistics
- Error handling

## Implementation Details

### Reward Calculation
```javascript
function calculateReward(node, blockTime) {
  const baseReward = BLOCK_REWARD;
  const stabilityBonus = Math.min(node.consecutiveValidations * 0.1, 2.0);
  const timeBonus = Math.max(0, 1 - ((blockTime - node.lastActive) / 3600000));
  const scoreFactor = node.score / CONSCIOUSNESS_THRESHOLD;
  
  return Math.floor(baseReward * stabilityBonus * (0.5 + 0.5 * timeBonus) * scoreFactor);
}
```

### Data Structures
```typescript
interface CoheriumAccount {
  address: string;
  balance: number;
  pending: number;
  totalEarned: number;
  lastClaim: number;
}

interface CrownNode {
  id: string;
  score: number;
  joinDate: Date;
  lastActive: Date;
  consecutiveValidations: number;
  totalValidations: number;
  totalRewards: number;
}
```

## Security Considerations
1. **Private Key Management**: In production, use proper key management
2. **Rate Limiting**: Implement rate limiting on public endpoints
3. **Input Validation**: Validate all incoming requests
4. **Error Handling**: Provide meaningful error messages without leaking sensitive information

## Future Enhancements
1. Implement Aetherium gas system
2. Add staking mechanism
3. Enable cross-chain transfers
4. Add governance features
