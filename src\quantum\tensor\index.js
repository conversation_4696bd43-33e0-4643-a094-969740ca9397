/**
 * Tensor Module
 *
 * This module exports all tensor-related components for the Comphyological framework.
 */

const TensorOperations = require('./tensor_operations');
const DynamicWeightingProtocol = require('./dynamic_weighting');
const PsiTensorCore = require('./psi_tensor_core');
const EnergyCalculator = require('./energy_calculator');
const { ComphyologicalTensorCore, createComphyologicalTensorCore } = require('./comphyological_tensor_core');
const { GPUAccelerator, createGPUAccelerator } = require('./gpu_accelerator');

/**
 * Create all tensor components
 * @param {Object} options - Configuration options
 * @returns {Object} - Object containing all tensor components
 */
function createTensorComponents(options = {}) {
  // Create GPU accelerator if GPU is enabled
  let gpuAccelerator = null;
  if (options.useGPU) {
    gpuAccelerator = createGPUAccelerator({
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      precision: options.precision || 6,
      ...options.gpuAcceleratorOptions
    });
  }

  // Create tensor operations
  const tensorOps = new TensorOperations({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    precision: options.precision || 6,
    useOptimizedOperations: true,
    ...options.tensorOpsOptions
  });

  // Create dynamic weighting protocol
  const dynamicWeighting = new DynamicWeightingProtocol({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    ...options.dynamicWeightingOptions
  });

  // Create Psi tensor core
  const psiTensorCore = new PsiTensorCore({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    useGPU: options.useGPU !== undefined ? options.useGPU : false,
    useDynamicWeighting: options.useDynamicWeighting !== undefined ? options.useDynamicWeighting : true,
    precision: options.precision || 6,
    ...options.psiTensorCoreOptions
  });

  // Create energy calculator
  const energyCalculator = new EnergyCalculator({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    precision: options.precision || 6,
    normalizationFactor: options.normalizationFactor || 166000,
    ...options.energyCalculatorOptions
  });

  // Create Comphyological tensor core
  const comphyologicalTensorCore = createComphyologicalTensorCore({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    strictMode: options.strictMode !== undefined ? options.strictMode : false,
    useGPU: options.useGPU !== undefined ? options.useGPU : false,
    useDynamicWeighting: options.useDynamicWeighting !== undefined ? options.useDynamicWeighting : true,
    precision: options.precision || 6,
    normalizationFactor: options.normalizationFactor || 166000,
    ...options.comphyologicalTensorCoreOptions
  });

  return {
    tensorOps,
    dynamicWeighting,
    psiTensorCore,
    energyCalculator,
    comphyologicalTensorCore,
    gpuAccelerator
  };
}

module.exports = {
  // Tensor operations
  TensorOperations,

  // Dynamic weighting protocol
  DynamicWeightingProtocol,

  // Psi tensor core
  PsiTensorCore,

  // Energy calculator
  EnergyCalculator,

  // Comphyological tensor core
  ComphyologicalTensorCore,
  createComphyologicalTensorCore,

  // GPU accelerator
  GPUAccelerator,
  createGPUAccelerator,

  // Factory function
  createTensorComponents
};
