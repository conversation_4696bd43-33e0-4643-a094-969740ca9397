"""
Fibonacci Bias Analysis Module

This module analyzes protein structures for Fibonacci sequence patterns and golden
ratio proportions, which are hypothesized to correlate with consciousness-related
properties in proteins.
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

@dataclass
class FibonacciMatch:
    """Data class to store Fibonacci sequence match results."""
    sequence: List[int]
    start_idx: int
    end_idx: int
    score: float
    ratio: float
    golden_ratio_diff: float

class FibonacciBiasAnalyzer:
    """Analyze Fibonacci sequence patterns in protein structures."""
    
    GOLDEN_RATIO = (1 + 5 ** 0.5) / 2  # φ (phi), approximately 1.618
    
    def __init__(self, min_sequence_length: int = 3):
        """Initialize the Fibonacci analyzer.
        
        Args:
            min_sequence_length: Minimum length of Fibonacci sequence to consider
        """
        self.min_sequence_length = min_sequence_length
        self.fib_cache = {0: 0, 1: 1}  # Cache for Fibonacci numbers
    
    def generate_fibonacci(self, n: int) -> List[int]:
        """Generate Fibonacci sequence up to n terms."""
        if n in self.fib_cache:
            return [self.fib_cache[i] for i in range(n + 1) if i in self.fib_cache]
            
        fib_sequence = [0, 1]
        for i in range(2, n + 1):
            fib_sequence.append(fib_sequence[-1] + fib_sequence[-2])
            self.fib_cache[i] = fib_sequence[-1]
        
        return fib_sequence
    
    def find_fibonacci_sequences(self, sequence: List[float], 
                               tolerance: float = 0.1) -> List[FibonacciMatch]:
        """Find Fibonacci-like sequences in a list of numbers.
        
        Args:
            sequence: Input sequence of numbers to analyze
            tolerance: Allowed deviation from perfect Fibonacci ratios
            
        Returns:
            List of FibonacciMatch objects describing found sequences
        """
        if len(sequence) < self.min_sequence_length:
            return []
            
        matches = []
        max_length = min(20, len(sequence))  # Limit maximum sequence length
        
        # Check all possible starting points and sequence lengths
        for start in range(len(sequence) - self.min_sequence_length + 1):
            for length in range(self.min_sequence_length, 
                              min(max_length, len(sequence) - start) + 1):
                subseq = sequence[start:start + length]
                match = self._check_fibonacci_sequence(subseq, tolerance)
                if match:
                    matches.append(FibonacciMatch(
                        sequence=subseq,
                        start_idx=start,
                        end_idx=start + length - 1,
                        score=match['score'],
                        ratio=match['ratio'],
                        golden_ratio_diff=match['golden_ratio_diff']
                    ))
        
        return sorted(matches, key=lambda x: x.score, reverse=True)
    
    def analyze_sequence(self, sequence: str) -> Dict:
        """Analyze a protein sequence for Fibonacci patterns.
        
        Args:
            sequence: Protein sequence to analyze
            
        Returns:
            Dictionary containing analysis results including:
            - has_pattern: bool - Whether Fibonacci patterns were found
            - score: float - Fibonacci pattern matching score (0-1)
            - matches: list - List of detected Fibonacci patterns
        """
        if not sequence or len(sequence) < self.min_sequence_length:
            return {
                'has_pattern': False,
                'score': 0.0,
                'matches': []
            }
            
        # Convert sequence to numerical representation (e.g., using amino acid properties)
        # For simplicity, we'll use the length of the sequence as a placeholder
        # In a real implementation, this would use actual amino acid properties
        sequence_length = len(sequence)
        
        # Generate Fibonacci sequence up to the sequence length
        fib_sequence = self.generate_fibonacci(min(20, sequence_length))
        
        # Simple pattern matching - check if sequence length is a Fibonacci number
        is_fib = sequence_length in fib_sequence
        
        # Calculate a simple score based on proximity to Fibonacci numbers
        closest_fib = min(fib_sequence, key=lambda x: abs(x - sequence_length))
        score = 1.0 - (abs(sequence_length - closest_fib) / sequence_length) if sequence_length > 0 else 0.0
        
        return {
            'has_pattern': is_fib,
            'score': max(0.0, min(1.0, score)),  # Ensure score is between 0 and 1
            'matches': [{
                'start': 0,
                'end': sequence_length - 1,
                'score': score,
                'type': 'length_fibonacci' if is_fib else 'proximity_to_fibonacci'
            }] if sequence_length > 0 else []
        }
        
    def _check_fibonacci_sequence(self, sequence: List[float], 
                                tolerance: float) -> Optional[Dict]:
        """Check if a sequence follows the Fibonacci pattern."""
        if len(sequence) < 3:
            return None
            
        # Calculate consecutive ratios
        ratios = []
        for i in range(2, len(sequence)):
            try:
                ratio = sequence[i] / sequence[i-1]
                ratios.append(ratio)
            except ZeroDivisionError:
                return None
        
        # Check if ratios approach the golden ratio
        avg_ratio = sum(ratios) / len(ratios) if ratios else 0.0
        golden_ratio_diff = abs(avg_ratio - self.GOLDEN_RATIO) / self.GOLDEN_RATIO
        
        # Calculate a score based on how close the ratios are to the golden ratio
        score = 1.0 / (1.0 + golden_ratio_diff * 10)  # Scale to [0,1] range
        
        # Consider it a match if within tolerance
        if golden_ratio_diff <= tolerance:
            return {
                'score': score,
                'ratio': avg_ratio,
                'golden_ratio_diff': golden_ratio_diff
            }
        return None
    
    def analyze_protein_structure(self, structure_data: Dict) -> Dict:
        """Analyze a protein structure for Fibonacci patterns.
        
        Args:
            structure_data: Dictionary containing protein structure information
            
        Returns:
            Dictionary with analysis results
        """
        # Extract relevant metrics (simplified - in practice would use actual structure data)
        metrics = {
            'residue_distances': structure_data.get('residue_distances', []),
            'torsion_angles': structure_data.get('torsion_angles', []),
            'domain_sizes': structure_data.get('domain_sizes', [])
        }
        
        results = {}
        
        # Analyze each metric for Fibonacci patterns
        for metric_name, values in metrics.items():
            if len(values) >= self.min_sequence_length:
                matches = self.find_fibonacci_sequences(values)
                if matches:
                    results[metric_name] = [{
                        'start': m.start_idx,
                        'end': m.end_idx,
                        'score': m.score,
                        'ratio': m.ratio,
                        'golden_ratio_diff': m.golden_ratio_diff
                    } for m in matches]
        
        # Calculate overall Fibonacci bias score
        if results:
            all_scores = [m['score'] for metric in results.values() for m in metric]
            results['overall_score'] = float(sum(all_scores) / len(all_scores)) if all_scores else 0.0
        else:
            results['overall_score'] = 0.0
        
        # Ensure all numeric values are native Python types
        for metric_name, matches in results.items():
            if isinstance(matches, list):
                for match in matches:
                    for key, value in match.items():
                        if hasattr(value, 'item'):  # Convert numpy types to native Python
                            match[key] = value.item()
                        elif isinstance(value, (np.floating, np.integer)):
                            match[key] = float(value) if isinstance(value, np.floating) else int(value)
    
        return results
    
    def find_optimal_fibonacci_alignment(self, sequence_length: int) -> Dict:
        """Find the closest Fibonacci number to a given sequence length.
        
        Args:
            sequence_length: Length of the sequence to analyze
            
        Returns:
            Dictionary with alignment information
        """
        if sequence_length <= 0:
            return {
                'closest_fib': 0,
                'difference': 0,
                'ratio': 0,
                'alignment_score': 0
            }
            
        # Generate Fibonacci numbers up to at least sequence_length
        n = 2
        while True:
            fib_n = self.generate_fibonacci(n)[-1]
            if fib_n >= sequence_length * 0.5:  # Stop when we're in the right range
                break
            n += 1
        
        # Find closest Fibonacci number
        fibs = self.generate_fibonacci(n + 2)  # Get a few extra terms
        closest_fib = min(fibs, key=lambda x: abs(x - sequence_length))
        difference = abs(sequence_length - closest_fib)
        
        # Calculate alignment score (higher is better)
        if closest_fib == 0:
            alignment_score = 0
        else:
            alignment_score = 1 / (1 + difference / closest_fib)
        
        # Ensure all numeric values are native Python types
        return {
            'closest_fibonacci': int(closest_fib),
            'difference': int(difference),
            'ratio': float(sequence_length / closest_fib) if closest_fib != 0 else 0.0,
            'alignment_score': float(alignment_score)
        }
