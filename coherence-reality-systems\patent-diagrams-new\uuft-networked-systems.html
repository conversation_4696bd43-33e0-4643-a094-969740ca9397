<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UUFT for Networked Systems</title>
    <style>
        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }

        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 800px;
            height: 700px; /* Extended height for adequate space */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: hidden; /* Prevents content from spilling out */
        }

        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: hidden; /* Prevents content from spilling out */
        }

        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: hidden; /* Prevents content from spilling out */
            text-overflow: ellipsis; /* Shows ellipsis for overflowing text */
        }

        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start; /* Align content from top */
            align-items: center;
            text-align: center;
            padding: 10px; /* Even padding all around */
            overflow: visible; /* Allow content to be fully visible */
            border-top-left-radius: 0; /* Flat corner for number */
        }

        /* Component Number Styles - Integrated into corner */
        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 20px;
            height: 20px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 0 0 6px 0; /* Rounded on bottom-right corner only */
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
            z-index: 2;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }

        .component-label {
            font-weight: bold;
            margin-top: 15px; /* Increased margin to make room for number */
            margin-bottom: 6px;
            font-size: 14px;
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: hidden; /* Prevents content from spilling out */
            text-overflow: ellipsis; /* Shows ellipsis for overflowing text */
        }

        .component-content {
            font-size: 11px; /* Slightly smaller font */
            text-align: center;
            width: 100%;
            padding: 0 5px;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            line-height: 1.3; /* Tighter line spacing */
        }

        /* Arrow Styles */
        .arrow-line {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            z-index: 0;
        }

        .arrow-head {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 0;
        }

        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }

        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }

        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }

        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }

        /* Equation Styles */
        .equation {
            font-weight: bold;
            font-size: 14px;
            margin-top: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>FIG. 15: Universal Unified Field Theory (UUFT) for Networked Systems</h1>

    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 580px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">UUFT ARCHITECTURE FOR NETWORKED SYSTEMS STABILITY</div>
        </div>

        <!-- Input Components -->
        <div class="component-box" style="left: 25px; top: 80px; width: 200px; height: 110px;">
            <div class="component-number-inside">1</div>
            <div class="component-label">Input A: Network Nodes</div>
            <div class="component-content">
                Data sources, agents, or network endpoints encoded as energy packets (En)
            </div>
        </div>

        <div class="component-box" style="left: 525px; top: 80px; width: 200px; height: 110px;">
            <div class="component-number-inside">2</div>
            <div class="component-label">Input B: Network Topology</div>
            <div class="component-content">
                Connection patterns, hierarchies, and relationships encoded as information packets (In)
            </div>
        </div>

        <!-- Tensor Operator -->
        <div class="component-box" style="left: 275px; top: 180px; width: 200px; height: 110px;">
            <div class="component-number-inside">3</div>
            <div class="component-label">Tensor Operator ⊗</div>
            <div class="component-content">
                Multi-dimensional integration of nodes and topology into a coherent field
            </div>
        </div>

        <!-- Tensor Product -->
        <div class="component-box" style="left: 275px; top: 280px; width: 200px; height: 110px;">
            <div class="component-number-inside">4</div>
            <div class="component-label">Tensor Product</div>
            <div class="component-content">
                Integrated network state with emergent properties beyond individual nodes
            </div>
        </div>

        <!-- Input C -->
        <div class="component-box" style="left: 25px; top: 280px; width: 200px; height: 110px;">
            <div class="component-number-inside">5</div>
            <div class="component-label">Input C: Governance Rules</div>
            <div class="component-content">
                Policies, constraints, and operational parameters for the network
            </div>
        </div>

        <!-- Fusion Operator -->
        <div class="component-box" style="left: 275px; top: 380px; width: 200px; height: 110px;">
            <div class="component-number-inside">6</div>
            <div class="component-label">Fusion Operator ⊕</div>
            <div class="component-content">
                Non-linear integration of governance rules with the network state
            </div>
        </div>

        <!-- Circular Trust Topology -->
        <div class="component-box" style="left: 275px; top: 480px; width: 200px; height: 110px;">
            <div class="component-number-inside">7</div>
            <div class="component-label">Circular Trust Topology</div>
            <div class="component-content">
                π10³ scaling factor that creates recursive trust loops for stability
            </div>
        </div>

        <!-- 18/82 Principle -->
        <div class="component-box" style="left: 525px; top: 380px; width: 200px; height: 110px;">
            <div class="component-number-inside">8</div>
            <div class="component-label">18/82 Principle</div>
            <div class="component-content">
                Optimal resource allocation where 18% of indicators provide 82% of predictive power
            </div>
        </div>

        <!-- Final Result -->
        <div class="component-box" style="left: 525px; top: 480px; width: 200px; height: 110px;">
            <div class="component-number-inside">9</div>
            <div class="component-label">Stabilized Network</div>
            <div class="component-content">
                3,142x faster threat detection with bounded error propagation
            </div>
        </div>

        <!-- Arrows -->
        <svg width="800" height="650" style="position: absolute; top: 0; left: 0; z-index: 0;">
            <!-- Arrows to Tensor Operator -->
            <line x1="225" y1="130" x2="275" y2="180" stroke="#555555" stroke-width="2" />
            <polygon points="275,180 265,175 270,170" fill="#555555" />

            <line x1="525" y1="130" x2="475" y2="180" stroke="#555555" stroke-width="2" />
            <polygon points="475,180 485,175 480,170" fill="#555555" />

            <!-- Arrow from Tensor Operator to Tensor Product -->
            <line x1="375" y1="280" x2="375" y2="330" stroke="#555555" stroke-width="2" />
            <polygon points="375,330 370,320 380,320" fill="#555555" />

            <!-- Arrows to Fusion Operator -->
            <line x1="375" y1="380" x2="375" y2="430" stroke="#555555" stroke-width="2" />
            <polygon points="375,430 370,420 380,420" fill="#555555" />

            <line x1="225" y1="330" x2="275" y2="380" stroke="#555555" stroke-width="2" />
            <polygon points="275,380 265,375 270,370" fill="#555555" />

            <!-- Arrow from Fusion Operator to Circular Trust Topology -->
            <line x1="375" y1="480" x2="375" y2="530" stroke="#555555" stroke-width="2" />
            <polygon points="375,530 370,520 380,520" fill="#555555" />

            <!-- Arrow from Circular Trust Topology to Final Result -->
            <line x1="475" y1="530" x2="525" y2="530" stroke="#555555" stroke-width="2" />
            <polygon points="525,530 515,525 515,535" fill="#555555" />

            <!-- Arrow from 18/82 Principle to Final Result -->
            <line x1="625" y1="430" x2="625" y2="480" stroke="#555555" stroke-width="2" />
            <polygon points="625,480 620,470 630,470" fill="#555555" />

            <!-- Equation at the bottom -->
            <text x="375" y="600" text-anchor="middle" font-weight="bold" font-size="16" fill="#000">T = (A⊗B⊕C) × π10³</text>
        </svg>

        <!-- Universal Pattern Elements -->
        <div class="container-box" style="width: 650px; height: 60px; left: 75px; top: 510px;">
            <div class="container-label" style="font-size: 14px; top: 5px;">IMPLEMENTATION CONTEXT</div>
        </div>

        <div style="position: absolute; left: 100px; top: 530px; display: flex; justify-content: space-between; width: 600px;">
            <div style="font-size: 11px; text-align: center; width: 600px; padding-top: 5px;">
                "UUFT provides a mathematical framework for stabilizing complex networked systems through tensorial governance and bounded coherence."
            </div>
        </div>

        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Input Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Processing Operators</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Output Components</div>
            </div>
        </div>

        <!-- Inventor Label -->
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
