/**
 * NovaFuse Universal API Connector - Connector API Test
 *
 * This script tests the connector API implementation.
 */

const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');
const connectorRoutes = require('./src/routes/connector-routes');

// Create Express app
const app = express();
const PORT = 3099;

// Middleware
app.use(bodyParser.json());
app.use(express.static('public'));

// Routes
app.use('/api/connectors', connectorRoutes);

// Serve connector dashboard
app.get('/dashboard', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'connector-dashboard.html'));
});

// Start the server
app.listen(PORT, () => {
  console.log(`Connector API test server running on http://localhost:${PORT}`);
  console.log(`Dashboard available at http://localhost:${PORT}/dashboard`);
  console.log(`API available at http://localhost:${PORT}/api/connectors`);
});
