# NovaConnect Monitoring Infrastructure

## Overview

This document provides an overview of the monitoring infrastructure implemented for NovaConnect UAC to prepare it for Google Cloud Marketplace.

## Components

The monitoring infrastructure consists of the following components:

1. **Prometheus Metrics Collection**
   - Collects and exposes metrics for monitoring and alerting
   - Provides detailed insights into system performance and health
   - Exposes metrics at `/metrics` endpoint

2. **OpenTelemetry Distributed Tracing**
   - Provides end-to-end request tracing
   - Integrates with Google Cloud Trace
   - Supports multiple exporters (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Google Cloud)

3. **Google Cloud Operations Integration**
   - Integrates with Google Cloud Monitoring and Logging
   - Provides custom dashboards and alerts
   - Enables seamless monitoring in Google Cloud environment

4. **Health Check Endpoints**
   - Basic health check at `/health`
   - Detailed health information at `/health/detailed`
   - Readiness check for load balancers at `/health/ready`

## Configuration

The monitoring infrastructure can be configured using environment variables:

```
# Monitoring
METRICS_ENABLED=true
TRACING_ENABLED=true
TRACING_EXPORTER=console  # Options: console, zipkin, gcp
ZIPKIN_URL=http://localhost:9411/api/v2/spans

# Google Cloud
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
GOOGLE_CLOUD_MONITORING_ENABLED=false
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/credentials.json
```

## Available Metrics

The following metrics are available:

### HTTP Metrics
- `novaconnect_http_requests_total` - Total number of HTTP requests
- `novaconnect_http_request_duration_seconds` - HTTP request duration in seconds
- `novaconnect_api_errors_total` - Total number of API errors

### Connector Metrics
- `novaconnect_connector_requests_total` - Total number of connector requests
- `novaconnect_connector_request_duration_seconds` - Connector request duration in seconds
- `novaconnect_connector_health` - Connector health status (1 = healthy, 0 = unhealthy)
- `novaconnect_connector_response_time` - Connector response time in milliseconds

### Data Processing Metrics
- `novaconnect_data_normalization_duration_seconds` - Data normalization duration in seconds
- `novaconnect_remediation_workflows_total` - Total number of remediation workflows
- `novaconnect_remediation_workflow_duration_seconds` - Remediation workflow duration in seconds

### System Metrics
- `novaconnect_active_connections` - Number of active connections
- `novaconnect_rate_limit_exceeded_total` - Total number of rate limit exceeded events
- `novaconnect_authentication_success_total` - Total number of successful authentications
- `novaconnect_authentication_failure_total` - Total number of failed authentications
- `novaconnect_cache_hits_total` - Total number of cache hits
- `novaconnect_cache_misses_total` - Total number of cache misses

## Testing

To test the monitoring infrastructure, run:

```
npm run test:unit -- --testMatch='**/monitoring/*.test.js'
```

## Google Cloud Marketplace Integration

For Google Cloud Marketplace deployment, the monitoring infrastructure automatically integrates with Google Cloud Operations, providing:

1. **Seamless Monitoring** - Automatic integration with Google Cloud Monitoring
2. **Log Analysis** - Structured logs in Google Cloud Logging
3. **Trace Visualization** - Distributed traces in Google Cloud Trace
4. **Custom Dashboards** - Pre-configured dashboards for NovaConnect
5. **Alert Notifications** - Configurable alerts based on metrics and logs

## Next Steps

1. **Implement Custom Dashboards** - Create custom dashboards for specific use cases
2. **Configure Alert Policies** - Set up alert policies for critical metrics
3. **Integrate with PagerDuty** - Configure PagerDuty integration for on-call notifications
4. **Implement SLO Monitoring** - Define and monitor Service Level Objectives
5. **Enhance Tracing** - Add more detailed tracing for critical paths
