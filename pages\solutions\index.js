import React from 'react';
import Link from 'next/link';
import PageWithSidebar from '../../components/PageWithSidebar';

const Solutions = () => {
  const sidebarItems = [
    { label: 'By Industry', href: '/solutions/by-industry' },
    { label: 'By Use Case', href: '/solutions/by-use-case' },
    { label: 'By Compliance Framework', href: '/solutions/by-framework' },
    { label: 'Back to Home', href: '/' },
  ];

  return (
    <PageWithSidebar title="NovaFuse Solutions" sidebarItems={sidebarItems}>
      <div className="space-y-12">
        {/* Hero Section */}
        <div className="bg-blue-900 p-8 rounded-lg shadow-lg">
          <h1 className="text-3xl font-bold mb-4">NovaFuse Solutions</h1>
          <p className="text-xl mb-6">
            Explore our comprehensive compliance solutions tailored to your industry, use case, or required frameworks.
          </p>
        </div>

        {/* Solutions Categories */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* By Industry */}
          <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
            <h2 className="text-xl font-bold mb-4">By Industry</h2>
            <p className="text-sm text-blue-100 mb-6">
              Industry-specific compliance solutions tailored to the unique regulatory requirements of your sector.
            </p>
            <ul className="space-y-2 mb-6">
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">Healthcare</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">Financial Services</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">Technology</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">And more...</span>
              </li>
            </ul>
            <Link href="/solutions/by-industry" className="bg-white text-blue-700 px-4 py-2 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
              Explore Industry Solutions
            </Link>
          </div>

          {/* By Use Case */}
          <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
            <h2 className="text-xl font-bold mb-4">By Use Case</h2>
            <p className="text-sm text-blue-100 mb-6">
              Solutions designed to address specific compliance challenges and use cases across organizations.
            </p>
            <ul className="space-y-2 mb-6">
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">Compliance Automation</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">Risk Management</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">Audit Readiness</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">And more...</span>
              </li>
            </ul>
            <Link href="/solutions/by-use-case" className="bg-white text-blue-700 px-4 py-2 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
              Explore Use Case Solutions
            </Link>
          </div>

          {/* By Compliance Framework */}
          <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
            <h2 className="text-xl font-bold mb-4">By Compliance Framework</h2>
            <p className="text-sm text-blue-100 mb-6">
              Framework-specific solutions to help you achieve and maintain compliance with major standards and regulations.
            </p>
            <ul className="space-y-2 mb-6">
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">SOC 2</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">HIPAA</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">GDPR</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">And more...</span>
              </li>
            </ul>
            <Link href="/solutions/by-framework" className="bg-white text-blue-700 px-4 py-2 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
              Explore Framework Solutions
            </Link>
          </div>
        </div>

        {/* Universal API Connector Section */}
        <div className="bg-secondary p-6 rounded-lg shadow-md">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-2/3 mb-6 md:mb-0 md:pr-8">
              <h2 className="text-2xl font-bold mb-4">Powered by the Universal API Connector</h2>
              <p className="mb-4">
                All NovaFuse solutions are powered by our revolutionary Universal API Connector (UAC), enabling seamless integration with your existing systems and real-time compliance monitoring.
              </p>
              <p className="mb-4">
                The UAC's 100ms data normalization and bidirectional control capabilities ensure that your compliance solution works across your entire technology stack.
              </p>
              <Link href="/novaconnect-uac" className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                Learn More About the UAC
              </Link>
            </div>
            <div className="md:w-1/3">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Key UAC Features</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>100ms Data Normalization</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>Bidirectional Control</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>Universal Compatibility</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>AI-Powered Mapping</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-900 to-purple-900 p-8 rounded-lg shadow-lg border border-blue-400">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Ready to Transform Your Compliance Program?</h2>
            <p className="mb-6">
              Contact us to discuss how NovaFuse can address your specific compliance needs.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/contact" className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                Contact Us
              </Link>
              <Link href="/partner-empowerment" className="bg-white text-blue-700 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                Partner With Us
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
};

export default Solutions;
