const Joi = require('joi');

// Validation schemas
const schemas = {
  // Target validation schemas
  createTarget: Joi.object({
    name: Joi.string().required().min(3).max(100),
    description: Joi.string().required().max(500),
    category: Joi.string().required().valid('environmental', 'social', 'governance'),
    subcategory: Joi.string().optional().max(100),
    metricId: Joi.string().optional(),
    targetValue: Joi.string().required(),
    baselineValue: Joi.string().optional(),
    baselineDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    targetDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    unit: Joi.string().optional().max(20),
    status: Joi.string().required().valid('planned', 'in-progress', 'achieved', 'missed', 'cancelled'),
    owner: Joi.string().optional().max(100),
    initiatives: Joi.array().items(Joi.string()).optional(),
    milestones: Joi.array().items(
      Joi.object({
        name: Joi.string().required().min(1).max(100),
        description: Joi.string().optional().max(500),
        targetValue: Joi.string().required(),
        targetDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
        status: Joi.string().required().valid('planned', 'in-progress', 'achieved', 'missed', 'cancelled')
      })
    ).optional()
  }),
  
  updateTarget: Joi.object({
    name: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    category: Joi.string().optional().valid('environmental', 'social', 'governance'),
    subcategory: Joi.string().optional().max(100),
    metricId: Joi.string().optional().allow(null, ''),
    targetValue: Joi.string().optional(),
    baselineValue: Joi.string().optional().allow(null, ''),
    baselineDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/).allow(null, ''), // YYYY-MM-DD format
    targetDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    unit: Joi.string().optional().max(20),
    status: Joi.string().optional().valid('planned', 'in-progress', 'achieved', 'missed', 'cancelled'),
    owner: Joi.string().optional().max(100),
    initiatives: Joi.array().items(Joi.string()).optional(),
    milestones: Joi.array().items(
      Joi.object({
        id: Joi.string().optional(),
        name: Joi.string().required().min(1).max(100),
        description: Joi.string().optional().max(500),
        targetValue: Joi.string().required(),
        targetDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
        status: Joi.string().required().valid('planned', 'in-progress', 'achieved', 'missed', 'cancelled')
      })
    ).optional()
  }).min(1), // At least one field must be provided
  
  // Progress validation schemas
  createProgress: Joi.object({
    date: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    value: Joi.string().required(),
    percentComplete: Joi.number().optional().min(0).max(100),
    notes: Joi.string().optional().max(500)
  }),
  
  updateProgress: Joi.object({
    date: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    value: Joi.string().optional(),
    percentComplete: Joi.number().optional().min(0).max(100),
    notes: Joi.string().optional().max(500)
  }).min(1) // At least one field must be provided
};

/**
 * Middleware to validate request data against a schema
 * @param {string} schemaName - Name of the schema to validate against
 * @returns {Function} Express middleware function
 */
const validateRequest = (schemaName) => {
  return (req, res, next) => {
    const schema = schemas[schemaName];
    
    if (!schema) {
      return res.status(500).json({
        error: 'Internal Server Error',
        message: `Validation schema '${schemaName}' not found`
      });
    }
    
    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // Return all errors, not just the first one
      stripUnknown: true // Remove unknown fields
    });
    
    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        error: 'Bad Request',
        message: errorMessage
      });
    }
    
    // Replace request body with validated value
    req.body = value;
    next();
  };
};

module.exports = {
  validateRequest
};
