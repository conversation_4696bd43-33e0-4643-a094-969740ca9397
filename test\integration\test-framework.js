/**
 * NovaFuse Universal API Connector - Integration Test Framework
 * 
 * This module provides utilities for integration testing.
 */

const assert = require('assert');
const { createLogger } = require('../../src/utils/logger');

const logger = createLogger('integration-tests');

/**
 * Test suite class
 */
class TestSuite {
  /**
   * Create a new test suite
   * 
   * @param {string} name - The suite name
   */
  constructor(name) {
    this.name = name;
    this.tests = [];
    this.beforeEachFns = [];
    this.afterEachFns = [];
    this.beforeAllFns = [];
    this.afterAllFns = [];
  }

  /**
   * Add a test
   * 
   * @param {string} name - The test name
   * @param {Function} fn - The test function
   */
  test(name, fn) {
    this.tests.push({ name, fn });
  }

  /**
   * Add a function to run before each test
   * 
   * @param {Function} fn - The function
   */
  beforeEach(fn) {
    this.beforeEachFns.push(fn);
  }

  /**
   * Add a function to run after each test
   * 
   * @param {Function} fn - The function
   */
  afterEach(fn) {
    this.afterEachFns.push(fn);
  }

  /**
   * Add a function to run before all tests
   * 
   * @param {Function} fn - The function
   */
  beforeAll(fn) {
    this.beforeAllFns.push(fn);
  }

  /**
   * Add a function to run after all tests
   * 
   * @param {Function} fn - The function
   */
  afterAll(fn) {
    this.afterAllFns.push(fn);
  }

  /**
   * Run the test suite
   */
  async run() {
    logger.info(`Running test suite: ${this.name}`);
    
    const results = {
      name: this.name,
      passed: 0,
      failed: 0,
      skipped: 0,
      total: this.tests.length,
      failures: []
    };
    
    try {
      // Run beforeAll hooks
      for (const fn of this.beforeAllFns) {
        await fn();
      }
      
      // Run tests
      for (const test of this.tests) {
        try {
          logger.info(`Running test: ${test.name}`);
          
          // Run beforeEach hooks
          for (const fn of this.beforeEachFns) {
            await fn();
          }
          
          // Run test
          await test.fn();
          
          // Run afterEach hooks
          for (const fn of this.afterEachFns) {
            await fn();
          }
          
          logger.info(`Test passed: ${test.name}`);
          results.passed++;
        } catch (error) {
          logger.error(`Test failed: ${test.name}`, { error });
          results.failed++;
          results.failures.push({
            name: test.name,
            error: error.message,
            stack: error.stack
          });
          
          // Run afterEach hooks even if test fails
          try {
            for (const fn of this.afterEachFns) {
              await fn();
            }
          } catch (hookError) {
            logger.error(`AfterEach hook failed for test: ${test.name}`, { error: hookError });
          }
        }
      }
      
      // Run afterAll hooks
      for (const fn of this.afterAllFns) {
        await fn();
      }
    } catch (error) {
      logger.error(`Test suite failed: ${this.name}`, { error });
    }
    
    // Log results
    logger.info(`Test suite results: ${this.name}`, {
      passed: results.passed,
      failed: results.failed,
      skipped: results.skipped,
      total: results.total
    });
    
    if (results.failures.length > 0) {
      logger.error(`Test failures:`, { failures: results.failures });
    }
    
    return results;
  }
}

/**
 * Create a new test suite
 * 
 * @param {string} name - The suite name
 * @returns {TestSuite} - The test suite
 */
function createTestSuite(name) {
  return new TestSuite(name);
}

/**
 * Assert that a value is truthy
 * 
 * @param {*} value - The value to check
 * @param {string} message - The error message
 */
function assertTrue(value, message) {
  assert(value, message);
}

/**
 * Assert that a value is falsy
 * 
 * @param {*} value - The value to check
 * @param {string} message - The error message
 */
function assertFalse(value, message) {
  assert(!value, message);
}

/**
 * Assert that two values are equal
 * 
 * @param {*} actual - The actual value
 * @param {*} expected - The expected value
 * @param {string} message - The error message
 */
function assertEqual(actual, expected, message) {
  assert.strictEqual(actual, expected, message);
}

/**
 * Assert that two values are not equal
 * 
 * @param {*} actual - The actual value
 * @param {*} expected - The expected value
 * @param {string} message - The error message
 */
function assertNotEqual(actual, expected, message) {
  assert.notStrictEqual(actual, expected, message);
}

/**
 * Assert that a value is defined
 * 
 * @param {*} value - The value to check
 * @param {string} message - The error message
 */
function assertDefined(value, message) {
  assert(value !== undefined, message || 'Value is undefined');
}

/**
 * Assert that a value is null
 * 
 * @param {*} value - The value to check
 * @param {string} message - The error message
 */
function assertNull(value, message) {
  assert.strictEqual(value, null, message);
}

/**
 * Assert that a value is not null
 * 
 * @param {*} value - The value to check
 * @param {string} message - The error message
 */
function assertNotNull(value, message) {
  assert.notStrictEqual(value, null, message);
}

/**
 * Assert that a function throws an error
 * 
 * @param {Function} fn - The function to check
 * @param {string|RegExp} expected - The expected error message or pattern
 * @param {string} message - The error message
 */
function assertThrows(fn, expected, message) {
  assert.throws(fn, expected, message);
}

/**
 * Assert that a function does not throw an error
 * 
 * @param {Function} fn - The function to check
 * @param {string} message - The error message
 */
async function assertDoesNotThrow(fn, message) {
  try {
    await fn();
  } catch (error) {
    assert.fail(message || `Expected function not to throw, but it threw: ${error.message}`);
  }
}

module.exports = {
  createTestSuite,
  assertTrue,
  assertFalse,
  assertEqual,
  assertNotEqual,
  assertDefined,
  assertNull,
  assertNotNull,
  assertThrows,
  assertDoesNotThrow
};
