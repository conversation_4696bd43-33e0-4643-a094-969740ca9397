/**
 * NovaCore Compliance Profile Service
 * 
 * This service provides functionality for managing compliance profiles.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const { ComplianceProfile, Framework, Regulation } = require('../models');
const logger = require('../../../config/logger');
const { ValidationError, NotFoundError } = require('../../../api/utils/errors');

class ComplianceProfileService {
  /**
   * Create a new compliance profile
   * @param {Object} data - Compliance profile data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created compliance profile
   */
  async createComplianceProfile(data, userId) {
    try {
      logger.info('Creating new compliance profile', { 
        organizationId: data.organizationId, 
        name: data.name 
      });
      
      // Set created by and updated by
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Create compliance profile
      const complianceProfile = new ComplianceProfile(data);
      await complianceProfile.save();
      
      logger.info('Compliance profile created successfully', { id: complianceProfile._id });
      
      return complianceProfile;
    } catch (error) {
      logger.error('Error creating compliance profile', { error });
      throw error;
    }
  }
  
  /**
   * Get all compliance profiles
   * @param {string} organizationId - Organization ID
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Compliance profiles with pagination info
   */
  async getAllComplianceProfiles(organizationId, filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { name: 1 } } = options;
      
      // Build query
      const query = { organizationId };
      
      // Apply filters
      if (filter.name) {
        query.name = { $regex: filter.name, $options: 'i' };
      }
      
      if (filter.status) {
        query.status = filter.status;
      }
      
      if (filter.industry) {
        query['organizationDetails.industry'] = filter.industry;
      }
      
      if (filter.size) {
        query['organizationDetails.size'] = filter.size;
      }
      
      if (filter.country) {
        query['jurisdictions.country'] = filter.country;
      }
      
      if (filter.region) {
        query['jurisdictions.region'] = filter.region;
      }
      
      if (filter.dataType) {
        query['dataInventory.dataType'] = filter.dataType;
      }
      
      if (filter.classification) {
        query['dataInventory.classification'] = filter.classification;
      }
      
      if (filter.frameworkId) {
        query['applicableFrameworks.frameworkId'] = filter.frameworkId;
      }
      
      if (filter.regulationId) {
        query['applicableRegulations.regulationId'] = filter.regulationId;
      }
      
      if (filter.complianceStatus) {
        query['overallComplianceStatus.status'] = filter.complianceStatus;
      }
      
      if (filter.search) {
        query['$or'] = [
          { name: { $regex: filter.search, $options: 'i' } },
          { description: { $regex: filter.search, $options: 'i' } },
          { 'organizationDetails.name': { $regex: filter.search, $options: 'i' } }
        ];
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [complianceProfiles, total] = await Promise.all([
        ComplianceProfile.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        ComplianceProfile.countDocuments(query)
      ]);
      
      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;
      
      return {
        data: complianceProfiles,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting compliance profiles', { organizationId, error });
      throw error;
    }
  }
  
  /**
   * Get compliance profile by ID
   * @param {string} id - Compliance profile ID
   * @returns {Promise<Object>} - Compliance profile
   */
  async getComplianceProfileById(id) {
    try {
      const complianceProfile = await ComplianceProfile.findById(id);
      
      if (!complianceProfile) {
        throw new NotFoundError(`Compliance profile with ID ${id} not found`);
      }
      
      return complianceProfile;
    } catch (error) {
      logger.error('Error getting compliance profile by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Update compliance profile
   * @param {string} id - Compliance profile ID
   * @param {Object} data - Updated compliance profile data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated compliance profile
   */
  async updateComplianceProfile(id, data, userId) {
    try {
      // Get existing compliance profile
      const complianceProfile = await this.getComplianceProfileById(id);
      
      // Set updated by
      data.updatedBy = userId;
      
      // Update compliance profile
      Object.assign(complianceProfile, data);
      await complianceProfile.save();
      
      logger.info('Compliance profile updated successfully', { id });
      
      return complianceProfile;
    } catch (error) {
      logger.error('Error updating compliance profile', { id, error });
      throw error;
    }
  }
  
  /**
   * Delete compliance profile
   * @param {string} id - Compliance profile ID
   * @returns {Promise<boolean>} - Deletion success
   */
  async deleteComplianceProfile(id) {
    try {
      const result = await ComplianceProfile.findByIdAndDelete(id);
      
      if (!result) {
        throw new NotFoundError(`Compliance profile with ID ${id} not found`);
      }
      
      logger.info('Compliance profile deleted successfully', { id });
      
      return true;
    } catch (error) {
      logger.error('Error deleting compliance profile', { id, error });
      throw error;
    }
  }
  
  /**
   * Add applicable framework
   * @param {string} id - Compliance profile ID
   * @param {Object} framework - Framework data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated compliance profile
   */
  async addApplicableFramework(id, framework, userId) {
    try {
      // Get existing compliance profile
      const complianceProfile = await this.getComplianceProfileById(id);
      
      // Check if framework already exists
      if (complianceProfile.applicableFrameworks.some(f => 
        f.frameworkId.toString() === framework.frameworkId.toString()
      )) {
        throw new ValidationError(`Framework with ID ${framework.frameworkId} already exists`);
      }
      
      // Validate framework exists
      const frameworkExists = await Framework.findById(framework.frameworkId);
      
      if (!frameworkExists) {
        throw new ValidationError(`Framework with ID ${framework.frameworkId} not found`);
      }
      
      // Set framework name if not provided
      if (!framework.frameworkName) {
        framework.frameworkName = frameworkExists.name;
      }
      
      // Add applicable framework
      complianceProfile.applicableFrameworks.push(framework);
      
      // Set updated by
      complianceProfile.updatedBy = userId;
      
      // Save compliance profile
      await complianceProfile.save();
      
      // Update overall compliance status
      complianceProfile.updateOverallComplianceStatus();
      await complianceProfile.save();
      
      logger.info('Applicable framework added successfully', { 
        id, 
        frameworkId: framework.frameworkId 
      });
      
      return complianceProfile;
    } catch (error) {
      logger.error('Error adding applicable framework', { id, error });
      throw error;
    }
  }
  
  /**
   * Update applicable framework
   * @param {string} id - Compliance profile ID
   * @param {string} frameworkId - Framework ID
   * @param {Object} data - Updated framework data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated compliance profile
   */
  async updateApplicableFramework(id, frameworkId, data, userId) {
    try {
      // Get existing compliance profile
      const complianceProfile = await this.getComplianceProfileById(id);
      
      // Find framework
      const frameworkIndex = complianceProfile.applicableFrameworks.findIndex(f => 
        f.frameworkId.toString() === frameworkId.toString()
      );
      
      if (frameworkIndex === -1) {
        throw new NotFoundError(`Framework with ID ${frameworkId} not found`);
      }
      
      // Update framework
      complianceProfile.applicableFrameworks[frameworkIndex] = {
        ...complianceProfile.applicableFrameworks[frameworkIndex].toObject(),
        ...data,
        frameworkId // Ensure ID doesn't change
      };
      
      // Set updated by
      complianceProfile.updatedBy = userId;
      
      // Save compliance profile
      await complianceProfile.save();
      
      // Update overall compliance status
      complianceProfile.updateOverallComplianceStatus();
      await complianceProfile.save();
      
      logger.info('Applicable framework updated successfully', { 
        id, 
        frameworkId 
      });
      
      return complianceProfile;
    } catch (error) {
      logger.error('Error updating applicable framework', { id, frameworkId, error });
      throw error;
    }
  }
  
  /**
   * Remove applicable framework
   * @param {string} id - Compliance profile ID
   * @param {string} frameworkId - Framework ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated compliance profile
   */
  async removeApplicableFramework(id, frameworkId, userId) {
    try {
      // Get existing compliance profile
      const complianceProfile = await this.getComplianceProfileById(id);
      
      // Find framework
      const frameworkIndex = complianceProfile.applicableFrameworks.findIndex(f => 
        f.frameworkId.toString() === frameworkId.toString()
      );
      
      if (frameworkIndex === -1) {
        throw new NotFoundError(`Framework with ID ${frameworkId} not found`);
      }
      
      // Remove framework
      complianceProfile.applicableFrameworks.splice(frameworkIndex, 1);
      
      // Set updated by
      complianceProfile.updatedBy = userId;
      
      // Save compliance profile
      await complianceProfile.save();
      
      // Update overall compliance status
      complianceProfile.updateOverallComplianceStatus();
      await complianceProfile.save();
      
      logger.info('Applicable framework removed successfully', { 
        id, 
        frameworkId 
      });
      
      return complianceProfile;
    } catch (error) {
      logger.error('Error removing applicable framework', { id, frameworkId, error });
      throw error;
    }
  }
  
  /**
   * Add applicable regulation
   * @param {string} id - Compliance profile ID
   * @param {Object} regulation - Regulation data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated compliance profile
   */
  async addApplicableRegulation(id, regulation, userId) {
    try {
      // Get existing compliance profile
      const complianceProfile = await this.getComplianceProfileById(id);
      
      // Check if regulation already exists
      if (complianceProfile.applicableRegulations.some(r => 
        r.regulationId.toString() === regulation.regulationId.toString()
      )) {
        throw new ValidationError(`Regulation with ID ${regulation.regulationId} already exists`);
      }
      
      // Validate regulation exists
      const regulationExists = await Regulation.findById(regulation.regulationId);
      
      if (!regulationExists) {
        throw new ValidationError(`Regulation with ID ${regulation.regulationId} not found`);
      }
      
      // Set regulation name if not provided
      if (!regulation.regulationName) {
        regulation.regulationName = regulationExists.name;
      }
      
      // Add applicable regulation
      complianceProfile.applicableRegulations.push(regulation);
      
      // Set updated by
      complianceProfile.updatedBy = userId;
      
      // Save compliance profile
      await complianceProfile.save();
      
      logger.info('Applicable regulation added successfully', { 
        id, 
        regulationId: regulation.regulationId 
      });
      
      return complianceProfile;
    } catch (error) {
      logger.error('Error adding applicable regulation', { id, error });
      throw error;
    }
  }
  
  /**
   * Update applicable regulation
   * @param {string} id - Compliance profile ID
   * @param {string} regulationId - Regulation ID
   * @param {Object} data - Updated regulation data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated compliance profile
   */
  async updateApplicableRegulation(id, regulationId, data, userId) {
    try {
      // Get existing compliance profile
      const complianceProfile = await this.getComplianceProfileById(id);
      
      // Find regulation
      const regulationIndex = complianceProfile.applicableRegulations.findIndex(r => 
        r.regulationId.toString() === regulationId.toString()
      );
      
      if (regulationIndex === -1) {
        throw new NotFoundError(`Regulation with ID ${regulationId} not found`);
      }
      
      // Update regulation
      complianceProfile.applicableRegulations[regulationIndex] = {
        ...complianceProfile.applicableRegulations[regulationIndex].toObject(),
        ...data,
        regulationId // Ensure ID doesn't change
      };
      
      // Set updated by
      complianceProfile.updatedBy = userId;
      
      // Save compliance profile
      await complianceProfile.save();
      
      logger.info('Applicable regulation updated successfully', { 
        id, 
        regulationId 
      });
      
      return complianceProfile;
    } catch (error) {
      logger.error('Error updating applicable regulation', { id, regulationId, error });
      throw error;
    }
  }
  
  /**
   * Add data inventory item
   * @param {string} id - Compliance profile ID
   * @param {Object} dataItem - Data inventory item
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated compliance profile
   */
  async addDataInventoryItem(id, dataItem, userId) {
    try {
      // Get existing compliance profile
      const complianceProfile = await this.getComplianceProfileById(id);
      
      // Check if data type already exists
      if (complianceProfile.dataInventory.some(item => item.dataType === dataItem.dataType)) {
        throw new ValidationError(`Data type ${dataItem.dataType} already exists`);
      }
      
      // Add data inventory item
      complianceProfile.dataInventory.push(dataItem);
      
      // Set updated by
      complianceProfile.updatedBy = userId;
      
      // Save compliance profile
      await complianceProfile.save();
      
      logger.info('Data inventory item added successfully', { 
        id, 
        dataType: dataItem.dataType 
      });
      
      return complianceProfile;
    } catch (error) {
      logger.error('Error adding data inventory item', { id, error });
      throw error;
    }
  }
  
  /**
   * Add business activity
   * @param {string} id - Compliance profile ID
   * @param {Object} activity - Business activity
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated compliance profile
   */
  async addBusinessActivity(id, activity, userId) {
    try {
      // Get existing compliance profile
      const complianceProfile = await this.getComplianceProfileById(id);
      
      // Check if activity already exists
      if (complianceProfile.businessActivities.some(a => a.name === activity.name)) {
        throw new ValidationError(`Business activity ${activity.name} already exists`);
      }
      
      // Add business activity
      complianceProfile.businessActivities.push(activity);
      
      // Set updated by
      complianceProfile.updatedBy = userId;
      
      // Save compliance profile
      await complianceProfile.save();
      
      logger.info('Business activity added successfully', { 
        id, 
        activityName: activity.name 
      });
      
      return complianceProfile;
    } catch (error) {
      logger.error('Error adding business activity', { id, error });
      throw error;
    }
  }
  
  /**
   * Update overall compliance status
   * @param {string} id - Compliance profile ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated compliance profile
   */
  async updateOverallComplianceStatus(id, userId) {
    try {
      // Get existing compliance profile
      const complianceProfile = await this.getComplianceProfileById(id);
      
      // Update overall compliance status
      complianceProfile.updateOverallComplianceStatus();
      
      // Set updated by
      complianceProfile.updatedBy = userId;
      
      // Save compliance profile
      await complianceProfile.save();
      
      logger.info('Overall compliance status updated successfully', { id });
      
      return complianceProfile;
    } catch (error) {
      logger.error('Error updating overall compliance status', { id, error });
      throw error;
    }
  }
  
  /**
   * Find compliance profiles by organization
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Array>} - Compliance profiles
   */
  async findByOrganization(organizationId) {
    try {
      return await ComplianceProfile.findByOrganization(organizationId);
    } catch (error) {
      logger.error('Error finding compliance profiles by organization', { organizationId, error });
      throw error;
    }
  }
  
  /**
   * Find compliance profiles by framework
   * @param {string} frameworkId - Framework ID
   * @returns {Promise<Array>} - Compliance profiles
   */
  async findByFramework(frameworkId) {
    try {
      return await ComplianceProfile.findByFramework(frameworkId);
    } catch (error) {
      logger.error('Error finding compliance profiles by framework', { frameworkId, error });
      throw error;
    }
  }
  
  /**
   * Find compliance profiles by regulation
   * @param {string} regulationId - Regulation ID
   * @returns {Promise<Array>} - Compliance profiles
   */
  async findByRegulation(regulationId) {
    try {
      return await ComplianceProfile.findByRegulation(regulationId);
    } catch (error) {
      logger.error('Error finding compliance profiles by regulation', { regulationId, error });
      throw error;
    }
  }
  
  /**
   * Find compliance profiles by compliance status
   * @param {string} status - Compliance status
   * @returns {Promise<Array>} - Compliance profiles
   */
  async findByComplianceStatus(status) {
    try {
      return await ComplianceProfile.findByComplianceStatus(status);
    } catch (error) {
      logger.error('Error finding compliance profiles by compliance status', { status, error });
      throw error;
    }
  }
  
  /**
   * Find compliance profiles by jurisdiction
   * @param {string} country - Country
   * @param {string} region - Region
   * @returns {Promise<Array>} - Compliance profiles
   */
  async findByJurisdiction(country, region) {
    try {
      return await ComplianceProfile.findByJurisdiction(country, region);
    } catch (error) {
      logger.error('Error finding compliance profiles by jurisdiction', { country, region, error });
      throw error;
    }
  }
  
  /**
   * Find compliance profiles by data type
   * @param {string} dataType - Data type
   * @returns {Promise<Array>} - Compliance profiles
   */
  async findByDataType(dataType) {
    try {
      return await ComplianceProfile.findByDataType(dataType);
    } catch (error) {
      logger.error('Error finding compliance profiles by data type', { dataType, error });
      throw error;
    }
  }
}

module.exports = new ComplianceProfileService();
