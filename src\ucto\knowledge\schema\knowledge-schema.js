/**
 * Compliance Knowledge Base Schema
 * 
 * This schema defines the structure and components of the UCTO Compliance Knowledge Base.
 */

/**
 * Knowledge base schema definition
 * @type {Object}
 */
const knowledgeSchema = {
  // Metadata
  metadata: {
    name: "UCTO Compliance Knowledge Base",
    description: "Centralized repository of compliance information, best practices, and guidance",
    version: "1.0.0"
  },
  
  // Entity types
  entityTypes: [
    {
      id: "framework",
      name: "Compliance Framework",
      description: "A compliance framework or standard",
      properties: [
        {
          name: "id",
          type: "string",
          description: "Unique identifier for the framework",
          required: true
        },
        {
          name: "name",
          type: "string",
          description: "Name of the framework",
          required: true
        },
        {
          name: "description",
          type: "string",
          description: "Description of the framework",
          required: true
        },
        {
          name: "version",
          type: "string",
          description: "Version of the framework",
          required: false
        },
        {
          name: "issuer",
          type: "string",
          description: "Organization that issued the framework",
          required: false
        },
        {
          name: "url",
          type: "string",
          description: "URL to the framework documentation",
          required: false
        },
        {
          name: "category",
          type: "string",
          description: "Category of the framework",
          required: false
        },
        {
          name: "tags",
          type: "array",
          description: "Tags for the framework",
          required: false,
          items: {
            type: "string"
          }
        },
        {
          name: "domains",
          type: "array",
          description: "Domains or sections of the framework",
          required: false,
          items: {
            type: "object",
            properties: {
              id: {
                type: "string",
                description: "Unique identifier for the domain"
              },
              name: {
                type: "string",
                description: "Name of the domain"
              },
              description: {
                type: "string",
                description: "Description of the domain"
              }
            }
          }
        }
      ]
    },
    {
      id: "control",
      name: "Control",
      description: "A compliance control or requirement",
      properties: [
        {
          name: "id",
          type: "string",
          description: "Unique identifier for the control",
          required: true
        },
        {
          name: "name",
          type: "string",
          description: "Name of the control",
          required: true
        },
        {
          name: "description",
          type: "string",
          description: "Description of the control",
          required: true
        },
        {
          name: "framework_id",
          type: "string",
          description: "ID of the framework this control belongs to",
          required: true
        },
        {
          name: "domain_id",
          type: "string",
          description: "ID of the domain this control belongs to",
          required: false
        },
        {
          name: "control_number",
          type: "string",
          description: "Control number or identifier within the framework",
          required: false
        },
        {
          name: "objective",
          type: "string",
          description: "Objective of the control",
          required: false
        },
        {
          name: "risk",
          type: "string",
          description: "Risk addressed by the control",
          required: false
        },
        {
          name: "implementation_guidance",
          type: "string",
          description: "Guidance for implementing the control",
          required: false
        },
        {
          name: "assessment_guidance",
          type: "string",
          description: "Guidance for assessing the control",
          required: false
        },
        {
          name: "evidence_requirements",
          type: "array",
          description: "Evidence requirements for the control",
          required: false,
          items: {
            type: "string"
          }
        },
        {
          name: "related_controls",
          type: "array",
          description: "Related controls",
          required: false,
          items: {
            type: "string"
          }
        },
        {
          name: "tags",
          type: "array",
          description: "Tags for the control",
          required: false,
          items: {
            type: "string"
          }
        }
      ]
    },
    {
      id: "guidance",
      name: "Guidance",
      description: "Guidance or best practice for compliance",
      properties: [
        {
          name: "id",
          type: "string",
          description: "Unique identifier for the guidance",
          required: true
        },
        {
          name: "title",
          type: "string",
          description: "Title of the guidance",
          required: true
        },
        {
          name: "content",
          type: "string",
          description: "Content of the guidance",
          required: true
        },
        {
          name: "framework_id",
          type: "string",
          description: "ID of the framework this guidance relates to",
          required: false
        },
        {
          name: "control_id",
          type: "string",
          description: "ID of the control this guidance relates to",
          required: false
        },
        {
          name: "author",
          type: "string",
          description: "Author of the guidance",
          required: false
        },
        {
          name: "created_at",
          type: "string",
          description: "Creation date of the guidance",
          required: false
        },
        {
          name: "updated_at",
          type: "string",
          description: "Last update date of the guidance",
          required: false
        },
        {
          name: "category",
          type: "string",
          description: "Category of the guidance",
          required: false
        },
        {
          name: "tags",
          type: "array",
          description: "Tags for the guidance",
          required: false,
          items: {
            type: "string"
          }
        }
      ]
    },
    {
      id: "resource",
      name: "Resource",
      description: "A compliance resource such as a template, document, or tool",
      properties: [
        {
          name: "id",
          type: "string",
          description: "Unique identifier for the resource",
          required: true
        },
        {
          name: "name",
          type: "string",
          description: "Name of the resource",
          required: true
        },
        {
          name: "description",
          type: "string",
          description: "Description of the resource",
          required: true
        },
        {
          name: "type",
          type: "string",
          description: "Type of resource",
          required: true,
          enum: ["template", "document", "tool", "checklist", "policy", "procedure", "other"]
        },
        {
          name: "url",
          type: "string",
          description: "URL to the resource",
          required: false
        },
        {
          name: "file_path",
          type: "string",
          description: "Path to the resource file",
          required: false
        },
        {
          name: "framework_id",
          type: "string",
          description: "ID of the framework this resource relates to",
          required: false
        },
        {
          name: "control_id",
          type: "string",
          description: "ID of the control this resource relates to",
          required: false
        },
        {
          name: "author",
          type: "string",
          description: "Author of the resource",
          required: false
        },
        {
          name: "created_at",
          type: "string",
          description: "Creation date of the resource",
          required: false
        },
        {
          name: "updated_at",
          type: "string",
          description: "Last update date of the resource",
          required: false
        },
        {
          name: "tags",
          type: "array",
          description: "Tags for the resource",
          required: false,
          items: {
            type: "string"
          }
        }
      ]
    },
    {
      id: "glossary_term",
      name: "Glossary Term",
      description: "A term in the compliance glossary",
      properties: [
        {
          name: "id",
          type: "string",
          description: "Unique identifier for the term",
          required: true
        },
        {
          name: "term",
          type: "string",
          description: "The term",
          required: true
        },
        {
          name: "definition",
          type: "string",
          description: "Definition of the term",
          required: true
        },
        {
          name: "abbreviation",
          type: "string",
          description: "Abbreviation of the term",
          required: false
        },
        {
          name: "framework_id",
          type: "string",
          description: "ID of the framework this term relates to",
          required: false
        },
        {
          name: "source",
          type: "string",
          description: "Source of the term definition",
          required: false
        },
        {
          name: "url",
          type: "string",
          description: "URL to more information about the term",
          required: false
        },
        {
          name: "tags",
          type: "array",
          description: "Tags for the term",
          required: false,
          items: {
            type: "string"
          }
        }
      ]
    },
    {
      id: "faq",
      name: "FAQ",
      description: "A frequently asked question about compliance",
      properties: [
        {
          name: "id",
          type: "string",
          description: "Unique identifier for the FAQ",
          required: true
        },
        {
          name: "question",
          type: "string",
          description: "The question",
          required: true
        },
        {
          name: "answer",
          type: "string",
          description: "The answer",
          required: true
        },
        {
          name: "framework_id",
          type: "string",
          description: "ID of the framework this FAQ relates to",
          required: false
        },
        {
          name: "control_id",
          type: "string",
          description: "ID of the control this FAQ relates to",
          required: false
        },
        {
          name: "category",
          type: "string",
          description: "Category of the FAQ",
          required: false
        },
        {
          name: "tags",
          type: "array",
          description: "Tags for the FAQ",
          required: false,
          items: {
            type: "string"
          }
        }
      ]
    }
  ],
  
  // Relationship types
  relationshipTypes: [
    {
      id: "framework_to_control",
      name: "Framework to Control",
      description: "Relationship between a framework and a control",
      sourceType: "framework",
      targetType: "control",
      properties: [
        {
          name: "relationship_type",
          type: "string",
          description: "Type of relationship",
          required: false,
          enum: ["contains", "references", "maps_to"]
        }
      ]
    },
    {
      id: "control_to_control",
      name: "Control to Control",
      description: "Relationship between controls",
      sourceType: "control",
      targetType: "control",
      properties: [
        {
          name: "relationship_type",
          type: "string",
          description: "Type of relationship",
          required: true,
          enum: ["related_to", "depends_on", "enhances", "conflicts_with", "equivalent_to", "maps_to"]
        },
        {
          name: "strength",
          type: "string",
          description: "Strength of the relationship",
          required: false,
          enum: ["exact", "strong", "moderate", "weak"]
        },
        {
          name: "notes",
          type: "string",
          description: "Notes about the relationship",
          required: false
        }
      ]
    },
    {
      id: "control_to_guidance",
      name: "Control to Guidance",
      description: "Relationship between a control and guidance",
      sourceType: "control",
      targetType: "guidance",
      properties: [
        {
          name: "relationship_type",
          type: "string",
          description: "Type of relationship",
          required: false,
          enum: ["has_guidance", "references"]
        }
      ]
    },
    {
      id: "control_to_resource",
      name: "Control to Resource",
      description: "Relationship between a control and a resource",
      sourceType: "control",
      targetType: "resource",
      properties: [
        {
          name: "relationship_type",
          type: "string",
          description: "Type of relationship",
          required: false,
          enum: ["has_resource", "references"]
        }
      ]
    }
  ],
  
  // Search configuration
  search: {
    indexedFields: [
      {
        entityType: "framework",
        fields: ["name", "description", "issuer", "category", "tags"]
      },
      {
        entityType: "control",
        fields: ["name", "description", "objective", "risk", "implementation_guidance", "assessment_guidance", "tags"]
      },
      {
        entityType: "guidance",
        fields: ["title", "content", "author", "category", "tags"]
      },
      {
        entityType: "resource",
        fields: ["name", "description", "type", "author", "tags"]
      },
      {
        entityType: "glossary_term",
        fields: ["term", "definition", "abbreviation", "source", "tags"]
      },
      {
        entityType: "faq",
        fields: ["question", "answer", "category", "tags"]
      }
    ],
    analyzers: [
      {
        name: "standard",
        type: "standard",
        stopWords: ["a", "an", "the", "and", "or", "but", "if", "then", "else", "when", "to", "of", "for", "with", "by", "at", "from"]
      },
      {
        name: "keyword",
        type: "keyword"
      }
    ]
  }
};

module.exports = knowledgeSchema;
