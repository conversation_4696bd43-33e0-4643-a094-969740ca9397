/**
 * Database Indexes Configuration
 * 
 * This module defines the indexes for the MongoDB collections used by the Privacy Management API.
 * Proper indexing is crucial for query performance, especially as the data grows.
 */

/**
 * Create indexes for all collections
 * @param {Object} db - MongoDB database connection
 */
async function createIndexes(db) {
  try {
    console.log('Creating indexes for Privacy Management API collections...');
    
    // Data Processing Activities collection indexes
    await db.collection('dataProcessingActivities').createIndexes([
      { key: { name: 1 }, name: 'name_index' },
      { key: { status: 1 }, name: 'status_index' },
      { key: { legalBasis: 1 }, name: 'legalBasis_index' },
      { key: { createdAt: -1 }, name: 'createdAt_index' },
      { key: { updatedAt: -1 }, name: 'updatedAt_index' },
      { key: { name: 'text', description: 'text', purpose: 'text' }, name: 'text_search_index' }
    ]);
    console.log('Created indexes for dataProcessingActivities collection');
    
    // Data Subject Requests collection indexes
    await db.collection('dataSubjectRequests').createIndexes([
      { key: { dataSubjectEmail: 1 }, name: 'dataSubjectEmail_index' },
      { key: { requestType: 1 }, name: 'requestType_index' },
      { key: { status: 1 }, name: 'status_index' },
      { key: { dueDate: 1 }, name: 'dueDate_index' },
      { key: { createdAt: -1 }, name: 'createdAt_index' },
      { key: { updatedAt: -1 }, name: 'updatedAt_index' },
      { key: { dataSubjectName: 'text', dataSubjectEmail: 'text', requestDetails: 'text' }, name: 'text_search_index' }
    ]);
    console.log('Created indexes for dataSubjectRequests collection');
    
    // Consent Records collection indexes
    await db.collection('consentRecords').createIndexes([
      { key: { dataSubjectId: 1 }, name: 'dataSubjectId_index' },
      { key: { dataSubjectEmail: 1 }, name: 'dataSubjectEmail_index' },
      { key: { consentType: 1 }, name: 'consentType_index' },
      { key: { status: 1 }, name: 'status_index' },
      { key: { expiryDate: 1 }, name: 'expiryDate_index' },
      { key: { collectionTimestamp: -1 }, name: 'collectionTimestamp_index' },
      { key: { withdrawalTimestamp: -1 }, name: 'withdrawalTimestamp_index' },
      { key: { dataSubjectName: 'text', dataSubjectEmail: 'text', consentDetails: 'text' }, name: 'text_search_index' }
    ]);
    console.log('Created indexes for consentRecords collection');
    
    // Privacy Notices collection indexes
    await db.collection('privacyNotices').createIndexes([
      { key: { title: 1 }, name: 'title_index' },
      { key: { version: 1 }, name: 'version_index' },
      { key: { status: 1 }, name: 'status_index' },
      { key: { effectiveDate: -1 }, name: 'effectiveDate_index' },
      { key: { audience: 1 }, name: 'audience_index' },
      { key: { language: 1 }, name: 'language_index' },
      { key: { title: 'text', content: 'text' }, name: 'text_search_index' }
    ]);
    console.log('Created indexes for privacyNotices collection');
    
    // Data Breaches collection indexes
    await db.collection('dataBreaches').createIndexes([
      { key: { title: 1 }, name: 'title_index' },
      { key: { status: 1 }, name: 'status_index' },
      { key: { severity: 1 }, name: 'severity_index' },
      { key: { detectionDate: -1 }, name: 'detectionDate_index' },
      { key: { notificationRequired: 1 }, name: 'notificationRequired_index' },
      { key: { supervisoryAuthorityNotified: 1 }, name: 'supervisoryAuthorityNotified_index' },
      { key: { dataSubjectsNotified: 1 }, name: 'dataSubjectsNotified_index' },
      { key: { title: 'text', description: 'text', affectedDataCategories: 'text', affectedDataSubjects: 'text' }, name: 'text_search_index' }
    ]);
    console.log('Created indexes for dataBreaches collection');
    
    // Notifications collection indexes
    await db.collection('notifications').createIndexes([
      { key: { recipient: 1 }, name: 'recipient_index' },
      { key: { type: 1 }, name: 'type_index' },
      { key: { status: 1 }, name: 'status_index' },
      { key: { priority: 1 }, name: 'priority_index' },
      { key: { createdAt: -1 }, name: 'createdAt_index' },
      { key: { sentAt: -1 }, name: 'sentAt_index' },
      { key: { readAt: -1 }, name: 'readAt_index' },
      { key: { relatedEntityType: 1, relatedEntityId: 1 }, name: 'relatedEntity_index' },
      { key: { subject: 'text', content: 'text' }, name: 'text_search_index' }
    ]);
    console.log('Created indexes for notifications collection');
    
    // Data Systems collection indexes
    await db.collection('dataSystems').createIndexes([
      { key: { name: 1 }, name: 'name_index' },
      { key: { type: 1 }, name: 'type_index' },
      { key: { status: 1 }, name: 'status_index' },
      { key: { supportedRequestTypes: 1 }, name: 'supportedRequestTypes_index' },
      { key: { name: 'text', description: 'text', dataCategories: 'text' }, name: 'text_search_index' }
    ]);
    console.log('Created indexes for dataSystems collection');
    
    // Integration Configurations collection indexes
    await db.collection('integrationConfigurations').createIndexes([
      { key: { integrationId: 1 }, name: 'integrationId_index', unique: true },
      { key: { status: 1 }, name: 'status_index' },
      { key: { createdAt: -1 }, name: 'createdAt_index' },
      { key: { updatedAt: -1 }, name: 'updatedAt_index' }
    ]);
    console.log('Created indexes for integrationConfigurations collection');
    
    // Integration Logs collection indexes
    await db.collection('integrationLogs').createIndexes([
      { key: { integrationId: 1 }, name: 'integrationId_index' },
      { key: { action: 1 }, name: 'action_index' },
      { key: { status: 1 }, name: 'status_index' },
      { key: { timestamp: -1 }, name: 'timestamp_index' },
      { key: { requestId: 1 }, name: 'requestId_index' },
      { key: { userId: 1 }, name: 'userId_index' }
    ]);
    console.log('Created indexes for integrationLogs collection');
    
    // Users collection indexes
    await db.collection('users').createIndexes([
      { key: { username: 1 }, name: 'username_index', unique: true },
      { key: { email: 1 }, name: 'email_index', unique: true },
      { key: { role: 1 }, name: 'role_index' },
      { key: { status: 1 }, name: 'status_index' },
      { key: { lastLoginAt: -1 }, name: 'lastLoginAt_index' },
      { key: { username: 'text', email: 'text', firstName: 'text', lastName: 'text' }, name: 'text_search_index' }
    ]);
    console.log('Created indexes for users collection');
    
    // Audit Logs collection indexes
    await db.collection('auditLogs').createIndexes([
      { key: { action: 1 }, name: 'action_index' },
      { key: { entityType: 1 }, name: 'entityType_index' },
      { key: { entityId: 1 }, name: 'entityId_index' },
      { key: { userId: 1 }, name: 'userId_index' },
      { key: { timestamp: -1 }, name: 'timestamp_index' },
      { key: { ipAddress: 1 }, name: 'ipAddress_index' }
    ]);
    console.log('Created indexes for auditLogs collection');
    
    console.log('All indexes created successfully');
  } catch (error) {
    console.error('Error creating indexes:', error);
    throw error;
  }
}

/**
 * Drop all indexes for all collections
 * @param {Object} db - MongoDB database connection
 */
async function dropIndexes(db) {
  try {
    console.log('Dropping indexes for Privacy Management API collections...');
    
    const collections = [
      'dataProcessingActivities',
      'dataSubjectRequests',
      'consentRecords',
      'privacyNotices',
      'dataBreaches',
      'notifications',
      'dataSystems',
      'integrationConfigurations',
      'integrationLogs',
      'users',
      'auditLogs'
    ];
    
    for (const collection of collections) {
      await db.collection(collection).dropIndexes();
      console.log(`Dropped indexes for ${collection} collection`);
    }
    
    console.log('All indexes dropped successfully');
  } catch (error) {
    console.error('Error dropping indexes:', error);
    throw error;
  }
}

module.exports = {
  createIndexes,
  dropIndexes
};
