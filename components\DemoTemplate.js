import React, { useState } from 'react';
import Link from 'next/link';

/**
 * Standardized Demo Template Component
 * 
 * This component provides a consistent structure for all NovaFuse demos.
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Demo title
 * @param {string} props.description - Demo description
 * @param {Array} props.features - List of key features
 * @param {Array} props.tabs - List of tabs for the demo
 * @param {Array} props.scenarios - List of demo scenarios
 * @param {Object} props.documentation - Documentation links
 * @param {React.ReactNode} props.children - Child components
 * @returns {React.ReactNode} - Rendered component
 */
const DemoTemplate = ({
  title,
  description,
  features = [],
  tabs = [],
  scenarios = [],
  documentation = {},
  children
}) => {
  const [activeTab, setActiveTab] = useState(tabs.length > 0 ? tabs[0].id : '');
  const [activeScenario, setActiveScenario] = useState('');
  const [demoResult, setDemoResult] = useState(null);
  const [demoStatus, setDemoStatus] = useState('idle'); // idle, loading, success, error

  // Handle tab change
  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    setDemoResult(null);
    setDemoStatus('idle');
  };

  // Handle scenario selection
  const handleScenarioSelect = (scenarioId) => {
    setActiveScenario(scenarioId);
    setDemoResult(null);
    setDemoStatus('idle');
  };

  // Run demo
  const runDemo = async () => {
    if (!activeScenario) return;
    
    setDemoStatus('loading');
    
    // Find the active scenario
    const scenario = scenarios.find(s => s.id === activeScenario);
    
    try {
      // Execute the scenario function if provided
      if (scenario && typeof scenario.execute === 'function') {
        const result = await scenario.execute();
        setDemoResult(result);
        setDemoStatus('success');
      }
    } catch (error) {
      console.error('Demo execution error:', error);
      setDemoResult({ error: error.message });
      setDemoStatus('error');
    }
  };

  return (
    <div className="demo-template bg-primary text-white">
      {/* Header Section */}
      <div className="accent-bg text-white rounded-lg p-8 mb-8">
        <div className="flex justify-center mb-4">
          <div className="bg-blue-800 bg-opacity-70 px-4 py-2 rounded-full text-sm font-bold inline-flex items-center">
            <span className="mr-2">🔌</span>
            <span>NOVAFUSE DEMO</span>
          </div>
        </div>
        
        <h2 className="text-3xl md:text-4xl font-bold mb-4 text-center">{title}</h2>
        
        <p className="text-xl mb-6 text-center max-w-3xl mx-auto">
          {description}
        </p>
        
        {/* Key Features */}
        {features.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-blue-800 bg-opacity-50 p-4 rounded-lg">
                <div className="flex items-center mb-2">
                  <span className="text-blue-300 mr-2">{feature.icon || '✓'}</span>
                  <h3 className="font-semibold">{feature.title}</h3>
                </div>
                <p className="text-sm text-blue-100">{feature.description}</p>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Demo Content */}
      <div className="bg-secondary rounded-lg p-6 mb-8">
        {/* Tabs Navigation */}
        {tabs.length > 0 && (
          <div className="mb-6">
            <div className="border-b border-gray-700">
              <nav className="flex -mb-px space-x-8">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-500'
                        : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                    }`}
                    onClick={() => handleTabChange(tab.id)}
                  >
                    {tab.icon && <span className="mr-2">{tab.icon}</span>}
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>
          </div>
        )}

        {/* Scenario Selection */}
        {scenarios.length > 0 && (
          <div className="mb-6">
            <h3 className="text-xl font-semibold mb-4">Select a Demo Scenario</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {scenarios
                .filter(scenario => !scenario.tabId || scenario.tabId === activeTab)
                .map((scenario) => (
                  <div
                    key={scenario.id}
                    className={`p-4 rounded-lg cursor-pointer border ${
                      activeScenario === scenario.id
                        ? 'border-blue-500 bg-blue-900 bg-opacity-30'
                        : 'border-gray-700 hover:border-gray-500'
                    }`}
                    onClick={() => handleScenarioSelect(scenario.id)}
                  >
                    <div className="flex items-center mb-2">
                      <span className={`mr-2 ${scenario.statusColor || 'text-blue-500'}`}>
                        {scenario.icon || '•'}
                      </span>
                      <h4 className="font-medium">{scenario.name}</h4>
                    </div>
                    <p className="text-sm text-gray-400">{scenario.description}</p>
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Run Demo Button */}
        {activeScenario && (
          <div className="mb-6">
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-bold disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={runDemo}
              disabled={demoStatus === 'loading'}
            >
              {demoStatus === 'loading' ? 'Running Demo...' : 'Run Demo'}
            </button>
          </div>
        )}

        {/* Demo Results */}
        {demoResult && (
          <div className="mt-6">
            <h3 className="text-xl font-semibold mb-4">Demo Results</h3>
            <div className="bg-gray-900 rounded-lg p-4 overflow-auto">
              <pre className="text-sm text-gray-300 whitespace-pre-wrap">
                {typeof demoResult === 'string'
                  ? demoResult
                  : JSON.stringify(demoResult, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* Custom Demo Content */}
        {children}
      </div>

      {/* Documentation Section */}
      {Object.keys(documentation).length > 0 && (
        <div className="bg-secondary rounded-lg p-6">
          <h3 className="text-xl font-semibold mb-4">Documentation & Resources</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(documentation).map(([key, value]) => (
              <div key={key} className="border border-gray-700 rounded-lg p-4">
                <h4 className="font-medium mb-2">{value.title}</h4>
                <p className="text-sm text-gray-400 mb-3">{value.description}</p>
                <Link href={value.url} className="text-blue-500 hover:text-blue-400 text-sm">
                  View Documentation →
                </Link>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DemoTemplate;
