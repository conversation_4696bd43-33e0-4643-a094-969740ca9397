730eb4067425ade72111f1f5601cd97d
/**
 * NovaConnect UAC End-to-End Tests
 * 
 * This test suite validates the end-to-end functionality of the Universal API Connector.
 */

const request = require('supertest');
const mongoose = require('mongoose');
const {
  MongoMemoryServer
} = require('mongodb-memory-server');
const app = require('../../app');
const {
  ConnectorRegistry
} = require('../../src/registry/connector-registry');
const {
  FeatureFlagService
} = require('../../src/services/feature-flag-service');
const {
  EncryptionService
} = require('../../src/security/encryption-service');
let mongoServer;
let server;
let connectorRegistry;
let featureFlagService;
let encryptionService;
let authToken;

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'Test@123456',
  name: 'Test User'
};
const testConnector = {
  name: 'Test Connector',
  type: 'REST',
  description: 'Test connector for E2E tests',
  config: {
    baseUrl: 'https://api.example.com',
    authType: 'oauth2',
    headers: {
      'Content-Type': 'application/json'
    }
  }
};
const testTransformation = {
  name: 'Test Transformation',
  description: 'Test transformation for E2E tests',
  rules: [{
    source: 'data.id',
    target: 'id',
    transform: 'toString'
  }, {
    source: 'data.attributes.name',
    target: 'name',
    transform: 'uppercase'
  }]
};
const testWorkflow = {
  name: 'Test Workflow',
  description: 'Test workflow for E2E tests',
  steps: [{
    type: 'connector',
    connectorId: null,
    // Will be set during test
    operation: 'GET',
    path: '/users'
  }, {
    type: 'transformation',
    transformationId: null // Will be set during test
  }]
};

// Setup and teardown
beforeAll(async () => {
  // Start MongoDB memory server
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();

  // Connect to in-memory database
  await mongoose.connect(mongoUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true
  });

  // Initialize services
  connectorRegistry = new ConnectorRegistry();
  featureFlagService = new FeatureFlagService();
  encryptionService = new EncryptionService();

  // Start server
  server = app.listen(0);
});
afterAll(async () => {
  // Stop server and close database connection
  server.close();
  await mongoose.disconnect();
  await mongoServer.stop();
});

// Helper function to authenticate
async function authenticate() {
  // Register user
  await request(app).post('/api/auth/register').send(testUser);

  // Login
  const response = await request(app).post('/api/auth/login').send({
    email: testUser.email,
    password: testUser.password
  });
  return response.body.token;
}
describe('NovaConnect UAC End-to-End Tests', () => {
  beforeAll(async () => {
    // Authenticate and get token
    authToken = await authenticate();
  });
  describe('Connector Management', () => {
    let connectorId;
    test('Should create a new connector', async () => {
      const response = await request(app).post('/api/connectors').set('Authorization', `Bearer ${authToken}`).send(testConnector);
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(testConnector.name);
      connectorId = response.body.id;
      testWorkflow.steps[0].connectorId = connectorId;
    });
    test('Should get connector by ID', async () => {
      const response = await request(app).get(`/api/connectors/${connectorId}`).set('Authorization', `Bearer ${authToken}`);
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(connectorId);
      expect(response.body.name).toBe(testConnector.name);
    });
    test('Should update connector', async () => {
      const updatedConnector = {
        ...testConnector,
        name: 'Updated Connector'
      };
      const response = await request(app).put(`/api/connectors/${connectorId}`).set('Authorization', `Bearer ${authToken}`).send(updatedConnector);
      expect(response.status).toBe(200);
      expect(response.body.name).toBe(updatedConnector.name);
    });
    test('Should list all connectors', async () => {
      const response = await request(app).get('/api/connectors').set('Authorization', `Bearer ${authToken}`);
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });
  });
  describe('Transformation Management', () => {
    let transformationId;
    test('Should create a new transformation', async () => {
      const response = await request(app).post('/api/transformations').set('Authorization', `Bearer ${authToken}`).send(testTransformation);
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(testTransformation.name);
      transformationId = response.body.id;
      testWorkflow.steps[1].transformationId = transformationId;
    });
    test('Should get transformation by ID', async () => {
      const response = await request(app).get(`/api/transformations/${transformationId}`).set('Authorization', `Bearer ${authToken}`);
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(transformationId);
      expect(response.body.name).toBe(testTransformation.name);
    });
    test('Should update transformation', async () => {
      const updatedTransformation = {
        ...testTransformation,
        name: 'Updated Transformation'
      };
      const response = await request(app).put(`/api/transformations/${transformationId}`).set('Authorization', `Bearer ${authToken}`).send(updatedTransformation);
      expect(response.status).toBe(200);
      expect(response.body.name).toBe(updatedTransformation.name);
    });
    test('Should list all transformations', async () => {
      const response = await request(app).get('/api/transformations').set('Authorization', `Bearer ${authToken}`);
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });
  });
  describe('Workflow Management', () => {
    let workflowId;
    test('Should create a new workflow', async () => {
      const response = await request(app).post('/api/workflows').set('Authorization', `Bearer ${authToken}`).send(testWorkflow);
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(testWorkflow.name);
      workflowId = response.body.id;
    });
    test('Should get workflow by ID', async () => {
      const response = await request(app).get(`/api/workflows/${workflowId}`).set('Authorization', `Bearer ${authToken}`);
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(workflowId);
      expect(response.body.name).toBe(testWorkflow.name);
    });
    test('Should update workflow', async () => {
      const updatedWorkflow = {
        ...testWorkflow,
        name: 'Updated Workflow'
      };
      const response = await request(app).put(`/api/workflows/${workflowId}`).set('Authorization', `Bearer ${authToken}`).send(updatedWorkflow);
      expect(response.status).toBe(200);
      expect(response.body.name).toBe(updatedWorkflow.name);
    });
    test('Should list all workflows', async () => {
      const response = await request(app).get('/api/workflows').set('Authorization', `Bearer ${authToken}`);
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });
    test('Should execute workflow', async () => {
      // Mock the connector execution
      jest.spyOn(connectorRegistry, 'executeConnector').mockResolvedValue({
        data: {
          id: 123,
          attributes: {
            name: 'test user'
          }
        }
      });
      const response = await request(app).post(`/api/workflows/${workflowId}/execute`).set('Authorization', `Bearer ${authToken}`).send({});
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('result');
      expect(response.body.result).toHaveProperty('id');
      expect(response.body.result).toHaveProperty('name');
      expect(response.body.result.name).toBe('TEST USER'); // Uppercase transformation
    });
  });
  describe('Feature Flag Management', () => {
    test('Should get feature flags', async () => {
      const response = await request(app).get('/api/feature-flags').set('Authorization', `Bearer ${authToken}`);
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('flags');
      expect(typeof response.body.flags).toBe('object');
    });
    test('Should update feature flags', async () => {
      const flags = {
        'premium.transformations': true,
        'premium.workflows': true
      };
      const response = await request(app).put('/api/feature-flags').set('Authorization', `Bearer ${authToken}`).send({
        flags
      });
      expect(response.status).toBe(200);
      expect(response.body.flags['premium.transformations']).toBe(true);
      expect(response.body.flags['premium.workflows']).toBe(true);
    });
  });
  describe('Security', () => {
    test('Should encrypt and decrypt data', async () => {
      const testData = {
        secret: 'test-secret'
      };

      // Encrypt
      const encryptResponse = await request(app).post('/api/security/encrypt').set('Authorization', `Bearer ${authToken}`).send({
        data: testData
      });
      expect(encryptResponse.status).toBe(200);
      expect(encryptResponse.body).toHaveProperty('encryptedData');

      // Decrypt
      const decryptResponse = await request(app).post('/api/security/decrypt').set('Authorization', `Bearer ${authToken}`).send({
        encryptedData: encryptResponse.body.encryptedData
      });
      expect(decryptResponse.status).toBe(200);
      expect(decryptResponse.body).toHaveProperty('data');
      expect(decryptResponse.body.data).toEqual(testData);
    });
  });
  describe('Audit Logging', () => {
    test('Should retrieve audit logs', async () => {
      const response = await request(app).get('/api/audit-logs').set('Authorization', `Bearer ${authToken}`);
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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