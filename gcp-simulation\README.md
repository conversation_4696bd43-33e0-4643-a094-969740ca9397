# NovaFuse GCP Simulation Environment

This directory contains a simulation environment for testing NovaFuse integration with Google Cloud Platform services.

## Overview

The GCP Simulation Environment provides a local development and testing environment that simulates key Google Cloud Platform services:

- **Security Command Center**: For security findings and compliance
- **Cloud IAM**: For identity and access management
- **BigQuery**: For data analytics and reporting
- **Cloud Storage**: For evidence management and data storage
- **Cloud Functions**: For automated compliance enforcement
- **Cloud Monitoring**: For compliance monitoring and alerting

It also includes the core NovaFuse components:

- **NovaFuse API**: GRC API services
- **NovaConnect (UAC)**: Universal API Connector
- **NovaFuse UI**: User interface for testing and demonstration

## Architecture

The simulation environment uses Docker Compose to create a network of containers that simulate the GCP environment:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   NovaFuse UI   │     │  NovaFuse API   │     │  NovaConnect    │
│   (Port 3003)   │     │   (Port 3001)   │     │   (Port 3002)   │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         └───────────────┬───────┴───────────────┬───────┘
                         │                       │
                ┌────────┴────────┐     ┌────────┴────────┐
                │   API Gateway   │     │    MongoDB      │
                │   (Port 3000)   │     │   (Port 27017)  │
                └────────┬────────┘     └─────────────────┘
                         │
         ┌───────────────┼───────────────┬───────────────┐
         │               │               │               │
┌────────┴────────┐┌────────┴────────┐┌────────┴────────┐┌─────────────────┐
│ Security Command││    Cloud IAM    ││    BigQuery     ││      Redis      │
│  (Port 8081)    ││   (Port 8082)   ││   (Port 8083)   ││   (Port 6379)   │
└─────────────────┘└─────────────────┘└─────────────────┘└─────────────────┘
```

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Bash shell (for running the scripts)

### Running the Simulation Environment

#### On Linux/macOS:

1. Start the simulation environment:

```bash
./run-simulation.sh
```

2. Test the simulation environment:

```bash
./test-simulation.sh
```

#### On Windows:

1. Start the simulation environment:

```powershell
.\Run-Simulation.ps1
```

2. Test the simulation environment:

```powershell
.\Test-Simulation.ps1
```

3. Access the NovaFuse UI:

```
http://localhost:3003
```

### Available Services

| Service | URL | Description |
|---------|-----|-------------|
| NovaFuse UI | http://localhost:3003 | User interface for testing and demonstration |
| API Gateway | http://localhost:3000 | Central entry point for all APIs |
| NovaFuse API | http://localhost:3001 | GRC API services |
| NovaConnect (UAC) | http://localhost:3002 | Universal API Connector |
| Security Command Center | http://localhost:8081 | Simulated GCP Security Command Center |
| Cloud IAM | http://localhost:8082 | Simulated GCP Cloud IAM |
| BigQuery | http://localhost:8083 | Simulated GCP BigQuery |
| Cloud Storage | http://localhost:8084 | Simulated GCP Cloud Storage |
| Cloud Functions | http://localhost:8085 | Simulated GCP Cloud Functions |
| Cloud Monitoring | http://localhost:8086 | Simulated GCP Cloud Monitoring |

## Testing the Feature Flag System

The simulation environment includes a feature flag system that controls access to features based on product tier. You can test this by setting the `X-Product-Tier` header in your API requests:

```bash
# Test with NovaPrime tier (all features)
curl -H "X-Product-Tier: novaPrime" http://localhost:3001/privacy/management/processing-activities

# Test with NovaCore tier (limited features)
curl -H "X-Product-Tier: novaCore" http://localhost:3001/privacy/management/processing-activities

# Test with NovaShield tier (security features)
curl -H "X-Product-Tier: novaShield" http://localhost:3001/security/assessment/assessments

# Test with NovaLearn tier (compliance features)
curl -H "X-Product-Tier: novaLearn" http://localhost:3001/compliance/regulatory/frameworks
```

## Testing Google Cloud Integration

The simulation environment includes integration with simulated Google Cloud services:

```bash
# Test Security Command Center integration
curl http://localhost:3001/integrations/gcp/scc/findings

# Test NovaConnect UAC with Google Cloud connectors
curl http://localhost:3002/integrations/gcp/connectors

# Test Cloud Storage Evidence Binder
curl http://localhost:8084/storage/evidence-binder

# Test Cloud Functions for compliance enforcement
curl http://localhost:8085/functions

# Test Cloud Monitoring for compliance metrics
curl http://localhost:8086/monitoring/metrics
```

## Enhanced Data Models

The simulation environment includes realistic data models for:

- **Compliance Frameworks**: GDPR, HIPAA, PCI-DSS, SOC2
- **Risk Scenarios**: Detailed risk scenarios with compliance impact
- **User Personas**: Role-specific dashboards and views
- **Evidence Records**: Comprehensive evidence management

You can access these data models through the API:

```bash
# Get compliance frameworks
curl http://localhost:3001/api/compliance/frameworks

# Get risk scenarios
curl http://localhost:3001/api/risk/scenarios

# Get user personas
curl http://localhost:3001/api/personas

# Get evidence records
curl http://localhost:3001/api/evidence/records
```

## Demo Scripts

The `demo-scripts` directory contains scripts for demonstrating NovaFuse's capabilities:

- `enhanced-gcp-demo.md`: Demonstrates the enhanced GCP simulation environment with realistic data, complex integration patterns, and additional Google Cloud services.

## Stopping the Simulation Environment

To stop the simulation environment:

```bash
docker-compose down
```

## Troubleshooting

If you encounter issues with the simulation environment:

1. Check the container logs:

```bash
docker-compose logs
```

2. Restart the simulation environment:

```bash
docker-compose down
./run-simulation.sh
```

3. Check if all containers are running:

```bash
docker-compose ps
```
