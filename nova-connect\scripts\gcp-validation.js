/**
 * NovaConnect UAC GCP Validation Script
 * 
 * This script validates the NovaConnect UAC deployment on Google Cloud Platform
 * for Google Cloud Marketplace requirements.
 */

const axios = require('axios');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Set default values
const NAMESPACE = process.env.NAMESPACE || 'novafuse';
const SERVICE_NAME = process.env.SERVICE_NAME || 'novafuse-uac';
const VERSION = require('../package.json').version;

/**
 * Validate the NovaConnect UAC deployment
 */
async function validateDeployment() {
  console.log(`Validating NovaConnect UAC deployment version ${VERSION}...`);

  try {
    // Check if the deployment exists
    console.log('Checking if deployment exists...');
    const deploymentOutput = execSync(`kubectl get deployment ${SERVICE_NAME} -n ${NAMESPACE} -o json`);
    const deployment = JSON.parse(deploymentOutput);

    if (!deployment) {
      console.error('Deployment not found');
      process.exit(1);
    }

    console.log(`Deployment ${SERVICE_NAME} found`);

    // Check if the deployment is ready
    console.log('Checking if deployment is ready...');
    const readyReplicas = deployment.status.readyReplicas || 0;
    const replicas = deployment.status.replicas || 0;

    if (readyReplicas < replicas) {
      console.error(`Deployment not ready: ${readyReplicas}/${replicas} replicas ready`);
      process.exit(1);
    }

    console.log(`Deployment is ready: ${readyReplicas}/${replicas} replicas ready`);

    // Get the service
    console.log('Getting service...');
    const serviceOutput = execSync(`kubectl get service ${SERVICE_NAME} -n ${NAMESPACE} -o json`);
    const service = JSON.parse(serviceOutput);

    if (!service) {
      console.error('Service not found');
      process.exit(1);
    }

    console.log(`Service ${SERVICE_NAME} found`);

    // Get the external IP
    console.log('Getting external IP...');
    const externalIP = service.status.loadBalancer.ingress[0].ip;

    if (!externalIP) {
      console.error('External IP not found');
      process.exit(1);
    }

    console.log(`External IP: ${externalIP}`);

    // Check if the service is accessible
    console.log('Checking if service is accessible...');
    const response = await axios.get(`http://${externalIP}/health`);

    if (response.status !== 200) {
      console.error(`Service not accessible: ${response.status}`);
      process.exit(1);
    }

    console.log('Service is accessible');

    // Check if the service is reporting metrics
    console.log('Checking if service is reporting metrics...');
    const metricsResponse = await axios.get(`http://${externalIP}/metrics`);

    if (metricsResponse.status !== 200) {
      console.error(`Metrics not accessible: ${metricsResponse.status}`);
      process.exit(1);
    }

    console.log('Metrics are accessible');

    // Check if the service is reporting logs
    console.log('Checking if service is reporting logs...');
    const logsOutput = execSync(`gcloud logging read "resource.type=k8s_container AND resource.labels.namespace_name=${NAMESPACE} AND resource.labels.container_name=${SERVICE_NAME}" --limit 10`);

    if (!logsOutput) {
      console.error('Logs not found');
      process.exit(1);
    }

    console.log('Logs are being reported');

    // Check if the service is reporting traces
    console.log('Checking if service is reporting traces...');
    const tracesOutput = execSync(`gcloud trace list --filter="labels.service=${SERVICE_NAME}" --limit 10`);

    if (!tracesOutput) {
      console.error('Traces not found');
      process.exit(1);
    }

    console.log('Traces are being reported');

    // Check if the service is reporting custom metrics
    console.log('Checking if service is reporting custom metrics...');
    const customMetricsOutput = execSync(`gcloud monitoring metrics list --filter="metric.type=custom.googleapis.com/novaconnect_"`);

    if (!customMetricsOutput) {
      console.error('Custom metrics not found');
      process.exit(1);
    }

    console.log('Custom metrics are being reported');

    // Validate marketplace requirements
    console.log('Validating marketplace requirements...');

    // Check if the deployment has resource limits
    console.log('Checking if deployment has resource limits...');
    const containers = deployment.spec.template.spec.containers;
    const mainContainer = containers.find(c => c.name === SERVICE_NAME);

    if (!mainContainer.resources.limits) {
      console.error('Resource limits not found');
      process.exit(1);
    }

    console.log('Resource limits found');

    // Check if the deployment has health checks
    console.log('Checking if deployment has health checks...');
    if (!mainContainer.livenessProbe || !mainContainer.readinessProbe) {
      console.error('Health checks not found');
      process.exit(1);
    }

    console.log('Health checks found');

    // Check if the deployment has a service account
    console.log('Checking if deployment has a service account...');
    if (!deployment.spec.template.spec.serviceAccountName) {
      console.error('Service account not found');
      process.exit(1);
    }

    console.log('Service account found');

    // All checks passed
    console.log('All validation checks passed!');
    console.log('NovaConnect UAC is ready for Google Cloud Marketplace');

  } catch (error) {
    console.error('Validation failed:', error.message);
    process.exit(1);
  }
}

// Run the validation
validateDeployment();
