/**
 * Testing Service
 * 
 * This service handles operations related to testing connectors.
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const ConnectorService = require('./ConnectorService');
const CredentialService = require('./CredentialService');
const { ValidationError } = require('../utils/errors');

class TestingService {
  constructor() {
    this.connectorService = new ConnectorService();
    this.credentialService = new CredentialService();
    this.requestHistory = new Map(); // In-memory storage for request history
  }

  /**
   * Execute a connector endpoint
   */
  async executeEndpoint(connectorId, endpointId, credentialId, parameters = {}, headers = {}) {
    try {
      // Get connector and endpoint
      const connector = await this.connectorService.getConnectorById(connectorId);
      const endpoint = connector.endpoints.find(e => e.id === endpointId);
      
      if (!endpoint) {
        throw new ValidationError(`Endpoint with ID ${endpointId} not found in connector ${connector.name}`);
      }
      
      // Get credentials
      const credentials = await this.credentialService.getCredentialById(credentialId);
      
      // Build request URL
      let url = this.buildUrl(connector.baseUrl, endpoint.path, parameters);
      
      // Build request headers
      const requestHeaders = this.buildHeaders(connector, endpoint, credentials, headers);
      
      // Build request body for non-GET requests
      let data = null;
      if (endpoint.method.toUpperCase() !== 'GET') {
        data = this.buildRequestBody(endpoint, parameters);
      }
      
      // Execute request
      const startTime = Date.now();
      const response = await axios({
        method: endpoint.method,
        url,
        headers: requestHeaders,
        data,
        timeout: connector.configuration?.timeout || 30000,
        validateStatus: () => true // Don't throw on error status codes
      });
      const endTime = Date.now();
      
      // Build response object
      const result = {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data,
        responseTime: endTime - startTime
      };
      
      // Save to history
      this.saveToHistory(connectorId, endpointId, {
        id: uuidv4(),
        timestamp: new Date(),
        endpoint: endpoint.name,
        parameters,
        headers,
        response: result
      });
      
      return result;
    } catch (error) {
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return {
          status: error.response.status,
          statusText: error.response.statusText,
          headers: error.response.headers,
          data: error.response.data,
          responseTime: 0
        };
      } else if (error.request) {
        // The request was made but no response was received
        return {
          status: 0,
          statusText: 'No Response',
          headers: {},
          data: {
            error: 'No response received from server',
            message: error.message
          },
          responseTime: 0
        };
      } else {
        // Something happened in setting up the request that triggered an Error
        return {
          status: 0,
          statusText: 'Request Error',
          headers: {},
          data: {
            error: 'Error setting up request',
            message: error.message
          },
          responseTime: 0
        };
      }
    }
  }

  /**
   * Build URL with path parameters
   */
  buildUrl(baseUrl, path, parameters) {
    let url = path;
    
    // Replace path parameters
    const pathParams = path.match(/\{([^}]+)\}/g) || [];
    pathParams.forEach(param => {
      const paramName = param.substring(1, param.length - 1);
      if (parameters[paramName]) {
        url = url.replace(param, encodeURIComponent(parameters[paramName]));
        delete parameters[paramName]; // Remove from parameters to avoid duplication in query string
      }
    });
    
    // Add query parameters for GET requests
    const queryParams = new URLSearchParams();
    Object.entries(parameters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value);
      }
    });
    
    const queryString = queryParams.toString();
    if (queryString) {
      url += `?${queryString}`;
    }
    
    // Combine with base URL
    return new URL(url, baseUrl).toString();
  }

  /**
   * Build request headers
   */
  buildHeaders(connector, endpoint, credentials, customHeaders) {
    const headers = {
      ...connector.configuration?.headers,
      ...endpoint.headers,
      ...customHeaders
    };
    
    // Add authentication headers
    if (connector.authentication) {
      switch (connector.authentication.type) {
        case 'API_KEY':
          headers[connector.authentication.fields.headerName || 'X-API-Key'] = credentials.apiKey;
          break;
        case 'BASIC':
          headers['Authorization'] = `Basic ${Buffer.from(`${credentials.username}:${credentials.password}`).toString('base64')}`;
          break;
        case 'BEARER':
        case 'JWT':
          headers['Authorization'] = `Bearer ${credentials.token}`;
          break;
        // Add other authentication types as needed
      }
    }
    
    return headers;
  }

  /**
   * Build request body
   */
  buildRequestBody(endpoint, parameters) {
    // For simplicity, just return parameters as the body
    // In a real implementation, this would be more sophisticated
    return parameters;
  }

  /**
   * Save request to history
   */
  saveToHistory(connectorId, endpointId, historyItem) {
    const key = `${connectorId}:${endpointId}`;
    if (!this.requestHistory.has(key)) {
      this.requestHistory.set(key, []);
    }
    
    const history = this.requestHistory.get(key);
    history.unshift(historyItem); // Add to beginning
    
    // Limit history size
    if (history.length > 50) {
      history.pop(); // Remove oldest
    }
  }

  /**
   * Get request history
   */
  getRequestHistory(connectorId, endpointId) {
    const key = `${connectorId}:${endpointId}`;
    return this.requestHistory.get(key) || [];
  }

  /**
   * Validate response against rules
   */
  validateResponse(response, rules) {
    if (!response || !rules || !Array.isArray(rules)) {
      throw new ValidationError('Invalid response or rules');
    }
    
    return rules.map(rule => {
      try {
        const result = this.evaluateRule(rule, response);
        return {
          ...rule,
          passed: result.passed,
          message: result.message
        };
      } catch (error) {
        return {
          ...rule,
          passed: false,
          message: `Error evaluating rule: ${error.message}`
        };
      }
    });
  }

  /**
   * Evaluate a single validation rule
   */
  evaluateRule(rule, response) {
    if (!rule.path || !rule.operator) {
      return { passed: false, message: 'Invalid rule: missing path or operator' };
    }
    
    try {
      // Get the value at the specified path
      const value = this.getValueAtPath(response.data, rule.path);
      
      // Validate based on operator
      switch (rule.operator) {
        case 'exists':
          return { 
            passed: value !== undefined, 
            message: value !== undefined 
              ? `Path ${rule.path} exists` 
              : `Path ${rule.path} does not exist` 
          };
          
        case 'not_exists':
          return { 
            passed: value === undefined, 
            message: value === undefined 
              ? `Path ${rule.path} does not exist` 
              : `Path ${rule.path} exists but should not` 
          };
          
        case 'equals':
          // Try to convert value to appropriate type
          let expectedValue = rule.value;
          if (!isNaN(Number(rule.value))) {
            expectedValue = Number(rule.value);
          } else if (rule.value === 'true') {
            expectedValue = true;
          } else if (rule.value === 'false') {
            expectedValue = false;
          }
          
          return { 
            passed: value === expectedValue, 
            message: value === expectedValue 
              ? `Value at ${rule.path} equals ${rule.value}` 
              : `Value at ${rule.path} is ${value}, expected ${rule.value}` 
          };
          
        case 'not_equals':
          // Try to convert value to appropriate type
          let notExpectedValue = rule.value;
          if (!isNaN(Number(rule.value))) {
            notExpectedValue = Number(rule.value);
          } else if (rule.value === 'true') {
            notExpectedValue = true;
          } else if (rule.value === 'false') {
            notExpectedValue = false;
          }
          
          return { 
            passed: value !== notExpectedValue, 
            message: value !== notExpectedValue 
              ? `Value at ${rule.path} does not equal ${rule.value}` 
              : `Value at ${rule.path} equals ${rule.value} but should not` 
          };
          
        case 'contains':
          if (typeof value === 'string') {
            return { 
              passed: value.includes(rule.value), 
              message: value.includes(rule.value) 
                ? `Value at ${rule.path} contains ${rule.value}` 
                : `Value at ${rule.path} does not contain ${rule.value}` 
            };
          } else if (Array.isArray(value)) {
            return { 
              passed: value.includes(rule.value), 
              message: value.includes(rule.value) 
                ? `Array at ${rule.path} contains ${rule.value}` 
                : `Array at ${rule.path} does not contain ${rule.value}` 
            };
          }
          return { 
            passed: false, 
            message: `Cannot check contains on value of type ${typeof value}` 
          };
          
        case 'not_contains':
          if (typeof value === 'string') {
            return { 
              passed: !value.includes(rule.value), 
              message: !value.includes(rule.value) 
                ? `Value at ${rule.path} does not contain ${rule.value}` 
                : `Value at ${rule.path} contains ${rule.value} but should not` 
            };
          } else if (Array.isArray(value)) {
            return { 
              passed: !value.includes(rule.value), 
              message: !value.includes(rule.value) 
                ? `Array at ${rule.path} does not contain ${rule.value}` 
                : `Array at ${rule.path} contains ${rule.value} but should not` 
            };
          }
          return { 
            passed: false, 
            message: `Cannot check not_contains on value of type ${typeof value}` 
          };
          
        case 'greater_than':
          const greaterThanValue = Number(rule.value);
          if (isNaN(greaterThanValue)) {
            return { 
              passed: false, 
              message: `Cannot compare with non-numeric value ${rule.value}` 
            };
          }
          return { 
            passed: value > greaterThanValue, 
            message: value > greaterThanValue 
              ? `Value ${value} at ${rule.path} is greater than ${greaterThanValue}` 
              : `Value ${value} at ${rule.path} is not greater than ${greaterThanValue}` 
          };
          
        case 'less_than':
          const lessThanValue = Number(rule.value);
          if (isNaN(lessThanValue)) {
            return { 
              passed: false, 
              message: `Cannot compare with non-numeric value ${rule.value}` 
            };
          }
          return { 
            passed: value < lessThanValue, 
            message: value < lessThanValue 
              ? `Value ${value} at ${rule.path} is less than ${lessThanValue}` 
              : `Value ${value} at ${rule.path} is not less than ${lessThanValue}` 
          };
          
        case 'matches':
          try {
            const regex = new RegExp(rule.value);
            return { 
              passed: regex.test(String(value)), 
              message: regex.test(String(value)) 
                ? `Value at ${rule.path} matches pattern ${rule.value}` 
                : `Value at ${rule.path} does not match pattern ${rule.value}` 
            };
          } catch (error) {
            return { 
              passed: false, 
              message: `Invalid regex pattern: ${error.message}` 
            };
          }
          
        default:
          return { 
            passed: false, 
            message: `Unknown operator: ${rule.operator}` 
          };
      }
    } catch (error) {
      return { 
        passed: false, 
        message: `Error validating rule: ${error.message}` 
      };
    }
  }

  /**
   * Get value at a specific path in an object
   */
  getValueAtPath(obj, path) {
    if (!path) return obj;
    
    const parts = path.split('.');
    let value = obj;
    
    for (const part of parts) {
      if (part === '') continue;
      
      if (part.includes('[') && part.includes(']')) {
        // Handle array access
        const arrayName = part.substring(0, part.indexOf('['));
        const index = parseInt(part.substring(part.indexOf('[') + 1, part.indexOf(']')));
        
        if (!value[arrayName]) {
          return undefined;
        }
        
        value = value[arrayName][index];
      } else {
        // Handle object property access
        if (value[part] === undefined) {
          return undefined;
        }
        
        value = value[part];
      }
    }
    
    return value;
  }

  /**
   * Simulate error scenarios
   */
  async simulateErrorScenario(type, options = {}) {
    switch (type) {
      case 'network':
        throw new Error('Network connection refused');
        
      case 'auth':
        return {
          status: 401,
          statusText: 'Unauthorized',
          headers: {
            'content-type': 'application/json'
          },
          data: {
            error: 'invalid_credentials',
            error_description: 'The credentials provided are invalid or expired'
          },
          responseTime: 200
        };
        
      case 'timeout':
        await new Promise(resolve => setTimeout(resolve, 3000));
        throw new Error('Request timed out after 3000ms');
        
      case 'rate_limit':
        return {
          status: 429,
          statusText: 'Too Many Requests',
          headers: {
            'content-type': 'application/json',
            'x-rate-limit-limit': '100',
            'x-rate-limit-remaining': '0',
            'x-rate-limit-reset': Math.floor(Date.now() / 1000) + 60
          },
          data: {
            error: 'rate_limit_exceeded',
            error_description: 'You have exceeded the rate limit of 100 requests per minute',
            retry_after: 60
          },
          responseTime: 150
        };
        
      case 'server':
        return {
          status: 500,
          statusText: 'Internal Server Error',
          headers: {
            'content-type': 'application/json'
          },
          data: {
            error: 'internal_server_error',
            error_description: 'An unexpected error occurred on the server',
            request_id: `req_${Math.random().toString(36).substring(2, 15)}`
          },
          responseTime: 300
        };
        
      case 'custom':
        if (options.customDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, Math.min(options.customDelay, 10000)));
        }
        
        return {
          status: options.customStatus || 500,
          statusText: this.getStatusText(options.customStatus || 500),
          headers: {
            'content-type': 'application/json'
          },
          data: {
            error: 'custom_error',
            error_description: options.customMessage || 'Custom error message',
            timestamp: new Date().toISOString()
          },
          responseTime: options.customDelay || 0
        };
        
      default:
        throw new Error('Unknown error scenario type');
    }
  }

  /**
   * Get HTTP status text
   */
  getStatusText(status) {
    const statusTexts = {
      400: 'Bad Request',
      401: 'Unauthorized',
      403: 'Forbidden',
      404: 'Not Found',
      405: 'Method Not Allowed',
      408: 'Request Timeout',
      409: 'Conflict',
      410: 'Gone',
      413: 'Payload Too Large',
      422: 'Unprocessable Entity',
      429: 'Too Many Requests',
      500: 'Internal Server Error',
      501: 'Not Implemented',
      502: 'Bad Gateway',
      503: 'Service Unavailable',
      504: 'Gateway Timeout'
    };
    
    return statusTexts[status] || 'Unknown Status';
  }
}

module.exports = TestingService;
