<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FIG. 7: NovaConnect NIST Crosswalk</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 950px;
            height: 650px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
        }
        .table-container {
            width: 900px;
            margin: 0 auto;
            border-collapse: collapse;
        }
        .table-header {
            background-color: #333;
            color: white;
            text-align: left;
            padding: 10px;
            font-weight: bold;
        }
        .table-row {
            border-bottom: 1px solid #ddd;
        }
        .table-row:nth-child(even) {
            background-color: #f9f9f9;
        }
        .table-cell {
            padding: 10px;
            vertical-align: top;
        }
        .framework-cell {
            width: 100px;
            font-weight: bold;
        }
        .control-cell {
            width: 100px;
        }
        .description-cell {
            width: 200px;
        }
        .implementation-cell {
            width: 250px;
        }
        .status-cell {
            width: 100px;
            font-weight: bold;
        }
        .exceeds {
            color: #28a745;
        }
        .meets {
            color: #007bff;
        }
        .component-number {
            position: absolute;
            width: 20px;
            height: 20px;
            background-color: #333;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
        }
        .legend {
            position: absolute;
            right: 20px;
            bottom: 20px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            width: 200px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border: 1px solid #333;
        }
        .inventor-label {
            position: absolute;
            left: 20px;
            bottom: 20px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
    </style>
</head>
<body>
    <h1 class="title">FIG. 7: NovaConnect NIST Crosswalk</h1>
    
    <div class="diagram-container">
        <table class="table-container">
            <tr>
                <th class="table-header framework-cell">Framework</th>
                <th class="table-header control-cell">Control ID</th>
                <th class="table-header description-cell">Description</th>
                <th class="table-header implementation-cell">NovaConnect Implementation</th>
                <th class="table-header status-cell">Status</th>
            </tr>
            <tr class="table-row">
                <td class="table-cell framework-cell">CSF 2.0</td>
                <td class="table-cell control-cell">ID.SC-4</td>
                <td class="table-cell description-cell">Suppliers and third-party partners are routinely assessed</td>
                <td class="table-cell implementation-cell">Real-time supplier validation (0.07ms) with ML-based risk scoring</td>
                <td class="table-cell status-cell exceeds">Exceeds</td>
            </tr>
            <tr class="table-row">
                <td class="table-cell framework-cell">SP 800-53</td>
                <td class="table-cell control-cell">AU-10</td>
                <td class="table-cell description-cell">Non-repudiation</td>
                <td class="table-cell implementation-cell">Quantum-sealed timestamps with blockchain attestation</td>
                <td class="table-cell status-cell exceeds">Exceeds</td>
            </tr>
            <tr class="table-row">
                <td class="table-cell framework-cell">SP 800-207</td>
                <td class="table-cell control-cell">ZTA-3</td>
                <td class="table-cell description-cell">Per-session trust evaluation</td>
                <td class="table-cell implementation-cell">Per-request zero-trust authentication with dynamic privilege calculation</td>
                <td class="table-cell status-cell exceeds">Exceeds</td>
            </tr>
            <tr class="table-row">
                <td class="table-cell framework-cell">SP 800-161</td>
                <td class="table-cell control-cell">SR-3</td>
                <td class="table-cell description-cell">Supply chain risk assessment</td>
                <td class="table-cell implementation-cell">Automated component analysis with 69K EPS processing</td>
                <td class="table-cell status-cell exceeds">Exceeds</td>
            </tr>
            <tr class="table-row">
                <td class="table-cell framework-cell">CSF 2.0</td>
                <td class="table-cell control-cell">DE.CM-1</td>
                <td class="table-cell description-cell">The network is monitored to detect potential cybersecurity events</td>
                <td class="table-cell implementation-cell">Continuous monitoring with sub-millisecond data normalization (0.07ms)</td>
                <td class="table-cell status-cell exceeds">Exceeds</td>
            </tr>
            <tr class="table-row">
                <td class="table-cell framework-cell">SP 800-53</td>
                <td class="table-cell control-cell">SI-4</td>
                <td class="table-cell description-cell">System monitoring</td>
                <td class="table-cell implementation-cell">Real-time anomaly detection with 69,000 events/sec processing capacity</td>
                <td class="table-cell status-cell exceeds">Exceeds</td>
            </tr>
            <tr class="table-row">
                <td class="table-cell framework-cell">SP 800-171</td>
                <td class="table-cell control-cell">3.13.1</td>
                <td class="table-cell description-cell">Monitor, control, and protect communications</td>
                <td class="table-cell implementation-cell">Zero-trust bidirectional control with protocol-agnostic connectivity</td>
                <td class="table-cell status-cell exceeds">Exceeds</td>
            </tr>
            <tr class="table-row">
                <td class="table-cell framework-cell">CSF 2.0</td>
                <td class="table-cell control-cell">RS.RP-1</td>
                <td class="table-cell description-cell">Response plan is executed during or after an incident</td>
                <td class="table-cell implementation-cell">Automated response orchestration with 2-second remediation time</td>
                <td class="table-cell status-cell exceeds">Exceeds</td>
            </tr>
        </table>
        
        <div class="legend">
            <div class="legend-title" style="font-weight: bold; margin-bottom: 5px;">Status Legend:</div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #28a745;"></div>
                <div>Exceeds NIST Requirements</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #007bff;"></div>
                <div>Meets NIST Requirements</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ffc107;"></div>
                <div>Partially Meets Requirements</div>
            </div>
        </div>
        
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
        
        <div class="component-number" style="top: 10px; left: 10px;">701</div>
    </div>
</body>
</html>
