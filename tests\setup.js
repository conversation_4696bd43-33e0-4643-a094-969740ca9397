// Global test setup
const axios = require('axios');
const MockAdapter = require('axios-mock-adapter');

// Increase timeout for all tests
jest.setTimeout(30000);

// Global setup
beforeAll(async () => {
  console.log('Setting up global test environment...');
  
  // Create global mock for axios
  global.axiosMock = new MockAdapter(axios);
  
  // Set up any other global test requirements
  global.testMetrics = {
    startTime: Date.now(),
    testCounts: {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0
    },
    performance: {
      responseTimeTotal: 0,
      responseTimeCount: 0,
      memoryUsage: []
    }
  };
  
  // Record memory usage at start
  global.testMetrics.memoryUsage.push({
    time: 'start',
    usage: process.memoryUsage()
  });
  
  // Set up helper functions
  global.recordResponseTime = (duration) => {
    global.testMetrics.performance.responseTimeTotal += duration;
    global.testMetrics.performance.responseTimeCount++;
  };
  
  // Record memory usage every 30 seconds
  global.memoryInterval = setInterval(() => {
    global.testMetrics.memoryUsage.push({
      time: Date.now() - global.testMetrics.startTime,
      usage: process.memoryUsage()
    });
  }, 30000);
});

// Global teardown
afterAll(async () => {
  console.log('Tearing down global test environment...');
  
  // Clean up global mock
  if (global.axiosMock) {
    global.axiosMock.restore();
  }
  
  // Record final memory usage
  global.testMetrics.memoryUsage.push({
    time: 'end',
    usage: process.memoryUsage()
  });
  
  // Clear memory interval
  if (global.memoryInterval) {
    clearInterval(global.memoryInterval);
  }
  
  // Calculate average response time
  if (global.testMetrics.performance.responseTimeCount > 0) {
    global.testMetrics.performance.averageResponseTime = 
      global.testMetrics.performance.responseTimeTotal / global.testMetrics.performance.responseTimeCount;
  }
  
  // Log test metrics
  console.log('Test Metrics:', {
    duration: Date.now() - global.testMetrics.startTime,
    testCounts: global.testMetrics.testCounts,
    performance: {
      averageResponseTime: global.testMetrics.performance.averageResponseTime,
      memoryUsage: {
        start: global.testMetrics.memoryUsage[0],
        end: global.testMetrics.memoryUsage[global.testMetrics.memoryUsage.length - 1]
      }
    }
  });
});
