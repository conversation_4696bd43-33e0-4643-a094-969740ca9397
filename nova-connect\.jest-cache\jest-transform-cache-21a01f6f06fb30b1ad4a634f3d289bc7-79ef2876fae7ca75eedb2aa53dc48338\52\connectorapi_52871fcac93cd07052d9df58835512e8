047824365a6fb7504950e94d4080ab97
/**
 * NovaFuse Universal API Connector API
 *
 * This module provides a REST API for managing and executing connectors.
 */

const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const http = require('http');
const connectorRegistry = require('../registry/connector-registry');
const authenticationManager = require('../auth/authentication-manager');
const connectorExecutor = require('../executor/connector-executor');
const createGraphQLServer = require('./graphql/server');

// Import routes
const rbacRoutes = require('./routes/rbacRoutes');
const billingRoutes = require('./routes/billingRoutes');
const marketplaceRoutes = require('./routes/marketplaceRoutes');
class ConnectorApi {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || 3010;

    // Configure middleware
    this.app.use(cors());
    this.app.use(bodyParser.json());

    // Register routes
    this.registerRoutes();
  }

  /**
   * Initialize the API
   */
  async initialize() {
    try {
      // Initialize dependencies
      await connectorRegistry.initialize();
      await authenticationManager.initialize();
      await connectorExecutor.initialize();

      // Initialize services
      const MarketplaceService = require('./services/MarketplaceService');
      const marketplaceService = new MarketplaceService();
      await marketplaceService.initialize();
      const BillingService = require('./services/BillingService');
      const billingService = new BillingService();
      await billingService.initialize();

      // Create HTTP server
      const httpServer = http.createServer(this.app);

      // Create GraphQL server
      const {
        server: graphqlServer
      } = await createGraphQLServer(this.app, {
        httpServer
      });
      this.graphqlServer = graphqlServer;

      // Start the server
      this.server = httpServer.listen(this.port, () => {
        console.log(`Connector API listening on port ${this.port}`);
        console.log(`GraphQL endpoint: http://localhost:${this.port}${graphqlServer.graphqlPath}`);
        console.log(`GraphQL subscriptions: ws://localhost:${this.port}${graphqlServer.graphqlPath}`);
      });
      return true;
    } catch (error) {
      console.error('Failed to initialize Connector API:', error);
      throw error;
    }
  }

  /**
   * Register API routes
   */
  registerRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.status(200).json({
        status: 'ok'
      });
    });

    // Connector routes
    this.app.get('/connectors', this.getConnectors.bind(this));
    this.app.get('/connectors/:id', this.getConnector.bind(this));
    this.app.post('/connectors', this.registerConnector.bind(this));
    this.app.put('/connectors/:id', this.updateConnector.bind(this));
    this.app.delete('/connectors/:id', this.deleteConnector.bind(this));

    // Credential routes
    this.app.post('/credentials', this.storeCredentials.bind(this));
    this.app.delete('/credentials/:id', this.deleteCredentials.bind(this));
    this.app.post('/credentials/:id/test', this.testConnection.bind(this));

    // Execution routes
    this.app.post('/execute/:connectorId/:endpointId', this.executeEndpoint.bind(this));

    // RBAC routes
    this.app.use('/rbac', rbacRoutes);

    // Billing routes
    this.app.use('/billing', billingRoutes);

    // Marketplace routes
    this.app.use('/marketplace', marketplaceRoutes);
  }

  /**
   * Get all connectors
   */
  getConnectors(req, res) {
    try {
      const {
        category,
        query
      } = req.query;
      let connectors;
      if (category) {
        connectors = connectorRegistry.getConnectorsByCategory(category);
      } else if (query) {
        connectors = connectorRegistry.searchConnectors(query);
      } else {
        connectors = connectorRegistry.getAllConnectors();
      }

      // Remove sensitive information
      const sanitizedConnectors = connectors.map(connector => ({
        ...connector,
        authentication: {
          ...connector.authentication,
          fields: Object.entries(connector.authentication.fields).reduce((acc, [key, field]) => {
            acc[key] = {
              ...field,
              default: field.sensitive ? undefined : field.default
            };
            return acc;
          }, {})
        }
      }));
      res.status(200).json(sanitizedConnectors);
    } catch (error) {
      console.error('Error getting connectors:', error);
      res.status(500).json({
        error: error.message
      });
    }
  }

  /**
   * Get a connector by ID
   */
  getConnector(req, res) {
    try {
      const {
        id
      } = req.params;
      const connector = connectorRegistry.getConnector(id);
      if (!connector) {
        return res.status(404).json({
          error: `Connector '${id}' not found`
        });
      }

      // Remove sensitive information
      const sanitizedConnector = {
        ...connector,
        authentication: {
          ...connector.authentication,
          fields: Object.entries(connector.authentication.fields).reduce((acc, [key, field]) => {
            acc[key] = {
              ...field,
              default: field.sensitive ? undefined : field.default
            };
            return acc;
          }, {})
        }
      };
      res.status(200).json(sanitizedConnector);
    } catch (error) {
      console.error('Error getting connector:', error);
      res.status(500).json({
        error: error.message
      });
    }
  }

  /**
   * Register a new connector
   */
  async registerConnector(req, res) {
    try {
      const connector = req.body;
      if (!connector) {
        return res.status(400).json({
          error: 'Connector template is required'
        });
      }
      await connectorRegistry.registerConnector(connector);
      res.status(201).json({
        message: 'Connector registered successfully'
      });
    } catch (error) {
      console.error('Error registering connector:', error);
      res.status(500).json({
        error: error.message
      });
    }
  }

  /**
   * Update an existing connector
   */
  async updateConnector(req, res) {
    try {
      const {
        id
      } = req.params;
      const connector = req.body;
      if (!connector) {
        return res.status(400).json({
          error: 'Connector template is required'
        });
      }
      await connectorRegistry.updateConnector(id, connector);
      res.status(200).json({
        message: 'Connector updated successfully'
      });
    } catch (error) {
      console.error('Error updating connector:', error);
      res.status(500).json({
        error: error.message
      });
    }
  }

  /**
   * Delete a connector
   */
  async deleteConnector(req, res) {
    try {
      const {
        id
      } = req.params;
      await connectorRegistry.deleteConnector(id);
      res.status(200).json({
        message: 'Connector deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting connector:', error);
      res.status(500).json({
        error: error.message
      });
    }
  }

  /**
   * Store credentials for a connector
   */
  storeCredentials(req, res) {
    try {
      const {
        connectorId,
        credentials
      } = req.body;
      if (!connectorId || !credentials) {
        return res.status(400).json({
          error: 'Connector ID and credentials are required'
        });
      }
      const credentialId = authenticationManager.storeCredentials(connectorId, credentials);
      res.status(201).json({
        credentialId
      });
    } catch (error) {
      console.error('Error storing credentials:', error);
      res.status(500).json({
        error: error.message
      });
    }
  }

  /**
   * Delete credentials
   */
  deleteCredentials(req, res) {
    try {
      const {
        id
      } = req.params;
      const success = authenticationManager.deleteCredentials(id);
      if (!success) {
        return res.status(404).json({
          error: `Credentials '${id}' not found`
        });
      }
      res.status(200).json({
        message: 'Credentials deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting credentials:', error);
      res.status(500).json({
        error: error.message
      });
    }
  }

  /**
   * Test a connection using stored credentials
   */
  async testConnection(req, res) {
    try {
      const {
        id
      } = req.params;
      const {
        connectorId
      } = req.body;
      if (!connectorId) {
        return res.status(400).json({
          error: 'Connector ID is required'
        });
      }
      const connector = connectorRegistry.getConnector(connectorId);
      if (!connector) {
        return res.status(404).json({
          error: `Connector '${connectorId}' not found`
        });
      }
      const credentials = authenticationManager.getCredentials(id);
      if (!credentials) {
        return res.status(404).json({
          error: `Credentials '${id}' not found`
        });
      }
      const result = await authenticationManager.testConnection(connector, credentials);
      res.status(200).json(result);
    } catch (error) {
      console.error('Error testing connection:', error);
      res.status(500).json({
        error: error.message
      });
    }
  }

  /**
   * Execute an endpoint
   */
  async executeEndpoint(req, res) {
    try {
      const {
        connectorId,
        endpointId
      } = req.params;
      const {
        credentialId,
        parameters
      } = req.body;
      if (!credentialId) {
        return res.status(400).json({
          error: 'Credential ID is required'
        });
      }
      const result = await connectorExecutor.executeEndpoint(connectorId, endpointId, credentialId, parameters);
      res.status(200).json(result);
    } catch (error) {
      console.error('Error executing endpoint:', error);
      res.status(500).json({
        error: error.message
      });
    }
  }
}

// Create and export a singleton instance
const connectorApi = new ConnectorApi();
module.exports = connectorApi;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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