/**
 * Authentication API Routes
 * 
 * This module provides API routes for authentication and authorization.
 */

const express = require('express');
const router = express.Router();
const { validateServiceAuth, validateUserAuth } = require('../middleware/validation');

/**
 * @route   POST /api/auth/service
 * @desc    Authenticate as a service
 * @access  Public
 */
router.post('/service', validateServiceAuth, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { apiKey, apiSecret } = req.body;
    const context = req.body.context || {};
    
    // Authenticate service
    const authResult = novaDNA.emergencyAuthenticator.authenticateService(apiKey, apiSecret, context);
    
    if (!authResult.authenticated) {
      return res.status(401).json({
        status: 'error',
        error: authResult.error
      });
    }
    
    res.json({
      status: 'success',
      data: {
        token: authResult.token,
        service: {
          serviceId: authResult.service.serviceId,
          name: authResult.service.name,
          type: authResult.service.type
        },
        expiresIn: authResult.token.expiresIn
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/auth/user
 * @desc    Authenticate as a user
 * @access  Public
 */
router.post('/user', validateUserAuth, async (req, res, next) => {
  try {
    // In a real implementation, this would authenticate the user
    // For now, we'll simulate user authentication
    
    const { email, password } = req.body;
    
    // Simulate user lookup
    if (email !== '<EMAIL>' || password !== 'password') {
      return res.status(401).json({
        status: 'error',
        error: 'Invalid credentials'
      });
    }
    
    // Simulate token generation
    const token = {
      value: 'user-token-' + Date.now(),
      expiresIn: 3600 // 1 hour
    };
    
    res.json({
      status: 'success',
      data: {
        token: token.value,
        user: {
          id: 'user-123',
          name: 'John Doe',
          email: '<EMAIL>'
        },
        expiresIn: token.expiresIn
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/auth/verify-token
 * @desc    Verify an authentication token
 * @access  Public
 */
router.post('/verify-token', async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { tokenId, tokenValue, tokenType } = req.body;
    
    if (!tokenId || !tokenValue || !tokenType) {
      return res.status(400).json({
        status: 'error',
        error: 'Token ID, value, and type are required'
      });
    }
    
    let verificationResult;
    
    if (tokenType === 'SERVICE') {
      // Verify service token
      verificationResult = novaDNA.emergencyAuthenticator.verifyToken(tokenId, tokenValue);
    } else if (tokenType === 'USER') {
      // In a real implementation, this would verify the user token
      // For now, we'll simulate user token verification
      verificationResult = {
        valid: tokenValue.startsWith('user-token-'),
        userId: 'user-123',
        expiresIn: 3600
      };
    } else {
      return res.status(400).json({
        status: 'error',
        error: 'Invalid token type'
      });
    }
    
    if (!verificationResult.valid) {
      return res.status(401).json({
        status: 'error',
        error: verificationResult.error || 'Invalid token'
      });
    }
    
    res.json({
      status: 'success',
      data: {
        valid: true,
        tokenType,
        expiresIn: verificationResult.expiresIn
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/auth/register-service
 * @desc    Register a new emergency service
 * @access  Private (Admin)
 */
router.post('/register-service', async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { name, type, metadata } = req.body;
    
    // Register service
    const service = novaDNA.emergencyAuthenticator.registerTrustedService({
      name,
      type,
      metadata
    });
    
    res.status(201).json({
      status: 'success',
      data: {
        serviceId: service.serviceId,
        name: service.name,
        type: service.type,
        credentials: {
          apiKey: service.credentials.apiKey,
          apiSecret: service.credentials.apiSecret
        },
        registeredAt: service.registeredAt
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
