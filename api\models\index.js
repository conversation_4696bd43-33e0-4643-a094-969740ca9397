const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  useCreateIndex: true,
  useFindAndModify: false
})
  .then(() => console.log('MongoDB connected'))
  .catch(err => console.error('MongoDB connection error:', err));

// Import models
const User = require('./User');
const Connector = require('./Connector');
const Installation = require('./Installation');
const Review = require('./Review');
const RefreshToken = require('./RefreshToken');
const Submission = require('./Submission');
const Notification = require('./Notification');

// Export models
module.exports = {
  User,
  Connector,
  Installation,
  Review,
  RefreshToken,
  Submission,
  Notification
};
