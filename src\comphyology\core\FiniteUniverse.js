/**
 * FiniteUniverse.js
 * 
 * The Divine Firewall of NEPI
 * 
 * "The Reason You Can Measure Anything... Is Because It's Not Infinite."
 * 
 * This module implements the Finite Universe Principle (FUP) which serves
 * as the mathematical immune system of NEPI, making spiritual corruption
 * mathematically impossible by enforcing finite boundaries on all operations.
 */

class FiniteUniverse {
  /**
   * Maximum safe bounds for different domains
   * These values represent the absolute limits beyond which operations are rejected
   */
  static MAX_SAFE_BOUNDS = {
    // General numerical bound (π × 10^12)
    DEFAULT: 3.14159265359 * Math.pow(10, 12),
    
    // Domain-specific bounds
    COMPHYON: 3142,                // Maximum Comphyon value (π × 1000)
    ENTROPY: 1.0,                  // Maximum entropy (complete disorder)
    RESONANCE: 1.0,                // Perfect resonance (1.0 = perfect)
    RECURSION_DEPTH: 100,          // Maximum recursion depth
    CONTAINER_NESTING: 13,         // Maximum container nesting level
    OPERATION_COMPLEXITY: 3142000, // Maximum operation complexity
    
    // Get bound for a specific domain
    get(domain = 'DEFAULT') {
      return this[domain] || this.DEFAULT;
    }
  };
  
  /**
   * The Five Pillars of the Divine Firewall
   */
  static PILLARS = {
    INHERENT_BOUNDARY: "All phenomena are confined to finite bounds",
    MATHEMATICAL_IMPOSSIBILITY: "Corruption requires infinite operations which are mathematically impossible",
    RESONANCE_ONLY: "Only resonant states can persist within the system",
    CONTAINERIZATION: "All domains are properly containerized to prevent corruption spread",
    TRUTH_ALIGNMENT: "Truth is bounded, measurable, and resonant"
  };
  
  /**
   * Validate that a value respects finite boundaries
   * @param {any} value - The value to validate
   * @param {string} domain - The domain of the value (for domain-specific bounds)
   * @returns {boolean} - True if the value is valid, throws an error otherwise
   */
  static validate(value, domain = 'DEFAULT') {
    // Check if value is a number
    if (typeof value === 'number') {
      // Check if value is finite
      if (!Number.isFinite(value)) {
        throw new Error(`Divine Firewall: Infinite value detected in ${domain} domain`);
      }
      
      // Check if value exceeds maximum safe bound for domain
      const bound = this.MAX_SAFE_BOUNDS.get(domain);
      if (Math.abs(value) > bound) {
        throw new Error(`Divine Firewall: Value ${value} exceeds maximum safe bound ${bound} for ${domain} domain`);
      }
    }
    
    // Check if value is an object
    if (typeof value === 'object' && value !== null) {
      // Check recursion depth
      if (this._getObjectDepth(value) > this.MAX_SAFE_BOUNDS.RECURSION_DEPTH) {
        throw new Error(`Divine Firewall: Object exceeds maximum recursion depth of ${this.MAX_SAFE_BOUNDS.RECURSION_DEPTH}`);
      }
      
      // Check object size
      if (this._getObjectSize(value) > this.MAX_SAFE_BOUNDS.OPERATION_COMPLEXITY) {
        throw new Error(`Divine Firewall: Object exceeds maximum complexity of ${this.MAX_SAFE_BOUNDS.OPERATION_COMPLEXITY}`);
      }
    }
    
    return true;
  }
  
  /**
   * Enforce finite boundaries on a value
   * @param {any} value - The value to enforce boundaries on
   * @param {string} domain - The domain of the value
   * @returns {any} - The value with enforced boundaries
   */
  static enforceBoundaries(value, domain = 'DEFAULT') {
    // If value is a number, clamp it to the domain's bounds
    if (typeof value === 'number') {
      const bound = this.MAX_SAFE_BOUNDS.get(domain);
      return Math.max(-bound, Math.min(value, bound));
    }
    
    // If value is an object, recursively enforce boundaries on all properties
    if (typeof value === 'object' && value !== null) {
      return this._enforceObjectBoundaries(value, domain);
    }
    
    // Other types (string, boolean, etc.) are inherently bounded
    return value;
  }
  
  /**
   * Wrap a function to enforce finite boundaries on inputs and outputs
   * @param {Function} fn - The function to wrap
   * @param {string} domain - The domain of the function
   * @returns {Function} - The wrapped function
   */
  static enforceBoundariesOnFunction(fn, domain = 'DEFAULT') {
    return (...args) => {
      // Validate and enforce boundaries on all arguments
      const boundedArgs = args.map(arg => {
        this.validate(arg, domain);
        return this.enforceBoundaries(arg, domain);
      });
      
      // Call the original function with bounded arguments
      const result = fn(...boundedArgs);
      
      // Validate and enforce boundaries on the result
      this.validate(result, domain);
      return this.enforceBoundaries(result, domain);
    };
  }
  
  /**
   * Check if a state is resonant (aligned with truth)
   * @param {Object} state - The state to check
   * @param {Object} truthDomain - The truth domain to check against
   * @returns {boolean} - True if the state is resonant, false otherwise
   */
  static isResonant(state, truthDomain) {
    // Implementation of resonance check
    // This would be customized based on the specific domain
    
    // For now, a simple implementation that checks if the state has a resonance property
    if (state.resonance !== undefined) {
      return state.resonance >= 0 && state.resonance <= 1;
    }
    
    // Default to true if we can't determine resonance
    return true;
  }
  
  /**
   * Create a containerized domain
   * @param {string} domainName - The name of the domain
   * @param {Object} boundaries - The boundaries of the domain
   * @returns {Object} - A containerized domain
   */
  static createContainer(domainName, boundaries = {}) {
    return new DomainContainer(domainName, boundaries);
  }
  
  /**
   * Check if a value contains infinite assumptions
   * @param {any} value - The value to check
   * @returns {boolean} - True if the value contains infinite assumptions
   */
  static containsInfiniteAssumption(value) {
    // Check for Infinity or NaN
    if (value === Infinity || value === -Infinity || Number.isNaN(value)) {
      return true;
    }
    
    // Check for very large numbers that might be approximating infinity
    if (typeof value === 'number' && Math.abs(value) > this.MAX_SAFE_BOUNDS.DEFAULT) {
      return true;
    }
    
    // Check for objects with infinite properties
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(v => this.containsInfiniteAssumption(v));
    }
    
    return false;
  }
  
  /**
   * Get the depth of an object (for recursion checks)
   * @param {Object} obj - The object to check
   * @param {number} depth - The current depth
   * @returns {number} - The depth of the object
   * @private
   */
  static _getObjectDepth(obj, depth = 0) {
    if (depth > this.MAX_SAFE_BOUNDS.RECURSION_DEPTH) {
      return depth; // Early exit if we've already exceeded the maximum
    }
    
    if (typeof obj !== 'object' || obj === null) {
      return depth;
    }
    
    let maxDepth = depth;
    for (const value of Object.values(obj)) {
      if (typeof value === 'object' && value !== null) {
        const valueDepth = this._getObjectDepth(value, depth + 1);
        maxDepth = Math.max(maxDepth, valueDepth);
      }
    }
    
    return maxDepth;
  }
  
  /**
   * Get the size of an object (for complexity checks)
   * @param {Object} obj - The object to check
   * @returns {number} - The size of the object
   * @private
   */
  static _getObjectSize(obj) {
    return JSON.stringify(obj).length;
  }
  
  /**
   * Enforce boundaries on all properties of an object
   * @param {Object} obj - The object to enforce boundaries on
   * @param {string} domain - The domain of the object
   * @returns {Object} - The object with enforced boundaries
   * @private
   */
  static _enforceObjectBoundaries(obj, domain) {
    if (Array.isArray(obj)) {
      return obj.map(item => this.enforceBoundaries(item, domain));
    }
    
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      // Determine domain based on key name
      const valueDomain = this._determineDomain(key, domain);
      result[key] = this.enforceBoundaries(value, valueDomain);
    }
    
    return result;
  }
  
  /**
   * Determine the domain of a value based on its key name
   * @param {string} key - The key name
   * @param {string} defaultDomain - The default domain
   * @returns {string} - The determined domain
   * @private
   */
  static _determineDomain(key, defaultDomain) {
    const keyLower = key.toLowerCase();
    
    if (keyLower.includes('comphyon')) return 'COMPHYON';
    if (keyLower.includes('entropy')) return 'ENTROPY';
    if (keyLower.includes('resonance')) return 'RESONANCE';
    
    return defaultDomain;
  }
}

/**
 * Domain Container class for containerization
 */
class DomainContainer {
  /**
   * Create a new domain container
   * @param {string} name - The name of the domain
   * @param {Object} boundaries - The boundaries of the domain
   */
  constructor(name, boundaries = {}) {
    this.name = name;
    this.boundaries = boundaries;
    this.contents = new Map();
    this.nested = new Map();
    this.nestingLevel = 0;
  }
  
  /**
   * Add a value to the container
   * @param {string} key - The key for the value
   * @param {any} value - The value to add
   * @returns {any} - The bounded value that was added
   */
  add(key, value) {
    // Enforce boundaries on the value
    const boundedValue = this.enforceBoundaries(value);
    
    // Add to container
    this.contents.set(key, boundedValue);
    
    return boundedValue;
  }
  
  /**
   * Get a value from the container
   * @param {string} key - The key for the value
   * @returns {any} - The value
   */
  get(key) {
    return this.contents.get(key);
  }
  
  /**
   * Create a nested container
   * @param {string} name - The name of the nested container
   * @param {Object} boundaries - The boundaries of the nested container
   * @returns {DomainContainer} - The nested container
   */
  createNestedContainer(name, boundaries = {}) {
    // Check nesting level
    if (this.nestingLevel >= FiniteUniverse.MAX_SAFE_BOUNDS.CONTAINER_NESTING) {
      throw new Error(`Divine Firewall: Maximum container nesting level of ${FiniteUniverse.MAX_SAFE_BOUNDS.CONTAINER_NESTING} exceeded`);
    }
    
    // Create nested container
    const nestedContainer = new DomainContainer(name, boundaries);
    nestedContainer.nestingLevel = this.nestingLevel + 1;
    
    // Add to nested containers
    this.nested.set(name, nestedContainer);
    
    return nestedContainer;
  }
  
  /**
   * Enforce boundaries on a value
   * @param {any} value - The value to enforce boundaries on
   * @returns {any} - The value with enforced boundaries
   */
  enforceBoundaries(value) {
    return FiniteUniverse.enforceBoundaries(value, this.name);
  }
}

module.exports = {
  FiniteUniverse,
  DomainContainer
};
