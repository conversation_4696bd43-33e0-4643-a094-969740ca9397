/**
 * Test script for GraphQL subscriptions
 */

const GraphQLSubscriptionService = require('./services/GraphQLSubscriptionService');

// Create an instance of the GraphQL subscription service
const subscriptionService = new GraphQLSubscriptionService();

// Test endpoint (public GraphQL API with subscription support)
// Note: This is a placeholder. You would need a real GraphQL endpoint with subscription support.
const endpoint = 'https://realtime-chat.hasura.app/v1/graphql';

// Simple subscription query
const query = `
  subscription {
    messages {
      id
      text
      user {
        id
        name
      }
      createdAt
    }
  }
`;

console.log('Testing GraphQL subscription service...');
console.log(`Endpoint: ${endpoint}`);
console.log(`Query: ${query}`);

// Message handler
const onMessage = (message) => {
  console.log('Received message:', JSON.stringify(message, null, 2));
};

// Error handler
const onError = (error) => {
  console.error('Subscription error:', JSON.stringify(error, null, 2));
};

// Create a subscription
subscriptionService.createSubscription(endpoint, query, {}, {}, null, onMessage, onError)
  .then(subscription => {
    console.log('Subscription created:', subscription);
    
    // Keep the subscription active for 30 seconds
    console.log('Listening for messages for 30 seconds...');
    
    setTimeout(() => {
      console.log('Cancelling subscription...');
      subscriptionService.cancelSubscription(subscription.id)
        .then(result => {
          console.log('Subscription cancelled:', result);
          process.exit(0);
        })
        .catch(error => {
          console.error('Error cancelling subscription:', error.message);
          process.exit(1);
        });
    }, 30000);
  })
  .catch(error => {
    console.error('Error creating subscription:', error.message);
    process.exit(1);
  });

// Handle process termination
process.on('SIGINT', () => {
  console.log('Process interrupted. Cleaning up...');
  
  // Get all active subscriptions
  const subscriptions = subscriptionService.getActiveSubscriptions();
  
  // Cancel all subscriptions
  Promise.all(subscriptions.map(sub => subscriptionService.cancelSubscription(sub.id)))
    .then(() => {
      console.log('All subscriptions cancelled');
      process.exit(0);
    })
    .catch(error => {
      console.error('Error cancelling subscriptions:', error.message);
      process.exit(1);
    });
});
