/**
 * Quantum State Inference Configuration
 *
 * This file contains the configuration parameters for the Quantum State Inference Layer.
 * These parameters control the behavior of the quantum inference engine, including
 * entropy thresholds, collapse rates, and Bayesian weighting.
 */

/**
 * Default configuration for Quantum State Inference
 */
const defaultConfig = {
  // Entropy threshold for state collapse (0-1)
  // Higher values increase certainty rate but may reduce accuracy
  entropyThreshold: 0.65, // Increased from 0.5 to significantly improve certainty rate

  // Maximum number of states in superposition
  superpositionLimit: 18, // Increased from 12 to align with 18/82 principle

  // Rate at which states collapse (0-1)
  // Increased to promote more state collapse while maintaining alignment with 18/82 principle
  collapseRate: 0.25,

  // Weight given to prior probabilities in Bayesian inference (0-1)
  // Adjusted for better balance while maintaining complementary relationship
  bayesianPriorWeight: 0.75,

  // Enable quantum memory for state persistence
  enableQuantumMemory: true,

  // Enable metrics collection
  enableMetrics: true,

  // Confidence threshold for actionable intelligence (0-1)
  // Increased to align with 18/82 principle and reduce false positives
  confidenceThreshold: 0.82,

  // Minimum entropy difference for state discrimination
  entropyDiscriminationThreshold: 0.18,

  // Noise reduction filter for signal processing
  noiseReductionFilter: {
    enabled: true,
    threshold: 0.18, // Filter out bottom 18% of signals
    method: 'wavelet' // Use wavelet transform for noise reduction
  },

  // Feature importance weighting
  featureImportanceWeighting: {
    enabled: true,
    method: 'phi_gradient', // Use golden ratio for gradient calculation
    topFeatures: 0.18 // Focus on top 18% of features
  },

  // Maximum time allowed for inference (ms)
  maxInferenceTime: 5,

  // Enable adaptive parameter tuning
  enableAdaptiveTuning: true,

  // Adaptive tuning parameters
  adaptiveTuning: {
    // Learning rate for parameter adaptation (0-1)
    learningRate: 0.018, // Based on 18/82 principle

    // Minimum certainty rate target (0-1)
    minCertaintyRate: 0.5, // Increased to match red carpet target

    // Maximum false positive rate allowed (0-1)
    maxFalsePositiveRate: 0.01, // Reduced to match red carpet target

    // Adaptation interval (number of inferences)
    adaptationInterval: 100,

    // Optimization metric
    optimizationMetric: 'f1_score', // Balance precision and recall

    // Update interval
    updateInterval: 1000 // Update parameters every 1000 inferences
  },

  // Two-stage detection pipeline
  detectionPipeline: {
    enabled: true,
    stage1: {
      // High recall stage
      recallTarget: 0.99,
      precisionMinimum: 0.5
    },
    stage2: {
      // High precision stage
      precisionTarget: 0.99,
      recallMinimum: 0.82
    }
  },

  // Advanced parameters
  advanced: {
    // Use phi-weighted entropy calculation
    usePhiWeightedEntropy: true,

    // Phi value (golden ratio) for weighting
    phiValue: 1.618,

    // Pi scaling factor for normalization
    piScalingFactor: 3.14159,

    // Use quantum-inspired noise reduction
    useQuantumNoiseReduction: true,

    // Noise reduction strength (0-1)
    noiseReductionStrength: 0.18,

    // Enable state vector optimization
    enableStateVectorOptimization: true,

    // State vector optimization method
    stateVectorOptimizationMethod: 'phi-gradient-descent',

    // Enable entropy collapse acceleration
    enableEntropyCollapseAcceleration: true,

    // Entropy collapse acceleration factor
    entropyCollapseAccelerationFactor: 1.618,

    // Quantum-inspired annealing
    quantumAnnealing: {
      enabled: true,
      initialTemperature: 3.14159, // π
      coolingRate: 0.618, // φ
      iterations: 1000
    },

    // Ensemble approach
    ensemble: {
      enabled: true,
      models: [
        { type: 'high_certainty', weight: 0.33 },
        { type: 'low_false_positive', weight: 0.33 },
        { type: 'balanced', weight: 0.34 }
      ],
      votingMethod: 'weighted'
    }
  }
};

/**
 * Configuration for high-certainty scenarios
 * Optimized for maximum certainty rate at the expense of some precision
 */
const highCertaintyConfig = {
  ...defaultConfig,
  entropyThreshold: 0.75, // Increased for maximum certainty
  collapseRate: 0.30, // Increased for maximum certainty
  bayesianPriorWeight: 0.70, // Adjusted for balance
  confidenceThreshold: 0.75, // Higher threshold for confident predictions
  noiseReductionFilter: {
    ...defaultConfig.noiseReductionFilter,
    threshold: 0.25 // More aggressive noise filtering
  },
  adaptiveTuning: {
    ...defaultConfig.adaptiveTuning,
    minCertaintyRate: 0.6, // Target 60% certainty rate
    maxFalsePositiveRate: 0.05, // Allow slightly higher false positives
    optimizationMetric: 'certainty_rate' // Optimize for certainty
  },
  detectionPipeline: {
    ...defaultConfig.detectionPipeline,
    stage2: {
      precisionTarget: 0.95, // Slightly lower precision target
      recallMinimum: 0.90 // Higher recall minimum
    }
  }
};

/**
 * Configuration for high-precision scenarios
 * Optimized for maximum precision at the expense of certainty rate
 */
const highPrecisionConfig = {
  ...defaultConfig,
  entropyThreshold: 0.55, // Slightly lower than default for better precision
  collapseRate: 0.18, // Standard 18/82 collapse rate
  bayesianPriorWeight: 0.82, // Standard 18/82 Bayesian weight
  confidenceThreshold: 0.95, // Very high confidence threshold to minimize false positives
  noiseReductionFilter: {
    ...defaultConfig.noiseReductionFilter,
    threshold: 0.30, // Very aggressive noise filtering
    method: 'wavelet_adaptive' // Advanced adaptive wavelet filtering
  },
  featureImportanceWeighting: {
    ...defaultConfig.featureImportanceWeighting,
    topFeatures: 0.10 // Focus on only the top 10% of features
  },
  adaptiveTuning: {
    ...defaultConfig.adaptiveTuning,
    minCertaintyRate: 0.30, // Lower certainty rate acceptable
    maxFalsePositiveRate: 0.005, // Extremely low false positive target
    optimizationMetric: 'precision' // Optimize for precision
  },
  detectionPipeline: {
    ...defaultConfig.detectionPipeline,
    stage1: {
      recallTarget: 0.90, // Lower recall target
      precisionMinimum: 0.80 // Higher precision minimum
    },
    stage2: {
      precisionTarget: 0.995, // Extremely high precision target
      recallMinimum: 0.70 // Lower recall minimum
    }
  },
  advanced: {
    ...defaultConfig.advanced,
    quantumAnnealing: {
      ...defaultConfig.advanced.quantumAnnealing,
      coolingRate: 0.5 // Faster cooling for more precise convergence
    }
  }
};

/**
 * Configuration for balanced scenarios
 * Provides a balance between certainty rate and precision
 */
const balancedConfig = {
  ...defaultConfig,
  // Use default entropy threshold, collapse rate, and Bayesian weight
  // but optimize other parameters for balance
  confidenceThreshold: 0.85, // High confidence threshold but not extreme
  noiseReductionFilter: {
    ...defaultConfig.noiseReductionFilter,
    threshold: 0.22 // Moderate noise filtering
  },
  featureImportanceWeighting: {
    ...defaultConfig.featureImportanceWeighting,
    topFeatures: 0.15 // Focus on top 15% of features
  },
  adaptiveTuning: {
    ...defaultConfig.adaptiveTuning,
    minCertaintyRate: 0.45, // Slightly below target but still high
    maxFalsePositiveRate: 0.015, // Slightly above target but still low
    optimizationMetric: 'f1_score' // Balance precision and recall
  },
  detectionPipeline: {
    ...defaultConfig.detectionPipeline,
    stage1: {
      recallTarget: 0.95, // High recall but not extreme
      precisionMinimum: 0.65 // Moderate precision minimum
    },
    stage2: {
      precisionTarget: 0.98, // High precision but not extreme
      recallMinimum: 0.80 // Good recall minimum
    }
  },
  advanced: {
    ...defaultConfig.advanced,
    ensemble: {
      ...defaultConfig.advanced.ensemble,
      models: [
        { type: 'high_certainty', weight: 0.40 },
        { type: 'low_false_positive', weight: 0.40 },
        { type: 'standard', weight: 0.20 }
      ]
    }
  }
};

/**
 * Get configuration based on scenario type
 * @param {string} scenario - Scenario type ('default', 'high-certainty', 'high-precision', 'balanced')
 * @returns {Object} - Configuration object
 */
function getConfig(scenario = 'default') {
  switch (scenario.toLowerCase()) {
    case 'high-certainty':
      return highCertaintyConfig;
    case 'high-precision':
      return highPrecisionConfig;
    case 'balanced':
      return balancedConfig;
    default:
      return defaultConfig;
  }
}

module.exports = {
  defaultConfig,
  highCertaintyConfig,
  highPrecisionConfig,
  balancedConfig,
  getConfig
};
