/**
 * NovaFuse AI-Powered Test Data Generator
 * 
 * This module uses advanced techniques to generate realistic test data for NovaFuse components.
 * It can create complex, interconnected data that mimics real-world scenarios.
 */

const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const faker = require('faker');

// Compliance frameworks and their controls
const COMPLIANCE_FRAMEWORKS = {
  'GDPR': {
    description: 'General Data Protection Regulation',
    categories: ['data_subject_rights', 'consent', 'data_breach', 'dpo', 'data_protection', 'records_of_processing', 'international_transfers', 'privacy_by_design'],
    controls: [
      { id: 'GDPR-DSR-1', name: 'Right to Access', category: 'data_subject_rights' },
      { id: 'GDPR-DSR-2', name: 'Right to Rectification', category: 'data_subject_rights' },
      { id: 'GDPR-DSR-3', name: 'Right to Erasure', category: 'data_subject_rights' },
      { id: 'GDPR-DSR-4', name: 'Right to Restrict Processing', category: 'data_subject_rights' },
      { id: 'GDPR-DSR-5', name: 'Right to Data Portability', category: 'data_subject_rights' },
      { id: 'GDPR-DSR-6', name: 'Right to Object', category: 'data_subject_rights' },
      { id: 'GDPR-CON-1', name: 'Valid Consent', category: 'consent' },
      { id: 'GDPR-CON-2', name: 'Consent Withdrawal', category: 'consent' },
      { id: 'GDPR-CON-3', name: 'Child Consent', category: 'consent' },
      { id: 'GDPR-BRE-1', name: 'Breach Notification to Authority', category: 'data_breach' },
      { id: 'GDPR-BRE-2', name: 'Breach Notification to Data Subject', category: 'data_breach' },
      { id: 'GDPR-DPO-1', name: 'DPO Appointment', category: 'dpo' },
      { id: 'GDPR-DPO-2', name: 'DPO Responsibilities', category: 'dpo' },
      { id: 'GDPR-DPR-1', name: 'Data Protection Impact Assessment', category: 'data_protection' },
      { id: 'GDPR-DPR-2', name: 'Data Minimization', category: 'data_protection' },
      { id: 'GDPR-DPR-3', name: 'Storage Limitation', category: 'data_protection' },
      { id: 'GDPR-ROP-1', name: 'Records of Processing Activities', category: 'records_of_processing' },
      { id: 'GDPR-INT-1', name: 'Adequacy Decision', category: 'international_transfers' },
      { id: 'GDPR-INT-2', name: 'Appropriate Safeguards', category: 'international_transfers' },
      { id: 'GDPR-PBD-1', name: 'Privacy by Design', category: 'privacy_by_design' },
      { id: 'GDPR-PBD-2', name: 'Privacy by Default', category: 'privacy_by_design' }
    ]
  },
  'SOC2': {
    description: 'Service Organization Control 2',
    categories: ['security', 'availability', 'processing_integrity', 'confidentiality', 'privacy'],
    controls: [
      { id: 'SOC2-SEC-1', name: 'Information Security Policy', category: 'security' },
      { id: 'SOC2-SEC-2', name: 'Risk Management', category: 'security' },
      { id: 'SOC2-SEC-3', name: 'Access Control', category: 'security' },
      { id: 'SOC2-SEC-4', name: 'System Operations', category: 'security' },
      { id: 'SOC2-SEC-5', name: 'Change Management', category: 'security' },
      { id: 'SOC2-AVA-1', name: 'Availability Policy', category: 'availability' },
      { id: 'SOC2-AVA-2', name: 'Backup and Recovery', category: 'availability' },
      { id: 'SOC2-AVA-3', name: 'Disaster Recovery', category: 'availability' },
      { id: 'SOC2-PIN-1', name: 'Processing Integrity Policy', category: 'processing_integrity' },
      { id: 'SOC2-PIN-2', name: 'Quality Assurance', category: 'processing_integrity' },
      { id: 'SOC2-CON-1', name: 'Confidentiality Policy', category: 'confidentiality' },
      { id: 'SOC2-CON-2', name: 'Data Classification', category: 'confidentiality' },
      { id: 'SOC2-CON-3', name: 'Data Retention and Disposal', category: 'confidentiality' },
      { id: 'SOC2-PRI-1', name: 'Privacy Notice', category: 'privacy' },
      { id: 'SOC2-PRI-2', name: 'Choice and Consent', category: 'privacy' },
      { id: 'SOC2-PRI-3', name: 'Collection', category: 'privacy' },
      { id: 'SOC2-PRI-4', name: 'Use, Retention, and Disposal', category: 'privacy' },
      { id: 'SOC2-PRI-5', name: 'Access', category: 'privacy' },
      { id: 'SOC2-PRI-6', name: 'Disclosure to Third Parties', category: 'privacy' },
      { id: 'SOC2-PRI-7', name: 'Security for Privacy', category: 'privacy' },
      { id: 'SOC2-PRI-8', name: 'Quality', category: 'privacy' },
      { id: 'SOC2-PRI-9', name: 'Monitoring and Enforcement', category: 'privacy' }
    ]
  },
  'HIPAA': {
    description: 'Health Insurance Portability and Accountability Act',
    categories: ['privacy_rule', 'security_rule', 'breach_notification_rule'],
    controls: [
      { id: 'HIPAA-PRV-1', name: 'Notice of Privacy Practices', category: 'privacy_rule' },
      { id: 'HIPAA-PRV-2', name: 'Minimum Necessary', category: 'privacy_rule' },
      { id: 'HIPAA-PRV-3', name: 'Patient Rights', category: 'privacy_rule' },
      { id: 'HIPAA-PRV-4', name: 'Administrative Requirements', category: 'privacy_rule' },
      { id: 'HIPAA-SEC-1', name: 'Administrative Safeguards', category: 'security_rule' },
      { id: 'HIPAA-SEC-2', name: 'Physical Safeguards', category: 'security_rule' },
      { id: 'HIPAA-SEC-3', name: 'Technical Safeguards', category: 'security_rule' },
      { id: 'HIPAA-SEC-4', name: 'Organizational Requirements', category: 'security_rule' },
      { id: 'HIPAA-SEC-5', name: 'Policies and Procedures', category: 'security_rule' },
      { id: 'HIPAA-BRE-1', name: 'Breach Notification', category: 'breach_notification_rule' },
      { id: 'HIPAA-BRE-2', name: 'Breach Assessment', category: 'breach_notification_rule' }
    ]
  },
  'ISO27001': {
    description: 'ISO/IEC 27001 - Information Security Management',
    categories: ['information_security_policies', 'organization_of_information_security', 'human_resource_security', 'asset_management', 'access_control', 'cryptography', 'physical_and_environmental_security', 'operations_security', 'communications_security', 'system_acquisition', 'supplier_relationships', 'information_security_incident_management', 'business_continuity', 'compliance'],
    controls: [
      { id: 'ISO-A5.1', name: 'Management direction for information security', category: 'information_security_policies' },
      { id: 'ISO-A6.1', name: 'Internal organization', category: 'organization_of_information_security' },
      { id: 'ISO-A6.2', name: 'Mobile devices and teleworking', category: 'organization_of_information_security' },
      { id: 'ISO-A7.1', name: 'Prior to employment', category: 'human_resource_security' },
      { id: 'ISO-A7.2', name: 'During employment', category: 'human_resource_security' },
      { id: 'ISO-A7.3', name: 'Termination and change of employment', category: 'human_resource_security' },
      { id: 'ISO-A8.1', name: 'Responsibility for assets', category: 'asset_management' },
      { id: 'ISO-A8.2', name: 'Information classification', category: 'asset_management' },
      { id: 'ISO-A8.3', name: 'Media handling', category: 'asset_management' },
      { id: 'ISO-A9.1', name: 'Business requirements of access control', category: 'access_control' },
      { id: 'ISO-A9.2', name: 'User access management', category: 'access_control' },
      { id: 'ISO-A9.3', name: 'User responsibilities', category: 'access_control' },
      { id: 'ISO-A9.4', name: 'System and application access control', category: 'access_control' },
      { id: 'ISO-A10.1', name: 'Cryptographic controls', category: 'cryptography' },
      { id: 'ISO-A11.1', name: 'Secure areas', category: 'physical_and_environmental_security' },
      { id: 'ISO-A11.2', name: 'Equipment', category: 'physical_and_environmental_security' },
      { id: 'ISO-A12.1', name: 'Operational procedures and responsibilities', category: 'operations_security' },
      { id: 'ISO-A12.2', name: 'Protection from malware', category: 'operations_security' },
      { id: 'ISO-A12.3', name: 'Backup', category: 'operations_security' },
      { id: 'ISO-A12.4', name: 'Logging and monitoring', category: 'operations_security' },
      { id: 'ISO-A12.5', name: 'Control of operational software', category: 'operations_security' },
      { id: 'ISO-A12.6', name: 'Technical vulnerability management', category: 'operations_security' },
      { id: 'ISO-A12.7', name: 'Information systems audit considerations', category: 'operations_security' },
      { id: 'ISO-A13.1', name: 'Network security management', category: 'communications_security' },
      { id: 'ISO-A13.2', name: 'Information transfer', category: 'communications_security' },
      { id: 'ISO-A14.1', name: 'Security requirements of information systems', category: 'system_acquisition' },
      { id: 'ISO-A14.2', name: 'Security in development and support processes', category: 'system_acquisition' },
      { id: 'ISO-A14.3', name: 'Test data', category: 'system_acquisition' },
      { id: 'ISO-A15.1', name: 'Information security in supplier relationships', category: 'supplier_relationships' },
      { id: 'ISO-A15.2', name: 'Supplier service delivery management', category: 'supplier_relationships' },
      { id: 'ISO-A16.1', name: 'Management of information security incidents and improvements', category: 'information_security_incident_management' },
      { id: 'ISO-A17.1', name: 'Information security continuity', category: 'business_continuity' },
      { id: 'ISO-A17.2', name: 'Redundancies', category: 'business_continuity' },
      { id: 'ISO-A18.1', name: 'Compliance with legal and contractual requirements', category: 'compliance' },
      { id: 'ISO-A18.2', name: 'Information security reviews', category: 'compliance' }
    ]
  },
  'PCI-DSS': {
    description: 'Payment Card Industry Data Security Standard',
    categories: ['build_and_maintain_secure_network', 'protect_cardholder_data', 'maintain_vulnerability_management', 'implement_strong_access_control', 'regularly_monitor_and_test_networks', 'maintain_information_security_policy'],
    controls: [
      { id: 'PCI-1.1', name: 'Install and maintain a firewall configuration', category: 'build_and_maintain_secure_network' },
      { id: 'PCI-1.2', name: 'Do not use vendor-supplied defaults', category: 'build_and_maintain_secure_network' },
      { id: 'PCI-2.1', name: 'Protect stored cardholder data', category: 'protect_cardholder_data' },
      { id: 'PCI-2.2', name: 'Encrypt transmission of cardholder data', category: 'protect_cardholder_data' },
      { id: 'PCI-3.1', name: 'Use and regularly update anti-virus software', category: 'maintain_vulnerability_management' },
      { id: 'PCI-3.2', name: 'Develop and maintain secure systems and applications', category: 'maintain_vulnerability_management' },
      { id: 'PCI-4.1', name: 'Restrict access to cardholder data by business need-to-know', category: 'implement_strong_access_control' },
      { id: 'PCI-4.2', name: 'Assign a unique ID to each person with computer access', category: 'implement_strong_access_control' },
      { id: 'PCI-4.3', name: 'Restrict physical access to cardholder data', category: 'implement_strong_access_control' },
      { id: 'PCI-5.1', name: 'Track and monitor all access to network resources and cardholder data', category: 'regularly_monitor_and_test_networks' },
      { id: 'PCI-5.2', name: 'Regularly test security systems and processes', category: 'regularly_monitor_and_test_networks' },
      { id: 'PCI-6.1', name: 'Maintain a policy that addresses information security', category: 'maintain_information_security_policy' }
    ]
  }
};

// Activity types and their descriptions
const ACTIVITY_TYPES = {
  'task': 'A specific action item to be completed',
  'meeting': 'A scheduled discussion or review',
  'review': 'An assessment or evaluation of compliance status',
  'audit': 'A formal examination of compliance evidence',
  'documentation': 'Creation or update of compliance documentation',
  'training': 'Educational activities for staff',
  'implementation': 'Deployment of technical controls or processes',
  'assessment': 'Evaluation of risks or controls',
  'remediation': 'Addressing identified gaps or issues',
  'monitoring': 'Ongoing surveillance of compliance status',
  'reporting': 'Creating formal reports on compliance status',
  'other': 'Other compliance-related activities'
};

// Status options for requirements and activities
const STATUS_OPTIONS = ['pending', 'in_progress', 'completed', 'deferred', 'cancelled'];

// Priority options for requirements
const PRIORITY_OPTIONS = ['low', 'medium', 'high', 'critical'];

// User roles for assignment
const USER_ROLES = [
  'compliance_officer',
  'privacy_officer',
  'security_officer',
  'data_protection_officer',
  'it_manager',
  'system_administrator',
  'developer',
  'legal_counsel',
  'risk_manager',
  'auditor',
  'business_owner',
  'executive'
];

/**
 * Generate a random date within a range
 * 
 * @param {Date} start - Start date
 * @param {Date} end - End date
 * @returns {string} - ISO date string
 */
function randomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime())).toISOString().split('T')[0];
}

/**
 * Generate a random requirement
 * 
 * @param {Object} options - Options for generating the requirement
 * @returns {Object} - Generated requirement
 */
function generateRequirement(options = {}) {
  // Select a random framework if not specified
  const framework = options.framework || Object.keys(COMPLIANCE_FRAMEWORKS)[Math.floor(Math.random() * Object.keys(COMPLIANCE_FRAMEWORKS).length)];
  
  // Get framework details
  const frameworkDetails = COMPLIANCE_FRAMEWORKS[framework];
  
  // Select a random control from the framework
  const control = options.control || frameworkDetails.controls[Math.floor(Math.random() * frameworkDetails.controls.length)];
  
  // Generate a requirement ID if not provided
  const id = options.id || `req-${uuidv4()}`;
  
  // Generate a requirement name if not provided
  const name = options.name || `Implement ${control.name} Control`;
  
  // Generate a requirement description if not provided
  const description = options.description || `Ensure compliance with ${framework} ${control.id} by implementing appropriate controls and processes for ${control.name}.`;
  
  // Select a random category if not specified
  const category = options.category || control.category;
  
  // Select a random priority if not specified
  const priority = options.priority || PRIORITY_OPTIONS[Math.floor(Math.random() * PRIORITY_OPTIONS.length)];
  
  // Select a random status if not specified
  const status = options.status || STATUS_OPTIONS[Math.floor(Math.random() * STATUS_OPTIONS.length)];
  
  // Generate random dates
  const currentDate = new Date();
  const oneYearLater = new Date();
  oneYearLater.setFullYear(currentDate.getFullYear() + 1);
  
  const due_date = options.due_date || randomDate(currentDate, oneYearLater);
  
  // Select a random assignee if not specified
  const assigned_to = options.assigned_to || USER_ROLES[Math.floor(Math.random() * USER_ROLES.length)];
  
  // Generate random tags if not specified
  const tags = options.tags || [
    framework.toLowerCase(),
    control.id.toLowerCase(),
    category,
    priority
  ];
  
  // Generate timestamps
  const created_at = options.created_at || new Date().toISOString();
  const updated_at = options.updated_at || created_at;
  
  // Return the generated requirement
  return {
    id,
    name,
    description,
    framework,
    category,
    priority,
    status,
    due_date,
    assigned_to,
    tags,
    created_at,
    updated_at,
    control_id: control.id
  };
}

/**
 * Generate a random activity
 * 
 * @param {Object} options - Options for generating the activity
 * @returns {Object} - Generated activity
 */
function generateActivity(options = {}) {
  // Generate an activity ID if not provided
  const id = options.id || `act-${uuidv4()}`;
  
  // Select a random activity type if not specified
  const type = options.type || Object.keys(ACTIVITY_TYPES)[Math.floor(Math.random() * Object.keys(ACTIVITY_TYPES).length)];
  
  // Generate an activity name if not provided
  const name = options.name || `${type.charAt(0).toUpperCase() + type.slice(1)}: ${faker.company.bs()}`;
  
  // Generate an activity description if not provided
  const description = options.description || ACTIVITY_TYPES[type] + ': ' + faker.lorem.paragraph();
  
  // Select a random status if not specified
  const status = options.status || STATUS_OPTIONS[Math.floor(Math.random() * STATUS_OPTIONS.length)];
  
  // Generate random dates
  const currentDate = new Date();
  const threeMonthsLater = new Date();
  threeMonthsLater.setMonth(currentDate.getMonth() + 3);
  
  const start_date = options.start_date || randomDate(currentDate, threeMonthsLater);
  
  const endDateStart = new Date(start_date);
  const threeMonthsAfterStart = new Date(endDateStart);
  threeMonthsAfterStart.setMonth(endDateStart.getMonth() + 3);
  
  const end_date = options.end_date || randomDate(endDateStart, threeMonthsAfterStart);
  
  // Select a random assignee if not specified
  const assigned_to = options.assigned_to || USER_ROLES[Math.floor(Math.random() * USER_ROLES.length)];
  
  // Generate random notes if not specified
  const notes = options.notes || faker.lorem.paragraphs(2);
  
  // Generate timestamps
  const created_at = options.created_at || new Date().toISOString();
  const updated_at = options.updated_at || created_at;
  
  // Return the generated activity
  return {
    id,
    name,
    description,
    requirement_id: options.requirement_id || null,
    type,
    status,
    start_date,
    end_date,
    assigned_to,
    notes,
    created_at,
    updated_at
  };
}

/**
 * Generate multiple requirements
 * 
 * @param {number} count - Number of requirements to generate
 * @param {Object} options - Options for generating requirements
 * @returns {Array} - Array of generated requirements
 */
function generateRequirements(count = 10, options = {}) {
  const requirements = [];
  
  for (let i = 0; i < count; i++) {
    requirements.push(generateRequirement(options));
  }
  
  return requirements;
}

/**
 * Generate multiple activities
 * 
 * @param {number} count - Number of activities to generate
 * @param {Object} options - Options for generating activities
 * @returns {Array} - Array of generated activities
 */
function generateActivities(count = 10, options = {}) {
  const activities = [];
  
  for (let i = 0; i < count; i++) {
    activities.push(generateActivity(options));
  }
  
  return activities;
}

/**
 * Generate a complete test dataset with requirements and activities
 * 
 * @param {Object} options - Options for generating the dataset
 * @returns {Object} - Generated dataset with requirements and activities
 */
function generateTestDataset(options = {}) {
  const requirementCount = options.requirementCount || 10;
  const activitiesPerRequirement = options.activitiesPerRequirement || 3;
  
  // Generate requirements
  const requirements = generateRequirements(requirementCount, options);
  
  // Generate activities for each requirement
  const activities = [];
  
  requirements.forEach(requirement => {
    for (let i = 0; i < activitiesPerRequirement; i++) {
      activities.push(generateActivity({
        requirement_id: requirement.id,
        ...options
      }));
    }
  });
  
  return {
    requirements,
    activities
  };
}

/**
 * Generate a complete test dataset with requirements and activities for a specific framework
 * 
 * @param {string} framework - The compliance framework to generate data for
 * @param {Object} options - Options for generating the dataset
 * @returns {Object} - Generated dataset with requirements and activities
 */
function generateFrameworkDataset(framework, options = {}) {
  if (!COMPLIANCE_FRAMEWORKS[framework]) {
    throw new Error(`Unknown framework: ${framework}`);
  }
  
  return generateTestDataset({
    ...options,
    framework
  });
}

/**
 * Generate a realistic compliance program with multiple frameworks
 * 
 * @param {Object} options - Options for generating the compliance program
 * @returns {Object} - Generated compliance program with requirements and activities
 */
function generateComplianceProgram(options = {}) {
  const frameworks = options.frameworks || Object.keys(COMPLIANCE_FRAMEWORKS);
  const requirementsPerFramework = options.requirementsPerFramework || 5;
  const activitiesPerRequirement = options.activitiesPerRequirement || 3;
  
  let allRequirements = [];
  let allActivities = [];
  
  frameworks.forEach(framework => {
    const dataset = generateFrameworkDataset(framework, {
      requirementCount: requirementsPerFramework,
      activitiesPerRequirement: activitiesPerRequirement,
      ...options
    });
    
    allRequirements = [...allRequirements, ...dataset.requirements];
    allActivities = [...allActivities, ...dataset.activities];
  });
  
  return {
    requirements: allRequirements,
    activities: allActivities
  };
}

/**
 * Save generated data to JSON files
 * 
 * @param {Object} data - The data to save
 * @param {string} outputDir - The directory to save the files to
 */
function saveTestData(data, outputDir) {
  // Create the output directory if it doesn't exist
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Save requirements
  fs.writeFileSync(
    path.join(outputDir, 'requirements.json'),
    JSON.stringify(data.requirements, null, 2)
  );
  
  // Save activities
  fs.writeFileSync(
    path.join(outputDir, 'activities.json'),
    JSON.stringify(data.activities, null, 2)
  );
  
  // Save combined data
  fs.writeFileSync(
    path.join(outputDir, 'test-data.json'),
    JSON.stringify(data, null, 2)
  );
  
  console.log(`Test data saved to ${outputDir}`);
}

/**
 * Generate evidence files for compliance testing
 * 
 * @param {Object} data - The test data
 * @param {string} outputDir - The directory to save the evidence files to
 */
function generateEvidenceFiles(data, outputDir) {
  // Create the evidence directory if it doesn't exist
  const evidenceDir = path.join(outputDir, 'evidence');
  if (!fs.existsSync(evidenceDir)) {
    fs.mkdirSync(evidenceDir, { recursive: true });
  }
  
  // Generate evidence files for completed activities
  data.activities.forEach(activity => {
    if (activity.status === 'completed') {
      const evidenceFileName = `${activity.id}-evidence.txt`;
      const evidenceFilePath = path.join(evidenceDir, evidenceFileName);
      
      // Generate evidence content based on activity type
      let evidenceContent = `Evidence for: ${activity.name}\n`;
      evidenceContent += `Type: ${activity.type}\n`;
      evidenceContent += `Date: ${activity.end_date}\n`;
      evidenceContent += `Completed by: ${activity.assigned_to}\n\n`;
      
      switch (activity.type) {
        case 'documentation':
          evidenceContent += faker.lorem.paragraphs(5);
          break;
        case 'meeting':
          evidenceContent += `Meeting Minutes:\n\n`;
          evidenceContent += `Attendees: ${faker.name.findName()}, ${faker.name.findName()}, ${faker.name.findName()}\n`;
          evidenceContent += `Agenda: ${faker.lorem.sentences(3)}\n\n`;
          evidenceContent += `Discussion:\n${faker.lorem.paragraphs(3)}\n\n`;
          evidenceContent += `Action Items:\n`;
          evidenceContent += `1. ${faker.lorem.sentence()}\n`;
          evidenceContent += `2. ${faker.lorem.sentence()}\n`;
          evidenceContent += `3. ${faker.lorem.sentence()}\n`;
          break;
        case 'audit':
          evidenceContent += `Audit Report:\n\n`;
          evidenceContent += `Scope: ${faker.lorem.paragraph()}\n\n`;
          evidenceContent += `Findings:\n`;
          evidenceContent += `1. ${faker.lorem.sentence()}\n`;
          evidenceContent += `2. ${faker.lorem.sentence()}\n`;
          evidenceContent += `3. ${faker.lorem.sentence()}\n\n`;
          evidenceContent += `Recommendations:\n`;
          evidenceContent += `1. ${faker.lorem.sentence()}\n`;
          evidenceContent += `2. ${faker.lorem.sentence()}\n`;
          break;
        case 'implementation':
          evidenceContent += `Implementation Details:\n\n`;
          evidenceContent += `System: ${faker.commerce.productName()}\n`;
          evidenceContent += `Version: ${faker.system.semver()}\n`;
          evidenceContent += `Configuration: ${faker.lorem.paragraph()}\n\n`;
          evidenceContent += `Testing Results: ${faker.lorem.paragraph()}\n`;
          break;
        default:
          evidenceContent += faker.lorem.paragraphs(3);
      }
      
      // Write the evidence file
      fs.writeFileSync(evidenceFilePath, evidenceContent);
    }
  });
  
  console.log(`Evidence files generated in ${evidenceDir}`);
}

// Export the functions
module.exports = {
  generateRequirement,
  generateActivity,
  generateRequirements,
  generateActivities,
  generateTestDataset,
  generateFrameworkDataset,
  generateComplianceProgram,
  saveTestData,
  generateEvidenceFiles,
  COMPLIANCE_FRAMEWORKS,
  ACTIVITY_TYPES,
  STATUS_OPTIONS,
  PRIORITY_OPTIONS,
  USER_ROLES
};
