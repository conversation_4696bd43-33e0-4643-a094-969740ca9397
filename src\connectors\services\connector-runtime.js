/**
 * NovaFuse Universal API Connector - Connector Runtime Service
 *
 * This module provides a service for executing connectors.
 */

const connectorRegistryService = require('./connector-registry');
const connectorConfigService = require('./connector-config');
const { createLogger } = require('../../utils/logger');

const logger = createLogger('connector-runtime-service');

/**
 * Connector Runtime Service class
 */
class ConnectorRuntimeService {
  constructor() {
    this.activeExecutions = new Map();
    logger.info('Connector runtime service initialized');
  }

  /**
   * Execute a connector
   *
   * @param {string} connectorId - The connector ID
   * @param {string} configId - The configuration ID
   * @param {Object} options - Execution options
   * @returns {Promise<Object>} - The execution result
   */
  async executeConnector(connectorId, configId, options = {}) {
    const startTime = Date.now();
    const executionId = `exec-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    try {
      // Get connector
      const connector = await connectorRegistryService.getConnector(connectorId);

      // Get configuration with decrypted sensitive values
      const config = await connectorConfigService.getConfiguration(configId, true);

      logger.info(`Executing connector: ${connector.name} with config: ${config.name}`, {
        executionId,
        connectorId,
        configId,
        options
      });

      // Create execution context
      const context = {
        executionId,
        connectorId: connector.id,
        connectorName: connector.name,
        configId: config.id,
        configName: config.name,
        startTime
      };

      // Track active execution
      this.activeExecutions.set(executionId, {
        context,
        status: 'running',
        startTime
      });

      // Simulate connector execution
      await new Promise(resolve => setTimeout(resolve, 500));

      // Create result
      const result = {
        success: true,
        message: 'Connector executed successfully',
        data: {
          connectorId: connector.id,
          connectorName: connector.name,
          timestamp: new Date().toISOString()
        }
      };

      // Update active execution
      const endTime = Date.now();
      this.activeExecutions.set(executionId, {
        context,
        status: 'completed',
        startTime,
        endTime,
        result
      });

      return {
        executionId,
        connectorId: connector.id,
        connectorName: connector.name,
        configId: config.id,
        configName: config.name,
        startTime: new Date(startTime).toISOString(),
        endTime: new Date(endTime).toISOString(),
        duration: endTime - startTime,
        status: 'success',
        result
      };
    } catch (error) {
      const endTime = Date.now();

      logger.error(`Error executing connector: ${error.message}`, {
        executionId,
        connectorId,
        configId,
        error
      });

      // Update active execution
      this.activeExecutions.set(executionId, {
        context: {
          executionId,
          connectorId,
          configId,
          startTime
        },
        status: 'failed',
        startTime,
        endTime,
        error: error.message
      });

      return {
        executionId,
        connectorId,
        configId,
        startTime: new Date(startTime).toISOString(),
        endTime: new Date(endTime).toISOString(),
        duration: endTime - startTime,
        status: 'error',
        error: {
          message: error.message,
          code: error.code || 'EXECUTION_ERROR'
        }
      };
    }
  }

  /**
   * Get execution status
   *
   * @param {string} executionId - The execution ID
   * @returns {Object} - The execution status
   */
  getExecutionStatus(executionId) {
    const execution = this.activeExecutions.get(executionId);

    if (!execution) {
      return {
        executionId,
        status: 'unknown',
        message: 'Execution not found'
      };
    }

    return {
      executionId,
      connectorId: execution.context.connectorId,
      connectorName: execution.context.connectorName,
      configId: execution.context.configId,
      configName: execution.context.configName,
      startTime: new Date(execution.startTime).toISOString(),
      endTime: execution.endTime ? new Date(execution.endTime).toISOString() : null,
      duration: execution.endTime ? execution.endTime - execution.startTime : Date.now() - execution.startTime,
      status: execution.status,
      result: execution.result,
      error: execution.error
    };
  }

  /**
   * Get all active executions
   *
   * @returns {Array<Object>} - The active executions
   */
  getAllExecutions() {
    return Array.from(this.activeExecutions.entries()).map(([executionId, execution]) => ({
      executionId,
      connectorId: execution.context.connectorId,
      status: execution.status,
      startTime: new Date(execution.startTime).toISOString(),
      duration: execution.endTime ? execution.endTime - execution.startTime : Date.now() - execution.startTime
    }));
  }

  /**
   * Cancel an execution
   *
   * @param {string} executionId - The execution ID
   * @returns {boolean} - Whether the execution was cancelled
   */
  cancelExecution(executionId) {
    const execution = this.activeExecutions.get(executionId);

    if (!execution || execution.status !== 'running') {
      return false;
    }

    // Update execution status
    execution.status = 'cancelled';
    execution.endTime = Date.now();

    return true;
  }
}

// Create singleton instance
const connectorRuntimeService = new ConnectorRuntimeService();

module.exports = connectorRuntimeService;
