import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';

const NavContainer = styled.div`
  background-color: #f8f9fa;
  border-bottom: 1px solid #ddd;
  padding: 10px 0;
  margin-bottom: 20px;
`;

const NavInner = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
`;

const MainNav = styled.div`
  display: flex;
  overflow-x: auto;
  padding-bottom: 10px;

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }
`;

const NavLink = styled(Link)`
  padding: 8px 16px;
  margin-right: 8px;
  text-decoration: none;
  color: ${props => props.active ? 'white' : '#333'};
  background-color: ${props => props.active ? '#555' : '#e9ecef'};
  border-radius: 4px;
  white-space: nowrap;
  font-weight: ${props => props.active ? 'bold' : 'normal'};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${props => props.active ? '#666' : '#dee2e6'};
  }
`;

const SubNav = styled.div`
  display: flex;
  overflow-x: auto;
  padding: 10px 0;

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }
`;

const SubNavLink = styled(Link)`
  padding: 6px 12px;
  margin-right: 8px;
  text-decoration: none;
  color: ${props => props.active ? 'white' : '#333'};
  background-color: ${props => props.active ? '#555' : '#f8f9fa'};
  border: 1px solid ${props => props.active ? '#555' : '#dee2e6'};
  border-radius: 4px;
  white-space: nowrap;
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${props => props.active ? '#666' : '#e9ecef'};
  }
`;

const Navigation = () => {
  const location = useLocation();
  const path = location.pathname;

  // Determine which main section is active
  const isStrategicFramework = path.includes('/strategic-framework');
  const isGodPatent = path.includes('/god-patent');
  const isUniversalComponents = path.includes('/universal-components');
  const isWhitePaper = path.includes('/white-paper');
  const isFinancial = path.includes('/financial');
  const isHealthcare = path.includes('/healthcare');
  const isEducation = path.includes('/education');
  const isGovernment = path.includes('/government');
  const isInfrastructure = path.includes('/infrastructure');
  const isAI = path.includes('/ai-governance');
  const isSupplyChain = path.includes('/supply-chain');
  const isInsurance = path.includes('/insurance');
  const isMobileIoT = path.includes('/mobile-iot');

  // Render sub-navigation based on active section
  const renderSubNav = () => {
    if (isGodPatent) {
      return (
        <SubNav>
          <SubNavLink to="/god-patent/architecture" active={path === '/god-patent/architecture'}>
            FIG. 1: Cyber-Safety Protocol Architecture
          </SubNavLink>
          <SubNavLink to="/god-patent/pillars" active={path === '/god-patent/pillars'}>
            FIG. 2: 12 Pillars of Cyber-Safety
          </SubNavLink>
          <SubNavLink to="/god-patent/novas" active={path === '/god-patent/novas'}>
            FIG. 3: 12+1 Universal Novas
          </SubNavLink>
          <SubNavLink to="/god-patent/continuances" active={path === '/god-patent/continuances'}>
            FIG. 4: 9 Industry Focus
          </SubNavLink>
          <SubNavLink to="/god-patent/alignment" active={path === '/god-patent/alignment'}>
            FIG. 5: 3-6-9-12-13 Alignment Architecture
          </SubNavLink>
          <SubNavLink to="/god-patent/novastore" active={path === '/god-patent/novastore'}>
            FIG. 6: NovaStore Partner Empowerment Model
          </SubNavLink>
          <SubNavLink to="/god-patent/unified-field-theory" active={path === '/god-patent/unified-field-theory'}>
            FIG. 7: Unified Field Theory Architecture
          </SubNavLink>
          <SubNavLink to="/god-patent/cross-domain-applications" active={path === '/god-patent/cross-domain-applications'}>
            FIG. 8: Cross-Domain Applications
          </SubNavLink>
          <SubNavLink to="/god-patent/novafuse-components" active={path === '/god-patent/novafuse-components'}>
            FIG. 9: NovaFuse 13 Universal Components
          </SubNavLink>
        </SubNav>
      );
    } else if (isStrategicFramework) {
      return (
        <SubNav>
          <SubNavLink to="/strategic-framework/trinity" active={path === '/strategic-framework/trinity'}>
            FIG. 1: Strategic Trinity
          </SubNavLink>
          <SubNavLink to="/strategic-framework/performance" active={path === '/strategic-framework/performance'}>
            FIG. 2: 3,142x Performance
          </SubNavLink>
          <SubNavLink to="/strategic-framework/partner-empowerment" active={path === '/strategic-framework/partner-empowerment'}>
            FIG. 3: Partner Empowerment Flywheel
          </SubNavLink>
          <SubNavLink to="/strategic-framework/patent-shield" active={path === '/strategic-framework/patent-shield'}>
            FIG. 4: Patent Shield & Strategic Moat
          </SubNavLink>
          <SubNavLink to="/strategic-framework/mathematics" active={path === '/strategic-framework/mathematics'}>
            FIG. 5: Mathematics Classification
          </SubNavLink>
          <SubNavLink to="/strategic-framework/performance-equation" active={path === '/strategic-framework/performance-equation'}>
            FIG. 6: Performance Equation
          </SubNavLink>
          <SubNavLink to="/strategic-framework/google-approach" active={path === '/strategic-framework/google-approach'}>
            FIG. 7: Google-Only Approach
          </SubNavLink>
          <SubNavLink to="/strategic-framework/enterprise-case" active={path === '/strategic-framework/enterprise-case'}>
            FIG. 8: Enterprise Case Study
          </SubNavLink>
          <SubNavLink to="/strategic-framework/patent-claims" active={path === '/strategic-framework/patent-claims'}>
            Patent Claims
          </SubNavLink>
          <SubNavLink to="/strategic-framework/consolidated" active={path === '/strategic-framework/consolidated'}>
            Complete Presentation
          </SubNavLink>
        </SubNav>
      );
    } else if (isUniversalComponents) {
      return (
        <SubNav>
          <SubNavLink to="/universal-components/nova-connect" active={path === '/universal-components/nova-connect'}>
            NovaConnect - Universal API Connector
          </SubNavLink>
          <SubNavLink to="/universal-components/nova-dna" active={path === '/universal-components/nova-dna'}>
            NovaDNA - Universal Identity Verification
          </SubNavLink>
          <SubNavLink to="/universal-components/nova-vision" active={path === '/universal-components/nova-vision'}>
            NovaVision - Universal UI Framework
          </SubNavLink>
        </SubNav>
      );
    } else if (isFinancial) {
      return (
        <SubNav>
          <SubNavLink to="/financial/architecture" active={path === '/financial/architecture'}>
            FIG. 1: System Architecture
          </SubNavLink>
          <SubNavLink to="/financial/audit-trail" active={path === '/financial/audit-trail'}>
            FIG. 2: Automated Audit Trail
          </SubNavLink>
          <SubNavLink to="/financial/explainable-ai" active={path === '/financial/explainable-ai'}>
            FIG. 3: Explainable AI
          </SubNavLink>
          <SubNavLink to="/financial/defi-compliance" active={path === '/financial/defi-compliance'}>
            FIG. 4: DeFi Compliance Layer
          </SubNavLink>
          <SubNavLink to="/financial/iot-security" active={path === '/financial/iot-security'}>
            FIG. 5: IoT Payment Security
          </SubNavLink>
          <SubNavLink to="/financial/kill-switch" active={path === '/financial/kill-switch'}>
            FIG. 6: Regulatory Kill Switch
          </SubNavLink>
          <SubNavLink to="/financial/risk-scoring" active={path === '/financial/risk-scoring'}>
            FIG. 7: Dynamic Risk Scoring
          </SubNavLink>
          <SubNavLink to="/financial/self-learning" active={path === '/financial/self-learning'}>
            FIG. 8: Self-Learning System
          </SubNavLink>
          <SubNavLink to="/financial/cross-border" active={path === '/financial/cross-border'}>
            FIG. 9: Cross-Border Compliance
          </SubNavLink>
          <SubNavLink to="/financial/fraud-bridge" active={path === '/financial/fraud-bridge'}>
            FIG. 10: Fraud-Compliance Bridge
          </SubNavLink>
        </SubNav>
      );
    } else if (isHealthcare) {
      return (
        <SubNav>
          <SubNavLink to="/healthcare/architecture" active={path === '/healthcare/architecture'}>
            FIG. 1: Healthcare System Architecture
          </SubNavLink>
          <SubNavLink to="/healthcare/phi-processing" active={path === '/healthcare/phi-processing'}>
            FIG. 2: Zero-Persistence PHI Processing
          </SubNavLink>
          <SubNavLink to="/healthcare/device-security" active={path === '/healthcare/device-security'}>
            FIG. 3: Medical Device Security Framework
          </SubNavLink>
          <SubNavLink to="/healthcare/hipaa" active={path === '/healthcare/hipaa'}>
            FIG. 4: HIPAA Enforcement Mechanism
          </SubNavLink>
        </SubNav>
      );
    } else if (isWhitePaper) {
      return (
        <SubNav>
          <SubNavLink to="/white-paper/alignment-architecture" active={path === '/white-paper/alignment-architecture'}>
            3–6–9–12–13 Alignment Architecture
          </SubNavLink>
        </SubNav>
      );
    }
    // Add more sub-navigation sections for other patents as needed

    return null;
  };

  return (
    <NavContainer>
      <NavInner>
        <MainNav>
          <NavLink to="/strategic-framework/trinity" active={isStrategicFramework}>
            Strategic Framework
          </NavLink>
          <NavLink to="/god-patent/architecture" active={isGodPatent}>
            God Patent
          </NavLink>
          <NavLink to="/universal-components/nova-connect" active={isUniversalComponents}>
            Universal Components
          </NavLink>
          <NavLink to="/white-paper/alignment-architecture" active={isWhitePaper}>
            White Paper
          </NavLink>
          <NavLink to="/financial/architecture" active={isFinancial}>
            Financial Services
          </NavLink>
          <NavLink to="/healthcare/architecture" active={isHealthcare}>
            Healthcare
          </NavLink>
          <NavLink to="/education/architecture" active={isEducation}>
            Education
          </NavLink>
          <NavLink to="/government/architecture" active={isGovernment}>
            Government & Defense
          </NavLink>
          <NavLink to="/infrastructure/architecture" active={isInfrastructure}>
            Critical Infrastructure
          </NavLink>
          <NavLink to="/ai-governance/architecture" active={isAI}>
            AI Governance
          </NavLink>
          <NavLink to="/supply-chain/architecture" active={isSupplyChain}>
            Supply Chain
          </NavLink>
          <NavLink to="/insurance/architecture" active={isInsurance}>
            Insurance
          </NavLink>
          <NavLink to="/mobile-iot/architecture" active={isMobileIoT}>
            Mobile/IoT
          </NavLink>
        </MainNav>

        {renderSubNav()}
      </NavInner>
    </NavContainer>
  );
};

export default Navigation;
