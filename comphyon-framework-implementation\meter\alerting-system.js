/**
 * Alerting System
 * 
 * This module implements the Alerting System component of the Meter.
 * It manages alerts, notifications, and escalation policies.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * AlertingSystem class
 */
class AlertingSystem extends EventEmitter {
  /**
   * Create a new AlertingSystem instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      enableMetrics: true,
      historySize: 100, // Number of historical data points to keep
      notificationChannels: ['console'], // Available channels: console, email, sms, webhook
      escalationLevels: {
        warning: {
          channels: ['console'],
          escalationDelay: 300000, // 5 minutes
          autoEscalate: false
        },
        critical: {
          channels: ['console'],
          escalationDelay: 180000, // 3 minutes
          autoEscalate: true
        },
        emergency: {
          channels: ['console'],
          escalationDelay: 60000, // 1 minute
          autoEscalate: true
        }
      },
      ...options
    };
    
    // Initialize state
    this.state = {
      activeAlerts: new Map(), // id -> alert
      alertHistory: [],
      notificationHistory: [],
      escalationHistory: [],
      isRunning: false,
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      alertsProcessed: 0,
      notificationsSent: {
        console: 0,
        email: 0,
        sms: 0,
        webhook: 0
      },
      escalationsTriggered: 0
    };
    
    // Initialize escalation timers
    this.escalationTimers = new Map(); // alertId -> timer
    
    if (this.options.enableLogging) {
      console.log('AlertingSystem initialized');
    }
  }
  
  /**
   * Start the alerting system
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('AlertingSystem is already running');
      }
      return false;
    }
    
    this.state.isRunning = true;
    
    if (this.options.enableLogging) {
      console.log('AlertingSystem started');
    }
    
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the alerting system
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('AlertingSystem is not running');
      }
      return false;
    }
    
    this.state.isRunning = false;
    
    // Clear all escalation timers
    for (const timerId of this.escalationTimers.values()) {
      clearTimeout(timerId);
    }
    this.escalationTimers.clear();
    
    if (this.options.enableLogging) {
      console.log('AlertingSystem stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Process alert
   * @param {Object} alert - Alert object
   * @returns {Object} - Processing result
   */
  processAlert(alert) {
    const startTime = performance.now();
    
    if (!this.state.isRunning) {
      throw new Error('AlertingSystem is not running');
    }
    
    if (!alert || typeof alert !== 'object') {
      throw new Error('Alert must be an object');
    }
    
    if (!alert.id || !alert.level || !alert.domain) {
      throw new Error('Alert must have id, level, and domain properties');
    }
    
    // Check if alert is already being processed
    const existingAlert = this.state.activeAlerts.get(alert.id);
    
    if (existingAlert) {
      // Update existing alert
      existingAlert.entropyValue = alert.entropyValue;
      existingAlert.updatedAt = Date.now();
      existingAlert.updateCount = (existingAlert.updateCount || 0) + 1;
      
      // Check if we need to send another notification
      if (existingAlert.updateCount % 5 === 0) { // Send notification every 5 updates
        this._sendNotifications(existingAlert);
      }
      
      // Update metrics
      this.metrics.processingTimeMs += performance.now() - startTime;
      this.metrics.alertsProcessed++;
      
      // Emit alert update event
      this.emit('alert-updated', existingAlert);
      
      if (this.options.enableLogging) {
        console.log(`AlertingSystem: Updated alert ${existingAlert.id} (${existingAlert.level})`);
      }
      
      return {
        alert: existingAlert,
        status: 'updated',
        timestamp: Date.now()
      };
    } else {
      // Process new alert
      const processedAlert = {
        ...alert,
        processedAt: Date.now(),
        notificationsSent: 0,
        escalationLevel: 0,
        status: 'active'
      };
      
      // Add to active alerts
      this.state.activeAlerts.set(alert.id, processedAlert);
      
      // Add to history
      this.state.alertHistory.push(processedAlert);
      
      // Limit history size
      if (this.state.alertHistory.length > this.options.historySize) {
        this.state.alertHistory.shift();
      }
      
      // Send notifications
      this._sendNotifications(processedAlert);
      
      // Set up escalation timer if needed
      this._setupEscalation(processedAlert);
      
      // Update metrics
      this.metrics.processingTimeMs += performance.now() - startTime;
      this.metrics.alertsProcessed++;
      
      // Emit alert processed event
      this.emit('alert-processed', processedAlert);
      
      if (this.options.enableLogging) {
        console.log(`AlertingSystem: Processed new alert ${processedAlert.id} (${processedAlert.level})`);
      }
      
      return {
        alert: processedAlert,
        status: 'new',
        timestamp: Date.now()
      };
    }
  }
  
  /**
   * Resolve alert
   * @param {string} alertId - Alert ID
   * @param {Object} metadata - Additional metadata
   * @returns {Object} - Resolution result
   */
  resolveAlert(alertId, metadata = {}) {
    const startTime = performance.now();
    
    if (!alertId) {
      throw new Error('Alert ID is required');
    }
    
    // Check if alert exists
    const alert = this.state.activeAlerts.get(alertId);
    
    if (!alert) {
      throw new Error(`Alert ${alertId} not found`);
    }
    
    // Update alert
    alert.status = 'resolved';
    alert.resolvedAt = Date.now();
    alert.resolutionMetadata = metadata;
    
    // Remove from active alerts
    this.state.activeAlerts.delete(alertId);
    
    // Clear escalation timer if exists
    if (this.escalationTimers.has(alertId)) {
      clearTimeout(this.escalationTimers.get(alertId));
      this.escalationTimers.delete(alertId);
    }
    
    // Send resolution notification
    this._sendResolutionNotification(alert);
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    // Emit alert resolved event
    this.emit('alert-resolved', alert);
    
    if (this.options.enableLogging) {
      console.log(`AlertingSystem: Resolved alert ${alertId}`);
    }
    
    return {
      alert,
      status: 'resolved',
      timestamp: Date.now()
    };
  }
  
  /**
   * Configure notification channel
   * @param {string} channel - Channel name
   * @param {Object} config - Channel configuration
   * @returns {Object} - Updated configuration
   */
  configureNotificationChannel(channel, config) {
    if (!channel || typeof channel !== 'string') {
      throw new Error('Channel name is required');
    }
    
    if (!config || typeof config !== 'object') {
      throw new Error('Channel configuration is required');
    }
    
    // Add channel to available channels if not already present
    if (!this.options.notificationChannels.includes(channel)) {
      this.options.notificationChannels.push(channel);
    }
    
    // Store channel configuration
    this.options[`${channel}Config`] = {
      ...this.options[`${channel}Config`] || {},
      ...config
    };
    
    // Emit configuration update event
    this.emit('channel-configured', {
      channel,
      config: { ...this.options[`${channel}Config`] },
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`AlertingSystem: Configured ${channel} notification channel`);
    }
    
    return { ...this.options[`${channel}Config`] };
  }
  
  /**
   * Configure escalation policy
   * @param {string} level - Alert level
   * @param {Object} policy - Escalation policy
   * @returns {Object} - Updated policy
   */
  configureEscalationPolicy(level, policy) {
    if (!level || !['warning', 'critical', 'emergency'].includes(level)) {
      throw new Error('Valid alert level is required (warning, critical, emergency)');
    }
    
    if (!policy || typeof policy !== 'object') {
      throw new Error('Escalation policy is required');
    }
    
    // Update escalation policy
    this.options.escalationLevels[level] = {
      ...this.options.escalationLevels[level],
      ...policy
    };
    
    // Emit policy update event
    this.emit('policy-configured', {
      level,
      policy: { ...this.options.escalationLevels[level] },
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`AlertingSystem: Configured escalation policy for ${level} alerts`);
    }
    
    return { ...this.options.escalationLevels[level] };
  }
  
  /**
   * Get active alerts
   * @param {string} level - Alert level (all levels if not specified)
   * @returns {Array} - Active alerts
   */
  getActiveAlerts(level) {
    const alerts = Array.from(this.state.activeAlerts.values());
    
    if (level && !['warning', 'critical', 'emergency'].includes(level)) {
      throw new Error('Invalid alert level');
    }
    
    if (level) {
      return alerts.filter(alert => alert.level === level);
    }
    
    return alerts;
  }
  
  /**
   * Get alert history
   * @param {number} limit - Maximum number of history items to return
   * @returns {Array} - Alert history
   */
  getAlertHistory(limit = 10) {
    return this.state.alertHistory.slice(0, limit);
  }
  
  /**
   * Get notification history
   * @param {number} limit - Maximum number of history items to return
   * @returns {Array} - Notification history
   */
  getNotificationHistory(limit = 10) {
    return this.state.notificationHistory.slice(0, limit);
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Send notifications for alert
   * @param {Object} alert - Alert object
   * @private
   */
  _sendNotifications(alert) {
    // Get channels for alert level
    const { channels } = this.options.escalationLevels[alert.level] || { channels: ['console'] };
    
    // Send notification to each channel
    for (const channel of channels) {
      if (this.options.notificationChannels.includes(channel)) {
        this._sendNotificationToChannel(channel, alert);
      }
    }
    
    // Update alert
    alert.notificationsSent = (alert.notificationsSent || 0) + 1;
    alert.lastNotificationAt = Date.now();
  }
  
  /**
   * Send notification to specific channel
   * @param {string} channel - Channel name
   * @param {Object} alert - Alert object
   * @private
   */
  _sendNotificationToChannel(channel, alert) {
    // Create notification
    const notification = {
      id: `notification-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      channel,
      alert: { ...alert },
      timestamp: Date.now(),
      status: 'sent'
    };
    
    // Add to history
    this.state.notificationHistory.push(notification);
    
    // Limit history size
    if (this.state.notificationHistory.length > this.options.historySize) {
      this.state.notificationHistory.shift();
    }
    
    // Send notification based on channel
    switch (channel) {
      case 'console':
        this._sendConsoleNotification(notification);
        break;
        
      case 'email':
        this._sendEmailNotification(notification);
        break;
        
      case 'sms':
        this._sendSmsNotification(notification);
        break;
        
      case 'webhook':
        this._sendWebhookNotification(notification);
        break;
        
      default:
        if (this.options.enableLogging) {
          console.log(`AlertingSystem: Unknown notification channel ${channel}`);
        }
    }
    
    // Update metrics
    this.metrics.notificationsSent[channel] = (this.metrics.notificationsSent[channel] || 0) + 1;
    
    // Emit notification sent event
    this.emit('notification-sent', notification);
  }
  
  /**
   * Send console notification
   * @param {Object} notification - Notification object
   * @private
   */
  _sendConsoleNotification(notification) {
    const { alert } = notification;
    
    console.log('='.repeat(80));
    console.log(`ALERT [${alert.level.toUpperCase()}]: ${alert.domain} Domain`);
    console.log('-'.repeat(80));
    console.log(`Alert ID: ${alert.id}`);
    console.log(`Entropy Value: ${alert.entropyValue.toFixed(4)}`);
    console.log(`Threshold: ${alert.threshold.toFixed(4)}`);
    console.log(`Deviation: ${alert.deviation.toFixed(4)}`);
    console.log(`Time: ${new Date(notification.timestamp).toISOString()}`);
    
    if (alert.metadata) {
      console.log('-'.repeat(40));
      console.log('Metadata:');
      for (const [key, value] of Object.entries(alert.metadata)) {
        console.log(`  ${key}: ${value}`);
      }
    }
    
    console.log('='.repeat(80));
  }
  
  /**
   * Send email notification
   * @param {Object} notification - Notification object
   * @private
   */
  _sendEmailNotification(notification) {
    // In a real implementation, this would send an email
    if (this.options.enableLogging) {
      console.log(`AlertingSystem: Would send email notification for alert ${notification.alert.id}`);
    }
  }
  
  /**
   * Send SMS notification
   * @param {Object} notification - Notification object
   * @private
   */
  _sendSmsNotification(notification) {
    // In a real implementation, this would send an SMS
    if (this.options.enableLogging) {
      console.log(`AlertingSystem: Would send SMS notification for alert ${notification.alert.id}`);
    }
  }
  
  /**
   * Send webhook notification
   * @param {Object} notification - Notification object
   * @private
   */
  _sendWebhookNotification(notification) {
    // In a real implementation, this would call a webhook
    if (this.options.enableLogging) {
      console.log(`AlertingSystem: Would call webhook for alert ${notification.alert.id}`);
    }
  }
  
  /**
   * Send resolution notification
   * @param {Object} alert - Alert object
   * @private
   */
  _sendResolutionNotification(alert) {
    // Get channels for alert level
    const { channels } = this.options.escalationLevels[alert.level] || { channels: ['console'] };
    
    // Create notification
    const notification = {
      id: `notification-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      type: 'resolution',
      alert: { ...alert },
      timestamp: Date.now(),
      status: 'sent'
    };
    
    // Add to history
    this.state.notificationHistory.push(notification);
    
    // Send to console
    if (channels.includes('console')) {
      console.log('='.repeat(80));
      console.log(`ALERT RESOLVED [${alert.level.toUpperCase()}]: ${alert.domain} Domain`);
      console.log('-'.repeat(80));
      console.log(`Alert ID: ${alert.id}`);
      console.log(`Resolution Time: ${new Date(alert.resolvedAt).toISOString()}`);
      console.log(`Duration: ${Math.round((alert.resolvedAt - alert.processedAt) / 1000)} seconds`);
      console.log('='.repeat(80));
    }
    
    // Emit notification sent event
    this.emit('resolution-notification-sent', notification);
  }
  
  /**
   * Set up escalation timer
   * @param {Object} alert - Alert object
   * @private
   */
  _setupEscalation(alert) {
    const { level } = alert;
    const { autoEscalate, escalationDelay } = this.options.escalationLevels[level] || { autoEscalate: false, escalationDelay: 300000 };
    
    // If auto-escalation is enabled, set up timer
    if (autoEscalate) {
      const timerId = setTimeout(() => {
        this._escalateAlert(alert);
      }, escalationDelay);
      
      // Store timer ID
      this.escalationTimers.set(alert.id, timerId);
      
      if (this.options.enableLogging) {
        console.log(`AlertingSystem: Set up escalation timer for alert ${alert.id} (${Math.round(escalationDelay / 1000)} seconds)`);
      }
    }
  }
  
  /**
   * Escalate alert
   * @param {Object} alert - Alert object
   * @private
   */
  _escalateAlert(alert) {
    // Remove timer
    this.escalationTimers.delete(alert.id);
    
    // Update alert
    alert.escalationLevel = (alert.escalationLevel || 0) + 1;
    alert.lastEscalationAt = Date.now();
    
    // Add to escalation history
    this.state.escalationHistory.push({
      alertId: alert.id,
      level: alert.level,
      escalationLevel: alert.escalationLevel,
      timestamp: Date.now()
    });
    
    // Limit history size
    if (this.state.escalationHistory.length > this.options.historySize) {
      this.state.escalationHistory.shift();
    }
    
    // Update metrics
    this.metrics.escalationsTriggered++;
    
    // Send escalation notification
    this._sendEscalationNotification(alert);
    
    // Emit escalation event
    this.emit('alert-escalated', {
      alert,
      escalationLevel: alert.escalationLevel,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`AlertingSystem: Escalated alert ${alert.id} to level ${alert.escalationLevel}`);
    }
  }
  
  /**
   * Send escalation notification
   * @param {Object} alert - Alert object
   * @private
   */
  _sendEscalationNotification(alert) {
    // In a real implementation, this would send notifications to higher-priority channels
    console.log('='.repeat(80));
    console.log(`ALERT ESCALATED [${alert.level.toUpperCase()}]: ${alert.domain} Domain`);
    console.log('-'.repeat(80));
    console.log(`Alert ID: ${alert.id}`);
    console.log(`Escalation Level: ${alert.escalationLevel}`);
    console.log(`Time: ${new Date(alert.lastEscalationAt).toISOString()}`);
    console.log(`Duration: ${Math.round((alert.lastEscalationAt - alert.processedAt) / 1000)} seconds`);
    console.log('='.repeat(80));
  }
}

module.exports = AlertingSystem;
