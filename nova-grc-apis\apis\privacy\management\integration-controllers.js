/**
 * Integration Controllers
 * 
 * This module provides controllers for the integration endpoints.
 */

const integrations = require('./integrations');

/**
 * Get all available integrations
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getIntegrations = (req, res) => {
  try {
    const availableIntegrations = integrations.getAvailableIntegrations();
    
    res.json({ data: availableIntegrations });
  } catch (error) {
    console.error('Error in getIntegrations:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get an integration by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getIntegrationById = (req, res) => {
  try {
    const { id } = req.params;
    
    const integration = integrations.getIntegrationById(id);
    
    if (!integration) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Integration with ID ${id} not found`
      });
    }
    
    res.json({ data: integration });
  } catch (error) {
    console.error('Error in getIntegrationById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Execute an integration action
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const executeIntegrationAction = async (req, res) => {
  try {
    const { id, action } = req.params;
    const data = req.body;
    
    const integration = integrations.getIntegrationById(id);
    
    if (!integration) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Integration with ID ${id} not found`
      });
    }
    
    if (!integration.capabilities.includes(action)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Action '${action}' not supported for integration '${id}'`
      });
    }
    
    const result = await integrations.executeIntegrationAction(id, action, data);
    
    res.json({
      data: result.data,
      message: result.message
    });
  } catch (error) {
    console.error('Error in executeIntegrationAction:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

module.exports = {
  getIntegrations,
  getIntegrationById,
  executeIntegrationAction
};
