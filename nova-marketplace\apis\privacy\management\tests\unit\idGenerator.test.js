/**
 * ID Generator Tests
 *
 * This file contains unit tests for the ID generator utility.
 */

const idGenerator = require('../../utils/idGenerator');

describe('ID Generator', () => {
  describe('generateRandomId', () => {
    it('should generate a random ID with the default length', () => {
      const id = idGenerator.generateRandomId();
      expect(id).toBeDefined();
      expect(typeof id).toBe('string');
      expect(id.length).toBe(16);
    });

    it('should generate a random ID with a custom length', () => {
      const id = idGenerator.generateRandomId(8);
      expect(id).toBeDefined();
      expect(typeof id).toBe('string');
      expect(id.length).toBe(8);
    });

    it('should generate different IDs on each call', () => {
      const id1 = idGenerator.generateRandomId();
      const id2 = idGenerator.generateRandomId();
      expect(id1).not.toBe(id2);
    });
  });

  describe('generateDsrId', () => {
    it('should generate a DSR ID with the correct format', () => {
      const id = idGenerator.generateDsrId();
      expect(id).toBeDefined();
      expect(typeof id).toBe('string');
      expect(id).toMatch(/^DSR-[A-Z0-9]+-[A-Z0-9]+$/);
    });

    it('should generate different IDs on each call', () => {
      const id1 = idGenerator.generateDsrId();
      const id2 = idGenerator.generateDsrId();
      expect(id1).not.toBe(id2);
    });
  });

  describe('generateConsentId', () => {
    it('should generate a consent ID with the correct format', () => {
      const id = idGenerator.generateConsentId();
      expect(id).toBeDefined();
      expect(typeof id).toBe('string');
      expect(id).toMatch(/^CONSENT-[A-Z0-9]+-[A-Z0-9]+$/);
    });

    it('should generate different IDs on each call', () => {
      const id1 = idGenerator.generateConsentId();
      const id2 = idGenerator.generateConsentId();
      expect(id1).not.toBe(id2);
    });
  });

  describe('generateDataBreachId', () => {
    it('should generate a data breach ID with the correct format', () => {
      const id = idGenerator.generateDataBreachId();
      expect(id).toBeDefined();
      expect(typeof id).toBe('string');
      expect(id).toMatch(/^BREACH-[A-Z0-9]+-[A-Z0-9]+$/);
    });

    it('should generate different IDs on each call', () => {
      const id1 = idGenerator.generateDataBreachId();
      const id2 = idGenerator.generateDataBreachId();
      expect(id1).not.toBe(id2);
    });
  });

  describe('generatePrivacyNoticeId', () => {
    it('should generate a privacy notice ID with the correct format', () => {
      const id = idGenerator.generatePrivacyNoticeId();
      expect(id).toBeDefined();
      expect(typeof id).toBe('string');
      expect(id).toMatch(/^NOTICE-[A-Z0-9]+-[A-Z0-9]+$/);
    });

    it('should generate different IDs on each call', () => {
      const id1 = idGenerator.generatePrivacyNoticeId();
      const id2 = idGenerator.generatePrivacyNoticeId();
      expect(id1).not.toBe(id2);
    });
  });

  describe('generateNotificationId', () => {
    it('should generate a notification ID with the correct format', () => {
      const id = idGenerator.generateNotificationId();
      expect(id).toBeDefined();
      expect(typeof id).toBe('string');
      expect(id).toMatch(/^NOTIF-[A-Z0-9]+-[A-Z0-9]+$/);
    });

    it('should generate different IDs on each call', () => {
      const id1 = idGenerator.generateNotificationId();
      const id2 = idGenerator.generateNotificationId();
      expect(id1).not.toBe(id2);
    });
  });

  describe('generateProcessingActivityId', () => {
    it('should generate a processing activity ID with the correct format', () => {
      const id = idGenerator.generateProcessingActivityId();
      expect(id).toBeDefined();
      expect(typeof id).toBe('string');
      expect(id).toMatch(/^PROC-[A-Z0-9]+-[A-Z0-9]+$/);
    });

    it('should generate different IDs on each call', () => {
      const id1 = idGenerator.generateProcessingActivityId();
      const id2 = idGenerator.generateProcessingActivityId();
      expect(id1).not.toBe(id2);
    });
  });

  describe('generateIntegrationId', () => {
    it('should generate an integration ID with the correct format', () => {
      const id = idGenerator.generateIntegrationId('Test Integration');
      expect(id).toBeDefined();
      expect(typeof id).toBe('string');
      expect(id).toMatch(/^test-integration-[a-z0-9]+$/);
    });

    it('should normalize the integration name', () => {
      const id = idGenerator.generateIntegrationId('Test Integration!@#$%^&*()');
      expect(id).toMatch(/^test-integration-[a-z0-9]+$/);
    });

    it('should generate different IDs for the same name on each call', () => {
      // Mock Date.now() to return different values on each call
      const originalDateNow = Date.now;
      let callCount = 0;
      Date.now = jest.fn(() => {
        callCount++;
        return 1000000 + callCount; // Return a different timestamp each time
      });

      try {
        const id1 = idGenerator.generateIntegrationId('Test Integration');
        const id2 = idGenerator.generateIntegrationId('Test Integration');
        expect(id1).not.toBe(id2);
      } finally {
        // Restore the original Date.now
        Date.now = originalDateNow;
      }
    });
  });

  describe('generateUuid', () => {
    it('should generate a UUID', () => {
      const id = idGenerator.generateUuid();
      expect(id).toBeDefined();
      expect(typeof id).toBe('string');
      expect(id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
    });

    it('should generate different UUIDs on each call', () => {
      const id1 = idGenerator.generateUuid();
      const id2 = idGenerator.generateUuid();
      expect(id1).not.toBe(id2);
    });
  });
});
