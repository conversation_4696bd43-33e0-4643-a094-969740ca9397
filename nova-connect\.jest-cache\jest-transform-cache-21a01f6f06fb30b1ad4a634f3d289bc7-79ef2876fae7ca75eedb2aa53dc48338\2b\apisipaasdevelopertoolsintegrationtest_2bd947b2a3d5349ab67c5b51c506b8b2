045ad149113d99574533975adf6d919e
// Mock logger
_getJestObj().mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Integration tests for the APIs, iPaaS & Developer Tools Connector
 */

const nock = require('nock');
const ApisIpaasDeveloperToolsConnector = require('../../../../connector/implementations/apis-ipaas-developer-tools');
describe('ApisIpaasDeveloperToolsConnector Integration', () => {
  let connector;
  const baseUrl = 'https://api.test.com';
  beforeAll(() => {
    // Disable real HTTP requests
    nock.disableNetConnect();
  });
  afterAll(() => {
    // Enable real HTTP requests
    nock.enableNetConnect();
  });
  beforeEach(() => {
    // Reset nock
    nock.cleanAll();

    // Create connector instance
    connector = new ApisIpaasDeveloperToolsConnector({
      baseUrl
    }, {
      apiKey: 'test-api-key',
      apiKeyHeader: 'X-API-Key'
    });
  });
  describe('API Management', () => {
    it('should list APIs', async () => {
      // Mock APIs endpoint
      const mockApis = {
        data: [{
          id: 'api-1',
          name: 'Customer API',
          type: 'rest',
          status: 'active',
          baseUrl: 'https://api.example.com/customers'
        }, {
          id: 'api-2',
          name: 'Product API',
          type: 'rest',
          status: 'active',
          baseUrl: 'https://api.example.com/products'
        }],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      nock(baseUrl).get('/apis').query({
        status: 'active',
        type: 'rest'
      }).reply(200, mockApis);

      // Initialize connector
      await connector.initialize();

      // List APIs
      const result = await connector.listApis({
        status: 'active',
        type: 'rest'
      });

      // Verify result
      expect(result).toEqual(mockApis);
    });
    it('should get a specific API', async () => {
      // Mock API endpoint
      const mockApi = {
        id: 'api-123',
        name: 'Customer API',
        description: 'API for managing customer data',
        version: '1.0.0',
        type: 'rest',
        status: 'active',
        baseUrl: 'https://api.example.com/customers',
        endpoints: [{
          id: 'endpoint-1',
          path: '/customers',
          method: 'GET',
          description: 'List customers'
        }]
      };
      nock(baseUrl).get('/apis/api-123').reply(200, mockApi);

      // Initialize connector
      await connector.initialize();

      // Get API
      const result = await connector.getApi('api-123');

      // Verify result
      expect(result).toEqual(mockApi);
    });
    it('should create a new API', async () => {
      // API data
      const apiData = {
        name: 'New API',
        description: 'New API description',
        type: 'rest',
        baseUrl: 'https://api.example.com/new'
      };

      // Mock response
      const mockResponse = {
        id: 'api-new',
        ...apiData,
        status: 'draft',
        createdAt: '2023-06-15T10:30:00Z',
        updatedAt: '2023-06-15T10:30:00Z'
      };
      nock(baseUrl).post('/apis', apiData).reply(201, mockResponse);

      // Initialize connector
      await connector.initialize();

      // Create API
      const result = await connector.createApi(apiData);

      // Verify result
      expect(result).toEqual(mockResponse);
    });
    it('should update an existing API', async () => {
      // API update data
      const apiId = 'api-123';
      const updateData = {
        name: 'Updated API',
        description: 'Updated description',
        status: 'active'
      };

      // Mock response
      const mockResponse = {
        id: apiId,
        name: 'Updated API',
        description: 'Updated description',
        status: 'active',
        type: 'rest',
        baseUrl: 'https://api.example.com/customers',
        updatedAt: '2023-06-15T11:45:00Z'
      };
      nock(baseUrl).put(`/apis/${apiId}`, updateData).reply(200, mockResponse);

      // Initialize connector
      await connector.initialize();

      // Update API
      const result = await connector.updateApi(apiId, updateData);

      // Verify result
      expect(result).toEqual(mockResponse);
    });
    it('should delete an API', async () => {
      // API ID
      const apiId = 'api-123';
      nock(baseUrl).delete(`/apis/${apiId}`).reply(204);

      // Initialize connector
      await connector.initialize();

      // Delete API
      await connector.deleteApi(apiId);

      // If no error is thrown, the test passes
      expect(true).toBe(true);
    });
  });
  describe('Integration Management', () => {
    it('should list integrations', async () => {
      // Mock integrations endpoint
      const mockIntegrations = {
        data: [{
          id: 'integration-1',
          name: 'Customer Data Sync',
          status: 'active',
          sourceSystem: 'CRM System',
          targetSystem: 'ERP System'
        }, {
          id: 'integration-2',
          name: 'Order Processing',
          status: 'active',
          sourceSystem: 'E-commerce Platform',
          targetSystem: 'Fulfillment System'
        }],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      nock(baseUrl).get('/integrations').query({
        status: 'active'
      }).reply(200, mockIntegrations);

      // Initialize connector
      await connector.initialize();

      // List integrations
      const result = await connector.listIntegrations({
        status: 'active'
      });

      // Verify result
      expect(result).toEqual(mockIntegrations);
    });
    it('should get a specific integration', async () => {
      // Mock integration endpoint
      const mockIntegration = {
        id: 'integration-123',
        name: 'Customer Data Sync',
        description: 'Synchronize customer data between CRM and ERP',
        status: 'active',
        type: 'scheduled',
        sourceSystem: {
          id: 'system-1',
          name: 'CRM System'
        },
        targetSystem: {
          id: 'system-2',
          name: 'ERP System'
        }
      };
      nock(baseUrl).get('/integrations/integration-123').reply(200, mockIntegration);

      // Initialize connector
      await connector.initialize();

      // Get integration
      const result = await connector.getIntegration('integration-123');

      // Verify result
      expect(result).toEqual(mockIntegration);
    });
    it('should execute an integration', async () => {
      // Integration ID
      const integrationId = 'integration-123';

      // Execution options
      const options = {
        parameters: {
          startDate: '2023-06-01',
          endDate: '2023-06-02'
        },
        async: true
      };

      // Mock response
      const mockResponse = {
        executionId: 'exec-123',
        status: 'queued',
        startTime: '2023-06-02T10:15:00Z',
        estimatedCompletionTime: '2023-06-02T10:20:00Z',
        statusUrl: `https://api.test.com/integrations/${integrationId}/executions/exec-123`
      };
      nock(baseUrl).post(`/integrations/${integrationId}/execute`, options).reply(202, mockResponse);

      // Initialize connector
      await connector.initialize();

      // Execute integration
      const result = await connector.executeIntegration(integrationId, options);

      // Verify result
      expect(result).toEqual(mockResponse);
    });
  });
  describe('Developer Tools', () => {
    it('should list developer tools', async () => {
      // Mock developer tools endpoint
      const mockTools = {
        data: [{
          id: 'tool-1',
          name: 'API Tester',
          category: 'testing',
          version: '2.1.0'
        }, {
          id: 'tool-2',
          name: 'Schema Validator',
          category: 'testing',
          version: '1.5.0'
        }],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      nock(baseUrl).get('/developer-tools').query({
        category: 'testing'
      }).reply(200, mockTools);

      // Initialize connector
      await connector.initialize();

      // List developer tools
      const result = await connector.listDeveloperTools({
        category: 'testing'
      });

      // Verify result
      expect(result).toEqual(mockTools);
    });
    it('should get a specific developer tool', async () => {
      // Mock developer tool endpoint
      const mockTool = {
        id: 'tool-123',
        name: 'API Tester',
        description: 'Tool for testing APIs',
        category: 'testing',
        version: '2.1.0',
        url: 'https://tools.example.com/api-tester'
      };
      nock(baseUrl).get('/developer-tools/tool-123').reply(200, mockTool);

      // Initialize connector
      await connector.initialize();

      // Get developer tool
      const result = await connector.getDeveloperTool('tool-123');

      // Verify result
      expect(result).toEqual(mockTool);
    });
  });
  describe('Error Handling', () => {
    it('should handle authentication errors', async () => {
      // Mock authentication error
      nock(baseUrl).get('/apis').reply(401, {
        error: 'unauthorized',
        error_description: 'Invalid API key'
      });

      // Initialize connector
      await connector.initialize();

      // Try to list APIs
      await expect(connector.listApis()).rejects.toThrow('Error listing APIs');
    });
    it('should handle not found errors', async () => {
      // Mock not found error
      nock(baseUrl).get('/apis/non-existent').reply(404, {
        error: 'not_found',
        error_description: 'API not found'
      });

      // Initialize connector
      await connector.initialize();

      // Try to get non-existent API
      await expect(connector.getApi('non-existent')).rejects.toThrow('Error getting API');
    });
    it('should handle validation errors', async () => {
      // API data with missing required fields
      const invalidData = {
        name: 'Invalid API'
        // Missing required fields: type, baseUrl
      };

      // Mock validation error
      nock(baseUrl).post('/apis', invalidData).reply(400, {
        error: 'validation_error',
        error_description: 'Validation failed',
        errors: [{
          field: 'type',
          message: 'Type is required'
        }, {
          field: 'baseUrl',
          message: 'Base URL is required'
        }]
      });

      // Initialize connector
      await connector.initialize();

      // Try to create invalid API
      await expect(connector.createApi(invalidData)).rejects.toThrow('type is required');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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