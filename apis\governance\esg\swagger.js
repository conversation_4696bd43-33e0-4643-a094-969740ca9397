/**
 * @swagger
 * tags:
 *   name: ESG
 *   description: ESG Reporting API
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     ESGReport:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the ESG report
 *         title:
 *           type: string
 *           description: Title of the ESG report
 *         description:
 *           type: string
 *           description: Description of the ESG report
 *         framework:
 *           type: string
 *           description: ESG framework used for the report
 *         reportingYear:
 *           type: integer
 *           description: Year the report covers
 *         status:
 *           type: string
 *           enum: [draft, in-progress, completed, published]
 *           description: Status of the ESG report
 *         metrics:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               category:
 *                 type: string
 *                 enum: [environmental, social, governance]
 *               name:
 *                 type: string
 *               value:
 *                 type: number
 *               unit:
 *                 type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the report was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the report was last updated
 *       required:
 *         - id
 *         - title
 *         - framework
 *         - reportingYear
 *         - status
 *         - createdAt
 *         - updatedAt
 *     
 *     ESGReportInput:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Title of the ESG report
 *         description:
 *           type: string
 *           description: Description of the ESG report
 *         framework:
 *           type: string
 *           description: ESG framework used for the report
 *         reportingYear:
 *           type: integer
 *           description: Year the report covers
 *         status:
 *           type: string
 *           enum: [draft, in-progress, completed, published]
 *           description: Status of the ESG report
 *         metrics:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               category:
 *                 type: string
 *                 enum: [environmental, social, governance]
 *               name:
 *                 type: string
 *               value:
 *                 type: number
 *               unit:
 *                 type: string
 *       required:
 *         - title
 *         - framework
 *         - reportingYear
 *     
 *     ESGMetric:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the metric
 *         name:
 *           type: string
 *           description: Name of the metric
 *         description:
 *           type: string
 *           description: Description of the metric
 *         category:
 *           type: string
 *           enum: [environmental, social, governance]
 *           description: Category of the metric
 *         unit:
 *           type: string
 *           description: Unit of measurement
 *         value:
 *           type: number
 *           description: Value of the metric
 *         year:
 *           type: integer
 *           description: Year the metric applies to
 *         trend:
 *           type: string
 *           enum: [increasing, decreasing, stable]
 *           description: Trend of the metric over time
 *       required:
 *         - id
 *         - name
 *         - category
 *         - unit
 *         - value
 *         - year
 *     
 *     ESGFramework:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the framework
 *         name:
 *           type: string
 *           description: Name of the framework
 *         description:
 *           type: string
 *           description: Description of the framework
 *         organization:
 *           type: string
 *           description: Organization that created the framework
 *         website:
 *           type: string
 *           description: Website of the framework
 *         categories:
 *           type: array
 *           items:
 *             type: string
 *           description: Categories covered by the framework
 *       required:
 *         - id
 *         - name
 *         - description
 *         - organization
 */
