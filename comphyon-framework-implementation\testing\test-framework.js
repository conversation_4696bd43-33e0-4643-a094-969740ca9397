/**
 * Comphyon Test Framework
 * 
 * This module implements a comprehensive testing framework for the Comphyon system.
 * It provides utilities for unit testing, integration testing, and end-to-end testing.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');
const assert = require('assert');

// Mathematical constants
const PI_10_CUBED = 3142; // π × 10³

/**
 * TestCase class
 */
class TestCase {
  /**
   * Create a new TestCase instance
   * @param {string} name - Test case name
   * @param {Function} testFunction - Test function
   * @param {Object} options - Test options
   */
  constructor(name, testFunction, options = {}) {
    this.name = name;
    this.testFunction = testFunction;
    this.options = {
      timeout: 5000, // ms
      skip: false,
      only: false,
      tags: [],
      ...options
    };
    this.result = {
      status: 'pending', // pending, passed, failed, skipped
      error: null,
      duration: 0,
      timestamp: null
    };
  }
  
  /**
   * Run the test case
   * @returns {Promise<Object>} - Test result
   */
  async run() {
    if (this.options.skip) {
      this.result.status = 'skipped';
      this.result.timestamp = Date.now();
      return this.result;
    }
    
    const startTime = performance.now();
    this.result.timestamp = Date.now();
    
    try {
      // Create timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Test case "${this.name}" timed out after ${this.options.timeout}ms`));
        }, this.options.timeout);
      });
      
      // Run test function with timeout
      await Promise.race([
        this.testFunction(),
        timeoutPromise
      ]);
      
      this.result.status = 'passed';
    } catch (error) {
      this.result.status = 'failed';
      this.result.error = error;
    }
    
    this.result.duration = performance.now() - startTime;
    
    return this.result;
  }
}

/**
 * TestSuite class
 */
class TestSuite extends EventEmitter {
  /**
   * Create a new TestSuite instance
   * @param {string} name - Test suite name
   * @param {Object} options - Test suite options
   */
  constructor(name, options = {}) {
    super();
    
    this.name = name;
    this.options = {
      enableLogging: true,
      parallelTests: false,
      ...options
    };
    this.testCases = [];
    this.beforeEachHooks = [];
    this.afterEachHooks = [];
    this.beforeAllHooks = [];
    this.afterAllHooks = [];
    this.result = {
      status: 'pending', // pending, running, completed
      passed: 0,
      failed: 0,
      skipped: 0,
      total: 0,
      duration: 0,
      timestamp: null
    };
  }
  
  /**
   * Add a test case
   * @param {string} name - Test case name
   * @param {Function} testFunction - Test function
   * @param {Object} options - Test options
   * @returns {TestCase} - Test case
   */
  test(name, testFunction, options = {}) {
    const testCase = new TestCase(name, testFunction, options);
    this.testCases.push(testCase);
    return testCase;
  }
  
  /**
   * Add a before each hook
   * @param {Function} hookFunction - Hook function
   */
  beforeEach(hookFunction) {
    this.beforeEachHooks.push(hookFunction);
  }
  
  /**
   * Add an after each hook
   * @param {Function} hookFunction - Hook function
   */
  afterEach(hookFunction) {
    this.afterEachHooks.push(hookFunction);
  }
  
  /**
   * Add a before all hook
   * @param {Function} hookFunction - Hook function
   */
  beforeAll(hookFunction) {
    this.beforeAllHooks.push(hookFunction);
  }
  
  /**
   * Add an after all hook
   * @param {Function} hookFunction - Hook function
   */
  afterAll(hookFunction) {
    this.afterAllHooks.push(hookFunction);
  }
  
  /**
   * Run the test suite
   * @returns {Promise<Object>} - Test suite result
   */
  async run() {
    const startTime = performance.now();
    this.result.timestamp = Date.now();
    this.result.status = 'running';
    this.result.total = this.testCases.length;
    
    try {
      // Run before all hooks
      for (const hook of this.beforeAllHooks) {
        await hook();
      }
      
      // Filter test cases
      const onlyTests = this.testCases.filter(test => test.options.only);
      const testsToRun = onlyTests.length > 0 ? onlyTests : this.testCases;
      
      // Run test cases
      if (this.options.parallelTests) {
        // Run tests in parallel
        const testPromises = testsToRun.map(async (testCase) => {
          return this._runTestCase(testCase);
        });
        
        await Promise.all(testPromises);
      } else {
        // Run tests sequentially
        for (const testCase of testsToRun) {
          await this._runTestCase(testCase);
        }
      }
      
      // Run after all hooks
      for (const hook of this.afterAllHooks) {
        await hook();
      }
      
      this.result.status = 'completed';
    } catch (error) {
      this.result.status = 'completed';
      this.emit('error', error);
    }
    
    this.result.duration = performance.now() - startTime;
    
    this.emit('complete', this.result);
    
    return this.result;
  }
  
  /**
   * Run a test case
   * @param {TestCase} testCase - Test case
   * @private
   */
  async _runTestCase(testCase) {
    try {
      // Run before each hooks
      for (const hook of this.beforeEachHooks) {
        await hook();
      }
      
      // Run test case
      const result = await testCase.run();
      
      // Update result
      if (result.status === 'passed') {
        this.result.passed++;
      } else if (result.status === 'failed') {
        this.result.failed++;
      } else if (result.status === 'skipped') {
        this.result.skipped++;
      }
      
      // Emit event
      this.emit('test-complete', {
        name: testCase.name,
        status: result.status,
        duration: result.duration,
        error: result.error
      });
      
      // Log result
      if (this.options.enableLogging) {
        if (result.status === 'passed') {
          console.log(`✅ PASS: ${testCase.name} (${result.duration.toFixed(2)}ms)`);
        } else if (result.status === 'failed') {
          console.log(`❌ FAIL: ${testCase.name} (${result.duration.toFixed(2)}ms)`);
          console.error(result.error);
        } else if (result.status === 'skipped') {
          console.log(`⏭️ SKIP: ${testCase.name}`);
        }
      }
      
      // Run after each hooks
      for (const hook of this.afterEachHooks) {
        await hook();
      }
    } catch (error) {
      this.emit('error', error);
    }
  }
}

/**
 * TestRunner class
 */
class TestRunner extends EventEmitter {
  /**
   * Create a new TestRunner instance
   * @param {Object} options - Test runner options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      parallelSuites: false,
      ...options
    };
    this.testSuites = [];
    this.result = {
      status: 'pending', // pending, running, completed
      passed: 0,
      failed: 0,
      skipped: 0,
      total: 0,
      duration: 0,
      timestamp: null
    };
  }
  
  /**
   * Add a test suite
   * @param {TestSuite} testSuite - Test suite
   */
  addSuite(testSuite) {
    this.testSuites.push(testSuite);
  }
  
  /**
   * Create a new test suite
   * @param {string} name - Test suite name
   * @param {Object} options - Test suite options
   * @returns {TestSuite} - Test suite
   */
  createSuite(name, options = {}) {
    const testSuite = new TestSuite(name, {
      enableLogging: this.options.enableLogging,
      ...options
    });
    this.testSuites.push(testSuite);
    return testSuite;
  }
  
  /**
   * Run all test suites
   * @returns {Promise<Object>} - Test runner result
   */
  async run() {
    const startTime = performance.now();
    this.result.timestamp = Date.now();
    this.result.status = 'running';
    
    try {
      // Run test suites
      if (this.options.parallelSuites) {
        // Run suites in parallel
        const suitePromises = this.testSuites.map(async (suite) => {
          return this._runTestSuite(suite);
        });
        
        await Promise.all(suitePromises);
      } else {
        // Run suites sequentially
        for (const suite of this.testSuites) {
          await this._runTestSuite(suite);
        }
      }
      
      this.result.status = 'completed';
    } catch (error) {
      this.result.status = 'completed';
      this.emit('error', error);
    }
    
    this.result.duration = performance.now() - startTime;
    
    // Log summary
    if (this.options.enableLogging) {
      console.log('\n=== Test Summary ===');
      console.log(`Total: ${this.result.total}`);
      console.log(`Passed: ${this.result.passed}`);
      console.log(`Failed: ${this.result.failed}`);
      console.log(`Skipped: ${this.result.skipped}`);
      console.log(`Duration: ${this.result.duration.toFixed(2)}ms`);
      
      if (this.result.failed === 0) {
        console.log('\n✅ All tests passed!');
      } else {
        console.log(`\n❌ ${this.result.failed} tests failed.`);
      }
    }
    
    this.emit('complete', this.result);
    
    return this.result;
  }
  
  /**
   * Run a test suite
   * @param {TestSuite} testSuite - Test suite
   * @private
   */
  async _runTestSuite(testSuite) {
    try {
      // Log suite start
      if (this.options.enableLogging) {
        console.log(`\n=== Running Test Suite: ${testSuite.name} ===`);
      }
      
      // Run suite
      const result = await testSuite.run();
      
      // Update result
      this.result.passed += result.passed;
      this.result.failed += result.failed;
      this.result.skipped += result.skipped;
      this.result.total += result.total;
      
      // Emit event
      this.emit('suite-complete', {
        name: testSuite.name,
        passed: result.passed,
        failed: result.failed,
        skipped: result.skipped,
        total: result.total,
        duration: result.duration
      });
      
      // Log suite result
      if (this.options.enableLogging) {
        console.log(`\n=== Test Suite Complete: ${testSuite.name} ===`);
        console.log(`Passed: ${result.passed}/${result.total}`);
        console.log(`Failed: ${result.failed}`);
        console.log(`Skipped: ${result.skipped}`);
        console.log(`Duration: ${result.duration.toFixed(2)}ms`);
      }
    } catch (error) {
      this.emit('error', error);
    }
  }
}

/**
 * Assertion utilities
 */
const assertions = {
  /**
   * Assert that a value is truthy
   * @param {*} value - Value to check
   * @param {string} message - Error message
   */
  isTrue(value, message = 'Expected value to be truthy') {
    assert(value, message);
  },
  
  /**
   * Assert that a value is falsy
   * @param {*} value - Value to check
   * @param {string} message - Error message
   */
  isFalse(value, message = 'Expected value to be falsy') {
    assert(!value, message);
  },
  
  /**
   * Assert that two values are equal
   * @param {*} actual - Actual value
   * @param {*} expected - Expected value
   * @param {string} message - Error message
   */
  equal(actual, expected, message = `Expected ${actual} to equal ${expected}`) {
    assert.strictEqual(actual, expected, message);
  },
  
  /**
   * Assert that two values are not equal
   * @param {*} actual - Actual value
   * @param {*} expected - Expected value
   * @param {string} message - Error message
   */
  notEqual(actual, expected, message = `Expected ${actual} to not equal ${expected}`) {
    assert.notStrictEqual(actual, expected, message);
  },
  
  /**
   * Assert that two values are deeply equal
   * @param {*} actual - Actual value
   * @param {*} expected - Expected value
   * @param {string} message - Error message
   */
  deepEqual(actual, expected, message = 'Expected values to be deeply equal') {
    assert.deepStrictEqual(actual, expected, message);
  },
  
  /**
   * Assert that two values are not deeply equal
   * @param {*} actual - Actual value
   * @param {*} expected - Expected value
   * @param {string} message - Error message
   */
  notDeepEqual(actual, expected, message = 'Expected values to not be deeply equal') {
    assert.notDeepStrictEqual(actual, expected, message);
  },
  
  /**
   * Assert that a function throws an error
   * @param {Function} fn - Function to check
   * @param {RegExp|Function|Object|Error} error - Expected error
   * @param {string} message - Error message
   */
  throws(fn, error, message = 'Expected function to throw an error') {
    assert.throws(fn, error, message);
  },
  
  /**
   * Assert that a function does not throw an error
   * @param {Function} fn - Function to check
   * @param {string} message - Error message
   */
  doesNotThrow(fn, message = 'Expected function to not throw an error') {
    assert.doesNotThrow(fn, message);
  },
  
  /**
   * Assert that a value is within a range
   * @param {number} value - Value to check
   * @param {number} min - Minimum value
   * @param {number} max - Maximum value
   * @param {string} message - Error message
   */
  inRange(value, min, max, message = `Expected ${value} to be between ${min} and ${max}`) {
    assert(value >= min && value <= max, message);
  },
  
  /**
   * Assert that a value is approximately equal to another value
   * @param {number} actual - Actual value
   * @param {number} expected - Expected value
   * @param {number} delta - Allowed delta
   * @param {string} message - Error message
   */
  approximately(actual, expected, delta, message = `Expected ${actual} to be approximately ${expected} (±${delta})`) {
    assert(Math.abs(actual - expected) <= delta, message);
  }
};

module.exports = {
  TestCase,
  TestSuite,
  TestRunner,
  assertions
};
