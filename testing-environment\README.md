<<<<<<< HEAD
# NovaConnect Testing Environment

This directory contains a complete testing environment for the NovaConnect Universal API Connector. It includes Docker containers for all the necessary services, as well as unit tests, integration tests, end-to-end tests, performance tests, and security tests.

## Testing Standards

All tests must achieve at least a **96% pass rate** in all areas to be considered successful. This includes:

- 96% code coverage for branches
- 96% code coverage for functions
- 96% code coverage for lines
- 96% code coverage for statements

## Services

The testing environment includes the following services:

- **Connector Registry**: Stores connector templates and configurations
- **Authentication Service**: Manages credentials for API connections
- **Connector Executor**: Executes API calls using connector templates and credentials
- **Usage Metering**: Tracks API usage for billing and analytics
- **Mock APIs**: Simulates third-party APIs for testing

## Directory Structure

```
testing-environment/
├── __tests__/                  # Test files
│   ├── unit/                   # Unit tests
│   ├── integration/            # Integration tests
│   ├── e2e/                    # End-to-end tests
│   ├── performance/            # Performance tests
│   └── security/               # Security tests
├── connector-registry/         # Connector Registry service
├── auth-service/               # Authentication Service
├── connector-executor/         # Connector Executor service
├── usage-metering/             # Usage Metering service
├── mock-apis/                  # Mock API services
├── ui/                         # Frontend UI
├── test-mock-api.js            # Mock API service
├── test-connector-registry.js  # Connector registry service
├── test-auth-service.js        # Authentication service
├── test-connector-executor.js  # Connector executor service
├── test-usage-metering.js      # Usage metering service
├── jest.config.js              # Jest configuration
├── jest.setup.js               # Jest setup
├── custom-reporter.js          # Custom test reporter
├── coverage-analyzer.js        # Coverage analysis tool
├── generate-coverage-report.js # Coverage report generator
├── docker-compose.yml          # Docker Compose configuration
├── run-tests.ps1               # Script to run tests
└── package.json                # Package configuration
```

## Prerequisites

- Docker and Docker Compose
- Node.js and npm
- PowerShell (for Windows)

## Running the Tests

### Running All Tests

```bash
# Run all tests
npm test
```

### Running Specific Test Types

```bash
# Run unit tests
npm run test:unit

# Run integration tests
npm run test:integration

# Run end-to-end tests
npm run test:e2e

# Run performance tests
npm run test:performance

# Run security tests
npm run test:security
```

### Running Tests with Coverage

```bash
# Run tests with coverage report
npm run test:coverage

# Analyze coverage to identify areas for improvement
npm run analyze:coverage

# Generate detailed HTML coverage report
npm run generate:report

# Run full test suite with coverage analysis and report generation
npm run test:full
```

### Using Docker Environment

You can also use the Docker environment for testing:

```powershell
# Run the test script
.\run-tests.ps1
```

This will:

1. Start the Docker environment with all services
2. Run the integration tests
3. Run the load tests
4. Run the end-to-end tests
5. Stop the Docker environment

## Manual Testing

You can start individual services for manual testing:

```bash
# Start mock API service
npm run start:mock-api

# Start connector registry service
npm run start:registry

# Start authentication service
npm run start:auth

# Start connector executor service
npm run start:executor

# Start usage metering service
npm run start:usage
```

Or start all services at once:

```bash
# Start all services
npm run start:all
```

You can also use Docker for manual testing:

```powershell
# Start the Docker environment
docker-compose up -d

# Stop the Docker environment
docker-compose down
```

## Service Endpoints

- Mock API: http://localhost:3005
- Connector Registry: http://localhost:3006
- Authentication Service: http://localhost:3007
- Connector Executor: http://localhost:3008
- Usage Metering: http://localhost:3009

## Test Reports

After running tests, reports are generated in the following locations:

- Coverage report: `coverage/lcov-report/index.html`
- Coverage analysis: Generated by `npm run analyze:coverage`
- Detailed HTML report: `coverage-report/index.html`
- JUnit report: `test-results/junit.xml`

## Customizing the Tests

You can customize the tests by modifying the files in the `__tests__` directory:

- **Unit Tests**: `__tests__/unit/*.test.js`
- **Integration Tests**: `__tests__/integration/*.test.js`
- **End-to-End Tests**: `__tests__/e2e/*.test.js`
- **Performance Tests**: `__tests__/performance/*.test.js`
- **Security Tests**: `__tests__/security/*.test.js`

## Adding New Mock APIs

To add a new mock API, modify the `test-mock-api.js` file and add new endpoints as needed.

## Troubleshooting

If you encounter issues running the tests:

1. Make sure all dependencies are installed: `npm install`
2. Check that no other services are running on the required ports
3. Try running the services individually to identify any issues
4. Check the logs for error messages

If using Docker, check the logs for each service:

```powershell
docker-compose logs connector-registry
docker-compose logs auth-service
docker-compose logs connector-executor
docker-compose logs usage-metering
docker-compose logs mock-apis
docker-compose logs frontend
```

## Contributing

When adding new tests, please follow these guidelines:

1. Place tests in the appropriate directory based on their type
2. Maintain the 96% coverage requirement
3. Use descriptive test names that clearly indicate what is being tested
4. Clean up any resources created during tests
5. Use the test helpers in `__tests__/helpers/test-utils.js` to simplify test creation
6. Follow the test templates in `__tests__/templates/` for consistent test structure

## Test Helpers

The testing framework includes several helpers to make writing tests easier:

- **Test Utilities**: `__tests__/helpers/test-utils.js` provides utilities for creating mocks, generating test data, etc.
- **Test Templates**: `__tests__/templates/unit-test-template.js` provides a template for creating new unit tests
- **Coverage Analyzer**: `coverage-analyzer.js` helps identify areas that need more test coverage
- **Report Generator**: `generate-coverage-report.js` creates detailed HTML coverage reports
=======
# novaconnect-universal-api-connector
NovaConnect Universal API Connector: A robust, enterprise-grade solution for seamless API integration with advanced security, transformation capabilities, and comprehensive testing. Features include strong encryption, flexible authentication methods, data mapping, and extensive test coverage with security, performance, and chaos testing frameworks
>>>>>>> 36c877fb8164441aea8b02dc053b48d6da5373a8
