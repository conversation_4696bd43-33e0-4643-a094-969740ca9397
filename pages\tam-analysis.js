import React from 'react';
import Sidebar from '../components/Sidebar';

export default function TAMAnalysis() {
  const sidebarItems = [
    { type: 'category', label: 'TAM Analysis', items: [
      { label: 'Overview', href: '#overview' },
      { label: 'Market Segments', href: '#segments' },
      { label: 'Revenue Projection', href: '#revenue' },
      { label: 'Key Takeaways', href: '#takeaways' }
    ]},
    { type: 'category', label: 'Related Pages', items: [
      { label: 'Strategic Independence Plan', href: '/strategic-independence-plan' },
      { label: 'Technology Roadmap', href: '/technology-roadmap' }
    ]}
  ];

  return (
    <div className="flex flex-col md:flex-row gap-8">
      {/* Sidebar - Hidden on mobile, shown on desktop */}
      <div className="hidden md:block md:w-1/4 lg:w-1/5">
        <div className="sticky top-4">
          <Sidebar items={sidebarItems} title="TAM Analysis" />
        </div>
      </div>

      {/* Mobile Sidebar Toggle - Shown on mobile only */}
      <div className="md:hidden mb-4">
        <select 
          className="w-full bg-secondary text-white border border-gray-700 rounded p-2"
          onChange={(e) => {
            if (e.target.value) window.location.href = e.target.value;
          }}
          defaultValue=""
        >
          <option value="" disabled>Navigate to...</option>
          {sidebarItems.map((item, index) => (
            item.type === 'category' ? (
              <optgroup key={index} label={item.label}>
                {item.items.map((subItem, subIndex) => (
                  <option key={`${index}-${subIndex}`} value={subItem.href}>
                    {subItem.label}
                  </option>
                ))}
              </optgroup>
            ) : (
              <option key={index} value={item.href}>
                {item.label}
              </option>
            )
          ))}
        </select>
      </div>

      {/* Main Content */}
      <div className="w-full md:w-3/4 lg:w-4/5">
        <div id="overview" className="mb-12">
          <h2 className="text-3xl font-bold mb-8 text-center">Our Market Opportunity</h2>
          
          <div className="bg-secondary p-6 rounded-lg mb-8">
            <h3 className="text-2xl font-bold mb-4 text-center">The NovaFuse Wedge Strategy</h3>
            
            <p className="text-gray-300 mb-6 text-center text-lg">
              We're not going after the entire GRC platform space. We're targeting the $2.3B+ integration and interoperability layer that sits between GRC systems, data sources, and automation platforms—something legacy players ignore or bolt on.
            </p>
            
            <div className="relative h-20 bg-gray-800 rounded-lg mb-8 overflow-hidden">
              <div className="absolute top-0 left-0 h-full bg-blue-600 bg-opacity-30 border-r-2 border-dashed border-blue-600" style={{width: '100%'}}></div>
              <span className="absolute top-1/2 transform -translate-y-1/2 left-4 font-bold">Total GRC Market: $15B</span>
            </div>
            
            <div className="relative h-20 bg-gray-800 rounded-lg mb-8 overflow-hidden">
              <div className="absolute top-0 left-0 h-full bg-blue-600 bg-opacity-30 border-r-2 border-dashed border-blue-600" style={{width: '40%'}}></div>
              <span className="absolute top-1/2 transform -translate-y-1/2 left-4 font-bold">GRC Software: $6B</span>
            </div>
            
            <div className="relative h-20 bg-gray-800 rounded-lg mb-8 overflow-hidden">
              <div className="absolute top-0 left-0 h-full bg-blue-600 bg-opacity-30 border-r-2 border-dashed border-blue-600" style={{width: '15.3%'}}></div>
              <span className="absolute top-1/2 transform -translate-y-1/2 left-4 font-bold">Integration Layer: $2.3B</span>
              <span className="absolute top-1/2 transform -translate-y-1/2 right-4 font-bold text-blue-400">NovaFuse Target</span>
            </div>
            
            <div className="relative h-20 bg-gray-800 rounded-lg overflow-hidden">
              <div className="absolute top-0 left-0 h-full bg-blue-600 bg-opacity-30 border-r-2 border-dashed border-blue-600" style={{width: '6.5%'}}></div>
              <span className="absolute top-1/2 transform -translate-y-1/2 left-4 font-bold">NovaFuse Opportunity: $1B ARR</span>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div id="segments" className="bg-secondary p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-4">Market Segments</h3>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-blue-400">Enterprise Tech & SaaS</h4>
                  <p className="text-gray-300">Companies with complex compliance requirements across multiple regulations and jurisdictions.</p>
                  <div className="mt-2 flex justify-between text-sm">
                    <span>Market Size: $850M</span>
                    <span>Target Share: 25%</span>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-blue-400">Healthcare & FinTech</h4>
                  <p className="text-gray-300">Highly regulated industries with strict compliance requirements and complex data flows.</p>
                  <div className="mt-2 flex justify-between text-sm">
                    <span>Market Size: $750M</span>
                    <span>Target Share: 20%</span>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-blue-400">Public Sector</h4>
                  <p className="text-gray-300">Government agencies and contractors with specialized compliance needs.</p>
                  <div className="mt-2 flex justify-between text-sm">
                    <span>Market Size: $450M</span>
                    <span>Target Share: 15%</span>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-blue-400">AI Startups</h4>
                  <p className="text-gray-300">Emerging companies with growing compliance requirements as they scale.</p>
                  <div className="mt-2 flex justify-between text-sm">
                    <span>Market Size: $250M</span>
                    <span>Target Share: 40%</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div id="revenue" className="bg-secondary p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-4">Revenue Projection</h3>
              
              <div className="space-y-6">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="font-semibold">Year 1</span>
                    <span>$10M ARR</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2.5">
                    <div className="bg-blue-600 h-2.5 rounded-full" style={{width: '1%'}}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="font-semibold">Year 3</span>
                    <span>$100M ARR</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2.5">
                    <div className="bg-blue-600 h-2.5 rounded-full" style={{width: '10%'}}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="font-semibold">Year 5</span>
                    <span>$500M ARR</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2.5">
                    <div className="bg-blue-600 h-2.5 rounded-full" style={{width: '50%'}}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="font-semibold">Year 7</span>
                    <span>$1B ARR</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2.5">
                    <div className="bg-blue-600 h-2.5 rounded-full" style={{width: '100%'}}></div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 pt-6 border-t border-gray-700">
                <h4 className="font-semibold mb-2">Key Growth Drivers</h4>
                <ul className="list-disc list-inside text-gray-300 space-y-1">
                  <li>Partner ecosystem expansion (100+ partners by Year 3)</li>
                  <li>API call volume growth (50% YoY)</li>
                  <li>Enterprise customer adoption (200+ by Year 3)</li>
                  <li>International expansion (EMEA, APAC in Year 2)</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        {/* Bottom Section: Key Takeaways */}
        <div id="takeaways" className="bg-secondary p-6 rounded-lg mt-8">
          <h3 className="text-2xl font-bold mb-4 text-center">Why This Market Strategy Works</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-4 border border-blue-600 rounded-lg">
              <h4 className="text-xl font-semibold mb-2 text-blue-400">Underserved Need</h4>
              <p className="text-gray-300">The integration layer between GRC systems is a critical pain point that legacy vendors have failed to address effectively.</p>
            </div>
            
            <div className="p-4 border border-blue-600 rounded-lg">
              <h4 className="text-xl font-semibold mb-2 text-blue-400">Network Effects</h4>
              <p className="text-gray-300">Each new partner and integration makes the NovaFuse platform more valuable, creating a flywheel effect that accelerates growth.</p>
            </div>
            
            <div className="p-4 border border-blue-600 rounded-lg">
              <h4 className="text-xl font-semibold mb-2 text-blue-400">Revenue Transformation</h4>
              <p className="text-gray-300">We're turning integrations from a cost center into a revenue center—for us and our partners—creating aligned incentives for growth.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
