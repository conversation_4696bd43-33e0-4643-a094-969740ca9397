/**
 * UserPreferencesService
 * 
 * This service provides methods for managing user preferences.
 */

class UserPreferencesService {
  constructor() {
    this.storageKey = 'novafuse-user-preferences';
    this.defaultPreferences = {
      visualizations: {
        defaultType: '3d_tensor_visualization',
        renderMode: 'high',
        showAxes: true,
        showGrid: true,
        rotationSpeed: 1,
        colorScheme: 'default',
        autoRotate: true,
        dimensions: 3,
        harmonicThreshold: 0.7,
        resonancePatterns: true
      },
      performance: {
        levelOfDetail: 'auto',
        frustumCulling: true,
        instancedMeshes: true,
        mergeGeometries: true,
        simplifyGeometries: true,
        maxTriangles: 100000,
        showPerformanceOverlay: false
      },
      ui: {
        theme: 'dark',
        controlsPosition: 'right',
        showTooltips: true,
        autoSave: true,
        confirmBeforeExport: true
      },
      presets: {}
    };
    
    // Load preferences from storage
    this.preferences = this.loadPreferences();
  }
  
  /**
   * Load preferences from storage
   * @returns {Object} - User preferences
   * @private
   */
  loadPreferences() {
    try {
      const storedPreferences = localStorage.getItem(this.storageKey);
      
      if (storedPreferences) {
        return JSON.parse(storedPreferences);
      }
    } catch (error) {
      console.error('Error loading preferences:', error);
    }
    
    return { ...this.defaultPreferences };
  }
  
  /**
   * Save preferences to storage
   * @private
   */
  savePreferences() {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.preferences));
    } catch (error) {
      console.error('Error saving preferences:', error);
    }
  }
  
  /**
   * Get all preferences
   * @returns {Object} - User preferences
   */
  getPreferences() {
    return { ...this.preferences };
  }
  
  /**
   * Get visualization preferences
   * @returns {Object} - Visualization preferences
   */
  getVisualizationPreferences() {
    return { ...this.preferences.visualizations };
  }
  
  /**
   * Get performance preferences
   * @returns {Object} - Performance preferences
   */
  getPerformancePreferences() {
    return { ...this.preferences.performance };
  }
  
  /**
   * Get UI preferences
   * @returns {Object} - UI preferences
   */
  getUiPreferences() {
    return { ...this.preferences.ui };
  }
  
  /**
   * Get preference value
   * @param {string} key - Preference key (e.g., 'visualizations.renderMode')
   * @returns {*} - Preference value
   */
  getPreference(key) {
    const keys = key.split('.');
    let value = this.preferences;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return undefined;
      }
    }
    
    return value;
  }
  
  /**
   * Set preference value
   * @param {string} key - Preference key (e.g., 'visualizations.renderMode')
   * @param {*} value - Preference value
   */
  setPreference(key, value) {
    const keys = key.split('.');
    let target = this.preferences;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      
      if (!(k in target) || typeof target[k] !== 'object') {
        target[k] = {};
      }
      
      target = target[k];
    }
    
    target[keys[keys.length - 1]] = value;
    
    // Save preferences
    this.savePreferences();
  }
  
  /**
   * Update preferences
   * @param {Object} preferences - Preferences to update
   */
  updatePreferences(preferences) {
    this.preferences = this.mergeObjects(this.preferences, preferences);
    
    // Save preferences
    this.savePreferences();
  }
  
  /**
   * Reset preferences to defaults
   */
  resetPreferences() {
    this.preferences = { ...this.defaultPreferences };
    
    // Save preferences
    this.savePreferences();
  }
  
  /**
   * Get visualization preset
   * @param {string} name - Preset name
   * @returns {Object|null} - Preset or null if not found
   */
  getVisualizationPreset(name) {
    return this.preferences.presets[name] || null;
  }
  
  /**
   * Get all visualization presets
   * @returns {Object} - Visualization presets
   */
  getVisualizationPresets() {
    return { ...this.preferences.presets };
  }
  
  /**
   * Save visualization preset
   * @param {string} name - Preset name
   * @param {Object} preset - Preset data
   */
  saveVisualizationPreset(name, preset) {
    this.preferences.presets[name] = { ...preset };
    
    // Save preferences
    this.savePreferences();
  }
  
  /**
   * Delete visualization preset
   * @param {string} name - Preset name
   */
  deleteVisualizationPreset(name) {
    delete this.preferences.presets[name];
    
    // Save preferences
    this.savePreferences();
  }
  
  /**
   * Export preferences to file
   * @returns {string} - JSON string
   */
  exportPreferences() {
    return JSON.stringify(this.preferences, null, 2);
  }
  
  /**
   * Import preferences from file
   * @param {string} json - JSON string
   * @returns {boolean} - Success flag
   */
  importPreferences(json) {
    try {
      const preferences = JSON.parse(json);
      
      // Validate preferences
      if (!preferences || typeof preferences !== 'object') {
        throw new Error('Invalid preferences format');
      }
      
      // Update preferences
      this.updatePreferences(preferences);
      
      return true;
    } catch (error) {
      console.error('Error importing preferences:', error);
      return false;
    }
  }
  
  /**
   * Merge objects recursively
   * @param {Object} target - Target object
   * @param {Object} source - Source object
   * @returns {Object} - Merged object
   * @private
   */
  mergeObjects(target, source) {
    const result = { ...target };
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          // Recursive merge for objects
          result[key] = this.mergeObjects(result[key] || {}, source[key]);
        } else {
          // Direct assignment for primitives and arrays
          result[key] = source[key];
        }
      }
    }
    
    return result;
  }
}

// Create singleton instance
const userPreferencesService = new UserPreferencesService();

export default userPreferencesService;
