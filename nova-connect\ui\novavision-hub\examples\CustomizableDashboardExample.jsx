/**
 * Customizable Dashboard Example
 * 
 * This example demonstrates how to use the user preferences features to create a customizable dashboard.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  CustomizableDashboard,
  PreferencesManager,
  SkipLink,
  ThemeSelector
} from '../components';
import { ThemeProvider } from '../theme';
import { PreferencesProvider } from '../preferences';

/**
 * Customizable Dashboard Example Content component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Customizable Dashboard Example Content component
 */
const CustomizableDashboardExampleContent = ({
  novaConnect,
  novaShield,
  novaTrack,
  enableLogging = false
}) => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [showPreferences, setShowPreferences] = useState(false);
  
  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        if (enableLogging) {
          console.log('Fetching dashboard data...');
        }
        
        // Simulate API call to fetch dashboard data
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Sample dashboard data
        const data = {
          // Metrics data
          metrics: [
            {
              label: 'Compliance Score',
              value: 87,
              suffix: '%',
              trend: 5,
              trendDirection: 'up',
              color: 'text-primary',
              description: 'Compliance score across all frameworks'
            },
            {
              label: 'Security Score',
              value: 92,
              suffix: '%',
              trend: 2,
              trendDirection: 'up',
              color: 'text-success',
              description: 'Security score across all systems'
            },
            {
              label: 'Identity Score',
              value: 78,
              suffix: '%',
              trend: -3,
              trendDirection: 'down',
              color: 'text-secondary',
              description: 'Identity management score'
            },
            {
              label: 'Risk Score',
              value: 65,
              suffix: '%',
              trend: 1,
              trendDirection: 'up',
              color: 'text-error',
              description: 'Overall risk score'
            }
          ],
          
          // Charts data
          charts: {
            'compliance-chart': {
              labels: ['GDPR', 'HIPAA', 'PCI DSS', 'SOC 2', 'ISO 27001'],
              datasets: [
                {
                  label: 'Compliance Score',
                  data: [92, 85, 78, 90, 88],
                  backgroundColor: 'rgba(54, 162, 235, 0.5)',
                  borderColor: 'rgba(54, 162, 235, 1)',
                  borderWidth: 1
                }
              ]
            },
            'security-chart': {
              labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
              datasets: [
                {
                  label: 'Threats Detected',
                  data: [12, 19, 8, 15, 10, 7],
                  backgroundColor: 'rgba(255, 99, 132, 0.5)',
                  borderColor: 'rgba(255, 99, 132, 1)',
                  borderWidth: 1
                },
                {
                  label: 'Threats Remediated',
                  data: [10, 15, 7, 12, 9, 5],
                  backgroundColor: 'rgba(75, 192, 192, 0.5)',
                  borderColor: 'rgba(75, 192, 192, 1)',
                  borderWidth: 1
                }
              ]
            }
          },
          
          // Alerts data
          alerts: [
            { id: 'alert-001', severity: 'critical', type: 'compliance', message: 'GDPR compliance violation detected', timestamp: '2023-06-15T10:30:00Z', status: 'open' },
            { id: 'alert-002', severity: 'high', type: 'security', message: 'Unusual authentication pattern detected', timestamp: '2023-06-15T09:45:00Z', status: 'investigating' },
            { id: 'alert-003', severity: 'medium', type: 'identity', message: 'User access review pending', timestamp: '2023-06-14T16:20:00Z', status: 'open' },
            { id: 'alert-004', severity: 'low', type: 'compliance', message: 'Policy update required', timestamp: '2023-06-14T14:10:00Z', status: 'resolved' }
          ],
          
          // Graph data
          graph: {
            nodes: [
              { id: 'n1', label: 'AWS S3', category: 'storage', riskScore: 25 },
              { id: 'n2', label: 'Azure AD', category: 'identity', riskScore: 15 },
              { id: 'n3', label: 'Salesforce', category: 'crm', riskScore: 40 },
              { id: 'n4', label: 'Google Workspace', category: 'productivity', riskScore: 20 },
              { id: 'n5', label: 'Customer Data', category: 'data', riskScore: 75 },
              { id: 'n6', label: 'Employee Data', category: 'data', riskScore: 60 },
              { id: 'n7', label: 'Payment Processing', category: 'finance', riskScore: 80 }
            ],
            edges: [
              { source: 'n1', target: 'n5', weight: 3 },
              { source: 'n1', target: 'n6', weight: 2 },
              { source: 'n2', target: 'n4', weight: 1 },
              { source: 'n2', target: 'n6', weight: 3 },
              { source: 'n3', target: 'n5', weight: 4 },
              { source: 'n4', target: 'n6', weight: 2 },
              { source: 'n5', target: 'n7', weight: 5 },
              { source: 'n6', target: 'n7', weight: 3 }
            ]
          },
          
          // Heatmap data
          heatmap: {
            x: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            y: ['AWS S3', 'Azure AD', 'Salesforce', 'Google Workspace', 'Payment Processing'],
            values: [
              [25, 30, 15, 20, 35, 40],
              [15, 20, 25, 30, 20, 15],
              [40, 35, 30, 25, 20, 15],
              [20, 25, 30, 35, 30, 25],
              [80, 75, 70, 65, 60, 55]
            ]
          },
          
          // Treemap data
          treemap: {
            name: 'Risk Map',
            children: [
              {
                name: 'Infrastructure',
                children: [
                  { name: 'AWS S3', value: 25 },
                  { name: 'Azure AD', value: 15 },
                  { name: 'Google Workspace', value: 20 }
                ]
              },
              {
                name: 'Applications',
                children: [
                  { name: 'Salesforce', value: 40 },
                  { name: 'Office 365', value: 30 },
                  { name: 'Slack', value: 15 }
                ]
              },
              {
                name: 'Data',
                children: [
                  { name: 'Customer Data', value: 75 },
                  { name: 'Employee Data', value: 60 },
                  { name: 'Financial Data', value: 85 }
                ]
              }
            ]
          },
          
          // Sankey data
          sankey: {
            nodes: [
              { id: 'a', name: 'AWS S3' },
              { id: 'b', name: 'Azure AD' },
              { id: 'c', name: 'Salesforce' },
              { id: 'd', name: 'Customer Data' },
              { id: 'e', name: 'Employee Data' },
              { id: 'f', name: 'Payment Processing' }
            ],
            links: [
              { source: 'a', target: 'd', value: 30 },
              { source: 'a', target: 'e', value: 20 },
              { source: 'b', target: 'e', value: 30 },
              { source: 'c', target: 'd', value: 40 },
              { source: 'd', target: 'f', value: 50 },
              { source: 'e', target: 'f', value: 30 }
            ]
          }
        };
        
        setDashboardData(data);
        setLoading(false);
        
        if (enableLogging) {
          console.log('Dashboard data fetched successfully');
        }
      } catch (error) {
        console.error('Error fetching dashboard data', error);
        setLoading(false);
      }
    };
    
    fetchDashboardData();
  }, [enableLogging]);
  
  // Refresh dashboard data
  const handleRefresh = async () => {
    setLoading(true);
    
    try {
      if (enableLogging) {
        console.log('Refreshing dashboard data...');
      }
      
      // Simulate API call to refresh dashboard data
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Update dashboard data with new values
      setDashboardData(prevData => {
        if (!prevData) return null;
        
        // Update metrics
        const updatedMetrics = prevData.metrics.map(metric => ({
          ...metric,
          value: Math.floor(Math.random() * 20) + 80,
          trend: Math.floor(Math.random() * 10) - 5,
          trendDirection: Math.random() > 0.5 ? 'up' : 'down'
        }));
        
        return {
          ...prevData,
          metrics: updatedMetrics
        };
      });
      
      setLoading(false);
      
      if (enableLogging) {
        console.log('Dashboard data refreshed successfully');
      }
    } catch (error) {
      console.error('Error refreshing dashboard data', error);
      setLoading(false);
    }
  };
  
  // Toggle preferences
  const togglePreferences = () => {
    setShowPreferences(!showPreferences);
  };
  
  return (
    <div className="space-y-6">
      {/* Skip link for keyboard navigation */}
      <SkipLink targetId="main-content" />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-textPrimary" id="dashboard-title">
          Customizable Dashboard
        </h1>
        
        <div className="flex items-center space-x-2">
          <ThemeSelector variant="dropdown" />
          
          <button
            className="px-4 py-2 bg-surface text-textPrimary border border-divider rounded-md hover:bg-actionHover transition-colors duration-200"
            onClick={togglePreferences}
          >
            User Preferences
          </button>
        </div>
      </div>
      
      {/* Main content */}
      <main id="main-content" tabIndex="-1">
        <div className="space-y-6">
          {/* Default dashboard */}
          <CustomizableDashboard
            id="default"
            data={dashboardData}
            onRefresh={handleRefresh}
            loading={loading}
          />
          
          {/* Risk dashboard */}
          <CustomizableDashboard
            id="risk"
            data={dashboardData}
            onRefresh={handleRefresh}
            loading={loading}
          />
        </div>
      </main>
      
      {/* User preferences */}
      {showPreferences && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="w-full max-w-4xl max-h-[90vh] overflow-auto">
            <PreferencesManager onClose={togglePreferences} />
          </div>
        </div>
      )}
    </div>
  );
};

CustomizableDashboardExampleContent.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  enableLogging: PropTypes.bool
};

/**
 * Customizable Dashboard Example component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} [props.initialTheme] - Initial theme
 * @param {boolean} [props.enableSystemPreference=true] - Whether to enable system preference detection
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Customizable Dashboard Example component
 */
const CustomizableDashboardExample = ({
  novaConnect,
  novaShield,
  novaTrack,
  initialTheme,
  enableSystemPreference = true,
  enableLogging = false
}) => {
  return (
    <ThemeProvider theme={initialTheme} enableSystemPreference={enableSystemPreference}>
      <PreferencesProvider>
        <CustomizableDashboardExampleContent
          novaConnect={novaConnect}
          novaShield={novaShield}
          novaTrack={novaTrack}
          enableLogging={enableLogging}
        />
      </PreferencesProvider>
    </ThemeProvider>
  );
};

CustomizableDashboardExample.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  initialTheme: PropTypes.object,
  enableSystemPreference: PropTypes.bool,
  enableLogging: PropTypes.bool
};

export default CustomizableDashboardExample;
