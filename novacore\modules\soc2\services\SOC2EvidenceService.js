/**
 * NovaCore SOC 2 Evidence Service
 * 
 * This service provides functionality for managing SOC 2 evidence.
 */

const { SOC2Evidence, SOC2Control } = require('../models');
const { BlockchainService } = require('../../../api/services');
const logger = require('../../../config/logger');
const { ValidationError, NotFoundError } = require('../../../api/utils/errors');
const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');

class SOC2EvidenceService {
  /**
   * Create a new SOC 2 evidence record
   * @param {Object} data - Evidence data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created evidence
   */
  async createEvidence(data, userId) {
    try {
      logger.info('Creating new SOC 2 evidence record', { controlId: data.control.id });
      
      // Validate control
      const control = await SOC2Control.findOne({ id: data.control.id });
      
      if (!control) {
        throw new ValidationError(`SOC 2 control with ID ${data.control.id} not found`);
      }
      
      // Generate hash for content if not provided
      if (data.content && !data.content.hash) {
        data.content.hash = this._generateContentHash(data.content.data);
      }
      
      // Set created by
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Create evidence
      const evidence = new SOC2Evidence(data);
      await evidence.save();
      
      logger.info('SOC 2 evidence record created successfully', { id: evidence._id });
      
      return evidence;
    } catch (error) {
      logger.error('Error creating SOC 2 evidence record', { error });
      throw error;
    }
  }
  
  /**
   * Get all SOC 2 evidence records for an organization
   * @param {string} organizationId - Organization ID
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Evidence records with pagination info
   */
  async getAllEvidence(organizationId, filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
      
      // Build query
      const query = { organizationId };
      
      // Apply filters
      if (filter.controlId) {
        query['control.id'] = filter.controlId;
      }
      
      if (filter.status) {
        query.status = filter.status;
      }
      
      if (filter.tags) {
        query['metadata.tags'] = { $all: Array.isArray(filter.tags) ? filter.tags : [filter.tags] };
      }
      
      if (filter.verificationStatus) {
        query['verification.status'] = filter.verificationStatus;
      }
      
      if (filter.createdAfter) {
        query.createdAt = { $gte: new Date(filter.createdAfter) };
      }
      
      if (filter.createdBefore) {
        query.createdAt = { ...query.createdAt, $lte: new Date(filter.createdBefore) };
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [evidence, total] = await Promise.all([
        SOC2Evidence.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        SOC2Evidence.countDocuments(query)
      ]);
      
      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;
      
      return {
        data: evidence,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting SOC 2 evidence records', { error });
      throw error;
    }
  }
  
  /**
   * Get SOC 2 evidence by ID
   * @param {string} id - Evidence ID
   * @returns {Promise<Object>} - Evidence record
   */
  async getEvidenceById(id) {
    try {
      const evidence = await SOC2Evidence.findById(id);
      
      if (!evidence) {
        throw new NotFoundError(`SOC 2 evidence with ID ${id} not found`);
      }
      
      return evidence;
    } catch (error) {
      logger.error('Error getting SOC 2 evidence by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Update SOC 2 evidence by ID
   * @param {string} id - Evidence ID
   * @param {Object} data - Updated evidence data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated evidence
   */
  async updateEvidence(id, data, userId) {
    try {
      // Get existing evidence
      const evidence = await this.getEvidenceById(id);
      
      // Update content hash if content data changed
      if (data.content && data.content.data) {
        data.content.hash = this._generateContentHash(data.content.data);
      }
      
      // Set updated by
      data.updatedBy = userId;
      
      // Update evidence
      Object.assign(evidence, data);
      await evidence.save();
      
      logger.info('SOC 2 evidence record updated successfully', { id });
      
      return evidence;
    } catch (error) {
      logger.error('Error updating SOC 2 evidence', { id, error });
      throw error;
    }
  }
  
  /**
   * Delete SOC 2 evidence by ID
   * @param {string} id - Evidence ID
   * @returns {Promise<boolean>} - Deletion success
   */
  async deleteEvidence(id) {
    try {
      const result = await SOC2Evidence.findByIdAndDelete(id);
      
      if (!result) {
        throw new NotFoundError(`SOC 2 evidence with ID ${id} not found`);
      }
      
      logger.info('SOC 2 evidence record deleted successfully', { id });
      
      return true;
    } catch (error) {
      logger.error('Error deleting SOC 2 evidence', { id, error });
      throw error;
    }
  }
  
  /**
   * Verify SOC 2 evidence with blockchain
   * @param {string} id - Evidence ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Verification result
   */
  async verifyEvidence(id, userId) {
    try {
      // Get evidence
      const evidence = await this.getEvidenceById(id);
      
      // Verify with blockchain service
      const verification = await BlockchainService.verifyEvidence(id, userId);
      
      // Update evidence verification status
      evidence.verification = {
        status: 'pending',
        method: 'blockchain',
        blockchainVerificationId: verification._id,
        verifiedAt: new Date(),
        verifiedBy: userId
      };
      
      await evidence.save();
      
      logger.info('SOC 2 evidence verification initiated', { id, verificationId: verification._id });
      
      return verification;
    } catch (error) {
      logger.error('Error verifying SOC 2 evidence', { id, error });
      throw error;
    }
  }
  
  /**
   * Check verification status for SOC 2 evidence
   * @param {string} id - Evidence ID
   * @returns {Promise<Object>} - Verification status
   */
  async checkVerificationStatus(id) {
    try {
      // Get evidence
      const evidence = await this.getEvidenceById(id);
      
      if (!evidence.verification.blockchainVerificationId) {
        return {
          evidenceId: id,
          status: 'not_verified',
          message: 'Evidence has not been submitted for verification'
        };
      }
      
      // Check verification status
      const verification = await BlockchainService.checkVerificationStatus(
        evidence.verification.blockchainVerificationId
      );
      
      // Update evidence verification status if verified
      if (verification.status === 'confirmed') {
        evidence.verification.status = 'verified';
        evidence.status = 'verified';
        await evidence.save();
      } else if (verification.status === 'failed') {
        evidence.verification.status = 'failed';
        await evidence.save();
      }
      
      return {
        evidenceId: id,
        status: evidence.verification.status,
        verificationId: evidence.verification.blockchainVerificationId,
        blockchainStatus: verification.status,
        transaction: verification.transaction
      };
    } catch (error) {
      logger.error('Error checking SOC 2 evidence verification status', { id, error });
      throw error;
    }
  }
  
  /**
   * Find SOC 2 evidence by control
   * @param {string} controlId - Control ID
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Array>} - Evidence records
   */
  async findByControl(controlId, organizationId) {
    try {
      return await SOC2Evidence.findByControl(controlId, organizationId);
    } catch (error) {
      logger.error('Error finding SOC 2 evidence by control', { controlId, error });
      throw error;
    }
  }
  
  /**
   * Find SOC 2 evidence by tags
   * @param {Array<string>} tags - Tags to search for
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Array>} - Evidence records
   */
  async findByTags(tags, organizationId) {
    try {
      return await SOC2Evidence.findByTags(tags, organizationId);
    } catch (error) {
      logger.error('Error finding SOC 2 evidence by tags', { tags, error });
      throw error;
    }
  }
  
  /**
   * Collect evidence from source
   * @param {string} organizationId - Organization ID
   * @param {string} controlId - Control ID
   * @param {string} source - Evidence source
   * @param {Object} options - Collection options
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Collection result
   */
  async collectEvidence(organizationId, controlId, source, options = {}, userId) {
    try {
      logger.info('Collecting SOC 2 evidence', { organizationId, controlId, source });
      
      // Get control
      const control = await SOC2Control.findOne({ id: controlId });
      
      if (!control) {
        throw new ValidationError(`SOC 2 control with ID ${controlId} not found`);
      }
      
      // Get collector for source
      const collector = this._getCollector(source);
      
      if (!collector) {
        throw new ValidationError(`Evidence collector for source ${source} not found`);
      }
      
      // Collect evidence
      const result = await collector.collect(organizationId, control, options);
      
      // Create evidence records
      const evidenceRecords = [];
      
      for (const item of result.items) {
        const evidence = await this.createEvidence({
          name: item.name,
          description: item.description,
          organizationId,
          control: {
            id: control.id,
            reference: control.reference,
            title: control.title,
            categories: control.category ? [control.category] : []
          },
          status: 'collected',
          metadata: {
            source,
            sourceType: source,
            collectionMethod: 'automated',
            collectedBy: userId,
            collectionDate: new Date(),
            tags: [source, control.category, ...control.trustServiceCriteria]
          },
          content: {
            format: item.format || 'json',
            data: item.data,
            hash: this._generateContentHash(item.data)
          }
        }, userId);
        
        evidenceRecords.push(evidence);
      }
      
      return {
        success: true,
        count: evidenceRecords.length,
        evidence: evidenceRecords,
        source,
        controlId
      };
    } catch (error) {
      logger.error('Error collecting SOC 2 evidence', { controlId, source, error });
      throw error;
    }
  }
  
  /**
   * Generate hash for content data
   * @param {*} data - Content data
   * @returns {string} - Content hash
   * @private
   */
  _generateContentHash(data) {
    const content = typeof data === 'object' ? JSON.stringify(data) : String(data);
    return crypto.createHash('sha256').update(content).digest('hex');
  }
  
  /**
   * Get collector for source
   * @param {string} source - Evidence source
   * @returns {Object} - Evidence collector
   * @private
   */
  _getCollector(source) {
    // Map of available collectors
    const collectors = {
      aws: require('./collectors/AWSCollector'),
      gcp: require('./collectors/GCPCollector'),
      github: require('./collectors/GitHubCollector'),
      jira: require('./collectors/JiraCollector'),
      manual: require('./collectors/ManualCollector')
    };
    
    return collectors[source.toLowerCase()];
  }
}

module.exports = new SOC2EvidenceService();
