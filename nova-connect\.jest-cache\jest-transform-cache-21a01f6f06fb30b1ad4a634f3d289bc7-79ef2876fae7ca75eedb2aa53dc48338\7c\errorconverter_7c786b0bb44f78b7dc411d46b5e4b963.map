{"version": 3, "names": ["createLogger", "require", "UAConnectorError", "ConnectionError", "TimeoutError", "NetworkError", "ServiceUnavailableError", "AuthenticationError", "InvalidCredentialsError", "RateLimitExceededError", "ResourceNotFoundError", "BadRequestError", "ServerError", "ValidationError", "logger", "convertAxiosError", "error", "context", "response", "status", "data", "errorContext", "request", "url", "config", "method", "headers", "code", "cause", "statusCode", "retryAfter", "parseInt", "undefined", "message", "convertJsonSchemaError", "schemaName", "validationErrors", "errors", "mappedErrors", "map", "err", "field", "dataPath", "instancePath", "keyword", "schemaPath", "convertError", "isAxiosError", "Array", "isArray", "module", "exports"], "sources": ["error-converter.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector - Error Converter\n * \n * This module provides utilities for converting errors from external libraries\n * to UAC-specific error types.\n */\n\nconst { createLogger } = require('./logger');\nconst {\n  UAConnectorError,\n  ConnectionError,\n  TimeoutError,\n  NetworkError,\n  ServiceUnavailableError,\n  AuthenticationError,\n  InvalidCredentialsError,\n  RateLimitExceededError,\n  ResourceNotFoundError,\n  BadRequestError,\n  ServerError,\n  ValidationError\n} = require('../errors');\n\nconst logger = createLogger('error-converter');\n\n/**\n * Convert an Axios error to a UAC-specific error\n * \n * @param {Error} error - The Axios error\n * @param {Object} context - Additional context for the error\n * @returns {UAConnectorError} - The converted error\n */\nfunction convertAxiosError(error, context = {}) {\n  // If it's already a UAConnectorError, return it\n  if (error instanceof UAConnectorError) {\n    return error;\n  }\n  \n  // Extract response data if available\n  const response = error.response || {};\n  const status = response.status;\n  const data = response.data || {};\n  \n  // Create context with request details\n  const errorContext = {\n    ...context,\n    request: {\n      url: error.config?.url,\n      method: error.config?.method,\n      headers: error.config?.headers\n    }\n  };\n  \n  // Handle different error types based on the status code\n  if (error.code === 'ECONNABORTED') {\n    return new TimeoutError('Request timed out', {\n      cause: error,\n      context: errorContext\n    });\n  }\n  \n  if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {\n    return new NetworkError(`Network error: ${error.code}`, {\n      cause: error,\n      context: errorContext\n    });\n  }\n  \n  // Handle HTTP status codes\n  if (status) {\n    // 401 Unauthorized\n    if (status === 401) {\n      return new InvalidCredentialsError('Authentication failed', {\n        cause: error,\n        context: errorContext,\n        statusCode: status,\n        response: data\n      });\n    }\n    \n    // 403 Forbidden\n    if (status === 403) {\n      return new AuthenticationError('Access forbidden', {\n        code: 'AUTH_FORBIDDEN',\n        cause: error,\n        context: errorContext,\n        statusCode: status,\n        response: data\n      });\n    }\n    \n    // 404 Not Found\n    if (status === 404) {\n      return new ResourceNotFoundError('Resource', error.config?.url, {\n        cause: error,\n        context: errorContext,\n        statusCode: status,\n        response: data\n      });\n    }\n    \n    // 429 Too Many Requests\n    if (status === 429) {\n      const retryAfter = response.headers?.['retry-after'];\n      return new RateLimitExceededError('Rate limit exceeded', {\n        cause: error,\n        context: errorContext,\n        statusCode: status,\n        response: data,\n        retryAfter: retryAfter ? parseInt(retryAfter, 10) : undefined\n      });\n    }\n    \n    // 400 Bad Request\n    if (status === 400) {\n      return new BadRequestError(data.message || 'Bad request', {\n        cause: error,\n        context: errorContext,\n        statusCode: status,\n        response: data\n      });\n    }\n    \n    // 500 Server Error\n    if (status >= 500) {\n      return new ServerError(data.message || 'Server error', {\n        cause: error,\n        context: errorContext,\n        statusCode: status,\n        response: data\n      });\n    }\n  }\n  \n  // Default to a generic ConnectionError\n  return new ConnectionError(error.message, {\n    cause: error,\n    context: errorContext\n  });\n}\n\n/**\n * Convert a JSON Schema validation error to a UAC-specific error\n * \n * @param {Error} error - The JSON Schema validation error\n * @param {string} schemaName - The name of the schema\n * @param {Object} context - Additional context for the error\n * @returns {ValidationError} - The converted error\n */\nfunction convertJsonSchemaError(error, schemaName, context = {}) {\n  // If it's already a UAConnectorError, return it\n  if (error instanceof UAConnectorError) {\n    return error;\n  }\n  \n  // Extract validation errors\n  const validationErrors = error.errors || [];\n  \n  // Map validation errors to a format we can use\n  const mappedErrors = validationErrors.map(err => ({\n    field: err.dataPath || err.instancePath,\n    message: err.message,\n    code: err.keyword,\n    schemaPath: err.schemaPath\n  }));\n  \n  // Create a SchemaValidationError\n  return new ValidationError(`Schema validation failed for ${schemaName}`, {\n    code: 'VALIDATION_SCHEMA_ERROR',\n    cause: error,\n    context: {\n      ...context,\n      schemaName\n    },\n    validationErrors: mappedErrors\n  });\n}\n\n/**\n * Convert any error to a UAC-specific error\n * \n * @param {Error} error - The error to convert\n * @param {Object} context - Additional context for the error\n * @returns {UAConnectorError} - The converted error\n */\nfunction convertError(error, context = {}) {\n  // If it's already a UAConnectorError, return it\n  if (error instanceof UAConnectorError) {\n    return error;\n  }\n  \n  // Check if it's an Axios error\n  if (error.isAxiosError) {\n    return convertAxiosError(error, context);\n  }\n  \n  // Check if it's a JSON Schema validation error\n  if (error.errors && Array.isArray(error.errors)) {\n    return convertJsonSchemaError(error, context.schemaName || 'unknown', context);\n  }\n  \n  // Default to a generic UAConnectorError\n  return new UAConnectorError(error.message, {\n    cause: error,\n    context\n  });\n}\n\nmodule.exports = {\n  convertAxiosError,\n  convertJsonSchemaError,\n  convertError\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAa,CAAC,GAAGC,OAAO,CAAC,UAAU,CAAC;AAC5C,MAAM;EACJC,gBAAgB;EAChBC,eAAe;EACfC,YAAY;EACZC,YAAY;EACZC,uBAAuB;EACvBC,mBAAmB;EACnBC,uBAAuB;EACvBC,sBAAsB;EACtBC,qBAAqB;EACrBC,eAAe;EACfC,WAAW;EACXC;AACF,CAAC,GAAGZ,OAAO,CAAC,WAAW,CAAC;AAExB,MAAMa,MAAM,GAAGd,YAAY,CAAC,iBAAiB,CAAC;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,iBAAiBA,CAACC,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC9C;EACA,IAAID,KAAK,YAAYd,gBAAgB,EAAE;IACrC,OAAOc,KAAK;EACd;;EAEA;EACA,MAAME,QAAQ,GAAGF,KAAK,CAACE,QAAQ,IAAI,CAAC,CAAC;EACrC,MAAMC,MAAM,GAAGD,QAAQ,CAACC,MAAM;EAC9B,MAAMC,IAAI,GAAGF,QAAQ,CAACE,IAAI,IAAI,CAAC,CAAC;;EAEhC;EACA,MAAMC,YAAY,GAAG;IACnB,GAAGJ,OAAO;IACVK,OAAO,EAAE;MACPC,GAAG,EAAEP,KAAK,CAACQ,MAAM,EAAED,GAAG;MACtBE,MAAM,EAAET,KAAK,CAACQ,MAAM,EAAEC,MAAM;MAC5BC,OAAO,EAAEV,KAAK,CAACQ,MAAM,EAAEE;IACzB;EACF,CAAC;;EAED;EACA,IAAIV,KAAK,CAACW,IAAI,KAAK,cAAc,EAAE;IACjC,OAAO,IAAIvB,YAAY,CAAC,mBAAmB,EAAE;MAC3CwB,KAAK,EAAEZ,KAAK;MACZC,OAAO,EAAEI;IACX,CAAC,CAAC;EACJ;EAEA,IAAIL,KAAK,CAACW,IAAI,KAAK,WAAW,IAAIX,KAAK,CAACW,IAAI,KAAK,cAAc,EAAE;IAC/D,OAAO,IAAItB,YAAY,CAAC,kBAAkBW,KAAK,CAACW,IAAI,EAAE,EAAE;MACtDC,KAAK,EAAEZ,KAAK;MACZC,OAAO,EAAEI;IACX,CAAC,CAAC;EACJ;;EAEA;EACA,IAAIF,MAAM,EAAE;IACV;IACA,IAAIA,MAAM,KAAK,GAAG,EAAE;MAClB,OAAO,IAAIX,uBAAuB,CAAC,uBAAuB,EAAE;QAC1DoB,KAAK,EAAEZ,KAAK;QACZC,OAAO,EAAEI,YAAY;QACrBQ,UAAU,EAAEV,MAAM;QAClBD,QAAQ,EAAEE;MACZ,CAAC,CAAC;IACJ;;IAEA;IACA,IAAID,MAAM,KAAK,GAAG,EAAE;MAClB,OAAO,IAAIZ,mBAAmB,CAAC,kBAAkB,EAAE;QACjDoB,IAAI,EAAE,gBAAgB;QACtBC,KAAK,EAAEZ,KAAK;QACZC,OAAO,EAAEI,YAAY;QACrBQ,UAAU,EAAEV,MAAM;QAClBD,QAAQ,EAAEE;MACZ,CAAC,CAAC;IACJ;;IAEA;IACA,IAAID,MAAM,KAAK,GAAG,EAAE;MAClB,OAAO,IAAIT,qBAAqB,CAAC,UAAU,EAAEM,KAAK,CAACQ,MAAM,EAAED,GAAG,EAAE;QAC9DK,KAAK,EAAEZ,KAAK;QACZC,OAAO,EAAEI,YAAY;QACrBQ,UAAU,EAAEV,MAAM;QAClBD,QAAQ,EAAEE;MACZ,CAAC,CAAC;IACJ;;IAEA;IACA,IAAID,MAAM,KAAK,GAAG,EAAE;MAClB,MAAMW,UAAU,GAAGZ,QAAQ,CAACQ,OAAO,GAAG,aAAa,CAAC;MACpD,OAAO,IAAIjB,sBAAsB,CAAC,qBAAqB,EAAE;QACvDmB,KAAK,EAAEZ,KAAK;QACZC,OAAO,EAAEI,YAAY;QACrBQ,UAAU,EAAEV,MAAM;QAClBD,QAAQ,EAAEE,IAAI;QACdU,UAAU,EAAEA,UAAU,GAAGC,QAAQ,CAACD,UAAU,EAAE,EAAE,CAAC,GAAGE;MACtD,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIb,MAAM,KAAK,GAAG,EAAE;MAClB,OAAO,IAAIR,eAAe,CAACS,IAAI,CAACa,OAAO,IAAI,aAAa,EAAE;QACxDL,KAAK,EAAEZ,KAAK;QACZC,OAAO,EAAEI,YAAY;QACrBQ,UAAU,EAAEV,MAAM;QAClBD,QAAQ,EAAEE;MACZ,CAAC,CAAC;IACJ;;IAEA;IACA,IAAID,MAAM,IAAI,GAAG,EAAE;MACjB,OAAO,IAAIP,WAAW,CAACQ,IAAI,CAACa,OAAO,IAAI,cAAc,EAAE;QACrDL,KAAK,EAAEZ,KAAK;QACZC,OAAO,EAAEI,YAAY;QACrBQ,UAAU,EAAEV,MAAM;QAClBD,QAAQ,EAAEE;MACZ,CAAC,CAAC;IACJ;EACF;;EAEA;EACA,OAAO,IAAIjB,eAAe,CAACa,KAAK,CAACiB,OAAO,EAAE;IACxCL,KAAK,EAAEZ,KAAK;IACZC,OAAO,EAAEI;EACX,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,sBAAsBA,CAAClB,KAAK,EAAEmB,UAAU,EAAElB,OAAO,GAAG,CAAC,CAAC,EAAE;EAC/D;EACA,IAAID,KAAK,YAAYd,gBAAgB,EAAE;IACrC,OAAOc,KAAK;EACd;;EAEA;EACA,MAAMoB,gBAAgB,GAAGpB,KAAK,CAACqB,MAAM,IAAI,EAAE;;EAE3C;EACA,MAAMC,YAAY,GAAGF,gBAAgB,CAACG,GAAG,CAACC,GAAG,KAAK;IAChDC,KAAK,EAAED,GAAG,CAACE,QAAQ,IAAIF,GAAG,CAACG,YAAY;IACvCV,OAAO,EAAEO,GAAG,CAACP,OAAO;IACpBN,IAAI,EAAEa,GAAG,CAACI,OAAO;IACjBC,UAAU,EAAEL,GAAG,CAACK;EAClB,CAAC,CAAC,CAAC;;EAEH;EACA,OAAO,IAAIhC,eAAe,CAAC,gCAAgCsB,UAAU,EAAE,EAAE;IACvER,IAAI,EAAE,yBAAyB;IAC/BC,KAAK,EAAEZ,KAAK;IACZC,OAAO,EAAE;MACP,GAAGA,OAAO;MACVkB;IACF,CAAC;IACDC,gBAAgB,EAAEE;EACpB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,YAAYA,CAAC9B,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACzC;EACA,IAAID,KAAK,YAAYd,gBAAgB,EAAE;IACrC,OAAOc,KAAK;EACd;;EAEA;EACA,IAAIA,KAAK,CAAC+B,YAAY,EAAE;IACtB,OAAOhC,iBAAiB,CAACC,KAAK,EAAEC,OAAO,CAAC;EAC1C;;EAEA;EACA,IAAID,KAAK,CAACqB,MAAM,IAAIW,KAAK,CAACC,OAAO,CAACjC,KAAK,CAACqB,MAAM,CAAC,EAAE;IAC/C,OAAOH,sBAAsB,CAAClB,KAAK,EAAEC,OAAO,CAACkB,UAAU,IAAI,SAAS,EAAElB,OAAO,CAAC;EAChF;;EAEA;EACA,OAAO,IAAIf,gBAAgB,CAACc,KAAK,CAACiB,OAAO,EAAE;IACzCL,KAAK,EAAEZ,KAAK;IACZC;EACF,CAAC,CAAC;AACJ;AAEAiC,MAAM,CAACC,OAAO,GAAG;EACfpC,iBAAiB;EACjBmB,sBAAsB;EACtBY;AACF,CAAC", "ignoreList": []}