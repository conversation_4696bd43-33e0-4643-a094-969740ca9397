/**
 * Data Subject Request Routes
 *
 * This file defines the routes for data subject requests.
 */

const express = require('express');
const router = express.Router();
const { subjectRequestController } = require('../controllers');
const { validate } = require('../middleware/validation');
const { subjectRequestSchema } = require('../validation');
const { authorize } = require('../middleware/auth');

// Get all data subject requests
router.get('/', authorize(['admin', 'user']), subjectRequestController.getAllSubjectRequests);

// Get a specific data subject request by ID
router.get('/:id', authorize(['admin', 'user']), subjectRequestController.getSubjectRequestById);

// Create a new data subject request
router.post('/',
  validate(subjectRequestSchema.createSubjectRequestSchema),
  subjectRequestController.createSubjectRequest
);

// Update a data subject request
router.put('/:id',
  authorize(['admin', 'user']),
  validate(subjectRequestSchema.updateSubjectRequestSchema),
  subjectRequestController.updateSubjectRequest
);

// Process a data subject request
router.post('/:id/process',
  authorize(['admin']),
  subjectRequestController.processSubjectRequest
);

// Generate a data export for a data subject request
router.get('/:id/export',
  authorize(['admin', 'user']),
  subjectRequestController.generateDataExport
);

// Identify affected systems for a data subject request
router.get('/:id/affected-systems',
  authorize(['admin', 'user']),
  subjectRequestController.identifyAffectedSystems
);

module.exports = router;
