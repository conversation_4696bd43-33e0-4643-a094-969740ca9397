#!/bin/sh
# wait-for-it.sh
# Use this script to wait for a service to be available before starting another service.
# Example: ./wait-for-it.sh host:port -- command args

set -e

host="$1"
shift
port="${host#*:}"
host="${host%:*}"
timeout=30

# Parse command line arguments
cmd="$@"

# Default timeout is 30 seconds
if [ "$1" = "--timeout" ]; then
    timeout="$2"
    shift 2
    cmd="$@"
fi

# Function to check if a service is available
wait_for_service() {
    local host=$1
    local port=$2
    local timeout=$3
    local start_time=$(date +%s)
    local end_time=$((start_time + timeout))
    
    echo "Waiting for $host:$port..."
    
    while ! nc -z "$host" "$port" 2>/dev/null; do
        current_time=$(date +%s)
        if [ $current_time -ge $end_time ]; then
            echo "Timeout reached while waiting for $host:$port"
            exit 1
        fi
        sleep 1
    done
    
    echo "$host:$port is available!"
}

# Wait for the service
wait_for_service "$host" "$port" "$timeout"

# Execute the command if provided
if [ -n "$cmd" ]; then
    exec $cmd
fi
