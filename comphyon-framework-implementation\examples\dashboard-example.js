/**
 * Dashboard Example
 * 
 * This example demonstrates the usage of the Dashboard component.
 */

const {
  Dashboard,
  NovaVisionIntegration,
  createDashboardSystem,
  createEnhancedDashboardSystem
} = require('../dashboard');

// Import other components for integration
const { createEnhancedMeterSystem } = require('../meter');
const { createEnhancedBridgeSystem } = require('../bridge');
const { createEnhancedGovernorSystem } = require('../governor');

// Mock NovaVision for demonstration
const mockNovaVision = {
  render: (schema, target) => {
    console.log(`[NovaVision] Rendering schema: ${schema.id}`);
    console.log(`[NovaVision] Schema type: ${schema.type}`);
    console.log(`[NovaVision] Schema title: ${schema.title}`);
    console.log(`[NovaVision] Schema description: ${schema.description}`);
    console.log(`[NovaVision] Target: ${target}`);
    return true;
  }
};

// Example 1: Using individual components
console.log('Example 1: Using individual components');

// Create components
const novaVisionIntegration = new NovaVisionIntegration({
  novaVision: mockNovaVision
});
const dashboard = new Dashboard({
  novaVision: mockNovaVision
});

// Connect to NovaVision
novaVisionIntegration.connect(mockNovaVision);

// Start dashboard
dashboard.start()
  .then(() => {
    console.log('Dashboard started');
    
    // Update dashboard with sample data
    dashboard.update({
      universalEntropy: 0.5,
      domainEntropy: {
        cyber: {
          overallEntropy: 0.6,
          policyEntropy: 0.7,
          auditEntropy: 0.5,
          regulatoryEntropy: 0.6
        },
        financial: {
          overallEntropy: 0.4,
          transactionEntropy: 0.5,
          attackSurfaceCoherence: 0.3,
          marketStress: 0.4
        },
        biological: {
          overallEntropy: 0.3,
          telomereLength: 0.4,
          mtorActivation: 0.2,
          inflammationLevel: 0.3
        }
      },
      comphyonValue: 0.1,
      timestamp: Date.now()
    });
    
    // Render dashboard
    dashboard.render('universal-entropy', 'dashboard-container');
    
    // Stop dashboard
    dashboard.stop();
    console.log('Dashboard stopped');
  })
  .catch(error => {
    console.error('Error starting dashboard:', error);
  });

// Example 2: Using the basic Dashboard system
console.log('\nExample 2: Using the basic Dashboard system');

// Create Dashboard system
const dashboardSystem = createDashboardSystem({
  novaVision: mockNovaVision,
  enableLogging: true
});

// Start dashboard
dashboardSystem.dashboard.start()
  .then(() => {
    console.log('Dashboard system started');
    
    // Update dashboard with sample data
    dashboardSystem.dashboard.update({
      universalEntropy: 0.6,
      domainEntropy: {
        cyber: {
          overallEntropy: 0.7,
          policyEntropy: 0.8,
          auditEntropy: 0.6,
          regulatoryEntropy: 0.7
        },
        financial: {
          overallEntropy: 0.5,
          transactionEntropy: 0.6,
          attackSurfaceCoherence: 0.4,
          marketStress: 0.5
        },
        biological: {
          overallEntropy: 0.4,
          telomereLength: 0.5,
          mtorActivation: 0.3,
          inflammationLevel: 0.4
        }
      },
      comphyonValue: 0.2,
      timestamp: Date.now()
    });
    
    // Render dashboard
    dashboardSystem.dashboard.render('universal-entropy', 'dashboard-container');
    
    // Stop dashboard
    dashboardSystem.dashboard.stop();
    console.log('Dashboard system stopped');
  })
  .catch(error => {
    console.error('Error starting dashboard system:', error);
  });

// Example 3: Using the enhanced Dashboard system with integrated components
console.log('\nExample 3: Using the enhanced Dashboard system with integrated components');

// Create Meter system
const meterSystem = createEnhancedMeterSystem({
  enableLogging: true
});

// Create Bridge system
const bridgeSystem = createEnhancedBridgeSystem({
  enableLogging: true
});

// Create Governor system
const governorSystem = createEnhancedGovernorSystem({
  enableLogging: true
}, meterSystem);

// Create enhanced Dashboard system
const enhancedDashboardSystem = createEnhancedDashboardSystem(
  {
    novaVision: mockNovaVision,
    enableLogging: true,
    updateInterval: 5000
  },
  meterSystem,
  bridgeSystem,
  governorSystem
);

// Start all systems
Promise.all([
  meterSystem.start(),
  bridgeSystem.start(),
  governorSystem.start(),
  enhancedDashboardSystem.start()
])
  .then(() => {
    console.log('All systems started');
    
    // Update domain metrics in Meter
    meterSystem.universalEntropyMeasurement.updateDomainMetric('cyber', 'policyEntropy', 0.7);
    meterSystem.universalEntropyMeasurement.updateDomainMetric('financial', 'transactionEntropy', 0.6);
    meterSystem.universalEntropyMeasurement.updateDomainMetric('biological', 'inflammationLevel', 0.5);
    
    // Process domain data in Bridge
    bridgeSystem.processDomainData('cyber', 'policy_entropy', 0.7, {
      source: 'example',
      timestamp: Date.now()
    });
    
    // Render dashboards
    console.log('\nRendering Universal Entropy Dashboard:');
    enhancedDashboardSystem.renderUniversalEntropyDashboard('universal-entropy-container');
    
    console.log('\nRendering Cross-Domain Risk Dashboard:');
    enhancedDashboardSystem.renderCrossDomainRiskDashboard('cross-domain-risk-container');
    
    // Get metrics
    const metrics = enhancedDashboardSystem.getMetrics();
    console.log('\nDashboard Metrics:');
    console.log(`- Processing Time: ${metrics.processingTimeMs.toFixed(2)} ms`);
    console.log(`- Updates Processed: ${metrics.updatesProcessed}`);
    console.log(`- Rendering Time: ${metrics.renderingTimeMs.toFixed(2)} ms`);
    console.log(`- Renders Performed: ${metrics.rendersPerformed}`);
    
    // Stop all systems
    meterSystem.stop();
    bridgeSystem.stop();
    governorSystem.stop();
    enhancedDashboardSystem.stop();
    console.log('\nAll systems stopped');
  })
  .catch(error => {
    console.error('Error starting systems:', error);
  });

// Example 4: Simulating real-time updates
console.log('\nExample 4: Simulating real-time updates');

// Create systems
const meterSystem2 = createEnhancedMeterSystem({
  enableLogging: false
});

const bridgeSystem2 = createEnhancedBridgeSystem({
  enableLogging: false
});

const governorSystem2 = createEnhancedGovernorSystem({
  enableLogging: false
}, meterSystem2);

const dashboardSystem2 = createEnhancedDashboardSystem(
  {
    novaVision: mockNovaVision,
    enableLogging: true,
    updateInterval: 1000 // 1 second
  },
  meterSystem2,
  bridgeSystem2,
  governorSystem2
);

// Start all systems
Promise.all([
  meterSystem2.start(),
  bridgeSystem2.start(),
  governorSystem2.start(),
  dashboardSystem2.start()
])
  .then(() => {
    console.log('All systems started for real-time simulation');
    
    // Set up update interval
    let updateCount = 0;
    const maxUpdates = 5;
    
    const updateInterval = setInterval(() => {
      updateCount++;
      
      // Generate random entropy values
      const policyEntropy = 0.5 + Math.random() * 0.3;
      const transactionEntropy = 0.4 + Math.random() * 0.3;
      const inflammationLevel = 0.3 + Math.random() * 0.3;
      
      console.log(`\nUpdate ${updateCount}:`);
      console.log(`- Policy Entropy: ${policyEntropy.toFixed(4)}`);
      console.log(`- Transaction Entropy: ${transactionEntropy.toFixed(4)}`);
      console.log(`- Inflammation Level: ${inflammationLevel.toFixed(4)}`);
      
      // Update domain metrics in Meter
      meterSystem2.universalEntropyMeasurement.updateDomainMetric('cyber', 'policyEntropy', policyEntropy);
      meterSystem2.universalEntropyMeasurement.updateDomainMetric('financial', 'transactionEntropy', transactionEntropy);
      meterSystem2.universalEntropyMeasurement.updateDomainMetric('biological', 'inflammationLevel', inflammationLevel);
      
      // Process domain data in Bridge
      bridgeSystem2.processDomainData('cyber', 'policy_entropy', policyEntropy, {
        source: 'simulation',
        timestamp: Date.now()
      });
      
      // Render dashboards
      console.log('\nRendering Universal Entropy Dashboard:');
      dashboardSystem2.renderUniversalEntropyDashboard('universal-entropy-container');
      
      // Stop after max updates
      if (updateCount >= maxUpdates) {
        clearInterval(updateInterval);
        
        // Stop all systems
        meterSystem2.stop();
        bridgeSystem2.stop();
        governorSystem2.stop();
        dashboardSystem2.stop();
        console.log('\nReal-time simulation completed');
        console.log('All systems stopped');
      }
    }, 1000);
  })
  .catch(error => {
    console.error('Error starting systems for real-time simulation:', error);
  });

console.log('\nDashboard example started. Check the console for output.');
console.log('Note: In a real application, the dashboard would be rendered in a web browser using NovaVision.');
