#!/usr/bin/env python3
"""
Example script demonstrating how to run quantum benchmarks and visualize results.

This script shows how to:
1. Run protein folding benchmarks with different quantum backends
2. Generate visualizations of the results
3. Create a comprehensive HTML report
"""

import os
import sys
import json
import time
import logging
import argparse
from pathlib import Path
from datetime import datetime

# Add parent directory to path to import from src
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('quantum_benchmark.log')
    ]
)
logger = logging.getLogger(__name__)

def run_benchmark(sequence: str, output_dir: str, rosettafold_path: str, 
                 backends: list = None, shots: int = 1000, depth: int = 100,
                 layers: int = 2) -> dict:
    """Run a benchmark with the given parameters.
    
    Args:
        sequence: Protein sequence to fold
        output_dir: Directory to save results
        rosettafold_path: Path to RoseTTAFold installation
        backends: List of backends to test
        shots: Number of quantum shots
        depth: Quantum circuit depth
        layers: Number of quantum layers
        
    Returns:
        Dictionary with benchmark results
    """
    from src.rosettafold_engine import RoseTTAFoldEngine
    
    # Default backends
    if backends is None:
        backends = ['qiskit', 'cirq']
    
    results = []
    
    for backend in backends:
        try:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running benchmark with {backend} backend")
            logger.info(f"Sequence length: {len(sequence)}")
            logger.info(f"Shots: {shots}, Depth: {depth}, Layers: {layers}")
            
            # Configure engine
            config = {
                'mode': 'hybrid',
                'rosettafold_path': rosettafold_path,
                'output_dir': os.path.join(output_dir, f"{backend}_{int(time.time())}"),
                'quantum_backend': backend,
                'quantum_shots': shots,
                'quantum_circuit_depth': depth,
                'quantum_layers': layers,
                'psi_optimization': True,
                'fib_constraints': {
                    'enabled': True,
                    'tolerance': 0.1
                },
                'debug': False
            }
            
            # Run prediction
            start_time = time.time()
            engine = RoseTTAFoldEngine(config=config)
            result = engine.predict(sequence)
            end_time = time.time()
            
            # Add benchmark metrics
            result['benchmark_metrics'] = {
                'backend': backend,
                'sequence_length': len(sequence),
                'duration': end_time - start_time,
                'timestamp': datetime.now().isoformat(),
                'config': config
            }
            
            results.append(result)
            logger.info(f"Completed in {result['benchmark_metrics']['duration']:.2f} seconds")
            
        except Exception as e:
            logger.error(f"Error running benchmark with {backend}: {str(e)}", exc_info=True)
    
    return results

def save_results(results: list, output_dir: str) -> str:
    """Save benchmark results to a file.
    
    Args:
        results: List of benchmark results
        output_dir: Directory to save results
        
    Returns:
        Path to the results file
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = output_dir / f"quantum_benchmark_{timestamp}.json"
    
    with open(output_file, 'w') as f:
        json.dump({
            'timestamp': timestamp,
            'results': results
        }, f, indent=2)
    
    return str(output_file)

def main():
    """Main function to run the benchmark and generate visualizations."""
    parser = argparse.ArgumentParser(description='Run quantum benchmarks for protein folding')
    parser.add_argument('--rosettafold-path', type=str, required=True,
                       help='Path to RoseTTAFold installation')
    parser.add_argument('--output-dir', type=str, default='quantum_benchmark_results',
                       help='Directory to save results')
    parser.add_argument('--sequence', type=str, 
                       default='ACDEFGHIKLMNPQRSTVWY',  # Example sequence
                       help='Protein sequence to fold')
    parser.add_argument('--backends', type=str, nargs='+', default=['qiskit', 'cirq'],
                       help='Quantum backends to test')
    parser.add_argument('--shots', type=int, default=1000,
                       help='Number of quantum shots')
    parser.add_argument('--depth', type=int, default=100,
                       help='Quantum circuit depth')
    parser.add_argument('--layers', type=int, default=2,
                       help='Number of quantum layers')
    parser.add_argument('--no-visualize', action='store_true',
                       help='Skip visualization')
    
    args = parser.parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Starting quantum benchmark with sequence: {args.sequence}")
    logger.info(f"Backends: {', '.join(args.backends)}")
    logger.info(f"Shots: {args.shots}, Depth: {args.depth}, Layers: {args.layers}")
    
    # Run benchmark
    results = run_benchmark(
        sequence=args.sequence,
        output_dir=str(output_dir / 'runs'),
        rosettafold_path=args.rosettafold_path,
        backends=args.backends,
        shots=args.shots,
        depth=args.depth,
        layers=args.layers
    )
    
    # Save results
    results_file = save_results(results, output_dir / 'data')
    logger.info(f"Results saved to: {results_file}")
    
    # Generate visualizations if not disabled
    if not args.no_visualize:
        try:
            from src.visualization.benchmark_visualizer import visualize_benchmark_results
            
            logger.info("\nGenerating visualizations...")
            report_path = visualize_benchmark_results(
                results_dir=str(Path(results_file).parent),
                output_dir=str(output_dir / 'visualizations')
            )
            logger.info(f"Visualization report generated: {report_path}")
            
        except ImportError as e:
            logger.warning(f"Could not generate visualizations: {str(e)}")
            logger.warning("Make sure all visualization dependencies are installed.")
    
    logger.info("\nBenchmark completed successfully!")

if __name__ == "__main__":
    main()
