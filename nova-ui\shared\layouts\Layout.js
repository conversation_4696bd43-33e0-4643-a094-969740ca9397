import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';

/**
 * Main Layout Component for NovaFuse Platform
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @returns {React.ReactNode} - The rendered layout
 */
export default function Layout({ children }) {
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: '🏠',
      description: 'Main NovaFuse Dashboard'
    },
    {
      name: 'NovaAlign',
      href: '/nova-align',
      icon: '🎯',
      description: 'AI Alignment & Safety Monitoring'
    },
    {
      name: 'NovaMatrix',
      href: '/novamatrix',
      icon: '🌌',
      description: 'Pentagonal Consciousness Fusion'
    },
    {
      name: 'NovaFold',
      href: '/novafold',
      icon: '🧬',
      description: 'Consciousness-Enhanced Protein Folding'
    },
    {
      name: 'NECE',
      href: '/nece',
      icon: '⚗️',
      description: 'Neuroemotive-Compatible Chemistry'
    }
  ];

  const isActivePage = (href) => {
    return router.pathname === href || router.pathname.startsWith(href + '/');
  };

  return (
    <div className="min-h-screen bg-slate-900">
      {/* Navigation Header */}
      <nav className="bg-slate-800 border-b border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            {/* Logo and Brand */}
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center space-x-3">
                <div className="text-2xl">🌌</div>
                <div className="text-xl font-bold text-white">CRSS</div>
                <div className="text-sm text-blue-300 hidden md:block">Coherence Reality Systems Studio</div>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-1">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActivePage(item.href)
                      ? 'bg-blue-600 text-white'
                      : 'text-slate-300 hover:text-white hover:bg-slate-700'
                  }`}
                  title={item.description}
                >
                  <span className="mr-2">{item.icon}</span>
                  {item.name}
                </Link>
              ))}
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden flex items-center">
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="text-slate-300 hover:text-white hover:bg-slate-700 px-3 py-2 rounded-md"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-slate-800 border-t border-slate-700">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                    isActivePage(item.href)
                      ? 'bg-blue-600 text-white'
                      : 'text-slate-300 hover:text-white hover:bg-slate-700'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <span className="mr-2">{item.icon}</span>
                  {item.name}
                  <div className="text-xs text-slate-400 mt-1">{item.description}</div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </nav>

      {/* Page Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-slate-800 border-t border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <div className="text-slate-400 text-sm">
                © 2024 CRSS Technologies. Coherence Reality Systems Studio - Consciousness-Enhanced Reality Engineering Platform.
              </div>
            </div>
            
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-slate-400 text-sm">All Systems Operational</span>
              </div>
              
              <div className="text-slate-400 text-sm">
                Platform Status: 
                <span className="text-green-400 ml-1 font-medium">Active</span>
              </div>
            </div>
          </div>
          
          {/* Platform Links */}
          <div className="mt-4 pt-4 border-t border-slate-700">
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
              {navigation.slice(1).map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-slate-400 hover:text-blue-400 transition-colors"
                >
                  <div className="flex items-center space-x-2">
                    <span>{item.icon}</span>
                    <span>{item.name}</span>
                  </div>
                  <div className="text-xs text-slate-500 mt-1">{item.description}</div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
