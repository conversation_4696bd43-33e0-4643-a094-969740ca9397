apiVersion: v1
kind: Service
metadata:
  name: novafuse-uac-service
  namespace: novafuse-production
  labels:
    app: novafuse-uac
    environment: production
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
    cloud.google.com/backend-config: '{"default": "novafuse-uac-backend-config"}'
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3001
    protocol: TCP
    name: http
  selector:
    app: novafuse-uac
