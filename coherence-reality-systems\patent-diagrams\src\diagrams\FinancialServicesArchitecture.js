import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText
} from '../components/DiagramComponents';

const FinancialServicesArchitecture = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>FINANCIAL SERVICES SYSTEM ARCHITECTURE</ContainerLabel>
      </ContainerBox>
      
      {/* Core Components */}
      <ContainerBox width="300px" height="150px" left="250px" top="70px">
        <ContainerLabel>CYBER-SAFETY PROTOCOL CORE</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="270px" top="120px" width="120px">
        <ComponentNumber>101</ComponentNumber>
        <ComponentLabel>Native Unification</ComponentLabel>
        Engine
      </ComponentBox>
      
      <ComponentBox left="410px" top="120px" width="120px">
        <ComponentNumber>102</ComponentNumber>
        <ComponentLabel>Dynamic UI</ComponentLabel>
        Enforcement
      </ComponentBox>
      
      {/* Financial Services Extensions */}
      <ContainerBox width="200px" height="120px" left="100px" top="250px">
        <ContainerLabel>FRAUD DETECTION</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="135px" top="290px" width="130px">
        <ComponentNumber>103</ComponentNumber>
        <ComponentLabel>AI Fraud</ComponentLabel>
        Prediction
      </ComponentBox>
      
      <ContainerBox width="200px" height="120px" left="500px" top="250px">
        <ContainerLabel>COMPLIANCE ENFORCEMENT</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="535px" top="290px" width="130px">
        <ComponentNumber>104</ComponentNumber>
        <ComponentLabel>Dynamic Compliance</ComponentLabel>
        Rulebook
      </ComponentBox>
      
      {/* Integration Layer */}
      <ContainerBox width="200px" height="120px" left="300px" top="250px">
        <ContainerLabel>INTEGRATION LAYER</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="335px" top="290px" width="130px">
        <ComponentNumber>105</ComponentNumber>
        <ComponentLabel>Fraud-Compliance</ComponentLabel>
        Bridge
      </ComponentBox>
      
      {/* Arrows connecting components */}
      <Arrow left="200px" top="290px" width="135px" />
      <Arrow left="465px" top="290px" width="70px" />
      
      <CurvedArrow width="300" height="150" left="335" top="170">
        <path
          d="M 0,0 Q 0,80 -150,120"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="-150,120 -140,112 -143,122"
          fill="#333"
        />
      </CurvedArrow>
      
      <CurvedArrow width="300" height="150" left="465" top="170">
        <path
          d="M 0,0 Q 0,80 150,120"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="150,120 140,112 143,122"
          fill="#333"
        />
      </CurvedArrow>
      
      {/* Novel Elements */}
      <ComponentBox left="80px" top="400px" width="100px" height="40px" style={{ opacity: 0.7 }}>
        <ComponentNumber>106</ComponentNumber>
        <ComponentLabel>Audit Trail</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="190px" top="400px" width="100px" height="40px" style={{ opacity: 0.7 }}>
        <ComponentNumber>107</ComponentNumber>
        <ComponentLabel>Explainable AI</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="300px" top="400px" width="100px" height="40px" style={{ opacity: 0.7 }}>
        <ComponentNumber>108</ComponentNumber>
        <ComponentLabel>DeFi Compliance</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="410px" top="400px" width="100px" height="40px" style={{ opacity: 0.7 }}>
        <ComponentNumber>109</ComponentNumber>
        <ComponentLabel>IoT Security</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="520px" top="400px" width="100px" height="40px" style={{ opacity: 0.7 }}>
        <ComponentNumber>110</ComponentNumber>
        <ComponentLabel>Kill Switch</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="630px" top="400px" width="100px" height="40px" style={{ opacity: 0.7 }}>
        <ComponentNumber>111</ComponentNumber>
        <ComponentLabel>Risk Scoring</ComponentLabel>
      </ComponentBox>
      
      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Core Components</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Financial Extensions</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Novel Elements</LegendText>
        </LegendItem>
      </DiagramLegend>
    </DiagramFrame>
  );
};

export default FinancialServicesArchitecture;
