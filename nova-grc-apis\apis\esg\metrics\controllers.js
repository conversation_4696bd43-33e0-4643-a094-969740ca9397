const { v4: uuidv4 } = require('uuid');
const models = require('./models');

/**
 * Centralized error handler for consistent error responses
 * @param {Error} error - The error object
 * @param {Object} res - Express response object
 * @param {string} functionName - Name of the function where error occurred
 */
const handleError = (error, res, functionName) => {
  console.error(`Error in ${functionName}:`, error);
  res.status(500).json({
    error: 'Internal Server Error',
    message: error.message,
    code: error.code || 'UNKNOWN_ERROR'
  });
};

/**
 * Get a list of ESG metrics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMetrics = (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      category,
      subcategory,
      framework,
      standardId,
      isStandardized,
      verificationRequired,
      dataCollectionFrequency,
      owner,
      status,
      tag,
      targetDateBefore,
      targetDateAfter,
      sortBy = 'name',
      sortOrder = 'asc'
    } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter metrics based on query parameters - using a single pass approach
    const filteredMetrics = models.esgMetrics.filter(metric => {
      // Apply all filters in a single pass
      const categoryMatch = !category || metric.category === category;
      const subcategoryMatch = !subcategory || (metric.subcategory && metric.subcategory.toLowerCase().includes(subcategory.toLowerCase()));

      // Framework matching - check both legacy framework field and new frameworkMappings
      const frameworkMatch = !framework ||
        (metric.framework && metric.framework.toLowerCase().includes(framework.toLowerCase())) ||
        (metric.frameworkMappings && metric.frameworkMappings.some(mapping =>
          mapping.frameworkId.toLowerCase().includes(framework.toLowerCase())
        ));

      const standardIdMatch = !standardId || (metric.standardId && metric.standardId.toLowerCase().includes(standardId.toLowerCase()));
      const isStandardizedMatch = isStandardized === undefined || metric.isStandardized === (isStandardized === 'true');
      const verificationRequiredMatch = verificationRequired === undefined || metric.verificationRequired === (verificationRequired === 'true');
      const dataCollectionFrequencyMatch = !dataCollectionFrequency || metric.dataCollectionFrequency === dataCollectionFrequency;
      const ownerMatch = !owner || (metric.owner && metric.owner.toLowerCase().includes(owner.toLowerCase()));
      const statusMatch = !status || metric.status === status;

      // Tag matching
      const tagMatch = !tag || (metric.tags && metric.tags.some(t => t.toLowerCase().includes(tag.toLowerCase())));

      // Target date range matching
      const targetDateBeforeMatch = !targetDateBefore || (metric.targetDate && metric.targetDate <= targetDateBefore);
      const targetDateAfterMatch = !targetDateAfter || (metric.targetDate && metric.targetDate >= targetDateAfter);

      return categoryMatch && subcategoryMatch && frameworkMatch && standardIdMatch &&
             isStandardizedMatch && verificationRequiredMatch && dataCollectionFrequencyMatch &&
             ownerMatch && statusMatch && tagMatch && targetDateBeforeMatch && targetDateAfterMatch;
    });

    // Sort metrics - improved comparison for better performance and correctness
    filteredMetrics.sort((a, b) => {
      // Handle undefined or null values
      const valA = a[sortBy] !== undefined ? a[sortBy] : '';
      const valB = b[sortBy] !== undefined ? b[sortBy] : '';

      // Case insensitive comparison for strings
      if (typeof valA === 'string' && typeof valB === 'string') {
        return sortOrder.toLowerCase() === 'desc'
          ? valB.localeCompare(valA)
          : valA.localeCompare(valB);
      }

      // Numeric comparison
      return sortOrder.toLowerCase() === 'desc'
        ? (valB > valA ? 1 : -1)
        : (valA > valB ? 1 : -1);
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedMetrics = filteredMetrics.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalMetrics = filteredMetrics.length;
    const totalPages = Math.ceil(totalMetrics / limitNum);

    res.json({
      data: paginatedMetrics,
      pagination: {
        total: totalMetrics,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    handleError(error, res, 'getMetrics');
  }
};

/**
 * Get a specific ESG metric by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMetricById = (req, res) => {
  try {
    const { id } = req.params;
    const metric = models.esgMetrics.find(m => m.id === id);

    if (!metric) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG metric with ID ${id} not found`
      });
    }

    res.json({ data: metric });
  } catch (error) {
    console.error('Error in getMetricById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new ESG metric
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createMetric = (req, res) => {
  try {
    const {
      name,
      description,
      category,
      subcategory,
      unit,
      dataType,
      isStandardized,
      standardId,
      frameworkMappings,
      calculationMethod,
      calculationFormula,
      dataCollectionFrequency,
      verificationRequired,
      verificationProcess,
      benchmarkValue,
      benchmarkSource,
      targetValue,
      targetDate,
      owner,
      status,
      tags
    } = req.body;

    // Check for duplicate metric names to ensure uniqueness
    const existingMetric = models.esgMetrics.find(m =>
      m.name.toLowerCase() === name.toLowerCase() &&
      m.category === category
    );

    if (existingMetric) {
      return res.status(409).json({
        error: 'Conflict',
        message: `An ESG metric with name '${name}' already exists in the ${category} category`
      });
    }

    // Validate framework mappings if provided
    if (frameworkMappings && Array.isArray(frameworkMappings)) {
      for (const mapping of frameworkMappings) {
        if (!mapping.frameworkId || !mapping.elementId) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'Framework mappings must include frameworkId and elementId'
          });
        }
      }
    }

    // Sanitize inputs (basic sanitization - in production, use a library like DOMPurify)
    const sanitizeInput = (input) => {
      if (typeof input !== 'string') return input;
      return input
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')
        .replace(/\//g, '&#x2F;');
    };

    // Create a new metric with a unique ID
    const newMetric = {
      id: `esg-m-${uuidv4().substring(0, 8)}`,
      name: sanitizeInput(name),
      description: sanitizeInput(description),
      category,
      subcategory: sanitizeInput(subcategory),
      unit: sanitizeInput(unit),
      dataType,
      isStandardized: isStandardized || false,
      standardId: sanitizeInput(standardId) || null,
      frameworkMappings: frameworkMappings || [],
      calculationMethod: sanitizeInput(calculationMethod) || null,
      calculationFormula: sanitizeInput(calculationFormula) || null,
      dataCollectionFrequency: dataCollectionFrequency || 'monthly',
      verificationRequired: verificationRequired || false,
      verificationProcess: sanitizeInput(verificationProcess) || null,
      benchmarkValue: sanitizeInput(benchmarkValue) || null,
      benchmarkSource: sanitizeInput(benchmarkSource) || null,
      targetValue: sanitizeInput(targetValue) || null,
      targetDate: targetDate || null,
      owner: sanitizeInput(owner) || null,
      status,
      tags: Array.isArray(tags) ? tags.map(tag => sanitizeInput(tag)) : [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new metric to the collection
    models.esgMetrics.push(newMetric);

    // Log the creation for audit purposes
    console.info(`ESG metric created: ${newMetric.id} - ${newMetric.name} by ${req.user?.username || 'unknown'}`);

    res.status(201).json({
      data: newMetric,
      message: 'ESG metric created successfully'
    });
  } catch (error) {
    handleError(error, res, 'createMetric');
  }
};

/**
 * Update an existing ESG metric
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateMetric = (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      category,
      subcategory,
      unit,
      dataType,
      isStandardized,
      standardId,
      frameworkMappings,
      calculationMethod,
      calculationFormula,
      dataCollectionFrequency,
      verificationRequired,
      verificationProcess,
      benchmarkValue,
      benchmarkSource,
      targetValue,
      targetDate,
      owner,
      status,
      tags
    } = req.body;

    // Find the metric to update
    const metricIndex = models.esgMetrics.findIndex(m => m.id === id);

    if (metricIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG metric with ID ${id} not found`
      });
    }

    // Validate framework mappings if provided
    if (frameworkMappings && Array.isArray(frameworkMappings)) {
      for (const mapping of frameworkMappings) {
        if (!mapping.frameworkId || !mapping.elementId) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'Framework mappings must include frameworkId and elementId'
          });
        }
      }
    }

    // Sanitize inputs (basic sanitization - in production, use a library like DOMPurify)
    const sanitizeInput = (input) => {
      if (typeof input !== 'string') return input;
      return input
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')
        .replace(/\//g, '&#x2F;');
    };

    // Update the metric
    const currentMetric = models.esgMetrics[metricIndex];
    const updatedMetric = {
      ...currentMetric,
      name: name !== undefined ? sanitizeInput(name) : currentMetric.name,
      description: description !== undefined ? sanitizeInput(description) : currentMetric.description,
      category: category || currentMetric.category,
      subcategory: subcategory !== undefined ? sanitizeInput(subcategory) : currentMetric.subcategory,
      unit: unit !== undefined ? sanitizeInput(unit) : currentMetric.unit,
      dataType: dataType || currentMetric.dataType,
      isStandardized: isStandardized !== undefined ? isStandardized : currentMetric.isStandardized || false,
      standardId: standardId !== undefined ? sanitizeInput(standardId) : currentMetric.standardId,
      frameworkMappings: frameworkMappings || currentMetric.frameworkMappings || [],
      calculationMethod: calculationMethod !== undefined ? sanitizeInput(calculationMethod) : currentMetric.calculationMethod,
      calculationFormula: calculationFormula !== undefined ? sanitizeInput(calculationFormula) : currentMetric.calculationFormula,
      dataCollectionFrequency: dataCollectionFrequency || currentMetric.dataCollectionFrequency || 'monthly',
      verificationRequired: verificationRequired !== undefined ? verificationRequired : currentMetric.verificationRequired || false,
      verificationProcess: verificationProcess !== undefined ? sanitizeInput(verificationProcess) : currentMetric.verificationProcess,
      benchmarkValue: benchmarkValue !== undefined ? sanitizeInput(benchmarkValue) : currentMetric.benchmarkValue,
      benchmarkSource: benchmarkSource !== undefined ? sanitizeInput(benchmarkSource) : currentMetric.benchmarkSource,
      targetValue: targetValue !== undefined ? sanitizeInput(targetValue) : currentMetric.targetValue,
      targetDate: targetDate || currentMetric.targetDate,
      owner: owner !== undefined ? sanitizeInput(owner) : currentMetric.owner,
      status: status || currentMetric.status,
      tags: tags !== undefined ? (Array.isArray(tags) ? tags.map(tag => sanitizeInput(tag)) : currentMetric.tags || []) : currentMetric.tags || [],
      updatedAt: new Date().toISOString()
    };

    // Replace the old metric with the updated one
    models.esgMetrics[metricIndex] = updatedMetric;

    // Log the update for audit purposes
    console.info(`ESG metric updated: ${updatedMetric.id} - ${updatedMetric.name} by ${req.user?.username || 'unknown'}`);

    res.json({
      data: updatedMetric,
      message: 'ESG metric updated successfully'
    });
  } catch (error) {
    handleError(error, res, 'updateMetric');
  }
};

/**
 * Delete an ESG metric
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteMetric = (req, res) => {
  try {
    const { id } = req.params;

    // Find the metric to delete
    const metricIndex = models.esgMetrics.findIndex(m => m.id === id);

    if (metricIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG metric with ID ${id} not found`
      });
    }

    // Check if there are any data points for this metric
    const hasDataPoints = models.esgDataPoints.some(dp => dp.metricId === id);

    if (hasDataPoints) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot delete metric with ID ${id} because it has associated data points`
      });
    }

    // Check if there are any initiatives using this metric
    const hasInitiatives = models.esgInitiatives.some(initiative =>
      initiative.metrics && initiative.metrics.includes(id)
    );

    if (hasInitiatives) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot delete metric with ID ${id} because it is used in one or more initiatives`
      });
    }

    // Remove the metric from the collection
    models.esgMetrics.splice(metricIndex, 1);

    res.json({
      message: 'ESG metric deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteMetric:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get data points for a specific ESG metric
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMetricDataPoints = (req, res) => {
  try {
    const { id } = req.params;
    const { startDate, endDate, period, verificationStatus } = req.query;

    // Check if the metric exists
    const metric = models.esgMetrics.find(m => m.id === id);

    if (!metric) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG metric with ID ${id} not found`
      });
    }

    // Get data points for the metric
    let dataPoints = models.esgDataPoints.filter(dp => dp.metricId === id);

    // Apply filters
    if (startDate) {
      dataPoints = dataPoints.filter(dp => dp.date >= startDate);
    }

    if (endDate) {
      dataPoints = dataPoints.filter(dp => dp.date <= endDate);
    }

    if (period) {
      dataPoints = dataPoints.filter(dp => dp.period === period);
    }

    if (verificationStatus) {
      dataPoints = dataPoints.filter(dp => dp.verificationStatus === verificationStatus);
    }

    // Sort by date (newest first)
    dataPoints.sort((a, b) => new Date(b.date) - new Date(a.date));

    res.json({ data: dataPoints });
  } catch (error) {
    console.error('Error in getMetricDataPoints:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Add a data point to an ESG metric
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const addDataPoint = (req, res) => {
  try {
    const { id } = req.params;
    const { value, date, period, source, notes, verificationStatus, verifiedBy, verifiedAt } = req.body;

    // Check if the metric exists
    const metric = models.esgMetrics.find(m => m.id === id);

    if (!metric) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG metric with ID ${id} not found`
      });
    }

    // Create a new data point
    const newDataPoint = {
      id: `esg-d-${uuidv4().substring(0, 8)}`,
      metricId: id,
      value,
      date,
      period,
      source,
      notes,
      verificationStatus,
      verifiedBy: verifiedBy || '',
      verifiedAt: verifiedAt || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the data point to the collection
    models.esgDataPoints.push(newDataPoint);

    res.status(201).json({
      data: newDataPoint,
      message: 'Data point added successfully'
    });
  } catch (error) {
    console.error('Error in addDataPoint:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of ESG initiatives
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getInitiatives = (req, res) => {
  try {
    const { page = 1, limit = 10, category, status, sortBy = 'name', sortOrder = 'asc' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter initiatives based on query parameters
    let filteredInitiatives = [...models.esgInitiatives];

    if (category) {
      filteredInitiatives = filteredInitiatives.filter(initiative => initiative.category === category);
    }

    if (status) {
      filteredInitiatives = filteredInitiatives.filter(initiative => initiative.status === status);
    }

    // Sort initiatives
    filteredInitiatives.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedInitiatives = filteredInitiatives.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalInitiatives = filteredInitiatives.length;
    const totalPages = Math.ceil(totalInitiatives / limitNum);

    res.json({
      data: paginatedInitiatives,
      pagination: {
        total: totalInitiatives,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getInitiatives:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific ESG initiative by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getInitiativeById = (req, res) => {
  try {
    const { id } = req.params;
    const initiative = models.esgInitiatives.find(i => i.id === id);

    if (!initiative) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG initiative with ID ${id} not found`
      });
    }

    res.json({ data: initiative });
  } catch (error) {
    console.error('Error in getInitiativeById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new ESG initiative
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createInitiative = (req, res) => {
  try {
    const { name, description, category, startDate, endDate, status, owner, budget, metrics, goals } = req.body;

    // Validate that all metrics exist
    if (metrics && metrics.length > 0) {
      const invalidMetrics = metrics.filter(metricId => !models.esgMetrics.some(m => m.id === metricId));

      if (invalidMetrics.length > 0) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `The following metrics do not exist: ${invalidMetrics.join(', ')}`
        });
      }
    }

    // Create a new initiative with a unique ID
    const newInitiative = {
      id: `esg-i-${uuidv4().substring(0, 8)}`,
      name,
      description,
      category,
      startDate,
      endDate,
      status,
      owner,
      budget,
      metrics: metrics || [],
      goals: goals || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new initiative to the collection
    models.esgInitiatives.push(newInitiative);

    res.status(201).json({
      data: newInitiative,
      message: 'ESG initiative created successfully'
    });
  } catch (error) {
    console.error('Error in createInitiative:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing ESG initiative
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateInitiative = (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, category, startDate, endDate, status, owner, budget, metrics, goals } = req.body;

    // Find the initiative to update
    const initiativeIndex = models.esgInitiatives.findIndex(i => i.id === id);

    if (initiativeIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG initiative with ID ${id} not found`
      });
    }

    // Validate that all metrics exist
    if (metrics && metrics.length > 0) {
      const invalidMetrics = metrics.filter(metricId => !models.esgMetrics.some(m => m.id === metricId));

      if (invalidMetrics.length > 0) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `The following metrics do not exist: ${invalidMetrics.join(', ')}`
        });
      }
    }

    // Update the initiative
    const updatedInitiative = {
      ...models.esgInitiatives[initiativeIndex],
      name: name || models.esgInitiatives[initiativeIndex].name,
      description: description || models.esgInitiatives[initiativeIndex].description,
      category: category || models.esgInitiatives[initiativeIndex].category,
      startDate: startDate || models.esgInitiatives[initiativeIndex].startDate,
      endDate: endDate || models.esgInitiatives[initiativeIndex].endDate,
      status: status || models.esgInitiatives[initiativeIndex].status,
      owner: owner || models.esgInitiatives[initiativeIndex].owner,
      budget: budget !== undefined ? budget : models.esgInitiatives[initiativeIndex].budget,
      metrics: metrics || models.esgInitiatives[initiativeIndex].metrics,
      goals: goals || models.esgInitiatives[initiativeIndex].goals,
      updatedAt: new Date().toISOString()
    };

    // Replace the old initiative with the updated one
    models.esgInitiatives[initiativeIndex] = updatedInitiative;

    res.json({
      data: updatedInitiative,
      message: 'ESG initiative updated successfully'
    });
  } catch (error) {
    console.error('Error in updateInitiative:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete an ESG initiative
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteInitiative = (req, res) => {
  try {
    const { id } = req.params;

    // Find the initiative to delete
    const initiativeIndex = models.esgInitiatives.findIndex(i => i.id === id);

    if (initiativeIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG initiative with ID ${id} not found`
      });
    }

    // Remove the initiative from the collection
    models.esgInitiatives.splice(initiativeIndex, 1);

    res.json({
      message: 'ESG initiative deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteInitiative:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get ESG categories
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getCategories = (req, res) => {
  try {
    res.json({ data: models.esgCategories });
  } catch (error) {
    console.error('Error in getCategories:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get ESG frameworks
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworks = (req, res) => {
  try {
    res.json({ data: models.esgFrameworks });
  } catch (error) {
    handleError(error, res, 'getFrameworks');
  }
};

/**
 * Get benchmark data for a specific metric
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMetricBenchmark = (req, res) => {
  try {
    const { id } = req.params;
    const { industry, region, companySize } = req.query;

    // Find the metric
    const metric = models.esgMetrics.find(m => m.id === id);

    if (!metric) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG metric with ID ${id} not found`
      });
    }

    // In a real implementation, this would fetch benchmark data from a database or external API
    // For now, we'll generate some sample benchmark data
    const benchmarkData = {
      metric: {
        id: metric.id,
        name: metric.name,
        category: metric.category,
        unit: metric.unit
      },
      currentValue: metric.benchmarkValue || null,
      industryAverage: {
        value: metric.benchmarkValue || '5000',
        source: metric.benchmarkSource || 'Industry average',
        date: '2023-12-31'
      },
      peerComparison: [
        {
          segment: 'Top Performers',
          value: (parseInt(metric.benchmarkValue || '5000') * 0.7).toString(),
          percentile: 90
        },
        {
          segment: 'Average Performers',
          value: metric.benchmarkValue || '5000',
          percentile: 50
        },
        {
          segment: 'Bottom Performers',
          value: (parseInt(metric.benchmarkValue || '5000') * 1.3).toString(),
          percentile: 10
        }
      ],
      historicalTrend: [
        { year: 2020, value: (parseInt(metric.benchmarkValue || '5000') * 1.2).toString() },
        { year: 2021, value: (parseInt(metric.benchmarkValue || '5000') * 1.1).toString() },
        { year: 2022, value: (parseInt(metric.benchmarkValue || '5000') * 1.05).toString() },
        { year: 2023, value: metric.benchmarkValue || '5000' }
      ],
      filters: {
        industry: industry || 'All',
        region: region || 'Global',
        companySize: companySize || 'All'
      },
      recommendations: [
        {
          description: `Consider implementing energy efficiency measures to reduce ${metric.name.toLowerCase()}`,
          potentialImprovement: '15%',
          difficulty: 'Medium'
        },
        {
          description: `Explore renewable energy options to offset ${metric.name.toLowerCase()}`,
          potentialImprovement: '30%',
          difficulty: 'High'
        }
      ]
    };

    res.json({ data: benchmarkData });
  } catch (error) {
    handleError(error, res, 'getMetricBenchmark');
  }
};

module.exports = {
  getMetrics,
  getMetricById,
  createMetric,
  updateMetric,
  deleteMetric,
  getMetricDataPoints,
  addDataPoint,
  getMetricBenchmark,
  getInitiatives,
  getInitiativeById,
  createInitiative,
  updateInitiative,
  deleteInitiative,
  getCategories,
  getFrameworks
};
