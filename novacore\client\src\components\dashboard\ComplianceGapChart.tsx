/**
 * Compliance Gap Chart Component
 * 
 * This component displays a chart showing compliance gaps across different frameworks.
 */

import React from 'react';

interface ComplianceGapChartProps {
  data: Array<{
    framework: string;
    compliant: number;
    gap: number;
  }>;
}

export const ComplianceGapChart: React.FC<ComplianceGapChartProps> = ({ data }) => {
  return (
    <div className="space-y-4">
      {data.map((item, index) => (
        <div key={index} className="space-y-1">
          <div className="flex justify-between text-sm">
            <span>{item.framework}</span>
            <span>{item.compliant}%</span>
          </div>
          <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
            <div 
              className="h-full bg-blue-600 rounded-full" 
              style={{ width: `${item.compliant}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500">
            <span>Compliant</span>
            <span>Gap: {item.gap}%</span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ComplianceGapChart;
