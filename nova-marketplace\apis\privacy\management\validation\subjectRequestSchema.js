/**
 * Data Subject Request Validation Schema
 * 
 * This file defines the validation schema for data subject requests.
 */

const Joi = require('joi');

// Create schema
const createSubjectRequestSchema = Joi.object({
  requestType: Joi.string()
    .valid('access', 'rectification', 'erasure', 'restriction', 'portability', 'objection', 'automated_decision')
    .required()
    .messages({
      'any.required': 'Request type is required',
      'string.empty': 'Request type cannot be empty',
      'any.only': 'Request type must be one of: access, rectification, erasure, restriction, portability, objection, automated_decision'
    }),
  
  dataSubjectId: Joi.string()
    .allow(null, '')
    .messages({
      'string.base': 'Data subject ID must be a string'
    }),
  
  dataSubjectName: Joi.string()
    .required()
    .messages({
      'any.required': 'Data subject name is required',
      'string.empty': 'Data subject name cannot be empty'
    }),
  
  dataSubjectEmail: Joi.string()
    .email()
    .required()
    .messages({
      'any.required': 'Data subject email is required',
      'string.empty': 'Data subject email cannot be empty',
      'string.email': 'Data subject email must be a valid email address'
    }),
  
  dataSubjectPhone: Joi.string()
    .allow(null, '')
    .pattern(/^\+?[0-9]{10,15}$/)
    .messages({
      'string.pattern.base': 'Data subject phone must be a valid phone number'
    }),
  
  requestDetails: Joi.string()
    .required()
    .messages({
      'any.required': 'Request details are required',
      'string.empty': 'Request details cannot be empty'
    }),
  
  status: Joi.string()
    .valid('pending', 'in-progress', 'completed', 'rejected', 'withdrawn')
    .default('pending')
    .messages({
      'any.only': 'Status must be one of: pending, in-progress, completed, rejected, withdrawn'
    }),
  
  assignedTo: Joi.string()
    .allow(null, '')
    .messages({
      'string.base': 'Assigned to must be a string'
    }),
  
  verificationMethod: Joi.string()
    .valid('email', 'id_verification', 'other')
    .allow(null, '')
    .messages({
      'any.only': 'Verification method must be one of: email, id_verification, other'
    }),
  
  verificationStatus: Joi.string()
    .valid('pending', 'verified', 'failed')
    .default('pending')
    .messages({
      'any.only': 'Verification status must be one of: pending, verified, failed'
    }),
  
  verificationDetails: Joi.string()
    .allow(null, '')
    .messages({
      'string.base': 'Verification details must be a string'
    }),
  
  affectedSystems: Joi.array()
    .items(Joi.object({
      systemId: Joi.string().required(),
      systemName: Joi.string().required(),
      status: Joi.string().valid('pending', 'in-progress', 'completed', 'failed').default('pending'),
      details: Joi.string().allow(null, '')
    }))
    .default([])
    .messages({
      'array.base': 'Affected systems must be an array'
    }),
  
  responseDetails: Joi.string()
    .allow(null, '')
    .messages({
      'string.base': 'Response details must be a string'
    }),
  
  responseDate: Joi.date()
    .allow(null)
    .messages({
      'date.base': 'Response date must be a valid date'
    }),
  
  responseMethod: Joi.string()
    .valid('email', 'mail', 'phone', 'in-person')
    .allow(null, '')
    .messages({
      'any.only': 'Response method must be one of: email, mail, phone, in-person'
    }),
  
  responseAttachments: Joi.array()
    .items(Joi.object({
      name: Joi.string().required(),
      type: Joi.string().required(),
      url: Joi.string().required()
    }))
    .default([])
    .messages({
      'array.base': 'Response attachments must be an array'
    }),
  
  notes: Joi.array()
    .items(Joi.object({
      content: Joi.string().required(),
      createdBy: Joi.string().required(),
      createdAt: Joi.date().default(Date.now)
    }))
    .default([])
    .messages({
      'array.base': 'Notes must be an array'
    }),
  
  dueDate: Joi.date()
    .messages({
      'date.base': 'Due date must be a valid date'
    }),
  
  completedDate: Joi.date()
    .allow(null)
    .messages({
      'date.base': 'Completed date must be a valid date'
    })
});

// Update schema
const updateSubjectRequestSchema = createSubjectRequestSchema.fork(
  ['requestType', 'dataSubjectName', 'dataSubjectEmail', 'requestDetails', 'dueDate'],
  (schema) => schema.optional()
);

module.exports = {
  createSubjectRequestSchema,
  updateSubjectRequestSchema
};
