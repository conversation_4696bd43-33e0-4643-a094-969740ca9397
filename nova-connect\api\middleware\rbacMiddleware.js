/**
 * RBAC Middleware
 * 
 * This middleware provides role-based access control for the API.
 */

const RBACService = require('../services/RBACService');
const { AuthorizationError } = require('../utils/errors');
const logger = require('../../config/logger');

// Initialize RBAC service
const rbacService = new RBACService();

/**
 * Check if user has permission
 * 
 * @param {string|string[]} requiredPermission - Required permission(s)
 * @param {Object} options - Options
 * @param {boolean} options.all - If true, user must have all permissions (default: false)
 * @param {string} options.scope - Scope to check permissions in (default: 'global')
 * @param {string} options.scopeIdParam - Request parameter to get scope ID from
 * @param {Function} options.getScopeId - Function to get scope ID from request
 * @returns {Function} - Express middleware
 */
const hasPermission = (requiredPermission, options = {}) => {
  return async (req, res, next) => {
    try {
      // Check if user exists
      if (!req.user) {
        throw new AuthorizationError('User not authenticated');
      }
      
      const userId = req.user.id;
      
      // Get scope ID if provided
      let scopeId = null;
      
      if (options.scope && options.scope !== 'global') {
        if (options.getScopeId) {
          scopeId = options.getScopeId(req);
        } else if (options.scopeIdParam) {
          scopeId = req.params[options.scopeIdParam];
        }
      }
      
      // Convert single permission to array
      const permissions = Array.isArray(requiredPermission) 
        ? requiredPermission 
        : [requiredPermission];
      
      // Check if user has permission(s)
      if (options.all) {
        // User must have all permissions
        for (const permission of permissions) {
          const hasPermission = await rbacService.hasPermission(userId, permission);
          
          if (!hasPermission) {
            throw new AuthorizationError(`User does not have required permission: ${permission}`);
          }
        }
      } else {
        // User must have at least one permission
        let hasAnyPermission = false;
        
        for (const permission of permissions) {
          const hasPermission = await rbacService.hasPermission(userId, permission);
          
          if (hasPermission) {
            hasAnyPermission = true;
            break;
          }
        }
        
        if (!hasAnyPermission) {
          throw new AuthorizationError(`User does not have any of the required permissions: ${permissions.join(', ')}`);
        }
      }
      
      next();
    } catch (error) {
      logger.error('RBAC authorization error', { 
        userId: req.user?.id, 
        requiredPermission, 
        error: error.message 
      });
      
      if (error instanceof AuthorizationError) {
        return res.status(403).json({
          error: 'Forbidden',
          message: error.message
        });
      }
      
      next(error);
    }
  };
};

/**
 * Check if user has role
 * 
 * @param {string|string[]} requiredRole - Required role(s)
 * @param {Object} options - Options
 * @param {boolean} options.all - If true, user must have all roles (default: false)
 * @param {string} options.scope - Scope to check roles in (default: 'global')
 * @param {string} options.scopeIdParam - Request parameter to get scope ID from
 * @param {Function} options.getScopeId - Function to get scope ID from request
 * @returns {Function} - Express middleware
 */
const hasRole = (requiredRole, options = {}) => {
  return async (req, res, next) => {
    try {
      // Check if user exists
      if (!req.user) {
        throw new AuthorizationError('User not authenticated');
      }
      
      const userId = req.user.id;
      
      // Get user roles
      const userRoles = await rbacService.getUserRoles(userId);
      const userRoleNames = userRoles.map(role => role.name);
      
      // Convert single role to array
      const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
      
      // Check if user has role(s)
      if (options.all) {
        // User must have all roles
        for (const role of roles) {
          if (!userRoleNames.includes(role)) {
            throw new AuthorizationError(`User does not have required role: ${role}`);
          }
        }
      } else {
        // User must have at least one role
        let hasAnyRole = false;
        
        for (const role of roles) {
          if (userRoleNames.includes(role)) {
            hasAnyRole = true;
            break;
          }
        }
        
        if (!hasAnyRole) {
          throw new AuthorizationError(`User does not have any of the required roles: ${roles.join(', ')}`);
        }
      }
      
      next();
    } catch (error) {
      logger.error('RBAC authorization error', { 
        userId: req.user?.id, 
        requiredRole, 
        error: error.message 
      });
      
      if (error instanceof AuthorizationError) {
        return res.status(403).json({
          error: 'Forbidden',
          message: error.message
        });
      }
      
      next(error);
    }
  };
};

/**
 * Check if user is an admin
 */
const isAdmin = (req, res, next) => {
  return hasRole('Administrator')(req, res, next);
};

module.exports = {
  hasPermission,
  hasRole,
  isAdmin
};
