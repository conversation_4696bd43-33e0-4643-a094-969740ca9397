/**
 * DomainUniverse.js
 * 
 * This module implements the concept of a Domain Universe - a containerized
 * domain with its own physics, boundaries, and finite possibility space.
 * 
 * Each domain (Biological, Financial, Cyber/GRC) is treated as a distinct
 * "containerized universe" within the Comphyological framework, with its
 * own internal physics and dynamics.
 */

const { FiniteUniverse, DomainContainer } = require('../core/FiniteUniverse');
const { v4: uuidv4 } = require('uuid');

/**
 * Represents a finite possibility space within a domain
 */
class FinitePossibilitySpace {
  /**
   * Create a new finite possibility space
   * @param {Object} boundaries - The boundaries of the possibility space
   */
  constructor(boundaries) {
    this.boundaries = boundaries;
    this.dimensions = Object.keys(boundaries);
    this.volume = this.calculateVolume();
  }
  
  /**
   * Calculate the volume (size) of the possibility space
   * @returns {number} - The volume of the possibility space
   */
  calculateVolume() {
    let volume = 1;
    
    for (const dimension of this.dimensions) {
      const boundary = this.boundaries[dimension];
      const range = boundary.max - boundary.min;
      volume *= range;
    }
    
    // Ensure volume is finite
    return FiniteUniverse.enforceBoundaries(volume);
  }
  
  /**
   * Check if a state is contained within the possibility space
   * @param {Object} state - The state to check
   * @returns {boolean} - True if the state is within the possibility space
   */
  contains(state) {
    for (const dimension of this.dimensions) {
      if (state[dimension] === undefined) {
        continue; // Skip dimensions not present in the state
      }
      
      const value = state[dimension];
      const boundary = this.boundaries[dimension];
      
      if (value < boundary.min || value > boundary.max) {
        return false;
      }
    }
    
    return true;
  }
  
  /**
   * Get a random valid state within the possibility space
   * @returns {Object} - A random valid state
   */
  getRandomState() {
    const state = {};
    
    for (const dimension of this.dimensions) {
      const boundary = this.boundaries[dimension];
      const range = boundary.max - boundary.min;
      state[dimension] = boundary.min + (Math.random() * range);
    }
    
    return state;
  }
  
  /**
   * Calculate the distance between a state and the boundaries
   * @param {Object} state - The state to check
   * @returns {Object} - The distance to each boundary
   */
  distanceToBoundaries(state) {
    const distances = {};
    
    for (const dimension of this.dimensions) {
      if (state[dimension] === undefined) {
        continue; // Skip dimensions not present in the state
      }
      
      const value = state[dimension];
      const boundary = this.boundaries[dimension];
      
      distances[dimension] = {
        min: value - boundary.min,
        max: boundary.max - value
      };
    }
    
    return distances;
  }
}

/**
 * Represents a Domain Universe - a containerized domain with its own physics
 */
class DomainUniverse {
  /**
   * Create a new Domain Universe
   * @param {string} name - The name of the domain
   * @param {Object} boundaries - The boundaries of the domain
   * @param {Object} physics - The physics rules of the domain
   */
  constructor(name, boundaries, physics = {}) {
    this.id = uuidv4();
    this.name = name;
    this.boundaries = boundaries;
    this.physics = {
      entropyRate: 0.01, // Default entropy increase rate
      resonanceThreshold: 0.7, // Minimum resonance for stability
      healingRate: 0.05, // Rate at which the domain heals
      ...physics
    };
    
    // Create the possibility space
    this.possibilitySpace = new FinitePossibilitySpace(boundaries);
    
    // Create the domain container
    this.container = new DomainContainer(name, boundaries);
    
    // Initialize domain state
    this.state = {
      entropy: 0, // Start with zero entropy
      resonance: 1, // Start with perfect resonance
      stability: 1, // Start with perfect stability
      creationTime: Date.now(),
      lastUpdateTime: Date.now(),
      entities: new Map(), // Entities within the domain
      events: [] // Events that have occurred in the domain
    };
    
    console.log(`Domain Universe "${name}" created with ${this.possibilitySpace.dimensions.length} dimensions and possibility space volume of ${this.possibilitySpace.volume}`);
  }
  
  /**
   * Check if a state is valid within this domain
   * @param {Object} state - The state to check
   * @returns {boolean} - True if the state is valid
   */
  isValidState(state) {
    // Check if state is within the possibility space
    if (!this.possibilitySpace.contains(state)) {
      return false;
    }
    
    // Check if state respects the domain's physics
    if (!this.respectsPhysics(state)) {
      return false;
    }
    
    return true;
  }
  
  /**
   * Check if a state respects the domain's physics
   * @param {Object} state - The state to check
   * @returns {boolean} - True if the state respects the physics
   */
  respectsPhysics(state) {
    // Implementation would depend on the specific physics of the domain
    // For now, a simple implementation
    return true;
  }
  
  /**
   * Add an entity to the domain
   * @param {string} id - The entity ID
   * @param {Object} entity - The entity to add
   * @returns {Object} - The added entity
   */
  addEntity(id, entity) {
    // Validate entity state
    if (!this.isValidState(entity)) {
      throw new Error(`Entity state is not valid within the ${this.name} domain`);
    }
    
    // Add entity to container with bounded values
    const boundedEntity = this.container.add(id, entity);
    
    // Add to entities map
    this.state.entities.set(id, boundedEntity);
    
    // Record event
    this.recordEvent('ENTITY_ADDED', { id, entity: boundedEntity });
    
    return boundedEntity;
  }
  
  /**
   * Get an entity from the domain
   * @param {string} id - The entity ID
   * @returns {Object} - The entity
   */
  getEntity(id) {
    return this.state.entities.get(id);
  }
  
  /**
   * Update an entity in the domain
   * @param {string} id - The entity ID
   * @param {Object} updates - The updates to apply
   * @returns {Object} - The updated entity
   */
  updateEntity(id, updates) {
    const entity = this.getEntity(id);
    
    if (!entity) {
      throw new Error(`Entity ${id} not found in ${this.name} domain`);
    }
    
    // Create updated entity
    const updatedEntity = { ...entity, ...updates };
    
    // Validate updated state
    if (!this.isValidState(updatedEntity)) {
      throw new Error(`Updated entity state is not valid within the ${this.name} domain`);
    }
    
    // Update entity with bounded values
    const boundedEntity = this.container.add(id, updatedEntity);
    
    // Record event
    this.recordEvent('ENTITY_UPDATED', { id, updates, entity: boundedEntity });
    
    return boundedEntity;
  }
  
  /**
   * Remove an entity from the domain
   * @param {string} id - The entity ID
   * @returns {boolean} - True if the entity was removed
   */
  removeEntity(id) {
    const entity = this.getEntity(id);
    
    if (!entity) {
      return false;
    }
    
    // Remove from entities map
    this.state.entities.delete(id);
    
    // Record event
    this.recordEvent('ENTITY_REMOVED', { id, entity });
    
    return true;
  }
  
  /**
   * Record an event in the domain
   * @param {string} type - The event type
   * @param {Object} data - The event data
   */
  recordEvent(type, data) {
    const event = {
      id: uuidv4(),
      type,
      data,
      timestamp: Date.now()
    };
    
    this.state.events.push(event);
    
    // Limit events history
    if (this.state.events.length > 1000) {
      this.state.events.shift();
    }
  }
  
  /**
   * Update the domain state
   * @param {number} deltaTime - The time elapsed since the last update
   */
  update(deltaTime) {
    const now = Date.now();
    const dt = deltaTime || (now - this.state.lastUpdateTime) / 1000; // Convert to seconds
    
    // Apply entropy increase based on physics
    this.state.entropy += this.physics.entropyRate * dt;
    
    // Apply healing based on physics
    if (this.state.resonance < 1) {
      this.state.resonance += this.physics.healingRate * dt;
      this.state.resonance = Math.min(this.state.resonance, 1);
    }
    
    // Update stability based on resonance and entropy
    this.state.stability = this.state.resonance * (1 - this.state.entropy);
    
    // Enforce boundaries on state values
    this.state.entropy = Math.max(0, Math.min(this.state.entropy, 1));
    this.state.resonance = Math.max(0, Math.min(this.state.resonance, 1));
    this.state.stability = Math.max(0, Math.min(this.state.stability, 1));
    
    // Update last update time
    this.state.lastUpdateTime = now;
    
    // Record event
    this.recordEvent('DOMAIN_UPDATED', { 
      deltaTime: dt, 
      entropy: this.state.entropy,
      resonance: this.state.resonance,
      stability: this.state.stability
    });
  }
  
  /**
   * Get the current state of the domain
   * @returns {Object} - The domain state
   */
  getState() {
    return {
      id: this.id,
      name: this.name,
      entropy: this.state.entropy,
      resonance: this.state.resonance,
      stability: this.state.stability,
      entityCount: this.state.entities.size,
      eventCount: this.state.events.length,
      lastUpdateTime: this.state.lastUpdateTime,
      age: (Date.now() - this.state.creationTime) / 1000 // Age in seconds
    };
  }
}

module.exports = {
  DomainUniverse,
  FinitePossibilitySpace
};
