import React, { useRef, useEffect, useState, useCallback } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import './TrinityVisualization.css';

/**
 * Trinity Visualization Component
 *
 * This component creates a 3D visualization of the "wheels within wheels" concept,
 * showing the nested Trinities in the NovaFuse architecture.
 */
const TrinityVisualization = ({ data, width = '100%', height = '600px', onParticleSelect }) => {
  const mountRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [selectedParticle, setSelectedParticle] = useState(null);
  const [hoveredParticle, setHoveredParticle] = useState(null);
  const [selectedWheel, setSelectedWheel] = useState('all');

  const sceneRef = useRef(null);
  const rendererRef = useRef(null);
  const cameraRef = useRef(null);
  const controlsRef = useRef(null);
  const raycasterRef = useRef(null);
  const mouseRef = useRef(new THREE.Vector2());

  const wheelsRef = useRef({
    outer: null,
    middle: null,
    inner: null
  });
  const connectorsRef = useRef([]);
  const particlesRef = useRef([]);

  // Callback for particle transition events
  const onParticleTransition = useCallback((transitionData) => {
    // This would be used to notify parent components about particle transitions
    if (data && data.onParticleTransition) {
      data.onParticleTransition(transitionData);
    }
  }, [data]);

  // Constants for the visualization
  const COLORS = {
    BACKGROUND: 0x111133,
    OUTER_WHEEL: 0x4CAF50, // Green
    MIDDLE_WHEEL: 0x2196F3, // Blue
    INNER_WHEEL: 0x9C27B0, // Purple
    CONNECTORS: 0xFFFFFF,
    PARTICLES: [0xF44336, 0xFF9800, 0xFFEB3B] // Red, Orange, Yellow
  };

  const WHEEL_PROPERTIES = {
    OUTER: {
      RADIUS: 10,
      TUBE: 0.5,
      SEGMENTS: 100,
      ROTATION_SPEED: 0.001
    },
    MIDDLE: {
      RADIUS: 6,
      TUBE: 0.4,
      SEGMENTS: 80,
      ROTATION_SPEED: 0.002
    },
    INNER: {
      RADIUS: 3,
      TUBE: 0.3,
      SEGMENTS: 60,
      ROTATION_SPEED: 0.003
    }
  };

  // Initialize the scene
  useEffect(() => {
    if (isInitialized || !mountRef.current) return;

    // Create scene
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(COLORS.BACKGROUND);
    sceneRef.current = scene;

    // Create camera
    const camera = new THREE.PerspectiveCamera(75, mountRef.current.clientWidth / mountRef.current.clientHeight, 0.1, 1000);
    camera.position.z = 20;
    cameraRef.current = camera;

    // Create renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
    mountRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Add controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controlsRef.current = controls;

    // Add ambient light
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);

    // Add directional light
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(10, 10, 10);
    scene.add(directionalLight);

    // Initialize raycaster for interaction
    const raycaster = new THREE.Raycaster();
    raycasterRef.current = raycaster;

    // Add mouse event listeners
    const handleMouseMove = (event) => {
      // Calculate mouse position in normalized device coordinates
      const rect = renderer.domElement.getBoundingClientRect();
      mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      // Update raycaster
      raycaster.setFromCamera(mouseRef.current, camera);

      // Get intersected objects
      const interactableParticles = particlesRef.current
        .filter(p => p.interactable)
        .map(p => p.mesh);

      const intersects = raycaster.intersectObjects(interactableParticles);

      // Handle hover state
      if (intersects.length > 0) {
        const intersectedMesh = intersects[0].object;
        const hoveredParticleData = particlesRef.current.find(p => p.mesh === intersectedMesh);

        if (hoveredParticleData && hoveredParticleData !== hoveredParticle) {
          // Set hovered particle
          setHoveredParticle(hoveredParticleData);

          // Update particle appearance
          intersectedMesh.scale.multiplyScalar(1.2);

          // Change cursor
          renderer.domElement.style.cursor = 'pointer';
        }
      } else if (hoveredParticle) {
        // Reset hovered particle
        hoveredParticle.mesh.scale.set(1, 1, 1);
        setHoveredParticle(null);

        // Reset cursor
        renderer.domElement.style.cursor = 'auto';
      }
    };

    const handleMouseClick = (event) => {
      // Update raycaster
      raycaster.setFromCamera(mouseRef.current, camera);

      // Get intersected objects
      const interactableParticles = particlesRef.current
        .filter(p => p.interactable)
        .map(p => p.mesh);

      const intersects = raycaster.intersectObjects(interactableParticles);

      // Handle click
      if (intersects.length > 0) {
        const intersectedMesh = intersects[0].object;
        const clickedParticleData = particlesRef.current.find(p => p.mesh === intersectedMesh);

        if (clickedParticleData) {
          // Set selected particle
          setSelectedParticle(clickedParticleData);

          // Notify parent component
          if (onParticleSelect) {
            onParticleSelect({
              id: clickedParticleData.data.id,
              type: clickedParticleData.type,
              aspect: clickedParticleData.aspect,
              wheel: clickedParticleData.startWheel,
              value: clickedParticleData.data.value,
              timestamp: clickedParticleData.data.timestamp
            });
          }

          // Update particle appearance
          intersectedMesh.scale.multiplyScalar(1.5);

          // Highlight particle
          const originalMaterial = intersectedMesh.material;
          intersectedMesh.material = new THREE.MeshPhongMaterial({
            color: originalMaterial.color,
            emissive: 0xffffff,
            emissiveIntensity: 0.5,
            transparent: true,
            opacity: 1.0
          });

          // Reset after a delay
          setTimeout(() => {
            intersectedMesh.scale.set(1, 1, 1);
            intersectedMesh.material = originalMaterial;
          }, 1000);
        }
      } else {
        // Deselect particle
        setSelectedParticle(null);
      }
    };

    // Add event listeners
    renderer.domElement.addEventListener('mousemove', handleMouseMove);
    renderer.domElement.addEventListener('click', handleMouseClick);

    // Create wheels
    createWheels();

    // Create connectors between wheels
    createConnectors();

    // Create particles
    createParticles();

    // Add wheel selection controls
    const handleWheelSelection = (wheel) => {
      setSelectedWheel(wheel);

      // Update wheel visibility
      if (wheel === 'all') {
        if (wheelsRef.current.outer) wheelsRef.current.outer.visible = true;
        if (wheelsRef.current.middle) wheelsRef.current.middle.visible = true;
        if (wheelsRef.current.inner) wheelsRef.current.inner.visible = true;
      } else {
        if (wheelsRef.current.outer) wheelsRef.current.outer.visible = wheel === 'outer';
        if (wheelsRef.current.middle) wheelsRef.current.middle.visible = wheel === 'middle';
        if (wheelsRef.current.inner) wheelsRef.current.inner.visible = wheel === 'inner';
      }
    };

    // Handle window resize
    const handleResize = () => {
      if (!mountRef.current) return;

      const width = mountRef.current.clientWidth;
      const height = mountRef.current.clientHeight;

      cameraRef.current.aspect = width / height;
      cameraRef.current.updateProjectionMatrix();
      rendererRef.current.setSize(width, height);
    };

    window.addEventListener('resize', handleResize);

    // Animation loop
    const animate = () => {
      requestAnimationFrame(animate);

      // Rotate wheels
      if (wheelsRef.current.outer) {
        wheelsRef.current.outer.rotation.x += WHEEL_PROPERTIES.OUTER.ROTATION_SPEED;
        wheelsRef.current.outer.rotation.y += WHEEL_PROPERTIES.OUTER.ROTATION_SPEED * 0.5;
      }

      if (wheelsRef.current.middle) {
        wheelsRef.current.middle.rotation.x += WHEEL_PROPERTIES.MIDDLE.ROTATION_SPEED;
        wheelsRef.current.middle.rotation.z += WHEEL_PROPERTIES.MIDDLE.ROTATION_SPEED * 0.7;
      }

      if (wheelsRef.current.inner) {
        wheelsRef.current.inner.rotation.y += WHEEL_PROPERTIES.INNER.ROTATION_SPEED;
        wheelsRef.current.inner.rotation.z += WHEEL_PROPERTIES.INNER.ROTATION_SPEED * 0.3;
      }

      // Animate particles
      animateParticles();

      // Update controls
      controlsRef.current.update();

      // Render scene
      rendererRef.current.render(sceneRef.current, cameraRef.current);
    };

    animate();

    setIsInitialized(true);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);

      // Remove mouse event listeners
      if (rendererRef.current && rendererRef.current.domElement) {
        rendererRef.current.domElement.removeEventListener('mousemove', handleMouseMove);
        rendererRef.current.domElement.removeEventListener('click', handleMouseClick);
      }

      if (mountRef.current && rendererRef.current) {
        mountRef.current.removeChild(rendererRef.current.domElement);
      }

      // Dispose of resources
      if (wheelsRef.current.outer) {
        wheelsRef.current.outer.geometry.dispose();
        wheelsRef.current.outer.material.dispose();
      }

      if (wheelsRef.current.middle) {
        wheelsRef.current.middle.geometry.dispose();
        wheelsRef.current.middle.material.dispose();
      }

      if (wheelsRef.current.inner) {
        wheelsRef.current.inner.geometry.dispose();
        wheelsRef.current.inner.material.dispose();
      }

      connectorsRef.current.forEach(connector => {
        connector.geometry.dispose();
        connector.material.dispose();
      });

      particlesRef.current.forEach(particle => {
        if (particle.mesh) {
          if (particle.mesh.geometry) particle.mesh.geometry.dispose();
          if (particle.mesh.material) particle.mesh.material.dispose();
        }

        if (particle.trail) {
          particle.trail.forEach(segment => {
            if (segment.geometry) segment.geometry.dispose();
            if (segment.material) segment.material.dispose();
          });
        }
      });
    };
  }, [isInitialized]);

  // Create the three wheels
  const createWheels = () => {
    // Create outer wheel (Ripple Effect Layers)
    const outerGeometry = new THREE.TorusGeometry(
      WHEEL_PROPERTIES.OUTER.RADIUS,
      WHEEL_PROPERTIES.OUTER.TUBE,
      WHEEL_PROPERTIES.OUTER.SEGMENTS,
      WHEEL_PROPERTIES.OUTER.SEGMENTS
    );
    const outerMaterial = new THREE.MeshPhongMaterial({
      color: COLORS.OUTER_WHEEL,
      transparent: true,
      opacity: 0.8,
      side: THREE.DoubleSide
    });
    const outerWheel = new THREE.Mesh(outerGeometry, outerMaterial);
    sceneRef.current.add(outerWheel);
    wheelsRef.current.outer = outerWheel;

    // Create middle wheel (Mathematical Constants)
    const middleGeometry = new THREE.TorusGeometry(
      WHEEL_PROPERTIES.MIDDLE.RADIUS,
      WHEEL_PROPERTIES.MIDDLE.TUBE,
      WHEEL_PROPERTIES.MIDDLE.SEGMENTS,
      WHEEL_PROPERTIES.MIDDLE.SEGMENTS
    );
    const middleMaterial = new THREE.MeshPhongMaterial({
      color: COLORS.MIDDLE_WHEEL,
      transparent: true,
      opacity: 0.8,
      side: THREE.DoubleSide
    });
    const middleWheel = new THREE.Mesh(middleGeometry, middleMaterial);
    middleWheel.rotation.x = Math.PI / 2; // Rotate to be perpendicular to outer wheel
    sceneRef.current.add(middleWheel);
    wheelsRef.current.middle = middleWheel;

    // Create inner wheel (Implementation Patterns)
    const innerGeometry = new THREE.TorusGeometry(
      WHEEL_PROPERTIES.INNER.RADIUS,
      WHEEL_PROPERTIES.INNER.TUBE,
      WHEEL_PROPERTIES.INNER.SEGMENTS,
      WHEEL_PROPERTIES.INNER.SEGMENTS
    );
    const innerMaterial = new THREE.MeshPhongMaterial({
      color: COLORS.INNER_WHEEL,
      transparent: true,
      opacity: 0.8,
      side: THREE.DoubleSide
    });
    const innerWheel = new THREE.Mesh(innerGeometry, innerMaterial);
    innerWheel.rotation.y = Math.PI / 2; // Rotate to be perpendicular to middle wheel
    sceneRef.current.add(innerWheel);
    wheelsRef.current.inner = innerWheel;

    // Add Trinity nodes to each wheel
    addTrinityNodes();
  };

  // Add Trinity nodes to each wheel
  const addTrinityNodes = () => {
    // Outer wheel nodes (Ripple Effect Layers)
    const outerNodes = [
      { name: 'Direct Impact', angle: 0, color: 0xF44336 },
      { name: 'Adjacent Resonance', angle: Math.PI * 2/3, color: 0xFF9800 },
      { name: 'Field Saturation', angle: Math.PI * 4/3, color: 0xFFEB3B }
    ];

    outerNodes.forEach(node => {
      const nodeGeometry = new THREE.SphereGeometry(0.8, 32, 32);
      const nodeMaterial = new THREE.MeshPhongMaterial({ color: node.color });
      const nodeMesh = new THREE.Mesh(nodeGeometry, nodeMaterial);

      const x = Math.cos(node.angle) * WHEEL_PROPERTIES.OUTER.RADIUS;
      const y = Math.sin(node.angle) * WHEEL_PROPERTIES.OUTER.RADIUS;
      nodeMesh.position.set(x, y, 0);

      wheelsRef.current.outer.add(nodeMesh);
    });

    // Middle wheel nodes (Mathematical Constants)
    const middleNodes = [
      { name: 'Pi (Governance)', angle: 0, color: 0x4CAF50 },
      { name: 'Phi (Detection)', angle: Math.PI * 2/3, color: 0x2196F3 },
      { name: 'e (Response)', angle: Math.PI * 4/3, color: 0x9C27B0 }
    ];

    middleNodes.forEach(node => {
      const nodeGeometry = new THREE.SphereGeometry(0.6, 32, 32);
      const nodeMaterial = new THREE.MeshPhongMaterial({ color: node.color });
      const nodeMesh = new THREE.Mesh(nodeGeometry, nodeMaterial);

      const x = 0;
      const y = Math.cos(node.angle) * WHEEL_PROPERTIES.MIDDLE.RADIUS;
      const z = Math.sin(node.angle) * WHEEL_PROPERTIES.MIDDLE.RADIUS;
      nodeMesh.position.set(x, y, z);

      wheelsRef.current.middle.add(nodeMesh);
    });

    // Inner wheel nodes (Implementation Patterns)
    const innerNodes = [
      { name: 'Quantum State Vectors', angle: 0, color: 0xE91E63 },
      { name: 'Resonance Patterns', angle: Math.PI * 2/3, color: 0x00BCD4 },
      { name: 'Field Matrices', angle: Math.PI * 4/3, color: 0x8BC34A }
    ];

    innerNodes.forEach(node => {
      const nodeGeometry = new THREE.SphereGeometry(0.4, 32, 32);
      const nodeMaterial = new THREE.MeshPhongMaterial({ color: node.color });
      const nodeMesh = new THREE.Mesh(nodeGeometry, nodeMaterial);

      const x = Math.cos(node.angle) * WHEEL_PROPERTIES.INNER.RADIUS;
      const z = Math.sin(node.angle) * WHEEL_PROPERTIES.INNER.RADIUS;
      const y = 0;
      nodeMesh.position.set(x, y, z);

      wheelsRef.current.inner.add(nodeMesh);
    });
  };

  // Create connectors between wheels
  const createConnectors = () => {
    // Create a connector from outer to middle wheel
    const outerToMiddleGeometry = new THREE.CylinderGeometry(0.1, 0.1, WHEEL_PROPERTIES.OUTER.RADIUS - WHEEL_PROPERTIES.MIDDLE.RADIUS, 8);
    const connectorMaterial = new THREE.MeshPhongMaterial({
      color: COLORS.CONNECTORS,
      transparent: true,
      opacity: 0.3
    });

    const outerToMiddleConnector = new THREE.Mesh(outerToMiddleGeometry, connectorMaterial);
    outerToMiddleConnector.position.set(
      (WHEEL_PROPERTIES.OUTER.RADIUS + WHEEL_PROPERTIES.MIDDLE.RADIUS) / 2,
      0,
      0
    );
    outerToMiddleConnector.rotation.z = Math.PI / 2;
    sceneRef.current.add(outerToMiddleConnector);
    connectorsRef.current.push(outerToMiddleConnector);

    // Create a connector from middle to inner wheel
    const middleToInnerGeometry = new THREE.CylinderGeometry(0.1, 0.1, WHEEL_PROPERTIES.MIDDLE.RADIUS - WHEEL_PROPERTIES.INNER.RADIUS, 8);

    const middleToInnerConnector = new THREE.Mesh(middleToInnerGeometry, connectorMaterial);
    middleToInnerConnector.position.set(
      0,
      (WHEEL_PROPERTIES.MIDDLE.RADIUS + WHEEL_PROPERTIES.INNER.RADIUS) / 2,
      0
    );
    sceneRef.current.add(middleToInnerConnector);
    connectorsRef.current.push(middleToInnerConnector);
  };

  // Create particles that flow between wheels
  const createParticles = () => {
    const particleCount = 100; // Increased particle count

    // Define particle types
    const particleTypes = [
      { type: 'threat', color: 0xF44336, size: 0.12, trailLength: 5 },
      { type: 'compliance', color: 0x4CAF50, size: 0.10, trailLength: 3 },
      { type: 'decision', color: 0x2196F3, size: 0.14, trailLength: 7 }
    ];

    // Define Trinity aspects
    const trinityAspects = [
      { aspect: 'pi', color: 0xE91E63, constant: Math.PI }, // Governance (Father)
      { aspect: 'phi', color: 0x00BCD4, constant: 0.618033988749895 }, // Detection (Son)
      { aspect: 'e', color: 0x8BC34A, constant: Math.E } // Response (Spirit)
    ];

    for (let i = 0; i < particleCount; i++) {
      // Determine particle type and aspect
      const typeIndex = Math.floor(Math.random() * particleTypes.length);
      const aspectIndex = Math.floor(Math.random() * trinityAspects.length);
      const particleType = particleTypes[typeIndex];
      const trinityAspect = trinityAspects[aspectIndex];

      // Create particle geometry
      const particleGeometry = new THREE.SphereGeometry(particleType.size, 12, 12);

      // Create particle material with combined color
      const baseColor = new THREE.Color(particleType.color);
      const aspectColor = new THREE.Color(trinityAspect.color);
      const mixedColor = new THREE.Color().addColors(baseColor, aspectColor).multiplyScalar(0.5);

      const particleMaterial = new THREE.MeshPhongMaterial({
        color: mixedColor,
        transparent: true,
        opacity: 0.8,
        emissive: trinityAspect.color,
        emissiveIntensity: 0.3,
        shininess: 80
      });

      // Create particle mesh
      const particle = new THREE.Mesh(particleGeometry, particleMaterial);

      // Create particle trail
      const trail = [];
      for (let t = 0; t < particleType.trailLength; t++) {
        const trailGeometry = new THREE.SphereGeometry(particleType.size * (1 - t / particleType.trailLength), 8, 8);
        const trailMaterial = new THREE.MeshPhongMaterial({
          color: mixedColor,
          transparent: true,
          opacity: 0.5 * (1 - t / particleType.trailLength),
          emissive: trinityAspect.color,
          emissiveIntensity: 0.2 * (1 - t / particleType.trailLength)
        });

        const trailSegment = new THREE.Mesh(trailGeometry, trailMaterial);
        trailSegment.visible = false; // Hide initially
        sceneRef.current.add(trailSegment);
        trail.push(trailSegment);
      }

      // Random position on the outer wheel
      const angle = Math.random() * Math.PI * 2;
      const radius = WHEEL_PROPERTIES.OUTER.RADIUS;
      particle.position.set(
        Math.cos(angle) * radius,
        Math.sin(angle) * radius,
        0
      );

      // Add to scene
      sceneRef.current.add(particle);

      // Create particle data object
      const particleData = {
        mesh: particle,
        trail: trail,
        type: particleType.type,
        aspect: trinityAspect.aspect,
        constant: trinityAspect.constant,
        startWheel: 'outer',
        targetWheel: 'middle',
        progress: 0,
        speed: 0.005 + Math.random() * 0.01,
        startPosition: { ...particle.position },
        startAngle: angle,
        pulsePhase: Math.random() * Math.PI * 2,
        pulseFrequency: 0.05 + Math.random() * 0.05,
        pulseAmplitude: 0.2 + Math.random() * 0.3,
        interactable: true,
        data: {
          id: `particle-${i}`,
          value: Math.random(),
          timestamp: new Date()
        }
      };

      // Store particle with its properties
      particlesRef.current.push(particleData);

      // Add particle to raycaster targets if interactable
      if (particleData.interactable) {
        // This would be used for interaction handling
      }
    }
  };

  // Animate particles flowing between wheels
  const animateParticles = () => {
    particlesRef.current.forEach(particle => {
      // Store previous position for trail
      const prevPosition = {
        x: particle.mesh.position.x,
        y: particle.mesh.position.y,
        z: particle.mesh.position.z
      };

      // Update progress
      particle.progress += particle.speed;

      if (particle.progress >= 1) {
        // Reset progress and change target
        particle.progress = 0;

        if (particle.startWheel === 'outer' && particle.targetWheel === 'middle') {
          // Move from outer to middle
          particle.startWheel = 'middle';
          particle.targetWheel = 'inner';

          // New start position on middle wheel
          const angle = Math.random() * Math.PI * 2;
          const radius = WHEEL_PROPERTIES.MIDDLE.RADIUS;
          particle.startPosition = {
            x: 0,
            y: Math.cos(angle) * radius,
            z: Math.sin(angle) * radius
          };
          particle.startAngle = angle;

          // Update particle data
          particle.data.value = Math.random();
          particle.data.timestamp = new Date();

          // Emit event for middle wheel transition
          if (typeof onParticleTransition === 'function') {
            onParticleTransition({
              particleId: particle.data.id,
              fromWheel: 'outer',
              toWheel: 'middle',
              type: particle.type,
              aspect: particle.aspect,
              value: particle.data.value
            });
          }
        } else if (particle.startWheel === 'middle' && particle.targetWheel === 'inner') {
          // Move from middle to inner
          particle.startWheel = 'inner';
          particle.targetWheel = 'outer';

          // New start position on inner wheel
          const angle = Math.random() * Math.PI * 2;
          const radius = WHEEL_PROPERTIES.INNER.RADIUS;
          particle.startPosition = {
            x: Math.cos(angle) * radius,
            y: 0,
            z: Math.sin(angle) * radius
          };
          particle.startAngle = angle;

          // Update particle data
          particle.data.value = Math.random();
          particle.data.timestamp = new Date();

          // Emit event for inner wheel transition
          if (typeof onParticleTransition === 'function') {
            onParticleTransition({
              particleId: particle.data.id,
              fromWheel: 'middle',
              toWheel: 'inner',
              type: particle.type,
              aspect: particle.aspect,
              value: particle.data.value
            });
          }
        } else {
          // Move from inner to outer
          particle.startWheel = 'outer';
          particle.targetWheel = 'middle';

          // New start position on outer wheel
          const angle = Math.random() * Math.PI * 2;
          const radius = WHEEL_PROPERTIES.OUTER.RADIUS;
          particle.startPosition = {
            x: Math.cos(angle) * radius,
            y: Math.sin(angle) * radius,
            z: 0
          };
          particle.startAngle = angle;

          // Update particle data
          particle.data.value = Math.random();
          particle.data.timestamp = new Date();

          // Emit event for outer wheel transition
          if (typeof onParticleTransition === 'function') {
            onParticleTransition({
              particleId: particle.data.id,
              fromWheel: 'inner',
              toWheel: 'outer',
              type: particle.type,
              aspect: particle.aspect,
              value: particle.data.value
            });
          }
        }
      }

      // Calculate new position based on progress
      let targetPosition;

      if (particle.targetWheel === 'middle') {
        // Target position on middle wheel
        const angle = particle.startAngle + Math.PI * particle.progress; // Spiral path
        const radius = WHEEL_PROPERTIES.MIDDLE.RADIUS;
        targetPosition = {
          x: particle.startPosition.x * (1 - particle.progress),
          y: Math.cos(angle) * radius * particle.progress + particle.startPosition.y * (1 - particle.progress),
          z: Math.sin(angle) * radius * particle.progress + particle.startPosition.z * (1 - particle.progress)
        };
      } else if (particle.targetWheel === 'inner') {
        // Target position on inner wheel
        const angle = particle.startAngle + Math.PI * particle.progress; // Spiral path
        const radius = WHEEL_PROPERTIES.INNER.RADIUS;
        targetPosition = {
          x: Math.cos(angle) * radius * particle.progress + particle.startPosition.x * (1 - particle.progress),
          y: particle.startPosition.y * (1 - particle.progress),
          z: Math.sin(angle) * radius * particle.progress + particle.startPosition.z * (1 - particle.progress)
        };
      } else {
        // Target position on outer wheel
        const angle = particle.startAngle + Math.PI * particle.progress; // Spiral path
        const radius = WHEEL_PROPERTIES.OUTER.RADIUS;
        targetPosition = {
          x: Math.cos(angle) * radius * particle.progress + particle.startPosition.x * (1 - particle.progress),
          y: Math.sin(angle) * radius * particle.progress + particle.startPosition.y * (1 - particle.progress),
          z: particle.startPosition.z * (1 - particle.progress)
        };
      }

      // Apply aspect-specific modulation
      if (particle.aspect === 'pi') {
        // Pi-based modulation (Governance) - More structured path
        const piModulation = Math.sin(particle.progress * Math.PI * 2) * 0.2;
        targetPosition.x += piModulation;
        targetPosition.y += piModulation;
      } else if (particle.aspect === 'phi') {
        // Phi-based modulation (Detection) - Golden spiral
        const phiAngle = particle.progress * Math.PI * 2 * 1.618;
        const phiRadius = 0.2 * Math.pow(0.618, particle.progress * 3);
        targetPosition.x += Math.cos(phiAngle) * phiRadius;
        targetPosition.y += Math.sin(phiAngle) * phiRadius;
      } else if (particle.aspect === 'e') {
        // e-based modulation (Response) - Natural growth
        const eModulation = 0.2 * Math.pow(Math.E, particle.progress - 1);
        targetPosition.z += eModulation;
      }

      // Interpolate position
      particle.mesh.position.x = targetPosition.x;
      particle.mesh.position.y = targetPosition.y;
      particle.mesh.position.z = targetPosition.z;

      // Update trail
      if (particle.trail && particle.trail.length > 0) {
        // Update trail positions
        for (let i = particle.trail.length - 1; i > 0; i--) {
          if (i === particle.trail.length - 1) {
            // Last trail segment takes the previous position of the particle
            particle.trail[i].position.set(prevPosition.x, prevPosition.y, prevPosition.z);
          } else {
            // Other trail segments take the position of the next segment
            particle.trail[i].position.copy(particle.trail[i + 1].position);
          }

          // Make trail segment visible
          particle.trail[i].visible = true;
        }

        // First trail segment takes current position
        if (particle.trail.length > 0) {
          particle.trail[0].position.copy(particle.mesh.position);
          particle.trail[0].visible = true;
        }
      }

      // Apply pulse effect
      particle.pulsePhase += particle.pulseFrequency;
      const pulseFactor = 1 + Math.sin(particle.pulsePhase) * particle.pulseAmplitude;

      // Scale based on progress and pulse
      const progressScale = 1 + Math.sin(particle.progress * Math.PI) * 0.3;
      const scale = progressScale * pulseFactor;
      particle.mesh.scale.set(scale, scale, scale);

      // Update material based on progress
      if (particle.mesh.material) {
        // Increase emissive intensity near transitions
        const transitionPoint = Math.abs(particle.progress - 0.5) * 2; // 0 at middle, 1 at ends
        const emissiveIntensity = 0.3 + (1 - transitionPoint) * 0.7;
        particle.mesh.material.emissiveIntensity = emissiveIntensity;

        // Increase opacity based on value
        particle.mesh.material.opacity = 0.7 + particle.data.value * 0.3;
      }

      // Apply type-specific effects
      if (particle.type === 'threat') {
        // Threats pulse more rapidly when near transitions
        const threatPulse = Math.sin(particle.progress * Math.PI * 10) * 0.1;
        particle.mesh.scale.x += threatPulse;
        particle.mesh.scale.y += threatPulse;
      } else if (particle.type === 'compliance') {
        // Compliance particles have more stable paths
        const complianceStability = Math.sin(particle.progress * Math.PI * 2) * 0.05;
        particle.mesh.position.x += complianceStability;
        particle.mesh.position.y += complianceStability;
      } else if (particle.type === 'decision') {
        // Decision particles grow larger near decision points
        const decisionPoint = Math.abs(particle.progress - 0.5); // 0 at middle, 0.5 at ends
        if (decisionPoint < 0.1) {
          const decisionScale = 1 + (0.1 - decisionPoint) * 3;
          particle.mesh.scale.multiplyScalar(decisionScale);
        }
      }
    });
  };

  // Update visualization when data changes
  useEffect(() => {
    if (!isInitialized || !data) return;

    // Update visualization based on data
    // This would be implemented based on the specific data structure
  }, [isInitialized, data]);

  // Handle wheel selection
  const handleWheelSelect = (wheel) => {
    setSelectedWheel(wheel);

    // Update wheel visibility
    if (wheelsRef.current) {
      if (wheel === 'all') {
        if (wheelsRef.current.outer) wheelsRef.current.outer.visible = true;
        if (wheelsRef.current.middle) wheelsRef.current.middle.visible = true;
        if (wheelsRef.current.inner) wheelsRef.current.inner.visible = true;
      } else {
        if (wheelsRef.current.outer) wheelsRef.current.outer.visible = wheel === 'outer';
        if (wheelsRef.current.middle) wheelsRef.current.middle.visible = wheel === 'middle';
        if (wheelsRef.current.inner) wheelsRef.current.inner.visible = wheel === 'inner';
      }
    }
  };

  return (
    <div className="trinity-visualization-container" style={{ width, height }}>
      <div className="trinity-visualization" ref={mountRef} style={{ width: '100%', height: '100%' }} />
      <div className="trinity-visualization-overlay">
        <h2>Trinity Visualization</h2>
        <div className="trinity-legend">
          <div className="legend-item">
            <div
              className={`legend-color ${selectedWheel === 'outer' || selectedWheel === 'all' ? 'active' : ''}`}
              style={{ backgroundColor: '#4CAF50' }}
              onClick={() => handleWheelSelect(selectedWheel === 'outer' ? 'all' : 'outer')}
            ></div>
            <div className="legend-label">Outer Wheel: Ripple Effect Layers</div>
          </div>
          <div className="legend-item">
            <div
              className={`legend-color ${selectedWheel === 'middle' || selectedWheel === 'all' ? 'active' : ''}`}
              style={{ backgroundColor: '#2196F3' }}
              onClick={() => handleWheelSelect(selectedWheel === 'middle' ? 'all' : 'middle')}
            ></div>
            <div className="legend-label">Middle Wheel: Mathematical Constants</div>
          </div>
          <div className="legend-item">
            <div
              className={`legend-color ${selectedWheel === 'inner' || selectedWheel === 'all' ? 'active' : ''}`}
              style={{ backgroundColor: '#9C27B0' }}
              onClick={() => handleWheelSelect(selectedWheel === 'inner' ? 'all' : 'inner')}
            ></div>
            <div className="legend-label">Inner Wheel: Implementation Patterns</div>
          </div>
        </div>

        {hoveredParticle && (
          <div className="particle-tooltip">
            <div className="tooltip-header">
              {hoveredParticle.type.charAt(0).toUpperCase() + hoveredParticle.type.slice(1)} Particle
            </div>
            <div className="tooltip-content">
              <div className="tooltip-item">
                <span className="tooltip-label">Type:</span>
                <span className="tooltip-value">{hoveredParticle.type}</span>
              </div>
              <div className="tooltip-item">
                <span className="tooltip-label">Aspect:</span>
                <span className="tooltip-value">{hoveredParticle.aspect}</span>
              </div>
              <div className="tooltip-item">
                <span className="tooltip-label">Wheel:</span>
                <span className="tooltip-value">{hoveredParticle.startWheel}</span>
              </div>
              <div className="tooltip-item">
                <span className="tooltip-label">Value:</span>
                <span className="tooltip-value">{hoveredParticle.data.value.toFixed(2)}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TrinityVisualization;
