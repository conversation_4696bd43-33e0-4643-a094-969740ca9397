/**
 * Enhanced Connector Details Component
 * 
 * This component provides an improved interface for viewing connector details,
 * with a more intuitive tabbed interface, visual metrics, and inline testing.
 */

import React, { useState, useEffect } from 'react';
import { 
  Alert,
  Avatar,
  Box, 
  Button, 
  Card, 
  CardContent, 
  CardHeader,
  Chip, 
  CircularProgress,
  Divider, 
  Grid, 
  IconButton, 
  LinearProgress,
  Link,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper, 
  Snackbar,
  Stack,
  Tab, 
  Tabs, 
  Tooltip,
  Typography 
} from '@mui/material';

// Icons
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import HistoryIcon from '@mui/icons-material/History';
import CodeIcon from '@mui/icons-material/Code';
import SettingsIcon from '@mui/icons-material/Settings';
import DescriptionIcon from '@mui/icons-material/Description';
import StorageIcon from '@mui/icons-material/Storage';
import ApiIcon from '@mui/icons-material/Api';
import SecurityIcon from '@mui/icons-material/Security';
import SpeedIcon from '@mui/icons-material/Speed';
import BarChartIcon from '@mui/icons-material/BarChart';
import BugReportIcon from '@mui/icons-material/BugReport';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import VisibilityIcon from '@mui/icons-material/Visibility';
import RefreshIcon from '@mui/icons-material/Refresh';
import { useRouter } from 'next/router';

// Components
import ConnectorConfiguration from './ConnectorConfiguration';
import EndpointList from './EndpointList';
import VersionHistory from './VersionHistory';

// Charts
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  ArcElement
);

const EnhancedConnectorDetails = ({ 
  connector, 
  onDelete, 
  onDuplicate, 
  loading = false,
  metrics = null,
  onRefresh
}) => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [testStatus, setTestStatus] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  // Handle edit
  const handleEdit = () => {
    router.push(`/connectors/edit/${connector.id}`);
  };
  
  // Handle test
  const handleTest = () => {
    setTestStatus('running');
    
    // Simulate test
    setTimeout(() => {
      setTestStatus('success');
      setSnackbar({
        open: true,
        message: 'Connector test completed successfully',
        severity: 'success'
      });
    }, 2000);
  };
  
  // Handle delete
  const handleDelete = () => {
    if (onDelete) {
      onDelete(connector.id);
    }
  };
  
  // Handle duplicate
  const handleDuplicate = () => {
    if (onDuplicate) {
      onDuplicate(connector.id);
      setSnackbar({
        open: true,
        message: `Connector "${connector.name}" duplicated successfully`,
        severity: 'success'
      });
    }
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };
  
  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'draft':
        return 'warning';
      case 'deprecated':
        return 'error';
      default:
        return 'default';
    }
  };
  
  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon fontSize="small" color="success" />;
      case 'draft':
        return <WarningIcon fontSize="small" color="warning" />;
      case 'deprecated':
        return <ErrorIcon fontSize="small" color="error" />;
      default:
        return null;
    }
  };
  
  // Generate sample metrics data if not provided
  const generateMetricsData = () => {
    if (metrics) return metrics;
    
    // Sample data for demonstration
    return {
      usage: {
        daily: [65, 59, 80, 81, 56, 55, 40],
        weekly: [28, 48, 40, 19, 86, 27, 90],
        monthly: [12, 19, 3, 5, 2, 3, 8, 12, 19, 3, 5, 2]
      },
      performance: {
        responseTime: [12, 19, 3, 5, 2, 3, 8, 12, 19, 3, 5, 2],
        errorRate: [1, 2, 5, 3, 2, 1, 0, 1, 2, 3, 2, 1],
        successRate: [99, 98, 95, 97, 98, 99, 100, 99, 98, 97, 98, 99]
      },
      distribution: {
        byEndpoint: {
          labels: ['GET /users', 'POST /users', 'GET /products', 'PUT /orders'],
          data: [65, 59, 80, 81]
        },
        byStatus: {
          labels: ['Success', 'Error', 'Warning'],
          data: [85, 10, 5]
        }
      }
    };
  };
  
  // Get metrics data
  const metricsData = generateMetricsData();
  
  // Chart options
  const lineChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Usage Over Time',
      },
    },
  };
  
  // Chart data
  const usageData = {
    labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'],
    datasets: [
      {
        label: 'API Calls',
        data: metricsData.usage.daily,
        borderColor: 'rgb(53, 162, 235)',
        backgroundColor: 'rgba(53, 162, 235, 0.5)',
      },
    ],
  };
  
  const performanceData = {
    labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'],
    datasets: [
      {
        label: 'Response Time (ms)',
        data: metricsData.performance.responseTime.slice(0, 7),
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
        yAxisID: 'y',
      },
      {
        label: 'Error Rate (%)',
        data: metricsData.performance.errorRate.slice(0, 7),
        borderColor: 'rgb(255, 159, 64)',
        backgroundColor: 'rgba(255, 159, 64, 0.5)',
        yAxisID: 'y1',
      },
    ],
  };
  
  const performanceOptions = {
    responsive: true,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    stacked: false,
    plugins: {
      title: {
        display: true,
        text: 'Performance Metrics',
      },
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'Response Time (ms)'
        }
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        grid: {
          drawOnChartArea: false,
        },
        title: {
          display: true,
          text: 'Error Rate (%)'
        }
      },
    },
  };
  
  const distributionData = {
    labels: metricsData.distribution.byEndpoint.labels,
    datasets: [
      {
        label: 'Calls by Endpoint',
        data: metricsData.distribution.byEndpoint.data,
        backgroundColor: [
          'rgba(255, 99, 132, 0.5)',
          'rgba(54, 162, 235, 0.5)',
          'rgba(255, 206, 86, 0.5)',
          'rgba(75, 192, 192, 0.5)',
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };
  
  const statusData = {
    labels: metricsData.distribution.byStatus.labels,
    datasets: [
      {
        label: 'Status Distribution',
        data: metricsData.distribution.byStatus.data,
        backgroundColor: [
          'rgba(75, 192, 192, 0.5)',
          'rgba(255, 99, 132, 0.5)',
          'rgba(255, 206, 86, 0.5)',
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(255, 206, 86, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };
  
  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
            sx={{ 
              width: 56, 
              height: 56, 
              bgcolor: `${getStatusColor(connector.status)}.main`,
              mr: 2
            }}
          >
            {connector.name.charAt(0).toUpperCase()}
          </Avatar>
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {getStatusIcon(connector.status)}
              <Typography variant="h4" component="h1" sx={{ ml: 1 }}>
                {connector.name}
              </Typography>
            </Box>
            <Typography variant="subtitle1" color="text.secondary">
              {connector.description}
            </Typography>
          </Box>
        </Box>
        
        <Box>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={onRefresh}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          
          <Button
            variant="outlined"
            startIcon={testStatus === 'running' ? <CircularProgress size={20} /> : <PlayArrowIcon />}
            onClick={handleTest}
            disabled={testStatus === 'running'}
            color={testStatus === 'success' ? 'success' : 'primary'}
            sx={{ mr: 1 }}
          >
            {testStatus === 'running' ? 'Testing...' : testStatus === 'success' ? 'Test Passed' : 'Test'}
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<ContentCopyIcon />}
            onClick={handleDuplicate}
            sx={{ mr: 1 }}
          >
            Duplicate
          </Button>
          
          <Button
            variant="contained"
            startIcon={<EditIcon />}
            onClick={handleEdit}
            sx={{ mr: 1 }}
          >
            Edit
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<DeleteIcon />}
            onClick={handleDelete}
            color="error"
          >
            Delete
          </Button>
        </Box>
      </Box>
      
      {/* Loading indicator */}
      {loading && (
        <Box sx={{ width: '100%', mb: 2 }}>
          <LinearProgress />
        </Box>
      )}
      
      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange} 
          aria-label="connector details tabs"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab 
            icon={<DescriptionIcon fontSize="small" />} 
            iconPosition="start" 
            label="Overview" 
            value="overview" 
          />
          <Tab 
            icon={<ApiIcon fontSize="small" />} 
            iconPosition="start" 
            label="Endpoints" 
            value="endpoints" 
          />
          <Tab 
            icon={<SettingsIcon fontSize="small" />} 
            iconPosition="start" 
            label="Configuration" 
            value="configuration" 
          />
          <Tab 
            icon={<BarChartIcon fontSize="small" />} 
            iconPosition="start" 
            label="Metrics" 
            value="metrics" 
          />
          <Tab 
            icon={<CodeIcon fontSize="small" />} 
            iconPosition="start" 
            label="Code" 
            value="code" 
          />
          <Tab 
            icon={<BugReportIcon fontSize="small" />} 
            iconPosition="start" 
            label="Testing" 
            value="testing" 
          />
          <Tab 
            icon={<HistoryIcon fontSize="small" />} 
            iconPosition="start" 
            label="History" 
            value="history" 
          />
        </Tabs>
      </Box>
      
      {/* Tab Content */}
      {activeTab === 'overview' && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardHeader title="Connector Details" />
              <Divider />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      ID
                    </Typography>
                    <Typography variant="body1">
                      {connector.id}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Version
                    </Typography>
                    <Typography variant="body1">
                      {connector.version}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Category
                    </Typography>
                    <Typography variant="body1">
                      {connector.category}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Type
                    </Typography>
                    <Typography variant="body1">
                      {connector.type}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Status
                    </Typography>
                    <Chip 
                      label={connector.status} 
                      size="small" 
                      color={getStatusColor(connector.status)} 
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Created
                    </Typography>
                    <Typography variant="body1">
                      {new Date(connector.created).toLocaleString()}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Last Updated
                    </Typography>
                    <Typography variant="body1">
                      {new Date(connector.updated).toLocaleString()}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Author
                    </Typography>
                    <Typography variant="body1">
                      {connector.author || 'Unknown'}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
            
            <Card variant="outlined">
              <CardHeader title="Description" />
              <Divider />
              <CardContent>
                <Typography variant="body1">
                  {connector.description || 'No description provided.'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardHeader title="Usage Statistics" />
              <Divider />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Total Calls
                    </Typography>
                    <Typography variant="h4">
                      {connector.usage || '0'}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Success Rate
                    </Typography>
                    <Typography variant="h4">
                      {connector.successRate || '100%'}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Avg. Response Time
                    </Typography>
                    <Typography variant="h4">
                      {connector.avgResponseTime || '0ms'}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Last Used
                    </Typography>
                    <Typography variant="h4">
                      {connector.lastUsed ? new Date(connector.lastUsed).toLocaleDateString() : 'Never'}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
            
            <Card variant="outlined">
              <CardHeader title="Tags" />
              <Divider />
              <CardContent>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {connector.tags?.map(tag => (
                    <Chip key={tag} label={tag} size="small" />
                  )) || (
                    <Typography variant="body2" color="text.secondary">
                      No tags
                    </Typography>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 'endpoints' && (
        <EndpointList endpoints={connector.endpoints || []} />
      )}
      
      {activeTab === 'configuration' && (
        <ConnectorConfiguration connector={connector} />
      )}
      
      {activeTab === 'metrics' && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardHeader title="Usage Over Time" />
              <Divider />
              <CardContent>
                <Box sx={{ height: 300 }}>
                  <Line options={lineChartOptions} data={usageData} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardHeader title="Performance Metrics" />
              <Divider />
              <CardContent>
                <Box sx={{ height: 300 }}>
                  <Line options={performanceOptions} data={performanceData} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardHeader title="Calls by Endpoint" />
              <Divider />
              <CardContent>
                <Box sx={{ height: 300 }}>
                  <Bar 
                    options={{
                      responsive: true,
                      plugins: {
                        legend: {
                          position: 'top',
                        },
                        title: {
                          display: true,
                          text: 'Calls by Endpoint',
                        },
                      },
                    }} 
                    data={distributionData} 
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardHeader title="Status Distribution" />
              <Divider />
              <CardContent>
                <Box sx={{ height: 300, display: 'flex', justifyContent: 'center' }}>
                  <Box sx={{ width: '70%' }}>
                    <Doughnut 
                      options={{
                        responsive: true,
                        plugins: {
                          legend: {
                            position: 'top',
                          },
                          title: {
                            display: true,
                            text: 'Status Distribution',
                          },
                        },
                      }} 
                      data={statusData} 
                    />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 'code' && (
        <Card variant="outlined">
          <CardHeader title="Connector Code" />
          <Divider />
          <CardContent>
            <Typography variant="body1" paragraph>
              This section will display the connector code and allow for code editing.
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Code editor implementation coming soon.
            </Typography>
          </CardContent>
        </Card>
      )}
      
      {activeTab === 'testing' && (
        <Card variant="outlined">
          <CardHeader title="Connector Testing" />
          <Divider />
          <CardContent>
            <Typography variant="body1" paragraph>
              This section will provide tools for testing the connector.
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Testing interface implementation coming soon.
            </Typography>
          </CardContent>
        </Card>
      )}
      
      {activeTab === 'history' && (
        <VersionHistory versions={connector.versions || []} />
      )}
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default EnhancedConnectorDetails;
