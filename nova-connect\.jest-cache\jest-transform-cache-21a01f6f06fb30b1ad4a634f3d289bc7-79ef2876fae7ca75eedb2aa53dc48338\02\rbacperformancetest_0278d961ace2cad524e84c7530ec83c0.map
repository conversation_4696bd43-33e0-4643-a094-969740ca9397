{"version": 3, "names": ["mongoose", "require", "RBACService", "CacheService", "setupTestEnvironment", "clearDatabase", "disconnectFromDatabase", "getTestData", "rbacService", "adminUser", "regularUser", "viewerUser", "testRole", "testPermission", "beforeAll", "testData", "error", "console", "afterAll", "measureExecutionTime", "fn", "start", "process", "hrtime", "bigint", "result", "end", "duration", "Number", "describe", "test", "flush", "firstDuration", "hasPermission", "_id", "secondDuration", "log", "toFixed", "expect", "toBeLessThan", "getUserPermissions", "getAllRoles", "getAllPermissions", "newRole", "name", "description", "permissions", "createRole", "roles", "hasNewRole", "some", "role", "toBe", "createdRole", "find", "deleteRole", "initialPermissions", "initialCount", "length", "assignRoleToUser", "updatedPermissions", "updatedCount", "toBeGreaterThan", "removeRoleFromUser", "<PERSON><PERSON><PERSON><PERSON>", "promises", "i", "push", "Promise", "all"], "sources": ["rbac-performance.test.js"], "sourcesContent": ["/**\n * RBAC Performance Tests\n * \n * This file contains performance tests for the RBAC system, focusing on caching.\n */\n\nconst mongoose = require('mongoose');\nconst RBACService = require('../../api/services/RBACService');\nconst CacheService = require('../../api/services/CacheService');\nconst { setupTestEnvironment, clearDatabase, disconnectFromDatabase, getTestData } = require('../setup/rbac-test-setup');\n\n// Initialize services\nconst rbacService = new RBACService();\n\n// Test data\nlet adminUser;\nlet regularUser;\nlet viewerUser;\nlet testRole;\nlet testPermission;\n\n// Setup and teardown\nbeforeAll(async () => {\n  try {\n    const testData = await setupTestEnvironment();\n    adminUser = testData.adminUser;\n    regularUser = testData.regularUser;\n    viewerUser = testData.viewerUser;\n    testRole = testData.testRole;\n    testPermission = testData.testPermission;\n  } catch (error) {\n    console.error('Error in beforeAll:', error);\n    throw error;\n  }\n});\n\nafterAll(async () => {\n  await disconnectFromDatabase();\n});\n\n// Helper function to measure execution time\nconst measureExecutionTime = async (fn) => {\n  const start = process.hrtime.bigint();\n  const result = await fn();\n  const end = process.hrtime.bigint();\n  const duration = Number(end - start) / 1_000_000; // Convert to milliseconds\n  return { result, duration };\n};\n\n// Test suites\ndescribe('RBAC Performance', () => {\n  describe('Permission Caching', () => {\n    test('hasPermission should be faster with caching', async () => {\n      // Clear cache before test\n      await CacheService.flush('rbac');\n      \n      // First call (no cache)\n      const { duration: firstDuration } = await measureExecutionTime(() => \n        rbacService.hasPermission(regularUser._id, 'resource:view')\n      );\n      \n      // Second call (with cache)\n      const { duration: secondDuration } = await measureExecutionTime(() => \n        rbacService.hasPermission(regularUser._id, 'resource:view')\n      );\n      \n      console.log(`First call (no cache): ${firstDuration.toFixed(2)}ms`);\n      console.log(`Second call (with cache): ${secondDuration.toFixed(2)}ms`);\n      \n      // The second call should be significantly faster\n      expect(secondDuration).toBeLessThan(firstDuration * 0.5);\n    });\n    \n    test('getUserPermissions should be faster with caching', async () => {\n      // Clear cache before test\n      await CacheService.flush('rbac');\n      \n      // First call (no cache)\n      const { duration: firstDuration } = await measureExecutionTime(() => \n        rbacService.getUserPermissions(regularUser._id)\n      );\n      \n      // Second call (with cache)\n      const { duration: secondDuration } = await measureExecutionTime(() => \n        rbacService.getUserPermissions(regularUser._id)\n      );\n      \n      console.log(`First call (no cache): ${firstDuration.toFixed(2)}ms`);\n      console.log(`Second call (with cache): ${secondDuration.toFixed(2)}ms`);\n      \n      // The second call should be significantly faster\n      expect(secondDuration).toBeLessThan(firstDuration * 0.5);\n    });\n    \n    test('getAllRoles should be faster with caching', async () => {\n      // Clear cache before test\n      await CacheService.flush('rbac');\n      \n      // First call (no cache)\n      const { duration: firstDuration } = await measureExecutionTime(() => \n        rbacService.getAllRoles()\n      );\n      \n      // Second call (with cache)\n      const { duration: secondDuration } = await measureExecutionTime(() => \n        rbacService.getAllRoles()\n      );\n      \n      console.log(`First call (no cache): ${firstDuration.toFixed(2)}ms`);\n      console.log(`Second call (with cache): ${secondDuration.toFixed(2)}ms`);\n      \n      // The second call should be significantly faster\n      expect(secondDuration).toBeLessThan(firstDuration * 0.5);\n    });\n    \n    test('getAllPermissions should be faster with caching', async () => {\n      // Clear cache before test\n      await CacheService.flush('rbac');\n      \n      // First call (no cache)\n      const { duration: firstDuration } = await measureExecutionTime(() => \n        rbacService.getAllPermissions()\n      );\n      \n      // Second call (with cache)\n      const { duration: secondDuration } = await measureExecutionTime(() => \n        rbacService.getAllPermissions()\n      );\n      \n      console.log(`First call (no cache): ${firstDuration.toFixed(2)}ms`);\n      console.log(`Second call (with cache): ${secondDuration.toFixed(2)}ms`);\n      \n      // The second call should be significantly faster\n      expect(secondDuration).toBeLessThan(firstDuration * 0.5);\n    });\n  });\n  \n  describe('Cache Invalidation', () => {\n    test('createRole should invalidate role cache', async () => {\n      // First, populate the cache\n      await rbacService.getAllRoles();\n      \n      // Create a new role\n      const newRole = {\n        name: 'Cache Test Role',\n        description: 'Role for testing cache invalidation',\n        permissions: [testPermission._id]\n      };\n      \n      await rbacService.createRole(newRole);\n      \n      // Get roles again and verify the new role is included\n      const roles = await rbacService.getAllRoles();\n      const hasNewRole = roles.some(role => role.name === newRole.name);\n      \n      expect(hasNewRole).toBe(true);\n      \n      // Clean up\n      const createdRole = roles.find(role => role.name === newRole.name);\n      if (createdRole) {\n        await rbacService.deleteRole(createdRole._id);\n      }\n    });\n    \n    test('assignRoleToUser should invalidate user permission cache', async () => {\n      // First, populate the cache\n      await rbacService.getUserPermissions(viewerUser._id);\n      \n      // Get initial permissions count\n      const initialPermissions = await rbacService.getUserPermissions(viewerUser._id);\n      const initialCount = initialPermissions.length;\n      \n      // Assign a role with more permissions\n      await rbacService.assignRoleToUser(viewerUser._id, testRole._id);\n      \n      // Get permissions again and verify they've changed\n      const updatedPermissions = await rbacService.getUserPermissions(viewerUser._id);\n      const updatedCount = updatedPermissions.length;\n      \n      expect(updatedCount).toBeGreaterThan(initialCount);\n      \n      // Clean up\n      await rbacService.removeRoleFromUser(viewerUser._id, testRole._id);\n    });\n  });\n  \n  describe('Performance Under Load', () => {\n    test('hasPermission should handle multiple concurrent requests', async () => {\n      // Clear cache before test\n      await CacheService.flush('rbac');\n      \n      // Create an array of promises for concurrent permission checks\n      const concurrentChecks = 100;\n      const promises = [];\n      \n      for (let i = 0; i < concurrentChecks; i++) {\n        promises.push(rbacService.hasPermission(regularUser._id, 'resource:view'));\n      }\n      \n      // Measure execution time for all concurrent checks\n      const { duration } = await measureExecutionTime(() => Promise.all(promises));\n      \n      console.log(`${concurrentChecks} concurrent permission checks: ${duration.toFixed(2)}ms`);\n      console.log(`Average time per check: ${(duration / concurrentChecks).toFixed(2)}ms`);\n      \n      // The average time should be reasonable\n      expect(duration / concurrentChecks).toBeLessThan(10); // Less than 10ms per check on average\n    });\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;AACpC,MAAMC,WAAW,GAAGD,OAAO,CAAC,gCAAgC,CAAC;AAC7D,MAAME,YAAY,GAAGF,OAAO,CAAC,iCAAiC,CAAC;AAC/D,MAAM;EAAEG,oBAAoB;EAAEC,aAAa;EAAEC,sBAAsB;EAAEC;AAAY,CAAC,GAAGN,OAAO,CAAC,0BAA0B,CAAC;;AAExH;AACA,MAAMO,WAAW,GAAG,IAAIN,WAAW,CAAC,CAAC;;AAErC;AACA,IAAIO,SAAS;AACb,IAAIC,WAAW;AACf,IAAIC,UAAU;AACd,IAAIC,QAAQ;AACZ,IAAIC,cAAc;;AAElB;AACAC,SAAS,CAAC,YAAY;EACpB,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMX,oBAAoB,CAAC,CAAC;IAC7CK,SAAS,GAAGM,QAAQ,CAACN,SAAS;IAC9BC,WAAW,GAAGK,QAAQ,CAACL,WAAW;IAClCC,UAAU,GAAGI,QAAQ,CAACJ,UAAU;IAChCC,QAAQ,GAAGG,QAAQ,CAACH,QAAQ;IAC5BC,cAAc,GAAGE,QAAQ,CAACF,cAAc;EAC1C,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC3C,MAAMA,KAAK;EACb;AACF,CAAC,CAAC;AAEFE,QAAQ,CAAC,YAAY;EACnB,MAAMZ,sBAAsB,CAAC,CAAC;AAChC,CAAC,CAAC;;AAEF;AACA,MAAMa,oBAAoB,GAAG,MAAOC,EAAE,IAAK;EACzC,MAAMC,KAAK,GAAGC,OAAO,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC;EACrC,MAAMC,MAAM,GAAG,MAAML,EAAE,CAAC,CAAC;EACzB,MAAMM,GAAG,GAAGJ,OAAO,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC;EACnC,MAAMG,QAAQ,GAAGC,MAAM,CAACF,GAAG,GAAGL,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC;EAClD,OAAO;IAAEI,MAAM;IAAEE;EAAS,CAAC;AAC7B,CAAC;;AAED;AACAE,QAAQ,CAAC,kBAAkB,EAAE,MAAM;EACjCA,QAAQ,CAAC,oBAAoB,EAAE,MAAM;IACnCC,IAAI,CAAC,6CAA6C,EAAE,YAAY;MAC9D;MACA,MAAM3B,YAAY,CAAC4B,KAAK,CAAC,MAAM,CAAC;;MAEhC;MACA,MAAM;QAAEJ,QAAQ,EAAEK;MAAc,CAAC,GAAG,MAAMb,oBAAoB,CAAC,MAC7DX,WAAW,CAACyB,aAAa,CAACvB,WAAW,CAACwB,GAAG,EAAE,eAAe,CAC5D,CAAC;;MAED;MACA,MAAM;QAAEP,QAAQ,EAAEQ;MAAe,CAAC,GAAG,MAAMhB,oBAAoB,CAAC,MAC9DX,WAAW,CAACyB,aAAa,CAACvB,WAAW,CAACwB,GAAG,EAAE,eAAe,CAC5D,CAAC;MAEDjB,OAAO,CAACmB,GAAG,CAAC,0BAA0BJ,aAAa,CAACK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MACnEpB,OAAO,CAACmB,GAAG,CAAC,6BAA6BD,cAAc,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;MAEvE;MACAC,MAAM,CAACH,cAAc,CAAC,CAACI,YAAY,CAACP,aAAa,GAAG,GAAG,CAAC;IAC1D,CAAC,CAAC;IAEFF,IAAI,CAAC,kDAAkD,EAAE,YAAY;MACnE;MACA,MAAM3B,YAAY,CAAC4B,KAAK,CAAC,MAAM,CAAC;;MAEhC;MACA,MAAM;QAAEJ,QAAQ,EAAEK;MAAc,CAAC,GAAG,MAAMb,oBAAoB,CAAC,MAC7DX,WAAW,CAACgC,kBAAkB,CAAC9B,WAAW,CAACwB,GAAG,CAChD,CAAC;;MAED;MACA,MAAM;QAAEP,QAAQ,EAAEQ;MAAe,CAAC,GAAG,MAAMhB,oBAAoB,CAAC,MAC9DX,WAAW,CAACgC,kBAAkB,CAAC9B,WAAW,CAACwB,GAAG,CAChD,CAAC;MAEDjB,OAAO,CAACmB,GAAG,CAAC,0BAA0BJ,aAAa,CAACK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MACnEpB,OAAO,CAACmB,GAAG,CAAC,6BAA6BD,cAAc,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;MAEvE;MACAC,MAAM,CAACH,cAAc,CAAC,CAACI,YAAY,CAACP,aAAa,GAAG,GAAG,CAAC;IAC1D,CAAC,CAAC;IAEFF,IAAI,CAAC,2CAA2C,EAAE,YAAY;MAC5D;MACA,MAAM3B,YAAY,CAAC4B,KAAK,CAAC,MAAM,CAAC;;MAEhC;MACA,MAAM;QAAEJ,QAAQ,EAAEK;MAAc,CAAC,GAAG,MAAMb,oBAAoB,CAAC,MAC7DX,WAAW,CAACiC,WAAW,CAAC,CAC1B,CAAC;;MAED;MACA,MAAM;QAAEd,QAAQ,EAAEQ;MAAe,CAAC,GAAG,MAAMhB,oBAAoB,CAAC,MAC9DX,WAAW,CAACiC,WAAW,CAAC,CAC1B,CAAC;MAEDxB,OAAO,CAACmB,GAAG,CAAC,0BAA0BJ,aAAa,CAACK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MACnEpB,OAAO,CAACmB,GAAG,CAAC,6BAA6BD,cAAc,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;MAEvE;MACAC,MAAM,CAACH,cAAc,CAAC,CAACI,YAAY,CAACP,aAAa,GAAG,GAAG,CAAC;IAC1D,CAAC,CAAC;IAEFF,IAAI,CAAC,iDAAiD,EAAE,YAAY;MAClE;MACA,MAAM3B,YAAY,CAAC4B,KAAK,CAAC,MAAM,CAAC;;MAEhC;MACA,MAAM;QAAEJ,QAAQ,EAAEK;MAAc,CAAC,GAAG,MAAMb,oBAAoB,CAAC,MAC7DX,WAAW,CAACkC,iBAAiB,CAAC,CAChC,CAAC;;MAED;MACA,MAAM;QAAEf,QAAQ,EAAEQ;MAAe,CAAC,GAAG,MAAMhB,oBAAoB,CAAC,MAC9DX,WAAW,CAACkC,iBAAiB,CAAC,CAChC,CAAC;MAEDzB,OAAO,CAACmB,GAAG,CAAC,0BAA0BJ,aAAa,CAACK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MACnEpB,OAAO,CAACmB,GAAG,CAAC,6BAA6BD,cAAc,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;MAEvE;MACAC,MAAM,CAACH,cAAc,CAAC,CAACI,YAAY,CAACP,aAAa,GAAG,GAAG,CAAC;IAC1D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFH,QAAQ,CAAC,oBAAoB,EAAE,MAAM;IACnCC,IAAI,CAAC,yCAAyC,EAAE,YAAY;MAC1D;MACA,MAAMtB,WAAW,CAACiC,WAAW,CAAC,CAAC;;MAE/B;MACA,MAAME,OAAO,GAAG;QACdC,IAAI,EAAE,iBAAiB;QACvBC,WAAW,EAAE,qCAAqC;QAClDC,WAAW,EAAE,CAACjC,cAAc,CAACqB,GAAG;MAClC,CAAC;MAED,MAAM1B,WAAW,CAACuC,UAAU,CAACJ,OAAO,CAAC;;MAErC;MACA,MAAMK,KAAK,GAAG,MAAMxC,WAAW,CAACiC,WAAW,CAAC,CAAC;MAC7C,MAAMQ,UAAU,GAAGD,KAAK,CAACE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACP,IAAI,KAAKD,OAAO,CAACC,IAAI,CAAC;MAEjEN,MAAM,CAACW,UAAU,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC;;MAE7B;MACA,MAAMC,WAAW,GAAGL,KAAK,CAACM,IAAI,CAACH,IAAI,IAAIA,IAAI,CAACP,IAAI,KAAKD,OAAO,CAACC,IAAI,CAAC;MAClE,IAAIS,WAAW,EAAE;QACf,MAAM7C,WAAW,CAAC+C,UAAU,CAACF,WAAW,CAACnB,GAAG,CAAC;MAC/C;IACF,CAAC,CAAC;IAEFJ,IAAI,CAAC,0DAA0D,EAAE,YAAY;MAC3E;MACA,MAAMtB,WAAW,CAACgC,kBAAkB,CAAC7B,UAAU,CAACuB,GAAG,CAAC;;MAEpD;MACA,MAAMsB,kBAAkB,GAAG,MAAMhD,WAAW,CAACgC,kBAAkB,CAAC7B,UAAU,CAACuB,GAAG,CAAC;MAC/E,MAAMuB,YAAY,GAAGD,kBAAkB,CAACE,MAAM;;MAE9C;MACA,MAAMlD,WAAW,CAACmD,gBAAgB,CAAChD,UAAU,CAACuB,GAAG,EAAEtB,QAAQ,CAACsB,GAAG,CAAC;;MAEhE;MACA,MAAM0B,kBAAkB,GAAG,MAAMpD,WAAW,CAACgC,kBAAkB,CAAC7B,UAAU,CAACuB,GAAG,CAAC;MAC/E,MAAM2B,YAAY,GAAGD,kBAAkB,CAACF,MAAM;MAE9CpB,MAAM,CAACuB,YAAY,CAAC,CAACC,eAAe,CAACL,YAAY,CAAC;;MAElD;MACA,MAAMjD,WAAW,CAACuD,kBAAkB,CAACpD,UAAU,CAACuB,GAAG,EAAEtB,QAAQ,CAACsB,GAAG,CAAC;IACpE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFL,QAAQ,CAAC,wBAAwB,EAAE,MAAM;IACvCC,IAAI,CAAC,0DAA0D,EAAE,YAAY;MAC3E;MACA,MAAM3B,YAAY,CAAC4B,KAAK,CAAC,MAAM,CAAC;;MAEhC;MACA,MAAMiC,gBAAgB,GAAG,GAAG;MAC5B,MAAMC,QAAQ,GAAG,EAAE;MAEnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,gBAAgB,EAAEE,CAAC,EAAE,EAAE;QACzCD,QAAQ,CAACE,IAAI,CAAC3D,WAAW,CAACyB,aAAa,CAACvB,WAAW,CAACwB,GAAG,EAAE,eAAe,CAAC,CAAC;MAC5E;;MAEA;MACA,MAAM;QAAEP;MAAS,CAAC,GAAG,MAAMR,oBAAoB,CAAC,MAAMiD,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC,CAAC;MAE5EhD,OAAO,CAACmB,GAAG,CAAC,GAAG4B,gBAAgB,kCAAkCrC,QAAQ,CAACU,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MACzFpB,OAAO,CAACmB,GAAG,CAAC,2BAA2B,CAACT,QAAQ,GAAGqC,gBAAgB,EAAE3B,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;MAEpF;MACAC,MAAM,CAACX,QAAQ,GAAGqC,gBAAgB,CAAC,CAACzB,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;IACxD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}