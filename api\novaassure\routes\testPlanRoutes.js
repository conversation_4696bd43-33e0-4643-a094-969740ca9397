/**
 * Test Plan Routes
 * 
 * This file defines the routes for test plan management.
 */

const express = require('express');
const router = express.Router();
const testPlanController = require('../controllers/testPlanController');
const authMiddleware = require('../middleware/authMiddleware');

/**
 * @swagger
 * /api/v1/novaassure/test-plans:
 *   get:
 *     summary: Get all test plans
 *     description: Retrieve a list of all test plans
 *     tags: [Test Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: framework
 *         schema:
 *           type: string
 *         description: Filter by framework (e.g., soc2, gdpr, hipaa)
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, active, completed, archived]
 *         description: Filter by status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *     responses:
 *       200:
 *         description: List of test plans
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', authMiddleware.authenticate, testPlanController.getAllTestPlans);

/**
 * @swagger
 * /api/v1/novaassure/test-plans/{id}:
 *   get:
 *     summary: Get test plan by ID
 *     description: Retrieve a test plan by its ID
 *     tags: [Test Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Test plan ID
 *     responses:
 *       200:
 *         description: Test plan details
 *       404:
 *         description: Test plan not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/:id', authMiddleware.authenticate, testPlanController.getTestPlanById);

/**
 * @swagger
 * /api/v1/novaassure/test-plans:
 *   post:
 *     summary: Create test plan
 *     description: Create a new test plan
 *     tags: [Test Plans]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - description
 *               - framework
 *               - controls
 *             properties:
 *               name:
 *                 type: string
 *                 description: Test plan name
 *               description:
 *                 type: string
 *                 description: Test plan description
 *               framework:
 *                 type: string
 *                 description: Framework (e.g., soc2, gdpr, hipaa)
 *               controls:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Control IDs to include in the test plan
 *               schedule:
 *                 type: object
 *                 properties:
 *                   frequency:
 *                     type: string
 *                     enum: [daily, weekly, monthly, quarterly, annually]
 *                     description: Test frequency
 *                   startDate:
 *                     type: string
 *                     format: date
 *                     description: Start date
 *                   endDate:
 *                     type: string
 *                     format: date
 *                     description: End date
 *               assignees:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: User IDs assigned to the test plan
 *     responses:
 *       201:
 *         description: Test plan created
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/', authMiddleware.authenticate, testPlanController.createTestPlan);

/**
 * @swagger
 * /api/v1/novaassure/test-plans/{id}:
 *   put:
 *     summary: Update test plan
 *     description: Update an existing test plan
 *     tags: [Test Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Test plan ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Test plan name
 *               description:
 *                 type: string
 *                 description: Test plan description
 *               framework:
 *                 type: string
 *                 description: Framework (e.g., soc2, gdpr, hipaa)
 *               controls:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Control IDs to include in the test plan
 *               schedule:
 *                 type: object
 *                 properties:
 *                   frequency:
 *                     type: string
 *                     enum: [daily, weekly, monthly, quarterly, annually]
 *                     description: Test frequency
 *                   startDate:
 *                     type: string
 *                     format: date
 *                     description: Start date
 *                   endDate:
 *                     type: string
 *                     format: date
 *                     description: End date
 *               assignees:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: User IDs assigned to the test plan
 *               status:
 *                 type: string
 *                 enum: [draft, active, completed, archived]
 *                 description: Test plan status
 *     responses:
 *       200:
 *         description: Test plan updated
 *       400:
 *         description: Invalid request
 *       404:
 *         description: Test plan not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.put('/:id', authMiddleware.authenticate, testPlanController.updateTestPlan);

/**
 * @swagger
 * /api/v1/novaassure/test-plans/{id}:
 *   delete:
 *     summary: Delete test plan
 *     description: Delete an existing test plan
 *     tags: [Test Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Test plan ID
 *     responses:
 *       200:
 *         description: Test plan deleted
 *       404:
 *         description: Test plan not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.delete('/:id', authMiddleware.authenticate, testPlanController.deleteTestPlan);

/**
 * @swagger
 * /api/v1/novaassure/test-plans/{id}/schedule:
 *   post:
 *     summary: Schedule test plan
 *     description: Schedule a test plan for execution
 *     tags: [Test Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Test plan ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - schedule
 *             properties:
 *               schedule:
 *                 type: object
 *                 properties:
 *                   frequency:
 *                     type: string
 *                     enum: [daily, weekly, monthly, quarterly, annually]
 *                     description: Test frequency
 *                   startDate:
 *                     type: string
 *                     format: date
 *                     description: Start date
 *                   endDate:
 *                     type: string
 *                     format: date
 *                     description: End date
 *     responses:
 *       200:
 *         description: Test plan scheduled
 *       400:
 *         description: Invalid request
 *       404:
 *         description: Test plan not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/:id/schedule', authMiddleware.authenticate, testPlanController.scheduleTestPlan);

/**
 * @swagger
 * /api/v1/novaassure/test-plans/{id}/assign:
 *   post:
 *     summary: Assign test plan
 *     description: Assign a test plan to users
 *     tags: [Test Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Test plan ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assignees
 *             properties:
 *               assignees:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: User IDs to assign to the test plan
 *     responses:
 *       200:
 *         description: Test plan assigned
 *       400:
 *         description: Invalid request
 *       404:
 *         description: Test plan not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/:id/assign', authMiddleware.authenticate, testPlanController.assignTestPlan);

/**
 * @swagger
 * /api/v1/novaassure/test-plans/{id}/clone:
 *   post:
 *     summary: Clone test plan
 *     description: Create a copy of an existing test plan
 *     tags: [Test Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Test plan ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: New test plan name
 *     responses:
 *       201:
 *         description: Test plan cloned
 *       400:
 *         description: Invalid request
 *       404:
 *         description: Test plan not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/:id/clone', authMiddleware.authenticate, testPlanController.cloneTestPlan);

module.exports = router;
