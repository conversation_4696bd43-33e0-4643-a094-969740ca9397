/**
 * Interactive Controls Example
 * 
 * This example demonstrates how to use the interactive controls to manipulate
 * the Self-Healing Tensor, 3D Tensor Visualization, and Analytics Dashboard.
 */

// Import the controls module
const { createUnifiedControlSystem } = require('../index');

// Import the WebSocket module
const { createRealTimeCommunication } = require('../../websocket');

// Import the unified integration module
const { createUnifiedIntegration } = require('../../integration');

// Import the Self-Healing Tensor
const SelfHealingTensor = require('../../quantum/self-healing-tensor');

// Import visualization components (mock for this example)
const VisualizationSystem = {
  on: (event, callback) => {
    // Mock event listener
    console.log(`Registered event listener for: ${event}`);
  },
  
  getVisualizationTypes: () => {
    return [
      '3d_tensor_visualization',
      'resonance_spectrogram',
      'phase_space_visualization',
      'harmonic_pattern_explorer'
    ];
  },
  
  create3dTensorVisualization: (data, options) => {
    console.log('Creating 3D Tensor Visualization', { data, options });
    return {
      id: `viz-${Date.now()}`,
      type: '3d_tensor_visualization',
      data,
      options
    };
  },
  
  createResonanceSpectrogram: (data, options) => {
    console.log('Creating Resonance Spectrogram', { data, options });
    return {
      id: `viz-${Date.now()}`,
      type: 'resonance_spectrogram',
      data,
      options
    };
  },
  
  updateVisualization: (id, data) => {
    console.log('Updating Visualization', { id, data });
    return {
      id,
      type: data.type || '3d_tensor_visualization',
      data,
      options: data.options
    };
  },
  
  deleteVisualization: (id) => {
    console.log('Deleting Visualization', { id });
    return true;
  }
};

// Import analytics components (mock for this example)
const AnalyticsDashboard = {
  on: (event, callback) => {
    // Mock event listener
    console.log(`Registered event listener for: ${event}`);
  },
  
  getMetrics: () => {
    return {
      'tensor.health': 0.95,
      'tensor.entropyContainment': 0.02,
      'tensor.healingCycles': 3,
      'visualization.updateRate': 30,
      'system.performance': 0.87
    };
  },
  
  getDashboards: () => {
    return [
      {
        id: 'tensor-health-dashboard',
        name: 'Tensor Health Dashboard',
        metrics: ['tensor.health', 'tensor.entropyContainment', 'tensor.healingCycles']
      },
      {
        id: 'system-performance-dashboard',
        name: 'System Performance Dashboard',
        metrics: ['visualization.updateRate', 'system.performance']
      }
    ];
  },
  
  getDashboard: (id) => {
    if (id === 'tensor-health-dashboard') {
      return {
        id: 'tensor-health-dashboard',
        name: 'Tensor Health Dashboard',
        metrics: ['tensor.health', 'tensor.entropyContainment', 'tensor.healingCycles']
      };
    } else if (id === 'system-performance-dashboard') {
      return {
        id: 'system-performance-dashboard',
        name: 'System Performance Dashboard',
        metrics: ['visualization.updateRate', 'system.performance']
      };
    }
    
    return null;
  },
  
  executeQuery: (query, params) => {
    console.log('Executing query', { query, params });
    return Promise.resolve({
      id: `query-${Date.now()}`,
      results: [
        { metric: 'tensor.health', value: 0.95, timestamp: Date.now() },
        { metric: 'tensor.entropyContainment', value: 0.02, timestamp: Date.now() }
      ]
    });
  }
};

// Mock UI for displaying controls
class MockUI {
  constructor() {
    this.controls = {};
    this.controlValues = {};
    this.actions = {};
  }
  
  renderControl(controlId, control, value) {
    this.controls[controlId] = control;
    this.controlValues[controlId] = value;
    
    console.log(`UI: Rendered control ${controlId} (${control.type}) with value ${JSON.stringify(value)}`);
  }
  
  registerAction(actionId, action) {
    this.actions[actionId] = action;
    
    console.log(`UI: Registered action ${actionId}`);
  }
  
  simulateControlChange(controlId, value) {
    console.log(`UI: Control ${controlId} changed to ${JSON.stringify(value)}`);
    
    // Update control value
    this.controlValues[controlId] = value;
    
    // Emit change event
    if (this.onControlChange) {
      this.onControlChange(controlId, value);
    }
  }
  
  simulateActionClick(actionId, params = {}) {
    console.log(`UI: Action ${actionId} clicked with params ${JSON.stringify(params)}`);
    
    // Execute action
    if (this.actions[actionId]) {
      this.actions[actionId](params);
    }
  }
}

// Run the example
async function runExample() {
  try {
    console.log('Starting Interactive Controls Example...');
    
    // Create Self-Healing Tensor instance
    const selfHealingTensor = new SelfHealingTensor({
      healingThreshold: 0.6,
      healingFactor: 0.6,
      maxHealingCycles: 6,
      autoHeal: true
    });
    
    // Create unified integration
    const unifiedIntegration = createUnifiedIntegration({
      enableLogging: true,
      enableMetrics: true,
      autoConnect: true,
      updateInterval: 1000,
      tensor: selfHealingTensor,
      visualization: VisualizationSystem,
      analytics: AnalyticsDashboard
    });
    
    console.log('\nUnified Integration created successfully!');
    
    // Create real-time communication system
    const realTimeCommunication = createRealTimeCommunication({
      port: 3001,
      enableLogging: true,
      enableMetrics: true,
      tensorAdapter: unifiedIntegration.adapters.tensor,
      visualizationAdapter: unifiedIntegration.adapters.visualization,
      analyticsAdapter: unifiedIntegration.adapters.analytics
    });
    
    console.log('\nReal-Time Communication system created successfully!');
    
    // Create unified control system
    const unifiedControlSystem = createUnifiedControlSystem({
      enableLogging: true,
      wsUrl: 'ws://localhost:3001/ws',
      autoConnect: false
    });
    
    console.log('\nUnified Control System created successfully!');
    
    // Connect unified control system
    await unifiedControlSystem.connect();
    
    console.log('\nUnified Control System connected successfully!');
    
    // Create mock UI
    const ui = new MockUI();
    
    // Set up control panel event handlers
    unifiedControlSystem.controlPanel.on('control-registered', (data) => {
      const { controlId } = data;
      const control = unifiedControlSystem.controlPanel.getControl(controlId);
      const value = unifiedControlSystem.controlPanel.getControlValue(controlId);
      
      // Render control in UI
      ui.renderControl(controlId, control, value);
    });
    
    unifiedControlSystem.controlPanel.on('control-value-changed', (data) => {
      const { controlId, value } = data;
      
      // Update control in UI
      ui.renderControl(controlId, unifiedControlSystem.controlPanel.getControl(controlId), value);
    });
    
    // Set up UI event handlers
    ui.onControlChange = (controlId, value) => {
      // Update control value in control panel
      unifiedControlSystem.controlPanel.setControlValue(controlId, value);
    };
    
    // Register actions in UI
    ui.registerAction('register-tensor', async (params) => {
      await unifiedControlSystem.registerTensor(params.id, params.tensor, params.domain);
    });
    
    ui.registerAction('heal-tensor', async (params) => {
      await unifiedControlSystem.healTensor(params.id);
    });
    
    ui.registerAction('damage-tensor', async (params) => {
      await unifiedControlSystem.damageTensor(params.id, params.damageLevel);
    });
    
    ui.registerAction('create-visualization', async (params) => {
      await unifiedControlSystem.createVisualization(params.visualizationType, params.data, params.options);
    });
    
    ui.registerAction('update-visualization', async (params) => {
      await unifiedControlSystem.updateVisualization(params.id, params.data);
    });
    
    ui.registerAction('delete-visualization', async (params) => {
      await unifiedControlSystem.deleteVisualization(params.id);
    });
    
    ui.registerAction('execute-query', async (params) => {
      await unifiedControlSystem.executeQuery(params.query, params.params);
    });
    
    ui.registerAction('refresh-metrics', async () => {
      await unifiedControlSystem.refreshMetrics();
    });
    
    ui.registerAction('refresh-dashboard', async (params) => {
      await unifiedControlSystem.refreshDashboard(params.id);
    });
    
    // Simulate user interactions
    console.log('\nSimulating user interactions...');
    
    // Register a tensor
    console.log('\nRegistering a tensor...');
    await ui.simulateActionClick('register-tensor', {
      id: 'example-tensor',
      tensor: {
        values: [0.5, 0.6, 0.7, 0.8, 0.9]
      },
      domain: 'universal'
    });
    
    // Wait for tensor to be registered
    await new Promise((resolve) => setTimeout(resolve, 1000));
    
    // Create a visualization
    console.log('\nCreating a visualization...');
    await ui.simulateActionClick('create-visualization', {
      visualizationType: '3d_tensor_visualization',
      data: {
        tensor: {
          values: [0.5, 0.6, 0.7, 0.8, 0.9],
          health: 1.0,
          entropyContainment: 0.0
        },
        dimensions: [5, 1, 1]
      },
      options: {
        renderMode: 'high-quality',
        showAxes: true,
        showGrid: true,
        rotationSpeed: 1,
        colorScheme: 'default'
      }
    });
    
    // Wait for visualization to be created
    await new Promise((resolve) => setTimeout(resolve, 1000));
    
    // Change visualization options
    console.log('\nChanging visualization options...');
    ui.simulateControlChange('render-quality', 'high');
    ui.simulateControlChange('show-axes', true);
    ui.simulateControlChange('show-grid', false);
    ui.simulateControlChange('rotation-speed', 2);
    ui.simulateControlChange('color-scheme', 'rainbow');
    
    // Wait for visualization to be updated
    await new Promise((resolve) => setTimeout(resolve, 1000));
    
    // Damage the tensor
    console.log('\nDamaging the tensor...');
    ui.simulateControlChange('damage-level', 0.7);
    await ui.simulateActionClick('damage-tensor', {
      id: 'example-tensor',
      damageLevel: 0.7
    });
    
    // Wait for tensor to be damaged
    await new Promise((resolve) => setTimeout(resolve, 1000));
    
    // Heal the tensor
    console.log('\nHealing the tensor...');
    await ui.simulateActionClick('heal-tensor', {
      id: 'example-tensor'
    });
    
    // Wait for tensor to be healed
    await new Promise((resolve) => setTimeout(resolve, 1000));
    
    // Execute a query
    console.log('\nExecuting a query...');
    ui.simulateControlChange('query-input', 'SELECT * FROM tensor_metrics WHERE tensor_id = "example-tensor"');
    await ui.simulateActionClick('execute-query', {
      query: 'SELECT * FROM tensor_metrics WHERE tensor_id = "example-tensor"',
      params: {
        limit: 10
      }
    });
    
    // Wait for query to complete
    await new Promise((resolve) => setTimeout(resolve, 1000));
    
    // Refresh metrics
    console.log('\nRefreshing metrics...');
    await ui.simulateActionClick('refresh-metrics');
    
    // Wait for metrics to be refreshed
    await new Promise((resolve) => setTimeout(resolve, 1000));
    
    // Set refresh interval
    console.log('\nSetting refresh interval...');
    ui.simulateControlChange('refresh-interval', 5);
    
    // Wait for a few refresh cycles
    console.log('\nWaiting for refresh cycles...');
    await new Promise((resolve) => setTimeout(resolve, 15000));
    
    // Clean up
    console.log('\nCleaning up...');
    
    // Disconnect unified control system
    await unifiedControlSystem.disconnect();
    
    // Stop real-time communication system
    await realTimeCommunication.stop();
    
    console.log('\nInteractive Controls Example completed successfully!');
  } catch (error) {
    console.error('Error running Interactive Controls Example:', error);
  }
}

// Run the example
runExample();
