/**
 * NovaProof Index
 *
 * This file exports all the components of the NovaProof system.
 * NovaProof is the Universal Compliance Evidence System that provides
 * blockchain-verified audit trails for compliance evidence.
 */

// Export evidence models
const { Evidence, EvidenceStatus } = require('./models/Evidence');

// Export blockchain verification
const {
  verifyEvidence,
  generateProof,
  verifyProof,
  batchVerifyEvidence,
  BlockchainVerificationManager,
  VerificationStatus,
  BlockchainType
} = require('./verification/blockchain-verification');

// Export blockchain providers
const {
  BlockchainProvider,
  EthereumProvider,
  HyperledgerProvider,
  createBlockchainProvider,
  TransactionStatus
} = require('./blockchain/blockchain-provider');

// Export Merkle tree
const {
  MerkleTree,
  MerkleNode
} = require('./merkle/merkle-tree');

// Export evidence utilities
const evidenceUtils = require('./utils/evidence-utils');

// Export all components
module.exports = {
  // Models
  Evidence,
  EvidenceStatus,

  // Verification
  verifyEvidence,
  generateProof,
  verifyProof,
  batchVerifyEvidence,
  BlockchainVerificationManager,
  VerificationStatus,
  BlockchainType,

  // Blockchain
  BlockchainProvider,
  EthereumProvider,
  HyperledgerProvider,
  createBlockchainProvider,
  TransactionStatus,

  // Merkle Tree
  MerkleTree,
  MerkleNode,

  // Utilities
  evidenceUtils
};
