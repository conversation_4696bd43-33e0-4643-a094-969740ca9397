@echo off
echo Starting NovaCore and NovaSphere Integration...

REM Create public directory for NovaCore if it doesn't exist
if not exist "%~dp0\src\novacore\public" mkdir "%~dp0\src\novacore\public"

REM Start NovaCore API Server
start cmd /k "cd %~dp0\src\novacore && npm run dev"

REM Wait for NovaCore to start
timeout /t 5

REM Start NovaSphere Web Application
start cmd /k "cd %~dp0\src\ucecs\web && npm run dev"

echo NovaCore and NovaSphere are now running.
echo NovaCore API: http://localhost:5000
echo NovaSphere Web: http://localhost:3000

REM Open browser to both services
timeout /t 2
start http://localhost:5000
timeout /t 1
start http://localhost:3000
