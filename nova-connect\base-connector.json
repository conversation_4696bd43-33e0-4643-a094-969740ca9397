﻿{
  "name": "novafuse-base-connector",
  "version": "1.0.0",
  "description": "Base connector template for NovaFuse API Superstore",
  "auth": {
    "type": "apikey",
    "header_name": "apikey",
    "query_param_name": "apikey"
  },
  "base_url": "http://localhost:8000",
  "endpoints": [
    {
      "name": "health",
      "path": "/health",
      "method": "GET",
      "description": "Check if the API is healthy"
    }
  ],
  "error_handling": {
    "retry_count": 3,
    "retry_delay": 1000,
    "timeout": 30000
  },
  "rate_limiting": {
    "requests_per_second": 10,
    "burst": 20
  },
  "logging": {
    "level": "info",
    "format": "json"
  }
}
