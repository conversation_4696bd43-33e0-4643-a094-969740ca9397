"""
Direct test of NovaFoldClient folding archetypes.
This script should be run from the src directory.
"""

from ConsciousNovaFold import NovaFoldClient

def main():
    print("Testing NovaFoldClient folding archetypes...\n")
    
    try:
        # Initialize the client
        client = NovaFoldClient(enable_benchmark=False)
        print("✅ Successfully created NovaFoldClient instance")
        
        # List available archetypes
        archetypes = client.list_archetypes()
        print("\n🔍 Available archetypes:")
        for archetype in archetypes:
            info = client.get_archetype_info(archetype)
            print(f"- {archetype}: {info.get('description', 'No description')}")
        
        # Test a simple prediction with each archetype
        test_sequence = "ACDEFGHIKLMNPQRSTVWY"  # 20 amino acids
        print(f"\n🧪 Testing predictions with sequence: {test_sequence}")
        
        for archetype in archetypes[:3]:  # Just test first 3 to keep output manageable
            print(f"\n🔧 Testing archetype: {archetype}")
            try:
                result = client.predict(
                    sequence=test_sequence,
                    folding_variant=archetype,
                    validate_against=None
                )
                
                # Print basic info
                metadata = result['structure']['metadata']
                print(f"  - Folding archetype: {metadata.get('folding_archetype', 'N/A')}")
                print(f"  - Consciousness impact: {metadata.get('consciousness_impact', 'N/A')}")
                print(f"  - Secondary structure: {result['structure']['secondary_structure']}")
                
            except Exception as e:
                print(f"  ❌ Error with {archetype}: {str(e)}")
        
        print("\n✅ All tests completed!")
        
    except Exception as e:
        print(f"\n❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
