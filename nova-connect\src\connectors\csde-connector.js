/**
 * CSDE Connector for NovaConnect
 *
 * This connector integrates the CSDE with NovaConnect's connector framework,
 * allowing it to be used as a standard connector in NovaConnect workflows.
 */

const BaseConnector = require('./base-connector');
const CSEDIntegration = require('../integrations/csde-integration');
const { performance } = require('perf_hooks');

class CSEDConnector extends BaseConnector {
  /**
   * Create a new CSDE Connector
   * @param {Object} config - Connector configuration
   */
  constructor(config = {}) {
    // Call base constructor
    super({
      id: config.id || 'csde-connector',
      name: config.name || 'CSDE Connector',
      description: config.description || 'Connector for the Cyber-Safety Domain Engine (CSDE)',
      version: config.version || '1.0.0',
      type: config.type || 'transformation',
      ...config
    });

    // Initialize CSDE integration
    this.csdeIntegration = new CSEDIntegration({
      csdeApiUrl: config.csdeApiUrl || process.env.CSDE_API_URL || 'http://localhost:3010',
      enableCaching: config.enableCaching !== false,
      enableMetrics: config.enableMetrics !== false,
      cacheSize: config.cacheSize || 1000,
      domain: config.domain || 'general',
      enableOptimizedMapping: config.enableOptimizedMapping !== false,
      enableParallelProcessing: config.enableParallelProcessing !== false,
      enableDirectPropertyAccess: config.enableDirectPropertyAccess !== false,
      timeout: config.timeout || 30000,
      maxConcurrentRequests: config.maxConcurrentRequests || 20
    });

    // Initialize metrics
    this.metrics = {
      totalOperations: 0,
      successfulOperations: 0,
      failedOperations: 0,
      totalDuration: 0,
      averageDuration: 0
    };

    this.logger.info('CSDE Connector initialized');
  }

  /**
   * Transform data using the CSDE
   * @param {Object} data - Data to transform
   * @param {Object} options - Transformation options
   * @returns {Promise<Object>} - Transformed data
   */
  async transform(data, options = {}) {
    const startTime = performance.now();

    try {
      this.logger.debug('Transforming data using CSDE', { dataSize: JSON.stringify(data).length });

      // Get transformation rules from options or config
      const rules = options.rules || this.config.rules || [];

      if (!rules || rules.length === 0) {
        throw new Error('No transformation rules provided');
      }

      // Transform data using CSDE integration
      const result = await this.csdeIntegration.transform(data, rules);

      // Update metrics
      const endTime = performance.now();
      const duration = endTime - startTime;

      this.metrics.totalOperations++;
      this.metrics.successfulOperations++;
      this.metrics.totalDuration += duration;
      this.metrics.averageDuration = this.metrics.totalDuration / this.metrics.successfulOperations;

      this.logger.debug('Data transformation complete', {
        duration: `${duration.toFixed(2)}ms`,
        resultSize: JSON.stringify(result).length
      });

      return result;
    } catch (error) {
      // Update metrics
      this.metrics.totalOperations++;
      this.metrics.failedOperations++;

      this.logger.error('Error transforming data using CSDE', {
        error: error.message,
        stack: error.stack
      });

      throw error;
    }
  }

  /**
   * Process a batch of data items
   * @param {Array} items - Data items to process
   * @param {Object} options - Processing options
   * @returns {Promise<Array>} - Processed items
   */
  async processBatch(items, options = {}) {
    this.logger.info(`Processing batch of ${items.length} items`);

    const startTime = performance.now();

    try {
      // Process items in parallel with concurrency limit
      const concurrency = options.concurrency || 5;
      const results = [];

      // Process items in chunks
      for (let i = 0; i < items.length; i += concurrency) {
        const chunk = items.slice(i, i + concurrency);

        // Process chunk in parallel
        const chunkPromises = chunk.map(item => this.transform(item, options));
        const chunkResults = await Promise.all(chunkPromises);

        // Add chunk results to overall results
        results.push(...chunkResults);
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      this.logger.info(`Batch processing complete`, {
        itemCount: items.length,
        duration: `${duration.toFixed(2)}ms`,
        averageItemDuration: `${(duration / items.length).toFixed(2)}ms`
      });

      return results;
    } catch (error) {
      this.logger.error('Error processing batch', {
        error: error.message,
        stack: error.stack
      });

      throw error;
    }
  }

  /**
   * Get connector health status
   * @returns {Promise<Object>} - Health status
   */
  async getHealth() {
    try {
      // Check CSDE API health
      const csdeMetrics = this.csdeIntegration.getMetrics();

      // Calculate success rate
      const successRate = this.metrics.totalOperations > 0
        ? this.metrics.successfulOperations / this.metrics.totalOperations
        : 1;

      // Determine health status
      const status = successRate >= 0.95 ? 'healthy' : 'degraded';

      return {
        status,
        metrics: {
          ...this.metrics,
          successRate,
          csdeMetrics
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Clear connector caches
   * @returns {Promise<void>}
   */
  async clearCache() {
    this.logger.info('Clearing CSDE connector cache');

    try {
      // Clear CSDE integration cache
      this.csdeIntegration.clearCache();

      return {
        success: true,
        message: 'Cache cleared successfully',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error clearing cache', {
        error: error.message,
        stack: error.stack
      });

      throw error;
    }
  }

  /**
   * Reset connector metrics
   * @returns {Promise<void>}
   */
  async resetMetrics() {
    this.logger.info('Resetting CSDE connector metrics');

    try {
      // Reset connector metrics
      this.metrics = {
        totalOperations: 0,
        successfulOperations: 0,
        failedOperations: 0,
        totalDuration: 0,
        averageDuration: 0
      };

      // Reset CSDE integration metrics
      this.csdeIntegration.resetMetrics();

      return {
        success: true,
        message: 'Metrics reset successfully',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error resetting metrics', {
        error: error.message,
        stack: error.stack
      });

      throw error;
    }
  }

  /**
   * Get connector configuration schema
   * @returns {Object} - Configuration schema
   */
  static getConfigSchema() {
    return {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'Connector ID'
        },
        name: {
          type: 'string',
          description: 'Connector name'
        },
        description: {
          type: 'string',
          description: 'Connector description'
        },
        csdeApiUrl: {
          type: 'string',
          description: 'CSDE API URL'
        },
        enableCaching: {
          type: 'boolean',
          description: 'Enable result caching'
        },
        enableMetrics: {
          type: 'boolean',
          description: 'Enable performance metrics'
        },
        cacheSize: {
          type: 'integer',
          description: 'Maximum cache size'
        },
        domain: {
          type: 'string',
          enum: ['general', 'security', 'compliance', 'finance', 'healthcare'],
          description: 'Domain-specific optimization'
        },
        enableOptimizedMapping: {
          type: 'boolean',
          description: 'Enable optimized data mapping'
        },
        enableParallelProcessing: {
          type: 'boolean',
          description: 'Enable parallel processing for large datasets'
        },
        enableDirectPropertyAccess: {
          type: 'boolean',
          description: 'Enable direct property access for faster mapping'
        },
        timeout: {
          type: 'integer',
          description: 'Request timeout in milliseconds'
        },
        maxConcurrentRequests: {
          type: 'integer',
          description: 'Maximum number of concurrent requests'
        },
        rules: {
          type: 'array',
          description: 'Default transformation rules',
          items: {
            type: 'object',
            properties: {
              source: {
                type: 'string',
                description: 'Source path'
              },
              target: {
                type: 'string',
                description: 'Target path'
              },
              transform: {
                oneOf: [
                  { type: 'string' },
                  { type: 'array', items: { type: 'string' } }
                ],
                description: 'Transformation to apply'
              },
              transformParams: {
                type: 'object',
                description: 'Transformation parameters'
              }
            },
            required: ['source', 'target']
          }
        }
      },
      required: ['id']
    };
  }
}

module.exports = CSEDConnector;
