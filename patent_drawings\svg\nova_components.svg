<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1466.65625 621" style="max-width: 1466.66px; background-color: white;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#333333;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#my-svg .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .uspto&gt;*{fill:#fff!important;stroke:#000!important;stroke-width:2px!important;}#my-svg .uspto span{fill:#fff!important;stroke:#000!important;stroke-width:2px!important;}#my-svg .reference200&gt;*{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference200 span{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference210&gt;*{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference210 span{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference220&gt;*{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference220 span{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference230&gt;*{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference230 span{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference240&gt;*{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference240 span{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .details&gt;*{fill:#f9f9f9!important;stroke:#ddd!important;stroke-width:1px!important;font-size:8pt!important;}#my-svg .details span{fill:#f9f9f9!important;stroke:#ddd!important;stroke-width:1px!important;font-size:8pt!important;}#my-svg .legend&gt;*{fill:#f0f0f0!important;stroke:#ccc!important;stroke-width:1px!important;}#my-svg .legend span{fill:#f0f0f0!important;stroke:#ccc!important;stroke-width:1px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"/><g class="edgePaths"/><g class="edgeLabels"/><g class="nodes"><g transform="translate(0, 0)" class="root"><g class="clusters"><g data-look="classic" id="NovaFuse_Components" class="cluster uspto"><rect height="605" width="224.078125" y="8" x="8" style="fill:#fff !important;stroke:#000 !important;stroke-width:2px !important"/><g transform="translate(20.0390625, 8)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>NovaFuse Components Architecture</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Core_Connection_0" d="M120.039,91.5L120.039,97.75C120.039,104,120.039,116.5,120.039,128.333C120.039,140.167,120.039,151.333,120.039,156.917L120.039,162.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Connection_Intelligence_0" d="M120.039,212.5L120.039,218.75C120.039,225,120.039,237.5,120.039,249.333C120.039,261.167,120.039,272.333,120.039,277.917L120.039,283.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Intelligence_Visualization_0" d="M120.039,333.5L120.039,339.75C120.039,346,120.039,358.5,120.039,370.333C120.039,382.167,120.039,393.333,120.039,398.917L120.039,404.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Visualization_Advanced_0" d="M120.039,454.5L120.039,460.75C120.039,467,120.039,479.5,120.039,491.333C120.039,503.167,120.039,514.333,120.039,519.917L120.039,525.5"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(120.0390625, 68.5)" id="flowchart-Core-0" class="node default uspto reference200"><rect height="46" width="115.625" y="-23" x="-57.8125" style="fill:none !important;stroke:none !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-27.8125, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="55.625"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>Core Trinity</p></span></div></foreignObject></g></g><g transform="translate(120.0390625, 189.5)" id="flowchart-Connection-1" class="node default uspto reference210"><rect height="46" width="147.21875" y="-23" x="-73.609375" style="fill:none !important;stroke:none !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-43.609375, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="87.21875"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>Connection Trinity</p></span></div></foreignObject></g></g><g transform="translate(120.0390625, 310.5)" id="flowchart-Intelligence-2" class="node default uspto reference220"><rect height="46" width="149.8125" y="-23" x="-74.90625" style="fill:none !important;stroke:none !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-44.90625, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="89.8125"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>Intelligence Trinity</p></span></div></foreignObject></g></g><g transform="translate(120.0390625, 431.5)" id="flowchart-Visualization-3" class="node default uspto reference230"><rect height="46" width="154.078125" y="-23" x="-77.0390625" style="fill:none !important;stroke:none !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-47.0390625, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="94.078125"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>Visualization Trinity</p></span></div></foreignObject></g></g><g transform="translate(120.0390625, 552.5)" id="flowchart-Advanced-4" class="node default uspto reference240"><rect height="46" width="139.484375" y="-23" x="-69.7421875" style="fill:none !important;stroke:none !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-39.7421875, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="79.484375"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>Advanced Trinity</p></span></div></foreignObject></g></g></g></g><g transform="translate(405.3671875, 310.5)" id="flowchart-Core_Details-15" class="node default details"><rect height="46" width="246.578125" y="-23" x="-123.2890625" style="fill:#f9f9f9 !important;stroke:#ddd !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-93.2890625, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="186.578125"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>• NovaCore\n• NovaSecure\n• NovaNet</p></span></div></foreignObject></g></g><g transform="translate(708.65625, 310.5)" id="flowchart-Connection_Details-16" class="node default details"><rect height="62" width="260" y="-31" x="-130" style="fill:#f9f9f9 !important;stroke:#ddd !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-100, -16)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="32" width="200"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;"><span class="nodeLabel" style="font-size:8pt !important"><p>Connection\n• NovaConnect\n• NovaSync\n• NovaNode</p></span></div></foreignObject></g></g><g transform="translate(1018.65625, 310.5)" id="flowchart-Intel_Details-17" class="node default details"><rect height="62" width="260" y="-31" x="-130" style="fill:#f9f9f9 !important;stroke:#ddd !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-100, -16)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="32" width="200"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;"><span class="nodeLabel" style="font-size:8pt !important"><p>Intelligence\n• NovaLearn\n• NovaPredict\n• NovaOrchestrate</p></span></div></foreignObject></g></g><g transform="translate(1328.65625, 310.5)" id="flowchart-Legend-18" class="node default legend"><rect height="78" width="260" y="-39" x="-130" style="fill:#f0f0f0 !important;stroke:#ccc !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>NovaFuse Components Architecture</p></span></div></foreignObject></g></g></g></g></g></svg>