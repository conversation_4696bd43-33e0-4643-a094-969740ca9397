/**
 * Authentication Service
 *
 * This service handles user authentication and authorization.
 */

const fs = require('fs').promises;
const path = require('path');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, AuthenticationError, AuthorizationError } = require('../utils/errors');

class AuthService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.usersFile = path.join(this.dataDir, 'users.json');
    this.tokensFile = path.join(this.dataDir, 'tokens.json');
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key-for-development-only';
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '24h';
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Load users from file
   */
  async loadUsers() {
    try {
      const data = await fs.readFile(this.usersFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return default admin user
        return [
          {
            id: '1',
            username: 'admin',
            password: await bcrypt.hash('admin', 10),
            email: '<EMAIL>',
            role: 'admin',
            permissions: ['*'],
            created: new Date().toISOString(),
            lastLogin: null
          }
        ];
      }
      console.error('Error loading users:', error);
      throw error;
    }
  }

  /**
   * Save users to file
   */
  async saveUsers(users) {
    try {
      await fs.writeFile(this.usersFile, JSON.stringify(users, null, 2));
    } catch (error) {
      console.error('Error saving users:', error);
      throw error;
    }
  }

  /**
   * Load tokens from file
   */
  async loadTokens() {
    try {
      const data = await fs.readFile(this.tokensFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error('Error loading tokens:', error);
      throw error;
    }
  }

  /**
   * Save tokens to file
   */
  async saveTokens(tokens) {
    try {
      await fs.writeFile(this.tokensFile, JSON.stringify(tokens, null, 2));
    } catch (error) {
      console.error('Error saving tokens:', error);
      throw error;
    }
  }

  /**
   * Register a new user
   */
  async register(userData) {
    if (!userData.username) {
      throw new ValidationError('Username is required');
    }

    if (!userData.password) {
      throw new ValidationError('Password is required');
    }

    if (!userData.email) {
      throw new ValidationError('Email is required');
    }

    const users = await this.loadUsers();

    // Check if username already exists
    if (users.some(user => user.username === userData.username)) {
      throw new ValidationError('Username already exists');
    }

    // Check if email already exists
    if (users.some(user => user.email === userData.email)) {
      throw new ValidationError('Email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(userData.password, 10);

    // Create new user
    const newUser = {
      id: uuidv4(),
      username: userData.username,
      password: hashedPassword,
      email: userData.email,
      role: userData.role || 'user',
      permissions: userData.permissions || ['read'],
      created: new Date().toISOString(),
      lastLogin: null
    };

    users.push(newUser);
    await this.saveUsers(users);

    // Return user without password
    const { password, ...userWithoutPassword } = newUser;
    return userWithoutPassword;
  }

  /**
   * Login a user
   */
  async login(username, password) {
    if (!username) {
      throw new ValidationError('Username is required');
    }

    if (!password) {
      throw new ValidationError('Password is required');
    }

    const users = await this.loadUsers();

    // Find user by username
    const user = users.find(user => user.username === username);

    if (!user) {
      throw new AuthenticationError('Invalid username or password');
    }

    // Check password
    const passwordMatch = await bcrypt.compare(password, user.password);

    if (!passwordMatch) {
      throw new AuthenticationError('Invalid username or password');
    }

    // Update last login
    user.lastLogin = new Date().toISOString();
    await this.saveUsers(users);

    // Check if 2FA is enabled
    if (user.twoFactorEnabled) {
      // Return user ID for 2FA verification
      return {
        requiresTwoFactor: true,
        userId: user.id,
        user: {
          id: user.id,
          username: user.username,
          email: user.email
        }
      };
    }

    // Generate JWT tokens
    const accessToken = this.generateToken(user);
    const refreshToken = this.generateRefreshToken(user);

    // Save tokens
    await this.saveToken(accessToken, user.id, 'access');
    await this.saveToken(refreshToken, user.id, 'refresh');

    // Return user and tokens
    const { password: _, ...userWithoutPassword } = user;
    return {
      user: userWithoutPassword,
      token: accessToken,
      refreshToken,
      expiresIn: this.jwtExpiresIn,
      authMethod: 'password'
    };
  }

  /**
   * Login with OAuth2
   */
  async loginWithOAuth2(userData, providerId, providerUserId) {
    const users = await this.loadUsers();

    // Find user by provider info
    let user = users.find(user =>
      user.providers &&
      user.providers.some(p => p.providerId === providerId && p.providerUserId === providerUserId)
    );

    if (!user) {
      // Check if user exists with the same email
      user = users.find(user => user.email === userData.email);

      if (user) {
        // Link provider to existing user
        if (!user.providers) {
          user.providers = [];
        }

        user.providers.push({
          providerId,
          providerUserId,
          linked: new Date().toISOString()
        });
      } else {
        // Create new user
        user = {
          id: uuidv4(),
          username: userData.username || userData.email,
          email: userData.email,
          firstName: userData.firstName || '',
          lastName: userData.lastName || '',
          role: 'user',
          permissions: ['read'],
          created: new Date().toISOString(),
          lastLogin: new Date().toISOString(),
          providers: [
            {
              providerId,
              providerUserId,
              linked: new Date().toISOString()
            }
          ]
        };

        users.push(user);
      }

      // Save updated users
      await this.saveUsers(users);
    } else {
      // Update last login
      user.lastLogin = new Date().toISOString();
      await this.saveUsers(users);
    }

    // Generate JWT tokens
    const accessToken = this.generateToken(user);
    const refreshToken = this.generateRefreshToken(user);

    // Save tokens
    await this.saveToken(accessToken, user.id, 'access');
    await this.saveToken(refreshToken, user.id, 'refresh');

    // Return user and tokens
    const { password, ...userWithoutPassword } = user;
    return {
      user: userWithoutPassword,
      token: accessToken,
      refreshToken,
      expiresIn: this.jwtExpiresIn,
      authMethod: 'oauth2',
      provider: {
        id: providerId
      }
    };
  }

  /**
   * Generate JWT token
   */
  generateToken(user) {
    const payload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      permissions: user.permissions
    };

    return jwt.sign(payload, this.jwtSecret, { expiresIn: this.jwtExpiresIn });
  }

  /**
   * Save token
   * @param {string} token - The token to save
   * @param {string} userId - The user ID associated with the token
   * @param {string} type - The token type (access or refresh)
   */
  async saveToken(token, userId, type = 'access') {
    const tokens = await this.loadTokens();

    // Decode token to get expiration
    const decoded = jwt.decode(token);

    tokens.push({
      token,
      userId,
      type,
      created: new Date().toISOString(),
      expires: new Date(decoded.exp * 1000).toISOString()
    });

    await this.saveTokens(tokens);
  }

  /**
   * Verify token
   */
  async verifyToken(token) {
    if (!token) {
      throw new AuthenticationError('Token is required');
    }

    try {
      // Verify JWT signature and expiration
      const decoded = jwt.verify(token, this.jwtSecret);

      // Check if token is in the database
      const tokens = await this.loadTokens();
      const tokenExists = tokens.some(t => t.token === token);

      if (!tokenExists) {
        throw new AuthenticationError('Invalid token');
      }

      return decoded;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new AuthenticationError('Token expired');
      }

      if (error.name === 'JsonWebTokenError') {
        throw new AuthenticationError('Invalid token');
      }

      throw error;
    }
  }

  /**
   * Logout a user
   */
  async logout(token) {
    if (!token) {
      throw new ValidationError('Token is required');
    }

    const tokens = await this.loadTokens();
    const filteredTokens = tokens.filter(t => t.token !== token);

    if (tokens.length === filteredTokens.length) {
      throw new ValidationError('Token not found');
    }

    await this.saveTokens(filteredTokens);

    return { success: true, message: 'Logged out successfully' };
  }

  /**
   * Get user by ID
   */
  async getUserById(id) {
    const users = await this.loadUsers();
    const user = users.find(user => user.id === id);

    if (!user) {
      throw new ValidationError(`User with ID ${id} not found`);
    }

    // Return user without password
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  /**
   * Get all users
   */
  async getAllUsers() {
    const users = await this.loadUsers();

    // Return users without passwords
    return users.map(user => {
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    });
  }

  /**
   * Update user
   */
  async updateUser(id, userData) {
    const users = await this.loadUsers();
    const index = users.findIndex(user => user.id === id);

    if (index === -1) {
      throw new ValidationError(`User with ID ${id} not found`);
    }

    // Don't allow updating username
    if (userData.username && userData.username !== users[index].username) {
      throw new ValidationError('Username cannot be changed');
    }

    // Hash password if provided
    if (userData.password) {
      userData.password = await bcrypt.hash(userData.password, 10);
    }

    // Update user
    const updatedUser = {
      ...users[index],
      ...userData,
      updated: new Date().toISOString()
    };

    users[index] = updatedUser;
    await this.saveUsers(users);

    // Return user without password
    const { password, ...userWithoutPassword } = updatedUser;
    return userWithoutPassword;
  }

  /**
   * Delete user
   */
  async deleteUser(id) {
    const users = await this.loadUsers();
    const filteredUsers = users.filter(user => user.id !== id);

    if (users.length === filteredUsers.length) {
      throw new ValidationError(`User with ID ${id} not found`);
    }

    await this.saveUsers(filteredUsers);

    // Delete user tokens
    const tokens = await this.loadTokens();
    const filteredTokens = tokens.filter(token => token.userId !== id);
    await this.saveTokens(filteredTokens);

    return { success: true, message: `User with ID ${id} deleted` };
  }

  /**
   * Check if user has permission
   */
  hasPermission(user, permission) {
    if (!user || !user.permissions) {
      return false;
    }

    // Admin has all permissions
    if (user.role === 'admin' || user.permissions.includes('*')) {
      return true;
    }

    return user.permissions.includes(permission);
  }

  /**
   * Check if user has role
   */
  hasRole(user, role) {
    if (!user) {
      return false;
    }

    // Admin has all roles
    if (user.role === 'admin') {
      return true;
    }

    return user.role === role;
  }

  /**
   * Generate refresh token
   */
  generateRefreshToken(user) {
    const payload = {
      sub: user.id,
      type: 'refresh'
    };

    // Refresh tokens have a longer expiration time
    const refreshExpiresIn = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

    return jwt.sign(payload, this.jwtSecret, { expiresIn: refreshExpiresIn });
  }

  /**
   * Refresh token
   *
   * This method takes a refresh token and returns a new access token
   */
  async refreshToken(refreshToken) {
    if (!refreshToken) {
      throw new ValidationError('Refresh token is required');
    }

    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, this.jwtSecret);

      // Check if token is in the database
      const tokens = await this.loadTokens();
      const tokenRecord = tokens.find(t => t.token === refreshToken && t.type === 'refresh');

      if (!tokenRecord) {
        throw new AuthenticationError('Invalid refresh token');
      }

      // Get user
      const userId = decoded.sub;
      const users = await this.loadUsers();
      const user = users.find(u => u.id === userId);

      if (!user) {
        throw new AuthenticationError('User not found');
      }

      // Generate new access token
      const accessToken = this.generateToken(user);

      // Save new access token
      await this.saveToken(accessToken, user.id, 'access');

      // Return new access token
      return {
        token: accessToken,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          permissions: user.permissions
        }
      };
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new AuthenticationError('Refresh token expired');
      }

      if (error.name === 'JsonWebTokenError') {
        throw new AuthenticationError('Invalid refresh token');
      }

      throw error;
    }
  }
}

module.exports = AuthService;
