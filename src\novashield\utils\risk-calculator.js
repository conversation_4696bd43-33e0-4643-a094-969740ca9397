/**
 * NovaShield - Risk Calculator
 * 
 * This utility provides risk calculation functions.
 */

const { createLogger } = require('../../utils/logger');

const logger = createLogger('risk-calculator');

/**
 * Calculate risk score for a vendor
 * 
 * @param {Object} vendor - Vendor object
 * @param {Object} options - Calculation options
 * @returns {number} - Risk score (0-100)
 */
function calculateRiskScore(vendor, options = {}) {
  logger.debug('Calculating risk score', { vendorId: vendor.id });
  
  // Base weights for different risk factors
  const weights = {
    criticality: 0.25,
    dataAccess: 0.20,
    complianceStatus: 0.15,
    financialStability: 0.10,
    contractStatus: 0.05,
    threatIntelligence: 0.25
  };
  
  // Calculate criticality score (0-100)
  const criticalityScore = calculateCriticalityScore(vendor);
  
  // Calculate data access score (0-100)
  const dataAccessScore = calculateDataAccessScore(vendor);
  
  // Calculate compliance status score (0-100)
  const complianceScore = calculateComplianceScore(vendor);
  
  // Calculate financial stability score (0-100)
  const financialScore = calculateFinancialScore(vendor);
  
  // Calculate contract status score (0-100)
  const contractScore = calculateContractScore(vendor);
  
  // Calculate threat intelligence score (0-100)
  const threatScore = calculateThreatScore(options.threatIntelligence);
  
  // Calculate weighted score
  const weightedScore = (
    criticalityScore * weights.criticality +
    dataAccessScore * weights.dataAccess +
    complianceScore * weights.complianceStatus +
    financialScore * weights.financialStability +
    contractScore * weights.contractStatus +
    threatScore * weights.threatIntelligence
  );
  
  // Round to nearest integer
  return Math.round(weightedScore);
}

/**
 * Calculate criticality score
 * 
 * @param {Object} vendor - Vendor object
 * @returns {number} - Criticality score (0-100)
 * @private
 */
function calculateCriticalityScore(vendor) {
  const criticalityMap = {
    'critical': 100,
    'high': 80,
    'medium': 50,
    'low': 20,
    'minimal': 5
  };
  
  return criticalityMap[vendor.criticality] || 50;
}

/**
 * Calculate data access score
 * 
 * @param {Object} vendor - Vendor object
 * @returns {number} - Data access score (0-100)
 * @private
 */
function calculateDataAccessScore(vendor) {
  const dataAccessMap = {
    'extensive': 100,
    'sensitive': 80,
    'limited': 40,
    'minimal': 10,
    'none': 0
  };
  
  return dataAccessMap[vendor.dataAccess] || 40;
}

/**
 * Calculate compliance score
 * 
 * @param {Object} vendor - Vendor object
 * @returns {number} - Compliance score (0-100)
 * @private
 */
function calculateComplianceScore(vendor) {
  const complianceMap = {
    'non-compliant': 100,
    'partial': 60,
    'compliant': 10,
    'unknown': 75
  };
  
  return complianceMap[vendor.complianceStatus] || 75;
}

/**
 * Calculate financial stability score
 * 
 * @param {Object} vendor - Vendor object
 * @returns {number} - Financial stability score (0-100)
 * @private
 */
function calculateFinancialScore(vendor) {
  const financialMap = {
    'unstable': 100,
    'concerning': 75,
    'stable': 30,
    'very-stable': 10,
    'unknown': 50
  };
  
  return financialMap[vendor.financialStability] || 50;
}

/**
 * Calculate contract status score
 * 
 * @param {Object} vendor - Vendor object
 * @returns {number} - Contract status score (0-100)
 * @private
 */
function calculateContractScore(vendor) {
  const contractMap = {
    'expired': 100,
    'expiring-soon': 70,
    'active': 20,
    'pending': 50,
    'unknown': 60
  };
  
  return contractMap[vendor.contractStatus] || 60;
}

/**
 * Calculate threat intelligence score
 * 
 * @param {Array|null} threatIntelligence - Threat intelligence data
 * @returns {number} - Threat intelligence score (0-100)
 * @private
 */
function calculateThreatScore(threatIntelligence) {
  if (!threatIntelligence || !Array.isArray(threatIntelligence) || threatIntelligence.length === 0) {
    return 0;
  }
  
  // Calculate score based on threat severity
  const severityScores = {
    'critical': 100,
    'high': 80,
    'medium': 50,
    'low': 20,
    'info': 5
  };
  
  // Calculate average severity score
  const totalScore = threatIntelligence.reduce((sum, threat) => {
    return sum + (severityScores[threat.severity] || 0);
  }, 0);
  
  return Math.round(totalScore / threatIntelligence.length);
}

/**
 * Identify risk factors for a vendor
 * 
 * @param {Object} vendor - Vendor object
 * @param {Object} options - Identification options
 * @returns {Array} - List of risk factors
 */
function identifyRiskFactors(vendor, options = {}) {
  logger.debug('Identifying risk factors', { vendorId: vendor.id });
  
  const riskFactors = [];
  
  // Check criticality
  if (vendor.criticality === 'critical' || vendor.criticality === 'high') {
    riskFactors.push({
      id: 'high-criticality',
      name: 'High Business Criticality',
      category: 'operational',
      severity: 'high',
      description: 'Vendor provides critical business services',
      impact: 25
    });
  }
  
  // Check data access
  if (vendor.dataAccess === 'extensive' || vendor.dataAccess === 'sensitive') {
    riskFactors.push({
      id: 'sensitive-data-access',
      name: 'Sensitive Data Access',
      category: 'data-security',
      severity: 'high',
      description: 'Vendor has access to sensitive data',
      impact: 20
    });
  }
  
  // Check compliance status
  if (vendor.complianceStatus === 'non-compliant' || vendor.complianceStatus === 'partial') {
    riskFactors.push({
      id: 'compliance-issues',
      name: 'Compliance Issues',
      category: 'compliance',
      severity: 'high',
      description: 'Vendor has compliance issues',
      impact: 15
    });
  }
  
  // Check financial stability
  if (vendor.financialStability === 'unstable' || vendor.financialStability === 'concerning') {
    riskFactors.push({
      id: 'financial-instability',
      name: 'Financial Instability',
      category: 'financial',
      severity: 'medium',
      description: 'Vendor has financial stability concerns',
      impact: 10
    });
  }
  
  // Check contract status
  if (vendor.contractStatus === 'expired' || vendor.contractStatus === 'expiring-soon') {
    riskFactors.push({
      id: 'contract-issues',
      name: 'Contract Issues',
      category: 'operational',
      severity: 'medium',
      description: 'Vendor contract is expired or expiring soon',
      impact: 5
    });
  }
  
  // Check threat intelligence
  if (options.threatIntelligence && Array.isArray(options.threatIntelligence)) {
    options.threatIntelligence.forEach(threat => {
      if (threat.severity === 'critical' || threat.severity === 'high') {
        riskFactors.push({
          id: `threat-${threat.id}`,
          name: `Security Threat: ${threat.type}`,
          category: 'security',
          severity: 'high',
          description: threat.description,
          impact: 25
        });
      }
    });
  }
  
  return riskFactors;
}

module.exports = {
  calculateRiskScore,
  identifyRiskFactors
};
