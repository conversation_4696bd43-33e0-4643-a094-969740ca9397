b4e566fac4d51927fb8352df0700a08a
/**
 * NovaFuse Universal API Connector Security Middleware Tests
 * 
 * This module contains tests for the security middleware.
 */

const {
  createRateLimiter,
  createInputValidator,
  createSecurityHeaders
} = require('../../../api/middleware/security-middleware');
const express = require('express');
const request = require('supertest');
describe('Security Middleware', () => {
  describe('Rate Limiter', () => {
    let app;
    beforeEach(() => {
      app = express();
      app.use(express.json());

      // Add rate limiter middleware
      app.use(createRateLimiter({
        windowMs: 1000,
        // 1 second
        maxRequests: 3 // 3 requests per second
      }));

      // Add test route
      app.get('/test', (req, res) => {
        res.status(200).json({
          message: 'Success'
        });
      });
    });
    test('should allow requests within rate limit', async () => {
      // Make 3 requests (within limit)
      for (let i = 0; i < 3; i++) {
        const response = await request(app).get('/test');
        expect(response.status).toBe(200);
        expect(response.body).toEqual({
          message: 'Success'
        });
        expect(response.headers['x-ratelimit-limit']).toBe('3');
        expect(parseInt(response.headers['x-ratelimit-remaining'])).toBeLessThanOrEqual(3 - i - 1);
      }
    });
    test('should block requests exceeding rate limit', async () => {
      // Make 3 requests (within limit)
      for (let i = 0; i < 3; i++) {
        const response = await request(app).get('/test');
        expect(response.status).toBe(200);
      }

      // Make 4th request (exceeding limit)
      const response = await request(app).get('/test');
      expect(response.status).toBe(429);
      expect(response.body).toEqual({
        error: 'Too many requests',
        message: 'Rate limit exceeded. Please try again later.'
      });
      expect(response.headers['x-ratelimit-limit']).toBe('3');
      expect(response.headers['x-ratelimit-remaining']).toBe('0');
      expect(response.headers['retry-after']).toBeDefined();
    });
    test('should reset rate limit after window', async () => {
      // Make 3 requests (within limit)
      for (let i = 0; i < 3; i++) {
        const response = await request(app).get('/test');
        expect(response.status).toBe(200);
      }

      // Make 4th request (exceeding limit)
      const response = await request(app).get('/test');
      expect(response.status).toBe(429);

      // Wait for rate limit window to reset
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Make another request (should be allowed)
      const newResponse = await request(app).get('/test');
      expect(newResponse.status).toBe(200);
      expect(newResponse.body).toEqual({
        message: 'Success'
      });
    });
  });
  describe('Input Validator', () => {
    let app;
    beforeEach(() => {
      app = express();
      app.use(express.json());

      // Add input validator middleware
      app.use(createInputValidator());

      // Add test routes
      app.get('/test/:id', (req, res) => {
        res.status(200).json({
          id: req.params.id
        });
      });
      app.get('/query', (req, res) => {
        res.status(200).json({
          query: req.query
        });
      });
      app.post('/body', (req, res) => {
        res.status(200).json({
          body: req.body
        });
      });
    });
    test('should allow valid path parameters', async () => {
      const response = await request(app).get('/test/123');
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        id: '123'
      });
    });
    test('should block path parameters with XSS', async () => {
      const response = await request(app).get('/test/<script>alert(1)</script>');
      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid parameter');
    });
    test('should allow valid query parameters', async () => {
      const response = await request(app).get('/query?name=test&age=25');
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        query: {
          name: 'test',
          age: '25'
        }
      });
    });
    test('should block query parameters with XSS', async () => {
      const response = await request(app).get('/query?name=<script>alert(1)</script>');
      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid parameter');
    });
    test('should allow valid body parameters', async () => {
      const response = await request(app).post('/body').send({
        name: 'test',
        age: 25
      });
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        body: {
          name: 'test',
          age: 25
        }
      });
    });
    test('should block body parameters with XSS', async () => {
      const response = await request(app).post('/body').send({
        name: '<script>alert(1)</script>'
      });
      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid request body');
    });
    test('should block body parameters with command injection', async () => {
      const response = await request(app).post('/body').send({
        command: 'rm -rf /'
      });
      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid request body');
    });
    test('should validate nested objects', async () => {
      const response = await request(app).post('/body').send({
        user: {
          name: 'test',
          profile: {
            bio: '<script>alert(1)</script>'
          }
        }
      });
      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid request body');
    });
  });
  describe('Security Headers', () => {
    let app;
    beforeEach(() => {
      app = express();

      // Add security headers middleware
      app.use(createSecurityHeaders());

      // Add test route
      app.get('/test', (req, res) => {
        res.status(200).json({
          message: 'Success'
        });
      });
    });
    test('should add security headers to response', async () => {
      const response = await request(app).get('/test');
      expect(response.status).toBe(200);
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['strict-transport-security']).toBe('max-age=31536000; includeSubDomains');
      expect(response.headers['content-security-policy']).toBe("default-src 'self'");
      expect(response.headers['referrer-policy']).toBe('no-referrer');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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