# NovaFuse Privacy Management API - User Guide

## Introduction

The NovaFuse Privacy Management API provides a comprehensive solution for managing privacy compliance across multiple systems and jurisdictions. This user guide will help you understand how to use the API effectively to manage data subject requests, consent records, privacy notices, data breaches, and regulatory compliance.

## Getting Started

### Authentication

All API requests (except for authentication endpoints) require a valid JWT token. To obtain a token, make a POST request to the authentication endpoint:

```http
POST /privacy/management/auth/login
Content-Type: application/json

{
  "username": "your-username",
  "password": "your-password"
}
```

The response will include a JWT token that should be included in the Authorization header of all subsequent requests:

```http
Authorization: Bearer your-jwt-token
```

### Base URL

The base URL for all API requests is:

```
https://api.novafuse.com/privacy/management
```

For local development, use:

```
http://localhost:3000/privacy/management
```

## Core Features

### 1. Data Processing Activities

Data processing activities represent the various ways your organization processes personal data. The API allows you to create, read, update, and delete data processing activities.

#### List Data Processing Activities

```http
GET /processing-activities
```

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `status`: Filter by status (active, inactive, archived)
- `search`: Search term
- `legalBasis`: Filter by legal basis
- `sortBy`: Field to sort by
- `sortOrder`: Sort order (asc, desc)

#### Get a Specific Data Processing Activity

```http
GET /processing-activities/{id}
```

#### Create a Data Processing Activity

```http
POST /processing-activities
Content-Type: application/json

{
  "name": "Customer Data Processing",
  "description": "Processing of customer data for service delivery",
  "purpose": "Service Delivery",
  "dataCategories": ["Contact Information", "Payment Details"],
  "dataSubjects": ["Customers"],
  "legalBasis": "contract",
  "retentionPeriod": "7 years",
  "status": "active"
}
```

#### Update a Data Processing Activity

```http
PUT /processing-activities/{id}
Content-Type: application/json

{
  "name": "Updated Customer Data Processing",
  "description": "Updated description",
  "status": "inactive"
}
```

#### Delete a Data Processing Activity

```http
DELETE /processing-activities/{id}
```

### 2. Data Subject Requests

Data subject requests allow individuals to exercise their privacy rights, such as the right to access, rectify, or erase their personal data.

#### List Data Subject Requests

```http
GET /subject-requests
```

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `status`: Filter by status (pending, in-progress, completed, rejected, withdrawn)
- `requestType`: Filter by request type
- `search`: Search term
- `startDate`: Filter by creation date (start)
- `endDate`: Filter by creation date (end)

#### Get a Specific Data Subject Request

```http
GET /subject-requests/{id}
```

#### Create a Data Subject Request

```http
POST /subject-requests
Content-Type: application/json

{
  "requestType": "access",
  "dataSubjectName": "John Doe",
  "dataSubjectEmail": "<EMAIL>",
  "requestDetails": "I would like to access all my personal data",
  "status": "pending",
  "dueDate": "2023-12-31T23:59:59Z"
}
```

#### Update a Data Subject Request

```http
PUT /subject-requests/{id}
Content-Type: application/json

{
  "status": "in-progress",
  "assignedTo": "<EMAIL>"
}
```

#### Process a Data Subject Request

```http
POST /subject-requests/{id}/process
```

This endpoint will automatically process the request across all integrated systems.

#### Generate a Data Export

```http
GET /subject-requests/{id}/export
```

This endpoint generates a comprehensive data export for access requests.

#### Determine Affected Systems

```http
GET /subject-requests/{id}/affected-systems
```

This endpoint identifies all systems that may contain the data subject's personal data.

### 3. Consent Management

The consent management feature allows you to track and manage user consent across systems.

#### List Consent Records

```http
GET /consent-records
```

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `status`: Filter by status (active, withdrawn, expired)
- `search`: Search term
- `dataSubjectId`: Filter by data subject ID
- `email`: Filter by email

#### Get a Specific Consent Record

```http
GET /consent-records/{id}
```

#### Create a Consent Record

```http
POST /consent-records
Content-Type: application/json

{
  "dataSubjectId": "ds-123456",
  "dataSubjectName": "John Doe",
  "dataSubjectEmail": "<EMAIL>",
  "consentType": "marketing",
  "consentDetails": "Email marketing communications",
  "collectionMethod": "web-form",
  "collectionTimestamp": "2023-01-01T12:00:00Z",
  "status": "active",
  "expiryDate": "2024-01-01T12:00:00Z"
}
```

#### Update a Consent Record

```http
PUT /consent-records/{id}
Content-Type: application/json

{
  "status": "withdrawn",
  "withdrawalTimestamp": "2023-06-01T12:00:00Z",
  "withdrawalMethod": "email-request"
}
```

#### Generate a Consent Form

```http
GET /consent/forms?consentType=marketing
```

This endpoint generates a consent form for the specified consent type.

#### Verify Consent Validity

```http
GET /consent/{id}/validity
```

This endpoint checks if a consent record is valid.

#### Withdraw Consent

```http
POST /consent/{id}/withdraw
Content-Type: application/json

{
  "withdrawalMethod": "email-request",
  "withdrawalReason": "No longer interested"
}
```

#### Get Consent Records by Data Subject

```http
GET /consent/data-subjects/{dataSubjectId}
```

#### Get Consent Records by Email

```http
GET /consent/emails/{email}
```

#### Verify Consent Proof

```http
POST /consent/proof/verify
Content-Type: application/json

{
  "consentProof": "proof-data"
}
```

#### Generate Consent Proof

```http
POST /consent/proof/generate
Content-Type: application/json

{
  "consentId": "consent-123456",
  "proofType": "hash"
}
```

### 4. Privacy Notices

Privacy notices inform individuals about how their personal data is processed.

#### List Privacy Notices

```http
GET /privacy-notices
```

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `status`: Filter by status (active, archived)
- `search`: Search term
- `version`: Filter by version

#### Get a Specific Privacy Notice

```http
GET /privacy-notices/{id}
```

#### Create a Privacy Notice

```http
POST /privacy-notices
Content-Type: application/json

{
  "title": "Website Privacy Notice",
  "content": "This privacy notice explains how we process your personal data...",
  "version": "1.0",
  "effectiveDate": "2023-01-01T00:00:00Z",
  "status": "active",
  "audience": "website-visitors",
  "language": "en"
}
```

#### Update a Privacy Notice

```http
PUT /privacy-notices/{id}
Content-Type: application/json

{
  "content": "Updated privacy notice content...",
  "version": "1.1",
  "effectiveDate": "2023-07-01T00:00:00Z"
}
```

#### Delete a Privacy Notice

```http
DELETE /privacy-notices/{id}
```

### 5. Data Breaches

The data breach management feature allows you to document and respond to data breaches.

#### List Data Breaches

```http
GET /data-breaches
```

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `status`: Filter by status (detected, investigating, contained, resolved)
- `search`: Search term
- `severity`: Filter by severity (low, medium, high, critical)
- `startDate`: Filter by detection date (start)
- `endDate`: Filter by detection date (end)

#### Get a Specific Data Breach

```http
GET /data-breaches/{id}
```

#### Create a Data Breach

```http
POST /data-breaches
Content-Type: application/json

{
  "title": "Unauthorized Access to Customer Database",
  "description": "Unauthorized access to customer database detected",
  "detectionDate": "2023-06-15T08:30:00Z",
  "affectedDataCategories": ["Contact Information", "Payment Details"],
  "affectedDataSubjects": ["Customers"],
  "potentialImpact": "Exposure of customer contact and payment information",
  "containmentMeasures": "Database access revoked, passwords reset",
  "severity": "high",
  "status": "contained",
  "notificationRequired": true,
  "supervisoryAuthorityNotified": true,
  "dataSubjectsNotified": false
}
```

#### Update a Data Breach

```http
PUT /data-breaches/{id}
Content-Type: application/json

{
  "status": "resolved",
  "resolutionDate": "2023-06-20T16:45:00Z",
  "resolutionMeasures": "Security patches applied, additional monitoring implemented",
  "dataSubjectsNotified": true,
  "dataSubjectNotificationDate": "2023-06-18T10:00:00Z"
}
```

#### Delete a Data Breach

```http
DELETE /data-breaches/{id}
```

#### Send Breach Notifications

```http
POST /data-breaches/{id}/notify
Content-Type: application/json

{
  "notificationType": "data-subjects",
  "notificationMethod": "email",
  "notificationContent": "We regret to inform you of a data breach..."
}
```

### 6. Integrations

The integrations feature allows you to connect with external systems where personal data might be stored.

#### List Available Integrations

```http
GET /integrations
```

#### Get a Specific Integration

```http
GET /integrations/{id}
```

#### Execute an Integration Action

```http
POST /integrations/{id}/{action}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "dataCategories": ["contacts", "emails"]
}
```

Available actions:
- `data-export`: Export data from the integration
- `data-deletion`: Delete data from the integration
- `data-update`: Update data in the integration
- `notifications`: Send notifications through the integration

### 7. Notifications

The notification system allows you to manage privacy-related notifications.

#### List Notifications

```http
GET /notifications
```

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `status`: Filter by status (pending, sent, read)
- `recipient`: Filter by recipient
- `type`: Filter by notification type
- `priority`: Filter by priority (low, medium, high)
- `startDate`: Filter by creation date (start)
- `endDate`: Filter by creation date (end)

#### Get a Specific Notification

```http
GET /notifications/{id}
```

#### Create a Notification

```http
POST /notifications
Content-Type: application/json

{
  "recipient": "<EMAIL>",
  "subject": "New Data Subject Request",
  "content": "A new data subject request has been submitted",
  "type": "dsr",
  "priority": "medium",
  "status": "pending",
  "relatedEntityType": "subject-request",
  "relatedEntityId": "sr-123456"
}
```

#### Mark a Notification as Read

```http
POST /notifications/{id}/read
```

#### Send a Notification

```http
POST /notifications/{id}/send
```

#### Generate Notifications

```http
POST /notifications/generate
Content-Type: application/json

{
  "notificationType": "dsr",
  "filter": {
    "status": "pending",
    "dueWithin": 7
  }
}
```

#### Send All Pending Notifications

```http
POST /notifications/send-all
```

### 8. Reports and Analytics

The reports and analytics feature provides insights into your privacy operations.

#### Generate a Report

```http
GET /reports/{reportType}
```

Query parameters:
- `period`: Time period (last-7-days, last-30-days, last-90-days, last-12-months, year-to-date, custom)
- `startDate`: Start date (required if period is custom)
- `endDate`: End date (required if period is custom)
- `groupBy`: Group by field

Available report types:
- `dsr-summary`: Data subject requests summary
- `consent-management`: Consent management report
- `data-breach`: Data breach report
- `processing-activities`: Processing activities report
- `compliance-status`: Compliance status report

#### Get Dashboard Metrics

```http
GET /dashboard/metrics
```

This endpoint returns metrics for the dashboard, including:
- DSR metrics (count by type, status, average resolution time)
- Consent metrics (active consents, withdrawals, expiring soon)
- Data breach metrics (count by severity, status, notification status)
- Compliance metrics (compliance score, gaps, upcoming deadlines)

## Advanced Features

### 1. Regulatory Compliance Integration

The API integrates with the Regulatory Compliance API to provide compliance information for data processing activities.

To include compliance information when retrieving a data processing activity, add the following query parameters:
- `includeCompliance=true`: Include compliance status information
- `includeRequirements=true`: Include relevant regulatory requirements
- `includeChanges=true`: Include relevant regulatory changes

### 2. Automated Privacy Impact Assessments

The API provides automated privacy impact assessments for data processing activities.

To include a privacy impact assessment when retrieving a data processing activity, add the following query parameter:
- `includeImpactAssessment=true`: Include automated privacy impact assessment

### 3. Data Systems Management

The API provides information about data systems where personal data might be stored.

```http
GET /data-systems
```

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `requestType`: Filter by supported request type (access, rectification, erasure, etc.)
- `search`: Search term

```http
GET /data-systems/{id}
```

## Error Handling

The API returns standard HTTP status codes to indicate the success or failure of a request:

- `200 OK`: The request was successful
- `201 Created`: The resource was successfully created
- `400 Bad Request`: The request was invalid or malformed
- `401 Unauthorized`: Authentication failed or token expired
- `403 Forbidden`: The authenticated user does not have permission to access the resource
- `404 Not Found`: The requested resource was not found
- `500 Internal Server Error`: An error occurred on the server

Error responses include a JSON object with the following structure:

```json
{
  "error": "Error Type",
  "message": "Detailed error message"
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse. The default rate limit is 100 requests per 15-minute window per IP address. If you exceed the rate limit, you will receive a `429 Too Many Requests` response.

The response headers include information about the rate limit:

- `X-RateLimit-Limit`: The maximum number of requests allowed in the current window
- `X-RateLimit-Remaining`: The number of requests remaining in the current window
- `X-RateLimit-Reset`: The time (in seconds) when the current window resets

## Pagination

List endpoints support pagination with the following query parameters:

- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)

The response includes pagination information:

```json
{
  "data": [...],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "pages": 10
  }
}
```

## Filtering and Sorting

List endpoints support filtering and sorting with the following query parameters:

- `search`: Search term (searches across multiple fields)
- `sortBy`: Field to sort by
- `sortOrder`: Sort order (asc, desc)

Additional filter parameters are available for specific endpoints, as documented above.

## Conclusion

This user guide provides a comprehensive overview of the NovaFuse Privacy Management API. For more detailed information, please refer to the OpenAPI specification or contact our support <NAME_EMAIL>.
