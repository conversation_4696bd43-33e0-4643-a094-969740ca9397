[{"success": true, "workflow_id": "workflow-019de50e-524e-4561-ab26-73c6282e3def", "results": {"create_data_subject_rights_requirement": {"success": true, "action_id": "create_data_subject_rights_requirement", "action_type": "create_requirement", "result": {"success": true, "requirement": {"id": "5109facb-1caa-447e-b356-e2756e9d2bc9", "name": "Data Subject Rights", "description": "Implement processes for handling data subject rights requests", "framework": "GDPR", "category": "privacy", "priority": "high", "status": "pending", "due_date": "2025-08-11T04:35:00.880985", "assigned_to": "privacy_officer", "tags": ["gdpr", "data_subject_rights", "privacy"], "created_at": "2025-07-12T04:35:01.208555", "updated_at": "2025-07-12T04:35:01.208555"}}}, "create_dpia_requirement": {"success": true, "action_id": "create_dpia_requirement", "action_type": "create_requirement", "result": {"success": true, "requirement": {"id": "fb4dc676-ba1b-43b2-add3-465d08aa160a", "name": "Data Protection Impact Assessment", "description": "Conduct data protection impact assessments for high-risk processing", "framework": "GDPR", "category": "risk_assessment", "priority": "medium", "status": "pending", "due_date": "2025-08-26T04:35:00.880985", "assigned_to": "privacy_officer", "tags": ["gdpr", "dpia", "risk_assessment"], "created_at": "2025-07-12T04:35:01.223523", "updated_at": "2025-07-12T04:35:01.223523"}}}, "create_breach_notification_requirement": {"success": true, "action_id": "create_breach_notification_requirement", "action_type": "create_requirement", "result": {"success": true, "requirement": {"id": "ac5c7363-54e6-433b-b3ac-2b7c2ce99021", "name": "Data Breach Notification", "description": "Implement processes for notifying authorities of data breaches", "framework": "GDPR", "category": "incident_response", "priority": "high", "status": "pending", "due_date": "2025-07-27T04:35:00.880985", "assigned_to": "security_officer", "tags": ["gdpr", "breach_notification", "incident_response"], "created_at": "2025-07-12T04:35:01.234282", "updated_at": "2025-07-12T04:35:01.234282"}}}, "collect_privacy_policy_evidence": {"success": true, "action_id": "collect_privacy_policy_evidence", "action_type": "collect_evidence", "result": {"success": true, "evidence": {"id": "6762e833-7467-4ab6-bd34-8e3603f4e26b", "name": "Privacy Policy", "description": "Organization's privacy policy document", "requirement_id": "${create_data_subject_rights_requirement.result.requirement.id}", "activity_id": null, "type": "document", "source": "website", "collection_method": "automated", "content": null, "url": "https://example.com/privacy-policy", "metadata": {"collected_at": "2025-07-12T04:35:01.247021", "collected_by": "automation"}, "created_at": "2025-07-12T04:35:01.247021", "updated_at": "2025-07-12T04:35:01.247021"}}}}}]