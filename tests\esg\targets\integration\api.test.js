const request = require('supertest');
const express = require('express');
const router = require('../../../../apis/esg/targets/routes');
const models = require('../../../../apis/esg/targets/models');

// Mock the models
jest.mock('../../../../apis/esg/targets/models', () => ({
  esgTargets: [
    {
      id: 'esg-t-12345678',
      name: 'Carbon Neutrality',
      description: 'Achieve carbon neutrality across all operations',
      category: 'environmental',
      type: 'reduction',
      metric: 'carbon-emissions',
      unit: 'metric-tons',
      baseline: {
        value: 10000,
        year: 2020
      },
      target: {
        value: 0,
        year: 2030
      },
      milestones: [
        {
          id: 'milestone-1',
          name: '50% Reduction',
          description: 'Achieve 50% reduction in carbon emissions',
          value: 5000,
          year: 2025,
          status: 'in-progress'
        }
      ],
      status: 'in-progress',
      progress: 0.2,
      owner: 'Sustainability Team',
      contributors: ['Operations', 'Facilities'],
      relatedFrameworks: ['GRI', 'SASB'],
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    },
    {
      id: 'esg-t-87654321',
      name: 'Diversity in Leadership',
      description: 'Increase diversity in leadership positions',
      category: 'social',
      type: 'improvement',
      metric: 'diversity-percentage',
      unit: 'percentage',
      baseline: {
        value: 25,
        year: 2022
      },
      target: {
        value: 50,
        year: 2025
      },
      milestones: [],
      status: 'not-started',
      progress: 0,
      owner: 'HR Team',
      contributors: ['Executive Leadership'],
      relatedFrameworks: ['GRI'],
      createdAt: '2023-02-01T00:00:00Z',
      updatedAt: '2023-02-01T00:00:00Z'
    }
  ],
  targetGroups: [
    {
      id: 'group-12345',
      name: 'Environmental Targets 2030',
      description: 'Group of environmental targets to be achieved by 2030',
      category: 'environmental',
      targets: ['esg-t-12345678'],
      owner: 'Sustainability Team',
      createdAt: '2023-01-15T00:00:00Z',
      updatedAt: '2023-01-15T00:00:00Z'
    }
  ]
}));

// Create a test Express app
const app = express();
app.use(express.json());
app.use('/governance/esg/targets', router);

describe('ESG Targets API Integration Tests', () => {
  describe('GET /governance/esg/targets', () => {
    it('should return all targets with default pagination', async () => {
      const response = await request(app).get('/governance/esg/targets');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination).toEqual({
        total: 2,
        page: 1,
        limit: 10,
        pages: 1
      });
    });

    it('should filter targets by category', async () => {
      const response = await request(app).get('/governance/esg/targets?category=environmental');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].category).toBe('environmental');
    });

    it('should filter targets by status', async () => {
      const response = await request(app).get('/governance/esg/targets?status=not-started');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].status).toBe('not-started');
    });
  });

  describe('GET /governance/esg/targets/:id', () => {
    it('should return a specific target by ID', async () => {
      const response = await request(app).get('/governance/esg/targets/esg-t-12345678');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('esg-t-12345678');
      expect(response.body.data.name).toBe('Carbon Neutrality');
    });

    it('should return 404 if target not found', async () => {
      const response = await request(app).get('/governance/esg/targets/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /governance/esg/targets', () => {
    it('should create a new target', async () => {
      const newTarget = {
        name: 'Water Conservation',
        description: 'Reduce water usage across all facilities',
        category: 'environmental',
        type: 'reduction',
        metric: 'water-usage',
        unit: 'gallons',
        baseline: {
          value: 1000000,
          year: 2022
        },
        target: {
          value: 750000,
          year: 2025
        },
        status: 'not-started',
        owner: 'Facilities Team'
      };

      const response = await request(app)
        .post('/governance/esg/targets')
        .send(newTarget);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'ESG target created successfully');
      expect(response.body.data.name).toBe('Water Conservation');
      expect(response.body.data.category).toBe('environmental');
    });

    it('should return 400 for invalid input', async () => {
      const invalidTarget = {
        // Missing required fields
        description: 'Invalid target'
      };

      const response = await request(app)
        .post('/governance/esg/targets')
        .send(invalidTarget);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /governance/esg/targets/:id', () => {
    it('should update an existing target', async () => {
      const updatedTarget = {
        name: 'Updated Target Name',
        progress: 0.3
      };

      const response = await request(app)
        .put('/governance/esg/targets/esg-t-12345678')
        .send(updatedTarget);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'ESG target updated successfully');
      expect(response.body.data.name).toBe('Updated Target Name');
      expect(response.body.data.progress).toBe(0.3);
    });

    it('should return 404 if target not found', async () => {
      const response = await request(app)
        .put('/governance/esg/targets/non-existent-id')
        .send({ name: 'Updated Name' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('DELETE /governance/esg/targets/:id', () => {
    it('should delete an existing target', async () => {
      const response = await request(app).delete('/governance/esg/targets/esg-t-12345678');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'ESG target deleted successfully');
    });

    it('should return 404 if target not found', async () => {
      const response = await request(app).delete('/governance/esg/targets/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /governance/esg/targets/:id/milestones', () => {
    it('should return milestones for a specific target', async () => {
      const response = await request(app).get('/governance/esg/targets/esg-t-12345678/milestones');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].id).toBe('milestone-1');
    });

    it('should return 404 if target not found', async () => {
      const response = await request(app).get('/governance/esg/targets/non-existent-id/milestones');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /governance/esg/targets/:id/milestones/:milestoneId', () => {
    it('should return a specific milestone by ID', async () => {
      const response = await request(app).get('/governance/esg/targets/esg-t-12345678/milestones/milestone-1');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('milestone-1');
      expect(response.body.data.name).toBe('50% Reduction');
    });

    it('should return 404 if milestone not found', async () => {
      const response = await request(app).get('/governance/esg/targets/esg-t-12345678/milestones/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /governance/esg/targets/:id/milestones', () => {
    it('should add a new milestone to a target', async () => {
      const newMilestone = {
        name: 'New Milestone',
        description: 'Description for new milestone',
        value: 2500,
        year: 2027,
        status: 'not-started'
      };

      const response = await request(app)
        .post('/governance/esg/targets/esg-t-12345678/milestones')
        .send(newMilestone);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Target milestone added successfully');
      expect(response.body.data.name).toBe('New Milestone');
      expect(response.body.data.value).toBe(2500);
    });

    it('should return 400 for invalid input', async () => {
      const invalidMilestone = {
        // Missing required fields
        description: 'Invalid milestone'
      };

      const response = await request(app)
        .post('/governance/esg/targets/esg-t-12345678/milestones')
        .send(invalidMilestone);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /governance/esg/targets/:id/milestones/:milestoneId', () => {
    it('should update an existing milestone', async () => {
      const updatedMilestone = {
        name: 'Updated Milestone Name',
        status: 'completed'
      };

      const response = await request(app)
        .put('/governance/esg/targets/esg-t-12345678/milestones/milestone-1')
        .send(updatedMilestone);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Target milestone updated successfully');
      expect(response.body.data.name).toBe('Updated Milestone Name');
      expect(response.body.data.status).toBe('completed');
    });

    it('should return 404 if milestone not found', async () => {
      const response = await request(app)
        .put('/governance/esg/targets/esg-t-12345678/milestones/non-existent-id')
        .send({ name: 'Updated Name' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('DELETE /governance/esg/targets/:id/milestones/:milestoneId', () => {
    it('should remove a milestone from a target', async () => {
      const response = await request(app).delete('/governance/esg/targets/esg-t-12345678/milestones/milestone-1');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Target milestone removed successfully');
    });

    it('should return 404 if milestone not found', async () => {
      const response = await request(app).delete('/governance/esg/targets/esg-t-12345678/milestones/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /governance/esg/targets/groups', () => {
    it('should return all target groups', async () => {
      const response = await request(app).get('/governance/esg/targets/groups');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].name).toBe('Environmental Targets 2030');
    });
  });

  describe('GET /governance/esg/targets/groups/:id', () => {
    it('should return a specific target group by ID', async () => {
      const response = await request(app).get('/governance/esg/targets/groups/group-12345');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('group-12345');
      expect(response.body.data.name).toBe('Environmental Targets 2030');
    });

    it('should return 404 if target group not found', async () => {
      const response = await request(app).get('/governance/esg/targets/groups/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /governance/esg/targets/groups', () => {
    it('should create a new target group', async () => {
      const newGroup = {
        name: 'Social Targets 2025',
        description: 'Group of social targets to be achieved by 2025',
        category: 'social',
        targets: ['esg-t-87654321'],
        owner: 'HR Team'
      };

      const response = await request(app)
        .post('/governance/esg/targets/groups')
        .send(newGroup);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Target group created successfully');
      expect(response.body.data.name).toBe('Social Targets 2025');
      expect(response.body.data.category).toBe('social');
    });

    it('should return 400 for invalid input', async () => {
      const invalidGroup = {
        // Missing required fields
        description: 'Invalid group'
      };

      const response = await request(app)
        .post('/governance/esg/targets/groups')
        .send(invalidGroup);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /governance/esg/targets/groups/:id', () => {
    it('should update an existing target group', async () => {
      const updatedGroup = {
        name: 'Updated Group Name',
        targets: ['esg-t-12345678', 'esg-t-87654321']
      };

      const response = await request(app)
        .put('/governance/esg/targets/groups/group-12345')
        .send(updatedGroup);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Target group updated successfully');
      expect(response.body.data.name).toBe('Updated Group Name');
      expect(response.body.data.targets).toEqual(['esg-t-12345678', 'esg-t-87654321']);
    });

    it('should return 404 if target group not found', async () => {
      const response = await request(app)
        .put('/governance/esg/targets/groups/non-existent-id')
        .send({ name: 'Updated Name' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('DELETE /governance/esg/targets/groups/:id', () => {
    it('should delete an existing target group', async () => {
      const response = await request(app).delete('/governance/esg/targets/groups/group-12345');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Target group deleted successfully');
    });

    it('should return 404 if target group not found', async () => {
      const response = await request(app).delete('/governance/esg/targets/groups/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });
});
