{"name": "privacy-management-api", "version": "1.0.0", "description": "NovaFuse Privacy Management API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint .", "seed": "node scripts/seed-db.js", "swagger:combine": "node scripts/combine-swagger.js"}, "keywords": ["privacy", "gdpr", "ccpa", "compliance", "api"], "author": "NovaFuse", "license": "MIT", "dependencies": {"axios": "^1.4.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "joi": "^17.9.2", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.3.1", "morgan": "^1.10.0", "node-cache": "^5.1.2", "pg": "^8.14.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.3", "winston": "^3.9.0"}, "devDependencies": {"eslint": "^8.43.0", "jest": "^29.5.0", "mongodb-memory-server": "^8.13.0", "nodemon": "^2.0.22", "rewire": "^7.0.0", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0"}, "private": true}