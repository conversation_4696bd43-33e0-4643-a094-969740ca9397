/**
 * NovaVision Marketplace Dashboard
 * 
 * This component displays a dashboard for the NovaStore marketplace with
 * Trinity CSDE verification metrics, adaptive ratios, and component listings.
 */

const React = require('react');
const PropTypes = require('prop-types');

// Import NovaVision core components
const { 
  Dashboard, 
  Card, 
  Chart, 
  Tabs, 
  Tab, 
  Icon, 
  TrinitySymbol 
} = require('../core');

// Import NovaVision NovaStore components
const ComponentCard = require('./component_card');

/**
 * Marketplace Dashboard
 * @param {Object} props - Component props
 * @returns {JSX.Element} - Marketplace dashboard
 */
function MarketplaceDashboard({ 
  components = [], 
  verificationMetrics = {}, 
  adaptiveRatios = {},
  marketplaceMetrics = {} 
}) {
  // Format percentage
  const formatPercent = (value) => `${(value * 100).toFixed(2)}%`;
  
  // Format number with commas
  const formatNumber = (value) => value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  
  // Get verification metrics
  const {
    totalComponents = 0,
    verifiedComponents = 0,
    rejectedComponents = 0,
    averageQualityScore = 0
  } = verificationMetrics;
  
  // Get adaptive ratios
  const {
    father = { alpha: 0.18 },
    son = { beta: 0.18 },
    spirit = { gamma: 0.18 }
  } = adaptiveRatios;
  
  // Get marketplace metrics
  const {
    totalDownloads = 0,
    averageRating = 0,
    verificationRate = 0,
    averageVerificationScore = 0,
    badgeDistribution = {
      gold: 0,
      silver: 0,
      bronze: 0,
      none: 0
    },
    categoryDistribution = {}
  } = marketplaceMetrics;
  
  // Calculate verification rate
  const calculatedVerificationRate = totalComponents > 0 
    ? verifiedComponents / totalComponents 
    : 0;
  
  // Prepare data for charts
  const verificationData = {
    labels: ['Verified', 'Rejected', 'Unverified'],
    datasets: [{
      data: [
        verifiedComponents, 
        rejectedComponents, 
        totalComponents - verifiedComponents - rejectedComponents
      ],
      backgroundColor: ['#4CAF50', '#F44336', '#FF9800']
    }]
  };
  
  const badgeData = {
    labels: ['Gold', 'Silver', 'Bronze', 'None'],
    datasets: [{
      data: [
        badgeDistribution.gold,
        badgeDistribution.silver,
        badgeDistribution.bronze,
        badgeDistribution.none
      ],
      backgroundColor: ['#FFD700', '#C0C0C0', '#CD7F32', '#999999']
    }]
  };
  
  const adaptiveRatiosData = {
    labels: ['Father (α)', 'Son (β)', 'Spirit (γ)'],
    datasets: [{
      label: 'Initial',
      data: [0.18, 0.18, 0.18],
      backgroundColor: 'rgba(54, 162, 235, 0.5)',
      borderColor: 'rgba(54, 162, 235, 1)',
      borderWidth: 1
    }, {
      label: 'Optimized',
      data: [father.alpha, son.beta, spirit.gamma],
      backgroundColor: 'rgba(255, 99, 132, 0.5)',
      borderColor: 'rgba(255, 99, 132, 1)',
      borderWidth: 1
    }]
  };
  
  const categoryData = {
    labels: Object.keys(categoryDistribution),
    datasets: [{
      data: Object.values(categoryDistribution),
      backgroundColor: [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
        '#9966FF', '#FF9F40', '#8AC249', '#EA80FC'
      ]
    }]
  };
  
  return (
    <Dashboard className="nova-marketplace-dashboard">
      {/* Dashboard Header */}
      <div className="nova-dashboard-header">
        <h1 className="nova-dashboard-title">
          <TrinitySymbol size={24} />
          <span>NovaStore Marketplace</span>
        </h1>
        
        <div className="nova-dashboard-actions">
          <button className="nova-button nova-button-primary">
            <Icon name="refresh" size={16} />
            <span>Refresh</span>
          </button>
          
          <button className="nova-button nova-button-secondary">
            <Icon name="settings" size={16} />
            <span>Settings</span>
          </button>
        </div>
      </div>
      
      {/* Dashboard Metrics */}
      <div className="nova-dashboard-metrics">
        <Card className="nova-metric-card">
          <div className="nova-metric-icon">
            <Icon name="package" size={24} />
          </div>
          <div className="nova-metric-content">
            <div className="nova-metric-value">{formatNumber(totalComponents)}</div>
            <div className="nova-metric-label">Total Components</div>
          </div>
        </Card>
        
        <Card className="nova-metric-card">
          <div className="nova-metric-icon">
            <Icon name="check-circle" size={24} />
          </div>
          <div className="nova-metric-content">
            <div className="nova-metric-value">{formatPercent(calculatedVerificationRate)}</div>
            <div className="nova-metric-label">Verification Rate</div>
          </div>
        </Card>
        
        <Card className="nova-metric-card">
          <div className="nova-metric-icon">
            <Icon name="download" size={24} />
          </div>
          <div className="nova-metric-content">
            <div className="nova-metric-value">{formatNumber(totalDownloads)}</div>
            <div className="nova-metric-label">Total Downloads</div>
          </div>
        </Card>
        
        <Card className="nova-metric-card">
          <div className="nova-metric-icon">
            <Icon name="star" size={24} />
          </div>
          <div className="nova-metric-content">
            <div className="nova-metric-value">{averageRating.toFixed(1)}</div>
            <div className="nova-metric-label">Average Rating</div>
          </div>
        </Card>
      </div>
      
      {/* Dashboard Charts */}
      <div className="nova-dashboard-charts">
        <Card className="nova-chart-card">
          <h3 className="nova-chart-title">Verification Status</h3>
          <Chart 
            type="pie"
            data={verificationData}
            options={{
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'bottom'
                }
              }
            }}
          />
        </Card>
        
        <Card className="nova-chart-card">
          <h3 className="nova-chart-title">Badge Distribution</h3>
          <Chart 
            type="pie"
            data={badgeData}
            options={{
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'bottom'
                }
              }
            }}
          />
        </Card>
        
        <Card className="nova-chart-card">
          <h3 className="nova-chart-title">Adaptive Ratios</h3>
          <Chart 
            type="bar"
            data={adaptiveRatiosData}
            options={{
              responsive: true,
              maintainAspectRatio: false,
              scales: {
                y: {
                  beginAtZero: true,
                  max: 1
                }
              },
              plugins: {
                legend: {
                  position: 'bottom'
                }
              }
            }}
          />
        </Card>
        
        <Card className="nova-chart-card">
          <h3 className="nova-chart-title">Category Distribution</h3>
          <Chart 
            type="doughnut"
            data={categoryData}
            options={{
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'bottom'
                }
              }
            }}
          />
        </Card>
      </div>
      
      {/* Component Listings */}
      <div className="nova-component-listings">
        <Card className="nova-listings-card">
          <h3 className="nova-listings-title">Components</h3>
          
          <Tabs>
            <Tab label="All Components">
              <div className="nova-component-grid">
                {components.map(component => (
                  <ComponentCard 
                    key={component.id}
                    component={component}
                  />
                ))}
              </div>
            </Tab>
            
            <Tab label="Verified Components">
              <div className="nova-component-grid">
                {components
                  .filter(component => component.verification?.status === 'verified')
                  .map(component => (
                    <ComponentCard 
                      key={component.id}
                      component={component}
                    />
                  ))
                }
              </div>
            </Tab>
            
            <Tab label="Featured Components">
              <div className="nova-component-grid">
                {components
                  .filter(component => component.getVerificationBadge?.() === 'gold')
                  .map(component => (
                    <ComponentCard 
                      key={component.id}
                      component={component}
                      detailed
                    />
                  ))
                }
              </div>
            </Tab>
          </Tabs>
        </Card>
      </div>
    </Dashboard>
  );
}

MarketplaceDashboard.propTypes = {
  components: PropTypes.array,
  verificationMetrics: PropTypes.object,
  adaptiveRatios: PropTypes.object,
  marketplaceMetrics: PropTypes.object
};

module.exports = MarketplaceDashboard;
