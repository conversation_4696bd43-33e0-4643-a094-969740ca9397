/**
 * Visualization Analytics Service
 * 
 * This service tracks user interactions with the Cyber-Safety visualizations.
 */

import axios from 'axios';

// API base URL
const API_BASE_URL = '/api';

// Event types
export const EVENT_TYPES = {
  VIEW: 'view',
  INTERACT: 'interact',
  FILTER: 'filter',
  EXPORT: 'export',
  FULLSCREEN: 'fullscreen',
  ERROR: 'error',
  FEEDBACK: 'feedback'
};

// Interaction types
export const INTERACTION_TYPES = {
  CLICK: 'click',
  HOVER: 'hover',
  ZOOM: 'zoom',
  PAN: 'pan',
  ROTATE: 'rotate',
  DRAG: 'drag',
  SELECT: 'select'
};

// Visualization types
export const VISUALIZATION_TYPES = {
  TRI_DOMAIN_TENSOR: 'triDomainTensor',
  HARMONY_INDEX: 'harmonyIndex',
  RISK_CONTROL_FUSION: 'riskControlFusion',
  RESONANCE_SPECTROGRAM: 'resonanceSpectrogram',
  UNIFIED_COMPLIANCE_SECURITY: 'unifiedComplianceSecurity'
};

// Queue for batching events
let eventQueue = [];

// Flag to track if a flush is scheduled
let flushScheduled = false;

// Flush interval in milliseconds
const FLUSH_INTERVAL = 10000; // 10 seconds

// Maximum queue size before forcing a flush
const MAX_QUEUE_SIZE = 50;

/**
 * Track a visualization event
 * @param {string} eventType - Type of event
 * @param {string} visualizationType - Type of visualization
 * @param {Object} details - Additional details about the event
 */
export const trackEvent = (eventType, visualizationType, details = {}) => {
  // Create event object
  const event = {
    eventType,
    visualizationType,
    timestamp: new Date().toISOString(),
    details,
    sessionId: getSessionId(),
    userId: getUserId(),
    deviceInfo: getDeviceInfo()
  };
  
  // Add event to queue
  eventQueue.push(event);
  
  // Schedule flush if not already scheduled
  if (!flushScheduled && eventQueue.length < MAX_QUEUE_SIZE) {
    flushScheduled = true;
    setTimeout(flushEvents, FLUSH_INTERVAL);
  } else if (eventQueue.length >= MAX_QUEUE_SIZE) {
    // Force flush if queue is full
    flushEvents();
  }
  
  // Log event in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log('Visualization Analytics Event:', event);
  }
};

/**
 * Track a view event
 * @param {string} visualizationType - Type of visualization
 * @param {Object} details - Additional details about the view
 */
export const trackView = (visualizationType, details = {}) => {
  trackEvent(EVENT_TYPES.VIEW, visualizationType, {
    ...details,
    viewDuration: 0, // Will be updated on unmount
    viewStartTime: Date.now()
  });
};

/**
 * Update view duration for a visualization
 * @param {string} visualizationType - Type of visualization
 */
export const updateViewDuration = (visualizationType) => {
  // Find the most recent view event for this visualization
  const viewEvent = [...eventQueue].reverse().find(
    event => event.eventType === EVENT_TYPES.VIEW && event.visualizationType === visualizationType
  );
  
  if (viewEvent && viewEvent.details.viewStartTime) {
    // Calculate duration in seconds
    const duration = Math.round((Date.now() - viewEvent.details.viewStartTime) / 1000);
    
    // Update event
    viewEvent.details.viewDuration = duration;
    
    // Force flush if duration is significant
    if (duration > 30) {
      flushEvents();
    }
  }
};

/**
 * Track an interaction event
 * @param {string} interactionType - Type of interaction
 * @param {string} visualizationType - Type of visualization
 * @param {Object} details - Additional details about the interaction
 */
export const trackInteraction = (interactionType, visualizationType, details = {}) => {
  trackEvent(EVENT_TYPES.INTERACT, visualizationType, {
    interactionType,
    ...details
  });
};

/**
 * Track a filter event
 * @param {string} visualizationType - Type of visualization
 * @param {Object} filters - Applied filters
 */
export const trackFilter = (visualizationType, filters = {}) => {
  trackEvent(EVENT_TYPES.FILTER, visualizationType, {
    filters
  });
};

/**
 * Track an export event
 * @param {string} visualizationType - Type of visualization
 * @param {string} format - Export format
 * @param {Object} details - Additional details about the export
 */
export const trackExport = (visualizationType, format, details = {}) => {
  trackEvent(EVENT_TYPES.EXPORT, visualizationType, {
    format,
    ...details
  });
};

/**
 * Track a fullscreen event
 * @param {string} visualizationType - Type of visualization
 * @param {boolean} entered - Whether fullscreen was entered or exited
 */
export const trackFullscreen = (visualizationType, entered) => {
  trackEvent(EVENT_TYPES.FULLSCREEN, visualizationType, {
    entered
  });
};

/**
 * Track an error event
 * @param {string} visualizationType - Type of visualization
 * @param {string} errorMessage - Error message
 * @param {Object} details - Additional details about the error
 */
export const trackError = (visualizationType, errorMessage, details = {}) => {
  trackEvent(EVENT_TYPES.ERROR, visualizationType, {
    errorMessage,
    ...details
  });
};

/**
 * Track a feedback event
 * @param {string} visualizationType - Type of visualization
 * @param {Object} feedback - Feedback data
 */
export const trackFeedback = (visualizationType, feedback) => {
  trackEvent(EVENT_TYPES.FEEDBACK, visualizationType, {
    feedback
  });
};

/**
 * Flush events to the server
 */
const flushEvents = async () => {
  // Reset scheduled flag
  flushScheduled = false;
  
  // Skip if queue is empty
  if (eventQueue.length === 0) {
    return;
  }
  
  // Copy queue and clear it
  const events = [...eventQueue];
  eventQueue = [];
  
  try {
    // Send events to server
    await axios.post(`${API_BASE_URL}/analytics/visualization-events`, {
      events
    });
    
    // Log success in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log(`Flushed ${events.length} visualization analytics events`);
    }
  } catch (error) {
    // Log error
    console.error('Error flushing visualization analytics events:', error);
    
    // Put events back in queue
    eventQueue = [...events, ...eventQueue];
    
    // Schedule another flush
    if (!flushScheduled) {
      flushScheduled = true;
      setTimeout(flushEvents, FLUSH_INTERVAL);
    }
  }
};

/**
 * Get session ID
 * @returns {string} - Session ID
 */
const getSessionId = () => {
  // Check if session ID exists in session storage
  let sessionId = sessionStorage.getItem('visualization_session_id');
  
  // Create new session ID if not found
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    sessionStorage.setItem('visualization_session_id', sessionId);
  }
  
  return sessionId;
};

/**
 * Get user ID
 * @returns {string|null} - User ID or null if not logged in
 */
const getUserId = () => {
  // Get user ID from local storage or session
  const user = JSON.parse(localStorage.getItem('user') || sessionStorage.getItem('user') || '{}');
  return user.id || null;
};

/**
 * Get device information
 * @returns {Object} - Device information
 */
const getDeviceInfo = () => {
  return {
    userAgent: navigator.userAgent,
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    viewportWidth: window.innerWidth,
    viewportHeight: window.innerHeight,
    devicePixelRatio: window.devicePixelRatio,
    language: navigator.language,
    platform: navigator.platform
  };
};

// Flush events before page unload
window.addEventListener('beforeunload', () => {
  // Synchronous flush for beforeunload
  if (eventQueue.length > 0) {
    try {
      navigator.sendBeacon(
        `${API_BASE_URL}/analytics/visualization-events`,
        JSON.stringify({ events: eventQueue })
      );
    } catch (error) {
      console.error('Error sending beacon:', error);
    }
  }
});

// Export service
export default {
  trackEvent,
  trackView,
  updateViewDuration,
  trackInteraction,
  trackFilter,
  trackExport,
  trackFullscreen,
  trackError,
  trackFeedback,
  EVENT_TYPES,
  INTERACTION_TYPES,
  VISUALIZATION_TYPES
};
