{"version": 3, "names": ["axios", "require", "GoogleAuth", "TransformationEngine", "ChronicleConnector", "constructor", "options", "enableMetrics", "maxConcurrentRequests", "pageSize", "baseUrl", "transformationEngine", "enableCaching", "registerTransformer", "_mapAttackToNist", "bind", "_extractIoc", "_normalizeChronicleTime", "_initializeAttackToNistMapping", "metrics", "eventsRetrieved", "eventsNormalized", "apiCalls", "totalApiLatency", "averageApiLatency", "totalNormalizationTime", "averageNormalizationTime", "initialize", "credentials", "auth", "scopes", "client", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "getAccessToken", "Authorization", "success", "error", "console", "message", "get<PERSON><PERSON><PERSON>", "params", "Error", "startTime", "Date", "now", "alertStartTime", "endTime", "alertEndTime", "pageToken", "response", "get", "alerts", "data", "nextPageToken", "latency", "length", "getIocs", "iocType", "value", "iocs", "searchEvents", "query", "eventStartTime", "eventEndTime", "post", "events", "normalizeAlerts", "rules", "source", "target", "transform", "normalizedAlerts", "map", "alert", "duration", "getAlertsNormalized", "getAllAlerts", "allAlerts", "concat", "getAllAlertsNormalized", "getMetrics", "transformationMetrics", "attackToNistMapping", "techniques", "Array", "isArray", "nistControls", "Set", "technique", "techniqueId", "split", "controls", "control", "add", "from", "indicators", "indicator", "type", "confidence", "timestamp", "getTime", "module", "exports"], "sources": ["chronicle-connector.js"], "sourcesContent": ["/**\n * Google Chronicle Connector\n * \n * Connects to Google Chronicle API and normalizes security events\n * for use with NovaConnect's remediation engine.\n * \n * This connector enables mapping between MITRE ATT&CK and NIST frameworks\n * for comprehensive threat-to-compliance correlation.\n */\n\nconst axios = require('axios');\nconst { GoogleAuth } = require('google-auth-library');\nconst TransformationEngine = require('../../engines/transformation-engine');\n\nclass ChronicleConnector {\n  constructor(options = {}) {\n    this.options = {\n      enableMetrics: true,\n      maxConcurrentRequests: 10,\n      pageSize: 1000,\n      baseUrl: 'https://backstory.googleapis.com/v1',\n      ...options\n    };\n    \n    this.transformationEngine = new TransformationEngine({\n      enableMetrics: this.options.enableMetrics,\n      enableCaching: true\n    });\n    \n    // Register Chronicle-specific transformers\n    this.transformationEngine.registerTransformer('mapAttackToNist', this._mapAttackToNist.bind(this));\n    this.transformationEngine.registerTransformer('extractIoc', this._extractIoc);\n    this.transformationEngine.registerTransformer('normalizeChronicleTime', this._normalizeChronicleTime);\n    \n    // Initialize MITRE ATT&CK to NIST mapping\n    this._initializeAttackToNistMapping();\n    \n    // Initialize metrics\n    this.metrics = {\n      eventsRetrieved: 0,\n      eventsNormalized: 0,\n      apiCalls: 0,\n      totalApiLatency: 0,\n      averageApiLatency: 0,\n      totalNormalizationTime: 0,\n      averageNormalizationTime: 0\n    };\n  }\n  \n  /**\n   * Initialize the Chronicle client with credentials\n   * @param {Object} credentials - GCP credentials\n   */\n  async initialize(credentials) {\n    try {\n      this.auth = new GoogleAuth({\n        credentials: credentials,\n        scopes: ['https://www.googleapis.com/auth/chronicle-backstory']\n      });\n      \n      this.client = axios.create({\n        baseURL: this.options.baseUrl,\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      \n      // Set up request interceptor to add authentication\n      this.client.interceptors.request.use(async (config) => {\n        const token = await this.auth.getAccessToken();\n        config.headers.Authorization = `Bearer ${token}`;\n        return config;\n      });\n      \n      return { success: true };\n    } catch (error) {\n      console.error('Error initializing Chronicle client:', error);\n      return { \n        success: false, \n        error: error.message \n      };\n    }\n  }\n  \n  /**\n   * Get alerts from Chronicle\n   * @param {Object} params - Query parameters\n   * @returns {Object} - Alerts and metadata\n   */\n  async getAlerts(params = {}) {\n    if (!this.client) {\n      throw new Error('Chronicle client not initialized. Call initialize() first.');\n    }\n    \n    const startTime = this.options.enableMetrics ? Date.now() : 0;\n    \n    try {\n      const {\n        startTime: alertStartTime,\n        endTime: alertEndTime,\n        pageSize = this.options.pageSize,\n        pageToken\n      } = params;\n      \n      // Build the request\n      const request = {\n        pageSize,\n        pageToken\n      };\n      \n      if (alertStartTime) {\n        request.startTime = alertStartTime;\n      }\n      \n      if (alertEndTime) {\n        request.endTime = alertEndTime;\n      }\n      \n      // Make the API call\n      const response = await this.client.get('/alerts', { params: request });\n      \n      const alerts = response.data.alerts || [];\n      const nextPageToken = response.data.nextPageToken;\n      \n      // Update metrics\n      if (this.options.enableMetrics) {\n        const endTime = Date.now();\n        const latency = endTime - startTime;\n        \n        this.metrics.apiCalls++;\n        this.metrics.eventsRetrieved += alerts.length;\n        this.metrics.totalApiLatency += latency;\n        this.metrics.averageApiLatency = this.metrics.totalApiLatency / this.metrics.apiCalls;\n      }\n      \n      return {\n        alerts,\n        nextPageToken\n      };\n    } catch (error) {\n      console.error('Error retrieving alerts from Chronicle:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Get IOCs from Chronicle\n   * @param {Object} params - Query parameters\n   * @returns {Object} - IOCs and metadata\n   */\n  async getIocs(params = {}) {\n    if (!this.client) {\n      throw new Error('Chronicle client not initialized. Call initialize() first.');\n    }\n    \n    const startTime = this.options.enableMetrics ? Date.now() : 0;\n    \n    try {\n      const {\n        iocType,\n        value,\n        pageSize = this.options.pageSize,\n        pageToken\n      } = params;\n      \n      // Build the request\n      const request = {\n        pageSize,\n        pageToken\n      };\n      \n      if (iocType) {\n        request.iocType = iocType;\n      }\n      \n      if (value) {\n        request.value = value;\n      }\n      \n      // Make the API call\n      const response = await this.client.get('/iocs', { params: request });\n      \n      const iocs = response.data.iocs || [];\n      const nextPageToken = response.data.nextPageToken;\n      \n      // Update metrics\n      if (this.options.enableMetrics) {\n        const endTime = Date.now();\n        const latency = endTime - startTime;\n        \n        this.metrics.apiCalls++;\n        this.metrics.eventsRetrieved += iocs.length;\n        this.metrics.totalApiLatency += latency;\n        this.metrics.averageApiLatency = this.metrics.totalApiLatency / this.metrics.apiCalls;\n      }\n      \n      return {\n        iocs,\n        nextPageToken\n      };\n    } catch (error) {\n      console.error('Error retrieving IOCs from Chronicle:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Search for events in Chronicle\n   * @param {Object} params - Query parameters\n   * @returns {Object} - Events and metadata\n   */\n  async searchEvents(params = {}) {\n    if (!this.client) {\n      throw new Error('Chronicle client not initialized. Call initialize() first.');\n    }\n    \n    const startTime = this.options.enableMetrics ? Date.now() : 0;\n    \n    try {\n      const {\n        query,\n        startTime: eventStartTime,\n        endTime: eventEndTime,\n        pageSize = this.options.pageSize,\n        pageToken\n      } = params;\n      \n      if (!query) {\n        throw new Error('Query parameter is required');\n      }\n      \n      // Build the request\n      const request = {\n        query,\n        pageSize,\n        pageToken\n      };\n      \n      if (eventStartTime) {\n        request.startTime = eventStartTime;\n      }\n      \n      if (eventEndTime) {\n        request.endTime = eventEndTime;\n      }\n      \n      // Make the API call\n      const response = await this.client.post('/events:search', request);\n      \n      const events = response.data.events || [];\n      const nextPageToken = response.data.nextPageToken;\n      \n      // Update metrics\n      if (this.options.enableMetrics) {\n        const endTime = Date.now();\n        const latency = endTime - startTime;\n        \n        this.metrics.apiCalls++;\n        this.metrics.eventsRetrieved += events.length;\n        this.metrics.totalApiLatency += latency;\n        this.metrics.averageApiLatency = this.metrics.totalApiLatency / this.metrics.apiCalls;\n      }\n      \n      return {\n        events,\n        nextPageToken\n      };\n    } catch (error) {\n      console.error('Error searching events in Chronicle:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Normalize Chronicle alerts to NovaConnect format\n   * @param {Array} alerts - Chronicle alerts\n   * @returns {Array} - Normalized alerts\n   */\n  normalizeAlerts(alerts) {\n    const startTime = this.options.enableMetrics ? Date.now() : 0;\n    \n    try {\n      // Define transformation rules for Chronicle alerts\n      const rules = [\n        { source: 'id', target: 'id' },\n        { source: 'type', target: 'type' },\n        { source: 'createdTime', target: 'createdAt', transform: 'normalizeChronicleTime' },\n        { source: 'severity', target: 'severity', transform: 'lowercase' },\n        { source: 'name', target: 'title' },\n        { source: 'description', target: 'description' },\n        { source: 'asset.hostname', target: 'resourceName' },\n        { source: 'asset.assetType', target: 'resourceType' },\n        { source: 'attackTechniques', target: 'attackTechniques' },\n        { source: 'attackTechniques', target: 'nistControls', transform: 'mapAttackToNist' },\n        { source: 'indicators', target: 'indicators', transform: 'extractIoc' }\n      ];\n      \n      // Transform each alert\n      const normalizedAlerts = alerts.map(alert => \n        this.transformationEngine.transform(alert, rules)\n      );\n      \n      // Update metrics\n      if (this.options.enableMetrics) {\n        const endTime = Date.now();\n        const duration = endTime - startTime;\n        \n        this.metrics.eventsNormalized += alerts.length;\n        this.metrics.totalNormalizationTime += duration;\n        this.metrics.averageNormalizationTime = \n          this.metrics.totalNormalizationTime / this.metrics.eventsNormalized;\n      }\n      \n      return normalizedAlerts;\n    } catch (error) {\n      console.error('Error normalizing Chronicle alerts:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Get and normalize alerts in a single call\n   * @param {Object} params - Query parameters\n   * @returns {Object} - Normalized alerts and metadata\n   */\n  async getAlertsNormalized(params = {}) {\n    const { alerts, nextPageToken } = await this.getAlerts(params);\n    const normalizedAlerts = this.normalizeAlerts(alerts);\n    \n    return {\n      alerts: normalizedAlerts,\n      nextPageToken\n    };\n  }\n  \n  /**\n   * Get all alerts with pagination handling\n   * @param {Object} params - Query parameters\n   * @returns {Array} - All alerts\n   */\n  async getAllAlerts(params = {}) {\n    let allAlerts = [];\n    let nextPageToken = null;\n    \n    do {\n      const { alerts, nextPageToken: token } = await this.getAlerts({\n        ...params,\n        pageToken: nextPageToken\n      });\n      \n      allAlerts = allAlerts.concat(alerts);\n      nextPageToken = token;\n    } while (nextPageToken);\n    \n    return allAlerts;\n  }\n  \n  /**\n   * Get all normalized alerts with pagination handling\n   * @param {Object} params - Query parameters\n   * @returns {Array} - All normalized alerts\n   */\n  async getAllAlertsNormalized(params = {}) {\n    const alerts = await this.getAllAlerts(params);\n    return this.normalizeAlerts(alerts);\n  }\n  \n  /**\n   * Get metrics for the connector\n   * @returns {Object} - Metrics\n   */\n  getMetrics() {\n    return {\n      ...this.metrics,\n      transformationMetrics: this.transformationEngine.getMetrics()\n    };\n  }\n  \n  /**\n   * Initialize MITRE ATT&CK to NIST mapping\n   * @private\n   */\n  _initializeAttackToNistMapping() {\n    // This is a simplified mapping - in a real implementation, this would be more comprehensive\n    this.attackToNistMapping = {\n      // Initial Access\n      'T1189': ['AC-4', 'SI-3'], // Drive-by Compromise\n      'T1190': ['AC-4', 'SI-3', 'SI-10'], // Exploit Public-Facing Application\n      'T1133': ['AC-2', 'AC-3', 'AC-17'], // External Remote Services\n      \n      // Execution\n      'T1059': ['CM-7', 'SI-3'], // Command and Scripting Interpreter\n      'T1203': ['SI-3', 'SI-4'], // Exploitation for Client Execution\n      \n      // Persistence\n      'T1136': ['AC-2'], // Create Account\n      'T1098': ['AC-2', 'AC-3'], // Account Manipulation\n      \n      // Privilege Escalation\n      'T1068': ['SI-2', 'RA-5'], // Exploitation for Privilege Escalation\n      'T1078': ['AC-2', 'AC-3', 'AC-6'], // Valid Accounts\n      \n      // Defense Evasion\n      'T1070': ['AU-9', 'SI-4'], // Indicator Removal on Host\n      'T1027': ['SI-3', 'SI-4'], // Obfuscated Files or Information\n      \n      // Credential Access\n      'T1110': ['AC-7', 'IA-5'], // Brute Force\n      'T1003': ['IA-5', 'SI-4'], // OS Credential Dumping\n      \n      // Discovery\n      'T1087': ['AC-2', 'SI-4'], // Account Discovery\n      'T1082': ['CM-8', 'SI-4'], // System Information Discovery\n      \n      // Lateral Movement\n      'T1021': ['AC-17', 'SI-4'], // Remote Services\n      'T1091': ['AC-4', 'SI-3'], // Replication Through Removable Media\n      \n      // Collection\n      'T1005': ['AC-3', 'SI-4'], // Data from Local System\n      'T1039': ['AC-3', 'SI-4'], // Data from Network Shared Drive\n      \n      // Command and Control\n      'T1071': ['SC-7', 'SI-4'], // Application Layer Protocol\n      'T1105': ['SI-3', 'SI-4'], // Ingress Tool Transfer\n      \n      // Exfiltration\n      'T1048': ['AC-4', 'SI-4'], // Exfiltration Over Alternative Protocol\n      'T1041': ['AC-4', 'SI-4'], // Exfiltration Over C2 Channel\n      \n      // Impact\n      'T1485': ['CP-9', 'CP-10'], // Data Destruction\n      'T1486': ['CP-9', 'CP-10', 'SI-4'] // Data Encrypted for Impact\n    };\n  }\n  \n  /**\n   * Map MITRE ATT&CK techniques to NIST controls\n   * @param {Array} techniques - ATT&CK techniques\n   * @returns {Array} - NIST controls\n   * @private\n   */\n  _mapAttackToNist(techniques) {\n    if (!techniques || !Array.isArray(techniques)) {\n      return [];\n    }\n    \n    const nistControls = new Set();\n    \n    for (const technique of techniques) {\n      const techniqueId = technique.split('.')[0]; // Handle sub-techniques\n      const controls = this.attackToNistMapping[techniqueId] || [];\n      \n      for (const control of controls) {\n        nistControls.add(control);\n      }\n    }\n    \n    return Array.from(nistControls);\n  }\n  \n  /**\n   * Extract IOCs from indicators\n   * @param {Array} indicators - Chronicle indicators\n   * @returns {Array} - Extracted IOCs\n   * @private\n   */\n  _extractIoc(indicators) {\n    if (!indicators || !Array.isArray(indicators)) {\n      return [];\n    }\n    \n    return indicators.map(indicator => ({\n      type: indicator.type,\n      value: indicator.value,\n      confidence: indicator.confidence\n    }));\n  }\n  \n  /**\n   * Normalize Chronicle timestamp to Unix timestamp\n   * @param {string} timestamp - Chronicle timestamp\n   * @returns {number} - Unix timestamp\n   * @private\n   */\n  _normalizeChronicleTime(timestamp) {\n    if (!timestamp) return null;\n    \n    // Chronicle uses RFC 3339 format\n    return new Date(timestamp).getTime();\n  }\n}\n\nmodule.exports = ChronicleConnector;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC9B,MAAM;EAAEC;AAAW,CAAC,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AACrD,MAAME,oBAAoB,GAAGF,OAAO,CAAC,qCAAqC,CAAC;AAE3E,MAAMG,kBAAkB,CAAC;EACvBC,WAAWA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,IAAI,CAACA,OAAO,GAAG;MACbC,aAAa,EAAE,IAAI;MACnBC,qBAAqB,EAAE,EAAE;MACzBC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,qCAAqC;MAC9C,GAAGJ;IACL,CAAC;IAED,IAAI,CAACK,oBAAoB,GAAG,IAAIR,oBAAoB,CAAC;MACnDI,aAAa,EAAE,IAAI,CAACD,OAAO,CAACC,aAAa;MACzCK,aAAa,EAAE;IACjB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACD,oBAAoB,CAACE,mBAAmB,CAAC,iBAAiB,EAAE,IAAI,CAACC,gBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClG,IAAI,CAACJ,oBAAoB,CAACE,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAACG,WAAW,CAAC;IAC7E,IAAI,CAACL,oBAAoB,CAACE,mBAAmB,CAAC,wBAAwB,EAAE,IAAI,CAACI,uBAAuB,CAAC;;IAErG;IACA,IAAI,CAACC,8BAA8B,CAAC,CAAC;;IAErC;IACA,IAAI,CAACC,OAAO,GAAG;MACbC,eAAe,EAAE,CAAC;MAClBC,gBAAgB,EAAE,CAAC;MACnBC,QAAQ,EAAE,CAAC;MACXC,eAAe,EAAE,CAAC;MAClBC,iBAAiB,EAAE,CAAC;MACpBC,sBAAsB,EAAE,CAAC;MACzBC,wBAAwB,EAAE;IAC5B,CAAC;EACH;;EAEA;AACF;AACA;AACA;EACE,MAAMC,UAAUA,CAACC,WAAW,EAAE;IAC5B,IAAI;MACF,IAAI,CAACC,IAAI,GAAG,IAAI3B,UAAU,CAAC;QACzB0B,WAAW,EAAEA,WAAW;QACxBE,MAAM,EAAE,CAAC,qDAAqD;MAChE,CAAC,CAAC;MAEF,IAAI,CAACC,MAAM,GAAG/B,KAAK,CAACgC,MAAM,CAAC;QACzBC,OAAO,EAAE,IAAI,CAAC3B,OAAO,CAACI,OAAO;QAC7BwB,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACH,MAAM,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAAC,MAAOC,MAAM,IAAK;QACrD,MAAMC,KAAK,GAAG,MAAM,IAAI,CAACV,IAAI,CAACW,cAAc,CAAC,CAAC;QAC9CF,MAAM,CAACJ,OAAO,CAACO,aAAa,GAAG,UAAUF,KAAK,EAAE;QAChD,OAAOD,MAAM;MACf,CAAC,CAAC;MAEF,OAAO;QAAEI,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO;QACLD,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEA,KAAK,CAACE;MACf,CAAC;IACH;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMC,SAASA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IAC3B,IAAI,CAAC,IAAI,CAAChB,MAAM,EAAE;MAChB,MAAM,IAAIiB,KAAK,CAAC,4DAA4D,CAAC;IAC/E;IAEA,MAAMC,SAAS,GAAG,IAAI,CAAC3C,OAAO,CAACC,aAAa,GAAG2C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;IAE7D,IAAI;MACF,MAAM;QACJF,SAAS,EAAEG,cAAc;QACzBC,OAAO,EAAEC,YAAY;QACrB7C,QAAQ,GAAG,IAAI,CAACH,OAAO,CAACG,QAAQ;QAChC8C;MACF,CAAC,GAAGR,MAAM;;MAEV;MACA,MAAMX,OAAO,GAAG;QACd3B,QAAQ;QACR8C;MACF,CAAC;MAED,IAAIH,cAAc,EAAE;QAClBhB,OAAO,CAACa,SAAS,GAAGG,cAAc;MACpC;MAEA,IAAIE,YAAY,EAAE;QAChBlB,OAAO,CAACiB,OAAO,GAAGC,YAAY;MAChC;;MAEA;MACA,MAAME,QAAQ,GAAG,MAAM,IAAI,CAACzB,MAAM,CAAC0B,GAAG,CAAC,SAAS,EAAE;QAAEV,MAAM,EAAEX;MAAQ,CAAC,CAAC;MAEtE,MAAMsB,MAAM,GAAGF,QAAQ,CAACG,IAAI,CAACD,MAAM,IAAI,EAAE;MACzC,MAAME,aAAa,GAAGJ,QAAQ,CAACG,IAAI,CAACC,aAAa;;MAEjD;MACA,IAAI,IAAI,CAACtD,OAAO,CAACC,aAAa,EAAE;QAC9B,MAAM8C,OAAO,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC;QAC1B,MAAMU,OAAO,GAAGR,OAAO,GAAGJ,SAAS;QAEnC,IAAI,CAAC9B,OAAO,CAACG,QAAQ,EAAE;QACvB,IAAI,CAACH,OAAO,CAACC,eAAe,IAAIsC,MAAM,CAACI,MAAM;QAC7C,IAAI,CAAC3C,OAAO,CAACI,eAAe,IAAIsC,OAAO;QACvC,IAAI,CAAC1C,OAAO,CAACK,iBAAiB,GAAG,IAAI,CAACL,OAAO,CAACI,eAAe,GAAG,IAAI,CAACJ,OAAO,CAACG,QAAQ;MACvF;MAEA,OAAO;QACLoC,MAAM;QACNE;MACF,CAAC;IACH,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMoB,OAAOA,CAAChB,MAAM,GAAG,CAAC,CAAC,EAAE;IACzB,IAAI,CAAC,IAAI,CAAChB,MAAM,EAAE;MAChB,MAAM,IAAIiB,KAAK,CAAC,4DAA4D,CAAC;IAC/E;IAEA,MAAMC,SAAS,GAAG,IAAI,CAAC3C,OAAO,CAACC,aAAa,GAAG2C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;IAE7D,IAAI;MACF,MAAM;QACJa,OAAO;QACPC,KAAK;QACLxD,QAAQ,GAAG,IAAI,CAACH,OAAO,CAACG,QAAQ;QAChC8C;MACF,CAAC,GAAGR,MAAM;;MAEV;MACA,MAAMX,OAAO,GAAG;QACd3B,QAAQ;QACR8C;MACF,CAAC;MAED,IAAIS,OAAO,EAAE;QACX5B,OAAO,CAAC4B,OAAO,GAAGA,OAAO;MAC3B;MAEA,IAAIC,KAAK,EAAE;QACT7B,OAAO,CAAC6B,KAAK,GAAGA,KAAK;MACvB;;MAEA;MACA,MAAMT,QAAQ,GAAG,MAAM,IAAI,CAACzB,MAAM,CAAC0B,GAAG,CAAC,OAAO,EAAE;QAAEV,MAAM,EAAEX;MAAQ,CAAC,CAAC;MAEpE,MAAM8B,IAAI,GAAGV,QAAQ,CAACG,IAAI,CAACO,IAAI,IAAI,EAAE;MACrC,MAAMN,aAAa,GAAGJ,QAAQ,CAACG,IAAI,CAACC,aAAa;;MAEjD;MACA,IAAI,IAAI,CAACtD,OAAO,CAACC,aAAa,EAAE;QAC9B,MAAM8C,OAAO,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC;QAC1B,MAAMU,OAAO,GAAGR,OAAO,GAAGJ,SAAS;QAEnC,IAAI,CAAC9B,OAAO,CAACG,QAAQ,EAAE;QACvB,IAAI,CAACH,OAAO,CAACC,eAAe,IAAI8C,IAAI,CAACJ,MAAM;QAC3C,IAAI,CAAC3C,OAAO,CAACI,eAAe,IAAIsC,OAAO;QACvC,IAAI,CAAC1C,OAAO,CAACK,iBAAiB,GAAG,IAAI,CAACL,OAAO,CAACI,eAAe,GAAG,IAAI,CAACJ,OAAO,CAACG,QAAQ;MACvF;MAEA,OAAO;QACL4C,IAAI;QACJN;MACF,CAAC;IACH,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMwB,YAAYA,CAACpB,MAAM,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAI,CAAC,IAAI,CAAChB,MAAM,EAAE;MAChB,MAAM,IAAIiB,KAAK,CAAC,4DAA4D,CAAC;IAC/E;IAEA,MAAMC,SAAS,GAAG,IAAI,CAAC3C,OAAO,CAACC,aAAa,GAAG2C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;IAE7D,IAAI;MACF,MAAM;QACJiB,KAAK;QACLnB,SAAS,EAAEoB,cAAc;QACzBhB,OAAO,EAAEiB,YAAY;QACrB7D,QAAQ,GAAG,IAAI,CAACH,OAAO,CAACG,QAAQ;QAChC8C;MACF,CAAC,GAAGR,MAAM;MAEV,IAAI,CAACqB,KAAK,EAAE;QACV,MAAM,IAAIpB,KAAK,CAAC,6BAA6B,CAAC;MAChD;;MAEA;MACA,MAAMZ,OAAO,GAAG;QACdgC,KAAK;QACL3D,QAAQ;QACR8C;MACF,CAAC;MAED,IAAIc,cAAc,EAAE;QAClBjC,OAAO,CAACa,SAAS,GAAGoB,cAAc;MACpC;MAEA,IAAIC,YAAY,EAAE;QAChBlC,OAAO,CAACiB,OAAO,GAAGiB,YAAY;MAChC;;MAEA;MACA,MAAMd,QAAQ,GAAG,MAAM,IAAI,CAACzB,MAAM,CAACwC,IAAI,CAAC,gBAAgB,EAAEnC,OAAO,CAAC;MAElE,MAAMoC,MAAM,GAAGhB,QAAQ,CAACG,IAAI,CAACa,MAAM,IAAI,EAAE;MACzC,MAAMZ,aAAa,GAAGJ,QAAQ,CAACG,IAAI,CAACC,aAAa;;MAEjD;MACA,IAAI,IAAI,CAACtD,OAAO,CAACC,aAAa,EAAE;QAC9B,MAAM8C,OAAO,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC;QAC1B,MAAMU,OAAO,GAAGR,OAAO,GAAGJ,SAAS;QAEnC,IAAI,CAAC9B,OAAO,CAACG,QAAQ,EAAE;QACvB,IAAI,CAACH,OAAO,CAACC,eAAe,IAAIoD,MAAM,CAACV,MAAM;QAC7C,IAAI,CAAC3C,OAAO,CAACI,eAAe,IAAIsC,OAAO;QACvC,IAAI,CAAC1C,OAAO,CAACK,iBAAiB,GAAG,IAAI,CAACL,OAAO,CAACI,eAAe,GAAG,IAAI,CAACJ,OAAO,CAACG,QAAQ;MACvF;MAEA,OAAO;QACLkD,MAAM;QACNZ;MACF,CAAC;IACH,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE8B,eAAeA,CAACf,MAAM,EAAE;IACtB,MAAMT,SAAS,GAAG,IAAI,CAAC3C,OAAO,CAACC,aAAa,GAAG2C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;IAE7D,IAAI;MACF;MACA,MAAMuB,KAAK,GAAG,CACZ;QAAEC,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE;MAAK,CAAC,EAC9B;QAAED,MAAM,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO,CAAC,EAClC;QAAED,MAAM,EAAE,aAAa;QAAEC,MAAM,EAAE,WAAW;QAAEC,SAAS,EAAE;MAAyB,CAAC,EACnF;QAAEF,MAAM,EAAE,UAAU;QAAEC,MAAM,EAAE,UAAU;QAAEC,SAAS,EAAE;MAAY,CAAC,EAClE;QAAEF,MAAM,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAQ,CAAC,EACnC;QAAED,MAAM,EAAE,aAAa;QAAEC,MAAM,EAAE;MAAc,CAAC,EAChD;QAAED,MAAM,EAAE,gBAAgB;QAAEC,MAAM,EAAE;MAAe,CAAC,EACpD;QAAED,MAAM,EAAE,iBAAiB;QAAEC,MAAM,EAAE;MAAe,CAAC,EACrD;QAAED,MAAM,EAAE,kBAAkB;QAAEC,MAAM,EAAE;MAAmB,CAAC,EAC1D;QAAED,MAAM,EAAE,kBAAkB;QAAEC,MAAM,EAAE,cAAc;QAAEC,SAAS,EAAE;MAAkB,CAAC,EACpF;QAAEF,MAAM,EAAE,YAAY;QAAEC,MAAM,EAAE,YAAY;QAAEC,SAAS,EAAE;MAAa,CAAC,CACxE;;MAED;MACA,MAAMC,gBAAgB,GAAGpB,MAAM,CAACqB,GAAG,CAACC,KAAK,IACvC,IAAI,CAACrE,oBAAoB,CAACkE,SAAS,CAACG,KAAK,EAAEN,KAAK,CAClD,CAAC;;MAED;MACA,IAAI,IAAI,CAACpE,OAAO,CAACC,aAAa,EAAE;QAC9B,MAAM8C,OAAO,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC;QAC1B,MAAM8B,QAAQ,GAAG5B,OAAO,GAAGJ,SAAS;QAEpC,IAAI,CAAC9B,OAAO,CAACE,gBAAgB,IAAIqC,MAAM,CAACI,MAAM;QAC9C,IAAI,CAAC3C,OAAO,CAACM,sBAAsB,IAAIwD,QAAQ;QAC/C,IAAI,CAAC9D,OAAO,CAACO,wBAAwB,GACnC,IAAI,CAACP,OAAO,CAACM,sBAAsB,GAAG,IAAI,CAACN,OAAO,CAACE,gBAAgB;MACvE;MAEA,OAAOyD,gBAAgB;IACzB,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMuC,mBAAmBA,CAACnC,MAAM,GAAG,CAAC,CAAC,EAAE;IACrC,MAAM;MAAEW,MAAM;MAAEE;IAAc,CAAC,GAAG,MAAM,IAAI,CAACd,SAAS,CAACC,MAAM,CAAC;IAC9D,MAAM+B,gBAAgB,GAAG,IAAI,CAACL,eAAe,CAACf,MAAM,CAAC;IAErD,OAAO;MACLA,MAAM,EAAEoB,gBAAgB;MACxBlB;IACF,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMuB,YAAYA,CAACpC,MAAM,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAIqC,SAAS,GAAG,EAAE;IAClB,IAAIxB,aAAa,GAAG,IAAI;IAExB,GAAG;MACD,MAAM;QAAEF,MAAM;QAAEE,aAAa,EAAErB;MAAM,CAAC,GAAG,MAAM,IAAI,CAACO,SAAS,CAAC;QAC5D,GAAGC,MAAM;QACTQ,SAAS,EAAEK;MACb,CAAC,CAAC;MAEFwB,SAAS,GAAGA,SAAS,CAACC,MAAM,CAAC3B,MAAM,CAAC;MACpCE,aAAa,GAAGrB,KAAK;IACvB,CAAC,QAAQqB,aAAa;IAEtB,OAAOwB,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAME,sBAAsBA,CAACvC,MAAM,GAAG,CAAC,CAAC,EAAE;IACxC,MAAMW,MAAM,GAAG,MAAM,IAAI,CAACyB,YAAY,CAACpC,MAAM,CAAC;IAC9C,OAAO,IAAI,CAAC0B,eAAe,CAACf,MAAM,CAAC;EACrC;;EAEA;AACF;AACA;AACA;EACE6B,UAAUA,CAAA,EAAG;IACX,OAAO;MACL,GAAG,IAAI,CAACpE,OAAO;MACfqE,qBAAqB,EAAE,IAAI,CAAC7E,oBAAoB,CAAC4E,UAAU,CAAC;IAC9D,CAAC;EACH;;EAEA;AACF;AACA;AACA;EACErE,8BAA8BA,CAAA,EAAG;IAC/B;IACA,IAAI,CAACuE,mBAAmB,GAAG;MACzB;MACA,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;MAC3B,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;MAAE;MACpC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;MAAE;;MAEpC;MACA,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;MAC3B,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;;MAE3B;MACA,OAAO,EAAE,CAAC,MAAM,CAAC;MAAE;MACnB,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;;MAE3B;MACA,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;MAC3B,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;MAAE;;MAEnC;MACA,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;MAC3B,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;;MAE3B;MACA,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;MAC3B,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;;MAE3B;MACA,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;MAC3B,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;;MAE3B;MACA,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;MAAE;MAC5B,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;;MAE3B;MACA,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;MAC3B,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;;MAE3B;MACA,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;MAC3B,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;;MAE3B;MACA,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;MAC3B,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAAE;;MAE3B;MACA,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;MAAE;MAC5B,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE3E,gBAAgBA,CAAC4E,UAAU,EAAE;IAC3B,IAAI,CAACA,UAAU,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;MAC7C,OAAO,EAAE;IACX;IAEA,MAAMG,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;IAE9B,KAAK,MAAMC,SAAS,IAAIL,UAAU,EAAE;MAClC,MAAMM,WAAW,GAAGD,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,MAAMC,QAAQ,GAAG,IAAI,CAACT,mBAAmB,CAACO,WAAW,CAAC,IAAI,EAAE;MAE5D,KAAK,MAAMG,OAAO,IAAID,QAAQ,EAAE;QAC9BL,YAAY,CAACO,GAAG,CAACD,OAAO,CAAC;MAC3B;IACF;IAEA,OAAOR,KAAK,CAACU,IAAI,CAACR,YAAY,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE7E,WAAWA,CAACsF,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,IAAI,CAACX,KAAK,CAACC,OAAO,CAACU,UAAU,CAAC,EAAE;MAC7C,OAAO,EAAE;IACX;IAEA,OAAOA,UAAU,CAACvB,GAAG,CAACwB,SAAS,KAAK;MAClCC,IAAI,EAAED,SAAS,CAACC,IAAI;MACpBvC,KAAK,EAAEsC,SAAS,CAACtC,KAAK;MACtBwC,UAAU,EAAEF,SAAS,CAACE;IACxB,CAAC,CAAC,CAAC;EACL;;EAEA;AACF;AACA;AACA;AACA;AACA;EACExF,uBAAuBA,CAACyF,SAAS,EAAE;IACjC,IAAI,CAACA,SAAS,EAAE,OAAO,IAAI;;IAE3B;IACA,OAAO,IAAIxD,IAAI,CAACwD,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC;EACtC;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGzG,kBAAkB", "ignoreList": []}