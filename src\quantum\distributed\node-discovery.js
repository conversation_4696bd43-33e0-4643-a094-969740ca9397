/**
 * Node Discovery Service
 *
 * This module provides dynamic node discovery capabilities for the Finite Universe
 * Principle defense system, enabling automatic discovery of nodes in the cluster.
 */

const EventEmitter = require('events');
const dgram = require('dgram');
const os = require('os');
const crypto = require('crypto');

/**
 * NodeDiscovery class
 * 
 * Provides dynamic node discovery capabilities for distributed processing.
 */
class NodeDiscovery extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      discoveryPort: options.discoveryPort || 41234,
      discoveryInterval: options.discoveryInterval || 5000, // 5 seconds
      advertisementInterval: options.advertisementInterval || 10000, // 10 seconds
      nodeTimeout: options.nodeTimeout || 30000, // 30 seconds
      discoveryKey: options.discoveryKey || 'finite-universe-principle',
      networkInterface: options.networkInterface || this._getDefaultInterface(),
      nodeId: options.nodeId || this._generateNodeId(),
      isMaster: options.isMaster !== undefined ? options.isMaster : false,
      capabilities: options.capabilities || ['default'],
      ...options
    };

    // Initialize node information
    this.nodeInfo = {
      id: this.options.nodeId,
      hostname: os.hostname(),
      address: this._getLocalAddress(),
      isMaster: this.options.isMaster,
      cpus: os.cpus().length,
      memory: os.totalmem(),
      freeMemory: os.freemem(),
      platform: os.platform(),
      arch: os.arch(),
      uptime: process.uptime(),
      status: 'initializing',
      lastSeen: Date.now(),
      capabilities: this.options.capabilities,
      load: 0,
      tasks: 0
    };

    // Initialize nodes registry
    this.nodes = new Map();
    
    // Add self to nodes registry
    this.nodes.set(this.nodeInfo.id, { ...this.nodeInfo });

    // Initialize UDP socket
    this.socket = null;
    
    // Initialize intervals
    this.discoveryInterval = null;
    this.advertisementInterval = null;
    this.cleanupInterval = null;

    if (this.options.enableLogging) {
      console.log('NodeDiscovery initialized with options:', {
        ...this.options,
        discoveryKey: '***' // Hide discovery key in logs
      });
      console.log('Node information:', this.nodeInfo);
    }
  }

  /**
   * Start node discovery
   */
  start() {
    if (this.socket) {
      return;
    }

    // Create UDP socket
    this.socket = dgram.createSocket('udp4');
    
    // Set up socket event handlers
    this._setupSocketHandlers();
    
    // Bind socket to discovery port
    this.socket.bind(this.options.discoveryPort, () => {
      // Enable broadcast
      this.socket.setBroadcast(true);
      
      // Update node status
      this.nodeInfo.status = 'active';
      this.nodes.set(this.nodeInfo.id, { ...this.nodeInfo });
      
      if (this.options.enableLogging) {
        console.log(`Node discovery started on port ${this.options.discoveryPort}`);
      }
      
      // Start discovery interval
      this.discoveryInterval = setInterval(() => {
        this._discoverNodes();
      }, this.options.discoveryInterval);
      
      // Start advertisement interval
      this.advertisementInterval = setInterval(() => {
        this._advertiseNode();
      }, this.options.advertisementInterval);
      
      // Start cleanup interval
      this.cleanupInterval = setInterval(() => {
        this._cleanupInactiveNodes();
      }, this.options.nodeTimeout / 2);
      
      // Emit start event
      this.emit('start', { nodeId: this.nodeInfo.id });
      
      // Perform initial discovery
      this._discoverNodes();
      
      // Perform initial advertisement
      this._advertiseNode();
    });
  }

  /**
   * Stop node discovery
   */
  stop() {
    // Clear intervals
    if (this.discoveryInterval) {
      clearInterval(this.discoveryInterval);
      this.discoveryInterval = null;
    }
    
    if (this.advertisementInterval) {
      clearInterval(this.advertisementInterval);
      this.advertisementInterval = null;
    }
    
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    // Update node status
    this.nodeInfo.status = 'stopping';
    this.nodes.set(this.nodeInfo.id, { ...this.nodeInfo });
    
    // Advertise node stopping
    this._advertiseNode();
    
    // Close socket
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    
    // Update node status
    this.nodeInfo.status = 'stopped';
    this.nodes.set(this.nodeInfo.id, { ...this.nodeInfo });
    
    if (this.options.enableLogging) {
      console.log('Node discovery stopped');
    }
    
    // Emit stop event
    this.emit('stop', { nodeId: this.nodeInfo.id });
  }

  /**
   * Set up socket event handlers
   * @private
   */
  _setupSocketHandlers() {
    // Handle incoming messages
    this.socket.on('message', (message, rinfo) => {
      try {
        // Parse message
        const data = JSON.parse(message.toString());
        
        // Verify discovery key
        if (data.key !== this.options.discoveryKey) {
          return;
        }
        
        // Handle message based on type
        switch (data.type) {
          case 'discovery':
            this._handleDiscovery(data, rinfo);
            break;
          case 'advertisement':
            this._handleAdvertisement(data, rinfo);
            break;
          default:
            if (this.options.enableLogging) {
              console.log(`Unknown message type: ${data.type}`);
            }
        }
      } catch (error) {
        if (this.options.enableLogging) {
          console.error('Error handling message:', error);
        }
      }
    });
    
    // Handle socket errors
    this.socket.on('error', (error) => {
      if (this.options.enableLogging) {
        console.error('Socket error:', error);
      }
      
      // Emit error event
      this.emit('error', { error });
    });
    
    // Handle socket close
    this.socket.on('close', () => {
      if (this.options.enableLogging) {
        console.log('Socket closed');
      }
    });
  }

  /**
   * Discover nodes in the cluster
   * @private
   */
  _discoverNodes() {
    // Create discovery message
    const message = {
      key: this.options.discoveryKey,
      type: 'discovery',
      nodeId: this.nodeInfo.id,
      timestamp: Date.now()
    };
    
    // Convert message to buffer
    const buffer = Buffer.from(JSON.stringify(message));
    
    // Send discovery message
    this.socket.send(buffer, 0, buffer.length, this.options.discoveryPort, '255.255.255.255', (error) => {
      if (error) {
        if (this.options.enableLogging) {
          console.error('Error sending discovery message:', error);
        }
        
        // Emit error event
        this.emit('error', { error });
      } else if (this.options.enableLogging) {
        console.log('Discovery message sent');
      }
    });
    
    // Emit discovery event
    this.emit('discovery', { nodeId: this.nodeInfo.id });
  }

  /**
   * Advertise node in the cluster
   * @private
   */
  _advertiseNode() {
    // Update node information
    this.nodeInfo.uptime = process.uptime();
    this.nodeInfo.freeMemory = os.freemem();
    this.nodeInfo.lastSeen = Date.now();
    
    // Create advertisement message
    const message = {
      key: this.options.discoveryKey,
      type: 'advertisement',
      node: { ...this.nodeInfo },
      timestamp: Date.now()
    };
    
    // Convert message to buffer
    const buffer = Buffer.from(JSON.stringify(message));
    
    // Send advertisement message
    this.socket.send(buffer, 0, buffer.length, this.options.discoveryPort, '255.255.255.255', (error) => {
      if (error) {
        if (this.options.enableLogging) {
          console.error('Error sending advertisement message:', error);
        }
        
        // Emit error event
        this.emit('error', { error });
      } else if (this.options.enableLogging) {
        console.log('Advertisement message sent');
      }
    });
    
    // Emit advertisement event
    this.emit('advertisement', { nodeId: this.nodeInfo.id });
  }

  /**
   * Handle discovery message
   * @param {Object} data - Discovery message data
   * @param {Object} rinfo - Remote info
   * @private
   */
  _handleDiscovery(data, rinfo) {
    // Ignore own discovery messages
    if (data.nodeId === this.nodeInfo.id) {
      return;
    }
    
    if (this.options.enableLogging) {
      console.log(`Discovery message received from ${data.nodeId}`);
    }
    
    // Respond with advertisement
    this._advertiseNode();
    
    // Emit discovery-received event
    this.emit('discovery-received', { nodeId: data.nodeId, address: rinfo.address });
  }

  /**
   * Handle advertisement message
   * @param {Object} data - Advertisement message data
   * @param {Object} rinfo - Remote info
   * @private
   */
  _handleAdvertisement(data, rinfo) {
    // Ignore own advertisement messages
    if (data.node.id === this.nodeInfo.id) {
      return;
    }
    
    // Update node address from rinfo
    data.node.address = rinfo.address;
    
    // Check if node is already registered
    const existingNode = this.nodes.get(data.node.id);
    
    if (existingNode) {
      // Update existing node
      this.nodes.set(data.node.id, {
        ...existingNode,
        ...data.node,
        lastSeen: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`Node ${data.node.id} updated`);
      }
      
      // Emit node-updated event
      this.emit('node-updated', { nodeId: data.node.id, node: data.node });
    } else {
      // Register new node
      this.nodes.set(data.node.id, {
        ...data.node,
        lastSeen: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`Node ${data.node.id} discovered`);
      }
      
      // Emit node-discovered event
      this.emit('node-discovered', { nodeId: data.node.id, node: data.node });
    }
  }

  /**
   * Clean up inactive nodes
   * @private
   */
  _cleanupInactiveNodes() {
    const now = Date.now();
    const timeout = this.options.nodeTimeout;
    
    // Check each node
    for (const [nodeId, nodeInfo] of this.nodes.entries()) {
      // Skip self
      if (nodeId === this.nodeInfo.id) {
        continue;
      }
      
      // Check if node is inactive
      if (now - nodeInfo.lastSeen > timeout) {
        // Remove node
        this.nodes.delete(nodeId);
        
        if (this.options.enableLogging) {
          console.log(`Node ${nodeId} timed out`);
        }
        
        // Emit node-timeout event
        this.emit('node-timeout', { nodeId, node: nodeInfo });
      }
    }
  }

  /**
   * Generate a unique node ID
   * @returns {string} - Node ID
   * @private
   */
  _generateNodeId() {
    const hostname = os.hostname();
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000000);
    const hash = crypto.createHash('md5').update(`${hostname}-${timestamp}-${random}`).digest('hex');
    return hash.substring(0, 8);
  }

  /**
   * Get default network interface
   * @returns {string} - Network interface name
   * @private
   */
  _getDefaultInterface() {
    const interfaces = os.networkInterfaces();
    
    // Find first non-internal interface
    for (const name of Object.keys(interfaces)) {
      for (const iface of interfaces[name]) {
        if (iface.family === 'IPv4' && !iface.internal) {
          return name;
        }
      }
    }
    
    // Fallback to first interface
    return Object.keys(interfaces)[0];
  }

  /**
   * Get local IP address
   * @returns {string} - Local IP address
   * @private
   */
  _getLocalAddress() {
    const interfaces = os.networkInterfaces();
    const interfaceName = this.options.networkInterface;
    
    // Get interface
    const iface = interfaces[interfaceName];
    
    if (iface) {
      // Find IPv4 address
      for (const addr of iface) {
        if (addr.family === 'IPv4') {
          return addr.address;
        }
      }
    }
    
    // Fallback to first non-internal IPv4 address
    for (const name of Object.keys(interfaces)) {
      for (const addr of interfaces[name]) {
        if (addr.family === 'IPv4' && !addr.internal) {
          return addr.address;
        }
      }
    }
    
    // Fallback to localhost
    return '127.0.0.1';
  }

  /**
   * Get all discovered nodes
   * @returns {Array} - Array of node information
   */
  getNodes() {
    return Array.from(this.nodes.values());
  }

  /**
   * Get node information
   * @param {string} nodeId - Node ID
   * @returns {Object} - Node information
   */
  getNodeInfo(nodeId) {
    return this.nodes.get(nodeId);
  }

  /**
   * Update node information
   * @param {Object} nodeInfo - Node information
   * @returns {boolean} - True if node was updated, false otherwise
   */
  updateNodeInfo(nodeInfo) {
    // Update local node information
    if (nodeInfo.id === this.nodeInfo.id) {
      this.nodeInfo = {
        ...this.nodeInfo,
        ...nodeInfo,
        lastSeen: Date.now()
      };
      
      // Update node in registry
      this.nodes.set(this.nodeInfo.id, { ...this.nodeInfo });
      
      // Advertise updated node
      this._advertiseNode();
      
      return true;
    }
    
    // Update other node
    if (this.nodes.has(nodeInfo.id)) {
      const existingNode = this.nodes.get(nodeInfo.id);
      
      this.nodes.set(nodeInfo.id, {
        ...existingNode,
        ...nodeInfo,
        lastSeen: Date.now()
      });
      
      return true;
    }
    
    return false;
  }

  /**
   * Dispose resources
   */
  dispose() {
    this.stop();
    
    // Clear nodes registry
    this.nodes.clear();
    
    if (this.options.enableLogging) {
      console.log('NodeDiscovery disposed');
    }
  }
}

/**
 * Create a node discovery service with recommended settings
 * @param {Object} options - Configuration options
 * @returns {NodeDiscovery} - Configured node discovery service
 */
function createNodeDiscovery(options = {}) {
  return new NodeDiscovery({
    enableLogging: true,
    discoveryPort: 41234,
    discoveryInterval: 5000,
    advertisementInterval: 10000,
    nodeTimeout: 30000,
    ...options
  });
}

module.exports = {
  NodeDiscovery,
  createNodeDiscovery
};
