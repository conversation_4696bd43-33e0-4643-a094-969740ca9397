import React from 'react';
import DiagramTemplate from '../DiagramTemplate';

// Define the diagram data
const elements = [
  {
    id: 'healthcare-implementation',
    top: 50,
    left: 350,
    width: 300,
    text: 'Healthcare Industry Implementation',
    number: '1',
    bold: true,
    fontSize: '20px',
    backgroundColor: '#e6f7ff'
  },
  // System Architecture
  {
    id: 'system-architecture',
    top: 120,
    left: 350,
    width: 300,
    text: 'System Architecture',
    number: '2',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'novacore',
    top: 180,
    left: 150,
    width: 200,
    text: 'NovaCore\nUUFT equation for patient data analysis',
    number: '3',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'novashield',
    top: 180,
    left: 400,
    width: 200,
    text: 'NovaShield\nTrinity Equation for medical data protection',
    number: '4',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'novatrack',
    top: 180,
    left: 650,
    width: 200,
    text: 'NovaTrack\nData Purity Score for HIPAA compliance',
    number: '5',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'novalearm',
    top: 240,
    left: 400,
    width: 200,
    text: 'NovaLearn\nAdaptive Coherence for treatment optimization',
    number: '6',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  // Data Flow
  {
    id: 'data-flow',
    top: 300,
    left: 350,
    width: 300,
    text: 'Data Flow',
    number: '7',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'healthcare-data',
    top: 360,
    left: 150,
    width: 200,
    text: 'Healthcare Data Sources',
    number: '8',
    fontSize: '14px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'patient-records',
    top: 420,
    left: 50,
    width: 150,
    text: 'Patient Records',
    number: '9',
    fontSize: '12px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'clinical-data',
    top: 420,
    left: 225,
    width: 150,
    text: 'Clinical Data',
    number: '10',
    fontSize: '12px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'medical-imaging',
    top: 420,
    left: 400,
    width: 150,
    text: 'Medical Imaging',
    number: '11',
    fontSize: '12px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'device-telemetry',
    top: 420,
    left: 575,
    width: 150,
    text: 'Device Telemetry',
    number: '12',
    fontSize: '12px',
    backgroundColor: '#e6f7ff'
  },
  // Processing
  {
    id: 'processing',
    top: 480,
    left: 350,
    width: 300,
    text: 'Data Processing',
    number: '13',
    fontSize: '14px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'pattern-identification',
    top: 540,
    left: 150,
    width: 200,
    text: 'Pattern Identification\nPatient outcomes, treatment efficacy',
    number: '14',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'security-assessment',
    top: 540,
    left: 400,
    width: 200,
    text: 'Security Assessment\nPatient data, medical devices',
    number: '15',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'compliance-evaluation',
    top: 540,
    left: 650,
    width: 200,
    text: 'Compliance Evaluation\nHIPAA, GDPR regulations',
    number: '16',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  // Outputs
  {
    id: 'outputs',
    top: 600,
    left: 350,
    width: 300,
    text: 'System Outputs',
    number: '17',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'treatment-protocols',
    top: 660,
    left: 150,
    width: 200,
    text: 'Treatment Protocol Optimization',
    number: '18',
    fontSize: '14px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'personalized-medicine',
    top: 660,
    left: 400,
    width: 200,
    text: 'Personalized Medicine',
    number: '19',
    fontSize: '14px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'compliance-reporting',
    top: 660,
    left: 650,
    width: 200,
    text: 'Compliance Reporting',
    number: '20',
    fontSize: '14px',
    backgroundColor: '#fff2e8'
  },
  // Key Metrics
  {
    id: 'key-metrics',
    top: 720,
    left: 350,
    width: 300,
    text: 'Key Metrics: 3,142x improvement, 95% accuracy',
    number: '21',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fffbe6'
  }
];

const connections = [
  // Connect Healthcare Implementation to System Architecture
  {
    start: { x: 500, y: 100 },
    end: { x: 500, y: 120 },
    type: 'arrow'
  },
  // Connect System Architecture to components
  {
    start: { x: 350, y: 170 },
    end: { x: 250, y: 180 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 170 },
    end: { x: 500, y: 180 },
    type: 'arrow'
  },
  {
    start: { x: 650, y: 170 },
    end: { x: 750, y: 180 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 210 },
    end: { x: 500, y: 240 },
    type: 'arrow'
  },
  // Connect to Data Flow
  {
    start: { x: 500, y: 270 },
    end: { x: 500, y: 300 },
    type: 'arrow'
  },
  // Connect Data Flow to Healthcare Data Sources
  {
    start: { x: 350, y: 350 },
    end: { x: 250, y: 360 },
    type: 'arrow'
  },
  // Connect Healthcare Data Sources to specific sources
  {
    start: { x: 150, y: 390 },
    end: { x: 125, y: 420 },
    type: 'arrow'
  },
  {
    start: { x: 200, y: 390 },
    end: { x: 300, y: 420 },
    type: 'arrow'
  },
  {
    start: { x: 250, y: 390 },
    end: { x: 475, y: 420 },
    type: 'arrow'
  },
  {
    start: { x: 300, y: 390 },
    end: { x: 650, y: 420 },
    type: 'arrow'
  },
  // Connect to Processing
  {
    start: { x: 500, y: 450 },
    end: { x: 500, y: 480 },
    type: 'arrow'
  },
  // Connect Processing to specific processes
  {
    start: { x: 350, y: 510 },
    end: { x: 250, y: 540 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 510 },
    end: { x: 500, y: 540 },
    type: 'arrow'
  },
  {
    start: { x: 650, y: 510 },
    end: { x: 750, y: 540 },
    type: 'arrow'
  },
  // Connect to Outputs
  {
    start: { x: 500, y: 570 },
    end: { x: 500, y: 600 },
    type: 'arrow'
  },
  // Connect Outputs to specific outputs
  {
    start: { x: 350, y: 650 },
    end: { x: 250, y: 660 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 650 },
    end: { x: 500, y: 660 },
    type: 'arrow'
  },
  {
    start: { x: 650, y: 650 },
    end: { x: 750, y: 660 },
    type: 'arrow'
  },
  // Connect to Key Metrics
  {
    start: { x: 500, y: 690 },
    end: { x: 500, y: 720 },
    type: 'arrow'
  }
];

const HealthcareImplementation: React.FC = () => {
  return (
    <DiagramTemplate 
      elements={elements} 
      connections={connections} 
      width="900px" 
      height="770px" 
    />
  );
};

export default HealthcareImplementation;
