# NovaVision Security Module

This module implements security features for NovaVision, including:

1. **Role-Based Access Control (RBAC)**: Ensures that sensitive information is only visible to users with appropriate roles.
2. **NIST Security Requirements**: Implements NIST security controls for UI rendering.

## Overview

The NovaVision Security Module consists of three main components:

- **NovaVisionRBAC**: Implements role-based access control
- **NovaVisionNISTSecurity**: Implements NIST security requirements
- **NovaVisionSecurityManager**: Integrates RBAC and NIST security

## RBAC Implementation

The RBAC implementation defines roles and permissions for NovaVision:

### Roles

- **Executive**: Executive leadership (CEO, CFO, etc.)
- **CISO**: Chief Information Security Officer
- **Security Analyst**: Security analyst role
- **Compliance Officer**: Compliance officer role
- **Auditor**: External or internal auditor
- **Admin**: System administrator
- **User**: Standard user with basic access
- **Partner**: Integration partner

### Permissions

- **View Permissions**: Control what content users can see
  - `VIEW_SUMMARY`: View summary information
  - `VIEW_DETAILED`: View detailed information
  - `VIEW_ALL`: View all information
  
- **Domain-Specific View Permissions**:
  - `VIEW_SECURITY`: View security information
  - `VIEW_COMPLIANCE`: View compliance information
  - `VIEW_FINANCIAL`: View financial information
  - `VIEW_STRATEGIC`: View strategic information
  - `VIEW_INTEGRATION`: View integration information
  - `VIEW_AUDIT_LOGS`: View audit logs
  
- **NovaFuse-Specific View Permissions**:
  - `VIEW_TRINITY_CSDE`: View Trinity CSDE information
  - `VIEW_OPTIMIZATION`: View optimization information
  - `VIEW_UUFT`: View UUFT information
  
- **Management Permissions**:
  - `MANAGE_USERS`: Manage users
  - `MANAGE_ROLES`: Manage roles
  - `MANAGE_SYSTEM`: Manage system

## NIST Security Implementation

The NIST security implementation applies security controls to UI schemas and rendered content:

### Security Controls

- **Content Security Policy (CSP)**: Prevents XSS attacks
- **XSS Protection**: Protects against cross-site scripting
- **Subresource Integrity (SRI)**: Ensures resources haven't been tampered with
- **HTTP Strict Transport Security (HSTS)**: Enforces secure connections
- **Secure Cookies**: Protects cookies from being accessed by malicious scripts
- **Input Validation**: Validates user input
- **Output Encoding**: Encodes output to prevent injection attacks
- **Audit Logging**: Logs security events
- **Access Control**: Controls access to resources
- **Secure Defaults**: Applies secure default settings

### NIST Frameworks

The module supports the following NIST frameworks:

- **NIST SP 800-53**: Security and Privacy Controls for Federal Information Systems and Organizations
- **NIST Cybersecurity Framework (CSF)**: Framework for Improving Critical Infrastructure Cybersecurity
- **NIST SP 800-171**: Protecting Controlled Unclassified Information in Nonfederal Systems and Organizations

## Usage

### Basic Usage

```javascript
const { NovaVision } = require('../index');

// Create a NovaVision instance with security enabled
const novaVision = new NovaVision({
  enableSecurity: true,
  enableNIST: true,
  enableRBAC: true
});

// Assign roles to users
const securityManager = novaVision.securityManager;
securityManager.rbac.assignRole('user-123', 'CISO');
securityManager.rbac.assignRole('user-456', 'ADMIN');

// Render UI with security
const secureUI = novaVision.renderUiFromSchema(
  schema,
  data,
  { userId: 'user-123' }
);

// Validate access to components
const hasAccess = novaVision.validateAccess('user-123', 'trinity_dashboard');

// Get security report
const securityReport = novaVision.getSecurityReport();
```

### Advanced Configuration

```javascript
const novaVision = new NovaVision({
  enableSecurity: true,
  enableNIST: true,
  enableRBAC: true,
  rbacOptions: {
    strictMode: true // Deny by default if role/permission not found
  },
  nistOptions: {
    enableCSP: true,
    enableXSS: true,
    enableSRI: true,
    enableHSTS: true,
    enableSecureCookies: true,
    enableInputValidation: true,
    enableOutputEncoding: true,
    enableAuditLogging: true,
    enableAccessControl: true,
    enableSecureDefaults: true,
    nistFrameworks: ['SP800-53', 'CSF', 'SP800-171']
  }
});
```

## Security Report

The security report provides information about the security status of NovaVision:

```javascript
const securityReport = novaVision.getSecurityReport();
```

The report includes:

- **NIST Compliance**: Compliance with NIST frameworks
- **Security Log**: Log of security events
- **Applied Controls**: Security controls that have been applied

## Examples

See the `examples/security-example.js` file for a complete example of using the NovaVision Security Module.

## Integration with NovaFuse Trust Architecture

The NovaVision Security Module is part of the NovaFuse Trust Architecture, which provides a comprehensive security framework for NovaFuse applications. It implements the security principles defined in the NovaFuse Trust Architecture, including:

- **Role-Based Access Control**: Ensures that sensitive information is only visible to users with appropriate roles
- **NIST Security Requirements**: Implements NIST security controls for UI rendering
- **Audit Logging**: Logs security events for compliance and forensic analysis
- **Secure Defaults**: Applies secure default settings to prevent security misconfigurations
