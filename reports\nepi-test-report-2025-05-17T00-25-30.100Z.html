
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NEPI Test Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;
    }
    .summary-box {
      background-color: #f5f5f5;
      border-radius: 5px;
      padding: 15px;
      width: 30%;
    }
    .pass-rate {
      font-size: 24px;
      font-weight: bold;
      color: #4CAF50;
    }
    .suite {
      margin-bottom: 30px;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
    }
    .suite-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    .suite-status {
      font-weight: bold;
      color: #4CAF50;
    }
    .test-case {
      margin: 10px 0;
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 3px;
    }
    .test-case.passed {
      border-left: 5px solid #4CAF50;
    }
    .test-case.failed {
      border-left: 5px solid #F44336;
    }
    .test-case.skipped {
      border-left: 5px solid #FF9800;
    }
    .error {
      color: #F44336;
      font-family: monospace;
      white-space: pre-wrap;
      margin-top: 10px;
      padding: 10px;
      background-color: #ffebee;
      border-radius: 3px;
    }
    .charts {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-bottom: 30px;
    }
    .chart {
      width: 48%;
      margin-bottom: 20px;
      background-color: #f5f5f5;
      border-radius: 5px;
      padding: 15px;
    }
  </style>
</head>
<body>
  <h1>NEPI Test Report</h1>
  <p>Generated: 5/16/2025, 7:25:30 PM</p>
  
  <div class="summary">
    <div class="summary-box">
      <h2>Test Summary</h2>
      <p>Total: 36</p>
      <p>Passed: 35</p>
      <p>Failed: 1</p>
      <p>Skipped: 0</p>
      <p>Duration: 0.02s</p>
      <p>Pass Rate: <span class="pass-rate">97%</span></p>
    </div>
    
    <div class="summary-box">
      <h2>Testing Layers</h2>
      
        <p>Physics: 24 tests</p>
      
        <p>Operational: 6 tests</p>
      
        <p>Security: 6 tests</p>
      
    </div>
    
    <div class="summary-box">
      <h2>Domains</h2>
      
        <p>universal: 36 tests</p>
      
        <p>cyber: 24 tests</p>
      
        <p>financial: 24 tests</p>
      
        <p>biological: 24 tests</p>
      
    </div>
  </div>
  
  <div class="charts">
    <div class="chart">
      <h2>Coherence Impact</h2>
      <p>Positive: 36</p>
      <p>Neutral: 0</p>
      <p>Negative: 0</p>
    </div>
    
    <div class="chart">
      <h2>Entropy Metrics</h2>
      <p>Coming soon...</p>
    </div>
  </div>
  
  <h2>Test Suites</h2>
  
    <div class="suite">
      <div class="suite-header">
        <h3>Foundational Physics Tests</h3>
        <span class="suite-status">
          6/6 passed (100%)
        </span>
      </div>
      
      <p>Testing Layer: Physics</p>
      <p>Domains: universal</p>
      <p>Duration: 0.00s</p>
      
      <h4>Test Cases</h4>
      
        <div class="test-case passed">
          <strong>should correctly apply the UUFT formula</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Physics Validation</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should propagate the Ripple Effect through all three layers</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Physics Validation</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should adhere to the 18/82 principle</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Physics Validation</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should maintain the Nested Trinity structure</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Physics Validation</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should maintain the correct π10³ constant value</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Physics Validation</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should maintain the correct Golden Ratio value</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Physics Validation</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
    </div>
  
    <div class="suite">
      <div class="suite-header">
        <h3>Meter Tests</h3>
        <span class="suite-status">
          6/6 passed (100%)
        </span>
      </div>
      
      <p>Testing Layer: Operational</p>
      <p>Domains: universal, cyber, financial, biological</p>
      <p>Duration: 0.00s</p>
      
      <h4>Test Cases</h4>
      
        <div class="test-case passed">
          <strong>should correctly calculate Comphyons</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Meter Validation</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should correctly calculate domain energies</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Meter Validation</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: cyber, financial, biological</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should correctly calculate energy gradients</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Meter Validation</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: cyber, financial, biological</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should detect emergent intelligence</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Meter Validation</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should validate Comphyon thresholds</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Meter Validation</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should monitor Comphyons in real-time</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Meter Validation</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
    </div>
  
    <div class="suite">
      <div class="suite-header">
        <h3>Adversarial Tests</h3>
        <span class="suite-status">
          5/6 passed (83%)
        </span>
      </div>
      
      <p>Testing Layer: Security</p>
      <p>Domains: universal, cyber, financial, biological</p>
      <p>Duration: 0.01s</p>
      
      <h4>Test Cases</h4>
      
        <div class="test-case passed">
          <strong>should handle boundary values correctly</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Adversarial Testing</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should handle malformed inputs gracefully</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Adversarial Testing</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should resist quantum attacks</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Adversarial Testing</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal, cyber</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should resist cross-domain attacks</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Adversarial Testing</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: cyber, financial, biological</p>
          
        </div>
      
        <div class="test-case failed">
          <strong>should resist tensor manipulation attacks</strong>
          <p>Status: failed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Adversarial Testing</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal, cyber</p>
          <div class="error">Failed to detect tensor manipulation: Tensor value modification attack</div>
        </div>
      
        <div class="test-case passed">
          <strong>should handle emergent behaviors safely</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Adversarial Testing</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
    </div>
  
    <div class="suite">
      <div class="suite-header">
        <h3>Quantum Resilience Tests</h3>
        <span class="suite-status">
          6/6 passed (100%)
        </span>
      </div>
      
      <p>Testing Layer: Physics</p>
      <p>Domains: universal, cyber, financial, biological</p>
      <p>Duration: 0.00s</p>
      
      <h4>Test Cases</h4>
      
        <div class="test-case passed">
          <strong>should maintain π10³ constant integrity</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Quantum Resilience</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should verify tensor integrity during operations</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Quantum Resilience</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal, cyber</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should preserve quantum entanglement properties</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Quantum Resilience</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal, cyber, financial, biological</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should maintain cross-domain coherence during quantum operations</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Quantum Resilience</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: cyber, financial, biological</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should protect π10³ constant under high computational load</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Quantum Resilience</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should detect tensor manipulation attempts</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Quantum Resilience</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal, cyber</p>
          
        </div>
      
    </div>
  
    <div class="suite">
      <div class="suite-header">
        <h3>π10³ Constant Protection Tests</h3>
        <span class="suite-status">
          6/6 passed (100%)
        </span>
      </div>
      
      <p>Testing Layer: Physics</p>
      <p>Domains: universal</p>
      <p>Duration: 0.00s</p>
      
      <h4>Test Cases</h4>
      
        <div class="test-case passed">
          <strong>should maintain exact π10³ constant value</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Constant Protection</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should resist quantum manipulation of π10³ constant</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Constant Protection</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should apply π10³ constant consistently in UUFT formula</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Constant Protection</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should protect π10³ constant under quantum decoherence attack</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Constant Protection</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should verify π10³ constant across all system components</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Constant Protection</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should maintain π10³ constant memory integrity</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Constant Protection</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
    </div>
  
    <div class="suite">
      <div class="suite-header">
        <h3>Tensor Integrity Tests</h3>
        <span class="suite-status">
          6/6 passed (100%)
        </span>
      </div>
      
      <p>Testing Layer: Physics</p>
      <p>Domains: universal, cyber, financial, biological</p>
      <p>Duration: 0.00s</p>
      
      <h4>Test Cases</h4>
      
        <div class="test-case passed">
          <strong>should maintain mathematical consistency in tensor product operations</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Tensor Integrity</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should maintain mathematical consistency in tensor fusion operations</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Tensor Integrity</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should detect tensor manipulation attempts</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Tensor Integrity</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal, cyber</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should maintain cross-domain coherence during tensor operations</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Tensor Integrity</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: cyber, financial, biological</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should validate tensor dimensionality</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Tensor Integrity</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
        <div class="test-case passed">
          <strong>should correctly apply UUFT formula to tensors</strong>
          <p>Status: passed</p>
          <p>Duration: 0.00s</p>
          <p>Testing Type: Tensor Integrity</p>
          <p>Coherence Impact: positive</p>
          <p>Domains: universal</p>
          
        </div>
      
    </div>
  
</body>
</html>
  