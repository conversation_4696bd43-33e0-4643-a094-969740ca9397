/**
 * Test Run Detail Page
 * 
 * This page displays the details of a specific test run including
 * step results, evidence, and notes.
 */

import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { useAuth } from '../../../contexts/AuthContext';
import ProtectedRoute from '../../../components/ProtectedRoute';
import useNovaAssureApi from '../../../hooks/useNovaAssureApi';
import useFetch from '../../../hooks/useFetch';
import LoadingSpinner from '../../../components/ui/loading-spinner';
import ErrorDisplay from '../../../components/ui/error-display';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { 
  ArrowLeft, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Clock, 
  FileText, 
  Download
} from 'lucide-react';

export default function TestRunDetailPage() {
  return (
    <ProtectedRoute>
      <TestRunDetailContent />
    </ProtectedRoute>
  );
}

function TestRunDetailContent() {
  const router = useRouter();
  const { id } = router.query;
  const { api, callApi } = useNovaAssureApi();
  const [error, setError] = useState<Error | null>(null);
  
  // Fetch test run details
  const { 
    data: testRun, 
    loading,
    error: testRunError
  } = useFetch(
    async () => await callApi(() => api.getTestRunById(id as string)),
    { 
      dependencies: [api, id],
      immediate: !!id // Only fetch when ID is available
    }
  );
  
  // Handle errors
  React.useEffect(() => {
    if (testRunError) {
      setError(testRunError);
    } else {
      setError(null);
    }
  }, [testRunError]);
  
  const handleDismissError = () => {
    setError(null);
  };
  
  // Format date to readable format
  const formatDate = (dateString: string | null): string => {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    }) + ' ' + date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Get status icon based on result
  const getStatusIcon = (status: string, result: string | null) => {
    if (status === 'pending' || status === 'in_progress') {
      return <Clock className="h-5 w-5 text-blue-500" />;
    }
    
    if (status === 'cancelled') {
      return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
    
    if (result === 'pass') {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
    
    if (result === 'fail') {
      return <XCircle className="h-5 w-5 text-red-500" />;
    }
    
    return <AlertCircle className="h-5 w-5 text-yellow-500" />;
  };
  
  // Get status text based on status and result
  const getStatusText = (status: string, result: string | null): string => {
    if (status === 'pending') return 'Pending';
    if (status === 'in_progress') return 'In Progress';
    if (status === 'cancelled') return 'Cancelled';
    if (result === 'pass') return 'Passed';
    if (result === 'fail') return 'Failed';
    return 'Inconclusive';
  };
  
  // Get status color based on status and result
  const getStatusColor = (status: string, result: string | null): string => {
    if (status === 'pending' || status === 'in_progress') return 'text-blue-600';
    if (status === 'cancelled') return 'text-gray-600';
    if (result === 'pass') return 'text-green-600';
    if (result === 'fail') return 'text-red-600';
    return 'text-yellow-600';
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button 
            variant="outline" 
            size="sm" 
            className="mr-4"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">Test Run Details</h1>
        </div>
      </div>
      
      {error && (
        <div className="mb-6">
          <ErrorDisplay 
            message="Error loading test run details" 
            details={error.message}
            severity="error"
            onDismiss={handleDismissError}
          />
        </div>
      )}
      
      {loading ? (
        <LoadingSpinner size="large" text="Loading test run details..." fullPage />
      ) : !testRun ? (
        <div className="text-center py-12">
          <h2 className="text-xl font-medium text-gray-600">Test run not found</h2>
          <p className="mt-2 text-gray-500">The requested test run could not be found or you don't have permission to view it.</p>
          <Button 
            className="mt-6"
            onClick={() => router.push('/novaassure')}
          >
            Return to Dashboard
          </Button>
        </div>
      ) : (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Test Run Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-md mb-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-xl font-bold">{testRun.testName}</h2>
                    <p className="text-gray-600 mt-1">{testRun.frameworkName} - {testRun.controlName}</p>
                  </div>
                  <div className={`flex items-center ${getStatusColor(testRun.status, testRun.result)}`}>
                    {getStatusIcon(testRun.status, testRun.result)}
                    <span className="ml-2 font-medium">
                      {getStatusText(testRun.status, testRun.result)}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div>
                  <div className="text-sm text-gray-500">Started</div>
                  <div className="font-medium">{formatDate(testRun.startTime)}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Completed</div>
                  <div className="font-medium">{formatDate(testRun.endTime)}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Executed By</div>
                  <div className="font-medium">{testRun.executedBy}</div>
                </div>
              </div>
              
              {testRun.notes && (
                <div className="mb-6">
                  <div className="text-sm font-medium text-gray-500 mb-1">Notes</div>
                  <div className="p-3 bg-gray-50 rounded-md">{testRun.notes}</div>
                </div>
              )}
              
              {testRun.issues && testRun.issues.length > 0 && (
                <div>
                  <div className="text-sm font-medium text-gray-500 mb-1">Issues</div>
                  <div className="space-y-2">
                    {testRun.issues.map((issue) => (
                      <div key={issue.id} className="p-3 bg-red-50 border border-red-200 rounded-md">
                        <div className="font-medium text-red-800">{issue.title}</div>
                        <div className="text-sm text-red-700 mt-1">{issue.description}</div>
                        <div className="flex justify-between items-center mt-2 text-xs text-red-600">
                          <div>Severity: {issue.severity}</div>
                          <div>Status: {issue.status}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Step Results</CardTitle>
            </CardHeader>
            <CardContent>
              {testRun.stepResults.length === 0 ? (
                <div className="text-center py-6 text-gray-500">
                  No step results available
                </div>
              ) : (
                <div className="space-y-4">
                  {testRun.stepResults.map((step, index) => {
                    let stepIcon;
                    let stepColor;
                    
                    if (step.status === 'pending' || step.status === 'in_progress') {
                      stepIcon = <Clock className="h-4 w-4" />;
                      stepColor = 'text-blue-600';
                    } else if (step.status === 'skipped') {
                      stepIcon = <AlertCircle className="h-4 w-4" />;
                      stepColor = 'text-gray-600';
                    } else if (step.result === 'pass') {
                      stepIcon = <CheckCircle className="h-4 w-4" />;
                      stepColor = 'text-green-600';
                    } else if (step.result === 'fail') {
                      stepIcon = <XCircle className="h-4 w-4" />;
                      stepColor = 'text-red-600';
                    } else {
                      stepIcon = <AlertCircle className="h-4 w-4" />;
                      stepColor = 'text-yellow-600';
                    }
                    
                    return (
                      <div key={step.stepId} className="p-4 border rounded-md bg-gray-50">
                        <div className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 text-blue-800 mr-3">
                            {index + 1}
                          </div>
                          <div className="flex-grow">
                            <div className="flex justify-between items-start">
                              <h3 className="font-medium">{step.stepName}</h3>
                              <div className={`flex items-center ${stepColor}`}>
                                {stepIcon}
                                <span className="ml-1 text-sm">
                                  {step.status === 'pending' ? 'Pending' :
                                   step.status === 'in_progress' ? 'In Progress' :
                                   step.status === 'skipped' ? 'Skipped' :
                                   step.result === 'pass' ? 'Passed' :
                                   step.result === 'fail' ? 'Failed' : 'Inconclusive'}
                                </span>
                              </div>
                            </div>
                            
                            {step.notes && (
                              <div className="mt-2">
                                <div className="text-xs text-gray-500">Notes</div>
                                <p className="text-sm mt-1">{step.notes}</p>
                              </div>
                            )}
                            
                            {step.evidence && step.evidence.length > 0 && (
                              <div className="mt-2">
                                <div className="text-xs text-gray-500">Evidence</div>
                                <div className="flex flex-wrap gap-2 mt-1">
                                  {step.evidence.map((evidence) => (
                                    <a 
                                      key={evidence.id}
                                      href={evidence.url}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="flex items-center px-2 py-1 bg-blue-50 text-blue-700 rounded text-xs hover:bg-blue-100"
                                    >
                                      <FileText className="h-3 w-3 mr-1" />
                                      {evidence.name}
                                    </a>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            <div className="mt-2 text-xs text-gray-500">
                              {step.startTime && (
                                <span>
                                  Started: {formatDate(step.startTime)}
                                  {step.endTime && (
                                    <span> • Duration: {Math.round((new Date(step.endTime).getTime() - new Date(step.startTime).getTime()) / 1000)} seconds</span>
                                  )}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Evidence</CardTitle>
            </CardHeader>
            <CardContent>
              {testRun.evidence.length === 0 ? (
                <div className="text-center py-6 text-gray-500">
                  No evidence available
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {testRun.evidence.map((evidence) => (
                    <div key={evidence.id} className="p-4 border rounded-md bg-gray-50">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium">{evidence.name}</h3>
                          <p className="text-sm text-gray-600 mt-1">{evidence.description}</p>
                        </div>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          evidence.verificationStatus === 'verified' 
                            ? 'bg-green-100 text-green-800' 
                            : evidence.verificationStatus === 'rejected'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {evidence.verificationStatus.charAt(0).toUpperCase() + evidence.verificationStatus.slice(1)}
                        </span>
                      </div>
                      
                      <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-gray-500">Type:</span> {evidence.type}
                        </div>
                        <div>
                          <span className="text-gray-500">Format:</span> {evidence.format}
                        </div>
                      </div>
                      
                      <div className="mt-3 text-xs text-gray-500">
                        Uploaded by {evidence.uploadedBy} on {formatDate(evidence.uploadedAt)}
                      </div>
                      
                      <div className="mt-3">
                        <a 
                          href={evidence.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Download Evidence
                        </a>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
          
          <div className="flex justify-between">
            <Link href={`/novaassure/tests/${testRun.testId}`}>
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Test
              </Button>
            </Link>
            
            {testRun.status === 'in_progress' && (
              <div className="space-x-2">
                <Button 
                  variant="outline" 
                  onClick={() => router.push(`/novaassure/test-runs/${testRun._id}/complete`)}
                >
                  Complete Test Run
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
