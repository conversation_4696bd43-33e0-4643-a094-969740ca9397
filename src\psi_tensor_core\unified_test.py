#!/usr/bin/env python3
"""
Unified Test Script for Ψ Tensor Core with Energy-Based Comphyon Calculation

This script demonstrates the functionality of the Unified Ψ Tensor Core with
energy-based Comphyon calculation.
"""

import numpy as np
import json
import time
import matplotlib.pyplot as plt

# Import the necessary components
from energy_calculator import EnergyCalculator
# Don't import PyTorch-dependent modules
# from psi_tensor_core import PsiTensorCore
# from dynamic_weighting import DynamicWeightingProtocol
# from quantum_consensus import QuantumConsensusEngine

class SimplifiedUnifiedPsiTensorCore:
    """
    Simplified version of the Unified Ψ Tensor Core for testing.
    """

    def __init__(self):
        """
        Initialize the Simplified Unified Ψ Tensor Core.
        """
        # Initialize components
        self.energy_calculator = EnergyCalculator()

    def process(self, csde_tensor, csfe_tensor, csme_tensor):
        """
        Process tensors and calculate Comphyon value.
        """
        # Calculate domain-specific energies
        E_CSDE = self.energy_calculator.calculate_csde_energy(csde_tensor)
        E_CSFE = self.energy_calculator.calculate_csfe_energy(csfe_tensor)
        E_CSME = self.energy_calculator.calculate_csme_energy(csme_tensor)

        # Calculate Comphyon value
        comphyon_result = self.energy_calculator.calculate_comphyon(
            csde_tensor, csfe_tensor, csme_tensor
        )

        # Create result
        result = {
            "energies": {
                "CSDE": E_CSDE,
                "CSFE": E_CSFE,
                "CSME": E_CSME
            },
            "comphyon": comphyon_result["Cph"],
            "gradients": comphyon_result["Gradients"]
        }

        return result

def create_sample_data():
    """Create sample data for CSDE, CSFE, and CSME engines."""
    # Sample CSDE data [G, D, A₁, c₁]
    csde_tensor = np.array([
        0.75,  # G: Governance input signal
        0.85,  # D: Data relevance signal
        0.65,  # A₁: Risk classification or threat vector
        0.90   # c₁: Contextual accuracy or confidence
    ])

    # Sample CSFE data [F₁, P, A₂, c₂]
    csfe_tensor = np.array([
        0.65,  # F₁: Framework signal
        0.70,  # P: Policy relevance
        0.80,  # A₂: Alignment accuracy
        0.80   # c₂: Confidence
    ])

    # Sample CSME data [T, I, E, c₃]
    csme_tensor = np.array([
        0.70,  # T: Trust
        0.90,  # I: Integrity
        0.60,  # E: Ethical component
        0.85   # c₃: Confidence
    ])

    return csde_tensor, csfe_tensor, csme_tensor

def create_time_series_data(num_samples=10, time_interval=1.0):
    """Create time series data for testing gradients."""
    time_series = []

    # Base tensors
    base_csde = np.array([0.75, 0.85, 0.65, 0.90])
    base_csfe = np.array([0.65, 0.70, 0.80, 0.80])
    base_csme = np.array([0.70, 0.90, 0.60, 0.85])

    # Create time series with small variations
    for i in range(num_samples):
        # Add some random variation
        csde_variation = np.random.uniform(-0.05, 0.05, 4)
        csfe_variation = np.random.uniform(-0.05, 0.05, 4)
        csme_variation = np.random.uniform(-0.05, 0.05, 4)

        # Add some trend (increasing risk over time)
        trend_factor = i / (num_samples * 2)
        csde_trend = np.array([0.0, 0.0, trend_factor, 0.0])

        # Combine base, variation, and trend
        csde_tensor = np.clip(base_csde + csde_variation + csde_trend, 0.1, 0.99)
        csfe_tensor = np.clip(base_csfe + csfe_variation, 0.1, 0.99)
        csme_tensor = np.clip(base_csme + csme_variation, 0.1, 0.99)

        # Create timestamp
        timestamp = time.time() + i * time_interval

        time_series.append({
            "csde_tensor": csde_tensor,
            "csfe_tensor": csfe_tensor,
            "csme_tensor": csme_tensor,
            "timestamp": timestamp
        })

    return time_series

def plot_results(results):
    """Plot the results of the Comphyon calculation."""
    # Extract data
    timestamps = [i for i in range(len(results))]

    comphyon_values = [result["comphyon"] for result in results]

    csde_energies = [result["energies"]["CSDE"] for result in results]
    csfe_energies = [result["energies"]["CSFE"] for result in results]
    csme_energies = [result["energies"]["CSME"] for result in results]

    csde_gradients = [result["gradients"]["CSDE"] for result in results]
    csfe_gradients = [result["gradients"]["CSFE"] for result in results]
    csme_gradients = [result["gradients"]["CSME"] for result in results]

    # Create figure with subplots
    fig, axs = plt.subplots(3, 1, figsize=(10, 12))

    # Plot Comphyon values
    axs[0].plot(timestamps, comphyon_values, 'o-', label='Comphyon (Cph)')
    axs[0].set_title('Comphyon Values')
    axs[0].set_xlabel('Time Step')
    axs[0].set_ylabel('Cph')
    axs[0].grid(True)
    axs[0].legend()

    # Plot energies
    axs[1].plot(timestamps, csde_energies, 'o-', label='E_CSDE')
    axs[1].plot(timestamps, csfe_energies, 'o-', label='E_CSFE')
    axs[1].plot(timestamps, csme_energies, 'o-', label='E_CSME')
    axs[1].set_title('Domain-Specific Energies')
    axs[1].set_xlabel('Time Step')
    axs[1].set_ylabel('Energy')
    axs[1].grid(True)
    axs[1].legend()

    # Plot gradients
    axs[2].plot(timestamps, csde_gradients, 'o-', label='∇E_CSDE')
    axs[2].plot(timestamps, csfe_gradients, 'o-', label='∇E_CSFE')
    axs[2].plot(timestamps, csme_gradients, 'o-', label='∇E_CSME')
    axs[2].set_title('Energy Gradients')
    axs[2].set_xlabel('Time Step')
    axs[2].set_ylabel('Gradient (dE/dt)')
    axs[2].grid(True)
    axs[2].legend()

    # Adjust layout
    plt.tight_layout()

    # Save figure
    plt.savefig('unified_comphyon_results.png')
    print("Results plot saved to 'unified_comphyon_results.png'")

def main():
    """Main function."""
    print("=== Unified Ψ Tensor Core with Energy-Based Comphyon Calculation Test ===")

    try:
        # Initialize Simplified Unified Ψ Tensor Core
        unified_core = SimplifiedUnifiedPsiTensorCore()

        # Test with single sample
        print("\n1. Testing with single sample...")
        csde_tensor, csfe_tensor, csme_tensor = create_sample_data()

        print(f"CSDE Tensor: {csde_tensor}")
        print(f"CSFE Tensor: {csfe_tensor}")
        print(f"CSME Tensor: {csme_tensor}")

        # Process tensors
        result = unified_core.process(csde_tensor, csfe_tensor, csme_tensor)

        print(f"E_CSDE = A₁ × D = {csde_tensor[2]:.2f} × {csde_tensor[1]:.2f} = {result['energies']['CSDE']:.4f}")
        print(f"E_CSFE = A₂ × P = {csfe_tensor[2]:.2f} × {csfe_tensor[1]:.2f} = {result['energies']['CSFE']:.4f}")
        print(f"E_CSME = T × I = {csme_tensor[0]:.2f} × {csme_tensor[1]:.2f} = {result['energies']['CSME']:.4f}")
        print(f"Initial Comphyon value: {result['comphyon']:.6f} Cph")
        print(f"Note: Gradients are zero for the first sample")

        # Test with time series data
        print("\n2. Testing with time series data...")
        time_series = create_time_series_data(num_samples=10, time_interval=1.0)

        results = []
        for i, sample in enumerate(time_series):
            result = unified_core.process(
                sample["csde_tensor"],
                sample["csfe_tensor"],
                sample["csme_tensor"]
            )

            results.append(result)

            print(f"Sample {i+1}:")
            print(f"  Energies: CSDE={result['energies']['CSDE']:.4f}, CSFE={result['energies']['CSFE']:.4f}, CSME={result['energies']['CSME']:.4f}")
            print(f"  Gradients: CSDE={result['gradients']['CSDE']:.4f}, CSFE={result['gradients']['CSFE']:.4f}, CSME={result['gradients']['CSME']:.4f}")
            print(f"  Comphyon: {result['comphyon']:.6f} Cph")

        # Plot results
        plot_results(results)

        print("\n=== Test Complete ===")

    except Exception as e:
        print(f"\nError during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
