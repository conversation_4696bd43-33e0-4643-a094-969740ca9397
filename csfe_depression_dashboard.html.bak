<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSFE Depression Prediction Dashboard</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .dashboard {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      padding: 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .header h1 {
      color: #333;
      margin-bottom: 5px;
    }
    .header p {
      color: #666;
      margin-top: 0;
    }
    .probability-gauge {
      text-align: center;
      margin-bottom: 30px;
    }
    .gauge-value {
      font-size: 48px;
      font-weight: bold;
      margin: 10px 0;
    }
    .gauge-label {
      font-size: 18px;
      color: #666;
    }
    .timeline-chart, .indicators-chart {
      height: 300px;
      margin-bottom: 30px;
    }
    .warning-level {
      text-align: center;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 30px;
      font-size: 24px;
      font-weight: bold;
    }
    .warning-green {
      background-color: #d4edda;
      color: #155724;
    }
    .warning-yellow {
      background-color: #fff3cd;
      color: #856404;
    }
    .warning-orange {
      background-color: #ffe5d0;
      color: #fd7e14;
    }
    .warning-red {
      background-color: #f8d7da;
      color: #721c24;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    .card h2 {
      margin-top: 0;
      color: #333;
      font-size: 20px;
    }
    .grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }
    .comparison-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    .comparison-table th, .comparison-table td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    .comparison-table th {
      background-color: #f2f2f2;
    }
    .footer {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      color: #666;
      font-size: 14px;
    }
    @media (max-width: 768px) {
      .grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="dashboard">
    <div class="header">
      <h1>CSFE Depression Prediction Dashboard</h1>
      <p>Unified Field Theory Application to Financial Depression Prediction (2027-2031)</p>
    </div>
    
    <div class="probability-gauge">
      <div class="gauge-label">Overall Depression Probability</div>
      <div class="gauge-value" id="probability-value">78%</div>
      <canvas id="gauge-chart" width="200" height="100"></canvas>
    </div>
    
    <div class="warning-level warning-orange" id="warning-level">
      ELEVATED RISK
    </div>
    
    <div class="card">
      <h2>Timeline Probability (2027-2031)</h2>
      <div class="timeline-chart">
        <canvas id="timeline-chart"></canvas>
      </div>
    </div>
    
    <div class="grid">
      <div class="card">
        <h2>Key Market Indicators</h2>
        <div class="indicators-chart">
          <canvas id="market-chart"></canvas>
        </div>
      </div>
      
      <div class="card">
        <h2>Key Economic Indicators</h2>
        <div class="indicators-chart">
          <canvas id="economic-chart"></canvas>
        </div>
      </div>
    </div>
    
    <div class="card">
      <h2>Key Sentiment Indicators</h2>
      <div class="indicators-chart">
        <canvas id="sentiment-chart"></canvas>
      </div>
    </div>
    
    <div class="card">
      <h2>Comparison: Current vs. Great Depression</h2>
      <table class="comparison-table">
        <thead>
          <tr>
            <th>Indicator Category</th>
            <th>Current (2023)</th>
            <th>Great Depression (1920s)</th>
            <th>Difference</th>
          </tr>
        </thead>
        <tbody id="comparison-table-body">
          <!-- Filled by JavaScript -->
        </tbody>
      </table>
    </div>
    
    <div class="card">
      <h2>Recommended Actions</h2>
      <ul id="recommended-actions">
        <!-- Filled by JavaScript -->
      </ul>
    </div>
    
    <div class="footer">
      <p>CSFE Depression Prediction Engine | Powered by the Unified Field Theory</p>
      <p>Performance improvement over traditional economic models: <span id="performance-factor">3,142x</span></p>
    </div>
  </div>

  <script>
    // Sample CSFE Depression Prediction results
    const currentResult = {
      csfeValue: 27584.32,
      depressionProbability: 0.78,
      performanceFactor: 3142,
      warningLevel: "ORANGE",
      timelineProbability: {
        years: [
          { year: 2027, probability: 0.15 },
          { year: 2028, probability: 0.25 },
          { year: 2029, probability: 0.35 },
          { year: 2030, probability: 0.18 },
          { year: 2031, probability: 0.07 }
        ],
        peakYear: 2029
      },
      keyIndicators: {
        market: [
          { name: "Yield Curve Inversion", value: 0.7, impact: 0.84, category: "market" },
          { name: "Equity Valuations", value: 0.8, impact: 0.96, category: "market" },
          { name: "Credit Spreads", value: 0.65, impact: 0.78, category: "market" },
          { name: "Market Breadth", value: 0.3, impact: 0.36, category: "market" },
          { name: "Volatility Patterns", value: 0.7, impact: 0.84, category: "market" }
        ],
        economic: [
          { name: "Debt Cycles", value: 0.85, impact: 0.935, category: "economic" },
          { name: "Monetary Policy", value: 0.7, impact: 0.77, category: "economic" },
          { name: "Fiscal Policy", value: 0.75, impact: 0.825, category: "economic" },
          { name: "Labor Market", value: 0.4, impact: 0.44, category: "economic" },
          { name: "Demographic Shifts", value: 0.65, impact: 0.715, category: "economic" }
        ],
        sentiment: [
          { name: "Investor Sentiment", value: 0.3, impact: 0.3, category: "sentiment" },
          { name: "Consumer Confidence", value: 0.35, impact: 0.35, category: "sentiment" },
          { name: "Media Sentiment", value: 0.3, impact: 0.3, category: "sentiment" },
          { name: "Corporate Behavior", value: 0.25, impact: 0.25, category: "sentiment" },
          { name: "Policy Uncertainty", value: 0.75, impact: 0.75, category: "sentiment" }
        ],
        key: [
          { name: "Debt Cycles", value: 0.85, impact: 0.935, category: "economic" },
          { name: "Equity Valuations", value: 0.8, impact: 0.96, category: "market" },
          { name: "Yield Curve Inversion", value: 0.7, impact: 0.84, category: "market" },
          { name: "Volatility Patterns", value: 0.7, impact: 0.84, category: "market" },
          { name: "Policy Uncertainty", value: 0.75, impact: 0.75, category: "sentiment" }
        ]
      },
      recommendedActions: [
        "Begin phased implementation of depression safeguards",
        "Reduce exposure to high-risk assets",
        "Prepare policy response options",
        "Stress test financial systems",
        "Develop contingency plans for critical sectors"
      ]
    };
    
    const greatDepressionResult = {
      csfeValue: 32145.67,
      depressionProbability: 0.92,
      performanceFactor: 3142,
      warningLevel: "RED",
      timelineProbability: {
        years: [
          { year: 1927, probability: 0.1 },
          { year: 1928, probability: 0.2 },
          { year: 1929, probability: 0.45 },
          { year: 1930, probability: 0.15 },
          { year: 1931, probability: 0.1 }
        ],
        peakYear: 1929
      },
      keyIndicators: {
        market: [
          { name: "Yield Curve Inversion", value: 0.8, impact: 0.96, category: "market" },
          { name: "Equity Valuations", value: 0.9, impact: 1.08, category: "market" },
          { name: "Credit Spreads", value: 0.7, impact: 0.84, category: "market" },
          { name: "Market Breadth", value: 0.25, impact: 0.3, category: "market" },
          { name: "Volatility Patterns", value: 0.8, impact: 0.96, category: "market" }
        ],
        economic: [
          { name: "Debt Cycles", value: 0.9, impact: 0.99, category: "economic" },
          { name: "Monetary Policy", value: 0.8, impact: 0.88, category: "economic" },
          { name: "Fiscal Policy", value: 0.8, impact: 0.88, category: "economic" },
          { name: "Labor Market", value: 0.3, impact: 0.33, category: "economic" },
          { name: "Demographic Shifts", value: 0.7, impact: 0.77, category: "economic" }
        ],
        sentiment: [
          { name: "Investor Sentiment", value: 0.2, impact: 0.2, category: "sentiment" },
          { name: "Consumer Confidence", value: 0.25, impact: 0.25, category: "sentiment" },
          { name: "Media Sentiment", value: 0.2, impact: 0.2, category: "sentiment" },
          { name: "Corporate Behavior", value: 0.15, impact: 0.15, category: "sentiment" },
          { name: "Policy Uncertainty", value: 0.85, impact: 0.85, category: "sentiment" }
        ],
        key: [
          { name: "Equity Valuations", value: 0.9, impact: 1.08, category: "market" },
          { name: "Debt Cycles", value: 0.9, impact: 0.99, category: "economic" },
          { name: "Policy Uncertainty", value: 0.85, impact: 0.85, category: "sentiment" },
          { name: "Yield Curve Inversion", value: 0.8, impact: 0.96, category: "market" },
          { name: "Volatility Patterns", value: 0.8, impact: 0.96, category: "market" }
        ]
      },
      recommendedActions: [
        "Implement full depression mitigation strategy",
        "Shift to defensive asset allocation",
        "Increase liquidity reserves",
        "Activate emergency policy measures",
        "Prepare for extended economic contraction"
      ]
    };

    // Update probability value
    document.getElementById('probability-value').textContent = `${Math.round(currentResult.depressionProbability * 100)}%`;
    
    // Update performance factor
    document.getElementById('performance-factor').textContent = `${currentResult.performanceFactor.toLocaleString()}x`;

    // Update warning level
    const warningLevel = document.getElementById('warning-level');
    if (currentResult.warningLevel === "RED") {
      warningLevel.className = 'warning-level warning-red';
      warningLevel.textContent = 'HIGH PROBABILITY OF DEPRESSION';
    } else if (currentResult.warningLevel === "ORANGE") {
      warningLevel.className = 'warning-level warning-orange';
      warningLevel.textContent = 'ELEVATED RISK';
    } else if (currentResult.warningLevel === "YELLOW") {
      warningLevel.className = 'warning-level warning-yellow';
      warningLevel.textContent = 'EARLY WARNING SIGNS';
    } else {
      warningLevel.className = 'warning-level warning-green';
      warningLevel.textContent = 'NORMAL CONDITIONS';
    }

    // Create gauge chart
    const gaugeCtx = document.getElementById('gauge-chart').getContext('2d');
    new Chart(gaugeCtx, {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [currentResult.depressionProbability, 1 - currentResult.depressionProbability],
          backgroundColor: [
            currentResult.warningLevel === "RED" ? '#dc3545' :
            currentResult.warningLevel === "ORANGE" ? '#fd7e14' :
            currentResult.warningLevel === "YELLOW" ? '#ffc107' : '#28a745',
            '#e9ecef'
          ],
          borderWidth: 0
        }]
      },
      options: {
        cutout: '80%',
        circumference: 180,
        rotation: 270,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: false
          }
        }
      }
    });

    // Create timeline chart
    const timelineCtx = document.getElementById('timeline-chart').getContext('2d');
    new Chart(timelineCtx, {
      type: 'bar',
      data: {
        labels: currentResult.timelineProbability.years.map(y => y.year),
        datasets: [{
          label: 'Depression Probability',
          data: currentResult.timelineProbability.years.map(y => y.probability * 100),
          backgroundColor: currentResult.timelineProbability.years.map(y => 
            y.year === currentResult.timelineProbability.peakYear ? '#fd7e14' : '#6c757d'
          ),
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Probability (%)'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Year'
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          title: {
            display: true,
            text: `Peak Probability: ${currentResult.timelineProbability.peakYear}`
          }
        }
      }
    });

    // Create market indicators chart
    const marketCtx = document.getElementById('market-chart').getContext('2d');
    new Chart(marketCtx, {
      type: 'radar',
      data: {
        labels: currentResult.keyIndicators.market.map(i => i.name),
        datasets: [{
          label: 'Current (2023)',
          data: currentResult.keyIndicators.market.map(i => i.value * 100),
          backgroundColor: 'rgba(253, 126, 20, 0.2)',
          borderColor: '#fd7e14',
          borderWidth: 2,
          pointBackgroundColor: '#fd7e14'
        }, {
          label: 'Great Depression (1920s)',
          data: greatDepressionResult.keyIndicators.market.map(i => i.value * 100),
          backgroundColor: 'rgba(220, 53, 69, 0.2)',
          borderColor: '#dc3545',
          borderWidth: 2,
          pointBackgroundColor: '#dc3545'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          r: {
            angleLines: {
              display: true
            },
            suggestedMin: 0,
            suggestedMax: 100
          }
        }
      }
    });

    // Create economic indicators chart
    const economicCtx = document.getElementById('economic-chart').getContext('2d');
    new Chart(economicCtx, {
      type: 'radar',
      data: {
        labels: currentResult.keyIndicators.economic.map(i => i.name),
        datasets: [{
          label: 'Current (2023)',
          data: currentResult.keyIndicators.economic.map(i => i.value * 100),
          backgroundColor: 'rgba(253, 126, 20, 0.2)',
          borderColor: '#fd7e14',
          borderWidth: 2,
          pointBackgroundColor: '#fd7e14'
        }, {
          label: 'Great Depression (1920s)',
          data: greatDepressionResult.keyIndicators.economic.map(i => i.value * 100),
          backgroundColor: 'rgba(220, 53, 69, 0.2)',
          borderColor: '#dc3545',
          borderWidth: 2,
          pointBackgroundColor: '#dc3545'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          r: {
            angleLines: {
              display: true
            },
            suggestedMin: 0,
            suggestedMax: 100
          }
        }
      }
    });

    // Create sentiment indicators chart
    const sentimentCtx = document.getElementById('sentiment-chart').getContext('2d');
    new Chart(sentimentCtx, {
      type: 'radar',
      data: {
        labels: currentResult.keyIndicators.sentiment.map(i => i.name),
        datasets: [{
          label: 'Current (2023)',
          data: currentResult.keyIndicators.sentiment.map(i => i.value * 100),
          backgroundColor: 'rgba(253, 126, 20, 0.2)',
          borderColor: '#fd7e14',
          borderWidth: 2,
          pointBackgroundColor: '#fd7e14'
        }, {
          label: 'Great Depression (1920s)',
          data: greatDepressionResult.keyIndicators.sentiment.map(i => i.value * 100),
          backgroundColor: 'rgba(220, 53, 69, 0.2)',
          borderColor: '#dc3545',
          borderWidth: 2,
          pointBackgroundColor: '#dc3545'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          r: {
            angleLines: {
              display: true
            },
            suggestedMin: 0,
            suggestedMax: 100
          }
        }
      }
    });
    
    // Fill comparison table
    const comparisonTableBody = document.getElementById('comparison-table-body');
    
    // Overall probability comparison
    const probabilityRow = document.createElement('tr');
    probabilityRow.innerHTML = `
      <td><strong>Depression Probability</strong></td>
      <td>${(currentResult.depressionProbability * 100).toFixed(2)}%</td>
      <td>${(greatDepressionResult.depressionProbability * 100).toFixed(2)}%</td>
      <td>${((currentResult.depressionProbability - greatDepressionResult.depressionProbability) * 100).toFixed(2)}%</td>
    `;
    comparisonTableBody.appendChild(probabilityRow);
    
    // Category comparisons
    const categories = ['market', 'economic', 'sentiment'];
    categories.forEach(category => {
      const currentAvg = currentResult.keyIndicators[category].reduce((sum, i) => sum + i.value, 0) / 
                        (currentResult.keyIndicators[category].length || 1);
      
      const greatDepressionAvg = greatDepressionResult.keyIndicators[category].reduce((sum, i) => sum + i.value, 0) / 
                                (greatDepressionResult.keyIndicators[category].length || 1);
      
      const diff = currentAvg - greatDepressionAvg;
      
      const categoryRow = document.createElement('tr');
      categoryRow.innerHTML = `
        <td><strong>${category.charAt(0).toUpperCase() + category.slice(1)} Indicators</strong></td>
        <td>${(currentAvg * 100).toFixed(2)}%</td>
        <td>${(greatDepressionAvg * 100).toFixed(2)}%</td>
        <td>${(diff * 100).toFixed(2)}%</td>
      `;
      comparisonTableBody.appendChild(categoryRow);
    });
    
    // Fill recommended actions
    const recommendedActionsList = document.getElementById('recommended-actions');
    currentResult.recommendedActions.forEach(action => {
      const li = document.createElement('li');
      li.textContent = action;
      recommendedActionsList.appendChild(li);
    });
  </script>
</body>
</html>
