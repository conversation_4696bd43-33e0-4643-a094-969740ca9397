/**
 * Advanced Resonance Detection Test
 * 
 * This script tests the advanced resonance detection capabilities of the
 * Comphyological Tensor Core.
 */

const { performance } = require('perf_hooks');
const { createComphyologicalTensorCore } = require('./src/quantum/tensor');
const { createEnhancedResonanceListener } = require('./src/quantum/resonance');

// Create Comphyological Tensor Core
console.log('Creating Comphyological Tensor Core...');
const tensorCore = createComphyologicalTensorCore({
  enableLogging: false,
  strictMode: false,
  useGPU: true,
  useDynamicWeighting: true,
  precision: 6,
  normalizationFactor: 166000
});

// Create enhanced resonance listener
console.log('Creating Enhanced Resonance Listener...');
const resonanceListener = createEnhancedResonanceListener({
  enableLogging: false,
  targetFrequency: 396, // Hz - the "OM Tone"
  precisionFFT: 0.001, // attohertz precision
  quantumVacuumNoise: true,
  crossDomainPhaseAlignment: true,
  silenceThreshold: 0.001,
  adaptiveThreshold: true,
  adaptiveRate: 0.01,
  harmonicDetection: true,
  harmonicThreshold: 0.05,
  forecastingWindow: 10,
  resonantSlopeWindow: 5,
  sampleRate: 44100,
  fftSize: 4096,
  detectionInterval: 100,
  maxHistoryLength: 100
});

// Start resonance listener
console.log('Starting Enhanced Resonance Listener...');
resonanceListener.startListening(tensorCore);

// Test data
const testCases = [
  {
    name: 'Balanced Case',
    csdeData: {
      governance: 0.5,
      dataQuality: 0.5,
      action: 'monitor',
      confidence: 0.5
    },
    csfeData: {
      risk: 0.5,
      policyCompliance: 0.5,
      action: 'monitor',
      confidence: 0.5
    },
    csmeData: {
      trustFactor: 0.5,
      integrityFactor: 0.5,
      action: 'monitor',
      confidence: 0.5
    }
  },
  {
    name: 'CSDE Dominant',
    csdeData: {
      governance: 0.9,
      dataQuality: 0.9,
      action: 'allow',
      confidence: 0.9
    },
    csfeData: {
      risk: 0.3,
      policyCompliance: 0.3,
      action: 'monitor',
      confidence: 0.3
    },
    csmeData: {
      trustFactor: 0.3,
      integrityFactor: 0.3,
      action: 'alert',
      confidence: 0.3
    }
  },
  {
    name: 'CSFE Dominant',
    csdeData: {
      governance: 0.3,
      dataQuality: 0.3,
      action: 'allow',
      confidence: 0.3
    },
    csfeData: {
      risk: 0.9,
      policyCompliance: 0.9,
      action: 'block',
      confidence: 0.9
    },
    csmeData: {
      trustFactor: 0.3,
      integrityFactor: 0.3,
      action: 'alert',
      confidence: 0.3
    }
  },
  {
    name: 'CSME Dominant',
    csdeData: {
      governance: 0.3,
      dataQuality: 0.3,
      action: 'allow',
      confidence: 0.3
    },
    csfeData: {
      risk: 0.3,
      policyCompliance: 0.3,
      action: 'monitor',
      confidence: 0.3
    },
    csmeData: {
      trustFactor: 0.9,
      integrityFactor: 0.9,
      action: 'remediate',
      confidence: 0.9
    }
  },
  {
    name: 'Conflict Case',
    csdeData: {
      governance: 0.8,
      dataQuality: 0.7,
      action: 'allow',
      confidence: 0.9
    },
    csfeData: {
      risk: 0.8,
      policyCompliance: 0.2,
      action: 'block',
      confidence: 0.8
    },
    csmeData: {
      trustFactor: 0.5,
      integrityFactor: 0.6,
      action: 'alert',
      confidence: 0.7
    }
  },
  {
    name: 'Resonance Case',
    csdeData: {
      governance: 0.618,
      dataQuality: 0.618,
      action: 'monitor',
      confidence: 0.618
    },
    csfeData: {
      risk: 0.382,
      policyCompliance: 0.618,
      action: 'monitor',
      confidence: 0.618
    },
    csmeData: {
      trustFactor: 0.618,
      integrityFactor: 0.618,
      action: 'monitor',
      confidence: 0.618
    }
  },
  {
    name: 'Perfect Resonance Case',
    csdeData: {
      governance: 0.618033988749895,
      dataQuality: 0.618033988749895,
      action: 'monitor',
      confidence: 0.618033988749895
    },
    csfeData: {
      risk: 0.381966011250105,
      policyCompliance: 0.618033988749895,
      action: 'monitor',
      confidence: 0.618033988749895
    },
    csmeData: {
      trustFactor: 0.618033988749895,
      integrityFactor: 0.618033988749895,
      action: 'monitor',
      confidence: 0.618033988749895
    }
  }
];

// Run tests
console.log('\n=== Advanced Resonance Detection Test ===');

// Process each test case
for (const testCase of testCases) {
  console.log(`\nProcessing test case: ${testCase.name}`);
  
  // Process data through tensor core
  const result = tensorCore.processData(
    testCase.csdeData,
    testCase.csfeData,
    testCase.csmeData
  );
  
  console.log(`Comphyon Value: ${result.comphyon.toFixed(6)}`);
  
  // Wait for resonance listener to process the result
  console.log('Waiting for resonance analysis...');
  await new Promise(resolve => setTimeout(resolve, 200));
  
  // Get resonance state
  const resonanceState = resonanceListener.getResonanceState();
  
  // Display resonance state
  console.log('\nResonance State:');
  console.log(`Frequency: ${resonanceState.frequency.toFixed(6)} Hz`);
  console.log(`Deviation: ${resonanceState.deviation.toFixed(6)}%`);
  console.log(`Quantum Silence: ${resonanceState.isQuantumSilence ? 'Yes' : 'No'}`);
  console.log(`Phase Alignment: ${resonanceState.phaseAlignment.toFixed(6)}`);
  console.log(`Quantum Vacuum Noise: ${resonanceState.quantumVacuumNoise.toFixed(6)}`);
  console.log(`Resonant Slope: ${resonanceState.resonantSlope.toFixed(6)}`);
  
  // Display harmonics
  const harmonics = resonanceListener.getHarmonics();
  console.log(`\nHarmonics (${harmonics.length}):`);
  
  if (harmonics.length > 0) {
    harmonics.slice(0, 3).forEach((harmonic, index) => {
      console.log(`  ${index + 1}. Frequency: ${harmonic.frequency.toFixed(2)} Hz, Ratio: ${harmonic.ratio.toFixed(2)}, Amplitude: ${harmonic.amplitude.toFixed(4)}`);
    });
    
    if (harmonics.length > 3) {
      console.log(`  ... and ${harmonics.length - 3} more`);
    }
  } else {
    console.log('  No harmonics detected');
  }
  
  // Display forecast
  const forecast = resonanceListener.getForecast();
  console.log(`\nForecast (${forecast.length} points):`);
  
  if (forecast.length > 0) {
    forecast.slice(0, 3).forEach((point, index) => {
      console.log(`  ${index + 1}. Comphyon: ${point.comphyon.toFixed(6)}, Frequency: ${point.frequency.toFixed(2)} Hz, Quantum Silence: ${point.isQuantumSilence ? 'Yes' : 'No'}`);
    });
    
    if (forecast.length > 3) {
      console.log(`  ... and ${forecast.length - 3} more`);
    }
  } else {
    console.log('  No forecast available');
  }
}

// Test harmonic detection with synthetic data
console.log('\n=== Harmonic Detection Test ===');

// Create synthetic data with known harmonics
const syntheticData = {
  csdeData: {
    governance: 0.618033988749895,
    dataQuality: 0.618033988749895,
    action: 'monitor',
    confidence: 0.618033988749895
  },
  csfeData: {
    risk: 0.381966011250105,
    policyCompliance: 0.618033988749895,
    action: 'monitor',
    confidence: 0.618033988749895
  },
  csmeData: {
    trustFactor: 0.618033988749895,
    integrityFactor: 0.618033988749895,
    action: 'monitor',
    confidence: 0.618033988749895
  }
};

// Process synthetic data multiple times to build up history
console.log('Processing synthetic data...');

for (let i = 0; i < 10; i++) {
  tensorCore.processData(
    syntheticData.csdeData,
    syntheticData.csfeData,
    syntheticData.csmeData
  );
  
  // Wait for resonance listener to process the result
  await new Promise(resolve => setTimeout(resolve, 100));
}

// Get resonance state
const finalResonanceState = resonanceListener.getResonanceState();

// Display final resonance state
console.log('\nFinal Resonance State:');
console.log(`Frequency: ${finalResonanceState.frequency.toFixed(6)} Hz`);
console.log(`Deviation: ${finalResonanceState.deviation.toFixed(6)}%`);
console.log(`Quantum Silence: ${finalResonanceState.isQuantumSilence ? 'Yes' : 'No'}`);
console.log(`Phase Alignment: ${finalResonanceState.phaseAlignment.toFixed(6)}`);
console.log(`Quantum Vacuum Noise: ${finalResonanceState.quantumVacuumNoise.toFixed(6)}`);
console.log(`Resonant Slope: ${finalResonanceState.resonantSlope.toFixed(6)}`);

// Display harmonics
const finalHarmonics = resonanceListener.getHarmonics();
console.log(`\nHarmonics (${finalHarmonics.length}):`);

if (finalHarmonics.length > 0) {
  finalHarmonics.forEach((harmonic, index) => {
    console.log(`  ${index + 1}. Frequency: ${harmonic.frequency.toFixed(2)} Hz, Ratio: ${harmonic.ratio.toFixed(2)}, Amplitude: ${harmonic.amplitude.toFixed(4)}`);
  });
} else {
  console.log('  No harmonics detected');
}

// Display forecast
const finalForecast = resonanceListener.getForecast();
console.log(`\nForecast (${finalForecast.length} points):`);

if (finalForecast.length > 0) {
  finalForecast.forEach((point, index) => {
    console.log(`  ${index + 1}. Comphyon: ${point.comphyon.toFixed(6)}, Frequency: ${point.frequency.toFixed(2)} Hz, Quantum Silence: ${point.isQuantumSilence ? 'Yes' : 'No'}`);
  });
} else {
  console.log('  No forecast available');
}

// Stop resonance listener
console.log('\nStopping Enhanced Resonance Listener...');
resonanceListener.stopListening();

console.log('\n=== Test Complete ===');
