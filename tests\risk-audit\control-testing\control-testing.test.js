const request = require('supertest');
const app = require('../../../server');

describe('Control Testing API', () => {
  // Test GET /risk-audit/control-testing/controls
  describe('GET /risk-audit/control-testing/controls', () => {
    it('should return a list of controls', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/controls')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
    
    it('should filter controls by type', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/controls?type=preventive')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(control => control.type === 'preventive')).toBe(true);
    });
    
    it('should filter controls by category', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/controls?category=technical')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(control => control.category === 'technical')).toBe(true);
    });
    
    it('should paginate results', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/controls?page=1&limit=2')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeLessThanOrEqual(2);
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(2);
    });
  });
  
  // Test GET /risk-audit/control-testing/controls/:id
  describe('GET /risk-audit/control-testing/controls/:id', () => {
    it('should return a specific control', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/controls/ctrl-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('ctrl-001');
    });
    
    it('should return 404 for non-existent control', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/controls/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test POST /risk-audit/control-testing/controls
  describe('POST /risk-audit/control-testing/controls', () => {
    it('should create a new control', async () => {
      const newControl = {
        title: 'Test Control',
        description: 'This is a test control',
        type: 'preventive',
        category: 'technical',
        status: 'draft',
        owner: 'Test Team',
        framework: 'NIST 800-53',
        riskLevel: 'medium',
        testFrequency: 'quarterly',
        testProcedure: 'Test procedure'
      };
      
      const response = await request(app)
        .post('/risk-audit/control-testing/controls')
        .set('apikey', 'test-api-key')
        .send(newControl);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.title).toBe(newControl.title);
      expect(response.body.data.type).toBe(newControl.type);
      expect(response.body.data).toHaveProperty('id');
    });
    
    it('should return 400 for invalid input', async () => {
      const invalidControl = {
        // Missing required fields
        description: 'This is an invalid control'
      };
      
      const response = await request(app)
        .post('/risk-audit/control-testing/controls')
        .set('apikey', 'test-api-key')
        .send(invalidControl);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test PUT /risk-audit/control-testing/controls/:id
  describe('PUT /risk-audit/control-testing/controls/:id', () => {
    it('should update an existing control', async () => {
      const updateData = {
        title: 'Updated Control',
        status: 'under-review'
      };
      
      const response = await request(app)
        .put('/risk-audit/control-testing/controls/ctrl-001')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.title).toBe(updateData.title);
      expect(response.body.data.status).toBe(updateData.status);
    });
    
    it('should return 404 for non-existent control', async () => {
      const updateData = {
        title: 'Updated Control'
      };
      
      const response = await request(app)
        .put('/risk-audit/control-testing/controls/non-existent-id')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test DELETE /risk-audit/control-testing/controls/:id
  describe('DELETE /risk-audit/control-testing/controls/:id', () => {
    it('should delete an existing control', async () => {
      const response = await request(app)
        .delete('/risk-audit/control-testing/controls/ctrl-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 404 for non-existent control', async () => {
      const response = await request(app)
        .delete('/risk-audit/control-testing/controls/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /risk-audit/control-testing/controls/:id/test-results
  describe('GET /risk-audit/control-testing/controls/:id/test-results', () => {
    it('should return test results for a specific control', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/controls/ctrl-002/test-results')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
    
    it('should return 404 for non-existent control', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/controls/non-existent-id/test-results')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test POST /risk-audit/control-testing/controls/:id/test-results
  describe('POST /risk-audit/control-testing/controls/:id/test-results', () => {
    it('should add a test result to a control', async () => {
      const newTestResult = {
        testDate: '2025-01-15',
        tester: 'Test User',
        result: 'pass',
        evidence: 'Test evidence',
        notes: 'Test notes'
      };
      
      const response = await request(app)
        .post('/risk-audit/control-testing/controls/ctrl-002/test-results')
        .set('apikey', 'test-api-key')
        .send(newTestResult);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.testDate).toBe(newTestResult.testDate);
      expect(response.body.data.result).toBe(newTestResult.result);
      expect(response.body.data).toHaveProperty('id');
    });
    
    it('should return 400 for invalid input', async () => {
      const invalidTestResult = {
        // Missing required fields
        notes: 'Test notes'
      };
      
      const response = await request(app)
        .post('/risk-audit/control-testing/controls/ctrl-002/test-results')
        .set('apikey', 'test-api-key')
        .send(invalidTestResult);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
    
    it('should return 404 for non-existent control', async () => {
      const newTestResult = {
        testDate: '2025-01-15',
        tester: 'Test User',
        result: 'pass',
        evidence: 'Test evidence',
        notes: 'Test notes'
      };
      
      const response = await request(app)
        .post('/risk-audit/control-testing/controls/non-existent-id/test-results')
        .set('apikey', 'test-api-key')
        .send(newTestResult);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /risk-audit/control-testing/test-results
  describe('GET /risk-audit/control-testing/test-results', () => {
    it('should return a list of test results', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/test-results')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
    
    it('should filter test results by result', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/test-results?result=pass')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(testResult => testResult.result === 'pass')).toBe(true);
    });
    
    it('should filter test results by controlId', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/test-results?controlId=ctrl-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body.data.every(testResult => testResult.controlId === 'ctrl-001')).toBe(true);
    });
  });
  
  // Test GET /risk-audit/control-testing/test-results/:id
  describe('GET /risk-audit/control-testing/test-results/:id', () => {
    it('should return a specific test result', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/test-results/test-001')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('test-001');
    });
    
    it('should return 404 for non-existent test result', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/test-results/non-existent-id')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test PUT /risk-audit/control-testing/test-results/:id
  describe('PUT /risk-audit/control-testing/test-results/:id', () => {
    it('should update an existing test result', async () => {
      const updateData = {
        result: 'fail',
        notes: 'Updated notes',
        remediation: 'Remediation steps',
        remediationStatus: 'pending'
      };
      
      const response = await request(app)
        .put('/risk-audit/control-testing/test-results/test-001')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message');
      expect(response.body.data.result).toBe(updateData.result);
      expect(response.body.data.notes).toBe(updateData.notes);
      expect(response.body.data.remediation).toBe(updateData.remediation);
      expect(response.body.data.remediationStatus).toBe(updateData.remediationStatus);
    });
    
    it('should return 404 for non-existent test result', async () => {
      const updateData = {
        result: 'fail'
      };
      
      const response = await request(app)
        .put('/risk-audit/control-testing/test-results/non-existent-id')
        .set('apikey', 'test-api-key')
        .send(updateData);
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('message');
    });
  });
  
  // Test GET /risk-audit/control-testing/types
  describe('GET /risk-audit/control-testing/types', () => {
    it('should return a list of control types', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/types')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0]).toHaveProperty('id');
      expect(response.body.data[0]).toHaveProperty('name');
      expect(response.body.data[0]).toHaveProperty('description');
    });
  });
  
  // Test GET /risk-audit/control-testing/categories
  describe('GET /risk-audit/control-testing/categories', () => {
    it('should return a list of control categories', async () => {
      const response = await request(app)
        .get('/risk-audit/control-testing/categories')
        .set('apikey', 'test-api-key');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0]).toHaveProperty('id');
      expect(response.body.data[0]).toHaveProperty('name');
      expect(response.body.data[0]).toHaveProperty('description');
    });
  });
});
