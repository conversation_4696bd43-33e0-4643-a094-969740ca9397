<!DOCTYPE html>
<html>
<head>
    <title>NovaFuse Patent Diagrams - Simplified</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            background-color: white;
        }
        .diagram-container {
            margin-bottom: 60px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: white;
        }
        h1 {
            color: #333;
        }
        h2 {
            color: #555;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <h1>NovaFuse Patent Diagrams - Simplified Black & White</h1>
    
    <h2>Core Architecture (Fig. 1)</h2>
    <div class="diagram-container">
        <div class="mermaid">
            graph TD
                A[Source Component<br>Input Conditioning] -->|18% Resources| B[Validation Core<br>Pattern Verification]
                B -->|82% Resources| C[Integration Engine<br>Output Synthesis]
                style A stroke:#000,stroke-width:2px
                style B stroke:#000,stroke-width:2px
                style C stroke:#000,stroke-width:2px
                linkStyle 0 stroke:#000,stroke-width:1.5px,stroke-dasharray:3
                linkStyle 1 stroke:#000,stroke-width:1.5px
        </div>
    </div>

    <h2>Tensor Fusion (Fig. 2)</h2>
    <div class="diagram-container">
        <div class="mermaid">
            graph LR
                A[Domain A] -->|0.618| F[⊕]
                B[Domain B] -->|0.382| F
                F --> C[⊗]
                C --> D[×π10³]
                style A,B stroke:#000
                style F,C,D stroke:#000,stroke-width:2px
                linkStyle 0,1 stroke:#000,stroke-dasharray:5
                linkStyle 2,3 stroke:#000
        </div>
    </div>

    <h2>Marketplace Flow (Fig. 3)</h2>
    <div class="diagram-container">
        <div class="mermaid">
            pie showData
                title Revenue Distribution
                "Platform (18%)" : 18
                "Developers (82%)" : 82
        </div>
    </div>

    <h2>AI Constraint Model (Fig. 4)</h2>
    <div class="diagram-container">
        <div class="mermaid">
            stateDiagram-v2
                [*] --> Source: Input Gate
                Source --> Validation: Hard Limit
                Validation --> Integration: Attestation
                Integration --> [*]
                note right of Validation: 18% Compute Budget
        </div>
    </div>

    <h2>Cross-Domain Translation (Fig. 5)</h2>
    <div class="diagram-container">
        <div class="mermaid">
            flowchart TB
                subgraph Input Domains
                    A[Financial]
                    B[Healthcare]
                end
                subgraph Output Domains
                    C[Energy]
                    D[Manufacturing]
                end
                A & B -->|Encode| U[Universal Tensor]
                U -->|Decode| C & D
                style U stroke:#000,stroke-width:2px
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'neutral',
            securityLevel: 'loose',
            themeVariables: {
                primaryColor: '#ffffff',
                primaryTextColor: '#000000',
                primaryBorderColor: '#000000',
                lineColor: '#000000',
                secondaryColor: '#ffffff',
                tertiaryColor: '#ffffff'
            }
        });
    </script>
</body>
</html>

