/**
 * Apply Feature Flags Script
 * 
 * This script scans the components directory and applies feature flags to components
 * that don't already have them.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Configuration
const componentsDir = path.join(__dirname, '..', 'components');
const featureFlagImport = "import { useFeatureFlag } from '@nova-ui/feature-flags';";
const featureFlaggedComponentImport = "import { FeatureFlaggedComponent, UpgradePrompt } from '@nova-ui/feature-flags';";

// Feature flag mapping - maps component names to feature flags
const featureFlagMapping = {
  'AnalyticsPanel': { category: 'dashboard', feature: 'analytics' },
  'ReportsPanel': { category: 'dashboard', feature: 'reports' },
  'PrivacyPanel': { category: 'grc', feature: 'privacy' },
  'SecurityPanel': { category: 'grc', feature: 'security' },
  'CompliancePanel': { category: 'grc', feature: 'compliance' },
  'ControlPanel': { category: 'grc', feature: 'control' },
  'ESGPanel': { category: 'grc', feature: 'esg' },
  'AIAssistantPanel': { category: 'advanced', feature: 'aiAssistant' },
  'PredictiveAnalyticsPanel': { category: 'advanced', feature: 'predictiveAnalytics' },
  'AutomatedRemediationPanel': { category: 'advanced', feature: 'automatedRemediation' },
  'CustomIntegrationsPanel': { category: 'advanced', feature: 'customIntegrations' },
  'UserManagementPanel': { category: 'administration', feature: 'userManagement' },
  'RoleManagementPanel': { category: 'administration', feature: 'roleManagement' },
  'OrganizationSettingsPanel': { category: 'administration', feature: 'organizationSettings' },
  'AuditLogsPanel': { category: 'administration', feature: 'auditLogs' },
  'GamificationPanel': { category: 'learning', feature: 'gamification' },
  'TrainingModulesPanel': { category: 'learning', feature: 'trainingModules' },
  'CertificationsPanel': { category: 'learning', feature: 'certifications' },
  'KnowledgeBasePanel': { category: 'learning', feature: 'knowledgeBase' }
};

// Function to check if a file already has feature flags
function hasFeatureFlags(content) {
  return content.includes('useFeatureFlag') || 
         content.includes('FeatureFlaggedComponent') || 
         content.includes('useFeatureFlagAdvanced');
}

// Function to apply feature flags to a component
function applyFeatureFlags(filePath, content) {
  // Get the component name from the file name
  const componentName = path.basename(filePath, path.extname(filePath));
  
  // Check if we have a mapping for this component
  if (!featureFlagMapping[componentName]) {
    console.log(`No feature flag mapping found for ${componentName}`);
    return content;
  }
  
  // Get the feature flag mapping
  const { category, feature } = featureFlagMapping[componentName];
  
  // Check if the component already has feature flags
  if (hasFeatureFlags(content)) {
    console.log(`${componentName} already has feature flags`);
    return content;
  }
  
  console.log(`Applying feature flags to ${componentName}`);
  
  // Add the import statement
  let newContent = content;
  
  // Check if the file already has the import
  if (!newContent.includes(featureFlagImport)) {
    // Find the last import statement
    const lastImportIndex = newContent.lastIndexOf('import');
    const lastImportEndIndex = newContent.indexOf('\n', lastImportIndex);
    
    // Insert the import after the last import
    newContent = newContent.slice(0, lastImportEndIndex + 1) + 
                featureFlagImport + '\n' + 
                newContent.slice(lastImportEndIndex + 1);
  }
  
  // Find the component function
  const functionRegex = new RegExp(`function\\s+${componentName}\\s*\\([^)]*\\)\\s*{`);
  const functionMatch = functionRegex.exec(newContent);
  
  if (!functionMatch) {
    console.log(`Could not find function declaration for ${componentName}`);
    return newContent;
  }
  
  // Find the function body start
  const functionBodyStartIndex = newContent.indexOf('{', functionMatch.index) + 1;
  
  // Add the feature flag check
  const featureFlagCheck = `
  const is${feature.charAt(0).toUpperCase() + feature.slice(1)}Enabled = useFeatureFlag('${category}', '${feature}');
  
  if (!is${feature.charAt(0).toUpperCase() + feature.slice(1)}Enabled) {
    return <UpgradePrompt featureName="${componentName.replace(/Panel$/, '')}" />;
  }
`;
  
  // Insert the feature flag check at the start of the function body
  newContent = newContent.slice(0, functionBodyStartIndex) + 
              featureFlagCheck + 
              newContent.slice(functionBodyStartIndex);
  
  return newContent;
}

// Function to apply feature flags to a page component
function applyFeatureFlagsToPage(filePath, content) {
  // Get the page name from the file name
  const pageName = path.basename(filePath, path.extname(filePath));
  
  // Check if the page already has feature flags
  if (hasFeatureFlags(content)) {
    console.log(`${pageName} already has feature flags`);
    return content;
  }
  
  console.log(`Applying feature flags to page ${pageName}`);
  
  // Add the import statement
  let newContent = content;
  
  // Check if the file already has the import
  if (!newContent.includes(featureFlaggedComponentImport)) {
    // Find the last import statement
    const lastImportIndex = newContent.lastIndexOf('import');
    const lastImportEndIndex = newContent.indexOf('\n', lastImportIndex);
    
    // Insert the import after the last import
    newContent = newContent.slice(0, lastImportEndIndex + 1) + 
                featureFlaggedComponentImport + '\n' + 
                newContent.slice(lastImportEndIndex + 1);
  }
  
  // Find component usages in the page
  for (const componentName in featureFlagMapping) {
    // Skip if the component is not used in the page
    if (!newContent.includes(`<${componentName}`)) {
      continue;
    }
    
    const { category, feature } = featureFlagMapping[componentName];
    
    // Replace the component usage with a feature-flagged version
    const componentRegex = new RegExp(`<${componentName}([^>]*)>`, 'g');
    newContent = newContent.replace(componentRegex, (match, props) => {
      return `<FeatureFlaggedComponent
        category="${category}"
        feature="${feature}"
        fallback={<UpgradePrompt featureName="${componentName.replace(/Panel$/, '')}" />}
      >
        <${componentName}${props}>`;
    });
    
    // Replace the closing tag
    const closingTagRegex = new RegExp(`</${componentName}>`, 'g');
    newContent = newContent.replace(closingTagRegex, `</${componentName}>
      </FeatureFlaggedComponent>`);
  }
  
  return newContent;
}

// Main function
function main() {
  // Create the scripts directory if it doesn't exist
  if (!fs.existsSync(path.join(__dirname, '..'))) {
    fs.mkdirSync(path.join(__dirname, '..'), { recursive: true });
  }
  
  // Find all component files
  const componentFiles = glob.sync(path.join(componentsDir, '**/*.js'));
  
  // Apply feature flags to each component
  componentFiles.forEach(filePath => {
    const content = fs.readFileSync(filePath, 'utf8');
    const newContent = applyFeatureFlags(filePath, content);
    
    // Write the new content back to the file
    if (content !== newContent) {
      fs.writeFileSync(filePath, newContent);
      console.log(`Updated ${filePath}`);
    }
  });
  
  // Find all page files
  const pageFiles = glob.sync(path.join(__dirname, '..', 'pages', '**/*.js'));
  
  // Apply feature flags to each page
  pageFiles.forEach(filePath => {
    const content = fs.readFileSync(filePath, 'utf8');
    const newContent = applyFeatureFlagsToPage(filePath, content);
    
    // Write the new content back to the file
    if (content !== newContent) {
      fs.writeFileSync(filePath, newContent);
      console.log(`Updated ${filePath}`);
    }
  });
  
  console.log('Feature flags applied successfully!');
}

// Run the main function
main();
