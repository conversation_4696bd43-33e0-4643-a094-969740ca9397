"""
Universal Compliance Intelligence Architecture (UCIA).

A modular compliance intelligence framework with cross-regulatory knowledge distillation.
"""

from .ucia import UCIA
from .core_engine import CoreComplianceEngine
from .framework_modules import (
    ComplianceFrameworkModule,
    GDPRModule,
    HIPAAModule,
    SOC2Module,
    ModuleRegistry,
    initialize_default_modules
)
from .knowledge_distillation import (
    CrossRegulatoryKnowledgeDistillation,
    ComplianceOntology
)
from .model_integration import ComplianceModelIntegration

__version__ = '0.1.0'
__all__ = [
    'UCIA',
    'CoreComplianceEngine',
    'ComplianceFrameworkModule',
    'GDPRModule',
    'HIPAAModule',
    'SOC2Module',
    'ModuleRegistry',
    'initialize_default_modules',
    'CrossRegulatoryKnowledgeDistillation',
    'ComplianceOntology',
    'ComplianceModelIntegration'
]
