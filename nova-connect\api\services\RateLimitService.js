/**
 * Rate Limit Service
 * 
 * This service handles API rate limiting.
 */

const fs = require('fs').promises;
const path = require('path');
const { RateLimitError } = require('../utils/errors');

class RateLimitService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.rateLimitsFile = path.join(this.dataDir, 'rate_limits.json');
    this.defaultRateLimit = {
      anonymous: {
        requests: 60,
        period: 60 // 60 requests per minute
      },
      authenticated: {
        requests: 300,
        period: 60 // 300 requests per minute
      },
      apiKey: {
        requests: 600,
        period: 60 // 600 requests per minute
      }
    };
    this.requestCounts = new Map();
    this.ensureDataDir();
    
    // Clean up expired rate limits every minute
    setInterval(() => this.cleanupExpiredRateLimits(), 60000);
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Load rate limits from file
   */
  async loadRateLimits() {
    try {
      const data = await fs.readFile(this.rateLimitsFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return default rate limits
        return this.defaultRateLimit;
      }
      console.error('Error loading rate limits:', error);
      throw error;
    }
  }

  /**
   * Save rate limits to file
   */
  async saveRateLimits(rateLimits) {
    try {
      await fs.writeFile(this.rateLimitsFile, JSON.stringify(rateLimits, null, 2));
    } catch (error) {
      console.error('Error saving rate limits:', error);
      throw error;
    }
  }

  /**
   * Check rate limit
   */
  async checkRateLimit(identifier, type = 'anonymous') {
    // Load rate limits
    const rateLimits = await this.loadRateLimits();
    
    // Get rate limit for the specified type
    const rateLimit = rateLimits[type] || this.defaultRateLimit[type];
    
    if (!rateLimit) {
      // No rate limit defined, allow the request
      return true;
    }
    
    const now = Date.now();
    const key = `${identifier}:${type}`;
    
    // Get current request count
    let requestCount = this.requestCounts.get(key);
    
    if (!requestCount || now - requestCount.timestamp > rateLimit.period * 1000) {
      // First request or period expired, reset counter
      requestCount = {
        count: 1,
        timestamp: now
      };
    } else {
      // Increment request count
      requestCount.count++;
    }
    
    // Save request count
    this.requestCounts.set(key, requestCount);
    
    // Check if rate limit exceeded
    if (requestCount.count > rateLimit.requests) {
      // Calculate retry after time
      const retryAfter = Math.ceil((requestCount.timestamp + rateLimit.period * 1000 - now) / 1000);
      
      throw new RateLimitError(
        `Rate limit exceeded. Try again in ${retryAfter} seconds.`,
        retryAfter
      );
    }
    
    return {
      limit: rateLimit.requests,
      remaining: rateLimit.requests - requestCount.count,
      reset: Math.ceil((requestCount.timestamp + rateLimit.period * 1000) / 1000)
    };
  }

  /**
   * Clean up expired rate limits
   */
  cleanupExpiredRateLimits() {
    const now = Date.now();
    
    for (const [key, requestCount] of this.requestCounts.entries()) {
      const [, type] = key.split(':');
      const rateLimit = this.defaultRateLimit[type];
      
      if (rateLimit && now - requestCount.timestamp > rateLimit.period * 1000) {
        this.requestCounts.delete(key);
      }
    }
  }

  /**
   * Update rate limits
   */
  async updateRateLimits(rateLimits) {
    await this.saveRateLimits(rateLimits);
    return rateLimits;
  }

  /**
   * Get rate limits
   */
  async getRateLimits() {
    return this.loadRateLimits();
  }
}

module.exports = RateLimitService;
