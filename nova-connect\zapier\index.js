/**
 * NovaConnect UAC Zapier Integration
 * 
 * This is the entry point for the Zapier integration.
 */

const authentication = require('./authentication');
const newConnectorTrigger = require('./triggers/new_connector');
const newWorkflowTrigger = require('./triggers/new_workflow');
const complianceEventTrigger = require('./triggers/compliance_event');
const createConnectorAction = require('./actions/create_connector');
const executeWorkflowAction = require('./actions/execute_workflow');
const createEvidenceAction = require('./actions/create_evidence');

// Define the Zapier app
const App = {
  // App version
  version: '1.0.0',
  
  // App title and description
  title: 'NovaConnect UAC',
  description: 'Connect NovaConnect UAC with 5,000+ apps on Zapier.',
  
  // Platform version
  platformVersion: require('zapier-platform-core').version,
  
  // Authentication
  authentication,
  
  // Before app hook
  beforeApp: {
    url: '{{process.env.API_BASE_URL}}/api/zapier/before-app'
  },
  
  // After app hook
  afterApp: {
    url: '{{process.env.API_BASE_URL}}/api/zapier/after-app'
  },
  
  // Triggers
  triggers: {
    [newConnectorTrigger.key]: newConnectorTrigger,
    [newWorkflowTrigger.key]: newWorkflowTrigger,
    [complianceEventTrigger.key]: complianceEventTrigger
  },
  
  // Actions
  creates: {
    [createConnectorAction.key]: createConnectorAction,
    [executeWorkflowAction.key]: executeWorkflowAction,
    [createEvidenceAction.key]: createEvidenceAction
  },
  
  // Resources
  resources: {},
  
  // Searches
  searches: {}
};

// Export the app
module.exports = App;
