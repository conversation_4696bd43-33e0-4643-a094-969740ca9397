/**
 * Rate Limit Service Tests
 */

const RateLimitService = require('../../../api/services/RateLimitService');
const fs = require('fs').promises;
const path = require('path');

// Mock fs.promises
jest.mock('fs', () => ({
  promises: {
    mkdir: jest.fn().mockResolvedValue(undefined),
    readFile: jest.fn(),
    writeFile: jest.fn().mockResolvedValue(undefined)
  }
}));

describe('RateLimitService', () => {
  let rateLimitService;
  const testDataDir = path.join(__dirname, 'test-data');
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create a new instance for each test
    rateLimitService = new RateLimitService(testDataDir);
  });
  
  describe('constructor', () => {
    it('should initialize with the correct data directory', () => {
      expect(rateLimitService.dataDir).toBe(testDataDir);
      expect(rateLimitService.configFile).toBe(path.join(testDataDir, 'rate_limit_config.json'));
    });
    
    it('should call ensureDataDir', () => {
      expect(fs.mkdir).toHaveBeenCalledWith(testDataDir, { recursive: true });
    });
  });
  
  describe('loadConfig', () => {
    it('should load configuration from file', async () => {
      const mockConfig = {
        global: {
          enabled: true,
          windowMs: 60000,
          max: 100
        }
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockConfig));
      
      const config = await rateLimitService.loadConfig();
      
      expect(fs.readFile).toHaveBeenCalledWith(rateLimitService.configFile, 'utf8');
      expect(config).toEqual(mockConfig);
    });
    
    it('should return default config if file does not exist', async () => {
      const error = new Error('File not found');
      error.code = 'ENOENT';
      fs.readFile.mockRejectedValueOnce(error);
      
      const config = await rateLimitService.loadConfig();
      
      expect(config).toEqual(rateLimitService.defaultConfig);
    });
    
    it('should throw error if file read fails for other reasons', async () => {
      const error = new Error('Permission denied');
      fs.readFile.mockRejectedValueOnce(error);
      
      await expect(rateLimitService.loadConfig()).rejects.toThrow('Permission denied');
    });
  });
  
  describe('saveConfig', () => {
    it('should save configuration to file', async () => {
      const config = {
        global: {
          enabled: true,
          windowMs: 60000,
          max: 100
        }
      };
      
      await rateLimitService.saveConfig(config);
      
      expect(fs.writeFile).toHaveBeenCalledWith(
        rateLimitService.configFile,
        JSON.stringify(config, null, 2)
      );
    });
    
    it('should throw error if file write fails', async () => {
      const error = new Error('Permission denied');
      fs.writeFile.mockRejectedValueOnce(error);
      
      await expect(rateLimitService.saveConfig({})).rejects.toThrow('Permission denied');
    });
  });
  
  describe('createLimiter', () => {
    it('should create a rate limiter with the correct configuration', async () => {
      const mockConfig = {
        test: {
          enabled: true,
          windowMs: 60000,
          max: 100,
          standardHeaders: true,
          legacyHeaders: false,
          message: 'Too many requests'
        }
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockConfig));
      
      const limiter = await rateLimitService.createLimiter('test');
      
      // Since we can't directly test the express-rate-limit instance,
      // we'll just verify that a function was returned
      expect(typeof limiter).toBe('function');
    });
    
    it('should return a pass-through middleware if rate limiting is disabled', async () => {
      const mockConfig = {
        test: {
          enabled: false
        }
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockConfig));
      
      const limiter = await rateLimitService.createLimiter('test');
      
      // Test that it's a pass-through middleware
      const req = {};
      const res = {};
      const next = jest.fn();
      
      limiter(req, res, next);
      
      expect(next).toHaveBeenCalled();
    });
  });
  
  describe('updateConfig', () => {
    it('should update the configuration for a specific type', async () => {
      const initialConfig = {
        test: {
          enabled: true,
          windowMs: 60000,
          max: 100
        }
      };
      
      const updatedConfig = {
        enabled: false,
        max: 50
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(initialConfig));
      
      const result = await rateLimitService.updateConfig('test', updatedConfig);
      
      expect(fs.writeFile).toHaveBeenCalled();
      expect(result).toEqual({
        enabled: false,
        windowMs: 60000,
        max: 50
      });
    });
    
    it('should throw an error if the type does not exist', async () => {
      const initialConfig = {};
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(initialConfig));
      
      await expect(rateLimitService.updateConfig('nonexistent', {}))
        .rejects.toThrow('Rate limit type');
    });
  });
});
