x-google-marketplace:
  schemaVersion: v2
  applicationApiVersion: v1beta1
  publishedVersion: '1.0.0'
  publishedVersionMetadata:
    releaseNote: >-
      Initial release of NovaConnect UAC.
    releaseTypes:
      - Feature
    recommended: true
  managedUpdates:
    kalmSupported: true
  images:
    '':
      properties:
        imageNovaFuseUAC:
          type: FULL
  clusterConstraints:
    k8sVersion: '>= 1.19.0'
    resources:
      - replicas: 3
        requests:
          cpu: 500m
          memory: 512Mi
        limits:
          cpu: 1000m
          memory: 1Gi

properties:
  name:
    type: string
    x-google-marketplace:
      type: NAME
  namespace:
    type: string
    x-google-marketplace:
      type: NAMESPACE
  mongodb.uri:
    type: string
    title: MongoDB URI
    description: URI for connecting to MongoDB
    default: mongodb://mongodb:27017/novafuse
  redis.uri:
    type: string
    title: Redis URI
    description: URI for connecting to Redis
    default: redis://redis:6379
  apiKey:
    type: string
    title: API Key
    description: API key for accessing the NovaConnect UAC API
    x-google-marketplace:
      type: GENERATED_PASSWORD
      generatedPassword:
        length: 32
        base64: false
  jwtSecret:
    type: string
    title: JWT Secret
    description: Secret for signing JWT tokens
    x-google-marketplace:
      type: GENERATED_PASSWORD
      generatedPassword:
        length: 32
        base64: false
  clusterEnabled:
    type: boolean
    title: Enable Cluster Mode
    description: Enable cluster mode for better performance
    default: true
  monitoringEnabled:
    type: boolean
    title: Enable Monitoring
    description: Enable monitoring for better observability
    default: true
  tracingEnabled:
    type: boolean
    title: Enable Tracing
    description: Enable tracing for better observability
    default: true
  corsOrigin:
    type: string
    title: CORS Origin
    description: Allowed origins for CORS
    default: "*"
  rateLimitWindowMs:
    type: integer
    title: Rate Limit Window
    description: Rate limit window in milliseconds
    default: 60000
  rateLimitMax:
    type: integer
    title: Rate Limit Max
    description: Maximum number of requests in the rate limit window
    default: 100
  dbOptimizationEnabled:
    type: boolean
    title: Enable Database Optimization
    description: Enable database optimization for better performance
    default: true
  dbOptimizationInterval:
    type: integer
    title: Database Optimization Interval
    description: Database optimization interval in milliseconds
    default: 86400000

required:
  - name
  - namespace
  - mongodb.uri
  - redis.uri
  - apiKey
  - jwtSecret

form:
  - widget: help
    description: NovaConnect UAC is a powerful and flexible API integration platform that enables you to connect to any API, normalize data, and integrate with your existing systems.
  - widget: section
    title: Database Configuration
    description: Configure the database connection
    fields:
      - widget: text
        name: mongodb.uri
        title: MongoDB URI
        description: URI for connecting to MongoDB
        default: mongodb://mongodb:27017/novafuse
      - widget: text
        name: redis.uri
        title: Redis URI
        description: URI for connecting to Redis
        default: redis://redis:6379
  - widget: section
    title: Security Configuration
    description: Configure security settings
    fields:
      - widget: password
        name: apiKey
        title: API Key
        description: API key for accessing the NovaConnect UAC API
      - widget: password
        name: jwtSecret
        title: JWT Secret
        description: Secret for signing JWT tokens
      - widget: text
        name: corsOrigin
        title: CORS Origin
        description: Allowed origins for CORS
        default: "*"
  - widget: section
    title: Performance Configuration
    description: Configure performance settings
    fields:
      - widget: checkbox
        name: clusterEnabled
        title: Enable Cluster Mode
        description: Enable cluster mode for better performance
        default: true
      - widget: checkbox
        name: dbOptimizationEnabled
        title: Enable Database Optimization
        description: Enable database optimization for better performance
        default: true
      - widget: text
        name: dbOptimizationInterval
        title: Database Optimization Interval
        description: Database optimization interval in milliseconds
        default: 86400000
  - widget: section
    title: Monitoring Configuration
    description: Configure monitoring settings
    fields:
      - widget: checkbox
        name: monitoringEnabled
        title: Enable Monitoring
        description: Enable monitoring for better observability
        default: true
      - widget: checkbox
        name: tracingEnabled
        title: Enable Tracing
        description: Enable tracing for better observability
        default: true
  - widget: section
    title: Rate Limiting Configuration
    description: Configure rate limiting settings
    fields:
      - widget: text
        name: rateLimitWindowMs
        title: Rate Limit Window
        description: Rate limit window in milliseconds
        default: 60000
      - widget: text
        name: rateLimitMax
        title: Rate Limit Max
        description: Maximum number of requests in the rate limit window
        default: 100
