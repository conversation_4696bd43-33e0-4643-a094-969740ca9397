/**
 * Testing Controller
 * 
 * This controller handles API requests related to testing connectors.
 */

const TestingService = require('../services/TestingService');
const { ValidationError, NotFoundError } = require('../utils/errors');

class TestingController {
  constructor() {
    this.testingService = new TestingService();
  }

  /**
   * Execute a connector endpoint
   */
  async executeEndpoint(req, res, next) {
    try {
      const { connectorId, endpointId, credentialId, parameters, headers } = req.body;
      
      if (!connectorId) {
        throw new ValidationError('Connector ID is required');
      }
      
      if (!endpointId) {
        throw new ValidationError('Endpoint ID is required');
      }
      
      if (!credentialId) {
        throw new ValidationError('Credential ID is required');
      }
      
      const result = await this.testingService.executeEndpoint(
        connectorId,
        endpointId,
        credentialId,
        parameters || {},
        headers || {}
      );
      
      res.json(result);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }

  /**
   * Get request history
   */
  async getRequestHistory(req, res, next) {
    try {
      const { connectorId, endpointId } = req.params;
      
      if (!connectorId) {
        throw new ValidationError('Connector ID is required');
      }
      
      if (!endpointId) {
        throw new ValidationError('Endpoint ID is required');
      }
      
      const history = this.testingService.getRequestHistory(connectorId, endpointId);
      res.json(history);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Validate response against rules
   */
  async validateResponse(req, res, next) {
    try {
      const { response, rules } = req.body;
      
      if (!response) {
        throw new ValidationError('Response is required');
      }
      
      if (!rules || !Array.isArray(rules)) {
        throw new ValidationError('Rules must be an array');
      }
      
      const results = this.testingService.validateResponse(response, rules);
      res.json(results);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Simulate error scenarios
   */
  async simulateErrorScenario(req, res, next) {
    try {
      const { type, options } = req.body;
      
      if (!type) {
        throw new ValidationError('Error scenario type is required');
      }
      
      const result = await this.testingService.simulateErrorScenario(type, options || {});
      res.json(result);
    } catch (error) {
      // For network errors, we want to return a specific response
      if (error.message === 'Network connection refused') {
        res.status(0).json({
          status: 0,
          statusText: 'Network Error',
          headers: {},
          data: {
            error: error.message
          },
          responseTime: 0
        });
      } else {
        next(error);
      }
    }
  }
}

module.exports = new TestingController();
