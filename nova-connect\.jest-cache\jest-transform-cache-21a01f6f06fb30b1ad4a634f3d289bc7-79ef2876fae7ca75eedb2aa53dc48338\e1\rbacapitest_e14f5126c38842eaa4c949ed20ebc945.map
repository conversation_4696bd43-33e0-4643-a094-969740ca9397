{"version": 3, "names": ["request", "require", "mongoose", "MongoMemoryServer", "app", "User", "Role", "Permission", "UserRole", "hashPassword", "mongoServer", "adminToken", "userToken", "adminUser", "regularUser", "testRole", "testPermission", "beforeAll", "create", "mongo<PERSON>ri", "get<PERSON><PERSON>", "connect", "useNewUrlParser", "useUnifiedTopology", "username", "email", "password", "firstName", "lastName", "isAdmin", "save", "name", "description", "resource", "action", "isSystem", "permissions", "_id", "isDefault", "userRole", "userId", "roleId", "adminLoginResponse", "post", "send", "userLoginResponse", "body", "token", "afterAll", "disconnect", "stop", "describe", "createdRoleId", "test", "response", "get", "set", "expect", "status", "toBe", "Array", "isArray", "length", "toBeGreaterThan", "find", "role", "toBeTruthy", "newRole", "toString", "updatedRole", "put", "delete", "success", "getResponse", "createdPermissionId", "perm", "newPermission", "updatedPermission", "getRolesResponse", "findOne", "r", "toBeFalsy"], "sources": ["rbac-api.test.js"], "sourcesContent": ["/**\n * RBAC API Integration Tests\n * \n * These tests verify that the RBAC API endpoints work correctly.\n */\n\nconst request = require('supertest');\nconst mongoose = require('mongoose');\nconst { MongoMemoryServer } = require('mongodb-memory-server');\nconst app = require('../../app');\nconst User = require('../../api/models/User');\nconst Role = require('../../api/models/Role');\nconst Permission = require('../../api/models/Permission');\nconst UserRole = require('../../api/models/UserRole');\nconst { hashPassword } = require('../../api/utils/auth');\n\nlet mongoServer;\nlet adminToken;\nlet userToken;\nlet adminUser;\nlet regularUser;\nlet testRole;\nlet testPermission;\n\n/**\n * Setup test environment before all tests\n */\nbeforeAll(async () => {\n  // Set up in-memory MongoDB\n  mongoServer = await MongoMemoryServer.create();\n  const mongoUri = mongoServer.getUri();\n  \n  await mongoose.connect(mongoUri, {\n    useNewUrlParser: true,\n    useUnifiedTopology: true\n  });\n  \n  // Create test users\n  adminUser = new User({\n    username: 'admin',\n    email: '<EMAIL>',\n    password: await hashPassword('password123'),\n    firstName: 'Admin',\n    lastName: 'User',\n    isAdmin: true\n  });\n  \n  regularUser = new User({\n    username: 'user',\n    email: '<EMAIL>',\n    password: await hashPassword('password123'),\n    firstName: 'Regular',\n    lastName: 'User',\n    isAdmin: false\n  });\n  \n  await adminUser.save();\n  await regularUser.save();\n  \n  // Create test permission\n  testPermission = new Permission({\n    name: 'Test Permission',\n    description: 'Test permission for integration tests',\n    resource: 'test',\n    action: 'read',\n    isSystem: false\n  });\n  \n  await testPermission.save();\n  \n  // Create test role\n  testRole = new Role({\n    name: 'Test Role',\n    description: 'Test role for integration tests',\n    permissions: [testPermission._id],\n    isSystem: false,\n    isDefault: false\n  });\n  \n  await testRole.save();\n  \n  // Assign role to regular user\n  const userRole = new UserRole({\n    userId: regularUser._id,\n    roleId: testRole._id\n  });\n  \n  await userRole.save();\n  \n  // Get authentication tokens\n  const adminLoginResponse = await request(app)\n    .post('/api/auth/login')\n    .send({ email: '<EMAIL>', password: 'password123' });\n  \n  const userLoginResponse = await request(app)\n    .post('/api/auth/login')\n    .send({ email: '<EMAIL>', password: 'password123' });\n  \n  adminToken = adminLoginResponse.body.token;\n  userToken = userLoginResponse.body.token;\n});\n\n/**\n * Clean up after all tests\n */\nafterAll(async () => {\n  await mongoose.disconnect();\n  await mongoServer.stop();\n});\n\n/**\n * Role API Tests\n */\ndescribe('Role API', () => {\n  let createdRoleId;\n  \n  test('GET /api/rbac/roles should return all roles', async () => {\n    const response = await request(app)\n      .get('/api/rbac/roles')\n      .set('Authorization', `Bearer ${adminToken}`);\n    \n    expect(response.status).toBe(200);\n    expect(Array.isArray(response.body)).toBe(true);\n    expect(response.body.length).toBeGreaterThan(0);\n    expect(response.body.find(role => role.name === 'Test Role')).toBeTruthy();\n  });\n  \n  test('GET /api/rbac/roles/:id should return a specific role', async () => {\n    const response = await request(app)\n      .get(`/api/rbac/roles/${testRole._id}`)\n      .set('Authorization', `Bearer ${adminToken}`);\n    \n    expect(response.status).toBe(200);\n    expect(response.body.name).toBe('Test Role');\n    expect(response.body.description).toBe('Test role for integration tests');\n    expect(Array.isArray(response.body.permissions)).toBe(true);\n  });\n  \n  test('POST /api/rbac/roles should create a new role', async () => {\n    const newRole = {\n      name: 'New Test Role',\n      description: 'New test role created via API',\n      permissions: [testPermission._id.toString()]\n    };\n    \n    const response = await request(app)\n      .post('/api/rbac/roles')\n      .set('Authorization', `Bearer ${adminToken}`)\n      .send(newRole);\n    \n    expect(response.status).toBe(201);\n    expect(response.body.name).toBe('New Test Role');\n    expect(response.body.description).toBe('New test role created via API');\n    expect(Array.isArray(response.body.permissions)).toBe(true);\n    \n    createdRoleId = response.body._id;\n  });\n  \n  test('POST /api/rbac/roles should handle string permission format', async () => {\n    const newRole = {\n      name: 'String Permission Role',\n      description: 'Role with string permission format',\n      permissions: ['test:read']\n    };\n    \n    const response = await request(app)\n      .post('/api/rbac/roles')\n      .set('Authorization', `Bearer ${adminToken}`)\n      .send(newRole);\n    \n    expect(response.status).toBe(201);\n    expect(response.body.name).toBe('String Permission Role');\n    expect(Array.isArray(response.body.permissions)).toBe(true);\n  });\n  \n  test('PUT /api/rbac/roles/:id should update a role', async () => {\n    const updatedRole = {\n      name: 'Updated Test Role',\n      description: 'Updated test role via API'\n    };\n    \n    const response = await request(app)\n      .put(`/api/rbac/roles/${createdRoleId}`)\n      .set('Authorization', `Bearer ${adminToken}`)\n      .send(updatedRole);\n    \n    expect(response.status).toBe(200);\n    expect(response.body.name).toBe('Updated Test Role');\n    expect(response.body.description).toBe('Updated test role via API');\n  });\n  \n  test('DELETE /api/rbac/roles/:id should delete a role', async () => {\n    const response = await request(app)\n      .delete(`/api/rbac/roles/${createdRoleId}`)\n      .set('Authorization', `Bearer ${adminToken}`);\n    \n    expect(response.status).toBe(200);\n    expect(response.body.success).toBe(true);\n    \n    // Verify role is deleted\n    const getResponse = await request(app)\n      .get(`/api/rbac/roles/${createdRoleId}`)\n      .set('Authorization', `Bearer ${adminToken}`);\n    \n    expect(getResponse.status).toBe(404);\n  });\n});\n\n/**\n * Permission API Tests\n */\ndescribe('Permission API', () => {\n  let createdPermissionId;\n  \n  test('GET /api/rbac/permissions should return all permissions', async () => {\n    const response = await request(app)\n      .get('/api/rbac/permissions')\n      .set('Authorization', `Bearer ${adminToken}`);\n    \n    expect(response.status).toBe(200);\n    expect(Array.isArray(response.body)).toBe(true);\n    expect(response.body.length).toBeGreaterThan(0);\n    expect(response.body.find(perm => perm.name === 'Test Permission')).toBeTruthy();\n  });\n  \n  test('GET /api/rbac/permissions/:id should return a specific permission', async () => {\n    const response = await request(app)\n      .get(`/api/rbac/permissions/${testPermission._id}`)\n      .set('Authorization', `Bearer ${adminToken}`);\n    \n    expect(response.status).toBe(200);\n    expect(response.body.name).toBe('Test Permission');\n    expect(response.body.resource).toBe('test');\n    expect(response.body.action).toBe('read');\n  });\n  \n  test('POST /api/rbac/permissions should create a new permission', async () => {\n    const newPermission = {\n      name: 'New Test Permission',\n      description: 'New test permission created via API',\n      resource: 'test',\n      action: 'write'\n    };\n    \n    const response = await request(app)\n      .post('/api/rbac/permissions')\n      .set('Authorization', `Bearer ${adminToken}`)\n      .send(newPermission);\n    \n    expect(response.status).toBe(201);\n    expect(response.body.name).toBe('New Test Permission');\n    expect(response.body.resource).toBe('test');\n    expect(response.body.action).toBe('write');\n    \n    createdPermissionId = response.body._id;\n  });\n  \n  test('PUT /api/rbac/permissions/:id should update a permission', async () => {\n    const updatedPermission = {\n      name: 'Updated Test Permission',\n      description: 'Updated test permission via API'\n    };\n    \n    const response = await request(app)\n      .put(`/api/rbac/permissions/${createdPermissionId}`)\n      .set('Authorization', `Bearer ${adminToken}`)\n      .send(updatedPermission);\n    \n    expect(response.status).toBe(200);\n    expect(response.body.name).toBe('Updated Test Permission');\n    expect(response.body.description).toBe('Updated test permission via API');\n  });\n  \n  test('DELETE /api/rbac/permissions/:id should delete a permission', async () => {\n    const response = await request(app)\n      .delete(`/api/rbac/permissions/${createdPermissionId}`)\n      .set('Authorization', `Bearer ${adminToken}`);\n    \n    expect(response.status).toBe(200);\n    expect(response.body.success).toBe(true);\n    \n    // Verify permission is deleted\n    const getResponse = await request(app)\n      .get(`/api/rbac/permissions/${createdPermissionId}`)\n      .set('Authorization', `Bearer ${adminToken}`);\n    \n    expect(getResponse.status).toBe(404);\n  });\n});\n\n/**\n * User Role API Tests\n */\ndescribe('User Role API', () => {\n  test('GET /api/rbac/users/:userId/roles should return user roles', async () => {\n    const response = await request(app)\n      .get(`/api/rbac/users/${regularUser._id}/roles`)\n      .set('Authorization', `Bearer ${adminToken}`);\n    \n    expect(response.status).toBe(200);\n    expect(Array.isArray(response.body)).toBe(true);\n    expect(response.body.length).toBe(1);\n    expect(response.body[0].name).toBe('Test Role');\n  });\n  \n  test('POST /api/rbac/users/:userId/roles should assign role to user', async () => {\n    // Create a new role to assign\n    const newRole = new Role({\n      name: 'Assign Test Role',\n      description: 'Role to test assignment',\n      permissions: [testPermission._id],\n      isSystem: false,\n      isDefault: false\n    });\n    \n    await newRole.save();\n    \n    const response = await request(app)\n      .post(`/api/rbac/users/${regularUser._id}/roles`)\n      .set('Authorization', `Bearer ${adminToken}`)\n      .send({ roleId: newRole._id.toString() });\n    \n    expect(response.status).toBe(200);\n    expect(response.body.success).toBe(true);\n    \n    // Verify role was assigned\n    const getRolesResponse = await request(app)\n      .get(`/api/rbac/users/${regularUser._id}/roles`)\n      .set('Authorization', `Bearer ${adminToken}`);\n    \n    expect(getRolesResponse.body.length).toBe(2);\n    expect(getRolesResponse.body.find(role => role.name === 'Assign Test Role')).toBeTruthy();\n  });\n  \n  test('DELETE /api/rbac/users/:userId/roles/:roleId should remove role from user', async () => {\n    const role = await Role.findOne({ name: 'Assign Test Role' });\n    \n    const response = await request(app)\n      .delete(`/api/rbac/users/${regularUser._id}/roles/${role._id}`)\n      .set('Authorization', `Bearer ${adminToken}`);\n    \n    expect(response.status).toBe(200);\n    expect(response.body.success).toBe(true);\n    \n    // Verify role was removed\n    const getRolesResponse = await request(app)\n      .get(`/api/rbac/users/${regularUser._id}/roles`)\n      .set('Authorization', `Bearer ${adminToken}`);\n    \n    expect(getRolesResponse.body.length).toBe(1);\n    expect(getRolesResponse.body.find(r => r.name === 'Assign Test Role')).toBeFalsy();\n  });\n  \n  test('GET /api/rbac/users/:userId/permissions should return user permissions', async () => {\n    const response = await request(app)\n      .get(`/api/rbac/users/${regularUser._id}/permissions`)\n      .set('Authorization', `Bearer ${adminToken}`);\n    \n    expect(response.status).toBe(200);\n    expect(Array.isArray(response.body)).toBe(true);\n    expect(response.body.length).toBeGreaterThan(0);\n    expect(response.body.find(perm => perm.resource === 'test' && perm.action === 'read')).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,OAAO,GAAGC,OAAO,CAAC,WAAW,CAAC;AACpC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;AACpC,MAAM;EAAEE;AAAkB,CAAC,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AAC9D,MAAMG,GAAG,GAAGH,OAAO,CAAC,WAAW,CAAC;AAChC,MAAMI,IAAI,GAAGJ,OAAO,CAAC,uBAAuB,CAAC;AAC7C,MAAMK,IAAI,GAAGL,OAAO,CAAC,uBAAuB,CAAC;AAC7C,MAAMM,UAAU,GAAGN,OAAO,CAAC,6BAA6B,CAAC;AACzD,MAAMO,QAAQ,GAAGP,OAAO,CAAC,2BAA2B,CAAC;AACrD,MAAM;EAAEQ;AAAa,CAAC,GAAGR,OAAO,CAAC,sBAAsB,CAAC;AAExD,IAAIS,WAAW;AACf,IAAIC,UAAU;AACd,IAAIC,SAAS;AACb,IAAIC,SAAS;AACb,IAAIC,WAAW;AACf,IAAIC,QAAQ;AACZ,IAAIC,cAAc;;AAElB;AACA;AACA;AACAC,SAAS,CAAC,YAAY;EACpB;EACAP,WAAW,GAAG,MAAMP,iBAAiB,CAACe,MAAM,CAAC,CAAC;EAC9C,MAAMC,QAAQ,GAAGT,WAAW,CAACU,MAAM,CAAC,CAAC;EAErC,MAAMlB,QAAQ,CAACmB,OAAO,CAACF,QAAQ,EAAE;IAC/BG,eAAe,EAAE,IAAI;IACrBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACAV,SAAS,GAAG,IAAIR,IAAI,CAAC;IACnBmB,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,mBAAmB;IAC1BC,QAAQ,EAAE,MAAMjB,YAAY,CAAC,aAAa,CAAC;IAC3CkB,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAE;EACX,CAAC,CAAC;EAEFf,WAAW,GAAG,IAAIT,IAAI,CAAC;IACrBmB,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,MAAMjB,YAAY,CAAC,aAAa,CAAC;IAC3CkB,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAMhB,SAAS,CAACiB,IAAI,CAAC,CAAC;EACtB,MAAMhB,WAAW,CAACgB,IAAI,CAAC,CAAC;;EAExB;EACAd,cAAc,GAAG,IAAIT,UAAU,CAAC;IAC9BwB,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAE,uCAAuC;IACpDC,QAAQ,EAAE,MAAM;IAChBC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMnB,cAAc,CAACc,IAAI,CAAC,CAAC;;EAE3B;EACAf,QAAQ,GAAG,IAAIT,IAAI,CAAC;IAClByB,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE,iCAAiC;IAC9CI,WAAW,EAAE,CAACpB,cAAc,CAACqB,GAAG,CAAC;IACjCF,QAAQ,EAAE,KAAK;IACfG,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAMvB,QAAQ,CAACe,IAAI,CAAC,CAAC;;EAErB;EACA,MAAMS,QAAQ,GAAG,IAAI/B,QAAQ,CAAC;IAC5BgC,MAAM,EAAE1B,WAAW,CAACuB,GAAG;IACvBI,MAAM,EAAE1B,QAAQ,CAACsB;EACnB,CAAC,CAAC;EAEF,MAAME,QAAQ,CAACT,IAAI,CAAC,CAAC;;EAErB;EACA,MAAMY,kBAAkB,GAAG,MAAM1C,OAAO,CAACI,GAAG,CAAC,CAC1CuC,IAAI,CAAC,iBAAiB,CAAC,CACvBC,IAAI,CAAC;IAAEnB,KAAK,EAAE,mBAAmB;IAAEC,QAAQ,EAAE;EAAc,CAAC,CAAC;EAEhE,MAAMmB,iBAAiB,GAAG,MAAM7C,OAAO,CAACI,GAAG,CAAC,CACzCuC,IAAI,CAAC,iBAAiB,CAAC,CACvBC,IAAI,CAAC;IAAEnB,KAAK,EAAE,kBAAkB;IAAEC,QAAQ,EAAE;EAAc,CAAC,CAAC;EAE/Df,UAAU,GAAG+B,kBAAkB,CAACI,IAAI,CAACC,KAAK;EAC1CnC,SAAS,GAAGiC,iBAAiB,CAACC,IAAI,CAACC,KAAK;AAC1C,CAAC,CAAC;;AAEF;AACA;AACA;AACAC,QAAQ,CAAC,YAAY;EACnB,MAAM9C,QAAQ,CAAC+C,UAAU,CAAC,CAAC;EAC3B,MAAMvC,WAAW,CAACwC,IAAI,CAAC,CAAC;AAC1B,CAAC,CAAC;;AAEF;AACA;AACA;AACAC,QAAQ,CAAC,UAAU,EAAE,MAAM;EACzB,IAAIC,aAAa;EAEjBC,IAAI,CAAC,6CAA6C,EAAE,YAAY;IAC9D,MAAMC,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCmD,GAAG,CAAC,iBAAiB,CAAC,CACtBC,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC;IAE/C8C,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACG,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACR,IAAI,CAAC,CAAC,CAACa,IAAI,CAAC,IAAI,CAAC;IAC/CF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACgB,MAAM,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;IAC/CN,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACkB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAClC,IAAI,KAAK,WAAW,CAAC,CAAC,CAACmC,UAAU,CAAC,CAAC;EAC5E,CAAC,CAAC;EAEFb,IAAI,CAAC,uDAAuD,EAAE,YAAY;IACxE,MAAMC,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCmD,GAAG,CAAC,mBAAmBxC,QAAQ,CAACsB,GAAG,EAAE,CAAC,CACtCmB,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC;IAE/C8C,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACf,IAAI,CAAC,CAAC4B,IAAI,CAAC,WAAW,CAAC;IAC5CF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACd,WAAW,CAAC,CAAC2B,IAAI,CAAC,iCAAiC,CAAC;IACzEF,MAAM,CAACG,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACR,IAAI,CAACV,WAAW,CAAC,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC;EAC7D,CAAC,CAAC;EAEFN,IAAI,CAAC,+CAA+C,EAAE,YAAY;IAChE,MAAMc,OAAO,GAAG;MACdpC,IAAI,EAAE,eAAe;MACrBC,WAAW,EAAE,+BAA+B;MAC5CI,WAAW,EAAE,CAACpB,cAAc,CAACqB,GAAG,CAAC+B,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAED,MAAMd,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCuC,IAAI,CAAC,iBAAiB,CAAC,CACvBa,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC,CAC5CiC,IAAI,CAACuB,OAAO,CAAC;IAEhBV,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACf,IAAI,CAAC,CAAC4B,IAAI,CAAC,eAAe,CAAC;IAChDF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACd,WAAW,CAAC,CAAC2B,IAAI,CAAC,+BAA+B,CAAC;IACvEF,MAAM,CAACG,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACR,IAAI,CAACV,WAAW,CAAC,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC;IAE3DP,aAAa,GAAGE,QAAQ,CAACR,IAAI,CAACT,GAAG;EACnC,CAAC,CAAC;EAEFgB,IAAI,CAAC,6DAA6D,EAAE,YAAY;IAC9E,MAAMc,OAAO,GAAG;MACdpC,IAAI,EAAE,wBAAwB;MAC9BC,WAAW,EAAE,oCAAoC;MACjDI,WAAW,EAAE,CAAC,WAAW;IAC3B,CAAC;IAED,MAAMkB,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCuC,IAAI,CAAC,iBAAiB,CAAC,CACvBa,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC,CAC5CiC,IAAI,CAACuB,OAAO,CAAC;IAEhBV,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACf,IAAI,CAAC,CAAC4B,IAAI,CAAC,wBAAwB,CAAC;IACzDF,MAAM,CAACG,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACR,IAAI,CAACV,WAAW,CAAC,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC;EAC7D,CAAC,CAAC;EAEFN,IAAI,CAAC,8CAA8C,EAAE,YAAY;IAC/D,MAAMgB,WAAW,GAAG;MAClBtC,IAAI,EAAE,mBAAmB;MACzBC,WAAW,EAAE;IACf,CAAC;IAED,MAAMsB,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCkE,GAAG,CAAC,mBAAmBlB,aAAa,EAAE,CAAC,CACvCI,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC,CAC5CiC,IAAI,CAACyB,WAAW,CAAC;IAEpBZ,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACf,IAAI,CAAC,CAAC4B,IAAI,CAAC,mBAAmB,CAAC;IACpDF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACd,WAAW,CAAC,CAAC2B,IAAI,CAAC,2BAA2B,CAAC;EACrE,CAAC,CAAC;EAEFN,IAAI,CAAC,iDAAiD,EAAE,YAAY;IAClE,MAAMC,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCmE,MAAM,CAAC,mBAAmBnB,aAAa,EAAE,CAAC,CAC1CI,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC;IAE/C8C,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAAC0B,OAAO,CAAC,CAACb,IAAI,CAAC,IAAI,CAAC;;IAExC;IACA,MAAMc,WAAW,GAAG,MAAMzE,OAAO,CAACI,GAAG,CAAC,CACnCmD,GAAG,CAAC,mBAAmBH,aAAa,EAAE,CAAC,CACvCI,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC;IAE/C8C,MAAM,CAACgB,WAAW,CAACf,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACtC,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;AACA;AACA;AACAR,QAAQ,CAAC,gBAAgB,EAAE,MAAM;EAC/B,IAAIuB,mBAAmB;EAEvBrB,IAAI,CAAC,yDAAyD,EAAE,YAAY;IAC1E,MAAMC,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCmD,GAAG,CAAC,uBAAuB,CAAC,CAC5BC,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC;IAE/C8C,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACG,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACR,IAAI,CAAC,CAAC,CAACa,IAAI,CAAC,IAAI,CAAC;IAC/CF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACgB,MAAM,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;IAC/CN,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACkB,IAAI,CAACW,IAAI,IAAIA,IAAI,CAAC5C,IAAI,KAAK,iBAAiB,CAAC,CAAC,CAACmC,UAAU,CAAC,CAAC;EAClF,CAAC,CAAC;EAEFb,IAAI,CAAC,mEAAmE,EAAE,YAAY;IACpF,MAAMC,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCmD,GAAG,CAAC,yBAAyBvC,cAAc,CAACqB,GAAG,EAAE,CAAC,CAClDmB,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC;IAE/C8C,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACf,IAAI,CAAC,CAAC4B,IAAI,CAAC,iBAAiB,CAAC;IAClDF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACb,QAAQ,CAAC,CAAC0B,IAAI,CAAC,MAAM,CAAC;IAC3CF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACZ,MAAM,CAAC,CAACyB,IAAI,CAAC,MAAM,CAAC;EAC3C,CAAC,CAAC;EAEFN,IAAI,CAAC,2DAA2D,EAAE,YAAY;IAC5E,MAAMuB,aAAa,GAAG;MACpB7C,IAAI,EAAE,qBAAqB;MAC3BC,WAAW,EAAE,qCAAqC;MAClDC,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE;IACV,CAAC;IAED,MAAMoB,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCuC,IAAI,CAAC,uBAAuB,CAAC,CAC7Ba,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC,CAC5CiC,IAAI,CAACgC,aAAa,CAAC;IAEtBnB,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACf,IAAI,CAAC,CAAC4B,IAAI,CAAC,qBAAqB,CAAC;IACtDF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACb,QAAQ,CAAC,CAAC0B,IAAI,CAAC,MAAM,CAAC;IAC3CF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACZ,MAAM,CAAC,CAACyB,IAAI,CAAC,OAAO,CAAC;IAE1Ce,mBAAmB,GAAGpB,QAAQ,CAACR,IAAI,CAACT,GAAG;EACzC,CAAC,CAAC;EAEFgB,IAAI,CAAC,0DAA0D,EAAE,YAAY;IAC3E,MAAMwB,iBAAiB,GAAG;MACxB9C,IAAI,EAAE,yBAAyB;MAC/BC,WAAW,EAAE;IACf,CAAC;IAED,MAAMsB,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCkE,GAAG,CAAC,yBAAyBI,mBAAmB,EAAE,CAAC,CACnDlB,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC,CAC5CiC,IAAI,CAACiC,iBAAiB,CAAC;IAE1BpB,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACf,IAAI,CAAC,CAAC4B,IAAI,CAAC,yBAAyB,CAAC;IAC1DF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACd,WAAW,CAAC,CAAC2B,IAAI,CAAC,iCAAiC,CAAC;EAC3E,CAAC,CAAC;EAEFN,IAAI,CAAC,6DAA6D,EAAE,YAAY;IAC9E,MAAMC,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCmE,MAAM,CAAC,yBAAyBG,mBAAmB,EAAE,CAAC,CACtDlB,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC;IAE/C8C,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAAC0B,OAAO,CAAC,CAACb,IAAI,CAAC,IAAI,CAAC;;IAExC;IACA,MAAMc,WAAW,GAAG,MAAMzE,OAAO,CAACI,GAAG,CAAC,CACnCmD,GAAG,CAAC,yBAAyBmB,mBAAmB,EAAE,CAAC,CACnDlB,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC;IAE/C8C,MAAM,CAACgB,WAAW,CAACf,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACtC,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;AACA;AACA;AACAR,QAAQ,CAAC,eAAe,EAAE,MAAM;EAC9BE,IAAI,CAAC,4DAA4D,EAAE,YAAY;IAC7E,MAAMC,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCmD,GAAG,CAAC,mBAAmBzC,WAAW,CAACuB,GAAG,QAAQ,CAAC,CAC/CmB,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC;IAE/C8C,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACG,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACR,IAAI,CAAC,CAAC,CAACa,IAAI,CAAC,IAAI,CAAC;IAC/CF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACgB,MAAM,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC;IACpCF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,CAAC4B,IAAI,CAAC,WAAW,CAAC;EACjD,CAAC,CAAC;EAEFN,IAAI,CAAC,+DAA+D,EAAE,YAAY;IAChF;IACA,MAAMc,OAAO,GAAG,IAAI7D,IAAI,CAAC;MACvByB,IAAI,EAAE,kBAAkB;MACxBC,WAAW,EAAE,yBAAyB;MACtCI,WAAW,EAAE,CAACpB,cAAc,CAACqB,GAAG,CAAC;MACjCF,QAAQ,EAAE,KAAK;MACfG,SAAS,EAAE;IACb,CAAC,CAAC;IAEF,MAAM6B,OAAO,CAACrC,IAAI,CAAC,CAAC;IAEpB,MAAMwB,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCuC,IAAI,CAAC,mBAAmB7B,WAAW,CAACuB,GAAG,QAAQ,CAAC,CAChDmB,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC,CAC5CiC,IAAI,CAAC;MAAEH,MAAM,EAAE0B,OAAO,CAAC9B,GAAG,CAAC+B,QAAQ,CAAC;IAAE,CAAC,CAAC;IAE3CX,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAAC0B,OAAO,CAAC,CAACb,IAAI,CAAC,IAAI,CAAC;;IAExC;IACA,MAAMmB,gBAAgB,GAAG,MAAM9E,OAAO,CAACI,GAAG,CAAC,CACxCmD,GAAG,CAAC,mBAAmBzC,WAAW,CAACuB,GAAG,QAAQ,CAAC,CAC/CmB,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC;IAE/C8C,MAAM,CAACqB,gBAAgB,CAAChC,IAAI,CAACgB,MAAM,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC;IAC5CF,MAAM,CAACqB,gBAAgB,CAAChC,IAAI,CAACkB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAClC,IAAI,KAAK,kBAAkB,CAAC,CAAC,CAACmC,UAAU,CAAC,CAAC;EAC3F,CAAC,CAAC;EAEFb,IAAI,CAAC,2EAA2E,EAAE,YAAY;IAC5F,MAAMY,IAAI,GAAG,MAAM3D,IAAI,CAACyE,OAAO,CAAC;MAAEhD,IAAI,EAAE;IAAmB,CAAC,CAAC;IAE7D,MAAMuB,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCmE,MAAM,CAAC,mBAAmBzD,WAAW,CAACuB,GAAG,UAAU4B,IAAI,CAAC5B,GAAG,EAAE,CAAC,CAC9DmB,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC;IAE/C8C,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAAC0B,OAAO,CAAC,CAACb,IAAI,CAAC,IAAI,CAAC;;IAExC;IACA,MAAMmB,gBAAgB,GAAG,MAAM9E,OAAO,CAACI,GAAG,CAAC,CACxCmD,GAAG,CAAC,mBAAmBzC,WAAW,CAACuB,GAAG,QAAQ,CAAC,CAC/CmB,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC;IAE/C8C,MAAM,CAACqB,gBAAgB,CAAChC,IAAI,CAACgB,MAAM,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC;IAC5CF,MAAM,CAACqB,gBAAgB,CAAChC,IAAI,CAACkB,IAAI,CAACgB,CAAC,IAAIA,CAAC,CAACjD,IAAI,KAAK,kBAAkB,CAAC,CAAC,CAACkD,SAAS,CAAC,CAAC;EACpF,CAAC,CAAC;EAEF5B,IAAI,CAAC,wEAAwE,EAAE,YAAY;IACzF,MAAMC,QAAQ,GAAG,MAAMtD,OAAO,CAACI,GAAG,CAAC,CAChCmD,GAAG,CAAC,mBAAmBzC,WAAW,CAACuB,GAAG,cAAc,CAAC,CACrDmB,GAAG,CAAC,eAAe,EAAE,UAAU7C,UAAU,EAAE,CAAC;IAE/C8C,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACjCF,MAAM,CAACG,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACR,IAAI,CAAC,CAAC,CAACa,IAAI,CAAC,IAAI,CAAC;IAC/CF,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACgB,MAAM,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;IAC/CN,MAAM,CAACH,QAAQ,CAACR,IAAI,CAACkB,IAAI,CAACW,IAAI,IAAIA,IAAI,CAAC1C,QAAQ,KAAK,MAAM,IAAI0C,IAAI,CAACzC,MAAM,KAAK,MAAM,CAAC,CAAC,CAACgC,UAAU,CAAC,CAAC;EACrG,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}