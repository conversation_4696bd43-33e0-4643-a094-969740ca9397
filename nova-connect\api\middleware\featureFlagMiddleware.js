/**
 * Feature Flag Middleware
 * 
 * This middleware provides feature flag functionality for Express routes.
 */

const FeatureFlagService = require('../services/FeatureFlagService');
const logger = require('../utils/logger');

/**
 * Create a new feature flag service instance
 */
const featureFlagService = new FeatureFlagService();

/**
 * Feature flag middleware
 * @param {string} featureId - Feature ID
 * @param {Object} options - Options
 * @returns {Function} - Express middleware
 */
const requireFeature = (featureId, options = {}) => {
  const {
    redirectUrl = '/api/unauthorized',
    sendError = true,
    errorStatus = 403,
    errorMessage = 'Feature not available in your subscription tier'
  } = options;
  
  return async (req, res, next) => {
    try {
      // Get user ID from request
      const userId = req.user?.id || 'anonymous';
      
      // Check if user has access to feature
      const hasAccess = await featureFlagService.hasFeatureAccess(userId, featureId);
      
      if (hasAccess) {
        // Track feature usage
        await featureFlagService.trackFeatureUsage(userId, featureId);
        
        // Add feature flag info to request
        req.featureFlags = req.featureFlags || {};
        req.featureFlags[featureId] = true;
        
        next();
      } else {
        // Feature is not enabled for user
        if (sendError) {
          // Send error response
          res.status(errorStatus).json({
            error: 'Feature Unavailable',
            message: errorMessage,
            feature: featureId
          });
        } else {
          // Redirect
          res.redirect(redirectUrl);
        }
      }
    } catch (error) {
      logger.error('Feature flag middleware error', { error, featureId });
      
      // Send error response
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'An error occurred while checking feature access'
      });
    }
  };
};

/**
 * Check if feature limit has been reached
 * @param {string} featureId - Feature ID
 * @param {string} limitKey - Limit key
 * @param {Object} options - Options
 * @returns {Function} - Express middleware
 */
const checkFeatureLimit = (featureId, limitKey, options = {}) => {
  const {
    redirectUrl = '/api/limit-exceeded',
    sendError = true,
    errorStatus = 429,
    errorMessage = 'Feature usage limit exceeded'
  } = options;
  
  return async (req, res, next) => {
    try {
      // Get user ID from request
      const userId = req.user?.id || 'anonymous';
      
      // Check if user has reached feature limit
      const limitReached = await featureFlagService.hasReachedFeatureLimit(userId, featureId, limitKey);
      
      if (!limitReached) {
        next();
      } else {
        // Limit has been reached
        if (sendError) {
          // Get feature limit
          const limit = await featureFlagService.getFeatureLimit(userId, featureId, limitKey);
          
          // Send error response
          res.status(errorStatus).json({
            error: 'Limit Exceeded',
            message: errorMessage,
            feature: featureId,
            limit: limit,
            limitKey: limitKey
          });
        } else {
          // Redirect
          res.redirect(redirectUrl);
        }
      }
    } catch (error) {
      logger.error('Feature limit middleware error', { error, featureId, limitKey });
      
      // Send error response
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'An error occurred while checking feature limit'
      });
    }
  };
};

/**
 * Get user subscription details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getUserSubscription = async (req, res) => {
  try {
    // Get user ID from request
    const userId = req.user?.id || 'anonymous';
    
    // Get user subscription details
    const subscriptionDetails = await featureFlagService.getUserSubscriptionDetails(userId);
    
    res.json(subscriptionDetails);
  } catch (error) {
    logger.error('Get user subscription error', { error });
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'An error occurred while getting subscription details'
    });
  }
};

/**
 * Get available features
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAvailableFeatures = async (req, res) => {
  try {
    // Get user ID from request
    const userId = req.user?.id || 'anonymous';
    
    // Get user's available features
    const availableFeatures = await featureFlagService.getUserAvailableFeatures(userId);
    
    res.json(availableFeatures);
  } catch (error) {
    logger.error('Get available features error', { error });
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'An error occurred while getting available features'
    });
  }
};

/**
 * Get feature usage
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFeatureUsage = async (req, res) => {
  try {
    // Get user ID from request
    const userId = req.user?.id || 'anonymous';
    
    // Get query parameters
    const { featureId, startDate, endDate } = req.query;
    
    // Get feature usage
    const usage = await featureFlagService.getFeatureUsageForUser(
      userId,
      featureId,
      startDate,
      endDate
    );
    
    res.json(usage);
  } catch (error) {
    logger.error('Get feature usage error', { error });
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'An error occurred while getting feature usage'
    });
  }
};

/**
 * Update user subscription
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateUserSubscription = async (req, res) => {
  try {
    // Get user ID from request
    const userId = req.params.userId;
    
    // Get request body
    const { tierId, customFeatures, customLimits } = req.body;
    
    // Update user entitlement
    const updatedEntitlement = await featureFlagService.updateUserEntitlement(userId, {
      tierId,
      customFeatures,
      customLimits
    });
    
    res.json(updatedEntitlement);
  } catch (error) {
    logger.error('Update user subscription error', { error });
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'An error occurred while updating subscription'
    });
  }
};

module.exports = {
  requireFeature,
  checkFeatureLimit,
  getUserSubscription,
  getAvailableFeatures,
  getFeatureUsage,
  updateUserSubscription
};
