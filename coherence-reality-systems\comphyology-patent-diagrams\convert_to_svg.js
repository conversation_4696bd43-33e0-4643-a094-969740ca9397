const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Set up paths
const baseDir = __dirname;
const outputDir = path.join(baseDir, 'SVG_Output');

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir);
    console.log(`Created output directory: ${outputDir}`);
}

// Get all .mmd files in the current directory
const mermaidFiles = fs.readdirSync(baseDir)
    .filter(file => file.endsWith('.mmd') && file.startsWith('FIG'))
    .map(file => ({
        input: path.join(baseDir, file),
        output: path.join(outputDir, file.replace(/\.mmd$/, '.svg')),
        name: file
    }));

console.log(`Found ${mermaidFiles.length} Mermaid diagram files to convert...\n`);

// Convert each file
mermaidFiles.forEach(({ input, output, name }) => {
    try {
        console.log(`🔄 Converting: ${name}`);
        execSync(`npx @mermaid-js/mermaid-cli -i "${input}" -o "${output}"`, { stdio: 'inherit' });
        console.log(`✅ Success: ${path.basename(output)}\n`);
    } catch (error) {
        console.error(`❌ Error converting ${name}:`, error.message);
        console.log(''); // Add spacing between errors
    }
});

console.log('\nConversion complete!');
console.log(`SVG files saved to: ${outputDir}`);
