/**
 * Report Routes
 */

const express = require('express');
const router = express.Router();
const ReportController = require('../controllers/ReportController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticate);

// Report template routes
router.get('/templates', (req, res, next) => {
  ReportController.getAllReportTemplates(req, res, next);
});

router.get('/templates/type/:type', (req, res, next) => {
  ReportController.getReportTemplatesByType(req, res, next);
});

router.get('/templates/:id', (req, res, next) => {
  ReportController.getReportTemplateById(req, res, next);
});

// Routes that require system settings permission
router.post('/templates', hasPermission('system:settings'), (req, res, next) => {
  ReportController.createReportTemplate(req, res, next);
});

router.put('/templates/:id', hasPermission('system:settings'), (req, res, next) => {
  ReportController.updateReportTemplate(req, res, next);
});

router.delete('/templates/:id', hasPermission('system:settings'), (req, res, next) => {
  ReportController.deleteReportTemplate(req, res, next);
});

router.post('/templates/:id/clone', (req, res, next) => {
  ReportController.cloneReportTemplate(req, res, next);
});

// Report generation routes
router.post('/generate/:templateId', (req, res, next) => {
  ReportController.generateReport(req, res, next);
});

// Report routes
router.get('/', hasPermission('system:audit'), (req, res, next) => {
  ReportController.getAllReports(req, res, next);
});

router.get('/my', (req, res, next) => {
  ReportController.getMyReports(req, res, next);
});

router.get('/:id', (req, res, next) => {
  ReportController.getReportById(req, res, next);
});

router.delete('/:id', (req, res, next) => {
  ReportController.deleteReport(req, res, next);
});

router.get('/:id/export/:format', (req, res, next) => {
  ReportController.exportReport(req, res, next);
});

// Scheduled report routes
router.get('/scheduled', hasPermission('system:audit'), (req, res, next) => {
  ReportController.getAllScheduledReports(req, res, next);
});

router.get('/scheduled/my', (req, res, next) => {
  ReportController.getMyScheduledReports(req, res, next);
});

router.get('/scheduled/:id', (req, res, next) => {
  ReportController.getScheduledReportById(req, res, next);
});

router.post('/scheduled', (req, res, next) => {
  ReportController.createScheduledReport(req, res, next);
});

router.put('/scheduled/:id', (req, res, next) => {
  ReportController.updateScheduledReport(req, res, next);
});

router.delete('/scheduled/:id', (req, res, next) => {
  ReportController.deleteScheduledReport(req, res, next);
});

// Admin routes
router.post('/scheduled/run', hasPermission('system:settings'), (req, res, next) => {
  ReportController.runScheduledReports(req, res, next);
});

module.exports = router;
