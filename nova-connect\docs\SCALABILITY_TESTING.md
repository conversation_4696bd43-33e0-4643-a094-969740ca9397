# NovaConnect UAC Scalability Testing Framework

This document describes the scalability testing framework for NovaConnect UAC, which provides comprehensive testing of performance, reliability, and scalability.

## Overview

The NovaConnect UAC Scalability Testing Framework consists of the following components:

1. **Load Testing Scripts**: k6 scripts for testing API endpoints, data normalization, and connectors
2. **Test Data Generator**: Generates realistic test data for load testing
3. **Test Runner**: Orchestrates the execution of load tests
4. **Results Analyzer**: Analyzes test results and generates reports
5. **Docker Environment**: Provides a consistent testing environment

## Prerequisites

Before running the scalability tests, ensure you have the following installed:

- [k6](https://k6.io/docs/getting-started/installation/) - Load testing tool
- [Docker](https://docs.docker.com/get-docker/) - Container platform
- [Docker Compose](https://docs.docker.com/compose/install/) - Multi-container Docker applications
- [Node.js](https://nodejs.org/) - JavaScript runtime

## Test Components

### API Load Test

The API Load Test (`api-load-test.js`) tests the performance and scalability of NovaConnect UAC API endpoints. It includes:

- **Health Check**: Tests the health endpoint
- **Connector Endpoints**: Tests CRUD operations on connectors
- **Data Normalization**: Tests data normalization performance
- **Error Handling**: Tests error handling capabilities

### Normalization Load Test

The Normalization Load Test (`normalization-load-test.js`) focuses on data normalization performance. It includes:

- **Small Data Normalization**: Tests normalization of small data payloads
- **Medium Data Normalization**: Tests normalization of medium data payloads
- **Large Data Normalization**: Tests normalization of large data payloads
- **Complex Data Normalization**: Tests normalization of complex nested data structures
- **Batch Normalization**: Tests batch normalization performance
- **Concurrent Normalization**: Tests concurrent normalization requests

### Connector Load Test

The Connector Load Test (`connector-load-test.js`) tests the performance and scalability of NovaConnect UAC connectors. It includes:

- **Connector Execution**: Tests execution of different connector types (AWS, Azure, GCP, HTTP, Database)
- **Concurrent Connector Execution**: Tests concurrent execution of multiple connectors
- **Connector Error Handling**: Tests connector error handling capabilities

## Test Configuration

The test configuration (`k6-config.js`) defines the load testing parameters:

- **Stages**: Defines the load profile (ramp-up, steady load, stress test, spike test, recovery)
- **Thresholds**: Defines pass/fail criteria for the tests
- **Scenarios**: Defines different test scenarios
- **HTTP Settings**: Defines HTTP request settings

## Running Tests

### Local Environment

To run the tests in your local environment:

1. Start NovaConnect UAC:

```bash
cd nova-connect
npm install
npm start
```

2. Run the tests:

```bash
cd tests/scalability
npm install
node run-scalability-tests.js
```

### Docker Environment

To run the tests in a Docker environment:

#### Linux/macOS

```bash
cd nova-connect/tests/scalability
chmod +x run-docker-tests.sh
./run-docker-tests.sh
```

#### Windows

```bash
cd nova-connect\tests\scalability
run-docker-tests.bat
```

## Test Results

After running the tests, a comprehensive report is generated in the `results` directory:

- `scalability-report.md`: Markdown report with test results and recommendations
- `api-load-test.json`: Raw results from the API load test
- `normalization-load-test.json`: Raw results from the normalization load test
- `connector-load-test.json`: Raw results from the connector load test

## Interpreting Results

The scalability report includes the following sections:

### Summary

The summary section provides an overview of the test results:

- **HTTP Requests**: Total number of HTTP requests made
- **Failed Requests**: Number of failed requests
- **Avg Response Time**: Average response time in milliseconds
- **P95 Response Time**: 95th percentile response time in milliseconds
- **RPS**: Requests per second

### Detailed Results

The detailed results section provides in-depth metrics for each test:

- **Metrics**: Performance metrics (avg, min, med, p90, p95, p99, max)
- **Checks**: Pass rates for various checks
- **Thresholds**: Pass/fail status for defined thresholds

### Recommendations

The recommendations section provides suggestions for improving performance, reliability, and scalability based on the test results.

### Google Cloud Marketplace Readiness Assessment

The Google Cloud Marketplace readiness assessment provides an evaluation of NovaConnect UAC's readiness for Google Cloud Marketplace submission:

- **Performance Score**: Evaluation of performance (1-5)
- **Reliability Score**: Evaluation of reliability (1-5)
- **Scalability Score**: Evaluation of scalability (1-5)
- **Overall Score**: Overall readiness score (1-5)

## Performance Targets

NovaConnect UAC should meet the following performance targets:

- **Response Time**: P95 < 500ms
- **Error Rate**: < 0.1%
- **Throughput**: > 100 RPS
- **Scalability**: Linear scaling with additional resources

## Troubleshooting

### Common Issues

- **Connection Refused**: Ensure NovaConnect UAC is running
- **Authentication Failed**: Ensure API key is correct
- **Out of Memory**: Increase memory allocation for Docker containers
- **Timeout**: Increase timeout settings in k6 configuration

### Debugging

To debug test failures:

1. Check the test logs in the `results` directory
2. Examine the NovaConnect UAC logs
3. Run individual tests with verbose logging:

```bash
k6 run --verbose api-load-test.js
```

## Continuous Integration

The scalability tests can be integrated into a CI/CD pipeline:

```yaml
# Example GitHub Actions workflow
name: Scalability Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  scalability-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Docker
        uses: docker/setup-buildx-action@v1
      - name: Run Scalability Tests
        run: |
          cd nova-connect/tests/scalability
          chmod +x run-docker-tests.sh
          ./run-docker-tests.sh
      - name: Upload Results
        uses: actions/upload-artifact@v2
        with:
          name: scalability-test-results
          path: nova-connect/tests/scalability/results
```

## Best Practices

### Writing Effective Tests

- **Realistic Scenarios**: Test realistic user scenarios
- **Gradual Load**: Gradually increase load to identify breaking points
- **Consistent Environment**: Use Docker to ensure a consistent testing environment
- **Comprehensive Metrics**: Collect comprehensive metrics to identify bottlenecks
- **Regular Testing**: Run tests regularly to detect performance regressions

### Improving Performance

- **Caching**: Implement caching for frequently accessed data
- **Connection Pooling**: Use connection pooling for database and external services
- **Compression**: Enable compression for HTTP responses
- **Clustering**: Use cluster mode to utilize all available CPU cores
- **Asynchronous Processing**: Use asynchronous processing for long-running tasks

## Conclusion

The NovaConnect UAC Scalability Testing Framework provides comprehensive testing of performance, reliability, and scalability. By regularly running these tests, you can ensure that NovaConnect UAC meets the requirements for Google Cloud Marketplace and provides a high-quality experience for users.
