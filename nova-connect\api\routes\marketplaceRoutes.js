/**
 * Marketplace Routes
 * 
 * This file contains routes for Google Cloud Marketplace operations.
 */

const express = require('express');
const router = express.Router();
const MarketplaceController = require('../controllers/MarketplaceController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// Public routes
router.get('/plans', MarketplaceController.getMarketplacePlans);
router.get('/plans/:id', MarketplaceController.getMarketplacePlan);

// GCP Marketplace notification webhook
// This route is public but should be secured with GCP-specific authentication
router.post('/notifications', MarketplaceController.handleMarketplaceNotification);

// Protected routes
router.use(authenticate);

// Tenant provisioning routes
router.post('/tenants', hasPermission('admin:system'), MarketplaceController.provisionTenant);
router.put('/tenants/:tenantId/plan', hasPermission('admin:system'), MarketplaceController.updateTenantPlan);
router.delete('/tenants/:tenantId', hasPermission('admin:system'), MarketplaceController.deprovisionTenant);
router.get('/tenants/:tenantId/status', hasPermission('admin:system'), MarketplaceController.getTenantStatus);

module.exports = router;
