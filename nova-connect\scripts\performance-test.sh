#!/bin/bash
# Script to perform performance testing for NovaConnect UAC

# Set variables
PROJECT_ID=${1:-"novafuse-test"}
CLUSTER_NAME=${2:-"novafuse-test-cluster"}
ZONE=${3:-"us-central1-a"}
NAMESPACE=${4:-"novafuse-test"}

# Get credentials for the cluster
echo "Getting credentials for the cluster..."
gcloud container clusters get-credentials $CLUSTER_NAME --zone $ZONE --project $PROJECT_ID

# Get the service URL
echo "Getting service URL..."
SERVICE_IP=$(kubectl get service novafuse-uac -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
if [ -z "$SERVICE_IP" ]; then
  echo "Service is not exposed externally. Creating a port-forward..."
  kubectl port-forward service/novafuse-uac -n $NAMESPACE 8080:80 &
  SERVICE_URL="http://localhost:8080"
  echo "Service available at $SERVICE_URL"
else
  SERVICE_URL="http://$SERVICE_IP"
  echo "Service available at $SERVICE_URL"
fi

# Install Apache Bench
echo "Installing Apache Bench..."
if ! command -v ab &> /dev/null; then
  echo "Apache Bench not found, installing..."
  apt-get update && apt-get install -y apache2-utils
fi

# Test the health endpoint
echo "Testing health endpoint performance..."
ab -n 1000 -c 10 $SERVICE_URL/health

# Test the API endpoint
echo "Testing API endpoint performance..."
ab -n 1000 -c 10 -H "Authorization: Bearer test-api-key-12345" $SERVICE_URL/api/v1/status

# Test with different concurrency levels
echo "Testing with different concurrency levels..."
for c in 1 5 10 20 50 100; do
  echo "Testing with concurrency $c..."
  ab -n 100 -c $c -H "Authorization: Bearer test-api-key-12345" $SERVICE_URL/api/v1/status
done

# Test with different tiers
echo "Testing with different tiers..."
for tier in core secure enterprise ai_boost; do
  echo "Testing with tier $tier..."
  ab -n 100 -c 10 -H "Authorization: Bearer test-api-key-12345" -H "X-Tier: $tier" $SERVICE_URL/api/v1/status
done

# Generate a report
echo "Generating performance report..."
ab -n 1000 -c 10 -H "Authorization: Bearer test-api-key-12345" -e performance-report.csv $SERVICE_URL/api/v1/status

echo "Performance testing complete!"
