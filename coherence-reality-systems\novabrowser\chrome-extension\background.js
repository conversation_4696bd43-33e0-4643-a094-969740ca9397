// NovaBrowser Chrome Extension - Background Script

class NovaBrowserBackground {
    constructor() {
        this.init();
    }

    init() {
        console.log('🌐 NovaBrowser background script initialized');
        
        // Handle extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                console.log('🎉 NovaBrowser extension installed');
                this.showWelcomeNotification();
            }
        });
        
        // Handle tab updates
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.onPageLoad(tabId, tab.url);
            }
        });
        
        // Handle messages from content scripts
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async response
        });
        
        // Set up context menu
        this.setupContextMenu();
    }

    showWelcomeNotification() {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icon48.png',
            title: 'NovaBrowser Activated',
            message: 'Coherence-first web browsing is now active. Visit any website to see real-time analysis.'
        });
    }

    onPageLoad(tabId, url) {
        // Skip chrome:// and extension pages
        if (url.startsWith('chrome://') || url.startsWith('chrome-extension://')) {
            return;
        }
        
        console.log(`📄 Page loaded: ${url}`);
        
        // Update badge with coherence status
        this.updateBadge(tabId, 'analyzing');
        
        // Inject content script if needed
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ['content-script.js']
        }).catch(error => {
            console.log('Content script injection skipped:', error.message);
        });
    }

    handleMessage(message, sender, sendResponse) {
        switch (message.type) {
            case 'ANALYSIS_COMPLETE':
                this.onAnalysisComplete(message.data, sender.tab);
                sendResponse({ status: 'received' });
                break;
                
            case 'REQUEST_BACKEND_DATA':
                this.getBackendData().then(data => {
                    sendResponse({ data: data });
                });
                break;
                
            case 'LOG_EVENT':
                console.log(`📊 ${message.event}:`, message.data);
                sendResponse({ status: 'logged' });
                break;
                
            default:
                console.log('Unknown message type:', message.type);
                sendResponse({ status: 'unknown' });
        }
    }

    onAnalysisComplete(data, tab) {
        console.log(`✅ Analysis complete for ${tab.url}:`, data);
        
        // Update badge with coherence score
        const coherence = data.coherence.overall;
        this.updateBadge(tab.id, coherence);
        
        // Store analysis data
        chrome.storage.local.set({
            [`analysis_${tab.id}`]: {
                ...data,
                timestamp: Date.now()
            }
        });
        
        // Send notification for low coherence
        if (coherence < 60) {
            this.showLowCoherenceNotification(tab.url, coherence);
        }
    }

    updateBadge(tabId, status) {
        if (typeof status === 'number') {
            // Coherence score
            const color = status >= 82 ? '#00ff96' : status >= 60 ? '#ffa726' : '#ff4757';
            chrome.action.setBadgeText({ text: `${status}%`, tabId: tabId });
            chrome.action.setBadgeBackgroundColor({ color: color, tabId: tabId });
        } else if (status === 'analyzing') {
            chrome.action.setBadgeText({ text: '...', tabId: tabId });
            chrome.action.setBadgeBackgroundColor({ color: '#667eea', tabId: tabId });
        } else {
            chrome.action.setBadgeText({ text: '', tabId: tabId });
        }
    }

    showLowCoherenceNotification(url, coherence) {
        const domain = new URL(url).hostname;
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icon48.png',
            title: '⚠️ Low Coherence Detected',
            message: `${domain} has ${coherence}% coherence (below 82% Ψ-Snap threshold)`
        });
    }

    async getBackendData() {
        try {
            const response = await fetch('http://localhost:8090/status', {
                method: 'GET',
                mode: 'cors'
            });
            
            if (response.ok) {
                return await response.json();
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.warn('Backend unavailable:', error);
            return null;
        }
    }

    setupContextMenu() {
        chrome.contextMenus.create({
            id: 'novabrowser-analyze',
            title: '🧬 Analyze with NovaBrowser',
            contexts: ['page']
        });
        
        chrome.contextMenus.create({
            id: 'novabrowser-autofix',
            title: '🔧 Auto-fix Accessibility',
            contexts: ['page']
        });
        
        chrome.contextMenus.onClicked.addListener((info, tab) => {
            switch (info.menuItemId) {
                case 'novabrowser-analyze':
                    chrome.tabs.sendMessage(tab.id, { type: 'REQUEST_ANALYSIS' });
                    break;
                case 'novabrowser-autofix':
                    chrome.tabs.sendMessage(tab.id, { type: 'AUTO_FIX' });
                    break;
            }
        });
    }
}

// Initialize background script
const background = new NovaBrowserBackground();
