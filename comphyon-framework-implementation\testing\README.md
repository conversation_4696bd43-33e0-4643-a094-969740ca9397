# Comphyon Testing Framework

A comprehensive testing framework for the Comphyon system, including both standard tests and NEPI (Natural Emerging Progressive Intelligence) tests.

## Overview

The Comphyon Testing Framework provides a robust and flexible approach to testing the Comphyon system. It includes:

1. **Standard Testing Framework**: For testing the core functionality of the Comphyon system.
2. **NEPI Testing Framework**: For testing the emergent intelligence aspects of the system.

## Directory Structure

```
testing/
├── test-framework.js           # Base testing framework
├── integration-layer-tests.js  # Tests for the Integration Layer
├── comphyon-system-tests.js    # Tests for the Comphyon System
├── run-tests.js                # Runner for all standard tests
├── generate-test-report.js     # Generator for standard test reports
├── run-all-tests.js            # Runner for both standard and NEPI tests
├── nepi/                       # NEPI Testing Framework
│   ├── nepi-test-framework.js  # NEPI-specific testing framework
│   ├── foundational-physics-tests.js  # Tests for foundational physics
│   ├── meter-tests.js          # Tests for the Comphyon Meter
│   ├── adversarial-tests.js    # Tests for adversarial scenarios
│   ├── run-nepi-tests.js       # Runner for all NEPI tests
│   └── README.md               # Documentation for NEPI Testing Framework
├── INTEGRATION.md              # Documentation on framework integration
└── README.md                   # This file
```

## Standard Testing Framework

The standard testing framework provides a foundation for testing the core functionality of the Comphyon system. It includes:

### Core Classes

- **TestCase**: Represents a single test case.
- **TestSuite**: Represents a collection of test cases.
- **TestRunner**: Runs test suites and aggregates results.

### Assertions

The framework includes a comprehensive set of assertions for validating test results:

- `equal`: Assert that two values are equal.
- `notEqual`: Assert that two values are not equal.
- `isTrue`: Assert that a value is true.
- `isFalse`: Assert that a value is false.
- `isNull`: Assert that a value is null.
- `isNotNull`: Assert that a value is not null.
- `isDefined`: Assert that a value is defined.
- `isUndefined`: Assert that a value is undefined.
- `isArray`: Assert that a value is an array.
- `isObject`: Assert that a value is an object.
- `isFunction`: Assert that a value is a function.
- `isString`: Assert that a value is a string.
- `isNumber`: Assert that a value is a number.
- `isBoolean`: Assert that a value is a boolean.
- `approximately`: Assert that a value is approximately equal to another value.
- `contains`: Assert that an array or string contains a value.
- `doesNotContain`: Assert that an array or string does not contain a value.
- `throws`: Assert that a function throws an error.
- `doesNotThrow`: Assert that a function does not throw an error.

### Test Suites

The framework includes test suites for various aspects of the Comphyon system:

1. **Integration Layer Tests**: Tests for the Comphyon Integration Layer.
2. **Comphyon System Tests**: Tests for the Comphyon System.

### Running Tests

To run the standard tests:

```bash
node testing/run-tests.js
```

### Test Reports

The framework generates comprehensive HTML reports that include:

- Test summary (total, passed, failed, skipped)
- Test suite details
- Test case details
- Error messages for failed tests

## NEPI Testing Framework

The NEPI Testing Framework extends the standard framework with NEPI-specific capabilities. It is designed to test the emergent intelligence aspects of the Comphyon system. See the [NEPI Testing Framework README](nepi/README.md) for more details.

### Running NEPI Tests

To run the NEPI tests:

```bash
node testing/nepi/run-nepi-tests.js
```

## Running All Tests

To run both standard and NEPI tests:

```bash
node testing/run-all-tests.js
```

## Integration

The NEPI Testing Framework is integrated with the standard framework to provide a comprehensive testing solution. See the [Integration Documentation](INTEGRATION.md) for more details on how the two frameworks are integrated.

## Test Reports

All test reports are generated in the `reports/` directory. The reports are HTML files that can be opened in any web browser.

## Continuous Testing

Testing the Comphyon system is not a one-time event. It must be a continuous process integrated into the development lifecycle:

1. **Automated Regression Testing**: Run core test suites automatically after any code changes or updates.
2. **Real-Time Performance Monitoring**: Continuously monitor key performance indicators (KPIs) and coherence metrics in production.
3. **Anomaly Detection in Test Results**: Implement systems to automatically detect unexpected patterns or deviations in test outcomes.
4. **Learning from Operational Data**: Feed insights from real-world operational data and incidents back into the testing framework to create new test cases.

## Conclusion

The Comphyon Testing Framework provides a comprehensive approach to testing the Comphyon system. It ensures that the system not only functions as designed but also adheres to its core principles of coherence, safety, and ethical alignment as it learns and evolves. This comprehensive testing framework is essential for building a trustworthy and resilient Comphyon system.
