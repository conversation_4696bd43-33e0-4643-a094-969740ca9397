/**
 * Key Backup Utility for NovaConnect Universal API Connector
 * 
 * This utility provides secure backup capabilities for encryption keys.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { encrypt, decrypt } = require('./encryption');
const winston = require('winston');

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'key-backup' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ 
      filename: path.join(process.env.LOG_DIR || 'logs', 'key-backup.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 10,
      tailable: true
    })
  ]
});

// Configuration
const KEY_DIRECTORY = process.env.KEY_DIRECTORY || path.join(__dirname, '../.keys');
const BACKUP_DIRECTORY = process.env.BACKUP_DIRECTORY || path.join(__dirname, '../.backups');
const MASTER_KEY_FILE = path.join(KEY_DIRECTORY, 'master.key');
const KEYS_FILE = path.join(KEY_DIRECTORY, 'keys.json');
const BACKUP_PASSWORD_FILE = path.join(KEY_DIRECTORY, 'backup-password.key');
const BACKUP_FREQUENCY = process.env.BACKUP_FREQUENCY || 'daily'; // 'hourly', 'daily', 'weekly'

/**
 * Generate a secure backup password
 * 
 * @returns {string} - The backup password
 */
function generateBackupPassword() {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Create a backup of encryption keys
 * 
 * @param {string} backupPassword - The password to encrypt the backup
 * @returns {Object} - Backup information
 */
function createBackup(backupPassword) {
  try {
    // Create backup directory if it doesn't exist
    if (!fs.existsSync(BACKUP_DIRECTORY)) {
      fs.mkdirSync(BACKUP_DIRECTORY, { recursive: true, mode: 0o700 });
    }
    
    // Read master key
    if (!fs.existsSync(MASTER_KEY_FILE)) {
      throw new Error('Master key file not found');
    }
    
    const masterKey = fs.readFileSync(MASTER_KEY_FILE, 'utf8');
    
    // Read keys file
    if (!fs.existsSync(KEYS_FILE)) {
      throw new Error('Keys file not found');
    }
    
    const keysData = fs.readFileSync(KEYS_FILE, 'utf8');
    
    // Create backup data
    const backupData = {
      timestamp: new Date().toISOString(),
      masterKey,
      keysData
    };
    
    // Encrypt backup data
    const encryptedBackup = encrypt(backupData, backupPassword, { deriveKey: true });
    
    // Generate backup filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFilename = `keys-backup-${timestamp}.enc`;
    const backupPath = path.join(BACKUP_DIRECTORY, backupFilename);
    
    // Write backup file
    fs.writeFileSync(backupPath, JSON.stringify(encryptedBackup), { mode: 0o600 });
    
    // Create backup manifest
    const manifestPath = path.join(BACKUP_DIRECTORY, 'manifest.json');
    let manifest = [];
    
    if (fs.existsSync(manifestPath)) {
      try {
        manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
      } catch (error) {
        logger.warn('Could not parse backup manifest, creating new one', { error: error.message });
      }
    }
    
    // Add backup to manifest
    manifest.push({
      filename: backupFilename,
      timestamp: backupData.timestamp,
      size: fs.statSync(backupPath).size
    });
    
    // Sort manifest by timestamp (newest first)
    manifest.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    // Keep only the last 10 backups in the manifest
    if (manifest.length > 10) {
      const removedBackups = manifest.splice(10);
      
      // Remove old backup files
      for (const backup of removedBackups) {
        const oldBackupPath = path.join(BACKUP_DIRECTORY, backup.filename);
        if (fs.existsSync(oldBackupPath)) {
          fs.unlinkSync(oldBackupPath);
          logger.info(`Removed old backup: ${backup.filename}`);
        }
      }
    }
    
    // Write manifest
    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2), { mode: 0o600 });
    
    logger.info('Backup created successfully', { 
      filename: backupFilename,
      timestamp: backupData.timestamp
    });
    
    return {
      filename: backupFilename,
      path: backupPath,
      timestamp: backupData.timestamp,
      size: fs.statSync(backupPath).size
    };
  } catch (error) {
    logger.error('Error creating backup', { error: error.message });
    throw error;
  }
}

/**
 * Restore encryption keys from a backup
 * 
 * @param {string} backupFile - The backup file to restore
 * @param {string} backupPassword - The password to decrypt the backup
 * @returns {boolean} - Whether the restore was successful
 */
function restoreFromBackup(backupFile, backupPassword) {
  try {
    // Check if backup file exists
    const backupPath = path.isAbsolute(backupFile) 
      ? backupFile 
      : path.join(BACKUP_DIRECTORY, backupFile);
    
    if (!fs.existsSync(backupPath)) {
      throw new Error(`Backup file not found: ${backupPath}`);
    }
    
    // Read backup file
    const encryptedBackup = JSON.parse(fs.readFileSync(backupPath, 'utf8'));
    
    // Decrypt backup data
    const backupData = decrypt(encryptedBackup, backupPassword, { deriveKey: true, parseJson: true });
    
    // Create backup of current keys
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    if (fs.existsSync(MASTER_KEY_FILE)) {
      fs.copyFileSync(MASTER_KEY_FILE, `${MASTER_KEY_FILE}.${timestamp}.bak`);
    }
    
    if (fs.existsSync(KEYS_FILE)) {
      fs.copyFileSync(KEYS_FILE, `${KEYS_FILE}.${timestamp}.bak`);
    }
    
    // Restore master key
    fs.writeFileSync(MASTER_KEY_FILE, backupData.masterKey, { mode: 0o600 });
    
    // Restore keys file
    fs.writeFileSync(KEYS_FILE, backupData.keysData, { mode: 0o600 });
    
    logger.info('Keys restored successfully', { 
      backupFile,
      timestamp: backupData.timestamp
    });
    
    return true;
  } catch (error) {
    logger.error('Error restoring from backup', { error: error.message });
    throw error;
  }
}

/**
 * List available backups
 * 
 * @returns {Array} - List of available backups
 */
function listBackups() {
  try {
    const manifestPath = path.join(BACKUP_DIRECTORY, 'manifest.json');
    
    if (!fs.existsSync(manifestPath)) {
      return [];
    }
    
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    return manifest;
  } catch (error) {
    logger.error('Error listing backups', { error: error.message });
    throw error;
  }
}

/**
 * Verify a backup
 * 
 * @param {string} backupFile - The backup file to verify
 * @param {string} backupPassword - The password to decrypt the backup
 * @returns {Object} - Verification result
 */
function verifyBackup(backupFile, backupPassword) {
  try {
    // Check if backup file exists
    const backupPath = path.isAbsolute(backupFile) 
      ? backupFile 
      : path.join(BACKUP_DIRECTORY, backupFile);
    
    if (!fs.existsSync(backupPath)) {
      throw new Error(`Backup file not found: ${backupPath}`);
    }
    
    // Read backup file
    const encryptedBackup = JSON.parse(fs.readFileSync(backupPath, 'utf8'));
    
    // Try to decrypt backup data
    const backupData = decrypt(encryptedBackup, backupPassword, { deriveKey: true, parseJson: true });
    
    // Verify backup data
    if (!backupData.masterKey || !backupData.keysData || !backupData.timestamp) {
      throw new Error('Invalid backup data');
    }
    
    // Try to parse keys data
    const keysData = JSON.parse(backupData.keysData);
    
    return {
      valid: true,
      timestamp: backupData.timestamp,
      keys: {
        hasMasterKey: !!backupData.masterKey,
        hasKeysData: !!backupData.keysData
      }
    };
  } catch (error) {
    logger.error('Error verifying backup', { error: error.message });
    
    return {
      valid: false,
      error: error.message
    };
  }
}

/**
 * Initialize backup system
 */
function initializeBackup() {
  try {
    // Create backup directory if it doesn't exist
    if (!fs.existsSync(BACKUP_DIRECTORY)) {
      fs.mkdirSync(BACKUP_DIRECTORY, { recursive: true, mode: 0o700 });
    }
    
    // Generate backup password if it doesn't exist
    if (!fs.existsSync(BACKUP_PASSWORD_FILE)) {
      const backupPassword = generateBackupPassword();
      fs.writeFileSync(BACKUP_PASSWORD_FILE, backupPassword, { mode: 0o600 });
      
      logger.info('Backup password generated');
    }
    
    // Schedule regular backups
    const backupPassword = fs.readFileSync(BACKUP_PASSWORD_FILE, 'utf8');
    
    // Create initial backup
    createBackup(backupPassword);
    
    // Schedule regular backups
    let interval;
    
    switch (BACKUP_FREQUENCY) {
      case 'hourly':
        interval = 60 * 60 * 1000; // 1 hour
        break;
      case 'weekly':
        interval = 7 * 24 * 60 * 60 * 1000; // 1 week
        break;
      case 'daily':
      default:
        interval = 24 * 60 * 60 * 1000; // 1 day
        break;
    }
    
    setInterval(() => {
      try {
        createBackup(backupPassword);
      } catch (error) {
        logger.error('Error creating scheduled backup', { error: error.message });
      }
    }, interval);
    
    logger.info(`Scheduled regular backups (${BACKUP_FREQUENCY})`);
    
    return true;
  } catch (error) {
    logger.error('Error initializing backup system', { error: error.message });
    throw error;
  }
}

module.exports = {
  createBackup,
  restoreFromBackup,
  listBackups,
  verifyBackup,
  initializeBackup
};
