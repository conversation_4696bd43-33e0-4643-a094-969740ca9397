/**
 * Compliance Metrics Chart Component
 * 
 * This component displays detailed compliance metrics across frameworks.
 */

import React from 'react';

export const ComplianceMetricsChart: React.FC = () => {
  // Mock data for the chart
  const metrics = [
    { category: 'Access Control', implemented: 85, partial: 10, notImplemented: 5 },
    { category: 'Data Protection', implemented: 70, partial: 20, notImplemented: 10 },
    { category: 'Risk Management', implemented: 60, partial: 30, notImplemented: 10 },
    { category: 'Incident Response', implemented: 75, partial: 15, notImplemented: 10 },
    { category: 'Business Continuity', implemented: 65, partial: 25, notImplemented: 10 },
    { category: 'Vendor Management', implemented: 50, partial: 30, notImplemented: 20 },
  ];
  
  return (
    <div className="space-y-6">
      {metrics.map((metric, index) => (
        <div key={index} className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="font-medium">{metric.category}</span>
            <span className="text-sm text-gray-500">
              {metric.implemented}% Implemented
            </span>
          </div>
          <div className="h-4 flex rounded-full overflow-hidden">
            <div 
              className="bg-green-500" 
              style={{ width: `${metric.implemented}%` }}
            />
            <div 
              className="bg-yellow-400" 
              style={{ width: `${metric.partial}%` }}
            />
            <div 
              className="bg-red-500" 
              style={{ width: `${metric.notImplemented}%` }}
            />
          </div>
          <div className="flex text-xs text-gray-600 justify-between">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-1" />
              <span>Implemented</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-yellow-400 rounded-full mr-1" />
              <span>Partial</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-500 rounded-full mr-1" />
              <span>Not Implemented</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ComplianceMetricsChart;
