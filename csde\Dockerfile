# CSDE Testing Dockerfile
# Incorporates Orion's recommendations for GPU acceleration

# Use Python base image instead of CUDA for compatibility
FROM python:3.9-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC
ENV UUFT_USE_GPU=True

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    python3 \
    python3-pip \
    python3-dev \
    build-essential \
    git \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Install Python dependencies
COPY csde_requirements.txt .
RUN pip3 install --no-cache-dir -r csde_requirements.txt

# Skip CuPy installation for CPU-only testing
# We'll use TensorFlow for tensor operations instead

# Copy application code
COPY csde/ /app/csde/
COPY run_csde_tests.py /app/

# Copy configuration files
COPY uuft_config.yaml /app/uuft_config.yaml
COPY config_loader.py /app/config_loader.py

# Create results directory
RUN mkdir -p /app/results

# Set entrypoint
ENTRYPOINT ["python3", "run_csde_tests.py"]

# Default command
CMD ["--use-gpu", "--output-dir", "results", "--domain", "cybersecurity"]
