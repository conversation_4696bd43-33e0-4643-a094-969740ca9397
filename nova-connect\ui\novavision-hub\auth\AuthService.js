/**
 * Authentication Service Interface
 * 
 * This module defines the interface for authentication services.
 */

/**
 * Authentication service interface
 * 
 * @interface
 */
class AuthService {
  /**
   * Get current user
   * 
   * @returns {Promise<Object|null>} Current user or null if not authenticated
   */
  async getCurrentUser() {
    throw new Error('Method not implemented');
  }
  
  /**
   * Sign in with email and password
   * 
   * @param {string} email - Email
   * @param {string} password - Password
   * @returns {Promise<Object>} User
   */
  async signInWithEmailAndPassword(email, password) {
    throw new Error('Method not implemented');
  }
  
  /**
   * Sign in with provider
   * 
   * @param {string} provider - Provider name
   * @returns {Promise<Object>} User
   */
  async signInWithProvider(provider) {
    throw new Error('Method not implemented');
  }
  
  /**
   * Sign up with email and password
   * 
   * @param {string} email - Email
   * @param {string} password - Password
   * @param {Object} [userData] - Additional user data
   * @returns {Promise<Object>} User
   */
  async signUpWithEmailAndPassword(email, password, userData = {}) {
    throw new Error('Method not implemented');
  }
  
  /**
   * Sign out
   * 
   * @returns {Promise<void>}
   */
  async signOut() {
    throw new Error('Method not implemented');
  }
  
  /**
   * Reset password
   * 
   * @param {string} email - Email
   * @returns {Promise<void>}
   */
  async resetPassword(email) {
    throw new Error('Method not implemented');
  }
  
  /**
   * Update user profile
   * 
   * @param {Object} user - User
   * @param {Object} data - Profile data
   * @returns {Promise<Object>} Updated user
   */
  async updateProfile(user, data) {
    throw new Error('Method not implemented');
  }
  
  /**
   * Change password
   * 
   * @param {Object} user - User
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise<void>}
   */
  async changePassword(user, currentPassword, newPassword) {
    throw new Error('Method not implemented');
  }
  
  /**
   * Get user token
   * 
   * @param {Object} user - User
   * @returns {Promise<string>} Token
   */
  async getToken(user) {
    throw new Error('Method not implemented');
  }
  
  /**
   * Set up auth state change listener
   * 
   * @param {Function} callback - Callback function
   * @returns {Function} Unsubscribe function
   */
  onAuthStateChanged(callback) {
    throw new Error('Method not implemented');
  }
}

export default AuthService;
