b964d4e3008de3f1b76654df811e0457
/**
 * Audit Service
 *
 * This service handles audit logging for tracking user actions.
 * It supports both local file storage and Google BigQuery integration.
 */

const fs = require('fs').promises;
const path = require('path');
const {
  v4: uuidv4
} = require('uuid');

// Import BigQuery if available
let BigQuery;
try {
  BigQuery = require('@google-cloud/bigquery').BigQuery;
} catch (error) {
  // BigQuery package not available, will use local storage only
  console.log('BigQuery package not available, using local storage only');
}
class AuditService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.auditDir = path.join(this.dataDir, 'audit');
    this.auditFile = path.join(this.auditDir, 'audit_log.json');
    this.retentionDays = 90; // Keep audit logs for 90 days

    // Initialize BigQuery if available
    this.bigQueryEnabled = process.env.BIGQUERY_ENABLED === 'true';
    this.projectId = process.env.GCP_PROJECT_ID;
    this.datasetId = process.env.BIGQUERY_DATASET_ID || 'novafuse_audit';
    this.tableId = process.env.BIGQUERY_TABLE_ID || 'events';
    if (this.bigQueryEnabled && this.projectId && BigQuery) {
      try {
        this.bigquery = new BigQuery({
          projectId: this.projectId
        });
        console.log('BigQuery integration enabled');
      } catch (error) {
        console.error('Error initializing BigQuery:', error);
      }
    }
    this.ensureDataDir();

    // Clean up old audit logs once a day
    setInterval(() => this.cleanupOldLogs(), 24 * 60 * 60 * 1000);
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.auditDir, {
        recursive: true
      });

      // Initialize audit log file if it doesn't exist
      try {
        await fs.access(this.auditFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          // File doesn't exist, create it with empty array
          await fs.writeFile(this.auditFile, JSON.stringify([]));
        } else {
          throw error;
        }
      }
    } catch (error) {
      console.error('Error creating audit directory:', error);
      throw error;
    }
  }

  /**
   * Load audit logs from file
   */
  async loadAuditLogs() {
    try {
      const data = await fs.readFile(this.auditFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error('Error loading audit logs:', error);
      throw error;
    }
  }

  /**
   * Save audit logs to file
   */
  async saveAuditLogs(logs) {
    try {
      await fs.writeFile(this.auditFile, JSON.stringify(logs, null, 2));
    } catch (error) {
      console.error('Error saving audit logs:', error);
      throw error;
    }
  }

  /**
   * Log an audit event
   */
  async logEvent(data) {
    try {
      // Create audit log entry
      const logEntry = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        userId: data.userId || null,
        action: data.action,
        resourceType: data.resourceType,
        resourceId: data.resourceId,
        details: data.details || null,
        ip: data.ip || null,
        userAgent: data.userAgent || null,
        status: data.status || 'success',
        teamId: data.teamId || null,
        environmentId: data.environmentId || null,
        tenantId: data.tenantId || null
      };

      // Log to BigQuery if enabled
      if (this.bigQueryEnabled && this.bigquery) {
        try {
          await this.logToBigQuery(logEntry);
        } catch (bigQueryError) {
          console.error('Error logging to BigQuery:', bigQueryError);
          // Continue with local logging even if BigQuery fails
        }
      }

      // Log to local file
      const auditLogs = await this.loadAuditLogs();
      auditLogs.push(logEntry);

      // Limit the size of the audit logs
      if (auditLogs.length > 10000) {
        auditLogs.splice(0, auditLogs.length - 10000);
      }
      await this.saveAuditLogs(auditLogs);
      return logEntry;
    } catch (error) {
      console.error('Error logging audit event:', error);
      // Don't throw error to prevent affecting the main request flow
    }
  }

  /**
   * Log a tenant-specific audit event
   */
  async logTenantEvent(tenantId, data) {
    try {
      // Add tenant ID to data
      const tenantData = {
        ...data,
        tenantId
      };

      // Log the event
      const logEntry = await this.logEvent(tenantData);

      // If BigQuery is enabled, also log to tenant-specific table
      if (this.bigQueryEnabled && this.bigquery && tenantId) {
        try {
          // Get tenant-specific dataset
          const dataset = this.bigquery.dataset(`tenant_${tenantId}`);

          // Get audit table (create if it doesn't exist)
          let table;
          try {
            table = dataset.table('audit_logs');
            await table.get();
          } catch (tableError) {
            // Table doesn't exist, create it
            const schema = [{
              name: 'id',
              type: 'STRING'
            }, {
              name: 'timestamp',
              type: 'TIMESTAMP'
            }, {
              name: 'userId',
              type: 'STRING'
            }, {
              name: 'action',
              type: 'STRING'
            }, {
              name: 'resourceType',
              type: 'STRING'
            }, {
              name: 'resourceId',
              type: 'STRING'
            }, {
              name: 'details',
              type: 'STRING'
            }, {
              name: 'ip',
              type: 'STRING'
            }, {
              name: 'userAgent',
              type: 'STRING'
            }, {
              name: 'status',
              type: 'STRING'
            }, {
              name: 'teamId',
              type: 'STRING'
            }, {
              name: 'environmentId',
              type: 'STRING'
            }, {
              name: 'tenantId',
              type: 'STRING'
            }];
            const options = {
              schema: schema,
              timePartitioning: {
                type: 'DAY',
                field: 'timestamp'
              }
            };
            await dataset.createTable('audit_logs', options);
            table = dataset.table('audit_logs');
          }

          // Insert into tenant-specific table
          await table.insert([logEntry]);
        } catch (bigQueryError) {
          console.error(`Error logging to tenant-specific BigQuery table for tenant ${tenantId}:`, bigQueryError);
        }
      }
      return logEntry;
    } catch (error) {
      console.error('Error logging tenant audit event:', error);
      // Don't throw error to prevent affecting the main request flow
    }
  }

  /**
   * Log an audit event to BigQuery
   */
  async logToBigQuery(event) {
    try {
      // Get dataset reference
      const dataset = this.bigquery.dataset(this.datasetId);

      // Get table reference
      const table = dataset.table(this.tableId);

      // Insert row
      await table.insert([event]);
    } catch (error) {
      console.error('Error logging to BigQuery:', error);
      throw error;
    }
  }

  /**
   * Get audit logs
   */
  async getAuditLogs(filters = {}) {
    const auditLogs = await this.loadAuditLogs();

    // Apply filters
    let filteredLogs = auditLogs;
    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= startDate);
    }
    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= endDate);
    }
    if (filters.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
    }
    if (filters.action) {
      filteredLogs = filteredLogs.filter(log => log.action === filters.action);
    }
    if (filters.resourceType) {
      filteredLogs = filteredLogs.filter(log => log.resourceType === filters.resourceType);
    }
    if (filters.resourceId) {
      filteredLogs = filteredLogs.filter(log => log.resourceId === filters.resourceId);
    }
    if (filters.status) {
      filteredLogs = filteredLogs.filter(log => log.status === filters.status);
    }
    if (filters.teamId) {
      filteredLogs = filteredLogs.filter(log => log.teamId === filters.teamId);
    }
    if (filters.environmentId) {
      filteredLogs = filteredLogs.filter(log => log.environmentId === filters.environmentId);
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Apply pagination
    const page = filters.page || 1;
    const limit = filters.limit || 100;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);
    return {
      logs: paginatedLogs,
      total: filteredLogs.length,
      page,
      limit,
      totalPages: Math.ceil(filteredLogs.length / limit)
    };
  }

  /**
   * Get audit log by ID
   */
  async getAuditLogById(id) {
    const auditLogs = await this.loadAuditLogs();
    const log = auditLogs.find(log => log.id === id);
    if (!log) {
      throw new Error(`Audit log with ID ${id} not found`);
    }
    return log;
  }

  /**
   * Get audit logs for a resource
   */
  async getAuditLogsForResource(resourceType, resourceId) {
    const auditLogs = await this.loadAuditLogs();

    // Filter logs for the resource
    const resourceLogs = auditLogs.filter(log => log.resourceType === resourceType && log.resourceId === resourceId);

    // Sort by timestamp (newest first)
    resourceLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    return resourceLogs;
  }

  /**
   * Get audit logs for a user
   */
  async getAuditLogsForUser(userId) {
    const auditLogs = await this.loadAuditLogs();

    // Filter logs for the user
    const userLogs = auditLogs.filter(log => log.userId === userId);

    // Sort by timestamp (newest first)
    userLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    return userLogs;
  }

  /**
   * Get audit logs for a team
   */
  async getAuditLogsForTeam(teamId) {
    const auditLogs = await this.loadAuditLogs();

    // Filter logs for the team
    const teamLogs = auditLogs.filter(log => log.teamId === teamId);

    // Sort by timestamp (newest first)
    teamLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    return teamLogs;
  }

  /**
   * Clean up old audit logs
   */
  async cleanupOldLogs() {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);
      const auditLogs = await this.loadAuditLogs();
      const filteredLogs = auditLogs.filter(log => new Date(log.timestamp) >= cutoffDate);
      if (filteredLogs.length < auditLogs.length) {
        await this.saveAuditLogs(filteredLogs);
        console.log(`Cleaned up audit logs older than ${cutoffDate.toISOString()}`);
      }
    } catch (error) {
      console.error('Error cleaning up old audit logs:', error);
    }
  }

  /**
   * Create audit middleware
   */
  createAuditMiddleware() {
    return (req, res, next) => {
      // Store original end method
      const originalEnd = res.end;

      // Override end method to capture response
      res.end = function (chunk, encoding) {
        // Restore original end method
        res.end = originalEnd;

        // Call original end method
        res.end(chunk, encoding);

        // Skip audit logging for certain paths
        if (req.path.startsWith('/health') || req.path.startsWith('/api/monitoring/health')) {
          return;
        }

        // Log audit event
        const auditData = {
          userId: req.user ? req.user.id : null,
          action: req.method,
          resourceType: req.path.split('/')[2] || 'unknown',
          resourceId: req.params.id || null,
          details: {
            path: req.path,
            query: req.query,
            body: req.method !== 'GET' ? req.body : null
          },
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          status: res.statusCode >= 400 ? 'failure' : 'success',
          teamId: req.headers['x-team-id'] || null,
          environmentId: req.headers['x-environment-id'] || null,
          tenantId: req.headers['x-tenant-id'] || null
        };

        // If tenant ID is present, use tenant-specific logging
        if (req.headers['x-tenant-id']) {
          this.logTenantEvent(req.headers['x-tenant-id'], auditData);
        } else {
          this.logEvent(auditData);
        }
      };
      next();
    };
  }
}
module.exports = AuditService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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