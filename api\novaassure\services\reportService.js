/**
 * Report Service
 * 
 * This service provides functionality for report generation.
 */

const { Report, TestPlan, TestExecution, Control } = require('../models');
const logger = require('../utils/logger');
const fs = require('fs');
const path = require('path');

/**
 * Get all reports
 * @param {Object} filters - Filters
 * @param {number} page - Page number
 * @param {number} limit - Items per page
 * @returns {Promise<Object>} - Reports with pagination
 */
async function getAllReports(filters = {}, page = 1, limit = 10) {
  try {
    // Build query
    const query = {};
    
    if (filters.type) {
      query.type = filters.type;
    }
    
    if (filters.framework) {
      query.framework = filters.framework;
    }
    
    if (filters.search) {
      query.$text = { $search: filters.search };
    }
    
    // Count total
    const total = await Report.countDocuments(query);
    
    // Get reports
    const reports = await Report.find(query)
      .populate('testPlan')
      .populate('testExecution')
      .populate('control')
      .populate('createdBy')
      .skip((page - 1) * limit)
      .limit(limit)
      .sort({ createdAt: -1 });
    
    return {
      reports,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error('Failed to get reports', error);
    throw error;
  }
}

/**
 * Get report by ID
 * @param {string} id - Report ID
 * @returns {Promise<Object>} - Report object
 */
async function getReportById(id) {
  try {
    const report = await Report.getById(id);
    
    if (!report) {
      throw new Error('Report not found');
    }
    
    return report;
  } catch (error) {
    logger.error(`Failed to get report ${id}`, error);
    throw error;
  }
}

/**
 * Generate compliance report
 * @param {Object} reportData - Report data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Generated report
 */
async function generateComplianceReport(reportData, userId) {
  try {
    // Validate framework
    if (!reportData.framework) {
      throw new Error('Framework is required');
    }
    
    // Create report
    const report = new Report({
      name: reportData.name,
      description: reportData.description || `Compliance report for ${reportData.framework}`,
      type: 'compliance',
      framework: reportData.framework,
      startDate: reportData.startDate,
      endDate: reportData.endDate,
      format: reportData.format || 'pdf',
      status: 'generating',
      createdBy: userId,
      updatedBy: userId
    });
    
    // Save report
    await report.save();
    
    // Generate report asynchronously
    generateComplianceReportFile(report._id)
      .catch(error => {
        logger.error(`Failed to generate compliance report ${report._id}`, error);
      });
    
    logger.info(`Compliance report ${report._id} generation started by ${userId}`);
    
    return report;
  } catch (error) {
    logger.error('Failed to generate compliance report', error);
    throw error;
  }
}

/**
 * Generate test results report
 * @param {Object} reportData - Report data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Generated report
 */
async function generateTestResultsReport(reportData, userId) {
  try {
    // Validate test plan
    if (!reportData.testPlanId) {
      throw new Error('Test plan ID is required');
    }
    
    const testPlan = await TestPlan.findById(reportData.testPlanId);
    
    if (!testPlan) {
      throw new Error(`Test plan ${reportData.testPlanId} not found`);
    }
    
    // Validate test execution if provided
    if (reportData.testExecutionId) {
      const testExecution = await TestExecution.findById(reportData.testExecutionId);
      
      if (!testExecution) {
        throw new Error(`Test execution ${reportData.testExecutionId} not found`);
      }
    }
    
    // Create report
    const report = new Report({
      name: reportData.name,
      description: reportData.description || `Test results report for ${testPlan.name}`,
      type: 'test-results',
      testPlan: reportData.testPlanId,
      testExecution: reportData.testExecutionId,
      startDate: reportData.startDate,
      endDate: reportData.endDate,
      format: reportData.format || 'pdf',
      status: 'generating',
      createdBy: userId,
      updatedBy: userId
    });
    
    // Save report
    await report.save();
    
    // Generate report asynchronously
    generateTestResultsReportFile(report._id)
      .catch(error => {
        logger.error(`Failed to generate test results report ${report._id}`, error);
      });
    
    logger.info(`Test results report ${report._id} generation started by ${userId}`);
    
    return report;
  } catch (error) {
    logger.error('Failed to generate test results report', error);
    throw error;
  }
}

/**
 * Generate evidence report
 * @param {Object} reportData - Report data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Generated report
 */
async function generateEvidenceReport(reportData, userId) {
  try {
    // Validate control if provided
    if (reportData.controlId) {
      const control = await Control.findById(reportData.controlId);
      
      if (!control) {
        throw new Error(`Control ${reportData.controlId} not found`);
      }
    }
    
    // Validate test execution if provided
    if (reportData.testExecutionId) {
      const testExecution = await TestExecution.findById(reportData.testExecutionId);
      
      if (!testExecution) {
        throw new Error(`Test execution ${reportData.testExecutionId} not found`);
      }
    }
    
    // Create report
    const report = new Report({
      name: reportData.name,
      description: reportData.description || 'Evidence report',
      type: 'evidence',
      control: reportData.controlId,
      testExecution: reportData.testExecutionId,
      startDate: reportData.startDate,
      endDate: reportData.endDate,
      format: reportData.format || 'pdf',
      status: 'generating',
      createdBy: userId,
      updatedBy: userId
    });
    
    // Save report
    await report.save();
    
    // Generate report asynchronously
    generateEvidenceReportFile(report._id)
      .catch(error => {
        logger.error(`Failed to generate evidence report ${report._id}`, error);
      });
    
    logger.info(`Evidence report ${report._id} generation started by ${userId}`);
    
    return report;
  } catch (error) {
    logger.error('Failed to generate evidence report', error);
    throw error;
  }
}

/**
 * Generate control effectiveness report
 * @param {Object} reportData - Report data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Generated report
 */
async function generateControlEffectivenessReport(reportData, userId) {
  try {
    // Validate framework
    if (!reportData.framework) {
      throw new Error('Framework is required');
    }
    
    // Create report
    const report = new Report({
      name: reportData.name,
      description: reportData.description || `Control effectiveness report for ${reportData.framework}`,
      type: 'control-effectiveness',
      framework: reportData.framework,
      startDate: reportData.startDate,
      endDate: reportData.endDate,
      format: reportData.format || 'pdf',
      status: 'generating',
      createdBy: userId,
      updatedBy: userId
    });
    
    // Save report
    await report.save();
    
    // Generate report asynchronously
    generateControlEffectivenessReportFile(report._id)
      .catch(error => {
        logger.error(`Failed to generate control effectiveness report ${report._id}`, error);
      });
    
    logger.info(`Control effectiveness report ${report._id} generation started by ${userId}`);
    
    return report;
  } catch (error) {
    logger.error('Failed to generate control effectiveness report', error);
    throw error;
  }
}

/**
 * Download report
 * @param {string} id - Report ID
 * @returns {Promise<Object>} - Report file information
 */
async function downloadReport(id) {
  try {
    // Get report
    const report = await Report.getById(id);
    
    if (!report) {
      throw new Error('Report not found');
    }
    
    if (report.status !== 'completed') {
      throw new Error('Report is not ready for download');
    }
    
    if (!report.filePath || !fs.existsSync(report.filePath)) {
      throw new Error('Report file not found');
    }
    
    return {
      filePath: report.filePath,
      fileName: path.basename(report.filePath),
      fileType: getContentType(report.format)
    };
  } catch (error) {
    logger.error(`Failed to download report ${id}`, error);
    throw error;
  }
}

/**
 * Share report
 * @param {string} id - Report ID
 * @param {string[]} recipients - Recipients
 * @param {string} [message] - Message
 * @param {Date} [expirationDate] - Expiration date
 * @returns {Promise<Object>} - Share result
 */
async function shareReport(id, recipients, message = '', expirationDate = null) {
  try {
    // Share report
    const shareResult = await Report.share(id, recipients, message, expirationDate);
    
    logger.info(`Report ${id} shared with ${recipients.join(', ')}`);
    
    return shareResult;
  } catch (error) {
    logger.error(`Failed to share report ${id}`, error);
    throw error;
  }
}

/**
 * Schedule report
 * @param {string} id - Report ID
 * @param {Object} schedule - Schedule data
 * @param {string[]} [recipients] - Recipients
 * @returns {Promise<Object>} - Updated report
 */
async function scheduleReport(id, schedule, recipients = []) {
  try {
    // Get report
    const report = await Report.getById(id);
    
    if (!report) {
      throw new Error('Report not found');
    }
    
    // Update report
    report.schedule = {
      ...schedule,
      recipients
    };
    
    // Save report
    await report.save();
    
    logger.info(`Report ${id} scheduled`);
    
    return report;
  } catch (error) {
    logger.error(`Failed to schedule report ${id}`, error);
    throw error;
  }
}

/**
 * Get content type for report format
 * @param {string} format - Report format
 * @returns {string} - Content type
 */
function getContentType(format) {
  switch (format) {
    case 'pdf':
      return 'application/pdf';
    case 'html':
      return 'text/html';
    case 'json':
      return 'application/json';
    case 'csv':
      return 'text/csv';
    default:
      return 'application/octet-stream';
  }
}

/**
 * Generate compliance report file
 * @param {string} reportId - Report ID
 * @returns {Promise<void>}
 */
async function generateComplianceReportFile(reportId) {
  try {
    // Get report
    const report = await Report.findById(reportId);
    
    if (!report) {
      throw new Error('Report not found');
    }
    
    // In a real implementation, this would generate a report file
    // For this placeholder, we'll just create a dummy file
    
    // Create reports directory if it doesn't exist
    const reportsDir = path.join(__dirname, '../../reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    // Create report file
    const fileName = `compliance_report_${report._id}_${Date.now()}.${report.format}`;
    const filePath = path.join(reportsDir, fileName);
    
    // Write dummy content
    const content = `Compliance Report
Name: ${report.name}
Description: ${report.description}
Framework: ${report.framework}
Generated: ${new Date().toISOString()}
`;
    
    fs.writeFileSync(filePath, content);
    
    // Update report
    report.status = 'completed';
    report.filePath = filePath;
    report.fileSize = fs.statSync(filePath).size;
    
    await report.save();
    
    logger.info(`Compliance report ${reportId} generated`);
  } catch (error) {
    // Update report status to failed
    const report = await Report.findById(reportId);
    
    if (report) {
      report.status = 'failed';
      report.error = error.message;
      
      await report.save();
    }
    
    logger.error(`Failed to generate compliance report ${reportId}`, error);
    throw error;
  }
}

/**
 * Generate test results report file
 * @param {string} reportId - Report ID
 * @returns {Promise<void>}
 */
async function generateTestResultsReportFile(reportId) {
  try {
    // Get report
    const report = await Report.findById(reportId)
      .populate('testPlan')
      .populate('testExecution');
    
    if (!report) {
      throw new Error('Report not found');
    }
    
    // In a real implementation, this would generate a report file
    // For this placeholder, we'll just create a dummy file
    
    // Create reports directory if it doesn't exist
    const reportsDir = path.join(__dirname, '../../reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    // Create report file
    const fileName = `test_results_report_${report._id}_${Date.now()}.${report.format}`;
    const filePath = path.join(reportsDir, fileName);
    
    // Write dummy content
    const content = `Test Results Report
Name: ${report.name}
Description: ${report.description}
Test Plan: ${report.testPlan ? report.testPlan.name : 'N/A'}
Test Execution: ${report.testExecution ? report.testExecution._id : 'N/A'}
Generated: ${new Date().toISOString()}
`;
    
    fs.writeFileSync(filePath, content);
    
    // Update report
    report.status = 'completed';
    report.filePath = filePath;
    report.fileSize = fs.statSync(filePath).size;
    
    await report.save();
    
    logger.info(`Test results report ${reportId} generated`);
  } catch (error) {
    // Update report status to failed
    const report = await Report.findById(reportId);
    
    if (report) {
      report.status = 'failed';
      report.error = error.message;
      
      await report.save();
    }
    
    logger.error(`Failed to generate test results report ${reportId}`, error);
    throw error;
  }
}

/**
 * Generate evidence report file
 * @param {string} reportId - Report ID
 * @returns {Promise<void>}
 */
async function generateEvidenceReportFile(reportId) {
  try {
    // Get report
    const report = await Report.findById(reportId)
      .populate('control')
      .populate('testExecution');
    
    if (!report) {
      throw new Error('Report not found');
    }
    
    // In a real implementation, this would generate a report file
    // For this placeholder, we'll just create a dummy file
    
    // Create reports directory if it doesn't exist
    const reportsDir = path.join(__dirname, '../../reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    // Create report file
    const fileName = `evidence_report_${report._id}_${Date.now()}.${report.format}`;
    const filePath = path.join(reportsDir, fileName);
    
    // Write dummy content
    const content = `Evidence Report
Name: ${report.name}
Description: ${report.description}
Control: ${report.control ? report.control.name : 'N/A'}
Test Execution: ${report.testExecution ? report.testExecution._id : 'N/A'}
Generated: ${new Date().toISOString()}
`;
    
    fs.writeFileSync(filePath, content);
    
    // Update report
    report.status = 'completed';
    report.filePath = filePath;
    report.fileSize = fs.statSync(filePath).size;
    
    await report.save();
    
    logger.info(`Evidence report ${reportId} generated`);
  } catch (error) {
    // Update report status to failed
    const report = await Report.findById(reportId);
    
    if (report) {
      report.status = 'failed';
      report.error = error.message;
      
      await report.save();
    }
    
    logger.error(`Failed to generate evidence report ${reportId}`, error);
    throw error;
  }
}

/**
 * Generate control effectiveness report file
 * @param {string} reportId - Report ID
 * @returns {Promise<void>}
 */
async function generateControlEffectivenessReportFile(reportId) {
  try {
    // Get report
    const report = await Report.findById(reportId);
    
    if (!report) {
      throw new Error('Report not found');
    }
    
    // In a real implementation, this would generate a report file
    // For this placeholder, we'll just create a dummy file
    
    // Create reports directory if it doesn't exist
    const reportsDir = path.join(__dirname, '../../reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    // Create report file
    const fileName = `control_effectiveness_report_${report._id}_${Date.now()}.${report.format}`;
    const filePath = path.join(reportsDir, fileName);
    
    // Write dummy content
    const content = `Control Effectiveness Report
Name: ${report.name}
Description: ${report.description}
Framework: ${report.framework}
Generated: ${new Date().toISOString()}
`;
    
    fs.writeFileSync(filePath, content);
    
    // Update report
    report.status = 'completed';
    report.filePath = filePath;
    report.fileSize = fs.statSync(filePath).size;
    
    await report.save();
    
    logger.info(`Control effectiveness report ${reportId} generated`);
  } catch (error) {
    // Update report status to failed
    const report = await Report.findById(reportId);
    
    if (report) {
      report.status = 'failed';
      report.error = error.message;
      
      await report.save();
    }
    
    logger.error(`Failed to generate control effectiveness report ${reportId}`, error);
    throw error;
  }
}

module.exports = {
  getAllReports,
  getReportById,
  generateComplianceReport,
  generateTestResultsReport,
  generateEvidenceReport,
  generateControlEffectivenessReport,
  downloadReport,
  shareReport,
  scheduleReport
};
