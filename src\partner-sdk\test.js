/**
 * NovaFuse Partner SDK - Test Script
 * 
 * This script tests the NovaFuse Partner SDK.
 */

const NovaFusePartnerSDK = require('./index');
const fs = require('fs');
const path = require('path');

// Create test partner configuration
const partnerConfig = {
  partnerId: 'TEST-PARTNER-001',
  partner<PERSON>ey: 'test-partner-api-key-123456',
  partnerName: 'Test Partner, Inc.',
  environment: 'sandbox'
};

// Create test client data
const clientData = {
  complianceData: {
    complianceScore: 0.65,
    controls: [
      {
        id: 'NIST-AC-2',
        name: 'Account Management',
        description: 'The organization needs to implement account management procedures',
        severity: 'high',
        status: 'non-compliant',
        framework: 'NIST 800-53'
      },
      {
        id: 'NIST-CM-7',
        name: 'Least Functionality',
        description: 'The organization needs to configure systems to provide only essential capabilities',
        severity: 'medium',
        status: 'partial',
        framework: 'NIST 800-53'
      },
      {
        id: 'NIST-SC-7',
        name: 'Boundary Protection',
        description: 'The organization needs to implement boundary protection mechanisms',
        severity: 'high',
        status: 'compliant',
        framework: 'NIST 800-53'
      }
    ]
  },
  gcpData: {
    integrationScore: 0.75,
    services: [
      {
        id: 'GCP-IAM-1',
        name: 'IAM Role Configuration',
        description: 'IAM roles need to be configured with least privilege',
        severity: 'high',
        status: 'non-optimal',
        service: 'Cloud IAM'
      },
      {
        id: 'GCP-VPC-1',
        name: 'VPC Network Security',
        description: 'VPC network security needs to be enhanced',
        severity: 'medium',
        status: 'partial',
        service: 'VPC Network'
      },
      {
        id: 'GCP-KMS-1',
        name: 'Key Management',
        description: 'Cloud KMS keys need to be properly managed',
        severity: 'high',
        status: 'optimal',
        service: 'Cloud KMS'
      }
    ]
  },
  cyberSafetyData: {
    safetyScore: 0.55,
    controls: [
      {
        id: 'CS-P3-1',
        name: 'Self-Destructing Compliance Servers',
        description: 'Implement self-destructing compliance servers with hardware-enforced geo-fencing',
        severity: 'high',
        status: 'not-implemented',
        pillar: 'Pillar 3'
      },
      {
        id: 'CS-P9-1',
        name: 'Post-Quantum Immutable Compliance Journal',
        description: 'Implement post-quantum immutable compliance journal',
        severity: 'medium',
        status: 'partial',
        pillar: 'Pillar 9'
      },
      {
        id: 'CS-P12-1',
        name: 'C-Suite Directive to Code Compiler',
        description: 'Implement C-Suite Directive to Code Compiler',
        severity: 'medium',
        status: 'implemented',
        pillar: 'Pillar 12'
      }
    ]
  },
  metadata: {
    clientId: 'TEST-CLIENT-001',
    scope: 'medium'
  }
};

// Create output directory
const outputDir = path.join(__dirname, 'test-output');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Run test
async function runTest() {
  try {
    console.log('Testing NovaFuse Partner SDK...');
    
    // Initialize SDK
    const sdk = new NovaFusePartnerSDK(partnerConfig);
    
    // Test analyze environment
    console.log('\nTesting analyzeEnvironment...');
    const analysisResult = await sdk.analyzeEnvironment(clientData);
    
    // Save analysis result
    fs.writeFileSync(
      path.join(outputDir, 'analysis_result.json'),
      JSON.stringify(analysisResult, null, 2)
    );
    
    console.log(`Analysis result saved to ${path.join(outputDir, 'analysis_result.json')}`);
    console.log(`CSDE Value: ${analysisResult.csdeValue.toFixed(2)}`);
    console.log(`Performance Factor: ${analysisResult.performanceFactor.toFixed(2)}x`);
    console.log(`Partner Revenue: $${analysisResult.partner.revenue.partnerRevenue.toFixed(2)}`);
    
    // Test remediate issues
    console.log('\nTesting remediateIssues...');
    const remediationResult = await sdk.remediateIssues(clientData, {
      confidenceThreshold: 0.7,
      automationLevels: ['high', 'medium'],
      priorityLevels: ['critical', 'high', 'medium'],
      dryRun: true
    });
    
    // Save remediation result
    fs.writeFileSync(
      path.join(outputDir, 'remediation_result.json'),
      JSON.stringify(remediationResult, null, 2)
    );
    
    console.log(`Remediation result saved to ${path.join(outputDir, 'remediation_result.json')}`);
    console.log(`Actions Selected: ${remediationResult.remediation.actionsSelected}`);
    console.log(`Actions Remediated: ${remediationResult.remediation.actionsRemediated}`);
    console.log(`Partner Revenue: $${remediationResult.partner.revenue.partnerRevenue.toFixed(2)}`);
    
    // Test generate report
    console.log('\nTesting generateReport...');
    const reportResult = await sdk.generateReport(clientData, {
      format: 'html',
      template: 'standard',
      includeRemediation: true
    });
    
    // Save report result
    fs.writeFileSync(
      path.join(outputDir, 'report_result.json'),
      JSON.stringify(reportResult, null, 2)
    );
    
    console.log(`Report result saved to ${path.join(outputDir, 'report_result.json')}`);
    console.log(`Report URL: ${reportResult.url}`);
    console.log(`Partner Revenue: $${reportResult.partner.revenue.partnerRevenue.toFixed(2)}`);
    
    // Test get revenue info
    console.log('\nTesting getRevenueInfo...');
    const revenueInfo = await sdk.getRevenueInfo();
    
    // Save revenue info
    fs.writeFileSync(
      path.join(outputDir, 'revenue_info.json'),
      JSON.stringify(revenueInfo, null, 2)
    );
    
    console.log(`Revenue info saved to ${path.join(outputDir, 'revenue_info.json')}`);
    console.log(`Total Revenue: $${revenueInfo.totalRevenue.toFixed(2)}`);
    console.log(`Total Transactions: ${revenueInfo.totalTransactions}`);
    
    // Test get usage info
    console.log('\nTesting getUsageInfo...');
    const usageInfo = await sdk.getUsageInfo();
    
    // Save usage info
    fs.writeFileSync(
      path.join(outputDir, 'usage_info.json'),
      JSON.stringify(usageInfo, null, 2)
    );
    
    console.log(`Usage info saved to ${path.join(outputDir, 'usage_info.json')}`);
    console.log(`Total Usage: ${usageInfo.totalUsage}`);
    
    // Generate HTML report
    generateHtmlReport({
      analysisResult,
      remediationResult,
      reportResult,
      revenueInfo,
      usageInfo
    });
    
    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error(`Error running test: ${error.message}`);
    console.error(error.stack);
  }
}

// Generate HTML report
function generateHtmlReport(results) {
  const reportHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse Partner SDK Test Results</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f8f9fa;
      color: #212529;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #0a84ff;
      color: white;
      padding: 20px;
      text-align: center;
      margin-bottom: 20px;
      border-radius: 5px;
    }
    .card {
      background-color: white;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      overflow: hidden;
    }
    .card-header {
      background-color: #0a84ff;
      color: white;
      padding: 15px 20px;
      font-weight: bold;
      font-size: 18px;
    }
    .card-body {
      padding: 20px;
    }
    .chart-container {
      height: 300px;
      margin-bottom: 20px;
    }
    .metrics {
      display: flex;
      flex-wrap: wrap;
      margin: 0 -10px;
    }
    .metric {
      flex: 1;
      min-width: 200px;
      margin: 10px;
      background-color: white;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 20px;
      text-align: center;
    }
    .metric-value {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
      color: #0a84ff;
    }
    .metric-label {
      font-size: 14px;
      color: #6c757d;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #e9ecef;
    }
    th {
      background-color: #f8f9fa;
      font-weight: bold;
    }
    tr:hover {
      background-color: #f8f9fa;
    }
    .footer {
      text-align: center;
      margin-top: 40px;
      padding: 20px;
      color: #6c757d;
      font-size: 14px;
    }
    .logo {
      text-align: center;
      margin-bottom: 20px;
    }
    .logo img {
      height: 60px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">
        <h1>NovaFuse</h1>
      </div>
      <h1>Partner SDK Test Results</h1>
      <p>Test Partner: ${partnerConfig.partnerName} (${partnerConfig.partnerId})</p>
    </div>
    
    <div class="card">
      <div class="card-header">Revenue Summary</div>
      <div class="card-body">
        <div class="metrics">
          <div class="metric">
            <div class="metric-label">Total Revenue</div>
            <div class="metric-value">$${results.revenueInfo.totalRevenue.toFixed(2)}</div>
          </div>
          <div class="metric">
            <div class="metric-label">Total Transactions</div>
            <div class="metric-value">${results.revenueInfo.totalTransactions}</div>
          </div>
          <div class="metric">
            <div class="metric-label">Average Transaction</div>
            <div class="metric-value">$${results.revenueInfo.averageTransactionValue.toFixed(2)}</div>
          </div>
          <div class="metric">
            <div class="metric-label">Revenue Share</div>
            <div class="metric-value">${results.revenueInfo.revenueShare * 100}%</div>
          </div>
        </div>
        
        <div class="chart-container">
          <canvas id="revenueChart"></canvas>
        </div>
      </div>
    </div>
    
    <div class="card">
      <div class="card-header">Usage Summary</div>
      <div class="card-body">
        <div class="metrics">
          <div class="metric">
            <div class="metric-label">Total Usage</div>
            <div class="metric-value">${results.usageInfo.totalUsage}</div>
          </div>
          ${Object.entries(results.usageInfo.byOperation || {}).map(([operation, count]) => `
            <div class="metric">
              <div class="metric-label">${operation.charAt(0).toUpperCase() + operation.slice(1)} Operations</div>
              <div class="metric-value">${count}</div>
            </div>
          `).join('')}
        </div>
        
        <div class="chart-container">
          <canvas id="usageChart"></canvas>
        </div>
      </div>
    </div>
    
    <div class="card">
      <div class="card-header">Analysis Results</div>
      <div class="card-body">
        <div class="metrics">
          <div class="metric">
            <div class="metric-label">CSDE Value</div>
            <div class="metric-value">${results.analysisResult.csdeValue.toFixed(2)}</div>
          </div>
          <div class="metric">
            <div class="metric-label">Performance Factor</div>
            <div class="metric-value">${results.analysisResult.performanceFactor.toFixed(2)}x</div>
          </div>
          <div class="metric">
            <div class="metric-label">ML Enhanced</div>
            <div class="metric-value">${results.analysisResult.mlEnhanced ? 'Yes' : 'No'}</div>
          </div>
          <div class="metric">
            <div class="metric-label">Revenue</div>
            <div class="metric-value">$${results.analysisResult.partner.revenue.partnerRevenue.toFixed(2)}</div>
          </div>
        </div>
        
        <h3>Remediation Actions</h3>
        <table>
          <thead>
            <tr>
              <th>Title</th>
              <th>Priority</th>
              <th>Type</th>
              <th>Automation</th>
            </tr>
          </thead>
          <tbody>
            ${results.analysisResult.remediationActions.slice(0, 5).map(action => `
              <tr>
                <td>${action.title}</td>
                <td>${action.priority.toUpperCase()}</td>
                <td>${action.type}</td>
                <td>${action.automationPotential}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    </div>
    
    <div class="card">
      <div class="card-header">Remediation Results</div>
      <div class="card-body">
        <div class="metrics">
          <div class="metric">
            <div class="metric-label">Actions Selected</div>
            <div class="metric-value">${results.remediationResult.remediation.actionsSelected}</div>
          </div>
          <div class="metric">
            <div class="metric-label">Actions Remediated</div>
            <div class="metric-value">${results.remediationResult.remediation.actionsRemediated}</div>
          </div>
          <div class="metric">
            <div class="metric-label">Success Rate</div>
            <div class="metric-value">${(results.remediationResult.remediation.actionsRemediated / results.remediationResult.remediation.actionsSelected * 100).toFixed(2)}%</div>
          </div>
          <div class="metric">
            <div class="metric-label">Revenue</div>
            <div class="metric-value">$${results.remediationResult.partner.revenue.partnerRevenue.toFixed(2)}</div>
          </div>
        </div>
        
        <div class="chart-container">
          <canvas id="remediationChart"></canvas>
        </div>
      </div>
    </div>
    
    <div class="footer">
      <p>NovaFuse Partner SDK Test Report</p>
      <p>Generated on ${new Date().toLocaleString()}</p>
    </div>
  </div>
  
  <script>
    // Revenue chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
      type: 'bar',
      data: {
        labels: ${JSON.stringify(Object.keys(results.revenueInfo.byOperation || {}))},
        datasets: [{
          label: 'Revenue by Operation',
          data: ${JSON.stringify(Object.values(results.revenueInfo.byOperation || {}).map(op => op.revenue))},
          backgroundColor: 'rgba(10, 132, 255, 0.6)',
          borderColor: 'rgba(10, 132, 255, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Revenue ($)'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Operation'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Revenue by Operation'
          }
        }
      }
    });
    
    // Usage chart
    const usageCtx = document.getElementById('usageChart').getContext('2d');
    new Chart(usageCtx, {
      type: 'pie',
      data: {
        labels: ${JSON.stringify(Object.keys(results.usageInfo.byOperation || {}))},
        datasets: [{
          label: 'Usage by Operation',
          data: ${JSON.stringify(Object.values(results.usageInfo.byOperation || {}))},
          backgroundColor: [
            'rgba(10, 132, 255, 0.6)',
            'rgba(255, 149, 0, 0.6)',
            'rgba(52, 199, 89, 0.6)'
          ],
          borderColor: [
            'rgba(10, 132, 255, 1)',
            'rgba(255, 149, 0, 1)',
            'rgba(52, 199, 89, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: 'Usage by Operation'
          }
        }
      }
    });
    
    // Remediation chart
    const remediationCtx = document.getElementById('remediationChart').getContext('2d');
    new Chart(remediationCtx, {
      type: 'bar',
      data: {
        labels: ['Selected', 'Remediated', 'Failed'],
        datasets: [{
          label: 'Remediation Actions',
          data: [
            ${results.remediationResult.remediation.actionsSelected},
            ${results.remediationResult.remediation.actionsRemediated},
            ${results.remediationResult.remediation.actionsFailed}
          ],
          backgroundColor: [
            'rgba(10, 132, 255, 0.6)',
            'rgba(52, 199, 89, 0.6)',
            'rgba(255, 69, 58, 0.6)'
          ],
          borderColor: [
            'rgba(10, 132, 255, 1)',
            'rgba(52, 199, 89, 1)',
            'rgba(255, 69, 58, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Actions'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Status'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Remediation Actions'
          }
        }
      }
    });
  </script>
</body>
</html>
`;

  fs.writeFileSync(
    path.join(outputDir, 'test_report.html'),
    reportHtml
  );

  console.log(`HTML report saved to ${path.join(outputDir, 'test_report.html')}`);
}

// Run the test
runTest();
