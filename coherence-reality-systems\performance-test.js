const axios = require('axios');
const { performance } = require('perf_hooks');
const { randomBytes } = require('crypto');
const { Worker, isMainThread, workerData, parentPort } = require('worker_threads');
const os = require('os');
const path = require('path');
const fs = require('fs');

// Configuration
const SERVER_URL = 'http://localhost:8080';
const TEST_DURATION = 30000; // 30 seconds per test
const WARMUP_DURATION = 5000; // 5 seconds
const COOLDOWN_DURATION = 2000; // 2 seconds
const TEST_ACCOUNTS_COUNT = 100;

// Test scenarios
const TEST_SCENARIOS = [
  { name: 'LOW_LOAD', tps: 5, duration: TEST_DURATION },
  { name: 'MEDIUM_LOAD', tps: 20, duration: TEST_DURATION },
  { name: 'HIGH_LOAD', tps: 50, duration: TEST_DURATION },
  { name: 'PEAK_LOAD', tps: 100, duration: TEST_DURATION / 2 },
];

// Test accounts
const TEST_ACCOUNTS = Array(TEST_ACCOUNTS_COUNT).fill().map((_, i) => ({
  id: `test-node-${i}`,
  address: '0x' + randomBytes(20).toString('hex'),
  privateKey: randomBytes(32).toString('hex'),
  nonce: 0,
  balance: 0
}));

// Global stats
const stats = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  responseTimes: [],
  startTime: 0,
  endTime: 0,
  errors: new Map(),
  scenarios: {}
};

// Create results directory
const RESULTS_DIR = path.join(__dirname, 'test-results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR);
}

// Worker thread for parallel load testing
if (!isMainThread) {
  const { testConfig, accounts } = workerData;
  const accountCount = accounts.length;
  let running = true;
  
  // Set up test duration timeout
  const testTimeout = setTimeout(() => {
    running = false;
    parentPort.postMessage({ type: 'completed' });
  }, testConfig.duration);

  // Main test loop
  const interval = setInterval(async () => {
    if (!running) {
      clearInterval(interval);
      clearTimeout(testTimeout);
      return;
    }

    const batchSize = Math.ceil(testConfig.tps / 10); // Send 10 batches per second
    const batchPromises = [];
    
    for (let i = 0; i < batchSize; i++) {
      const fromAccount = accounts[Math.floor(Math.random() * accountCount)];
      const toAccount = accounts[Math.floor(Math.random() * accountCount)];
      const start = performance.now();
      
      // Send a transaction
      const promise = axios.post(
        `${SERVER_URL}/aetherium/send`,
        {
          from: fromAccount.address,
          to: toAccount.address,
          value: '**********000000', // 0.001 AE
          maxFeePerGas: '**********',
          maxPriorityFeePerGas: '**********',
          data: '0x' + randomBytes(32).toString('hex')
        },
        {
          headers: { 'x-node-id': fromAccount.id },
          timeout: 10000 // 10 second timeout
        }
      )
      .then(() => ({
        success: true,
        duration: performance.now() - start
      }))
      .catch(error => {
        const errorMessage = error.response?.data?.error || error.message;
        return {
          success: false,
          duration: performance.now() - start,
          error: errorMessage
        };
      });
      
      batchPromises.push(promise);
    }

    // Wait for all requests in this batch to complete
    const results = await Promise.all(batchPromises);
    
    // Process results
    const batchStats = {
      total: results.length,
      success: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      durations: results.map(r => r.duration)
    };
    
    // Record errors
    results.filter(r => !r.success).forEach(({ error }) => {
      const errorKey = error || 'unknown';
      stats.errors.set(errorKey, (stats.errors.get(errorKey) || 0) + 1);
    });
    
    parentPort.postMessage({
      type: 'batchComplete',
      stats: batchStats
    });
    
  }, 100); // 10 batches per second
  
  return;
}

// Helper function to calculate statistics
function calculateStats(durations) {
  if (!durations.length) return {};
  
  const sorted = [...durations].sort((a, b) => a - b);
  const sum = sorted.reduce((a, b) => a + b, 0);
  const avg = sum / sorted.length;
  const min = sorted[0];
  const max = sorted[sorted.length - 1];
  const p50 = sorted[Math.floor(sorted.length * 0.5)];
  const p90 = sorted[Math.floor(sorted.length * 0.9)];
  const p99 = sorted[Math.floor(sorted.length * 0.99)] || p90;
  
  return {
    avg,
    min,
    max,
    p50,
    p90,
    p99,
    total: sorted.length
  };
}

// Save test results to file
function saveResults(scenario, results) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    .replace('T', '_')
    .replace('Z', '');
  const filename = `perf-${scenario.name.toLowerCase()}-${timestamp}.json`;
  const filepath = path.join(RESULTS_DIR, filename);
  
  const resultData = {
    scenario,
    timestamp: new Date().toISOString(),
    duration: results.endTime - results.startTime,
    stats: {
      totalRequests: results.totalRequests,
      successfulRequests: results.successfulRequests,
      failedRequests: results.failedRequests,
      successRate: results.successfulRequests / results.totalRequests,
      tps: results.successfulRequests / ((results.endTime - results.startTime) / 1000),
      responseTimes: calculateStats(results.responseTimes)
    },
    errors: Object.fromEntries(results.errors)
  };
  
  fs.writeFileSync(filepath, JSON.stringify(resultData, null, 2));
  return filepath;
}

// Main test runner
async function runPerformanceTest() {
  console.log('🚀 Starting KetherNet Performance Test Suite\n');
  
  // Check server status
  try {
    console.log('🔍 Checking server status...');
    const { data: status } = await axios.get(`${SERVER_URL}/health`);
    console.log(`✅ Server online | Status: ${status.status}`);
  } catch (error) {
    console.error('❌ Server not responding:', error.message);
    process.exit(1);
  }
  
  // Initialize test accounts
  console.log(`\n🔧 Initializing ${TEST_ACCOUNTS.length} test accounts...`);
  const initPromises = TEST_ACCOUNTS.map(account => 
    axios.post(
      `${SERVER_URL}/aetherium/faucet`,
      { address: account.address },
      { headers: { 'x-node-id': account.id } }
    ).catch(error => {
      console.warn(`  ⚠️ Failed to fund account ${account.address}:`, error.message);
      return null;
    })
  );
  
  await Promise.all(initPromises);
  console.log('✅ Test accounts initialized');
  
  // Run each test scenario
  for (const scenario of TEST_SCENARIOS) {
    console.log(`\n🚀 Starting scenario: ${scenario.name} (${scenario.tps} TPS for ${scenario.duration/1000}s)`);
    
    // Reset stats for this scenario
    const scenarioStats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      responseTimes: [],
      startTime: 0,
      endTime: 0,
      errors: new Map()
    };
    
    // Warm-up period
    console.log('  🔥 Warming up...');
    await new Promise(resolve => setTimeout(resolve, WARMUP_DURATION));
    
    // Run the test
    console.log('  🚀 Running test...');
    scenarioStats.startTime = performance.now();
    
    // Calculate number of workers (one per CPU core)
    const numWorkers = Math.min(4, Math.max(1, os.cpus().length - 1));
    const tpsPerWorker = Math.ceil(scenario.tps / numWorkers);
    const accountsPerWorker = Math.ceil(TEST_ACCOUNTS.length / numWorkers);
    
    console.log(`  🛠  Using ${numWorkers} workers (${tpsPerWorker} TPS each)`);
    
    // Create workers
    const workers = [];
    for (let i = 0; i < numWorkers; i++) {
      const startIdx = i * accountsPerWorker;
      const endIdx = Math.min(startIdx + accountsPerWorker, TEST_ACCOUNTS.length);
      const workerAccounts = TEST_ACCOUNTS.slice(startIdx, endIdx);
      
      const worker = new Worker(__filename, {
        workerData: {
          testConfig: { ...scenario, tps: tpsPerWorker },
          accounts: workerAccounts
        }
      });
      
      worker.on('message', ({ type, stats: batchStats }) => {
        if (type === 'batchComplete') {
          scenarioStats.totalRequests += batchStats.total;
          scenarioStats.successfulRequests += batchStats.success;
          scenarioStats.failedRequests += batchStats.failed;
          scenarioStats.responseTimes.push(...batchStats.durations);
          
          // Update global stats
          stats.totalRequests += batchStats.total;
          stats.successfulRequests += batchStats.success;
          stats.failedRequests += batchStats.failed;
          stats.responseTimes.push(...batchStats.durations);
        }
      });
      
      workers.push(new Promise(resolve => {
        worker.on('exit', resolve);
      }));
    }
    
    // Wait for the test duration
    await new Promise(resolve => setTimeout(resolve, scenario.duration));
    
    // Stop all workers
    for (const worker of workers) {
      worker.terminate();
    }
    
    // Wait for workers to terminate
    await Promise.all(workers);
    
    scenarioStats.endTime = performance.now();
    
    // Calculate and display results
    const duration = (scenarioStats.endTime - scenarioStats.startTime) / 1000;
    const totalRequests = scenarioStats.successfulRequests + scenarioStats.failedRequests;
    const tps = scenarioStats.successfulRequests / duration;
    const successRate = (scenarioStats.successfulRequests / totalRequests) * 100 || 0;
    
    const stats = calculateStats(scenarioStats.responseTimes);
    
    console.log('\n📊 Test Results:');
    console.log('='.repeat(50));
    console.log(`Scenario: ${scenario.name}`);
    console.log(`Duration: ${duration.toFixed(2)}s`);
    console.log(`Total Requests: ${totalRequests}`);
    console.log(`Successful: ${scenarioStats.successfulRequests} (${successRate.toFixed(2)}%)`);
    console.log(`Failed: ${scenarioStats.failedRequests}`);
    console.log(`Throughput: ${tps.toFixed(2)} TPS`);
    console.log('\n⏱️  Response Times (ms):');
    console.log(`  Average: ${stats.avg.toFixed(2)}`);
    console.log(`  Min: ${stats.min.toFixed(2)}`);
    console.log(`  Max: ${stats.max.toFixed(2)}`);
    console.log(`  p50: ${stats.p50.toFixed(2)}`);
    console.log(`  p90: ${stats.p90.toFixed(2)}`);
    console.log(`  p99: ${stats.p99.toFixed(2)}`);
    
    // Save results
    const resultFile = saveResults(scenario, scenarioStats);
    console.log(`\n💾 Results saved to: ${resultFile}`);
    
    // Cooldown between tests
    console.log(`\n🔄 Cooldown...`);
    await new Promise(resolve => setTimeout(resolve, COOLDOWN_DURATION));
  }
  
  // Calculate and display overall statistics
  const totalDuration = (Date.now() - stats.startTime) / 1000;
  const totalTps = stats.successfulRequests / totalDuration;
  
  console.log('\n🏁 All performance tests completed');
  console.log('='.repeat(50));
  console.log(`Total Test Duration: ${totalDuration.toFixed(2)}s`);
  console.log(`Total Requests: ${stats.totalRequests}`);
  console.log(`Successful: ${stats.successfulRequests}`);
  console.log(`Failed: ${stats.failedRequests}`);
  console.log(`Average Throughput: ${totalTps.toFixed(2)} TPS`);
  
  if (stats.errors.size > 0) {
    console.log('\n❌ Errors encountered:');
    for (const [error, count] of stats.errors.entries()) {
      console.log(`  ${count}x ${error}`);
    }
  }
}

// Run the tests
if (isMainThread) {
  stats.startTime = Date.now();
  runPerformanceTest()
    .catch(error => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}
    });
    
    workers.push(worker);
  }
  
  // Wait for all workers to complete
  await Promise.all(workers.map(worker => 
    new Promise(resolve => worker.on('exit', resolve))
  ));
  
  // Record test end time
  stats.endTime = performance.now();
  
  // Calculate and display results
  const duration = (stats.endTime - stats.startTime) / 1000; // in seconds
  const actualTps = stats.totalRequests / duration;
  const successRate = (stats.successfulRequests / stats.totalRequests) * 100;
  
  // Calculate percentiles
  const sortedTimes = [...stats.responseTimes].sort((a, b) => a - b);
  const p50 = sortedTimes[Math.floor(sortedTimes.length * 0.5)] || 0;
  const p90 = sortedTimes[Math.floor(sortedTimes.length * 0.9)] || 0;
  const p99 = sortedTimes[Math.floor(sortedTimes.length * 0.99)] || 0;
  const maxTime = Math.max(...stats.responseTimes, 0);
  
  console.log('\n📈 Test Results:');
  console.log('='.repeat(60));
  console.log(`Test Config:      ${config.name}`);
  console.log(`Duration:         ${duration.toFixed(2)}s`);
  console.log(`Target TPS:       ${config.tps}`);
  console.log(`Actual TPS:       ${actualTps.toFixed(2)}`);
  console.log(`Success Rate:     ${successRate.toFixed(2)}%`);
  console.log('\nResponse Times (ms):');
  console.log(`  Avg: ${(stats.responseTimes.reduce((a, b) => a + b, 0) / stats.responseTimes.length).toFixed(2)}`);
  console.log(`  P50: ${p50.toFixed(2)}`);
  console.log(`  P90: ${p90.toFixed(2)}`);
  console.log(`  P99: ${p99.toFixed(2)}`);
  console.log(`  Max: ${maxTime.toFixed(2)}`);
  
  if (stats.errors.size > 0) {
    console.log('\n⚠️  Errors:');
    for (const [error, count] of stats.errors.entries()) {
      console.log(`  ${count}x ${error}`);
    }
  }
  
  console.log('='.repeat(60));
  
  // Add a small cooldown between tests
  await new Promise(resolve => setTimeout(resolve, COOLDOWN_DURATION));
}

// Run the tests
runPerformanceTest().catch(console.error);
