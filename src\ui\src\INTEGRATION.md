# Integrating the UI with the Backend

This document explains how to integrate the NovaFuse UI with the backend WebSocket server.

## Overview

The NovaFuse UI communicates with the backend using WebSockets. The communication is handled by the `WebSocketService` class, which provides methods for connecting to the server, sending messages, and subscribing to channels.

## WebSocket Service

The `WebSocketService` class is located in `src/services/WebSocketService.js`. It provides the following methods:

- `connect()`: Connect to the WebSocket server
- `disconnect()`: Disconnect from the WebSocket server
- `send(message, timeout)`: Send a message to the server
- `subscribe(channel)`: Subscribe to a channel
- `unsubscribe(channel)`: Unsubscribe from a channel
- `publish(channel, data)`: Publish a message to a channel
- `addEventListener(event, listener)`: Add an event listener
- `removeEventListener(event, listener)`: Remove an event listener

## Control Context

The `ControlContext` provides a React context for accessing the control system. It uses the `WebSocketService` to communicate with the backend. The context provides methods for getting and setting control values, executing actions, and accessing the control system.

## Integration Steps

To integrate the UI with the backend, follow these steps:

1. **Configure the WebSocket URL**

   Update the WebSocket URL in `src/services/WebSocketService.js`:

   ```javascript
   constructor(url = 'ws://localhost:3001/ws') {
     this.url = url;
     // ...
   }
   ```

2. **Update the ControlContext**

   The `ControlContext` is already set up to use the `WebSocketService`. It handles connecting to the server, subscribing to channels, and setting up event listeners.

3. **Handle Real-Time Updates**

   The `ControlContext` handles real-time updates by listening for messages on the `control-updates`, `control-actions`, and `control-events` channels.

4. **Execute Actions**

   Actions are executed by publishing messages to the `control-actions` channel:

   ```javascript
   executeAction: (action, params = {}) => {
     return webSocketService.publish('control-actions', {
       action,
       params,
       timestamp: Date.now()
     });
   }
   ```

5. **Update Control Values**

   Control values are updated by publishing messages to the `control-updates` channel:

   ```javascript
   setControlValue: (controlId, value) => {
     setControlValues(prev => ({
       ...prev,
       [controlId]: value
     }));
     
     webSocketService.publish('control-updates', {
       controlId,
       value,
       timestamp: Date.now()
     });
     
     return true;
   }
   ```

## Example

See `src/examples/ConnectToBackend.js` for a complete example of how to connect to the backend, register a tensor, create a visualization, execute a query, and subscribe to real-time updates.

```javascript
import { connectToBackend, disconnectFromBackend } from '../examples/ConnectToBackend';

// Connect to the backend
connectToBackend()
  .then(({ tensorId, visualizationId }) => {
    console.log('Connected to backend');
    console.log('Tensor ID:', tensorId);
    console.log('Visualization ID:', visualizationId);
    
    // Disconnect when done
    return disconnectFromBackend();
  })
  .catch(error => {
    console.error('Error:', error);
  });
```

## Message Format

Messages sent to and received from the server should follow this format:

```javascript
{
  // Common fields
  id: 'message-id', // Optional, added automatically if not provided
  timestamp: 1234567890, // Optional, added automatically if not provided
  
  // For tensor operations
  component: 'tensor',
  type: 'register-tensor', // or 'get-tensor', 'update-tensor', 'heal-tensor', 'damage-tensor'
  id: 'tensor-id',
  tensor: { /* tensor data */ },
  domain: 'universal',
  
  // For visualization operations
  component: 'visualization',
  type: 'create-visualization', // or 'get-visualization-types', 'update-visualization', 'delete-visualization'
  visualizationType: '3d_tensor_visualization',
  data: { /* visualization data */ },
  options: { /* visualization options */ },
  
  // For analytics operations
  component: 'analytics',
  type: 'execute-query', // or 'get-metrics', 'get-dashboards', 'get-dashboard'
  query: 'SELECT * FROM tensor_metrics',
  params: { /* query parameters */ }
}
```

## Troubleshooting

If you encounter issues connecting to the backend, check the following:

1. **WebSocket Server Running**: Make sure the WebSocket server is running on the specified URL.
2. **CORS**: If you're running the UI and server on different domains, make sure CORS is properly configured.
3. **Message Format**: Make sure the messages you're sending follow the expected format.
4. **Event Listeners**: Make sure you're setting up event listeners for the channels you're interested in.
5. **Console Errors**: Check the browser console for errors.

## Next Steps

Once the basic integration is working, you can:

1. **Implement Authentication**: Add authentication to the WebSocket connection.
2. **Add Error Handling**: Improve error handling for WebSocket communication.
3. **Optimize Performance**: Optimize the WebSocket communication for high-frequency updates.
4. **Add Reconnection Logic**: Improve the reconnection logic for better reliability.
5. **Add Offline Support**: Add support for offline operation and synchronization when back online.
