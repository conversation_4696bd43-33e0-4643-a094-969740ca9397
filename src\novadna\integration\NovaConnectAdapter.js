/**
 * NovaConnectAdapter.js
 * 
 * This module provides integration with NovaConnect's API connectors for NovaDNA.
 * It enables secure data exchange with healthcare systems and implements
 * standardized API interfaces.
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');

/**
 * NovaConnectAdapter class for integrating with NovaConnect
 */
class NovaConnectAdapter {
  constructor(options = {}) {
    this.apiUrl = options.apiUrl || 'http://localhost:3000/api/novaconnect';
    this.apiKey = options.apiKey;
    this.apiSecret = options.apiSecret;
    this.timeout = options.timeout || 30000; // 30 seconds
    this.connectors = new Map();
    
    // Create axios instance
    this.client = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey
      }
    });
    
    // Add request interceptor for authentication
    this.client.interceptors.request.use(config => {
      if (this.apiSecret) {
        const timestamp = Date.now().toString();
        const signature = this._generateSignature(timestamp);
        
        config.headers['X-Timestamp'] = timestamp;
        config.headers['X-Signature'] = signature;
      }
      
      return config;
    });
    
    // Initialize connectors
    this._initializeConnectors();
  }

  /**
   * Connect to an EHR system
   * @param {String} ehrSystem - The EHR system to connect to
   * @param {Object} credentials - The credentials for the EHR system
   * @returns {Promise<Object>} - The connection result
   */
  async connectToEHR(ehrSystem, credentials) {
    try {
      const connector = this.connectors.get(ehrSystem);
      
      if (!connector) {
        throw new Error(`Unsupported EHR system: ${ehrSystem}`);
      }
      
      const response = await this.client.post('/ehr/connect', {
        ehrSystem,
        credentials,
        connectorId: connector.id
      });
      
      return {
        connectionId: response.data.connectionId,
        status: response.data.status,
        ehrSystem,
        connectedAt: response.data.connectedAt
      };
    } catch (error) {
      // Handle API errors
      if (error.response) {
        throw new Error(`NovaConnect API error: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
      } else if (error.request) {
        throw new Error(`NovaConnect API request failed: ${error.message}`);
      } else {
        throw new Error(`NovaConnect error: ${error.message}`);
      }
    }
  }

  /**
   * Fetch patient data from an EHR system
   * @param {String} connectionId - The connection ID
   * @param {String} patientId - The patient ID
   * @param {Object} options - Options for fetching data
   * @returns {Promise<Object>} - The patient data
   */
  async fetchPatientData(connectionId, patientId, options = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (options.dataTypes) {
        queryParams.append('dataTypes', options.dataTypes.join(','));
      }
      
      if (options.startDate) {
        queryParams.append('startDate', options.startDate);
      }
      
      if (options.endDate) {
        queryParams.append('endDate', options.endDate);
      }
      
      const response = await this.client.get(
        `/ehr/${connectionId}/patient/${patientId}?${queryParams.toString()}`
      );
      
      return response.data;
    } catch (error) {
      // Handle API errors
      if (error.response) {
        throw new Error(`NovaConnect API error: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
      } else if (error.request) {
        throw new Error(`NovaConnect API request failed: ${error.message}`);
      } else {
        throw new Error(`NovaConnect error: ${error.message}`);
      }
    }
  }

  /**
   * Create an emergency profile from EHR data
   * @param {Object} patientData - The patient data from EHR
   * @returns {Object} - The emergency profile
   */
  createProfileFromEHR(patientData) {
    if (!patientData || !patientData.demographics) {
      throw new Error('Invalid patient data');
    }
    
    // Extract demographics
    const demographics = patientData.demographics;
    
    // Create basic profile
    const profile = {
      fullName: `${demographics.firstName} ${demographics.lastName}`,
      dateOfBirth: demographics.dateOfBirth,
      bloodType: demographics.bloodType || 'Unknown',
      emergencyContacts: []
    };
    
    // Add emergency contacts
    if (patientData.contacts) {
      profile.emergencyContacts = patientData.contacts
        .filter(contact => contact.relationship && contact.phone)
        .map(contact => ({
          name: contact.name,
          relationship: contact.relationship,
          phone: contact.phone
        }));
    }
    
    // Add allergies
    if (patientData.allergies) {
      profile.allergies = patientData.allergies.map(allergy => ({
        name: allergy.name,
        severity: allergy.severity || 'Unknown',
        reactions: allergy.reactions || []
      }));
    }
    
    // Add medications
    if (patientData.medications) {
      profile.medications = patientData.medications.map(medication => ({
        name: medication.name,
        dosage: medication.dosage,
        frequency: medication.frequency,
        purpose: medication.purpose
      }));
    }
    
    // Add medical conditions
    if (patientData.conditions) {
      profile.medicalConditions = patientData.conditions.map(condition => ({
        name: condition.name,
        diagnosisDate: condition.diagnosisDate,
        notes: condition.notes
      }));
    }
    
    // Add insurance info
    if (patientData.insurance) {
      profile.insuranceInfo = {
        provider: patientData.insurance.provider,
        policyNumber: patientData.insurance.policyNumber,
        groupNumber: patientData.insurance.groupNumber,
        phone: patientData.insurance.phone
      };
    }
    
    // Add advanced directives
    if (patientData.advancedDirectives) {
      if (patientData.advancedDirectives.dnr !== undefined) {
        profile.dnr = patientData.advancedDirectives.dnr;
      }
      
      if (patientData.advancedDirectives.organDonor !== undefined) {
        profile.organDonor = patientData.advancedDirectives.organDonor;
      }
    }
    
    // Add primary care provider
    if (patientData.providers && patientData.providers.length > 0) {
      const primaryProvider = patientData.providers.find(p => p.isPrimary) || patientData.providers[0];
      
      profile.primaryCareProvider = {
        name: primaryProvider.name,
        phone: primaryProvider.phone,
        address: primaryProvider.address
      };
    }
    
    return profile;
  }

  /**
   * Send emergency notification to healthcare providers
   * @param {String} profileId - The profile ID
   * @param {Object} emergencyInfo - Information about the emergency
   * @returns {Promise<Object>} - The notification result
   */
  async sendEmergencyNotification(profileId, emergencyInfo) {
    try {
      const notificationId = uuidv4();
      const timestamp = new Date().toISOString();
      
      const response = await this.client.post('/notifications/emergency', {
        notificationId,
        profileId,
        timestamp,
        emergencyInfo: {
          type: emergencyInfo.type || 'MEDICAL',
          location: emergencyInfo.location,
          severity: emergencyInfo.severity || 'HIGH',
          description: emergencyInfo.description,
          responderId: emergencyInfo.responderId
        }
      });
      
      return response.data;
    } catch (error) {
      // Handle API errors
      if (error.response) {
        throw new Error(`NovaConnect API error: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
      } else if (error.request) {
        throw new Error(`NovaConnect API request failed: ${error.message}`);
      } else {
        throw new Error(`NovaConnect error: ${error.message}`);
      }
    }
  }

  /**
   * Get available healthcare connectors
   * @returns {Promise<Array>} - The available connectors
   */
  async getAvailableConnectors() {
    try {
      const response = await this.client.get('/connectors');
      
      // Update local connectors
      for (const connector of response.data) {
        this.connectors.set(connector.name, connector);
      }
      
      return response.data;
    } catch (error) {
      // Handle API errors
      if (error.response) {
        throw new Error(`NovaConnect API error: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
      } else if (error.request) {
        throw new Error(`NovaConnect API request failed: ${error.message}`);
      } else {
        throw new Error(`NovaConnect error: ${error.message}`);
      }
    }
  }

  /**
   * Initialize connectors
   * @private
   */
  async _initializeConnectors() {
    try {
      // In a real implementation, this would fetch connectors from the API
      // For now, we'll initialize with some common EHR systems
      
      const defaultConnectors = [
        {
          id: 'epic',
          name: 'Epic',
          type: 'EHR',
          description: 'Epic Systems EHR connector',
          apiVersion: '1.0.0',
          status: 'ACTIVE'
        },
        {
          id: 'cerner',
          name: 'Cerner',
          type: 'EHR',
          description: 'Cerner EHR connector',
          apiVersion: '1.0.0',
          status: 'ACTIVE'
        },
        {
          id: 'allscripts',
          name: 'Allscripts',
          type: 'EHR',
          description: 'Allscripts EHR connector',
          apiVersion: '1.0.0',
          status: 'ACTIVE'
        },
        {
          id: 'meditech',
          name: 'Meditech',
          type: 'EHR',
          description: 'Meditech EHR connector',
          apiVersion: '1.0.0',
          status: 'ACTIVE'
        },
        {
          id: 'athenahealth',
          name: 'athenahealth',
          type: 'EHR',
          description: 'athenahealth EHR connector',
          apiVersion: '1.0.0',
          status: 'ACTIVE'
        }
      ];
      
      for (const connector of defaultConnectors) {
        this.connectors.set(connector.name, connector);
      }
      
      // Try to fetch actual connectors from the API
      try {
        await this.getAvailableConnectors();
      } catch (error) {
        // Use default connectors if API call fails
        console.warn('Failed to fetch connectors from API, using defaults');
      }
    } catch (error) {
      console.error('Error initializing connectors:', error);
    }
  }

  /**
   * Generate a signature for API authentication
   * @param {String} timestamp - The timestamp
   * @returns {String} - The generated signature
   * @private
   */
  _generateSignature(timestamp) {
    return crypto
      .createHmac('sha256', this.apiSecret)
      .update(`${this.apiKey}:${timestamp}`)
      .digest('hex');
  }
}

module.exports = NovaConnectAdapter;
