groups:
  - name: novafuse_alerts
    rules:
      # Quantum Inference Alerts
      - alert: LowCertaintyRate
        expr: quantum_certainty_rate < 0.3
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low Quantum Certainty Rate"
          description: "Quantum Certainty Rate is below 30% for 5 minutes (current value: {{ $value }})"
          
      - alert: HighInferenceTime
        expr: quantum_inference_time > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High Quantum Inference Time"
          description: "Quantum Inference Time is above 5ms for 5 minutes (current value: {{ $value }}ms)"
          
      # Trinity CSDE Alerts
      - alert: LowGovernanceScore
        expr: trinity_governance_score < 0.7
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Low Governance Score"
          description: "Trinity CSDE Governance Score is below 70% for 10 minutes (current value: {{ $value }})"
          
      - alert: LowDetectionScore
        expr: trinity_detection_score < 0.7
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Low Detection Score"
          description: "Trinity CSDE Detection Score is below 70% for 10 minutes (current value: {{ $value }})"
          
      - alert: LowResponseScore
        expr: trinity_response_score < 0.7
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Low Response Score"
          description: "Trinity CSDE Response Score is below 70% for 10 minutes (current value: {{ $value }})"
          
      - alert: LowOverallTrinityScore
        expr: trinity_csde_score < 0.7
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: "Low Overall Trinity CSDE Score"
          description: "Overall Trinity CSDE Score is below 70% for 10 minutes (current value: {{ $value }})"
          
      # Resource Utilization Alerts
      - alert: HighCPUUsage
        expr: process_cpu_seconds_total:rate5m > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU Usage"
          description: "NovaFuse API CPU usage is above 80% for 5 minutes (current value: {{ $value }})"
          
      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / process_heap_size_total_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High Memory Usage"
          description: "NovaFuse API memory usage is above 80% for 5 minutes (current value: {{ $value }})"
          
      # API Performance Alerts
      - alert: HighAPILatency
        expr: http_request_duration_seconds{quantile="0.95"} > 0.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High API Latency"
          description: "95th percentile of API request duration is above 500ms for 5 minutes (current value: {{ $value }}s)"
          
      - alert: HighErrorRate
        expr: sum(rate(http_requests_total{status=~"5.."}[5m])) / sum(rate(http_requests_total[5m])) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High Error Rate"
          description: "Error rate is above 5% for 5 minutes (current value: {{ $value }})"
