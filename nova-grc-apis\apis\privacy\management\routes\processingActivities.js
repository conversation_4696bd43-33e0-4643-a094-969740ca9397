/**
 * Processing Activities Routes
 * 
 * Routes for managing data processing activities.
 */

const express = require('express');
const { check } = require('express-validator');
const ProcessingActivityController = require('../controllers/ProcessingActivityController');
const auth = require('../../../middleware/auth');

const router = express.Router();

/**
 * @route   GET /api/privacy/management/processing-activities
 * @desc    Get all processing activities
 * @access  Private
 */
router.get('/', auth, ProcessingActivityController.getAllProcessingActivities);

/**
 * @route   GET /api/privacy/management/processing-activities/:id
 * @desc    Get a single processing activity
 * @access  Private
 */
router.get('/:id', auth, ProcessingActivityController.getProcessingActivity);

/**
 * @route   POST /api/privacy/management/processing-activities
 * @desc    Create a new processing activity
 * @access  Private
 */
router.post('/', [
  auth,
  [
    check('name', 'Name is required').not().isEmpty(),
    check('description', 'Description is required').not().isEmpty(),
    check('purpose', 'Purpose is required').not().isEmpty(),
    check('dataCategories', 'Data categories must be an array').isArray(),
    check('dataSubjects', 'Data subjects must be an array').isArray(),
    check('legalBasis', 'Legal basis is required').not().isEmpty()
  ]
], ProcessingActivityController.createProcessingActivity);

/**
 * @route   PUT /api/privacy/management/processing-activities/:id
 * @desc    Update a processing activity
 * @access  Private
 */
router.put('/:id', [
  auth,
  [
    check('name', 'Name is required').optional().not().isEmpty(),
    check('description', 'Description is required').optional().not().isEmpty(),
    check('purpose', 'Purpose is required').optional().not().isEmpty(),
    check('dataCategories', 'Data categories must be an array').optional().isArray(),
    check('dataSubjects', 'Data subjects must be an array').optional().isArray(),
    check('legalBasis', 'Legal basis is required').optional().not().isEmpty()
  ]
], ProcessingActivityController.updateProcessingActivity);

/**
 * @route   DELETE /api/privacy/management/processing-activities/:id
 * @desc    Delete a processing activity
 * @access  Private
 */
router.delete('/:id', auth, ProcessingActivityController.deleteProcessingActivity);

/**
 * @route   GET /api/privacy/management/processing-activities/:id/dpia-required
 * @desc    Check if a DPIA is required for a processing activity
 * @access  Private
 */
router.get('/:id/dpia-required', auth, ProcessingActivityController.checkDpiaRequired);

module.exports = router;
