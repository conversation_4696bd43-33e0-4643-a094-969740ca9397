"""
Risk model for the Universal Vendor Risk Management System.

This module provides the Risk class for representing vendor risks.
"""

import uuid
import datetime
from typing import Dict, List, Any, Optional


class Risk:
    """
    Risk class for representing vendor risks.

    This class represents a vendor risk in the Universal Vendor Risk Management System.
    """

    def __init__(self,
                vendor_id: str,
                title: str,
                description: str,
                category: str,
                severity: str,
                likelihood: str,
                impact: str,
                status: str = 'open',
                mitigation_plan: Optional[str] = None,
                mitigation_status: Optional[str] = None,
                due_date: Optional[str] = None,
                owner: Optional[str] = None,
                related_assessments: Optional[List[str]] = None,
                metadata: Optional[Dict[str, Any]] = None,
                risk_id: Optional[str] = None,
                created_at: Optional[str] = None,
                updated_at: Optional[str] = None,
                closed_at: Optional[str] = None):
        """
        Initialize a Risk.

        Args:
            vendor_id: The ID of the vendor associated with the risk
            title: The title of the risk
            description: The description of the risk
            category: The category of the risk (e.g., 'security', 'privacy', 'compliance')
            severity: The severity of the risk (e.g., 'critical', 'high', 'medium', 'low')
            likelihood: The likelihood of the risk (e.g., 'high', 'medium', 'low')
            impact: The impact of the risk (e.g., 'high', 'medium', 'low')
            status: The status of the risk (e.g., 'open', 'mitigated', 'accepted', 'closed')
            mitigation_plan: The plan for mitigating the risk
            mitigation_status: The status of the mitigation plan
            due_date: The due date for mitigating the risk
            owner: The owner of the risk
            related_assessments: The IDs of related assessments
            metadata: Additional metadata about the risk
            risk_id: The ID of the risk (generated if not provided)
            created_at: The creation timestamp (generated if not provided)
            updated_at: The last update timestamp (generated if not provided)
            closed_at: The closure timestamp
        """
        self.vendor_id = vendor_id
        self.title = title
        self.description = description
        self.category = category
        self.severity = severity
        self.likelihood = likelihood
        self.impact = impact
        self.status = status
        self.mitigation_plan = mitigation_plan
        self.mitigation_status = mitigation_status
        self.due_date = due_date
        self.owner = owner
        self.related_assessments = related_assessments or []
        self.metadata = metadata or {}
        self.risk_id = risk_id or str(uuid.uuid4())
        self.created_at = created_at or datetime.datetime.now().isoformat()
        self.updated_at = updated_at or self.created_at
        self.closed_at = closed_at

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Risk':
        """
        Create a Risk from a dictionary.

        Args:
            data: Dictionary containing risk data

        Returns:
            A Risk instance
        """
        return cls(
            vendor_id=data.get('vendor_id', ''),
            title=data.get('title', ''),
            description=data.get('description', ''),
            category=data.get('category', ''),
            severity=data.get('severity', 'low'),
            likelihood=data.get('likelihood', 'low'),
            impact=data.get('impact', 'low'),
            status=data.get('status', 'open'),
            mitigation_plan=data.get('mitigation_plan', None),
            mitigation_status=data.get('mitigation_status', None),
            due_date=data.get('due_date', None),
            owner=data.get('owner', None),
            related_assessments=data.get('related_assessments', []),
            metadata=data.get('metadata', {}),
            risk_id=data.get('risk_id', None),
            created_at=data.get('created_at', None),
            updated_at=data.get('updated_at', None),
            closed_at=data.get('closed_at', None)
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the Risk to a dictionary.

        Returns:
            Dictionary representation of the Risk
        """
        return {
            'risk_id': self.risk_id,
            'vendor_id': self.vendor_id,
            'title': self.title,
            'description': self.description,
            'category': self.category,
            'severity': self.severity,
            'likelihood': self.likelihood,
            'impact': self.impact,
            'status': self.status,
            'mitigation_plan': self.mitigation_plan,
            'mitigation_status': self.mitigation_status,
            'due_date': self.due_date,
            'owner': self.owner,
            'related_assessments': self.related_assessments,
            'metadata': self.metadata,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'closed_at': self.closed_at
        }

    def update_status(self, status: str) -> None:
        """
        Update the status of the risk.

        Args:
            status: The new status
        """
        self.status = status
        self.updated_at = datetime.datetime.now().isoformat()

        if status == 'closed' and not self.closed_at:
            self.closed_at = self.updated_at

    def update_mitigation(self, mitigation_plan: str, mitigation_status: str) -> None:
        """
        Update the mitigation plan and status.

        Args:
            mitigation_plan: The mitigation plan
            mitigation_status: The mitigation status
        """
        self.mitigation_plan = mitigation_plan
        self.mitigation_status = mitigation_status
        self.updated_at = datetime.datetime.now().isoformat()

    def calculate_risk_score(self) -> int:
        """
        Calculate the risk score based on severity, likelihood, and impact.

        Returns:
            The calculated risk score
        """
        # Convert severity, likelihood, and impact to numeric values
        severity_map = {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}
        likelihood_map = {'high': 3, 'medium': 2, 'low': 1}
        impact_map = {'high': 3, 'medium': 2, 'low': 1}

        severity_value = severity_map.get(self.severity.lower(), 1)
        likelihood_value = likelihood_map.get(self.likelihood.lower(), 1)
        impact_value = impact_map.get(self.impact.lower(), 1)

        # Calculate the risk score
        risk_score = severity_value * likelihood_value * impact_value

        return risk_score

    def is_critical(self) -> bool:
        """
        Check if the risk is critical.

        Returns:
            True if the risk is critical, False otherwise
        """
        return self.severity.lower() == 'critical'

    def is_overdue(self) -> bool:
        """
        Check if the risk mitigation is overdue.

        Returns:
            True if the risk mitigation is overdue, False otherwise
        """
        if not self.due_date or self.status == 'closed':
            return False

        due_date = datetime.datetime.fromisoformat(self.due_date)
        current_date = datetime.datetime.now()

        return current_date > due_date

    def add_related_assessment(self, assessment_id: str) -> None:
        """
        Add a related assessment to the risk.

        Args:
            assessment_id: The ID of the related assessment
        """
        if assessment_id not in self.related_assessments:
            self.related_assessments.append(assessment_id)
            self.updated_at = datetime.datetime.now().isoformat()