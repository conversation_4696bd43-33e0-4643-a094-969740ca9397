﻿{
  "extends": "base-connector",
  "name": "novafuse-security-connector",
  "version": "1.0.0",
  "description": "Security connector template for NovaFuse API Superstore",
  "category": "security",
  "base_url": "http://localhost:8000/security",
  "endpoints": [
    {
      "name": "vulnerabilities",
      "path": "/vulnerabilities",
      "method": "GET",
      "description": "Get a list of vulnerabilities",
      "parameters": [
        {
          "name": "severity",
          "in": "query",
          "required": false,
          "description": "Filter by severity (low, medium, high, critical)"
        },
        {
          "name": "status",
          "in": "query",
          "required": false,
          "description": "Filter by status (open, closed, in_progress)"
        }
      ]
    },
    {
      "name": "security_scan",
      "path": "/scan",
      "method": "POST",
      "description": "Perform a security scan"
    }
  ]
}
