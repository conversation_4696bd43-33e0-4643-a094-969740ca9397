class CustomReporter {
  constructor(globalConfig, options) {
    this._globalConfig = globalConfig;
    this._options = options || {};
    this.testResults = [];
    this.startTime = null;
  }

  onRunStart() {
    this.startTime = new Date();
    console.log('\n🚀 Starting test suite with 96% pass rate requirement');
    console.log('==================================================\n');
  }

  onTestResult(_test, testResult, _aggregatedResult) {
    this.testResults.push(testResult);

    // Print individual test results
    console.log(`\n📋 Test File: ${testResult.testFilePath}`);
    console.log(`   ✅ Passed: ${testResult.numPassingTests} | ❌ Failed: ${testResult.numFailingTests} | ⏩ Skipped: ${testResult.numPendingTests}`);

    // Print failed tests with details
    if (testResult.numFailingTests > 0) {
      console.log('\n   Failed Tests:');
      testResult.testResults.forEach(result => {
        if (result.status === 'failed') {
          console.log(`   ❌ ${result.fullName}`);
          console.log(`      ${result.failureMessages[0].split('\n')[0]}`);
        }
      });
    }
  }

  onRunComplete(_contexts, results) {
    const endTime = new Date();
    const duration = (endTime - this.startTime) / 1000;

    console.log('\n==================================================');
    console.log('📊 TEST SUMMARY');
    console.log('==================================================');
    console.log(`🕒 Duration: ${duration.toFixed(2)} seconds`);
    console.log(`📝 Test Suites: ${results.numPassedTestSuites} passed, ${results.numFailedTestSuites} failed, ${results.numTotalTestSuites} total`);
    console.log(`🧪 Tests: ${results.numPassedTests} passed, ${results.numFailedTests} failed, ${results.numPendingTests} skipped, ${results.numTotalTests} total`);

    // Calculate pass rate
    const passRate = (results.numPassedTests / (results.numTotalTests - results.numPendingTests) * 100).toFixed(2);
    console.log(`✨ Pass Rate: ${passRate}%`);

    // Check if pass rate meets the 96% threshold
    if (parseFloat(passRate) >= 96) {
      console.log('\n✅ PASS: Test suite meets the 96% pass rate requirement');
    } else {
      console.log('\n❌ FAIL: Test suite does not meet the 96% pass rate requirement');
      console.log(`   Required: 96%, Actual: ${passRate}%`);
    }

    // Coverage summary if available
    if (results.coverageMap) {
      const coverage = results.coverageMap.getCoverageSummary();
      console.log('\n📊 COVERAGE SUMMARY');
      console.log('==================================================');
      console.log(`📏 Statements: ${coverage.statements.pct.toFixed(2)}%`);
      console.log(`🔀 Branches: ${coverage.branches.pct.toFixed(2)}%`);
      console.log(`🔄 Functions: ${coverage.functions.pct.toFixed(2)}%`);
      console.log(`📝 Lines: ${coverage.lines.pct.toFixed(2)}%`);

      // Check if coverage meets the 96% threshold
      const coveragePasses =
        coverage.statements.pct >= 96 &&
        coverage.branches.pct >= 96 &&
        coverage.functions.pct >= 96 &&
        coverage.lines.pct >= 96;

      if (coveragePasses) {
        console.log('\n✅ PASS: Coverage meets the 96% threshold');
      } else {
        console.log('\n❌ FAIL: Coverage does not meet the 96% threshold');

        // Show detailed coverage gaps
        console.log('\n📊 COVERAGE GAPS:');
        if (coverage.statements.pct < 96) console.log(`   Statements: ${coverage.statements.pct.toFixed(2)}% (need ${(96 - coverage.statements.pct).toFixed(2)}% more)`);
        if (coverage.branches.pct < 96) console.log(`   Branches: ${coverage.branches.pct.toFixed(2)}% (need ${(96 - coverage.branches.pct).toFixed(2)}% more)`);
        if (coverage.functions.pct < 96) console.log(`   Functions: ${coverage.functions.pct.toFixed(2)}% (need ${(96 - coverage.functions.pct).toFixed(2)}% more)`);
        if (coverage.lines.pct < 96) console.log(`   Lines: ${coverage.lines.pct.toFixed(2)}% (need ${(96 - coverage.lines.pct).toFixed(2)}% more)`);
      }
    }

    console.log('\n==================================================');
  }
}

module.exports = CustomReporter;
