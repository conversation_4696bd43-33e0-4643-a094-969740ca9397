/**
 * Alerts Module
 * 
 * This module provides a unified interface for creating and managing alerts.
 * It supports multiple notification channels (email, Slack, PagerDuty, etc.).
 */

const logger = require('../config/logger');
const { getConfig } = require('../config');
const { getRedisClient } = require('../config/redis');

// Get alerts configuration
const config = getConfig().monitoring.alerts;

// Redis client for alert state
const redisClient = getRedisClient();

// Alert severity levels
const SEVERITY = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  CRITICAL: 'critical'
};

// Alert status
const STATUS = {
  ACTIVE: 'active',
  RESOLVED: 'resolved'
};

// Notification channels
let emailClient;
let slackClient;
let pagerDutyClient;

// Initialize notification channels
const initializeChannels = () => {
  // Initialize email if enabled
  if (config.email.enabled) {
    try {
      const nodemailer = require('nodemailer');
      emailClient = nodemailer.createTransport(config.email.transport);
      logger.info('Email notifications initialized');
    } catch (error) {
      logger.error('Failed to initialize email notifications:', error);
    }
  }
  
  // Initialize Slack if enabled
  if (config.slack.enabled) {
    try {
      const { WebClient } = require('@slack/web-api');
      slackClient = new WebClient(config.slack.token);
      logger.info('Slack notifications initialized');
    } catch (error) {
      logger.error('Failed to initialize Slack notifications:', error);
    }
  }
  
  // Initialize PagerDuty if enabled
  if (config.pagerDuty.enabled) {
    try {
      const pdClient = require('@pagerduty/pdjs');
      pagerDutyClient = pdClient.api({
        token: config.pagerDuty.token,
        tokenType: 'token'
      });
      logger.info('PagerDuty notifications initialized');
    } catch (error) {
      logger.error('Failed to initialize PagerDuty notifications:', error);
    }
  }
};

// Initialize channels
initializeChannels();

/**
 * Create a new alert
 * @param {string} name - Alert name
 * @param {string} description - Alert description
 * @param {Object} options - Alert options
 * @returns {Object} - Alert object
 */
const createAlert = (name, description, options = {}) => {
  // Default options
  const defaultOptions = {
    severity: SEVERITY.WARNING,
    threshold: null,
    windowSize: 5 * 60 * 1000, // 5 minutes
    minSampleSize: 1,
    cooldown: 15 * 60 * 1000, // 15 minutes
    channels: {
      email: config.email.enabled,
      slack: config.slack.enabled,
      pagerDuty: config.pagerDuty.enabled && options.severity === SEVERITY.CRITICAL
    }
  };
  
  // Merge options
  const mergedOptions = { ...defaultOptions, ...options };
  
  // Create alert object
  const alert = {
    name,
    description,
    options: mergedOptions,
    
    /**
     * Trigger the alert
     * @param {Object} data - Alert data
     */
    trigger: async (data) => {
      try {
        // Check if alert is in cooldown
        const cooldownKey = `alert:cooldown:${name}`;
        const inCooldown = await redisClient.get(cooldownKey);
        
        if (inCooldown) {
          logger.debug(`Alert ${name} is in cooldown, skipping`);
          return;
        }
        
        // Get alert state
        const stateKey = `alert:state:${name}`;
        const state = await redisClient.get(stateKey);
        const isActive = state === STATUS.ACTIVE;
        
        // Prepare alert payload
        const payload = {
          name,
          description,
          severity: mergedOptions.severity,
          status: STATUS.ACTIVE,
          timestamp: new Date().toISOString(),
          data
        };
        
        // Log alert
        logger.warn(`Alert triggered: ${name}`, payload);
        
        // Send notifications if alert is not already active
        if (!isActive) {
          // Send email notification
          if (mergedOptions.channels.email && emailClient) {
            sendEmailNotification(payload);
          }
          
          // Send Slack notification
          if (mergedOptions.channels.slack && slackClient) {
            sendSlackNotification(payload);
          }
          
          // Send PagerDuty notification
          if (mergedOptions.channels.pagerDuty && pagerDutyClient) {
            sendPagerDutyNotification(payload);
          }
        }
        
        // Update alert state
        await redisClient.set(stateKey, STATUS.ACTIVE);
        
        // Set cooldown
        await redisClient.set(cooldownKey, '1');
        await redisClient.expire(cooldownKey, Math.floor(mergedOptions.cooldown / 1000));
        
        // Store alert in history
        storeAlertHistory(payload);
      } catch (error) {
        logger.error(`Failed to trigger alert ${name}:`, error);
      }
    },
    
    /**
     * Resolve the alert
     * @param {Object} data - Resolution data
     */
    resolve: async (data) => {
      try {
        // Get alert state
        const stateKey = `alert:state:${name}`;
        const state = await redisClient.get(stateKey);
        
        // Only resolve if alert is active
        if (state === STATUS.ACTIVE) {
          // Prepare resolution payload
          const payload = {
            name,
            description,
            severity: mergedOptions.severity,
            status: STATUS.RESOLVED,
            timestamp: new Date().toISOString(),
            data
          };
          
          // Log resolution
          logger.info(`Alert resolved: ${name}`, payload);
          
          // Send notifications
          if (mergedOptions.channels.email && emailClient) {
            sendEmailNotification(payload);
          }
          
          if (mergedOptions.channels.slack && slackClient) {
            sendSlackNotification(payload);
          }
          
          if (mergedOptions.channels.pagerDuty && pagerDutyClient) {
            resolvePagerDutyIncident(payload);
          }
          
          // Update alert state
          await redisClient.set(stateKey, STATUS.RESOLVED);
          
          // Store resolution in history
          storeAlertHistory(payload);
        }
      } catch (error) {
        logger.error(`Failed to resolve alert ${name}:`, error);
      }
    }
  };
  
  return alert;
};

/**
 * Send email notification
 * @param {Object} payload - Alert payload
 */
const sendEmailNotification = async (payload) => {
  try {
    // Skip if email client is not initialized
    if (!emailClient) return;
    
    // Prepare email content
    const subject = `[${payload.severity.toUpperCase()}] ${payload.status === STATUS.ACTIVE ? 'Alert' : 'Resolved'}: ${payload.name}`;
    const text = `
      ${payload.description}
      
      Status: ${payload.status}
      Severity: ${payload.severity}
      Time: ${payload.timestamp}
      
      Details:
      ${JSON.stringify(payload.data, null, 2)}
    `;
    
    // Send email
    await emailClient.sendMail({
      from: config.email.from,
      to: config.email.to,
      subject,
      text
    });
    
    logger.debug(`Email notification sent for alert ${payload.name}`);
  } catch (error) {
    logger.error(`Failed to send email notification for alert ${payload.name}:`, error);
  }
};

/**
 * Send Slack notification
 * @param {Object} payload - Alert payload
 */
const sendSlackNotification = async (payload) => {
  try {
    // Skip if Slack client is not initialized
    if (!slackClient) return;
    
    // Determine color based on severity
    let color;
    switch (payload.severity) {
      case SEVERITY.INFO:
        color = '#2196F3'; // Blue
        break;
      case SEVERITY.WARNING:
        color = '#FF9800'; // Orange
        break;
      case SEVERITY.ERROR:
        color = '#F44336'; // Red
        break;
      case SEVERITY.CRITICAL:
        color = '#9C27B0'; // Purple
        break;
      default:
        color = '#9E9E9E'; // Grey
    }
    
    // Prepare Slack message
    const message = {
      channel: config.slack.channel,
      attachments: [
        {
          color,
          title: `${payload.status === STATUS.ACTIVE ? '🔔' : '✅'} ${payload.name}`,
          text: payload.description,
          fields: [
            {
              title: 'Status',
              value: payload.status,
              short: true
            },
            {
              title: 'Severity',
              value: payload.severity,
              short: true
            },
            {
              title: 'Time',
              value: payload.timestamp,
              short: true
            },
            {
              title: 'Details',
              value: '```' + JSON.stringify(payload.data, null, 2) + '```',
              short: false
            }
          ],
          footer: 'NovaFuse Monitoring',
          ts: Math.floor(new Date(payload.timestamp).getTime() / 1000)
        }
      ]
    };
    
    // Send Slack message
    await slackClient.chat.postMessage(message);
    
    logger.debug(`Slack notification sent for alert ${payload.name}`);
  } catch (error) {
    logger.error(`Failed to send Slack notification for alert ${payload.name}:`, error);
  }
};

/**
 * Send PagerDuty notification
 * @param {Object} payload - Alert payload
 */
const sendPagerDutyNotification = async (payload) => {
  try {
    // Skip if PagerDuty client is not initialized
    if (!pagerDutyClient) return;
    
    // Prepare PagerDuty event
    const event = {
      routing_key: config.pagerDuty.routingKey,
      event_action: 'trigger',
      dedup_key: payload.name,
      payload: {
        summary: `${payload.name}: ${payload.description}`,
        source: 'NovaFuse',
        severity: payload.severity === SEVERITY.CRITICAL ? 'critical' : 'warning',
        timestamp: payload.timestamp,
        custom_details: payload.data
      }
    };
    
    // Send PagerDuty event
    await pagerDutyClient.post('/events/v2/enqueue', { data: event });
    
    logger.debug(`PagerDuty notification sent for alert ${payload.name}`);
  } catch (error) {
    logger.error(`Failed to send PagerDuty notification for alert ${payload.name}:`, error);
  }
};

/**
 * Resolve PagerDuty incident
 * @param {Object} payload - Alert payload
 */
const resolvePagerDutyIncident = async (payload) => {
  try {
    // Skip if PagerDuty client is not initialized
    if (!pagerDutyClient) return;
    
    // Prepare PagerDuty event
    const event = {
      routing_key: config.pagerDuty.routingKey,
      event_action: 'resolve',
      dedup_key: payload.name,
      payload: {
        summary: `Resolved: ${payload.name}`,
        source: 'NovaFuse',
        timestamp: payload.timestamp
      }
    };
    
    // Send PagerDuty event
    await pagerDutyClient.post('/events/v2/enqueue', { data: event });
    
    logger.debug(`PagerDuty incident resolved for alert ${payload.name}`);
  } catch (error) {
    logger.error(`Failed to resolve PagerDuty incident for alert ${payload.name}:`, error);
  }
};

/**
 * Store alert in history
 * @param {Object} payload - Alert payload
 */
const storeAlertHistory = async (payload) => {
  try {
    // Create key for alert history
    const key = `alert:history:${payload.name}:${Date.now()}`;
    
    // Store alert in Redis
    await redisClient.set(key, JSON.stringify(payload));
    
    // Set TTL for alert history (30 days)
    await redisClient.expire(key, 30 * 24 * 60 * 60);
  } catch (error) {
    logger.error(`Failed to store alert history for ${payload.name}:`, error);
  }
};

/**
 * Get alert history
 * @param {string} name - Alert name
 * @param {number} limit - Maximum number of alerts to return
 * @returns {Array} - Alert history
 */
const getAlertHistory = async (name, limit = 100) => {
  try {
    // Get all keys for the alert
    const keys = await redisClient.keys(`alert:history:${name}:*`);
    
    // Sort keys by timestamp (descending)
    keys.sort((a, b) => {
      const timestampA = parseInt(a.split(':').pop(), 10);
      const timestampB = parseInt(b.split(':').pop(), 10);
      return timestampB - timestampA;
    });
    
    // Limit number of keys
    const limitedKeys = keys.slice(0, limit);
    
    // Get alerts for keys
    const alerts = [];
    
    for (const key of limitedKeys) {
      const data = await redisClient.get(key);
      if (data) {
        alerts.push(JSON.parse(data));
      }
    }
    
    return alerts;
  } catch (error) {
    logger.error(`Failed to get alert history for ${name}:`, error);
    return [];
  }
};

module.exports = {
  createAlert,
  getAlertHistory,
  SEVERITY,
  STATUS
};
