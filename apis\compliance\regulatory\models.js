/**
 * Models for the Regulatory Compliance API
 */

// Sample data for regulatory frameworks
const regulatoryFrameworks = [
  {
    id: 'rf-0001',
    name: 'General Data Protection Regulation',
    shortName: 'GDPR',
    description: 'EU regulation on data protection and privacy for all individuals within the European Union and the European Economic Area.',
    version: '2016/679',
    effectiveDate: '2018-05-25',
    category: 'privacy',
    jurisdiction: 'eu',
    status: 'active',
    website: 'https://gdpr.eu/',
    enforcementAuthority: 'Data Protection Authorities of EU Member States',
    lastUpdated: '2021-06-15T10:30:00Z',
    createdAt: '2021-01-10T08:15:00Z',
    updatedAt: '2021-06-15T10:30:00Z'
  },
  {
    id: 'rf-0002',
    name: 'Health Insurance Portability and Accountability Act',
    shortName: 'HIPAA',
    description: 'US legislation that provides data privacy and security provisions for safeguarding medical information.',
    version: '1996, amended 2013',
    effectiveDate: '1996-08-21',
    category: 'healthcare',
    jurisdiction: 'us',
    status: 'active',
    website: 'https://www.hhs.gov/hipaa/index.html',
    enforcementAuthority: 'Office for Civil Rights (OCR)',
    lastUpdated: '2021-05-20T14:45:00Z',
    createdAt: '2021-01-15T09:20:00Z',
    updatedAt: '2021-05-20T14:45:00Z'
  },
  {
    id: 'rf-0003',
    name: 'California Consumer Privacy Act',
    shortName: 'CCPA',
    description: 'State statute intended to enhance privacy rights and consumer protection for residents of California.',
    version: 'AB-375',
    effectiveDate: '2020-01-01',
    category: 'privacy',
    jurisdiction: 'us-ca',
    status: 'active',
    website: 'https://oag.ca.gov/privacy/ccpa',
    enforcementAuthority: 'California Attorney General',
    lastUpdated: '2021-04-10T11:25:00Z',
    createdAt: '2021-01-20T10:30:00Z',
    updatedAt: '2021-04-10T11:25:00Z'
  },
  {
    id: 'rf-0004',
    name: 'Sarbanes-Oxley Act',
    shortName: 'SOX',
    description: 'US federal law that mandates certain practices in financial record keeping and reporting for corporations.',
    version: '2002',
    effectiveDate: '2002-07-30',
    category: 'financial',
    jurisdiction: 'us',
    status: 'active',
    website: 'https://www.sec.gov/spotlight/sarbanes-oxley.htm',
    enforcementAuthority: 'Securities and Exchange Commission (SEC)',
    lastUpdated: '2021-03-15T13:40:00Z',
    createdAt: '2021-01-25T11:45:00Z',
    updatedAt: '2021-03-15T13:40:00Z'
  },
  {
    id: 'rf-0005',
    name: 'Payment Card Industry Data Security Standard',
    shortName: 'PCI DSS',
    description: 'Information security standard for organizations that handle branded credit cards from the major card schemes.',
    version: '4.0',
    effectiveDate: '2022-03-31',
    category: 'financial',
    jurisdiction: 'global',
    status: 'active',
    website: 'https://www.pcisecuritystandards.org/',
    enforcementAuthority: 'Payment Card Industry Security Standards Council',
    lastUpdated: '2022-04-01T09:15:00Z',
    createdAt: '2021-02-01T12:50:00Z',
    updatedAt: '2022-04-01T09:15:00Z'
  }
];

// Sample data for regulatory requirements
const regulatoryRequirements = [
  {
    id: 'rr-0001',
    frameworkId: 'rf-0001', // GDPR
    code: 'GDPR-A5',
    name: 'Principles relating to processing of personal data',
    description: 'Personal data shall be processed lawfully, fairly and in a transparent manner in relation to the data subject.',
    article: 'Article 5',
    section: '1(a)',
    category: 'data-processing',
    priority: 'high',
    status: 'applicable',
    applicableJurisdictions: ['eu', 'eea'],
    controlObjectives: [
      'Ensure lawful basis for processing',
      'Provide transparent information to data subjects',
      'Process data fairly and without discrimination'
    ],
    relatedRequirements: ['rr-0002', 'rr-0003'],
    createdAt: '2021-01-10T09:00:00Z',
    updatedAt: '2021-06-15T11:00:00Z'
  },
  {
    id: 'rr-0002',
    frameworkId: 'rf-0001', // GDPR
    code: 'GDPR-A6',
    name: 'Lawfulness of processing',
    description: 'Processing shall be lawful only if and to the extent that at least one of the specified conditions applies.',
    article: 'Article 6',
    section: '1',
    category: 'data-processing',
    priority: 'high',
    status: 'applicable',
    applicableJurisdictions: ['eu', 'eea'],
    controlObjectives: [
      'Establish valid lawful basis for processing',
      'Document lawful basis for each processing activity',
      'Review and update lawful basis as necessary'
    ],
    relatedRequirements: ['rr-0001', 'rr-0003'],
    createdAt: '2021-01-10T09:15:00Z',
    updatedAt: '2021-06-15T11:15:00Z'
  },
  {
    id: 'rr-0003',
    frameworkId: 'rf-0001', // GDPR
    code: 'GDPR-A7',
    name: 'Conditions for consent',
    description: 'Where processing is based on consent, the controller shall be able to demonstrate that the data subject has consented to processing of his or her personal data.',
    article: 'Article 7',
    section: '1',
    category: 'consent',
    priority: 'high',
    status: 'applicable',
    applicableJurisdictions: ['eu', 'eea'],
    controlObjectives: [
      'Implement demonstrable consent mechanisms',
      'Ensure consent is freely given, specific, informed and unambiguous',
      'Provide easy withdrawal of consent'
    ],
    relatedRequirements: ['rr-0001', 'rr-0002'],
    createdAt: '2021-01-10T09:30:00Z',
    updatedAt: '2021-06-15T11:30:00Z'
  },
  {
    id: 'rr-0004',
    frameworkId: 'rf-0002', // HIPAA
    code: 'HIPAA-P',
    name: 'Privacy Rule',
    description: 'Establishes national standards to protect individuals\' medical records and other personal health information.',
    article: '45 CFR Part 160',
    section: 'Subpart A',
    category: 'privacy',
    priority: 'high',
    status: 'applicable',
    applicableJurisdictions: ['us'],
    controlObjectives: [
      'Limit uses and disclosures of PHI',
      'Provide patients with rights over their health information',
      'Implement administrative safeguards'
    ],
    relatedRequirements: ['rr-0005'],
    createdAt: '2021-01-15T10:00:00Z',
    updatedAt: '2021-05-20T15:00:00Z'
  },
  {
    id: 'rr-0005',
    frameworkId: 'rf-0002', // HIPAA
    code: 'HIPAA-S',
    name: 'Security Rule',
    description: 'Establishes national standards to protect individuals\' electronic personal health information that is created, received, used, or maintained by a covered entity.',
    article: '45 CFR Part 160',
    section: 'Subpart C',
    category: 'security',
    priority: 'high',
    status: 'applicable',
    applicableJurisdictions: ['us'],
    controlObjectives: [
      'Implement administrative safeguards',
      'Implement physical safeguards',
      'Implement technical safeguards'
    ],
    relatedRequirements: ['rr-0004'],
    createdAt: '2021-01-15T10:15:00Z',
    updatedAt: '2021-05-20T15:15:00Z'
  }
];

// Sample data for jurisdictions
const jurisdictions = [
  {
    id: 'j-0001',
    code: 'global',
    name: 'Global',
    type: 'global',
    description: 'Applicable worldwide',
    createdAt: '2021-01-05T08:00:00Z',
    updatedAt: '2021-01-05T08:00:00Z'
  },
  {
    id: 'j-0002',
    code: 'eu',
    name: 'European Union',
    type: 'region',
    description: 'Member states of the European Union',
    parentJurisdiction: 'global',
    createdAt: '2021-01-05T08:15:00Z',
    updatedAt: '2021-01-05T08:15:00Z'
  },
  {
    id: 'j-0003',
    code: 'eea',
    name: 'European Economic Area',
    type: 'region',
    description: 'Member states of the European Economic Area',
    parentJurisdiction: 'global',
    createdAt: '2021-01-05T08:30:00Z',
    updatedAt: '2021-01-05T08:30:00Z'
  },
  {
    id: 'j-0004',
    code: 'us',
    name: 'United States',
    type: 'country',
    description: 'United States of America',
    parentJurisdiction: 'global',
    createdAt: '2021-01-05T08:45:00Z',
    updatedAt: '2021-01-05T08:45:00Z'
  },
  {
    id: 'j-0005',
    code: 'us-ca',
    name: 'California',
    type: 'state',
    description: 'State of California, USA',
    parentJurisdiction: 'us',
    createdAt: '2021-01-05T09:00:00Z',
    updatedAt: '2021-01-05T09:00:00Z'
  }
];

// Sample data for regulatory changes
const regulatoryChanges = [
  {
    id: 'rc-0001',
    frameworkId: 'rf-0001', // GDPR
    title: 'Updated Guidelines on Consent',
    description: 'European Data Protection Board published updated guidelines on consent under GDPR.',
    changeType: 'guidance',
    publicationDate: '2021-05-10',
    effectiveDate: '2021-06-01',
    source: 'European Data Protection Board',
    sourceUrl: 'https://edpb.europa.eu/guidelines',
    impactLevel: 'medium',
    affectedRequirements: ['rr-0003'],
    status: 'published',
    summary: 'The updated guidelines clarify requirements for valid consent, particularly in the context of cookie banners and tracking technologies.',
    createdAt: '2021-05-12T10:00:00Z',
    updatedAt: '2021-05-12T10:00:00Z'
  },
  {
    id: 'rc-0002',
    frameworkId: 'rf-0002', // HIPAA
    title: 'HIPAA Safe Harbor Act',
    description: 'New legislation providing safe harbor for HIPAA-covered entities that implement recognized cybersecurity practices.',
    changeType: 'legislation',
    publicationDate: '2021-01-05',
    effectiveDate: '2021-01-05',
    source: 'U.S. Congress',
    sourceUrl: 'https://www.congress.gov/bill/116th-congress/house-bill/7898',
    impactLevel: 'high',
    affectedRequirements: ['rr-0005'],
    status: 'in-effect',
    summary: 'The HIPAA Safe Harbor Act amends the HITECH Act to require the Department of Health and Human Services to consider whether covered entities and business associates have adequately demonstrated that they had recognized security practices in place for at least 12 months.',
    createdAt: '2021-01-10T14:30:00Z',
    updatedAt: '2021-01-10T14:30:00Z'
  }
];

// Sample data for regulatory reports
const regulatoryReports = [
  {
    id: 'rpt-0001',
    frameworkId: 'rf-0001', // GDPR
    name: 'GDPR Annual Compliance Report',
    description: 'Annual report documenting GDPR compliance status and activities.',
    type: 'internal',
    frequency: 'annual',
    lastSubmissionDate: '2021-12-15',
    nextDueDate: '2022-12-15',
    assignee: 'Data Protection Officer',
    status: 'completed',
    templateUrl: '/templates/gdpr-annual-report.docx',
    sections: [
      'Executive Summary',
      'Data Processing Activities',
      'Data Subject Rights Requests',
      'Data Breaches',
      'DPIAs Conducted',
      'Training and Awareness',
      'Recommendations'
    ],
    createdAt: '2021-01-20T11:00:00Z',
    updatedAt: '2021-12-16T09:30:00Z'
  },
  {
    id: 'rpt-0002',
    frameworkId: 'rf-0002', // HIPAA
    name: 'HIPAA Security Risk Assessment',
    description: 'Annual assessment of security risks and vulnerabilities as required by the HIPAA Security Rule.',
    type: 'internal',
    frequency: 'annual',
    lastSubmissionDate: '2021-11-10',
    nextDueDate: '2022-11-10',
    assignee: 'Security Officer',
    status: 'completed',
    templateUrl: '/templates/hipaa-risk-assessment.xlsx',
    sections: [
      'Administrative Safeguards',
      'Physical Safeguards',
      'Technical Safeguards',
      'Organizational Requirements',
      'Policies and Procedures',
      'Risk Analysis',
      'Risk Management Plan'
    ],
    createdAt: '2021-01-25T13:15:00Z',
    updatedAt: '2021-11-12T10:45:00Z'
  }
];

// Export all models
module.exports = {
  regulatoryFrameworks,
  regulatoryRequirements,
  jurisdictions,
  regulatoryChanges,
  regulatoryReports
};
