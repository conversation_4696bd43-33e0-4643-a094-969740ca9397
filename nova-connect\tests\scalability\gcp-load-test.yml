config:
  target: "{{ $processEnvironment.TARGET_URL }}"
  phases:
    - duration: 60
      arrivalRate: 10
      name: "Warm up"
    - duration: 300
      arrivalRate: 10
      rampTo: 100
      name: "Ramp up"
    - duration: 300
      arrivalRate: 100
      name: "Sustained load"
    - duration: 60
      arrivalRate: 200
      name: "Peak load"
    - duration: 60
      arrivalRate: 10
      name: "Cool down"
  environments:
    gcp:
      target: "https://{{ $processEnvironment.GCP_SERVICE_URL }}"
      phases:
        - duration: 60
          arrivalRate: 10
          name: "Warm up"
        - duration: 300
          arrivalRate: 10
          rampTo: 200
          name: "Ramp up"
        - duration: 300
          arrivalRate: 200
          name: "Sustained load"
        - duration: 60
          arrivalRate: 500
          name: "Peak load"
        - duration: 60
          arrivalRate: 10
          name: "Cool down"
  plugins:
    metrics-by-endpoint: {}
    expect: {}
    cloud-metrics:
      provider: "gcp"
      projectId: "{{ $processEnvironment.GCP_PROJECT_ID }}"
  http:
    timeout: 30
  processor: "./gcp-load-test-functions.js"
  defaults:
    headers:
      x-api-key: "{{ $processEnvironment.API_KEY }}"
      Content-Type: "application/json"
      Accept: "application/json"

scenarios:
  - name: "Health Check"
    weight: 10
    flow:
      - get:
          url: "/health"
          expect:
            - statusCode: 200
            - contentType: "application/json"

  - name: "API Status"
    weight: 10
    flow:
      - get:
          url: "/api/v1/status"
          expect:
            - statusCode: 200
            - contentType: "application/json"

  - name: "Connector Operations"
    weight: 30
    flow:
      - function: "generateConnectorPayload"
      - post:
          url: "/api/v1/connectors"
          json: "{{ connector }}"
          capture:
            - json: "$.id"
              as: "connectorId"
          expect:
            - statusCode: 201
      - get:
          url: "/api/v1/connectors/{{ connectorId }}"
          expect:
            - statusCode: 200
      - function: "updateConnectorPayload"
      - put:
          url: "/api/v1/connectors/{{ connectorId }}"
          json: "{{ updatedConnector }}"
          expect:
            - statusCode: 200
      - delete:
          url: "/api/v1/connectors/{{ connectorId }}"
          expect:
            - statusCode: 204

  - name: "Data Normalization"
    weight: 30
    flow:
      - function: "generateNormalizationPayload"
      - post:
          url: "/api/v1/normalize"
          json: "{{ normalization }}"
          expect:
            - statusCode: 200
            - contentType: "application/json"

  - name: "Endpoint Operations"
    weight: 20
    flow:
      - function: "generateEndpointPayload"
      - post:
          url: "/api/v1/endpoints"
          json: "{{ endpoint }}"
          capture:
            - json: "$.id"
              as: "endpointId"
          expect:
            - statusCode: 201
      - get:
          url: "/api/v1/endpoints/{{ endpointId }}"
          expect:
            - statusCode: 200
      - function: "updateEndpointPayload"
      - put:
          url: "/api/v1/endpoints/{{ endpointId }}"
          json: "{{ updatedEndpoint }}"
          expect:
            - statusCode: 200
      - delete:
          url: "/api/v1/endpoints/{{ endpointId }}"
          expect:
            - statusCode: 204
