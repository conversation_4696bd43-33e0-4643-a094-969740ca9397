/**
 * Theme Service
 * 
 * This service handles theme management for custom branding.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const AuditService = require('./AuditService');

class ThemeService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.themesDir = path.join(this.dataDir, 'themes');
    this.themesFile = path.join(this.themesDir, 'themes.json');
    this.organizationThemesFile = path.join(this.themesDir, 'organization_themes.json');
    this.auditService = new AuditService(dataDir);
    
    // Define default theme
    this.defaultTheme = {
      id: 'default',
      name: 'Default',
      description: 'Default NovaConnect theme',
      isSystem: true,
      colors: {
        primary: '#1976d2',
        secondary: '#dc004e',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196f3',
        success: '#4caf50',
        background: '#f5f5f5',
        surface: '#ffffff',
        text: {
          primary: 'rgba(0, 0, 0, 0.87)',
          secondary: 'rgba(0, 0, 0, 0.54)',
          disabled: 'rgba(0, 0, 0, 0.38)',
          hint: 'rgba(0, 0, 0, 0.38)'
        }
      },
      typography: {
        fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
        fontSize: 14,
        fontWeightLight: 300,
        fontWeightRegular: 400,
        fontWeightMedium: 500,
        fontWeightBold: 700
      },
      shape: {
        borderRadius: 4
      },
      spacing: 8,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    // Define dark theme
    this.darkTheme = {
      id: 'dark',
      name: 'Dark',
      description: 'Dark NovaConnect theme',
      isSystem: true,
      colors: {
        primary: '#90caf9',
        secondary: '#f48fb1',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196f3',
        success: '#4caf50',
        background: '#121212',
        surface: '#1e1e1e',
        text: {
          primary: 'rgba(255, 255, 255, 0.87)',
          secondary: 'rgba(255, 255, 255, 0.54)',
          disabled: 'rgba(255, 255, 255, 0.38)',
          hint: 'rgba(255, 255, 255, 0.38)'
        }
      },
      typography: {
        fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
        fontSize: 14,
        fontWeightLight: 300,
        fontWeightRegular: 400,
        fontWeightMedium: 500,
        fontWeightBold: 700
      },
      shape: {
        borderRadius: 4
      },
      spacing: 8,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.themesDir, { recursive: true });
      
      // Initialize files if they don't exist
      await this.initializeFile(this.themesFile, [this.defaultTheme, this.darkTheme]);
      await this.initializeFile(this.organizationThemesFile, []);
    } catch (error) {
      console.error('Error creating themes directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get all themes
   */
  async getAllThemes() {
    return this.loadData(this.themesFile);
  }

  /**
   * Get theme by ID
   */
  async getThemeById(id) {
    const themes = await this.loadData(this.themesFile);
    const theme = themes.find(t => t.id === id);
    
    if (!theme) {
      throw new NotFoundError(`Theme with ID ${id} not found`);
    }
    
    return theme;
  }

  /**
   * Create a new theme
   */
  async createTheme(data, userId) {
    if (!data.name) {
      throw new ValidationError('Theme name is required');
    }
    
    const themes = await this.loadData(this.themesFile);
    
    // Check for duplicate name
    if (themes.some(t => t.name.toLowerCase() === data.name.toLowerCase() && !t.isSystem)) {
      throw new ValidationError(`Theme with name "${data.name}" already exists`);
    }
    
    // Create new theme
    const newTheme = {
      id: uuidv4(),
      name: data.name,
      description: data.description || '',
      isSystem: false,
      colors: data.colors || this.defaultTheme.colors,
      typography: data.typography || this.defaultTheme.typography,
      shape: data.shape || this.defaultTheme.shape,
      spacing: data.spacing || this.defaultTheme.spacing,
      createdBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    themes.push(newTheme);
    await this.saveData(this.themesFile, themes);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'CREATE',
      resourceType: 'theme',
      resourceId: newTheme.id,
      details: {
        name: newTheme.name
      }
    });
    
    return newTheme;
  }

  /**
   * Update a theme
   */
  async updateTheme(id, data, userId) {
    const themes = await this.loadData(this.themesFile);
    const index = themes.findIndex(t => t.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Theme with ID ${id} not found`);
    }
    
    const theme = themes[index];
    
    // Check if trying to update a system theme
    if (theme.isSystem) {
      throw new ValidationError('Cannot update system themes');
    }
    
    // Check for duplicate name
    if (data.name && data.name !== theme.name && 
        themes.some(t => t.name.toLowerCase() === data.name.toLowerCase() && !t.isSystem)) {
      throw new ValidationError(`Theme with name "${data.name}" already exists`);
    }
    
    // Update theme
    const updatedTheme = {
      ...theme,
      name: data.name || theme.name,
      description: data.description !== undefined ? data.description : theme.description,
      colors: data.colors || theme.colors,
      typography: data.typography || theme.typography,
      shape: data.shape || theme.shape,
      spacing: data.spacing !== undefined ? data.spacing : theme.spacing,
      updated: new Date().toISOString()
    };
    
    themes[index] = updatedTheme;
    await this.saveData(this.themesFile, themes);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UPDATE',
      resourceType: 'theme',
      resourceId: id,
      details: {
        name: updatedTheme.name
      }
    });
    
    return updatedTheme;
  }

  /**
   * Delete a theme
   */
  async deleteTheme(id, userId) {
    const themes = await this.loadData(this.themesFile);
    const index = themes.findIndex(t => t.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Theme with ID ${id} not found`);
    }
    
    const theme = themes[index];
    
    // Check if trying to delete a system theme
    if (theme.isSystem) {
      throw new ValidationError('Cannot delete system themes');
    }
    
    // Check if theme is in use by any organization
    const organizationThemes = await this.loadData(this.organizationThemesFile);
    if (organizationThemes.some(ot => ot.themeId === id)) {
      throw new ValidationError('Cannot delete theme that is in use by organizations');
    }
    
    // Remove the theme
    themes.splice(index, 1);
    await this.saveData(this.themesFile, themes);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'DELETE',
      resourceType: 'theme',
      resourceId: id,
      details: {
        name: theme.name
      }
    });
    
    return { success: true, message: `Theme ${id} deleted` };
  }

  /**
   * Clone a theme
   */
  async cloneTheme(id, data, userId) {
    const sourceTheme = await this.getThemeById(id);
    
    // Create new theme based on source
    const newTheme = {
      id: uuidv4(),
      name: data.name || `${sourceTheme.name} (Copy)`,
      description: data.description || sourceTheme.description,
      isSystem: false,
      colors: JSON.parse(JSON.stringify(sourceTheme.colors)),
      typography: JSON.parse(JSON.stringify(sourceTheme.typography)),
      shape: JSON.parse(JSON.stringify(sourceTheme.shape)),
      spacing: sourceTheme.spacing,
      createdBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    const themes = await this.loadData(this.themesFile);
    
    // Check for duplicate name
    if (themes.some(t => t.name.toLowerCase() === newTheme.name.toLowerCase() && !t.isSystem)) {
      throw new ValidationError(`Theme with name "${newTheme.name}" already exists`);
    }
    
    themes.push(newTheme);
    await this.saveData(this.themesFile, themes);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'CLONE',
      resourceType: 'theme',
      resourceId: newTheme.id,
      details: {
        name: newTheme.name,
        sourceId: id,
        sourceName: sourceTheme.name
      }
    });
    
    return newTheme;
  }

  /**
   * Get organization theme
   */
  async getOrganizationTheme(organizationId) {
    const organizationThemes = await this.loadData(this.organizationThemesFile);
    const organizationTheme = organizationThemes.find(ot => ot.organizationId === organizationId);
    
    if (!organizationTheme) {
      // Return default theme if no theme is set for organization
      return this.getThemeById('default');
    }
    
    try {
      // Get the actual theme
      return await this.getThemeById(organizationTheme.themeId);
    } catch (error) {
      // If theme not found, return default theme
      if (error instanceof NotFoundError) {
        return this.getThemeById('default');
      }
      throw error;
    }
  }

  /**
   * Set organization theme
   */
  async setOrganizationTheme(organizationId, themeId, userId) {
    // Check if theme exists
    await this.getThemeById(themeId);
    
    const organizationThemes = await this.loadData(this.organizationThemesFile);
    const index = organizationThemes.findIndex(ot => ot.organizationId === organizationId);
    
    if (index === -1) {
      // Create new organization theme
      const newOrganizationTheme = {
        organizationId,
        themeId,
        updatedBy: userId,
        updated: new Date().toISOString()
      };
      
      organizationThemes.push(newOrganizationTheme);
    } else {
      // Update existing organization theme
      organizationThemes[index] = {
        ...organizationThemes[index],
        themeId,
        updatedBy: userId,
        updated: new Date().toISOString()
      };
    }
    
    await this.saveData(this.organizationThemesFile, organizationThemes);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'SET_THEME',
      resourceType: 'organization',
      resourceId: organizationId,
      details: {
        themeId
      }
    });
    
    return { success: true, message: `Theme set for organization ${organizationId}` };
  }

  /**
   * Reset organization theme to default
   */
  async resetOrganizationTheme(organizationId, userId) {
    const organizationThemes = await this.loadData(this.organizationThemesFile);
    const index = organizationThemes.findIndex(ot => ot.organizationId === organizationId);
    
    if (index === -1) {
      // Organization already using default theme
      return { success: true, message: `Organization ${organizationId} already using default theme` };
    }
    
    // Remove organization theme
    organizationThemes.splice(index, 1);
    await this.saveData(this.organizationThemesFile, organizationThemes);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'RESET_THEME',
      resourceType: 'organization',
      resourceId: organizationId
    });
    
    return { success: true, message: `Theme reset for organization ${organizationId}` };
  }

  /**
   * Get CSS variables for a theme
   */
  getCssVariables(theme) {
    const variables = [];
    
    // Add color variables
    variables.push(`--primary-color: ${theme.colors.primary};`);
    variables.push(`--secondary-color: ${theme.colors.secondary};`);
    variables.push(`--error-color: ${theme.colors.error};`);
    variables.push(`--warning-color: ${theme.colors.warning};`);
    variables.push(`--info-color: ${theme.colors.info};`);
    variables.push(`--success-color: ${theme.colors.success};`);
    variables.push(`--background-color: ${theme.colors.background};`);
    variables.push(`--surface-color: ${theme.colors.surface};`);
    variables.push(`--text-primary-color: ${theme.colors.text.primary};`);
    variables.push(`--text-secondary-color: ${theme.colors.text.secondary};`);
    variables.push(`--text-disabled-color: ${theme.colors.text.disabled};`);
    variables.push(`--text-hint-color: ${theme.colors.text.hint};`);
    
    // Add typography variables
    variables.push(`--font-family: ${theme.typography.fontFamily};`);
    variables.push(`--font-size: ${theme.typography.fontSize}px;`);
    variables.push(`--font-weight-light: ${theme.typography.fontWeightLight};`);
    variables.push(`--font-weight-regular: ${theme.typography.fontWeightRegular};`);
    variables.push(`--font-weight-medium: ${theme.typography.fontWeightMedium};`);
    variables.push(`--font-weight-bold: ${theme.typography.fontWeightBold};`);
    
    // Add shape variables
    variables.push(`--border-radius: ${theme.shape.borderRadius}px;`);
    
    // Add spacing variable
    variables.push(`--spacing: ${theme.spacing}px;`);
    
    return `:root {\n  ${variables.join('\n  ')}\n}`;
  }

  /**
   * Get theme CSS
   */
  async getThemeCss(themeId) {
    const theme = await this.getThemeById(themeId);
    return this.getCssVariables(theme);
  }

  /**
   * Get organization theme CSS
   */
  async getOrganizationThemeCss(organizationId) {
    const theme = await this.getOrganizationTheme(organizationId);
    return this.getCssVariables(theme);
  }

  /**
   * Get theme preview
   */
  getThemePreview(theme) {
    // Generate a preview object with key theme properties
    return {
      id: theme.id,
      name: theme.name,
      colors: {
        primary: theme.colors.primary,
        secondary: theme.colors.secondary,
        background: theme.colors.background,
        surface: theme.colors.surface,
        text: theme.colors.text.primary
      },
      borderRadius: theme.shape.borderRadius,
      fontFamily: theme.typography.fontFamily
    };
  }
}

module.exports = ThemeService;
