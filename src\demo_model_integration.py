"""
Demonstrate the model integration capabilities of the UCIA.

This script shows how to use different language models with the UCIA.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the UCIA
from ucia import UCIA

def demo_model_integration():
    """Demonstrate the model integration capabilities of the UCIA."""
    logger.info("Demonstrating model integration capabilities")
    
    # Get available models
    logger.info("Getting available models...")
    available_models = UCIA.get_available_models()
    logger.info(f"Available models:")
    for model in available_models:
        logger.info(f"  - {model['name']} ({model['id']})")
        logger.info(f"    {model['description']}")
        logger.info(f"    Path: {model['path']}")
    
    # Initialize UCIA with the default model
    logger.info("\nInitializing UCIA with the default model...")
    ucia = UCIA(model_name="gpt2")  # Use a small model for testing
    
    # Test queries
    test_queries = [
        "What are the GDPR requirements for data breach notification?",
        "How does HIPAA define a security incident?",
        "What are the SOC2 requirements for incident response?"
    ]
    
    for query in test_queries:
        logger.info(f"\nProcessing query with default model: {query}")
        response = ucia.process_query(query)
        logger.info(f"Response: {response['response_text']}")
    
    # If a fine-tuned model is available, test it
    fine_tuned_models = [model for model in available_models if "Fine-tuned" in model['name']]
    if fine_tuned_models:
        fine_tuned_model = fine_tuned_models[0]
        logger.info(f"\nInitializing UCIA with fine-tuned model: {fine_tuned_model['name']}...")
        fine_tuned_ucia = UCIA(model_name=fine_tuned_model['path'])
        
        for query in test_queries:
            logger.info(f"\nProcessing query with fine-tuned model: {query}")
            response = fine_tuned_ucia.process_query(query)
            logger.info(f"Response: {response['response_text']}")
    else:
        logger.info("\nNo fine-tuned models available. Run the fine-tuning pipeline to create one.")
    
    logger.info("\nModel integration demonstration complete!")

if __name__ == "__main__":
    demo_model_integration()
