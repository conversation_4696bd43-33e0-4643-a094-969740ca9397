/**
 * Cyber-Safety Visualization Routes
 *
 * This file defines the routes for the Cyber-Safety visualization API.
 */

const express = require('express');
const router = express.Router();
const { asyncHandler } = require('../middleware/asyncHandler');
const cyberSafetyVisualizationController = require('../controllers/CyberSafetyVisualizationController');
const { monitorApiEndpoint } = require('../../cyber-safety/monitoring/visualizationApiMonitor');

// Apply monitoring middleware to all routes
router.use(monitorApiEndpoint);

/**
 * @route   GET /api/cyber-safety/visualizations/tri-domain-tensor
 * @desc    Get Tri-Domain Tensor Visualization data
 * @access  Private
 */
router.get(
  '/tri-domain-tensor',
  asyncHandler(cyberSafetyVisualizationController.getTriDomainTensorData)
);

/**
 * @route   GET /api/cyber-safety/visualizations/harmony-index
 * @desc    Get Cyber-Safety Harmony Index data
 * @access  Private
 */
router.get(
  '/harmony-index',
  asyncHandler(cyberSafetyVisualizationController.getHarmonyIndexData)
);

/**
 * @route   GET /api/cyber-safety/visualizations/risk-control-fusion
 * @desc    Get Risk-Control Fusion Map data
 * @access  Private
 */
router.get(
  '/risk-control-fusion',
  asyncHandler(cyberSafetyVisualizationController.getRiskControlFusionData)
);

/**
 * @route   GET /api/cyber-safety/visualizations/resonance-spectrogram
 * @desc    Get Cyber-Safety Resonance Spectrogram data
 * @access  Private
 */
router.get(
  '/resonance-spectrogram',
  asyncHandler(cyberSafetyVisualizationController.getResonanceSpectrogramData)
);

/**
 * @route   GET /api/cyber-safety/visualizations/unified-compliance-security
 * @desc    Get Unified Compliance-Security Visualizer data
 * @access  Private
 */
router.get(
  '/unified-compliance-security',
  asyncHandler(cyberSafetyVisualizationController.getUnifiedComplianceSecurityData)
);

/**
 * @route   GET /api/cyber-safety/visualizations/:visualizationType/real-time
 * @desc    Get real-time visualization data
 * @access  Private
 */
router.get(
  '/:visualizationType/real-time',
  asyncHandler(cyberSafetyVisualizationController.getRealTimeData)
);

/**
 * @route   GET /api/cyber-safety/visualizations/:visualizationType/historical
 * @desc    Get historical visualization data
 * @access  Private
 */
router.get(
  '/:visualizationType/historical',
  asyncHandler(cyberSafetyVisualizationController.getHistoricalData)
);

/**
 * @route   GET /api/cyber-safety/visualizations/:visualizationType/export
 * @desc    Export visualization data
 * @access  Private
 */
router.get(
  '/:visualizationType/export',
  asyncHandler(cyberSafetyVisualizationController.exportData)
);

/**
 * @route   POST /api/cyber-safety/visualizations/:visualizationType/feedback
 * @desc    Submit visualization feedback
 * @access  Private
 */
router.post(
  '/:visualizationType/feedback',
  asyncHandler(cyberSafetyVisualizationController.submitFeedback)
);

module.exports = router;
