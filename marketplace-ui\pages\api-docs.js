import { useState } from "react";
import Head from "next/head";

export default function ApiDocs() {
  const [activeCategory, setActiveCategory] = useState("getting-started");
  const [activeApi, setActiveApi] = useState("overview");

  const categories = [
    { id: "getting-started", name: "Getting Started" },
    { id: "authentication", name: "Authentication" },
    { id: "privacy-api", name: "Privacy Management API" },
    { id: "security-api", name: "Security Assessment API" },
    { id: "compliance-api", name: "Compliance API" },
    { id: "control-api", name: "Control Testing API" },
    { id: "esg-api", name: "ESG API" },
    { id: "uac", name: "Universal API Connector" },
  ];

  const apiEndpoints = {
    "getting-started": [
      { id: "overview", name: "Overview" },
      { id: "quickstart", name: "Quick Start Guide" },
      { id: "installation", name: "Installation" },
    ],
    "authentication": [
      { id: "api-keys", name: "API Keys" },
      { id: "oauth", name: "OAuth 2.0" },
      { id: "jwt", name: "JWT Authentication" },
    ],
    "privacy-api": [
      { id: "privacy-overview", name: "Overview" },
      { id: "data-subjects", name: "Data Subjects" },
      { id: "consent-management", name: "Consent Management" },
      { id: "dsar", name: "DSAR Processing" },
    ],
    "security-api": [
      { id: "security-overview", name: "Overview" },
      { id: "vulnerability-scan", name: "Vulnerability Scanning" },
      { id: "threat-detection", name: "Threat Detection" },
    ],
    "compliance-api": [
      { id: "compliance-overview", name: "Overview" },
      { id: "frameworks", name: "Compliance Frameworks" },
      { id: "reporting", name: "Compliance Reporting" },
    ],
    "control-api": [
      { id: "control-overview", name: "Overview" },
      { id: "control-testing", name: "Control Testing" },
      { id: "evidence-collection", name: "Evidence Collection" },
    ],
    "esg-api": [
      { id: "esg-overview", name: "Overview" },
      { id: "metrics", name: "ESG Metrics" },
      { id: "reporting", name: "ESG Reporting" },
    ],
    "uac": [
      { id: "uac-overview", name: "Overview" },
      { id: "connector-templates", name: "Connector Templates" },
      { id: "connector-execution", name: "Connector Execution" },
    ],
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>NovaFuse API Documentation</title>
        <meta name="description" content="NovaFuse API Documentation - Comprehensive guides and references" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900">API Documentation</h1>
          <div className="flex space-x-4">
            <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
              API Reference
            </button>
            <button className="border border-blue-600 text-blue-600 px-4 py-2 rounded hover:bg-blue-50">
              Download SDK
            </button>
          </div>
        </div>

        <div className="flex flex-col md:flex-row">
          {/* Sidebar */}
          <div className="w-full md:w-64 flex-shrink-0 mb-6 md:mb-0 md:mr-8">
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="p-4 border-b border-gray-200">
                <h2 className="font-semibold text-gray-800">Documentation</h2>
              </div>
              <nav className="p-4">
                <ul className="space-y-6">
                  {categories.map((category) => (
                    <li key={category.id}>
                      <button
                        onClick={() => {
                          setActiveCategory(category.id);
                          setActiveApi(apiEndpoints[category.id][0].id);
                        }}
                        className={`text-left w-full font-medium ${
                          activeCategory === category.id
                            ? "text-blue-600"
                            : "text-gray-700 hover:text-blue-600"
                        }`}
                      >
                        {category.name}
                      </button>
                      {activeCategory === category.id && (
                        <ul className="mt-2 ml-4 space-y-2">
                          {apiEndpoints[category.id].map((endpoint) => (
                            <li key={endpoint.id}>
                              <button
                                onClick={() => setActiveApi(endpoint.id)}
                                className={`text-left w-full text-sm ${
                                  activeApi === endpoint.id
                                    ? "text-blue-600 font-medium"
                                    : "text-gray-600 hover:text-blue-600"
                                }`}
                              >
                                {endpoint.name}
                              </button>
                            </li>
                          ))}
                        </ul>
                      )}
                    </li>
                  ))}
                </ul>
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="bg-white rounded-lg shadow p-6">
              {/* Getting Started - Overview */}
              {activeCategory === "getting-started" && activeApi === "overview" && (
                <div>
                  <h2 className="text-2xl font-bold mb-4">NovaFuse API Overview</h2>
                  <p className="text-gray-700 mb-4">
                    Welcome to the NovaFuse API documentation. NovaFuse provides a comprehensive set of APIs for governance, risk, and compliance (GRC) management. Our APIs are designed to be easy to use, secure, and scalable.
                  </p>
                  <h3 className="text-xl font-semibold mb-2">Available APIs</h3>
                  <ul className="list-disc pl-6 mb-4 space-y-2">
                    <li><strong>Privacy Management API</strong> - Manage data subject requests, consent, and privacy compliance</li>
                    <li><strong>Security Assessment API</strong> - Scan for vulnerabilities and detect threats</li>
                    <li><strong>Compliance API</strong> - Manage compliance frameworks and reporting</li>
                    <li><strong>Control Testing API</strong> - Test controls and collect evidence</li>
                    <li><strong>ESG API</strong> - Track and report on environmental, social, and governance metrics</li>
                  </ul>
                  <h3 className="text-xl font-semibold mb-2">Universal API Connector</h3>
                  <p className="text-gray-700 mb-4">
                    The Universal API Connector (UAC) is a powerful tool that allows you to connect to any API using a simple, standardized interface. With the UAC, you can integrate with third-party services without writing custom code for each integration.
                  </p>
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                    <h4 className="text-lg font-semibold text-blue-800 mb-2">Getting Started</h4>
                    <p className="text-blue-700">
                      To get started with the NovaFuse API, you'll need to:
                    </p>
                    <ol className="list-decimal pl-6 mt-2 text-blue-700">
                      <li>Sign up for a NovaFuse account</li>
                      <li>Generate an API key</li>
                      <li>Choose the APIs you want to use</li>
                      <li>Start making requests</li>
                    </ol>
                  </div>
                </div>
              )}

              {/* Authentication - API Keys */}
              {activeCategory === "authentication" && activeApi === "api-keys" && (
                <div>
                  <h2 className="text-2xl font-bold mb-4">API Keys</h2>
                  <p className="text-gray-700 mb-4">
                    API keys are used to authenticate requests to the NovaFuse API. Each API key is associated with a specific user account and has specific permissions.
                  </p>
                  <h3 className="text-xl font-semibold mb-2">Generating an API Key</h3>
                  <p className="text-gray-700 mb-4">
                    To generate an API key, go to the API Keys section in your NovaFuse dashboard and click "Generate New Key". You'll need to specify a name for the key and select the permissions you want to grant.
                  </p>
                  <h3 className="text-xl font-semibold mb-2">Using API Keys</h3>
                  <p className="text-gray-700 mb-4">
                    To authenticate your requests, include your API key in the <code>X-API-Key</code> header:
                  </p>
                  <div className="bg-gray-800 text-white p-4 rounded-md mb-4 overflow-x-auto">
                    <pre>
                      {`curl -X GET "https://api.novafuse.com/v1/privacy/data-subjects" \\
  -H "X-API-Key: your_api_key_here"`}
                    </pre>
                  </div>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                    <h4 className="text-lg font-semibold text-yellow-800 mb-2">Security Warning</h4>
                    <p className="text-yellow-700">
                      Keep your API keys secure and never share them publicly. If you believe your API key has been compromised, you should regenerate it immediately.
                    </p>
                  </div>
                </div>
              )}

              {/* UAC - Overview */}
              {activeCategory === "uac" && activeApi === "uac-overview" && (
                <div>
                  <h2 className="text-2xl font-bold mb-4">Universal API Connector (UAC)</h2>
                  <p className="text-gray-700 mb-4">
                    The Universal API Connector (UAC) is a powerful tool that allows you to connect to any API using a simple, standardized interface. With the UAC, you can integrate with third-party services without writing custom code for each integration.
                  </p>
                  <h3 className="text-xl font-semibold mb-2">Key Features</h3>
                  <ul className="list-disc pl-6 mb-4 space-y-2">
                    <li><strong>Standardized Interface</strong> - Connect to any API using a consistent interface</li>
                    <li><strong>Connector Templates</strong> - Pre-built templates for popular services</li>
                    <li><strong>Custom Connectors</strong> - Create your own connectors for any API</li>
                    <li><strong>Secure Credential Storage</strong> - Safely store and manage API credentials</li>
                    <li><strong>Automatic Retries</strong> - Handle transient errors with configurable retry policies</li>
                  </ul>
                  <h3 className="text-xl font-semibold mb-2">How It Works</h3>
                  <p className="text-gray-700 mb-4">
                    The UAC works by abstracting away the details of each API behind a standardized interface. You define a connector template that specifies the API's endpoints, authentication method, and data mapping. The UAC then handles the rest, including authentication, request formatting, and response parsing.
                  </p>
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                    <h4 className="text-lg font-semibold text-blue-800 mb-2">Getting Started with UAC</h4>
                    <p className="text-blue-700">
                      To get started with the UAC, you'll need to:
                    </p>
                    <ol className="list-decimal pl-6 mt-2 text-blue-700">
                      <li>Choose a connector template or create your own</li>
                      <li>Configure the connector with your API credentials</li>
                      <li>Start making requests through the UAC</li>
                    </ol>
                  </div>
                </div>
              )}

              {/* Default Content */}
              {!(
                (activeCategory === "getting-started" && activeApi === "overview") ||
                (activeCategory === "authentication" && activeApi === "api-keys") ||
                (activeCategory === "uac" && activeApi === "uac-overview")
              ) && (
                <div>
                  <h2 className="text-2xl font-bold mb-4">Documentation Coming Soon</h2>
                  <p className="text-gray-700 mb-4">
                    We're working on comprehensive documentation for all our APIs. Please check back soon for updates.
                  </p>
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <h4 className="text-lg font-semibold text-blue-800 mb-2">Need Help?</h4>
                    <p className="text-blue-700">
                      If you need assistance with our APIs, please contact our support team at <a href="mailto:<EMAIL>" className="underline"><EMAIL></a>.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
