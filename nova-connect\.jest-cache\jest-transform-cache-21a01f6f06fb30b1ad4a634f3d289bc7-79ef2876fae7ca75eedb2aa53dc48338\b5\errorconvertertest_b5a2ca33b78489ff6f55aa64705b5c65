02fd2a0714274f71f21da3b62751a0fa
/**
 * NovaFuse Universal API Connector - Error Converter Tests
 * 
 * This module tests the error converter utilities for the UAC.
 */

const {
  convertAxiosError,
  convertJsonSchemaError,
  convertError
} = require('../../src/utils/error-converter');
const {
  UAConnectorError,
  TimeoutError,
  NetworkError,
  InvalidCredentialsError,
  AuthenticationError,
  ResourceNotFoundError,
  RateLimitExceededError,
  BadRequestError,
  ServerError,
  ValidationError
} = require('../../src/errors');
describe('Error Converter', () => {
  describe('convertAxiosError', () => {
    it('should return the error if it is already a UAConnectorError', () => {
      const error = new UAConnectorError('Test error');
      const result = convertAxiosError(error);
      expect(result).toBe(error);
    });
    it('should convert timeout error', () => {
      const error = {
        code: 'ECONNABORTED',
        message: 'timeout of 1000ms exceeded',
        config: {
          url: 'https://api.example.com',
          method: 'get'
        }
      };
      const result = convertAxiosError(error);
      expect(result).toBeInstanceOf(TimeoutError);
      expect(result.message).toBe('Request timed out');
      expect(result.cause).toBe(error);
      expect(result.context.request.url).toBe('https://api.example.com');
    });
    it('should convert network error', () => {
      const error = {
        code: 'ENOTFOUND',
        message: 'getaddrinfo ENOTFOUND api.example.com',
        config: {
          url: 'https://api.example.com',
          method: 'get'
        }
      };
      const result = convertAxiosError(error);
      expect(result).toBeInstanceOf(NetworkError);
      expect(result.message).toBe('Network error: ENOTFOUND');
      expect(result.cause).toBe(error);
    });
    it('should convert 401 Unauthorized error', () => {
      const error = {
        response: {
          status: 401,
          data: {
            message: 'Unauthorized'
          }
        },
        config: {
          url: 'https://api.example.com',
          method: 'get'
        }
      };
      const result = convertAxiosError(error);
      expect(result).toBeInstanceOf(InvalidCredentialsError);
      expect(result.statusCode).toBe(401);
      expect(result.response).toEqual({
        message: 'Unauthorized'
      });
    });
    it('should convert 403 Forbidden error', () => {
      const error = {
        response: {
          status: 403,
          data: {
            message: 'Forbidden'
          }
        },
        config: {
          url: 'https://api.example.com',
          method: 'get'
        }
      };
      const result = convertAxiosError(error);
      expect(result).toBeInstanceOf(AuthenticationError);
      expect(result.code).toBe('AUTH_FORBIDDEN');
      expect(result.statusCode).toBe(403);
    });
    it('should convert 404 Not Found error', () => {
      const error = {
        response: {
          status: 404,
          data: {
            message: 'Not Found'
          }
        },
        config: {
          url: 'https://api.example.com/users/123',
          method: 'get'
        }
      };
      const result = convertAxiosError(error);
      expect(result).toBeInstanceOf(ResourceNotFoundError);
      expect(result.statusCode).toBe(404);
      expect(result.resourceId).toBe('https://api.example.com/users/123');
    });
    it('should convert 429 Too Many Requests error', () => {
      const error = {
        response: {
          status: 429,
          data: {
            message: 'Too Many Requests'
          },
          headers: {
            'retry-after': '60'
          }
        },
        config: {
          url: 'https://api.example.com',
          method: 'get'
        }
      };
      const result = convertAxiosError(error);
      expect(result).toBeInstanceOf(RateLimitExceededError);
      expect(result.statusCode).toBe(429);
      expect(result.retryAfter).toBe(60);
    });
    it('should convert 400 Bad Request error', () => {
      const error = {
        response: {
          status: 400,
          data: {
            message: 'Bad Request'
          }
        },
        config: {
          url: 'https://api.example.com',
          method: 'get'
        }
      };
      const result = convertAxiosError(error);
      expect(result).toBeInstanceOf(BadRequestError);
      expect(result.statusCode).toBe(400);
    });
    it('should convert 500 Server Error', () => {
      const error = {
        response: {
          status: 500,
          data: {
            message: 'Internal Server Error'
          }
        },
        config: {
          url: 'https://api.example.com',
          method: 'get'
        }
      };
      const result = convertAxiosError(error);
      expect(result).toBeInstanceOf(ServerError);
      expect(result.statusCode).toBe(500);
    });
  });
  describe('convertJsonSchemaError', () => {
    it('should return the error if it is already a UAConnectorError', () => {
      const error = new UAConnectorError('Test error');
      const result = convertJsonSchemaError(error, 'TestSchema');
      expect(result).toBe(error);
    });
    it('should convert JSON Schema validation error', () => {
      const error = {
        errors: [{
          keyword: 'required',
          dataPath: '.user',
          schemaPath: '#/properties/user/required',
          message: 'should have required property \'name\''
        }, {
          keyword: 'format',
          dataPath: '.user.email',
          schemaPath: '#/properties/user/properties/email/format',
          message: 'should match format "email"'
        }]
      };
      const result = convertJsonSchemaError(error, 'UserSchema');
      expect(result).toBeInstanceOf(ValidationError);
      expect(result.code).toBe('VALIDATION_SCHEMA_ERROR');
      expect(result.validationErrors.length).toBe(2);
      expect(result.validationErrors[0].field).toBe('.user');
      expect(result.validationErrors[0].code).toBe('required');
    });
  });
  describe('convertError', () => {
    it('should return the error if it is already a UAConnectorError', () => {
      const error = new UAConnectorError('Test error');
      const result = convertError(error);
      expect(result).toBe(error);
    });
    it('should convert Axios error', () => {
      const error = {
        isAxiosError: true,
        response: {
          status: 404,
          data: {
            message: 'Not Found'
          }
        },
        config: {
          url: 'https://api.example.com/users/123',
          method: 'get'
        }
      };
      const result = convertError(error);
      expect(result).toBeInstanceOf(ResourceNotFoundError);
    });
    it('should convert JSON Schema validation error', () => {
      const error = {
        errors: [{
          keyword: 'required',
          dataPath: '.user',
          message: 'should have required property \'name\''
        }]
      };
      const result = convertError(error, {
        schemaName: 'UserSchema'
      });
      expect(result).toBeInstanceOf(ValidationError);
    });
    it('should convert generic error to UAConnectorError', () => {
      const error = new Error('Generic error');
      const result = convertError(error);
      expect(result).toBeInstanceOf(UAConnectorError);
      expect(result.message).toBe('Generic error');
      expect(result.cause).toBe(error);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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