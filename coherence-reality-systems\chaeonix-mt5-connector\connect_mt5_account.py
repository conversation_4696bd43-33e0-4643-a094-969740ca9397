#!/usr/bin/env python3
"""
CHAEONIX MT5 REAL CONNECTION BRIDGE
Connects to actual MetaTrader 5 platform for live trading
Account: *********** (<PERSON>) | MetaQuotes-Demo
"""

import MetaTrader5 as mt5
import time
import json
from datetime import datetime

class CHAEONIXMT5Bridge:
    def __init__(self):
        self.connected = False
        self.account_info = None
        self.login = ***********
        self.server = "MetaQuotes-Demo"
        self.password = "E*7gLkTd"

    def connect(self):
        """Connect to real MT5 platform"""
        print("🔌 CHAEONIX MT5 BRIDGE - CONNECTING TO REAL MT5")
        print("=" * 60)

        # Initialize MT5
        if not mt5.initialize():
            error = mt5.last_error()
            print(f"❌ MT5 initialization failed: {error}")
            return False

        print("✅ MT5 initialized successfully")

        # Login to account
        authorized = mt5.login(login=self.login, server=self.server, password=self.password)

        if not authorized:
            error = mt5.last_error()
            print(f"❌ Login failed: {error}")
            print("\n🔧 TROUBLESHOOTING STEPS:")
            print("1. Ensure MetaTrader 5 terminal is installed and running")
            print("2. Check 'Tools > Options > Expert Advisors > Allow automated trading'")
            print("3. Verify account credentials are correct")
            print("4. Check internet connection")
            mt5.shutdown()
            return False

        print("✅ Login successful!")

        # Get account information
        self.account_info = mt5.account_info()
        if self.account_info is None:
            print("❌ Failed to get account information")
            mt5.shutdown()
            return False

        self.connected = True
        self.display_account_info()
        return True

    def display_account_info(self):
        """Display account information"""
        if not self.account_info:
            return

        print(f"\n📊 REAL MT5 ACCOUNT INFORMATION:")
        print(f"   👤 Name: {self.account_info.name}")
        print(f"   🆔 Login: {self.account_info.login}")
        print(f"   🏢 Server: {self.account_info.server}")
        print(f"   💰 Balance: ${self.account_info.balance:,.2f} {self.account_info.currency}")
        print(f"   💎 Equity: ${self.account_info.equity:,.2f} {self.account_info.currency}")
        print(f"   📈 Profit: ${self.account_info.profit:,.2f} {self.account_info.currency}")
        print(f"   🔧 Leverage: 1:{self.account_info.leverage}")
        print(f"   🏦 Company: {self.account_info.company}")

    def test_symbols(self):
        """Test symbol availability"""
        print(f"\n📈 TESTING SYMBOL AVAILABILITY:")
        test_symbols = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "NZDUSD"]

        available_symbols = []
        for symbol in test_symbols:
            tick = mt5.symbol_info_tick(symbol)
            if tick is not None:
                print(f"   ✅ {symbol}: {tick.bid:.5f} / {tick.ask:.5f}")
                available_symbols.append(symbol)
            else:
                print(f"   ❌ {symbol}: Not available")

        return available_symbols

    def execute_test_trade(self):
        """Execute a small test trade"""
        if not self.connected:
            print("❌ Not connected to MT5")
            return False

        print(f"\n🧪 EXECUTING TEST TRADE:")

        # Get current price for EURUSD
        tick = mt5.symbol_info_tick("EURUSD")
        if tick is None:
            print("❌ Cannot get EURUSD price")
            return False

        # Prepare trade request
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": "EURUSD",
            "volume": 0.01,  # Minimum lot size
            "type": mt5.ORDER_TYPE_BUY,
            "price": tick.ask,
            "sl": tick.ask - 0.0010,  # Stop loss 10 pips
            "tp": tick.ask + 0.0010,  # Take profit 10 pips
            "deviation": 20,
            "magic": 234000,
            "comment": "CHAEONIX test trade",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        # Send trade request
        result = mt5.order_send(request)

        if result.retcode != mt5.TRADE_RETCODE_DONE:
            print(f"❌ Test trade failed: {result.retcode} - {result.comment}")
            return False
        else:
            print(f"✅ Test trade successful!")
            print(f"   🎫 Ticket: {result.order}")
            print(f"   💰 Volume: {result.volume}")
            print(f"   💵 Price: {result.price}")
            return True

    def get_status(self):
        """Get current MT5 status for CHAEONIX dashboard"""
        if not self.connected:
            return {"connected": False, "error": "Not connected"}

        # Refresh account info
        self.account_info = mt5.account_info()
        if self.account_info is None:
            return {"connected": False, "error": "Cannot get account info"}

        # Get positions
        positions = mt5.positions_get()

        return {
            "connected": True,
            "account": {
                "login": self.account_info.login,
                "server": self.account_info.server,
                "balance": self.account_info.balance,
                "equity": self.account_info.equity,
                "profit": self.account_info.profit,
                "margin": self.account_info.margin,
                "free_margin": self.account_info.margin_free,
                "currency": self.account_info.currency,
                "leverage": self.account_info.leverage,
                "company": self.account_info.company
            },
            "positions": len(positions) if positions else 0,
            "timestamp": datetime.now().isoformat()
        }

    def disconnect(self):
        """Disconnect from MT5"""
        if self.connected:
            mt5.shutdown()
            self.connected = False
            print("🙏 Disconnected from MT5 gracefully")

if __name__ == "__main__":
    bridge = CHAEONIXMT5Bridge()

    if bridge.connect():
        bridge.test_symbols()

        # Ask user if they want to execute a test trade
        test_trade = input("\n🧪 Execute test trade? (y/n): ").lower().strip()
        if test_trade == 'y':
            bridge.execute_test_trade()

        print(f"\n🚀 CHAEONIX MT5 BRIDGE READY!")
        print("   ✅ Real MT5 connection established")
        print("   ✅ Account verified")
        print("   ✅ Ready for live trading integration")

        # Keep connection alive for testing
        print(f"\n⏰ Keeping connection alive for 30 seconds...")
        time.sleep(30)

    bridge.disconnect()
"""
CHAEONIX MT5 ACCOUNT CONNECTION SCRIPT
Connect to David Irvin's MetaTrader 5 Account: **********
Server: MetaQuotes-Demo | Balance: $100,000 USD
"""

import sys
import os
import time
from datetime import datetime

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from divine_mt5_bridge import DivineMT5Bridge

def main():
    print("🚀 CHAEONIX MT5 ACCOUNT CONNECTION")
    print("=" * 60)
    print("🏢 Server: MetaQuotes-Demo")
    print("🆔 Account: *********** (David Irvin)")
    print("💰 Balance: $100,000.00 USD")
    print("🎯 Type: Forex Hedged USD")
    print("=" * 60)
    
    # Initialize Divine MT5 Bridge
    bridge = DivineMT5Bridge()
    
    print("\n🔌 ESTABLISHING DIVINE CONNECTION...")
    
    # Attempt connection
    if bridge.initialize_mt5_connection():
        print("\n✅ CONNECTION SUCCESSFUL!")
        
        # Display account information
        status = bridge.get_account_status()
        print("\n📊 ACCOUNT STATUS:")
        print(f"   🆔 Login: {status['account_id']}")
        print(f"   🏢 Server: {status['server']}")
        print(f"   💰 Balance: ${status['balance']:,.2f} {status['currency']}")
        print(f"   💎 Equity: ${status['equity']:,.2f} {status['currency']}")
        print(f"   📈 Profit: ${status['profit']:,.2f} {status['currency']}")
        print(f"   🔧 Leverage: 1:{status['leverage']}")
        print(f"   🛡️ Protection: {status['protection_status']}")
        
        # Display φ-metrics
        phi_metrics = status['phi_metrics']
        print(f"\n🔮 φ-DIVINE METRICS:")
        print(f"   ⚡ φ-Balance: ${phi_metrics['phi_balance']:,.2f}")
        print(f"   📊 φ-Equity Ratio: {phi_metrics['phi_equity_ratio']:.3f}")
        print(f"   🌟 Golden Ratio Health: {phi_metrics['golden_ratio_health']:.3f}")
        
        # Test symbol information
        print(f"\n📈 TESTING SYMBOL ACCESS...")
        test_symbols = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD"]
        
        for symbol in test_symbols:
            info = bridge.get_symbol_info(symbol)
            if "error" not in info:
                print(f"   ✅ {symbol}: {info['bid']:.5f} / {info['ask']:.5f} (Spread: {info['spread']:.5f})")
                
                # Show Fibonacci levels for EURUSD
                if symbol == "EURUSD" and "fibonacci_levels" in info:
                    fib = info["fibonacci_levels"]
                    if fib:
                        print(f"      🔮 Fibonacci Levels:")
                        print(f"         📈 High: {fib.get('high', 0):.5f}")
                        print(f"         📉 Low: {fib.get('low', 0):.5f}")
                        print(f"         ⚡ 61.8% (φ): {fib.get('fib_61.8', 0):.5f}")
                        print(f"         🎯 161.8% Ext: {fib.get('extension_161.8', 0):.5f}")
            else:
                print(f"   ❌ {symbol}: {info['error']}")
        
        # Check current positions
        print(f"\n📋 CURRENT POSITIONS:")
        positions = bridge.get_positions()
        if positions:
            for pos in positions:
                print(f"   🎯 {pos['symbol']} {pos['type']} {pos['volume']} @ {pos['price_open']:.5f}")
                print(f"      💰 Profit: ${pos['profit']:.2f} | φ-Profit: ${pos['phi_metrics']['phi_profit']:.2f}")
        else:
            print("   📭 No open positions")
        
        # Display connection capabilities
        print(f"\n🌟 CHAEONIX CAPABILITIES READY:")
        print("   ✅ Divine Order Execution with φ-Protection")
        print("   ✅ Fibonacci Level Calculation")
        print("   ✅ Golden Ratio Risk Management")
        print("   ✅ Sacred Frequency Synchronization")
        print("   ✅ Real-time Position Monitoring")
        
        print(f"\n🎯 READY FOR LIVE TRADING!")
        print("   • Use bridge.execute_divine_order() for protected trades")
        print("   • All orders include φ-based stop losses")
        print("   • Maximum 2% risk per trade")
        print("   • Golden ratio position sizing")
        
        # Keep connection alive for testing
        print(f"\n⏰ Connection will remain active for 30 seconds for testing...")
        print("   (Press Ctrl+C to disconnect immediately)")
        
        try:
            for i in range(30, 0, -1):
                print(f"\r   ⏳ Disconnecting in {i} seconds...", end="", flush=True)
                time.sleep(1)
        except KeyboardInterrupt:
            print(f"\n\n⚡ Manual disconnection requested")
        
        # Graceful shutdown
        print(f"\n\n🙏 GRACEFUL SHUTDOWN...")
        bridge.shutdown()
        print("✅ Divine disconnection complete")
        
    else:
        print("\n❌ CONNECTION FAILED!")
        print("\n🔧 TROUBLESHOOTING STEPS:")
        print("   1. Ensure MetaTrader 5 is installed")
        print("   2. Verify account credentials are correct")
        print("   3. Check if MetaTrader 5 terminal is running")
        print("   4. Ensure 'Allow automated trading' is enabled in MT5")
        print("   5. Verify internet connection to MetaQuotes-Demo server")
        
        print(f"\n📋 ACCOUNT DETAILS TO VERIFY:")
        print(f"   🏢 Server: MetaQuotes-Demo")
        print(f"   🆔 Login: ***********")
        print(f"   🔑 Password: E*7gLkTd")
        print(f"   👁️ Investor: Y!7fFkAj")
        
        print(f"\n💡 NEXT STEPS:")
        print("   1. Update password in divine_mt5_bridge.py")
        print("   2. Install MetaTrader5 package: pip install MetaTrader5")
        print("   3. Run this script again")

if __name__ == "__main__":
    main()
