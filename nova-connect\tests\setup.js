/**
 * NovaFuse Universal API Connector Test Setup
 * 
 * This file is run before each test file.
 */

// Set environment variables for testing
process.env.NODE_ENV = 'test';
process.env.PORT = '3010';
process.env.LOG_LEVEL = 'error';
process.env.TEMPLATES_DIR = './templates';
process.env.CREDENTIALS_ENCRYPTION_KEY = 'test-encryption-key';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.JWT_EXPIRATION = '1h';
process.env.RATE_LIMIT_WINDOW_MS = '60000';
process.env.RATE_LIMIT_MAX_REQUESTS = '100';
process.env.ENABLE_METRICS = 'true';
process.env.ENABLE_TRACING = 'false';
process.env.ENABLE_CACHE = 'true';
process.env.ENABLE_RATE_LIMIT = 'true';
process.env.ENABLE_SSRF_PROTECTION = 'true';
process.env.ENABLE_INPUT_VALIDATION = 'true';

// Mock external dependencies
jest.mock('axios');
jest.mock('jsonwebtoken');
jest.mock('mongoose');
jest.mock('redis');

// Global test utilities
global.testUtils = {
  // Create a mock request object
  createMockRequest: (options = {}) => {
    return {
      ip: '127.0.0.1',
      method: 'GET',
      path: '/',
      headers: {},
      query: {},
      params: {},
      body: {},
      ...options
    };
  },
  
  // Create a mock response object
  createMockResponse: () => {
    const res = {
      statusCode: 200,
      headers: {},
      body: null,
      status: function(code) {
        this.statusCode = code;
        return this;
      },
      json: function(data) {
        this.body = data;
        return this;
      },
      send: function(data) {
        this.body = data;
        return this;
      },
      setHeader: function(name, value) {
        this.headers[name] = value;
        return this;
      }
    };
    
    return res;
  },
  
  // Create a mock connector
  createMockConnector: (options = {}) => {
    return {
      metadata: {
        name: 'Test Connector',
        version: '1.0.0',
        category: 'Test',
        description: 'Test connector',
        author: 'NovaFuse',
        tags: ['test']
      },
      authentication: {
        type: 'API_KEY',
        fields: {
          apiKey: {
            type: 'string',
            description: 'API Key',
            required: true,
            sensitive: true
          }
        }
      },
      configuration: {
        baseUrl: 'https://api.example.com',
        headers: {
          'Content-Type': 'application/json'
        }
      },
      endpoints: [
        {
          id: 'test-endpoint',
          name: 'Test Endpoint',
          path: '/test',
          method: 'GET'
        }
      ],
      ...options
    };
  }
};
