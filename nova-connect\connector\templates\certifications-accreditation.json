{"id": "certifications-accreditation", "name": "Certifications & Accreditation Connector", "description": "Connector for certification and accreditation management systems", "version": "1.0.0", "category": "certifications", "icon": "certifications-icon.svg", "author": "NovaFuse", "website": "https://novafuse.io", "documentation": "https://docs.novafuse.io/connectors/certifications", "supportEmail": "<EMAIL>", "authentication": {"type": "oauth2", "oauth2": {"authorizationUrl": "https://auth.example.com/oauth2/authorize", "tokenUrl": "https://auth.example.com/oauth2/token", "scopes": ["read:certifications", "write:certifications", "read:assessments", "write:assessments", "read:evidence", "write:evidence"], "refreshTokenUrl": "https://auth.example.com/oauth2/token"}, "fields": {"clientId": {"type": "string", "label": "Client ID", "required": true, "sensitive": false, "description": "OAuth 2.0 Client ID"}, "clientSecret": {"type": "string", "label": "Client Secret", "required": true, "sensitive": true, "description": "OAuth 2.0 Client Secret"}, "redirectUri": {"type": "string", "label": "Redirect URI", "required": true, "sensitive": false, "description": "OAuth 2.0 Redirect URI"}}}, "endpoints": [{"id": "listCertifications", "name": "List Certifications", "description": "List all certifications", "method": "GET", "url": "https://api.example.com/certifications", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "status": {"type": "string", "label": "Status", "required": false, "enum": ["active", "pending", "expired", "revoked"], "description": "Filter by certification status"}, "type": {"type": "string", "label": "Type", "required": false, "description": "Filter by certification type"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "status": {"type": "string", "description": "Filter by certification status", "enum": ["active", "pending", "expired", "revoked"]}, "type": {"type": "string", "description": "Filter by certification type"}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Certification ID"}, "name": {"type": "string", "description": "Certification name"}, "description": {"type": "string", "description": "Certification description"}, "type": {"type": "string", "description": "Certification type"}, "status": {"type": "string", "description": "Certification status", "enum": ["active", "pending", "expired", "revoked"]}, "issuer": {"type": "string", "description": "Certification issuer"}, "issuedDate": {"type": "string", "format": "date", "description": "Certification issue date"}, "expirationDate": {"type": "string", "format": "date", "description": "Certification expiration date"}, "createdAt": {"type": "string", "format": "date-time", "description": "Certification creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Certification last update date"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}, {"id": "getCertification", "name": "Get Certification", "description": "Get a specific certification", "method": "GET", "url": "https://api.example.com/certifications/{certificationId}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"certificationId": {"type": "string", "label": "Certification ID", "required": true, "description": "ID of the certification to retrieve"}}, "inputSchema": {"type": "object", "properties": {"certificationId": {"type": "string", "description": "ID of the certification to retrieve"}}, "required": ["certificationId"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Certification ID"}, "name": {"type": "string", "description": "Certification name"}, "description": {"type": "string", "description": "Certification description"}, "type": {"type": "string", "description": "Certification type"}, "status": {"type": "string", "description": "Certification status", "enum": ["active", "pending", "expired", "revoked"]}, "issuer": {"type": "string", "description": "Certification issuer"}, "issuedDate": {"type": "string", "format": "date", "description": "Certification issue date"}, "expirationDate": {"type": "string", "format": "date", "description": "Certification expiration date"}, "requirements": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Requirement ID"}, "name": {"type": "string", "description": "Requirement name"}, "description": {"type": "string", "description": "Requirement description"}, "status": {"type": "string", "description": "Requirement status", "enum": ["met", "not_met", "in_progress", "not_applicable"]}}}}, "documents": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Document ID"}, "name": {"type": "string", "description": "Document name"}, "type": {"type": "string", "description": "Document type"}, "url": {"type": "string", "description": "Document URL"}}}}, "createdAt": {"type": "string", "format": "date-time", "description": "Certification creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Certification last update date"}}}}, {"id": "listAssessments", "name": "List Assessments", "description": "List all assessments", "method": "GET", "url": "https://api.example.com/assessments", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "status": {"type": "string", "label": "Status", "required": false, "enum": ["planned", "in_progress", "completed", "canceled"], "description": "Filter by assessment status"}, "certificationId": {"type": "string", "label": "Certification ID", "required": false, "description": "Filter by certification ID"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "status": {"type": "string", "description": "Filter by assessment status", "enum": ["planned", "in_progress", "completed", "canceled"]}, "certificationId": {"type": "string", "description": "Filter by certification ID"}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Assessment ID"}, "name": {"type": "string", "description": "Assessment name"}, "description": {"type": "string", "description": "Assessment description"}, "status": {"type": "string", "description": "Assessment status", "enum": ["planned", "in_progress", "completed", "canceled"]}, "certificationId": {"type": "string", "description": "Associated certification ID"}, "certificationName": {"type": "string", "description": "Associated certification name"}, "startDate": {"type": "string", "format": "date", "description": "Assessment start date"}, "endDate": {"type": "string", "format": "date", "description": "Assessment end date"}, "createdAt": {"type": "string", "format": "date-time", "description": "Assessment creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Assessment last update date"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}, {"id": "getAssessment", "name": "Get Assessment", "description": "Get a specific assessment", "method": "GET", "url": "https://api.example.com/assessments/{assessmentId}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"assessmentId": {"type": "string", "label": "Assessment ID", "required": true, "description": "ID of the assessment to retrieve"}}, "inputSchema": {"type": "object", "properties": {"assessmentId": {"type": "string", "description": "ID of the assessment to retrieve"}}, "required": ["assessmentId"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Assessment ID"}, "name": {"type": "string", "description": "Assessment name"}, "description": {"type": "string", "description": "Assessment description"}, "status": {"type": "string", "description": "Assessment status", "enum": ["planned", "in_progress", "completed", "canceled"]}, "certificationId": {"type": "string", "description": "Associated certification ID"}, "certificationName": {"type": "string", "description": "Associated certification name"}, "startDate": {"type": "string", "format": "date", "description": "Assessment start date"}, "endDate": {"type": "string", "format": "date", "description": "Assessment end date"}, "assessor": {"type": "object", "properties": {"id": {"type": "string", "description": "Assessor ID"}, "name": {"type": "string", "description": "Assessor name"}, "organization": {"type": "string", "description": "Assessor organization"}}}, "controls": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Control ID"}, "name": {"type": "string", "description": "Control name"}, "description": {"type": "string", "description": "Control description"}, "status": {"type": "string", "description": "Control status", "enum": ["compliant", "non_compliant", "partially_compliant", "not_tested"]}, "findings": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Finding ID"}, "description": {"type": "string", "description": "Finding description"}, "severity": {"type": "string", "description": "Finding severity", "enum": ["critical", "high", "medium", "low", "informational"]}}}}}}}, "createdAt": {"type": "string", "format": "date-time", "description": "Assessment creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Assessment last update date"}}}}, {"id": "listEvidence", "name": "List Evidence", "description": "List all evidence", "method": "GET", "url": "https://api.example.com/evidence", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "assessmentId": {"type": "string", "label": "Assessment ID", "required": false, "description": "Filter by assessment ID"}, "controlId": {"type": "string", "label": "Control ID", "required": false, "description": "Filter by control ID"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "assessmentId": {"type": "string", "description": "Filter by assessment ID"}, "controlId": {"type": "string", "description": "Filter by control ID"}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Evidence ID"}, "name": {"type": "string", "description": "Evidence name"}, "description": {"type": "string", "description": "Evidence description"}, "type": {"type": "string", "description": "Evidence type"}, "assessmentId": {"type": "string", "description": "Associated assessment ID"}, "controlId": {"type": "string", "description": "Associated control ID"}, "url": {"type": "string", "description": "Evidence URL"}, "createdAt": {"type": "string", "format": "date-time", "description": "Evidence creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Evidence last update date"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}, {"id": "getEvidence", "name": "Get Evidence", "description": "Get a specific evidence", "method": "GET", "url": "https://api.example.com/evidence/{evidenceId}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"evidenceId": {"type": "string", "label": "Evidence ID", "required": true, "description": "ID of the evidence to retrieve"}}, "inputSchema": {"type": "object", "properties": {"evidenceId": {"type": "string", "description": "ID of the evidence to retrieve"}}, "required": ["evidenceId"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Evidence ID"}, "name": {"type": "string", "description": "Evidence name"}, "description": {"type": "string", "description": "Evidence description"}, "type": {"type": "string", "description": "Evidence type"}, "assessmentId": {"type": "string", "description": "Associated assessment ID"}, "controlId": {"type": "string", "description": "Associated control ID"}, "url": {"type": "string", "description": "Evidence URL"}, "metadata": {"type": "object", "description": "Evidence metadata"}, "createdAt": {"type": "string", "format": "date-time", "description": "Evidence creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Evidence last update date"}}}}]}