/**
 * Cache Middleware
 * 
 * This middleware provides caching functionality for Express routes.
 */

const cacheService = require('../services/CacheService');
const logger = require('../utils/logger');

/**
 * Cache middleware
 * @param {Object} options - Cache options
 * @returns {Function} - Express middleware
 */
const cacheMiddleware = (options = {}) => {
  return cacheService.middleware(options);
};

/**
 * Cache clear middleware
 * @param {Object} options - Cache clear options
 * @returns {Function} - Express middleware
 */
const cacheClearMiddleware = (options = {}) => {
  const {
    getKeys = (req) => [`${req.method}:${req.originalUrl}`],
    shouldClear = (req) => true
  } = options;
  
  return async (req, res, next) => {
    if (!shouldClear(req)) {
      return next();
    }
    
    try {
      const keys = getKeys(req);
      
      // Delete keys from cache
      for (const key of keys) {
        await cacheService.delete(key);
      }
      
      next();
    } catch (error) {
      logger.error('Cache clear middleware error', { error });
      next();
    }
  };
};

/**
 * Cache metrics endpoint
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const cacheMetrics = (req, res) => {
  const metrics = cacheService.getMetrics();
  
  res.json({
    status: 'ok',
    cache: metrics
  });
};

/**
 * Cache clear endpoint
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const cacheClear = async (req, res) => {
  try {
    const cleared = await cacheService.clear();
    
    res.json({
      status: cleared ? 'ok' : 'error',
      message: cleared ? 'Cache cleared successfully' : 'Failed to clear cache'
    });
  } catch (error) {
    logger.error('Cache clear endpoint error', { error });
    
    res.status(500).json({
      status: 'error',
      message: 'Failed to clear cache',
      error: error.message
    });
  }
};

module.exports = {
  cacheMiddleware,
  cacheClearMiddleware,
  cacheMetrics,
  cacheClear
};
