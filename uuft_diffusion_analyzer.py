#!/usr/bin/env python3
"""
UUFT Diffusion Analyzer

This module analyzes diffusion processes for UUFT patterns, detecting and measuring
18/82 patterns and π-related relationships in adoption dynamics.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import networkx as nx
import os
import logging
import json
from collections import defaultdict
from scipy import stats
from uuft_social_analyzer import UUFTSocialNetwork
from uuft_diffusion_model import UUFTDiffusionModel

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uuft_diffusion_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UUFT_Diffusion_Analysis')

# Constants
PATTERN_1882_RATIO = 18 / 82
PI = np.pi
PI_10_CUBED = PI * 10**3
RESULTS_DIR = "uuft_results/social"
os.makedirs(RESULTS_DIR, exist_ok=True)

class UUFTDiffusionAnalyzer:
    """
    Analyzes diffusion processes for UUFT patterns.
    """
    
    def __init__(self, diffusion_model, pattern_threshold=0.05):
        """
        Initialize the diffusion analyzer.
        
        Args:
            diffusion_model: UUFTDiffusionModel instance
            pattern_threshold: Threshold for considering a match to the 18/82 pattern (0-1)
        """
        self.diffusion_model = diffusion_model
        self.pattern_threshold = pattern_threshold
        logger.info(f"Initialized diffusion analyzer with pattern threshold {pattern_threshold}")
    
    def analyze_adoption_distribution(self):
        """
        Analyze the distribution of adoption times for 18/82 patterns.
        
        Returns:
            Dict with analysis results
        """
        logger.info("Analyzing adoption distribution")
        
        # Get adoption times
        adoption_times = self.diffusion_model.adoption_time.copy()
        
        # Replace inf with max time + 1 for non-adopters
        max_time = self.diffusion_model.current_time
        adoption_times[np.isinf(adoption_times)] = max_time + 1
        
        # Get node categories
        influencer_nodes = [node for node, attrs in self.diffusion_model.network.node_attributes.items() 
                          if attrs["category"] == "influencer"]
        regular_nodes = [node for node, attrs in self.diffusion_model.network.node_attributes.items() 
                       if attrs["category"] == "regular"]
        
        # Calculate adoption statistics by category
        influencer_times = adoption_times[influencer_nodes]
        regular_times = adoption_times[regular_nodes]
        
        # Calculate mean adoption times
        mean_influencer_time = np.mean([t for t in influencer_times if t <= max_time])
        mean_regular_time = np.mean([t for t in regular_times if t <= max_time])
        
        # Calculate adoption rates
        influencer_adoption_rate = np.sum(influencer_times <= max_time) / len(influencer_nodes)
        regular_adoption_rate = np.sum(regular_times <= max_time) / len(regular_nodes)
        
        # Check for 18/82 pattern in early adopters
        early_adopter_threshold = max_time * 0.25  # First 25% of time steps
        early_adopters = [node for node in range(len(adoption_times)) 
                         if adoption_times[node] <= early_adopter_threshold]
        
        early_influencers = [node for node in early_adopters if node in influencer_nodes]
        early_regulars = [node for node in early_adopters if node in regular_nodes]
        
        # Calculate percentages
        if early_adopters:
            early_influencer_percent = len(early_influencers) / len(early_adopters)
            early_regular_percent = len(early_regulars) / len(early_adopters)
            
            # Check if early adopter distribution follows 18/82 pattern
            proximity_to_1882 = abs(early_influencer_percent - 0.18) / 0.18
            is_1882_pattern = proximity_to_1882 <= self.pattern_threshold
        else:
            early_influencer_percent = 0
            early_regular_percent = 0
            proximity_to_1882 = 1.0
            is_1882_pattern = False
        
        # Check for 18/82 pattern in adoption speed
        if mean_influencer_time > 0 and mean_regular_time > 0:
            speed_ratio = mean_regular_time / mean_influencer_time
            speed_proximity_to_1882 = abs(speed_ratio - (1 + PATTERN_1882_RATIO)) / (1 + PATTERN_1882_RATIO)
            speed_is_1882_pattern = speed_proximity_to_1882 <= self.pattern_threshold
        else:
            speed_ratio = 0
            speed_proximity_to_1882 = 1.0
            speed_is_1882_pattern = False
        
        # Compile results
        result = {
            "total_nodes": len(adoption_times),
            "total_adopters": int(np.sum(adoption_times <= max_time)),
            "adoption_rate": float(np.sum(adoption_times <= max_time) / len(adoption_times)),
            "influencer_nodes": len(influencer_nodes),
            "regular_nodes": len(regular_nodes),
            "influencer_adoption_rate": float(influencer_adoption_rate),
            "regular_adoption_rate": float(regular_adoption_rate),
            "mean_influencer_adoption_time": float(mean_influencer_time),
            "mean_regular_adoption_time": float(mean_regular_time),
            "early_adopters": len(early_adopters),
            "early_influencer_percent": float(early_influencer_percent),
            "early_regular_percent": float(early_regular_percent),
            "early_adopter_1882_proximity": float(proximity_to_1882),
            "early_adopter_is_1882_pattern": bool(is_1882_pattern),
            "adoption_speed_ratio": float(speed_ratio),
            "adoption_speed_1882_proximity": float(speed_proximity_to_1882),
            "adoption_speed_is_1882_pattern": bool(speed_is_1882_pattern)
        }
        
        return result
    
    def analyze_influence_patterns(self):
        """
        Analyze the patterns of influence during the diffusion process.
        
        Returns:
            Dict with analysis results
        """
        logger.info("Analyzing influence patterns")
        
        # Get adoption times and states
        adoption_times = self.diffusion_model.adoption_time.copy()
        adoption_state = self.diffusion_model.adoption_state.copy()
        
        # Create directed influence graph
        influence_graph = nx.DiGraph()
        influence_graph.add_nodes_from(range(self.diffusion_model.network.num_nodes))
        
        # Track influence weights
        influence_weights = defaultdict(float)
        
        # Analyze each adoption event
        for entry in self.diffusion_model.adoption_history[1:]:  # Skip initial adopters
            time = entry["time"]
            new_adopters = entry["new_adopters"]
            
            for adopter in new_adopters:
                # Find neighbors that had already adopted
                neighbors = list(self.diffusion_model.network.graph.neighbors(adopter))
                adopted_neighbors = [n for n in neighbors if adoption_times[n] < time]
                
                # Calculate influence from each neighbor
                for neighbor in adopted_neighbors:
                    # Base influence
                    influence = self.diffusion_model.network.node_attributes[neighbor]["influence"]
                    
                    # Time decay factor (more recent adoptions have stronger influence)
                    time_factor = 1.0 / (1.0 + (time - adoption_times[neighbor]))
                    
                    # Add edge with weight
                    weight = influence * time_factor
                    influence_graph.add_edge(neighbor, adopter, weight=weight)
                    influence_weights[(neighbor, adopter)] = weight
        
        # Calculate influence metrics
        if influence_graph.number_of_edges() > 0:
            # Calculate total influence by node
            total_influence = defaultdict(float)
            for (source, target), weight in influence_weights.items():
                total_influence[source] += weight
            
            # Get node categories
            influencer_nodes = [node for node, attrs in self.diffusion_model.network.node_attributes.items() 
                              if attrs["category"] == "influencer"]
            regular_nodes = [node for node, attrs in self.diffusion_model.network.node_attributes.items() 
                           if attrs["category"] == "regular"]
            
            # Calculate influence by category
            influencer_influence = sum(total_influence[node] for node in influencer_nodes)
            regular_influence = sum(total_influence[node] for node in regular_nodes)
            total_influence_sum = influencer_influence + regular_influence
            
            # Check for 18/82 pattern in influence distribution
            if total_influence_sum > 0:
                influencer_influence_percent = influencer_influence / total_influence_sum
                regular_influence_percent = regular_influence / total_influence_sum
                
                proximity_to_1882 = abs(influencer_influence_percent - 0.82) / 0.82
                is_1882_pattern = proximity_to_1882 <= self.pattern_threshold
            else:
                influencer_influence_percent = 0
                regular_influence_percent = 0
                proximity_to_1882 = 1.0
                is_1882_pattern = False
            
            # Calculate centrality metrics
            try:
                # Eigenvector centrality
                eigenvector_centrality = nx.eigenvector_centrality_numpy(influence_graph, weight='weight')
                
                # PageRank
                pagerank = nx.pagerank(influence_graph, weight='weight')
                
                # Calculate centrality by category
                influencer_eigenvector = np.mean([eigenvector_centrality[node] for node in influencer_nodes])
                regular_eigenvector = np.mean([eigenvector_centrality[node] for node in regular_nodes])
                
                influencer_pagerank = np.mean([pagerank[node] for node in influencer_nodes])
                regular_pagerank = np.mean([pagerank[node] for node in regular_nodes])
                
                # Check for π-related patterns in centrality ratios
                eigenvector_ratio = influencer_eigenvector / regular_eigenvector if regular_eigenvector > 0 else 0
                pagerank_ratio = influencer_pagerank / regular_pagerank if regular_pagerank > 0 else 0
                
                pi_proximity_eigenvector = abs(eigenvector_ratio - PI) / PI
                pi_proximity_pagerank = abs(pagerank_ratio - PI) / PI
                
                is_pi_pattern_eigenvector = pi_proximity_eigenvector <= self.pattern_threshold
                is_pi_pattern_pagerank = pi_proximity_pagerank <= self.pattern_threshold
            except:
                # Fallback if centrality calculation fails
                eigenvector_ratio = 0
                pagerank_ratio = 0
                pi_proximity_eigenvector = 1.0
                pi_proximity_pagerank = 1.0
                is_pi_pattern_eigenvector = False
                is_pi_pattern_pagerank = False
        else:
            # No influence edges
            influencer_influence_percent = 0
            regular_influence_percent = 0
            proximity_to_1882 = 1.0
            is_1882_pattern = False
            
            eigenvector_ratio = 0
            pagerank_ratio = 0
            pi_proximity_eigenvector = 1.0
            pi_proximity_pagerank = 1.0
            is_pi_pattern_eigenvector = False
            is_pi_pattern_pagerank = False
        
        # Compile results
        result = {
            "influence_edges": influence_graph.number_of_edges(),
            "influencer_influence_percent": float(influencer_influence_percent),
            "regular_influence_percent": float(regular_influence_percent),
            "influence_1882_proximity": float(proximity_to_1882),
            "influence_is_1882_pattern": bool(is_1882_pattern),
            "eigenvector_centrality_ratio": float(eigenvector_ratio),
            "pagerank_ratio": float(pagerank_ratio),
            "eigenvector_pi_proximity": float(pi_proximity_eigenvector),
            "pagerank_pi_proximity": float(pi_proximity_pagerank),
            "eigenvector_is_pi_pattern": bool(is_pi_pattern_eigenvector),
            "pagerank_is_pi_pattern": bool(is_pi_pattern_pagerank)
        }
        
        return result
    
    def analyze_temporal_stability(self):
        """
        Analyze the temporal stability of 18/82 patterns during diffusion.
        
        Returns:
            Dict with analysis results
        """
        logger.info("Analyzing temporal stability")
        
        # Get adoption history
        history = self.diffusion_model.adoption_history
        
        if len(history) < 3:
            logger.warning("Not enough history for temporal stability analysis")
            return {
                "insufficient_data": True
            }
        
        # Get node categories
        influencer_nodes = [node for node, attrs in self.diffusion_model.network.node_attributes.items() 
                          if attrs["category"] == "influencer"]
        regular_nodes = [node for node, attrs in self.diffusion_model.network.node_attributes.items() 
                       if attrs["category"] == "regular"]
        
        # Calculate adoption rates over time
        time_points = []
        influencer_rates = []
        regular_rates = []
        ratio_values = []
        proximity_values = []
        
        for entry in history:
            time = entry["time"]
            
            # Calculate cumulative adopters up to this time
            cumulative_adopters = set()
            for prev_entry in history:
                if prev_entry["time"] <= time:
                    cumulative_adopters.update(prev_entry["new_adopters"])
            
            # Calculate adoption rates by category
            influencer_adopters = [node for node in cumulative_adopters if node in influencer_nodes]
            regular_adopters = [node for node in cumulative_adopters if node in regular_nodes]
            
            influencer_rate = len(influencer_adopters) / len(influencer_nodes) if influencer_nodes else 0
            regular_rate = len(regular_adopters) / len(regular_nodes) if regular_nodes else 0
            
            # Calculate ratio and proximity to 18/82
            if regular_rate > 0 and influencer_rate > 0:
                ratio = influencer_rate / regular_rate
                proximity = abs(ratio - (1 + PATTERN_1882_RATIO)) / (1 + PATTERN_1882_RATIO)
            else:
                ratio = 0
                proximity = 1.0
            
            time_points.append(time)
            influencer_rates.append(influencer_rate)
            regular_rates.append(regular_rate)
            ratio_values.append(ratio)
            proximity_values.append(proximity)
        
        # Calculate temporal stability metrics
        if len(proximity_values) > 2:
            # Mean proximity
            mean_proximity = np.mean(proximity_values)
            
            # Standard deviation of proximity (lower = more stable)
            std_proximity = np.std(proximity_values)
            
            # Calculate stability quotient (1 = perfectly stable, 0 = completely unstable)
            stability_quotient = 1.0 - min(1.0, mean_proximity * (1.0 + std_proximity / max(0.001, mean_proximity)))
            
            # Calculate pattern persistence (fraction of time points with 18/82 pattern)
            pattern_matches = sum(1 for p in proximity_values if p <= self.pattern_threshold)
            persistence = pattern_matches / len(proximity_values)
            
            # Check for π-related patterns in stability metrics
            pi_proximity = abs(stability_quotient * 10 - PI) / PI
            is_pi_pattern = pi_proximity <= self.pattern_threshold
        else:
            mean_proximity = 1.0
            std_proximity = 1.0
            stability_quotient = 0.0
            persistence = 0.0
            pi_proximity = 1.0
            is_pi_pattern = False
        
        # Compile results
        result = {
            "time_points": len(time_points),
            "mean_proximity": float(mean_proximity),
            "std_proximity": float(std_proximity),
            "temporal_stability_quotient": float(stability_quotient),
            "pattern_persistence": float(persistence),
            "pi_proximity": float(pi_proximity),
            "is_pi_pattern": bool(is_pi_pattern),
            "time_series": {
                "time": time_points,
                "influencer_rate": [float(r) for r in influencer_rates],
                "regular_rate": [float(r) for r in regular_rates],
                "ratio": [float(r) for r in ratio_values],
                "proximity": [float(p) for p in proximity_values]
            }
        }
        
        return result
    
    def analyze_pi_relationships(self):
        """
        Analyze π-related relationships in the diffusion process.
        
        Returns:
            Dict with analysis results
        """
        logger.info("Analyzing π relationships")
        
        # Get adoption times
        adoption_times = self.diffusion_model.adoption_time.copy()
        
        # Replace inf with max time + 1 for non-adopters
        max_time = self.diffusion_model.current_time
        adoption_times[np.isinf(adoption_times)] = max_time + 1
        
        # Get adoption history
        history = self.diffusion_model.adoption_history
        
        # Check for π-related patterns in adoption timing
        pi_relationships = []
        
        # 1. Check for adoption waves at π-related intervals
        if len(history) > 3:
            # Calculate new adoptions at each time step
            time_points = [entry["time"] for entry in history]
            new_adoption_counts = [len(entry["new_adopters"]) for entry in history]
            
            # Find peaks in adoption rate
            peaks = []
            for i in range(1, len(new_adoption_counts) - 1):
                if (new_adoption_counts[i] > new_adoption_counts[i-1] and 
                    new_adoption_counts[i] > new_adoption_counts[i+1] and
                    new_adoption_counts[i] > np.mean(new_adoption_counts)):
                    peaks.append(time_points[i])
            
            # Check for π-related intervals between peaks
            for i in range(len(peaks) - 1):
                for j in range(i + 1, len(peaks)):
                    interval = peaks[j] - peaks[i]
                    
                    # Check if interval is close to π or π×10
                    pi_proximity = min(abs(interval - PI) / PI, abs(interval - PI * 10) / (PI * 10))
                    
                    if pi_proximity <= self.pattern_threshold:
                        pi_relationships.append({
                            "type": "peak_interval",
                            "value": float(interval),
                            "proximity_to_pi": float(pi_proximity),
                            "time_points": [int(peaks[i]), int(peaks[j])]
                        })
        
        # 2. Check for π-related ratios in adoption rates
        if len(history) > 0:
            final_entry = history[-1]
            total_adopters = final_entry["total_adopters"]
            total_nodes = self.diffusion_model.network.num_nodes
            
            # Check if adoption rate is close to 1/π
            if total_nodes > 0:
                adoption_rate = total_adopters / total_nodes
                pi_inv_proximity = abs(adoption_rate - 1/PI) / (1/PI)
                
                if pi_inv_proximity <= self.pattern_threshold:
                    pi_relationships.append({
                        "type": "adoption_rate_pi_inverse",
                        "value": float(adoption_rate),
                        "proximity_to_pi_inverse": float(pi_inv_proximity)
                    })
        
        # 3. Check for π-related patterns in adoption thresholds
        # Get nodes with π-related thresholds
        pi_threshold_nodes = [node for node, attrs in self.diffusion_model.network.node_attributes.items() 
                            if abs(attrs["threshold"] - PI/10) < 0.01]
        
        if pi_threshold_nodes:
            # Calculate adoption rate for these nodes
            pi_node_adoption_rate = np.sum(adoption_times[pi_threshold_nodes] <= max_time) / len(pi_threshold_nodes)
            
            # Calculate adoption rate for other nodes
            other_nodes = [node for node in range(self.diffusion_model.network.num_nodes) 
                         if node not in pi_threshold_nodes]
            other_adoption_rate = np.sum(adoption_times[other_nodes] <= max_time) / len(other_nodes) if other_nodes else 0
            
            # Check if ratio is close to π
            if other_adoption_rate > 0:
                ratio = pi_node_adoption_rate / other_adoption_rate
                pi_proximity = abs(ratio - PI) / PI
                
                if pi_proximity <= self.pattern_threshold:
                    pi_relationships.append({
                        "type": "pi_threshold_adoption_ratio",
                        "value": float(ratio),
                        "proximity_to_pi": float(pi_proximity)
                    })
        
        # Compile results
        result = {
            "pi_relationships_count": len(pi_relationships),
            "pi_relationships": pi_relationships,
            "pi_threshold_nodes": len(pi_threshold_nodes),
            "has_significant_pi_patterns": len(pi_relationships) > 0
        }
        
        return result
    
    def create_comprehensive_report(self, save_path=None):
        """
        Create a comprehensive report of all UUFT pattern analyses.
        
        Args:
            save_path: Path to save the report
            
        Returns:
            Dict with report summary
        """
        logger.info("Creating comprehensive report")
        
        # Run all analyses
        adoption_result = self.analyze_adoption_distribution()
        influence_result = self.analyze_influence_patterns()
        temporal_result = self.analyze_temporal_stability()
        pi_result = self.analyze_pi_relationships()
        
        # Calculate overall pattern scores
        pattern_1882_score = 0.0
        count_1882 = 0
        
        # Add adoption distribution score
        if adoption_result.get("early_adopter_is_1882_pattern", False):
            pattern_1882_score += 1.0
            count_1882 += 1
        
        if adoption_result.get("adoption_speed_is_1882_pattern", False):
            pattern_1882_score += 1.0
            count_1882 += 1
        
        # Add influence pattern score
        if influence_result.get("influence_is_1882_pattern", False):
            pattern_1882_score += 1.0
            count_1882 += 1
        
        # Add temporal stability score
        if temporal_result.get("pattern_persistence", 0) > 0.5:
            pattern_1882_score += temporal_result.get("pattern_persistence", 0)
            count_1882 += 1
        
        # Calculate final score
        overall_1882_score = pattern_1882_score / max(1, count_1882)
        
        # Calculate π relationship score
        pi_relationship_score = 0.0
        count_pi = 0
        
        # Add influence pattern π scores
        if influence_result.get("eigenvector_is_pi_pattern", False):
            pi_relationship_score += 1.0
            count_pi += 1
        
        if influence_result.get("pagerank_is_pi_pattern", False):
            pi_relationship_score += 1.0
            count_pi += 1
        
        # Add temporal stability π score
        if temporal_result.get("is_pi_pattern", False):
            pi_relationship_score += 1.0
            count_pi += 1
        
        # Add direct π relationships
        pi_relationship_score += min(1.0, pi_result.get("pi_relationships_count", 0) / 3)
        count_pi += 1
        
        # Calculate final score
        overall_pi_score = pi_relationship_score / max(1, count_pi)
        
        # Compile report
        report = {
            "model_type": self.diffusion_model.model_type,
            "network_type": self.diffusion_model.network.network_type,
            "uuft_bias": self.diffusion_model.network.uuft_bias,
            "num_nodes": self.diffusion_model.network.num_nodes,
            "simulation_steps": self.diffusion_model.current_time,
            "final_adoption_rate": adoption_result.get("adoption_rate", 0),
            "overall_1882_pattern_score": float(overall_1882_score),
            "overall_pi_relationship_score": float(overall_pi_score),
            "adoption_analysis": adoption_result,
            "influence_analysis": influence_result,
            "temporal_analysis": temporal_result,
            "pi_analysis": pi_result
        }
        
        # Save report if path provided
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2)
            logger.info(f"Comprehensive report saved to {save_path}")
        
        return report
    
    def visualize_1882_patterns(self, save_path=None):
        """
        Visualize 18/82 patterns in the diffusion process.
        
        Args:
            save_path: Path to save the visualization
        """
        logger.info("Visualizing 18/82 patterns")
        
        # Get temporal stability results
        temporal_result = self.analyze_temporal_stability()
        
        if temporal_result.get("insufficient_data", False):
            logger.warning("Not enough data for 18/82 pattern visualization")
            return
        
        # Create figure
        plt.figure(figsize=(12, 8))
        
        # Get time series data
        time_series = temporal_result["time_series"]
        times = time_series["time"]
        influencer_rates = time_series["influencer_rate"]
        regular_rates = time_series["regular_rate"]
        proximity_values = time_series["proximity"]
        
        # Plot adoption rates
        plt.subplot(2, 1, 1)
        plt.plot(times, [r * 100 for r in influencer_rates], 'r-', label='Influencers (18%)', linewidth=2)
        plt.plot(times, [r * 100 for r in regular_rates], 'b-', label='Regular Nodes (82%)', linewidth=2)
        
        plt.xlabel('Time Step')
        plt.ylabel('Adoption Rate (%)')
        plt.title('Adoption Rates by Node Category')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Plot proximity to 18/82 pattern
        plt.subplot(2, 1, 2)
        plt.plot(times, [p * 100 for p in proximity_values], 'g-', linewidth=2)
        
        # Add threshold line
        plt.axhline(y=self.pattern_threshold * 100, color='r', linestyle='--', 
                   label=f'Pattern Threshold ({self.pattern_threshold * 100}%)')
        
        # Highlight regions with 18/82 pattern
        for i in range(len(times)):
            if proximity_values[i] <= self.pattern_threshold:
                plt.axvspan(times[i] - 0.5, times[i] + 0.5, color='g', alpha=0.2)
        
        plt.xlabel('Time Step')
        plt.ylabel('Proximity to 18/82 Pattern (%)')
        plt.title('18/82 Pattern Stability Over Time')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Add overall metrics
        plt.figtext(0.02, 0.02, 
                   f"Temporal Stability Quotient: {temporal_result['temporal_stability_quotient']:.4f}\n" +
                   f"Pattern Persistence: {temporal_result['pattern_persistence']:.4f}",
                   fontsize=10, bbox=dict(facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        # Save if path provided
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"18/82 pattern visualization saved to {save_path}")
        
        plt.close()
