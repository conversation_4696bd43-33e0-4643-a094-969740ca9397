openapi: 3.0.0
info:
  title: Data Subject Request API
  description: API endpoints for managing data subject requests
  version: 1.0.0

paths:
  /subject-requests:
    get:
      summary: Get all data subject requests
      description: Get a list of all data subject requests with pagination and filtering
      tags:
        - Data Subject Requests
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
        - name: status
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - pending
              - in-progress
              - completed
              - rejected
              - withdrawn
        - name: requestType
          in: query
          description: Filter by request type
          schema:
            type: string
            enum:
              - access
              - rectification
              - erasure
              - restriction
              - portability
              - objection
              - automated_decision
        - name: search
          in: query
          description: Search term
          schema:
            type: string
        - name: startDate
          in: query
          description: Start date for filtering (ISO format)
          schema:
            type: string
            format: date-time
        - name: endDate
          in: query
          description: End date for filtering (ISO format)
          schema:
            type: string
            format: date-time
        - name: sortBy
          in: query
          description: Field to sort by
          schema:
            type: string
            default: createdAt
        - name: sortOrder
          in: query
          description: Sort order
          schema:
            type: string
            enum:
              - asc
              - desc
            default: desc
      responses:
        '200':
          description: List of data subject requests
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/SubjectRequest'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    post:
      summary: Create a new data subject request
      description: Create a new data subject request
      tags:
        - Data Subject Requests
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubjectRequestInput'
      responses:
        '201':
          description: Data subject request created
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/SubjectRequest'
                  message:
                    type: string
                    example: Data subject request created successfully
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /subject-requests/{id}:
    get:
      summary: Get a specific data subject request
      description: Get a specific data subject request by ID
      tags:
        - Data Subject Requests
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: Data subject request ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Data subject request
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/SubjectRequest'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    put:
      summary: Update a data subject request
      description: Update a specific data subject request by ID
      tags:
        - Data Subject Requests
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: Data subject request ID
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubjectRequestUpdateInput'
      responses:
        '200':
          description: Data subject request updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/SubjectRequest'
                  message:
                    type: string
                    example: Data subject request updated successfully
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /subject-requests/{id}/process:
    post:
      summary: Process a data subject request
      description: Process a specific data subject request by ID
      tags:
        - Data Subject Requests
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: Data subject request ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Data subject request processing initiated
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/SubjectRequest'
                  message:
                    type: string
                    example: Data subject request processing initiated
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /subject-requests/{id}/export:
    get:
      summary: Generate a data export
      description: Generate a data export for a specific data subject request
      tags:
        - Data Subject Requests
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: Data subject request ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Data export generated
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                        description: Data subject request ID
                        example: 5f7b5b5b5b5b5b5b5b5b5b5b
                      exportId:
                        type: string
                        description: Export ID
                        example: export-1234567890
                      exportUrl:
                        type: string
                        description: Export URL
                        example: https://api.novafuse.com/privacy/management/exports/export-1234567890
                      exportFormat:
                        type: string
                        description: Export format
                        example: json
                      exportSize:
                        type: string
                        description: Export size
                        example: 1.2 MB
                      exportCreatedAt:
                        type: string
                        format: date-time
                        description: Export creation date
                        example: 2023-06-15T12:00:00Z
                      exportExpiresAt:
                        type: string
                        format: date-time
                        description: Export expiration date
                        example: 2023-06-22T12:00:00Z
                  message:
                    type: string
                    example: Data export generated successfully
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /subject-requests/{id}/affected-systems:
    get:
      summary: Identify affected systems
      description: Identify systems affected by a specific data subject request
      tags:
        - Data Subject Requests
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: Data subject request ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Affected systems identified
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                        description: Data subject request ID
                        example: 5f7b5b5b5b5b5b5b5b5b5b5b
                      dataSubjectEmail:
                        type: string
                        description: Data subject email
                        example: <EMAIL>
                      affectedSystems:
                        type: array
                        items:
                          type: object
                          properties:
                            systemId:
                              type: string
                              description: System ID
                              example: system-1
                            systemName:
                              type: string
                              description: System name
                              example: CRM System
                            dataCategories:
                              type: array
                              items:
                                type: string
                              description: Data categories
                              example:
                                - Contact Information
                                - Purchase History
                            processingPurposes:
                              type: array
                              items:
                                type: string
                              description: Processing purposes
                              example:
                                - Customer Service
                                - Marketing
                  message:
                    type: string
                    example: Affected systems identified successfully
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  schemas:
    SubjectRequest:
      type: object
      properties:
        _id:
          type: string
          description: Data subject request ID
          example: 5f7b5b5b5b5b5b5b5b5b5b5b
        requestType:
          type: string
          description: Request type
          enum:
            - access
            - rectification
            - erasure
            - restriction
            - portability
            - objection
            - automated_decision
          example: access
        dataSubjectId:
          type: string
          description: Data subject ID
          example: ds-123
        dataSubjectName:
          type: string
          description: Data subject name
          example: John Doe
        dataSubjectEmail:
          type: string
          description: Data subject email
          example: <EMAIL>
        dataSubjectPhone:
          type: string
          description: Data subject phone
          example: ******-123-4567
        requestDetails:
          type: string
          description: Request details
          example: I would like to access all my personal data.
        status:
          type: string
          description: Request status
          enum:
            - pending
            - in-progress
            - completed
            - rejected
            - withdrawn
          example: pending
        assignedTo:
          type: string
          description: Assigned to
          example: user-456
        verificationMethod:
          type: string
          description: Verification method
          enum:
            - email
            - id_verification
            - other
          example: email
        verificationStatus:
          type: string
          description: Verification status
          enum:
            - pending
            - verified
            - failed
          example: pending
        verificationDetails:
          type: string
          description: Verification details
          example: Verification email sent.
        affectedSystems:
          type: array
          description: Affected systems
          items:
            type: object
            properties:
              systemId:
                type: string
                description: System ID
                example: system-1
              systemName:
                type: string
                description: System name
                example: CRM System
              status:
                type: string
                description: System status
                enum:
                  - pending
                  - in-progress
                  - completed
                  - failed
                example: pending
              details:
                type: string
                description: System details
                example: Processing data export
        responseDetails:
          type: string
          description: Response details
          example: Data export generated.
        responseDate:
          type: string
          format: date-time
          description: Response date
          example: 2023-06-15T12:00:00Z
        responseMethod:
          type: string
          description: Response method
          enum:
            - email
            - mail
            - phone
            - in-person
          example: email
        responseAttachments:
          type: array
          description: Response attachments
          items:
            type: object
            properties:
              name:
                type: string
                description: Attachment name
                example: data-export.zip
              type:
                type: string
                description: Attachment type
                example: application/zip
              url:
                type: string
                description: Attachment URL
                example: https://api.novafuse.com/privacy/management/attachments/data-export.zip
        notes:
          type: array
          description: Notes
          items:
            type: object
            properties:
              content:
                type: string
                description: Note content
                example: Verification email sent.
              createdBy:
                type: string
                description: Note creator
                example: user-123
              createdAt:
                type: string
                format: date-time
                description: Note creation date
                example: 2023-06-15T12:00:00Z
        createdAt:
          type: string
          format: date-time
          description: Creation date
          example: 2023-06-15T12:00:00Z
        updatedAt:
          type: string
          format: date-time
          description: Update date
          example: 2023-06-15T12:00:00Z
        dueDate:
          type: string
          format: date-time
          description: Due date
          example: 2023-07-15T12:00:00Z
        completedDate:
          type: string
          format: date-time
          description: Completion date
          example: 2023-06-20T12:00:00Z
    
    SubjectRequestInput:
      type: object
      required:
        - requestType
        - dataSubjectName
        - dataSubjectEmail
        - requestDetails
      properties:
        requestType:
          type: string
          description: Request type
          enum:
            - access
            - rectification
            - erasure
            - restriction
            - portability
            - objection
            - automated_decision
          example: access
        dataSubjectId:
          type: string
          description: Data subject ID
          example: ds-123
        dataSubjectName:
          type: string
          description: Data subject name
          example: John Doe
        dataSubjectEmail:
          type: string
          description: Data subject email
          example: <EMAIL>
        dataSubjectPhone:
          type: string
          description: Data subject phone
          example: ******-123-4567
        requestDetails:
          type: string
          description: Request details
          example: I would like to access all my personal data.
        status:
          type: string
          description: Request status
          enum:
            - pending
            - in-progress
            - completed
            - rejected
            - withdrawn
          default: pending
          example: pending
        assignedTo:
          type: string
          description: Assigned to
          example: user-456
        verificationMethod:
          type: string
          description: Verification method
          enum:
            - email
            - id_verification
            - other
          example: email
        verificationStatus:
          type: string
          description: Verification status
          enum:
            - pending
            - verified
            - failed
          default: pending
          example: pending
        verificationDetails:
          type: string
          description: Verification details
          example: Verification email sent.
        dueDate:
          type: string
          format: date-time
          description: Due date
          example: 2023-07-15T12:00:00Z
    
    SubjectRequestUpdateInput:
      type: object
      properties:
        requestType:
          type: string
          description: Request type
          enum:
            - access
            - rectification
            - erasure
            - restriction
            - portability
            - objection
            - automated_decision
          example: access
        dataSubjectId:
          type: string
          description: Data subject ID
          example: ds-123
        dataSubjectName:
          type: string
          description: Data subject name
          example: John Doe
        dataSubjectEmail:
          type: string
          description: Data subject email
          example: <EMAIL>
        dataSubjectPhone:
          type: string
          description: Data subject phone
          example: ******-123-4567
        requestDetails:
          type: string
          description: Request details
          example: I would like to access all my personal data.
        status:
          type: string
          description: Request status
          enum:
            - pending
            - in-progress
            - completed
            - rejected
            - withdrawn
          example: in-progress
        assignedTo:
          type: string
          description: Assigned to
          example: user-456
        verificationMethod:
          type: string
          description: Verification method
          enum:
            - email
            - id_verification
            - other
          example: email
        verificationStatus:
          type: string
          description: Verification status
          enum:
            - pending
            - verified
            - failed
          example: verified
        verificationDetails:
          type: string
          description: Verification details
          example: Identity verified via email.
        affectedSystems:
          type: array
          description: Affected systems
          items:
            type: object
            properties:
              systemId:
                type: string
                description: System ID
                example: system-1
              systemName:
                type: string
                description: System name
                example: CRM System
              status:
                type: string
                description: System status
                enum:
                  - pending
                  - in-progress
                  - completed
                  - failed
                example: in-progress
              details:
                type: string
                description: System details
                example: Processing data export
        responseDetails:
          type: string
          description: Response details
          example: Data export generated.
        responseDate:
          type: string
          format: date-time
          description: Response date
          example: 2023-06-15T12:00:00Z
        responseMethod:
          type: string
          description: Response method
          enum:
            - email
            - mail
            - phone
            - in-person
          example: email
        responseAttachments:
          type: array
          description: Response attachments
          items:
            type: object
            properties:
              name:
                type: string
                description: Attachment name
                example: data-export.zip
              type:
                type: string
                description: Attachment type
                example: application/zip
              url:
                type: string
                description: Attachment URL
                example: https://api.novafuse.com/privacy/management/attachments/data-export.zip
        notes:
          type: array
          description: Notes
          items:
            type: object
            properties:
              content:
                type: string
                description: Note content
                example: Verification email sent.
              createdBy:
                type: string
                description: Note creator
                example: user-123
              createdAt:
                type: string
                format: date-time
                description: Note creation date
                example: 2023-06-15T12:00:00Z
        dueDate:
          type: string
          format: date-time
          description: Due date
          example: 2023-07-15T12:00:00Z
        completedDate:
          type: string
          format: date-time
          description: Completion date
          example: 2023-06-20T12:00:00Z
    
    Pagination:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 100
        page:
          type: integer
          description: Current page
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        pages:
          type: integer
          description: Total number of pages
          example: 10
    
    Error:
      type: object
      properties:
        error:
          type: string
          description: Error type
          example: ValidationError
        message:
          type: string
          description: Error message
          example: Validation failed
        errors:
          type: array
          description: Validation errors
          items:
            type: object
            properties:
              field:
                type: string
                description: Field name
                example: dataSubjectEmail
              message:
                type: string
                description: Error message
                example: Data subject email must be a valid email address
