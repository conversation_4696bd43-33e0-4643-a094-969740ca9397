/**
 * NovaCore Control System Example
 * 
 * This example demonstrates how to use the NovaCore control system,
 * event processor, and component communicator.
 */

const {
  ControlSystem,
  ControlLoop,
  ControlLoopPriority,
  EventProcessor,
  Event,
  EventPriority,
  ComponentCommunicator,
  Message,
  MessageType
} = require('../src/novacore');

// Create an event processor
const eventProcessor = new EventProcessor({
  enableLogging: true,
  maxConcurrentEvents: 10
});

// Create a control system
const controlSystem = new ControlSystem({
  enableLogging: true,
  maxConcurrentLoops: 5
});

// Create component communicators
const componentA = new ComponentCommunicator({
  componentId: 'component-a',
  enableLogging: true,
  eventProcessor
});

const componentB = new ComponentCommunicator({
  componentId: 'component-b',
  enableLogging: true,
  eventProcessor
});

// Example data
const sensorData = {
  temperature: 25.5,
  humidity: 60,
  pressure: 1013.25,
  timestamp: new Date().toISOString()
};

// Register control loops
function registerControlLoops() {
  console.log('Registering control loops...');
  
  // Register a high-priority control loop for monitoring temperature
  controlSystem.registerControlLoop({
    id: 'temperature-monitor',
    name: 'Temperature Monitor',
    description: 'Monitors temperature and triggers alerts if it exceeds thresholds',
    priority: ControlLoopPriority.HIGH,
    interval: 2000, // 2 seconds
    callback: async () => {
      console.log(`[Temperature Monitor] Current temperature: ${sensorData.temperature}°C`);
      
      // Simulate temperature change
      sensorData.temperature += (Math.random() - 0.5) * 2;
      
      // Check if temperature exceeds threshold
      if (sensorData.temperature > 30) {
        // Create and process an event
        const event = new Event('temperature.alert', {
          temperature: sensorData.temperature,
          threshold: 30,
          timestamp: new Date().toISOString()
        }, {
          priority: EventPriority.HIGH,
          source: 'temperature-monitor'
        });
        
        await eventProcessor.processEvent(event);
      }
      
      return { temperature: sensorData.temperature };
    }
  });
  
  // Register a normal-priority control loop for monitoring humidity
  controlSystem.registerControlLoop({
    id: 'humidity-monitor',
    name: 'Humidity Monitor',
    description: 'Monitors humidity and adjusts system settings',
    priority: ControlLoopPriority.NORMAL,
    interval: 3000, // 3 seconds
    callback: async () => {
      console.log(`[Humidity Monitor] Current humidity: ${sensorData.humidity}%`);
      
      // Simulate humidity change
      sensorData.humidity += (Math.random() - 0.5) * 5;
      
      // Publish humidity data to component B
      await componentA.publish(
        MessageType.EVENT,
        'sensors.humidity',
        {
          humidity: sensorData.humidity,
          timestamp: new Date().toISOString()
        }
      );
      
      return { humidity: sensorData.humidity };
    }
  });
  
  // Register a low-priority control loop for system health check
  controlSystem.registerControlLoop({
    id: 'system-health',
    name: 'System Health Check',
    description: 'Performs periodic system health checks',
    priority: ControlLoopPriority.LOW,
    interval: 5000, // 5 seconds
    callback: async () => {
      console.log('[System Health] Performing system health check...');
      
      // Get system state
      const controlSystemState = controlSystem.getSystemState();
      const eventProcessorState = eventProcessor.getState();
      
      // Publish system health data
      await componentA.publish(
        MessageType.NOTIFICATION,
        'system.health',
        {
          controlSystem: controlSystemState,
          eventProcessor: eventProcessorState,
          timestamp: new Date().toISOString()
        }
      );
      
      return { status: 'healthy' };
    }
  });
}

// Register event handlers
function registerEventHandlers() {
  console.log('Registering event handlers...');
  
  // Register handler for temperature alerts
  eventProcessor.registerHandler('temperature.alert', async (event) => {
    console.log(`[Event Handler] Temperature alert: ${event.data.temperature}°C exceeds threshold of ${event.data.threshold}°C`);
    
    // Publish an alert message
    await componentA.publish(
      MessageType.NOTIFICATION,
      'alerts.temperature',
      {
        temperature: event.data.temperature,
        threshold: event.data.threshold,
        timestamp: event.data.timestamp
      },
      {
        priority: EventPriority.HIGH
      }
    );
  });
}

// Set up component communication
function setupComponentCommunication() {
  console.log('Setting up component communication...');
  
  // Component A subscribes to system health notifications
  componentA.subscribe('system.health', (message) => {
    console.log(`[Component A] Received system health notification:`, {
      activeLoops: message.payload.controlSystem.activeLoops,
      totalLoops: message.payload.controlSystem.totalLoops,
      queueLength: message.payload.eventProcessor.queueLength,
      processedEvents: message.payload.eventProcessor.processedEvents
    });
  });
  
  // Component B subscribes to temperature alerts
  componentB.subscribe('alerts.temperature', (message) => {
    console.log(`[Component B] Received temperature alert: ${message.payload.temperature}°C exceeds threshold of ${message.payload.threshold}°C`);
    
    // Simulate taking action
    console.log(`[Component B] Taking action to reduce temperature...`);
  });
  
  // Component B subscribes to humidity data
  componentB.subscribe('sensors.humidity', (message) => {
    console.log(`[Component B] Received humidity data: ${message.payload.humidity}%`);
    
    // Simulate processing humidity data
    if (message.payload.humidity > 70) {
      console.log(`[Component B] Humidity is too high, activating dehumidifier...`);
    } else if (message.payload.humidity < 30) {
      console.log(`[Component B] Humidity is too low, activating humidifier...`);
    }
  });
}

// Run the example
async function runExample() {
  try {
    console.log('Starting NovaCore Control System Example...');
    
    // Connect components
    await componentA.connect();
    await componentB.connect();
    
    // Register event handlers
    registerEventHandlers();
    
    // Set up component communication
    setupComponentCommunication();
    
    // Register control loops
    registerControlLoops();
    
    // Start all control loops
    controlSystem.startAllControlLoops({ priorityOrder: true });
    
    console.log('Control system started. Press Ctrl+C to exit.');
    
    // Run for 30 seconds, then clean up
    setTimeout(async () => {
      console.log('Stopping example...');
      
      // Stop all control loops
      controlSystem.stopAllControlLoops();
      
      // Disconnect components
      await componentA.disconnect();
      await componentB.disconnect();
      
      console.log('Example completed.');
      process.exit(0);
    }, 30000);
  } catch (error) {
    console.error('Error in example:', error);
    process.exit(1);
  }
}

// Run the example
runExample();
