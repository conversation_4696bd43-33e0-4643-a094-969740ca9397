/**
 * NovaVision Component Card
 * 
 * This component displays a NovaStore component with Trinity CSDE verification
 * status, quality metrics, and adaptive ratios.
 */

const React = require('react');
const PropTypes = require('prop-types');

// Import NovaVision core components
const { 
  Card, 
  Badge, 
  ProgressBar, 
  Tooltip, 
  Icon, 
  TrinitySymbol 
} = require('../core');

/**
 * Component Card
 * @param {Object} props - Component props
 * @returns {JSX.Element} - Component card
 */
function ComponentCard({ component, detailed = false }) {
  // Get verification status and badge
  const isVerified = component.verification?.status === 'verified';
  const badge = component.getVerificationBadge?.() || 'none';
  
  // Get quality metrics
  const qualityMetrics = component.verification?.qualityMetrics || {
    governance: 0,
    detection: 0,
    response: 0,
    overall: 0
  };
  
  // Get adaptive ratios
  const adaptiveRatios = component.verification?.adaptiveRatios || {
    father: 0.18,
    son: 0.18,
    spirit: 0.18
  };
  
  // Get revenue share
  const revenueShare = component.revenueShare || {
    novaFuse: 0.18,
    partners: 0.82
  };
  
  // Badge colors
  const badgeColors = {
    gold: '#FFD700',
    silver: '#C0C0C0',
    bronze: '#CD7F32',
    none: '#999999'
  };
  
  // Status colors
  const statusColors = {
    verified: '#4CAF50',
    rejected: '#F44336',
    unverified: '#FF9800'
  };
  
  // Format percentage
  const formatPercent = (value) => `${(value * 100).toFixed(2)}%`;
  
  return (
    <Card className="nova-component-card">
      {/* Component Header */}
      <div className="nova-component-header">
        <h3 className="nova-component-name">{component.name}</h3>
        <Badge 
          className={`nova-badge nova-badge-${badge}`}
          color={badgeColors[badge]}
          label={badge.toUpperCase()}
        />
      </div>
      
      {/* Component Info */}
      <div className="nova-component-info">
        <div className="nova-component-meta">
          <div className="nova-component-version">v{component.version}</div>
          <div className="nova-component-author">{component.author}</div>
          <div className="nova-component-category">{component.category}</div>
        </div>
        
        <div className="nova-component-description">
          {component.description}
        </div>
        
        {/* Tags */}
        <div className="nova-component-tags">
          {component.tags?.map(tag => (
            <Badge 
              key={tag}
              className="nova-tag"
              color="#E0E0E0"
              label={tag}
              small
            />
          ))}
        </div>
      </div>
      
      {/* Verification Status */}
      <div className="nova-verification-status">
        <div 
          className="nova-status-indicator"
          style={{ color: statusColors[component.verification?.status || 'unverified'] }}
        >
          <Icon 
            name={isVerified ? 'check-circle' : 'warning'} 
            size={16} 
          />
          <span className="nova-status-text">
            {component.verification?.status || 'Unverified'}
          </span>
        </div>
        
        <div className="nova-verification-score">
          <Tooltip content="Verification Score">
            <ProgressBar 
              value={component.verification?.score || 0}
              max={1}
              color={badgeColors[badge]}
              showLabel
              label={formatPercent(component.verification?.score || 0)}
            />
          </Tooltip>
        </div>
      </div>
      
      {/* Trinity CSDE Quality Metrics */}
      <div className="nova-quality-metrics">
        <h4 className="nova-section-title">
          <TrinitySymbol size={16} />
          <span>Trinity CSDE Quality Metrics</span>
        </h4>
        
        <div className="nova-metrics-grid">
          <div className="nova-metric">
            <Tooltip content="Father (Governance) Quality">
              <div className="nova-metric-label">Governance</div>
              <ProgressBar 
                value={qualityMetrics.governance}
                max={1}
                color="#FF6B6B"
                showLabel
                label={formatPercent(qualityMetrics.governance)}
              />
            </Tooltip>
          </div>
          
          <div className="nova-metric">
            <Tooltip content="Son (Detection) Quality">
              <div className="nova-metric-label">Detection</div>
              <ProgressBar 
                value={qualityMetrics.detection}
                max={1}
                color="#4ECDC4"
                showLabel
                label={formatPercent(qualityMetrics.detection)}
              />
            </Tooltip>
          </div>
          
          <div className="nova-metric">
            <Tooltip content="Spirit (Response) Quality">
              <div className="nova-metric-label">Response</div>
              <ProgressBar 
                value={qualityMetrics.response}
                max={1}
                color="#45B7D1"
                showLabel
                label={formatPercent(qualityMetrics.response)}
              />
            </Tooltip>
          </div>
          
          <div className="nova-metric">
            <Tooltip content="Overall Quality">
              <div className="nova-metric-label">Overall</div>
              <ProgressBar 
                value={qualityMetrics.overall}
                max={1}
                color="#6C5CE7"
                showLabel
                label={formatPercent(qualityMetrics.overall)}
              />
            </Tooltip>
          </div>
        </div>
      </div>
      
      {/* Show detailed information if requested */}
      {detailed && (
        <>
          {/* Adaptive Ratios */}
          <div className="nova-adaptive-ratios">
            <h4 className="nova-section-title">
              <Icon name="sliders" size={16} />
              <span>Adaptive Ratios</span>
            </h4>
            
            <div className="nova-ratios-grid">
              <div className="nova-ratio">
                <Tooltip content="Father (Governance) Ratio">
                  <div className="nova-ratio-label">Father</div>
                  <ProgressBar 
                    value={adaptiveRatios.father}
                    max={1}
                    color="#FF6B6B"
                    showLabel
                    label={formatPercent(adaptiveRatios.father)}
                  />
                </Tooltip>
              </div>
              
              <div className="nova-ratio">
                <Tooltip content="Son (Detection) Ratio">
                  <div className="nova-ratio-label">Son</div>
                  <ProgressBar 
                    value={adaptiveRatios.son}
                    max={1}
                    color="#4ECDC4"
                    showLabel
                    label={formatPercent(adaptiveRatios.son)}
                  />
                </Tooltip>
              </div>
              
              <div className="nova-ratio">
                <Tooltip content="Spirit (Response) Ratio">
                  <div className="nova-ratio-label">Spirit</div>
                  <ProgressBar 
                    value={adaptiveRatios.spirit}
                    max={1}
                    color="#45B7D1"
                    showLabel
                    label={formatPercent(adaptiveRatios.spirit)}
                  />
                </Tooltip>
              </div>
            </div>
          </div>
          
          {/* Revenue Share */}
          <div className="nova-revenue-share">
            <h4 className="nova-section-title">
              <Icon name="dollar-sign" size={16} />
              <span>Revenue Share</span>
            </h4>
            
            <div className="nova-revenue-chart">
              <div 
                className="nova-revenue-novafuse"
                style={{ 
                  width: `${revenueShare.novaFuse * 100}%`,
                  backgroundColor: '#6C5CE7'
                }}
              >
                <Tooltip content="NovaFuse Share">
                  <span className="nova-revenue-label">
                    {formatPercent(revenueShare.novaFuse)}
                  </span>
                </Tooltip>
              </div>
              
              <div 
                className="nova-revenue-partners"
                style={{ 
                  width: `${revenueShare.partners * 100}%`,
                  backgroundColor: '#00B894'
                }}
              >
                <Tooltip content="Partners Share">
                  <span className="nova-revenue-label">
                    {formatPercent(revenueShare.partners)}
                  </span>
                </Tooltip>
              </div>
            </div>
            
            <div className="nova-revenue-legend">
              <div className="nova-legend-item">
                <div 
                  className="nova-legend-color"
                  style={{ backgroundColor: '#6C5CE7' }}
                ></div>
                <div className="nova-legend-label">NovaFuse</div>
              </div>
              
              <div className="nova-legend-item">
                <div 
                  className="nova-legend-color"
                  style={{ backgroundColor: '#00B894' }}
                ></div>
                <div className="nova-legend-label">Partners</div>
              </div>
            </div>
          </div>
        </>
      )}
      
      {/* Component Actions */}
      <div className="nova-component-actions">
        <button className="nova-button nova-button-primary">
          <Icon name="download" size={16} />
          <span>Download</span>
        </button>
        
        <button className="nova-button nova-button-secondary">
          <Icon name="info" size={16} />
          <span>Details</span>
        </button>
      </div>
    </Card>
  );
}

ComponentCard.propTypes = {
  component: PropTypes.object.isRequired,
  detailed: PropTypes.bool
};

module.exports = ComponentCard;
