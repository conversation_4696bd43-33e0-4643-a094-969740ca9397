const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const ConnectorSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  longDescription: {
    type: String,
    trim: true
  },
  vendor: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    required: true,
    enum: ['data-privacy', 'security', 'healthcare', 'financial', 'other']
  },
  framework: {
    type: String,
    required: true,
    enum: ['gdpr', 'hipaa', 'soc2', 'pci-dss', 'iso-27001', 'ccpa', 'nist', 'finra', 'fedramp', 'other']
  },
  price: {
    type: String,
    required: true,
    trim: true
  },
  priceNumeric: {
    type: Number,
    required: true
  },
  features: [{
    type: String,
    trim: true
  }],
  integrations: [{
    type: String,
    trim: true
  }],
  averageRating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  reviewCount: {
    type: Number,
    default: 0
  },
  popularity: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['draft', 'pending', 'published', 'rejected'],
    default: 'draft'
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  publishedAt: Date
});

module.exports = mongoose.model('Connector', ConnectorSchema);
