0d16fdea62394f3b5a39f5b947c34984
/**
 * NovaFuse Universal API Connector - Validation Error
 * 
 * This module defines validation-related errors for the UAC.
 */

const UAConnectorError = require('./base-error');

/**
 * Error class for validation failures
 * @class ValidationError
 * @extends UAConnectorError
 */
class ValidationError extends UAConnectorError {
  /**
   * Create a new ValidationError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {string} options.severity - Error severity
   * @param {Object} options.context - Additional context for the error
   * @param {Error} options.cause - The error that caused this error
   * @param {Array<Object>} options.validationErrors - List of validation errors
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'VALIDATION_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
    this.validationErrors = options.validationErrors || [];
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    if (this.validationErrors.length > 0) {
      const errorMessages = this.validationErrors.map(err => err.message).join('; ');
      return `Validation failed: ${errorMessages}`;
    }
    return 'The provided data is invalid. Please check your input and try again.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.validationErrors = this.validationErrors;
    return json;
  }
}

/**
 * Error class for missing required fields
 * @class MissingRequiredFieldError
 * @extends ValidationError
 */
class MissingRequiredFieldError extends ValidationError {
  /**
   * Create a new MissingRequiredFieldError
   * 
   * @param {string|Array<string>} fields - The missing field(s)
   * @param {Object} options - Error options
   */
  constructor(fields, options = {}) {
    const fieldList = Array.isArray(fields) ? fields : [fields];
    const fieldStr = fieldList.join(', ');
    const message = `Missing required field(s): ${fieldStr}`;
    super(message, {
      code: options.code || 'VALIDATION_MISSING_REQUIRED_FIELD',
      severity: options.severity || 'error',
      context: {
        ...options.context,
        fields: fieldList
      },
      cause: options.cause,
      validationErrors: fieldList.map(field => ({
        field,
        message: `Missing required field: ${field}`,
        code: 'MISSING_REQUIRED_FIELD'
      }))
    });
  }
}

/**
 * Error class for invalid field values
 * @class InvalidFieldValueError
 * @extends ValidationError
 */
class InvalidFieldValueError extends ValidationError {
  /**
   * Create a new InvalidFieldValueError
   * 
   * @param {Object|Array<Object>} fieldErrors - The field error(s)
   * @param {Object} options - Error options
   */
  constructor(fieldErrors, options = {}) {
    const errors = Array.isArray(fieldErrors) ? fieldErrors : [fieldErrors];
    const fieldStr = errors.map(e => e.field).join(', ');
    const message = `Invalid value(s) for field(s): ${fieldStr}`;
    super(message, {
      code: options.code || 'VALIDATION_INVALID_FIELD_VALUE',
      severity: options.severity || 'error',
      context: {
        ...options.context,
        fieldErrors: errors
      },
      cause: options.cause,
      validationErrors: errors.map(error => ({
        field: error.field,
        value: error.value,
        message: error.message || `Invalid value for field: ${error.field}`,
        code: 'INVALID_FIELD_VALUE',
        constraint: error.constraint
      }))
    });
  }
}

/**
 * Error class for schema validation failures
 * @class SchemaValidationError
 * @extends ValidationError
 */
class SchemaValidationError extends ValidationError {
  /**
   * Create a new SchemaValidationError
   * 
   * @param {string} schemaName - The name of the schema
   * @param {Array<Object>} errors - The validation errors
   * @param {Object} options - Error options
   */
  constructor(schemaName, errors = [], options = {}) {
    const message = `Schema validation failed for ${schemaName}`;
    super(message, {
      code: options.code || 'VALIDATION_SCHEMA_ERROR',
      severity: options.severity || 'error',
      context: {
        ...options.context,
        schemaName
      },
      cause: options.cause,
      validationErrors: errors.map(error => ({
        field: error.field || error.path,
        message: error.message,
        code: error.code || 'SCHEMA_VALIDATION_ERROR',
        schemaPath: error.schemaPath
      }))
    });
    this.schemaName = schemaName;
  }
}
module.exports = {
  ValidationError,
  MissingRequiredFieldError,
  InvalidFieldValueError,
  SchemaValidationError
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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