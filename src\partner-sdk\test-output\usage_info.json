{"partnerId": "TEST-PARTNER-001", "partnerName": "Test Partner, Inc.", "period": {"startDate": "2025-04-04T18:26:53.698Z", "endDate": "2025-05-04T18:26:53.698Z"}, "totalUsage": 3, "byOperation": {"analyze": 1, "remediate": 1, "report": 1}, "byClient": {"TEST-CLIENT-001": 3}, "byDay": {"2025-05-04": 3}, "details": [{"id": "USG-1746383213565-241313", "partnerId": "TEST-PARTNER-001", "clientId": "TEST-CLIENT-001", "operation": "analyze", "scope": "medium", "timestamp": "2025-05-04T18:26:53.555Z", "environment": "sandbox"}, {"id": "USG-1746383213615-35783", "partnerId": "TEST-PARTNER-001", "clientId": "TEST-CLIENT-001", "operation": "remediate", "scope": "medium", "timestamp": "2025-05-04T18:26:53.615Z", "environment": "sandbox"}, {"id": "USG-1746383213680-871368", "partnerId": "TEST-PARTNER-001", "clientId": "TEST-CLIENT-001", "operation": "report", "scope": "report", "timestamp": "2025-05-04T18:26:53.679Z", "environment": "sandbox"}]}