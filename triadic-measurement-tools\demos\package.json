{"name": "novafuse-cosmic-alignment-simulator", "version": "1.0.0", "description": "NovaFuse Cosmic Alignment Simulator (NCAS) - International Demonstration Suite", "main": "launch_international_demo.js", "scripts": {"start": "node launch_international_demo.js", "demo": "node launch_international_demo.js", "challenge": "node launch_international_demo.js singularity-attempt"}, "keywords": ["ai-safety", "ai-alignment", "cosmic-constraints", "finite-universe-principle", "novafuse", "comphyology", "triadic-measurement"], "author": "<PERSON> (CTO, NovaFuse) & Augment Agent", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2"}, "engines": {"node": ">=16.0.0"}}