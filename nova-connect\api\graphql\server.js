/**
 * GraphQL Server
 * 
 * This file sets up the GraphQL server for NovaConnect.
 */

const { ApolloServer } = require('apollo-server-express');
const { ApolloServerPluginDrainHttpServer } = require('apollo-server-core');
const { makeExecutableSchema } = require('@graphql-tools/schema');
const { SubscriptionServer } = require('subscriptions-transport-ws');
const { execute, subscribe } = require('graphql');
const http = require('http');
const express = require('express');
const typeDefs = require('./schema');
const resolvers = require('./resolvers');
const logger = require('../../config/logger');
const { authenticate } = require('../middleware/authMiddleware');

/**
 * Create a GraphQL server
 * @param {Object} app - Express app
 * @param {Object} options - Server options
 * @returns {Object} - GraphQL server
 */
async function createGraphQLServer(app, options = {}) {
  // Create HTTP server
  const httpServer = http.createServer(app);
  
  // Create schema
  const schema = makeExecutableSchema({
    typeDefs,
    resolvers
  });
  
  // Create subscription server
  const subscriptionServer = SubscriptionServer.create(
    {
      schema,
      execute,
      subscribe,
      onConnect: (connectionParams, webSocket, context) => {
        // Authenticate subscription connections
        const token = connectionParams.Authorization || connectionParams.authorization;
        
        if (token) {
          // In a real implementation, this would validate the token
          // and return the authenticated user
          return { user: { id: 'user-1' } };
        }
        
        throw new Error('Missing auth token');
      }
    },
    {
      server: httpServer,
      path: '/graphql'
    }
  );
  
  // Create Apollo server
  const server = new ApolloServer({
    schema,
    context: async ({ req }) => {
      // Authenticate requests
      try {
        // Get token from request
        const token = req.headers.authorization;
        
        if (token) {
          // In a real implementation, this would validate the token
          // and return the authenticated user
          return { user: { id: 'user-1' } };
        }
        
        return {};
      } catch (error) {
        logger.error('Error authenticating GraphQL request', { error: error.message });
        return {};
      }
    },
    plugins: [
      // Proper shutdown
      ApolloServerPluginDrainHttpServer({ httpServer }),
      {
        async serverWillStart() {
          return {
            async drainServer() {
              subscriptionServer.close();
            }
          };
        }
      }
    ],
    formatError: (error) => {
      // Log errors
      logger.error('GraphQL error', { error: error.message });
      
      // Return formatted error
      return {
        message: error.message,
        path: error.path,
        extensions: error.extensions
      };
    }
  });
  
  // Start server
  await server.start();
  
  // Apply middleware
  server.applyMiddleware({
    app,
    path: '/graphql'
  });
  
  return { server, httpServer };
}

module.exports = createGraphQLServer;
