# NovaFuse UI

This is the user interface for the NovaFuse system, providing interactive controls for manipulating the Self-Healing Tensor, 3D Tensor Visualization, Analytics Dashboard, and other components.

## Overview

The NovaFuse UI is a React application that provides a modern, responsive interface for interacting with the NovaFuse system. It includes:

- Dashboard for monitoring system status
- Tensor controls for manipulating tensors
- Visualization controls for configuring visualizations
- Analytics controls for querying and monitoring
- Settings for configuring the system

## Getting Started

### Prerequisites

- Node.js 14.x or higher
- npm 6.x or higher

### Installation

1. Install dependencies:

```bash
npm install
```

2. Start the development server:

```bash
npm start
```

3. Open [http://localhost:3000](http://localhost:3000) to view the application in your browser.

## Architecture

The NovaFuse UI is built using the following technologies:

- **React**: A JavaScript library for building user interfaces
- **React Router**: For navigation between pages
- **Material-UI**: For UI components and styling
- **Context API**: For state management

The application is structured as follows:

- `src/components`: Reusable UI components
- `src/contexts`: React contexts for state management
- `src/pages`: Page components
- `src/theme.js`: Material-UI theme configuration

### Components

- **Layout**: The main layout component that includes the app bar and navigation drawer
- **ControlGroup**: A component for grouping related controls
- **Control**: A component for rendering different types of controls
- **ActionButton**: A component for executing actions

### Contexts

- **ControlContext**: Provides access to the control system and manages control state

### Pages

- **Dashboard**: Displays system status and metrics
- **TensorControls**: Provides controls for manipulating tensors
- **VisualizationControls**: Provides controls for configuring visualizations
- **AnalyticsControls**: Provides controls for querying and monitoring
- **Settings**: Provides settings for configuring the system

## Control Types

The UI supports the following control types:

- **select**: A dropdown select control
- **checkbox**: A checkbox control
- **slider**: A slider control
- **number**: A numeric input control
- **text**: A text input control
- **textarea**: A multi-line text input control
- **json**: A JSON editor control
- **progress**: A progress bar control

## Actions

The UI supports the following actions:

- **registerTensor**: Register a new tensor
- **healTensor**: Heal a tensor
- **damageTensor**: Damage a tensor
- **createVisualization**: Create a new visualization
- **updateVisualization**: Update a visualization
- **deleteVisualization**: Delete a visualization
- **executeQuery**: Execute a query
- **refreshMetrics**: Refresh metrics
- **refreshDashboard**: Refresh a dashboard

## Integration with Backend

The UI integrates with the backend through WebSockets, using the WebSocket-based real-time data flow system. This allows for real-time updates and bidirectional communication between the UI and the backend.

## Customization

### Theme

The UI uses a custom Material-UI theme defined in `src/theme.js`. You can customize the theme by modifying this file.

### Adding New Controls

To add a new control:

1. Add the control to the `ControlContext` provider
2. Create a new component for the control if needed
3. Add the control to the appropriate page

### Adding New Actions

To add a new action:

1. Add the action to the `ControlContext` provider
2. Create a new component for the action if needed
3. Add the action to the appropriate page

## Deployment

To build the application for production:

```bash
npm run build
```

This will create a `build` directory with the production-ready application. You can then serve the application using a static file server.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
