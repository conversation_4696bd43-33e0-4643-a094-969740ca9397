/**
 * DEBUG CDAIE INTELLIGENCE GRID
 * Step-by-step debugging version
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  TrendingUpIcon,
  TrendingDownIcon,
  BoltIcon,
  EyeIcon,
  StarIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

// Import constants one by one to test
import { PHI, COHERENCE_THRESHOLDS, TRI_MARKET_DOMAINS } from '../utils/chaeonixConstants';

// Simple mock data
const DEBUG_MOCK_DATA = {
  STOCKS: [
    {
      symbol: 'GME',
      domain: 'Stocks',
      coherence: 0.85,
      prophetic_signal: 'Retail revival cycle',
      action: 'Swing long to $35.28',
      current_price: 28.34,
      target_price: 35.28,
      confidence: 0.89,
      engines: ['NEPE', 'NEBE', 'NEPI'],
      fibonacci_level: '61.8%',
      sentiment_phase: 'Hope',
      time_window: '24-48h'
    }
  ],
  CRYPTO: [
    {
      symbol: 'ETH',
      domain: 'Crypto',
      coherence: 0.91,
      prophetic_signal: 'Decentralized stable boom',
      action: 'Stack and hold',
      current_price: 3245.67,
      target_price: 4250.00,
      confidence: 0.93,
      engines: ['NECO', 'NEPI', 'NEEE'],
      fibonacci_level: '50%',
      sentiment_phase: 'Belief',
      time_window: '2-4 weeks'
    }
  ],
  FOREX: [
    {
      symbol: 'GBP/USD',
      domain: 'Forex',
      coherence: 0.79,
      prophetic_signal: 'Rate surprise incoming',
      action: 'Short until 1.2579',
      current_price: 1.2845,
      target_price: 1.2579,
      confidence: 0.84,
      engines: ['NERE', 'NEFC', 'NECO'],
      fibonacci_level: '38.2%',
      sentiment_phase: 'Anxiety',
      time_window: '2-5 days'
    }
  ]
};

export default function DebugCDAIE({ activePhase, selectedMarket, coherenceLevel, divineMode }) {
  const [intelligenceData] = useState(DEBUG_MOCK_DATA);
  const [sortBy, setSortBy] = useState('coherence');
  const [filterThreshold, setFilterThreshold] = useState(0.6);

  // Get data for selected market
  const currentData = intelligenceData[selectedMarket] || [];

  // Sort and filter data
  const processedData = currentData
    .filter(item => item.coherence >= filterThreshold)
    .sort((a, b) => {
      if (sortBy === 'coherence') return b.coherence - a.coherence;
      if (sortBy === 'confidence') return b.confidence - a.confidence;
      if (sortBy === 'symbol') return a.symbol.localeCompare(b.symbol);
      return 0;
    });

  const getCoherenceColor = (coherence) => {
    if (coherence >= 0.90) return 'text-purple-400';
    if (coherence >= 0.70) return 'text-green-400';
    if (coherence >= 0.50) return 'text-yellow-400';
    if (coherence >= 0.30) return 'text-orange-400';
    return 'text-red-400';
  };

  const getCoherenceBackground = (coherence) => {
    if (coherence >= 0.90) return 'bg-purple-500/20 border-purple-400 shadow-divine animate-pulse';
    if (coherence >= 0.70) return 'bg-green-500/20 border-green-400 shadow-coherence';
    if (coherence >= 0.50) return 'bg-yellow-500/20 border-yellow-400';
    if (coherence >= 0.30) return 'bg-orange-500/20 border-orange-400';
    return 'bg-red-500/20 border-red-400';
  };

  const getActionIcon = (action) => {
    if (action.toLowerCase().includes('long') || action.toLowerCase().includes('buy')) {
      return <TrendingUpIcon className="w-4 h-4 text-green-400" />;
    }
    if (action.toLowerCase().includes('short') || action.toLowerCase().includes('sell')) {
      return <TrendingDownIcon className="w-4 h-4 text-red-400" />;
    }
    return <EyeIcon className="w-4 h-4 text-blue-400" />;
  };

  return (
    <div className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-white flex items-center space-x-2">
            <StarIcon className="w-6 h-6 text-purple-400" />
            <span>DEBUG CDAIE Intelligence Grid</span>
          </h2>
          <p className="text-sm text-gray-400 mt-1">
            {selectedMarket} • Phase: {activePhase} • {processedData.length} signals • PHI: {PHI.toFixed(3)}
          </p>
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-4">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded px-3 py-1 text-sm text-white"
          >
            <option value="coherence">Sort by Coherence</option>
            <option value="confidence">Sort by Confidence</option>
            <option value="symbol">Sort by Symbol</option>
          </select>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Min Coherence:</span>
            <input
              type="range"
              min="0"
              max="1"
              step="0.05"
              value={filterThreshold}
              onChange={(e) => setFilterThreshold(parseFloat(e.target.value))}
              className="w-20"
            />
            <span className="text-sm text-white w-12">
              {(filterThreshold * 100).toFixed(0)}%
            </span>
          </div>
        </div>
      </div>

      {/* Intelligence Grid */}
      <div className="grid gap-4">
        {processedData.map((item, index) => (
          <motion.div
            key={item.symbol}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`p-4 rounded-lg border transition-all hover:shadow-lg ${getCoherenceBackground(item.coherence)}`}
          >
            <div className="grid grid-cols-12 gap-4 items-center">
              {/* Symbol & Domain */}
              <div className="col-span-2">
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-bold text-white">
                    {item.symbol}
                  </span>
                  <span className="text-xs px-2 py-1 rounded bg-gray-700 text-gray-300">
                    {item.domain}
                  </span>
                </div>
                <div className="text-sm text-gray-400">
                  Fib: {item.fibonacci_level}
                </div>
              </div>

              {/* Coherence */}
              <div className="col-span-2">
                <div className="text-center">
                  <div className={`text-lg font-bold ${getCoherenceColor(item.coherence)}`}>
                    {(item.coherence * 100).toFixed(0)}%
                  </div>
                  <div className="text-xs text-gray-400">Coherence</div>
                </div>
              </div>

              {/* Prophetic Signal */}
              <div className="col-span-3">
                <div className="flex items-center space-x-2">
                  <BoltIcon className="w-4 h-4 text-yellow-400" />
                  <span className="text-sm text-white font-medium">
                    {item.prophetic_signal}
                  </span>
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  Engines: {item.engines.join(', ')}
                </div>
              </div>

              {/* Action */}
              <div className="col-span-2">
                <div className="flex items-center space-x-2">
                  {getActionIcon(item.action)}
                  <span className="text-sm text-white">
                    {item.action}
                  </span>
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  {item.time_window}
                </div>
              </div>

              {/* Price & Target */}
              <div className="col-span-2">
                <div className="text-sm">
                  <div className="text-white">
                    ${item.current_price.toLocaleString()}
                  </div>
                  <div className="flex items-center space-x-1 text-gray-400">
                    <span>→</span>
                    <span>${item.target_price.toLocaleString()}</span>
                  </div>
                </div>
              </div>

              {/* Action Button */}
              <div className="col-span-1">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded-lg transition-colors"
                  onClick={() => {
                    console.log('Execute action for', item.symbol);
                  }}
                >
                  Execute
                </motion.button>
              </div>
            </div>

            {/* Divine Mode - Additional Details */}
            {divineMode && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="mt-4 pt-4 border-t border-gray-600"
              >
                <div className="grid grid-cols-4 gap-4 text-xs">
                  <div>
                    <span className="text-gray-400">φ Ratio:</span>
                    <span className="text-white ml-2">
                      {(item.target_price / item.current_price).toFixed(3)}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-400">Confidence:</span>
                    <span className="text-green-400 ml-2">
                      {(item.confidence * 100).toFixed(0)}%
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-400">Sentiment:</span>
                    <span className="text-yellow-400 ml-2">{item.sentiment_phase}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Divine Score:</span>
                    <span className="text-purple-400 ml-2">
                      {(item.coherence * item.confidence * PHI).toFixed(2)}
                    </span>
                  </div>
                </div>
              </motion.div>
            )}
          </motion.div>
        ))}
      </div>

      {/* Summary Stats */}
      <div className="mt-6 pt-6 border-t border-gray-600">
        <div className="grid grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-purple-400">
              {processedData.length}
            </div>
            <div className="text-sm text-gray-400">Active Signals</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-400">
              {processedData.length > 0 ? (processedData.reduce((sum, item) => sum + item.coherence, 0) / processedData.length * 100).toFixed(0) : 0}%
            </div>
            <div className="text-sm text-gray-400">Avg Coherence</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-yellow-400">
              {processedData.length > 0 ? (processedData.reduce((sum, item) => sum + item.confidence, 0) / processedData.length * 100).toFixed(0) : 0}%
            </div>
            <div className="text-sm text-gray-400">Avg Confidence</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">
              {processedData.filter(item => item.coherence >= COHERENCE_THRESHOLDS.HIGH).length}
            </div>
            <div className="text-sm text-gray-400">High Coherence</div>
          </div>
        </div>
      </div>
    </div>
  );
}
