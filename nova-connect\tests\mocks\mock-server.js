/**
 * Mock server for testing API calls
 */

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

class MockServer {
  constructor(port = 3001) {
    this.app = express();
    this.port = port;
    this.server = null;
    this.routes = [];
    
    // Configure middleware
    this.app.use(cors());
    this.app.use(bodyParser.json());
    this.app.use(bodyParser.urlencoded({ extended: true }));
    
    // Add default routes
    this.addRoute('GET', '/health', (req, res) => {
      res.json({ status: 'ok' });
    });
  }
  
  /**
   * Add a route to the mock server
   * @param {string} method - HTTP method
   * @param {string} path - Route path
   * @param {Function} handler - Route handler
   */
  addRoute(method, path, handler) {
    this.routes.push({ method, path, handler });
    this.app[method.toLowerCase()](path, handler);
  }
  
  /**
   * Start the mock server
   * @returns {Promise} - Promise that resolves when the server is started
   */
  start() {
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(this.port, () => {
        console.log(`Mock server running on port ${this.port}`);
        resolve();
      });
    });
  }
  
  /**
   * Stop the mock server
   * @returns {Promise} - Promise that resolves when the server is stopped
   */
  stop() {
    return new Promise((resolve, reject) => {
      if (this.server) {
        this.server.close(() => {
          console.log('Mock server stopped');
          this.server = null;
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
  
  /**
   * Reset the mock server routes
   */
  reset() {
    // Remove all routes except default ones
    this.routes = this.routes.filter(route => route.path === '/health');
    
    // Recreate the Express app
    this.app = express();
    this.app.use(cors());
    this.app.use(bodyParser.json());
    this.app.use(bodyParser.urlencoded({ extended: true }));
    
    // Add default routes
    this.addRoute('GET', '/health', (req, res) => {
      res.json({ status: 'ok' });
    });
  }
}

module.exports = MockServer;
