import React from 'react';
import {
  <PERSON>agramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  VerticalArrow,
  ContainerBox,
  ContainerLabel
} from '../components/DiagramComponents';

const FraudComplianceBridge = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>FRAUD-TO-COMPLIANCE BRIDGE API</ContainerLabel>
      </ContainerBox>
      
      {/* Top row components */}
      <ComponentBox left="80px" top="100px" width="130px">
        <ComponentNumber>1001</ComponentNumber>
        <ComponentLabel>Fraud Detection</ComponentLabel>
        System
      </ComponentBox>
      
      <Arrow left="210px" top="130px" width="100px" />
      
      <ComponentBox left="320px" top="100px" width="130px">
        <ComponentNumber>1002</ComponentNumber>
        <ComponentLabel>Unified API</ComponentLabel>
        Bridge
      </ComponentBox>
      
      <Arrow left="450px" top="130px" width="100px" />
      
      <ComponentBox left="560px" top="100px" width="130px">
        <ComponentNumber>1003</ComponentNumber>
        <ComponentLabel>Compliance</ComponentLabel>
        System
      </ComponentBox>
      
      <Arrow left="690px" top="130px" width="50px" />
      
      <ComponentBox left="650px" top="100px" width="130px" style={{ left: '650px' }}>
        <ComponentNumber>1004</ComponentNumber>
        <ComponentLabel>Regulatory</ComponentLabel>
        Response
      </ComponentBox>
      
      {/* Bottom row components */}
      <ComponentBox left="80px" top="250px" width="130px">
        <ComponentNumber>1005</ComponentNumber>
        <ComponentLabel>Event</ComponentLabel>
        Stream
      </ComponentBox>
      
      <VerticalArrow left="145px" top="160px" height="90px" />
      
      <ComponentBox left="320px" top="250px" width="130px">
        <ComponentNumber>1006</ComponentNumber>
        <ComponentLabel>Data</ComponentLabel>
        Transformation
      </ComponentBox>
      
      <VerticalArrow left="385px" top="160px" height="90px" />
      
      <ComponentBox left="560px" top="250px" width="130px">
        <ComponentNumber>1007</ComponentNumber>
        <ComponentLabel>Action</ComponentLabel>
        Orchestration
      </ComponentBox>
      
      <VerticalArrow left="625px" top="160px" height="90px" />
      
      <ComponentBox left="650px" top="250px" width="130px" style={{ left: '650px' }}>
        <ComponentNumber>1008</ComponentNumber>
        <ComponentLabel>Audit Trail</ComponentLabel>
        Generator
      </ComponentBox>
      
      <VerticalArrow left="715px" top="160px" height="90px" />
      
      {/* Additional components */}
      <ComponentBox left="80px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>1009</ComponentNumber>
        <ComponentLabel>Real-Time</ComponentLabel>
        Integration
      </ComponentBox>
      
      <ComponentBox left="240px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>1010</ComponentNumber>
        <ComponentLabel>Standardized</ComponentLabel>
        API
      </ComponentBox>
      
      <ComponentBox left="400px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>1011</ComponentNumber>
        <ComponentLabel>Compliance System</ComponentLabel>
        Connector
      </ComponentBox>
      
      <ComponentBox left="560px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>1012</ComponentNumber>
        <ComponentLabel>Response Time</ComponentLabel>
        Optimization
      </ComponentBox>
    </DiagramFrame>
  );
};

export default FraudComplianceBridge;
