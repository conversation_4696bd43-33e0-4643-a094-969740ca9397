/**
 * Code Snippet Component
 * 
 * This component displays code snippets in the chat with syntax highlighting
 * and copy functionality.
 */

import React, { useState } from 'react';
import { Copy, Check } from 'lucide-react';
import Prism from 'prismjs';
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-jsx';
import 'prismjs/components/prism-tsx';
import 'prismjs/components/prism-json';
import 'prismjs/components/prism-yaml';
import 'prismjs/components/prism-bash';
import 'prismjs/components/prism-python';
import 'prismjs/themes/prism.css';

interface CodeSnippetProps {
  code: string;
  language?: string;
  showLineNumbers?: boolean;
}

const CodeSnippet: React.FC<CodeSnippetProps> = ({ 
  code, 
  language = 'javascript',
  showLineNumbers = true
}) => {
  const [copied, setCopied] = useState(false);
  
  // Determine language
  const getLanguage = () => {
    // Map common file extensions to languages
    const extensionMap: Record<string, string> = {
      'js': 'javascript',
      'ts': 'typescript',
      'jsx': 'jsx',
      'tsx': 'tsx',
      'json': 'json',
      'yml': 'yaml',
      'yaml': 'yaml',
      'sh': 'bash',
      'bash': 'bash',
      'py': 'python'
    };
    
    // Try to detect language from code
    if (language === 'auto') {
      if (code.includes('import React') || code.includes('export default')) {
        return code.includes('tsx') ? 'tsx' : 'jsx';
      }
      if (code.includes('function') || code.includes('const') || code.includes('let')) {
        return code.includes('interface') ? 'typescript' : 'javascript';
      }
      if (code.startsWith('{') || code.startsWith('[')) {
        try {
          JSON.parse(code);
          return 'json';
        } catch (e) {
          // Not valid JSON
        }
      }
      if (code.includes('def ') || code.includes('import ') && code.includes('print(')) {
        return 'python';
      }
      return 'javascript'; // Default
    }
    
    // Use provided language or map from extension
    return extensionMap[language.toLowerCase()] || language;
  };
  
  const handleCopy = () => {
    navigator.clipboard.writeText(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };
  
  // Get highlighted HTML
  const getHighlightedCode = () => {
    const lang = getLanguage();
    
    try {
      // Ensure the language is loaded
      if (Prism.languages[lang]) {
        const highlighted = Prism.highlight(code, Prism.languages[lang], lang);
        
        if (showLineNumbers) {
          const lines = highlighted.split('\n');
          return lines.map((line, i) => (
            <div key={i} className="table-row">
              <span className="table-cell text-right pr-4 text-gray-500 select-none w-12">{i + 1}</span>
              <span className="table-cell" dangerouslySetInnerHTML={{ __html: line || ' ' }} />
            </div>
          ));
        }
        
        return <div dangerouslySetInnerHTML={{ __html: highlighted }} />;
      }
      
      // Fallback if language not supported
      return <div className="whitespace-pre-wrap">{code}</div>;
    } catch (error) {
      console.error('Error highlighting code:', error);
      return <div className="whitespace-pre-wrap">{code}</div>;
    }
  };
  
  return (
    <div className="rounded-md overflow-hidden bg-gray-800 text-gray-100 my-2">
      <div className="flex justify-between items-center px-4 py-2 bg-gray-700">
        <div className="text-xs font-mono">{getLanguage()}</div>
        <button
          onClick={handleCopy}
          className="text-gray-300 hover:text-white focus:outline-none"
          title="Copy code"
        >
          {copied ? <Check size={16} /> : <Copy size={16} />}
        </button>
      </div>
      <div className="p-4 overflow-x-auto text-sm font-mono">
        {showLineNumbers ? (
          <div className="table">{getHighlightedCode()}</div>
        ) : (
          getHighlightedCode()
        )}
      </div>
    </div>
  );
};

export default CodeSnippet;
