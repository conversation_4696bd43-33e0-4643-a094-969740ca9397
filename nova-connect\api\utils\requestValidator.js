/**
 * Request Validator
 * 
 * This module provides utilities for validating API requests:
 * - Schema-based validation for request body, query, and params
 * - Support for Joi validation schemas
 * - Consistent error handling
 * - Custom validation rules
 */

const Joi = require('joi');
const { ValidationError } = require('./enhancedErrors');

/**
 * Create a validation schema for a request
 * 
 * @param {Object} schema - Validation schema
 * @param {Object} schema.body - Validation schema for request body
 * @param {Object} schema.query - Validation schema for request query
 * @param {Object} schema.params - Validation schema for request params
 * @returns {Object} - Validation schema
 */
const createValidationSchema = (schema) => {
  return {
    body: schema.body || Joi.object().optional(),
    query: schema.query || Joi.object().optional(),
    params: schema.params || Joi.object().optional()
  };
};

/**
 * Validate a request against a schema
 * 
 * @param {Object} req - Express request object
 * @param {Object} schema - Validation schema
 * @returns {Object} - Validation result
 */
const validateRequest = (req, schema) => {
  const validationSchema = createValidationSchema(schema);
  const errors = {};
  
  // Validate body
  if (validationSchema.body) {
    const { error, value } = validationSchema.body.validate(req.body, { abortEarly: false });
    
    if (error) {
      errors.body = error.details.map(detail => ({
        path: detail.path.join('.'),
        message: detail.message,
        type: detail.type
      }));
    } else {
      req.body = value;
    }
  }
  
  // Validate query
  if (validationSchema.query) {
    const { error, value } = validationSchema.query.validate(req.query, { abortEarly: false });
    
    if (error) {
      errors.query = error.details.map(detail => ({
        path: detail.path.join('.'),
        message: detail.message,
        type: detail.type
      }));
    } else {
      req.query = value;
    }
  }
  
  // Validate params
  if (validationSchema.params) {
    const { error, value } = validationSchema.params.validate(req.params, { abortEarly: false });
    
    if (error) {
      errors.params = error.details.map(detail => ({
        path: detail.path.join('.'),
        message: detail.message,
        type: detail.type
      }));
    } else {
      req.params = value;
    }
  }
  
  // Return validation result
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Create a validation middleware
 * 
 * @param {Object} schema - Validation schema
 * @returns {Function} - Express middleware
 */
const validate = (schema) => {
  return (req, res, next) => {
    const { isValid, errors } = validateRequest(req, schema);
    
    if (!isValid) {
      const validationError = new ValidationError('Validation failed', errors);
      return next(validationError);
    }
    
    next();
  };
};

/**
 * Common validation schemas
 */
const commonSchemas = {
  id: Joi.string().pattern(/^[a-f0-9]{24}$/).message('Invalid ID format'),
  uuid: Joi.string().uuid().message('Invalid UUID format'),
  email: Joi.string().email().message('Invalid email format'),
  password: Joi.string().min(8).message('Password must be at least 8 characters long'),
  date: Joi.date().iso().message('Invalid date format'),
  page: Joi.number().integer().min(1).default(1).message('Page must be a positive integer'),
  limit: Joi.number().integer().min(1).max(100).default(10).message('Limit must be between 1 and 100'),
  sortBy: Joi.string(),
  sortOrder: Joi.string().valid('asc', 'desc').default('asc'),
  search: Joi.string().allow('').optional(),
  boolean: Joi.boolean(),
  array: Joi.array(),
  object: Joi.object(),
  number: Joi.number(),
  string: Joi.string(),
  enum: (values) => Joi.string().valid(...values)
};

/**
 * Create a pagination schema
 * 
 * @param {Object} options - Pagination options
 * @param {number} options.defaultLimit - Default limit
 * @param {number} options.maxLimit - Maximum limit
 * @param {Array} options.sortFields - Valid sort fields
 * @returns {Object} - Pagination schema
 */
const createPaginationSchema = (options = {}) => {
  const defaultLimit = options.defaultLimit || 10;
  const maxLimit = options.maxLimit || 100;
  const sortFields = options.sortFields || [];
  
  return {
    page: Joi.number().integer().min(1).default(1).message('Page must be a positive integer'),
    limit: Joi.number().integer().min(1).max(maxLimit).default(defaultLimit).message(`Limit must be between 1 and ${maxLimit}`),
    sortBy: sortFields.length > 0 ? Joi.string().valid(...sortFields).default(sortFields[0]) : Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('asc')
  };
};

/**
 * Create a date range schema
 * 
 * @param {Object} options - Date range options
 * @param {boolean} options.required - Whether date range is required
 * @returns {Object} - Date range schema
 */
const createDateRangeSchema = (options = {}) => {
  const required = options.required || false;
  
  const startDateSchema = Joi.date().iso().message('Invalid startDate format');
  const endDateSchema = Joi.date().iso().min(Joi.ref('startDate')).message('endDate must be after startDate');
  
  return {
    startDate: required ? startDateSchema.required() : startDateSchema,
    endDate: required ? endDateSchema.required() : endDateSchema
  };
};

/**
 * Create an ID schema
 * 
 * @param {string} name - ID name
 * @param {Object} options - ID options
 * @param {boolean} options.required - Whether ID is required
 * @param {string} options.type - ID type (id, uuid)
 * @returns {Object} - ID schema
 */
const createIdSchema = (name, options = {}) => {
  const required = options.required || false;
  const type = options.type || 'id';
  
  let schema;
  
  if (type === 'uuid') {
    schema = Joi.string().uuid().message(`Invalid ${name} format`);
  } else {
    schema = Joi.string().pattern(/^[a-f0-9]{24}$/).message(`Invalid ${name} format`);
  }
  
  return required ? schema.required() : schema;
};

module.exports = {
  validate,
  validateRequest,
  createValidationSchema,
  commonSchemas,
  createPaginationSchema,
  createDateRangeSchema,
  createIdSchema
};
