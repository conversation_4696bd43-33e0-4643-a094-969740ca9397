#!/usr/bin/env python3
"""
AEONIX DIVINE API DEMONSTRATION
Quick demo of the FastAPI microservice architecture
"""

import requests
import json
from datetime import datetime

# API Configuration
BASE_URL = "http://localhost:8000"

def test_divine_status():
    """Test divine status"""
    print("🌟 TESTING DIVINE STATUS")
    response = requests.get(f"{BASE_URL}/divine/status")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Kernel: {data['kernel']}")
        print(f"⚡ Engines: {data['engines']}")
        print(f"🌊 φ-Coupling: {data['phi_coupling']}")
        print(f"⏰ Timestamp: {data['timestamp']}")
    return response.status_code == 200

def test_nepi_harmonics():
    """Test NEPI harmonic analysis"""
    print("\n🔢 TESTING NEPI HARMONIC ENGINE")
    payload = {
        "series": [25.06, 26.20, 27.80, 28.34, 29.10, 28.75, 28.97],
        "timeframe": "4h",
        "current_price": 28.34
    }
    
    response = requests.post(f"{BASE_URL}/api/harmonics", json=payload)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Engine: {data['engine']}")
        print(f"📐 Convergence Score: {data['convergence_score']:.3f}")
        print(f"🎯 Closest Level: {data['closest_level']}")
        print(f"🚀 Breakout Target: ${data['breakout_target']:.2f}")
        print(f"📊 Confidence: {data['confidence']:.3f}")
        print(f"🔢 Fibonacci Levels:")
        for level, price in data['fib_levels'].items():
            print(f"   {level}: ${price:.2f}")
    return response.status_code == 200

def test_nepe_prophecy():
    """Test NEPE prophetic engine"""
    print("\n🔮 TESTING NEPE PROPHETIC ENGINE")
    payload = {
        "event": "SEC files sudden lawsuit against major broker",
        "amplification": 1.63,
        "impact_scope": "market",
        "ticker": "GME"
    }
    
    response = requests.post(f"{BASE_URL}/api/prophecy", json=payload)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Engine: {data['engine']}")
        print(f"💥 Impact Score: {data['impact_score']:.3f}")
        print(f"📈 Volatility Spike: {data['volatility_spike']}")
        print(f"🎯 Probability Alteration: {data['probability_alteration']:.3f}")
        print(f"⏰ Timeline: {data['manifestation_timeline']}")
    return response.status_code == 200

def test_divine_simulation():
    """Test complete divine simulation"""
    print("\n🌟 TESTING DIVINE SIMULATION (AEONIX KERNEL)")
    payload = {
        "ticker": "GME",
        "prophecy_event": "CFO resigns unexpectedly",
        "amplification": 1.63,
        "include_engines": ["NEPI", "NEFC", "NEPE", "NEEE", "NERS"]
    }
    
    response = requests.post(f"{BASE_URL}/api/divine-simulation", json=payload)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Ticker: {data['ticker']}")
        print(f"🎯 Target Price: ${data['target_price']:.2f}")
        print(f"🔮 Prophecy Impact: {data['prophecy_impact']:.3f}")
        print(f"📐 Fib Convergence: {data['fib_convergence']:.3f}")
        print(f"💭 Sentiment Phase: {data['sentiment_phase']}")
        print(f"⏰ Time Window: {data['time_window']}")
        print(f"📊 Confidence: {data['confidence']:.3f}")
        print(f"🔧 Engines Used: {len(data['engine_results'])}")
    return response.status_code == 200

def main():
    """Run demonstration"""
    print("🚀 AEONIX DIVINE API DEMONSTRATION")
    print("=" * 50)
    
    tests = [
        ("Divine Status", test_divine_status),
        ("NEPI Harmonics", test_nepi_harmonics),
        ("NEPE Prophecy", test_nepe_prophecy),
        ("Divine Simulation", test_divine_simulation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
            print(f"{'✅' if success else '❌'} {test_name}: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DEMONSTRATION RESULTS")
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"✅ Passed: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🌟 ALL TESTS PASSED - DIVINE API FULLY OPERATIONAL!")
        print("📡 API Documentation: http://localhost:8000/divine/docs")
        print("🔮 WebSocket Stream: ws://localhost:8000/ws/divine-stream")
    else:
        print("🔄 SOME TESTS FAILED - CHECK SERVER STATUS")

if __name__ == "__main__":
    main()
