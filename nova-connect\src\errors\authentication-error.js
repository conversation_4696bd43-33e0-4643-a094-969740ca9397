/**
 * NovaFuse Universal API Connector - Authentication Error
 * 
 * This module defines authentication-related errors for the UAC.
 */

const UAConnectorError = require('./base-error');

/**
 * Error class for authentication failures
 * @class AuthenticationError
 * @extends UAConnectorError
 */
class AuthenticationError extends UAConnectorError {
  /**
   * Create a new AuthenticationError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {string} options.severity - Error severity
   * @param {Object} options.context - Additional context for the error
   * @param {Error} options.cause - The error that caused this error
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'AUTH_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'Authentication failed. Please check your credentials and try again.';
  }
}

/**
 * Error class for missing credentials
 * @class MissingCredentialsError
 * @extends AuthenticationError
 */
class MissingCredentialsError extends AuthenticationError {
  /**
   * Create a new MissingCredentialsError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'Required credentials are missing', options = {}) {
    super(message, {
      code: options.code || 'AUTH_MISSING_CREDENTIALS',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'Authentication failed. Required credentials are missing.';
  }
}

/**
 * Error class for invalid credentials
 * @class InvalidCredentialsError
 * @extends AuthenticationError
 */
class InvalidCredentialsError extends AuthenticationError {
  /**
   * Create a new InvalidCredentialsError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'The provided credentials are invalid', options = {}) {
    super(message, {
      code: options.code || 'AUTH_INVALID_CREDENTIALS',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'Authentication failed. The provided credentials are invalid.';
  }
}

/**
 * Error class for expired credentials
 * @class ExpiredCredentialsError
 * @extends AuthenticationError
 */
class ExpiredCredentialsError extends AuthenticationError {
  /**
   * Create a new ExpiredCredentialsError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'The credentials have expired', options = {}) {
    super(message, {
      code: options.code || 'AUTH_EXPIRED_CREDENTIALS',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'Authentication failed. Your credentials have expired. Please refresh your credentials and try again.';
  }
}

/**
 * Error class for insufficient permissions
 * @class InsufficientPermissionsError
 * @extends AuthenticationError
 */
class InsufficientPermissionsError extends AuthenticationError {
  /**
   * Create a new InsufficientPermissionsError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'Insufficient permissions to perform this operation', options = {}) {
    super(message, {
      code: options.code || 'AUTH_INSUFFICIENT_PERMISSIONS',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'Authentication failed. You do not have sufficient permissions to perform this operation.';
  }
}

module.exports = {
  AuthenticationError,
  MissingCredentialsError,
  InvalidCredentialsError,
  ExpiredCredentialsError,
  InsufficientPermissionsError
};
