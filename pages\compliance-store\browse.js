import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import PageWithSidebar from '../../components/PageWithSidebar';
import AnimatedConnectorCard from '../../components/AnimatedConnectorCard';
import AnimatedSection from '../../components/AnimatedSection';
import { motion } from 'framer-motion';

export default function BrowseConnectors() {
  // Define sidebar items for the Browse page
  const sidebarItems = [
    { type: 'category', label: 'Categories', items: [
      { label: 'All Connectors', href: '/compliance-store/browse' },
      { label: 'Data Privacy', href: '/compliance-store/browse?category=data-privacy' },
      { label: 'Security', href: '/compliance-store/browse?category=security' },
      { label: 'Healthcare', href: '/compliance-store/browse?category=healthcare' },
      { label: 'Financial', href: '/compliance-store/browse?category=financial' }
    ]},
    { type: 'category', label: 'Frameworks', items: [
      { label: 'GDPR', href: '/compliance-store/browse?framework=gdpr' },
      { label: 'HIPAA', href: '/compliance-store/browse?framework=hipaa' },
      { label: 'SOC 2', href: '/compliance-store/browse?framework=soc2' },
      { label: 'PCI DSS', href: '/compliance-store/browse?framework=pci-dss' },
      { label: 'ISO 27001', href: '/compliance-store/browse?framework=iso-27001' }
    ]},
    { type: 'category', label: 'Navigation', items: [
      { label: 'Back to Compliance Store', href: '/compliance-store' }
    ]}
  ];

  // SEO metadata
  const pageProps = {
    title: 'Browse Connectors - NovaFuse Compliance App Store',
    description: 'Browse and discover compliance connectors for GDPR, HIPAA, SOC2, PCI DSS, and more in the NovaFuse Compliance App Store.',
    keywords: 'compliance connectors, GDPR, HIPAA, SOC2, PCI DSS, ISO 27001, compliance solutions',
    canonical: 'https://novafuse.io/compliance-store/browse',
    ogImage: '/images/compliance-store-browse-og-image.png'
  };

  const router = useRouter();
  const { category, framework, sort, page = 1 } = router.query;

  // State for search and filters
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(category || 'all');
  const [selectedFramework, setSelectedFramework] = useState(framework || 'all');
  const [sortOption, setSortOption] = useState(sort || 'popular');
  const [currentPage, setCurrentPage] = useState(parseInt(page) || 1);

  // Update state when URL query params change
  useEffect(() => {
    if (category) setSelectedCategory(category);
    if (framework) setSelectedFramework(framework);
    if (sort) setSortOption(sort);
    if (page) setCurrentPage(parseInt(page) || 1);
  }, [category, framework, sort, page]);

  // Update URL when filters change
  const updateFilters = (newFilters) => {
    const query = { ...router.query, ...newFilters, page: 1 };
    // Remove any undefined or 'all' values
    Object.keys(query).forEach(key => {
      if (query[key] === undefined || query[key] === 'all') {
        delete query[key];
      }
    });
    router.push({
      pathname: router.pathname,
      query
    }, undefined, { shallow: true });
  };

  // Sample connector data (in a real app, this would come from an API)
  const sampleConnectors = [
    {
      id: 'gdpr-shield',
      name: 'GDPR Shield',
      vendor: 'Compliance Partners Inc.',
      description: 'Comprehensive GDPR compliance solution with automated data mapping, consent management, and breach notification.',
      category: 'data-privacy',
      framework: 'gdpr',
      rating: 4.5,
      price: '$499/mo',
      popularity: 95,
      dateAdded: '2023-01-15'
    },
    {
      id: 'soc2-automator',
      name: 'SOC2 Automator',
      vendor: 'TrustTech Solutions',
      description: 'Automate SOC2 evidence collection, control testing, and audit preparation with continuous compliance monitoring.',
      category: 'security',
      framework: 'soc2',
      rating: 4.0,
      price: '$599/mo',
      popularity: 87,
      dateAdded: '2023-02-20'
    },
    {
      id: 'hipaa-guardian',
      name: 'HIPAA Guardian',
      vendor: 'HealthSec Systems',
      description: 'Complete HIPAA compliance solution with PHI tracking, business associate management, and security risk assessment.',
      category: 'healthcare',
      framework: 'hipaa',
      rating: 5.0,
      price: '$699/mo',
      popularity: 92,
      dateAdded: '2023-01-05'
    },
    {
      id: 'pci-compliance-suite',
      name: 'PCI Compliance Suite',
      vendor: 'SecurePayments Inc.',
      description: 'End-to-end PCI DSS compliance solution with cardholder data scanning, vulnerability management, and audit reporting.',
      category: 'financial',
      framework: 'pci-dss',
      rating: 4.2,
      price: '$549/mo',
      popularity: 88,
      dateAdded: '2023-03-10'
    },
    {
      id: 'iso27001-toolkit',
      name: 'ISO 27001 Toolkit',
      vendor: 'Global Compliance Solutions',
      description: 'Complete ISO 27001 implementation toolkit with risk assessment templates, policy generators, and audit preparation tools.',
      category: 'security',
      framework: 'iso-27001',
      rating: 4.7,
      price: '$649/mo',
      popularity: 85,
      dateAdded: '2023-02-15'
    },
    {
      id: 'ccpa-compliance-manager',
      name: 'CCPA Compliance Manager',
      vendor: 'PrivacyTech Solutions',
      description: 'California Consumer Privacy Act compliance solution with consumer request management, data inventory, and privacy policy generator.',
      category: 'data-privacy',
      framework: 'ccpa',
      rating: 4.3,
      price: '$399/mo',
      popularity: 80,
      dateAdded: '2023-04-05'
    },
    {
      id: 'nist-framework-connector',
      name: 'NIST Framework Connector',
      vendor: 'FedSec Solutions',
      description: 'NIST Cybersecurity Framework implementation tool with control mapping, gap analysis, and continuous monitoring.',
      category: 'security',
      framework: 'nist',
      rating: 4.6,
      price: '$599/mo',
      popularity: 83,
      dateAdded: '2023-03-25'
    },
    {
      id: 'finra-compliance-assistant',
      name: 'FINRA Compliance Assistant',
      vendor: 'FinTech Compliance Inc.',
      description: 'Financial Industry Regulatory Authority compliance solution with trade monitoring, communications review, and regulatory reporting.',
      category: 'financial',
      framework: 'finra',
      rating: 4.4,
      price: '$749/mo',
      popularity: 78,
      dateAdded: '2023-05-10'
    },
    {
      id: 'fedramp-accelerator',
      name: 'FedRAMP Accelerator',
      vendor: 'GovCloud Security',
      description: 'Federal Risk and Authorization Management Program compliance accelerator with security control implementation and documentation.',
      category: 'security',
      framework: 'fedramp',
      rating: 4.8,
      price: '$899/mo',
      popularity: 75,
      dateAdded: '2023-04-15'
    }
  ];

  // Filter connectors based on search and filters
  const filteredConnectors = sampleConnectors.filter(connector => {
    // Apply search filter
    if (searchQuery && !connector.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !connector.description.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }

    // Apply category filter
    if (selectedCategory !== 'all' && connector.category !== selectedCategory) {
      return false;
    }

    // Apply framework filter
    if (selectedFramework !== 'all' && connector.framework !== selectedFramework) {
      return false;
    }

    return true;
  });

  // Sort connectors
  const sortedConnectors = [...filteredConnectors].sort((a, b) => {
    switch (sortOption) {
      case 'newest':
        return new Date(b.dateAdded) - new Date(a.dateAdded);
      case 'price-low':
        return parseFloat(a.price.replace('$', '')) - parseFloat(b.price.replace('$', ''));
      case 'price-high':
        return parseFloat(b.price.replace('$', '')) - parseFloat(a.price.replace('$', ''));
      case 'rating':
        return b.rating - a.rating;
      case 'popular':
      default:
        return b.popularity - a.popularity;
    }
  });

  // Pagination
  const connectorsPerPage = 6;
  const totalPages = Math.ceil(sortedConnectors.length / connectorsPerPage);
  const paginatedConnectors = sortedConnectors.slice(
    (currentPage - 1) * connectorsPerPage,
    currentPage * connectorsPerPage
  );

  // Handle page change
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
    router.push({
      pathname: router.pathname,
      query: { ...router.query, page: newPage }
    }, undefined, { shallow: true });
  };

  return (
    <PageWithSidebar title={pageProps.title} sidebarItems={sidebarItems} {...pageProps}>
      {/* Header */}
      <AnimatedSection className="mb-8" animation="fadeInLeft">
        <motion.h1
          className="text-3xl font-bold mb-4"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          Browse Compliance Connectors
        </motion.h1>
        <motion.p
          className="text-xl mb-6"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          Discover pre-built compliance connectors for your specific regulatory needs.
        </motion.p>

        {/* Search Bar */}
        <div className="relative mb-8">
          <input
            type="text"
            placeholder="Search connectors..."
            className="w-full bg-secondary border border-gray-700 rounded-lg py-3 px-4 pr-12 focus:outline-none focus:ring-2 focus:ring-blue-600"
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              if (e.target.value === '') {
                updateFilters({ search: undefined });
              }
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                updateFilters({ search: searchQuery || undefined });
              }
            }}
          />
          <button
            className="absolute right-3 top-3 text-gray-400 hover:text-white"
            onClick={() => updateFilters({ search: searchQuery || undefined })}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
        </div>
      </AnimatedSection>

      {/* Filters and Sorting */}
      <AnimatedSection className="mb-8" animation="fadeInRight" delay={100}>
        <div className="flex flex-col md:flex-row justify-between gap-4">
          <div className="flex flex-wrap gap-4">
          {/* Category Filter */}
          <div>
            <label htmlFor="category-filter" className="block text-sm font-medium mb-1">Category</label>
            <select
              id="category-filter"
              className="bg-secondary border border-gray-700 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600"
              value={selectedCategory}
              onChange={(e) => updateFilters({ category: e.target.value })}
            >
              <option value="all">All Categories</option>
              <option value="data-privacy">Data Privacy</option>
              <option value="security">Security</option>
              <option value="healthcare">Healthcare</option>
              <option value="financial">Financial</option>
            </select>
          </div>

          {/* Framework Filter */}
          <div>
            <label htmlFor="framework-filter" className="block text-sm font-medium mb-1">Framework</label>
            <select
              id="framework-filter"
              className="bg-secondary border border-gray-700 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600"
              value={selectedFramework}
              onChange={(e) => updateFilters({ framework: e.target.value })}
            >
              <option value="all">All Frameworks</option>
              <option value="gdpr">GDPR</option>
              <option value="hipaa">HIPAA</option>
              <option value="soc2">SOC 2</option>
              <option value="pci-dss">PCI DSS</option>
              <option value="iso-27001">ISO 27001</option>
              <option value="ccpa">CCPA</option>
              <option value="nist">NIST</option>
              <option value="finra">FINRA</option>
              <option value="fedramp">FedRAMP</option>
            </select>
          </div>
          </div>

          {/* Sort Options */}
          <div>
            <label htmlFor="sort-options" className="block text-sm font-medium mb-1">Sort By</label>
            <select
              id="sort-options"
              className="bg-secondary border border-gray-700 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600"
              value={sortOption}
              onChange={(e) => updateFilters({ sort: e.target.value })}
            >
              <option value="popular">Most Popular</option>
              <option value="newest">Newest</option>
              <option value="rating">Highest Rated</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
            </select>
          </div>
        </div>
      </AnimatedSection>

      {/* Results Summary */}
      <AnimatedSection className="mb-6" animation="fadeInUp" delay={150}>
        <div className="flex justify-between items-center">
          <motion.p
            className="text-gray-400"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Showing {paginatedConnectors.length} of {filteredConnectors.length} connectors
          </motion.p>

          {selectedCategory !== 'all' || selectedFramework !== 'all' || searchQuery ? (
            <motion.button
              className="text-sm text-blue-400 hover:text-blue-300 flex items-center"
              onClick={() => {
                setSearchQuery('');
                setSelectedCategory('all');
                setSelectedFramework('all');
                setSortOption('popular');
                setCurrentPage(1);
                router.push(router.pathname, undefined, { shallow: true });
              }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              Clear Filters
            </motion.button>
          ) : null}
        </div>
      </AnimatedSection>

      {/* Connectors Grid */}
      <AnimatedSection className="mb-12" animation="fadeInUp">
        {paginatedConnectors.length > 0 ? (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            initial="hidden"
            animate="visible"
            variants={{
              visible: {
                transition: {
                  staggerChildren: 0.1
                }
              }
            }}
          >
            {paginatedConnectors.map((connector, index) => (
              <AnimatedConnectorCard key={connector.id} connector={connector} index={index} />
            ))}
          </motion.div>
        ) : (
          <div className="bg-secondary rounded-lg p-8 text-center">
            <h3 className="text-xl font-bold mb-2">No connectors found</h3>
            <p className="text-gray-400 mb-4">Try adjusting your search or filters to find what you're looking for.</p>
            <button
              className="text-blue-400 hover:text-blue-300"
              onClick={() => {
                setSearchQuery('');
                setSelectedCategory('all');
                setSelectedFramework('all');
                setSortOption('popular');
                setCurrentPage(1);
                router.push(router.pathname, undefined, { shallow: true });
              }}
            >
              Clear all filters
            </button>
          </div>
        )}
      </AnimatedSection>

      {/* Pagination */}
      {totalPages > 1 && (
        <AnimatedSection className="mb-12" animation="fadeInUp" delay={200}>
          <div className="flex justify-center">
            <nav className="flex items-center space-x-2">
              <motion.button
                onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className={`px-3 py-1 rounded-md ${currentPage === 1 ? 'text-gray-500 cursor-not-allowed' : 'text-white hover:bg-blue-700'}`}
                whileHover={currentPage !== 1 ? { scale: 1.05 } : undefined}
                whileTap={currentPage !== 1 ? { scale: 0.95 } : undefined}
              >
                Previous
              </motion.button>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                <motion.button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  className={`px-3 py-1 rounded-md ${currentPage === page ? 'bg-blue-600 text-white' : 'text-white hover:bg-blue-700'}`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 * (page - 1) }}
                >
                  {page}
                </motion.button>
              ))}

              <motion.button
                onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className={`px-3 py-1 rounded-md ${currentPage === totalPages ? 'text-gray-500 cursor-not-allowed' : 'text-white hover:bg-blue-700'}`}
                whileHover={currentPage !== totalPages ? { scale: 1.05 } : undefined}
                whileTap={currentPage !== totalPages ? { scale: 0.95 } : undefined}
              >
                Next
              </motion.button>
            </nav>
          </div>
        </AnimatedSection>
      )}

      {/* Call to Action */}
      <AnimatedSection className="mb-12" animation="fadeInUp" delay={200}>
        <div className="bg-blue-900 rounded-lg p-8 text-center">
          <motion.h2
            className="text-2xl font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            Ready to simplify compliance?
          </motion.h2>
          <motion.p
            className="mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            Join the NovaFuse Compliance App Store and transform your regulatory compliance program.
          </motion.p>
          <motion.button
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => window.location.href = '/contact'}
          >
            Get Started
          </motion.button>
        </div>
      </AnimatedSection>

      {/* Confidentiality Notice */}
      <AnimatedSection animation="fadeInUp" delay={300}>
        <motion.div
          className="border border-blue-800 bg-blue-900 bg-opacity-20 rounded-lg p-4"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
        >
          <div className="flex items-start">
            <motion.div
              className="text-yellow-400 mr-3 mt-1"
              animate={{ rotate: [0, 10, 0, -10, 0] }}
              transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 5 }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </motion.div>
            <div>
              <p className="text-sm">
                <strong>CONFIDENTIAL:</strong> The NovaFuse Compliance App Store is currently under IP protection review.
                All content is considered confidential and proprietary. Unauthorized access or sharing is prohibited.
              </p>
            </div>
          </div>
        </motion.div>
      </AnimatedSection>
    </PageWithSidebar>
  );
}
