{"name": "novaactuary", "version": "1.0.0-REVOLUTIONARY", "description": "NovaActuary™ - The ∂Ψ=0 Underwriting Revolution", "main": "index.js", "scripts": {"test": "node test-novaactuary.js", "start": "node index.js", "demo": "node demo-novaactuary.js"}, "keywords": ["actuarial", "insurance", "comphyology", "psi-zero", "pi-coherence", "csm-prs", "mathematical-underwriting", "cyber-safety", "novafuse"], "author": "<PERSON>, NovaFuse Technologies", "license": "PROPRIETARY", "dependencies": {"perf_hooks": "*"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/novafuse/novafuse-api-superstore"}, "bugs": {"url": "https://github.com/novafuse/novafuse-api-superstore/issues"}, "homepage": "https://novafuse.com/novaactuary"}