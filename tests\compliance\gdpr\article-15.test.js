/**
 * GDPR Article 15 Test Implementation
 * 
 * Tests NovaFuse compliance with GDPR Article 15: Right of access by the data subject
 */

const { describe, it, expect } = require('@jest/globals');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import the TrackingManager mock
const { TrackingManager } = require('../../mocks/tracking-manager.mock');

describe('GDPR Compliance - Article 15: Right of access by the data subject', () => {
  let trackingManager;
  let tempDir;
  
  beforeEach(() => {
    // Create a temporary directory for test data
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'gdpr-test-'));
    
    // Initialize the TrackingManager with the temporary directory
    trackingManager = new TrackingManager(tempDir);
  });
  
  afterEach(() => {
    // Clean up the temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });
  
  it('should support tracking data subject access requests', () => {
    // Create a requirement for data subject access requests
    const requirement = trackingManager.create_requirement({
      name: 'Implement Data Subject Access Request Process',
      description: 'Implement a process for handling data subject access requests in compliance with GDPR Article 15',
      framework: 'GDPR',
      category: 'Data Subject Rights',
      priority: 'high',
      status: 'in_progress',
      due_date: '2023-12-31',
      assigned_to: 'privacy_officer',
      tags: ['gdpr', 'article_15', 'data_subject_rights', 'dsar']
    });
    
    // Create activities for implementing the requirement
    const activity1 = trackingManager.create_activity({
      name: 'Create DSAR Form',
      description: 'Create a form for data subjects to submit access requests',
      requirement_id: requirement.id,
      type: 'documentation',
      status: 'completed',
      start_date: '2023-01-01',
      end_date: '2023-01-15',
      assigned_to: 'privacy_officer',
      notes: 'DSAR form created and approved'
    });
    
    const activity2 = trackingManager.create_activity({
      name: 'Implement DSAR Workflow',
      description: 'Implement a workflow for processing data subject access requests',
      requirement_id: requirement.id,
      type: 'implementation',
      status: 'in_progress',
      start_date: '2023-01-16',
      end_date: '2023-02-15',
      assigned_to: 'developer',
      notes: 'DSAR workflow implementation in progress'
    });
    
    const activity3 = trackingManager.create_activity({
      name: 'Train Staff on DSAR Process',
      description: 'Train staff on how to handle data subject access requests',
      requirement_id: requirement.id,
      type: 'training',
      status: 'pending',
      start_date: '2023-02-16',
      end_date: '2023-02-28',
      assigned_to: 'privacy_officer',
      notes: 'Training scheduled'
    });
    
    // Verify the requirement was created correctly
    expect(requirement).toBeDefined();
    expect(requirement.framework).toBe('GDPR');
    expect(requirement.tags).toContain('article_15');
    
    // Verify the activities were created correctly
    expect(activity1).toBeDefined();
    expect(activity1.requirement_id).toBe(requirement.id);
    expect(activity1.status).toBe('completed');
    
    expect(activity2).toBeDefined();
    expect(activity2.requirement_id).toBe(requirement.id);
    expect(activity2.status).toBe('in_progress');
    
    expect(activity3).toBeDefined();
    expect(activity3.requirement_id).toBe(requirement.id);
    expect(activity3.status).toBe('pending');
    
    // Verify the activities can be retrieved for the requirement
    const activities = trackingManager.get_requirement_activities(requirement.id);
    expect(activities.length).toBe(3);
    expect(activities).toContainEqual(activity1);
    expect(activities).toContainEqual(activity2);
    expect(activities).toContainEqual(activity3);
    
    // Document compliance status
    console.log('COMPLIANCE STATUS: PARTIAL');
    console.log('COMPLIANCE NOTES: NovaFuse supports tracking data subject access requests, but does not provide built-in functionality for processing them.');
    console.log('GAPS: NovaFuse does not provide templates or automated workflows for handling data subject access requests.');
    console.log('RECOMMENDATIONS: Add templates and automated workflows for handling data subject access requests.');
  });
  
  it('should support retrieving all data related to a data subject', () => {
    // Create requirements and activities for a data subject
    const dataSubject = '<EMAIL>';
    
    // Create requirements assigned to the data subject
    const requirement1 = trackingManager.create_requirement({
      name: 'GDPR Requirement 1',
      framework: 'GDPR',
      assigned_to: dataSubject
    });
    
    const requirement2 = trackingManager.create_requirement({
      name: 'GDPR Requirement 2',
      framework: 'GDPR',
      assigned_to: dataSubject
    });
    
    // Create activities assigned to the data subject
    const activity1 = trackingManager.create_activity({
      name: 'GDPR Activity 1',
      requirement_id: requirement1.id,
      assigned_to: dataSubject
    });
    
    const activity2 = trackingManager.create_activity({
      name: 'GDPR Activity 2',
      requirement_id: requirement2.id,
      assigned_to: dataSubject
    });
    
    // Create data for another data subject (should not be included in results)
    const otherRequirement = trackingManager.create_requirement({
      name: 'Other Requirement',
      framework: 'GDPR',
      assigned_to: '<EMAIL>'
    });
    
    const otherActivity = trackingManager.create_activity({
      name: 'Other Activity',
      requirement_id: otherRequirement.id,
      assigned_to: '<EMAIL>'
    });
    
    // Get all data for the data subject
    const dataSubjectRequirements = Object.values(trackingManager.requirements)
      .filter(req => req.assigned_to === dataSubject);
    
    const dataSubjectActivities = Object.values(trackingManager.activities)
      .filter(act => act.assigned_to === dataSubject);
    
    // Verify that all data for the data subject is retrieved
    expect(dataSubjectRequirements.length).toBe(2);
    expect(dataSubjectRequirements).toContainEqual(requirement1);
    expect(dataSubjectRequirements).toContainEqual(requirement2);
    
    expect(dataSubjectActivities.length).toBe(2);
    expect(dataSubjectActivities).toContainEqual(activity1);
    expect(dataSubjectActivities).toContainEqual(activity2);
    
    // Verify that data for other data subjects is not included
    expect(dataSubjectRequirements).not.toContainEqual(otherRequirement);
    expect(dataSubjectActivities).not.toContainEqual(otherActivity);
    
    // Document compliance status
    console.log('COMPLIANCE STATUS: PASSED');
    console.log('COMPLIANCE NOTES: NovaFuse supports retrieving all data related to a data subject.');
    console.log('GAPS: None');
    console.log('RECOMMENDATIONS: None');
  });
});
