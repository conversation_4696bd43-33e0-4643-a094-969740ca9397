/**
 * HealthcareIntegrationTest.js
 * 
 * This module provides tests for the healthcare integration in NovaDNA.
 */

const assert = require('assert');
const NovaConnectAdapter = require('../../integration/NovaConnectAdapter');
const HealthcareIntegration = require('../../integration/healthcare/HealthcareIntegration');
const EmergencyDataPipeline = require('../../integration/healthcare/EmergencyDataPipeline');
const ProviderConnector = require('../../integration/healthcare/ProviderConnector');
const DataSourcePrioritization = require('../../integration/healthcare/DataSourcePrioritization');
const SecureTemporaryCache = require('../../integration/healthcare/SecureTemporaryCache');

/**
 * Test suite for healthcare integration
 */
describe('Healthcare Integration Tests', () => {
  let novaConnectAdapter;
  let healthcareIntegration;
  
  // Set up test environment
  beforeEach(() => {
    // Initialize NovaConnectAdapter with mock configuration
    novaConnectAdapter = new NovaConnectAdapter({
      apiUrl: 'http://localhost:3000/api/novaconnect',
      apiKey: 'test-api-key',
      apiSecret: 'test-api-secret'
    });
    
    // Mock the client to prevent actual API calls
    novaConnectAdapter.client = {
      get: async () => ({ data: [] }),
      post: async () => ({ data: { success: true } })
    };
    
    // Initialize HealthcareIntegration
    healthcareIntegration = new HealthcareIntegration({
      novaConnectAdapter,
      cacheEnabled: true,
      encryptionEnabled: true
    });
  });
  
  /**
   * Test emergency session management
   */
  describe('Emergency Session Management', () => {
    it('should create an emergency session', () => {
      // Create emergency session
      const session = healthcareIntegration.startEmergencySession({
        emergencyType: 'CARDIAC',
        emergencySeverity: 'HIGH',
        responderType: 'PARAMEDIC',
        locationType: 'AMBULANCE'
      });
      
      // Verify session
      assert.strictEqual(typeof session, 'object', 'Session should be an object');
      assert.strictEqual(typeof session.sessionId, 'string', 'Session should have a session ID');
      assert.strictEqual(typeof session.timestamp, 'string', 'Session should have a timestamp');
      assert.strictEqual(typeof session.expiresAt, 'string', 'Session should have an expiration time');
      assert.strictEqual(typeof session.context, 'object', 'Session should have a context object');
      assert.strictEqual(session.context.emergencyType, 'CARDIAC', 'Session should have correct emergency type');
      assert.strictEqual(session.context.emergencySeverity, 'HIGH', 'Session should have correct emergency severity');
    });
    
    it('should end an emergency session', () => {
      // Create emergency session
      const session = healthcareIntegration.startEmergencySession({
        emergencyType: 'CARDIAC',
        emergencySeverity: 'HIGH'
      });
      
      // End session
      const result = healthcareIntegration.endEmergencySession(session.sessionId);
      
      // Verify result
      assert.strictEqual(result, true, 'Session should be ended successfully');
      
      // Verify session is no longer active
      const activeSessions = healthcareIntegration.getActiveEmergencySessions();
      const sessionStillActive = activeSessions.some(s => s.sessionId === session.sessionId);
      assert.strictEqual(sessionStillActive, false, 'Session should no longer be active');
    });
    
    it('should get active emergency sessions', () => {
      // Create multiple sessions
      const session1 = healthcareIntegration.startEmergencySession({
        emergencyType: 'CARDIAC',
        emergencySeverity: 'HIGH'
      });
      
      const session2 = healthcareIntegration.startEmergencySession({
        emergencyType: 'TRAUMA',
        emergencySeverity: 'CRITICAL'
      });
      
      // Get active sessions
      const activeSessions = healthcareIntegration.getActiveEmergencySessions();
      
      // Verify active sessions
      assert.strictEqual(Array.isArray(activeSessions), true, 'Active sessions should be an array');
      assert.strictEqual(activeSessions.length >= 2, true, 'There should be at least 2 active sessions');
      
      // Verify session details
      const foundSession1 = activeSessions.find(s => s.sessionId === session1.sessionId);
      const foundSession2 = activeSessions.find(s => s.sessionId === session2.sessionId);
      
      assert.strictEqual(typeof foundSession1, 'object', 'Session 1 should be found');
      assert.strictEqual(typeof foundSession2, 'object', 'Session 2 should be found');
      assert.strictEqual(foundSession1.context.emergencyType, 'CARDIAC', 'Session 1 should have correct emergency type');
      assert.strictEqual(foundSession2.context.emergencyType, 'TRAUMA', 'Session 2 should have correct emergency type');
    });
  });
  
  /**
   * Test EmergencyDataPipeline
   */
  describe('Emergency Data Pipeline', () => {
    let emergencyDataPipeline;
    
    beforeEach(() => {
      // Initialize EmergencyDataPipeline
      emergencyDataPipeline = new EmergencyDataPipeline({
        novaConnectAdapter,
        cacheEnabled: true
      });
    });
    
    it('should create a transfer session', () => {
      // Create transfer session
      const session = emergencyDataPipeline.createTransferSession({
        emergencyType: 'CARDIAC',
        emergencySeverity: 'HIGH'
      });
      
      // Verify session
      assert.strictEqual(typeof session, 'object', 'Session should be an object');
      assert.strictEqual(typeof session.sessionId, 'string', 'Session should have a session ID');
      assert.strictEqual(typeof session.timestamp, 'string', 'Session should have a timestamp');
      assert.strictEqual(typeof session.expiresAt, 'string', 'Session should have an expiration time');
      assert.strictEqual(session.status, 'ACTIVE', 'Session should be active');
    });
    
    it('should close a transfer session', () => {
      // Create transfer session
      const session = emergencyDataPipeline.createTransferSession({
        emergencyType: 'CARDIAC',
        emergencySeverity: 'HIGH'
      });
      
      // Close session
      const result = emergencyDataPipeline.closeSession(session.sessionId);
      
      // Verify result
      assert.strictEqual(result, true, 'Session should be closed successfully');
    });
    
    it('should get active sessions', () => {
      // Create multiple sessions
      emergencyDataPipeline.createTransferSession({
        emergencyType: 'CARDIAC',
        emergencySeverity: 'HIGH'
      });
      
      emergencyDataPipeline.createTransferSession({
        emergencyType: 'TRAUMA',
        emergencySeverity: 'CRITICAL'
      });
      
      // Get active sessions
      const activeSessions = emergencyDataPipeline.getActiveSessions();
      
      // Verify active sessions
      assert.strictEqual(Array.isArray(activeSessions), true, 'Active sessions should be an array');
      assert.strictEqual(activeSessions.length >= 2, true, 'There should be at least 2 active sessions');
    });
  });
  
  /**
   * Test DataSourcePrioritization
   */
  describe('Data Source Prioritization', () => {
    let dataSourcePrioritization;
    
    beforeEach(() => {
      // Initialize DataSourcePrioritization
      dataSourcePrioritization = new DataSourcePrioritization();
    });
    
    it('should prioritize data sources based on context', () => {
      // Define data sources
      const dataSources = [
        {
          id: 'epic-1',
          type: 'EHR',
          provider: 'Epic',
          availableDataTypes: ['demographics', 'allergies', 'medications', 'conditions'],
          lastUpdated: new Date().toISOString()
        },
        {
          id: 'cerner-1',
          type: 'EHR',
          provider: 'Cerner',
          availableDataTypes: ['demographics', 'allergies', 'medications'],
          lastUpdated: new Date(Date.now() - 86400000).toISOString() // 1 day ago
        },
        {
          id: 'personal-1',
          type: 'PERSONAL',
          provider: 'Patient',
          availableDataTypes: ['demographics', 'allergies', 'medications', 'conditions'],
          lastUpdated: new Date().toISOString()
        }
      ];
      
      // Prioritize for cardiac emergency
      const cardiacPriorities = dataSourcePrioritization.prioritizeDataSources(dataSources, {
        emergencyType: 'CARDIAC',
        emergencySeverity: 'HIGH'
      });
      
      // Verify priorities
      assert.strictEqual(Array.isArray(cardiacPriorities), true, 'Priorities should be an array');
      assert.strictEqual(cardiacPriorities.length, 3, 'There should be 3 prioritized sources');
      
      // Epic should be first for cardiac emergency (fresher data + EHR type)
      assert.strictEqual(cardiacPriorities[0].id, 'epic-1', 'Epic should be the highest priority for cardiac emergency');
      
      // Prioritize for allergic emergency
      const allergicPriorities = dataSourcePrioritization.prioritizeDataSources(dataSources, {
        emergencyType: 'ALLERGIC',
        emergencySeverity: 'HIGH'
      });
      
      // Verify priorities
      assert.strictEqual(Array.isArray(allergicPriorities), true, 'Priorities should be an array');
      assert.strictEqual(allergicPriorities.length, 3, 'There should be 3 prioritized sources');
    });
    
    it('should rate data sources', () => {
      // Rate a data source
      const result = dataSourcePrioritization.rateDataSource('epic-1', {
        score: 8,
        reason: 'Good data quality'
      });
      
      // Verify result
      assert.strictEqual(result, true, 'Rating should be successful');
      
      // Get ratings
      const ratings = dataSourcePrioritization.getDataSourceRatings();
      
      // Verify ratings
      assert.strictEqual(typeof ratings, 'object', 'Ratings should be an object');
      assert.strictEqual(typeof ratings['epic-1'], 'object', 'Epic rating should exist');
      assert.strictEqual(ratings['epic-1'].averageScore, 8, 'Epic average score should be 8');
      assert.strictEqual(ratings['epic-1'].count, 1, 'Epic rating count should be 1');
    });
    
    it('should set custom priorities', () => {
      // Set custom priorities
      const result = dataSourcePrioritization.setCustomPriorities('TRAUMA', {
        sourceTypes: {
          EHR: 10,
          HIE: 8,
          PERSONAL: 5
        },
        providers: {
          Epic: 10,
          Cerner: 8
        }
      });
      
      // Verify result
      assert.strictEqual(result, true, 'Setting custom priorities should be successful');
      
      // Define data sources
      const dataSources = [
        {
          id: 'epic-1',
          type: 'EHR',
          provider: 'Epic',
          availableDataTypes: ['demographics', 'allergies', 'medications', 'conditions'],
          lastUpdated: new Date().toISOString()
        },
        {
          id: 'cerner-1',
          type: 'EHR',
          provider: 'Cerner',
          availableDataTypes: ['demographics', 'allergies', 'medications'],
          lastUpdated: new Date().toISOString()
        }
      ];
      
      // Prioritize with custom priorities
      const priorities = dataSourcePrioritization.prioritizeDataSources(dataSources, {
        emergencyType: 'TRAUMA',
        emergencySeverity: 'HIGH'
      });
      
      // Verify priorities
      assert.strictEqual(Array.isArray(priorities), true, 'Priorities should be an array');
      assert.strictEqual(priorities.length, 2, 'There should be 2 prioritized sources');
      assert.strictEqual(priorities[0].id, 'epic-1', 'Epic should be the highest priority with custom priorities');
    });
  });
  
  /**
   * Test SecureTemporaryCache
   */
  describe('Secure Temporary Cache', () => {
    let secureTemporaryCache;
    
    beforeEach(() => {
      // Initialize SecureTemporaryCache
      secureTemporaryCache = new SecureTemporaryCache({
        enabled: true,
        encryptionEnabled: true,
        defaultTTL: 300000, // 5 minutes
        maxTTL: 3600000 // 1 hour
      });
    });
    
    it('should store and retrieve data', () => {
      // Store data
      const key = 'test-key';
      const data = { name: 'Test Data', value: 123 };
      
      const storeResult = secureTemporaryCache.store(key, data, {
        context: {
          sessionId: 'test-session',
          emergencyType: 'CARDIAC'
        }
      });
      
      // Verify store result
      assert.strictEqual(storeResult, true, 'Data should be stored successfully');
      
      // Retrieve data
      const retrievedData = secureTemporaryCache.retrieve(key, {
        sessionId: 'test-session',
        action: 'RETRIEVE'
      });
      
      // Verify retrieved data
      assert.strictEqual(typeof retrievedData, 'object', 'Retrieved data should be an object');
      assert.strictEqual(retrievedData.name, 'Test Data', 'Retrieved data should have correct name');
      assert.strictEqual(retrievedData.value, 123, 'Retrieved data should have correct value');
    });
    
    it('should remove data', () => {
      // Store data
      const key = 'test-key';
      const data = { name: 'Test Data', value: 123 };
      
      secureTemporaryCache.store(key, data);
      
      // Remove data
      const removeResult = secureTemporaryCache.remove(key, {
        reason: 'TEST'
      });
      
      // Verify remove result
      assert.strictEqual(removeResult, true, 'Data should be removed successfully');
      
      // Try to retrieve removed data
      const retrievedData = secureTemporaryCache.retrieve(key);
      
      // Verify retrieved data
      assert.strictEqual(retrievedData, null, 'Removed data should not be retrievable');
    });
    
    it('should get cache statistics', () => {
      // Store some data
      secureTemporaryCache.store('key1', { value: 1 });
      secureTemporaryCache.store('key2', { value: 2 });
      
      // Get stats
      const stats = secureTemporaryCache.getStats();
      
      // Verify stats
      assert.strictEqual(typeof stats, 'object', 'Stats should be an object');
      assert.strictEqual(stats.enabled, true, 'Cache should be enabled');
      assert.strictEqual(stats.encryptionEnabled, true, 'Encryption should be enabled');
      assert.strictEqual(stats.totalEntries >= 2, true, 'There should be at least 2 entries');
    });
    
    it('should get access logs', () => {
      // Store and retrieve data to generate logs
      secureTemporaryCache.store('key1', { value: 1 });
      secureTemporaryCache.retrieve('key1');
      
      // Get logs
      const logs = secureTemporaryCache.getAccessLogs();
      
      // Verify logs
      assert.strictEqual(Array.isArray(logs), true, 'Logs should be an array');
      assert.strictEqual(logs.length >= 2, true, 'There should be at least 2 log entries');
      
      // Verify log entries
      const storeLog = logs.find(log => log.action === 'STORE');
      const retrieveLog = logs.find(log => log.action === 'RETRIEVE');
      
      assert.strictEqual(typeof storeLog, 'object', 'Store log should exist');
      assert.strictEqual(typeof retrieveLog, 'object', 'Retrieve log should exist');
      assert.strictEqual(storeLog.key, 'key1', 'Store log should have correct key');
      assert.strictEqual(retrieveLog.key, 'key1', 'Retrieve log should have correct key');
    });
  });
});

// If running directly (not through a test runner)
if (require.main === module) {
  // Simple test runner
  const runTests = async () => {
    console.log('Running Healthcare Integration Tests...');
    
    // Initialize components
    const novaConnectAdapter = new NovaConnectAdapter({
      apiUrl: 'http://localhost:3000/api/novaconnect',
      apiKey: 'test-api-key',
      apiSecret: 'test-api-secret'
    });
    
    // Mock the client to prevent actual API calls
    novaConnectAdapter.client = {
      get: async () => ({ data: [] }),
      post: async () => ({ data: { success: true } })
    };
    
    const healthcareIntegration = new HealthcareIntegration({
      novaConnectAdapter,
      cacheEnabled: true,
      encryptionEnabled: true
    });
    
    // Test emergency session management
    console.log('\nTesting Emergency Session Management...');
    const session = healthcareIntegration.startEmergencySession({
      emergencyType: 'CARDIAC',
      emergencySeverity: 'HIGH',
      responderType: 'PARAMEDIC',
      locationType: 'AMBULANCE'
    });
    
    console.log('- Session created successfully');
    console.log(`- Session ID: ${session.sessionId}`);
    console.log(`- Emergency Type: ${session.context.emergencyType}`);
    console.log(`- Expires At: ${session.expiresAt}`);
    
    // Test data source prioritization
    console.log('\nTesting Data Source Prioritization...');
    const dataSourcePrioritization = new DataSourcePrioritization();
    
    const dataSources = [
      {
        id: 'epic-1',
        type: 'EHR',
        provider: 'Epic',
        availableDataTypes: ['demographics', 'allergies', 'medications', 'conditions'],
        lastUpdated: new Date().toISOString()
      },
      {
        id: 'cerner-1',
        type: 'EHR',
        provider: 'Cerner',
        availableDataTypes: ['demographics', 'allergies', 'medications'],
        lastUpdated: new Date(Date.now() - 86400000).toISOString() // 1 day ago
      }
    ];
    
    const priorities = dataSourcePrioritization.prioritizeDataSources(dataSources, {
      emergencyType: 'CARDIAC',
      emergencySeverity: 'HIGH'
    });
    
    console.log('- Data sources prioritized successfully');
    console.log(`- Highest priority: ${priorities[0].id}`);
    
    // Test secure temporary cache
    console.log('\nTesting Secure Temporary Cache...');
    const secureTemporaryCache = new SecureTemporaryCache({
      enabled: true,
      encryptionEnabled: true
    });
    
    secureTemporaryCache.store('test-key', { name: 'Test Data', value: 123 });
    const retrievedData = secureTemporaryCache.retrieve('test-key');
    
    console.log('- Data stored and retrieved successfully');
    console.log(`- Retrieved data: ${JSON.stringify(retrievedData)}`);
    
    const stats = secureTemporaryCache.getStats();
    console.log('- Cache statistics retrieved successfully');
    console.log(`- Enabled: ${stats.enabled}`);
    console.log(`- Encryption Enabled: ${stats.encryptionEnabled}`);
    console.log(`- Total Entries: ${stats.totalEntries}`);
    
    // End emergency session
    const endResult = healthcareIntegration.endEmergencySession(session.sessionId);
    console.log('\n- Session ended successfully:', endResult);
    
    console.log('\nAll tests completed successfully!');
  };
  
  runTests().catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testHealthcareIntegration: () => {
    // Initialize components
    const novaConnectAdapter = new NovaConnectAdapter({
      apiUrl: 'http://localhost:3000/api/novaconnect',
      apiKey: 'test-api-key',
      apiSecret: 'test-api-secret'
    });
    
    // Mock the client to prevent actual API calls
    novaConnectAdapter.client = {
      get: async () => ({ data: [] }),
      post: async () => ({ data: { success: true } })
    };
    
    const healthcareIntegration = new HealthcareIntegration({
      novaConnectAdapter,
      cacheEnabled: true,
      encryptionEnabled: true
    });
    
    // Run basic tests
    const session = healthcareIntegration.startEmergencySession({
      emergencyType: 'CARDIAC',
      emergencySeverity: 'HIGH'
    });
    
    const activeSessions = healthcareIntegration.getActiveEmergencySessions();
    const status = healthcareIntegration.getStatus();
    
    return {
      success: true,
      session,
      activeSessions,
      status
    };
  }
};
