/**
 * merkle-tree.js
 * 
 * This file implements a Merkle tree for NovaProof.
 * A Merkle tree is a binary tree of hashes, where each leaf node is a hash of a data block,
 * and each non-leaf node is a hash of its children.
 */

const crypto = require('crypto');

/**
 * MerkleNode class representing a node in the Merkle tree
 */
class MerkleNode {
  /**
   * Create a new Merkle node
   * @param {string} hash - The hash value of the node
   * @param {MerkleNode|null} left - The left child node
   * @param {MerkleNode|null} right - The right child node
   * @param {Object} data - Additional data associated with the node
   */
  constructor(hash, left = null, right = null, data = null) {
    this.hash = hash;
    this.left = left;
    this.right = right;
    this.data = data;
    this.parent = null;
    
    // Set parent references for children
    if (left) left.parent = this;
    if (right) right.parent = this;
  }
  
  /**
   * Check if the node is a leaf node
   * @returns {boolean} - Whether the node is a leaf node
   */
  isLeaf() {
    return this.left === null && this.right === null;
  }
  
  /**
   * Get the sibling of the node
   * @returns {MerkleNode|null} - The sibling node, or null if no parent
   */
  getSibling() {
    if (!this.parent) return null;
    return this.parent.left === this ? this.parent.right : this.parent.left;
  }
  
  /**
   * Convert the node to a JSON-serializable object
   * @returns {Object} - A JSON-serializable representation of the node
   */
  toJSON() {
    return {
      hash: this.hash,
      isLeaf: this.isLeaf(),
      data: this.data
    };
  }
}

/**
 * MerkleTree class for creating and verifying Merkle trees
 */
class MerkleTree {
  /**
   * Create a new Merkle tree
   * @param {Array<string|Object>} items - The items to include in the tree
   * @param {Object} options - Options for creating the tree
   */
  constructor(items = [], options = {}) {
    this.options = {
      hashFunction: options.hashFunction || 'sha256',
      hashEncoding: options.hashEncoding || 'hex',
      sortLeaves: options.sortLeaves !== undefined ? options.sortLeaves : false,
      sortPairs: options.sortPairs !== undefined ? options.sortPairs : false,
      duplicateOdd: options.duplicateOdd !== undefined ? options.duplicateOdd : true,
      ...options
    };
    
    this.leaves = [];
    this.root = null;
    
    // Build the tree if items are provided
    if (items.length > 0) {
      this.build(items);
    }
  }
  
  /**
   * Hash data using the configured hash function
   * @param {string|Buffer} data - The data to hash
   * @returns {string} - The hash of the data
   */
  hash(data) {
    if (Buffer.isBuffer(data) || typeof data === 'string') {
      return crypto
        .createHash(this.options.hashFunction)
        .update(data)
        .digest(this.options.hashEncoding);
    } else {
      return crypto
        .createHash(this.options.hashFunction)
        .update(JSON.stringify(data))
        .digest(this.options.hashEncoding);
    }
  }
  
  /**
   * Combine two hashes to create a parent hash
   * @param {string} left - The left hash
   * @param {string} right - The right hash
   * @returns {string} - The combined hash
   */
  combinedHash(left, right) {
    if (this.options.sortPairs && left > right) {
      [left, right] = [right, left];
    }
    
    return this.hash(left + right);
  }
  
  /**
   * Build the Merkle tree from items
   * @param {Array<string|Object>} items - The items to include in the tree
   * @returns {MerkleTree} - The Merkle tree instance
   */
  build(items) {
    if (items.length === 0) {
      throw new Error('Cannot build a Merkle tree with no items');
    }
    
    // Create leaf nodes
    let leaves = items.map(item => {
      const hash = typeof item === 'string' ? item : this.hash(item);
      return new MerkleNode(hash, null, null, item);
    });
    
    // Sort leaves if configured
    if (this.options.sortLeaves) {
      leaves.sort((a, b) => a.hash.localeCompare(b.hash));
    }
    
    // If odd number of leaves and duplicateOdd is true, duplicate the last leaf
    if (leaves.length % 2 === 1 && this.options.duplicateOdd) {
      leaves.push(leaves[leaves.length - 1]);
    }
    
    this.leaves = leaves;
    
    // Build the tree bottom-up
    let nodes = leaves;
    while (nodes.length > 1) {
      const parents = [];
      
      for (let i = 0; i < nodes.length; i += 2) {
        const left = nodes[i];
        const right = i + 1 < nodes.length ? nodes[i + 1] : left;
        
        const parentHash = this.combinedHash(left.hash, right.hash);
        const parent = new MerkleNode(parentHash, left, right);
        
        parents.push(parent);
      }
      
      nodes = parents;
    }
    
    this.root = nodes[0];
    return this;
  }
  
  /**
   * Get the root hash of the tree
   * @returns {string} - The root hash
   */
  getRootHash() {
    return this.root ? this.root.hash : null;
  }
  
  /**
   * Get the proof for a leaf by its index
   * @param {number} index - The index of the leaf
   * @returns {Array<Object>} - The proof for the leaf
   */
  getProof(index) {
    if (!this.root) {
      throw new Error('No root exists. Build the tree first.');
    }
    
    if (index < 0 || index >= this.leaves.length) {
      throw new Error(`Leaf index ${index} out of range (0 to ${this.leaves.length - 1})`);
    }
    
    const proof = [];
    let current = this.leaves[index];
    
    while (current.parent) {
      const sibling = current.getSibling();
      
      if (sibling) {
        proof.push({
          position: current.parent.left === current ? 'right' : 'left',
          hash: sibling.hash
        });
      }
      
      current = current.parent;
    }
    
    return proof;
  }
  
  /**
   * Get the proof for a leaf by its hash
   * @param {string} leafHash - The hash of the leaf
   * @returns {Array<Object>} - The proof for the leaf
   */
  getProofByHash(leafHash) {
    const index = this.leaves.findIndex(leaf => leaf.hash === leafHash);
    
    if (index === -1) {
      throw new Error(`Leaf with hash ${leafHash} not found`);
    }
    
    return this.getProof(index);
  }
  
  /**
   * Verify a proof for a leaf
   * @param {string} leafHash - The hash of the leaf
   * @param {Array<Object>} proof - The proof for the leaf
   * @param {string} rootHash - The root hash to verify against
   * @returns {boolean} - Whether the proof is valid
   */
  verifyProof(leafHash, proof, rootHash) {
    let currentHash = leafHash;
    
    for (const node of proof) {
      if (node.position === 'left') {
        currentHash = this.combinedHash(node.hash, currentHash);
      } else {
        currentHash = this.combinedHash(currentHash, node.hash);
      }
    }
    
    return currentHash === rootHash;
  }
  
  /**
   * Get all leaf hashes
   * @returns {Array<string>} - The hashes of all leaves
   */
  getLeafHashes() {
    return this.leaves.map(leaf => leaf.hash);
  }
  
  /**
   * Get the number of leaves in the tree
   * @returns {number} - The number of leaves
   */
  getLeafCount() {
    return this.leaves.length;
  }
  
  /**
   * Get the height of the tree
   * @returns {number} - The height of the tree
   */
  getHeight() {
    if (!this.root) return 0;
    
    let height = 0;
    let levelSize = 1;
    let totalNodes = 1;
    
    while (totalNodes < this.leaves.length) {
      height++;
      levelSize *= 2;
      totalNodes += levelSize;
    }
    
    return height + 1; // Add 1 for the root level
  }
  
  /**
   * Convert the tree to a JSON-serializable object
   * @returns {Object} - A JSON-serializable representation of the tree
   */
  toJSON() {
    return {
      rootHash: this.getRootHash(),
      leaves: this.getLeafHashes(),
      leafCount: this.getLeafCount(),
      height: this.getHeight(),
      options: this.options
    };
  }
  
  /**
   * Create a Merkle tree from a JSON object
   * @param {Object} json - The JSON representation of a Merkle tree
   * @param {Array<string|Object>} items - The original items (optional)
   * @returns {MerkleTree} - A new Merkle tree created from the JSON
   */
  static fromJSON(json, items = []) {
    const tree = new MerkleTree([], json.options);
    
    if (items.length > 0) {
      tree.build(items);
      
      // Verify that the root hash matches
      if (tree.getRootHash() !== json.rootHash) {
        throw new Error('Root hash mismatch. The provided items do not match the JSON representation.');
      }
    } else if (json.leaves.length > 0) {
      // Build from leaf hashes
      tree.build(json.leaves);
    }
    
    return tree;
  }
}

module.exports = {
  MerkleTree,
  MerkleNode
};
