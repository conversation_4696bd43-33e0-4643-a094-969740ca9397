/**
 * Theme Controller
 * 
 * This controller handles API requests related to themes.
 */

const ThemeService = require('../services/ThemeService');
const { ValidationError } = require('../utils/errors');

class ThemeController {
  constructor() {
    this.themeService = new ThemeService();
  }

  /**
   * Get all themes
   */
  async getAllThemes(req, res, next) {
    try {
      const themes = await this.themeService.getAllThemes();
      res.json(themes);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get theme by ID
   */
  async getThemeById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Theme ID is required');
      }
      
      const theme = await this.themeService.getThemeById(id);
      res.json(theme);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new theme
   */
  async createTheme(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Theme data is required');
      }
      
      const theme = await this.themeService.createTheme(data, req.user.id);
      res.status(201).json(theme);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a theme
   */
  async updateTheme(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Theme ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Theme data is required');
      }
      
      const theme = await this.themeService.updateTheme(id, data, req.user.id);
      res.json(theme);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a theme
   */
  async deleteTheme(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Theme ID is required');
      }
      
      const result = await this.themeService.deleteTheme(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Clone a theme
   */
  async cloneTheme(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body || {};
      
      if (!id) {
        throw new ValidationError('Theme ID is required');
      }
      
      const theme = await this.themeService.cloneTheme(id, data, req.user.id);
      res.status(201).json(theme);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get organization theme
   */
  async getOrganizationTheme(req, res, next) {
    try {
      const { organizationId } = req.params;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      const theme = await this.themeService.getOrganizationTheme(organizationId);
      res.json(theme);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Set organization theme
   */
  async setOrganizationTheme(req, res, next) {
    try {
      const { organizationId } = req.params;
      const { themeId } = req.body;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      if (!themeId) {
        throw new ValidationError('Theme ID is required');
      }
      
      const result = await this.themeService.setOrganizationTheme(organizationId, themeId, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reset organization theme to default
   */
  async resetOrganizationTheme(req, res, next) {
    try {
      const { organizationId } = req.params;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      const result = await this.themeService.resetOrganizationTheme(organizationId, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get theme CSS
   */
  async getThemeCss(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Theme ID is required');
      }
      
      const css = await this.themeService.getThemeCss(id);
      
      // Set content type to CSS
      res.set('Content-Type', 'text/css');
      res.send(css);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get organization theme CSS
   */
  async getOrganizationThemeCss(req, res, next) {
    try {
      const { organizationId } = req.params;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      const css = await this.themeService.getOrganizationThemeCss(organizationId);
      
      // Set content type to CSS
      res.set('Content-Type', 'text/css');
      res.send(css);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new ThemeController();
