/**
 * Authentication Routes
 */

const express = require('express');
const router = express.Router();
const AuthController = require('../controllers/AuthController');
const { authenticate, hasRole } = require('../middleware/authMiddleware');

// Public routes
router.post('/register', (req, res, next) => {
  AuthController.register(req, res, next);
});

router.post('/login', (req, res, next) => {
  AuthController.login(req, res, next);
});

// Protected routes
router.post('/logout', authenticate, (req, res, next) => {
  AuthController.logout(req, res, next);
});

router.get('/me', authenticate, (req, res, next) => {
  AuthController.getCurrentUser(req, res, next);
});

// User management routes (admin only)
router.get('/users', authenticate, hasRole('admin'), (req, res, next) => {
  AuthController.getAllUsers(req, res, next);
});

// User routes
router.get('/users/:id', authenticate, (req, res, next) => {
  AuthController.getUserById(req, res, next);
});

router.put('/users/:id', authenticate, (req, res, next) => {
  AuthController.updateUser(req, res, next);
});

router.delete('/users/:id', authenticate, (req, res, next) => {
  AuthController.deleteUser(req, res, next);
});

// API key routes
router.post('/api-keys', authenticate, (req, res, next) => {
  AuthController.createApiKey(req, res, next);
});

router.get('/api-keys', authenticate, (req, res, next) => {
  AuthController.getApiKeys(req, res, next);
});

router.delete('/api-keys/:id', authenticate, (req, res, next) => {
  AuthController.deleteApiKey(req, res, next);
});

module.exports = router;
