{"openapi": "3.0.0", "info": {"title": "NovaFuse API Superstore", "description": "API documentation for NovaFuse API Superstore", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8000", "description": "Local Development Server"}], "paths": {"/governance/board/meetings": {"get": {"summary": "Get a list of board meetings", "description": "Returns a list of board meetings with optional filtering", "parameters": [{"name": "status", "in": "query", "description": "Filter by status (scheduled, completed, cancelled)", "schema": {"type": "string", "enum": ["scheduled", "completed", "cancelled"]}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "date": {"type": "string", "format": "date"}, "status": {"type": "string"}}}}, "total": {"type": "integer"}}}}}}}}}}}