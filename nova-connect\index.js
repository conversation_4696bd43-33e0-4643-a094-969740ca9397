/**
 * NovaFuse Universal API Connector
 * 
 * This is the main entry point for the Universal API Connector.
 */

const connectorApi = require('./api/connector-api');

async function main() {
  try {
    console.log('Starting NovaFuse Universal API Connector...');
    
    // Initialize the API
    await connectorApi.initialize();
    
    console.log('NovaFuse Universal API Connector started successfully');
  } catch (error) {
    console.error('Failed to start NovaFuse Universal API Connector:', error);
    process.exit(1);
  }
}

// Start the application
main();
