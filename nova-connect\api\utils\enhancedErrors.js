/**
 * Enhanced Error Classes
 * 
 * This module provides enhanced error classes for the NovaConnect API:
 * - Base error class with consistent properties
 * - Specific error classes for different error types
 * - Support for error codes and details
 * - Support for error severity levels
 * - Support for error context
 */

/**
 * Base error class
 */
class BaseError extends Error {
  /**
   * Create a new BaseError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {number} options.status - HTTP status code
   * @param {string} options.severity - Error severity (info, warn, error, fatal)
   * @param {Object} options.details - Error details
   * @param {Object} options.context - Error context
   * @param {Error} options.cause - Error cause
   */
  constructor(message, options = {}) {
    super(message);
    
    this.name = this.constructor.name;
    this.code = options.code || 'UNKNOWN_ERROR';
    this.status = options.status || 500;
    this.severity = options.severity || 'error';
    this.details = options.details || {};
    this.context = options.context || {};
    this.cause = options.cause;
    this.timestamp = new Date().toISOString();
    
    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }
  
  /**
   * Convert error to JSON
   * 
   * @param {boolean} includeStack - Whether to include stack trace
   * @returns {Object} - JSON representation of error
   */
  toJSON(includeStack = false) {
    const json = {
      name: this.name,
      message: this.message,
      code: this.code,
      status: this.status,
      severity: this.severity,
      details: this.details,
      timestamp: this.timestamp
    };
    
    if (includeStack) {
      json.stack = this.stack;
      
      if (this.cause) {
        json.cause = this.cause instanceof Error
          ? {
              name: this.cause.name,
              message: this.cause.message,
              stack: this.cause.stack
            }
          : this.cause;
      }
    }
    
    return json;
  }
}

/**
 * Validation error
 */
class ValidationError extends BaseError {
  /**
   * Create a new ValidationError
   * 
   * @param {string} message - Error message
   * @param {Object|Array} details - Validation error details
   * @param {Object} options - Error options
   */
  constructor(message, details = {}, options = {}) {
    super(message, {
      code: options.code || 'VALIDATION_ERROR',
      status: options.status || 400,
      severity: options.severity || 'warn',
      details: details,
      context: options.context,
      cause: options.cause
    });
  }
}

/**
 * Authentication error
 */
class AuthenticationError extends BaseError {
  /**
   * Create a new AuthenticationError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'AUTHENTICATION_ERROR',
      status: options.status || 401,
      severity: options.severity || 'warn',
      details: options.details,
      context: options.context,
      cause: options.cause
    });
  }
}

/**
 * Authorization error
 */
class AuthorizationError extends BaseError {
  /**
   * Create a new AuthorizationError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'AUTHORIZATION_ERROR',
      status: options.status || 403,
      severity: options.severity || 'warn',
      details: options.details,
      context: options.context,
      cause: options.cause
    });
  }
}

/**
 * Not found error
 */
class NotFoundError extends BaseError {
  /**
   * Create a new NotFoundError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'NOT_FOUND_ERROR',
      status: options.status || 404,
      severity: options.severity || 'warn',
      details: options.details,
      context: options.context,
      cause: options.cause
    });
  }
}

/**
 * Conflict error
 */
class ConflictError extends BaseError {
  /**
   * Create a new ConflictError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'CONFLICT_ERROR',
      status: options.status || 409,
      severity: options.severity || 'warn',
      details: options.details,
      context: options.context,
      cause: options.cause
    });
  }
}

/**
 * Rate limit error
 */
class RateLimitError extends BaseError {
  /**
   * Create a new RateLimitError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'RATE_LIMIT_ERROR',
      status: options.status || 429,
      severity: options.severity || 'warn',
      details: options.details,
      context: options.context,
      cause: options.cause
    });
  }
}

/**
 * Timeout error
 */
class TimeoutError extends BaseError {
  /**
   * Create a new TimeoutError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'TIMEOUT_ERROR',
      status: options.status || 504,
      severity: options.severity || 'error',
      details: options.details,
      context: options.context,
      cause: options.cause
    });
  }
}

/**
 * Database error
 */
class DatabaseError extends BaseError {
  /**
   * Create a new DatabaseError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'DATABASE_ERROR',
      status: options.status || 500,
      severity: options.severity || 'error',
      details: options.details,
      context: options.context,
      cause: options.cause
    });
  }
}

/**
 * External service error
 */
class ExternalServiceError extends BaseError {
  /**
   * Create a new ExternalServiceError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'EXTERNAL_SERVICE_ERROR',
      status: options.status || 502,
      severity: options.severity || 'error',
      details: options.details,
      context: options.context,
      cause: options.cause
    });
  }
}

/**
 * Internal server error
 */
class InternalServerError extends BaseError {
  /**
   * Create a new InternalServerError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'INTERNAL_SERVER_ERROR',
      status: options.status || 500,
      severity: options.severity || 'error',
      details: options.details,
      context: options.context,
      cause: options.cause
    });
  }
}

module.exports = {
  BaseError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  TimeoutError,
  DatabaseError,
  ExternalServiceError,
  InternalServerError
};
