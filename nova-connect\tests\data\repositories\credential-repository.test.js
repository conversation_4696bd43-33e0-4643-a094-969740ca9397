/**
 * NovaFuse Universal API Connector - Credential Repository Tests
 */

const { credentialRepository } = require('../../../src/data/repositories');
const Credential = require('../../../src/data/models/credential');

// Mock the Credential model
jest.mock('../../../src/data/models/credential', () => {
  return {
    findOne: jest.fn(),
    find: jest.fn().mockReturnThis(),
    countDocuments: jest.fn(),
    findOneAndUpdate: jest.fn(),
    deleteOne: jest.fn(),
    sort: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis()
  };
});

describe('Credential Repository', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  const mockCredential = {
    id: 'cred-12345',
    name: 'Test Credential',
    description: 'Test credential for unit tests',
    connectorId: 'test-connector-1.0',
    credentials: new Map([
      ['apiKey', 'encrypted-api-key']
    ]),
    ownerId: 'user-123',
    isActive: true,
    getDecryptedCredentials: jest.fn().mockReturnValue({ apiKey: 'test-api-key' }),
    updateLastUsed: jest.fn().mockResolvedValue(true),
    updateTestResult: jest.fn().mockResolvedValue(true)
  };
  
  it('should get a credential by ID', async () => {
    Credential.findOne.mockResolvedValue(mockCredential);
    
    const credential = await credentialRepository.getCredentialById('cred-12345');
    
    expect(Credential.findOne).toHaveBeenCalledWith({ id: 'cred-12345', isActive: true });
    expect(credential).toEqual(mockCredential);
  });
  
  it('should get credentials by connector', async () => {
    Credential.find.mockImplementation(() => ({
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockResolvedValue([mockCredential])
    }));
    
    const credentials = await credentialRepository.getCredentialsByConnector('test-connector-1.0', 'user-123');
    
    expect(Credential.find).toHaveBeenCalledWith({ connectorId: 'test-connector-1.0', ownerId: 'user-123', isActive: true });
    expect(credentials).toEqual([mockCredential]);
  });
  
  it('should get credentials by owner', async () => {
    Credential.find.mockImplementation(() => ({
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockResolvedValue([mockCredential])
    }));
    
    const credentials = await credentialRepository.getCredentialsByOwner('user-123');
    
    expect(Credential.find).toHaveBeenCalledWith({ ownerId: 'user-123', isActive: true });
    expect(credentials).toEqual([mockCredential]);
  });
  
  it('should create a credential', async () => {
    const mockCredentialModel = {
      save: jest.fn().mockResolvedValue(mockCredential)
    };
    
    jest.spyOn(global, 'Object').mockImplementationOnce(() => mockCredentialModel);
    
    const credential = await credentialRepository.createCredential(mockCredential);
    
    expect(mockCredentialModel.save).toHaveBeenCalled();
    expect(credential).toEqual(mockCredential);
    
    global.Object.mockRestore();
  });
  
  it('should update a credential', async () => {
    Credential.findOneAndUpdate.mockResolvedValue(mockCredential);
    
    const credential = await credentialRepository.updateCredential('cred-12345', { name: 'Updated Credential' });
    
    expect(Credential.findOneAndUpdate).toHaveBeenCalled();
    expect(credential).toEqual(mockCredential);
  });
  
  it('should delete a credential', async () => {
    Credential.findOneAndUpdate.mockResolvedValue(mockCredential);
    
    const result = await credentialRepository.deleteCredential('cred-12345');
    
    expect(Credential.findOneAndUpdate).toHaveBeenCalled();
    expect(result).toBe(true);
  });
  
  it('should hard delete a credential', async () => {
    Credential.deleteOne.mockResolvedValue({ deletedCount: 1 });
    
    const result = await credentialRepository.hardDeleteCredential('cred-12345');
    
    expect(Credential.deleteOne).toHaveBeenCalledWith({ id: 'cred-12345' });
    expect(result).toBe(true);
  });
  
  it('should update test result', async () => {
    Credential.findOne.mockResolvedValue(mockCredential);
    
    const result = await credentialRepository.updateTestResult('cred-12345', true, 'Test successful');
    
    expect(Credential.findOne).toHaveBeenCalledWith({ id: 'cred-12345', isActive: true });
    expect(mockCredential.updateTestResult).toHaveBeenCalledWith(true, 'Test successful');
    expect(result).toBe(true);
  });
  
  it('should update last used timestamp', async () => {
    Credential.findOne.mockResolvedValue(mockCredential);
    
    const result = await credentialRepository.updateLastUsed('cred-12345');
    
    expect(Credential.findOne).toHaveBeenCalledWith({ id: 'cred-12345', isActive: true });
    expect(mockCredential.updateLastUsed).toHaveBeenCalled();
    expect(result).toBe(true);
  });
  
  it('should count credentials', async () => {
    Credential.countDocuments.mockResolvedValue(3);
    
    const count = await credentialRepository.countCredentials();
    
    expect(Credential.countDocuments).toHaveBeenCalledWith({ isActive: true });
    expect(count).toBe(3);
  });
  
  it('should get decrypted credentials', async () => {
    Credential.findOne.mockResolvedValue(mockCredential);
    
    const credentials = await credentialRepository.getDecryptedCredentials('cred-12345');
    
    expect(Credential.findOne).toHaveBeenCalledWith({ id: 'cred-12345', isActive: true });
    expect(mockCredential.getDecryptedCredentials).toHaveBeenCalled();
    expect(credentials).toEqual({ apiKey: 'test-api-key' });
  });
});
