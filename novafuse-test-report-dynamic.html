<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Dynamic Test Report</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .metric-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .trend-up { color: #10b981; }
        .trend-down { color: #ef4444; }
        .trend-stable { color: #6b7280; }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Header -->
    <div class="gradient-bg p-8 mb-8">
        <div class="max-w-7xl mx-auto">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-4xl font-bold mb-2">NovaFuse Dynamic Test Report</h1>
                    <p class="text-xl opacity-90">Real-time analytics from NovaConnect Test Service</p>
                    <p class="text-sm opacity-75 mt-2">Generated: <span id="reportTimestamp">Loading...</span></p>
                </div>
                <div class="text-right">
                    <div class="text-sm opacity-75">Data Source</div>
                    <div id="dataSource" class="text-lg font-semibold">Connecting...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div id="loadingState" class="max-w-7xl mx-auto px-8 text-center py-16">
        <div class="animate-spin w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <p class="text-xl">Loading test analytics from NovaConnect...</p>
        <p class="text-sm text-gray-400 mt-2">Connecting to API server at localhost:3100</p>
    </div>

    <!-- Main Content -->
    <div id="reportContent" class="max-w-7xl mx-auto px-8 hidden">
        <!-- Summary Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="metric-card p-6 rounded-lg text-center">
                <div class="text-3xl font-bold mb-2" id="totalExecutions">-</div>
                <div class="text-sm opacity-90">Total Executions</div>
                <div class="text-xs opacity-75 mt-1" id="executionPeriod">Last 30 days</div>
            </div>
            <div class="metric-card p-6 rounded-lg text-center">
                <div class="text-3xl font-bold mb-2" id="avgPassRate">-</div>
                <div class="text-sm opacity-90">Average Pass Rate</div>
                <div class="text-xs opacity-75 mt-1 flex items-center justify-center">
                    <i id="passRateTrend" data-lucide="trending-up" class="w-4 h-4 mr-1"></i>
                    <span id="passRateTrendText">-</span>
                </div>
            </div>
            <div class="metric-card p-6 rounded-lg text-center">
                <div class="text-3xl font-bold mb-2" id="totalTests">-</div>
                <div class="text-sm opacity-90">Total Tests Run</div>
                <div class="text-xs opacity-75 mt-1" id="testsPerDay">- per day avg</div>
            </div>
            <div class="metric-card p-6 rounded-lg text-center">
                <div class="text-3xl font-bold mb-2" id="avgDuration">-</div>
                <div class="text-sm opacity-90">Avg Duration</div>
                <div class="text-xs opacity-75 mt-1">Per execution</div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Trend Chart -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h3 class="text-xl font-semibold mb-4 flex items-center">
                    <i data-lucide="trending-up" class="w-5 h-5 mr-2"></i>
                    Pass Rate Trends
                </h3>
                <canvas id="trendChart" width="400" height="200"></canvas>
            </div>

            <!-- Category Performance -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h3 class="text-xl font-semibold mb-4 flex items-center">
                    <i data-lucide="pie-chart" class="w-5 h-5 mr-2"></i>
                    Category Performance
                </h3>
                <canvas id="categoryChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Category Breakdown -->
        <div class="bg-gray-800 p-6 rounded-lg mb-8">
            <h3 class="text-xl font-semibold mb-6 flex items-center">
                <i data-lucide="layers" class="w-5 h-5 mr-2"></i>
                Test Category Analysis
            </h3>
            <div id="categoryBreakdown" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Categories will be populated here -->
            </div>
        </div>

        <!-- Recent Executions -->
        <div class="bg-gray-800 p-6 rounded-lg mb-8">
            <h3 class="text-xl font-semibold mb-6 flex items-center">
                <i data-lucide="clock" class="w-5 h-5 mr-2"></i>
                Recent Test Executions
            </h3>
            <div class="overflow-x-auto">
                <table class="w-full text-sm">
                    <thead>
                        <tr class="border-b border-gray-700">
                            <th class="text-left py-3 px-4">Execution ID</th>
                            <th class="text-left py-3 px-4">Start Time</th>
                            <th class="text-left py-3 px-4">Duration</th>
                            <th class="text-left py-3 px-4">Status</th>
                            <th class="text-left py-3 px-4">Pass Rate</th>
                            <th class="text-left py-3 px-4">Tests</th>
                        </tr>
                    </thead>
                    <tbody id="executionHistory">
                        <!-- Execution history will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Export Options -->
        <div class="bg-gray-800 p-6 rounded-lg mb-8">
            <h3 class="text-xl font-semibold mb-4 flex items-center">
                <i data-lucide="download" class="w-5 h-5 mr-2"></i>
                Export Report
            </h3>
            <div class="flex flex-wrap gap-4">
                <button onclick="exportReport('json')" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors flex items-center">
                    <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                    Export JSON
                </button>
                <button onclick="exportReport('csv')" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors flex items-center">
                    <i data-lucide="table" class="w-4 h-4 mr-2"></i>
                    Export CSV
                </button>
                <button onclick="printReport()" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors flex items-center">
                    <i data-lucide="printer" class="w-4 h-4 mr-2"></i>
                    Print Report
                </button>
                <button onclick="refreshReport()" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors flex items-center">
                    <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                    Refresh Data
                </button>
            </div>
        </div>
    </div>

    <!-- Error State -->
    <div id="errorState" class="max-w-7xl mx-auto px-8 text-center py-16 hidden">
        <div class="text-red-500 mb-4">
            <i data-lucide="alert-circle" class="w-16 h-16 mx-auto"></i>
        </div>
        <h2 class="text-2xl font-bold mb-4">Unable to Load Test Data</h2>
        <p class="text-gray-400 mb-6">Could not connect to the NovaFuse Test API Server.</p>
        <div class="space-y-2 text-sm text-gray-500">
            <p>• Make sure the test API server is running on port 3100</p>
            <p>• Run: <code class="bg-gray-800 px-2 py-1 rounded">node start-test-server.js</code></p>
            <p>• Check the console for connection errors</p>
        </div>
        <button onclick="refreshReport()" class="mt-6 px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
            Try Again
        </button>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // API Configuration
        const API_BASE_URL = 'http://localhost:3100/api';
        let reportData = null;
        let trendChart = null;
        let categoryChart = null;

        // Load report data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadReportData();
        });

        // Load real data from API
        async function loadReportData() {
            try {
                showLoadingState();
                
                // Get analytics data
                const analyticsResponse = await fetch(`${API_BASE_URL}/tests/analytics?days=30`);
                const analyticsData = await analyticsResponse.json();
                
                // Get recent execution history
                const historyResponse = await fetch(`${API_BASE_URL}/tests/results?limit=20`);
                const historyData = await historyResponse.json();
                
                if (analyticsData.success && historyData.success) {
                    reportData = {
                        analytics: analyticsData.data,
                        history: historyData.data.history || historyData.data,
                        generatedAt: new Date().toISOString()
                    };
                    
                    updateReportWithRealData();
                    showReportContent();
                } else {
                    throw new Error('API returned error response');
                }
                
            } catch (error) {
                console.error('Error loading report data:', error);
                showErrorState();
            }
        }

        // Update report with real data
        function updateReportWithRealData() {
            const { analytics, history, generatedAt } = reportData;
            
            // Update timestamp
            document.getElementById('reportTimestamp').textContent = new Date(generatedAt).toLocaleString();
            document.getElementById('dataSource').textContent = analytics.source === 'database' ? 'Database' : 'Memory Cache';
            
            // Update summary metrics
            document.getElementById('totalExecutions').textContent = analytics.summary.total_executions || 0;
            document.getElementById('avgPassRate').textContent = (analytics.summary.avg_pass_rate || 0).toFixed(1) + '%';
            document.getElementById('totalTests').textContent = analytics.summary.total_tests || 0;
            
            const avgDuration = analytics.summary.avg_duration || 0;
            document.getElementById('avgDuration').textContent = avgDuration > 1000 ? 
                (avgDuration / 1000).toFixed(1) + 's' : avgDuration.toFixed(0) + 'ms';
            
            // Update computed metrics
            if (analytics.computed) {
                const testsPerDay = (analytics.computed.totalTestsRun / analytics.period.days).toFixed(1);
                document.getElementById('testsPerDay').textContent = testsPerDay + ' per day avg';
                
                // Update trend indicator
                const trendDirection = analytics.computed.trendDirection;
                const trendIcon = document.getElementById('passRateTrend');
                const trendText = document.getElementById('passRateTrendText');
                
                trendIcon.className = 'w-4 h-4 mr-1';
                if (trendDirection === 'improving') {
                    trendIcon.classList.add('trend-up');
                    trendIcon.setAttribute('data-lucide', 'trending-up');
                    trendText.textContent = 'Improving';
                } else if (trendDirection === 'declining') {
                    trendIcon.classList.add('trend-down');
                    trendIcon.setAttribute('data-lucide', 'trending-down');
                    trendText.textContent = 'Declining';
                } else {
                    trendIcon.classList.add('trend-stable');
                    trendIcon.setAttribute('data-lucide', 'minus');
                    trendText.textContent = 'Stable';
                }
            }
            
            // Update category breakdown
            updateCategoryBreakdown(analytics.categoryBreakdown || []);
            
            // Update execution history
            updateExecutionHistory(Array.isArray(history) ? history : []);
            
            // Update charts
            updateCharts(analytics);
            
            // Refresh icons
            lucide.createIcons();
        }

        // Update category breakdown
        function updateCategoryBreakdown(categories) {
            const container = document.getElementById('categoryBreakdown');
            container.innerHTML = '';
            
            categories.forEach(category => {
                const passRate = category.avg_pass_rate || 0;
                const statusClass = passRate >= 90 ? 'text-green-400' : passRate >= 70 ? 'text-yellow-400' : 'text-red-400';
                
                const categoryCard = document.createElement('div');
                categoryCard.className = 'bg-gray-700 p-4 rounded-lg';
                categoryCard.innerHTML = `
                    <h4 class="font-semibold mb-2">${category.category_name || category.category_key}</h4>
                    <div class="text-2xl font-bold ${statusClass} mb-1">${passRate.toFixed(1)}%</div>
                    <div class="text-sm text-gray-400">
                        ${category.executions} executions<br>
                        Avg duration: ${(category.avg_duration || 0).toFixed(0)}ms
                    </div>
                `;
                container.appendChild(categoryCard);
            });
        }

        // Update execution history table
        function updateExecutionHistory(executions) {
            const tbody = document.getElementById('executionHistory');
            tbody.innerHTML = '';
            
            executions.slice(0, 10).forEach(execution => {
                const passRate = execution.pass_rate || 0;
                const statusClass = execution.status === 'completed' ? 'text-green-400' : 
                                  execution.status === 'failed' ? 'text-red-400' : 'text-yellow-400';
                
                const row = document.createElement('tr');
                row.className = 'border-b border-gray-700 hover:bg-gray-750';
                row.innerHTML = `
                    <td class="py-3 px-4 font-mono text-xs">${execution.id.substring(0, 8)}...</td>
                    <td class="py-3 px-4">${new Date(execution.start_time).toLocaleString()}</td>
                    <td class="py-3 px-4">${execution.duration ? (execution.duration / 1000).toFixed(1) + 's' : '-'}</td>
                    <td class="py-3 px-4"><span class="${statusClass}">${execution.status.toUpperCase()}</span></td>
                    <td class="py-3 px-4">${passRate.toFixed(1)}%</td>
                    <td class="py-3 px-4">${execution.total_tests || 0}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // Update charts
        function updateCharts(analytics) {
            // Trend chart
            if (analytics.trends && analytics.trends.length > 0) {
                updateTrendChart(analytics.trends);
            }
            
            // Category chart
            if (analytics.categoryBreakdown && analytics.categoryBreakdown.length > 0) {
                updateCategoryChart(analytics.categoryBreakdown);
            }
        }

        // Update trend chart
        function updateTrendChart(trends) {
            const ctx = document.getElementById('trendChart').getContext('2d');
            
            if (trendChart) {
                trendChart.destroy();
            }
            
            const labels = trends.slice(0, 14).reverse().map(t => new Date(t.date).toLocaleDateString());
            const data = trends.slice(0, 14).reverse().map(t => t.avg_pass_rate || 0);
            
            trendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Pass Rate %',
                        data: data,
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: { color: '#ffffff' }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: '#9ca3af' },
                            grid: { color: '#374151' }
                        },
                        y: {
                            ticks: { color: '#9ca3af' },
                            grid: { color: '#374151' },
                            min: 0,
                            max: 100
                        }
                    }
                }
            });
        }

        // Update category chart
        function updateCategoryChart(categories) {
            const ctx = document.getElementById('categoryChart').getContext('2d');
            
            if (categoryChart) {
                categoryChart.destroy();
            }
            
            const labels = categories.map(c => c.category_name || c.category_key);
            const data = categories.map(c => c.avg_pass_rate || 0);
            
            categoryChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#10b981', '#3b82f6', '#8b5cf6', '#f59e0b',
                            '#ef4444', '#06b6d4', '#84cc16', '#f97316'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }

        // UI State Management
        function showLoadingState() {
            document.getElementById('loadingState').classList.remove('hidden');
            document.getElementById('reportContent').classList.add('hidden');
            document.getElementById('errorState').classList.add('hidden');
        }

        function showReportContent() {
            document.getElementById('loadingState').classList.add('hidden');
            document.getElementById('reportContent').classList.remove('hidden');
            document.getElementById('errorState').classList.add('hidden');
        }

        function showErrorState() {
            document.getElementById('loadingState').classList.add('hidden');
            document.getElementById('reportContent').classList.add('hidden');
            document.getElementById('errorState').classList.remove('hidden');
        }

        // Export functions
        function exportReport(format) {
            if (!reportData) {
                alert('No data to export');
                return;
            }
            
            if (format === 'json') {
                const dataStr = JSON.stringify(reportData, null, 2);
                downloadFile(dataStr, `novafuse-test-report-${new Date().toISOString().split('T')[0]}.json`, 'application/json');
            } else if (format === 'csv') {
                const csv = convertToCSV(reportData.history);
                downloadFile(csv, `novafuse-test-history-${new Date().toISOString().split('T')[0]}.csv`, 'text/csv');
            }
        }

        function convertToCSV(data) {
            if (!data || data.length === 0) return '';
            
            const headers = ['ID', 'Start Time', 'Duration (ms)', 'Status', 'Pass Rate', 'Total Tests', 'Passed', 'Failed'];
            const rows = data.map(item => [
                item.id,
                item.start_time,
                item.duration || 0,
                item.status,
                item.pass_rate || 0,
                item.total_tests || 0,
                item.passed_tests || 0,
                item.failed_tests || 0
            ]);
            
            return [headers, ...rows].map(row => row.join(',')).join('\n');
        }

        function downloadFile(content, filename, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.click();
            URL.revokeObjectURL(url);
        }

        function printReport() {
            window.print();
        }

        function refreshReport() {
            loadReportData();
        }
    </script>
</body>
</html>
