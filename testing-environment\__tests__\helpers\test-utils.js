/**
 * Test Utilities for NovaConnect
 *
 * This module provides helper functions for writing precise, targeted tests
 * to help achieve the 96% coverage threshold.
 */

const axios = require('axios');
const MockAdapter = require('axios-mock-adapter');

/**
 * Creates a mock for axios with predefined responses
 * @param {Object} mockResponses - Key-value pairs of URL patterns and their responses
 * @returns {MockAdapter} The configured mock adapter
 */
function createAxiosMock(mockResponses = {}) {
  // Create a new mock adapter
  const mock = new MockAdapter(axios);

  // If mockResponses is empty, just return the mock
  if (!mockResponses || Object.keys(mockResponses).length === 0) {
    return mock;
  }

  // Configure mock responses
  Object.entries(mockResponses).forEach(([urlPattern, response]) => {
    // Default values for response properties
    const {
      method = 'get',
      url = null,
      status = 200,
      data = {},
      headers = {}
    } = response || {};

    // Use url if provided, otherwise use urlPattern
    const matchUrl = url || urlPattern;

    // Handle different HTTP methods
    const methodLower = method.toLowerCase();
    switch (methodLower) {
      case 'get':
        mock.onGet(new RegExp(matchUrl)).reply(status, data, headers);
        break;
      case 'post':
        mock.onPost(new RegExp(matchUrl)).reply(status, data, headers);
        break;
      case 'put':
        mock.onPut(new RegExp(matchUrl)).reply(status, data, headers);
        break;
      case 'delete':
        mock.onDelete(new RegExp(matchUrl)).reply(status, data, headers);
        break;
      case 'patch':
        mock.onPatch(new RegExp(matchUrl)).reply(status, data, headers);
        break;
      default:
        // For unknown methods, use onAny
        mock.onAny(new RegExp(matchUrl)).reply(status, data, headers);
        break;
    }
  });

  return mock;
}

/**
 * Measures execution time of a function
 * @param {Function} fn - The function to measure
 * @returns {Object} Object containing the result and duration in milliseconds
 */
async function measureExecutionTime(fn) {
  const start = process.hrtime.bigint();
  const result = await fn();
  const end = process.hrtime.bigint();
  const duration = Number(end - start) / 1_000_000; // Convert to milliseconds

  return { result, duration };
}

/**
 * Generates test data for connectors
 * @param {Object} overrides - Properties to override in the default test connector
 * @returns {Object} A test connector object
 */
function generateTestConnector(overrides = {}) {
  const defaultConnector = {
    metadata: {
      name: 'Test Connector',
      version: '1.0.0',
      category: 'Test',
      description: 'Test connector for coverage testing',
      author: 'NovaGRC',
      tags: ['test', 'coverage']
    },
    authentication: {
      type: 'API_KEY',
      fields: {
        apiKey: {
          type: 'string',
          description: 'API Key',
          required: true,
          sensitive: true
        }
      },
      testConnection: {
        endpoint: '/health',
        method: 'GET',
        expectedResponse: {
          status: 200
        }
      }
    },
    configuration: {
      baseUrl: 'http://localhost:3005',
      headers: {},
      timeout: 30000,
      retryPolicy: {
        maxRetries: 3,
        backoffStrategy: 'exponential'
      }
    },
    endpoints: [
      {
        id: 'getFindings',
        name: 'Get Findings',
        path: '/findings',
        method: 'GET',
        parameters: {
          query: {},
          path: {},
          body: {}
        },
        response: {
          successCode: 200
        }
      }
    ],
    mappings: [
      {
        sourceEndpoint: 'getFindings',
        targetSystem: 'NovaGRC',
        targetEntity: 'ComplianceFindings',
        transformations: [
          {
            source: '$.Findings[0].Id',
            target: 'findingId',
            transform: 'identity'
          }
        ]
      }
    ],
    events: {
      webhooks: [],
      polling: []
    }
  };

  // Deep merge the overrides
  return deepMerge(defaultConnector, overrides);
}

/**
 * Generates test data for credentials
 * @param {Object} overrides - Properties to override in the default test credential
 * @returns {Object} A test credential object
 */
function generateTestCredential(overrides = {}) {
  const defaultCredential = {
    name: 'Test Credential',
    connectorId: 'test-connector-id',
    authType: 'API_KEY',
    credentials: {
      apiKey: 'test-api-key',
      header: 'X-API-Key'
    },
    userId: 'test-user'
  };

  return { ...defaultCredential, ...overrides };
}

/**
 * Deep merges two objects
 * @param {Object} target - The target object
 * @param {Object} source - The source object
 * @returns {Object} The merged object
 */
function deepMerge(target, source) {
  // Handle non-object target
  if (!isObject(target)) {
    return source && isObject(source) ? { ...source } : {};
  }

  // Handle non-object source
  if (!isObject(source)) {
    return { ...target };
  }

  const output = { ...target };

  Object.keys(source).forEach(key => {
    if (isObject(source[key])) {
      if (!(key in target)) {
        output[key] = source[key];
      } else {
        output[key] = deepMerge(target[key], source[key]);
      }
    } else {
      output[key] = source[key];
    }
  });

  return output;
}

/**
 * Checks if a value is an object
 * @param {*} item - The value to check
 * @returns {boolean} True if the value is an object
 */
function isObject(item) {
  return (item !== null && typeof item === 'object' && !Array.isArray(item));
}

module.exports = {
  createAxiosMock,
  measureExecutionTime,
  generateTestConnector,
  generateTestCredential,
  deepMerge,
  isObject
};
