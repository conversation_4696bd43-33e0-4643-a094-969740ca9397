import React, { useEffect, useRef } from 'react';
import analyticsService, { 
  INTERACTION_TYPES, 
  VISUALIZATION_TYPES 
} from '../../../services/visualizationAnalyticsService';

/**
 * Higher-Order Component (HOC) that adds analytics tracking to visualizations
 * @param {React.Component} WrappedComponent - The component to wrap
 * @param {Object} options - Additional options for the HOC
 * @returns {React.Component} - The wrapped component with analytics tracking
 */
const withAnalyticsTracking = (WrappedComponent, options = {}) => {
  // Return a new component
  return function WithAnalyticsTracking(props) {
    // Reference to the wrapped component
    const componentRef = useRef(null);
    
    // Get visualization type from options or props
    const visualizationType = options.visualizationType || props.visualizationType;
    
    // Track view when component mounts
    useEffect(() => {
      // Skip tracking if visualization type is not provided
      if (!visualizationType) {
        console.warn('Visualization type not provided for analytics tracking');
        return;
      }
      
      // Track view event
      analyticsService.trackView(visualizationType, {
        width: props.width,
        height: props.height,
        options: props.options
      });
      
      // Update view duration when component unmounts
      return () => {
        analyticsService.updateViewDuration(visualizationType);
      };
    }, [visualizationType]);
    
    // Track errors
    useEffect(() => {
      if (props.error && visualizationType) {
        analyticsService.trackError(visualizationType, props.error.message, {
          errorType: props.error.name,
          errorStack: props.error.stack
        });
      }
    }, [props.error, visualizationType]);
    
    // Create event handlers for tracking interactions
    const createInteractionHandler = (interactionType, originalHandler) => {
      return (event, ...args) => {
        // Skip tracking if visualization type is not provided
        if (!visualizationType) {
          return originalHandler && originalHandler(event, ...args);
        }
        
        // Track interaction event
        analyticsService.trackInteraction(interactionType, visualizationType, {
          target: event.target.nodeName,
          elementId: event.target.id,
          elementClass: event.target.className,
          interactionData: args[0] || {}
        });
        
        // Call original handler if provided
        return originalHandler && originalHandler(event, ...args);
      };
    };
    
    // Create event handlers for common interactions
    const eventHandlers = {
      onClick: createInteractionHandler(INTERACTION_TYPES.CLICK, props.onClick),
      onMouseOver: createInteractionHandler(INTERACTION_TYPES.HOVER, props.onMouseOver),
      onZoom: createInteractionHandler(INTERACTION_TYPES.ZOOM, props.onZoom),
      onPan: createInteractionHandler(INTERACTION_TYPES.PAN, props.onPan),
      onRotate: createInteractionHandler(INTERACTION_TYPES.ROTATE, props.onRotate),
      onDrag: createInteractionHandler(INTERACTION_TYPES.DRAG, props.onDrag),
      onSelect: createInteractionHandler(INTERACTION_TYPES.SELECT, props.onSelect)
    };
    
    // Track filter changes
    useEffect(() => {
      // Skip if no filters or visualization type
      if (!props.filters || !visualizationType) {
        return;
      }
      
      // Track filter event
      analyticsService.trackFilter(visualizationType, props.filters);
    }, [props.filters, visualizationType]);
    
    // Track fullscreen changes
    useEffect(() => {
      // Skip if fullscreen is not defined or visualization type is not provided
      if (props.fullscreen === undefined || !visualizationType) {
        return;
      }
      
      // Track fullscreen event
      analyticsService.trackFullscreen(visualizationType, props.fullscreen);
    }, [props.fullscreen, visualizationType]);
    
    // Create handler for export
    const handleExport = (format, details) => {
      // Skip if visualization type is not provided
      if (!visualizationType) {
        return;
      }
      
      // Track export event
      analyticsService.trackExport(visualizationType, format, details);
    };
    
    // Create handler for feedback
    const handleFeedback = (feedback) => {
      // Skip if visualization type is not provided
      if (!visualizationType) {
        return;
      }
      
      // Track feedback event
      analyticsService.trackFeedback(visualizationType, feedback);
    };
    
    // Add analytics handlers to props
    const analyticsProps = {
      ...props,
      ...eventHandlers,
      onExport: handleExport,
      onFeedback: handleFeedback,
      ref: componentRef
    };
    
    // Render the wrapped component with analytics props
    return <WrappedComponent {...analyticsProps} />;
  };
};

// Export HOC with visualization types
export { VISUALIZATION_TYPES };
export default withAnalyticsTracking;
