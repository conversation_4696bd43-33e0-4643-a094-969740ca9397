/**
 * Comphyology Real-Time Dashboard Example
 * 
 * This example demonstrates the Comphyology Real-Time Dashboard with simulated data.
 */

const fs = require('fs');
const path = require('path');
const { novaVision } = require('../../novavision');
const ComphyologyRealTimeDashboard = require('../real_time_dashboard');

// Create output directory
const OUTPUT_DIR = path.join(__dirname, '../../../comphyology_real_time');
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * NovaShield Mock
 */
class NovaShieldMock extends require('events').EventEmitter {
  constructor() {
    super();
    this.threats = [];
    this.metrics = {
      averageEntropy: 0.5,
      averagePhase: Math.PI,
      averageCertainty: 0.7
    };
  }
  
  async getThreatData() {
    return {
      threats: this.threats,
      ...this.metrics
    };
  }
  
  generateRandomThreat() {
    const threatTypes = ['Malware', 'Phishing', 'DDoS', 'Intrusion', 'Data Exfiltration'];
    const threat = {
      id: `threat-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      type: threatTypes[Math.floor(Math.random() * threatTypes.length)],
      entropy: Math.random(),
      phase: Math.random() * Math.PI * 2,
      certainty: Math.random(),
      direction: Math.random() * Math.PI * 2,
      magnitude: Math.random(),
      timestamp: new Date()
    };
    
    this.threats.push(threat);
    
    // Limit to 20 threats
    if (this.threats.length > 20) {
      this.threats.shift();
    }
    
    // Update metrics
    this.metrics.averageEntropy = this.threats.reduce((sum, t) => sum + t.entropy, 0) / this.threats.length;
    this.metrics.averagePhase = this.threats.reduce((sum, t) => sum + t.phase, 0) / this.threats.length;
    this.metrics.averageCertainty = this.threats.reduce((sum, t) => sum + t.certainty, 0) / this.threats.length;
    
    // Emit event
    this.emit('threatDetected', threat);
    this.emit('threatAnalyzed', {
      threats: this.threats,
      ...this.metrics
    });
    
    return threat;
  }
}

/**
 * NovaTrack Mock
 */
class NovaTrackMock extends require('events').EventEmitter {
  constructor() {
    super();
    this.regulations = [];
    this.metrics = {
      averageComplexity: 0.5,
      averageAdaptability: 0.5,
      averageResonance: 0.7
    };
  }
  
  async getComplianceData() {
    return {
      regulations: this.regulations,
      ...this.metrics
    };
  }
  
  generateRandomRegulation() {
    const regulationTypes = ['GDPR', 'HIPAA', 'PCI-DSS', 'SOX', 'NIST CSF'];
    const regulation = {
      id: `regulation-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      name: regulationTypes[Math.floor(Math.random() * regulationTypes.length)],
      complexity: Math.random(),
      adaptability: Math.random(),
      resonance: Math.random(),
      environmentalPressure: Math.random(),
      timestamp: new Date()
    };
    
    this.regulations.push(regulation);
    
    // Limit to 20 regulations
    if (this.regulations.length > 20) {
      this.regulations.shift();
    }
    
    // Update metrics
    this.metrics.averageComplexity = this.regulations.reduce((sum, r) => sum + r.complexity, 0) / this.regulations.length;
    this.metrics.averageAdaptability = this.regulations.reduce((sum, r) => sum + r.adaptability, 0) / this.regulations.length;
    this.metrics.averageResonance = this.regulations.reduce((sum, r) => sum + r.resonance, 0) / this.regulations.length;
    
    // Emit event
    this.emit('complianceChanged', regulation);
    this.emit('regulationUpdated', {
      regulations: this.regulations,
      ...this.metrics
    });
    
    return regulation;
  }
}

/**
 * NovaCore Mock
 */
class NovaCoreMock extends require('events').EventEmitter {
  constructor() {
    super();
    this.decisions = [];
    this.metrics = {
      averageFairness: 0.5,
      averageTransparency: 0.5,
      averageEthicalTensor: 0.7
    };
  }
  
  async getDecisionData() {
    return {
      decisions: this.decisions,
      ...this.metrics
    };
  }
  
  generateRandomDecision() {
    const decisionTypes = ['Block', 'Allow', 'Quarantine', 'Alert', 'Escalate'];
    const decision = {
      id: `decision-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      type: decisionTypes[Math.floor(Math.random() * decisionTypes.length)],
      fairness: Math.random(),
      transparency: Math.random(),
      ethicalTensor: Math.random(),
      accountability: Math.random(),
      timestamp: new Date()
    };
    
    this.decisions.push(decision);
    
    // Limit to 20 decisions
    if (this.decisions.length > 20) {
      this.decisions.shift();
    }
    
    // Update metrics
    this.metrics.averageFairness = this.decisions.reduce((sum, d) => sum + d.fairness, 0) / this.decisions.length;
    this.metrics.averageTransparency = this.decisions.reduce((sum, d) => sum + d.transparency, 0) / this.decisions.length;
    this.metrics.averageEthicalTensor = this.decisions.reduce((sum, d) => sum + d.ethicalTensor, 0) / this.decisions.length;
    
    // Emit event
    this.emit('decisionMade', decision);
    this.emit('policyApplied', {
      decisions: this.decisions,
      ...this.metrics
    });
    
    return decision;
  }
}

/**
 * Run the example
 */
async function runExample() {
  console.log('Comphyology Real-Time Dashboard Example');
  
  // Create mock components
  const novaShield = new NovaShieldMock();
  const novaTrack = new NovaTrackMock();
  const novaCore = new NovaCoreMock();
  
  // Create dashboard
  const dashboard = new ComphyologyRealTimeDashboard({
    novaVision,
    novaShield,
    novaTrack,
    novaCore,
    enableLogging: true,
    updateInterval: 1000
  });
  
  // Wait for initial schemas to be generated
  console.log('Generating initial schemas...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Get dashboard schema
  const dashboardSchema = dashboard.getDashboardSchema();
  
  // Save dashboard schema to file
  const schemaFile = path.join(OUTPUT_DIR, 'real_time_dashboard_schema.json');
  fs.writeFileSync(schemaFile, JSON.stringify(dashboardSchema, null, 2));
  console.log(`Dashboard schema saved to: ${schemaFile}`);
  
  // Start real-time updates
  console.log('Starting real-time updates...');
  await dashboard.start();
  
  // Generate random data
  console.log('Generating random data...');
  const dataGenerationInterval = setInterval(() => {
    novaShield.generateRandomThreat();
    novaTrack.generateRandomRegulation();
    novaCore.generateRandomDecision();
  }, 2000);
  
  // Generate HTML example
  console.log('Generating HTML example...');
  const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comphyology Real-Time Dashboard</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    h1 {
      color: #333;
      text-align: center;
    }
    
    .dashboard {
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-top: 20px;
    }
    
    pre {
      background-color: #f9f9f9;
      border: 1px solid #ddd;
      border-radius: 3px;
      padding: 10px;
      overflow: auto;
      max-height: 500px;
    }
    
    .note {
      background-color: #fffde7;
      border-left: 4px solid #ffd600;
      padding: 10px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Comphyology Real-Time Dashboard</h1>
    
    <div class="note">
      <p><strong>Note:</strong> This is a static representation of the real-time dashboard. In a real implementation, this would be a dynamic dashboard that updates as new data arrives.</p>
    </div>
    
    <div class="dashboard">
      <h2>Dashboard Schema</h2>
      <pre>${JSON.stringify(dashboardSchema, null, 2)}</pre>
    </div>
  </div>
</body>
</html>
  `;
  
  const htmlFile = path.join(OUTPUT_DIR, 'real_time_dashboard_example.html');
  fs.writeFileSync(htmlFile, htmlContent);
  console.log(`HTML example saved to: ${htmlFile}`);
  
  // Run for 10 seconds
  console.log('Running for 10 seconds...');
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  // Stop data generation
  clearInterval(dataGenerationInterval);
  
  // Stop real-time updates
  console.log('Stopping real-time updates...');
  await dashboard.stop();
  
  console.log('Example completed successfully!');
  console.log(`Output directory: ${OUTPUT_DIR}`);
  console.log(`\nTo view the HTML example, open: ${htmlFile}`);
}

// Run the example
runExample().catch(error => {
  console.error('Example failed:', error);
});
