/**
 * NovaCore Regulatory Change Service
 * 
 * This service provides functionality for managing regulatory changes.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const { RegulatoryChange, Regulation } = require('../models');
const logger = require('../../../config/logger');
const { ValidationError, NotFoundError } = require('../../../api/utils/errors');
const { v4: uuidv4 } = require('uuid');

class RegulatoryChangeService {
  /**
   * Create a new regulatory change
   * @param {Object} data - Regulatory change data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created regulatory change
   */
  async createRegulatoryChange(data, userId) {
    try {
      logger.info('Creating new regulatory change', { 
        regulationId: data.regulationId, 
        title: data.title 
      });
      
      // Validate regulation exists
      const regulation = await Regulation.findById(data.regulationId);
      
      if (!regulation) {
        throw new ValidationError(`Regulation with ID ${data.regulationId} not found`);
      }
      
      // Set regulation name if not provided
      if (!data.regulationName) {
        data.regulationName = regulation.name;
      }
      
      // Set created by and updated by
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Create regulatory change
      const regulatoryChange = new RegulatoryChange(data);
      await regulatoryChange.save();
      
      logger.info('Regulatory change created successfully', { id: regulatoryChange._id });
      
      return regulatoryChange;
    } catch (error) {
      logger.error('Error creating regulatory change', { error });
      throw error;
    }
  }
  
  /**
   * Get all regulatory changes
   * @param {string} organizationId - Organization ID
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Regulatory changes with pagination info
   */
  async getAllRegulatoryChanges(organizationId, filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { publicationDate: -1 } } = options;
      
      // Build query
      const query = { organizationId };
      
      // Apply filters
      if (filter.regulationId) {
        query.regulationId = filter.regulationId;
      }
      
      if (filter.type) {
        query.type = filter.type;
      }
      
      if (filter.category) {
        query.category = filter.category;
      }
      
      if (filter.status) {
        query.status = filter.status;
      }
      
      if (filter.priority) {
        query.priority = filter.priority;
      }
      
      if (filter.country) {
        query['$or'] = [
          { 'jurisdiction.country': filter.country },
          { 'jurisdiction.isGlobal': true }
        ];
      }
      
      if (filter.region) {
        query['$or'] = [
          { 'jurisdiction.region': filter.region },
          { 'jurisdiction.isGlobal': true }
        ];
      }
      
      if (filter.industry) {
        query['applicability.industries'] = filter.industry;
      }
      
      if (filter.organizationType) {
        query['applicability.organizationTypes'] = filter.organizationType;
      }
      
      if (filter.dataType) {
        query['applicability.dataTypes'] = filter.dataType;
      }
      
      if (filter.implementationStatus) {
        query['implementationStatus.status'] = filter.implementationStatus;
      }
      
      if (filter.effectiveDateFrom) {
        query.effectiveDate = { $gte: new Date(filter.effectiveDateFrom) };
      }
      
      if (filter.effectiveDateTo) {
        if (query.effectiveDate) {
          query.effectiveDate.$lte = new Date(filter.effectiveDateTo);
        } else {
          query.effectiveDate = { $lte: new Date(filter.effectiveDateTo) };
        }
      }
      
      if (filter.complianceDeadlineFrom) {
        query.complianceDeadline = { $gte: new Date(filter.complianceDeadlineFrom) };
      }
      
      if (filter.complianceDeadlineTo) {
        if (query.complianceDeadline) {
          query.complianceDeadline.$lte = new Date(filter.complianceDeadlineTo);
        } else {
          query.complianceDeadline = { $lte: new Date(filter.complianceDeadlineTo) };
        }
      }
      
      if (filter.search) {
        query['$or'] = [
          { title: { $regex: filter.search, $options: 'i' } },
          { description: { $regex: filter.search, $options: 'i' } },
          { summary: { $regex: filter.search, $options: 'i' } },
          { regulationName: { $regex: filter.search, $options: 'i' } }
        ];
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [regulatoryChanges, total] = await Promise.all([
        RegulatoryChange.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        RegulatoryChange.countDocuments(query)
      ]);
      
      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;
      
      return {
        data: regulatoryChanges,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting regulatory changes', { organizationId, error });
      throw error;
    }
  }
  
  /**
   * Get regulatory change by ID
   * @param {string} id - Regulatory change ID
   * @returns {Promise<Object>} - Regulatory change
   */
  async getRegulatoryChangeById(id) {
    try {
      const regulatoryChange = await RegulatoryChange.findById(id);
      
      if (!regulatoryChange) {
        throw new NotFoundError(`Regulatory change with ID ${id} not found`);
      }
      
      return regulatoryChange;
    } catch (error) {
      logger.error('Error getting regulatory change by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Update regulatory change
   * @param {string} id - Regulatory change ID
   * @param {Object} data - Updated regulatory change data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated regulatory change
   */
  async updateRegulatoryChange(id, data, userId) {
    try {
      // Get existing regulatory change
      const regulatoryChange = await this.getRegulatoryChangeById(id);
      
      // Set updated by
      data.updatedBy = userId;
      
      // Update regulatory change
      Object.assign(regulatoryChange, data);
      await regulatoryChange.save();
      
      logger.info('Regulatory change updated successfully', { id });
      
      return regulatoryChange;
    } catch (error) {
      logger.error('Error updating regulatory change', { id, error });
      throw error;
    }
  }
  
  /**
   * Delete regulatory change
   * @param {string} id - Regulatory change ID
   * @returns {Promise<boolean>} - Deletion success
   */
  async deleteRegulatoryChange(id) {
    try {
      const result = await RegulatoryChange.findByIdAndDelete(id);
      
      if (!result) {
        throw new NotFoundError(`Regulatory change with ID ${id} not found`);
      }
      
      logger.info('Regulatory change deleted successfully', { id });
      
      return true;
    } catch (error) {
      logger.error('Error deleting regulatory change', { id, error });
      throw error;
    }
  }
  
  /**
   * Add impact assessment
   * @param {string} id - Regulatory change ID
   * @param {Object} assessment - Impact assessment data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated regulatory change
   */
  async addImpactAssessment(id, assessment, userId) {
    try {
      // Get existing regulatory change
      const regulatoryChange = await this.getRegulatoryChangeById(id);
      
      // Set assessment details
      assessment.assessedBy = userId;
      assessment.assessedAt = new Date();
      
      // Add impact assessment
      regulatoryChange.impactAssessment = assessment;
      
      // Update status
      regulatoryChange.status = 'assessed';
      
      // Set updated by
      regulatoryChange.updatedBy = userId;
      
      // Save regulatory change
      await regulatoryChange.save();
      
      logger.info('Impact assessment added successfully', { id });
      
      return regulatoryChange;
    } catch (error) {
      logger.error('Error adding impact assessment', { id, error });
      throw error;
    }
  }
  
  /**
   * Update implementation status
   * @param {string} id - Regulatory change ID
   * @param {Object} status - Implementation status data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated regulatory change
   */
  async updateImplementationStatus(id, status, userId) {
    try {
      // Get existing regulatory change
      const regulatoryChange = await this.getRegulatoryChangeById(id);
      
      // Update implementation status
      regulatoryChange.implementationStatus = {
        ...regulatoryChange.implementationStatus,
        ...status
      };
      
      // Update status based on implementation status
      switch (status.status) {
        case 'not_started':
          regulatoryChange.status = 'assessed';
          break;
        case 'in_progress':
          regulatoryChange.status = 'implementation_planned';
          break;
        case 'completed':
          regulatoryChange.status = 'implemented';
          regulatoryChange.implementationStatus.completionDate = new Date();
          break;
        case 'verified':
          regulatoryChange.status = 'verified';
          regulatoryChange.implementationStatus.verificationDate = new Date();
          break;
      }
      
      // Set updated by
      regulatoryChange.updatedBy = userId;
      
      // Save regulatory change
      await regulatoryChange.save();
      
      logger.info('Implementation status updated successfully', { id });
      
      return regulatoryChange;
    } catch (error) {
      logger.error('Error updating implementation status', { id, error });
      throw error;
    }
  }
  
  /**
   * Add notification
   * @param {string} id - Regulatory change ID
   * @param {Object} notification - Notification data
   * @returns {Promise<Object>} - Updated regulatory change
   */
  async addNotification(id, notification) {
    try {
      // Get existing regulatory change
      const regulatoryChange = await this.getRegulatoryChangeById(id);
      
      // Set notification ID if not provided
      if (!notification.id) {
        notification.id = `notification-${uuidv4().substring(0, 8)}`;
      }
      
      // Add notification
      regulatoryChange.notifications.push(notification);
      
      // Save regulatory change
      await regulatoryChange.save();
      
      logger.info('Notification added successfully', { 
        id, 
        notificationId: notification.id 
      });
      
      return regulatoryChange;
    } catch (error) {
      logger.error('Error adding notification', { id, error });
      throw error;
    }
  }
  
  /**
   * Update notification status
   * @param {string} id - Regulatory change ID
   * @param {string} notificationId - Notification ID
   * @param {string} status - Notification status
   * @param {string} error - Error message (if status is 'failed')
   * @returns {Promise<Object>} - Updated regulatory change
   */
  async updateNotificationStatus(id, notificationId, status, error = null) {
    try {
      // Get existing regulatory change
      const regulatoryChange = await this.getRegulatoryChangeById(id);
      
      // Find notification
      const notificationIndex = regulatoryChange.notifications.findIndex(n => n.id === notificationId);
      
      if (notificationIndex === -1) {
        throw new NotFoundError(`Notification with ID ${notificationId} not found`);
      }
      
      // Update notification status
      regulatoryChange.notifications[notificationIndex].status = status;
      
      if (status === 'sent') {
        regulatoryChange.notifications[notificationIndex].sentAt = new Date();
      }
      
      if (status === 'failed' && error) {
        regulatoryChange.notifications[notificationIndex].error = error;
      }
      
      // Save regulatory change
      await regulatoryChange.save();
      
      logger.info('Notification status updated successfully', { 
        id, 
        notificationId, 
        status 
      });
      
      return regulatoryChange;
    } catch (error) {
      logger.error('Error updating notification status', { id, notificationId, error });
      throw error;
    }
  }
  
  /**
   * Add related workflow
   * @param {string} id - Regulatory change ID
   * @param {Object} workflow - Workflow data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated regulatory change
   */
  async addRelatedWorkflow(id, workflow, userId) {
    try {
      // Get existing regulatory change
      const regulatoryChange = await this.getRegulatoryChangeById(id);
      
      // Check if workflow already exists
      if (regulatoryChange.relatedWorkflows.some(w => w.workflowId.toString() === workflow.workflowId.toString())) {
        throw new ValidationError(`Workflow with ID ${workflow.workflowId} already exists`);
      }
      
      // Add related workflow
      regulatoryChange.relatedWorkflows.push(workflow);
      
      // Set updated by
      regulatoryChange.updatedBy = userId;
      
      // Save regulatory change
      await regulatoryChange.save();
      
      logger.info('Related workflow added successfully', { 
        id, 
        workflowId: workflow.workflowId 
      });
      
      return regulatoryChange;
    } catch (error) {
      logger.error('Error adding related workflow', { id, error });
      throw error;
    }
  }
  
  /**
   * Update related workflow status
   * @param {string} id - Regulatory change ID
   * @param {string} workflowId - Workflow ID
   * @param {string} status - Workflow status
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated regulatory change
   */
  async updateRelatedWorkflowStatus(id, workflowId, status, userId) {
    try {
      // Get existing regulatory change
      const regulatoryChange = await this.getRegulatoryChangeById(id);
      
      // Find workflow
      const workflowIndex = regulatoryChange.relatedWorkflows.findIndex(
        w => w.workflowId.toString() === workflowId.toString()
      );
      
      if (workflowIndex === -1) {
        throw new NotFoundError(`Workflow with ID ${workflowId} not found`);
      }
      
      // Update workflow status
      regulatoryChange.relatedWorkflows[workflowIndex].status = status;
      
      // Set updated by
      regulatoryChange.updatedBy = userId;
      
      // Save regulatory change
      await regulatoryChange.save();
      
      logger.info('Related workflow status updated successfully', { 
        id, 
        workflowId, 
        status 
      });
      
      return regulatoryChange;
    } catch (error) {
      logger.error('Error updating related workflow status', { id, workflowId, error });
      throw error;
    }
  }
  
  /**
   * Find pending regulatory changes
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Array>} - Regulatory changes
   */
  async findPending(organizationId) {
    try {
      return await RegulatoryChange.findPending(organizationId);
    } catch (error) {
      logger.error('Error finding pending regulatory changes', { organizationId, error });
      throw error;
    }
  }
  
  /**
   * Find overdue regulatory changes
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Array>} - Regulatory changes
   */
  async findOverdue(organizationId) {
    try {
      return await RegulatoryChange.findOverdue(organizationId);
    } catch (error) {
      logger.error('Error finding overdue regulatory changes', { organizationId, error });
      throw error;
    }
  }
  
  /**
   * Find upcoming regulatory changes
   * @param {string} organizationId - Organization ID
   * @param {number} days - Number of days to look ahead
   * @returns {Promise<Array>} - Regulatory changes
   */
  async findUpcoming(organizationId, days = 30) {
    try {
      return await RegulatoryChange.findUpcoming(organizationId, days);
    } catch (error) {
      logger.error('Error finding upcoming regulatory changes', { organizationId, days, error });
      throw error;
    }
  }
  
  /**
   * Find regulatory changes by impact level
   * @param {string} organizationId - Organization ID
   * @param {string} level - Impact level
   * @returns {Promise<Array>} - Regulatory changes
   */
  async findByImpactLevel(organizationId, level) {
    try {
      return await RegulatoryChange.findByImpactLevel(organizationId, level);
    } catch (error) {
      logger.error('Error finding regulatory changes by impact level', { organizationId, level, error });
      throw error;
    }
  }
  
  /**
   * Find regulatory changes by applicability
   * @param {string} organizationId - Organization ID
   * @param {Object} criteria - Applicability criteria
   * @returns {Promise<Array>} - Regulatory changes
   */
  async findByApplicability(organizationId, criteria) {
    try {
      return await RegulatoryChange.findByApplicability(organizationId, criteria);
    } catch (error) {
      logger.error('Error finding regulatory changes by applicability', { organizationId, criteria, error });
      throw error;
    }
  }
}

module.exports = new RegulatoryChangeService();
