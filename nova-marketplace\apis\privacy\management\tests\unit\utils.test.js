/**
 * Utils Index Tests
 *
 * This file contains unit tests for the utils index module.
 */

describe('Utils Index', () => {
  it('should export all utility modules', () => {
    // Import the utils index module
    const utils = require('../../utils');

    // Verify that all expected utility modules are exported
    expect(utils.validators).toBeDefined();
    expect(utils.idGenerator).toBeDefined();
    expect(utils.dateUtils).toBeDefined();
    expect(utils.encryptionUtils).toBeDefined();
  });

  it('should export validators with all expected functions', () => {
    // Import the utils index module
    const utils = require('../../utils');

    // Verify that validators has all expected functions
    expect(typeof utils.validators.isValidEmail).toBe('function');
    expect(typeof utils.validators.isValidPhone).toBe('function');
    expect(typeof utils.validators.isValidDate).toBe('function');
    expect(typeof utils.validators.isValidDateRange).toBe('function');
    expect(typeof utils.validators.isValidUrl).toBe('function');
    expect(typeof utils.validators.isValidIp).toBe('function');
    expect(typeof utils.validators.isValidDsrType).toBe('function');
    expect(typeof utils.validators.isValidConsentType).toBe('function');
    expect(typeof utils.validators.isValidLegalBasis).toBe('function');
    expect(typeof utils.validators.isValidBreachSeverity).toBe('function');
    expect(typeof utils.validators.isValidNotificationPriority).toBe('function');
  });

  it('should export idGenerator with all expected functions', () => {
    // Import the utils index module
    const utils = require('../../utils');

    // Verify that idGenerator has all expected functions
    expect(typeof utils.idGenerator.generateRandomId).toBe('function');
    expect(typeof utils.idGenerator.generateDsrId).toBe('function');
    expect(typeof utils.idGenerator.generateConsentId).toBe('function');
    expect(typeof utils.idGenerator.generateDataBreachId).toBe('function');
    expect(typeof utils.idGenerator.generatePrivacyNoticeId).toBe('function');
    expect(typeof utils.idGenerator.generateNotificationId).toBe('function');
    expect(typeof utils.idGenerator.generateProcessingActivityId).toBe('function');
    expect(typeof utils.idGenerator.generateIntegrationId).toBe('function');
    expect(typeof utils.idGenerator.generateUuid).toBe('function');
  });

  it('should export dateUtils with all expected functions', () => {
    // Import the utils index module
    const utils = require('../../utils');

    // Verify that dateUtils has all expected functions
    expect(typeof utils.dateUtils.calculateDsrDueDate).toBe('function');
    expect(typeof utils.dateUtils.calculateConsentExpiryDate).toBe('function');
    expect(typeof utils.dateUtils.calculateBreachNotificationDeadline).toBe('function');
    expect(typeof utils.dateUtils.formatIsoDate).toBe('function');
    expect(typeof utils.dateUtils.formatHumanDate).toBe('function');
    expect(typeof utils.dateUtils.daysBetween).toBe('function');
    expect(typeof utils.dateUtils.isInPast).toBe('function');
    expect(typeof utils.dateUtils.isInFuture).toBe('function');
    expect(typeof utils.dateUtils.getPeriodDates).toBe('function');
  });

  it('should export encryptionUtils with all expected functions', () => {
    // Import the utils index module
    const utils = require('../../utils');

    // Verify that encryptionUtils has all expected functions
    expect(typeof utils.encryptionUtils.encrypt).toBe('function');
    expect(typeof utils.encryptionUtils.decrypt).toBe('function');
    expect(typeof utils.encryptionUtils.hashData).toBe('function');
    expect(typeof utils.encryptionUtils.generateKey).toBe('function');
  });
});
