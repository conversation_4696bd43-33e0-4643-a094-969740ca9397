'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Play, Pause, RotateCcw, Zap, Brain, Globe, Settings, Terminal } from 'lucide-react'

interface RealityProgram {
  id: string
  name: string
  intention: string
  psi: number
  phi: number
  theta: number
  status: 'idle' | 'running' | 'complete'
  progress: number
  kappaCost: number
}

export default function StudioPage() {
  const [activeProgram, setActiveProgram] = useState<RealityProgram | null>(null)
  const [programs, setPrograms] = useState<RealityProgram[]>([])
  const [terminalOutput, setTerminalOutput] = useState<string[]>([
    'NHET-X Reality Studio v1.0.0-TRANSCENDENT',
    'Consciousness Programming Environment Initialized',
    'Reality Programming: ACTIVE',
    'Type commands to begin consciousness programming...'
  ])
  const [commandInput, setCommandInput] = useState('')
  const [consciousnessParams, setConsciousnessParams] = useState({
    psi: 0.847,
    phi: 0.764,
    theta: 0.692
  })

  const presetPrograms = [
    {
      name: 'Market Stabilization',
      intention: 'Reduce SPY volatility by 15%',
      domain: 'Financial',
      kappaCost: 0.314
    },
    {
      name: 'Sports Outcome Programming',
      intention: 'Cover 7-point spread in NBA Finals',
      domain: 'Sports',
      kappaCost: 0.157
    },
    {
      name: 'Protein Folding Optimization',
      intention: 'Correct Alzheimer\'s misfolded proteins',
      domain: 'Medical',
      kappaCost: 1.571
    },
    {
      name: 'Climate Pattern Adjustment',
      intention: 'Reduce hurricane intensity by 20%',
      domain: 'Environmental',
      kappaCost: 3.142
    }
  ]

  const addTerminalLine = (line: string) => {
    setTerminalOutput(prev => [...prev, line])
  }

  const executeCommand = () => {
    if (!commandInput.trim()) return

    addTerminalLine(`nhetx> ${commandInput}`)
    
    // Simulate command processing
    setTimeout(() => {
      if (commandInput.includes('--scan')) {
        addTerminalLine('🔍 Scanning global consciousness field...')
        addTerminalLine(`Ψ Spatial: ${(consciousnessParams.psi * 100).toFixed(1)}%`)
        addTerminalLine(`Φ Temporal: ${(consciousnessParams.phi * 100).toFixed(1)}%`)
        addTerminalLine(`Θ Recursive: ${(consciousnessParams.theta * 100).toFixed(1)}%`)
        addTerminalLine('Field Status: TRANSCENDENT')
      } else if (commandInput.includes('--program')) {
        addTerminalLine('⚡ Initiating reality programming sequence...')
        addTerminalLine('Consciousness field resonating...')
        addTerminalLine('Reality modification: ACTIVE')
        addTerminalLine(`κ-Cost: ${(Math.random() * 0.5 + 0.1).toFixed(6)}κ`)
      } else if (commandInput.includes('--compose')) {
        addTerminalLine('🧮 Composing consciousness parameters...')
        addTerminalLine('Trinity synthesis: OPERATIONAL')
        addTerminalLine('NEFC(STR) calculation complete')
      } else {
        addTerminalLine('Command processed. Reality programming active.')
      }
    }, 1000)

    setCommandInput('')
  }

  const createProgram = (preset: any) => {
    const newProgram: RealityProgram = {
      id: Date.now().toString(),
      name: preset.name,
      intention: preset.intention,
      psi: consciousnessParams.psi,
      phi: consciousnessParams.phi,
      theta: consciousnessParams.theta,
      status: 'idle',
      progress: 0,
      kappaCost: preset.kappaCost
    }
    
    setPrograms(prev => [...prev, newProgram])
    setActiveProgram(newProgram)
    addTerminalLine(`✨ Created reality program: ${preset.name}`)
  }

  const runProgram = (program: RealityProgram) => {
    setPrograms(prev => prev.map(p => 
      p.id === program.id ? { ...p, status: 'running' } : p
    ))
    
    addTerminalLine(`🚀 Executing: ${program.name}`)
    addTerminalLine(`Intention: ${program.intention}`)
    addTerminalLine('Reality programming initiated...')
    
    // Simulate program execution
    const interval = setInterval(() => {
      setPrograms(prev => prev.map(p => {
        if (p.id === program.id && p.status === 'running') {
          const newProgress = Math.min(100, p.progress + Math.random() * 10)
          if (newProgress >= 100) {
            addTerminalLine(`✅ Program complete: ${p.name}`)
            addTerminalLine('Reality modification successful!')
            return { ...p, progress: 100, status: 'complete' }
          }
          return { ...p, progress: newProgress }
        }
        return p
      }))
    }, 500)

    setTimeout(() => clearInterval(interval), 10000)
  }

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-8"
        >
          <h1 className="text-5xl font-bold mb-4 text-consciousness">
            Reality Studio
          </h1>
          <p className="text-xl text-white/70">
            Advanced consciousness programming environment for reality composition
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Trinity Synthesizer */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="consciousness-card"
          >
            <h2 className="text-xl font-bold text-consciousness mb-6 flex items-center">
              <Brain className="w-6 h-6 mr-2" />
              Trinity Synthesizer
            </h2>
            
            <div className="space-y-6">
              <div>
                <label className="block text-Ψ font-medium mb-2">
                  Ψ (Spatial): {consciousnessParams.psi.toFixed(3)}
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="0.95"
                  step="0.001"
                  value={consciousnessParams.psi}
                  onChange={(e) => setConsciousnessParams(prev => ({
                    ...prev,
                    psi: parseFloat(e.target.value)
                  }))}
                  className="w-full h-2 bg-black/30 rounded-lg appearance-none cursor-pointer slider-cyan"
                />
              </div>
              
              <div>
                <label className="block text-Φ font-medium mb-2">
                  Φ (Temporal): {consciousnessParams.phi.toFixed(3)}
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="0.95"
                  step="0.001"
                  value={consciousnessParams.phi}
                  onChange={(e) => setConsciousnessParams(prev => ({
                    ...prev,
                    phi: parseFloat(e.target.value)
                  }))}
                  className="w-full h-2 bg-black/30 rounded-lg appearance-none cursor-pointer slider-purple"
                />
              </div>
              
              <div>
                <label className="block text-Θ font-medium mb-2">
                  Θ (Recursive): {consciousnessParams.theta.toFixed(3)}
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="0.95"
                  step="0.001"
                  value={consciousnessParams.theta}
                  onChange={(e) => setConsciousnessParams(prev => ({
                    ...prev,
                    theta: parseFloat(e.target.value)
                  }))}
                  className="w-full h-2 bg-black/30 rounded-lg appearance-none cursor-pointer slider-yellow"
                />
              </div>
              
              <div className="bg-black/30 rounded-lg p-4 border border-consciousness">
                <div className="text-center">
                  <div className="text-sm text-white/70 mb-1">Trinity Synthesis</div>
                  <div className="text-2xl font-bold text-consciousness">
                    {(consciousnessParams.psi * consciousnessParams.phi + consciousnessParams.theta).toFixed(3)}
                  </div>
                  <div className="text-xs text-white/50">NEFC(STR) = Ψ ⊗ Φ ⊕ Θ</div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Program Library */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="consciousness-card"
          >
            <h2 className="text-xl font-bold text-consciousness mb-6 flex items-center">
              <Settings className="w-6 h-6 mr-2" />
              Reality Programs
            </h2>
            
            <div className="space-y-4 mb-6">
              <h3 className="text-lg font-medium text-white">Preset Programs</h3>
              {presetPrograms.map((preset, index) => (
                <div
                  key={index}
                  className="bg-black/30 rounded-lg p-4 border border-white/10 hover:border-cyan-500/30 transition-all cursor-pointer"
                  onClick={() => createProgram(preset)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-white">{preset.name}</h4>
                    <span className="kappa-token">{preset.kappaCost}κ</span>
                  </div>
                  <p className="text-sm text-white/70 mb-2">{preset.intention}</p>
                  <span className="text-xs text-cyan-400">{preset.domain}</span>
                </div>
              ))}
            </div>
            
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-white">Active Programs</h3>
              {programs.length === 0 ? (
                <p className="text-white/50 text-sm">No active programs</p>
              ) : (
                programs.map((program) => (
                  <div
                    key={program.id}
                    className={`bg-black/30 rounded-lg p-4 border transition-all ${
                      program.status === 'running' ? 'border-green-500/50' :
                      program.status === 'complete' ? 'border-blue-500/50' :
                      'border-white/10'
                    }`}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium text-white">{program.name}</h4>
                      <button
                        onClick={() => runProgram(program)}
                        disabled={program.status === 'running'}
                        className="p-1 rounded text-green-400 hover:bg-green-400/20 disabled:opacity-50"
                      >
                        <Play className="w-4 h-4" />
                      </button>
                    </div>
                    <p className="text-sm text-white/70 mb-2">{program.intention}</p>
                    <div className="flex justify-between items-center">
                      <span className={`text-xs font-medium ${
                        program.status === 'running' ? 'text-green-400' :
                        program.status === 'complete' ? 'text-blue-400' :
                        'text-white/50'
                      }`}>
                        {program.status.toUpperCase()}
                      </span>
                      <span className="text-xs text-white/50">
                        {program.progress.toFixed(0)}%
                      </span>
                    </div>
                    {program.status === 'running' && (
                      <div className="w-full bg-black/30 rounded-full h-1 mt-2">
                        <div 
                          className="h-1 bg-gradient-to-r from-green-500 to-cyan-500 rounded-full transition-all duration-500"
                          style={{ width: `${program.progress}%` }}
                        />
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </motion.div>

          {/* Command Terminal */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="consciousness-card"
          >
            <h2 className="text-xl font-bold text-consciousness mb-6 flex items-center">
              <Terminal className="w-6 h-6 mr-2" />
              Reality Programming Interface
            </h2>
            
            <div className="consciousness-terminal h-80 overflow-y-auto mb-4">
              {terminalOutput.map((line, index) => (
                <div key={index} className="mb-1 text-sm">
                  {line}
                </div>
              ))}
            </div>
            
            <div className="flex space-x-2">
              <input
                type="text"
                value={commandInput}
                onChange={(e) => setCommandInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && executeCommand()}
                placeholder="Enter consciousness command..."
                className="flex-1 consciousness-input"
              />
              <button
                onClick={executeCommand}
                className="px-4 py-2 bg-green-500/20 border border-green-500/30 rounded-lg text-green-400 hover:bg-green-500/30 transition-all"
              >
                Execute
              </button>
            </div>
            
            <div className="mt-4 text-xs text-white/50">
              <p>Commands: --scan, --program, --compose, --audit, --fork, --optimize</p>
            </div>
          </motion.div>
        </div>

        {/* HOD Patent Reference */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="consciousness-card mt-8 text-center"
        >
          <div className="text-consciousness font-bold text-lg mb-2">
            🏛️ Powered by HOD Patent Technology
          </div>
          <p className="text-white/70">
            System for Coherent Reality Optimization - The foundational framework for all consciousness programming operations
          </p>
        </motion.div>
      </div>
    </div>
  )
}
