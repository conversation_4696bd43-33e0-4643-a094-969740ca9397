{"metadata": {"name": "AWS Security Hub", "version": "1.0.0", "category": "Cloud Security", "description": "Connect to AWS Security Hub for continuous compliance monitoring and security findings", "author": "NovaGRC", "tags": ["aws", "security", "compliance", "cloud"], "created": "2025-01-01T00:00:00Z", "updated": "2025-01-01T00:00:00Z", "icon": "https://d1.awsstatic.com/security-center/SecurityHub_Logo.ff40eb5c3f91425913485aa5d0a6d9e4e7c3e7d2.png"}, "authentication": {"type": "AWS_SIG_V4", "fields": {"accessKeyId": {"type": "string", "description": "AWS Access Key ID", "required": true}, "secretAccessKey": {"type": "string", "description": "AWS Secret Access Key", "required": true, "sensitive": true}, "region": {"type": "string", "description": "AWS Region", "required": true, "default": "us-east-1"}}, "testConnection": {"endpoint": "/securityhub/home", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "https://securityhub.{{region}}.amazonaws.com", "headers": {"Content-Type": "application/json"}, "rateLimit": {"requests": 100, "period": "1m"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "getComplianceFindings", "name": "Get Compliance Findings", "description": "Get compliance findings from AWS Security Hub", "path": "/findings", "method": "POST", "parameters": {"body": {"Filters": {"ComplianceStatus": {"Comparison": "EQUALS", "Value": ["PASSED", "FAILED", "WARNING", "NOT_AVAILABLE"]}, "RecordState": {"Comparison": "EQUALS", "Value": ["ACTIVE"]}}, "MaxResults": 100}}, "pagination": {"type": "token", "parameters": {"nextToken": "NextToken"}}, "response": {"successCode": 200, "schema": {"Findings": "array", "NextToken": "string"}}}, {"id": "getSecurityFindings", "name": "Get Security Findings", "description": "Get security findings from AWS Security Hub", "path": "/findings", "method": "POST", "parameters": {"body": {"Filters": {"SeverityLabel": {"Comparison": "EQUALS", "Value": ["CRITICAL", "HIGH", "MEDIUM", "LOW", "INFORMATIONAL"]}, "RecordState": {"Comparison": "EQUALS", "Value": ["ACTIVE"]}}, "MaxResults": 100}}, "pagination": {"type": "token", "parameters": {"nextToken": "NextToken"}}, "response": {"successCode": 200, "schema": {"Findings": "array", "NextToken": "string"}}}], "mappings": [{"sourceEndpoint": "getComplianceFindings", "targetSystem": "NovaGRC", "targetEntity": "ComplianceFindings", "transformations": [{"source": "$.Findings[*].Compliance.Status", "target": "complianceStatus", "transform": "mapComplianceStatus"}, {"source": "$.Findings[*].Resources", "target": "affectedResources", "transform": "extractResourceDetails"}, {"source": "$.Findings[*].Title", "target": "title", "transform": "identity"}, {"source": "$.Findings[*].Description", "target": "description", "transform": "identity"}]}, {"sourceEndpoint": "getSecurityFindings", "targetSystem": "NovaGRC", "targetEntity": "RiskFindings", "transformations": [{"source": "$.Findings[*].Severity.Label", "target": "riskSeverity", "transform": "mapSeverityToRisk"}, {"source": "$.Findings[*].Resources", "target": "affectedAssets", "transform": "extractAssetDetails"}, {"source": "$.Findings[*].Title", "target": "title", "transform": "identity"}, {"source": "$.Findings[*].Description", "target": "description", "transform": "identity"}]}], "events": {"polling": [{"endpoint": "getComplianceFindings", "interval": "15m", "condition": "hasNewFindings"}, {"endpoint": "getSecurityFindings", "interval": "5m", "condition": "hasCriticalFindings"}]}}