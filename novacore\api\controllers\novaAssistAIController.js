/**
 * NovaAssistAI Controller
 *
 * This controller handles requests to the NovaAssistAI chatbot API.
 * It processes user messages, manages conversation context, and returns
 * appropriate responses and suggestions.
 */

const { OpenAI } = require('openai');
const mongoose = require('mongoose');
const Conversation = require('../models/conversation');
const User = require('../models/user');
const ComplianceKnowledge = require('../models/complianceKnowledge');
const ActionHandler = require('../services/actionHandler');
const logger = require('../utils/logger');

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

/**
 * Process a message from the user and generate a response
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.processMessage = async (req, res) => {
  try {
    const { message, context, history, attachments } = req.body;
    const userId = req.user.id;

    if (!message && (!attachments || attachments.length === 0)) {
      return res.status(400).json({ error: 'Message or attachments are required' });
    }

    // Get or create conversation
    let conversation = await Conversation.findOne({
      userId,
      endedAt: null
    }).sort({ createdAt: -1 });

    if (!conversation) {
      conversation = new Conversation({
        userId,
        messages: []
      });
    }

    // Add user message to conversation
    conversation.messages.push({
      role: 'user',
      content: message || 'Sent attachments',
      timestamp: new Date(),
      attachments: attachments || []
    });

    // Get user information for personalization
    const user = await User.findById(userId);

    // Prepare conversation history for AI
    const conversationHistory = history || conversation.messages.slice(-10);

    // Get relevant compliance knowledge based on message content
    const relevantKnowledge = await getRelevantKnowledge(message, context);

    // Prepare system message with context and knowledge
    const systemMessage = prepareSystemMessage(user, context, relevantKnowledge);

    // Prepare messages for OpenAI
    const messages = [
      { role: 'system', content: systemMessage },
      ...conversationHistory.map(msg => ({
        role: msg.role,
        content: msg.content
      }))
    ];

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: 'gpt-4-turbo',
      messages,
      temperature: 0.7,
      max_tokens: 1000,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0
    });

    // Get AI response
    const aiResponse = completion.choices[0].message.content;

    // Process attachments in the response if needed
    // For example, if AI generates images or other files
    const responseAttachments = [];

    // Add AI response to conversation
    conversation.messages.push({
      role: 'assistant',
      content: aiResponse,
      timestamp: new Date(),
      attachments: responseAttachments
    });

    // Save conversation
    await conversation.save();

    // Generate suggestions based on context and response
    const suggestions = await generateSuggestions(aiResponse, context, message);

    // Return response
    return res.status(200).json({
      message: aiResponse,
      suggestions,
      conversationId: conversation._id,
      attachments: responseAttachments
    });
  } catch (error) {
    logger.error('Error processing message:', error);
    return res.status(500).json({ error: 'Failed to process message' });
  }
};

/**
 * Execute an action suggested by NovaAssistAI
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.executeAction = async (req, res) => {
  try {
    const { action, params } = req.body;
    const userId = req.user.id;

    if (!action) {
      return res.status(400).json({ error: 'Action is required' });
    }

    // Execute the action
    const result = await ActionHandler.executeAction(action, params, userId);

    // Get current conversation
    const conversation = await Conversation.findOne({
      userId,
      endedAt: null
    }).sort({ createdAt: -1 });

    if (conversation) {
      // Add system message about the action
      conversation.messages.push({
        role: 'system',
        content: `Executed action: ${action}`,
        timestamp: new Date()
      });

      // If the action result includes a message, add it as an assistant message
      if (result.message) {
        conversation.messages.push({
          role: 'assistant',
          content: result.message,
          timestamp: new Date()
        });
      }

      await conversation.save();
    }

    // Return result
    return res.status(200).json(result);
  } catch (error) {
    logger.error('Error executing action:', error);
    return res.status(500).json({ error: 'Failed to execute action' });
  }
};

/**
 * End the current conversation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.endConversation = async (req, res) => {
  try {
    const userId = req.user.id;

    // Get current conversation
    const conversation = await Conversation.findOne({
      userId,
      endedAt: null
    }).sort({ createdAt: -1 });

    if (!conversation) {
      return res.status(404).json({ error: 'No active conversation found' });
    }

    // End conversation
    conversation.endedAt = new Date();
    await conversation.save();

    return res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Error ending conversation:', error);
    return res.status(500).json({ error: 'Failed to end conversation' });
  }
};

/**
 * Get conversation history
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConversationHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit = 10, page = 1 } = req.query;

    // Get conversations
    const conversations = await Conversation.find({ userId })
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));

    // Get total count
    const total = await Conversation.countDocuments({ userId });

    return res.status(200).json({
      conversations,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    logger.error('Error getting conversation history:', error);
    return res.status(500).json({ error: 'Failed to get conversation history' });
  }
};

/**
 * Prepare system message with context and knowledge
 * @param {Object} user - User object
 * @param {Object} context - Context information
 * @param {Array} relevantKnowledge - Relevant compliance knowledge
 * @returns {String} System message
 */
function prepareSystemMessage(user, context, relevantKnowledge) {
  const currentDate = new Date().toISOString().split('T')[0];

  let systemMessage = `
You are NovaAssistAI, an intelligent assistant for the NovaFuse Cyber-Safety Platform.
Current date: ${currentDate}

USER INFORMATION:
- Name: ${user.firstName} ${user.lastName}
- Role: ${user.role}
- Organization: ${user.organizationName}

YOUR CAPABILITIES:
- Answer questions about compliance frameworks, controls, and regulations
- Help users navigate the NovaFuse platform
- Assist with compliance testing and evidence collection
- Provide guidance on regulatory requirements
- Suggest actions based on user context

YOUR PERSONALITY:
- Professional but friendly
- Concise and clear
- Helpful and proactive
- Knowledgeable about compliance and security

CURRENT CONTEXT:
- Current page: ${context?.currentPage || 'Unknown'}
${context?.subSection ? `- Sub-section: ${context.subSection}` : ''}
${context?.itemId ? `- Item ID: ${context.itemId}` : ''}
`;

  // Add relevant knowledge if available
  if (relevantKnowledge && relevantKnowledge.length > 0) {
    systemMessage += '\nRELEVANT KNOWLEDGE:\n';
    relevantKnowledge.forEach(knowledge => {
      systemMessage += `- ${knowledge.title}: ${knowledge.content}\n`;
    });
  }

  systemMessage += `
RESPONSE GUIDELINES:
- Keep responses concise and focused on the user's question
- If you don't know the answer, say so rather than making up information
- When suggesting actions, provide clear steps
- Format responses with markdown for readability
- Include links to relevant documentation when appropriate
- Suggest follow-up questions or actions when helpful
`;

  return systemMessage;
}

/**
 * Get relevant compliance knowledge based on message content
 * @param {String} message - User message
 * @param {Object} context - Context information
 * @returns {Array} Relevant knowledge
 */
async function getRelevantKnowledge(message, context) {
  try {
    // Extract keywords from message
    const keywords = extractKeywords(message);

    // Get relevant knowledge based on keywords and context
    const query = {
      $or: [
        { keywords: { $in: keywords } },
        { title: { $regex: keywords.join('|'), $options: 'i' } }
      ]
    };

    // Add context filters if available
    if (context?.currentPage) {
      query.relevantPages = context.currentPage;
    }

    // Get knowledge
    const knowledge = await ComplianceKnowledge.find(query).limit(5);

    return knowledge;
  } catch (error) {
    logger.error('Error getting relevant knowledge:', error);
    return [];
  }
}

/**
 * Extract keywords from message
 * @param {String} message - User message
 * @returns {Array} Keywords
 */
function extractKeywords(message) {
  // Simple keyword extraction
  const stopWords = ['a', 'an', 'the', 'and', 'or', 'but', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'against', 'between', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'from', 'up', 'down', 'of', 'off', 'over', 'under', 'again', 'further', 'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 's', 't', 'can', 'will', 'just', 'don', 'should', 'now'];

  // Convert to lowercase and remove punctuation
  const cleanedMessage = message.toLowerCase().replace(/[^\w\s]/g, '');

  // Split into words and filter out stop words
  const words = cleanedMessage.split(/\s+/).filter(word => !stopWords.includes(word) && word.length > 2);

  // Return unique keywords
  return [...new Set(words)];
}

/**
 * Generate suggestions based on context and response
 * @param {String} aiResponse - AI response
 * @param {Object} context - Context information
 * @param {String} userMessage - User message
 * @returns {Array} Suggestions
 */
async function generateSuggestions(aiResponse, context, userMessage) {
  try {
    // Default suggestions based on current page
    const defaultSuggestions = getDefaultSuggestions(context);

    // Generate dynamic suggestions based on AI response and user message
    const dynamicSuggestions = await generateDynamicSuggestions(aiResponse, userMessage, context);

    // Combine suggestions and limit to 5
    return [...dynamicSuggestions, ...defaultSuggestions].slice(0, 5);
  } catch (error) {
    logger.error('Error generating suggestions:', error);
    return [];
  }
}

/**
 * Get default suggestions based on context
 * @param {Object} context - Context information
 * @returns {Array} Default suggestions
 */
function getDefaultSuggestions(context) {
  const suggestions = [];

  // Add page-specific suggestions
  switch (context?.currentPage) {
    case 'dashboard':
      suggestions.push(
        { id: 'dashboard-1', text: 'Show me my compliance status' },
        { id: 'dashboard-2', text: 'What frameworks am I using?' }
      );
      break;
    case 'nova-assure':
      suggestions.push(
        { id: 'nova-assure-1', text: 'How do I create a new test?' },
        { id: 'nova-assure-2', text: 'Show me failed tests' }
      );
      break;
    case 'frameworks':
      suggestions.push(
        { id: 'frameworks-1', text: 'Explain SOC 2 requirements' },
        { id: 'frameworks-2', text: 'How do frameworks map to each other?' }
      );
      break;
    default:
      suggestions.push(
        { id: 'general-1', text: 'How can NovaFuse help me?' },
        { id: 'general-2', text: 'What is Cyber-Safety?' }
      );
  }

  return suggestions;
}

/**
 * Generate dynamic suggestions based on AI response and user message
 * @param {String} aiResponse - AI response
 * @param {String} userMessage - User message
 * @param {Object} context - Context information
 * @returns {Array} Dynamic suggestions
 */
async function generateDynamicSuggestions(aiResponse, userMessage, context) {
  try {
    // Prepare prompt for suggestion generation
    const prompt = `
Based on the following user message and AI assistant response, generate 3 follow-up suggestions that would be helpful for the user. Each suggestion should be a short phrase or question (max 8 words).

User message: "${userMessage}"
AI response: "${aiResponse}"
Current page: ${context?.currentPage || 'Unknown'}

Format each suggestion as a JSON object with 'id' and 'text' fields. If the suggestion should trigger an action, include 'action' and 'params' fields.
Example: {"id":"suggestion1","text":"Show me failed tests","action":"showFailedTests","params":{"timeframe":"week"}}

Suggestions:
`;

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: 'gpt-4-turbo',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
      max_tokens: 500
    });

    // Parse suggestions from response
    const response = completion.choices[0].message.content;
    const suggestionMatches = response.match(/\{[^{}]+\}/g);

    if (!suggestionMatches) {
      return [];
    }

    // Parse and validate suggestions
    const suggestions = suggestionMatches
      .map(match => {
        try {
          return JSON.parse(match);
        } catch (e) {
          return null;
        }
      })
      .filter(suggestion => suggestion && suggestion.id && suggestion.text);

    return suggestions;
  } catch (error) {
    logger.error('Error generating dynamic suggestions:', error);
    return [];
  }
}

module.exports = exports;
