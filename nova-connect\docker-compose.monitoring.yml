version: '3.8'

services:
  # NovaConnect API
  nova-connect:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - MONGODB_URI=mongodb://mongo:27017/nova-connect
      - METRICS_ENABLED=true
      - TRACING_ENABLED=true
      - TRACING_EXPORTER=zipkin
      - ZIPKIN_URL=http://zipkin:9411/api/v2/spans
    volumes:
      - ./:/app
      - /app/node_modules
    depends_on:
      - mongo
      - prometheus
      - zipkin
    networks:
      - nova-network

  # MongoDB
  mongo:
    image: mongo:4.4
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    networks:
      - nova-network

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    networks:
      - nova-network

  # Grafana for metrics visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    volumes:
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - grafana-data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus
    networks:
      - nova-network

  # Zipkin for distributed tracing
  zipkin:
    image: openzipkin/zipkin:latest
    ports:
      - "9411:9411"
    networks:
      - nova-network

  # Jaeger for distributed tracing (alternative to Zipkin)
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"  # UI
      - "14268:14268"  # Collector
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=9411
    networks:
      - nova-network

volumes:
  mongo-data:
  prometheus-data:
  grafana-data:

networks:
  nova-network:
    driver: bridge
