<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: rgba(0, 255, 150, 0.2); border: 1px solid #00ff96; }
        .error { background: rgba(255, 71, 87, 0.2); border: 1px solid #ff4757; }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🔌 Backend Connection Diagnostic</h1>
    
    <button onclick="testConnection()">Test Connection</button>
    <button onclick="testCORS()">Test CORS</button>
    <button onclick="testWebSocket()">Test WebSocket</button>
    
    <div id="results"></div>

    <script>
        function addResult(message, isSuccess = true) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
        }

        async function testConnection() {
            addResult('🔌 Testing basic connection to localhost:8090...');
            
            try {
                // Test with different approaches
                const response = await fetch('http://localhost:8090/status', {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Connection successful! Status: ${data.status}`, true);
                    addResult(`📊 Coherence: ${(data.coherence * 100).toFixed(1)}%`, true);
                } else {
                    addResult(`❌ HTTP Error: ${response.status} ${response.statusText}`, false);
                }
            } catch (error) {
                addResult(`❌ Connection failed: ${error.message}`, false);
                
                // Try alternative approaches
                addResult('🔄 Trying alternative connection methods...', true);
                
                // Test with no-cors mode
                try {
                    const response2 = await fetch('http://localhost:8090/status', {
                        method: 'GET',
                        mode: 'no-cors'
                    });
                    addResult('✅ No-CORS request sent (response opaque)', true);
                } catch (error2) {
                    addResult(`❌ No-CORS also failed: ${error2.message}`, false);
                }
            }
        }

        async function testCORS() {
            addResult('🌐 Testing CORS configuration...');
            
            try {
                const response = await fetch('http://localhost:8090/health', {
                    method: 'OPTIONS'
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                
                addResult(`✅ CORS Headers: ${JSON.stringify(corsHeaders)}`, true);
                
            } catch (error) {
                addResult(`❌ CORS test failed: ${error.message}`, false);
            }
        }

        function testWebSocket() {
            addResult('📡 Testing WebSocket connection...');
            
            try {
                const ws = new WebSocket('ws://localhost:8090/ws');
                
                ws.onopen = function() {
                    addResult('✅ WebSocket connected successfully!', true);
                    ws.close();
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    addResult(`📨 WebSocket message: ${data.status}`, true);
                };
                
                ws.onerror = function(error) {
                    addResult(`❌ WebSocket error: ${error}`, false);
                };
                
                ws.onclose = function() {
                    addResult('🔌 WebSocket connection closed', true);
                };
                
            } catch (error) {
                addResult(`❌ WebSocket test failed: ${error.message}`, false);
            }
        }

        // Auto-run tests
        setTimeout(() => {
            addResult('🚀 Starting automatic connection tests...');
            testConnection();
        }, 1000);
    </script>
</body>
</html>
