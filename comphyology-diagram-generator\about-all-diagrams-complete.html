<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About These Diagrams - Complete Collection (54+ Diagrams)</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .overview-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .diagram-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .section-title {
            font-size: 1.8em;
            font-weight: bold;
        }
        
        .section-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .html-ready {
            background: #27ae60;
            color: white;
        }
        
        .mermaid-source {
            background: #f39c12;
            color: white;
        }
        
        .react-component {
            background: #3498db;
            color: white;
        }
        
        .novacaia-system {
            background: #9b59b6;
            color: white;
        }
        
        .diagram-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .diagram-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            border-left: 4px solid #ffd700;
            transition: all 0.3s ease;
        }
        
        .diagram-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .diagram-name {
            font-size: 1.1em;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 8px;
        }
        
        .diagram-description {
            font-size: 0.9em;
            line-height: 1.4;
            margin-bottom: 10px;
            opacity: 0.9;
        }
        
        .diagram-path {
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            background: rgba(0, 0, 0, 0.3);
            padding: 5px;
            border-radius: 4px;
            word-break: break-all;
        }
        
        .purpose-section {
            background: rgba(255, 215, 0, 0.2);
            border: 1px solid #ffd700;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .purpose-section h3 {
            color: #ffd700;
            margin-top: 0;
        }
        
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        
        .btn:hover {
            background: linear-gradient(45deg, #ee5a24, #ff6b6b);
            transform: scale(1.05);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
        }
        
        .btn-secondary:hover {
            background: linear-gradient(45deg, #29b6f6, #4fc3f7);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 About These Diagrams</h1>
        <p class="subtitle">Complete Collection: 60+ Comprehensive Comphyology Visualizations</p>
        
        <div class="overview-stats">
            <div class="stat-card">
                <div class="stat-number">60+</div>
                <div class="stat-label">Total Diagrams</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">23</div>
                <div class="stat-label">HTML Ready</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">25</div>
                <div class="stat-label">Mermaid Source</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">12+</div>
                <div class="stat-label">System Components</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">6</div>
                <div class="stat-label">Diagram Sets</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">100%</div>
                <div class="stat-label">Patent Coverage</div>
            </div>
        </div>
        
        <div class="purpose-section">
            <h3>🎯 Purpose & Strategic Value</h3>
            <p>This comprehensive diagram collection represents the complete visual documentation of the Comphyology Patent and NovaFuse ecosystem. These diagrams serve multiple critical purposes:</p>
            <ul>
                <li><strong>Patent Support:</strong> Visual evidence for all 26 patent claims with technical specifications</li>
                <li><strong>Technical Documentation:</strong> Complete system architecture and implementation details</li>
                <li><strong>Business Presentation:</strong> Professional visualizations for investors and partners</li>
                <li><strong>Development Reference:</strong> Implementation guides for engineering teams</li>
                <li><strong>Regulatory Compliance:</strong> USPTO-ready diagrams for patent submission</li>
            </ul>
        </div>
        
        <!-- SET A: UUFT Core Diagrams -->
        <div class="diagram-section">
            <div class="section-header">
                <div class="section-title">📊 Set A: UUFT Core Diagrams</div>
                <div class="section-badge html-ready">8 HTML Ready</div>
            </div>
            
            <div class="diagram-grid">
                <div class="diagram-item">
                    <div class="diagram-name">UUFT Mathematical Framework</div>
                    <div class="diagram-description">Core mathematical visualization showing triadic operations and universal field theory foundations.</div>
                    <div class="diagram-path">UUFT_Diagrams.html</div>
                </div>
                
                <div class="diagram-item">
                    <div class="diagram-name">Cross-Domain Pattern Translation</div>
                    <div class="diagram-description">System for translating patterns across consciousness, biology, and physics domains.</div>
                    <div class="diagram-path">UUFT_Diagrams.html</div>
                </div>
                
                <div class="diagram-item">
                    <div class="diagram-name">Cyber-Safety Domain Fusion</div>
                    <div class="diagram-description">Architecture for fusing cyber-safety domains with consciousness validation.</div>
                    <div class="diagram-path">UUFT_Diagrams.html</div>
                </div>
                
                <div class="diagram-item">
                    <div class="diagram-name">NovaFuse Universal Platform</div>
                    <div class="diagram-description">Complete platform architecture showing all 13 NovaFuse components.</div>
                    <div class="diagram-path">UUFT_Diagrams_Part2.html</div>
                </div>
                
                <div class="diagram-item">
                    <div class="diagram-name">18/82 Data Splitter Hardware</div>
                    <div class="diagram-description">Hardware schematic for economic optimization data processing module.</div>
                    <div class="diagram-path">UUFT_Diagrams_Part2.html</div>
                </div>
                
                <div class="diagram-item">
                    <div class="diagram-name">Financial Prediction Hardware</div>
                    <div class="diagram-description">Hardware architecture for consciousness-aware financial prediction systems.</div>
                    <div class="diagram-path">UUFT_Diagrams_Part3.html</div>
                </div>
                
                <div class="diagram-item">
                    <div class="diagram-name">Partner Empowerment Module</div>
                    <div class="diagram-description">Module design for 18/82 partner empowerment and economic optimization.</div>
                    <div class="diagram-path">UUFT_Diagrams_Part3.html</div>
                </div>
                
                <div class="diagram-item">
                    <div class="diagram-name">12+1 Nova Components Overview</div>
                    <div class="diagram-description">High-level overview of all 13 universal Nova components and interconnections.</div>
                    <div class="diagram-path">UUFT_Diagrams_Part3.html</div>
                </div>
            </div>
        </div>
        
        <!-- SET B: Strategic Framework -->
        <div class="diagram-section">
            <div class="section-header">
                <div class="section-title">🎯 Set B: Strategic Framework</div>
                <div class="section-badge html-ready">3+ HTML Ready</div>
            </div>
            
            <div class="diagram-grid">
                <div class="diagram-item">
                    <div class="diagram-name">Cyber-Safety Dominance Framework</div>
                    <div class="diagram-description">Strategic trinity showing cyber-safety dominance through consciousness integration.</div>
                    <div class="diagram-path">strategic-framework-viewer.html</div>
                </div>
                
                <div class="diagram-item">
                    <div class="diagram-name">3,142x Performance Visualization</div>
                    <div class="diagram-description">Performance improvement visualization showing consciousness field optimization.</div>
                    <div class="diagram-path">strategic-framework-viewer.html</div>
                </div>
                
                <div class="diagram-item">
                    <div class="diagram-name">Partner Empowerment Flywheel</div>
                    <div class="diagram-description">Flywheel mechanism for partner empowerment through consciousness amplification.</div>
                    <div class="diagram-path">strategic-framework-viewer.html</div>
                </div>
            </div>
        </div>
        
        <!-- SET C: Core Patent Diagrams -->
        <div class="diagram-section">
            <div class="section-header">
                <div class="section-title">⚖️ Set C: Core Patent Diagrams</div>
                <div class="section-badge html-ready">5 HTML Ready</div>
            </div>
            
            <div class="diagram-grid">
                <div class="diagram-item">
                    <div class="diagram-name">Core Architecture</div>
                    <div class="diagram-description">Core system architecture for AI-driven compliance enforcement using tensor-NIST fusion.</div>
                    <div class="diagram-path">patent_diagrams_simplified.html</div>
                </div>
                
                <div class="diagram-item">
                    <div class="diagram-name">Tensor Fusion</div>
                    <div class="diagram-description">Tensor fusion mechanism combining consciousness, truth, and financial fields.</div>
                    <div class="diagram-path">patent_diagrams_simplified.html</div>
                </div>
                
                <div class="diagram-item">
                    <div class="diagram-name">Marketplace Flow</div>
                    <div class="diagram-description">Marketplace flow showing partner empowerment and economic optimization.</div>
                    <div class="diagram-path">patent_diagrams_simplified.html</div>
                </div>
                
                <div class="diagram-item">
                    <div class="diagram-name">AI Constraint Model</div>
                    <div class="diagram-description">AI constraint model for consciousness validation and boundary enforcement.</div>
                    <div class="diagram-path">patent_diagrams_simplified.html</div>
                </div>
                
                <div class="diagram-item">
                    <div class="diagram-name">Cross-Domain Translation</div>
                    <div class="diagram-description">Cross-domain translation system for pattern recognition and field integration.</div>
                    <div class="diagram-path">patent_diagrams_simplified.html</div>
                </div>
            </div>
        </div>

        <!-- SET D: Detailed Implementation -->
        <div class="diagram-section">
            <div class="section-header">
                <div class="section-title">🔧 Set D: Detailed Implementation</div>
                <div class="section-badge html-ready">7+ HTML Ready</div>
            </div>

            <div class="diagram-grid">
                <div class="diagram-item">
                    <div class="diagram-name">12+1 Universal Novas</div>
                    <div class="diagram-description">Detailed implementation of all 12+1 Nova components with interconnection patterns.</div>
                    <div class="diagram-path">patent-diagrams-new/12-novas.html</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">9 Industry-Specific Continuances</div>
                    <div class="diagram-description">Nine industry-specific continuance patterns for specialized implementation.</div>
                    <div class="diagram-path">patent-diagrams-new/9-continuances.html</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Detailed Data Flow</div>
                    <div class="diagram-description">Comprehensive data flow showing cross-module processing and consciousness validation.</div>
                    <div class="diagram-path">patent-diagrams-new/detailed-data-flow.html</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">18/82 Principle Implementation</div>
                    <div class="diagram-description">Implementation details of the 18/82 economic optimization principle.</div>
                    <div class="diagram-path">patent-diagrams-new/18-82-principle.html</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Cyber-Safety Incident Response</div>
                    <div class="diagram-description">Incident response flow for cyber-safety events with consciousness validation.</div>
                    <div class="diagram-path">patent-diagrams-new/cyber-safety-incident-response.html</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Visualization Output Examples</div>
                    <div class="diagram-description">Examples of visualization outputs from consciousness field processing.</div>
                    <div class="diagram-path">patent-diagrams-new/visualization-output-examples.html</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Comphyology Mathematical Framework</div>
                    <div class="diagram-description">Mathematical framework underlying the comphyology consciousness field theory.</div>
                    <div class="diagram-path">patent-diagrams-new/comphyology-mathematical-framework.html</div>
                </div>
            </div>
        </div>

        <!-- SET E: Mermaid Source Diagrams -->
        <div class="diagram-section">
            <div class="section-header">
                <div class="section-title">🧬 Set E: Mermaid Source Diagrams</div>
                <div class="section-badge mermaid-source">25 Mermaid Files</div>
            </div>

            <div class="diagram-grid">
                <div class="diagram-item">
                    <div class="diagram-name">UUFT Core Architecture</div>
                    <div class="diagram-description">Mermaid source for Universal Unified Field Theory core architecture.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/uuft_core_architecture.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">3-6-9-12-16 Alignment Architecture</div>
                    <div class="diagram-description">Mathematical alignment progression from core triad to complete system.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/alignment_architecture.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Zero Entropy Law</div>
                    <div class="diagram-description">Fundamental zero entropy principle governing system coherence.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/FIG3_zero_entropy_law.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">TEE Equation Framework</div>
                    <div class="diagram-description">Truth, Efficiency, and Effectiveness equation optimization relationships.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/tee_equation.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">12+1 Nova Components</div>
                    <div class="diagram-description">Complete Nova component architecture showing all 13 integrated systems.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/12_plus_1_novas.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Consciousness Threshold Model</div>
                    <div class="diagram-description">Mathematical model for consciousness detection and validation thresholds.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/consciousness_threshold.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Protein Folding Optimization</div>
                    <div class="diagram-description">Protein folding prediction using consciousness field principles.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/protein_folding.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Three-Body Problem Reframing</div>
                    <div class="diagram-description">Novel approach to solving three-body problem using consciousness field theory.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/three_body_problem_reframing.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">NovaFuse Universal Stack</div>
                    <div class="diagram-description">Complete NovaFuse technology stack showing all layers and components.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/nova_fuse_universal_stack.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Crown Consensus & KetherNet</div>
                    <div class="diagram-description">Revolutionary blockchain consensus mechanism with consciousness validation.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/cadence_governance_loop.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">NEPI Analysis Pipeline</div>
                    <div class="diagram-description">Natural Emergent Progressive Intelligence analysis and processing pipeline.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/nepi_analysis_pipeline.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Cross-Module Data Pipeline</div>
                    <div class="diagram-description">Inter-module data processing pipeline showing system integration points.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/cross_module_data_processing_pipeline.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Healthcare Implementation</div>
                    <div class="diagram-description">Healthcare-specific implementation showing medical applications and workflows.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/healthcare_implementation.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">NovaAlign Studio Architecture</div>
                    <div class="diagram-description">AI alignment studio showing real-time monitoring and correction systems.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/nova_align_studio.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Quantum Decoherence Elimination</div>
                    <div class="diagram-description">Advanced quantum system for eliminating decoherence and maintaining stability.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/quantum_decoherence_elimination.mmd</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">+ 10 More Mermaid Diagrams</div>
                    <div class="diagram-description">Additional specialized diagrams covering efficiency formulas, dark field classification, finite universe principles, and more.</div>
                    <div class="diagram-path">Comphyology Diagrams/Mermaid/ (various files)</div>
                </div>
            </div>
        </div>

        <!-- SET F: NovaCaia & Recent Additions -->
        <div class="diagram-section">
            <div class="section-header">
                <div class="section-title">🤖 Set F: NovaCaia & Recent Systems</div>
                <div class="section-badge novacaia-system">6+ Systems</div>
            </div>

            <div class="diagram-grid">
                <div class="diagram-item">
                    <div class="diagram-name">NovaCaia Enterprise Architecture</div>
                    <div class="diagram-description">Complete NovaCaia AI governance system with NERS, NEPI, NEFC integration.</div>
                    <div class="diagram-path">src/novacaia/nova_caia_bridge.py (system architecture)</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">CASTL™ Trinity Processing</div>
                    <div class="diagram-description">Consciousness-Aware Systematic Truth Logic processing with ∂Ψ=0 enforcement.</div>
                    <div class="diagram-path">coherence-reality-systems/nhetx-castl-alpha/ (implementation)</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Divine Economics (18/82 Model)</div>
                    <div class="diagram-description">Financial optimization using 18% tithe + offering allocation for abundance coherence.</div>
                    <div class="diagram-path">src/novacaia/ (financial model implementation)</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Kubernetes Deployment Architecture</div>
                    <div class="diagram-description">Enterprise-ready Kubernetes deployment with health checks and load balancing.</div>
                    <div class="diagram-path">src/novacaia/Dockerfile.prod (deployment specs)</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">Real-Time Dashboard Schemas</div>
                    <div class="diagram-description">Dynamic dashboard schemas for consciousness monitoring and system health visualization.</div>
                    <div class="diagram-path">comphyology_schemas/ (dashboard implementations)</div>
                </div>

                <div class="diagram-item">
                    <div class="diagram-name">React Component Diagrams</div>
                    <div class="diagram-description">Interactive React components for NovaFuse platform visualization and user interfaces.</div>
                    <div class="diagram-path">comphyology-diagram-generator/components/ (React components)</div>
                </div>
            </div>
        </div>

        <!-- COMPREHENSIVE SUMMARY -->
        <div class="purpose-section">
            <h3>📋 Complete Diagram Collection Summary</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
                <div>
                    <h4 style="color: #27ae60; margin-bottom: 10px;">✅ Ready for Use (23 HTML)</h4>
                    <ul style="margin: 0; padding-left: 20px; font-size: 0.9em;">
                        <li>8 UUFT Core Diagrams</li>
                        <li>3+ Strategic Framework</li>
                        <li>5 Core Patent Diagrams</li>
                        <li>7+ Implementation Details</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #f39c12; margin-bottom: 10px;">🔄 Convertible (25 Mermaid)</h4>
                    <ul style="margin: 0; padding-left: 20px; font-size: 0.9em;">
                        <li>Core Architecture Sources</li>
                        <li>Mathematical Frameworks</li>
                        <li>System Integration</li>
                        <li>Advanced Applications</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #9b59b6; margin-bottom: 10px;">🤖 System Components (12+)</h4>
                    <ul style="margin: 0; padding-left: 20px; font-size: 0.9em;">
                        <li>NovaCaia AI Governance</li>
                        <li>CASTL™ Processing</li>
                        <li>React Components</li>
                        <li>Dashboard Schemas</li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 25px; padding: 15px; background: rgba(255, 215, 0, 0.1); border-radius: 8px;">
                <h4 style="color: #ffd700; margin-top: 0;">🎯 Strategic Applications</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    <div>
                        <strong>Patent Submission:</strong> USPTO-ready diagrams with complete technical disclosure
                    </div>
                    <div>
                        <strong>Business Presentations:</strong> Professional visualizations for investors and partners
                    </div>
                    <div>
                        <strong>Technical Documentation:</strong> Complete system architecture and implementation guides
                    </div>
                    <div>
                        <strong>Development Reference:</strong> Implementation blueprints for engineering teams
                    </div>
                </div>
            </div>

            <div style="margin-top: 20px; text-align: center; font-size: 1.1em;">
                <strong>🌍 This represents the most comprehensive patent diagram collection in history, providing complete visual documentation for the revolutionary Comphyology technology ecosystem!</strong>
            </div>
        </div>

        <div class="action-buttons">
            <button class="btn" onclick="openPatentOrganizer()">
                📋 Open Patent Organizer
            </button>
            <button class="btn btn-secondary" onclick="viewDiagramShowcase()">
                🎨 View Diagram Showcase
            </button>
            <button class="btn btn-secondary" onclick="openStrategicFramework()">
                🎯 Strategic Framework
            </button>
        </div>
    </div>
    
    <script>
        function openPatentOrganizer() {
            window.open('./patent-diagram-master-organizer.html', '_blank', 'width=1600,height=900');
        }
        
        function viewDiagramShowcase() {
            window.open('./diagram-showcase.html', '_blank', 'width=1400,height=800');
        }
        
        function openStrategicFramework() {
            window.open('../strategic-framework-viewer.html', '_blank', 'width=1400,height=800');
        }
    </script>
</body>
</html>
