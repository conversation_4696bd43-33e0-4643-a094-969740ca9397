/**
 * Privacy Notice Model
 * 
 * Represents a privacy notice that informs individuals about how
 * their personal data is processed.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const privacyNoticeSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  content: {
    type: String,
    required: true
  },
  version: {
    type: String,
    required: true,
    trim: true
  },
  effectiveDate: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    required: true,
    enum: ['draft', 'active', 'archived'],
    default: 'draft'
  },
  audience: {
    type: String,
    required: true,
    trim: true
  },
  language: {
    type: String,
    required: true,
    trim: true,
    default: 'en'
  },
  jurisdiction: {
    type: String,
    trim: true
  },
  applicableRegulations: [{
    type: String,
    trim: true
  }],
  dataCategories: [{
    type: String,
    trim: true
  }],
  purposes: [{
    type: String,
    trim: true
  }],
  retentionPeriods: [{
    category: {
      type: String,
      required: true,
      trim: true
    },
    period: {
      type: String,
      required: true,
      trim: true
    }
  }],
  thirdPartyRecipients: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    purpose: {
      type: String,
      required: true,
      trim: true
    },
    location: {
      type: String,
      trim: true
    }
  }],
  dataSubjectRights: [{
    right: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      required: true,
      trim: true
    }
  }],
  contactInformation: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    email: {
      type: String,
      required: true,
      trim: true
    },
    phone: {
      type: String,
      trim: true
    },
    address: {
      type: String,
      trim: true
    }
  },
  revisionHistory: [{
    version: {
      type: String,
      required: true,
      trim: true
    },
    date: {
      type: Date,
      required: true
    },
    changes: {
      type: String,
      required: true,
      trim: true
    },
    changedBy: {
      type: String,
      required: true,
      trim: true
    }
  }],
  approvals: [{
    approverName: {
      type: String,
      required: true,
      trim: true
    },
    approverRole: {
      type: String,
      required: true,
      trim: true
    },
    approvalDate: {
      type: Date,
      required: true
    },
    comments: {
      type: String,
      trim: true
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create a text index for searching
privacyNoticeSchema.index({
  title: 'text',
  content: 'text'
});

// Pre-save hook to update the updatedAt field
privacyNoticeSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual for age of the privacy notice
privacyNoticeSchema.virtual('age').get(function() {
  const now = new Date();
  const effectiveDate = new Date(this.effectiveDate);
  const diffTime = now - effectiveDate;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

const PrivacyNotice = mongoose.model('PrivacyNotice', privacyNoticeSchema);

module.exports = PrivacyNotice;
