# NovaFuse Universal API Connector (UAC) Quick Start Guide

This guide will help you get started with the NovaFuse Universal API Connector (UAC) quickly and easily.

## Prerequisites

- Node.js 14.x or higher
- npm 7.x or higher
- Basic knowledge of REST APIs

## Installation

### 1. <PERSON>lone the Repository

```bash
git clone https://github.com/novafuse/nova-connect.git
cd nova-connect
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Configure Environment

Create a `.env` file in the root directory:

```
PORT=3010
NODE_ENV=development
LOG_LEVEL=info
TEMPLATES_DIR=./templates
CREDENTIALS_ENCRYPTION_KEY=your-secure-encryption-key
```

### 4. Start the Server

```bash
npm start
```

The UAC API will be available at `http://localhost:3010`.

## Using the UAC

### 1. Register a Connector

First, let's register a connector template. Create a file named `google-cloud-security.json` with the following content:

```json
{
  "metadata": {
    "name": "Google Cloud Security",
    "version": "1.0.0",
    "category": "Cloud Security",
    "description": "Connect to Google Cloud Security Command Center API",
    "author": "NovaFuse",
    "tags": ["google", "cloud", "security"]
  },
  "authentication": {
    "type": "OAUTH2",
    "fields": {
      "clientId": {
        "type": "string",
        "description": "OAuth 2.0 Client ID",
        "required": true
      },
      "clientSecret": {
        "type": "string",
        "description": "OAuth 2.0 Client Secret",
        "required": true,
        "sensitive": true
      }
    }
  },
  "configuration": {
    "baseUrl": "https://securitycenter.googleapis.com/v1"
  },
  "endpoints": [
    {
      "id": "list-findings",
      "name": "List Security Findings",
      "path": "organizations/{organizationId}/sources/{sourceId}/findings",
      "method": "GET",
      "parameters": {
        "path": {
          "organizationId": {
            "type": "string",
            "description": "Organization ID",
            "required": true
          },
          "sourceId": {
            "type": "string",
            "description": "Source ID",
            "required": true
          }
        }
      }
    }
  ]
}
```

Then, register the connector:

```bash
curl -X POST http://localhost:3010/connectors \
  -H "Content-Type: application/json" \
  -d @google-cloud-security.json
```

### 2. Store Credentials

Next, store your API credentials:

```bash
curl -X POST http://localhost:3010/credentials \
  -H "Content-Type: application/json" \
  -d '{
    "connectorId": "google-cloud-security-1.0.0",
    "credentials": {
      "clientId": "your-client-id",
      "clientSecret": "your-client-secret"
    }
  }'
```

This will return a credential ID:

```json
{
  "credentialId": "cred-123456"
}
```

### 3. Test the Connection

Test the connection using the stored credentials:

```bash
curl -X POST http://localhost:3010/credentials/cred-123456/test \
  -H "Content-Type: application/json" \
  -d '{
    "connectorId": "google-cloud-security-1.0.0"
  }'
```

### 4. Execute an Endpoint

Finally, execute an endpoint:

```bash
curl -X POST http://localhost:3010/execute/google-cloud-security-1.0.0/list-findings \
  -H "Content-Type: application/json" \
  -d '{
    "credentialId": "cred-123456",
    "parameters": {
      "path": {
        "organizationId": "123",
        "sourceId": "456"
      },
      "query": {
        "pageSize": 10
      }
    }
  }'
```

## Creating Your Own Connector

### 1. Define Connector Template

Create a JSON file with your connector definition:

```json
{
  "metadata": {
    "name": "Your API",
    "version": "1.0.0",
    "category": "Your Category",
    "description": "Description of your API connector",
    "author": "Your Name",
    "tags": ["tag1", "tag2"]
  },
  "authentication": {
    "type": "API_KEY",
    "fields": {
      "apiKey": {
        "type": "string",
        "description": "API Key",
        "required": true,
        "sensitive": true
      }
    }
  },
  "configuration": {
    "baseUrl": "https://api.example.com/v1"
  },
  "endpoints": [
    {
      "id": "list-items",
      "name": "List Items",
      "path": "items",
      "method": "GET"
    },
    {
      "id": "get-item",
      "name": "Get Item",
      "path": "items/{itemId}",
      "method": "GET",
      "parameters": {
        "path": {
          "itemId": {
            "type": "string",
            "description": "Item ID",
            "required": true
          }
        }
      }
    }
  ]
}
```

### 2. Register the Connector

Register your connector:

```bash
curl -X POST http://localhost:3010/connectors \
  -H "Content-Type: application/json" \
  -d @your-connector.json
```

### 3. Use the Connector

Follow steps 2-4 from the previous section to store credentials, test the connection, and execute endpoints.

## Next Steps

- Explore the [UAC Documentation](https://docs.novafuse.com/uac) for more details
- Learn about [Advanced Connector Features](https://docs.novafuse.com/uac/advanced)
- Check out the [API Reference](https://docs.novafuse.com/uac/api)
- Join the [NovaFuse Community](https://community.novafuse.com)

## Troubleshooting

### Common Issues

#### Connection Errors

- Check that your API credentials are correct
- Verify that the API endpoint is accessible
- Check for rate limiting or IP blocking

#### Authentication Errors

- Verify that your authentication configuration is correct
- Check that your credentials have not expired
- Ensure that you have the required permissions

#### Execution Errors

- Check your API parameters for correctness
- Verify that the endpoint path is correct
- Check for API-specific error messages

### Getting Help

If you encounter any issues, please:

1. Check the [UAC Documentation](https://docs.novafuse.com/uac)
2. Search the [Knowledge Base](https://support.novafuse.com/kb)
3. Contact [Support](mailto:<EMAIL>)
