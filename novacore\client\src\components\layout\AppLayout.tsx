/**
 * Application Layout
 * 
 * This component provides the main layout for the application,
 * including navigation, header, and the NovaAssistAI chat widget.
 */

import React, { ReactNode } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import ChatWidget from '../NovaAssistAI/ChatWidget';
import { 
  LayoutDashboard, 
  Shield, 
  FileText, 
  Settings, 
  Bell, 
  User, 
  LogOut,
  Menu,
  X
} from 'lucide-react';
import { Button } from '../ui/button';

interface AppLayoutProps {
  children: ReactNode;
}

export const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const router = useRouter();
  const { user, logout } = useAuth();
  const [sidebarOpen, setSidebarOpen] = React.useState(false);
  
  const handleLogout = () => {
    logout();
    router.push('/login');
  };
  
  const navItems = [
    { 
      name: 'Dashboard', 
      href: '/dashboard', 
      icon: <LayoutDashboard className="h-5 w-5" /> 
    },
    { 
      name: 'NovaAssure', 
      href: '/novaassure', 
      icon: <Shield className="h-5 w-5" /> 
    },
    { 
      name: 'Frameworks', 
      href: '/frameworks', 
      icon: <FileText className="h-5 w-5" /> 
    },
    { 
      name: 'Settings', 
      href: '/settings', 
      icon: <Settings className="h-5 w-5" /> 
    }
  ];
  
  // Get current page context for NovaAssistAI
  const getCurrentPageContext = () => {
    const currentPath = router.pathname;
    const pathParts = currentPath.split('/').filter(Boolean);
    
    if (pathParts.length === 0) {
      return { currentPage: 'home' };
    }
    
    const mainSection = pathParts[0];
    const subSection = pathParts.length > 1 ? pathParts[1] : null;
    const itemId = pathParts.length > 2 ? pathParts[2] : null;
    
    return {
      currentPage: mainSection,
      subSection,
      itemId
    };
  };
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-gray-600 bg-opacity-75 z-20 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
      
      {/* Sidebar */}
      <aside 
        className={`fixed top-0 left-0 bottom-0 w-64 bg-white border-r border-gray-200 z-30 transition-transform duration-300 transform ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-4 border-b">
            <Link href="/dashboard" className="flex items-center">
              <span className="text-xl font-bold text-blue-600">NovaFuse</span>
            </Link>
            <button 
              className="lg:hidden text-gray-500 hover:text-gray-700"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          {/* Navigation */}
          <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
            {navItems.map((item) => {
              const isActive = router.pathname === item.href || router.pathname.startsWith(`${item.href}/`);
              
              return (
                <Link 
                  key={item.name} 
                  href={item.href}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                    isActive 
                      ? 'bg-blue-50 text-blue-700' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <span className={`mr-3 ${isActive ? 'text-blue-500' : 'text-gray-500'}`}>
                    {item.icon}
                  </span>
                  {item.name}
                </Link>
              );
            })}
          </nav>
          
          {/* User section */}
          <div className="p-4 border-t">
            {user && (
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium">
                    {user.firstName?.charAt(0) || user.email?.charAt(0) || 'U'}
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-700">
                    {user.firstName} {user.lastName}
                  </p>
                  <p className="text-xs text-gray-500">
                    {user.email}
                  </p>
                </div>
                <button 
                  onClick={handleLogout}
                  className="ml-auto text-gray-500 hover:text-gray-700"
                  aria-label="Logout"
                >
                  <LogOut className="h-5 w-5" />
                </button>
              </div>
            )}
          </div>
        </div>
      </aside>
      
      {/* Main content */}
      <div className="lg:pl-64">
        {/* Header */}
        <header className="bg-white shadow-sm h-16 flex items-center z-10 sticky top-0">
          <div className="flex items-center justify-between w-full px-4">
            <button 
              className="lg:hidden text-gray-500 hover:text-gray-700"
              onClick={() => setSidebarOpen(true)}
            >
              <Menu className="h-6 w-6" />
            </button>
            
            <div className="flex items-center ml-auto space-x-4">
              <button className="text-gray-500 hover:text-gray-700 relative">
                <Bell className="h-5 w-5" />
                <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500"></span>
              </button>
              
              <button className="text-gray-500 hover:text-gray-700">
                <User className="h-5 w-5" />
              </button>
            </div>
          </div>
        </header>
        
        {/* Page content */}
        <main className="pb-16">
          {children}
        </main>
      </div>
      
      {/* NovaAssistAI Chat Widget */}
      <ChatWidget context={getCurrentPageContext()} />
    </div>
  );
};

export default AppLayout;
