/**
 * NovaFuse Privacy Management API
 *
 * This API provides a comprehensive solution for managing privacy compliance
 * across multiple systems and jurisdictions. It includes features for managing
 * data subject requests, consent records, privacy notices, data breaches, and
 * regulatory compliance.
 */

const express = require('express');
const router = express.Router();

// Import middleware
const { authenticate } = require('./middleware/auth');
const errorHandler = require('./middleware/errorHandler');

// Import routes
const dataProcessingRoutes = require('./routes/dataProcessingRoutes');
const subjectRequestRoutes = require('./routes/subjectRequestRoutes');
const consentRoutes = require('./routes/consentRoutes');
const privacyNoticeRoutes = require('./routes/privacyNoticeRoutes');
const dataBreachRoutes = require('./routes/dataBreachRoutes');
const integrationRoutes = require('./routes/integrationRoutes');
const notificationRoutes = require('./routes/notificationRoutes');
const reportRoutes = require('./routes/reportRoutes');
const regulatoryComplianceRoutes = require('./routes/regulatoryComplianceRoutes');

// Base route for health check
router.get('/', (req, res) => {
  res.json({
    name: 'NovaFuse Privacy Management API',
    version: '1.0.0',
    status: 'operational',
    features: [
      'Data Processing Activities',
      'Data Subject Requests',
      'Consent Management',
      'Privacy Notices',
      'Data Breach Management',
      'Integrations',
      'Notifications',
      'Reports and Analytics',
      'Regulatory Compliance'
    ]
  });
});

// Import authentication service and validation
const authService = require('./services/authService');
const { validate } = require('./middleware/validation');
const { authSchema } = require('./validation');

// Authentication routes (no auth middleware required)
router.post('/auth/login', validate(authSchema.loginSchema), async (req, res, next) => {
  try {
    const { username, password } = req.body;

    // Simple validation
    if (!username || !password) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Username and password are required'
      });
    }

    // Authenticate the user
    const authResult = await authService.authenticate(username, password);

    // Return the authentication result
    res.json(authResult);
  } catch (error) {
    if (error.name === 'AuthenticationError') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: error.message
      });
    }

    next(error);
  }
});

// Apply authentication middleware to all other routes
router.use(authenticate);

// Register routes
router.use('/processing-activities', dataProcessingRoutes);
router.use('/subject-requests', subjectRequestRoutes);
router.use('/consent', consentRoutes);
router.use('/privacy-notices', privacyNoticeRoutes);
router.use('/data-breaches', dataBreachRoutes);
router.use('/integrations', integrationRoutes);
router.use('/notifications', notificationRoutes);
router.use('/reports', reportRoutes);
router.use('/compliance', regulatoryComplianceRoutes);

// Apply error handler middleware
router.use(errorHandler);

module.exports = router;
