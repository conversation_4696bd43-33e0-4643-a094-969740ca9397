/**
 * Error Handler
 * 
 * This module provides error handling functionality.
 */

const logger = require('./logger');

/**
 * Custom error class
 */
class AppError extends Error {
  /**
   * Create a new AppError
   * @param {string} message - Error message
   * @param {number} statusCode - HTTP status code
   * @param {Object} [data] - Additional error data
   */
  constructor(message, statusCode, data = {}) {
    super(message);
    this.statusCode = statusCode;
    this.data = data;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Not found error
 * @param {string} message - Error message
 * @param {Object} [data] - Additional error data
 * @returns {AppError} - Not found error
 */
function notFound(message = 'Resource not found', data = {}) {
  return new AppError(message, 404, data);
}

/**
 * Bad request error
 * @param {string} message - Error message
 * @param {Object} [data] - Additional error data
 * @returns {AppError} - Bad request error
 */
function badRequest(message = 'Bad request', data = {}) {
  return new AppError(message, 400, data);
}

/**
 * Unauthorized error
 * @param {string} message - Error message
 * @param {Object} [data] - Additional error data
 * @returns {AppError} - Unauthorized error
 */
function unauthorized(message = 'Unauthorized', data = {}) {
  return new AppError(message, 401, data);
}

/**
 * Forbidden error
 * @param {string} message - Error message
 * @param {Object} [data] - Additional error data
 * @returns {AppError} - Forbidden error
 */
function forbidden(message = 'Forbidden', data = {}) {
  return new AppError(message, 403, data);
}

/**
 * Internal server error
 * @param {string} message - Error message
 * @param {Object} [data] - Additional error data
 * @returns {AppError} - Internal server error
 */
function internal(message = 'Internal server error', data = {}) {
  return new AppError(message, 500, data);
}

/**
 * Validation error
 * @param {string} message - Error message
 * @param {Object} [errors] - Validation errors
 * @returns {AppError} - Validation error
 */
function validation(message = 'Validation error', errors = {}) {
  return new AppError(message, 400, { errors });
}

/**
 * Error handler middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function errorHandler(err, req, res, next) {
  // Default error status and message
  let statusCode = err.statusCode || 500;
  let message = err.message || 'Something went wrong';
  let data = err.data || {};
  
  // Log error
  if (statusCode >= 500) {
    logger.error(`${statusCode} - ${message}`, {
      error: err,
      path: req.path,
      method: req.method,
      ip: req.ip,
      user: req.user ? req.user.id : 'anonymous'
    });
  } else {
    logger.warn(`${statusCode} - ${message}`, {
      path: req.path,
      method: req.method,
      ip: req.ip,
      user: req.user ? req.user.id : 'anonymous'
    });
  }
  
  // Handle Mongoose validation errors
  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation error';
    data = { errors: {} };
    
    // Format validation errors
    for (const field in err.errors) {
      data.errors[field] = err.errors[field].message;
    }
  }
  
  // Handle Mongoose cast errors
  if (err.name === 'CastError') {
    statusCode = 400;
    message = `Invalid ${err.path}: ${err.value}`;
  }
  
  // Handle Mongoose duplicate key errors
  if (err.code === 11000) {
    statusCode = 400;
    message = 'Duplicate field value';
    
    // Extract duplicate field
    const field = Object.keys(err.keyValue)[0];
    data = { field, value: err.keyValue[field] };
  }
  
  // Handle JWT errors
  if (err.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
  }
  
  if (err.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
  }
  
  // Send error response
  res.status(statusCode).json({
    success: false,
    error: message,
    ...data,
    ...(process.env.NODE_ENV === 'development' ? { stack: err.stack } : {})
  });
}

/**
 * Async handler
 * @param {Function} fn - Async function
 * @returns {Function} - Express middleware
 */
function asyncHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

module.exports = {
  AppError,
  notFound,
  badRequest,
  unauthorized,
  forbidden,
  internal,
  validation,
  errorHandler,
  asyncHandler
};
