import React from 'react';
import { motion } from 'framer-motion';
import { fadeInUp, scaleIn } from '../utils/animations';

/**
 * Animated card component with hover effects
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Card content
 * @param {string} props.className - Additional CSS classes
 * @param {number} props.delay - Animation delay in milliseconds
 * @param {string} props.animation - Animation type ('fadeInUp' or 'scaleIn')
 * @param {Function} props.onClick - Click handler
 */
const AnimatedCard = ({ 
  children, 
  className = '', 
  delay = 0, 
  animation = 'fadeInUp',
  onClick
}) => {
  // Get animation properties based on animation type
  const getAnimationProps = () => {
    switch (animation) {
      case 'scaleIn':
        return scaleIn(500, delay);
      case 'fadeInUp':
      default:
        return fadeInUp(500, delay);
    }
  };

  // Hover animation
  const hoverAnimation = {
    scale: 1.02,
    boxShadow: '0 10px 30px -15px rgba(0, 0, 0, 0.5)',
    transition: { duration: 0.2 }
  };

  // Tap animation
  const tapAnimation = {
    scale: 0.98,
    transition: { duration: 0.1 }
  };

  return (
    <motion.div
      className={`bg-secondary rounded-lg overflow-hidden ${className}`}
      {...getAnimationProps()}
      whileHover={hoverAnimation}
      whileTap={tapAnimation}
      onClick={onClick}
    >
      {children}
    </motion.div>
  );
};

export default AnimatedCard;
