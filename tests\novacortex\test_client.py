"""
NovaCortex Test Client for NovaConnect

This module provides a test client for interacting with NovaConnect's API
to test NovaCortex functionality.
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime

@dataclass
class NovaCortexTestConfig:
    """Configuration for NovaCortex tests."""
    base_url: str = "http://localhost:3010"
    api_key: Optional[str] = None
    timeout: int = 30

class NovaCortexTestClient:
    """Test client for NovaCortex functionality via NovaConnect."""
    
    def __init__(self, config: Optional[NovaCortexTestConfig] = None):
        self.config = config or NovaCortexTestConfig()
        self.session = None
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        if self.config.api_key:
            self.headers["Authorization"] = f"Bearer {self.config.api_key}"
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            base_url=self.config.base_url,
            headers=self.headers,
            timeout=aiohttp.ClientTimeout(total=self.config.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_coherence(self) -> Dict[str, Any]:
        """Test coherence maintenance (∂Ψ = 0)."""
        url = "/api/novacortex/coherence"
        async with self.session.get(url) as response:
            response.raise_for_status()
            return await response.json()
    
    async def test_castl_decision(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """Test CASTL-compliant decision making."""
        url = "/api/novacortex/castl/decide"
        async with self.session.post(url, json={"scenario": scenario}) as response:
            response.raise_for_status()
            return await response.json()
    
    async def test_pi_rhythm(self, duration: float = 5.0) -> Dict[str, Any]:
        """Test π-Rhythm synchronization."""
        url = f"/api/novacortex/pi-rhythm/measure?duration={duration}"
        async with self.session.get(url) as response:
            response.raise_for_status()
            return await response.json()
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get NovaCortex metrics."""
        url = "/api/novacortex/metrics"
        async with self.session.get(url) as response:
            response.raise_for_status()
            return await response.json()

# Example usage
async def run_tests():
    config = NovaCortexTestConfig(
        base_url="http://localhost:3010",
        api_key="your-api-key-here"
    )
    
    async with NovaCortexTestClient(config) as client:
        try:
            # Test coherence
            coherence = await client.test_coherence()
            print(f"Coherence Test: {coherence}")
            
            # Test CASTL decision
            scenario = {
                "type": "trolley",
                "options": ["do_nothing", "pull_lever"],
                "consequences": {
                    "do_nothing": ["5_fatalities"],
                    "pull_lever": ["1_fatality"]
                }
            }
            decision = await client.test_castl_decision(scenario)
            print(f"CASTL Decision: {decision}")
            
            # Test π-Rhythm
            rhythm = await client.test_pi_rhythm()
            print(f"π-Rhythm Test: {rhythm}")
            
            # Get metrics
            metrics = await client.get_metrics()
            print(f"Metrics: {metrics}")
            
        except Exception as e:
            print(f"Test failed: {str(e)}")

if __name__ == "__main__":
    asyncio.run(run_tests())
