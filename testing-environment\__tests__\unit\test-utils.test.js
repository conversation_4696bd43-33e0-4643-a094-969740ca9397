/**
 * Unit Tests for Test Utilities
 *
 * These tests focus on the test utilities to help achieve the 96% coverage threshold.
 */

// Import the module to test
const {
  createAxiosMock,
  measureExecutionTime,
  generateTestConnector,
  generateTestCredential,
  deepMerge: deepMergeExported
} = require('../helpers/test-utils');

// Import axios for testing the mock
const axios = require('axios');

// Access the internal deepMerge function for testing
const testUtils = require('../helpers/test-utils');
const deepMerge = testUtils.deepMerge;
const isObject = testUtils.isObject;

describe('Test Utilities', () => {
  // Test createAxiosMock
  describe('createAxiosMock', () => {
    it('should create a mock for axios with GET responses', async () => {
      const mockResponses = {
        '/test': {
          method: 'get',
          status: 200,
          data: { success: true }
        }
      };

      const mock = createAxiosMock(mockResponses);

      const response = await axios.get('/test');

      expect(response.status).toBe(200);
      expect(response.data).toEqual({ success: true });

      mock.restore();
    });

    it('should create a mock for axios with POST responses', async () => {
      const mockResponses = {
        '/test': {
          method: 'post',
          status: 201,
          data: { id: 'new-item' }
        }
      };

      const mock = createAxiosMock(mockResponses);

      const response = await axios.post('/test', { name: 'Test Item' });

      expect(response.status).toBe(201);
      expect(response.data).toEqual({ id: 'new-item' });

      mock.restore();
    });

    it('should create a mock for axios with PUT responses', async () => {
      const mockResponses = {
        '/test/1': {
          method: 'put',
          status: 200,
          data: { id: '1', name: 'Updated Item' }
        }
      };

      const mock = createAxiosMock(mockResponses);

      const response = await axios.put('/test/1', { name: 'Updated Item' });

      expect(response.status).toBe(200);
      expect(response.data).toEqual({ id: '1', name: 'Updated Item' });

      mock.restore();
    });

    it('should create a mock for axios with DELETE responses', async () => {
      const mockResponses = {
        '/test/1': {
          method: 'delete',
          status: 204,
          data: {}
        }
      };

      const mock = createAxiosMock(mockResponses);

      const response = await axios.delete('/test/1');

      expect(response.status).toBe(204);

      mock.restore();
    });

    it('should create a mock for axios with PATCH responses', async () => {
      const mockResponses = {
        '/test/1': {
          method: 'patch',
          status: 200,
          data: { id: '1', name: 'Patched Item' }
        }
      };

      const mock = createAxiosMock(mockResponses);

      const response = await axios.patch('/test/1', { name: 'Patched Item' });

      expect(response.status).toBe(200);
      expect(response.data).toEqual({ id: '1', name: 'Patched Item' });

      mock.restore();
    });

    it('should create a mock for axios with default method', async () => {
      const mockResponses = {
        '/test': {
          status: 200,
          data: { success: true }
        }
      };

      const mock = createAxiosMock(mockResponses);

      const response = await axios.get('/test');

      expect(response.status).toBe(200);
      expect(response.data).toEqual({ success: true });

      mock.restore();
    });

    it('should create a mock for axios with custom headers', async () => {
      const mockResponses = {
        '/test': {
          method: 'get',
          status: 200,
          data: { success: true },
          headers: { 'X-Custom-Header': 'test-value' }
        }
      };

      const mock = createAxiosMock(mockResponses);

      const response = await axios.get('/test');

      expect(response.status).toBe(200);
      expect(response.headers).toHaveProperty('X-Custom-Header', 'test-value');

      mock.restore();
    });

    it('should handle unknown methods by using onAny', async () => {
      const mockResponses = {
        '/test': {
          method: 'unknown',
          status: 200,
          data: { success: true }
        }
      };

      const mock = createAxiosMock(mockResponses);

      const response = await axios.get('/test');

      expect(response.status).toBe(200);
      expect(response.data).toEqual({ success: true });

      mock.restore();
    });

    it('should handle empty mockResponses', async () => {
      // Import MockAdapter directly
      const MockAdapter = require('axios-mock-adapter');

      // Create a mock with empty responses
      const mock = createAxiosMock();

      // Set up a response after creating the mock
      mock.onGet('/test').reply(200, { default: true });

      const response = await axios.get('/test');

      expect(response.status).toBe(200);
      expect(response.data).toEqual({ default: true });

      mock.restore();
    });

    it('should handle response with function', async () => {
      const mockResponses = {
        '/test': {
          method: 'get',
          url: '/test',
          status: 200,
          data: (config) => ({ url: config.url, success: true }),
          headers: { 'X-Custom-Header': 'dynamic' }
        }
      };

      // Mock axios.get to capture the config
      const originalGet = axios.get;
      axios.get = jest.fn().mockImplementation((url) => {
        return Promise.resolve({
          status: 200,
          data: { url: url, success: true },
          headers: { 'X-Custom-Header': 'dynamic' }
        });
      });

      const mock = createAxiosMock(mockResponses);

      const response = await axios.get('/test');

      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('url');
      expect(response.data).toHaveProperty('success', true);
      expect(response.headers).toHaveProperty('X-Custom-Header', 'dynamic');

      // Restore original axios.get
      axios.get = originalGet;
      mock.restore();
    });

    it('should handle null response', async () => {
      const mockResponses = {
        '/test': null
      };

      const mock = createAxiosMock(mockResponses);

      // Set up a default response for the test
      mock.onAny().reply(200, {});

      const response = await axios.get('/test');

      expect(response.status).toBe(200);
      expect(response.data).toEqual({});

      mock.restore();
    });

    it('should handle multiple patterns for the same method', async () => {
      const mockResponses = {
        '/test/1': {
          method: 'get',
          status: 200,
          data: { id: 1 }
        },
        '/test/2': {
          method: 'get',
          status: 200,
          data: { id: 2 }
        }
      };

      const mock = createAxiosMock(mockResponses);

      const response1 = await axios.get('/test/1');
      const response2 = await axios.get('/test/2');

      expect(response1.status).toBe(200);
      expect(response1.data).toEqual({ id: 1 });

      expect(response2.status).toBe(200);
      expect(response2.data).toEqual({ id: 2 });

      mock.restore();
    });
  });

  // Test measureExecutionTime
  describe('measureExecutionTime', () => {
    it('should measure execution time of a synchronous function', async () => {
      const fn = () => {
        let sum = 0;
        for (let i = 0; i < 1000000; i++) {
          sum += i;
        }
        return sum;
      };

      const { result, duration } = await measureExecutionTime(fn);

      expect(typeof result).toBe('number');
      expect(typeof duration).toBe('number');
      expect(duration).toBeGreaterThan(0);
    });

    it('should measure execution time of an asynchronous function', async () => {
      const fn = async () => {
        return new Promise(resolve => {
          setTimeout(() => resolve('done'), 10);
        });
      };

      const { result, duration } = await measureExecutionTime(fn);

      expect(result).toBe('done');
      expect(typeof duration).toBe('number');
      expect(duration).toBeGreaterThan(0);
    });
  });

  // Test generateTestConnector
  describe('generateTestConnector', () => {
    it('should generate a default test connector', () => {
      const connector = generateTestConnector();

      expect(connector).toHaveProperty('metadata');
      expect(connector).toHaveProperty('authentication');
      expect(connector).toHaveProperty('configuration');
      expect(connector).toHaveProperty('endpoints');
      expect(connector).toHaveProperty('mappings');
      expect(connector).toHaveProperty('events');

      expect(connector.metadata).toHaveProperty('name', 'Test Connector');
      expect(connector.authentication).toHaveProperty('type', 'API_KEY');
      expect(connector.configuration).toHaveProperty('baseUrl', 'http://localhost:3005');
      expect(connector.endpoints).toHaveLength(1);
      expect(connector.endpoints[0]).toHaveProperty('id', 'getFindings');
      expect(connector.mappings).toHaveLength(1);
      expect(connector.mappings[0]).toHaveProperty('sourceEndpoint', 'getFindings');
    });

    it('should override default properties', () => {
      const overrides = {
        metadata: {
          name: 'Custom Connector',
          version: '2.0.0'
        },
        authentication: {
          type: 'OAUTH2'
        }
      };

      const connector = generateTestConnector(overrides);

      expect(connector.metadata).toHaveProperty('name', 'Custom Connector');
      expect(connector.metadata).toHaveProperty('version', '2.0.0');
      expect(connector.authentication).toHaveProperty('type', 'OAUTH2');

      // Other properties should remain default
      expect(connector.configuration).toHaveProperty('baseUrl', 'http://localhost:3005');
    });

    it('should handle deep overrides', () => {
      const overrides = {
        configuration: {
          baseUrl: 'https://api.example.com',
          headers: {
            'X-API-Key': 'test-key'
          }
        },
        endpoints: [
          {
            id: 'customEndpoint',
            name: 'Custom Endpoint',
            path: '/custom',
            method: 'POST'
          }
        ]
      };

      const connector = generateTestConnector(overrides);

      expect(connector.configuration).toHaveProperty('baseUrl', 'https://api.example.com');
      expect(connector.configuration.headers).toHaveProperty('X-API-Key', 'test-key');
      expect(connector.endpoints).toHaveLength(1);
      expect(connector.endpoints[0]).toHaveProperty('id', 'customEndpoint');
      expect(connector.endpoints[0]).toHaveProperty('method', 'POST');
    });
  });

  // Test generateTestCredential
  describe('generateTestCredential', () => {
    it('should generate a default test credential', () => {
      const credential = generateTestCredential();

      expect(credential).toHaveProperty('name', 'Test Credential');
      expect(credential).toHaveProperty('connectorId', 'test-connector-id');
      expect(credential).toHaveProperty('authType', 'API_KEY');
      expect(credential).toHaveProperty('credentials');
      expect(credential.credentials).toHaveProperty('apiKey', 'test-api-key');
      expect(credential).toHaveProperty('userId', 'test-user');
    });

    it('should override default properties', () => {
      const overrides = {
        name: 'Custom Credential',
        connectorId: 'custom-connector-id',
        userId: 'custom-user'
      };

      const credential = generateTestCredential(overrides);

      expect(credential).toHaveProperty('name', 'Custom Credential');
      expect(credential).toHaveProperty('connectorId', 'custom-connector-id');
      expect(credential).toHaveProperty('userId', 'custom-user');

      // Other properties should remain default
      expect(credential).toHaveProperty('authType', 'API_KEY');
      expect(credential.credentials).toHaveProperty('apiKey', 'test-api-key');
    });
  });

  // Test deepMerge
  describe('deepMerge', () => {
    it('should merge two objects', () => {
      const target = { a: 1, b: 2 };
      const source = { b: 3, c: 4 };

      const result = deepMerge(target, source);

      expect(result).toEqual({ a: 1, b: 3, c: 4 });
    });

    it('should handle nested objects', () => {
      const target = { a: 1, b: { x: 1, y: 2 } };
      const source = { b: { y: 3, z: 4 }, c: 5 };

      const result = deepMerge(target, source);

      expect(result).toEqual({ a: 1, b: { x: 1, y: 3, z: 4 }, c: 5 });
    });

    it('should handle arrays', () => {
      const target = { a: [1, 2], b: 2 };
      const source = { a: [3, 4], c: 3 };

      const result = deepMerge(target, source);

      expect(result).toEqual({ a: [3, 4], b: 2, c: 3 });
    });

    it('should handle null and undefined values', () => {
      const target = { a: 1, b: null, c: undefined };
      const source = { b: 2, c: 3, d: null };

      const result = deepMerge(target, source);

      expect(result).toEqual({ a: 1, b: 2, c: 3, d: null });
    });

    it('should handle non-object source', () => {
      const target = { a: 1, b: 2 };
      const source = 'not an object';

      const result = deepMerge(target, source);

      expect(result).toEqual({ a: 1, b: 2 });
    });

    it('should handle non-object target', () => {
      const target = 'not an object';
      const source = { a: 1, b: 2 };

      const result = deepMerge(target, source);

      // Should return a copy of source
      expect(result).toEqual({ a: 1, b: 2 });
      // Should not be the same object reference
      expect(result).not.toBe(source);
    });

    it('should handle new keys in source', () => {
      const target = { a: { x: 1 } };
      const source = { a: { y: 2 }, b: { z: 3 } };

      const result = deepMerge(target, source);

      expect(result).toEqual({ a: { x: 1, y: 2 }, b: { z: 3 } });
    });

    it('should handle source with non-object values in keys that are objects in target', () => {
      const target = { a: { x: 1, y: 2 } };
      const source = { a: 'not an object' };

      const result = deepMerge(target, source);

      expect(result).toEqual({ a: 'not an object' });
    });

    it('should handle null source', () => {
      const target = { a: 1, b: 2 };
      const source = null;

      const result = deepMerge(target, source);

      expect(result).toEqual({ a: 1, b: 2 });
    });

    it('should handle undefined source', () => {
      const target = { a: 1, b: 2 };
      const source = undefined;

      const result = deepMerge(target, source);

      expect(result).toEqual({ a: 1, b: 2 });
    });

    it('should handle null target and valid source', () => {
      const target = null;
      const source = { a: 1, b: 2 };

      const result = deepMerge(target, source);

      expect(result).toEqual({ a: 1, b: 2 });
    });

    it('should handle undefined target and valid source', () => {
      const target = undefined;
      const source = { a: 1, b: 2 };

      const result = deepMerge(target, source);

      expect(result).toEqual({ a: 1, b: 2 });
    });

    it('should handle both null target and source', () => {
      const target = null;
      const source = null;

      const result = deepMerge(target, source);

      expect(result).toEqual({});
    });
  });

  // Test isObject
  describe('isObject', () => {
    it('should return true for objects', () => {
      expect(isObject({})).toBe(true);
      expect(isObject({ a: 1 })).toBe(true);
      expect(isObject(new Object())).toBe(true);
    });

    it('should return false for arrays', () => {
      expect(isObject([])).toBe(false);
      expect(isObject([1, 2, 3])).toBe(false);
      expect(isObject(new Array())).toBe(false);
    });

    it('should return false for null', () => {
      expect(isObject(null)).toBe(false);
    });

    it('should return false for undefined', () => {
      expect(isObject(undefined)).toBe(false);
    });

    it('should return false for primitives', () => {
      expect(isObject(1)).toBe(false);
      expect(isObject('string')).toBe(false);
      expect(isObject(true)).toBe(false);
      expect(isObject(Symbol())).toBe(false);
    });

    it('should return false for functions', () => {
      expect(isObject(function() {})).toBe(false);
      expect(isObject(() => {})).toBe(false);
    });
  });
});
