/**
 * Authentication Audit Controller
 * 
 * This controller handles API requests related to authentication audit logs.
 */

const AuthAuditService = require('../services/AuthAuditService');
const { ValidationError, AuthorizationError } = require('../utils/errors');

class AuthAuditController {
  constructor() {
    this.authAuditService = new AuthAuditService();
  }

  /**
   * Get authentication audit logs
   */
  async getAuthAuditLogs(req, res, next) {
    try {
      // Check if user has admin role
      if (!req.user.role || (req.user.role !== 'admin' && req.user.role !== 'security')) {
        throw new AuthorizationError('Unauthorized: Requires admin or security role');
      }

      // Parse filters from query parameters
      const filters = {
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        userId: req.query.userId,
        action: req.query.action,
        status: req.query.status,
        page: req.query.page ? parseInt(req.query.page) : 1,
        limit: req.query.limit ? parseInt(req.query.limit) : 100
      };

      const logs = await this.authAuditService.getAuthAuditLogs(filters);

      res.json({
        success: true,
        data: logs
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get authentication audit logs for a specific user
   */
  async getUserAuthAuditLogs(req, res, next) {
    try {
      const { userId } = req.params;

      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      // Check if user is requesting their own logs or has admin role
      if (userId !== req.user.id && req.user.role !== 'admin' && req.user.role !== 'security') {
        throw new AuthorizationError('Unauthorized: Can only access your own logs or requires admin/security role');
      }

      // Parse filters from query parameters
      const filters = {
        userId,
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        action: req.query.action,
        status: req.query.status,
        page: req.query.page ? parseInt(req.query.page) : 1,
        limit: req.query.limit ? parseInt(req.query.limit) : 100
      };

      const logs = await this.authAuditService.getAuthAuditLogs(filters);

      res.json({
        success: true,
        data: logs
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get login history for a specific user
   */
  async getUserLoginHistory(req, res, next) {
    try {
      const { userId } = req.params;

      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      // Check if user is requesting their own logs or has admin role
      if (userId !== req.user.id && req.user.role !== 'admin' && req.user.role !== 'security') {
        throw new AuthorizationError('Unauthorized: Can only access your own login history or requires admin/security role');
      }

      // Parse filters from query parameters
      const filters = {
        userId,
        action: 'LOGIN',
        page: req.query.page ? parseInt(req.query.page) : 1,
        limit: req.query.limit ? parseInt(req.query.limit) : 100
      };

      const logs = await this.authAuditService.getAuthAuditLogs(filters);

      res.json({
        success: true,
        data: logs
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get failed login attempts
   */
  async getFailedLoginAttempts(req, res, next) {
    try {
      // Check if user has admin role
      if (!req.user.role || (req.user.role !== 'admin' && req.user.role !== 'security')) {
        throw new AuthorizationError('Unauthorized: Requires admin or security role');
      }

      // Parse filters from query parameters
      const filters = {
        action: 'LOGIN',
        status: 'failure',
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        page: req.query.page ? parseInt(req.query.page) : 1,
        limit: req.query.limit ? parseInt(req.query.limit) : 100
      };

      const logs = await this.authAuditService.getAuthAuditLogs(filters);

      res.json({
        success: true,
        data: logs
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get authentication activity summary
   */
  async getAuthActivitySummary(req, res, next) {
    try {
      // Check if user has admin role
      if (!req.user.role || (req.user.role !== 'admin' && req.user.role !== 'security')) {
        throw new AuthorizationError('Unauthorized: Requires admin or security role');
      }

      // Get all auth logs for the specified time period
      const startDate = req.query.startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(); // Default to last 30 days
      const endDate = req.query.endDate || new Date().toISOString();

      const filters = {
        startDate,
        endDate,
        limit: 10000 // Get a large number of logs for accurate summary
      };

      const logs = await this.authAuditService.getAuthAuditLogs(filters);

      // Calculate summary statistics
      const summary = {
        totalEvents: logs.logs.length,
        successfulLogins: logs.logs.filter(log => log.action === 'LOGIN' && log.status === 'success').length,
        failedLogins: logs.logs.filter(log => log.action === 'LOGIN' && log.status === 'failure').length,
        registrations: logs.logs.filter(log => log.action === 'REGISTER').length,
        logouts: logs.logs.filter(log => log.action === 'LOGOUT').length,
        tokenRefreshes: logs.logs.filter(log => log.action === 'TOKEN_REFRESH').length,
        twoFactorEvents: logs.logs.filter(log => log.action.startsWith('2FA_')).length,
        startDate,
        endDate
      };

      res.json({
        success: true,
        data: summary
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new AuthAuditController();
