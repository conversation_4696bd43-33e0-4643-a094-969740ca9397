<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse UCECS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #0A84FF;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            background-color: #0A84FF;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            margin-top: 10px;
        }
        .btn:hover {
            background-color: #0064D1;
        }
    </style>
</head>
<body>
    <header>
        <h1>NovaFuse UCECS</h1>
        <p>Universal Compliance Evidence Collection System</p>
    </header>
    <div class="container">
        <div class="card">
            <h2>Welcome to NovaFuse UCECS</h2>
            <p>This is a simple static page to verify that the web server is working correctly.</p>
            <p>The full Next.js application is still being set up.</p>
            <a href="#" class="btn">Learn More</a>
        </div>
        
        <div class="card">
            <h2>System Status</h2>
            <p>API Server: <span id="api-status">Checking...</span></p>
            <p>Web Server: <span id="web-status" style="color: green;">Running</span></p>
        </div>
    </div>

    <script>
        // Check API status
        fetch('/api')
            .then(response => {
                if (response.ok) {
                    document.getElementById('api-status').textContent = 'Running';
                    document.getElementById('api-status').style.color = 'green';
                } else {
                    document.getElementById('api-status').textContent = 'Error';
                    document.getElementById('api-status').style.color = 'red';
                }
            })
            .catch(error => {
                document.getElementById('api-status').textContent = 'Not Available';
                document.getElementById('api-status').style.color = 'red';
            });
    </script>
</body>
</html>
