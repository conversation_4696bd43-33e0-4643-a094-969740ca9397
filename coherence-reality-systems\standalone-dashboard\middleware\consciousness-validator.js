const NovaShield = require('../utils/nova-shield');

// Create NovaShield instance
const novaShield = new NovaShield();

// Consciousness validation middleware
const consciousnessValidator = (req, res, next) => {
  try {
    // Protect session
    const protectedSession = novaShield.protectSession(req);
    
    // Add consciousness metrics to request
    req.consciousness = {
      level: protectedSession.consciousness_level,
      ip: protectedSession.ip,
      protected: protectedSession.protected
    };
    
    next();
  } catch (error) {
    // Handle consciousness validation errors
    if (error.message === 'RATE_LIMIT_EXCEEDED') {
      return res.status(429).json({
        error: 'RATE_LIMIT_EXCEEDED',
        message: 'Too many requests from this IP address'
      });
    }
    
    if (error.message === 'CONSCIOUSNESS_THRESHOLD_VIOLATION') {
      return res.status(403).json({
        error: 'CONSCIOUSNESS_THRESHOLD_VIOLATION',
        message: 'Insufficient consciousness level detected',
        required_minimum: novaShield.Ψ_threshold
      });
    }
    
    // Log threat
    novaShield.logThreat({
      type: 'MIDDLEWARE_ERROR',
      source_ip: req.ip,
      error_message: error.message,
      timestamp: new Date().toISOString(),
      action: 'BLOCKED'
    });
    
    return res.status(500).json({
      error: 'INTERNAL_ERROR',
      message: 'Consciousness validation failed'
    });
  }
};

// Export middleware
module.exports = consciousnessValidator;
