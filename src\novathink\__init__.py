"""
NovaThink (NUCI) - Universal Compliance Intelligence.

A centralized repository of compliance information, best practices, and guidance.

Key Differentiation: Explainable AI decision engine
"""

# Import from legacy module for backward compatibility
try:
    from ucto.knowledge import KnowledgeManager, KnowledgeAPI
    has_knowledge = True
except ImportError:
    has_knowledge = False
    KnowledgeManager = None
    KnowledgeAPI = None

__version__ = '0.1.0'
__all__ = []

# Add knowledge components to __all__ if available
if has_knowledge:
    __all__.extend(['KnowledgeManager', 'KnowledgeAPI'])
