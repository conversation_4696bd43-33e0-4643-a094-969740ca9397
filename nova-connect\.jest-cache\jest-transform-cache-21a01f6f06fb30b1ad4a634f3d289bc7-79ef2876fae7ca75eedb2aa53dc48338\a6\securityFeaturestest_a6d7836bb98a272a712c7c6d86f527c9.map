{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "verify", "jest", "fn", "mockImplementation", "token", "secret", "sub", "role", "Error", "sign", "mockReturnValue", "require", "request", "app", "fs", "promises", "path", "RateLimitService", "BruteForceProtectionService", "IpRestrictionService", "AuthAuditService", "AuthService", "describe", "beforeEach", "clearAllMocks", "loadConfig", "mockResolvedValue", "global", "enabled", "windowMs", "max", "auth", "api", "createLimiter", "req", "res", "next", "updateConfig", "checkLoginAttempt", "handleSuccessfulLogin", "handleFailedLogin", "attemptsCount", "maxAttempts", "remainingAttempts", "getConfig", "blockDuration", "isAllowed", "loadRestrictions", "mode", "allowlist", "blocklist", "rules", "addToAllowlist", "addToBlocklist", "logLoginAttempt", "id", "logLogout", "logRegistration", "logTwoFactorAuth", "getAuthAuditLogs", "logs", "total", "page", "limit", "login", "user", "username", "email", "refreshToken", "expiresIn", "logout", "success", "message", "register", "it", "response", "get", "set", "expect", "status", "toBe", "put", "send", "rateLimits", "anonymous", "requests", "period", "post", "ip", "password", "authAuditServiceInstance", "instances", "toHaveBeenCalled", "bruteForceServiceInstance", "mockImplementationOnce", "mockRejectedValue"], "sources": ["securityFeatures.test.js"], "sourcesContent": ["/**\n * Security Features Integration Tests\n */\n\nconst request = require('supertest');\nconst app = require('../../../api/app');\nconst fs = require('fs').promises;\nconst path = require('path');\n\n// Mock services\njest.mock('../../../api/services/RateLimitService');\njest.mock('../../../api/services/BruteForceProtectionService');\njest.mock('../../../api/services/IpRestrictionService');\njest.mock('../../../api/services/AuthAuditService');\njest.mock('../../../api/services/AuthService');\n\n// Import mocked services\nconst RateLimitService = require('../../../api/services/RateLimitService');\nconst BruteForceProtectionService = require('../../../api/services/BruteForceProtectionService');\nconst IpRestrictionService = require('../../../api/services/IpRestrictionService');\nconst AuthAuditService = require('../../../api/services/AuthAuditService');\nconst AuthService = require('../../../api/services/AuthService');\n\n// Mock JWT token verification\njest.mock('jsonwebtoken', () => ({\n  verify: jest.fn().mockImplementation((token, secret) => {\n    if (token === 'valid-admin-token') {\n      return { sub: 'admin-user-id', role: 'admin' };\n    } else if (token === 'valid-user-token') {\n      return { sub: 'regular-user-id', role: 'user' };\n    } else {\n      throw new Error('Invalid token');\n    }\n  }),\n  sign: jest.fn().mockReturnValue('new-token')\n}));\n\ndescribe('Security Features Integration Tests', () => {\n  beforeEach(() => {\n    // Reset mocks\n    jest.clearAllMocks();\n    \n    // Mock RateLimitService\n    RateLimitService.mockImplementation(() => ({\n      loadConfig: jest.fn().mockResolvedValue({\n        global: { enabled: true, windowMs: 60000, max: 100 },\n        auth: { enabled: true, windowMs: 900000, max: 10 },\n        api: { enabled: true, windowMs: 60000, max: 60 }\n      }),\n      createLimiter: jest.fn().mockReturnValue((req, res, next) => next()),\n      updateConfig: jest.fn().mockResolvedValue({})\n    }));\n    \n    // Mock BruteForceProtectionService\n    BruteForceProtectionService.mockImplementation(() => ({\n      checkLoginAttempt: jest.fn().mockResolvedValue(true),\n      handleSuccessfulLogin: jest.fn().mockResolvedValue(true),\n      handleFailedLogin: jest.fn().mockResolvedValue({\n        attemptsCount: 1,\n        maxAttempts: 5,\n        remainingAttempts: 4\n      }),\n      getConfig: jest.fn().mockReturnValue({\n        maxAttempts: 5,\n        windowMs: 900000,\n        blockDuration: 1800000\n      }),\n      updateConfig: jest.fn().mockResolvedValue({})\n    }));\n    \n    // Mock IpRestrictionService\n    IpRestrictionService.mockImplementation(() => ({\n      isAllowed: jest.fn().mockResolvedValue(true),\n      loadRestrictions: jest.fn().mockResolvedValue({\n        enabled: false,\n        mode: 'allowlist',\n        allowlist: [],\n        blocklist: [],\n        rules: []\n      }),\n      addToAllowlist: jest.fn().mockResolvedValue({}),\n      addToBlocklist: jest.fn().mockResolvedValue({}),\n      updateConfig: jest.fn().mockResolvedValue({})\n    }));\n    \n    // Mock AuthAuditService\n    AuthAuditService.mockImplementation(() => ({\n      logLoginAttempt: jest.fn().mockResolvedValue({ id: 'log-id' }),\n      logLogout: jest.fn().mockResolvedValue({ id: 'log-id' }),\n      logRegistration: jest.fn().mockResolvedValue({ id: 'log-id' }),\n      logTwoFactorAuth: jest.fn().mockResolvedValue({ id: 'log-id' }),\n      getAuthAuditLogs: jest.fn().mockResolvedValue({\n        logs: [],\n        total: 0,\n        page: 1,\n        limit: 10\n      })\n    }));\n    \n    // Mock AuthService\n    AuthService.mockImplementation(() => ({\n      login: jest.fn().mockResolvedValue({\n        user: {\n          id: 'user-id',\n          username: 'testuser',\n          email: '<EMAIL>',\n          role: 'user'\n        },\n        token: 'valid-user-token',\n        refreshToken: 'refresh-token',\n        expiresIn: '24h'\n      }),\n      logout: jest.fn().mockResolvedValue({\n        success: true,\n        message: 'Logged out successfully'\n      }),\n      register: jest.fn().mockResolvedValue({\n        id: 'user-id',\n        username: 'testuser',\n        email: '<EMAIL>',\n        role: 'user'\n      })\n    }));\n  });\n  \n  describe('Rate Limiting', () => {\n    it('should allow access to rate limit configuration for admin users', async () => {\n      const response = await request(app)\n        .get('/api/rate-limits')\n        .set('Authorization', 'Bearer valid-admin-token');\n      \n      expect(response.status).toBe(200);\n    });\n    \n    it('should deny access to rate limit configuration for non-admin users', async () => {\n      const response = await request(app)\n        .get('/api/rate-limits')\n        .set('Authorization', 'Bearer valid-user-token');\n      \n      expect(response.status).toBe(403);\n    });\n    \n    it('should allow updating rate limit configuration for admin users', async () => {\n      const response = await request(app)\n        .put('/api/rate-limits')\n        .set('Authorization', 'Bearer valid-admin-token')\n        .send({\n          rateLimits: {\n            anonymous: {\n              requests: 30,\n              period: 60\n            }\n          }\n        });\n      \n      expect(response.status).toBe(200);\n    });\n  });\n  \n  describe('Brute Force Protection', () => {\n    it('should allow access to brute force protection configuration for admin users', async () => {\n      const response = await request(app)\n        .get('/api/brute-force/config')\n        .set('Authorization', 'Bearer valid-admin-token');\n      \n      expect(response.status).toBe(200);\n    });\n    \n    it('should deny access to brute force protection configuration for non-admin users', async () => {\n      const response = await request(app)\n        .get('/api/brute-force/config')\n        .set('Authorization', 'Bearer valid-user-token');\n      \n      expect(response.status).toBe(403);\n    });\n    \n    it('should allow updating brute force protection configuration for admin users', async () => {\n      const response = await request(app)\n        .put('/api/brute-force/config')\n        .set('Authorization', 'Bearer valid-admin-token')\n        .send({\n          maxAttempts: 3,\n          windowMs: 600000,\n          blockDuration: 3600000\n        });\n      \n      expect(response.status).toBe(200);\n    });\n  });\n  \n  describe('IP Restrictions', () => {\n    it('should allow access to IP restrictions configuration for admin users', async () => {\n      const response = await request(app)\n        .get('/api/ip-restrictions')\n        .set('Authorization', 'Bearer valid-admin-token');\n      \n      expect(response.status).toBe(200);\n    });\n    \n    it('should deny access to IP restrictions configuration for non-admin users', async () => {\n      const response = await request(app)\n        .get('/api/ip-restrictions')\n        .set('Authorization', 'Bearer valid-user-token');\n      \n      expect(response.status).toBe(403);\n    });\n    \n    it('should allow adding IP to allowlist for admin users', async () => {\n      const response = await request(app)\n        .post('/api/ip-restrictions/allowlist')\n        .set('Authorization', 'Bearer valid-admin-token')\n        .send({\n          ip: '***********'\n        });\n      \n      expect(response.status).toBe(200);\n    });\n  });\n  \n  describe('Authentication Audit Logging', () => {\n    it('should allow access to auth audit logs for admin users', async () => {\n      const response = await request(app)\n        .get('/api/auth/audit')\n        .set('Authorization', 'Bearer valid-admin-token');\n      \n      expect(response.status).toBe(200);\n    });\n    \n    it('should deny access to auth audit logs for non-admin users', async () => {\n      const response = await request(app)\n        .get('/api/auth/audit')\n        .set('Authorization', 'Bearer valid-user-token');\n      \n      expect(response.status).toBe(403);\n    });\n    \n    it('should allow access to user\\'s own login history', async () => {\n      const response = await request(app)\n        .get('/api/auth/audit/user/regular-user-id/login-history')\n        .set('Authorization', 'Bearer valid-user-token');\n      \n      expect(response.status).toBe(200);\n    });\n    \n    it('should deny access to another user\\'s login history for non-admin users', async () => {\n      const response = await request(app)\n        .get('/api/auth/audit/user/admin-user-id/login-history')\n        .set('Authorization', 'Bearer valid-user-token');\n      \n      expect(response.status).toBe(403);\n    });\n  });\n  \n  describe('Authentication with Security Features', () => {\n    it('should log successful login attempts', async () => {\n      const response = await request(app)\n        .post('/api/auth/login')\n        .send({\n          username: 'testuser',\n          password: 'password123'\n        });\n      \n      expect(response.status).toBe(200);\n      \n      // Verify that login attempt was logged\n      const authAuditServiceInstance = AuthAuditService.mock.instances[0];\n      expect(authAuditServiceInstance.logLoginAttempt).toHaveBeenCalled();\n      \n      // Verify that brute force protection was checked\n      const bruteForceServiceInstance = BruteForceProtectionService.mock.instances[0];\n      expect(bruteForceServiceInstance.checkLoginAttempt).toHaveBeenCalled();\n      expect(bruteForceServiceInstance.handleSuccessfulLogin).toHaveBeenCalled();\n    });\n    \n    it('should log failed login attempts', async () => {\n      // Mock AuthService to simulate failed login\n      AuthService.mockImplementationOnce(() => ({\n        login: jest.fn().mockRejectedValue(new Error('Invalid username or password'))\n      }));\n      \n      const response = await request(app)\n        .post('/api/auth/login')\n        .send({\n          username: 'testuser',\n          password: 'wrongpassword'\n        });\n      \n      expect(response.status).toBe(500); // In a real app, this would be 401\n      \n      // Verify that failed login attempt was handled by brute force protection\n      const bruteForceServiceInstance = BruteForceProtectionService.mock.instances[0];\n      expect(bruteForceServiceInstance.checkLoginAttempt).toHaveBeenCalled();\n      expect(bruteForceServiceInstance.handleFailedLogin).toHaveBeenCalled();\n    });\n  });\n});\n"], "mappings": "AASA;AACAA,WAAA,GAAKC,IAAI,CAAC,wCAAwC,CAAC;AACnDD,WAAA,GAAKC,IAAI,CAAC,mDAAmD,CAAC;AAC9DD,WAAA,GAAKC,IAAI,CAAC,4CAA4C,CAAC;AACvDD,WAAA,GAAKC,IAAI,CAAC,wCAAwC,CAAC;AACnDD,WAAA,GAAKC,IAAI,CAAC,mCAAmC,CAAC;;AAE9C;;AAOA;AACAD,WAAA,GAAKC,IAAI,CAAC,cAAc,EAAE,OAAO;EAC/BC,MAAM,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAACC,KAAK,EAAEC,MAAM,KAAK;IACtD,IAAID,KAAK,KAAK,mBAAmB,EAAE;MACjC,OAAO;QAAEE,GAAG,EAAE,eAAe;QAAEC,IAAI,EAAE;MAAQ,CAAC;IAChD,CAAC,MAAM,IAAIH,KAAK,KAAK,kBAAkB,EAAE;MACvC,OAAO;QAAEE,GAAG,EAAE,iBAAiB;QAAEC,IAAI,EAAE;MAAO,CAAC;IACjD,CAAC,MAAM;MACL,MAAM,IAAIC,KAAK,CAAC,eAAe,CAAC;IAClC;EACF,CAAC,CAAC;EACFC,IAAI,EAAER,IAAI,CAACC,EAAE,CAAC,CAAC,CAACQ,eAAe,CAAC,WAAW;AAC7C,CAAC,CAAC,CAAC;AAAC,SAAAZ,YAAA;EAAA;IAAAG;EAAA,IAAAU,OAAA;EAAAb,WAAA,GAAAA,CAAA,KAAAG,IAAA;EAAA,OAAAA,IAAA;AAAA;AAnCJ;AACA;AACA;;AAEA,MAAMW,OAAO,GAAGD,OAAO,CAAC,WAAW,CAAC;AACpC,MAAME,GAAG,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AACvC,MAAMG,EAAE,GAAGH,OAAO,CAAC,IAAI,CAAC,CAACI,QAAQ;AACjC,MAAMC,IAAI,GAAGL,OAAO,CAAC,MAAM,CAAC;AAU5B,MAAMM,gBAAgB,GAAGN,OAAO,CAAC,wCAAwC,CAAC;AAC1E,MAAMO,2BAA2B,GAAGP,OAAO,CAAC,mDAAmD,CAAC;AAChG,MAAMQ,oBAAoB,GAAGR,OAAO,CAAC,4CAA4C,CAAC;AAClF,MAAMS,gBAAgB,GAAGT,OAAO,CAAC,wCAAwC,CAAC;AAC1E,MAAMU,WAAW,GAAGV,OAAO,CAAC,mCAAmC,CAAC;AAgBhEW,QAAQ,CAAC,qCAAqC,EAAE,MAAM;EACpDC,UAAU,CAAC,MAAM;IACf;IACAtB,IAAI,CAACuB,aAAa,CAAC,CAAC;;IAEpB;IACAP,gBAAgB,CAACd,kBAAkB,CAAC,OAAO;MACzCsB,UAAU,EAAExB,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC;QACtCC,MAAM,EAAE;UAAEC,OAAO,EAAE,IAAI;UAAEC,QAAQ,EAAE,KAAK;UAAEC,GAAG,EAAE;QAAI,CAAC;QACpDC,IAAI,EAAE;UAAEH,OAAO,EAAE,IAAI;UAAEC,QAAQ,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAG,CAAC;QAClDE,GAAG,EAAE;UAAEJ,OAAO,EAAE,IAAI;UAAEC,QAAQ,EAAE,KAAK;UAAEC,GAAG,EAAE;QAAG;MACjD,CAAC,CAAC;MACFG,aAAa,EAAEhC,IAAI,CAACC,EAAE,CAAC,CAAC,CAACQ,eAAe,CAAC,CAACwB,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAKA,IAAI,CAAC,CAAC,CAAC;MACpEC,YAAY,EAAEpC,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;;IAEH;IACAR,2BAA2B,CAACf,kBAAkB,CAAC,OAAO;MACpDmC,iBAAiB,EAAErC,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC,IAAI,CAAC;MACpDa,qBAAqB,EAAEtC,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC,IAAI,CAAC;MACxDc,iBAAiB,EAAEvC,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC;QAC7Ce,aAAa,EAAE,CAAC;QAChBC,WAAW,EAAE,CAAC;QACdC,iBAAiB,EAAE;MACrB,CAAC,CAAC;MACFC,SAAS,EAAE3C,IAAI,CAACC,EAAE,CAAC,CAAC,CAACQ,eAAe,CAAC;QACnCgC,WAAW,EAAE,CAAC;QACdb,QAAQ,EAAE,MAAM;QAChBgB,aAAa,EAAE;MACjB,CAAC,CAAC;MACFR,YAAY,EAAEpC,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;;IAEH;IACAP,oBAAoB,CAAChB,kBAAkB,CAAC,OAAO;MAC7C2C,SAAS,EAAE7C,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC,IAAI,CAAC;MAC5CqB,gBAAgB,EAAE9C,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC;QAC5CE,OAAO,EAAE,KAAK;QACdoB,IAAI,EAAE,WAAW;QACjBC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE;MACT,CAAC,CAAC;MACFC,cAAc,EAAEnD,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC/C2B,cAAc,EAAEpD,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC/CW,YAAY,EAAEpC,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;;IAEH;IACAN,gBAAgB,CAACjB,kBAAkB,CAAC,OAAO;MACzCmD,eAAe,EAAErD,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC;QAAE6B,EAAE,EAAE;MAAS,CAAC,CAAC;MAC9DC,SAAS,EAAEvD,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC;QAAE6B,EAAE,EAAE;MAAS,CAAC,CAAC;MACxDE,eAAe,EAAExD,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC;QAAE6B,EAAE,EAAE;MAAS,CAAC,CAAC;MAC9DG,gBAAgB,EAAEzD,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC;QAAE6B,EAAE,EAAE;MAAS,CAAC,CAAC;MAC/DI,gBAAgB,EAAE1D,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC;QAC5CkC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CAAC;QACRC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC,CAAC;;IAEH;IACA1C,WAAW,CAAClB,kBAAkB,CAAC,OAAO;MACpC6D,KAAK,EAAE/D,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC;QACjCuC,IAAI,EAAE;UACJV,EAAE,EAAE,SAAS;UACbW,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,kBAAkB;UACzB5D,IAAI,EAAE;QACR,CAAC;QACDH,KAAK,EAAE,kBAAkB;QACzBgE,YAAY,EAAE,eAAe;QAC7BC,SAAS,EAAE;MACb,CAAC,CAAC;MACFC,MAAM,EAAErE,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC;QAClC6C,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;MACX,CAAC,CAAC;MACFC,QAAQ,EAAExE,IAAI,CAACC,EAAE,CAAC,CAAC,CAACwB,iBAAiB,CAAC;QACpC6B,EAAE,EAAE,SAAS;QACbW,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAE,kBAAkB;QACzB5D,IAAI,EAAE;MACR,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EAEFe,QAAQ,CAAC,eAAe,EAAE,MAAM;IAC9BoD,EAAE,CAAC,iEAAiE,EAAE,YAAY;MAChF,MAAMC,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChC+D,GAAG,CAAC,kBAAkB,CAAC,CACvBC,GAAG,CAAC,eAAe,EAAE,0BAA0B,CAAC;MAEnDC,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;IAEFN,EAAE,CAAC,oEAAoE,EAAE,YAAY;MACnF,MAAMC,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChC+D,GAAG,CAAC,kBAAkB,CAAC,CACvBC,GAAG,CAAC,eAAe,EAAE,yBAAyB,CAAC;MAElDC,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;IAEFN,EAAE,CAAC,gEAAgE,EAAE,YAAY;MAC/E,MAAMC,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChCoE,GAAG,CAAC,kBAAkB,CAAC,CACvBJ,GAAG,CAAC,eAAe,EAAE,0BAA0B,CAAC,CAChDK,IAAI,CAAC;QACJC,UAAU,EAAE;UACVC,SAAS,EAAE;YACTC,QAAQ,EAAE,EAAE;YACZC,MAAM,EAAE;UACV;QACF;MACF,CAAC,CAAC;MAEJR,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1D,QAAQ,CAAC,wBAAwB,EAAE,MAAM;IACvCoD,EAAE,CAAC,6EAA6E,EAAE,YAAY;MAC5F,MAAMC,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChC+D,GAAG,CAAC,yBAAyB,CAAC,CAC9BC,GAAG,CAAC,eAAe,EAAE,0BAA0B,CAAC;MAEnDC,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;IAEFN,EAAE,CAAC,gFAAgF,EAAE,YAAY;MAC/F,MAAMC,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChC+D,GAAG,CAAC,yBAAyB,CAAC,CAC9BC,GAAG,CAAC,eAAe,EAAE,yBAAyB,CAAC;MAElDC,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;IAEFN,EAAE,CAAC,4EAA4E,EAAE,YAAY;MAC3F,MAAMC,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChCoE,GAAG,CAAC,yBAAyB,CAAC,CAC9BJ,GAAG,CAAC,eAAe,EAAE,0BAA0B,CAAC,CAChDK,IAAI,CAAC;QACJxC,WAAW,EAAE,CAAC;QACdb,QAAQ,EAAE,MAAM;QAChBgB,aAAa,EAAE;MACjB,CAAC,CAAC;MAEJiC,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1D,QAAQ,CAAC,iBAAiB,EAAE,MAAM;IAChCoD,EAAE,CAAC,sEAAsE,EAAE,YAAY;MACrF,MAAMC,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChC+D,GAAG,CAAC,sBAAsB,CAAC,CAC3BC,GAAG,CAAC,eAAe,EAAE,0BAA0B,CAAC;MAEnDC,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;IAEFN,EAAE,CAAC,yEAAyE,EAAE,YAAY;MACxF,MAAMC,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChC+D,GAAG,CAAC,sBAAsB,CAAC,CAC3BC,GAAG,CAAC,eAAe,EAAE,yBAAyB,CAAC;MAElDC,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;IAEFN,EAAE,CAAC,qDAAqD,EAAE,YAAY;MACpE,MAAMC,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChC0E,IAAI,CAAC,gCAAgC,CAAC,CACtCV,GAAG,CAAC,eAAe,EAAE,0BAA0B,CAAC,CAChDK,IAAI,CAAC;QACJM,EAAE,EAAE;MACN,CAAC,CAAC;MAEJV,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1D,QAAQ,CAAC,8BAA8B,EAAE,MAAM;IAC7CoD,EAAE,CAAC,wDAAwD,EAAE,YAAY;MACvE,MAAMC,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChC+D,GAAG,CAAC,iBAAiB,CAAC,CACtBC,GAAG,CAAC,eAAe,EAAE,0BAA0B,CAAC;MAEnDC,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;IAEFN,EAAE,CAAC,2DAA2D,EAAE,YAAY;MAC1E,MAAMC,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChC+D,GAAG,CAAC,iBAAiB,CAAC,CACtBC,GAAG,CAAC,eAAe,EAAE,yBAAyB,CAAC;MAElDC,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;IAEFN,EAAE,CAAC,kDAAkD,EAAE,YAAY;MACjE,MAAMC,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChC+D,GAAG,CAAC,oDAAoD,CAAC,CACzDC,GAAG,CAAC,eAAe,EAAE,yBAAyB,CAAC;MAElDC,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;IAEFN,EAAE,CAAC,yEAAyE,EAAE,YAAY;MACxF,MAAMC,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChC+D,GAAG,CAAC,kDAAkD,CAAC,CACvDC,GAAG,CAAC,eAAe,EAAE,yBAAyB,CAAC;MAElDC,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1D,QAAQ,CAAC,uCAAuC,EAAE,MAAM;IACtDoD,EAAE,CAAC,sCAAsC,EAAE,YAAY;MACrD,MAAMC,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChC0E,IAAI,CAAC,iBAAiB,CAAC,CACvBL,IAAI,CAAC;QACJhB,QAAQ,EAAE,UAAU;QACpBuB,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEJX,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;;MAEjC;MACA,MAAMU,wBAAwB,GAAGtE,gBAAgB,CAACrB,IAAI,CAAC4F,SAAS,CAAC,CAAC,CAAC;MACnEb,MAAM,CAACY,wBAAwB,CAACpC,eAAe,CAAC,CAACsC,gBAAgB,CAAC,CAAC;;MAEnE;MACA,MAAMC,yBAAyB,GAAG3E,2BAA2B,CAACnB,IAAI,CAAC4F,SAAS,CAAC,CAAC,CAAC;MAC/Eb,MAAM,CAACe,yBAAyB,CAACvD,iBAAiB,CAAC,CAACsD,gBAAgB,CAAC,CAAC;MACtEd,MAAM,CAACe,yBAAyB,CAACtD,qBAAqB,CAAC,CAACqD,gBAAgB,CAAC,CAAC;IAC5E,CAAC,CAAC;IAEFlB,EAAE,CAAC,kCAAkC,EAAE,YAAY;MACjD;MACArD,WAAW,CAACyE,sBAAsB,CAAC,OAAO;QACxC9B,KAAK,EAAE/D,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC6F,iBAAiB,CAAC,IAAIvF,KAAK,CAAC,8BAA8B,CAAC;MAC9E,CAAC,CAAC,CAAC;MAEH,MAAMmE,QAAQ,GAAG,MAAM/D,OAAO,CAACC,GAAG,CAAC,CAChC0E,IAAI,CAAC,iBAAiB,CAAC,CACvBL,IAAI,CAAC;QACJhB,QAAQ,EAAE,UAAU;QACpBuB,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEJX,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;MAEnC;MACA,MAAMa,yBAAyB,GAAG3E,2BAA2B,CAACnB,IAAI,CAAC4F,SAAS,CAAC,CAAC,CAAC;MAC/Eb,MAAM,CAACe,yBAAyB,CAACvD,iBAAiB,CAAC,CAACsD,gBAAgB,CAAC,CAAC;MACtEd,MAAM,CAACe,yBAAyB,CAACrD,iBAAiB,CAAC,CAACoD,gBAAgB,CAAC,CAAC;IACxE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}