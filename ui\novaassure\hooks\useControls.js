/**
 * React hook for managing controls
 */

import { useState, useEffect, useCallback } from 'react';
import { controlsApi } from '../utils/api';

export default function useControls() {
  const [controls, setControls] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  // Fetch controls
  const fetchControls = useCallback(async (params = {}) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await controlsApi.getAll({
        page: pagination.page,
        limit: pagination.limit,
        ...params
      });
      
      setControls(response.data.controls);
      setPagination(response.data.pagination);
    } catch (err) {
      setError(err.response?.data?.error || err.message);
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.limit]);

  // Get control by ID
  const getControlById = useCallback(async (id) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await controlsApi.getById(id);
      return response.data;
    } catch (err) {
      setError(err.response?.data?.error || err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Create control
  const createControl = useCallback(async (data) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await controlsApi.create(data);
      await fetchControls();
      return response.data;
    } catch (err) {
      setError(err.response?.data?.error || err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchControls]);

  // Update control
  const updateControl = useCallback(async (id, data) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await controlsApi.update(id, data);
      await fetchControls();
      return response.data;
    } catch (err) {
      setError(err.response?.data?.error || err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchControls]);

  // Delete control
  const deleteControl = useCallback(async (id) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await controlsApi.delete(id);
      await fetchControls();
      return response.data;
    } catch (err) {
      setError(err.response?.data?.error || err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchControls]);

  // Import controls
  const importControls = useCallback(async (formData) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await controlsApi.import(formData);
      await fetchControls();
      return response.data;
    } catch (err) {
      setError(err.response?.data?.error || err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchControls]);

  // Export controls
  const exportControls = useCallback(async (params) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await controlsApi.export(params);
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      
      // Set filename from Content-Disposition header or use default
      const contentDisposition = response.headers['content-disposition'];
      let filename = 'controls.json';
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?(.+)"?/);
        if (filenameMatch && filenameMatch.length === 2) {
          filename = filenameMatch[1];
        }
      }
      
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      return true;
    } catch (err) {
      setError(err.response?.data?.error || err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Get control mapping
  const getControlMapping = useCallback(async (sourceFramework, targetFramework) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await controlsApi.getMapping({
        sourceFramework,
        targetFramework
      });
      
      return response.data;
    } catch (err) {
      setError(err.response?.data?.error || err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Change page
  const changePage = useCallback((page) => {
    setPagination((prev) => ({
      ...prev,
      page
    }));
  }, []);

  // Change limit
  const changeLimit = useCallback((limit) => {
    setPagination((prev) => ({
      ...prev,
      limit,
      page: 1
    }));
  }, []);

  // Load controls on mount and when pagination changes
  useEffect(() => {
    fetchControls();
  }, [fetchControls, pagination.page, pagination.limit]);

  return {
    controls,
    loading,
    error,
    pagination,
    fetchControls,
    getControlById,
    createControl,
    updateControl,
    deleteControl,
    importControls,
    exportControls,
    getControlMapping,
    changePage,
    changeLimit
  };
}
