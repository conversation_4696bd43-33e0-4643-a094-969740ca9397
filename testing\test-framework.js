/**
 * Base Testing Framework
 * 
 * This module provides a base testing framework for the Comphyon system.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');
const assert = require('assert');

/**
 * Test Case class
 */
class TestCase {
  /**
   * Create a new TestCase instance
   * @param {string} name - Test case name
   * @param {Function} testFunction - Test function
   * @param {Object} options - Test options
   */
  constructor(name, testFunction, options = {}) {
    this.name = name;
    this.testFunction = testFunction;
    this.options = options;
    this.result = {
      status: 'pending',
      duration: 0,
      error: null
    };
  }
  
  /**
   * Run the test case
   * @returns {Promise<Object>} - Test result
   */
  async run() {
    this.result.status = 'running';
    
    const startTime = performance.now();
    
    try {
      await this.testFunction();
      this.result.status = 'passed';
    } catch (error) {
      this.result.status = 'failed';
      this.result.error = error.message;
    }
    
    this.result.duration = performance.now() - startTime;
    
    return this.result;
  }
}

/**
 * Test Suite class
 */
class TestSuite {
  /**
   * Create a new TestSuite instance
   * @param {string} name - Test suite name
   * @param {Object} options - Test suite options
   */
  constructor(name, options = {}) {
    this.name = name;
    this.options = options;
    this.testCases = [];
    this.result = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0
    };
  }
  
  /**
   * Add a test case
   * @param {string} name - Test case name
   * @param {Function} testFunction - Test function
   * @param {Object} options - Test options
   * @returns {TestCase} - Test case
   */
  test(name, testFunction, options = {}) {
    const testCase = new TestCase(name, testFunction, options);
    this.testCases.push(testCase);
    return testCase;
  }
  
  /**
   * Run the test suite
   * @returns {Promise<Object>} - Test suite result
   */
  async run() {
    const startTime = performance.now();
    
    this.result.total = this.testCases.length;
    this.result.passed = 0;
    this.result.failed = 0;
    this.result.skipped = 0;
    
    for (const testCase of this.testCases) {
      await testCase.run();
      
      if (testCase.result.status === 'passed') {
        this.result.passed++;
      } else if (testCase.result.status === 'failed') {
        this.result.failed++;
      } else if (testCase.result.status === 'skipped') {
        this.result.skipped++;
      }
    }
    
    this.result.duration = performance.now() - startTime;
    
    return this.result;
  }
}

/**
 * Test Runner class
 */
class TestRunner extends EventEmitter {
  /**
   * Create a new TestRunner instance
   * @param {Object} options - Test runner options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      parallelSuites: false,
      ...options
    };
    
    this.testSuites = [];
    this.result = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
      suites: []
    };
  }
  
  /**
   * Add a test suite
   * @param {TestSuite} testSuite - Test suite
   * @returns {TestRunner} - Test runner
   */
  addSuite(testSuite) {
    this.testSuites.push(testSuite);
    return this;
  }
  
  /**
   * Create a new test suite
   * @param {string} name - Test suite name
   * @param {Object} options - Test suite options
   * @returns {TestSuite} - Test suite
   */
  createSuite(name, options = {}) {
    const testSuite = new TestSuite(name, options);
    this.testSuites.push(testSuite);
    return testSuite;
  }
  
  /**
   * Run all test suites
   * @returns {Promise<Object>} - Test runner result
   */
  async run() {
    const startTime = performance.now();
    
    this.emit('start');
    
    if (this.options.parallelSuites) {
      // Run suites in parallel
      const suitePromises = this.testSuites.map(suite => suite.run());
      await Promise.all(suitePromises);
    } else {
      // Run suites sequentially
      for (const suite of this.testSuites) {
        await suite.run();
      }
    }
    
    // Aggregate results
    this.result.total = 0;
    this.result.passed = 0;
    this.result.failed = 0;
    this.result.skipped = 0;
    this.result.suites = this.testSuites;
    
    for (const suite of this.testSuites) {
      this.result.total += suite.result.total;
      this.result.passed += suite.result.passed;
      this.result.failed += suite.result.failed;
      this.result.skipped += suite.result.skipped;
    }
    
    this.result.duration = performance.now() - startTime;
    
    this.emit('end', this.result);
    
    return this.result;
  }
}

/**
 * Assertions
 */
const assertions = {
  ...assert,
  
  /**
   * Assert that a value is approximately equal to another value
   * @param {number} actual - Actual value
   * @param {number} expected - Expected value
   * @param {number} delta - Allowed delta
   * @param {string} message - Error message
   */
  approximately(actual, expected, delta, message) {
    const diff = Math.abs(actual - expected);
    if (diff > delta) {
      throw new Error(message || `Expected ${actual} to be approximately ${expected} (±${delta})`);
    }
  }
};

module.exports = {
  TestCase,
  TestSuite,
  TestRunner,
  assertions
};
