/**
 * NovaGateway
 * 
 * Main server entry point for the NovaGateway API Gateway.
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { createProxyMiddleware } = require('http-proxy-middleware');
const rateLimit = require('express-rate-limit');
const winston = require('winston');
const config = require('./config');

// Create Express app
const app = express();
const PORT = config.server.port;

// Configure logger
const logger = winston.createLogger({
  level: config.logging.level,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'nova-gateway' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: `${config.logging.directory}/error.log`, level: 'error' }),
    new winston.transports.File({ filename: `${config.logging.directory}/combined.log` })
  ]
});

// Middleware
app.use(helmet());
app.use(cors(config.server.cors));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(morgan('dev'));

// Rate limiting
const apiLimiter = rateLimit(config.rateLimit);

// Apply rate limiting to all API routes
app.use('/api', apiLimiter);

// Health check endpoint
app.get(config.healthCheck.path, (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: require('./package.json').version
  });
});

// API documentation endpoint
app.get(config.documentation.path, (req, res) => {
  res.json({
    title: config.documentation.title,
    version: config.documentation.version,
    description: config.documentation.description,
    services: Object.keys(config.services).map(key => ({
      name: key,
      path: config.services[key].path,
      url: config.services[key].url
    }))
  });
});

// Set up proxy routes for each service
Object.keys(config.services).forEach(key => {
  const service = config.services[key];
  
  app.use(service.path, createProxyMiddleware({
    target: service.url,
    changeOrigin: true,
    pathRewrite: {
      [`^${service.path}`]: '/'
    },
    logLevel: 'debug',
    onProxyReq: (proxyReq, req, res) => {
      logger.debug(`Proxying request to ${service.url}${req.url}`);
    },
    onProxyRes: (proxyRes, req, res) => {
      logger.debug(`Received response from ${service.url}${req.url} with status ${proxyRes.statusCode}`);
    },
    onError: (err, req, res) => {
      logger.error(`Proxy error: ${err.message}`);
      res.status(500).json({
        error: 'Proxy Error',
        message: config.server.env === 'production' ? 'An unexpected error occurred' : err.message
      });
    }
  }));
});

// 404 handler
app.use((req, res) => {
  logger.warn(`404 - Not Found: ${req.method} ${req.url}`);
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found'
  });
});

// Error handler
app.use((err, req, res, next) => {
  logger.error('Unhandled error', { error: err.message, stack: err.stack });
  res.status(500).json({
    error: 'Internal Server Error',
    message: config.server.env === 'production' ? 'An unexpected error occurred' : err.message
  });
});

// Start the server
app.listen(PORT, () => {
  logger.info(`NovaGateway server running on port ${PORT}`);
  console.log(`NovaGateway server running on http://localhost:${PORT}`);
});

module.exports = app;
