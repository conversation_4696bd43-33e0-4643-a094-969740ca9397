/**
 * Market Stress Infusion (ΔΨₘ)
 * 
 * This module implements the Market Stress Infusion component of the CSFE.
 * It models shockwave propagation in financial networks.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * MarketStressInfusion class
 */
class MarketStressInfusion extends EventEmitter {
  /**
   * Create a new MarketStressInfusion instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      updateInterval: 15000, // ms
      enableLogging: true,
      enableMetrics: true,
      historySize: 100, // Number of historical data points to keep
      propagationSpeed: 0.2, // Speed of stress propagation (0-1)
      decayRate: 0.1, // Natural decay rate of stress (0-1)
      thresholds: {
        stress: {
          low: 0.3,
          medium: 0.6,
          high: 0.8,
          critical: 0.95
        }
      },
      ...options
    };
    
    // Initialize state
    this.state = {
      marketStress: 0.2, // Start with low stress
      nodeStress: new Map(), // node id -> stress level
      edgeStress: new Map(), // edge id -> stress level
      stressHistory: [],
      stressStatus: 'low', // low, medium, high, critical
      stressEvents: [], // Significant stress events
      marketGraph: {
        nodes: new Map(), // id -> node object
        edges: new Map() // id -> edge object
      },
      isRunning: false,
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalUpdates: 0,
      stressEventsDetected: 0,
      nodesMonitored: 0,
      edgesMonitored: 0
    };
    
    if (this.options.enableLogging) {
      console.log('MarketStressInfusion initialized');
    }
  }
  
  /**
   * Start the market stress monitor
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('MarketStressInfusion is already running');
      }
      return false;
    }
    
    this.state.isRunning = true;
    this._startUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('MarketStressInfusion started');
    }
    
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the market stress monitor
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('MarketStressInfusion is not running');
      }
      return false;
    }
    
    this.state.isRunning = false;
    this._stopUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('MarketStressInfusion stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Add market node
   * @param {Object} node - Market node object
   * @returns {Object} - Added node
   */
  addNode(node) {
    const startTime = performance.now();
    
    if (!node || typeof node !== 'object') {
      throw new Error('Node must be an object');
    }
    
    if (!node.id) {
      node.id = `node-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    
    // Set default values
    node = {
      type: 'generic', // bank, exchange, broker, etc.
      size: 0.5, // 0-1 (relative size/importance)
      resilience: 0.5, // 0-1 (ability to withstand stress)
      connections: [], // ids of connected nodes
      ...node
    };
    
    // Add to state
    this.state.marketGraph.nodes.set(node.id, node);
    this.state.nodeStress.set(node.id, 0.1); // Start with low stress
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.nodesMonitored++;
    
    // Emit event
    this.emit('node-added', node);
    
    if (this.options.enableLogging) {
      console.log(`MarketStressInfusion: Added ${node.type} node ${node.id}`);
    }
    
    return node;
  }
  
  /**
   * Add market edge (connection between nodes)
   * @param {Object} edge - Market edge object
   * @returns {Object} - Added edge
   */
  addEdge(edge) {
    const startTime = performance.now();
    
    if (!edge || typeof edge !== 'object') {
      throw new Error('Edge must be an object');
    }
    
    if (!edge.id) {
      edge.id = `edge-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    
    if (!edge.source || !edge.target) {
      throw new Error('Edge must have source and target node IDs');
    }
    
    // Check if nodes exist
    if (!this.state.marketGraph.nodes.has(edge.source)) {
      throw new Error(`Source node ${edge.source} not found`);
    }
    
    if (!this.state.marketGraph.nodes.has(edge.target)) {
      throw new Error(`Target node ${edge.target} not found`);
    }
    
    // Set default values
    edge = {
      type: 'generic', // credit, trading, settlement, etc.
      weight: 0.5, // 0-1 (strength of connection)
      bidirectional: true, // Whether stress flows both ways
      ...edge
    };
    
    // Add to state
    this.state.marketGraph.edges.set(edge.id, edge);
    this.state.edgeStress.set(edge.id, 0.1); // Start with low stress
    
    // Update node connections
    const sourceNode = this.state.marketGraph.nodes.get(edge.source);
    const targetNode = this.state.marketGraph.nodes.get(edge.target);
    
    if (!sourceNode.connections.includes(edge.target)) {
      sourceNode.connections.push(edge.target);
    }
    
    if (edge.bidirectional && !targetNode.connections.includes(edge.source)) {
      targetNode.connections.push(edge.source);
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.edgesMonitored++;
    
    // Emit event
    this.emit('edge-added', edge);
    
    if (this.options.enableLogging) {
      console.log(`MarketStressInfusion: Added ${edge.type} edge ${edge.id} from ${edge.source} to ${edge.target}`);
    }
    
    return edge;
  }
  
  /**
   * Inject stress at a node
   * @param {string} nodeId - Node ID
   * @param {number} stressAmount - Amount of stress to inject (0-1)
   * @param {Object} eventData - Additional event data
   * @returns {Object} - Stress event
   */
  injectStress(nodeId, stressAmount, eventData = {}) {
    const startTime = performance.now();
    
    if (!nodeId || !this.state.marketGraph.nodes.has(nodeId)) {
      throw new Error(`Node ${nodeId} not found`);
    }
    
    if (typeof stressAmount !== 'number' || stressAmount < 0 || stressAmount > 1) {
      throw new Error('Stress amount must be a number between 0 and 1');
    }
    
    // Create stress event
    const stressEvent = {
      id: `event-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      nodeId,
      stressAmount,
      timestamp: Date.now(),
      propagated: false,
      ...eventData
    };
    
    // Add to state
    this.state.stressEvents.push(stressEvent);
    
    // Limit events size
    if (this.state.stressEvents.length > this.options.historySize) {
      this.state.stressEvents.shift();
    }
    
    // Update node stress
    const currentStress = this.state.nodeStress.get(nodeId) || 0;
    const newStress = this._clamp(currentStress + stressAmount);
    this.state.nodeStress.set(nodeId, newStress);
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.stressEventsDetected++;
    
    // Propagate stress
    this._propagateStress(nodeId, stressAmount);
    
    // Update market stress
    this._updateMarketStress();
    
    // Emit event
    this.emit('stress-injected', stressEvent);
    
    if (this.options.enableLogging) {
      console.log(`MarketStressInfusion: Injected ${stressAmount.toFixed(2)} stress at node ${nodeId}`);
    }
    
    return stressEvent;
  }
  
  /**
   * Get market stress
   * @returns {number} - Market stress level
   */
  getMarketStress() {
    return this.state.marketStress;
  }
  
  /**
   * Get stress status
   * @returns {string} - Stress status
   */
  getStressStatus() {
    return this.state.stressStatus;
  }
  
  /**
   * Get node stress
   * @param {string} nodeId - Node ID
   * @returns {number} - Node stress level
   */
  getNodeStress(nodeId) {
    if (!nodeId || !this.state.nodeStress.has(nodeId)) {
      throw new Error(`Node ${nodeId} not found`);
    }
    
    return this.state.nodeStress.get(nodeId);
  }
  
  /**
   * Get edge stress
   * @param {string} edgeId - Edge ID
   * @returns {number} - Edge stress level
   */
  getEdgeStress(edgeId) {
    if (!edgeId || !this.state.edgeStress.has(edgeId)) {
      throw new Error(`Edge ${edgeId} not found`);
    }
    
    return this.state.edgeStress.get(edgeId);
  }
  
  /**
   * Get current state
   * @returns {Object} - Current state
   */
  getState() {
    return {
      marketStress: this.state.marketStress,
      stressStatus: this.state.stressStatus,
      nodeCount: this.state.marketGraph.nodes.size,
      edgeCount: this.state.marketGraph.edges.size,
      stressHistory: [...this.state.stressHistory],
      recentEvents: this.state.stressEvents.slice(-5),
      isRunning: this.state.isRunning,
      lastUpdateTime: this.state.lastUpdateTime
    };
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Get stress events
   * @param {number} limit - Maximum number of events to return
   * @returns {Array} - Stress events
   */
  getStressEvents(limit = 10) {
    return this.state.stressEvents.slice(-limit);
  }
  
  /**
   * Get market graph
   * @returns {Object} - Market graph
   */
  getMarketGraph() {
    return {
      nodes: Array.from(this.state.marketGraph.nodes.values()),
      edges: Array.from(this.state.marketGraph.edges.values())
    };
  }
  
  /**
   * Propagate stress from a node
   * @param {string} nodeId - Source node ID
   * @param {number} stressAmount - Amount of stress to propagate
   * @private
   */
  _propagateStress(nodeId, stressAmount) {
    const node = this.state.marketGraph.nodes.get(nodeId);
    
    if (!node) {
      return;
    }
    
    // Get node resilience
    const resilience = node.resilience || 0.5;
    
    // Calculate propagated stress (reduced by resilience)
    const propagatedStress = stressAmount * (1 - resilience) * this.options.propagationSpeed;
    
    // If propagated stress is too small, don't propagate
    if (propagatedStress < 0.01) {
      return;
    }
    
    // Propagate to connected nodes
    for (const connectedNodeId of node.connections) {
      // Find edge between nodes
      const edge = Array.from(this.state.marketGraph.edges.values()).find(e => 
        (e.source === nodeId && e.target === connectedNodeId) ||
        (e.bidirectional && e.target === nodeId && e.source === connectedNodeId)
      );
      
      if (!edge) {
        continue;
      }
      
      // Calculate edge stress
      const edgeWeight = edge.weight || 0.5;
      const edgeStress = propagatedStress * edgeWeight;
      
      // Update edge stress
      const currentEdgeStress = this.state.edgeStress.get(edge.id) || 0;
      this.state.edgeStress.set(edge.id, this._clamp(currentEdgeStress + edgeStress));
      
      // Update connected node stress
      const currentNodeStress = this.state.nodeStress.get(connectedNodeId) || 0;
      this.state.nodeStress.set(connectedNodeId, this._clamp(currentNodeStress + edgeStress));
      
      // Recursively propagate stress (with reduced amount)
      // Only if the propagated stress is significant
      if (edgeStress >= 0.05) {
        this._propagateStress(connectedNodeId, edgeStress);
      }
    }
  }
  
  /**
   * Update market stress
   * @private
   */
  _updateMarketStress() {
    const startTime = performance.now();
    
    // Calculate weighted average of node stress
    let totalStress = 0;
    let totalWeight = 0;
    
    for (const [nodeId, stress] of this.state.nodeStress.entries()) {
      const node = this.state.marketGraph.nodes.get(nodeId);
      const weight = node ? node.size : 0.5;
      
      totalStress += stress * weight;
      totalWeight += weight;
    }
    
    // Calculate market stress
    const marketStress = totalWeight > 0 ? totalStress / totalWeight : 0;
    
    // Apply 18/82 principle
    // 18% weight to edge stress, 82% weight to node stress
    let totalEdgeStress = 0;
    
    for (const stress of this.state.edgeStress.values()) {
      totalEdgeStress += stress;
    }
    
    const avgEdgeStress = this.state.edgeStress.size > 0 ? totalEdgeStress / this.state.edgeStress.size : 0;
    
    const weightedMarketStress = (
      0.18 * avgEdgeStress +
      0.82 * marketStress
    );
    
    // Update state
    this.state.marketStress = this._clamp(weightedMarketStress);
    this.state.lastUpdateTime = Date.now();
    
    // Update stress status
    this._updateStressStatus();
    
    // Add to history
    this.state.stressHistory.push({
      marketStress: this.state.marketStress,
      stressStatus: this.state.stressStatus,
      timestamp: Date.now()
    });
    
    // Limit history size
    if (this.state.stressHistory.length > this.options.historySize) {
      this.state.stressHistory.shift();
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    
    // Emit update event
    this.emit('stress-update', {
      marketStress: this.state.marketStress,
      stressStatus: this.state.stressStatus,
      timestamp: Date.now()
    });
  }
  
  /**
   * Update stress status
   * @private
   */
  _updateStressStatus() {
    const { marketStress } = this.state;
    const { thresholds } = this.options;
    
    let newStatus = 'low';
    
    if (marketStress >= thresholds.stress.critical) {
      newStatus = 'critical';
    } else if (marketStress >= thresholds.stress.high) {
      newStatus = 'high';
    } else if (marketStress >= thresholds.stress.medium) {
      newStatus = 'medium';
    }
    
    // If status changed, emit event
    if (newStatus !== this.state.stressStatus) {
      this.state.stressStatus = newStatus;
      
      // Emit status change event
      this.emit('status-change', {
        stressStatus: this.state.stressStatus,
        timestamp: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`MarketStressInfusion: Stress status changed to ${this.state.stressStatus}`);
      }
    }
  }
  
  /**
   * Apply natural decay to stress levels
   * @private
   */
  _applyStressDecay() {
    // Apply decay to node stress
    for (const [nodeId, stress] of this.state.nodeStress.entries()) {
      const newStress = stress * (1 - this.options.decayRate);
      this.state.nodeStress.set(nodeId, newStress);
    }
    
    // Apply decay to edge stress
    for (const [edgeId, stress] of this.state.edgeStress.entries()) {
      const newStress = stress * (1 - this.options.decayRate);
      this.state.edgeStress.set(edgeId, newStress);
    }
    
    // Update market stress
    this._updateMarketStress();
  }
  
  /**
   * Start update interval
   * @private
   */
  _startUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
    }
    
    this._updateInterval = setInterval(() => {
      if (this.state.isRunning) {
        // Apply natural decay to stress levels
        this._applyStressDecay();
        
        // In a real implementation, this would fetch real-time data
        // For now, just simulate some changes
        this._simulateChanges();
      }
    }, this.options.updateInterval);
  }
  
  /**
   * Stop update interval
   * @private
   */
  _stopUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
      this._updateInterval = null;
    }
  }
  
  /**
   * Simulate changes
   * @private
   */
  _simulateChanges() {
    // Ensure we have some nodes and edges
    if (this.state.marketGraph.nodes.size === 0) {
      this._initializeSimulatedMarket();
      return;
    }
    
    // Simulate random stress events
    const rand = Math.random();
    
    if (rand < 0.2) {
      // Inject stress at a random node
      const nodes = Array.from(this.state.marketGraph.nodes.keys());
      const randomNodeId = nodes[Math.floor(Math.random() * nodes.length)];
      const stressAmount = Math.random() * 0.3; // Random stress amount (0-0.3)
      
      this.injectStress(randomNodeId, stressAmount, {
        type: 'simulated',
        description: 'Simulated stress event'
      });
    }
  }
  
  /**
   * Initialize simulated market
   * @private
   */
  _initializeSimulatedMarket() {
    // Create some nodes
    const nodeTypes = ['bank', 'exchange', 'broker', 'clearing_house', 'market_maker'];
    
    for (let i = 0; i < 10; i++) {
      const nodeType = nodeTypes[Math.floor(Math.random() * nodeTypes.length)];
      
      this.addNode({
        id: `node-${i}`,
        type: nodeType,
        size: Math.random() * 0.5 + 0.3, // Random size (0.3-0.8)
        resilience: Math.random() * 0.7 + 0.2 // Random resilience (0.2-0.9)
      });
    }
    
    // Create some edges
    const edgeTypes = ['credit', 'trading', 'settlement', 'clearing', 'funding'];
    
    for (let i = 0; i < 20; i++) {
      const sourceId = `node-${Math.floor(Math.random() * 10)}`;
      let targetId = `node-${Math.floor(Math.random() * 10)}`;
      
      // Ensure source and target are different
      while (sourceId === targetId) {
        targetId = `node-${Math.floor(Math.random() * 10)}`;
      }
      
      const edgeType = edgeTypes[Math.floor(Math.random() * edgeTypes.length)];
      
      try {
        this.addEdge({
          id: `edge-${i}`,
          source: sourceId,
          target: targetId,
          type: edgeType,
          weight: Math.random() * 0.6 + 0.2, // Random weight (0.2-0.8)
          bidirectional: Math.random() < 0.7 // 70% chance of bidirectional
        });
      } catch (error) {
        // Ignore errors (might happen if trying to add duplicate edge)
      }
    }
  }
  
  /**
   * Clamp value between 0 and 1
   * @param {number} value - Value to clamp
   * @returns {number} - Clamped value
   * @private
   */
  _clamp(value) {
    return Math.max(0, Math.min(1, value));
  }
}

module.exports = MarketStressInfusion;
