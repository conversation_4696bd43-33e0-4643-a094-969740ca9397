/**
 * Validations Index
 * 
 * This file exports all validation schemas for the Privacy Management API.
 */

const dataProcessingActivityValidation = require('./dataProcessingActivityValidation');
const subjectRequestValidation = require('./subjectRequestValidation');
const consentRecordValidation = require('./consentRecordValidation');
const privacyNoticeValidation = require('./privacyNoticeValidation');
const dataBreachValidation = require('./dataBreachValidation');
const integrationValidation = require('./integrationValidation');
const notificationValidation = require('./notificationValidation');
const regulatoryComplianceValidation = require('./regulatoryComplianceValidation');

module.exports = {
  dataProcessingActivityValidation,
  subjectRequestValidation,
  consentRecordValidation,
  privacyNoticeValidation,
  dataBreachValidation,
  integrationValidation,
  notificationValidation,
  regulatoryComplianceValidation
};
