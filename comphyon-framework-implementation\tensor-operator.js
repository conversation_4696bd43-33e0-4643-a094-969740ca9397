/**
 * Tensor Operator
 * 
 * This module implements tensor operations for the Comphyology framework.
 * It provides functionality for tensor products, contractions, and other
 * tensor operations used in the UUFT formula.
 */

/**
 * TensorOperator class
 */
class TensorOperator {
  /**
   * Create a new TensorOperator
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      dimensions: 3, // Default dimensions
      precision: 6, // Decimal precision
      ...options
    };
    
    // Constants
    this.PI = Math.PI;
    this.PI_10_3 = this.PI * 1000;
    this.GOLDEN_RATIO = 1.618033988749895;
    
    console.log(`TensorOperator initialized with ${this.options.dimensions} dimensions`);
  }
  
  /**
   * Calculate tensor product of two vectors
   * @param {Array} a - First vector
   * @param {Array} b - Second vector
   * @returns {Array} - Tensor product result
   */
  product(a, b) {
    if (!Array.isArray(a) || !Array.isArray(b)) {
      throw new Error('Inputs must be arrays');
    }
    
    if (a.length === 0 || b.length === 0) {
      return [];
    }
    
    const result = [];
    
    for (let i = 0; i < a.length; i++) {
      for (let j = 0; j < b.length; j++) {
        result.push(this._round(a[i] * b[j]));
      }
    }
    
    return result;
  }
  
  /**
   * Calculate tensor product of multiple vectors
   * @param {Array} vectors - Array of vectors
   * @returns {Array} - Tensor product result
   */
  multiProduct(vectors) {
    if (!Array.isArray(vectors) || vectors.length === 0) {
      return [];
    }
    
    if (vectors.length === 1) {
      return vectors[0];
    }
    
    let result = this.product(vectors[0], vectors[1]);
    
    for (let i = 2; i < vectors.length; i++) {
      result = this.product(result, vectors[i]);
    }
    
    return result;
  }
  
  /**
   * Calculate outer product of two vectors
   * @param {Array} a - First vector
   * @param {Array} b - Second vector
   * @returns {Array} - Outer product result (matrix)
   */
  outerProduct(a, b) {
    if (!Array.isArray(a) || !Array.isArray(b)) {
      throw new Error('Inputs must be arrays');
    }
    
    if (a.length === 0 || b.length === 0) {
      return [];
    }
    
    const result = [];
    
    for (let i = 0; i < a.length; i++) {
      const row = [];
      for (let j = 0; j < b.length; j++) {
        row.push(this._round(a[i] * b[j]));
      }
      result.push(row);
    }
    
    return result;
  }
  
  /**
   * Calculate inner product of two vectors
   * @param {Array} a - First vector
   * @param {Array} b - Second vector
   * @returns {number} - Inner product result
   */
  innerProduct(a, b) {
    if (!Array.isArray(a) || !Array.isArray(b)) {
      throw new Error('Inputs must be arrays');
    }
    
    if (a.length !== b.length) {
      throw new Error('Vectors must have the same length');
    }
    
    let result = 0;
    
    for (let i = 0; i < a.length; i++) {
      result += a[i] * b[i];
    }
    
    return this._round(result);
  }
  
  /**
   * Calculate tensor contraction
   * @param {Array} tensor - Tensor to contract
   * @param {number} dim1 - First dimension to contract
   * @param {number} dim2 - Second dimension to contract
   * @returns {Array} - Contracted tensor
   */
  contraction(tensor, dim1, dim2) {
    // For simplicity, we'll implement a basic version that works with matrices
    if (!Array.isArray(tensor) || !Array.isArray(tensor[0])) {
      throw new Error('Tensor must be at least 2-dimensional');
    }
    
    if (dim1 === dim2) {
      throw new Error('Dimensions to contract must be different');
    }
    
    // For a matrix, contraction is the trace (sum of diagonal elements)
    if (tensor.length === tensor[0].length) {
      let trace = 0;
      for (let i = 0; i < tensor.length; i++) {
        trace += tensor[i][i];
      }
      return this._round(trace);
    }
    
    // For higher-dimensional tensors, we would need a more complex implementation
    throw new Error('Contraction for higher-dimensional tensors not implemented');
  }
  
  /**
   * Apply 18/82 principle to tensor
   * @param {Array} tensor - Input tensor
   * @param {number} minorWeight - Weight for minor component (default: 0.18)
   * @param {number} majorWeight - Weight for major component (default: 0.82)
   * @returns {Array} - Weighted tensor
   */
  apply1882Principle(tensor, minorWeight = 0.18, majorWeight = 0.82) {
    if (!Array.isArray(tensor)) {
      throw new Error('Input must be an array');
    }
    
    if (tensor.length === 0) {
      return [];
    }
    
    // For a vector, split into minor (first 18%) and major (last 82%) components
    const minorCount = Math.max(1, Math.round(tensor.length * minorWeight));
    const minorPart = tensor.slice(0, minorCount);
    const majorPart = tensor.slice(minorCount);
    
    // Calculate weighted average of minor and major parts
    const minorAvg = minorPart.reduce((sum, val) => sum + val, 0) / minorPart.length;
    const majorAvg = majorPart.reduce((sum, val) => sum + val, 0) / majorPart.length;
    
    const weightedResult = this._round(minorWeight * minorAvg + majorWeight * majorAvg);
    
    return weightedResult;
  }
  
  /**
   * Apply circular trust topology factor
   * @param {Array} tensor - Input tensor
   * @returns {Array} - Tensor with circular trust factor applied
   */
  applyCircularTrustFactor(tensor) {
    if (!Array.isArray(tensor)) {
      throw new Error('Input must be an array');
    }
    
    if (tensor.length === 0) {
      return [];
    }
    
    // For a vector, multiply each element by PI_10_3
    if (!Array.isArray(tensor[0])) {
      return tensor.map(val => this._round(val * this.PI_10_3));
    }
    
    // For a matrix, multiply each element by PI_10_3
    return tensor.map(row => row.map(val => this._round(val * this.PI_10_3)));
  }
  
  /**
   * Round number to specified precision
   * @param {number} value - Value to round
   * @returns {number} - Rounded value
   * @private
   */
  _round(value) {
    const factor = Math.pow(10, this.options.precision);
    return Math.round(value * factor) / factor;
  }
}

module.exports = TensorOperator;
