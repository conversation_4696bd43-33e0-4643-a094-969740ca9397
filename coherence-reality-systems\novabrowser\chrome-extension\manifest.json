{"manifest_version": 3, "name": "NovaBrowser - Coherence Gateway", "version": "1.0.0", "description": "Real-time consciousness validation and accessibility auto-remediation for any website", "permissions": ["activeTab", "storage", "scripting"], "host_permissions": ["<all_urls>", "http://localhost:8090/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content-script.js"], "css": ["overlay.css"], "run_at": "document_idle"}], "action": {"default_popup": "popup.html", "default_title": "NovaBrowser Analysis", "default_icon": {"16": "icon16.png", "32": "icon32.png", "48": "icon48.png", "128": "icon128.png"}}, "icons": {"16": "icon16.png", "32": "icon32.png", "48": "icon48.png", "128": "icon128.png"}}