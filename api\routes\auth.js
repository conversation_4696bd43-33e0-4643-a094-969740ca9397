const express = require('express');
const router = express.Router();
const { validateRegistration, validateLogin } = require('../middleware/validators');
const { authenticateJWT } = require('../middleware/auth');

// Import controllers
const authController = require('../controllers/authController');

/**
 * @route POST /api/auth/register
 * @desc Register a new user
 * @access Public
 */
router.post('/register', validateRegistration, authController.register);

/**
 * @route POST /api/auth/login
 * @desc Login a user
 * @access Public
 */
router.post('/login', validateLogin, authController.login);

/**
 * @route POST /api/auth/refresh
 * @desc Refresh access token using refresh token
 * @access Public (with refresh token)
 */
router.post('/refresh', authController.refreshToken);

/**
 * @route POST /api/auth/logout
 * @desc Logout a user (invalidate refresh token)
 * @access Private
 */
router.post('/logout', authenticateJWT, authController.logout);

/**
 * @route GET /api/auth/me
 * @desc Get current user profile
 * @access Private
 */
router.get('/me', authenticateJWT, authController.getCurrentUser);

/**
 * @route POST /api/auth/forgot-password
 * @desc Request password reset
 * @access Public
 */
router.post('/forgot-password', authController.forgotPassword);

/**
 * @route POST /api/auth/reset-password
 * @desc Reset password with token
 * @access Public (with reset token)
 */
router.post('/reset-password', authController.resetPassword);

/**
 * @route POST /api/auth/verify-email
 * @desc Verify email address
 * @access Public (with verification token)
 */
router.post('/verify-email', authController.verifyEmail);

module.exports = router;
