/**
 * Package Controller
 * 
 * This controller handles package management operations.
 */

const FeatureFlagService = require('../services/FeatureFlagService');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');

// Initialize services
const featureFlagService = new FeatureFlagService();

/**
 * Get all packages
 */
const getAllPackages = async (req, res, next) => {
  try {
    const packages = await featureFlagService.getAllPackages();
    res.json(packages);
  } catch (error) {
    next(error);
  }
};

/**
 * Get package by ID
 */
const getPackageById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const pkg = await featureFlagService.getPackageById(id);
    res.json(pkg);
  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Not Found',
        message: error.message
      });
    }
    next(error);
  }
};

/**
 * Create a new package
 */
const createPackage = async (req, res, next) => {
  try {
    const packageData = req.body;
    
    // Validate required fields
    if (!packageData.id) {
      throw new ValidationError('Package ID is required');
    }
    
    if (!packageData.name) {
      throw new ValidationError('Package name is required');
    }
    
    if (!packageData.tier) {
      throw new ValidationError('Package tier is required');
    }
    
    if (!packageData.features || !Array.isArray(packageData.features)) {
      throw new ValidationError('Package features must be an array');
    }
    
    if (!packageData.limits || typeof packageData.limits !== 'object') {
      throw new ValidationError('Package limits must be an object');
    }
    
    const newPackage = await featureFlagService.createPackage(packageData);
    res.status(201).json(newPackage);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.message
      });
    }
    next(error);
  }
};

/**
 * Update a package
 */
const updatePackage = async (req, res, next) => {
  try {
    const { id } = req.params;
    const packageData = req.body;
    
    const updatedPackage = await featureFlagService.updatePackage(id, packageData);
    res.json(updatedPackage);
  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Not Found',
        message: error.message
      });
    }
    next(error);
  }
};

/**
 * Delete a package
 */
const deletePackage = async (req, res, next) => {
  try {
    const { id } = req.params;
    await featureFlagService.deletePackage(id);
    res.status(204).end();
  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Not Found',
        message: error.message
      });
    }
    next(error);
  }
};

/**
 * Get tenant package
 */
const getTenantPackage = async (req, res, next) => {
  try {
    const { tenantId } = req.params;
    const pkg = await featureFlagService.getTenantPackage(tenantId);
    res.json(pkg);
  } catch (error) {
    next(error);
  }
};

/**
 * Set tenant package
 */
const setTenantPackage = async (req, res, next) => {
  try {
    const { tenantId } = req.params;
    const { packageId, customFeatures, customLimits } = req.body;
    
    // Validate required fields
    if (!packageId) {
      throw new ValidationError('Package ID is required');
    }
    
    const mapping = await featureFlagService.setTenantPackage(
      tenantId,
      packageId,
      customFeatures || [],
      customLimits || {}
    );
    
    res.json(mapping);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.message
      });
    }
    next(error);
  }
};

/**
 * Clear cache
 */
const clearCache = async (req, res, next) => {
  try {
    featureFlagService.clearCache();
    res.json({ message: 'Cache cleared successfully' });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllPackages,
  getPackageById,
  createPackage,
  updatePackage,
  deletePackage,
  getTenantPackage,
  setTenantPackage,
  clearCache
};
