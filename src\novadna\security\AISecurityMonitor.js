/**
 * AISecurityMonitor.js
 * 
 * This module provides AI-based security monitoring for NovaDNA.
 * It detects anomalies, identifies potential security threats,
 * and provides automated incident reporting.
 */

const EventEmitter = require('events');
const { v4: uuidv4 } = require('uuid');

/**
 * AISecurityMonitor class for AI-based security monitoring
 */
class AISecurityMonitor extends EventEmitter {
  constructor(options = {}) {
    super();
    this.anomalyThreshold = options.anomalyThreshold || 0.7;
    this.learningEnabled = options.learningEnabled !== false;
    this.monitoringInterval = options.monitoringInterval || 60000; // 1 minute
    this.maxIncidentHistory = options.maxIncidentHistory || 1000;
    this.accessPatterns = new Map();
    this.incidents = [];
    this.threatModels = this._initializeThreatModels();
    
    // Start monitoring if enabled
    if (options.autoStart !== false) {
      this.startMonitoring();
    }
  }

  /**
   * Start security monitoring
   */
  startMonitoring() {
    if (this.monitoringIntervalId) {
      return;
    }
    
    this.monitoringIntervalId = setInterval(() => {
      this._runSecurityScan();
    }, this.monitoringInterval);
    
    this.emit('monitoring:started');
  }

  /**
   * Stop security monitoring
   */
  stopMonitoring() {
    if (this.monitoringIntervalId) {
      clearInterval(this.monitoringIntervalId);
      this.monitoringIntervalId = null;
      this.emit('monitoring:stopped');
    }
  }

  /**
   * Track an access event
   * @param {Object} accessEvent - The access event to track
   */
  trackAccessEvent(accessEvent) {
    if (!accessEvent.profileId || !accessEvent.accessType) {
      throw new Error('Access event must include profileId and accessType');
    }
    
    const eventId = uuidv4();
    const timestamp = accessEvent.timestamp || new Date().toISOString();
    
    const event = {
      eventId,
      timestamp,
      ...accessEvent
    };
    
    // Store in access patterns
    const profileId = accessEvent.profileId;
    if (!this.accessPatterns.has(profileId)) {
      this.accessPatterns.set(profileId, []);
    }
    
    const profileEvents = this.accessPatterns.get(profileId);
    profileEvents.push(event);
    
    // Limit the number of stored events
    if (profileEvents.length > 100) {
      profileEvents.shift();
    }
    
    // Check for immediate threats
    const anomalyScore = this._calculateAnomalyScore(event, profileEvents);
    
    if (anomalyScore > this.anomalyThreshold) {
      const incident = this._createSecurityIncident(
        'ANOMALOUS_ACCESS',
        `Anomalous access detected for profile ${profileId}`,
        {
          profileId,
          eventId,
          anomalyScore,
          event
        }
      );
      
      this.emit('anomaly:detected', {
        incident,
        event,
        anomalyScore
      });
    }
    
    return {
      eventId,
      anomalyScore
    };
  }

  /**
   * Track an authentication event
   * @param {Object} authEvent - The authentication event to track
   */
  trackAuthEvent(authEvent) {
    if (!authEvent.serviceId) {
      throw new Error('Auth event must include serviceId');
    }
    
    const eventId = uuidv4();
    const timestamp = authEvent.timestamp || new Date().toISOString();
    
    const event = {
      eventId,
      timestamp,
      eventType: 'AUTH',
      ...authEvent
    };
    
    // Check for authentication-based threats
    const threatDetected = this._detectAuthThreat(event);
    
    if (threatDetected) {
      const incident = this._createSecurityIncident(
        'SUSPICIOUS_AUTH',
        `Suspicious authentication detected for service ${authEvent.serviceId}`,
        {
          serviceId: authEvent.serviceId,
          eventId,
          event
        }
      );
      
      this.emit('threat:detected', {
        incident,
        event
      });
    }
    
    return {
      eventId,
      threatDetected
    };
  }

  /**
   * Get security incidents
   * @param {Object} options - Options for filtering incidents
   * @returns {Array} - The security incidents
   */
  getSecurityIncidents(options = {}) {
    const { startDate, endDate, severity, type, limit } = options;
    
    let filteredIncidents = [...this.incidents];
    
    if (startDate) {
      const startTimestamp = new Date(startDate).getTime();
      filteredIncidents = filteredIncidents.filter(incident => 
        new Date(incident.timestamp).getTime() >= startTimestamp
      );
    }
    
    if (endDate) {
      const endTimestamp = new Date(endDate).getTime();
      filteredIncidents = filteredIncidents.filter(incident => 
        new Date(incident.timestamp).getTime() <= endTimestamp
      );
    }
    
    if (severity) {
      filteredIncidents = filteredIncidents.filter(incident => 
        incident.severity === severity
      );
    }
    
    if (type) {
      filteredIncidents = filteredIncidents.filter(incident => 
        incident.type === type
      );
    }
    
    // Sort by timestamp (newest first)
    filteredIncidents.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
    
    if (limit) {
      filteredIncidents = filteredIncidents.slice(0, limit);
    }
    
    return filteredIncidents;
  }

  /**
   * Get access patterns for a profile
   * @param {String} profileId - The profile ID
   * @returns {Array} - The access patterns
   */
  getProfileAccessPatterns(profileId) {
    return this.accessPatterns.get(profileId) || [];
  }

  /**
   * Update the security model with new data
   * @param {Object} trainingData - The training data
   * @returns {Object} - The training result
   */
  updateSecurityModel(trainingData) {
    if (!this.learningEnabled) {
      return {
        success: false,
        error: 'Learning is disabled'
      };
    }
    
    // In a real implementation, this would update an ML model
    // For now, we'll just update our threat models
    
    if (trainingData.threatPatterns) {
      for (const [threatType, patterns] of Object.entries(trainingData.threatPatterns)) {
        if (this.threatModels.has(threatType)) {
          const currentPatterns = this.threatModels.get(threatType);
          this.threatModels.set(threatType, [...currentPatterns, ...patterns]);
        } else {
          this.threatModels.set(threatType, patterns);
        }
      }
    }
    
    if (trainingData.anomalyThreshold) {
      this.anomalyThreshold = trainingData.anomalyThreshold;
    }
    
    this.emit('model:updated');
    
    return {
      success: true,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Initialize threat models
   * @returns {Map} - The initialized threat models
   * @private
   */
  _initializeThreatModels() {
    const threatModels = new Map();
    
    // Brute force attack patterns
    threatModels.set('BRUTE_FORCE', [
      {
        timeWindow: 300000, // 5 minutes
        minAttempts: 10,
        maxSuccessRate: 0.3
      }
    ]);
    
    // Unauthorized access patterns
    threatModels.set('UNAUTHORIZED_ACCESS', [
      {
        indicators: ['unusual_location', 'unusual_time', 'unusual_device'],
        minIndicators: 2
      }
    ]);
    
    // Data exfiltration patterns
    threatModels.set('DATA_EXFILTRATION', [
      {
        timeWindow: 3600000, // 1 hour
        minProfiles: 5,
        accessType: 'full'
      }
    ]);
    
    return threatModels;
  }

  /**
   * Run a security scan
   * @private
   */
  _runSecurityScan() {
    // Check for brute force attacks
    this._detectBruteForceAttacks();
    
    // Check for data exfiltration
    this._detectDataExfiltration();
    
    // Check for unusual access patterns
    this._detectUnusualAccessPatterns();
    
    this.emit('scan:completed');
  }

  /**
   * Detect brute force attacks
   * @private
   */
  _detectBruteForceAttacks() {
    // Get auth events from the last 5 minutes
    const now = Date.now();
    const fiveMinutesAgo = now - 300000;
    
    // Group auth events by service
    const serviceAuthEvents = new Map();
    
    // In a real implementation, this would query a database
    // For now, we'll just simulate it
    
    // Check against threat model
    const bruteForceModel = this.threatModels.get('BRUTE_FORCE')[0];
    
    for (const [serviceId, events] of serviceAuthEvents.entries()) {
      const recentEvents = events.filter(event => 
        new Date(event.timestamp).getTime() >= fiveMinutesAgo
      );
      
      if (recentEvents.length >= bruteForceModel.minAttempts) {
        const successfulEvents = recentEvents.filter(event => event.success);
        const successRate = successfulEvents.length / recentEvents.length;
        
        if (successRate <= bruteForceModel.maxSuccessRate) {
          const incident = this._createSecurityIncident(
            'BRUTE_FORCE',
            `Possible brute force attack detected for service ${serviceId}`,
            {
              serviceId,
              attempts: recentEvents.length,
              successRate
            }
          );
          
          this.emit('threat:detected', { incident });
        }
      }
    }
  }

  /**
   * Detect data exfiltration
   * @private
   */
  _detectDataExfiltration() {
    // Get access events from the last hour
    const now = Date.now();
    const oneHourAgo = now - 3600000;
    
    // Group access events by service/user
    const serviceAccessEvents = new Map();
    
    // In a real implementation, this would query a database
    // For now, we'll just simulate it
    
    // Check against threat model
    const exfiltrationModel = this.threatModels.get('DATA_EXFILTRATION')[0];
    
    for (const [serviceId, events] of serviceAccessEvents.entries()) {
      const recentEvents = events.filter(event => 
        new Date(event.timestamp).getTime() >= oneHourAgo &&
        event.accessType === exfiltrationModel.accessType
      );
      
      // Count unique profiles accessed
      const uniqueProfiles = new Set(recentEvents.map(event => event.profileId));
      
      if (uniqueProfiles.size >= exfiltrationModel.minProfiles) {
        const incident = this._createSecurityIncident(
          'DATA_EXFILTRATION',
          `Possible data exfiltration detected for service ${serviceId}`,
          {
            serviceId,
            profilesAccessed: uniqueProfiles.size,
            accessEvents: recentEvents.length
          },
          'HIGH'
        );
        
        this.emit('threat:detected', { incident });
      }
    }
  }

  /**
   * Detect unusual access patterns
   * @private
   */
  _detectUnusualAccessPatterns() {
    // In a real implementation, this would use ML for anomaly detection
    // For now, we'll just use a simple heuristic approach
    
    for (const [profileId, events] of this.accessPatterns.entries()) {
      // Get recent events
      const now = Date.now();
      const oneDayAgo = now - 86400000; // 24 hours
      
      const recentEvents = events.filter(event => 
        new Date(event.timestamp).getTime() >= oneDayAgo
      );
      
      if (recentEvents.length < 3) {
        continue; // Not enough data
      }
      
      // Check for unusual patterns
      const unusualEvents = [];
      
      for (const event of recentEvents) {
        const anomalyScore = this._calculateAnomalyScore(event, events);
        
        if (anomalyScore > this.anomalyThreshold) {
          unusualEvents.push({
            event,
            anomalyScore
          });
        }
      }
      
      if (unusualEvents.length >= 3) {
        const incident = this._createSecurityIncident(
          'UNUSUAL_ACCESS_PATTERN',
          `Unusual access pattern detected for profile ${profileId}`,
          {
            profileId,
            unusualEvents: unusualEvents.length,
            totalEvents: recentEvents.length,
            events: unusualEvents.map(e => e.event.eventId)
          }
        );
        
        this.emit('threat:detected', { incident });
      }
    }
  }

  /**
   * Calculate anomaly score for an access event
   * @param {Object} event - The event to check
   * @param {Array} profileEvents - Previous events for the profile
   * @returns {Number} - The anomaly score (0-1)
   * @private
   */
  _calculateAnomalyScore(event, profileEvents) {
    // In a real implementation, this would use ML for anomaly detection
    // For now, we'll implement a simple heuristic-based approach
    
    let anomalyScore = 0;
    
    // Check for unusual location
    if (event.location) {
      const usualLocations = new Set(
        profileEvents
          .filter(e => e.location && e.eventId !== event.eventId)
          .map(e => e.location.city || e.location.region || e.location.country)
      );
      
      const currentLocation = event.location.city || event.location.region || event.location.country;
      
      if (currentLocation && !usualLocations.has(currentLocation)) {
        anomalyScore += 0.3;
      }
    }
    
    // Check for unusual device
    if (event.device) {
      const usualDevices = new Set(
        profileEvents
          .filter(e => e.device && e.eventId !== event.eventId)
          .map(e => e.device.type || e.device.model || e.device.id)
      );
      
      const currentDevice = event.device.type || event.device.model || event.device.id;
      
      if (currentDevice && !usualDevices.has(currentDevice)) {
        anomalyScore += 0.2;
      }
    }
    
    // Check for unusual time
    if (event.timestamp) {
      const eventHours = profileEvents
        .filter(e => e.timestamp && e.eventId !== event.eventId)
        .map(e => new Date(e.timestamp).getHours());
      
      const currentHour = new Date(event.timestamp).getHours();
      const unusualTime = !eventHours.some(hour => Math.abs(hour - currentHour) <= 2);
      
      if (unusualTime && eventHours.length > 0) {
        anomalyScore += 0.2;
      }
    }
    
    // Check for unusual access type
    if (event.accessType) {
      const usualAccessTypes = new Set(
        profileEvents
          .filter(e => e.accessType && e.eventId !== event.eventId)
          .map(e => e.accessType)
      );
      
      if (!usualAccessTypes.has(event.accessType) && usualAccessTypes.size > 0) {
        anomalyScore += 0.2;
      }
    }
    
    // Check for high frequency of access
    const recentEvents = profileEvents.filter(e => 
      e.timestamp && 
      e.eventId !== event.eventId &&
      new Date(e.timestamp).getTime() > new Date(event.timestamp).getTime() - 3600000 // Last hour
    );
    
    if (recentEvents.length > 5) { // More than 5 accesses in an hour
      anomalyScore += 0.1;
    }
    
    return Math.min(anomalyScore, 1.0);
  }

  /**
   * Detect authentication-based threats
   * @param {Object} authEvent - The auth event to check
   * @returns {Boolean} - Whether a threat was detected
   * @private
   */
  _detectAuthThreat(authEvent) {
    // In a real implementation, this would use more sophisticated detection
    // For now, we'll implement a simple check
    
    // Check for failed authentication
    if (authEvent.success === false) {
      // Check for multiple failed attempts
      // In a real implementation, this would query a database
      // For now, we'll just return false
      return false;
    }
    
    // Check for unusual authentication patterns
    if (authEvent.context) {
      const unusualFactors = [];
      
      if (authEvent.context.location && authEvent.context.location.unusual) {
        unusualFactors.push('location');
      }
      
      if (authEvent.context.device && authEvent.context.device.unusual) {
        unusualFactors.push('device');
      }
      
      if (authEvent.context.time && authEvent.context.time.unusual) {
        unusualFactors.push('time');
      }
      
      if (unusualFactors.length >= 2) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * Create a security incident
   * @param {String} type - The incident type
   * @param {String} description - The incident description
   * @param {Object} details - The incident details
   * @param {String} severity - The incident severity
   * @returns {Object} - The created incident
   * @private
   */
  _createSecurityIncident(type, description, details, severity = 'MEDIUM') {
    const incidentId = uuidv4();
    const timestamp = new Date().toISOString();
    
    const incident = {
      incidentId,
      type,
      description,
      details,
      severity,
      timestamp,
      status: 'OPEN'
    };
    
    this.incidents.push(incident);
    
    // Limit the number of stored incidents
    if (this.incidents.length > this.maxIncidentHistory) {
      this.incidents.shift();
    }
    
    return incident;
  }
}

module.exports = AISecurityMonitor;
