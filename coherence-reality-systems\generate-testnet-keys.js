const { generate<PERSON>rivate<PERSON><PERSON>, get<PERSON><PERSON><PERSON><PERSON><PERSON>, getAddress } = require('./crypto');
const fs = require('fs');
const path = require('path');

// Number of validator nodes
const NODE_COUNT = 3;

// Generate keys for each node
const nodes = Array.from({ length: NODE_COUNT }, (_, i) => {
  const privateKey = generatePrivateKey();
  const publicKey = getPublicKey(privateKey);
  const address = getAddress(publicKey);
  
  return {
    id: i + 1,
    privateKey,
    publicKey,
    address
  };
});

// Generate .env file content
let envContent = `# KetherNet Testnet Configuration\n`;
nodes.forEach((node, index) => {
  envContent += `\n# Node ${node.id}\n`;
  envContent += `VALIDATOR${node.id}_PRIVATE_KEY=${node.privateKey}\n`;
  envContent += `VALIDATOR${node.id}_ADDRESS=${node.address}\n`;
});

// Add common configuration
envContent += `\n# Common Configuration\n`;
envContent += `NETWORK_ID=2025\n`;
envContent += `CHAIN_ID=2025\n`;
envContent += `NETWORK=testnet\n`;

// Write to .env file
const envPath = path.join(__dirname, '.env');
fs.writeFileSync(envPath, envContent);

// Generate peer information
console.log('✅ Generated .env file with testnet configuration');
console.log('\nValidator Information:');
nodes.forEach(node => {
  console.log(`\nNode ${node.id}:`);
  console.log(`  Address:    ${node.address}`);
  console.log(`  Public Key: ${node.publicKey}`);
  console.log(`  Private Key: ${node.privateKey}`);
});

console.log('\nAdd these environment variables to your shell or .env file before starting the testnet.');
console.log('To start the testnet, run: docker-compose -f docker-compose.testnet.yml up --build');
