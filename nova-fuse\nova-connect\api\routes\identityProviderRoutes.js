/**
 * Identity Provider Routes
 */

const express = require('express');
const router = express.Router();
const IdentityProviderController = require('../controllers/IdentityProviderController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// All routes require authentication and admin permission
router.use(authenticate);
router.use(hasPermission('system:settings'));

// Get all identity providers
router.get('/', (req, res, next) => {
  IdentityProviderController.getAllProviders(req, res, next);
});

// Create a new identity provider
router.post('/', (req, res, next) => {
  IdentityProviderController.createProvider(req, res, next);
});

// Get identity provider by ID
router.get('/:id', (req, res, next) => {
  IdentityProviderController.getProviderById(req, res, next);
});

// Update an identity provider
router.put('/:id', (req, res, next) => {
  IdentityProviderController.updateProvider(req, res, next);
});

// Delete an identity provider
router.delete('/:id', (req, res, next) => {
  IdentityProviderController.deleteProvider(req, res, next);
});

// Test an identity provider connection
router.post('/:id/test', (req, res, next) => {
  IdentityProviderController.testProviderConnection(req, res, next);
});

// Generate SAML metadata
router.get('/:id/saml/metadata', (req, res, next) => {
  IdentityProviderController.generateSamlMetadata(req, res, next);
});

// Get OIDC configuration
router.get('/:id/oidc/configuration', (req, res, next) => {
  IdentityProviderController.getOidcConfiguration(req, res, next);
});

module.exports = router;
