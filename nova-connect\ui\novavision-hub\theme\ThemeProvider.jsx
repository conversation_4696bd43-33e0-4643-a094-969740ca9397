/**
 * Theme Provider Component
 * 
 * This component provides theme context and applies theme styles to the application.
 */

import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { ThemeProvider as ThemeContextProvider, useTheme } from './ThemeContext';
import { defaultTheme } from './themes';

/**
 * Theme Applier component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @returns {React.ReactElement} Theme Applier component
 */
const ThemeApplier = ({ children }) => {
  const { theme, colorMode } = useTheme();
  
  // Apply theme to CSS variables
  useEffect(() => {
    if (!theme) return;
    
    // Get root element
    const root = document.documentElement;
    
    // Apply color variables
    if (theme.colors) {
      Object.entries(theme.colors).forEach(([key, value]) => {
        root.style.setProperty(`--color-${key}`, value);
      });
    }
    
    // Apply typography variables
    if (theme.typography) {
      // Font family
      if (theme.typography.fontFamily) {
        root.style.setProperty('--font-family', theme.typography.fontFamily);
      }
      
      if (theme.typography.fontFamilyCode) {
        root.style.setProperty('--font-family-code', theme.typography.fontFamilyCode);
      }
      
      // Font weights
      if (theme.typography.fontWeightLight) {
        root.style.setProperty('--font-weight-light', theme.typography.fontWeightLight);
      }
      
      if (theme.typography.fontWeightRegular) {
        root.style.setProperty('--font-weight-regular', theme.typography.fontWeightRegular);
      }
      
      if (theme.typography.fontWeightMedium) {
        root.style.setProperty('--font-weight-medium', theme.typography.fontWeightMedium);
      }
      
      if (theme.typography.fontWeightBold) {
        root.style.setProperty('--font-weight-bold', theme.typography.fontWeightBold);
      }
      
      // Font sizes
      if (theme.typography.fontSize) {
        Object.entries(theme.typography.fontSize).forEach(([key, value]) => {
          root.style.setProperty(`--font-size-${key}`, value);
        });
      }
      
      // Line heights
      if (theme.typography.lineHeight) {
        Object.entries(theme.typography.lineHeight).forEach(([key, value]) => {
          root.style.setProperty(`--line-height-${key}`, value);
        });
      }
    }
    
    // Apply spacing variables
    if (theme.spacing) {
      if (theme.spacing.unit) {
        root.style.setProperty('--spacing-unit', `${theme.spacing.unit}px`);
      }
      
      Object.entries(theme.spacing).forEach(([key, value]) => {
        if (key !== 'unit') {
          root.style.setProperty(`--spacing-${key}`, value);
        }
      });
    }
    
    // Apply breakpoint variables
    if (theme.breakpoints) {
      Object.entries(theme.breakpoints).forEach(([key, value]) => {
        root.style.setProperty(`--breakpoint-${key}`, `${value}px`);
      });
    }
    
    // Apply shadow variables
    if (theme.shadows) {
      Object.entries(theme.shadows).forEach(([key, value]) => {
        root.style.setProperty(`--shadow-${key}`, value);
      });
    }
    
    // Apply border radius variables
    if (theme.radii) {
      Object.entries(theme.radii).forEach(([key, value]) => {
        root.style.setProperty(`--radius-${key}`, value);
      });
    }
    
    // Apply z-index variables
    if (theme.zIndices) {
      Object.entries(theme.zIndices).forEach(([key, value]) => {
        root.style.setProperty(`--z-index-${key}`, value);
      });
    }
    
    // Apply transition variables
    if (theme.transitions) {
      // Easing
      if (theme.transitions.easing) {
        Object.entries(theme.transitions.easing).forEach(([key, value]) => {
          root.style.setProperty(`--transition-easing-${key}`, value);
        });
      }
      
      // Duration
      if (theme.transitions.duration) {
        Object.entries(theme.transitions.duration).forEach(([key, value]) => {
          root.style.setProperty(`--transition-duration-${key}`, `${value}ms`);
        });
      }
    }
    
    // Apply color mode class
    if (colorMode === 'dark') {
      document.body.classList.add('dark-mode');
      document.body.classList.remove('light-mode');
    } else {
      document.body.classList.add('light-mode');
      document.body.classList.remove('dark-mode');
    }
    
  }, [theme, colorMode]);
  
  return <>{children}</>;
};

ThemeApplier.propTypes = {
  children: PropTypes.node.isRequired
};

/**
 * Theme Provider component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Object} [props.theme] - Theme object
 * @param {boolean} [props.enableSystemPreference=true] - Whether to enable system preference detection
 * @returns {React.ReactElement} Theme Provider component
 */
const ThemeProvider = ({ 
  children, 
  theme = defaultTheme,
  enableSystemPreference = true
}) => {
  return (
    <ThemeContextProvider initialTheme={theme} enableSystemPreference={enableSystemPreference}>
      <ThemeApplier>
        {children}
      </ThemeApplier>
    </ThemeContextProvider>
  );
};

ThemeProvider.propTypes = {
  children: PropTypes.node.isRequired,
  theme: PropTypes.object,
  enableSystemPreference: PropTypes.bool
};

export default ThemeProvider;
