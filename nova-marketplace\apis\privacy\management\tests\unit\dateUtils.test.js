/**
 * Date Utilities Tests
 *
 * This file contains unit tests for the date utilities.
 */

const dateUtils = require('../../utils/dateUtils');

describe('Date Utilities', () => {
  describe('calculateDsrDueDate', () => {
    it('should calculate the due date for a DSR with default jurisdiction (GDPR)', () => {
      const dueDate = dateUtils.calculateDsrDueDate('access');
      const expectedDate = new Date();
      expectedDate.setDate(expectedDate.getDate() + 30);

      // Compare dates by converting to ISO string and removing milliseconds
      const dueDateStr = dueDate.toISOString().split('.')[0];
      const expectedDateStr = expectedDate.toISOString().split('.')[0];

      // Allow for a small difference due to test execution time
      expect(dueDateStr.substring(0, 10)).toBe(expectedDateStr.substring(0, 10));
    });

    it('should calculate the due date for a DSR with CCPA jurisdiction', () => {
      const dueDate = dateUtils.calculateDsrDueDate('access', 'ccpa');
      const expectedDate = new Date();
      expectedDate.setDate(expectedDate.getDate() + 45);

      const dueDateStr = dueDate.toISOString().split('.')[0];
      const expectedDateStr = expectedDate.toISOString().split('.')[0];

      expect(dueDateStr.substring(0, 10)).toBe(expectedDateStr.substring(0, 10));
    });

    it('should calculate the due date for a DSR with LGPD jurisdiction', () => {
      const dueDate = dateUtils.calculateDsrDueDate('access', 'lgpd');
      const expectedDate = new Date();
      expectedDate.setDate(expectedDate.getDate() + 15);

      const dueDateStr = dueDate.toISOString().split('.')[0];
      const expectedDateStr = expectedDate.toISOString().split('.')[0];

      expect(dueDateStr.substring(0, 10)).toBe(expectedDateStr.substring(0, 10));
    });
  });

  describe('calculateConsentExpiryDate', () => {
    it('should return null for consent types that do not expire', () => {
      expect(dateUtils.calculateConsentExpiryDate('research')).toBeNull();
      expect(dateUtils.calculateConsentExpiryDate('legal_obligation')).toBeNull();
    });

    it('should calculate the expiry date based on consent type', () => {
      const marketingExpiry = dateUtils.calculateConsentExpiryDate('marketing');
      const expectedDate = new Date();
      expectedDate.setDate(expectedDate.getDate() + 730); // 2 years

      const expiryStr = marketingExpiry.toISOString().split('.')[0];
      const expectedStr = expectedDate.toISOString().split('.')[0];

      expect(expiryStr.substring(0, 10)).toBe(expectedStr.substring(0, 10));
    });

    it('should use the provided validity period if specified', () => {
      const expiry = dateUtils.calculateConsentExpiryDate('marketing', 90);
      const expectedDate = new Date();
      expectedDate.setDate(expectedDate.getDate() + 90);

      const expiryStr = expiry.toISOString().split('.')[0];
      const expectedStr = expectedDate.toISOString().split('.')[0];

      expect(expiryStr.substring(0, 10)).toBe(expectedStr.substring(0, 10));
    });
  });

  describe('calculateBreachNotificationDeadline', () => {
    it('should calculate the notification deadline for GDPR (72 hours)', () => {
      const detectionDate = new Date('2023-06-15T12:00:00Z');
      const deadline = dateUtils.calculateBreachNotificationDeadline(detectionDate, 'gdpr');

      const expectedDate = new Date(detectionDate);
      expectedDate.setHours(expectedDate.getHours() + 72);

      expect(deadline.toISOString()).toBe(expectedDate.toISOString());
    });

    it('should calculate the notification deadline for CCPA (5 days)', () => {
      const detectionDate = new Date('2023-06-15T12:00:00Z');
      const deadline = dateUtils.calculateBreachNotificationDeadline(detectionDate, 'ccpa');

      const expectedDate = new Date(detectionDate);
      expectedDate.setDate(expectedDate.getDate() + 5);

      expect(deadline.toISOString()).toBe(expectedDate.toISOString());
    });

    it('should default to 72 hours for unknown jurisdictions', () => {
      const detectionDate = new Date('2023-06-15T12:00:00Z');
      const deadline = dateUtils.calculateBreachNotificationDeadline(detectionDate, 'unknown');

      const expectedDate = new Date(detectionDate);
      expectedDate.setHours(expectedDate.getHours() + 72);

      expect(deadline.toISOString()).toBe(expectedDate.toISOString());
    });
  });

  describe('formatIsoDate', () => {
    it('should format a date as ISO string without milliseconds', () => {
      const date = new Date('2023-06-15T12:34:56.789Z');
      const formatted = dateUtils.formatIsoDate(date);

      expect(formatted).toBe('2023-06-15T12:34:56Z');
    });

    it('should return null for null or undefined dates', () => {
      expect(dateUtils.formatIsoDate(null)).toBeNull();
      expect(dateUtils.formatIsoDate(undefined)).toBeNull();
    });
  });

  describe('formatHumanDate', () => {
    it('should format a date as a human-readable string', () => {
      // This test is a bit tricky because the exact format depends on the locale
      const date = new Date('2023-06-15T12:34:56Z');
      const formatted = dateUtils.formatHumanDate(date);

      expect(typeof formatted).toBe('string');
      expect(formatted.length).toBeGreaterThan(10);
      expect(formatted).toContain('2023');
      // Don't check for specific hours as they may be affected by timezone
      expect(formatted).toContain('34'); // Minutes should be consistent
      expect(formatted).toContain('56'); // Seconds should be consistent
    });

    it('should return null for null or undefined dates', () => {
      expect(dateUtils.formatHumanDate(null)).toBeNull();
      expect(dateUtils.formatHumanDate(undefined)).toBeNull();
    });
  });

  describe('daysBetween', () => {
    it('should calculate the number of days between two dates', () => {
      const date1 = new Date('2023-06-01');
      const date2 = new Date('2023-06-15');

      expect(dateUtils.daysBetween(date1, date2)).toBe(14);
      expect(dateUtils.daysBetween(date2, date1)).toBe(14);
    });

    it('should handle string dates', () => {
      expect(dateUtils.daysBetween('2023-06-01', '2023-06-15')).toBe(14);
    });
  });

  describe('isInPast', () => {
    it('should return true for dates in the past', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      expect(dateUtils.isInPast(pastDate)).toBe(true);
      expect(dateUtils.isInPast('2000-01-01')).toBe(true);
    });

    it('should return false for dates in the future', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      expect(dateUtils.isInPast(futureDate)).toBe(false);
      expect(dateUtils.isInPast('2100-01-01')).toBe(false);
    });
  });

  describe('isInFuture', () => {
    it('should return true for dates in the future', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      expect(dateUtils.isInFuture(futureDate)).toBe(true);
      expect(dateUtils.isInFuture('2100-01-01')).toBe(true);
    });

    it('should return false for dates in the past', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      expect(dateUtils.isInFuture(pastDate)).toBe(false);
      expect(dateUtils.isInFuture('2000-01-01')).toBe(false);
    });
  });

  describe('getPeriodDates', () => {
    it('should return the correct dates for last-7-days', () => {
      const { startDate, endDate } = dateUtils.getPeriodDates('last-7-days');

      const expectedEndDate = new Date();
      const expectedStartDate = new Date();
      expectedStartDate.setDate(expectedEndDate.getDate() - 7);

      expect(startDate.toISOString().substring(0, 10)).toBe(expectedStartDate.toISOString().substring(0, 10));
      expect(endDate.toISOString().substring(0, 10)).toBe(expectedEndDate.toISOString().substring(0, 10));
    });

    it('should return the correct dates for last-30-days', () => {
      const { startDate, endDate } = dateUtils.getPeriodDates('last-30-days');

      const expectedEndDate = new Date();
      const expectedStartDate = new Date();
      expectedStartDate.setDate(expectedEndDate.getDate() - 30);

      expect(startDate.toISOString().substring(0, 10)).toBe(expectedStartDate.toISOString().substring(0, 10));
      expect(endDate.toISOString().substring(0, 10)).toBe(expectedEndDate.toISOString().substring(0, 10));
    });

    it('should return the correct dates for last-90-days', () => {
      const { startDate, endDate } = dateUtils.getPeriodDates('last-90-days');

      const expectedEndDate = new Date();
      const expectedStartDate = new Date();
      expectedStartDate.setDate(expectedEndDate.getDate() - 90);

      expect(startDate.toISOString().substring(0, 10)).toBe(expectedStartDate.toISOString().substring(0, 10));
      expect(endDate.toISOString().substring(0, 10)).toBe(expectedEndDate.toISOString().substring(0, 10));
    });

    it('should return the correct dates for last-12-months', () => {
      const { startDate, endDate } = dateUtils.getPeriodDates('last-12-months');

      const expectedEndDate = new Date();
      const expectedStartDate = new Date();
      expectedStartDate.setMonth(expectedEndDate.getMonth() - 12);

      expect(startDate.toISOString().substring(0, 10)).toBe(expectedStartDate.toISOString().substring(0, 10));
      expect(endDate.toISOString().substring(0, 10)).toBe(expectedEndDate.toISOString().substring(0, 10));
    });

    it('should return the correct dates for year-to-date', () => {
      const { startDate, endDate } = dateUtils.getPeriodDates('year-to-date');

      const expectedEndDate = new Date();
      const expectedStartDate = new Date(expectedEndDate.getFullYear(), 0, 1);

      expect(startDate.toISOString().substring(0, 10)).toBe(expectedStartDate.toISOString().substring(0, 10));
      expect(endDate.toISOString().substring(0, 10)).toBe(expectedEndDate.toISOString().substring(0, 10));
    });

    it('should default to last-30-days for unknown periods', () => {
      const { startDate, endDate } = dateUtils.getPeriodDates('unknown');

      const expectedEndDate = new Date();
      const expectedStartDate = new Date();
      expectedStartDate.setDate(expectedEndDate.getDate() - 30);

      expect(startDate.toISOString().substring(0, 10)).toBe(expectedStartDate.toISOString().substring(0, 10));
      expect(endDate.toISOString().substring(0, 10)).toBe(expectedEndDate.toISOString().substring(0, 10));
    });
  });
});
