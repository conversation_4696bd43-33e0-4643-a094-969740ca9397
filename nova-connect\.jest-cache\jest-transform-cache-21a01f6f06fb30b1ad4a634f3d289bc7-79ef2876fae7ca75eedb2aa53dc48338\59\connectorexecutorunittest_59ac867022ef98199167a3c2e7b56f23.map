{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "getConnector", "jest", "fn", "initialize", "mockResolvedValue", "require", "path", "fs", "promises", "axios", "<PERSON>ck<PERSON><PERSON>pt<PERSON>", "connectorExecutor", "connectorRegistry", "mockGoogleCloudConnector", "mockAxios", "describe", "beforeEach", "reset", "clearAllMocks", "test", "mockReturnValue", "onGet", "reply", "findings", "name", "parent", "resourceName", "state", "category", "severity", "eventTime", "createTime", "nextPageToken", "result", "executeConnector", "organizationId", "sourceId", "auth", "token", "expect", "success", "toBe", "data", "toEqual", "statusCode", "toHaveBeenCalledWith", "history", "get", "length", "url", "headers", "Authorization", "error", "toContain", "code", "message", "status", "initialMetrics", "getMetrics", "updatedMetrics", "totalRequests", "successfulRequests"], "sources": ["connector-executor.unit.test.js"], "sourcesContent": ["/**\n * Unit tests for the Connector Executor\n */\n\nconst path = require('path');\nconst fs = require('fs').promises;\nconst axios = require('axios');\nconst MockAdapter = require('axios-mock-adapter');\nconst connectorExecutor = require('../../connector-executor');\nconst connectorRegistry = require('../../registry/connector-registry');\nconst { mockGoogleCloudConnector } = require('../mocks/mock-connector');\n\n// Mock axios\nconst mockAxios = new MockAdapter(axios);\n\n// Mock connector registry\njest.mock('../../registry/connector-registry', () => ({\n  getConnector: jest.fn(),\n  initialize: jest.fn().mockResolvedValue(true)\n}));\n\ndescribe('Connector Executor', () => {\n  beforeEach(() => {\n    // Reset mocks\n    mockAxios.reset();\n    jest.clearAllMocks();\n  });\n  \n  test('should execute a connector endpoint successfully', async () => {\n    // Mock connector registry response\n    connectorRegistry.getConnector.mockReturnValue(mockGoogleCloudConnector);\n    \n    // Mock axios response\n    mockAxios.onGet('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings')\n      .reply(200, {\n        findings: [\n          {\n            name: 'finding-1',\n            parent: 'organizations/123/sources/456',\n            resourceName: 'projects/my-project/instances/my-instance',\n            state: 'ACTIVE',\n            category: 'VULNERABILITY',\n            severity: 'HIGH',\n            eventTime: '2023-06-01T00:00:00Z',\n            createTime: '2023-06-01T00:00:00Z'\n          }\n        ],\n        nextPageToken: 'next-page-token'\n      });\n    \n    // Execute connector\n    const result = await connectorExecutor.executeConnector(\n      'google-cloud-security-1.0.0',\n      'list-findings',\n      {\n        path: {\n          organizationId: '123',\n          sourceId: '456'\n        },\n        auth: {\n          token: 'test-token'\n        }\n      }\n    );\n    \n    // Verify result\n    expect(result.success).toBe(true);\n    expect(result.data).toEqual([\n      {\n        name: 'finding-1',\n        parent: 'organizations/123/sources/456',\n        resourceName: 'projects/my-project/instances/my-instance',\n        state: 'ACTIVE',\n        category: 'VULNERABILITY',\n        severity: 'HIGH',\n        eventTime: '2023-06-01T00:00:00Z',\n        createTime: '2023-06-01T00:00:00Z'\n      }\n    ]);\n    expect(result.statusCode).toBe(200);\n    \n    // Verify connector registry was called\n    expect(connectorRegistry.getConnector).toHaveBeenCalledWith('google-cloud-security-1.0.0');\n    \n    // Verify axios was called with correct parameters\n    expect(mockAxios.history.get.length).toBe(1);\n    expect(mockAxios.history.get[0].url).toBe('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings');\n    expect(mockAxios.history.get[0].headers.Authorization).toBe('Bearer test-token');\n  });\n  \n  test('should handle connector not found error', async () => {\n    // Mock connector registry response\n    connectorRegistry.getConnector.mockReturnValue(null);\n    \n    // Execute connector\n    const result = await connectorExecutor.executeConnector(\n      'non-existent-connector',\n      'list-findings',\n      {}\n    );\n    \n    // Verify result\n    expect(result.success).toBe(false);\n    expect(result.error).toContain('not found');\n    expect(result.statusCode).toBe(500);\n    \n    // Verify connector registry was called\n    expect(connectorRegistry.getConnector).toHaveBeenCalledWith('non-existent-connector');\n    \n    // Verify axios was not called\n    expect(mockAxios.history.get.length).toBe(0);\n  });\n  \n  test('should handle endpoint not found error', async () => {\n    // Mock connector registry response\n    connectorRegistry.getConnector.mockReturnValue(mockGoogleCloudConnector);\n    \n    // Execute connector\n    const result = await connectorExecutor.executeConnector(\n      'google-cloud-security-1.0.0',\n      'non-existent-endpoint',\n      {}\n    );\n    \n    // Verify result\n    expect(result.success).toBe(false);\n    expect(result.error).toContain('not found');\n    expect(result.statusCode).toBe(500);\n    \n    // Verify connector registry was called\n    expect(connectorRegistry.getConnector).toHaveBeenCalledWith('google-cloud-security-1.0.0');\n    \n    // Verify axios was not called\n    expect(mockAxios.history.get.length).toBe(0);\n  });\n  \n  test('should handle API error', async () => {\n    // Mock connector registry response\n    connectorRegistry.getConnector.mockReturnValue(mockGoogleCloudConnector);\n    \n    // Mock axios response\n    mockAxios.onGet('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings')\n      .reply(403, {\n        error: {\n          code: 403,\n          message: 'Permission denied',\n          status: 'PERMISSION_DENIED'\n        }\n      });\n    \n    // Execute connector\n    const result = await connectorExecutor.executeConnector(\n      'google-cloud-security-1.0.0',\n      'list-findings',\n      {\n        path: {\n          organizationId: '123',\n          sourceId: '456'\n        },\n        auth: {\n          token: 'test-token'\n        }\n      }\n    );\n    \n    // Verify result\n    expect(result.success).toBe(false);\n    expect(result.statusCode).toBe(403);\n    \n    // Verify connector registry was called\n    expect(connectorRegistry.getConnector).toHaveBeenCalledWith('google-cloud-security-1.0.0');\n    \n    // Verify axios was called with correct parameters\n    expect(mockAxios.history.get.length).toBe(1);\n    expect(mockAxios.history.get[0].url).toBe('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings');\n  });\n  \n  test('should update metrics after execution', async () => {\n    // Mock connector registry response\n    connectorRegistry.getConnector.mockReturnValue(mockGoogleCloudConnector);\n    \n    // Mock axios response\n    mockAxios.onGet('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings')\n      .reply(200, {\n        findings: [],\n        nextPageToken: ''\n      });\n    \n    // Get initial metrics\n    const initialMetrics = connectorExecutor.getMetrics();\n    \n    // Execute connector\n    await connectorExecutor.executeConnector(\n      'google-cloud-security-1.0.0',\n      'list-findings',\n      {\n        path: {\n          organizationId: '123',\n          sourceId: '456'\n        }\n      }\n    );\n    \n    // Get updated metrics\n    const updatedMetrics = connectorExecutor.getMetrics();\n    \n    // Verify metrics were updated\n    expect(updatedMetrics.totalRequests).toBe(initialMetrics.totalRequests + 1);\n    expect(updatedMetrics.successfulRequests).toBe(initialMetrics.successfulRequests + 1);\n  });\n});\n"], "mappings": "AAeA;AACAA,WAAA,GAAKC,IAAI,CAAC,mCAAmC,EAAE,OAAO;EACpDC,YAAY,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC;EACvBC,UAAU,EAAEF,IAAI,CAACC,EAAE,CAAC,CAAC,CAACE,iBAAiB,CAAC,IAAI;AAC9C,CAAC,CAAC,CAAC;AAAC,SAAAN,YAAA;EAAA;IAAAG;EAAA,IAAAI,OAAA;EAAAP,WAAA,GAAAA,CAAA,KAAAG,IAAA;EAAA,OAAAA,IAAA;AAAA;AAnBJ;AACA;AACA;;AAEA,MAAMK,IAAI,GAAGD,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAME,EAAE,GAAGF,OAAO,CAAC,IAAI,CAAC,CAACG,QAAQ;AACjC,MAAMC,KAAK,GAAGJ,OAAO,CAAC,OAAO,CAAC;AAC9B,MAAMK,WAAW,GAAGL,OAAO,CAAC,oBAAoB,CAAC;AACjD,MAAMM,iBAAiB,GAAGN,OAAO,CAAC,0BAA0B,CAAC;AAC7D,MAAMO,iBAAiB,GAAGP,OAAO,CAAC,mCAAmC,CAAC;AACtE,MAAM;EAAEQ;AAAyB,CAAC,GAAGR,OAAO,CAAC,yBAAyB,CAAC;;AAEvE;AACA,MAAMS,SAAS,GAAG,IAAIJ,WAAW,CAACD,KAAK,CAAC;AAQxCM,QAAQ,CAAC,oBAAoB,EAAE,MAAM;EACnCC,UAAU,CAAC,MAAM;IACf;IACAF,SAAS,CAACG,KAAK,CAAC,CAAC;IACjBhB,IAAI,CAACiB,aAAa,CAAC,CAAC;EACtB,CAAC,CAAC;EAEFC,IAAI,CAAC,kDAAkD,EAAE,YAAY;IACnE;IACAP,iBAAiB,CAACZ,YAAY,CAACoB,eAAe,CAACP,wBAAwB,CAAC;;IAExE;IACAC,SAAS,CAACO,KAAK,CAAC,iFAAiF,CAAC,CAC/FC,KAAK,CAAC,GAAG,EAAE;MACVC,QAAQ,EAAE,CACR;QACEC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE,+BAA+B;QACvCC,YAAY,EAAE,2CAA2C;QACzDC,KAAK,EAAE,QAAQ;QACfC,QAAQ,EAAE,eAAe;QACzBC,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE,sBAAsB;QACjCC,UAAU,EAAE;MACd,CAAC,CACF;MACDC,aAAa,EAAE;IACjB,CAAC,CAAC;;IAEJ;IACA,MAAMC,MAAM,GAAG,MAAMtB,iBAAiB,CAACuB,gBAAgB,CACrD,6BAA6B,EAC7B,eAAe,EACf;MACE5B,IAAI,EAAE;QACJ6B,cAAc,EAAE,KAAK;QACrBC,QAAQ,EAAE;MACZ,CAAC;MACDC,IAAI,EAAE;QACJC,KAAK,EAAE;MACT;IACF,CACF,CAAC;;IAED;IACAC,MAAM,CAACN,MAAM,CAACO,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACjCF,MAAM,CAACN,MAAM,CAACS,IAAI,CAAC,CAACC,OAAO,CAAC,CAC1B;MACEnB,IAAI,EAAE,WAAW;MACjBC,MAAM,EAAE,+BAA+B;MACvCC,YAAY,EAAE,2CAA2C;MACzDC,KAAK,EAAE,QAAQ;MACfC,QAAQ,EAAE,eAAe;MACzBC,QAAQ,EAAE,MAAM;MAChBC,SAAS,EAAE,sBAAsB;MACjCC,UAAU,EAAE;IACd,CAAC,CACF,CAAC;IACFQ,MAAM,CAACN,MAAM,CAACW,UAAU,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;;IAEnC;IACAF,MAAM,CAAC3B,iBAAiB,CAACZ,YAAY,CAAC,CAAC6C,oBAAoB,CAAC,6BAA6B,CAAC;;IAE1F;IACAN,MAAM,CAACzB,SAAS,CAACgC,OAAO,CAACC,GAAG,CAACC,MAAM,CAAC,CAACP,IAAI,CAAC,CAAC,CAAC;IAC5CF,MAAM,CAACzB,SAAS,CAACgC,OAAO,CAACC,GAAG,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,CAACR,IAAI,CAAC,iFAAiF,CAAC;IAC5HF,MAAM,CAACzB,SAAS,CAACgC,OAAO,CAACC,GAAG,CAAC,CAAC,CAAC,CAACG,OAAO,CAACC,aAAa,CAAC,CAACV,IAAI,CAAC,mBAAmB,CAAC;EAClF,CAAC,CAAC;EAEFtB,IAAI,CAAC,yCAAyC,EAAE,YAAY;IAC1D;IACAP,iBAAiB,CAACZ,YAAY,CAACoB,eAAe,CAAC,IAAI,CAAC;;IAEpD;IACA,MAAMa,MAAM,GAAG,MAAMtB,iBAAiB,CAACuB,gBAAgB,CACrD,wBAAwB,EACxB,eAAe,EACf,CAAC,CACH,CAAC;;IAED;IACAK,MAAM,CAACN,MAAM,CAACO,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IAClCF,MAAM,CAACN,MAAM,CAACmB,KAAK,CAAC,CAACC,SAAS,CAAC,WAAW,CAAC;IAC3Cd,MAAM,CAACN,MAAM,CAACW,UAAU,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;;IAEnC;IACAF,MAAM,CAAC3B,iBAAiB,CAACZ,YAAY,CAAC,CAAC6C,oBAAoB,CAAC,wBAAwB,CAAC;;IAErF;IACAN,MAAM,CAACzB,SAAS,CAACgC,OAAO,CAACC,GAAG,CAACC,MAAM,CAAC,CAACP,IAAI,CAAC,CAAC,CAAC;EAC9C,CAAC,CAAC;EAEFtB,IAAI,CAAC,wCAAwC,EAAE,YAAY;IACzD;IACAP,iBAAiB,CAACZ,YAAY,CAACoB,eAAe,CAACP,wBAAwB,CAAC;;IAExE;IACA,MAAMoB,MAAM,GAAG,MAAMtB,iBAAiB,CAACuB,gBAAgB,CACrD,6BAA6B,EAC7B,uBAAuB,EACvB,CAAC,CACH,CAAC;;IAED;IACAK,MAAM,CAACN,MAAM,CAACO,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IAClCF,MAAM,CAACN,MAAM,CAACmB,KAAK,CAAC,CAACC,SAAS,CAAC,WAAW,CAAC;IAC3Cd,MAAM,CAACN,MAAM,CAACW,UAAU,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;;IAEnC;IACAF,MAAM,CAAC3B,iBAAiB,CAACZ,YAAY,CAAC,CAAC6C,oBAAoB,CAAC,6BAA6B,CAAC;;IAE1F;IACAN,MAAM,CAACzB,SAAS,CAACgC,OAAO,CAACC,GAAG,CAACC,MAAM,CAAC,CAACP,IAAI,CAAC,CAAC,CAAC;EAC9C,CAAC,CAAC;EAEFtB,IAAI,CAAC,yBAAyB,EAAE,YAAY;IAC1C;IACAP,iBAAiB,CAACZ,YAAY,CAACoB,eAAe,CAACP,wBAAwB,CAAC;;IAExE;IACAC,SAAS,CAACO,KAAK,CAAC,iFAAiF,CAAC,CAC/FC,KAAK,CAAC,GAAG,EAAE;MACV8B,KAAK,EAAE;QACLE,IAAI,EAAE,GAAG;QACTC,OAAO,EAAE,mBAAmB;QAC5BC,MAAM,EAAE;MACV;IACF,CAAC,CAAC;;IAEJ;IACA,MAAMvB,MAAM,GAAG,MAAMtB,iBAAiB,CAACuB,gBAAgB,CACrD,6BAA6B,EAC7B,eAAe,EACf;MACE5B,IAAI,EAAE;QACJ6B,cAAc,EAAE,KAAK;QACrBC,QAAQ,EAAE;MACZ,CAAC;MACDC,IAAI,EAAE;QACJC,KAAK,EAAE;MACT;IACF,CACF,CAAC;;IAED;IACAC,MAAM,CAACN,MAAM,CAACO,OAAO,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IAClCF,MAAM,CAACN,MAAM,CAACW,UAAU,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;;IAEnC;IACAF,MAAM,CAAC3B,iBAAiB,CAACZ,YAAY,CAAC,CAAC6C,oBAAoB,CAAC,6BAA6B,CAAC;;IAE1F;IACAN,MAAM,CAACzB,SAAS,CAACgC,OAAO,CAACC,GAAG,CAACC,MAAM,CAAC,CAACP,IAAI,CAAC,CAAC,CAAC;IAC5CF,MAAM,CAACzB,SAAS,CAACgC,OAAO,CAACC,GAAG,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,CAACR,IAAI,CAAC,iFAAiF,CAAC;EAC9H,CAAC,CAAC;EAEFtB,IAAI,CAAC,uCAAuC,EAAE,YAAY;IACxD;IACAP,iBAAiB,CAACZ,YAAY,CAACoB,eAAe,CAACP,wBAAwB,CAAC;;IAExE;IACAC,SAAS,CAACO,KAAK,CAAC,iFAAiF,CAAC,CAC/FC,KAAK,CAAC,GAAG,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZS,aAAa,EAAE;IACjB,CAAC,CAAC;;IAEJ;IACA,MAAMyB,cAAc,GAAG9C,iBAAiB,CAAC+C,UAAU,CAAC,CAAC;;IAErD;IACA,MAAM/C,iBAAiB,CAACuB,gBAAgB,CACtC,6BAA6B,EAC7B,eAAe,EACf;MACE5B,IAAI,EAAE;QACJ6B,cAAc,EAAE,KAAK;QACrBC,QAAQ,EAAE;MACZ;IACF,CACF,CAAC;;IAED;IACA,MAAMuB,cAAc,GAAGhD,iBAAiB,CAAC+C,UAAU,CAAC,CAAC;;IAErD;IACAnB,MAAM,CAACoB,cAAc,CAACC,aAAa,CAAC,CAACnB,IAAI,CAACgB,cAAc,CAACG,aAAa,GAAG,CAAC,CAAC;IAC3ErB,MAAM,CAACoB,cAAc,CAACE,kBAAkB,CAAC,CAACpB,IAAI,CAACgB,cAAc,CAACI,kBAAkB,GAAG,CAAC,CAAC;EACvF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}