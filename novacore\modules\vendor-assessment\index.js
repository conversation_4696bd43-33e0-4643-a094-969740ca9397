/**
 * NovaCore Vendor Assessment Module
 * 
 * This module provides SaaS vendor assessment functionality.
 */

const routes = require('./routes');
const { 
  VendorController, 
  AssessmentController, 
  AssessmentTemplateController, 
  DocumentController 
} = require('./controllers');
const { 
  VendorService, 
  AssessmentService, 
  AssessmentTemplateService, 
  DocumentService 
} = require('./services');
const { 
  Vendor, 
  Assessment, 
  AssessmentTemplate, 
  Document 
} = require('./models');

module.exports = {
  routes,
  controllers: {
    VendorController,
    AssessmentController,
    AssessmentTemplateController,
    DocumentController
  },
  services: {
    VendorService,
    AssessmentService,
    AssessmentTemplateService,
    DocumentService
  },
  models: {
    Vendor,
    Assessment,
    AssessmentTemplate,
    Document
  }
};
