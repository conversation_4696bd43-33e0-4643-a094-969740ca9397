import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText,
  InventorLabel
} from '../../components/DiagramComponents';

const TwelveNovas = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel>CYBER-SAFETY FRAMEWORK: 12 UNIVERSAL NOVAS</ContainerLabel>
      </ContainerBox>
      
      {/* Top Row - Novas 1-4 */}
      <ComponentBox left="100px" top="80px" width="150px" height="100px">
        <ComponentNumber>301</ComponentNumber>
        <ComponentLabel>Nova 1</ComponentLabel>
        NovaCore
      </ComponentBox>
      
      <ComponentBox left="270px" top="80px" width="150px" height="100px">
        <ComponentNumber>302</ComponentNumber>
        <ComponentLabel>Nova 2</ComponentLabel>
        NovaShield
      </ComponentBox>
      
      <ComponentBox left="440px" top="80px" width="150px" height="100px">
        <ComponentNumber>303</ComponentNumber>
        <ComponentLabel>Nova 3</ComponentLabel>
        NovaTrack
      </ComponentBox>
      
      <ComponentBox left="610px" top="80px" width="150px" height="100px">
        <ComponentNumber>304</ComponentNumber>
        <ComponentLabel>Nova 4</ComponentLabel>
        NovaLearn
      </ComponentBox>
      
      {/* Middle Row - Novas 5-8 */}
      <ComponentBox left="100px" top="200px" width="150px" height="100px">
        <ComponentNumber>305</ComponentNumber>
        <ComponentLabel>Nova 5</ComponentLabel>
        NovaView
      </ComponentBox>
      
      <ComponentBox left="270px" top="200px" width="150px" height="100px">
        <ComponentNumber>306</ComponentNumber>
        <ComponentLabel>Nova 6</ComponentLabel>
        NovaFlowX
      </ComponentBox>
      
      <ComponentBox left="440px" top="200px" width="150px" height="100px">
        <ComponentNumber>307</ComponentNumber>
        <ComponentLabel>Nova 7</ComponentLabel>
        NovaPulse+
      </ComponentBox>
      
      <ComponentBox left="610px" top="200px" width="150px" height="100px">
        <ComponentNumber>308</ComponentNumber>
        <ComponentLabel>Nova 8</ComponentLabel>
        NovaProof
      </ComponentBox>
      
      {/* Bottom Row - Novas 9-12 */}
      <ComponentBox left="100px" top="320px" width="150px" height="100px">
        <ComponentNumber>309</ComponentNumber>
        <ComponentLabel>Nova 9</ComponentLabel>
        NovaThink
      </ComponentBox>
      
      <ComponentBox left="270px" top="320px" width="150px" height="100px">
        <ComponentNumber>310</ComponentNumber>
        <ComponentLabel>Nova 10</ComponentLabel>
        NovaConnect
      </ComponentBox>
      
      <ComponentBox left="440px" top="320px" width="150px" height="100px">
        <ComponentNumber>311</ComponentNumber>
        <ComponentLabel>Nova 11</ComponentLabel>
        NovaVision
      </ComponentBox>
      
      <ComponentBox left="610px" top="320px" width="150px" height="100px">
        <ComponentNumber>312</ComponentNumber>
        <ComponentLabel>Nova 12</ComponentLabel>
        NovaDNA
      </ComponentBox>
      
      {/* NovaStore - The 13th Nova */}
      <ContainerBox width="700px" height="120px" left="80px" top="450px">
        <ContainerLabel>THE 13TH NOVA</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="355px" top="490px" width="150px" height="60px">
        <ComponentNumber>313</ComponentNumber>
        <ComponentLabel>Nova 13</ComponentLabel>
        NovaStore
      </ComponentBox>
      
      {/* Connecting arrows */}
      <Arrow left="175px" top="180px" width="2px" height="20px" />
      <Arrow left="345px" top="180px" width="2px" height="20px" />
      <Arrow left="515px" top="180px" width="2px" height="20px" />
      <Arrow left="685px" top="180px" width="2px" height="20px" />
      
      <Arrow left="175px" top="300px" width="2px" height="20px" />
      <Arrow left="345px" top="300px" width="2px" height="20px" />
      <Arrow left="515px" top="300px" width="2px" height="20px" />
      <Arrow left="685px" top="300px" width="2px" height="20px" />
      
      <Arrow left="175px" top="420px" width="2px" height="30px" />
      <Arrow left="345px" top="420px" width="2px" height="30px" />
      <Arrow left="515px" top="420px" width="2px" height="30px" />
      <Arrow left="685px" top="420px" width="2px" height="30px" />
      
      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>12 Universal Novas</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>13th Nova (NovaStore)</LegendText>
        </LegendItem>
      </DiagramLegend>
      
      {/* Inventor Label */}
      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default TwelveNovas;
