const express = require('express');
const { validateRequest } = require('./validation');
const controllers = require('./controllers');

const router = express.Router();

/**
 * @swagger
 * /compliance/regulatory/frameworks:
 *   get:
 *     summary: Get a list of regulatory frameworks
 *     description: Returns a paginated list of regulatory frameworks with optional filtering
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: category
 *         in: query
 *         description: Filter by framework category
 *         schema:
 *           type: string
 *           enum: [privacy, security, financial, healthcare, environmental, employment, other]
 *       - name: jurisdiction
 *         in: query
 *         description: Filter by jurisdiction
 *         schema:
 *           type: string
 *       - name: status
 *         in: query
 *         description: Filter by framework status
 *         schema:
 *           type: string
 *           enum: [draft, active, superseded, retired]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/RegulatoryFramework'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/frameworks', controllers.getFrameworks);

/**
 * @swagger
 * /compliance/regulatory/frameworks/{id}:
 *   get:
 *     summary: Get a specific regulatory framework
 *     description: Returns a specific regulatory framework by ID
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulatory framework ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/RegulatoryFramework'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/frameworks/:id', controllers.getFrameworkById);

/**
 * @swagger
 * /compliance/regulatory/frameworks:
 *   post:
 *     summary: Create a new regulatory framework
 *     description: Creates a new regulatory framework
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateRegulatoryFramework'
 *     responses:
 *       201:
 *         description: Framework created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/RegulatoryFramework'
 *                 message:
 *                   type: string
 *                   example: Regulatory framework created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/frameworks', validateRequest('createRegulatoryFramework'), controllers.createFramework);

/**
 * @swagger
 * /compliance/regulatory/frameworks/{id}:
 *   put:
 *     summary: Update a regulatory framework
 *     description: Updates an existing regulatory framework
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulatory framework ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateRegulatoryFramework'
 *     responses:
 *       200:
 *         description: Framework updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/RegulatoryFramework'
 *                 message:
 *                   type: string
 *                   example: Regulatory framework updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/frameworks/:id', validateRequest('updateRegulatoryFramework'), controllers.updateFramework);

/**
 * @swagger
 * /compliance/regulatory/frameworks/{id}:
 *   delete:
 *     summary: Delete a regulatory framework
 *     description: Deletes a regulatory framework if it has no associated requirements
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulatory framework ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Framework deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Regulatory framework deleted successfully
 *       400:
 *         description: Bad request - framework has associated requirements
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 message:
 *                   type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/frameworks/:id', controllers.deleteFramework);

/**
 * @swagger
 * /compliance/regulatory/requirements:
 *   get:
 *     summary: Get a list of regulatory requirements
 *     description: Returns a paginated list of regulatory requirements with optional filtering
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: frameworkId
 *         in: query
 *         description: Filter by framework ID
 *         schema:
 *           type: string
 *       - name: category
 *         in: query
 *         description: Filter by requirement category
 *         schema:
 *           type: string
 *       - name: priority
 *         in: query
 *         description: Filter by priority level
 *         schema:
 *           type: string
 *           enum: [low, medium, high, critical]
 *       - name: status
 *         in: query
 *         description: Filter by status
 *         schema:
 *           type: string
 *           enum: [applicable, not-applicable, under-review]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/RegulatoryRequirement'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/requirements', controllers.getRequirements);

/**
 * @swagger
 * /compliance/regulatory/requirements/{id}:
 *   get:
 *     summary: Get a specific regulatory requirement
 *     description: Returns a specific regulatory requirement by ID
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulatory requirement ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/RegulatoryRequirement'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/requirements/:id', controllers.getRequirementById);

/**
 * @swagger
 * /compliance/regulatory/requirements:
 *   post:
 *     summary: Create a new regulatory requirement
 *     description: Creates a new regulatory requirement
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateRegulatoryRequirement'
 *     responses:
 *       201:
 *         description: Requirement created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/RegulatoryRequirement'
 *                 message:
 *                   type: string
 *                   example: Regulatory requirement created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: Framework not found
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/requirements', validateRequest('createRegulatoryRequirement'), controllers.createRequirement);

/**
 * @swagger
 * /compliance/regulatory/requirements/{id}:
 *   put:
 *     summary: Update a regulatory requirement
 *     description: Updates an existing regulatory requirement
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulatory requirement ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateRegulatoryRequirement'
 *     responses:
 *       200:
 *         description: Requirement updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/RegulatoryRequirement'
 *                 message:
 *                   type: string
 *                   example: Regulatory requirement updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/requirements/:id', validateRequest('updateRegulatoryRequirement'), controllers.updateRequirement);

/**
 * @swagger
 * /compliance/regulatory/requirements/{id}:
 *   delete:
 *     summary: Delete a regulatory requirement
 *     description: Deletes a regulatory requirement and updates any references to it
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulatory requirement ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Requirement deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Regulatory requirement deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/requirements/:id', controllers.deleteRequirement);

/**
 * @swagger
 * /compliance/regulatory/jurisdictions:
 *   get:
 *     summary: Get a list of jurisdictions
 *     description: Returns a paginated list of jurisdictions with optional filtering
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: type
 *         in: query
 *         description: Filter by jurisdiction type
 *         schema:
 *           type: string
 *           enum: [global, region, country, state, province, city, other]
 *       - name: parentJurisdiction
 *         in: query
 *         description: Filter by parent jurisdiction code
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Jurisdiction'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/jurisdictions', controllers.getJurisdictions);

/**
 * @swagger
 * /compliance/regulatory/jurisdictions/{id}:
 *   get:
 *     summary: Get a specific jurisdiction
 *     description: Returns a specific jurisdiction by ID
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Jurisdiction ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Jurisdiction'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/jurisdictions/:id', controllers.getJurisdictionById);

/**
 * @swagger
 * /compliance/regulatory/jurisdictions:
 *   post:
 *     summary: Create a new jurisdiction
 *     description: Creates a new jurisdiction
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateJurisdiction'
 *     responses:
 *       201:
 *         description: Jurisdiction created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Jurisdiction'
 *                 message:
 *                   type: string
 *                   example: Jurisdiction created successfully
 *       400:
 *         description: Bad request - validation error or duplicate code
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: Parent jurisdiction not found
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/jurisdictions', validateRequest('createJurisdiction'), controllers.createJurisdiction);

/**
 * @swagger
 * /compliance/regulatory/changes:
 *   get:
 *     summary: Get a list of regulatory changes
 *     description: Returns a paginated list of regulatory changes with optional filtering
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: frameworkId
 *         in: query
 *         description: Filter by framework ID
 *         schema:
 *           type: string
 *       - name: changeType
 *         in: query
 *         description: Filter by change type
 *         schema:
 *           type: string
 *           enum: [legislation, regulation, guidance, case-law, other]
 *       - name: status
 *         in: query
 *         description: Filter by status
 *         schema:
 *           type: string
 *           enum: [draft, published, in-effect, superseded]
 *       - name: impactLevel
 *         in: query
 *         description: Filter by impact level
 *         schema:
 *           type: string
 *           enum: [low, medium, high, critical]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/RegulatoryChange'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/changes', controllers.getChanges);

/**
 * @swagger
 * /compliance/regulatory/changes/{id}:
 *   get:
 *     summary: Get a specific regulatory change
 *     description: Returns a specific regulatory change by ID
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulatory change ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/RegulatoryChange'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/changes/:id', controllers.getChangeById);

/**
 * @swagger
 * /compliance/regulatory/changes:
 *   post:
 *     summary: Create a new regulatory change
 *     description: Creates a new regulatory change
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateRegulatoryChange'
 *     responses:
 *       201:
 *         description: Change created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/RegulatoryChange'
 *                 message:
 *                   type: string
 *                   example: Regulatory change created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: Framework not found
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/changes', validateRequest('createRegulatoryChange'), controllers.createChange);

/**
 * @swagger
 * /compliance/regulatory/changes/{id}:
 *   put:
 *     summary: Update a regulatory change
 *     description: Updates an existing regulatory change
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulatory change ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateRegulatoryChange'
 *     responses:
 *       200:
 *         description: Change updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/RegulatoryChange'
 *                 message:
 *                   type: string
 *                   example: Regulatory change updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/changes/:id', validateRequest('updateRegulatoryChange'), controllers.updateChange);

/**
 * @swagger
 * /compliance/regulatory/changes/{id}:
 *   delete:
 *     summary: Delete a regulatory change
 *     description: Deletes a regulatory change
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulatory change ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Change deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Regulatory change deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/changes/:id', controllers.deleteChange);

/**
 * @swagger
 * /compliance/regulatory/reports:
 *   get:
 *     summary: Get a list of regulatory reports
 *     description: Returns a paginated list of regulatory reports with optional filtering
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: frameworkId
 *         in: query
 *         description: Filter by framework ID
 *         schema:
 *           type: string
 *       - name: type
 *         in: query
 *         description: Filter by report type
 *         schema:
 *           type: string
 *           enum: [internal, external, regulatory, audit]
 *       - name: frequency
 *         in: query
 *         description: Filter by report frequency
 *         schema:
 *           type: string
 *           enum: [one-time, monthly, quarterly, semi-annual, annual, biennial]
 *       - name: status
 *         in: query
 *         description: Filter by report status
 *         schema:
 *           type: string
 *           enum: [not-started, in-progress, review, completed, submitted]
 *       - name: assignee
 *         in: query
 *         description: Filter by assignee (partial match)
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/RegulatoryReport'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/reports', controllers.getReports);

/**
 * @swagger
 * /compliance/regulatory/reports/{id}:
 *   get:
 *     summary: Get a specific regulatory report
 *     description: Returns a specific regulatory report by ID
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulatory report ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/RegulatoryReport'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/reports/:id', controllers.getReportById);

/**
 * @swagger
 * /compliance/regulatory/reports:
 *   post:
 *     summary: Create a new regulatory report
 *     description: Creates a new regulatory report
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateRegulatoryReport'
 *     responses:
 *       201:
 *         description: Report created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/RegulatoryReport'
 *                 message:
 *                   type: string
 *                   example: Regulatory report created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: Framework not found
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/reports', validateRequest('createRegulatoryReport'), controllers.createReport);

/**
 * @swagger
 * /compliance/regulatory/reports/{id}:
 *   put:
 *     summary: Update a regulatory report
 *     description: Updates an existing regulatory report
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulatory report ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateRegulatoryReport'
 *     responses:
 *       200:
 *         description: Report updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/RegulatoryReport'
 *                 message:
 *                   type: string
 *                   example: Regulatory report updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/reports/:id', validateRequest('updateRegulatoryReport'), controllers.updateReport);

/**
 * @swagger
 * /compliance/regulatory/reports/{id}:
 *   delete:
 *     summary: Delete a regulatory report
 *     description: Deletes a regulatory report
 *     tags: [Regulatory Compliance]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulatory report ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Report deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Regulatory report deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/reports/:id', controllers.deleteReport);

module.exports = router;
