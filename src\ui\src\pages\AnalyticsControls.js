import React, { useState, useEffect } from 'react';
import { Box, Grid, Typography, <PERSON><PERSON>, Card, CardContent, Divider } from '@mui/material';
import { PlayArrow as ExecuteIcon, Refresh as RefreshIcon } from '@mui/icons-material';
import ControlGroupRenderer from '../components/controls/ControlGroupRenderer';
import ActionButton from '../components/controls/ActionButton';
import { useControl } from '../contexts/ControlContext';

function AnalyticsControls() {
  const { getControlValue, executeQuery, refreshMetrics, refreshDashboard } = useControl();
  const [queryResults, setQueryResults] = useState([]);
  const [refreshInterval, setRefreshInterval] = useState(null);
  
  const selectedDashboardId = getControlValue('dashboard-selector');
  const refreshIntervalValue = getControlValue('refresh-interval');
  const queryInput = getControlValue('query-input');
  
  // Set up refresh interval
  useEffect(() => {
    // Clear existing interval
    if (refreshInterval) {
      clearInterval(refreshInterval);
    }
    
    // Set new interval if greater than 0
    if (refreshIntervalValue > 0) {
      const interval = setInterval(() => {
        refreshMetrics();
        if (selectedDashboardId) {
          refreshDashboard(selectedDashboardId);
        }
      }, refreshIntervalValue * 1000);
      
      setRefreshInterval(interval);
      
      // Clean up on unmount
      return () => clearInterval(interval);
    }
  }, [refreshIntervalValue, selectedDashboardId, refreshMetrics, refreshDashboard]);
  
  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [refreshInterval]);
  
  const handleExecuteQuery = async () => {
    try {
      // Execute query
      const result = await executeQuery(queryInput);
      
      // Update query results
      setQueryResults([
        {
          id: `query-${Date.now()}`,
          query: queryInput,
          timestamp: Date.now(),
          results: [
            { metric: 'tensor.health', value: 0.95, timestamp: Date.now() },
            { metric: 'tensor.entropyContainment', value: 0.02, timestamp: Date.now() }
          ]
        },
        ...queryResults.slice(0, 4) // Keep only the 5 most recent results
      ]);
    } catch (error) {
      console.error('Error executing query:', error);
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Analytics Controls
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          {/* Dashboard Selector */}
          <ControlGroupRenderer
            groupId="analytics-controls"
            title="Dashboard Controls"
          />
          
          <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
            <ActionButton
              action="refresh-metrics"
              label="Refresh Metrics"
              icon={<RefreshIcon />}
              color="primary"
              sx={{ flex: 1 }}
            />
            
            <ActionButton
              action="refresh-dashboard"
              params={{ id: selectedDashboardId }}
              label="Refresh Dashboard"
              icon={<RefreshIcon />}
              color="secondary"
              sx={{ flex: 1 }}
            />
          </Box>
          
          {/* Query Input */}
          <ControlGroupRenderer
            groupId="analytics-query"
            title="Query Execution"
          />
          
          <Box sx={{ mt: 2 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<ExecuteIcon />}
              onClick={handleExecuteQuery}
              fullWidth
            >
              Execute Query
            </Button>
          </Box>
        </Grid>
        
        <Grid item xs={12} md={8}>
          {/* Dashboard Preview */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Dashboard Preview
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Box
                sx={{
                  height: 300,
                  bgcolor: 'background.default',
                  borderRadius: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography variant="body1" color="text.secondary">
                  Dashboard preview will be displayed here
                </Typography>
              </Box>
            </CardContent>
          </Card>
          
          {/* Query Results */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Query Results
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              {queryResults.length > 0 ? (
                <Box>
                  {queryResults.map((result) => (
                    <Box key={result.id} sx={{ mb: 3 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Query: {result.query}
                      </Typography>
                      <Typography variant="caption" display="block" gutterBottom>
                        Executed at: {new Date(result.timestamp).toLocaleString()}
                      </Typography>
                      <Box
                        sx={{
                          bgcolor: 'background.default',
                          p: 2,
                          borderRadius: 1,
                          mt: 1,
                        }}
                      >
                        <pre style={{ margin: 0, overflow: 'auto' }}>
                          {JSON.stringify(result.results, null, 2)}
                        </pre>
                      </Box>
                    </Box>
                  ))}
                </Box>
              ) : (
                <Typography variant="body1" color="text.secondary">
                  No query results yet. Execute a query to see results here.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default AnalyticsControls;
