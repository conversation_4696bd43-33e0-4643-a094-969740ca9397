/**
 * Role-Based Access Control Configuration
 * 
 * This file defines the roles and permissions for the NovaFuse platform.
 * It follows the principle of least privilege and implements a hierarchical
 * permission structure.
 */

/**
 * Permission definitions
 * Format: 'resource:action'
 */
const permissions = {
  // Quantum Inference permissions
  QUANTUM_INFERENCE_VIEW: 'quantum_inference:view',
  QUANTUM_INFERENCE_PREDICT: 'quantum_inference:predict',
  QUANTUM_INFERENCE_CONFIGURE: 'quantum_inference:configure',
  
  // Dashboard permissions
  DASHBOARD_VIEW: 'dashboard:view',
  DASHBOARD_VIEW_LIMITED: 'dashboard:view:limited',
  DASHBOARD_VIEW_READONLY: 'dashboard:view:readonly',
  DASHBOARD_EXPORT: 'dashboard:export',
  DASHBOARD_CONFIGURE: 'dashboard:configure',
  
  // Audit permissions
  AUDIT_VIEW: 'audit:view',
  AUDIT_EXPORT: 'audit:export',
  
  // NovaStore permissions
  STORE_VIEW: 'store:view',
  STORE_INSTALL: 'store:install',
  STORE_PUBLISH: 'store:publish',
  STORE_VERIFY: 'store:verify',
  
  // Admin permissions
  ADMIN_USERS: 'admin:users',
  ADMIN_ROLES: 'admin:roles',
  ADMIN_SYSTEM: 'admin:system'
};

/**
 * Role definitions with assigned permissions
 */
const roles = {
  // Chief Information Security Officer
  CISO: [
    permissions.QUANTUM_INFERENCE_VIEW,
    permissions.QUANTUM_INFERENCE_PREDICT,
    permissions.QUANTUM_INFERENCE_CONFIGURE,
    permissions.DASHBOARD_VIEW,
    permissions.DASHBOARD_EXPORT,
    permissions.DASHBOARD_CONFIGURE,
    permissions.AUDIT_VIEW,
    permissions.AUDIT_EXPORT,
    permissions.STORE_VIEW,
    permissions.STORE_INSTALL,
    permissions.STORE_VERIFY,
    permissions.ADMIN_ROLES,
    permissions.ADMIN_SYSTEM
  ],
  
  // Security Analyst
  SECURITY_ANALYST: [
    permissions.QUANTUM_INFERENCE_VIEW,
    permissions.QUANTUM_INFERENCE_PREDICT,
    permissions.DASHBOARD_VIEW,  // Fixed: Added dashboard view permission
    permissions.DASHBOARD_EXPORT,
    permissions.STORE_VIEW,
    permissions.STORE_INSTALL
  ],
  
  // Standard User
  USER: [
    permissions.DASHBOARD_VIEW_LIMITED,  // Fixed: Changed from full view to limited view
    permissions.STORE_VIEW
  ],
  
  // Auditor
  AUDITOR: [
    permissions.AUDIT_VIEW,
    permissions.AUDIT_EXPORT,
    permissions.DASHBOARD_VIEW_READONLY,
    permissions.QUANTUM_INFERENCE_VIEW  // Added: View-only access to quantum inference
  ],
  
  // Administrator
  ADMIN: [
    permissions.ADMIN_USERS,
    permissions.ADMIN_ROLES,
    permissions.ADMIN_SYSTEM,
    permissions.DASHBOARD_VIEW,
    permissions.DASHBOARD_CONFIGURE,
    permissions.STORE_VIEW,
    permissions.STORE_INSTALL,
    permissions.STORE_PUBLISH,
    permissions.STORE_VERIFY
  ],
  
  // Developer
  DEVELOPER: [
    permissions.QUANTUM_INFERENCE_VIEW,
    permissions.DASHBOARD_VIEW,
    permissions.STORE_VIEW,
    permissions.STORE_PUBLISH
  ]
};

/**
 * Permission checking function
 * @param {string} role - User role
 * @param {string} permission - Required permission
 * @returns {boolean} - Whether the role has the permission
 */
function hasPermission(role, permission) {
  if (!roles[role]) {
    return false;
  }
  
  return roles[role].includes(permission);
}

/**
 * Get all permissions for a role
 * @param {string} role - User role
 * @returns {Array} - Array of permissions
 */
function getRolePermissions(role) {
  return roles[role] || [];
}

/**
 * Check if a user has permission for an operation
 * @param {Object} user - User object with roles
 * @param {string} permission - Required permission
 * @returns {boolean} - Whether the user has permission
 */
function checkUserPermission(user, permission) {
  if (!user || !user.roles || !Array.isArray(user.roles)) {
    return false;
  }
  
  // Check if any of the user's roles has the required permission
  return user.roles.some(role => hasPermission(role, permission));
}

/**
 * Validate access for a user to a component
 * @param {string} userId - User ID
 * @param {string} componentType - Component type
 * @param {Object} options - Additional options
 * @returns {Object} - Access validation result
 */
function validateAccess(userId, componentType, options = {}) {
  // This would typically look up the user from a database
  // For now, we'll simulate with a mock user lookup
  const user = mockGetUser(userId);
  
  if (!user) {
    return {
      userId,
      componentType,
      action: options.action || 'view',
      allowed: false,
      reason: 'user_not_found'
    };
  }
  
  // Map component type to required permission
  const requiredPermission = mapComponentToPermission(componentType, options.action);
  
  // Check if user has permission
  const allowed = checkUserPermission(user, requiredPermission);
  
  return {
    userId,
    componentType,
    action: options.action || 'view',
    allowed,
    reason: allowed ? (user.roles.includes('CISO') ? 'admin_role' : 'component_permission') : 'access_denied',
    roles: user.roles
  };
}

/**
 * Map component type to required permission
 * @param {string} componentType - Component type
 * @param {string} action - Action being performed
 * @returns {string} - Required permission
 */
function mapComponentToPermission(componentType, action = 'view') {
  const permissionMap = {
    'quantum_inference': {
      'view': permissions.QUANTUM_INFERENCE_VIEW,
      'predict': permissions.QUANTUM_INFERENCE_PREDICT,
      'configure': permissions.QUANTUM_INFERENCE_CONFIGURE
    },
    'view:dashboard': {
      'view': permissions.DASHBOARD_VIEW,
      'export': permissions.DASHBOARD_EXPORT,
      'configure': permissions.DASHBOARD_CONFIGURE
    },
    'view:audit_logs': {
      'view': permissions.AUDIT_VIEW,
      'export': permissions.AUDIT_EXPORT
    }
  };
  
  if (permissionMap[componentType] && permissionMap[componentType][action]) {
    return permissionMap[componentType][action];
  }
  
  // Default to component:action format if not found
  return `${componentType}:${action}`;
}

/**
 * Mock function to get user by ID
 * @param {string} userId - User ID
 * @returns {Object|null} - User object or null if not found
 */
function mockGetUser(userId) {
  const mockUsers = {
    'user-ciso': {
      id: 'user-ciso',
      name: 'CISO User',
      roles: ['CISO']
    },
    'user-analyst': {
      id: 'user-analyst',
      name: 'Security Analyst',
      roles: ['SECURITY_ANALYST']
    },
    'user-standard': {
      id: 'user-standard',
      name: 'Standard User',
      roles: ['USER']
    },
    'user-auditor': {
      id: 'user-auditor',
      name: 'Auditor',
      roles: ['AUDITOR']
    },
    'user-admin': {
      id: 'user-admin',
      name: 'Administrator',
      roles: ['ADMIN']
    },
    'user-developer': {
      id: 'user-developer',
      name: 'Developer',
      roles: ['DEVELOPER']
    }
  };
  
  return mockUsers[userId] || null;
}

module.exports = {
  permissions,
  roles,
  hasPermission,
  getRolePermissions,
  checkUserPermission,
  validateAccess
};
