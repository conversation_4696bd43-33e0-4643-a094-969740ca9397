/**
 * AnimationProvider Component
 * 
 * A component that provides animation context and styles.
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { createAllAnimationClasses } from './AnimationStyles';

// Create animation context
const AnimationContext = createContext({
  enabled: true,
  reducedMotion: false,
  setEnabled: () => {},
  setReducedMotion: () => {}
});

/**
 * AnimationProvider component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {boolean} [props.enabled=true] - Whether animations are enabled
 * @param {boolean} [props.respectReducedMotion=true] - Whether to respect reduced motion preference
 * @returns {React.ReactElement} AnimationProvider component
 */
export const AnimationProvider = ({
  children,
  enabled = true,
  respectReducedMotion = true
}) => {
  // State
  const [isEnabled, setIsEnabled] = useState(enabled);
  const [reducedMotion, setReducedMotion] = useState(false);
  
  // Check for reduced motion preference
  useEffect(() => {
    if (respectReducedMotion && typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      setReducedMotion(mediaQuery.matches);
      
      const handleChange = (event) => {
        setReducedMotion(event.matches);
      };
      
      mediaQuery.addEventListener('change', handleChange);
      
      return () => {
        mediaQuery.removeEventListener('change', handleChange);
      };
    }
  }, [respectReducedMotion]);
  
  // Create context value
  const contextValue = {
    enabled: isEnabled && !reducedMotion,
    reducedMotion,
    setEnabled: setIsEnabled,
    setReducedMotion
  };
  
  // Generate animation styles
  const animationStyles = createAllAnimationClasses();
  
  return (
    <AnimationContext.Provider value={contextValue}>
      <style>
        {animationStyles}
        {reducedMotion && `
          *, *::before, *::after {
            animation-duration: 0.001ms !important;
            transition-duration: 0.001ms !important;
          }
        `}
      </style>
      {children}
    </AnimationContext.Provider>
  );
};

AnimationProvider.propTypes = {
  children: PropTypes.node.isRequired,
  enabled: PropTypes.bool,
  respectReducedMotion: PropTypes.bool
};

/**
 * useAnimation hook
 * 
 * @returns {Object} Animation context
 */
export const useAnimationContext = () => {
  const context = useContext(AnimationContext);
  
  if (!context) {
    throw new Error('useAnimationContext must be used within an AnimationProvider');
  }
  
  return context;
};

export default AnimationContext;
