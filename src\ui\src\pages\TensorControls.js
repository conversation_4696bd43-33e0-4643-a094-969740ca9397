import React, { useState } from 'react';
import { <PERSON>, Grid, <PERSON>po<PERSON>, <PERSON>ton, TextField, Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';
import { Healing as HealingIcon, Warning as DamageIcon, Add as AddIcon } from '@mui/icons-material';
import ControlGroupRenderer from '../components/controls/ControlGroupRenderer';
import ActionButton from '../components/controls/ActionButton';
import { useControl } from '../contexts/ControlContext';

function TensorControls() {
  const { getControlValue, registerTensor } = useControl();
  const [openDialog, setOpenDialog] = useState(false);
  const [newTensorId, setNewTensorId] = useState('');
  const [newTensorValues, setNewTensorValues] = useState('0.5, 0.6, 0.7, 0.8, 0.9');
  const [newTensorDomain, setNewTensorDomain] = useState('universal');

  const selectedTensorId = getControlValue('tensor-selector');
  const damageLevel = getControlValue('damage-level');

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleRegisterTensor = async () => {
    try {
      // Parse tensor values
      const values = newTensorValues.split(',').map(v => parseFloat(v.trim()));

      // Register tensor
      await registerTensor(newTensorId, { values }, newTensorDomain);

      // Close dialog
      handleCloseDialog();

      // Reset form
      setNewTensorId('');
      setNewTensorValues('0.5, 0.6, 0.7, 0.8, 0.9');
      setNewTensorDomain('universal');
    } catch (error) {
      console.error('Error registering tensor:', error);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Tensor Controls
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleOpenDialog}
        >
          Register New Tensor
        </Button>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          {/* Tensor Selector */}
          <ControlGroupRenderer
            groupId="tensor-controls"
            title="Select Tensor"
          />

          {/* Tensor Actions */}
          <ControlGroupRenderer
            groupId="tensor-actions"
            title="Tensor Actions"
          />

          <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
            <ActionButton
              action="heal-tensor"
              params={{ id: selectedTensorId }}
              label="Heal Tensor"
              icon={<HealingIcon />}
              color="success"
              sx={{ flex: 1 }}
            />

            <ActionButton
              action="damage-tensor"
              params={{ id: selectedTensorId, damageLevel }}
              label="Damage Tensor"
              icon={<DamageIcon />}
              color="error"
              sx={{ flex: 1 }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} md={8}>
          {/* Tensor Details */}
          <ControlGroupRenderer
            groupId="tensor-details"
            title="Tensor Details"
          />

          {/* Tensor Visualization */}
          <Box
            sx={{
              height: 300,
              bgcolor: 'background.paper',
              borderRadius: 1,
              mt: 3,
            }}
          >
            {selectedTensorId ? (
              <VisualizationRenderer
                visualizationType="3d_tensor_visualization"
                tensor={{
                  values: [0.5, 0.6, 0.7, 0.8, 0.9],
                  health: getControlValue('tensor-health') || 0,
                  entropyContainment: getControlValue('tensor-entropy') || 0
                }}
                dimensions={[5, 1, 1]}
                options={{
                  renderMode: 'high',
                  showAxes: true,
                  showGrid: true,
                  rotationSpeed: 1,
                  colorScheme: 'default'
                }}
                height="100%"
              />
            ) : (
              <Box
                sx={{
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography variant="body1" color="text.secondary">
                  Select a tensor to visualize
                </Typography>
              </Box>
            )}
          </Box>
        </Grid>
      </Grid>

      {/* Register Tensor Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog}>
        <DialogTitle>Register New Tensor</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            id="tensor-id"
            label="Tensor ID"
            type="text"
            fullWidth
            variant="outlined"
            value={newTensorId}
            onChange={(e) => setNewTensorId(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            id="tensor-values"
            label="Tensor Values (comma-separated)"
            type="text"
            fullWidth
            variant="outlined"
            value={newTensorValues}
            onChange={(e) => setNewTensorValues(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            id="tensor-domain"
            label="Domain"
            type="text"
            fullWidth
            variant="outlined"
            value={newTensorDomain}
            onChange={(e) => setNewTensorDomain(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleRegisterTensor} variant="contained" color="primary">
            Register
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default TensorControls;
