#!/usr/bin/env python3
"""
KetherNet Simulation Results Aggregator
Consciousness-Validated Network Performance Analysis

Author: NovaFuse Technologies
Date: January 2025
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import glob
import os
from pathlib import Path

class SimulationResultsAggregator:
    def __init__(self, results_dir="/results"):
        self.results_dir = Path(results_dir)
        self.output_dir = self.results_dir / "aggregated"
        self.output_dir.mkdir(exist_ok=True)
        
    def load_simulation_data(self):
        """Load all simulation result files"""
        json_files = glob.glob(str(self.results_dir / "kethernet_simulation_report_*.json"))
        
        all_results = []
        for file_path in json_files:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    all_results.append(data)
            except Exception as e:
                print(f"Error loading {file_path}: {e}")
        
        return all_results
    
    def create_consciousness_analysis(self, results_data):
        """Analyze consciousness filtering effectiveness"""
        consciousness_data = []
        
        for result in results_data:
            for test in result.get('detailed_results', []):
                if test.get('test') == 'consciousness_filtering':
                    consciousness_data.append({
                        'timestamp': test['timestamp'],
                        'psi_level': test['psi_level'],
                        'expected_block': test['expected_block'],
                        'actual_block': test['actual_block'],
                        'correct_decision': test['expected_block'] == test['actual_block']
                    })
        
        if not consciousness_data:
            return None
            
        df = pd.DataFrame(consciousness_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Create consciousness filtering analysis chart
        plt.figure(figsize=(12, 8))
        
        # Subplot 1: Consciousness levels vs decisions
        plt.subplot(2, 2, 1)
        scatter_colors = ['red' if not correct else 'green' for correct in df['correct_decision']]
        plt.scatter(df['psi_level'], df['actual_block'], c=scatter_colors, alpha=0.6)
        plt.xlabel('Consciousness Level (Ψ)')
        plt.ylabel('Blocked (True/False)')
        plt.title('Consciousness Filtering Decisions')
        plt.axvline(x=0.618, color='gold', linestyle='--', label='Golden Ratio Threshold')
        plt.legend()
        
        # Subplot 2: Accuracy over time
        plt.subplot(2, 2, 2)
        df_hourly = df.set_index('timestamp').resample('1H')['correct_decision'].mean()
        plt.plot(df_hourly.index, df_hourly.values, marker='o')
        plt.xlabel('Time')
        plt.ylabel('Accuracy Rate')
        plt.title('Consciousness Filtering Accuracy Over Time')
        plt.xticks(rotation=45)
        
        # Subplot 3: Distribution of consciousness levels
        plt.subplot(2, 2, 3)
        plt.hist(df['psi_level'], bins=20, alpha=0.7, edgecolor='black')
        plt.xlabel('Consciousness Level (Ψ)')
        plt.ylabel('Frequency')
        plt.title('Distribution of Tested Consciousness Levels')
        plt.axvline(x=0.618, color='gold', linestyle='--', label='Golden Ratio Threshold')
        plt.legend()
        
        # Subplot 4: Success rate by consciousness range
        plt.subplot(2, 2, 4)
        df['psi_range'] = pd.cut(df['psi_level'], bins=[0, 0.3, 0.6, 0.9, 3.0], 
                                labels=['Low (0-0.3)', 'Medium (0.3-0.6)', 'High (0.6-0.9)', 'Divine (0.9+)'])
        success_by_range = df.groupby('psi_range')['correct_decision'].mean()
        success_by_range.plot(kind='bar', color='skyblue', edgecolor='black')
        plt.xlabel('Consciousness Range')
        plt.ylabel('Success Rate')
        plt.title('Filtering Success by Consciousness Range')
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'consciousness_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        return df
    
    def create_trinity_performance_analysis(self, results_data):
        """Analyze Trinity stack performance"""
        trinity_data = []
        
        for result in results_data:
            for test in result.get('detailed_results', []):
                if test.get('test') == 'trinity_validation':
                    trinity_data.append({
                        'timestamp': test['timestamp'],
                        'component': test['component'],
                        'status_code': test['status_code'],
                        'response_time': test.get('response_time', 'N/A'),
                        'success': test['status_code'] == 200
                    })
        
        if not trinity_data:
            return None
            
        df = pd.DataFrame(trinity_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Create Trinity performance analysis
        plt.figure(figsize=(15, 10))
        
        # Subplot 1: Success rate by component
        plt.subplot(2, 3, 1)
        success_by_component = df.groupby('component')['success'].mean()
        success_by_component.plot(kind='bar', color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
        plt.xlabel('Trinity Component')
        plt.ylabel('Success Rate')
        plt.title('Trinity Stack Success Rates')
        plt.xticks(rotation=45)
        
        # Subplot 2: Response times by component (if available)
        plt.subplot(2, 3, 2)
        response_times = df[df['response_time'] != 'N/A']['response_time'].astype(float)
        if len(response_times) > 0:
            df_with_times = df[df['response_time'] != 'N/A'].copy()
            df_with_times['response_time'] = df_with_times['response_time'].astype(float)
            sns.boxplot(data=df_with_times, x='component', y='response_time')
            plt.xlabel('Trinity Component')
            plt.ylabel('Response Time (ms)')
            plt.title('Response Time Distribution')
            plt.xticks(rotation=45)
        
        # Subplot 3: Success over time
        plt.subplot(2, 3, 3)
        df_hourly = df.set_index('timestamp').resample('1H')['success'].mean()
        plt.plot(df_hourly.index, df_hourly.values, marker='o', color='green')
        plt.xlabel('Time')
        plt.ylabel('Success Rate')
        plt.title('Trinity Stack Reliability Over Time')
        plt.xticks(rotation=45)
        
        # Subplot 4: Component status distribution
        plt.subplot(2, 3, 4)
        status_counts = df['status_code'].value_counts()
        plt.pie(status_counts.values, labels=status_counts.index, autopct='%1.1f%%')
        plt.title('HTTP Status Code Distribution')
        
        # Subplot 5: Component interaction heatmap
        plt.subplot(2, 3, 5)
        component_success = df.pivot_table(values='success', index='component', 
                                         columns=df['timestamp'].dt.hour, aggfunc='mean')
        sns.heatmap(component_success, annot=True, cmap='RdYlGn', center=0.5)
        plt.xlabel('Hour of Day')
        plt.ylabel('Trinity Component')
        plt.title('Success Rate Heatmap by Hour')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'trinity_performance.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        return df
    
    def create_threat_detection_analysis(self, results_data):
        """Analyze threat detection effectiveness"""
        threat_data = []
        
        for result in results_data:
            for test in result.get('detailed_results', []):
                if test.get('test') == 'threat_detection':
                    threat_data.append({
                        'timestamp': test['timestamp'],
                        'threat_type': test['threat_type'],
                        'is_malicious': test['is_malicious'],
                        'blocked': test['blocked'],
                        'correct_decision': test['is_malicious'] == test['blocked']
                    })
        
        if not threat_data:
            return None
            
        df = pd.DataFrame(threat_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Create threat detection analysis
        plt.figure(figsize=(12, 8))
        
        # Subplot 1: Detection accuracy by threat type
        plt.subplot(2, 2, 1)
        accuracy_by_type = df.groupby('threat_type')['correct_decision'].mean()
        accuracy_by_type.plot(kind='bar', color='coral', edgecolor='black')
        plt.xlabel('Threat Type')
        plt.ylabel('Detection Accuracy')
        plt.title('Threat Detection Accuracy by Type')
        plt.xticks(rotation=45)
        
        # Subplot 2: True positives vs false positives
        plt.subplot(2, 2, 2)
        confusion_data = df.groupby(['is_malicious', 'blocked']).size().unstack(fill_value=0)
        sns.heatmap(confusion_data, annot=True, fmt='d', cmap='Blues')
        plt.xlabel('Blocked')
        plt.ylabel('Actually Malicious')
        plt.title('Threat Detection Confusion Matrix')
        
        # Subplot 3: Detection rate over time
        plt.subplot(2, 2, 3)
        df_hourly = df.set_index('timestamp').resample('1H')['correct_decision'].mean()
        plt.plot(df_hourly.index, df_hourly.values, marker='o', color='red')
        plt.xlabel('Time')
        plt.ylabel('Detection Accuracy')
        plt.title('Threat Detection Accuracy Over Time')
        plt.xticks(rotation=45)
        
        # Subplot 4: Threat type distribution
        plt.subplot(2, 2, 4)
        threat_counts = df['threat_type'].value_counts()
        plt.pie(threat_counts.values, labels=threat_counts.index, autopct='%1.1f%%')
        plt.title('Threat Type Distribution')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'threat_detection_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        return df
    
    def generate_executive_summary(self, results_data):
        """Generate executive summary report"""
        summary = {
            'total_simulations': len(results_data),
            'time_range': {
                'start': min([r['timestamp'] for r in results_data]),
                'end': max([r['timestamp'] for r in results_data])
            },
            'test_summary': {},
            'key_metrics': {}
        }
        
        # Aggregate test summaries
        all_test_summaries = {}
        for result in results_data:
            for test_type, test_summary in result.get('test_summary', {}).items():
                if test_type not in all_test_summaries:
                    all_test_summaries[test_type] = {'total': 0, 'passed': 0, 'failed': 0}
                
                all_test_summaries[test_type]['total'] += test_summary['total']
                all_test_summaries[test_type]['passed'] += test_summary['passed']
                all_test_summaries[test_type]['failed'] += test_summary['failed']
        
        # Calculate success rates
        for test_type, counts in all_test_summaries.items():
            success_rate = (counts['passed'] / counts['total']) * 100 if counts['total'] > 0 else 0
            summary['test_summary'][test_type] = {
                **counts,
                'success_rate': round(success_rate, 2)
            }
        
        # Calculate overall metrics
        total_tests = sum([s['total'] for s in all_test_summaries.values()])
        total_passed = sum([s['passed'] for s in all_test_summaries.values()])
        
        summary['key_metrics'] = {
            'overall_success_rate': round((total_passed / total_tests) * 100, 2) if total_tests > 0 else 0,
            'total_tests_run': total_tests,
            'consciousness_threshold_effectiveness': 'High' if summary['test_summary'].get('consciousness_filtering', {}).get('success_rate', 0) > 90 else 'Medium',
            'trinity_stack_reliability': 'High' if summary['test_summary'].get('trinity_validation', {}).get('success_rate', 0) > 95 else 'Medium',
            'threat_detection_accuracy': 'High' if summary['test_summary'].get('threat_detection', {}).get('success_rate', 0) > 85 else 'Medium'
        }
        
        # Save summary
        with open(self.output_dir / 'executive_summary.json', 'w') as f:
            json.dump(summary, f, indent=2)
        
        return summary
    
    def run_aggregation(self):
        """Run complete results aggregation"""
        print("📊 Starting KetherNet simulation results aggregation...")
        
        # Load all simulation data
        results_data = self.load_simulation_data()
        
        if not results_data:
            print("❌ No simulation results found")
            return
        
        print(f"📈 Found {len(results_data)} simulation reports")
        
        # Generate analyses
        consciousness_df = self.create_consciousness_analysis(results_data)
        trinity_df = self.create_trinity_performance_analysis(results_data)
        threat_df = self.create_threat_detection_analysis(results_data)
        
        # Generate executive summary
        summary = self.generate_executive_summary(results_data)
        
        print("✅ Aggregation complete!")
        print(f"📊 Results saved to: {self.output_dir}")
        print(f"🎯 Overall Success Rate: {summary['key_metrics']['overall_success_rate']}%")
        
        return summary

if __name__ == "__main__":
    aggregator = SimulationResultsAggregator()
    aggregator.run_aggregation()
