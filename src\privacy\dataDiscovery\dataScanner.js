/**
 * Data Scanner Module
 * Responsible for scanning various data sources to discover personal data
 */

const connectorFactory = require('./connectors/connectorFactory');
const privacyPatterns = require('./patterns/privacyPatterns');
const dataClassifier = require('./classification/dataClassifier');
const dataMapGenerator = require('./visualization/dataMapGenerator');
const riskCalculator = require('../common/riskCalculator');

/**
 * Data Scanner Module for discovering personal data across various sources
 */
class DataScanner {
  /**
   * Scan a data source for personal information
   * @param {Object} sourceConfig - Configuration for the data source
   * @param {string} sourceConfig.type - Type of data source (e.g., 'bigquery', 'cloudstorage', 'mysql')
   * @param {Object} sourceConfig.credentials - Credentials for accessing the data source
   * @param {string} sourceConfig.jurisdiction - Primary jurisdiction for compliance (e.g., 'gdpr', 'ccpa')
   * @param {string} sourceConfig.scanDepth - Depth of scan ('basic', 'standard', 'deep')
   * @param {number} sourceConfig.sampleSize - Number of records to sample for large datasets
   * @returns {Promise<Object>} Scan results with classified data and risk assessment
   */
  async scanDataSource(sourceConfig) {
    try {
      // Get the appropriate connector for this data source
      const connector = connectorFactory.getConnector(sourceConfig.type);
      
      // Authenticate with the data source
      await connector.authenticate(sourceConfig.credentials);
      
      // Get privacy patterns relevant to the jurisdiction
      const patterns = privacyPatterns.getRegionalPatterns(sourceConfig.jurisdiction);
      
      // Scan the data source for matches to privacy patterns
      const scanResults = await connector.scanData({
        patterns,
        depth: sourceConfig.scanDepth || 'standard',
        sampleSize: sourceConfig.sampleSize || 1000
      });
      
      // Classify the findings into categories of personal data
      const classifiedResults = await dataClassifier.classifyFindings(scanResults);
      
      // Generate a visual data map
      const dataMap = dataMapGenerator.createFromScanResults(classifiedResults);
      
      // Calculate risk score based on findings
      const riskScore = riskCalculator.calculateFromFindings(classifiedResults);
      
      return {
        classifiedResults,
        dataMap,
        riskScore,
        summary: this._generateSummary(classifiedResults, riskScore)
      };
    } catch (error) {
      console.error('Error scanning data source:', error);
      throw new Error(`Data scanning failed: ${error.message}`);
    }
  }
  
  /**
   * Generate a summary of scan results
   * @private
   * @param {Object} classifiedResults - Classified scan results
   * @param {Object} riskScore - Risk assessment scores
   * @returns {Object} Summary of findings
   */
  _generateSummary(classifiedResults, riskScore) {
    const categoryCounts = {};
    let totalFindings = 0;
    
    // Count findings by category
    classifiedResults.forEach(result => {
      if (!categoryCounts[result.category]) {
        categoryCounts[result.category] = 0;
      }
      categoryCounts[result.category]++;
      totalFindings++;
    });
    
    return {
      totalFindings,
      categoryCounts,
      riskLevel: riskScore.overallRisk,
      highRiskFindings: classifiedResults.filter(r => r.sensitivity === 'high').length,
      recommendedActions: this._generateRecommendedActions(classifiedResults, riskScore)
    };
  }
  
  /**
   * Generate recommended actions based on findings
   * @private
   * @param {Object} classifiedResults - Classified scan results
   * @param {Object} riskScore - Risk assessment scores
   * @returns {Array} List of recommended actions
   */
  _generateRecommendedActions(classifiedResults, riskScore) {
    const actions = [];
    
    // Add recommendations based on risk level
    if (riskScore.overallRisk === 'high') {
      actions.push('Conduct a full data protection impact assessment');
      actions.push('Review data retention policies for high-risk data');
    }
    
    // Add recommendations for specific data categories
    const sensitiveCategories = new Set(
      classifiedResults
        .filter(r => r.sensitivity === 'high')
        .map(r => r.category)
    );
    
    sensitiveCategories.forEach(category => {
      actions.push(`Review access controls for ${category} data`);
      actions.push(`Verify legal basis for processing ${category} data`);
    });
    
    return actions;
  }
}

module.exports = new DataScanner();
