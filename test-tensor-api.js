/**
 * Tensor API Test
 * 
 * This script tests the Tensor API endpoints.
 */

const axios = require('axios');

// API base URL
const API_BASE_URL = 'http://localhost:3000/api/tensor';

/**
 * Test the process endpoint
 */
async function testProcess() {
  console.log('\nTesting /api/tensor/process endpoint...');
  
  try {
    const response = await axios.post(`${API_BASE_URL}/process`, {
      csdeData: {
        governance: 0.8,
        dataQuality: 0.7,
        action: 'allow',
        confidence: 0.9
      },
      csfeData: {
        risk: 0.3,
        policyCompliance: 0.6,
        action: 'monitor',
        confidence: 0.8
      },
      csmeData: {
        trustFactor: 0.5,
        integrityFactor: 0.6,
        action: 'alert',
        confidence: 0.7
      }
    });
    
    console.log('Response:', response.data);
  } catch (error) {
    console.error('Error:', error.response ? error.response.data : error.message);
  }
}

/**
 * Test the process-unified endpoint
 */
async function testProcessUnified() {
  console.log('\nTesting /api/tensor/process-unified endpoint...');
  
  try {
    const response = await axios.post(`${API_BASE_URL}/process-unified`, {
      csdeData: {
        governance: 0.8,
        dataQuality: 0.7,
        action: 'allow',
        confidence: 0.9
      },
      csfeData: {
        risk: 0.3,
        policyCompliance: 0.6,
        action: 'monitor',
        confidence: 0.8
      },
      csmeData: {
        trustFactor: 0.5,
        integrityFactor: 0.6,
        action: 'alert',
        confidence: 0.7
      }
    });
    
    console.log('Response:', response.data);
  } catch (error) {
    console.error('Error:', error.response ? error.response.data : error.message);
  }
}

/**
 * Test the metrics endpoint
 */
async function testMetrics() {
  console.log('\nTesting /api/tensor/metrics endpoint...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/metrics`);
    
    console.log('Response:', response.data);
  } catch (error) {
    console.error('Error:', error.response ? error.response.data : error.message);
  }
}

/**
 * Test the metrics-unified endpoint
 */
async function testMetricsUnified() {
  console.log('\nTesting /api/tensor/metrics-unified endpoint...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/metrics-unified`);
    
    console.log('Response:', response.data);
  } catch (error) {
    console.error('Error:', error.response ? error.response.data : error.message);
  }
}

/**
 * Test the resonance endpoint
 */
async function testResonance() {
  console.log('\nTesting /api/tensor/resonance endpoint...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/resonance`);
    
    console.log('Response:', response.data);
  } catch (error) {
    console.error('Error:', error.response ? error.response.data : error.message);
  }
}

/**
 * Test the resonance-history endpoint
 */
async function testResonanceHistory() {
  console.log('\nTesting /api/tensor/resonance-history endpoint...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/resonance-history`);
    
    console.log('Response:', response.data);
  } catch (error) {
    console.error('Error:', error.response ? error.response.data : error.message);
  }
}

/**
 * Test the reset-metrics endpoint
 */
async function testResetMetrics() {
  console.log('\nTesting /api/tensor/reset-metrics endpoint...');
  
  try {
    const response = await axios.post(`${API_BASE_URL}/reset-metrics`);
    
    console.log('Response:', response.data);
  } catch (error) {
    console.error('Error:', error.response ? error.response.data : error.message);
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('Running Tensor API tests...');
  
  await testProcess();
  await testProcessUnified();
  await testMetrics();
  await testMetricsUnified();
  await testResonance();
  await testResonanceHistory();
  await testResetMetrics();
  
  console.log('\nAll tests completed!');
}

// Run all tests
runAllTests().catch(error => {
  console.error('Error running tests:', error);
});
