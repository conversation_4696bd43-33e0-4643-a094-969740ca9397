import React, { useEffect, useMemo, useRef } from 'react';
import PropTypes from 'prop-types';
import { 
  Nova3DEngine, 
  NovaQuantumMesh, 
  NovaOrbitControls,
  useNovaState
} from '@novafuse/nova-vision-3d';
import '../styles/EntropyVisualization.css';

// Default configuration for the 3D visualization
const DEFAULT_CONFIG = {
  quality: 'high',
  maxFPS: 120,
  gpuPriority: 'discrete',
  fallback: '2d',
  antialias: true,
  shadowMap: true
};

/**
 * Quantum Entropy Visualization using Nova3D
 * Renders a 3D visualization of quantum entropy data with interactive controls
 */
const EntropyVisualization = ({ 
  entropyHistory = [], 
  coherence = 1, 
  isQuantumActive = true,
  config = {},
  onNodeClick,
  className = ''
}) => {
  const engineRef = useRef(null);
  const [quantumData, setQuantumData] = useNovaState('quantumVisualization', {
    nodes: [],
    links: []
  });
  
  // Merge default config with user config
  const engineConfig = useMemo(() => ({
    ...DEFAULT_CONFIG,
    ...config
  }), [config]);
  
  // Process history data for 3D visualization
  useEffect(() => {
    if (!entropyHistory.length) return;
    
    // Transform data for 3D visualization
    const nodes = [];
    const links = [];
    
    entropyHistory.forEach((entry, index) => {
      // Create node for each data point
      nodes.push({
        id: entry.id || `node-${index}`,
        position: [
          Math.sin(index * 0.1) * 10,
          entry.entropy * 2,
          Math.cos(index * 0.1) * 10
        ],
        size: entry.coherence * 2 + 0.5,
        color: entry.mode === 'quantum' ? 
          `hsl(${200 + (entry.entropy * 100)}, 80%, 60%)` :
          `hsl(${360 - (entry.entropy * 100)}, 80%, 60%)`,
        opacity: 0.8,
        metadata: {
          timestamp: entry.timestamp,
          entropy: entry.entropy,
          coherence: entry.coherence,
          isAnomaly: entry.anomaly?.isAnomaly || false,
          mode: entry.mode
        }
      });
      
      // Create links between consecutive points
      if (index > 0) {
        links.push({
          source: nodes[index - 1].id,
          target: nodes[index].id,
          strength: 0.3,
          color: `rgba(138, 43, 226, ${0.2 + (entry.coherence * 0.8)})`
        });
      }
    });
    
    setQuantumData({ nodes, links });
  }, [entropyHistory, setQuantumData]);

  // Handle node click events
  const handleNodeClick = useCallback((node) => {
    if (onNodeClick && node && node.metadata) {
      onNodeClick(node.metadata);
    }
  }, [onNodeClick]);

  // Calculate statistics
  const stats = useMemo(() => {
    if (!entropyHistory.length) return {};
    
    const quantumEntries = entropyHistory.filter(d => d.mode === 'quantum');
    const classicalEntries = entropyHistory.filter(d => d.mode === 'classical');
    
    return {
      total: entropyHistory.length,
      quantum: quantumEntries.length,
      classical: classicalEntries.length,
      avgQuantum: quantumEntries.length > 0 
        ? quantumEntries.reduce((sum, entry) => sum + (entry.entropy || 0), 0) / quantumEntries.length 
        : 0,
      avgClassical: classicalEntries.length > 0 
        ? classicalEntries.reduce((sum, entry) => sum + (entry.entropy || 0), 0) / classicalEntries.length 
        : 0,
      anomalies: entropyHistory.filter(d => d.anomaly?.isAnomaly).length,
      lastUpdated: entropyHistory[entropyHistory.length - 1]?.timestamp || null
    };
  }, [entropyHistory]);

  return (
    <div className={`entropy-visualization ${className}`}>
      <div className="visualization-header">
        <h3>Quantum Entropy Analysis</h3>
        <div className="coherence-indicator">
          <span className="label">Coherence:</span>
          <span className="value">{coherence.toFixed(4)}</span>
          <div 
            className="coherence-bar" 
            style={{ '--coherence-level': Math.min(coherence * 100, 100) + '%' }}
          ></div>
        </div>
      </div>
      
      <div className="quantum-canvas-container">
        {entropyHistory.length === 0 ? (
          <div className="no-data">
            <p>No entropy data available</p>
            <p>Submit a task to see quantum measurements</p>
          </div>
        ) : (
          <Nova3DEngine
            ref={engineRef}
            config={engineConfig}
            className="quantum-canvas"
          >
            <NovaQuantumMesh 
              nodes={quantumData.nodes}
              links={quantumData.links}
              onClick={handleNodeClick}
              properties={{
                amplitude: 'entropy',
                frequency: 'coherence',
                phase: 'timestamp'
              }}
            />
            <NovaOrbitControls 
              enableDamping={true} 
              dampingFactor={0.05}
              autoRotate={isQuantumActive}
              autoRotateSpeed={0.5 * coherence}
            />
          </Nova3DEngine>
        )}
      </div>
      
      <div className="visualization-stats">
        <div className="stat">
          <span className="label">Total Measurements:</span>
          <span className="value">{stats.total || 0}</span>
        </div>
        <div className="stat">
          <span className="label">Quantum Entries:</span>
          <span className="value">{stats.quantum || 0}</span>
        </div>
        <div className="stat">
          <span className="label">Avg Quantum Entropy:</span>
          <span className="value">{(stats.avgQuantum || 0).toFixed(4)}</span>
        </div>
        <div className="stat">
          <span className="label">Anomalies Detected:</span>
          <span className="value">{stats.anomalies || 0}</span>
        </div>
      </div>
    </div>
  );
};

EntropyVisualization.propTypes = {
  /**
   * Array of entropy measurement objects
   * @type {Array<{
   *   id?: string,
   *   timestamp: number,
   *   entropy: number,
   *   mode: 'quantum'|'classical',
   *   coherence: number,
   *   anomaly?: { isAnomaly: boolean, confidence?: number }
   * }>}
   */
  entropyHistory: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string,
    timestamp: PropTypes.number.isRequired,
    entropy: PropTypes.number.isRequired,
    mode: PropTypes.oneOf(['quantum', 'classical']).isRequired,
    coherence: PropTypes.number,
    anomaly: PropTypes.shape({
      isAnomaly: PropTypes.bool,
      confidence: PropTypes.number
    })
  })),
  
  /**
   * Current coherence level (0-1)
   */
  coherence: PropTypes.number,
  
  /**
   * Whether quantum effects are active
   */
  isQuantumActive: PropTypes.bool,
  
  /**
   * Configuration for the 3D engine
   * @see DEFAULT_CONFIG for available options
   */
  config: PropTypes.object,
  
  /**
   * Callback when a node is clicked
   * @param {Object} nodeData - The metadata of the clicked node
   */
  onNodeClick: PropTypes.func,
  
  /**
   * Additional CSS class name
   */
  className: PropTypes.string
};

EntropyVisualization.defaultProps = {
  entropyHistory: [],
  coherence: 1,
  isQuantumActive: true,
  config: {}
};

export default EntropyVisualization;
