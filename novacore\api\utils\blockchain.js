/**
 * NovaCore Blockchain Utilities
 * 
 * This file provides utility functions for blockchain operations.
 */

const crypto = require('crypto');
const config = require('../../config');
const logger = require('../../config/logger');

/**
 * Generate a Merkle tree from a list of hashes
 * @param {Array<string>} hashes - List of hashes
 * @returns {Object} - Merkle tree with root and proof
 */
function generateMerkleTree(hashes) {
  if (!hashes || hashes.length === 0) {
    throw new Error('No hashes provided');
  }
  
  // If only one hash, it's the root
  if (hashes.length === 1) {
    return {
      root: hashes[0],
      proof: []
    };
  }
  
  // Make sure we have an even number of hashes
  const leaves = [...hashes];
  if (leaves.length % 2 !== 0) {
    leaves.push(leaves[leaves.length - 1]);
  }
  
  // Build the tree
  const tree = [leaves];
  let level = leaves;
  
  while (level.length > 1) {
    const nextLevel = [];
    
    for (let i = 0; i < level.length; i += 2) {
      const left = level[i];
      const right = i + 1 < level.length ? level[i + 1] : left;
      const hash = hashPair(left, right);
      nextLevel.push(hash);
    }
    
    tree.push(nextLevel);
    level = nextLevel;
  }
  
  // Generate proof for the first hash
  const proof = generateMerkleProof(tree, 0);
  
  return {
    root: tree[tree.length - 1][0],
    proof
  };
}

/**
 * Generate a Merkle proof for a specific leaf
 * @param {Array<Array<string>>} tree - Merkle tree
 * @param {number} index - Leaf index
 * @returns {Array<Object>} - Merkle proof
 */
function generateMerkleProof(tree, index) {
  const proof = [];
  let idx = index;
  
  for (let i = 0; i < tree.length - 1; i++) {
    const level = tree[i];
    const isRight = idx % 2 === 0;
    const siblingIdx = isRight ? idx + 1 : idx - 1;
    
    // Check if sibling exists
    if (siblingIdx < level.length) {
      proof.push({
        position: isRight ? 'right' : 'left',
        hash: level[siblingIdx]
      });
    }
    
    // Move to the next level
    idx = Math.floor(idx / 2);
  }
  
  return proof;
}

/**
 * Verify a Merkle proof
 * @param {string} leaf - Leaf hash
 * @param {Array<Object>} proof - Merkle proof
 * @param {string} root - Merkle root
 * @returns {boolean} - Verification result
 */
function verifyMerkleProof(leaf, proof, root) {
  let hash = leaf;
  
  for (const node of proof) {
    if (node.position === 'left') {
      hash = hashPair(node.hash, hash);
    } else {
      hash = hashPair(hash, node.hash);
    }
  }
  
  return hash === root;
}

/**
 * Hash a pair of hashes
 * @param {string} left - Left hash
 * @param {string} right - Right hash
 * @returns {string} - Combined hash
 */
function hashPair(left, right) {
  return crypto
    .createHash('sha256')
    .update(left + right)
    .digest('hex');
}

/**
 * Generate a random nonce
 * @param {number} length - Nonce length
 * @returns {string} - Random nonce
 */
function generateNonce(length = 16) {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Format a blockchain transaction for display
 * @param {Object} transaction - Transaction object
 * @returns {Object} - Formatted transaction
 */
function formatTransaction(transaction) {
  if (!transaction) {
    return null;
  }
  
  return {
    txHash: transaction.txHash,
    blockNumber: transaction.blockNumber,
    timestamp: transaction.timestamp,
    confirmations: transaction.confirmations,
    status: transaction.status,
    url: getTransactionUrl(transaction)
  };
}

/**
 * Get transaction URL for a specific blockchain
 * @param {Object} transaction - Transaction object
 * @returns {string} - Transaction URL
 */
function getTransactionUrl(transaction) {
  if (!transaction || !transaction.txHash) {
    return null;
  }
  
  const provider = config.blockchain.provider;
  const network = transaction.network || 'mainnet';
  
  switch (provider) {
    case 'ethereum':
      return network === 'mainnet'
        ? `https://etherscan.io/tx/${transaction.txHash}`
        : `https://${network}.etherscan.io/tx/${transaction.txHash}`;
    case 'bitcoin':
      return `https://www.blockchain.com/btc/tx/${transaction.txHash}`;
    case 'hyperledger':
      return null; // No public explorer
    default:
      return null;
  }
}

module.exports = {
  generateMerkleTree,
  generateMerkleProof,
  verifyMerkleProof,
  hashPair,
  generateNonce,
  formatTransaction,
  getTransactionUrl
};
