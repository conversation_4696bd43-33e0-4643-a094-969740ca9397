const axios = require('axios');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const registryPort = 3006;
const authPort = 3007;
const registryUrl = `http://localhost:${registryPort}`;
const authUrl = `http://localhost:${authPort}`;

// Test data
const testConnector = {
  metadata: {
    name: 'Test Connector',
    version: '1.0.0',
    category: 'Test',
    description: 'Test connector',
    author: 'NovaGRC',
    tags: ['test']
  },
  authentication: {
    type: 'API_KEY',
    fields: {
      apiKey: {
        type: 'string',
        description: 'API Key',
        required: true,
        sensitive: true
      }
    },
    testConnection: {
      endpoint: '/health',
      method: 'GET',
      expectedResponse: {
        status: 200
      }
    }
  },
  configuration: {
    baseUrl: 'http://localhost:3005',
    headers: {},
    timeout: 30000,
    retryPolicy: {
      maxRetries: 3,
      backoffStrategy: 'exponential'
    }
  },
  endpoints: [
    {
      id: 'getFindings',
      name: 'Get Findings',
      path: '/aws/securityhub/findings',
      method: 'GET',
      parameters: {
        query: {},
        path: {},
        body: {}
      },
      response: {
        successCode: 200
      }
    }
  ],
  mappings: [
    {
      sourceEndpoint: 'getFindings',
      targetSystem: 'NovaGRC',
      targetEntity: 'ComplianceFindings',
      transformations: [
        {
          source: '$.Findings[0].Id',
          target: 'findingId',
          transform: 'identity'
        }
      ]
    }
  ],
  events: {
    webhooks: [],
    polling: []
  }
};

const testCredential = {
  name: 'Test Credential',
  connectorId: '', // Will be set after connector creation
  authType: 'API_KEY',
  credentials: {
    apiKey: 'test-api-key',
    header: 'X-API-Key'
  },
  userId: 'test-user'
};

// Helper function to start a service
const startService = (scriptPath, port) => {
  const service = spawn('node', [scriptPath], {
    env: { ...process.env, PORT: port.toString() },
    stdio: 'pipe'
  });
  
  service.stdout.on('data', (data) => {
    console.log(`[${path.basename(scriptPath)}] ${data.toString().trim()}`);
  });
  
  service.stderr.on('data', (data) => {
    console.error(`[${path.basename(scriptPath)}] ERROR: ${data.toString().trim()}`);
  });
  
  return service;
};

// Helper function to wait for a service to be ready
const waitForService = async (url, maxRetries = 10, retryDelay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await axios.get(`${url}/health`);
      if (response.status === 200) {
        return true;
      }
    } catch (err) {
      console.log(`Waiting for service at ${url}... (${i + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }
  
  throw new Error(`Service at ${url} is not available after ${maxRetries} retries`);
};

// Integration tests for connector registry and authentication services
describe('Connector Registry and Authentication Integration', () => {
  let registryService;
  let authService;
  let connectorId;
  let credentialId;
  
  // Start services before all tests
  beforeAll(async () => {
    // Start connector registry service
    registryService = startService(
      path.resolve(__dirname, '../../test-connector-registry.js'),
      registryPort
    );
    
    // Start authentication service
    authService = startService(
      path.resolve(__dirname, '../../test-auth-service.js'),
      authPort
    );
    
    // Wait for services to be ready
    await Promise.all([
      waitForService(registryUrl),
      waitForService(authUrl)
    ]);
    
    console.log('Services are ready for testing');
  }, 30000);
  
  // Stop services after all tests
  afterAll(() => {
    // Stop services
    if (registryService) {
      registryService.kill();
    }
    
    if (authService) {
      authService.kill();
    }
    
    console.log('Services stopped');
  });
  
  // Test creating a connector and credential
  describe('Create Connector and Credential', () => {
    it('should create a connector in the registry', async () => {
      const response = await axios.post(`${registryUrl}/connectors`, testConnector);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      expect(response.data).toHaveProperty('metadata.name', 'Test Connector');
      
      // Save connector ID for later tests
      connectorId = response.data.id;
      console.log(`Created connector with ID: ${connectorId}`);
    });
    
    it('should create a credential in the auth service', async () => {
      // Update test credential with connector ID
      testCredential.connectorId = connectorId;
      
      const response = await axios.post(`${authUrl}/credentials`, testCredential);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      expect(response.data).toHaveProperty('name', 'Test Credential');
      expect(response.data).toHaveProperty('connectorId', connectorId);
      
      // Save credential ID for later tests
      credentialId = response.data.id;
      console.log(`Created credential with ID: ${credentialId}`);
    });
  });
  
  // Test retrieving connector and credential
  describe('Retrieve Connector and Credential', () => {
    it('should retrieve the connector from the registry', async () => {
      const response = await axios.get(`${registryUrl}/connectors/${connectorId}`);
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('id', connectorId);
      expect(response.data).toHaveProperty('metadata.name', 'Test Connector');
    });
    
    it('should retrieve the credential from the auth service', async () => {
      const response = await axios.get(`${authUrl}/credentials/${credentialId}`);
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('id', credentialId);
      expect(response.data).toHaveProperty('name', 'Test Credential');
      expect(response.data).toHaveProperty('connectorId', connectorId);
      
      // Ensure sensitive data is not returned
      expect(response.data).not.toHaveProperty('encryptedCredentials');
      expect(response.data).not.toHaveProperty('credentials');
    });
    
    it('should retrieve decrypted credentials for internal use', async () => {
      const response = await axios.get(`${authUrl}/credentials/${credentialId}/decrypt`);
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('id', credentialId);
      expect(response.data).toHaveProperty('authType', 'API_KEY');
      expect(response.data).toHaveProperty('credentials');
      expect(response.data.credentials).toHaveProperty('apiKey', 'test-api-key');
      expect(response.data.credentials).toHaveProperty('header', 'X-API-Key');
    });
  });
  
  // Test updating connector and credential
  describe('Update Connector and Credential', () => {
    it('should update the connector in the registry', async () => {
      const response = await axios.put(`${registryUrl}/connectors/${connectorId}`, {
        metadata: {
          name: 'Updated Connector',
          description: 'Updated description'
        }
      });
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('id', connectorId);
      expect(response.data).toHaveProperty('metadata.name', 'Updated Connector');
      expect(response.data).toHaveProperty('metadata.description', 'Updated description');
    });
    
    it('should update the credential in the auth service', async () => {
      const response = await axios.put(`${authUrl}/credentials/${credentialId}`, {
        name: 'Updated Credential',
        credentials: {
          apiKey: 'updated-api-key',
          header: 'X-API-Key'
        }
      });
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('id', credentialId);
      expect(response.data).toHaveProperty('name', 'Updated Credential');
      
      // Verify the credentials were updated
      const decryptResponse = await axios.get(`${authUrl}/credentials/${credentialId}/decrypt`);
      expect(decryptResponse.data.credentials).toHaveProperty('apiKey', 'updated-api-key');
    });
  });
  
  // Test listing connectors and credentials
  describe('List Connectors and Credentials', () => {
    it('should list all connectors in the registry', async () => {
      const response = await axios.get(`${registryUrl}/connectors`);
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data.length).toBeGreaterThan(0);
      
      // Find our test connector
      const connector = response.data.find(c => c.id === connectorId);
      expect(connector).toBeDefined();
      expect(connector).toHaveProperty('metadata.name', 'Updated Connector');
    });
    
    it('should list all credentials for a user', async () => {
      const response = await axios.get(`${authUrl}/credentials?userId=test-user`);
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data.length).toBeGreaterThan(0);
      
      // Find our test credential
      const credential = response.data.find(c => c.id === credentialId);
      expect(credential).toBeDefined();
      expect(credential).toHaveProperty('name', 'Updated Credential');
      
      // Ensure sensitive data is not returned
      expect(credential).not.toHaveProperty('encryptedCredentials');
      expect(credential).not.toHaveProperty('credentials');
    });
  });
  
  // Test error handling
  describe('Error Handling', () => {
    it('should handle non-existent connector', async () => {
      try {
        await axios.get(`${registryUrl}/connectors/non-existent-id`);
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(404);
        expect(err.response.data).toHaveProperty('error', 'Connector not found');
      }
    });
    
    it('should handle non-existent credential', async () => {
      try {
        await axios.get(`${authUrl}/credentials/non-existent-id`);
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(404);
        expect(err.response.data).toHaveProperty('error', 'Credential not found');
      }
    });
    
    it('should require userId when listing credentials', async () => {
      try {
        await axios.get(`${authUrl}/credentials`);
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(400);
        expect(err.response.data).toHaveProperty('error', 'userId is required');
      }
    });
  });
  
  // Test deleting connector and credential
  describe('Delete Connector and Credential', () => {
    it('should delete the credential from the auth service', async () => {
      const response = await axios.delete(`${authUrl}/credentials/${credentialId}`);
      expect(response.status).toBe(204);
      
      // Verify the credential was deleted
      try {
        await axios.get(`${authUrl}/credentials/${credentialId}`);
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(404);
      }
    });
    
    it('should delete the connector from the registry', async () => {
      const response = await axios.delete(`${registryUrl}/connectors/${connectorId}`);
      expect(response.status).toBe(204);
      
      // Verify the connector was deleted
      try {
        await axios.get(`${registryUrl}/connectors/${connectorId}`);
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(404);
      }
    });
  });
});
