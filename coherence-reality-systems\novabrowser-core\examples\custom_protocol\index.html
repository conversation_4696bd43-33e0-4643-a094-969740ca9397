<!-- Copyright 2020-2023 Tauri Programme within The Commons Conservancy
     SPDX-License-Identifier: Apache-2.0
     SPDX-License-Identifier: MIT -->

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  </head>

  <body>
    <h1>Welcome to WRY!</h1>
    <p>Page 1</p>
    <textarea></textarea>
    <p id="keypresses"></p>
    <a href="/subpage.html">Link</a>
    <script type="text/javascript" src="/script.js"></script>
    <script type="text/javascript">
      document.body.addEventListener("keydown", (event) => {
        document.querySelector("#keypresses").innerHTML += " " + event.key;
      });
    </script>
  </body>
</html>
