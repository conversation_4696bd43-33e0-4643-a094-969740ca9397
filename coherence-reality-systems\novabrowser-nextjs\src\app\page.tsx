'use client'

import { useState, useEffect } from 'react'
import <PERSON><PERSON><PERSON><PERSON>ead<PERSON> from './components/BrowserHeader'
import ServiceCards from './components/ServiceCards'
import WebsiteFrame from './components/WebsiteFrame'
import Sidebar from './components/Sidebar'

export default function NovaBrowser() {
  const [currentUrl, setCurrentUrl] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showHomePage, setShowHomePage] = useState(true)
  const [coherenceData, setCoherenceData] = useState({
    overall: 95,
    structural: 98,
    functional: 94,
    relational: 93,
    accessibility: 98,
    security: 'DIVINE' as const,
    threats: 0,
    analysisTime: 12
  })

  const handleNavigate = (url: string) => {
    if (!url.trim()) return

    setIsLoading(true)
    setShowHomePage(false)
    setCurrentUrl(url)

    // Simulate page load and analysis
    setTimeout(() => {
      setIsLoading(false)
      runAnalysis(url)
    }, 2000)
  }

  const runAnalysis = async (url: string) => {
    // Simulate coherence analysis
    const mockData = {
      overall: Math.floor(Math.random() * 40) + 60, // 60-100%
      structural: Math.floor(Math.random() * 30) + 70,
      functional: Math.floor(Math.random() * 30) + 70,
      relational: Math.floor(Math.random() * 30) + 70,
      accessibility: Math.floor(Math.random() * 30) + 70,
      security: ['LOW', 'MEDIUM', 'HIGH', 'DIVINE'][Math.floor(Math.random() * 4)] as const,
      threats: Math.floor(Math.random() * 3),
      analysisTime: Math.floor(Math.random() * 50) + 10
    }

    setCoherenceData(mockData)
  }

  const handleHome = () => {
    setShowHomePage(true)
    setCurrentUrl('')
    setCoherenceData({
      overall: 95,
      structural: 98,
      functional: 94,
      relational: 93,
      accessibility: 98,
      security: 'DIVINE',
      threats: 0,
      analysisTime: 12
    })
  }

  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-gray-900 via-gray-800 to-indigo-900">
      {/* Browser Header */}
      <BrowserHeader 
        currentUrl={currentUrl}
        onNavigate={handleNavigate}
        onHome={handleHome}
        coherenceData={coherenceData}
      />

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Sidebar */}
        <Sidebar coherenceData={coherenceData} />

        {/* Main Content Area */}
        <div className="flex-1 relative">
          {showHomePage ? (
            <ServiceCards onNavigate={handleNavigate} />
          ) : (
            <WebsiteFrame 
              url={currentUrl}
              isLoading={isLoading}
              coherenceData={coherenceData}
            />
          )}
        </div>
      </div>
    </div>
  )
}
