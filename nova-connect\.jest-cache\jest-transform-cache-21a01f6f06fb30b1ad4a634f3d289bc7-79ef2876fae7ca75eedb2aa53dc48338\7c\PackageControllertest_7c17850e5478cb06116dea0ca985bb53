04121f777650bb9b26c7490dae75dbcc
// Mock the FeatureFlagService
_getJestObj().mock('../../../api/services/FeatureFlagService');
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Package Controller Tests
 */

const PackageController = require('../../../api/controllers/PackageController');
const FeatureFlagService = require('../../../api/services/FeatureFlagService');
describe('PackageController', () => {
  let req, res, next;
  beforeEach(() => {
    // Mock request, response, and next
    req = {
      params: {},
      body: {}
    };
    res = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis(),
      end: jest.fn()
    };
    next = jest.fn();

    // Mock FeatureFlagService methods
    FeatureFlagService.mockImplementation(() => ({
      getAllPackages: jest.fn().mockResolvedValue([{
        id: 'core',
        name: 'Core Package'
      }, {
        id: 'enterprise',
        name: 'Enterprise Package'
      }]),
      getPackageById: jest.fn().mockResolvedValue({
        id: 'enterprise',
        name: 'Enterprise Package',
        features: ['feature1', 'feature2', 'feature3'],
        limits: {
          connections: 100
        }
      }),
      createPackage: jest.fn().mockResolvedValue({
        id: 'test-package',
        name: 'Test Package',
        features: ['test.feature1', 'test.feature2'],
        limits: {
          connections: 50
        }
      }),
      updatePackage: jest.fn().mockResolvedValue({
        id: 'enterprise',
        name: 'Updated Enterprise Package',
        features: ['feature1', 'feature2', 'feature3'],
        limits: {
          connections: 100
        }
      }),
      deletePackage: jest.fn().mockResolvedValue(true),
      getTenantPackage: jest.fn().mockResolvedValue({
        id: 'enterprise',
        name: 'Enterprise Package',
        features: ['feature1', 'feature2', 'feature3'],
        limits: {
          connections: 100
        }
      }),
      setTenantPackage: jest.fn().mockResolvedValue({
        tenantId: 'test-tenant',
        packageId: 'enterprise',
        customFeatures: ['custom.feature1'],
        customLimits: {
          connections: 200
        }
      }),
      clearCache: jest.fn()
    }));
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  test('getAllPackages should return all packages', async () => {
    await PackageController.getAllPackages(req, res, next);
    expect(res.json).toHaveBeenCalledWith([{
      id: 'core',
      name: 'Core Package'
    }, {
      id: 'enterprise',
      name: 'Enterprise Package'
    }]);
  });
  test('getPackageById should return package by ID', async () => {
    req.params.id = 'enterprise';
    await PackageController.getPackageById(req, res, next);
    expect(res.json).toHaveBeenCalledWith({
      id: 'enterprise',
      name: 'Enterprise Package',
      features: ['feature1', 'feature2', 'feature3'],
      limits: {
        connections: 100
      }
    });
  });
  test('getPackageById should handle not found error', async () => {
    req.params.id = 'non-existent';

    // Mock getPackageById to throw not found error
    FeatureFlagService.mockImplementation(() => ({
      getPackageById: jest.fn().mockRejectedValue(new Error('Package with ID non-existent not found'))
    }));
    await PackageController.getPackageById(req, res, next);
    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Not Found',
      message: 'Package with ID non-existent not found'
    });
  });
  test('createPackage should create a new package', async () => {
    req.body = {
      id: 'test-package',
      name: 'Test Package',
      tier: 'test',
      features: ['test.feature1', 'test.feature2'],
      limits: {
        connections: 50
      }
    };
    await PackageController.createPackage(req, res, next);
    expect(res.status).toHaveBeenCalledWith(201);
    expect(res.json).toHaveBeenCalledWith({
      id: 'test-package',
      name: 'Test Package',
      features: ['test.feature1', 'test.feature2'],
      limits: {
        connections: 50
      }
    });
  });
  test('createPackage should validate required fields', async () => {
    req.body = {
      name: 'Test Package'
      // Missing id, tier, features, limits
    };
    await PackageController.createPackage(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Validation Error',
      message: 'Package ID is required'
    });
  });
  test('updatePackage should update a package', async () => {
    req.params.id = 'enterprise';
    req.body = {
      name: 'Updated Enterprise Package'
    };
    await PackageController.updatePackage(req, res, next);
    expect(res.json).toHaveBeenCalledWith({
      id: 'enterprise',
      name: 'Updated Enterprise Package',
      features: ['feature1', 'feature2', 'feature3'],
      limits: {
        connections: 100
      }
    });
  });
  test('updatePackage should handle not found error', async () => {
    req.params.id = 'non-existent';
    req.body = {
      name: 'Updated Package'
    };

    // Mock updatePackage to throw not found error
    FeatureFlagService.mockImplementation(() => ({
      updatePackage: jest.fn().mockRejectedValue(new Error('Package with ID non-existent not found'))
    }));
    await PackageController.updatePackage(req, res, next);
    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Not Found',
      message: 'Package with ID non-existent not found'
    });
  });
  test('deletePackage should delete a package', async () => {
    req.params.id = 'enterprise';
    await PackageController.deletePackage(req, res, next);
    expect(res.status).toHaveBeenCalledWith(204);
    expect(res.end).toHaveBeenCalled();
  });
  test('deletePackage should handle not found error', async () => {
    req.params.id = 'non-existent';

    // Mock deletePackage to throw not found error
    FeatureFlagService.mockImplementation(() => ({
      deletePackage: jest.fn().mockRejectedValue(new Error('Package with ID non-existent not found'))
    }));
    await PackageController.deletePackage(req, res, next);
    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Not Found',
      message: 'Package with ID non-existent not found'
    });
  });
  test('getTenantPackage should return tenant package', async () => {
    req.params.tenantId = 'test-tenant';
    await PackageController.getTenantPackage(req, res, next);
    expect(res.json).toHaveBeenCalledWith({
      id: 'enterprise',
      name: 'Enterprise Package',
      features: ['feature1', 'feature2', 'feature3'],
      limits: {
        connections: 100
      }
    });
  });
  test('setTenantPackage should set tenant package', async () => {
    req.params.tenantId = 'test-tenant';
    req.body = {
      packageId: 'enterprise',
      customFeatures: ['custom.feature1'],
      customLimits: {
        connections: 200
      }
    };
    await PackageController.setTenantPackage(req, res, next);
    expect(res.json).toHaveBeenCalledWith({
      tenantId: 'test-tenant',
      packageId: 'enterprise',
      customFeatures: ['custom.feature1'],
      customLimits: {
        connections: 200
      }
    });
  });
  test('setTenantPackage should validate required fields', async () => {
    req.params.tenantId = 'test-tenant';
    req.body = {
      // Missing packageId
      customFeatures: ['custom.feature1'],
      customLimits: {
        connections: 200
      }
    };
    await PackageController.setTenantPackage(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Validation Error',
      message: 'Package ID is required'
    });
  });
  test('clearCache should clear cache', async () => {
    await PackageController.clearCache(req, res, next);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Cache cleared successfully'
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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