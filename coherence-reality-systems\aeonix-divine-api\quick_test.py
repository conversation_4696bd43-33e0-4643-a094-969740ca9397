import requests
import json

# Test the API
try:
    # Test status
    response = requests.get("http://localhost:8000/divine/status")
    print("Status:", response.json())
    
    # Test NEPI
    payload = {
        "series": [25.06, 26.20, 27.80, 28.34, 29.10],
        "timeframe": "4h",
        "current_price": 28.34
    }
    response = requests.post("http://localhost:8000/api/harmonics", json=payload)
    print("NEPI:", response.json())
    
    # Test Divine Simulation
    payload = {
        "ticker": "GME",
        "prophecy_event": "<PERSON><PERSON> resigns",
        "amplification": 1.63
    }
    response = requests.post("http://localhost:8000/api/divine-simulation", json=payload)
    print("Divine Simulation:", response.json())
    
except Exception as e:
    print("Error:", e)
