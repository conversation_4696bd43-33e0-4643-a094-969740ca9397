/**
 * Audit Logger
 *
 * This module provides audit logging capabilities for the Finite Universe
 * Principle defense system, enabling comprehensive tracking of all operations.
 */

const EventEmitter = require('events');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

/**
 * AuditLogger class
 * 
 * Provides audit logging capabilities for tracking operations.
 */
class AuditLogger extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      enableConsole: options.enableConsole !== undefined ? options.enableConsole : true,
      enableFile: options.enableFile !== undefined ? options.enableFile : true,
      logLevel: options.logLevel || 'info', // 'debug', 'info', 'warn', 'error', 'critical'
      logFormat: options.logFormat || 'json', // 'json', 'text'
      logPath: options.logPath || './logs',
      logFilename: options.logFilename || 'audit.log',
      maxLogSize: options.maxLogSize || 10 * 1024 * 1024, // 10MB
      maxLogFiles: options.maxLogFiles || 5,
      logRotationInterval: options.logRotationInterval || 24 * 60 * 60 * 1000, // 24 hours
      ...options
    };

    // Initialize log levels
    this.logLevels = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3,
      critical: 4
    };

    // Initialize log file
    this.logFile = null;
    this.logSize = 0;
    this.lastRotation = Date.now();

    // Initialize log buffer
    this.logBuffer = [];
    this.flushInterval = null;

    // Initialize log file if enabled
    if (this.options.enableFile) {
      this._initializeLogFile();
    }

    // Start flush interval
    this.flushInterval = setInterval(() => {
      this._flushLogBuffer();
    }, 1000); // Flush every second

    if (this.options.enableLogging) {
      console.log('AuditLogger initialized with options:', this.options);
    }
  }

  /**
   * Initialize log file
   * @private
   */
  _initializeLogFile() {
    try {
      // Create log directory if not exists
      if (!fs.existsSync(this.options.logPath)) {
        fs.mkdirSync(this.options.logPath, { recursive: true });
      }

      // Get log file path
      const logFilePath = path.join(this.options.logPath, this.options.logFilename);

      // Check if log file exists
      if (fs.existsSync(logFilePath)) {
        // Get log file stats
        const stats = fs.statSync(logFilePath);
        this.logSize = stats.size;

        // Check if log file needs rotation
        if (this.logSize >= this.options.maxLogSize || Date.now() - stats.mtime.getTime() >= this.options.logRotationInterval) {
          this._rotateLogFile();
        }
      }

      // Open log file for appending
      this.logFile = fs.createWriteStream(logFilePath, { flags: 'a' });

      // Handle log file errors
      this.logFile.on('error', (error) => {
        console.error('Error writing to log file:', error);
      });

      if (this.options.enableLogging) {
        console.log(`Log file initialized: ${logFilePath}`);
      }
    } catch (error) {
      console.error('Error initializing log file:', error);
      this.options.enableFile = false;
    }
  }

  /**
   * Rotate log file
   * @private
   */
  _rotateLogFile() {
    try {
      // Close current log file
      if (this.logFile) {
        this.logFile.end();
        this.logFile = null;
      }

      // Get log file path
      const logFilePath = path.join(this.options.logPath, this.options.logFilename);

      // Check if log file exists
      if (fs.existsSync(logFilePath)) {
        // Get timestamp
        const timestamp = new Date().toISOString().replace(/:/g, '-');

        // Create rotated log file name
        const rotatedLogFilePath = path.join(this.options.logPath, `${path.basename(this.options.logFilename, path.extname(this.options.logFilename))}-${timestamp}${path.extname(this.options.logFilename)}`);

        // Rename current log file
        fs.renameSync(logFilePath, rotatedLogFilePath);

        if (this.options.enableLogging) {
          console.log(`Log file rotated: ${rotatedLogFilePath}`);
        }

        // Clean up old log files
        this._cleanupOldLogFiles();
      }

      // Reset log size
      this.logSize = 0;
      this.lastRotation = Date.now();

      // Open new log file
      this.logFile = fs.createWriteStream(logFilePath, { flags: 'a' });

      // Handle log file errors
      this.logFile.on('error', (error) => {
        console.error('Error writing to log file:', error);
      });
    } catch (error) {
      console.error('Error rotating log file:', error);
    }
  }

  /**
   * Clean up old log files
   * @private
   */
  _cleanupOldLogFiles() {
    try {
      // Get log files
      const logFiles = fs.readdirSync(this.options.logPath)
        .filter(file => file.startsWith(path.basename(this.options.logFilename, path.extname(this.options.logFilename))) && file !== this.options.logFilename)
        .map(file => ({
          name: file,
          path: path.join(this.options.logPath, file),
          time: fs.statSync(path.join(this.options.logPath, file)).mtime.getTime()
        }))
        .sort((a, b) => b.time - a.time); // Sort by time (newest first)

      // Delete old log files
      if (logFiles.length > this.options.maxLogFiles) {
        for (let i = this.options.maxLogFiles; i < logFiles.length; i++) {
          fs.unlinkSync(logFiles[i].path);

          if (this.options.enableLogging) {
            console.log(`Old log file deleted: ${logFiles[i].path}`);
          }
        }
      }
    } catch (error) {
      console.error('Error cleaning up old log files:', error);
    }
  }

  /**
   * Flush log buffer
   * @private
   */
  _flushLogBuffer() {
    if (this.logBuffer.length === 0) {
      return;
    }

    // Get log entries
    const logEntries = [...this.logBuffer];
    this.logBuffer = [];

    // Write to file
    if (this.options.enableFile && this.logFile) {
      try {
        // Check if log file needs rotation
        if (this.logSize >= this.options.maxLogSize || Date.now() - this.lastRotation >= this.options.logRotationInterval) {
          this._rotateLogFile();
        }

        // Write log entries
        for (const entry of logEntries) {
          const logString = this.options.logFormat === 'json'
            ? JSON.stringify(entry) + '\n'
            : this._formatLogEntry(entry) + '\n';

          this.logFile.write(logString);
          this.logSize += logString.length;
        }
      } catch (error) {
        console.error('Error writing to log file:', error);
      }
    }
  }

  /**
   * Format log entry as text
   * @param {Object} entry - Log entry
   * @returns {string} - Formatted log entry
   * @private
   */
  _formatLogEntry(entry) {
    return `[${entry.timestamp}] [${entry.level.toUpperCase()}] [${entry.category}] [${entry.userId}] ${entry.message}`;
  }

  /**
   * Log an event
   * @param {string} level - Log level
   * @param {string} category - Log category
   * @param {string} message - Log message
   * @param {Object} data - Additional data
   * @param {string} userId - User ID
   * @returns {Object} - Log entry
   */
  log(level, category, message, data = {}, userId = 'system') {
    // Check if log level is enabled
    if (this.logLevels[level] < this.logLevels[this.options.logLevel]) {
      return null;
    }

    // Create log entry
    const entry = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      data,
      userId
    };

    // Add to buffer
    this.logBuffer.push(entry);

    // Log to console if enabled
    if (this.options.enableConsole) {
      const consoleMessage = this.options.logFormat === 'json'
        ? JSON.stringify(entry)
        : this._formatLogEntry(entry);

      switch (level) {
        case 'debug':
          console.debug(consoleMessage);
          break;
        case 'info':
          console.info(consoleMessage);
          break;
        case 'warn':
          console.warn(consoleMessage);
          break;
        case 'error':
        case 'critical':
          console.error(consoleMessage);
          break;
        default:
          console.log(consoleMessage);
      }
    }

    // Emit log event
    this.emit('log', entry);

    return entry;
  }

  /**
   * Log a debug event
   * @param {string} category - Log category
   * @param {string} message - Log message
   * @param {Object} data - Additional data
   * @param {string} userId - User ID
   * @returns {Object} - Log entry
   */
  debug(category, message, data = {}, userId = 'system') {
    return this.log('debug', category, message, data, userId);
  }

  /**
   * Log an info event
   * @param {string} category - Log category
   * @param {string} message - Log message
   * @param {Object} data - Additional data
   * @param {string} userId - User ID
   * @returns {Object} - Log entry
   */
  info(category, message, data = {}, userId = 'system') {
    return this.log('info', category, message, data, userId);
  }

  /**
   * Log a warning event
   * @param {string} category - Log category
   * @param {string} message - Log message
   * @param {Object} data - Additional data
   * @param {string} userId - User ID
   * @returns {Object} - Log entry
   */
  warn(category, message, data = {}, userId = 'system') {
    return this.log('warn', category, message, data, userId);
  }

  /**
   * Log an error event
   * @param {string} category - Log category
   * @param {string} message - Log message
   * @param {Object} data - Additional data
   * @param {string} userId - User ID
   * @returns {Object} - Log entry
   */
  error(category, message, data = {}, userId = 'system') {
    return this.log('error', category, message, data, userId);
  }

  /**
   * Log a critical event
   * @param {string} category - Log category
   * @param {string} message - Log message
   * @param {Object} data - Additional data
   * @param {string} userId - User ID
   * @returns {Object} - Log entry
   */
  critical(category, message, data = {}, userId = 'system') {
    return this.log('critical', category, message, data, userId);
  }

  /**
   * Dispose resources
   */
  dispose() {
    // Flush log buffer
    this._flushLogBuffer();

    // Clear flush interval
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }

    // Close log file
    if (this.logFile) {
      this.logFile.end();
      this.logFile = null;
    }

    if (this.options.enableLogging) {
      console.log('AuditLogger disposed');
    }
  }
}

/**
 * Create an audit logger with recommended settings
 * @param {Object} options - Configuration options
 * @returns {AuditLogger} - Configured audit logger
 */
function createAuditLogger(options = {}) {
  return new AuditLogger({
    enableLogging: true,
    enableConsole: true,
    enableFile: true,
    logLevel: 'info',
    logFormat: 'json',
    ...options
  });
}

module.exports = {
  AuditLogger,
  createAuditLogger
};
