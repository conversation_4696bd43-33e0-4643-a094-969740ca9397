/**
 * NovaVision - Report Example
 *
 * This example demonstrates how to use NovaVision to generate and render a report.
 */

const { novaVision } = require('../index');

// Create a report configuration
const reportConfig = {
  id: 'compliance-report',
  title: 'Quarterly Compliance Report',
  description: 'Compliance status report for Q4 2023',
  parameters: [
    {
      id: 'quarter',
      type: 'select',
      label: 'Quarter',
      options: [
        { value: 'Q1', label: 'Q1 2023' },
        { value: 'Q2', label: 'Q2 2023' },
        { value: 'Q3', label: 'Q3 2023' },
        { value: 'Q4', label: 'Q4 2023' }
      ],
      defaultValue: 'Q4'
    },
    {
      id: 'department',
      type: 'select',
      label: 'Department',
      options: [
        { value: 'all', label: 'All Departments' },
        { value: 'it', label: 'IT' },
        { value: 'finance', label: 'Finance' },
        { value: 'hr', label: 'Human Resources' },
        { value: 'legal', label: 'Legal' }
      ],
      defaultValue: 'all'
    }
  ],
  sections: [
    {
      id: 'executive-summary',
      title: 'Executive Summary',
      elements: [
        {
          id: 'summary-text',
          type: 'text',
          textConfig: {
            content: 'This report provides an overview of the organization\'s compliance status for Q4 2023. Overall compliance has improved by 7% compared to the previous quarter, with significant improvements in GDPR and PCI DSS compliance. However, there are still some areas that require attention, particularly in the IT department.',
            format: 'paragraph'
          }
        },
        {
          id: 'compliance-score',
          type: 'chart',
          title: 'Compliance Score Trend',
          chartConfig: {
            type: 'line',
            data: {
              labels: ['Q1 2023', 'Q2 2023', 'Q3 2023', 'Q4 2023'],
              datasets: [{
                label: 'Compliance Score',
                data: [72, 75, 78, 85],
                borderColor: '#0A84FF',
                tension: 0.1
              }]
            },
            options: {
              scales: {
                y: {
                  beginAtZero: true,
                  max: 100
                }
              }
            }
          }
        }
      ]
    },
    {
      id: 'compliance-by-framework',
      title: 'Compliance by Framework',
      elements: [
        {
          id: 'framework-text',
          type: 'text',
          textConfig: {
            content: 'The organization is subject to multiple regulatory frameworks. This section breaks down compliance status by framework.',
            format: 'paragraph'
          }
        },
        {
          id: 'framework-chart',
          type: 'chart',
          title: 'Compliance by Framework',
          chartConfig: {
            type: 'bar',
            data: {
              labels: ['GDPR', 'PCI DSS', 'HIPAA', 'SOX', 'ISO 27001'],
              datasets: [
                {
                  label: 'Current Quarter',
                  data: [92, 78, 85, 90, 88],
                  backgroundColor: '#0A84FF'
                },
                {
                  label: 'Previous Quarter',
                  data: [85, 70, 82, 88, 85],
                  backgroundColor: '#8E8E93'
                }
              ]
            },
            options: {
              scales: {
                y: {
                  beginAtZero: true,
                  max: 100
                }
              }
            }
          }
        },
        {
          id: 'framework-table',
          type: 'table',
          title: 'Compliance Details by Framework',
          tableConfig: {
            columns: [
              { id: 'framework', label: 'Framework', type: 'string' },
              { id: 'score', label: 'Score', type: 'number' },
              { id: 'previousScore', label: 'Previous Score', type: 'number' },
              { id: 'change', label: 'Change', type: 'number' },
              { id: 'status', label: 'Status', type: 'string' }
            ],
            data: [
              { framework: 'GDPR', score: 92, previousScore: 85, change: 7, status: 'Compliant' },
              { framework: 'PCI DSS', score: 78, previousScore: 70, change: 8, status: 'Partially Compliant' },
              { framework: 'HIPAA', score: 85, previousScore: 82, change: 3, status: 'Compliant' },
              { framework: 'SOX', score: 90, previousScore: 88, change: 2, status: 'Compliant' },
              { framework: 'ISO 27001', score: 88, previousScore: 85, change: 3, status: 'Compliant' }
            ]
          }
        }
      ]
    },
    {
      id: 'issues-and-risks',
      title: 'Issues and Risks',
      elements: [
        {
          id: 'issues-text',
          type: 'text',
          textConfig: {
            content: 'This section highlights key compliance issues and risks identified during the quarter.',
            format: 'paragraph'
          }
        },
        {
          id: 'issues-by-severity',
          type: 'chart',
          title: 'Issues by Severity',
          chartConfig: {
            type: 'pie',
            data: {
              labels: ['Critical', 'High', 'Medium', 'Low'],
              datasets: [{
                data: [3, 8, 15, 22],
                backgroundColor: ['#FF4136', '#FF851B', '#FFDC00', '#2ECC40']
              }]
            }
          }
        },
        {
          id: 'critical-issues',
          type: 'table',
          title: 'Critical and High Severity Issues',
          tableConfig: {
            columns: [
              { id: 'id', label: 'ID', type: 'string' },
              { id: 'title', label: 'Title', type: 'string' },
              { id: 'severity', label: 'Severity', type: 'string' },
              { id: 'department', label: 'Department', type: 'string' },
              { id: 'framework', label: 'Framework', type: 'string' },
              { id: 'status', label: 'Status', type: 'string' }
            ],
            data: [
              { id: 'ISSUE-001', title: 'Missing data encryption', severity: 'Critical', department: 'IT', framework: 'PCI DSS', status: 'Open' },
              { id: 'ISSUE-002', title: 'Incomplete access controls', severity: 'High', department: 'IT', framework: 'ISO 27001', status: 'In Progress' },
              { id: 'ISSUE-003', title: 'Insufficient audit logs', severity: 'High', department: 'Finance', framework: 'SOX', status: 'In Progress' },
              { id: 'ISSUE-004', title: 'Outdated data processing agreement', severity: 'Critical', department: 'Legal', framework: 'GDPR', status: 'Open' },
              { id: 'ISSUE-005', title: 'Inadequate security testing', severity: 'Critical', department: 'IT', framework: 'PCI DSS', status: 'Open' }
            ]
          }
        }
      ]
    },
    {
      id: 'recommendations',
      title: 'Recommendations',
      elements: [
        {
          id: 'recommendations-text',
          type: 'text',
          textConfig: {
            content: 'Based on the findings in this report, the following recommendations are provided to improve compliance:',
            format: 'paragraph'
          }
        },
        {
          id: 'recommendations-list',
          type: 'text',
          textConfig: {
            content: `
1. **Implement Data Encryption**: Address the critical issue of missing data encryption in the IT department to comply with PCI DSS requirements.

2. **Enhance Access Controls**: Complete the implementation of access controls to meet ISO 27001 requirements.

3. **Improve Audit Logging**: Enhance audit logging capabilities in financial systems to comply with SOX requirements.

4. **Update Data Processing Agreements**: Review and update all data processing agreements to ensure GDPR compliance.

5. **Conduct Security Testing**: Implement a regular schedule of security testing, including penetration testing and vulnerability assessments.

6. **Enhance Employee Training**: Increase the frequency and coverage of compliance training for all employees.
            `,
            format: 'markdown'
          }
        }
      ]
    },
    {
      id: 'conclusion',
      title: 'Conclusion',
      elements: [
        {
          id: 'conclusion-text',
          type: 'text',
          textConfig: {
            content: 'Overall, the organization has made significant progress in improving compliance during Q4 2023. The compliance score has increased from 78% to 85%, with improvements across all regulatory frameworks. However, there are still several critical and high-severity issues that need to be addressed promptly. By implementing the recommendations outlined in this report, the organization can further enhance its compliance posture and reduce regulatory risks.',
            format: 'paragraph'
          }
        }
      ]
    }
  ]
};

// Generate UI schema for the report
const reportSchema = novaVision.generateReportSchema(reportConfig);

// Sample report data
const reportData = {
  quarter: 'Q4',
  department: 'all'
};

// Render UI from schema
const renderedReport = novaVision.renderUiFromSchema(reportSchema, reportData);

// Export the schema and rendered report
module.exports = {
  reportConfig,
  reportSchema,
  reportData,
  renderedReport
};

// Example of how to use the rendered report in a React application:
/*
import React from 'react';
import { ReportRenderer } from 'novavision-react';

function ComplianceReport() {
  const [reportData, setReportData] = React.useState({
    quarter: 'Q4',
    department: 'all'
  });

  const handleParameterChange = (newData) => {
    setReportData(newData);
    // Fetch updated report data based on parameters
  };

  const handleExport = (format) => {
    // Export report to PDF, Excel, etc.
    console.log(`Exporting report to ${format}`);
  };

  return (
    <ReportRenderer
      schema={reportSchema}
      data={reportData}
      onParameterChange={handleParameterChange}
      onExport={handleExport}
    />
  );
}

export default ComplianceReport;
*/
