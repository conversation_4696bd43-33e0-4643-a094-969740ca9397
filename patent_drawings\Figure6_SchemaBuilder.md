```
+------------------------------------------------------------------+
|                                                                  |
|  Schema Builder Interface                                        |
|                                                                  |
+------------------------------------------------------------------+
|                                                                  |
| +------------------------+  +-------------------------------+     |
| | Entity Properties      |  | Field Properties              |     |
| +------------------------+  +-------------------------------+     |
| | Entity Name:           |  | Field Name:                   |     |
| | [Patient Form        ] |  | [diagnosis                  ] |     |
| |                        |  |                               |     |
| | Compliance Framework:  |  | Field Label:                  |     |
| | [HIPAA          (v)]   |  | [Diagnosis                  ] |     |
| |                        |  |                               |     |
| | API Endpoint:          |  | Field Type:                   |     |
| | [/api/patients       ] |  | [textarea              (v)]   |     |
| |                        |  |                               |     |
| | Submit Label:          |  | Required:                     |     |
| | [Save Patient Data   ] |  | [x]                           |     |
| +------------------------+  |                               |     |
|                             | Sensitivity:                  |     |
| +------------------------+  | [PHI                   (v)]   |     |
| | Fields                 |  |                               |     |
| +------------------------+  | Description:                  |     |
| | - name                 |  | [Patient diagnosis notes    ] |     |
| | - dob                  |  |                               |     |
| | - ssn                  |  | [Add Field]    [Update Field] |     |
| | - diagnosis            |  +-------------------------------+     |
| | - treatment            |                                        |
| | + Add New Field        |  +-------------------------------+     |
| +------------------------+  | Role Permissions              |     |
|                             +-------------------------------+     |
| +------------------------+  | Doctor:                       |     |
| | Components             |  | [x] View  [x] Edit            |     |
| +------------------------+  |                               |     |
| | - PHI Disclaimer       |  | Nurse:                        |     |
| | - Consent Checkbox     |  | [x] View  [ ] Edit            |     |
| | - Audit Trail          |  |                               |     |
| | + Add Component        |  | Admin:                        |     |
| +------------------------+  | [ ] View  [ ] Edit            |     |
|                             +-------------------------------+     |
+------------------------------------------------------------------+
|                                                                  |
| [Preview Form]                     [Save Schema]                 |
|                                                                  |
+------------------------------------------------------------------+

Figure 6: SchemaBuilder Interface
```
