openapi: 3.0.0
info:
  title: Privacy Management API
  description: API for managing privacy-related operations and compliance
  version: 1.0.0
  contact:
    name: NovaFuse API Support
    email: <EMAIL>
    url: https://novafuse.io/support
  license:
    name: Proprietary
    url: https://novafuse.io/license

servers:
  - url: /api/privacy/management
    description: Privacy Management API

tags:
  - name: Data Processing Activities
    description: Operations related to data processing activities
  - name: Subject Requests
    description: Operations related to data subject requests
  - name: Consent Management
    description: Operations related to consent management
  - name: Privacy Notices
    description: Operations related to privacy notices
  - name: Data Breaches
    description: Operations related to data breach management
  - name: Integrations
    description: Operations related to third-party integrations
  - name: Notifications
    description: Operations related to notifications
  - name: Regulatory Frameworks
    description: Operations related to regulatory frameworks
  - name: Compliance Requirements
    description: Operations related to compliance requirements
  - name: Compliance Status
    description: Operations related to compliance status tracking
  - name: Compliance Reports
    description: Operations related to compliance reporting
  - name: Regulatory Updates
    description: Operations related to regulatory updates

paths:
  /health:
    get:
      summary: Health check
      description: Returns the health status of the API
      operationId: healthCheck
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  service:
                    type: string
                    example: privacy-management-api
                  timestamp:
                    type: string
                    format: date-time

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
