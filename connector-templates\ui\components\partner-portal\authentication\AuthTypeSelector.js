import React from 'react';
import { 
  Box, 
  Paper, 
  Typography,
  Grid,
  Tooltip
} from '@mui/material';
import {
  Vpn<PERSON>ey as ApiKeyIcon,
  Person as BasicAuthIcon,
  Security as OAuth2Icon,
  Token as JwtIcon,
  Cloud as AwsIcon,
  Code as CustomIcon
} from '@mui/icons-material';

/**
 * Authentication Type Selector Component
 * 
 * This component provides a visual interface for selecting the authentication type.
 */
const AuthTypeSelector = ({ selectedType, onChange }) => {
  // Authentication types with icons and descriptions
  const authTypes = [
    {
      value: 'API_KEY',
      label: 'API Key',
      icon: <ApiKeyIcon sx={{ fontSize: 40 }} />,
      description: 'Simple token included in request header, query parameter, or URL'
    },
    {
      value: 'BASIC',
      label: 'Basic Auth',
      icon: <BasicAuthIcon sx={{ fontSize: 40 }} />,
      description: 'Username and password encoded in base64 format'
    },
    {
      value: 'OAUTH2',
      label: 'OAuth 2.0',
      icon: <OAuth2Icon sx={{ fontSize: 40 }} />,
      description: 'Token-based authorization framework for third-party access'
    },
    {
      value: 'JWT',
      label: 'JWT',
      icon: <JwtIcon sx={{ fontSize: 40 }} />,
      description: 'JSON Web Token for secure information transmission'
    },
    {
      value: 'AWS_SIG_V4',
      label: 'AWS Signature',
      icon: <AwsIcon sx={{ fontSize: 40 }} />,
      description: 'AWS Signature Version 4 for authenticating API requests'
    },
    {
      value: 'CUSTOM',
      label: 'Custom',
      icon: <CustomIcon sx={{ fontSize: 40 }} />,
      description: 'Custom authentication method for specialized APIs'
    }
  ];

  return (
    <Grid container spacing={2}>
      {authTypes.map((type) => (
        <Grid item xs={6} sm={4} md={2} key={type.value}>
          <Tooltip title={type.description} placement="top">
            <Paper 
              sx={{ 
                p: 2, 
                cursor: 'pointer',
                border: selectedType === type.value ? '2px solid #2563eb' : '1px solid transparent',
                backgroundColor: selectedType === type.value ? 'rgba(37, 99, 235, 0.1)' : 'background.default',
                height: 140,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'all 0.2s',
                '&:hover': {
                  backgroundColor: selectedType === type.value 
                    ? 'rgba(37, 99, 235, 0.15)' 
                    : 'rgba(0, 0, 0, 0.04)'
                }
              }}
              onClick={() => onChange(type.value)}
              elevation={selectedType === type.value ? 3 : 1}
            >
              <Box sx={{ 
                color: selectedType === type.value ? 'primary.main' : 'text.secondary',
                mb: 1
              }}>
                {type.icon}
              </Box>
              <Typography 
                variant="subtitle2" 
                align="center"
                sx={{ 
                  color: selectedType === type.value ? 'primary.main' : 'text.primary',
                  fontWeight: selectedType === type.value ? 'bold' : 'normal'
                }}
              >
                {type.label}
              </Typography>
            </Paper>
          </Tooltip>
        </Grid>
      ))}
    </Grid>
  );
};

export default AuthTypeSelector;
