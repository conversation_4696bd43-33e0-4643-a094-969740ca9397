const express = require('express');
const { validateRequest } = require('./validation');
const controllers = require('./controllers');

const router = express.Router();

/**
 * @swagger
 * /governance/esg/reports:
 *   get:
 *     summary: Get a list of ESG reports
 *     description: Returns a paginated list of ESG reports with optional filtering
 *     tags: [ESG]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: framework
 *         in: query
 *         description: Filter by ESG framework
 *         schema:
 *           type: string
 *       - name: status
 *         in: query
 *         description: Filter by report status
 *         schema:
 *           type: string
 *           enum: [draft, in-progress, completed, published]
 *       - name: year
 *         in: query
 *         description: Filter by reporting year
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ESGReport'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/reports', controllers.getReports);

/**
 * @swagger
 * /governance/esg/reports/{id}:
 *   get:
 *     summary: Get a specific ESG report
 *     description: Returns a specific ESG report by ID
 *     tags: [ESG]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG report ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGReport'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/reports/:id', controllers.getReportById);

/**
 * @swagger
 * /governance/esg/reports:
 *   post:
 *     summary: Create a new ESG report
 *     description: Creates a new ESG report
 *     tags: [ESG]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ESGReportInput'
 *     responses:
 *       201:
 *         description: Report created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGReport'
 *                 message:
 *                   type: string
 *                   example: ESG report created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/reports', validateRequest('createReport'), controllers.createReport);

/**
 * @swagger
 * /governance/esg/reports/{id}:
 *   put:
 *     summary: Update an ESG report
 *     description: Updates an existing ESG report
 *     tags: [ESG]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG report ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ESGReportInput'
 *     responses:
 *       200:
 *         description: Report updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGReport'
 *                 message:
 *                   type: string
 *                   example: ESG report updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/reports/:id', validateRequest('updateReport'), controllers.updateReport);

/**
 * @swagger
 * /governance/esg/reports/{id}:
 *   delete:
 *     summary: Delete an ESG report
 *     description: Deletes an existing ESG report
 *     tags: [ESG]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG report ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Report deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: ESG report deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/reports/:id', controllers.deleteReport);

/**
 * @swagger
 * /governance/esg/metrics:
 *   get:
 *     summary: Get ESG metrics
 *     description: Returns ESG metrics with optional filtering
 *     tags: [ESG]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: category
 *         in: query
 *         description: Filter by metric category (environmental, social, governance)
 *         schema:
 *           type: string
 *           enum: [environmental, social, governance]
 *       - name: year
 *         in: query
 *         description: Filter by reporting year
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     environmental:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ESGMetric'
 *                     social:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ESGMetric'
 *                     governance:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ESGMetric'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/metrics', controllers.getMetrics);

/**
 * @swagger
 * /governance/esg/frameworks:
 *   get:
 *     summary: Get ESG frameworks
 *     description: Returns a list of ESG reporting frameworks
 *     tags: [ESG]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ESGFramework'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/frameworks', controllers.getFrameworks);

/**
 * @swagger
 * /governance/esg/frameworks/{id}:
 *   get:
 *     summary: Get a specific ESG framework
 *     description: Returns a specific ESG framework by ID
 *     tags: [ESG]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG framework ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGFramework'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/frameworks/:id', controllers.getFrameworkById);

module.exports = router;
