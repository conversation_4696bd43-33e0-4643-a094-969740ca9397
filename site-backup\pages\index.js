import React from 'react';
import Link from 'next/link';
import Sidebar from '../components/Sidebar';
import PartnerKnowledgeBase from '../components/PartnerKnowledgeBase';

export default function Home() {
  const sidebarItems = [
    { type: 'category', label: 'Explore', items: [
      { label: 'Welcome to NovaFuse', href: '#' },
      { label: 'Core Innovations', href: '#core-innovations' },
      { label: 'Why NovaFuse Stands Alone', href: '#why-novafuse-stands-alone' },
      { label: 'Partner Empowerment', href: '#partner-empowerment' },
      { label: 'API Categories', href: '#api-categories' },
      { label: 'Getting Started', href: '#getting-started' }
    ]},
    { type: 'category', label: 'Partners', items: [
      { label: 'Zapier', href: '/zapier-partnership' },
      { label: 'Okta', href: '/okta-partnership' },
      { label: 'Airtable', href: '/airtable-partnership' }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'API Documentation', href: '/api-docs' },
      { label: 'Partner Ecosystem', href: '/partner-ecosystem' },
      { label: 'Partner Empowerment', href: '/partner-empowerment' },
      { label: 'Technology Roadmap', href: '/technology-roadmap' },
      { label: 'Early Access Program', href: '/early-access-program' }
    ]}
  ];

  return (
    <div className="flex flex-col md:flex-row gap-8">
      {/* Sidebar - Hidden on mobile, shown on desktop */}
      <div className="hidden md:block md:w-1/4 lg:w-1/5">
        <div className="sticky top-4">
          <Sidebar items={sidebarItems} title="NovaFuse API Superstore" />
        </div>
      </div>

      {/* Mobile Sidebar Toggle - Shown on mobile only */}
      <div className="md:hidden mb-4">
        <select
          className="w-full bg-secondary text-white border border-gray-700 rounded p-2"
          onChange={(e) => {
            if (e.target.value) window.location.href = e.target.value;
          }}
          defaultValue=""
        >
          <option value="" disabled>Navigate to...</option>
          {sidebarItems.map((item, index) => (
            item.type === 'category' ? (
              <optgroup key={index} label={item.label}>
                {item.items.map((subItem, subIndex) => (
                  <option key={`${index}-${subIndex}`} value={subItem.href}>
                    {subItem.label}
                  </option>
                ))}
              </optgroup>
            ) : (
              <option key={index} value={item.href}>
                {item.label}
              </option>
            )
          ))}
        </select>
      </div>

      {/* Main Content */}
      <div className="w-full md:w-3/4 lg:w-4/5">
      {/* Hero Section */}
      <div className="bg-blue-900 text-white rounded-lg p-8 mb-8 shadow-lg">
        <div className="flex justify-center mb-4">
          <div className="bg-blue-900 bg-opacity-70 px-4 py-2 rounded-full text-sm font-bold inline-flex items-center border border-blue-400 shadow-md">
            <span className="mr-2">🔒</span>
            <span>THE FUTURE OF BUSINESS SECURITY IS NOW</span>
          </div>
        </div>

        <h2 className="text-3xl md:text-4xl font-bold mb-4 text-center">Welcome to NovaFuse</h2>
        <h3 className="text-xl md:text-2xl font-semibold mb-6 text-center">Introducing the World's First Big 3 for the Future of Business</h3>

        {/* Value Promise Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          {/* Cyber-Safety Framework */}
          <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg border border-blue-400 hover:border-blue-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200">
            <div className="text-center mb-4">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white mb-2 shadow-md border border-blue-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-bold mb-2 text-center text-blue-100">Cyber-Safety Framework</h3>
            <p className="text-center text-blue-100">
              Because Proactive Compliance, Predictive Risk, and peace of mind is no longer an option - it is a necessity!
            </p>
          </div>

          {/* Universal API Connector */}
          <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg border border-blue-400 hover:border-blue-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200">
            <div className="text-center mb-4">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white mb-2 shadow-md border border-blue-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-bold mb-2 text-center text-purple-100">NovaConnect</h3>
            <p className="text-center text-purple-100">
              The Universal API Connector (UAC) - Fluid Interconnectivity, secure seamless integrations. The world's first!
            </p>
          </div>

          {/* Partner Empowerment Engine */}
          <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg border border-blue-400 hover:border-blue-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200">
            <div className="text-center mb-4">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white mb-2 shadow-md border border-blue-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-bold mb-2 text-center text-indigo-100">Partner Empowerment</h3>
            <p className="text-center text-indigo-100">
              More money. Less effort. The New business model. We want Partners not customers.
            </p>
          </div>
        </div>





        <div className="flex flex-wrap justify-center gap-4">
          <Link href="#core-innovations" className="bg-white text-blue-600 px-6 py-3 rounded-lg font-bold hover:bg-blue-50 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200">
            Discover Our Innovations
          </Link>
          <Link href="/partner-onboarding" className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
            Become a Partner
          </Link>
        </div>
      </div>

      {/* Core Innovations Section */}
      <div id="core-innovations" className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Core Innovations</h2>
        <div className="bg-secondary border border-gray-700 rounded-lg p-6 mb-8">
          <p className="text-xl text-center mb-6">
            NovaFuse is built on three revolutionary technologies that work together to create a comprehensive GRC solution.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Cyber-Safety Framework */}
            <div className="bg-blue-900 p-6 rounded-lg shadow-lg border border-blue-700">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center shadow-md border border-blue-400">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-xl font-bold mb-3 text-center">Cyber-Safety Framework</h3>
              <p className="text-gray-300 mb-4 text-center">
                Because Proactive Compliance, Predictive Risk, and peace of mind is no longer an option - it is a necessity! Our philosophy is our technology!
              </p>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>Compliance-by-Design Architecture</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>Real-time Compliance Monitoring</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>Automated Evidence Collection</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>Multi-Framework Support</span>
                </li>
              </ul>
            </div>

            {/* Universal API Connector */}
            <div className="bg-blue-900 p-6 rounded-lg shadow-lg border border-blue-700">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center shadow-md border border-blue-400">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-xl font-bold mb-3 text-center">NovaConnect (UAC)</h3>
              <p className="text-gray-300 mb-4 text-center">
                The Universal API Connector - Fluid Interconnectivity, secure seamless integrations. The world's first!
              </p>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>4 Patents Pending</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>Bidirectional Data Flow</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>Cross-Platform Compatibility</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>No-Code Integration</span>
                </li>
              </ul>
            </div>

            {/* Partner Empowerment Engine */}
            <div className="bg-blue-900 p-6 rounded-lg shadow-lg border border-blue-700">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center shadow-md border border-blue-400">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-xl font-bold mb-3 text-center">Partner Empowerment</h3>
              <p className="text-gray-300 mb-4 text-center">
                More money. Less effort. The New business model. We want Partners not customers.
              </p>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>Up to 85% Revenue Share</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>White-Label Capabilities</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>Accelerated Go-To-Market</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>Differentiation Without Development</span>
                </li>
              </ul>
            </div>
          </div>

          <div className="mt-8 text-center">
            <Link href="/uac-demo" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200">
              See the UAC Demo
            </Link>
          </div>
        </div>
      </div>

      {/* Competitive Advantage */}
      <div id="why-novafuse-stands-alone" className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Why NovaFuse Stands Alone</h2>
        <div className="bg-secondary border border-gray-700 rounded-lg p-6 mb-8">
          <p className="text-xl text-center mb-6">
            We've searched the market extensively, and the verdict is clear: <span className="text-blue-400 font-bold">No one else is doing what NovaFuse is doing.</span>
          </p>

          <div className="bg-blue-900 bg-opacity-40 p-4 rounded-lg mb-6 flex flex-col md:flex-row items-center justify-center gap-6">
            <div className="flex items-center bg-blue-800 bg-opacity-50 px-4 py-2 rounded-lg">
              <span className="text-2xl font-bold text-blue-400 mr-2">4</span>
              <span className="font-semibold">Patents Pending</span>
            </div>
            <div className="text-center md:text-left">
              <p className="font-bold text-lg">The Universal API Connector (UAC) is NOT just a GRC tool!</p>
              <p>GRC is just our first use case. The UAC is a versatile platform with unlimited potential applications.</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-2 rounded mr-2 shadow-sm">vs</span> Google Cloud Platform (GCP)
              </h3>
              <p className="text-gray-300 mb-3"><strong>What They Do:</strong> Vast ecosystem of tools and APIs with revenue-sharing potential for Google Marketplace integrations.</p>
              <p className="text-gray-300"><strong>But...</strong> Their focus is on scaling infrastructure and AI capabilities, not driving compliance innovation. Their partner programs lack the personalized, collaborative empowerment that NovaFuse delivers.</p>
            </div>

            <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-2 rounded mr-2 shadow-sm">vs</span> Amazon Web Services (AWS)
              </h3>
              <p className="text-gray-300 mb-3"><strong>What They Do:</strong> Expansive partner programs with integration options and technical support for cloud services.</p>
              <p className="text-gray-300"><strong>But...</strong> Centered around cloud infrastructure and building apps. No unique emphasis on compliance as a service or partner collaboration. It's about selling products, not empowering partners.</p>
            </div>

            <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-2 rounded mr-2 shadow-sm">vs</span> Stripe
              </h3>
              <p className="text-gray-300 mb-3"><strong>What They Do:</strong> Payment processing with API integration. Partnerships focused on simplifying payment solutions.</p>
              <p className="text-gray-300"><strong>But...</strong> Doesn't delve into compliance-driven innovation. NovaFuse offers broader use cases and integrates complex regulatory requirements directly into partner workflows—creating a compliance-first infrastructure.</p>
            </div>

            <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-2 rounded mr-2 shadow-sm">vs</span> Zapier
              </h3>
              <p className="text-gray-300 mb-3"><strong>What They Do:</strong> Automate workflows and integrate thousands of applications without coding.</p>
              <p className="text-gray-300"><strong>But...</strong> Doesn't emphasize compliance or data security to the degree that NovaFuse does. Lacks focus on compliance-driven automation—a big differentiator for NovaFuse.</p>
            </div>

            <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-2 rounded mr-2 shadow-sm">vs</span> Okta
              </h3>
              <p className="text-gray-300 mb-3"><strong>What They Do:</strong> Identity and access management, providing SSO and MFA solutions.</p>
              <p className="text-gray-300"><strong>But...</strong> Focused on identity security rather than a holistic compliance solution spanning regulatory requirements, risk management, and data security. NovaFuse offers an all-in-one compliance platform with partner empowerment.</p>
            </div>

            <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-2 rounded mr-2 shadow-sm">vs</span> MuleSoft
              </h3>
              <p className="text-gray-300 mb-3"><strong>What They Do:</strong> API integration platform to connect applications, data, and devices.</p>
              <p className="text-gray-300"><strong>But...</strong> Focuses on technical integration without delving into compliance. NovaFuse offers hyper-personalized compliance enablement with the Universal API Connector, something MuleSoft doesn't do.</p>
            </div>
          </div>

          <div className="mt-8 p-5 bg-blue-900 border border-blue-700 rounded-lg shadow-lg">
            <h3 className="text-xl font-bold mb-3 text-center">The NovaFuse Difference</h3>

            <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-4 rounded-lg mb-5 text-center shadow-md border border-blue-400">
              <h4 className="text-lg font-bold mb-2">No Catch. No Fine Print. Complete Transparency.</h4>
              <p className="text-white">We're radically transparent. No hidden fees, no middlemen, no surprises.
              We're going straight to the source and laying everything bare. What you see is what you get.</p>
            </div>

            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                <span>We've laid ego aside to offer real partnership rather than just a typical vendor-client relationship</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                <span>We emphasize mutual growth, not just outsourcing compliance</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                <span>We shift the focus from just tech features to business transformation—creating opportunities for partners to profit</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                <span>Our revenue-sharing model is exceptional, incentivizing everyone in our ecosystem to work together toward growth</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                <span>We show the human side of business, where collaboration, support, and win-win models take center stage</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                <span>We eliminate the middleman and go straight to the source—giving partners direct access to our technology and team</span>
              </li>
            </ul>

            <div className="mt-6 p-4 bg-gradient-to-r from-blue-900 to-purple-900 rounded-lg shadow-lg border border-blue-400">
              <h4 className="text-xl font-bold mb-3 text-center">Global Vision: Beyond Borders</h4>

              <div className="flex flex-wrap justify-center gap-3 mb-4">
                <span className="bg-gradient-to-r from-blue-700 to-purple-700 px-3 py-1 rounded-full text-sm font-semibold shadow-sm border border-blue-400">Europe</span>
                <span className="bg-gradient-to-r from-blue-700 to-purple-700 px-3 py-1 rounded-full text-sm font-semibold shadow-sm border border-blue-400">Asia</span>
                <span className="bg-gradient-to-r from-blue-700 to-purple-700 px-3 py-1 rounded-full text-sm font-semibold shadow-sm border border-blue-400">Africa</span>
                <span className="bg-gradient-to-r from-blue-700 to-purple-700 px-3 py-1 rounded-full text-sm font-semibold shadow-sm border border-blue-400">Australia</span>
                <span className="bg-gradient-to-r from-blue-700 to-purple-700 px-3 py-1 rounded-full text-sm font-semibold shadow-sm border border-blue-400">Americas</span>
              </div>

              <p className="text-center mb-4">NovaFuse is built for global scale. Our platform transcends geographical boundaries, regulatory frameworks, and cultural differences.</p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-3 rounded-lg text-center shadow-md border border-blue-400">
                  <h5 className="font-semibold mb-2">Multi-Region Compliance</h5>
                  <p className="text-sm">Built-in support for GDPR, CCPA, LGPD, PIPEDA and other regional regulations</p>
                </div>
                <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-3 rounded-lg text-center shadow-md border border-blue-400">
                  <h5 className="font-semibold mb-2">Global Partner Network</h5>
                  <p className="text-sm">Strategic partnerships across continents to ensure local expertise and support</p>
                </div>
                <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-3 rounded-lg text-center shadow-md border border-blue-400">
                  <h5 className="font-semibold mb-2">Universal Compatibility</h5>
                  <p className="text-sm">Our UAC works with any API worldwide, regardless of region or industry standards</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Partner Empowerment Knowledge Base */}
      <div id="partner-empowerment" className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Partner Knowledge Base</h2>
        <PartnerKnowledgeBase />
      </div>



      {/* API Categories */}
      <div id="api-categories">
        <h2 className="text-2xl font-bold mb-6">API Categories</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Governance */}
          <div className="bg-blue-900 border border-blue-700 rounded-lg p-6 shadow-lg">
            <h3 className="text-xl font-semibold mb-3">Governance & Board Compliance</h3>
            <p className="text-gray-300 mb-4">APIs for board meetings, governance policies, compliance reports, and board resolutions.</p>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">12 Endpoints</span>
              <Link href="/api-docs#governance" className="bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text hover:text-blue-300">View Documentation →</Link>
            </div>
          </div>

          {/* Security */}
          <div className="bg-blue-900 border border-blue-700 rounded-lg p-6 shadow-lg">
            <h3 className="text-xl font-semibold mb-3">Security</h3>
            <p className="text-gray-300 mb-4">APIs for vulnerabilities, security policies, incidents, and security scanning.</p>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">15 Endpoints</span>
              <Link href="/api-docs#security" className="bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text hover:text-blue-300">View Documentation →</Link>
            </div>
          </div>

          {/* APIs */}
          <div className="bg-blue-900 border border-blue-700 rounded-lg p-6 shadow-lg">
            <h3 className="text-xl font-semibold mb-3">APIs & Developer Tools</h3>
            <p className="text-gray-300 mb-4">APIs for API catalog, metrics, integration flows, and developer resources.</p>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">10 Endpoints</span>
              <Link href="/api-docs#apis" className="bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text hover:text-blue-300">View Documentation →</Link>
            </div>
          </div>

          {/* Risk */}
          <div className="bg-blue-900 border border-blue-700 rounded-lg p-6 shadow-lg">
            <h3 className="text-xl font-semibold mb-3">Risk & Audit</h3>
            <p className="text-gray-300 mb-4">APIs for risk assessments, audit reports, risk register, and audit findings.</p>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">14 Endpoints</span>
              <Link href="/api-docs#risk" className="bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text hover:text-blue-300">View Documentation →</Link>
            </div>
          </div>

          {/* Contracts */}
          <div className="bg-blue-900 border border-blue-700 rounded-lg p-6 shadow-lg">
            <h3 className="text-xl font-semibold mb-3">Contracts & Policy Lifecycle</h3>
            <p className="text-gray-300 mb-4">APIs for contracts, policies, contract renewals, and policy reviews.</p>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">11 Endpoints</span>
              <Link href="/api-docs#contracts" className="bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text hover:text-blue-300">View Documentation →</Link>
            </div>
          </div>

          {/* Certifications */}
          <div className="bg-blue-900 border border-blue-700 rounded-lg p-6 shadow-lg">
            <h3 className="text-xl font-semibold mb-3">Certifications & Accreditation</h3>
            <p className="text-gray-300 mb-4">APIs for certifications, accreditations, audit schedules, and certification status.</p>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">9 Endpoints</span>
              <Link href="/api-docs#certifications" className="bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text hover:text-blue-300">View Documentation →</Link>
            </div>
          </div>
        </div>
      </div>

      {/* Getting Started */}
      <div id="getting-started" className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Getting Started</h2>
        <div className="bg-secondary border border-gray-700 rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mb-4 shadow-md border border-blue-400">
                <span className="text-2xl">1</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Create an Account</h3>
              <p className="text-gray-300">Sign up for a NovaFuse account to access the API Superstore.</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mb-4 shadow-md border border-blue-400">
                <span className="text-2xl">2</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Choose Your APIs</h3>
              <p className="text-gray-300">Browse our API categories and select the ones that fit your needs.</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mb-4 shadow-md border border-blue-400">
                <span className="text-2xl">3</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Integrate & Launch</h3>
              <p className="text-gray-300">Use our documentation and SDKs to quickly integrate and go live.</p>
            </div>
          </div>
          <div className="mt-6 text-center">
            <Link href="/api-docs" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200">
              View Documentation
            </Link>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
}
