/**
 * Cache Module
 * 
 * This module provides functionality for caching API responses.
 */

// In-memory cache
const cache = new Map();

// Default TTL in seconds
const DEFAULT_TTL = 60;

/**
 * Set a cache entry
 * @param {string} key - Cache key
 * @param {*} value - Value to cache
 * @param {number} ttl - Time to live in seconds
 */
const set = (key, value, ttl = DEFAULT_TTL) => {
  const expiresAt = Date.now() + (ttl * 1000);
  cache.set(key, {
    value,
    expiresAt
  });
};

/**
 * Get a cache entry
 * @param {string} key - Cache key
 * @returns {*} - Cached value or undefined if not found or expired
 */
const get = (key) => {
  const entry = cache.get(key);
  
  if (!entry) {
    return undefined;
  }
  
  if (entry.expiresAt < Date.now()) {
    cache.delete(key);
    return undefined;
  }
  
  return entry.value;
};

/**
 * Delete a cache entry
 * @param {string} key - Cache key
 */
const del = (key) => {
  cache.delete(key);
};

/**
 * Clear all cache entries
 */
const clear = () => {
  cache.clear();
};

/**
 * Get cache statistics
 * @returns {Object} - Cache statistics
 */
const getStats = () => {
  const now = Date.now();
  let activeEntries = 0;
  let expiredEntries = 0;
  
  cache.forEach(entry => {
    if (entry.expiresAt >= now) {
      activeEntries++;
    } else {
      expiredEntries++;
    }
  });
  
  return {
    totalEntries: cache.size,
    activeEntries,
    expiredEntries
  };
};

/**
 * Clean expired cache entries
 * @returns {number} - Number of entries removed
 */
const cleanExpired = () => {
  const now = Date.now();
  let removedCount = 0;
  
  cache.forEach((entry, key) => {
    if (entry.expiresAt < now) {
      cache.delete(key);
      removedCount++;
    }
  });
  
  return removedCount;
};

/**
 * Cache middleware
 * @param {number} ttl - Time to live in seconds
 * @returns {Function} - Express middleware
 */
const cacheMiddleware = (ttl = DEFAULT_TTL) => {
  return (req, res, next) => {
    // Skip caching for non-GET requests
    if (req.method !== 'GET') {
      return next();
    }
    
    // Create a cache key from the request URL and query parameters
    const cacheKey = `${req.originalUrl || req.url}`;
    
    // Check if we have a cached response
    const cachedResponse = get(cacheKey);
    
    if (cachedResponse) {
      // Return the cached response
      return res.json(cachedResponse);
    }
    
    // Store the original json method
    const originalJson = res.json;
    
    // Override the json method to cache the response
    res.json = function(body) {
      // Cache the response
      set(cacheKey, body, ttl);
      
      // Call the original json method
      return originalJson.call(this, body);
    };
    
    next();
  };
};

// Periodically clean expired cache entries (every 5 minutes)
setInterval(cleanExpired, 5 * 60 * 1000);

module.exports = {
  set,
  get,
  del,
  clear,
  getStats,
  cleanExpired,
  cacheMiddleware
};
