@echo off
REM CHAEONIX HIDDEN BACKGROUND LAUNCHER
REM Starts CHAEONIX without visible terminal windows

REM Change to the correct directory
cd /d "%~dp0"

REM Kill any existing Node processes on port 3141
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3141') do (
    taskkill /f /pid %%a >nul 2>&1
)

REM Start CHAEONIX in completely hidden mode
powershell -WindowStyle Hidden -Command "Start-Process powershell -ArgumentList '-WindowStyle Hidden -Command \"cd ''%~dp0''; npm run dev\"' -WindowStyle Hidden"

REM Wait a moment for startup
timeout /t 5 /nobreak >nul

REM Open browser automatically
start http://localhost:3141

REM Create a notification that it's running
echo CHAEONIX is now running in the background on port 3141 > "%TEMP%\chaeonix-status.txt"
echo You can close this window - CHAEONIX will keep running >> "%TEMP%\chaeonix-status.txt"
echo To stop CHAEONIX, run CHAEONIX-STOP.bat >> "%TEMP%\chaeonix-status.txt"

REM Show brief notification then close
echo.
echo ========================================
echo   CHAEONIX STARTED IN BACKGROUND
echo ========================================
echo   Server: http://localhost:3141
echo   Status: Running hidden
echo   This window will close in 3 seconds
echo ========================================
echo.

timeout /t 3 /nobreak >nul

REM Exit this window
exit
