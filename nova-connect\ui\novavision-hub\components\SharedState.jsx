/**
 * SharedState Component
 * 
 * A component for managing shared state in collaborative sessions.
 */

import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useCollaboration } from '../collaboration/CollaborationContext';
import { useAuth } from '../auth/AuthContext';
import { useTheme } from '../theme/ThemeContext';
import { Animated } from './Animated';

/**
 * SharedState component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.roomId] - Room ID (if not using the current room from context)
 * @param {string} props.stateKey - Key for the shared state
 * @param {any} [props.initialValue] - Initial value for the shared state
 * @param {Function} props.children - Render function for the shared state
 * @param {boolean} [props.showControls=false] - Whether to show controls
 * @param {boolean} [props.showHistory=false] - Whether to show state history
 * @param {number} [props.historyLimit=10] - Maximum number of history entries to show
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} SharedState component
 */
const SharedState = ({
  roomId: externalRoomId,
  stateKey,
  initialValue,
  children,
  showControls = false,
  showHistory = false,
  historyLimit = 10,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const { user } = useAuth();
  const {
    isConnected,
    isLoading,
    error,
    currentRoom,
    sharedState,
    updateSharedState
  } = useCollaboration();
  
  // State
  const [localValue, setLocalValue] = useState(initialValue);
  const [isUpdating, setIsUpdating] = useState(false);
  const [localError, setLocalError] = useState(null);
  
  // Get room ID
  const roomId = externalRoomId || (currentRoom ? currentRoom.id : null);
  
  // Get state value
  const stateValue = roomId && sharedState[roomId] && sharedState[roomId][stateKey] !== undefined
    ? sharedState[roomId][stateKey]
    : localValue;
  
  // Get state history
  const stateHistory = roomId && sharedState[roomId] && sharedState[roomId][`${stateKey}_history`]
    ? sharedState[roomId][`${stateKey}_history`]
    : [];
  
  // Update local value when state value changes
  useEffect(() => {
    if (stateValue !== undefined) {
      setLocalValue(stateValue);
    }
  }, [stateValue]);
  
  // Initialize shared state
  useEffect(() => {
    if (roomId && isConnected && initialValue !== undefined && !isLoading && !isUpdating) {
      const initializeState = async () => {
        // Check if state already exists
        if (sharedState[roomId] && sharedState[roomId][stateKey] !== undefined) {
          return;
        }
        
        setIsUpdating(true);
        setLocalError(null);
        
        try {
          await updateSharedState(roomId, {
            [stateKey]: initialValue,
            [`${stateKey}_history`]: []
          });
        } catch (err) {
          console.error('Error initializing shared state:', err);
          setLocalError(err);
        } finally {
          setIsUpdating(false);
        }
      };
      
      initializeState();
    }
  }, [roomId, isConnected, initialValue, stateKey, isLoading, isUpdating, sharedState, updateSharedState]);
  
  // Update shared state
  const updateState = useCallback(async (newValue) => {
    if (!roomId || !isConnected || isUpdating) {
      return;
    }
    
    setIsUpdating(true);
    setLocalError(null);
    
    try {
      // Create history entry
      const historyEntry = {
        value: stateValue,
        timestamp: new Date().toISOString(),
        userId: user?.id,
        userDisplayName: user?.displayName
      };
      
      // Update state with history
      const newHistory = [historyEntry, ...stateHistory].slice(0, historyLimit);
      
      await updateSharedState(roomId, {
        [stateKey]: newValue,
        [`${stateKey}_history`]: newHistory
      });
      
      setLocalValue(newValue);
    } catch (err) {
      console.error('Error updating shared state:', err);
      setLocalError(err);
    } finally {
      setIsUpdating(false);
    }
  }, [roomId, isConnected, isUpdating, stateValue, stateHistory, stateKey, user, historyLimit, updateSharedState]);
  
  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  };
  
  return (
    <div
      className={`shared-state ${className}`}
      style={style}
      data-testid="shared-state"
    >
      {/* Error message */}
      {(error || localError) && (
        <div className="bg-error bg-opacity-10 border border-error text-error px-4 py-3 rounded mb-4">
          {(error || localError).message || 'An error occurred'}
        </div>
      )}
      
      {/* Content */}
      <div className="mb-4">
        {children({
          value: stateValue,
          updateValue: updateState,
          isLoading: isLoading || isUpdating,
          error: error || localError
        })}
      </div>
      
      {/* Controls */}
      {showControls && (
        <div className="flex items-center justify-between mb-4 p-2 bg-background rounded border border-divider">
          <div className="text-sm text-textSecondary">
            {isUpdating ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Updating...
              </span>
            ) : (
              <span>
                Last updated: {sharedState[roomId]?.lastUpdated ? formatTimestamp(sharedState[roomId].lastUpdated) : 'Never'}
                {sharedState[roomId]?.lastUpdatedBy && (
                  <span className="ml-2">
                    by {sharedState[roomId].lastUpdatedByName || sharedState[roomId].lastUpdatedBy}
                  </span>
                )}
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              type="button"
              className="bg-primary text-primaryContrast px-3 py-1 rounded-md text-sm hover:bg-primaryDark transition-colors duration-200 disabled:opacity-50"
              onClick={() => updateState(initialValue)}
              disabled={!isConnected || isUpdating || JSON.stringify(stateValue) === JSON.stringify(initialValue)}
            >
              Reset
            </button>
          </div>
        </div>
      )}
      
      {/* History */}
      {showHistory && stateHistory.length > 0 && (
        <Animated animation="fadeIn" className="mt-4">
          <h3 className="text-sm font-medium text-textPrimary mb-2">
            History
          </h3>
          <div className="bg-background border border-divider rounded overflow-hidden">
            <table className="w-full text-sm">
              <thead>
                <tr className="bg-surface">
                  <th className="px-4 py-2 text-left text-textPrimary">Time</th>
                  <th className="px-4 py-2 text-left text-textPrimary">User</th>
                  <th className="px-4 py-2 text-left text-textPrimary">Value</th>
                </tr>
              </thead>
              <tbody>
                {stateHistory.map((entry, index) => (
                  <tr key={index} className="border-t border-divider">
                    <td className="px-4 py-2 text-textSecondary">
                      {formatTimestamp(entry.timestamp)}
                    </td>
                    <td className="px-4 py-2 text-textPrimary">
                      {entry.userDisplayName || entry.userId || 'Unknown'}
                    </td>
                    <td className="px-4 py-2 text-textPrimary">
                      {typeof entry.value === 'object'
                        ? JSON.stringify(entry.value).substring(0, 50) + (JSON.stringify(entry.value).length > 50 ? '...' : '')
                        : String(entry.value)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Animated>
      )}
    </div>
  );
};

SharedState.propTypes = {
  roomId: PropTypes.string,
  stateKey: PropTypes.string.isRequired,
  initialValue: PropTypes.any,
  children: PropTypes.func.isRequired,
  showControls: PropTypes.bool,
  showHistory: PropTypes.bool,
  historyLimit: PropTypes.number,
  className: PropTypes.string,
  style: PropTypes.object
};

export default SharedState;
