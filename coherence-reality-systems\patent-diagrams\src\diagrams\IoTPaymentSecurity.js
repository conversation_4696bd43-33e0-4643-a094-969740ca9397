import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  VerticalArrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow
} from '../components/DiagramComponents';

const IoTPaymentSecurity = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>IOT PAYMENT DEVICE PCI-DSS VALIDATION</ContainerLabel>
      </ContainerBox>
      
      {/* Main flow components */}
      <ComponentBox left="80px" top="100px" width="130px">
        <ComponentNumber>501</ComponentNumber>
        <ComponentLabel>IoT Device</ComponentLabel>
        Monitor
      </ComponentBox>
      
      <Arrow left="210px" top="130px" width="100px" />
      
      <ComponentBox left="320px" top="100px" width="130px">
        <ComponentNumber>502</ComponentNumber>
        <ComponentLabel>Edge Compliance</ComponentLabel>
        Engine
      </ComponentBox>
      
      <Arrow left="450px" top="130px" width="100px" />
      
      <ComponentBox left="560px" top="100px" width="130px">
        <ComponentNumber>503</ComponentNumber>
        <ComponentLabel>PCI-DSS</ComponentLabel>
        Validation
      </ComponentBox>
      
      <Arrow left="625px" top="160px" width="2px" height="100px" />
      
      <ComponentBox left="560px" top="260px" width="130px">
        <ComponentNumber>504</ComponentNumber>
        <ComponentLabel>Compliance</ComponentLabel>
        Report
      </ComponentBox>
      
      {/* Device Security Profile Database */}
      <ComponentBox left="320px" top="260px" width="130px">
        <ComponentNumber>505</ComponentNumber>
        <ComponentLabel>Device Security</ComponentLabel>
        Profile Database
      </ComponentBox>
      
      {/* Connecting arrows */}
      <VerticalArrow left="145px" top="160px" height="100px" />
      
      <Arrow left="145px" top="260px" width="175px" />
      
      <Arrow left="450px" top="290px" width="110px" />
      
      {/* Curved arrow from PCI-DSS Validation to Device Security */}
      <CurvedArrow width="240" height="160" left="560" top="160">
        <path
          d="M 65,0 Q 0,80 -110,100"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="-110,100 -100,92 -103,102"
          fill="#333"
        />
      </CurvedArrow>
      
      {/* Additional components */}
      <ComponentBox left="80px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>506</ComponentNumber>
        <ComponentLabel>Secure Update</ComponentLabel>
        Mechanism
      </ComponentBox>
      
      <ComponentBox left="240px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>507</ComponentNumber>
        <ComponentLabel>Anomaly Detection</ComponentLabel>
        System
      </ComponentBox>
      
      <ComponentBox left="400px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>508</ComponentNumber>
        <ComponentLabel>Sub-100ms Latency</ComponentLabel>
        Enforcement
      </ComponentBox>
      
      <ComponentBox left="560px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>509</ComponentNumber>
        <ComponentLabel>Connected Device</ComponentLabel>
        Registry
      </ComponentBox>
    </DiagramFrame>
  );
};

export default IoTPaymentSecurity;
