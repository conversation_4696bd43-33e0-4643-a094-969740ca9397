# NovaCore Tests

This directory contains tests for the NovaCore API.

## Test Structure

The tests are organized as follows:

- `unit/`: Unit tests for individual components
  - `security/`: Tests for security components
  - `compliance/`: Tests for compliance components
- `integration/`: Integration tests for API endpoints
  - `security/`: Tests for security endpoints
  - `compliance/`: Tests for compliance endpoints

## Running Tests

To run all tests:

```bash
npm test
```

To run tests with Mocha:

```bash
npm run test:mocha
```

To run only unit tests:

```bash
npm run test:unit
```

To run only integration tests:

```bash
npm run test:integration
```

To run only security tests:

```bash
npm run test:security
```

To run only compliance tests:

```bash
npm run test:compliance
```

## Test Coverage

To generate a test coverage report:

```bash
npm run test:coverage
```

## Linting

To lint the code:

```bash
npm run lint
```

To automatically fix linting issues:

```bash
npm run lint:fix
```
