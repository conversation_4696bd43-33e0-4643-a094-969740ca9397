"""
UCTO Automation Module

This module provides a framework for automating compliance workflows.
"""

import os
import json
import logging
import subprocess
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AutomationManager:
    """
    Python wrapper for the JavaScript AutomationManager.
    
    This class provides a Python interface to the JavaScript AutomationManager.
    """
    
    def __init__(self, data_dir: Optional[str] = None, tracking_manager = None):
        """
        Initialize the Automation Manager.
        
        Args:
            data_dir: Path to a directory for storing automation data
            tracking_manager: Reference to the TrackingManager
        """
        logger.info("Initializing Automation Manager (Python wrapper)")
        
        # Set the data directory
        self.data_dir = data_dir or os.path.join(os.getcwd(), 'automation_data')
        
        # Create the data directory if it doesn't exist
        os.makedirs(self.data_dir, exist_ok=True)
        
        # Store the tracking manager
        self.tracking_manager = tracking_manager
        
        # Initialize workflows
        self.workflows = {}
        
        # Initialize triggers
        self.triggers = {}
        
        # Initialize actions
        self.actions = {}
        
        logger.info(f"Automation Manager initialized with data directory: {self.data_dir}")
    
    def get_schema(self) -> Dict[str, Any]:
        """
        Get the automation schema.
        
        Returns:
            The automation schema
        """
        # In a real implementation, this would call the JavaScript AutomationManager
        # For now, return a simplified schema
        return {
            "metadata": {
                "name": "UCTO Compliance Automation Framework",
                "description": "Framework for automating compliance workflows, evidence collection, and remediation actions",
                "version": "1.0.0"
            },
            "triggerTypes": [
                {
                    "id": "schedule",
                    "name": "Schedule",
                    "description": "Trigger based on a schedule (cron expression)"
                },
                {
                    "id": "event",
                    "name": "Event",
                    "description": "Trigger based on an event"
                }
            ],
            "actionTypes": [
                {
                    "id": "create_requirement",
                    "name": "Create Requirement",
                    "description": "Create a new compliance requirement"
                },
                {
                    "id": "collect_evidence",
                    "name": "Collect Evidence",
                    "description": "Collect evidence for a compliance requirement or activity"
                }
            ]
        }
    
    def register_workflow(self, workflow: Dict[str, Any]) -> str:
        """
        Register a workflow.
        
        Args:
            workflow: Workflow definition
            
        Returns:
            Workflow ID
        """
        logger.info(f"Registering workflow: {workflow.get('name')}")
        
        # Validate the workflow
        self._validate_workflow(workflow)
        
        # Store the workflow
        self.workflows[workflow['id']] = workflow
        
        # Save the workflow to disk
        self._save_workflow(workflow)
        
        return workflow['id']
    
    def get_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Get a workflow by ID.
        
        Args:
            workflow_id: Workflow ID
            
        Returns:
            Workflow definition
        """
        return self.workflows.get(workflow_id)
    
    def get_all_workflows(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all workflows.
        
        Returns:
            All workflows
        """
        return self.workflows
    
    def update_workflow(self, workflow_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a workflow.
        
        Args:
            workflow_id: Workflow ID
            updates: Updates to apply
            
        Returns:
            Updated workflow
        """
        logger.info(f"Updating workflow: {workflow_id}")
        
        # Get the workflow
        workflow = self.workflows.get(workflow_id)
        
        if not workflow:
            raise ValueError(f"Workflow not found: {workflow_id}")
        
        # Apply updates
        updated_workflow = {**workflow, **updates}
        
        # Validate the updated workflow
        self._validate_workflow(updated_workflow)
        
        # Store the updated workflow
        self.workflows[workflow_id] = updated_workflow
        
        # Save the workflow to disk
        self._save_workflow(updated_workflow)
        
        return updated_workflow
    
    def delete_workflow(self, workflow_id: str) -> bool:
        """
        Delete a workflow.
        
        Args:
            workflow_id: Workflow ID
            
        Returns:
            Success
        """
        logger.info(f"Deleting workflow: {workflow_id}")
        
        # Get the workflow
        workflow = self.workflows.get(workflow_id)
        
        if not workflow:
            raise ValueError(f"Workflow not found: {workflow_id}")
        
        # Delete the workflow
        del self.workflows[workflow_id]
        
        # Delete the workflow file
        workflow_path = os.path.join(self.data_dir, 'workflows', f"{workflow_id}.json")
        if os.path.exists(workflow_path):
            os.remove(workflow_path)
        
        return True
    
    def execute_workflow(self, workflow_id: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a workflow.
        
        Args:
            workflow_id: Workflow ID
            context: Execution context
            
        Returns:
            Execution result
        """
        logger.info(f"Executing workflow: {workflow_id}")
        
        # Get the workflow
        workflow = self.workflows.get(workflow_id)
        
        if not workflow:
            raise ValueError(f"Workflow not found: {workflow_id}")
        
        # Check if the workflow is enabled
        if not workflow.get('enabled', True):
            logger.info(f"Workflow is disabled: {workflow_id}")
            return {"success": False, "message": "Workflow is disabled"}
        
        # Create execution context
        execution_context = {
            **(context or {}),
            "workflow": workflow,
            "variables": {**workflow.get('variables', {})},
            "results": {}
        }
        
        # Execute actions
        try:
            for action in workflow.get('actions', []):
                # Check if the action has a condition
                if action.get('condition'):
                    # Evaluate the condition
                    condition_result = self._evaluate_condition(action['condition'], execution_context)
                    
                    if not condition_result:
                        logger.info(f"Skipping action due to condition: {action.get('name')}")
                        continue
                
                # Execute the action
                action_result = self._execute_action(action, execution_context)
                
                # Store the result
                execution_context['results'][action['id']] = action_result
                
                # Check if the action succeeded
                if action_result.get('success'):
                    # Execute onSuccess actions if defined
                    on_success = action.get('onSuccess', {})
                    for success_action in on_success.get('actions', []):
                        success_action_result = self._execute_action(success_action, execution_context)
                        execution_context['results'][f"{action['id']}_success_{success_action['id']}"] = success_action_result
                else:
                    # Execute onFailure actions if defined
                    on_failure = action.get('onFailure', {})
                    for failure_action in on_failure.get('actions', []):
                        failure_action_result = self._execute_action(failure_action, execution_context)
                        execution_context['results'][f"{action['id']}_failure_{failure_action['id']}"] = failure_action_result
            
            return {
                "success": True,
                "workflow_id": workflow_id,
                "results": execution_context['results']
            }
        except Exception as e:
            logger.error(f"Error executing workflow: {workflow_id}", exc_info=True)
            
            return {
                "success": False,
                "workflow_id": workflow_id,
                "error": str(e),
                "results": execution_context['results']
            }
    
    def register_trigger(self, trigger_type: str, handler: callable) -> bool:
        """
        Register a trigger.
        
        Args:
            trigger_type: Trigger type
            handler: Trigger handler
            
        Returns:
            Success
        """
        logger.info(f"Registering trigger: {trigger_type}")
        
        # Store the trigger handler
        self.triggers[trigger_type] = handler
        
        return True
    
    def register_action(self, action_type: str, handler: callable) -> bool:
        """
        Register an action.
        
        Args:
            action_type: Action type
            handler: Action handler
            
        Returns:
            Success
        """
        logger.info(f"Registering action: {action_type}")
        
        # Store the action handler
        self.actions[action_type] = handler
        
        return True
    
    def _validate_workflow(self, workflow: Dict[str, Any]) -> bool:
        """
        Validate a workflow.
        
        Args:
            workflow: Workflow definition
            
        Returns:
            Valid
        """
        # Check required fields
        if not workflow.get('id'):
            raise ValueError("Workflow ID is required")
        
        if not workflow.get('name'):
            raise ValueError("Workflow name is required")
        
        if not workflow.get('trigger'):
            raise ValueError("Workflow trigger is required")
        
        if not workflow.get('trigger', {}).get('type'):
            raise ValueError("Workflow trigger type is required")
        
        if not workflow.get('actions') or not isinstance(workflow.get('actions'), list) or len(workflow.get('actions', [])) == 0:
            raise ValueError("Workflow must have at least one action")
        
        # Validate actions
        for action in workflow.get('actions', []):
            if not action.get('id'):
                raise ValueError("Action ID is required")
            
            if not action.get('type'):
                raise ValueError("Action type is required")
        
        return True
    
    def _save_workflow(self, workflow: Dict[str, Any]) -> bool:
        """
        Save a workflow to disk.
        
        Args:
            workflow: Workflow definition
            
        Returns:
            Success
        """
        # Create the workflows directory if it doesn't exist
        workflows_dir = os.path.join(self.data_dir, 'workflows')
        os.makedirs(workflows_dir, exist_ok=True)
        
        # Save the workflow to a file
        workflow_path = os.path.join(workflows_dir, f"{workflow['id']}.json")
        with open(workflow_path, 'w', encoding='utf-8') as f:
            json.dump(workflow, f, indent=2)
        
        return True
    
    def _load_workflows(self) -> bool:
        """
        Load workflows from disk.
        
        Returns:
            Success
        """
        logger.info("Loading workflows from disk")
        
        # Create the workflows directory if it doesn't exist
        workflows_dir = os.path.join(self.data_dir, 'workflows')
        os.makedirs(workflows_dir, exist_ok=True)
        
        # Get all workflow files
        workflow_files = [f for f in os.listdir(workflows_dir) if f.endswith('.json')]
        
        # Load each workflow
        for workflow_file in workflow_files:
            try:
                workflow_path = os.path.join(workflows_dir, workflow_file)
                with open(workflow_path, 'r', encoding='utf-8') as f:
                    workflow = json.load(f)
                
                # Store the workflow
                self.workflows[workflow['id']] = workflow
                
                logger.info(f"Loaded workflow from disk: {workflow['id']}")
            except Exception as e:
                logger.error(f"Error loading workflow: {workflow_file}", exc_info=True)
        
        return True
    
    def _evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """
        Evaluate a condition.
        
        Args:
            condition: Condition expression
            context: Execution context
            
        Returns:
            Result
        """
        # For now, just return True
        # In a real implementation, this would evaluate the condition expression
        return True
    
    def _execute_action(self, action: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute an action.
        
        Args:
            action: Action definition
            context: Execution context
            
        Returns:
            Execution result
        """
        logger.info(f"Executing action: {action.get('name') or action.get('id')}")
        
        # Get the action handler
        action_handler = self.actions.get(action['type'])
        
        if not action_handler:
            raise ValueError(f"No handler registered for action type: {action['type']}")
        
        try:
            # Execute the action handler
            result = action_handler(action.get('parameters', {}), context)
            
            return {
                "success": True,
                "action_id": action['id'],
                "action_type": action['type'],
                "result": result
            }
        except Exception as e:
            logger.error(f"Error executing action: {action.get('name') or action.get('id')}", exc_info=True)
            
            return {
                "success": False,
                "action_id": action['id'],
                "action_type": action['type'],
                "error": str(e)
            }

class ScheduleTrigger:
    """
    Python wrapper for the JavaScript ScheduleTrigger.
    
    This class provides a Python interface to the JavaScript ScheduleTrigger.
    """
    
    def __init__(self, automation_manager: AutomationManager):
        """
        Initialize the Schedule Trigger.
        
        Args:
            automation_manager: Reference to the AutomationManager
        """
        logger.info("Initializing Schedule Trigger (Python wrapper)")
        
        # Store the automation manager
        self.automation_manager = automation_manager
        
        # Initialize scheduled jobs
        self.scheduled_jobs = {}
        
        logger.info("Schedule Trigger initialized")
    
    def register_workflow(self, workflow: Dict[str, Any]) -> bool:
        """
        Register a workflow with this trigger.
        
        Args:
            workflow: Workflow definition
            
        Returns:
            Success
        """
        logger.info(f"Registering workflow with Schedule Trigger: {workflow.get('id')}")
        
        # Validate the workflow trigger
        if workflow.get('trigger', {}).get('type') != 'schedule':
            raise ValueError(f"Invalid trigger type for Schedule Trigger: {workflow.get('trigger', {}).get('type')}")
        
        # Get the schedule parameters
        schedule = workflow.get('trigger', {}).get('parameters', {}).get('schedule')
        timezone = workflow.get('trigger', {}).get('parameters', {}).get('timezone')
        
        if not schedule:
            raise ValueError("Schedule parameter is required for Schedule Trigger")
        
        # Schedule the workflow
        self._schedule_workflow(workflow['id'], schedule, timezone)
        
        return True
    
    def _schedule_workflow(self, workflow_id: str, schedule: str, timezone: Optional[str] = None) -> Dict[str, Any]:
        """
        Schedule a workflow.
        
        Args:
            workflow_id: Workflow ID
            schedule: Cron expression
            timezone: Timezone
            
        Returns:
            Scheduled job
        """
        logger.info(f"Scheduling workflow: {workflow_id}, Schedule: {schedule}, Timezone: {timezone or 'UTC'}")
        
        # In a real implementation, this would use a library like APScheduler to schedule the workflow
        # For now, just create a mock job
        job = {
            "workflow_id": workflow_id,
            "schedule": schedule,
            "timezone": timezone or 'UTC'
        }
        
        # Store the job
        self.scheduled_jobs[workflow_id] = job
        
        return job

class EventTrigger:
    """
    Python wrapper for the JavaScript EventTrigger.
    
    This class provides a Python interface to the JavaScript EventTrigger.
    """
    
    def __init__(self, automation_manager: AutomationManager):
        """
        Initialize the Event Trigger.
        
        Args:
            automation_manager: Reference to the AutomationManager
        """
        logger.info("Initializing Event Trigger (Python wrapper)")
        
        # Store the automation manager
        self.automation_manager = automation_manager
        
        # Initialize event listeners
        self.event_listeners = {}
        
        logger.info("Event Trigger initialized")
    
    def register_workflow(self, workflow: Dict[str, Any]) -> bool:
        """
        Register a workflow with this trigger.
        
        Args:
            workflow: Workflow definition
            
        Returns:
            Success
        """
        logger.info(f"Registering workflow with Event Trigger: {workflow.get('id')}")
        
        # Validate the workflow trigger
        if workflow.get('trigger', {}).get('type') != 'event':
            raise ValueError(f"Invalid trigger type for Event Trigger: {workflow.get('trigger', {}).get('type')}")
        
        # Get the event parameters
        event_type = workflow.get('trigger', {}).get('parameters', {}).get('eventType')
        conditions = workflow.get('trigger', {}).get('parameters', {}).get('conditions')
        
        if not event_type:
            raise ValueError("Event type parameter is required for Event Trigger")
        
        # Register the workflow for the event
        if event_type not in self.event_listeners:
            self.event_listeners[event_type] = []
        
        self.event_listeners[event_type].append({
            "workflow_id": workflow['id'],
            "conditions": conditions
        })
        
        return True
    
    def trigger_event(self, event_type: str, event_data: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Trigger an event.
        
        Args:
            event_type: Event type
            event_data: Event data
            
        Returns:
            Execution results
        """
        logger.info(f"Triggering event: {event_type}")
        
        # Get the listeners for this event type
        listeners = self.event_listeners.get(event_type, [])
        
        if not listeners:
            logger.info(f"No listeners for event type: {event_type}")
            return []
        
        # Execute workflows for each listener
        results = []
        
        for listener in listeners:
            # Check conditions
            if listener.get('conditions') and not self._check_conditions(listener['conditions'], event_data or {}):
                logger.info(f"Conditions not met for workflow: {listener['workflow_id']}")
                continue
            
            # Execute the workflow
            result = self._execute_workflow(listener['workflow_id'], event_type, event_data or {})
            
            results.append(result)
        
        return results
    
    def _check_conditions(self, conditions: Dict[str, Any], event_data: Dict[str, Any]) -> bool:
        """
        Check if conditions are met.
        
        Args:
            conditions: Conditions to check
            event_data: Event data
            
        Returns:
            Conditions met
        """
        # For now, just return True
        # In a real implementation, this would check the conditions against the event data
        return True
    
    def _execute_workflow(self, workflow_id: str, event_type: str, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a workflow.
        
        Args:
            workflow_id: Workflow ID
            event_type: Event type
            event_data: Event data
            
        Returns:
            Execution result
        """
        logger.info(f"Executing workflow from Event Trigger: {workflow_id}")
        
        # Create execution context
        context = {
            "trigger": {
                "type": "event",
                "event_type": event_type,
                "timestamp": None,  # Will be set by the automation manager
                "data": event_data
            }
        }
        
        # Execute the workflow
        return self.automation_manager.execute_workflow(workflow_id, context)

class CreateRequirementAction:
    """
    Python wrapper for the JavaScript CreateRequirementAction.
    
    This class provides a Python interface to the JavaScript CreateRequirementAction.
    """
    
    def __init__(self, tracking_manager):
        """
        Initialize the Create Requirement Action.
        
        Args:
            tracking_manager: Reference to the TrackingManager
        """
        logger.info("Initializing Create Requirement Action (Python wrapper)")
        
        # Store the tracking manager
        self.tracking_manager = tracking_manager
        
        logger.info("Create Requirement Action initialized")
    
    def __call__(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the action.
        
        Args:
            parameters: Action parameters
            context: Execution context
            
        Returns:
            Execution result
        """
        logger.info("Executing Create Requirement Action")
        
        # Validate parameters
        if not parameters.get('name'):
            raise ValueError("Name parameter is required for Create Requirement Action")
        
        if not parameters.get('description'):
            raise ValueError("Description parameter is required for Create Requirement Action")
        
        if not parameters.get('framework'):
            raise ValueError("Framework parameter is required for Create Requirement Action")
        
        # Create the requirement data
        requirement_data = {
            "name": parameters.get('name'),
            "description": parameters.get('description'),
            "framework": parameters.get('framework'),
            "category": parameters.get('category'),
            "priority": parameters.get('priority'),
            "status": parameters.get('status', 'pending'),
            "due_date": parameters.get('due_date'),
            "assigned_to": parameters.get('assigned_to'),
            "tags": parameters.get('tags')
        }
        
        # If tracking manager is available, create the requirement
        if self.tracking_manager and hasattr(self.tracking_manager, 'create_requirement'):
            try:
                requirement = self.tracking_manager.create_requirement(requirement_data)
                
                return {
                    "success": True,
                    "requirement": requirement
                }
            except Exception as e:
                logger.error("Error creating requirement:", exc_info=True)
                
                raise ValueError(f"Failed to create requirement: {str(e)}")
        else:
            # Return mock data if tracking manager is not available
            import uuid
            from datetime import datetime
            
            return {
                "success": True,
                "requirement": {
                    "id": str(uuid.uuid4()),
                    **requirement_data,
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }
            }

class CollectEvidenceAction:
    """
    Python wrapper for the JavaScript CollectEvidenceAction.
    
    This class provides a Python interface to the JavaScript CollectEvidenceAction.
    """
    
    def __init__(self, tracking_manager):
        """
        Initialize the Collect Evidence Action.
        
        Args:
            tracking_manager: Reference to the TrackingManager
        """
        logger.info("Initializing Collect Evidence Action (Python wrapper)")
        
        # Store the tracking manager
        self.tracking_manager = tracking_manager
        
        logger.info("Collect Evidence Action initialized")
    
    def __call__(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the action.
        
        Args:
            parameters: Action parameters
            context: Execution context
            
        Returns:
            Execution result
        """
        logger.info("Executing Collect Evidence Action")
        
        # Validate parameters
        if not parameters.get('name'):
            raise ValueError("Name parameter is required for Collect Evidence Action")
        
        if not parameters.get('description'):
            raise ValueError("Description parameter is required for Collect Evidence Action")
        
        if not parameters.get('requirement_id') and not parameters.get('activity_id'):
            raise ValueError("Either requirement_id or activity_id parameter is required for Collect Evidence Action")
        
        # Create the evidence data
        evidence_data = {
            "name": parameters.get('name'),
            "description": parameters.get('description'),
            "requirement_id": parameters.get('requirement_id'),
            "activity_id": parameters.get('activity_id'),
            "type": parameters.get('type', 'document'),
            "source": parameters.get('source'),
            "collection_method": parameters.get('collection_method', 'automated'),
            "content": parameters.get('content'),
            "url": parameters.get('url'),
            "metadata": parameters.get('metadata', {})
        }
        
        # Add collection timestamp
        from datetime import datetime
        evidence_data["metadata"]["collected_at"] = datetime.now().isoformat()
        evidence_data["metadata"]["collected_by"] = "automation"
        
        # If tracking manager is available, create the evidence
        if self.tracking_manager and hasattr(self.tracking_manager, 'create_evidence'):
            try:
                evidence = self.tracking_manager.create_evidence(evidence_data)
                
                return {
                    "success": True,
                    "evidence": evidence
                }
            except Exception as e:
                logger.error("Error collecting evidence:", exc_info=True)
                
                raise ValueError(f"Failed to collect evidence: {str(e)}")
        else:
            # Return mock data if tracking manager is not available
            import uuid
            
            return {
                "success": True,
                "evidence": {
                    "id": str(uuid.uuid4()),
                    **evidence_data,
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }
            }
