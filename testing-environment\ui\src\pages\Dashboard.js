import React from 'react';
import { Box, Typography, Button, Paper, Grid } from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';

function Dashboard() {
  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" gutterBottom>
          NovaConnect Dashboard
        </Typography>
        <Button 
          variant="contained" 
          startIcon={<AddIcon />}
          href="/partner-portal/connectors/new"
        >
          Create New Connector
        </Button>
      </Box>
      
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>
              Welcome to NovaConnect
            </Typography>
            <Typography variant="body1" paragraph>
              This is a test environment for the NovaConnect Universal API Connector.
            </Typography>
            <Typography variant="body1">
              Use the "Create New Connector" button to start building a new connector.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}

export default Dashboard;
