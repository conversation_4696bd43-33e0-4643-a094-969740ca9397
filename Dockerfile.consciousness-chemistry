# Multi-stage Dockerfile for NovaFuse Consciousness Chemistry Engine (Python Service)
# This Dockerfile builds the Python-based consciousness chemistry analysis service

# Stage 1: Base Python environment
FROM python:3.11-slim AS base
LABEL maintainer="NovaFuse Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="NovaFuse Consciousness Chemistry Engine"

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    ca-certificates \
    dumb-init \
    && rm -rf /var/lib/apt/lists/*

# Create app user for security
RUN groupadd -r novafuse && useradd -r -g novafuse novafuse

# Set working directory
WORKDIR /app

# Stage 2: Dependencies installation
FROM base AS deps

# Copy requirements files
COPY consciousness-chemistry-engine/requirements.txt ./consciousness-chemistry-engine/
COPY src/psi_tensor_core/requirements.txt ./src/psi_tensor_core/

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r consciousness-chemistry-engine/requirements.txt && \
    pip install --no-cache-dir -r src/psi_tensor_core/requirements.txt

# Stage 3: Build environment  
FROM base AS builder

# Copy requirements and install dependencies
COPY --from=deps /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=deps /usr/local/bin /usr/local/bin

# Copy source code
COPY consciousness-chemistry-engine/ ./consciousness-chemistry-engine/
COPY src/psi_tensor_core/ ./src/psi_tensor_core/
COPY src/comphyology/ ./src/comphyology/
COPY config/ ./config/

# Create Python bytecode for faster startup
RUN python -m compileall .

# Stage 4: Development environment
FROM base AS development
ENV PYTHONPATH=/app
ENV FLASK_ENV=development
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

COPY --from=deps /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=deps /usr/local/bin /usr/local/bin

# Install development dependencies
RUN pip install --no-cache-dir pytest pytest-cov flake8 black

COPY . .
RUN chown -R novafuse:novafuse /app
USER novafuse

EXPOSE 5000
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

CMD ["python", "-m", "consciousness-chemistry-engine.app"]

# Stage 5: Production environment
FROM base AS production
ENV PYTHONPATH=/app
ENV FLASK_ENV=production
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV GUNICORN_WORKERS=4
ENV GUNICORN_TIMEOUT=120

# Install production WSGI server
RUN pip install --no-cache-dir gunicorn

# Copy built application
COPY --from=builder --chown=novafuse:novafuse /app ./

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/temp && \
    chown -R novafuse:novafuse /app/logs /app/data /app/temp

# Switch to non-root user
USER novafuse

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=15s --start-period=90s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Use gunicorn for production
ENTRYPOINT ["dumb-init", "--"]
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "--timeout", "120", "--access-logfile", "-", "--error-logfile", "-", "consciousness-chemistry-engine.app:app"]

# Stage 6: Testing environment
FROM development AS testing
ENV PYTHONPATH=/app
RUN pip install --no-cache-dir pytest pytest-cov pytest-xdist
COPY . .
USER novafuse
CMD ["python", "-m", "pytest", "-v", "--cov=consciousness-chemistry-engine"]

# Stage 7: Data science/research environment
FROM base AS research
ENV PYTHONPATH=/app
ENV JUPYTER_ENABLE_LAB=yes

# Install additional research tools
RUN pip install --no-cache-dir \
    jupyter \
    jupyterlab \
    plotly \
    seaborn \
    scikit-learn \
    tensorflow \
    torch \
    transformers

COPY --from=builder --chown=novafuse:novafuse /app ./
RUN chown -R novafuse:novafuse /app
USER novafuse

EXPOSE 8888
CMD ["jupyter", "lab", "--ip=0.0.0.0", "--port=8888", "--no-browser", "--allow-root"]
