/**
 * NovaConnect UAC API Load Test
 * 
 * This script tests the performance and scalability of NovaConnect UAC API endpoints.
 * 
 * Usage:
 * k6 run api-load-test.js
 */

import http from 'k6/http';
import { check, group, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';
import { SharedArray } from 'k6/data';
import { randomItem, randomIntBetween } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';
import { options } from './k6-config.js';

// Custom metrics
const errorRate = new Rate('error_rate');
const dataNormalizationDuration = new Trend('data_normalization_duration');

// Load test data
const testData = new SharedArray('test_data', function() {
  return JSON.parse(open('./test-data.json'));
});

// API base URL
const baseUrl = __ENV.API_BASE_URL || 'http://localhost:3001';

// API key for authentication
const apiKey = __ENV.API_KEY || 'test-api-key';

// Default headers
const headers = {
  'Content-Type': 'application/json',
  'X-API-Key': apiKey
};

/**
 * Setup function - called once per VU
 */
export function setup() {
  // Verify API is accessible
  const res = http.get(`${baseUrl}/health`);
  if (res.status !== 200) {
    throw new Error(`API is not accessible: ${res.status} ${res.body}`);
  }
  
  console.log('API is accessible, starting load test');
  
  return {
    connectorIds: [],
    normalizedData: []
  };
}

/**
 * Teardown function - called once after all VUs are done
 */
export function teardown(data) {
  console.log(`Load test completed. Created ${data.connectorIds.length} connectors.`);
}

/**
 * Default function - called for each VU iteration
 */
export default function(data) {
  group('Health Check', () => {
    const res = http.get(`${baseUrl}/health`);
    
    check(res, {
      'status is 200': (r) => r.status === 200,
      'response time < 200ms': (r) => r.timings.duration < 200
    });
    
    errorRate.add(res.status !== 200);
  });
  
  group('API Endpoints', () => {
    // Test connector endpoints
    testConnectorEndpoints(data);
    
    // Test data normalization
    testDataNormalization(data);
    
    // Test error handling
    testErrorHandling();
    
    // Small sleep to prevent overwhelming the server
    sleep(randomIntBetween(0.1, 0.5));
  });
}

/**
 * Test connector endpoints
 */
function testConnectorEndpoints(data) {
  group('Connector Endpoints', () => {
    // Get connectors
    const getRes = http.get(`${baseUrl}/api/connectors`, { headers });
    
    check(getRes, {
      'get connectors status is 200': (r) => r.status === 200,
      'get connectors response time < 300ms': (r) => r.timings.duration < 300,
      'get connectors returns array': (r) => Array.isArray(JSON.parse(r.body))
    });
    
    errorRate.add(getRes.status !== 200);
    
    // Create connector
    const testConnector = randomItem(testData.connectors);
    const createRes = http.post(
      `${baseUrl}/api/connectors`,
      JSON.stringify(testConnector),
      { headers }
    );
    
    check(createRes, {
      'create connector status is 201': (r) => r.status === 201,
      'create connector response time < 500ms': (r) => r.timings.duration < 500,
      'create connector returns id': (r) => JSON.parse(r.body).id !== undefined
    });
    
    errorRate.add(createRes.status !== 201);
    
    // Store connector ID for later use
    if (createRes.status === 201) {
      const connectorId = JSON.parse(createRes.body).id;
      data.connectorIds.push(connectorId);
      
      // Get connector by ID
      const getByIdRes = http.get(
        `${baseUrl}/api/connectors/${connectorId}`,
        { headers }
      );
      
      check(getByIdRes, {
        'get connector by id status is 200': (r) => r.status === 200,
        'get connector by id response time < 300ms': (r) => r.timings.duration < 300,
        'get connector by id returns correct connector': (r) => JSON.parse(r.body).id === connectorId
      });
      
      errorRate.add(getByIdRes.status !== 200);
      
      // Test connector
      const testConnectorRes = http.post(
        `${baseUrl}/api/connectors/${connectorId}/test`,
        JSON.stringify({ testData: randomItem(testData.testRequests) }),
        { headers }
      );
      
      check(testConnectorRes, {
        'test connector status is 200': (r) => r.status === 200,
        'test connector response time < 1000ms': (r) => r.timings.duration < 1000,
        'test connector returns success': (r) => JSON.parse(r.body).success === true
      });
      
      errorRate.add(testConnectorRes.status !== 200);
      
      // Update connector
      const updateRes = http.put(
        `${baseUrl}/api/connectors/${connectorId}`,
        JSON.stringify({
          ...testConnector,
          name: `${testConnector.name}-updated`
        }),
        { headers }
      );
      
      check(updateRes, {
        'update connector status is 200': (r) => r.status === 200,
        'update connector response time < 500ms': (r) => r.timings.duration < 500,
        'update connector returns updated connector': (r) => JSON.parse(r.body).name.includes('updated')
      });
      
      errorRate.add(updateRes.status !== 200);
      
      // Delete connector (only delete some to keep data for other tests)
      if (Math.random() < 0.3) {
        const deleteRes = http.del(
          `${baseUrl}/api/connectors/${connectorId}`,
          null,
          { headers }
        );
        
        check(deleteRes, {
          'delete connector status is 204': (r) => r.status === 204,
          'delete connector response time < 300ms': (r) => r.timings.duration < 300
        });
        
        errorRate.add(deleteRes.status !== 204);
        
        // Remove from data
        data.connectorIds = data.connectorIds.filter(id => id !== connectorId);
      }
    }
  });
}

/**
 * Test data normalization
 */
function testDataNormalization(data) {
  group('Data Normalization', () => {
    // Get random test data
    const testData = randomItem(testData.normalizationRequests);
    
    // Start timer
    const startTime = new Date();
    
    // Normalize data
    const normalizeRes = http.post(
      `${baseUrl}/api/normalize`,
      JSON.stringify(testData),
      { headers }
    );
    
    // Calculate duration
    const duration = new Date() - startTime;
    
    // Record normalization duration
    dataNormalizationDuration.add(duration);
    
    check(normalizeRes, {
      'normalize data status is 200': (r) => r.status === 200,
      'normalize data response time < 100ms': (r) => r.timings.duration < 100,
      'normalize data returns normalized data': (r) => JSON.parse(r.body).normalized !== undefined
    });
    
    errorRate.add(normalizeRes.status !== 200);
    
    // Store normalized data for later use
    if (normalizeRes.status === 200) {
      data.normalizedData.push(JSON.parse(normalizeRes.body).normalized);
    }
    
    // Batch normalization
    const batchNormalizeRes = http.post(
      `${baseUrl}/api/normalize/batch`,
      JSON.stringify({ items: testData.items || [testData] }),
      { headers }
    );
    
    check(batchNormalizeRes, {
      'batch normalize data status is 200': (r) => r.status === 200,
      'batch normalize data response time < 500ms': (r) => r.timings.duration < 500,
      'batch normalize data returns normalized data': (r) => Array.isArray(JSON.parse(r.body).normalized)
    });
    
    errorRate.add(batchNormalizeRes.status !== 200);
  });
}

/**
 * Test error handling
 */
function testErrorHandling() {
  group('Error Handling', () => {
    // Test 404 error
    const notFoundRes = http.get(
      `${baseUrl}/api/non-existent-endpoint`,
      { headers }
    );
    
    check(notFoundRes, {
      'not found status is 404': (r) => r.status === 404,
      'not found response time < 100ms': (r) => r.timings.duration < 100,
      'not found returns error': (r) => JSON.parse(r.body).error !== undefined
    });
    
    // Test validation error
    const validationErrorRes = http.post(
      `${baseUrl}/api/connectors`,
      JSON.stringify({ invalid: 'data' }),
      { headers }
    );
    
    check(validationErrorRes, {
      'validation error status is 400': (r) => r.status === 400,
      'validation error response time < 100ms': (r) => r.timings.duration < 100,
      'validation error returns error': (r) => JSON.parse(r.body).error !== undefined,
      'validation error returns validation errors': (r) => JSON.parse(r.body).error.validation !== undefined
    });
    
    // Test authentication error
    const authErrorRes = http.get(
      `${baseUrl}/api/connectors`,
      { headers: { 'Content-Type': 'application/json' } } // No API key
    );
    
    check(authErrorRes, {
      'auth error status is 401': (r) => r.status === 401,
      'auth error response time < 100ms': (r) => r.timings.duration < 100,
      'auth error returns error': (r) => JSON.parse(r.body).error !== undefined
    });
    
    // Test rate limit (this might actually trigger rate limiting)
    for (let i = 0; i < 10; i++) {
      const rateLimitRes = http.get(
        `${baseUrl}/api/connectors`,
        { headers }
      );
      
      if (rateLimitRes.status === 429) {
        check(rateLimitRes, {
          'rate limit status is 429': (r) => r.status === 429,
          'rate limit response time < 100ms': (r) => r.timings.duration < 100,
          'rate limit returns error': (r) => JSON.parse(r.body).error !== undefined,
          'rate limit returns retry-after header': (r) => r.headers['Retry-After'] !== undefined
        });
        
        break;
      }
    }
  });
}
