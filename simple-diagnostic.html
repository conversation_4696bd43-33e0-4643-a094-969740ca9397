<!DOCTYPE html>
<html>
<head>
    <title>NovaFuse Diagnostic</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #1a1a1a; 
            color: white; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
        }
        .status { 
            background: #2d2d2d; 
            padding: 20px; 
            border-radius: 10px; 
            margin: 20px 0; 
        }
        .success { border-left: 5px solid #22c55e; }
        .error { border-left: 5px solid #ef4444; }
        .warning { border-left: 5px solid #f59e0b; }
        .btn { 
            background: #667eea; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 5px; 
        }
        .btn:hover { background: #5a67d8; }
        .output { 
            background: #000; 
            color: #00ff00; 
            padding: 15px; 
            border-radius: 5px; 
            font-family: monospace; 
            white-space: pre-wrap; 
            max-height: 300px; 
            overflow-y: auto; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 NovaFuse Diagnostic Tool</h1>
        <p>Let's figure out what's happening with your dashboards...</p>
        
        <div class="status">
            <h3>🌐 Server Status</h3>
            <button class="btn" onclick="checkServer()">Check Server</button>
            <button class="btn" onclick="checkAPI()">Check API</button>
            <button class="btn" onclick="listFiles()">List Test Files</button>
            <div id="server-status"></div>
        </div>
        
        <div class="status">
            <h3>📁 File System Check</h3>
            <button class="btn" onclick="checkFiles()">Check UUFT Files</button>
            <button class="btn" onclick="checkDirectories()">Check Directories</button>
            <div id="file-status"></div>
        </div>
        
        <div class="status">
            <h3>🔗 Dashboard Links</h3>
            <a href="http://localhost:3300" target="_blank" class="btn">Main Dashboard</a>
            <a href="http://localhost:3300/real-test-dashboard" target="_blank" class="btn">Test Dashboard</a>
            <a href="http://localhost:3300/api/status" target="_blank" class="btn">API Status</a>
        </div>
        
        <div class="status">
            <h3>📊 Output Log</h3>
            <div id="output" class="output">Ready for diagnostics...\n</div>
        </div>
    </div>

    <script>
        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        async function checkServer() {
            log('🔍 Checking server status...');
            try {
                const response = await fetch('http://localhost:3300/api/status');
                const data = await response.json();
                
                document.getElementById('server-status').innerHTML = `
                    <div class="success">
                        ✅ Server is running on port ${data.port}<br>
                        📊 Total test files: ${data.testFiles?.total || 'Unknown'}<br>
                        🕐 Server time: ${data.timestamp}
                    </div>
                `;
                log('✅ Server is responding correctly');
                log(`📊 Found ${data.testFiles?.total || 0} test files`);
            } catch (error) {
                document.getElementById('server-status').innerHTML = `
                    <div class="error">❌ Server not responding: ${error.message}</div>
                `;
                log(`❌ Server error: ${error.message}`);
            }
        }

        async function checkAPI() {
            log('🔍 Checking API endpoints...');
            try {
                const response = await fetch('http://localhost:3300/api/test-files');
                const data = await response.json();
                
                log(`✅ API working - found ${data.total} test files`);
                data.categories.forEach(cat => {
                    log(`📂 ${cat.name.toUpperCase()}: ${cat.count} files`);
                });
            } catch (error) {
                log(`❌ API error: ${error.message}`);
            }
        }

        async function listFiles() {
            log('📁 Listing actual test files...');
            try {
                const response = await fetch('http://localhost:3300/api/test-files');
                const data = await response.json();
                
                data.categories.forEach(cat => {
                    if (cat.files.length > 0) {
                        log(`\n📂 ${cat.name.toUpperCase()} (${cat.count} files):`);
                        cat.files.slice(0, 5).forEach(file => {
                            log(`  📄 ${file.name} (${(file.size/1024).toFixed(1)}KB)`);
                        });
                        if (cat.files.length > 5) {
                            log(`  ... and ${cat.files.length - 5} more files`);
                        }
                    }
                });
            } catch (error) {
                log(`❌ File listing error: ${error.message}`);
            }
        }

        async function checkFiles() {
            log('🔍 Checking for UUFT test files...');
            
            // Check if we can access a specific file
            try {
                const response = await fetch('http://localhost:3300/api/test-file/UUFT_test_01.py');
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Found UUFT_test_01.py (${data.lines} lines, ${(data.fullSize/1024).toFixed(1)}KB)`);
                    log(`📄 First few lines:\n${data.content.split('\n').slice(0, 3).join('\n')}`);
                } else {
                    log('❌ Could not access UUFT_test_01.py');
                }
            } catch (error) {
                log(`❌ File check error: ${error.message}`);
            }
        }

        function checkDirectories() {
            log('📁 Checking directory structure...');
            log('This diagnostic is running from the browser');
            log('Server should be scanning the NovaFuse directory for test files');
            log('Expected files: UUFT_test_01.py through UUFT_test_20.py');
        }

        // Auto-run basic checks
        window.onload = function() {
            log('🚀 Starting NovaFuse diagnostics...');
            setTimeout(checkServer, 500);
            setTimeout(checkAPI, 1000);
            setTimeout(listFiles, 1500);
        };
    </script>
</body>
</html>
