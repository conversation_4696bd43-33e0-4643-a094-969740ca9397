#!/usr/bin/env python3
"""
Analyze LBL-TCP-3 data for 18/82 patterns and other UUFT patterns.
This script processes the LBL-TCP-3 dataset from the Internet Traffic Archive
and analyzes it for various patterns related to the Universal Unified Field Theory (UUFT).
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import logging
import json
from collections import defaultdict

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('lbl_tcp_analysis.log')
    ]
)
logger = logging.getLogger('LBL_TCP_Analysis')

# Constants
PATTERN_1882_RATIO = 18 / 82
PATTERN_1882_THRESHOLD = 0.05  # 5% threshold for considering a match
PI = np.pi
RESULTS_DIR = "lbl_tcp_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

# Helper function to make objects JSON serializable
def json_serializable(obj):
    """Convert any non-serializable values to strings."""
    if isinstance(obj, (np.int8, np.int16, np.int32, np.int64,
                        np.uint8, np.uint16, np.uint32, np.uint64)):
        return int(obj)
    elif isinstance(obj, (np.float16, np.float32, np.float64)):
        return float(obj)
    elif isinstance(obj, (bool, np.bool_)):
        return bool(obj)
    elif isinstance(obj, (np.ndarray,)):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {k: json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [json_serializable(item) for item in obj]
    else:
        return str(obj)

def load_lbl_tcp_data(file_path, sample_size=100000):
    """Load the LBL-TCP-3 data into a pandas DataFrame."""
    logger.info(f"Loading LBL-TCP-3 data from {file_path} (sample size: {sample_size})")

    # Define column names based on the format described in the documentation
    columns = ['timestamp', 'source_host', 'dest_host', 'source_port', 'dest_port', 'data_bytes']

    # Load the data with a limited number of rows for performance
    df = pd.read_csv(file_path, sep=' ', header=None, names=columns, nrows=sample_size)

    # Convert timestamp to datetime
    df['timestamp'] = pd.to_numeric(df['timestamp'])

    logger.info(f"Loaded {len(df)} TCP records")
    return df

def load_lbl_sf_data(file_path, sample_size=32441):
    """Load the LBL-TCP-3 SYN/FIN data into a pandas DataFrame."""
    logger.info(f"Loading LBL-TCP-3 SYN/FIN data from {file_path} (sample size: {sample_size})")

    # Define column names based on the format described in the documentation
    columns = ['timestamp', 'source_host', 'dest_host', 'source_port', 'dest_port',
               'flags', 'sequence', 'ack']

    # Load the data with a limited number of rows for performance
    df = pd.read_csv(file_path, sep=' ', header=None, names=columns, nrows=sample_size)

    # Convert timestamp to datetime
    df['timestamp'] = pd.to_numeric(df['timestamp'])

    logger.info(f"Loaded {len(df)} SYN/FIN records")
    return df

def analyze_1882_patterns(data, feature_name):
    """Analyze a dataset for 18/82 patterns."""
    logger.info(f"Analyzing {feature_name} for 18/82 patterns...")

    # Check if we have enough data
    if len(data) < 10:
        logger.warning(f"Not enough data points in {feature_name} to test for 18/82 patterns")
        return None

    # Sort the data
    sorted_data = np.sort(data)
    total_sum = np.sum(sorted_data)

    # Find the best 18/82 split
    best_split_idx = None
    best_proximity = float('inf')

    for i in range(1, len(sorted_data)):
        lower_sum = np.sum(sorted_data[:i])
        upper_sum = np.sum(sorted_data[i:])

        if total_sum == 0:
            continue

        lower_ratio = lower_sum / total_sum
        upper_ratio = upper_sum / total_sum

        # Calculate proximity to 18/82 ratio
        proximity_to_1882 = abs((lower_ratio / upper_ratio) - (18 / 82))

        if proximity_to_1882 < best_proximity:
            best_proximity = proximity_to_1882
            best_split_idx = i

    if best_split_idx is None:
        logger.warning(f"Could not find a valid 18/82 split for {feature_name}")
        return None

    # Calculate the actual ratios
    lower_sum = np.sum(sorted_data[:best_split_idx])
    upper_sum = np.sum(sorted_data[best_split_idx:])

    if total_sum == 0:
        lower_ratio = 0
        upper_ratio = 0
    else:
        lower_ratio = lower_sum / total_sum
        upper_ratio = upper_sum / total_sum

    # Calculate proximity to 18/82
    proximity_percent = abs((lower_ratio / upper_ratio) - (18 / 82)) / (18 / 82) * 100
    is_1882_pattern = proximity_percent <= PATTERN_1882_THRESHOLD * 100

    result = {
        "feature": feature_name,
        "total_data_points": len(data),
        "split_index": best_split_idx,
        "lower_sum": float(lower_sum),
        "upper_sum": float(upper_sum),
        "lower_ratio": float(lower_ratio),
        "upper_ratio": float(upper_ratio),
        "proximity_to_1882_percent": float(proximity_percent),
        "is_1882_pattern": is_1882_pattern
    }

    logger.info(f"18/82 pattern analysis for {feature_name}:")
    logger.info(f"  Lower ratio: {lower_ratio:.4f}, Upper ratio: {upper_ratio:.4f}")
    logger.info(f"  Proximity to 18/82: {proximity_percent:.2f}%")
    logger.info(f"  18/82 pattern present: {is_1882_pattern}")

    return result

def analyze_pi_relationships(data, feature_name):
    """Analyze a dataset for Pi relationships."""
    logger.info(f"Analyzing {feature_name} for Pi relationships...")

    # Check if we have enough data
    if len(data) < 10:
        logger.warning(f"Not enough data points in {feature_name} to test for Pi relationships")
        return None

    pi_values = []
    pi_ratios = []
    pi_10e3_values = []
    pi_10e3_ratios = []

    # Check for values close to Pi
    for i, value in enumerate(data):
        if abs(value - PI) / PI < 0.05:
            pi_values.append({
                "type": "Individual Value Proximity to Pi",
                "value": float(value),
                "target": float(PI),
                "proximity_percent": float(abs(value - PI) / PI * 100),
                "index": i
            })

        if abs(value - (PI * 1000)) / (PI * 1000) < 0.05:
            pi_10e3_values.append({
                "type": "Individual Value Proximity to Pi*10^3",
                "value": float(value),
                "target": float(PI * 1000),
                "proximity_percent": float(abs(value - (PI * 1000)) / (PI * 1000) * 100),
                "index": i
            })

    # Check for ratios close to Pi
    for i in range(len(data)):
        for j in range(i+1, len(data)):
            if data[i] == 0 or data[j] == 0:
                continue

            ratio = data[i] / data[j]
            if abs(ratio - PI) / PI < 0.05:
                pi_ratios.append({
                    "type": "Ratio Proximity to Pi",
                    "value1": float(data[i]),
                    "value2": float(data[j]),
                    "ratio": float(ratio),
                    "target": float(PI),
                    "proximity_percent": float(abs(ratio - PI) / PI * 100),
                    "indices": [i, j]
                })

            ratio = data[j] / data[i]
            if abs(ratio - PI) / PI < 0.05:
                pi_ratios.append({
                    "type": "Inverse Ratio Proximity to Pi",
                    "value1": float(data[j]),
                    "value2": float(data[i]),
                    "ratio": float(ratio),
                    "target": float(PI),
                    "proximity_percent": float(abs(ratio - PI) / PI * 100),
                    "indices": [j, i]
                })

            # Check for ratios close to Pi*10^3
            ratio = data[i] / data[j]
            if abs(ratio - (PI * 1000)) / (PI * 1000) < 0.05:
                pi_10e3_ratios.append({
                    "type": "Ratio Proximity to Pi*10^3",
                    "value1": float(data[i]),
                    "value2": float(data[j]),
                    "ratio": float(ratio),
                    "target": float(PI * 1000),
                    "proximity_percent": float(abs(ratio - (PI * 1000)) / (PI * 1000) * 100),
                    "indices": [i, j]
                })

            ratio = data[j] / data[i]
            if abs(ratio - (PI * 1000)) / (PI * 1000) < 0.05:
                pi_10e3_ratios.append({
                    "type": "Inverse Ratio Proximity to Pi*10^3",
                    "value1": float(data[j]),
                    "value2": float(data[i]),
                    "ratio": float(ratio),
                    "target": float(PI * 1000),
                    "proximity_percent": float(abs(ratio - (PI * 1000)) / (PI * 1000) * 100),
                    "indices": [j, i]
                })

    result = {
        "feature": feature_name,
        "total_data_points": len(data),
        "pi_values_count": len(pi_values),
        "pi_ratios_count": len(pi_ratios),
        "pi_10e3_values_count": len(pi_10e3_values),
        "pi_10e3_ratios_count": len(pi_10e3_ratios),
        "pi_values": pi_values[:10],  # Limit to first 10 for brevity
        "pi_ratios": pi_ratios[:10],  # Limit to first 10 for brevity
        "pi_10e3_values": pi_10e3_values[:10],  # Limit to first 10 for brevity
        "pi_10e3_ratios": pi_10e3_ratios[:10]  # Limit to first 10 for brevity
    }

    logger.info(f"Pi relationships analysis for {feature_name}:")
    logger.info(f"  Pi values: {len(pi_values)}, Pi ratios: {len(pi_ratios)}")
    logger.info(f"  Pi*10^3 values: {len(pi_10e3_values)}, Pi*10^3 ratios: {len(pi_10e3_ratios)}")

    return result

def main():
    """Main function to analyze the LBL-TCP-3 data."""
    logger.info("Starting LBL-TCP-3 data analysis...")

    # Load the data
    tcp_data = load_lbl_tcp_data("lbl-tcp-3/lbl-tcp-3.tcp")
    sf_data = load_lbl_sf_data("lbl-tcp-3/lbl-tcp-3.sf")

    # Analyze various features for 18/82 patterns
    results_1882 = []

    # Analyze data bytes
    result = analyze_1882_patterns(tcp_data['data_bytes'].values, "data_bytes")
    if result:
        results_1882.append(result)

    # Analyze source and destination ports
    result = analyze_1882_patterns(tcp_data['source_port'].values, "source_port")
    if result:
        results_1882.append(result)

    result = analyze_1882_patterns(tcp_data['dest_port'].values, "dest_port")
    if result:
        results_1882.append(result)

    # Analyze host distribution
    source_host_counts = tcp_data['source_host'].value_counts().values
    result = analyze_1882_patterns(source_host_counts, "source_host_distribution")
    if result:
        results_1882.append(result)

    dest_host_counts = tcp_data['dest_host'].value_counts().values
    result = analyze_1882_patterns(dest_host_counts, "dest_host_distribution")
    if result:
        results_1882.append(result)

    # Analyze time intervals between packets
    time_diffs = np.diff(tcp_data['timestamp'].values)
    result = analyze_1882_patterns(time_diffs, "packet_time_intervals")
    if result:
        results_1882.append(result)

    # Analyze Pi relationships
    results_pi = []

    # Use a smaller sample size for Pi relationship analysis (computationally intensive)
    pi_sample_size = 500

    # Analyze data bytes for Pi relationships
    result = analyze_pi_relationships(tcp_data['data_bytes'].values[:pi_sample_size], "data_bytes")
    if result:
        results_pi.append(result)

    # Analyze source and destination ports for Pi relationships
    result = analyze_pi_relationships(tcp_data['source_port'].values[:pi_sample_size], "source_port")
    if result:
        results_pi.append(result)

    result = analyze_pi_relationships(tcp_data['dest_port'].values[:pi_sample_size], "dest_port")
    if result:
        results_pi.append(result)

    # Analyze time intervals for Pi relationships
    result = analyze_pi_relationships(time_diffs[:pi_sample_size], "packet_time_intervals")
    if result:
        results_pi.append(result)

    # Save results
    combined_results = {
        "dataset": "LBL-TCP-3",
        "total_tcp_records": len(tcp_data),
        "total_sf_records": len(sf_data),
        "1882_pattern_results": results_1882,
        "pi_relationship_results": results_pi
    }

    # Convert to JSON serializable
    serializable_results = json_serializable(combined_results)

    # Save to file
    results_path = os.path.join(RESULTS_DIR, "lbl_tcp_analysis_results.json")
    try:
        with open(results_path, 'w') as f:
            json.dump(serializable_results, f, indent=2)
        logger.info(f"Results saved to {results_path}")
    except Exception as e:
        logger.error(f"Error saving results to JSON: {e}")

    logger.info("LBL-TCP-3 data analysis complete.")

    # Print summary
    logger.info("\n=== LBL-TCP-3 Analysis Summary ===")
    logger.info(f"Total TCP records analyzed: {len(tcp_data)}")
    logger.info(f"Total SYN/FIN records analyzed: {len(sf_data)}")
    logger.info(f"Features with 18/82 patterns: {sum(1 for r in results_1882 if r['is_1882_pattern'])}/{len(results_1882)}")

    pi_values_total = sum(r['pi_values_count'] for r in results_pi)
    pi_ratios_total = sum(r['pi_ratios_count'] for r in results_pi)
    pi_10e3_values_total = sum(r['pi_10e3_values_count'] for r in results_pi)
    pi_10e3_ratios_total = sum(r['pi_10e3_ratios_count'] for r in results_pi)

    logger.info(f"Total Pi values found: {pi_values_total}")
    logger.info(f"Total Pi ratios found: {pi_ratios_total}")
    logger.info(f"Total Pi*10^3 values found: {pi_10e3_values_total}")
    logger.info(f"Total Pi*10^3 ratios found: {pi_10e3_ratios_total}")

    return combined_results

if __name__ == "__main__":
    main()
