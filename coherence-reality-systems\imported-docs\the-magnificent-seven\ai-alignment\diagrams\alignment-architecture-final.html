<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3-6-9-12-13 Alignment Architecture</title>
    <style>

        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }

        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 800px;
            min-min-height: 70px; /* Minimum height, will expand with content */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: visible; /* Allow content to be fully visible */
        }

        /* Ensure Universal Pattern Elements stay within main container */
        .universal-pattern-elements {
            position: relative;
            width: 650px;
            margin-top: 450px; /* Position it near the bottom of the main container */
            margin-left: 50px; /* Center it within the main container */
            z-index: 1;
        }

        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: visible; /* Allow content to be fully visible */
            min-height: 100px; /* Minimum height */
            min-width: 200px; /* Minimum width */
        }

        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Prevents content from spilling out */
            text-overflow: ellipsis; /* Shows ellipsis for overflowing text */
        }

        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically */
            align-items: center;
            text-align: center;
            padding: 5px; /* Reduced padding */
            overflow: visible; /* Allow content to be fully visible */
            border-top-left-radius: 0; /* Flat corner for number */
            min-height: 50px; /* Minimum height */
            min-width: 100px; /* Minimum width */
        }

        /* Component Number Styles - Integrated into corner */

        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 0 0 6px 0; /* Rounded on bottom-right corner only */
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px; /* Smaller font */
            font-weight: bold;
            z-index: 2;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }

        .component-label {
            font-weight: bold;
            margin-top: 10px; /* Reduced margin */
            margin-bottom: 2px; /* Reduced margin */
            font-size: 12px; /* Smaller font */
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }

        .component-content {
            font-size: 10px; /* Smaller font */
            text-align: center;
            width: 100%;
            padding: 0;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            line-height: 1.2; /* Tighter line spacing */
        }

        /* Arrow Styles */
        .arrow-line {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            z-index: 0;
        }

        .arrow-head {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 0;
        }

        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }

        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }

        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }

        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }

        /* SVG Styles */
        svg {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 0;
        }

        /* Equation Styles */
        .equation {
            font-weight: bold;
            font-size: 14px;
            margin-top: 5px;
            text-align: center;
        }
    
    </style>
</head>
<body>
    <h1>FIG. 5: 3-6-9-12-13 Alignment Architecture</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="overflow: visible; min-height: 100px; min-width: 200px;" style="width: 850px; height: 580px; left: 25px; top: 20px;">
            <div class="container-label">CYBER-SAFETY FRAMEWORK: 3-6-9-12-13 ALIGNMENT ARCHITECTURE</div>
        </div>
        
        <!-- 3 Core Infrastructure Components -->
        <div class="container-box" style="overflow: visible; min-height: 100px; min-width: 200px;" style="width: 800px; height: 580px; left: 50px; top: 70px;">
            <div class="container-label">3 CORE INFRASTRUCTURE COMPONENTS</div>
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 100px; top: 110px; width: 200px; height: 100px;">
            <div class="component-number-inside">501</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Pillar 3</div>
            Self-Destructing Compliance Servers
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 350px; top: 110px; width: 200px; height: 100px;">
            <div class="component-number-inside">502</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Pillar 9</div>
            Post-Quantum Immutable Journal
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 600px; top: 110px; width: 200px; height: 100px;">
            <div class="component-number-inside">503</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Pillar 12</div>
            C-Suite Directive to Code Compiler
        </div>
        
        <!-- 6 Data Processing Components -->
        <div class="container-box" style="overflow: visible; min-height: 100px; min-width: 200px;" style="width: 800px; height: 580px; left: 50px; top: 210px;">
            <div class="container-label">6 DATA PROCESSING COMPONENTS</div>
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 75px; top: 250px; width: 120px; height: 100px;">
            <div class="component-number-inside">504</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Nova 3</div>
            NovaTrack
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 205px; top: 250px; width: 120px; height: 100px;">
            <div class="component-number-inside">505</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Nova 6</div>
            NovaFlowX
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 335px; top: 250px; width: 120px; height: 100px;">
            <div class="component-number-inside">506</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Nova 9</div>
            NovaThink
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 465px; top: 250px; width: 120px; height: 100px;">
            <div class="component-number-inside">507</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Nova 12</div>
            NovaDNA
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 595px; top: 250px; width: 120px; height: 100px;">
            <div class="component-number-inside">508</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Pillar 6</div>
            Cost Optimizer
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 725px; top: 250px; width: 120px; height: 100px;">
            <div class="component-number-inside">509</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Pillar 7</div>
            Training Data
        </div>
        
        <!-- 9 Continuances -->
        <div class="container-box" style="overflow: visible; min-height: 100px; min-width: 200px;" style="width: 800px; height: 580px; left: 50px; top: 350px;">
            <div class="container-label">9 INDUSTRY-SPECIFIC CONTINUANCES</div>
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 60px; top: 390px; width: 80px; height: 100px;">
            <div class="component-number-inside">510</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">C1</div>
            Financial
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 150px; top: 390px; width: 80px; height: 100px;">
            <div class="component-number-inside">511</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">C2</div>
            Healthcare
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 240px; top: 390px; width: 80px; height: 100px;">
            <div class="component-number-inside">512</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">C3</div>
            Education
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 330px; top: 390px; width: 80px; height: 100px;">
            <div class="component-number-inside">513</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">C4</div>
            Government
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 420px; top: 390px; width: 80px; height: 100px;">
            <div class="component-number-inside">514</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">C5</div>
            Infrastructure
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 510px; top: 390px; width: 80px; height: 100px;">
            <div class="component-number-inside">515</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">C6</div>
            AI Governance
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 600px; top: 390px; width: 80px; height: 100px;">
            <div class="component-number-inside">516</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">C7</div>
            Supply Chain
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 690px; top: 390px; width: 80px; height: 100px;">
            <div class="component-number-inside">517</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">C8</div>
            Insurance
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 780px; top: 390px; width: 80px; height: 100px;">
            <div class="component-number-inside">518</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">C9</div>
            Mobile/IoT
        </div>
        
        <!-- 12 Pillars -->
        <div class="container-box" style="overflow: visible; min-height: 100px; min-width: 200px;" style="width: 800px; height: 580px; left: 50px; top: 490px;">
            <div class="container-label" style="top: 5px;">12 PILLARS</div>
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 425px; top: 520px; width: 100px; height: 100px;">
            <div class="component-number-inside">519</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">P1-P12</div>
        </div>
        
        <!-- 12+1 Novas -->
        <div class="container-box" style="overflow: visible; min-height: 100px; min-width: 200px;" style="width: 800px; height: 580px; left: 50px; top: 590px;">
            <div class="container-label" style="top: 5px;">12+1 UNIVERSAL NOVAS</div>
        </div>
        
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 425px; top: 620px; width: 100px; height: 100px;">
            <div class="component-number-inside">520</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">N1-N13</div>
        </div>
        
        <!-- Connecting arrows -->
        <div class="arrow" style="left: 200px; top: 190px; height: 70px;"></div>
        <div class="arrow" style="left: 450px; top: 190px; height: 70px;"></div>
        <div class="arrow" style="left: 700px; top: 190px; height: 70px;"></div>
        
        <div class="arrow" style="left: 135px; top: 330px; height: 70px;"></div>
        <div class="arrow" style="left: 265px; top: 330px; height: 70px;"></div>
        <div class="arrow" style="left: 395px; top: 330px; height: 70px;"></div>
        <div class="arrow" style="left: 525px; top: 330px; height: 70px;"></div>
        <div class="arrow" style="left: 655px; top: 330px; height: 70px;"></div>
        <div class="arrow" style="left: 785px; top: 330px; height: 70px;"></div>
        
        <div class="arrow" style="left: 100px; top: 450px; height: 70px;"></div>
        <div class="arrow" style="left: 190px; top: 450px; height: 70px;"></div>
        <div class="arrow" style="left: 280px; top: 450px; height: 70px;"></div>
        <div class="arrow" style="left: 370px; top: 450px; height: 70px;"></div>
        <div class="arrow" style="left: 460px; top: 450px; height: 70px;"></div>
        <div class="arrow" style="left: 550px; top: 450px; height: 70px;"></div>
        <div class="arrow" style="left: 640px; top: 450px; height: 70px;"></div>
        <div class="arrow" style="left: 730px; top: 450px; height: 70px;"></div>
        <div class="arrow" style="left: 820px; top: 450px; height: 70px;"></div>
        
        <div class="arrow" style="left: 475px; top: 570px; height: 70px;"></div>
        
        <!-- Legend -->
        <div class="legend" style="position: absolute; right: 10px; bottom: 10px; background-color: white; border: 1px solid #ddd; border-radius: 4px; padding: 5px; z-index: 10; width: 200px;">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Core Infrastructure (3)</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Data Processing (6)</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Continuances (9)</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Pillars (12)</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Novas (12+1)</div>
            </div>
        </div>
        
        <!-- Inventor Label -->
        <div class="inventor-label" style="position: absolute; left: 10px; bottom: 10px; font-size: 12px; font-style: italic; color: #333;">Inventor: David Nigel Irvin</div>
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
</div>
</body>
</html>






