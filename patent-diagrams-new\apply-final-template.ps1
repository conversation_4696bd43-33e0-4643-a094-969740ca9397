# Script to apply the final improved template to all patent diagram HTML files
# This script ensures consistent styling, proper margins, and correct component numbering

# List of specific patent diagram files
$patentDiagrams = @(
    "cyber-safety-architecture-fixed.html",
    "unified-field-theory.html",
    "alignment-architecture-final.html",
    "novafuse-components.html",
    "hardware-software-implementation.html",
    "detailed-data-flow.html",
    "18-82-principle.html",
    "cyber-safety-incident-response.html",
    "adaptive-compliance-process.html",
    "comphyology-universal-application-framework.html",
    "financial-services-architecture.html",
    "healthcare-architecture.html",
    "visualization-output-examples.html"
)

# Extract CSS from the template
$templatePath = Join-Path -Path "D:\novafuse-api-superstore\patent-diagrams-new" -ChildPath "patent-diagram-template.html"
$templateContent = Get-Content -Path $templatePath -Raw

# Extract CSS from template
if ($templateContent -match "(?s)<style>(.*?)</style>") {
    $cssTemplate = $Matches[1]
} else {
    Write-Error "Could not extract CSS from template"
    exit 1
}

# Process each patent diagram file
foreach ($fileName in $patentDiagrams) {
    $filePath = Join-Path -Path "D:\novafuse-api-superstore\patent-diagrams-new" -ChildPath $fileName

    # Check if file exists
    if (Test-Path $filePath) {
        Write-Host "Processing $fileName..."

        # Read the file content
        $content = Get-Content -Path $filePath -Raw

        # Create a backup of the original file
        Copy-Item -Path $filePath -Destination "$filePath.final.bak"

        # Replace CSS styles
        # First, find the style section
        if ($content -match "<style>(.*?)</style>") {
            $styleSection = $Matches[0]
            $newStyleSection = "<style>`n$cssTemplate`n    </style>"
            $content = $content -replace [regex]::Escape($styleSection), $newStyleSection
        }

        # Convert component-number to component-number-inside
        $content = $content -replace '<div class="component-number"([^>]*)>', '<div class="component-number-inside">'

        # Update arrow colors from #333 to #555555
        $content = $content -replace "border-color: transparent transparent transparent #333", "border-color: transparent transparent transparent #555555"
        $content = $content -replace "border-color: transparent #333 transparent transparent", "border-color: transparent #555555 transparent transparent"
        $content = $content -replace "border-color: #333 transparent transparent transparent", "border-color: #555555 transparent transparent transparent"

        # Update any stroke="#333" in SVG elements
        $content = $content -replace 'stroke="#333"', 'stroke="#555555"'

        # Update any fill="#333" in SVG elements
        $content = $content -replace 'fill="#333"', 'fill="#555555"'

        # Ensure inventor label is positioned correctly
        if ($content -match '<div class="inventor-label">') {
            if (!($content -match '<div class="inventor-label"[^>]*style="[^"]*position: absolute[^"]*"')) {
                $content = $content -replace '<div class="inventor-label">', '<div class="inventor-label" style="position: absolute; left: 10px; bottom: 30px; font-size: 12px; font-style: italic; color: #333; z-index: 10;">'
            }
        } else {
            # Add inventor label if missing
            $content = $content -replace '</div>\s*</body>', '    <div class="inventor-label">Inventor: David Nigel Irvin</div>
</div>
</body>'
        }

        # Ensure legend is positioned correctly
        if ($content -match '<div class="legend">') {
            if (!($content -match '<div class="legend"[^>]*style="[^"]*position: absolute[^"]*"')) {
                $content = $content -replace '<div class="legend">', '<div class="legend" style="position: absolute; right: 10px; bottom: 30px; background-color: white; border: 1px solid #ddd; border-radius: 4px; padding: 8px; z-index: 10; width: 200px; font-size: 12px;">'
            }
        }

        # Ensure diagram container has proper height
        $content = $content -replace 'height: \d+px', 'height: 700px'

        # Ensure main container has proper height
        if ($content -match '<div class="container-box"[^>]*style="[^"]*width: \d+px; height: \d+px[^"]*"') {
            $content = $content -replace '(width: \d+px; height: )\d+px', '${1}580px'
        }

        # Ensure component boxes have proper height
        $content = $content -replace '(<div class="component-box"[^>]*style="[^"]*width: \d+px; height: )\d+px', '${1}100px'

        # Write the updated content back to the file
        Set-Content -Path $filePath -Value $content

        Write-Host "Updated $fileName successfully with final template."
    } else {
        Write-Host "File not found: $fileName"
    }
}

Write-Host "All patent diagram files have been updated with the final template."
