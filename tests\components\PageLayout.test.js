import React from 'react';
import { render, screen } from '@testing-library/react';
import PageLayout from '../../components/PageLayout';

// Mock the Navigation component
jest.mock('../../components/Navigation', () => {
  return function MockNavigation() {
    return <div data-testid="mock-navigation">Navigation Component</div>;
  };
});

// Mock the SidebarLayout component
jest.mock('../../components/SidebarLayout', () => {
  return function MockSidebarLayout({ children, sidebarItems, sidebarTitle, title }) {
    return (
      <div data-testid="mock-sidebar-layout">
        <h1>{title}</h1>
        <h2>{sidebarTitle}</h2>
        <div data-testid="sidebar-items">
          {sidebarItems.map((item, index) => (
            <div key={index}>{item.label}</div>
          ))}
        </div>
        <div data-testid="content">{children}</div>
      </div>
    );
  };
});

describe('PageLayout', () => {
  const sidebarItems = [
    { label: 'Item 1', href: '/item1' },
    { label: 'Item 2', href: '/item2' },
    { label: 'Item 3', href: '/item3' }
  ];
  
  it('renders the Navigation component', () => {
    render(
      <PageLayout sidebarItems={sidebarItems} sidebarTitle="Sidebar Title" title="Page Title">
        <div>Page Content</div>
      </PageLayout>
    );
    
    // Check if Navigation component is rendered
    expect(screen.getByTestId('mock-navigation')).toBeInTheDocument();
  });
  
  it('renders the SidebarLayout component with correct props', () => {
    render(
      <PageLayout sidebarItems={sidebarItems} sidebarTitle="Sidebar Title" title="Page Title">
        <div>Page Content</div>
      </PageLayout>
    );
    
    // Check if SidebarLayout component is rendered
    expect(screen.getByTestId('mock-sidebar-layout')).toBeInTheDocument();
    
    // Check if title is passed correctly
    expect(screen.getByText('Page Title')).toBeInTheDocument();
    
    // Check if sidebarTitle is passed correctly
    expect(screen.getByText('Sidebar Title')).toBeInTheDocument();
    
    // Check if sidebarItems are passed correctly
    const sidebarItemsContainer = screen.getByTestId('sidebar-items');
    expect(sidebarItemsContainer).toHaveTextContent('Item 1');
    expect(sidebarItemsContainer).toHaveTextContent('Item 2');
    expect(sidebarItemsContainer).toHaveTextContent('Item 3');
  });
  
  it('renders children correctly', () => {
    render(
      <PageLayout sidebarItems={sidebarItems} sidebarTitle="Sidebar Title" title="Page Title">
        <div data-testid="page-content">Page Content</div>
      </PageLayout>
    );
    
    // Check if children are rendered
    const content = screen.getByTestId('content');
    expect(content).toContainElement(screen.getByTestId('page-content'));
    expect(content).toHaveTextContent('Page Content');
  });
});
