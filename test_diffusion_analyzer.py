#!/usr/bin/env python3
"""
Test script for UUFT Diffusion Analyzer
"""

import os
import logging
import numpy as np
import matplotlib.pyplot as plt
from uuft_social_analyzer import UUFTSocialNetwork
from uuft_diffusion_model import UUFTDiffusionModel
from uuft_diffusion_analyzer import UUFTDiffusionAnalyzer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_analyzer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('Test_Analyzer')

# Constants
RESULTS_DIR = "uuft_results/social"
os.makedirs(RESULTS_DIR, exist_ok=True)

def test_diffusion_analyzer():
    """Test the diffusion analyzer."""
    logger.info("Testing diffusion analyzer")
    
    # Create a small-world network
    network = UUFTSocialNetwork(
        num_nodes=50,
        network_type="small_world",
        uuft_bias=0.5
    )
    
    # Create diffusion model
    diffusion_model = UUFTDiffusionModel(
        social_network=network,
        model_type="uuft_optimized",
        uuft_parameters={
            "pi_influence": 0.5,
            "pattern_1882_strength": 0.7,
            "temporal_stability": 0.8
        }
    )
    
    # Initialize adopters
    diffusion_model.initialize_adopters(
        method="1882_optimized",
        initial_fraction=0.1
    )
    
    # Run simulation
    logger.info("Running diffusion simulation")
    diffusion_model.run_simulation(max_steps=20)
    
    # Create analyzer
    analyzer = UUFTDiffusionAnalyzer(
        diffusion_model=diffusion_model,
        pattern_threshold=0.1
    )
    
    # Run analyses
    logger.info("Running diffusion analyses")
    
    # Analyze adoption distribution
    adoption_result = analyzer.analyze_adoption_distribution()
    logger.info(f"Adoption distribution analysis: {adoption_result}")
    
    # Analyze influence patterns
    influence_result = analyzer.analyze_influence_patterns()
    logger.info(f"Influence patterns analysis: {influence_result}")
    
    # Analyze temporal stability
    temporal_result = analyzer.analyze_temporal_stability()
    logger.info(f"Temporal stability analysis: {temporal_result}")
    
    # Analyze π relationships
    pi_result = analyzer.analyze_pi_relationships()
    logger.info(f"π relationships analysis: {pi_result}")
    
    # Create comprehensive report
    report = analyzer.create_comprehensive_report(
        save_path=os.path.join(RESULTS_DIR, "test_analysis_report.json")
    )
    
    # Visualize 18/82 patterns
    analyzer.visualize_1882_patterns(
        save_path=os.path.join(RESULTS_DIR, "test_1882_patterns.png")
    )
    
    logger.info("Diffusion analyzer test completed successfully")

if __name__ == "__main__":
    test_diffusion_analyzer()
