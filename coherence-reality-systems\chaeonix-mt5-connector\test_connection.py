#!/usr/bin/env python3
"""
QUICK MT5 CONNECTION TEST
Test connection to <PERSON>'s MT5 account: ***********
"""

def test_mt5_connection():
    print("🧪 QUICK MT5 CONNECTION TEST")
    print("=" * 50)
    
    try:
        import MetaTrader5 as mt5
        print("✅ MetaTrader5 package imported successfully")
    except ImportError:
        print("❌ MetaTrader5 package not found")
        print("💡 Install with: pip install MetaTrader5")
        return False
    
    # Initialize MT5
    if not mt5.initialize():
        print("❌ MT5 initialization failed")
        print(f"Error: {mt5.last_error()}")
        return False
    
    print("✅ MT5 initialized successfully")
    
    # Account credentials
    login = ***********
    server = "MetaQuotes-Demo"
    password = "E*7gLkTd"
    
    print(f"\n🔌 Attempting connection...")
    print(f"   🏢 Server: {server}")
    print(f"   🆔 Login: {login}")
    print(f"   🔑 Password: {'*' * len(password)}")
    
    # Attempt login
    authorized = mt5.login(login=login, server=server, password=password)
    
    if not authorized:
        print("❌ Login failed")
        error = mt5.last_error()
        print(f"Error code: {error[0]}")
        print(f"Error message: {error[1]}")
        
        print(f"\n🔧 TROUBLESHOOTING:")
        print("   1. Verify MetaTrader 5 terminal is installed")
        print("   2. Check if MT5 terminal is running")
        print("   3. Verify account credentials are correct")
        print("   4. Ensure 'Allow automated trading' is enabled")
        print("   5. Check internet connection")
        
        mt5.shutdown()
        return False
    
    print("✅ Login successful!")
    
    # Get account info
    account_info = mt5.account_info()
    if account_info is None:
        print("❌ Failed to get account information")
        mt5.shutdown()
        return False
    
    print(f"\n📊 ACCOUNT INFORMATION:")
    print(f"   👤 Name: David Irvin")
    print(f"   🆔 Login: {account_info.login}")
    print(f"   🏢 Server: {account_info.server}")
    print(f"   💰 Balance: ${account_info.balance:,.2f} {account_info.currency}")
    print(f"   💎 Equity: ${account_info.equity:,.2f} {account_info.currency}")
    print(f"   📈 Profit: ${account_info.profit:,.2f} {account_info.currency}")
    print(f"   🔧 Leverage: 1:{account_info.leverage}")
    print(f"   🏦 Company: {account_info.company}")
    
    # Test symbol access
    print(f"\n📈 TESTING SYMBOL ACCESS:")
    test_symbols = ["EURUSD", "GBPUSD", "USDJPY"]
    
    for symbol in test_symbols:
        tick = mt5.symbol_info_tick(symbol)
        if tick is not None:
            print(f"   ✅ {symbol}: {tick.bid:.5f} / {tick.ask:.5f}")
        else:
            print(f"   ❌ {symbol}: Not available")
    
    # Check positions
    positions = mt5.positions_get()
    print(f"\n📋 CURRENT POSITIONS: {len(positions) if positions else 0}")
    
    if positions:
        for pos in positions:
            print(f"   🎯 {pos.symbol} {pos.type} {pos.volume} @ {pos.price_open}")
    else:
        print("   📭 No open positions")
    
    print(f"\n🌟 CONNECTION TEST SUCCESSFUL!")
    print("   ✅ MT5 connection established")
    print("   ✅ Account access verified")
    print("   ✅ Symbol data available")
    print("   ✅ Ready for CHAEONIX integration")
    
    # Shutdown
    mt5.shutdown()
    print(f"\n🙏 Connection closed gracefully")
    return True

if __name__ == "__main__":
    success = test_mt5_connection()
    
    if success:
        print(f"\n🚀 NEXT STEPS:")
        print("   1. Run: python connect_mt5_account.py")
        print("   2. Integrate with CHAEONIX dashboard")
        print("   3. Activate divine trading protocols")
    else:
        print(f"\n🔧 SETUP REQUIRED:")
        print("   1. Install MetaTrader 5 terminal")
        print("   2. Install Python package: pip install MetaTrader5")
        print("   3. Verify account credentials")
        print("   4. Run test again")
