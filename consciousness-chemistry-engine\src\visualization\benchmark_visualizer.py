"""
Visualization tools for quantum benchmark results.

This module provides functions to visualize the performance metrics
from quantum backend benchmarks.
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from matplotlib.ticker import MaxNLocator
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px

# Set style
plt.style.use('seaborn')
sns.set_palette("viridis")

class BenchmarkVisualizer:
    """Visualization tools for quantum benchmark results."""
    
    def __init__(self, results_dir: str):
        """Initialize the visualizer with benchmark results.
        
        Args:
            results_dir: Directory containing benchmark results
        """
        self.results_dir = Path(results_dir)
        self.results = self._load_results()
        self.df = self._create_dataframe()
    
    def _load_results(self) -> Dict[str, Any]:
        """Load benchmark results from JSON files."""
        result_files = list(self.results_dir.glob('**/benchmark_results.json'))
        
        if not result_files:
            raise FileNotFoundError(f"No benchmark results found in {self.results_dir}")
        
        all_results = []
        for result_file in result_files:
            with open(result_file) as f:
                data = json.load(f)
                all_results.extend(data['results'])
        
        return all_results
    
    def _create_dataframe(self) -> pd.DataFrame:
        """Create a pandas DataFrame from the results."""
        # Flatten the results for easier analysis
        rows = []
        for result in self.results:
            if not result['success']:
                continue
                
            row = {
                'sequence_name': result['sequence_name'],
                'sequence_length': result['sequence_length'],
                'backend': result['backend'],
                'duration': result['duration'],
                'cpu_avg': result.get('cpu_avg', np.nan),
                'cpu_max': result.get('cpu_max', np.nan),
                'memory_avg_mb': result.get('memory_avg_mb', np.nan),
                'memory_max_mb': result.get('memory_max_mb', np.nan),
                'gpu_memory_avg': result.get('gpu_memory_avg', np.nan),
                'gpu_memory_max': result.get('gpu_memory_max', np.nan),
                'gpu_load_avg': result.get('gpu_load_avg', np.nan)
            }
            
            # Add quantum info if available
            if 'result' in result and 'quantum_info' in result['result']:
                qinfo = result['result']['quantum_info']
                row.update({
                    'quantum_backend': qinfo.get('quantum_backend', ''),
                    'quantum_circuit_depth': qinfo.get('quantum_circuit_depth', 0),
                    'quantum_shots': qinfo.get('quantum_shots', 0),
                    'quantum_layers': qinfo.get('quantum_layers', 0)
                })
            
            rows.append(row)
        
        return pd.DataFrame(rows)
    
    def plot_duration_by_backend(self, output_file: Optional[str] = None) -> go.Figure:
        """Plot duration by backend and sequence length.
        
        Args:
            output_file: Optional path to save the plot
            
        Returns:
            Plotly figure object
        """
        if self.df.empty:
            raise ValueError("No successful benchmark results to visualize")
        
        fig = px.box(
            self.df, 
            x='backend', 
            y='duration',
            color='sequence_name',
            title='Processing Time by Backend and Sequence',
            labels={
                'duration': 'Processing Time (s)',
                'backend': 'Quantum Backend',
                'sequence_name': 'Sequence'
            },
            log_y=True
        )
        
        fig.update_layout(
            xaxis_title='Quantum Backend',
            yaxis_title='Processing Time (s, log scale)',
            legend_title='Sequence',
            hovermode='x',
            template='plotly_white'
        )
        
        if output_file:
            fig.write_html(str(Path(output_file).with_suffix('.html')))
            fig.write_image(str(Path(output_file).with_suffix('.png')))
        
        return fig
    
    def plot_resource_usage(self, output_file: Optional[str] = None) -> go.Figure:
        """Plot resource usage (CPU, GPU, memory) by backend.
        
        Args:
            output_file: Optional path to save the plot
            
        Returns:
            Plotly figure object
        """
        if self.df.empty:
            raise ValueError("No successful benchmark results to visualize")
        
        # Create subplots
        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=(
                'Average CPU Usage (%)',
                'Average Memory Usage (MB)',
                'Average GPU Memory Usage (MB)'
            ),
            vertical_spacing=0.1
        )
        
        # Add CPU usage
        fig.add_trace(
            go.Box(
                x=self.df['backend'],
                y=self.df['cpu_avg'],
                name='CPU Usage (%)',
                boxpoints='all',
                jitter=0.3,
                pointpos=-1.8,
                marker=dict(size=4, opacity=0.5)
            ),
            row=1, col=1
        )
        
        # Add memory usage
        fig.add_trace(
            go.Box(
                x=self.df['backend'],
                y=self.df['memory_avg_mb'],
                name='Memory (MB)',
                boxpoints='all',
                jitter=0.3,
                pointpos=-1.8,
                marker=dict(size=4, opacity=0.5)
            ),
            row=2, col=1
        )
        
        # Add GPU memory usage if available
        if not self.df['gpu_memory_avg'].isna().all():
            fig.add_trace(
                go.Box(
                    x=self.df['backend'],
                    y=self.df['gpu_memory_avg'],
                    name='GPU Memory (MB)',
                    boxpoints='all',
                    jitter=0.3,
                    pointpos=-1.8,
                    marker=dict(size=4, opacity=0.5)
                ),
                row=3, col=1
            )
        
        # Update layout
        fig.update_layout(
            title='Resource Usage by Backend',
            showlegend=False,
            height=1000,
            template='plotly_white',
            margin=dict(t=60, b=60, l=60, r=60)
        )
        
        # Update y-axes
        fig.update_yaxes(title_text='CPU Usage (%)', row=1, col=1)
        fig.update_yaxes(title_text='Memory (MB)', row=2, col=1)
        if not self.df['gpu_memory_avg'].isna().all():
            fig.update_yaxes(title_text='GPU Memory (MB)', row=3, col=1)
        
        if output_file:
            fig.write_html(str(Path(output_file).with_suffix('.html')))
            fig.write_image(str(Path(output_file).with_suffix('.png')))
        
        return fig
    
    def plot_quantum_metrics(self, output_file: Optional[str] = None) -> Optional[go.Figure]:
        """Plot quantum-specific metrics.
        
        Args:
            output_file: Optional path to save the plot
            
        Returns:
            Plotly figure object, or None if no quantum metrics available
        """
        if 'quantum_circuit_depth' not in self.df.columns:
            return None
            
        # Filter out non-quantum backends
        quantum_df = self.df[self.df['quantum_circuit_depth'] > 0]
        
        if quantum_df.empty:
            return None
            
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=(
                'Quantum Circuit Depth by Backend',
                'Quantum Shots by Backend',
                'Quantum Layers by Backend',
                'Quantum Backend Distribution'
            ),
            specs=[[{"type": "box"}, {"type": "box"}],
                  [{"type": "box"}, {"type": "pie"}]]
        )
        
        # Circuit Depth
        fig.add_trace(
            go.Box(
                x=quantum_df['backend'],
                y=quantum_df['quantum_circuit_depth'],
                name='Circuit Depth',
                boxpoints='all',
                jitter=0.3,
                pointpos=-1.8,
                marker=dict(size=4, opacity=0.5)
            ),
            row=1, col=1
        )
        
        # Shots
        fig.add_trace(
            go.Box(
                x=quantum_df['backend'],
                y=quantum_df['quantum_shots'],
                name='Shots',
                boxpoints='all',
                jitter=0.3,
                pointpos=-1.8,
                marker=dict(size=4, opacity=0.5)
            ),
            row=1, col=2
        )
        
        # Layers
        fig.add_trace(
            go.Box(
                x=quantum_df['backend'],
                y=quantum_df['quantum_layers'],
                name='Layers',
                boxpoints='all',
                jitter=0.3,
                pointpos=-1.8,
                marker=dict(size=4, opacity=0.5)
            ),
            row=2, col=1
        )
        
        # Backend distribution
        if 'quantum_backend' in quantum_df.columns:
            backend_counts = quantum_df['quantum_backend'].value_counts()
            fig.add_trace(
                go.Pie(
                    labels=backend_counts.index,
                    values=backend_counts.values,
                    name='Backend Distribution',
                    hole=0.4
                ),
                row=2, col=2
            )
        
        # Update layout
        fig.update_layout(
            title='Quantum Metrics by Backend',
            showlegend=False,
            height=800,
            template='plotly_white',
            margin=dict(t=60, b=60, l=60, r=60)
        )
        
        if output_file:
            fig.write_html(str(Path(output_file).with_suffix('.html')))
            fig.write_image(str(Path(output_file).with_suffix('.png')))
        
        return fig
    
    def generate_report(self, output_dir: str) -> None:
        """Generate a comprehensive HTML report with all visualizations.
        
        Args:
            output_dir: Directory to save the report
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate all plots
        duration_plot = self.plot_duration_by_backend()
        resource_plot = self.plot_resource_usage()
        quantum_plot = self.plot_quantum_metrics()
        
        # Save individual plots
        plots_dir = output_dir / 'plots'
        plots_dir.mkdir(exist_ok=True)
        
        duration_plot.write_html(str(plots_dir / 'duration_plot.html'))
        resource_plot.write_html(str(plots_dir / 'resource_plot.html'))
        if quantum_plot:
            quantum_plot.write_html(str(plots_dir / 'quantum_plot.html'))
        
        # Generate summary statistics
        summary = self.df.describe().round(2).to_html()
        
        # Create HTML report
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Quantum Backend Benchmark Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                .plot-container {{ margin: 30px 0; }}
                .summary {{ margin: 20px 0; }}
                h1, h2 {{ color: #2c3e50; }}
                iframe {{ border: none; width: 100%; height: 500px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Quantum Backend Benchmark Report</h1>
                <p>Generated on {date}</p>
                
                <div class="summary">
                    <h2>Summary Statistics</h2>
                    {summary}
                </div>
                
                <div class="plot-container">
                    <h2>Processing Time by Backend</h2>
                    <iframe src="plots/duration_plot.html"></iframe>
                </div>
                
                <div class="plot-container">
                    <h2>Resource Usage</h2>
                    <iframe src="plots/resource_plot.html"></iframe>
                </div>
        """
        
        if quantum_plot:
            html_content += """
                <div class="plot-container">
                    <h2>Quantum Metrics</h2>
                    <iframe src="plots/quantum_plot.html"></iframe>
                </div>
            """
        
        html_content += """
            </div>
        </body>
        </html>
        """.format(
            date=pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
            summary=summary
        )
        
        # Save report
        report_file = output_dir / 'benchmark_report.html'
        with open(report_file, 'w') as f:
            f.write(html_content)
        
        print(f"Report generated: {report_file}")


def visualize_benchmark_results(results_dir: str, output_dir: str) -> None:
    """Generate visualizations for benchmark results.
    
    Args:
        results_dir: Directory containing benchmark results
        output_dir: Directory to save visualizations
    """
    visualizer = BenchmarkVisualizer(results_dir)
    visualizer.generate_report(output_dir)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Visualize quantum benchmark results')
    parser.add_argument('results_dir', help='Directory containing benchmark results')
    parser.add_argument('-o', '--output-dir', default='benchmark_visualizations',
                       help='Output directory for visualizations')
    
    args = parser.parse_args()
    visualize_benchmark_results(args.results_dir, args.output_dir)
