/**
 * Control Testing API - Routes
 * 
 * This file defines the routes for the Control Testing API.
 */

const express = require('express');
const router = express.Router();
const { controlController, testPlanController, testResultController } = require('./controllers');
const validate = require('../../../middleware/validate');
const validationSchemas = require('./validation');
const auth = require('../../../middleware/auth');

/**
 * Control Routes
 */
// Get all controls
router.get('/controls', auth, validate(validationSchemas.query.pagination, 'query'), controlController.getAllControls);

// Get control by ID
router.get('/controls/:id', auth, controlController.getControlById);

// Create a new control
router.post('/controls', auth, validate(validationSchemas.control.create), controlController.createControl);

// Update a control
router.put('/controls/:id', auth, validate(validationSchemas.control.update), controlController.updateControl);

// Delete a control
router.delete('/controls/:id', auth, controlController.deleteControl);

/**
 * Test Plan Routes
 */
// Get all test plans
router.get('/test-plans', auth, validate(validationSchemas.query.pagination, 'query'), testPlanController.getAllTestPlans);

// Get test plan by ID
router.get('/test-plans/:id', auth, testPlanController.getTestPlanById);

// Create a new test plan
router.post('/test-plans', auth, validate(validationSchemas.testPlan.create), testPlanController.createTestPlan);

// Update a test plan
router.put('/test-plans/:id', auth, validate(validationSchemas.testPlan.update), testPlanController.updateTestPlan);

// Delete a test plan
router.delete('/test-plans/:id', auth, testPlanController.deleteTestPlan);

// Add a control to a test plan
router.post('/test-plans/:id/controls', auth, validate(validationSchemas.testPlan.addControl), testPlanController.addControlToTestPlan);

// Remove a control from a test plan
router.delete('/test-plans/:id/controls/:controlId', auth, testPlanController.removeControlFromTestPlan);

/**
 * Test Result Routes
 */
// Get all test results
router.get('/test-results', auth, validate(validationSchemas.query.pagination, 'query'), testResultController.getAllTestResults);

// Get test result by ID
router.get('/test-results/:id', auth, testResultController.getTestResultById);

// Create a new test result
router.post('/test-results', auth, validate(validationSchemas.testResult.create), testResultController.createTestResult);

// Update a test result
router.put('/test-results/:id', auth, validate(validationSchemas.testResult.update), testResultController.updateTestResult);

// Delete a test result
router.delete('/test-results/:id', auth, testResultController.deleteTestResult);

module.exports = router;
