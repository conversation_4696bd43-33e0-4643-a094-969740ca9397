const express = require('express');
const { validateRequest } = require('./validation');
const controllers = require('./controllers');

const router = express.Router();

/**
 * @swagger
 * /governance/esg/disclosures:
 *   get:
 *     summary: Get a list of disclosures
 *     description: Returns a paginated list of disclosures with optional filtering
 *     tags: [ESG Disclosures]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: status
 *         in: query
 *         description: Filter by disclosure status
 *         schema:
 *           type: string
 *           enum: [not-started, in-progress, submitted, approved, rejected]
 *       - name: regulationType
 *         in: query
 *         description: Filter by regulation type
 *         schema:
 *           type: string
 *           enum: [mandatory, voluntary]
 *       - name: jurisdiction
 *         in: query
 *         description: Filter by jurisdiction
 *         schema:
 *           type: string
 *       - name: reportingFrequency
 *         in: query
 *         description: Filter by reporting frequency
 *         schema:
 *           type: string
 *           enum: [annual, semi-annual, quarterly, one-time]
 *       - name: dueBefore
 *         in: query
 *         description: Filter by due date before (format YYYY-MM-DD)
 *         schema:
 *           type: string
 *           format: date
 *       - name: dueAfter
 *         in: query
 *         description: Filter by due date after (format YYYY-MM-DD)
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Disclosure'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/', controllers.getDisclosures);

/**
 * @swagger
 * /governance/esg/disclosures/{id}:
 *   get:
 *     summary: Get a specific disclosure
 *     description: Returns a specific disclosure by ID
 *     tags: [ESG Disclosures]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Disclosure ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Disclosure'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/:id', controllers.getDisclosureById);

/**
 * @swagger
 * /governance/esg/disclosures:
 *   post:
 *     summary: Create a new disclosure
 *     description: Creates a new disclosure
 *     tags: [ESG Disclosures]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               regulationType:
 *                 type: string
 *                 enum: [mandatory, voluntary]
 *               regulationName:
 *                 type: string
 *               jurisdiction:
 *                 type: string
 *               applicabilityDate:
 *                 type: string
 *                 format: date
 *               reportingFrequency:
 *                 type: string
 *                 enum: [annual, semi-annual, quarterly, one-time]
 *               nextDueDate:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [not-started, in-progress, submitted, approved, rejected]
 *               assignedTo:
 *                 type: string
 *               frameworkIds:
 *                 type: array
 *                 items:
 *                   type: string
 *               metricIds:
 *                 type: array
 *                 items:
 *                   type: string
 *               documents:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     type:
 *                       type: string
 *                     url:
 *                       type: string
 *             required:
 *               - title
 *               - regulationType
 *               - regulationName
 *               - jurisdiction
 *               - reportingFrequency
 *               - status
 *     responses:
 *       201:
 *         description: Disclosure created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Disclosure'
 *                 message:
 *                   type: string
 *                   example: Disclosure created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/', validateRequest('createDisclosure'), controllers.createDisclosure);

/**
 * @swagger
 * /governance/esg/disclosures/{id}:
 *   put:
 *     summary: Update a disclosure
 *     description: Updates an existing disclosure
 *     tags: [ESG Disclosures]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Disclosure ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               regulationType:
 *                 type: string
 *                 enum: [mandatory, voluntary]
 *               regulationName:
 *                 type: string
 *               jurisdiction:
 *                 type: string
 *               applicabilityDate:
 *                 type: string
 *                 format: date
 *               reportingFrequency:
 *                 type: string
 *                 enum: [annual, semi-annual, quarterly, one-time]
 *               nextDueDate:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [not-started, in-progress, submitted, approved, rejected]
 *               assignedTo:
 *                 type: string
 *               frameworkIds:
 *                 type: array
 *                 items:
 *                   type: string
 *               metricIds:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Disclosure updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Disclosure'
 *                 message:
 *                   type: string
 *                   example: Disclosure updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/:id', validateRequest('updateDisclosure'), controllers.updateDisclosure);

/**
 * @swagger
 * /governance/esg/disclosures/{id}:
 *   delete:
 *     summary: Delete a disclosure
 *     description: Deletes an existing disclosure
 *     tags: [ESG Disclosures]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Disclosure ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Disclosure deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Disclosure deleted successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/:id', controllers.deleteDisclosure);

/**
 * @swagger
 * /governance/esg/disclosures/{id}/documents:
 *   post:
 *     summary: Add a document to a disclosure
 *     description: Adds a new document to a specific disclosure
 *     tags: [ESG Disclosures]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Disclosure ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               type:
 *                 type: string
 *               url:
 *                 type: string
 *             required:
 *               - name
 *               - type
 *               - url
 *     responses:
 *       201:
 *         description: Document added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     type:
 *                       type: string
 *                     url:
 *                       type: string
 *                     uploadedBy:
 *                       type: string
 *                     uploadedAt:
 *                       type: string
 *                       format: date-time
 *                 message:
 *                   type: string
 *                   example: Document added successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/:id/documents', validateRequest('addDocument'), controllers.addDocument);

/**
 * @swagger
 * /governance/esg/disclosures/{id}/documents/{documentId}:
 *   delete:
 *     summary: Remove a document from a disclosure
 *     description: Removes a document from a specific disclosure
 *     tags: [ESG Disclosures]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Disclosure ID
 *         required: true
 *         schema:
 *           type: string
 *       - name: documentId
 *         in: path
 *         description: Document ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Document removed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Document removed successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/:id/documents/:documentId', controllers.removeDocument);

/**
 * @swagger
 * /governance/esg/disclosures/{id}/submissions:
 *   post:
 *     summary: Add a submission record to a disclosure
 *     description: Adds a new submission record to a specific disclosure
 *     tags: [ESG Disclosures]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Disclosure ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [submitted, approved, rejected]
 *               notes:
 *                 type: string
 *             required:
 *               - date
 *               - status
 *     responses:
 *       201:
 *         description: Submission record added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     date:
 *                       type: string
 *                       format: date
 *                     status:
 *                       type: string
 *                     submittedBy:
 *                       type: string
 *                     notes:
 *                       type: string
 *                 message:
 *                   type: string
 *                   example: Submission record added successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/:id/submissions', validateRequest('addSubmission'), controllers.addSubmission);

/**
 * @swagger
 * /governance/esg/regulations:
 *   get:
 *     summary: Get a list of regulations
 *     description: Returns a paginated list of regulations with optional filtering
 *     tags: [ESG Disclosures]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: type
 *         in: query
 *         description: Filter by regulation type
 *         schema:
 *           type: string
 *           enum: [mandatory, voluntary]
 *       - name: category
 *         in: query
 *         description: Filter by regulation category
 *         schema:
 *           type: string
 *           enum: [environmental, social, governance, general]
 *       - name: jurisdiction
 *         in: query
 *         description: Filter by jurisdiction
 *         schema:
 *           type: string
 *       - name: reportingFrequency
 *         in: query
 *         description: Filter by reporting frequency
 *         schema:
 *           type: string
 *           enum: [annual, semi-annual, quarterly, one-time]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Regulation'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/regulations', controllers.getRegulations);

/**
 * @swagger
 * /governance/esg/regulations/{id}:
 *   get:
 *     summary: Get a specific regulation
 *     description: Returns a specific regulation by ID
 *     tags: [ESG Disclosures]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulation ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Regulation'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/regulations/:id', controllers.getRegulationById);

/**
 * @swagger
 * /governance/esg/regulations:
 *   post:
 *     summary: Create a new regulation
 *     description: Creates a new regulation
 *     tags: [ESG Disclosures]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [mandatory, voluntary]
 *               category:
 *                 type: string
 *                 enum: [environmental, social, governance, general]
 *               jurisdiction:
 *                 type: string
 *               issuingAuthority:
 *                 type: string
 *               effectiveDate:
 *                 type: string
 *                 format: date
 *               reportingFrequency:
 *                 type: string
 *                 enum: [annual, semi-annual, quarterly, one-time]
 *               website:
 *                 type: string
 *               requirements:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     description:
 *                       type: string
 *               frameworkIds:
 *                 type: array
 *                 items:
 *                   type: string
 *             required:
 *               - name
 *               - description
 *               - type
 *               - category
 *               - jurisdiction
 *               - issuingAuthority
 *               - effectiveDate
 *               - reportingFrequency
 *     responses:
 *       201:
 *         description: Regulation created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Regulation'
 *                 message:
 *                   type: string
 *                   example: Regulation created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/regulations', validateRequest('createRegulation'), controllers.createRegulation);

/**
 * @swagger
 * /governance/esg/regulations/{id}:
 *   put:
 *     summary: Update a regulation
 *     description: Updates an existing regulation
 *     tags: [ESG Disclosures]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulation ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [mandatory, voluntary]
 *               category:
 *                 type: string
 *                 enum: [environmental, social, governance, general]
 *               jurisdiction:
 *                 type: string
 *               issuingAuthority:
 *                 type: string
 *               effectiveDate:
 *                 type: string
 *                 format: date
 *               reportingFrequency:
 *                 type: string
 *                 enum: [annual, semi-annual, quarterly, one-time]
 *               website:
 *                 type: string
 *               requirements:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     description:
 *                       type: string
 *               frameworkIds:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Regulation updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Regulation'
 *                 message:
 *                   type: string
 *                   example: Regulation updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/regulations/:id', validateRequest('updateRegulation'), controllers.updateRegulation);

/**
 * @swagger
 * /governance/esg/regulations/{id}:
 *   delete:
 *     summary: Delete a regulation
 *     description: Deletes an existing regulation
 *     tags: [ESG Disclosures]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Regulation ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Regulation deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Regulation deleted successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/regulations/:id', controllers.deleteRegulation);

module.exports = router;
