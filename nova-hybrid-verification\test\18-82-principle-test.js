/**
 * Test for 18/82 Principle Implementation
 * 
 * This test demonstrates the 18/82 Principle in action within the
 * Hybrid DAG-based Zero-Knowledge System. It shows how 18% of nodes
 * handle 82% of critical operations, optimizing performance and
 * resource allocation.
 */

const NovaHybridVerification = require('../index');
const { performance } = require('perf_hooks');

/**
 * Test the 18/82 Principle implementation
 */
async function test1882Principle() {
  console.log('🚀 Starting 18/82 Principle Test...\n');

  // Initialize the Nova Hybrid Verification System
  const hybridSystem = new NovaHybridVerification({
    enableLogging: true,
    enableMetrics: true,
    maxNodes: 100,
    nodeSelectionRatio: 0.18 // 18/82 Principle
  });

  try {
    // Initialize the system
    console.log('📋 Initializing system...');
    await hybridSystem.initialize();
    console.log('✅ System initialized successfully\n');

    // Test 1: Critical Operations (should use critical nodes - 18%)
    console.log('🔥 Test 1: Critical Operations');
    const criticalTransactions = [];
    
    for (let i = 0; i < 10; i++) {
      const startTime = performance.now();
      
      const result = await hybridSystem.addTransaction({
        data: { 
          type: 'critical_operation',
          value: Math.random() * 1000,
          timestamp: Date.now()
        },
        priority: 'critical',
        metadata: { testType: 'critical' }
      });
      
      const endTime = performance.now();
      const processingTime = endTime - startTime;
      
      criticalTransactions.push({
        transactionId: result.transactionId,
        processingTime,
        nodeId: result.nodeId
      });
      
      console.log(`   Transaction ${i + 1}: ${result.transactionId} (${processingTime.toFixed(2)}ms)`);
    }

    // Test 2: Standard Operations (should use standard nodes - 82%)
    console.log('\n📊 Test 2: Standard Operations');
    const standardTransactions = [];
    
    for (let i = 0; i < 20; i++) {
      const startTime = performance.now();
      
      const result = await hybridSystem.addTransaction({
        data: { 
          type: 'standard_operation',
          value: Math.random() * 100,
          timestamp: Date.now()
        },
        priority: 'standard',
        metadata: { testType: 'standard' }
      });
      
      const endTime = performance.now();
      const processingTime = endTime - startTime;
      
      standardTransactions.push({
        transactionId: result.transactionId,
        processingTime,
        nodeId: result.nodeId
      });
      
      if (i < 5) { // Only show first 5 for brevity
        console.log(`   Transaction ${i + 1}: ${result.transactionId} (${processingTime.toFixed(2)}ms)`);
      }
    }
    
    if (standardTransactions.length > 5) {
      console.log(`   ... and ${standardTransactions.length - 5} more transactions`);
    }

    // Test 3: Batch Operations (should optimize node selection)
    console.log('\n🔄 Test 3: Batch Operations');
    const batchTransactions = [];
    
    for (let i = 0; i < 15; i++) {
      batchTransactions.push({
        data: { 
          type: 'batch_operation',
          value: Math.random() * 500,
          timestamp: Date.now()
        },
        metadata: { testType: 'batch', batchIndex: i }
      });
    }
    
    const startTime = performance.now();
    const batchResult = await hybridSystem.createBatch(batchTransactions);
    const endTime = performance.now();
    const batchProcessingTime = endTime - startTime;
    
    console.log(`   Batch ID: ${batchResult.batchId}`);
    console.log(`   Transactions: ${batchResult.transactionCount}`);
    console.log(`   Processing Time: ${batchProcessingTime.toFixed(2)}ms`);
    console.log(`   Selected Nodes: ${batchResult.selectedNodes.length}`);

    // Test 4: Zero-Knowledge Proof Generation
    console.log('\n🔐 Test 4: Zero-Knowledge Proof Generation');
    const proofStartTime = performance.now();
    
    const proof = await hybridSystem.generateProof({
      id: 'test-proof-transaction',
      data: {
        type: 'proof_test',
        sensitiveData: 'This data should remain private',
        publicData: 'This data can be verified'
      }
    });
    
    const proofEndTime = performance.now();
    const proofGenerationTime = proofEndTime - proofStartTime;
    
    console.log(`   Proof ID: ${proof.proofId}`);
    console.log(`   Proof Type: ${proof.proofType}`);
    console.log(`   Generation Time: ${proofGenerationTime.toFixed(2)}ms`);
    
    // Verify the proof
    const verifyStartTime = performance.now();
    const verificationResult = await hybridSystem.verifyProof(proof);
    const verifyEndTime = performance.now();
    const verificationTime = verifyEndTime - verifyStartTime;
    
    console.log(`   Verification Result: ${verificationResult.verified}`);
    console.log(`   Verification Time: ${verificationTime.toFixed(2)}ms`);

    // Test 5: System Metrics and 18/82 Analysis
    console.log('\n📈 Test 5: System Metrics and 18/82 Analysis');
    const metrics = hybridSystem.dagSystem.getMetrics();
    
    console.log('   System Metrics:');
    console.log(`     Total Transactions: ${metrics.transactions.total}`);
    console.log(`     Processed Transactions: ${metrics.transactions.processed}`);
    console.log(`     Failed Transactions: ${metrics.transactions.failed}`);
    console.log(`     Average Transaction Time: ${metrics.performance.averageTransactionTime.toFixed(2)}ms`);
    
    if (metrics.components.nodeSelector) {
      const nodeStats = metrics.components.nodeSelector;
      console.log('\n   Node Selector Metrics (18/82 Principle):');
      console.log(`     Total Nodes: ${nodeStats.totalNodes}`);
      console.log(`     Critical Nodes: ${nodeStats.criticalNodes} (${(nodeStats.criticalNodeRatio * 100).toFixed(1)}%)`);
      console.log(`     Standard Nodes: ${nodeStats.standardNodes} (${((1 - nodeStats.criticalNodeRatio) * 100).toFixed(1)}%)`);
      console.log(`     Average Load: ${nodeStats.averageLoad.toFixed(2)}%`);
      console.log(`     Average Performance: ${nodeStats.averagePerformance.toFixed(2)}`);
      
      // Verify 18/82 Principle
      const criticalRatio = nodeStats.criticalNodeRatio;
      const isWithinTolerance = Math.abs(criticalRatio - 0.18) < 0.05; // 5% tolerance
      
      console.log(`\n   18/82 Principle Verification:`);
      console.log(`     Target Critical Ratio: 18%`);
      console.log(`     Actual Critical Ratio: ${(criticalRatio * 100).toFixed(1)}%`);
      console.log(`     Within Tolerance: ${isWithinTolerance ? '✅ Yes' : '❌ No'}`);
    }

    // Performance Analysis
    console.log('\n⚡ Performance Analysis:');
    const avgCriticalTime = criticalTransactions.reduce((sum, tx) => sum + tx.processingTime, 0) / criticalTransactions.length;
    const avgStandardTime = standardTransactions.reduce((sum, tx) => sum + tx.processingTime, 0) / standardTransactions.length;
    const avgBatchTimePerTx = batchProcessingTime / batchResult.transactionCount;
    
    console.log(`   Average Critical Transaction Time: ${avgCriticalTime.toFixed(2)}ms`);
    console.log(`   Average Standard Transaction Time: ${avgStandardTime.toFixed(2)}ms`);
    console.log(`   Average Batch Transaction Time: ${avgBatchTimePerTx.toFixed(2)}ms`);
    console.log(`   Proof Generation Time: ${proofGenerationTime.toFixed(2)}ms`);
    console.log(`   Proof Verification Time: ${verificationTime.toFixed(2)}ms`);

    // Test 6: Verify Batch
    console.log('\n🔍 Test 6: Batch Verification');
    const batchVerifyStartTime = performance.now();
    const batchVerificationResult = await hybridSystem.verifyBatch(batchResult.batchId);
    const batchVerifyEndTime = performance.now();
    const batchVerificationTime = batchVerifyEndTime - batchVerifyStartTime;
    
    console.log(`   Batch Verification Result: ${batchVerificationResult.verified}`);
    console.log(`   Verified Transactions: ${batchVerificationResult.transactionCount}`);
    console.log(`   Batch Verification Time: ${batchVerificationTime.toFixed(2)}ms`);

    console.log('\n🎉 18/82 Principle Test Completed Successfully!');
    console.log('\n📊 Summary:');
    console.log(`   • Demonstrated 18/82 Principle with ${nodeStats?.criticalNodes || 'N/A'} critical nodes handling high-priority operations`);
    console.log(`   • Processed ${metrics.transactions.total} total transactions`);
    console.log(`   • Generated and verified ${metrics.proofs.total} zero-knowledge proofs`);
    console.log(`   • Achieved ${metrics.performance.averageTransactionTime.toFixed(2)}ms average transaction time`);
    console.log(`   • Successfully demonstrated hybrid DAG-based verification system`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  test1882Principle().catch(console.error);
}

module.exports = { test1882Principle };
