/**
 * Historical Patterns Analyzer for Depression Prediction
 *
 * This module analyzes historical patterns of economic depressions and compares
 * current data with these patterns to identify similarities and differences.
 */

class HistoricalPatterns {
  /**
   * Create a new Historical Patterns instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableGreatDepression: true,
      enable2008Crisis: true,
      enableDotComBubble: true,
      enableCovid19Recession: true,
      similarityThreshold: 0.7,
      ...options
    };
    
    // Initialize historical depression patterns
    this.historicalPatterns = this._initializeHistoricalPatterns();
    
    console.log('Historical Patterns Analyzer initialized');
  }
  
  /**
   * Compare current data with historical patterns
   * @param {Object} marketData - Current market data
   * @param {Object} economicData - Current economic data
   * @param {Object} sentimentData - Current sentiment data
   * @returns {Object} - Comparison results
   */
  compareWithHistorical(marketData, economicData, sentimentData) {
    console.log('Comparing with historical patterns');
    
    try {
      const results = {};
      
      // Compare with each historical pattern
      for (const [patternName, pattern] of Object.entries(this.historicalPatterns)) {
        // Skip disabled patterns
        if (!this._isPatternEnabled(patternName)) continue;
        
        // Calculate similarity scores
        const marketSimilarity = this._calculateMarketSimilarity(marketData, pattern.market);
        const economicSimilarity = this._calculateEconomicSimilarity(economicData, pattern.economic);
        const sentimentSimilarity = this._calculateSentimentSimilarity(sentimentData, pattern.sentiment);
        
        // Calculate overall similarity
        const overallSimilarity = (
          marketSimilarity * 0.3 + 
          economicSimilarity * 0.5 + 
          sentimentSimilarity * 0.2
        );
        
        // Identify key similarities and differences
        const keySimilarities = this._identifyKeySimilarities(
          marketData, economicData, sentimentData,
          pattern.market, pattern.economic, pattern.sentiment
        );
        
        const keyDifferences = this._identifyKeyDifferences(
          marketData, economicData, sentimentData,
          pattern.market, pattern.economic, pattern.sentiment
        );
        
        // Store results
        results[patternName] = {
          overallSimilarity,
          marketSimilarity,
          economicSimilarity,
          sentimentSimilarity,
          keySimilarities,
          keyDifferences,
          isSignificant: overallSimilarity >= this.options.similarityThreshold,
          pattern: pattern.description
        };
      }
      
      // Sort patterns by similarity
      const sortedPatterns = Object.entries(results)
        .sort(([, a], [, b]) => b.overallSimilarity - a.overallSimilarity)
        .reduce((acc, [key, value]) => {
          acc[key] = value;
          return acc;
        }, {});
      
      // Identify most similar pattern
      const mostSimilarPattern = Object.entries(sortedPatterns)[0] || [null, null];
      
      return {
        patterns: sortedPatterns,
        mostSimilarPattern: {
          name: mostSimilarPattern[0],
          ...mostSimilarPattern[1]
        },
        hasSignificantMatch: Object.values(results).some(r => r.isSignificant),
        analyzedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error comparing with historical patterns:', error);
      throw new Error(`Historical pattern comparison failed: ${error.message}`);
    }
  }
  
  /**
   * Initialize historical depression patterns
   * @returns {Object} - Historical patterns
   * @private
   */
  _initializeHistoricalPatterns() {
    return {
      greatDepression: {
        description: 'Great Depression (1929-1939)',
        market: {
          price: {
            current: 100,
            moving_average: 150,
            trend: 'decreasing'
          },
          volume: {
            current: 1000000,
            average: 800000,
            trend: 'increasing'
          },
          liquidity: {
            value: 0.3,
            trend: 'decreasing'
          },
          volatility: {
            value: 35,
            trend: 'increasing'
          },
          depth: {
            value: 0.4,
            trend: 'decreasing'
          },
          spread: {
            value: 2.0,
            trend: 'increasing'
          }
        },
        economic: {
          gdp: {
            value: 10000,
            growth: -5.0,
            trend: 'decreasing'
          },
          inflation: {
            rate: -10.0,
            core: -8.0,
            trend: 'decreasing'
          },
          unemployment: {
            rate: 25.0,
            trend: 'increasing'
          },
          interestRates: {
            fed_funds: 3.5,
            ten_year: 3.6,
            trend: 'decreasing'
          },
          pmi: {
            value: 35.0,
            trend: 'decreasing'
          },
          consumerConfidence: {
            value: 30.0,
            trend: 'decreasing'
          },
          buildingPermits: {
            value: 500000,
            growth: -15.0,
            trend: 'decreasing'
          }
        },
        sentiment: {
          retail: {
            bullishPercentage: 15,
            bearishPercentage: 85,
            trend: 'decreasing'
          },
          institutional: {
            bullishPercentage: 10,
            bearishPercentage: 90,
            netPositioning: -50,
            trend: 'decreasing'
          },
          media: {
            sentiment: 0.1,
            volume: 5000,
            trend: 'decreasing'
          },
          social: {
            sentiment: 0.2,
            volume: 10000,
            trend: 'decreasing'
          },
          futures: {
            commercialNetPositioning: -40,
            nonCommercialNetPositioning: -60,
            trend: 'decreasing'
          }
        }
      },
      crisis2008: {
        description: 'Financial Crisis (2008-2009)',
        market: {
          price: {
            current: 100,
            moving_average: 120,
            trend: 'decreasing'
          },
          volume: {
            current: 2000000,
            average: 1500000,
            trend: 'increasing'
          },
          liquidity: {
            value: 0.4,
            trend: 'decreasing'
          },
          volatility: {
            value: 40,
            trend: 'increasing'
          },
          depth: {
            value: 0.5,
            trend: 'decreasing'
          },
          spread: {
            value: 1.5,
            trend: 'increasing'
          }
        },
        economic: {
          gdp: {
            value: 15000,
            growth: -2.5,
            trend: 'decreasing'
          },
          inflation: {
            rate: 1.0,
            core: 1.5,
            trend: 'decreasing'
          },
          unemployment: {
            rate: 10.0,
            trend: 'increasing'
          },
          interestRates: {
            fed_funds: 0.25,
            ten_year: 3.0,
            trend: 'decreasing'
          },
          pmi: {
            value: 38.0,
            trend: 'decreasing'
          },
          consumerConfidence: {
            value: 40.0,
            trend: 'decreasing'
          },
          buildingPermits: {
            value: 600000,
            growth: -10.0,
            trend: 'decreasing'
          }
        },
        sentiment: {
          retail: {
            bullishPercentage: 20,
            bearishPercentage: 80,
            trend: 'decreasing'
          },
          institutional: {
            bullishPercentage: 15,
            bearishPercentage: 85,
            netPositioning: -40,
            trend: 'decreasing'
          },
          media: {
            sentiment: 0.2,
            volume: 8000,
            trend: 'decreasing'
          },
          social: {
            sentiment: 0.3,
            volume: 15000,
            trend: 'decreasing'
          },
          futures: {
            commercialNetPositioning: -30,
            nonCommercialNetPositioning: -50,
            trend: 'decreasing'
          }
        }
      },
      dotComBubble: {
        description: 'Dot-Com Bubble (2000-2002)',
        market: {
          price: {
            current: 100,
            moving_average: 130,
            trend: 'decreasing'
          },
          volume: {
            current: 1500000,
            average: 1200000,
            trend: 'increasing'
          },
          liquidity: {
            value: 0.6,
            trend: 'decreasing'
          },
          volatility: {
            value: 30,
            trend: 'increasing'
          },
          depth: {
            value: 0.6,
            trend: 'decreasing'
          },
          spread: {
            value: 1.0,
            trend: 'increasing'
          }
        },
        economic: {
          gdp: {
            value: 12000,
            growth: 0.5,
            trend: 'decreasing'
          },
          inflation: {
            rate: 2.5,
            core: 2.0,
            trend: 'stable'
          },
          unemployment: {
            rate: 6.0,
            trend: 'increasing'
          },
          interestRates: {
            fed_funds: 3.5,
            ten_year: 5.0,
            trend: 'decreasing'
          },
          pmi: {
            value: 45.0,
            trend: 'decreasing'
          },
          consumerConfidence: {
            value: 60.0,
            trend: 'decreasing'
          },
          buildingPermits: {
            value: 1500000,
            growth: -5.0,
            trend: 'stable'
          }
        },
        sentiment: {
          retail: {
            bullishPercentage: 30,
            bearishPercentage: 70,
            trend: 'decreasing'
          },
          institutional: {
            bullishPercentage: 25,
            bearishPercentage: 75,
            netPositioning: -20,
            trend: 'decreasing'
          },
          media: {
            sentiment: 0.3,
            volume: 6000,
            trend: 'decreasing'
          },
          social: {
            sentiment: 0.4,
            volume: 10000,
            trend: 'decreasing'
          },
          futures: {
            commercialNetPositioning: -15,
            nonCommercialNetPositioning: -30,
            trend: 'decreasing'
          }
        }
      },
      covid19Recession: {
        description: 'COVID-19 Recession (2020)',
        market: {
          price: {
            current: 100,
            moving_average: 110,
            trend: 'decreasing'
          },
          volume: {
            current: 3000000,
            average: 2000000,
            trend: 'increasing'
          },
          liquidity: {
            value: 0.5,
            trend: 'decreasing'
          },
          volatility: {
            value: 50,
            trend: 'increasing'
          },
          depth: {
            value: 0.5,
            trend: 'decreasing'
          },
          spread: {
            value: 1.2,
            trend: 'increasing'
          }
        },
        economic: {
          gdp: {
            value: 20000,
            growth: -3.5,
            trend: 'decreasing'
          },
          inflation: {
            rate: 1.2,
            core: 1.0,
            trend: 'decreasing'
          },
          unemployment: {
            rate: 14.8,
            trend: 'increasing'
          },
          interestRates: {
            fed_funds: 0.1,
            ten_year: 0.7,
            trend: 'decreasing'
          },
          pmi: {
            value: 36.0,
            trend: 'decreasing'
          },
          consumerConfidence: {
            value: 50.0,
            trend: 'decreasing'
          },
          buildingPermits: {
            value: 1000000,
            growth: -12.0,
            trend: 'decreasing'
          }
        },
        sentiment: {
          retail: {
            bullishPercentage: 25,
            bearishPercentage: 75,
            trend: 'decreasing'
          },
          institutional: {
            bullishPercentage: 20,
            bearishPercentage: 80,
            netPositioning: -35,
            trend: 'decreasing'
          },
          media: {
            sentiment: 0.2,
            volume: 10000,
            trend: 'decreasing'
          },
          social: {
            sentiment: 0.3,
            volume: 20000,
            trend: 'decreasing'
          },
          futures: {
            commercialNetPositioning: -25,
            nonCommercialNetPositioning: -45,
            trend: 'decreasing'
          }
        }
      }
    };
  }
  
  /**
   * Check if a pattern is enabled
   * @param {String} patternName - Pattern name
   * @returns {Boolean} - Whether the pattern is enabled
   * @private
   */
  _isPatternEnabled(patternName) {
    switch (patternName) {
      case 'greatDepression':
        return this.options.enableGreatDepression;
      case 'crisis2008':
        return this.options.enable2008Crisis;
      case 'dotComBubble':
        return this.options.enableDotComBubble;
      case 'covid19Recession':
        return this.options.enableCovid19Recession;
      default:
        return true;
    }
  }
  
  /**
   * Calculate market similarity
   * @param {Object} currentMarket - Current market data
   * @param {Object} historicalMarket - Historical market data
   * @returns {Number} - Similarity score (0-1)
   * @private
   */
  _calculateMarketSimilarity(currentMarket, historicalMarket) {
    // In a real implementation, this would use sophisticated analysis
    // For now, use a simplified approach
    
    let similarityScore = 0;
    let factorCount = 0;
    
    // Compare price trend
    if (currentMarket.price && historicalMarket.price) {
      const priceTrendSimilarity = currentMarket.price.trend === historicalMarket.price.trend ? 1 : 0;
      similarityScore += priceTrendSimilarity;
      factorCount++;
    }
    
    // Compare volatility
    if (currentMarket.volatility && historicalMarket.volatility) {
      const volatilitySimilarity = 1 - Math.min(1, Math.abs(currentMarket.volatility.value - historicalMarket.volatility.value) / 30);
      similarityScore += volatilitySimilarity;
      factorCount++;
    }
    
    // Compare liquidity
    if (currentMarket.liquidity && historicalMarket.liquidity) {
      const liquiditySimilarity = 1 - Math.min(1, Math.abs(currentMarket.liquidity.value - historicalMarket.liquidity.value));
      similarityScore += liquiditySimilarity;
      factorCount++;
    }
    
    // Compare spread
    if (currentMarket.spread && historicalMarket.spread) {
      const spreadSimilarity = 1 - Math.min(1, Math.abs(currentMarket.spread.value - historicalMarket.spread.value) / 2);
      similarityScore += spreadSimilarity;
      factorCount++;
    }
    
    return factorCount > 0 ? similarityScore / factorCount : 0;
  }
  
  /**
   * Calculate economic similarity
   * @param {Object} currentEconomic - Current economic data
   * @param {Object} historicalEconomic - Historical economic data
   * @returns {Number} - Similarity score (0-1)
   * @private
   */
  _calculateEconomicSimilarity(currentEconomic, historicalEconomic) {
    // In a real implementation, this would use sophisticated analysis
    // For now, use a simplified approach
    
    let similarityScore = 0;
    let factorCount = 0;
    
    // Compare GDP growth
    if (currentEconomic.gdp && historicalEconomic.gdp) {
      const gdpGrowthSimilarity = 1 - Math.min(1, Math.abs(currentEconomic.gdp.growth - historicalEconomic.gdp.growth) / 10);
      similarityScore += gdpGrowthSimilarity;
      factorCount++;
    }
    
    // Compare unemployment
    if (currentEconomic.unemployment && historicalEconomic.unemployment) {
      const unemploymentSimilarity = 1 - Math.min(1, Math.abs(currentEconomic.unemployment.rate - historicalEconomic.unemployment.rate) / 20);
      similarityScore += unemploymentSimilarity;
      factorCount++;
    }
    
    // Compare inflation
    if (currentEconomic.inflation && historicalEconomic.inflation) {
      const inflationSimilarity = 1 - Math.min(1, Math.abs(currentEconomic.inflation.rate - historicalEconomic.inflation.rate) / 10);
      similarityScore += inflationSimilarity;
      factorCount++;
    }
    
    // Compare interest rates
    if (currentEconomic.interestRates && historicalEconomic.interestRates) {
      const interestRateSimilarity = 1 - Math.min(1, Math.abs(currentEconomic.interestRates.fed_funds - historicalEconomic.interestRates.fed_funds) / 5);
      similarityScore += interestRateSimilarity;
      factorCount++;
    }
    
    // Compare PMI
    if (currentEconomic.pmi && historicalEconomic.pmi) {
      const pmiSimilarity = 1 - Math.min(1, Math.abs(currentEconomic.pmi.value - historicalEconomic.pmi.value) / 50);
      similarityScore += pmiSimilarity;
      factorCount++;
    }
    
    return factorCount > 0 ? similarityScore / factorCount : 0;
  }
  
  /**
   * Calculate sentiment similarity
   * @param {Object} currentSentiment - Current sentiment data
   * @param {Object} historicalSentiment - Historical sentiment data
   * @returns {Number} - Similarity score (0-1)
   * @private
   */
  _calculateSentimentSimilarity(currentSentiment, historicalSentiment) {
    // In a real implementation, this would use sophisticated analysis
    // For now, use a simplified approach
    
    let similarityScore = 0;
    let factorCount = 0;
    
    // Compare retail sentiment
    if (currentSentiment.retail && historicalSentiment.retail) {
      const retailSimilarity = 1 - Math.min(1, Math.abs(currentSentiment.retail.bullishPercentage - historicalSentiment.retail.bullishPercentage) / 100);
      similarityScore += retailSimilarity;
      factorCount++;
    }
    
    // Compare institutional sentiment
    if (currentSentiment.institutional && historicalSentiment.institutional) {
      const institutionalSimilarity = 1 - Math.min(1, Math.abs(currentSentiment.institutional.netPositioning - historicalSentiment.institutional.netPositioning) / 100);
      similarityScore += institutionalSimilarity;
      factorCount++;
    }
    
    // Compare media sentiment
    if (currentSentiment.media && historicalSentiment.media) {
      const mediaSimilarity = 1 - Math.min(1, Math.abs(currentSentiment.media.sentiment - historicalSentiment.media.sentiment));
      similarityScore += mediaSimilarity;
      factorCount++;
    }
    
    return factorCount > 0 ? similarityScore / factorCount : 0;
  }
  
  /**
   * Identify key similarities between current and historical data
   * @param {Object} currentMarket - Current market data
   * @param {Object} currentEconomic - Current economic data
   * @param {Object} currentSentiment - Current sentiment data
   * @param {Object} historicalMarket - Historical market data
   * @param {Object} historicalEconomic - Historical economic data
   * @param {Object} historicalSentiment - Historical sentiment data
   * @returns {Array} - Key similarities
   * @private
   */
  _identifyKeySimilarities(
    currentMarket, currentEconomic, currentSentiment,
    historicalMarket, historicalEconomic, historicalSentiment
  ) {
    const similarities = [];
    
    // Check market similarities
    if (currentMarket.volatility && historicalMarket.volatility &&
        Math.abs(currentMarket.volatility.value - historicalMarket.volatility.value) < 10) {
      similarities.push({
        factor: 'Market Volatility',
        current: currentMarket.volatility.value,
        historical: historicalMarket.volatility.value,
        category: 'market'
      });
    }
    
    // Check economic similarities
    if (currentEconomic.gdp && historicalEconomic.gdp &&
        Math.abs(currentEconomic.gdp.growth - historicalEconomic.gdp.growth) < 2) {
      similarities.push({
        factor: 'GDP Growth',
        current: currentEconomic.gdp.growth,
        historical: historicalEconomic.gdp.growth,
        category: 'economic'
      });
    }
    
    if (currentEconomic.unemployment && historicalEconomic.unemployment &&
        Math.abs(currentEconomic.unemployment.rate - historicalEconomic.unemployment.rate) < 5) {
      similarities.push({
        factor: 'Unemployment Rate',
        current: currentEconomic.unemployment.rate,
        historical: historicalEconomic.unemployment.rate,
        category: 'economic'
      });
    }
    
    // Check sentiment similarities
    if (currentSentiment.retail && historicalSentiment.retail &&
        Math.abs(currentSentiment.retail.bullishPercentage - historicalSentiment.retail.bullishPercentage) < 20) {
      similarities.push({
        factor: 'Retail Sentiment',
        current: currentSentiment.retail.bullishPercentage,
        historical: historicalSentiment.retail.bullishPercentage,
        category: 'sentiment'
      });
    }
    
    return similarities;
  }
  
  /**
   * Identify key differences between current and historical data
   * @param {Object} currentMarket - Current market data
   * @param {Object} currentEconomic - Current economic data
   * @param {Object} currentSentiment - Current sentiment data
   * @param {Object} historicalMarket - Historical market data
   * @param {Object} historicalEconomic - Historical economic data
   * @param {Object} historicalSentiment - Historical sentiment data
   * @returns {Array} - Key differences
   * @private
   */
  _identifyKeyDifferences(
    currentMarket, currentEconomic, currentSentiment,
    historicalMarket, historicalEconomic, historicalSentiment
  ) {
    const differences = [];
    
    // Check market differences
    if (currentMarket.liquidity && historicalMarket.liquidity &&
        Math.abs(currentMarket.liquidity.value - historicalMarket.liquidity.value) > 0.3) {
      differences.push({
        factor: 'Market Liquidity',
        current: currentMarket.liquidity.value,
        historical: historicalMarket.liquidity.value,
        category: 'market'
      });
    }
    
    // Check economic differences
    if (currentEconomic.inflation && historicalEconomic.inflation &&
        Math.abs(currentEconomic.inflation.rate - historicalEconomic.inflation.rate) > 5) {
      differences.push({
        factor: 'Inflation Rate',
        current: currentEconomic.inflation.rate,
        historical: historicalEconomic.inflation.rate,
        category: 'economic'
      });
    }
    
    if (currentEconomic.interestRates && historicalEconomic.interestRates &&
        Math.abs(currentEconomic.interestRates.fed_funds - historicalEconomic.interestRates.fed_funds) > 2) {
      differences.push({
        factor: 'Interest Rates',
        current: currentEconomic.interestRates.fed_funds,
        historical: historicalEconomic.interestRates.fed_funds,
        category: 'economic'
      });
    }
    
    // Check sentiment differences
    if (currentSentiment.institutional && historicalSentiment.institutional &&
        Math.abs(currentSentiment.institutional.netPositioning - historicalSentiment.institutional.netPositioning) > 30) {
      differences.push({
        factor: 'Institutional Positioning',
        current: currentSentiment.institutional.netPositioning,
        historical: historicalSentiment.institutional.netPositioning,
        category: 'sentiment'
      });
    }
    
    return differences;
  }
}

module.exports = HistoricalPatterns;
