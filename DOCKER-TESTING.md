# Testing NovaFuse API Superstore in Docker

This guide explains how to test the NovaFuse API Superstore using the existing Docker implementation.

## Prerequisites

- Docker installed and running

## Starting the Environment

1. Start the Docker containers:
   ```
   start.bat
   ```

   This will start the following services:
   - Marketplace UI (port 3000)
   - API Documentation (port 8080)
   - Governance API (port 3001)
   - Security API (port 3002)
   - APIs API (port 3003)
   - Kong API Gateway (ports 8000-8001)
   - Kong Admin API (ports 8443-8444)
   - PostgreSQL Database (port 5432)

2. Wait for all services to start (this may take a few minutes)

3. Set up the Kong API Gateway:
   ```
   setup-kong.ps1
   ```

## Accessing the Components

- Marketplace UI: http://localhost:3000
- API Documentation: http://localhost:8080
- Kong API Gateway: http://localhost:8000
- Kong Admin API: http://localhost:8001
- Governance API: http://localhost:3001
- Security API: http://localhost:3002
- APIs API: http://localhost:3003

## Testing the API

You can test the API directly using the following endpoints:

- Governance API: http://localhost:3001/governance/board/meetings
- Security API: http://localhost:3002/security/vulnerabilities
- APIs API: http://localhost:3003/apis/catalog

Note: There may be some issues with the Kong API Gateway name resolution. If you encounter errors when testing through Kong, try accessing the API services directly.

## Stopping the Environment

When you are done testing, stop the Docker containers:
```
stop.bat
```

## Notes

- The Docker implementation provides a complete testing environment for the NovaFuse API Superstore.
- All data is stored in Docker volumes and will persist between restarts.
- You can use the Kong Admin API to manage the API Gateway configuration.

