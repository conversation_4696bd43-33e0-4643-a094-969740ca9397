/**
 * PerformanceMonitorPanel Component
 * 
 * A component for visualizing performance metrics.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import performanceMonitor from '../performance/PerformanceMonitor';
import { TabPanel } from './index';

/**
 * PerformanceMonitorPanel component
 * 
 * @param {Object} props - Component props
 * @param {boolean} [props.startMonitoringOnMount=false] - Whether to start monitoring on mount
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} PerformanceMonitorPanel component
 */
const PerformanceMonitorPanel = ({
  startMonitoringOnMount = false,
  className = '',
  style = {}
}) => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [metrics, setMetrics] = useState({
    renders: {},
    operations: {},
    resources: {},
    memory: []
  });
  const [activeTab, setActiveTab] = useState('overview');
  const [updateInterval, setUpdateInterval] = useState(1000);
  const [autoUpdate, setAutoUpdate] = useState(true);
  
  // Start/stop monitoring on mount/unmount
  useEffect(() => {
    if (startMonitoringOnMount) {
      handleStartMonitoring();
    }
    
    return () => {
      performanceMonitor.stopMonitoring();
    };
  }, [startMonitoringOnMount]);
  
  // Update metrics periodically
  useEffect(() => {
    if (!autoUpdate) return;
    
    const intervalId = setInterval(() => {
      setMetrics(performanceMonitor.getMetrics());
    }, updateInterval);
    
    return () => {
      clearInterval(intervalId);
    };
  }, [autoUpdate, updateInterval]);
  
  // Start monitoring
  const handleStartMonitoring = () => {
    performanceMonitor.startMonitoring();
    setIsMonitoring(true);
  };
  
  // Stop monitoring
  const handleStopMonitoring = () => {
    performanceMonitor.stopMonitoring();
    setIsMonitoring(false);
  };
  
  // Clear metrics
  const handleClearMetrics = () => {
    performanceMonitor.clearMetrics();
    setMetrics(performanceMonitor.getMetrics());
  };
  
  // Toggle monitoring
  const toggleMonitoring = () => {
    if (isMonitoring) {
      handleStopMonitoring();
    } else {
      handleStartMonitoring();
    }
  };
  
  // Toggle auto update
  const toggleAutoUpdate = () => {
    setAutoUpdate(!autoUpdate);
  };
  
  // Update metrics now
  const updateMetricsNow = () => {
    setMetrics(performanceMonitor.getMetrics());
  };
  
  // Format bytes
  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };
  
  // Format time
  const formatTime = (time) => {
    if (time < 1) {
      return `${(time * 1000).toFixed(2)} μs`;
    } else if (time < 1000) {
      return `${time.toFixed(2)} ms`;
    } else {
      return `${(time / 1000).toFixed(2)} s`;
    }
  };
  
  // Get performance report
  const report = performanceMonitor.getPerformanceReport();
  
  // Define tabs
  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      content: (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Render metrics */}
            <div className="bg-surface p-4 rounded-md border border-divider">
              <h3 className="text-lg font-medium mb-2 text-textPrimary">Render Performance</h3>
              <div className="space-y-2">
                <p className="text-sm text-textSecondary">Total renders: {report.totalRenders}</p>
                
                {report.slowestComponents.length > 0 ? (
                  <div>
                    <h4 className="text-sm font-medium text-textSecondary mt-4 mb-2">Slowest Components</h4>
                    <div className="max-h-40 overflow-y-auto">
                      <table className="min-w-full divide-y divide-divider">
                        <thead className="bg-surface">
                          <tr>
                            <th className="px-2 py-1 text-left text-xs font-medium text-textSecondary">Component</th>
                            <th className="px-2 py-1 text-left text-xs font-medium text-textSecondary">Avg Time</th>
                            <th className="px-2 py-1 text-left text-xs font-medium text-textSecondary">Max Time</th>
                            <th className="px-2 py-1 text-left text-xs font-medium text-textSecondary">Count</th>
                          </tr>
                        </thead>
                        <tbody className="bg-background divide-y divide-divider">
                          {report.slowestComponents.map((component) => (
                            <tr key={component.name}>
                              <td className="px-2 py-1 text-xs text-textPrimary">{component.name}</td>
                              <td className="px-2 py-1 text-xs text-textPrimary">{formatTime(component.avgTime)}</td>
                              <td className="px-2 py-1 text-xs text-textPrimary">{formatTime(component.maxTime)}</td>
                              <td className="px-2 py-1 text-xs text-textPrimary">{component.count}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-textSecondary">No render metrics available</p>
                )}
              </div>
            </div>
            
            {/* Operation metrics */}
            <div className="bg-surface p-4 rounded-md border border-divider">
              <h3 className="text-lg font-medium mb-2 text-textPrimary">Operation Performance</h3>
              
              {report.slowestOperations.length > 0 ? (
                <div>
                  <h4 className="text-sm font-medium text-textSecondary mt-4 mb-2">Slowest Operations</h4>
                  <div className="max-h-40 overflow-y-auto">
                    <table className="min-w-full divide-y divide-divider">
                      <thead className="bg-surface">
                        <tr>
                          <th className="px-2 py-1 text-left text-xs font-medium text-textSecondary">Operation</th>
                          <th className="px-2 py-1 text-left text-xs font-medium text-textSecondary">Avg Time</th>
                          <th className="px-2 py-1 text-left text-xs font-medium text-textSecondary">Max Time</th>
                          <th className="px-2 py-1 text-left text-xs font-medium text-textSecondary">Count</th>
                        </tr>
                      </thead>
                      <tbody className="bg-background divide-y divide-divider">
                        {report.slowestOperations.map((operation) => (
                          <tr key={operation.name}>
                            <td className="px-2 py-1 text-xs text-textPrimary">{operation.name}</td>
                            <td className="px-2 py-1 text-xs text-textPrimary">{formatTime(operation.avgTime)}</td>
                            <td className="px-2 py-1 text-xs text-textPrimary">{formatTime(operation.maxTime)}</td>
                            <td className="px-2 py-1 text-xs text-textPrimary">{operation.count}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-textSecondary">No operation metrics available</p>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Resource metrics */}
            <div className="bg-surface p-4 rounded-md border border-divider">
              <h3 className="text-lg font-medium mb-2 text-textPrimary">Resource Usage</h3>
              
              {report.largestResources.length > 0 ? (
                <div>
                  <h4 className="text-sm font-medium text-textSecondary mt-4 mb-2">Largest Resources</h4>
                  <div className="max-h-40 overflow-y-auto">
                    <table className="min-w-full divide-y divide-divider">
                      <thead className="bg-surface">
                        <tr>
                          <th className="px-2 py-1 text-left text-xs font-medium text-textSecondary">Type</th>
                          <th className="px-2 py-1 text-left text-xs font-medium text-textSecondary">Size</th>
                          <th className="px-2 py-1 text-left text-xs font-medium text-textSecondary">Time</th>
                        </tr>
                      </thead>
                      <tbody className="bg-background divide-y divide-divider">
                        {report.largestResources.map((resource, index) => (
                          <tr key={index}>
                            <td className="px-2 py-1 text-xs text-textPrimary">{resource.type}</td>
                            <td className="px-2 py-1 text-xs text-textPrimary">{formatBytes(resource.size)}</td>
                            <td className="px-2 py-1 text-xs text-textPrimary">{formatTime(resource.time)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-textSecondary">No resource metrics available</p>
              )}
            </div>
            
            {/* Memory metrics */}
            <div className="bg-surface p-4 rounded-md border border-divider">
              <h3 className="text-lg font-medium mb-2 text-textPrimary">Memory Usage</h3>
              
              {report.memoryUsage ? (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-textSecondary">Used:</span>
                    <span className="text-textPrimary">{formatBytes(report.memoryUsage.used)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-textSecondary">Total:</span>
                    <span className="text-textPrimary">{formatBytes(report.memoryUsage.total)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-textSecondary">Limit:</span>
                    <span className="text-textPrimary">{formatBytes(report.memoryUsage.limit)}</span>
                  </div>
                  
                  <div className="mt-2">
                    <div className="text-xs text-textSecondary mb-1">Memory Usage</div>
                    <div className="w-full bg-background rounded-full h-2.5">
                      <div
                        className="bg-primary h-2.5 rounded-full"
                        style={{ width: `${report.memoryUsage.usedPercentage}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-right text-textSecondary mt-1">
                      {report.memoryUsage.usedPercentage.toFixed(1)}%
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-textSecondary">No memory metrics available</p>
              )}
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'renders',
      label: 'Renders',
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-medium mb-2 text-textPrimary">Component Renders</h3>
          
          {Object.keys(metrics.renders).length > 0 ? (
            <div className="max-h-96 overflow-y-auto">
              <table className="min-w-full divide-y divide-divider">
                <thead className="bg-surface sticky top-0">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Component</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Count</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Avg Time</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Min Time</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Max Time</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Total Time</th>
                  </tr>
                </thead>
                <tbody className="bg-background divide-y divide-divider">
                  {Object.entries(metrics.renders)
                    .sort((a, b) => b[1].avgTime - a[1].avgTime)
                    .map(([componentName, metric]) => (
                      <tr key={componentName}>
                        <td className="px-4 py-2 text-sm text-textPrimary">{componentName}</td>
                        <td className="px-4 py-2 text-sm text-textPrimary">{metric.count}</td>
                        <td className="px-4 py-2 text-sm text-textPrimary">{formatTime(metric.avgTime)}</td>
                        <td className="px-4 py-2 text-sm text-textPrimary">{formatTime(metric.minTime)}</td>
                        <td className="px-4 py-2 text-sm text-textPrimary">{formatTime(metric.maxTime)}</td>
                        <td className="px-4 py-2 text-sm text-textPrimary">{formatTime(metric.totalTime)}</td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-sm text-textSecondary">No render metrics available</p>
          )}
        </div>
      )
    },
    {
      id: 'operations',
      label: 'Operations',
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-medium mb-2 text-textPrimary">Operations</h3>
          
          {Object.keys(metrics.operations).length > 0 ? (
            <div className="max-h-96 overflow-y-auto">
              <table className="min-w-full divide-y divide-divider">
                <thead className="bg-surface sticky top-0">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Operation</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Count</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Avg Time</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Min Time</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Max Time</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Total Time</th>
                  </tr>
                </thead>
                <tbody className="bg-background divide-y divide-divider">
                  {Object.entries(metrics.operations)
                    .sort((a, b) => b[1].avgTime - a[1].avgTime)
                    .map(([operationName, metric]) => (
                      <tr key={operationName}>
                        <td className="px-4 py-2 text-sm text-textPrimary">{operationName}</td>
                        <td className="px-4 py-2 text-sm text-textPrimary">{metric.count}</td>
                        <td className="px-4 py-2 text-sm text-textPrimary">{formatTime(metric.avgTime)}</td>
                        <td className="px-4 py-2 text-sm text-textPrimary">{formatTime(metric.minTime)}</td>
                        <td className="px-4 py-2 text-sm text-textPrimary">{formatTime(metric.maxTime)}</td>
                        <td className="px-4 py-2 text-sm text-textPrimary">{formatTime(metric.totalTime)}</td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-sm text-textSecondary">No operation metrics available</p>
          )}
        </div>
      )
    },
    {
      id: 'resources',
      label: 'Resources',
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-medium mb-2 text-textPrimary">Resources</h3>
          
          {Object.keys(metrics.resources).length > 0 ? (
            <div>
              {Object.entries(metrics.resources).map(([type, metric]) => (
                <div key={type} className="mb-6">
                  <h4 className="text-md font-medium mb-2 text-textPrimary">{type}</h4>
                  <div className="text-sm text-textSecondary mb-2">
                    Count: {metric.count} | Total Size: {formatBytes(metric.totalSize)} | Total Time: {formatTime(metric.totalTime)}
                  </div>
                  
                  <div className="max-h-60 overflow-y-auto">
                    <table className="min-w-full divide-y divide-divider">
                      <thead className="bg-surface sticky top-0">
                        <tr>
                          <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">URL</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Size</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Time</th>
                        </tr>
                      </thead>
                      <tbody className="bg-background divide-y divide-divider">
                        {metric.items
                          .sort((a, b) => b.size - a.size)
                          .map((item, index) => (
                            <tr key={index}>
                              <td className="px-4 py-2 text-xs text-textPrimary truncate max-w-xs">{item.url}</td>
                              <td className="px-4 py-2 text-xs text-textPrimary">{formatBytes(item.size)}</td>
                              <td className="px-4 py-2 text-xs text-textPrimary">{formatTime(item.time)}</td>
                            </tr>
                          ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-textSecondary">No resource metrics available</p>
          )}
        </div>
      )
    }
  ];
  
  return (
    <div
      className={`bg-background border border-divider rounded-lg shadow-lg ${className}`}
      style={style}
      data-testid="performance-monitor-panel"
    >
      {/* Header */}
      <div className="px-6 py-4 border-b border-divider flex justify-between items-center">
        <h2 className="text-xl font-semibold text-textPrimary">Performance Monitor</h2>
        
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <label htmlFor="update-interval" className="text-sm text-textSecondary">Update:</label>
            <select
              id="update-interval"
              className="text-sm px-2 py-1 bg-surface border border-divider rounded text-textPrimary"
              value={updateInterval}
              onChange={(e) => setUpdateInterval(Number(e.target.value))}
              disabled={!autoUpdate}
            >
              <option value="500">0.5s</option>
              <option value="1000">1s</option>
              <option value="2000">2s</option>
              <option value="5000">5s</option>
            </select>
          </div>
          
          <button
            className={`text-sm px-3 py-1 rounded border ${
              autoUpdate
                ? 'bg-primary text-primaryContrast border-primary'
                : 'bg-surface text-textPrimary border-divider'
            }`}
            onClick={toggleAutoUpdate}
          >
            {autoUpdate ? 'Auto' : 'Manual'}
          </button>
          
          {!autoUpdate && (
            <button
              className="text-sm px-3 py-1 rounded bg-surface text-textPrimary border border-divider"
              onClick={updateMetricsNow}
            >
              Update Now
            </button>
          )}
          
          <button
            className={`text-sm px-3 py-1 rounded border ${
              isMonitoring
                ? 'bg-error text-errorContrast border-error'
                : 'bg-success text-successContrast border-success'
            }`}
            onClick={toggleMonitoring}
          >
            {isMonitoring ? 'Stop' : 'Start'}
          </button>
          
          <button
            className="text-sm px-3 py-1 rounded bg-surface text-textPrimary border border-divider"
            onClick={handleClearMetrics}
          >
            Clear
          </button>
        </div>
      </div>
      
      {/* Content */}
      <div className="p-6">
        <TabPanel
          tabs={tabs}
          defaultTab="overview"
          variant="pills"
          onTabChange={setActiveTab}
        />
      </div>
    </div>
  );
};

PerformanceMonitorPanel.propTypes = {
  startMonitoringOnMount: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object
};

export default PerformanceMonitorPanel;
