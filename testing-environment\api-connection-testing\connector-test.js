/**
 * Universal API Connector Test
 * 
 * This test simulates how the Universal API Connector would interact with an API.
 */

const axios = require('axios');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Test connector definition
const connector = {
  metadata: {
    name: 'Test API Connector',
    version: '1.0.0',
    description: 'Test connector for API connection testing'
  },
  authentication: {
    type: 'API_KEY',
    fields: {
      apiKey: {
        type: 'string',
        description: 'API Key',
        required: true
      },
      headerName: {
        type: 'string',
        description: 'Header name for the API key',
        default: 'X-API-Key'
      }
    }
  },
  configuration: {
    baseUrl: 'http://localhost:3005',
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 5000
  },
  endpoints: [
    {
      id: 'getResource',
      name: 'Get Resource',
      path: '/api-key/resource',
      method: 'GET'
    }
  ],
  mappings: [
    {
      sourceEndpoint: 'getResource',
      targetEntity: 'Resource',
      transformations: [
        {
          source: '$.data.id',
          target: 'id'
        },
        {
          source: '$.data.name',
          target: 'name'
        }
      ]
    }
  ]
};

// Test credentials
const credentials = {
  apiKey: 'valid-api-key',
  headerName: 'X-API-Key'
};

/**
 * Execute a connector endpoint
 * 
 * @param {Object} connector - The connector definition
 * @param {string} endpointId - The endpoint ID to execute
 * @param {Object} credentials - The credentials to use
 * @param {Object} parameters - The parameters to pass to the endpoint
 * @returns {Promise<Object>} - The response data
 */
async function executeConnectorEndpoint(connector, endpointId, credentials, parameters = {}) {
  // Find the endpoint
  const endpoint = connector.endpoints.find(e => e.id === endpointId);
  if (!endpoint) {
    throw new Error(`Endpoint ${endpointId} not found`);
  }
  
  // Find the mapping
  const mapping = connector.mappings.find(m => m.sourceEndpoint === endpointId);
  
  // Build the request URL
  const url = `${connector.configuration.baseUrl}${endpoint.path}`;
  
  // Build the request headers
  const headers = {
    ...connector.configuration.headers
  };
  
  // Add authentication headers
  if (connector.authentication.type === 'API_KEY') {
    headers[credentials.headerName] = credentials.apiKey;
  }
  
  // Make the request
  console.log(`${colors.yellow}Making request to ${endpoint.method} ${url}${colors.reset}`);
  
  try {
    let response;
    
    switch (endpoint.method) {
      case 'GET':
        response = await axios.get(url, { headers, params: parameters.query });
        break;
      case 'POST':
        response = await axios.post(url, parameters.body, { headers });
        break;
      case 'PUT':
        response = await axios.put(url, parameters.body, { headers });
        break;
      case 'DELETE':
        response = await axios.delete(url, { headers });
        break;
      default:
        throw new Error(`Unsupported method: ${endpoint.method}`);
    }
    
    console.log(`${colors.green}✓ Request successful! Status: ${response.status}${colors.reset}`);
    
    // Transform the response if a mapping exists
    if (mapping) {
      console.log(`${colors.yellow}Transforming response using mapping...${colors.reset}`);
      
      const transformedData = {};
      
      for (const transformation of mapping.transformations) {
        // Simple path-based transformation (in a real implementation, this would use JSONPath)
        const pathParts = transformation.source.replace('$.', '').split('.');
        let value = response.data;
        
        for (const part of pathParts) {
          value = value[part];
        }
        
        transformedData[transformation.target] = value;
      }
      
      console.log(`${colors.green}✓ Response transformed successfully!${colors.reset}`);
      return transformedData;
    }
    
    return response.data;
  } catch (error) {
    console.log(`${colors.red}✗ Request failed! Error: ${error.message}${colors.reset}`);
    if (error.response) {
      console.log(`${colors.red}Status: ${error.response.status}${colors.reset}`);
      console.log(`${colors.red}Response: ${JSON.stringify(error.response.data, null, 2)}${colors.reset}`);
    }
    throw error;
  }
}

/**
 * Run the connector test
 */
async function runConnectorTest() {
  console.log(`${colors.bright}${colors.magenta}=== Universal API Connector Test ===${colors.reset}\n`);
  
  try {
    // Test with valid credentials
    console.log(`${colors.bright}${colors.blue}Testing with valid credentials...${colors.reset}`);
    const validResult = await executeConnectorEndpoint(connector, 'getResource', credentials);
    console.log(`${colors.cyan}Transformed result: ${JSON.stringify(validResult, null, 2)}${colors.reset}`);
    
    // Verify the result
    if (validResult.id === 'resource-1' && validResult.name === 'Test Resource') {
      console.log(`${colors.green}✓ Test passed! The connector successfully retrieved and transformed the data.${colors.reset}`);
    } else {
      console.log(`${colors.red}✗ Test failed! The transformed data does not match the expected values.${colors.reset}`);
    }
    
    // Test with invalid credentials
    console.log(`\n${colors.bright}${colors.blue}Testing with invalid credentials...${colors.reset}`);
    try {
      await executeConnectorEndpoint(connector, 'getResource', { apiKey: 'invalid-api-key', headerName: 'X-API-Key' });
      console.log(`${colors.red}✗ Test failed! Expected an error but got success.${colors.reset}`);
    } catch (error) {
      if (error.response && error.response.status === 403) {
        console.log(`${colors.green}✓ Test passed! The connector correctly handled invalid credentials.${colors.reset}`);
      } else {
        console.log(`${colors.red}✗ Test failed! Expected a 403 error but got a different error.${colors.reset}`);
      }
    }
    
    console.log(`\n${colors.bright}${colors.green}All tests completed!${colors.reset}`);
  } catch (error) {
    console.log(`${colors.bright}${colors.red}Test failed with error: ${error.message}${colors.reset}`);
  }
}

// Run the test
runConnectorTest();
