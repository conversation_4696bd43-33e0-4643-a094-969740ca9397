/**
 * ProgressiveDisclosureComponent.js
 * 
 * This component demonstrates how to use the NovaVisionIntegration
 * to render the progressive disclosure interface using NovaVision.
 */

import React, { useEffect, useState } from 'react';
import NovaVisionComponents from '../NovaVisionComponents';
import NovaVisionIntegration from './NovaVisionIntegration';
import NovaVisionRenderer from '@nova-ui/ui-components/NovaVisionRenderer';

/**
 * Progressive Disclosure Component
 * @param {Object} props - Component props
 * @param {String} props.profileId - The profile ID
 * @param {String} props.accessLevel - The access level (basic, standard, full)
 * @param {Object} props.context - The emergency context
 * @returns {React.ReactNode} - The rendered component
 */
function ProgressiveDisclosureComponent({ profileId, accessLevel = 'standard', context = {} }) {
  const [uiSchema, setUiSchema] = useState(null);
  const [profileData, setProfileData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    try {
      // Initialize NovaVisionComponents
      const novaVisionComponents = new NovaVisionComponents({
        baseUrl: '/novadna',
        theme: 'emergency'
      });
      
      // Initialize NovaVisionIntegration
      const novaVisionIntegration = new NovaVisionIntegration({
        apiBaseUrl: '/api',
        novaVisionComponents
      });
      
      // Get the profile view UI schema
      const schema = novaVisionIntegration.getProfileViewUI(accessLevel);
      
      // Set the UI schema
      setUiSchema(schema);
      
      // Fetch profile data
      fetchProfileData();
    } catch (err) {
      console.error('Failed to initialize NovaVision integration:', err);
      setError(err.message);
      setLoading(false);
    }
  }, [profileId, accessLevel]);
  
  /**
   * Fetch profile data from the API
   */
  const fetchProfileData = async () => {
    try {
      setLoading(true);
      
      // Prepare context data for API request
      const contextData = {
        formFactorId: context.formFactorId,
        accessCode: context.accessCode,
        context: {
          emergencyType: context.emergencyType || 'MEDICAL',
          emergencySeverity: context.emergencySeverity || 'MODERATE',
          responderType: context.responderType || 'PARAMEDIC',
          locationType: context.locationType || 'AMBULANCE'
        }
      };
      
      // Make API call to get profile data
      const response = await fetch('/api/access/emergency', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(contextData)
      });
      
      const data = await response.json();
      
      if (data.status === 'success') {
        setProfileData(data.data.profile);
      } else {
        setError(data.error || 'Failed to fetch profile data');
      }
      
      setLoading(false);
    } catch (err) {
      console.error('Error fetching profile data:', err);
      setError(err.message);
      setLoading(false);
    }
  };
  
  // Handle loading state
  if (loading) {
    return (
      <div className="p-4 bg-gray-100 rounded-lg">
        <div className="text-center">
          <h2 className="text-xl font-bold text-gray-800">Loading Medical Profile...</h2>
          <p className="mt-2 text-gray-600">Please wait while we retrieve the emergency medical information.</p>
        </div>
      </div>
    );
  }
  
  // Handle error state
  if (error) {
    return (
      <div className="p-4 bg-red-50 rounded-lg">
        <div className="text-center">
          <h2 className="text-xl font-bold text-red-800">Error</h2>
          <p className="mt-2 text-red-600">{error}</p>
          <button
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            onClick={fetchProfileData}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }
  
  // Render the NovaVision UI with profile data
  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Access level indicator */}
      <div className={`p-2 text-center text-white font-semibold ${getAccessLevelColor()}`}>
        {getAccessLevelText()} Access Level
      </div>
      
      {/* Emergency context indicator */}
      {context.emergencyType && (
        <div className="p-2 bg-yellow-50 border-b border-yellow-200">
          <div className="flex items-center justify-center">
            <svg className="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span className="text-sm font-medium text-yellow-800">
              {context.emergencyType} Emergency - {context.emergencySeverity} Severity
            </span>
          </div>
        </div>
      )}
      
      {/* NovaVision renderer */}
      <NovaVisionRenderer
        schema={uiSchema}
        data={profileData}
        onAction={handleAction}
        onError={handleError}
      />
      
      {/* Highlighted fields indicator */}
      {profileData && profileData._highlighted && profileData._highlighted.length > 0 && (
        <div className="p-3 bg-blue-50 border-t border-blue-200">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <span className="text-sm font-medium text-blue-800">
              Important information highlighted based on emergency context
            </span>
          </div>
        </div>
      )}
    </div>
  );
  
  /**
   * Get the color class for the access level indicator
   * @returns {String} - The color class
   */
  function getAccessLevelColor() {
    switch (accessLevel) {
      case 'basic':
        return 'bg-green-600';
      case 'standard':
        return 'bg-blue-600';
      case 'full':
        return 'bg-purple-600';
      default:
        return 'bg-gray-600';
    }
  }
  
  /**
   * Get the text for the access level indicator
   * @returns {String} - The access level text
   */
  function getAccessLevelText() {
    switch (accessLevel) {
      case 'basic':
        return 'Basic';
      case 'standard':
        return 'Standard';
      case 'full':
        return 'Full';
      default:
        return 'Unknown';
    }
  }
  
  /**
   * Handle NovaVision actions
   * @param {String} actionId - The action ID
   * @param {Object} actionData - The action data
   */
  function handleAction(actionId, actionData) {
    console.log('Action:', actionId, actionData);
    
    // Handle specific actions
    switch (actionId) {
      case 'print':
        window.print();
        break;
      
      case 'share':
        // This would be implemented using your UI framework
        console.log('Share profile:', profileId);
        break;
      
      case 'notify':
        // Make API call to notify emergency contacts
        fetch(`/api/profiles/${profileId}/notify-contacts`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })
          .then(response => response.json())
          .then(data => {
            if (data.status === 'success') {
              alert('Emergency contacts have been notified.');
            } else {
              handleError(data.error);
            }
          })
          .catch(err => {
            handleError(err.message);
          });
        break;
      
      default:
        // Handle other actions
        break;
    }
  }
  
  /**
   * Handle errors
   * @param {String} errorMessage - The error message
   */
  function handleError(errorMessage) {
    console.error('Error:', errorMessage);
    
    // Show error message
    setError(errorMessage);
  }
}

export default ProgressiveDisclosureComponent;
