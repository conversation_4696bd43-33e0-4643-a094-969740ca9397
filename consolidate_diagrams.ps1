# Script to consolidate all Mermaid diagrams into one directory

# Target directory
$targetDir = "D:\novafuse-api-superstore\Comphyology Diagrams\Mermaid"

# Source directories to search for .mmd files
$sourceDirs = @(
    "D:\novafuse-api-superstore\patent_diagrams",
    "D:\novafuse-api-superstore\patent_drawings",
    "D:\novafuse-api-superstore\patent_figures",
    "D:\novafuse-api-superstore\patent-diagrams",
    "D:\novafuse-api-superstore\patent-diagrams-new",
    "D:\novafuse-api-superstore\consolidated_diagrams",
    "D:\novafuse-api-superstore\patent_drawings\mermaid_diagrams",
    "D:\novafuse-api-superstore\coherence-reality-systems"
)

# Create target directory if it doesn't exist
if (-not (Test-Path -Path $targetDir)) {
    New-Item -ItemType Directory -Path $targetDir | Out-Null
}

# Counter for reporting
$filesCopied = 0
$filesSkipped = 0

# Process each source directory
foreach ($dir in $sourceDirs) {
    if (Test-Path -Path $dir) {
        Write-Host "Searching in: $dir"
        
        # Get all .mmd files in the current directory and subdirectories
        $files = Get-ChildItem -Path $dir -Filter "*.mmd" -Recurse -ErrorAction SilentlyContinue
        
        foreach ($file in $files) {
            $targetFile = Join-Path -Path $targetDir -ChildPath $file.Name
            
            # If file with same name exists, append a number
            $counter = 1
            while (Test-Path -Path $targetFile) {
                $nameWithoutExt = [System.IO.Path]::GetFileNameWithoutExtension($file.Name)
                $ext = [System.IO.Path]::GetExtension($file.Name)
                $newName = "${nameWithoutExt}_${counter}${ext}"
                $targetFile = Join-Path -Path $targetDir -ChildPath $newName
                $counter++
            }
            
            try {
                Copy-Item -Path $file.FullName -Destination $targetFile -Force
                Write-Host "Copied: $($file.FullName) -> $targetFile"
                $filesCopied++
            }
            catch {
                Write-Warning "Failed to copy $($file.FullName): $_"
                $filesSkipped++
            }
        }
    } else {
        Write-Warning "Directory not found: $dir"
    }
}

# Report results
Write-Host "`nConsolidation complete!"
Write-Host "Files copied: $filesCopied"
Write-Host "Files skipped: $filesSkipped"
Write-Host "Total files in target directory: $(Get-ChildItem -Path $targetDir -Filter "*.mmd" -Recurse | Measure-Object).Count"
