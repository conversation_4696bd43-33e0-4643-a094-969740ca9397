/**
 * API Key Service
 * 
 * This service handles API key management.
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, AuthenticationError } = require('../utils/errors');

class ApiKeyService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.apiKeysFile = path.join(this.dataDir, 'api_keys.json');
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Load API keys from file
   */
  async loadApiKeys() {
    try {
      const data = await fs.readFile(this.apiKeysFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error('Error loading API keys:', error);
      throw error;
    }
  }

  /**
   * Save API keys to file
   */
  async saveApiKeys(apiKeys) {
    try {
      await fs.writeFile(this.apiKeysFile, JSON.stringify(apiKeys, null, 2));
    } catch (error) {
      console.error('Error saving API keys:', error);
      throw error;
    }
  }

  /**
   * Generate a new API key
   */
  generateApiKey() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Create a new API key
   */
  async createApiKey(name, userId, permissions = ['read'], expiresIn = null) {
    if (!name) {
      throw new ValidationError('API key name is required');
    }
    
    if (!userId) {
      throw new ValidationError('User ID is required');
    }
    
    const apiKeys = await this.loadApiKeys();
    
    // Generate API key
    const apiKey = this.generateApiKey();
    
    // Calculate expiration date if provided
    let expiresAt = null;
    if (expiresIn) {
      expiresAt = new Date();
      expiresAt.setSeconds(expiresAt.getSeconds() + expiresIn);
      expiresAt = expiresAt.toISOString();
    }
    
    // Create new API key
    const newApiKey = {
      id: uuidv4(),
      key: apiKey,
      name,
      userId,
      permissions,
      created: new Date().toISOString(),
      expiresAt,
      lastUsed: null
    };
    
    apiKeys.push(newApiKey);
    await this.saveApiKeys(apiKeys);
    
    return newApiKey;
  }

  /**
   * Verify API key
   */
  async verifyApiKey(apiKey) {
    if (!apiKey) {
      throw new AuthenticationError('API key is required');
    }
    
    const apiKeys = await this.loadApiKeys();
    
    // Find API key
    const foundApiKey = apiKeys.find(key => key.key === apiKey);
    
    if (!foundApiKey) {
      throw new AuthenticationError('Invalid API key');
    }
    
    // Check if API key is expired
    if (foundApiKey.expiresAt && new Date(foundApiKey.expiresAt) < new Date()) {
      throw new AuthenticationError('API key expired');
    }
    
    // Update last used
    foundApiKey.lastUsed = new Date().toISOString();
    await this.saveApiKeys(apiKeys);
    
    return foundApiKey;
  }

  /**
   * Get all API keys
   */
  async getAllApiKeys() {
    return this.loadApiKeys();
  }

  /**
   * Get API keys by user ID
   */
  async getApiKeysByUserId(userId) {
    const apiKeys = await this.loadApiKeys();
    return apiKeys.filter(key => key.userId === userId);
  }

  /**
   * Get API key by ID
   */
  async getApiKeyById(id) {
    const apiKeys = await this.loadApiKeys();
    const apiKey = apiKeys.find(key => key.id === id);
    
    if (!apiKey) {
      throw new ValidationError(`API key with ID ${id} not found`);
    }
    
    return apiKey;
  }

  /**
   * Update API key
   */
  async updateApiKey(id, data) {
    const apiKeys = await this.loadApiKeys();
    const index = apiKeys.findIndex(key => key.id === id);
    
    if (index === -1) {
      throw new ValidationError(`API key with ID ${id} not found`);
    }
    
    // Don't allow updating the key itself
    if (data.key) {
      delete data.key;
    }
    
    // Update API key
    const updatedApiKey = {
      ...apiKeys[index],
      ...data,
      updated: new Date().toISOString()
    };
    
    apiKeys[index] = updatedApiKey;
    await this.saveApiKeys(apiKeys);
    
    return updatedApiKey;
  }

  /**
   * Delete API key
   */
  async deleteApiKey(id) {
    const apiKeys = await this.loadApiKeys();
    const filteredApiKeys = apiKeys.filter(key => key.id !== id);
    
    if (apiKeys.length === filteredApiKeys.length) {
      throw new ValidationError(`API key with ID ${id} not found`);
    }
    
    await this.saveApiKeys(filteredApiKeys);
    
    return { success: true, message: `API key with ID ${id} deleted` };
  }

  /**
   * Revoke all API keys for a user
   */
  async revokeUserApiKeys(userId) {
    const apiKeys = await this.loadApiKeys();
    const filteredApiKeys = apiKeys.filter(key => key.userId !== userId);
    
    await this.saveApiKeys(filteredApiKeys);
    
    return { 
      success: true, 
      message: `All API keys for user ${userId} revoked`,
      count: apiKeys.length - filteredApiKeys.length
    };
  }

  /**
   * Check if API key has permission
   */
  hasPermission(apiKey, permission) {
    if (!apiKey || !apiKey.permissions) {
      return false;
    }
    
    // Check for wildcard permission
    if (apiKey.permissions.includes('*')) {
      return true;
    }
    
    return apiKey.permissions.includes(permission);
  }
}

module.exports = ApiKeyService;
