/**
 * Brute Force Protection Controller
 * 
 * This controller handles API requests related to brute force protection.
 */

const BruteForceProtectionService = require('../services/BruteForceProtectionService');
const { ValidationError } = require('../utils/errors');

class BruteForceController {
  constructor() {
    this.bruteForceService = new BruteForceProtectionService();
  }

  /**
   * Get brute force protection configuration
   */
  async getConfig(req, res, next) {
    try {
      const config = this.bruteForceService.getConfig();
      
      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update brute force protection configuration
   */
  async updateConfig(req, res, next) {
    try {
      const { maxAttempts, windowMs, blockDuration } = req.body;
      
      // Validate configuration
      if (maxAttempts !== undefined && (!Number.isInteger(maxAttempts) || maxAttempts < 1)) {
        throw new ValidationError('maxAttempts must be a positive integer');
      }
      
      if (windowMs !== undefined && (!Number.isInteger(windowMs) || windowMs < 1000)) {
        throw new ValidationError('windowMs must be a positive integer (milliseconds) and at least 1000');
      }
      
      if (blockDuration !== undefined && (!Number.isInteger(blockDuration) || blockDuration < 1000)) {
        throw new ValidationError('blockDuration must be a positive integer (milliseconds) and at least 1000');
      }
      
      // Update configuration
      const config = await this.bruteForceService.updateConfig({
        maxAttempts,
        windowMs,
        blockDuration
      });
      
      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reset brute force protection for a specific identifier
   */
  async resetAttempts(req, res, next) {
    try {
      const { identifier } = req.body;
      
      if (!identifier) {
        throw new ValidationError('Identifier is required');
      }
      
      await this.bruteForceService.resetAttempts(identifier);
      
      res.json({
        success: true,
        message: `Brute force protection reset for ${identifier}`
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new BruteForceController();
