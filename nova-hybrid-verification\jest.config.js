/**
 * Jest configuration for the Hybrid DAG-based Zero-Knowledge System
 */

module.exports = {
  // The test environment that will be used for testing
  testEnvironment: 'node',
  
  // The glob patterns <PERSON><PERSON> uses to detect test files
  testMatch: [
    '**/test/**/*.test.js'
  ],
  
  // An array of regexp pattern strings that are matched against all test paths
  testPathIgnorePatterns: [
    '/node_modules/'
  ],
  
  // An array of regexp pattern strings that are matched against all source file paths
  // before re-running tests in watch mode
  watchPathIgnorePatterns: [
    '/node_modules/'
  ],
  
  // Indicates whether each individual test should be reported during the run
  verbose: true,
  
  // Automatically clear mock calls and instances between every test
  clearMocks: true,
  
  // Indicates whether the coverage information should be collected while executing the test
  collectCoverage: true,
  
  // The directory where Jest should output its coverage files
  coverageDirectory: 'coverage',
  
  // An array of regexp pattern strings used to skip coverage collection
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/test/'
  ],
  
  // A list of reporter names that <PERSON><PERSON> uses when writing coverage reports
  coverageReporters: [
    'json',
    'text',
    'lcov',
    'clover'
  ],
  
  // The minimum threshold enforcement for coverage results
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  
  // Make calling deprecated APIs throw helpful error messages
  errorOnDeprecated: true,
  
  // A set of global variables that need to be available in all test environments
  globals: {
    'NODE_ENV': 'test'
  },
  
  // An array of directory names to be searched recursively up from the requiring module's location
  moduleDirectories: [
    'node_modules',
    'src'
  ],
  
  // A map from regular expressions to module names that allow to stub out resources
  moduleNameMapper: {},
  
  // A preset that is used as a base for Jest's configuration
  preset: null,
  
  // Run tests with specified tag
  testRunner: 'jest-circus/runner',
  
  // Setup files after environment is set up
  setupFilesAfterEnv: [],
  
  // The maximum amount of workers used to run your tests
  maxWorkers: '50%'
};
