import React from 'react';
import DiagramTemplate from '../DiagramTemplate';

// Define the diagram data
const elements = [
  {
    id: 'pattern-translation',
    top: 50,
    left: 350,
    width: 300,
    text: 'Universal Pattern Language',
    number: '1',
    bold: true,
    fontSize: '20px',
    backgroundColor: '#f9f0ff'
  },
  // Pattern Translation Equation
  {
    id: 'translation-equation',
    top: 120,
    left: 350,
    width: 300,
    text: 'Pattern Translation = T(Pₐ → Pᵦ) = ∫(Pₐ⊗G)·dM',
    number: '2',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  // Domains
  {
    id: 'domain-a',
    top: 200,
    left: 150,
    width: 200,
    text: 'Domain A\n(e.g., Cybersecurity)',
    number: '3',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'domain-b',
    top: 200,
    left: 650,
    width: 200,
    text: 'Domain B\n(e.g., Healthcare)',
    number: '4',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  // Pattern Primitives
  {
    id: 'pattern-primitives',
    top: 280,
    left: 350,
    width: 300,
    text: 'Pattern Primitives',
    number: '5',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'oscillators',
    top: 340,
    left: 150,
    width: 150,
    text: 'Oscillators\n(periodic patterns)',
    number: '6',
    fontSize: '12px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'attractors',
    top: 340,
    left: 350,
    width: 150,
    text: 'Attractors\n(convergent patterns)',
    number: '7',
    fontSize: '12px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'bifurcators',
    top: 340,
    left: 550,
    width: 150,
    text: 'Bifurcators\n(divergent patterns)',
    number: '8',
    fontSize: '12px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'resonators',
    top: 340,
    left: 750,
    width: 150,
    text: 'Resonators\n(amplifying patterns)',
    number: '9',
    fontSize: '12px',
    backgroundColor: '#f6ffed'
  },
  // Transformation Operators
  {
    id: 'transformation-operators',
    top: 400,
    left: 350,
    width: 300,
    text: 'Transformation Operators',
    number: '10',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'tensor-product',
    top: 460,
    left: 150,
    width: 150,
    text: 'Tensor product (⊗)\nCombines patterns',
    number: '11',
    fontSize: '12px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'fusion-operator',
    top: 460,
    left: 350,
    width: 150,
    text: 'Fusion operator (⊕)\nMerges patterns',
    number: '12',
    fontSize: '12px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'curl-operator',
    top: 460,
    left: 550,
    width: 150,
    text: 'Curl operator (∇×)\nDetects rotational patterns',
    number: '13',
    fontSize: '12px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'divergence-operator',
    top: 460,
    left: 750,
    width: 150,
    text: 'Divergence operator (∇·)\nDetects expansive patterns',
    number: '14',
    fontSize: '12px',
    backgroundColor: '#fff2e8'
  },
  // Grammar Rules
  {
    id: 'grammar-rules',
    top: 520,
    left: 350,
    width: 300,
    text: 'Grammar Rules',
    number: '15',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'nesting',
    top: 580,
    left: 150,
    width: 150,
    text: 'Nesting\nPatterns within patterns',
    number: '16',
    fontSize: '12px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'scaling',
    top: 580,
    left: 350,
    width: 150,
    text: 'Scaling\nPatterns across scales',
    number: '17',
    fontSize: '12px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'resonance',
    top: 580,
    left: 550,
    width: 150,
    text: 'Resonance\nPatterns in harmony',
    number: '18',
    fontSize: '12px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'interference',
    top: 580,
    left: 750,
    width: 150,
    text: 'Interference\nPatterns in conflict',
    number: '19',
    fontSize: '12px',
    backgroundColor: '#e6f7ff'
  },
  // Implementation
  {
    id: 'implementation',
    top: 650,
    left: 350,
    width: 300,
    text: 'Technical Implementation: Language Processing System',
    number: '20',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#f9f0ff'
  }
];

const connections = [
  // Connect Universal Pattern Language to Translation Equation
  {
    start: { x: 500, y: 100 },
    end: { x: 500, y: 120 },
    type: 'arrow'
  },
  // Connect Translation Equation to Domains
  {
    start: { x: 400, y: 170 },
    end: { x: 250, y: 200 },
    type: 'arrow'
  },
  {
    start: { x: 600, y: 170 },
    end: { x: 750, y: 200 },
    type: 'arrow'
  },
  // Connect to Pattern Primitives
  {
    start: { x: 500, y: 170 },
    end: { x: 500, y: 280 },
    type: 'arrow'
  },
  // Connect Pattern Primitives to specific primitives
  {
    start: { x: 350, y: 330 },
    end: { x: 225, y: 340 },
    type: 'arrow'
  },
  {
    start: { x: 425, y: 330 },
    end: { x: 425, y: 340 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 330 },
    end: { x: 625, y: 340 },
    type: 'arrow'
  },
  {
    start: { x: 575, y: 330 },
    end: { x: 825, y: 340 },
    type: 'arrow'
  },
  // Connect to Transformation Operators
  {
    start: { x: 500, y: 370 },
    end: { x: 500, y: 400 },
    type: 'arrow'
  },
  // Connect Transformation Operators to specific operators
  {
    start: { x: 350, y: 450 },
    end: { x: 225, y: 460 },
    type: 'arrow'
  },
  {
    start: { x: 425, y: 450 },
    end: { x: 425, y: 460 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 450 },
    end: { x: 625, y: 460 },
    type: 'arrow'
  },
  {
    start: { x: 575, y: 450 },
    end: { x: 825, y: 460 },
    type: 'arrow'
  },
  // Connect to Grammar Rules
  {
    start: { x: 500, y: 490 },
    end: { x: 500, y: 520 },
    type: 'arrow'
  },
  // Connect Grammar Rules to specific rules
  {
    start: { x: 350, y: 570 },
    end: { x: 225, y: 580 },
    type: 'arrow'
  },
  {
    start: { x: 425, y: 570 },
    end: { x: 425, y: 580 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 570 },
    end: { x: 625, y: 580 },
    type: 'arrow'
  },
  {
    start: { x: 575, y: 570 },
    end: { x: 825, y: 580 },
    type: 'arrow'
  },
  // Connect to Implementation
  {
    start: { x: 500, y: 610 },
    end: { x: 500, y: 650 },
    type: 'arrow'
  },
  // Connect Domains across the diagram
  {
    start: { x: 350, y: 200 },
    end: { x: 650, y: 200 },
    type: 'arrow',
    dashed: true
  }
];

const PatternTranslation: React.FC = () => {
  return (
    <DiagramTemplate 
      elements={elements} 
      connections={connections} 
      width="950px" 
      height="700px" 
    />
  );
};

export default PatternTranslation;
