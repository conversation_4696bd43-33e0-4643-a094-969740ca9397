/**
 * Distributed Processing Components
 *
 * This module exports distributed processing components for the Finite Universe
 * Principle defense system, enabling distributed processing across multiple nodes.
 */

const { ClusterManager, createClusterManager } = require('./cluster-manager');
const { LoadBalancer, createLoadBalancer } = require('./load-balancer');
const { DistributedProcessor, createDistributedProcessor } = require('./distributed-processor');
const { NodeDiscovery, createNodeDiscovery } = require('./node-discovery');
const { PriorityQueue, createPriorityQueue } = require('./priority-queue');
const { CapabilityRouter, createCapabilityRouter } = require('./capability-router');

/**
 * Create all distributed processing components
 * @param {Object} options - Configuration options
 * @returns {Object} - Object containing all distributed processing components
 */
function createDistributedComponents(options = {}) {
  // Create cluster manager
  const clusterManager = createClusterManager({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    isMaster: options.isMaster !== undefined ? options.isMaster : true,
    ...options.clusterManagerOptions
  });

  // Create load balancer if master
  const loadBalancer = options.isMaster !== false
    ? createLoadBalancer({
        enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
        ...options.loadBalancerOptions
      })
    : null;

  // Create node discovery
  const nodeDiscovery = createNodeDiscovery({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    isMaster: options.isMaster !== undefined ? options.isMaster : true,
    nodeId: options.nodeId,
    capabilities: options.capabilities,
    ...options.nodeDiscoveryOptions
  });

  // Create priority queue
  const priorityQueue = createPriorityQueue({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    ...options.priorityQueueOptions
  });

  // Create capability router
  const capabilityRouter = createCapabilityRouter({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    ...options.capabilityRouterOptions
  });

  // Create distributed processor
  const distributedProcessor = createDistributedProcessor({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    isMaster: options.isMaster !== undefined ? options.isMaster : true,
    clusterManager,
    loadBalancer,
    nodeDiscovery,
    priorityQueue,
    capabilityRouter,
    capabilities: options.capabilities,
    ...options.distributedProcessorOptions
  });

  return {
    clusterManager,
    loadBalancer,
    nodeDiscovery,
    priorityQueue,
    capabilityRouter,
    distributedProcessor
  };
}

module.exports = {
  // Cluster manager
  ClusterManager,
  createClusterManager,

  // Load balancer
  LoadBalancer,
  createLoadBalancer,

  // Node discovery
  NodeDiscovery,
  createNodeDiscovery,

  // Priority queue
  PriorityQueue,
  createPriorityQueue,

  // Capability router
  CapabilityRouter,
  createCapabilityRouter,

  // Distributed processor
  DistributedProcessor,
  createDistributedProcessor,

  // Factory function
  createDistributedComponents
};
