/**
 * Feature Flag Test Script
 * 
 * This script tests the feature flag system by checking if the feature flags
 * are correctly applied to UI components and API routes.
 */

const puppeteer = require('puppeteer');
const axios = require('axios');
const chalk = require('chalk');

// Define product tiers to test
const productTiers = ['novaPrime', 'novaCore', 'novaShield', 'novaLearn', 'novaAssistAI'];

// Define features to test
const features = [
  // Dashboard features
  { category: 'dashboard', feature: 'overview', name: 'Dashboard Overview' },
  { category: 'dashboard', feature: 'analytics', name: 'Analytics Dashboard' },
  { category: 'dashboard', feature: 'reports', name: 'Reports' },
  { category: 'dashboard', feature: 'customization', name: 'Dashboard Customization' },
  
  // GRC features
  { category: 'grc', feature: 'privacy', name: 'Privacy Management' },
  { category: 'grc', feature: 'security', name: 'Security Assessment' },
  { category: 'grc', feature: 'compliance', name: 'Regulatory Compliance' },
  { category: 'grc', feature: 'control', name: 'Control Testing' },
  { category: 'grc', feature: 'esg', name: 'ESG Management' },
  
  // Advanced features
  { category: 'advanced', feature: 'aiAssistant', name: 'AI Assistant' },
  { category: 'advanced', feature: 'predictiveAnalytics', name: 'Predictive Analytics' },
  { category: 'advanced', feature: 'automatedRemediation', name: 'Automated Remediation' },
  { category: 'advanced', feature: 'customIntegrations', name: 'Custom Integrations' },
];

// Define expected feature access for each product tier
const expectedFeatureAccess = {
  novaPrime: {
    dashboard: { overview: true, analytics: true, reports: true, customization: true },
    grc: { privacy: true, security: true, compliance: true, control: true, esg: true },
    advanced: { aiAssistant: true, predictiveAnalytics: true, automatedRemediation: true, customIntegrations: true },
  },
  novaCore: {
    dashboard: { overview: true, analytics: false, reports: true, customization: false },
    grc: { privacy: true, security: true, compliance: true, control: false, esg: false },
    advanced: { aiAssistant: false, predictiveAnalytics: false, automatedRemediation: false, customIntegrations: false },
  },
  novaShield: {
    dashboard: { overview: true, analytics: true, reports: true, customization: false },
    grc: { privacy: false, security: true, compliance: true, control: true, esg: false },
    advanced: { aiAssistant: false, predictiveAnalytics: true, automatedRemediation: true, customIntegrations: false },
  },
  novaLearn: {
    dashboard: { overview: true, analytics: false, reports: true, customization: false },
    grc: { privacy: false, security: false, compliance: true, control: false, esg: false },
    advanced: { aiAssistant: true, predictiveAnalytics: false, automatedRemediation: false, customIntegrations: false },
  },
  novaAssistAI: {
    dashboard: { overview: true, analytics: true, reports: true, customization: true },
    grc: { privacy: true, security: true, compliance: true, control: true, esg: true },
    advanced: { aiAssistant: true, predictiveAnalytics: true, automatedRemediation: true, customIntegrations: true },
  },
};

// Base URLs
const UI_URL = process.env.UI_URL || 'http://localhost:3001';
const API_URL = process.env.API_URL || 'http://localhost:3000';

/**
 * Test UI feature flags using Puppeteer
 */
async function testUIFeatureFlags() {
  console.log(chalk.blue('Testing UI Feature Flags\n'));
  
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  try {
    // Navigate to the feature flag demo page
    await page.goto(`${UI_URL}/feature-flag-demo`);
    
    // Wait for the page to load
    await page.waitForSelector('.product-switcher');
    
    let passCount = 0;
    let failCount = 0;
    
    // Test each product tier
    for (const tier of productTiers) {
      console.log(chalk.yellow(`\nTesting UI with product tier: ${tier}\n`));
      
      // Click the product tier button
      await page.click(`button:contains("${tier}")`);
      await page.waitForTimeout(500); // Wait for the UI to update
      
      // Test each feature
      for (const { category, feature, name } of features) {
        if (expectedFeatureAccess[tier][category] && expectedFeatureAccess[tier][category][feature] !== undefined) {
          const expectedAccess = expectedFeatureAccess[tier][category][feature];
          
          // Check if the feature is enabled or disabled
          const featureCard = await page.$(`div:contains("${name}")`);
          const isEnabled = await featureCard.evaluate(el => el.classList.contains('bg-green-50'));
          
          if (isEnabled === expectedAccess) {
            console.log(chalk.green(`✓ ${name} - Enabled: ${isEnabled} (Expected: ${expectedAccess})`));
            passCount++;
          } else {
            console.log(chalk.red(`✗ ${name} - Enabled: ${isEnabled} (Expected: ${expectedAccess})`));
            failCount++;
          }
        }
      }
    }
    
    console.log(chalk.blue(`\nUI Test Results: ${passCount} passed, ${failCount} failed`));
    
  } catch (error) {
    console.error(chalk.red(`Error testing UI feature flags: ${error.message}`));
  } finally {
    await browser.close();
  }
}

/**
 * Test API feature flags using Axios
 */
async function testAPIFeatureFlags() {
  console.log(chalk.blue('\nTesting API Feature Flags\n'));
  
  // Define API endpoints to test
  const endpoints = [
    { path: '/api/privacy/management/processing-activities', feature: 'privacy-management-processing-activities' },
    { path: '/api/privacy/management/subject-requests', feature: 'privacy-management-subject-requests' },
    { path: '/api/privacy/management/consent', feature: 'privacy-management-consent-records' },
    { path: '/api/privacy/management/privacy-notices', feature: 'privacy-management-privacy-notices' },
    { path: '/api/privacy/management/data-breaches', feature: 'privacy-management-data-breaches' },
    { path: '/api/privacy/management/impact-assessment', feature: 'privacy-management-impact-assessment' },
    { path: '/api/compliance/regulatory/frameworks', feature: 'compliance-regulatory-frameworks' },
    { path: '/api/compliance/regulatory/requirements', feature: 'compliance-regulatory-requirements' },
    { path: '/api/security/assessment/assessments', feature: 'security-assessment-assessments' },
  ];
  
  // Define expected access based on feature flags
  const featureAccess = {
    'privacy-management-processing-activities': { novaprime: true, novacore: true, novashield: false, novalearn: false },
    'privacy-management-subject-requests': { novaprime: true, novacore: true, novashield: false, novalearn: false },
    'privacy-management-consent-records': { novaprime: true, novacore: true, novashield: false, novalearn: false },
    'privacy-management-privacy-notices': { novaprime: true, novacore: true, novashield: false, novalearn: false },
    'privacy-management-data-breaches': { novaprime: true, novacore: true, novashield: true, novalearn: false },
    'privacy-management-impact-assessment': { novaprime: true, novacore: false, novashield: false, novalearn: false },
    'compliance-regulatory-frameworks': { novaprime: true, novacore: true, novashield: true, novalearn: true },
    'compliance-regulatory-requirements': { novaprime: true, novacore: true, novashield: true, novalearn: true },
    'security-assessment-assessments': { novaprime: true, novacore: false, novashield: true, novalearn: false },
  };
  
  // Check if the API server is running
  try {
    await axios.get(`${API_URL}/health`, { timeout: 5000 });
  } catch (error) {
    console.log(chalk.red(`API server not running at ${API_URL}. Please start the API server before running this script.`));
    return;
  }
  
  let passCount = 0;
  let failCount = 0;
  
  // Test each product tier
  for (const tier of productTiers.map(t => t.toLowerCase())) {
    console.log(chalk.yellow(`\nTesting API with product tier: ${tier.toUpperCase()}\n`));
    
    // Test each endpoint
    for (const endpoint of endpoints) {
      const { path, feature } = endpoint;
      const expectedAccess = featureAccess[feature][tier];
      const url = `${API_URL}${path}`;
      
      try {
        const response = await axios.get(url, {
          headers: {
            'Authorization': 'Bearer test-token',
            'X-Product-Tier': tier
          },
          validateStatus: (status) => status < 500 // Don't throw for 4xx errors
        });
        
        const hasAccess = response.status === 200;
        
        if (hasAccess === expectedAccess) {
          console.log(chalk.green(`✓ ${path} - Access: ${hasAccess} (Expected: ${expectedAccess})`));
          passCount++;
        } else {
          console.log(chalk.red(`✗ ${path} - Access: ${hasAccess} (Expected: ${expectedAccess})`));
          console.log(chalk.gray(`  Status: ${response.status}`));
          console.log(chalk.gray(`  Response: ${JSON.stringify(response.data)}`));
          failCount++;
        }
      } catch (error) {
        console.log(chalk.red(`✗ ${path} - Error: ${error.message}`));
        failCount++;
      }
    }
  }
  
  console.log(chalk.blue(`\nAPI Test Results: ${passCount} passed, ${failCount} failed`));
}

/**
 * Run all tests
 */
async function runTests() {
  console.log(chalk.blue('Starting Feature Flag Tests\n'));
  
  try {
    await testUIFeatureFlags();
    await testAPIFeatureFlags();
    
    console.log(chalk.blue('\nAll tests completed.'));
  } catch (error) {
    console.error(chalk.red(`Error running tests: ${error.message}`));
  }
}

// Run the tests
runTests().catch(console.error);
