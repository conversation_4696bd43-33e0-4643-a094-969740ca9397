/**
 * Error Scenario Tester Component
 * 
 * This component allows users to simulate and test error scenarios for API connectors.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Chip, 
  CircularProgress, 
  Divider, 
  FormControl, 
  Grid, 
  InputLabel, 
  MenuItem, 
  Paper, 
  Select, 
  TextField, 
  Typography 
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import WarningIcon from '@mui/icons-material/Warning';
import ErrorIcon from '@mui/icons-material/Error';
import NetworkCheckIcon from '@mui/icons-material/NetworkCheck';
import LockIcon from '@mui/icons-material/Lock';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CodeIcon from '@mui/icons-material/Code';
import ResponseViewer from './ResponseViewer';

const ErrorScenarioTester = ({ connector, credentials }) => {
  const [scenarioType, setScenarioType] = useState('network');
  const [customStatus, setCustomStatus] = useState(500);
  const [customMessage, setCustomMessage] = useState('Internal Server Error');
  const [customDelay, setCustomDelay] = useState(5000);
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState(null);
  
  const errorScenarios = [
    { value: 'network', label: 'Network Error', icon: <NetworkCheckIcon /> },
    { value: 'auth', label: 'Authentication Error', icon: <LockIcon /> },
    { value: 'timeout', label: 'Timeout', icon: <AccessTimeIcon /> },
    { value: 'rate_limit', label: 'Rate Limit Exceeded', icon: <WarningIcon /> },
    { value: 'server', label: 'Server Error', icon: <ErrorIcon /> },
    { value: 'custom', label: 'Custom Error', icon: <CodeIcon /> }
  ];
  
  const handleScenarioChange = (event) => {
    setScenarioType(event.target.value);
    setResponse(null);
  };
  
  const handleCustomStatusChange = (event) => {
    setCustomStatus(parseInt(event.target.value) || 500);
  };
  
  const handleCustomMessageChange = (event) => {
    setCustomMessage(event.target.value);
  };
  
  const handleCustomDelayChange = (event) => {
    setCustomDelay(parseInt(event.target.value) || 5000);
  };
  
  const handleTestScenario = async () => {
    setLoading(true);
    setResponse(null);
    
    try {
      // In a real implementation, this would call an API to simulate the error scenario
      const result = await simulateErrorScenario(scenarioType, {
        customStatus,
        customMessage,
        customDelay
      });
      
      setResponse(result);
    } catch (error) {
      console.error('Error simulating scenario:', error);
      
      // For network errors, we might not get a proper response object
      setResponse({
        status: 0,
        statusText: 'Network Error',
        headers: {},
        data: {
          error: error.message || 'A network error occurred'
        }
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Mock function to simulate error scenarios
  const simulateErrorScenario = async (type, options) => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    switch (type) {
      case 'network':
        throw new Error('Network connection refused');
        
      case 'auth':
        return {
          status: 401,
          statusText: 'Unauthorized',
          headers: {
            'content-type': 'application/json'
          },
          data: {
            error: 'invalid_credentials',
            error_description: 'The credentials provided are invalid or expired'
          }
        };
        
      case 'timeout':
        await new Promise(resolve => setTimeout(resolve, 3000));
        throw new Error('Request timed out after 3000ms');
        
      case 'rate_limit':
        return {
          status: 429,
          statusText: 'Too Many Requests',
          headers: {
            'content-type': 'application/json',
            'x-rate-limit-limit': '100',
            'x-rate-limit-remaining': '0',
            'x-rate-limit-reset': Math.floor(Date.now() / 1000) + 60
          },
          data: {
            error: 'rate_limit_exceeded',
            error_description: 'You have exceeded the rate limit of 100 requests per minute',
            retry_after: 60
          }
        };
        
      case 'server':
        return {
          status: 500,
          statusText: 'Internal Server Error',
          headers: {
            'content-type': 'application/json'
          },
          data: {
            error: 'internal_server_error',
            error_description: 'An unexpected error occurred on the server',
            request_id: `req_${Math.random().toString(36).substring(2, 15)}`
          }
        };
        
      case 'custom':
        if (options.customDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, Math.min(options.customDelay, 10000)));
        }
        
        return {
          status: options.customStatus,
          statusText: getStatusText(options.customStatus),
          headers: {
            'content-type': 'application/json'
          },
          data: {
            error: 'custom_error',
            error_description: options.customMessage,
            timestamp: new Date().toISOString()
          }
        };
        
      default:
        throw new Error('Unknown error scenario type');
    }
  };
  
  const getStatusText = (status) => {
    const statusTexts = {
      400: 'Bad Request',
      401: 'Unauthorized',
      403: 'Forbidden',
      404: 'Not Found',
      405: 'Method Not Allowed',
      408: 'Request Timeout',
      409: 'Conflict',
      410: 'Gone',
      413: 'Payload Too Large',
      422: 'Unprocessable Entity',
      429: 'Too Many Requests',
      500: 'Internal Server Error',
      501: 'Not Implemented',
      502: 'Bad Gateway',
      503: 'Service Unavailable',
      504: 'Gateway Timeout'
    };
    
    return statusTexts[status] || 'Unknown Status';
  };
  
  const getScenarioDescription = () => {
    switch (scenarioType) {
      case 'network':
        return 'Simulates a network connection error where the client cannot reach the server.';
        
      case 'auth':
        return 'Simulates an authentication error (401 Unauthorized) due to invalid or expired credentials.';
        
      case 'timeout':
        return 'Simulates a request timeout where the server takes too long to respond.';
        
      case 'rate_limit':
        return 'Simulates a rate limit exceeded error (429 Too Many Requests) when too many requests are made in a short period.';
        
      case 'server':
        return 'Simulates a server error (500 Internal Server Error) when something goes wrong on the server side.';
        
      case 'custom':
        return 'Create a custom error scenario with specific status code, message, and delay.';
        
      default:
        return '';
    }
  };
  
  return (
    <Box>
      <Card variant="outlined" sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Error Scenario Tester
          </Typography>
          
          <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
            Test how your connector handles various error scenarios.
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="scenario-select-label">Error Scenario</InputLabel>
                <Select
                  labelId="scenario-select-label"
                  id="scenario-select"
                  value={scenarioType}
                  label="Error Scenario"
                  onChange={handleScenarioChange}
                  renderValue={(selected) => {
                    const scenario = errorScenarios.find(s => s.value === selected);
                    return (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {scenario.icon}
                        <Typography sx={{ ml: 1 }}>{scenario.label}</Typography>
                      </Box>
                    );
                  }}
                >
                  {errorScenarios.map(scenario => (
                    <MenuItem key={scenario.value} value={scenario.value}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {scenario.icon}
                        <Typography sx={{ ml: 1 }}>{scenario.label}</Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              <Typography variant="body2" color="textSecondary" sx={{ mt: 1, mb: 3 }}>
                {getScenarioDescription()}
              </Typography>
              
              {scenarioType === 'custom' && (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      label="Status Code"
                      type="number"
                      value={customStatus}
                      onChange={handleCustomStatusChange}
                      inputProps={{ min: 100, max: 599 }}
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={8}>
                    <TextField
                      fullWidth
                      label="Error Message"
                      value={customMessage}
                      onChange={handleCustomMessageChange}
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Delay (ms)"
                      type="number"
                      value={customDelay}
                      onChange={handleCustomDelayChange}
                      helperText="Delay before response (max 10000ms)"
                      inputProps={{ min: 0, max: 10000 }}
                    />
                  </Grid>
                </Grid>
              )}
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                <Typography variant="subtitle1" gutterBottom>
                  How to Handle This Error
                </Typography>
                
                <Typography variant="body2" paragraph>
                  {scenarioType === 'network' && (
                    'Network errors should be handled by implementing retry logic with exponential backoff. Check network connectivity and server availability before retrying.'
                  )}
                  
                  {scenarioType === 'auth' && (
                    'Authentication errors should trigger a credential refresh or prompt the user to re-authenticate. Check if tokens are expired or credentials are incorrect.'
                  )}
                  
                  {scenarioType === 'timeout' && (
                    'Timeout errors should be handled by implementing retry logic with increased timeout values. Consider breaking large requests into smaller chunks.'
                  )}
                  
                  {scenarioType === 'rate_limit' && (
                    'Rate limit errors should be handled by implementing rate limiting on the client side and respecting the retry-after header. Implement request queuing if necessary.'
                  )}
                  
                  {scenarioType === 'server' && (
                    'Server errors should be logged and reported. Implement retry logic for idempotent operations, but avoid retrying non-idempotent operations that might cause data corruption.'
                  )}
                  
                  {scenarioType === 'custom' && (
                    'Custom errors should be handled based on the specific status code and error message. Implement appropriate error handling logic for your specific use case.'
                  )}
                </Typography>
                
                <Typography variant="subtitle2" gutterBottom>
                  Best Practices:
                </Typography>
                
                <ul>
                  <li>
                    <Typography variant="body2">
                      Log all errors with relevant context for debugging
                    </Typography>
                  </li>
                  <li>
                    <Typography variant="body2">
                      Implement retry logic with exponential backoff for transient errors
                    </Typography>
                  </li>
                  <li>
                    <Typography variant="body2">
                      Provide clear error messages to users
                    </Typography>
                  </li>
                  <li>
                    <Typography variant="body2">
                      Fail gracefully and maintain application state
                    </Typography>
                  </li>
                </ul>
              </Paper>
            </Grid>
          </Grid>
          
          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <PlayArrowIcon />}
              onClick={handleTestScenario}
              disabled={loading}
            >
              {loading ? 'Simulating...' : 'Simulate Error'}
            </Button>
          </Box>
        </CardContent>
      </Card>
      
      {response && (
        <Card variant="outlined">
          <CardContent>
            <ResponseViewer response={response} />
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default ErrorScenarioTester;
