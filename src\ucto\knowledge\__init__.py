"""
UCTO Knowledge Module

This module provides a knowledge base for compliance information.
"""

import os
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KnowledgeManager:
    """
    Python wrapper for the JavaScript KnowledgeManager.
    
    This class provides a Python interface to the JavaScript KnowledgeManager.
    """
    
    def __init__(self, data_dir: Optional[str] = None):
        """
        Initialize the Knowledge Manager.
        
        Args:
            data_dir: Path to a directory for storing knowledge data
        """
        logger.info("Initializing Knowledge Manager (Python wrapper)")
        
        # Set the data directory
        self.data_dir = data_dir or os.path.join(os.getcwd(), 'knowledge_data')
        
        # Create the data directory if it doesn't exist
        os.makedirs(self.data_dir, exist_ok=True)
        
        # Initialize entity stores
        self.entities = {}
        
        # Initialize relationship stores
        self.relationships = {}
        
        # Load data
        self._load_data()
        
        logger.info(f"Knowledge Manager initialized with data directory: {self.data_dir}")
    
    def get_schema(self) -> Dict[str, Any]:
        """
        Get the knowledge schema.
        
        Returns:
            The knowledge schema
        """
        # In a real implementation, this would call the JavaScript KnowledgeManager
        # For now, return a simplified schema
        return {
            "metadata": {
                "name": "UCTO Compliance Knowledge Base",
                "description": "Centralized repository of compliance information, best practices, and guidance",
                "version": "1.0.0"
            },
            "entityTypes": [
                {
                    "id": "framework",
                    "name": "Compliance Framework",
                    "description": "A compliance framework or standard"
                },
                {
                    "id": "control",
                    "name": "Control",
                    "description": "A compliance control or requirement"
                },
                {
                    "id": "guidance",
                    "name": "Guidance",
                    "description": "Guidance or best practice for compliance"
                },
                {
                    "id": "resource",
                    "name": "Resource",
                    "description": "A compliance resource such as a template, document, or tool"
                },
                {
                    "id": "glossary_term",
                    "name": "Glossary Term",
                    "description": "A term in the compliance glossary"
                },
                {
                    "id": "faq",
                    "name": "FAQ",
                    "description": "A frequently asked question about compliance"
                }
            ],
            "relationshipTypes": [
                {
                    "id": "framework_to_control",
                    "name": "Framework to Control",
                    "description": "Relationship between a framework and a control",
                    "sourceType": "framework",
                    "targetType": "control"
                },
                {
                    "id": "control_to_control",
                    "name": "Control to Control",
                    "description": "Relationship between controls",
                    "sourceType": "control",
                    "targetType": "control"
                },
                {
                    "id": "control_to_guidance",
                    "name": "Control to Guidance",
                    "description": "Relationship between a control and guidance",
                    "sourceType": "control",
                    "targetType": "guidance"
                },
                {
                    "id": "control_to_resource",
                    "name": "Control to Resource",
                    "description": "Relationship between a control and a resource",
                    "sourceType": "control",
                    "targetType": "resource"
                }
            ]
        }
    
    def get_entity_types(self) -> List[Dict[str, Any]]:
        """
        Get entity types.
        
        Returns:
            Entity types
        """
        return self.get_schema()["entityTypes"]
    
    def get_relationship_types(self) -> List[Dict[str, Any]]:
        """
        Get relationship types.
        
        Returns:
            Relationship types
        """
        return self.get_schema()["relationshipTypes"]
    
    def create_entity(self, entity_type: str, entity_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an entity.
        
        Args:
            entity_type: Type of entity
            entity_data: Entity data
            
        Returns:
            Created entity
        """
        logger.info(f"Creating entity of type: {entity_type}")
        
        # Validate entity type
        entity_types = [et["id"] for et in self.get_entity_types()]
        if entity_type not in entity_types:
            raise ValueError(f"Invalid entity type: {entity_type}")
        
        # Generate ID if not provided
        if "id" not in entity_data:
            entity_data["id"] = f"{entity_type}_{uuid.uuid4()}"
        
        # Add timestamps
        entity_data["created_at"] = datetime.now().isoformat()
        entity_data["updated_at"] = datetime.now().isoformat()
        
        # Initialize entity store if it doesn't exist
        if entity_type not in self.entities:
            self.entities[entity_type] = {}
        
        # Check if entity already exists
        if entity_data["id"] in self.entities[entity_type]:
            raise ValueError(f"Entity already exists: {entity_type}/{entity_data['id']}")
        
        # Store the entity
        self.entities[entity_type][entity_data["id"]] = entity_data
        
        # Save the entity to disk
        self._save_entity(entity_type, entity_data)
        
        return entity_data
    
    def get_entity(self, entity_type: str, entity_id: str) -> Optional[Dict[str, Any]]:
        """
        Get an entity by ID.
        
        Args:
            entity_type: Type of entity
            entity_id: Entity ID
            
        Returns:
            Entity
        """
        # Check if entity store exists
        if entity_type not in self.entities:
            return None
        
        # Return the entity
        return self.entities[entity_type].get(entity_id)
    
    def get_entities(self, entity_type: str) -> List[Dict[str, Any]]:
        """
        Get all entities of a type.
        
        Args:
            entity_type: Type of entity
            
        Returns:
            Entities
        """
        # Check if entity store exists
        if entity_type not in self.entities:
            return []
        
        # Return all entities
        return list(self.entities[entity_type].values())
    
    def update_entity(self, entity_type: str, entity_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an entity.
        
        Args:
            entity_type: Type of entity
            entity_id: Entity ID
            updates: Updates to apply
            
        Returns:
            Updated entity
        """
        logger.info(f"Updating entity: {entity_type}/{entity_id}")
        
        # Check if entity store exists
        if entity_type not in self.entities:
            raise ValueError(f"Entity type not found: {entity_type}")
        
        # Check if entity exists
        if entity_id not in self.entities[entity_type]:
            raise ValueError(f"Entity not found: {entity_type}/{entity_id}")
        
        # Get the entity
        entity = self.entities[entity_type][entity_id]
        
        # Apply updates
        updated_entity = {**entity, **updates}
        
        # Update timestamp
        updated_entity["updated_at"] = datetime.now().isoformat()
        
        # Store the updated entity
        self.entities[entity_type][entity_id] = updated_entity
        
        # Save the entity to disk
        self._save_entity(entity_type, updated_entity)
        
        return updated_entity
    
    def delete_entity(self, entity_type: str, entity_id: str) -> bool:
        """
        Delete an entity.
        
        Args:
            entity_type: Type of entity
            entity_id: Entity ID
            
        Returns:
            Success
        """
        logger.info(f"Deleting entity: {entity_type}/{entity_id}")
        
        # Check if entity store exists
        if entity_type not in self.entities:
            raise ValueError(f"Entity type not found: {entity_type}")
        
        # Check if entity exists
        if entity_id not in self.entities[entity_type]:
            raise ValueError(f"Entity not found: {entity_type}/{entity_id}")
        
        # Delete the entity
        del self.entities[entity_type][entity_id]
        
        # Delete the entity file
        entity_dir = os.path.join(self.data_dir, entity_type)
        entity_path = os.path.join(entity_dir, f"{entity_id}.json")
        
        if os.path.exists(entity_path):
            os.remove(entity_path)
        
        # Delete relationships involving the entity
        self._delete_entity_relationships(entity_type, entity_id)
        
        return True
    
    def create_relationship(self, relationship_type: str, source_type: str, source_id: str, target_type: str, target_id: str, properties: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create a relationship between entities.
        
        Args:
            relationship_type: Type of relationship
            source_type: Type of source entity
            source_id: ID of source entity
            target_type: Type of target entity
            target_id: ID of target entity
            properties: Relationship properties
            
        Returns:
            Created relationship
        """
        logger.info(f"Creating relationship: {relationship_type} from {source_type}/{source_id} to {target_type}/{target_id}")
        
        # Validate relationship type
        relationship_types = self.get_relationship_types()
        relationship_type_def = next((rt for rt in relationship_types if rt["id"] == relationship_type), None)
        
        if not relationship_type_def:
            raise ValueError(f"Invalid relationship type: {relationship_type}")
        
        # Validate source and target types
        if relationship_type_def["sourceType"] != source_type:
            raise ValueError(f"Invalid source type for relationship: {source_type}")
        
        if relationship_type_def["targetType"] != target_type:
            raise ValueError(f"Invalid target type for relationship: {target_type}")
        
        # Check if source and target entities exist
        if source_type not in self.entities or source_id not in self.entities[source_type]:
            raise ValueError(f"Source entity not found: {source_type}/{source_id}")
        
        if target_type not in self.entities or target_id not in self.entities[target_type]:
            raise ValueError(f"Target entity not found: {target_type}/{target_id}")
        
        # Generate relationship ID
        relationship_id = f"{source_type}_{source_id}_{target_type}_{target_id}"
        
        # Initialize relationship store if it doesn't exist
        if relationship_type not in self.relationships:
            self.relationships[relationship_type] = {}
        
        # Check if relationship already exists
        if relationship_id in self.relationships[relationship_type]:
            raise ValueError(f"Relationship already exists: {relationship_type}/{relationship_id}")
        
        # Create the relationship
        relationship = {
            "id": relationship_id,
            "type": relationship_type,
            "sourceType": source_type,
            "sourceId": source_id,
            "targetType": target_type,
            "targetId": target_id,
            "properties": properties or {}
        }
        
        # Store the relationship
        self.relationships[relationship_type][relationship_id] = relationship
        
        # Save the relationship to disk
        self._save_relationship(relationship_type, relationship)
        
        return relationship
    
    def get_entity_relationships(self, entity_type: str, entity_id: str, relationship_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get relationships for an entity.
        
        Args:
            entity_type: Type of entity
            entity_id: Entity ID
            relationship_type: Type of relationship (optional)
            
        Returns:
            Relationships
        """
        relationships = []
        
        # Get relationship types to check
        relationship_types = [relationship_type] if relationship_type else list(self.relationships.keys())
        
        # Check each relationship type
        for rel_type in relationship_types:
            # Skip if relationship store doesn't exist
            if rel_type not in self.relationships:
                continue
            
            # Get relationships where entity is source or target
            for relationship in self.relationships[rel_type].values():
                if ((relationship["sourceType"] == entity_type and relationship["sourceId"] == entity_id) or
                    (relationship["targetType"] == entity_type and relationship["targetId"] == entity_id)):
                    relationships.append(relationship)
        
        return relationships
    
    def delete_relationship(self, relationship_type: str, relationship_id: str) -> bool:
        """
        Delete a relationship.
        
        Args:
            relationship_type: Type of relationship
            relationship_id: Relationship ID
            
        Returns:
            Success
        """
        logger.info(f"Deleting relationship: {relationship_type}/{relationship_id}")
        
        # Check if relationship store exists
        if relationship_type not in self.relationships:
            raise ValueError(f"Relationship type not found: {relationship_type}")
        
        # Check if relationship exists
        if relationship_id not in self.relationships[relationship_type]:
            raise ValueError(f"Relationship not found: {relationship_type}/{relationship_id}")
        
        # Delete the relationship
        del self.relationships[relationship_type][relationship_id]
        
        # Delete the relationship file
        relationship_dir = os.path.join(self.data_dir, "relationships", relationship_type)
        relationship_path = os.path.join(relationship_dir, f"{relationship_id}.json")
        
        if os.path.exists(relationship_path):
            os.remove(relationship_path)
        
        return True
    
    def search(self, query: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Search the knowledge base.
        
        Args:
            query: Search query
            options: Search options
            
        Returns:
            Search results
        """
        logger.info(f"Searching knowledge base: {query}")
        
        # Default options
        default_options = {
            "entityTypes": None,  # All entity types
            "limit": 10,
            "offset": 0,
            "sortBy": "relevance"
        }
        
        # Merge options
        search_options = {**default_options, **(options or {})}
        
        # Get entity types to search
        entity_types = search_options["entityTypes"] or list(self.entities.keys())
        
        # Search results
        results = []
        
        # Search each entity type
        for entity_type in entity_types:
            # Skip if entity store doesn't exist
            if entity_type not in self.entities:
                continue
            
            # Search entities
            for entity in self.entities[entity_type].values():
                # Check if entity matches query
                matches = False
                score = 0
                
                # Simple search implementation
                for key, value in entity.items():
                    if isinstance(value, str) and query.lower() in value.lower():
                        matches = True
                        score += 1
                    elif isinstance(value, list):
                        for item in value:
                            if isinstance(item, str) and query.lower() in item.lower():
                                matches = True
                                score += 1
                
                if matches:
                    results.append({
                        "entity": entity,
                        "entityType": entity_type,
                        "score": score
                    })
        
        # Sort results
        if search_options["sortBy"] == "relevance":
            results.sort(key=lambda x: x["score"], reverse=True)
        
        # Apply limit and offset
        paginated_results = results[search_options["offset"]:search_options["offset"] + search_options["limit"]]
        
        return {
            "query": query,
            "total": len(results),
            "results": paginated_results
        }
    
    def _save_entity(self, entity_type: str, entity: Dict[str, Any]) -> bool:
        """
        Save an entity to disk.
        
        Args:
            entity_type: Type of entity
            entity: Entity data
            
        Returns:
            Success
        """
        # Create the entity directory if it doesn't exist
        entity_dir = os.path.join(self.data_dir, entity_type)
        os.makedirs(entity_dir, exist_ok=True)
        
        # Save the entity to a file
        entity_path = os.path.join(entity_dir, f"{entity['id']}.json")
        with open(entity_path, 'w', encoding='utf-8') as f:
            json.dump(entity, f, indent=2)
        
        return True
    
    def _save_relationship(self, relationship_type: str, relationship: Dict[str, Any]) -> bool:
        """
        Save a relationship to disk.
        
        Args:
            relationship_type: Type of relationship
            relationship: Relationship data
            
        Returns:
            Success
        """
        # Create the relationship directory if it doesn't exist
        relationship_dir = os.path.join(self.data_dir, "relationships", relationship_type)
        os.makedirs(relationship_dir, exist_ok=True)
        
        # Save the relationship to a file
        relationship_path = os.path.join(relationship_dir, f"{relationship['id']}.json")
        with open(relationship_path, 'w', encoding='utf-8') as f:
            json.dump(relationship, f, indent=2)
        
        return True
    
    def _load_data(self) -> bool:
        """
        Load data from disk.
        
        Returns:
            Success
        """
        logger.info("Loading data from disk")
        
        # Load entities
        for entity_type in [et["id"] for et in self.get_entity_types()]:
            self._load_entities(entity_type)
        
        # Load relationships
        for relationship_type in [rt["id"] for rt in self.get_relationship_types()]:
            self._load_relationships(relationship_type)
        
        return True
    
    def _load_entities(self, entity_type: str) -> bool:
        """
        Load entities of a type from disk.
        
        Args:
            entity_type: Type of entity
            
        Returns:
            Success
        """
        logger.info(f"Loading entities of type: {entity_type}")
        
        # Initialize entity store
        self.entities[entity_type] = {}
        
        # Create the entity directory if it doesn't exist
        entity_dir = os.path.join(self.data_dir, entity_type)
        os.makedirs(entity_dir, exist_ok=True)
        
        # Get all entity files
        entity_files = [f for f in os.listdir(entity_dir) if f.endswith('.json')]
        
        # Load each entity
        for entity_file in entity_files:
            try:
                entity_path = os.path.join(entity_dir, entity_file)
                with open(entity_path, 'r', encoding='utf-8') as f:
                    entity = json.load(f)
                
                # Store the entity
                self.entities[entity_type][entity["id"]] = entity
                
                logger.info(f"Loaded entity from disk: {entity_type}/{entity['id']}")
            except Exception as e:
                logger.error(f"Error loading entity: {entity_file}", exc_info=True)
        
        return True
    
    def _load_relationships(self, relationship_type: str) -> bool:
        """
        Load relationships of a type from disk.
        
        Args:
            relationship_type: Type of relationship
            
        Returns:
            Success
        """
        logger.info(f"Loading relationships of type: {relationship_type}")
        
        # Initialize relationship store
        self.relationships[relationship_type] = {}
        
        # Create the relationship directory if it doesn't exist
        relationship_dir = os.path.join(self.data_dir, "relationships", relationship_type)
        os.makedirs(relationship_dir, exist_ok=True)
        
        # Get all relationship files
        relationship_files = [f for f in os.listdir(relationship_dir) if f.endswith('.json')]
        
        # Load each relationship
        for relationship_file in relationship_files:
            try:
                relationship_path = os.path.join(relationship_dir, relationship_file)
                with open(relationship_path, 'r', encoding='utf-8') as f:
                    relationship = json.load(f)
                
                # Store the relationship
                self.relationships[relationship_type][relationship["id"]] = relationship
                
                logger.info(f"Loaded relationship from disk: {relationship_type}/{relationship['id']}")
            except Exception as e:
                logger.error(f"Error loading relationship: {relationship_file}", exc_info=True)
        
        return True
    
    def _delete_entity_relationships(self, entity_type: str, entity_id: str) -> bool:
        """
        Delete relationships involving an entity.
        
        Args:
            entity_type: Type of entity
            entity_id: Entity ID
            
        Returns:
            Success
        """
        # Get all relationships involving the entity
        relationships = self.get_entity_relationships(entity_type, entity_id)
        
        # Delete each relationship
        for relationship in relationships:
            self.delete_relationship(relationship["type"], relationship["id"])
        
        return True

class KnowledgeAPI:
    """
    Python wrapper for the JavaScript KnowledgeAPI.
    
    This class provides a Python interface to the JavaScript KnowledgeAPI.
    """
    
    def __init__(self, options: Optional[Dict[str, Any]] = None):
        """
        Initialize the Knowledge API.
        
        Args:
            options: API options
        """
        logger.info("Initializing Knowledge API (Python wrapper)")
        
        # Create a knowledge manager
        self.knowledge_manager = KnowledgeManager(options.get('data_dir') if options else None)
        
        logger.info("Knowledge API initialized")
    
    def get_schema(self) -> Dict[str, Any]:
        """
        Get the knowledge schema.
        
        Returns:
            The knowledge schema
        """
        return self.knowledge_manager.get_schema()
    
    def get_entity_types(self) -> List[Dict[str, Any]]:
        """
        Get entity types.
        
        Returns:
            Entity types
        """
        return self.knowledge_manager.get_entity_types()
    
    def get_relationship_types(self) -> List[Dict[str, Any]]:
        """
        Get relationship types.
        
        Returns:
            Relationship types
        """
        return self.knowledge_manager.get_relationship_types()
    
    def create_entity(self, entity_type: str, entity_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an entity.
        
        Args:
            entity_type: Type of entity
            entity_data: Entity data
            
        Returns:
            Created entity
        """
        return self.knowledge_manager.create_entity(entity_type, entity_data)
    
    def get_entity(self, entity_type: str, entity_id: str) -> Optional[Dict[str, Any]]:
        """
        Get an entity by ID.
        
        Args:
            entity_type: Type of entity
            entity_id: Entity ID
            
        Returns:
            Entity
        """
        return self.knowledge_manager.get_entity(entity_type, entity_id)
    
    def get_entities(self, entity_type: str) -> List[Dict[str, Any]]:
        """
        Get all entities of a type.
        
        Args:
            entity_type: Type of entity
            
        Returns:
            Entities
        """
        return self.knowledge_manager.get_entities(entity_type)
    
    def update_entity(self, entity_type: str, entity_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an entity.
        
        Args:
            entity_type: Type of entity
            entity_id: Entity ID
            updates: Updates to apply
            
        Returns:
            Updated entity
        """
        return self.knowledge_manager.update_entity(entity_type, entity_id, updates)
    
    def delete_entity(self, entity_type: str, entity_id: str) -> bool:
        """
        Delete an entity.
        
        Args:
            entity_type: Type of entity
            entity_id: Entity ID
            
        Returns:
            Success
        """
        return self.knowledge_manager.delete_entity(entity_type, entity_id)
    
    def create_relationship(self, relationship_type: str, source_type: str, source_id: str, target_type: str, target_id: str, properties: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create a relationship between entities.
        
        Args:
            relationship_type: Type of relationship
            source_type: Type of source entity
            source_id: ID of source entity
            target_type: Type of target entity
            target_id: ID of target entity
            properties: Relationship properties
            
        Returns:
            Created relationship
        """
        return self.knowledge_manager.create_relationship(relationship_type, source_type, source_id, target_type, target_id, properties)
    
    def get_entity_relationships(self, entity_type: str, entity_id: str, relationship_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get relationships for an entity.
        
        Args:
            entity_type: Type of entity
            entity_id: Entity ID
            relationship_type: Type of relationship (optional)
            
        Returns:
            Relationships
        """
        return self.knowledge_manager.get_entity_relationships(entity_type, entity_id, relationship_type)
    
    def delete_relationship(self, relationship_type: str, relationship_id: str) -> bool:
        """
        Delete a relationship.
        
        Args:
            relationship_type: Type of relationship
            relationship_id: Relationship ID
            
        Returns:
            Success
        """
        return self.knowledge_manager.delete_relationship(relationship_type, relationship_id)
    
    def search(self, query: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Search the knowledge base.
        
        Args:
            query: Search query
            options: Search options
            
        Returns:
            Search results
        """
        return self.knowledge_manager.search(query, options)
    
    def get_frameworks(self) -> List[Dict[str, Any]]:
        """
        Get frameworks.
        
        Returns:
            Frameworks
        """
        return self.knowledge_manager.get_entities("framework")
    
    def get_framework(self, framework_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a framework by ID.
        
        Args:
            framework_id: Framework ID
            
        Returns:
            Framework
        """
        return self.knowledge_manager.get_entity("framework", framework_id)
    
    def get_framework_controls(self, framework_id: str) -> List[Dict[str, Any]]:
        """
        Get controls for a framework.
        
        Args:
            framework_id: Framework ID
            
        Returns:
            Controls
        """
        controls = self.knowledge_manager.get_entities("control")
        return [c for c in controls if c.get("framework_id") == framework_id]
    
    def get_control(self, control_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a control by ID.
        
        Args:
            control_id: Control ID
            
        Returns:
            Control
        """
        return self.knowledge_manager.get_entity("control", control_id)
    
    def get_control_guidance(self, control_id: str) -> List[Dict[str, Any]]:
        """
        Get guidance for a control.
        
        Args:
            control_id: Control ID
            
        Returns:
            Guidance
        """
        guidance = self.knowledge_manager.get_entities("guidance")
        return [g for g in guidance if g.get("control_id") == control_id]
    
    def get_control_resources(self, control_id: str) -> List[Dict[str, Any]]:
        """
        Get resources for a control.
        
        Args:
            control_id: Control ID
            
        Returns:
            Resources
        """
        resources = self.knowledge_manager.get_entities("resource")
        return [r for r in resources if r.get("control_id") == control_id]
    
    def get_glossary_terms(self) -> List[Dict[str, Any]]:
        """
        Get glossary terms.
        
        Returns:
            Glossary terms
        """
        return self.knowledge_manager.get_entities("glossary_term")
    
    def get_faqs(self) -> List[Dict[str, Any]]:
        """
        Get FAQs.
        
        Returns:
            FAQs
        """
        return self.knowledge_manager.get_entities("faq")
