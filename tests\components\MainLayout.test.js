import React from 'react';
import { render, screen } from '@testing-library/react';
import MainLayout from '../../components/MainLayout';

// Mock the Navigation component
jest.mock('../../components/Navigation', () => {
  return function MockNavigation() {
    return <nav data-testid="mock-navigation">Mock Navigation</nav>;
  };
});

// Mock the FloatingNovaConcierge component
jest.mock('../../components/FloatingNovaConcierge', () => {
  return function MockFloatingNovaConcierge() {
    return <div data-testid="mock-nova-concierge">Mock NovaConcierge</div>;
  };
});

// Mock the NavigationContext
jest.mock('../../components/NavigationContext', () => ({
  useNavigation: jest.fn().mockReturnValue({
    isNavigationRendered: true,
    setIsNavigationRendered: jest.fn()
  })
}));

// Mock the Next.js Head component
jest.mock('next/head', () => {
  return function MockHead({ children }) {
    return <div data-testid="mock-head">{children}</div>;
  };
});

describe('MainLayout', () => {
  it('renders children correctly', () => {
    render(
      <MainLayout>
        <div data-testid="test-content">Test Content</div>
      </MainLayout>
    );
    
    // Check if the content is rendered
    expect(screen.getByTestId('test-content')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
    
    // Check if the Navigation component is rendered
    expect(screen.getByTestId('mock-navigation')).toBeInTheDocument();
    
    // Check if the NovaConcierge component is rendered
    expect(screen.getByTestId('mock-nova-concierge')).toBeInTheDocument();
  });
  
  it('renders with default title', () => {
    render(
      <MainLayout>
        <div>Content</div>
      </MainLayout>
    );
    
    // Check if the Head component is rendered
    const head = screen.getByTestId('mock-head');
    expect(head).toBeInTheDocument();
    
    // Check if the main container has the correct classes
    const mainContainer = screen.getByText('Content').closest('main');
    expect(mainContainer).toHaveClass('container');
    expect(mainContainer).toHaveClass('mx-auto');
  });
  
  it('renders with custom title', () => {
    render(
      <MainLayout title="Custom Title">
        <div>Content</div>
      </MainLayout>
    );
    
    // Check if the Head component is rendered
    const head = screen.getByTestId('mock-head');
    expect(head).toBeInTheDocument();
    
    // Check if the footer is rendered
    expect(screen.getByText('© 2025 NovaFuse. All rights reserved.')).toBeInTheDocument();
    
    // Check if the footer links are rendered
    expect(screen.getByText('Terms')).toBeInTheDocument();
    expect(screen.getByText('Privacy')).toBeInTheDocument();
    expect(screen.getByText('Contact')).toBeInTheDocument();
  });
});
