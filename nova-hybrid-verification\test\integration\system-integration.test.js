/**
 * Integration tests for the Hybrid DAG-based Zero-Knowledge System
 * 
 * These tests verify that the different components of the system work together correctly.
 */

// Import the system
const { DAGSystem } = require('../../src');

describe('System Integration', () => {
  let system;
  
  beforeEach(() => {
    system = new DAGSystem({
      enableLogging: false,
      enableMetrics: true
    });
  });
  
  test('should process a transaction and verify its proof', async () => {
    // Create a transaction
    const transaction = system.createTransaction({
      data: {
        action: 'test_action',
        entity: 'test_entity',
        timestamp: Date.now()
      },
      type: 'test',
      metadata: {
        priority: 'high',
        source: 'integration_test'
      }
    });
    
    // Process the transaction
    const processingResult = await system.processTransaction(transaction);
    
    // Verify the processing result
    expect(processingResult).toBeDefined();
    expect(processingResult.transactionId).toBe(transaction.id);
    expect(processingResult.status).toBe('processed');
    expect(processingResult.proof).toBeDefined();
    
    // Verify the proof
    const verificationResult = await system.verifyProof(processingResult.proof);
    
    // Verify the verification result
    expect(verificationResult).toBeDefined();
    expect(verificationResult.proofId).toBe(processingResult.proof.proofId);
    expect(verificationResult.status).toBe('verified');
    expect(verificationResult.verified).toBe(true);
  });
  
  test('should handle a batch of transactions', async () => {
    const batchSize = 5;
    const transactions = [];
    const processingResults = [];
    
    // Create and process transactions
    for (let i = 0; i < batchSize; i++) {
      const transaction = system.createTransaction({
        data: {
          action: 'batch_test',
          index: i,
          timestamp: Date.now()
        },
        type: 'batch_test',
        metadata: {
          batchId: `batch-${Date.now()}`,
          index: i
        }
      });
      
      transactions.push(transaction);
      
      const result = await system.processTransaction(transaction);
      processingResults.push(result);
    }
    
    // Verify all transactions were processed
    expect(processingResults.length).toBe(batchSize);
    expect(processingResults.every(result => result.status === 'processed')).toBe(true);
    
    // Verify all proofs
    const verificationResults = await Promise.all(
      processingResults.map(result => system.verifyProof(result.proof))
    );
    
    // Verify all verifications succeeded
    expect(verificationResults.length).toBe(batchSize);
    expect(verificationResults.every(result => result.verified)).toBe(true);
    
    // Check metrics
    const metrics = system.getMetrics();
    expect(metrics.transactions.total).toBeGreaterThanOrEqual(batchSize);
    expect(metrics.transactions.processed).toBeGreaterThanOrEqual(batchSize);
    expect(metrics.proofs.total).toBeGreaterThanOrEqual(batchSize);
    expect(metrics.proofs.verified).toBeGreaterThanOrEqual(batchSize);
  });
  
  test('should handle cross-layer communication', async () => {
    // Create a transaction that should be escalated to Meso layer
    const transaction = system.createTransaction({
      data: {
        action: 'complex_action',
        entity: 'test_entity',
        timestamp: Date.now()
      },
      type: 'complex',
      metadata: {
        complex: true,
        crossDomain: true,
        requiresConsensus: true
      }
    });
    
    // Process the transaction
    const processingResult = await system.processTransaction(transaction);
    
    // Verify the processing result
    expect(processingResult).toBeDefined();
    expect(processingResult.transactionId).toBe(transaction.id);
    
    // The actual status depends on the implementation details
    // In a real system, we would check for specific cross-layer behavior
    // For this test, we just verify that processing completed
    expect(processingResult.status).toBe('processed');
  });
  
  test('should maintain DAG structure', async () => {
    // Create parent transaction
    const parentTransaction = system.createTransaction({
      data: {
        action: 'parent_action',
        timestamp: Date.now()
      },
      type: 'parent'
    });
    
    // Process parent transaction
    const parentResult = await system.processTransaction(parentTransaction);
    
    // Create child transaction referencing parent
    const childTransaction = system.createTransaction({
      data: {
        action: 'child_action',
        timestamp: Date.now()
      },
      type: 'child',
      parents: [parentTransaction.id]
    });
    
    // Process child transaction
    const childResult = await system.processTransaction(childTransaction);
    
    // Verify both transactions were processed
    expect(parentResult.status).toBe('processed');
    expect(childResult.status).toBe('processed');
    
    // Verify DAG structure
    const parentNode = system.dag.getNode(parentTransaction.id);
    const childNode = system.dag.getNode(childTransaction.id);
    
    expect(parentNode).toBeDefined();
    expect(childNode).toBeDefined();
    
    // In a real implementation, we would verify the parent-child relationship
    // For this test, we just verify that both nodes exist in the DAG
  });
});
