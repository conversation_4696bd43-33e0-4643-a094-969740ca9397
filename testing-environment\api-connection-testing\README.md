# NovaConnect API Connection Testing

This framework provides rigorous testing for the NovaConnect Universal API Connector's ability to connect to external APIs.

## Overview

The API Connection Testing framework tests the following aspects of the Universal API Connector:

- **Authentication**: Tests various authentication methods (API Key, Basic Auth, OAuth2, JWT, AWS SigV4, Custom)
- **Data Transformation**: Tests the connector's ability to transform data from external APIs
- **Error Handling**: Tests the connector's ability to handle various error scenarios
- **Parameter Handling**: Tests the connector's ability to handle different parameter types
- **Rate Limiting**: Tests the connector's ability to handle rate limiting

## Test Environment

The test environment consists of:

- **Mock API Server**: Simulates various API behaviors
- **Connector Registry**: Manages connector definitions
- **Authentication Service**: Manages credentials
- **Connector Executor**: Executes connector operations
- **Usage Metering**: Tracks API usage

## Running Tests

### Prerequisites

- Node.js 14+
- npm 6+

### Installation

```bash
cd testing-environment
npm install
```

### Running All API Connection Tests

```bash
npm run test:api-connection
```

### Running Specific Test Categories

```bash
npm run test:api-auth         # Run authentication tests
npm run test:api-transform    # Run data transformation tests
npm run test:api-errors       # Run error handling tests
npm run test:api-params       # Run parameter handling tests
npm run test:api-rate-limit   # Run rate limiting tests
```

## Test Structure

The tests are organized by category:

```
api-connection-testing/
  ├── connectors/              # Test connector definitions
  │   ├── api-key-connector.json
  │   ├── basic-auth-connector.json
  │   ├── oauth2-connector.json
  │   ├── jwt-connector.json
  │   ├── aws-sigv4-connector.json
  │   ├── custom-auth-connector.json
  │   ├── data-transformation-connector.json
  │   ├── error-handling-connector.json
  │   ├── parameter-handling-connector.json
  │   └── rate-limiting-connector.json
  ├── mock-api/                # Mock API server
  │   └── server.js
  ├── tests/                   # Test files
  │   ├── authentication.test.js
  │   ├── data-transformation.test.js
  │   ├── error-handling.test.js
  │   ├── parameter-handling.test.js
  │   └── rate-limiting.test.js
  ├── setup.js                 # Test environment setup
  └── run-tests.js             # Test runner
```

## Test Categories

### Authentication Tests

Tests the connector's ability to authenticate with various authentication methods:

- **API Key**: Tests API Key authentication
- **Basic Auth**: Tests Basic authentication
- **OAuth2**: Tests OAuth2 authentication
- **JWT**: Tests JWT authentication
- **AWS SigV4**: Tests AWS SigV4 authentication
- **Custom Auth**: Tests custom authentication methods

### Data Transformation Tests

Tests the connector's ability to transform data from external APIs:

- **Basic Transformation**: Tests basic data transformation
- **Array Handling**: Tests handling of arrays in the response
- **Nested Object Handling**: Tests handling of nested objects in the response
- **Transformation with Parameters**: Tests transformations with parameters

### Error Handling Tests

Tests the connector's ability to handle various error scenarios:

- **HTTP Errors**: Tests handling of HTTP error codes (400, 401, 403, 404, 429, 500)
- **Timeout Handling**: Tests handling of request timeouts
- **Malformed Response Handling**: Tests handling of malformed JSON responses
- **Retry Policy**: Tests the retry policy for failed requests
- **Error Reporting**: Tests the error reporting mechanism

### Parameter Handling Tests

Tests the connector's ability to handle different parameter types:

- **Query Parameters**: Tests handling of query parameters
- **Path Parameters**: Tests handling of path parameters
- **Body Parameters**: Tests handling of body parameters
- **Parameter Validation**: Tests validation of parameter types
- **Parameter Combinations**: Tests combinations of parameter types

### Rate Limiting Tests

Tests the connector's ability to handle rate limiting:

- **Rate Limiting**: Tests handling of rate-limited endpoints
- **Rate Limit Configuration**: Tests the connector's rate limit configuration
- **Rate Limit Backoff**: Tests exponential backoff for rate-limited requests

## Mock API Server

The mock API server simulates various API behaviors for testing the Universal API Connector:

- **Authentication Endpoints**: Endpoints for testing different authentication methods
- **Error Endpoints**: Endpoints that return different error codes
- **Data Transformation Endpoints**: Endpoints that return data for transformation testing
- **Parameter Handling Endpoints**: Endpoints for testing parameter handling
- **Rate Limiting Endpoints**: Endpoints that implement rate limiting

## Extending the Tests

To add new tests:

1. Create a new connector definition in the `connectors` directory
2. Add test cases to the appropriate test file in the `tests` directory
3. Update the `run-tests.js` file if necessary

## Troubleshooting

### Common Issues

- **Service Startup Failures**: Check that all required ports are available
- **Authentication Failures**: Verify that the credentials match the expected values
- **Timeout Errors**: Increase the timeout in the connector configuration
- **Rate Limiting Errors**: Reduce the number of requests or increase the rate limit

### Getting Help

If you encounter issues with the API connection testing framework, please contact the NovaConnect development team.
