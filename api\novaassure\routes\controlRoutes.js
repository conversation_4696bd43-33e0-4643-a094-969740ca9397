/**
 * Control Routes
 * 
 * This file defines the routes for control management.
 */

const express = require('express');
const router = express.Router();
const controlController = require('../controllers/controlController');
const authMiddleware = require('../middleware/authMiddleware');

/**
 * @swagger
 * /api/v1/novaassure/controls:
 *   get:
 *     summary: Get all controls
 *     description: Retrieve a list of all controls
 *     tags: [Controls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: framework
 *         schema:
 *           type: string
 *         description: Filter by framework (e.g., soc2, gdpr, hipaa)
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *     responses:
 *       200:
 *         description: List of controls
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', authMiddleware.authenticate, controlController.getAllControls);

/**
 * @swagger
 * /api/v1/novaassure/controls/{id}:
 *   get:
 *     summary: Get control by ID
 *     description: Retrieve a control by its ID
 *     tags: [Controls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Control ID
 *     responses:
 *       200:
 *         description: Control details
 *       404:
 *         description: Control not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/:id', authMiddleware.authenticate, controlController.getControlById);

/**
 * @swagger
 * /api/v1/novaassure/controls:
 *   post:
 *     summary: Create control
 *     description: Create a new control
 *     tags: [Controls]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - description
 *               - framework
 *             properties:
 *               name:
 *                 type: string
 *                 description: Control name
 *               description:
 *                 type: string
 *                 description: Control description
 *               framework:
 *                 type: string
 *                 description: Framework (e.g., soc2, gdpr, hipaa)
 *               category:
 *                 type: string
 *                 description: Control category
 *               requirements:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Control requirements
 *               testProcedures:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Test procedures
 *     responses:
 *       201:
 *         description: Control created
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/', authMiddleware.authenticate, controlController.createControl);

/**
 * @swagger
 * /api/v1/novaassure/controls/{id}:
 *   put:
 *     summary: Update control
 *     description: Update an existing control
 *     tags: [Controls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Control ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Control name
 *               description:
 *                 type: string
 *                 description: Control description
 *               framework:
 *                 type: string
 *                 description: Framework (e.g., soc2, gdpr, hipaa)
 *               category:
 *                 type: string
 *                 description: Control category
 *               requirements:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Control requirements
 *               testProcedures:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Test procedures
 *     responses:
 *       200:
 *         description: Control updated
 *       400:
 *         description: Invalid request
 *       404:
 *         description: Control not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.put('/:id', authMiddleware.authenticate, controlController.updateControl);

/**
 * @swagger
 * /api/v1/novaassure/controls/{id}:
 *   delete:
 *     summary: Delete control
 *     description: Delete an existing control
 *     tags: [Controls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Control ID
 *     responses:
 *       200:
 *         description: Control deleted
 *       404:
 *         description: Control not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.delete('/:id', authMiddleware.authenticate, controlController.deleteControl);

/**
 * @swagger
 * /api/v1/novaassure/controls/import:
 *   post:
 *     summary: Import controls
 *     description: Import controls from a file
 *     tags: [Controls]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Control file (CSV, JSON, or Excel)
 *     responses:
 *       200:
 *         description: Controls imported
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/import', authMiddleware.authenticate, controlController.importControls);

/**
 * @swagger
 * /api/v1/novaassure/controls/export:
 *   get:
 *     summary: Export controls
 *     description: Export controls to a file
 *     tags: [Controls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: framework
 *         schema:
 *           type: string
 *         description: Filter by framework (e.g., soc2, gdpr, hipaa)
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [csv, json, excel]
 *           default: json
 *         description: Export format
 *     responses:
 *       200:
 *         description: Controls exported
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/export', authMiddleware.authenticate, controlController.exportControls);

/**
 * @swagger
 * /api/v1/novaassure/controls/mapping:
 *   get:
 *     summary: Get control mapping
 *     description: Get control mapping across frameworks
 *     tags: [Controls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: sourceFramework
 *         schema:
 *           type: string
 *         required: true
 *         description: Source framework (e.g., soc2, gdpr, hipaa)
 *       - in: query
 *         name: targetFramework
 *         schema:
 *           type: string
 *         required: true
 *         description: Target framework (e.g., soc2, gdpr, hipaa)
 *     responses:
 *       200:
 *         description: Control mapping
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/mapping', authMiddleware.authenticate, controlController.getControlMapping);

module.exports = router;
