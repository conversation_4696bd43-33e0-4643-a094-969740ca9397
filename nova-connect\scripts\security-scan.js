/**
 * Security Scanning Script for NovaConnect UAC
 * 
 * This script performs security scanning on the NovaConnect UAC codebase using various security tools.
 * It checks for vulnerabilities in dependencies, code quality issues, and security best practices.
 * 
 * Usage:
 *   node security-scan.js [options]
 * 
 * Options:
 *   --fix           Automatically fix issues where possible
 *   --report-only   Only generate reports without failing on issues
 *   --output <dir>  Directory to output reports (default: ./security-reports)
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  fix: args.includes('--fix'),
  reportOnly: args.includes('--report-only'),
  output: './security-reports'
};

// Get output directory from arguments
const outputIndex = args.indexOf('--output');
if (outputIndex !== -1 && args[outputIndex + 1]) {
  options.output = args[outputIndex + 1];
}

// Create output directory if it doesn't exist
if (!fs.existsSync(options.output)) {
  fs.mkdirSync(options.output, { recursive: true });
}

// Log configuration
console.log('Security Scan Configuration:');
console.log(`- Fix issues: ${options.fix}`);
console.log(`- Report only: ${options.reportOnly}`);
console.log(`- Output directory: ${options.output}`);
console.log('');

// Track overall status
let hasIssues = false;

/**
 * Run a command and handle errors
 * 
 * @param {string} command - Command to run
 * @param {string} errorMessage - Error message to display on failure
 * @returns {string} - Command output
 */
function runCommand(command, errorMessage) {
  try {
    return execSync(command, { encoding: 'utf8' });
  } catch (error) {
    if (options.reportOnly) {
      console.error(`${errorMessage}:`);
      console.error(error.stdout || error.message);
      hasIssues = true;
      return error.stdout || error.message;
    } else {
      console.error(`${errorMessage}:`);
      console.error(error.stdout || error.message);
      process.exit(1);
    }
  }
}

/**
 * Save report to file
 * 
 * @param {string} name - Report name
 * @param {string} content - Report content
 */
function saveReport(name, content) {
  const reportPath = path.join(options.output, `${name}.txt`);
  fs.writeFileSync(reportPath, content);
  console.log(`Report saved to ${reportPath}`);
}

// Run npm audit to check for vulnerable dependencies
console.log('Running npm audit...');
const auditCommand = options.fix ? 'npm audit fix --json' : 'npm audit --json';
const auditOutput = runCommand(auditCommand, 'Vulnerable dependencies found');
const auditReport = JSON.parse(auditOutput);
const vulnerabilitiesCount = auditReport.metadata?.vulnerabilities?.total || 0;

if (vulnerabilitiesCount > 0) {
  console.log(`Found ${vulnerabilitiesCount} vulnerabilities`);
  hasIssues = true;
} else {
  console.log('No vulnerabilities found');
}

saveReport('npm-audit', auditOutput);
console.log('');

// Run ESLint to check for code quality issues
console.log('Running ESLint...');
const eslintCommand = options.fix 
  ? 'npx eslint --fix --format json .'
  : 'npx eslint --format json .';
const eslintOutput = runCommand(eslintCommand, 'ESLint issues found');
const eslintReport = JSON.parse(eslintOutput);
const eslintIssuesCount = eslintReport.reduce((count, file) => count + file.errorCount + file.warningCount, 0);

if (eslintIssuesCount > 0) {
  console.log(`Found ${eslintIssuesCount} ESLint issues`);
  hasIssues = true;
} else {
  console.log('No ESLint issues found');
}

saveReport('eslint', eslintOutput);
console.log('');

// Run Snyk to check for security vulnerabilities
console.log('Running Snyk...');
const snykCommand = options.fix 
  ? 'npx snyk test --json --severity-threshold=medium'
  : 'npx snyk test --json --severity-threshold=medium';
const snykOutput = runCommand(snykCommand, 'Snyk vulnerabilities found');
const snykReport = JSON.parse(snykOutput);
const snykVulnerabilitiesCount = snykReport.vulnerabilities?.length || 0;

if (snykVulnerabilitiesCount > 0) {
  console.log(`Found ${snykVulnerabilitiesCount} Snyk vulnerabilities`);
  hasIssues = true;
} else {
  console.log('No Snyk vulnerabilities found');
}

saveReport('snyk', snykOutput);
console.log('');

// Run helmet-csp to check for Content Security Policy issues
console.log('Running helmet-csp check...');
const helmetOutput = runCommand('node scripts/check-helmet.js', 'Helmet CSP issues found');
saveReport('helmet-csp', helmetOutput);
console.log('');

// Run express-rate-limit check
console.log('Running rate limiting check...');
const rateLimitOutput = runCommand('node scripts/check-rate-limit.js', 'Rate limiting issues found');
saveReport('rate-limit', rateLimitOutput);
console.log('');

// Run JWT configuration check
console.log('Running JWT configuration check...');
const jwtOutput = runCommand('node scripts/check-jwt-config.js', 'JWT configuration issues found');
saveReport('jwt-config', jwtOutput);
console.log('');

// Run HTTPS redirect check
console.log('Running HTTPS redirect check...');
const httpsOutput = runCommand('node scripts/check-https-redirect.js', 'HTTPS redirect issues found');
saveReport('https-redirect', httpsOutput);
console.log('');

// Run secrets detection
console.log('Running secrets detection...');
const secretsOutput = runCommand('npx detect-secrets scan .', 'Potential secrets found in code');
saveReport('secrets-detection', secretsOutput);
console.log('');

// Generate security report summary
console.log('Generating security report summary...');
const summary = `
# NovaConnect UAC Security Scan Summary
Date: ${new Date().toISOString()}

## Vulnerabilities Summary
- npm audit: ${vulnerabilitiesCount} vulnerabilities
- ESLint: ${eslintIssuesCount} issues
- Snyk: ${snykVulnerabilitiesCount} vulnerabilities

## Recommendations
${hasIssues ? 'Issues were found during the security scan. Please review the detailed reports and address the issues.' : 'No major security issues were found.'}

## Next Steps
1. Review detailed reports in the ${options.output} directory
2. Address any critical or high severity issues
3. Run the security scan regularly as part of your CI/CD pipeline
`;

saveReport('summary', summary);

// Final output
if (hasIssues) {
  console.log('\nSecurity issues were found. Please review the reports in the output directory.');
  if (!options.reportOnly) {
    process.exit(1);
  }
} else {
  console.log('\nNo security issues were found.');
}
