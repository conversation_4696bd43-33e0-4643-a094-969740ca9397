const express = require("express");
const cors = require("cors");
const morgan = require("morgan");

const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan("dev"));

// Create router with prefix
const router = express.Router();
app.use('/apis', router);

// Direct health check route (no prefix)
app.get("/health", (req, res) => {
  res.json({ status: "healthy", timestamp: new Date().toISOString() });
});

// Sample data
const apiCatalog = [
  {
    id: "api-001",
    name: "User Management API",
    category: "identity",
    status: "active",
    version: "2.1.0",
    description: "API for managing user accounts, roles, and permissions",
    baseUrl: "https://api.example.com/users",
    documentation: "https://docs.example.com/apis/users",
    authType: "oauth2"
  },
  {
    id: "api-002",
    name: "Compliance Reporting API",
    category: "compliance",
    status: "active",
    version: "1.5.2",
    description: "API for generating and retrieving compliance reports",
    baseUrl: "https://api.example.com/compliance",
    documentation: "https://docs.example.com/apis/compliance",
    authType: "apikey"
  },
  {
    id: "api-003",
    name: "Risk Assessment API",
    category: "risk",
    status: "beta",
    version: "0.9.1",
    description: "API for performing risk assessments and retrieving results",
    baseUrl: "https://api.example.com/risk",
    documentation: "https://docs.example.com/apis/risk",
    authType: "oauth2"
  },
  {
    id: "api-004",
    name: "Legacy Audit API",
    category: "audit",
    status: "deprecated",
    version: "1.0.0",
    description: "Legacy API for audit logs (replaced by Audit Events API)",
    baseUrl: "https://api.example.com/audit-legacy",
    documentation: "https://docs.example.com/apis/audit-legacy",
    authType: "basic"
  },
  {
    id: "api-005",
    name: "Audit Events API",
    category: "audit",
    status: "active",
    version: "2.0.0",
    description: "Modern API for audit events and logs",
    baseUrl: "https://api.example.com/audit",
    documentation: "https://docs.example.com/apis/audit",
    authType: "oauth2"
  }
];

const apiMetrics = [
  {
    apiId: "api-001",
    period: "day",
    date: "2024-10-15",
    requests: 12500,
    avgResponseTime: 120,
    errors: 75,
    uniqueUsers: 450
  },
  {
    apiId: "api-001",
    period: "week",
    date: "2024-10-15",
    requests: 87500,
    avgResponseTime: 125,
    errors: 520,
    uniqueUsers: 1200
  },
  {
    apiId: "api-001",
    period: "month",
    date: "2024-10-15",
    requests: 375000,
    avgResponseTime: 130,
    errors: 2250,
    uniqueUsers: 3500
  },
  {
    apiId: "api-002",
    period: "day",
    date: "2024-10-15",
    requests: 5000,
    avgResponseTime: 200,
    errors: 25,
    uniqueUsers: 120
  },
  {
    apiId: "api-002",
    period: "week",
    date: "2024-10-15",
    requests: 35000,
    avgResponseTime: 210,
    errors: 175,
    uniqueUsers: 350
  },
  {
    apiId: "api-002",
    period: "month",
    date: "2024-10-15",
    requests: 150000,
    avgResponseTime: 215,
    errors: 750,
    uniqueUsers: 900
  }
];

const integrationFlows = [
  {
    id: "flow-001",
    name: "User Onboarding Flow",
    category: "identity",
    status: "active",
    description: "Integration flow for onboarding new users",
    steps: [
      { id: "step-001", name: "Create user in IdP", type: "api", target: "api-001" },
      { id: "step-002", name: "Assign default roles", type: "api", target: "api-001" },
      { id: "step-003", name: "Send welcome email", type: "email", target: "email-service" }
    ],
    createdBy: "admin",
    createdAt: "2024-08-15T10:30:00Z",
    lastRun: "2024-10-14T15:45:00Z",
    runCount: 250
  },
  {
    id: "flow-002",
    name: "Compliance Report Generation",
    category: "compliance",
    status: "active",
    description: "Integration flow for generating compliance reports",
    steps: [
      { id: "step-004", name: "Collect audit logs", type: "api", target: "api-005" },
      { id: "step-005", name: "Generate compliance report", type: "api", target: "api-002" },
      { id: "step-006", name: "Store report in document management", type: "api", target: "document-api" },
      { id: "step-007", name: "Notify compliance team", type: "email", target: "email-service" }
    ],
    createdBy: "compliance-admin",
    createdAt: "2024-09-01T09:15:00Z",
    lastRun: "2024-10-15T01:00:00Z",
    runCount: 45
  },
  {
    id: "flow-003",
    name: "Risk Assessment Workflow",
    category: "risk",
    status: "draft",
    description: "Integration flow for risk assessment workflow",
    steps: [
      { id: "step-008", name: "Collect asset data", type: "api", target: "asset-api" },
      { id: "step-009", name: "Perform risk assessment", type: "api", target: "api-003" },
      { id: "step-010", name: "Generate risk report", type: "transform", target: null },
      { id: "step-011", name: "Store risk report", type: "api", target: "document-api" }
    ],
    createdBy: "risk-admin",
    createdAt: "2024-10-10T14:20:00Z",
    lastRun: null,
    runCount: 0
  }
];

const apiKeys = [
  {
    id: "key-001",
    name: "Production API Key",
    key: "prd_api_key_12345",
    status: "active",
    createdAt: "2024-07-15T10:00:00Z",
    expiresAt: "2025-07-15T10:00:00Z",
    lastUsed: "2024-10-15T09:30:00Z",
    allowedIps: ["***********", "***********"],
    allowedApis: ["api-001", "api-002", "api-005"]
  },
  {
    id: "key-002",
    name: "Development API Key",
    key: "dev_api_key_67890",
    status: "active",
    createdAt: "2024-08-20T14:30:00Z",
    expiresAt: "2025-08-20T14:30:00Z",
    lastUsed: "2024-10-14T16:45:00Z",
    allowedIps: ["***********"],
    allowedApis: ["api-001", "api-002", "api-003", "api-005"]
  },
  {
    id: "key-003",
    name: "Legacy API Key",
    key: "legacy_api_key_54321",
    status: "revoked",
    createdAt: "2023-05-10T09:15:00Z",
    expiresAt: "2024-05-10T09:15:00Z",
    lastUsed: "2024-05-01T11:20:00Z",
    allowedIps: [],
    allowedApis: ["api-004"]
  }
];

const developerResources = [
  {
    id: "res-001",
    type: "documentation",
    title: "API Integration Guide",
    description: "Comprehensive guide for integrating with our APIs",
    url: "https://docs.example.com/guides/api-integration",
    apiId: null,
    tags: ["getting-started", "integration"]
  },
  {
    id: "res-002",
    type: "sample",
    title: "User Management API Sample",
    description: "Sample code for the User Management API",
    url: "https://docs.example.com/samples/user-management",
    apiId: "api-001",
    tags: ["code-sample", "user-management"]
  },
  {
    id: "res-003",
    type: "tutorial",
    title: "Building Your First Integration Flow",
    description: "Step-by-step tutorial for creating integration flows",
    url: "https://docs.example.com/tutorials/first-integration-flow",
    apiId: null,
    tags: ["tutorial", "integration-flow"]
  },
  {
    id: "res-004",
    type: "documentation",
    title: "Authentication Guide",
    description: "Guide for authenticating with our APIs",
    url: "https://docs.example.com/guides/authentication",
    apiId: null,
    tags: ["authentication", "security"]
  },
  {
    id: "res-005",
    type: "sample",
    title: "Compliance Reporting API Sample",
    description: "Sample code for the Compliance Reporting API",
    url: "https://docs.example.com/samples/compliance-reporting",
    apiId: "api-002",
    tags: ["code-sample", "compliance"]
  }
];

// Routes

// API Catalog
router.get("/catalog", (req, res) => {
  let result = [...apiCatalog];

  // Apply filters
  if (req.query.category) {
    result = result.filter(api => api.category === req.query.category);
  }

  if (req.query.status) {
    result = result.filter(api => api.status === req.query.status);
  }

  res.json({
    data: result,
    total: result.length,
    page: 1,
    limit: result.length
  });
});

router.get("/catalog/:id", (req, res) => {
  const api = apiCatalog.find(api => api.id === req.params.id);

  if (!api) {
    return res.status(404).json({ error: "API not found" });
  }

  res.json({ data: api });
});

// API Metrics
router.get("/metrics", (req, res) => {
  let result = [...apiMetrics];

  // Apply filters
  if (req.query.api_id) {
    result = result.filter(metric => metric.apiId === req.query.api_id);
  }

  if (req.query.period) {
    result = result.filter(metric => metric.period === req.query.period);
  }

  res.json({
    data: result,
    total: result.length,
    page: 1,
    limit: result.length
  });
});

// Integration Flows
router.get("/integration/flows", (req, res) => {
  let result = [...integrationFlows];

  // Apply filters
  if (req.query.category) {
    result = result.filter(flow => flow.category === req.query.category);
  }

  if (req.query.status) {
    result = result.filter(flow => flow.status === req.query.status);
  }

  res.json({
    data: result,
    total: result.length,
    page: 1,
    limit: result.length
  });
});

router.get("/integration/flows/:id", (req, res) => {
  const flow = integrationFlows.find(flow => flow.id === req.params.id);

  if (!flow) {
    return res.status(404).json({ error: "Integration flow not found" });
  }

  res.json({ data: flow });
});

// API Keys
router.get("/keys", (req, res) => {
  let result = [...apiKeys];

  // Apply filters
  if (req.query.status) {
    result = result.filter(key => key.status === req.query.status);
  }

  // Remove sensitive information
  result = result.map(key => {
    const { key: apiKey, ...rest } = key;
    return {
      ...rest,
      key: apiKey.substring(0, 8) + "..." + apiKey.substring(apiKey.length - 4)
    };
  });

  res.json({
    data: result,
    total: result.length,
    page: 1,
    limit: result.length
  });
});

// Developer Resources
router.get("/resources", (req, res) => {
  let result = [...developerResources];

  // Apply filters
  if (req.query.type) {
    result = result.filter(resource => resource.type === req.query.type);
  }

  if (req.query.api_id) {
    result = result.filter(resource => resource.apiId === req.query.api_id);
  }

  res.json({
    data: result,
    total: result.length,
    page: 1,
    limit: result.length
  });
});

// API Test endpoint
router.post("/test", (req, res) => {
  // Simulate an API test
  const testId = "test-" + Math.floor(Math.random() * 1000);
  const timestamp = new Date().toISOString();

  // Extract request details
  const { url, method, headers, body } = req.body;

  // Simulate a response
  const response = {
    status: 200,
    statusText: "OK",
    headers: {
      "content-type": "application/json",
      "x-request-id": testId
    },
    body: {
      success: true,
      message: "API test successful",
      data: {
        testId,
        timestamp
      }
    }
  };

  res.json({
    data: {
      testId,
      timestamp,
      request: {
        url: url || "https://api.example.com/test",
        method: method || "GET",
        headers: headers || { "content-type": "application/json" },
        body: body || {}
      },
      response,
      duration: Math.floor(Math.random() * 100) + 50, // Random duration between 50-150ms
      success: true
    }
  });
});

// Start server
app.listen(port, () => {
  console.log(`APIs mock API running on port ${port}`);
});
