"""
Learner Manager for the Universal Compliance Training Optimizer.

This module provides functionality for managing learners and their progress.
"""

import os
import json
import logging
import uuid
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LearnerManager:
    """
    Manager for learners and their progress.
    
    This class is responsible for tracking learners and their progress through
    compliance training programs.
    """
    
    def __init__(self, learners_dir: Optional[str] = None):
        """
        Initialize the Learner Manager.
        
        Args:
            learners_dir: Path to a directory for storing learner data
        """
        logger.info("Initializing Learner Manager")
        
        # Set the learners directory
        self.learners_dir = learners_dir or os.path.join(os.getcwd(), 'learner_data')
        
        # Create the learners directory if it doesn't exist
        os.makedirs(self.learners_dir, exist_ok=True)
        
        # Dictionary to store learners in memory
        self.learners: Dict[str, Dict[str, Any]] = {}
        
        # Dictionary to store progress in memory
        self.progress: Dict[str, Dict[str, Any]] = {}
        
        # Load learners and progress from disk
        self._load_data_from_disk()
        
        logger.info(f"Learner Manager initialized with {len(self.learners)} learners and {len(self.progress)} progress records")
    
    def create_learner(self, learner_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new learner.
        
        Args:
            learner_data: The learner data
            
        Returns:
            The created learner
            
        Raises:
            ValueError: If the learner data is invalid
        """
        logger.info("Creating new learner")
        
        # Validate the learner data
        self._validate_learner_data(learner_data)
        
        # Generate a unique learner ID
        learner_id = str(uuid.uuid4())
        
        # Create the learner object
        learner = {
            'id': learner_id,
            'name': learner_data['name'],
            'email': learner_data['email'],
            'role': learner_data.get('role', ''),
            'department': learner_data.get('department', ''),
            'created_at': self._get_current_timestamp(),
            'updated_at': self._get_current_timestamp()
        }
        
        # Store the learner in memory
        self.learners[learner_id] = learner
        
        # Store the learner on disk
        self._save_learner_to_disk(learner)
        
        logger.info(f"Learner created: {learner_id}")
        
        return learner
    
    def get_learner(self, learner_id: str) -> Dict[str, Any]:
        """
        Get a learner.
        
        Args:
            learner_id: The ID of the learner
            
        Returns:
            The learner
            
        Raises:
            ValueError: If the learner does not exist
        """
        logger.info(f"Getting learner: {learner_id}")
        
        if learner_id not in self.learners:
            raise ValueError(f"Learner not found: {learner_id}")
        
        return self.learners[learner_id]
    
    def update_learner(self, learner_id: str, learner_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a learner.
        
        Args:
            learner_id: The ID of the learner
            learner_data: The updated learner data
            
        Returns:
            The updated learner
            
        Raises:
            ValueError: If the learner does not exist
        """
        logger.info(f"Updating learner: {learner_id}")
        
        # Check if the learner exists
        if learner_id not in self.learners:
            raise ValueError(f"Learner not found: {learner_id}")
        
        # Get the existing learner
        learner = self.learners[learner_id]
        
        # Update the learner data
        if 'name' in learner_data:
            learner['name'] = learner_data['name']
        
        if 'email' in learner_data:
            learner['email'] = learner_data['email']
        
        if 'role' in learner_data:
            learner['role'] = learner_data['role']
        
        if 'department' in learner_data:
            learner['department'] = learner_data['department']
        
        # Update the updated_at timestamp
        learner['updated_at'] = self._get_current_timestamp()
        
        # Store the updated learner on disk
        self._save_learner_to_disk(learner)
        
        logger.info(f"Learner updated: {learner_id}")
        
        return learner
    
    def delete_learner(self, learner_id: str) -> None:
        """
        Delete a learner.
        
        Args:
            learner_id: The ID of the learner
            
        Raises:
            ValueError: If the learner does not exist
        """
        logger.info(f"Deleting learner: {learner_id}")
        
        # Check if the learner exists
        if learner_id not in self.learners:
            raise ValueError(f"Learner not found: {learner_id}")
        
        # Remove the learner from memory
        del self.learners[learner_id]
        
        # Remove the learner from disk
        self._delete_learner_from_disk(learner_id)
        
        # Remove any progress records for the learner
        progress_to_delete = []
        for progress_id, progress in self.progress.items():
            if progress.get('learner_id') == learner_id:
                progress_to_delete.append(progress_id)
        
        for progress_id in progress_to_delete:
            del self.progress[progress_id]
            self._delete_progress_from_disk(progress_id)
        
        logger.info(f"Learner deleted: {learner_id}")
    
    def get_all_learners(self) -> List[Dict[str, Any]]:
        """
        Get all learners.
        
        Returns:
            List of learners
        """
        logger.info("Getting all learners")
        
        return list(self.learners.values())
    
    def get_learners_by_role(self, role: str) -> List[Dict[str, Any]]:
        """
        Get all learners with a specific role.
        
        Args:
            role: The role
            
        Returns:
            List of learners with the specified role
        """
        logger.info(f"Getting learners with role: {role}")
        
        return [l for l in self.learners.values() if l.get('role') == role]
    
    def get_learners_by_department(self, department: str) -> List[Dict[str, Any]]:
        """
        Get all learners in a specific department.
        
        Args:
            department: The department
            
        Returns:
            List of learners in the specified department
        """
        logger.info(f"Getting learners in department: {department}")
        
        return [l for l in self.learners.values() if l.get('department') == department]
    
    def record_progress(self, progress_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Record learner progress.
        
        Args:
            progress_data: The progress data
            
        Returns:
            The created progress record
            
        Raises:
            ValueError: If the progress data is invalid
        """
        logger.info("Recording learner progress")
        
        # Validate the progress data
        self._validate_progress_data(progress_data)
        
        # Generate a unique progress ID
        progress_id = str(uuid.uuid4())
        
        # Create the progress object
        progress = {
            'id': progress_id,
            'learner_id': progress_data['learner_id'],
            'program_id': progress_data['program_id'],
            'module_id': progress_data.get('module_id'),
            'status': progress_data.get('status', 'in_progress'),
            'score': progress_data.get('score'),
            'completed_at': progress_data.get('completed_at'),
            'created_at': self._get_current_timestamp(),
            'updated_at': self._get_current_timestamp()
        }
        
        # Store the progress in memory
        self.progress[progress_id] = progress
        
        # Store the progress on disk
        self._save_progress_to_disk(progress)
        
        logger.info(f"Progress recorded: {progress_id}")
        
        return progress
    
    def get_learner_progress(self, learner_id: str, program_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get progress for a learner.
        
        Args:
            learner_id: The ID of the learner
            program_id: Optional ID of a specific program
            
        Returns:
            List of progress records for the learner
            
        Raises:
            ValueError: If the learner does not exist
        """
        logger.info(f"Getting progress for learner: {learner_id}")
        
        # Check if the learner exists
        if learner_id not in self.learners:
            raise ValueError(f"Learner not found: {learner_id}")
        
        # Filter progress records by learner ID and optionally by program ID
        if program_id:
            return [p for p in self.progress.values() if p.get('learner_id') == learner_id and p.get('program_id') == program_id]
        else:
            return [p for p in self.progress.values() if p.get('learner_id') == learner_id]
    
    def get_program_progress(self, program_id: str) -> List[Dict[str, Any]]:
        """
        Get progress for a program.
        
        Args:
            program_id: The ID of the program
            
        Returns:
            List of progress records for the program
        """
        logger.info(f"Getting progress for program: {program_id}")
        
        return [p for p in self.progress.values() if p.get('program_id') == program_id]
    
    def _validate_learner_data(self, learner_data: Dict[str, Any]) -> None:
        """
        Validate learner data.
        
        Args:
            learner_data: The learner data to validate
            
        Raises:
            ValueError: If the learner data is invalid
        """
        # Check required fields
        if 'name' not in learner_data:
            raise ValueError("Learner name is required")
        
        if 'email' not in learner_data:
            raise ValueError("Learner email is required")
    
    def _validate_progress_data(self, progress_data: Dict[str, Any]) -> None:
        """
        Validate progress data.
        
        Args:
            progress_data: The progress data to validate
            
        Raises:
            ValueError: If the progress data is invalid
        """
        # Check required fields
        if 'learner_id' not in progress_data:
            raise ValueError("Learner ID is required")
        
        if 'program_id' not in progress_data:
            raise ValueError("Program ID is required")
        
        # Check if the learner exists
        if progress_data['learner_id'] not in self.learners:
            raise ValueError(f"Learner not found: {progress_data['learner_id']}")
        
        # Validate status if provided
        if 'status' in progress_data:
            valid_statuses = ['not_started', 'in_progress', 'completed', 'failed']
            if progress_data['status'] not in valid_statuses:
                raise ValueError(f"Invalid status: {progress_data['status']}")
    
    def _load_data_from_disk(self) -> None:
        """Load learners and progress from disk."""
        # Load learners
        self._load_learners_from_disk()
        
        # Load progress
        self._load_progress_from_disk()
    
    def _load_learners_from_disk(self) -> None:
        """Load learners from disk."""
        try:
            # Create the learners directory if it doesn't exist
            learners_dir = os.path.join(self.learners_dir, 'learners')
            os.makedirs(learners_dir, exist_ok=True)
            
            # Get all JSON files in the learners directory
            learner_files = [f for f in os.listdir(learners_dir) if f.endswith('.json')]
            
            for learner_file in learner_files:
                try:
                    # Load the learner from disk
                    file_path = os.path.join(learners_dir, learner_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        learner = json.load(f)
                    
                    # Store the learner in memory
                    learner_id = learner.get('id')
                    
                    if learner_id:
                        self.learners[learner_id] = learner
                        logger.info(f"Loaded learner from disk: {learner_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load learner from {learner_file}: {e}")
            
            logger.info(f"Loaded {len(self.learners)} learners from disk")
        
        except Exception as e:
            logger.error(f"Failed to load learners from disk: {e}")
    
    def _load_progress_from_disk(self) -> None:
        """Load progress from disk."""
        try:
            # Create the progress directory if it doesn't exist
            progress_dir = os.path.join(self.learners_dir, 'progress')
            os.makedirs(progress_dir, exist_ok=True)
            
            # Get all JSON files in the progress directory
            progress_files = [f for f in os.listdir(progress_dir) if f.endswith('.json')]
            
            for progress_file in progress_files:
                try:
                    # Load the progress from disk
                    file_path = os.path.join(progress_dir, progress_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        progress = json.load(f)
                    
                    # Store the progress in memory
                    progress_id = progress.get('id')
                    
                    if progress_id:
                        self.progress[progress_id] = progress
                        logger.info(f"Loaded progress from disk: {progress_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load progress from {progress_file}: {e}")
            
            logger.info(f"Loaded {len(self.progress)} progress records from disk")
        
        except Exception as e:
            logger.error(f"Failed to load progress from disk: {e}")
    
    def _save_learner_to_disk(self, learner: Dict[str, Any]) -> None:
        """
        Save a learner to disk.
        
        Args:
            learner: The learner to save
        """
        try:
            # Get the learner ID
            learner_id = learner.get('id')
            
            if not learner_id:
                raise ValueError("Learner ID is missing")
            
            # Create the learners directory if it doesn't exist
            learners_dir = os.path.join(self.learners_dir, 'learners')
            os.makedirs(learners_dir, exist_ok=True)
            
            # Create the file path
            file_path = os.path.join(learners_dir, f"{learner_id}.json")
            
            # Save the learner to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(learner, f, indent=2)
            
            logger.info(f"Saved learner to disk: {learner_id}")
        
        except Exception as e:
            logger.error(f"Failed to save learner to disk: {e}")
    
    def _save_progress_to_disk(self, progress: Dict[str, Any]) -> None:
        """
        Save progress to disk.
        
        Args:
            progress: The progress to save
        """
        try:
            # Get the progress ID
            progress_id = progress.get('id')
            
            if not progress_id:
                raise ValueError("Progress ID is missing")
            
            # Create the progress directory if it doesn't exist
            progress_dir = os.path.join(self.learners_dir, 'progress')
            os.makedirs(progress_dir, exist_ok=True)
            
            # Create the file path
            file_path = os.path.join(progress_dir, f"{progress_id}.json")
            
            # Save the progress to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(progress, f, indent=2)
            
            logger.info(f"Saved progress to disk: {progress_id}")
        
        except Exception as e:
            logger.error(f"Failed to save progress to disk: {e}")
    
    def _delete_learner_from_disk(self, learner_id: str) -> None:
        """
        Delete a learner from disk.
        
        Args:
            learner_id: The ID of the learner
        """
        try:
            # Create the learners directory if it doesn't exist
            learners_dir = os.path.join(self.learners_dir, 'learners')
            os.makedirs(learners_dir, exist_ok=True)
            
            # Create the file path
            file_path = os.path.join(learners_dir, f"{learner_id}.json")
            
            # Check if the file exists
            if os.path.exists(file_path):
                # Delete the file
                os.remove(file_path)
                logger.info(f"Deleted learner from disk: {learner_id}")
            else:
                logger.warning(f"Learner file not found on disk: {learner_id}")
        
        except Exception as e:
            logger.error(f"Failed to delete learner from disk: {e}")
    
    def _delete_progress_from_disk(self, progress_id: str) -> None:
        """
        Delete progress from disk.
        
        Args:
            progress_id: The ID of the progress
        """
        try:
            # Create the progress directory if it doesn't exist
            progress_dir = os.path.join(self.learners_dir, 'progress')
            os.makedirs(progress_dir, exist_ok=True)
            
            # Create the file path
            file_path = os.path.join(progress_dir, f"{progress_id}.json")
            
            # Check if the file exists
            if os.path.exists(file_path):
                # Delete the file
                os.remove(file_path)
                logger.info(f"Deleted progress from disk: {progress_id}")
            else:
                logger.warning(f"Progress file not found on disk: {progress_id}")
        
        except Exception as e:
            logger.error(f"Failed to delete progress from disk: {e}")
    
    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.
        
        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
