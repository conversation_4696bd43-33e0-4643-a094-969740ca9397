/**
 * Unit tests for EmergencyProfile
 */

const { expect } = require('chai');
const EmergencyProfile = require('../../core/EmergencyProfile');

describe('EmergencyProfile', () => {
  let profileManager;
  
  beforeEach(() => {
    profileManager = new EmergencyProfile();
  });
  
  describe('createProfile', () => {
    it('should create a valid emergency profile', () => {
      const profileData = {
        fullName: '<PERSON>',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+',
        emergencyContacts: [
          {
            name: '<PERSON>',
            relationship: 'Spouse',
            phone: '************'
          }
        ]
      };
      
      const profile = profileManager.createProfile(profileData);
      
      expect(profile).to.be.an('object');
      expect(profile.profileId).to.be.a('string');
      expect(profile.schemaVersion).to.equal('1.0.0');
      expect(profile.createdAt).to.be.a('string');
      expect(profile.updatedAt).to.be.a('string');
      expect(profile.fullName).to.equal('<PERSON>');
      expect(profile.dateOfBirth).to.equal('1980-01-01');
      expect(profile.bloodType).to.equal('A+');
      expect(profile.emergencyContacts).to.be.an('array');
      expect(profile.emergencyContacts[0].name).to.equal('Jane Doe');
      expect(profile.emergencyContacts[0].relationship).to.equal('Spouse');
      expect(profile.emergencyContacts[0].phone).to.equal('************');
    });
    
    it('should throw an error for missing required fields', () => {
      const profileData = {
        fullName: 'John Doe',
        // Missing dateOfBirth
        bloodType: 'A+',
        emergencyContacts: [
          {
            name: 'Jane Doe',
            relationship: 'Spouse',
            phone: '************'
          }
        ]
      };
      
      expect(() => profileManager.createProfile(profileData)).to.throw('Missing required field: dateOfBirth');
    });
    
    it('should throw an error for invalid field values', () => {
      const profileData = {
        fullName: 'John Doe',
        dateOfBirth: 'invalid-date', // Invalid date format
        bloodType: 'A+',
        emergencyContacts: [
          {
            name: 'Jane Doe',
            relationship: 'Spouse',
            phone: '************'
          }
        ]
      };
      
      expect(() => profileManager.createProfile(profileData)).to.throw('Invalid value for field: dateOfBirth');
    });
    
    it('should accept optional fields', () => {
      const profileData = {
        fullName: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+',
        emergencyContacts: [
          {
            name: 'Jane Doe',
            relationship: 'Spouse',
            phone: '************'
          }
        ],
        allergies: [
          {
            name: 'Peanuts',
            severity: 'Severe'
          }
        ],
        medications: [
          {
            name: 'Aspirin',
            dosage: '100mg',
            frequency: 'Daily'
          }
        ],
        dnr: true,
        organDonor: true
      };
      
      const profile = profileManager.createProfile(profileData);
      
      expect(profile.allergies).to.be.an('array');
      expect(profile.allergies[0].name).to.equal('Peanuts');
      expect(profile.allergies[0].severity).to.equal('Severe');
      expect(profile.medications).to.be.an('array');
      expect(profile.medications[0].name).to.equal('Aspirin');
      expect(profile.medications[0].dosage).to.equal('100mg');
      expect(profile.medications[0].frequency).to.equal('Daily');
      expect(profile.dnr).to.be.true;
      expect(profile.organDonor).to.be.true;
    });
  });
  
  describe('updateProfile', () => {
    it('should update an existing profile', () => {
      const profileData = {
        fullName: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+',
        emergencyContacts: [
          {
            name: 'Jane Doe',
            relationship: 'Spouse',
            phone: '************'
          }
        ]
      };
      
      const profile = profileManager.createProfile(profileData);
      
      const updates = {
        bloodType: 'B-',
        dnr: true
      };
      
      const updatedProfile = profileManager.updateProfile(profile, updates);
      
      expect(updatedProfile.profileId).to.equal(profile.profileId);
      expect(updatedProfile.fullName).to.equal('John Doe');
      expect(updatedProfile.bloodType).to.equal('B-'); // Updated
      expect(updatedProfile.dnr).to.be.true; // Added
      expect(updatedProfile.updatedAt).to.not.equal(profile.updatedAt); // Should be updated
    });
    
    it('should throw an error for invalid updates', () => {
      const profileData = {
        fullName: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+',
        emergencyContacts: [
          {
            name: 'Jane Doe',
            relationship: 'Spouse',
            phone: '************'
          }
        ]
      };
      
      const profile = profileManager.createProfile(profileData);
      
      const updates = {
        bloodType: 'Invalid' // Invalid blood type
      };
      
      expect(() => profileManager.updateProfile(profile, updates)).to.throw('Invalid value for field: bloodType');
    });
    
    it('should throw an error for invalid profile', () => {
      const updates = {
        bloodType: 'B-'
      };
      
      expect(() => profileManager.updateProfile(null, updates)).to.throw('Invalid existing profile');
      expect(() => profileManager.updateProfile({}, updates)).to.throw('Invalid existing profile');
    });
  });
  
  describe('createEmergencyAccessToken', () => {
    it('should create an emergency access token', () => {
      const profileData = {
        fullName: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+',
        emergencyContacts: [
          {
            name: 'Jane Doe',
            relationship: 'Spouse',
            phone: '************'
          }
        ]
      };
      
      const profile = profileManager.createProfile(profileData);
      const token = profileManager.createEmergencyAccessToken(profile);
      
      expect(token).to.be.an('object');
      expect(token.tokenId).to.be.a('string');
      expect(token.profileId).to.equal(profile.profileId);
      expect(token.createdAt).to.be.a('string');
      expect(token.expiresAt).to.be.a('string');
      expect(token.accessLevel).to.equal('full');
      expect(token.emergencyType).to.equal('medical');
      expect(token.issuedTo).to.equal('emergency_services');
      expect(token.issuedBy).to.equal('system');
    });
    
    it('should accept custom options', () => {
      const profileData = {
        fullName: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+',
        emergencyContacts: [
          {
            name: 'Jane Doe',
            relationship: 'Spouse',
            phone: '************'
          }
        ]
      };
      
      const profile = profileManager.createProfile(profileData);
      const options = {
        expiresIn: 1800, // 30 minutes
        accessLevel: 'standard',
        emergencyType: 'accident',
        issuedTo: 'paramedic',
        issuedBy: 'hospital'
      };
      
      const token = profileManager.createEmergencyAccessToken(profile, options);
      
      expect(token.accessLevel).to.equal('standard');
      expect(token.emergencyType).to.equal('accident');
      expect(token.issuedTo).to.equal('paramedic');
      expect(token.issuedBy).to.equal('hospital');
      
      // Check expiration time (should be about 30 minutes from now)
      const expiresAt = new Date(token.expiresAt).getTime();
      const createdAt = new Date(token.createdAt).getTime();
      const diff = expiresAt - createdAt;
      
      expect(diff).to.be.closeTo(1800 * 1000, 100); // Within 100ms of 30 minutes
    });
    
    it('should throw an error for invalid profile', () => {
      expect(() => profileManager.createEmergencyAccessToken(null)).to.throw('Invalid profile');
      expect(() => profileManager.createEmergencyAccessToken({})).to.throw('Invalid profile');
    });
  });
  
  describe('getFilteredProfile', () => {
    let fullProfile;
    
    beforeEach(() => {
      const profileData = {
        fullName: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+',
        emergencyContacts: [
          {
            name: 'Jane Doe',
            relationship: 'Spouse',
            phone: '************'
          }
        ],
        allergies: [
          {
            name: 'Peanuts',
            severity: 'Severe'
          }
        ],
        medications: [
          {
            name: 'Aspirin',
            dosage: '100mg',
            frequency: 'Daily'
          }
        ],
        medicalConditions: [
          {
            name: 'Hypertension',
            diagnosisDate: '2010-05-15'
          }
        ],
        insuranceInfo: {
          provider: 'Health Insurance Co',
          policyNumber: '12345678',
          groupNumber: 'G123',
          phone: '************'
        },
        dnr: true,
        organDonor: true,
        primaryCareProvider: {
          name: 'Dr. Smith',
          phone: '************',
          address: '123 Medical Center Dr'
        },
        notes: 'Additional medical notes'
      };
      
      fullProfile = profileManager.createProfile(profileData);
    });
    
    it('should return basic profile with basic access level', () => {
      const filteredProfile = profileManager.getFilteredProfile(fullProfile, 'basic');
      
      expect(filteredProfile.profileId).to.equal(fullProfile.profileId);
      expect(filteredProfile.fullName).to.equal(fullProfile.fullName);
      expect(filteredProfile.dateOfBirth).to.equal(fullProfile.dateOfBirth);
      expect(filteredProfile.bloodType).to.equal(fullProfile.bloodType);
      expect(filteredProfile.emergencyContacts).to.deep.equal(fullProfile.emergencyContacts);
      
      // Should not include medical info
      expect(filteredProfile.allergies).to.be.undefined;
      expect(filteredProfile.medications).to.be.undefined;
      expect(filteredProfile.medicalConditions).to.be.undefined;
      expect(filteredProfile.dnr).to.be.undefined;
      expect(filteredProfile.organDonor).to.be.undefined;
      
      // Should not include sensitive info
      expect(filteredProfile.insuranceInfo).to.be.undefined;
      expect(filteredProfile.primaryCareProvider).to.be.undefined;
      expect(filteredProfile.notes).to.be.undefined;
    });
    
    it('should return standard profile with standard access level', () => {
      const filteredProfile = profileManager.getFilteredProfile(fullProfile, 'standard');
      
      expect(filteredProfile.profileId).to.equal(fullProfile.profileId);
      expect(filteredProfile.fullName).to.equal(fullProfile.fullName);
      expect(filteredProfile.dateOfBirth).to.equal(fullProfile.dateOfBirth);
      expect(filteredProfile.bloodType).to.equal(fullProfile.bloodType);
      expect(filteredProfile.emergencyContacts).to.deep.equal(fullProfile.emergencyContacts);
      
      // Should include medical info
      expect(filteredProfile.allergies).to.deep.equal(fullProfile.allergies);
      expect(filteredProfile.medications).to.deep.equal(fullProfile.medications);
      expect(filteredProfile.medicalConditions).to.deep.equal(fullProfile.medicalConditions);
      expect(filteredProfile.dnr).to.equal(fullProfile.dnr);
      expect(filteredProfile.organDonor).to.equal(fullProfile.organDonor);
      
      // Should not include sensitive info
      expect(filteredProfile.insuranceInfo).to.be.undefined;
      expect(filteredProfile.primaryCareProvider).to.be.undefined;
      expect(filteredProfile.notes).to.be.undefined;
    });
    
    it('should return full profile with full access level', () => {
      const filteredProfile = profileManager.getFilteredProfile(fullProfile, 'full');
      
      expect(filteredProfile.profileId).to.equal(fullProfile.profileId);
      expect(filteredProfile.fullName).to.equal(fullProfile.fullName);
      expect(filteredProfile.dateOfBirth).to.equal(fullProfile.dateOfBirth);
      expect(filteredProfile.bloodType).to.equal(fullProfile.bloodType);
      expect(filteredProfile.emergencyContacts).to.deep.equal(fullProfile.emergencyContacts);
      
      // Should include medical info
      expect(filteredProfile.allergies).to.deep.equal(fullProfile.allergies);
      expect(filteredProfile.medications).to.deep.equal(fullProfile.medications);
      expect(filteredProfile.medicalConditions).to.deep.equal(fullProfile.medicalConditions);
      expect(filteredProfile.dnr).to.equal(fullProfile.dnr);
      expect(filteredProfile.organDonor).to.equal(fullProfile.organDonor);
      
      // Should include sensitive info
      expect(filteredProfile.insuranceInfo).to.deep.equal(fullProfile.insuranceInfo);
      expect(filteredProfile.primaryCareProvider).to.deep.equal(fullProfile.primaryCareProvider);
      expect(filteredProfile.notes).to.equal(fullProfile.notes);
    });
    
    it('should throw an error for invalid profile', () => {
      expect(() => profileManager.getFilteredProfile(null)).to.throw('Invalid profile');
      expect(() => profileManager.getFilteredProfile({})).to.throw('Invalid profile');
    });
  });
  
  describe('validateProfile', () => {
    it('should validate a valid profile', () => {
      const profileData = {
        fullName: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+',
        emergencyContacts: [
          {
            name: 'Jane Doe',
            relationship: 'Spouse',
            phone: '************'
          }
        ]
      };
      
      const profile = profileManager.createProfile(profileData);
      const validation = profileManager.validateProfile(profile);
      
      expect(validation.valid).to.be.true;
      expect(validation.errors).to.be.undefined;
      expect(validation.schemaVersion).to.equal('1.0.0');
    });
    
    it('should invalidate a profile with missing required fields', () => {
      const profile = {
        profileId: '123',
        fullName: 'John Doe',
        // Missing dateOfBirth
        bloodType: 'A+',
        emergencyContacts: [
          {
            name: 'Jane Doe',
            relationship: 'Spouse',
            phone: '************'
          }
        ]
      };
      
      const validation = profileManager.validateProfile(profile);
      
      expect(validation.valid).to.be.false;
      expect(validation.errors).to.be.an('array');
      expect(validation.errors).to.include('Missing required field: dateOfBirth');
    });
    
    it('should invalidate a profile with invalid field values', () => {
      const profile = {
        profileId: '123',
        fullName: 'John Doe',
        dateOfBirth: 'invalid-date', // Invalid date format
        bloodType: 'A+',
        emergencyContacts: [
          {
            name: 'Jane Doe',
            relationship: 'Spouse',
            phone: '************'
          }
        ]
      };
      
      const validation = profileManager.validateProfile(profile);
      
      expect(validation.valid).to.be.false;
      expect(validation.errors).to.be.an('array');
      expect(validation.errors).to.include('Invalid value for field: dateOfBirth');
    });
    
    it('should invalidate a profile with invalid optional field values', () => {
      const profile = {
        profileId: '123',
        fullName: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+',
        emergencyContacts: [
          {
            name: 'Jane Doe',
            relationship: 'Spouse',
            phone: '************'
          }
        ],
        allergies: 'Not an array' // Should be an array
      };
      
      const validation = profileManager.validateProfile(profile);
      
      expect(validation.valid).to.be.false;
      expect(validation.errors).to.be.an('array');
      expect(validation.errors).to.include('Invalid value for field: allergies');
    });
    
    it('should invalidate a non-profile object', () => {
      const validation = profileManager.validateProfile(null);
      
      expect(validation.valid).to.be.false;
      expect(validation.errors).to.be.an('array');
      expect(validation.errors).to.include('Invalid profile: missing profileId');
    });
  });
  
  describe('getSchemaDefinition', () => {
    it('should return the schema definition', () => {
      const schema = profileManager.getSchemaDefinition();
      
      expect(schema).to.be.an('object');
      expect(schema.schemaVersion).to.equal('1.0.0');
      expect(schema.requiredFields).to.be.an('array');
      expect(schema.sensitiveFields).to.be.an('array');
      expect(schema.fields).to.be.an('object');
      
      // Check required fields
      expect(schema.requiredFields).to.include('fullName');
      expect(schema.requiredFields).to.include('dateOfBirth');
      expect(schema.requiredFields).to.include('bloodType');
      expect(schema.requiredFields).to.include('emergencyContacts');
      
      // Check sensitive fields
      expect(schema.sensitiveFields).to.include('allergies');
      expect(schema.sensitiveFields).to.include('medications');
      expect(schema.sensitiveFields).to.include('medicalConditions');
      expect(schema.sensitiveFields).to.include('insuranceInfo');
      
      // Check field definitions
      expect(schema.fields.fullName).to.be.an('object');
      expect(schema.fields.dateOfBirth).to.be.an('object');
      expect(schema.fields.bloodType).to.be.an('object');
      expect(schema.fields.emergencyContacts).to.be.an('object');
    });
  });
});
