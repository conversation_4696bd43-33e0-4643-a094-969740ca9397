# NovaFuse Development Environment Setup Guide

This guide provides instructions for setting up the development environment for the NovaFuse platform.

## Prerequisites

- **Node.js**: v16 or higher
- **npm**: v8 or higher
- **Docker**: v20 or higher
- **Docker Compose**: v2 or higher
- **MongoDB**: v4.4 or higher (or use Docker)
- **Git**: v2.30 or higher

## Repository Structure

The NovaFuse platform consists of the following repositories:

1. **nova-fuse**: Main repository with documentation and project overview
2. **nova-connect**: Universal API Connector for seamless API integration
3. **nova-grc-apis**: Collection of GRC APIs
4. **nova-ui**: UI components for all NovaFuse products
5. **nova-gateway**: API Gateway for routing and managing API requests

## Setup Instructions

### 1. Clone Repositories

```bash
# Create a directory for NovaFuse
mkdir novafuse
cd novafuse

# Clone repositories
git clone https://github.com/Dartan1983/nova-fuse.git
git clone https://github.com/Dartan1983/nova-connect.git
git clone https://github.com/Dartan1983/nova-grc-apis.git
git clone https://github.com/Dartan1983/nova-ui.git
git clone https://github.com/Dartan1983/nova-gateway.git
```

### 2. Set Up Environment Variables

Create `.env` files in each repository based on the provided `.env.example` files:

```bash
# nova-connect
cd nova-connect
cp .env.example .env
# Edit .env with your configuration

# nova-grc-apis
cd ../nova-grc-apis
cp .env.example .env
# Edit .env with your configuration

# nova-ui
cd ../nova-ui
cp .env.example .env.local
# Edit .env.local with your configuration

# nova-gateway
cd ../nova-gateway
cp .env.example .env
# Edit .env with your configuration
```

### 3. Install Dependencies

Install dependencies for each repository:

```bash
# nova-connect
cd nova-connect
npm install

# nova-grc-apis
cd ../nova-grc-apis
npm install

# nova-ui
cd ../nova-ui
npm install

# nova-gateway
cd ../nova-gateway
npm install
```

### 4. Set Up MongoDB

You can either install MongoDB locally or use Docker to run MongoDB:

#### Option 1: Local MongoDB

1. Install MongoDB from the [official website](https://www.mongodb.com/try/download/community)
2. Start MongoDB service
3. Create databases for each service:
   - `nova-connect`
   - `nova-grc-apis`

#### Option 2: MongoDB with Docker

```bash
# Create a docker-compose.yml file
cat > docker-compose.yml << EOL
version: '3.8'
services:
  mongodb:
    image: mongo:4.4
    container_name: mongodb
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password

volumes:
  mongodb_data:
EOL

# Start MongoDB
docker-compose up -d
```

Update the `.env` files to use the MongoDB container:

```
MONGODB_URI=*****************************************************
```

### 5. Start Development Servers

Start the development servers for each repository:

```bash
# nova-connect
cd nova-connect
npm run dev

# nova-grc-apis
cd ../nova-grc-apis
npm run dev

# nova-ui
cd ../nova-ui
npm run dev

# nova-gateway
cd ../nova-gateway
npm run dev
```

### 6. Docker Compose Setup (Optional)

For a more integrated development experience, you can use Docker Compose to run all services together:

```bash
# Create a docker-compose.yml file in the root directory
cat > docker-compose.yml << EOL
version: '3.8'
services:
  mongodb:
    image: mongo:4.4
    container_name: mongodb
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password

  nova-connect:
    build:
      context: ./nova-connect
    container_name: nova-connect
    ports:
      - "3001:3001"
    depends_on:
      - mongodb
    environment:
      - MONGODB_URI=***************************************************
      - PORT=3001

  nova-grc-apis:
    build:
      context: ./nova-grc-apis
    container_name: nova-grc-apis
    ports:
      - "3002:3002"
    depends_on:
      - mongodb
    environment:
      - MONGODB_URI=****************************************************
      - PORT=3002

  nova-gateway:
    build:
      context: ./nova-gateway
    container_name: nova-gateway
    ports:
      - "3000:3000"
    depends_on:
      - nova-connect
      - nova-grc-apis
    environment:
      - NOVACONNECT_URL=http://nova-connect:3001
      - PRIVACY_MANAGEMENT_URL=http://nova-grc-apis:3002
      - PORT=3000

  nova-ui:
    build:
      context: ./nova-ui
    container_name: nova-ui
    ports:
      - "3003:3003"
    environment:
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:3000
      - PORT=3003

volumes:
  mongodb_data:
EOL

# Start all services
docker-compose up -d
```

### 7. Create Dockerfiles

Create Dockerfiles for each repository:

#### nova-connect/Dockerfile

```dockerfile
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3001

CMD ["node", "server.js"]
```

#### nova-grc-apis/Dockerfile

```dockerfile
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3002

CMD ["node", "server.js"]
```

#### nova-ui/Dockerfile

```dockerfile
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

RUN npm run build

EXPOSE 3003

CMD ["npm", "start"]
```

#### nova-gateway/Dockerfile

```dockerfile
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["node", "server.js"]
```

## Testing

### Running Tests

Run tests for each repository:

```bash
# nova-connect
cd nova-connect
npm test

# nova-grc-apis
cd ../nova-grc-apis
npm test

# nova-ui
cd ../nova-ui
npm test

# nova-gateway
cd ../nova-gateway
npm test
```

### Test Coverage

Generate test coverage reports:

```bash
# nova-connect
cd nova-connect
npm run test:coverage

# nova-grc-apis
cd ../nova-grc-apis
npm run test:coverage

# nova-ui
cd ../nova-ui
npm run test:coverage

# nova-gateway
cd ../nova-gateway
npm run test:coverage
```

## Linting

Run linting for each repository:

```bash
# nova-connect
cd nova-connect
npm run lint

# nova-grc-apis
cd ../nova-grc-apis
npm run lint

# nova-ui
cd ../nova-ui
npm run lint

# nova-gateway
cd ../nova-gateway
npm run lint
```

## Building

Build each repository:

```bash
# nova-connect
cd nova-connect
npm run build

# nova-grc-apis
cd ../nova-grc-apis
npm run build

# nova-ui
cd ../nova-ui
npm run build

# nova-gateway
cd ../nova-gateway
npm run build
```

## Troubleshooting

### Common Issues

#### MongoDB Connection Issues

If you're having trouble connecting to MongoDB, check the following:

1. Ensure MongoDB is running
2. Verify the connection string in the `.env` file
3. Check if the MongoDB user has the correct permissions

#### Port Conflicts

If you're seeing port conflicts, you can change the ports in the `.env` files:

```
PORT=3001  # Change to an available port
```

#### Node.js Version Issues

If you're seeing errors related to Node.js version, ensure you're using Node.js v16 or higher:

```bash
node --version
```

If you need to install a different version, you can use nvm (Node Version Manager):

```bash
nvm install 16
nvm use 16
```

## Conclusion

You now have a fully configured development environment for the NovaFuse platform. If you encounter any issues, please refer to the troubleshooting section or contact the NovaFuse team for assistance.
