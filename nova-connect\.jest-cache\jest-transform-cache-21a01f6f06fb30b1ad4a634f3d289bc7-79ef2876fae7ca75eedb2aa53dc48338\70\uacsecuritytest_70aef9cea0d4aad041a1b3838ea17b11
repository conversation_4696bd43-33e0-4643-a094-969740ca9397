c7007a5837f5caafbd4b6e12fc7d8c8d
/**
 * NovaConnect UAC Security Tests
 * 
 * This test suite validates the security features of the Universal API Connector.
 */

const request = require('supertest');
const mongoose = require('mongoose');
const {
  MongoMemoryServer
} = require('mongodb-memory-server');
const app = require('../../app');
const {
  EncryptionService
} = require('../../src/security/encryption-service');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
let mongoServer;
let server;
let encryptionService;
let validToken;
let expiredToken;
let invalidSignatureToken;

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'SecurePassword123!',
  name: 'Security Test User'
};

// Setup and teardown
beforeAll(async () => {
  // Start MongoDB memory server
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();

  // Connect to in-memory database
  await mongoose.connect(mongoUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true
  });

  // Initialize encryption service
  encryptionService = new EncryptionService();

  // Start server
  server = app.listen(0);

  // Create test tokens
  const jwtSecret = process.env.JWT_SECRET || 'test-secret';

  // Valid token
  validToken = jwt.sign({
    id: 'user-123',
    email: testUser.email
  }, jwtSecret, {
    expiresIn: '1h'
  });

  // Expired token
  expiredToken = jwt.sign({
    id: 'user-123',
    email: testUser.email
  }, jwtSecret, {
    expiresIn: '0s'
  });

  // Invalid signature token
  invalidSignatureToken = jwt.sign({
    id: 'user-123',
    email: testUser.email
  }, 'wrong-secret', {
    expiresIn: '1h'
  });
});
afterAll(async () => {
  // Stop server and close database connection
  server.close();
  await mongoose.disconnect();
  await mongoServer.stop();
});
describe('NovaConnect UAC Security Tests', () => {
  describe('Authentication Security', () => {
    test('Should register a new user with password validation', async () => {
      // Test with weak password
      const weakPasswordResponse = await request(app).post('/api/auth/register').send({
        email: '<EMAIL>',
        password: 'weak',
        name: 'Weak Password User'
      });
      expect(weakPasswordResponse.status).toBe(400);
      expect(weakPasswordResponse.body).toHaveProperty('error');
      expect(weakPasswordResponse.body.error).toMatch(/password/i);

      // Test with valid password
      const validResponse = await request(app).post('/api/auth/register').send(testUser);
      expect(validResponse.status).toBe(201);
      expect(validResponse.body).toHaveProperty('token');
    });
    test('Should enforce rate limiting on login attempts', async () => {
      // Make multiple failed login attempts
      const failedLoginPromises = [];
      for (let i = 0; i < 10; i++) {
        failedLoginPromises.push(request(app).post('/api/auth/login').send({
          email: testUser.email,
          password: 'wrong-password'
        }));
      }
      const responses = await Promise.all(failedLoginPromises);

      // At least one of the later responses should be rate limited
      const rateLimitedResponse = responses.find(res => res.status === 429);
      expect(rateLimitedResponse).toBeDefined();
      expect(rateLimitedResponse.body).toHaveProperty('error');
      expect(rateLimitedResponse.body.error).toMatch(/too many/i);
    });
    test('Should reject expired tokens', async () => {
      const response = await request(app).get('/api/connectors').set('Authorization', `Bearer ${expiredToken}`);
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toMatch(/expired/i);
    });
    test('Should reject tokens with invalid signatures', async () => {
      const response = await request(app).get('/api/connectors').set('Authorization', `Bearer ${invalidSignatureToken}`);
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toMatch(/invalid/i);
    });
  });
  describe('Encryption Security', () => {
    test('Should generate secure encryption keys', async () => {
      const keyInfo = await encryptionService.generateKey();
      expect(keyInfo).toHaveProperty('keyId');
      expect(keyInfo).toHaveProperty('key');
      expect(keyInfo.key.length).toBeGreaterThanOrEqual(32); // At least 256 bits
    });
    test('Should encrypt and decrypt data securely', async () => {
      const testData = {
        secret: 'sensitive-information'
      };
      const keyInfo = await encryptionService.generateKey();

      // Encrypt data
      const encryptedPackage = await encryptionService.encrypt(testData, keyInfo.keyId);
      expect(encryptedPackage).toHaveProperty('keyId');
      expect(encryptedPackage).toHaveProperty('iv');
      expect(encryptedPackage).toHaveProperty('authTag');
      expect(encryptedPackage).toHaveProperty('data');

      // Encrypted data should not contain original data
      const encryptedString = encryptedPackage.data;
      expect(encryptedString).not.toContain('sensitive-information');

      // Decrypt data
      const decryptedData = await encryptionService.decrypt(encryptedPackage);
      expect(decryptedData).toEqual(testData);
    });
    test('Should detect tampering with encrypted data', async () => {
      const testData = {
        secret: 'sensitive-information'
      };
      const keyInfo = await encryptionService.generateKey();

      // Encrypt data
      const encryptedPackage = await encryptionService.encrypt(testData, keyInfo.keyId);

      // Tamper with auth tag
      const tamperedPackage = {
        ...encryptedPackage,
        authTag: Buffer.from('tampered-auth-tag').toString('base64')
      };

      // Attempt to decrypt tampered data
      await expect(encryptionService.decrypt(tamperedPackage)).rejects.toThrow();
    });
    test('Should support key rotation', async () => {
      const testData = {
        secret: 'sensitive-information'
      };

      // Generate initial key and encrypt data
      const initialKeyInfo = await encryptionService.generateKey();
      const encryptedPackage = await encryptionService.encrypt(testData, initialKeyInfo.keyId);

      // Rotate keys
      const newKeyInfo = await encryptionService.rotateKeys();

      // Should still be able to decrypt with old key
      const decryptedData = await encryptionService.decrypt(encryptedPackage);
      expect(decryptedData).toEqual(testData);

      // Should be able to encrypt with new key
      const newEncryptedPackage = await encryptionService.encrypt(testData, newKeyInfo.keyId);
      const newDecryptedData = await encryptionService.decrypt(newEncryptedPackage);
      expect(newDecryptedData).toEqual(testData);
    });
  });
  describe('API Security', () => {
    let authToken;
    beforeAll(async () => {
      // Login to get valid token
      const response = await request(app).post('/api/auth/login').send({
        email: testUser.email,
        password: testUser.password
      });
      authToken = response.body.token;
    });
    test('Should enforce CSRF protection', async () => {
      // Get CSRF token
      const csrfResponse = await request(app).get('/api/csrf-token').set('Authorization', `Bearer ${authToken}`);
      expect(csrfResponse.status).toBe(200);
      expect(csrfResponse.body).toHaveProperty('csrfToken');
      const csrfToken = csrfResponse.body.csrfToken;

      // Request with CSRF token should succeed
      const validResponse = await request(app).post('/api/connectors').set('Authorization', `Bearer ${authToken}`).set('X-CSRF-Token', csrfToken).send({
        name: 'Test Connector',
        type: 'REST',
        description: 'Test connector'
      });
      expect(validResponse.status).toBe(201);

      // Request without CSRF token should fail
      const invalidResponse = await request(app).post('/api/connectors').set('Authorization', `Bearer ${authToken}`).send({
        name: 'Another Connector',
        type: 'REST',
        description: 'Another test connector'
      });
      expect(invalidResponse.status).toBe(403);
      expect(invalidResponse.body).toHaveProperty('error');
      expect(invalidResponse.body.error).toMatch(/csrf/i);
    });
    test('Should enforce content security policy', async () => {
      const response = await request(app).get('/api/connectors').set('Authorization', `Bearer ${authToken}`);
      expect(response.headers).toHaveProperty('content-security-policy');
      expect(response.headers['content-security-policy']).toContain('default-src');
    });
    test('Should set secure HTTP headers', async () => {
      const response = await request(app).get('/api/connectors').set('Authorization', `Bearer ${authToken}`);
      expect(response.headers).toHaveProperty('x-content-type-options');
      expect(response.headers).toHaveProperty('x-frame-options');
      expect(response.headers).toHaveProperty('x-xss-protection');
      expect(response.headers).toHaveProperty('strict-transport-security');
    });
    test('Should sanitize input to prevent injection attacks', async () => {
      const maliciousInput = {
        name: 'Test<script>alert("XSS")</script>',
        type: 'REST',
        description: 'Test connector with script injection'
      };

      // Get CSRF token
      const csrfResponse = await request(app).get('/api/csrf-token').set('Authorization', `Bearer ${authToken}`);
      const csrfToken = csrfResponse.body.csrfToken;
      const response = await request(app).post('/api/connectors').set('Authorization', `Bearer ${authToken}`).set('X-CSRF-Token', csrfToken).send(maliciousInput);
      expect(response.status).toBe(201);

      // Get the created connector
      const connectorResponse = await request(app).get(`/api/connectors/${response.body.id}`).set('Authorization', `Bearer ${authToken}`);

      // Script tags should be sanitized
      expect(connectorResponse.body.name).not.toContain('<script>');
    });
  });
  describe('Authorization Security', () => {
    let adminToken;
    let userToken;
    let resourceId;
    beforeAll(async () => {
      // Create admin user
      await request(app).post('/api/auth/register').send({
        email: '<EMAIL>',
        password: 'AdminPassword123!',
        name: 'Admin User',
        role: 'admin'
      });

      // Create regular user
      await request(app).post('/api/auth/register').send({
        email: '<EMAIL>',
        password: 'UserPassword123!',
        name: 'Regular User',
        role: 'user'
      });

      // Login as admin
      const adminResponse = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: 'AdminPassword123!'
      });
      adminToken = adminResponse.body.token;

      // Login as user
      const userResponse = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: 'UserPassword123!'
      });
      userToken = userResponse.body.token;

      // Create a resource as admin
      const resourceResponse = await request(app).post('/api/connectors').set('Authorization', `Bearer ${adminToken}`).send({
        name: 'Admin Connector',
        type: 'REST',
        description: 'Connector created by admin'
      });
      resourceId = resourceResponse.body.id;
    });
    test('Should enforce role-based access control', async () => {
      // Admin should be able to access admin-only endpoint
      const adminResponse = await request(app).get('/api/admin/users').set('Authorization', `Bearer ${adminToken}`);
      expect(adminResponse.status).toBe(200);

      // Regular user should not be able to access admin-only endpoint
      const userResponse = await request(app).get('/api/admin/users').set('Authorization', `Bearer ${userToken}`);
      expect(userResponse.status).toBe(403);
      expect(userResponse.body).toHaveProperty('error');
      expect(userResponse.body.error).toMatch(/permission/i);
    });
    test('Should enforce resource-based access control', async () => {
      // Admin should be able to delete any resource
      const adminResponse = await request(app).delete(`/api/connectors/${resourceId}`).set('Authorization', `Bearer ${adminToken}`);
      expect(adminResponse.status).toBe(200);

      // Create another resource
      const resourceResponse = await request(app).post('/api/connectors').set('Authorization', `Bearer ${adminToken}`).send({
        name: 'Another Connector',
        type: 'REST',
        description: 'Another connector'
      });
      const anotherResourceId = resourceResponse.body.id;

      // Regular user should not be able to delete resources they don't own
      const userResponse = await request(app).delete(`/api/connectors/${anotherResourceId}`).set('Authorization', `Bearer ${userToken}`);
      expect(userResponse.status).toBe(403);
      expect(userResponse.body).toHaveProperty('error');
      expect(userResponse.body.error).toMatch(/permission/i);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************