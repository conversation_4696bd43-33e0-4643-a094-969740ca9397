<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>2. Finite Universe Paradigm</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1000px;
            height: 700px;
            position: relative;
            border: 2px solid black;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
        }
        .element {
            position: absolute;
            border-radius: 0; /* Square corners for patent diagrams */
            padding: 20px 20px 20px 50px; /* Extra padding on left for number */
            z-index: 2;
            background-color: white;
            border: 2px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .bold-formula {
            font-weight: bold;
        }
        .circle {
            position: absolute;
            border-radius: 50%;
            border: 2px solid black;
            z-index: 1;
            background-color: white;
        }
    </style>
</head>
<body>
    <h1>2. Finite Universe Paradigm</h1>

    <div class="diagram-container">
        <!-- Finite Universe Paradigm -->
        <div class="element" style="top: 50px; left: 300px; width: 400px; font-weight: bold; font-size: 20px;">
            Finite Universe Paradigm
            <div class="element-number">1</div>
        </div>

        <!-- Core Principles -->
        <div class="element" style="top: 150px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Core Principles
            <div class="element-number">2</div>
        </div>

        <!-- Nested Systems -->
        <div class="element" style="top: 250px; left: 100px; width: 250px; font-size: 14px;">
            Nested Systems
            <div class="element-number">3</div>
        </div>

        <!-- Finite Boundaries -->
        <div class="element" style="top: 250px; left: 375px; width: 250px; font-size: 14px;">
            Finite Boundaries
            <div class="element-number">4</div>
        </div>

        <!-- Coherent Order -->
        <div class="element" style="top: 250px; left: 650px; width: 250px; font-size: 14px;">
            Coherent Order
            <div class="element-number">5</div>
        </div>

        <!-- Mathematical Expression -->
        <div class="element" style="top: 350px; left: 300px; width: 400px; font-size: 16px;">
            <span class="bold-formula">U=T[∑(n=1 to 5) Sn⋅(En+In)⋅Φn]</span>
            <div class="element-number">6</div>
        </div>

        <!-- Practical Applications -->
        <div class="element" style="top: 450px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Practical Applications
            <div class="element-number">7</div>
        </div>

        <!-- Nested Circles Visualization -->
        <div class="circle" style="top: 550px; left: 400px; width: 200px; height: 200px;"></div>
        <div class="circle" style="top: 575px; left: 425px; width: 150px; height: 150px;"></div>
        <div class="circle" style="top: 600px; left: 450px; width: 100px; height: 100px;"></div>
        <div class="circle" style="top: 625px; left: 475px; width: 50px; height: 50px;"></div>

        <!-- Connections -->
        <!-- Title to Core Principles -->
        <!-- Line removed as requested -->

        <!-- Core Principles to Nested Systems -->
        <!-- Lines removed as requested -->

        <!-- Core Principles to Finite Boundaries -->
        <div class="connection" style="top: 200px; left: 500px; width: 2px; height: 50px;"></div>

        <!-- Core Principles to Coherent Order -->
        <div class="connection" style="top: 200px; left: 700px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 200px; left: 700px; width: 75px; height: 2px;"></div>

        <!-- Principles to Mathematical Expression -->
        <div class="connection" style="top: 300px; left: 225px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 350px; left: 225px; width: 75px; height: 2px;"></div>

        <div class="connection" style="top: 300px; left: 500px; width: 2px; height: 50px;"></div>

        <div class="connection" style="top: 300px; left: 775px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 350px; left: 700px; width: 75px; height: 2px;"></div>

        <!-- Mathematical Expression to Practical Applications -->
        <div class="connection" style="top: 400px; left: 500px; width: 2px; height: 50px;"></div>

        <!-- Label for Nested Circles -->
        <div class="element" style="top: 550px; left: 650px; width: 250px; font-size: 14px;">
            Nested Structure Visualization<br>Finite, Nested, Coherent
            <div class="element-number">8</div>
        </div>

        <div class="connection" style="top: 500px; left: 500px; width: 2px; height: 100px;"></div>
        <div class="connection" style="top: 600px; left: 500px; width: 150px; height: 2px;"></div>
    </div>
</body>
</html>
