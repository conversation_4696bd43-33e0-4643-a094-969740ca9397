/**
 * UUICComponentRegistry - Central registry of shared components
 * 
 * This module provides a central registry for all UI components that can be
 * used by the NovaVision system. It allows components to be registered and
 * retrieved by key.
 */

// Import default components from uuicConfig
import { uuicConfig } from './uuicConfig';

/**
 * Component registry class
 */
class ComponentRegistry {
  constructor() {
    // Initialize the registry with default components
    this.registry = { ...uuicConfig.components };
  }
  
  /**
   * Register a component
   * 
   * @param {string} key - Component key
   * @param {React.Component} component - Component to register
   * @returns {void}
   */
  registerComponent(key, component) {
    if (!key) {
      throw new Error('Component key is required');
    }
    
    if (!component) {
      throw new Error('Component is required');
    }
    
    this.registry[key] = component;
  }
  
  /**
   * Register multiple components
   * 
   * @param {Object} components - Object mapping keys to components
   * @returns {void}
   */
  registerComponents(components) {
    if (!components || typeof components !== 'object') {
      throw new Error('Components must be an object');
    }
    
    Object.entries(components).forEach(([key, component]) => {
      this.registerComponent(key, component);
    });
  }
  
  /**
   * Get a component by key
   * 
   * @param {string} key - Component key
   * @returns {React.Component|null} Component or null if not found
   */
  getComponent(key) {
    return this.registry[key] || null;
  }
  
  /**
   * Check if a component exists
   * 
   * @param {string} key - Component key
   * @returns {boolean} Whether the component exists
   */
  hasComponent(key) {
    return !!this.registry[key];
  }
  
  /**
   * Unregister a component
   * 
   * @param {string} key - Component key
   * @returns {boolean} Whether the component was unregistered
   */
  unregisterComponent(key) {
    if (this.hasComponent(key)) {
      delete this.registry[key];
      return true;
    }
    return false;
  }
  
  /**
   * Get all registered component keys
   * 
   * @returns {string[]} Array of component keys
   */
  getComponentKeys() {
    return Object.keys(this.registry);
  }
  
  /**
   * Get all registered components
   * 
   * @returns {Object} Object mapping keys to components
   */
  getAllComponents() {
    return { ...this.registry };
  }
}

// Create a singleton instance
const UUICComponentRegistry = new ComponentRegistry();

export default UUICComponentRegistry;
