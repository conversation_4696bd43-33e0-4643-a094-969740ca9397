/**
 * NovaVision - React Integration Example
 * 
 * This example demonstrates how to use NovaVision with React.
 */

import React, { useState, useEffect } from 'react';
import { UUICBridge } from '../react';

/**
 * Example of using NovaVision with React
 */
const NovaVisionExample = () => {
  // State for the UI schema and data
  const [schema, setSchema] = useState(null);
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Fetch the UI schema from the server
  useEffect(() => {
    const fetchSchema = async () => {
      try {
        setLoading(true);
        
        // Fetch the schema from the server
        const response = await fetch('/api/vision/examples/form');
        const result = await response.json();
        
        setSchema(result.schema);
        setData(result.formData || {});
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };
    
    fetchSchema();
  }, []);
  
  // Handle form submission
  const handleSubmit = async (formData) => {
    try {
      // Submit the form data to the server
      const response = await fetch('/api/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });
      
      const result = await response.json();
      
      // Handle the response
      if (response.ok) {
        alert('Form submitted successfully!');
      } else {
        alert(`Error: ${result.message}`);
      }
    } catch (err) {
      alert(`Error: ${err.message}`);
    }
  };
  
  // Handle data changes
  const handleChange = (newData) => {
    setData(newData);
  };
  
  // Render loading state
  if (loading) {
    return <div>Loading...</div>;
  }
  
  // Render error state
  if (error) {
    return <div>Error: {error}</div>;
  }
  
  // Render the UI
  return (
    <div className="nova-vision-example">
      <h1>NovaVision Example</h1>
      
      {schema ? (
        <UUICBridge
          schema={schema}
          data={data}
          onSubmit={handleSubmit}
          onChange={handleChange}
          options={{
            theme: 'default',
            responsive: true,
            accessibilityLevel: 'AA'
          }}
        />
      ) : (
        <div>No schema available</div>
      )}
    </div>
  );
};

export default NovaVisionExample;
