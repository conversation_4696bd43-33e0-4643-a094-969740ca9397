{"version": 3, "names": ["UAConnectorError", "require", "ValidationError", "constructor", "message", "options", "code", "severity", "context", "cause", "validationErrors", "getUserMessage", "length", "errorMessages", "map", "err", "join", "toJSON", "includeStack", "json", "MissingRequiredFieldError", "fields", "fieldList", "Array", "isArray", "fieldStr", "field", "InvalidFieldValueError", "fieldErrors", "errors", "e", "error", "value", "constraint", "SchemaValidationError", "schemaName", "path", "schemaPath", "module", "exports"], "sources": ["validation-error.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector - Validation Error\n * \n * This module defines validation-related errors for the UAC.\n */\n\nconst UAConnectorError = require('./base-error');\n\n/**\n * Error class for validation failures\n * @class ValidationError\n * @extends UAConnectorError\n */\nclass ValidationError extends UAConnectorError {\n  /**\n   * Create a new ValidationError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   * @param {string} options.code - Error code\n   * @param {string} options.severity - Error severity\n   * @param {Object} options.context - Additional context for the error\n   * @param {Error} options.cause - The error that caused this error\n   * @param {Array<Object>} options.validationErrors - List of validation errors\n   */\n  constructor(message, options = {}) {\n    super(message, {\n      code: options.code || 'VALIDATION_ERROR',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause\n    });\n    \n    this.validationErrors = options.validationErrors || [];\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    if (this.validationErrors.length > 0) {\n      const errorMessages = this.validationErrors.map(err => err.message).join('; ');\n      return `Validation failed: ${errorMessages}`;\n    }\n    return 'The provided data is invalid. Please check your input and try again.';\n  }\n\n  /**\n   * Convert the error to a JSON object\n   * \n   * @param {boolean} includeStack - Whether to include the stack trace\n   * @returns {Object} - JSON representation of the error\n   */\n  toJSON(includeStack = false) {\n    const json = super.toJSON(includeStack);\n    json.validationErrors = this.validationErrors;\n    return json;\n  }\n}\n\n/**\n * Error class for missing required fields\n * @class MissingRequiredFieldError\n * @extends ValidationError\n */\nclass MissingRequiredFieldError extends ValidationError {\n  /**\n   * Create a new MissingRequiredFieldError\n   * \n   * @param {string|Array<string>} fields - The missing field(s)\n   * @param {Object} options - Error options\n   */\n  constructor(fields, options = {}) {\n    const fieldList = Array.isArray(fields) ? fields : [fields];\n    const fieldStr = fieldList.join(', ');\n    const message = `Missing required field(s): ${fieldStr}`;\n    \n    super(message, {\n      code: options.code || 'VALIDATION_MISSING_REQUIRED_FIELD',\n      severity: options.severity || 'error',\n      context: { ...options.context, fields: fieldList },\n      cause: options.cause,\n      validationErrors: fieldList.map(field => ({\n        field,\n        message: `Missing required field: ${field}`,\n        code: 'MISSING_REQUIRED_FIELD'\n      }))\n    });\n  }\n}\n\n/**\n * Error class for invalid field values\n * @class InvalidFieldValueError\n * @extends ValidationError\n */\nclass InvalidFieldValueError extends ValidationError {\n  /**\n   * Create a new InvalidFieldValueError\n   * \n   * @param {Object|Array<Object>} fieldErrors - The field error(s)\n   * @param {Object} options - Error options\n   */\n  constructor(fieldErrors, options = {}) {\n    const errors = Array.isArray(fieldErrors) ? fieldErrors : [fieldErrors];\n    const fieldStr = errors.map(e => e.field).join(', ');\n    const message = `Invalid value(s) for field(s): ${fieldStr}`;\n    \n    super(message, {\n      code: options.code || 'VALIDATION_INVALID_FIELD_VALUE',\n      severity: options.severity || 'error',\n      context: { ...options.context, fieldErrors: errors },\n      cause: options.cause,\n      validationErrors: errors.map(error => ({\n        field: error.field,\n        value: error.value,\n        message: error.message || `Invalid value for field: ${error.field}`,\n        code: 'INVALID_FIELD_VALUE',\n        constraint: error.constraint\n      }))\n    });\n  }\n}\n\n/**\n * Error class for schema validation failures\n * @class SchemaValidationError\n * @extends ValidationError\n */\nclass SchemaValidationError extends ValidationError {\n  /**\n   * Create a new SchemaValidationError\n   * \n   * @param {string} schemaName - The name of the schema\n   * @param {Array<Object>} errors - The validation errors\n   * @param {Object} options - Error options\n   */\n  constructor(schemaName, errors = [], options = {}) {\n    const message = `Schema validation failed for ${schemaName}`;\n    \n    super(message, {\n      code: options.code || 'VALIDATION_SCHEMA_ERROR',\n      severity: options.severity || 'error',\n      context: { ...options.context, schemaName },\n      cause: options.cause,\n      validationErrors: errors.map(error => ({\n        field: error.field || error.path,\n        message: error.message,\n        code: error.code || 'SCHEMA_VALIDATION_ERROR',\n        schemaPath: error.schemaPath\n      }))\n    });\n    \n    this.schemaName = schemaName;\n  }\n}\n\nmodule.exports = {\n  ValidationError,\n  MissingRequiredFieldError,\n  InvalidFieldValueError,\n  SchemaValidationError\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,gBAAgB,GAAGC,OAAO,CAAC,cAAc,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASF,gBAAgB,CAAC;EAC7C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,WAAWA,CAACC,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,kBAAkB;MACxCC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI;IACjB,CAAC,CAAC;IAEF,IAAI,CAACC,gBAAgB,GAAGL,OAAO,CAACK,gBAAgB,IAAI,EAAE;EACxD;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,IAAI,IAAI,CAACD,gBAAgB,CAACE,MAAM,GAAG,CAAC,EAAE;MACpC,MAAMC,aAAa,GAAG,IAAI,CAACH,gBAAgB,CAACI,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACX,OAAO,CAAC,CAACY,IAAI,CAAC,IAAI,CAAC;MAC9E,OAAO,sBAAsBH,aAAa,EAAE;IAC9C;IACA,OAAO,sEAAsE;EAC/E;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEI,MAAMA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,YAAY,CAAC;IACvCC,IAAI,CAACT,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC7C,OAAOS,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,SAASlB,eAAe,CAAC;EACtD;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACkB,MAAM,EAAEhB,OAAO,GAAG,CAAC,CAAC,EAAE;IAChC,MAAMiB,SAAS,GAAGC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;IAC3D,MAAMI,QAAQ,GAAGH,SAAS,CAACN,IAAI,CAAC,IAAI,CAAC;IACrC,MAAMZ,OAAO,GAAG,8BAA8BqB,QAAQ,EAAE;IAExD,KAAK,CAACrB,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,mCAAmC;MACzDC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAE;QAAE,GAAGH,OAAO,CAACG,OAAO;QAAEa,MAAM,EAAEC;MAAU,CAAC;MAClDb,KAAK,EAAEJ,OAAO,CAACI,KAAK;MACpBC,gBAAgB,EAAEY,SAAS,CAACR,GAAG,CAACY,KAAK,KAAK;QACxCA,KAAK;QACLtB,OAAO,EAAE,2BAA2BsB,KAAK,EAAE;QAC3CpB,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMqB,sBAAsB,SAASzB,eAAe,CAAC;EACnD;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACyB,WAAW,EAAEvB,OAAO,GAAG,CAAC,CAAC,EAAE;IACrC,MAAMwB,MAAM,GAAGN,KAAK,CAACC,OAAO,CAACI,WAAW,CAAC,GAAGA,WAAW,GAAG,CAACA,WAAW,CAAC;IACvE,MAAMH,QAAQ,GAAGI,MAAM,CAACf,GAAG,CAACgB,CAAC,IAAIA,CAAC,CAACJ,KAAK,CAAC,CAACV,IAAI,CAAC,IAAI,CAAC;IACpD,MAAMZ,OAAO,GAAG,kCAAkCqB,QAAQ,EAAE;IAE5D,KAAK,CAACrB,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,gCAAgC;MACtDC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAE;QAAE,GAAGH,OAAO,CAACG,OAAO;QAAEoB,WAAW,EAAEC;MAAO,CAAC;MACpDpB,KAAK,EAAEJ,OAAO,CAACI,KAAK;MACpBC,gBAAgB,EAAEmB,MAAM,CAACf,GAAG,CAACiB,KAAK,KAAK;QACrCL,KAAK,EAAEK,KAAK,CAACL,KAAK;QAClBM,KAAK,EAAED,KAAK,CAACC,KAAK;QAClB5B,OAAO,EAAE2B,KAAK,CAAC3B,OAAO,IAAI,4BAA4B2B,KAAK,CAACL,KAAK,EAAE;QACnEpB,IAAI,EAAE,qBAAqB;QAC3B2B,UAAU,EAAEF,KAAK,CAACE;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,SAAShC,eAAe,CAAC;EAClD;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACgC,UAAU,EAAEN,MAAM,GAAG,EAAE,EAAExB,OAAO,GAAG,CAAC,CAAC,EAAE;IACjD,MAAMD,OAAO,GAAG,gCAAgC+B,UAAU,EAAE;IAE5D,KAAK,CAAC/B,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,yBAAyB;MAC/CC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAE;QAAE,GAAGH,OAAO,CAACG,OAAO;QAAE2B;MAAW,CAAC;MAC3C1B,KAAK,EAAEJ,OAAO,CAACI,KAAK;MACpBC,gBAAgB,EAAEmB,MAAM,CAACf,GAAG,CAACiB,KAAK,KAAK;QACrCL,KAAK,EAAEK,KAAK,CAACL,KAAK,IAAIK,KAAK,CAACK,IAAI;QAChChC,OAAO,EAAE2B,KAAK,CAAC3B,OAAO;QACtBE,IAAI,EAAEyB,KAAK,CAACzB,IAAI,IAAI,yBAAyB;QAC7C+B,UAAU,EAAEN,KAAK,CAACM;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAI,CAACF,UAAU,GAAGA,UAAU;EAC9B;AACF;AAEAG,MAAM,CAACC,OAAO,GAAG;EACfrC,eAAe;EACfkB,yBAAyB;EACzBO,sBAAsB;EACtBO;AACF,CAAC", "ignoreList": []}