<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1536.421875 478" style="max-width: 1536.42px; background-color: white;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#333333;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#my-svg .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .uspto&gt;*{fill:#fff!important;stroke:#000!important;stroke-width:2px!important;}#my-svg .uspto span{fill:#fff!important;stroke:#000!important;stroke-width:2px!important;}#my-svg .reference300&gt;*{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference300 span{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference310&gt;*{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference310 span{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference320&gt;*{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference320 span{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference330&gt;*{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference330 span{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference340&gt;*{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference340 span{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .math&gt;*{fill:#f9f9f9!important;stroke:#ddd!important;stroke-dasharray:5 5!important;font-size:8pt!important;}#my-svg .math span{fill:#f9f9f9!important;stroke:#ddd!important;stroke-dasharray:5 5!important;font-size:8pt!important;}#my-svg .legend&gt;*{fill:#f0f0f0!important;stroke:#ccc!important;stroke-width:1px!important;}#my-svg .legend span{fill:#f0f0f0!important;stroke:#ccc!important;stroke-width:1px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A3_A6_0" d="M97.359,70L97.359,76.833C97.359,83.667,97.359,97.333,97.359,107.667C97.359,118,97.359,125,97.359,128.5L97.359,132"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A6_A9_0" d="M97.359,182L97.359,186.167C97.359,190.333,97.359,198.667,97.359,206.333C97.359,214,97.359,221,97.359,224.5L97.359,228"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A9_A12_0" d="M97.359,278L97.359,282.167C97.359,286.333,97.359,294.667,97.359,302.333C97.359,310,97.359,317,97.359,320.5L97.359,324"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A12_A16_0" d="M97.359,374L97.359,378.167C97.359,382.333,97.359,390.667,97.359,398.333C97.359,406,97.359,413,97.359,416.5L97.359,420"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(97.359375, 47)" id="flowchart-A3-0" class="node default uspto reference300"><rect height="46" width="121.53125" y="-23" x="-60.765625" style="fill:none !important;stroke:none !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-30.765625, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="61.53125"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>3: Core Triad</p></span></div></foreignObject></g></g><g transform="translate(97.359375, 159)" id="flowchart-A6-1" class="node default uspto reference310"><rect height="46" width="159.5" y="-23" x="-79.75" style="fill:none !important;stroke:none !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-49.75, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="99.5"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>6: Connection Matrix</p></span></div></foreignObject></g></g><g transform="translate(97.359375, 255)" id="flowchart-A9-3" class="node default uspto reference320"><rect height="46" width="152.5" y="-23" x="-76.25" style="fill:none !important;stroke:none !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-46.25, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="92.5"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>9: Intelligence Grid</p></span></div></foreignObject></g></g><g transform="translate(97.359375, 351)" id="flowchart-A12-5" class="node default uspto reference330"><rect height="46" width="178.71875" y="-23" x="-89.359375" style="fill:none !important;stroke:none !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-59.359375, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="118.71875"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>12: Universal Framework</p></span></div></foreignObject></g></g><g transform="translate(97.359375, 447)" id="flowchart-A16-7" class="node default uspto reference340"><rect height="46" width="161" y="-23" x="-80.5" style="fill:none !important;stroke:none !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-50.5, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="101"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>16: Complete System</p></span></div></foreignObject></g></g><g transform="translate(280.109375, 47)" id="flowchart-Math1-13" class="node default math"><rect height="46" width="143.96875" y="-23" x="-71.984375" style="fill:#f9f9f9 !important;stroke:#ddd !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-41.984375, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="83.96875"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>3: Core Principles</p></span></div></foreignObject></g></g><g transform="translate(478.15625, 47)" id="flowchart-Math2-14" class="node default math"><rect height="46" width="152.125" y="-23" x="-76.0625" style="fill:#f9f9f9 !important;stroke:#ddd !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-46.0625, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="92.125"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>6: Connection Pairs</p></span></div></foreignObject></g></g><g transform="translate(685.265625, 47)" id="flowchart-Math3-15" class="node default math"><rect height="46" width="162.09375" y="-23" x="-81.046875" style="fill:#f9f9f9 !important;stroke:#ddd !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-51.046875, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="102.09375"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>9: Intelligence Matrix</p></span></div></foreignObject></g></g><g transform="translate(905.5078125, 47)" id="flowchart-Math4-16" class="node default math"><rect height="46" width="178.390625" y="-23" x="-89.1953125" style="fill:#f9f9f9 !important;stroke:#ddd !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-59.1953125, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="118.390625"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>12: Framework Elements</p></span></div></foreignObject></g></g><g transform="translate(1131.5625, 47)" id="flowchart-Math5-17" class="node default math"><rect height="46" width="173.71875" y="-23" x="-86.859375" style="fill:#f9f9f9 !important;stroke:#ddd !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-56.859375, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="113.71875"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>16: System Components</p></span></div></foreignObject></g></g><g transform="translate(1398.421875, 47)" id="flowchart-Legend-18" class="node default legend"><rect height="78" width="260" y="-39" x="-130" style="fill:#f0f0f0 !important;stroke:#ccc !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>3-6-9-12-16 Alignment Architecture</p></span></div></foreignObject></g></g></g></g></g></svg>