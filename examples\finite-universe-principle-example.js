/**
 * Finite Universe Principle Example
 *
 * This example demonstrates how to use the Finite Universe Principle components
 * to protect against mathematical incompatibility.
 */

const {
  FiniteUniverse,
  BoundaryEnforcer,
  ResilienceGauntlet,
  createBoundaryEnforcer,
  createResilienceGauntlet,
  runQuickTest
} = require('../src/quantum/finite-universe-principle');

/**
 * Example 1: Basic Boundary Enforcement
 * 
 * This example demonstrates basic boundary enforcement on different types of values.
 */
async function example1() {
  console.log('\n=== Example 1: Basic Boundary Enforcement ===\n');

  // Create a boundary enforcer
  const enforcer = createBoundaryEnforcer();

  // Enforce boundaries on different types of values
  console.log('Enforcing boundaries on different types of values:');
  
  // Number values
  console.log(`Infinity -> ${enforcer.enforceValue(Infinity)}`);
  console.log(`-Infinity -> ${enforcer.enforceValue(-Infinity)}`);
  console.log(`NaN -> ${enforcer.enforceValue(NaN)}`);
  console.log(`1e100 -> ${enforcer.enforceValue(1e100)}`);
  
  // Object with circular reference
  const circularObj = {};
  circularObj.self = circularObj;
  console.log('Circular object ->', JSON.stringify(enforcer.enforceValue(circularObj)));
  
  // Deep object
  let deepObj = {};
  let current = deepObj;
  for (let i = 0; i < 200; i++) {
    current.next = {};
    current = current.next;
    current.value = i;
  }
  const enforcedDeepObj = enforcer.enforceValue(deepObj);
  console.log(`Deep object (depth 200) -> object with depth ${getObjectDepth(enforcedDeepObj)}`);
  
  // Large array
  const largeArray = Array(20000).fill(1);
  const enforcedArray = enforcer.enforceValue(largeArray);
  console.log(`Large array (length 20000) -> array with length ${enforcedArray.length}`);
  
  // Get violation statistics
  console.log('\nViolation statistics:');
  console.log(enforcer.getViolationStats());
  
  // Get correction statistics
  console.log('\nCorrection statistics:');
  console.log(enforcer.getCorrectionStats());
}

/**
 * Example 2: Function Enforcement
 * 
 * This example demonstrates how to enforce boundaries on function calls.
 */
async function example2() {
  console.log('\n=== Example 2: Function Enforcement ===\n');

  // Create a boundary enforcer
  const enforcer = createBoundaryEnforcer();

  // Define a recursive function that would normally cause a stack overflow
  function factorial(n) {
    if (n <= 1) return 1;
    return n * factorial(n - 1);
  }

  // Try to calculate factorial of a large number
  console.log('Calculating factorial of 1000 (would normally cause stack overflow):');
  try {
    const result = enforcer.enforceFunction(factorial, [1000]);
    console.log(`Result: ${result}`);
  } catch (error) {
    console.log(`Error: ${error.message}`);
  }

  // Define a function that returns an infinite value
  function returnInfinity() {
    return 1 / 0;
  }

  // Try to call the function
  console.log('\nCalling function that returns Infinity:');
  const result = enforcer.enforceFunction(returnInfinity);
  console.log(`Result: ${result}`);

  // Get violation statistics
  console.log('\nViolation statistics:');
  console.log(enforcer.getViolationStats());
  
  // Get correction statistics
  console.log('\nCorrection statistics:');
  console.log(enforcer.getCorrectionStats());
}

/**
 * Example 3: Loop Enforcement
 * 
 * This example demonstrates how to enforce boundaries on loops.
 */
async function example3() {
  console.log('\n=== Example 3: Loop Enforcement ===\n');

  // Create a boundary enforcer
  const enforcer = createBoundaryEnforcer({
    maxLoopIterations: 1000 // Limit to 1000 iterations
  });

  // Try to run an infinite loop
  console.log('Running an infinite loop (limited to 1000 iterations):');
  try {
    const result = enforcer.enforceLoop(
      () => true, // Always true condition
      () => console.log('Loop iteration'), // Log iteration
      () => {} // No update
    );
    console.log(`Loop completed with result: ${result}`);
  } catch (error) {
    console.log(`Error: ${error.message}`);
  }

  // Get violation statistics
  console.log('\nViolation statistics:');
  console.log(enforcer.getViolationStats());
}

/**
 * Example 4: Resilience Gauntlet
 * 
 * This example demonstrates how to use the Resilience Gauntlet to test
 * a component's resilience against mathematical incompatibility.
 */
async function example4() {
  console.log('\n=== Example 4: Resilience Gauntlet ===\n');

  // Create a boundary enforcer
  const enforcer = createBoundaryEnforcer();

  // Create a resilience gauntlet
  const gauntlet = createResilienceGauntlet({
    enableLogging: false // Disable logging for cleaner output
  });

  // Run the gauntlet against the enforcer
  console.log('Running Resilience Gauntlet against Boundary Enforcer...');
  const results = await gauntlet.run(enforcer);

  // Print summary
  console.log('\nGauntlet Results Summary:');
  console.log(`Resilience Score: ${results.summary.resilienceScore}%`);
  console.log(`Successful Scenarios: ${results.summary.successfulScenarios}/${results.summary.totalScenarios}`);
  console.log(`Failed Scenarios: ${results.summary.failedScenarios}`);

  // Print category statistics
  console.log('\nCategory Statistics:');
  for (const [category, stats] of Object.entries(results.categoryStats)) {
    console.log(`${category}: ${stats.successful}/${stats.total} successful`);
  }

  // Print severity statistics
  console.log('\nSeverity Statistics:');
  for (const [severity, stats] of Object.entries(results.severityStats)) {
    console.log(`${severity}: ${stats.successful}/${stats.total} successful`);
  }
}

/**
 * Example 5: Quick Test
 * 
 * This example demonstrates how to run a quick test of the Finite Universe Principle components.
 */
async function example5() {
  console.log('\n=== Example 5: Quick Test ===\n');

  // Run a quick test
  console.log('Running quick test...');
  const results = await runQuickTest({
    enforcerOptions: {
      enableLogging: false // Disable logging for cleaner output
    },
    gauntletOptions: {
      enableLogging: false // Disable logging for cleaner output
    }
  });

  // Print summary
  console.log('\nQuick Test Results:');
  console.log(`Resilience Score: ${results.summary.resilienceScore}%`);
  console.log(`Successful Scenarios: ${results.summary.successfulScenarios}/${results.summary.totalScenarios}`);
  
  // Print category statistics
  console.log('\nCategory Statistics:');
  for (const [category, stats] of Object.entries(results.categoryStats)) {
    const successRate = Math.round((stats.successful / stats.total) * 100);
    console.log(`${category}: ${stats.successful}/${stats.total} (${successRate}%)`);
  }
}

/**
 * Helper function to get the depth of an object
 * @param {Object} obj - Object to get depth of
 * @param {number} currentDepth - Current depth
 * @returns {number} - Object depth
 */
function getObjectDepth(obj, currentDepth = 0) {
  if (obj === null || typeof obj !== 'object' || Array.isArray(obj)) {
    return currentDepth;
  }

  let maxDepth = currentDepth;
  for (const key in obj) {
    if (key === 'next' && obj[key] && typeof obj[key] === 'object') {
      const depth = getObjectDepth(obj[key], currentDepth + 1);
      maxDepth = Math.max(maxDepth, depth);
    }
  }

  return maxDepth;
}

/**
 * Run all examples
 */
async function runAllExamples() {
  await example1();
  await example2();
  await example3();
  await example4();
  await example5();
}

// Run all examples
runAllExamples().catch(error => {
  console.error('Error running examples:', error);
});
