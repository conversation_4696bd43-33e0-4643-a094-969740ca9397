import React from 'react';
import { render, screen } from '@testing-library/react';
import AnimatedSection from '../../components/AnimatedSection';

describe('AnimatedSection', () => {
  it('renders children correctly', () => {
    render(
      <AnimatedSection>
        <div data-testid="test-child">Test Content</div>
      </AnimatedSection>
    );
    
    // Check if children are rendered
    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });
  
  it('applies custom className', () => {
    const { container } = render(
      <AnimatedSection className="custom-class">
        <div>Test Content</div>
      </AnimatedSection>
    );
    
    // Check if custom class is applied
    expect(container.firstChild).toHaveClass('custom-class');
  });
  
  it('uses default animation when none is specified', () => {
    const { container } = render(
      <AnimatedSection>
        <div>Test Content</div>
      </AnimatedSection>
    );
    
    // The default animation should be applied (we can't test the actual animation,
    // but we can check that the component renders without errors)
    expect(container.firstChild).toBeInTheDocument();
  });
  
  it('accepts custom animation and delay props', () => {
    const { container } = render(
      <AnimatedSection animation="fadeInLeft" delay={0.5}>
        <div>Test Content</div>
      </AnimatedSection>
    );
    
    // Again, we can't test the actual animation, but we can check that
    // the component renders without errors with custom props
    expect(container.firstChild).toBeInTheDocument();
  });
});
