/**
 * Example usage of HealthcareIntegration
 * 
 * This example demonstrates how to use the HealthcareIntegration module
 * to access emergency medical data from healthcare providers.
 */

const HealthcareIntegration = require('./HealthcareIntegration');

/**
 * Example function to demonstrate healthcare integration
 */
async function healthcareIntegrationExample() {
  try {
    // Initialize healthcare integration
    const healthcareIntegration = new HealthcareIntegration({
      apiUrl: 'http://localhost:3000/api/novaconnect',
      apiKey: 'your-api-key',
      apiSecret: 'your-api-secret',
      cacheEnabled: true,
      encryptionEnabled: true
    });
    
    // Initialize with provider credentials
    await healthcareIntegration.initialize({
      providers: {
        Epic: {
          clientId: 'your-epic-client-id',
          clientSecret: 'your-epic-client-secret',
          redirectUri: 'http://localhost:3000/callback'
        },
        Cerner: {
          clientId: 'your-cerner-client-id',
          clientSecret: 'your-cerner-client-secret',
          redirectUri: 'http://localhost:3000/callback'
        }
      }
    });
    
    // Start an emergency session
    const session = healthcareIntegration.startEmergencySession({
      emergencyType: 'CARDIAC',
      emergencySeverity: 'HIGH',
      responderType: 'PARAMEDIC',
      locationType: 'AMBULANCE'
    });
    
    console.log('Emergency session started:', session);
    
    // Get emergency medical data
    const patientId = 'patient-123';
    const emergencyData = await healthcareIntegration.getEmergencyMedicalData(patientId, session.sessionId);
    
    console.log('Emergency medical data:', emergencyData);
    
    // Use the data for emergency response
    // ...
    
    // End the emergency session when done
    healthcareIntegration.endEmergencySession(session.sessionId);
    
    console.log('Emergency session ended');
    
    // Get integration status
    const status = healthcareIntegration.getStatus();
    
    console.log('Healthcare integration status:', status);
  } catch (error) {
    console.error('Error in healthcare integration example:', error);
  }
}

/**
 * Example function to demonstrate integration with NovaDNA API
 */
async function integrateWithNovaDNAApi(req, res, next) {
  try {
    // Get NovaDNA instance from request
    const { novaDNA } = req;
    
    // Get healthcare integration from NovaDNA
    const healthcareIntegration = novaDNA.healthcareIntegration;
    
    // Get emergency context from request
    const { emergencyType, emergencySeverity, responderType, locationType } = req.body.context || {};
    
    // Start an emergency session
    const session = healthcareIntegration.startEmergencySession({
      emergencyType,
      emergencySeverity,
      responderType,
      locationType
    });
    
    // Get patient ID from request
    const { patientId } = req.body;
    
    if (!patientId) {
      return res.status(400).json({
        status: 'error',
        error: 'Patient ID is required'
      });
    }
    
    // Get emergency medical data
    const emergencyData = await healthcareIntegration.getEmergencyMedicalData(patientId, session.sessionId);
    
    // Return the data
    res.json({
      status: 'success',
      data: {
        profile: emergencyData,
        session: {
          sessionId: session.sessionId,
          expiresAt: session.expiresAt
        }
      }
    });
    
    // Note: The session will be ended automatically when it expires,
    // or it can be ended explicitly with endEmergencySession
  } catch (error) {
    next(error);
  }
}

// Export example functions
module.exports = {
  healthcareIntegrationExample,
  integrateWithNovaDNAApi
};
