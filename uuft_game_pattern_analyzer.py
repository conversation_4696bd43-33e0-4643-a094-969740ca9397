#!/usr/bin/env python3
"""
UUFT Game Theory Pattern Analyzer

This module analyzes game theory simulations for 18/82 patterns and π-related relationships,
detecting and measuring these patterns in strategic interactions and decision-making processes.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
import logging
import json
from collections import defaultdict
from uuft_game_analyzer import UUFTGameScenario
from uuft_game_simulator import UUFTGameSimulator

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uuft_game_pattern.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UUFT_Game_Pattern')

# Constants
PATTERN_1882_RATIO = 18 / 82
PI = np.pi
PI_10_CUBED = PI * 10**3
RESULTS_DIR = "uuft_results/game"
os.makedirs(RESULTS_DIR, exist_ok=True)

class UUFTGamePatternAnalyzer:
    """Analyzes game theory simulations for UUFT patterns."""
    
    def __init__(self, game_simulator, pattern_threshold=0.05):
        """
        Initialize the pattern analyzer.
        
        Args:
            game_simulator: UUFTGameSimulator instance
            pattern_threshold: Threshold for considering a match to the 18/82 pattern (0-1)
        """
        self.simulator = game_simulator
        self.pattern_threshold = pattern_threshold
        logger.info(f"Initialized game pattern analyzer with pattern threshold {pattern_threshold}")
    
    def analyze_strategy_distribution(self):
        """
        Analyze the distribution of strategies for 18/82 patterns.
        
        Returns:
            Dict with analysis results
        """
        logger.info("Analyzing strategy distribution")
        
        if not self.simulator.history:
            logger.warning("No simulation data to analyze")
            return {"error": "No simulation data"}
        
        # Get final strategy distribution
        final_dist = np.array(self.simulator.history[-1]["strategy_distribution"])
        
        # Check for 18/82 pattern
        if final_dist[0] < final_dist[1]:
            # Strategy 0 is minority
            minority_percent = final_dist[0]
            majority_percent = final_dist[1]
        else:
            # Strategy 1 is minority
            minority_percent = final_dist[1]
            majority_percent = final_dist[0]
        
        # Calculate proximity to 18/82 pattern
        proximity_to_1882 = abs(minority_percent - 0.18) / 0.18
        is_1882_pattern = proximity_to_1882 <= self.pattern_threshold
        
        # Check for π-related patterns
        pi_relationships = []
        pi_values = [1/PI, PI/10, 1/(PI*10)]
        
        for i, value in enumerate(pi_values):
            for j, strategy_prob in enumerate(final_dist):
                proximity = abs(strategy_prob - value) / value
                
                if proximity <= self.pattern_threshold:
                    pi_relationships.append({
                        "strategy": j,
                        "pi_value": float(value),
                        "actual_value": float(strategy_prob),
                        "proximity": float(proximity)
                    })
        
        # Compile results
        result = {
            "final_distribution": [float(p) for p in final_dist],
            "minority_strategy": 0 if final_dist[0] < final_dist[1] else 1,
            "minority_percent": float(minority_percent),
            "majority_percent": float(majority_percent),
            "proximity_to_1882": float(proximity_to_1882),
            "is_1882_pattern": bool(is_1882_pattern),
            "pi_relationships": pi_relationships,
            "has_pi_pattern": len(pi_relationships) > 0
        }
        
        return result
    
    def analyze_temporal_stability(self):
        """
        Analyze the temporal stability of strategy distributions.
        
        Returns:
            Dict with analysis results
        """
        logger.info("Analyzing temporal stability")
        
        if not self.simulator.history or len(self.simulator.history) < 3:
            logger.warning("Not enough simulation data for temporal stability analysis")
            return {"error": "Insufficient data"}
        
        # Extract strategy distributions over time
        times = []
        strategy_0_freq = []
        
        for entry in self.simulator.history:
            times.append(entry["time"])
            strategy_0_freq.append(entry["strategy_0_frequency"])
        
        # Calculate stability metrics
        strategy_0_mean = np.mean(strategy_0_freq)
        strategy_0_std = np.std(strategy_0_freq)
        
        # Calculate stability quotient (1 = perfectly stable, 0 = completely unstable)
        stability_quotient = 1.0 - min(1.0, strategy_0_std / max(0.001, strategy_0_mean))
        
        # Check for 18/82 pattern persistence
        pattern_matches = 0
        for freq in strategy_0_freq:
            # Check if either strategy follows 18/82 pattern
            proximity_0 = abs(freq - 0.18) / 0.18
            proximity_1 = abs(1 - freq - 0.18) / 0.18
            
            if min(proximity_0, proximity_1) <= self.pattern_threshold:
                pattern_matches += 1
        
        pattern_persistence = pattern_matches / len(strategy_0_freq)
        
        # Check for π-related patterns in stability metrics
        pi_proximity = abs(stability_quotient * 10 - PI) / PI
        is_pi_pattern = pi_proximity <= self.pattern_threshold
        
        # Compile results
        result = {
            "time_points": len(times),
            "strategy_0_mean": float(strategy_0_mean),
            "strategy_0_std": float(strategy_0_std),
            "temporal_stability_quotient": float(stability_quotient),
            "pattern_persistence": float(pattern_persistence),
            "pi_proximity": float(pi_proximity),
            "is_pi_pattern": bool(is_pi_pattern),
            "time_series": {
                "time": times,
                "strategy_0_frequency": [float(f) for f in strategy_0_freq]
            }
        }
        
        return result
    
    def analyze_payoff_distribution(self):
        """
        Analyze the distribution of payoffs for 18/82 patterns.
        
        Returns:
            Dict with analysis results
        """
        logger.info("Analyzing payoff distribution")
        
        if not self.simulator.history:
            logger.warning("No simulation data to analyze")
            return {"error": "No simulation data"}
        
        # Get final payoffs
        final_payoffs = np.array(self.simulator.history[-1]["payoffs"])
        
        # Sort payoffs
        sorted_payoffs = np.sort(final_payoffs)
        
        # Calculate 18/82 split
        split_index = int(len(sorted_payoffs) * 0.18)
        top_payoffs = sorted_payoffs[split_index:]
        bottom_payoffs = sorted_payoffs[:split_index]
        
        # Calculate payoff statistics
        mean_payoff = np.mean(final_payoffs)
        top_mean = np.mean(top_payoffs)
        bottom_mean = np.mean(bottom_payoffs)
        
        # Check for 18/82 pattern in payoff distribution
        if top_mean != bottom_mean:  # Avoid division by zero
            payoff_ratio = top_mean / bottom_mean if bottom_mean != 0 else float('inf')
            target_ratio = 1 + PATTERN_1882_RATIO  # top should be (1 + 18/82) times bottom
            
            proximity_to_1882 = abs(payoff_ratio - target_ratio) / target_ratio
            is_1882_pattern = proximity_to_1882 <= self.pattern_threshold
        else:
            payoff_ratio = 1.0
            proximity_to_1882 = 1.0
            is_1882_pattern = False
        
        # Check for π-related patterns in payoff statistics
        pi_relationships = []
        
        # Check if mean payoff is related to π
        mean_pi_proximity = min(
            abs(mean_payoff - PI) / PI,
            abs(mean_payoff - PI/10) / (PI/10),
            abs(mean_payoff - PI*10) / (PI*10)
        )
        
        if mean_pi_proximity <= self.pattern_threshold:
            pi_relationships.append({
                "type": "mean_payoff",
                "pi_proximity": float(mean_pi_proximity)
            })
        
        # Check if payoff ratio is related to π
        ratio_pi_proximity = abs(payoff_ratio - PI) / PI
        
        if ratio_pi_proximity <= self.pattern_threshold:
            pi_relationships.append({
                "type": "payoff_ratio",
                "pi_proximity": float(ratio_pi_proximity)
            })
        
        # Compile results
        result = {
            "mean_payoff": float(mean_payoff),
            "top_mean_payoff": float(top_mean),
            "bottom_mean_payoff": float(bottom_mean),
            "payoff_ratio": float(payoff_ratio),
            "proximity_to_1882": float(proximity_to_1882),
            "is_1882_pattern": bool(is_1882_pattern),
            "pi_relationships": pi_relationships,
            "has_pi_pattern": len(pi_relationships) > 0
        }
        
        return result
    
    def analyze_equilibrium_convergence(self):
        """
        Analyze the convergence to equilibrium for UUFT patterns.
        
        Returns:
            Dict with analysis results
        """
        logger.info("Analyzing equilibrium convergence")
        
        if not self.simulator.history:
            logger.warning("No simulation data to analyze")
            return {"error": "No simulation data"}
        
        # Get Nash equilibria
        nash_equilibria = self.simulator.game.find_nash_equilibria()
        
        if not nash_equilibria:
            logger.warning("No Nash equilibria found")
            return {"error": "No Nash equilibria"}
        
        # For 2x2 games, check convergence to pure strategy equilibria
        if self.simulator.game.num_players == 2 and self.simulator.game.num_strategies == 2:
            # Get final strategy distribution
            final_dist = np.array(self.simulator.history[-1]["strategy_distribution"])
            
            # Check convergence to each equilibrium
            equilibrium_convergence = []
            
            for eq in nash_equilibria:
                # Calculate distance to equilibrium
                if eq[0] == 0:
                    # Equilibrium strategy 0 for player 1
                    p1_distance = 1 - final_dist[0]
                else:
                    # Equilibrium strategy 1 for player 1
                    p1_distance = final_dist[0]
                
                if eq[1] == 0:
                    # Equilibrium strategy 0 for player 2
                    p2_distance = 1 - final_dist[1]
                else:
                    # Equilibrium strategy 1 for player 2
                    p2_distance = final_dist[1]
                
                # Calculate overall distance
                distance = (p1_distance + p2_distance) / 2
                
                equilibrium_convergence.append({
                    "equilibrium": eq,
                    "distance": float(distance)
                })
            
            # Find closest equilibrium
            closest_eq = min(equilibrium_convergence, key=lambda x: x["distance"])
            
            # Check for 18/82 pattern in equilibrium selection
            if len(nash_equilibria) > 1:
                # Sort equilibria by distance
                sorted_eq = sorted(equilibrium_convergence, key=lambda x: x["distance"])
                
                # Calculate ratio of distances
                if sorted_eq[1]["distance"] > 0:  # Avoid division by zero
                    distance_ratio = sorted_eq[0]["distance"] / sorted_eq[1]["distance"]
                    
                    # Check if ratio is close to 18/82
                    proximity_to_1882 = abs(distance_ratio - PATTERN_1882_RATIO) / PATTERN_1882_RATIO
                    is_1882_pattern = proximity_to_1882 <= self.pattern_threshold
                else:
                    distance_ratio = 0
                    proximity_to_1882 = 1.0
                    is_1882_pattern = False
            else:
                distance_ratio = 0
                proximity_to_1882 = 1.0
                is_1882_pattern = False
            
            # Check for π-related patterns in convergence
            pi_proximity = abs(closest_eq["distance"] * 10 - PI) / PI
            is_pi_pattern = pi_proximity <= self.pattern_threshold
            
            # Compile results
            result = {
                "nash_equilibria": nash_equilibria,
                "equilibrium_convergence": equilibrium_convergence,
                "closest_equilibrium": closest_eq["equilibrium"],
                "distance_to_closest": float(closest_eq["distance"]),
                "distance_ratio": float(distance_ratio),
                "proximity_to_1882": float(proximity_to_1882),
                "is_1882_pattern": bool(is_1882_pattern),
                "pi_proximity": float(pi_proximity),
                "is_pi_pattern": bool(is_pi_pattern)
            }
        else:
            # For other games, just return the equilibria
            result = {
                "nash_equilibria": nash_equilibria,
                "error": "Detailed convergence analysis only implemented for 2x2 games"
            }
        
        return result
    
    def create_comprehensive_report(self, save_path=None):
        """
        Create a comprehensive report of all UUFT pattern analyses.
        
        Args:
            save_path: Path to save the report
            
        Returns:
            Dict with report summary
        """
        logger.info("Creating comprehensive report")
        
        # Run all analyses
        strategy_result = self.analyze_strategy_distribution()
        temporal_result = self.analyze_temporal_stability()
        payoff_result = self.analyze_payoff_distribution()
        equilibrium_result = self.analyze_equilibrium_convergence()
        
        # Calculate overall pattern scores
        pattern_1882_score = 0.0
        count_1882 = 0
        
        # Add strategy distribution score
        if strategy_result.get("is_1882_pattern", False):
            pattern_1882_score += 1.0
            count_1882 += 1
        
        # Add temporal stability score
        if temporal_result.get("pattern_persistence", 0) > 0.5:
            pattern_1882_score += temporal_result.get("pattern_persistence", 0)
            count_1882 += 1
        
        # Add payoff distribution score
        if payoff_result.get("is_1882_pattern", False):
            pattern_1882_score += 1.0
            count_1882 += 1
        
        # Add equilibrium convergence score
        if equilibrium_result.get("is_1882_pattern", False):
            pattern_1882_score += 1.0
            count_1882 += 1
        
        # Calculate final score
        overall_1882_score = pattern_1882_score / max(1, count_1882)
        
        # Calculate π relationship score
        pi_relationship_score = 0.0
        count_pi = 0
        
        # Add strategy distribution π score
        if strategy_result.get("has_pi_pattern", False):
            pi_relationship_score += 1.0
            count_pi += 1
        
        # Add temporal stability π score
        if temporal_result.get("is_pi_pattern", False):
            pi_relationship_score += 1.0
            count_pi += 1
        
        # Add payoff distribution π score
        if payoff_result.get("has_pi_pattern", False):
            pi_relationship_score += 1.0
            count_pi += 1
        
        # Add equilibrium convergence π score
        if equilibrium_result.get("is_pi_pattern", False):
            pi_relationship_score += 1.0
            count_pi += 1
        
        # Calculate final score
        overall_pi_score = pi_relationship_score / max(1, count_pi)
        
        # Compile report
        report = {
            "game_type": self.simulator.game.game_type,
            "uuft_bias": self.simulator.game.uuft_bias,
            "num_agents": self.simulator.num_agents,
            "simulation_steps": self.simulator.current_time,
            "overall_1882_pattern_score": float(overall_1882_score),
            "overall_pi_relationship_score": float(overall_pi_score),
            "strategy_analysis": strategy_result,
            "temporal_analysis": temporal_result,
            "payoff_analysis": payoff_result,
            "equilibrium_analysis": equilibrium_result
        }
        
        # Save report if path provided
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2)
            logger.info(f"Comprehensive report saved to {save_path}")
        
        return report
    
    def visualize_1882_patterns(self, save_path=None):
        """
        Visualize 18/82 patterns in the game simulation.
        
        Args:
            save_path: Path to save the visualization
        """
        logger.info("Visualizing 18/82 patterns")
        
        # Get temporal stability results
        temporal_result = self.analyze_temporal_stability()
        
        if "error" in temporal_result:
            logger.warning("Not enough data for 18/82 pattern visualization")
            return
        
        # Create figure
        plt.figure(figsize=(12, 8))
        
        # Get time series data
        time_series = temporal_result["time_series"]
        times = time_series["time"]
        strategy_0_freq = time_series["strategy_0_frequency"]
        
        # Plot strategy frequencies
        plt.subplot(2, 1, 1)
        plt.plot(times, strategy_0_freq, 'b-', label='Strategy 0', linewidth=2)
        plt.plot(times, [1 - freq for freq in strategy_0_freq], 'r-', label='Strategy 1', linewidth=2)
        
        # Add reference lines
        plt.axhline(y=0.18, color='g', linestyle='--', alpha=0.5, label='18%')
        plt.axhline(y=0.82, color='g', linestyle='--', alpha=0.5, label='82%')
        
        plt.xlabel('Time Step')
        plt.ylabel('Strategy Frequency')
        plt.title('Strategy Distribution Over Time')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Plot proximity to 18/82 pattern
        plt.subplot(2, 1, 2)
        
        # Calculate proximity at each time step
        proximity_values = []
        for freq in strategy_0_freq:
            proximity_0 = abs(freq - 0.18) / 0.18
            proximity_1 = abs(1 - freq - 0.18) / 0.18
            proximity_values.append(min(proximity_0, proximity_1) * 100)
        
        plt.plot(times, proximity_values, 'g-', linewidth=2)
        
        # Add threshold line
        plt.axhline(y=self.pattern_threshold * 100, color='r', linestyle='--', 
                   label=f'Pattern Threshold ({self.pattern_threshold * 100}%)')
        
        # Highlight regions with 18/82 pattern
        for i in range(len(times)):
            if proximity_values[i] <= self.pattern_threshold * 100:
                plt.axvspan(times[i] - 0.5, times[i] + 0.5, color='g', alpha=0.2)
        
        plt.xlabel('Time Step')
        plt.ylabel('Proximity to 18/82 Pattern (%)')
        plt.title('18/82 Pattern Proximity Over Time')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Add overall metrics
        plt.figtext(0.02, 0.02, 
                   f"Temporal Stability Quotient: {temporal_result['temporal_stability_quotient']:.4f}\n" +
                   f"Pattern Persistence: {temporal_result['pattern_persistence']:.4f}",
                   fontsize=10, bbox=dict(facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        # Save if path provided
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"18/82 pattern visualization saved to {save_path}")
        
        plt.close()
