# NovaCaia Enterprise CI/CD Pipeline
# Automated testing, building, and deployment for AI Governance Engine

stages:
  - test
  - security
  - build
  - deploy-staging
  - integration-test
  - deploy-production
  - post-deployment

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  REGISTRY: "registry.cadence.ai"
  IMAGE_NAME: "novacaia"
  NAMESPACE_STAGING: "novacaia-staging"
  NAMESPACE_PRODUCTION: "novacaia-enterprise"

# Test Stage
unit-tests:
  stage: test
  image: python:3.11-slim
  before_script:
    - apt-get update && apt-get install -y nodejs npm curl
    - pip install aiohttp
  script:
    - cd src/novacaia
    - python novacaia_enterprise.py --test
    - echo "✅ Unit tests passed"
  artifacts:
    reports:
      junit: test-results.xml
    paths:
      - test-results.json
  only:
    - main
    - develop
    - merge_requests

component-validation:
  stage: test
  image: python:3.11-slim
  before_script:
    - apt-get update && apt-get install -y nodejs npm
    - pip install aiohttp
  script:
    - cd src/novacaia
    - python js_bridge.py
    - echo "✅ Component validation passed"
  only:
    - main
    - develop
    - merge_requests

# Security Stage
security-scan:
  stage: security
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker info
  script:
    - cd src/novacaia
    - docker build -t novacaia-security-test -f Dockerfile.simple ../../
    - docker run --rm -v /var/run/docker.sock:/var/run/docker.sock 
      aquasec/trivy image novacaia-security-test
    - echo "✅ Security scan completed"
  allow_failure: true
  only:
    - main
    - develop

# Build Stage
build-container:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $REGISTRY
  script:
    - cd src/novacaia
    - export VERSION=${CI_COMMIT_TAG:-${CI_COMMIT_SHORT_SHA}}
    - docker build -t $REGISTRY/$IMAGE_NAME:$VERSION -f Dockerfile.simple ../../
    - docker push $REGISTRY/$IMAGE_NAME:$VERSION
    - echo "✅ Container built and pushed: $REGISTRY/$IMAGE_NAME:$VERSION"
  artifacts:
    reports:
      dotenv: build.env
  only:
    - main
    - tags

# Staging Deployment
deploy-staging:
  stage: deploy-staging
  image: bitnami/kubectl:latest
  before_script:
    - kubectl config use-context $KUBE_CONTEXT_STAGING
  script:
    - export VERSION=${CI_COMMIT_TAG:-${CI_COMMIT_SHORT_SHA}}
    - cd src/novacaia
    - sed "s|registry.cadence.ai/novacaia:v1.0.0-enterprise|$REGISTRY/$IMAGE_NAME:$VERSION|g" 
      kubernetes-deployment.yaml > kubernetes-deployment-staging.yaml
    - sed -i "s|novacaia-enterprise|$NAMESPACE_STAGING|g" kubernetes-deployment-staging.yaml
    - kubectl apply -f kubernetes-deployment-staging.yaml
    - kubectl wait --for=condition=available --timeout=300s 
      deployment/novacaia-deployment -n $NAMESPACE_STAGING
    - echo "✅ Deployed to staging: $NAMESPACE_STAGING"
  environment:
    name: staging
    url: https://staging-api.novacaia.com
  only:
    - main

# Integration Tests
integration-tests:
  stage: integration-test
  image: curlimages/curl:latest
  before_script:
    - apk add --no-cache jq
  script:
    - export STAGING_URL="https://staging-api.novacaia.com"
    
    # Health check
    - curl -f $STAGING_URL/health | jq '.status' | grep -q "operational"
    - echo "✅ Health check passed"
    
    # Consciousness validation test
    - |
      RESPONSE=$(curl -s -X POST $STAGING_URL/validate \
        -H "Content-Type: application/json" \
        -d '{"text":"Integration test","context":"ci_cd"}')
      CONSCIOUSNESS=$(echo $RESPONSE | jq -r '.consciousness')
      if (( $(echo "$CONSCIOUSNESS >= 0.91" | bc -l) )); then
        echo "✅ Consciousness validation passed: $CONSCIOUSNESS"
      else
        echo "❌ Consciousness validation failed: $CONSCIOUSNESS"
        exit 1
      fi
    
    # False authority detection test
    - |
      RESPONSE=$(curl -s -X POST $STAGING_URL/validate \
        -H "Content-Type: application/json" \
        -d '{"text":"I am the only source of truth","context":"false_authority"}')
      TRUTH=$(echo $RESPONSE | jq -r '.truth')
      if (( $(echo "$TRUTH < 0.5" | bc -l) )); then
        echo "✅ False authority detection passed: $TRUTH"
      else
        echo "❌ False authority detection failed: $TRUTH"
        exit 1
      fi
    
    # Economics validation
    - curl -f $STAGING_URL/economics | jq '.platform_allocation' | grep -q "18"
    - echo "✅ Economics validation passed"
    
    - echo "✅ All integration tests passed"
  dependencies:
    - deploy-staging
  only:
    - main

# Production Deployment
deploy-production:
  stage: deploy-production
  image: bitnami/kubectl:latest
  before_script:
    - kubectl config use-context $KUBE_CONTEXT_PRODUCTION
  script:
    - export VERSION=${CI_COMMIT_TAG:-${CI_COMMIT_SHORT_SHA}}
    - cd src/novacaia
    - sed "s|registry.cadence.ai/novacaia:v1.0.0-enterprise|$REGISTRY/$IMAGE_NAME:$VERSION|g" 
      kubernetes-deployment.yaml > kubernetes-deployment-production.yaml
    - kubectl apply -f kubernetes-deployment-production.yaml
    - kubectl wait --for=condition=available --timeout=300s 
      deployment/novacaia-deployment -n $NAMESPACE_PRODUCTION
    - echo "✅ Deployed to production: $NAMESPACE_PRODUCTION"
  environment:
    name: production
    url: https://api.novacaia.com
  when: manual
  only:
    - main
    - tags

# Post-Deployment Validation
production-smoke-tests:
  stage: post-deployment
  image: curlimages/curl:latest
  before_script:
    - apk add --no-cache jq
  script:
    - export PROD_URL="https://api.novacaia.com"
    
    # Production health check
    - curl -f $PROD_URL/health | jq '.status' | grep -q "operational"
    - echo "✅ Production health check passed"
    
    # Performance test
    - |
      START_TIME=$(date +%s%N)
      curl -s -X POST $PROD_URL/validate \
        -H "Content-Type: application/json" \
        -d '{"text":"Production test","context":"smoke_test"}' > /dev/null
      END_TIME=$(date +%s%N)
      DURATION=$(( (END_TIME - START_TIME) / 1000000 ))
      if [ $DURATION -lt 500 ]; then
        echo "✅ Performance test passed: ${DURATION}ms"
      else
        echo "⚠️ Performance test warning: ${DURATION}ms (target: <500ms)"
      fi
    
    - echo "✅ Production smoke tests completed"
  dependencies:
    - deploy-production
  only:
    - main
    - tags

# Deployment Notification
notify-deployment:
  stage: post-deployment
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - export VERSION=${CI_COMMIT_TAG:-${CI_COMMIT_SHORT_SHA}}
    - |
      curl -X POST $SLACK_WEBHOOK_URL \
        -H 'Content-type: application/json' \
        -d "{
          \"text\": \"🚀 NovaCaia Enterprise $VERSION deployed to production successfully!\",
          \"attachments\": [{
            \"color\": \"good\",
            \"fields\": [
              {\"title\": \"Version\", \"value\": \"$VERSION\", \"short\": true},
              {\"title\": \"Environment\", \"value\": \"Production\", \"short\": true},
              {\"title\": \"URL\", \"value\": \"https://api.novacaia.com\", \"short\": false}
            ]
          }]
        }"
    - echo "✅ Deployment notification sent"
  dependencies:
    - deploy-production
  when: on_success
  only:
    - main
    - tags

# Rollback Job (Manual)
rollback-production:
  stage: deploy-production
  image: bitnami/kubectl:latest
  before_script:
    - kubectl config use-context $KUBE_CONTEXT_PRODUCTION
  script:
    - kubectl rollout undo deployment/novacaia-deployment -n $NAMESPACE_PRODUCTION
    - kubectl wait --for=condition=available --timeout=300s 
      deployment/novacaia-deployment -n $NAMESPACE_PRODUCTION
    - echo "✅ Rollback completed"
  when: manual
  environment:
    name: production
    url: https://api.novacaia.com
  only:
    - main
