#!/usr/bin/env python3
"""
UUFT Gravity Test

This script tests whether the UUFT equation can accurately model gravitational interactions
by comparing its predictions with Newton's law of universal gravitation.
"""

import numpy as np
import matplotlib.pyplot as plt
import math
import os
from datetime import datetime

# Create results directory
RESULTS_DIR = "uuft_test_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

# Constants
PI = math.pi
GOLDEN_RATIO = (1 + math.sqrt(5)) / 2  # φ ≈ 1.618
PI_10_CUBED = PI * 10**3  # π10³

# Physical constants
G = 6.67430e-11  # Gravitational constant (m³/kg/s²)

def apply_uuft_equation(A, B, C):
    """
    Apply the UUFT equation: (A ⊗ B ⊕ C) × π10³
    
    Args:
        A: First input component
        B: Second input component
        C: Third input component
        
    Returns:
        float: Result of applying the UUFT equation
    """
    # Tensor product (⊗) with golden ratio
    tensor_product = A * B * GOLDEN_RATIO
    
    # Fusion operator (⊕) with inverse golden ratio
    fusion_result = tensor_product + (C * (1 / GOLDEN_RATIO))
    
    # Apply pi factor
    result = fusion_result * PI_10_CUBED
    
    return result

def calculate_newtonian_gravity(m1, m2, r):
    """
    Calculate gravitational force using Newton's law of universal gravitation.
    
    Args:
        m1: Mass of first object (kg)
        m2: Mass of second object (kg)
        r: Distance between objects (m)
        
    Returns:
        float: Gravitational force (N)
    """
    return G * m1 * m2 / (r**2)

def calculate_uuft_gravity(m1, m2, r):
    """
    Calculate gravitational force using the UUFT equation.
    
    For gravity, we'll map the UUFT components as follows:
    - A: First mass (m1)
    - B: Second mass (m2)
    - C: Distance factor (1/r²)
    
    Args:
        m1: Mass of first object (kg)
        m2: Mass of second object (kg)
        r: Distance between objects (m)
        
    Returns:
        float: Gravitational force predicted by UUFT (N)
    """
    # Scale inputs to avoid numerical issues
    # (gravitational constant is very small, masses can be very large)
    scale_factor = 1e-20
    
    A = m1 * scale_factor
    B = m2 * scale_factor
    C = 1 / (r**2) * scale_factor
    
    # Apply UUFT equation
    uuft_result = apply_uuft_equation(A, B, C)
    
    # Scale back to physical units
    # We need to calibrate this to match Newtonian gravity
    # This calibration factor would ideally be derived from first principles
    calibration_factor = G / (PI_10_CUBED * GOLDEN_RATIO * scale_factor)
    
    return uuft_result * calibration_factor

def test_gravity_predictions():
    """
    Test UUFT gravity predictions against Newtonian gravity.
    
    Returns:
        dict: Test results
    """
    print("Testing UUFT gravity predictions")
    
    # Test cases: various masses and distances
    test_cases = [
        # Earth-Sun system
        {"name": "Earth-Sun", "m1": 5.97e24, "m2": 1.99e30, "r": 1.50e11},
        
        # Earth-Moon system
        {"name": "Earth-Moon", "m1": 5.97e24, "m2": 7.35e22, "r": 3.84e8},
        
        # Jupiter-Sun system
        {"name": "Jupiter-Sun", "m1": 1.90e27, "m2": 1.99e30, "r": 7.78e11},
        
        # Binary star system (typical)
        {"name": "Binary Star", "m1": 2.0e30, "m2": 1.5e30, "r": 1.0e11},
        
        # Neutron star binary
        {"name": "Neutron Star Binary", "m1": 2.8e30, "m2": 2.8e30, "r": 1.0e9},
        
        # Galaxy-scale (rough approximation)
        {"name": "Galaxy Scale", "m1": 1.0e41, "m2": 1.0e30, "r": 1.0e20}
    ]
    
    results = []
    
    for case in test_cases:
        # Calculate Newtonian gravity
        newtonian_force = calculate_newtonian_gravity(case["m1"], case["m2"], case["r"])
        
        # Calculate UUFT gravity
        uuft_force = calculate_uuft_gravity(case["m1"], case["m2"], case["r"])
        
        # Calculate accuracy
        if newtonian_force != 0:
            accuracy = 1.0 - abs(uuft_force - newtonian_force) / newtonian_force
        else:
            accuracy = 0.0
        
        # Store results
        result = {
            "name": case["name"],
            "m1": case["m1"],
            "m2": case["m2"],
            "r": case["r"],
            "newtonian_force": newtonian_force,
            "uuft_force": uuft_force,
            "accuracy": accuracy
        }
        
        results.append(result)
        
        print(f"  {case['name']}: Newtonian = {newtonian_force:.4e} N, UUFT = {uuft_force:.4e} N, Accuracy = {accuracy:.6f}")
    
    # Calculate overall accuracy
    overall_accuracy = np.mean([r["accuracy"] for r in results])
    print(f"\nOverall Accuracy: {overall_accuracy:.6f}")
    
    return {
        "results": results,
        "overall_accuracy": overall_accuracy
    }

def test_gravity_scaling():
    """
    Test how UUFT gravity predictions scale with mass and distance.
    
    Returns:
        dict: Test results
    """
    print("\nTesting UUFT gravity scaling")
    
    # Test mass scaling (fixed distance)
    fixed_distance = 1.0e11  # 100 million km
    base_mass = 1.0e30  # Solar mass
    mass_factors = np.logspace(-3, 3, 7)  # 10^-3 to 10^3
    
    mass_scaling_results = []
    
    for factor in mass_factors:
        m1 = base_mass
        m2 = base_mass * factor
        
        newtonian_force = calculate_newtonian_gravity(m1, m2, fixed_distance)
        uuft_force = calculate_uuft_gravity(m1, m2, fixed_distance)
        
        mass_scaling_results.append({
            "mass_factor": factor,
            "m1": m1,
            "m2": m2,
            "r": fixed_distance,
            "newtonian_force": newtonian_force,
            "uuft_force": uuft_force
        })
    
    # Test distance scaling (fixed masses)
    fixed_mass1 = 1.0e30  # Solar mass
    fixed_mass2 = 1.0e30  # Solar mass
    distance_factors = np.logspace(-1, 1, 7)  # 10^-1 to 10^1
    base_distance = 1.0e11  # 100 million km
    
    distance_scaling_results = []
    
    for factor in distance_factors:
        r = base_distance * factor
        
        newtonian_force = calculate_newtonian_gravity(fixed_mass1, fixed_mass2, r)
        uuft_force = calculate_uuft_gravity(fixed_mass1, fixed_mass2, r)
        
        distance_scaling_results.append({
            "distance_factor": factor,
            "m1": fixed_mass1,
            "m2": fixed_mass2,
            "r": r,
            "newtonian_force": newtonian_force,
            "uuft_force": uuft_force
        })
    
    return {
        "mass_scaling": mass_scaling_results,
        "distance_scaling": distance_scaling_results
    }

def plot_results(prediction_results, scaling_results):
    """
    Plot the results of the gravity tests.
    
    Args:
        prediction_results: Results from test_gravity_predictions()
        scaling_results: Results from test_gravity_scaling()
    """
    # Plot prediction accuracy
    plt.figure(figsize=(10, 6))
    
    names = [r["name"] for r in prediction_results["results"]]
    accuracies = [r["accuracy"] for r in prediction_results["results"]]
    
    plt.bar(names, accuracies)
    plt.axhline(y=0.9973, color='r', linestyle='--', label='Quantum Ceiling (99.73%)')
    plt.xlabel('Test Case')
    plt.ylabel('Accuracy')
    plt.title('UUFT Gravity Prediction Accuracy')
    plt.ylim(0, 1.05)
    plt.xticks(rotation=45)
    plt.legend()
    plt.tight_layout()
    
    plt.savefig(os.path.join(RESULTS_DIR, "uuft_gravity_accuracy.png"))
    plt.close()
    
    # Plot mass scaling
    plt.figure(figsize=(10, 6))
    
    mass_factors = [r["mass_factor"] for r in scaling_results["mass_scaling"]]
    newtonian_forces = [r["newtonian_force"] for r in scaling_results["mass_scaling"]]
    uuft_forces = [r["uuft_force"] for r in scaling_results["mass_scaling"]]
    
    plt.loglog(mass_factors, newtonian_forces, 'o-', label='Newtonian Gravity')
    plt.loglog(mass_factors, uuft_forces, 's-', label='UUFT Gravity')
    plt.xlabel('Mass Factor (m2/m1)')
    plt.ylabel('Gravitational Force (N)')
    plt.title('Gravity Scaling with Mass')
    plt.grid(True, which="both", ls="-")
    plt.legend()
    
    plt.savefig(os.path.join(RESULTS_DIR, "uuft_gravity_mass_scaling.png"))
    plt.close()
    
    # Plot distance scaling
    plt.figure(figsize=(10, 6))
    
    distance_factors = [r["distance_factor"] for r in scaling_results["distance_scaling"]]
    newtonian_forces = [r["newtonian_force"] for r in scaling_results["distance_scaling"]]
    uuft_forces = [r["uuft_force"] for r in scaling_results["distance_scaling"]]
    
    plt.loglog(distance_factors, newtonian_forces, 'o-', label='Newtonian Gravity')
    plt.loglog(distance_factors, uuft_forces, 's-', label='UUFT Gravity')
    plt.xlabel('Distance Factor (r/r0)')
    plt.ylabel('Gravitational Force (N)')
    plt.title('Gravity Scaling with Distance')
    plt.grid(True, which="both", ls="-")
    plt.legend()
    
    plt.savefig(os.path.join(RESULTS_DIR, "uuft_gravity_distance_scaling.png"))
    plt.close()

def save_results(prediction_results, scaling_results):
    """
    Save the results to a text file.
    
    Args:
        prediction_results: Results from test_gravity_predictions()
        scaling_results: Results from test_gravity_scaling()
    """
    with open(os.path.join(RESULTS_DIR, "uuft_gravity_test_results.txt"), "w") as f:
        f.write("UUFT Gravity Test Results\n")
        f.write("========================\n")
        f.write(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Write prediction results
        f.write("Gravity Prediction Results:\n")
        f.write("--------------------------\n")
        
        for result in prediction_results["results"]:
            f.write(f"\nTest Case: {result['name']}\n")
            f.write(f"  Mass 1: {result['m1']:.2e} kg\n")
            f.write(f"  Mass 2: {result['m2']:.2e} kg\n")
            f.write(f"  Distance: {result['r']:.2e} m\n")
            f.write(f"  Newtonian Force: {result['newtonian_force']:.6e} N\n")
            f.write(f"  UUFT Force: {result['uuft_force']:.6e} N\n")
            f.write(f"  Accuracy: {result['accuracy']:.6f}\n")
        
        f.write(f"\nOverall Accuracy: {prediction_results['overall_accuracy']:.6f}\n")
        
        # Write scaling results
        f.write("\nMass Scaling Results:\n")
        f.write("--------------------\n")
        
        for result in scaling_results["mass_scaling"]:
            f.write(f"\nMass Factor: {result['mass_factor']:.2e}\n")
            f.write(f"  Newtonian Force: {result['newtonian_force']:.6e} N\n")
            f.write(f"  UUFT Force: {result['uuft_force']:.6e} N\n")
        
        f.write("\nDistance Scaling Results:\n")
        f.write("------------------------\n")
        
        for result in scaling_results["distance_scaling"]:
            f.write(f"\nDistance Factor: {result['distance_factor']:.2e}\n")
            f.write(f"  Newtonian Force: {result['newtonian_force']:.6e} N\n")
            f.write(f"  UUFT Force: {result['uuft_force']:.6e} N\n")

def main():
    """Run the UUFT gravity test."""
    print("Running UUFT Gravity Test")
    print("========================")
    
    # Test gravity predictions
    prediction_results = test_gravity_predictions()
    
    # Test gravity scaling
    scaling_results = test_gravity_scaling()
    
    # Plot results
    plot_results(prediction_results, scaling_results)
    
    # Save results
    save_results(prediction_results, scaling_results)
    
    print("\nTest complete. Results saved to the uuft_test_results directory.")
    
    return prediction_results, scaling_results

if __name__ == "__main__":
    main()
