/**
 * IP Restriction Controller
 * 
 * This controller handles API requests related to IP-based access restrictions.
 */

const IpRestrictionService = require('../services/IpRestrictionService');
const { ValidationError } = require('../utils/errors');

class IpRestrictionController {
  constructor() {
    this.ipRestrictionService = new IpRestrictionService();
  }

  /**
   * Get IP restrictions
   */
  async getRestrictions(req, res, next) {
    try {
      const restrictions = await this.ipRestrictionService.loadRestrictions();
      
      res.json({
        success: true,
        data: restrictions
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update IP restrictions configuration
   */
  async updateConfig(req, res, next) {
    try {
      const { enabled, mode } = req.body;
      
      if (enabled === undefined && mode === undefined) {
        throw new ValidationError('At least one configuration parameter is required');
      }
      
      const restrictions = await this.ipRestrictionService.updateConfig({
        enabled,
        mode
      });
      
      res.json({
        success: true,
        data: restrictions
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Add IP to allowlist
   */
  async addToAllowlist(req, res, next) {
    try {
      const { ip } = req.body;
      
      if (!ip) {
        throw new ValidationError('IP address or range is required');
      }
      
      const restrictions = await this.ipRestrictionService.addToAllowlist(ip);
      
      res.json({
        success: true,
        data: restrictions
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Remove IP from allowlist
   */
  async removeFromAllowlist(req, res, next) {
    try {
      const { ip } = req.body;
      
      if (!ip) {
        throw new ValidationError('IP address or range is required');
      }
      
      const restrictions = await this.ipRestrictionService.removeFromAllowlist(ip);
      
      res.json({
        success: true,
        data: restrictions
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Add IP to blocklist
   */
  async addToBlocklist(req, res, next) {
    try {
      const { ip } = req.body;
      
      if (!ip) {
        throw new ValidationError('IP address or range is required');
      }
      
      const restrictions = await this.ipRestrictionService.addToBlocklist(ip);
      
      res.json({
        success: true,
        data: restrictions
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Remove IP from blocklist
   */
  async removeFromBlocklist(req, res, next) {
    try {
      const { ip } = req.body;
      
      if (!ip) {
        throw new ValidationError('IP address or range is required');
      }
      
      const restrictions = await this.ipRestrictionService.removeFromBlocklist(ip);
      
      res.json({
        success: true,
        data: restrictions
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Add rule
   */
  async addRule(req, res, next) {
    try {
      const { rule } = req.body;
      
      if (!rule) {
        throw new ValidationError('Rule is required');
      }
      
      const restrictions = await this.ipRestrictionService.addRule(rule);
      
      res.json({
        success: true,
        data: restrictions
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Remove rule
   */
  async removeRule(req, res, next) {
    try {
      const { name } = req.body;
      
      if (!name) {
        throw new ValidationError('Rule name is required');
      }
      
      const restrictions = await this.ipRestrictionService.removeRule(name);
      
      res.json({
        success: true,
        data: restrictions
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reset to default
   */
  async resetToDefault(req, res, next) {
    try {
      const restrictions = await this.ipRestrictionService.resetToDefault();
      
      res.json({
        success: true,
        data: restrictions
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new IpRestrictionController();
