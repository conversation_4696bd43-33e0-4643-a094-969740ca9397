/**
 * Human-System Coherence Interface
 * 
 * This module implements the Human-System Coherence Interface component of the CSDE.
 * It optimizes UI safety via the Human Coherence Index (Ψₕ).
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * HumanSystemCoherenceInterface class
 */
class HumanSystemCoherenceInterface extends EventEmitter {
  /**
   * Create a new HumanSystemCoherenceInterface
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      updateInterval: 2000, // ms
      enableLogging: true,
      enableMetrics: true,
      thresholds: {
        coherence: {
          low: 0.3,
          medium: 0.6,
          high: 0.8
        }
      },
      adaptationRules: {
        cognitiveLoad: {
          high: [
            { action: 'simplifyUI', value: 0.7 },
            { action: 'reduceNotifications', value: 0.8 },
            { action: 'prioritizeContent', value: 0.9 }
          ],
          medium: [
            { action: 'simplifyUI', value: 0.4 },
            { action: 'reduceNotifications', value: 0.5 }
          ],
          low: []
        },
        stressLevel: {
          high: [
            { action: 'calmColorScheme', value: 0.8 },
            { action: 'increaseWhitespace', value: 0.7 },
            { action: 'reduceMotion', value: 0.9 }
          ],
          medium: [
            { action: 'calmColorScheme', value: 0.5 },
            { action: 'reduceMotion', value: 0.6 }
          ],
          low: []
        }
      },
      ...options
    };
    
    // Initialize state
    this.state = {
      humanCoherenceIndex: 0.7,
      cognitiveLoad: 0.5,
      stressLevel: 0.3,
      fatigue: 0.4,
      attention: 0.8,
      expertise: 0.6,
      activeAdaptations: [],
      adaptationHistory: [],
      isRunning: false,
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalUpdates: 0,
      adaptationsApplied: 0,
      adaptationsRemoved: 0
    };
    
    if (this.options.enableLogging) {
      console.log('HumanSystemCoherenceInterface initialized');
    }
  }
  
  /**
   * Start the interface
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('HumanSystemCoherenceInterface is already running');
      }
      return false;
    }
    
    this.state.isRunning = true;
    this._startUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('HumanSystemCoherenceInterface started');
    }
    
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the interface
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('HumanSystemCoherenceInterface is not running');
      }
      return false;
    }
    
    this.state.isRunning = false;
    this._stopUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('HumanSystemCoherenceInterface stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Update human coherence index (Ψₕ)
   * @param {Object} humanData - Human data
   * @returns {number} - Human coherence index
   */
  updateHumanCoherenceIndex(humanData) {
    const startTime = performance.now();
    
    if (!humanData || typeof humanData !== 'object') {
      throw new Error('Human data must be an object');
    }
    
    const {
      cognitiveLoad = this.state.cognitiveLoad,
      stressLevel = this.state.stressLevel,
      fatigue = this.state.fatigue,
      attention = this.state.attention,
      expertise = this.state.expertise
    } = humanData;
    
    // Update state with new values
    this.state.cognitiveLoad = this._clamp(cognitiveLoad);
    this.state.stressLevel = this._clamp(stressLevel);
    this.state.fatigue = this._clamp(fatigue);
    this.state.attention = this._clamp(attention);
    this.state.expertise = this._clamp(expertise);
    
    // Calculate human coherence index using 18/82 principle
    // 18% weight to expertise, 82% weight to the average of other factors
    const otherFactorsAvg = (
      (1 - this.state.cognitiveLoad) + // Lower cognitive load is better
      (1 - this.state.stressLevel) +   // Lower stress is better
      (1 - this.state.fatigue) +       // Lower fatigue is better
      this.state.attention              // Higher attention is better
    ) / 4;
    
    const humanCoherenceIndex = (
      0.18 * this.state.expertise +
      0.82 * otherFactorsAvg
    );
    
    // Update state
    this.state.humanCoherenceIndex = this._clamp(humanCoherenceIndex);
    
    // Update adaptations
    this._updateAdaptations();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    
    // Emit update event
    this.emit('coherence-update', {
      humanCoherenceIndex: this.state.humanCoherenceIndex,
      timestamp: Date.now()
    });
    
    return this.state.humanCoherenceIndex;
  }
  
  /**
   * Get current state
   * @returns {Object} - Current state
   */
  getState() {
    return { ...this.state };
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Get active adaptations
   * @returns {Array} - Active adaptations
   */
  getActiveAdaptations() {
    return [...this.state.activeAdaptations];
  }
  
  /**
   * Get adaptation history
   * @param {number} limit - Maximum number of history items to return
   * @returns {Array} - Adaptation history
   */
  getAdaptationHistory(limit = 10) {
    return this.state.adaptationHistory.slice(0, limit);
  }
  
  /**
   * Update adaptations based on current state
   * @private
   */
  _updateAdaptations() {
    const { cognitiveLoad, stressLevel } = this.state;
    const { adaptationRules } = this.options;
    
    // Determine cognitive load level
    let cognitiveLoadLevel = 'low';
    if (cognitiveLoad >= 0.7) {
      cognitiveLoadLevel = 'high';
    } else if (cognitiveLoad >= 0.4) {
      cognitiveLoadLevel = 'medium';
    }
    
    // Determine stress level
    let stressLevelCategory = 'low';
    if (stressLevel >= 0.7) {
      stressLevelCategory = 'high';
    } else if (stressLevel >= 0.4) {
      stressLevelCategory = 'medium';
    }
    
    // Get adaptations for current levels
    const cognitiveLoadAdaptations = adaptationRules.cognitiveLoad[cognitiveLoadLevel] || [];
    const stressLevelAdaptations = adaptationRules.stressLevel[stressLevelCategory] || [];
    
    // Combine adaptations
    const newAdaptations = [...cognitiveLoadAdaptations, ...stressLevelAdaptations];
    
    // Compare with current adaptations and update
    this._applyAdaptations(newAdaptations);
  }
  
  /**
   * Apply adaptations
   * @param {Array} newAdaptations - New adaptations to apply
   * @private
   */
  _applyAdaptations(newAdaptations) {
    const currentAdaptations = this.state.activeAdaptations;
    
    // Find adaptations to add
    const adaptationsToAdd = newAdaptations.filter(newAdapt => {
      return !currentAdaptations.some(currentAdapt => 
        currentAdapt.action === newAdapt.action
      );
    });
    
    // Find adaptations to remove
    const adaptationsToRemove = currentAdaptations.filter(currentAdapt => {
      return !newAdaptations.some(newAdapt => 
        newAdapt.action === currentAdapt.action
      );
    });
    
    // Apply changes
    if (adaptationsToAdd.length > 0 || adaptationsToRemove.length > 0) {
      // Remove adaptations
      adaptationsToRemove.forEach(adaptation => {
        this._removeAdaptation(adaptation);
      });
      
      // Add adaptations
      adaptationsToAdd.forEach(adaptation => {
        this._addAdaptation(adaptation);
      });
    }
  }
  
  /**
   * Add adaptation
   * @param {Object} adaptation - Adaptation to add
   * @private
   */
  _addAdaptation(adaptation) {
    // Add timestamp
    const adaptationWithTimestamp = {
      ...adaptation,
      appliedAt: Date.now()
    };
    
    // Add to active adaptations
    this.state.activeAdaptations.push(adaptationWithTimestamp);
    
    // Add to history
    this.state.adaptationHistory.unshift({
      ...adaptationWithTimestamp,
      type: 'add'
    });
    
    // Limit history size
    if (this.state.adaptationHistory.length > 100) {
      this.state.adaptationHistory.pop();
    }
    
    // Update metrics
    this.metrics.adaptationsApplied++;
    
    // Emit event
    this.emit('adaptation-added', adaptationWithTimestamp);
    
    if (this.options.enableLogging) {
      console.log(`HumanSystemCoherenceInterface: Added adaptation ${adaptation.action} (${adaptation.value})`);
    }
  }
  
  /**
   * Remove adaptation
   * @param {Object} adaptation - Adaptation to remove
   * @private
   */
  _removeAdaptation(adaptation) {
    // Find adaptation index
    const index = this.state.activeAdaptations.findIndex(a => a.action === adaptation.action);
    
    if (index !== -1) {
      // Remove from active adaptations
      const removedAdaptation = this.state.activeAdaptations.splice(index, 1)[0];
      
      // Add to history
      this.state.adaptationHistory.unshift({
        ...removedAdaptation,
        removedAt: Date.now(),
        type: 'remove'
      });
      
      // Limit history size
      if (this.state.adaptationHistory.length > 100) {
        this.state.adaptationHistory.pop();
      }
      
      // Update metrics
      this.metrics.adaptationsRemoved++;
      
      // Emit event
      this.emit('adaptation-removed', removedAdaptation);
      
      if (this.options.enableLogging) {
        console.log(`HumanSystemCoherenceInterface: Removed adaptation ${adaptation.action}`);
      }
    }
  }
  
  /**
   * Start update interval
   * @private
   */
  _startUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
    }
    
    this._updateInterval = setInterval(() => {
      if (this.state.isRunning) {
        // In a real implementation, this would fetch real-time data
        // For now, just simulate human data changes
        this._simulateHumanDataChanges();
      }
    }, this.options.updateInterval);
  }
  
  /**
   * Stop update interval
   * @private
   */
  _stopUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
      this._updateInterval = null;
    }
  }
  
  /**
   * Simulate human data changes
   * @private
   */
  _simulateHumanDataChanges() {
    // Simulate random changes to human data
    const cognitiveLoadChange = (Math.random() - 0.5) * 0.1;
    const stressLevelChange = (Math.random() - 0.5) * 0.1;
    const fatigueChange = (Math.random() - 0.5) * 0.1;
    const attentionChange = (Math.random() - 0.5) * 0.1;
    
    // Update human data
    this.updateHumanCoherenceIndex({
      cognitiveLoad: this.state.cognitiveLoad + cognitiveLoadChange,
      stressLevel: this.state.stressLevel + stressLevelChange,
      fatigue: this.state.fatigue + fatigueChange,
      attention: this.state.attention + attentionChange,
      expertise: this.state.expertise // Expertise doesn't change rapidly
    });
  }
  
  /**
   * Clamp value between 0 and 1
   * @param {number} value - Value to clamp
   * @returns {number} - Clamped value
   * @private
   */
  _clamp(value) {
    return Math.max(0, Math.min(1, value));
  }
}

module.exports = HumanSystemCoherenceInterface;
