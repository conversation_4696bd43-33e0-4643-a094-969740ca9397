import React from 'react';
import {
  <PERSON>agramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  VerticalArrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow
} from '../../components/DiagramComponents';

const AutomatedAuditTrail = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>AUTOMATED AUDIT TRAIL GENERATION</ContainerLabel>
      </ContainerBox>
      
      {/* Main flow components */}
      <ComponentBox left="80px" top="100px" width="130px">
        <ComponentNumber>201</ComponentNumber>
        <ComponentLabel>Fraud Detection</ComponentLabel>
        System
      </ComponentBox>
      
      <Arrow left="210px" top="130px" width="100px" />
      
      <ComponentBox left="320px" top="100px" width="130px">
        <ComponentNumber>202</ComponentNumber>
        <ComponentLabel>Event Capture</ComponentLabel>
        Engine
      </ComponentBox>
      
      <Arrow left="450px" top="130px" width="100px" />
      
      <ComponentBox left="560px" top="100px" width="130px">
        <ComponentNumber>203</ComponentNumber>
        <ComponentLabel>Audit Formatting</ComponentLabel>
        Module
      </ComponentBox>
      
      <Arrow left="625px" top="160px" width="2px" height="100px" />
      
      <ComponentBox left="560px" top="260px" width="130px">
        <ComponentNumber>204</ComponentNumber>
        <ComponentLabel>Regulatory Storage</ComponentLabel>
        System
      </ComponentBox>
      
      {/* Compliance Rules Engine */}
      <ComponentBox left="320px" top="260px" width="130px">
        <ComponentNumber>205</ComponentNumber>
        <ComponentLabel>Compliance Rules</ComponentLabel>
        Engine
      </ComponentBox>
      
      {/* Connecting arrows */}
      <VerticalArrow left="145px" top="160px" height="100px" />
      
      <Arrow left="145px" top="260px" width="175px" />
      
      <Arrow left="450px" top="290px" width="110px" />
      
      {/* Curved arrow from Fraud Detection to Compliance Rules */}
      <CurvedArrow width="240" height="160" left="145" top="130">
        <path
          d="M 0,0 Q 120,80 175,130"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="175,130 165,122 168,132"
          fill="#333"
        />
      </CurvedArrow>
      
      {/* Additional components */}
      <ComponentBox left="80px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>206</ComponentNumber>
        <ComponentLabel>Chain of Custody</ComponentLabel>
        Verification
      </ComponentBox>
      
      <ComponentBox left="240px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>207</ComponentNumber>
        <ComponentLabel>Automated Evidence</ComponentLabel>
        Collection
      </ComponentBox>
      
      <ComponentBox left="400px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>208</ComponentNumber>
        <ComponentLabel>Regulatory Reporting</ComponentLabel>
        Interface
      </ComponentBox>
      
      <ComponentBox left="560px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>209</ComponentNumber>
        <ComponentLabel>Audit Trail</ComponentLabel>
        Verification
      </ComponentBox>
    </DiagramFrame>
  );
};

export default AutomatedAuditTrail;
