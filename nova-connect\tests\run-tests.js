/**
 * NovaConnect UAC Test Runner
 * 
 * This script runs all tests for the NovaConnect UAC and generates a comprehensive report.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');

// Configuration
const config = {
  testTypes: [
    { name: 'Unit Tests', command: 'npm run test:unit', required: true },
    { name: 'Integration Tests', command: 'npm run test:integration', required: true },
    { name: 'Performance Tests', command: 'npm run test:performance', required: false },
    { name: 'Security Tests', command: 'npm run test:security', required: false }
  ],
  reportDir: path.join(__dirname, '../reports'),
  reportFile: 'test-report.json',
  htmlReportFile: 'test-report.html',
  coverageThreshold: {
    branches: 96,
    functions: 96,
    lines: 96,
    statements: 96
  }
};

// Ensure report directory exists
if (!fs.existsSync(config.reportDir)) {
  fs.mkdirSync(config.reportDir, { recursive: true });
}

// Initialize report
const report = {
  timestamp: new Date().toISOString(),
  summary: {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    skippedTests: 0,
    coverage: {
      branches: 0,
      functions: 0,
      lines: 0,
      statements: 0
    },
    duration: 0
  },
  testSuites: []
};

// Run tests
console.log('Starting NovaConnect UAC Test Runner');
console.log('=====================================');

const startTime = performance.now();

// Run each test type
for (const testType of config.testTypes) {
  console.log(`\nRunning ${testType.name}...`);
  
  try {
    const output = execSync(testType.command, { encoding: 'utf8' });
    console.log(output);
    
    // Parse test results
    const testSuite = {
      name: testType.name,
      status: 'passed',
      output: output
    };
    
    // Extract test counts (this is a simple implementation, adjust based on your test output format)
    const totalMatch = output.match(/Tests:\\s+(\\d+)\\s+passed/i);
    const passedMatch = output.match(/Tests:\\s+(\\d+)\\s+passed/i);
    const failedMatch = output.match(/(\\d+)\\s+failed/i);
    const skippedMatch = output.match(/(\\d+)\\s+skipped/i);
    
    testSuite.totalTests = totalMatch ? parseInt(totalMatch[1]) : 0;
    testSuite.passedTests = passedMatch ? parseInt(passedMatch[1]) : 0;
    testSuite.failedTests = failedMatch ? parseInt(failedMatch[1]) : 0;
    testSuite.skippedTests = skippedMatch ? parseInt(skippedMatch[1]) : 0;
    
    // Extract coverage information if available
    if (output.includes('Coverage')) {
      const branchMatch = output.match(/Branch\\s+(\\d+\\.\\d+)%/i);
      const funcMatch = output.match(/Function\\s+(\\d+\\.\\d+)%/i);
      const lineMatch = output.match(/Line\\s+(\\d+\\.\\d+)%/i);
      const stmtMatch = output.match(/Statement\\s+(\\d+\\.\\d+)%/i);
      
      testSuite.coverage = {
        branches: branchMatch ? parseFloat(branchMatch[1]) : 0,
        functions: funcMatch ? parseFloat(funcMatch[1]) : 0,
        lines: lineMatch ? parseFloat(lineMatch[1]) : 0,
        statements: stmtMatch ? parseFloat(stmtMatch[1]) : 0
      };
      
      // Update overall coverage
      report.summary.coverage.branches += testSuite.coverage.branches;
      report.summary.coverage.functions += testSuite.coverage.functions;
      report.summary.coverage.lines += testSuite.coverage.lines;
      report.summary.coverage.statements += testSuite.coverage.statements;
    }
    
    // Update overall counts
    report.summary.totalTests += testSuite.totalTests;
    report.summary.passedTests += testSuite.passedTests;
    report.summary.failedTests += testSuite.failedTests;
    report.summary.skippedTests += testSuite.skippedTests;
    
    report.testSuites.push(testSuite);
  } catch (error) {
    console.error(`Error running ${testType.name}:`, error.message);
    
    const testSuite = {
      name: testType.name,
      status: 'failed',
      error: error.message,
      output: error.stdout || ''
    };
    
    report.testSuites.push(testSuite);
    
    // If this test type is required, exit with error
    if (testType.required) {
      console.error(`Required test type ${testType.name} failed. Exiting.`);
      process.exit(1);
    }
  }
}

// Calculate average coverage
const coverageTypes = Object.keys(report.summary.coverage);
coverageTypes.forEach(type => {
  report.summary.coverage[type] = report.summary.coverage[type] / config.testTypes.length;
});

// Calculate total duration
const endTime = performance.now();
report.summary.duration = endTime - startTime;

// Save report
fs.writeFileSync(
  path.join(config.reportDir, config.reportFile),
  JSON.stringify(report, null, 2)
);

// Generate HTML report
const htmlReport = generateHtmlReport(report);
fs.writeFileSync(
  path.join(config.reportDir, config.htmlReportFile),
  htmlReport
);

console.log('\nTest Run Complete');
console.log('=================');
console.log(`Total Tests: ${report.summary.totalTests}`);
console.log(`Passed: ${report.summary.passedTests}`);
console.log(`Failed: ${report.summary.failedTests}`);
console.log(`Skipped: ${report.summary.skippedTests}`);
console.log(`Coverage: ${report.summary.coverage.statements.toFixed(2)}%`);
console.log(`Duration: ${(report.summary.duration / 1000).toFixed(2)}s`);
console.log(`Report saved to: ${path.join(config.reportDir, config.htmlReportFile)}`);

// Check if coverage meets threshold
const coveragePassed = Object.keys(config.coverageThreshold).every(
  type => report.summary.coverage[type] >= config.coverageThreshold[type]
);

if (!coveragePassed) {
  console.error('\nCoverage does not meet threshold:');
  Object.keys(config.coverageThreshold).forEach(type => {
    const actual = report.summary.coverage[type];
    const threshold = config.coverageThreshold[type];
    console.error(`${type}: ${actual.toFixed(2)}% (threshold: ${threshold}%)`);
  });
  process.exit(1);
}

// Exit with appropriate code
if (report.summary.failedTests > 0) {
  console.error('\nSome tests failed. See report for details.');
  process.exit(1);
} else {
  console.log('\nAll tests passed!');
  process.exit(0);
}

/**
 * Generate HTML report
 * @param {Object} report - Test report
 * @returns {string} - HTML report
 */
function generateHtmlReport(report) {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaConnect UAC Test Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .summary {
      background-color: #f5f5f5;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .summary-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .test-suite {
      margin-bottom: 30px;
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
    }
    .test-suite-header {
      padding: 10px 20px;
      background-color: #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .test-suite-body {
      padding: 20px;
    }
    .passed {
      color: #2ecc71;
    }
    .failed {
      color: #e74c3c;
    }
    .coverage-bar {
      height: 20px;
      background-color: #ecf0f1;
      border-radius: 10px;
      overflow: hidden;
      margin-top: 5px;
    }
    .coverage-progress {
      height: 100%;
      background-color: #2ecc71;
    }
    .coverage-progress.warning {
      background-color: #f39c12;
    }
    .coverage-progress.danger {
      background-color: #e74c3c;
    }
    .coverage-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      margin-top: 20px;
    }
    .coverage-item {
      background-color: #f9f9f9;
      padding: 15px;
      border-radius: 5px;
    }
    .output {
      background-color: #f9f9f9;
      padding: 15px;
      border-radius: 5px;
      font-family: monospace;
      white-space: pre-wrap;
      max-height: 300px;
      overflow-y: auto;
    }
    .toggle-button {
      background-color: #0066cc;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 3px;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <h1>NovaConnect UAC Test Report</h1>
  <p>Generated on: ${new Date(report.timestamp).toLocaleString()}</p>
  
  <div class="summary">
    <h2>Summary</h2>
    <div class="summary-item">
      <span>Total Tests:</span>
      <span>${report.summary.totalTests}</span>
    </div>
    <div class="summary-item">
      <span>Passed Tests:</span>
      <span class="passed">${report.summary.passedTests}</span>
    </div>
    <div class="summary-item">
      <span>Failed Tests:</span>
      <span class="failed">${report.summary.failedTests}</span>
    </div>
    <div class="summary-item">
      <span>Skipped Tests:</span>
      <span>${report.summary.skippedTests}</span>
    </div>
    <div class="summary-item">
      <span>Duration:</span>
      <span>${(report.summary.duration / 1000).toFixed(2)}s</span>
    </div>
    
    <h3>Coverage</h3>
    <div class="coverage-grid">
      ${Object.keys(report.summary.coverage).map(type => {
        const coverage = report.summary.coverage[type];
        const threshold = config.coverageThreshold[type];
        let colorClass = 'danger';
        if (coverage >= threshold) {
          colorClass = 'success';
        } else if (coverage >= threshold * 0.8) {
          colorClass = 'warning';
        }
        
        return `
          <div class="coverage-item">
            <div>${type.charAt(0).toUpperCase() + type.slice(1)}</div>
            <div>${coverage.toFixed(2)}% (threshold: ${threshold}%)</div>
            <div class="coverage-bar">
              <div class="coverage-progress ${colorClass}" style="width: ${coverage}%"></div>
            </div>
          </div>
        `;
      }).join('')}
    </div>
  </div>
  
  <h2>Test Suites</h2>
  ${report.testSuites.map((suite, index) => `
    <div class="test-suite">
      <div class="test-suite-header">
        <h3>${suite.name}</h3>
        <span class="${suite.status === 'passed' ? 'passed' : 'failed'}">${suite.status.toUpperCase()}</span>
      </div>
      <div class="test-suite-body">
        ${suite.totalTests ? `
          <div class="summary-item">
            <span>Total Tests:</span>
            <span>${suite.totalTests}</span>
          </div>
          <div class="summary-item">
            <span>Passed Tests:</span>
            <span class="passed">${suite.passedTests}</span>
          </div>
          <div class="summary-item">
            <span>Failed Tests:</span>
            <span class="failed">${suite.failedTests}</span>
          </div>
          <div class="summary-item">
            <span>Skipped Tests:</span>
            <span>${suite.skippedTests}</span>
          </div>
        ` : ''}
        
        ${suite.coverage ? `
          <h4>Coverage</h4>
          <div class="coverage-grid">
            ${Object.keys(suite.coverage).map(type => {
              const coverage = suite.coverage[type];
              const threshold = config.coverageThreshold[type];
              let colorClass = 'danger';
              if (coverage >= threshold) {
                colorClass = 'success';
              } else if (coverage >= threshold * 0.8) {
                colorClass = 'warning';
              }
              
              return `
                <div class="coverage-item">
                  <div>${type.charAt(0).toUpperCase() + type.slice(1)}</div>
                  <div>${coverage.toFixed(2)}% (threshold: ${threshold}%)</div>
                  <div class="coverage-bar">
                    <div class="coverage-progress ${colorClass}" style="width: ${coverage}%"></div>
                  </div>
                </div>
              `;
            }).join('')}
          </div>
        ` : ''}
        
        <h4>Output</h4>
        <button class="toggle-button" onclick="toggleOutput(${index})">Toggle Output</button>
        <div id="output-${index}" class="output" style="display: none;">
          ${suite.output || suite.error || 'No output available'}
        </div>
      </div>
    </div>
  `).join('')}
  
  <script>
    function toggleOutput(index) {
      const output = document.getElementById('output-' + index);
      output.style.display = output.style.display === 'none' ? 'block' : 'none';
    }
  </script>
</body>
</html>`;
}
