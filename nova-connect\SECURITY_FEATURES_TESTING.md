# NovaConnect Security Features Testing Guide

This guide provides instructions for testing the security features implemented in the NovaConnect API.

## Security Features Overview

The NovaConnect API includes the following security features:

1. **Rate Limiting**: Prevents abuse by limiting the number of requests a client can make in a given time period.
2. **Brute Force Protection**: Prevents brute force attacks by temporarily blocking accounts after multiple failed login attempts.
3. **IP-based Restrictions**: Allows configuring allowlists, blocklists, and custom rules to control access to the API.
4. **Authentication Audit Logging**: Tracks and monitors authentication-related activities for security and compliance purposes.

## Testing Setup

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)
- Running instance of NovaConnect API

### Starting the API Server

1. Navigate to the NovaConnect directory:
   ```
   cd nova-connect
   ```

2. Install dependencies (if not already installed):
   ```
   npm install
   ```

3. Start the API server:
   ```
   npm run dev
   ```

## Testing Methods

### Automated Tests

We have created unit and integration tests for the security features. To run these tests:

```
npm run test:security:features
```

This will run the following tests:
- Unit tests for RateLimitService
- Unit tests for BruteForceProtectionService
- Unit tests for IpRestrictionService
- Unit tests for AuthAuditService
- Integration tests for security features

### Manual Testing

We have also created a manual testing script that tests the security features against a running instance of the API. To run this test:

1. Make sure the API server is running.
2. In a separate terminal, run:
   ```
   npm run test:security:manual
   ```

This script will:
- Test rate limiting by attempting to access rate limit configuration as admin and regular user
- Test brute force protection by making multiple failed login attempts
- Test IP restrictions by attempting to access IP restriction configuration
- Test authentication audit logging by accessing audit logs and login history

## Testing Each Feature Individually

### Rate Limiting

1. **Test API Endpoint Rate Limiting**:
   - Make multiple requests to an API endpoint in quick succession
   - After exceeding the limit, you should receive a 429 Too Many Requests response

2. **Test Authentication Endpoint Rate Limiting**:
   - Make multiple requests to the login endpoint in quick succession
   - After exceeding the limit, you should receive a 429 Too Many Requests response

3. **Test Rate Limit Configuration Access**:
   - Login as admin and access `/api/rate-limits` (should succeed)
   - Login as regular user and access `/api/rate-limits` (should be forbidden)

### Brute Force Protection

1. **Test Failed Login Attempts**:
   - Make multiple failed login attempts for the same username
   - After exceeding the maximum attempts, the account should be temporarily blocked
   - Subsequent login attempts should receive a 429 Too Many Requests response

2. **Test Successful Login Reset**:
   - Make a few failed login attempts (less than the maximum)
   - Successfully login with the correct credentials
   - The failed attempt counter should be reset

### IP-based Restrictions

1. **Test Allowlist Mode**:
   - Login as admin and enable IP restrictions in allowlist mode
   - Add your IP to the allowlist
   - You should still be able to access the API
   - From a different IP (e.g., using a VPN), you should be blocked

2. **Test Blocklist Mode**:
   - Login as admin and enable IP restrictions in blocklist mode
   - Add an IP to the blocklist
   - From that IP, you should be blocked
   - From other IPs, you should still be able to access the API

3. **Test Custom Rules**:
   - Login as admin and create a custom rule (e.g., allow a specific IP range)
   - Test access from IPs within and outside that range

### Authentication Audit Logging

1. **Test Login Audit**:
   - Login with valid credentials
   - Login with invalid credentials
   - As admin, access `/api/auth/audit` to view the audit logs
   - Verify that both login attempts are recorded with the correct status

2. **Test Logout Audit**:
   - Login and then logout
   - As admin, access `/api/auth/audit` to view the audit logs
   - Verify that the logout event is recorded

3. **Test User Login History**:
   - Login multiple times with the same user
   - Access `/api/auth/audit/user/{userId}/login-history` to view your login history
   - Verify that all login attempts are recorded

4. **Test Failed Login Report**:
   - As admin, access `/api/auth/audit/failed-logins` to view failed login attempts
   - Verify that all failed login attempts are recorded

## Troubleshooting

If you encounter issues during testing:

1. **API Server Not Running**:
   - Make sure the API server is running on the expected port (default: 3000)
   - Check for any error messages in the server console

2. **Authentication Issues**:
   - Make sure you're using valid credentials for admin and regular users
   - Check that the JWT token is being properly included in the Authorization header

3. **Test Script Errors**:
   - Check for any error messages in the test script output
   - Verify that the API URL in the test script matches your API server URL

## Conclusion

By thoroughly testing these security features, you can ensure that the NovaConnect API is protected against common security threats and provides proper audit logging for compliance purposes.
