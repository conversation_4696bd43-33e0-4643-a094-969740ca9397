/**
 * Domain Containerization Index
 * 
 * This module exports all the domain containerization components, which implement
 * the concept of treating each domain (Biological, Financial, Cyber/GRC) as a
 * distinct "containerized universe" within the Comphyological framework.
 */

const { DomainUniverse, FinitePossibilitySpace } = require('./DomainUniverse');
const { CrossDomainEntropyBridge } = require('./CrossDomainEntropyBridge');
const {
  BIOLOGICAL_BOUNDARIES,
  BIOLOGICAL_PHYSICS,
  FINANCIAL_BOUNDARIES,
  FINANCIAL_PHYSICS,
  CYBER_BOUNDARIES,
  CYBER_PHYSICS
} = require('./DomainDefinitions');

/**
 * Create a new domain universe
 * @param {string} name - The name of the domain
 * @param {Object} boundaries - The boundaries of the domain
 * @param {Object} physics - The physics rules of the domain
 * @returns {DomainUniverse} - The domain universe
 */
function createDomainUniverse(name, boundaries, physics) {
  return new DomainUniverse(name, boundaries, physics);
}

/**
 * Create a new cross-domain entropy bridge
 * @param {Object} options - Configuration options
 * @returns {CrossDomainEntropyBridge} - The cross-domain entropy bridge
 */
function createEntropyBridge(options) {
  return new CrossDomainEntropyBridge(options);
}

/**
 * Create a biological domain universe
 * @param {Object} customBoundaries - Custom boundaries to override defaults
 * @param {Object} customPhysics - Custom physics to override defaults
 * @returns {DomainUniverse} - The biological domain universe
 */
function createBiologicalDomain(customBoundaries = {}, customPhysics = {}) {
  const boundaries = { ...BIOLOGICAL_BOUNDARIES, ...customBoundaries };
  const physics = { ...BIOLOGICAL_PHYSICS, ...customPhysics };
  return createDomainUniverse('biological', boundaries, physics);
}

/**
 * Create a financial domain universe
 * @param {Object} customBoundaries - Custom boundaries to override defaults
 * @param {Object} customPhysics - Custom physics to override defaults
 * @returns {DomainUniverse} - The financial domain universe
 */
function createFinancialDomain(customBoundaries = {}, customPhysics = {}) {
  const boundaries = { ...FINANCIAL_BOUNDARIES, ...customBoundaries };
  const physics = { ...FINANCIAL_PHYSICS, ...customPhysics };
  return createDomainUniverse('financial', boundaries, physics);
}

/**
 * Create a cyber domain universe
 * @param {Object} customBoundaries - Custom boundaries to override defaults
 * @param {Object} customPhysics - Custom physics to override defaults
 * @returns {DomainUniverse} - The cyber domain universe
 */
function createCyberDomain(customBoundaries = {}, customPhysics = {}) {
  const boundaries = { ...CYBER_BOUNDARIES, ...customBoundaries };
  const physics = { ...CYBER_PHYSICS, ...customPhysics };
  return createDomainUniverse('cyber', boundaries, physics);
}

module.exports = {
  DomainUniverse,
  FinitePossibilitySpace,
  CrossDomainEntropyBridge,
  createDomainUniverse,
  createEntropyBridge,
  createBiologicalDomain,
  createFinancialDomain,
  createCyberDomain,
  BIOLOGICAL_BOUNDARIES,
  BIOLOGICAL_PHYSICS,
  FINANCIAL_BOUNDARIES,
  FINANCIAL_PHYSICS,
  CYBER_BOUNDARIES,
  CYBER_PHYSICS
};
