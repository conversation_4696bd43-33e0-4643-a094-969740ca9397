{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 1, "numPassedTests": 6, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 6, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1747982066349, "success": false, "testResults": [{"leaks": false, "numFailingTests": 0, "numPassingTests": 6, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1747982076597, "runtime": 6691, "slow": true, "start": 1747982069906}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\unit\\novacore\\tensor-runtime.test.js", "testResults": [{"ancestorTitles": ["NovaCore Tensor-Based Runtime", "Tensor Creation"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore Tensor-Based Runtime Tensor Creation should create a tensor with the correct dimensions", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should create a tensor with the correct dimensions"}, {"ancestorTitles": ["NovaCore Tensor-Based Runtime", "Tensor Creation"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore Tensor-Based Runtime Tensor Creation should handle empty data", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should handle empty data"}, {"ancestorTitles": ["NovaCore Tensor-Based Runtime", "Tensor Processing"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore Tensor-Based Runtime Tensor Processing should process a tensor correctly", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should process a tensor correctly"}, {"ancestorTitles": ["NovaCore Tensor-Based Runtime", "Tensor Processing"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore Tensor-Based Runtime Tensor Processing should handle complex data in tensors", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should handle complex data in tensors"}, {"ancestorTitles": ["NovaCore Tensor-Based Runtime", "Tensor Transformation"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore Tensor-Based Runtime Tensor Transformation should transform a tensor correctly", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should transform a tensor correctly"}, {"ancestorTitles": ["NovaCore Tensor-Based Runtime", "Performance"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore Tensor-Based Runtime Performance should process tensors efficiently", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should process tensors efficiently"}], "failureMessage": null}], "wasInterrupted": false}