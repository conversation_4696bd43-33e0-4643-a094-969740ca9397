import React from 'react';
import Link from 'next/link';
import PageWithSidebar from '../../components/PageWithSidebar';

export default function PartnerDemos() {
  // SEO metadata
  const pageProps = {
    title: 'Partner Integration Demos - NovaFuse',
    description: 'Explore NovaFuse\'s partner integration demos showcasing how our technology enhances platforms like Drata, Postman, Tines, and more.',
    keywords: 'NovaFuse partner demos, integration demos, Drata, Postman, Tines, Swimlane, Domo, API2Cart',
    canonical: 'https://novafuse.io/partner-demos',
    ogImage: '/images/partner-demos-og.png'
  };

  // Partner demo data
  const partnerDemos = [
    {
      id: 'drata-demo',
      name: 'Drata',
      title: 'Proactive Compliance',
      description: 'Layer NovaFuse\'s predictive risk engine atop Drata\'s framework monitoring.',
      logo: '/images/partners/drata-logo.png',
      icon: '🔮',
      url: '/partner-demos/drata-demo',
      featured: true
    },
    {
      id: 'postman-demo',
      name: '<PERSON><PERSON>',
      title: 'API-First Compliance Testing',
      description: 'Embed NovaFuse\'s compliance checks into Postman API workflows.',
      logo: '/images/partners/postman-logo.png',
      icon: '🧪',
      url: '/partner-demos/postman-demo',
      featured: true
    },
    {
      id: 'tines-demo',
      name: 'Tines',
      title: 'Automated SOAR Workflow Validation',
      description: 'Validate Tines playbooks against NIST 800-53 controls in real-time using NovaFuse\'s AI auditor.',
      logo: '/images/partners/tines-logo.png',
      icon: '🤖',
      url: '/partner-demos/tines-demo',
      featured: true
    },
    {
      id: 'swimlane-demo',
      name: 'Swimlane',
      title: 'Low-Code Compliance Automation',
      description: 'Drag-and-drop compliance workflows into Swimlane using NovaFuse\'s low-code builder.',
      logo: '/images/partners/swimlane-logo.png',
      icon: '🔄',
      url: '/partner-demos/swimlane-demo'
    },
    {
      id: 'domo-demo',
      name: 'Domo',
      title: 'Live Compliance Risk Dashboards',
      description: 'Pipe NovaFuse risk scores into Domo dashboards for CISO/board visibility.',
      logo: '/images/partners/domo-logo.png',
      icon: '📊',
      url: '/partner-demos/domo-demo'
    },
    {
      id: 'api2cart-demo',
      name: 'API2Cart',
      title: 'Cross-Platform Compliance Sync',
      description: 'Auto-synchronize compliance policies across 50+ eCommerce APIs.',
      logo: '/images/partners/api2cart-logo.png',
      icon: '🛒',
      url: '/partner-demos/api2cart-demo'
    }
  ];

  // Featured partners
  const featuredPartners = partnerDemos.filter(demo => demo.featured);

  // Sidebar items
  const sidebarItems = [
    { type: 'category', label: 'Partner Demos', items: [
      { label: 'All Partners', href: '/partner-demos' },
      { label: 'Featured Partners', href: '#featured' },
      { label: 'Become a Partner', href: '/partner-program' }
    ]},
    { type: 'category', label: 'Featured Partners', items:
      featuredPartners.map(partner => ({
        label: partner.name,
        href: partner.url
      }))
    },
    { type: 'category', label: 'Resources', items: [
      { label: 'Partner Empowerment Model', href: '/component-demos/partner-empowerment-demo' },
      { label: 'Partner Network', href: '/component-demos/partner-network-demo' },
      { label: 'Schedule a War Room', href: '/contact?type=war-room' }
    ]}
  ];

  return (
    <PageWithSidebar
      title={pageProps.title}
      description={pageProps.description}
      sidebarItems={sidebarItems}
    >
      <div className="partner-demos">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-blue-900 to-indigo-900 text-white rounded-lg p-8 mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-4 text-center">Partner Integration Demos</h1>
          <p className="text-xl mb-6 text-center max-w-3xl mx-auto">
            Explore how NovaFuse enhances leading platforms with compliance intelligence and automation.
          </p>
          <p className="text-center text-blue-200">
            Our partner integrations showcase NovaFuse as a complementary solution, not a competitor.
          </p>
        </div>

        {/* Featured Partners Section */}
        <div id="featured" className="mb-12 scroll-mt-16">
          <h2 className="text-2xl font-bold mb-6">Featured Partner Integrations</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {featuredPartners.map(partner => (
              <div key={partner.id} className="bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-700 hover:border-blue-500 transition-all">
                {/* Partner Logo/Icon */}
                <div className="h-40 bg-gray-900 relative flex items-center justify-center">
                  <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-900 to-indigo-900 opacity-50"></div>
                  <div className="relative z-10 flex flex-col items-center">
                    {partner.logo ? (
                      <img
                        src={partner.logo}
                        alt={`${partner.name} logo`}
                        className="h-16 mb-3"
                      />
                    ) : (
                      <span className="text-5xl mb-3">{partner.icon}</span>
                    )}
                    <h3 className="text-xl font-bold">{partner.name}</h3>
                  </div>

                  {/* Featured Badge */}
                  <div className="absolute top-2 right-2 bg-blue-600 px-2 py-1 rounded text-xs font-bold">
                    FEATURED
                  </div>
                </div>

                {/* Partner Info */}
                <div className="p-5">
                  <h3 className="text-xl font-bold mb-2">{partner.title}</h3>
                  <p className="text-gray-400 text-sm mb-4">{partner.description}</p>

                  {/* Action Button */}
                  <Link href={partner.url} className="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 rounded-lg font-medium">
                    View Demo
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* All Partners Section */}
        <div>
          <h2 className="text-2xl font-bold mb-6">All Partner Integrations</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {partnerDemos.map(partner => (
              <div key={partner.id} className="bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-700 hover:border-blue-500 transition-all">
                {/* Partner Info */}
                <div className="p-5">
                  <div className="flex items-center mb-3">
                    {partner.logo ? (
                      <img
                        src={partner.logo}
                        alt={`${partner.name} logo`}
                        className="h-8 mr-3"
                      />
                    ) : (
                      <span className="text-3xl mr-3">{partner.icon}</span>
                    )}
                    <h3 className="text-xl font-bold">{partner.name}</h3>
                  </div>

                  <h4 className="text-lg font-semibold mb-2 text-blue-400">{partner.title}</h4>
                  <p className="text-gray-400 text-sm mb-4">{partner.description}</p>

                  {/* Action Button */}
                  <Link href={partner.url} className="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 rounded-lg font-medium">
                    View Demo
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Become a Partner CTA */}
        <div className="mt-12 bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-3">Become a NovaFuse Partner</h2>
          <p className="text-lg mb-6 max-w-3xl mx-auto">
            Join the NovaFuse Partner Network and revolutionize how you deliver compliance solutions to your customers.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/partner-program" className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-bold">
              Explore Partner Program
            </Link>
            <Link href="/contact?type=partner" className="border border-white text-white px-6 py-3 rounded-lg font-bold hover:bg-white hover:text-blue-900">
              Contact Partner Team
            </Link>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
}
