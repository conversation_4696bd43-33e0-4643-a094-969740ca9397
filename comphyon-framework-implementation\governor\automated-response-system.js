/**
 * Automated Response System
 * 
 * This module implements the Automated Response System component of the Governor.
 * It automatically responds to alerts and threshold violations.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * AutomatedResponseSystem class
 */
class AutomatedResponseSystem extends EventEmitter {
  /**
   * Create a new AutomatedResponseSystem instance
   * @param {Object} options - Configuration options
   * @param {Object} controlActionExecution - Control Action Execution component
   */
  constructor(controlActionExecution, options = {}) {
    super();
    
    if (!controlActionExecution) {
      throw new Error('Control Action Execution component is required');
    }
    
    this.controlActionExecution = controlActionExecution;
    
    this.options = {
      enableLogging: true,
      enableMetrics: true,
      historySize: 100, // Number of historical data points to keep
      responseRules: new Map(), // id -> rule
      defaultCooldown: 300000, // 5 minutes
      ...options
    };
    
    // Initialize state
    this.state = {
      activeResponses: new Map(), // id -> response
      responseHistory: [],
      cooldowns: new Map(), // ruleId -> timestamp
      isRunning: false,
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      alertsProcessed: 0,
      responsesTriggered: 0,
      responsesByLevel: {
        warning: 0,
        critical: 0,
        emergency: 0
      },
      responsesByDomain: {
        universal: 0,
        cyber: 0,
        financial: 0,
        biological: 0
      }
    };
    
    if (this.options.enableLogging) {
      console.log('AutomatedResponseSystem initialized');
    }
  }
  
  /**
   * Start the automated response system
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('AutomatedResponseSystem is already running');
      }
      return false;
    }
    
    this.state.isRunning = true;
    
    if (this.options.enableLogging) {
      console.log('AutomatedResponseSystem started');
    }
    
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the automated response system
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('AutomatedResponseSystem is not running');
      }
      return false;
    }
    
    this.state.isRunning = false;
    
    if (this.options.enableLogging) {
      console.log('AutomatedResponseSystem stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Register response rule
   * @param {Object} rule - Response rule
   * @returns {Object} - Registered rule
   */
  registerRule(rule) {
    const startTime = performance.now();
    
    if (!rule || typeof rule !== 'object') {
      throw new Error('Rule must be an object');
    }
    
    if (!rule.id) {
      rule.id = `rule-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    
    if (!rule.name) {
      throw new Error('Rule must have a name');
    }
    
    if (!rule.condition || typeof rule.condition !== 'function') {
      throw new Error('Rule must have a condition function');
    }
    
    if (!rule.actions || !Array.isArray(rule.actions) || rule.actions.length === 0) {
      throw new Error('Rule must have at least one action');
    }
    
    // Validate actions
    for (const action of rule.actions) {
      if (!action.actionId) {
        throw new Error('Each action must have an actionId');
      }
      
      // Check if action exists
      if (!this.controlActionExecution.getRegisteredActions().find(a => a.id === action.actionId)) {
        throw new Error(`Action ${action.actionId} is not registered`);
      }
    }
    
    // Set default values
    rule = {
      domains: ['universal'], // universal, cyber, financial, biological
      alertLevels: ['warning', 'critical', 'emergency'],
      description: `Response rule: ${rule.name}`,
      priority: 'medium', // low, medium, high, critical
      cooldown: this.options.defaultCooldown,
      enabled: true,
      registeredAt: Date.now(),
      ...rule
    };
    
    // Add to response rules
    this.options.responseRules.set(rule.id, rule);
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    // Emit event
    this.emit('rule-registered', {
      ruleId: rule.id,
      name: rule.name,
      domains: rule.domains,
      alertLevels: rule.alertLevels,
      priority: rule.priority,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`AutomatedResponseSystem: Registered rule ${rule.id} (${rule.name})`);
    }
    
    return rule;
  }
  
  /**
   * Unregister response rule
   * @param {string} ruleId - Rule ID
   * @returns {boolean} - Success status
   */
  unregisterRule(ruleId) {
    if (!ruleId || !this.options.responseRules.has(ruleId)) {
      return false;
    }
    
    // Get rule
    const rule = this.options.responseRules.get(ruleId);
    
    // Remove from response rules
    this.options.responseRules.delete(ruleId);
    
    // Emit event
    this.emit('rule-unregistered', {
      ruleId: rule.id,
      name: rule.name,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`AutomatedResponseSystem: Unregistered rule ${rule.id} (${rule.name})`);
    }
    
    return true;
  }
  
  /**
   * Enable response rule
   * @param {string} ruleId - Rule ID
   * @returns {boolean} - Success status
   */
  enableRule(ruleId) {
    if (!ruleId || !this.options.responseRules.has(ruleId)) {
      return false;
    }
    
    // Get rule
    const rule = this.options.responseRules.get(ruleId);
    
    // Enable rule
    rule.enabled = true;
    
    // Emit event
    this.emit('rule-enabled', {
      ruleId: rule.id,
      name: rule.name,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`AutomatedResponseSystem: Enabled rule ${rule.id} (${rule.name})`);
    }
    
    return true;
  }
  
  /**
   * Disable response rule
   * @param {string} ruleId - Rule ID
   * @returns {boolean} - Success status
   */
  disableRule(ruleId) {
    if (!ruleId || !this.options.responseRules.has(ruleId)) {
      return false;
    }
    
    // Get rule
    const rule = this.options.responseRules.get(ruleId);
    
    // Disable rule
    rule.enabled = false;
    
    // Emit event
    this.emit('rule-disabled', {
      ruleId: rule.id,
      name: rule.name,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`AutomatedResponseSystem: Disabled rule ${rule.id} (${rule.name})`);
    }
    
    return true;
  }
  
  /**
   * Process alert
   * @param {Object} alert - Alert object
   * @returns {Array} - Triggered responses
   */
  processAlert(alert) {
    const startTime = performance.now();
    
    if (!this.state.isRunning) {
      throw new Error('AutomatedResponseSystem is not running');
    }
    
    if (!alert || typeof alert !== 'object') {
      throw new Error('Alert must be an object');
    }
    
    if (!alert.id || !alert.level || !alert.domain) {
      throw new Error('Alert must have id, level, and domain properties');
    }
    
    // Find matching rules
    const matchingRules = this._findMatchingRules(alert);
    
    // Trigger responses for matching rules
    const responses = [];
    
    for (const rule of matchingRules) {
      const response = this._triggerResponse(rule, alert);
      
      if (response) {
        responses.push(response);
      }
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.alertsProcessed++;
    
    if (this.options.enableLogging) {
      console.log(`AutomatedResponseSystem: Processed alert ${alert.id} (${alert.level}) with ${responses.length} responses`);
    }
    
    return responses;
  }
  
  /**
   * Get response rules
   * @param {string} domain - Domain filter (all domains if not specified)
   * @returns {Array} - Response rules
   */
  getResponseRules(domain) {
    const rules = Array.from(this.options.responseRules.values());
    
    if (domain) {
      return rules.filter(r => r.domains.includes(domain));
    }
    
    return rules;
  }
  
  /**
   * Get active responses
   * @returns {Array} - Active responses
   */
  getActiveResponses() {
    return Array.from(this.state.activeResponses.values());
  }
  
  /**
   * Get response history
   * @param {number} limit - Maximum number of history items to return
   * @returns {Array} - Response history
   */
  getResponseHistory(limit = 10) {
    return this.state.responseHistory.slice(0, limit);
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Find matching rules for alert
   * @param {Object} alert - Alert object
   * @returns {Array} - Matching rules
   * @private
   */
  _findMatchingRules(alert) {
    const matchingRules = [];
    const now = Date.now();
    
    // Check each rule
    for (const rule of this.options.responseRules.values()) {
      // Skip disabled rules
      if (!rule.enabled) {
        continue;
      }
      
      // Check domain
      if (!rule.domains.includes(alert.domain) && !rule.domains.includes('universal')) {
        continue;
      }
      
      // Check alert level
      if (!rule.alertLevels.includes(alert.level)) {
        continue;
      }
      
      // Check cooldown
      if (this.state.cooldowns.has(rule.id)) {
        const cooldownUntil = this.state.cooldowns.get(rule.id) + rule.cooldown;
        
        if (now < cooldownUntil) {
          // Rule is in cooldown
          continue;
        }
      }
      
      // Check condition
      try {
        if (!rule.condition(alert)) {
          continue;
        }
      } catch (error) {
        if (this.options.enableLogging) {
          console.error(`AutomatedResponseSystem: Error evaluating rule condition for ${rule.id} (${rule.name}):`, error.message);
        }
        continue;
      }
      
      // Rule matches
      matchingRules.push(rule);
    }
    
    return matchingRules;
  }
  
  /**
   * Trigger response for rule
   * @param {Object} rule - Response rule
   * @param {Object} alert - Alert object
   * @returns {Object} - Response object
   * @private
   */
  _triggerResponse(rule, alert) {
    // Create response ID
    const responseId = `response-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    
    // Create response object
    const response = {
      id: responseId,
      ruleId: rule.id,
      ruleName: rule.name,
      alertId: alert.id,
      alertLevel: alert.level,
      alertDomain: alert.domain,
      actions: [],
      status: 'triggered',
      triggeredAt: Date.now()
    };
    
    // Execute actions
    for (const action of rule.actions) {
      try {
        const executionId = this.controlActionExecution.executeAction(
          action.actionId,
          action.parameters || {},
          {
            alert,
            rule,
            response: responseId
          }
        );
        
        response.actions.push({
          actionId: action.actionId,
          executionId,
          status: 'queued',
          queuedAt: Date.now()
        });
      } catch (error) {
        response.actions.push({
          actionId: action.actionId,
          status: 'failed',
          error: error.message || 'Unknown error',
          failedAt: Date.now()
        });
        
        if (this.options.enableLogging) {
          console.error(`AutomatedResponseSystem: Error executing action ${action.actionId} for rule ${rule.id}:`, error.message);
        }
      }
    }
    
    // Set cooldown
    this.state.cooldowns.set(rule.id, Date.now());
    
    // Add to active responses
    this.state.activeResponses.set(responseId, response);
    
    // Add to history
    this.state.responseHistory.push(response);
    
    // Limit history size
    if (this.state.responseHistory.length > this.options.historySize) {
      this.state.responseHistory.shift();
    }
    
    // Update metrics
    this.metrics.responsesTriggered++;
    this.metrics.responsesByLevel[alert.level] = (this.metrics.responsesByLevel[alert.level] || 0) + 1;
    this.metrics.responsesByDomain[alert.domain] = (this.metrics.responsesByDomain[alert.domain] || 0) + 1;
    
    // Emit event
    this.emit('response-triggered', {
      responseId,
      ruleId: rule.id,
      ruleName: rule.name,
      alertId: alert.id,
      alertLevel: alert.level,
      alertDomain: alert.domain,
      actionCount: response.actions.length,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`AutomatedResponseSystem: Triggered response ${responseId} for rule ${rule.id} (${rule.name})`);
    }
    
    return response;
  }
  
  /**
   * Update response action status
   * @param {string} responseId - Response ID
   * @param {string} executionId - Execution ID
   * @param {string} status - Action status
   * @param {*} result - Action result
   * @private
   */
  updateResponseActionStatus(responseId, executionId, status, result) {
    if (!this.state.activeResponses.has(responseId)) {
      return;
    }
    
    // Get response
    const response = this.state.activeResponses.get(responseId);
    
    // Find action
    const action = response.actions.find(a => a.executionId === executionId);
    
    if (!action) {
      return;
    }
    
    // Update action status
    action.status = status;
    action.result = result;
    action.updatedAt = Date.now();
    
    // Check if all actions are completed
    const allCompleted = response.actions.every(a => 
      ['completed', 'failed', 'cancelled', 'timeout'].includes(a.status)
    );
    
    if (allCompleted) {
      // Update response status
      response.status = 'completed';
      response.completedAt = Date.now();
      
      // Remove from active responses
      this.state.activeResponses.delete(responseId);
      
      // Emit event
      this.emit('response-completed', {
        responseId,
        ruleId: response.ruleId,
        ruleName: response.ruleName,
        alertId: response.alertId,
        timestamp: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`AutomatedResponseSystem: Completed response ${responseId} for rule ${response.ruleId} (${response.ruleName})`);
      }
    }
  }
}

module.exports = AutomatedResponseSystem;
