/**
 * Framework Explorer Component
 * 
 * This component allows users to explore compliance frameworks and their controls.
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { ChevronRight, ChevronDown, Shield, Book } from 'lucide-react';

interface FrameworkExplorerProps {
  organizationId: string;
}

export const FrameworkExplorer: React.FC<FrameworkExplorerProps> = ({ organizationId }) => {
  const [expandedFramework, setExpandedFramework] = useState<string | null>(null);
  const [expandedDomain, setExpandedDomain] = useState<string | null>(null);
  
  // Mock data for frameworks
  const frameworks = [
    {
      id: 'framework1',
      name: 'SOC 2',
      description: 'Service Organization Control 2',
      domains: [
        {
          id: 'domain1',
          name: 'Security',
          controls: [
            { id: 'control1', name: 'Access Control', description: 'Logical access to systems is restricted to authorized users' },
            { id: 'control2', name: 'System Operations', description: 'System operations are monitored to detect deviations' },
          ]
        },
        {
          id: 'domain2',
          name: 'Availability',
          controls: [
            { id: 'control3', name: 'Business Continuity', description: 'Business continuity planning addresses availability risks' },
            { id: 'control4', name: 'Disaster Recovery', description: 'Disaster recovery procedures are tested regularly' },
          ]
        }
      ]
    },
    {
      id: 'framework2',
      name: 'HIPAA',
      description: 'Health Insurance Portability and Accountability Act',
      domains: [
        {
          id: 'domain3',
          name: 'Privacy',
          controls: [
            { id: 'control5', name: 'Notice of Privacy Practices', description: 'Patients are informed about privacy practices' },
            { id: 'control6', name: 'Patient Rights', description: 'Patients can access and amend their health information' },
          ]
        },
        {
          id: 'domain4',
          name: 'Security',
          controls: [
            { id: 'control7', name: 'Risk Analysis', description: 'Conduct risk analysis to identify vulnerabilities' },
            { id: 'control8', name: 'Security Measures', description: 'Implement security measures to protect PHI' },
          ]
        }
      ]
    }
  ];
  
  const toggleFramework = (frameworkId: string) => {
    if (expandedFramework === frameworkId) {
      setExpandedFramework(null);
      setExpandedDomain(null);
    } else {
      setExpandedFramework(frameworkId);
      setExpandedDomain(null);
    }
  };
  
  const toggleDomain = (domainId: string) => {
    if (expandedDomain === domainId) {
      setExpandedDomain(null);
    } else {
      setExpandedDomain(domainId);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Framework Explorer</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {frameworks.map((framework) => (
            <div key={framework.id} className="border rounded-md">
              <div 
                className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50"
                onClick={() => toggleFramework(framework.id)}
              >
                <div className="flex items-center">
                  <Shield className="h-5 w-5 text-blue-500 mr-2" />
                  <div>
                    <div className="font-medium">{framework.name}</div>
                    <div className="text-sm text-gray-500">{framework.description}</div>
                  </div>
                </div>
                {expandedFramework === framework.id ? (
                  <ChevronDown className="h-5 w-5 text-gray-400" />
                ) : (
                  <ChevronRight className="h-5 w-5 text-gray-400" />
                )}
              </div>
              
              {expandedFramework === framework.id && (
                <div className="border-t px-4 py-2 bg-gray-50">
                  <div className="text-sm font-medium text-gray-500 mb-2">Domains</div>
                  <div className="space-y-2">
                    {framework.domains.map((domain) => (
                      <div key={domain.id} className="border rounded-md bg-white">
                        <div 
                          className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50"
                          onClick={() => toggleDomain(domain.id)}
                        >
                          <div className="flex items-center">
                            <Book className="h-4 w-4 text-blue-500 mr-2" />
                            <div className="font-medium">{domain.name}</div>
                          </div>
                          {expandedDomain === domain.id ? (
                            <ChevronDown className="h-4 w-4 text-gray-400" />
                          ) : (
                            <ChevronRight className="h-4 w-4 text-gray-400" />
                          )}
                        </div>
                        
                        {expandedDomain === domain.id && (
                          <div className="border-t px-3 py-2">
                            <div className="text-xs font-medium text-gray-500 mb-2">Controls</div>
                            <div className="space-y-2">
                              {domain.controls.map((control) => (
                                <div key={control.id} className="p-2 border rounded-md bg-gray-50">
                                  <div className="font-medium text-sm">{control.name}</div>
                                  <div className="text-xs text-gray-600 mt-1">{control.description}</div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default FrameworkExplorer;
