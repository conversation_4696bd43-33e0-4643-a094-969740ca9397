/**
 * Offline Manager
 * 
 * This module provides offline functionality for the NovaVision Hub.
 */

/**
 * Offline Manager class
 */
class OfflineManager {
  /**
   * Constructor
   */
  constructor() {
    this.dbName = 'NovaVisionOfflineDB';
    this.dbVersion = 1;
    this.db = null;
    this.isOnline = navigator.onLine;
    this.pendingRequests = [];
    this.listeners = {
      online: [],
      offline: [],
      syncComplete: []
    };
    
    // Initialize
    this.init();
  }
  
  /**
   * Initialize offline manager
   */
  async init() {
    // Open database
    try {
      this.db = await this.openDatabase();
    } catch (error) {
      console.error('Failed to open database:', error);
    }
    
    // Register service worker
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/service-worker.js');
        console.log('Service Worker registered with scope:', registration.scope);
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }
    
    // Add event listeners
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
    
    // Load pending requests
    await this.loadPendingRequests();
  }
  
  /**
   * Open IndexedDB database
   * 
   * @returns {Promise<IDBDatabase>} Database instance
   */
  openDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Create object store for pending requests
        if (!db.objectStoreNames.contains('pendingRequests')) {
          db.createObjectStore('pendingRequests', { keyPath: 'id', autoIncrement: true });
        }
        
        // Create object store for offline data
        if (!db.objectStoreNames.contains('offlineData')) {
          db.createObjectStore('offlineData', { keyPath: 'key' });
        }
      };
    });
  }
  
  /**
   * Load pending requests from database
   */
  async loadPendingRequests() {
    if (!this.db) return;
    
    try {
      this.pendingRequests = await this.getAll('pendingRequests');
    } catch (error) {
      console.error('Failed to load pending requests:', error);
      this.pendingRequests = [];
    }
  }
  
  /**
   * Handle online event
   */
  handleOnline() {
    this.isOnline = true;
    
    // Trigger sync
    this.syncPendingRequests();
    
    // Notify listeners
    this.notifyListeners('online');
  }
  
  /**
   * Handle offline event
   */
  handleOffline() {
    this.isOnline = false;
    
    // Notify listeners
    this.notifyListeners('offline');
  }
  
  /**
   * Sync pending requests
   */
  async syncPendingRequests() {
    if (!this.isOnline || this.pendingRequests.length === 0) return;
    
    // Register sync with service worker if available
    if ('serviceWorker' in navigator && 'SyncManager' in window) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await registration.sync.register('sync-pending-requests');
        return;
      } catch (error) {
        console.error('Failed to register sync:', error);
      }
    }
    
    // Fallback: sync manually
    const successfulRequests = [];
    
    for (const request of this.pendingRequests) {
      try {
        const response = await fetch(request.url, {
          method: request.method,
          headers: request.headers,
          body: request.body
        });
        
        if (response.ok) {
          successfulRequests.push(request);
        }
      } catch (error) {
        console.error('Failed to sync request:', error);
      }
    }
    
    // Remove successful requests
    for (const request of successfulRequests) {
      await this.removeRequest(request.id);
    }
    
    // Notify listeners
    this.notifyListeners('syncComplete', { 
      successful: successfulRequests.length,
      remaining: this.pendingRequests.length
    });
  }
  
  /**
   * Add request to pending requests
   * 
   * @param {Object} request - Request to add
   * @returns {Promise<number>} Request ID
   */
  async addRequest(request) {
    if (!this.db) return null;
    
    try {
      const id = await this.add('pendingRequests', request);
      this.pendingRequests.push({ ...request, id });
      return id;
    } catch (error) {
      console.error('Failed to add request:', error);
      return null;
    }
  }
  
  /**
   * Remove request from pending requests
   * 
   * @param {number} id - Request ID
   * @returns {Promise<boolean>} Success
   */
  async removeRequest(id) {
    if (!this.db) return false;
    
    try {
      await this.delete('pendingRequests', id);
      this.pendingRequests = this.pendingRequests.filter(request => request.id !== id);
      return true;
    } catch (error) {
      console.error('Failed to remove request:', error);
      return false;
    }
  }
  
  /**
   * Save data for offline use
   * 
   * @param {string} key - Data key
   * @param {*} data - Data to save
   * @returns {Promise<boolean>} Success
   */
  async saveOfflineData(key, data) {
    if (!this.db) return false;
    
    try {
      await this.put('offlineData', { key, data, timestamp: Date.now() });
      return true;
    } catch (error) {
      console.error('Failed to save offline data:', error);
      return false;
    }
  }
  
  /**
   * Get data for offline use
   * 
   * @param {string} key - Data key
   * @returns {Promise<*>} Data
   */
  async getOfflineData(key) {
    if (!this.db) return null;
    
    try {
      const result = await this.get('offlineData', key);
      return result ? result.data : null;
    } catch (error) {
      console.error('Failed to get offline data:', error);
      return null;
    }
  }
  
  /**
   * Clear offline data
   * 
   * @param {string} [key] - Data key (if not provided, clears all data)
   * @returns {Promise<boolean>} Success
   */
  async clearOfflineData(key) {
    if (!this.db) return false;
    
    try {
      if (key) {
        await this.delete('offlineData', key);
      } else {
        await this.clear('offlineData');
      }
      return true;
    } catch (error) {
      console.error('Failed to clear offline data:', error);
      return false;
    }
  }
  
  /**
   * Add event listener
   * 
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  addEventListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback);
    }
  }
  
  /**
   * Remove event listener
   * 
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  removeEventListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
  }
  
  /**
   * Notify listeners
   * 
   * @param {string} event - Event name
   * @param {*} [data] - Event data
   */
  notifyListeners(event, data) {
    if (this.listeners[event]) {
      for (const callback of this.listeners[event]) {
        callback(data);
      }
    }
  }
  
  /**
   * Add data to object store
   * 
   * @param {string} storeName - Object store name
   * @param {*} data - Data to add
   * @returns {Promise<number>} Generated key
   */
  add(storeName, data) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.add(data);
      
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
  
  /**
   * Put data in object store
   * 
   * @param {string} storeName - Object store name
   * @param {*} data - Data to put
   * @returns {Promise<*>} Key
   */
  put(storeName, data) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(data);
      
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
  
  /**
   * Get data from object store
   * 
   * @param {string} storeName - Object store name
   * @param {*} key - Key
   * @returns {Promise<*>} Data
   */
  get(storeName, key) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(storeName, 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(key);
      
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
  
  /**
   * Get all data from object store
   * 
   * @param {string} storeName - Object store name
   * @returns {Promise<Array>} Data
   */
  getAll(storeName) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(storeName, 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();
      
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
  
  /**
   * Delete data from object store
   * 
   * @param {string} storeName - Object store name
   * @param {*} key - Key
   * @returns {Promise<undefined>} Undefined
   */
  delete(storeName, key) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(key);
      
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }
  
  /**
   * Clear object store
   * 
   * @param {string} storeName - Object store name
   * @returns {Promise<undefined>} Undefined
   */
  clear(storeName) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();
      
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }
}

// Create singleton instance
const offlineManager = new OfflineManager();

export default offlineManager;
