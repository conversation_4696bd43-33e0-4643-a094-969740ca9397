# NovaActuary™ Manual Docker Build Steps
**Step-by-Step Container Creation Guide**

---

## **🎯 Prerequisites**

### **1. Ensure Docker Desktop is Running**
- Look for Docker whale icon in system tray
- Should show "Docker Desktop is running"
- If not running, start Docker Desktop from Windows Start Menu

### **2. Open Terminal/PowerShell**
- Press `Win + R`, type `powershell`, press Enter
- Or open Command Prompt/PowerShell from Start Menu

---

## **🚀 Build Process**

### **Step 1: Navigate to NovaActuary Directory**
```powershell
cd "C:\Users\<USER>\novafuse-api-superstore\src\novaactuary"
```

### **Step 2: Verify Files Exist**
```powershell
# Check if required files are present
dir Dockerfile
dir docker-compose.yml
```

**Expected Output:**
```
Dockerfile
docker-compose.yml
```

### **Step 3: Check Docker Status**
```powershell
docker --version
docker-compose --version
```

**Expected Output:**
```
Docker version 24.x.x
Docker Compose version v2.x.x
```

### **Step 4: Clean Up (Optional)**
```powershell
docker-compose down --remove-orphans
```

### **Step 5: Build the Container**
```powershell
docker-compose build --no-cache novaactuary
```

**This will:**
- Download Node.js base image
- Install system dependencies
- Copy NovaActuary™ source code
- Install Node.js dependencies
- Configure the container
- **Takes 5-10 minutes on first build**

### **Step 6: Verify Build Success**
```powershell
docker images | findstr novaactuary
```

**Expected Output:**
```
novaactuary_novaactuary   latest   abc123def456   2 minutes ago   500MB
```

---

## **🧪 Testing the Container**

### **Step 1: Start the Container**
```powershell
docker-compose up -d novaactuary
```

### **Step 2: Check Container Status**
```powershell
docker-compose ps
```

**Expected Output:**
```
Name                State    Ports
novaactuary-core    Up       0.0.0.0:3000->3000/tcp
```

### **Step 3: Run Health Check**
```powershell
docker-compose exec novaactuary node novaactuary/health-check.js
```

**Expected Output:**
```
🏥 NOVAACTUARY™ HEALTH CHECK RESULTS
==================================================
📊 Overall Status: HEALTHY
⏱️  Health Check Duration: 287.45ms

📋 Component Checks:
   ✅ coreModule: PASS - NovaActuary™ v1.0.0-REVOLUTIONARY loaded successfully
   ✅ componentIntegration: PASS - All components integrated
   ✅ mathematicalFramework: PASS - Mathematical framework validated
   ✅ performance: PASS - Performance within acceptable limits

🎉 NovaActuary™ is ready for production deployment!
```

### **Step 4: Run Quick Test**
```powershell
docker-compose exec novaactuary node novaactuary/quick-test.js
```

**Expected Output:**
```
🚀 NovaActuary™ Quick Validation Test
==================================================
✅ NovaActuary™ v1.0.0-REVOLUTIONARY loaded successfully
🎯 Risk Classification: EXCELLENT
💰 Mathematical Premium: $70,000
📉 Traditional Premium: $250,000
💵 Premium Savings: $180,000
⚡ Processing Time: 287ms
🔬 ∂Ψ Deviation: 0.2847
🌟 π-Coherence Score: 0.6234
🏆 Certification: NOVAACTUARY_CERTIFIED

📊 Success Rate: 100.0% (7/7)
🎉 NovaActuary™ is ready to revolutionize the insurance industry!
```

### **Step 5: Run Executive Demo**
```powershell
docker-compose exec novaactuary node novaactuary/demo-novaactuary.js
```

---

## **🔧 Alternative Build Methods**

### **Method 1: Using Batch Script**
```powershell
# Navigate to directory
cd "C:\Users\<USER>\novafuse-api-superstore\src\novaactuary"

# Run build script
.\BUILD-DOCKER-MANUAL.bat
```

### **Method 2: Using PowerShell Script**
```powershell
# Navigate to directory
cd "C:\Users\<USER>\novafuse-api-superstore\src\novaactuary"

# Run PowerShell script
.\Build-Docker.ps1
```

### **Method 3: Direct Docker Build**
```powershell
# Navigate to project root
cd "C:\Users\<USER>\novafuse-api-superstore"

# Build directly with Docker
docker build -t novaactuary:latest -f src/novaactuary/Dockerfile .

# Run the container
docker run -d --name novaactuary-core -p 3000:3000 novaactuary:latest

# Test the container
docker exec novaactuary-core node novaactuary/health-check.js
```

---

## **🚨 Troubleshooting**

### **Problem: "Docker not found"**
**Solution:**
1. Install Docker Desktop from https://www.docker.com/products/docker-desktop
2. Start Docker Desktop
3. Wait for initialization (whale icon in system tray)

### **Problem: "Permission denied"**
**Solution:**
1. Run PowerShell as Administrator
2. Try the build commands again

### **Problem: "Build failed with network error"**
**Solution:**
1. Check internet connection
2. Try building again (sometimes network timeouts occur)
3. Use `--no-cache` flag: `docker-compose build --no-cache novaactuary`

### **Problem: "Out of disk space"**
**Solution:**
1. Free up disk space (need 2GB+ available)
2. Clean Docker: `docker system prune -f`
3. Try building again

### **Problem: "Container won't start"**
**Solution:**
1. Check logs: `docker-compose logs novaactuary`
2. Check system resources: `docker system df`
3. Restart Docker Desktop

### **Problem: "Health check fails"**
**Solution:**
1. Wait 30 seconds for container to fully initialize
2. Check container logs: `docker-compose logs novaactuary`
3. Try health check again

---

## **📊 Build Progress Indicators**

### **During Build, You'll See:**
```
Building novaactuary
Step 1/15 : FROM node:18-alpine
 ---> Pulling from library/node
Step 2/15 : WORKDIR /app
 ---> Running in abc123def456
Step 3/15 : RUN addgroup -g 1001 -S novaactuary
...
Step 15/15 : CMD ["node", "novaactuary/index.js"]
 ---> Running in xyz789abc123
Successfully built abc123def456
Successfully tagged novaactuary_novaactuary:latest
```

### **Build Time Estimates:**
- **First build**: 5-10 minutes (downloading base images)
- **Subsequent builds**: 2-5 minutes (using cache)
- **With --no-cache**: 5-10 minutes (fresh build)

---

## **🎯 Success Criteria**

### **Build Success:**
- ✅ No error messages during build
- ✅ "Successfully built" message appears
- ✅ Container image appears in `docker images`

### **Container Success:**
- ✅ Container starts without errors
- ✅ Health check passes
- ✅ Quick test completes successfully
- ✅ All mathematical frameworks operational

### **Production Ready:**
- ✅ All tests pass
- ✅ Performance within targets (< 1 second)
- ✅ Mathematical validation successful
- ✅ Ready for insurance industry deployment

---

## **🚀 Next Steps After Successful Build**

1. **Run comprehensive tests**
2. **Execute executive demonstration**
3. **Prepare for insurance company pilots**
4. **Deploy to production environment**
5. **Begin market penetration strategy**

**RECOMMENDATION: Start with Step 1 and follow the process sequentially for best results! 🎯**
