# Simple script to fix remaining character issues in the dictionary

# Define paths
$filePath = "d:\\novafuse-api-superstore\\coherence-reality-systems\\Comphyology Master Archive\\3. APPENDIX A - MATH FOUNDATION\\The Comphyological Dictionary 1st Edition.md"
$backupPath = "$filePath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss').md"

# Create backup
Copy-Item -Path $filePath -Destination $backupPath -Force
Write-Host "Created backup at: $backupPath"

# Read the file
$content = Get-Content -Path $filePath -Raw -Encoding UTF8

# Fix specific patterns
$replacements = @{
    # Fix lightbulb + quote
    '💡â€˜' = '💡'
    
    # Fix equivalence symbol
    'Ã¢â€°Â¡' = '≡'
    
    # Fix other common patterns
    'Ãƒâ€"' = '–'
    'â€"' = '–'
    'Ã¢â€œ' = '\"'
    'Ã¢â€' = '\"'
    'Ã¢â€˜' = "'"
    'Ã¢â„¢Â' = '™'
    'Â' = ' '
    'â€"' = '–'
    'â€"' = '—'
    'â€œ' = '\"'
    'â€' = '\"'
    'â„¢' = '™'
    'â€"' = '…'
    'ÃŽÂ¨Ã¡Â¶Å"ÃŠÂ°' = 'Ψ'
    'ÃŽÂ¨' = 'Ψ'
    'Ã¡Â¶Å"' = ''
    'ÃŠÂ°' = ''
    'Ãƒâ€”' = '×'
}

# Apply replacements
foreach ($key in $replacements.Keys) {
    $content = $content.Replace($key, $replacements[$key])
}

# Clean up any remaining problematic sequences
$content = $content -replace 'Ã[^\s]{1,10}', '?'

# Write the fixed content back to the file
[System.IO.File]::WriteAllText($filePath, $content, [System.Text.Encoding]::UTF8)

Write-Host "Fixed remaining character issues in the dictionary file."
Write-Host "Please review the changes in the file: $filePath"
