"""
Workflow utilities for the Universal Compliance Workflow Orchestrator.

This module provides utility functions for working with workflows.
"""

import json
import logging
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_workflow_definition(file_path: str) -> Dict[str, Any]:
    """
    Load a workflow definition from a JSON file.
    
    Args:
        file_path: Path to the JSON file
        
    Returns:
        The workflow definition
        
    Raises:
        FileNotFoundError: If the file does not exist
        json.JSONDecodeError: If the file is not valid JSON
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            workflow_def = json.load(f)
        
        logger.info(f"Loaded workflow definition from {file_path}")
        return workflow_def
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        raise
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in file: {file_path}")
        raise

def save_workflow_definition(workflow_def: Dict[str, Any], file_path: str) -> None:
    """
    Save a workflow definition to a JSON file.
    
    Args:
        workflow_def: The workflow definition
        file_path: Path to the output JSON file
        
    Raises:
        IOError: If the file cannot be written
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(workflow_def, f, indent=2)
        
        logger.info(f"Saved workflow definition to {file_path}")
    except IOError:
        logger.error(f"Failed to save workflow definition to {file_path}")
        raise

def create_workflow_diagram(workflow_def: Dict[str, Any], output_file: Optional[str] = None) -> str:
    """
    Create a diagram of a workflow.
    
    Args:
        workflow_def: The workflow definition
        output_file: Path to the output file (optional)
        
    Returns:
        The diagram as a string
    """
    # Create a simple text-based diagram
    diagram = f"Workflow: {workflow_def.get('name', 'Unnamed')}\n"
    diagram += f"ID: {workflow_def.get('id', 'unknown')}\n"
    diagram += f"Version: {workflow_def.get('version', 'unknown')}\n"
    diagram += f"Description: {workflow_def.get('description', 'No description')}\n\n"
    
    # Add steps
    diagram += "Steps:\n"
    
    steps = workflow_def.get('steps', {})
    start_step = workflow_def.get('start_step')
    
    # Create a mapping of step IDs to their details
    step_details = {}
    for step_id, step_def in steps.items():
        step_type = step_def.get('type', 'unknown')
        step_name = step_def.get('name', step_id)
        
        next_steps = []
        if step_type == 'task' and 'next_step' in step_def:
            next_steps.append(step_def['next_step'])
        elif step_type == 'decision':
            if 'true_step' in step_def:
                next_steps.append(step_def['true_step'])
            if 'false_step' in step_def:
                next_steps.append(step_def['false_step'])
        elif step_type == 'parallel':
            if 'next_step' in step_def:
                next_steps.append(step_def['next_step'])
        
        step_details[step_id] = {
            'type': step_type,
            'name': step_name,
            'next_steps': next_steps
        }
    
    # Start with the start step
    visited_steps = set()
    
    def add_step_to_diagram(step_id: str, indent: int = 0) -> None:
        if step_id in visited_steps:
            diagram_line = ' ' * indent + f"[{step_id}] (already visited)\n"
            nonlocal diagram
            diagram += diagram_line
            return
        
        visited_steps.add(step_id)
        
        if step_id not in step_details:
            diagram_line = ' ' * indent + f"[{step_id}] (unknown step)\n"
            nonlocal diagram
            diagram += diagram_line
            return
        
        step = step_details[step_id]
        step_type = step['type']
        step_name = step['name']
        
        if step_type == 'end':
            diagram_line = ' ' * indent + f"[{step_id}] {step_name} (END)\n"
            nonlocal diagram
            diagram += diagram_line
            return
        
        diagram_line = ' ' * indent + f"[{step_id}] {step_name} ({step_type})\n"
        nonlocal diagram
        diagram += diagram_line
        
        for next_step in step['next_steps']:
            connector_line = ' ' * indent + '  |\n'
            diagram += connector_line
            add_step_to_diagram(next_step, indent + 2)
    
    if start_step:
        add_step_to_diagram(start_step)
    
    # Save to file if requested
    if output_file:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(diagram)
            
            logger.info(f"Saved workflow diagram to {output_file}")
        except IOError:
            logger.error(f"Failed to save workflow diagram to {output_file}")
    
    return diagram

def validate_workflow_definition(workflow_def: Dict[str, Any]) -> List[str]:
    """
    Validate a workflow definition.
    
    Args:
        workflow_def: The workflow definition to validate
        
    Returns:
        List of validation errors, empty if valid
    """
    errors = []
    
    # Check required fields
    required_fields = ['id', 'name', 'description', 'version', 'start_step', 'steps']
    for field in required_fields:
        if field not in workflow_def:
            errors.append(f"Missing required field: {field}")
    
    # If any required fields are missing, return early
    if errors:
        return errors
    
    # Check that the start step exists
    start_step = workflow_def['start_step']
    if start_step not in workflow_def['steps']:
        errors.append(f"Start step not found in workflow steps: {start_step}")
    
    # Check that all steps have required fields
    for step_id, step_def in workflow_def['steps'].items():
        if 'id' not in step_def:
            errors.append(f"Missing 'id' field in step: {step_id}")
        elif step_def['id'] != step_id:
            errors.append(f"Step ID mismatch: {step_id} != {step_def['id']}")
        
        if 'type' not in step_def:
            errors.append(f"Missing 'type' field in step: {step_id}")
            continue
        
        # Check step-specific required fields
        step_type = step_def['type']
        
        if step_type == 'task':
            if 'task_id' not in step_def:
                errors.append(f"Missing 'task_id' field in task step: {step_id}")
            
            if 'next_step' in step_def and step_def['next_step'] not in workflow_def['steps']:
                errors.append(f"Next step not found in workflow steps: {step_def['next_step']}")
        
        elif step_type == 'decision':
            if 'condition' not in step_def:
                errors.append(f"Missing 'condition' field in decision step: {step_id}")
            
            if 'true_step' not in step_def:
                errors.append(f"Missing 'true_step' field in decision step: {step_id}")
            elif step_def['true_step'] not in workflow_def['steps']:
                errors.append(f"True step not found in workflow steps: {step_def['true_step']}")
            
            if 'false_step' not in step_def:
                errors.append(f"Missing 'false_step' field in decision step: {step_id}")
            elif step_def['false_step'] not in workflow_def['steps']:
                errors.append(f"False step not found in workflow steps: {step_def['false_step']}")
        
        elif step_type == 'parallel':
            if 'steps' not in step_def:
                errors.append(f"Missing 'steps' field in parallel step: {step_id}")
            else:
                for parallel_step in step_def['steps']:
                    if parallel_step not in workflow_def['steps']:
                        errors.append(f"Parallel step not found in workflow steps: {parallel_step}")
            
            if 'next_step' in step_def and step_def['next_step'] not in workflow_def['steps']:
                errors.append(f"Next step not found in workflow steps: {step_def['next_step']}")
        
        elif step_type == 'end':
            # No additional validation needed for end steps
            pass
        
        else:
            errors.append(f"Unknown step type: {step_type}")
    
    # Check for cycles in the workflow
    cycles = detect_cycles(workflow_def)
    if cycles:
        for cycle in cycles:
            errors.append(f"Cycle detected in workflow: {' -> '.join(cycle)}")
    
    return errors

def detect_cycles(workflow_def: Dict[str, Any]) -> List[List[str]]:
    """
    Detect cycles in a workflow.
    
    Args:
        workflow_def: The workflow definition
        
    Returns:
        List of cycles, each represented as a list of step IDs
    """
    cycles = []
    steps = workflow_def.get('steps', {})
    
    # Create a graph representation of the workflow
    graph = {}
    for step_id, step_def in steps.items():
        graph[step_id] = []
        
        step_type = step_def.get('type', 'unknown')
        
        if step_type == 'task' and 'next_step' in step_def:
            graph[step_id].append(step_def['next_step'])
        elif step_type == 'decision':
            if 'true_step' in step_def:
                graph[step_id].append(step_def['true_step'])
            if 'false_step' in step_def:
                graph[step_id].append(step_def['false_step'])
        elif step_type == 'parallel':
            if 'next_step' in step_def:
                graph[step_id].append(step_def['next_step'])
    
    # Detect cycles using DFS
    def dfs(node: str, path: List[str], visited: set) -> None:
        if node in path:
            # Cycle detected
            cycle_start = path.index(node)
            cycles.append(path[cycle_start:] + [node])
            return
        
        if node in visited:
            return
        
        visited.add(node)
        path.append(node)
        
        for neighbor in graph.get(node, []):
            dfs(neighbor, path.copy(), visited)
    
    # Start DFS from each node
    visited = set()
    for node in graph:
        if node not in visited:
            dfs(node, [], visited)
    
    return cycles
