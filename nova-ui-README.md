# NovaUI

NovaUI is a comprehensive UI component library for the NovaFuse platform, providing a consistent user experience across all NovaFuse products.

## Overview

NovaUI is a key component of the NovaFuse platform, providing the user interface for all NovaFuse products. It uses a feature toggle approach to enable/disable features based on the product tier, allowing for a single codebase to power multiple products.

## Products

NovaUI powers the following NovaFuse products:

1. **NovaPrime** - Comprehensive GRC platform with all features
2. **NovaCore** - Freemium version with limited functionality
3. **NovaShield** - Security-focused product
4. **NovaLearn** - Gamification and education platform
5. **NovaAssistAI** - AI-powered chatbot assistant

## Features

- **Feature Toggles**: Enable/disable features based on product tier
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Accessibility**: WCAG 2.1 AA compliant
- **Theming**: Customizable themes with dark mode support
- **Component Library**: Reusable UI components
- **State Management**: Centralized state management with Redux
- **Internationalization**: Multi-language support
- **Analytics**: User behavior tracking

## Architecture

NovaUI is built using a modern frontend architecture with the following components:

- **Next.js**: React framework for server-side rendering and static site generation
- **React**: UI library for building component-based interfaces
- **Redux**: State management
- **Tailwind CSS**: Utility-first CSS framework
- **Feature Flags**: Configuration-based feature toggles
- **API Client**: Centralized API communication

## Feature Flag System

NovaUI uses a feature flag system to enable/disable features based on the product tier. This allows for a single codebase to power multiple products with different feature sets.

### Feature Flag Configuration

```javascript
// Example feature flag configuration
const featureFlags = {
  // NovaPrime features (all enabled)
  novaPrime: {
    dashboard: true,
    reports: true,
    analytics: true,
    administration: true,
    integrations: true,
    advancedSecurity: true,
    gamification: true,
    aiAssistant: true
  },
  
  // NovaCore features (limited)
  novaCore: {
    dashboard: true,
    reports: true,
    analytics: false,
    administration: false,
    integrations: false,
    advancedSecurity: false,
    gamification: false,
    aiAssistant: false
  },
  
  // NovaShield features (security-focused)
  novaShield: {
    dashboard: true,
    reports: true,
    analytics: true,
    administration: true,
    integrations: false,
    advancedSecurity: true,
    gamification: false,
    aiAssistant: false
  },
  
  // NovaLearn features (gamification-focused)
  novaLearn: {
    dashboard: true,
    reports: false,
    analytics: false,
    administration: false,
    integrations: false,
    advancedSecurity: false,
    gamification: true,
    aiAssistant: false
  }
};
```

### Using Feature Flags

```jsx
// Example component with feature flag
import { useFeatureFlag } from '@/hooks/useFeatureFlag';

function AnalyticsPanel() {
  const isEnabled = useFeatureFlag('analytics');
  
  if (!isEnabled) {
    return null;
  }
  
  return (
    <div className="analytics-panel">
      {/* Analytics content */}
    </div>
  );
}
```

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/Dartan1983/nova-ui.git
   cd nova-ui
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Configure environment variables:
   ```
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. Start the development server:
   ```
   npm run dev
   ```

### Development

1. Start in development mode:
   ```
   npm run dev
   ```

2. Run tests:
   ```
   npm test
   ```

3. Build for production:
   ```
   npm run build
   ```

4. Start production server:
   ```
   npm start
   ```

## Folder Structure

```
nova-ui/
├── components/             # Reusable UI components
│   ├── common/             # Common components
│   ├── dashboard/          # Dashboard components
│   ├── forms/              # Form components
│   ├── layout/             # Layout components
│   ├── navigation/         # Navigation components
│   └── tables/             # Table components
├── config/                 # Configuration files
│   └── feature-flags.js    # Feature flag configuration
├── contexts/               # React contexts
├── hooks/                  # Custom React hooks
├── pages/                  # Next.js pages
│   ├── api/                # API routes
│   ├── dashboard/          # Dashboard pages
│   ├── privacy/            # Privacy Management pages
│   ├── compliance/         # Compliance pages
│   ├── security/           # Security pages
│   ├── control/            # Control Testing pages
│   └── esg/                # ESG pages
├── public/                 # Static assets
├── services/               # API services
├── store/                  # Redux store
│   ├── actions/            # Redux actions
│   ├── reducers/           # Redux reducers
│   └── selectors/          # Redux selectors
├── styles/                 # Global styles
├── utils/                  # Utility functions
└── next.config.js          # Next.js configuration
```

## Product-Specific UI Shells

NovaUI provides product-specific UI shells for each NovaFuse product:

1. **NovaPrime UI**: `/pages/novaprime/`
2. **NovaCore UI**: `/pages/novacore/`
3. **NovaShield UI**: `/pages/novashield/`
4. **NovaLearn UI**: `/pages/novalearning/`
5. **NovaAssistAI UI**: `/pages/novaassist/`

Each UI shell uses the same underlying components but with different feature flags enabled.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

For more information, contact the NovaFuse <NAME_EMAIL>.
