'use client';

import { useState } from 'react';

interface CBEHeaderProps {
  currentUrl: string;
  onNavigate: (url: string) => void;
  onHome: () => void;
  cbeMode: boolean;
  setCbeMode: (mode: boolean) => void;
  consciousnessData: {
    overall: number;
    coherence: number;
    psiSnap: boolean;
  };
}

export default function CBEHeader({ 
  currentUrl, 
  onNavigate, 
  onHome, 
  cbeMode, 
  setCbeMode, 
  consciousnessData 
}: CBEHeaderProps) {
  const [urlInput, setUrlInput] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (urlInput.trim()) {
      onNavigate(urlInput.trim());
      setUrlInput('');
    }
  };

  const getCoherenceColor = (score: number) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getCoherenceIndicatorColor = (score: number) => {
    if (score >= 90) return 'bg-green-400';
    if (score >= 70) return 'bg-yellow-400';
    return 'bg-red-400';
  };

  return (
    <header className="border-b border-purple-500/20 bg-black/20 backdrop-blur-xl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Logo & Title */}
          <div className="flex items-center space-x-4">
            <button 
              onClick={onHome}
              className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center hover:scale-105 transition-transform"
            >
              <span className="text-white font-bold text-xl animate-pulse">🌌</span>
            </button>
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                CBE Browser
              </h1>
              <p className="text-xs text-purple-300">
                Comphyological Browsing Engine
              </p>
            </div>
          </div>
          
          {/* Address Bar */}
          <div className="flex-1 max-w-2xl mx-8">
            <form onSubmit={handleSubmit} className="relative">
              <div className="flex items-center bg-gray-900/50 rounded-full px-4 py-3 border border-gray-600 hover:border-purple-400 transition-all">
                <div 
                  className={`w-3 h-3 rounded-full mr-3 animate-pulse ${getCoherenceIndicatorColor(consciousnessData.overall)}`} 
                  title={`Coherence: ${consciousnessData.overall}%`} 
                />
                <input
                  type="text"
                  value={urlInput}
                  onChange={(e) => setUrlInput(e.target.value)}
                  placeholder="Enter consciousness intention or URL..."
                  className="flex-1 bg-transparent text-white placeholder-gray-400 outline-none font-mono text-sm"
                />
                <button
                  type="submit"
                  className="ml-2 px-3 py-1 bg-purple-600 hover:bg-purple-700 rounded-full text-xs font-medium transition-colors"
                >
                  Navigate
                </button>
              </div>
            </form>
          </div>
          
          {/* Status Indicators */}
          <div className="flex items-center space-x-6">
            {/* Consciousness Level */}
            <div className="flex items-center space-x-2">
              <span className="text-blue-400">🧬</span>
              <span className="text-sm text-gray-300">
                Coherence: <span className={`font-mono ${getCoherenceColor(consciousnessData.overall)}`}>
                  {consciousnessData.overall}%
                </span>
              </span>
            </div>
            
            {/* Ψ-Snap Status */}
            <div className="flex items-center space-x-2">
              <span className="text-yellow-400">⚡</span>
              <span className="text-sm text-gray-300">
                Ψ-Snap: <span className={consciousnessData.psiSnap ? 'text-green-400' : 'text-red-400'}>
                  {consciousnessData.psiSnap ? 'ACTIVE' : 'INACTIVE'}
                </span>
              </span>
            </div>
            
            {/* CBE Mode Toggle */}
            <button 
              onClick={() => setCbeMode(!cbeMode)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                cbeMode 
                  ? 'bg-purple-600 text-white shadow-lg hover:bg-purple-700' 
                  : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
              }`}
            >
              <span className="text-purple-300">🌌</span>
              <span className="ml-2">{cbeMode ? 'CBE MODE' : 'STANDARD'}</span>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}
