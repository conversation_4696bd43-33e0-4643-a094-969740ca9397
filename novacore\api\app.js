/**
 * NovaCore API Server
 *
 * This is the main entry point for the NovaCore API server.
 * NovaCore is the foundation of the NovaFuse Cyber-Safety Platform.
 */

const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const compression = require('compression');
const path = require('path');
const { errorHandler } = require('./middleware');
const databaseManager = require('../config/database');
const logger = require('../config/logger');
const config = require('../config');

// Import routes
const {
  evidenceRoutes,
  blockchainRoutes,
  connectorRoutes,
  safetyRoutes,
  novaAssistAIRoutes,
  fileUploadRoutes,
  securityRoutes,
  complianceRoutes,
  monitoringRoutes,
  analyticsRoutes,
  userTestingRoutes,
  testResultsAnalysisRoutes,
  participantRecruitmentRoutes
} = require('./routes');

// Import NovaAssure component
const novaAssure = require('../api/novaassure');

// Import Cyber-Safety components
const { middleware: safetyMiddleware } = require('../cyber-safety');

// Import modules
const soc2Module = require('../modules/soc2');
const vendorAssessmentModule = require('../modules/vendor-assessment');
const novaflowModule = require('../modules/novaflow');

// Create Express app
const app = express();

// Set trust proxy
app.set('trust proxy', true);

// Configure middleware
app.use(helmet()); // Security headers
app.use(cors()); // CORS
app.use(compression()); // Compression
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } })); // Logging
app.use(express.json({ limit: '10mb' })); // JSON body parser
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // URL-encoded body parser

// Apply Cyber-Safety middleware
app.use(safetyMiddleware.safetyMiddleware);

// API routes
const apiPrefix = config.api.prefix;
app.use(`${apiPrefix}/evidence`, evidenceRoutes);
app.use(`${apiPrefix}/blockchain`, blockchainRoutes);
app.use(`${apiPrefix}/connectors`, connectorRoutes);
app.use(`${apiPrefix}/cyber-safety`, safetyRoutes);
app.use(`${apiPrefix}/soc2`, soc2Module.routes);
app.use(`${apiPrefix}/vendor-assessment`, vendorAssessmentModule.routes);
app.use(`${apiPrefix}/novaflow`, novaflowModule.routes);
app.use(`${apiPrefix}/nova-assist`, novaAssistAIRoutes);
app.use(`${apiPrefix}/uploads`, fileUploadRoutes);
app.use(`${apiPrefix}/security`, securityRoutes);
app.use(`${apiPrefix}/compliance`, complianceRoutes);
app.use(`${apiPrefix}/novaassure`, novaAssure.routes);
app.use(`${apiPrefix}/monitoring`, monitoringRoutes);
app.use(`${apiPrefix}/analytics`, analyticsRoutes);
app.use(`${apiPrefix}/user-testing`, userTestingRoutes);
app.use(`${apiPrefix}/user-testing`, testResultsAnalysisRoutes);
app.use(`${apiPrefix}/user-testing`, participantRecruitmentRoutes);

// Serve uploaded files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    service: 'novacore-api',
    description: 'NovaCore - Foundation of the NovaFuse Cyber-Safety Platform',
    version: process.env.npm_package_version || '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// API documentation endpoint
app.get('/', (req, res) => {
  res.status(200).json({
    name: 'NovaCore API',
    description: 'Foundation of the NovaFuse Cyber-Safety Platform',
    tagline: 'Trust, Automated',
    version: process.env.npm_package_version || '1.0.0',
    features: [
      'Evidence Management with Blockchain Verification',
      'Universal API Connector System',
      'Cyber-Safety by Design',
      'Compliance Built Into Every API',
      'SOC 2 Automation',
      'SaaS Vendor Assessment',
      'Compliance Workflow Orchestration',
      'Zero Trust Security Architecture',
      'Blockchain-Based Evidence System',
      'Dynamic Compliance Badge System',
      'Comprehensive Compliance Kit',
      'Universal Control Testing Framework (NovaAssure)'
    ],
    endpoints: [
      `${apiPrefix}/evidence`,
      `${apiPrefix}/blockchain`,
      `${apiPrefix}/connectors`,
      `${apiPrefix}/cyber-safety`,
      `${apiPrefix}/soc2`,
      `${apiPrefix}/vendor-assessment`,
      `${apiPrefix}/novaflow`,
      `${apiPrefix}/nova-assist`,
      `${apiPrefix}/uploads`,
      `${apiPrefix}/security`,
      `${apiPrefix}/compliance`,
      `${apiPrefix}/novaassure`,
      `${apiPrefix}/monitoring`,
      `${apiPrefix}/analytics`,
      `${apiPrefix}/user-testing`
    ],
    documentation: '/docs'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: {
      message: `Route not found: ${req.method} ${req.originalUrl}`,
      code: 'NOT_FOUND_ERROR'
    }
  });
});

// Error handler
app.use(errorHandler);

// Export app
module.exports = app;
