# Triadic Affiliate System Configuration

## ShareASale API Credentials (Optional - For Full API Access)
# To use the full API features, you need to provide your ShareASale credentials
# If you don't have API keys, the system will fall back to public scraping mode

# ShareASale API Key
SHAREASALE_API_KEY=your_api_key_here

# ShareASale Affiliate ID
SHAREASALE_AFFILIATE_ID=your_affiliate_id_here
 
## System Configuration
# Set to true to use public scraping mode (no API keys needed)
PUBLIC_ONLY=false

# Platform selection (currently only supports 'sharesale')
PLATFORM=sharesale

# Create .env File
# The system will automatically create a .env file from this template
# This file contains all your configuration settings

2. **Enter Credentials**
   - Run the setup script:
   ```bash
node scripts/setup-clickbank.js
```
   - The script will ask for your ClickBank username and password
   - Password input will be hidden for security

3. **Verify Setup**
   - The script will confirm successful setup
   - You can verify your credentials by checking the `.env` file
   - The file should contain your ClickBank credentials

## Security Notes

1. **Keep .env Secure**
   - Never share your `.env` file
   - Never commit it to version control
   - The file is automatically ignored by git

2. **Password Protection**
   - Password input is hidden during setup
   - Credentials are stored in plain text in `.env`
   - Consider using environment variable manager for production

3. **API Security**
   - The system uses HTTPS for all API calls
   - Credentials are encrypted in transit
   - Access is rate-limited for security

## Troubleshooting

1. **Missing .env**
   - If `.env` is missing, run setup script
   - Or copy `.env.example` manually:
   ```bash
cp .env.example .env
```

2. **Invalid Credentials**
   - Double-check your ClickBank login
   - Ensure you have affiliate access
   - Verify account verification status

3. **API Errors**
   - Check network connection
   - Verify ClickBank API status
   - Contact ClickBank support if needed

## Next Steps

After setup, you can:
1. Run product discovery:
```bash
node scripts/discover-products.js
```

2. Access the dashboard:
```
http://localhost:3000
```

3. Start optimizing campaigns:
- Scan for products
- Apply triadic optimization
- Generate κ-boosted links
- Monitor performance

## Support

For assistance:
- Email: <EMAIL>
- Telegram: @TriadicAffiliateSupport
- Phone: +****************
# Triadic Affiliate System Configuration

# ClickBank API Credentials
CLICKBANK_USERNAME=your_username
CLICKBANK_PASSWORD=your_password
CLICKBANK_API_KEY=your_clickbank_api_key

# NovaShield Settings
NOVA_SHIELD_Ψ_THRESHOLD=0.85
NOVA_SHIELD_φ_RESONANCE=1.618
NOVA_SHIELD_κ_BOOST=3142

# ShareASale API Credentials
SHAREASALE_USERNAME=your_shareasale_username
SHAREASALE_PASSWORD=your_shareasale_password
SHAREASALE_API_KEY=your_shareasale_api_key

# System Configuration
CONSCIOUSNESS_THRESHOLD=0.85
TRIADIC_WEIGHTS=0.4,0.4,0.2
K_BOOST_MULTIPLIER=3142

# Monitoring Settings
MONITORING_INTERVAL=300
MAX_SCAN_RATE=100

# White-Label Settings
BRAND_NAME=YourBrandName
BRAND_COLOR=#007bff
BRAND_LOGO=/path/to/your/logo.png
