/**
 * CSFE NovaVision Dashboard
 * 
 * This module implements a dashboard for visualizing CSFE calculations and financial predictions.
 * It integrates with the NovaVision Universal UI Connector to provide a consistent user experience
 * across the NovaFuse platform.
 */

const express = require('express');
const path = require('path');
const { CSFEEngine, TrinityCSFEEngine, TrinityCSFE1882Engine } = require('../index');
const bodyParser = require('body-parser');
const cors = require('cors');

// Create Express app
const app = express();
const port = process.env.PORT || 3002;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, 'public')));

// Create CSFE Engine instances
const csfeEngine = new CSFEEngine();
const trinityCSFEEngine = new TrinityCSFEEngine();
const trinityCSFE1882Engine = new TrinityCSFE1882Engine();

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

/**
 * Get CSFE dashboard data
 * 
 * GET /api/dashboard
 */
app.get('/api/dashboard', (req, res) => {
  res.json({
    name: 'CSFE NovaVision Dashboard',
    version: '1.0.0',
    description: 'Dashboard for visualizing CSFE calculations and financial predictions',
    components: [
      { id: 'csfe-overview', name: 'CSFE Overview', description: 'Overview of CSFE calculations' },
      { id: 'market-predictions', name: 'Market Predictions', description: 'Financial market predictions' },
      { id: 'asset-allocation', name: 'Asset Allocation', description: 'Optimal asset allocation' },
      { id: 'risk-assessment', name: 'Risk Assessment', description: 'Financial risk assessment' },
      { id: 'timeline-predictions', name: 'Timeline Predictions', description: 'Financial timeline predictions' },
      { id: 'depression-prediction', name: 'Depression Prediction', description: 'Depression prediction for 2027-2031' }
    ]
  });
});

/**
 * Calculate CSFE value and return dashboard data
 * 
 * POST /api/calculate
 * 
 * Request body:
 * {
 *   "marketData": { ... },
 *   "economicData": { ... },
 *   "sentimentData": { ... },
 *   "engine": "standard" | "trinity" | "trinity-1882"
 * }
 */
app.post('/api/calculate', (req, res) => {
  try {
    const { marketData, economicData, sentimentData, engine = 'standard' } = req.body;
    
    // Validate input
    if (!marketData || !economicData || !sentimentData) {
      return res.status(400).json({
        error: 'Missing required data',
        message: 'marketData, economicData, and sentimentData are required'
      });
    }
    
    // Calculate CSFE value based on selected engine
    let result;
    switch (engine) {
      case 'trinity':
        result = trinityCSFEEngine.calculate(marketData, economicData, sentimentData);
        break;
      case 'trinity-1882':
        result = trinityCSFE1882Engine.calculate(marketData, economicData, sentimentData);
        break;
      case 'standard':
      default:
        result = csfeEngine.calculate(marketData, economicData, sentimentData);
        break;
    }
    
    // Format result for dashboard
    const dashboardData = formatResultForDashboard(result, engine);
    
    // Return result
    res.json(dashboardData);
  } catch (error) {
    console.error('Error calculating CSFE value:', error);
    res.status(500).json({
      error: 'CSFE calculation failed',
      message: error.message
    });
  }
});

/**
 * Get sample data for dashboard
 * 
 * GET /api/sample-data
 */
app.get('/api/sample-data', (req, res) => {
  // Sample market data
  const marketData = {
    price: {
      current: 100,
      moving_average: 95,
      history: [90, 92, 95, 98, 100]
    },
    volume: {
      current: 1000000,
      average: 900000,
      trend: 'increasing'
    },
    liquidity: {
      value: 0.8,
      trend: 'stable'
    },
    volatility: {
      value: 15,
      trend: 'decreasing'
    },
    depth: {
      value: 0.7,
      trend: 'increasing'
    },
    spread: {
      value: 0.5,
      trend: 'decreasing'
    }
  };

  // Sample economic data
  const economicData = {
    gdp: {
      value: 21000,
      growth: 2.5,
      trend: 'increasing'
    },
    inflation: {
      rate: 2.1,
      core: 1.8,
      trend: 'stable'
    },
    unemployment: {
      rate: 3.8,
      trend: 'decreasing'
    },
    interestRates: {
      fed_funds: 0.25,
      ten_year: 1.5,
      trend: 'stable'
    },
    pmi: {
      value: 53.5,
      trend: 'increasing'
    },
    consumerConfidence: {
      value: 110,
      trend: 'increasing'
    },
    buildingPermits: {
      value: 1800000,
      growth: 3.2,
      trend: 'increasing'
    }
  };

  // Sample sentiment data
  const sentimentData = {
    retail: {
      bullishPercentage: 65,
      bearishPercentage: 35,
      trend: 'increasing'
    },
    institutional: {
      bullishPercentage: 55,
      bearishPercentage: 45,
      netPositioning: 10,
      trend: 'stable'
    },
    media: {
      sentiment: 0.6,
      volume: 1000,
      trend: 'increasing'
    },
    social: {
      sentiment: 0.7,
      volume: 5000,
      trend: 'increasing'
    },
    futures: {
      commercialNetPositioning: 15,
      nonCommercialNetPositioning: -5,
      trend: 'increasing'
    }
  };

  res.json({
    marketData,
    economicData,
    sentimentData
  });
});

/**
 * Format CSFE result for dashboard
 * @param {Object} result - CSFE calculation result
 * @param {String} engine - Engine type
 * @returns {Object} - Formatted dashboard data
 */
function formatResultForDashboard(result, engine) {
  if (engine === 'trinity' || engine === 'trinity-1882') {
    return {
      engine,
      csfeValue: result.csfeTrinity,
      performanceFactor: result.performanceFactor,
      calculatedAt: result.timestamp,
      components: {
        father: result.fatherComponent,
        son: result.sonComponent,
        spirit: result.spiritComponent
      }
    };
  } else {
    return {
      engine: 'standard',
      csfeValue: result.csfeValue,
      performanceFactor: result.performanceFactor,
      calculatedAt: result.calculatedAt,
      components: {
        market: result.marketComponent,
        economic: result.economicComponent,
        sentiment: result.sentimentComponent,
        tensor: result.tensorProduct,
        fusion: result.fusionResult
      },
      predictions: result.financialPredictions
    };
  }
}

/**
 * Start the dashboard server
 */
function start() {
  return new Promise((resolve, reject) => {
    try {
      const server = app.listen(port, () => {
        console.log(`CSFE NovaVision Dashboard running on port ${port}`);
        resolve(server);
      });
    } catch (error) {
      console.error('Failed to start CSFE NovaVision Dashboard:', error);
      reject(error);
    }
  });
}

// Export the app and start function
module.exports = {
  app,
  start
};

// Start the server if this file is run directly
if (require.main === module) {
  start().catch(error => {
    console.error('Failed to start CSFE NovaVision Dashboard:', error);
    process.exit(1);
  });
}
