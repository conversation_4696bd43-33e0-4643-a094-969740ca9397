/**
 * Analytics Adapter
 * 
 * This module provides an adapter for the analytics dashboard to integrate
 * with the unified integration system.
 */

const EventEmitter = require('events');

/**
 * AnalyticsAdapter class
 */
class AnalyticsAdapter extends EventEmitter {
  /**
   * Create a new AnalyticsAdapter instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      autoConnect: true,
      ...options
    };
    
    // Store analytics instance
    this.analytics = options.analytics || null;
    
    // Initialize state
    this.state = {
      isConnected: false,
      lastUpdate: null,
      metrics: new Map(),
      dashboards: new Map(),
      activeQueries: new Set()
    };
    
    // Connect to analytics if provided and autoConnect is true
    if (this.analytics && this.options.autoConnect) {
      this.connect();
    }
    
    if (this.options.enableLogging) {
      console.log('AnalyticsAdapter initialized');
    }
  }
  
  /**
   * Connect to the analytics system
   * @returns {boolean} - Success status
   */
  connect() {
    if (!this.analytics) {
      if (this.options.enableLogging) {
        console.log('AnalyticsAdapter: No analytics instance provided');
      }
      return false;
    }
    
    if (this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('AnalyticsAdapter: Already connected');
      }
      return true;
    }
    
    // Set up event listeners
    this._setupEventListeners();
    
    // Discover available metrics and dashboards
    this._discoverMetrics();
    this._discoverDashboards();
    
    // Update state
    this.state.isConnected = true;
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('connected', {
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log('AnalyticsAdapter: Connected to analytics system');
    }
    
    return true;
  }
  
  /**
   * Disconnect from the analytics system
   * @returns {boolean} - Success status
   */
  disconnect() {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('AnalyticsAdapter: Not connected');
      }
      return false;
    }
    
    // Remove event listeners
    this._removeEventListeners();
    
    // Update state
    this.state.isConnected = false;
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('disconnected', {
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log('AnalyticsAdapter: Disconnected from analytics system');
    }
    
    return true;
  }
  
  /**
   * Set up event listeners
   * @private
   */
  _setupEventListeners() {
    if (!this.analytics) {
      return;
    }
    
    // Set up event listeners based on the analytics system
    if (this.analytics.on) {
      // If the analytics system has an event emitter interface
      this.analytics.on('metrics-updated', this._handleMetricsUpdated.bind(this));
      this.analytics.on('dashboard-updated', this._handleDashboardUpdated.bind(this));
      this.analytics.on('query-started', this._handleQueryStarted.bind(this));
      this.analytics.on('query-completed', this._handleQueryCompleted.bind(this));
      this.analytics.on('query-error', this._handleQueryError.bind(this));
    }
  }
  
  /**
   * Remove event listeners
   * @private
   */
  _removeEventListeners() {
    if (!this.analytics || !this.analytics.removeAllListeners) {
      return;
    }
    
    // Remove analytics event listeners
    this.analytics.removeAllListeners('metrics-updated');
    this.analytics.removeAllListeners('dashboard-updated');
    this.analytics.removeAllListeners('query-started');
    this.analytics.removeAllListeners('query-completed');
    this.analytics.removeAllListeners('query-error');
  }
  
  /**
   * Discover available metrics
   * @private
   */
  _discoverMetrics() {
    if (!this.analytics) {
      return;
    }
    
    // Check for a getMetrics method
    if (typeof this.analytics.getMetrics === 'function') {
      const metrics = this.analytics.getMetrics();
      
      if (metrics && typeof metrics === 'object') {
        for (const [key, value] of Object.entries(metrics)) {
          this.state.metrics.set(key, value);
        }
      }
    }
    
    // Check for a getAvailableMetrics method
    if (typeof this.analytics.getAvailableMetrics === 'function') {
      const availableMetrics = this.analytics.getAvailableMetrics();
      
      if (Array.isArray(availableMetrics)) {
        for (const metric of availableMetrics) {
          if (typeof metric === 'string') {
            this.state.metrics.set(metric, null);
          } else if (metric && typeof metric === 'object' && metric.id) {
            this.state.metrics.set(metric.id, metric);
          }
        }
      }
    }
    
    if (this.options.enableLogging) {
      console.log(`AnalyticsAdapter: Discovered ${this.state.metrics.size} metrics`);
    }
  }
  
  /**
   * Discover available dashboards
   * @private
   */
  _discoverDashboards() {
    if (!this.analytics) {
      return;
    }
    
    // Check for a getDashboards method
    if (typeof this.analytics.getDashboards === 'function') {
      const dashboards = this.analytics.getDashboards();
      
      if (Array.isArray(dashboards)) {
        for (const dashboard of dashboards) {
          if (dashboard && typeof dashboard === 'object' && dashboard.id) {
            this.state.dashboards.set(dashboard.id, dashboard);
          }
        }
      }
    }
    
    if (this.options.enableLogging) {
      console.log(`AnalyticsAdapter: Discovered ${this.state.dashboards.size} dashboards`);
    }
  }
  
  /**
   * Handle metrics updated event
   * @param {Object} data - Event data
   * @private
   */
  _handleMetricsUpdated(data) {
    // Update metrics
    if (data && typeof data === 'object') {
      for (const [key, value] of Object.entries(data)) {
        this.state.metrics.set(key, value);
      }
    }
    
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('metrics-updated', {
      metrics: data,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log('AnalyticsAdapter: Metrics updated');
    }
  }
  
  /**
   * Handle dashboard updated event
   * @param {Object} data - Event data
   * @private
   */
  _handleDashboardUpdated(data) {
    // Update dashboard
    if (data && typeof data === 'object' && data.id) {
      this.state.dashboards.set(data.id, data);
    }
    
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('dashboard-updated', {
      dashboard: data,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`AnalyticsAdapter: Dashboard updated - ${data.id}`);
    }
  }
  
  /**
   * Handle query started event
   * @param {Object} data - Event data
   * @private
   */
  _handleQueryStarted(data) {
    // Add query to active queries
    if (data && typeof data === 'object' && data.id) {
      this.state.activeQueries.add(data.id);
    }
    
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('query-started', {
      query: data,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`AnalyticsAdapter: Query started - ${data.id}`);
    }
  }
  
  /**
   * Handle query completed event
   * @param {Object} data - Event data
   * @private
   */
  _handleQueryCompleted(data) {
    // Remove query from active queries
    if (data && typeof data === 'object' && data.id) {
      this.state.activeQueries.delete(data.id);
    }
    
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('query-completed', {
      query: data,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`AnalyticsAdapter: Query completed - ${data.id}`);
    }
  }
  
  /**
   * Handle query error event
   * @param {Object} data - Event data
   * @private
   */
  _handleQueryError(data) {
    // Remove query from active queries
    if (data && typeof data === 'object' && data.id) {
      this.state.activeQueries.delete(data.id);
    }
    
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('query-error', {
      query: data,
      error: data.error,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`AnalyticsAdapter: Query error - ${data.id}: ${data.error}`);
    }
  }
  
  /**
   * Get available metrics
   * @returns {Object} - Metrics object
   */
  getMetrics() {
    const metrics = {};
    
    for (const [key, value] of this.state.metrics.entries()) {
      metrics[key] = value;
    }
    
    return metrics;
  }
  
  /**
   * Get available dashboards
   * @returns {Array} - Array of dashboards
   */
  getDashboards() {
    return Array.from(this.state.dashboards.values());
  }
  
  /**
   * Get a dashboard by ID
   * @param {string} id - Dashboard ID
   * @returns {Object|null} - Dashboard or null if not found
   */
  getDashboard(id) {
    return this.state.dashboards.get(id) || null;
  }
  
  /**
   * Execute a query
   * @param {string} query - Query to execute
   * @param {Object} params - Query parameters
   * @returns {Promise} - Promise that resolves with query results
   */
  executeQuery(query, params = {}) {
    if (!this.analytics) {
      throw new Error('No analytics instance provided');
    }
    
    if (!this.state.isConnected) {
      throw new Error('Not connected to analytics system');
    }
    
    // Execute query
    if (typeof this.analytics.executeQuery === 'function') {
      return this.analytics.executeQuery(query, params);
    }
    
    throw new Error('Analytics system does not support query execution');
  }
}

module.exports = AnalyticsAdapter;
