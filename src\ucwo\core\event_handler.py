"""
Event Handler for the Universal Compliance Workflow Orchestrator.

This module provides functionality for handling workflow events.
"""

import logging
from typing import Dict, List, Any, Callable, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EventHandler:
    """
    Handler for workflow events.
    
    This class is responsible for registering event handlers and triggering
    events during workflow execution.
    """
    
    def __init__(self):
        """Initialize the Event Handler."""
        logger.info("Initializing Event Handler")
        
        # Initialize the event handlers dictionary
        # Structure: {event_type: [handler1, handler2, ...]}
        self.event_handlers: Dict[str, List[Callable]] = {}
        
        # Register default event handlers
        self._register_default_handlers()
        
        logger.info("Event Handler initialized")
    
    def _register_default_handlers(self) -> None:
        """Register default event handlers."""
        # Register handlers for workflow lifecycle events
        self.register_handler('workflow_started', self._log_workflow_started)
        self.register_handler('workflow_completed', self._log_workflow_completed)
        self.register_handler('workflow_failed', self._log_workflow_failed)
        self.register_handler('workflow_cancelled', self._log_workflow_cancelled)
        
        # Register handlers for task events
        self.register_handler('task_started', self._log_task_started)
        self.register_handler('task_completed', self._log_task_completed)
        self.register_handler('task_failed', self._log_task_failed)
        
        # Register handlers for specific compliance events
        self.register_handler('data_breach_detected', self._handle_data_breach)
        self.register_handler('high_risk_vendor_detected', self._handle_high_risk_vendor)
    
    def register_handler(self, event_type: str, handler: Callable) -> None:
        """
        Register a handler for a specific event type.
        
        Args:
            event_type: The type of event
            handler: The handler function
        """
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        
        self.event_handlers[event_type].append(handler)
        logger.info(f"Registered handler for event type: {event_type}")
    
    def unregister_handler(self, event_type: str, handler: Callable) -> None:
        """
        Unregister a handler for a specific event type.
        
        Args:
            event_type: The type of event
            handler: The handler function
        """
        if event_type in self.event_handlers and handler in self.event_handlers[event_type]:
            self.event_handlers[event_type].remove(handler)
            logger.info(f"Unregistered handler for event type: {event_type}")
    
    def trigger_event(self, event_type: str, event_data: Optional[Dict[str, Any]] = None) -> None:
        """
        Trigger an event.
        
        Args:
            event_type: The type of event
            event_data: Data associated with the event
        """
        logger.info(f"Triggering event: {event_type}")
        
        if event_data is None:
            event_data = {}
        
        # Add event type to the event data
        event_data['event_type'] = event_type
        
        # Add timestamp to the event data
        event_data['timestamp'] = self._get_current_timestamp()
        
        # Call all registered handlers for this event type
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    handler(event_data)
                except Exception as e:
                    logger.error(f"Error in event handler for {event_type}: {e}")
    
    def get_registered_event_types(self) -> List[str]:
        """
        Get all registered event types.
        
        Returns:
            List of event types
        """
        return list(self.event_handlers.keys())
    
    # Default event handlers
    
    def _log_workflow_started(self, event_data: Dict[str, Any]) -> None:
        """
        Log a workflow started event.
        
        Args:
            event_data: Data associated with the event
        """
        logger.info(f"Workflow started: {event_data.get('workflow_id')}, Instance ID: {event_data.get('instance_id')}")
    
    def _log_workflow_completed(self, event_data: Dict[str, Any]) -> None:
        """
        Log a workflow completed event.
        
        Args:
            event_data: Data associated with the event
        """
        logger.info(f"Workflow completed: {event_data.get('workflow_id')}, Instance ID: {event_data.get('instance_id')}")
    
    def _log_workflow_failed(self, event_data: Dict[str, Any]) -> None:
        """
        Log a workflow failed event.
        
        Args:
            event_data: Data associated with the event
        """
        logger.info(f"Workflow failed: {event_data.get('workflow_id')}, Instance ID: {event_data.get('instance_id')}, Error: {event_data.get('error')}")
    
    def _log_workflow_cancelled(self, event_data: Dict[str, Any]) -> None:
        """
        Log a workflow cancelled event.
        
        Args:
            event_data: Data associated with the event
        """
        logger.info(f"Workflow cancelled: {event_data.get('workflow_id')}, Instance ID: {event_data.get('instance_id')}")
    
    def _log_task_started(self, event_data: Dict[str, Any]) -> None:
        """
        Log a task started event.
        
        Args:
            event_data: Data associated with the event
        """
        logger.info(f"Task started: {event_data.get('task_id')}, Workflow Instance ID: {event_data.get('instance_id')}")
    
    def _log_task_completed(self, event_data: Dict[str, Any]) -> None:
        """
        Log a task completed event.
        
        Args:
            event_data: Data associated with the event
        """
        logger.info(f"Task completed: {event_data.get('task_id')}, Workflow Instance ID: {event_data.get('instance_id')}")
    
    def _log_task_failed(self, event_data: Dict[str, Any]) -> None:
        """
        Log a task failed event.
        
        Args:
            event_data: Data associated with the event
        """
        logger.info(f"Task failed: {event_data.get('task_id')}, Workflow Instance ID: {event_data.get('instance_id')}, Error: {event_data.get('error')}")
    
    def _handle_data_breach(self, event_data: Dict[str, Any]) -> None:
        """
        Handle a data breach event.
        
        Args:
            event_data: Data associated with the event
        """
        logger.info(f"Data breach detected: {event_data.get('breach_id')}")
        
        # In a real implementation, this would trigger appropriate actions
        # For example, starting a data breach notification workflow
        logger.info("Initiating data breach response procedures")
    
    def _handle_high_risk_vendor(self, event_data: Dict[str, Any]) -> None:
        """
        Handle a high risk vendor event.
        
        Args:
            event_data: Data associated with the event
        """
        logger.info(f"High risk vendor detected: {event_data.get('vendor_id')}")
        
        # In a real implementation, this would trigger appropriate actions
        # For example, escalating the vendor assessment to senior management
        logger.info("Escalating vendor assessment to senior management")
    
    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.
        
        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
