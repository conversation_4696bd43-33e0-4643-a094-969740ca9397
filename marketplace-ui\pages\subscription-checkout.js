import { useState } from "react";
import Head from "next/head";
import { useRouter } from "next/router";

export default function SubscriptionCheckout() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    cardNumber: "",
    expiryDate: "",
    cvc: "",
    billingAddress: "",
    city: "",
    state: "",
    zipCode: "",
    country: "US"
  });
  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
    
    // Clear error when field is changed
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name) newErrors.name = "Name is required";
    if (!formData.email) newErrors.email = "Email is required";
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = "Email is invalid";
    if (!formData.company) newErrors.company = "Company is required";
    
    if (!formData.cardNumber) newErrors.cardNumber = "Card number is required";
    if (formData.cardNumber && !/^\d{16}$/.test(formData.cardNumber.replace(/\s/g, ""))) {
      newErrors.cardNumber = "Card number must be 16 digits";
    }
    
    if (!formData.expiryDate) newErrors.expiryDate = "Expiry date is required";
    if (formData.expiryDate && !/^(0[1-9]|1[0-2])\/\d{2}$/.test(formData.expiryDate)) {
      newErrors.expiryDate = "Expiry date must be in MM/YY format";
    }
    
    if (!formData.cvc) newErrors.cvc = "CVC is required";
    if (formData.cvc && !/^\d{3,4}$/.test(formData.cvc)) {
      newErrors.cvc = "CVC must be 3 or 4 digits";
    }
    
    if (!formData.billingAddress) newErrors.billingAddress = "Billing address is required";
    if (!formData.city) newErrors.city = "City is required";
    if (!formData.state) newErrors.state = "State is required";
    if (!formData.zipCode) newErrors.zipCode = "ZIP code is required";
    
    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const newErrors = validateForm();
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    setLoading(true);
    
    // In a real implementation, this would submit to a payment processor API
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Redirect to success page
      router.push("/subscription-success");
    } catch (error) {
      console.error("Error processing payment:", error);
      setErrors({
        submit: "There was an error processing your payment. Please try again."
      });
    } finally {
      setLoading(false);
    }
  };

  // Mock subscription details
  const subscriptionDetails = {
    plan: "Professional",
    price: 499,
    billingCycle: "monthly",
    features: [
      "50,000 API calls per month",
      "15 API endpoints",
      "Priority support",
      "Enhanced SLA (99.5% uptime)"
    ]
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>Checkout | NovaFuse API Superstore</title>
        <meta name="description" content="Complete your NovaFuse API Superstore subscription" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-gray-900">Complete Your Subscription</h1>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Checkout Form */}
          <div className="md:col-span-2">
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold">Payment Information</h2>
              </div>
              
              <form onSubmit={handleSubmit} className="p-6">
                {errors.submit && (
                  <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    {errors.submit}
                  </div>
                )}
                
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-4">Account Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-gray-700 mb-2" htmlFor="name">
                        Full Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        className={`w-full p-2 border rounded ${
                          errors.name ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="John Doe"
                      />
                      {errors.name && (
                        <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-gray-700 mb-2" htmlFor="email">
                        Email <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        className={`w-full p-2 border rounded ${
                          errors.email ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="<EMAIL>"
                      />
                      {errors.email && (
                        <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                      )}
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-gray-700 mb-2" htmlFor="company">
                        Company <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="company"
                        name="company"
                        value={formData.company}
                        onChange={handleChange}
                        className={`w-full p-2 border rounded ${
                          errors.company ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Acme Inc."
                      />
                      {errors.company && (
                        <p className="text-red-500 text-sm mt-1">{errors.company}</p>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-4">Payment Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="md:col-span-2">
                      <label className="block text-gray-700 mb-2" htmlFor="cardNumber">
                        Card Number <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="cardNumber"
                        name="cardNumber"
                        value={formData.cardNumber}
                        onChange={handleChange}
                        className={`w-full p-2 border rounded ${
                          errors.cardNumber ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="4242 4242 4242 4242"
                      />
                      {errors.cardNumber && (
                        <p className="text-red-500 text-sm mt-1">{errors.cardNumber}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-gray-700 mb-2" htmlFor="expiryDate">
                        Expiry Date <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="expiryDate"
                        name="expiryDate"
                        value={formData.expiryDate}
                        onChange={handleChange}
                        className={`w-full p-2 border rounded ${
                          errors.expiryDate ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="MM/YY"
                      />
                      {errors.expiryDate && (
                        <p className="text-red-500 text-sm mt-1">{errors.expiryDate}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-gray-700 mb-2" htmlFor="cvc">
                        CVC <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="cvc"
                        name="cvc"
                        value={formData.cvc}
                        onChange={handleChange}
                        className={`w-full p-2 border rounded ${
                          errors.cvc ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="123"
                      />
                      {errors.cvc && (
                        <p className="text-red-500 text-sm mt-1">{errors.cvc}</p>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-4">Billing Address</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="md:col-span-2">
                      <label className="block text-gray-700 mb-2" htmlFor="billingAddress">
                        Address <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="billingAddress"
                        name="billingAddress"
                        value={formData.billingAddress}
                        onChange={handleChange}
                        className={`w-full p-2 border rounded ${
                          errors.billingAddress ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="123 Main St"
                      />
                      {errors.billingAddress && (
                        <p className="text-red-500 text-sm mt-1">{errors.billingAddress}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-gray-700 mb-2" htmlFor="city">
                        City <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="city"
                        name="city"
                        value={formData.city}
                        onChange={handleChange}
                        className={`w-full p-2 border rounded ${
                          errors.city ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="San Francisco"
                      />
                      {errors.city && (
                        <p className="text-red-500 text-sm mt-1">{errors.city}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-gray-700 mb-2" htmlFor="state">
                        State <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="state"
                        name="state"
                        value={formData.state}
                        onChange={handleChange}
                        className={`w-full p-2 border rounded ${
                          errors.state ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="CA"
                      />
                      {errors.state && (
                        <p className="text-red-500 text-sm mt-1">{errors.state}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-gray-700 mb-2" htmlFor="zipCode">
                        ZIP Code <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="zipCode"
                        name="zipCode"
                        value={formData.zipCode}
                        onChange={handleChange}
                        className={`w-full p-2 border rounded ${
                          errors.zipCode ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="94103"
                      />
                      {errors.zipCode && (
                        <p className="text-red-500 text-sm mt-1">{errors.zipCode}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-gray-700 mb-2" htmlFor="country">
                        Country <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="country"
                        name="country"
                        value={formData.country}
                        onChange={handleChange}
                        className="w-full p-2 border border-gray-300 rounded"
                      >
                        <option value="US">United States</option>
                        <option value="CA">Canada</option>
                        <option value="GB">United Kingdom</option>
                        <option value="AU">Australia</option>
                        <option value="DE">Germany</option>
                        <option value="FR">France</option>
                      </select>
                    </div>
                  </div>
                </div>
                
                <div className="mt-8">
                  <button
                    type="submit"
                    disabled={loading}
                    className={`w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium ${
                      loading ? "opacity-50 cursor-not-allowed" : "hover:bg-blue-700"
                    }`}
                  >
                    {loading ? "Processing..." : `Pay $${subscriptionDetails.price}/${subscriptionDetails.billingCycle}`}
                  </button>
                  <p className="text-sm text-gray-500 mt-2 text-center">
                    Your card will be charged immediately
                  </p>
                </div>
              </form>
            </div>
          </div>
          
          {/* Order Summary */}
          <div className="md:col-span-1">
            <div className="bg-white rounded-lg shadow sticky top-6">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold">Order Summary</h2>
              </div>
              <div className="p-6">
                <div className="flex justify-between mb-4">
                  <span className="font-medium">{subscriptionDetails.plan} Plan</span>
                  <span>${subscriptionDetails.price}/{subscriptionDetails.billingCycle}</span>
                </div>
                
                <div className="border-t border-gray-200 pt-4 mb-4">
                  <h3 className="font-medium mb-2">Includes:</h3>
                  <ul className="space-y-2">
                    {subscriptionDetails.features.map((feature, index) => (
                      <li key={index} className="flex items-start text-sm">
                        <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div className="border-t border-gray-200 pt-4">
                  <div className="flex justify-between font-medium">
                    <span>Total</span>
                    <span>${subscriptionDetails.price}/{subscriptionDetails.billingCycle}</span>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">
                    You can cancel or change your subscription at any time.
                  </p>
                </div>
                
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="flex items-center">
                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    <span className="text-sm text-gray-600">Secure payment processing</span>
                  </div>
                  <div className="flex items-center mt-2">
                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    <span className="text-sm text-gray-600">30-day money-back guarantee</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <footer className="bg-gray-800 text-white py-12 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} NovaFuse. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
