/**
 * APIs, iPaaS & Developer Tools Connector Implementation
 * 
 * This module implements the connector for API management, iPaaS, and developer tools platforms.
 */

const axios = require('axios');
const { createLogger } = require('../../utils/logger');

const logger = createLogger('apis-ipaas-developer-tools-connector');

/**
 * APIs, iPaaS & Developer Tools Connector
 */
class ApisIpaasDeveloperToolsConnector {
  /**
   * Constructor
   * 
   * @param {Object} config - Connector configuration
   * @param {Object} credentials - Connector credentials
   */
  constructor(config, credentials) {
    this.config = config || {};
    this.credentials = credentials || {};
    this.baseUrl = this.config.baseUrl || 'https://api.example.com';
    this.apiKeyHeader = this.credentials.apiKeyHeader || 'X-API-Key';
  }

  /**
   * Initialize the connector
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    logger.info('Initializing APIs, iPaaS & Developer Tools connector');
    
    if (!this.credentials.apiKey) {
      throw new Error('API Key is required');
    }
  }

  /**
   * Get authentication headers
   * 
   * @returns {Object} - Authentication headers
   */
  getAuthHeaders() {
    const headers = {};
    headers[this.apiKeyHeader] = this.credentials.apiKey;
    return headers;
  }

  /**
   * List APIs
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of APIs
   */
  async listApis(params = {}) {
    logger.info('Listing APIs', { params });
    
    try {
      const response = await axios.get(`${this.baseUrl}/apis`, {
        params,
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error listing APIs', { error: error.message });
      throw new Error(`Error listing APIs: ${error.message}`);
    }
  }

  /**
   * Get an API
   * 
   * @param {string} apiId - API ID
   * @returns {Promise<Object>} - API details
   */
  async getApi(apiId) {
    logger.info('Getting API', { apiId });
    
    if (!apiId) {
      throw new Error('API ID is required');
    }
    
    try {
      const response = await axios.get(`${this.baseUrl}/apis/${apiId}`, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting API', { apiId, error: error.message });
      throw new Error(`Error getting API: ${error.message}`);
    }
  }

  /**
   * Create an API
   * 
   * @param {Object} apiData - API data
   * @returns {Promise<Object>} - Created API
   */
  async createApi(apiData) {
    logger.info('Creating API');
    
    if (!apiData) {
      throw new Error('API data is required');
    }
    
    // Validate required fields
    const requiredFields = ['name', 'type', 'baseUrl'];
    for (const field of requiredFields) {
      if (!apiData[field]) {
        throw new Error(`${field} is required`);
      }
    }
    
    try {
      const response = await axios.post(`${this.baseUrl}/apis`, apiData, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error creating API', { error: error.message });
      throw new Error(`Error creating API: ${error.message}`);
    }
  }

  /**
   * Update an API
   * 
   * @param {string} apiId - API ID
   * @param {Object} apiData - API data
   * @returns {Promise<Object>} - Updated API
   */
  async updateApi(apiId, apiData) {
    logger.info('Updating API', { apiId });
    
    if (!apiId) {
      throw new Error('API ID is required');
    }
    
    if (!apiData) {
      throw new Error('API data is required');
    }
    
    try {
      const response = await axios.put(`${this.baseUrl}/apis/${apiId}`, apiData, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error updating API', { apiId, error: error.message });
      throw new Error(`Error updating API: ${error.message}`);
    }
  }

  /**
   * Delete an API
   * 
   * @param {string} apiId - API ID
   * @returns {Promise<void>}
   */
  async deleteApi(apiId) {
    logger.info('Deleting API', { apiId });
    
    if (!apiId) {
      throw new Error('API ID is required');
    }
    
    try {
      await axios.delete(`${this.baseUrl}/apis/${apiId}`, {
        headers: {
          ...this.getAuthHeaders(),
          'Accept': 'application/json'
        }
      });
      
      logger.info('API deleted successfully', { apiId });
    } catch (error) {
      logger.error('Error deleting API', { apiId, error: error.message });
      throw new Error(`Error deleting API: ${error.message}`);
    }
  }

  /**
   * List integrations
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of integrations
   */
  async listIntegrations(params = {}) {
    logger.info('Listing integrations', { params });
    
    try {
      const response = await axios.get(`${this.baseUrl}/integrations`, {
        params,
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error listing integrations', { error: error.message });
      throw new Error(`Error listing integrations: ${error.message}`);
    }
  }

  /**
   * Get an integration
   * 
   * @param {string} integrationId - Integration ID
   * @returns {Promise<Object>} - Integration details
   */
  async getIntegration(integrationId) {
    logger.info('Getting integration', { integrationId });
    
    if (!integrationId) {
      throw new Error('Integration ID is required');
    }
    
    try {
      const response = await axios.get(`${this.baseUrl}/integrations/${integrationId}`, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting integration', { integrationId, error: error.message });
      throw new Error(`Error getting integration: ${error.message}`);
    }
  }

  /**
   * Create an integration
   * 
   * @param {Object} integrationData - Integration data
   * @returns {Promise<Object>} - Created integration
   */
  async createIntegration(integrationData) {
    logger.info('Creating integration');
    
    if (!integrationData) {
      throw new Error('Integration data is required');
    }
    
    // Validate required fields
    const requiredFields = ['name', 'sourceSystem', 'targetSystem'];
    for (const field of requiredFields) {
      if (!integrationData[field]) {
        throw new Error(`${field} is required`);
      }
    }
    
    try {
      const response = await axios.post(`${this.baseUrl}/integrations`, integrationData, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error creating integration', { error: error.message });
      throw new Error(`Error creating integration: ${error.message}`);
    }
  }

  /**
   * Update an integration
   * 
   * @param {string} integrationId - Integration ID
   * @param {Object} integrationData - Integration data
   * @returns {Promise<Object>} - Updated integration
   */
  async updateIntegration(integrationId, integrationData) {
    logger.info('Updating integration', { integrationId });
    
    if (!integrationId) {
      throw new Error('Integration ID is required');
    }
    
    if (!integrationData) {
      throw new Error('Integration data is required');
    }
    
    try {
      const response = await axios.put(`${this.baseUrl}/integrations/${integrationId}`, integrationData, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error updating integration', { integrationId, error: error.message });
      throw new Error(`Error updating integration: ${error.message}`);
    }
  }

  /**
   * Delete an integration
   * 
   * @param {string} integrationId - Integration ID
   * @returns {Promise<void>}
   */
  async deleteIntegration(integrationId) {
    logger.info('Deleting integration', { integrationId });
    
    if (!integrationId) {
      throw new Error('Integration ID is required');
    }
    
    try {
      await axios.delete(`${this.baseUrl}/integrations/${integrationId}`, {
        headers: {
          ...this.getAuthHeaders(),
          'Accept': 'application/json'
        }
      });
      
      logger.info('Integration deleted successfully', { integrationId });
    } catch (error) {
      logger.error('Error deleting integration', { integrationId, error: error.message });
      throw new Error(`Error deleting integration: ${error.message}`);
    }
  }

  /**
   * Execute an integration
   * 
   * @param {string} integrationId - Integration ID
   * @param {Object} options - Execution options
   * @returns {Promise<Object>} - Execution result
   */
  async executeIntegration(integrationId, options = {}) {
    logger.info('Executing integration', { integrationId, options });
    
    if (!integrationId) {
      throw new Error('Integration ID is required');
    }
    
    try {
      const response = await axios.post(`${this.baseUrl}/integrations/${integrationId}/execute`, options, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error executing integration', { integrationId, error: error.message });
      throw new Error(`Error executing integration: ${error.message}`);
    }
  }

  /**
   * List developer tools
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of developer tools
   */
  async listDeveloperTools(params = {}) {
    logger.info('Listing developer tools', { params });
    
    try {
      const response = await axios.get(`${this.baseUrl}/developer-tools`, {
        params,
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error listing developer tools', { error: error.message });
      throw new Error(`Error listing developer tools: ${error.message}`);
    }
  }

  /**
   * Get a developer tool
   * 
   * @param {string} toolId - Tool ID
   * @returns {Promise<Object>} - Tool details
   */
  async getDeveloperTool(toolId) {
    logger.info('Getting developer tool', { toolId });
    
    if (!toolId) {
      throw new Error('Tool ID is required');
    }
    
    try {
      const response = await axios.get(`${this.baseUrl}/developer-tools/${toolId}`, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting developer tool', { toolId, error: error.message });
      throw new Error(`Error getting developer tool: ${error.message}`);
    }
  }
}

module.exports = ApisIpaasDeveloperToolsConnector;
