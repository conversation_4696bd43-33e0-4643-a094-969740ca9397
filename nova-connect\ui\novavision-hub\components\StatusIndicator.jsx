/**
 * StatusIndicator Component
 * 
 * A reusable status indicator component for displaying status information with visual cues.
 */

import React from 'react';
import PropTypes from 'prop-types';

/**
 * StatusIndicator component
 * 
 * @param {Object} props - Component props
 * @param {string} props.status - Status value (success, warning, error, info, pending, neutral)
 * @param {string} [props.label] - Status label
 * @param {string} [props.customColor] - Custom color for the status indicator
 * @param {boolean} [props.pulse=false] - Whether the status indicator should pulse
 * @param {boolean} [props.showDot=true] - Whether to show the status dot
 * @param {string} [props.size='md'] - Size of the status indicator (sm, md, lg)
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} StatusIndicator component
 */
const StatusIndicator = ({
  status,
  label,
  customColor,
  pulse = false,
  showDot = true,
  size = 'md',
  className = '',
  style = {}
}) => {
  // Determine color based on status
  const getStatusColor = () => {
    if (customColor) return customColor;
    
    const statusColors = {
      success: 'bg-green-500',
      warning: 'bg-yellow-500',
      error: 'bg-red-500',
      info: 'bg-blue-500',
      pending: 'bg-purple-500',
      neutral: 'bg-gray-500'
    };
    
    return statusColors[status] || 'bg-gray-500';
  };
  
  // Determine text color based on status
  const getTextColor = () => {
    if (customColor) return customColor.replace('bg-', 'text-');
    
    const textColors = {
      success: 'text-green-700',
      warning: 'text-yellow-700',
      error: 'text-red-700',
      info: 'text-blue-700',
      pending: 'text-purple-700',
      neutral: 'text-gray-700'
    };
    
    return textColors[status] || 'text-gray-700';
  };
  
  // Determine dot size based on size prop
  const getDotSize = () => {
    const dotSizes = {
      sm: 'w-2 h-2',
      md: 'w-3 h-3',
      lg: 'w-4 h-4'
    };
    
    return dotSizes[size] || 'w-3 h-3';
  };
  
  // Determine text size based on size prop
  const getTextSize = () => {
    const textSizes = {
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base'
    };
    
    return textSizes[size] || 'text-sm';
  };
  
  // Determine pulse animation class
  const getPulseClass = () => {
    if (!pulse) return '';
    
    return 'animate-pulse';
  };
  
  return (
    <div
      className={`inline-flex items-center ${className}`}
      style={style}
      data-testid="status-indicator"
    >
      {showDot && (
        <span
          className={`${getDotSize()} ${getStatusColor()} rounded-full ${getPulseClass()}`}
          data-testid="status-dot"
        ></span>
      )}
      {label && (
        <span
          className={`${getTextSize()} ${getTextColor()} font-medium ${showDot ? 'ml-2' : ''}`}
          data-testid="status-label"
        >
          {label}
        </span>
      )}
    </div>
  );
};

StatusIndicator.propTypes = {
  status: PropTypes.oneOf(['success', 'warning', 'error', 'info', 'pending', 'neutral']).isRequired,
  label: PropTypes.string,
  customColor: PropTypes.string,
  pulse: PropTypes.bool,
  showDot: PropTypes.bool,
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  className: PropTypes.string,
  style: PropTypes.object
};

export default StatusIndicator;
