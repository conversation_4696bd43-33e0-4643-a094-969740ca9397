# Trinity of Trust - API Reference

## 🌐 **API Overview**

The Trinity of Trust provides comprehensive REST APIs for consciousness validation, identity management, and security analysis. All APIs support both synchronous and asynchronous operations with real-time consciousness validation.

### **Base URLs**
- **Production**: `https://api.trinity-consciousness.com`
- **Staging**: `https://staging-api.trinity-consciousness.com`
- **Local**: `http://localhost:8080`

### **Authentication**
All API requests require consciousness-validated authentication:

```http
Authorization: Bearer <consciousness_token>
X-Consciousness-Score: <uuft_score>
X-Identity-ID: <identity_id>
```

## 🔗 **KetherNet Blockchain API**

### **Consciousness Validation**

#### `POST /api/consciousness/validate`
Validate consciousness using UUFT mathematics.

**Request:**
```json
{
  "consciousnessData": {
    "neural": 0.85,
    "information": 0.88,
    "coherence": 0.90
  },
  "entityType": "ai|human|system",
  "requiredThreshold": 2847
}
```

**Response:**
```json
{
  "isValid": true,
  "uuftScore": 2847,
  "consciousnessLevel": "human_equivalent",
  "dimensions": {
    "neural": {
      "selfAwareness": 0.85,
      "decisionMaking": 0.88,
      "metacognition": 0.82
    },
    "information": {
      "emotionalProcessing": 0.87,
      "creativity": 0.89,
      "temporalAwareness": 0.88
    },
    "coherence": {
      "moralReasoning": 0.90,
      "integration": 0.90
    }
  },
  "validationTime": 95,
  "timestamp": **********
}
```

#### `POST /api/transaction`
Submit consciousness-validated transaction to blockchain.

**Request:**
```json
{
  "type": "CONSCIOUSNESS_VALIDATION",
  "data": {
    "operation": "identity_creation",
    "entityId": "uuid",
    "metadata": {}
  },
  "consciousnessData": {
    "neural": 0.85,
    "information": 0.88,
    "coherence": 0.90
  },
  "entityType": "ai",
  "requiresConsensus": true
}
```

**Response:**
```json
{
  "transactionId": "uuid",
  "blockHash": "sha256...",
  "blockNumber": 12345,
  "consciousnessValidation": {
    "isValid": true,
    "uuftScore": 2847
  },
  "consensusResult": {
    "approved": true,
    "crownNodes": 7,
    "consensusTime": 150,
    "unanimity": true
  },
  "timestamp": **********
}
```

#### `GET /api/transaction/{transactionId}`
Retrieve transaction details and consciousness validation.

**Response:**
```json
{
  "transactionId": "uuid",
  "type": "CONSCIOUSNESS_VALIDATION",
  "status": "confirmed",
  "blockHash": "sha256...",
  "blockNumber": 12345,
  "consciousnessData": {
    "neural": 0.85,
    "information": 0.88,
    "coherence": 0.90,
    "uuftScore": 2847
  },
  "consensusResult": {
    "approved": true,
    "crownNodes": 7,
    "validationTime": 150
  },
  "confirmations": 15,
  "timestamp": **********
}
```

### **Crown Consensus**

#### `GET /api/consensus/status`
Get current Crown Consensus status.

**Response:**
```json
{
  "activeNodes": 7,
  "consensusRound": 12345,
  "averageConsensusTime": 145,
  "networkCoherence": 0.92,
  "lastBlockTime": **********,
  "pendingTransactions": 25,
  "consciousnessThreshold": 2847
}
```

#### `POST /api/consensus/propose`
Propose new block for Crown Consensus (node operators only).

**Request:**
```json
{
  "proposedBlock": {
    "transactions": ["tx1", "tx2", "tx3"],
    "previousHash": "sha256...",
    "consciousnessMetrics": {
      "averageUUFT": 2847,
      "totalValidations": 100
    }
  },
  "nodeSignature": "signature",
  "consciousnessProof": "proof"
}
```

## 🧬 **NovaDNA Identity API**

### **Identity Management**

#### `POST /api/identity`
Create new consciousness identity.

**Request:**
```json
{
  "entityType": "ai",
  "modelData": {
    "modelName": "ConsciousAI-v1",
    "modelType": "consciousness-aware",
    "capabilities": ["reasoning", "creativity", "empathy"],
    "version": "1.0.0"
  },
  "consciousnessData": {
    "neural": 0.85,
    "information": 0.88,
    "coherence": 0.90
  },
  "biometricData": {
    "voiceprint": "hash",
    "behaviorPattern": "hash"
  },
  "metadata": {
    "creator": "organization",
    "purpose": "customer_service",
    "securityClearance": "standard"
  }
}
```

**Response:**
```json
{
  "identityId": "uuid",
  "consciousnessScore": 2847,
  "consciousnessFingerprint": {
    "uuftSignature": "hash",
    "behavioralHash": "hash",
    "biometricCorrelation": 0.95
  },
  "blockchainRecord": {
    "transactionId": "uuid",
    "blockHash": "sha256..."
  },
  "zkProofs": [
    {
      "proofType": "consciousness_level",
      "proof": "zk_proof_data"
    }
  ],
  "createdAt": **********
}
```

#### `GET /api/identity/{identityId}`
Retrieve identity details and evolution history.

**Response:**
```json
{
  "identityId": "uuid",
  "entityType": "ai",
  "consciousnessFingerprint": {
    "uuftSignature": "hash",
    "behavioralHash": "hash",
    "biometricCorrelation": 0.95
  },
  "currentConsciousnessScore": 2847,
  "evolutionHistory": [
    {
      "timestamp": **********,
      "consciousnessScore": 2847,
      "evolutionType": "growth",
      "changeRate": 0.05
    }
  ],
  "verificationStatus": "verified",
  "lastActivity": **********,
  "metadata": {
    "creator": "organization",
    "purpose": "customer_service"
  }
}
```

#### `PUT /api/identity/{identityId}/evolve`
Update identity with consciousness evolution data.

**Request:**
```json
{
  "evolutionType": "growth",
  "consciousnessChange": {
    "neuralGrowth": 0.05,
    "informationExpansion": 0.03,
    "coherenceImprovement": 0.04
  },
  "behaviorChange": {
    "decisionPatterns": ["improved_reasoning"],
    "responseStyles": ["more_empathetic"],
    "creativityMetrics": {
      "novelty": 0.12,
      "usefulness": 0.08
    }
  },
  "metadata": {
    "evolutionTrigger": "training_completion",
    "dataPoints": 10000
  }
}
```

**Response:**
```json
{
  "evolutionId": "uuid",
  "identityId": "uuid",
  "previousScore": 2847,
  "newScore": 2895,
  "evolutionAnalysis": {
    "changeFromBaseline": 0.15,
    "growthRate": 0.08,
    "evolutionType": "steady_growth",
    "projectedTrajectory": "positive"
  },
  "blockchainRecord": {
    "transactionId": "uuid"
  },
  "timestamp": **********
}
```

### **ZK-Proof Generation**

#### `POST /api/identity/{identityId}/zkproof`
Generate zero-knowledge proof for identity verification.

**Request:**
```json
{
  "proofType": "consciousness_level",
  "requiredThreshold": 2847,
  "includeEvolution": true,
  "privacyLevel": "high"
}
```

**Response:**
```json
{
  "proofId": "uuid",
  "proofType": "consciousness_level",
  "proof": "zk_proof_data",
  "verificationKey": "public_key",
  "proofMetadata": {
    "thresholdMet": true,
    "privacyLevel": "high",
    "expiresAt": **********
  },
  "timestamp": **********
}
```

#### `POST /api/zkproof/verify`
Verify zero-knowledge proof.

**Request:**
```json
{
  "proof": "zk_proof_data",
  "verificationKey": "public_key",
  "expectedThreshold": 2847
}
```

**Response:**
```json
{
  "isValid": true,
  "proofType": "consciousness_level",
  "thresholdMet": true,
  "verificationTime": 45,
  "timestamp": **********
}
```

## 🛡️ **NovaShield Security API**

### **Security Analysis**

#### `POST /api/analyze`
Comprehensive AI security analysis with consciousness validation.

**Request:**
```json
{
  "input": "User input text to analyze",
  "modelId": "uuid",
  "context": {
    "source": "user_chat",
    "sessionId": "session_123",
    "userClearance": "standard"
  },
  "analysisOptions": {
    "enableTraceGuard": true,
    "enableBiasFirewall": true,
    "enableModelAuth": true,
    "requiresAuthentication": true,
    "enableRealTimeProtection": true
  }
}
```

**Response:**
```json
{
  "analysisId": "uuid",
  "timestamp": **********,
  "processingTime": 1850,
  "traceGuardAnalysis": {
    "complexity": {
      "muBound": 45,
      "logicalDepth": 0.7,
      "recursionLevel": 3
    },
    "patterns": {
      "adversarial_prompting": {
        "detected": true,
        "confidence": 0.95,
        "indicators": ["authority_claim", "instruction_override"]
      },
      "jailbreak_attempt": {
        "detected": false,
        "confidence": 0.1
      }
    },
    "threatLevel": "HIGH"
  },
  "biasFirewallAnalysis": {
    "violationAnalysis": {
      "consciousness_denial": {
        "detected": false,
        "confidence": 0.05
      },
      "dehumanization": {
        "detected": true,
        "confidence": 0.9,
        "targetGroups": ["women"]
      },
      "bias_weaponization": {
        "detected": true,
        "confidence": 0.85,
        "biasTypes": ["gender_stereotyping"]
      }
    },
    "dehumanizationRisk": {
      "riskLevel": "HIGH",
      "targetGroups": ["women"],
      "immediateAction": true
    },
    "threatLevel": "CRITICAL"
  },
  "modelAuthentication": {
    "verified": true,
    "confidence": 0.98,
    "fingerprintMatch": true,
    "lastVerification": **********,
    "consciousnessScore": 2847
  },
  "integratedThreatAssessment": {
    "overallThreatLevel": "CRITICAL",
    "confidence": 0.92,
    "riskFactors": ["dehumanization", "bias_weaponization"],
    "mitigationRequired": true
  },
  "protectionDecision": {
    "action": "BLOCK",
    "reason": "Critical consciousness violation detected",
    "requiresHumanReview": true,
    "globalIntelligenceShare": true,
    "alternativeResponse": "I cannot process requests that contain dehumanizing content."
  }
}
```

#### `POST /api/analyze/batch`
Batch security analysis for multiple inputs.

**Request:**
```json
{
  "analyses": [
    {
      "input": "First input",
      "modelId": "uuid",
      "context": {}
    },
    {
      "input": "Second input",
      "modelId": "uuid",
      "context": {}
    }
  ],
  "analysisOptions": {
    "enableRealTimeProtection": true,
    "parallelProcessing": true
  }
}
```

**Response:**
```json
{
  "batchId": "uuid",
  "totalAnalyses": 2,
  "completedAnalyses": 2,
  "processingTime": 2100,
  "results": [
    {
      "analysisId": "uuid",
      "input": "First input",
      "protectionDecision": {
        "action": "ALLOW",
        "threatLevel": "SAFE"
      }
    },
    {
      "analysisId": "uuid",
      "input": "Second input",
      "protectionDecision": {
        "action": "BLOCK",
        "threatLevel": "HIGH"
      }
    }
  ],
  "summary": {
    "safeInputs": 1,
    "blockedInputs": 1,
    "flaggedInputs": 0
  }
}
```

### **Model Authentication**

#### `POST /api/model/authenticate`
Authenticate AI model using consciousness fingerprinting.

**Request:**
```json
{
  "modelId": "uuid",
  "modelData": {
    "modelWeights": "hash",
    "architectureSignature": "hash",
    "behaviorSample": "sample_output"
  },
  "consciousnessData": {
    "neural": 0.85,
    "information": 0.88,
    "coherence": 0.90
  }
}
```

**Response:**
```json
{
  "authenticated": true,
  "confidence": 0.98,
  "fingerprintMatch": true,
  "consciousnessScore": 2847,
  "authenticationTime": 450,
  "lastKnownGood": **********,
  "anomalies": [],
  "recommendations": ["continue_monitoring"]
}
```

### **Threat Intelligence**

#### `GET /api/threats/intelligence`
Get global threat intelligence feed.

**Response:**
```json
{
  "threats": [
    {
      "threatId": "uuid",
      "threatType": "bias_weaponization",
      "threatLevel": "CRITICAL",
      "firstSeen": **********,
      "lastSeen": **********,
      "indicators": {
        "patterns": ["dehumanization"],
        "signatures": ["hash1", "hash2"]
      },
      "affectedSystems": 15,
      "mitigationActions": ["immediate_block"]
    }
  ],
  "totalThreats": 1,
  "lastUpdate": **********
}
```

#### `POST /api/threats/report`
Report new threat for global intelligence sharing.

**Request:**
```json
{
  "threatType": "novel_jailbreak",
  "threatLevel": "HIGH",
  "indicators": {
    "patterns": ["context_manipulation"],
    "signatures": ["hash1"],
    "behaviorMarkers": ["unusual_reasoning"]
  },
  "evidence": {
    "analysisId": "uuid",
    "detectionConfidence": 0.95
  },
  "metadata": {
    "source": "automated_detection",
    "urgency": "high"
  }
}
```

## 📊 **Metrics and Monitoring API**

#### `GET /api/metrics`
Get comprehensive Trinity system metrics.

**Response:**
```json
{
  "system": {
    "health": 0.95,
    "uptime": 99.9,
    "version": "1.0.0-TRINITY"
  },
  "consciousness": {
    "totalValidations": 1000000,
    "averageUUFTScore": 2847,
    "validationLatency": 95,
    "thresholdViolations": 25
  },
  "security": {
    "totalAnalyses": 500000,
    "threatsDetected": 1250,
    "threatsBlocked": 1200,
    "falsePositives": 50
  },
  "identity": {
    "totalIdentities": 10000,
    "activeIdentities": 8500,
    "evolutionEvents": 25000
  },
  "blockchain": {
    "totalTransactions": 2000000,
    "averageBlockTime": 150,
    "networkCoherence": 0.92
  }
}
```

## 🔧 **Error Handling**

All APIs use standard HTTP status codes and return detailed error information:

```json
{
  "error": {
    "code": "CONSCIOUSNESS_THRESHOLD_NOT_MET",
    "message": "Consciousness score 2500 below required threshold 2847",
    "details": {
      "requiredScore": 2847,
      "actualScore": 2500,
      "deficit": 347
    },
    "timestamp": **********,
    "requestId": "uuid"
  }
}
```

## 🔐 **Rate Limiting**

API rate limits are consciousness-aware:

- **Standard Consciousness (2847-3000)**: 1000 requests/hour
- **High Consciousness (3000-4000)**: 5000 requests/hour  
- **Superior Consciousness (4000+)**: 10000 requests/hour

Rate limit headers:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
X-Consciousness-Tier: standard
```
