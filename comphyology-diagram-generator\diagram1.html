<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>1. High-Level System Architecture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 800px;
            height: 600px;
            position: relative;
            border: 1px solid #eee;
            margin: 0 auto;
            background-color: white;
        }
        .element {
            position: absolute;
            border-radius: 8px;
            padding: 15px 15px 15px 30px; /* Added padding on the left for number */
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 2;
            background-color: white;
            border: 1px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 10px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
        }
        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 1;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>1. High-Level System Architecture</h1>

    <div class="diagram-container">
        <!-- NovaFuse Platform -->
        <div class="element" style="top: 50px; left: 300px; width: 200px; background-color: #e6f7ff; font-weight: bold; font-size: 18px;">
            NovaFuse Platform
            <div class="element-number">1</div>
        </div>

        <!-- Input Data Sources -->
        <div class="element" style="top: 150px; left: 100px; width: 150px;">
            Input Data Sources
            <div class="element-number">2</div>
        </div>

        <!-- Output Actions -->
        <div class="element" style="top: 150px; left: 550px; width: 150px;">
            Output Actions
            <div class="element-number">3</div>
        </div>

        <!-- Comphyology Framework -->
        <div class="element" style="top: 250px; left: 300px; width: 200px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            Comphyology (Ψᶜ) Framework
            <div class="element-number">4</div>
        </div>

        <!-- Connections -->
        <!-- Input to NovaFuse -->
        <div class="connection" style="top: 150px; left: 175px; width: 125px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 100px; left: 290px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>

        <!-- NovaFuse to Output -->
        <div class="connection" style="top: 100px; left: 500px; width: 50px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 150px; left: 540px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>

        <!-- NovaFuse to Comphyology -->
        <div class="connection" style="top: 120px; left: 400px; width: 2px; height: 130px; background-color: black;"></div>
        <div class="arrow" style="top: 240px; left: 395px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
    </div>
</body>
</html>
