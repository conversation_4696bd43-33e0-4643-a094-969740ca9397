/**
 * NovaAssure Dashboard
 * 
 * This dashboard provides an overview of the organization's control testing status,
 * test results, and upcoming tests.
 */

import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { useAuth } from '../../contexts/AuthContext';
import ProtectedRoute from '../../components/ProtectedRoute';
import useNovaAssureApi from '../../hooks/useNovaAssureApi';
import useFetch from '../../hooks/useFetch';
import LoadingSpinner from '../../components/ui/loading-spinner';
import ErrorDisplay from '../../components/ui/error-display';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { 
  ArrowRight, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Clock, 
  Calendar, 
  FileText, 
  Shield, 
  LogOut,
  BarChart,
  PieChart,
  Activity
} from 'lucide-react';
import { TestResultsChart } from '../../components/novaassure/TestResultsChart';
import { FrameworkCoverageChart } from '../../components/novaassure/FrameworkCoverageChart';
import { TestStatusCards } from '../../components/novaassure/TestStatusCards';
import { UpcomingTestsList } from '../../components/novaassure/UpcomingTestsList';
import { RecentTestRunsList } from '../../components/novaassure/RecentTestRunsList';

export default function NovaAssureDashboard() {
  return (
    <ProtectedRoute>
      <NovaAssureDashboardContent />
    </ProtectedRoute>
  );
}

function NovaAssureDashboardContent() {
  const router = useRouter();
  const { logout } = useAuth();
  const { api, callApi } = useNovaAssureApi();
  const [error, setError] = useState<Error | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  
  // Fetch testing metrics
  const { 
    data: testingMetrics = {
      testsPassed: 0,
      testsFailed: 0,
      testsInconclusive: 0,
      testsPending: 0,
      controlsCovered: 0,
      controlsUncovered: 0,
      automationRate: 0,
      evidenceCount: 0
    }, 
    loading: metricsLoading,
    error: metricsError
  } = useFetch(
    async () => await callApi(() => api.getTestingMetrics()),
    { dependencies: [api] }
  );
  
  // Fetch test results by framework
  const { 
    data: frameworkResults = [], 
    loading: frameworkResultsLoading,
    error: frameworkResultsError
  } = useFetch(
    async () => await callApi(() => api.getTestResultsByFramework()),
    { dependencies: [api] }
  );
  
  // Fetch upcoming tests
  const { 
    data: upcomingTests = [], 
    loading: upcomingTestsLoading,
    error: upcomingTestsError
  } = useFetch(
    async () => await callApi(() => api.getUpcomingTests(7)),
    { dependencies: [api] }
  );
  
  // Fetch latest test runs
  const { 
    data: latestTestRuns = [], 
    loading: latestTestRunsLoading,
    error: latestTestRunsError
  } = useFetch(
    async () => await callApi(() => api.getLatestTestRuns(5)),
    { dependencies: [api] }
  );
  
  // Fetch test result trend
  const { 
    data: testResultTrend = [], 
    loading: trendLoading,
    error: trendError
  } = useFetch(
    async () => await callApi(() => api.getTestResultTrend('month')),
    { dependencies: [api] }
  );
  
  // Combine all loading states
  const loading = metricsLoading || frameworkResultsLoading || upcomingTestsLoading || latestTestRunsLoading || trendLoading;
  
  // Combine all errors
  React.useEffect(() => {
    const firstError = metricsError || frameworkResultsError || upcomingTestsError || latestTestRunsError || trendError;
    if (firstError) {
      setError(firstError);
    } else {
      setError(null);
    }
  }, [metricsError, frameworkResultsError, upcomingTestsError, latestTestRunsError, trendError]);
  
  const handleLogout = () => {
    logout();
    router.push('/login');
  };
  
  const handleDismissError = () => {
    setError(null);
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">NovaAssure Dashboard</h1>
        <Button variant="outline" size="sm" onClick={handleLogout} className="flex items-center">
          <LogOut className="h-4 w-4 mr-2" />
          Logout
        </Button>
      </div>
      
      {error && (
        <div className="mb-6">
          <ErrorDisplay 
            message="Error loading NovaAssure data" 
            details={error.message}
            severity="error"
            onDismiss={handleDismissError}
          />
        </div>
      )}
      
      {loading && <LoadingSpinner size="large" text="Loading NovaAssure data..." fullPage />}
      
      <div className="mb-6">
        <nav className="flex flex-wrap gap-2">
          <Link 
            href="/dashboard" 
            className="px-4 py-2 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200"
          >
            Main Dashboard
          </Link>
          <Link 
            href="/novaassure" 
            className="px-4 py-2 rounded-md text-sm font-medium bg-blue-600 text-white"
          >
            NovaAssure
          </Link>
          <Link 
            href="/frameworks" 
            className="px-4 py-2 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200"
          >
            Frameworks
          </Link>
          <Link 
            href="/regulations" 
            className="px-4 py-2 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200"
          >
            Regulations
          </Link>
        </nav>
      </div>
      
      <div className="mb-6">
        <TestStatusCards 
          passed={testingMetrics.testsPassed}
          failed={testingMetrics.testsFailed}
          pending={testingMetrics.testsPending}
          inconclusive={testingMetrics.testsInconclusive}
        />
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="tests">Tests</TabsTrigger>
          <TabsTrigger value="frameworks">Framework Coverage</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center">
                  <PieChart className="h-5 w-5 mr-2 text-blue-500" />
                  Test Results
                </CardTitle>
              </CardHeader>
              <CardContent>
                {metricsLoading ? (
                  <div className="h-64 flex items-center justify-center">
                    <LoadingSpinner size="medium" text="Loading test results..." />
                  </div>
                ) : (
                  <TestResultsChart 
                    passed={testingMetrics.testsPassed}
                    failed={testingMetrics.testsFailed}
                    pending={testingMetrics.testsPending}
                    inconclusive={testingMetrics.testsInconclusive}
                  />
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center">
                  <Shield className="h-5 w-5 mr-2 text-purple-500" />
                  Control Coverage
                </CardTitle>
              </CardHeader>
              <CardContent>
                {metricsLoading ? (
                  <div className="h-64 flex items-center justify-center">
                    <LoadingSpinner size="medium" text="Loading control coverage..." />
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="text-sm text-gray-500">Controls Covered</div>
                        <div className="text-2xl font-bold">{testingMetrics.controlsCovered}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-500">Controls Uncovered</div>
                        <div className="text-2xl font-bold">{testingMetrics.controlsUncovered}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-500">Automation Rate</div>
                        <div className="text-2xl font-bold">{testingMetrics.automationRate}%</div>
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>Control Coverage</span>
                        <span>
                          {Math.round(testingMetrics.controlsCovered / (testingMetrics.controlsCovered + testingMetrics.controlsUncovered) * 100) || 0}%
                        </span>
                      </div>
                      <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-purple-600 rounded-full" 
                          style={{ 
                            width: `${Math.round(testingMetrics.controlsCovered / (testingMetrics.controlsCovered + testingMetrics.controlsUncovered) * 100) || 0}%` 
                          }}
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>Automation Rate</span>
                        <span>{testingMetrics.automationRate}%</span>
                      </div>
                      <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-blue-600 rounded-full" 
                          style={{ width: `${testingMetrics.automationRate}%` }}
                        />
                      </div>
                    </div>
                    
                    <div className="text-center mt-4">
                      <div className="text-sm text-gray-500">Total Evidence Collected</div>
                      <div className="text-3xl font-bold">{testingMetrics.evidenceCount}</div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2 text-yellow-500" />
                  Upcoming Tests
                </CardTitle>
              </CardHeader>
              <CardContent>
                {upcomingTestsLoading ? (
                  <div className="h-64 flex items-center justify-center">
                    <LoadingSpinner size="medium" text="Loading upcoming tests..." />
                  </div>
                ) : (
                  <UpcomingTestsList tests={upcomingTests} />
                )}
                <div className="mt-4 text-right">
                  <Link href="/novaassure/schedule">
                    <Button variant="link" size="sm" className="text-blue-600">
                      View Test Schedule
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center">
                  <Activity className="h-5 w-5 mr-2 text-green-500" />
                  Recent Test Runs
                </CardTitle>
              </CardHeader>
              <CardContent>
                {latestTestRunsLoading ? (
                  <div className="h-64 flex items-center justify-center">
                    <LoadingSpinner size="medium" text="Loading recent test runs..." />
                  </div>
                ) : (
                  <RecentTestRunsList testRuns={latestTestRuns} />
                )}
                <div className="mt-4 text-right">
                  <Link href="/novaassure/test-runs">
                    <Button variant="link" size="sm" className="text-blue-600">
                      View All Test Runs
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="tests">
          <Card>
            <CardHeader>
              <CardTitle>Control Tests</CardTitle>
              <CardDescription>Manage and execute control tests</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between mb-6">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search tests..."
                    className="pl-9 pr-4 py-2 border rounded-md w-full md:w-64"
                  />
                </div>
                <Link href="/novaassure/tests/create">
                  <Button size="sm">
                    Create Test
                  </Button>
                </Link>
              </div>
              
              <div className="space-y-4">
                <div className="flex justify-between p-4 border rounded-md bg-gray-50">
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                    <div>
                      <div className="font-medium">Access Control Review</div>
                      <div className="text-sm text-gray-500">SOC 2 - CC6.1</div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 mr-2">Automated</span>
                    <Link href="/novaassure/tests/test1">
                      <Button variant="outline" size="sm">View</Button>
                    </Link>
                  </div>
                </div>
                
                <div className="flex justify-between p-4 border rounded-md bg-gray-50">
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                    <div>
                      <div className="font-medium">Vulnerability Scanning</div>
                      <div className="text-sm text-gray-500">SOC 2 - CC7.1</div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <span className="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800 mr-2">Hybrid</span>
                    <Link href="/novaassure/tests/test2">
                      <Button variant="outline" size="sm">View</Button>
                    </Link>
                  </div>
                </div>
                
                <div className="flex justify-between p-4 border rounded-md bg-gray-50">
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                    <div>
                      <div className="font-medium">Backup Verification</div>
                      <div className="text-sm text-gray-500">SOC 2 - A1.2</div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 mr-2">Manual</span>
                    <Link href="/novaassure/tests/test3">
                      <Button variant="outline" size="sm">View</Button>
                    </Link>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 text-center">
                <Link href="/novaassure/tests">
                  <Button>View All Tests</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="frameworks">
          <Card>
            <CardHeader>
              <CardTitle>Framework Coverage</CardTitle>
              <CardDescription>Test coverage across compliance frameworks</CardDescription>
            </CardHeader>
            <CardContent>
              {frameworkResultsLoading ? (
                <div className="h-64 flex items-center justify-center">
                  <LoadingSpinner size="medium" text="Loading framework coverage..." />
                </div>
              ) : (
                <FrameworkCoverageChart data={frameworkResults} />
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="trends">
          <Card>
            <CardHeader>
              <CardTitle>Test Result Trends</CardTitle>
              <CardDescription>Historical test results over time</CardDescription>
            </CardHeader>
            <CardContent>
              {trendLoading ? (
                <div className="h-64 flex items-center justify-center">
                  <LoadingSpinner size="medium" text="Loading trend data..." />
                </div>
              ) : (
                <div className="h-80">
                  <div className="text-center text-gray-500">
                    Test result trend visualization would be displayed here.
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
