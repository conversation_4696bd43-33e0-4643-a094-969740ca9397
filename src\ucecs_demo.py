"""
Demo script for the Universal Compliance Evidence Collection System (UCECS).

This script demonstrates how to use the UCECS to collect, validate, and store
compliance evidence.
"""

import os
import sys
import json
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the UCECS
from ucecs import EvidenceManager
from ucecs.utils.evidence_utils import get_evidence_summary, filter_sensitive_data

def custom_evidence_handler(evidence: Dict[str, Any]) -> None:
    """
    Custom handler for evidence events.
    
    Args:
        evidence: The evidence
    """
    logger.info(f"Custom evidence handler: {evidence.get('id')}")
    logger.info(f"Evidence type: {evidence.get('type')}")
    logger.info(f"Evidence status: {evidence.get('status')}")

def main():
    """Run the UCECS demo."""
    logger.info("Starting UCECS demo")
    
    # Initialize the Evidence Manager
    manager = EvidenceManager()
    
    # Register a custom evidence handler for file evidence
    manager.register_evidence_handler('file', custom_evidence_handler)
    
    # Create output directory if it doesn't exist
    output_dir = os.path.join(os.path.dirname(__file__), 'ucecs_output')
    os.makedirs(output_dir, exist_ok=True)
    
    # Get available collector types
    collector_types = manager.get_collector_types()
    logger.info(f"Available collector types: {collector_types}")
    
    # Get available validator types
    validator_types = manager.get_validator_types()
    logger.info(f"Available validator types: {validator_types}")
    
    # Get available storage types
    storage_types = manager.get_storage_types()
    logger.info(f"Available storage types: {storage_types}")
    
    # Collect evidence from the file system
    logger.info("Collecting evidence from the file system")
    
    file_evidence = manager.collect_evidence('file_system', {
        'file_path': '/path/to/evidence/file.txt'
    })
    
    logger.info(f"Collected file evidence: {file_evidence.get('id')}")
    
    # Save the evidence to a file
    file_evidence_path = os.path.join(output_dir, f"{file_evidence.get('id')}_collected.json")
    
    with open(file_evidence_path, 'w', encoding='utf-8') as f:
        json.dump(filter_sensitive_data(file_evidence), f, indent=2)
    
    logger.info(f"Saved file evidence to {file_evidence_path}")
    
    # Validate the file evidence
    logger.info("Validating file evidence")
    
    validated_file_evidence = manager.validate_evidence(file_evidence, 'file_exists')
    
    logger.info(f"Validated file evidence: {validated_file_evidence.get('id')}")
    logger.info(f"Validation result: {validated_file_evidence.get('validation_results', {}).get('is_valid')}")
    
    # Save the validated evidence to a file
    validated_file_evidence_path = os.path.join(output_dir, f"{validated_file_evidence.get('id')}_validated.json")
    
    with open(validated_file_evidence_path, 'w', encoding='utf-8') as f:
        json.dump(filter_sensitive_data(validated_file_evidence), f, indent=2)
    
    logger.info(f"Saved validated file evidence to {validated_file_evidence_path}")
    
    # Store the file evidence
    logger.info("Storing file evidence")
    
    stored_file_evidence = manager.store_evidence(validated_file_evidence, 'file_system')
    
    logger.info(f"Stored file evidence: {stored_file_evidence.get('id')}")
    logger.info(f"Storage location: {stored_file_evidence.get('storage_location')}")
    
    # Save the stored evidence to a file
    stored_file_evidence_path = os.path.join(output_dir, f"{stored_file_evidence.get('id')}_stored.json")
    
    with open(stored_file_evidence_path, 'w', encoding='utf-8') as f:
        json.dump(filter_sensitive_data(stored_file_evidence), f, indent=2)
    
    logger.info(f"Saved stored file evidence to {stored_file_evidence_path}")
    
    # Collect evidence from a database
    logger.info("Collecting evidence from a database")
    
    db_evidence = manager.collect_evidence('database', {
        'connection_string': 'postgresql://user:password@localhost:5432/database',
        'query': 'SELECT * FROM users'
    })
    
    logger.info(f"Collected database evidence: {db_evidence.get('id')}")
    
    # Save the evidence to a file
    db_evidence_path = os.path.join(output_dir, f"{db_evidence.get('id')}_collected.json")
    
    with open(db_evidence_path, 'w', encoding='utf-8') as f:
        json.dump(filter_sensitive_data(db_evidence), f, indent=2)
    
    logger.info(f"Saved database evidence to {db_evidence_path}")
    
    # Validate the database evidence
    logger.info("Validating database evidence")
    
    validated_db_evidence = manager.validate_evidence(db_evidence, 'database_results')
    
    logger.info(f"Validated database evidence: {validated_db_evidence.get('id')}")
    logger.info(f"Validation result: {validated_db_evidence.get('validation_results', {}).get('is_valid')}")
    
    # Save the validated evidence to a file
    validated_db_evidence_path = os.path.join(output_dir, f"{validated_db_evidence.get('id')}_validated.json")
    
    with open(validated_db_evidence_path, 'w', encoding='utf-8') as f:
        json.dump(filter_sensitive_data(validated_db_evidence), f, indent=2)
    
    logger.info(f"Saved validated database evidence to {validated_db_evidence_path}")
    
    # Store the database evidence
    logger.info("Storing database evidence")
    
    stored_db_evidence = manager.store_evidence(validated_db_evidence, 'database')
    
    logger.info(f"Stored database evidence: {stored_db_evidence.get('id')}")
    logger.info(f"Storage location: {stored_db_evidence.get('storage_location')}")
    
    # Save the stored evidence to a file
    stored_db_evidence_path = os.path.join(output_dir, f"{stored_db_evidence.get('id')}_stored.json")
    
    with open(stored_db_evidence_path, 'w', encoding='utf-8') as f:
        json.dump(filter_sensitive_data(stored_db_evidence), f, indent=2)
    
    logger.info(f"Saved stored database evidence to {stored_db_evidence_path}")
    
    # Collect evidence from an API
    logger.info("Collecting evidence from an API")
    
    api_evidence = manager.collect_evidence('api', {
        'url': 'https://api.example.com/data',
        'method': 'GET',
        'headers': {'Authorization': 'Bearer token123'}
    })
    
    logger.info(f"Collected API evidence: {api_evidence.get('id')}")
    
    # Save the evidence to a file
    api_evidence_path = os.path.join(output_dir, f"{api_evidence.get('id')}_collected.json")
    
    with open(api_evidence_path, 'w', encoding='utf-8') as f:
        json.dump(filter_sensitive_data(api_evidence), f, indent=2)
    
    logger.info(f"Saved API evidence to {api_evidence_path}")
    
    # Validate the API evidence
    logger.info("Validating API evidence")
    
    validated_api_evidence = manager.validate_evidence(api_evidence, 'api_status')
    
    logger.info(f"Validated API evidence: {validated_api_evidence.get('id')}")
    logger.info(f"Validation result: {validated_api_evidence.get('validation_results', {}).get('is_valid')}")
    
    # Save the validated evidence to a file
    validated_api_evidence_path = os.path.join(output_dir, f"{validated_api_evidence.get('id')}_validated.json")
    
    with open(validated_api_evidence_path, 'w', encoding='utf-8') as f:
        json.dump(filter_sensitive_data(validated_api_evidence), f, indent=2)
    
    logger.info(f"Saved validated API evidence to {validated_api_evidence_path}")
    
    # Store the API evidence
    logger.info("Storing API evidence")
    
    stored_api_evidence = manager.store_evidence(validated_api_evidence, 's3')
    
    logger.info(f"Stored API evidence: {stored_api_evidence.get('id')}")
    logger.info(f"Storage location: {stored_api_evidence.get('storage_location')}")
    
    # Save the stored evidence to a file
    stored_api_evidence_path = os.path.join(output_dir, f"{stored_api_evidence.get('id')}_stored.json")
    
    with open(stored_api_evidence_path, 'w', encoding='utf-8') as f:
        json.dump(filter_sensitive_data(stored_api_evidence), f, indent=2)
    
    logger.info(f"Saved stored API evidence to {stored_api_evidence_path}")
    
    # Create a summary of all evidence
    evidence_list = [
        stored_file_evidence,
        stored_db_evidence,
        stored_api_evidence
    ]
    
    evidence_summaries = [get_evidence_summary(e) for e in evidence_list]
    
    summary_path = os.path.join(output_dir, "evidence_summary.json")
    
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(evidence_summaries, f, indent=2)
    
    logger.info(f"Saved evidence summary to {summary_path}")
    
    logger.info("UCECS demo completed successfully")

if __name__ == "__main__":
    main()
