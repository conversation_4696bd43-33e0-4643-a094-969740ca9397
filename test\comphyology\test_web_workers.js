/**
 * Comphyology Web Worker Test
 * 
 * This script tests the Web Worker implementation for Comphyology visualizations.
 */

const fs = require('fs');
const path = require('path');
const { ComphyologyCore } = require('../../src/comphyology/index');
const ComphyologyVisualization = require('../../src/comphyology/visualization');
const ComphyologyVisualizationWorkerManager = require('../../src/comphyology/worker/visualization_worker_manager');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../comphyology_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Test Web Worker vs. Main Thread Performance
 */
async function testWorkerPerformance() {
  console.log('\n=== Testing Web Worker vs. Main Thread Performance ===');
  
  // Initialize visualization generator
  const visualizer = new ComphyologyVisualization({ 
    enableLogging: false,
    resolution: 50
  });
  
  // Initialize worker manager
  const workerManager = new ComphyologyVisualizationWorkerManager({
    enableLogging: true,
    maxWorkers: 4,
    resolution: 50
  });
  
  // Wait for worker manager to initialize
  await workerManager.initialize();
  
  // Test scenarios
  const scenarios = [
    { name: 'Morphological Resonance Field', resolution: 50 },
    { name: 'Quantum Phase Space Map', resolution: 50 },
    { name: 'Ethical Tensor Projection', resolution: 50 },
    { name: 'Trinity Integration Diagram', resolution: 50 }
  ];
  
  // Results
  const results = [];
  
  // Run tests for each scenario
  for (const scenario of scenarios) {
    console.log(`\nScenario: ${scenario.name} (Resolution: ${scenario.resolution})`);
    
    // Create options
    const options = {
      resolution: scenario.resolution,
      useCache: false
    };
    
    // Test main thread
    console.log('Testing main thread:');
    const mainThreadStartTime = performance.now();
    
    let mainThreadData;
    switch (scenario.name) {
      case 'Morphological Resonance Field':
        mainThreadData = visualizer.generateMorphologicalResonanceField(options);
        break;
      case 'Quantum Phase Space Map':
        mainThreadData = visualizer.generateQuantumPhaseSpaceMap(options);
        break;
      case 'Ethical Tensor Projection':
        mainThreadData = visualizer.generateEthicalTensorProjection(options);
        break;
      case 'Trinity Integration Diagram':
        mainThreadData = visualizer.generateTrinityIntegrationDiagram(options);
        break;
    }
    
    const mainThreadDuration = performance.now() - mainThreadStartTime;
    console.log(`  Main Thread Time: ${mainThreadDuration.toFixed(2)}ms`);
    
    // Test worker thread
    console.log('Testing worker thread:');
    const workerStartTime = performance.now();
    
    let workerData;
    try {
      switch (scenario.name) {
        case 'Morphological Resonance Field':
          workerData = await workerManager.generateMorphologicalResonanceField(options);
          break;
        case 'Quantum Phase Space Map':
          workerData = await workerManager.generateQuantumPhaseSpaceMap(options);
          break;
        case 'Ethical Tensor Projection':
          workerData = await workerManager.generateEthicalTensorProjection(options);
          break;
        case 'Trinity Integration Diagram':
          workerData = await workerManager.generateTrinityIntegrationDiagram(options);
          break;
      }
      
      const workerDuration = performance.now() - workerStartTime;
      console.log(`  Worker Thread Time: ${workerDuration.toFixed(2)}ms`);
      
      // Calculate speedup
      const speedup = mainThreadDuration / workerDuration;
      console.log(`  Speedup: ${speedup.toFixed(2)}x`);
      
      // Add to results
      results.push({
        scenario: scenario.name,
        resolution: scenario.resolution,
        mainThreadTime: mainThreadDuration,
        workerTime: workerDuration,
        speedup
      });
    } catch (error) {
      console.error(`  Worker Thread Error: ${error.message}`);
      
      // Add to results
      results.push({
        scenario: scenario.name,
        resolution: scenario.resolution,
        mainThreadTime: mainThreadDuration,
        workerTime: null,
        speedup: null,
        error: error.message
      });
    }
  }
  
  // Terminate worker manager
  workerManager.terminate();
  
  // Save results to file
  const resultFile = path.join(RESULTS_DIR, `web_worker_performance_${new Date().toISOString().replace(/:/g, '-')}.json`);
  fs.writeFileSync(resultFile, JSON.stringify(results, null, 2));
  
  console.log(`\nResults saved to ${resultFile}`);
  
  // Print summary
  console.log('\n=== Web Worker Performance Summary ===');
  console.log('Scenario | Resolution | Main Thread (ms) | Worker Thread (ms) | Speedup');
  console.log('-------------------------------------------------------------------------');
  for (const result of results) {
    if (result.workerTime) {
      console.log(`${result.scenario.padEnd(20)} | ${result.resolution.toString().padStart(10)} | ${result.mainThreadTime.toFixed(2).padStart(15)} | ${result.workerTime.toFixed(2).padStart(17)} | ${result.speedup.toFixed(2).padStart(7)}x`);
    } else {
      console.log(`${result.scenario.padEnd(20)} | ${result.resolution.toString().padStart(10)} | ${result.mainThreadTime.toFixed(2).padStart(15)} | ${'ERROR'.padStart(17)} | ${'N/A'.padStart(7)}`);
    }
  }
  
  return results;
}

/**
 * Test parallel processing with Web Workers
 */
async function testParallelProcessing() {
  console.log('\n=== Testing Parallel Processing with Web Workers ===');
  
  // Initialize worker manager
  const workerManager = new ComphyologyVisualizationWorkerManager({
    enableLogging: true,
    maxWorkers: 4,
    resolution: 50
  });
  
  // Wait for worker manager to initialize
  await workerManager.initialize();
  
  // Create options
  const options = {
    resolution: 50,
    useCache: false
  };
  
  // Test sequential processing
  console.log('Testing sequential processing:');
  const sequentialStartTime = performance.now();
  
  await workerManager.generateMorphologicalResonanceField(options);
  await workerManager.generateQuantumPhaseSpaceMap(options);
  await workerManager.generateEthicalTensorProjection(options);
  await workerManager.generateTrinityIntegrationDiagram(options);
  
  const sequentialDuration = performance.now() - sequentialStartTime;
  console.log(`  Sequential Processing Time: ${sequentialDuration.toFixed(2)}ms`);
  
  // Test parallel processing
  console.log('Testing parallel processing:');
  const parallelStartTime = performance.now();
  
  await Promise.all([
    workerManager.generateMorphologicalResonanceField(options),
    workerManager.generateQuantumPhaseSpaceMap(options),
    workerManager.generateEthicalTensorProjection(options),
    workerManager.generateTrinityIntegrationDiagram(options)
  ]);
  
  const parallelDuration = performance.now() - parallelStartTime;
  console.log(`  Parallel Processing Time: ${parallelDuration.toFixed(2)}ms`);
  
  // Calculate speedup
  const speedup = sequentialDuration / parallelDuration;
  console.log(`  Speedup: ${speedup.toFixed(2)}x`);
  
  // Terminate worker manager
  workerManager.terminate();
  
  // Save results to file
  const resultFile = path.join(RESULTS_DIR, `parallel_processing_${new Date().toISOString().replace(/:/g, '-')}.json`);
  fs.writeFileSync(resultFile, JSON.stringify({
    sequentialTime: sequentialDuration,
    parallelTime: parallelDuration,
    speedup
  }, null, 2));
  
  console.log(`\nResults saved to ${resultFile}`);
  
  return {
    sequentialTime: sequentialDuration,
    parallelTime: parallelDuration,
    speedup
  };
}

/**
 * Main test function
 */
async function main() {
  console.log('=== Comphyology Web Worker Test ===');
  
  // Run performance tests
  const performanceResults = await testWorkerPerformance();
  
  // Run parallel processing tests
  const parallelResults = await testParallelProcessing();
  
  // Print conclusion
  console.log('\n=== Web Worker Implementation Conclusion ===');
  console.log('1. Web Workers provide significant performance improvements for visualization generation');
  console.log('2. Parallel processing with Web Workers enables efficient generation of multiple visualizations');
  console.log('3. The implementation gracefully falls back to main thread processing when Web Workers are not available');
  console.log('4. The API remains the same whether using Web Workers or main thread processing');
  
  console.log('\nCONCLUSION: Web Worker implementation provides significant performance improvements while maintaining API compatibility.');
}

// Run the tests
main().catch(error => {
  console.error('Test failed:', error);
});
