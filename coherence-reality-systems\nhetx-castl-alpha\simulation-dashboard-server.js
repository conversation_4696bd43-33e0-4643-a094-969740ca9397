/**
 * SIM<PERSON>LATION DASHBOARD SERVER
 * 
 * Web-based dashboard for monitoring ALPHA 90-day simulation
 * Real-time updates and comprehensive performance visualization
 * 
 * Mission: Provide intuitive monitoring interface for simulation progress
 */

const express = require('express');
const WebSocket = require('ws');
const path = require('path');
const moment = require('moment');

console.log('\n🌐 SIMULATION DASHBOARD SERVER INITIALIZING');
console.log('='.repeat(70));
console.log('📊 Mission: Real-time ALPHA simulation monitoring dashboard');
console.log('🎯 Features: Performance charts, trade log, MT5 comparison');
console.log('🔄 Updates: Real-time via WebSocket');
console.log('='.repeat(70));

// DASHBOARD CONFIGURATION
const DASHBOARD_CONFIG = {
  // Server Settings
  port: process.env.DASHBOARD_PORT || 8100,
  websocket_port: process.env.DASHBOARD_WS_PORT || 8101,
  
  // Update Intervals
  data_refresh_interval: 5000,  // 5 seconds
  chart_update_interval: 10000, // 10 seconds
  
  // Dashboard Settings
  max_chart_points: 1000,
  default_timeframe: '1h',
  
  // External APIs
  performance_api: 'http://localhost:8103',
  mt5_api: 'http://localhost:8104',
  simulation_api: 'http://localhost:8100'
};

// SIMULATION DASHBOARD SERVER
class SimulationDashboardServer {
  constructor() {
    this.name = 'Simulation Dashboard Server';
    this.version = '1.0.0-REAL_TIME';
    
    // Dashboard Data
    this.dashboard_data = {
      simulation_status: 'INITIALIZING',
      current_metrics: {},
      performance_history: [],
      trade_log: [],
      mt5_comparison: {},
      alerts: []
    };
    
    // WebSocket Connections
    this.websocket_clients = new Set();
    
    // Express App
    this.app = express();
    this.setupExpressApp();
    
    console.log(`🌐 ${this.name} v${this.version} initialized`);
  }

  // START DASHBOARD SERVER
  async startDashboardServer() {
    console.log('\n🚀 STARTING SIMULATION DASHBOARD SERVER');
    console.log('='.repeat(50));
    
    try {
      // Start Express server
      this.startExpressServer();
      
      // Start WebSocket server
      this.startWebSocketServer();
      
      // Start data collection
      this.startDataCollection();
      
      console.log('✅ Dashboard server started successfully');
      console.log(`🌐 Dashboard: http://localhost:${DASHBOARD_CONFIG.port}`);
      console.log(`📡 WebSocket: ws://localhost:${DASHBOARD_CONFIG.websocket_port}`);
      
    } catch (error) {
      console.error('❌ Dashboard server startup failed:', error.message);
    }
  }

  // SETUP EXPRESS APP
  setupExpressApp() {
    this.app.use(express.json());
    this.app.use(express.static(path.join(__dirname, '../deployment-systems')));
    
    // CORS middleware
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
      next();
    });

    // Dashboard Routes
    this.app.get('/', (req, res) => {
      res.send(this.generateDashboardHTML());
    });

    this.app.get('/api/dashboard/data', (req, res) => {
      res.json(this.dashboard_data);
    });

    this.app.get('/api/dashboard/status', (req, res) => {
      res.json({
        server_status: 'RUNNING',
        connected_clients: this.websocket_clients.size,
        last_update: moment().toISOString(),
        data_points: this.dashboard_data.performance_history.length
      });
    });

    this.app.post('/api/dashboard/alert', (req, res) => {
      this.addAlert(req.body);
      res.json({ success: true });
    });

    this.app.get('/api/dashboard/export', (req, res) => {
      const export_data = this.generateExportData();
      res.json(export_data);
    });
  }

  // START EXPRESS SERVER
  startExpressServer() {
    this.server = this.app.listen(DASHBOARD_CONFIG.port, () => {
      console.log(`🌐 Dashboard server running on port ${DASHBOARD_CONFIG.port}`);
    });
  }

  // START WEBSOCKET SERVER
  startWebSocketServer() {
    this.wss = new WebSocket.Server({ port: DASHBOARD_CONFIG.websocket_port });
    
    this.wss.on('connection', (ws) => {
      console.log('📡 New dashboard client connected');
      this.websocket_clients.add(ws);
      
      // Send current data to new client
      ws.send(JSON.stringify({
        type: 'dashboard_data',
        data: this.dashboard_data
      }));
      
      ws.on('close', () => {
        console.log('📡 Dashboard client disconnected');
        this.websocket_clients.delete(ws);
      });
      
      ws.on('error', (error) => {
        console.error('📡 WebSocket error:', error.message);
        this.websocket_clients.delete(ws);
      });
    });
    
    console.log(`📡 WebSocket server running on port ${DASHBOARD_CONFIG.websocket_port}`);
  }

  // START DATA COLLECTION
  startDataCollection() {
    // Collect performance data
    setInterval(() => {
      this.collectPerformanceData();
    }, DASHBOARD_CONFIG.data_refresh_interval);
    
    // Collect MT5 comparison data
    setInterval(() => {
      this.collectMT5Data();
    }, DASHBOARD_CONFIG.data_refresh_interval * 2);
    
    // Update charts
    setInterval(() => {
      this.updateChartData();
      this.broadcastToClients();
    }, DASHBOARD_CONFIG.chart_update_interval);
    
    console.log('🔄 Data collection started');
  }

  // COLLECT PERFORMANCE DATA
  async collectPerformanceData() {
    try {
      // This would fetch from the performance tracker API
      // For now, we'll simulate the data
      
      const current_metrics = {
        timestamp: moment().toISOString(),
        portfolio_value: 100000 + (Math.random() - 0.5) * 10000,
        total_return: (Math.random() - 0.3) * 0.5, // -30% to +20%
        daily_return: (Math.random() - 0.5) * 0.02,
        win_rate: 0.6 + Math.random() * 0.3, // 60% to 90%
        total_trades: Math.floor(Math.random() * 50) + 10,
        sharpe_ratio: Math.random() * 3,
        max_drawdown: Math.random() * 0.15,
        alpha_accuracy: 0.7 + Math.random() * 0.25,
        psi_inflection_trades: Math.floor(Math.random() * 20),
        consciousness_events: Math.floor(Math.random() * 100) + 50
      };
      
      this.dashboard_data.current_metrics = current_metrics;
      this.dashboard_data.performance_history.push(current_metrics);
      
      // Keep only recent data points
      if (this.dashboard_data.performance_history.length > DASHBOARD_CONFIG.max_chart_points) {
        this.dashboard_data.performance_history = this.dashboard_data.performance_history.slice(-DASHBOARD_CONFIG.max_chart_points);
      }
      
      // Update simulation status
      this.updateSimulationStatus(current_metrics);
      
    } catch (error) {
      console.error('❌ Performance data collection failed:', error.message);
    }
  }

  // COLLECT MT5 DATA
  async collectMT5Data() {
    try {
      // This would fetch from the MT5 connector API
      // For now, we'll simulate the data
      
      const mt5_comparison = {
        connection_status: 'CONNECTED',
        account_balance: 100000,
        active_positions: Math.floor(Math.random() * 5),
        correlation: 0.7 + Math.random() * 0.25,
        performance_difference: (Math.random() - 0.5) * 0.1,
        deployment_readiness: Math.random() * 100
      };
      
      this.dashboard_data.mt5_comparison = mt5_comparison;
      
    } catch (error) {
      console.error('❌ MT5 data collection failed:', error.message);
    }
  }

  // UPDATE SIMULATION STATUS
  updateSimulationStatus(metrics) {
    if (metrics.total_trades === 0) {
      this.dashboard_data.simulation_status = 'INITIALIZING';
    } else if (metrics.total_trades < 10) {
      this.dashboard_data.simulation_status = 'WARMING_UP';
    } else if (metrics.total_return >= 0.15 && metrics.win_rate >= 0.75) {
      this.dashboard_data.simulation_status = 'EXCELLENT';
    } else if (metrics.total_return >= 0.10 && metrics.win_rate >= 0.65) {
      this.dashboard_data.simulation_status = 'GOOD';
    } else if (metrics.total_return >= 0.05 && metrics.win_rate >= 0.55) {
      this.dashboard_data.simulation_status = 'FAIR';
    } else {
      this.dashboard_data.simulation_status = 'POOR';
    }
  }

  // UPDATE CHART DATA
  updateChartData() {
    // Add simulated trade log entries
    if (Math.random() > 0.7) { // 30% chance of new trade
      const trade = {
        timestamp: moment().toISOString(),
        symbol: ['SPY', 'QQQ', 'AAPL', 'MSFT', 'GOOGL'][Math.floor(Math.random() * 5)],
        action: Math.random() > 0.5 ? 'BUY' : 'SELL',
        quantity: Math.floor(Math.random() * 100) + 10,
        price: 100 + Math.random() * 300,
        psi_alignment: 0.8 + Math.random() * 0.2,
        signal_strength: 0.7 + Math.random() * 0.3,
        status: 'EXECUTED'
      };
      
      this.dashboard_data.trade_log.unshift(trade);
      
      // Keep only recent trades
      if (this.dashboard_data.trade_log.length > 100) {
        this.dashboard_data.trade_log = this.dashboard_data.trade_log.slice(0, 100);
      }
    }
  }

  // ADD ALERT
  addAlert(alert_data) {
    const alert = {
      id: Date.now(),
      timestamp: moment().toISOString(),
      type: alert_data.type || 'INFO',
      message: alert_data.message || 'Alert',
      acknowledged: false
    };
    
    this.dashboard_data.alerts.unshift(alert);
    
    // Keep only recent alerts
    if (this.dashboard_data.alerts.length > 50) {
      this.dashboard_data.alerts = this.dashboard_data.alerts.slice(0, 50);
    }
    
    // Broadcast alert to clients
    this.broadcastToClients({
      type: 'new_alert',
      data: alert
    });
  }

  // BROADCAST TO CLIENTS
  broadcastToClients(message = null) {
    if (this.websocket_clients.size === 0) return;
    
    const data = message || {
      type: 'dashboard_update',
      data: this.dashboard_data
    };
    
    const message_str = JSON.stringify(data);
    
    this.websocket_clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message_str);
      }
    });
  }

  // GENERATE EXPORT DATA
  generateExportData() {
    return {
      export_timestamp: moment().toISOString(),
      simulation_summary: {
        status: this.dashboard_data.simulation_status,
        current_metrics: this.dashboard_data.current_metrics,
        mt5_comparison: this.dashboard_data.mt5_comparison
      },
      performance_history: this.dashboard_data.performance_history,
      trade_log: this.dashboard_data.trade_log,
      alerts: this.dashboard_data.alerts
    };
  }

  // GENERATE DASHBOARD HTML
  generateDashboardHTML() {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ALPHA 90-Day Simulation Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .card h3 {
            margin: 0 0 15px 0;
            font-size: 1.3em;
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
            padding-bottom: 10px;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .metric:last-child {
            border-bottom: none;
        }
        .metric-value {
            font-weight: bold;
            font-size: 1.1em;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-excellent { background: #4CAF50; }
        .status-good { background: #8BC34A; }
        .status-fair { background: #FFC107; }
        .status-poor { background: #F44336; }
        .status-initializing { background: #2196F3; }
        .status-warming_up { background: #FF9800; }
        .trade-log {
            max-height: 300px;
            overflow-y: auto;
            font-size: 0.9em;
        }
        .trade-entry {
            padding: 8px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
            border-left: 3px solid #4CAF50;
        }
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 0.9em;
            font-weight: bold;
        }
        .connected { background: #4CAF50; }
        .disconnected { background: #F44336; }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            font-size: 0.9em;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">Connecting...</div>
    
    <div class="header">
        <h1>🎯 ALPHA 90-Day Simulation Dashboard</h1>
        <p>Real-time monitoring of ALPHA Observer-Class Engine trading simulation</p>
        <p>Target: 20%+ returns, 80%+ win rate before MetaTrader 5 deployment</p>
    </div>

    <div class="dashboard-grid">
        <div class="card">
            <h3>📊 Current Performance</h3>
            <div class="metric">
                <span>Status:</span>
                <span class="metric-value" id="simulationStatus">
                    <span class="status-indicator status-initializing"></span>Initializing
                </span>
            </div>
            <div class="metric">
                <span>Total Return:</span>
                <span class="metric-value" id="totalReturn">0.00%</span>
            </div>
            <div class="metric">
                <span>Win Rate:</span>
                <span class="metric-value" id="winRate">0.0%</span>
            </div>
            <div class="metric">
                <span>Sharpe Ratio:</span>
                <span class="metric-value" id="sharpeRatio">0.00</span>
            </div>
            <div class="metric">
                <span>Max Drawdown:</span>
                <span class="metric-value" id="maxDrawdown">0.00%</span>
            </div>
        </div>

        <div class="card">
            <h3>⚡ ALPHA Consciousness Metrics</h3>
            <div class="metric">
                <span>Total Trades:</span>
                <span class="metric-value" id="totalTrades">0</span>
            </div>
            <div class="metric">
                <span>Ψᶜʰ Inflection Trades:</span>
                <span class="metric-value" id="psiTrades">0</span>
            </div>
            <div class="metric">
                <span>ALPHA Accuracy:</span>
                <span class="metric-value" id="alphaAccuracy">0.0%</span>
            </div>
            <div class="metric">
                <span>Consciousness Events:</span>
                <span class="metric-value" id="consciousnessEvents">0</span>
            </div>
        </div>

        <div class="card">
            <h3>🔌 MT5 Comparison</h3>
            <div class="metric">
                <span>Connection:</span>
                <span class="metric-value" id="mt5Connection">Disconnected</span>
            </div>
            <div class="metric">
                <span>Correlation:</span>
                <span class="metric-value" id="mt5Correlation">0.00</span>
            </div>
            <div class="metric">
                <span>Deployment Readiness:</span>
                <span class="metric-value" id="deploymentReadiness">0%</span>
            </div>
        </div>

        <div class="card">
            <h3>📈 Recent Trades</h3>
            <div class="trade-log" id="tradeLog">
                <div style="text-align: center; opacity: 0.7; padding: 20px;">
                    No trades yet...
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>🌟 ALPHA Observer-Class Engine - Proto-Sentient Trading System</p>
        <p>Preparing for deployment to MetaTrader 5 Account: **********</p>
    </div>

    <script>
        let ws;
        let reconnectInterval;

        function connectWebSocket() {
            ws = new WebSocket('ws://localhost:${DASHBOARD_CONFIG.websocket_port}');
            
            ws.onopen = function() {
                console.log('WebSocket connected');
                document.getElementById('connectionStatus').textContent = 'Connected';
                document.getElementById('connectionStatus').className = 'connection-status connected';
                clearInterval(reconnectInterval);
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                updateDashboard(message.data);
            };
            
            ws.onclose = function() {
                console.log('WebSocket disconnected');
                document.getElementById('connectionStatus').textContent = 'Disconnected';
                document.getElementById('connectionStatus').className = 'connection-status disconnected';
                
                // Attempt to reconnect every 5 seconds
                reconnectInterval = setInterval(connectWebSocket, 5000);
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
            };
        }

        function updateDashboard(data) {
            if (!data) return;
            
            // Update simulation status
            if (data.simulation_status) {
                const statusElement = document.getElementById('simulationStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                indicator.className = 'status-indicator status-' + data.simulation_status.toLowerCase();
                statusElement.innerHTML = indicator.outerHTML + data.simulation_status.replace('_', ' ');
            }
            
            // Update performance metrics
            if (data.current_metrics) {
                const metrics = data.current_metrics;
                document.getElementById('totalReturn').textContent = (metrics.total_return * 100).toFixed(2) + '%';
                document.getElementById('winRate').textContent = (metrics.win_rate * 100).toFixed(1) + '%';
                document.getElementById('sharpeRatio').textContent = metrics.sharpe_ratio.toFixed(2);
                document.getElementById('maxDrawdown').textContent = (metrics.max_drawdown * 100).toFixed(2) + '%';
                document.getElementById('totalTrades').textContent = metrics.total_trades;
                document.getElementById('psiTrades').textContent = metrics.psi_inflection_trades;
                document.getElementById('alphaAccuracy').textContent = (metrics.alpha_accuracy * 100).toFixed(1) + '%';
                document.getElementById('consciousnessEvents').textContent = metrics.consciousness_events;
            }
            
            // Update MT5 comparison
            if (data.mt5_comparison) {
                const mt5 = data.mt5_comparison;
                document.getElementById('mt5Connection').textContent = mt5.connection_status || 'Disconnected';
                document.getElementById('mt5Correlation').textContent = (mt5.correlation || 0).toFixed(2);
                document.getElementById('deploymentReadiness').textContent = (mt5.deployment_readiness || 0).toFixed(0) + '%';
            }
            
            // Update trade log
            if (data.trade_log && data.trade_log.length > 0) {
                const tradeLogElement = document.getElementById('tradeLog');
                tradeLogElement.innerHTML = data.trade_log.slice(0, 10).map(trade => 
                    '<div class="trade-entry">' +
                    '<strong>' + trade.action + ' ' + trade.symbol + '</strong> - ' +
                    trade.quantity + ' @ $' + trade.price.toFixed(2) + '<br>' +
                    '<small>Ψᶜʰ: ' + (trade.psi_alignment * 100).toFixed(1) + '% | ' +
                    'Signal: ' + (trade.signal_strength * 100).toFixed(1) + '%</small>' +
                    '</div>'
                ).join('');
            }
        }

        // Connect WebSocket on page load
        connectWebSocket();
        
        // Refresh page data every 30 seconds as fallback
        setInterval(() => {
            fetch('/api/dashboard/data')
                .then(response => response.json())
                .then(data => updateDashboard(data))
                .catch(error => console.error('Failed to fetch dashboard data:', error));
        }, 30000);
    </script>
</body>
</html>
    `;
  }
}

// Export for use
module.exports = { 
  SimulationDashboardServer,
  DASHBOARD_CONFIG
};

// Execute dashboard server if run directly
if (require.main === module) {
  const dashboard = new SimulationDashboardServer();
  dashboard.startDashboardServer();
}
