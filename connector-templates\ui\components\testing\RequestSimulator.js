/**
 * Request Simulator Component
 * 
 * This component allows users to simulate API requests to test connector endpoints.
 */

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  <PERSON>ton, 
  Card, 
  CardContent, 
  CircularProgress, 
  Divider, 
  FormControl, 
  Grid, 
  InputLabel, 
  MenuItem, 
  Select, 
  TextField, 
  Typography 
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import SaveIcon from '@mui/icons-material/Save';
import HistoryIcon from '@mui/icons-material/History';
import ParameterInput from './ParameterInput';
import ResponseViewer from './ResponseViewer';
import RequestHistory from './RequestHistory';

const RequestSimulator = ({ connector, credentials }) => {
  const [selectedEndpoint, setSelectedEndpoint] = useState('');
  const [parameters, setParameters] = useState({});
  const [headers, setHeaders] = useState({});
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState(null);
  const [showHistory, setShowHistory] = useState(false);
  const [history, setHistory] = useState([]);
  const [error, setError] = useState(null);

  // Reset parameters when endpoint changes
  useEffect(() => {
    if (selectedEndpoint) {
      const endpoint = connector.endpoints.find(e => e.id === selectedEndpoint);
      if (endpoint) {
        const defaultParams = {};
        if (endpoint.parameters) {
          endpoint.parameters.forEach(param => {
            if (param.default !== undefined) {
              defaultParams[param.name] = param.default;
            }
          });
        }
        setParameters(defaultParams);
      }
    } else {
      setParameters({});
    }
    setResponse(null);
    setError(null);
  }, [selectedEndpoint, connector.endpoints]);

  const handleEndpointChange = (event) => {
    setSelectedEndpoint(event.target.value);
  };

  const handleParameterChange = (name, value) => {
    setParameters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleHeaderChange = (name, value) => {
    setHeaders(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddHeader = () => {
    setHeaders(prev => ({
      ...prev,
      '': ''
    }));
  };

  const handleRemoveHeader = (name) => {
    setHeaders(prev => {
      const newHeaders = { ...prev };
      delete newHeaders[name];
      return newHeaders;
    });
  };

  const handleExecute = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // In a real implementation, this would call the API
      const result = await executeEndpoint(connector.id, selectedEndpoint, credentials.id, parameters, headers);
      
      setResponse(result);
      
      // Add to history
      const endpoint = connector.endpoints.find(e => e.id === selectedEndpoint);
      setHistory(prev => [
        {
          id: Date.now(),
          timestamp: new Date(),
          endpoint: endpoint.name,
          parameters,
          response: result
        },
        ...prev
      ]);
    } catch (err) {
      console.error('Error executing endpoint:', err);
      setError(err.message || 'An error occurred while executing the endpoint');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveRequest = () => {
    // In a real implementation, this would save the request to a database
    alert('Request saved!');
  };

  const handleLoadFromHistory = (historyItem) => {
    const endpoint = connector.endpoints.find(e => e.name === historyItem.endpoint);
    if (endpoint) {
      setSelectedEndpoint(endpoint.id);
      setParameters(historyItem.parameters);
      setResponse(historyItem.response);
    }
    setShowHistory(false);
  };

  // Mock function to simulate API call
  const executeEndpoint = async (connectorId, endpointId, credentialId, params, headers) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Simulate response
    return {
      status: 200,
      statusText: 'OK',
      headers: {
        'content-type': 'application/json',
        'server': 'nginx'
      },
      data: {
        success: true,
        timestamp: new Date().toISOString(),
        params: params,
        results: [
          { id: 1, name: 'Sample Result 1' },
          { id: 2, name: 'Sample Result 2' }
        ]
      }
    };
  };

  const getSelectedEndpoint = () => {
    return connector.endpoints.find(e => e.id === selectedEndpoint);
  };

  return (
    <Box>
      <Card variant="outlined" sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Request Simulator
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel id="endpoint-select-label">Endpoint</InputLabel>
                <Select
                  labelId="endpoint-select-label"
                  id="endpoint-select"
                  value={selectedEndpoint}
                  label="Endpoint"
                  onChange={handleEndpointChange}
                >
                  {connector.endpoints.map(endpoint => (
                    <MenuItem key={endpoint.id} value={endpoint.id}>
                      {endpoint.name} - {endpoint.method} {endpoint.path}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            {selectedEndpoint && (
              <>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    Parameters
                  </Typography>
                  
                  {getSelectedEndpoint().parameters && getSelectedEndpoint().parameters.length > 0 ? (
                    getSelectedEndpoint().parameters.map(param => (
                      <ParameterInput
                        key={param.name}
                        parameter={param}
                        value={parameters[param.name] || ''}
                        onChange={(value) => handleParameterChange(param.name, value)}
                      />
                    ))
                  ) : (
                    <Typography variant="body2" color="textSecondary">
                      This endpoint has no parameters.
                    </Typography>
                  )}
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    Headers
                  </Typography>
                  
                  {Object.keys(headers).map(name => (
                    <Grid container spacing={2} key={name} sx={{ mb: 2 }}>
                      <Grid item xs={5}>
                        <TextField
                          fullWidth
                          label="Header Name"
                          value={name}
                          onChange={(e) => {
                            const value = headers[name];
                            handleRemoveHeader(name);
                            handleHeaderChange(e.target.value, value);
                          }}
                        />
                      </Grid>
                      <Grid item xs={5}>
                        <TextField
                          fullWidth
                          label="Header Value"
                          value={headers[name]}
                          onChange={(e) => handleHeaderChange(name, e.target.value)}
                        />
                      </Grid>
                      <Grid item xs={2}>
                        <Button
                          variant="outlined"
                          color="error"
                          onClick={() => handleRemoveHeader(name)}
                          sx={{ mt: 1 }}
                        >
                          Remove
                        </Button>
                      </Grid>
                    </Grid>
                  ))}
                  
                  <Button
                    variant="outlined"
                    onClick={handleAddHeader}
                    sx={{ mt: 1 }}
                  >
                    Add Header
                  </Button>
                </Grid>
                
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                    <Box>
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <PlayArrowIcon />}
                        onClick={handleExecute}
                        disabled={loading || !selectedEndpoint}
                        sx={{ mr: 2 }}
                      >
                        {loading ? 'Executing...' : 'Execute'}
                      </Button>
                      
                      <Button
                        variant="outlined"
                        startIcon={<SaveIcon />}
                        onClick={handleSaveRequest}
                        disabled={!selectedEndpoint}
                      >
                        Save Request
                      </Button>
                    </Box>
                    
                    <Button
                      variant="outlined"
                      startIcon={<HistoryIcon />}
                      onClick={() => setShowHistory(!showHistory)}
                    >
                      {showHistory ? 'Hide History' : 'Show History'}
                    </Button>
                  </Box>
                </Grid>
              </>
            )}
          </Grid>
        </CardContent>
      </Card>
      
      {showHistory && (
        <Card variant="outlined" sx={{ mb: 3 }}>
          <CardContent>
            <RequestHistory history={history} onSelect={handleLoadFromHistory} />
          </CardContent>
        </Card>
      )}
      
      {error && (
        <Card variant="outlined" sx={{ mb: 3, bgcolor: '#ffebee' }}>
          <CardContent>
            <Typography variant="subtitle1" color="error" gutterBottom>
              Error
            </Typography>
            <Typography variant="body2" color="error">
              {error}
            </Typography>
          </CardContent>
        </Card>
      )}
      
      {response && (
        <Card variant="outlined">
          <CardContent>
            <ResponseViewer response={response} />
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default RequestSimulator;
