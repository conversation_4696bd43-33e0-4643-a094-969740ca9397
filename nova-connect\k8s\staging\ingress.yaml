apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: novafuse-uac-ingress
  namespace: novafuse-staging
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "novafuse-staging-ip"
    networking.gke.io/managed-certificates: "novafuse-staging-cert"
    networking.gke.io/v1beta1.FrontendConfig: "novafuse-staging-frontend-config"
spec:
  rules:
  - host: api-staging.novafuse.io
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: novafuse-uac-service
            port:
              number: 80
