/**
 * NovaConnect Test Environment Deployment Script
 * 
 * This script automates the deployment of NovaConnect to a test environment
 * with GCP integration.
 * 
 * Usage:
 *   node scripts/deploy-test-env.js
 * 
 * Environment Variables:
 *   GCP_PROJECT_ID - Google Cloud Project ID
 *   GCP_ORGANIZATION_ID - Google Cloud Organization ID
 *   GCP_CREDENTIALS_PATH - Path to GCP service account key file
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { BigQuery } = require('@google-cloud/bigquery');
const { SecurityCenterClient } = require('@google-cloud/security-center');

// Configuration
const config = {
  projectId: process.env.GCP_PROJECT_ID,
  organizationId: process.env.GCP_ORGANIZATION_ID,
  credentialsPath: process.env.GCP_CREDENTIALS_PATH,
  region: process.env.GCP_REGION || 'us-central1',
  apiPort: process.env.API_PORT || 3000,
  testDataDir: path.join(__dirname, '../tests/data'),
  outputDir: path.join(__dirname, '../deployment')
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

/**
 * Validate environment configuration
 */
function validateConfig() {
  console.log('Validating configuration...');
  
  const requiredVars = ['projectId', 'organizationId', 'credentialsPath'];
  const missingVars = requiredVars.filter(varName => !config[varName]);
  
  if (missingVars.length > 0) {
    console.error(`Missing required environment variables: ${missingVars.join(', ')}`);
    console.error('Please set the following environment variables:');
    console.error('  GCP_PROJECT_ID - Google Cloud Project ID');
    console.error('  GCP_ORGANIZATION_ID - Google Cloud Organization ID');
    console.error('  GCP_CREDENTIALS_PATH - Path to GCP service account key file');
    process.exit(1);
  }
  
  // Check if credentials file exists
  if (!fs.existsSync(config.credentialsPath)) {
    console.error(`GCP credentials file not found: ${config.credentialsPath}`);
    process.exit(1);
  }
  
  console.log('Configuration validated successfully.');
  return true;
}

/**
 * Validate GCP credentials
 */
async function validateGcpCredentials() {
  console.log('Validating GCP credentials...');
  
  try {
    // Set credentials environment variable
    process.env.GOOGLE_APPLICATION_CREDENTIALS = config.credentialsPath;
    
    // Test BigQuery access
    const bigquery = new BigQuery({
      projectId: config.projectId
    });
    
    await bigquery.query({ query: 'SELECT 1' });
    console.log('BigQuery access validated.');
    
    // Test Security Command Center access
    const sccClient = new SecurityCenterClient();
    const [sources] = await sccClient.listSources({
      parent: `organizations/${config.organizationId}`
    });
    
    console.log(`SCC access validated. Found ${sources.length} sources.`);
    
    console.log('GCP credentials validated successfully.');
    return true;
  } catch (error) {
    console.error('Error validating GCP credentials:', error);
    return false;
  }
}

/**
 * Build the NovaConnect API
 */
function buildApi() {
  console.log('Building NovaConnect API...');
  
  try {
    // Run build script
    execSync('npm run build', { stdio: 'inherit' });
    console.log('API built successfully.');
    return true;
  } catch (error) {
    console.error('Error building API:', error);
    return false;
  }
}

/**
 * Deploy the NovaConnect API to a test environment
 */
async function deployApi() {
  console.log('Deploying NovaConnect API...');
  
  try {
    // Create deployment configuration
    const deploymentConfig = {
      projectId: config.projectId,
      organizationId: config.organizationId,
      region: config.region,
      apiPort: config.apiPort,
      gcpCredentialsPath: config.credentialsPath,
      deploymentTime: new Date().toISOString()
    };
    
    // Write deployment configuration to file
    const configFile = path.join(config.outputDir, 'deployment-config.json');
    fs.writeFileSync(configFile, JSON.stringify(deploymentConfig, null, 2));
    
    // Create .env file for the deployment
    const envFile = path.join(config.outputDir, '.env');
    const envContent = `
# NovaConnect Test Environment Configuration
GCP_PROJECT_ID=${config.projectId}
GCP_ORGANIZATION_ID=${config.organizationId}
GCP_REGION=${config.region}
PORT=${config.apiPort}
NODE_ENV=test
LOG_LEVEL=info
GOOGLE_APPLICATION_CREDENTIALS=${config.credentialsPath}
    `.trim();
    
    fs.writeFileSync(envFile, envContent);
    
    // Create deployment script
    const deployScript = path.join(config.outputDir, 'start-api.sh');
    const deployScriptContent = `
#!/bin/bash
# NovaConnect API Startup Script

# Load environment variables
source .env

# Start the API server
cd ..
npm start
    `.trim();
    
    fs.writeFileSync(deployScript, deployScriptContent);
    fs.chmodSync(deployScript, '755');
    
    console.log('API deployment prepared successfully.');
    console.log(`Deployment configuration: ${configFile}`);
    console.log(`Environment file: ${envFile}`);
    console.log(`Startup script: ${deployScript}`);
    
    return true;
  } catch (error) {
    console.error('Error deploying API:', error);
    return false;
  }
}

/**
 * Run tests in the test environment
 */
async function runTests() {
  console.log('Running tests in test environment...');
  
  try {
    // Set environment variables for tests
    process.env.SYSTEM_TEST_API_URL = `http://localhost:${config.apiPort}`;
    process.env.SYSTEM_TEST_API_KEY = 'test-api-key';
    
    // Run integration tests
    console.log('Running integration tests...');
    execSync('npm run test:integration', { stdio: 'inherit' });
    
    // Run system tests
    console.log('Running system tests...');
    execSync('npm run test:system', { stdio: 'inherit' });
    
    // Run security tests
    console.log('Running security tests...');
    execSync('npm run security:encryption', { stdio: 'inherit' });
    
    console.log('All tests completed successfully.');
    return true;
  } catch (error) {
    console.error('Error running tests:', error);
    return false;
  }
}

/**
 * Generate deployment report
 */
function generateReport() {
  console.log('Generating deployment report...');
  
  try {
    const reportFile = path.join(config.outputDir, 'deployment-report.md');
    
    const report = `# NovaConnect Test Environment Deployment Report

## Deployment Information

- **Project ID**: ${config.projectId}
- **Organization ID**: ${config.organizationId}
- **Region**: ${config.region}
- **API Port**: ${config.apiPort}
- **Deployment Time**: ${new Date().toISOString()}

## Deployment Steps

1. Validated environment configuration
2. Validated GCP credentials
3. Built NovaConnect API
4. Deployed API to test environment
5. Ran integration tests
6. Ran system tests
7. Ran security tests

## Next Steps

1. Start the API server using the startup script:
   \`\`\`
   cd ${config.outputDir}
   ./start-api.sh
   \`\`\`

2. Access the API at http://localhost:${config.apiPort}

3. Run the "Breach to Boardroom" demo:
   \`\`\`
   npm run demo:setup
   \`\`\`

## Troubleshooting

If you encounter any issues, please check the logs in the \`logs\` directory.

For more information, see the [NovaConnect Documentation](../docs/README.md).
`;
    
    fs.writeFileSync(reportFile, report);
    
    console.log(`Deployment report generated: ${reportFile}`);
    return true;
  } catch (error) {
    console.error('Error generating report:', error);
    return false;
  }
}

/**
 * Main function
 */
async function main() {
  console.log('Starting NovaConnect test environment deployment...');
  
  // Validate configuration
  if (!validateConfig()) {
    process.exit(1);
  }
  
  // Validate GCP credentials
  if (!await validateGcpCredentials()) {
    process.exit(1);
  }
  
  // Build API
  if (!buildApi()) {
    process.exit(1);
  }
  
  // Deploy API
  if (!await deployApi()) {
    process.exit(1);
  }
  
  // Run tests
  if (!await runTests()) {
    process.exit(1);
  }
  
  // Generate report
  if (!generateReport()) {
    process.exit(1);
  }
  
  console.log('\nNovaConnect test environment deployment completed successfully!');
  console.log(`Deployment files are in: ${config.outputDir}`);
  console.log('To start the API server, run:');
  console.log(`  cd ${config.outputDir}`);
  console.log('  ./start-api.sh');
}

// Run the main function
if (require.main === module) {
  main().catch(error => {
    console.error('Deployment failed:', error);
    process.exit(1);
  });
}

module.exports = {
  validateConfig,
  validateGcpCredentials,
  buildApi,
  deployApi,
  runTests,
  generateReport
};
