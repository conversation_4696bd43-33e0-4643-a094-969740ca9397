"""
Schedule Manager for the Universal Compliance Evidence Collection System.

This module provides functionality for scheduling tasks to run at specific intervals.
"""

import os
import json
import logging
import datetime
import time
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ScheduleInterval(Enum):
    """Intervals for scheduled tasks."""
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


class TaskType(Enum):
    """Types of scheduled tasks."""
    COLLECT_EVIDENCE = "collect_evidence"
    VALIDATE_EVIDENCE = "validate_evidence"
    GENERATE_REPORT = "generate_report"
    CUSTOM = "custom"


class TaskStatus(Enum):
    """Status of scheduled task executions."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class ScheduleManager:
    """
    Manager for scheduling tasks to run at specific intervals.
    
    This class is responsible for scheduling tasks such as evidence collection,
    validation, and report generation to run at specific intervals.
    """
    
    def __init__(self, schedules_dir: Optional[str] = None):
        """
        Initialize the Schedule Manager.
        
        Args:
            schedules_dir: Path to a directory for storing schedule definitions
        """
        logger.info("Initializing Schedule Manager")
        
        # Set the schedules directory
        self.schedules_dir = schedules_dir or os.path.join(os.getcwd(), 'schedules')
        
        # Create the schedules directory if it doesn't exist
        os.makedirs(self.schedules_dir, exist_ok=True)
        
        # Dictionary to store scheduled tasks
        self.scheduled_tasks: Dict[str, Dict[str, Any]] = {}
        
        # Dictionary to store task execution history
        self.task_history: Dict[str, List[Dict[str, Any]]] = {}
        
        # Load scheduled tasks from disk
        self._load_scheduled_tasks()
        
        logger.info("Schedule Manager initialized")
    
    def _load_scheduled_tasks(self) -> None:
        """Load scheduled tasks from disk."""
        try:
            # Get all JSON files in the schedules directory
            schedule_files = [f for f in os.listdir(self.schedules_dir) if f.endswith('.json')]
            
            for schedule_file in schedule_files:
                try:
                    # Load the schedule
                    file_path = os.path.join(self.schedules_dir, schedule_file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        task = json.load(f)
                    
                    # Get the task ID from the filename (without extension)
                    task_id = os.path.splitext(schedule_file)[0]
                    
                    # Add the task to the in-memory dictionary
                    self.scheduled_tasks[task_id] = task
                    
                    logger.info(f"Loaded scheduled task: {task_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load scheduled task from {schedule_file}: {e}")
            
            logger.info(f"Loaded {len(self.scheduled_tasks)} scheduled tasks")
        
        except Exception as e:
            logger.error(f"Failed to load scheduled tasks from directory {self.schedules_dir}: {e}")
    
    def _save_scheduled_task(self, task_id: str) -> None:
        """
        Save a scheduled task to disk.
        
        Args:
            task_id: The ID of the task
        """
        try:
            # Get the task
            task = self.scheduled_tasks.get(task_id)
            
            if not task:
                logger.warning(f"No task found with ID: {task_id}")
                return
            
            # Save the task to disk
            file_path = os.path.join(self.schedules_dir, f"{task_id}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(task, f, indent=2)
            
            logger.info(f"Saved scheduled task: {task_id}")
        
        except Exception as e:
            logger.error(f"Failed to save scheduled task {task_id}: {e}")
    
    def create_scheduled_task(self,
                            name: str,
                            task_type: TaskType,
                            interval: ScheduleInterval,
                            parameters: Dict[str, Any],
                            start_date: Optional[str] = None,
                            end_date: Optional[str] = None,
                            enabled: bool = True) -> str:
        """
        Create a new scheduled task.
        
        Args:
            name: The name of the task
            task_type: The type of task
            interval: The interval at which to run the task
            parameters: Parameters for the task
            start_date: The date from which to start running the task (ISO format)
            end_date: The date after which to stop running the task (ISO format)
            enabled: Whether the task is enabled
            
        Returns:
            The ID of the created task
        """
        # Generate a unique task ID
        task_id = f"{task_type.value}_{int(time.time())}"
        
        # Get the current timestamp
        current_timestamp = datetime.datetime.now(datetime.timezone.utc).isoformat()
        
        # Create the task
        task = {
            'id': task_id,
            'name': name,
            'type': task_type.value,
            'interval': interval.value,
            'parameters': parameters,
            'start_date': start_date or current_timestamp,
            'end_date': end_date,
            'last_run': None,
            'next_run': self._calculate_next_run(current_timestamp, interval),
            'enabled': enabled,
            'created_at': current_timestamp,
            'updated_at': current_timestamp
        }
        
        # Add the task to the in-memory dictionary
        self.scheduled_tasks[task_id] = task
        
        # Save the task to disk
        self._save_scheduled_task(task_id)
        
        logger.info(f"Created scheduled task: {task_id}")
        
        return task_id
    
    def update_scheduled_task(self,
                            task_id: str,
                            name: Optional[str] = None,
                            interval: Optional[ScheduleInterval] = None,
                            parameters: Optional[Dict[str, Any]] = None,
                            start_date: Optional[str] = None,
                            end_date: Optional[str] = None,
                            enabled: Optional[bool] = None) -> Dict[str, Any]:
        """
        Update a scheduled task.
        
        Args:
            task_id: The ID of the task
            name: The name of the task
            interval: The interval at which to run the task
            parameters: Parameters for the task
            start_date: The date from which to start running the task (ISO format)
            end_date: The date after which to stop running the task (ISO format)
            enabled: Whether the task is enabled
            
        Returns:
            The updated task
            
        Raises:
            ValueError: If the task does not exist
        """
        # Check if the task exists
        if task_id not in self.scheduled_tasks:
            raise ValueError(f"Task not found: {task_id}")
        
        # Get the task
        task = self.scheduled_tasks[task_id]
        
        # Update the task
        if name is not None:
            task['name'] = name
        
        if interval is not None:
            task['interval'] = interval.value
            # Recalculate the next run time
            task['next_run'] = self._calculate_next_run(task['last_run'] or task['created_at'], interval)
        
        if parameters is not None:
            task['parameters'] = parameters
        
        if start_date is not None:
            task['start_date'] = start_date
        
        if end_date is not None:
            task['end_date'] = end_date
        
        if enabled is not None:
            task['enabled'] = enabled
        
        # Update the timestamp
        task['updated_at'] = datetime.datetime.now(datetime.timezone.utc).isoformat()
        
        # Save the task to disk
        self._save_scheduled_task(task_id)
        
        logger.info(f"Updated scheduled task: {task_id}")
        
        return task
    
    def delete_scheduled_task(self, task_id: str) -> None:
        """
        Delete a scheduled task.
        
        Args:
            task_id: The ID of the task
            
        Raises:
            ValueError: If the task does not exist
        """
        # Check if the task exists
        if task_id not in self.scheduled_tasks:
            raise ValueError(f"Task not found: {task_id}")
        
        # Remove the task from the in-memory dictionary
        del self.scheduled_tasks[task_id]
        
        # Delete the task file
        file_path = os.path.join(self.schedules_dir, f"{task_id}.json")
        if os.path.exists(file_path):
            os.remove(file_path)
        
        logger.info(f"Deleted scheduled task: {task_id}")
    
    def get_scheduled_task(self, task_id: str) -> Dict[str, Any]:
        """
        Get a scheduled task.
        
        Args:
            task_id: The ID of the task
            
        Returns:
            The task
            
        Raises:
            ValueError: If the task does not exist
        """
        # Check if the task exists
        if task_id not in self.scheduled_tasks:
            raise ValueError(f"Task not found: {task_id}")
        
        return self.scheduled_tasks[task_id]
    
    def get_all_scheduled_tasks(self) -> List[Dict[str, Any]]:
        """
        Get all scheduled tasks.
        
        Returns:
            List of all scheduled tasks
        """
        return list(self.scheduled_tasks.values())
    
    def get_due_tasks(self) -> List[Dict[str, Any]]:
        """
        Get all tasks that are due to run.
        
        Returns:
            List of tasks that are due to run
        """
        # Get the current timestamp
        current_time = datetime.datetime.now(datetime.timezone.utc)
        
        # Find tasks that are due to run
        due_tasks = []
        for task_id, task in self.scheduled_tasks.items():
            # Skip disabled tasks
            if not task.get('enabled', True):
                continue
            
            # Skip tasks that haven't started yet
            start_date = task.get('start_date')
            if start_date:
                start_time = datetime.datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                if current_time < start_time:
                    continue
            
            # Skip tasks that have ended
            end_date = task.get('end_date')
            if end_date:
                end_time = datetime.datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                if current_time > end_time:
                    continue
            
            # Check if the task is due to run
            next_run = task.get('next_run')
            if next_run:
                next_run_time = datetime.datetime.fromisoformat(next_run.replace('Z', '+00:00'))
                if current_time >= next_run_time:
                    due_tasks.append(task)
        
        return due_tasks
    
    def execute_task(self, task_id: str, task_executor: Callable[[Dict[str, Any]], Any]) -> Dict[str, Any]:
        """
        Execute a scheduled task.
        
        Args:
            task_id: The ID of the task
            task_executor: A function that executes the task
            
        Returns:
            The task execution result
            
        Raises:
            ValueError: If the task does not exist
        """
        # Check if the task exists
        if task_id not in self.scheduled_tasks:
            raise ValueError(f"Task not found: {task_id}")
        
        # Get the task
        task = self.scheduled_tasks[task_id]
        
        # Get the current timestamp
        current_timestamp = datetime.datetime.now(datetime.timezone.utc).isoformat()
        
        # Create a task execution record
        execution = {
            'task_id': task_id,
            'start_time': current_timestamp,
            'end_time': None,
            'status': TaskStatus.RUNNING.value,
            'result': None,
            'error': None
        }
        
        # Add the execution to the task history
        if task_id not in self.task_history:
            self.task_history[task_id] = []
        self.task_history[task_id].append(execution)
        
        logger.info(f"Executing scheduled task: {task_id}")
        
        try:
            # Execute the task
            result = task_executor(task)
            
            # Update the execution record
            execution['end_time'] = datetime.datetime.now(datetime.timezone.utc).isoformat()
            execution['status'] = TaskStatus.COMPLETED.value
            execution['result'] = result
            
            # Update the task's last run time
            task['last_run'] = current_timestamp
            
            # Calculate the next run time
            task['next_run'] = self._calculate_next_run(current_timestamp, ScheduleInterval(task['interval']))
            
            # Save the task to disk
            self._save_scheduled_task(task_id)
            
            logger.info(f"Scheduled task executed successfully: {task_id}")
            
        except Exception as e:
            # Update the execution record
            execution['end_time'] = datetime.datetime.now(datetime.timezone.utc).isoformat()
            execution['status'] = TaskStatus.FAILED.value
            execution['error'] = str(e)
            
            logger.error(f"Failed to execute scheduled task {task_id}: {e}")
        
        return execution
    
    def _calculate_next_run(self, timestamp: str, interval: ScheduleInterval) -> str:
        """
        Calculate the next run time based on the interval.
        
        Args:
            timestamp: The timestamp from which to calculate the next run
            interval: The interval at which to run the task
            
        Returns:
            The next run time in ISO format
        """
        # Parse the timestamp
        dt = datetime.datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        
        # Calculate the next run time based on the interval
        if interval == ScheduleInterval.HOURLY:
            next_run = dt + datetime.timedelta(hours=1)
        elif interval == ScheduleInterval.DAILY:
            next_run = dt + datetime.timedelta(days=1)
        elif interval == ScheduleInterval.WEEKLY:
            next_run = dt + datetime.timedelta(weeks=1)
        elif interval == ScheduleInterval.MONTHLY:
            # Add one month (approximately)
            if dt.month == 12:
                next_run = dt.replace(year=dt.year + 1, month=1)
            else:
                next_run = dt.replace(month=dt.month + 1)
        elif interval == ScheduleInterval.QUARTERLY:
            # Add three months
            month = dt.month
            year = dt.year
            month += 3
            if month > 12:
                month -= 12
                year += 1
            next_run = dt.replace(year=year, month=month)
        elif interval == ScheduleInterval.YEARLY:
            next_run = dt.replace(year=dt.year + 1)
        else:
            # Default to daily
            next_run = dt + datetime.timedelta(days=1)
        
        # Return the next run time in ISO format
        return next_run.isoformat()
    
    def get_task_history(self, task_id: str) -> List[Dict[str, Any]]:
        """
        Get the execution history for a task.
        
        Args:
            task_id: The ID of the task
            
        Returns:
            List of task executions
            
        Raises:
            ValueError: If the task does not exist
        """
        # Check if the task exists
        if task_id not in self.scheduled_tasks:
            raise ValueError(f"Task not found: {task_id}")
        
        return self.task_history.get(task_id, [])
