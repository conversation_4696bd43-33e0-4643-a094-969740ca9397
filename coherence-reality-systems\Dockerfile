# Use Node.js LTS (Long Term Support) as base image with build tools
FROM node:18-alpine AS builder

# Install build dependencies as virtual package
RUN apk add --no-cache --virtual .build-deps \
    python3 \
    make \
    g++ \
    curl \
    wget

# Set working directory
WORKDIR /app

# Copy package files for dependency installation
COPY package-kethernet.json package.json
COPY package-lock.json* ./

# Install production dependencies
RUN npm ci --only=production --no-optional --prefer-offline --no-audit --progress=false

# Remove build dependencies to reduce image size
RUN apk del .build-deps

# Production stage
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy installed dependencies from builder
COPY --from=builder /app/node_modules ./node_modules

# Copy application files
COPY kether-server-enhanced.js .

# Create necessary directories and set permissions
RUN mkdir -p /app/prometheus /app/logs && \
    chown -R node:node /app

# Switch to non-root user
USER node

# Expose the application port
EXPOSE 8080

# Health check with curl
HEALTHCHECK --interval=30s --timeout=3s \
  CMD curl -f http://localhost:8080/health || exit 1

# Start the application
CMD ["node", "--max-old-space-size=1024", "kether-server-enhanced.js"]
