import React from 'react';
import Sidebar from '../components/Sidebar';
import Link from 'next/link';

export default function GamificationAPIs() {
  const sidebarItems = [
    { type: 'category', label: 'Gamification APIs', items: [
      { label: 'Overview', href: '#overview' },
      { label: 'Core APIs', href: '#core-apis' },
      { label: 'Advanced APIs', href: '#advanced-apis' },
      { label: 'Integration Examples', href: '#integration-examples' },
      { label: 'Partners', href: '#partners' }
    ]},
    { type: 'category', label: 'Core APIs', items: [
      { label: 'Achievement API', href: '#achievement-api' },
      { label: 'Points & Leaderboard API', href: '#points-api' },
      { label: 'Challenges API', href: '#challenges-api' },
      { label: 'Progress Tracking API', href: '#progress-api' }
    ]},
    { type: 'category', label: 'Advanced APIs', items: [
      { label: 'Team Competition API', href: '#team-api' },
      { label: 'Reward Marketplace API', href: '#reward-api' },
      { label: 'Compliance Quest API', href: '#quest-api' },
      { label: 'Compliance Insights API', href: '#insights-api' }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'API Documentation', href: '/api-docs' },
      { label: 'Partner Ecosystem', href: '/partner-ecosystem' },
      { label: 'Simple Version', href: '/gamification-apis-simple' }
    ]}
  ];

  return (
    <div className="flex flex-col md:flex-row gap-8">
      {/* Sidebar - Hidden on mobile, shown on desktop */}
      <div className="hidden md:block md:w-1/4 lg:w-1/5">
        <div className="sticky top-4">
          <Sidebar items={sidebarItems} title="Gamification APIs" />
        </div>
      </div>

      {/* Mobile Sidebar Toggle - Shown on mobile only */}
      <div className="md:hidden mb-4">
        <select 
          className="w-full bg-secondary text-white border border-gray-700 rounded p-2"
          onChange={(e) => {
            if (e.target.value) window.location.href = e.target.value;
          }}
          defaultValue=""
        >
          <option value="" disabled>Navigate to...</option>
          {sidebarItems.map((item, index) => (
            item.type === 'category' ? (
              <optgroup key={index} label={item.label}>
                {item.items.map((subItem, subIndex) => (
                  <option key={`${index}-${subIndex}`} value={subItem.href}>
                    {subItem.label}
                  </option>
                ))}
              </optgroup>
            ) : (
              <option key={index} value={item.href}>
                {item.label}
              </option>
            )
          ))}
        </select>
      </div>

      {/* Main Content */}
      <div className="w-full md:w-3/4 lg:w-4/5">
        {/* Hero Section */}
        <div id="overview" className="bg-secondary p-8 rounded-lg mb-8 relative overflow-hidden">
          <div className="relative z-10">
            <h2 className="text-3xl font-bold mb-4">Transform Compliance into Engagement</h2>
            <p className="text-xl mb-6 max-w-3xl">
              NovaFuse Gamification APIs turn compliance activities into engaging experiences that drive user adoption and create a culture of cyber-safety.
            </p>
            <a href="#core-apis" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
              Explore Gamification APIs
            </a>
          </div>
          <div className="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-l from-purple-500 to-transparent opacity-10"></div>
        </div>
        
        <div className="mb-12">
          <h3 id="core-apis" className="text-2xl font-bold mb-6">Core Gamification APIs</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
            {/* Achievement API */}
            <div id="achievement-api" className="bg-secondary p-6 rounded-lg border border-gray-700 hover:border-blue-500 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
              <h4 className="text-xl font-semibold mb-2">Achievement API</h4>
              <p className="text-gray-300 mb-4">Create, award, and track achievements for compliance activities. Define custom achievement criteria, badges, and rewards.</p>
              
              <div className="bg-[#0f172a] p-4 rounded-lg overflow-x-auto font-mono mb-4 text-sm">
                <pre className="text-green-400">
{`// Create a new achievement
POST /api/gamification/achievements
{
  "name": "Security Champion",
  "description": "Complete all security awareness training modules",
  "points": 500,
  "badge_url": "https://api.novafuse.io/badges/security-champion.png",
  "criteria": {
    "training_modules_completed": ["mod-001", "mod-002", "mod-003"]
  }
}`}
                </pre>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-400">View Documentation</span>
                <span className="bg-green-900 text-green-200 text-xs px-2 py-1 rounded">Available</span>
              </div>
            </div>
            
            {/* Points & Leaderboard API */}
            <div id="points-api" className="bg-secondary p-6 rounded-lg border border-gray-700 hover:border-blue-500 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
              <h4 className="text-xl font-semibold mb-2">Points & Leaderboard API</h4>
              <p className="text-gray-300 mb-4">Award points for compliance activities and create leaderboards to drive friendly competition and engagement.</p>
              
              <div className="bg-[#0f172a] p-4 rounded-lg overflow-x-auto font-mono mb-4 text-sm">
                <pre className="text-green-400">
{`// Award points to a user
POST /api/gamification/points
{
  "user_id": "user-123",
  "points": 100,
  "reason": "Completed quarterly security review",
  "category": "security"
}

// Get leaderboard
GET /api/gamification/leaderboards/security?timeframe=monthly`}
                </pre>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-400">View Documentation</span>
                <span className="bg-green-900 text-green-200 text-xs px-2 py-1 rounded">Available</span>
              </div>
            </div>
            
            {/* Challenges API */}
            <div id="challenges-api" className="bg-secondary p-6 rounded-lg border border-gray-700 hover:border-blue-500 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
              <h4 className="text-xl font-semibold mb-2">Challenges API</h4>
              <p className="text-gray-300 mb-4">Create time-limited challenges that encourage users to complete specific compliance activities within a deadline.</p>
              
              <div className="bg-[#0f172a] p-4 rounded-lg overflow-x-auto font-mono mb-4 text-sm">
                <pre className="text-green-400">
{`// Create a new challenge
POST /api/gamification/challenges
{
  "name": "Security Sprint",
  "description": "Complete all security tasks within 7 days",
  "start_date": "2025-06-01T00:00:00Z",
  "end_date": "2025-06-07T23:59:59Z",
  "reward_points": 1000,
  "tasks": [
    {"id": "task-001", "name": "Update password", "points": 200},
    {"id": "task-002", "name": "Enable 2FA", "points": 300},
    {"id": "task-003", "name": "Complete security quiz", "points": 500}
  ]
}`}
                </pre>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-400">View Documentation</span>
                <span className="bg-green-900 text-green-200 text-xs px-2 py-1 rounded">Available</span>
              </div>
            </div>
            
            {/* Progress Tracking API */}
            <div id="progress-api" className="bg-secondary p-6 rounded-lg border border-gray-700 hover:border-blue-500 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
              <h4 className="text-xl font-semibold mb-2">Progress Tracking API</h4>
              <p className="text-gray-300 mb-4">Track and visualize user progress through compliance journeys with customizable milestones and progress indicators.</p>
              
              <div className="bg-[#0f172a] p-4 rounded-lg overflow-x-auto font-mono mb-4 text-sm">
                <pre className="text-green-400">
{`// Create a compliance journey
POST /api/gamification/journeys
{
  "name": "NIST Compliance Path",
  "description": "Complete all steps to achieve NIST compliance",
  "milestones": [
    {"id": "ms-001", "name": "Identify", "order": 1, "points": 500},
    {"id": "ms-002", "name": "Protect", "order": 2, "points": 500},
    {"id": "ms-003", "name": "Detect", "order": 3, "points": 500},
    {"id": "ms-004", "name": "Respond", "order": 4, "points": 500},
    {"id": "ms-005", "name": "Recover", "order": 5, "points": 500}
  ]
}`}
                </pre>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-400">View Documentation</span>
                <span className="bg-green-900 text-green-200 text-xs px-2 py-1 rounded">Available</span>
              </div>
            </div>
          </div>
          
          <h3 id="advanced-apis" className="text-2xl font-bold mb-6">Advanced Gamification APIs</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Team Competition API */}
            <div id="team-api" className="bg-secondary p-6 rounded-lg border border-gray-700 hover:border-blue-500 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
              <h4 className="text-xl font-semibold mb-2">Team Competition API</h4>
              <p className="text-gray-300 mb-4">Create team-based competitions that encourage departments to collaborate on compliance activities.</p>
              
              <div className="bg-[#0f172a] p-4 rounded-lg overflow-x-auto font-mono mb-4 text-sm">
                <pre className="text-green-400">
{`// Create a team competition
POST /api/gamification/competitions
{
  "name": "Security Champions League",
  "description": "Department vs Department security challenge",
  "start_date": "2025-07-01T00:00:00Z",
  "end_date": "2025-07-31T23:59:59Z",
  "teams": [
    {"id": "team-001", "name": "Engineering"},
    {"id": "team-002", "name": "Marketing"},
    {"id": "team-003", "name": "Finance"}
  ],
  "reward_points": 5000
}`}
                </pre>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-400">View Documentation</span>
                <span className="bg-green-900 text-green-200 text-xs px-2 py-1 rounded">Available</span>
              </div>
            </div>
            
            {/* Reward Marketplace API */}
            <div id="reward-api" className="bg-secondary p-6 rounded-lg border border-gray-700 hover:border-blue-500 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
              <h4 className="text-xl font-semibold mb-2">Reward Marketplace API</h4>
              <p className="text-gray-300 mb-4">Create a marketplace where users can redeem points for rewards, driving long-term engagement with compliance activities.</p>
              
              <div className="bg-[#0f172a] p-4 rounded-lg overflow-x-auto font-mono mb-4 text-sm">
                <pre className="text-green-400">
{`// Create a reward
POST /api/gamification/rewards
{
  "name": "Coffee Gift Card",
  "description": "A $25 gift card for your favorite coffee shop",
  "points_cost": 2500,
  "image_url": "https://api.novafuse.io/rewards/coffee-card.png",
  "quantity_available": 100,
  "redemption_type": "digital_code"
}`}
                </pre>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-400">View Documentation</span>
                <span className="bg-green-900 text-green-200 text-xs px-2 py-1 rounded">Available</span>
              </div>
            </div>
            
            {/* Compliance Quest API */}
            <div id="quest-api" className="bg-secondary p-6 rounded-lg border border-gray-700 hover:border-blue-500 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
              <h4 className="text-xl font-semibold mb-2">Compliance Quest API</h4>
              <p className="text-gray-300 mb-4">Create narrative-driven quests that guide users through compliance activities with storylines and characters.</p>
              
              <div className="bg-[#0f172a] p-4 rounded-lg overflow-x-auto font-mono mb-4 text-sm">
                <pre className="text-green-400">
{`// Create a compliance quest
POST /api/gamification/quests
{
  "name": "Cyber Defender's Journey",
  "description": "Protect your organization from cyber threats",
  "chapters": [
    {
      "id": "ch-001",
      "title": "The Phishing Threat",
      "description": "Learn to identify and report phishing attempts",
      "tasks": [
        {"id": "task-001", "name": "Complete phishing training", "points": 200},
        {"id": "task-002", "name": "Report test phishing email", "points": 300}
      ]
    },
    {
      "id": "ch-002",
      "title": "Password Protector",
      "description": "Master the art of secure passwords",
      "tasks": [
        {"id": "task-003", "name": "Update weak passwords", "points": 200},
        {"id": "task-004", "name": "Enable password manager", "points": 300}
      ]
    }
  ]
}`}
                </pre>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-400">View Documentation</span>
                <span className="bg-green-900 text-green-200 text-xs px-2 py-1 rounded">Available</span>
              </div>
            </div>
            
            {/* Compliance Insights API */}
            <div id="insights-api" className="bg-secondary p-6 rounded-lg border border-gray-700 hover:border-blue-500 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
              <h4 className="text-xl font-semibold mb-2">Compliance Insights API</h4>
              <p className="text-gray-300 mb-4">Generate insights and recommendations based on gamification data to improve compliance programs and user engagement.</p>
              
              <div className="bg-[#0f172a] p-4 rounded-lg overflow-x-auto font-mono mb-4 text-sm">
                <pre className="text-green-400">
{`// Get engagement insights
GET /api/gamification/insights/engagement

// Response
{
  "overall_engagement_score": 78,
  "most_engaging_activities": [
    {"id": "act-001", "name": "Security quiz", "engagement_score": 92},
    {"id": "act-002", "name": "Phishing simulation", "engagement_score": 87}
  ],
  "least_engaging_activities": [
    {"id": "act-003", "name": "Policy review", "engagement_score": 45}
  ],
  "recommendations": [
    "Add interactive elements to policy review activities",
    "Increase points for completing less engaging activities"
  ]
}`}
                </pre>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-400">View Documentation</span>
                <span className="bg-green-900 text-green-200 text-xs px-2 py-1 rounded">Available</span>
              </div>
            </div>
          </div>
        </div>
        
        {/* Integration Examples */}
        <div id="integration-examples" className="bg-secondary p-6 rounded-lg mb-12">
          <h3 className="text-2xl font-bold mb-6 text-center">Integration Examples</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-4 border border-blue-600 rounded-lg">
              <h4 className="text-lg font-semibold mb-2">Zapier + Gamification</h4>
              <p className="text-gray-300 mb-4">Automatically award points and achievements when users complete compliance tasks in other systems.</p>
              <div className="bg-[#0f172a] p-4 rounded-lg overflow-x-auto font-mono text-sm">
                <pre className="text-green-400">
{`// Zapier Trigger: When a user completes security training in LMS
// Zapier Action: Award points via NovaFuse API
POST /api/gamification/points
{
  "user_id": "{{user_id}}",
  "points": 100,
  "reason": "Completed security training: {{training_name}}",
  "category": "security"
}`}
                </pre>
              </div>
            </div>
            
            <div className="p-4 border border-blue-600 rounded-lg">
              <h4 className="text-lg font-semibold mb-2">Slack + Gamification</h4>
              <p className="text-gray-300 mb-4">Send achievement notifications and leaderboard updates to Slack channels to drive visibility and engagement.</p>
              <div className="bg-[#0f172a] p-4 rounded-lg overflow-x-auto font-mono text-sm">
                <pre className="text-green-400">
{`// Webhook: When a user earns an achievement
// Slack Message:
{
  "blocks": [
    {
      "type": "header",
      "text": {
        "type": "plain_text",
        "text": "🏆 Achievement Unlocked!"
      }
    },
    {
      "type": "section",
      "text": {
        "type": "mrkdwn",
        "text": "*{{user_name}}* just earned the *{{achievement_name}}* badge!"
      }
    }
  ]
}`}
                </pre>
              </div>
            </div>
            
            <div className="p-4 border border-blue-600 rounded-lg">
              <h4 className="text-lg font-semibold mb-2">Microsoft Teams + Gamification</h4>
              <p className="text-gray-300 mb-4">Create compliance challenges and quests directly within Microsoft Teams to drive engagement.</p>
              <div className="bg-[#0f172a] p-4 rounded-lg overflow-x-auto font-mono text-sm">
                <pre className="text-green-400">
{`// Teams Bot Command: /compliance-challenge
// NovaFuse API Call:
POST /api/gamification/challenges
{
  "name": "Weekly Security Challenge",
  "description": "Complete these security tasks this week",
  "start_date": "{{today}}",
  "end_date": "{{today+7days}}",
  "team_id": "{{teams_channel_id}}",
  "tasks": [
    {"name": "Update password", "points": 200},
    {"name": "Enable 2FA", "points": 300}
  ]
}`}
                </pre>
              </div>
            </div>
          </div>
        </div>
        
        {/* Partner Showcase */}
        <div id="partners" className="bg-secondary p-6 rounded-lg">
          <h3 className="text-2xl font-bold mb-6 text-center">Gamification Partners</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="p-4 border border-gray-700 rounded-lg text-center">
              <div className="h-12 flex items-center justify-center mb-2">
                <div className="text-xl font-bold text-blue-500">Badgr</div>
              </div>
              <p className="text-gray-300 text-sm">Digital badge platform for achievements and certifications</p>
            </div>
            
            <div className="p-4 border border-gray-700 rounded-lg text-center">
              <div className="h-12 flex items-center justify-center mb-2">
                <div className="text-xl font-bold text-green-500">Bunchball</div>
              </div>
              <p className="text-gray-300 text-sm">Enterprise gamification platform for employee engagement</p>
            </div>
            
            <div className="p-4 border border-gray-700 rounded-lg text-center">
              <div className="h-12 flex items-center justify-center mb-2">
                <div className="text-xl font-bold text-purple-500">Habitica</div>
              </div>
              <p className="text-gray-300 text-sm">Habit-building and productivity gamification platform</p>
            </div>
            
            <div className="p-4 border border-gray-700 rounded-lg text-center">
              <div className="h-12 flex items-center justify-center mb-2">
                <div className="text-xl font-bold text-yellow-500">Centrical</div>
              </div>
              <p className="text-gray-300 text-sm">Employee performance gamification platform</p>
            </div>
          </div>
          
          <div className="text-center mt-8">
            <Link href="/partner-ecosystem" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
              View All Partners
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
