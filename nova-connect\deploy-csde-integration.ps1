# Deployment script for NovaConnect with CSDE integration

# Configuration
$NOVA_CONNECT_DIR = Get-Location
$CSDE_DIR = "..\src\csde"
$ENV_FILE = ".env"

# Print header
Write-Host "=========================================" -ForegroundColor Green
Write-Host "NovaConnect + CSDE Integration Deployment" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Check if Docker is running
Write-Host "`nChecking if Docker is running..." -ForegroundColor Yellow
try {
    docker info | Out-Null
    Write-Host "Docker is running." -ForegroundColor Green
} catch {
    Write-Host "Docker is not running. Please start Docker and try again." -ForegroundColor Red
    exit 1
}

# Check if CSDE directory exists
Write-Host "`nChecking if CSDE directory exists..." -ForegroundColor Yellow
if (-not (Test-Path $CSDE_DIR)) {
    Write-Host "CSDE directory not found at $CSDE_DIR. Please check the path and try again." -ForegroundColor Red
    exit 1
}
Write-Host "CSDE directory found at $CSDE_DIR." -ForegroundColor Green

# Create .env file if it doesn't exist
Write-Host "`nCreating .env file..." -ForegroundColor Yellow
if (-not (Test-Path $ENV_FILE)) {
    @"
NODE_ENV=production
PORT=3001
CSDE_API_URL=http://csde-api:3010
LOG_LEVEL=info
METRICS_ENABLED=true
TRACING_ENABLED=true
"@ | Out-File -FilePath $ENV_FILE -Encoding utf8
    Write-Host ".env file created." -ForegroundColor Green
} else {
    # Check if CSDE_API_URL is in .env file
    $envContent = Get-Content $ENV_FILE
    if (-not ($envContent -match "CSDE_API_URL")) {
        Add-Content -Path $ENV_FILE -Value "CSDE_API_URL=http://csde-api:3010"
        Write-Host "Added CSDE_API_URL to .env file." -ForegroundColor Green
    } else {
        Write-Host ".env file already exists and contains CSDE_API_URL." -ForegroundColor Green
    }
}

# Create necessary directories
Write-Host "`nCreating necessary directories..." -ForegroundColor Yellow
New-Item -ItemType Directory -Path "logs" -Force | Out-Null
New-Item -ItemType Directory -Path "monitoring\prometheus" -Force | Out-Null
New-Item -ItemType Directory -Path "monitoring\grafana\provisioning\dashboards" -Force | Out-Null
Write-Host "Directories created." -ForegroundColor Green

# Build and start the containers
Write-Host "`nBuilding and starting containers..." -ForegroundColor Yellow
docker-compose build
docker-compose up -d
Write-Host "Containers built and started." -ForegroundColor Green

# Wait for services to start
Write-Host "`nWaiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check if services are running
Write-Host "`nChecking if services are running..." -ForegroundColor Yellow
$servicesRunning = docker-compose ps | Select-String "Up"
if (-not $servicesRunning) {
    Write-Host "Services failed to start. Please check the logs with 'docker-compose logs'." -ForegroundColor Red
    exit 1
}
Write-Host "Services are running." -ForegroundColor Green

# Print service URLs
Write-Host "`n=========================================" -ForegroundColor Green
Write-Host "Services are now available at:" -ForegroundColor Green
Write-Host "NovaConnect API: http://localhost:3001" -ForegroundColor Green
Write-Host "CSDE API: http://localhost:3010" -ForegroundColor Green
Write-Host "Grafana: http://localhost:3002" -ForegroundColor Green
Write-Host "Prometheus: http://localhost:9090" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Print next steps
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Access the NovaConnect API at http://localhost:3001"
Write-Host "2. Access the CSDE API at http://localhost:3010"
Write-Host "3. Access Grafana at http://localhost:3002 (username: admin, password: admin)"
Write-Host "4. Access Prometheus at http://localhost:9090"
Write-Host "5. View the CSDE Integration Dashboard in Grafana"
Write-Host "6. To stop the services, run 'docker-compose down'"
Write-Host "7. To view logs, run 'docker-compose logs -f'"

Write-Host "`nDeployment complete!" -ForegroundColor Green
