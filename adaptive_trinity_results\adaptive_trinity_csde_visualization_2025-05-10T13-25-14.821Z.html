
    <!DOCTYPE html>
    <html>
    <head>
      <title>Adaptive Trinity CSDE Optimization</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          background-color: #f5f5f5;
        }
        .container {
          max-width: 1200px;
          margin: 0 auto;
          background-color: white;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
          color: #333;
        }
        .chart {
          margin: 20px 0;
          height: 400px;
        }
        .results {
          margin-top: 30px;
          padding: 20px;
          background-color: #f0f0f0;
          border-radius: 8px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 20px;
        }
        th, td {
          padding: 10px;
          text-align: left;
          border-bottom: 1px solid #ddd;
        }
        th {
          background-color: #f2f2f2;
        }
        .improvement {
          font-size: 24px;
          font-weight: bold;
          color: #4CAF50;
          margin: 20px 0;
        }
      </style>
      <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    </head>
    <body>
      <div class="container">
        <h1>Adaptive Trinity CSDE Optimization Results</h1>
        
        <div class="chart">
          <canvas id="csdeChart"></canvas>
        </div>
        
        <div class="chart">
          <canvas id="ratioChart"></canvas>
        </div>
        
        <div class="chart">
          <canvas id="performanceChart"></canvas>
        </div>
        
        <div class="results">
          <h2>Optimization Summary</h2>
          <div class="improvement">
            Improvement: 0.00%
          </div>
          
          <h3>Initial vs Final Values</h3>
          <table>
            <tr>
              <th></th>
              <th>Initial (18/82)</th>
              <th>Final (Optimized)</th>
              <th>Change</th>
            </tr>
            <tr>
              <td>Trinity CSDE Value</td>
              <td>6.9121</td>
              <td>6.9121</td>
              <td>0.0000</td>
            </tr>
            <tr>
              <td>Father Ratio (α)</td>
              <td>0.1800</td>
              <td>0.1800</td>
              <td>0.0000</td>
            </tr>
            <tr>
              <td>Son Ratio (β)</td>
              <td>0.1800</td>
              <td>0.1800</td>
              <td>0.0000</td>
            </tr>
            <tr>
              <td>Spirit Ratio (γ)</td>
              <td>0.1800</td>
              <td>0.1800</td>
              <td>0.0000</td>
            </tr>
          </table>
        </div>
      </div>
      
      <script>
        // CSDE Value Chart
        const csdeCtx = document.getElementById('csdeChart').getContext('2d');
        new Chart(csdeCtx, {
          type: 'line',
          data: {
            labels: [0,1,2,3,4,5,6,7,8,9,10],
            datasets: [{
              label: 'Trinity CSDE Value',
              data: [6.912106220989875,6.912106220989875,6.912106220989875,6.912106220989875,6.912106220989875,6.912106220989875,6.912106220989875,6.912106220989875,6.912106220989875,6.912106220989875,6.912106220989875],
              borderColor: 'rgb(75, 192, 192)',
              tension: 0.1,
              fill: false
            }]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'Trinity CSDE Value Over Optimization Cycles'
              }
            },
            scales: {
              y: {
                beginAtZero: false
              }
            }
          }
        });
        
        // Ratio Chart
        const ratioCtx = document.getElementById('ratioChart').getContext('2d');
        new Chart(ratioCtx, {
          type: 'line',
          data: {
            labels: [0,1,2,3,4,5,6,7,8,9,10],
            datasets: [
              {
                label: 'Father Ratio (α)',
                data: [0.18,0.18,0.18,0.18,0.18,0.18,0.18,0.18,0.18,0.18,0.18],
                borderColor: 'rgb(255, 99, 132)',
                tension: 0.1,
                fill: false
              },
              {
                label: 'Son Ratio (β)',
                data: [0.18,0.18,0.18,0.18,0.18,0.18,0.18,0.18,0.18,0.18,0.18],
                borderColor: 'rgb(54, 162, 235)',
                tension: 0.1,
                fill: false
              },
              {
                label: 'Spirit Ratio (γ)',
                data: [0.18,0.18,0.18,0.18,0.18,0.18,0.18,0.18,0.18,0.18,0.18],
                borderColor: 'rgb(255, 206, 86)',
                tension: 0.1,
                fill: false
              }
            ]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'Adaptive Ratios Over Optimization Cycles'
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                max: 1
              }
            }
          }
        });
        
        // Performance Chart
        const perfCtx = document.getElementById('performanceChart').getContext('2d');
        new Chart(perfCtx, {
          type: 'line',
          data: {
            labels: [0,1,2,3,4,5,6,7,8,9,10],
            datasets: [{
              label: 'Performance Score',
              data: [0,0,6.912106220989875,6.912106220989875,6.912106220989875,6.912106220989875,6.912106220989875,6.912106220989875,6.912106220989875,6.912106220989875,6.912106220989875],
              borderColor: 'rgb(153, 102, 255)',
              tension: 0.1,
              fill: false
            }]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'Performance Score Over Optimization Cycles'
              }
            },
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
      </script>
    </body>
    </html>
  