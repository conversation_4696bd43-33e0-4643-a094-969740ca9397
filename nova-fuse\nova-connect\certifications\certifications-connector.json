{"extends": "base-connector", "name": "novafuse-certifications-connector", "version": "1.0.0", "description": "Certifications & Accreditation connector template for NovaFuse API Superstore", "category": "certifications", "base_url": "http://localhost:8000/certifications", "endpoints": [{"name": "certifications", "path": "/list", "method": "GET", "description": "Get a list of certifications", "parameters": [{"name": "type", "in": "query", "required": false, "description": "Filter by certification type"}, {"name": "status", "in": "query", "required": false, "description": "Filter by status (active, expired, in_progress)"}]}, {"name": "certification_details", "path": "/details/{id}", "method": "GET", "description": "Get details of a specific certification", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Certification ID"}]}, {"name": "certification_requirements", "path": "/requirements", "method": "GET", "description": "Get certification requirements", "parameters": [{"name": "certification_id", "in": "query", "required": false, "description": "Filter by certification ID"}, {"name": "status", "in": "query", "required": false, "description": "Filter by status (met, not_met, in_progress)"}]}, {"name": "accreditations", "path": "/accreditations", "method": "GET", "description": "Get a list of accreditations", "parameters": [{"name": "type", "in": "query", "required": false, "description": "Filter by accreditation type"}, {"name": "status", "in": "query", "required": false, "description": "Filter by status (active, expired, in_progress)"}]}, {"name": "accreditation_details", "path": "/accreditations/{id}", "method": "GET", "description": "Get details of a specific accreditation", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Accreditation ID"}]}, {"name": "audit_schedule", "path": "/audits/schedule", "method": "GET", "description": "Get certification audit schedule", "parameters": [{"name": "certification_id", "in": "query", "required": false, "description": "Filter by certification ID"}, {"name": "start_date", "in": "query", "required": false, "description": "Filter by start date (YYYY-MM-DD)"}, {"name": "end_date", "in": "query", "required": false, "description": "Filter by end date (YYYY-MM-DD)"}]}, {"name": "certification_status", "path": "/status/{id}", "method": "GET", "description": "Get certification status", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Certification ID"}]}, {"name": "certification_assessment", "path": "/assessment", "method": "POST", "description": "Perform a certification readiness assessment", "parameters": []}]}