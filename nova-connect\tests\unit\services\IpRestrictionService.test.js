/**
 * IP Restriction Service Tests
 */

const IpRestrictionService = require('../../../api/services/IpRestrictionService');
const fs = require('fs').promises;
const path = require('path');

// Mock fs.promises
jest.mock('fs', () => ({
  promises: {
    mkdir: jest.fn().mockResolvedValue(undefined),
    readFile: jest.fn(),
    writeFile: jest.fn().mockResolvedValue(undefined)
  }
}));

// Mock ip-range-check
jest.mock('ip-range-check', () => jest.fn());
const ipRangeCheck = require('ip-range-check');

describe('IpRestrictionService', () => {
  let ipRestrictionService;
  const testDataDir = path.join(__dirname, 'test-data');
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create a new instance for each test
    ipRestrictionService = new IpRestrictionService(testDataDir);
    
    // Reset ip-range-check mock
    ipRangeCheck.mockReset();
  });
  
  describe('constructor', () => {
    it('should initialize with the correct data directory', () => {
      expect(ipRestrictionService.dataDir).toBe(testDataDir);
      expect(ipRestrictionService.ipRestrictionsFile).toBe(path.join(testDataDir, 'ip_restrictions.json'));
    });
    
    it('should call ensureDataDir', () => {
      expect(fs.mkdir).toHaveBeenCalledWith(testDataDir, { recursive: true });
    });
    
    it('should initialize with default config', () => {
      expect(ipRestrictionService.defaultConfig).toEqual({
        enabled: false,
        mode: 'allowlist',
        allowlist: [],
        blocklist: [],
        rules: []
      });
    });
  });
  
  describe('loadRestrictions', () => {
    it('should load restrictions from file', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'allowlist',
        allowlist: ['***********'],
        blocklist: [],
        rules: []
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      
      const restrictions = await ipRestrictionService.loadRestrictions();
      
      expect(fs.readFile).toHaveBeenCalledWith(ipRestrictionService.ipRestrictionsFile, 'utf8');
      expect(restrictions).toEqual(mockRestrictions);
    });
    
    it('should return default config if file does not exist', async () => {
      const error = new Error('File not found');
      error.code = 'ENOENT';
      fs.readFile.mockRejectedValueOnce(error);
      
      const restrictions = await ipRestrictionService.loadRestrictions();
      
      expect(restrictions).toEqual(ipRestrictionService.defaultConfig);
    });
    
    it('should throw error if file read fails for other reasons', async () => {
      const error = new Error('Permission denied');
      fs.readFile.mockRejectedValueOnce(error);
      
      await expect(ipRestrictionService.loadRestrictions()).rejects.toThrow('Permission denied');
    });
  });
  
  describe('saveRestrictions', () => {
    it('should save restrictions to file', async () => {
      const restrictions = {
        enabled: true,
        mode: 'allowlist',
        allowlist: ['***********'],
        blocklist: [],
        rules: []
      };
      
      await ipRestrictionService.saveRestrictions(restrictions);
      
      expect(fs.writeFile).toHaveBeenCalledWith(
        ipRestrictionService.ipRestrictionsFile,
        JSON.stringify(restrictions, null, 2)
      );
    });
    
    it('should throw error if file write fails', async () => {
      const error = new Error('Permission denied');
      fs.writeFile.mockRejectedValueOnce(error);
      
      await expect(ipRestrictionService.saveRestrictions({})).rejects.toThrow('Permission denied');
    });
  });
  
  describe('isAllowed', () => {
    it('should allow all IPs if restrictions are disabled', async () => {
      const mockRestrictions = {
        enabled: false
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      
      const result = await ipRestrictionService.isAllowed('***********');
      
      expect(result).toBe(true);
    });
    
    it('should check rules first', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'allowlist',
        allowlist: [],
        blocklist: [],
        rules: [
          {
            name: 'Allow Office',
            action: 'allow',
            ipRange: '***********/16'
          }
        ]
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      ipRangeCheck.mockReturnValueOnce(true); // IP matches rule
      
      const result = await ipRestrictionService.isAllowed('***********');
      
      expect(ipRangeCheck).toHaveBeenCalledWith('***********', '***********/16');
      expect(result).toBe(true);
    });
    
    it('should check allowlist in allowlist mode', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'allowlist',
        allowlist: ['***********'],
        blocklist: [],
        rules: []
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      
      const result = await ipRestrictionService.isAllowed('***********');
      
      expect(result).toBe(true);
    });
    
    it('should deny IP not in allowlist in allowlist mode', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'allowlist',
        allowlist: ['***********'],
        blocklist: [],
        rules: []
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      
      const result = await ipRestrictionService.isAllowed('***********');
      
      expect(result).toBe(false);
    });
    
    it('should check blocklist in blocklist mode', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'blocklist',
        allowlist: [],
        blocklist: ['***********'],
        rules: []
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      
      const result = await ipRestrictionService.isAllowed('***********');
      
      expect(result).toBe(false);
    });
    
    it('should allow IP not in blocklist in blocklist mode', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'blocklist',
        allowlist: [],
        blocklist: ['***********'],
        rules: []
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      
      const result = await ipRestrictionService.isAllowed('***********');
      
      expect(result).toBe(true);
    });
  });
  
  describe('addToAllowlist', () => {
    it('should add IP to allowlist', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'allowlist',
        allowlist: [],
        blocklist: [],
        rules: []
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      
      // Mock isValidIpOrRange
      jest.spyOn(ipRestrictionService, 'isValidIpOrRange').mockReturnValueOnce(true);
      
      await ipRestrictionService.addToAllowlist('***********');
      
      expect(fs.writeFile).toHaveBeenCalled();
      const savedRestrictions = JSON.parse(fs.writeFile.mock.calls[0][1]);
      expect(savedRestrictions.allowlist).toContain('***********');
    });
    
    it('should remove IP from blocklist if added to allowlist', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'allowlist',
        allowlist: [],
        blocklist: ['***********'],
        rules: []
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      
      // Mock isValidIpOrRange
      jest.spyOn(ipRestrictionService, 'isValidIpOrRange').mockReturnValueOnce(true);
      
      await ipRestrictionService.addToAllowlist('***********');
      
      expect(fs.writeFile).toHaveBeenCalled();
      const savedRestrictions = JSON.parse(fs.writeFile.mock.calls[0][1]);
      expect(savedRestrictions.allowlist).toContain('***********');
      expect(savedRestrictions.blocklist).not.toContain('***********');
    });
  });
});
