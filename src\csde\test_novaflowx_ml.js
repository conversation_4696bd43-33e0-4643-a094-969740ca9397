/**
 * Test NovaFlowX ML Engine
 * 
 * This script tests the NovaFlowX ML Engine integration.
 */

const fs = require('fs');
const path = require('path');
const NovaFlowXMLEngine = require('./novaflowx_ml_engine');

// Configuration
const config = {
  outputDir: path.join(__dirname, 'ml', 'data'),
  dryRun: true // Use dry run for testing
};

// Create output directory if it doesn't exist
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Initialize NovaFlowX ML Engine
const novaFlowXML = new NovaFlowXMLEngine({
  confidenceThreshold: 0.7,
  automationLevels: ['high', 'medium'],
  priorityLevels: ['critical', 'high', 'medium'],
  dryRun: config.dryRun
});

// Create test input
const testInput = {
  complianceData: {
    complianceScore: 0.65,
    controls: [
      {
        id: 'NIST-AC-2',
        name: 'Account Management',
        description: 'The organization needs to implement account management procedures',
        severity: 'high',
        status: 'non-compliant',
        framework: 'NIST 800-53'
      },
      {
        id: 'NIST-CM-7',
        name: 'Least Functionality',
        description: 'The organization needs to configure systems to provide only essential capabilities',
        severity: 'medium',
        status: 'partial',
        framework: 'NIST 800-53'
      },
      {
        id: 'NIST-SC-7',
        name: 'Boundary Protection',
        description: 'The organization needs to implement boundary protection mechanisms',
        severity: 'high',
        status: 'compliant',
        framework: 'NIST 800-53'
      }
    ]
  },
  gcpData: {
    integrationScore: 0.75,
    services: [
      {
        id: 'GCP-IAM-1',
        name: 'IAM Role Configuration',
        description: 'IAM roles need to be configured with least privilege',
        severity: 'high',
        status: 'non-optimal',
        service: 'Cloud IAM'
      },
      {
        id: 'GCP-VPC-1',
        name: 'VPC Network Security',
        description: 'VPC network security needs to be enhanced',
        severity: 'medium',
        status: 'partial',
        service: 'VPC Network'
      },
      {
        id: 'GCP-KMS-1',
        name: 'Key Management',
        description: 'Cloud KMS keys need to be properly managed',
        severity: 'high',
        status: 'optimal',
        service: 'Cloud KMS'
      }
    ]
  },
  cyberSafetyData: {
    safetyScore: 0.55,
    controls: [
      {
        id: 'CS-P3-1',
        name: 'Self-Destructing Compliance Servers',
        description: 'Implement self-destructing compliance servers with hardware-enforced geo-fencing',
        severity: 'high',
        status: 'not-implemented',
        pillar: 'Pillar 3'
      },
      {
        id: 'CS-P9-1',
        name: 'Post-Quantum Immutable Compliance Journal',
        description: 'Implement post-quantum immutable compliance journal',
        severity: 'medium',
        status: 'partial',
        pillar: 'Pillar 9'
      },
      {
        id: 'CS-P12-1',
        name: 'C-Suite Directive to Code Compiler',
        description: 'Implement C-Suite Directive to Code Compiler',
        severity: 'medium',
        status: 'implemented',
        pillar: 'Pillar 12'
      }
    ]
  }
};

// Run NovaFlowX ML Engine
async function runTest() {
  try {
    console.log('Running NovaFlowX ML Engine test...');
    
    // Analyze and remediate
    const result = await novaFlowXML.analyzeAndRemediate(
      testInput.complianceData,
      testInput.gcpData,
      testInput.cyberSafetyData
    );
    
    // Save result
    fs.writeFileSync(
      path.join(config.outputDir, 'novaflowx_ml_result.json'),
      JSON.stringify(result, null, 2)
    );
    
    console.log(`Result saved to ${path.join(config.outputDir, 'novaflowx_ml_result.json')}`);
    
    // Display key metrics
    console.log('\nAnalysis Results:');
    console.log(`CSDE Value: ${result.analysis.csdeValue.toFixed(2)}`);
    console.log(`Performance Factor: ${result.analysis.performanceFactor.toFixed(2)}x`);
    
    console.log('\nRemediation Results:');
    console.log(`Actions Selected: ${result.remediation.actionsSelected}`);
    console.log(`Actions Remediated: ${result.remediation.actionsRemediated}`);
    console.log(`Actions Failed: ${result.remediation.actionsFailed}`);
    
    // Display remediation details
    console.log('\nRemediation Details:');
    result.remediation.results.forEach((remediation, index) => {
      console.log(`\n${index + 1}. ${remediation.action.title} (${remediation.action.priority.toUpperCase()})`);
      console.log(`   Success: ${remediation.success}`);
      console.log(`   Dry Run: ${remediation.dryRun}`);
      console.log(`   Message: ${remediation.message}`);
      
      if (remediation.remediationPlan) {
        console.log('   Remediation Plan:');
        console.log(`     Type: ${remediation.remediationPlan.type}`);
        console.log('     Steps:');
        remediation.remediationPlan.steps.forEach((step, stepIndex) => {
          console.log(`       ${stepIndex + 1}. ${step}`);
        });
      }
    });
    
    // Get remediation statistics
    const statistics = novaFlowXML.getRemediationStatistics();
    
    console.log('\nRemediation Statistics:');
    console.log(`Total Remediations: ${statistics.total}`);
    console.log(`Successful Remediations: ${statistics.successful}`);
    console.log(`Failed Remediations: ${statistics.failed}`);
    console.log(`Success Rate: ${(statistics.successRate * 100).toFixed(2)}%`);
    
    // Generate HTML report
    generateHtmlReport(result, statistics);
    
    console.log('\nDone!');
  } catch (error) {
    console.error('Error running test:', error);
  }
}

// Generate HTML report
function generateHtmlReport(result, statistics) {
  const reportHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFlowX ML Engine Results</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f8f9fa;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .chart-container {
      width: 100%;
      margin: 20px auto;
      height: 400px;
    }
    h1, h2, h3 {
      color: #0a84ff;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    .metrics {
      width: 100%;
      border-collapse: collapse;
    }
    .metrics th, .metrics td {
      border: 1px solid #ddd;
      padding: 12px;
      text-align: left;
    }
    .metrics th {
      background-color: #f2f2f2;
    }
    .metrics tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-weight: bold;
    }
    .status-success {
      background-color: #d4edda;
      color: #155724;
    }
    .status-failure {
      background-color: #f8d7da;
      color: #721c24;
    }
    .status-dryrun {
      background-color: #fff3cd;
      color: #856404;
    }
    .priority-critical {
      color: #dc3545;
    }
    .priority-high {
      color: #fd7e14;
    }
    .priority-medium {
      color: #ffc107;
    }
    .priority-low {
      color: #28a745;
    }
    .stats-card {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .stat-item {
      flex: 1;
      min-width: 200px;
      text-align: center;
      padding: 20px;
      margin: 10px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .stat-value {
      font-size: 36px;
      font-weight: bold;
      margin: 10px 0;
      color: #0a84ff;
    }
    .stat-label {
      font-size: 14px;
      color: #6c757d;
    }
    .remediation-item {
      margin-bottom: 15px;
      padding: 15px;
      border-left: 4px solid #0a84ff;
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .remediation-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    .remediation-details {
      margin-left: 20px;
      font-size: 14px;
    }
    .step-item {
      margin: 5px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>NovaFlowX ML Engine Results</h1>
    
    <div class="card">
      <h2>Analysis Summary</h2>
      <div class="stats-card">
        <div class="stat-item">
          <div class="stat-label">CSDE Value</div>
          <div class="stat-value">${result.analysis.csdeValue.toFixed(2)}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">Performance Factor</div>
          <div class="stat-value">${result.analysis.performanceFactor.toFixed(2)}x</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">ML Enhanced</div>
          <div class="stat-value">${result.analysis.mlEnhanced ? 'Yes' : 'No'}</div>
        </div>
      </div>
    </div>
    
    <div class="card">
      <h2>Remediation Summary</h2>
      <div class="stats-card">
        <div class="stat-item">
          <div class="stat-label">Actions Selected</div>
          <div class="stat-value">${result.remediation.actionsSelected}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">Actions Remediated</div>
          <div class="stat-value">${result.remediation.actionsRemediated}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">Success Rate</div>
          <div class="stat-value">${(result.remediation.actionsRemediated / result.remediation.actionsSelected * 100).toFixed(2)}%</div>
        </div>
      </div>
    </div>
    
    <div class="card">
      <h2>Remediation Statistics</h2>
      <div class="chart-container">
        <canvas id="remediationChart"></canvas>
      </div>
    </div>
    
    <div class="card">
      <h2>Remediation Details</h2>
      ${result.remediation.results.map((remediation, index) => `
        <div class="remediation-item">
          <div class="remediation-title ${remediation.action.priority ? `priority-${remediation.action.priority}` : ''}">
            ${index + 1}. ${remediation.action.title} (${remediation.action.priority.toUpperCase()})
            <span class="status-badge ${remediation.success ? 'status-success' : 'status-failure'} ${remediation.dryRun ? 'status-dryrun' : ''}">
              ${remediation.dryRun ? 'DRY RUN' : (remediation.success ? 'SUCCESS' : 'FAILED')}
            </span>
          </div>
          <div class="remediation-details">
            <p>${remediation.action.description}</p>
            <p><strong>Type:</strong> ${remediation.action.type}</p>
            <p><strong>Automation Potential:</strong> ${remediation.action.automationPotential}</p>
            <p><strong>Message:</strong> ${remediation.message}</p>
            ${remediation.remediationPlan ? `
              <p><strong>Remediation Plan:</strong></p>
              <ul>
                ${remediation.remediationPlan.steps.map((step, stepIndex) => `
                  <li class="step-item">${step}</li>
                `).join('')}
              </ul>
            ` : ''}
          </div>
        </div>
      `).join('')}
    </div>
    
    <div class="card">
      <h2>ML Insights</h2>
      <h3>Status Analysis</h3>
      <table class="metrics">
        <tr>
          <th>Domain</th>
          <th>Level</th>
          <th>Score</th>
          <th>Details</th>
        </tr>
        <tr>
          <td>Compliance</td>
          <td>${result.analysis.mlInsights.complianceStatus.level}</td>
          <td>${(result.analysis.mlInsights.complianceStatus.score * 100).toFixed(2)}%</td>
          <td>
            Compliant: ${result.analysis.mlInsights.complianceStatus.statusCounts.compliant}<br>
            Partial: ${result.analysis.mlInsights.complianceStatus.statusCounts.partial}<br>
            Non-Compliant: ${result.analysis.mlInsights.complianceStatus.statusCounts['non-compliant']}
          </td>
        </tr>
        <tr>
          <td>GCP</td>
          <td>${result.analysis.mlInsights.gcpStatus.level}</td>
          <td>${(result.analysis.mlInsights.gcpStatus.score * 100).toFixed(2)}%</td>
          <td>
            Optimal: ${result.analysis.mlInsights.gcpStatus.statusCounts.optimal}<br>
            Partial: ${result.analysis.mlInsights.gcpStatus.statusCounts.partial}<br>
            Non-Optimal: ${result.analysis.mlInsights.gcpStatus.statusCounts['non-optimal']}
          </td>
        </tr>
        <tr>
          <td>Cyber-Safety</td>
          <td>${result.analysis.mlInsights.cyberSafetyStatus.level}</td>
          <td>${(result.analysis.mlInsights.cyberSafetyStatus.score * 100).toFixed(2)}%</td>
          <td>
            Implemented: ${result.analysis.mlInsights.cyberSafetyStatus.statusCounts.implemented}<br>
            Partial: ${result.analysis.mlInsights.cyberSafetyStatus.statusCounts.partial}<br>
            Not Implemented: ${result.analysis.mlInsights.cyberSafetyStatus.statusCounts['not-implemented']}
          </td>
        </tr>
      </table>
      
      <h3>Improvement Areas</h3>
      <table class="metrics">
        <tr>
          <th>Area</th>
          <th>Priority</th>
          <th>Description</th>
          <th>Contribution</th>
        </tr>
        ${result.analysis.mlInsights.improvementAreas.map(area => `
          <tr>
            <td>${area.area}</td>
            <td class="priority-${area.priority}">${area.priority.toUpperCase()}</td>
            <td>${area.description}</td>
            <td>${area.contribution.toFixed(2)}%</td>
          </tr>
        `).join('')}
      </table>
    </div>
  </div>
  
  <script>
    // Remediation chart
    const remediationCtx = document.getElementById('remediationChart').getContext('2d');
    new Chart(remediationCtx, {
      type: 'bar',
      data: {
        labels: ['Total', 'Selected', 'Remediated', 'Failed'],
        datasets: [{
          label: 'Remediation Actions',
          data: [
            ${result.analysis.remediationActions.length},
            ${result.remediation.actionsSelected},
            ${result.remediation.actionsRemediated},
            ${result.remediation.actionsFailed}
          ],
          backgroundColor: [
            'rgba(75, 192, 192, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(255, 99, 132, 0.6)'
          ],
          borderColor: [
            'rgba(75, 192, 192, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(255, 99, 132, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Actions'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Remediation Status'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Remediation Actions Summary'
          }
        }
      }
    });
  </script>
</body>
</html>
`;

  fs.writeFileSync(
    path.join(config.outputDir, 'novaflowx_ml_report.html'),
    reportHtml
  );

  console.log(`Report saved to ${path.join(config.outputDir, 'novaflowx_ml_report.html')}`);
}

// Run the test
runTest();
