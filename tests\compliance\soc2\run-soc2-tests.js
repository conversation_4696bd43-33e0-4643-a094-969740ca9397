/**
 * Run SOC 2 Compliance Tests
 * 
 * This script runs the SOC 2 compliance tests and generates a report.
 */

const path = require('path');
const { createSoc2Framework } = require('./soc2-framework');
const { generateHtmlReport } = require('../framework/compliance-test-framework');

/**
 * Run the SOC 2 compliance tests
 */
async function runSoc2Tests() {
  console.log('Running SOC 2 Compliance Tests...');
  
  // Create the SOC 2 framework
  const framework = createSoc2Framework();
  
  // Run the tests
  const results = await framework.runTests();
  
  // Generate the report
  const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
  const reportPath = path.join(__dirname, '..', '..', '..', 'test-results', 'compliance', `soc2-report-${timestamp}.json`);
  await framework.saveReport(reportPath);
  
  // Generate the HTML report
  const htmlReportPath = path.join(__dirname, '..', '..', '..', 'test-results', 'compliance', `soc2-report-${timestamp}.html`);
  await generateHtmlReport(results, htmlReportPath);
  
  console.log('SOC 2 Compliance Tests completed.');
  console.log(`Report saved to: ${reportPath}`);
  console.log(`HTML Report saved to: ${htmlReportPath}`);
}

// Run the tests if this script is executed directly
if (require.main === module) {
  runSoc2Tests().catch(error => {
    console.error('Error running SOC 2 tests:', error);
    process.exit(1);
  });
}

module.exports = {
  runSoc2Tests
};
