import React, { createContext, useContext, useState, useCallback, useMemo } from 'react';
import { useNovaConnect } from '../context/NovaConnectContext';

export const QuantumFieldContext = createContext();

// Quantum state constants
const QUANTUM_STATES = {
  IDLE: 'idle',
  MEASURING: 'measuring',
  ENTANGLED: 'entangled',
  COLLAPSED: 'collapsed',
  ERROR: 'error'
};

export const QuantumFieldProvider = ({ children }) => {
  const { updateMetrics } = useNovaConnect();
  const [quantumState, setQuantumState] = useState(QUANTUM_STATES.IDLE);
  const [entropyHistory, setEntropyHistory] = useState([]);
  const [coherence, setCoherence] = useState(1.0);
  const [entanglement, setEntanglement] = useState({});
  const [debugInfo, setDebugInfo] = useState({
    lastMeasurement: null,
    quantumOperations: 0,
    classicalFallbacks: 0,
    averageEntropy: 0
  });

  // Measure quantum entropy for a given task
  const measureEntropy = useCallback(async (task) => {
    setQuantumState(QUANTUM_STATES.MEASURING);
    
    try {
      // Simulate quantum measurement
      const entropy = Math.random(); // Replace with actual quantum measurement
      const newEntanglement = {
        ...entanglement,
        [task.id]: {
          timestamp: Date.now(),
          value: entropy,
          correlation: Math.random()
        }
      };

      setEntanglement(newEntanglement);
      setEntropyHistory(prev => {
        const newHistory = [...prev, { ...task, entropy, timestamp: Date.now() }];
        // Keep only last 1000 measurements for performance
        return newHistory.slice(-1000);
      });

      // Update coherence based on measurement stability
      const newCoherence = Math.max(0.1, 1 - (Math.random() * 0.1));
      setCoherence(newCoherence);

      setQuantumState(QUANTUM_STATES.ENTANGLED);
      updateMetrics('quantum', true);

      return {
        entropy,
        coherence: newCoherence,
        state: QUANTUM_STATES.ENTANGLED,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Quantum measurement failed:', error);
      setQuantumState(QUANTUM_STATES.ERROR);
      updateMetrics('quantum', false);
      throw error;
    }
  }, [entanglement, updateMetrics]);

  // Collapse quantum state (measurement)
  const collapseState = useCallback(() => {
    setQuantumState(QUANTUM_STATES.COLLAPSED);
    return entropyHistory[entropyHistory.length - 1]?.entropy || 0;
  }, [entropyHistory]);

  // Reset quantum state
  const resetState = useCallback(() => {
    setQuantumState(QUANTUM_STATES.IDLE);
    setEntanglement({});
  }, []);

  // Calculate entanglement correlation between tasks
  const getEntanglementCorrelation = useCallback((taskId1, taskId2) => {
    if (!entanglement[taskId1] || !entanglement[taskId2]) return 0;
    
    const val1 = entanglement[taskId1].value;
    const val2 = entanglement[taskId2].value;
    return Math.abs(val1 - val2);
  }, [entanglement]);

  // Calculate average entropy over a time window
  const getAverageEntropy = useCallback((windowMs = 60000) => {
    const now = Date.now();
    const recent = entropyHistory.filter(
      entry => now - entry.timestamp < windowMs
    );
    
    if (recent.length === 0) return 0;
    
    const sum = recent.reduce((acc, curr) => acc + (curr.entropy || 0), 0);
    return sum / recent.length;
  }, [entropyHistory]);

  // Detect anomalies in entropy patterns
  const detectAnomalies = useCallback((task, currentEntropy) => {
    const avgEntropy = getAverageEntropy();
    const threshold = 0.15; // 15% deviation from average
    
    const deviation = Math.abs(currentEntropy - avgEntropy);
    const isAnomaly = deviation > threshold;
    
    if (isAnomaly) {
      console.warn(`Anomaly detected in task ${task.id}:`, {
        currentEntropy,
        averageEntropy: avgEntropy,
        deviation,
        threshold
      });
    }
    
    return {
      isAnomaly,
      deviation,
      threshold,
      averageEntropy: avgEntropy,
      timestamp: Date.now()
    };
  }, [getAverageEntropy]);

  // Update debug info
  const updateDebugInfo = useCallback(() => {
    setDebugInfo({
      lastMeasurement: entropyHistory[entropyHistory.length - 1]?.timestamp || null,
      quantumOperations: entropyHistory.length,
      classicalFallbacks: 0, // TODO: Track classical fallbacks
      averageEntropy: getAverageEntropy(),
      currentCoherence: coherence,
      quantumState,
      timestamp: Date.now()
    });
  }, [entropyHistory, coherence, quantumState, getAverageEntropy]);

  // Memoize context value
  const value = useMemo(() => ({
    quantumState,
    entropyHistory,
    coherence,
    entanglement,
    debugInfo,
    measureEntropy,
    collapseState,
    resetState,
    getEntanglementCorrelation,
    detectAnomalies,
    updateDebugInfo,
    getAverageEntropy
  }), [
    quantumState,
    entropyHistory,
    coherence,
    entanglement,
    debugInfo,
    measureEntropy,
    collapseState,
    resetState,
    getEntanglementCorrelation,
    detectAnomalies,
    updateDebugInfo,
    getAverageEntropy
  ]);

  return (
    <QuantumFieldContext.Provider value={value}>
      {children}
    </QuantumFieldContext.Provider>
  );
};

export const useQuantumField = () => {
  const context = useContext(QuantumFieldContext);
  if (!context) {
    throw new Error('useQuantumField must be used within a QuantumFieldProvider');
  }
  return context;
};

export { QUANTUM_STATES };
