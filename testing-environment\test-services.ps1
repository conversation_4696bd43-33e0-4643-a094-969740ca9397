# Test all services

# Test Mock API Service
Write-Host "Testing Mock API Service..." -ForegroundColor Green
$response = Invoke-WebRequest -Uri "http://localhost:3005/health" -Method GET
Write-Host "Mock API Health Check: $($response.StatusCode)" -ForegroundColor Green

$response = Invoke-WebRequest -Uri "http://localhost:3005/aws/securityhub/findings" -Method GET
Write-Host "AWS Security Hub Findings: $($response.StatusCode)" -ForegroundColor Green

$response = Invoke-WebRequest -Uri "http://localhost:3005/okta/users" -Method GET
Write-Host "Okta Users: $($response.StatusCode)" -ForegroundColor Green

$response = Invoke-WebRequest -Uri "http://localhost:3005/jira/issues" -Method GET
Write-Host "Jira Issues: $($response.StatusCode)" -ForegroundColor Green

# Test Connector Registry Service
Write-Host "Testing Connector Registry Service..." -ForegroundColor Green
$response = Invoke-WebRequest -Uri "http://localhost:3006/health" -Method GET
Write-Host "Connector Registry Health Check: $($response.StatusCode)" -ForegroundColor Green

$response = Invoke-WebRequest -Uri "http://localhost:3006/connectors" -Method GET
Write-Host "Connectors: $($response.StatusCode)" -ForegroundColor Green

# Create a test connector
$connector = @{
    metadata = @{
        name = "Test Connector"
        version = "1.0.0"
        category = "Test"
        description = "Test connector"
        author = "NovaGRC"
        tags = @("test")
    }
    authentication = @{
        type = "API_KEY"
        fields = @{
            apiKey = @{
                type = "string"
                description = "API Key"
                required = $true
                sensitive = $true
            }
        }
        testConnection = @{
            endpoint = "/health"
            method = "GET"
            expectedResponse = @{
                status = 200
            }
        }
    }
    configuration = @{
        baseUrl = "http://localhost:3005"
        headers = @{}
        timeout = 30000
        retryPolicy = @{
            maxRetries = 3
            backoffStrategy = "exponential"
        }
    }
    endpoints = @(
        @{
            id = "getFindings"
            name = "Get Findings"
            path = "/aws/securityhub/findings"
            method = "GET"
            parameters = @{
                query = @{}
                path = @{}
                body = @{}
            }
            response = @{
                successCode = 200
            }
        }
    )
    mappings = @(
        @{
            sourceEndpoint = "getFindings"
            targetSystem = "NovaGRC"
            targetEntity = "ComplianceFindings"
            transformations = @(
                @{
                    source = "$.Findings[0].Id"
                    target = "findingId"
                    transform = "identity"
                }
            )
        }
    )
    events = @{
        webhooks = @()
        polling = @()
    }
} | ConvertTo-Json -Depth 10

$response = Invoke-WebRequest -Uri "http://localhost:3006/connectors" -Method POST -Body $connector -ContentType "application/json"
$connectorId = ($response.Content | ConvertFrom-Json).id
Write-Host "Created Connector: $connectorId" -ForegroundColor Green

# Test Authentication Service
Write-Host "Testing Authentication Service..." -ForegroundColor Green
$response = Invoke-WebRequest -Uri "http://localhost:3007/health" -Method GET
Write-Host "Authentication Service Health Check: $($response.StatusCode)" -ForegroundColor Green

# Create a test credential
$credential = @{
    name = "Test Credential"
    connectorId = $connectorId
    authType = "API_KEY"
    credentials = @{
        apiKey = "test-api-key"
        header = "X-API-Key"
    }
    userId = "test-user"
} | ConvertTo-Json -Depth 10

$response = Invoke-WebRequest -Uri "http://localhost:3007/credentials" -Method POST -Body $credential -ContentType "application/json"
$credentialId = ($response.Content | ConvertFrom-Json).id
Write-Host "Created Credential: $credentialId" -ForegroundColor Green

# Test Usage Metering Service
Write-Host "Testing Usage Metering Service..." -ForegroundColor Green
$response = Invoke-WebRequest -Uri "http://localhost:3009/health" -Method GET
Write-Host "Usage Metering Service Health Check: $($response.StatusCode)" -ForegroundColor Green

# Create a test subscription
$subscription = @{
    userId = "test-user"
    plan = "Test Plan"
    limits = @{
        apiCalls = 1000
        connectors = 10
    }
} | ConvertTo-Json -Depth 10

$response = Invoke-WebRequest -Uri "http://localhost:3009/subscriptions" -Method POST -Body $subscription -ContentType "application/json"
Write-Host "Created Subscription: $($response.StatusCode)" -ForegroundColor Green

# Test Connector Executor Service
Write-Host "Testing Connector Executor Service..." -ForegroundColor Green
$response = Invoke-WebRequest -Uri "http://localhost:3008/health" -Method GET
Write-Host "Connector Executor Service Health Check: $($response.StatusCode)" -ForegroundColor Green

# Execute a connector
$execution = @{
    credentialId = $credentialId
    parameters = @{}
    userId = "test-user"
} | ConvertTo-Json -Depth 10

$response = Invoke-WebRequest -Uri "http://localhost:3008/execute/$connectorId/getFindings" -Method POST -Body $execution -ContentType "application/json"
Write-Host "Executed Connector: $($response.StatusCode)" -ForegroundColor Green
Write-Host "Response: $($response.Content)" -ForegroundColor Green

# Test Usage Tracking
Write-Host "Testing Usage Tracking..." -ForegroundColor Green
$response = Invoke-WebRequest -Uri "http://localhost:3009/usage?userId=test-user" -Method GET
Write-Host "Usage: $($response.StatusCode)" -ForegroundColor Green
Write-Host "Usage Data: $($response.Content)" -ForegroundColor Green

Write-Host "All tests completed successfully!" -ForegroundColor Green
