/**
 * Pattern Detector
 *
 * This module provides pattern detection capabilities for the Finite Universe
 * Principle defense system, enabling identification of complex patterns.
 */

const EventEmitter = require('events');

/**
 * PatternDetector class
 * 
 * Provides pattern detection capabilities for identifying complex patterns.
 */
class PatternDetector extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      historyLength: options.historyLength || 1000, // Number of data points to keep in history
      analysisInterval: options.analysisInterval || 60000, // 1 minute
      patternThreshold: options.patternThreshold || 0.8, // 80% similarity
      minPatternLength: options.minPatternLength || 3, // Minimum pattern length
      maxPatternLength: options.maxPatternLength || 20, // Maximum pattern length
      ...options
    };

    // Initialize metrics history
    this.metricsHistory = [];
    
    // Initialize pattern detection results
    this.patternDetection = {
      boundaryViolations: {
        patterns: [],
        lastDetection: null
      },
      validationFailures: {
        patterns: [],
        lastDetection: null
      },
      domainViolations: {
        cyber: {
          patterns: [],
          lastDetection: null
        },
        financial: {
          patterns: [],
          lastDetection: null
        },
        medical: {
          patterns: [],
          lastDetection: null
        }
      }
    };
    
    // Initialize analysis interval
    this.analysisInterval = null;

    if (this.options.enableLogging) {
      console.log('PatternDetector initialized with options:', this.options);
    }
  }

  /**
   * Start pattern detection
   */
  start() {
    if (this.analysisInterval) {
      return;
    }

    // Start analysis interval
    this.analysisInterval = setInterval(() => {
      this.detectPatterns();
    }, this.options.analysisInterval);

    if (this.options.enableLogging) {
      console.log('PatternDetector started');
    }
    
    // Emit start event
    this.emit('start');
  }

  /**
   * Stop pattern detection
   */
  stop() {
    if (!this.analysisInterval) {
      return;
    }

    // Clear analysis interval
    clearInterval(this.analysisInterval);
    this.analysisInterval = null;

    if (this.options.enableLogging) {
      console.log('PatternDetector stopped');
    }
    
    // Emit stop event
    this.emit('stop');
  }

  /**
   * Process metrics for pattern detection
   * @param {Object} metrics - Metrics to process
   * @returns {Object} - Pattern detection results
   */
  processMetrics(metrics) {
    // Add metrics to history
    this._addToHistory(metrics);
    
    // Detect patterns if enough history
    if (this.metricsHistory.length >= this.options.minPatternLength * 2) {
      this.detectPatterns();
    }
    
    return { ...this.patternDetection };
  }

  /**
   * Add metrics to history
   * @param {Object} metrics - Metrics to add
   * @private
   */
  _addToHistory(metrics) {
    // Add metrics to history
    this.metricsHistory.push({
      timestamp: new Date(),
      metrics: { ...metrics }
    });
    
    // Trim history if needed
    if (this.metricsHistory.length > this.options.historyLength) {
      this.metricsHistory.shift();
    }
  }

  /**
   * Detect patterns in metrics
   */
  detectPatterns() {
    if (this.metricsHistory.length < this.options.minPatternLength * 2) {
      if (this.options.enableLogging) {
        console.log('Not enough history for pattern detection');
      }
      return;
    }
    
    // Extract time series data
    const boundaryViolations = this.metricsHistory.map(entry => entry.metrics.boundaryViolations || 0);
    const validationFailures = this.metricsHistory.map(entry => entry.metrics.validationFailures || 0);
    
    const cyberViolations = this.metricsHistory.map(entry => 
      entry.metrics.domainMetrics?.cyber?.boundaryViolations || 0
    );
    
    const financialViolations = this.metricsHistory.map(entry => 
      entry.metrics.domainMetrics?.financial?.boundaryViolations || 0
    );
    
    const medicalViolations = this.metricsHistory.map(entry => 
      entry.metrics.domainMetrics?.medical?.boundaryViolations || 0
    );
    
    // Detect patterns
    this.patternDetection.boundaryViolations.patterns = this._detectPatterns(boundaryViolations);
    this.patternDetection.validationFailures.patterns = this._detectPatterns(validationFailures);
    this.patternDetection.domainViolations.cyber.patterns = this._detectPatterns(cyberViolations);
    this.patternDetection.domainViolations.financial.patterns = this._detectPatterns(financialViolations);
    this.patternDetection.domainViolations.medical.patterns = this._detectPatterns(medicalViolations);
    
    // Check for new patterns
    this._checkNewPatterns();
    
    // Emit detection event
    this.emit('detection', { ...this.patternDetection });
    
    if (this.options.enableLogging) {
      console.log('Patterns detected');
    }
  }

  /**
   * Detect patterns in time series data
   * @param {Array} data - Time series data
   * @returns {Array} - Detected patterns
   * @private
   */
  _detectPatterns(data) {
    if (data.length < this.options.minPatternLength * 2) {
      return [];
    }
    
    const patterns = [];
    
    // Detect repeating patterns
    for (let patternLength = this.options.minPatternLength; patternLength <= this.options.maxPatternLength; patternLength++) {
      // Skip if pattern length is too long
      if (patternLength > data.length / 2) {
        continue;
      }
      
      // Check for patterns
      for (let i = 0; i <= data.length - patternLength * 2; i++) {
        const pattern = data.slice(i, i + patternLength);
        
        // Check if pattern repeats
        for (let j = i + patternLength; j <= data.length - patternLength; j++) {
          const candidate = data.slice(j, j + patternLength);
          
          // Calculate similarity
          const similarity = this._calculateSimilarity(pattern, candidate);
          
          // Check if similarity is above threshold
          if (similarity >= this.options.patternThreshold) {
            // Check if pattern is already detected
            const patternExists = patterns.some(p => 
              p.length === patternLength && 
              this._calculateSimilarity(p.pattern, pattern) >= this.options.patternThreshold
            );
            
            if (!patternExists) {
              patterns.push({
                pattern,
                length: patternLength,
                similarity,
                occurrences: [i, j]
              });
            }
          }
        }
      }
    }
    
    // Sort patterns by length (longest first)
    return patterns.sort((a, b) => b.length - a.length);
  }

  /**
   * Calculate similarity between two arrays
   * @param {Array} array1 - First array
   * @param {Array} array2 - Second array
   * @returns {number} - Similarity (0-1)
   * @private
   */
  _calculateSimilarity(array1, array2) {
    if (array1.length !== array2.length) {
      return 0;
    }
    
    // Calculate Euclidean distance
    let sumSquaredDiff = 0;
    let sumSquared1 = 0;
    let sumSquared2 = 0;
    
    for (let i = 0; i < array1.length; i++) {
      sumSquaredDiff += Math.pow(array1[i] - array2[i], 2);
      sumSquared1 += Math.pow(array1[i], 2);
      sumSquared2 += Math.pow(array2[i], 2);
    }
    
    // Handle zero vectors
    if (sumSquared1 === 0 && sumSquared2 === 0) {
      return 1; // Both arrays are all zeros
    }
    
    if (sumSquared1 === 0 || sumSquared2 === 0) {
      return 0; // One array is all zeros, the other is not
    }
    
    // Calculate similarity
    const distance = Math.sqrt(sumSquaredDiff);
    const magnitude1 = Math.sqrt(sumSquared1);
    const magnitude2 = Math.sqrt(sumSquared2);
    
    // Normalize to 0-1 range
    return 1 - (distance / (magnitude1 + magnitude2));
  }

  /**
   * Check for new patterns
   * @private
   */
  _checkNewPatterns() {
    const now = new Date();
    
    // Check boundary violations patterns
    if (this.patternDetection.boundaryViolations.patterns.length > 0) {
      if (!this.patternDetection.boundaryViolations.lastDetection) {
        this.patternDetection.boundaryViolations.lastDetection = now;
        
        this.emit('pattern-detected', {
          metric: 'boundaryViolations',
          patterns: this.patternDetection.boundaryViolations.patterns
        });
      }
    }
    
    // Check validation failures patterns
    if (this.patternDetection.validationFailures.patterns.length > 0) {
      if (!this.patternDetection.validationFailures.lastDetection) {
        this.patternDetection.validationFailures.lastDetection = now;
        
        this.emit('pattern-detected', {
          metric: 'validationFailures',
          patterns: this.patternDetection.validationFailures.patterns
        });
      }
    }
    
    // Check domain violations patterns
    for (const domain of ['cyber', 'financial', 'medical']) {
      if (this.patternDetection.domainViolations[domain].patterns.length > 0) {
        if (!this.patternDetection.domainViolations[domain].lastDetection) {
          this.patternDetection.domainViolations[domain].lastDetection = now;
          
          this.emit('pattern-detected', {
            metric: `${domain}Violations`,
            patterns: this.patternDetection.domainViolations[domain].patterns
          });
        }
      }
    }
  }

  /**
   * Get pattern detection results
   * @returns {Object} - Pattern detection results
   */
  getPatternDetection() {
    return { ...this.patternDetection };
  }

  /**
   * Dispose resources
   */
  dispose() {
    this.stop();
    
    // Clear history
    this.metricsHistory = [];
    
    if (this.options.enableLogging) {
      console.log('PatternDetector disposed');
    }
  }
}

/**
 * Create a pattern detector with recommended settings
 * @param {Object} options - Configuration options
 * @returns {PatternDetector} - Configured pattern detector
 */
function createPatternDetector(options = {}) {
  return new PatternDetector({
    enableLogging: true,
    historyLength: 1000,
    analysisInterval: 60000,
    patternThreshold: 0.8,
    minPatternLength: 3,
    maxPatternLength: 20,
    ...options
  });
}

module.exports = {
  PatternDetector,
  createPatternDetector
};
