/**
 * Quantum Metrics Dashboard Example
 *
 * This example demonstrates how to use the Quantum Metrics Dashboard
 * to visualize metrics from the Quantum State Inference Layer.
 */

// Import required modules
const NovaStoreTrinityIntegration = require('../src/novastore/trinity_csde_integration');
const QuantumMetricsDashboard = require('../src/novavision/components/quantum-metrics-dashboard');
const { NovaVision } = require('../src/novavision');
const { NovaVisionSecurityManager } = require('../src/novavision/security');

// Example user IDs
const users = {
  ciso: 'user-123',
  securityAnalyst: 'user-456',
  standardUser: 'user-789'
};

// Create a NovaVision instance with security enabled
const novaVision = new NovaVision({
  theme: 'cyber-safety',
  enableSecurity: true,
  enableNIST: true,
  enableRBAC: true
});

// Assign roles to users
const securityManager = novaVision.securityManager;
securityManager.rbac.assignRole(users.ciso, 'CISO');
securityManager.rbac.assignRole(users.securityAnalyst, 'SECURITY_ANALYST');
securityManager.rbac.assignRole(users.standardUser, 'USER');

// Create a NovaStore Trinity Integration instance with Quantum Inference enabled
const trinityIntegration = new NovaStoreTrinityIntegration({
  enableMetrics: true,
  enableCaching: true,
  enableQuantumInference: true
});

// Create a Quantum Metrics Dashboard with enhanced features
const metricsDashboard = new QuantumMetricsDashboard({
  theme: 'cyber-safety',
  colorScheme: 'quantum',
  refreshInterval: 30000, // 30 seconds
  enableRealTimeUpdates: true,
  enablePerformanceOptimization: true,
  samplingRate: 0.18, // Use 18% sampling for large datasets
  lazyLoading: true
});

// Example component to verify
const exampleComponent = {
  id: 'component-123',
  name: 'Example Security Component',
  type: 'security',
  category: 'firewall',
  tags: ['network', 'security', 'firewall'],
  detectionCapability: 0.75,
  threatSeverity: 0.65,
  threatConfidence: 0.8,
  baselineSignals: 0.7,
  complianceScore: 0.85,
  auditFrequency: 2,
  baseResponseTime: 50,
  threatSurface: 0.8,
  systemRadius: 120,
  reactionTime: 0.3,
  mitigationSurface: 0.6,
  estimatedRevenue: 5000,
  timestamp: new Date().toISOString(),
  location: 'us-east-1',
  timePatterns: [
    {
      timestamp: new Date().toISOString(),
      window: 3600000 // 1 hour
    }
  ],
  locationPatterns: [
    {
      location: 'us-east-1'
    }
  ],

  // Add user information for security context and audit trail
  userId: users.ciso,
  roles: ['CISO'],
  permissions: ['quantum_inference:predict', 'quantum_inference:view']
};

/**
 * Simulate security events
 * @param {number} count - Number of events to simulate
 * @returns {Array} - Simulated security events
 */
function simulateSecurityEvents(count = 10) {
  const events = [];
  const operations = ['quantum_inference', 'view:dashboard', 'export:metrics'];
  const userIds = [users.ciso, users.securityAnalyst, users.standardUser];
  const roles = ['CISO', 'SECURITY_ANALYST', 'USER'];

  for (let i = 0; i < count; i++) {
    const userId = userIds[Math.floor(Math.random() * userIds.length)];
    const role = roles[userIds.indexOf(userId)];
    const operation = operations[Math.floor(Math.random() * operations.length)];
    const allowed = role === 'USER' ? Math.random() > 0.8 : Math.random() > 0.2; // Users mostly denied, others mostly allowed

    events.push({
      timestamp: new Date(Date.now() - Math.floor(Math.random() * 3600000)).toISOString(),
      userId,
      role,
      operation,
      allowed,
      reason: allowed ?
        (role === 'CISO' ? 'admin_role' : 'component_permission') :
        'access_denied'
    });
  }

  return events;
}

/**
 * Simulate action distribution
 * @returns {Object} - Simulated action distribution
 */
function simulateActionDistribution() {
  return {
    enhance_detection: {
      count: Math.floor(Math.random() * 20) + 5,
      byPriority: {
        low: Math.floor(Math.random() * 10),
        medium: Math.floor(Math.random() * 8),
        high: Math.floor(Math.random() * 5),
        critical: Math.floor(Math.random() * 2)
      }
    },
    mitigate_threat: {
      count: Math.floor(Math.random() * 30) + 10,
      byPriority: {
        low: Math.floor(Math.random() * 15),
        medium: Math.floor(Math.random() * 10),
        high: Math.floor(Math.random() * 8),
        critical: Math.floor(Math.random() * 3)
      }
    },
    escalate: {
      count: Math.floor(Math.random() * 10) + 2,
      byPriority: {
        low: Math.floor(Math.random() * 3),
        medium: Math.floor(Math.random() * 4),
        high: Math.floor(Math.random() * 5),
        critical: Math.floor(Math.random() * 2)
      }
    }
  };
}

/**
 * Run the example
 */
async function runExample() {
  console.log('=== Quantum Metrics Dashboard Example ===\n');

  try {
    // Simulate multiple component verifications to generate metrics
    console.log('Simulating component verifications to generate metrics...');

    for (let i = 0; i < 5; i++) {
      // Modify component slightly for each iteration
      const component = {
        ...exampleComponent,
        id: `component-${123 + i}`,
        detectionCapability: 0.75 + (Math.random() * 0.2 - 0.1),
        threatSeverity: 0.65 + (Math.random() * 0.2 - 0.1),
        userId: i % 3 === 0 ? users.ciso : (i % 3 === 1 ? users.securityAnalyst : users.standardUser),
        roles: i % 3 === 0 ? ['CISO'] : (i % 3 === 1 ? ['SECURITY_ANALYST'] : ['USER'])
      };

      // Verify component
      const verificationResult = await trinityIntegration.verifyComponent(component, 'advanced');

      // Update metrics dashboard with quantum metrics
      if (verificationResult.quantumInference) {
        metricsDashboard.updateMetrics(
          verificationResult.quantumInference.metrics,
          {
            events: simulateSecurityEvents(3),
            actionDistribution: simulateActionDistribution()
          }
        );
      }

      // Wait a bit between verifications
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Generate dashboard schema
    console.log('\nGenerating metrics dashboard...');
    const dashboardSchema = metricsDashboard.generateDashboard();

    console.log(`\nDashboard generated: ${dashboardSchema.title}`);
    console.log(`- Sections: ${dashboardSchema.sections.length}`);
    console.log(`- Section IDs: ${dashboardSchema.sections.map(s => s.id).join(', ')}`);

    // Export metrics in different formats
    console.log('\nExporting metrics in different formats:');

    console.log('\n1. JSON Format (sample):');
    const jsonMetrics = metricsDashboard.exportMetrics('json');
    console.log(jsonMetrics.substring(0, 500) + '...');

    console.log('\n2. CSV Format:');
    const csvMetrics = metricsDashboard.exportMetrics('csv');
    console.log(csvMetrics);

    console.log('\n3. Prometheus Format:');
    const prometheusMetrics = metricsDashboard.exportMetrics('prometheus');
    console.log(prometheusMetrics);

    // Integration with monitoring tools
    console.log('\nIntegration with monitoring tools:');
    console.log('- Dashboard can be exported to Grafana using the JSON format');
    console.log('- Metrics can be scraped by Prometheus using the Prometheus format');
    console.log('- CSV export can be used for offline analysis in tools like Excel or Tableau');

    // Stop the refresh timer
    metricsDashboard.stopRefreshTimer();

  } catch (error) {
    console.error('Error running example:', error);
  }

  console.log('\n=== End of Example ===');
}

// Run the example
runExample();

/**
 * How to run this example:
 *
 * 1. Make sure all dependencies are installed
 * 2. Run the example using Node.js:
 *    node examples/quantum_metrics_dashboard_example.js
 */
