/**
 * Enhanced Audit Routes
 * 
 * This file defines enhanced routes for audit logging functionality including:
 * - Advanced filtering
 * - Export capabilities
 * - Analytics and reporting
 * - Tenant-specific audit logs
 */

const express = require('express');
const router = express.Router();
const EnhancedAuditController = require('../controllers/EnhancedAuditController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticate);

/**
 * @route GET /api/audit/logs
 * @description Get audit logs with advanced filtering
 * @access Admin
 */
router.get('/logs', hasPermission('system:audit'), (req, res, next) => {
  EnhancedAuditController.getAuditLogs(req, res, next);
});

/**
 * @route GET /api/audit/logs/export
 * @description Export audit logs to CSV
 * @access Admin
 */
router.get('/logs/export', hasPermission('system:audit'), (req, res, next) => {
  EnhancedAuditController.exportAuditLogs(req, res, next);
});

/**
 * @route GET /api/audit/logs/stats
 * @description Get audit log statistics
 * @access Admin
 */
router.get('/logs/stats', hasPermission('system:audit'), (req, res, next) => {
  EnhancedAuditController.getAuditLogStats(req, res, next);
});

/**
 * @route GET /api/audit/tenant/:tenantId/logs
 * @description Get tenant-specific audit logs
 * @access Tenant Admin
 */
router.get('/tenant/:tenantId/logs', hasPermission('tenant:audit'), (req, res, next) => {
  EnhancedAuditController.getTenantAuditLogs(req, res, next);
});

/**
 * @route GET /api/audit/tenant/:tenantId/logs/export
 * @description Export tenant-specific audit logs to CSV
 * @access Tenant Admin
 */
router.get('/tenant/:tenantId/logs/export', hasPermission('tenant:audit'), (req, res, next) => {
  EnhancedAuditController.exportAuditLogs(req, res, next);
});

/**
 * @route GET /api/audit/tenant/:tenantId/logs/stats
 * @description Get tenant-specific audit log statistics
 * @access Tenant Admin
 */
router.get('/tenant/:tenantId/logs/stats', hasPermission('tenant:audit'), (req, res, next) => {
  EnhancedAuditController.getAuditLogStats(req, res, next);
});

/**
 * @route GET /api/audit/user/me/logs
 * @description Get audit logs for the current user
 * @access Authenticated User
 */
router.get('/user/me/logs', (req, res, next) => {
  // Add user ID to query parameters
  req.query.userId = req.user.id;
  EnhancedAuditController.getAuditLogs(req, res, next);
});

/**
 * @route GET /api/audit/user/me/logs/export
 * @description Export audit logs for the current user to CSV
 * @access Authenticated User
 */
router.get('/user/me/logs/export', (req, res, next) => {
  // Add user ID to query parameters
  req.query.userId = req.user.id;
  EnhancedAuditController.exportAuditLogs(req, res, next);
});

/**
 * @route GET /api/audit/team/:teamId/logs
 * @description Get audit logs for a specific team
 * @access Team Admin
 */
router.get('/team/:teamId/logs', hasPermission('team:audit'), (req, res, next) => {
  // Add team ID to query parameters
  req.query.teamId = req.params.teamId;
  EnhancedAuditController.getAuditLogs(req, res, next);
});

/**
 * @route GET /api/audit/team/:teamId/logs/export
 * @description Export audit logs for a specific team to CSV
 * @access Team Admin
 */
router.get('/team/:teamId/logs/export', hasPermission('team:audit'), (req, res, next) => {
  // Add team ID to query parameters
  req.query.teamId = req.params.teamId;
  EnhancedAuditController.exportAuditLogs(req, res, next);
});

/**
 * @route GET /api/audit/team/:teamId/logs/stats
 * @description Get audit log statistics for a specific team
 * @access Team Admin
 */
router.get('/team/:teamId/logs/stats', hasPermission('team:audit'), (req, res, next) => {
  // Add team ID to query parameters
  req.query.teamId = req.params.teamId;
  EnhancedAuditController.getAuditLogStats(req, res, next);
});

/**
 * @route GET /api/audit/resource/:resourceType/:resourceId/logs
 * @description Get audit logs for a specific resource
 * @access Admin
 */
router.get('/resource/:resourceType/:resourceId/logs', hasPermission('system:audit'), (req, res, next) => {
  // Add resource type and ID to query parameters
  req.query.resourceType = req.params.resourceType;
  req.query.resourceId = req.params.resourceId;
  EnhancedAuditController.getAuditLogs(req, res, next);
});

/**
 * @route GET /api/audit/resource/:resourceType/:resourceId/logs/export
 * @description Export audit logs for a specific resource to CSV
 * @access Admin
 */
router.get('/resource/:resourceType/:resourceId/logs/export', hasPermission('system:audit'), (req, res, next) => {
  // Add resource type and ID to query parameters
  req.query.resourceType = req.params.resourceType;
  req.query.resourceId = req.params.resourceId;
  EnhancedAuditController.exportAuditLogs(req, res, next);
});

module.exports = router;
