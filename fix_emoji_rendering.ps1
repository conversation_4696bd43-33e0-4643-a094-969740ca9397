# Script to fix emoji rendering in the dictionary

# Define paths
$dictionaryPath = "d:\\novafuse-api-superstore\\coherence-reality-systems\\Comphyology Master Archive\\3. APPENDIX A - MATH FOUNDATION\\The Comphyological Dictionary 1st Edition.md"
$backupPath = "$dictionaryPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss').md"

# Create a backup of the original file
Copy-Item -Path $dictionaryPath -Destination $backupPath -Force
Write-Host "Created backup at: $backupPath"

# Read the content with UTF-8 encoding
$content = [System.IO.File]::ReadAllText($dictionaryPath, [System.Text.Encoding]::UTF8)

# Define replacement pairs as an array of hashtables
$replacements = @(
    @{Pattern = 'ðŸ”‘'; Replacement = '💡'},  # Lightbulb emoji
    @{Pattern = 'ðŸ§¬'; Replacement = '📋'},  # Clipboard emoji
    @{Pattern = 'ðŸ§ '; Replacement = '⚠️'},  # Warning emoji (with space after §)
    @{Pattern = 'ðŸ§';  Replacement = '⚠️'},  # Warning emoji (without space after §)
    
    # Special characters
    @{Pattern = 'Îº'; Replacement = 'κ'},    # Greek kappa
    @{Pattern = 'Ã—'; Replacement = '×'},    # Multiplication sign
    
    # Common encoding fixes
    @{Pattern = 'Ã©'; Replacement = 'é'},
    @{Pattern = 'Ã¨'; Replacement = 'è'},
    @{Pattern = 'Ã´'; Replacement = 'ô'},
    @{Pattern = 'Ã¢'; Replacement = 'â'},
    @{Pattern = 'Ã '; Replacement = 'à'},
    @{Pattern = 'Ã§'; Replacement = 'ç'},
    @{Pattern = 'Ãª'; Replacement = 'ê'},
    @{Pattern = 'Ã«'; Replacement = 'ë'},
    @{Pattern = 'Ã¯'; Replacement = 'ï'},
    @{Pattern = 'Ã®'; Replacement = 'î'},
    @{Pattern = 'Ã¹'; Replacement = 'ù'},
    @{Pattern = 'Ã»'; Replacement = 'û'},
    @{Pattern = 'Ã¼'; Replacement = 'ü'},
    @{Pattern = 'Ã¿'; Replacement = 'ÿ'},
    @{Pattern = 'Ã';  Replacement = 'à'}  # Fallback for any other Ã combinations
)

# Apply all replacements
foreach ($replacement in $replacements) {
    $content = $content -replace [regex]::Escape($replacement.Pattern), $replacement.Replacement
}

# Write the modified content back to the file with UTF-8 encoding
[System.IO.File]::WriteAllText($dictionaryPath, $content.Trim(), [System.Text.Encoding]::UTF8)

Write-Host "Fixed emoji and special character rendering in the dictionary file."
