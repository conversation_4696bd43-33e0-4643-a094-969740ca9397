/**
 * Cluster Middleware
 * 
 * This middleware integrates with the ClusterService to track requests and provide
 * cluster-aware functionality.
 */

const clusterService = require('../services/ClusterService');
const logger = require('../utils/logger');

/**
 * Cluster middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const clusterMiddleware = (req, res, next) => {
  // Record request start
  clusterService.recordRequest();
  
  // Add cluster info to request
  req.cluster = {
    info: clusterService.getInfo()
  };
  
  // Add response listener to record request completion
  res.on('finish', () => {
    const isError = res.statusCode >= 400;
    clusterService.recordRequestComplete(isError);
  });
  
  next();
};

/**
 * Cluster health endpoint
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const clusterHealth = (req, res) => {
  const clusterInfo = clusterService.getInfo();
  
  res.json({
    status: 'ok',
    cluster: clusterInfo
  });
};

module.exports = {
  clusterMiddleware,
  clusterHealth
};
