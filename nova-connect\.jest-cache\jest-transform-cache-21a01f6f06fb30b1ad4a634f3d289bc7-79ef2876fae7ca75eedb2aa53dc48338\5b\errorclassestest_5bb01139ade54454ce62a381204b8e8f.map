{"version": 3, "names": ["UAConnectorError", "AuthenticationError", "MissingCredentialsError", "ConnectionError", "TimeoutError", "ValidationError", "MissingRequiredFieldError", "ApiError", "RateLimitExceededError", "ResourceNotFoundError", "TransformationError", "ConnectorError", "ConnectorNotFoundError", "require", "describe", "it", "error", "expect", "toBeInstanceOf", "Error", "name", "toBe", "message", "code", "severity", "context", "toEqual", "cause", "toBeUndefined", "timestamp", "toBeDefined", "errorId", "foo", "json", "toJSON", "stack", "getUserMessage", "getDeveloperMessage", "validationErrors", "field", "length", "statusCode", "response", "retryAfter", "resourceType", "resourceId", "connectorId"], "sources": ["error-classes.test.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector - Error Classes Tests\n * \n * This module tests the error classes for the UAC.\n */\n\nconst {\n  UAConnectorError,\n  AuthenticationError,\n  MissingCredentialsError,\n  ConnectionError,\n  TimeoutError,\n  ValidationError,\n  MissingRequiredFieldError,\n  ApiError,\n  RateLimitExceededError,\n  ResourceNotFoundError,\n  TransformationError,\n  ConnectorError,\n  ConnectorNotFoundError\n} = require('../../src/errors');\n\ndescribe('Error Classes', () => {\n  describe('UAConnectorError', () => {\n    it('should create a base error with default values', () => {\n      const error = new UAConnectorError('Test error');\n      \n      expect(error).toBeInstanceOf(Error);\n      expect(error.name).toBe('UAConnectorError');\n      expect(error.message).toBe('Test error');\n      expect(error.code).toBe('UAC_ERROR');\n      expect(error.severity).toBe('error');\n      expect(error.context).toEqual({});\n      expect(error.cause).toBeUndefined();\n      expect(error.timestamp).toBeDefined();\n      expect(error.errorId).toBeDefined();\n    });\n    \n    it('should create a base error with custom values', () => {\n      const cause = new Error('Original error');\n      const error = new UAConnectorError('Test error', {\n        code: 'CUSTOM_CODE',\n        severity: 'warning',\n        context: { foo: 'bar' },\n        cause\n      });\n      \n      expect(error.code).toBe('CUSTOM_CODE');\n      expect(error.severity).toBe('warning');\n      expect(error.context).toEqual({ foo: 'bar' });\n      expect(error.cause).toBe(cause);\n    });\n    \n    it('should convert to JSON', () => {\n      const error = new UAConnectorError('Test error', {\n        code: 'CUSTOM_CODE',\n        context: { foo: 'bar' }\n      });\n      \n      const json = error.toJSON();\n      \n      expect(json.errorId).toBe(error.errorId);\n      expect(json.name).toBe('UAConnectorError');\n      expect(json.message).toBe('Test error');\n      expect(json.code).toBe('CUSTOM_CODE');\n      expect(json.severity).toBe('error');\n      expect(json.context).toEqual({ foo: 'bar' });\n      expect(json.stack).toBeUndefined();\n    });\n    \n    it('should convert to JSON with stack trace', () => {\n      const error = new UAConnectorError('Test error');\n      \n      const json = error.toJSON(true);\n      \n      expect(json.stack).toBeDefined();\n    });\n    \n    it('should provide user and developer messages', () => {\n      const error = new UAConnectorError('Test error', {\n        code: 'CUSTOM_CODE'\n      });\n      \n      expect(error.getUserMessage()).toBe('Test error');\n      expect(error.getDeveloperMessage()).toBe('[CUSTOM_CODE] Test error');\n    });\n  });\n  \n  describe('AuthenticationError', () => {\n    it('should create an authentication error', () => {\n      const error = new AuthenticationError('Auth failed');\n      \n      expect(error).toBeInstanceOf(UAConnectorError);\n      expect(error.name).toBe('AuthenticationError');\n      expect(error.code).toBe('AUTH_ERROR');\n      expect(error.getUserMessage()).toBe('Authentication failed. Please check your credentials and try again.');\n    });\n    \n    it('should create a missing credentials error', () => {\n      const error = new MissingCredentialsError();\n      \n      expect(error).toBeInstanceOf(AuthenticationError);\n      expect(error.name).toBe('MissingCredentialsError');\n      expect(error.code).toBe('AUTH_MISSING_CREDENTIALS');\n      expect(error.getUserMessage()).toBe('Authentication failed. Required credentials are missing.');\n    });\n  });\n  \n  describe('ConnectionError', () => {\n    it('should create a connection error', () => {\n      const error = new ConnectionError('Connection failed');\n      \n      expect(error).toBeInstanceOf(UAConnectorError);\n      expect(error.name).toBe('ConnectionError');\n      expect(error.code).toBe('CONNECTION_ERROR');\n      expect(error.getUserMessage()).toBe('Failed to connect to the service. Please check your network connection and try again.');\n    });\n    \n    it('should create a timeout error', () => {\n      const error = new TimeoutError();\n      \n      expect(error).toBeInstanceOf(ConnectionError);\n      expect(error.name).toBe('TimeoutError');\n      expect(error.code).toBe('CONNECTION_TIMEOUT');\n      expect(error.getUserMessage()).toBe('The request timed out. Please try again later or contact support if the issue persists.');\n    });\n  });\n  \n  describe('ValidationError', () => {\n    it('should create a validation error', () => {\n      const error = new ValidationError('Validation failed');\n      \n      expect(error).toBeInstanceOf(UAConnectorError);\n      expect(error.name).toBe('ValidationError');\n      expect(error.code).toBe('VALIDATION_ERROR');\n      expect(error.getUserMessage()).toBe('The provided data is invalid. Please check your input and try again.');\n    });\n    \n    it('should create a validation error with validation errors', () => {\n      const validationErrors = [\n        { field: 'name', message: 'Name is required' },\n        { field: 'email', message: 'Email is invalid' }\n      ];\n      \n      const error = new ValidationError('Validation failed', {\n        validationErrors\n      });\n      \n      expect(error.validationErrors).toEqual(validationErrors);\n      expect(error.getUserMessage()).toBe('Validation failed: Name is required; Email is invalid');\n    });\n    \n    it('should create a missing required field error', () => {\n      const error = new MissingRequiredFieldError('name');\n      \n      expect(error).toBeInstanceOf(ValidationError);\n      expect(error.name).toBe('MissingRequiredFieldError');\n      expect(error.code).toBe('VALIDATION_MISSING_REQUIRED_FIELD');\n      expect(error.getUserMessage()).toBe('Validation failed: Missing required field: name');\n    });\n    \n    it('should create a missing required field error with multiple fields', () => {\n      const error = new MissingRequiredFieldError(['name', 'email']);\n      \n      expect(error.validationErrors.length).toBe(2);\n      expect(error.getUserMessage()).toBe('Validation failed: Missing required field: name; Missing required field: email');\n    });\n  });\n  \n  describe('ApiError', () => {\n    it('should create an API error', () => {\n      const error = new ApiError('API error');\n      \n      expect(error).toBeInstanceOf(UAConnectorError);\n      expect(error.name).toBe('ApiError');\n      expect(error.code).toBe('API_ERROR');\n      expect(error.getUserMessage()).toBe('An error occurred while communicating with the external service. Please try again later.');\n    });\n    \n    it('should create an API error with status code and response', () => {\n      const error = new ApiError('API error', {\n        statusCode: 400,\n        response: { message: 'Bad request' }\n      });\n      \n      expect(error.statusCode).toBe(400);\n      expect(error.response).toEqual({ message: 'Bad request' });\n      \n      const json = error.toJSON();\n      expect(json.statusCode).toBe(400);\n      expect(json.response).toEqual({ message: 'Bad request' });\n    });\n    \n    it('should create a rate limit exceeded error', () => {\n      const error = new RateLimitExceededError('Rate limit exceeded', {\n        retryAfter: 60\n      });\n      \n      expect(error).toBeInstanceOf(ApiError);\n      expect(error.name).toBe('RateLimitExceededError');\n      expect(error.code).toBe('API_RATE_LIMIT_EXCEEDED');\n      expect(error.retryAfter).toBe(60);\n      expect(error.getUserMessage()).toBe('Rate limit exceeded. Please try again after 60 seconds.');\n    });\n    \n    it('should create a resource not found error', () => {\n      const error = new ResourceNotFoundError('User', '123');\n      \n      expect(error).toBeInstanceOf(ApiError);\n      expect(error.name).toBe('ResourceNotFoundError');\n      expect(error.code).toBe('API_RESOURCE_NOT_FOUND');\n      expect(error.resourceType).toBe('User');\n      expect(error.resourceId).toBe('123');\n      expect(error.getUserMessage()).toBe('The requested user could not be found.');\n    });\n  });\n  \n  describe('TransformationError', () => {\n    it('should create a transformation error', () => {\n      const error = new TransformationError('Transformation failed');\n      \n      expect(error).toBeInstanceOf(UAConnectorError);\n      expect(error.name).toBe('TransformationError');\n      expect(error.code).toBe('TRANSFORMATION_ERROR');\n      expect(error.getUserMessage()).toBe('An error occurred while processing the data. Please contact support if the issue persists.');\n    });\n  });\n  \n  describe('ConnectorError', () => {\n    it('should create a connector error', () => {\n      const error = new ConnectorError('Connector error', {\n        connectorId: 'test-connector'\n      });\n      \n      expect(error).toBeInstanceOf(UAConnectorError);\n      expect(error.name).toBe('ConnectorError');\n      expect(error.code).toBe('CONNECTOR_ERROR');\n      expect(error.connectorId).toBe('test-connector');\n      expect(error.getUserMessage()).toBe('An error occurred with the connector. Please try again later or contact support if the issue persists.');\n    });\n    \n    it('should create a connector not found error', () => {\n      const error = new ConnectorNotFoundError('test-connector');\n      \n      expect(error).toBeInstanceOf(ConnectorError);\n      expect(error.name).toBe('ConnectorNotFoundError');\n      expect(error.code).toBe('CONNECTOR_NOT_FOUND');\n      expect(error.connectorId).toBe('test-connector');\n      expect(error.getUserMessage()).toBe('The connector \"test-connector\" was not found. Please check the connector ID and try again.');\n    });\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EACJA,gBAAgB;EAChBC,mBAAmB;EACnBC,uBAAuB;EACvBC,eAAe;EACfC,YAAY;EACZC,eAAe;EACfC,yBAAyB;EACzBC,QAAQ;EACRC,sBAAsB;EACtBC,qBAAqB;EACrBC,mBAAmB;EACnBC,cAAc;EACdC;AACF,CAAC,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAE/BC,QAAQ,CAAC,eAAe,EAAE,MAAM;EAC9BA,QAAQ,CAAC,kBAAkB,EAAE,MAAM;IACjCC,EAAE,CAAC,gDAAgD,EAAE,MAAM;MACzD,MAAMC,KAAK,GAAG,IAAIhB,gBAAgB,CAAC,YAAY,CAAC;MAEhDiB,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,CAACC,KAAK,CAAC;MACnCF,MAAM,CAACD,KAAK,CAACI,IAAI,CAAC,CAACC,IAAI,CAAC,kBAAkB,CAAC;MAC3CJ,MAAM,CAACD,KAAK,CAACM,OAAO,CAAC,CAACD,IAAI,CAAC,YAAY,CAAC;MACxCJ,MAAM,CAACD,KAAK,CAACO,IAAI,CAAC,CAACF,IAAI,CAAC,WAAW,CAAC;MACpCJ,MAAM,CAACD,KAAK,CAACQ,QAAQ,CAAC,CAACH,IAAI,CAAC,OAAO,CAAC;MACpCJ,MAAM,CAACD,KAAK,CAACS,OAAO,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;MACjCT,MAAM,CAACD,KAAK,CAACW,KAAK,CAAC,CAACC,aAAa,CAAC,CAAC;MACnCX,MAAM,CAACD,KAAK,CAACa,SAAS,CAAC,CAACC,WAAW,CAAC,CAAC;MACrCb,MAAM,CAACD,KAAK,CAACe,OAAO,CAAC,CAACD,WAAW,CAAC,CAAC;IACrC,CAAC,CAAC;IAEFf,EAAE,CAAC,+CAA+C,EAAE,MAAM;MACxD,MAAMY,KAAK,GAAG,IAAIR,KAAK,CAAC,gBAAgB,CAAC;MACzC,MAAMH,KAAK,GAAG,IAAIhB,gBAAgB,CAAC,YAAY,EAAE;QAC/CuB,IAAI,EAAE,aAAa;QACnBC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE;UAAEO,GAAG,EAAE;QAAM,CAAC;QACvBL;MACF,CAAC,CAAC;MAEFV,MAAM,CAACD,KAAK,CAACO,IAAI,CAAC,CAACF,IAAI,CAAC,aAAa,CAAC;MACtCJ,MAAM,CAACD,KAAK,CAACQ,QAAQ,CAAC,CAACH,IAAI,CAAC,SAAS,CAAC;MACtCJ,MAAM,CAACD,KAAK,CAACS,OAAO,CAAC,CAACC,OAAO,CAAC;QAAEM,GAAG,EAAE;MAAM,CAAC,CAAC;MAC7Cf,MAAM,CAACD,KAAK,CAACW,KAAK,CAAC,CAACN,IAAI,CAACM,KAAK,CAAC;IACjC,CAAC,CAAC;IAEFZ,EAAE,CAAC,wBAAwB,EAAE,MAAM;MACjC,MAAMC,KAAK,GAAG,IAAIhB,gBAAgB,CAAC,YAAY,EAAE;QAC/CuB,IAAI,EAAE,aAAa;QACnBE,OAAO,EAAE;UAAEO,GAAG,EAAE;QAAM;MACxB,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAGjB,KAAK,CAACkB,MAAM,CAAC,CAAC;MAE3BjB,MAAM,CAACgB,IAAI,CAACF,OAAO,CAAC,CAACV,IAAI,CAACL,KAAK,CAACe,OAAO,CAAC;MACxCd,MAAM,CAACgB,IAAI,CAACb,IAAI,CAAC,CAACC,IAAI,CAAC,kBAAkB,CAAC;MAC1CJ,MAAM,CAACgB,IAAI,CAACX,OAAO,CAAC,CAACD,IAAI,CAAC,YAAY,CAAC;MACvCJ,MAAM,CAACgB,IAAI,CAACV,IAAI,CAAC,CAACF,IAAI,CAAC,aAAa,CAAC;MACrCJ,MAAM,CAACgB,IAAI,CAACT,QAAQ,CAAC,CAACH,IAAI,CAAC,OAAO,CAAC;MACnCJ,MAAM,CAACgB,IAAI,CAACR,OAAO,CAAC,CAACC,OAAO,CAAC;QAAEM,GAAG,EAAE;MAAM,CAAC,CAAC;MAC5Cf,MAAM,CAACgB,IAAI,CAACE,KAAK,CAAC,CAACP,aAAa,CAAC,CAAC;IACpC,CAAC,CAAC;IAEFb,EAAE,CAAC,yCAAyC,EAAE,MAAM;MAClD,MAAMC,KAAK,GAAG,IAAIhB,gBAAgB,CAAC,YAAY,CAAC;MAEhD,MAAMiC,IAAI,GAAGjB,KAAK,CAACkB,MAAM,CAAC,IAAI,CAAC;MAE/BjB,MAAM,CAACgB,IAAI,CAACE,KAAK,CAAC,CAACL,WAAW,CAAC,CAAC;IAClC,CAAC,CAAC;IAEFf,EAAE,CAAC,4CAA4C,EAAE,MAAM;MACrD,MAAMC,KAAK,GAAG,IAAIhB,gBAAgB,CAAC,YAAY,EAAE;QAC/CuB,IAAI,EAAE;MACR,CAAC,CAAC;MAEFN,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,YAAY,CAAC;MACjDJ,MAAM,CAACD,KAAK,CAACqB,mBAAmB,CAAC,CAAC,CAAC,CAAChB,IAAI,CAAC,0BAA0B,CAAC;IACtE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFP,QAAQ,CAAC,qBAAqB,EAAE,MAAM;IACpCC,EAAE,CAAC,uCAAuC,EAAE,MAAM;MAChD,MAAMC,KAAK,GAAG,IAAIf,mBAAmB,CAAC,aAAa,CAAC;MAEpDgB,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,CAAClB,gBAAgB,CAAC;MAC9CiB,MAAM,CAACD,KAAK,CAACI,IAAI,CAAC,CAACC,IAAI,CAAC,qBAAqB,CAAC;MAC9CJ,MAAM,CAACD,KAAK,CAACO,IAAI,CAAC,CAACF,IAAI,CAAC,YAAY,CAAC;MACrCJ,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,qEAAqE,CAAC;IAC5G,CAAC,CAAC;IAEFN,EAAE,CAAC,2CAA2C,EAAE,MAAM;MACpD,MAAMC,KAAK,GAAG,IAAId,uBAAuB,CAAC,CAAC;MAE3Ce,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,CAACjB,mBAAmB,CAAC;MACjDgB,MAAM,CAACD,KAAK,CAACI,IAAI,CAAC,CAACC,IAAI,CAAC,yBAAyB,CAAC;MAClDJ,MAAM,CAACD,KAAK,CAACO,IAAI,CAAC,CAACF,IAAI,CAAC,0BAA0B,CAAC;MACnDJ,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,0DAA0D,CAAC;IACjG,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFP,QAAQ,CAAC,iBAAiB,EAAE,MAAM;IAChCC,EAAE,CAAC,kCAAkC,EAAE,MAAM;MAC3C,MAAMC,KAAK,GAAG,IAAIb,eAAe,CAAC,mBAAmB,CAAC;MAEtDc,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,CAAClB,gBAAgB,CAAC;MAC9CiB,MAAM,CAACD,KAAK,CAACI,IAAI,CAAC,CAACC,IAAI,CAAC,iBAAiB,CAAC;MAC1CJ,MAAM,CAACD,KAAK,CAACO,IAAI,CAAC,CAACF,IAAI,CAAC,kBAAkB,CAAC;MAC3CJ,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,uFAAuF,CAAC;IAC9H,CAAC,CAAC;IAEFN,EAAE,CAAC,+BAA+B,EAAE,MAAM;MACxC,MAAMC,KAAK,GAAG,IAAIZ,YAAY,CAAC,CAAC;MAEhCa,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,CAACf,eAAe,CAAC;MAC7Cc,MAAM,CAACD,KAAK,CAACI,IAAI,CAAC,CAACC,IAAI,CAAC,cAAc,CAAC;MACvCJ,MAAM,CAACD,KAAK,CAACO,IAAI,CAAC,CAACF,IAAI,CAAC,oBAAoB,CAAC;MAC7CJ,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,yFAAyF,CAAC;IAChI,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFP,QAAQ,CAAC,iBAAiB,EAAE,MAAM;IAChCC,EAAE,CAAC,kCAAkC,EAAE,MAAM;MAC3C,MAAMC,KAAK,GAAG,IAAIX,eAAe,CAAC,mBAAmB,CAAC;MAEtDY,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,CAAClB,gBAAgB,CAAC;MAC9CiB,MAAM,CAACD,KAAK,CAACI,IAAI,CAAC,CAACC,IAAI,CAAC,iBAAiB,CAAC;MAC1CJ,MAAM,CAACD,KAAK,CAACO,IAAI,CAAC,CAACF,IAAI,CAAC,kBAAkB,CAAC;MAC3CJ,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,sEAAsE,CAAC;IAC7G,CAAC,CAAC;IAEFN,EAAE,CAAC,yDAAyD,EAAE,MAAM;MAClE,MAAMuB,gBAAgB,GAAG,CACvB;QAAEC,KAAK,EAAE,MAAM;QAAEjB,OAAO,EAAE;MAAmB,CAAC,EAC9C;QAAEiB,KAAK,EAAE,OAAO;QAAEjB,OAAO,EAAE;MAAmB,CAAC,CAChD;MAED,MAAMN,KAAK,GAAG,IAAIX,eAAe,CAAC,mBAAmB,EAAE;QACrDiC;MACF,CAAC,CAAC;MAEFrB,MAAM,CAACD,KAAK,CAACsB,gBAAgB,CAAC,CAACZ,OAAO,CAACY,gBAAgB,CAAC;MACxDrB,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,uDAAuD,CAAC;IAC9F,CAAC,CAAC;IAEFN,EAAE,CAAC,8CAA8C,EAAE,MAAM;MACvD,MAAMC,KAAK,GAAG,IAAIV,yBAAyB,CAAC,MAAM,CAAC;MAEnDW,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,CAACb,eAAe,CAAC;MAC7CY,MAAM,CAACD,KAAK,CAACI,IAAI,CAAC,CAACC,IAAI,CAAC,2BAA2B,CAAC;MACpDJ,MAAM,CAACD,KAAK,CAACO,IAAI,CAAC,CAACF,IAAI,CAAC,mCAAmC,CAAC;MAC5DJ,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,iDAAiD,CAAC;IACxF,CAAC,CAAC;IAEFN,EAAE,CAAC,mEAAmE,EAAE,MAAM;MAC5E,MAAMC,KAAK,GAAG,IAAIV,yBAAyB,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;MAE9DW,MAAM,CAACD,KAAK,CAACsB,gBAAgB,CAACE,MAAM,CAAC,CAACnB,IAAI,CAAC,CAAC,CAAC;MAC7CJ,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,gFAAgF,CAAC;IACvH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFP,QAAQ,CAAC,UAAU,EAAE,MAAM;IACzBC,EAAE,CAAC,4BAA4B,EAAE,MAAM;MACrC,MAAMC,KAAK,GAAG,IAAIT,QAAQ,CAAC,WAAW,CAAC;MAEvCU,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,CAAClB,gBAAgB,CAAC;MAC9CiB,MAAM,CAACD,KAAK,CAACI,IAAI,CAAC,CAACC,IAAI,CAAC,UAAU,CAAC;MACnCJ,MAAM,CAACD,KAAK,CAACO,IAAI,CAAC,CAACF,IAAI,CAAC,WAAW,CAAC;MACpCJ,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,0FAA0F,CAAC;IACjI,CAAC,CAAC;IAEFN,EAAE,CAAC,0DAA0D,EAAE,MAAM;MACnE,MAAMC,KAAK,GAAG,IAAIT,QAAQ,CAAC,WAAW,EAAE;QACtCkC,UAAU,EAAE,GAAG;QACfC,QAAQ,EAAE;UAAEpB,OAAO,EAAE;QAAc;MACrC,CAAC,CAAC;MAEFL,MAAM,CAACD,KAAK,CAACyB,UAAU,CAAC,CAACpB,IAAI,CAAC,GAAG,CAAC;MAClCJ,MAAM,CAACD,KAAK,CAAC0B,QAAQ,CAAC,CAAChB,OAAO,CAAC;QAAEJ,OAAO,EAAE;MAAc,CAAC,CAAC;MAE1D,MAAMW,IAAI,GAAGjB,KAAK,CAACkB,MAAM,CAAC,CAAC;MAC3BjB,MAAM,CAACgB,IAAI,CAACQ,UAAU,CAAC,CAACpB,IAAI,CAAC,GAAG,CAAC;MACjCJ,MAAM,CAACgB,IAAI,CAACS,QAAQ,CAAC,CAAChB,OAAO,CAAC;QAAEJ,OAAO,EAAE;MAAc,CAAC,CAAC;IAC3D,CAAC,CAAC;IAEFP,EAAE,CAAC,2CAA2C,EAAE,MAAM;MACpD,MAAMC,KAAK,GAAG,IAAIR,sBAAsB,CAAC,qBAAqB,EAAE;QAC9DmC,UAAU,EAAE;MACd,CAAC,CAAC;MAEF1B,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,CAACX,QAAQ,CAAC;MACtCU,MAAM,CAACD,KAAK,CAACI,IAAI,CAAC,CAACC,IAAI,CAAC,wBAAwB,CAAC;MACjDJ,MAAM,CAACD,KAAK,CAACO,IAAI,CAAC,CAACF,IAAI,CAAC,yBAAyB,CAAC;MAClDJ,MAAM,CAACD,KAAK,CAAC2B,UAAU,CAAC,CAACtB,IAAI,CAAC,EAAE,CAAC;MACjCJ,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,yDAAyD,CAAC;IAChG,CAAC,CAAC;IAEFN,EAAE,CAAC,0CAA0C,EAAE,MAAM;MACnD,MAAMC,KAAK,GAAG,IAAIP,qBAAqB,CAAC,MAAM,EAAE,KAAK,CAAC;MAEtDQ,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,CAACX,QAAQ,CAAC;MACtCU,MAAM,CAACD,KAAK,CAACI,IAAI,CAAC,CAACC,IAAI,CAAC,uBAAuB,CAAC;MAChDJ,MAAM,CAACD,KAAK,CAACO,IAAI,CAAC,CAACF,IAAI,CAAC,wBAAwB,CAAC;MACjDJ,MAAM,CAACD,KAAK,CAAC4B,YAAY,CAAC,CAACvB,IAAI,CAAC,MAAM,CAAC;MACvCJ,MAAM,CAACD,KAAK,CAAC6B,UAAU,CAAC,CAACxB,IAAI,CAAC,KAAK,CAAC;MACpCJ,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,wCAAwC,CAAC;IAC/E,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFP,QAAQ,CAAC,qBAAqB,EAAE,MAAM;IACpCC,EAAE,CAAC,sCAAsC,EAAE,MAAM;MAC/C,MAAMC,KAAK,GAAG,IAAIN,mBAAmB,CAAC,uBAAuB,CAAC;MAE9DO,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,CAAClB,gBAAgB,CAAC;MAC9CiB,MAAM,CAACD,KAAK,CAACI,IAAI,CAAC,CAACC,IAAI,CAAC,qBAAqB,CAAC;MAC9CJ,MAAM,CAACD,KAAK,CAACO,IAAI,CAAC,CAACF,IAAI,CAAC,sBAAsB,CAAC;MAC/CJ,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,4FAA4F,CAAC;IACnI,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFP,QAAQ,CAAC,gBAAgB,EAAE,MAAM;IAC/BC,EAAE,CAAC,iCAAiC,EAAE,MAAM;MAC1C,MAAMC,KAAK,GAAG,IAAIL,cAAc,CAAC,iBAAiB,EAAE;QAClDmC,WAAW,EAAE;MACf,CAAC,CAAC;MAEF7B,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,CAAClB,gBAAgB,CAAC;MAC9CiB,MAAM,CAACD,KAAK,CAACI,IAAI,CAAC,CAACC,IAAI,CAAC,gBAAgB,CAAC;MACzCJ,MAAM,CAACD,KAAK,CAACO,IAAI,CAAC,CAACF,IAAI,CAAC,iBAAiB,CAAC;MAC1CJ,MAAM,CAACD,KAAK,CAAC8B,WAAW,CAAC,CAACzB,IAAI,CAAC,gBAAgB,CAAC;MAChDJ,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,wGAAwG,CAAC;IAC/I,CAAC,CAAC;IAEFN,EAAE,CAAC,2CAA2C,EAAE,MAAM;MACpD,MAAMC,KAAK,GAAG,IAAIJ,sBAAsB,CAAC,gBAAgB,CAAC;MAE1DK,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,CAACP,cAAc,CAAC;MAC5CM,MAAM,CAACD,KAAK,CAACI,IAAI,CAAC,CAACC,IAAI,CAAC,wBAAwB,CAAC;MACjDJ,MAAM,CAACD,KAAK,CAACO,IAAI,CAAC,CAACF,IAAI,CAAC,qBAAqB,CAAC;MAC9CJ,MAAM,CAACD,KAAK,CAAC8B,WAAW,CAAC,CAACzB,IAAI,CAAC,gBAAgB,CAAC;MAChDJ,MAAM,CAACD,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,4FAA4F,CAAC;IACnI,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}