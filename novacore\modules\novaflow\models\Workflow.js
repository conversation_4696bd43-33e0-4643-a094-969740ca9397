/**
 * NovaCore Workflow Model
 * 
 * This model defines the schema for workflows in the NovaFlow module.
 * NovaFlow is the Universal Compliance Workflow Orchestrator (UCWO) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define task schema
const taskSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  type: { 
    type: String, 
    enum: [
      'manual', 
      'automated', 
      'approval', 
      'notification', 
      'integration', 
      'decision', 
      'evidence_collection',
      'assessment',
      'verification',
      'reporting'
    ], 
    default: 'manual' 
  },
  status: { 
    type: String, 
    enum: [
      'pending', 
      'in_progress', 
      'completed', 
      'failed', 
      'skipped', 
      'blocked'
    ], 
    default: 'pending' 
  },
  priority: { 
    type: String, 
    enum: ['low', 'medium', 'high', 'critical'], 
    default: 'medium' 
  },
  dueDate: { 
    type: Date 
  },
  assignedTo: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  assignedRole: { 
    type: String, 
    trim: true 
  },
  dependencies: [{ 
    type: String, 
    trim: true 
  }],
  completionCriteria: { 
    type: String, 
    enum: [
      'manual', 
      'automatic', 
      'approval_required', 
      'evidence_required', 
      'condition'
    ], 
    default: 'manual' 
  },
  completionCondition: { 
    type: String, 
    trim: true 
  },
  automationConfig: {
    serviceType: { 
      type: String, 
      trim: true 
    },
    actionName: { 
      type: String, 
      trim: true 
    },
    parameters: { 
      type: Map, 
      of: Schema.Types.Mixed 
    },
    retryConfig: {
      maxRetries: { 
        type: Number, 
        default: 3 
      },
      retryInterval: { 
        type: Number, 
        default: 300 // seconds
      }
    }
  },
  integrationConfig: {
    connectorId: { 
      type: String, 
      trim: true 
    },
    endpoint: { 
      type: String, 
      trim: true 
    },
    method: { 
      type: String, 
      enum: ['GET', 'POST', 'PUT', 'DELETE'], 
      default: 'GET' 
    },
    headers: { 
      type: Map, 
      of: String 
    },
    payload: { 
      type: Schema.Types.Mixed 
    },
    responseMapping: { 
      type: Map, 
      of: String 
    }
  },
  evidenceConfig: {
    evidenceTypeId: { 
      type: String, 
      trim: true 
    },
    sourceId: { 
      type: String, 
      trim: true 
    },
    controlId: { 
      type: String, 
      trim: true 
    },
    verificationRequired: { 
      type: Boolean, 
      default: false 
    }
  },
  notificationConfig: {
    templateId: { 
      type: String, 
      trim: true 
    },
    channels: [{ 
      type: String, 
      enum: ['email', 'sms', 'in_app', 'webhook'], 
      default: 'email' 
    }],
    recipients: [{ 
      type: String, 
      trim: true 
    }],
    recipientRoles: [{ 
      type: String, 
      trim: true 
    }]
  },
  decisionConfig: {
    conditions: [{
      field: { 
        type: String, 
        trim: true 
      },
      operator: { 
        type: String, 
        enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than'], 
        default: 'equals' 
      },
      value: { 
        type: Schema.Types.Mixed 
      },
      nextTaskId: { 
        type: String, 
        trim: true 
      }
    }],
    defaultNextTaskId: { 
      type: String, 
      trim: true 
    }
  },
  startedAt: { 
    type: Date 
  },
  completedAt: { 
    type: Date 
  },
  result: { 
    type: Schema.Types.Mixed 
  },
  notes: { 
    type: String, 
    trim: true 
  },
  tags: [{ 
    type: String, 
    trim: true 
  }]
}, { _id: false });

// Define stage schema
const stageSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  order: { 
    type: Number, 
    default: 0 
  },
  status: { 
    type: String, 
    enum: [
      'pending', 
      'in_progress', 
      'completed', 
      'failed', 
      'skipped'
    ], 
    default: 'pending' 
  },
  tasks: [taskSchema],
  completionCriteria: { 
    type: String, 
    enum: [
      'all_tasks', 
      'any_task', 
      'specific_tasks', 
      'percentage'
    ], 
    default: 'all_tasks' 
  },
  completionConfig: {
    taskIds: [{ 
      type: String, 
      trim: true 
    }],
    percentage: { 
      type: Number, 
      min: 0, 
      max: 100 
    }
  },
  startedAt: { 
    type: Date 
  },
  completedAt: { 
    type: Date 
  },
  dueDate: { 
    type: Date 
  }
}, { _id: false });

// Define trigger schema
const triggerSchema = new Schema({
  type: { 
    type: String, 
    enum: [
      'manual', 
      'scheduled', 
      'event', 
      'api', 
      'condition'
    ], 
    default: 'manual' 
  },
  config: {
    schedule: { 
      type: String, 
      trim: true // cron expression
    },
    eventType: { 
      type: String, 
      trim: true 
    },
    eventSource: { 
      type: String, 
      trim: true 
    },
    condition: { 
      type: String, 
      trim: true 
    },
    apiEndpoint: { 
      type: String, 
      trim: true 
    }
  },
  enabled: { 
    type: Boolean, 
    default: true 
  },
  lastTriggeredAt: { 
    type: Date 
  }
}, { _id: false });

// Define workflow schema
const workflowSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  organizationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Organization', 
    required: true 
  },
  type: { 
    type: String, 
    enum: [
      'compliance', 
      'assessment', 
      'evidence_collection', 
      'remediation', 
      'certification', 
      'audit', 
      'custom'
    ], 
    default: 'compliance' 
  },
  category: { 
    type: String, 
    trim: true 
  },
  status: { 
    type: String, 
    enum: [
      'draft', 
      'active', 
      'inactive', 
      'archived'
    ], 
    default: 'draft' 
  },
  executionStatus: { 
    type: String, 
    enum: [
      'not_started', 
      'in_progress', 
      'completed', 
      'failed', 
      'paused'
    ], 
    default: 'not_started' 
  },
  priority: { 
    type: String, 
    enum: ['low', 'medium', 'high', 'critical'], 
    default: 'medium' 
  },
  stages: [stageSchema],
  triggers: [triggerSchema],
  dataModel: { 
    type: Map, 
    of: Schema.Types.Mixed 
  },
  variables: { 
    type: Map, 
    of: Schema.Types.Mixed 
  },
  integrations: [{
    name: { 
      type: String, 
      required: true, 
      trim: true 
    },
    type: { 
      type: String, 
      required: true, 
      trim: true 
    },
    config: { 
      type: Map, 
      of: Schema.Types.Mixed 
    },
    status: { 
      type: String, 
      enum: ['active', 'inactive', 'failed'], 
      default: 'active' 
    }
  }],
  permissions: [{
    role: { 
      type: String, 
      required: true, 
      trim: true 
    },
    actions: [{ 
      type: String, 
      enum: ['view', 'edit', 'execute', 'manage'], 
      default: 'view' 
    }]
  }],
  startDate: { 
    type: Date 
  },
  endDate: { 
    type: Date 
  },
  dueDate: { 
    type: Date 
  },
  completionPercentage: { 
    type: Number, 
    min: 0, 
    max: 100, 
    default: 0 
  },
  tags: [{ 
    type: String, 
    trim: true 
  }],
  frameworks: [{ 
    type: String, 
    trim: true 
  }],
  relatedEntities: [{
    entityType: { 
      type: String, 
      required: true, 
      trim: true 
    },
    entityId: { 
      type: Schema.Types.ObjectId, 
      required: true 
    }
  }],
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
workflowSchema.index({ organizationId: 1 });
workflowSchema.index({ name: 1, organizationId: 1 });
workflowSchema.index({ type: 1 });
workflowSchema.index({ category: 1 });
workflowSchema.index({ status: 1 });
workflowSchema.index({ executionStatus: 1 });
workflowSchema.index({ priority: 1 });
workflowSchema.index({ 'stages.status': 1 });
workflowSchema.index({ 'stages.tasks.status': 1 });
workflowSchema.index({ 'stages.tasks.assignedTo': 1 });
workflowSchema.index({ tags: 1 });
workflowSchema.index({ frameworks: 1 });
workflowSchema.index({ 'relatedEntities.entityType': 1, 'relatedEntities.entityId': 1 });
workflowSchema.index({ startDate: 1 });
workflowSchema.index({ endDate: 1 });
workflowSchema.index({ dueDate: 1 });
workflowSchema.index({ createdAt: 1 });

// Add methods
workflowSchema.methods.isActive = function() {
  return this.status === 'active';
};

workflowSchema.methods.isCompleted = function() {
  return this.executionStatus === 'completed';
};

workflowSchema.methods.getStage = function(stageId) {
  return this.stages.find(stage => stage.id === stageId);
};

workflowSchema.methods.getTask = function(taskId) {
  for (const stage of this.stages) {
    const task = stage.tasks.find(task => task.id === taskId);
    if (task) {
      return { stage, task };
    }
  }
  return null;
};

workflowSchema.methods.calculateCompletionPercentage = function() {
  if (!this.stages || this.stages.length === 0) {
    return 0;
  }
  
  const totalTasks = this.stages.reduce((sum, stage) => sum + stage.tasks.length, 0);
  
  if (totalTasks === 0) {
    return 0;
  }
  
  const completedTasks = this.stages.reduce((sum, stage) => {
    return sum + stage.tasks.filter(task => task.status === 'completed').length;
  }, 0);
  
  return Math.round((completedTasks / totalTasks) * 100);
};

workflowSchema.methods.getNextTasks = function() {
  const nextTasks = [];
  
  for (const stage of this.stages) {
    if (stage.status === 'in_progress') {
      for (const task of stage.tasks) {
        if (task.status === 'pending') {
          // Check if dependencies are met
          const dependenciesMet = task.dependencies.every(depTaskId => {
            const { task: depTask } = this.getTask(depTaskId) || {};
            return depTask && depTask.status === 'completed';
          });
          
          if (dependenciesMet) {
            nextTasks.push({ stageId: stage.id, taskId: task.id });
          }
        }
      }
    }
  }
  
  return nextTasks;
};

// Add statics
workflowSchema.statics.findByOrganization = function(organizationId) {
  return this.find({ organizationId });
};

workflowSchema.statics.findActive = function(organizationId) {
  return this.find({ 
    organizationId, 
    status: 'active' 
  });
};

workflowSchema.statics.findByType = function(organizationId, type) {
  return this.find({ 
    organizationId, 
    type 
  });
};

workflowSchema.statics.findByFramework = function(organizationId, framework) {
  return this.find({ 
    organizationId, 
    frameworks: framework 
  });
};

workflowSchema.statics.findByRelatedEntity = function(entityType, entityId) {
  return this.find({ 
    'relatedEntities.entityType': entityType, 
    'relatedEntities.entityId': entityId 
  });
};

workflowSchema.statics.findDue = function(organizationId, date = new Date()) {
  return this.find({ 
    organizationId, 
    status: 'active',
    dueDate: { $lte: date }
  });
};

// Create model
const Workflow = mongoose.model('Workflow', workflowSchema);

module.exports = Workflow;
