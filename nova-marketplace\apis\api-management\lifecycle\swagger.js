/**
 * @swagger
 * tags:
 *   name: API Lifecycle
 *   description: API Lifecycle Management
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     API:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the API
 *         name:
 *           type: string
 *           description: Name of the API
 *         description:
 *           type: string
 *           description: Description of the API
 *         version:
 *           type: string
 *           description: Current version of the API
 *         status:
 *           type: string
 *           enum: [planning, development, testing, staging, production, deprecated, retired]
 *           description: Current status of the API
 *         type:
 *           type: string
 *           enum: [rest, graphql, soap, grpc, webhook]
 *           description: Type of API
 *         owner:
 *           type: string
 *           description: Owner of the API
 *         team:
 *           type: string
 *           description: Team responsible for the API
 *         baseUrl:
 *           type: string
 *           description: Base URL of the API
 *         documentation:
 *           type: string
 *           description: URL to the API documentation
 *         repository:
 *           type: string
 *           description: URL to the API source code repository
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           description: Tags associated with the API
 *         versions:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/APIVersion'
 *         dependencies:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/APIDependency'
 *         consumers:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/APIConsumer'
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the API was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the API was last updated
 *       required:
 *         - id
 *         - name
 *         - version
 *         - status
 *         - type
 *         - owner
 *         - createdAt
 *         - updatedAt
 *     
 *     APIInput:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Name of the API
 *         description:
 *           type: string
 *           description: Description of the API
 *         version:
 *           type: string
 *           description: Current version of the API
 *         status:
 *           type: string
 *           enum: [planning, development, testing, staging, production, deprecated, retired]
 *           description: Current status of the API
 *         type:
 *           type: string
 *           enum: [rest, graphql, soap, grpc, webhook]
 *           description: Type of API
 *         owner:
 *           type: string
 *           description: Owner of the API
 *         team:
 *           type: string
 *           description: Team responsible for the API
 *         baseUrl:
 *           type: string
 *           description: Base URL of the API
 *         documentation:
 *           type: string
 *           description: URL to the API documentation
 *         repository:
 *           type: string
 *           description: URL to the API source code repository
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           description: Tags associated with the API
 *       required:
 *         - name
 *         - version
 *         - status
 *         - type
 *         - owner
 *     
 *     APIVersion:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the API version
 *         apiId:
 *           type: string
 *           description: ID of the API this version belongs to
 *         version:
 *           type: string
 *           description: Version number or identifier
 *         status:
 *           type: string
 *           enum: [planning, development, testing, staging, production, deprecated, retired]
 *           description: Status of this version
 *         releaseDate:
 *           type: string
 *           format: date
 *           description: Date when this version was released
 *         endOfLifeDate:
 *           type: string
 *           format: date
 *           description: Date when this version will be retired
 *         changeLog:
 *           type: string
 *           description: Description of changes in this version
 *         baseUrl:
 *           type: string
 *           description: Base URL for this version
 *         documentation:
 *           type: string
 *           description: URL to the documentation for this version
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the version was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the version was last updated
 *       required:
 *         - id
 *         - apiId
 *         - version
 *         - status
 *         - createdAt
 *         - updatedAt
 *     
 *     APIVersionInput:
 *       type: object
 *       properties:
 *         version:
 *           type: string
 *           description: Version number or identifier
 *         status:
 *           type: string
 *           enum: [planning, development, testing, staging, production, deprecated, retired]
 *           description: Status of this version
 *         releaseDate:
 *           type: string
 *           format: date
 *           description: Date when this version was released
 *         endOfLifeDate:
 *           type: string
 *           format: date
 *           description: Date when this version will be retired
 *         changeLog:
 *           type: string
 *           description: Description of changes in this version
 *         baseUrl:
 *           type: string
 *           description: Base URL for this version
 *         documentation:
 *           type: string
 *           description: URL to the documentation for this version
 *       required:
 *         - version
 *         - status
 *     
 *     APIDependency:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the dependency
 *         apiId:
 *           type: string
 *           description: ID of the API that has the dependency
 *         dependencyType:
 *           type: string
 *           enum: [api, service, database, library, other]
 *           description: Type of dependency
 *         name:
 *           type: string
 *           description: Name of the dependency
 *         version:
 *           type: string
 *           description: Version of the dependency
 *         description:
 *           type: string
 *           description: Description of the dependency
 *         criticality:
 *           type: string
 *           enum: [critical, high, medium, low]
 *           description: Criticality of the dependency
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the dependency was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the dependency was last updated
 *       required:
 *         - id
 *         - apiId
 *         - dependencyType
 *         - name
 *         - createdAt
 *         - updatedAt
 *     
 *     APIDependencyInput:
 *       type: object
 *       properties:
 *         dependencyType:
 *           type: string
 *           enum: [api, service, database, library, other]
 *           description: Type of dependency
 *         name:
 *           type: string
 *           description: Name of the dependency
 *         version:
 *           type: string
 *           description: Version of the dependency
 *         description:
 *           type: string
 *           description: Description of the dependency
 *         criticality:
 *           type: string
 *           enum: [critical, high, medium, low]
 *           description: Criticality of the dependency
 *       required:
 *         - dependencyType
 *         - name
 *     
 *     APIConsumer:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the consumer
 *         apiId:
 *           type: string
 *           description: ID of the API being consumed
 *         name:
 *           type: string
 *           description: Name of the consumer
 *         type:
 *           type: string
 *           enum: [internal, external, partner]
 *           description: Type of consumer
 *         contact:
 *           type: string
 *           description: Contact information for the consumer
 *         usageLevel:
 *           type: string
 *           enum: [high, medium, low]
 *           description: Level of API usage by this consumer
 *         apiVersions:
 *           type: array
 *           items:
 *             type: string
 *           description: Versions of the API being used by this consumer
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the consumer was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the consumer was last updated
 *       required:
 *         - id
 *         - apiId
 *         - name
 *         - type
 *         - createdAt
 *         - updatedAt
 *     
 *     APIConsumerInput:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Name of the consumer
 *         type:
 *           type: string
 *           enum: [internal, external, partner]
 *           description: Type of consumer
 *         contact:
 *           type: string
 *           description: Contact information for the consumer
 *         usageLevel:
 *           type: string
 *           enum: [high, medium, low]
 *           description: Level of API usage by this consumer
 *         apiVersions:
 *           type: array
 *           items:
 *             type: string
 *           description: Versions of the API being used by this consumer
 *       required:
 *         - name
 *         - type
 */
