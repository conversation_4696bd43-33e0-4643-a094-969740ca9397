{"manifest_version": 3, "name": "NovaBrowser - Coherence-First Web Gateway", "version": "1.0.0", "description": "Real-time consciousness validation, accessibility auto-remediation, and coherence analysis for every webpage.", "permissions": ["activeTab", "storage", "webRequest", "webRequestBlocking", "scripting", "tabs"], "host_permissions": ["<all_urls>", "http://localhost:8090/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["nova-agent.js", "coherence-analyzer.js", "novavision-overlay.js", "content-script.js"], "css": ["novabrowser-overlay.css"], "run_at": "document_start"}], "action": {"default_popup": "popup.html", "default_title": "NovaBrowser Controls", "default_icon": {"16": "icons/nova-16.png", "32": "icons/nova-32.png", "48": "icons/nova-48.png", "128": "icons/nova-128.png"}}, "icons": {"16": "icons/nova-16.png", "32": "icons/nova-32.png", "48": "icons/nova-48.png", "128": "icons/nova-128.png"}, "web_accessible_resources": [{"resources": ["nova-agent-wasm.wasm", "coherence-overlay.html", "novavision-panel.html"], "matches": ["<all_urls>"]}], "options_page": "options.html", "declarative_net_request": {"rule_resources": [{"id": "coherence_filter", "enabled": true, "path": "coherence-rules.json"}]}}