import { 
  login, 
  register, 
  logout, 
  getCurrentUser, 
  forgotPassword, 
  resetPassword 
} from '../../services/authService';

// Mock fetch
global.fetch = jest.fn();

// Mock localStorage
const localStorageMock = (() => {
  let store = {};
  return {
    getItem: jest.fn(key => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn(key => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    })
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('authService', () => {
  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
    localStorageMock.clear();
    
    // Mock successful fetch response for login
    global.fetch.mockResolvedValue({
      ok: true,
      json: jest.fn().mockResolvedValue({
        user: { id: '123', name: 'Test User', email: '<EMAIL>' },
        token: 'test-token'
      })
    });
  });
  
  describe('login', () => {
    it('logs in user correctly', async () => {
      const result = await login('<EMAIL>', 'password');
      
      // Check if fetch was called with correct URL and body
      expect(global.fetch).toHaveBeenCalledWith('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password'
        })
      });
      
      // Check if result has correct structure
      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('token', 'test-token');
      expect(result.user).toHaveProperty('id', '123');
      expect(result.user).toHaveProperty('name', 'Test User');
      expect(result.user).toHaveProperty('email', '<EMAIL>');
      
      // Check if token was stored in localStorage
      expect(localStorageMock.setItem).toHaveBeenCalledWith('token', 'test-token');
    });
    
    it('handles login errors correctly', async () => {
      // Mock fetch to return error response
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        json: jest.fn().mockResolvedValue({
          message: 'Invalid credentials'
        })
      });
      
      // Call should throw an error
      await expect(login('<EMAIL>', 'wrong-password')).rejects.toThrow('Invalid credentials');
      
      // Token should not be stored in localStorage
      expect(localStorageMock.setItem).not.toHaveBeenCalled();
    });
  });
  
  describe('register', () => {
    it('registers user correctly', async () => {
      // Mock successful fetch response for register
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          user: { id: '123', name: 'New User', email: '<EMAIL>' },
          token: 'new-token'
        })
      });
      
      const result = await register('New User', '<EMAIL>', 'password');
      
      // Check if fetch was called with correct URL and body
      expect(global.fetch).toHaveBeenCalledWith('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: 'New User',
          email: '<EMAIL>',
          password: 'password'
        })
      });
      
      // Check if result has correct structure
      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('token', 'new-token');
      expect(result.user).toHaveProperty('id', '123');
      expect(result.user).toHaveProperty('name', 'New User');
      expect(result.user).toHaveProperty('email', '<EMAIL>');
      
      // Check if token was stored in localStorage
      expect(localStorageMock.setItem).toHaveBeenCalledWith('token', 'new-token');
    });
    
    it('handles registration errors correctly', async () => {
      // Mock fetch to return error response
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: jest.fn().mockResolvedValue({
          message: 'Email already in use'
        })
      });
      
      // Call should throw an error
      await expect(register('New User', '<EMAIL>', 'password')).rejects.toThrow('Email already in use');
      
      // Token should not be stored in localStorage
      expect(localStorageMock.setItem).not.toHaveBeenCalled();
    });
  });
  
  describe('logout', () => {
    it('logs out user correctly', async () => {
      // Set token in localStorage
      localStorageMock.setItem('token', 'test-token');
      
      const result = await logout();
      
      // Check if token was removed from localStorage
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('token');
      
      // Check if result is true
      expect(result).toBe(true);
    });
  });
  
  describe('getCurrentUser', () => {
    it('gets current user correctly when token exists', async () => {
      // Set token in localStorage
      localStorageMock.setItem('token', 'test-token');
      
      // Mock successful fetch response for getCurrentUser
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          id: '123',
          name: 'Test User',
          email: '<EMAIL>'
        })
      });
      
      const result = await getCurrentUser();
      
      // Check if fetch was called with correct URL and headers
      expect(global.fetch).toHaveBeenCalledWith('/api/auth/me', {
        headers: {
          'Authorization': 'Bearer test-token'
        }
      });
      
      // Check if result has correct structure
      expect(result).toHaveProperty('id', '123');
      expect(result).toHaveProperty('name', 'Test User');
      expect(result).toHaveProperty('email', '<EMAIL>');
    });
    
    it('returns null when no token exists', async () => {
      // No token in localStorage
      
      const result = await getCurrentUser();
      
      // Check if fetch was not called
      expect(global.fetch).not.toHaveBeenCalled();
      
      // Check if result is null
      expect(result).toBeNull();
    });
    
    it('handles API errors correctly', async () => {
      // Set token in localStorage
      localStorageMock.setItem('token', 'invalid-token');
      
      // Mock fetch to return error response
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      });
      
      const result = await getCurrentUser();
      
      // Check if token was removed from localStorage
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('token');
      
      // Check if result is null
      expect(result).toBeNull();
    });
  });
  
  describe('forgotPassword', () => {
    it('sends forgot password request correctly', async () => {
      // Mock successful fetch response for forgotPassword
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          success: true,
          message: 'Password reset email sent'
        })
      });
      
      const result = await forgotPassword('<EMAIL>');
      
      // Check if fetch was called with correct URL and body
      expect(global.fetch).toHaveBeenCalledWith('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>'
        })
      });
      
      // Check if result has correct structure
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('message', 'Password reset email sent');
    });
    
    it('handles API errors correctly', async () => {
      // Mock fetch to return error response
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        json: jest.fn().mockResolvedValue({
          message: 'Email not found'
        })
      });
      
      // Call should throw an error
      await expect(forgotPassword('<EMAIL>')).rejects.toThrow('Email not found');
    });
  });
  
  describe('resetPassword', () => {
    it('resets password correctly', async () => {
      // Mock successful fetch response for resetPassword
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          success: true,
          message: 'Password reset successful'
        })
      });
      
      const result = await resetPassword('reset-token', 'new-password');
      
      // Check if fetch was called with correct URL and body
      expect(global.fetch).toHaveBeenCalledWith('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: 'reset-token',
          password: 'new-password'
        })
      });
      
      // Check if result has correct structure
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('message', 'Password reset successful');
    });
    
    it('handles API errors correctly', async () => {
      // Mock fetch to return error response
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: jest.fn().mockResolvedValue({
          message: 'Invalid or expired token'
        })
      });
      
      // Call should throw an error
      await expect(resetPassword('invalid-token', 'new-password')).rejects.toThrow('Invalid or expired token');
    });
  });
});
