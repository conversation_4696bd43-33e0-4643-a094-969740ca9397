/**
 * Integration Registry Service
 * 
 * This service manages the registry of available integrations.
 */

const { Integration } = require('../models');

/**
 * Get all registered integrations
 * @returns {Promise<Array>} Array of integrations
 */
const getAllIntegrations = async () => {
  return await Integration.find();
};

/**
 * Get a specific integration by ID
 * @param {string} id - Integration ID
 * @returns {Promise<Object>} Integration object
 */
const getIntegrationById = async (id) => {
  const integration = await Integration.findOne({ id });
  
  if (!integration) {
    const error = new Error(`Integration with ID '${id}' not found`);
    error.name = 'NotFoundError';
    throw error;
  }
  
  return integration;
};

/**
 * Register a new integration
 * @param {Object} integrationData - Integration data
 * @returns {Promise<Object>} Created integration
 */
const registerIntegration = async (integrationData) => {
  // Check if integration with the same ID already exists
  const existingIntegration = await Integration.findOne({ id: integrationData.id });
  
  if (existingIntegration) {
    const error = new Error(`Integration with ID '${integrationData.id}' already exists`);
    error.name = 'ConflictError';
    throw error;
  }
  
  const integration = new Integration(integrationData);
  await integration.save();
  
  return integration;
};

/**
 * Update an integration
 * @param {string} id - Integration ID
 * @param {Object} integrationData - Updated integration data
 * @returns {Promise<Object>} Updated integration
 */
const updateIntegration = async (id, integrationData) => {
  const integration = await getIntegrationById(id);
  
  // Update only the fields that are provided in the integration data
  Object.keys(integrationData).forEach(key => {
    // Don't allow changing the ID
    if (key !== 'id') {
      integration[key] = integrationData[key];
    }
  });
  
  await integration.save();
  
  return integration;
};

/**
 * Delete an integration
 * @param {string} id - Integration ID
 * @returns {Promise<void>}
 */
const deleteIntegration = async (id) => {
  const integration = await getIntegrationById(id);
  await integration.remove();
};

/**
 * Configure an integration
 * @param {string} id - Integration ID
 * @param {Object} config - Integration configuration
 * @returns {Promise<Object>} Updated integration
 */
const configureIntegration = async (id, config) => {
  const integration = await getIntegrationById(id);
  
  // In a real implementation, this would validate the configuration against the schema
  integration.config = config;
  integration.status = 'active';
  
  await integration.save();
  
  return integration;
};

/**
 * Check if an integration supports a specific action
 * @param {string} id - Integration ID
 * @param {string} action - Action to check
 * @returns {Promise<boolean>} Whether the integration supports the action
 */
const supportsAction = async (id, action) => {
  const integration = await getIntegrationById(id);
  return integration.capabilities.includes(action);
};

/**
 * Get the handler for a specific integration action
 * @param {string} id - Integration ID
 * @param {string} action - Action to get handler for
 * @returns {Promise<Object>} Handler object with endpoint and method
 */
const getActionHandler = async (id, action) => {
  const integration = await getIntegrationById(id);
  
  if (!integration.capabilities.includes(action)) {
    const error = new Error(`Integration does not support action '${action}'`);
    error.name = 'ValidationError';
    throw error;
  }
  
  return integration.handlers[action];
};

module.exports = {
  getAllIntegrations,
  getIntegrationById,
  registerIntegration,
  updateIntegration,
  deleteIntegration,
  configureIntegration,
  supportsAction,
  getActionHandler
};
