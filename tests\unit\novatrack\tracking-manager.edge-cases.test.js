/**
 * Edge Case Tests for NovaTrack TrackingManager
 * 
 * These tests focus on edge cases and error handling
 */

const { describe, it, beforeEach, afterEach, expect } = require('@jest/globals');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import test environment configuration
const testEnv = require('../../setup/test-environment');

// Import the TrackingManager mock
const { TrackingManager } = require('../../mocks/tracking-manager.mock');

// Import test data generator
const { 
  generateRequirement, 
  generateActivity
} = require('../../data/novatrack-test-data');

describe('NovaTrack TrackingManager - Edge Cases', () => {
  let trackingManager;
  let tempDir;
  
  beforeEach(() => {
    // Create a temporary directory for test data
    if (testEnv.isDocker) {
      // In Docker, use a fixed path
      tempDir = '/app/tmp/novatrack-edge-test';
      // Ensure the directory exists
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
    } else {
      // Locally, use a temporary directory
      tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'novatrack-edge-test-'));
    }
    
    // Initialize the TrackingManager with the temporary directory
    trackingManager = new TrackingManager(tempDir);
    
    console.log(`Running edge case tests in ${testEnv.isDocker ? 'Docker' : 'local'} environment`);
    console.log(`Using test directory: ${tempDir}`);
  });
  
  afterEach(() => {
    // Clean up the temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });
  
  describe('Error Handling', () => {
    it('should handle file system errors gracefully', () => {
      // Create a TrackingManager with a non-writable directory
      const nonWritableDir = path.join(tempDir, 'non-writable');
      
      // Create the directory
      fs.mkdirSync(nonWritableDir, { recursive: true });
      
      // Make the directory read-only (this is a best effort, may not work on all systems)
      try {
        fs.chmodSync(nonWritableDir, 0o444);
      } catch (error) {
        console.warn('Could not change directory permissions, skipping test');
        return;
      }
      
      // Create a TrackingManager with the read-only directory
      const readOnlyManager = new TrackingManager(nonWritableDir);
      
      // Generate a requirement
      const requirementData = generateRequirement();
      
      // Attempt to create a requirement (this should handle the error gracefully)
      try {
        readOnlyManager.create_requirement(requirementData);
        // If we get here, the test should fail
        expect(true).toBe(false);
      } catch (error) {
        // We expect an error, but it should be handled gracefully
        expect(error).toBeDefined();
        expect(error instanceof Error).toBe(true);
      }
    });
    
    it('should handle invalid JSON data gracefully', () => {
      // Create a requirement
      const requirementData = generateRequirement();
      const requirement = trackingManager.create_requirement(requirementData);
      
      // Write invalid JSON to the requirement file
      const requirementFilePath = path.join(tempDir, `${requirement.id}.json`);
      fs.writeFileSync(requirementFilePath, 'This is not valid JSON');
      
      // Attempt to get the requirement (this should handle the error gracefully)
      try {
        trackingManager.get_requirement(requirement.id);
        // If we get here, the test should fail
        expect(true).toBe(false);
      } catch (error) {
        // We expect an error, but it should be handled gracefully
        expect(error).toBeDefined();
        expect(error instanceof Error).toBe(true);
      }
    });
  });
  
  describe('Edge Cases', () => {
    it('should handle empty requirement data', () => {
      // Attempt to create a requirement with empty data
      try {
        trackingManager.create_requirement({});
        // If we get here, the test should fail
        expect(true).toBe(false);
      } catch (error) {
        // We expect an error about missing name
        expect(error).toBeDefined();
        expect(error.message).toContain('name is required');
      }
    });
    
    it('should handle empty activity data', () => {
      // Attempt to create an activity with empty data
      try {
        trackingManager.create_activity({});
        // If we get here, the test should fail
        expect(true).toBe(false);
      } catch (error) {
        // We expect an error about missing name
        expect(error).toBeDefined();
        expect(error.message).toContain('name is required');
      }
    });
    
    it('should handle very large requirement data', () => {
      // Generate a requirement with a very large description
      const largeDescription = 'A'.repeat(1000000); // 1MB of data
      const requirementData = generateRequirement({
        description: largeDescription
      });
      
      // Create the requirement
      const requirement = trackingManager.create_requirement(requirementData);
      
      // Verify the requirement was created correctly
      expect(requirement).toBeDefined();
      expect(requirement.id).toBeDefined();
      expect(requirement.description).toBe(largeDescription);
      
      // Verify the requirement can be retrieved
      const retrievedRequirement = trackingManager.get_requirement(requirement.id);
      expect(retrievedRequirement).toEqual(requirement);
    });
    
    it('should handle special characters in requirement names', () => {
      // Generate a requirement with special characters in the name
      const specialName = 'Requirement with special characters: !@#$%^&*()_+{}|:"<>?~`-=[]\\;\',./';
      const requirementData = generateRequirement({
        name: specialName
      });
      
      // Create the requirement
      const requirement = trackingManager.create_requirement(requirementData);
      
      // Verify the requirement was created correctly
      expect(requirement).toBeDefined();
      expect(requirement.id).toBeDefined();
      expect(requirement.name).toBe(specialName);
      
      // Verify the requirement can be retrieved
      const retrievedRequirement = trackingManager.get_requirement(requirement.id);
      expect(retrievedRequirement).toEqual(requirement);
    });
    
    it('should handle concurrent operations', async () => {
      // Create multiple requirements concurrently
      const requirementCount = 10;
      const requirementPromises = [];
      
      for (let i = 0; i < requirementCount; i++) {
        const requirementData = generateRequirement({
          name: `Concurrent Requirement ${i}`
        });
        
        requirementPromises.push(
          new Promise(resolve => {
            const requirement = trackingManager.create_requirement(requirementData);
            resolve(requirement);
          })
        );
      }
      
      // Wait for all requirements to be created
      const requirements = await Promise.all(requirementPromises);
      
      // Verify all requirements were created correctly
      expect(requirements.length).toBe(requirementCount);
      
      // Verify all requirements can be retrieved
      for (const requirement of requirements) {
        const retrievedRequirement = trackingManager.get_requirement(requirement.id);
        expect(retrievedRequirement).toEqual(requirement);
      }
    });
  });
  
  describe('Boundary Conditions', () => {
    it('should handle minimum valid requirement data', () => {
      // Create a requirement with only the required name field
      const minimalRequirement = {
        name: 'Minimal Requirement'
      };
      
      // Create the requirement
      const requirement = trackingManager.create_requirement(minimalRequirement);
      
      // Verify the requirement was created correctly
      expect(requirement).toBeDefined();
      expect(requirement.id).toBeDefined();
      expect(requirement.name).toBe(minimalRequirement.name);
      
      // Verify default values were set for optional fields
      expect(requirement.description).toBe('');
      expect(requirement.framework).toBe('');
      expect(requirement.category).toBe('');
      expect(requirement.priority).toBe('medium');
      expect(requirement.status).toBe('pending');
      expect(requirement.due_date).toBe('');
      expect(requirement.assigned_to).toBe('');
      expect(Array.isArray(requirement.tags)).toBe(true);
      expect(requirement.created_at).toBeDefined();
      expect(requirement.updated_at).toBeDefined();
    });
    
    it('should handle minimum valid activity data', () => {
      // Create an activity with only the required name field
      const minimalActivity = {
        name: 'Minimal Activity'
      };
      
      // Create the activity
      const activity = trackingManager.create_activity(minimalActivity);
      
      // Verify the activity was created correctly
      expect(activity).toBeDefined();
      expect(activity.id).toBeDefined();
      expect(activity.name).toBe(minimalActivity.name);
      
      // Verify default values were set for optional fields
      expect(activity.description).toBe('');
      expect(activity.requirement_id).toBeNull();
      expect(activity.type).toBe('task');
      expect(activity.status).toBe('pending');
      expect(activity.start_date).toBe('');
      expect(activity.end_date).toBe('');
      expect(activity.assigned_to).toBe('');
      expect(activity.notes).toBe('');
      expect(activity.created_at).toBeDefined();
      expect(activity.updated_at).toBeDefined();
    });
    
    it('should handle updating a requirement with all fields', () => {
      // Create a requirement
      const requirementData = generateRequirement();
      const requirement = trackingManager.create_requirement(requirementData);
      
      // Update all fields
      const updateData = {
        name: 'Updated Requirement Name',
        description: 'Updated description',
        framework: 'Updated framework',
        category: 'Updated category',
        priority: 'high',
        status: 'completed',
        due_date: '2025-12-31',
        assigned_to: 'updated_assignee',
        tags: ['updated', 'tags']
      };
      
      // Update the requirement
      const updatedRequirement = trackingManager.update_requirement(requirement.id, updateData);
      
      // Verify all fields were updated correctly
      expect(updatedRequirement.name).toBe(updateData.name);
      expect(updatedRequirement.description).toBe(updateData.description);
      expect(updatedRequirement.framework).toBe(updateData.framework);
      expect(updatedRequirement.category).toBe(updateData.category);
      expect(updatedRequirement.priority).toBe(updateData.priority);
      expect(updatedRequirement.status).toBe(updateData.status);
      expect(updatedRequirement.due_date).toBe(updateData.due_date);
      expect(updatedRequirement.assigned_to).toBe(updateData.assigned_to);
      expect(updatedRequirement.tags).toEqual(updateData.tags);
      expect(updatedRequirement.updated_at).not.toBe(requirement.updated_at);
    });
    
    it('should handle updating an activity with all fields', () => {
      // Create an activity
      const activityData = generateActivity();
      const activity = trackingManager.create_activity(activityData);
      
      // Update all fields
      const updateData = {
        name: 'Updated Activity Name',
        description: 'Updated description',
        requirement_id: 'updated_requirement_id',
        type: 'meeting',
        status: 'completed',
        start_date: '2025-01-01',
        end_date: '2025-01-15',
        assigned_to: 'updated_assignee',
        notes: 'Updated notes'
      };
      
      // Update the activity
      const updatedActivity = trackingManager.update_activity(activity.id, updateData);
      
      // Verify all fields were updated correctly
      expect(updatedActivity.name).toBe(updateData.name);
      expect(updatedActivity.description).toBe(updateData.description);
      expect(updatedActivity.requirement_id).toBe(updateData.requirement_id);
      expect(updatedActivity.type).toBe(updateData.type);
      expect(updatedActivity.status).toBe(updateData.status);
      expect(updatedActivity.start_date).toBe(updateData.start_date);
      expect(updatedActivity.end_date).toBe(updateData.end_date);
      expect(updatedActivity.assigned_to).toBe(updateData.assigned_to);
      expect(updatedActivity.notes).toBe(updateData.notes);
      expect(updatedActivity.updated_at).not.toBe(activity.updated_at);
    });
  });
});
