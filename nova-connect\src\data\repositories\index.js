/**
 * NovaFuse Universal API Connector - Repositories
 * 
 * This module exports all repository classes.
 */

const connectorRepository = require('./connector-repository');
const credentialRepository = require('./credential-repository');
const apiUsageRepository = require('./api-usage-repository');
const partnerRepository = require('./partner-repository');

module.exports = {
  connectorRepository,
  credentialRepository,
  apiUsageRepository,
  partnerRepository
};
