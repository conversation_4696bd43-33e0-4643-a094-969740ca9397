"""
ComphyonΨᶜ Visualizer - Visualization tools for ComphyonΨᶜ metrics.
"""

import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

class ComphyonVisualizer:
    """
    Visualizer for ComphyonΨᶜ metrics.
    
    Provides methods to visualize ComphyonΨᶜ metrics in various formats.
    """
    
    def __init__(self):
        """Initialize the ComphyonΨᶜ Visualizer."""
        self.default_colors = {
            'velocity': '#3498db',  # Blue
            'acceleration': '#e74c3c',  # Red
            'E_CSDE': '#2ecc71',  # Green
            'E_CSFE': '#f39c12',  # Orange
            'E_CSME': '#9b59b6'   # Purple
        }
    
    def plot_metrics_time_series(self, metrics_history, figsize=(12, 8)):
        """
        Plot ComphyonΨᶜ metrics as a time series.
        
        Args:
            metrics_history: List of metrics dictionaries from ComphyonCalculator
            figsize: Figure size (width, height) in inches
            
        Returns:
            matplotlib.figure.Figure: The generated figure
        """
        # Extract timestamps and metrics
        timestamps = [m['timestamp'] for m in metrics_history]
        velocities = [m['velocity'] for m in metrics_history]
        accelerations = [m['acceleration'] for m in metrics_history]
        
        # Convert timestamps to datetime objects
        dates = [datetime.fromtimestamp(ts) for ts in timestamps]
        
        # Create figure and axes
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize, sharex=True)
        
        # Plot velocity
        ax1.plot(dates, velocities, color=self.default_colors['velocity'], linewidth=2)
        ax1.set_ylabel('ComphyonΨᶜ Velocity')
        ax1.set_title('ComphyonΨᶜ Metrics Time Series')
        ax1.grid(True, alpha=0.3)
        
        # Plot acceleration
        ax2.plot(dates, accelerations, color=self.default_colors['acceleration'], linewidth=2)
        ax2.set_xlabel('Time')
        ax2.set_ylabel('ComphyonΨᶜ Acceleration')
        ax2.grid(True, alpha=0.3)
        
        # Add safety thresholds to acceleration plot
        ax2.axhline(y=1.0, color='orange', linestyle='--', alpha=0.7, label='Warning Threshold')
        ax2.axhline(y=2.5, color='red', linestyle='--', alpha=0.7, label='Critical Threshold')
        ax2.legend()
        
        plt.tight_layout()
        return fig
    
    def plot_domain_energies(self, metrics_history, figsize=(12, 6)):
        """
        Plot domain-specific energies.
        
        Args:
            metrics_history: List of metrics dictionaries from ComphyonCalculator
            figsize: Figure size (width, height) in inches
            
        Returns:
            matplotlib.figure.Figure: The generated figure
        """
        # Extract timestamps and domain energies
        timestamps = [m['timestamp'] for m in metrics_history]
        e_csde = [m['domain_energies']['E_CSDE'] for m in metrics_history]
        e_csfe = [m['domain_energies']['E_CSFE'] for m in metrics_history]
        e_csme = [m['domain_energies']['E_CSME'] for m in metrics_history]
        
        # Convert timestamps to datetime objects
        dates = [datetime.fromtimestamp(ts) for ts in timestamps]
        
        # Create figure and axis
        fig, ax = plt.subplots(figsize=figsize)
        
        # Plot domain energies
        ax.plot(dates, e_csde, color=self.default_colors['E_CSDE'], linewidth=2, label='CSDE Energy')
        ax.plot(dates, e_csfe, color=self.default_colors['E_CSFE'], linewidth=2, label='CSFE Energy')
        ax.plot(dates, e_csme, color=self.default_colors['E_CSME'], linewidth=2, label='CSME Energy')
        
        ax.set_xlabel('Time')
        ax.set_ylabel('Energy Value')
        ax.set_title('Domain-Specific Energies')
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        plt.tight_layout()
        return fig
    
    def create_dashboard(self, metrics_history, figsize=(15, 10)):
        """
        Create a comprehensive dashboard of ComphyonΨᶜ metrics.
        
        Args:
            metrics_history: List of metrics dictionaries from ComphyonCalculator
            figsize: Figure size (width, height) in inches
            
        Returns:
            matplotlib.figure.Figure: The generated figure
        """
        if not metrics_history:
            raise ValueError("Metrics history is empty")
        
        # Create figure and axes
        fig = plt.figure(figsize=figsize)
        gs = fig.add_gridspec(3, 3)
        
        # Time series of ComphyonΨᶜ metrics
        ax1 = fig.add_subplot(gs[0, :])
        ax2 = fig.add_subplot(gs[1, :])
        
        # Domain energies
        ax3 = fig.add_subplot(gs[2, 0])
        ax4 = fig.add_subplot(gs[2, 1])
        ax5 = fig.add_subplot(gs[2, 2])
        
        # Extract data
        timestamps = [m['timestamp'] for m in metrics_history]
        dates = [datetime.fromtimestamp(ts) for ts in timestamps]
        velocities = [m['velocity'] for m in metrics_history]
        accelerations = [m['acceleration'] for m in metrics_history]
        
        # Latest metrics
        latest = metrics_history[-1]
        e_csde = latest['domain_energies']['E_CSDE']
        e_csfe = latest['domain_energies']['E_CSFE']
        e_csme = latest['domain_energies']['E_CSME']
        
        # Plot velocity time series
        ax1.plot(dates, velocities, color=self.default_colors['velocity'], linewidth=2)
        ax1.set_ylabel('ComphyonΨᶜ Velocity')
        ax1.set_title('ComphyonΨᶜ Metrics Dashboard')
        ax1.grid(True, alpha=0.3)
        
        # Plot acceleration time series
        ax2.plot(dates, accelerations, color=self.default_colors['acceleration'], linewidth=2)
        ax2.set_xlabel('Time')
        ax2.set_ylabel('ComphyonΨᶜ Acceleration')
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=1.0, color='orange', linestyle='--', alpha=0.7, label='Warning')
        ax2.axhline(y=2.5, color='red', linestyle='--', alpha=0.7, label='Critical')
        ax2.legend()
        
        # Plot domain energy gauges
        self._plot_gauge(ax3, e_csde, 'CSDE Energy', self.default_colors['E_CSDE'])
        self._plot_gauge(ax4, e_csfe, 'CSFE Energy', self.default_colors['E_CSFE'])
        self._plot_gauge(ax5, e_csme, 'CSME Energy', self.default_colors['E_CSME'])
        
        plt.tight_layout()
        return fig
    
    def _plot_gauge(self, ax, value, title, color, max_value=1.0):
        """
        Plot a gauge chart for a single value.
        
        Args:
            ax: matplotlib axis to plot on
            value: Value to display (0-1)
            title: Title for the gauge
            color: Color for the gauge
            max_value: Maximum value for the gauge
        """
        # Normalize value
        norm_value = min(value / max_value, 1.0)
        
        # Create gauge
        theta = np.linspace(0, 180, 100) * np.pi / 180
        r = 1.0
        
        # Background
        ax.plot(r * np.cos(theta), r * np.sin(theta), color='lightgray', linewidth=15, alpha=0.3)
        
        # Value
        value_theta = np.linspace(0, 180 * norm_value, 100) * np.pi / 180
        ax.plot(r * np.cos(value_theta), r * np.sin(value_theta), color=color, linewidth=15)
        
        # Add value text
        ax.text(0, 0, f"{value:.2f}", ha='center', va='center', fontsize=20, fontweight='bold')
        
        # Set title
        ax.set_title(title)
        
        # Remove axes
        ax.set_axis_off()
        
        # Set aspect ratio
        ax.set_aspect('equal')
