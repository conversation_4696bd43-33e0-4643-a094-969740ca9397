/**
 * NovaFuse Universal API Connector Test Setup After Environment
 * 
 * This file is run after the test environment is set up but before each test file.
 */

// Add Jest extended matchers
require('@jest/globals');

// Set Jest timeout
jest.setTimeout(30000);

// Add custom matchers
expect.extend({
  toBeValidConnector(received) {
    const { ConnectorValidator } = require('../src/validation');
    const result = ConnectorValidator.validateConnector(received);
    
    if (result.isValid) {
      return {
        message: () => `expected ${received} not to be a valid connector`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid connector, but found errors: ${result.errors.join(', ')}`,
        pass: false
      };
    }
  },
  
  toHaveSecureHeaders(received) {
    const requiredHeaders = [
      'X-Content-Type-Options',
      'X-Frame-Options',
      'X-XSS-Protection',
      'Strict-Transport-Security',
      'Content-Security-Policy',
      'Referrer-Policy'
    ];
    
    const missingHeaders = requiredHeaders.filter(header => !received.headers[header]);
    
    if (missingHeaders.length === 0) {
      return {
        message: () => `expected ${received} not to have secure headers`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to have secure headers, but missing: ${missingHeaders.join(', ')}`,
        pass: false
      };
    }
  }
});

// Global beforeEach and afterEach
beforeEach(() => {
  // Reset mocks
  jest.clearAllMocks();
});

afterEach(() => {
  // Clean up any resources
});
