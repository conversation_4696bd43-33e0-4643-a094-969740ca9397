{"version": 3, "names": ["UAConnectorError", "require", "ConnectorError", "constructor", "message", "options", "code", "severity", "context", "cause", "connectorId", "getUserMessage", "toJSON", "includeStack", "json", "ConnectorNotFoundError", "ConnectorConfigurationError", "configErrors", "length", "errorMessages", "map", "err", "join", "ConnectorExecutionError", "operationId", "ConnectorVersionError", "currentVersion", "requiredVersion", "module", "exports"], "sources": ["connector-error.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector - Connector Error\n * \n * This module defines connector-related errors for the UAC.\n */\n\nconst UAConnectorError = require('./base-error');\n\n/**\n * Error class for connector failures\n * @class ConnectorError\n * @extends UAConnectorError\n */\nclass ConnectorError extends UAConnectorError {\n  /**\n   * Create a new ConnectorError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   * @param {string} options.code - Error code\n   * @param {string} options.severity - Error severity\n   * @param {Object} options.context - Additional context for the error\n   * @param {Error} options.cause - The error that caused this error\n   * @param {string} options.connectorId - ID of the connector\n   */\n  constructor(message, options = {}) {\n    super(message, {\n      code: options.code || 'CONNECTOR_ERROR',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause\n    });\n    \n    this.connectorId = options.connectorId;\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'An error occurred with the connector. Please try again later or contact support if the issue persists.';\n  }\n\n  /**\n   * Convert the error to a JSON object\n   * \n   * @param {boolean} includeStack - Whether to include the stack trace\n   * @returns {Object} - JSON representation of the error\n   */\n  toJSON(includeStack = false) {\n    const json = super.toJSON(includeStack);\n    \n    if (this.connectorId) {\n      json.connectorId = this.connectorId;\n    }\n    \n    return json;\n  }\n}\n\n/**\n * Error class for connector not found errors\n * @class ConnectorNotFoundError\n * @extends ConnectorError\n */\nclass ConnectorNotFoundError extends ConnectorError {\n  /**\n   * Create a new ConnectorNotFoundError\n   * \n   * @param {string} connectorId - ID of the connector\n   * @param {Object} options - Error options\n   */\n  constructor(connectorId, options = {}) {\n    const message = `Connector not found: ${connectorId}`;\n    \n    super(message, {\n      code: options.code || 'CONNECTOR_NOT_FOUND',\n      severity: options.severity || 'error',\n      context: { ...options.context, connectorId },\n      cause: options.cause,\n      connectorId\n    });\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return `The connector \"${this.connectorId}\" was not found. Please check the connector ID and try again.`;\n  }\n}\n\n/**\n * Error class for connector configuration errors\n * @class ConnectorConfigurationError\n * @extends ConnectorError\n */\nclass ConnectorConfigurationError extends ConnectorError {\n  /**\n   * Create a new ConnectorConfigurationError\n   * \n   * @param {string} connectorId - ID of the connector\n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   */\n  constructor(connectorId, message = 'Invalid connector configuration', options = {}) {\n    super(message, {\n      code: options.code || 'CONNECTOR_CONFIGURATION_ERROR',\n      severity: options.severity || 'error',\n      context: { ...options.context, connectorId },\n      cause: options.cause,\n      connectorId\n    });\n    \n    this.configErrors = options.configErrors || [];\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    if (this.configErrors.length > 0) {\n      const errorMessages = this.configErrors.map(err => err.message).join('; ');\n      return `The connector configuration is invalid: ${errorMessages}`;\n    }\n    return `The connector configuration is invalid. Please check your configuration and try again.`;\n  }\n\n  /**\n   * Convert the error to a JSON object\n   * \n   * @param {boolean} includeStack - Whether to include the stack trace\n   * @returns {Object} - JSON representation of the error\n   */\n  toJSON(includeStack = false) {\n    const json = super.toJSON(includeStack);\n    json.configErrors = this.configErrors;\n    return json;\n  }\n}\n\n/**\n * Error class for connector execution errors\n * @class ConnectorExecutionError\n * @extends ConnectorError\n */\nclass ConnectorExecutionError extends ConnectorError {\n  /**\n   * Create a new ConnectorExecutionError\n   * \n   * @param {string} connectorId - ID of the connector\n   * @param {string} operationId - ID of the operation\n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   */\n  constructor(connectorId, operationId, message = 'Error executing connector operation', options = {}) {\n    super(message, {\n      code: options.code || 'CONNECTOR_EXECUTION_ERROR',\n      severity: options.severity || 'error',\n      context: { ...options.context, connectorId, operationId },\n      cause: options.cause,\n      connectorId\n    });\n    \n    this.operationId = operationId;\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return `An error occurred while executing the connector operation. Please try again later or contact support if the issue persists.`;\n  }\n\n  /**\n   * Convert the error to a JSON object\n   * \n   * @param {boolean} includeStack - Whether to include the stack trace\n   * @returns {Object} - JSON representation of the error\n   */\n  toJSON(includeStack = false) {\n    const json = super.toJSON(includeStack);\n    json.operationId = this.operationId;\n    return json;\n  }\n}\n\n/**\n * Error class for connector version incompatibility errors\n * @class ConnectorVersionError\n * @extends ConnectorError\n */\nclass ConnectorVersionError extends ConnectorError {\n  /**\n   * Create a new ConnectorVersionError\n   * \n   * @param {string} connectorId - ID of the connector\n   * @param {string} currentVersion - Current version\n   * @param {string} requiredVersion - Required version\n   * @param {Object} options - Error options\n   */\n  constructor(connectorId, currentVersion, requiredVersion, options = {}) {\n    const message = `Connector version incompatible: ${connectorId} (current: ${currentVersion}, required: ${requiredVersion})`;\n    \n    super(message, {\n      code: options.code || 'CONNECTOR_VERSION_ERROR',\n      severity: options.severity || 'error',\n      context: { ...options.context, connectorId, currentVersion, requiredVersion },\n      cause: options.cause,\n      connectorId\n    });\n    \n    this.currentVersion = currentVersion;\n    this.requiredVersion = requiredVersion;\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return `The connector version is incompatible. Please update the connector to version ${this.requiredVersion} or later.`;\n  }\n\n  /**\n   * Convert the error to a JSON object\n   * \n   * @param {boolean} includeStack - Whether to include the stack trace\n   * @returns {Object} - JSON representation of the error\n   */\n  toJSON(includeStack = false) {\n    const json = super.toJSON(includeStack);\n    json.currentVersion = this.currentVersion;\n    json.requiredVersion = this.requiredVersion;\n    return json;\n  }\n}\n\nmodule.exports = {\n  ConnectorError,\n  ConnectorNotFoundError,\n  ConnectorConfigurationError,\n  ConnectorExecutionError,\n  ConnectorVersionError\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,gBAAgB,GAAGC,OAAO,CAAC,cAAc,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,SAASF,gBAAgB,CAAC;EAC5C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,WAAWA,CAACC,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,iBAAiB;MACvCC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI;IACjB,CAAC,CAAC;IAEF,IAAI,CAACC,WAAW,GAAGL,OAAO,CAACK,WAAW;EACxC;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,wGAAwG;EACjH;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEC,MAAMA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,YAAY,CAAC;IAEvC,IAAI,IAAI,CAACH,WAAW,EAAE;MACpBI,IAAI,CAACJ,WAAW,GAAG,IAAI,CAACA,WAAW;IACrC;IAEA,OAAOI,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,SAASb,cAAc,CAAC;EAClD;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACO,WAAW,EAAEL,OAAO,GAAG,CAAC,CAAC,EAAE;IACrC,MAAMD,OAAO,GAAG,wBAAwBM,WAAW,EAAE;IAErD,KAAK,CAACN,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,qBAAqB;MAC3CC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAE;QAAE,GAAGH,OAAO,CAACG,OAAO;QAAEE;MAAY,CAAC;MAC5CD,KAAK,EAAEJ,OAAO,CAACI,KAAK;MACpBC;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,kBAAkB,IAAI,CAACD,WAAW,+DAA+D;EAC1G;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMM,2BAA2B,SAASd,cAAc,CAAC;EACvD;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACO,WAAW,EAAEN,OAAO,GAAG,iCAAiC,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAClF,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,+BAA+B;MACrDC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAE;QAAE,GAAGH,OAAO,CAACG,OAAO;QAAEE;MAAY,CAAC;MAC5CD,KAAK,EAAEJ,OAAO,CAACI,KAAK;MACpBC;IACF,CAAC,CAAC;IAEF,IAAI,CAACO,YAAY,GAAGZ,OAAO,CAACY,YAAY,IAAI,EAAE;EAChD;;EAEA;AACF;AACA;AACA;AACA;EACEN,cAAcA,CAAA,EAAG;IACf,IAAI,IAAI,CAACM,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAChC,MAAMC,aAAa,GAAG,IAAI,CAACF,YAAY,CAACG,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACjB,OAAO,CAAC,CAACkB,IAAI,CAAC,IAAI,CAAC;MAC1E,OAAO,2CAA2CH,aAAa,EAAE;IACnE;IACA,OAAO,wFAAwF;EACjG;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEP,MAAMA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,YAAY,CAAC;IACvCC,IAAI,CAACG,YAAY,GAAG,IAAI,CAACA,YAAY;IACrC,OAAOH,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMS,uBAAuB,SAASrB,cAAc,CAAC;EACnD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACO,WAAW,EAAEc,WAAW,EAAEpB,OAAO,GAAG,qCAAqC,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACnG,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,2BAA2B;MACjDC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAE;QAAE,GAAGH,OAAO,CAACG,OAAO;QAAEE,WAAW;QAAEc;MAAY,CAAC;MACzDf,KAAK,EAAEJ,OAAO,CAACI,KAAK;MACpBC;IACF,CAAC,CAAC;IAEF,IAAI,CAACc,WAAW,GAAGA,WAAW;EAChC;;EAEA;AACF;AACA;AACA;AACA;EACEb,cAAcA,CAAA,EAAG;IACf,OAAO,6HAA6H;EACtI;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEC,MAAMA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,YAAY,CAAC;IACvCC,IAAI,CAACU,WAAW,GAAG,IAAI,CAACA,WAAW;IACnC,OAAOV,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMW,qBAAqB,SAASvB,cAAc,CAAC;EACjD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACO,WAAW,EAAEgB,cAAc,EAAEC,eAAe,EAAEtB,OAAO,GAAG,CAAC,CAAC,EAAE;IACtE,MAAMD,OAAO,GAAG,mCAAmCM,WAAW,cAAcgB,cAAc,eAAeC,eAAe,GAAG;IAE3H,KAAK,CAACvB,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,yBAAyB;MAC/CC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAE;QAAE,GAAGH,OAAO,CAACG,OAAO;QAAEE,WAAW;QAAEgB,cAAc;QAAEC;MAAgB,CAAC;MAC7ElB,KAAK,EAAEJ,OAAO,CAACI,KAAK;MACpBC;IACF,CAAC,CAAC;IAEF,IAAI,CAACgB,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,eAAe,GAAGA,eAAe;EACxC;;EAEA;AACF;AACA;AACA;AACA;EACEhB,cAAcA,CAAA,EAAG;IACf,OAAO,iFAAiF,IAAI,CAACgB,eAAe,YAAY;EAC1H;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEf,MAAMA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,YAAY,CAAC;IACvCC,IAAI,CAACY,cAAc,GAAG,IAAI,CAACA,cAAc;IACzCZ,IAAI,CAACa,eAAe,GAAG,IAAI,CAACA,eAAe;IAC3C,OAAOb,IAAI;EACb;AACF;AAEAc,MAAM,CAACC,OAAO,GAAG;EACf3B,cAAc;EACda,sBAAsB;EACtBC,2BAA2B;EAC3BO,uBAAuB;EACvBE;AACF,CAAC", "ignoreList": []}