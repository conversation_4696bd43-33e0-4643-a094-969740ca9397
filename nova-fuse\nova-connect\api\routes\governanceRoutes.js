/**
 * Governance Routes
 */

const express = require('express');
const router = express.Router();
const GovernanceController = require('../controllers/GovernanceController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');
const { hasFeatureAccess, trackFeatureUsage } = require('../middleware/featureAccessMiddleware');

// All routes require authentication
router.use(authenticate);

// Approval workflow routes
router.get('/approvals', 
  hasFeatureAccess('governance.approvals'),
  hasPermission('governance:view'),
  (req, res, next) => {
    GovernanceController.getAllApprovalWorkflows(req, res, next);
  }
);

router.get('/approvals/my', 
  hasFeatureAccess('governance.approvals'),
  (req, res, next) => {
    GovernanceController.getApprovalWorkflowsForUser(req, res, next);
  }
);

router.get('/approvals/:id', 
  hasFeatureAccess('governance.approvals'),
  (req, res, next) => {
    GovernanceController.getApprovalWorkflowById(req, res, next);
  }
);

router.post('/approvals', 
  hasFeatureAccess('governance.approvals'),
  trackFeatureUsage('governance.approvals'),
  (req, res, next) => {
    GovernanceController.createApprovalWorkflow(req, res, next);
  }
);

router.post('/approvals/:id/status', 
  hasFeatureAccess('governance.approvals'),
  (req, res, next) => {
    GovernanceController.updateApprovalStatus(req, res, next);
  }
);

router.post('/approvals/:id/cancel', 
  hasFeatureAccess('governance.approvals'),
  (req, res, next) => {
    GovernanceController.cancelApprovalWorkflow(req, res, next);
  }
);

router.get('/resources/:resourceType/:resourceId/approvals', 
  hasFeatureAccess('governance.approvals'),
  (req, res, next) => {
    GovernanceController.getApprovalWorkflowsForResource(req, res, next);
  }
);

// Compliance template routes
router.get('/compliance-templates', 
  hasFeatureAccess('governance.compliance'),
  (req, res, next) => {
    GovernanceController.getAllComplianceTemplates(req, res, next);
  }
);

router.get('/compliance-templates/:id', 
  hasFeatureAccess('governance.compliance'),
  (req, res, next) => {
    GovernanceController.getComplianceTemplateById(req, res, next);
  }
);

router.post('/compliance-templates', 
  hasFeatureAccess('governance.compliance'),
  hasPermission('governance:manage'),
  trackFeatureUsage('governance.compliance'),
  (req, res, next) => {
    GovernanceController.createComplianceTemplate(req, res, next);
  }
);

router.delete('/compliance-templates/:id', 
  hasFeatureAccess('governance.compliance'),
  hasPermission('governance:manage'),
  (req, res, next) => {
    GovernanceController.deleteComplianceTemplate(req, res, next);
  }
);

// Data lineage routes
router.post('/data-lineage', 
  hasFeatureAccess('governance.data_lineage'),
  trackFeatureUsage('governance.data_lineage'),
  (req, res, next) => {
    GovernanceController.trackDataLineage(req, res, next);
  }
);

router.get('/resources/:resourceType/:resourceId/lineage', 
  hasFeatureAccess('governance.data_lineage'),
  (req, res, next) => {
    GovernanceController.getDataLineageForResource(req, res, next);
  }
);

router.get('/resources/:resourceType/:resourceId/lineage-graph', 
  hasFeatureAccess('governance.data_lineage'),
  (req, res, next) => {
    GovernanceController.buildDataLineageGraph(req, res, next);
  }
);

module.exports = router;
