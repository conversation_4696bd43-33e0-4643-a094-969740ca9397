/**
 * Role Model
 *
 * This model defines the role schema for the RBAC system.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const roleSchema = new Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  permissions: [{
    type: String
  }],
  inheritsFrom: [{
    type: Schema.Types.ObjectId,
    ref: 'Role'
  }],
  isSystem: {
    type: Boolean,
    default: false
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Add method to check if role has a specific permission
roleSchema.methods.hasPermission = function(permission) {
  // Wildcard permission grants access to all resources
  if (this.permissions.includes('*')) {
    return true;
  }

  // Check for resource wildcard (e.g., 'connector:*')
  if (permission.includes(':')) {
    const [resource] = permission.split(':');
    const resourceWildcard = `${resource}:*`;

    if (this.permissions.includes(resourceWildcard)) {
      return true;
    }
  }

  // Check for specific permission
  if (this.permissions.includes(permission)) {
    return true;
  }

  // Check inherited roles if populated
  if (this.inheritsFrom && this.inheritsFrom.length > 0 && this.populated('inheritsFrom')) {
    for (const parentRole of this.inheritsFrom) {
      if (parentRole.hasPermission(permission)) {
        return true;
      }
    }
  }

  return false;
};

// Add method to add a permission to the role
roleSchema.methods.addPermission = function(permission) {
  if (!this.permissions.includes(permission)) {
    this.permissions.push(permission);
  }
  return this;
};

// Add method to remove a permission from the role
roleSchema.methods.removePermission = function(permission) {
  this.permissions = this.permissions.filter(p => p !== permission);
  return this;
};

// Add indexes
roleSchema.index({ name: 1 });
roleSchema.index({ isSystem: 1 });
roleSchema.index({ isDefault: 1 });

// Create model
const Role = mongoose.model('Role', roleSchema);

module.exports = Role;
