const { Wallet } = require('ethers');
const fs = require('fs');
const path = require('path');

// Generate 3 validator key pairs
const validators = Array(3).fill().map((_, i) => {
  const wallet = Wallet.createRandom();
  return {
    id: i + 1,
    privateKey: wallet.privateKey,
    address: wallet.address
  };
});

// Create .env file content
const envContent = validators.map((v, i) => 
  `VALIDATOR${i + 1}_PRIVATE_KEY=${v.privateKey}`
).join('\n') + '\n';

// Save to .env file
fs.writeFileSync(path.join(__dirname, '.env'), envContent);

// Display validator info
console.log('Generated Validator Keys:');
console.log('========================');
validators.forEach(v => {
  console.log(`\nValidator ${v.id}:`);
  console.log(`Address:    ${v.address}`);
  console.log(`Private Key: ${v.privateKey}`);
});

console.log('\n✅ Keys saved to .env file');
