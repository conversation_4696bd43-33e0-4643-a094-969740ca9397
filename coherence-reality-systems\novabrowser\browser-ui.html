<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaBrowser - Coherence-First Web Gateway</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=SF+Pro+Display:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Modern Browser Colors */
            --bg-primary: #1c1c1e;
            --bg-secondary: #2c2c2e;
            --bg-tertiary: #3a3a3c;
            --bg-surface: #48484a;

            /* Glass Effects */
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);

            /* Text Colors */
            --text-primary: #ffffff;
            --text-secondary: #ebebf5;
            --text-tertiary: #ebebf599;

            /* Accent Colors */
            --accent-blue: #007aff;
            --accent-green: #30d158;
            --accent-orange: #ff9f0a;
            --accent-red: #ff453a;

            /* Coherence Colors */
            --coherence-divine: #bf5af2;
            --coherence-high: #30d158;
            --coherence-medium: #ff9f0a;
            --coherence-low: #ff453a;

            /* Shadows */
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.15);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        body {
            font-family: 'SF Pro Display', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* Modern Browser Header */
        .browser-header {
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--bg-tertiary);
            padding: 8px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            height: 60px;
            position: relative;
        }

        /* Traffic Light Buttons (macOS style) */
        .traffic-lights {
            display: flex;
            gap: 8px;
            margin-right: 20px;
        }

        .traffic-light {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .traffic-light.close { background: #ff5f57; }
        .traffic-light.minimize { background: #ffbd2e; }
        .traffic-light.maximize { background: #28ca42; }

        .traffic-light:hover {
            transform: scale(1.1);
            box-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
        }

        /* Navigation Controls */
        .nav-controls {
            display: flex;
            gap: 4px;
        }

        .nav-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 8px;
            background: var(--glass-bg);
            color: var(--text-secondary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .nav-btn:hover {
            background: var(--bg-surface);
            color: var(--text-primary);
        }

        .nav-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
        }

        /* Modern Address Bar */
        .address-bar-container {
            flex: 1;
            max-width: 600px;
            margin: 0 20px;
            position: relative;
        }

        .address-bar {
            width: 100%;
            height: 36px;
            background: var(--bg-tertiary);
            border: 1px solid var(--bg-surface);
            border-radius: 18px;
            padding: 0 16px 0 40px;
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 400;
            outline: none;
            transition: all 0.2s ease;
        }

        .address-bar:focus {
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.2);
        }

        .address-bar::placeholder {
            color: var(--text-tertiary);
        }

        /* Traffic Light Buttons (macOS style) */
        .traffic-lights {
            display: flex;
            gap: 8px;
            margin-right: 20px;
        }

        .traffic-light {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .traffic-light.close { background: #ff5f57; }
        .traffic-light.minimize { background: #ffbd2e; }
        .traffic-light.maximize { background: #28ca42; }

        .traffic-light:hover {
            transform: scale(1.1);
            box-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
        }

        /* Status Indicators */
        .status-indicators {
            display: flex;
            gap: 12px;
            margin-left: 20px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            background: var(--glass-bg);
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-value {
            color: var(--coherence-high);
            font-family: 'SF Mono', 'JetBrains Mono', monospace;
        }

        /* Modern Sidebar Sections */
        .sidebar-section {
            margin-bottom: 24px;
        }

        .sidebar-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-label {
            font-size: 12px;
            color: var(--text-tertiary);
            margin-bottom: 4px;
        }

        .metric-description {
            font-size: 11px;
            color: var(--text-tertiary);
            margin-top: 4px;
        }

        /* Website Viewport */
        .website-viewport {
            flex: 1;
            background: #ffffff;
            position: relative;
            border-radius: 8px;
            margin: 8px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        /* Loading Overlay */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 2px solid var(--bg-surface);
            border-top: 2px solid var(--accent-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Floating Coherence Panel */
        .coherence-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            padding: 16px;
            min-width: 200px;
            box-shadow: var(--shadow-lg);
        }

        .panel-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .panel-metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .panel-metric-value {
            font-family: 'SF Mono', monospace;
            font-weight: 600;
        }

        .coherence-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 12px;
            position: relative;
            transition: all 0.3s ease;
        }

        .coherence-indicator::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 50%;
            background: inherit;
            opacity: 0.3;
            animation: pulse 2s infinite;
        }

        .coherence-high {
            background: var(--coherence-high);
            box-shadow: 0 0 8px var(--coherence-high);
        }
        .coherence-medium {
            background: var(--coherence-medium);
            box-shadow: 0 0 8px var(--coherence-medium);
        }
        .coherence-low {
            background: var(--coherence-low);
            box-shadow: 0 0 8px var(--coherence-low);
        }

        .url-input {
            flex: 1;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 400;
            outline: none;
            font-family: 'SF Pro Display', sans-serif;
        }

        .url-input::placeholder {
            color: var(--text-muted);
            font-family: 'Inter', sans-serif;
        }

        .go-btn {
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            border: none;
            color: white;
            padding: 6px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 13px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            margin-left: 8px;
        }

        .go-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .go-btn:active {
            transform: translateY(0);
        }

        /* Modern Status Bar */
        .status-bar {
            display: flex;
            align-items: center;
            gap: 20px;
            font-size: 12px;
            padding: 0 4px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 6px;
            background: var(--bg-glass);
            border: 1px solid var(--border-secondary);
            transition: all 0.2s ease;
        }

        .status-item:hover {
            background: var(--bg-glass-hover);
            border-color: var(--border-primary);
        }

        .status-value {
            font-weight: 600;
            font-family: 'JetBrains Mono', monospace;
        }

        .status-item .emoji {
            font-size: 14px;
        }

        /* Main Content Area */
        .content-area {
            height: calc(100vh - 140px);
            display: flex;
            position: relative;
        }

        /* Modern Sidebar */
        .sidebar {
            width: 320px;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-right: 1px solid var(--border-primary);
            padding: 24px 20px;
            overflow-y: auto;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: var(--bg-glass);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: var(--border-primary);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        .sidebar h3 {
            margin-bottom: 16px;
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .sidebar h3::before {
            content: '';
            width: 3px;
            height: 16px;
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            border-radius: 2px;
        }

        .metric-card {
            background: var(--bg-glass);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-primary);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
        }

        .metric-card:hover {
            background: var(--bg-glass-hover);
            border-color: var(--accent-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .metric-title {
            font-size: 12px;
            color: var(--text-tertiary);
            margin-bottom: 8px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-value {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            color: var(--text-primary);
            font-family: 'JetBrains Mono', monospace;
        }

        .metric-details {
            font-size: 11px;
            color: var(--text-muted);
            font-weight: 400;
        }

        .violations-list {
            max-height: 240px;
            overflow-y: auto;
            padding-right: 4px;
        }

        .violations-list::-webkit-scrollbar {
            width: 4px;
        }

        .violations-list::-webkit-scrollbar-track {
            background: transparent;
        }

        .violations-list::-webkit-scrollbar-thumb {
            background: var(--border-primary);
            border-radius: 2px;
        }

        .violation-item {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--accent-danger);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            font-size: 12px;
            transition: all 0.2s ease;
            position: relative;
        }

        .violation-item:hover {
            background: rgba(239, 68, 68, 0.15);
            transform: translateX(4px);
        }

        .auto-fix-btn {
            background: linear-gradient(135deg, var(--coherence-high), #059669);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 600;
            margin-top: 8px;
            transition: all 0.2s ease;
        }

        .auto-fix-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Modern Website Frame */
        .website-frame {
            flex: 1;
            background: var(--bg-primary);
            position: relative;
            border-radius: 12px;
            margin: 8px;
            overflow: hidden;
            border: 1px solid var(--border-primary);
        }

        .website-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 12px;
        }

        /* Modern Coherence Overlay */
        .coherence-overlay {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-primary);
            border-radius: 12px;
            padding: 20px;
            min-width: 240px;
            box-shadow: var(--shadow-xl);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .coherence-overlay:hover {
            background: var(--bg-glass-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl), var(--shadow-glow);
        }

        .overlay-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .overlay-title::after {
            content: '';
            flex: 1;
            height: 1px;
            background: linear-gradient(90deg, var(--accent-primary), transparent);
        }

        .overlay-metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            padding: 4px 0;
        }

        .overlay-metric span:first-child {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .overlay-metric span:last-child {
            color: var(--text-primary);
            font-weight: 600;
            font-family: 'JetBrains Mono', monospace;
        }

        .psi-snap {
            text-align: center;
            margin-top: 16px;
            padding: 12px;
            background: linear-gradient(135deg, var(--coherence-high), #059669);
            border-radius: 8px;
            font-weight: 600;
            font-size: 12px;
            color: white;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: var(--shadow-md);
        }

        .psi-snap.inactive {
            background: linear-gradient(135deg, var(--coherence-medium), #d97706);
        }

        /* Modern Loading States */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            flex-direction: column;
            gap: 24px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }

        .spinner {
            width: 48px;
            height: 48px;
            border: 3px solid var(--border-primary);
            border-left: 3px solid var(--accent-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Animations */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes slideIn {
            0% { transform: translateX(100%); opacity: 0; }
            100% { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            0% { transform: translateX(0); opacity: 1; }
            100% { transform: translateX(100%); opacity: 0; }
        }

        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(10px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .sidebar {
                width: 280px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                position: absolute;
                left: -320px;
                z-index: 1000;
                height: 100%;
                transition: left 0.3s ease;
            }

            .sidebar.open {
                left: 0;
            }

            .coherence-overlay {
                position: fixed;
                top: 80px;
                right: 10px;
                min-width: 200px;
            }
        }
    </style>
</head>
<body>
    <!-- Modern Browser Header -->
    <div class="browser-header">
        <!-- Traffic Light Buttons -->
        <div class="traffic-lights">
            <button class="traffic-light close"></button>
            <button class="traffic-light minimize"></button>
            <button class="traffic-light maximize"></button>
        </div>

        <!-- Navigation Controls -->
        <div class="nav-controls">
            <button class="nav-btn" id="back-btn" disabled>←</button>
            <button class="nav-btn" id="forward-btn" disabled>→</button>
            <button class="nav-btn" id="refresh-btn">⟳</button>
        </div>

        <!-- Modern Address Bar -->
        <div class="address-bar-container">
            <div class="coherence-indicator" id="coherence-indicator"></div>
            <input type="text" class="address-bar" id="url-input"
                   placeholder="Search or enter website URL">
        </div>

        <!-- Status Indicators -->
        <div class="status-indicators">
            <div class="status-indicator">
                <span>🧬</span>
                <span class="status-value" id="status-coherence">--</span>
            </div>
            <div class="status-indicator">
                <span>🛡️</span>
                <span class="status-value" id="status-threats">--</span>
            </div>
        </div>

        <!-- Browser Actions -->
        <div class="browser-actions">
            <button class="action-btn" title="NovaVision">👁️</button>
            <button class="action-btn" title="NovaShield">🛡️</button>
            <button class="action-btn" title="Settings">⚙️</button>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="browser-content">
        <!-- Modern Sidebar -->
        <div class="sidebar">
            <div class="sidebar-section">
                <div class="sidebar-title">🧬 NovaDNA Analysis</div>
                <div class="metric-card">
                    <div class="metric-label">Overall Coherence</div>
                    <div class="metric-value" id="coherence-score">--</div>
                    <div class="metric-description">Structural • Functional • Relational</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Ψ-Snap Status</div>
                    <div class="metric-value" id="psi-snap-status">INACTIVE</div>
                    <div class="metric-description">82/18 Comphyological Model</div>
                </div>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-title">👁️ NovaVision</div>
                <div class="metric-card">
                    <div class="metric-label">Accessibility Score</div>
                    <div class="metric-value" id="accessibility-score">--</div>
                    <div class="metric-description">WCAG 2.1 • ADA Compliance</div>
                </div>

                <div class="violations-list" id="violations-list">
                    <!-- Violations will be populated here -->
                </div>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-title">🛡️ NovaShield</div>
                <div class="metric-card">
                    <div class="metric-label">Threat Level</div>
                    <div class="metric-value" id="threat-level">--</div>
                    <div class="metric-description">Real-time Protection</div>
                </div>
            </div>
        </div>

        <!-- Website Viewport -->
        <div class="website-viewport">
            <div class="loading-overlay" id="loading-screen">
                <div class="loading-spinner"></div>
                <div class="loading-text">Analyzing page coherence...</div>
            </div>

            <iframe class="website-iframe" id="website-iframe" style="display: none;"></iframe>

            <!-- Floating Coherence Panel -->
            <div class="coherence-panel" id="coherence-overlay" style="display: none;">
                <div class="panel-title">🧬 Live Analysis</div>
                <div class="panel-metric">
                    <span>Structural:</span>
                    <span class="panel-metric-value" id="overlay-structural">--</span>
                </div>
                <div class="panel-metric">
                    <span>Functional:</span>
                    <span class="panel-metric-value" id="overlay-functional">--</span>
                </div>
                <div class="panel-metric">
                    <span>Relational:</span>
                    <span class="panel-metric-value" id="overlay-relational">--</span>
                </div>
                <div class="psi-snap inactive" id="overlay-psi-snap">
                    Ψ-Snap: INACTIVE
                </div>
            </div>
        </div>
    </div>

    <script>
        // NovaBrowser UI Controller
        class NovaBrowserUI {
            constructor() {
                this.currentUrl = '';
                this.analysisData = {};
                this.initializeEventListeners();
                this.loadDefaultPage();
            }
            
            initializeEventListeners() {
                // Address bar navigation
                document.getElementById('url-input').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.navigateToUrl();
                });
                document.getElementById('refresh-btn').addEventListener('click', () => this.refreshPage());

                // Traffic lights
                document.querySelector('.traffic-light.close').addEventListener('click', () => {
                    if (confirm('Close NovaBrowser?')) window.close();
                });

                // Listen for messages from home page
                window.addEventListener('message', (event) => {
                    if (event.data.type === 'navigate') {
                        document.getElementById('url-input').value = event.data.url;
                        this.navigateToUrl();
                    }
                });
            }
            
            navigateToUrl() {
                const url = document.getElementById('url-input').value.trim();
                if (!url) return;

                // Handle special URLs
                if (url === 'test' || url === 'novabrowser://test') {
                    this.loadPage('working-test.html');
                    return;
                }

                // Add protocol if missing
                let fullUrl;
                if (url.startsWith('http://') || url.startsWith('https://')) {
                    fullUrl = url;
                } else if (url.includes('.')) {
                    fullUrl = `https://${url}`;
                } else {
                    // Search query
                    fullUrl = `https://www.google.com/search?q=${encodeURIComponent(url)}`;
                }

                this.loadPage(fullUrl);
            }
            
            loadPage(url) {
                this.currentUrl = url;
                this.showLoading();
                
                // Simulate page load and analysis
                setTimeout(() => {
                    this.hideLoading();
                    this.showPage(url);
                    this.runAnalysis();
                }, 2000);
            }
            
            loadDefaultPage() {
                // Show Nova Home page directly in the browser
                document.getElementById('url-input').value = 'Nova Home - Your Consciousness Gateway';
                this.showNovaHomePage();
            }

            showNovaHomePage() {
                // Hide loading and iframe
                document.getElementById('loading-screen').style.display = 'none';
                document.getElementById('website-iframe').style.display = 'none';
                document.getElementById('coherence-overlay').style.display = 'none';

                // Create Nova Home content
                const homeContent = document.createElement('div');
                homeContent.id = 'nova-home-content';
                homeContent.innerHTML = `
                    <style>
                        #nova-home-content {
                            background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%);
                            color: white;
                            padding: 40px 20px;
                            height: 100%;
                            overflow-y: auto;
                            font-family: 'SF Pro Display', 'Inter', sans-serif;
                        }

                        .home-hero {
                            text-align: center;
                            margin-bottom: 50px;
                        }

                        .home-title {
                            font-size: 3rem;
                            font-weight: 700;
                            margin-bottom: 20px;
                            background: linear-gradient(135deg, #6366f1, #a855f7);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                            background-clip: text;
                        }

                        .home-subtitle {
                            font-size: 1.2rem;
                            color: rgba(255, 255, 255, 0.8);
                            margin-bottom: 30px;
                        }

                        .services-grid {
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
                            gap: 24px;
                            max-width: 1200px;
                            margin: 0 auto;
                        }

                        .service-card {
                            background: rgba(255, 255, 255, 0.05);
                            backdrop-filter: blur(20px);
                            border: 1px solid rgba(255, 255, 255, 0.1);
                            border-radius: 16px;
                            padding: 32px;
                            text-align: center;
                            transition: all 0.3s ease;
                            cursor: pointer;
                            position: relative;
                            overflow: hidden;
                        }

                        .service-card::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            height: 3px;
                            background: linear-gradient(90deg, #6366f1, #a855f7);
                            transform: scaleX(0);
                            transition: transform 0.3s ease;
                        }

                        .service-card:hover {
                            transform: translateY(-8px);
                            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                            border-color: #6366f1;
                        }

                        .service-card:hover::before {
                            transform: scaleX(1);
                        }

                        .service-icon {
                            font-size: 3.5rem;
                            margin-bottom: 20px;
                            display: block;
                        }

                        .service-title {
                            font-size: 1.8rem;
                            font-weight: 600;
                            margin-bottom: 16px;
                            color: white;
                        }

                        .service-description {
                            color: rgba(255, 255, 255, 0.8);
                            margin-bottom: 24px;
                            line-height: 1.6;
                            font-size: 1rem;
                        }

                        .service-features {
                            list-style: none;
                            margin-bottom: 28px;
                            text-align: left;
                        }

                        .service-features li {
                            color: rgba(255, 255, 255, 0.7);
                            font-size: 0.9rem;
                            margin-bottom: 10px;
                            display: flex;
                            align-items: center;
                            gap: 10px;
                        }

                        .service-features li::before {
                            content: '✓';
                            color: #10b981;
                            font-weight: bold;
                            font-size: 1.1rem;
                        }

                        .service-cta {
                            background: linear-gradient(135deg, #6366f1, #8b5cf6);
                            color: white;
                            border: none;
                            padding: 14px 28px;
                            border-radius: 10px;
                            font-weight: 600;
                            font-size: 1rem;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            width: 100%;
                        }

                        .service-cta:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
                        }

                        .premium-badge {
                            position: absolute;
                            top: 16px;
                            right: 16px;
                            background: #a855f7;
                            color: white;
                            padding: 6px 12px;
                            border-radius: 12px;
                            font-size: 0.8rem;
                            font-weight: 600;
                        }

                        .enterprise-badge {
                            background: #f59e0b;
                        }

                        .divine-badge {
                            background: linear-gradient(135deg, #a855f7, #ec4899);
                        }
                    </style>

                    <div class="home-hero">
                        <h1 class="home-title">Nova Ecosystem</h1>
                        <p class="home-subtitle">Experience the future of consciousness-driven technology</p>
                    </div>

                    <div class="services-grid">
                        <!-- KetherNet Card -->
                        <div class="service-card" onclick="window.parent.postMessage({type: 'navigate', url: 'http://localhost:3004'}, '*')">
                            <div class="premium-badge enterprise-badge">Enterprise</div>
                            <div class="service-icon">⛓️</div>
                            <h3 class="service-title">KetherNet</h3>
                            <p class="service-description">
                                Blockchain-powered consciousness verification and decentralized identity management for the Nova ecosystem
                            </p>
                            <ul class="service-features">
                                <li>Quantum-safe blockchain technology</li>
                                <li>Consciousness-based identity verification</li>
                                <li>Cross-platform synchronization</li>
                                <li>Decentralized data sovereignty</li>
                                <li>Smart contract automation</li>
                                <li>Zero-knowledge privacy protection</li>
                            </ul>
                            <button class="service-cta">Connect to KetherNet</button>
                        </div>

                        <!-- NovaAgent Card -->
                        <div class="service-card" onclick="window.parent.postMessage({type: 'navigate', url: 'http://localhost:8090'}, '*')">
                            <div class="premium-badge">Premium</div>
                            <div class="service-icon">🤖</div>
                            <h3 class="service-title">NovaAgent</h3>
                            <p class="service-description">
                                Advanced AI assistant powered by Comphyological principles for consciousness-aware interactions and analysis
                            </p>
                            <ul class="service-features">
                                <li>Consciousness-based response generation</li>
                                <li>Real-time coherence optimization</li>
                                <li>Multi-dimensional analysis capabilities</li>
                                <li>Adaptive learning algorithms</li>
                                <li>Natural language understanding</li>
                                <li>Contextual awareness processing</li>
                            </ul>
                            <button class="service-cta">Launch NovaAgent</button>
                        </div>

                        <!-- Coherence Marketing Card -->
                        <div class="service-card" onclick="window.parent.postMessage({type: 'navigate', url: 'http://localhost:3006'}, '*')">
                            <div class="premium-badge divine-badge">Divine</div>
                            <div class="service-icon">📈</div>
                            <h3 class="service-title">Coherence Marketing</h3>
                            <p class="service-description">
                                Revolutionary marketing platform that optimizes campaigns based on consciousness coherence and divine alignment
                            </p>
                            <ul class="service-features">
                                <li>Consciousness-driven targeting</li>
                                <li>Divine coherence optimization</li>
                                <li>Ethical influence algorithms</li>
                                <li>Quantum resonance analytics</li>
                                <li>Spiritual ROI measurement</li>
                                <li>Harmonic conversion tracking</li>
                            </ul>
                            <button class="service-cta">Elevate Your Marketing</button>
                        </div>

                        <!-- NovaBrowser Card -->
                        <div class="service-card" onclick="location.reload()">
                            <div class="service-icon">🌐</div>
                            <h3 class="service-title">NovaBrowser</h3>
                            <p class="service-description">
                                Coherence-first web gateway with real-time consciousness analysis of websites and content
                            </p>
                            <ul class="service-features">
                                <li>Real-time coherence scoring</li>
                                <li>Ψ-Snap threshold monitoring</li>
                                <li>Accessibility compliance checking</li>
                                <li>Advanced threat detection</li>
                                <li>Consciousness-based filtering</li>
                                <li>Divine web experience</li>
                            </ul>
                            <button class="service-cta">Continue Browsing</button>
                        </div>

                        <!-- NovaVision Card -->
                        <div class="service-card" onclick="window.parent.postMessage({type: 'navigate', url: 'http://localhost:3002'}, '*')">
                            <div class="service-icon">👁️</div>
                            <h3 class="service-title">NovaVision</h3>
                            <p class="service-description">
                                Advanced accessibility and compliance analysis with consciousness-aware recommendations
                            </p>
                            <ul class="service-features">
                                <li>WCAG 2.1 compliance analysis</li>
                                <li>ADA compliance verification</li>
                                <li>Consciousness accessibility scoring</li>
                                <li>Automated fix suggestions</li>
                                <li>Real-time monitoring</li>
                                <li>Divine design principles</li>
                            </ul>
                            <button class="service-cta">Analyze Accessibility</button>
                        </div>

                        <!-- NovaShield Card -->
                        <div class="service-card" onclick="window.parent.postMessage({type: 'navigate', url: 'http://localhost:3003'}, '*')">
                            <div class="service-icon">🛡️</div>
                            <h3 class="service-title">NovaShield</h3>
                            <p class="service-description">
                                Quantum-enhanced security with consciousness-based threat detection and divine protection
                            </p>
                            <ul class="service-features">
                                <li>Real-time threat monitoring</li>
                                <li>Consciousness-based filtering</li>
                                <li>Quantum encryption protocols</li>
                                <li>Predictive security analysis</li>
                                <li>Divine protection algorithms</li>
                                <li>Harmonic firewall technology</li>
                            </ul>
                            <button class="service-cta">Enable Protection</button>
                        </div>
                    </div>
                `;

                // Replace website content with home page
                const viewport = document.querySelector('.website-viewport');
                viewport.innerHTML = '';
                viewport.appendChild(homeContent);

                // Update analysis for home page
                this.updateAnalysisResults({
                    coherence: { overall: 95, structural: 98, functional: 94, relational: 93 },
                    accessibility: { score: 98, violations: [] },
                    security: { level: 'DIVINE', threats: 0 },
                    performance: { analysisTime: 12 }
                });
            }
            
            showLoading() {
                document.getElementById('loading-screen').style.display = 'flex';
                document.getElementById('website-iframe').style.display = 'none';
                document.getElementById('coherence-overlay').style.display = 'none';
            }
            
            hideLoading() {
                document.getElementById('loading-screen').style.display = 'none';
                document.getElementById('website-iframe').style.display = 'block';
                document.getElementById('coherence-overlay').style.display = 'block';
            }
            
            showPage(url) {
                const iframe = document.getElementById('website-iframe');

                // List of sites that block iframe embedding
                const blockedSites = [
                    'google.com', 'facebook.com', 'twitter.com', 'linkedin.com',
                    'instagram.com', 'youtube.com', 'github.com', 'stackoverflow.com'
                ];

                const needsProxy = blockedSites.some(site => url.includes(site));

                if (needsProxy) {
                    // Use proxy server to bypass iframe restrictions
                    const proxyUrl = `http://localhost:3001/proxy?url=${encodeURIComponent(url)}`;
                    iframe.src = proxyUrl;
                    console.log(`Using proxy for: ${url}`);
                } else {
                    // Direct iframe for sites that allow it
                    iframe.src = url;
                }

                // Handle iframe load errors
                iframe.onerror = () => {
                    this.showIframeError(url);
                };
            }

            showIframeError(url) {
                const iframe = document.getElementById('website-iframe');
                iframe.style.display = 'none';

                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = `
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    background: rgba(255, 71, 87, 0.1);
                    color: white;
                    text-align: center;
                    padding: 40px;
                `;

                errorDiv.innerHTML = `
                    <h2>🔒 Site Blocked Iframe Embedding</h2>
                    <p>This website prevents embedding for security reasons.</p>
                    <p><strong>URL:</strong> ${url}</p>
                    <button onclick="window.open('${url}', '_blank')"
                            style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 20px;">
                        🌐 Open in New Tab
                    </button>
                    <p style="margin-top: 20px; font-size: 12px; opacity: 0.7;">
                        NovaBrowser analysis still active - coherence data from URL structure
                    </p>
                `;

                const frame = document.querySelector('.website-frame');
                frame.appendChild(errorDiv);
            }
            
            async runAnalysis() {
                const startTime = performance.now();

                try {
                    // Get backend coherence data
                    const backendResponse = await fetch('http://localhost:8090/status', {
                        method: 'GET',
                        mode: 'cors',
                        headers: { 'Content-Type': 'application/json' }
                    });

                    let backendCoherence = 0.5;
                    if (backendResponse.ok) {
                        const backendData = await backendResponse.json();
                        backendCoherence = backendData.coherence || 0.5;
                    }

                    // Analyze the loaded page via iframe (if accessible)
                    const iframe = document.getElementById('website-iframe');
                    let pageAnalysis = this.analyzeCurrentPage();

                    // Combine local analysis with backend data
                    const analysisResults = {
                        coherence: {
                            overall: Math.round((pageAnalysis.structural + pageAnalysis.functional + backendCoherence) / 3 * 100),
                            structural: Math.round(pageAnalysis.structural * 100),
                            functional: Math.round(pageAnalysis.functional * 100),
                            relational: Math.round(backendCoherence * 100)
                        },
                        accessibility: pageAnalysis.accessibility,
                        security: pageAnalysis.security,
                        performance: {
                            analysisTime: Math.round(performance.now() - startTime)
                        }
                    };

                    this.updateAnalysisResults(analysisResults);

                } catch (error) {
                    console.error('Analysis failed:', error);
                    this.updateAnalysisResults({
                        coherence: { overall: 0, structural: 0, functional: 0, relational: 0 },
                        accessibility: { score: 0, violations: ['Analysis failed: ' + error.message] },
                        security: { level: 'UNKNOWN', threats: 0 },
                        performance: { analysisTime: Math.round(performance.now() - startTime) }
                    });
                }
            }

            analyzeCurrentPage() {
                // Analyze the current page structure
                const url = this.currentUrl;

                // Basic URL-based analysis
                let structural = 0.8; // Default for most sites
                let functional = 0.7; // Default for most sites

                // URL-based heuristics
                if (url.includes('gov') || url.includes('edu')) {
                    structural += 0.1; // Government/education sites tend to be well-structured
                }
                if (url.includes('https://')) {
                    functional += 0.1; // HTTPS is good for functionality
                }
                if (url.includes('example.com')) {
                    structural = 0.9; // Example.com is well-structured
                    functional = 0.8;
                }

                // Accessibility analysis (simulated for cross-origin)
                const violations = [];
                let accessibilityScore = 85; // Default assumption

                // Security analysis
                const isHttps = url.startsWith('https://');
                const securityLevel = isHttps ? 'LOW' : 'MEDIUM';
                const threats = isHttps ? 0 : 1;

                if (!isHttps) {
                    violations.push('Insecure HTTP connection detected');
                    accessibilityScore -= 10;
                }

                // Simulate some violations for demo
                if (url.includes('example.com')) {
                    violations.push('Missing alt text on logo image');
                    violations.push('Poor color contrast in header');
                    accessibilityScore = 60;
                }

                return {
                    structural: Math.min(1, structural),
                    functional: Math.min(1, functional),
                    accessibility: {
                        score: accessibilityScore,
                        violations: violations
                    },
                    security: {
                        level: securityLevel,
                        threats: threats
                    }
                };
            }
            
            updateAnalysisResults(data) {
                // Update status bar
                document.getElementById('status-coherence').textContent = `${data.coherence.overall}%`;
                document.getElementById('status-accessibility').textContent = `${data.accessibility.score}%`;
                document.getElementById('status-threats').textContent = data.security.level;
                document.getElementById('status-speed').textContent = `${data.performance.analysisTime}ms`;
                
                // Update sidebar
                document.getElementById('coherence-score').textContent = `${data.coherence.overall}%`;
                document.getElementById('accessibility-score').textContent = `${data.accessibility.score}%`;
                document.getElementById('threat-level').textContent = data.security.level;
                
                // Update Ψ-Snap status with modern styling
                const psiSnap = data.coherence.overall >= 82;
                const psiSnapElement = document.getElementById('psi-snap-status');
                psiSnapElement.textContent = psiSnap ? 'ACTIVE' : 'INACTIVE';
                psiSnapElement.style.color = psiSnap ? 'var(--coherence-high)' : 'var(--coherence-medium)';

                // Update overlay with enhanced styling
                document.getElementById('overlay-structural').textContent = `${data.coherence.structural}%`;
                document.getElementById('overlay-functional').textContent = `${data.coherence.functional}%`;
                document.getElementById('overlay-relational').textContent = `${data.coherence.relational}%`;

                const overlayPsiSnap = document.getElementById('overlay-psi-snap');
                overlayPsiSnap.textContent = `Ψ-Snap: ${psiSnap ? 'ACTIVE' : 'INACTIVE'}`;
                overlayPsiSnap.className = psiSnap ? 'psi-snap' : 'psi-snap inactive';

                // Update coherence indicator with enhanced visual feedback
                const indicator = document.getElementById('coherence-indicator');
                const overall = data.coherence.overall;
                const relational = data.coherence.relational;

                if (overall >= 82) {
                    indicator.className = 'coherence-indicator coherence-high';
                    indicator.title = `✅ Ψ-Snap ACTIVE (${overall}%) - Divine Coherence Achieved`;
                } else if (overall >= 60) {
                    indicator.className = 'coherence-indicator coherence-medium';
                    indicator.title = `⚠️ Below Ψ-Snap threshold (${overall}% < 82%) - Approaching Coherence`;
                } else {
                    indicator.className = 'coherence-indicator coherence-low';
                    indicator.title = `❌ Low coherence detected (${overall}%) - Coherence Restoration Needed`;
                }

                // Alert for low relational coherence
                if (relational < 85) {
                    this.showCoherenceAlert(`⚠️ Relational coherence below 85% (${relational}%)`);
                }
                
                // Update violations
                this.updateViolationsList(data.accessibility.violations);
            }
            
            updateViolationsList(violations) {
                const list = document.getElementById('violations-list');
                list.innerHTML = '';
                
                if (violations.length === 0) {
                    list.innerHTML = '<div style="color: #00ff96; text-align: center; padding: 20px;">✅ No violations detected</div>';
                    return;
                }
                
                violations.forEach(violation => {
                    const item = document.createElement('div');
                    item.className = 'violation-item';
                    item.innerHTML = `
                        <div>${violation}</div>
                        <button class="auto-fix-btn" onclick="this.parentElement.style.display='none'">Auto-Fix</button>
                    `;
                    list.appendChild(item);
                });
            }
            
            refreshPage() {
                if (this.currentUrl) {
                    this.loadPage(this.currentUrl);
                }
            }

            showCoherenceAlert(message) {
                // Create modern alert notification
                const alert = document.createElement('div');
                alert.style.cssText = `
                    position: fixed;
                    top: 100px;
                    right: 24px;
                    background: var(--bg-glass);
                    backdrop-filter: blur(20px);
                    border: 1px solid var(--coherence-medium);
                    color: var(--text-primary);
                    padding: 16px 20px;
                    border-radius: 12px;
                    font-weight: 600;
                    font-size: 14px;
                    z-index: 10000;
                    box-shadow: var(--shadow-xl);
                    animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                    max-width: 320px;
                    min-width: 280px;
                `;

                alert.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="width: 8px; height: 8px; background: var(--coherence-medium); border-radius: 50%; box-shadow: 0 0 8px var(--coherence-medium);"></div>
                        <div>${message}</div>
                    </div>
                `;

                document.body.appendChild(alert);

                // Auto-remove after 6 seconds with enhanced animation
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.style.animation = 'slideOut 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                        setTimeout(() => alert.remove(), 400);
                    }
                }, 6000);
            }
        }
        
        // Initialize NovaBrowser UI
        const novaBrowser = new NovaBrowserUI();
    </script>
</body>
</html>
