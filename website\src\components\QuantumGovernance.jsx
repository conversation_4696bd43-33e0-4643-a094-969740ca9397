import React, { useEffect, useCallback } from 'react';
import { useNovaConnect } from '../context/NovaConnectContext';
import { useQuantumField, QuantumFieldProvider, QUANTUM_STATES } from '../contexts/QuantumField';
import TaskForm from './TaskForm';
import QuantumToggle from './QuantumToggle';
import EntropyVisualization from './EntropyVisualization';
import ComplianceBadge from './ComplianceBadge';
import QuantumDebugPanel from './QuantumDebugPanel';
import '../styles/QuantumGovernance.css';

// Enhanced Quantum Governance Component
const QuantumGovernanceDashboard = () => {
  const { 
    isQuantumAvailable,
    updateMetrics,
    metrics
  } = useNovaConnect();
  
  const {
    quantumState,
    entropyHistory,
    coherence,
    measureEntropy,
    detectAnomalies,
    updateDebugInfo
  } = useQuantumField();

  // Handle task submission with quantum/classical fallback
  const handleTaskSubmit = useCallback(async (taskData) => {
    try {
      // Start with quantum measurement if available
      if (isQuantumAvailable) {
        try {
          const quantumResult = await measureEntropy(taskData);
          const anomalyCheck = detectAnomalies(taskData, quantumResult.entropy);
          
          // If anomaly detected, log it but continue with quantum result
          if (anomalyCheck.isAnomaly) {
            console.warn('Quantum anomaly detected:', anomalyCheck);
          }
          
          updateDebugInfo();
          return {
            ...quantumResult,
            mode: 'quantum',
            anomaly: anomalyCheck,
            timestamp: Date.now()
          };
        } catch (quantumError) {
          console.error('Quantum measurement failed, falling back to classical:', quantumError);
          updateMetrics('quantum', false);
        }
      }
      
      // Fallback to classical measurement
      const classicalEntropy = Math.random(); // Replace with actual classical measurement
      updateMetrics('classical', true);
      
      return {
        entropy: classicalEntropy,
        coherence: 1.0,
        state: 'classical',
        mode: 'classical',
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Task processing failed:', error);
      throw error;
    }
  }, [isQuantumAvailable, measureEntropy, detectAnomalies, updateMetrics, updateDebugInfo]);

  // Update debug info periodically
  useEffect(() => {
    const interval = setInterval(updateDebugInfo, 5000);
    return () => clearInterval(interval);
  }, [updateDebugInfo]);

  return (
    <div className="governance-field" data-quantum-state={quantumState}>
      <div className="governance-header">
        <div>
          <h2>Quantum Governance Dashboard</h2>
          <div className="quantum-status">
            Status: <span className={`status-${quantumState}`}>
              {quantumState}
              {quantumState === QUANTUM_STATES.ENTANGLED && (
                <span className="quantum-pulse"></span>
              )}
            </span>
          </div>
        </div>
        <QuantumToggle 
          enabled={isQuantumAvailable} 
          disabled={quantumState === QUANTUM_STATES.MEASURING}
        />
      </div>
      
      <div className="governance-content">
        <div className="task-form-container">
          <TaskForm 
            onSubmit={handleTaskSubmit}
            isQuantumActive={isQuantumAvailable}
            coherence={coherence}
          />
          
          <QuantumDebugPanel 
            metrics={metrics}
            entropyHistory={entropyHistory}
            quantumState={quantumState}
            coherence={coherence}
          />
        </div>
        
        <div className="visualization-container">
          <EntropyVisualization 
            entropyHistory={entropyHistory}
            coherence={coherence}
            isQuantumActive={isQuantumAvailable}
          />
          
          {entropyHistory.length > 0 && (
            <div className="compliance-container">
              <ComplianceBadge 
                entropy={entropyHistory[entropyHistory.length - 1].entropy}
                coherence={coherence}
                mode={isQuantumAvailable ? 'quantum' : 'classical'}
              />
              {assessment.quantum_used && (
                <span className="quantum-indicator">
                  Quantum Assessment Active
                </span>
              )}
            </div>
          )}
        </div>
        
        {error && (
          <div className="error-message">
            {error}
          </div>
        )}
      </div>
    </div>
  );
};

export default QuantumGovernanceDashboard;
