/**
 * API Key Controller
 * 
 * This controller handles API requests related to API keys.
 */

const ApiKeyService = require('../services/ApiKeyService');
const AuthService = require('../services/AuthService');
const { ValidationError, AuthenticationError } = require('../utils/errors');

class ApiKeyController {
  constructor() {
    this.apiKeyService = new ApiKeyService();
    this.authService = new AuthService();
  }

  /**
   * Get all API keys (admin only)
   */
  async getAllApiKeys(req, res, next) {
    try {
      // Check if user has admin role
      if (!this.authService.hasRole(req.user, 'admin')) {
        throw new AuthenticationError('Unauthorized');
      }
      
      const apiKeys = await this.apiKeyService.getAllApiKeys();
      
      res.json(apiKeys);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get API key by ID
   */
  async getApiKeyById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('API key ID is required');
      }
      
      // Get API key
      const apiKey = await this.apiKeyService.getApiKeyById(id);
      
      // Check if API key belongs to the current user or user is admin
      if (apiKey.userId !== req.user.id && !this.authService.hasRole(req.user, 'admin')) {
        throw new AuthenticationError('Unauthorized');
      }
      
      res.json(apiKey);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update API key
   */
  async updateApiKey(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('API key ID is required');
      }
      
      if (!data) {
        throw new ValidationError('API key data is required');
      }
      
      // Get API key
      const apiKey = await this.apiKeyService.getApiKeyById(id);
      
      // Check if API key belongs to the current user or user is admin
      if (apiKey.userId !== req.user.id && !this.authService.hasRole(req.user, 'admin')) {
        throw new AuthenticationError('Unauthorized');
      }
      
      // Only admin can update permissions
      if (!this.authService.hasRole(req.user, 'admin')) {
        delete data.permissions;
      }
      
      const updatedApiKey = await this.apiKeyService.updateApiKey(id, data);
      
      res.json(updatedApiKey);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Revoke all API keys for a user (admin only)
   */
  async revokeUserApiKeys(req, res, next) {
    try {
      const { userId } = req.params;
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      // Check if user has admin role
      if (!this.authService.hasRole(req.user, 'admin')) {
        throw new AuthenticationError('Unauthorized');
      }
      
      const result = await this.apiKeyService.revokeUserApiKeys(userId);
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new ApiKeyController();
