/**
 * Loading Spinner Component
 * 
 * A reusable component for displaying loading states.
 * It can be used in various sizes and with optional text.
 */

import React from 'react';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  text?: string;
  fullPage?: boolean;
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  text,
  fullPage = false,
  className = '',
}) => {
  // Define spinner size
  const getSizeClasses = (): string => {
    switch (size) {
      case 'small':
        return 'h-4 w-4 border-2';
      case 'large':
        return 'h-16 w-16 border-4';
      case 'medium':
      default:
        return 'h-8 w-8 border-3';
    }
  };

  const sizeClasses = getSizeClasses();
  const containerClasses = fullPage
    ? 'fixed inset-0 flex items-center justify-center bg-white bg-opacity-75 z-50'
    : 'flex flex-col items-center justify-center';

  return (
    <div className={`${containerClasses} ${className}`}>
      <div
        className={`${sizeClasses} animate-spin rounded-full border-t-transparent border-blue-600`}
        role="status"
      >
        <span className="sr-only">Loading...</span>
      </div>
      {text && <p className="mt-2 text-sm text-gray-600">{text}</p>}
    </div>
  );
};

export default LoadingSpinner;
