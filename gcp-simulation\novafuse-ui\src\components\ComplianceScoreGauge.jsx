import React from 'react';
import { Box, Typography, CircularProgress, Card, CardContent, CardHeader } from '@mui/material';

/**
 * ComplianceScoreGauge Component
 * 
 * Displays a circular gauge showing compliance score with color-coded indicators.
 * 
 * @param {Object} props - Component props
 * @param {number} props.score - Compliance score (0-100)
 * @param {string} props.title - Title for the gauge
 * @param {string} props.subtitle - Subtitle for the gauge
 * @param {Object} props.thresholds - Score thresholds for color coding
 * @param {number} props.thresholds.low - Threshold for low score (red)
 * @param {number} props.thresholds.medium - Threshold for medium score (yellow)
 * @param {number} props.thresholds.high - Threshold for high score (green)
 * @param {number} props.size - Size of the gauge in pixels
 */
const ComplianceScoreGauge = ({ 
  score = 0, 
  title = 'Compliance Score', 
  subtitle = 'Overall compliance status',
  thresholds = { low: 60, medium: 80, high: 90 },
  size = 200
}) => {
  // Ensure score is between 0 and 100
  const normalizedScore = Math.min(Math.max(score, 0), 100);
  
  // Determine color based on score thresholds
  const getScoreColor = (score) => {
    if (score >= thresholds.high) return 'success.main';
    if (score >= thresholds.medium) return 'warning.main';
    return 'error.main';
  };
  
  // Get status text based on score
  const getStatusText = (score) => {
    if (score >= thresholds.high) return 'Compliant';
    if (score >= thresholds.medium) return 'Partially Compliant';
    return 'Non-Compliant';
  };
  
  const color = getScoreColor(normalizedScore);
  const statusText = getStatusText(normalizedScore);
  
  return (
    <Card sx={{ maxWidth: size + 50 }}>
      <CardHeader
        title={title}
        subheader={subtitle}
        titleTypographyProps={{ variant: 'h6' }}
        subheaderTypographyProps={{ variant: 'body2' }}
      />
      <CardContent sx={{ display: 'flex', justifyContent: 'center' }}>
        <Box sx={{ position: 'relative', display: 'inline-flex' }}>
          <CircularProgress
            variant="determinate"
            value={100}
            size={size}
            thickness={4}
            sx={{ color: 'grey.200' }}
          />
          <CircularProgress
            variant="determinate"
            value={normalizedScore}
            size={size}
            thickness={4}
            sx={{ 
              color: color,
              position: 'absolute',
              left: 0,
            }}
          />
          <Box
            sx={{
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              position: 'absolute',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column'
            }}
          >
            <Typography variant="h3" component="div" color="text.primary" fontWeight="bold">
              {normalizedScore}%
            </Typography>
            <Typography variant="body2" component="div" color={color} fontWeight="medium">
              {statusText}
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ComplianceScoreGauge;
