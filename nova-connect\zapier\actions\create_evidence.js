/**
 * Create Compliance Evidence Action
 * 
 * This action creates a new compliance evidence record.
 */

// Define the action
module.exports = {
  key: 'create_compliance_evidence',
  noun: 'Compliance Evidence',
  
  // Display information
  display: {
    label: 'Create Compliance Evidence',
    description: 'Creates a new compliance evidence record.',
    important: true
  },
  
  // Operation
  operation: {
    // Perform operation
    type: 'perform',
    
    // Perform the operation
    perform: {
      url: '{{process.env.API_BASE_URL}}/api/zapier/actions/create-compliance-evidence',
      method: 'POST',
      headers: {
        Authorization: 'Bearer {{bundle.authData.access_token}}',
        'Content-Type': 'application/json'
      },
      body: {
        controlId: '{{bundle.inputData.controlId}}',
        evidenceType: '{{bundle.inputData.evidenceType}}',
        description: '{{bundle.inputData.description}}',
        data: '{{bundle.inputData.data}}'
      }
    },
    
    // Input fields
    inputFields: [
      {
        key: 'controlId',
        label: 'Control ID',
        type: 'string',
        required: true,
        helpText: 'The ID of the compliance control.'
      },
      {
        key: 'evidenceType',
        label: 'Evidence Type',
        type: 'string',
        required: true,
        choices: {
          document: 'Document',
          screenshot: 'Screenshot',
          log: 'Log',
          test_result: 'Test Result',
          attestation: 'Attestation'
        },
        helpText: 'The type of evidence.'
      },
      {
        key: 'description',
        label: 'Description',
        type: 'text',
        required: true,
        helpText: 'A description of the evidence.'
      },
      {
        key: 'data',
        label: 'Data',
        type: 'text',
        required: false,
        helpText: 'Additional data for the evidence in JSON format.'
      }
    ],
    
    // Sample data
    sample: {
      id: 'evid-123',
      controlId: 'ctrl-123',
      evidenceType: 'document',
      description: 'Sample evidence',
      createdAt: '2023-01-01T00:00:00Z'
    },
    
    // Output fields
    outputFields: [
      { key: 'id', label: 'ID' },
      { key: 'controlId', label: 'Control ID' },
      { key: 'evidenceType', label: 'Evidence Type' },
      { key: 'description', label: 'Description' },
      { key: 'createdAt', label: 'Created At' }
    ]
  }
};
