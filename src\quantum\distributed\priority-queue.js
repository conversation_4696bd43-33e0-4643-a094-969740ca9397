/**
 * Priority Queue
 *
 * This module provides a priority queue implementation for the Finite Universe
 * Principle defense system, enabling task prioritization for distributed processing.
 */

const EventEmitter = require('events');

/**
 * PriorityQueue class
 * 
 * Provides a priority queue implementation for task prioritization.
 */
class PriorityQueue extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      priorityLevels: options.priorityLevels || 10, // Number of priority levels (0-9, 0 is highest)
      defaultPriority: options.defaultPriority || 5, // Default priority level
      preemptionEnabled: options.preemptionEnabled !== undefined ? options.preemptionEnabled : true, // Whether preemption is enabled
      fairnessEnabled: options.fairnessEnabled !== undefined ? options.fairnessEnabled : true, // Whether fairness is enabled
      fairnessThreshold: options.fairnessThreshold || 5, // Number of tasks to process before checking lower priorities
      ...options
    };

    // Initialize queue
    this.queue = Array.from({ length: this.options.priorityLevels }, () => []);
    
    // Initialize queue statistics
    this.stats = {
      enqueued: 0,
      dequeued: 0,
      preempted: 0,
      priorityDistribution: Array.from({ length: this.options.priorityLevels }, () => 0)
    };
    
    // Initialize fairness counters
    this.fairnessCounters = Array.from({ length: this.options.priorityLevels }, () => 0);
    
    // Initialize current processing tasks
    this.processing = new Map();

    if (this.options.enableLogging) {
      console.log('PriorityQueue initialized with options:', this.options);
    }
  }

  /**
   * Enqueue a task
   * @param {Object} task - Task to enqueue
   * @param {number} priority - Priority level (0-9, 0 is highest)
   * @returns {string} - Task ID
   */
  enqueue(task, priority = this.options.defaultPriority) {
    // Validate priority
    priority = Math.max(0, Math.min(this.options.priorityLevels - 1, priority));
    
    // Generate task ID if not provided
    if (!task.id) {
      task.id = this._generateTaskId();
    }
    
    // Create task object
    const taskObj = {
      ...task,
      priority,
      enqueuedAt: Date.now(),
      attempts: 0
    };
    
    // Add task to queue
    this.queue[priority].push(taskObj);
    
    // Update statistics
    this.stats.enqueued++;
    this.stats.priorityDistribution[priority]++;
    
    if (this.options.enableLogging) {
      console.log(`Task ${taskObj.id} enqueued with priority ${priority}`);
    }
    
    // Emit enqueue event
    this.emit('enqueue', { taskId: taskObj.id, priority });
    
    // Check for preemption
    if (this.options.preemptionEnabled) {
      this._checkPreemption(taskObj);
    }
    
    return taskObj.id;
  }

  /**
   * Dequeue a task
   * @returns {Object} - Dequeued task or null if queue is empty
   */
  dequeue() {
    // Check if queue is empty
    if (this.isEmpty()) {
      return null;
    }
    
    // Find highest priority non-empty queue
    let priority = 0;
    
    if (this.options.fairnessEnabled) {
      // Use fairness algorithm
      priority = this._getFairPriority();
    } else {
      // Use strict priority
      while (priority < this.options.priorityLevels && this.queue[priority].length === 0) {
        priority++;
      }
    }
    
    // Get task from queue
    const task = this.queue[priority].shift();
    
    // Update statistics
    this.stats.dequeued++;
    
    // Update fairness counter
    this.fairnessCounters[priority]++;
    
    // Add task to processing map
    this.processing.set(task.id, {
      ...task,
      startedAt: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`Task ${task.id} dequeued with priority ${priority}`);
    }
    
    // Emit dequeue event
    this.emit('dequeue', { taskId: task.id, priority });
    
    return task;
  }

  /**
   * Complete a task
   * @param {string} taskId - Task ID
   * @param {Object} result - Task result
   * @returns {boolean} - True if task was completed, false otherwise
   */
  complete(taskId, result) {
    // Check if task is being processed
    if (!this.processing.has(taskId)) {
      if (this.options.enableLogging) {
        console.log(`Task ${taskId} not found in processing map`);
      }
      return false;
    }
    
    // Get task
    const task = this.processing.get(taskId);
    
    // Remove task from processing map
    this.processing.delete(taskId);
    
    if (this.options.enableLogging) {
      console.log(`Task ${taskId} completed`);
    }
    
    // Emit complete event
    this.emit('complete', { taskId, task, result });
    
    return true;
  }

  /**
   * Fail a task
   * @param {string} taskId - Task ID
   * @param {Error} error - Error
   * @param {boolean} retry - Whether to retry the task
   * @returns {boolean} - True if task was failed, false otherwise
   */
  fail(taskId, error, retry = true) {
    // Check if task is being processed
    if (!this.processing.has(taskId)) {
      if (this.options.enableLogging) {
        console.log(`Task ${taskId} not found in processing map`);
      }
      return false;
    }
    
    // Get task
    const task = this.processing.get(taskId);
    
    // Remove task from processing map
    this.processing.delete(taskId);
    
    if (retry) {
      // Increment attempts
      task.attempts++;
      
      // Re-enqueue task
      this.queue[task.priority].push(task);
      
      if (this.options.enableLogging) {
        console.log(`Task ${taskId} failed and re-enqueued (attempt ${task.attempts})`);
      }
      
      // Emit retry event
      this.emit('retry', { taskId, task, error });
    } else {
      if (this.options.enableLogging) {
        console.log(`Task ${taskId} failed permanently`);
      }
      
      // Emit fail event
      this.emit('fail', { taskId, task, error });
    }
    
    return true;
  }

  /**
   * Preempt a task
   * @param {string} taskId - Task ID
   * @returns {boolean} - True if task was preempted, false otherwise
   */
  preempt(taskId) {
    // Check if task is being processed
    if (!this.processing.has(taskId)) {
      if (this.options.enableLogging) {
        console.log(`Task ${taskId} not found in processing map`);
      }
      return false;
    }
    
    // Get task
    const task = this.processing.get(taskId);
    
    // Remove task from processing map
    this.processing.delete(taskId);
    
    // Re-enqueue task
    this.queue[task.priority].push(task);
    
    // Update statistics
    this.stats.preempted++;
    
    if (this.options.enableLogging) {
      console.log(`Task ${taskId} preempted`);
    }
    
    // Emit preempt event
    this.emit('preempt', { taskId, task });
    
    return true;
  }

  /**
   * Check if queue is empty
   * @returns {boolean} - True if queue is empty, false otherwise
   */
  isEmpty() {
    return this.queue.every(q => q.length === 0);
  }

  /**
   * Get queue size
   * @returns {number} - Queue size
   */
  size() {
    return this.queue.reduce((sum, q) => sum + q.length, 0);
  }

  /**
   * Get queue statistics
   * @returns {Object} - Queue statistics
   */
  getStats() {
    return {
      ...this.stats,
      size: this.size(),
      processing: this.processing.size
    };
  }

  /**
   * Get fair priority
   * @returns {number} - Priority level
   * @private
   */
  _getFairPriority() {
    // Check highest priority first
    for (let priority = 0; priority < this.options.priorityLevels; priority++) {
      // Skip empty queues
      if (this.queue[priority].length === 0) {
        continue;
      }
      
      // Check if fairness threshold is reached
      if (this.fairnessCounters[priority] < this.options.fairnessThreshold) {
        return priority;
      }
    }
    
    // Reset fairness counters
    this.fairnessCounters = Array.from({ length: this.options.priorityLevels }, () => 0);
    
    // Find highest priority non-empty queue
    for (let priority = 0; priority < this.options.priorityLevels; priority++) {
      if (this.queue[priority].length > 0) {
        return priority;
      }
    }
    
    // Should never reach here if queue is not empty
    return 0;
  }

  /**
   * Check for preemption
   * @param {Object} task - Task to check for preemption
   * @private
   */
  _checkPreemption(task) {
    // Skip if no tasks are being processed
    if (this.processing.size === 0) {
      return;
    }
    
    // Skip if task priority is not higher than any processing task
    let shouldPreempt = false;
    let taskToPreempt = null;
    
    for (const [taskId, processingTask] of this.processing.entries()) {
      if (task.priority < processingTask.priority) {
        shouldPreempt = true;
        taskToPreempt = taskId;
        break;
      }
    }
    
    if (shouldPreempt && taskToPreempt) {
      // Preempt task
      this.preempt(taskToPreempt);
    }
  }

  /**
   * Generate a unique task ID
   * @returns {string} - Task ID
   * @private
   */
  _generateTaskId() {
    return Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15);
  }

  /**
   * Dispose resources
   */
  dispose() {
    // Clear queue
    this.queue = Array.from({ length: this.options.priorityLevels }, () => []);
    
    // Clear processing map
    this.processing.clear();
    
    if (this.options.enableLogging) {
      console.log('PriorityQueue disposed');
    }
  }
}

/**
 * Create a priority queue with recommended settings
 * @param {Object} options - Configuration options
 * @returns {PriorityQueue} - Configured priority queue
 */
function createPriorityQueue(options = {}) {
  return new PriorityQueue({
    enableLogging: true,
    priorityLevels: 10,
    defaultPriority: 5,
    preemptionEnabled: true,
    fairnessEnabled: true,
    fairnessThreshold: 5,
    ...options
  });
}

module.exports = {
  PriorityQueue,
  createPriorityQueue
};
