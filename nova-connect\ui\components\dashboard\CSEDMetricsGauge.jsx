import React from 'react';
import { Box, Typography, useTheme } from '@mui/material';
import { 
  <PERSON><PERSON><PERSON>, 
  Pie, 
  Cell, 
  ResponsiveContainer,
  Label,
  Tooltip
} from 'recharts';

/**
 * CSED Metrics Gauge Component
 * 
 * Displays a gauge visualization of CSDE performance metrics.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.metrics - Metrics data
 * @param {number} props.size - Size of the gauge
 */
const CSEDMetricsGauge = ({ metrics, size = 200 }) => {
  const theme = useTheme();
  
  // Calculate overall success rate
  const calculateOverallSuccessRate = () => {
    const totalRequests = 
      metrics.offlineProcessing.totalRequests + 
      metrics.crossDomainPrediction.totalRequests + 
      metrics.complianceMapping.totalRequests;
    
    const successfulRequests = 
      metrics.offlineProcessing.successfulRequests + 
      metrics.crossDomainPrediction.successfulRequests + 
      metrics.complianceMapping.successfulRequests;
    
    return totalRequests > 0 ? successfulRequests / totalRequests : 1;
  };
  
  // Calculate average latency
  const calculateAverageLatency = () => {
    const totalLatency = 
      metrics.offlineProcessing.totalLatency + 
      metrics.crossDomainPrediction.totalLatency + 
      metrics.complianceMapping.totalLatency;
    
    const totalSuccessfulRequests = 
      metrics.offlineProcessing.successfulRequests + 
      metrics.crossDomainPrediction.successfulRequests + 
      metrics.complianceMapping.successfulRequests;
    
    return totalSuccessfulRequests > 0 ? totalLatency / totalSuccessfulRequests : 0;
  };
  
  // Calculate total requests
  const calculateTotalRequests = () => {
    return metrics.offlineProcessing.totalRequests + 
      metrics.crossDomainPrediction.totalRequests + 
      metrics.complianceMapping.totalRequests;
  };
  
  // Get success rate and format as percentage
  const successRate = calculateOverallSuccessRate();
  const successRateFormatted = `${(successRate * 100).toFixed(1)}%`;
  
  // Get average latency and format
  const averageLatency = calculateAverageLatency();
  const averageLatencyFormatted = `${averageLatency.toFixed(2)}ms`;
  
  // Get total requests
  const totalRequests = calculateTotalRequests();
  
  // Prepare data for gauge chart
  const gaugeData = [
    { name: 'Success', value: successRate },
    { name: 'Failure', value: 1 - successRate }
  ];
  
  // Define colors based on success rate
  const getGaugeColors = () => {
    if (successRate >= 0.95) {
      return [theme.palette.success.main, theme.palette.grey[300]];
    } else if (successRate >= 0.9) {
      return [theme.palette.warning.main, theme.palette.grey[300]];
    } else {
      return [theme.palette.error.main, theme.palette.grey[300]];
    }
  };
  
  const gaugeColors = getGaugeColors();
  
  // Additional metrics to display
  const additionalMetrics = [
    { label: 'Total Requests', value: totalRequests },
    { label: 'Avg. Latency', value: averageLatencyFormatted },
    { label: 'Offline Success', value: `${(metrics.offlineProcessing.successfulRequests / (metrics.offlineProcessing.totalRequests || 1) * 100).toFixed(1)}%` },
    { label: 'Prediction Success', value: `${(metrics.crossDomainPrediction.successfulRequests / (metrics.crossDomainPrediction.totalRequests || 1) * 100).toFixed(1)}%` },
    { label: 'Mapping Success', value: `${(metrics.complianceMapping.successfulRequests / (metrics.complianceMapping.totalRequests || 1) * 100).toFixed(1)}%` }
  ];
  
  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      justifyContent: 'center',
      height: '100%',
      position: 'relative'
    }}>
      {/* Gauge Chart */}
      <Box sx={{ width: size, height: size, position: 'relative' }}>
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={gaugeData}
              cx="50%"
              cy="50%"
              startAngle={180}
              endAngle={0}
              innerRadius={size * 0.6 / 2}
              outerRadius={size * 0.8 / 2}
              paddingAngle={0}
              dataKey="value"
            >
              {gaugeData.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={gaugeColors[index % gaugeColors.length]} 
                />
              ))}
              <Label
                value={successRateFormatted}
                position="center"
                fill={theme.palette.text.primary}
                style={{
                  fontSize: size * 0.15,
                  fontWeight: 'bold',
                  fontFamily: theme.typography.fontFamily
                }}
              />
            </Pie>
            <Tooltip />
          </PieChart>
        </ResponsiveContainer>
        
        {/* Success Rate Label */}
        <Typography 
          variant="caption" 
          color="text.secondary"
          sx={{ 
            position: 'absolute', 
            bottom: 0, 
            left: 0, 
            right: 0, 
            textAlign: 'center' 
          }}
        >
          Success Rate
        </Typography>
      </Box>
      
      {/* Additional Metrics */}
      <Box sx={{ 
        display: 'flex', 
        flexWrap: 'wrap', 
        justifyContent: 'center',
        mt: 1,
        gap: 2
      }}>
        {additionalMetrics.map((metric, index) => (
          <Box 
            key={index} 
            sx={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center',
              minWidth: 80
            }}
          >
            <Typography variant="caption" color="text.secondary">
              {metric.label}
            </Typography>
            <Typography variant="body2" fontWeight="bold">
              {metric.value}
            </Typography>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default CSEDMetricsGauge;
