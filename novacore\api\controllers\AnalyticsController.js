/**
 * Analytics Controller
 * 
 * This controller handles analytics events from the frontend.
 */

const logger = require('../../config/logger');
const { getRedisClient } = require('../../config/redis');
const { createMetric } = require('../../monitoring/metrics');

// Redis client for analytics
const redisClient = getRedisClient();

// Metrics
const metrics = {
  eventCount: createMetric('visualization_analytics_event_count', 'counter', 'Number of visualization analytics events'),
  viewDuration: createMetric('visualization_analytics_view_duration', 'histogram', 'View duration for visualizations in seconds'),
  interactionCount: createMetric('visualization_analytics_interaction_count', 'counter', 'Number of interactions with visualizations'),
  errorCount: createMetric('visualization_analytics_error_count', 'counter', 'Number of errors in visualizations')
};

/**
 * Record visualization events
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const recordVisualizationEvents = async (req, res, next) => {
  try {
    logger.debug('Recording visualization events');
    
    // Get events from request body
    const { events } = req.body;
    
    // Validate events
    if (!events || !Array.isArray(events) || events.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invalid events data',
          code: 'INVALID_EVENTS'
        }
      });
    }
    
    // Process events
    await processEvents(events);
    
    // Return success
    res.status(200).json({
      success: true,
      message: `Recorded ${events.length} events`
    });
  } catch (error) {
    logger.error('Error recording visualization events:', error);
    next(error);
  }
};

/**
 * Get visualization analytics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getVisualizationAnalytics = async (req, res, next) => {
  try {
    logger.debug('Getting visualization analytics');
    
    // Get query parameters
    const { timeRange = '7d', visualizationType } = req.query;
    
    // Calculate time range
    const endTime = Date.now();
    let startTime;
    
    switch (timeRange) {
      case '1d':
        startTime = endTime - 24 * 60 * 60 * 1000; // 1 day
        break;
      case '7d':
        startTime = endTime - 7 * 24 * 60 * 60 * 1000; // 7 days
        break;
      case '30d':
        startTime = endTime - 30 * 24 * 60 * 60 * 1000; // 30 days
        break;
      case '90d':
        startTime = endTime - 90 * 24 * 60 * 60 * 1000; // 90 days
        break;
      default:
        startTime = endTime - 7 * 24 * 60 * 60 * 1000; // Default to 7 days
    }
    
    // Get analytics data
    const analytics = await getAnalyticsData(startTime, endTime, visualizationType);
    
    // Return analytics
    res.status(200).json(analytics);
  } catch (error) {
    logger.error('Error getting visualization analytics:', error);
    next(error);
  }
};

/**
 * Process events
 * @param {Array} events - Events to process
 */
const processEvents = async (events) => {
  try {
    // Process each event
    for (const event of events) {
      // Validate event
      if (!event.eventType || !event.visualizationType) {
        logger.warn('Invalid event:', event);
        continue;
      }
      
      // Store event in Redis
      await storeEvent(event);
      
      // Update metrics
      updateMetrics(event);
    }
  } catch (error) {
    logger.error('Error processing events:', error);
    throw error;
  }
};

/**
 * Store event in Redis
 * @param {Object} event - Event to store
 */
const storeEvent = async (event) => {
  try {
    // Create key for event
    const key = `analytics:visualization:${event.visualizationType}:${event.eventType}:${Date.now()}`;
    
    // Store event in Redis
    await redisClient.set(key, JSON.stringify(event));
    
    // Set TTL for event (90 days)
    await redisClient.expire(key, 90 * 24 * 60 * 60);
  } catch (error) {
    logger.error('Error storing event in Redis:', error);
    throw error;
  }
};

/**
 * Update metrics
 * @param {Object} event - Event to update metrics for
 */
const updateMetrics = (event) => {
  try {
    // Update event count
    metrics.eventCount.inc({
      eventType: event.eventType,
      visualizationType: event.visualizationType
    });
    
    // Update specific metrics based on event type
    switch (event.eventType) {
      case 'view':
        // Update view duration if available
        if (event.details && event.details.viewDuration) {
          metrics.viewDuration.observe({
            visualizationType: event.visualizationType
          }, event.details.viewDuration);
        }
        break;
      
      case 'interact':
        // Update interaction count
        metrics.interactionCount.inc({
          visualizationType: event.visualizationType,
          interactionType: event.details && event.details.interactionType
        });
        break;
      
      case 'error':
        // Update error count
        metrics.errorCount.inc({
          visualizationType: event.visualizationType,
          errorType: event.details && event.details.errorType
        });
        break;
    }
  } catch (error) {
    logger.error('Error updating metrics:', error);
  }
};

/**
 * Get analytics data
 * @param {number} startTime - Start time in milliseconds
 * @param {number} endTime - End time in milliseconds
 * @param {string} visualizationType - Visualization type filter
 * @returns {Object} - Analytics data
 */
const getAnalyticsData = async (startTime, endTime, visualizationType) => {
  try {
    // Get events from Redis
    const events = await getEventsFromRedis(startTime, endTime, visualizationType);
    
    // Process events into analytics data
    const analytics = {
      summary: {
        totalEvents: events.length,
        uniqueUsers: new Set(events.filter(e => e.userId).map(e => e.userId)).size,
        uniqueSessions: new Set(events.map(e => e.sessionId)).size
      },
      eventCounts: {},
      visualizationUsage: {},
      interactionTypes: {},
      errorTypes: {},
      viewDuration: {},
      timeSeriesData: {}
    };
    
    // Process events
    events.forEach(event => {
      // Count events by type
      if (!analytics.eventCounts[event.eventType]) {
        analytics.eventCounts[event.eventType] = 0;
      }
      analytics.eventCounts[event.eventType]++;
      
      // Count events by visualization type
      if (!analytics.visualizationUsage[event.visualizationType]) {
        analytics.visualizationUsage[event.visualizationType] = 0;
      }
      analytics.visualizationUsage[event.visualizationType]++;
      
      // Process specific event types
      switch (event.eventType) {
        case 'view':
          // Track view duration
          if (event.details && event.details.viewDuration) {
            if (!analytics.viewDuration[event.visualizationType]) {
              analytics.viewDuration[event.visualizationType] = {
                total: 0,
                count: 0
              };
            }
            
            analytics.viewDuration[event.visualizationType].total += event.details.viewDuration;
            analytics.viewDuration[event.visualizationType].count++;
          }
          break;
        
        case 'interact':
          // Track interaction types
          if (event.details && event.details.interactionType) {
            if (!analytics.interactionTypes[event.details.interactionType]) {
              analytics.interactionTypes[event.details.interactionType] = 0;
            }
            analytics.interactionTypes[event.details.interactionType]++;
          }
          break;
        
        case 'error':
          // Track error types
          if (event.details && event.details.errorType) {
            if (!analytics.errorTypes[event.details.errorType]) {
              analytics.errorTypes[event.details.errorType] = 0;
            }
            analytics.errorTypes[event.details.errorType]++;
          }
          break;
      }
      
      // Add to time series data
      const date = new Date(event.timestamp);
      const day = date.toISOString().split('T')[0];
      
      if (!analytics.timeSeriesData[day]) {
        analytics.timeSeriesData[day] = {
          date: day,
          total: 0
        };
      }
      
      analytics.timeSeriesData[day].total++;
      
      // Add count for event type
      const eventTypeKey = `${event.eventType}Count`;
      if (!analytics.timeSeriesData[day][eventTypeKey]) {
        analytics.timeSeriesData[day][eventTypeKey] = 0;
      }
      analytics.timeSeriesData[day][eventTypeKey]++;
      
      // Add count for visualization type
      const visualizationTypeKey = `${event.visualizationType}Count`;
      if (!analytics.timeSeriesData[day][visualizationTypeKey]) {
        analytics.timeSeriesData[day][visualizationTypeKey] = 0;
      }
      analytics.timeSeriesData[day][visualizationTypeKey]++;
    });
    
    // Calculate average view duration
    Object.keys(analytics.viewDuration).forEach(key => {
      const data = analytics.viewDuration[key];
      analytics.viewDuration[key] = {
        ...data,
        average: data.count > 0 ? data.total / data.count : 0
      };
    });
    
    // Convert time series data to array
    analytics.timeSeriesData = Object.values(analytics.timeSeriesData).sort((a, b) => a.date.localeCompare(b.date));
    
    return analytics;
  } catch (error) {
    logger.error('Error getting analytics data:', error);
    throw error;
  }
};

/**
 * Get events from Redis
 * @param {number} startTime - Start time in milliseconds
 * @param {number} endTime - End time in milliseconds
 * @param {string} visualizationType - Visualization type filter
 * @returns {Array} - Events
 */
const getEventsFromRedis = async (startTime, endTime, visualizationType) => {
  try {
    // Get all keys for events
    const pattern = visualizationType
      ? `analytics:visualization:${visualizationType}:*`
      : 'analytics:visualization:*';
    
    const keys = await redisClient.keys(pattern);
    
    // Get events for keys
    const events = [];
    
    for (const key of keys) {
      const data = await redisClient.get(key);
      if (data) {
        try {
          const event = JSON.parse(data);
          
          // Check if event is within time range
          const eventTime = new Date(event.timestamp).getTime();
          if (eventTime >= startTime && eventTime <= endTime) {
            events.push(event);
          }
        } catch (error) {
          logger.error(`Error parsing event data for key ${key}:`, error);
        }
      }
    }
    
    return events;
  } catch (error) {
    logger.error('Error getting events from Redis:', error);
    return [];
  }
};

module.exports = {
  recordVisualizationEvents,
  getVisualizationAnalytics
};
