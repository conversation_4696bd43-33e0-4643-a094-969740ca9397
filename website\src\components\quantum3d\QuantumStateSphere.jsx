import React, { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';

const QuantumStateSphere = ({ 
  position = [0, 0, 0],
  entropy = 0.5,
  coherence = 1.0,
  color = '#8a2be2',
  ...props 
}) => {
  const meshRef = useRef();
  const materialRef = useRef();
  
  // Calculate sphere size based on entropy
  const radius = useMemo(() => {
    // Base size + entropy influence
    return 0.5 + (entropy * 0.5);
  }, [entropy]);
  
  // Animate the sphere
  useFrame(({ clock }) => {
    if (!meshRef.current) return;
    
    // Pulsing effect based on coherence
    const pulse = Math.sin(clock.getElapsedTime() * 2) * 0.05 * coherence;
    meshRef.current.scale.setScalar(1 + pulse);
    
    // Subtle rotation
    meshRef.current.rotation.y += 0.002 * coherence;
    meshRef.current.rotation.x += 0.001 * coherence;
    
    // Update material properties
    if (materialRef.current) {
      materialRef.current.opacity = 0.6 + (coherence * 0.4);
      materialRef.current.emissiveIntensity = coherence * 0.5;
    }
  });
  
  // Generate gradient texture for the sphere
  const gradientTexture = useMemo(() => {
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 512;
    const context = canvas.getContext('2d');
    
    const gradient = context.createRadialGradient(
      canvas.width / 2, 
      canvas.height / 2, 
      0, 
      canvas.width / 2, 
      canvas.height / 2, 
      canvas.width / 2
    );
    
    gradient.addColorStop(0, '#ffffff');
    gradient.addColorStop(0.5, color);
    gradient.addColorStop(1, '#000000');
    
    context.fillStyle = gradient;
    context.fillRect(0, 0, canvas.width, canvas.height);
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    return texture;
  }, [color]);
  
  return (
    <group position={position} {...props}>
      <mesh ref={meshRef} castShadow receiveShadow>
        <sphereGeometry args={[radius, 64, 64]} />
        <meshStandardMaterial
          ref={materialRef}
          map={gradientTexture}
          color={color}
          transparent={true}
          opacity={0.8}
          roughness={0.3}
          metalness={0.7}
          emissive={color}
          emissiveIntensity={0.3}
          side={THREE.DoubleSide}
        />
      </mesh>
      
      {/* Glow effect */}
      <pointLight
        color={color}
        intensity={1 * coherence}
        distance={radius * 3}
        decay={2}
      />
    </group>
  );
};

export default React.memo(QuantumStateSphere);
