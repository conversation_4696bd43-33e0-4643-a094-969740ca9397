/**
 * NovaCore Cyber-Safety Routes
 *
 * This file defines the routes for the Cyber-Safety platform.
 */

const express = require('express');
const router = express.Router();
const { authenticate, authorize } = require('../middleware/authMiddleware');
const cyberSafetyVisualizationRoutes = require('./cyberSafetyVisualizationRoutes');
const { safetyService } = require('../../cyber-safety/services');

// Get safety score for an operation
router.get(
  '/score/:operationId',
  authenticate,
  authorize('read:safety'),
  async (req, res, next) => {
    try {
      const score = await safetyService.calculateSafetyScore({
        operationId: req.params.operationId
      });

      res.status(200).json({
        success: true,
        data: score
      });
    } catch (error) {
      next(error);
    }
  }
);

// Verify safety of an operation
router.get(
  '/verify/:operationId',
  authenticate,
  authorize('read:safety'),
  async (req, res, next) => {
    try {
      const result = await safetyService.verifySafety(req.params.operationId);

      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
);

// Assess risk for an operation
router.get(
  '/risk/:operationId',
  authenticate,
  authorize('read:safety'),
  async (req, res, next) => {
    try {
      const risk = await safetyService.assessRisk({
        operationId: req.params.operationId
      });

      res.status(200).json({
        success: true,
        data: risk
      });
    } catch (error) {
      next(error);
    }
  }
);

// Map operation to compliance frameworks
router.get(
  '/compliance/:operationId',
  authenticate,
  authorize('read:safety'),
  async (req, res, next) => {
    try {
      const compliance = await safetyService.mapToCompliance({
        operationId: req.params.operationId
      });

      res.status(200).json({
        success: true,
        data: compliance
      });
    } catch (error) {
      next(error);
    }
  }
);

// Get all safety policies
router.get(
  '/policies',
  authenticate,
  authorize('read:safety'),
  async (req, res, next) => {
    try {
      const { SafetyPolicy } = require('../../cyber-safety/models');

      // Extract filter parameters from query
      const filter = {};

      if (req.query.enabled !== undefined) {
        filter.enabled = req.query.enabled === 'true';
      }

      if (req.query.severity) {
        filter.severity = req.query.severity;
      }

      if (req.query.tags) {
        filter.tags = { $all: req.query.tags.split(',') };
      }

      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };

      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      } else {
        options.sort = { priority: 1 };
      }

      // Execute query with pagination
      const skip = (page - 1) * limit;

      const [policies, total] = await Promise.all([
        SafetyPolicy.find(filter)
          .sort(options.sort)
          .skip(skip)
          .limit(options.limit),
        SafetyPolicy.countDocuments(filter)
      ]);

      // Calculate pagination info
      const totalPages = Math.ceil(total / options.limit);
      const hasNext = options.page < totalPages;
      const hasPrev = options.page > 1;

      res.status(200).json({
        success: true,
        data: policies,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          totalPages,
          hasNext,
          hasPrev
        }
      });
    } catch (error) {
      next(error);
    }
  }
);

// Get safety policy by ID
router.get(
  '/policies/:id',
  authenticate,
  authorize('read:safety'),
  async (req, res, next) => {
    try {
      const { SafetyPolicy } = require('../../cyber-safety/models');
      const policy = await SafetyPolicy.findById(req.params.id);

      if (!policy) {
        return res.status(404).json({
          success: false,
          error: {
            message: `Safety policy with ID ${req.params.id} not found`,
            code: 'NOT_FOUND_ERROR'
          }
        });
      }

      res.status(200).json({
        success: true,
        data: policy
      });
    } catch (error) {
      next(error);
    }
  }
);

// Create safety policy
router.post(
  '/policies',
  authenticate,
  authorize('create:safety'),
  async (req, res, next) => {
    try {
      const { SafetyPolicy } = require('../../cyber-safety/models');

      // Set created by
      req.body.createdBy = req.user.id;
      req.body.updatedBy = req.user.id;

      const policy = new SafetyPolicy(req.body);
      await policy.save();

      res.status(201).json({
        success: true,
        data: policy
      });
    } catch (error) {
      next(error);
    }
  }
);

// Update safety policy
router.put(
  '/policies/:id',
  authenticate,
  authorize('update:safety'),
  async (req, res, next) => {
    try {
      const { SafetyPolicy } = require('../../cyber-safety/models');

      // Set updated by
      req.body.updatedBy = req.user.id;

      const policy = await SafetyPolicy.findByIdAndUpdate(
        req.params.id,
        req.body,
        { new: true, runValidators: true }
      );

      if (!policy) {
        return res.status(404).json({
          success: false,
          error: {
            message: `Safety policy with ID ${req.params.id} not found`,
            code: 'NOT_FOUND_ERROR'
          }
        });
      }

      res.status(200).json({
        success: true,
        data: policy
      });
    } catch (error) {
      next(error);
    }
  }
);

// Delete safety policy
router.delete(
  '/policies/:id',
  authenticate,
  authorize('delete:safety'),
  async (req, res, next) => {
    try {
      const { SafetyPolicy } = require('../../cyber-safety/models');
      const policy = await SafetyPolicy.findByIdAndDelete(req.params.id);

      if (!policy) {
        return res.status(404).json({
          success: false,
          error: {
            message: `Safety policy with ID ${req.params.id} not found`,
            code: 'NOT_FOUND_ERROR'
          }
        });
      }

      res.status(200).json({
        success: true,
        message: 'Safety policy deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }
);

// Mount visualization routes
// These routes provide data for the Cyber-Safety fusion visualizations
router.use('/visualizations', cyberSafetyVisualizationRoutes);

module.exports = router;
