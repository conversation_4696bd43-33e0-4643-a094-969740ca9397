<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hardware/Software Implementation Layer Diagram</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 800px;
            height: 650px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            box-sizing: border-box;
        }
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
        }
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            box-sizing: border-box;
            font-size: 14px;
            line-height: 1.2;
            z-index: 1;
        }
        .component-number {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 20px;
            height: 20px;
            background-color: #555555; /* Grey color for patent compliance */
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
        }
        .component-label {
            font-weight: bold;
            margin-bottom: 4px;
        }
        .arrow {
            position: absolute;
            background-color: #333;
        }
        .arrow-down {
            width: 2px;
            height: 20px;
        }
        .arrow-right {
            height: 2px;
            width: 20px;
        }
        .legend {
            position: absolute;
            right: 10px;
            bottom: 10px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            z-index: 10;
            width: 200px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border: 1px solid #333;
        }
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 10px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>FIG. 5: Hardware/Software Implementation Layer Diagram</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 600px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">HARDWARE/SOFTWARE IMPLEMENTATION LAYER</div>
        </div>

        <!-- Hardware Layer -->
        <div class="container-box" style="width: 700px; height: 150px; left: 50px; top: 70px;">
            <div class="container-label" style="font-size: 16px;">HARDWARE LAYER</div>
        </div>

        <div class="component-box" style="left: 100px; top: 110px; width: 150px; height: 80px;">
            <div class="component-number">701</div>
            <div class="component-label">Tensor Processing Units</div>
            <div style="font-size: 12px; text-align: center;">
                Specialized for UUFT Tensor Operations
            </div>
        </div>

        <div class="component-box" style="left: 300px; top: 110px; width: 150px; height: 80px;">
            <div class="component-number">702</div>
            <div class="component-label">Field-Programmable Gate Arrays</div>
            <div style="font-size: 12px; text-align: center;">
                Reconfigurable for Fusion Operations
            </div>
        </div>

        <div class="component-box" style="left: 500px; top: 110px; width: 150px; height: 80px;">
            <div class="component-number">703</div>
            <div class="component-label">Application-Specific ICs</div>
            <div style="font-size: 12px; text-align: center;">
                Optimized for π10³ Calculations
            </div>
        </div>

        <!-- Middleware Layer -->
        <div class="container-box" style="width: 700px; height: 150px; left: 50px; top: 250px;">
            <div class="container-label" style="font-size: 16px;">MIDDLEWARE LAYER</div>
        </div>

        <div class="component-box" style="left: 100px; top: 290px; width: 150px; height: 80px;">
            <div class="component-number">704</div>
            <div class="component-label">Trinitarian Processing Framework</div>
            <div style="font-size: 12px; text-align: center;">
                Source/Validation/Integration
            </div>
        </div>

        <div class="component-box" style="left: 300px; top: 290px; width: 150px; height: 80px;">
            <div class="component-number">705</div>
            <div class="component-label">18/82 Resource Allocator</div>
            <div style="font-size: 12px; text-align: center;">
                Optimal Resource Distribution
            </div>
        </div>

        <div class="component-box" style="left: 500px; top: 290px; width: 150px; height: 80px;">
            <div class="component-number">706</div>
            <div class="component-label">Cross-Domain Pattern Detector</div>
            <div style="font-size: 12px; text-align: center;">
                Multi-domain Intelligence
            </div>
        </div>

        <!-- Software Layer -->
        <div class="container-box" style="width: 700px; height: 150px; left: 50px; top: 430px;">
            <div class="container-label" style="font-size: 16px;">SOFTWARE LAYER</div>
        </div>

        <div class="component-box" style="left: 100px; top: 470px; width: 150px; height: 80px;">
            <div class="component-number">707</div>
            <div class="component-label">NovaFuse Universal Components</div>
            <div style="font-size: 12px; text-align: center;">
                13 Core Software Modules
            </div>
        </div>

        <div class="component-box" style="left: 300px; top: 470px; width: 150px; height: 80px;">
            <div class="component-number">708</div>
            <div class="component-label">Domain-Specific Adaptors</div>
            <div style="font-size: 12px; text-align: center;">
                Industry-Specific Implementations
            </div>
        </div>

        <div class="component-box" style="left: 500px; top: 470px; width: 150px; height: 80px;">
            <div class="component-number">709</div>
            <div class="component-label">User Interface Layer</div>
            <div style="font-size: 12px; text-align: center;">
                NovaVision Framework
            </div>
        </div>

        <!-- Connecting arrows -->
        <div class="arrow arrow-down" style="left: 175px; top: 190px;"></div>
        <div class="arrow arrow-down" style="left: 375px; top: 190px;"></div>
        <div class="arrow arrow-down" style="left: 575px; top: 190px;"></div>

        <div class="arrow arrow-down" style="left: 175px; top: 370px;"></div>
        <div class="arrow arrow-down" style="left: 375px; top: 370px;"></div>
        <div class="arrow arrow-down" style="left: 575px; top: 370px;"></div>

        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Hardware Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Middleware Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Software Components</div>
            </div>
        </div>

        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
