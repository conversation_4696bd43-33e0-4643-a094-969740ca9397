/**
 * Trinity-Comphyon Integration Test
 * 
 * This script tests the integration between the Comphyological Trinity and
 * the Comphyon Meter-Governor system, demonstrating how the fundamental laws
 * of system behavior connect with the measurement and control mechanisms.
 */

const fs = require('fs');
const path = require('path');
const {
  ComphyologicalTrinity,
  ComphyonMeter,
  ComphyonGovernor,
  TrinityComphyonBridge
} = require('../../src/comphyology');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../resonance_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Test Trinity-Comphyon Bridge
 */
function testTrinityComphyonBridge() {
  console.log('=== Testing Trinity-Comphyon Bridge ===');
  
  // Create components
  const trinity = new ComphyologicalTrinity({
    enforceFirstLaw: true,
    enforceSecondLaw: true,
    enforceThirdLaw: true,
    logGovernance: true
  });
  
  const comphyonMeter = new ComphyonMeter({
    logMeasurements: true
  });
  
  const comphyonGovernor = new ComphyonGovernor({
    logGovernance: true
  });
  
  // Create bridge
  const bridge = new TrinityComphyonBridge({
    enforceFirstLaw: true,
    enforceSecondLaw: true,
    enforceThirdLaw: true,
    comphyonMeterEnabled: true,
    comphyonGovernorEnabled: true,
    comphyonDirectorEnabled: true,
    logBridge: true
  });
  
  // Override placeholder implementations with actual components
  bridge.comphyonMeter = comphyonMeter;
  bridge.comphyonGovernor = comphyonGovernor;
  
  // Test with different values
  console.log('\nTesting with different values:');
  
  const testValues = [0.1, 0.3, 0.5, 0.7, 0.9, 1.1, 1.3, 1.5, 1.7, 1.9];
  
  const results = testValues.map(value => {
    try {
      const result = bridge.process(value, { domain: 'cyber' });
      console.log(`Value ${value} -> ${result.processedState} (Comphyon: ${result.comphyonValue.toFixed(3)})`);
      return { value, result, valid: true };
    } catch (error) {
      console.log(`Value ${value} -> Error: ${error.message}`);
      return { value, error: error.message, valid: false };
    }
  });
  
  // Test cross-domain translation
  console.log('\nTesting cross-domain translation:');
  
  const domains = ['cyber', 'financial', 'medical'];
  const crossDomainResults = [];
  
  for (const sourceDomain of domains) {
    for (const targetDomain of domains) {
      if (sourceDomain !== targetDomain) {
        try {
          const result = bridge.process(0.6, {
            domain: sourceDomain,
            targetDomain
          });
          
          console.log(`Value 0.6 from ${sourceDomain} to ${targetDomain} -> ${JSON.stringify(result.processedState)}`);
          crossDomainResults.push({
            sourceDomain,
            targetDomain,
            result,
            valid: true
          });
        } catch (error) {
          console.log(`Value 0.6 from ${sourceDomain} to ${targetDomain} -> Error: ${error.message}`);
          crossDomainResults.push({
            sourceDomain,
            targetDomain,
            error: error.message,
            valid: false
          });
        }
      }
    }
  }
  
  // Test with complex state
  console.log('\nTesting with complex state:');
  
  const complexState = {
    cyber: {
      access: 0.7,
      risk: 0.4
    },
    financial: {
      transaction: 0.5,
      volatility: 0.8
    },
    medical: {
      care: 0.2,
      severity: 0.9
    }
  };
  
  try {
    const complexResult = bridge.process(complexState, { domain: 'cyber' });
    console.log('Complex state processed:');
    console.log('Original:', JSON.stringify(complexState, null, 2));
    console.log('Processed:', JSON.stringify(complexResult.processedState, null, 2));
    console.log('Comphyon:', complexResult.comphyonValue.toFixed(3));
  } catch (error) {
    console.log(`Complex state processing error: ${error.message}`);
  }
  
  // Get metrics
  const metrics = {
    bridge: bridge.getMetrics(),
    trinity: trinity.getMetrics(),
    comphyonMeter: comphyonMeter.getMetrics(),
    comphyonGovernor: comphyonGovernor.getMetrics()
  };
  
  console.log('\nBridge Metrics:', JSON.stringify(metrics.bridge, null, 2));
  
  return {
    bridge,
    trinity,
    comphyonMeter,
    comphyonGovernor,
    testValues,
    results,
    crossDomainResults,
    metrics
  };
}

/**
 * Generate HTML report
 */
function generateHtmlReport(results) {
  console.log('\n=== Generating HTML Report ===');
  
  const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Trinity-Comphyon Integration Demo</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .card {
      background-color: #f9f9f9;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
      flex: 1;
      min-width: 300px;
    }
    .integration-info {
      background-color: #f0f8ff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      border-left: 5px solid #0066cc;
    }
    .trinity-info {
      border-left-color: #cc0000;
    }
    .comphyon-info {
      border-left-color: #009900;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .valid {
      color: #009900;
      font-weight: bold;
    }
    .invalid {
      color: #cc0000;
    }
    footer {
      margin-top: 40px;
      text-align: center;
      color: #666;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <h1>Trinity-Comphyon Integration Demo</h1>
  <p>Generated: ${new Date().toLocaleString()}</p>
  
  <div class="integration-info">
    <h2>The Unified Architecture</h2>
    <p>This demo demonstrates the integration between the Comphyological Trinity and the Comphyon Meter-Governor system, showing how the fundamental laws of system behavior connect with the measurement and control mechanisms.</p>
  </div>
  
  <div class="container">
    <div class="card trinity-info">
      <h3>Comphyological Trinity</h3>
      <p>The three laws of Comphyological Governance:</p>
      <ol>
        <li><strong>First Law (Boundary Condition):</strong> "A system shall neither externalize non-resonant states nor propagate unmeasured energy transitions."</li>
        <li><strong>Second Law (Internal Coherence):</strong> "A system shall sustain resonance through self-similar, energy-minimizing transitions."</li>
        <li><strong>Third Law (Cross-Domain Harmony):</strong> "Systems shall interact through translational resonance, preserving integrity across domains."</li>
      </ol>
    </div>
    
    <div class="card comphyon-info">
      <h3>Comphyon Meter-Governor</h3>
      <p>The measurement and control mechanisms:</p>
      <ul>
        <li><strong>Comphyon Meter:</strong> Measures emergent intelligence (Cph) using the formula: Cph = ((dE_CSDE × dE_CSFE) × log(E_CSME))/166000</li>
        <li><strong>Comphyon Governor:</strong> Enforces limits on system behavior based on Comphyon measurements using the Universal Unified Field Theory equation: (A ⊗ B ⊕ C) × π10³</li>
      </ul>
    </div>
  </div>
  
  <h2>Test Results</h2>
  
  <div class="card">
    <h3>Value Processing Results</h3>
    <table>
      <tr>
        <th>Original Value</th>
        <th>Processed Value</th>
        <th>Comphyon Value</th>
        <th>Status</th>
      </tr>
      ${results.results.map(result => `
      <tr>
        <td>${result.value}</td>
        <td>${result.valid ? result.result.processedState : 'Error'}</td>
        <td>${result.valid ? result.result.comphyonValue.toFixed(3) : 'N/A'}</td>
        <td class="${result.valid ? 'valid' : 'invalid'}">${result.valid ? 'Valid' : 'Invalid'}</td>
      </tr>
      `).join('')}
    </table>
  </div>
  
  <h2>Cross-Domain Translation</h2>
  
  <div class="card">
    <h3>Cross-Domain Results</h3>
    <table>
      <tr>
        <th>Source Domain</th>
        <th>Target Domain</th>
        <th>Status</th>
      </tr>
      ${results.crossDomainResults.map(result => `
      <tr>
        <td>${result.sourceDomain}</td>
        <td>${result.targetDomain}</td>
        <td class="${result.valid ? 'valid' : 'invalid'}">${result.valid ? 'Valid' : 'Invalid'}</td>
      </tr>
      `).join('')}
    </table>
  </div>
  
  <h2>Metrics</h2>
  
  <div class="container">
    <div class="card">
      <h3>Bridge Metrics</h3>
      <ul>
        <li>Bridge Operations: ${results.metrics.bridge.bridgeOperations}</li>
        <li>Trinity Enforcements: ${results.metrics.bridge.trinityEnforcements}</li>
        <li>Comphyon Measurements: ${results.metrics.bridge.comphyonMeasurements}</li>
        <li>Governance Decisions: ${results.metrics.bridge.governanceDecisions}</li>
        <li>Cross-Domain Translations: ${results.metrics.bridge.crossDomainTranslations}</li>
        <li>Harmonization Events: ${results.metrics.bridge.harmonizationEvents}</li>
      </ul>
    </div>
    
    <div class="card">
      <h3>Comphyon Meter Metrics</h3>
      <ul>
        <li>Measurements: ${results.metrics.comphyonMeter.measurements}</li>
        <li>Average Comphyon: ${results.metrics.comphyonMeter.averageComphyon.toFixed(3)}</li>
        <li>Max Comphyon: ${results.metrics.comphyonMeter.maxComphyon.toFixed(3)}</li>
        <li>Warnings: ${results.metrics.comphyonMeter.warnings}</li>
        <li>Critical Alerts: ${results.metrics.comphyonMeter.criticalAlerts}</li>
      </ul>
    </div>
    
    <div class="card">
      <h3>Comphyon Governor Metrics</h3>
      <ul>
        <li>Governance Operations: ${results.metrics.comphyonGovernor.governanceOperations}</li>
        <li>Mode Changes: ${results.metrics.comphyonGovernor.modeChanges}</li>
        <li>Standard Operations: ${results.metrics.comphyonGovernor.standardOperations}</li>
        <li>Constrained Operations: ${results.metrics.comphyonGovernor.constrainedOperations}</li>
        <li>Minimal Operations: ${results.metrics.comphyonGovernor.minimalOperations}</li>
        <li>Adjustments: ${results.metrics.comphyonGovernor.adjustments}</li>
      </ul>
    </div>
  </div>
  
  <footer>
    <p>NovaFuse Trinity-Comphyon Integration Demo - Copyright © ${new Date().getFullYear()}</p>
    <p><em>"The universe counts in 3s. Now, so do we."</em></p>
  </footer>
</body>
</html>`;
  
  // Save HTML report
  const reportPath = path.join(RESULTS_DIR, 'trinity_comphyon_integration_demo_report.html');
  fs.writeFileSync(reportPath, htmlContent);
  
  console.log(`HTML report saved to ${reportPath}`);
  
  return {
    htmlContent,
    reportPath
  };
}

/**
 * Main function
 */
function main() {
  console.log('=== Trinity-Comphyon Integration Demo ===');
  
  // Run test
  const results = testTrinityComphyonBridge();
  
  // Generate HTML report
  const reportResults = generateHtmlReport(results);
  
  // Save results to JSON file
  fs.writeFileSync(
    path.join(RESULTS_DIR, 'trinity_comphyon_integration_demo_results.json'),
    JSON.stringify(results, null, 2)
  );
  
  console.log(`\nResults saved to ${path.join(RESULTS_DIR, 'trinity_comphyon_integration_demo_results.json')}`);
  console.log(`HTML report saved to ${reportResults.reportPath}`);
  console.log('\nOpen the HTML report to view the results in a browser.');
}

// Run main function
main();
