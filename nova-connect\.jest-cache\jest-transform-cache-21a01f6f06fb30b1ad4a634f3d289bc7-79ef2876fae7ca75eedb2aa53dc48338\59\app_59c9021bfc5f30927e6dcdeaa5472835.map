{"version": 3, "names": ["express", "require", "cors", "morgan", "helmet", "<PERSON><PERSON><PERSON><PERSON>", "databaseManager", "logger", "initializeDatabase", "UserAuthenticationService", "connectorRoutes", "testingRoutes", "monitoringRoutes", "credentialRoutes", "graphqlRoutes", "graphqlSubscriptionRoutes", "authRoutes", "oauth2Routes", "twoFactorRoutes", "authTwoFactorRoutes", "authAuditRoutes", "apiKeyRoutes", "rateLimitRoutes", "bruteForceRoutes", "ipRestrictionRoutes", "analyticsRoutes", "environmentRoutes", "teamRoutes", "rolePermissionRoutes", "auditRoutes", "enhancedAuditRoutes", "reportRoutes", "enhancedReportRoutes", "enhancedAnalyticsRoutes", "changeRequestRoutes", "policyRoutes", "resourceLockRoutes", "identityProviderRoutes", "ssoAuthRoutes", "themeRoutes", "brandingRoutes", "whiteLabelRoutes", "exportImportRoutes", "workflowRoutes", "featureFlagRoutes", "packageRoutes", "billingRoutes", "zapierRoutes", "aiAssistRoutes", "governanceRoutes", "securityRoutes", "optionalAuth", "trackApiUsage", "trackApiErrors", "globalLimiter", "authLimiter", "apiLimiter", "createIpRestrictionMiddleware", "ipRestrictionMiddleware", "AuditService", "auditService", "auditMiddleware", "createAuditMiddleware", "enhancedErrorHandler", "notFoundHandler", "enhancedNotFoundHandler", "correlationIdMiddleware", "timeoutMiddleware", "userAuthService", "app", "use", "json", "u<PERSON><PERSON><PERSON>", "extended", "get", "_req", "res", "status", "PORT", "process", "env", "startServer", "info", "connect", "initialize", "connection", "listen", "error", "message", "exit", "module", "exports"], "sources": ["app.js"], "sourcesContent": ["/**\n * NovaConnect API Server\n *\n * This is the main entry point for the NovaConnect API server.\n */\n\nconst express = require('express');\nconst cors = require('cors');\nconst morgan = require('morgan');\nconst helmet = require('helmet');\nconst errorHandler = require('./middleware/errorHandler');\nconst databaseManager = require('./config/database');\nconst logger = require('./config/logger');\nconst initializeDatabase = require('./config/init-db');\nconst UserAuthenticationService = require('../auth/user-authentication-service');\n\n// Import routes\nconst connectorRoutes = require('./routes/connectorRoutes');\nconst testingRoutes = require('./routes/testingRoutes');\nconst monitoringRoutes = require('./routes/monitoringRoutes');\nconst credentialRoutes = require('./routes/credentialRoutes');\nconst graphqlRoutes = require('./routes/graphqlRoutes');\nconst graphqlSubscriptionRoutes = require('./routes/graphqlSubscriptionRoutes');\nconst authRoutes = require('./routes/authRoutes');\nconst oauth2Routes = require('./routes/oauth2Routes');\nconst twoFactorRoutes = require('./routes/twoFactorRoutes');\nconst authTwoFactorRoutes = require('./routes/authTwoFactorRoutes');\nconst authAuditRoutes = require('./routes/authAuditRoutes');\nconst apiKeyRoutes = require('./routes/apiKeyRoutes');\nconst rateLimitRoutes = require('./routes/rateLimitRoutes');\nconst bruteForceRoutes = require('./routes/bruteForceRoutes');\nconst ipRestrictionRoutes = require('./routes/ipRestrictionRoutes');\nconst analyticsRoutes = require('./routes/analyticsRoutes');\nconst environmentRoutes = require('./routes/environmentRoutes');\nconst teamRoutes = require('./routes/teamRoutes');\nconst rolePermissionRoutes = require('./routes/rolePermissionRoutes');\nconst auditRoutes = require('./routes/auditRoutes');\nconst enhancedAuditRoutes = require('./routes/enhancedAuditRoutes');\nconst reportRoutes = require('./routes/reportRoutes');\nconst enhancedReportRoutes = require('./routes/enhancedReportRoutes');\nconst enhancedAnalyticsRoutes = require('./routes/enhancedAnalyticsRoutes');\nconst changeRequestRoutes = require('./routes/changeRequestRoutes');\nconst policyRoutes = require('./routes/policyRoutes');\nconst resourceLockRoutes = require('./routes/resourceLockRoutes');\nconst identityProviderRoutes = require('./routes/identityProviderRoutes');\nconst ssoAuthRoutes = require('./routes/ssoAuthRoutes');\nconst themeRoutes = require('./routes/themeRoutes');\nconst brandingRoutes = require('./routes/brandingRoutes');\nconst whiteLabelRoutes = require('./routes/whiteLabelRoutes');\n\nconst exportImportRoutes = require('./routes/exportImportRoutes');\nconst workflowRoutes = require('./routes/workflowRoutes');\nconst featureFlagRoutes = require('./routes/featureFlagRoutes');\nconst packageRoutes = require('./routes/packageRoutes');\nconst billingRoutes = require('./routes/billingRoutes');\nconst zapierRoutes = require('./routes/zapierRoutes');\nconst aiAssistRoutes = require('./routes/aiAssistRoutes');\nconst governanceRoutes = require('./routes/governanceRoutes');\nconst securityRoutes = require('./routes/securityRoutes');\n\n// Import middleware\nconst { optionalAuth } = require('./middleware/authMiddleware');\nconst { trackApiUsage, trackApiErrors } = require('./middleware/analyticsMiddleware');\nconst { globalLimiter, authLimiter, apiLimiter } = require('./middleware/rateLimitMiddleware');\nconst createIpRestrictionMiddleware = require('./middleware/ipRestrictionMiddleware');\nconst ipRestrictionMiddleware = createIpRestrictionMiddleware();\nconst AuditService = require('./services/AuditService');\nconst auditService = new AuditService();\nconst auditMiddleware = auditService.createAuditMiddleware();\n\n// Import enhanced error handling middleware\nconst {\n  errorHandler: enhancedErrorHandler,\n  notFoundHandler: enhancedNotFoundHandler,\n  correlationIdMiddleware,\n  timeoutMiddleware\n} = require('./middleware/enhancedErrorHandlingMiddleware');\n\n// Initialize authentication service\nconst userAuthService = new UserAuthenticationService();\n\n// Create Express app\nconst app = express();\n\n// Middleware\napp.use(helmet()); // Security headers\napp.use(cors()); // Enable CORS\napp.use(morgan('dev')); // Logging\napp.use(express.json()); // Parse JSON bodies\napp.use(express.urlencoded({ extended: true })); // Parse URL-encoded bodies\n\n// Apply global rate limiter to all requests\napp.use(globalLimiter);\n\n// Apply IP restriction middleware to all requests\napp.use(ipRestrictionMiddleware);\n\n// Apply analytics middleware to track API usage\napp.use(trackApiUsage);\n\n// Apply audit middleware to log user actions\napp.use(auditMiddleware);\n\n// Apply optional authentication to all routes\napp.use(optionalAuth);\n\n// API routes\n// Apply stricter rate limiting to authentication routes\napp.use('/api/auth', authLimiter, authRoutes);\napp.use('/api/auth/2fa', authLimiter, authTwoFactorRoutes);\napp.use('/api/auth/audit', apiLimiter, authAuditRoutes);\napp.use('/api/oauth2', authLimiter, oauth2Routes);\n\n// Apply standard API rate limiting to other routes\napp.use('/api/2fa', apiLimiter, twoFactorRoutes);\napp.use('/api/api-keys', apiLimiter, apiKeyRoutes);\napp.use('/api/analytics', apiLimiter, analyticsRoutes);\napp.use('/api/enhanced-analytics', apiLimiter, enhancedAnalyticsRoutes);\napp.use('/api/environments', apiLimiter, environmentRoutes);\napp.use('/api/teams', apiLimiter, teamRoutes);\napp.use('/api/roles', apiLimiter, rolePermissionRoutes);\napp.use('/api/audit', apiLimiter, auditRoutes);\napp.use('/api/enhanced-audit', apiLimiter, enhancedAuditRoutes);\napp.use('/api/change-requests', apiLimiter, changeRequestRoutes);\napp.use('/api/policies', apiLimiter, policyRoutes);\napp.use('/api/resource-locks', apiLimiter, resourceLockRoutes);\napp.use('/api/identity-providers', apiLimiter, identityProviderRoutes);\napp.use('/api/sso', apiLimiter, ssoAuthRoutes);\napp.use('/api/rate-limits', apiLimiter, rateLimitRoutes);\napp.use('/api/brute-force', apiLimiter, bruteForceRoutes);\napp.use('/api/ip-restrictions', apiLimiter, ipRestrictionRoutes);\napp.use('/api/themes', apiLimiter, themeRoutes);\napp.use('/api/branding', apiLimiter, brandingRoutes);\napp.use('/api/white-label', apiLimiter, whiteLabelRoutes);\napp.use('/api/reports', apiLimiter, reportRoutes);\napp.use('/api/enhanced-reports', apiLimiter, enhancedReportRoutes);\napp.use('/api/export-import', apiLimiter, exportImportRoutes);\napp.use('/api/workflows', apiLimiter, workflowRoutes);\napp.use('/api/subscription', apiLimiter, featureFlagRoutes);\napp.use('/api/packages', apiLimiter, packageRoutes);\napp.use('/api/billing', apiLimiter, billingRoutes);\napp.use('/api/zapier', apiLimiter, zapierRoutes);\napp.use('/api/ai-assist', apiLimiter, aiAssistRoutes);\napp.use('/api/governance', apiLimiter, governanceRoutes);\napp.use('/api/security', apiLimiter, securityRoutes);\napp.use('/api/connectors', apiLimiter, connectorRoutes);\napp.use('/api/testing', apiLimiter, testingRoutes);\napp.use('/api/monitoring', apiLimiter, monitoringRoutes);\napp.use('/api/credentials', apiLimiter, credentialRoutes);\napp.use('/api/graphql', apiLimiter, graphqlRoutes);\napp.use('/api/graphql/subscriptions', apiLimiter, graphqlSubscriptionRoutes);\n\n// Health check endpoint\napp.get('/health', (_req, res) => {\n  res.status(200).json({ status: 'ok' });\n});\n\n// Apply correlation ID middleware\napp.use(correlationIdMiddleware);\n\n// Apply timeout middleware (30 seconds)\napp.use(timeoutMiddleware(30000));\n\n// Apply error tracking middleware\napp.use(trackApiErrors);\n\n// Apply 404 handler for undefined routes\napp.use(enhancedNotFoundHandler);\n\n// Apply enhanced error handling\napp.use(enhancedErrorHandler);\n\n// Start server\nconst PORT = process.env.PORT || 3001;\n\n// Connect to database and initialize server\nasync function startServer() {\n  try {\n    logger.info('Starting NovaConnect API server...');\n\n    // Connect to database\n    logger.info('Connecting to database...');\n    await databaseManager.connect();\n\n    // Initialize database with default data\n    await initializeDatabase();\n\n    // Initialize authentication service\n    await userAuthService.initialize(databaseManager.connection);\n\n    // Start server\n    app.listen(PORT, () => {\n      logger.info(`NovaConnect API server running on port ${PORT}`);\n      logger.info('Available routes:');\n      logger.info('- /api/auth/login');\n      logger.info('- /api/auth/register');\n      logger.info('- /api/connectors');\n      logger.info('- /api/testing');\n      logger.info('- /api/graphql/execute');\n      logger.info('- /api/enhanced-audit/logs');\n      logger.info('- /api/enhanced-audit/logs/export');\n      logger.info('- /api/enhanced-audit/logs/stats');\n      logger.info('- /api/enhanced-reports/compliance');\n      logger.info('- /api/enhanced-reports/performance');\n      logger.info('- /api/enhanced-reports/security');\n      logger.info('- /api/enhanced-reports/custom');\n      logger.info('- /api/enhanced-analytics/user');\n      logger.info('- /api/enhanced-analytics/connector');\n      logger.info('- /api/enhanced-analytics/compliance');\n      logger.info('- /api/enhanced-analytics/predictive');\n    });\n  } catch (error) {\n    logger.error('Failed to start server:', { error: error.message });\n    process.exit(1);\n  }\n}\n\n// Start the server\nstartServer();\n\nmodule.exports = app;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,OAAO,GAAGC,OAAO,CAAC,SAAS,CAAC;AAClC,MAAMC,IAAI,GAAGD,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAME,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;AAChC,MAAMG,MAAM,GAAGH,OAAO,CAAC,QAAQ,CAAC;AAChC,MAAMI,YAAY,GAAGJ,OAAO,CAAC,2BAA2B,CAAC;AACzD,MAAMK,eAAe,GAAGL,OAAO,CAAC,mBAAmB,CAAC;AACpD,MAAMM,MAAM,GAAGN,OAAO,CAAC,iBAAiB,CAAC;AACzC,MAAMO,kBAAkB,GAAGP,OAAO,CAAC,kBAAkB,CAAC;AACtD,MAAMQ,yBAAyB,GAAGR,OAAO,CAAC,qCAAqC,CAAC;;AAEhF;AACA,MAAMS,eAAe,GAAGT,OAAO,CAAC,0BAA0B,CAAC;AAC3D,MAAMU,aAAa,GAAGV,OAAO,CAAC,wBAAwB,CAAC;AACvD,MAAMW,gBAAgB,GAAGX,OAAO,CAAC,2BAA2B,CAAC;AAC7D,MAAMY,gBAAgB,GAAGZ,OAAO,CAAC,2BAA2B,CAAC;AAC7D,MAAMa,aAAa,GAAGb,OAAO,CAAC,wBAAwB,CAAC;AACvD,MAAMc,yBAAyB,GAAGd,OAAO,CAAC,oCAAoC,CAAC;AAC/E,MAAMe,UAAU,GAAGf,OAAO,CAAC,qBAAqB,CAAC;AACjD,MAAMgB,YAAY,GAAGhB,OAAO,CAAC,uBAAuB,CAAC;AACrD,MAAMiB,eAAe,GAAGjB,OAAO,CAAC,0BAA0B,CAAC;AAC3D,MAAMkB,mBAAmB,GAAGlB,OAAO,CAAC,8BAA8B,CAAC;AACnE,MAAMmB,eAAe,GAAGnB,OAAO,CAAC,0BAA0B,CAAC;AAC3D,MAAMoB,YAAY,GAAGpB,OAAO,CAAC,uBAAuB,CAAC;AACrD,MAAMqB,eAAe,GAAGrB,OAAO,CAAC,0BAA0B,CAAC;AAC3D,MAAMsB,gBAAgB,GAAGtB,OAAO,CAAC,2BAA2B,CAAC;AAC7D,MAAMuB,mBAAmB,GAAGvB,OAAO,CAAC,8BAA8B,CAAC;AACnE,MAAMwB,eAAe,GAAGxB,OAAO,CAAC,0BAA0B,CAAC;AAC3D,MAAMyB,iBAAiB,GAAGzB,OAAO,CAAC,4BAA4B,CAAC;AAC/D,MAAM0B,UAAU,GAAG1B,OAAO,CAAC,qBAAqB,CAAC;AACjD,MAAM2B,oBAAoB,GAAG3B,OAAO,CAAC,+BAA+B,CAAC;AACrE,MAAM4B,WAAW,GAAG5B,OAAO,CAAC,sBAAsB,CAAC;AACnD,MAAM6B,mBAAmB,GAAG7B,OAAO,CAAC,8BAA8B,CAAC;AACnE,MAAM8B,YAAY,GAAG9B,OAAO,CAAC,uBAAuB,CAAC;AACrD,MAAM+B,oBAAoB,GAAG/B,OAAO,CAAC,+BAA+B,CAAC;AACrE,MAAMgC,uBAAuB,GAAGhC,OAAO,CAAC,kCAAkC,CAAC;AAC3E,MAAMiC,mBAAmB,GAAGjC,OAAO,CAAC,8BAA8B,CAAC;AACnE,MAAMkC,YAAY,GAAGlC,OAAO,CAAC,uBAAuB,CAAC;AACrD,MAAMmC,kBAAkB,GAAGnC,OAAO,CAAC,6BAA6B,CAAC;AACjE,MAAMoC,sBAAsB,GAAGpC,OAAO,CAAC,iCAAiC,CAAC;AACzE,MAAMqC,aAAa,GAAGrC,OAAO,CAAC,wBAAwB,CAAC;AACvD,MAAMsC,WAAW,GAAGtC,OAAO,CAAC,sBAAsB,CAAC;AACnD,MAAMuC,cAAc,GAAGvC,OAAO,CAAC,yBAAyB,CAAC;AACzD,MAAMwC,gBAAgB,GAAGxC,OAAO,CAAC,2BAA2B,CAAC;AAE7D,MAAMyC,kBAAkB,GAAGzC,OAAO,CAAC,6BAA6B,CAAC;AACjE,MAAM0C,cAAc,GAAG1C,OAAO,CAAC,yBAAyB,CAAC;AACzD,MAAM2C,iBAAiB,GAAG3C,OAAO,CAAC,4BAA4B,CAAC;AAC/D,MAAM4C,aAAa,GAAG5C,OAAO,CAAC,wBAAwB,CAAC;AACvD,MAAM6C,aAAa,GAAG7C,OAAO,CAAC,wBAAwB,CAAC;AACvD,MAAM8C,YAAY,GAAG9C,OAAO,CAAC,uBAAuB,CAAC;AACrD,MAAM+C,cAAc,GAAG/C,OAAO,CAAC,yBAAyB,CAAC;AACzD,MAAMgD,gBAAgB,GAAGhD,OAAO,CAAC,2BAA2B,CAAC;AAC7D,MAAMiD,cAAc,GAAGjD,OAAO,CAAC,yBAAyB,CAAC;;AAEzD;AACA,MAAM;EAAEkD;AAAa,CAAC,GAAGlD,OAAO,CAAC,6BAA6B,CAAC;AAC/D,MAAM;EAAEmD,aAAa;EAAEC;AAAe,CAAC,GAAGpD,OAAO,CAAC,kCAAkC,CAAC;AACrF,MAAM;EAAEqD,aAAa;EAAEC,WAAW;EAAEC;AAAW,CAAC,GAAGvD,OAAO,CAAC,kCAAkC,CAAC;AAC9F,MAAMwD,6BAA6B,GAAGxD,OAAO,CAAC,sCAAsC,CAAC;AACrF,MAAMyD,uBAAuB,GAAGD,6BAA6B,CAAC,CAAC;AAC/D,MAAME,YAAY,GAAG1D,OAAO,CAAC,yBAAyB,CAAC;AACvD,MAAM2D,YAAY,GAAG,IAAID,YAAY,CAAC,CAAC;AACvC,MAAME,eAAe,GAAGD,YAAY,CAACE,qBAAqB,CAAC,CAAC;;AAE5D;AACA,MAAM;EACJzD,YAAY,EAAE0D,oBAAoB;EAClCC,eAAe,EAAEC,uBAAuB;EACxCC,uBAAuB;EACvBC;AACF,CAAC,GAAGlE,OAAO,CAAC,8CAA8C,CAAC;;AAE3D;AACA,MAAMmE,eAAe,GAAG,IAAI3D,yBAAyB,CAAC,CAAC;;AAEvD;AACA,MAAM4D,GAAG,GAAGrE,OAAO,CAAC,CAAC;;AAErB;AACAqE,GAAG,CAACC,GAAG,CAAClE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACnBiE,GAAG,CAACC,GAAG,CAACpE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACjBmE,GAAG,CAACC,GAAG,CAACnE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACxBkE,GAAG,CAACC,GAAG,CAACtE,OAAO,CAACuE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACzBF,GAAG,CAACC,GAAG,CAACtE,OAAO,CAACwE,UAAU,CAAC;EAAEC,QAAQ,EAAE;AAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjD;AACAJ,GAAG,CAACC,GAAG,CAAChB,aAAa,CAAC;;AAEtB;AACAe,GAAG,CAACC,GAAG,CAACZ,uBAAuB,CAAC;;AAEhC;AACAW,GAAG,CAACC,GAAG,CAAClB,aAAa,CAAC;;AAEtB;AACAiB,GAAG,CAACC,GAAG,CAACT,eAAe,CAAC;;AAExB;AACAQ,GAAG,CAACC,GAAG,CAACnB,YAAY,CAAC;;AAErB;AACA;AACAkB,GAAG,CAACC,GAAG,CAAC,WAAW,EAAEf,WAAW,EAAEvC,UAAU,CAAC;AAC7CqD,GAAG,CAACC,GAAG,CAAC,eAAe,EAAEf,WAAW,EAAEpC,mBAAmB,CAAC;AAC1DkD,GAAG,CAACC,GAAG,CAAC,iBAAiB,EAAEd,UAAU,EAAEpC,eAAe,CAAC;AACvDiD,GAAG,CAACC,GAAG,CAAC,aAAa,EAAEf,WAAW,EAAEtC,YAAY,CAAC;;AAEjD;AACAoD,GAAG,CAACC,GAAG,CAAC,UAAU,EAAEd,UAAU,EAAEtC,eAAe,CAAC;AAChDmD,GAAG,CAACC,GAAG,CAAC,eAAe,EAAEd,UAAU,EAAEnC,YAAY,CAAC;AAClDgD,GAAG,CAACC,GAAG,CAAC,gBAAgB,EAAEd,UAAU,EAAE/B,eAAe,CAAC;AACtD4C,GAAG,CAACC,GAAG,CAAC,yBAAyB,EAAEd,UAAU,EAAEvB,uBAAuB,CAAC;AACvEoC,GAAG,CAACC,GAAG,CAAC,mBAAmB,EAAEd,UAAU,EAAE9B,iBAAiB,CAAC;AAC3D2C,GAAG,CAACC,GAAG,CAAC,YAAY,EAAEd,UAAU,EAAE7B,UAAU,CAAC;AAC7C0C,GAAG,CAACC,GAAG,CAAC,YAAY,EAAEd,UAAU,EAAE5B,oBAAoB,CAAC;AACvDyC,GAAG,CAACC,GAAG,CAAC,YAAY,EAAEd,UAAU,EAAE3B,WAAW,CAAC;AAC9CwC,GAAG,CAACC,GAAG,CAAC,qBAAqB,EAAEd,UAAU,EAAE1B,mBAAmB,CAAC;AAC/DuC,GAAG,CAACC,GAAG,CAAC,sBAAsB,EAAEd,UAAU,EAAEtB,mBAAmB,CAAC;AAChEmC,GAAG,CAACC,GAAG,CAAC,eAAe,EAAEd,UAAU,EAAErB,YAAY,CAAC;AAClDkC,GAAG,CAACC,GAAG,CAAC,qBAAqB,EAAEd,UAAU,EAAEpB,kBAAkB,CAAC;AAC9DiC,GAAG,CAACC,GAAG,CAAC,yBAAyB,EAAEd,UAAU,EAAEnB,sBAAsB,CAAC;AACtEgC,GAAG,CAACC,GAAG,CAAC,UAAU,EAAEd,UAAU,EAAElB,aAAa,CAAC;AAC9C+B,GAAG,CAACC,GAAG,CAAC,kBAAkB,EAAEd,UAAU,EAAElC,eAAe,CAAC;AACxD+C,GAAG,CAACC,GAAG,CAAC,kBAAkB,EAAEd,UAAU,EAAEjC,gBAAgB,CAAC;AACzD8C,GAAG,CAACC,GAAG,CAAC,sBAAsB,EAAEd,UAAU,EAAEhC,mBAAmB,CAAC;AAChE6C,GAAG,CAACC,GAAG,CAAC,aAAa,EAAEd,UAAU,EAAEjB,WAAW,CAAC;AAC/C8B,GAAG,CAACC,GAAG,CAAC,eAAe,EAAEd,UAAU,EAAEhB,cAAc,CAAC;AACpD6B,GAAG,CAACC,GAAG,CAAC,kBAAkB,EAAEd,UAAU,EAAEf,gBAAgB,CAAC;AACzD4B,GAAG,CAACC,GAAG,CAAC,cAAc,EAAEd,UAAU,EAAEzB,YAAY,CAAC;AACjDsC,GAAG,CAACC,GAAG,CAAC,uBAAuB,EAAEd,UAAU,EAAExB,oBAAoB,CAAC;AAClEqC,GAAG,CAACC,GAAG,CAAC,oBAAoB,EAAEd,UAAU,EAAEd,kBAAkB,CAAC;AAC7D2B,GAAG,CAACC,GAAG,CAAC,gBAAgB,EAAEd,UAAU,EAAEb,cAAc,CAAC;AACrD0B,GAAG,CAACC,GAAG,CAAC,mBAAmB,EAAEd,UAAU,EAAEZ,iBAAiB,CAAC;AAC3DyB,GAAG,CAACC,GAAG,CAAC,eAAe,EAAEd,UAAU,EAAEX,aAAa,CAAC;AACnDwB,GAAG,CAACC,GAAG,CAAC,cAAc,EAAEd,UAAU,EAAEV,aAAa,CAAC;AAClDuB,GAAG,CAACC,GAAG,CAAC,aAAa,EAAEd,UAAU,EAAET,YAAY,CAAC;AAChDsB,GAAG,CAACC,GAAG,CAAC,gBAAgB,EAAEd,UAAU,EAAER,cAAc,CAAC;AACrDqB,GAAG,CAACC,GAAG,CAAC,iBAAiB,EAAEd,UAAU,EAAEP,gBAAgB,CAAC;AACxDoB,GAAG,CAACC,GAAG,CAAC,eAAe,EAAEd,UAAU,EAAEN,cAAc,CAAC;AACpDmB,GAAG,CAACC,GAAG,CAAC,iBAAiB,EAAEd,UAAU,EAAE9C,eAAe,CAAC;AACvD2D,GAAG,CAACC,GAAG,CAAC,cAAc,EAAEd,UAAU,EAAE7C,aAAa,CAAC;AAClD0D,GAAG,CAACC,GAAG,CAAC,iBAAiB,EAAEd,UAAU,EAAE5C,gBAAgB,CAAC;AACxDyD,GAAG,CAACC,GAAG,CAAC,kBAAkB,EAAEd,UAAU,EAAE3C,gBAAgB,CAAC;AACzDwD,GAAG,CAACC,GAAG,CAAC,cAAc,EAAEd,UAAU,EAAE1C,aAAa,CAAC;AAClDuD,GAAG,CAACC,GAAG,CAAC,4BAA4B,EAAEd,UAAU,EAAEzC,yBAAyB,CAAC;;AAE5E;AACAsD,GAAG,CAACK,GAAG,CAAC,SAAS,EAAE,CAACC,IAAI,EAAEC,GAAG,KAAK;EAChCA,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACN,IAAI,CAAC;IAAEM,MAAM,EAAE;EAAK,CAAC,CAAC;AACxC,CAAC,CAAC;;AAEF;AACAR,GAAG,CAACC,GAAG,CAACJ,uBAAuB,CAAC;;AAEhC;AACAG,GAAG,CAACC,GAAG,CAACH,iBAAiB,CAAC,KAAK,CAAC,CAAC;;AAEjC;AACAE,GAAG,CAACC,GAAG,CAACjB,cAAc,CAAC;;AAEvB;AACAgB,GAAG,CAACC,GAAG,CAACL,uBAAuB,CAAC;;AAEhC;AACAI,GAAG,CAACC,GAAG,CAACP,oBAAoB,CAAC;;AAE7B;AACA,MAAMe,IAAI,GAAGC,OAAO,CAACC,GAAG,CAACF,IAAI,IAAI,IAAI;;AAErC;AACA,eAAeG,WAAWA,CAAA,EAAG;EAC3B,IAAI;IACF1E,MAAM,CAAC2E,IAAI,CAAC,oCAAoC,CAAC;;IAEjD;IACA3E,MAAM,CAAC2E,IAAI,CAAC,2BAA2B,CAAC;IACxC,MAAM5E,eAAe,CAAC6E,OAAO,CAAC,CAAC;;IAE/B;IACA,MAAM3E,kBAAkB,CAAC,CAAC;;IAE1B;IACA,MAAM4D,eAAe,CAACgB,UAAU,CAAC9E,eAAe,CAAC+E,UAAU,CAAC;;IAE5D;IACAhB,GAAG,CAACiB,MAAM,CAACR,IAAI,EAAE,MAAM;MACrBvE,MAAM,CAAC2E,IAAI,CAAC,0CAA0CJ,IAAI,EAAE,CAAC;MAC7DvE,MAAM,CAAC2E,IAAI,CAAC,mBAAmB,CAAC;MAChC3E,MAAM,CAAC2E,IAAI,CAAC,mBAAmB,CAAC;MAChC3E,MAAM,CAAC2E,IAAI,CAAC,sBAAsB,CAAC;MACnC3E,MAAM,CAAC2E,IAAI,CAAC,mBAAmB,CAAC;MAChC3E,MAAM,CAAC2E,IAAI,CAAC,gBAAgB,CAAC;MAC7B3E,MAAM,CAAC2E,IAAI,CAAC,wBAAwB,CAAC;MACrC3E,MAAM,CAAC2E,IAAI,CAAC,4BAA4B,CAAC;MACzC3E,MAAM,CAAC2E,IAAI,CAAC,mCAAmC,CAAC;MAChD3E,MAAM,CAAC2E,IAAI,CAAC,kCAAkC,CAAC;MAC/C3E,MAAM,CAAC2E,IAAI,CAAC,oCAAoC,CAAC;MACjD3E,MAAM,CAAC2E,IAAI,CAAC,qCAAqC,CAAC;MAClD3E,MAAM,CAAC2E,IAAI,CAAC,kCAAkC,CAAC;MAC/C3E,MAAM,CAAC2E,IAAI,CAAC,gCAAgC,CAAC;MAC7C3E,MAAM,CAAC2E,IAAI,CAAC,gCAAgC,CAAC;MAC7C3E,MAAM,CAAC2E,IAAI,CAAC,qCAAqC,CAAC;MAClD3E,MAAM,CAAC2E,IAAI,CAAC,sCAAsC,CAAC;MACnD3E,MAAM,CAAC2E,IAAI,CAAC,sCAAsC,CAAC;IACrD,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdhF,MAAM,CAACgF,KAAK,CAAC,yBAAyB,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC,CAAC;IACjET,OAAO,CAACU,IAAI,CAAC,CAAC,CAAC;EACjB;AACF;;AAEA;AACAR,WAAW,CAAC,CAAC;AAEbS,MAAM,CAACC,OAAO,GAAGtB,GAAG", "ignoreList": []}