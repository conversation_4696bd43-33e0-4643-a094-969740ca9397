import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText,
  InventorLabel
} from '../../components/DiagramComponents';

const PartnerEmpowermentFlywheel = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel fontSize="18px">PARTNER EMPOWERMENT FLYWHEEL</ContainerLabel>
      </ContainerBox>

      {/* Center Formula */}
      <ComponentBox left="325px" top="270px" width="150px" height="60px">
        <ComponentNumber>301</ComponentNumber>
        <ComponentLabel fontSize="16px">Growth Formula</ComponentLabel>
        <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#555555' }}>(0.82 × 2)^n</span>
      </ComponentBox>

      {/* Flywheel Circle */}
      <svg width="800" height="600" style={{ position: 'absolute', top: 0, left: 0, zIndex: 0 }}>
        <circle
          cx="400"
          cy="300"
          r="180"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />

        {/* Arrows around the circle */}
        <path
          d="M 580,300 A 180,180 0 0,1 490,460"
          fill="none"
          stroke="#333"
          strokeWidth="2"
          markerEnd="url(#arrowhead)"
        />

        <path
          d="M 490,460 A 180,180 0 0,1 310,460"
          fill="none"
          stroke="#333"
          strokeWidth="2"
          markerEnd="url(#arrowhead)"
        />

        <path
          d="M 310,460 A 180,180 0 0,1 220,300"
          fill="none"
          stroke="#333"
          strokeWidth="2"
          markerEnd="url(#arrowhead)"
        />

        <path
          d="M 220,300 A 180,180 0 0,1 310,140"
          fill="none"
          stroke="#333"
          strokeWidth="2"
          markerEnd="url(#arrowhead)"
        />

        <path
          d="M 310,140 A 180,180 0 0,1 490,140"
          fill="none"
          stroke="#333"
          strokeWidth="2"
          markerEnd="url(#arrowhead)"
        />

        <path
          d="M 490,140 A 180,180 0 0,1 580,300"
          fill="none"
          stroke="#333"
          strokeWidth="2"
          markerEnd="url(#arrowhead)"
        />

        {/* Arrow definitions */}
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="0"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
          </marker>
        </defs>
      </svg>

      {/* Flywheel Components */}
      <ComponentBox left="550px" top="270px" width="120px" height="60px">
        <ComponentNumber>302</ComponentNumber>
        <ComponentLabel fontSize="14px">Partner Revenue</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#555555' }}>82% Share</span>
      </ComponentBox>

      <ComponentBox left="470px" top="430px" width="120px" height="60px">
        <ComponentNumber>303</ComponentNumber>
        <ComponentLabel fontSize="14px">Partner Growth</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Ecosystem Expansion</span>
      </ComponentBox>

      <ComponentBox left="210px" top="430px" width="120px" height="60px">
        <ComponentNumber>304</ComponentNumber>
        <ComponentLabel fontSize="14px">Customer Value</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#555555' }}>82% Cost Reduction</span>
      </ComponentBox>

      <ComponentBox left="130px" top="270px" width="120px" height="60px">
        <ComponentNumber>305</ComponentNumber>
        <ComponentLabel fontSize="14px">Market Adoption</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Accelerated Uptake</span>
      </ComponentBox>

      <ComponentBox left="210px" top="110px" width="120px" height="60px">
        <ComponentNumber>306</ComponentNumber>
        <ComponentLabel fontSize="14px">Platform Growth</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Compounding Value</span>
      </ComponentBox>

      <ComponentBox left="470px" top="110px" width="120px" height="60px">
        <ComponentNumber>307</ComponentNumber>
        <ComponentLabel fontSize="14px">Innovation</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Ecosystem Creativity</span>
      </ComponentBox>

      {/* Traditional Model Comparison */}
      <ContainerBox width="700px" height="100px" left="50px" top="500px">
        <ContainerLabel fontSize="16px">TRADITIONAL VS. PARTNER EMPOWERMENT MODEL</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="100px" top="530px" width="250px" height="50px" style={{ border: '1px dashed #333' }}>
        <ComponentNumber>308</ComponentNumber>
        <ComponentLabel fontSize="14px">Traditional Model</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Zero-Sum: Partners compete for limited value</span>
      </ComponentBox>

      <ComponentBox left="450px" top="530px" width="250px" height="50px">
        <ComponentNumber>309</ComponentNumber>
        <ComponentLabel fontSize="14px">Partner Empowerment Model</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#555555' }}>Exponential: (0.82 × 2)^n value creation</span>
      </ComponentBox>

      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Flywheel Components</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" style={{ border: '1px dashed #333' }} />
          <LegendText>Traditional Model (Comparison)</LegendText>
        </LegendItem>
      </DiagramLegend>

      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default PartnerEmpowermentFlywheel;
