# Visualization Examples

This directory contains examples of visualizing ComphyonΨᶜ metrics using various visualization tools.

## Examples

### 1. Dashboard Example

[dashboard_example.py](dashboard_example.py) demonstrates how to create an interactive dashboard for visualizing ComphyonΨᶜ metrics.

Key concepts demonstrated:
- Creating an interactive web-based dashboard
- Real-time visualization of ComphyonΨᶜ metrics
- Threshold visualization
- Historical trend analysis
- Control action visualization

### 2. 3D Visualization

[3d_visualization.py](3d_visualization.py) demonstrates how to create 3D visualizations of ComphyonΨᶜ metrics.

Key concepts demonstrated:
- 3D visualization of the Nested Trinity
- Visualizing the relationship between different metrics
- Phase space visualization
- Emergent pattern detection

## Getting Started

To run these examples, you'll need to have the ComphyonΨᶜ Meter and ComphyonΨᶜ Governor packages installed:

```bash
# Clone the repositories
git clone https://github.com/Dartan1983/comphyon-meter.git
git clone https://github.com/Dartan1983/comphyon-governor.git

# Install the packages
cd comphyon-meter
pip install -e .
cd ../comphyon-governor
pip install -e .
```

Additionally, these visualization examples require the following packages:

```bash
pip install dash plotly pandas matplotlib
```

Then you can run the examples:

```bash
cd comphyon-framework/examples/visualization
python dashboard_example.py
```
