const { Connector, User, Review, Installation } = require('../models');

/**
 * Get all connectors with optional filtering
 */
const getAllConnectors = async (req, res, next) => {
  try {
    const { 
      category, 
      framework, 
      search, 
      sort = 'popular',
      page = 1,
      limit = 10
    } = req.query;

    // Build query
    const query = {};
    
    if (category && category !== 'all') {
      query.category = category;
    }
    
    if (framework && framework !== 'all') {
      query.framework = framework;
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Determine sort order
    let sortOption = {};
    switch (sort) {
      case 'newest':
        sortOption = { createdAt: -1 };
        break;
      case 'price-low':
        sortOption = { priceNumeric: 1 };
        break;
      case 'price-high':
        sortOption = { priceNumeric: -1 };
        break;
      case 'rating':
        sortOption = { averageRating: -1 };
        break;
      case 'popular':
      default:
        sortOption = { popularity: -1 };
        break;
    }

    // Execute query with pagination and sorting
    const connectors = await Connector.find(query)
      .sort(sortOption)
      .skip(skip)
      .limit(parseInt(limit));
    
    // Get total count for pagination
    const total = await Connector.countDocuments(query);
    
    res.status(200).json({
      connectors,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get a single connector by ID
 */
const getConnectorById = async (req, res, next) => {
  try {
    const connector = await Connector.findById(req.params.id);
    
    if (!connector) {
      return res.status(404).json({ error: true, message: 'Connector not found' });
    }
    
    res.status(200).json(connector);
  } catch (error) {
    next(error);
  }
};

/**
 * Create a new connector
 */
const createConnector = async (req, res, next) => {
  try {
    const {
      name,
      description,
      longDescription,
      category,
      framework,
      price,
      features,
      integrations
    } = req.body;

    // Extract numeric price for sorting
    const priceNumeric = parseFloat(price.replace(/[^0-9.]/g, ''));

    const connector = new Connector({
      name,
      description,
      longDescription,
      category,
      framework,
      price,
      priceNumeric,
      features: features || [],
      integrations: integrations || [],
      vendor: req.user.company || req.user.firstName + ' ' + req.user.lastName,
      createdBy: req.user.id,
      averageRating: 0,
      reviewCount: 0,
      popularity: 0
    });

    await connector.save();
    
    res.status(201).json(connector);
  } catch (error) {
    next(error);
  }
};

/**
 * Update a connector
 */
const updateConnector = async (req, res, next) => {
  try {
    const connector = await Connector.findById(req.params.id);
    
    if (!connector) {
      return res.status(404).json({ error: true, message: 'Connector not found' });
    }
    
    // Check if user is the owner or an admin
    if (connector.createdBy.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ error: true, message: 'You do not have permission to update this connector' });
    }
    
    const {
      name,
      description,
      longDescription,
      category,
      framework,
      price,
      features,
      integrations
    } = req.body;

    // Update fields if provided
    if (name) connector.name = name;
    if (description) connector.description = description;
    if (longDescription) connector.longDescription = longDescription;
    if (category) connector.category = category;
    if (framework) connector.framework = framework;
    if (price) {
      connector.price = price;
      connector.priceNumeric = parseFloat(price.replace(/[^0-9.]/g, ''));
    }
    if (features) connector.features = features;
    if (integrations) connector.integrations = integrations;
    
    connector.updatedAt = Date.now();
    
    await connector.save();
    
    res.status(200).json(connector);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete a connector
 */
const deleteConnector = async (req, res, next) => {
  try {
    const connector = await Connector.findById(req.params.id);
    
    if (!connector) {
      return res.status(404).json({ error: true, message: 'Connector not found' });
    }
    
    // Check if user is the owner or an admin
    if (connector.createdBy.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ error: true, message: 'You do not have permission to delete this connector' });
    }
    
    await connector.remove();
    
    res.status(200).json({ message: 'Connector deleted successfully' });
  } catch (error) {
    next(error);
  }
};

/**
 * Get connectors by category
 */
const getConnectorsByCategory = async (req, res, next) => {
  try {
    const { category } = req.params;
    const { page = 1, limit = 10 } = req.query;
    
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const connectors = await Connector.find({ category })
      .sort({ popularity: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    const total = await Connector.countDocuments({ category });
    
    res.status(200).json({
      connectors,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get connectors by framework
 */
const getConnectorsByFramework = async (req, res, next) => {
  try {
    const { framework } = req.params;
    const { page = 1, limit = 10 } = req.query;
    
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const connectors = await Connector.find({ framework })
      .sort({ popularity: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    const total = await Connector.countDocuments({ framework });
    
    res.status(200).json({
      connectors,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Install a connector for the authenticated user
 */
const installConnector = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    // Check if connector exists
    const connector = await Connector.findById(id);
    if (!connector) {
      return res.status(404).json({ error: true, message: 'Connector not found' });
    }
    
    // Check if already installed
    const existingInstallation = await Installation.findOne({ 
      connector: id,
      user: userId
    });
    
    if (existingInstallation) {
      return res.status(400).json({ error: true, message: 'Connector already installed' });
    }
    
    // Create new installation
    const installation = new Installation({
      connector: id,
      user: userId,
      status: 'active',
      installedAt: Date.now()
    });
    
    await installation.save();
    
    // Increment connector popularity
    connector.popularity += 1;
    await connector.save();
    
    res.status(200).json({ 
      message: 'Connector installed successfully',
      installation
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Uninstall a connector for the authenticated user
 */
const uninstallConnector = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    // Find the installation
    const installation = await Installation.findOne({ 
      connector: id,
      user: userId
    });
    
    if (!installation) {
      return res.status(404).json({ error: true, message: 'Installation not found' });
    }
    
    // Remove the installation
    await installation.remove();
    
    // Decrement connector popularity
    const connector = await Connector.findById(id);
    if (connector) {
      connector.popularity = Math.max(0, connector.popularity - 1);
      await connector.save();
    }
    
    res.status(200).json({ message: 'Connector uninstalled successfully' });
  } catch (error) {
    next(error);
  }
};

/**
 * Get reviews for a connector
 */
const getConnectorReviews = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10 } = req.query;
    
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const reviews = await Review.find({ connector: id })
      .populate('user', 'firstName lastName company')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    const total = await Review.countDocuments({ connector: id });
    
    res.status(200).json({
      reviews,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Add a review for a connector
 */
const addConnectorReview = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { rating, comment } = req.body;
    
    // Check if connector exists
    const connector = await Connector.findById(id);
    if (!connector) {
      return res.status(404).json({ error: true, message: 'Connector not found' });
    }
    
    // Check if user has already reviewed this connector
    const existingReview = await Review.findOne({
      connector: id,
      user: userId
    });
    
    if (existingReview) {
      return res.status(400).json({ error: true, message: 'You have already reviewed this connector' });
    }
    
    // Create new review
    const review = new Review({
      connector: id,
      user: userId,
      rating,
      comment,
      createdAt: Date.now()
    });
    
    await review.save();
    
    // Update connector rating
    const allReviews = await Review.find({ connector: id });
    const totalRating = allReviews.reduce((sum, review) => sum + review.rating, 0);
    connector.averageRating = totalRating / allReviews.length;
    connector.reviewCount = allReviews.length;
    await connector.save();
    
    res.status(201).json(review);
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllConnectors,
  getConnectorById,
  createConnector,
  updateConnector,
  deleteConnector,
  getConnectorsByCategory,
  getConnectorsByFramework,
  installConnector,
  uninstallConnector,
  getConnectorReviews,
  addConnectorReview
};
