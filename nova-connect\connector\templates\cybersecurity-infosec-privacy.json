{"id": "cybersecurity-infosec-privacy", "name": "Cybersecurity/InfoSec/Privacy Connector", "description": "Connector for cybersecurity, information security, and privacy systems", "version": "1.0.0", "category": "cybersecurity", "icon": "cybersecurity-icon.svg", "author": "NovaFuse", "website": "https://novafuse.io", "documentation": "https://docs.novafuse.io/connectors/cybersecurity", "supportEmail": "<EMAIL>", "authentication": {"type": "api_key", "fields": {"apiKey": {"type": "string", "label": "API Key", "required": true, "sensitive": true, "description": "API Key for authentication"}, "apiKeyHeader": {"type": "string", "label": "API Key Header", "required": true, "sensitive": false, "default": "X-API-Key", "description": "HTTP header name for the API Key"}}}, "endpoints": [{"id": "listVulnerabilities", "name": "List Vulnerabilities", "description": "List all vulnerabilities", "method": "GET", "url": "https://api.example.com/vulnerabilities", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "severity": {"type": "string", "label": "Severity", "required": false, "enum": ["critical", "high", "medium", "low", "info"], "description": "Filter by vulnerability severity"}, "status": {"type": "string", "label": "Status", "required": false, "enum": ["open", "closed", "in_progress", "deferred"], "description": "Filter by vulnerability status"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "severity": {"type": "string", "description": "Filter by vulnerability severity", "enum": ["critical", "high", "medium", "low", "info"]}, "status": {"type": "string", "description": "Filter by vulnerability status", "enum": ["open", "closed", "in_progress", "deferred"]}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Vulnerability ID"}, "title": {"type": "string", "description": "Vulnerability title"}, "description": {"type": "string", "description": "Vulnerability description"}, "severity": {"type": "string", "description": "Vulnerability severity", "enum": ["critical", "high", "medium", "low", "info"]}, "status": {"type": "string", "description": "Vulnerability status", "enum": ["open", "closed", "in_progress", "deferred"]}, "discoveredAt": {"type": "string", "format": "date-time", "description": "Vulnerability discovery date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Vulnerability last update date"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}, {"id": "getVulnerability", "name": "Get Vulnerability", "description": "Get a specific vulnerability", "method": "GET", "url": "https://api.example.com/vulnerabilities/{vulnerabilityId}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"vulnerabilityId": {"type": "string", "label": "Vulnerability ID", "required": true, "description": "ID of the vulnerability to retrieve"}}, "inputSchema": {"type": "object", "properties": {"vulnerabilityId": {"type": "string", "description": "ID of the vulnerability to retrieve"}}, "required": ["vulnerabilityId"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Vulnerability ID"}, "title": {"type": "string", "description": "Vulnerability title"}, "description": {"type": "string", "description": "Vulnerability description"}, "severity": {"type": "string", "description": "Vulnerability severity", "enum": ["critical", "high", "medium", "low", "info"]}, "cvss": {"type": "number", "description": "CVSS score"}, "cve": {"type": "string", "description": "CVE identifier"}, "status": {"type": "string", "description": "Vulnerability status", "enum": ["open", "closed", "in_progress", "deferred"]}, "affectedAssets": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Asset ID"}, "name": {"type": "string", "description": "Asset name"}, "type": {"type": "string", "description": "Asset type"}}}}, "remediation": {"type": "string", "description": "Remediation steps"}, "discoveredAt": {"type": "string", "format": "date-time", "description": "Vulnerability discovery date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Vulnerability last update date"}}}}, {"id": "updateVulnerabilityStatus", "name": "Update Vulnerability Status", "description": "Update the status of a vulnerability", "method": "PATCH", "url": "https://api.example.com/vulnerabilities/{vulnerabilityId}/status", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"vulnerabilityId": {"type": "string", "label": "Vulnerability ID", "required": true, "description": "ID of the vulnerability to update"}}, "bodyParameters": {"status": {"type": "string", "label": "Status", "required": true, "enum": ["open", "closed", "in_progress", "deferred"], "description": "New vulnerability status"}, "comment": {"type": "string", "label": "Comment", "required": false, "description": "Comment about the status update"}}, "inputSchema": {"type": "object", "properties": {"vulnerabilityId": {"type": "string", "description": "ID of the vulnerability to update"}, "status": {"type": "string", "description": "New vulnerability status", "enum": ["open", "closed", "in_progress", "deferred"]}, "comment": {"type": "string", "description": "Comment about the status update"}}, "required": ["vulnerabilityId", "status"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Vulnerability ID"}, "status": {"type": "string", "description": "Updated vulnerability status", "enum": ["open", "closed", "in_progress", "deferred"]}, "updatedAt": {"type": "string", "format": "date-time", "description": "Vulnerability last update date"}}}}]}