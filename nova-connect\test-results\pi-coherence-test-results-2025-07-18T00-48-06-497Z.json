{"timestamp": "2025-07-18T00:48:06.498Z", "testConfig": {"iterations": 100, "baseUrl": "http://localhost:3001", "endpoints": ["/health", "/api/connectors", "/api/status"], "concurrency": 5}, "timingConfigs": {"standard": {"FAST": 10, "MEDIUM": 100, "SLOW": 1000, "HEARTBEAT": 30000, "TIMEOUT": 30000, "RETRY_DELAY": 1000, "HEALTH_CHECK": 30000}, "piTiming": {"FAST": 31.42, "MEDIUM": 42.53, "SLOW": 53.64, "HEARTBEAT": 314.2, "TIMEOUT": 3142, "RETRY_DELAY": 42.53, "HEALTH_CHECK": 314.2}}, "results": {"standard": {"requests": [{"success": false, "duration": 353, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 354, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 1014, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 354, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 356, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 19, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 116, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 1007, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 19, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 117, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 26, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 111, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 1010, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 26, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 112, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 19, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 118, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 1055, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 20, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 118, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 64, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 131, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 1009, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 65, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 131, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 18, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 112, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 1003, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 19, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 113, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 29, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 104, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 1014, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 29, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 105, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 13, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 111, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 1029, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 13, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 111, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 19, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 117, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 1036, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 20, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 117, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 41, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 128, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 1016, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 41, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 129, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 18, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 114, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 1008, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 19, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 114, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 21, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 119, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 1007, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 21, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 119, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 18, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 138, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 1008, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 18, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 138, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 19, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 111, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 1018, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 19, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 111, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 20, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 109, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 1009, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 21, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 110, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 37, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 119, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 1023, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 37, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 120, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 20, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 123, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 1007, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 21, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 123, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 17, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 110, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 1018, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 17, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 111, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 20, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 119, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 1011, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 20, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 120, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 35, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 122, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/status", "coherence": 0}, {"success": false, "duration": 1020, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/health", "coherence": 0}, {"success": false, "duration": 34, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}, {"success": false, "duration": 123, "error": "connect ECONNREFUSED ::1:3001", "endpoint": "/api/connectors", "coherence": 0}], "errors": 100, "totalTime": 22674, "coherenceScores": [], "avgResponseTime": 0, "successRate": 0, "coherenceScore": 0}, "piTiming": {"requests": [{"success": false, "duration": 84, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 86, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 87, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 85, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 86, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 63, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 64, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 64, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 63, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 64, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 48, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 49, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 65, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 48, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 49, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 69, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 70, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 71, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 70, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 71, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 50, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 51, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 106, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 51, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 52, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 64, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 66, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 67, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 65, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 67, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 57, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 58, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 59, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 57, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 58, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 67, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 68, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 73, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 68, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 69, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 64, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 72, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 74, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 72, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 73, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 80, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 81, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 82, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 80, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 81, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 56, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 66, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 67, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 65, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 67, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 70, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 71, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 72, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 71, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 72, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 60, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 62, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 65, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 61, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 64, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 133, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 134, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 135, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 133, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 134, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 57, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 59, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 60, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 58, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 59, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 95, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 96, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 98, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 95, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 97, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 113, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 114, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 115, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 113, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 114, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 78, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 79, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 80, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 79, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 80, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 118, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 128, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 129, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 118, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 129, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 76, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 78, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 80, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 77, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}, {"success": false, "duration": 79, "error": "Invalid character in header content [\"X-Test-Config\"]", "endpoint": "unknown", "coherence": 0}], "errors": 100, "totalTime": 4422, "coherenceScores": [], "avgResponseTime": 0, "successRate": 0, "coherenceScore": 0}}, "summary": {"efficiencyGain": null, "coherenceImprovement": 0, "errorReduction": 0}}