#!/usr/bin/env python3
"""
Unified Interface for Ψ Tensor Core

This module provides a unified interface for the Ψ Tensor Core, combining the
PsiTensorCore, DynamicWeightingProtocol, and QuantumConsensusEngine components.
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Union, Optional, Any

from .psi_tensor_core import PsiTensorCore
from .dynamic_weighting import DynamicWeightingProtocol
from .quantum_consensus import QuantumConsensusEngine
from .energy_calculator import EnergyCalculator

class UnifiedPsiTensorCore:
    """
    Unified interface for the Ψ Tensor Core.
    """

    def __init__(self,
                action_space: Optional[List[str]] = None,
                threshold: float = 0.82,
                use_gpu: bool = False,
                use_energy_based_comphyon: bool = True):
        """
        Initialize the Unified Ψ Tensor Core.

        Args:
            action_space: List of possible actions
            threshold: Threshold for action execution (default: 0.82)
            use_gpu: Whether to use GPU acceleration if available
            use_energy_based_comphyon: Whether to use energy-based Comphyon calculation
        """
        self.device = torch.device("cuda" if use_gpu and torch.cuda.is_available() else "cpu")
        self.use_energy_based_comphyon = use_energy_based_comphyon

        # Initialize components
        self.psi_tensor_core = PsiTensorCore(use_gpu=use_gpu)
        self.dynamic_weighting = DynamicWeightingProtocol(device=self.device)
        self.quantum_consensus = QuantumConsensusEngine(
            action_space=action_space,
            threshold=threshold,
            device=self.device
        )

        # Initialize energy calculator for Comphyon calculation
        if use_energy_based_comphyon:
            self.energy_calculator = EnergyCalculator()

        print(f"Unified Ψ Tensor Core initialized on {self.device}")
        if use_energy_based_comphyon:
            print("Using energy-based Comphyon calculation")

    def process(self,
               csde_data: Dict[str, Any],
               csfe_data: Dict[str, Any],
               csme_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process data from all three engines and generate a unified result.

        Args:
            csde_data: Data from CSDE engine
            csfe_data: Data from CSFE engine
            csme_data: Data from CSME engine

        Returns:
            Unified result
        """
        # Create tensors for each engine
        csde_tensor = self.psi_tensor_core.create_csde_tensor(
            governance=csde_data.get("governance", 0.5),
            data=csde_data.get("data", 0.5),
            action=csde_data.get("action", "No action"),
            confidence=csde_data.get("confidence", 0.5)
        )

        csfe_tensor = self.psi_tensor_core.create_csfe_tensor(
            risk=csfe_data.get("risk", 0.5),
            finance=csfe_data.get("finance", 0.5),
            action=csfe_data.get("action", "No action"),
            confidence=csfe_data.get("confidence", 0.5)
        )

        csme_tensor = self.psi_tensor_core.create_csme_tensor(
            bio=csme_data.get("bio", 0.5),
            med_compliance=csme_data.get("med_compliance", 0.5),
            action=csme_data.get("action", "No action"),
            confidence=csme_data.get("confidence", 0.5)
        )

        # Apply dynamic weighting
        weights = self.dynamic_weighting.calculate_weights(
            governance=csde_data.get("governance", 0.5),
            data=csde_data.get("data", 0.5),
            risk=csfe_data.get("risk", 0.5),
            finance=csfe_data.get("finance", 0.5),
            bio=csme_data.get("bio", 0.5),
            med_compliance=csme_data.get("med_compliance", 0.5)
        )

        # Fuse engines
        fused_tensor = self.psi_tensor_core.fuse_engines(csde_tensor, csfe_tensor, csme_tensor)

        # Reach consensus on actions
        consensus_action, consensus_confidence, execute_action = self.quantum_consensus.reach_consensus(
            actions=[
                csde_data.get("action", "No action"),
                csfe_data.get("action", "No action"),
                csme_data.get("action", "No action")
            ],
            confidences=[
                csde_data.get("confidence", 0.5),
                csfe_data.get("confidence", 0.5),
                csme_data.get("confidence", 0.5)
            ]
        )

        # Calculate Comphyon value if energy-based calculation is enabled
        comphyon_data = {}
        if self.use_energy_based_comphyon:
            # Convert PyTorch tensors to NumPy arrays
            csde_np = csde_tensor.cpu().numpy()
            csfe_np = csfe_tensor.cpu().numpy()
            csme_np = csme_tensor.cpu().numpy()

            # Calculate Comphyon value
            comphyon_result = self.energy_calculator.calculate_comphyon(csde_np, csfe_np, csme_np)

            comphyon_data = {
                "comphyon_value": comphyon_result["Cph"],
                "energies": comphyon_result["Energies"],
                "gradients": comphyon_result["Gradients"]
            }

        # Create unified result
        result = {
            "fused_tensor": fused_tensor.cpu().numpy().tolist(),
            "weights": weights,
            "dominant_engine": self.dynamic_weighting.get_dominant_engine(),
            "consensus_action": consensus_action,
            "consensus_confidence": consensus_confidence,
            "execute_action": execute_action,
            "pi_factor": 3141.59,  # π103
            "timestamp": np.datetime64('now').astype(str)
        }

        # Add Comphyon data if available
        if comphyon_data:
            result["comphyon"] = comphyon_data

        return result

    def process_batch(self,
                     csde_batch: List[Dict[str, Any]],
                     csfe_batch: List[Dict[str, Any]],
                     csme_batch: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process a batch of data from all three engines.

        Args:
            csde_batch: Batch of data from CSDE engine
            csfe_batch: Batch of data from CSFE engine
            csme_batch: Batch of data from CSME engine

        Returns:
            List of unified results
        """
        results = []

        # Ensure all batches have the same length
        min_length = min(len(csde_batch), len(csfe_batch), len(csme_batch))

        for i in range(min_length):
            result = self.process(csde_batch[i], csfe_batch[i], csme_batch[i])
            results.append(result)

        return results

    def get_weight_history(self) -> Dict[str, List[float]]:
        """
        Get the weight history.

        Returns:
            Dictionary of weight history for each engine
        """
        return self.dynamic_weighting.get_weight_history()

    def set_consensus_threshold(self, threshold: float):
        """
        Set the threshold for action execution.

        Args:
            threshold: New threshold value
        """
        self.quantum_consensus.set_threshold(threshold)

    def add_action_to_space(self, action: str):
        """
        Add a new action to the action space.

        Args:
            action: Action string to add
        """
        self.quantum_consensus.add_action_to_space(action)
