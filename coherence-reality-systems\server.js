const express = require('express');
const cors = require('cors');
const crypto = require('crypto');
const BN = require('bn.js');
const AetheriumGas = require('./aetherium-gas');

const app = express();
app.use(cors());
app.use(express.json());

// In-memory stores
const crownNodes = new Map();
const coheriumLedger = new Map();
const pendingRewards = new Map();
const aetheriumAccounts = new Map();

// Initialize Aetherium Gas System
const aetheriumGas = new AetheriumGas();

// Constants
const CONSCIOUSNESS_THRESHOLD = 2847;
const MAX_COHERIUM_SUPPLY = *********;
const CROWN_NODE_THRESHOLD = 0.18;
const BLOCK_REWARD = 100;
const GENESIS_ADDRESS = '0000000000000000000000000000000000000000';
const GENESIS_REWARD = 1000000;

// Initialize genesis account
aetheriumAccounts.set('******************************************', {
  balance: new BN('**********000000000000000'), // 1M AE (18 decimals)
  nonce: 0
});

// Coherium Manager
class CoheriumManager {
  constructor() {
    this.totalSupply = GENESIS_REWARD;
    this.blockHeight = 0;
    this.lastRewardTime = Date.now();
  }

  getNodeAddress(nodeId) {
    return crypto.createHash('sha256').update(`node:${nodeId}`).digest('hex');
  }

  initializeNode(nodeId) {
    const address = this.getNodeAddress(nodeId);
    if (!coheriumLedger.has(address)) {
      coheriumLedger.set(address, {
        balance: 0,
        lastClaim: 0,
        totalEarned: 0,
        address
      });
    }
    return address;
  }

  calculateReward(node, blockTime) {
    // Base reward
    let reward = BLOCK_REWARD;
    
    // Apply consciousness multiplier (0.5x to 1.5x based on score)
    const consciousnessFactor = 0.5 + (node.score / (2 * CONSCIOUSNESS_THRESHOLD));
    reward *= consciousnessFactor;
    
    // Apply stability bonus (up to 2x for long-running nodes)
    const stabilityBonus = Math.min(2, 1 + (node.validations / 1000));
    reward *= stabilityBonus;
    
    // Apply time-based bonus (up to 1.5x for recent activity)
    const hoursSinceLastActive = (blockTime - node.lastActive) / (1000 * 60 * 60);
    const timeBonus = Math.max(0.5, 1.5 - (hoursSinceLastActive / 24));
    reward *= timeBonus;
    
    // Ensure we don't exceed max supply
    if (this.totalSupply + reward > MAX_COHERIUM_SUPPLY) {
      reward = Math.max(0, MAX_COHERIUM_SUPPLY - this.totalSupply);
    }
    
    return Math.floor(reward);
  }

  distributeRewards(blockData) {
    const rewards = [];
    const blockTime = blockData.timestamp || Date.now();
    
    // Only distribute rewards for non-genesis blocks
    if (!blockData.isGenesis) {
      const crownNodesList = Array.from(crownNodes.values())
        .filter(n => n.score >= CONSCIOUSNESS_THRESHOLD)
        .sort((a, b) => b.score - a.score);
      
      // Take top 18% as Crown Nodes
      const numCrownNodes = Math.ceil(crownNodesList.length * CROWN_NODE_THRESHOLD);
      const eligibleNodes = crownNodesList.slice(0, numCrownNodes);
      
      // Distribute rewards to eligible nodes
      for (const node of eligibleNodes) {
        const reward = this.calculateReward(node, blockTime);
        if (reward > 0) {
          const address = this.getNodeAddress(node.id);
          pendingRewards.set(address, (pendingRewards.get(address) || 0) + reward);
          this.totalSupply += reward;
          
          rewards.push({
            nodeId: node.id,
            address,
            amount: reward,
            block: this.blockHeight
          });
          
          // Update node stats
          node.lastReward = blockTime;
          node.totalRewards = (node.totalRewards || 0) + reward;
          node.validations = (node.validations || 0) + 1;
        }
      }
      
      this.blockHeight = blockData.blockHeight || this.blockHeight + 1;
      this.lastRewardTime = blockTime;
    }
    
    return rewards;
  }

  claimRewards(nodeId) {
    const address = this.getNodeAddress(nodeId);
    const pending = pendingRewards.get(address) || 0;
    
    if (pending <= 0) {
      return { success: false, message: 'No pending rewards' };
    }
    
    // Update ledger
    const nodeData = coheriumLedger.get(address) || { balance: 0, lastClaim: 0, totalEarned: 0 };
    nodeData.balance += pending;
    nodeData.lastClaim = Date.now();
    nodeData.totalEarned += pending;
    coheriumLedger.set(address, nodeData);
    
    // Clear pending rewards
    pendingRewards.delete(address);
    
    return {
      success: true,
      amount: pending,
      newBalance: nodeData.balance,
      address
    };
  }

  getNodeBalance(nodeId) {
    const address = this.getNodeAddress(nodeId);
    const nodeData = coheriumLedger.get(address) || { balance: 0, lastClaim: 0, totalEarned: 0 };
    const pending = pendingRewards.get(address) || 0;
    
    return {
      address,
      balance: nodeData.balance,
      pendingRewards: pending,
      totalEarned: nodeData.totalEarned,
      lastClaim: nodeData.lastClaim
    };
  }
}

const coheriumManager = new CoheriumManager();

// Helper functions
function getAeBalance(address) {
  const account = aetheriumAccounts.get(address) || { balance: new BN(0) };
  return account.balance;
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    blockHeight: coheriumManager.blockHeight,
    totalSupply: coheriumManager.totalSupply,
    activeNodes: crownNodes.size,
    aetheriumAccounts: aetheriumAccounts.size
  });
});

// Aetherium faucet endpoint
app.post('/aetherium/faucet', express.json(), (req, res) => {
  try {
    const { address } = req.body;
    
    if (!address || typeof address !== 'string' || !address.startsWith('0x')) {
      return res.status(400).json({ error: 'Invalid address format' });
    }
    
    // Initialize account if it doesn't exist
    if (!aetheriumAccounts.has(address)) {
      aetheriumAccounts.set(address, {
        balance: new BN(0),
        nonce: 0
      });
    }
    
    // Fund the account (1 AE = 1e18 wei)
    const amount = new BN('**********000000000'); // 1 AE
    aetheriumAccounts.get(address).balance.iadd(amount);
    
    // Deduct from genesis
    aetheriumAccounts.get('******************************************').balance.isub(amount);
    
    res.json({
      success: true,
      address,
      amount: amount.toString(),
      newBalance: aetheriumAccounts.get(address).balance.toString()
    });
  } catch (error) {
    console.error('Faucet error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Send Aetherium transaction
app.post('/aetherium/send', express.json(), (req, res) => {
  try {
    const { from, to, value, maxFeePerGas, maxPriorityFeePerGas, data } = req.body;
    const nodeId = req.headers['x-node-id'] || 'unknown';
    
    // Validate required fields
    if (!from || !to || !value) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Initialize accounts if they don't exist
    if (!aetheriumAccounts.has(from)) {
      aetheriumAccounts.set(from, { balance: new BN(0), nonce: 0 });
    }
    if (!aetheriumAccounts.has(to)) {
      aetheriumAccounts.set(to, { balance: new BN(0), nonce: 0 });
    }
    
    const fromAccount = aetheriumAccounts.get(from);
    const toAccount = aetheriumAccounts.get(to);
    
    // Convert values to BN for calculations
    const amount = new BN(value);
    const fee = new BN(maxFeePerGas || '**********'); // Default to 1 Gwei if not specified
    
    // Check balance (amount + fee)
    const totalCost = amount.add(fee);
    if (fromAccount.balance.lt(totalCost)) {
      return res.status(400).json({ 
        error: 'Insufficient balance', 
        required: totalCost.toString(),
        available: fromAccount.balance.toString()
      });
    }
    
    // Process transaction
    fromAccount.balance.isub(totalCost);
    toAccount.balance.iadd(amount);
    
    // Update nonce
    fromAccount.nonce++;
    
    // Log transaction
    const txHash = '0x' + crypto.randomBytes(32).toString('hex');
    
    res.json({
      success: true,
      txHash,
      from,
      to,
      value: amount.toString(),
      fee: fee.toString(),
      nonce: fromAccount.nonce - 1
    });
    
    console.log(`[${new Date().toISOString()}] TX ${txHash} | From: ${from} | To: ${to} | Value: ${amount} | Fee: ${fee}`);
  } catch (error) {
    console.error('Send error:', error);
    res.status(500).json({ 
      error: 'Transaction failed',
      details: error.message 
    });
  }
});

// Initialize the server
async function initServer() {
  try {
    // Initialize genesis block
    console.log('Initializing genesis block...');
    coheriumManager.distributeRewards({
    
    // Start the server
    const PORT = process.env.PORT || 8080;
    const HOST = '0.0.0.0'; // Bind to all network interfaces
    
    const server = app.listen(PORT, HOST, () => {
      console.log(`\n🚀 KetherNet Server started on http://${HOST}:${PORT}`);
      console.log('💎 Coherium validation active');
      console.log('👑 Crown Consensus enabled');
      console.log(`💰 Coherium supply: ${coheriumManager.totalSupply} / ${MAX_COHERIUM_SUPPLY}`);
      console.log('⚡ Aetherium gas system ready');
    });
    
    // Schedule regular block rewards
    const blockRewardInterval = setInterval(() => {
      try {
        const rewards = coheriumManager.distributeRewards({
          blockHeight: coheriumManager.blockHeight + 1,
          timestamp: Date.now()
        });
        
        // Update base fee based on simulated block utilization
        const gasUsed = new BN(Math.floor(Math.random() * 30000000));
        aetheriumGas.updateBaseFee(gasUsed);
        
        // Log the new block
        console.log(`\n✨ Block #${coheriumManager.blockHeight}:`);
        console.log(`   Rewarded ${Object.keys(rewards).length} nodes`);
        console.log(`   New supply: ${coheriumManager.totalSupply}/${MAX_COHERIUM_SUPPLY}`);
        console.log(`   Base fee: ${aetheriumGas.getBaseFee().toString()} wei`);
      } catch (error) {
        console.error('Error in block reward distribution:', error);
      }
    }, 15000); // Every 15 seconds
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down KetherNet server...');
      clearInterval(blockRewardInterval);
      server.close(() => {
        console.log('✅ Server stopped');
        process.exit(0);
      });
      
      // Force shutdown after 5 seconds
      setTimeout(() => {
        console.error('⚠️ Forcing shutdown...');
        process.exit(1);
      }, 5000);
    });
    
    return server;
  } catch (error) {
    console.error('Failed to initialize server:', error);
    process.exit(1);
  }
}

// Start the server
const server = initServer().catch(error => {
  console.error('Failed to start server:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Optionally exit with a non-zero code
  // process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // Optionally exit with a non-zero code
  // process.exit(1);
});
