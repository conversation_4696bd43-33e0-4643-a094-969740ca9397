<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>14. Dashboard and Visualization Examples</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 900px;
            height: 830px;
            position: relative;
            border: 1px solid #eee;
            margin: 0 auto;
            background-color: white;
        }
        .element {
            position: absolute;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 2;
        }
        .element-number {
            position: absolute;
            top: 5px;
            left: 5px;
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
        }
        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 1;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>14. Dashboard and Visualization Examples</h1>
    
    <div class="diagram-container">
        <!-- Dashboard Visualization -->
        <div class="element" style="top: 50px; left: 350px; width: 300px; background-color: #e6f7ff; font-weight: bold; font-size: 20px;">
            Dashboard and Visualization Examples
            <div class="element-number">1</div>
        </div>
        
        <!-- Real-time Dashboards -->
        <div class="element" style="top: 120px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            Real-time Dashboards
            <div class="element-number">2</div>
        </div>
        
        <div class="element" style="top: 180px; left: 150px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            Metric Gauges<br>Circular or linear gauges showing current values
            <div class="element-number">3</div>
        </div>
        
        <div class="element" style="top: 180px; left: 550px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            Trend Charts<br>Line, bar, or area charts showing changes over time
            <div class="element-number">4</div>
        </div>
        
        <div class="element" style="top: 240px; left: 150px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            Heatmaps<br>Color-coded representations of metric values
            <div class="element-number">5</div>
        </div>
        
        <div class="element" style="top: 240px; left: 550px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            Status Indicators<br>Red/yellow/green indicators showing status
            <div class="element-number">6</div>
        </div>
        
        <!-- Trinity Visualization -->
        <div class="element" style="top: 300px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            Trinity Visualization
            <div class="element-number">7</div>
        </div>
        
        <div class="element" style="top: 360px; left: 150px; width: 200px; background-color: #fffbe6; font-size: 14px;">
            Trinity Triangle<br>Three-sided representation with G, D, R vertices
            <div class="element-number">8</div>
        </div>
        
        <div class="element" style="top: 360px; left: 550px; width: 200px; background-color: #fffbe6; font-size: 14px;">
            Force Diagram<br>Dynamic visualization showing push and pull
            <div class="element-number">9</div>
        </div>
        
        <div class="element" style="top: 420px; left: 150px; width: 200px; background-color: #fffbe6; font-size: 14px;">
            Vector Field<br>Directional representation of curl and divergence
            <div class="element-number">10</div>
        </div>
        
        <div class="element" style="top: 420px; left: 550px; width: 200px; background-color: #fffbe6; font-size: 14px;">
            Phase Space Plot<br>Representation of system state in G-D-R coordinates
            <div class="element-number">11</div>
        </div>
        
        <!-- Field Coherence Map -->
        <div class="element" style="top: 480px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            Field Coherence Map
            <div class="element-number">12</div>
        </div>
        
        <div class="element" style="top: 540px; left: 150px; width: 200px; background-color: #f9f0ff; font-size: 14px;">
            State Representation<br>Models π, φ, e states
            <div class="element-number">13</div>
        </div>
        
        <div class="element" style="top: 540px; left: 550px; width: 200px; background-color: #f9f0ff; font-size: 14px;">
            Energy Level Calculation<br>Computes energy levels
            <div class="element-number">14</div>
        </div>
        
        <div class="element" style="top: 600px; left: 350px; width: 300px; background-color: #f9f0ff; font-size: 14px;">
            Coherence Visualization<br>Generates coherence maps
            <div class="element-number">15</div>
        </div>
        
        <!-- User Interaction Models -->
        <div class="element" style="top: 660px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            User Interaction Models
            <div class="element-number">16</div>
        </div>
        
        <div class="element" style="top: 720px; left: 100px; width: 150px; background-color: #fff2e8; font-size: 12px;">
            Executive View<br>High-level dashboards
            <div class="element-number">17</div>
        </div>
        
        <div class="element" style="top: 720px; left: 300px; width: 150px; background-color: #fff2e8; font-size: 12px;">
            Analyst View<br>Detailed visualizations
            <div class="element-number">18</div>
        </div>
        
        <div class="element" style="top: 720px; left: 500px; width: 150px; background-color: #fff2e8; font-size: 12px;">
            Operator View<br>Operational controls
            <div class="element-number">19</div>
        </div>
        
        <div class="element" style="top: 720px; left: 700px; width: 150px; background-color: #fff2e8; font-size: 12px;">
            Compliance View<br>Regulatory tracking
            <div class="element-number">20</div>
        </div>
        
        <!-- Implementation -->
        <div class="element" style="top: 780px; left: 350px; width: 300px; background-color: #fffbe6; font-weight: bold; font-size: 16px;">
            Technical Implementation: NovaView and NovaVision
            <div class="element-number">21</div>
        </div>
        
        <!-- Connections -->
        <!-- Connect Dashboard Visualization to Real-time Dashboards -->
        <div class="connection" style="top: 100px; left: 500px; width: 2px; height: 20px; background-color: black;"></div>
        <div class="arrow" style="top: 110px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Real-time Dashboards to components -->
        <div class="connection" style="top: 170px; left: 350px; width: 100px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 180px; left: 240px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 170px; left: 550px; width: 100px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 180px; left: 640px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 200px; left: 350px; width: 100px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 240px; left: 240px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 200px; left: 550px; width: 100px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 240px; left: 640px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to Trinity Visualization -->
        <div class="connection" style="top: 270px; left: 500px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 290px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Trinity Visualization to components -->
        <div class="connection" style="top: 350px; left: 350px; width: 100px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 360px; left: 240px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 350px; left: 550px; width: 100px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 360px; left: 640px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 390px; left: 350px; width: 100px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 420px; left: 240px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 390px; left: 550px; width: 100px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 420px; left: 640px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to Field Coherence Map -->
        <div class="connection" style="top: 450px; left: 500px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 470px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Field Coherence Map to components -->
        <div class="connection" style="top: 530px; left: 350px; width: 100px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 540px; left: 240px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 530px; left: 550px; width: 100px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 540px; left: 640px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 570px; left: 500px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 590px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect to User Interaction Models -->
        <div class="connection" style="top: 630px; left: 500px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 650px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect User Interaction Models to components -->
        <div class="connection" style="top: 710px; left: 350px; width: 175px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 720px; left: 165px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 710px; left: 425px; width: 50px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 720px; left: 365px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 710px; left: 475px; width: 100px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 720px; left: 565px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 710px; left: 550px; width: 225px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 720px; left: 765px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to Implementation -->
        <div class="connection" style="top: 750px; left: 500px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 770px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
    </div>
</body>
</html>
