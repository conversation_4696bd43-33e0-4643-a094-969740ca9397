/**
 * CHAEONIX Live Trading Bot Control Component
 * Provides interface to start/stop and monitor the live auto-trading bot
 */

import React, { useState, useEffect } from 'react';

const LiveTradingBotControl = () => {
  const [botStatus, setBotStatus] = useState({
    active: false,
    total_trades: 0,
    daily_pnl: 0,
    positions: 0,
    account_balance: 100000
  });
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [error, setError] = useState(null);

  // Fetch bot status
  const fetchBotStatus = async () => {
    try {
      const response = await fetch('/api/trading/live-bot');
      const data = await response.json();
      setBotStatus(data.bot_status || {
        active: false,
        total_trades: 0,
        daily_pnl: 0,
        positions: 0,
        account_balance: 100000
      });
      setLastUpdate(new Date().toLocaleTimeString());
      setError(null);
    } catch (err) {
      setError('Failed to fetch bot status');
      console.error('Bot status fetch error:', err);
    }
  };

  // Start live trading bot
  const startBot = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/trading/live-bot', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'START' })
      });
      
      const result = await response.json();
      
      if (response.ok) {
        console.log('🚀 CHAEONIX Live Trading Bot Started:', result);
        await fetchBotStatus();
      } else {
        throw new Error(result.error || 'Failed to start bot');
      }
    } catch (err) {
      setError(err.message);
      console.error('Start bot error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Stop live trading bot
  const stopBot = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/trading/live-bot', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'STOP' })
      });
      
      const result = await response.json();
      
      if (response.ok) {
        console.log('🛑 CHAEONIX Live Trading Bot Stopped:', result);
        await fetchBotStatus();
      } else {
        throw new Error(result.error || 'Failed to stop bot');
      }
    } catch (err) {
      setError(err.message);
      console.error('Stop bot error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-refresh bot status
  useEffect(() => {
    fetchBotStatus();
    
    const interval = setInterval(fetchBotStatus, 5000); // Update every 5 seconds
    
    return () => clearInterval(interval);
  }, []);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Calculate P&L percentage
  const pnlPercentage = ((botStatus.daily_pnl / botStatus.account_balance) * 100).toFixed(2);
  const isProfitable = botStatus.daily_pnl >= 0;

  return (
    <div className="bg-gray-900 border border-gray-700 rounded-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className={`w-3 h-3 rounded-full ${botStatus.active ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`} />
          <h3 className="text-xl font-bold text-white">
            🤖 CHAEONIX Live Auto-Trading Bot
          </h3>
        </div>
        
        <div className="flex items-center space-x-2 text-sm text-gray-400">
          <span>Last Update:</span>
          <span className="text-blue-400">{lastUpdate || 'Never'}</span>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900/50 border border-red-500 rounded-lg p-3 mb-4">
          <div className="flex items-center space-x-2">
            <span className="text-red-400">⚠️</span>
            <span className="text-red-300">{error}</span>
          </div>
        </div>
      )}

      {/* Bot Status Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        {/* Status */}
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="text-sm text-gray-400 mb-1">Status</div>
          <div className={`text-lg font-bold ${botStatus.active ? 'text-green-400' : 'text-red-400'}`}>
            {botStatus.active ? 'ACTIVE' : 'STOPPED'}
          </div>
        </div>

        {/* Account Balance */}
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="text-sm text-gray-400 mb-1">Balance</div>
          <div className="text-lg font-bold text-blue-400">
            {formatCurrency(botStatus.account_balance)}
          </div>
        </div>

        {/* Daily P&L */}
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="text-sm text-gray-400 mb-1">Daily P&L</div>
          <div className={`text-lg font-bold ${isProfitable ? 'text-green-400' : 'text-red-400'}`}>
            {formatCurrency(botStatus.daily_pnl)}
            <span className="text-sm ml-1">({pnlPercentage}%)</span>
          </div>
        </div>

        {/* Total Trades */}
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="text-sm text-gray-400 mb-1">Trades</div>
          <div className="text-lg font-bold text-purple-400">
            {botStatus.total_trades}
            <span className="text-sm text-gray-400 ml-1">
              ({botStatus.positions} open)
            </span>
          </div>
        </div>
      </div>

      {/* Trading Configuration */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6">
        <h4 className="text-lg font-semibold text-white mb-3">🔱 738-Point System Configuration</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <div className="text-gray-400 mb-2">Big 3 Engines (153.61%)</div>
            <div className="space-y-1">
              <div className="text-yellow-400">🏆 NEFC - Financial Coherence</div>
              <div className="text-yellow-400">🏆 NERS - Risk Management</div>
              <div className="text-yellow-400">🏆 NEPI - Intelligence</div>
            </div>
          </div>
          <div>
            <div className="text-gray-400 mb-2">Other Penta (105.58%)</div>
            <div className="space-y-1">
              <div className="text-blue-400">⚡ NERE - Resonance</div>
              <div className="text-blue-400">⚡ NECE - Cognition</div>
            </div>
          </div>
          <div>
            <div className="text-gray-400 mb-2">Markets & Risk</div>
            <div className="space-y-1">
              <div className="text-green-400">📈 Stocks (40%)</div>
              <div className="text-orange-400">₿ Crypto (35%)</div>
              <div className="text-purple-400">💱 Forex (25%)</div>
              <div className="text-red-400">🛡️ Max Risk: 2%/trade, 5%/day</div>
            </div>
          </div>
        </div>
      </div>

      {/* Control Buttons */}
      <div className="flex space-x-4">
        {!botStatus.active ? (
          <button
            onClick={startBot}
            disabled={isLoading}
            className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Starting...</span>
              </>
            ) : (
              <>
                <span>🚀</span>
                <span>START LIVE TRADING</span>
              </>
            )}
          </button>
        ) : (
          <button
            onClick={stopBot}
            disabled={isLoading}
            className="flex-1 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Stopping...</span>
              </>
            ) : (
              <>
                <span>🛑</span>
                <span>STOP TRADING</span>
              </>
            )}
          </button>
        )}

        <button
          onClick={fetchBotStatus}
          disabled={isLoading}
          className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
        >
          <span>🔄</span>
          <span>REFRESH</span>
        </button>
      </div>

      {/* Footer Info */}
      <div className="mt-4 pt-4 border-t border-gray-700">
        <div className="text-xs text-gray-500 text-center">
          🔱 CHAEONIX Coherence-Driven Aeonic Intelligence Engine • 
          738-Point System • φ-Encryption • MT5 Integration
        </div>
      </div>
    </div>
  );
};

export default LiveTradingBotControl;
