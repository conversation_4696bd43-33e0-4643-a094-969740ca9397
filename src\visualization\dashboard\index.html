<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comphyological Tensor Core Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="styles.css">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react@18.2.0/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <div class="container-fluid">
        <header class="py-3 mb-4 border-bottom">
            <div class="container d-flex flex-wrap justify-content-center">
                <a href="/" class="d-flex align-items-center mb-3 mb-lg-0 me-lg-auto text-dark text-decoration-none">
                    <span class="fs-4">Comphyological Tensor Core Dashboard</span>
                </a>
            </div>
        </header>

        <div class="row">
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>System Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="status-card">
                                    <h6>Comphyon Value</h6>
                                    <div id="comphyon-value" class="status-value">0.0000</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-card">
                                    <h6>Resonance Frequency</h6>
                                    <div id="resonance-frequency" class="status-value">396.00 Hz</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-card">
                                    <h6>Quantum Silence</h6>
                                    <div id="quantum-silence" class="status-value">No</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-card">
                                    <h6>Dominant Engine</h6>
                                    <div id="dominant-engine" class="status-value">None</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Comphyon History</h5>
                    </div>
                    <div class="card-body">
                        <div id="comphyon-chart" class="chart-container"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Resonance State</h5>
                    </div>
                    <div class="card-body">
                        <div id="resonance-chart" class="chart-container"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Domain Energies</h5>
                    </div>
                    <div class="card-body">
                        <div id="energy-chart" class="chart-container"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Domain Weights</h5>
                    </div>
                    <div class="card-body">
                        <div id="weight-chart" class="chart-container"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Cross-Domain Resonance</h5>
                    </div>
                    <div class="card-body">
                        <div id="resonance-radar" class="chart-container"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Tensor Visualization</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>CSDE Tensor</h6>
                                <div id="csde-tensor" class="tensor-container"></div>
                            </div>
                            <div class="col-md-4">
                                <h6>CSFE Tensor</h6>
                                <div id="csfe-tensor" class="tensor-container"></div>
                            </div>
                            <div class="col-md-4">
                                <h6>CSME Tensor</h6>
                                <div id="csme-tensor" class="tensor-container"></div>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h6>Fused Tensor</h6>
                                <div id="fused-tensor" class="tensor-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Data Input</h5>
                    </div>
                    <div class="card-body">
                        <form id="data-form">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>CSDE Data</h6>
                                    <div class="mb-3">
                                        <label for="csde-governance" class="form-label">Governance</label>
                                        <input type="range" class="form-range" min="0" max="1" step="0.01" id="csde-governance" value="0.8">
                                        <span class="range-value">0.8</span>
                                    </div>
                                    <div class="mb-3">
                                        <label for="csde-data" class="form-label">Data Quality</label>
                                        <input type="range" class="form-range" min="0" max="1" step="0.01" id="csde-data" value="0.7">
                                        <span class="range-value">0.7</span>
                                    </div>
                                    <div class="mb-3">
                                        <label for="csde-action" class="form-label">Action</label>
                                        <select class="form-select" id="csde-action">
                                            <option value="allow" selected>Allow</option>
                                            <option value="monitor">Monitor</option>
                                            <option value="alert">Alert</option>
                                            <option value="block">Block</option>
                                            <option value="remediate">Remediate</option>
                                            <option value="escalate">Escalate</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="csde-confidence" class="form-label">Confidence</label>
                                        <input type="range" class="form-range" min="0" max="1" step="0.01" id="csde-confidence" value="0.9">
                                        <span class="range-value">0.9</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <h6>CSFE Data</h6>
                                    <div class="mb-3">
                                        <label for="csfe-risk" class="form-label">Risk</label>
                                        <input type="range" class="form-range" min="0" max="1" step="0.01" id="csfe-risk" value="0.3">
                                        <span class="range-value">0.3</span>
                                    </div>
                                    <div class="mb-3">
                                        <label for="csfe-finance" class="form-label">Policy Compliance</label>
                                        <input type="range" class="form-range" min="0" max="1" step="0.01" id="csfe-finance" value="0.6">
                                        <span class="range-value">0.6</span>
                                    </div>
                                    <div class="mb-3">
                                        <label for="csfe-action" class="form-label">Action</label>
                                        <select class="form-select" id="csfe-action">
                                            <option value="allow">Allow</option>
                                            <option value="monitor" selected>Monitor</option>
                                            <option value="alert">Alert</option>
                                            <option value="block">Block</option>
                                            <option value="remediate">Remediate</option>
                                            <option value="escalate">Escalate</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="csfe-confidence" class="form-label">Confidence</label>
                                        <input type="range" class="form-range" min="0" max="1" step="0.01" id="csfe-confidence" value="0.8">
                                        <span class="range-value">0.8</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <h6>CSME Data</h6>
                                    <div class="mb-3">
                                        <label for="csme-bio" class="form-label">Trust Factor</label>
                                        <input type="range" class="form-range" min="0" max="1" step="0.01" id="csme-bio" value="0.5">
                                        <span class="range-value">0.5</span>
                                    </div>
                                    <div class="mb-3">
                                        <label for="csme-med" class="form-label">Integrity Factor</label>
                                        <input type="range" class="form-range" min="0" max="1" step="0.01" id="csme-med" value="0.6">
                                        <span class="range-value">0.6</span>
                                    </div>
                                    <div class="mb-3">
                                        <label for="csme-action" class="form-label">Action</label>
                                        <select class="form-select" id="csme-action">
                                            <option value="allow">Allow</option>
                                            <option value="monitor">Monitor</option>
                                            <option value="alert" selected>Alert</option>
                                            <option value="block">Block</option>
                                            <option value="remediate">Remediate</option>
                                            <option value="escalate">Escalate</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="csme-confidence" class="form-label">Confidence</label>
                                        <input type="range" class="form-range" min="0" max="1" step="0.01" id="csme-confidence" value="0.7">
                                        <span class="range-value">0.7</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-primary">Process Data</button>
                                    <button type="button" id="reset-button" class="btn btn-secondary">Reset Metrics</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="dashboard.js"></script>
</body>
</html>
