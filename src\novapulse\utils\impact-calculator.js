/**
 * NovaPulse+ - Impact Calculator
 * 
 * This utility provides impact calculation functions for regulatory changes.
 */

const { createLogger } = require('../../utils/logger');

const logger = createLogger('impact-calculator');

/**
 * Calculate impact scores for a regulatory change
 * 
 * @param {Object} regulatoryChange - Regulatory change object
 * @returns {Object} - Impact scores
 */
function calculateImpactScores(regulatoryChange) {
  logger.debug('Calculating impact scores', { 
    changeId: regulatoryChange.id,
    regulation: regulatoryChange.regulation
  });
  
  // Calculate operational impact
  const operationalImpact = calculateOperationalImpact(regulatoryChange);
  
  // Calculate financial impact
  const financialImpact = calculateFinancialImpact(regulatoryChange);
  
  // Calculate compliance impact
  const complianceImpact = calculateComplianceImpact(regulatoryChange);
  
  // Calculate reputational impact
  const reputationalImpact = calculateReputationalImpact(regulatoryChange);
  
  // Calculate overall impact (weighted average)
  const weights = {
    operational: 0.25,
    financial: 0.25,
    compliance: 0.3,
    reputational: 0.2
  };
  
  const overallImpact = Math.round(
    operationalImpact * weights.operational +
    financialImpact * weights.financial +
    complianceImpact * weights.compliance +
    reputationalImpact * weights.reputational
  );
  
  return {
    overall: overallImpact,
    operational: operationalImpact,
    financial: financialImpact,
    compliance: complianceImpact,
    reputational: reputationalImpact
  };
}

/**
 * Calculate operational impact
 * 
 * @param {Object} regulatoryChange - Regulatory change object
 * @returns {number} - Operational impact score (0-100)
 * @private
 */
function calculateOperationalImpact(regulatoryChange) {
  let score = 0;
  
  // Base score based on priority
  const priorityScores = {
    'critical': 80,
    'high': 60,
    'medium': 40,
    'low': 20
  };
  
  score += priorityScores[regulatoryChange.priority] || 40;
  
  // Adjust based on categories
  const operationalCategories = [
    'data-security',
    'payment-security',
    'reporting',
    'governance'
  ];
  
  const categoryImpact = regulatoryChange.categories.reduce((impact, category) => {
    return operationalCategories.includes(category) ? impact + 10 : impact;
  }, 0);
  
  score += categoryImpact;
  
  // Adjust based on number of requirements
  score += Math.min(regulatoryChange.requirements.length * 5, 20);
  
  // Cap score at 100
  return Math.min(score, 100);
}

/**
 * Calculate financial impact
 * 
 * @param {Object} regulatoryChange - Regulatory change object
 * @returns {number} - Financial impact score (0-100)
 * @private
 */
function calculateFinancialImpact(regulatoryChange) {
  let score = 0;
  
  // Base score based on regulation
  const regulationScores = {
    'GDPR': 70, // High fines
    'CCPA': 60,
    'PCI DSS': 80, // Payment card industry
    'SOX': 75, // Financial reporting
    'HIPAA': 65,
    'ISO 27001': 50
  };
  
  score += regulationScores[regulatoryChange.regulation] || 50;
  
  // Adjust based on categories
  const financialCategories = [
    'payment-security',
    'financial-reporting',
    'contracts'
  ];
  
  const categoryImpact = regulatoryChange.categories.reduce((impact, category) => {
    return financialCategories.includes(category) ? impact + 15 : impact;
  }, 0);
  
  score += categoryImpact;
  
  // Adjust based on industries
  if (regulatoryChange.industries.includes('financial') || 
      regulatoryChange.industries.includes('banking') ||
      regulatoryChange.industries.includes('insurance')) {
    score += 20;
  } else if (regulatoryChange.industries.includes('healthcare') ||
             regulatoryChange.industries.includes('retail')) {
    score += 10;
  }
  
  // Cap score at 100
  return Math.min(score, 100);
}

/**
 * Calculate compliance impact
 * 
 * @param {Object} regulatoryChange - Regulatory change object
 * @returns {number} - Compliance impact score (0-100)
 * @private
 */
function calculateComplianceImpact(regulatoryChange) {
  let score = 0;
  
  // Base score based on regulation
  const regulationScores = {
    'GDPR': 80, // Strict compliance requirements
    'CCPA': 70,
    'PCI DSS': 85, // Very strict compliance requirements
    'SOX': 80, // Strict financial compliance
    'HIPAA': 75,
    'ISO 27001': 65
  };
  
  score += regulationScores[regulatoryChange.regulation] || 60;
  
  // Adjust based on deadline proximity
  if (regulatoryChange.deadline) {
    const daysUntilDeadline = Math.ceil(
      (new Date(regulatoryChange.deadline) - new Date()) / (1000 * 60 * 60 * 24)
    );
    
    if (daysUntilDeadline < 30) {
      score += 30; // Less than 1 month: high urgency
    } else if (daysUntilDeadline < 90) {
      score += 20; // Less than 3 months: medium urgency
    } else if (daysUntilDeadline < 180) {
      score += 10; // Less than 6 months: low urgency
    }
  }
  
  // Adjust based on categories
  const complianceCategories = [
    'data-protection',
    'consumer-rights',
    'reporting',
    'governance'
  ];
  
  const categoryImpact = regulatoryChange.categories.reduce((impact, category) => {
    return complianceCategories.includes(category) ? impact + 10 : impact;
  }, 0);
  
  score += categoryImpact;
  
  // Cap score at 100
  return Math.min(score, 100);
}

/**
 * Calculate reputational impact
 * 
 * @param {Object} regulatoryChange - Regulatory change object
 * @returns {number} - Reputational impact score (0-100)
 * @private
 */
function calculateReputationalImpact(regulatoryChange) {
  let score = 0;
  
  // Base score based on regulation
  const regulationScores = {
    'GDPR': 75, // High public visibility
    'CCPA': 70, // High public visibility
    'PCI DSS': 60, // Payment security
    'SOX': 50,
    'HIPAA': 80, // Health data is sensitive
    'ISO 27001': 40
  };
  
  score += regulationScores[regulatoryChange.regulation] || 50;
  
  // Adjust based on categories
  const reputationalCategories = [
    'data-protection',
    'consumer-rights',
    'data-security'
  ];
  
  const categoryImpact = regulatoryChange.categories.reduce((impact, category) => {
    return reputationalCategories.includes(category) ? impact + 15 : impact;
  }, 0);
  
  score += categoryImpact;
  
  // Adjust based on industries
  if (regulatoryChange.industries.includes('healthcare') || 
      regulatoryChange.industries.includes('financial') ||
      regulatoryChange.industries.includes('education')) {
    score += 20; // Industries with sensitive data
  } else if (regulatoryChange.industries.includes('retail') ||
             regulatoryChange.industries.includes('hospitality')) {
    score += 15; // Consumer-facing industries
  }
  
  // Cap score at 100
  return Math.min(score, 100);
}

module.exports = {
  calculateImpactScores
};
