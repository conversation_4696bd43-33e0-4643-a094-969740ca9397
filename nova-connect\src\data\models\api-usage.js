/**
 * NovaFuse Universal API Connector - API Usage Model
 * 
 * This module defines the MongoDB schema for tracking API usage.
 */

const mongoose = require('mongoose');
const { Schema } = mongoose;

// Define API usage schema
const ApiUsageSchema = new Schema({
  partnerId: {
    type: String,
    required: true,
    index: true
  },
  connectorId: {
    type: String,
    required: true,
    index: true
  },
  endpointId: {
    type: String,
    required: true
  },
  credentialId: {
    type: String,
    required: true,
    index: true
  },
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  requestId: {
    type: String,
    required: true,
    unique: true
  },
  success: {
    type: Boolean,
    required: true,
    index: true
  },
  statusCode: Number,
  errorMessage: String,
  duration: {
    type: Number, // in milliseconds
    required: true
  },
  requestSize: Number, // in bytes
  responseSize: Number, // in bytes
  ipAddress: String,
  userAgent: String,
  region: String,
  tags: [String],
  billable: {
    type: Boolean,
    default: true,
    index: true
  },
  cost: {
    type: Number,
    default: 0
  },
  metadata: {
    type: Map,
    of: Schema.Types.Mixed
  }
}, {
  timestamps: true
});

// Create compound indexes for efficient querying
ApiUsageSchema.index({ partnerId: 1, timestamp: 1 });
ApiUsageSchema.index({ connectorId: 1, timestamp: 1 });
ApiUsageSchema.index({ partnerId: 1, connectorId: 1, timestamp: 1 });
ApiUsageSchema.index({ partnerId: 1, success: 1, timestamp: 1 });
ApiUsageSchema.index({ timestamp: 1, billable: 1 });

// Create TTL index for automatic data expiration (default: 90 days)
const TTL_DAYS = process.env.API_USAGE_TTL_DAYS || 90;
ApiUsageSchema.index({ timestamp: 1 }, { expireAfterSeconds: TTL_DAYS * 24 * 60 * 60 });

// Static method to record API usage
ApiUsageSchema.statics.recordUsage = async function(usageData) {
  try {
    const usage = new this(usageData);
    return await usage.save();
  } catch (error) {
    console.error('Error recording API usage:', error);
    // Don't throw - we don't want API usage tracking to break the main functionality
    return null;
  }
};

// Static method to get usage statistics
ApiUsageSchema.statics.getStatistics = async function(filter, timeframe) {
  const match = { ...filter };
  
  // Add timeframe filter
  if (timeframe) {
    const now = new Date();
    let startDate;
    
    switch (timeframe) {
      case 'day':
        startDate = new Date(now.setDate(now.getDate() - 1));
        break;
      case 'week':
        startDate = new Date(now.setDate(now.getDate() - 7));
        break;
      case 'month':
        startDate = new Date(now.setMonth(now.getMonth() - 1));
        break;
      case 'year':
        startDate = new Date(now.setFullYear(now.getFullYear() - 1));
        break;
      default:
        startDate = new Date(0); // Beginning of time
    }
    
    match.timestamp = { $gte: startDate };
  }
  
  // Run aggregation
  const result = await this.aggregate([
    { $match: match },
    { 
      $group: {
        _id: null,
        totalRequests: { $sum: 1 },
        successfulRequests: { 
          $sum: { $cond: [{ $eq: ['$success', true] }, 1, 0] }
        },
        failedRequests: { 
          $sum: { $cond: [{ $eq: ['$success', false] }, 1, 0] }
        },
        totalDuration: { $sum: '$duration' },
        averageDuration: { $avg: '$duration' },
        totalCost: { $sum: '$cost' },
        minDuration: { $min: '$duration' },
        maxDuration: { $max: '$duration' }
      }
    }
  ]);
  
  if (result.length === 0) {
    return {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalDuration: 0,
      averageDuration: 0,
      totalCost: 0,
      minDuration: 0,
      maxDuration: 0,
      successRate: 0
    };
  }
  
  const stats = result[0];
  stats.successRate = stats.totalRequests > 0 
    ? (stats.successfulRequests / stats.totalRequests) * 100 
    : 0;
    
  delete stats._id;
  return stats;
};

// Create the model
const ApiUsage = mongoose.model('ApiUsage', ApiUsageSchema);

module.exports = ApiUsage;
