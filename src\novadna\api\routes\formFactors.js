/**
 * Form Factor API Routes
 * 
 * This module provides API routes for managing physical form factors.
 */

const express = require('express');
const router = express.Router();
const { validateFormFactorRequest } = require('../middleware/validation');
const { authenticateUser, authorizeProfile } = require('../middleware/auth');

/**
 * @route   POST /api/form-factors/qr
 * @desc    Generate a QR code for a profile
 * @access  Private
 */
router.post('/qr', authenticateUser, validateFormFactorRequest, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { profileId, accessLevel, metadata } = req.body;
    
    // Generate QR code
    const qrCode = await novaDNA.formFactorManager.generateQRCode(profileId, {
      accessLevel,
      metadata,
      qrCodeOptions: req.body.qrCodeOptions
    });
    
    res.status(201).json({
      status: 'success',
      data: {
        formFactorId: qrCode.formFactorId,
        profileId: qrCode.profileId,
        type: qrCode.type,
        qrCodeDataUrl: qrCode.qrCodeDataUrl,
        accessCode: qrCode.accessCode,
        createdAt: qrCode.createdAt
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/form-factors/nfc
 * @desc    Generate NFC data for a profile
 * @access  Private
 */
router.post('/nfc', authenticateUser, validateFormFactorRequest, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { profileId, accessLevel, metadata } = req.body;
    
    // Generate NFC data
    const nfcData = novaDNA.formFactorManager.generateNFCData(profileId, {
      accessLevel,
      metadata,
      nfcOptions: req.body.nfcOptions,
      additionalRecords: req.body.additionalRecords
    });
    
    res.status(201).json({
      status: 'success',
      data: {
        formFactorId: nfcData.formFactorId,
        profileId: nfcData.profileId,
        type: nfcData.type,
        ndefMessage: nfcData.ndefMessage,
        accessCode: nfcData.accessCode,
        createdAt: nfcData.createdAt
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/form-factors/wristband
 * @desc    Generate a wristband for a profile
 * @access  Private
 */
router.post('/wristband', authenticateUser, validateFormFactorRequest, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { profileId, accessLevel, metadata } = req.body;
    
    // Generate wristband
    const wristband = await novaDNA.formFactorManager.generateWristband(profileId, {
      accessLevel,
      metadata,
      qrCodeOptions: req.body.qrCodeOptions,
      nfcOptions: req.body.nfcOptions
    });
    
    res.status(201).json({
      status: 'success',
      data: {
        formFactorId: wristband.formFactorId,
        profileId: wristband.profileId,
        type: wristband.type,
        qrCodeDataUrl: wristband.qrCodeDataUrl,
        ndefMessage: wristband.ndefMessage,
        accessCode: wristband.accessCode,
        createdAt: wristband.createdAt
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/form-factors/vehicle
 * @desc    Generate a vehicle sticker for a profile
 * @access  Private
 */
router.post('/vehicle', authenticateUser, validateFormFactorRequest, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { profileId, accessLevel, vehicleInfo, metadata } = req.body;
    
    if (!vehicleInfo || !vehicleInfo.make || !vehicleInfo.model) {
      return res.status(400).json({
        status: 'error',
        error: 'Vehicle information is required'
      });
    }
    
    // Generate vehicle sticker
    const sticker = novaDNA.formFactorManager.generateVehicleSticker(profileId, vehicleInfo, {
      accessLevel,
      metadata,
      qrCodeOptions: req.body.qrCodeOptions
    });
    
    res.status(201).json({
      status: 'success',
      data: {
        formFactorId: sticker.formFactorId,
        profileId: sticker.profileId,
        type: sticker.type,
        vehicleInfo: sticker.vehicleInfo,
        accessCode: sticker.accessCode,
        createdAt: sticker.createdAt
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/form-factors/profile/:profileId
 * @desc    Get all form factors for a profile
 * @access  Private
 */
router.get('/profile/:profileId', authenticateUser, authorizeProfile, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { profileId } = req.params;
    
    // Get form factors
    const formFactors = novaDNA.formFactorManager.getProfileFormFactors(profileId);
    
    res.json({
      status: 'success',
      data: formFactors
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   DELETE /api/form-factors/:formFactorId
 * @desc    Revoke a form factor
 * @access  Private
 */
router.delete('/:formFactorId', authenticateUser, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { formFactorId } = req.params;
    
    // Revoke form factor
    const revoked = novaDNA.formFactorManager.revokeFormFactor(formFactorId);
    
    if (!revoked) {
      return res.status(404).json({
        status: 'error',
        error: 'Form factor not found'
      });
    }
    
    res.json({
      status: 'success',
      data: {
        formFactorId,
        revoked: true,
        revokedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/form-factors/verify
 * @desc    Verify a form factor
 * @access  Private (Service)
 */
router.post('/verify', async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { formFactorId, accessCode } = req.body;
    
    if (!formFactorId || !accessCode) {
      return res.status(400).json({
        status: 'error',
        error: 'Form factor ID and access code are required'
      });
    }
    
    // Verify form factor
    const verification = novaDNA.formFactorManager.verifyFormFactor(formFactorId, accessCode);
    
    if (!verification.valid) {
      return res.status(400).json({
        status: 'error',
        error: verification.error
      });
    }
    
    res.json({
      status: 'success',
      data: {
        valid: true,
        profileId: verification.profileId,
        type: verification.type,
        accessLevel: verification.accessLevel,
        createdAt: verification.createdAt
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
