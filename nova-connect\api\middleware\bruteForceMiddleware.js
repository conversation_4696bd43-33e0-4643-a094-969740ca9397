/**
 * Brute Force Protection Middleware
 * 
 * This middleware provides brute force attack protection for authentication endpoints.
 */

const BruteForceProtectionService = require('../services/BruteForceProtectionService');
const bruteForceService = new BruteForceProtectionService();

/**
 * Middleware to protect login routes from brute force attacks
 */
const protectLogin = async (req, res, next) => {
  try {
    // Get identifier (username or email)
    const identifier = req.body.username || req.body.email || req.ip;
    
    // Check if the identifier is blocked
    await bruteForceService.checkLoginAttempt(identifier);
    
    // Store the identifier in the request for later use
    req.bruteForceIdentifier = identifier;
    
    // Continue to the next middleware
    next();
  } catch (error) {
    if (error.name === 'BruteForceError') {
      res.setHeader('Retry-After', error.retryAfter);
      res.status(429).json({
        success: false,
        error: {
          code: 'BRUTE_FORCE_PROTECTION',
          message: error.message
        }
      });
    } else {
      next(error);
    }
  }
};

/**
 * Middleware to handle successful login
 */
const handleSuccessfulLogin = async (req, res, next) => {
  try {
    // Get identifier from request
    const identifier = req.bruteForceIdentifier;
    
    if (identifier) {
      // Reset login attempts for this identifier
      await bruteForceService.handleSuccessfulLogin(identifier);
    }
    
    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to handle failed login
 */
const handleFailedLogin = async (req, res, next) => {
  try {
    // Get identifier from request or body
    const identifier = req.bruteForceIdentifier || req.body.username || req.body.email || req.ip;
    
    if (identifier) {
      // Record failed login attempt
      await bruteForceService.handleFailedLogin(identifier);
    }
    
    next();
  } catch (error) {
    if (error.name === 'BruteForceError') {
      res.setHeader('Retry-After', error.retryAfter);
      res.status(429).json({
        success: false,
        error: {
          code: 'BRUTE_FORCE_PROTECTION',
          message: error.message
        }
      });
    } else {
      next(error);
    }
  }
};

module.exports = {
  protectLogin,
  handleSuccessfulLogin,
  handleFailedLogin
};
