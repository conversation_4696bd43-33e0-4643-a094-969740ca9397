/**
 * NovaFuse Universal API Connector - Marketplace Routes
 *
 * This module provides routes for the NovaMarketplace API.
 */

const express = require('express');
const router = express.Router();
const { auth, validator, sanitizer, rateLimiter } = require('../middleware');
const marketplaceService = require('../marketplace/services/marketplace-service');

// Async handler to catch errors
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Apply rate limiting to all routes
router.use(rateLimiter.rateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 100 // 100 requests per minute
}));

// Apply sanitization to all routes
router.use(sanitizer.sanitize);

/**
 * @route GET /api/marketplace
 * @description Get marketplace overview
 * @access Public
 */
router.get('/', asyncHandler(async (req, res) => {
  const overview = await marketplaceService.getMarketplaceOverview();
  res.json(overview);
}));

/**
 * @route GET /api/marketplace/categories
 * @description Get all marketplace categories
 * @access Public
 */
router.get('/categories', asyncHandler(async (req, res) => {
  const categories = await marketplaceService.getCategories();
  res.json({
    count: categories.length,
    categories
  });
}));

/**
 * @route GET /api/marketplace/categories/:id
 * @description Get a category by ID
 * @access Public
 */
router.get('/categories/:id', asyncHandler(async (req, res) => {
  const category = await marketplaceService.getCategory(req.params.id);
  res.json(category);
}));

/**
 * @route GET /api/marketplace/apis
 * @description Get all APIs in the marketplace
 * @access Public
 */
router.get('/apis', asyncHandler(async (req, res) => {
  const filters = {
    category: req.query.category,
    status: req.query.status,
    provider: req.query.provider,
    search: req.query.search
  };

  const pagination = {
    page: parseInt(req.query.page) || 1,
    limit: parseInt(req.query.limit) || 20
  };

  const apis = await marketplaceService.getApis(filters, pagination);
  res.json(apis);
}));

/**
 * @route GET /api/marketplace/apis/:id
 * @description Get an API by ID
 * @access Public
 */
router.get('/apis/:id', asyncHandler(async (req, res) => {
  const api = await marketplaceService.getApi(req.params.id);
  res.json(api);
}));

/**
 * @route POST /api/marketplace/apis/:id/subscribe
 * @description Subscribe to an API
 * @access Private
 */
router.post('/apis/:id/subscribe', auth.apiKeyAuth, asyncHandler(async (req, res) => {
  const subscription = await marketplaceService.subscribeToApi(req.params.id, req.user.id);
  res.status(201).json(subscription);
}));

/**
 * @route GET /api/marketplace/subscriptions
 * @description Get user's API subscriptions
 * @access Private
 */
router.get('/subscriptions', auth.apiKeyAuth, asyncHandler(async (req, res) => {
  const subscriptions = await marketplaceService.getUserSubscriptions(req.user.id);
  res.json({
    count: subscriptions.length,
    subscriptions
  });
}));

/**
 * @route DELETE /api/marketplace/subscriptions/:id
 * @description Cancel an API subscription
 * @access Private
 */
router.delete('/subscriptions/:id', auth.apiKeyAuth, asyncHandler(async (req, res) => {
  await marketplaceService.cancelSubscription(req.params.id, req.user.id);
  res.status(204).end();
}));

/**
 * @route GET /api/marketplace/providers
 * @description Get all API providers
 * @access Public
 */
router.get('/providers', asyncHandler(async (req, res) => {
  const providers = await marketplaceService.getProviders();
  res.json({
    count: providers.length,
    providers
  });
}));

/**
 * @route GET /api/marketplace/providers/:id
 * @description Get a provider by ID
 * @access Public
 */
router.get('/providers/:id', asyncHandler(async (req, res) => {
  const provider = await marketplaceService.getProvider(req.params.id);
  res.json(provider);
}));

/**
 * @route GET /api/marketplace/featured
 * @description Get featured APIs
 * @access Public
 */
router.get('/featured', asyncHandler(async (req, res) => {
  const featuredApis = await marketplaceService.getFeaturedApis();
  res.json({
    count: featuredApis.length,
    apis: featuredApis
  });
}));

/**
 * @route GET /api/marketplace/trending
 * @description Get trending APIs
 * @access Public
 */
router.get('/trending', asyncHandler(async (req, res) => {
  const trendingApis = await marketplaceService.getTrendingApis();
  res.json({
    count: trendingApis.length,
    apis: trendingApis
  });
}));

module.exports = router;
