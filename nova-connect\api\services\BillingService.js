/**
 * NovaConnect UAC Billing Service
 *
 * This service handles billing-related functionality, including
 * GCP Marketplace entitlements and usage reporting.
 */

const { ServiceUsageClient } = require('@google-cloud/service-usage');
const { CloudBillingClient } = require('@google-cloud/billing');
const { BigQuery } = require('@google-cloud/bigquery');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs').promises;
const path = require('path');
const logger = require('../../config/logger');
const FeatureService = require('./FeatureService');
const { ValidationError, NotFoundError } = require('../utils/errors');

class BillingService {
  constructor() {
    this.dataDir = path.join(__dirname, '../../data/billing');
    this.entitlementsFile = path.join(this.dataDir, 'entitlements.json');
    this.usageFile = path.join(this.dataDir, 'usage.json');
    this.featureService = new FeatureService();

    // Initialize Google Cloud clients if available
    try {
      if (process.env.GCP_PROJECT_ID) {
        this.serviceUsageClient = new ServiceUsageClient();
        this.cloudBillingClient = new CloudBillingClient();
        this.bigquery = new BigQuery({
          projectId: process.env.GCP_PROJECT_ID
        });
        logger.info('Initialized Google Cloud clients for billing');
      }
    } catch (error) {
      logger.error('Error initializing Google Cloud clients for billing', { error: error.message });
    }

    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });

      // Initialize entitlements file if it doesn't exist
      try {
        await fs.access(this.entitlementsFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.entitlementsFile, JSON.stringify({}));
        } else {
          throw error;
        }
      }

      // Initialize usage file if it doesn't exist
      try {
        await fs.access(this.usageFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.usageFile, JSON.stringify({}));
        } else {
          throw error;
        }
      }
    } catch (error) {
      logger.error('Error ensuring billing data directory', { error: error.message });
      throw error;
    }
  }

  /**
   * Load entitlements from file
   */
  async loadEntitlements() {
    try {
      const data = await fs.readFile(this.entitlementsFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      logger.error('Error loading entitlements', { error: error.message });
      return {};
    }
  }

  /**
   * Save entitlements to file
   */
  async saveEntitlements(entitlements) {
    try {
      await fs.writeFile(this.entitlementsFile, JSON.stringify(entitlements, null, 2));
    } catch (error) {
      logger.error('Error saving entitlements', { error: error.message });
      throw error;
    }
  }

  /**
   * Load usage from file
   */
  async loadUsage() {
    try {
      const data = await fs.readFile(this.usageFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      logger.error('Error loading usage', { error: error.message });
      return {};
    }
  }

  /**
   * Save usage to file
   */
  async saveUsage(usage) {
    try {
      await fs.writeFile(this.usageFile, JSON.stringify(usage, null, 2));
    } catch (error) {
      logger.error('Error saving usage', { error: error.message });
      throw error;
    }
  }

  /**
   * Enable features for a customer
   */
  async enableFeatures(customerId, entitlement) {
    try {
      logger.info('Enabling features for customer', { customerId, entitlement });

      // Load current entitlements
      const entitlements = await this.loadEntitlements();

      // Add or update entitlement
      entitlements[customerId] = {
        ...entitlement,
        status: 'ACTIVE',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Save entitlements
      await this.saveEntitlements(entitlements);

      // Enable features based on entitlement
      const tier = entitlement.plan || 'core';
      await this.featureService.enableFeaturesForTier(customerId, tier);

      // Log to BigQuery if available
      if (this.bigquery) {
        const dataset = this.bigquery.dataset('billing');
        const table = dataset.table('entitlements');

        await table.insert([{
          customer_id: customerId,
          entitlement_id: entitlement.id || `entitlement-${customerId}`,
          plan: tier,
          status: 'ACTIVE',
          created_at: new Date().toISOString(),
          event_type: 'ENTITLEMENT_CREATED'
        }]);
      }

      logger.info('Features enabled for customer', { customerId, tier });
    } catch (error) {
      logger.error('Error enabling features', { error: error.message, customerId });
      throw error;
    }
  }

  /**
   * Update features for a customer
   */
  async updateFeatures(customerId, entitlement) {
    try {
      logger.info('Updating features for customer', { customerId, entitlement });

      // Load current entitlements
      const entitlements = await this.loadEntitlements();

      // Check if entitlement exists
      if (!entitlements[customerId]) {
        logger.warn('Entitlement not found for customer', { customerId });
        return this.enableFeatures(customerId, entitlement);
      }

      // Get current tier
      const currentTier = entitlements[customerId].plan || 'core';

      // Update entitlement
      entitlements[customerId] = {
        ...entitlements[customerId],
        ...entitlement,
        updatedAt: new Date().toISOString()
      };

      // Save entitlements
      await this.saveEntitlements(entitlements);

      // Update features if tier changed
      const newTier = entitlement.plan || currentTier;
      if (newTier !== currentTier) {
        await this.featureService.enableFeaturesForTier(customerId, newTier);
      }

      // Log to BigQuery if available
      if (this.bigquery) {
        const dataset = this.bigquery.dataset('billing');
        const table = dataset.table('entitlements');

        await table.insert([{
          customer_id: customerId,
          entitlement_id: entitlement.id || `entitlement-${customerId}`,
          plan: newTier,
          status: entitlements[customerId].status,
          updated_at: new Date().toISOString(),
          event_type: 'ENTITLEMENT_UPDATED'
        }]);
      }

      logger.info('Features updated for customer', { customerId, tier: newTier });
    } catch (error) {
      logger.error('Error updating features', { error: error.message, customerId });
      throw error;
    }
  }

  /**
   * Disable features for a customer
   */
  async disableFeatures(customerId, entitlement) {
    try {
      logger.info('Disabling features for customer', { customerId });

      // Load current entitlements
      const entitlements = await this.loadEntitlements();

      // Check if entitlement exists
      if (!entitlements[customerId]) {
        logger.warn('Entitlement not found for customer', { customerId });
        return;
      }

      // Update entitlement status
      entitlements[customerId] = {
        ...entitlements[customerId],
        status: 'DELETED',
        updatedAt: new Date().toISOString(),
        deletedAt: new Date().toISOString()
      };

      // Save entitlements
      await this.saveEntitlements(entitlements);

      // Disable features
      await this.featureService.disableFeatures(customerId);

      // Log to BigQuery if available
      if (this.bigquery) {
        const dataset = this.bigquery.dataset('billing');
        const table = dataset.table('entitlements');

        await table.insert([{
          customer_id: customerId,
          entitlement_id: entitlement?.id || entitlements[customerId].id || `entitlement-${customerId}`,
          plan: entitlements[customerId].plan,
          status: 'DELETED',
          updated_at: new Date().toISOString(),
          deleted_at: new Date().toISOString(),
          event_type: 'ENTITLEMENT_DELETED'
        }]);
      }

      logger.info('Features disabled for customer', { customerId });
    } catch (error) {
      logger.error('Error disabling features', { error: error.message, customerId });
      throw error;
    }
  }

  /**
   * Activate features for a customer
   */
  async activateFeatures(customerId, entitlement) {
    try {
      logger.info('Activating features for customer', { customerId });

      // Load current entitlements
      const entitlements = await this.loadEntitlements();

      // Check if entitlement exists
      if (!entitlements[customerId]) {
        logger.warn('Entitlement not found for customer', { customerId });
        return this.enableFeatures(customerId, entitlement);
      }

      // Update entitlement status
      entitlements[customerId] = {
        ...entitlements[customerId],
        status: 'ACTIVE',
        updatedAt: new Date().toISOString()
      };

      // Save entitlements
      await this.saveEntitlements(entitlements);

      // Enable features
      const tier = entitlements[customerId].plan || 'core';
      await this.featureService.enableFeaturesForTier(customerId, tier);

      // Log to BigQuery if available
      if (this.bigquery) {
        const dataset = this.bigquery.dataset('billing');
        const table = dataset.table('entitlements');

        await table.insert([{
          customer_id: customerId,
          entitlement_id: entitlement?.id || entitlements[customerId].id || `entitlement-${customerId}`,
          plan: tier,
          status: 'ACTIVE',
          updated_at: new Date().toISOString(),
          event_type: 'ENTITLEMENT_ACTIVATED'
        }]);
      }

      logger.info('Features activated for customer', { customerId, tier });
    } catch (error) {
      logger.error('Error activating features', { error: error.message, customerId });
      throw error;
    }
  }

  /**
   * Suspend features for a customer
   */
  async suspendFeatures(customerId, entitlement) {
    try {
      logger.info('Suspending features for customer', { customerId });

      // Load current entitlements
      const entitlements = await this.loadEntitlements();

      // Check if entitlement exists
      if (!entitlements[customerId]) {
        logger.warn('Entitlement not found for customer', { customerId });
        return;
      }

      // Update entitlement status
      entitlements[customerId] = {
        ...entitlements[customerId],
        status: 'SUSPENDED',
        updatedAt: new Date().toISOString()
      };

      // Save entitlements
      await this.saveEntitlements(entitlements);

      // Suspend features
      await this.featureService.suspendFeatures(customerId);

      // Log to BigQuery if available
      if (this.bigquery) {
        const dataset = this.bigquery.dataset('billing');
        const table = dataset.table('entitlements');

        await table.insert([{
          customer_id: customerId,
          entitlement_id: entitlement?.id || entitlements[customerId].id || `entitlement-${customerId}`,
          plan: entitlements[customerId].plan,
          status: 'SUSPENDED',
          updated_at: new Date().toISOString(),
          event_type: 'ENTITLEMENT_SUSPENDED'
        }]);
      }

      logger.info('Features suspended for customer', { customerId });
    } catch (error) {
      logger.error('Error suspending features', { error: error.message, customerId });
      throw error;
    }
  }

  /**
   * Get customer entitlements
   */
  async getCustomerEntitlements(customerId) {
    try {
      // Load entitlements
      const entitlements = await this.loadEntitlements();

      // Return customer entitlement or empty object
      return entitlements[customerId] || { status: 'NOT_FOUND' };
    } catch (error) {
      logger.error('Error getting customer entitlements', { error: error.message, customerId });
      throw error;
    }
  }

  /**
   * Get customer usage
   */
  async getCustomerUsage(customerId, startDate, endDate) {
    try {
      // Parse dates
      const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Default to last 30 days
      const end = endDate ? new Date(endDate) : new Date();

      // Load usage
      const usage = await this.loadUsage();

      // Get customer usage
      const customerUsage = usage[customerId] || {};

      // Filter by date range
      const filteredUsage = Object.entries(customerUsage)
        .filter(([date]) => {
          const usageDate = new Date(date);
          return usageDate >= start && usageDate <= end;
        })
        .reduce((acc, [date, metrics]) => {
          acc[date] = metrics;
          return acc;
        }, {});

      // Calculate totals
      const totals = {};
      Object.values(filteredUsage).forEach(metrics => {
        Object.entries(metrics).forEach(([metric, value]) => {
          totals[metric] = (totals[metric] || 0) + value;
        });
      });

      return {
        customerId,
        startDate: start.toISOString(),
        endDate: end.toISOString(),
        usage: filteredUsage,
        totals
      };
    } catch (error) {
      logger.error('Error getting customer usage', { error: error.message, customerId });
      throw error;
    }
  }

  /**
   * Report usage
   */
  async reportUsage(customerId, metricName, quantity, timestamp = new Date().toISOString(), tenantId = null) {
    try {
      logger.info('Reporting usage', { customerId, metricName, quantity, tenantId });

      // Load usage
      const usage = await this.loadUsage();

      // Initialize customer usage if needed
      if (!usage[customerId]) {
        usage[customerId] = {};
      }

      // Get date (YYYY-MM-DD)
      const date = timestamp.split('T')[0];

      // Initialize date usage if needed
      if (!usage[customerId][date]) {
        usage[customerId][date] = {};
      }

      // Update usage
      usage[customerId][date][metricName] = (usage[customerId][date][metricName] || 0) + quantity;

      // Save usage
      await this.saveUsage(usage);

      // Report to GCP Marketplace if available
      if (this.cloudBillingClient && process.env.GCP_BILLING_ACCOUNT) {
        // Implement GCP Marketplace usage reporting for single-tenant architecture
        if (tenantId) {
          try {
            // For single-tenant architecture, we need to include the tenant ID
            const reportRequest = {
              parent: `projects/${process.env.GCP_PROJECT_ID}`,
              resourceName: `projects/${process.env.GCP_PROJECT_ID}/services/novafuse.googleapis.com`,
              usageMetric: metricName,
              usageValue: quantity,
              timestamp: timestamp,
              labels: {
                tenant_id: tenantId
              }
            };

            // Report usage to GCP Marketplace
            await this.cloudBillingClient.reportUsage(reportRequest);
            logger.info('GCP Marketplace usage reported for tenant', { tenantId, metricName, quantity });
          } catch (reportError) {
            logger.error('Error reporting usage to GCP Marketplace', {
              error: reportError.message,
              tenantId,
              customerId,
              metricName
            });
          }
        } else {
          logger.warn('Tenant ID not provided for GCP Marketplace usage reporting');
        }
      }

      // Log to BigQuery if available
      if (this.bigquery) {
        const dataset = this.bigquery.dataset('billing');
        const table = dataset.table('usage');

        await table.insert([{
          customer_id: customerId,
          tenant_id: tenantId,
          metric_name: metricName,
          quantity: quantity,
          timestamp: timestamp,
          date: date
        }]);
      }

      logger.info('Usage reported', { customerId, tenantId, metricName, quantity });
    } catch (error) {
      logger.error('Error reporting usage', { error: error.message, customerId, tenantId, metricName });
      throw error;
    }
  }

  /**
   * Report tenant-specific usage
   */
  async reportTenantUsage(tenantId, metricName, quantity, timestamp = new Date().toISOString()) {
    try {
      // For tenant-specific usage, we use the tenant ID as the customer ID
      // This ensures proper isolation in our usage tracking
      const customerId = `tenant-${tenantId}`;

      // Report usage with tenant ID
      return this.reportUsage(customerId, metricName, quantity, timestamp, tenantId);
    } catch (error) {
      logger.error('Error reporting tenant usage', { error: error.message, tenantId, metricName });
      throw error;
    }
  }
}

module.exports = BillingService;
