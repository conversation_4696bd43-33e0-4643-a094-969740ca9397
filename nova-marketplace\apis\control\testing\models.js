/**
 * Control Testing API - Models
 * 
 * This file defines the models for the Control Testing API.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Control Schema
 * 
 * Represents a control that can be tested.
 */
const ControlSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  type: {
    type: String,
    required: true,
    enum: ['Preventive', 'Detective', 'Corrective', 'Directive']
  },
  category: {
    type: String,
    required: true,
    enum: ['Administrative', 'Technical', 'Physical']
  },
  status: {
    type: String,
    required: true,
    enum: ['Active', 'Inactive', 'Deprecated'],
    default: 'Active'
  },
  owner: {
    type: String,
    required: true
  },
  implementationDate: {
    type: Date,
    required: true
  },
  lastReviewDate: {
    type: Date
  },
  nextReviewDate: {
    type: Date
  },
  relatedFrameworks: [{
    type: Schema.Types.ObjectId,
    ref: 'Framework'
  }],
  relatedPolicies: [{
    type: Schema.Types.ObjectId,
    ref: 'Policy'
  }],
  tags: [{
    type: String
  }],
  metadata: {
    type: Map,
    of: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

/**
 * Test Plan Schema
 * 
 * Represents a plan for testing controls.
 */
const TestPlanSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  status: {
    type: String,
    required: true,
    enum: ['Draft', 'Active', 'Completed', 'Archived'],
    default: 'Draft'
  },
  owner: {
    type: String,
    required: true
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  frequency: {
    type: String,
    enum: ['One-time', 'Daily', 'Weekly', 'Monthly', 'Quarterly', 'Semi-annually', 'Annually'],
    default: 'One-time'
  },
  controls: [{
    control: {
      type: Schema.Types.ObjectId,
      ref: 'Control',
      required: true
    },
    testProcedure: {
      type: String,
      required: true
    },
    expectedResults: {
      type: String,
      required: true
    },
    assignedTo: {
      type: String
    },
    dueDate: {
      type: Date
    }
  }],
  tags: [{
    type: String
  }],
  metadata: {
    type: Map,
    of: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

/**
 * Test Result Schema
 * 
 * Represents the results of a control test.
 */
const TestResultSchema = new Schema({
  testPlan: {
    type: Schema.Types.ObjectId,
    ref: 'TestPlan',
    required: true
  },
  control: {
    type: Schema.Types.ObjectId,
    ref: 'Control',
    required: true
  },
  tester: {
    type: String,
    required: true
  },
  testDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  status: {
    type: String,
    required: true,
    enum: ['Passed', 'Failed', 'Inconclusive', 'Not Tested'],
    default: 'Not Tested'
  },
  result: {
    type: String,
    required: true
  },
  evidence: [{
    name: {
      type: String,
      required: true
    },
    description: {
      type: String
    },
    fileUrl: {
      type: String
    },
    fileType: {
      type: String
    },
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],
  notes: {
    type: String
  },
  remediation: {
    required: {
      type: Boolean,
      default: false
    },
    plan: {
      type: String
    },
    assignedTo: {
      type: String
    },
    dueDate: {
      type: Date
    },
    status: {
      type: String,
      enum: ['Not Started', 'In Progress', 'Completed', 'Deferred'],
      default: 'Not Started'
    },
    completionDate: {
      type: Date
    },
    notes: {
      type: String
    }
  },
  reviewer: {
    type: String
  },
  reviewDate: {
    type: Date
  },
  reviewNotes: {
    type: String
  },
  tags: [{
    type: String
  }],
  metadata: {
    type: Map,
    of: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

/**
 * Framework Schema
 * 
 * Represents a compliance framework that contains controls.
 */
const FrameworkSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  version: {
    type: String,
    required: true
  },
  publisher: {
    type: String,
    required: true
  },
  publishDate: {
    type: Date,
    required: true
  },
  controls: [{
    type: Schema.Types.ObjectId,
    ref: 'Control'
  }],
  categories: [{
    name: {
      type: String,
      required: true
    },
    description: {
      type: String
    },
    controls: [{
      type: Schema.Types.ObjectId,
      ref: 'Control'
    }]
  }],
  status: {
    type: String,
    required: true,
    enum: ['Active', 'Inactive', 'Deprecated'],
    default: 'Active'
  },
  tags: [{
    type: String
  }],
  metadata: {
    type: Map,
    of: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

/**
 * Policy Schema
 * 
 * Represents a policy that is related to controls.
 */
const PolicySchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  content: {
    type: String,
    required: true
  },
  version: {
    type: String,
    required: true
  },
  status: {
    type: String,
    required: true,
    enum: ['Draft', 'Active', 'Archived'],
    default: 'Draft'
  },
  owner: {
    type: String,
    required: true
  },
  approver: {
    type: String
  },
  approvalDate: {
    type: Date
  },
  effectiveDate: {
    type: Date
  },
  expirationDate: {
    type: Date
  },
  lastReviewDate: {
    type: Date
  },
  nextReviewDate: {
    type: Date
  },
  relatedControls: [{
    type: Schema.Types.ObjectId,
    ref: 'Control'
  }],
  relatedFrameworks: [{
    type: Schema.Types.ObjectId,
    ref: 'Framework'
  }],
  tags: [{
    type: String
  }],
  metadata: {
    type: Map,
    of: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

/**
 * Dashboard Schema
 * 
 * Represents a dashboard for control testing metrics.
 */
const DashboardSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String
  },
  owner: {
    type: String,
    required: true
  },
  widgets: [{
    name: {
      type: String,
      required: true
    },
    type: {
      type: String,
      required: true,
      enum: ['ControlStatus', 'TestPlanStatus', 'TestResultTrend', 'FrameworkCompliance', 'RemediationStatus', 'Custom']
    },
    config: {
      type: Map,
      of: Schema.Types.Mixed,
      required: true
    },
    position: {
      x: {
        type: Number,
        required: true
      },
      y: {
        type: Number,
        required: true
      },
      width: {
        type: Number,
        required: true
      },
      height: {
        type: Number,
        required: true
      }
    }
  }],
  isPublic: {
    type: Boolean,
    default: false
  },
  sharedWith: [{
    type: String
  }],
  tags: [{
    type: String
  }],
  metadata: {
    type: Map,
    of: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create and export models
const Control = mongoose.model('Control', ControlSchema);
const TestPlan = mongoose.model('TestPlan', TestPlanSchema);
const TestResult = mongoose.model('TestResult', TestResultSchema);
const Framework = mongoose.model('Framework', FrameworkSchema);
const Policy = mongoose.model('Policy', PolicySchema);
const Dashboard = mongoose.model('Dashboard', DashboardSchema);

module.exports = {
  Control,
  TestPlan,
  TestResult,
  Framework,
  Policy,
  Dashboard
};
