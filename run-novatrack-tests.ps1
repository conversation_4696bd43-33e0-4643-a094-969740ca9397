# NovaFuse Universal Platform - NovaTrack Tests

# Function to display colored output
function Write-ColorOutput {
    param(
        [string]$Text,
        [string]$ForegroundColor = "White"
    )

    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Text
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Create directories for test results
Write-ColorOutput "Creating directories for test results..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "./test-results" | Out-Null
New-Item -ItemType Directory -Force -Path "./test-results/junit" | Out-Null

# Display welcome message
Write-ColorOutput "NovaFuse Universal Platform - NovaTrack Tests" -ForegroundColor Cyan
Write-ColorOutput "=============================================" -ForegroundColor Cyan
Write-ColorOutput "This script will run tests for the NovaTrack component." -ForegroundColor Cyan
Write-ColorOutput "" -ForegroundColor Cyan

# Run the simple test
Write-ColorOutput "Running simple NovaTrack tests..." -ForegroundColor Green
npx jest tests/unit/novatrack/tracking-manager.simple.test.js --verbose

# Run the tests with coverage
Write-ColorOutput "`nRunning NovaTrack tests with coverage..." -ForegroundColor Green
npx jest tests/unit/novatrack --coverage --coverageThreshold='{"global":{"branches":81,"functions":81,"lines":81,"statements":81}}'

# Display summary
Write-ColorOutput "`nTesting completed!" -ForegroundColor Green
Write-ColorOutput "Test results are available in the console output above." -ForegroundColor Green
Write-ColorOutput "Coverage report is available in ./coverage/lcov-report/index.html" -ForegroundColor Green
