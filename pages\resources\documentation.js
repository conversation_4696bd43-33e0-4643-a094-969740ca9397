import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import PageWithSidebar from '../../components/PageWithSidebar';

export default function Documentation() {
  const sidebarItems = [
    { label: 'Framework Documentation', href: '#framework-docs' },
    { label: 'White Papers', href: '#white-papers' },
    { label: 'Technical Specifications', href: '#technical-specs' },
    { label: 'Legal Documents', href: '#legal-docs' },
    { label: 'Partner Resources', href: '#partner-resources' },
    { label: 'Developer Resources', href: '#developer-resources' },
    { label: 'Back to Resources', href: '/resources' },
  ];

  return (
    <PageWithSidebar title="NovaFuse Documentation Repository" sidebarItems={sidebarItems}>
      <Head>
        <title>Documentation Repository | NovaFuse</title>
        <meta name="description" content="Access comprehensive documentation for the NovaFuse platform, including framework guides, white papers, and technical specifications." />
      </Head>

      {/* Introduction */}
      <div className="mb-12">
        <h1 className="text-3xl font-bold mb-4">NovaFuse Documentation Repository</h1>
        <p className="text-lg text-gray-300 mb-6">
          Access comprehensive documentation for the NovaFuse platform, including framework guides,
          white papers, technical specifications, and legal documents.
        </p>
        <div className="bg-blue-900 bg-opacity-30 border border-blue-700 rounded-lg p-6">
          <p className="text-gray-300">
            This repository houses all official NovaFuse documentation. All resources are regularly
            updated to reflect the latest platform capabilities, ecosystem structure, and best practices.
          </p>
        </div>
      </div>

      {/* Framework Documentation */}
      <div id="framework-docs" className="mb-12 scroll-mt-20">
        <h2 className="text-2xl font-bold mb-6 flex items-center">
          <span className="bg-blue-600 text-white p-1 rounded mr-2 text-sm">NEW</span>
          Framework Documentation
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-secondary border border-gray-700 rounded-lg overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2">NovaFuse Partner Network Handbook</h3>
              <p className="text-gray-300 mb-4">
                Comprehensive guide to the NovaFuse Partner Network, including Pioneer Partner tiers,
                Daring Developer domains, Cross-Cloud Guardians, and Innovation Catalysts.
              </p>
              <div className="flex items-center text-sm text-gray-400 mb-4">
                <span className="mr-4">Version 1.0 • April 2025</span>
                <span>42 pages</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-blue-900 bg-opacity-50 px-2 py-1 rounded text-xs">Strategic Framework</span>
                <span className="bg-blue-900 bg-opacity-50 px-2 py-1 rounded text-xs">Ecosystem Design</span>
                <span className="bg-blue-900 bg-opacity-50 px-2 py-1 rounded text-xs">Partner Program</span>
              </div>
              <a
                href="/resources/documents/novafuse-partner-network-handbook.pdf"
                className="accent-bg text-white px-4 py-2 rounded inline-flex items-center"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download PDF
              </a>
            </div>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2">Daring Developer Domain Allocation Guide</h3>
              <p className="text-gray-300 mb-4">
                Detailed specifications for the 40 Daring Developer collaborative roles, with emphasis on AI Governance,
                Quantum-Safe Compliance, and other emerging domains.
              </p>
              <div className="flex items-center text-sm text-gray-400 mb-4">
                <span className="mr-4">Version 1.0 • April 2025</span>
                <span>18 pages</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-purple-900 bg-opacity-50 px-2 py-1 rounded text-xs">Developer Program</span>
                <span className="bg-purple-900 bg-opacity-50 px-2 py-1 rounded text-xs">Domain Allocation</span>
                <span className="bg-purple-900 bg-opacity-50 px-2 py-1 rounded text-xs">Emerging Threats</span>
              </div>
              <a
                href="/resources/documents/daring-developer-domain-allocation.pdf"
                className="accent-bg text-white px-4 py-2 rounded inline-flex items-center"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download PDF
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* White Papers */}
      <div id="white-papers" className="mb-12 scroll-mt-20">
        <h2 className="text-2xl font-bold mb-6">White Papers</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-secondary border border-gray-700 rounded-lg overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2">The Partner Empowerment Model</h3>
              <p className="text-gray-300 mb-4">
                Detailed analysis of NovaFuse's revolutionary 18/82 Partner Empowerment Model and how it
                transforms the economics of compliance partnerships.
              </p>
              <div className="flex items-center text-sm text-gray-400 mb-4">
                <span className="mr-4">April 2025</span>
                <span>24 pages</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-blue-900 bg-opacity-50 px-2 py-1 rounded text-xs">Partner Economics</span>
                <span className="bg-blue-900 bg-opacity-50 px-2 py-1 rounded text-xs">Revenue Sharing</span>
                <span className="bg-blue-900 bg-opacity-50 px-2 py-1 rounded text-xs">Implementation Model</span>
              </div>
              <div className="flex flex-wrap gap-2">
                <a
                  href="/resources/white-papers/partner-empowerment-model.pdf"
                  className="accent-bg text-white px-4 py-2 rounded inline-flex items-center"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Download PDF
                </a>
                <Link
                  href="/component-demos/partner-empowerment-demo"
                  className="bg-blue-700 text-white px-4 py-2 rounded inline-flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                  View Interactive Demo
                </Link>
              </div>
            </div>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2">From 1882 to 18/82: A Revolution in Partnership Economics</h3>
              <p className="text-gray-300 mb-4">
                Historical analysis of how NovaFuse's 18/82 model represents a revolutionary approach to
                partnership economics, drawing parallels to the business transformation of 1882.
              </p>
              <div className="flex items-center text-sm text-gray-400 mb-4">
                <span className="mr-4">April 2025</span>
                <span>16 pages</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-blue-900 bg-opacity-50 px-2 py-1 rounded text-xs">Historical Context</span>
                <span className="bg-blue-900 bg-opacity-50 px-2 py-1 rounded text-xs">Economic Revolution</span>
                <span className="bg-blue-900 bg-opacity-50 px-2 py-1 rounded text-xs">Partnership Models</span>
              </div>
              <a
                href="/resources/white-papers/1882-to-1882-revolution.pdf"
                className="accent-bg text-white px-4 py-2 rounded inline-flex items-center"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download PDF
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Technical Specifications */}
      <div id="technical-specs" className="mb-12 scroll-mt-20">
        <h2 className="text-2xl font-bold mb-6">Technical Specifications</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-secondary border border-gray-700 rounded-lg overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2">NovaConnect UAC Technical Reference</h3>
              <p className="text-gray-300 mb-4">
                Comprehensive technical documentation for the NovaConnect Universal API Connector,
                including architecture, API reference, and integration guides.
              </p>
              <div className="flex items-center text-sm text-gray-400 mb-4">
                <span className="mr-4">Version 2.1 • March 2025</span>
                <span>86 pages</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-green-900 bg-opacity-50 px-2 py-1 rounded text-xs">API Reference</span>
                <span className="bg-green-900 bg-opacity-50 px-2 py-1 rounded text-xs">Integration</span>
                <span className="bg-green-900 bg-opacity-50 px-2 py-1 rounded text-xs">Architecture</span>
              </div>
              <a
                href="/resources/technical/novaconnect-uac-reference.pdf"
                className="accent-bg text-white px-4 py-2 rounded inline-flex items-center"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download PDF
              </a>
            </div>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2">Partner Empowerment Model Demo</h3>
              <p className="text-gray-300 mb-4">
                Interactive demo and technical documentation for the 18/82 Partner Empowerment Model,
                including ROI calculators, competitor comparisons, and role-specific views.
              </p>
              <div className="flex items-center text-sm text-gray-400 mb-4">
                <span className="mr-4">Version 1.0 • April 2025</span>
                <span>Interactive Demo</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-green-900 bg-opacity-50 px-2 py-1 rounded text-xs">Interactive</span>
                <span className="bg-green-900 bg-opacity-50 px-2 py-1 rounded text-xs">ROI Calculator</span>
                <span className="bg-green-900 bg-opacity-50 px-2 py-1 rounded text-xs">Role-Specific</span>
              </div>
              <Link
                href="/component-demos/partner-empowerment-demo"
                className="accent-bg text-white px-4 py-2 rounded inline-flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
                View Interactive Demo
              </Link>
            </div>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2">App Store Developer Guide</h3>
              <p className="text-gray-300 mb-4">
                Technical guide for developing applications for the NovaFuse Compliance App Store,
                including SDK documentation, best practices, and submission requirements.
              </p>
              <div className="flex items-center text-sm text-gray-400 mb-4">
                <span className="mr-4">Version 1.5 • February 2025</span>
                <span>64 pages</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-green-900 bg-opacity-50 px-2 py-1 rounded text-xs">SDK Reference</span>
                <span className="bg-green-900 bg-opacity-50 px-2 py-1 rounded text-xs">Development</span>
                <span className="bg-green-900 bg-opacity-50 px-2 py-1 rounded text-xs">Submission</span>
              </div>
              <a
                href="/resources/technical/app-store-developer-guide.pdf"
                className="accent-bg text-white px-4 py-2 rounded inline-flex items-center"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download PDF
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Legal Documents */}
      <div id="legal-docs" className="mb-12 scroll-mt-20">
        <h2 className="text-2xl font-bold mb-6">Legal Documents</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-secondary border border-gray-700 rounded-lg overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2">Pioneer Partner Program Agreement</h3>
              <p className="text-gray-300 mb-4">
                Legal agreement for Pioneer Partners, detailing rights, responsibilities, revenue sharing,
                and performance requirements for the 100-slot program.
              </p>
              <div className="flex items-center text-sm text-gray-400 mb-4">
                <span className="mr-4">Version 1.0 • April 2025</span>
                <span>28 pages</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-red-900 bg-opacity-50 px-2 py-1 rounded text-xs">Legal</span>
                <span className="bg-red-900 bg-opacity-50 px-2 py-1 rounded text-xs">Partner Agreement</span>
                <span className="bg-red-900 bg-opacity-50 px-2 py-1 rounded text-xs">Revenue Share</span>
              </div>
              <a
                href="/resources/legal/pioneer-partner-agreement.pdf"
                className="accent-bg text-white px-4 py-2 rounded inline-flex items-center"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download PDF
              </a>
            </div>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2">App Store Developer Pact</h3>
              <p className="text-gray-300 mb-4">
                Legal agreement for Daring Developers, detailing rights, responsibilities, revenue sharing,
                and performance requirements for the 40-slot program.
              </p>
              <div className="flex items-center text-sm text-gray-400 mb-4">
                <span className="mr-4">Version 1.0 • April 2025</span>
                <span>24 pages</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-red-900 bg-opacity-50 px-2 py-1 rounded text-xs">Legal</span>
                <span className="bg-red-900 bg-opacity-50 px-2 py-1 rounded text-xs">Developer Agreement</span>
                <span className="bg-red-900 bg-opacity-50 px-2 py-1 rounded text-xs">18/82 Promo</span>
              </div>
              <a
                href="/resources/legal/app-store-developer-pact.pdf"
                className="accent-bg text-white px-4 py-2 rounded inline-flex items-center"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download PDF
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Partner Resources */}
      <div id="partner-resources" className="mb-12 scroll-mt-20">
        <h2 className="text-2xl font-bold mb-6">Partner Resources</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-secondary border border-gray-700 rounded-lg overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2">Pioneer Partner Onboarding Guide</h3>
              <p className="text-gray-300 mb-4">
                Step-by-step guide for new Pioneer Partners, covering certification, platform access,
                marketing resources, and go-to-market strategies.
              </p>
              <div className="flex items-center text-sm text-gray-400 mb-4">
                <span className="mr-4">Version 1.0 • April 2025</span>
                <span>32 pages</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-yellow-900 bg-opacity-50 px-2 py-1 rounded text-xs">Onboarding</span>
                <span className="bg-yellow-900 bg-opacity-50 px-2 py-1 rounded text-xs">Partner Guide</span>
                <span className="bg-yellow-900 bg-opacity-50 px-2 py-1 rounded text-xs">Go-to-Market</span>
              </div>
              <a
                href="/resources/partner/pioneer-partner-onboarding.pdf"
                className="accent-bg text-white px-4 py-2 rounded inline-flex items-center"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download PDF
              </a>
            </div>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2">Implementation Methodology Guide</h3>
              <p className="text-gray-300 mb-4">
                Comprehensive guide to NovaFuse's implementation methodology, including best practices,
                templates, and case studies for successful client deployments.
              </p>
              <div className="flex items-center text-sm text-gray-400 mb-4">
                <span className="mr-4">Version 1.0 • April 2025</span>
                <span>48 pages</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-yellow-900 bg-opacity-50 px-2 py-1 rounded text-xs">Implementation</span>
                <span className="bg-yellow-900 bg-opacity-50 px-2 py-1 rounded text-xs">Methodology</span>
                <span className="bg-yellow-900 bg-opacity-50 px-2 py-1 rounded text-xs">Best Practices</span>
              </div>
              <a
                href="/resources/partner/implementation-methodology.pdf"
                className="accent-bg text-white px-4 py-2 rounded inline-flex items-center"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download PDF
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Developer Resources */}
      <div id="developer-resources" className="mb-12 scroll-mt-20">
        <h2 className="text-2xl font-bold mb-6">Developer Resources</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-secondary border border-gray-700 rounded-lg overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2">Daring Developer Onboarding Guide</h3>
              <p className="text-gray-300 mb-4">
                Step-by-step guide for new Daring Developers, covering SDK access, development environment setup,
                NovaCert certification, and Solutions Marketplace submission process.
              </p>
              <div className="flex items-center text-sm text-gray-400 mb-4">
                <span className="mr-4">Version 1.0 • April 2025</span>
                <span>36 pages</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-purple-900 bg-opacity-50 px-2 py-1 rounded text-xs">Onboarding</span>
                <span className="bg-purple-900 bg-opacity-50 px-2 py-1 rounded text-xs">Developer Guide</span>
                <span className="bg-purple-900 bg-opacity-50 px-2 py-1 rounded text-xs">App Submission</span>
              </div>
              <a
                href="/resources/developer/daring-developer-onboarding.pdf"
                className="accent-bg text-white px-4 py-2 rounded inline-flex items-center"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download PDF
              </a>
            </div>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2">Domain-Specific Development Guides</h3>
              <p className="text-gray-300 mb-4">
                Specialized guides for each of the 7 Daring Developer domains, with domain-specific
                requirements, best practices, and compliance considerations.
              </p>
              <div className="flex items-center text-sm text-gray-400 mb-4">
                <span className="mr-4">Version 1.0 • April 2025</span>
                <span>7 guides, 15-20 pages each</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-purple-900 bg-opacity-50 px-2 py-1 rounded text-xs">Domain Guides</span>
                <span className="bg-purple-900 bg-opacity-50 px-2 py-1 rounded text-xs">Specialized Dev</span>
                <span className="bg-purple-900 bg-opacity-50 px-2 py-1 rounded text-xs">Best Practices</span>
              </div>
              <a
                href="/resources/developer/domain-specific-guides.zip"
                className="accent-bg text-white px-4 py-2 rounded inline-flex items-center"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download ZIP
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-blue-900 rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Need Additional Resources?</h2>
        <p className="text-lg mb-6 max-w-3xl mx-auto">
          Our documentation is continuously updated to support your success in the NovaFuse ecosystem.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Link href="/contact" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
            Request Custom Documentation
          </Link>
          <Link href="/partner-knowledge-base" className="bg-white text-blue-700 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 inline-block">
            Visit Knowledge Base
          </Link>
        </div>
      </div>
    </PageWithSidebar>
  );
}
