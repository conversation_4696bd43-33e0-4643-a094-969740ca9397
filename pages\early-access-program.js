import React from 'react';

export default function EarlyAccessProgram() {
  return (
    <div>
      {/* Hero Section */}
      <div className="bg-secondary p-8 rounded-lg mb-8 relative overflow-hidden">
        <div className="relative z-10">
          <h2 className="text-3xl font-bold mb-4">Exclusive Early Access to NovaFuse API Superstore</h2>
          <p className="text-xl mb-6 max-w-3xl">
            Join a select group of innovative companies with early access to the NovaFuse API Superstore. Shape the future of GRC integration while gaining a competitive advantage.
          </p>
          <button className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700">
            Apply for Early Access
          </button>
        </div>
        <div className="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-l from-blue-500 to-transparent opacity-10"></div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        {/* Left Column */}
        <div>
          <div className="bg-secondary p-6 rounded-lg mb-8">
            <h3 className="text-2xl font-bold mb-4">Early Access Benefits</h3>
            
            <div className="space-y-4">
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full accent-bg flex items-center justify-center text-white font-bold mr-3">1</div>
                <div>
                  <h4 className="text-lg font-semibold">First-Mover Advantage</h4>
                  <p className="text-gray-300">Be among the first to integrate with NovaFuse and establish your position in the GRC ecosystem.</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full accent-bg flex items-center justify-center text-white font-bold mr-3">2</div>
                <div>
                  <h4 className="text-lg font-semibold">Shape the Product</h4>
                  <p className="text-gray-300">Provide direct input on features, APIs, and integration capabilities that matter to your business.</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full accent-bg flex items-center justify-center text-white font-bold mr-3">3</div>
                <div>
                  <h4 className="text-lg font-semibold">Premium Support</h4>
                  <p className="text-gray-300">Receive dedicated technical support and direct access to our engineering team.</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full accent-bg flex items-center justify-center text-white font-bold mr-3">4</div>
                <div>
                  <h4 className="text-lg font-semibold">Early Partner Status</h4>
                  <p className="text-gray-300">Lock in founding partner benefits, including premium revenue share and featured placement.</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-secondary p-6 rounded-lg">
            <h3 className="text-2xl font-bold mb-4">Program Timeline</h3>
            
            <div className="relative pl-8 space-y-8">
              <div className="relative">
                <div className="absolute left-0 top-0 -ml-8 w-6 h-6 rounded-full accent-bg flex items-center justify-center">
                  <span className="text-white font-bold">1</span>
                </div>
                <h4 className="text-lg font-semibold">Application & Selection</h4>
                <p className="text-gray-300 mt-1">Submit your application and our team will review it within 48 hours.</p>
                <p className="text-sm text-gray-400 mt-1">March 2025</p>
              </div>
              
              <div className="relative">
                <div className="absolute left-0 top-0 -ml-8 w-6 h-6 rounded-full accent-bg flex items-center justify-center">
                  <span className="text-white font-bold">2</span>
                </div>
                <h4 className="text-lg font-semibold">Onboarding & Setup</h4>
                <p className="text-gray-300 mt-1">Get access to the NovaFuse API Superstore and technical documentation.</p>
                <p className="text-sm text-gray-400 mt-1">April 2025</p>
              </div>
              
              <div className="relative">
                <div className="absolute left-0 top-0 -ml-8 w-6 h-6 rounded-full accent-bg flex items-center justify-center">
                  <span className="text-white font-bold">3</span>
                </div>
                <h4 className="text-lg font-semibold">Integration & Testing</h4>
                <p className="text-gray-300 mt-1">Build your integration with support from our engineering team.</p>
                <p className="text-sm text-gray-400 mt-1">April-May 2025</p>
              </div>
              
              <div className="relative">
                <div className="absolute left-0 top-0 -ml-8 w-6 h-6 rounded-full accent-bg flex items-center justify-center">
                  <span className="text-white font-bold">4</span>
                </div>
                <h4 className="text-lg font-semibold">Early Launch</h4>
                <p className="text-gray-300 mt-1">Go live with your integration ahead of the public launch.</p>
                <p className="text-sm text-gray-400 mt-1">June 2025</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Right Column */}
        <div>
          <div className="bg-secondary p-6 rounded-lg mb-8">
            <h3 className="text-2xl font-bold mb-4">Available Integrations</h3>
            
            <div className="space-y-4">
              <div className="p-4 border border-blue-600 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">Governance & Board Compliance</h4>
                <p className="text-gray-300 mb-2">APIs for board meetings, governance policies, compliance reports, and board resolutions.</p>
                <div className="flex flex-wrap gap-2">
                  <span className="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                    board meetings
                  </span>
                  <span className="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                    policies
                  </span>
                  <span className="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                    compliance reports
                  </span>
                </div>
              </div>
              
              <div className="p-4 border border-blue-600 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">Security</h4>
                <p className="text-gray-300 mb-2">APIs for vulnerabilities, security policies, incidents, and security scanning.</p>
                <div className="flex flex-wrap gap-2">
                  <span className="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                    vulnerabilities
                  </span>
                  <span className="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                    incidents
                  </span>
                  <span className="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                    security scanning
                  </span>
                </div>
              </div>
              
              <div className="p-4 border border-blue-600 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">APIs & Developer Tools</h4>
                <p className="text-gray-300 mb-2">APIs for API catalog, metrics, integration flows, and developer resources.</p>
                <div className="flex flex-wrap gap-2">
                  <span className="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                    api catalog
                  </span>
                  <span className="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                    metrics
                  </span>
                  <span className="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                    integration flows
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-secondary p-6 rounded-lg">
            <h3 className="text-2xl font-bold mb-4">Application Form</h3>
            
            <form>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-gray-300 mb-2" htmlFor="company_name">Company Name</label>
                  <input type="text" id="company_name" className="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" placeholder="Your company name" />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2" htmlFor="website">Website</label>
                  <input type="url" id="website" className="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" placeholder="https://example.com" />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-gray-300 mb-2" htmlFor="contact_name">Contact Name</label>
                  <input type="text" id="contact_name" className="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" placeholder="Your name" />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2" htmlFor="email">Email</label>
                  <input type="email" id="email" className="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" placeholder="<EMAIL>" />
                </div>
              </div>
              
              <div className="mb-4">
                <label className="block text-gray-300 mb-2" htmlFor="integration_interest">Integration Interest</label>
                <select id="integration_interest" className="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white">
                  <option value="">Select an integration category</option>
                  <option value="governance">Governance & Board Compliance</option>
                  <option value="security">Security</option>
                  <option value="apis">APIs & Developer Tools</option>
                  <option value="risk">Risk & Audit</option>
                  <option value="contracts">Contracts & Policy Lifecycle</option>
                  <option value="certifications">Certifications & Accreditation</option>
                </select>
              </div>
              
              <div className="mb-4">
                <label className="block text-gray-300 mb-2" htmlFor="use_case">Use Case Description</label>
                <textarea id="use_case" className="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" rows="4" placeholder="Describe how you plan to use the NovaFuse API Superstore"></textarea>
              </div>
              
              <div className="flex justify-end">
                <button type="submit" className="accent-bg text-white px-6 py-3 rounded font-bold hover:bg-blue-700">
                  Submit Application
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
      
      {/* Feedback Mechanism Section */}
      <div className="bg-secondary p-6 rounded-lg mb-8">
        <h3 className="text-2xl font-bold mb-4 text-center">Feedback & Collaboration</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 border border-blue-600 rounded-lg">
            <h4 className="text-lg font-semibold mb-2">Dedicated Slack Channel</h4>
            <p className="text-gray-300">Join a private Slack channel with direct access to our engineering team for real-time collaboration and support.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <h4 className="text-lg font-semibold mb-2">Bi-Weekly Check-ins</h4>
            <p className="text-gray-300">Regular check-in calls with our product team to provide feedback and discuss upcoming features.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <h4 className="text-lg font-semibold mb-2">Feature Request Portal</h4>
            <p className="text-gray-300">Submit and vote on feature requests through our dedicated early access portal.</p>
          </div>
        </div>
      </div>
      
      {/* FAQ Section */}
      <div className="bg-secondary p-6 rounded-lg">
        <h3 className="text-2xl font-bold mb-4 text-center">Frequently Asked Questions</h3>
        
        <div className="space-y-4">
          <div>
            <h4 className="text-lg font-semibold">How many companies will be accepted into the Early Access Program?</h4>
            <p className="text-gray-300 mt-1">We're limiting the program to 10 companies to ensure we can provide dedicated support to each participant.</p>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold">Is there a cost to join the Early Access Program?</h4>
            <p className="text-gray-300 mt-1">No, the Early Access Program is free. We're looking for partners who can provide valuable feedback and help shape the product.</p>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold">What technical resources are required to participate?</h4>
            <p className="text-gray-300 mt-1">You'll need at least one developer who can work with RESTful APIs and implement the integration. Our team will provide support throughout the process.</p>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold">Can we use the integration in production during the Early Access Program?</h4>
            <p className="text-gray-300 mt-1">Yes, you can use the integration in production, but we recommend starting with non-critical workflows until the platform is fully launched.</p>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold">What happens after the Early Access Program ends?</h4>
            <p className="text-gray-300 mt-1">Early Access participants will automatically transition to Founding Partner status with all associated benefits when the platform launches publicly.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
