/**
 * Mock Security Service
 * 
 * This module provides a mock security service for testing and development.
 */

import { v4 as uuidv4 } from 'uuid';

/**
 * Mock security service
 */
class MockSecurityService {
  constructor() {
    this.users = new Map();
    this.securityLogs = new Map();
    this.twoFactorCodes = new Map();
    
    // Initialize with default data
    this.initializeDefaultData();
  }
  
  /**
   * Initialize default data
   */
  initializeDefaultData() {
    // Default user settings
    const defaultSettings = {
      twoFactorAuthEnabled: false,
      passwordPolicyEnabled: true,
      sessionTimeout: 30, // minutes
      maxLoginAttempts: 5,
      lockoutDuration: 15, // minutes
      requirePasswordChange: 90 // days
    };
    
    // Default 2FA status
    const defaultTwoFactorStatus = {
      isEnrolled: false,
      isVerified: false,
      verificationMethod: null,
      availableMethods: ['app', 'sms', 'email'],
      preferredMethod: null
    };
    
    // Add default user
    this.users.set('default', {
      settings: { ...defaultSettings },
      twoFactorStatus: { ...defaultTwoFactorStatus }
    });
    
    // Add default security log
    this.securityLogs.set('default', [
      {
        id: uuidv4(),
        type: 'login',
        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        userId: 'default',
        details: {
          success: true,
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      },
      {
        id: uuidv4(),
        type: 'password_change',
        timestamp: new Date(Date.now() - 604800000).toISOString(), // 7 days ago
        userId: 'default',
        details: {
          success: true
        }
      }
    ]);
  }
  
  /**
   * Get user security settings
   * 
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User security settings
   */
  async getUserSecuritySettings(userId) {
    // Simulate network delay
    await this.delay(500);
    
    // Get user
    const user = this.users.get(userId) || this.users.get('default');
    
    return { ...user.settings };
  }
  
  /**
   * Update user security settings
   * 
   * @param {string} userId - User ID
   * @param {Object} settings - New settings
   * @returns {Promise<Object>} Updated settings
   */
  async updateUserSecuritySettings(userId, settings) {
    // Simulate network delay
    await this.delay(500);
    
    // Get user
    let user = this.users.get(userId);
    
    // Create user if not exists
    if (!user) {
      user = {
        settings: { ...this.users.get('default').settings },
        twoFactorStatus: { ...this.users.get('default').twoFactorStatus }
      };
      this.users.set(userId, user);
    }
    
    // Update settings
    user.settings = {
      ...user.settings,
      ...settings
    };
    
    return { ...user.settings };
  }
  
  /**
   * Get two-factor authentication status
   * 
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Two-factor authentication status
   */
  async getTwoFactorStatus(userId) {
    // Simulate network delay
    await this.delay(500);
    
    // Get user
    const user = this.users.get(userId) || this.users.get('default');
    
    return { ...user.twoFactorStatus };
  }
  
  /**
   * Enable two-factor authentication
   * 
   * @param {string} userId - User ID
   * @param {string} method - Verification method
   * @returns {Promise<Object>} Enrollment data
   */
  async enableTwoFactorAuth(userId, method) {
    // Simulate network delay
    await this.delay(1000);
    
    // Get user
    let user = this.users.get(userId);
    
    // Create user if not exists
    if (!user) {
      user = {
        settings: { ...this.users.get('default').settings },
        twoFactorStatus: { ...this.users.get('default').twoFactorStatus }
      };
      this.users.set(userId, user);
    }
    
    // Generate enrollment data
    let enrollmentData = {};
    
    if (method === 'app') {
      // Generate secret key and QR code URL
      const secretKey = this.generateRandomString(16);
      const qrCodeUrl = `https://chart.googleapis.com/chart?chs=200x200&chld=M|0&cht=qr&chl=otpauth://totp/NovaFuse:${userId}?secret=${secretKey}&issuer=NovaFuse`;
      
      enrollmentData = {
        secretKey,
        qrCodeUrl
      };
    } else if (method === 'sms' || method === 'email') {
      // Generate verification code
      const verificationCode = this.generateVerificationCode();
      
      // Store code
      this.twoFactorCodes.set(userId, {
        code: verificationCode,
        method,
        expiresAt: Date.now() + 600000 // 10 minutes
      });
      
      enrollmentData = {
        codeSent: true,
        method
      };
    }
    
    // Update user
    user.twoFactorStatus = {
      ...user.twoFactorStatus,
      isEnrolled: true,
      verificationMethod: method,
      preferredMethod: method
    };
    
    user.settings = {
      ...user.settings,
      twoFactorAuthEnabled: true
    };
    
    return enrollmentData;
  }
  
  /**
   * Disable two-factor authentication
   * 
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} Success
   */
  async disableTwoFactorAuth(userId) {
    // Simulate network delay
    await this.delay(500);
    
    // Get user
    const user = this.users.get(userId);
    
    if (!user) {
      return false;
    }
    
    // Update user
    user.twoFactorStatus = {
      ...user.twoFactorStatus,
      isEnrolled: false,
      isVerified: false,
      verificationMethod: null
    };
    
    user.settings = {
      ...user.settings,
      twoFactorAuthEnabled: false
    };
    
    return true;
  }
  
  /**
   * Verify two-factor authentication
   * 
   * @param {string} userId - User ID
   * @param {string} code - Verification code
   * @param {string} method - Verification method
   * @returns {Promise<boolean>} Is valid
   */
  async verifyTwoFactorAuth(userId, code, method) {
    // Simulate network delay
    await this.delay(500);
    
    // Get user
    const user = this.users.get(userId);
    
    if (!user) {
      return false;
    }
    
    // Check if 2FA is enabled
    if (!user.settings.twoFactorAuthEnabled || !user.twoFactorStatus.isEnrolled) {
      return false;
    }
    
    // Verify code
    if (method === 'app') {
      // For app method, any 6-digit code is valid in mock
      return /^\d{6}$/.test(code);
    } else if (method === 'sms' || method === 'email') {
      // Get stored code
      const storedCode = this.twoFactorCodes.get(userId);
      
      if (!storedCode || storedCode.method !== method) {
        return false;
      }
      
      // Check if code is expired
      if (storedCode.expiresAt < Date.now()) {
        return false;
      }
      
      // Check if code matches
      return storedCode.code === code;
    }
    
    return false;
  }
  
  /**
   * Send two-factor authentication code
   * 
   * @param {string} userId - User ID
   * @param {string} method - Verification method
   * @returns {Promise<boolean>} Success
   */
  async sendTwoFactorAuthCode(userId, method) {
    // Simulate network delay
    await this.delay(500);
    
    // Get user
    const user = this.users.get(userId);
    
    if (!user) {
      return false;
    }
    
    // Generate verification code
    const verificationCode = this.generateVerificationCode();
    
    // Store code
    this.twoFactorCodes.set(userId, {
      code: verificationCode,
      method,
      expiresAt: Date.now() + 600000 // 10 minutes
    });
    
    // In a real implementation, this would send the code via SMS or email
    console.log(`Sending ${method} verification code to user ${userId}: ${verificationCode}`);
    
    return true;
  }
  
  /**
   * Change password
   * 
   * @param {string} userId - User ID
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise<boolean>} Success
   */
  async changePassword(userId, currentPassword, newPassword) {
    // Simulate network delay
    await this.delay(500);
    
    // In a real implementation, this would verify the current password
    // and update the password in the database
    
    // Check password strength
    const strengthCheck = this.checkPasswordStrength(newPassword);
    
    if (strengthCheck.score < 3) {
      throw new Error('Password is too weak');
    }
    
    // Add to security log
    this.addSecurityLogEntry(userId, {
      type: 'password_change',
      timestamp: new Date().toISOString(),
      userId,
      details: {
        success: true
      }
    });
    
    return true;
  }
  
  /**
   * Check password strength
   * 
   * @param {string} password - Password to check
   * @returns {Object} Strength check result
   */
  checkPasswordStrength(password) {
    // Simple password strength check
    let score = 0;
    let feedback = {
      warning: '',
      suggestions: []
    };
    
    // Length check
    if (password.length < 8) {
      score = 0;
      feedback.warning = 'Password is too short';
      feedback.suggestions.push('Use at least 8 characters');
    } else {
      score += 1;
    }
    
    // Complexity checks
    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.suggestions.push('Add uppercase letters');
    }
    
    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.suggestions.push('Add lowercase letters');
    }
    
    if (/[0-9]/.test(password)) {
      score += 1;
    } else {
      feedback.suggestions.push('Add numbers');
    }
    
    if (/[^A-Za-z0-9]/.test(password)) {
      score += 1;
    } else {
      feedback.suggestions.push('Add special characters');
    }
    
    // Final score
    if (score < 3) {
      feedback.warning = 'Password is too weak';
    } else if (score < 4) {
      feedback.warning = 'Password could be stronger';
    }
    
    return {
      score,
      feedback
    };
  }
  
  /**
   * Get security log
   * 
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Security log
   */
  async getSecurityLog(userId) {
    // Simulate network delay
    await this.delay(500);
    
    // Get log
    const log = this.securityLogs.get(userId) || this.securityLogs.get('default') || [];
    
    return [...log];
  }
  
  /**
   * Add security log entry
   * 
   * @param {string} userId - User ID
   * @param {Object} entry - Log entry
   * @returns {Promise<boolean>} Success
   */
  async addSecurityLogEntry(userId, entry) {
    // Simulate network delay
    await this.delay(200);
    
    // Get log
    let log = this.securityLogs.get(userId);
    
    // Create log if not exists
    if (!log) {
      log = [];
      this.securityLogs.set(userId, log);
    }
    
    // Add entry with ID
    const entryWithId = {
      ...entry,
      id: uuidv4()
    };
    
    log.unshift(entryWithId);
    
    return true;
  }
  
  /**
   * Generate verification code
   * 
   * @returns {string} Verification code
   */
  generateVerificationCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
  
  /**
   * Generate random string
   * 
   * @param {number} length - String length
   * @returns {string} Random string
   */
  generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }
  
  /**
   * Delay
   * 
   * @param {number} ms - Milliseconds
   * @returns {Promise} Promise
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default MockSecurityService;
