%% Finite Universe Principle Diagram
graph LR
    %% Main Components
    subgraph Finite_Universe["Finite Universe (∂Ψ=0)"]
        A[Conscious Observer] --> B[Measurable Reality]
        B --> C[Information Boundary]
    end
    D[Infinite Potential] -.->|18% Permeability| C
    C -->|82% Containment| B
    
    %% USPTO Compliance
    classDef uspto fill:#fff,stroke:#000,stroke-width:2px
    class A,B,C,D uspto
    
    %% Reference Numbers (800 series)
    A:::reference800
    B:::reference810
    C:::reference820
    D:::reference830
    
    %% Mathematical Annotations
    Math1[∂Ψ = 0]:::math
    Math2[18/82 ≈ φ-1]:::math
    
    %% Flow Annotations
    Flow1[Observer Effect]:::flow
    Flow2[Measurement Collapse]:::flow
    
    %% Legend
    Legend[Finite Universe Principle (FUP) - Boundary Conditions]:::legend
    
    %% Styling
    classDef reference800,reference810,reference820,reference830 fill:none,stroke:none,font-size:8pt
    classDef math fill:#f9f9f9,stroke:#ddd,stroke-dasharray: 5 5,font-size:8pt
    classDef flow fill:#f0f8ff,stroke:#4682b4,stroke-width:1px,font-size:8pt
    classDef legend fill:#f0f0f0,stroke:#ccc,stroke-width:1px
    class A,B,C,D,Finite_Universe uspto
    
    %% Reference Numbers
    A:::reference100
    B:::reference200
    C:::reference300
    
    %% Mathematical Annotations
    BoundaryEquation[∂Ψ/∂t = ∇·(TEE × UUFT)]:::math
    PermeabilityRatio[18.000...% (φ-derived)]:::math
    
    %% Legend
    Legend["Finite Universe Principle (FUP) Diagram"]:::legend
    
    %% Hidden elements for layout
    classDef reference100 fill:none,stroke:none,font-size:8pt
    classDef reference200 fill:none,stroke:none,font-size:8pt
    classDef reference300 fill:none,stroke:none,font-size:8pt
    classDef math fill:#f9f9f9,stroke:#ddd,stroke-dasharray: 5 5
    classDef legend fill:#f0f0f0,stroke:#ccc,stroke-width:1px
