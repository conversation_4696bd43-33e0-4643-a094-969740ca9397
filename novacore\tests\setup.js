/**
 * Test Setup
 * 
 * This file is loaded before running tests to set up the test environment.
 */

// Set environment to test
process.env.NODE_ENV = 'test';

// Set up global variables
global.expect = require('chai').expect;
global.sinon = require('sinon');

// Silence console logs during tests
console.log = () => {};
console.info = () => {};
console.warn = () => {};
console.error = () => {};

// Reset console logs after tests
after(() => {
  console.log = console.info = console.warn = console.error = require('console').log;
});
