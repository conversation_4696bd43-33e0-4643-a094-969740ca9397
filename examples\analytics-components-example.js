/**
 * Analytics Components Example
 *
 * This example demonstrates how to use the analytics components
 * for the Finite Universe Principle defense system, including
 * trend analysis, pattern detection, and anomaly classification.
 */

const {
  // Complete defense system
  createCompleteDefenseSystem,
  
  // Analytics components
  createTrendAnalyzer,
  createPatternDetector,
  createAnomalyClassifier,
  createAnalyticsComponents,
  createAnalyticsDashboard
} = require('../src/quantum/finite-universe-principle');

/**
 * Example 1: Trend Analysis
 * 
 * This example demonstrates how to use the trend analyzer component.
 */
async function example1() {
  console.log('\n=== Example 1: Trend Analysis ===\n');

  // Create trend analyzer
  const trendAnalyzer = createTrendAnalyzer({
    enableLogging: true,
    historyLength: 100,
    analysisInterval: 10000, // 10 seconds
    trendThreshold: 0.1,
    correlationThreshold: 0.7
  });

  // Register event listeners
  trendAnalyzer.on('analysis', (data) => {
    console.log('Analysis completed:', {
      boundaryViolationsTrend: data.trendAnalysis.boundaryViolations.trend,
      validationFailuresTrend: data.trendAnalysis.validationFailures.trend
    });
  });

  trendAnalyzer.on('significant-trend', (data) => {
    console.log('Significant trend detected:', data);
  });

  trendAnalyzer.on('significant-correlation', (data) => {
    console.log('Significant correlation detected:', data);
  });

  trendAnalyzer.on('seasonality-detected', (data) => {
    console.log('Seasonality detected:', data);
  });

  // Start trend analyzer
  trendAnalyzer.start();

  // Process some metrics
  console.log('\nProcessing metrics:');
  
  // Generate sample metrics
  const metrics1 = {
    boundaryViolations: 5,
    validationFailures: 2,
    domainMetrics: {
      cyber: {
        boundaryViolations: 3,
        averageSecurityScore: 7.5
      },
      financial: {
        boundaryViolations: 1,
        averageInterestRate: 0.05
      },
      medical: {
        boundaryViolations: 1,
        averageHeartRate: 75
      }
    }
  };
  
  const metrics2 = {
    boundaryViolations: 7,
    validationFailures: 3,
    domainMetrics: {
      cyber: {
        boundaryViolations: 4,
        averageSecurityScore: 7.2
      },
      financial: {
        boundaryViolations: 2,
        averageInterestRate: 0.06
      },
      medical: {
        boundaryViolations: 1,
        averageHeartRate: 76
      }
    }
  };
  
  const metrics3 = {
    boundaryViolations: 10,
    validationFailures: 5,
    domainMetrics: {
      cyber: {
        boundaryViolations: 6,
        averageSecurityScore: 6.8
      },
      financial: {
        boundaryViolations: 3,
        averageInterestRate: 0.07
      },
      medical: {
        boundaryViolations: 1,
        averageHeartRate: 77
      }
    }
  };
  
  // Process metrics
  const result1 = trendAnalyzer.processMetrics(metrics1);
  console.log('Result 1:', {
    boundaryViolationsTrend: result1.trendAnalysis.boundaryViolations.trend,
    validationFailuresTrend: result1.trendAnalysis.validationFailures.trend
  });
  
  // Wait a bit
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const result2 = trendAnalyzer.processMetrics(metrics2);
  console.log('Result 2:', {
    boundaryViolationsTrend: result2.trendAnalysis.boundaryViolations.trend,
    validationFailuresTrend: result2.trendAnalysis.validationFailures.trend
  });
  
  // Wait a bit
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const result3 = trendAnalyzer.processMetrics(metrics3);
  console.log('Result 3:', {
    boundaryViolationsTrend: result3.trendAnalysis.boundaryViolations.trend,
    validationFailuresTrend: result3.trendAnalysis.validationFailures.trend
  });
  
  // Get trend analysis
  console.log('\nTrend analysis:', trendAnalyzer.getTrendAnalysis());
  
  // Get correlation analysis
  console.log('\nCorrelation analysis:', trendAnalyzer.getCorrelationAnalysis());

  // Dispose trend analyzer
  trendAnalyzer.dispose();
}

/**
 * Example 2: Pattern Detection
 * 
 * This example demonstrates how to use the pattern detector component.
 */
async function example2() {
  console.log('\n=== Example 2: Pattern Detection ===\n');

  // Create pattern detector
  const patternDetector = createPatternDetector({
    enableLogging: true,
    historyLength: 100,
    analysisInterval: 10000, // 10 seconds
    patternThreshold: 0.8,
    minPatternLength: 3,
    maxPatternLength: 10
  });

  // Register event listeners
  patternDetector.on('detection', (data) => {
    console.log('Pattern detection completed');
  });

  patternDetector.on('pattern-detected', (data) => {
    console.log('Pattern detected:', data.metric);
  });

  // Start pattern detector
  patternDetector.start();

  // Process some metrics
  console.log('\nProcessing metrics with patterns:');
  
  // Generate metrics with patterns
  const patternMetrics = [];
  
  // Create a repeating pattern for boundary violations
  const pattern = [3, 5, 8, 5, 3];
  
  for (let i = 0; i < 3; i++) {
    for (const value of pattern) {
      patternMetrics.push({
        boundaryViolations: value,
        validationFailures: Math.floor(value / 2),
        domainMetrics: {
          cyber: {
            boundaryViolations: Math.floor(value * 0.6)
          },
          financial: {
            boundaryViolations: Math.floor(value * 0.3)
          },
          medical: {
            boundaryViolations: Math.floor(value * 0.1)
          }
        }
      });
    }
  }
  
  // Process metrics
  for (let i = 0; i < patternMetrics.length; i++) {
    const result = patternDetector.processMetrics(patternMetrics[i]);
    
    // Wait a bit between processing
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  // Get pattern detection results
  console.log('\nPattern detection results:', patternDetector.getPatternDetection());

  // Dispose pattern detector
  patternDetector.dispose();
}

/**
 * Example 3: Anomaly Classification
 * 
 * This example demonstrates how to use the anomaly classifier component.
 */
async function example3() {
  console.log('\n=== Example 3: Anomaly Classification ===\n');

  // Create anomaly classifier
  const anomalyClassifier = createAnomalyClassifier({
    enableLogging: true,
    classificationInterval: 10000, // 10 seconds
    severityLevels: ['low', 'medium', 'high', 'critical'],
    severityThresholds: [3, 5, 8]
  });

  // Register event listeners
  anomalyClassifier.on('classification', (data) => {
    console.log('Anomaly classified:', {
      category: data.category,
      severity: data.severity,
      domain: data.domain
    });
  });

  anomalyClassifier.on('classification-complete', (data) => {
    console.log('Classification completed');
  });

  // Start anomaly classifier
  anomalyClassifier.start();

  // Process some anomalies
  console.log('\nProcessing anomalies:');
  
  // Create sample anomalies
  const anomalies = [
    {
      value: 15,
      mean: 5,
      stdDev: 2,
      zScore: 5,
      domain: 'cyber'
    },
    {
      value: 0,
      mean: 10,
      stdDev: 2,
      zScore: 5,
      domain: 'financial'
    },
    {
      boundaryViolation: true,
      zScore: 10,
      domain: 'medical'
    },
    {
      trend: true,
      zScore: 4,
      domain: 'cyber'
    },
    {
      crossDomain: true,
      zScore: 7,
      domain: 'financial'
    }
  ];
  
  // Process anomalies
  for (const anomaly of anomalies) {
    const result = anomalyClassifier.processAnomaly(anomaly);
    console.log('Classification result:', {
      category: result.category,
      severity: result.severity,
      domain: result.domain
    });
    
    // Wait a bit between processing
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // Classify all anomalies
  anomalyClassifier.classifyAnomalies();
  
  // Get classification results
  console.log('\nClassification results:', anomalyClassifier.getClassificationResults());

  // Dispose anomaly classifier
  anomalyClassifier.dispose();
}

/**
 * Example 4: Integrated Analytics Components
 * 
 * This example demonstrates how to use all analytics components together.
 */
async function example4() {
  console.log('\n=== Example 4: Integrated Analytics Components ===\n');

  // Create analytics components
  const {
    trendAnalyzer,
    patternDetector,
    anomalyClassifier
  } = createAnalyticsComponents({
    enableLogging: true
  });

  // Start components
  trendAnalyzer.start();
  patternDetector.start();
  anomalyClassifier.start();

  // Process metrics through all components
  console.log('\nProcessing metrics through all components:');
  
  // Generate sample metrics
  const metrics = {
    boundaryViolations: 12, // Anomalously high
    validationFailures: 5,
    domainMetrics: {
      cyber: {
        boundaryViolations: 8,
        averageSecurityScore: 6.5
      },
      financial: {
        boundaryViolations: 3,
        averageInterestRate: 0.08
      },
      medical: {
        boundaryViolations: 1,
        averageHeartRate: 78
      }
    }
  };
  
  // Process metrics through trend analyzer
  const trendResults = trendAnalyzer.processMetrics(metrics);
  console.log('Trend results:', {
    boundaryViolationsTrend: trendResults.trendAnalysis.boundaryViolations.trend
  });
  
  // Process metrics through pattern detector
  const patternResults = patternDetector.processMetrics(metrics);
  console.log('Pattern results:', {
    boundaryViolationsPatterns: patternResults.boundaryViolations?.patterns?.length || 0
  });
  
  // Create anomaly from metrics
  const anomaly = {
    value: metrics.boundaryViolations,
    mean: 5,
    stdDev: 2,
    zScore: (metrics.boundaryViolations - 5) / 2,
    domain: 'cyber'
  };
  
  // Process anomaly through classifier
  const classificationResult = anomalyClassifier.processAnomaly(anomaly);
  console.log('Classification result:', {
    category: classificationResult.category,
    severity: classificationResult.severity
  });

  // Dispose components
  trendAnalyzer.dispose();
  patternDetector.dispose();
  anomalyClassifier.dispose();
}

/**
 * Example 5: Analytics Dashboard
 * 
 * This example demonstrates how to use the analytics dashboard.
 */
async function example5() {
  console.log('\n=== Example 5: Analytics Dashboard ===\n');

  // Create complete defense system
  const defenseSystem = createCompleteDefenseSystem({
    enableLogging: false, // Disable logging for cleaner output
    enableMonitoring: true,
    strictMode: true
  });

  // Create analytics components
  const analyticsComponents = createAnalyticsComponents({
    enableLogging: true
  });

  // Create analytics dashboard
  const analyticsDashboard = createAnalyticsDashboard({
    enableLogging: true,
    port: 3002,
    updateInterval: 5000,
    enableRealTimeUpdates: true,
    ...analyticsComponents,
    monitoringDashboard: defenseSystem.monitoringDashboard
  });

  // Start analytics dashboard
  await analyticsDashboard.start();
  console.log(`Analytics dashboard started on http://localhost:${analyticsDashboard.options.port}`);

  // Generate test data
  console.log('\nGenerating test data...');
  
  // Generate data at regular intervals
  const dataInterval = setInterval(async () => {
    // Generate random metrics
    const metrics = {
      boundaryViolations: Math.floor(Math.random() * 10),
      validationFailures: Math.floor(Math.random() * 5),
      domainMetrics: {
        cyber: {
          boundaryViolations: Math.floor(Math.random() * 6),
          averageSecurityScore: 7 + Math.random() * 2
        },
        financial: {
          boundaryViolations: Math.floor(Math.random() * 4),
          averageInterestRate: 0.05 + Math.random() * 0.05
        },
        medical: {
          boundaryViolations: Math.floor(Math.random() * 3),
          averageHeartRate: 70 + Math.random() * 10
        }
      }
    };
    
    // Process data through defense system
    await defenseSystem.processData(metrics);
    
    // Process metrics through analytics dashboard
    analyticsDashboard.processMetrics(metrics);
    
    // Occasionally generate anomalies
    if (Math.random() < 0.3) {
      const anomaly = {
        value: 15 + Math.floor(Math.random() * 10),
        mean: 5,
        stdDev: 2,
        zScore: 5 + Math.random() * 5,
        domain: ['cyber', 'financial', 'medical'][Math.floor(Math.random() * 3)]
      };
      
      analyticsDashboard.processAnomaly(anomaly);
    }
  }, 2000);

  // Keep running for 30 seconds
  await new Promise(resolve => setTimeout(resolve, 30000));
  
  // Clean up
  clearInterval(dataInterval);
  analyticsDashboard.dispose();
  defenseSystem.dispose();
}

/**
 * Run all examples
 */
async function runAllExamples() {
  await example1();
  await example2();
  await example3();
  await example4();
  await example5();
}

// Run all examples
runAllExamples().catch(error => {
  console.error('Error running examples:', error);
});
