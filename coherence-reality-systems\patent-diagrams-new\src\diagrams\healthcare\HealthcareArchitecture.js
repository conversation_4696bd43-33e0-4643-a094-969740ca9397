import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText
} from '../../components/DiagramComponents';

const HealthcareArchitecture = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>HEALTHCARE SYSTEM ARCHITECTURE</ContainerLabel>
      </ContainerBox>
      
      {/* Core Components */}
      <ContainerBox width="300px" height="150px" left="250px" top="70px">
        <ContainerLabel>CYBER-SAFETY PROTOCOL CORE</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="270px" top="120px" width="120px">
        <ComponentNumber>301</ComponentNumber>
        <ComponentLabel>Native Unification</ComponentLabel>
        Engine
      </ComponentBox>
      
      <ComponentBox left="410px" top="120px" width="120px">
        <ComponentNumber>302</ComponentNumber>
        <ComponentLabel>Dynamic UI</ComponentLabel>
        Enforcement
      </ComponentBox>
      
      {/* Healthcare Extensions */}
      <ContainerBox width="200px" height="120px" left="100px" top="250px">
        <ContainerLabel>PHI PROTECTION</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="135px" top="290px" width="130px">
        <ComponentNumber>303</ComponentNumber>
        <ComponentLabel>Zero-Persistence</ComponentLabel>
        PHI Processing
      </ComponentBox>
      
      <ContainerBox width="200px" height="120px" left="500px" top="250px">
        <ContainerLabel>COMPLIANCE ENFORCEMENT</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="535px" top="290px" width="130px">
        <ComponentNumber>304</ComponentNumber>
        <ComponentLabel>HIPAA</ComponentLabel>
        Enforcement
      </ComponentBox>
      
      {/* Integration Layer */}
      <ContainerBox width="200px" height="120px" left="300px" top="250px">
        <ContainerLabel>DEVICE SECURITY</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="335px" top="290px" width="130px">
        <ComponentNumber>305</ComponentNumber>
        <ComponentLabel>Medical Device</ComponentLabel>
        Security Framework
      </ComponentBox>
      
      {/* Arrows connecting components */}
      <Arrow left="200px" top="290px" width="135px" />
      <Arrow left="465px" top="290px" width="70px" />
      
      <CurvedArrow width="300" height="150" left="335" top="170">
        <path
          d="M 0,0 Q 0,80 -150,120"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="-150,120 -140,112 -143,122"
          fill="#333"
        />
      </CurvedArrow>
      
      <CurvedArrow width="300" height="150" left="465" top="170">
        <path
          d="M 0,0 Q 0,80 150,120"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="150,120 140,112 143,122"
          fill="#333"
        />
      </CurvedArrow>
      
      {/* Novel Elements */}
      <ComponentBox left="80px" top="400px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>306</ComponentNumber>
        <ComponentLabel>Blockchain-Anchored</ComponentLabel>
        PHI Disclosure
      </ComponentBox>
      
      <ComponentBox left="240px" top="400px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>307</ComponentNumber>
        <ComponentLabel>Break-Glass Access</ComponentLabel>
        Protocol
      </ComponentBox>
      
      <ComponentBox left="400px" top="400px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>308</ComponentNumber>
        <ComponentLabel>Self-Destructing</ComponentLabel>
        Medical Sessions
      </ComponentBox>
      
      <ComponentBox left="560px" top="400px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>309</ComponentNumber>
        <ComponentLabel>Dynamic UI</ComponentLabel>
        Consent
      </ComponentBox>
      
      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Core Components</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Healthcare Extensions</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Novel Elements</LegendText>
        </LegendItem>
      </DiagramLegend>
    </DiagramFrame>
  );
};

export default HealthcareArchitecture;
