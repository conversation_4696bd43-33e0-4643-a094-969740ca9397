// API: Exposes endpoints for NovaAscend system management

const express = require('express');
const NovaCortex = require('../core/NovaCortex');
const NovaLift = require('../core/NovaLift');
const NovaCaia = require('../core/NovaCaia');
const CoherenceStateAdapter = require('../adapters/CoherenceStateAdapter');
const PerformanceEthicsBridge = require('../adapters/PerformanceEthicsBridge');
const TelemetryStreamNormalizer = require('../adapters/TelemetryStreamNormalizer');
const app = express();
app.use(express.json());

// Load core module instances
const cortex = NovaCortex.load();
const coherenceAdapter = new CoherenceStateAdapter();
const lift = NovaLift.connect(cortex, coherenceAdapter);
const caia = NovaCaia.load();
const performanceBridge = new PerformanceEthicsBridge();
const telemetryNormalizer = new TelemetryStreamNormalizer();

// POST /decree: Send commands to NovaCortex
app.post('/decree', (req, res) => {
  const { command, target } = req.body;
  if (command === 'align' && target) {
    const state = cortex.align(target);
    res.json({ status: 'aligned', state });
  } else {
    res.status(400).json({ error: 'Invalid command or target' });
  }
});

// GET /vision: Fetch insights from NovaCortex
app.get('/vision', (req, res) => {
  const vision = cortex.getVision();
  res.json(vision);
});

// POST /lift/scale: Scale power using NovaLift
app.post('/lift/scale', (req, res) => {
  const { level } = req.body;
  if (level) {
    const result = lift.scalePower(level);
    res.json(result);
  } else {
    res.status(400).json({ error: 'Missing level parameter' });
  }
});

// GET /lift/status: Get NovaLift status
app.get('/lift/status', (req, res) => {
  res.json(lift.getStatus());
});

// PUT /firewall: Update policy using NovaCaia
app.put('/firewall', (req, res) => {
  const { newLaw } = req.body;
  if (newLaw) {
    const policies = caia.updatePolicy(newLaw);
    res.json({ status: 'policy updated', policies });
  } else {
    res.status(400).json({ error: 'Missing newLaw parameter' });
  }
});

// GET /caia/status: Get NovaCaia status
app.get('/caia/status', (req, res) => {
  res.json(caia.getStatus());
});

// POST /coherence/check: Check system coherence using CoherenceStateAdapter
app.post('/coherence/check', async (req, res) => {
  const { coherenceScore } = req.body;
  if (typeof coherenceScore !== 'number') {
    return res.status(400).json({ error: 'Missing or invalid coherenceScore' });
  }
  const result = await coherenceAdapter.check(coherenceScore);
  res.json({ coherence: result });
});

// POST /performance/evaluate: Evaluate performance vs. compliance
app.post('/performance/evaluate', (req, res) => {
  const { performance, compliance } = req.body;
  if (typeof performance !== 'number' || typeof compliance !== 'number') {
    return res.status(400).json({ error: 'Missing or invalid performance/compliance' });
  }
  const result = performanceBridge.evaluate(performance, compliance);
  res.json({ meetsStandard: result });
});

// POST /telemetry/normalize: Normalize telemetry data
app.post('/telemetry/normalize', (req, res) => {
  const { data } = req.body;
  if (typeof data === 'undefined') {
    return res.status(400).json({ error: 'Missing data' });
  }
  const normalized = telemetryNormalizer.normalize(data);
  res.json({ normalized });
});

// DELETE /sin: Purge corruption (stub)
app.delete('/sin', (req, res) => {
  // Placeholder for purge logic
  res.json({ status: 'corruption purged', entropyThreshold: req.query.entropyThreshold });
});

module.exports = app;
