/**
 * NovaCore - Core API for NovaFuse Cyber-Safety Platform
 * 
 * This is the main entry point for the NovaCore API.
 * NovaCore provides the core functionality for the NovaFuse Cyber-Safety Platform.
 */

const express = require('express');
const app = express();
const config = require('./config');
const logger = require('./config/logger');
const errorHandler = require('./api/middleware/errorHandler');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const mongoose = require('mongoose');

// Import modules
const novaflow = require('./modules/novaflow');
const novapulse = require('./modules/novapulse');

// Configure middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());
app.use(helmet());
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));

// Connect to MongoDB
mongoose.connect(config.mongodb.uri, config.mongodb.options)
  .then(() => {
    logger.info('Connected to MongoDB');
  })
  .catch(err => {
    logger.error('MongoDB connection error', { error: err });
    process.exit(1);
  });

// API routes
app.use('/api/v1/novaflow', novaflow.routes);
app.use('/api/v1/novapulse', novapulse.routes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use(errorHandler);

// Export app for testing
module.exports = app;

// Start server if not imported
if (require.main === module) {
  const server = app.listen(config.port, () => {
    logger.info(`NovaCore API server started on port ${config.port}`);
  });
  
  // Handle graceful shutdown
  process.on('SIGTERM', () => {
    logger.info('SIGTERM signal received: closing HTTP server');
    server.close(() => {
      logger.info('HTTP server closed');
      mongoose.connection.close(false, () => {
        logger.info('MongoDB connection closed');
        process.exit(0);
      });
    });
  });
}
