# Script to create properly spaced versions of all patent diagram HTML files
# This script creates new files with proper spacing between sections

# List of specific patent diagram files
$patentDiagrams = @(
    "cyber-safety-architecture-fixed.html",
    "unified-field-theory.html",
    "alignment-architecture-final.html",
    "novafuse-components.html",
    "hardware-software-implementation.html",
    "detailed-data-flow.html",
    "18-82-principle.html",
    "cyber-safety-incident-response.html",
    "adaptive-compliance-process.html",
    "comphyology-universal-application-framework.html",
    "financial-services-architecture.html",
    "healthcare-architecture.html",
    "visualization-output-examples.html",
    "comphyology-mathematical-framework.html",
    "uuft-networked-systems.html"
)

# Base CSS template
$baseCSS = @"
        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }

        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 950px;
            min-height: 900px; /* Increased minimum height */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: visible; /* Allow content to be fully visible */
        }

        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: visible; /* Allow content to be fully visible */
            min-height: 100px; /* Minimum height */
            min-width: 200px; /* Minimum width */
        }

        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            z-index: 10; /* Ensure labels are in front of connecting lines */
            background-color: white; /* Add background to hide lines behind text */
            padding: 0 5px; /* Add padding around text */
        }

        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically */
            align-items: center;
            text-align: center;
            padding: 5px; /* Reduced padding */
            overflow: visible; /* Allow content to be fully visible */
            border-top-left-radius: 0; /* Flat corner for number */
            min-height: 50px; /* Minimum height */
            min-width: 100px; /* Minimum width */
        }

        /* Component Number Styles - Integrated into corner */
        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 0 0 6px 0; /* Rounded on bottom-right corner only */
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px; /* Smaller font */
            font-weight: bold;
            z-index: 2;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }

        .component-label {
            font-weight: bold;
            margin-top: 10px; /* Reduced margin */
            margin-bottom: 2px; /* Reduced margin */
            font-size: 12px; /* Smaller font */
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }

        .component-content {
            font-size: 10px; /* Smaller font */
            text-align: center;
            width: 100%;
            padding: 0;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            line-height: 1.2; /* Tighter line spacing */
        }

        /* Arrow Styles */
        .arrow {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            width: 2px;
            z-index: 0;
        }

        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }

        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }

        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }

        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }
"@

# Process each patent diagram file
foreach ($fileName in $patentDiagrams) {
    $filePath = Join-Path -Path "D:\novafuse-api-superstore\patent-diagrams-new" -ChildPath $fileName
    $newFilePath = $filePath -replace ".html", "-spaced.html"

    # Check if file exists
    if (Test-Path $filePath) {
        Write-Host "Processing $fileName..."

        # Read the file content
        $content = Get-Content -Path $filePath -Raw

        # Extract the body content
        if ($content -match "(?s)<body>(.*?)</body>") {
            $bodyContent = $Matches[1]

            # Extract the title
            if ($content -match "<title>(.*?)</title>") {
                $title = $Matches[1]
            } else {
                $title = "Patent Diagram"
            }

            # Fix container box heights and positions
            $bodyContent = $bodyContent -replace 'height: 580px', 'height: 800px'

            # Adjust positions of sections to add more space
            $bodyContent = $bodyContent -replace 'top: 220px', 'top: 230px'
            $bodyContent = $bodyContent -replace 'top: 260px', 'top: 270px'
            $bodyContent = $bodyContent -replace 'top: 330px', 'top: 340px'
            $bodyContent = $bodyContent -replace 'top: 420px', 'top: 440px'
            $bodyContent = $bodyContent -replace 'top: 460px', 'top: 480px'
            $bodyContent = $bodyContent -replace 'top: 550px', 'top: 600px'
            $bodyContent = $bodyContent -replace 'top: 570px', 'top: 620px'

            # Adjust arrow heights
            $bodyContent = $bodyContent -replace 'top: 190px; height: 70px', 'top: 200px; height: 70px'
            $bodyContent = $bodyContent -replace 'top: 400px; height: 60px', 'top: 410px; height: 70px'

            # Make principles bold, larger, and in front of lines
            $bodyContent = $bodyContent -replace 'font-size: 11px; text-align: center; width: 120px; padding-top: 5px;', 'font-size: 16px; text-align: center; width: 120px; padding-top: 5px; font-weight: bold; z-index: 10; background-color: white; padding: 0 5px;'

            # Convert principles to all caps
            $bodyContent = $bodyContent -replace '>Truth<', '>TRUTH<'
            $bodyContent = $bodyContent -replace '>Trust<', '>TRUST<'
            $bodyContent = $bodyContent -replace '>Transparency<', '>TRANSPARENCY<'
            $bodyContent = $bodyContent -replace '>Tensorial Governance<', '>TENSORIAL GOVERNANCE<'

            # Create new clean HTML file
            $newContent = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$title</title>
    <style>
$baseCSS
    </style>
</head>
<body>
$bodyContent
</body>
</html>
"@

            # Write the new content to the file
            Set-Content -Path $newFilePath -Value $newContent

            Write-Host "Created spaced version: $newFilePath"
        } else {
            Write-Host "Could not extract body content from $fileName"
        }
    } else {
        Write-Host "File not found: $fileName"
    }
}

Write-Host "All spaced patent diagram files have been created."
