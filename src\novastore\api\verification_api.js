/**
 * NovaStore Component Verification API
 * 
 * This module provides an API for verifying components in the NovaStore marketplace
 * using the Adaptive Trinity CSDE with Data Quality Framework.
 */

const express = require('express');
const NovaStoreTrinityIntegration = require('../trinity_csde_integration');

/**
 * Create a NovaStore verification API
 * @param {Object} options - Configuration options
 * @returns {Object} - Express router
 */
function createVerificationAPI(options = {}) {
  const router = express.Router();
  
  // Initialize NovaStore Trinity Integration
  const trinityIntegration = new NovaStoreTrinityIntegration(options);
  
  /**
   * Verify a component
   * POST /api/verify
   */
  router.post('/verify', async (req, res) => {
    try {
      const { component, level } = req.body;
      
      if (!component) {
        return res.status(400).json({
          error: 'Component data is required'
        });
      }
      
      const verificationResult = await trinityIntegration.verifyComponent(component, level);
      
      return res.status(200).json(verificationResult);
    } catch (error) {
      console.error(`Error verifying component: ${error.message}`);
      return res.status(500).json({
        error: 'Error verifying component',
        message: error.message
      });
    }
  });
  
  /**
   * Verify multiple components
   * POST /api/verify-batch
   */
  router.post('/verify-batch', async (req, res) => {
    try {
      const { components, level } = req.body;
      
      if (!components || !Array.isArray(components)) {
        return res.status(400).json({
          error: 'Components array is required'
        });
      }
      
      const verificationResults = await trinityIntegration.verifyComponents(components, level);
      
      return res.status(200).json(verificationResults);
    } catch (error) {
      console.error(`Error verifying components: ${error.message}`);
      return res.status(500).json({
        error: 'Error verifying components',
        message: error.message
      });
    }
  });
  
  /**
   * Get verification metrics
   * GET /api/metrics
   */
  router.get('/metrics', (req, res) => {
    try {
      const metrics = trinityIntegration.getVerificationMetrics();
      return res.status(200).json(metrics);
    } catch (error) {
      console.error(`Error getting metrics: ${error.message}`);
      return res.status(500).json({
        error: 'Error getting metrics',
        message: error.message
      });
    }
  });
  
  /**
   * Get adaptive ratios
   * GET /api/adaptive-ratios
   */
  router.get('/adaptive-ratios', (req, res) => {
    try {
      const ratios = trinityIntegration.getAdaptiveRatios();
      return res.status(200).json(ratios);
    } catch (error) {
      console.error(`Error getting adaptive ratios: ${error.message}`);
      return res.status(500).json({
        error: 'Error getting adaptive ratios',
        message: error.message
      });
    }
  });
  
  /**
   * Get performance metrics
   * GET /api/performance
   */
  router.get('/performance', (req, res) => {
    try {
      const performance = trinityIntegration.getPerformanceMetrics();
      return res.status(200).json(performance);
    } catch (error) {
      console.error(`Error getting performance metrics: ${error.message}`);
      return res.status(500).json({
        error: 'Error getting performance metrics',
        message: error.message
      });
    }
  });
  
  return router;
}

module.exports = createVerificationAPI;
