/**
 * VisualizationPerformanceService
 * 
 * This service provides methods for optimizing visualization performance.
 */

class VisualizationPerformanceService {
  constructor() {
    this.performanceStats = {
      fps: 0,
      renderTime: 0,
      triangleCount: 0,
      drawCalls: 0,
      memoryUsage: 0
    };
    
    this.performanceHistory = [];
    this.maxHistoryLength = 100;
    this.isMonitoring = false;
    this.monitoringInterval = null;
    this.lastFrameTime = 0;
    this.frameCount = 0;
    this.frameTimeSum = 0;
    this.fpsUpdateInterval = 1000; // 1 second
    this.lastFpsUpdate = 0;
  }
  
  /**
   * Start performance monitoring
   * @param {THREE.WebGLRenderer} renderer - Three.js renderer
   * @param {THREE.Scene} scene - Three.js scene
   * @param {number} [interval=1000] - Monitoring interval in milliseconds
   * @returns {Object} - Performance monitoring control
   */
  startMonitoring(renderer, scene, interval = 1000) {
    if (this.isMonitoring) {
      return;
    }
    
    this.isMonitoring = true;
    this.lastFrameTime = performance.now();
    this.frameCount = 0;
    this.frameTimeSum = 0;
    this.lastFpsUpdate = this.lastFrameTime;
    
    // Create performance monitoring control
    const monitoringControl = {
      stop: () => this.stopMonitoring(),
      getStats: () => this.getPerformanceStats(),
      getHistory: () => this.getPerformanceHistory(),
      clearHistory: () => this.clearPerformanceHistory()
    };
    
    // Start monitoring interval
    this.monitoringInterval = setInterval(() => {
      this.updatePerformanceStats(renderer, scene);
    }, interval);
    
    return monitoringControl;
  }
  
  /**
   * Stop performance monitoring
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }
    
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }
  
  /**
   * Update performance stats
   * @param {THREE.WebGLRenderer} renderer - Three.js renderer
   * @param {THREE.Scene} scene - Three.js scene
   * @private
   */
  updatePerformanceStats(renderer, scene) {
    if (!renderer || !scene) {
      return;
    }
    
    // Get renderer info
    const info = renderer.info;
    
    // Update triangle count and draw calls
    this.performanceStats.triangleCount = info.render?.triangles || 0;
    this.performanceStats.drawCalls = info.render?.calls || 0;
    
    // Update memory usage
    this.performanceStats.memoryUsage = (
      (info.memory?.geometries || 0) +
      (info.memory?.textures || 0)
    );
    
    // Add to history
    this.addToPerformanceHistory({
      ...this.performanceStats,
      timestamp: Date.now()
    });
  }
  
  /**
   * Update FPS counter
   * @param {number} timestamp - Current timestamp
   */
  updateFps(timestamp) {
    if (!this.isMonitoring) {
      return;
    }
    
    // Calculate frame time
    const frameTime = timestamp - this.lastFrameTime;
    this.lastFrameTime = timestamp;
    
    // Update frame count and time sum
    this.frameCount++;
    this.frameTimeSum += frameTime;
    
    // Update FPS every second
    if (timestamp - this.lastFpsUpdate >= this.fpsUpdateInterval) {
      // Calculate FPS and average render time
      this.performanceStats.fps = Math.round(this.frameCount * 1000 / (timestamp - this.lastFpsUpdate));
      this.performanceStats.renderTime = this.frameTimeSum / this.frameCount;
      
      // Reset counters
      this.frameCount = 0;
      this.frameTimeSum = 0;
      this.lastFpsUpdate = timestamp;
    }
  }
  
  /**
   * Add performance stats to history
   * @param {Object} stats - Performance stats
   * @private
   */
  addToPerformanceHistory(stats) {
    this.performanceHistory.push(stats);
    
    // Limit history length
    if (this.performanceHistory.length > this.maxHistoryLength) {
      this.performanceHistory.shift();
    }
  }
  
  /**
   * Get current performance stats
   * @returns {Object} - Performance stats
   */
  getPerformanceStats() {
    return { ...this.performanceStats };
  }
  
  /**
   * Get performance history
   * @returns {Array} - Performance history
   */
  getPerformanceHistory() {
    return [...this.performanceHistory];
  }
  
  /**
   * Clear performance history
   */
  clearPerformanceHistory() {
    this.performanceHistory = [];
  }
  
  /**
   * Optimize scene for performance
   * @param {THREE.Scene} scene - Three.js scene
   * @param {Object} options - Optimization options
   */
  optimizeScene(scene, options = {}) {
    if (!scene) {
      return;
    }
    
    const {
      levelOfDetail = 'auto',
      frustumCulling = true,
      instancedMeshes = true,
      mergeGeometries = true,
      simplifyGeometries = true,
      maxTriangles = 100000
    } = options;
    
    // Enable frustum culling
    if (frustumCulling) {
      scene.traverse((object) => {
        if (object.isMesh) {
          object.frustumCulled = true;
        }
      });
    }
    
    // Simplify geometries
    if (simplifyGeometries) {
      let totalTriangles = 0;
      
      // Count total triangles
      scene.traverse((object) => {
        if (object.isMesh && object.geometry) {
          if (object.geometry.index) {
            totalTriangles += object.geometry.index.count / 3;
          } else if (object.geometry.attributes.position) {
            totalTriangles += object.geometry.attributes.position.count / 3;
          }
        }
      });
      
      // Simplify if too many triangles
      if (totalTriangles > maxTriangles) {
        const simplificationRatio = maxTriangles / totalTriangles;
        
        scene.traverse((object) => {
          if (object.isMesh && object.geometry) {
            this.simplifyGeometry(object, simplificationRatio);
          }
        });
      }
    }
    
    // Use instanced meshes for repeated objects
    if (instancedMeshes) {
      this.convertToInstancedMeshes(scene);
    }
    
    // Merge geometries
    if (mergeGeometries) {
      this.mergeGeometries(scene);
    }
    
    // Set level of detail
    this.setLevelOfDetail(scene, levelOfDetail);
  }
  
  /**
   * Simplify geometry
   * @param {THREE.Mesh} mesh - Three.js mesh
   * @param {number} ratio - Simplification ratio
   * @private
   */
  simplifyGeometry(mesh, ratio) {
    // In a real implementation, you would use a geometry simplification library
    // such as THREE.SimplifyModifier or THREE.DecimateModifier
    console.log(`Simplifying geometry with ratio ${ratio}`);
  }
  
  /**
   * Convert to instanced meshes
   * @param {THREE.Scene} scene - Three.js scene
   * @private
   */
  convertToInstancedMeshes(scene) {
    // In a real implementation, you would identify repeated meshes and convert them to instanced meshes
    console.log('Converting to instanced meshes');
  }
  
  /**
   * Merge geometries
   * @param {THREE.Scene} scene - Three.js scene
   * @private
   */
  mergeGeometries(scene) {
    // In a real implementation, you would merge geometries with the same material
    console.log('Merging geometries');
  }
  
  /**
   * Set level of detail
   * @param {THREE.Scene} scene - Three.js scene
   * @param {string} level - Level of detail
   * @private
   */
  setLevelOfDetail(scene, level) {
    let subdivisions = 32;
    
    switch (level) {
      case 'low':
        subdivisions = 8;
        break;
      case 'medium':
        subdivisions = 16;
        break;
      case 'high':
        subdivisions = 32;
        break;
      case 'ultra':
        subdivisions = 64;
        break;
      case 'auto':
      default:
        // Determine based on device capabilities
        const gpu = this.detectGPUCapabilities();
        
        if (gpu.tier === 3) {
          subdivisions = 32;
        } else if (gpu.tier === 2) {
          subdivisions = 16;
        } else {
          subdivisions = 8;
        }
        break;
    }
    
    // Update geometries with new subdivision level
    scene.traverse((object) => {
      if (object.isMesh && object.geometry) {
        if (object.geometry.type === 'SphereGeometry') {
          object.geometry.parameters.widthSegments = subdivisions;
          object.geometry.parameters.heightSegments = subdivisions;
        } else if (object.geometry.type === 'BoxGeometry') {
          object.geometry.parameters.widthSegments = Math.max(1, subdivisions / 8);
          object.geometry.parameters.heightSegments = Math.max(1, subdivisions / 8);
          object.geometry.parameters.depthSegments = Math.max(1, subdivisions / 8);
        }
      }
    });
  }
  
  /**
   * Detect GPU capabilities
   * @returns {Object} - GPU capabilities
   * @private
   */
  detectGPUCapabilities() {
    // In a real implementation, you would use a library like detect-gpu
    // For now, we'll use a simple heuristic based on the user agent
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (userAgent.includes('mobile') || userAgent.includes('android')) {
      return { tier: 1 }; // Low-end
    } else if (userAgent.includes('macintosh') || userAgent.includes('windows nt 10')) {
      return { tier: 3 }; // High-end
    } else {
      return { tier: 2 }; // Mid-range
    }
  }
}

// Create singleton instance
const visualizationPerformanceService = new VisualizationPerformanceService();

export default visualizationPerformanceService;
