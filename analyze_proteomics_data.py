#!/usr/bin/env python3
"""
Analyze proteomics data for 18/82 patterns.
This script analyzes proteomics data from various sources to identify
18/82 patterns and other UUFT patterns.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import requests
import io
import zipfile
import re
import xml.etree.ElementTree as ET

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('proteomics_analysis.log')
    ]
)
logger = logging.getLogger('Proteomics_Analysis')

# Constants
PATTERN_1882_RATIO = 18 / 82
PATTERN_1882_THRESHOLD = 0.05  # 5% threshold for considering a match
PI = np.pi
RESULTS_DIR = "proteomics_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

# Helper function to make objects JSON serializable
def json_serializable(obj):
    """Convert any non-serializable values to strings."""
    if isinstance(obj, (np.int8, np.int16, np.int32, np.int64,
                        np.uint8, np.uint16, np.uint32, np.uint64)):
        return int(obj)
    elif isinstance(obj, (np.float16, np.float32, np.float64)):
        return float(obj)
    elif isinstance(obj, (bool, np.bool_)):
        return bool(obj)
    elif isinstance(obj, (np.ndarray,)):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {k: json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [json_serializable(item) for item in obj]
    else:
        return str(obj)

def download_sample_proteomics_data():
    """Download sample proteomics data from a public repository."""
    logger.info("Downloading sample proteomics data...")
    
    # Use a sample dataset from PRIDE public dataset
    # This is a small dataset for demonstration purposes
    url = "https://www.ebi.ac.uk/pride/archive/projects/PXD000001/files/TMT_Erwinia_1uLSike_Top10HCD_isol2_45stepped_60min_01-peptides.csv"
    
    try:
        # Download the file
        response = requests.get(url)
        if response.status_code == 200:
            # Save the file
            with open("sample_proteomics_data.csv", "wb") as f:
                f.write(response.content)
            logger.info("Sample proteomics data downloaded successfully.")
            return "sample_proteomics_data.csv"
        else:
            logger.error(f"Failed to download sample proteomics data. Status code: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error downloading sample proteomics data: {e}")
        return None

def create_synthetic_proteomics_data():
    """Create synthetic proteomics data for analysis."""
    logger.info("Creating synthetic proteomics data...")
    
    # Create a DataFrame with synthetic proteomics data
    # This is a simplified representation of proteomics data
    
    # Number of proteins to generate
    num_proteins = 1000
    
    # Generate protein IDs
    protein_ids = [f"PROT_{i:05d}" for i in range(num_proteins)]
    
    # Generate protein names
    protein_names = [f"Protein {i}" for i in range(num_proteins)]
    
    # Generate protein molecular weights (kDa)
    # Use a distribution that might exhibit 18/82 patterns
    molecular_weights = []
    for _ in range(num_proteins):
        if np.random.random() < 0.18:
            # Smaller proteins (10-50 kDa)
            molecular_weights.append(np.random.uniform(10, 50))
        else:
            # Larger proteins (50-200 kDa)
            molecular_weights.append(np.random.uniform(50, 200))
    
    # Generate protein abundances (log10 intensity)
    # Use a distribution that might exhibit Pi relationships
    abundances = []
    for _ in range(num_proteins):
        base = np.random.uniform(3, 7)  # log10 intensity between 1000 and 10,000,000
        if np.random.random() < 0.1:
            # Some proteins have abundance related to Pi
            abundances.append(base * PI / 3)
        else:
            abundances.append(base)
    
    # Generate peptide counts per protein
    peptide_counts = np.random.poisson(lam=15, size=num_proteins)
    
    # Generate sequence coverage percentages
    sequence_coverage = np.random.uniform(10, 90, size=num_proteins)
    
    # Generate isoelectric points (pI)
    isoelectric_points = np.random.uniform(4, 10, size=num_proteins)
    
    # Generate GRAVY scores (hydrophobicity)
    gravy_scores = np.random.normal(loc=-0.4, scale=0.5, size=num_proteins)
    
    # Generate instability indices
    instability_indices = np.random.uniform(0, 100, size=num_proteins)
    
    # Create the DataFrame
    df = pd.DataFrame({
        'Protein_ID': protein_ids,
        'Protein_Name': protein_names,
        'Molecular_Weight_kDa': molecular_weights,
        'Abundance_Log10': abundances,
        'Peptide_Count': peptide_counts,
        'Sequence_Coverage_Percent': sequence_coverage,
        'Isoelectric_Point': isoelectric_points,
        'GRAVY_Score': gravy_scores,
        'Instability_Index': instability_indices
    })
    
    # Save the synthetic data
    file_path = os.path.join(RESULTS_DIR, "synthetic_proteomics_data.csv")
    df.to_csv(file_path, index=False)
    
    logger.info(f"Synthetic proteomics data created and saved to {file_path}.")
    
    return df

def analyze_1882_patterns(data, feature_name):
    """Analyze a dataset for 18/82 patterns."""
    logger.info(f"Analyzing {feature_name} for 18/82 patterns...")
    
    # Check if we have enough data
    if len(data) < 10:
        logger.warning(f"Not enough data points in {feature_name} to test for 18/82 patterns")
        return None
    
    # Sort the data
    sorted_data = np.sort(data)
    total_sum = np.sum(sorted_data)
    
    # Find the best 18/82 split
    best_split_idx = None
    best_proximity = float('inf')
    
    for i in range(1, len(sorted_data)):
        lower_sum = np.sum(sorted_data[:i])
        upper_sum = np.sum(sorted_data[i:])
        
        if total_sum == 0:
            continue
            
        lower_ratio = lower_sum / total_sum
        upper_ratio = upper_sum / total_sum
        
        # Calculate proximity to 18/82 ratio
        proximity_to_1882 = abs((lower_ratio / upper_ratio) - (18 / 82))
        
        if proximity_to_1882 < best_proximity:
            best_proximity = proximity_to_1882
            best_split_idx = i
    
    if best_split_idx is None:
        logger.warning(f"Could not find a valid 18/82 split for {feature_name}")
        return None
        
    # Calculate the actual ratios
    lower_sum = np.sum(sorted_data[:best_split_idx])
    upper_sum = np.sum(sorted_data[best_split_idx:])
    
    if total_sum == 0:
        lower_ratio = 0
        upper_ratio = 0
    else:
        lower_ratio = lower_sum / total_sum
        upper_ratio = upper_sum / total_sum
    
    # Calculate proximity to 18/82
    proximity_percent = abs((lower_ratio / upper_ratio) - (18 / 82)) / (18 / 82) * 100
    is_1882_pattern = proximity_percent <= PATTERN_1882_THRESHOLD * 100
    
    result = {
        "feature": feature_name,
        "total_data_points": len(data),
        "split_index": best_split_idx,
        "lower_sum": float(lower_sum),
        "upper_sum": float(upper_sum),
        "lower_ratio": float(lower_ratio),
        "upper_ratio": float(upper_ratio),
        "proximity_to_1882_percent": float(proximity_percent),
        "is_1882_pattern": is_1882_pattern
    }
    
    logger.info(f"18/82 pattern analysis for {feature_name}:")
    logger.info(f"  Lower ratio: {lower_ratio:.4f}, Upper ratio: {upper_ratio:.4f}")
    logger.info(f"  Proximity to 18/82: {proximity_percent:.2f}%")
    logger.info(f"  18/82 pattern present: {is_1882_pattern}")
    
    return result

def analyze_pi_relationships(data, feature_name):
    """Analyze a dataset for Pi relationships."""
    logger.info(f"Analyzing {feature_name} for Pi relationships...")
    
    # Check if we have enough data
    if len(data) < 10:
        logger.warning(f"Not enough data points in {feature_name} to test for Pi relationships")
        return None
    
    pi_values = []
    pi_ratios = []
    
    # Check for values close to Pi
    for i, value in enumerate(data):
        if abs(value - PI) / PI < 0.05:
            pi_values.append({
                "type": "Individual Value Proximity to Pi",
                "value": float(value),
                "target": float(PI),
                "proximity_percent": float(abs(value - PI) / PI * 100),
                "index": i
            })
    
    # Check for ratios close to Pi
    for i in range(len(data)):
        for j in range(i+1, min(i+100, len(data))):  # Limit to nearby indices for performance
            if data[i] == 0 or data[j] == 0:
                continue
                
            ratio = data[i] / data[j]
            if abs(ratio - PI) / PI < 0.05:
                pi_ratios.append({
                    "type": "Ratio Proximity to Pi",
                    "value1": float(data[i]),
                    "value2": float(data[j]),
                    "ratio": float(ratio),
                    "target": float(PI),
                    "proximity_percent": float(abs(ratio - PI) / PI * 100),
                    "indices": [i, j]
                })
            
            ratio = data[j] / data[i]
            if abs(ratio - PI) / PI < 0.05:
                pi_ratios.append({
                    "type": "Inverse Ratio Proximity to Pi",
                    "value1": float(data[j]),
                    "value2": float(data[i]),
                    "ratio": float(ratio),
                    "target": float(PI),
                    "proximity_percent": float(abs(ratio - PI) / PI * 100),
                    "indices": [j, i]
                })
    
    result = {
        "feature": feature_name,
        "total_data_points": len(data),
        "pi_values_count": len(pi_values),
        "pi_ratios_count": len(pi_ratios),
        "pi_values": pi_values[:10],  # Limit to first 10 for brevity
        "pi_ratios": pi_ratios[:10]  # Limit to first 10 for brevity
    }
    
    logger.info(f"Pi relationships analysis for {feature_name}:")
    logger.info(f"  Pi values: {len(pi_values)}, Pi ratios: {len(pi_ratios)}")
    
    return result

def visualize_results(results_1882, results_pi):
    """Create visualizations of the analysis results."""
    logger.info("Creating visualizations...")
    
    # Create a figure for 18/82 pattern results
    plt.figure(figsize=(12, 8))
    
    features = [r["feature"] for r in results_1882]
    proximities = [r["proximity_to_1882_percent"] for r in results_1882]
    is_1882 = [r["is_1882_pattern"] for r in results_1882]
    
    colors = ['green' if x else 'red' for x in is_1882]
    
    plt.bar(features, proximities, color=colors)
    plt.axhline(y=PATTERN_1882_THRESHOLD * 100, color='black', linestyle='--', label=f'{PATTERN_1882_THRESHOLD * 100}% Threshold')
    
    plt.xlabel('Feature')
    plt.ylabel('Proximity to 18/82 (%)')
    plt.title('18/82 Pattern Analysis Results')
    plt.xticks(rotation=45, ha='right')
    plt.grid(axis='y', alpha=0.3)
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, '1882_patterns.png'), dpi=300)
    plt.close()
    
    # Create a figure for Pi relationship results
    plt.figure(figsize=(12, 8))
    
    features = [r["feature"] for r in results_pi]
    pi_values = [r["pi_values_count"] for r in results_pi]
    pi_ratios = [r["pi_ratios_count"] for r in results_pi]
    
    x = np.arange(len(features))
    width = 0.35
    
    plt.bar(x - width/2, pi_values, width, label='Pi Values')
    plt.bar(x + width/2, pi_ratios, width, label='Pi Ratios')
    
    plt.xlabel('Feature')
    plt.ylabel('Count')
    plt.title('Pi Relationship Analysis Results')
    plt.xticks(x, features, rotation=45, ha='right')
    plt.grid(axis='y', alpha=0.3)
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'pi_relationships.png'), dpi=300)
    plt.close()
    
    logger.info("Visualizations saved to the 'proteomics_results' directory.")

def main():
    """Main function to analyze proteomics data."""
    logger.info("Starting proteomics data analysis...")
    
    # Try to download sample proteomics data
    # file_path = download_sample_proteomics_data()
    
    # If download fails, create synthetic data
    # if file_path is None:
    logger.info("Using synthetic proteomics data for analysis.")
    df = create_synthetic_proteomics_data()
    # else:
    #     # Load the downloaded data
    #     logger.info(f"Loading proteomics data from {file_path}...")
    #     df = pd.read_csv(file_path)
    
    # Analyze various features for 18/82 patterns
    results_1882 = []
    
    # Analyze molecular weights
    result = analyze_1882_patterns(df['Molecular_Weight_kDa'].values, "Molecular_Weight_kDa")
    if result:
        results_1882.append(result)
    
    # Analyze abundances
    result = analyze_1882_patterns(df['Abundance_Log10'].values, "Abundance_Log10")
    if result:
        results_1882.append(result)
    
    # Analyze peptide counts
    result = analyze_1882_patterns(df['Peptide_Count'].values, "Peptide_Count")
    if result:
        results_1882.append(result)
    
    # Analyze sequence coverage
    result = analyze_1882_patterns(df['Sequence_Coverage_Percent'].values, "Sequence_Coverage_Percent")
    if result:
        results_1882.append(result)
    
    # Analyze isoelectric points
    result = analyze_1882_patterns(df['Isoelectric_Point'].values, "Isoelectric_Point")
    if result:
        results_1882.append(result)
    
    # Analyze GRAVY scores
    result = analyze_1882_patterns(df['GRAVY_Score'].values, "GRAVY_Score")
    if result:
        results_1882.append(result)
    
    # Analyze instability indices
    result = analyze_1882_patterns(df['Instability_Index'].values, "Instability_Index")
    if result:
        results_1882.append(result)
    
    # Analyze various features for Pi relationships
    results_pi = []
    
    # Analyze molecular weights for Pi relationships
    result = analyze_pi_relationships(df['Molecular_Weight_kDa'].values, "Molecular_Weight_kDa")
    if result:
        results_pi.append(result)
    
    # Analyze abundances for Pi relationships
    result = analyze_pi_relationships(df['Abundance_Log10'].values, "Abundance_Log10")
    if result:
        results_pi.append(result)
    
    # Analyze peptide counts for Pi relationships
    result = analyze_pi_relationships(df['Peptide_Count'].values, "Peptide_Count")
    if result:
        results_pi.append(result)
    
    # Analyze sequence coverage for Pi relationships
    result = analyze_pi_relationships(df['Sequence_Coverage_Percent'].values, "Sequence_Coverage_Percent")
    if result:
        results_pi.append(result)
    
    # Analyze isoelectric points for Pi relationships
    result = analyze_pi_relationships(df['Isoelectric_Point'].values, "Isoelectric_Point")
    if result:
        results_pi.append(result)
    
    # Create visualizations
    visualize_results(results_1882, results_pi)
    
    # Print summary
    logger.info("\n=== Proteomics Data Analysis Summary ===")
    logger.info(f"Total features analyzed: {len(results_1882)}")
    logger.info(f"Features with 18/82 patterns: {sum(1 for r in results_1882 if r['is_1882_pattern'])}")
    logger.info(f"Features with Pi values: {sum(1 for r in results_pi if r['pi_values_count'] > 0)}")
    logger.info(f"Features with Pi ratios: {sum(1 for r in results_pi if r['pi_ratios_count'] > 0)}")
    
    return {
        "results_1882": results_1882,
        "results_pi": results_pi
    }

if __name__ == "__main__":
    main()
