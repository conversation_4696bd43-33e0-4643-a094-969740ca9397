import React, { useEffect, useState, useRef } from "react";
import { CheckCircle, XCircle, Loader, Shield, FileText, Clock, BarChart2, Zap, Star, Eye } from "lucide-react";

// Expanded steps with more detail and visual impact
const steps = [
  {
    id: 1,
    title: "Code Commit Detected",
    status: "running",
    description: "Automated tests triggered instantly.",
    icon: <Zap className="text-yellow-400" size={24} />,
    detail: "Continuous integration pipeline activated. Running 1,243 automated tests across all compliance domains.",
    metric: "< 50ms response time",
    ciciInsight: "Most platforms take 2-5 minutes just to start their test suite. We're already 240x faster before we even begin."
  },
  {
    id: 2,
    title: "OWASP Vulnerability Found",
    status: "error",
    description: "Blocked before it ever reached production.",
    icon: <Shield className="text-red-500" size={24} />,
    detail: "SQL injection vulnerability detected in user input validation. Preventing deployment and notifying development team.",
    metric: "99.8% detection rate",
    ciciInsight: "This vulnerability would have cost an average of $4.2M in breach expenses. Just paid for your entire annual subscription."
  },
  {
    id: 3,
    title: "Compliance Framework Mapping",
    status: "running",
    description: "Evidence collected automatically across frameworks.",
    icon: <BarChart2 className="text-purple-500" size={24} />,
    detail: "Mapping to GDPR, HIPAA, SOC 2, PCI DSS, and ISO 27001. Collecting and categorizing evidence from 17 different sources.",
    metric: "5 frameworks → 1 process",
    frameworks: ["GDPR", "HIPAA", "SOC2", "PCI", "ISO"],
    ciciInsight: "Traditional GRC tools require separate evidence collection for each framework. We're saving you approximately 400 hours of work right here."
  },
  {
    id: 4,
    title: "Audit Report Generated",
    status: "success",
    description: "Audit-ready. No stress. Just trust.",
    icon: <FileText className="text-green-500" size={24} />,
    detail: "Comprehensive audit report with 215 evidence items, mapped across all frameworks with 97.3% compliance score.",
    metric: "3-click compliance",
    ciciInsight: "Your auditor just texted me: 'This is the cleanest evidence package I've ever seen. Are you sure humans were involved?'"
  }
];

// Time comparison data
const timeComparison = {
  traditional: {
    days: 300,
    resources: 6,
    cost: "$250,000+"
  },
  novafuse: {
    seconds: 45,
    resources: 0,
    cost: "Fraction of traditional cost"
  }
};

const TrustAutomationDemo = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [expandedStep, setExpandedStep] = useState(null);
  const [showComparison, setShowComparison] = useState(false);
  const [evidenceCount, setEvidenceCount] = useState(0);
  const [insightMode, setInsightMode] = useState(false);
  const [konami, setKonami] = useState([]);
  const [showEasterEgg, setShowEasterEgg] = useState(false);
  const evidenceIntervalRef = useRef(null);

  // Load saved progress
  useEffect(() => {
    const saved = localStorage.getItem("novaFuseStep");
    if (saved) {
      const savedStep = Number(saved);
      setCurrentStep(savedStep);
      if (savedStep === steps.length - 1) {
        setShowComparison(true);
      }
      if (savedStep >= 2) {
        startEvidenceCounter();
      }
    }
  }, []);

  // Progress through steps
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStep((prev) => {
        if (prev < steps.length - 1) {
          // When moving to step 3, start showing evidence counter
          if (prev === 1) {
            startEvidenceCounter();
          }
          // Save progress
          localStorage.setItem("novaFuseStep", (prev + 1).toString());
          return prev + 1;
        } else {
          // When reaching the end, show comparison
          setShowComparison(true);
          return prev;
        }
      });
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  // We're now using fixed metrics instead of a dynamic counter

  // Evidence counter for step 3 - more evenly paced
  const startEvidenceCounter = () => {
    // Clear any existing interval
    if (evidenceIntervalRef.current) {
      clearInterval(evidenceIntervalRef.current);
    }

    // Calculate increment to evenly fill during step duration
    const totalItems = 215;
    const stepDuration = 3000; // 3 seconds
    const intervalTime = 200; // update every 200ms
    const increment = Math.ceil(totalItems / (stepDuration / intervalTime));

    evidenceIntervalRef.current = setInterval(() => {
      setEvidenceCount(prev => {
        const newCount = Math.min(prev + increment, totalItems);
        // Save to localStorage
        localStorage.setItem("novaFuseEvidenceCount", newCount.toString());

        if (newCount >= totalItems) {
          clearInterval(evidenceIntervalRef.current);
        }
        return newCount;
      });
    }, intervalTime);
  };

  // Konami code easter egg for CiCi Insight Mode
  useEffect(() => {
    const konamiCode = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'b', 'a'];

    const handleKeyDown = (e) => {
      setKonami(prev => {
        const updatedKonami = [...prev, e.key];
        if (updatedKonami.length > konamiCode.length) {
          updatedKonami.shift();
        }

        // Check if konami code is entered
        if (updatedKonami.join('') === konamiCode.join('')) {
          setInsightMode(true);
          setShowEasterEgg(true);
          setTimeout(() => setShowEasterEgg(false), 3000);
        }

        return updatedKonami;
      });
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  const statusIcon = (status) => {
    switch (status) {
      case "success":
        return <CheckCircle className="text-green-500" size={28} />;
      case "error":
        return <XCircle className="text-red-500" size={28} />;
      default:
        return <Loader className="animate-spin text-yellow-500" size={28} />;
    }
  };

  const toggleExpand = (id) => {
    setExpandedStep(expandedStep === id ? null : id);
  };

  return (
    <div className="bg-black min-h-screen flex flex-col items-center justify-center p-6 text-[#0A84FF] font-sans relative overflow-hidden">
      {/* Background glow effect */}
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] bg-[#0A84FF] rounded-full opacity-5 blur-[100px]"></div>

      {/* CiCi Easter Egg */}
      {showEasterEgg && (
        <div className="fixed top-0 left-0 w-full h-full flex items-center justify-center z-50 pointer-events-none">
          <div className="bg-black/80 p-8 rounded-2xl border-2 border-[#0A84FF] transform animate-bounce"
            style={{
              boxShadow: "0 0 30px rgba(10, 132, 255, 0.3), 0 0 15px rgba(168, 85, 247, 0.3)"
            }}>
            <div className="text-center">
              <Star className="text-yellow-400 inline-block mb-2" size={40} />
              <h3 className="text-2xl font-bold" style={{
                background: "linear-gradient(to right, #0A84FF, #A855F7)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}>CiCi Insight Mode Activated!</h3>
              <p className="text-[#0A84FF]">NovaConcierge commentary unlocked</p>
            </div>
          </div>
        </div>
      )}

      {/* CiCi Welcome Message */}
      <div className="absolute top-4 right-4 flex items-center gap-3">
        {insightMode && (
          <div className="bg-black/60 p-3 rounded-xl border border-[#0A84FF]/30 max-w-xs animate-fadeIn flex items-start gap-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold"
                style={{
                  background: "linear-gradient(135deg, #0A84FF, #A855F7)",
                  boxShadow: "0 0 10px rgba(168, 85, 247, 0.5)"
                }}>
                Ci
              </div>
            </div>
            <div>
              <p className="text-sm text-white">"Hello! I'm CiCi, your API integration assistant. How can I help you today?"</p>
            </div>
          </div>
        )}
        <button
          onClick={() => setInsightMode(!insightMode)}
          className={`p-2 rounded-full transition-all ${insightMode ? '' : 'bg-gray-800/50 animate-pulse-subtle'}`}
          title="Toggle CiCi Insight Mode"
          style={{
            background: insightMode ? 'linear-gradient(135deg, #0A84FF, #A855F7)' : '',
            boxShadow: insightMode ? '0 0 15px rgba(10, 132, 255, 0.5), 0 0 10px rgba(168, 85, 247, 0.3)' : '',
            transform: insightMode ? 'scale(1.05)' : 'scale(1)',
            border: insightMode ? 'none' : '1px solid rgba(255,255,255,0.1)',
            transition: 'all 0.3s ease'
          }}
        >
          <Eye size={20} color={insightMode ? 'white' : '#6B7280'} />
        </button>
      </div>

      {/* Glowing header with animation */}
      <div className="text-center mb-2 transition-all duration-500 transform">
        <h1
          className="text-5xl font-bold tracking-tight"
          style={{
            background: "linear-gradient(to right, #0A84FF, #A855F7)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            textShadow: "0 0 15px rgba(10, 132, 255, 0.7), 0 0 30px rgba(168, 85, 247, 0.5)"
          }}
        >
          Trust, Automated.
        </h1>
      </div>

      <p className="mb-8 text-xl" style={{
        background: "linear-gradient(to right, rgba(255,255,255,0.9), rgba(168, 85, 247, 0.8))",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        textShadow: "0 0 10px rgba(255, 255, 255, 0.3)"
      }}>From 300-Day Audits → 3-Click Compliance</p>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 w-full max-w-2xl">
        {/* Time saved counter */}
        <div className="flex items-center gap-4 bg-[#0A84FF]/10 p-3 rounded-xl border border-[#0A84FF]/30 transition-all hover:bg-[#0A84FF]/15">
          <Clock className="text-[#0A84FF] flex-shrink-0" />
          <div>
            <p className="text-white/80 text-sm">Time Saved vs. Traditional Audits</p>
            <p className="text-2xl font-bold">117.7 days</p>
          </div>
        </div>

        {/* Threat Detection */}
        <div className="flex items-center gap-4 bg-[#0A84FF]/10 p-3 rounded-xl border border-[#0A84FF]/30 transition-all hover:bg-[#0A84FF]/15">
          <Shield className="text-[#0A84FF] flex-shrink-0" />
          <div>
            <p className="text-white/80 text-sm">Threat Detection Rate</p>
            <p className="text-2xl font-bold">99.8%</p>
          </div>
        </div>

        {/* Time to Compliance */}
        <div className="flex items-center gap-4 bg-[#0A84FF]/10 p-3 rounded-xl border border-[#0A84FF]/30 transition-all hover:bg-[#0A84FF]/15">
          <Zap className="text-[#0A84FF] flex-shrink-0" />
          <div>
            <p className="text-white/80 text-sm">Time to Compliance</p>
            <p className="text-2xl font-bold">45 sec</p>
          </div>
        </div>
      </div>

      {/* Main workflow steps */}
      <div className="space-y-4 w-full max-w-2xl">
        {steps.map((step, index) => (
          <div
            key={step.id}
            className={`transition-all duration-500 rounded-2xl shadow-xl overflow-hidden transform ${
              index === currentStep ? "scale-[1.02] opacity-100 bg-[#0A84FF]/10" :
              index < currentStep ? "scale-100 opacity-90 bg-gray-900" :
              "scale-[0.98] opacity-60 bg-gray-900/50"
            } ${
              expandedStep === step.id ? "border-2 border-[#0A84FF]" :
              index === currentStep ? "border-2 border-[#0A84FF]" :
              index < currentStep ? "border border-green-500/30" :
              "border border-gray-700"
            }`}
          >
            {/* Step header */}
            <div
              className="flex items-center gap-4 p-4 cursor-pointer"
              onClick={() => index <= currentStep && toggleExpand(step.id)}
            >
              <div className="flex-shrink-0">
                {statusIcon(index < currentStep ? "success" : step.status)}
              </div>
              <div className="flex-grow">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold">{step.title}</h2>
                  {step.metric && (
                    <span className="text-xs bg-[#0A84FF]/20 px-2 py-1 rounded-full text-white/90">
                      {step.metric}
                    </span>
                  )}
                </div>
                <p className="text-base text-white/80">{step.description}</p>
              </div>
              <div className="flex-shrink-0">
                {step.icon}
              </div>
            </div>

            {/* Expanded details */}
            {expandedStep === step.id && (
              <div className="p-4 pt-0 bg-gray-900 border-t border-[#0A84FF]/30 text-white/80">
                <p className="mb-3">{step.detail}</p>

                {/* Framework badges for step 3 */}
                {step.frameworks && (
                  <div className="flex gap-2 flex-wrap mt-2">
                    {step.frameworks.map(framework => (
                      <span
                        key={framework}
                        className="px-2 py-1 bg-[#0A84FF]/20 rounded-md text-xs font-medium transition-all hover:bg-[#0A84FF]/30"
                      >
                        {framework}
                      </span>
                    ))}
                  </div>
                )}

                {/* Evidence counter for step 3 */}
                {step.id === 3 && currentStep >= 2 && (
                  <div className="mt-3 bg-black/30 p-2 rounded-lg">
                    <div className="flex justify-between mb-1">
                      <span>Evidence Collection Progress</span>
                      <span>{Math.min(evidenceCount, 215)}/215 items</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2.5">
                      <div
                        className="bg-green-500 h-2.5 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min((evidenceCount / 215) * 100, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* CiCi Insight */}
                {insightMode && step.ciciInsight && (
                  <div className="mt-4 bg-purple-900/20 border border-purple-500/30 p-3 rounded-lg flex items-start gap-3">
                    <div className="flex-shrink-0 mt-1">
                      <div className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold"
                        style={{
                          background: "linear-gradient(135deg, #0A84FF, #A855F7)",
                          boxShadow: "0 0 10px rgba(168, 85, 247, 0.5)"
                        }}>
                        Ci
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-purple-300">{step.ciciInsight}</p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Time comparison section with animation */}
      {showComparison && (
        <div
          className="mt-8 w-full max-w-2xl bg-[#0A84FF]/10 rounded-2xl p-6 border-2 border-[#0A84FF] transition-all duration-1000 opacity-100 transform translate-y-0"
          style={{animation: "fadeInUp 0.8s ease-out"}}
        >
          <h2 className="text-2xl font-bold mb-4 text-center">Trust Automation Metrics</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-900 p-4 rounded-xl transition-all hover:bg-gray-800">
              <h3 className="text-lg font-medium mb-2 text-white/90">Traditional Approach</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-white/70">Time to Compliance:</span>
                  <span className="font-bold text-red-400">{timeComparison.traditional.days} days</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">Resources Required:</span>
                  <span className="font-bold text-red-400">{timeComparison.traditional.resources} team members</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">Typical Cost:</span>
                  <span className="font-bold text-red-400">{timeComparison.traditional.cost}</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-900 p-4 rounded-xl transition-all hover:bg-gray-800">
              <h3 className="text-lg font-medium mb-2 text-white/90">NovaFuse Approach</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-white/70">Time to Compliance:</span>
                  <span className="font-bold text-green-400">{timeComparison.novafuse.seconds} seconds</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">Resources Required:</span>
                  <span className="font-bold text-green-400">{timeComparison.novafuse.resources} (fully automated)</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">Typical Cost:</span>
                  <span className="font-bold text-green-400">{timeComparison.novafuse.cost}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 text-center">
            <p className="text-white/80 italic mb-4">"In a world of risk, we engineer trust."</p>

            {/* Added CTA Button */}
            <button className="mt-4 font-bold py-3 px-8 rounded-full transition-all transform hover:scale-105 shadow-lg hover:shadow-[#0A84FF]/20 border border-[#0A84FF]/30"
              style={{
                background: "linear-gradient(to right, #0A84FF, #A855F7)",
                color: "white",
                textShadow: "0 1px 3px rgba(0,0,0,0.3)"
              }}>
              Book a Live Demo 🚀
            </button>
          </div>
        </div>
      )}

      {/* Add CSS animation */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        @keyframes pulseSubtle {
          0% {
            box-shadow: 0 0 0 0 rgba(10, 132, 255, 0.1);
          }
          70% {
            box-shadow: 0 0 0 6px rgba(10, 132, 255, 0);
          }
          100% {
            box-shadow: 0 0 0 0 rgba(10, 132, 255, 0);
          }
        }

        .animate-fadeIn {
          animation: fadeIn 0.5s ease-out;
        }

        .animate-pulse-subtle {
          animation: pulseSubtle 2s infinite;
        }

        .animate-fadeInUp {
          animation: fadeInUp 0.8s ease-out;
        }
      `}</style>
    </div>
  );
};

export default TrustAutomationDemo;
