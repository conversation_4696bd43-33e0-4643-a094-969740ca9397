/**
 * Financial Entropy Interpreter for CSFE
 * 
 * This module implements the Financial Entropy Interpreter, which formalizes metrics
 * for "financial entropy" (Ψₜᶠ) in cyber-risk contexts. It mirrors the BioEntropicTensor
 * in the CSME component but is tailored for financial systems.
 * 
 * Key metrics include:
 * - Transaction Entropy (Tᵋ): Measure unpredictability in payment flows
 * - Attack Surface Coherence (Ψₐ): Quantify system vulnerability decay rates
 * - Market Stress Infusion (ΔΨₘ): Model shockwave propagation in financial networks
 * - Liquidity Entropy Index (Lᵋ): Measures payment system fragility
 * - Cryptographic Coherence Score (Ψₖ): Tracks quantum-vulnerability decay in encryption
 */

const { performance } = require('perf_hooks');

/**
 * FinancialEntropyInterpreter class
 */
class FinancialEntropyInterpreter {
  /**
   * Create a new FinancialEntropyInterpreter instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      entropyEstimator: 'shannon', // 'shannon', 'renyi', 'tsallis'
      renyiAlpha: 2.0, // Alpha parameter for Rényi entropy
      tsallisQ: 1.5, // q parameter for Tsallis entropy
      tensorDimensions: 4, // Default tensor dimensions
      enableCaching: true, // Enable result caching
      enableMetrics: true, // Enable performance metrics
      coherenceThreshold: 0.82, // Default coherence threshold based on 18/82 principle
      ...options
    };
    
    // Initialize cache
    this.cache = new Map();
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      cacheHits: 0,
      cacheMisses: 0,
      totalProcessed: 0
    };
    
    console.log('FinancialEntropyInterpreter initialized with entropy estimator:', this.options.entropyEstimator);
  }
  
  /**
   * Process financial data into entropy variables and tensor representation
   * @param {Object} financialData - Raw financial data
   * @returns {Object} - Processed data with entropy variables and tensor representation
   */
  processFinancialData(financialData) {
    const startTime = performance.now();
    this.metrics.totalProcessed++;
    
    // Generate cache key
    const cacheKey = this._generateCacheKey(financialData);
    
    // Check cache if enabled
    if (this.options.enableCaching && this.cache.has(cacheKey)) {
      this.metrics.cacheHits++;
      const cachedResult = this.cache.get(cacheKey);
      
      // Update processing time
      this.metrics.processingTimeMs += performance.now() - startTime;
      
      return cachedResult;
    }
    
    this.metrics.cacheMisses++;
    
    // Extract key financial metrics
    const { 
      transactionData = {}, 
      marketData = {}, 
      securityData = {},
      networkData = {}
    } = financialData;
    
    // Calculate entropy for each component
    const transactionEntropy = this._calculateTransactionEntropy(transactionData);
    const attackSurfaceCoherence = this._calculateAttackSurfaceCoherence(securityData);
    const marketStressInfusion = this._calculateMarketStressInfusion(marketData);
    const liquidityEntropyIndex = this._calculateLiquidityEntropyIndex(transactionData, networkData);
    const cryptographicCoherenceScore = this._calculateCryptographicCoherenceScore(securityData);
    
    // Calculate financial coherence
    const coherence = this._calculateFinancialCoherence(
      transactionEntropy,
      attackSurfaceCoherence,
      marketStressInfusion,
      liquidityEntropyIndex,
      cryptographicCoherenceScore
    );
    
    // Create tensor representation
    const tensor = this._createTensor(
      transactionEntropy,
      attackSurfaceCoherence,
      marketStressInfusion,
      liquidityEntropyIndex,
      cryptographicCoherenceScore,
      coherence
    );
    
    // Calculate entropy gradient (if previous data available)
    const entropyGradient = this._calculateEntropyGradient(coherence);
    
    // Prepare result
    const result = {
      coherence,
      entropyGradient,
      tensor,
      components: {
        transactionEntropy,
        attackSurfaceCoherence,
        marketStressInfusion,
        liquidityEntropyIndex,
        cryptographicCoherenceScore
      },
      processedAt: new Date().toISOString()
    };
    
    // Cache result if caching is enabled
    if (this.options.enableCaching) {
      this.cache.set(cacheKey, result);
      
      // Limit cache size
      if (this.cache.size > 1000) {
        const oldestKey = this.cache.keys().next().value;
        this.cache.delete(oldestKey);
      }
    }
    
    // Update processing time
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    return result;
  }
  
  /**
   * Calculate Transaction Entropy (Tᵋ)
   * @param {Object} transactionData - Transaction data
   * @returns {number} - Transaction entropy value
   * @private
   */
  _calculateTransactionEntropy(transactionData) {
    // Extract transaction values
    const transactions = transactionData.transactions || [];
    const volumes = transactions.map(t => t.volume || 0);
    const frequencies = transactions.map(t => t.frequency || 0);
    const anomalyScores = transactions.map(t => t.anomalyScore || 0);
    
    // Calculate entropy of transaction patterns
    const volumeEntropy = this._calculateEntropy(volumes);
    const frequencyEntropy = this._calculateEntropy(frequencies);
    const anomalyEntropy = this._calculateEntropy(anomalyScores);
    
    // Apply 18/82 principle: 18% weight to volume and frequency, 82% to anomaly scores
    return (0.18 * (volumeEntropy + frequencyEntropy) / 2) + (0.82 * anomalyEntropy);
  }
  
  /**
   * Calculate Attack Surface Coherence (Ψₐ)
   * @param {Object} securityData - Security data
   * @returns {number} - Attack surface coherence value
   * @private
   */
  _calculateAttackSurfaceCoherence(securityData) {
    // Extract security metrics
    const vulnerabilities = securityData.vulnerabilities || [];
    const patchStatus = securityData.patchStatus || [];
    const threatIntel = securityData.threatIntel || [];
    
    // Calculate entropy of security metrics
    const vulnerabilityEntropy = this._calculateEntropy(vulnerabilities.map(v => v.severity || 0));
    const patchEntropy = this._calculateEntropy(patchStatus.map(p => p.age || 0));
    const threatEntropy = this._calculateEntropy(threatIntel.map(t => t.relevance || 0));
    
    // Calculate attack surface coherence (inverse of entropy)
    const attackSurfaceEntropy = (vulnerabilityEntropy + patchEntropy + threatEntropy) / 3;
    
    // Return coherence (1 - entropy)
    return 1 - attackSurfaceEntropy;
  }
  
  /**
   * Calculate Market Stress Infusion (ΔΨₘ)
   * @param {Object} marketData - Market data
   * @returns {number} - Market stress infusion value
   * @private
   */
  _calculateMarketStressInfusion(marketData) {
    // Extract market metrics
    const volatility = marketData.volatility || [];
    const correlations = marketData.correlations || [];
    const liquidityMetrics = marketData.liquidity || [];
    
    // Calculate entropy of market metrics
    const volatilityEntropy = this._calculateEntropy(volatility);
    const correlationEntropy = this._calculateEntropy(correlations);
    const liquidityEntropy = this._calculateEntropy(liquidityMetrics);
    
    // Apply 18/82 principle: 18% weight to volatility, 82% to correlations and liquidity
    return (0.18 * volatilityEntropy) + (0.82 * (correlationEntropy + liquidityEntropy) / 2);
  }
  
  /**
   * Calculate Liquidity Entropy Index (Lᵋ)
   * @param {Object} transactionData - Transaction data
   * @param {Object} networkData - Network data
   * @returns {number} - Liquidity entropy index value
   * @private
   */
  _calculateLiquidityEntropyIndex(transactionData, networkData) {
    // Extract liquidity metrics
    const liquidityGaps = transactionData.liquidityGaps || [];
    const transactionVelocity = transactionData.velocity || [];
    const networkResilience = networkData.resilience || 0.5;
    
    // Calculate liquidity gaps derivative
    const liquidityGapsDerivative = this._calculateDerivative(liquidityGaps);
    
    // Calculate transaction velocity delta
    const transactionVelocityDelta = this._calculateDelta(transactionVelocity);
    
    // Calculate liquidity entropy index: (∂(Liquidity Gaps)/∂t) × (Δ(Transaction Velocity)/Network Resilience)
    return (liquidityGapsDerivative * transactionVelocityDelta) / Math.max(0.1, networkResilience);
  }
  
  /**
   * Calculate Cryptographic Coherence Score (Ψₖ)
   * @param {Object} securityData - Security data
   * @returns {number} - Cryptographic coherence score value
   * @private
   */
  _calculateCryptographicCoherenceScore(securityData) {
    // Extract cryptographic metrics
    const keyLength = securityData.keyLength || 2048;
    const algoAge = securityData.algoAge || 5;
    const threatIntel = securityData.quantumThreatLevel || 0.5;
    
    // Calculate cryptographic coherence score: (key_length * e^(-algo_age)) / threat_intel
    return (keyLength * Math.exp(-algoAge / 10)) / (threatIntel * 10000);
  }
  
  /**
   * Calculate financial coherence from entropy components
   * @param {number} transactionEntropy - Transaction entropy
   * @param {number} attackSurfaceCoherence - Attack surface coherence
   * @param {number} marketStressInfusion - Market stress infusion
   * @param {number} liquidityEntropyIndex - Liquidity entropy index
   * @param {number} cryptographicCoherenceScore - Cryptographic coherence score
   * @returns {number} - Coherence value (0-1)
   * @private
   */
  _calculateFinancialCoherence(
    transactionEntropy,
    attackSurfaceCoherence,
    marketStressInfusion,
    liquidityEntropyIndex,
    cryptographicCoherenceScore
  ) {
    // Normalize cryptographic coherence score to 0-1 range
    const normalizedCryptoScore = Math.min(1, Math.max(0, cryptographicCoherenceScore / 10));
    
    // Normalize liquidity entropy index to 0-1 range
    const normalizedLiquidityIndex = Math.min(1, Math.max(0, liquidityEntropyIndex));
    
    // Apply 18/82 principle: 18% weight to positive factors, 82% to negative factors
    const positiveFactors = (attackSurfaceCoherence + normalizedCryptoScore) / 2;
    const negativeFactors = (transactionEntropy + marketStressInfusion + normalizedLiquidityIndex) / 3;
    
    const weightedEntropy = (0.18 * (1 - positiveFactors)) + (0.82 * negativeFactors);
    
    // Convert entropy to coherence (inverse relationship)
    // Normalize to 0-1 range where 1 is maximum coherence (minimum entropy)
    return Math.max(0, Math.min(1, 1 - weightedEntropy));
  }
  
  /**
   * Create tensor representation of financial metrics
   * @param {number} transactionEntropy - Transaction entropy
   * @param {number} attackSurfaceCoherence - Attack surface coherence
   * @param {number} marketStressInfusion - Market stress infusion
   * @param {number} liquidityEntropyIndex - Liquidity entropy index
   * @param {number} cryptographicCoherenceScore - Cryptographic coherence score
   * @param {number} coherence - Overall coherence
   * @returns {Array} - Tensor representation [Tᵋ, Ψₐ, ΔΨₘ, Lᵋ, Ψₖ, Ψₜᶠ]
   * @private
   */
  _createTensor(
    transactionEntropy,
    attackSurfaceCoherence,
    marketStressInfusion,
    liquidityEntropyIndex,
    cryptographicCoherenceScore,
    coherence
  ) {
    // Normalize cryptographic coherence score to 0-1 range
    const normalizedCryptoScore = Math.min(1, Math.max(0, cryptographicCoherenceScore / 10));
    
    // Normalize liquidity entropy index to 0-1 range
    const normalizedLiquidityIndex = Math.min(1, Math.max(0, liquidityEntropyIndex));
    
    // Create tensor [Tᵋ, Ψₐ, ΔΨₘ, Lᵋ, Ψₖ, Ψₜᶠ]
    return [
      transactionEntropy,
      attackSurfaceCoherence,
      marketStressInfusion,
      normalizedLiquidityIndex,
      normalizedCryptoScore,
      coherence
    ];
  }
  
  /**
   * Calculate entropy based on the selected estimator
   * @param {Array} values - Input values
   * @returns {number} - Entropy value
   * @private
   */
  _calculateEntropy(values) {
    // If no values, return maximum entropy
    if (!values || values.length === 0) {
      return 1.0;
    }
    
    // Normalize values to probabilities
    const probabilities = this._normalizeToProbabilities(values);
    
    // Calculate entropy based on selected estimator
    switch (this.options.entropyEstimator) {
      case 'renyi':
        return this._calculateRenyiEntropy(probabilities);
      case 'tsallis':
        return this._calculateTsallisEntropy(probabilities);
      case 'shannon':
      default:
        return this._calculateShannonEntropy(probabilities);
    }
  }
  
  /**
   * Calculate Shannon entropy
   * @param {Array} probabilities - Array of probability values
   * @returns {number} - Shannon entropy value
   * @private
   */
  _calculateShannonEntropy(probabilities) {
    return -probabilities.reduce((sum, p) => {
      return sum + (p > 0 ? p * Math.log2(p) : 0);
    }, 0);
  }
  
  /**
   * Calculate Rényi entropy
   * @param {Array} probabilities - Array of probability values
   * @returns {number} - Rényi entropy value
   * @private
   */
  _calculateRenyiEntropy(probabilities) {
    const alpha = this.options.renyiAlpha;
    
    // Handle special case: alpha = 1 (Shannon entropy)
    if (Math.abs(alpha - 1.0) < 0.0001) {
      return this._calculateShannonEntropy(probabilities);
    }
    
    const sum = probabilities.reduce((acc, p) => {
      return acc + (p > 0 ? Math.pow(p, alpha) : 0);
    }, 0);
    
    return (1 / (1 - alpha)) * Math.log2(sum);
  }
  
  /**
   * Calculate Tsallis entropy
   * @param {Array} probabilities - Array of probability values
   * @returns {number} - Tsallis entropy value
   * @private
   */
  _calculateTsallisEntropy(probabilities) {
    const q = this.options.tsallisQ;
    
    // Handle special case: q = 1 (Shannon entropy)
    if (Math.abs(q - 1.0) < 0.0001) {
      return this._calculateShannonEntropy(probabilities);
    }
    
    const sum = probabilities.reduce((acc, p) => {
      return acc + (p > 0 ? Math.pow(p, q) : 0);
    }, 0);
    
    return (1 - sum) / (q - 1);
  }
  
  /**
   * Calculate derivative of a time series
   * @param {Array} values - Time series values
   * @returns {number} - Derivative value
   * @private
   */
  _calculateDerivative(values) {
    if (!values || values.length < 2) {
      return 0;
    }
    
    // Calculate average rate of change
    let sum = 0;
    for (let i = 1; i < values.length; i++) {
      sum += values[i] - values[i - 1];
    }
    
    return sum / (values.length - 1);
  }
  
  /**
   * Calculate delta (change) in a time series
   * @param {Array} values - Time series values
   * @returns {number} - Delta value
   * @private
   */
  _calculateDelta(values) {
    if (!values || values.length < 2) {
      return 0;
    }
    
    // Calculate total change
    return values[values.length - 1] - values[0];
  }
  
  /**
   * Calculate entropy gradient based on current coherence
   * @param {number} currentCoherence - Current coherence value
   * @returns {number} - Entropy gradient
   * @private
   */
  _calculateEntropyGradient(currentCoherence) {
    // If no previous coherence, return 0 gradient
    if (!this.previousCoherence) {
      this.previousCoherence = currentCoherence;
      this.previousTimestamp = Date.now();
      return 0;
    }
    
    // Calculate time delta in seconds
    const currentTime = Date.now();
    const deltaTime = (currentTime - this.previousTimestamp) / 1000;
    
    // If time delta is too small, return previous gradient
    if (deltaTime < 0.001) {
      return this.previousGradient || 0;
    }
    
    // Calculate gradient
    const deltaCoherence = currentCoherence - this.previousCoherence;
    const gradient = deltaCoherence / deltaTime;
    
    // Update previous values
    this.previousCoherence = currentCoherence;
    this.previousTimestamp = currentTime;
    this.previousGradient = gradient;
    
    return gradient;
  }
  
  /**
   * Normalize values to probabilities
   * @param {Array} values - Array of values
   * @returns {Array} - Array of probabilities
   * @private
   */
  _normalizeToProbabilities(values) {
    const sum = values.reduce((acc, val) => acc + Math.abs(val), 0);
    
    if (sum === 0) {
      // If sum is 0, return uniform distribution
      return values.map(() => 1 / values.length);
    }
    
    // Normalize values to probabilities
    return values.map(val => Math.abs(val) / sum);
  }
  
  /**
   * Generate cache key for financial data
   * @param {Object} financialData - Financial data
   * @returns {string} - Cache key
   * @private
   */
  _generateCacheKey(financialData) {
    try {
      // Use only essential properties for the key
      const keyData = {
        t: this._hashObject(financialData.transactionData),
        m: this._hashObject(financialData.marketData),
        s: this._hashObject(financialData.securityData),
        n: this._hashObject(financialData.networkData)
      };
      
      return JSON.stringify(keyData);
    } catch (error) {
      console.error('Error generating cache key:', error);
      return Date.now().toString(); // Fallback to timestamp
    }
  }
  
  /**
   * Create a simple hash of an object
   * @param {Object} obj - Input object
   * @returns {number} - Hash value
   * @private
   */
  _hashObject(obj) {
    if (!obj) return 0;
    
    const str = JSON.stringify(obj);
    let hash = 0;
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    
    return hash;
  }
}

module.exports = FinancialEntropyInterpreter;
