/**
 * Swagger documentation for the Compliance Automation API
 */
const swaggerDocs = {
  tags: [
    {
      name: 'Compliance Automation',
      description: 'API endpoints for compliance automation'
    }
  ],
  components: {
    parameters: {
      page: {
        name: 'page',
        in: 'query',
        description: 'Page number for pagination',
        schema: {
          type: 'integer',
          default: 1,
          minimum: 1
        }
      },
      limit: {
        name: 'limit',
        in: 'query',
        description: 'Number of items per page',
        schema: {
          type: 'integer',
          default: 10,
          minimum: 1,
          maximum: 100
        }
      },
      sortBy: {
        name: 'sortBy',
        in: 'query',
        description: 'Field to sort by',
        schema: {
          type: 'string'
        }
      },
      sortOrder: {
        name: 'sortOrder',
        in: 'query',
        description: 'Sort order (asc or desc)',
        schema: {
          type: 'string',
          enum: ['asc', 'desc'],
          default: 'asc'
        }
      }
    },
    schemas: {
      Pagination: {
        type: 'object',
        properties: {
          total: {
            type: 'integer',
            description: 'Total number of items'
          },
          page: {
            type: 'integer',
            description: 'Current page number'
          },
          limit: {
            type: 'integer',
            description: 'Number of items per page'
          },
          pages: {
            type: 'integer',
            description: 'Total number of pages'
          }
        }
      },
      Error: {
        type: 'object',
        properties: {
          error: {
            type: 'string',
            description: 'Error type'
          },
          message: {
            type: 'string',
            description: 'Error message'
          }
        }
      }
    },
    responses: {
      BadRequest: {
        description: 'Bad Request',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              error: 'Bad Request',
              message: 'Invalid request parameters'
            }
          }
        }
      },
      Unauthorized: {
        description: 'Unauthorized',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              error: 'Unauthorized',
              message: 'Authentication required'
            }
          }
        }
      },
      NotFound: {
        description: 'Not Found',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              error: 'Not Found',
              message: 'Resource not found'
            }
          }
        }
      },
      InternalError: {
        description: 'Internal Server Error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              error: 'Internal Server Error',
              message: 'An unexpected error occurred'
            }
          }
        }
      }
    },
    securitySchemes: {
      ApiKeyAuth: {
        type: 'apiKey',
        in: 'header',
        name: 'X-API-Key'
      }
    }
  }
};

module.exports = swaggerDocs;
