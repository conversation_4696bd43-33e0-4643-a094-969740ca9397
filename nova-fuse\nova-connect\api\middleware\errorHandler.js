/**
 * Error Handler Middleware
 * 
 * This middleware handles errors and sends appropriate responses.
 */

const { 
  ValidationError, 
  AuthenticationError, 
  AuthorizationError, 
  NotFoundError, 
  ConflictError, 
  RateLimitError, 
  ServerError 
} = require('../utils/errors');

const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);
  
  // Handle specific error types
  if (err instanceof ValidationError) {
    return res.status(400).json({
      error: 'Bad Request',
      message: err.message
    });
  }
  
  if (err instanceof AuthenticationError) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: err.message
    });
  }
  
  if (err instanceof AuthorizationError) {
    return res.status(403).json({
      error: 'Forbidden',
      message: err.message
    });
  }
  
  if (err instanceof NotFoundError) {
    return res.status(404).json({
      error: 'Not Found',
      message: err.message
    });
  }
  
  if (err instanceof ConflictError) {
    return res.status(409).json({
      error: 'Conflict',
      message: err.message
    });
  }
  
  if (err instanceof RateLimitError) {
    return res.status(429).json({
      error: 'Too Many Requests',
      message: err.message,
      retryAfter: err.retryAfter
    });
  }
  
  if (err instanceof ServerError) {
    return res.status(500).json({
      error: 'Internal Server Error',
      message: err.message
    });
  }
  
  // Handle other errors
  return res.status(500).json({
    error: 'Internal Server Error',
    message: 'An unexpected error occurred'
  });
};

module.exports = errorHandler;
