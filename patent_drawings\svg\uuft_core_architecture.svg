<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1007.609375 430" style="max-width: 1007.61px; background-color: white;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#333333;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#my-svg .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .uspto&gt;*{fill:#fff!important;stroke:#000!important;stroke-width:2px!important;}#my-svg .uspto span{fill:#fff!important;stroke:#000!important;stroke-width:2px!important;}#my-svg .reference100&gt;*{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference100 span{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference200&gt;*{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference200 span{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference300&gt;*{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference300 span{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference400&gt;*{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .reference400 span{fill:none!important;stroke:none!important;font-size:8pt!important;}#my-svg .math&gt;*{fill:#f9f9f9!important;stroke:#ddd!important;stroke-dasharray:5 5!important;font-size:8pt!important;}#my-svg .math span{fill:#f9f9f9!important;stroke:#ddd!important;stroke-dasharray:5 5!important;font-size:8pt!important;}#my-svg .legend&gt;*{fill:#f0f0f0!important;stroke:#ccc!important;stroke-width:1px!important;}#my-svg .legend span{fill:#f0f0f0!important;stroke:#ccc!important;stroke-width:1px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M86.852,58L86.852,64.833C86.852,71.667,86.852,85.333,86.852,97.667C86.852,110,86.852,121,86.852,126.5L86.852,132"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_0" d="M86.852,182L86.852,188.167C86.852,194.333,86.852,206.667,86.852,218.333C86.852,230,86.852,241,86.852,246.5L86.852,252"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_0" d="M86.852,302L86.852,308.167C86.852,314.333,86.852,326.667,86.852,338.333C86.852,350,86.852,361,86.852,366.5L86.852,372"/></g><g class="edgeLabels"><g transform="translate(86.8515625, 99)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>⊗</p></span></div></foreignObject></g></g><g transform="translate(86.8515625, 219)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>⊕</p></span></div></foreignObject></g></g><g transform="translate(86.8515625, 339)" class="edgeLabel"><g transform="translate(-16.8359375, -12)" class="label"><foreignObject height="24" width="33.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>π10³</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(86.8515625, 35)" id="flowchart-A-0" class="node default uspto reference100"><rect height="46" width="104.484375" y="-23" x="-52.2421875" style="fill:none !important;stroke:none !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-22.2421875, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="44.484375"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>Domain A</p></span></div></foreignObject></g></g><g transform="translate(86.8515625, 159)" id="flowchart-B-1" class="node default uspto reference200"><rect height="46" width="104.828125" y="-23" x="-52.4140625" style="fill:none !important;stroke:none !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-22.4140625, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="44.828125"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>Domain B</p></span></div></foreignObject></g></g><g transform="translate(86.8515625, 279)" id="flowchart-C-3" class="node default uspto reference300"><rect height="46" width="105.171875" y="-23" x="-52.5859375" style="fill:none !important;stroke:none !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-22.5859375, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="45.171875"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>Domain C</p></span></div></foreignObject></g></g><g transform="translate(86.8515625, 399)" id="flowchart-D-5" class="node default uspto reference400"><rect height="46" width="157.703125" y="-23" x="-78.8515625" style="fill:none !important;stroke:none !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-48.8515625, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="97.703125"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>Unified Field Output</p></span></div></foreignObject></g></g><g transform="translate(265.484375, 35)" id="flowchart-Math1-10" class="node default math"><rect height="46" width="152.78125" y="-23" x="-76.390625" style="fill:#f9f9f9 !important;stroke:#ddd !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-46.390625, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="92.78125"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>π10³ Scaling Factor</p></span></div></foreignObject></g></g><g transform="translate(465.8828125, 35)" id="flowchart-Math2-11" class="node default math"><rect height="46" width="148.015625" y="-23" x="-74.0078125" style="fill:#f9f9f9 !important;stroke:#ddd !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-44.0078125, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="88.015625"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>⊗: Tensor Product</p></span></div></foreignObject></g></g><g transform="translate(654.7890625, 35)" id="flowchart-Math3-12" class="node default math"><rect height="46" width="129.796875" y="-23" x="-64.8984375" style="fill:#f9f9f9 !important;stroke:#ddd !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-34.8984375, -8)" style="font-size:8pt !important" class="label"><rect/><foreignObject height="16" width="69.796875"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 8pt !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:8pt !important"><p>⊕: Direct Sum</p></span></div></foreignObject></g></g><g transform="translate(884.6484375, 35)" id="flowchart-Legend-13" class="node default legend"><rect height="54" width="229.921875" y="-27" x="-114.9609375" style="fill:#f0f0f0 !important;stroke:#ccc !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-84.9609375, -12)" style="" class="label"><rect/><foreignObject height="24" width="169.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>UUFT Core Architecture</p></span></div></foreignObject></g></g></g></g></g></svg>