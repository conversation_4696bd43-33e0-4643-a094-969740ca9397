/**
 * New Workflow Trigger
 * 
 * This trigger fires when a new workflow is created.
 */

// Define the trigger
module.exports = {
  key: 'new_workflow',
  noun: 'Workflow',
  
  // Display information
  display: {
    label: 'New Workflow',
    description: 'Triggers when a new workflow is created.',
    important: true
  },
  
  // Operation
  operation: {
    // Polling operation
    type: 'polling',
    
    // Perform the operation
    perform: {
      url: '{{process.env.API_BASE_URL}}/api/zapier/triggers/new-workflow',
      headers: {
        Authorization: 'Bearer {{bundle.authData.access_token}}'
      }
    },
    
    // Sample data
    sample: {
      id: 'wf-123',
      name: 'Sample Workflow',
      status: 'active',
      createdAt: '2023-01-01T00:00:00Z'
    },
    
    // Output fields
    outputFields: [
      { key: 'id', label: 'ID' },
      { key: 'name', label: 'Name' },
      { key: 'status', label: 'Status' },
      { key: 'createdAt', label: 'Created At' }
    ]
  }
};
