/**
 * Conversation History Component
 * 
 * This component displays the user's recent conversations with NovaAssistAI
 * and allows them to continue previous conversations.
 */

import React, { useState, useEffect } from 'react';
import { Clock, MessageSquare, ChevronRight, Search } from 'lucide-react';
import axios from 'axios';

interface Conversation {
  _id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  messageCount: number;
  isActive: boolean;
}

interface ConversationHistoryProps {
  onSelectConversation: (conversationId: string) => void;
  className?: string;
}

const ConversationHistory: React.FC<ConversationHistoryProps> = ({ 
  onSelectConversation,
  className = ''
}) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  
  useEffect(() => {
    fetchConversations();
  }, []);
  
  const fetchConversations = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/v1/nova-assist/history');
      setConversations(response.data.conversations);
      setError(null);
    } catch (err) {
      setError('Failed to load conversation history');
      console.error('Error fetching conversations:', err);
    } finally {
      setLoading(false);
    }
  };
  
  const handleSelectConversation = (conversationId: string) => {
    onSelectConversation(conversationId);
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return 'Today, ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return 'Yesterday, ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays < 7) {
      return date.toLocaleDateString([], { weekday: 'long' }) + ', ' + 
        date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' }) + ', ' + 
        date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };
  
  const filteredConversations = searchQuery.trim() === '' 
    ? conversations 
    : conversations.filter(conv => 
        conv.title.toLowerCase().includes(searchQuery.toLowerCase())
      );
  
  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      <div className="p-3 border-b border-gray-200">
        <h3 className="font-medium">Recent Conversations</h3>
        
        <div className="mt-2 relative">
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full px-3 py-2 pl-9 border border-gray-300 rounded-md text-sm"
          />
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
        </div>
      </div>
      
      <div className="overflow-y-auto max-h-80">
        {loading ? (
          <div className="p-4 text-center text-gray-500">
            Loading conversations...
          </div>
        ) : error ? (
          <div className="p-4 text-center text-red-500">
            {error}
          </div>
        ) : filteredConversations.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            {searchQuery.trim() === '' 
              ? 'No recent conversations' 
              : 'No conversations match your search'}
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {filteredConversations.map((conversation) => (
              <li 
                key={conversation._id}
                className="hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => handleSelectConversation(conversation._id)}
              >
                <div className="p-3">
                  <div className="flex justify-between items-start">
                    <div className="flex items-start space-x-2">
                      <MessageSquare className="h-5 w-5 text-blue-500 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-gray-900 line-clamp-1">
                          {conversation.title}
                        </h4>
                        <p className="text-xs text-gray-500 mt-1">
                          {conversation.messageCount} messages
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <span className="text-xs text-gray-500 flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatDate(conversation.updatedAt)}
                      </span>
                      <ChevronRight className="h-4 w-4 text-gray-400 ml-2" />
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
      
      <div className="p-3 border-t border-gray-200 text-center">
        <button 
          onClick={fetchConversations}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          Refresh
        </button>
      </div>
    </div>
  );
};

export default ConversationHistory;
