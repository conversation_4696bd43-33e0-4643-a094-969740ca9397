/**
 * DataTable Component
 * 
 * A reusable data table component with sorting, filtering, and pagination.
 */

import React, { useState, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';

/**
 * DataTable component
 * 
 * @param {Object} props - Component props
 * @param {Array} props.columns - Table columns configuration
 * @param {Array} props.data - Table data
 * @param {Object} [props.pagination] - Pagination configuration
 * @param {number} props.pagination.total - Total number of items
 * @param {number} props.pagination.limit - Number of items per page
 * @param {number} props.pagination.offset - Current offset
 * @param {Function} props.pagination.onPageChange - Function to call when page changes
 * @param {boolean} [props.sortable=true] - Whether the table is sortable
 * @param {boolean} [props.filterable=true] - Whether the table is filterable
 * @param {boolean} [props.loading=false] - Whether the table is in loading state
 * @param {string} [props.emptyMessage='No data available'] - Message to display when there is no data
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} DataTable component
 */
const DataTable = ({
  columns,
  data,
  pagination,
  sortable = true,
  filterable = true,
  loading = false,
  emptyMessage = 'No data available',
  className = '',
  style = {}
}) => {
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [filters, setFilters] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  
  // Calculate items per page and total pages
  const itemsPerPage = pagination?.limit || data.length;
  const totalPages = pagination ? Math.ceil(pagination.total / itemsPerPage) : 1;
  
  // Update current page when offset changes
  useEffect(() => {
    if (pagination) {
      setCurrentPage(Math.floor(pagination.offset / pagination.limit) + 1);
    }
  }, [pagination]);
  
  // Handle sort request
  const requestSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };
  
  // Handle filter change
  const handleFilterChange = (key, value) => {
    setFilters(prevFilters => ({
      ...prevFilters,
      [key]: value
    }));
  };
  
  // Handle page change
  const handlePageChange = (page) => {
    if (pagination && pagination.onPageChange) {
      const newOffset = (page - 1) * pagination.limit;
      pagination.onPageChange({ limit: pagination.limit, offset: newOffset });
    }
    setCurrentPage(page);
  };
  
  // Handle search change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };
  
  // Sort and filter data
  const sortedAndFilteredData = useMemo(() => {
    // If pagination is provided, don't sort or filter locally
    if (pagination) {
      return data;
    }
    
    // Filter data
    let filteredData = data;
    if (filterable && searchTerm) {
      filteredData = data.filter(item => {
        return columns.some(column => {
          const value = item[column.field];
          if (value == null) return false;
          return String(value).toLowerCase().includes(searchTerm.toLowerCase());
        });
      });
    }
    
    // Apply column-specific filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        filteredData = filteredData.filter(item => {
          const itemValue = item[key];
          if (itemValue == null) return false;
          return String(itemValue).toLowerCase().includes(value.toLowerCase());
        });
      }
    });
    
    // Sort data
    if (sortable && sortConfig.key) {
      filteredData = [...filteredData].sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];
        
        if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1;
        if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1;
        
        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }
    
    return filteredData;
  }, [data, sortConfig, filters, searchTerm, columns, filterable, sortable, pagination]);
  
  // Paginate data
  const paginatedData = useMemo(() => {
    if (pagination) {
      return sortedAndFilteredData;
    }
    
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return sortedAndFilteredData.slice(startIndex, endIndex);
  }, [sortedAndFilteredData, currentPage, itemsPerPage, pagination]);
  
  // Render sort indicator
  const renderSortIndicator = (key) => {
    if (!sortable) return null;
    
    if (sortConfig.key === key) {
      return (
        <span className="ml-1">
          {sortConfig.direction === 'asc' ? '▲' : '▼'}
        </span>
      );
    }
    return null;
  };
  
  // Render pagination controls
  const renderPagination = () => {
    if (totalPages <= 1) return null;
    
    const pageNumbers = [];
    const maxPageButtons = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);
    
    if (endPage - startPage + 1 < maxPageButtons) {
      startPage = Math.max(1, endPage - maxPageButtons + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }
    
    return (
      <div className="flex justify-between items-center mt-4">
        <div className="text-sm text-gray-700">
          Showing {pagination ? pagination.offset + 1 : (currentPage - 1) * itemsPerPage + 1} to {Math.min(pagination ? pagination.offset + pagination.limit : currentPage * itemsPerPage, pagination ? pagination.total : sortedAndFilteredData.length)} of {pagination ? pagination.total : sortedAndFilteredData.length} entries
        </div>
        <div className="flex space-x-1">
          <button
            className={`px-3 py-1 rounded border ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-blue-600 hover:bg-blue-50'}`}
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Previous
          </button>
          {pageNumbers.map(number => (
            <button
              key={number}
              className={`px-3 py-1 rounded border ${currentPage === number ? 'bg-blue-600 text-white' : 'bg-white text-blue-600 hover:bg-blue-50'}`}
              onClick={() => handlePageChange(number)}
            >
              {number}
            </button>
          ))}
          <button
            className={`px-3 py-1 rounded border ${currentPage === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-blue-600 hover:bg-blue-50'}`}
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Next
          </button>
        </div>
      </div>
    );
  };
  
  return (
    <div className={`overflow-hidden ${className}`} style={style} data-testid="data-table">
      {filterable && (
        <div className="mb-4">
          <input
            type="text"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search..."
            value={searchTerm}
            onChange={handleSearchChange}
            data-testid="search-input"
          />
        </div>
      )}
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {columns.map(column => (
                <th
                  key={column.field}
                  scope="col"
                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${sortable ? 'cursor-pointer select-none' : ''}`}
                  onClick={() => sortable && requestSort(column.field)}
                  data-testid={`column-header-${column.field}`}
                >
                  <div className="flex items-center">
                    {column.header}
                    {renderSortIndicator(column.field)}
                  </div>
                  {column.filterable && (
                    <div className="mt-1">
                      <input
                        type="text"
                        className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                        placeholder={`Filter ${column.header}`}
                        value={filters[column.field] || ''}
                        onChange={(e) => handleFilterChange(column.field, e.target.value)}
                        onClick={(e) => e.stopPropagation()}
                        data-testid={`column-filter-${column.field}`}
                      />
                    </div>
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-4 text-center">
                  <div className="flex justify-center items-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading...</span>
                  </div>
                </td>
              </tr>
            ) : paginatedData.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-4 text-center text-gray-500">
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              paginatedData.map((row, rowIndex) => (
                <tr key={rowIndex} className="hover:bg-gray-50">
                  {columns.map(column => (
                    <td key={column.field} className="px-6 py-4 whitespace-nowrap">
                      {typeof column.render === 'function'
                        ? column.render(row[column.field], row)
                        : row[column.field]}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      
      {renderPagination()}
    </div>
  );
};

DataTable.propTypes = {
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      field: PropTypes.string.isRequired,
      header: PropTypes.string.isRequired,
      filterable: PropTypes.bool,
      render: PropTypes.func
    })
  ).isRequired,
  data: PropTypes.array.isRequired,
  pagination: PropTypes.shape({
    total: PropTypes.number.isRequired,
    limit: PropTypes.number.isRequired,
    offset: PropTypes.number.isRequired,
    onPageChange: PropTypes.func.isRequired
  }),
  sortable: PropTypes.bool,
  filterable: PropTypes.bool,
  loading: PropTypes.bool,
  emptyMessage: PropTypes.string,
  className: PropTypes.string,
  style: PropTypes.object
};

export default DataTable;
