/**
 * Simple Quantum State Inference Test
 * 
 * This example tests the basic functionality of the Quantum State Inference Layer.
 */

const QuantumStateInference = require('../src/csde/quantum/quantum_state_inference');

// Create a new Quantum State Inference instance
const quantumInference = new QuantumStateInference({
  entropyThreshold: 0.3,
  superpositionLimit: 10,
  collapseRate: 0.05,
  bayesianPriorWeight: 0.7,
  enableQuantumMemory: true,
  enableMetrics: true
});

// Example detection data
const detectionData = {
  detectionCapability: 0.75,
  threatSeverity: 0.65,
  confidence: 0.8,
  baselineSignals: 0.7,
  threats: {
    'threat-1': {
      severity: 0.8,
      confidence: 0.7,
      metadata: {
        type: 'malware',
        source: 'external',
        timestamp: new Date().toISOString()
      }
    },
    'threat-2': {
      severity: 0.6,
      confidence: 0.9,
      metadata: {
        type: 'ddos',
        source: 'external',
        timestamp: new Date().toISOString()
      }
    },
    'threat-3': {
      severity: 0.4,
      confidence: 0.5,
      metadata: {
        type: 'insider',
        source: 'internal',
        timestamp: new Date().toISOString()
      }
    }
  }
};

// Example context data
const contextData = {
  timePatterns: [
    {
      timestamp: new Date().toISOString(),
      window: 3600000 // 1 hour
    }
  ],
  locationPatterns: [
    {
      location: 'us-east-1'
    }
  ],
  componentType: 'security',
  componentCategory: 'firewall',
  componentTags: ['network', 'security', 'firewall']
};

console.log('=== Simple Quantum State Inference Test ===\n');

// Predict threats
console.log('Predicting threats...');
const prediction = quantumInference.predictThreats(detectionData, contextData);

// Display results
console.log('\nQuantum States:');
console.log(`- Total states: ${prediction.quantumStates.length}`);
console.log(`- Collapsed states: ${prediction.collapsedStates.length}`);

console.log('\nCollapsed States:');
prediction.collapsedStates.forEach((state, index) => {
  console.log(`\n${index + 1}. State ID: ${state.id}`);
  console.log(`   Type: ${state.type}`);
  console.log(`   Probability: ${state.collapsedProbability ? state.collapsedProbability.toFixed(4) : state.probability.toFixed(4)}`);
  console.log(`   Entropy: ${state.entropy.toFixed(4)}`);
});

console.log('\nActionable Intelligence:');
prediction.actionableIntelligence.forEach((item, index) => {
  console.log(`\n${index + 1}. State ID: ${item.stateId}`);
  console.log(`   Action Type: ${item.action.type}`);
  console.log(`   Description: ${item.action.description}`);
  console.log(`   Priority: ${item.action.priority.toFixed(4)}`);
  console.log(`   Confidence: ${item.confidence.toFixed(4)}`);
});

console.log('\nBayesian Results:');
console.log('- Priors:', Object.keys(prediction.bayesianResults.priors).length);
console.log('- Likelihoods:', Object.keys(prediction.bayesianResults.likelihoods).length);
console.log('- Posteriors:', Object.keys(prediction.bayesianResults.posteriors).length);

console.log('\nMetrics:');
console.log(`- Inference Count: ${prediction.metrics.inferenceCount}`);
console.log(`- Average Inference Time: ${prediction.metrics.averageInferenceTime.toFixed(2)} ms`);
console.log(`- Collapse Events: ${prediction.metrics.collapseEvents}`);
console.log(`- Superposition Events: ${prediction.metrics.superpositionEvents}`);
console.log(`- Certainty Rate: ${(prediction.metrics.certaintyRate * 100).toFixed(2)}%`);

console.log('\n=== End of Test ===');
