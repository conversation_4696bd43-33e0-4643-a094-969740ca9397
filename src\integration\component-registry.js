/**
 * Component Registry
 * 
 * This module provides a registry for components in the unified integration system.
 * It allows components to register themselves and discover other components.
 */

const EventEmitter = require('events');

/**
 * ComponentRegistry class
 */
class ComponentRegistry extends EventEmitter {
  /**
   * Create a new ComponentRegistry instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      ...options
    };
    
    // Initialize registry
    this.registry = new Map();
    
    // Component type definitions
    this.componentTypes = {
      TENSOR: 'tensor',
      VISUALIZATION: 'visualization',
      ANALYTICS: 'analytics',
      DATA_FLOW: 'dataflow',
      INTEGRATION: 'integration',
      UNKNOWN: 'unknown'
    };
    
    if (this.options.enableLogging) {
      console.log('ComponentRegistry initialized');
    }
  }
  
  /**
   * Register a component
   * @param {string} id - Component ID
   * @param {Object} component - Component instance
   * @param {Object} metadata - Component metadata
   * @returns {boolean} - Success status
   */
  register(id, component, metadata = {}) {
    // Validate parameters
    if (!id || typeof id !== 'string') {
      throw new Error('Component ID must be a non-empty string');
    }
    
    if (!component) {
      throw new Error('Component instance is required');
    }
    
    // Check if component already exists
    if (this.registry.has(id)) {
      throw new Error(`Component already registered: ${id}`);
    }
    
    // Determine component type if not provided
    if (!metadata.type) {
      metadata.type = this._determineComponentType(id, component);
    }
    
    // Register component
    this.registry.set(id, {
      instance: component,
      metadata: {
        type: metadata.type,
        version: metadata.version || '1.0.0',
        interfaces: metadata.interfaces || [],
        dependencies: metadata.dependencies || [],
        ...metadata
      },
      registeredAt: Date.now()
    });
    
    // Emit event
    this.emit('component-registered', {
      id,
      type: metadata.type,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ComponentRegistry: Registered component ${id} (${metadata.type})`);
    }
    
    return true;
  }
  
  /**
   * Determine component type based on ID and instance
   * @param {string} id - Component ID
   * @param {Object} component - Component instance
   * @returns {string} - Component type
   * @private
   */
  _determineComponentType(id, component) {
    const idLower = id.toLowerCase();
    
    if (idLower.includes('tensor') || 
        (component.constructor && component.constructor.name.includes('Tensor'))) {
      return this.componentTypes.TENSOR;
    }
    
    if (idLower.includes('visual') || 
        idLower.includes('chart') || 
        idLower.includes('graph') ||
        (component.constructor && component.constructor.name.includes('Visual'))) {
      return this.componentTypes.VISUALIZATION;
    }
    
    if (idLower.includes('analytics') || 
        idLower.includes('dashboard') || 
        idLower.includes('metric') ||
        (component.constructor && component.constructor.name.includes('Analytics'))) {
      return this.componentTypes.ANALYTICS;
    }
    
    if (idLower.includes('flow') || 
        idLower.includes('data') || 
        idLower.includes('stream') ||
        (component.constructor && component.constructor.name.includes('Flow'))) {
      return this.componentTypes.DATA_FLOW;
    }
    
    if (idLower.includes('integration') || 
        idLower.includes('connector') || 
        idLower.includes('bridge') ||
        (component.constructor && component.constructor.name.includes('Integration'))) {
      return this.componentTypes.INTEGRATION;
    }
    
    return this.componentTypes.UNKNOWN;
  }
  
  /**
   * Get a component by ID
   * @param {string} id - Component ID
   * @returns {Object|null} - Component instance or null if not found
   */
  get(id) {
    const component = this.registry.get(id);
    return component ? component.instance : null;
  }
  
  /**
   * Get component metadata
   * @param {string} id - Component ID
   * @returns {Object|null} - Component metadata or null if not found
   */
  getMetadata(id) {
    const component = this.registry.get(id);
    return component ? component.metadata : null;
  }
  
  /**
   * Check if a component exists
   * @param {string} id - Component ID
   * @returns {boolean} - Whether the component exists
   */
  has(id) {
    return this.registry.has(id);
  }
  
  /**
   * Unregister a component
   * @param {string} id - Component ID
   * @returns {boolean} - Success status
   */
  unregister(id) {
    // Check if component exists
    if (!this.registry.has(id)) {
      return false;
    }
    
    // Get component metadata
    const component = this.registry.get(id);
    const type = component.metadata.type;
    
    // Remove component
    this.registry.delete(id);
    
    // Emit event
    this.emit('component-unregistered', {
      id,
      type,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ComponentRegistry: Unregistered component ${id}`);
    }
    
    return true;
  }
  
  /**
   * Get all components
   * @param {string} [type] - Filter by component type
   * @returns {Array} - Array of component IDs
   */
  getAll(type) {
    const components = [];
    
    for (const [id, component] of this.registry.entries()) {
      if (!type || component.metadata.type === type) {
        components.push(id);
      }
    }
    
    return components;
  }
  
  /**
   * Get all components of a specific type
   * @param {string} type - Component type
   * @returns {Array} - Array of component IDs
   */
  getByType(type) {
    return this.getAll(type);
  }
  
  /**
   * Get all tensor components
   * @returns {Array} - Array of component IDs
   */
  getTensors() {
    return this.getByType(this.componentTypes.TENSOR);
  }
  
  /**
   * Get all visualization components
   * @returns {Array} - Array of component IDs
   */
  getVisualizations() {
    return this.getByType(this.componentTypes.VISUALIZATION);
  }
  
  /**
   * Get all analytics components
   * @returns {Array} - Array of component IDs
   */
  getAnalytics() {
    return this.getByType(this.componentTypes.ANALYTICS);
  }
  
  /**
   * Get all data flow components
   * @returns {Array} - Array of component IDs
   */
  getDataFlows() {
    return this.getByType(this.componentTypes.DATA_FLOW);
  }
  
  /**
   * Get component count
   * @param {string} [type] - Filter by component type
   * @returns {number} - Number of components
   */
  getCount(type) {
    if (type) {
      return this.getByType(type).length;
    }
    
    return this.registry.size;
  }
  
  /**
   * Clear the registry
   */
  clear() {
    this.registry.clear();
    
    // Emit event
    this.emit('registry-cleared', {
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log('ComponentRegistry: Registry cleared');
    }
  }
}

module.exports = ComponentRegistry;
