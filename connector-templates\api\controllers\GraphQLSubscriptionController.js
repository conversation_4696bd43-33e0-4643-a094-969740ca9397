/**
 * GraphQL Subscription Controller
 * 
 * This controller handles API requests related to GraphQL subscriptions.
 */

const GraphQLSubscriptionService = require('../services/GraphQLSubscriptionService');
const { ValidationError } = require('../utils/errors');

class GraphQLSubscriptionController {
  constructor() {
    this.subscriptionService = new GraphQLSubscriptionService();
    this.subscriptionCallbacks = new Map();
  }

  /**
   * Create a new subscription
   */
  async createSubscription(req, res, next) {
    try {
      const { endpoint, query, variables, headers, auth } = req.body;
      
      if (!endpoint) {
        throw new ValidationError('GraphQL endpoint URL is required');
      }
      
      if (!query) {
        throw new ValidationError('GraphQL subscription query is required');
      }
      
      // Create a unique client ID for this connection
      const clientId = req.headers['x-client-id'] || `client-${Date.now()}`;
      
      // Store callbacks for this client
      if (!this.subscriptionCallbacks.has(clientId)) {
        this.subscriptionCallbacks.set(clientId, {
          messages: [],
          errors: []
        });
      }
      
      // Define message and error handlers
      const onMessage = (message) => {
        const clientCallbacks = this.subscriptionCallbacks.get(clientId);
        if (clientCallbacks) {
          clientCallbacks.messages.push(message);
          // Limit the number of stored messages
          if (clientCallbacks.messages.length > 100) {
            clientCallbacks.messages.shift();
          }
        }
      };
      
      const onError = (error) => {
        const clientCallbacks = this.subscriptionCallbacks.get(clientId);
        if (clientCallbacks) {
          clientCallbacks.errors.push(error);
          // Limit the number of stored errors
          if (clientCallbacks.errors.length > 100) {
            clientCallbacks.errors.shift();
          }
        }
      };
      
      // Create the subscription
      const subscription = await this.subscriptionService.createSubscription(
        endpoint,
        query,
        variables || {},
        headers || {},
        auth || null,
        onMessage,
        onError
      );
      
      // Add client ID to the response
      subscription.clientId = clientId;
      
      res.status(201).json(subscription);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Subscription ID is required');
      }
      
      const result = this.subscriptionService.cancelSubscription(parseInt(id, 10));
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all active subscriptions
   */
  async getActiveSubscriptions(req, res, next) {
    try {
      const subscriptions = this.subscriptionService.getActiveSubscriptions();
      
      res.json(subscriptions);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get subscription by ID
   */
  async getSubscription(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Subscription ID is required');
      }
      
      const subscription = this.subscriptionService.getSubscription(parseInt(id, 10));
      
      res.json(subscription);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get subscription messages
   */
  async getSubscriptionMessages(req, res, next) {
    try {
      const { id } = req.params;
      const { clientId } = req.query;
      
      if (!id) {
        throw new ValidationError('Subscription ID is required');
      }
      
      if (!clientId) {
        throw new ValidationError('Client ID is required');
      }
      
      // Get client callbacks
      const clientCallbacks = this.subscriptionCallbacks.get(clientId);
      
      if (!clientCallbacks) {
        throw new ValidationError(`No subscription found for client ID ${clientId}`);
      }
      
      // Filter messages for this subscription
      const messages = clientCallbacks.messages.filter(msg => msg.id === parseInt(id, 10));
      
      res.json(messages);
      
      // Clear messages after sending
      clientCallbacks.messages = clientCallbacks.messages.filter(msg => msg.id !== parseInt(id, 10));
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get subscription errors
   */
  async getSubscriptionErrors(req, res, next) {
    try {
      const { id } = req.params;
      const { clientId } = req.query;
      
      if (!id) {
        throw new ValidationError('Subscription ID is required');
      }
      
      if (!clientId) {
        throw new ValidationError('Client ID is required');
      }
      
      // Get client callbacks
      const clientCallbacks = this.subscriptionCallbacks.get(clientId);
      
      if (!clientCallbacks) {
        throw new ValidationError(`No subscription found for client ID ${clientId}`);
      }
      
      // Filter errors for this subscription
      const errors = clientCallbacks.errors.filter(err => err.id === parseInt(id, 10));
      
      res.json(errors);
      
      // Clear errors after sending
      clientCallbacks.errors = clientCallbacks.errors.filter(err => err.id !== parseInt(id, 10));
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new GraphQLSubscriptionController();
