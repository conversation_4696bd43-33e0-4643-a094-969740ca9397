import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  BoltIcon,
  StarIcon
} from '@heroicons/react/24/outline';

const TriadicAffiliateDashboard = () => {
  const [products, setProducts] = useState([]);
  const [scanning, setScanning] = useState(false);
  const [stats, setStats] = useState({
    totalRevenue: 0,
    monthlySales: 0,
    conversionRate: 0,
    roi: 0
  });

  const scanProducts = async () => {
    setScanning(true);
    try {
      // Mock API call - replace with actual implementation
      const response = await fetch('/api/triadic-affiliate/scan');
      const data = await response.json();
      setProducts(data.products);
      updateStats(data.metrics);
    } catch (error) {
      console.error('Error scanning products:', error);
    } finally {
      setScanning(false);
    }
  };

  const updateStats = (metrics) => {
    setStats({
      totalRevenue: metrics.totalRevenue || 0,
      monthlySales: metrics.monthlySales || 0,
      conversionRate: metrics.conversionRate || 0,
      roi: metrics.roi || 0
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg shadow-lg p-6"
    >
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">
          Triadic Affiliate Dashboard
        </h2>
        <button
          onClick={scanProducts}
          className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg"
          disabled={scanning}
        >
          {scanning ? 'Scanning...' : 'Scan Products'}
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Stats Cards */}
        <motion.div
          whileHover={{ scale: 1.02 }}
          className="bg-blue-100 p-4 rounded-lg"
        >
          <div className="flex items-center">
            <CurrencyDollarIcon className="h-6 w-6 text-blue-500 mr-2" />
            <span className="text-xl font-bold">${stats.totalRevenue.toFixed(2)}</span>
          </div>
          <p className="text-sm text-gray-600">Total Revenue</p>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className="bg-green-100 p-4 rounded-lg"
        >
          <div className="flex items-center">
            <ChartBarIcon className="h-6 w-6 text-green-500 mr-2" />
            <span className="text-xl font-bold">{stats.monthlySales}</span>
          </div>
          <p className="text-sm text-gray-600">Monthly Sales</p>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className="bg-purple-100 p-4 rounded-lg"
        >
          <div className="flex items-center">
            <BoltIcon className="h-6 w-6 text-purple-500 mr-2" />
            <span className="text-xl font-bold">{stats.conversionRate * 100}%</span>
          </div>
          <p className="text-sm text-gray-600">Conversion Rate</p>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className="bg-yellow-100 p-4 rounded-lg"
        >
          <div className="flex items-center">
            <StarIcon className="h-6 w-6 text-yellow-500 mr-2" />
            <span className="text-xl font-bold">{stats.roi}%</span>
          </div>
          <p className="text-sm text-gray-600">ROI</p>
        </motion.div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="bg-gray-100">
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Product
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Vendor
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ψ Score
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                φ Resonance
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                κ Boost
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Commission
              </th>
            </tr>
          </thead>
          <tbody className="bg-white">
            {products.map((product, index) => (
              <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {product.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {product.vendor}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {product.ψ_score.toFixed(2)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {product.φ_resonance.toFixed(2)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {product.κ_boost}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ${product.commission}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </motion.div>
  );
};

export default TriadicAffiliateDashboard;
