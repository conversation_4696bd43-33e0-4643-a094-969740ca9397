/**
 * Directed Acyclic Graph (DAG) implementation for the Hybrid Verification System
 * 
 * This module implements a DAG data structure that serves as the foundation
 * for the verification system, replacing traditional blockchain.
 * 
 * The DAG structure allows for:
 * - Higher transaction throughput
 * - Parallel processing
 * - Reduced confirmation times
 * - Scalability
 * 
 * This implementation is aligned with Comphyology principles, particularly
 * the Nested Trinity structure and UUFT equation.
 */

const { v4: uuidv4 } = require('uuid');
const EventEmitter = require('events');
const debug = require('debug')('nova:dag');

/**
 * Represents a node in the DAG
 * @class DAGNode
 */
class DAGNode {
  /**
   * Create a new DAG node
   * @param {Object} options - Node options
   * @param {string} [options.id] - Node ID (generated if not provided)
   * @param {Object} options.data - Node data
   * @param {string} options.type - Node type
   * @param {Array<string>} [options.parents=[]] - Parent node IDs
   * @param {number} [options.weight=1] - Node weight
   * @param {Object} [options.metadata={}] - Additional metadata
   */
  constructor(options = {}) {
    this.id = options.id || uuidv4();
    this.data = options.data || {};
    this.type = options.type || 'transaction';
    this.parents = options.parents || [];
    this.children = [];
    this.weight = options.weight || 1;
    this.metadata = options.metadata || {};
    this.timestamp = options.timestamp || Date.now();
    this.status = options.status || 'pending';
    this.layer = options.layer || 'micro'; // micro, meso, or macro
  }

  /**
   * Add a child node
   * @param {string} childId - Child node ID
   */
  addChild(childId) {
    if (!this.children.includes(childId)) {
      this.children.push(childId);
    }
  }

  /**
   * Check if this node is a root node (no parents)
   * @returns {boolean} - True if this is a root node
   */
  isRoot() {
    return this.parents.length === 0;
  }

  /**
   * Check if this node is a leaf node (no children)
   * @returns {boolean} - True if this is a leaf node
   */
  isLeaf() {
    return this.children.length === 0;
  }

  /**
   * Convert the node to a JSON object
   * @returns {Object} - JSON representation of the node
   */
  toJSON() {
    return {
      id: this.id,
      data: this.data,
      type: this.type,
      parents: this.parents,
      children: this.children,
      weight: this.weight,
      metadata: this.metadata,
      timestamp: this.timestamp,
      status: this.status,
      layer: this.layer
    };
  }
}

/**
 * Directed Acyclic Graph implementation
 * @class DAG
 * @extends EventEmitter
 */
class DAG extends EventEmitter {
  /**
   * Create a new DAG
   * @param {Object} options - DAG options
   * @param {boolean} [options.enableLogging=false] - Enable logging
   * @param {boolean} [options.enableMetrics=false] - Enable metrics collection
   * @param {string} [options.id] - DAG ID (generated if not provided)
   * @param {Object} [options.metadata={}] - Additional metadata
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: false,
      enableMetrics: false,
      ...options
    };
    
    this.id = options.id || uuidv4();
    this.nodes = new Map();
    this.roots = new Set();
    this.leaves = new Set();
    this.metadata = options.metadata || {};
    this.metrics = {
      nodeCount: 0,
      edgeCount: 0,
      layerCounts: {
        micro: 0,
        meso: 0,
        macro: 0
      },
      typeCounts: {},
      creationTime: Date.now()
    };
    
    debug(`DAG created with ID: ${this.id}`);
  }
  
  /**
   * Add a node to the DAG
   * @param {Object} nodeData - Node data
   * @returns {DAGNode} - The created node
   */
  addNode(nodeData) {
    const node = new DAGNode(nodeData);
    
    // Add node to the graph
    this.nodes.set(node.id, node);
    
    // Update metrics
    this.metrics.nodeCount++;
    this.metrics.layerCounts[node.layer]++;
    this.metrics.typeCounts[node.type] = (this.metrics.typeCounts[node.type] || 0) + 1;
    
    // Check if this is a root node
    if (node.isRoot()) {
      this.roots.add(node.id);
    }
    
    // Check if this is a leaf node
    if (node.isLeaf()) {
      this.leaves.add(node.id);
    }
    
    // Update parent nodes
    for (const parentId of node.parents) {
      const parentNode = this.nodes.get(parentId);
      if (parentNode) {
        parentNode.addChild(node.id);
        
        // Parent is no longer a leaf
        this.leaves.delete(parentId);
        
        // Update edge count
        this.metrics.edgeCount++;
      } else {
        debug(`Warning: Parent node ${parentId} not found for node ${node.id}`);
      }
    }
    
    // Emit event
    this.emit('nodeAdded', node);
    
    debug(`Node added: ${node.id}, type: ${node.type}, layer: ${node.layer}`);
    
    return node;
  }
  
  /**
   * Get a node by ID
   * @param {string} nodeId - Node ID
   * @returns {DAGNode|null} - The node, or null if not found
   */
  getNode(nodeId) {
    return this.nodes.get(nodeId) || null;
  }
  
  /**
   * Get all nodes
   * @returns {Array<DAGNode>} - All nodes in the DAG
   */
  getAllNodes() {
    return Array.from(this.nodes.values());
  }
  
  /**
   * Get root nodes
   * @returns {Array<DAGNode>} - Root nodes in the DAG
   */
  getRootNodes() {
    return Array.from(this.roots).map(id => this.nodes.get(id)).filter(Boolean);
  }
  
  /**
   * Get leaf nodes
   * @returns {Array<DAGNode>} - Leaf nodes in the DAG
   */
  getLeafNodes() {
    return Array.from(this.leaves).map(id => this.nodes.get(id)).filter(Boolean);
  }
  
  /**
   * Get children of a node
   * @param {string} nodeId - Node ID
   * @returns {Array<DAGNode>} - Child nodes
   */
  getChildren(nodeId) {
    const node = this.nodes.get(nodeId);
    if (!node) return [];
    
    return node.children.map(id => this.nodes.get(id)).filter(Boolean);
  }
  
  /**
   * Get parents of a node
   * @param {string} nodeId - Node ID
   * @returns {Array<DAGNode>} - Parent nodes
   */
  getParents(nodeId) {
    const node = this.nodes.get(nodeId);
    if (!node) return [];
    
    return node.parents.map(id => this.nodes.get(id)).filter(Boolean);
  }
  
  /**
   * Update a node's status
   * @param {string} nodeId - Node ID
   * @param {string} status - New status
   * @returns {boolean} - True if the update was successful
   */
  updateNodeStatus(nodeId, status) {
    const node = this.nodes.get(nodeId);
    if (!node) return false;
    
    const oldStatus = node.status;
    node.status = status;
    
    // Emit event
    this.emit('nodeStatusChanged', { nodeId, oldStatus, newStatus: status });
    
    debug(`Node ${nodeId} status changed from ${oldStatus} to ${status}`);
    
    return true;
  }
  
  /**
   * Check if the DAG contains a cycle
   * @returns {boolean} - True if a cycle is detected
   */
  hasCycle() {
    // Implementation of cycle detection algorithm
    const visited = new Set();
    const recursionStack = new Set();
    
    const checkCycle = (nodeId) => {
      if (recursionStack.has(nodeId)) return true;
      if (visited.has(nodeId)) return false;
      
      visited.add(nodeId);
      recursionStack.add(nodeId);
      
      const node = this.nodes.get(nodeId);
      if (!node) return false;
      
      for (const childId of node.children) {
        if (checkCycle(childId)) return true;
      }
      
      recursionStack.delete(nodeId);
      return false;
    };
    
    for (const nodeId of this.nodes.keys()) {
      if (!visited.has(nodeId)) {
        if (checkCycle(nodeId)) return true;
      }
    }
    
    return false;
  }
  
  /**
   * Get metrics for the DAG
   * @returns {Object} - DAG metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Convert the DAG to a JSON object
   * @returns {Object} - JSON representation of the DAG
   */
  toJSON() {
    return {
      id: this.id,
      nodes: Array.from(this.nodes.values()).map(node => node.toJSON()),
      metadata: this.metadata,
      metrics: this.metrics
    };
  }
}

module.exports = {
  DAG,
  DAGNode
};
