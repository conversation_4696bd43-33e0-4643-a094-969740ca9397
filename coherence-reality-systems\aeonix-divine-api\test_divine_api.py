"""
AEONIX DIVINE API TEST SUITE
Test all 9 engines and divine simulation capabilities
"""

import asyncio
import httpx
import json
from datetime import datetime

# API Configuration
BASE_URL = "http://localhost:8000"
HEADERS = {"Content-Type": "application/json"}

class AeonixAPITester:
    def __init__(self):
        self.base_url = BASE_URL
        self.client = httpx.AsyncClient()
        
    async def test_divine_status(self):
        """Test divine status endpoint"""
        print("\n🌟 TESTING DIVINE STATUS")
        response = await self.client.get(f"{self.base_url}/divine/status")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Kernel: {data['kernel']}")
            print(f"⚡ Engines: {data['engines']}")
            print(f"🌊 φ-Coupling: {data['phi_coupling']}")
        return response.status_code == 200

    async def test_nepi_harmonics(self):
        """Test NEPI harmonic analysis"""
        print("\n🔢 TESTING NEPI HARMONIC ENGINE")
        payload = {
            "series": [25.06, 26.20, 27.80, 28.34, 29.10, 28.75, 28.97],
            "timeframe": "4h",
            "current_price": 28.34
        }
        
        response = await self.client.post(
            f"{self.base_url}/api/harmonics",
            json=payload,
            headers=HEADERS
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Engine: {data['engine']}")
            print(f"📐 Convergence Score: {data['convergence_score']:.3f}")
            print(f"🎯 Closest Level: {data['closest_level']}")
            print(f"🚀 Breakout Target: ${data['breakout_target']:.2f}")
            print(f"📊 Confidence: {data['confidence']:.3f}")
        return response.status_code == 200

    async def test_nefc_predators(self):
        """Test NEFC predator analysis"""
        print("\n🦈 TESTING NEFC PREDATOR ENGINE")
        payload = {
            "ticker": "GME",
            "short_interest": True,
            "options_flow": {"calls": 1200, "puts": 800}
        }
        
        response = await self.client.post(
            f"{self.base_url}/api/predators",
            json=payload,
            headers=HEADERS
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Engine: {data['engine']}")
            print(f"🎯 Predator Strength: {data['predator_strength']:.3f}")
            print(f"⚠️ Risk Level: {data['risk_level']}")
            print(f"📊 Short Clusters: {[f'${x:.2f}' for x in data['short_clusters']]}")
            print(f"🎲 Gamma Traps: {[f'${x:.2f}' for x in data['gamma_traps']]}")
        return response.status_code == 200

    async def test_nepe_prophecy(self):
        """Test NEPE prophetic engine"""
        print("\n🔮 TESTING NEPE PROPHETIC ENGINE")
        payload = {
            "event": "SEC files sudden lawsuit against major broker",
            "amplification": 1.63,
            "impact_scope": "market",
            "ticker": "GME"
        }
        
        response = await self.client.post(
            f"{self.base_url}/api/prophecy",
            json=payload,
            headers=HEADERS
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Engine: {data['engine']}")
            print(f"💥 Impact Score: {data['impact_score']:.3f}")
            print(f"📈 Volatility Spike: {data['volatility_spike']}")
            print(f"🎯 Probability Alteration: {data['probability_alteration']:.3f}")
            print(f"⏰ Timeline: {data['manifestation_timeline']}")
        return response.status_code == 200

    async def test_neee_sentiment(self):
        """Test NEEE sentiment engine"""
        print("\n💭 TESTING NEEE SENTIMENT ENGINE")
        payload = {
            "reddit_posts": 1500,
            "sentiment": "bullish",
            "meme_intensity": 0.7,
            "social_metrics": {"engagement": 0.85}
        }
        
        response = await self.client.post(
            f"{self.base_url}/api/sentiment",
            json=payload,
            headers=HEADERS
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Engine: {data['engine']}")
            print(f"🎭 Phase: {data['phase']}")
            print(f"🚀 FOMO Score: {data['retail_fomo_score']:.3f}")
            print(f"😨 Fear/Greed Index: {data['fear_greed_index']:.3f}")
            print(f"🔄 Reversal Probability: {data['cycle_reversal_probability']:.3f}")
        return response.status_code == 200

    async def test_ners_vulnerability(self):
        """Test NERS vulnerability engine"""
        print("\n🎯 TESTING NERS VULNERABILITY ENGINE")
        payload = {
            "ticker": "GME",
            "float_size": 76.35e6,
            "market_cap": 8.5e9,
            "beta": 1.8
        }
        
        response = await self.client.post(
            f"{self.base_url}/api/vulnerability",
            json=payload,
            headers=HEADERS
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Engine: {data['engine']}")
            print(f"🎯 Vulnerability Score: {data['vulnerability_score']:.3f}")
            print(f"💥 Squeeze Potential: {data['squeeze_potential']:.3f}")
            print(f"👥 Retail Target Score: {data['retail_target_score']:.3f}")
            print(f"⚠️ Risk Category: {data['risk_category']}")
        return response.status_code == 200

    async def test_divine_simulation(self):
        """Test complete divine simulation"""
        print("\n🌟 TESTING DIVINE SIMULATION (AEONIX KERNEL)")
        payload = {
            "ticker": "GME",
            "prophecy_event": "CFO resigns unexpectedly",
            "amplification": 1.63,
            "include_engines": ["NEPI", "NEFC", "NEPE", "NEEE", "NERS"]
        }
        
        response = await self.client.post(
            f"{self.base_url}/api/divine-simulation",
            json=payload,
            headers=HEADERS
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Ticker: {data['ticker']}")
            print(f"🎯 Target Price: ${data['target_price']:.2f}")
            print(f"🔮 Prophecy Impact: {data['prophecy_impact']:.3f}")
            print(f"📐 Fib Convergence: {data['fib_convergence']:.3f}")
            print(f"💭 Sentiment Phase: {data['sentiment_phase']}")
            print(f"⏰ Time Window: {data['time_window']}")
            print(f"📊 Confidence: {data['confidence']:.3f}")
            print(f"🔧 Engines Used: {len(data['engine_results'])}")
        return response.status_code == 200

    async def run_complete_test(self):
        """Run complete API test suite"""
        print("🚀 AEONIX DIVINE API TEST SUITE")
        print("=" * 60)
        
        tests = [
            ("Divine Status", self.test_divine_status),
            ("NEPI Harmonics", self.test_nepi_harmonics),
            ("NEFC Predators", self.test_nefc_predators),
            ("NEPE Prophecy", self.test_nepe_prophecy),
            ("NEEE Sentiment", self.test_neee_sentiment),
            ("NERS Vulnerability", self.test_ners_vulnerability),
            ("Divine Simulation", self.test_divine_simulation)
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                success = await test_func()
                results.append((test_name, success))
                print(f"{'✅' if success else '❌'} {test_name}: {'PASSED' if success else 'FAILED'}")
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {str(e)}")
                results.append((test_name, False))
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        passed = sum(1 for _, success in results if success)
        total = len(results)
        print(f"✅ Passed: {passed}/{total} ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🌟 ALL TESTS PASSED - DIVINE API FULLY OPERATIONAL!")
        elif passed >= total * 0.8:
            print("⚡ MOST TESTS PASSED - API LARGELY FUNCTIONAL")
        else:
            print("🔄 SOME TESTS FAILED - API NEEDS ATTENTION")
        
        await self.client.aclose()
        return passed, total

async def main():
    """Main test execution"""
    print("🔮 Starting AEONIX Divine API Tests...")
    print("📡 Make sure the API server is running on http://localhost:8000")
    print("💫 Run: uvicorn main:app --reload")
    print()
    
    tester = AeonixAPITester()
    await tester.run_complete_test()

if __name__ == "__main__":
    asyncio.run(main())
