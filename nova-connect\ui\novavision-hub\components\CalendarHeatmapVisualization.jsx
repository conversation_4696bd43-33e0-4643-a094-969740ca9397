/**
 * CalendarHeatmapVisualization Component
 * 
 * A component for visualizing data over time in a calendar heatmap format.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useTheme } from '../theme/ThemeContext';
import { usePerformance } from '../performance/usePerformance';

/**
 * CalendarHeatmapVisualization component
 * 
 * @param {Object} props - Component props
 * @param {Array} props.data - Chart data
 * @param {Object} [props.options] - Chart options
 * @param {string} [props.options.startDate] - Start date (ISO string)
 * @param {string} [props.options.endDate] - End date (ISO string)
 * @param {number} [props.options.cellSize=14] - Cell size in pixels
 * @param {number} [props.options.cellPadding=2] - Cell padding in pixels
 * @param {boolean} [props.options.showMonthLabels=true] - Whether to show month labels
 * @param {boolean} [props.options.showDayLabels=true] - Whether to show day labels
 * @param {boolean} [props.options.showTooltips=true] - Whether to show tooltips
 * @param {Array} [props.options.colorRange] - Color range for the heatmap
 * @param {Function} [props.options.tooltipContent] - Function to generate tooltip content
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} CalendarHeatmapVisualization component
 */
const CalendarHeatmapVisualization = ({
  data,
  options = {},
  className = '',
  style = {}
}) => {
  const { measureOperation } = usePerformance('CalendarHeatmapVisualization');
  const { theme } = useTheme();
  
  // State
  const [hoveredCell, setHoveredCell] = useState(null);
  const [calendarData, setCalendarData] = useState([]);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  
  // Default options
  const {
    startDate: startDateOption,
    endDate: endDateOption,
    cellSize = 14,
    cellPadding = 2,
    showMonthLabels = true,
    showDayLabels = true,
    showTooltips = true,
    colorRange = [
      theme.colors.surface,
      theme.colors.primary + '33',
      theme.colors.primary + '66',
      theme.colors.primary + '99',
      theme.colors.primary
    ],
    tooltipContent
  } = options;
  
  // Process data
  useEffect(() => {
    if (!data || data.length === 0) return;
    
    const processData = () => {
      // Determine date range
      const now = new Date();
      const endDate = endDateOption ? new Date(endDateOption) : now;
      const startDate = startDateOption
        ? new Date(startDateOption)
        : new Date(now.getFullYear(), now.getMonth() - 11, 1);
      
      // Create a map of date to value
      const dateValueMap = new Map();
      
      data.forEach(item => {
        const date = new Date(item.date);
        const dateKey = date.toISOString().split('T')[0];
        dateValueMap.set(dateKey, item);
      });
      
      // Find min and max values
      let minValue = Infinity;
      let maxValue = -Infinity;
      
      dateValueMap.forEach(item => {
        minValue = Math.min(minValue, item.value);
        maxValue = Math.max(maxValue, item.value);
      });
      
      // Generate calendar cells
      const cells = [];
      const currentDate = new Date(startDate);
      
      while (currentDate <= endDate) {
        const dateKey = currentDate.toISOString().split('T')[0];
        const dayOfWeek = currentDate.getDay();
        const weekOfYear = getWeekOfYear(currentDate);
        const month = currentDate.getMonth();
        const date = currentDate.getDate();
        const year = currentDate.getFullYear();
        
        const dataItem = dateValueMap.get(dateKey);
        const value = dataItem ? dataItem.value : 0;
        const colorIndex = dataItem
          ? Math.floor(((value - minValue) / (maxValue - minValue)) * (colorRange.length - 1))
          : 0;
        
        cells.push({
          date: new Date(currentDate),
          dateKey,
          dayOfWeek,
          weekOfYear,
          month,
          year,
          value,
          colorIndex,
          dataItem
        });
        
        // Move to next day
        currentDate.setDate(currentDate.getDate() + 1);
      }
      
      // Calculate dimensions
      const totalWidth = (cellSize + cellPadding) * 53 + (showDayLabels ? 30 : 0);
      const totalHeight = (cellSize + cellPadding) * 7 + (showMonthLabels ? 20 : 0);
      
      setCalendarData(cells);
      setDimensions({ width: totalWidth, height: totalHeight });
    };
    
    processData();
  }, [data, startDateOption, endDateOption, cellSize, cellPadding, showMonthLabels, showDayLabels, colorRange]);
  
  // Get week of year
  const getWeekOfYear = (date) => {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() + 4 - (d.getDay() || 7));
    const yearStart = new Date(d.getFullYear(), 0, 1);
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
  };
  
  // Get month positions for labels
  const getMonthPositions = () => {
    if (!calendarData || calendarData.length === 0) return [];
    
    const months = [];
    let currentMonth = -1;
    
    calendarData.forEach(cell => {
      if (cell.month !== currentMonth) {
        currentMonth = cell.month;
        months.push({
          month: currentMonth,
          x: (cell.weekOfYear - getWeekOfYear(new Date(cell.year, 0, 1))) * (cellSize + cellPadding) + (showDayLabels ? 30 : 0)
        });
      }
    });
    
    return months;
  };
  
  // Format date
  const formatDate = (date) => {
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  // Format value
  const formatValue = (value) => {
    return value.toLocaleString();
  };
  
  // Get tooltip content
  const getTooltipContent = (cell) => {
    if (tooltipContent && cell.dataItem) {
      return tooltipContent(cell.dataItem);
    }
    
    return `
      <div>
        <div><strong>${formatDate(cell.date)}</strong></div>
        <div>Value: ${formatValue(cell.value)}</div>
      </div>
    `;
  };
  
  // Handle mouse enter
  const handleMouseEnter = (cell) => {
    setHoveredCell(cell);
  };
  
  // Handle mouse leave
  const handleMouseLeave = () => {
    setHoveredCell(null);
  };
  
  // Get month label
  const getMonthLabel = (month) => {
    return new Date(2000, month, 1).toLocaleDateString(undefined, { month: 'short' });
  };
  
  // Get day label
  const getDayLabel = (day) => {
    return ['S', 'M', 'T', 'W', 'T', 'F', 'S'][day];
  };
  
  // Get month positions
  const monthPositions = getMonthPositions();
  
  return (
    <div
      className={`relative ${className}`}
      style={{ ...style, minHeight: '200px' }}
      data-testid="calendar-heatmap-visualization"
    >
      <svg
        width={dimensions.width}
        height={dimensions.height}
        viewBox={`0 0 ${dimensions.width} ${dimensions.height}`}
      >
        {/* Month labels */}
        {showMonthLabels && monthPositions.map((month, index) => (
          <text
            key={index}
            x={month.x}
            y={10}
            fontSize={10}
            textAnchor="start"
            fill={theme.colors.textSecondary}
          >
            {getMonthLabel(month.month)}
          </text>
        ))}
        
        {/* Day labels */}
        {showDayLabels && [0, 1, 2, 3, 4, 5, 6].map(day => (
          <text
            key={day}
            x={10}
            y={(day * (cellSize + cellPadding)) + (cellSize / 2) + (showMonthLabels ? 20 : 0)}
            fontSize={10}
            textAnchor="middle"
            dominantBaseline="middle"
            fill={theme.colors.textSecondary}
          >
            {getDayLabel(day)}
          </text>
        ))}
        
        {/* Calendar cells */}
        {calendarData.map((cell, index) => {
          const x = (cell.weekOfYear - getWeekOfYear(new Date(cell.year, 0, 1))) * (cellSize + cellPadding) + (showDayLabels ? 30 : 0);
          const y = (cell.dayOfWeek * (cellSize + cellPadding)) + (showMonthLabels ? 20 : 0);
          
          return (
            <rect
              key={index}
              x={x}
              y={y}
              width={cellSize}
              height={cellSize}
              fill={colorRange[cell.colorIndex] || colorRange[0]}
              stroke={hoveredCell && hoveredCell.dateKey === cell.dateKey ? theme.colors.textPrimary : 'none'}
              strokeWidth={1}
              rx={2}
              ry={2}
              onMouseEnter={() => handleMouseEnter(cell)}
              onMouseLeave={handleMouseLeave}
              style={{ cursor: 'pointer' }}
              data-date={cell.dateKey}
              data-value={cell.value}
            />
          );
        })}
      </svg>
      
      {/* Tooltip */}
      {showTooltips && hoveredCell && (
        <div
          className="absolute z-10 bg-surface border border-divider rounded-md shadow-lg p-2 text-sm pointer-events-none"
          style={{
            left: `${(hoveredCell.weekOfYear - getWeekOfYear(new Date(hoveredCell.year, 0, 1))) * (cellSize + cellPadding) + (showDayLabels ? 30 : 0) + cellSize / 2}px`,
            top: `${(hoveredCell.dayOfWeek * (cellSize + cellPadding)) + (showMonthLabels ? 20 : 0) - 40}px`,
            transform: 'translateX(-50%)'
          }}
          dangerouslySetInnerHTML={{ __html: getTooltipContent(hoveredCell) }}
        />
      )}
      
      {/* Legend */}
      <div className="flex items-center justify-end mt-2">
        <div className="text-xs text-textSecondary mr-2">Less</div>
        {colorRange.map((color, index) => (
          <div
            key={index}
            className="w-3 h-3 mx-0.5"
            style={{ backgroundColor: color }}
          />
        ))}
        <div className="text-xs text-textSecondary ml-2">More</div>
      </div>
    </div>
  );
};

CalendarHeatmapVisualization.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      date: PropTypes.string.isRequired,
      value: PropTypes.number.isRequired
    })
  ).isRequired,
  options: PropTypes.shape({
    startDate: PropTypes.string,
    endDate: PropTypes.string,
    cellSize: PropTypes.number,
    cellPadding: PropTypes.number,
    showMonthLabels: PropTypes.bool,
    showDayLabels: PropTypes.bool,
    showTooltips: PropTypes.bool,
    colorRange: PropTypes.arrayOf(PropTypes.string),
    tooltipContent: PropTypes.func
  }),
  className: PropTypes.string,
  style: PropTypes.object
};

export default CalendarHeatmapVisualization;
