/**
 * NovaCore Workflow Controller
 *
 * This controller handles API requests related to workflows.
 * Nova<PERSON>low is the Universal Compliance Workflow Orchestrator (UCWO) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const { WorkflowService, WorkflowTemplateService } = require('../services');
const logger = require('../../../config/logger');

class WorkflowController {
  /**
   * Create a new workflow
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createWorkflow(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const workflow = await WorkflowService.createWorkflow(req.body, userId);

      res.status(201).json({
        success: true,
        data: workflow
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create workflow from template
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createWorkflowFromTemplate(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const { templateId } = req.params;

      const workflow = await WorkflowService.createWorkflowFromTemplate(templateId, req.body, userId);

      res.status(201).json({
        success: true,
        data: workflow
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all workflows
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllWorkflows(req, res, next) {
    try {
      const { organizationId } = req.params;

      // Extract filter parameters from query
      const filter = {};

      if (req.query.type) filter.type = req.query.type;
      if (req.query.category) filter.category = req.query.category;
      if (req.query.status) filter.status = req.query.status;
      if (req.query.executionStatus) filter.executionStatus = req.query.executionStatus;
      if (req.query.priority) filter.priority = req.query.priority;
      if (req.query.tags) filter.tags = req.query.tags.split(',');
      if (req.query.frameworks) filter.frameworks = req.query.frameworks.split(',');
      if (req.query.search) filter.search = req.query.search;

      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };

      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }

      const result = await WorkflowService.getAllWorkflows(organizationId, filter, options);

      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get workflow by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getWorkflowById(req, res, next) {
    try {
      const workflow = await WorkflowService.getWorkflowById(req.params.id);

      res.status(200).json({
        success: true,
        data: workflow
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update workflow
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateWorkflow(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const workflow = await WorkflowService.updateWorkflow(req.params.id, req.body, userId);

      res.status(200).json({
        success: true,
        data: workflow
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete workflow
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async deleteWorkflow(req, res, next) {
    try {
      await WorkflowService.deleteWorkflow(req.params.id);

      res.status(200).json({
        success: true,
        message: 'Workflow deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Activate workflow
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async activateWorkflow(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const workflow = await WorkflowService.activateWorkflow(req.params.id, userId);

      res.status(200).json({
        success: true,
        data: workflow
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Start workflow execution
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async startWorkflow(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const execution = await WorkflowService.startWorkflow(req.params.id, req.body, userId);

      res.status(200).json({
        success: true,
        data: execution
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get workflow executions
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getWorkflowExecutions(req, res, next) {
    try {
      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };

      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }

      const result = await WorkflowService.getWorkflowExecutions(req.params.id, options);

      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get execution by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getExecutionById(req, res, next) {
    try {
      const execution = await WorkflowService.getExecutionById(req.params.id);

      res.status(200).json({
        success: true,
        data: execution
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Complete task in workflow execution
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async completeTask(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const { executionId, taskId } = req.params;

      const execution = await WorkflowService.completeTask(executionId, taskId, req.body, userId);

      res.status(200).json({
        success: true,
        data: execution
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Fail task in workflow execution
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async failTask(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const { executionId, taskId } = req.params;

      const execution = await WorkflowService.failTask(executionId, taskId, req.body, userId);

      res.status(200).json({
        success: true,
        data: execution
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Skip task in workflow execution
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async skipTask(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const { executionId, taskId } = req.params;

      const execution = await WorkflowService.skipTask(executionId, taskId, req.body, userId);

      res.status(200).json({
        success: true,
        data: execution
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Pause workflow execution
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async pauseExecution(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const { id } = req.params;

      const execution = await WorkflowService.pauseExecution(id, userId);

      res.status(200).json({
        success: true,
        data: execution
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Resume workflow execution
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async resumeExecution(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const { id } = req.params;

      const execution = await WorkflowService.resumeExecution(id, userId);

      res.status(200).json({
        success: true,
        data: execution
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Cancel workflow execution
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async cancelExecution(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const { id } = req.params;

      const execution = await WorkflowService.cancelExecution(id, userId);

      res.status(200).json({
        success: true,
        data: execution
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Process workflow execution
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async processExecution(req, res, next) {
    try {
      const { id } = req.params;

      const execution = await WorkflowService.processExecution(id);

      res.status(200).json({
        success: true,
        data: execution
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new workflow template
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createTemplate(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const template = await WorkflowTemplateService.createTemplate(req.body, userId);

      res.status(201).json({
        success: true,
        data: template
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all workflow templates
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllTemplates(req, res, next) {
    try {
      const { organizationId } = req.params;

      // Extract filter parameters from query
      const filter = {};

      if (req.query.type) filter.type = req.query.type;
      if (req.query.category) filter.category = req.query.category;
      if (req.query.status) filter.status = req.query.status;
      if (req.query.tags) filter.tags = req.query.tags.split(',');
      if (req.query.frameworks) filter.frameworks = req.query.frameworks.split(',');
      if (req.query.isDefault) filter.isDefault = req.query.isDefault;
      if (req.query.search) filter.search = req.query.search;

      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };

      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }

      const result = await WorkflowTemplateService.getAllTemplates(organizationId, filter, options);

      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get workflow template by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getTemplateById(req, res, next) {
    try {
      const template = await WorkflowTemplateService.getTemplateById(req.params.id);

      res.status(200).json({
        success: true,
        data: template
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update workflow template
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateTemplate(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const template = await WorkflowTemplateService.updateTemplate(req.params.id, req.body, userId);

      res.status(200).json({
        success: true,
        data: template
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete workflow template
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async deleteTemplate(req, res, next) {
    try {
      await WorkflowTemplateService.deleteTemplate(req.params.id);

      res.status(200).json({
        success: true,
        message: 'Workflow template deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Set template as default
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async setTemplateAsDefault(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const template = await WorkflowTemplateService.setAsDefault(req.params.id, userId);

      res.status(200).json({
        success: true,
        data: template
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create new version of template
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createNewTemplateVersion(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const template = await WorkflowTemplateService.createNewVersion(req.params.id, req.body, userId);

      res.status(201).json({
        success: true,
        data: template
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get default template
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getDefaultTemplate(req, res, next) {
    try {
      const { organizationId } = req.params;
      const type = req.query.type || 'compliance';

      const template = await WorkflowTemplateService.getDefaultTemplate(organizationId, type);

      res.status(200).json({
        success: true,
        data: template
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Import template from JSON
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async importTemplate(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const { organizationId } = req.params;

      const template = await WorkflowTemplateService.importTemplate(req.body, organizationId, userId);

      res.status(201).json({
        success: true,
        data: template
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new WorkflowController();
