/**
 * FeatureFlaggedComponent
 * 
 * A higher-order component that conditionally renders content based on feature flags.
 */

import React from 'react';
import { useFeatureFlag } from '../feature-flags/useFeatureFlag';

/**
 * FeatureFlaggedComponent
 * @param {object} props - Component props
 * @param {string} props.category - The feature category
 * @param {string} props.feature - The feature name
 * @param {React.ReactNode} props.children - The content to render if the feature is enabled
 * @param {React.ReactNode} [props.fallback] - The content to render if the feature is disabled
 * @returns {React.ReactNode} - The rendered component
 */
export function FeatureFlaggedComponent({ category, feature, children, fallback = null }) {
  const isEnabled = useFeatureFlag(category, feature);
  
  if (!isEnabled) {
    return fallback;
  }
  
  return children;
}

/**
 * UpgradePrompt
 * @param {object} props - Component props
 * @param {string} props.featureName - The name of the feature
 * @param {string} [props.description] - Description of the feature
 * @param {string} [props.buttonText] - Text for the upgrade button
 * @param {function} [props.onUpgradeClick] - Function to call when the upgrade button is clicked
 * @returns {React.ReactNode} - The rendered component
 */
export function UpgradePrompt({ 
  featureName, 
  description = 'This feature is not available in your current plan.', 
  buttonText = 'Upgrade to access this feature',
  onUpgradeClick = () => {}
}) {
  return (
    <div className="bg-gray-100 p-4 rounded-lg text-center">
      <h3 className="text-lg font-semibold text-gray-500">{featureName}</h3>
      <p className="text-gray-500">{description}</p>
      <button 
        className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        onClick={onUpgradeClick}
      >
        {buttonText}
      </button>
    </div>
  );
}

/**
 * Example usage:
 * 
 * ```jsx
 * <FeatureFlaggedComponent 
 *   category="dashboard" 
 *   feature="analytics"
 *   fallback={<UpgradePrompt featureName="Analytics" />}
 * >
 *   <AnalyticsDashboard />
 * </FeatureFlaggedComponent>
 * ```
 */

export default FeatureFlaggedComponent;
