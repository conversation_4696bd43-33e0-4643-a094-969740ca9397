# Interactive Controls

This module provides interactive controls for manipulating the Self-Healing Tensor, 3D Tensor Visualization, Analytics Dashboard, and other components.

## Overview

The interactive controls system enables users to interact with the system components in real-time. It provides:

- Control panel for managing controls and actions
- Tensor controls for manipulating tensors
- Visualization controls for configuring visualizations
- Analytics controls for querying and monitoring
- WebSocket-based communication for real-time updates

## Architecture

The interactive controls system consists of the following components:

### Core Components

- **ControlPanel**: The main control panel that manages controls and actions.
- **TensorControls**: Controls for manipulating tensors.
- **VisualizationControls**: Controls for configuring visualizations.
- **AnalyticsControls**: Controls for querying and monitoring analytics.

### Control Flow

1. The control panel registers controls and actions.
2. Users interact with the controls through a UI.
3. Control changes are sent to the control panel.
4. The control panel processes the changes and sends them to the appropriate component.
5. Components respond to the changes and update their state.
6. State changes are propagated back to the control panel and UI.

## Usage

### Basic Usage

```javascript
const { createUnifiedControlSystem } = require('./controls');
const { createRealTimeCommunication } = require('./websocket');
const { createUnifiedIntegration } = require('./integration');
const SelfHealingTensor = require('./quantum/self-healing-tensor');

// Create components
const selfHealingTensor = new SelfHealingTensor();
const visualizationSystem = createVisualizationSystem();
const analyticsDashboard = createAnalyticsDashboard();

// Create unified integration
const unifiedIntegration = createUnifiedIntegration({
  tensor: selfHealingTensor,
  visualization: visualizationSystem,
  analytics: analyticsDashboard
});

// Create real-time communication system
const realTimeCommunication = createRealTimeCommunication({
  tensorAdapter: unifiedIntegration.adapters.tensor,
  visualizationAdapter: unifiedIntegration.adapters.visualization,
  analyticsAdapter: unifiedIntegration.adapters.analytics
});

// Create unified control system
const unifiedControlSystem = createUnifiedControlSystem({
  wsUrl: 'ws://localhost:3001/ws'
});

// Connect unified control system
await unifiedControlSystem.connect();

// Register a tensor
await unifiedControlSystem.registerTensor('example-tensor', {
  values: [0.5, 0.6, 0.7, 0.8, 0.9]
});

// Create a visualization
await unifiedControlSystem.createVisualization('3d_tensor_visualization', {
  tensor: {
    values: [0.5, 0.6, 0.7, 0.8, 0.9],
    health: 1.0,
    entropyContainment: 0.0
  },
  dimensions: [5, 1, 1]
}, {
  renderMode: 'high-quality',
  showAxes: true,
  showGrid: true
});

// Execute a query
await unifiedControlSystem.executeQuery('SELECT * FROM tensor_metrics');
```

### Advanced Usage

For more advanced usage, see the example in `examples/interactive-controls-example.js`.

## API Reference

### ControlPanel

The main control panel that manages controls and actions.

#### Methods

- `connect()`: Connect to the WebSocket server.
- `disconnect()`: Disconnect from the WebSocket server.
- `registerControl(controlId, control, groupId)`: Register a control.
- `unregisterControl(controlId)`: Unregister a control.
- `getControl(controlId)`: Get a control.
- `getControls(groupId)`: Get all controls.
- `getControlValue(controlId)`: Get a control value.
- `getControlValues(groupId)`: Get all control values.
- `setControlValue(controlId, value)`: Set a control value.
- `executeAction(action, params)`: Execute an action.

### TensorControls

Controls for manipulating tensors.

#### Methods

- `connect()`: Connect to the WebSocket server.
- `disconnect()`: Disconnect from the WebSocket server.
- `registerTensor(id, tensor, domain)`: Register a tensor.
- `healTensor(id)`: Heal a tensor.
- `damageTensor(id, damageLevel)`: Damage a tensor.

### VisualizationControls

Controls for configuring visualizations.

#### Methods

- `connect()`: Connect to the WebSocket server.
- `disconnect()`: Disconnect from the WebSocket server.
- `createVisualization(visualizationType, data, options)`: Create a visualization.
- `updateVisualization(id, data)`: Update a visualization.
- `deleteVisualization(id)`: Delete a visualization.

### AnalyticsControls

Controls for querying and monitoring analytics.

#### Methods

- `connect()`: Connect to the WebSocket server.
- `disconnect()`: Disconnect from the WebSocket server.
- `executeQuery(query, params)`: Execute a query.
- `refreshMetrics()`: Refresh metrics.
- `refreshDashboard(id)`: Refresh a dashboard.

## Control Types

The control panel supports the following control types:

- **select**: A dropdown select control.
- **checkbox**: A checkbox control.
- **slider**: A slider control.
- **number**: A numeric input control.
- **text**: A text input control.
- **textarea**: A multi-line text input control.
- **json**: A JSON editor control.
- **progress**: A progress bar control.

## Events

The interactive controls system uses events to communicate between components. Here are the main events:

### ControlPanel Events

- `connected`: Emitted when the control panel connects.
- `disconnected`: Emitted when the control panel disconnects.
- `control-registered`: Emitted when a control is registered.
- `control-unregistered`: Emitted when a control is unregistered.
- `control-value-changed`: Emitted when a control value changes.
- `action-executed`: Emitted when an action is executed.
- `control-event`: Emitted when a control event occurs.
- `action`: Emitted when an action is received.

### TensorControls Events

- `tensor-registered`: Emitted when a tensor is registered.
- `tensor-healed`: Emitted when a tensor is healed.
- `tensor-damaged`: Emitted when a tensor is damaged.

### VisualizationControls Events

- `visualization-created`: Emitted when a visualization is created.
- `visualization-updated`: Emitted when a visualization is updated.
- `visualization-deleted`: Emitted when a visualization is deleted.

### AnalyticsControls Events

- `metrics-updated`: Emitted when metrics are updated.
- `dashboard-updated`: Emitted when a dashboard is updated.
- `query-started`: Emitted when a query starts.
- `query-completed`: Emitted when a query completes.
- `query-error`: Emitted when a query errors.

## Examples

See the `examples` directory for usage examples:

- `interactive-controls-example.js`: A complete example that demonstrates how to use the interactive controls to manipulate the Self-Healing Tensor, 3D Tensor Visualization, and Analytics Dashboard.
