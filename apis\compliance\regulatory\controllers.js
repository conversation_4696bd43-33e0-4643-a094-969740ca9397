const { v4: uuidv4 } = require('uuid');
const models = require('./models');

/**
 * Get a list of regulatory frameworks
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworks = (req, res) => {
  try {
    const { page = 1, limit = 10, category, jurisdiction, status, sortBy = 'name', sortOrder = 'asc' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter frameworks based on query parameters
    let filteredFrameworks = [...models.regulatoryFrameworks];

    if (category) {
      filteredFrameworks = filteredFrameworks.filter(framework => framework.category === category);
    }

    if (jurisdiction) {
      filteredFrameworks = filteredFrameworks.filter(framework => framework.jurisdiction === jurisdiction);
    }

    if (status) {
      filteredFrameworks = filteredFrameworks.filter(framework => framework.status === status);
    }

    // Sort frameworks
    filteredFrameworks.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedFrameworks = filteredFrameworks.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalFrameworks = filteredFrameworks.length;
    const totalPages = Math.ceil(totalFrameworks / limitNum);

    res.json({
      data: paginatedFrameworks,
      pagination: {
        total: totalFrameworks,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getFrameworks:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific regulatory framework by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworkById = (req, res) => {
  try {
    const { id } = req.params;
    const framework = models.regulatoryFrameworks.find(f => f.id === id);

    if (!framework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory framework with ID ${id} not found`
      });
    }

    res.json({ data: framework });
  } catch (error) {
    console.error('Error in getFrameworkById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new regulatory framework
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createFramework = (req, res) => {
  try {
    const {
      name,
      shortName,
      description,
      version,
      effectiveDate,
      category,
      jurisdiction,
      status,
      website,
      enforcementAuthority
    } = req.body;

    // Create a new framework with a unique ID
    const newFramework = {
      id: `rf-${uuidv4().substring(0, 4)}`,
      name,
      shortName,
      description,
      version,
      effectiveDate,
      category,
      jurisdiction,
      status,
      website: website || '',
      enforcementAuthority: enforcementAuthority || '',
      lastUpdated: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new framework to the collection
    models.regulatoryFrameworks.push(newFramework);

    res.status(201).json({
      data: newFramework,
      message: 'Regulatory framework created successfully'
    });
  } catch (error) {
    console.error('Error in createFramework:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing regulatory framework
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateFramework = (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      shortName,
      description,
      version,
      effectiveDate,
      category,
      jurisdiction,
      status,
      website,
      enforcementAuthority
    } = req.body;

    // Find the framework to update
    const frameworkIndex = models.regulatoryFrameworks.findIndex(f => f.id === id);

    if (frameworkIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory framework with ID ${id} not found`
      });
    }

    // Create updated framework object
    const updatedFramework = {
      ...models.regulatoryFrameworks[frameworkIndex],
      name: name !== undefined ? name : models.regulatoryFrameworks[frameworkIndex].name,
      shortName: shortName !== undefined ? shortName : models.regulatoryFrameworks[frameworkIndex].shortName,
      description: description !== undefined ? description : models.regulatoryFrameworks[frameworkIndex].description,
      version: version !== undefined ? version : models.regulatoryFrameworks[frameworkIndex].version,
      effectiveDate: effectiveDate !== undefined ? effectiveDate : models.regulatoryFrameworks[frameworkIndex].effectiveDate,
      category: category !== undefined ? category : models.regulatoryFrameworks[frameworkIndex].category,
      jurisdiction: jurisdiction !== undefined ? jurisdiction : models.regulatoryFrameworks[frameworkIndex].jurisdiction,
      status: status !== undefined ? status : models.regulatoryFrameworks[frameworkIndex].status,
      website: website !== undefined ? website : models.regulatoryFrameworks[frameworkIndex].website,
      enforcementAuthority: enforcementAuthority !== undefined ? enforcementAuthority : models.regulatoryFrameworks[frameworkIndex].enforcementAuthority,
      lastUpdated: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Replace the old framework with the updated one
    models.regulatoryFrameworks[frameworkIndex] = updatedFramework;

    res.json({
      data: updatedFramework,
      message: 'Regulatory framework updated successfully'
    });
  } catch (error) {
    console.error('Error in updateFramework:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a regulatory framework
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteFramework = (req, res) => {
  try {
    const { id } = req.params;

    // Find the framework to delete
    const frameworkIndex = models.regulatoryFrameworks.findIndex(f => f.id === id);

    if (frameworkIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory framework with ID ${id} not found`
      });
    }

    // Check if there are any requirements associated with this framework
    const hasRequirements = models.regulatoryRequirements.some(r => r.frameworkId === id);

    if (hasRequirements) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot delete framework with ID ${id} because it has associated requirements. Remove the requirements first.`
      });
    }

    // Remove the framework from the collection
    models.regulatoryFrameworks.splice(frameworkIndex, 1);

    res.json({
      message: 'Regulatory framework deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteFramework:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of regulatory requirements
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getRequirements = (req, res) => {
  try {
    const { page = 1, limit = 10, frameworkId, category, priority, status, sortBy = 'name', sortOrder = 'asc' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter requirements based on query parameters
    let filteredRequirements = [...models.regulatoryRequirements];

    if (frameworkId) {
      filteredRequirements = filteredRequirements.filter(req => req.frameworkId === frameworkId);
    }

    if (category) {
      filteredRequirements = filteredRequirements.filter(req => req.category === category);
    }

    if (priority) {
      filteredRequirements = filteredRequirements.filter(req => req.priority === priority);
    }

    if (status) {
      filteredRequirements = filteredRequirements.filter(req => req.status === status);
    }

    // Sort requirements
    filteredRequirements.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedRequirements = filteredRequirements.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalRequirements = filteredRequirements.length;
    const totalPages = Math.ceil(totalRequirements / limitNum);

    res.json({
      data: paginatedRequirements,
      pagination: {
        total: totalRequirements,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getRequirements:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific regulatory requirement by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getRequirementById = (req, res) => {
  try {
    const { id } = req.params;
    const requirement = models.regulatoryRequirements.find(r => r.id === id);

    if (!requirement) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory requirement with ID ${id} not found`
      });
    }

    res.json({ data: requirement });
  } catch (error) {
    console.error('Error in getRequirementById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new regulatory requirement
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createRequirement = (req, res) => {
  try {
    const {
      frameworkId,
      code,
      name,
      description,
      article,
      section,
      category,
      priority,
      status,
      applicableJurisdictions,
      controlObjectives,
      relatedRequirements
    } = req.body;

    // Validate that the framework exists
    const framework = models.regulatoryFrameworks.find(f => f.id === frameworkId);
    if (!framework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory framework with ID ${frameworkId} not found`
      });
    }

    // Validate that all related requirements exist
    if (relatedRequirements && relatedRequirements.length > 0) {
      const invalidRequirements = relatedRequirements.filter(
        reqId => !models.regulatoryRequirements.some(r => r.id === reqId)
      );

      if (invalidRequirements.length > 0) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `The following requirement IDs do not exist: ${invalidRequirements.join(', ')}`
        });
      }
    }

    // Create a new requirement with a unique ID
    const newRequirement = {
      id: `rr-${uuidv4().substring(0, 4)}`,
      frameworkId,
      code,
      name,
      description,
      article: article || '',
      section: section || '',
      category,
      priority,
      status,
      applicableJurisdictions,
      controlObjectives: controlObjectives || [],
      relatedRequirements: relatedRequirements || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new requirement to the collection
    models.regulatoryRequirements.push(newRequirement);

    res.status(201).json({
      data: newRequirement,
      message: 'Regulatory requirement created successfully'
    });
  } catch (error) {
    console.error('Error in createRequirement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing regulatory requirement
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateRequirement = (req, res) => {
  try {
    const { id } = req.params;
    const {
      frameworkId,
      code,
      name,
      description,
      article,
      section,
      category,
      priority,
      status,
      applicableJurisdictions,
      controlObjectives,
      relatedRequirements
    } = req.body;

    // Find the requirement to update
    const requirementIndex = models.regulatoryRequirements.findIndex(r => r.id === id);

    if (requirementIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory requirement with ID ${id} not found`
      });
    }

    // If frameworkId is being updated, validate that the new framework exists
    if (frameworkId && frameworkId !== models.regulatoryRequirements[requirementIndex].frameworkId) {
      const framework = models.regulatoryFrameworks.find(f => f.id === frameworkId);
      if (!framework) {
        return res.status(404).json({
          error: 'Not Found',
          message: `Regulatory framework with ID ${frameworkId} not found`
        });
      }
    }

    // Validate that all related requirements exist
    if (relatedRequirements && relatedRequirements.length > 0) {
      const invalidRequirements = relatedRequirements.filter(
        reqId => !models.regulatoryRequirements.some(r => r.id === reqId)
      );

      if (invalidRequirements.length > 0) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `The following requirement IDs do not exist: ${invalidRequirements.join(', ')}`
        });
      }
    }

    // Create updated requirement object
    const updatedRequirement = {
      ...models.regulatoryRequirements[requirementIndex],
      frameworkId: frameworkId || models.regulatoryRequirements[requirementIndex].frameworkId,
      code: code !== undefined ? code : models.regulatoryRequirements[requirementIndex].code,
      name: name !== undefined ? name : models.regulatoryRequirements[requirementIndex].name,
      description: description !== undefined ? description : models.regulatoryRequirements[requirementIndex].description,
      article: article !== undefined ? article : models.regulatoryRequirements[requirementIndex].article,
      section: section !== undefined ? section : models.regulatoryRequirements[requirementIndex].section,
      category: category !== undefined ? category : models.regulatoryRequirements[requirementIndex].category,
      priority: priority !== undefined ? priority : models.regulatoryRequirements[requirementIndex].priority,
      status: status !== undefined ? status : models.regulatoryRequirements[requirementIndex].status,
      applicableJurisdictions: applicableJurisdictions || models.regulatoryRequirements[requirementIndex].applicableJurisdictions,
      controlObjectives: controlObjectives || models.regulatoryRequirements[requirementIndex].controlObjectives,
      relatedRequirements: relatedRequirements || models.regulatoryRequirements[requirementIndex].relatedRequirements,
      updatedAt: new Date().toISOString()
    };

    // Replace the old requirement with the updated one
    models.regulatoryRequirements[requirementIndex] = updatedRequirement;

    res.json({
      data: updatedRequirement,
      message: 'Regulatory requirement updated successfully'
    });
  } catch (error) {
    console.error('Error in updateRequirement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a regulatory requirement
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteRequirement = (req, res) => {
  try {
    const { id } = req.params;

    // Find the requirement to delete
    const requirementIndex = models.regulatoryRequirements.findIndex(r => r.id === id);

    if (requirementIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory requirement with ID ${id} not found`
      });
    }

    // Remove the requirement from the collection
    models.regulatoryRequirements.splice(requirementIndex, 1);

    // Update any requirements that reference this one as a related requirement
    models.regulatoryRequirements.forEach(requirement => {
      if (requirement.relatedRequirements && requirement.relatedRequirements.includes(id)) {
        requirement.relatedRequirements = requirement.relatedRequirements.filter(reqId => reqId !== id);
        requirement.updatedAt = new Date().toISOString();
      }
    });

    res.json({
      message: 'Regulatory requirement deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteRequirement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of jurisdictions
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getJurisdictions = (req, res) => {
  try {
    const { page = 1, limit = 10, type, parentJurisdiction, sortBy = 'name', sortOrder = 'asc' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter jurisdictions based on query parameters
    let filteredJurisdictions = [...models.jurisdictions];

    if (type) {
      filteredJurisdictions = filteredJurisdictions.filter(j => j.type === type);
    }

    if (parentJurisdiction) {
      filteredJurisdictions = filteredJurisdictions.filter(j => j.parentJurisdiction === parentJurisdiction);
    }

    // Sort jurisdictions
    filteredJurisdictions.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedJurisdictions = filteredJurisdictions.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalJurisdictions = filteredJurisdictions.length;
    const totalPages = Math.ceil(totalJurisdictions / limitNum);

    res.json({
      data: paginatedJurisdictions,
      pagination: {
        total: totalJurisdictions,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getJurisdictions:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific jurisdiction by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getJurisdictionById = (req, res) => {
  try {
    const { id } = req.params;
    const jurisdiction = models.jurisdictions.find(j => j.id === id);

    if (!jurisdiction) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Jurisdiction with ID ${id} not found`
      });
    }

    res.json({ data: jurisdiction });
  } catch (error) {
    console.error('Error in getJurisdictionById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new jurisdiction
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createJurisdiction = (req, res) => {
  try {
    const {
      code,
      name,
      type,
      description,
      parentJurisdiction
    } = req.body;

    // Validate that the parent jurisdiction exists if provided
    if (parentJurisdiction) {
      const parent = models.jurisdictions.find(j => j.code === parentJurisdiction);
      if (!parent) {
        return res.status(404).json({
          error: 'Not Found',
          message: `Parent jurisdiction with code ${parentJurisdiction} not found`
        });
      }
    }

    // Check if a jurisdiction with the same code already exists
    const existingJurisdiction = models.jurisdictions.find(j => j.code === code);
    if (existingJurisdiction) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `A jurisdiction with code ${code} already exists`
      });
    }

    // Create a new jurisdiction with a unique ID
    const newJurisdiction = {
      id: `j-${uuidv4().substring(0, 4)}`,
      code,
      name,
      type,
      description: description || '',
      parentJurisdiction: parentJurisdiction || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new jurisdiction to the collection
    models.jurisdictions.push(newJurisdiction);

    res.status(201).json({
      data: newJurisdiction,
      message: 'Jurisdiction created successfully'
    });
  } catch (error) {
    console.error('Error in createJurisdiction:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of regulatory changes
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getChanges = (req, res) => {
  try {
    const { page = 1, limit = 10, frameworkId, changeType, status, impactLevel, sortBy = 'publicationDate', sortOrder = 'desc' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter changes based on query parameters
    let filteredChanges = [...models.regulatoryChanges];

    if (frameworkId) {
      filteredChanges = filteredChanges.filter(change => change.frameworkId === frameworkId);
    }

    if (changeType) {
      filteredChanges = filteredChanges.filter(change => change.changeType === changeType);
    }

    if (status) {
      filteredChanges = filteredChanges.filter(change => change.status === status);
    }

    if (impactLevel) {
      filteredChanges = filteredChanges.filter(change => change.impactLevel === impactLevel);
    }

    // Sort changes
    filteredChanges.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedChanges = filteredChanges.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalChanges = filteredChanges.length;
    const totalPages = Math.ceil(totalChanges / limitNum);

    res.json({
      data: paginatedChanges,
      pagination: {
        total: totalChanges,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getChanges:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific regulatory change by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getChangeById = (req, res) => {
  try {
    const { id } = req.params;
    const change = models.regulatoryChanges.find(c => c.id === id);

    if (!change) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory change with ID ${id} not found`
      });
    }

    res.json({ data: change });
  } catch (error) {
    console.error('Error in getChangeById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new regulatory change
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createChange = (req, res) => {
  try {
    const {
      frameworkId,
      title,
      description,
      changeType,
      publicationDate,
      effectiveDate,
      source,
      sourceUrl,
      impactLevel,
      affectedRequirements,
      status,
      summary
    } = req.body;

    // Validate that the framework exists
    const framework = models.regulatoryFrameworks.find(f => f.id === frameworkId);
    if (!framework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory framework with ID ${frameworkId} not found`
      });
    }

    // Validate that all affected requirements exist
    if (affectedRequirements && affectedRequirements.length > 0) {
      const invalidRequirements = affectedRequirements.filter(
        reqId => !models.regulatoryRequirements.some(r => r.id === reqId)
      );

      if (invalidRequirements.length > 0) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `The following requirement IDs do not exist: ${invalidRequirements.join(', ')}`
        });
      }
    }

    // Create a new change with a unique ID
    const newChange = {
      id: `rc-${uuidv4().substring(0, 4)}`,
      frameworkId,
      title,
      description,
      changeType,
      publicationDate,
      effectiveDate: effectiveDate || null,
      source,
      sourceUrl: sourceUrl || '',
      impactLevel,
      affectedRequirements: affectedRequirements || [],
      status,
      summary: summary || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new change to the collection
    models.regulatoryChanges.push(newChange);

    res.status(201).json({
      data: newChange,
      message: 'Regulatory change created successfully'
    });
  } catch (error) {
    console.error('Error in createChange:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing regulatory change
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateChange = (req, res) => {
  try {
    const { id } = req.params;
    const {
      frameworkId,
      title,
      description,
      changeType,
      publicationDate,
      effectiveDate,
      source,
      sourceUrl,
      impactLevel,
      affectedRequirements,
      status,
      summary
    } = req.body;

    // Find the change to update
    const changeIndex = models.regulatoryChanges.findIndex(c => c.id === id);

    if (changeIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory change with ID ${id} not found`
      });
    }

    // If frameworkId is being updated, validate that the new framework exists
    if (frameworkId && frameworkId !== models.regulatoryChanges[changeIndex].frameworkId) {
      const framework = models.regulatoryFrameworks.find(f => f.id === frameworkId);
      if (!framework) {
        return res.status(404).json({
          error: 'Not Found',
          message: `Regulatory framework with ID ${frameworkId} not found`
        });
      }
    }

    // Validate that all affected requirements exist
    if (affectedRequirements && affectedRequirements.length > 0) {
      const invalidRequirements = affectedRequirements.filter(
        reqId => !models.regulatoryRequirements.some(r => r.id === reqId)
      );

      if (invalidRequirements.length > 0) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `The following requirement IDs do not exist: ${invalidRequirements.join(', ')}`
        });
      }
    }

    // Create updated change object
    const updatedChange = {
      ...models.regulatoryChanges[changeIndex],
      frameworkId: frameworkId || models.regulatoryChanges[changeIndex].frameworkId,
      title: title !== undefined ? title : models.regulatoryChanges[changeIndex].title,
      description: description !== undefined ? description : models.regulatoryChanges[changeIndex].description,
      changeType: changeType !== undefined ? changeType : models.regulatoryChanges[changeIndex].changeType,
      publicationDate: publicationDate !== undefined ? publicationDate : models.regulatoryChanges[changeIndex].publicationDate,
      effectiveDate: effectiveDate !== undefined ? effectiveDate : models.regulatoryChanges[changeIndex].effectiveDate,
      source: source !== undefined ? source : models.regulatoryChanges[changeIndex].source,
      sourceUrl: sourceUrl !== undefined ? sourceUrl : models.regulatoryChanges[changeIndex].sourceUrl,
      impactLevel: impactLevel !== undefined ? impactLevel : models.regulatoryChanges[changeIndex].impactLevel,
      affectedRequirements: affectedRequirements || models.regulatoryChanges[changeIndex].affectedRequirements,
      status: status !== undefined ? status : models.regulatoryChanges[changeIndex].status,
      summary: summary !== undefined ? summary : models.regulatoryChanges[changeIndex].summary,
      updatedAt: new Date().toISOString()
    };

    // Replace the old change with the updated one
    models.regulatoryChanges[changeIndex] = updatedChange;

    res.json({
      data: updatedChange,
      message: 'Regulatory change updated successfully'
    });
  } catch (error) {
    console.error('Error in updateChange:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a regulatory change
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteChange = (req, res) => {
  try {
    const { id } = req.params;

    // Find the change to delete
    const changeIndex = models.regulatoryChanges.findIndex(c => c.id === id);

    if (changeIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory change with ID ${id} not found`
      });
    }

    // Remove the change from the collection
    models.regulatoryChanges.splice(changeIndex, 1);

    res.json({
      message: 'Regulatory change deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteChange:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of regulatory reports
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getReports = (req, res) => {
  try {
    const { page = 1, limit = 10, frameworkId, type, frequency, status, assignee, sortBy = 'nextDueDate', sortOrder = 'asc' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter reports based on query parameters
    let filteredReports = [...models.regulatoryReports];

    if (frameworkId) {
      filteredReports = filteredReports.filter(report => report.frameworkId === frameworkId);
    }

    if (type) {
      filteredReports = filteredReports.filter(report => report.type === type);
    }

    if (frequency) {
      filteredReports = filteredReports.filter(report => report.frequency === frequency);
    }

    if (status) {
      filteredReports = filteredReports.filter(report => report.status === status);
    }

    if (assignee) {
      filteredReports = filteredReports.filter(report =>
        report.assignee && report.assignee.toLowerCase().includes(assignee.toLowerCase())
      );
    }

    // Sort reports
    filteredReports.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedReports = filteredReports.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalReports = filteredReports.length;
    const totalPages = Math.ceil(totalReports / limitNum);

    res.json({
      data: paginatedReports,
      pagination: {
        total: totalReports,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getReports:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific regulatory report by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getReportById = (req, res) => {
  try {
    const { id } = req.params;
    const report = models.regulatoryReports.find(r => r.id === id);

    if (!report) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory report with ID ${id} not found`
      });
    }

    res.json({ data: report });
  } catch (error) {
    console.error('Error in getReportById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new regulatory report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createReport = (req, res) => {
  try {
    const {
      frameworkId,
      name,
      description,
      type,
      frequency,
      lastSubmissionDate,
      nextDueDate,
      assignee,
      status,
      templateUrl,
      sections
    } = req.body;

    // Validate that the framework exists
    const framework = models.regulatoryFrameworks.find(f => f.id === frameworkId);
    if (!framework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory framework with ID ${frameworkId} not found`
      });
    }

    // Create a new report with a unique ID
    const newReport = {
      id: `rpt-${uuidv4().substring(0, 4)}`,
      frameworkId,
      name,
      description,
      type,
      frequency,
      lastSubmissionDate: lastSubmissionDate || null,
      nextDueDate: nextDueDate || null,
      assignee: assignee || '',
      status,
      templateUrl: templateUrl || '',
      sections: sections || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new report to the collection
    models.regulatoryReports.push(newReport);

    res.status(201).json({
      data: newReport,
      message: 'Regulatory report created successfully'
    });
  } catch (error) {
    console.error('Error in createReport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing regulatory report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateReport = (req, res) => {
  try {
    const { id } = req.params;
    const {
      frameworkId,
      name,
      description,
      type,
      frequency,
      lastSubmissionDate,
      nextDueDate,
      assignee,
      status,
      templateUrl,
      sections
    } = req.body;

    // Find the report to update
    const reportIndex = models.regulatoryReports.findIndex(r => r.id === id);

    if (reportIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory report with ID ${id} not found`
      });
    }

    // If frameworkId is being updated, validate that the new framework exists
    if (frameworkId && frameworkId !== models.regulatoryReports[reportIndex].frameworkId) {
      const framework = models.regulatoryFrameworks.find(f => f.id === frameworkId);
      if (!framework) {
        return res.status(404).json({
          error: 'Not Found',
          message: `Regulatory framework with ID ${frameworkId} not found`
        });
      }
    }

    // Create updated report object
    const updatedReport = {
      ...models.regulatoryReports[reportIndex],
      frameworkId: frameworkId || models.regulatoryReports[reportIndex].frameworkId,
      name: name !== undefined ? name : models.regulatoryReports[reportIndex].name,
      description: description !== undefined ? description : models.regulatoryReports[reportIndex].description,
      type: type !== undefined ? type : models.regulatoryReports[reportIndex].type,
      frequency: frequency !== undefined ? frequency : models.regulatoryReports[reportIndex].frequency,
      lastSubmissionDate: lastSubmissionDate !== undefined ? lastSubmissionDate : models.regulatoryReports[reportIndex].lastSubmissionDate,
      nextDueDate: nextDueDate !== undefined ? nextDueDate : models.regulatoryReports[reportIndex].nextDueDate,
      assignee: assignee !== undefined ? assignee : models.regulatoryReports[reportIndex].assignee,
      status: status !== undefined ? status : models.regulatoryReports[reportIndex].status,
      templateUrl: templateUrl !== undefined ? templateUrl : models.regulatoryReports[reportIndex].templateUrl,
      sections: sections || models.regulatoryReports[reportIndex].sections,
      updatedAt: new Date().toISOString()
    };

    // Replace the old report with the updated one
    models.regulatoryReports[reportIndex] = updatedReport;

    res.json({
      data: updatedReport,
      message: 'Regulatory report updated successfully'
    });
  } catch (error) {
    console.error('Error in updateReport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a regulatory report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteReport = (req, res) => {
  try {
    const { id } = req.params;

    // Find the report to delete
    const reportIndex = models.regulatoryReports.findIndex(r => r.id === id);

    if (reportIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulatory report with ID ${id} not found`
      });
    }

    // Remove the report from the collection
    models.regulatoryReports.splice(reportIndex, 1);

    res.json({
      message: 'Regulatory report deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteReport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

// Export the controller functions
module.exports = {
  getFrameworks,
  getFrameworkById,
  createFramework,
  updateFramework,
  deleteFramework,
  getRequirements,
  getRequirementById,
  createRequirement,
  updateRequirement,
  deleteRequirement,
  getJurisdictions,
  getJurisdictionById,
  createJurisdiction,
  getChanges,
  getChangeById,
  createChange,
  updateChange,
  deleteChange,
  getReports,
  getReportById,
  createReport,
  updateReport,
  deleteReport
};
