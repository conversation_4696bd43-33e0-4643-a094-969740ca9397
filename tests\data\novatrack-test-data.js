/**
 * Test Data Generator for NovaTrack
 * 
 * This module provides functions to generate test data for NovaTrack tests.
 */

/**
 * Generate a sample requirement
 * 
 * @param {Object} overrides - Properties to override in the generated requirement
 * @returns {Object} A sample requirement
 */
function generateRequirement(overrides = {}) {
  const defaultRequirement = {
    name: 'Data Subject Rights',
    description: 'Implement processes for handling data subject rights requests',
    framework: 'GDPR',
    category: 'privacy',
    priority: 'high',
    status: 'in_progress',
    due_date: '2023-12-31',
    assigned_to: 'privacy_officer',
    tags: ['gdpr', 'data_subject_rights', 'privacy']
  };

  return { ...defaultRequirement, ...overrides };
}

/**
 * Generate a sample activity
 * 
 * @param {Object} overrides - Properties to override in the generated activity
 * @returns {Object} A sample activity
 */
function generateActivity(overrides = {}) {
  const defaultActivity = {
    name: 'Document Data Subject Rights Process',
    description: 'Create documentation for handling data subject rights requests',
    requirement_id: 'req-123', // This should be replaced with an actual requirement ID
    type: 'documentation',
    status: 'completed',
    start_date: '2023-01-01',
    end_date: '2023-01-15',
    assigned_to: 'privacy_officer',
    notes: 'Documentation completed and reviewed'
  };

  return { ...defaultActivity, ...overrides };
}

/**
 * Generate multiple requirements
 * 
 * @param {number} count - Number of requirements to generate
 * @param {Function} customizer - Function to customize each requirement
 * @returns {Array} Array of requirements
 */
function generateRequirements(count = 5, customizer = null) {
  const requirements = [];

  for (let i = 0; i < count; i++) {
    const requirement = generateRequirement({
      name: `Requirement ${i + 1}`,
      description: `Description for requirement ${i + 1}`
    });

    if (customizer && typeof customizer === 'function') {
      customizer(requirement, i);
    }

    requirements.push(requirement);
  }

  return requirements;
}

/**
 * Generate multiple activities for a requirement
 * 
 * @param {string} requirementId - ID of the requirement
 * @param {number} count - Number of activities to generate
 * @param {Function} customizer - Function to customize each activity
 * @returns {Array} Array of activities
 */
function generateActivities(requirementId, count = 3, customizer = null) {
  const activities = [];

  for (let i = 0; i < count; i++) {
    const activity = generateActivity({
      name: `Activity ${i + 1}`,
      description: `Description for activity ${i + 1}`,
      requirement_id: requirementId
    });

    if (customizer && typeof customizer === 'function') {
      customizer(activity, i);
    }

    activities.push(activity);
  }

  return activities;
}

/**
 * Generate a complete test dataset with requirements and activities
 * 
 * @param {Object} options - Options for generating the dataset
 * @param {number} options.requirementCount - Number of requirements to generate
 * @param {number} options.activitiesPerRequirement - Number of activities per requirement
 * @returns {Object} Object containing requirements and activities
 */
function generateTestDataset(options = {}) {
  const { 
    requirementCount = 5, 
    activitiesPerRequirement = 3 
  } = options;

  const requirements = generateRequirements(requirementCount);
  const activities = [];

  // Add a unique ID to each requirement
  requirements.forEach((req, index) => {
    req.id = `req-${index + 1}`;
    
    // Generate activities for this requirement
    const reqActivities = generateActivities(req.id, activitiesPerRequirement);
    activities.push(...reqActivities);
  });

  return {
    requirements,
    activities
  };
}

module.exports = {
  generateRequirement,
  generateActivity,
  generateRequirements,
  generateActivities,
  generateTestDataset
};
