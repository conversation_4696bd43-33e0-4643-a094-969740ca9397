/**
 * Governance Service
 *
 * This service provides advanced governance features including approval workflows,
 * compliance templates, and enhanced audit capabilities.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const AuditService = require('./AuditService');

class GovernanceService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.governanceDir = path.join(this.dataDir, 'governance');
    this.approvalsFile = path.join(this.governanceDir, 'approvals.json');
    this.complianceTemplatesFile = path.join(this.governanceDir, 'compliance_templates.json');
    this.dataLineageFile = path.join(this.governanceDir, 'data_lineage.json');
    this.auditService = new AuditService(dataDir);

    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.governanceDir, { recursive: true });

      // Initialize files if they don't exist
      await this.initializeFile(this.approvalsFile, []);
      await this.initializeFile(this.complianceTemplatesFile, this.getDefaultComplianceTemplates());
      await this.initializeFile(this.dataLineageFile, []);
    } catch (error) {
      console.error('Error creating governance directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array or default data
        if (filePath === this.complianceTemplatesFile) {
          return this.getDefaultComplianceTemplates();
        }
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get all approval workflows
   */
  async getAllApprovalWorkflows(filters = {}) {
    const approvals = await this.loadData(this.approvalsFile);

    // Apply filters
    let filteredApprovals = approvals;

    if (filters.status) {
      filteredApprovals = filteredApprovals.filter(a => a.status === filters.status);
    }

    if (filters.type) {
      filteredApprovals = filteredApprovals.filter(a => a.type === filters.type);
    }

    if (filters.requestedBy) {
      filteredApprovals = filteredApprovals.filter(a => a.requestedBy === filters.requestedBy);
    }

    if (filters.assignedTo) {
      filteredApprovals = filteredApprovals.filter(a =>
        a.approvers.some(approver => approver.userId === filters.assignedTo)
      );
    }

    // Sort by created date (newest first)
    filteredApprovals.sort((a, b) => new Date(b.created) - new Date(a.created));

    return filteredApprovals;
  }

  /**
   * Get approval workflow by ID
   */
  async getApprovalWorkflowById(id) {
    const approvals = await this.loadData(this.approvalsFile);
    const approval = approvals.find(a => a.id === id);

    if (!approval) {
      throw new NotFoundError(`Approval workflow with ID ${id} not found`);
    }

    return approval;
  }

  /**
   * Create a new approval workflow
   */
  async createApprovalWorkflow(data, userId) {
    // Validate required fields
    if (!data.title) {
      throw new ValidationError('Approval title is required');
    }

    if (!data.type) {
      throw new ValidationError('Approval type is required');
    }

    if (!data.resourceType) {
      throw new ValidationError('Resource type is required');
    }

    if (!data.resourceId) {
      throw new ValidationError('Resource ID is required');
    }

    if (!data.approvers || !Array.isArray(data.approvers) || data.approvers.length === 0) {
      throw new ValidationError('At least one approver is required');
    }

    // Validate approvers
    for (const approver of data.approvers) {
      if (!approver.userId) {
        throw new ValidationError('Approver user ID is required');
      }

      if (!approver.role) {
        throw new ValidationError('Approver role is required');
      }
    }

    // Create approval workflow
    const approval = {
      id: uuidv4(),
      title: data.title,
      description: data.description || '',
      type: data.type,
      resourceType: data.resourceType,
      resourceId: data.resourceId,
      resourceData: data.resourceData || {},
      status: 'pending',
      approvers: data.approvers.map(approver => ({
        ...approver,
        status: 'pending',
        comments: [],
        updated: null
      })),
      requestedBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      completed: null,
      result: null,
      history: [
        {
          action: 'created',
          userId,
          timestamp: new Date().toISOString(),
          details: 'Approval workflow created'
        }
      ]
    };

    // Save approval workflow
    const approvals = await this.loadData(this.approvalsFile);
    approvals.push(approval);
    await this.saveData(this.approvalsFile, approvals);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'CREATE',
      resourceType: 'approval',
      resourceId: approval.id,
      details: {
        title: approval.title,
        type: approval.type,
        resourceType: approval.resourceType,
        resourceId: approval.resourceId
      }
    });

    return approval;
  }

  /**
   * Update approval workflow status
   */
  async updateApprovalStatus(id, data, userId) {
    const approvals = await this.loadData(this.approvalsFile);
    const index = approvals.findIndex(a => a.id === id);

    if (index === -1) {
      throw new NotFoundError(`Approval workflow with ID ${id} not found`);
    }

    const approval = approvals[index];

    // Check if approval is already completed
    if (approval.status === 'approved' || approval.status === 'rejected') {
      throw new ValidationError(`Approval workflow is already ${approval.status}`);
    }

    // Check if user is an approver
    const approverIndex = approval.approvers.findIndex(a => a.userId === userId);
    if (approverIndex === -1) {
      throw new AuthorizationError('You are not authorized to update this approval workflow');
    }

    // Update approver status
    const approver = approval.approvers[approverIndex];
    approver.status = data.status;
    approver.comments.push({
      text: data.comment || '',
      timestamp: new Date().toISOString()
    });
    approver.updated = new Date().toISOString();

    // Update approval history
    approval.history.push({
      action: `approver_${data.status}`,
      userId,
      timestamp: new Date().toISOString(),
      details: data.comment || `Approver ${data.status} the request`
    });

    // Check if all approvers have responded
    const allResponded = approval.approvers.every(a => a.status !== 'pending');
    const allApproved = approval.approvers.every(a => a.status === 'approved');

    if (allResponded) {
      // Update approval status
      approval.status = allApproved ? 'approved' : 'rejected';
      approval.completed = new Date().toISOString();
      approval.result = {
        status: approval.status,
        reason: data.comment || ''
      };

      // Update approval history
      approval.history.push({
        action: `workflow_${approval.status}`,
        userId,
        timestamp: new Date().toISOString(),
        details: `Workflow ${approval.status}`
      });
    }

    // Update approval
    approval.updated = new Date().toISOString();
    approvals[index] = approval;
    await this.saveData(this.approvalsFile, approvals);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UPDATE',
      resourceType: 'approval',
      resourceId: approval.id,
      details: {
        title: approval.title,
        status: approval.status,
        approverStatus: data.status
      }
    });

    return approval;
  }

  /**
   * Cancel approval workflow
   */
  async cancelApprovalWorkflow(id, data, userId) {
    const approvals = await this.loadData(this.approvalsFile);
    const index = approvals.findIndex(a => a.id === id);

    if (index === -1) {
      throw new NotFoundError(`Approval workflow with ID ${id} not found`);
    }

    const approval = approvals[index];

    // Check if approval is already completed
    if (approval.status === 'approved' || approval.status === 'rejected') {
      throw new ValidationError(`Approval workflow is already ${approval.status}`);
    }

    // Check if user is the requester or an admin
    if (approval.requestedBy !== userId && !data.isAdmin) {
      throw new AuthorizationError('You are not authorized to cancel this approval workflow');
    }

    // Update approval status
    approval.status = 'cancelled';
    approval.completed = new Date().toISOString();
    approval.result = {
      status: 'cancelled',
      reason: data.reason || 'Cancelled by requester'
    };

    // Update approval history
    approval.history.push({
      action: 'workflow_cancelled',
      userId,
      timestamp: new Date().toISOString(),
      details: data.reason || 'Workflow cancelled'
    });

    // Update approval
    approval.updated = new Date().toISOString();
    approvals[index] = approval;
    await this.saveData(this.approvalsFile, approvals);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'CANCEL',
      resourceType: 'approval',
      resourceId: approval.id,
      details: {
        title: approval.title,
        status: approval.status,
        reason: data.reason || 'Cancelled by requester'
      }
    });

    return approval;
  }

  /**
   * Get approval workflows for resource
   */
  async getApprovalWorkflowsForResource(resourceType, resourceId) {
    const approvals = await this.loadData(this.approvalsFile);

    // Filter approvals by resource type and ID
    const filteredApprovals = approvals.filter(a =>
      a.resourceType === resourceType && a.resourceId === resourceId
    );

    // Sort by created date (newest first)
    filteredApprovals.sort((a, b) => new Date(b.created) - new Date(a.created));

    return filteredApprovals;
  }

  /**
   * Get approval workflows for user
   */
  async getApprovalWorkflowsForUser(userId, role = 'approver') {
    const approvals = await this.loadData(this.approvalsFile);

    // Filter approvals by user role
    let filteredApprovals;

    if (role === 'requester') {
      filteredApprovals = approvals.filter(a => a.requestedBy === userId);
    } else if (role === 'approver') {
      filteredApprovals = approvals.filter(a =>
        a.approvers.some(approver => approver.userId === userId)
      );
    } else {
      filteredApprovals = approvals.filter(a =>
        a.requestedBy === userId || a.approvers.some(approver => approver.userId === userId)
      );
    }

    // Sort by created date (newest first)
    filteredApprovals.sort((a, b) => new Date(b.created) - new Date(a.created));

    return filteredApprovals;
  }

  /**
   * Get all compliance templates
   */
  async getAllComplianceTemplates(filters = {}) {
    const templates = await this.loadData(this.complianceTemplatesFile);

    // Apply filters
    let filteredTemplates = templates;

    if (filters.category) {
      filteredTemplates = filteredTemplates.filter(t => t.category === filters.category);
    }

    return filteredTemplates;
  }

  /**
   * Get compliance template by ID
   */
  async getComplianceTemplateById(id) {
    const templates = await this.loadData(this.complianceTemplatesFile);
    const template = templates.find(t => t.id === id);

    if (!template) {
      throw new NotFoundError(`Compliance template with ID ${id} not found`);
    }

    return template;
  }

  /**
   * Create a new compliance template
   */
  async createComplianceTemplate(data, userId) {
    // Validate required fields
    if (!data.name) {
      throw new ValidationError('Template name is required');
    }

    if (!data.category) {
      throw new ValidationError('Template category is required');
    }

    if (!data.requirements || !Array.isArray(data.requirements) || data.requirements.length === 0) {
      throw new ValidationError('At least one requirement is required');
    }

    // Validate requirements
    for (const requirement of data.requirements) {
      if (!requirement.name) {
        throw new ValidationError('Requirement name is required');
      }

      if (!requirement.controls || !Array.isArray(requirement.controls) || requirement.controls.length === 0) {
        throw new ValidationError(`At least one control is required for requirement: ${requirement.name}`);
      }

      // Validate controls
      for (const control of requirement.controls) {
        if (!control.name) {
          throw new ValidationError(`Control name is required for requirement: ${requirement.name}`);
        }

        if (!control.type) {
          throw new ValidationError(`Control type is required for control: ${control.name}`);
        }

        if (!control.validation) {
          throw new ValidationError(`Validation method is required for control: ${control.name}`);
        }
      }
    }

    // Create template
    const template = {
      id: data.id || uuidv4(),
      name: data.name,
      description: data.description || '',
      version: data.version || '1.0',
      category: data.category,
      requirements: data.requirements,
      createdBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };

    // Save template
    const templates = await this.loadData(this.complianceTemplatesFile);

    // Check if template with same ID already exists
    const existingIndex = templates.findIndex(t => t.id === template.id);

    if (existingIndex !== -1) {
      // Update existing template
      templates[existingIndex] = template;
    } else {
      // Add new template
      templates.push(template);
    }

    await this.saveData(this.complianceTemplatesFile, templates);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: existingIndex !== -1 ? 'UPDATE' : 'CREATE',
      resourceType: 'compliance_template',
      resourceId: template.id,
      details: {
        name: template.name,
        category: template.category,
        version: template.version
      }
    });

    return template;
  }

  /**
   * Delete compliance template
   */
  async deleteComplianceTemplate(id, userId) {
    const templates = await this.loadData(this.complianceTemplatesFile);
    const index = templates.findIndex(t => t.id === id);

    if (index === -1) {
      throw new NotFoundError(`Compliance template with ID ${id} not found`);
    }

    const template = templates[index];

    // Remove template
    templates.splice(index, 1);
    await this.saveData(this.complianceTemplatesFile, templates);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'DELETE',
      resourceType: 'compliance_template',
      resourceId: id,
      details: {
        name: template.name,
        category: template.category
      }
    });

    return { success: true, message: `Compliance template ${id} deleted` };
  }

  /**
   * Track data lineage
   */
  async trackDataLineage(data, userId) {
    // Validate required fields
    if (!data.sourceType) {
      throw new ValidationError('Source type is required');
    }

    if (!data.sourceId) {
      throw new ValidationError('Source ID is required');
    }

    if (!data.destinationType) {
      throw new ValidationError('Destination type is required');
    }

    if (!data.destinationId) {
      throw new ValidationError('Destination ID is required');
    }

    if (!data.operation) {
      throw new ValidationError('Operation is required');
    }

    // Create lineage record
    const lineage = {
      id: uuidv4(),
      sourceType: data.sourceType,
      sourceId: data.sourceId,
      destinationType: data.destinationType,
      destinationId: data.destinationId,
      operation: data.operation,
      metadata: data.metadata || {},
      timestamp: new Date().toISOString(),
      userId
    };

    // Save lineage record
    const lineages = await this.loadData(this.dataLineageFile);
    lineages.push(lineage);
    await this.saveData(this.dataLineageFile, lineages);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'TRACK',
      resourceType: 'data_lineage',
      resourceId: lineage.id,
      details: {
        sourceType: lineage.sourceType,
        sourceId: lineage.sourceId,
        destinationType: lineage.destinationType,
        destinationId: lineage.destinationId,
        operation: lineage.operation
      }
    });

    return lineage;
  }

  /**
   * Get data lineage for resource
   */
  async getDataLineageForResource(resourceType, resourceId, direction = 'both') {
    const lineages = await this.loadData(this.dataLineageFile);

    // Filter lineages by resource type and ID
    let filteredLineages = [];

    if (direction === 'source' || direction === 'both') {
      // Resource as source
      const sourceLineages = lineages.filter(l =>
        l.sourceType === resourceType && l.sourceId === resourceId
      );
      filteredLineages = filteredLineages.concat(sourceLineages);
    }

    if (direction === 'destination' || direction === 'both') {
      // Resource as destination
      const destinationLineages = lineages.filter(l =>
        l.destinationType === resourceType && l.destinationId === resourceId
      );
      filteredLineages = filteredLineages.concat(destinationLineages);
    }

    // Sort by timestamp (newest first)
    filteredLineages.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    return filteredLineages;
  }

  /**
   * Build data lineage graph
   */
  async buildDataLineageGraph(resourceType, resourceId, depth = 2) {
    // Start with the resource
    const graph = {
      nodes: [
        {
          id: `${resourceType}:${resourceId}`,
          type: resourceType,
          resourceId: resourceId,
          level: 0
        }
      ],
      edges: []
    };

    // Build graph recursively
    await this.buildDataLineageGraphRecursive(graph, resourceType, resourceId, 0, depth);

    return graph;
  }

  /**
   * Build data lineage graph recursively
   */
  async buildDataLineageGraphRecursive(graph, resourceType, resourceId, level, maxDepth) {
    if (level >= maxDepth) {
      return;
    }

    // Get lineages for resource
    const lineages = await this.getDataLineageForResource(resourceType, resourceId, 'both');

    for (const lineage of lineages) {
      // Add source node if not already in graph
      const sourceNodeId = `${lineage.sourceType}:${lineage.sourceId}`;
      if (!graph.nodes.some(n => n.id === sourceNodeId)) {
        graph.nodes.push({
          id: sourceNodeId,
          type: lineage.sourceType,
          resourceId: lineage.sourceId,
          level: level + 1
        });
      }

      // Add destination node if not already in graph
      const destNodeId = `${lineage.destinationType}:${lineage.destinationId}`;
      if (!graph.nodes.some(n => n.id === destNodeId)) {
        graph.nodes.push({
          id: destNodeId,
          type: lineage.destinationType,
          resourceId: lineage.destinationId,
          level: level + 1
        });
      }

      // Add edge if not already in graph
      const edgeId = `${sourceNodeId}->${destNodeId}`;
      if (!graph.edges.some(e => e.id === edgeId)) {
        graph.edges.push({
          id: edgeId,
          source: sourceNodeId,
          target: destNodeId,
          operation: lineage.operation,
          timestamp: lineage.timestamp
        });
      }

      // Recursively build graph for connected nodes
      if (lineage.sourceType === resourceType && lineage.sourceId === resourceId) {
        // Resource is source, follow destination
        await this.buildDataLineageGraphRecursive(
          graph,
          lineage.destinationType,
          lineage.destinationId,
          level + 1,
          maxDepth
        );
      } else if (lineage.destinationType === resourceType && lineage.destinationId === resourceId) {
        // Resource is destination, follow source
        await this.buildDataLineageGraphRecursive(
          graph,
          lineage.sourceType,
          lineage.sourceId,
          level + 1,
          maxDepth
        );
      }
    }
  }

  /**
   * Get default compliance templates
   */
  getDefaultComplianceTemplates() {
    return [
      {
        id: 'gdpr',
        name: 'GDPR Compliance',
        description: 'General Data Protection Regulation compliance template',
        version: '1.0',
        category: 'privacy',
        requirements: [
          {
            id: 'gdpr-1',
            name: 'Data Processing Agreement',
            description: 'Ensure a Data Processing Agreement is in place',
            controls: [
              {
                id: 'gdpr-1-1',
                name: 'DPA Verification',
                description: 'Verify that a DPA is signed and stored',
                type: 'document',
                validation: 'manual'
              }
            ]
          },
          {
            id: 'gdpr-2',
            name: 'Data Subject Rights',
            description: 'Ensure data subject rights can be fulfilled',
            controls: [
              {
                id: 'gdpr-2-1',
                name: 'Data Export',
                description: 'Ability to export all data related to a subject',
                type: 'technical',
                validation: 'automated'
              },
              {
                id: 'gdpr-2-2',
                name: 'Data Deletion',
                description: 'Ability to delete all data related to a subject',
                type: 'technical',
                validation: 'automated'
              }
            ]
          },
          {
            id: 'gdpr-3',
            name: 'Data Transfer',
            description: 'Ensure compliant data transfer mechanisms',
            controls: [
              {
                id: 'gdpr-3-1',
                name: 'Transfer Mechanism',
                description: 'Verify appropriate transfer mechanism is in place',
                type: 'document',
                validation: 'manual'
              }
            ]
          }
        ]
      },
      {
        id: 'hipaa',
        name: 'HIPAA Compliance',
        description: 'Health Insurance Portability and Accountability Act compliance template',
        version: '1.0',
        category: 'healthcare',
        requirements: [
          {
            id: 'hipaa-1',
            name: 'Business Associate Agreement',
            description: 'Ensure a BAA is in place',
            controls: [
              {
                id: 'hipaa-1-1',
                name: 'BAA Verification',
                description: 'Verify that a BAA is signed and stored',
                type: 'document',
                validation: 'manual'
              }
            ]
          },
          {
            id: 'hipaa-2',
            name: 'Access Controls',
            description: 'Ensure appropriate access controls',
            controls: [
              {
                id: 'hipaa-2-1',
                name: 'Authentication',
                description: 'Verify multi-factor authentication is enabled',
                type: 'technical',
                validation: 'automated'
              },
              {
                id: 'hipaa-2-2',
                name: 'Authorization',
                description: 'Verify role-based access control is implemented',
                type: 'technical',
                validation: 'automated'
              }
            ]
          },
          {
            id: 'hipaa-3',
            name: 'Audit Controls',
            description: 'Ensure audit controls are in place',
            controls: [
              {
                id: 'hipaa-3-1',
                name: 'Audit Logging',
                description: 'Verify audit logging is enabled',
                type: 'technical',
                validation: 'automated'
              }
            ]
          }
        ]
      },
      {
        id: 'soc2',
        name: 'SOC 2 Compliance',
        description: 'Service Organization Control 2 compliance template',
        version: '1.0',
        category: 'security',
        requirements: [
          {
            id: 'soc2-1',
            name: 'Security',
            description: 'Ensure security controls are in place',
            controls: [
              {
                id: 'soc2-1-1',
                name: 'Access Control',
                description: 'Verify access control policies are implemented',
                type: 'policy',
                validation: 'manual'
              },
              {
                id: 'soc2-1-2',
                name: 'Encryption',
                description: 'Verify data encryption in transit and at rest',
                type: 'technical',
                validation: 'automated'
              }
            ]
          },
          {
            id: 'soc2-2',
            name: 'Availability',
            description: 'Ensure availability controls are in place',
            controls: [
              {
                id: 'soc2-2-1',
                name: 'Backup',
                description: 'Verify backup procedures are implemented',
                type: 'process',
                validation: 'manual'
              },
              {
                id: 'soc2-2-2',
                name: 'Monitoring',
                description: 'Verify system monitoring is in place',
                type: 'technical',
                validation: 'automated'
              }
            ]
          },
          {
            id: 'soc2-3',
            name: 'Processing Integrity',
            description: 'Ensure processing integrity controls are in place',
            controls: [
              {
                id: 'soc2-3-1',
                name: 'Quality Assurance',
                description: 'Verify quality assurance procedures',
                type: 'process',
                validation: 'manual'
              }
            ]
          },
          {
            id: 'soc2-4',
            name: 'Confidentiality',
            description: 'Ensure confidentiality controls are in place',
            controls: [
              {
                id: 'soc2-4-1',
                name: 'Data Classification',
                description: 'Verify data classification procedures',
                type: 'policy',
                validation: 'manual'
              }
            ]
          },
          {
            id: 'soc2-5',
            name: 'Privacy',
            description: 'Ensure privacy controls are in place',
            controls: [
              {
                id: 'soc2-5-1',
                name: 'Privacy Notice',
                description: 'Verify privacy notice is available',
                type: 'document',
                validation: 'manual'
              }
            ]
          }
        ]
      }
    ];
  }
}

module.exports = GovernanceService;
