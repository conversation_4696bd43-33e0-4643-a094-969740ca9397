/**
 * Secure Dashboard Example
 * 
 * This example demonstrates advanced security features.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  DashboardCard,
  TwoFactorAuth,
  PasswordStrengthMeter,
  SecurityLog,
  TabPanel,
  SkipLink,
  ThemeSelector
} from '../components';
import { ThemeProvider } from '../theme';
import { PreferencesProvider } from '../preferences';
import { OfflineProvider } from '../offline';
import { AnimationProvider } from '../animation';
import { I18nProvider } from '../i18n';
import { AccessibilityProvider } from '../accessibility';
import { SecurityProvider } from '../security';
import { MockSecurityService } from '../security';
import { AuthProvider, useAuth } from '../auth';
import { Translation } from '../components';

/**
 * Secure Dashboard Content component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Secure Dashboard Content component
 */
const SecureDashboardContent = ({
  novaConnect,
  novaShield,
  novaTrack,
  enableLogging = false
}) => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showTwoFactorAuth, setShowTwoFactorAuth] = useState(false);
  const [password, setPassword] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState(null);
  const [passwordSuccess, setPasswordSuccess] = useState(false);
  
  // Handle two-factor auth setup complete
  const handleTwoFactorAuthSetupComplete = () => {
    setShowTwoFactorAuth(false);
  };
  
  // Handle password change
  const handlePasswordChange = async () => {
    try {
      setPasswordError(null);
      setPasswordSuccess(false);
      
      // Validate passwords
      if (newPassword !== confirmPassword) {
        setPasswordError('Passwords do not match');
        return;
      }
      
      // Check password strength
      const strengthCheck = novaShield.security.checkPasswordStrength(newPassword);
      
      if (strengthCheck.score < 3) {
        setPasswordError('Password is too weak');
        return;
      }
      
      // Change password
      await novaShield.security.changePassword(currentPassword, newPassword);
      
      // Reset form
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      
      // Show success message
      setPasswordSuccess(true);
    } catch (error) {
      setPasswordError(error.message || 'Failed to change password');
    }
  };
  
  // Tabs
  const tabs = [
    {
      id: 'dashboard',
      label: <Translation id="dashboard.dashboard" defaultValue="Dashboard" />,
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DashboardCard
            title={<Translation id="security.securityOverview" defaultValue="Security Overview" />}
            subtitle={<Translation id="security.accountSecurity" defaultValue="Account Security" />}
          >
            <div className="p-4">
              <h3 className="text-lg font-medium text-textPrimary mb-4">
                <Translation id="security.securityFeatures" defaultValue="Security Features" />
              </h3>
              <p className="text-textSecondary mb-4">
                <Translation
                  id="security.securityFeaturesDescription"
                  defaultValue="This dashboard demonstrates advanced security features for protecting user accounts."
                />
              </p>
              <ul className="list-disc list-inside space-y-2 text-textSecondary">
                <li>
                  <Translation id="security.twoFactorAuthentication" defaultValue="Two-Factor Authentication" />
                </li>
                <li>
                  <Translation id="security.passwordStrengthChecking" defaultValue="Password Strength Checking" />
                </li>
                <li>
                  <Translation id="security.securityLogging" defaultValue="Security Logging" />
                </li>
                <li>
                  <Translation id="security.accountLockout" defaultValue="Account Lockout Protection" />
                </li>
                <li>
                  <Translation id="security.sessionManagement" defaultValue="Session Management" />
                </li>
                <li>
                  <Translation id="security.secureDataStorage" defaultValue="Secure Data Storage" />
                </li>
              </ul>
            </div>
          </DashboardCard>
          
          <DashboardCard
            title={<Translation id="security.twoFactorAuthentication" defaultValue="Two-Factor Authentication" />}
            subtitle={<Translation id="security.additionalSecurity" defaultValue="Additional Security Layer" />}
          >
            <div className="p-4">
              <p className="text-textSecondary mb-4">
                <Translation
                  id="security.twoFactorAuthDescription"
                  defaultValue="Two-factor authentication adds an extra layer of security to your account by requiring a verification code in addition to your password."
                />
              </p>
              
              {showTwoFactorAuth ? (
                <TwoFactorAuth
                  variant="setup"
                  onCancel={() => setShowTwoFactorAuth(false)}
                  onSetupComplete={handleTwoFactorAuthSetupComplete}
                />
              ) : (
                <button
                  type="button"
                  className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
                  onClick={() => setShowTwoFactorAuth(true)}
                >
                  <Translation id="security.setupTwoFactorAuth" defaultValue="Setup Two-Factor Authentication" />
                </button>
              )}
            </div>
          </DashboardCard>
          
          <DashboardCard
            title={<Translation id="security.passwordStrength" defaultValue="Password Strength" />}
            subtitle={<Translation id="security.passwordSecurity" defaultValue="Password Security" />}
          >
            <div className="p-4">
              <p className="text-textSecondary mb-4">
                <Translation
                  id="security.passwordStrengthDescription"
                  defaultValue="Strong passwords are essential for account security. Use a mix of letters, numbers, and symbols."
                />
              </p>
              
              <div className="space-y-4">
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-textSecondary mb-1">
                    <Translation id="security.testPasswordStrength" defaultValue="Test Password Strength" />
                  </label>
                  
                  <input
                    type="password"
                    id="password"
                    className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary focus:outline-none focus:ring-2 focus:ring-primary"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter a password"
                  />
                </div>
                
                <PasswordStrengthMeter password={password} />
              </div>
            </div>
          </DashboardCard>
          
          <DashboardCard
            title={<Translation id="security.changePassword" defaultValue="Change Password" />}
            subtitle={<Translation id="security.updatePassword" defaultValue="Update Your Password" />}
          >
            <div className="p-4">
              <div className="space-y-4">
                <div>
                  <label htmlFor="current-password" className="block text-sm font-medium text-textSecondary mb-1">
                    <Translation id="security.currentPassword" defaultValue="Current Password" />
                  </label>
                  
                  <input
                    type="password"
                    id="current-password"
                    className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary focus:outline-none focus:ring-2 focus:ring-primary"
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                  />
                </div>
                
                <div>
                  <label htmlFor="new-password" className="block text-sm font-medium text-textSecondary mb-1">
                    <Translation id="security.newPassword" defaultValue="New Password" />
                  </label>
                  
                  <input
                    type="password"
                    id="new-password"
                    className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary focus:outline-none focus:ring-2 focus:ring-primary"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                  />
                  
                  {newPassword && (
                    <div className="mt-2">
                      <PasswordStrengthMeter password={newPassword} />
                    </div>
                  )}
                </div>
                
                <div>
                  <label htmlFor="confirm-password" className="block text-sm font-medium text-textSecondary mb-1">
                    <Translation id="security.confirmPassword" defaultValue="Confirm Password" />
                  </label>
                  
                  <input
                    type="password"
                    id="confirm-password"
                    className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary focus:outline-none focus:ring-2 focus:ring-primary"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                  />
                </div>
                
                {passwordError && (
                  <div className="text-sm text-error">
                    {passwordError}
                  </div>
                )}
                
                {passwordSuccess && (
                  <div className="text-sm text-success">
                    <Translation id="security.passwordChangedSuccessfully" defaultValue="Password changed successfully" />
                  </div>
                )}
                
                <div>
                  <button
                    type="button"
                    className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
                    onClick={handlePasswordChange}
                    disabled={!currentPassword || !newPassword || !confirmPassword}
                  >
                    <Translation id="security.changePassword" defaultValue="Change Password" />
                  </button>
                </div>
              </div>
            </div>
          </DashboardCard>
        </div>
      )
    },
    {
      id: 'security-log',
      label: <Translation id="security.securityLog" defaultValue="Security Log" />,
      content: (
        <DashboardCard
          title={<Translation id="security.securityLog" defaultValue="Security Log" />}
          subtitle={<Translation id="security.accountActivity" defaultValue="Account Activity" />}
        >
          <div className="p-4">
            <p className="text-textSecondary mb-4">
              <Translation
                id="security.securityLogDescription"
                defaultValue="The security log shows recent security-related events for your account."
              />
            </p>
            
            <SecurityLog />
          </div>
        </DashboardCard>
      )
    },
    {
      id: 'security-settings',
      label: <Translation id="security.securitySettings" defaultValue="Security Settings" />,
      content: (
        <DashboardCard
          title={<Translation id="security.securitySettings" defaultValue="Security Settings" />}
          subtitle={<Translation id="security.configureSecurityOptions" defaultValue="Configure Security Options" />}
        >
          <div className="p-4">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-textPrimary mb-2">
                  <Translation id="security.sessionSettings" defaultValue="Session Settings" />
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label htmlFor="session-timeout" className="block text-sm font-medium text-textSecondary mb-1">
                      <Translation id="security.sessionTimeout" defaultValue="Session Timeout (minutes)" />
                    </label>
                    
                    <select
                      id="session-timeout"
                      className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary focus:outline-none focus:ring-2 focus:ring-primary"
                      defaultValue="30"
                    >
                      <option value="15">15</option>
                      <option value="30">30</option>
                      <option value="60">60</option>
                      <option value="120">120</option>
                    </select>
                    
                    <p className="mt-1 text-sm text-textSecondary">
                      <Translation
                        id="security.sessionTimeoutDescription"
                        defaultValue="Your session will automatically expire after this period of inactivity."
                      />
                    </p>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-textPrimary mb-2">
                  <Translation id="security.loginSettings" defaultValue="Login Settings" />
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="remember-devices"
                        type="checkbox"
                        className="h-4 w-4 text-primary border-divider rounded focus:ring-primary"
                        defaultChecked
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="remember-devices" className="font-medium text-textPrimary">
                        <Translation id="security.rememberDevices" defaultValue="Remember Devices" />
                      </label>
                      <p className="text-textSecondary">
                        <Translation
                          id="security.rememberDevicesDescription"
                          defaultValue="Skip two-factor authentication on devices you've previously verified."
                        />
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="notify-logins"
                        type="checkbox"
                        className="h-4 w-4 text-primary border-divider rounded focus:ring-primary"
                        defaultChecked
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="notify-logins" className="font-medium text-textPrimary">
                        <Translation id="security.notifyNewLogins" defaultValue="Notify on New Logins" />
                      </label>
                      <p className="text-textSecondary">
                        <Translation
                          id="security.notifyNewLoginsDescription"
                          defaultValue="Receive notifications when your account is accessed from a new device or location."
                        />
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-textPrimary mb-2">
                  <Translation id="security.advancedSecurity" defaultValue="Advanced Security" />
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="password-policy"
                        type="checkbox"
                        className="h-4 w-4 text-primary border-divider rounded focus:ring-primary"
                        defaultChecked
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="password-policy" className="font-medium text-textPrimary">
                        <Translation id="security.enforcePasswordPolicy" defaultValue="Enforce Password Policy" />
                      </label>
                      <p className="text-textSecondary">
                        <Translation
                          id="security.enforcePasswordPolicyDescription"
                          defaultValue="Require strong passwords that meet security requirements."
                        />
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="account-lockout"
                        type="checkbox"
                        className="h-4 w-4 text-primary border-divider rounded focus:ring-primary"
                        defaultChecked
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="account-lockout" className="font-medium text-textPrimary">
                        <Translation id="security.enableAccountLockout" defaultValue="Enable Account Lockout" />
                      </label>
                      <p className="text-textSecondary">
                        <Translation
                          id="security.enableAccountLockoutDescription"
                          defaultValue="Lock account after multiple failed login attempts."
                        />
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end">
                <button
                  type="button"
                  className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
                >
                  <Translation id="common.saveChanges" defaultValue="Save Changes" />
                </button>
              </div>
            </div>
          </div>
        </DashboardCard>
      )
    }
  ];
  
  return (
    <div className="space-y-6">
      {/* Skip link for keyboard navigation */}
      <SkipLink targetId="main-content" />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-textPrimary" id="dashboard-title">
          <Translation id="security.secureDashboard" defaultValue="Secure Dashboard" />
        </h1>
        
        <div className="flex items-center space-x-2">
          <ThemeSelector variant="dropdown" size="sm" />
        </div>
      </div>
      
      {/* Main content */}
      <main id="main-content" tabIndex="-1">
        <TabPanel
          tabs={tabs}
          defaultTab="dashboard"
          variant="pills"
          onTabChange={setActiveTab}
        />
      </main>
    </div>
  );
};

SecureDashboardContent.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  enableLogging: PropTypes.bool
};

/**
 * Secure Dashboard component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} [props.initialTheme] - Initial theme
 * @param {boolean} [props.enableSystemPreference=true] - Whether to enable system preference detection
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Secure Dashboard component
 */
const SecureDashboard = ({
  novaConnect,
  novaShield,
  novaTrack,
  initialTheme,
  enableSystemPreference = true,
  enableLogging = false
}) => {
  // Create mock security service
  const mockSecurityService = new MockSecurityService();
  
  return (
    <ThemeProvider theme={initialTheme} enableSystemPreference={enableSystemPreference}>
      <I18nProvider detectBrowserLocale>
        <AccessibilityProvider>
          <AuthProvider>
            <SecurityProvider securityService={mockSecurityService}>
              <PreferencesProvider>
                <OfflineProvider>
                  <AnimationProvider>
                    <SecureDashboardContent
                      novaConnect={novaConnect}
                      novaShield={novaShield}
                      novaTrack={novaTrack}
                      enableLogging={enableLogging}
                    />
                  </AnimationProvider>
                </OfflineProvider>
              </PreferencesProvider>
            </SecurityProvider>
          </AuthProvider>
        </AccessibilityProvider>
      </I18nProvider>
    </ThemeProvider>
  );
};

SecureDashboard.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  initialTheme: PropTypes.object,
  enableSystemPreference: PropTypes.bool,
  enableLogging: PropTypes.bool
};

export default SecureDashboard;
