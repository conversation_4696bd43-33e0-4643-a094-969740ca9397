61e3cbd7c32c3caf0a11c285153dc4ed
/**
 * NovaFuse Universal API Connector - Connection Error
 * 
 * This module defines connection-related errors for the UAC.
 */

const UAConnectorError = require('./base-error');

/**
 * Error class for connection failures
 * @class ConnectionError
 * @extends UAConnectorError
 */
class ConnectionError extends UAConnectorError {
  /**
   * Create a new ConnectionError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {string} options.severity - Error severity
   * @param {Object} options.context - Additional context for the error
   * @param {Error} options.cause - The error that caused this error
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'CONNECTION_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'Failed to connect to the service. Please check your network connection and try again.';
  }
}

/**
 * Error class for timeout errors
 * @class TimeoutError
 * @extends ConnectionError
 */
class TimeoutError extends ConnectionError {
  /**
   * Create a new TimeoutError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'The request timed out', options = {}) {
    super(message, {
      code: options.code || 'CONNECTION_TIMEOUT',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'The request timed out. Please try again later or contact support if the issue persists.';
  }
}

/**
 * Error class for network errors
 * @class NetworkError
 * @extends ConnectionError
 */
class NetworkError extends ConnectionError {
  /**
   * Create a new NetworkError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'A network error occurred', options = {}) {
    super(message, {
      code: options.code || 'CONNECTION_NETWORK_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'A network error occurred. Please check your internet connection and try again.';
  }
}

/**
 * Error class for service unavailable errors
 * @class ServiceUnavailableError
 * @extends ConnectionError
 */
class ServiceUnavailableError extends ConnectionError {
  /**
   * Create a new ServiceUnavailableError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'The service is currently unavailable', options = {}) {
    super(message, {
      code: options.code || 'CONNECTION_SERVICE_UNAVAILABLE',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'The service is currently unavailable. Please try again later.';
  }
}

/**
 * Error class for DNS resolution errors
 * @class DnsResolutionError
 * @extends ConnectionError
 */
class DnsResolutionError extends ConnectionError {
  /**
   * Create a new DnsResolutionError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'Failed to resolve the hostname', options = {}) {
    super(message, {
      code: options.code || 'CONNECTION_DNS_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'Failed to connect to the service. The hostname could not be resolved.';
  }
}
module.exports = {
  ConnectionError,
  TimeoutError,
  NetworkError,
  ServiceUnavailableError,
  DnsResolutionError
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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