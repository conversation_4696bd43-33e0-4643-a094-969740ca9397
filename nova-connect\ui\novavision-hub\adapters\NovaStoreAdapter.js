/**
 * NovaStore Adapter for NovaVision
 * 
 * This adapter connects NovaStore with NovaVision, allowing NovaVision to render UI schemas
 * based on NovaStore data and functionality for the compliance app store.
 */

/**
 * NovaStore Adapter class
 */
class NovaStoreAdapter {
  /**
   * Constructor
   * 
   * @param {Object} options - Adapter options
   * @param {Object} options.novaStore - NovaStore instance
   * @param {Object} options.novaVision - NovaVision instance
   * @param {boolean} [options.enableLogging=false] - Whether to enable logging
   * @param {Array} [options.subscribeTopics=[]] - Topics to subscribe to
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: false,
      subscribeTopics: [],
      ...options
    };
    
    this.novaStore = options.novaStore;
    this.novaVision = options.novaVision;
    this.logger = options.logger || console;
    
    if (!this.novaStore) {
      throw new Error('NovaStore instance is required');
    }
    
    if (!this.novaVision) {
      throw new Error('NovaVision instance is required');
    }
    
    this.logger.info('NovaStore Adapter initialized');
  }
  
  /**
   * Initialize the adapter
   * 
   * @returns {Promise} - Promise that resolves when initialization is complete
   */
  async initialize() {
    if (this.options.enableLogging) {
      this.logger.info('Initializing NovaStore Adapter...');
    }
    
    try {
      // Subscribe to NovaStore events
      if (this.options.subscribeTopics.length > 0) {
        await this._subscribeToEvents();
      }
      
      if (this.options.enableLogging) {
        this.logger.info('NovaStore Adapter initialized successfully');
      }
      
      return true;
    } catch (error) {
      this.logger.error('Error initializing NovaStore Adapter', error);
      throw error;
    }
  }
  
  /**
   * Subscribe to NovaStore events
   * 
   * @private
   * @returns {Promise} - Promise that resolves when subscription is complete
   */
  async _subscribeToEvents() {
    if (this.options.enableLogging) {
      this.logger.info('Subscribing to NovaStore events...');
    }
    
    try {
      // Subscribe to events
      for (const topic of this.options.subscribeTopics) {
        if (topic.startsWith('novaStore.')) {
          const eventName = topic.split('.')[1];
          
          // Subscribe to event
          this.novaStore.on(eventName, (data) => {
            if (this.options.enableLogging) {
              this.logger.info(`NovaStore event: ${eventName}`, data);
            }
            
            // Handle event
            this._handleEvent(eventName, data);
          });
          
          if (this.options.enableLogging) {
            this.logger.info(`Subscribed to NovaStore event: ${eventName}`);
          }
        }
      }
      
      return true;
    } catch (error) {
      this.logger.error('Error subscribing to NovaStore events', error);
      throw error;
    }
  }
  
  /**
   * Handle NovaStore event
   * 
   * @private
   * @param {string} eventName - Event name
   * @param {Object} data - Event data
   */
  _handleEvent(eventName, data) {
    // Handle event based on event name
    switch (eventName) {
      case 'connectorInstalled':
      case 'connectorUninstalled':
      case 'connectorUpdated':
        // Update connector list UI
        this._updateConnectorListUI();
        break;
      
      case 'connectorDetailsViewed':
        // Update connector details UI
        this._updateConnectorDetailsUI(data);
        break;
      
      case 'frameworkMapped':
        // Update framework mapping UI
        this._updateFrameworkMappingUI();
        break;
      
      case 'purchaseCompleted':
        // Update purchase UI
        this._updatePurchaseUI(data);
        break;
      
      default:
        // Unknown event
        if (this.options.enableLogging) {
          this.logger.warn(`Unknown NovaStore event: ${eventName}`);
        }
        break;
    }
  }
  
  /**
   * Update connector list UI
   * 
   * @private
   */
  async _updateConnectorListUI() {
    try {
      // Get connector list schema
      const schema = await this.getUISchema('connectorList');
      
      // Notify NovaVision of schema update
      this.novaVision.updateSchema('novaStore.connectorList', schema);
    } catch (error) {
      this.logger.error('Error updating connector list UI', error);
    }
  }
  
  /**
   * Update connector details UI
   * 
   * @private
   * @param {Object} data - Connector details data
   */
  async _updateConnectorDetailsUI(data) {
    try {
      // Get connector details schema
      const schema = await this.getUISchema('connectorDetails', { connectorId: data.connectorId });
      
      // Notify NovaVision of schema update
      this.novaVision.updateSchema('novaStore.connectorDetails', schema);
    } catch (error) {
      this.logger.error('Error updating connector details UI', error);
    }
  }
  
  /**
   * Update framework mapping UI
   * 
   * @private
   */
  async _updateFrameworkMappingUI() {
    try {
      // Get framework mapping schema
      const schema = await this.getUISchema('frameworkMapping');
      
      // Notify NovaVision of schema update
      this.novaVision.updateSchema('novaStore.frameworkMapping', schema);
    } catch (error) {
      this.logger.error('Error updating framework mapping UI', error);
    }
  }
  
  /**
   * Update purchase UI
   * 
   * @private
   * @param {Object} data - Purchase data
   */
  async _updatePurchaseUI(data) {
    try {
      // Get purchase schema
      const schema = await this.getUISchema('purchase', { purchaseId: data.purchaseId });
      
      // Notify NovaVision of schema update
      this.novaVision.updateSchema('novaStore.purchase', schema);
    } catch (error) {
      this.logger.error('Error updating purchase UI', error);
    }
  }
  
  /**
   * Get UI schema for NovaStore
   * 
   * @param {string} schemaType - Schema type
   * @param {Object} options - Schema options
   * @returns {Promise<Object>} - UI schema
   */
  async getUISchema(schemaType, options = {}) {
    if (this.options.enableLogging) {
      this.logger.info(`Getting UI schema for NovaStore.${schemaType}...`);
    }
    
    try {
      // Get schema based on schema type
      switch (schemaType) {
        case 'connectorList':
          return await this._getConnectorListSchema(options);
        
        case 'connectorDetails':
          return await this._getConnectorDetailsSchema(options);
        
        case 'frameworkMapping':
          return await this._getFrameworkMappingSchema(options);
        
        case 'purchase':
          return await this._getPurchaseSchema(options);
        
        case 'dashboard':
          return await this.getDashboardSchema(options);
        
        default:
          throw new Error(`Unknown schema type: ${schemaType}`);
      }
    } catch (error) {
      this.logger.error(`Error getting UI schema for NovaStore.${schemaType}`, error);
      throw error;
    }
  }
  
  /**
   * Get connector list schema
   * 
   * @private
   * @param {Object} options - Schema options
   * @returns {Promise<Object>} - Connector list schema
   */
  async _getConnectorListSchema(options = {}) {
    try {
      // Get connectors from NovaStore
      const connectors = await this.novaStore.getConnectors({
        limit: options.limit || 50,
        offset: options.offset || 0,
        category: options.category,
        framework: options.framework,
        search: options.search,
        sort: options.sort || 'popularity'
      });
      
      // Create connector list schema
      return {
        type: 'grid',
        title: 'Compliance App Store',
        filters: {
          type: 'form',
          fields: [
            {
              type: 'select',
              name: 'category',
              label: 'Category',
              options: [
                { value: '', label: 'All Categories' },
                { value: 'data-privacy', label: 'Data Privacy' },
                { value: 'security', label: 'Security' },
                { value: 'risk', label: 'Risk Management' },
                { value: 'audit', label: 'Audit' },
                { value: 'reporting', label: 'Reporting' }
              ]
            },
            {
              type: 'select',
              name: 'framework',
              label: 'Framework',
              options: [
                { value: '', label: 'All Frameworks' },
                { value: 'gdpr', label: 'GDPR' },
                { value: 'hipaa', label: 'HIPAA' },
                { value: 'pci', label: 'PCI DSS' },
                { value: 'soc2', label: 'SOC 2' },
                { value: 'iso27001', label: 'ISO 27001' }
              ]
            },
            {
              type: 'textField',
              name: 'search',
              label: 'Search',
              placeholder: 'Search connectors...'
            },
            {
              type: 'select',
              name: 'sort',
              label: 'Sort By',
              options: [
                { value: 'popularity', label: 'Popularity' },
                { value: 'rating', label: 'Rating' },
                { value: 'price-asc', label: 'Price: Low to High' },
                { value: 'price-desc', label: 'Price: High to Low' },
                { value: 'newest', label: 'Newest' }
              ]
            }
          ],
          actions: [
            {
              type: 'button',
              text: 'Apply Filters',
              variant: 'primary',
              onClick: 'novaStore.filterConnectors'
            },
            {
              type: 'button',
              text: 'Reset Filters',
              variant: 'secondary',
              onClick: 'novaStore.resetFilters'
            }
          ]
        },
        items: connectors.map(connector => ({
          type: 'card',
          header: connector.name,
          content: {
            type: 'flex',
            direction: 'column',
            items: [
              {
                type: 'image',
                src: connector.icon,
                alt: `${connector.name} icon`,
                width: 64,
                height: 64
              },
              {
                type: 'text',
                text: connector.description,
                maxLines: 3
              },
              {
                type: 'flex',
                direction: 'row',
                items: [
                  {
                    type: 'rating',
                    value: connector.rating,
                    max: 5
                  },
                  {
                    type: 'text',
                    text: `(${connector.reviewCount})`
                  }
                ]
              },
              {
                type: 'flex',
                direction: 'row',
                items: [
                  {
                    type: 'text',
                    text: connector.price > 0 ? `$${connector.price}` : 'Free',
                    style: {
                      fontWeight: 'bold'
                    }
                  },
                  {
                    type: 'text',
                    text: connector.installed ? 'Installed' : '',
                    style: {
                      color: '#28a745'
                    }
                  }
                ]
              },
              {
                type: 'tags',
                tags: connector.frameworks.map(framework => ({
                  text: framework,
                  variant: 'info'
                }))
              }
            ]
          },
          actions: [
            {
              type: 'button',
              text: 'View Details',
              variant: 'primary',
              onClick: `novaStore.viewConnectorDetails:${connector.id}`
            },
            {
              type: 'button',
              text: connector.installed ? 'Uninstall' : (connector.price > 0 ? 'Purchase' : 'Install'),
              variant: connector.installed ? 'danger' : 'success',
              onClick: connector.installed
                ? `novaStore.uninstallConnector:${connector.id}`
                : (connector.price > 0
                  ? `novaStore.purchaseConnector:${connector.id}`
                  : `novaStore.installConnector:${connector.id}`)
            }
          ]
        })),
        pagination: {
          total: connectors.total,
          limit: connectors.limit,
          offset: connectors.offset,
          onPageChange: 'novaStore.changeConnectorsPage'
        }
      };
    } catch (error) {
      this.logger.error('Error getting connector list schema', error);
      throw error;
    }
  }
  
  /**
   * Get dashboard schema
   * 
   * @param {Object} options - Schema options
   * @returns {Promise<Object>} - Dashboard schema
   */
  async getDashboardSchema(options = {}) {
    try {
      // Get store stats from NovaStore
      const stats = await this.novaStore.getStoreStats();
      
      // Create dashboard schema
      return {
        type: 'card',
        title: 'NovaStore Dashboard',
        content: {
          type: 'grid',
          columns: 2,
          rows: 2,
          areas: [
            ['storeStats', 'connectorDistribution'],
            ['installedConnectors', 'installedConnectors']
          ],
          components: [
            {
              type: 'card',
              gridArea: 'storeStats',
              header: 'Store Statistics',
              content: {
                type: 'stats',
                stats: [
                  { label: 'Total Connectors', value: stats.totalConnectors },
                  { label: 'Installed Connectors', value: stats.installedConnectors },
                  { label: 'Available Frameworks', value: stats.availableFrameworks },
                  { label: 'Total Purchases', value: stats.totalPurchases }
                ]
              }
            },
            {
              type: 'card',
              gridArea: 'connectorDistribution',
              header: 'Connector Distribution',
              content: {
                type: 'chart',
                chartType: 'pie',
                data: {
                  labels: Object.keys(stats.connectorDistribution),
                  datasets: [
                    {
                      data: Object.values(stats.connectorDistribution),
                      backgroundColor: [
                        '#007bff',
                        '#28a745',
                        '#ffc107',
                        '#dc3545',
                        '#17a2b8',
                        '#6f42c1'
                      ]
                    }
                  ]
                }
              }
            },
            {
              type: 'card',
              gridArea: 'installedConnectors',
              header: 'Installed Connectors',
              content: {
                type: 'table',
                columns: [
                  { field: 'name', header: 'Name' },
                  { field: 'category', header: 'Category' },
                  { field: 'version', header: 'Version' },
                  { field: 'installedAt', header: 'Installed At' },
                  { field: 'status', header: 'Status' }
                ],
                data: stats.installedConnectorsList
              }
            }
          ]
        }
      };
    } catch (error) {
      this.logger.error('Error getting dashboard schema', error);
      throw error;
    }
  }
  
  /**
   * Handle action from NovaVision
   * 
   * @param {string} action - Action name
   * @param {Object} data - Action data
   * @returns {Promise<Object>} - Action result
   */
  async handleAction(action, data) {
    if (this.options.enableLogging) {
      this.logger.info(`Handling NovaStore action: ${action}...`, data);
    }
    
    try {
      // Handle action based on action name
      switch (action) {
        case 'viewConnectorDetails':
          return await this.novaStore.viewConnectorDetails(data.connectorId);
        
        case 'installConnector':
          return await this.novaStore.installConnector(data.connectorId);
        
        case 'uninstallConnector':
          return await this.novaStore.uninstallConnector(data.connectorId);
        
        case 'purchaseConnector':
          return await this.novaStore.purchaseConnector(data.connectorId);
        
        case 'filterConnectors':
          return await this.novaStore.filterConnectors(data);
        
        case 'resetFilters':
          return await this.novaStore.resetFilters();
        
        case 'changeConnectorsPage':
          return await this.novaStore.getConnectors({
            limit: data.limit,
            offset: data.offset
          });
        
        case 'viewFrameworkMapping':
          return await this.novaStore.viewFrameworkMapping(data.frameworkId);
        
        case 'mapFrameworks':
          return await this.novaStore.mapFrameworks(data);
        
        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error) {
      this.logger.error(`Error handling NovaStore action: ${action}`, error);
      throw error;
    }
  }
}

export default NovaStoreAdapter;
