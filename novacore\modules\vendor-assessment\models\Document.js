/**
 * NovaCore Document Model
 * 
 * This model defines the schema for documents in the SaaS vendor assessment module.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define document schema
const documentSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  organizationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Organization', 
    required: true 
  },
  vendorId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Vendor' 
  },
  assessmentId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Assessment' 
  },
  type: { 
    type: String, 
    enum: [
      'contract', 
      'sla', 
      'dpa', 
      'security_questionnaire', 
      'audit_report', 
      'certification', 
      'policy', 
      'procedure', 
      'evidence', 
      'other'
    ], 
    default: 'other' 
  },
  format: { 
    type: String, 
    enum: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv', 'json', 'xml', 'image', 'other'], 
    default: 'pdf' 
  },
  mimeType: { 
    type: String, 
    trim: true 
  },
  size: { 
    type: Number 
  },
  path: { 
    type: String, 
    trim: true 
  },
  url: { 
    type: String, 
    trim: true 
  },
  content: { 
    type: Buffer 
  },
  hash: { 
    type: String, 
    trim: true 
  },
  status: { 
    type: String, 
    enum: ['draft', 'active', 'archived', 'expired'], 
    default: 'active' 
  },
  expirationDate: { 
    type: Date 
  },
  tags: [{ 
    type: String, 
    trim: true 
  }],
  frameworks: [{ 
    type: String, 
    trim: true 
  }],
  blockchainVerified: { 
    type: Boolean, 
    default: false 
  },
  blockchainVerificationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'BlockchainVerification' 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
documentSchema.index({ organizationId: 1 });
documentSchema.index({ vendorId: 1 });
documentSchema.index({ assessmentId: 1 });
documentSchema.index({ type: 1 });
documentSchema.index({ status: 1 });
documentSchema.index({ expirationDate: 1 });
documentSchema.index({ tags: 1 });
documentSchema.index({ frameworks: 1 });
documentSchema.index({ blockchainVerified: 1 });
documentSchema.index({ createdAt: 1 });

// Add methods
documentSchema.methods.isActive = function() {
  return this.status === 'active' && 
    (!this.expirationDate || new Date() < this.expirationDate);
};

documentSchema.methods.isExpired = function() {
  return this.expirationDate && new Date() > this.expirationDate;
};

documentSchema.methods.isVerified = function() {
  return this.blockchainVerified;
};

// Add statics
documentSchema.statics.findByOrganization = function(organizationId) {
  return this.find({ organizationId });
};

documentSchema.statics.findByVendor = function(vendorId) {
  return this.find({ vendorId });
};

documentSchema.statics.findByAssessment = function(assessmentId) {
  return this.find({ assessmentId });
};

documentSchema.statics.findActive = function(organizationId) {
  return this.find({ 
    organizationId, 
    status: 'active',
    $or: [
      { expirationDate: { $exists: false } },
      { expirationDate: { $gt: new Date() } }
    ]
  });
};

documentSchema.statics.findExpired = function(organizationId) {
  return this.find({ 
    organizationId, 
    expirationDate: { $lt: new Date() } 
  });
};

documentSchema.statics.findVerified = function(organizationId) {
  return this.find({ 
    organizationId, 
    blockchainVerified: true 
  });
};

// Create model
const Document = mongoose.model('Document', documentSchema);

module.exports = Document;
