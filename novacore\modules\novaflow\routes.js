/**
 * NovaCore NovaFlow Routes
 *
 * This file defines the routes for the NovaFlow module.
 * Nova<PERSON>low is the Universal Compliance Workflow Orchestrator (UCWO) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const express = require('express');
const router = express.Router();
const { WorkflowController, VerificationController } = require('./controllers');
const { authenticate, authorize } = require('../../api/middleware/authMiddleware');

// Workflow Routes

// Create a new workflow
router.post(
  '/workflows',
  authenticate,
  authorize('create:workflow'),
  WorkflowController.createWorkflow
);

// Create workflow from template
router.post(
  '/templates/:templateId/workflows',
  authenticate,
  authorize('create:workflow'),
  WorkflowController.createWorkflowFromTemplate
);

// Get all workflows
router.get(
  '/organizations/:organizationId/workflows',
  authenticate,
  authorize('read:workflow'),
  WorkflowController.getAllWorkflows
);

// Get workflow by ID
router.get(
  '/workflows/:id',
  authenticate,
  authorize('read:workflow'),
  WorkflowController.getWorkflowById
);

// Update workflow
router.put(
  '/workflows/:id',
  authenticate,
  authorize('update:workflow'),
  WorkflowController.updateWorkflow
);

// Delete workflow
router.delete(
  '/workflows/:id',
  authenticate,
  authorize('delete:workflow'),
  WorkflowController.deleteWorkflow
);

// Activate workflow
router.post(
  '/workflows/:id/activate',
  authenticate,
  authorize('update:workflow'),
  WorkflowController.activateWorkflow
);

// Start workflow execution
router.post(
  '/workflows/:id/execute',
  authenticate,
  authorize('execute:workflow'),
  WorkflowController.startWorkflow
);

// Get workflow executions
router.get(
  '/workflows/:id/executions',
  authenticate,
  authorize('read:workflow'),
  WorkflowController.getWorkflowExecutions
);

// Get execution by ID
router.get(
  '/executions/:id',
  authenticate,
  authorize('read:workflow'),
  WorkflowController.getExecutionById
);

// Complete task in workflow execution
router.post(
  '/executions/:executionId/tasks/:taskId/complete',
  authenticate,
  authorize('execute:workflow'),
  WorkflowController.completeTask
);

// Fail task in workflow execution
router.post(
  '/executions/:executionId/tasks/:taskId/fail',
  authenticate,
  authorize('execute:workflow'),
  WorkflowController.failTask
);

// Skip task in workflow execution
router.post(
  '/executions/:executionId/tasks/:taskId/skip',
  authenticate,
  authorize('execute:workflow'),
  WorkflowController.skipTask
);

// Pause workflow execution
router.post(
  '/executions/:id/pause',
  authenticate,
  authorize('execute:workflow'),
  WorkflowController.pauseExecution
);

// Resume workflow execution
router.post(
  '/executions/:id/resume',
  authenticate,
  authorize('execute:workflow'),
  WorkflowController.resumeExecution
);

// Cancel workflow execution
router.post(
  '/executions/:id/cancel',
  authenticate,
  authorize('execute:workflow'),
  WorkflowController.cancelExecution
);

// Process workflow execution
router.post(
  '/executions/:id/process',
  authenticate,
  authorize('execute:workflow'),
  WorkflowController.processExecution
);

// Workflow Template Routes

// Create a new workflow template
router.post(
  '/templates',
  authenticate,
  authorize('create:workflow-template'),
  WorkflowController.createTemplate
);

// Get all workflow templates
router.get(
  '/organizations/:organizationId/templates',
  authenticate,
  authorize('read:workflow-template'),
  WorkflowController.getAllTemplates
);

// Get workflow template by ID
router.get(
  '/templates/:id',
  authenticate,
  authorize('read:workflow-template'),
  WorkflowController.getTemplateById
);

// Update workflow template
router.put(
  '/templates/:id',
  authenticate,
  authorize('update:workflow-template'),
  WorkflowController.updateTemplate
);

// Delete workflow template
router.delete(
  '/templates/:id',
  authenticate,
  authorize('delete:workflow-template'),
  WorkflowController.deleteTemplate
);

// Set template as default
router.post(
  '/templates/:id/set-default',
  authenticate,
  authorize('update:workflow-template'),
  WorkflowController.setTemplateAsDefault
);

// Create new version of template
router.post(
  '/templates/:id/versions',
  authenticate,
  authorize('create:workflow-template'),
  WorkflowController.createNewTemplateVersion
);

// Get default template
router.get(
  '/organizations/:organizationId/default-template',
  authenticate,
  authorize('read:workflow-template'),
  WorkflowController.getDefaultTemplate
);

// Import template from JSON
router.post(
  '/organizations/:organizationId/import-template',
  authenticate,
  authorize('create:workflow-template'),
  WorkflowController.importTemplate
);

// Verification Routes

// Create verification checkpoint
router.post(
  '/workflows/:workflowId/stages/:stageId/checkpoints',
  authenticate,
  authorize('create:verification-checkpoint'),
  VerificationController.createCheckpoint
);

// Process verification checkpoint
router.post(
  '/executions/:executionId/checkpoints/:checkpointId/process',
  authenticate,
  authorize('process:verification-checkpoint'),
  VerificationController.processCheckpoint
);

// Get checkpoints for stage
router.get(
  '/executions/:executionId/stages/:stageId/checkpoints',
  authenticate,
  authorize('read:verification-checkpoint'),
  VerificationController.getCheckpointsForStage
);

// Create verification rule
router.post(
  '/verification/rules',
  authenticate,
  authorize('create:verification-rule'),
  VerificationController.createRule
);

// Verify evidence
router.post(
  '/verification/evidence/:evidenceId/verify',
  authenticate,
  authorize('verify:evidence'),
  VerificationController.verifyEvidence
);

// Get verification record
router.get(
  '/verification/records/:verificationId',
  authenticate,
  authorize('read:verification-record'),
  VerificationController.getVerificationRecord
);

// Get verifications for evidence
router.get(
  '/verification/evidence/:evidenceId/verifications',
  authenticate,
  authorize('read:verification-record'),
  VerificationController.getVerificationsForEvidence
);

module.exports = router;
