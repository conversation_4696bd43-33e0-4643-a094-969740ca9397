/**
 * CSDE Adapter
 *
 * This module provides a concrete implementation of the CSDE (Cyber-Safety Decision Engine)
 * adapter for the Finite Universe Principle defense system. It connects the boundary
 * enforcement mechanisms with the CSDE engine to ensure that cyber domain operations
 * remain within finite boundaries.
 * 
 * Key features:
 * 1. Domain-specific processing for cyber security data
 * 2. Integration with CSDE components
 * 3. Cyber domain boundary enforcement
 * 4. Specialized handling for security metrics
 */

const EventEmitter = require('events');
const { MAX_SAFE_BOUNDS } = require('../constants');

/**
 * CSEDAdapter class
 * 
 * Provides integration between the Boundary Enforcer and the CSDE engine.
 */
class CSEDAdapter extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      strictMode: true,
      csdeInstance: null, // Optional CSDE instance
      ...options
    };

    // Initialize cyber domain boundaries
    this.cyberBoundaries = {
      ...MAX_SAFE_BOUNDS.CYBER,
      // Additional cyber-specific boundaries
      MAX_SECURITY_SCORE: 10,
      MIN_SECURITY_SCORE: 0,
      MAX_THREAT_LEVEL: 10,
      MIN_THREAT_LEVEL: 0,
      MAX_ENCRYPTION_STRENGTH: 4096,
      MIN_ENCRYPTION_STRENGTH: 0
    };

    // Initialize metrics
    this.metrics = {
      processedDataCount: 0,
      boundaryViolations: 0,
      averageSecurityScore: 0,
      averageThreatLevel: 0,
      totalSecurityScore: 0,
      totalThreatLevel: 0
    };

    if (this.options.enableLogging) {
      console.log('CSEDAdapter initialized with options:', this.options);
    }
  }

  /**
   * Process data through the CSDE engine
   * @param {Object} data - Data to process
   * @returns {Object} - Processed data
   */
  async processData(data) {
    try {
      // Apply cyber domain pre-processing
      const preprocessedData = this._preprocessCyberData(data);
      
      // Process through CSDE if available
      let processedData = preprocessedData;
      if (this.options.csdeInstance) {
        processedData = await this._processThroughCSDE(preprocessedData);
      } else {
        // Apply default processing if no CSDE instance is available
        processedData = this._applyDefaultProcessing(preprocessedData);
      }
      
      // Apply cyber domain post-processing
      const postprocessedData = this._postprocessCyberData(processedData);
      
      // Update metrics
      this._updateMetrics(postprocessedData);
      
      return postprocessedData;
    } catch (error) {
      this.emit('processing-error', { error: error.message, data });
      
      if (this.options.strictMode) {
        throw error;
      }
      
      // Return sanitized data on error
      return this._sanitizeCyberData(data);
    }
  }

  /**
   * Pre-process cyber domain data
   * @param {Object} data - Data to pre-process
   * @returns {Object} - Pre-processed data
   * @private
   */
  _preprocessCyberData(data) {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    const result = { ...data };
    
    // Sanitize security score
    if (result.securityScore !== undefined) {
      const originalValue = result.securityScore;
      result.securityScore = Math.max(
        this.cyberBoundaries.MIN_SECURITY_SCORE,
        Math.min(this.cyberBoundaries.MAX_SECURITY_SCORE, result.securityScore)
      );
      
      if (result.securityScore !== originalValue) {
        this.metrics.boundaryViolations++;
      }
    }
    
    // Sanitize threat level
    if (result.threatLevel !== undefined) {
      const originalValue = result.threatLevel;
      result.threatLevel = Math.max(
        this.cyberBoundaries.MIN_THREAT_LEVEL,
        Math.min(this.cyberBoundaries.MAX_THREAT_LEVEL, result.threatLevel)
      );
      
      if (result.threatLevel !== originalValue) {
        this.metrics.boundaryViolations++;
      }
    }
    
    // Sanitize encryption strength
    if (result.encryptionStrength !== undefined) {
      const originalValue = result.encryptionStrength;
      result.encryptionStrength = Math.max(
        this.cyberBoundaries.MIN_ENCRYPTION_STRENGTH,
        Math.min(this.cyberBoundaries.MAX_ENCRYPTION_STRENGTH, result.encryptionStrength)
      );
      
      if (result.encryptionStrength !== originalValue) {
        this.metrics.boundaryViolations++;
      }
    }
    
    return result;
  }

  /**
   * Process data through the CSDE engine
   * @param {Object} data - Data to process
   * @returns {Object} - Processed data
   * @private
   */
  async _processThroughCSDE(data) {
    try {
      // Call CSDE instance to process data
      return await this.options.csdeInstance.processData(data);
    } catch (error) {
      this.emit('csde-processing-error', { error: error.message, data });
      
      // Apply default processing on CSDE error
      return this._applyDefaultProcessing(data);
    }
  }

  /**
   * Apply default processing when no CSDE instance is available
   * @param {Object} data - Data to process
   * @returns {Object} - Processed data
   * @private
   */
  _applyDefaultProcessing(data) {
    const result = { ...data };
    
    // Add CSDE processing flag
    result.csdeProcessed = true;
    
    // Calculate risk score if security score and threat level are available
    if (result.securityScore !== undefined && result.threatLevel !== undefined) {
      result.riskScore = Math.max(0, Math.min(10, 10 - result.securityScore + result.threatLevel));
    }
    
    // Add timestamp
    result.processedAt = new Date().toISOString();
    
    return result;
  }

  /**
   * Post-process cyber domain data
   * @param {Object} data - Data to post-process
   * @returns {Object} - Post-processed data
   * @private
   */
  _postprocessCyberData(data) {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    const result = { ...data };
    
    // Add cyber domain marker
    result._domain = 'cyber';
    
    // Add processing metadata
    result._metadata = {
      processor: 'CSEDAdapter',
      version: '1.0.0',
      timestamp: new Date().toISOString()
    };
    
    return result;
  }

  /**
   * Sanitize cyber domain data (used as fallback)
   * @param {Object} data - Data to sanitize
   * @returns {Object} - Sanitized data
   * @private
   */
  _sanitizeCyberData(data) {
    if (!data || typeof data !== 'object') {
      return { _domain: 'cyber', _sanitized: true };
    }
    
    const result = { ...data, _domain: 'cyber', _sanitized: true };
    
    // Sanitize security score
    if (result.securityScore !== undefined) {
      result.securityScore = Math.max(
        this.cyberBoundaries.MIN_SECURITY_SCORE,
        Math.min(this.cyberBoundaries.MAX_SECURITY_SCORE, result.securityScore)
      );
    }
    
    // Sanitize threat level
    if (result.threatLevel !== undefined) {
      result.threatLevel = Math.max(
        this.cyberBoundaries.MIN_THREAT_LEVEL,
        Math.min(this.cyberBoundaries.MAX_THREAT_LEVEL, result.threatLevel)
      );
    }
    
    // Sanitize encryption strength
    if (result.encryptionStrength !== undefined) {
      result.encryptionStrength = Math.max(
        this.cyberBoundaries.MIN_ENCRYPTION_STRENGTH,
        Math.min(this.cyberBoundaries.MAX_ENCRYPTION_STRENGTH, result.encryptionStrength)
      );
    }
    
    return result;
  }

  /**
   * Update metrics based on processed data
   * @param {Object} data - Processed data
   * @private
   */
  _updateMetrics(data) {
    this.metrics.processedDataCount++;
    
    if (data.securityScore !== undefined) {
      this.metrics.totalSecurityScore += data.securityScore;
      this.metrics.averageSecurityScore = this.metrics.totalSecurityScore / this.metrics.processedDataCount;
    }
    
    if (data.threatLevel !== undefined) {
      this.metrics.totalThreatLevel += data.threatLevel;
      this.metrics.averageThreatLevel = this.metrics.totalThreatLevel / this.metrics.processedDataCount;
    }
  }

  /**
   * Get adapter metrics
   * @returns {Object} - Adapter metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Reset adapter metrics
   */
  resetMetrics() {
    this.metrics = {
      processedDataCount: 0,
      boundaryViolations: 0,
      averageSecurityScore: 0,
      averageThreatLevel: 0,
      totalSecurityScore: 0,
      totalThreatLevel: 0
    };
    
    this.emit('metrics-reset');
  }
}

/**
 * Create a CSDE adapter with recommended settings
 * @param {Object} options - Configuration options
 * @returns {CSEDAdapter} - Configured CSDE adapter
 */
function createCSEDAdapter(options = {}) {
  return new CSEDAdapter({
    enableLogging: true,
    strictMode: true,
    ...options
  });
}

module.exports = {
  CSEDAdapter,
  createCSEDAdapter
};
