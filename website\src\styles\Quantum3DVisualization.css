.quantum-3d-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 500px;
  border-radius: 8px;
  overflow: hidden;
  background: linear-gradient(135deg, #0a0b1e 0%, #1a1b3a 100%);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.quantum-3d-container canvas {
  display: block;
  width: 100%;
  height: 100%;
  outline: none;
}

/* Loading overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(10, 11, 30, 0.8);
  color: #e2e8f0;
  font-family: 'Fira Code', monospace;
  z-index: 10;
  opacity: 1;
  transition: opacity 0.5s ease;
}

.loading-overlay.hidden {
  opacity: 0;
  pointer-events: none;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(138, 43, 226, 0.3);
  border-radius: 50%;
  border-top-color: #8a2be2;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Controls overlay */
.controls-overlay {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.6);
  padding: 8px 16px;
  border-radius: 20px;
  color: #e2e8f0;
  font-size: 12px;
  font-family: 'Fira Code', monospace;
  display: flex;
  gap: 16px;
  z-index: 5;
  backdrop-filter: blur(5px);
}

.control-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.control-key {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 2px 6px;
  font-family: 'Fira Code', monospace;
  font-size: 11px;
}

/* Performance warning */
.performance-warning {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(255, 152, 0, 0.2);
  border-left: 3px solid #ff9800;
  color: #ffcc80;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  max-width: 250px;
  z-index: 5;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .quantum-3d-container {
    height: 400px;
    min-height: 400px;
  }
  
  .controls-overlay {
    flex-direction: column;
    gap: 8px;
    padding: 8px 12px;
    bottom: 8px;
  }
  
  .performance-warning {
    top: 8px;
    right: 8px;
    left: 8px;
    max-width: none;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .quantum-3d-container {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  }
  
  .loading-overlay {
    background: rgba(10, 11, 30, 0.9);
  }
}

/* Print styles */
@media print {
  .quantum-3d-container {
    display: none; /* Hide 3D view in print */
  }
}

/* High contrast mode */
@media (prefers-contrast: more) {
  .quantum-3d-container {
    border: 2px solid #fff;
  }
  
  .loading-spinner {
    border-color: #fff;
    border-top-color: #000;
  }
}
