/**
 * Monitoring Service
 *
 * This service handles operations related to monitoring connectors.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const ConnectorService = require('./ConnectorService');
const TestingService = require('./TestingService');
const { ValidationError } = require('../utils/errors');

class MonitoringService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.healthDataFile = path.join(this.dataDir, 'health_data.json');
    this.alertsFile = path.join(this.dataDir, 'alerts.json');
    this.alertConfigFile = path.join(this.dataDir, 'alert_config.json');
    this.anomalyDataFile = path.join(this.dataDir, 'anomaly_data.json');
    this.connectorService = new ConnectorService();
    this.testingService = new TestingService();
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Load health data from file
   */
  async loadHealthData() {
    try {
      const data = await fs.readFile(this.healthDataFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty object
        return {};
      }
      console.error('Error loading health data:', error);
      throw error;
    }
  }

  /**
   * Save health data to file
   */
  async saveHealthData(healthData) {
    try {
      await fs.writeFile(this.healthDataFile, JSON.stringify(healthData, null, 2));
    } catch (error) {
      console.error('Error saving health data:', error);
      throw error;
    }
  }

  /**
   * Load alerts from file
   */
  async loadAlerts() {
    try {
      const data = await fs.readFile(this.alertsFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error('Error loading alerts:', error);
      throw error;
    }
  }

  /**
   * Save alerts to file
   */
  async saveAlerts(alerts) {
    try {
      await fs.writeFile(this.alertsFile, JSON.stringify(alerts, null, 2));
    } catch (error) {
      console.error('Error saving alerts:', error);
      throw error;
    }
  }

  /**
   * Load alert configuration from file
   */
  async loadAlertConfig() {
    try {
      const data = await fs.readFile(this.alertConfigFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return default config
        return this.getDefaultAlertConfig();
      }
      console.error('Error loading alert configuration:', error);
      throw error;
    }
  }

  /**
   * Save alert configuration to file
   */
  async saveAlertConfig(config) {
    try {
      await fs.writeFile(this.alertConfigFile, JSON.stringify(config, null, 2));
    } catch (error) {
      console.error('Error saving alert configuration:', error);
      throw error;
    }
  }

  /**
   * Get default alert configuration
   */
  getDefaultAlertConfig() {
    return {
      globalSettings: {
        emailEnabled: true,
        emailRecipients: '<EMAIL>',
        slackEnabled: false,
        slackWebhook: '',
        slackChannel: '#alerts',
        smsEnabled: false,
        smsRecipients: '',
        webhookEnabled: false,
        webhookUrl: ''
      },
      thresholds: {
        responseTime: {
          warning: 500,
          error: 1000
        },
        successRate: {
          warning: 90,
          error: 70
        },
        errorRate: {
          warning: 10,
          error: 30
        }
      },
      rules: [
        {
          id: '1',
          name: 'High Response Time',
          description: 'Alert when response time exceeds threshold',
          condition: 'responseTime > {{threshold.responseTime.warning}}',
          severity: 'medium',
          enabled: true
        },
        {
          id: '2',
          name: 'Critical Response Time',
          description: 'Alert when response time exceeds critical threshold',
          condition: 'responseTime > {{threshold.responseTime.error}}',
          severity: 'high',
          enabled: true
        },
        {
          id: '3',
          name: 'Low Success Rate',
          description: 'Alert when success rate falls below threshold',
          condition: 'successRate < {{threshold.successRate.warning}}',
          severity: 'medium',
          enabled: true
        },
        {
          id: '4',
          name: 'Critical Success Rate',
          description: 'Alert when success rate falls below critical threshold',
          condition: 'successRate < {{threshold.successRate.error}}',
          severity: 'high',
          enabled: true
        },
        {
          id: '5',
          name: 'Connection Failure',
          description: 'Alert when connection to API fails',
          condition: 'status == "error" && errorType == "connection"',
          severity: 'critical',
          enabled: true
        }
      ],
      connectorSettings: []
    };
  }

  /**
   * Get health status of all connectors
   */
  async getHealthStatus() {
    const connectors = await this.connectorService.getAllConnectors();
    const healthData = await this.loadHealthData();

    return connectors.map(connector => {
      const connectorHealth = healthData[connector.id] || this.getDefaultHealthData(connector);
      return {
        id: connector.id,
        name: connector.name,
        status: connectorHealth.status,
        responseTime: connectorHealth.responseTime,
        successRate: connectorHealth.successRate,
        requestCount: connectorHealth.requestCount,
        errorCount: connectorHealth.errorCount,
        lastCheck: connectorHealth.lastCheck,
        issues: connectorHealth.issues || []
      };
    });
  }

  /**
   * Get health status of a specific connector
   */
  async getConnectorHealth(connectorId) {
    const connector = await this.connectorService.getConnectorById(connectorId);
    const healthData = await this.loadHealthData();

    const connectorHealth = healthData[connectorId] || this.getDefaultHealthData(connector);

    return {
      id: connector.id,
      name: connector.name,
      status: connectorHealth.status,
      responseTime: connectorHealth.responseTime,
      successRate: connectorHealth.successRate,
      requestCount: connectorHealth.requestCount,
      errorCount: connectorHealth.errorCount,
      lastCheck: connectorHealth.lastCheck,
      issues: connectorHealth.issues || [],
      history: connectorHealth.history || {
        status: Array(24).fill('healthy'),
        responseTimes: Array(24).fill(200)
      }
    };
  }

  /**
   * Get default health data for a connector
   */
  getDefaultHealthData(connector) {
    return {
      status: 'healthy',
      responseTime: 200,
      successRate: 100,
      requestCount: 0,
      errorCount: 0,
      lastCheck: new Date().toISOString(),
      issues: [],
      history: {
        status: Array(24).fill('healthy'),
        responseTimes: Array(24).fill(200)
      }
    };
  }

  /**
   * Run health check for a connector
   */
  async runHealthCheck(connectorId, credentialId) {
    const connector = await this.connectorService.getConnectorById(connectorId);
    const healthData = await this.loadHealthData();

    // Get current health data or initialize
    const currentHealth = healthData[connectorId] || this.getDefaultHealthData(connector);

    try {
      // For each endpoint, run a test
      const results = [];

      for (const endpoint of connector.endpoints) {
        // Skip endpoints marked as not for health checks
        if (endpoint.skipHealthCheck) continue;

        // Execute the endpoint
        const result = await this.testingService.executeEndpoint(
          connectorId,
          endpoint.id,
          credentialId,
          endpoint.healthCheckParameters || {}
        );

        results.push({
          endpoint: endpoint.id,
          status: result.status >= 200 && result.status < 300 ? 'success' : 'error',
          responseTime: result.responseTime,
          statusCode: result.status
        });
      }

      // Calculate health metrics
      const totalRequests = results.length;
      const successfulRequests = results.filter(r => r.status === 'success').length;
      const avgResponseTime = results.length > 0
        ? results.reduce((sum, r) => sum + r.responseTime, 0) / results.length
        : 0;

      // Determine overall status
      let status = 'healthy';
      const issues = [];

      // Check response time
      const alertConfig = await this.loadAlertConfig();
      const responseTimeWarning = alertConfig.thresholds.responseTime.warning;
      const responseTimeError = alertConfig.thresholds.responseTime.error;

      if (avgResponseTime > responseTimeError) {
        status = 'error';
        issues.push({
          type: 'performance',
          message: `High response time: ${Math.round(avgResponseTime)}ms exceeds error threshold of ${responseTimeError}ms`,
          timestamp: new Date().toISOString()
        });
      } else if (avgResponseTime > responseTimeWarning) {
        status = status === 'error' ? 'error' : 'warning';
        issues.push({
          type: 'performance',
          message: `Elevated response time: ${Math.round(avgResponseTime)}ms exceeds warning threshold of ${responseTimeWarning}ms`,
          timestamp: new Date().toISOString()
        });
      }

      // Check success rate
      const successRate = totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 100;
      const successRateWarning = alertConfig.thresholds.successRate.warning;
      const successRateError = alertConfig.thresholds.successRate.error;

      if (successRate < successRateError) {
        status = 'error';
        issues.push({
          type: 'reliability',
          message: `Low success rate: ${Math.round(successRate)}% is below error threshold of ${successRateError}%`,
          timestamp: new Date().toISOString()
        });
      } else if (successRate < successRateWarning) {
        status = status === 'error' ? 'error' : 'warning';
        issues.push({
          type: 'reliability',
          message: `Reduced success rate: ${Math.round(successRate)}% is below warning threshold of ${successRateWarning}%`,
          timestamp: new Date().toISOString()
        });
      }

      // Check for connection failures
      const connectionFailures = results.filter(r => r.status === 'error' && r.statusCode === 0).length;
      if (connectionFailures > 0) {
        status = 'error';
        issues.push({
          type: 'connection',
          message: `Connection failures detected: ${connectionFailures} endpoint(s) could not be reached`,
          timestamp: new Date().toISOString()
        });
      }

      // Update history
      const now = new Date();
      const currentHour = now.getHours();

      // Shift history arrays if needed
      if (!currentHealth.history) {
        currentHealth.history = {
          status: Array(24).fill('healthy'),
          responseTimes: Array(24).fill(200)
        };
      }

      currentHealth.history.status[currentHour] = status;
      currentHealth.history.responseTimes[currentHour] = Math.round(avgResponseTime);

      // Update health data
      const updatedHealth = {
        ...currentHealth,
        status,
        responseTime: Math.round(avgResponseTime),
        successRate: Math.round(successRate),
        requestCount: (currentHealth.requestCount || 0) + totalRequests,
        errorCount: (currentHealth.errorCount || 0) + (totalRequests - successfulRequests),
        lastCheck: now.toISOString(),
        issues
      };

      healthData[connectorId] = updatedHealth;
      await this.saveHealthData(healthData);

      // Check if we need to create alerts
      await this.checkForAlerts(connector, updatedHealth);

      // Detect anomalies
      const anomalyResults = await this.detectAnomalies(connectorId);

      // If anomalies were detected, add them to the health data issues
      if (anomalyResults.anomalies.length > 0) {
        for (const anomaly of anomalyResults.anomalies) {
          updatedHealth.issues.push({
            type: 'anomaly',
            message: anomaly.message,
            timestamp: anomaly.timestamp,
            details: anomaly
          });
        }

        // Update health data with anomalies
        healthData[connectorId] = updatedHealth;
        await this.saveHealthData(healthData);
      }

      return {
        ...updatedHealth,
        anomalies: anomalyResults.anomalies
      };
    } catch (error) {
      console.error(`Error running health check for connector ${connectorId}:`, error);

      // Update health data with error
      const updatedHealth = {
        ...currentHealth,
        status: 'error',
        lastCheck: new Date().toISOString(),
        issues: [
          ...currentHealth.issues || [],
          {
            type: 'system',
            message: `Error running health check: ${error.message}`,
            timestamp: new Date().toISOString()
          }
        ]
      };

      healthData[connectorId] = updatedHealth;
      await this.saveHealthData(healthData);

      throw error;
    }
  }

  /**
   * Check for alerts based on health data
   */
  async checkForAlerts(connector, healthData) {
    const alertConfig = await this.loadAlertConfig();
    const alerts = await this.loadAlerts();

    // Get connector-specific settings
    const connectorSettings = alertConfig.connectorSettings.find(cs => cs.id === connector.id);

    // Skip if alerts are disabled for this connector
    if (connectorSettings && !connectorSettings.enabled) {
      return;
    }

    // Check each rule
    for (const rule of alertConfig.rules) {
      // Skip disabled rules
      if (!rule.enabled) continue;

      // Evaluate rule condition
      const condition = this.parseCondition(rule.condition, alertConfig.thresholds);
      const result = this.evaluateCondition(condition, healthData);

      if (result) {
        // Check if there's already an active alert for this rule and connector
        const existingAlert = alerts.find(a =>
          a.connectorId === connector.id &&
          a.ruleId === rule.id &&
          (a.status === 'active' || a.status === 'acknowledged')
        );

        if (!existingAlert) {
          // Create new alert
          const newAlert = {
            id: uuidv4(),
            connectorId: connector.id,
            connector: {
              id: connector.id,
              name: connector.name,
              version: connector.version,
              category: connector.category,
              status: connector.status
            },
            ruleId: rule.id,
            severity: rule.severity,
            status: 'active',
            message: rule.name,
            description: rule.description,
            timestamp: new Date().toISOString(),
            details: {
              condition: rule.condition,
              healthData: {
                status: healthData.status,
                responseTime: healthData.responseTime,
                successRate: healthData.successRate,
                requestCount: healthData.requestCount,
                errorCount: healthData.errorCount
              }
            },
            comments: [
              {
                user: 'System',
                timestamp: new Date().toISOString(),
                text: 'Alert created'
              }
            ],
            timeline: [
              {
                timestamp: new Date().toISOString(),
                message: 'Alert created'
              }
            ]
          };

          alerts.push(newAlert);
          await this.saveAlerts(alerts);

          // Send notifications
          await this.sendAlertNotifications(newAlert, alertConfig);
        }
      } else {
        // Check if there's an active alert that should be auto-resolved
        const existingAlert = alerts.find(a =>
          a.connectorId === connector.id &&
          a.ruleId === rule.id &&
          (a.status === 'active' || a.status === 'acknowledged')
        );

        if (existingAlert) {
          // Auto-resolve the alert
          existingAlert.status = 'resolved';
          existingAlert.timeline.push({
            timestamp: new Date().toISOString(),
            message: 'Alert auto-resolved'
          });
          existingAlert.comments.push({
            user: 'System',
            timestamp: new Date().toISOString(),
            text: 'Alert auto-resolved: condition no longer met'
          });

          await this.saveAlerts(alerts);
        }
      }
    }
  }

  /**
   * Parse condition string with thresholds
   */
  parseCondition(condition, thresholds) {
    // Replace threshold placeholders with actual values
    return condition.replace(/\{\{threshold\.([^}]+)\.([^}]+)\}\}/g, (match, category, level) => {
      if (thresholds[category] && thresholds[category][level] !== undefined) {
        return thresholds[category][level];
      }
      return match;
    });
  }

  /**
   * Evaluate condition against health data
   */
  evaluateCondition(condition, healthData) {
    // Create a safe evaluation context
    const context = {
      status: healthData.status,
      responseTime: healthData.responseTime,
      successRate: healthData.successRate,
      errorRate: healthData.errorCount > 0 && healthData.requestCount > 0
        ? (healthData.errorCount / healthData.requestCount) * 100
        : 0,
      requestCount: healthData.requestCount,
      errorCount: healthData.errorCount,
      errorType: healthData.issues && healthData.issues.length > 0
        ? healthData.issues[0].type
        : null
    };

    try {
      // Use Function constructor to create a safe evaluation function
      const evalFunc = new Function(
        ...Object.keys(context),
        `return ${condition};`
      );

      return evalFunc(...Object.values(context));
    } catch (error) {
      console.error(`Error evaluating condition "${condition}":`, error);
      return false;
    }
  }

  /**
   * Send alert notifications
   */
  async sendAlertNotifications(alert, config) {
    // This is a placeholder for actual notification sending logic
    console.log(`[NOTIFICATION] Alert: ${alert.message} for ${alert.connector.name}`);

    // Email notifications
    if (config.globalSettings.emailEnabled) {
      console.log(`[EMAIL] To: ${config.globalSettings.emailRecipients}`);
      console.log(`[EMAIL] Subject: [${alert.severity.toUpperCase()}] ${alert.message}`);
      console.log(`[EMAIL] Body: ${alert.description}`);
    }

    // Slack notifications
    if (config.globalSettings.slackEnabled) {
      console.log(`[SLACK] Channel: ${config.globalSettings.slackChannel}`);
      console.log(`[SLACK] Message: [${alert.severity.toUpperCase()}] ${alert.message} - ${alert.description}`);
    }

    // SMS notifications
    if (config.globalSettings.smsEnabled) {
      console.log(`[SMS] To: ${config.globalSettings.smsRecipients}`);
      console.log(`[SMS] Message: [${alert.severity.toUpperCase()}] ${alert.message}`);
    }

    // Webhook notifications
    if (config.globalSettings.webhookEnabled) {
      console.log(`[WEBHOOK] URL: ${config.globalSettings.webhookUrl}`);
      console.log(`[WEBHOOK] Payload: ${JSON.stringify(alert)}`);
    }
  }

  /**
   * Get all alerts
   */
  async getAllAlerts() {
    return this.loadAlerts();
  }

  /**
   * Get alert by ID
   */
  async getAlertById(id) {
    const alerts = await this.loadAlerts();
    const alert = alerts.find(a => a.id === id);

    if (!alert) {
      throw new Error(`Alert with ID ${id} not found`);
    }

    return alert;
  }

  /**
   * Acknowledge alert
   */
  async acknowledgeAlert(id, user, comment) {
    const alerts = await this.loadAlerts();
    const index = alerts.findIndex(a => a.id === id);

    if (index === -1) {
      throw new Error(`Alert with ID ${id} not found`);
    }

    if (alerts[index].status === 'resolved') {
      throw new ValidationError('Cannot acknowledge a resolved alert');
    }

    alerts[index].status = 'acknowledged';
    alerts[index].timeline.push({
      timestamp: new Date().toISOString(),
      message: `Alert acknowledged by ${user}`
    });

    if (comment) {
      alerts[index].comments.push({
        user,
        timestamp: new Date().toISOString(),
        text: comment
      });
    }

    await this.saveAlerts(alerts);

    return alerts[index];
  }

  /**
   * Resolve alert
   */
  async resolveAlert(id, user, comment) {
    const alerts = await this.loadAlerts();
    const index = alerts.findIndex(a => a.id === id);

    if (index === -1) {
      throw new Error(`Alert with ID ${id} not found`);
    }

    if (alerts[index].status === 'resolved') {
      throw new ValidationError('Alert is already resolved');
    }

    alerts[index].status = 'resolved';
    alerts[index].timeline.push({
      timestamp: new Date().toISOString(),
      message: `Alert resolved by ${user}`
    });

    if (comment) {
      alerts[index].comments.push({
        user,
        timestamp: new Date().toISOString(),
        text: comment
      });
    }

    await this.saveAlerts(alerts);

    return alerts[index];
  }

  /**
   * Add comment to alert
   */
  async addAlertComment(id, user, comment) {
    if (!comment) {
      throw new ValidationError('Comment text is required');
    }

    const alerts = await this.loadAlerts();
    const index = alerts.findIndex(a => a.id === id);

    if (index === -1) {
      throw new Error(`Alert with ID ${id} not found`);
    }

    alerts[index].comments.push({
      user,
      timestamp: new Date().toISOString(),
      text: comment
    });

    await this.saveAlerts(alerts);

    return alerts[index];
  }

  /**
   * Get alert configuration
   */
  async getAlertConfig() {
    return this.loadAlertConfig();
  }

  /**
   * Update alert configuration
   */
  async updateAlertConfig(config) {
    // Validate config
    if (!config.globalSettings) {
      throw new ValidationError('Global settings are required');
    }

    if (!config.thresholds) {
      throw new ValidationError('Thresholds are required');
    }

    if (!config.rules || !Array.isArray(config.rules)) {
      throw new ValidationError('Rules must be an array');
    }

    await this.saveAlertConfig(config);

    return config;
  }

  /**
   * Load anomaly data from file
   */
  async loadAnomalyData() {
    try {
      const data = await fs.readFile(this.anomalyDataFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty object
        return {};
      }
      console.error('Error loading anomaly data:', error);
      throw error;
    }
  }

  /**
   * Save anomaly data to file
   */
  async saveAnomalyData(anomalyData) {
    try {
      await fs.writeFile(this.anomalyDataFile, JSON.stringify(anomalyData, null, 2));
    } catch (error) {
      console.error('Error saving anomaly data:', error);
      throw error;
    }
  }

  /**
   * Detect anomalies in connector health data
   */
  async detectAnomalies(connectorId) {
    try {
      // Get connector health data
      const healthData = await this.loadHealthData();
      const connectorHealth = healthData[connectorId];

      if (!connectorHealth || !connectorHealth.history) {
        return { anomalies: [] };
      }

      // Load existing anomaly data
      const anomalyData = await this.loadAnomalyData();
      const connectorAnomalies = anomalyData[connectorId] || {
        lastDetection: null,
        baseline: null,
        anomalies: []
      };

      // Get current timestamp
      const now = new Date();

      // Calculate or update baseline if needed
      if (!connectorAnomalies.baseline || this.shouldUpdateBaseline(connectorAnomalies.lastDetection)) {
        connectorAnomalies.baseline = this.calculateBaseline(connectorHealth.history);
      }

      // Detect response time anomalies
      const responseTimeAnomalies = this.detectResponseTimeAnomalies(
        connectorHealth.history.responseTimes,
        connectorAnomalies.baseline.responseTimes
      );

      // Detect status anomalies
      const statusAnomalies = this.detectStatusAnomalies(
        connectorHealth.history.status,
        connectorAnomalies.baseline.statusDistribution
      );

      // Combine anomalies
      const newAnomalies = [...responseTimeAnomalies, ...statusAnomalies];

      // Add new anomalies to the list
      if (newAnomalies.length > 0) {
        connectorAnomalies.anomalies = [
          ...newAnomalies,
          ...connectorAnomalies.anomalies
        ].slice(0, 100); // Keep only the 100 most recent anomalies
      }

      // Update last detection timestamp
      connectorAnomalies.lastDetection = now.toISOString();

      // Save updated anomaly data
      anomalyData[connectorId] = connectorAnomalies;
      await this.saveAnomalyData(anomalyData);

      // Return detected anomalies
      return {
        anomalies: newAnomalies,
        baseline: connectorAnomalies.baseline
      };
    } catch (error) {
      console.error(`Error detecting anomalies for connector ${connectorId}:`, error);
      throw error;
    }
  }

  /**
   * Check if baseline should be updated
   */
  shouldUpdateBaseline(lastDetection) {
    if (!lastDetection) return true;

    const lastDetectionDate = new Date(lastDetection);
    const now = new Date();

    // Update baseline once a day
    return (now - lastDetectionDate) > (24 * 60 * 60 * 1000);
  }

  /**
   * Calculate baseline from historical data
   */
  calculateBaseline(history) {
    // Calculate response time statistics
    const responseTimes = history.responseTimes || [];
    const validResponseTimes = responseTimes.filter(time => time > 0);

    const responseTimeStats = {
      mean: this.calculateMean(validResponseTimes),
      stdDev: this.calculateStdDev(validResponseTimes),
      min: Math.min(...validResponseTimes, 0),
      max: Math.max(...validResponseTimes, 0),
      median: this.calculateMedian(validResponseTimes)
    };

    // Calculate status distribution
    const statusCounts = {};
    const statuses = history.status || [];

    for (const status of statuses) {
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    }

    const statusDistribution = {};
    const totalStatuses = statuses.length || 1; // Avoid division by zero

    for (const [status, count] of Object.entries(statusCounts)) {
      statusDistribution[status] = count / totalStatuses;
    }

    return {
      responseTimes: responseTimeStats,
      statusDistribution,
      calculatedAt: new Date().toISOString()
    };
  }

  /**
   * Calculate mean of an array
   */
  calculateMean(arr) {
    if (!arr.length) return 0;
    return arr.reduce((sum, val) => sum + val, 0) / arr.length;
  }

  /**
   * Calculate standard deviation of an array
   */
  calculateStdDev(arr) {
    if (!arr.length) return 0;

    const mean = this.calculateMean(arr);
    const squareDiffs = arr.map(value => {
      const diff = value - mean;
      return diff * diff;
    });

    const avgSquareDiff = this.calculateMean(squareDiffs);
    return Math.sqrt(avgSquareDiff);
  }

  /**
   * Calculate median of an array
   */
  calculateMedian(arr) {
    if (!arr.length) return 0;

    const sorted = [...arr].sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);

    if (sorted.length % 2 === 0) {
      return (sorted[middle - 1] + sorted[middle]) / 2;
    }

    return sorted[middle];
  }

  /**
   * Detect response time anomalies
   */
  detectResponseTimeAnomalies(responseTimes, baseline) {
    const anomalies = [];
    const now = new Date().toISOString();

    // Z-score threshold for anomaly detection
    const zScoreThreshold = 3;

    for (let i = 0; i < responseTimes.length; i++) {
      const responseTime = responseTimes[i];

      // Skip invalid response times
      if (responseTime <= 0) continue;

      // Calculate z-score
      const zScore = Math.abs((responseTime - baseline.mean) / baseline.stdDev);

      // Check if response time is an anomaly
      if (zScore > zScoreThreshold) {
        anomalies.push({
          id: uuidv4(),
          type: 'response_time',
          timestamp: now,
          hour: i,
          value: responseTime,
          expected: baseline.mean,
          deviation: zScore,
          message: `Response time of ${responseTime}ms is ${zScore.toFixed(2)} standard deviations from the mean (${baseline.mean.toFixed(2)}ms)`
        });
      }
    }

    return anomalies;
  }

  /**
   * Detect status anomalies
   */
  detectStatusAnomalies(statuses, statusDistribution) {
    const anomalies = [];
    const now = new Date().toISOString();

    // Probability threshold for anomaly detection
    const probabilityThreshold = 0.1;

    for (let i = 0; i < statuses.length; i++) {
      const status = statuses[i];

      // Skip if status is healthy or undefined
      if (!status || status === 'healthy') continue;

      // Get probability of this status
      const probability = statusDistribution[status] || 0;

      // Check if status is an anomaly
      if (probability < probabilityThreshold) {
        anomalies.push({
          id: uuidv4(),
          type: 'status',
          timestamp: now,
          hour: i,
          value: status,
          probability,
          message: `Unusual status '${status}' detected with probability ${(probability * 100).toFixed(2)}%`
        });
      }
    }

    return anomalies;
  }

  /**
   * Get anomalies for a connector
   */
  async getConnectorAnomalies(connectorId) {
    try {
      const anomalyData = await this.loadAnomalyData();
      const connectorAnomalies = anomalyData[connectorId] || {
        lastDetection: null,
        baseline: null,
        anomalies: []
      };

      return {
        connectorId,
        lastDetection: connectorAnomalies.lastDetection,
        baseline: connectorAnomalies.baseline,
        anomalies: connectorAnomalies.anomalies
      };
    } catch (error) {
      console.error(`Error getting anomalies for connector ${connectorId}:`, error);
      throw error;
    }
  }

  /**
   * Get all anomalies across all connectors
   */
  async getAllAnomalies() {
    try {
      const anomalyData = await this.loadAnomalyData();
      const result = [];

      for (const [connectorId, data] of Object.entries(anomalyData)) {
        result.push({
          connectorId,
          lastDetection: data.lastDetection,
          anomalyCount: data.anomalies.length,
          recentAnomalies: data.anomalies.slice(0, 5) // Return only the 5 most recent anomalies
        });
      }

      return result;
    } catch (error) {
      console.error('Error getting all anomalies:', error);
      throw error;
    }
  }
}

module.exports = MonitoringService;
