# NovaFuse Migration Guide

This guide provides instructions for migrating code from the existing NovaFuse repositories to the new repository structure.

## Overview

The NovaFuse project is being reorganized into the following repositories:

1. **nova-fuse** - Main repository with documentation and project overview
2. **nova-connect** - Universal API Connector for seamless API integration
3. **nova-grc-apis** - Collection of GRC APIs
4. **nova-ui** - UI components for all NovaFuse products
5. **nova-gateway** - API Gateway for routing and managing API requests

This migration guide will help you move code from the existing repositories to the new structure.

## Prerequisites

- PowerShell 5.1 or higher
- Access to the existing NovaFuse repositories
- Access to the new NovaFuse repositories

## Migration Process

### Step 1: Configure the Migration

1. Open the `nova-fuse\scripts\migration-config.json` file
2. Update the `sourceRoot` setting to point to the root directory of the source code
3. Update the `destinationRoot` setting to point to the root directory where the new repositories are located
4. Update the `logFile` setting to specify the path to the log file
5. Review the `migrationMappings` array to ensure it includes all the code that needs to be migrated

### Step 2: Run the Migration Script

1. Open a command prompt
2. Navigate to the `nova-fuse\scripts` directory
3. Run the `run-migration.bat` script:
   ```
   run-migration.bat
   ```
4. The script will perform the following actions:
   - Copy files from the source repositories to the new repositories
   - Update import paths in JavaScript/TypeScript files
   - Verify that all files were migrated successfully
   - Generate log files with details of the migration process

### Step 3: Review the Migration Results

1. Check the log files for any errors or warnings:
   - Migration log: `nova-fuse\scripts\migration-log.txt`
   - Verification log: `nova-fuse\scripts\verification-log.txt`
2. If there are any issues, fix them manually and run the verification script again:
   ```
   powershell -ExecutionPolicy Bypass -File "verify-migration.ps1"
   ```

### Step 4: Update Dependencies

1. Review the `package.json` files in each repository to ensure they include all the necessary dependencies
2. Update the dependencies as needed to reflect the new repository structure
3. Run `npm install` in each repository to install the dependencies

### Step 5: Test the Migrated Code

1. Run the tests in each repository to ensure the migrated code works correctly
2. Fix any issues that are found during testing
3. Verify that all features work as expected

## Troubleshooting

### Common Issues

#### Source Path Does Not Exist

If the migration script reports that a source path does not exist, check the following:

1. Verify that the `sourceRoot` setting in the configuration file is correct
2. Verify that the `sourcePath` setting for the migration mapping is correct
3. Verify that the source directory exists

#### Destination Path Does Not Exist

If the migration script reports that a destination path does not exist, check the following:

1. Verify that the `destinationRoot` setting in the configuration file is correct
2. Verify that the `destinationPath` setting for the migration mapping is correct
3. Verify that the destination directory exists

#### Missing Files

If the verification script reports that files are missing, check the following:

1. Verify that the source files exist
2. Verify that the destination directory exists
3. Verify that the file filter is correct
4. Check the migration log for any errors during the copy process

#### Import Path Replacements

If the import path replacements are not working correctly, check the following:

1. Verify that the regular expressions in the `importPathReplacements` dictionary are correct
2. Verify that the replacement strings are correct
3. Check the migration log for any errors during the import path replacement process

## Manual Migration

If the automated migration process does not work for some files, you can migrate them manually:

1. Copy the files from the source repository to the new repository
2. Update the import paths in the files to reflect the new repository structure
3. Update any other references to the old repository structure

## Post-Migration Tasks

After the migration is complete, you should:

1. Update the documentation to reflect the new repository structure
2. Update the CI/CD pipelines to build and test the new repositories
3. Update any deployment scripts to deploy the new repositories
4. Notify team members of the new repository structure

## Conclusion

This guide has provided instructions for migrating code from the existing NovaFuse repositories to the new repository structure. If you encounter any issues during the migration process, please refer to the troubleshooting section or contact the NovaFuse team for assistance.
