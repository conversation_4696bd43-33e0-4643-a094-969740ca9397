<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaConnect UAC - Universal API Connector | NovaFuse</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .diagram-container {
            position: relative;
            height: 400px;
            margin: 2rem 0;
        }
        
        .diagram-node {
            position: absolute;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .diagram-node:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }
        
        .diagram-node-center {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 150px;
            height: 150px;
            background-color: #2563eb;
            color: white;
            z-index: 10;
        }
        
        .diagram-node-center:hover {
            transform: translate(-50%, -50%) scale(1.1);
        }
        
        .diagram-node-1 {
            top: 20%;
            left: 20%;
            background-color: #10b981;
            color: white;
        }
        
        .diagram-node-2 {
            top: 20%;
            right: 20%;
            background-color: #f59e0b;
            color: white;
        }
        
        .diagram-node-3 {
            bottom: 20%;
            left: 20%;
            background-color: #ef4444;
            color: white;
        }
        
        .diagram-node-4 {
            bottom: 20%;
            right: 20%;
            background-color: #8b5cf6;
            color: white;
        }
        
        .diagram-line {
            position: absolute;
            background-color: #64748b;
            z-index: 5;
        }
        
        .diagram-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .diagram-label {
            font-weight: 600;
            font-size: 0.875rem;
        }
        
        .step-card {
            background-color: #1e293b;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
            border-left: 4px solid #2563eb;
        }
        
        .step-number {
            position: absolute;
            top: -15px;
            left: -15px;
            width: 40px;
            height: 40px;
            background-color: #2563eb;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.25rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .animation-container {
            height: 200px;
            background-color: #1e293b;
            border-radius: 0.5rem;
            position: relative;
            overflow: hidden;
            margin: 2rem 0;
        }
        
        .data-packet {
            position: absolute;
            width: 20px;
            height: 20px;
            background-color: #2563eb;
            border-radius: 50%;
            top: 50%;
            transform: translateY(-50%);
            animation: movePacket 3s infinite linear;
        }
        
        @keyframes movePacket {
            0% {
                left: 0;
                background-color: #ef4444;
            }
            50% {
                background-color: #f59e0b;
            }
            100% {
                left: calc(100% - 20px);
                background-color: #10b981;
            }
        }
        
        .use-case-card {
            background-color: #1e293b;
            border-radius: 0.5rem;
            padding: 1.5rem;
            height: 100%;
            transition: transform 0.3s ease;
            border-top: 4px solid #2563eb;
        }
        
        .use-case-card:hover {
            transform: translateY(-10px);
        }
        
        .use-case-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #2563eb;
        }
    </style>
</head>
<body>
    <!-- Include Header and Navigation -->
    <div id="header-container"></div>

    <main class="container">
        <!-- Breadcrumbs -->
        <div id="breadcrumbs-container"></div>

        <!-- Hero Section -->
        <div class="hero">
            <h2>NovaConnect Universal API Connector</h2>
            <p>
                The central nervous system for your digital operations.
                Connect, normalize, and control data across your entire ecosystem in real-time.
            </p>
        </div>

        <!-- Technology Overview -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6">Technology Overview</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <p class="mb-4">
                        The NovaConnect Universal API Connector (UAC) is a revolutionary technology that enables bidirectional data flow and control between any systems with an API. Unlike traditional integration platforms that simply move data from point A to point B, the UAC provides true cross-domain intelligence and control.
                    </p>
                    <p class="mb-4">
                        With 4 patents pending, the UAC technology normalizes data across 59+ domains in under 100ms, enabling real-time compliance monitoring, cross-system automation, and unprecedented visibility into your digital operations.
                    </p>
                    <p>
                        The UAC serves as the foundation for NovaFuse's suite of GRC products, but its applications extend far beyond compliance and risk management.
                    </p>
                </div>
                <div class="diagram-container">
                    <div class="diagram-node diagram-node-center">
                        <i class="fas fa-exchange-alt diagram-icon"></i>
                        <span class="diagram-label">NovaConnect UAC</span>
                    </div>
                    <div class="diagram-node diagram-node-1">
                        <i class="fas fa-shield-alt diagram-icon"></i>
                        <span class="diagram-label">Security Tools</span>
                    </div>
                    <div class="diagram-node diagram-node-2">
                        <i class="fas fa-cloud diagram-icon"></i>
                        <span class="diagram-label">Cloud Services</span>
                    </div>
                    <div class="diagram-node diagram-node-3">
                        <i class="fas fa-database diagram-icon"></i>
                        <span class="diagram-label">Data Systems</span>
                    </div>
                    <div class="diagram-node diagram-node-4">
                        <i class="fas fa-cogs diagram-icon"></i>
                        <span class="diagram-label">Business Apps</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Key Differentiators -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6">Key Differentiators</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Speed -->
                <div class="card">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 mr-4 bg-blue-600 rounded-md flex items-center justify-center">
                            <i class="fas fa-bolt text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold">100ms Data Normalization</h3>
                    </div>
                    <p class="mb-4">
                        Traditional integration platforms take seconds or even minutes to process data. The UAC normalizes and transforms data in under 100 milliseconds, enabling true real-time operations.
                    </p>
                    <ul class="list-disc list-inside text-secondary">
                        <li>Real-time compliance monitoring</li>
                        <li>Instant cross-system updates</li>
                        <li>Immediate policy enforcement</li>
                    </ul>
                </div>

                <!-- Universal Connectivity -->
                <div class="card">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 mr-4 bg-blue-600 rounded-md flex items-center justify-center">
                            <i class="fas fa-plug text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold">Universal Connectivity</h3>
                    </div>
                    <p class="mb-4">
                        Connect any API to any API without custom coding. The UAC's intelligent mapping engine automatically understands and translates between different data models and schemas.
                    </p>
                    <ul class="list-disc list-inside text-secondary">
                        <li>REST, SOAP, GraphQL support</li>
                        <li>Legacy system integration</li>
                        <li>Custom API support</li>
                    </ul>
                </div>

                <!-- Cross-Domain Mapping -->
                <div class="card">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 mr-4 bg-blue-600 rounded-md flex items-center justify-center">
                            <i class="fas fa-project-diagram text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold">Cross-Domain Mapping</h3>
                    </div>
                    <p class="mb-4">
                        The UAC understands the relationships between 59+ different domains, from security and compliance to HR and finance, enabling unprecedented cross-domain intelligence.
                    </p>
                    <ul class="list-disc list-inside text-secondary">
                        <li>59+ domain knowledge</li>
                        <li>Automatic field mapping</li>
                        <li>Semantic understanding</li>
                    </ul>
                </div>

                <!-- Bidirectional Control -->
                <div class="card">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 mr-4 bg-blue-600 rounded-md flex items-center justify-center">
                            <i class="fas fa-exchange-alt text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold">Bidirectional Control</h3>
                    </div>
                    <p class="mb-4">
                        Unlike traditional data pipelines, the UAC provides true bidirectional control. Not just reading data, but writing, updating, and controlling systems in real-time.
                    </p>
                    <ul class="list-disc list-inside text-secondary">
                        <li>Read and write operations</li>
                        <li>Cross-system orchestration</li>
                        <li>Automated remediation</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- How It Works -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6">How It Works</h2>
            
            <div class="animation-container">
                <div class="data-packet"></div>
                <div class="data-packet" style="animation-delay: 1s;"></div>
                <div class="data-packet" style="animation-delay: 2s;"></div>
            </div>
            
            <div class="grid grid-cols-1 gap-6">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <h3 class="text-xl font-bold mb-2">Connection</h3>
                    <p>
                        The UAC connects to your systems through their APIs. This can be done through direct API connections, webhooks, or our agent-based connectors for systems without public APIs.
                    </p>
                </div>
                
                <div class="step-card">
                    <div class="step-number">2</div>
                    <h3 class="text-xl font-bold mb-2">Normalization</h3>
                    <p>
                        Data from different systems is normalized into a common format. The UAC's AI-powered mapping engine automatically understands the relationships between fields and entities across different systems.
                    </p>
                </div>
                
                <div class="step-card">
                    <div class="step-number">3</div>
                    <h3 class="text-xl font-bold mb-2">Intelligence</h3>
                    <p>
                        The normalized data is processed by the UAC's intelligence engine, which applies business rules, compliance requirements, and machine learning to derive insights and trigger actions.
                    </p>
                </div>
                
                <div class="step-card">
                    <div class="step-number">4</div>
                    <h3 class="text-xl font-bold mb-2">Control</h3>
                    <p>
                        Based on the intelligence derived, the UAC can take action across your systems - updating records, triggering workflows, enforcing policies, and more - all in real-time.
                    </p>
                </div>
            </div>
        </section>

        <!-- Use Cases -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6">Use Cases</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Compliance and GRC -->
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Compliance and GRC</h3>
                    <p class="mb-4">
                        Connect security tools, documentation platforms, and audit systems for real-time compliance monitoring and enforcement.
                    </p>
                    <a href="solutions-by-use-case.html#compliance" class="text-blue-400 hover:text-blue-300">Learn More →</a>
                </div>
                
                <!-- Business Process Automation -->
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Business Process Automation</h3>
                    <p class="mb-4">
                        Automate complex business processes that span multiple systems and departments without custom coding.
                    </p>
                    <a href="solutions-by-use-case.html#automation" class="text-blue-400 hover:text-blue-300">Learn More →</a>
                </div>
                
                <!-- Cross-System Intelligence -->
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Cross-System Intelligence</h3>
                    <p class="mb-4">
                        Gain insights by connecting data across previously siloed systems. Identify patterns and relationships.
                    </p>
                    <a href="solutions-by-use-case.html#intelligence" class="text-blue-400 hover:text-blue-300">Learn More →</a>
                </div>
                
                <!-- Data Integration -->
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Data Integration</h3>
                    <p class="mb-4">
                        Integrate data from multiple sources into a single source of truth, with real-time updates and bidirectional sync.
                    </p>
                    <a href="solutions-by-use-case.html#integration" class="text-blue-400 hover:text-blue-300">Learn More →</a>
                </div>
                
                <!-- Real-Time Monitoring -->
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Real-Time Monitoring</h3>
                    <p class="mb-4">
                        Monitor compliance status, security posture, and operational metrics in real-time across all your systems.
                    </p>
                    <a href="solutions-by-use-case.html#monitoring" class="text-blue-400 hover:text-blue-300">Learn More →</a>
                </div>
                
                <!-- Multi-Cloud Management -->
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-cloud"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Multi-Cloud Management</h3>
                    <p class="mb-4">
                        Manage and govern resources across AWS, Azure, GCP, and on-premises environments from a single interface.
                    </p>
                    <a href="solutions-by-use-case.html#cloud" class="text-blue-400 hover:text-blue-300">Learn More →</a>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="mb-12">
            <div class="bg-blue-900 rounded-lg p-8 text-center">
                <h2 class="text-2xl font-bold mb-4">Ready to Experience the UAC Difference?</h2>
                <p class="mb-6 max-w-3xl mx-auto">
                    Schedule a demo to see how the NovaConnect Universal API Connector can transform your digital operations.
                </p>
                <div class="flex justify-center space-x-4">
                    <a href="contact.html" class="btn btn-primary">Request a Demo</a>
                    <a href="partner-empowerment.html" class="btn btn-outline">Partner with Us</a>
                </div>
            </div>
        </section>
    </main>

    <!-- Include Footer -->
    <div id="footer-container"></div>

    <script src="js/main.js"></script>
    <script>
        // Load header and footer components
        document.addEventListener('DOMContentLoaded', function() {
            // Load header
            fetch('components/header.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('header-container').innerHTML = data;
                    // Set active nav item
                    document.getElementById('nav-uac').classList.add('active');
                });
            
            // Load footer
            fetch('components/footer.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('footer-container').innerHTML = data;
                });
                
            // Load breadcrumbs
            fetch('components/breadcrumbs.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('breadcrumbs-container').innerHTML = data;
                    // Add current page to breadcrumbs
                    const breadcrumbs = document.querySelector('.breadcrumbs');
                    const li = document.createElement('li');
                    const a = document.createElement('a');
                    a.href = 'novaconnect-uac.html';
                    a.textContent = 'NovaConnect UAC';
                    li.appendChild(a);
                    breadcrumbs.appendChild(li);
                });
                
            // Animate diagram lines
            const centerNode = document.querySelector('.diagram-node-center');
            const centerRect = centerNode.getBoundingClientRect();
            const centerX = centerRect.left + centerRect.width / 2;
            const centerY = centerRect.top + centerRect.height / 2;
            
            const nodes = document.querySelectorAll('.diagram-node:not(.diagram-node-center)');
            nodes.forEach(node => {
                const rect = node.getBoundingClientRect();
                const nodeX = rect.left + rect.width / 2;
                const nodeY = rect.top + rect.height / 2;
                
                const angle = Math.atan2(nodeY - centerY, nodeX - centerX);
                const distance = Math.sqrt(Math.pow(nodeX - centerX, 2) + Math.pow(nodeY - centerY, 2));
                
                const line = document.createElement('div');
                line.className = 'diagram-line';
                line.style.width = `${distance}px`;
                line.style.height = '2px';
                line.style.top = `${centerY}px`;
                line.style.left = `${centerX}px`;
                line.style.transformOrigin = '0 0';
                line.style.transform = `rotate(${angle}rad)`;
                
                document.querySelector('.diagram-container').appendChild(line);
            });
        });
    </script>
</body>
</html>
