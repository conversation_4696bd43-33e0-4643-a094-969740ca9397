<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patent Shield Diagram</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 800px;
            height: 650px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            box-sizing: border-box;
        }
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
        }
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            box-sizing: border-box;
            font-size: 14px;
            line-height: 1.2;
            z-index: 1;
        }
        .dashed-box {
            border: 1px dashed #333;
        }
        .component-number {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 20px;
            height: 20px;
            background-color: #555555; /* Changed from blue to grey for patent compliance */
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
        }
        .component-label {
            font-weight: bold;
            margin-bottom: 4px;
        }
        .legend {
            position: absolute;
            right: 10px;
            bottom: 10px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            z-index: 10;
            width: 200px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border: 1px solid #333;
        }
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 10px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>FIG. 2: Patent Shield & Strategic Moat</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 600px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">PATENT SHIELD & STRATEGIC MOAT</div>
        </div>

        <!-- Central Shield -->
        <svg width="800" height="600" style="position: absolute; top: 0; left: 0; z-index: 0;">
            <path
                d="M 400,80 L 600,130 L 600,300 C 600,400 500,480 400,500 C 300,480 200,400 200,300 L 200,130 Z"
                fill="none"
                stroke="#333"
                stroke-width="2"
            />
        </svg>

        <!-- Core Patent Components -->
        <div class="component-box" style="left: 325px; top: 100px; width: 150px; height: 60px;">
            <div class="component-number">601</div>
            <div class="component-label" style="font-size: 16px;">God Patent</div>
            <span style="font-size: 12px;">Cyber-Safety Framework</span>
        </div>

        <!-- Key Innovations -->
        <div class="component-box" style="left: 250px; top: 180px; width: 300px; height: 40px;">
            <div class="component-number">602</div>
            <div class="component-label" style="font-size: 16px; color: #555555;">48 Foundational Patents</div>
        </div>

        <!-- Patent Categories -->
        <div class="component-box" style="left: 150px; top: 240px; width: 150px; height: 60px;">
            <div class="component-number">603</div>
            <div class="component-label" style="font-size: 14px;">Universal Architecture</div>
            <span style="font-size: 12px;">12 Pillars</span>
        </div>

        <div class="component-box" style="left: 325px; top: 240px; width: 150px; height: 60px;">
            <div class="component-number">604</div>
            <div class="component-label" style="font-size: 14px;">AI/ML Compliance</div>
            <span style="font-size: 12px;">12 Novas</span>
        </div>

        <div class="component-box" style="left: 500px; top: 240px; width: 150px; height: 60px;">
            <div class="component-number">605</div>
            <div class="component-label" style="font-size: 14px;">Verifiable Identity</div>
            <span style="font-size: 12px;">NovaDNA</span>
        </div>

        <!-- Key Innovations -->
        <div class="component-box dashed-box" style="left: 150px; top: 320px; width: 150px; height: 60px;">
            <div class="component-number">606</div>
            <div class="component-label" style="font-size: 12px;">Self-Destructing Servers</div>
            <span style="font-size: 12px;">Pillar 3</span>
        </div>

        <div class="component-box dashed-box" style="left: 325px; top: 320px; width: 150px; height: 60px;">
            <div class="component-number">607</div>
            <div class="component-label" style="font-size: 12px;">GDPR-by-Default Compiler</div>
            <span style="font-size: 12px;">Pillar 4</span>
        </div>

        <div class="component-box dashed-box" style="left: 500px; top: 320px; width: 150px; height: 60px;">
            <div class="component-number">608</div>
            <div class="component-label" style="font-size: 12px;">Post-Quantum Journal</div>
            <span style="font-size: 12px;">Pillar 9</span>
        </div>

        <!-- Strategic Moat -->
        <div class="container-box" style="width: 700px; height: 120px; left: 50px; top: 400px;">
            <div class="container-label" style="font-size: 16px;">STRATEGIC MOAT</div>
        </div>

        <div class="component-box" style="left: 100px; top: 440px; width: 180px; height: 60px;">
            <div class="component-number">609</div>
            <div class="component-label" style="font-size: 14px;">Zero Legal Risk</div>
            <span style="font-size: 12px;">Full IP Protection</span>
        </div>

        <div class="component-box" style="left: 310px; top: 440px; width: 180px; height: 60px;">
            <div class="component-number">610</div>
            <div class="component-label" style="font-size: 14px;">First-Mover Rights</div>
            <span style="font-size: 12px; color: #555555;">$1.2T Compliance-Cloud Market</span>
        </div>

        <div class="component-box" style="left: 520px; top: 440px; width: 180px; height: 60px;">
            <div class="component-number">611</div>
            <div class="component-label" style="font-size: 14px;">Cross-Domain Scaling</div>
            <span style="font-size: 12px;">Consistent Performance</span>
        </div>

        <!-- Competitive Advantage -->
        <div class="container-box" style="width: 700px; height: 80px; left: 50px; top: 520px;">
            <div class="container-label" style="font-size: 16px;">COMPETITIVE ADVANTAGE</div>
        </div>

        <div class="component-box" style="left: 100px; top: 540px; width: 600px; height: 40px;">
            <div class="component-number">612</div>
            <div class="component-label" style="font-size: 14px; color: #555555;">AWS and Azure Cannot Replicate Due to Architectural Incompatibility</div>
        </div>

        <!-- Patent Holder Information -->
        <div style="position: absolute; bottom: 10px; left: 0; width: 100%; text-align: center; font-size: 12px; font-style: italic;">
            Patent Pending Holder: David Nigel Irvin
        </div>

        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Patent Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff; border: 1px dashed #333;"></div>
                <div>Key Innovations</div>
            </div>
        </div>

        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
