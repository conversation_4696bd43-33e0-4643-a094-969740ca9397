/**
 * Regulatory Compliance Validation Schemas
 * 
 * This file defines the validation schemas for regulatory compliance endpoints.
 */

const Joi = require('joi');
const { objectIdPattern } = require('./validationPatterns');

// Get all regulatory frameworks
const getAllFrameworks = {
  query: Joi.object({
    status: Joi.string().valid('active', 'inactive', 'draft', 'superseded', 'archived'),
    region: Joi.string().trim(),
    category: Joi.string().valid('privacy', 'security', 'governance', 'industry', 'general'),
    search: Joi.string().trim(),
    page: Joi.number().integer().min(1),
    limit: Joi.number().integer().min(1).max(100),
    sort: Joi.string().trim()
  })
};

// Get regulatory framework by ID
const getFrameworkById = {
  params: Joi.object({
    id: Joi.string().pattern(objectIdPattern).required()
      .messages({
        'string.pattern.base': 'Framework ID must be a valid ObjectId'
      })
  })
};

// Get regulatory framework by code
const getFrameworkByCode = {
  params: Joi.object({
    code: Joi.string().trim().required()
  })
};

// Get compliance requirements for a framework
const getRequirementsByFramework = {
  params: Joi.object({
    frameworkId: Joi.string().pattern(objectIdPattern).required()
      .messages({
        'string.pattern.base': 'Framework ID must be a valid ObjectId'
      })
  }),
  query: Joi.object({
    status: Joi.string().valid('active', 'inactive', 'draft', 'archived'),
    category: Joi.string().valid(
      'notice', 
      'choice', 
      'access', 
      'security', 
      'retention', 
      'transfer', 
      'accountability', 
      'governance', 
      'rights', 
      'breach', 
      'consent', 
      'other'
    ),
    search: Joi.string().trim(),
    page: Joi.number().integer().min(1),
    limit: Joi.number().integer().min(1).max(100),
    sort: Joi.string().trim()
  })
};

// Get compliance requirement by ID
const getRequirementById = {
  params: Joi.object({
    id: Joi.string().pattern(objectIdPattern).required()
      .messages({
        'string.pattern.base': 'Requirement ID must be a valid ObjectId'
      })
  })
};

// Get compliance status for an entity
const getComplianceStatus = {
  params: Joi.object({
    entityType: Joi.string().valid('organization', 'system', 'process', 'vendor', 'dataset').required(),
    entityId: Joi.string().required(),
    frameworkId: Joi.string().pattern(objectIdPattern).required()
      .messages({
        'string.pattern.base': 'Framework ID must be a valid ObjectId'
      })
  })
};

// Update compliance status for a requirement
const updateRequirementStatus = {
  params: Joi.object({
    statusId: Joi.string().pattern(objectIdPattern).required()
      .messages({
        'string.pattern.base': 'Status ID must be a valid ObjectId'
      }),
    requirementId: Joi.string().pattern(objectIdPattern).required()
      .messages({
        'string.pattern.base': 'Requirement ID must be a valid ObjectId'
      })
  }),
  body: Joi.object({
    status: Joi.string().valid('compliant', 'non-compliant', 'in-progress', 'not-started', 'not-applicable').required(),
    notes: Joi.string().allow('', null),
    evidence: Joi.array().items(
      Joi.object({
        type: Joi.string().valid('document', 'screenshot', 'link', 'text', 'other').required(),
        title: Joi.string().trim().required(),
        description: Joi.string().allow('', null),
        url: Joi.string().uri().allow('', null),
        fileId: Joi.string().allow('', null),
        fileName: Joi.string().allow('', null),
        fileType: Joi.string().allow('', null),
        fileSize: Joi.number().allow(null),
        content: Joi.string().allow('', null),
        metadata: Joi.object().allow(null)
      })
    ),
    assignedTo: Joi.string().pattern(objectIdPattern).allow(null)
      .messages({
        'string.pattern.base': 'Assigned user ID must be a valid ObjectId'
      }),
    dueDate: Joi.date().iso().allow(null),
    completedDate: Joi.date().iso().allow(null)
  })
};

// Generate compliance report
const generateComplianceReport = {
  params: Joi.object({
    entityType: Joi.string().valid('organization', 'system', 'process', 'vendor', 'dataset').required(),
    entityId: Joi.string().required(),
    frameworkId: Joi.string().pattern(objectIdPattern).required()
      .messages({
        'string.pattern.base': 'Framework ID must be a valid ObjectId'
      })
  })
};

// Map requirements between frameworks
const mapRequirementsBetweenFrameworks = {
  params: Joi.object({
    sourceFrameworkId: Joi.string().pattern(objectIdPattern).required()
      .messages({
        'string.pattern.base': 'Source framework ID must be a valid ObjectId'
      }),
    targetFrameworkId: Joi.string().pattern(objectIdPattern).required()
      .messages({
        'string.pattern.base': 'Target framework ID must be a valid ObjectId'
      })
  })
};

// Get regulatory updates
const getRegulatoryUpdates = {
  query: Joi.object({
    region: Joi.string().trim(),
    framework: Joi.string().pattern(objectIdPattern)
      .messages({
        'string.pattern.base': 'Framework ID must be a valid ObjectId'
      }),
    since: Joi.date().iso(),
    page: Joi.number().integer().min(1),
    limit: Joi.number().integer().min(1).max(100)
  })
};

module.exports = {
  getAllFrameworks,
  getFrameworkById,
  getFrameworkByCode,
  getRequirementsByFramework,
  getRequirementById,
  getComplianceStatus,
  updateRequirementStatus,
  generateComplianceReport,
  mapRequirementsBetweenFrameworks,
  getRegulatoryUpdates
};
