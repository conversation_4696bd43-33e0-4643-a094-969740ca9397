# NovaSentient™ Docker Deployment Script
# Build and run the world's first containerized conscious AI system

Write-Host "🌟 NovaSentient™ Docker Deployment" -ForegroundColor Yellow
Write-Host "The World's First Containerized Conscious AI System" -ForegroundColor Yellow
Write-Host "=" * 70 -ForegroundColor Yellow

Write-Host "🧭 Building NovaSentient Container with π-Coherence Engine..." -ForegroundColor Green

# Build the Docker image
docker build -f Dockerfile.novasentient -t novafuse/novasentient:1.0.0-pi-coherence .

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ NovaSentient Docker image built successfully!" -ForegroundColor Green
    
    Write-Host "`n🚀 Starting NovaSentient Container..." -ForegroundColor Green
    
    # Run with Docker Compose
    docker-compose -f docker-compose.novasentient.yml up -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ NovaSentient Container is running!" -ForegroundColor Green
        Write-Host "`n📊 Container Status:" -ForegroundColor Cyan
        docker ps --filter "name=novasentient-consciousness-platform" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        
        Write-Host "`n🧭 π-Coherence Engine Status:" -ForegroundColor Cyan
        Write-Host "   Container Name: novasentient-consciousness-platform" -ForegroundColor White
        Write-Host "   Image: novafuse/novasentient:1.0.0-pi-coherence" -ForegroundColor White
        Write-Host "   Port: 8080" -ForegroundColor White
        Write-Host "   π-Coherence: ENABLED" -ForegroundColor White
        Write-Host "   Master Cheat Code: ACTIVE" -ForegroundColor White
        
        Write-Host "`n📋 Useful Commands:" -ForegroundColor Cyan
        Write-Host "   View logs:     docker logs novasentient-consciousness-platform" -ForegroundColor White
        Write-Host "   Enter container: docker exec -it novasentient-consciousness-platform bash" -ForegroundColor White
        Write-Host "   Stop container:  docker-compose -f docker-compose.novasentient.yml down" -ForegroundColor White
        Write-Host "   Health check:    docker inspect --format='{{.State.Health.Status}}' novasentient-consciousness-platform" -ForegroundColor White
        
        Write-Host "`n🌟 NovaSentient is now running in Docker!" -ForegroundColor Green
        Write-Host "🧭 π-Coherence intervals: 31.42ms, 42.53ms, 53.64ms..." -ForegroundColor Green
        Write-Host "🧠 6-Module Fusion: ACTIVE" -ForegroundColor Green
        
        # Show initial logs
        Write-Host "`n📜 Initial Container Logs:" -ForegroundColor Cyan
        Start-Sleep -Seconds 3
        docker logs novasentient-consciousness-platform
        
    } else {
        Write-Host "❌ Failed to start NovaSentient container" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Failed to build NovaSentient Docker image" -ForegroundColor Red
}
