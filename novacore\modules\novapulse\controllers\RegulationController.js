/**
 * NovaCore Regulation Controller
 * 
 * This controller handles API requests related to regulations.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const { RegulationService } = require('../services');
const logger = require('../../../config/logger');

class RegulationController {
  /**
   * Create a new regulation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createRegulation(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const regulation = await RegulationService.createRegulation(req.body, userId);
      
      res.status(201).json({
        success: true,
        data: regulation
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get all regulations
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllRegulations(req, res, next) {
    try {
      // Extract filter criteria from query params
      const filter = {};
      
      if (req.query.name) filter.name = req.query.name;
      if (req.query.shortName) filter.shortName = req.query.shortName;
      if (req.query.type) filter.type = req.query.type;
      if (req.query.category) filter.category = req.query.category;
      if (req.query.status) filter.status = req.query.status;
      if (req.query.country) filter.country = req.query.country;
      if (req.query.region) filter.region = req.query.region;
      if (req.query.industry) filter.industry = req.query.industry;
      if (req.query.organizationType) filter.organizationType = req.query.organizationType;
      if (req.query.dataType) filter.dataType = req.query.dataType;
      if (req.query.search) filter.search = req.query.search;
      
      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };
      
      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }
      
      const result = await RegulationService.getAllRegulations(filter, options);
      
      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get regulation by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getRegulationById(req, res, next) {
    try {
      const regulation = await RegulationService.getRegulationById(req.params.id);
      
      res.status(200).json({
        success: true,
        data: regulation
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update regulation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateRegulation(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const regulation = await RegulationService.updateRegulation(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: regulation
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Delete regulation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async deleteRegulation(req, res, next) {
    try {
      await RegulationService.deleteRegulation(req.params.id);
      
      res.status(200).json({
        success: true,
        message: 'Regulation deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Add requirement to regulation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async addRequirement(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const regulation = await RegulationService.addRequirement(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: regulation
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update requirement
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateRequirement(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const regulation = await RegulationService.updateRequirement(
        req.params.id, 
        req.params.requirementId, 
        req.body, 
        userId
      );
      
      res.status(200).json({
        success: true,
        data: regulation
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Remove requirement
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async removeRequirement(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const regulation = await RegulationService.removeRequirement(
        req.params.id, 
        req.params.requirementId, 
        userId
      );
      
      res.status(200).json({
        success: true,
        data: regulation
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Add version to regulation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async addVersion(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const regulation = await RegulationService.addVersion(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: regulation
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update version
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateVersion(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const regulation = await RegulationService.updateVersion(
        req.params.id, 
        req.params.versionNumber, 
        req.body, 
        userId
      );
      
      res.status(200).json({
        success: true,
        data: regulation
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find regulations by jurisdiction
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByJurisdiction(req, res, next) {
    try {
      const { country, region } = req.query;
      const regulations = await RegulationService.findByJurisdiction(country, region);
      
      res.status(200).json({
        success: true,
        data: regulations
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find regulations by category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByCategory(req, res, next) {
    try {
      const regulations = await RegulationService.findByCategory(req.params.category);
      
      res.status(200).json({
        success: true,
        data: regulations
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find regulations by applicability
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByApplicability(req, res, next) {
    try {
      const criteria = {
        industry: req.query.industry,
        region: req.query.region,
        organizationType: req.query.organizationType,
        dataTypes: req.query.dataTypes ? req.query.dataTypes.split(',') : undefined
      };
      
      const regulations = await RegulationService.findByApplicability(criteria);
      
      res.status(200).json({
        success: true,
        data: regulations
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find regulations by requirement ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByRequirementId(req, res, next) {
    try {
      const regulations = await RegulationService.findByRequirementId(req.params.requirementId);
      
      res.status(200).json({
        success: true,
        data: regulations
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find regulations by control mapping
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByControlMapping(req, res, next) {
    try {
      const { frameworkId, controlId } = req.params;
      const regulations = await RegulationService.findByControlMapping(frameworkId, controlId);
      
      res.status(200).json({
        success: true,
        data: regulations
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new RegulationController();
