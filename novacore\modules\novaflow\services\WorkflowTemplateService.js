/**
 * NovaCore Workflow Template Service
 * 
 * This service provides functionality for managing workflow templates.
 * NovaFlow is the Universal Compliance Workflow Orchestrator (UCWO) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const { WorkflowTemplate } = require('../models');
const logger = require('../../../config/logger');
const { ValidationError, NotFoundError } = require('../../../api/utils/errors');
const { v4: uuidv4 } = require('uuid');

class WorkflowTemplateService {
  /**
   * Create a new workflow template
   * @param {Object} data - Template data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created template
   */
  async createTemplate(data, userId) {
    try {
      logger.info('Creating new workflow template', { organizationId: data.organizationId });
      
      // Generate IDs for stages and tasks if not provided
      if (data.stages) {
        for (const stage of data.stages) {
          if (!stage.id) {
            stage.id = `stage-${uuidv4().substring(0, 8)}`;
          }
          
          if (stage.tasks) {
            for (const task of stage.tasks) {
              if (!task.id) {
                task.id = `task-${uuidv4().substring(0, 8)}`;
              }
            }
          }
        }
      }
      
      // Set created by
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Create template
      const template = new WorkflowTemplate(data);
      await template.save();
      
      logger.info('Workflow template created successfully', { id: template._id });
      
      return template;
    } catch (error) {
      logger.error('Error creating workflow template', { error });
      throw error;
    }
  }
  
  /**
   * Get all workflow templates for an organization
   * @param {string} organizationId - Organization ID
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Templates with pagination info
   */
  async getAllTemplates(organizationId, filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
      
      // Build query
      const query = { organizationId };
      
      // Apply filters
      if (filter.type) {
        query.type = filter.type;
      }
      
      if (filter.category) {
        query.category = filter.category;
      }
      
      if (filter.status) {
        query.status = filter.status;
      }
      
      if (filter.tags) {
        query.tags = { $all: Array.isArray(filter.tags) ? filter.tags : [filter.tags] };
      }
      
      if (filter.frameworks) {
        query.frameworks = { $all: Array.isArray(filter.frameworks) ? filter.frameworks : [filter.frameworks] };
      }
      
      if (filter.isDefault) {
        query.isDefault = filter.isDefault === 'true';
      }
      
      if (filter.search) {
        query.$or = [
          { name: { $regex: filter.search, $options: 'i' } },
          { description: { $regex: filter.search, $options: 'i' } }
        ];
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [templates, total] = await Promise.all([
        WorkflowTemplate.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        WorkflowTemplate.countDocuments(query)
      ]);
      
      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;
      
      return {
        data: templates,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting workflow templates', { error });
      throw error;
    }
  }
  
  /**
   * Get workflow template by ID
   * @param {string} id - Template ID
   * @returns {Promise<Object>} - Template
   */
  async getTemplateById(id) {
    try {
      const template = await WorkflowTemplate.findById(id);
      
      if (!template) {
        throw new NotFoundError(`Workflow template with ID ${id} not found`);
      }
      
      return template;
    } catch (error) {
      logger.error('Error getting workflow template by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Update workflow template
   * @param {string} id - Template ID
   * @param {Object} data - Updated template data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated template
   */
  async updateTemplate(id, data, userId) {
    try {
      // Get existing template
      const template = await this.getTemplateById(id);
      
      // Validate status transition
      if (data.status && data.status !== template.status) {
        this._validateStatusTransition(template.status, data.status);
      }
      
      // Set updated by
      data.updatedBy = userId;
      
      // Update template
      Object.assign(template, data);
      await template.save();
      
      logger.info('Workflow template updated successfully', { id });
      
      return template;
    } catch (error) {
      logger.error('Error updating workflow template', { id, error });
      throw error;
    }
  }
  
  /**
   * Delete workflow template
   * @param {string} id - Template ID
   * @returns {Promise<boolean>} - Deletion success
   */
  async deleteTemplate(id) {
    try {
      const result = await WorkflowTemplate.findByIdAndDelete(id);
      
      if (!result) {
        throw new NotFoundError(`Workflow template with ID ${id} not found`);
      }
      
      logger.info('Workflow template deleted successfully', { id });
      
      return true;
    } catch (error) {
      logger.error('Error deleting workflow template', { id, error });
      throw error;
    }
  }
  
  /**
   * Set template as default
   * @param {string} id - Template ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated template
   */
  async setAsDefault(id, userId) {
    try {
      // Get template
      const template = await this.getTemplateById(id);
      
      // Validate template status
      if (template.status !== 'active') {
        throw new ValidationError(`Cannot set template with status ${template.status} as default`);
      }
      
      // Get organization ID and type
      const organizationId = template.organizationId;
      const type = template.type;
      
      // Clear default flag from other templates of the same type
      await WorkflowTemplate.updateMany(
        { 
          organizationId, 
          type, 
          isDefault: true 
        },
        { 
          isDefault: false,
          updatedBy: userId
        }
      );
      
      // Set this template as default
      template.isDefault = true;
      template.updatedBy = userId;
      await template.save();
      
      logger.info('Workflow template set as default successfully', { id });
      
      return template;
    } catch (error) {
      logger.error('Error setting workflow template as default', { id, error });
      throw error;
    }
  }
  
  /**
   * Create new version of template
   * @param {string} id - Template ID
   * @param {Object} data - New version data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - New template version
   */
  async createNewVersion(id, data, userId) {
    try {
      // Get existing template
      const template = await this.getTemplateById(id);
      
      // Create new template data
      const newTemplateData = {
        ...JSON.parse(JSON.stringify(template)),
        _id: undefined,
        name: data.name || `${template.name} (v${data.version || 'new'})`,
        description: data.description || template.description,
        version: data.version || this._incrementVersion(template.version),
        previousVersion: template._id,
        status: 'draft',
        isDefault: false,
        createdBy: userId,
        updatedBy: userId
      };
      
      // Apply any other updates
      if (data.stages) {
        newTemplateData.stages = data.stages;
      }
      
      if (data.dataModel) {
        newTemplateData.dataModel = data.dataModel;
      }
      
      if (data.variables) {
        newTemplateData.variables = data.variables;
      }
      
      if (data.integrationRequirements) {
        newTemplateData.integrationRequirements = data.integrationRequirements;
      }
      
      if (data.frameworks) {
        newTemplateData.frameworks = data.frameworks;
      }
      
      if (data.tags) {
        newTemplateData.tags = data.tags;
      }
      
      // Create new template
      const newTemplate = new WorkflowTemplate(newTemplateData);
      await newTemplate.save();
      
      logger.info('New workflow template version created successfully', { 
        originalId: id, 
        newId: newTemplate._id 
      });
      
      return newTemplate;
    } catch (error) {
      logger.error('Error creating new workflow template version', { id, error });
      throw error;
    }
  }
  
  /**
   * Get default template
   * @param {string} organizationId - Organization ID
   * @param {string} type - Template type
   * @returns {Promise<Object>} - Default template
   */
  async getDefaultTemplate(organizationId, type = 'compliance') {
    try {
      const template = await WorkflowTemplate.findDefault(organizationId, type);
      
      if (!template) {
        throw new NotFoundError(`No default workflow template found for type ${type}`);
      }
      
      return template;
    } catch (error) {
      logger.error('Error getting default workflow template', { organizationId, type, error });
      throw error;
    }
  }
  
  /**
   * Import template from JSON
   * @param {Object} data - Template data
   * @param {string} organizationId - Organization ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Imported template
   */
  async importTemplate(data, organizationId, userId) {
    try {
      logger.info('Importing workflow template', { organizationId });
      
      // Validate template data
      if (!data.name || !data.stages) {
        throw new ValidationError('Invalid template data: name and stages are required');
      }
      
      // Set organization ID and user IDs
      data.organizationId = organizationId;
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Generate IDs for stages and tasks if not provided
      for (const stage of data.stages) {
        if (!stage.id) {
          stage.id = `stage-${uuidv4().substring(0, 8)}`;
        }
        
        if (stage.tasks) {
          for (const task of stage.tasks) {
            if (!task.id) {
              task.id = `task-${uuidv4().substring(0, 8)}`;
            }
          }
        }
      }
      
      // Create template
      const template = new WorkflowTemplate(data);
      await template.save();
      
      logger.info('Workflow template imported successfully', { id: template._id });
      
      return template;
    } catch (error) {
      logger.error('Error importing workflow template', { error });
      throw error;
    }
  }
  
  /**
   * Validate template status transition
   * @param {string} currentStatus - Current status
   * @param {string} newStatus - New status
   * @private
   */
  _validateStatusTransition(currentStatus, newStatus) {
    const validTransitions = {
      'draft': ['active', 'archived'],
      'active': ['inactive', 'archived'],
      'inactive': ['active', 'archived'],
      'archived': []
    };
    
    if (!validTransitions[currentStatus].includes(newStatus)) {
      throw new ValidationError(`Invalid status transition from ${currentStatus} to ${newStatus}`);
    }
  }
  
  /**
   * Increment version string
   * @param {string} version - Current version
   * @returns {string} - Incremented version
   * @private
   */
  _incrementVersion(version) {
    if (!version) {
      return '1.0';
    }
    
    const parts = version.split('.');
    
    if (parts.length === 1) {
      return `${parseInt(parts[0], 10) + 1}.0`;
    }
    
    const minor = parseInt(parts[parts.length - 1], 10) + 1;
    parts[parts.length - 1] = minor.toString();
    
    return parts.join('.');
  }
}

module.exports = new WorkflowTemplateService();
