/**
 * tensor-runtime.js
 * 
 * This file defines the TensorRuntime class, which manages tensor operations and provides
 * a high-level interface for working with tensors in the NovaCore system.
 */

const { createTensor, transformTensor, processTensor } = require('./tensor-operations');
const Tensor = require('../models/Tensor');
const EventEmitter = require('events');

/**
 * TensorRuntime class for managing tensor operations
 * @extends EventEmitter
 */
class TensorRuntime extends EventEmitter {
  /**
   * Create a new TensorRuntime instance
   * @param {Object} options - Runtime options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      maxConcurrentOperations: options.maxConcurrentOperations || 10,
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      cacheResults: options.cacheResults !== undefined ? options.cacheResults : true,
      ...options
    };
    
    this.tensors = new Map(); // Store tensors by ID
    this.operationQueue = []; // Queue of pending operations
    this.activeOperations = 0; // Number of currently active operations
    this.resultCache = new Map(); // Cache of operation results
    
    this.log('TensorRuntime initialized with options:', this.options);
  }
  
  /**
   * Log a message if logging is enabled
   * @param {...any} args - Arguments to log
   */
  log(...args) {
    if (this.options.enableLogging) {
      console.log(`[TensorRuntime ${new Date().toISOString()}]`, ...args);
    }
  }
  
  /**
   * Create a new tensor
   * @param {Object} data - The data to store in the tensor
   * @param {Array} dimensions - Array of dimension objects with name and size properties
   * @param {Object} options - Additional options for the tensor
   * @returns {Promise<Tensor>} - A promise that resolves to the created tensor
   */
  async create(data, dimensions, options = {}) {
    this.log('Creating tensor with dimensions:', dimensions);
    
    try {
      const tensor = createTensor(data, dimensions, options);
      this.tensors.set(tensor.id, tensor);
      
      this.emit('tensorCreated', { tensorId: tensor.id });
      return tensor;
    } catch (error) {
      this.log('Error creating tensor:', error);
      throw error;
    }
  }
  
  /**
   * Transform a tensor
   * @param {Tensor|String} tensor - The tensor or tensor ID to transform
   * @param {Object} transformation - The transformation to apply
   * @returns {Promise<Tensor>} - A promise that resolves to the transformed tensor
   */
  async transform(tensor, transformation) {
    const tensorObj = this._resolveTensor(tensor);
    this.log(`Transforming tensor ${tensorObj.id} with transformation type: ${transformation.type}`);
    
    // Check cache if enabled
    const cacheKey = this._getCacheKey('transform', tensorObj.id, transformation);
    if (this.options.cacheResults && this.resultCache.has(cacheKey)) {
      this.log(`Using cached result for transformation of tensor ${tensorObj.id}`);
      return this.resultCache.get(cacheKey);
    }
    
    // Queue the operation
    return this._queueOperation(async () => {
      try {
        const transformedTensor = transformTensor(tensorObj, transformation);
        this.tensors.set(transformedTensor.id, transformedTensor);
        
        // Cache the result if enabled
        if (this.options.cacheResults) {
          this.resultCache.set(cacheKey, transformedTensor);
        }
        
        this.emit('tensorTransformed', { 
          originalTensorId: tensorObj.id,
          newTensorId: transformedTensor.id,
          transformationType: transformation.type
        });
        
        return transformedTensor;
      } catch (error) {
        this.log(`Error transforming tensor ${tensorObj.id}:`, error);
        throw error;
      }
    });
  }
  
  /**
   * Process a tensor
   * @param {Tensor|String} tensor - The tensor or tensor ID to process
   * @param {Object} options - Processing options
   * @returns {Promise<Tensor>} - A promise that resolves to the processed tensor
   */
  async process(tensor, options = {}) {
    const tensorObj = this._resolveTensor(tensor);
    this.log(`Processing tensor ${tensorObj.id}`);
    
    // Check cache if enabled
    const cacheKey = this._getCacheKey('process', tensorObj.id, options);
    if (this.options.cacheResults && this.resultCache.has(cacheKey)) {
      this.log(`Using cached result for processing of tensor ${tensorObj.id}`);
      return this.resultCache.get(cacheKey);
    }
    
    // Queue the operation
    return this._queueOperation(async () => {
      try {
        const processedTensor = processTensor(tensorObj, options);
        this.tensors.set(processedTensor.id, processedTensor);
        
        // Cache the result if enabled
        if (this.options.cacheResults) {
          this.resultCache.set(cacheKey, processedTensor);
        }
        
        this.emit('tensorProcessed', { 
          originalTensorId: tensorObj.id,
          newTensorId: processedTensor.id
        });
        
        return processedTensor;
      } catch (error) {
        this.log(`Error processing tensor ${tensorObj.id}:`, error);
        throw error;
      }
    });
  }
  
  /**
   * Get a tensor by ID
   * @param {String} id - The tensor ID
   * @returns {Tensor} - The tensor with the specified ID
   */
  getTensor(id) {
    if (!this.tensors.has(id)) {
      throw new Error(`Tensor with ID ${id} not found`);
    }
    return this.tensors.get(id);
  }
  
  /**
   * Clear the result cache
   */
  clearCache() {
    this.log('Clearing result cache');
    this.resultCache.clear();
  }
  
  /**
   * Resolve a tensor object from a tensor or tensor ID
   * @param {Tensor|String} tensor - The tensor or tensor ID
   * @returns {Tensor} - The resolved tensor object
   * @private
   */
  _resolveTensor(tensor) {
    if (tensor instanceof Tensor) {
      return tensor;
    }
    
    if (typeof tensor === 'string') {
      return this.getTensor(tensor);
    }
    
    throw new Error('Invalid tensor: must be a Tensor instance or a tensor ID');
  }
  
  /**
   * Queue an operation for execution
   * @param {Function} operation - The operation function to queue
   * @returns {Promise<any>} - A promise that resolves to the operation result
   * @private
   */
  async _queueOperation(operation) {
    return new Promise((resolve, reject) => {
      const queuedOperation = {
        operation,
        resolve,
        reject
      };
      
      this.operationQueue.push(queuedOperation);
      this._processQueue();
    });
  }
  
  /**
   * Process the operation queue
   * @private
   */
  _processQueue() {
    if (this.operationQueue.length === 0 || this.activeOperations >= this.options.maxConcurrentOperations) {
      return;
    }
    
    const { operation, resolve, reject } = this.operationQueue.shift();
    this.activeOperations++;
    
    Promise.resolve()
      .then(operation)
      .then(result => {
        resolve(result);
        this.activeOperations--;
        this._processQueue();
      })
      .catch(error => {
        reject(error);
        this.activeOperations--;
        this._processQueue();
      });
  }
  
  /**
   * Generate a cache key for an operation
   * @param {String} operationType - The type of operation
   * @param {String} tensorId - The ID of the tensor
   * @param {Object} params - The operation parameters
   * @returns {String} - The cache key
   * @private
   */
  _getCacheKey(operationType, tensorId, params) {
    return `${operationType}:${tensorId}:${JSON.stringify(params)}`;
  }
}

// Export the TensorRuntime class and tensor operations
module.exports = {
  TensorRuntime,
  createTensor,
  transformTensor,
  processTensor
};
