# NovaFuse Migration Verification Script
# This script verifies that the migration was successful by checking for expected files and directories.

# Load configuration
$configPath = Join-Path -Path $PSScriptRoot -ChildPath "migration-config.json"
if (-not (Test-Path $configPath)) {
    Write-Host "Configuration file not found: $configPath" -ForegroundColor Red
    exit 1
}

$config = Get-Content -Path $configPath -Raw | ConvertFrom-Json
$sourceRoot = $config.sourceRoot
$destinationRoot = $config.destinationRoot
$logFile = Join-Path -Path $PSScriptRoot -ChildPath "verification-log.txt"

# Create log file if it doesn't exist
if (-not (Test-Path $logFile)) {
    New-Item -Path $logFile -ItemType File -Force | Out-Null
}

# Function to log messages
function Log-Message {
    param (
        [string]$message,
        [string]$type = "INFO"
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$type] $message"

    # Write to console
    if ($type -eq "ERROR") {
        Write-Host $logMessage -ForegroundColor Red
    }
    elseif ($type -eq "WARNING") {
        Write-Host $logMessage -ForegroundColor Yellow
    }
    elseif ($type -eq "SUCCESS") {
        Write-Host $logMessage -ForegroundColor Green
    }
    else {
        Write-Host $logMessage
    }

    # Write to log file
    Add-Content -Path $logFile -Value $logMessage
}

# Function to verify files were migrated
function Verify-Migration {
    param (
        [string]$sourcePath,
        [string]$destinationPath,
        [string]$filter = "*.*"
    )

    # Check if source path exists
    if (-not (Test-Path $sourcePath)) {
        Log-Message "Source path does not exist: $sourcePath" "ERROR"
        return @{
            Success = $false
            SourceFiles = 0
            DestinationFiles = 0
            MissingFiles = @()
        }
    }

    # Check if destination path exists
    if (-not (Test-Path $destinationPath)) {
        Log-Message "Destination path does not exist: $destinationPath" "ERROR"
        return @{
            Success = $false
            SourceFiles = 0
            DestinationFiles = 0
            MissingFiles = @()
        }
    }

    # Get all files in the source directory and subdirectories
    $sourceFiles = Get-ChildItem -Path $sourcePath -Filter $filter -Recurse -File

    if ($sourceFiles.Count -eq 0) {
        Log-Message "No files found in source path: $sourcePath" "WARNING"
        return @{
            Success = $true
            SourceFiles = 0
            DestinationFiles = 0
            MissingFiles = @()
        }
    }

    # Get all files in the destination directory and subdirectories
    $destinationFiles = Get-ChildItem -Path $destinationPath -Filter $filter -Recurse -File

    # Check if all source files were migrated
    $missingFiles = @()

    foreach ($sourceFile in $sourceFiles) {
        # Get the relative path of the file
        $relativePath = $sourceFile.FullName.Substring($sourcePath.Length)

        # Create the destination path
        $destinationFilePath = Join-Path -Path $destinationPath -ChildPath $relativePath

        # Check if the file exists in the destination
        if (-not (Test-Path $destinationFilePath)) {
            $missingFiles += $relativePath
        }
    }

    $success = $missingFiles.Count -eq 0

    return @{
        Success = $success
        SourceFiles = $sourceFiles.Count
        DestinationFiles = $destinationFiles.Count
        MissingFiles = $missingFiles
    }
}

# Main verification process
Log-Message "Starting NovaFuse migration verification..." "INFO"
Log-Message "Source root: $sourceRoot" "INFO"
Log-Message "Destination root: $destinationRoot" "INFO"

# Process each migration mapping
$successCount = 0
$failureCount = 0
$totalSourceFiles = 0
$totalDestinationFiles = 0
$allMissingFiles = @()

foreach ($mapping in $config.migrationMappings) {
    $sourcePath = Join-Path -Path $sourceRoot -ChildPath $mapping.sourcePath
    $destinationPath = Join-Path -Path $destinationRoot -ChildPath $mapping.destinationPath

    Log-Message "Verifying migration: $($mapping.name)" "INFO"
    Log-Message "Source path: $sourcePath" "INFO"
    Log-Message "Destination path: $destinationPath" "INFO"

    # Verify migration
    $result = Verify-Migration -sourcePath $sourcePath -destinationPath $destinationPath -filter $mapping.filter

    $totalSourceFiles += $result.SourceFiles
    $totalDestinationFiles += $result.DestinationFiles

    if ($result.Success) {
        Log-Message "Migration successful: $($mapping.name)" "SUCCESS"
        Log-Message "Source files: $($result.SourceFiles)" "INFO"
        Log-Message "Destination files: $($result.DestinationFiles)" "INFO"
        $successCount++
    }
    else {
        Log-Message "Migration failed: $($mapping.name)" "ERROR"
        Log-Message "Source files: $($result.SourceFiles)" "INFO"
        Log-Message "Destination files: $($result.DestinationFiles)" "INFO"
        Log-Message "Missing files: $($result.MissingFiles.Count)" "ERROR"

        foreach ($missingFile in $result.MissingFiles) {
            Log-Message "Missing file: $missingFile" "ERROR"
            $allMissingFiles += "$($mapping.name): $missingFile"
        }

        $failureCount++
    }
}

# Summary
Log-Message "Verification summary:" "INFO"
Log-Message "Successful migrations: $successCount" "SUCCESS"
if ($failureCount -gt 0) {
    Log-Message "Failed migrations: $failureCount" "ERROR"
} else {
    Log-Message "Failed migrations: $failureCount" "INFO"
}
Log-Message "Total source files: $totalSourceFiles" "INFO"
Log-Message "Total destination files: $totalDestinationFiles" "INFO"
if ($allMissingFiles.Count -gt 0) {
    Log-Message "Total missing files: $($allMissingFiles.Count)" "ERROR"
} else {
    Log-Message "Total missing files: $($allMissingFiles.Count)" "INFO"
}

if ($allMissingFiles.Count -gt 0) {
    Log-Message "Missing files:" "ERROR"
    foreach ($missingFile in $allMissingFiles) {
        Log-Message "  $missingFile" "ERROR"
    }
}

Log-Message "NovaFuse migration verification completed!" "SUCCESS"
