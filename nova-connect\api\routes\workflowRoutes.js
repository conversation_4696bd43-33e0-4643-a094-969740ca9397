/**
 * Workflow Routes
 */

const express = require('express');
const router = express.Router();
const WorkflowController = require('../controllers/WorkflowController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');
const { hasFeatureAccess, hasNotReachedFeatureLimit, trackFeatureUsage } = require('../middleware/featureAccessMiddleware');

// All routes require authentication
router.use(authenticate);

// Workflow routes
router.get('/', hasPermission('system:audit'), (req, res, next) => {
  WorkflowController.getAllWorkflows(req, res, next);
});

router.get('/my', hasFeatureAccess('workflow.basic'), (req, res, next) => {
  WorkflowController.getMyWorkflows(req, res, next);
});

router.get('/:id', hasFeatureAccess('workflow.basic'), (req, res, next) => {
  WorkflowController.getWorkflowById(req, res, next);
});

router.post('/',
  hasFeatureAccess('workflow.basic'),
  hasNotReachedFeatureLimit('workflow.basic', 'workflows'),
  trackFeatureUsage('workflow.basic'),
  (req, res, next) => {
    WorkflowController.createWorkflow(req, res, next);
  }
);

router.put('/:id', hasFeatureAccess('workflow.basic'), (req, res, next) => {
  WorkflowController.updateWorkflow(req, res, next);
});

router.delete('/:id', hasFeatureAccess('workflow.basic'), (req, res, next) => {
  WorkflowController.deleteWorkflow(req, res, next);
});

router.post('/:id/enable', hasFeatureAccess('workflow.basic'), (req, res, next) => {
  WorkflowController.enableWorkflow(req, res, next);
});

router.post('/:id/disable', hasFeatureAccess('workflow.basic'), (req, res, next) => {
  WorkflowController.disableWorkflow(req, res, next);
});

router.post('/:id/execute',
  hasFeatureAccess('workflow.basic'),
  trackFeatureUsage('workflow.basic'),
  (req, res, next) => {
    WorkflowController.executeWorkflow(req, res, next);
  }
);

// Workflow run routes
router.get('/runs', hasPermission('system:audit'), (req, res, next) => {
  WorkflowController.getAllWorkflowRuns(req, res, next);
});

router.get('/:id/runs', hasFeatureAccess('workflow.basic'), (req, res, next) => {
  WorkflowController.getWorkflowRunsForWorkflow(req, res, next);
});

router.get('/runs/:id', hasFeatureAccess('workflow.basic'), (req, res, next) => {
  WorkflowController.getWorkflowRunById(req, res, next);
});

// Scheduled workflow routes
router.post('/scheduled/process',
  hasPermission('system:settings'),
  hasFeatureAccess('workflow.scheduled'),
  (req, res, next) => {
    WorkflowController.processScheduledWorkflows(req, res, next);
  }
);

// Event workflow routes
router.post('/events/:eventType',
  hasFeatureAccess('workflow.event_triggered'),
  (req, res, next) => {
    WorkflowController.triggerEventWorkflows(req, res, next);
  }
);

module.exports = router;
