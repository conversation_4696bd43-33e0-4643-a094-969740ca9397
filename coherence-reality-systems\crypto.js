const { randomBytes } = require('crypto');
const { keccak256 } = require('ethereum-cryptography/keccak');
const { toHex, utf8ToBytes } = require('ethereum-cryptography/utils');
const secp256k1 = require('secp256k1');

/**
 * Generate a new private key
 * @returns {string} - Hex-encoded private key
 */
function generatePrivateKey() {
  let privateKey;
  do {
    privateKey = randomBytes(32);
  } while (!secp256k1.privateKeyVerify(privateKey));
  
  return toHex(privateKey);
}

/**
 * Get public key from private key
 * @param {string} privateKey - Hex-encoded private key
 * @returns {string} - Hex-encoded public key
 */
function getPublicKey(privateKey) {
  const privateKeyBuffer = Buffer.from(privateKey.replace('0x', ''), 'hex');
  const publicKey = secp256k1.publicKeyCreate(privateKeyBuffer, false).slice(1);
  return '0x' + toHex(publicKey);
}

/**
 * Get address from public key
 * @param {string} publicKey - Hex-encoded public key (uncompressed, without 0x04 prefix)
 * @returns {string} - Hex-encoded address
 */
function getAddress(publicKey) {
  const publicKeyBytes = Buffer.from(publicKey.replace('0x', ''), 'hex');
  const hash = keccak256(publicKeyBytes);
  return '0x' + toHex(hash.slice(-20));
}

/**
 * Sign a message with a private key
 * @param {string} message - Message to sign
 * @param {string} privateKey - Hex-encoded private key
 * @returns {string} - Hex-encoded signature
 */
function sign(message, privateKey) {
  const privateKeyBuffer = Buffer.from(privateKey.replace('0x', ''), 'hex');
  const messageHash = typeof message === 'string' 
    ? keccak256(utf8ToBytes(message))
    : message;
  
  const { signature, recid } = secp256k1.ecdsaSign(
    messageHash,
    privateKeyBuffer
  );
  
  // Add recovery id to create a 65-byte signature
  const signatureWithRecovery = new Uint8Array(65);
  signatureWithRecovery.set(signature);
  signatureWithRecovery[64] = recid;
  
  return '0x' + toHex(signatureWithRecovery);
}

/**
 * Recover public key from signature
 * @param {string} message - Original message
 * @param {string} signature - Hex-encoded signature
 * @returns {string} - Hex-encoded public key
 */
function recoverPublicKey(message, signature) {
  const signatureBuffer = Buffer.from(signature.replace('0x', ''), 'hex');
  const signatureBytes = signatureBuffer.slice(0, 64);
  const recoveryId = signatureBuffer[64];
  
  const messageHash = typeof message === 'string' 
    ? keccak256(utf8ToBytes(message))
    : message;
  
  const publicKey = secp256k1.ecdsaRecover(
    signatureBytes,
    recoveryId,
    messageHash,
    false
  );
  
  return '0x' + toHex(publicKey);
}

/**
 * Recover address from signature
 * @param {string} message - Original message
 * @param {string} signature - Hex-encoded signature
 * @returns {string} - Hex-encoded address
 */
function recoverAddress(message, signature) {
  const publicKey = recoverPublicKey(message, signature);
  return getAddress(publicKey);
}

/**
 * Verify a signature
 * @param {string} message - Original message
 * @param {string} signature - Hex-encoded signature
 * @param {string} address - Expected address
 * @returns {boolean} - True if signature is valid
 */
function verify(message, signature, address) {
  try {
    const recoveredAddress = recoverAddress(message, signature);
    return recoveredAddress.toLowerCase() === address.toLowerCase();
  } catch (error) {
    return false;
  }
}

module.exports = {
  generatePrivateKey,
  getPublicKey,
  getAddress,
  sign,
  recoverPublicKey,
  recoverAddress,
  verify
};
