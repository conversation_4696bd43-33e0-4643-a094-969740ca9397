/**
 * Compliance Navigation Component
 * 
 * This component provides navigation links for the compliance section of the application.
 */

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';

export const ComplianceNavigation: React.FC = () => {
  const router = useRouter();
  
  const navItems = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Frameworks', href: '/frameworks' },
    { label: 'Regulations', href: '/regulations' },
    { label: 'Regulatory Changes', href: '/regulatory-changes' },
    { label: 'Compliance Profiles', href: '/compliance-profiles' },
  ];
  
  return (
    <div className="mb-6">
      <nav className="flex flex-wrap gap-2">
        {navItems.map((item) => {
          const isActive = router.pathname === item.href || 
                          (item.href !== '/dashboard' && router.pathname.startsWith(item.href));
          
          return (
            <Link 
              key={item.href} 
              href={item.href}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                isActive 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {item.label}
            </Link>
          );
        })}
      </nav>
    </div>
  );
};

export default ComplianceNavigation;
