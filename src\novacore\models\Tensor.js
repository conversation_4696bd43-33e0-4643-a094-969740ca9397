/**
 * Tensor.js
 * 
 * This file defines the Tensor class, which is the core data structure for the NovaCore tensor-based runtime.
 * Tensors are multi-dimensional arrays that can represent complex data relationships across multiple domains
 * (compliance, security, risk, etc.).
 */

const crypto = require('crypto');

/**
 * Tensor class representing a multi-dimensional data structure
 */
class Tensor {
  /**
   * Create a new Tensor
   * @param {Object} data - The data to store in the tensor
   * @param {Array} dimensions - Array of dimension objects with name and size properties
   * @param {Object} options - Additional options for the tensor
   */
  constructor(data, dimensions, options = {}) {
    this.id = options.id || crypto.randomUUID();
    this.data = data || {};
    this.dimensions = dimensions || [];
    this.shape = this.dimensions.map(d => d.size);
    this.metadata = options.metadata || {
      created: new Date().toISOString(),
      source: 'NovaCore',
      version: '1.0.0'
    };
    this.processed = false;
    this.transformations = [];
  }

  /**
   * Get the size of the tensor (total number of elements)
   * @returns {Number} - The total number of elements in the tensor
   */
  size() {
    if (this.shape.length === 0) return 0;
    return this.shape.reduce((acc, val) => acc * val, 1);
  }

  /**
   * Get the dimension names
   * @returns {Array} - Array of dimension names
   */
  getDimensionNames() {
    return this.dimensions.map(d => d.name);
  }

  /**
   * Get the size of a specific dimension
   * @param {String} dimensionName - The name of the dimension
   * @returns {Number} - The size of the dimension, or -1 if not found
   */
  getDimensionSize(dimensionName) {
    const dimension = this.dimensions.find(d => d.name === dimensionName);
    return dimension ? dimension.size : -1;
  }

  /**
   * Add a transformation to the tensor
   * @param {Object} transformation - The transformation to add
   * @returns {Tensor} - The tensor with the added transformation
   */
  addTransformation(transformation) {
    this.transformations.push({
      ...transformation,
      timestamp: new Date().toISOString()
    });
    return this;
  }

  /**
   * Mark the tensor as processed
   * @param {Object} processingMetadata - Metadata about the processing
   * @returns {Tensor} - The processed tensor
   */
  markAsProcessed(processingMetadata = {}) {
    this.processed = true;
    this.processingMetadata = {
      timestamp: new Date().toISOString(),
      ...processingMetadata
    };
    return this;
  }

  /**
   * Create a deep clone of the tensor
   * @returns {Tensor} - A new tensor with the same data and dimensions
   */
  clone() {
    return new Tensor(
      JSON.parse(JSON.stringify(this.data)),
      JSON.parse(JSON.stringify(this.dimensions)),
      {
        metadata: JSON.parse(JSON.stringify(this.metadata)),
        id: crypto.randomUUID()
      }
    );
  }

  /**
   * Create a tensor slice along a specific dimension
   * @param {String} dimensionName - The name of the dimension to slice
   * @param {Number} index - The index to slice at
   * @returns {Tensor} - A new tensor representing the slice
   */
  slice(dimensionName, index) {
    const dimensionIndex = this.dimensions.findIndex(d => d.name === dimensionName);
    if (dimensionIndex === -1) {
      throw new Error(`Dimension ${dimensionName} not found`);
    }

    if (index < 0 || index >= this.dimensions[dimensionIndex].size) {
      throw new Error(`Index ${index} out of bounds for dimension ${dimensionName} with size ${this.dimensions[dimensionIndex].size}`);
    }

    // Create a new set of dimensions without the sliced dimension
    const newDimensions = [...this.dimensions];
    newDimensions.splice(dimensionIndex, 1);

    // Create a new tensor with the sliced data
    const slicedTensor = new Tensor(
      { 
        ...this.data,
        _sliceInfo: {
          originalTensorId: this.id,
          dimension: dimensionName,
          index
        }
      },
      newDimensions,
      {
        metadata: {
          ...this.metadata,
          sliced: true,
          sliceDimension: dimensionName,
          sliceIndex: index,
          originalTensorId: this.id
        }
      }
    );

    return slicedTensor;
  }

  /**
   * Calculate a hash of the tensor data
   * @returns {String} - A SHA-256 hash of the tensor data
   */
  hash() {
    const dataString = JSON.stringify({
      data: this.data,
      dimensions: this.dimensions,
      shape: this.shape
    });
    
    return crypto.createHash('sha256').update(dataString).digest('hex');
  }

  /**
   * Convert the tensor to a JSON-serializable object
   * @returns {Object} - A JSON-serializable representation of the tensor
   */
  toJSON() {
    return {
      id: this.id,
      data: this.data,
      dimensions: this.dimensions,
      shape: this.shape,
      metadata: this.metadata,
      processed: this.processed,
      processingMetadata: this.processingMetadata,
      transformations: this.transformations,
      size: this.size(),
      hash: this.hash()
    };
  }

  /**
   * Create a tensor from a JSON object
   * @param {Object} json - The JSON representation of a tensor
   * @returns {Tensor} - A new tensor created from the JSON
   */
  static fromJSON(json) {
    const tensor = new Tensor(json.data, json.dimensions, {
      id: json.id,
      metadata: json.metadata
    });
    
    if (json.processed) {
      tensor.processed = json.processed;
      tensor.processingMetadata = json.processingMetadata;
    }
    
    if (json.transformations) {
      tensor.transformations = json.transformations;
    }
    
    return tensor;
  }
}

module.exports = Tensor;
