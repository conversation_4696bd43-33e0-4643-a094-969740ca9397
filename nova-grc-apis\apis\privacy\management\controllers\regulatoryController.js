/**
 * Regulatory Controller
 * 
 * This controller handles requests related to regulatory intelligence.
 */

const regulatory = require('../regulatory');
const { getDb } = require('../database/connection');
const logger = require('../logging');

/**
 * Get all regulations
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getRegulations(req, res) {
  try {
    const db = getDb();
    const { includeInactive } = req.query;
    
    const regulations = await regulatory.getRegulations(db, {
      includeInactive: includeInactive === 'true'
    });
    
    res.json({
      data: regulations
    });
  } catch (error) {
    logger.error('Error getting regulations', { error: error.message });
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get regulations'
    });
  }
}

/**
 * Get a specific regulation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getRegulation(req, res) {
  try {
    const db = getDb();
    const { id } = req.params;
    
    const regulation = await regulatory.getRegulation(db, id);
    
    res.json({
      data: regulation
    });
  } catch (error) {
    logger.error('Error getting regulation', { id: req.params.id, error: error.message });
    
    if (error.message.includes('not found')) {
      res.status(404).json({
        error: 'Not Found',
        message: `Regulation with ID ${req.params.id} not found`
      });
    } else {
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to get regulation'
      });
    }
  }
}

/**
 * Get all requirements
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getRequirements(req, res) {
  try {
    const db = getDb();
    const { regulationId, category, status } = req.query;
    
    const requirements = await regulatory.getRequirements(db, {
      regulationId,
      category,
      status
    });
    
    res.json({
      data: requirements
    });
  } catch (error) {
    logger.error('Error getting requirements', { error: error.message });
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get requirements'
    });
  }
}

/**
 * Get a specific requirement
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getRequirement(req, res) {
  try {
    const db = getDb();
    const { id } = req.params;
    
    const requirement = await regulatory.getRequirement(db, id);
    
    res.json({
      data: requirement
    });
  } catch (error) {
    logger.error('Error getting requirement', { id: req.params.id, error: error.message });
    
    if (error.message.includes('not found')) {
      res.status(404).json({
        error: 'Not Found',
        message: `Requirement with ID ${req.params.id} not found`
      });
    } else {
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to get requirement'
      });
    }
  }
}

/**
 * Get regulatory changes
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getChanges(req, res) {
  try {
    const db = getDb();
    const { regulationId, status, since } = req.query;
    
    const options = {
      regulationId,
      status
    };
    
    if (since) {
      options.since = new Date(since);
    }
    
    const changes = await regulatory.getChanges(db, options);
    
    res.json({
      data: changes
    });
  } catch (error) {
    logger.error('Error getting regulatory changes', { error: error.message });
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get regulatory changes'
    });
  }
}

/**
 * Get a specific change
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getChange(req, res) {
  try {
    const db = getDb();
    const { id } = req.params;
    
    const change = await regulatory.getChange(db, id);
    
    res.json({
      data: change
    });
  } catch (error) {
    logger.error('Error getting regulatory change', { id: req.params.id, error: error.message });
    
    if (error.message.includes('not found')) {
      res.status(404).json({
        error: 'Not Found',
        message: `Regulatory change with ID ${req.params.id} not found`
      });
    } else {
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to get regulatory change'
      });
    }
  }
}

/**
 * Check compliance for a data processing activity
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function checkCompliance(req, res) {
  try {
    const db = getDb();
    const { activityId } = req.params;
    const { includeRequirements, includeChanges } = req.query;
    
    // Get the activity from the database
    const activity = await db.collection('dataProcessingActivities').findOne({ id: activityId });
    
    if (!activity) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Data processing activity with ID ${activityId} not found`
      });
    }
    
    const compliance = await regulatory.checkCompliance(db, activity, {
      includeRequirements: includeRequirements === 'true',
      includeChanges: includeChanges === 'true'
    });
    
    res.json({
      data: compliance
    });
  } catch (error) {
    logger.error('Error checking compliance', { activityId: req.params.activityId, error: error.message });
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to check compliance'
    });
  }
}

/**
 * Get compliance status for all data processing activities
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getComplianceStatus(req, res) {
  try {
    const db = getDb();
    
    // Get all active data processing activities
    const activities = await db.collection('dataProcessingActivities').find({ status: 'active' }).toArray();
    
    const complianceStatus = await regulatory.getComplianceStatus(db, activities);
    
    res.json({
      data: complianceStatus
    });
  } catch (error) {
    logger.error('Error getting compliance status', { error: error.message });
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get compliance status'
    });
  }
}

/**
 * Get regulatory intelligence for a data processing activity
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getRegulatoryIntelligence(req, res) {
  try {
    const db = getDb();
    const { activityId } = req.params;
    const { includeCompliance, includeRequirements, includeChanges } = req.query;
    
    // Get the activity from the database
    const activity = await db.collection('dataProcessingActivities').findOne({ id: activityId });
    
    if (!activity) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Data processing activity with ID ${activityId} not found`
      });
    }
    
    const intelligence = await regulatory.getRegulatoryIntelligence(db, activity, {
      includeCompliance: includeCompliance !== 'false',
      includeRequirements: includeRequirements === 'true',
      includeChanges: includeChanges === 'true'
    });
    
    res.json({
      data: intelligence
    });
  } catch (error) {
    logger.error('Error getting regulatory intelligence', { activityId: req.params.activityId, error: error.message });
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get regulatory intelligence'
    });
  }
}

module.exports = {
  getRegulations,
  getRegulation,
  getRequirements,
  getRequirement,
  getChanges,
  getChange,
  checkCompliance,
  getComplianceStatus,
  getRegulatoryIntelligence
};
