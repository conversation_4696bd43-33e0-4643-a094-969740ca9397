/**
 * NovaFuse Demo Execution Service Connector
 * Integrates demo launching and management with NovaConnect
 * Enables real-time demo execution, monitoring, and results tracking
 */

const { spawn, exec } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class DemoExecutionConnector extends EventEmitter {
    constructor(novaConnect, options = {}) {
        super();
        
        this.novaConnect = novaConnect;
        this.options = {
            demoRootPath: options.demoRootPath || process.cwd(),
            demoTimeout: options.demoTimeout || 180000, // 3 minutes
            maxConcurrentDemos: options.maxConcurrentDemos || 3,
            enableRealTimeUpdates: options.enableRealTimeUpdates !== false,
            outputDirectory: options.outputDirectory || './demo-outputs',
            ...options
        };
        
        // Demo categories and their configurations
        this.demoCategories = {
            uuft: {
                name: 'UUFT Demonstrations',
                description: 'Universal Unified Field Theory validation demos',
                demos: [
                    { name: 'UUFT Gravity Test', file: 'uuft_gravity_test.py', type: 'python', duration: 60 },
                    { name: 'UUFT Medical Test', file: 'uuft_medical_test.py', type: 'python', duration: 90 },
                    { name: 'Three Body Problem Solver', file: 'three_body_problem_solver.py', type: 'python', duration: 120 },
                    { name: 'UUFT Performance Test', file: 'uuft_performance_test.py', type: 'python', duration: 45 }
                ]
            },
            consciousness: {
                name: 'Coherence Technology Demos',
                description: 'Coherence-native computing demonstrations',
                demos: [
                    { name: 'Advanced Coherence Demo', file: 'advanced_coherence_demo.py', type: 'python', duration: 120 },
                    { name: 'N3C Coherence Simulation', file: 'N3C_Coherence_Simulation.py', type: 'python', duration: 90 },
                    { name: 'Trinity Coherence Calibration', file: 'trinity_coherence_final_calibration.py', type: 'python', duration: 150 },
                    { name: 'Coherence Field Restoration', file: 'coherence_field_restoration.py', type: 'python', duration: 75 }
                ]
            },
            financial: {
                name: 'Financial Technology Demos',
                description: 'NovaFinX and financial prediction demonstrations',
                demos: [
                    { name: 'Volatility Smile Test', file: 'volatility_smile_test.py', type: 'python', duration: 60 },
                    { name: 'Quantum Finance Crisis', file: 'quantum_finance_adaptive_crisis.py', type: 'python', duration: 90 },
                    { name: 'Trinity Fusion Revenue', file: 'trinity_fusion_revenue_prediction_csm.py', type: 'python', duration: 120 },
                    { name: 'Sacred Numerology Demo', file: 'sacred_numerology_demo.py', type: 'python', duration: 45 }
                ]
            },
            medical: {
                name: 'Medical Technology Demos',
                description: 'NovaFold and medical applications',
                demos: [
                    { name: 'NovaFold Live Demo', file: 'NovaFold_Live_Demo.py', type: 'python', duration: 90 },
                    { name: 'NovaFold Strategic Demo', file: 'NovaFold_Strategic_Demo.py', type: 'python', duration: 120 },
                    { name: 'Reflex Grasp Test', file: 'reflex_grasp_test_complete_hand.py', type: 'python', duration: 60 }
                ]
            },
            blockchain: {
                name: 'Blockchain & Security Demos',
                description: 'KetherNet and security demonstrations',
                demos: [
                    { name: 'KetherNet Demo', file: 'coherence-reality-systems/kethernet-demo.js', type: 'node', duration: 90 },
                    { name: 'KetherNet Load Test', file: 'coherence-reality-systems/kethernet-load-test.js', type: 'node', duration: 120 },
                    { name: 'Quantum Coherence Firewall', file: 'quantum_consciousness_firewall.py', type: 'python', duration: 75 }
                ]
            },
            integration: {
                name: 'Integration Demos',
                description: 'Cross-platform integration demonstrations',
                demos: [
                    { name: 'Trinity CSDE Test', file: 'test_trinity_csde.py', type: 'python', duration: 90 },
                    { name: 'Trinity Day 3 Complete', file: 'trinity-day3-complete-test.js', type: 'node', duration: 120 },
                    { name: 'NovaMemX Test', file: 'test_novamemx.py', type: 'python', duration: 60 }
                ]
            }
        };
        
        this.activeDemos = new Map();
        this.demoHistory = [];
        this.demoResults = new Map();
        
        this.registerWithNovaConnect();
    }
    
    /**
     * Register this connector with NovaConnect
     */
    async registerWithNovaConnect() {
        const connectorConfig = {
            id: 'novafuse-demo-execution',
            name: 'NovaFuse Demo Execution Service',
            description: 'Demo launching and management system',
            version: '1.0.0',
            type: 'service',
            category: 'demonstration',
            endpoints: {
                'discover-demos': {
                    method: 'GET',
                    path: '/demos/discover',
                    description: 'Discover available demos by category'
                },
                'launch-demo': {
                    method: 'POST',
                    path: '/demos/launch',
                    description: 'Launch a specific demo or demo category'
                },
                'demo-status': {
                    method: 'GET',
                    path: '/demos/status',
                    description: 'Get demo execution status'
                },
                'demo-results': {
                    method: 'GET',
                    path: '/demos/results',
                    description: 'Get demo execution results and outputs'
                },
                'stop-demo': {
                    method: 'POST',
                    path: '/demos/stop',
                    description: 'Stop running demo'
                },
                'demo-recommendations': {
                    method: 'GET',
                    path: '/demos/recommendations',
                    description: 'Get intelligent demo recommendations'
                }
            },
            authentication: {
                type: 'none',
                required: false
            },
            realTimeEvents: [
                'demo.started',
                'demo.progress',
                'demo.output',
                'demo.completed',
                'demo.failed'
            ]
        };
        
        try {
            await this.novaConnect.registerConnector(connectorConfig);
            console.log('✅ Demo Execution Connector registered with NovaConnect');
            
            this.setupEventHandlers();
            await this.ensureOutputDirectory();
            
        } catch (error) {
            console.error('❌ Failed to register Demo Execution Connector:', error);
            throw error;
        }
    }
    
    /**
     * Set up NovaConnect event handlers
     */
    setupEventHandlers() {
        this.novaConnect.on('connector.execute', async (event) => {
            if (event.connectorId === 'novafuse-demo-execution') {
                await this.handleConnectorRequest(event);
            }
        });
        
        if (this.options.enableRealTimeUpdates) {
            this.on('demoStarted', (data) => {
                this.novaConnect.emit('demo.started', data);
            });
            
            this.on('demoProgress', (data) => {
                this.novaConnect.emit('demo.progress', data);
            });
            
            this.on('demoOutput', (data) => {
                this.novaConnect.emit('demo.output', data);
            });
            
            this.on('demoCompleted', (data) => {
                this.novaConnect.emit('demo.completed', data);
            });
            
            this.on('demoFailed', (data) => {
                this.novaConnect.emit('demo.failed', data);
            });
        }
    }
    
    /**
     * Handle NovaConnect connector requests
     */
    async handleConnectorRequest(event) {
        const { endpointId, parameters, requestId } = event;
        
        try {
            let result;
            
            switch (endpointId) {
                case 'discover-demos':
                    result = await this.discoverDemos(parameters);
                    break;
                    
                case 'launch-demo':
                    result = await this.launchDemo(parameters);
                    break;
                    
                case 'demo-status':
                    result = await this.getDemoStatus(parameters);
                    break;
                    
                case 'demo-results':
                    result = await this.getDemoResults(parameters);
                    break;
                    
                case 'stop-demo':
                    result = await this.stopDemo(parameters);
                    break;
                    
                case 'demo-recommendations':
                    result = await this.getDemoRecommendations(parameters);
                    break;
                    
                default:
                    throw new Error(`Unknown endpoint: ${endpointId}`);
            }
            
            this.novaConnect.emit('connector.response', {
                requestId,
                success: true,
                data: result
            });
            
        } catch (error) {
            console.error(`Demo Execution error for ${endpointId}:`, error);
            
            this.novaConnect.emit('connector.response', {
                requestId,
                success: false,
                error: {
                    message: error.message,
                    code: error.code || 'DEMO_EXECUTION_ERROR'
                }
            });
        }
    }
    
    /**
     * Discover available demos
     */
    async discoverDemos(parameters = {}) {
        const { category, includeMetadata = true } = parameters;
        
        const discoveredDemos = {};
        const categoriesToScan = category ? [category] : Object.keys(this.demoCategories);
        
        for (const categoryKey of categoriesToScan) {
            const categoryConfig = this.demoCategories[categoryKey];
            if (!categoryConfig) continue;
            
            const demos = [];
            
            for (const demo of categoryConfig.demos) {
                const demoInfo = {
                    name: demo.name,
                    file: demo.file,
                    type: demo.type,
                    duration: demo.duration,
                    status: 'available'
                };
                
                if (includeMetadata) {
                    try {
                        const filePath = path.join(this.options.demoRootPath, demo.file);
                        const stats = await fs.stat(filePath);
                        demoInfo.metadata = {
                            size: stats.size,
                            modified: stats.mtime,
                            exists: true
                        };
                    } catch (error) {
                        demoInfo.metadata = {
                            exists: false,
                            error: 'File not found'
                        };
                    }
                }
                
                demos.push(demoInfo);
            }
            
            discoveredDemos[categoryKey] = {
                name: categoryConfig.name,
                description: categoryConfig.description,
                demos: demos,
                count: demos.length
            };
        }
        
        return {
            totalCategories: Object.keys(discoveredDemos).length,
            totalDemos: Object.values(discoveredDemos).reduce((sum, cat) => sum + cat.count, 0),
            categories: discoveredDemos,
            discoveredAt: new Date().toISOString()
        };
    }
    
    /**
     * Launch a demo
     */
    async launchDemo(parameters = {}) {
        const { 
            category, 
            demoName, 
            demoFile,
            options = {},
            requestId = Date.now().toString()
        } = parameters;
        
        // Find the demo to launch
        let demoToLaunch = null;
        let categoryKey = null;
        
        if (demoFile) {
            // Direct file execution
            demoToLaunch = { name: demoFile, file: demoFile, type: this.detectFileType(demoFile) };
        } else if (category && demoName) {
            // Find demo by category and name
            const categoryConfig = this.demoCategories[category];
            if (categoryConfig) {
                demoToLaunch = categoryConfig.demos.find(d => d.name === demoName);
                categoryKey = category;
            }
        }
        
        if (!demoToLaunch) {
            throw new Error('Demo not found');
        }
        
        const demoExecution = {
            id: requestId,
            demo: demoToLaunch,
            category: categoryKey,
            startTime: new Date().toISOString(),
            status: 'running',
            options: options,
            output: [],
            process: null
        };
        
        this.activeDemos.set(requestId, demoExecution);
        
        this.emit('demoStarted', {
            executionId: requestId,
            demo: demoToLaunch.name,
            category: categoryKey,
            startTime: demoExecution.startTime
        });
        
        try {
            await this.executeDemoFile(demoExecution);
            
        } catch (error) {
            demoExecution.status = 'failed';
            demoExecution.error = error.message;
            demoExecution.endTime = new Date().toISOString();
            
            this.emit('demoFailed', {
                executionId: requestId,
                error: error.message
            });
        }
        
        return {
            executionId: requestId,
            demo: demoToLaunch.name,
            status: demoExecution.status,
            startTime: demoExecution.startTime
        };
    }
    
    /**
     * Execute a demo file
     */
    async executeDemoFile(demoExecution) {
        const { demo, options } = demoExecution;
        const filePath = path.join(this.options.demoRootPath, demo.file);
        
        return new Promise((resolve, reject) => {
            let command, args;
            
            switch (demo.type) {
                case 'python':
                    command = 'python';
                    args = [filePath];
                    break;
                case 'node':
                    command = 'node';
                    args = [filePath];
                    break;
                default:
                    reject(new Error(`Unsupported demo type: ${demo.type}`));
                    return;
            }
            
            const process = spawn(command, args, {
                cwd: this.options.demoRootPath,
                stdio: ['pipe', 'pipe', 'pipe']
            });
            
            demoExecution.process = process;
            
            // Capture output
            process.stdout.on('data', (data) => {
                const output = data.toString();
                demoExecution.output.push({
                    type: 'stdout',
                    content: output,
                    timestamp: new Date().toISOString()
                });
                
                this.emit('demoOutput', {
                    executionId: demoExecution.id,
                    type: 'stdout',
                    content: output
                });
            });
            
            process.stderr.on('data', (data) => {
                const output = data.toString();
                demoExecution.output.push({
                    type: 'stderr',
                    content: output,
                    timestamp: new Date().toISOString()
                });
                
                this.emit('demoOutput', {
                    executionId: demoExecution.id,
                    type: 'stderr',
                    content: output
                });
            });
            
            process.on('close', (code) => {
                demoExecution.status = code === 0 ? 'completed' : 'failed';
                demoExecution.exitCode = code;
                demoExecution.endTime = new Date().toISOString();
                demoExecution.duration = Date.now() - new Date(demoExecution.startTime).getTime();
                
                // Save output to file
                this.saveDemoOutput(demoExecution);
                
                // Store results
                this.demoResults.set(demoExecution.id, demoExecution);
                this.demoHistory.push({
                    id: demoExecution.id,
                    demo: demo.name,
                    category: demoExecution.category,
                    status: demoExecution.status,
                    duration: demoExecution.duration,
                    startTime: demoExecution.startTime,
                    endTime: demoExecution.endTime
                });
                
                this.activeDemos.delete(demoExecution.id);
                
                if (code === 0) {
                    this.emit('demoCompleted', {
                        executionId: demoExecution.id,
                        duration: demoExecution.duration,
                        outputLines: demoExecution.output.length
                    });
                    resolve(demoExecution);
                } else {
                    this.emit('demoFailed', {
                        executionId: demoExecution.id,
                        exitCode: code
                    });
                    reject(new Error(`Demo failed with exit code ${code}`));
                }
            });
            
            process.on('error', (error) => {
                demoExecution.status = 'failed';
                demoExecution.error = error.message;
                demoExecution.endTime = new Date().toISOString();
                
                this.activeDemos.delete(demoExecution.id);
                reject(error);
            });
            
            // Set timeout
            setTimeout(() => {
                if (demoExecution.status === 'running') {
                    process.kill('SIGTERM');
                    demoExecution.status = 'timeout';
                    demoExecution.endTime = new Date().toISOString();
                    this.activeDemos.delete(demoExecution.id);
                    reject(new Error('Demo execution timeout'));
                }
            }, this.options.demoTimeout);
        });
    }
    
    /**
     * Save demo output to file
     */
    async saveDemoOutput(demoExecution) {
        try {
            const outputFile = path.join(
                this.options.outputDirectory,
                `demo_${demoExecution.id}_${Date.now()}.json`
            );
            
            const outputData = {
                executionId: demoExecution.id,
                demo: demoExecution.demo,
                category: demoExecution.category,
                startTime: demoExecution.startTime,
                endTime: demoExecution.endTime,
                duration: demoExecution.duration,
                status: demoExecution.status,
                exitCode: demoExecution.exitCode,
                output: demoExecution.output,
                options: demoExecution.options
            };
            
            await fs.writeFile(outputFile, JSON.stringify(outputData, null, 2));
            demoExecution.outputFile = outputFile;
            
        } catch (error) {
            console.error('Failed to save demo output:', error);
        }
    }
    
    /**
     * Get demo status
     */
    async getDemoStatus(parameters = {}) {
        const { executionId } = parameters;
        
        if (executionId) {
            const execution = this.activeDemos.get(executionId) || this.demoResults.get(executionId);
            return execution || { error: 'Demo execution not found' };
        }
        
        return {
            activeDemos: Array.from(this.activeDemos.values()).map(demo => ({
                id: demo.id,
                demo: demo.demo.name,
                category: demo.category,
                status: demo.status,
                startTime: demo.startTime,
                outputLines: demo.output.length
            })),
            recentHistory: this.demoHistory.slice(-10)
        };
    }
    
    /**
     * Get demo results
     */
    async getDemoResults(parameters = {}) {
        const { executionId, includeOutput = false } = parameters;
        
        if (executionId) {
            const result = this.demoResults.get(executionId);
            if (result) {
                const response = { ...result };
                if (!includeOutput) {
                    response.output = `${result.output.length} lines (use includeOutput=true to view)`;
                }
                return response;
            }
            return { error: 'Demo results not found' };
        }
        
        return {
            history: this.demoHistory.slice(-20),
            totalExecutions: this.demoHistory.length
        };
    }
    
    /**
     * Stop a running demo
     */
    async stopDemo(parameters = {}) {
        const { executionId } = parameters;
        
        const execution = this.activeDemos.get(executionId);
        if (!execution) {
            return { success: false, message: 'Demo not found or already completed' };
        }
        
        if (execution.process) {
            execution.process.kill('SIGTERM');
            execution.status = 'stopped';
            execution.endTime = new Date().toISOString();
            this.activeDemos.delete(executionId);
            
            return { success: true, message: `Demo ${executionId} stopped` };
        }
        
        return { success: false, message: 'Demo process not found' };
    }
    
    /**
     * Get intelligent demo recommendations
     */
    async getDemoRecommendations(parameters = {}) {
        const { userInterests = [], previousDemos = [], difficulty = 'intermediate' } = parameters;
        
        const recommendations = [];
        
        // Recommend based on categories
        const categoryPriority = {
            'beginner': ['uuft', 'consciousness', 'integration'],
            'intermediate': ['financial', 'medical', 'blockchain'],
            'advanced': ['consciousness', 'financial', 'blockchain', 'integration']
        };
        
        const priorityCategories = categoryPriority[difficulty] || categoryPriority['intermediate'];
        
        for (const categoryKey of priorityCategories) {
            const category = this.demoCategories[categoryKey];
            if (category) {
                // Recommend 1-2 demos from each priority category
                const categoryDemos = category.demos.slice(0, 2);
                for (const demo of categoryDemos) {
                    recommendations.push({
                        category: categoryKey,
                        categoryName: category.name,
                        demo: demo.name,
                        file: demo.file,
                        duration: demo.duration,
                        type: demo.type,
                        reason: `Recommended for ${difficulty} users interested in ${category.name.toLowerCase()}`
                    });
                }
            }
        }
        
        return {
            recommendations: recommendations.slice(0, 8), // Limit to 8 recommendations
            difficulty: difficulty,
            totalAvailable: Object.values(this.demoCategories).reduce((sum, cat) => sum + cat.demos.length, 0)
        };
    }
    
    /**
     * Detect file type from extension
     */
    detectFileType(filename) {
        const ext = path.extname(filename).toLowerCase();
        switch (ext) {
            case '.py': return 'python';
            case '.js': return 'node';
            default: return 'unknown';
        }
    }
    
    /**
     * Ensure output directory exists
     */
    async ensureOutputDirectory() {
        try {
            await fs.mkdir(this.options.outputDirectory, { recursive: true });
        } catch (error) {
            console.error('Failed to create output directory:', error);
        }
    }
}

module.exports = DemoExecutionConnector;
