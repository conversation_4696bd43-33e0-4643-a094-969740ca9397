import React from 'react';
import { render, screen } from '@testing-library/react';
import UACInfographicStatic from '../../components/UACInfographicStatic';

// Mock Next.js Image component
jest.mock('next/image', () => {
  return ({ src, alt }) => {
    return <img src={src} alt={alt} data-testid="mock-image" />;
  };
});

describe('UACInfographicStatic', () => {
  it('renders the header correctly', () => {
    render(<UACInfographicStatic />);
    
    // Check if the header is rendered
    expect(screen.getByText('The Dual Meaning of UAC')).toBeInTheDocument();
    expect(screen.getByText(/One acronym with two powerful interpretations/)).toBeInTheDocument();
  });
  
  it('renders the brain infographic image', () => {
    render(<UACInfographicStatic />);
    
    // Check if the image is rendered with the correct alt text
    const image = screen.getByAltText('UAC: Universal API Connector and Unified AI Compliance');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', '/images/uac/left_right_brain_infographic.png');
  });
  
  it('renders the Universal API Connector section', () => {
    render(<UACInfographicStatic />);
    
    // Check if the Universal API Connector section is rendered
    expect(screen.getByText('Universal API Connector')).toBeInTheDocument();
    expect(screen.getByText(/connectivity layer that seamlessly integrates/)).toBeInTheDocument();
    
    // Check if the key capabilities are rendered
    expect(screen.getByText('Connect')).toBeInTheDocument();
    expect(screen.getByText('Transform')).toBeInTheDocument();
    expect(screen.getByText('Secure')).toBeInTheDocument();
  });
  
  it('renders the Unified AI Compliance section', () => {
    render(<UACInfographicStatic />);
    
    // Check if the Unified AI Compliance section is rendered
    expect(screen.getByText('Unified AI Compliance')).toBeInTheDocument();
    expect(screen.getByText(/intelligence layer that continuously monitors/)).toBeInTheDocument();
    
    // Check if the key capabilities are rendered
    expect(screen.getByText('Monitor')).toBeInTheDocument();
    expect(screen.getByText('Predict')).toBeInTheDocument();
    expect(screen.getByText('Adapt')).toBeInTheDocument();
  });
  
  it('renders the Power of Integration section', () => {
    render(<UACInfographicStatic />);
    
    // Check if the Power of Integration section is rendered
    expect(screen.getByText('The Power of Integration')).toBeInTheDocument();
    expect(screen.getByText(/When Universal API Connector meets Unified AI Compliance/)).toBeInTheDocument();
    
    // Check if the benefits are rendered
    expect(screen.getByText('Proactive Protection')).toBeInTheDocument();
    expect(screen.getByText('Accelerated Innovation')).toBeInTheDocument();
    expect(screen.getByText('Unified Governance')).toBeInTheDocument();
    expect(screen.getByText('Cost Reduction')).toBeInTheDocument();
  });
  
  it('renders the Compliance Brain section', () => {
    render(<UACInfographicStatic />);
    
    // Check if the Compliance Brain section is rendered
    expect(screen.getByText('The Compliance Brain')).toBeInTheDocument();
    expect(screen.getByText(/Together, these two aspects of UAC/)).toBeInTheDocument();
    
    // Check if the key features are rendered
    expect(screen.getByText('Real-Time Risk Interception')).toBeInTheDocument();
    expect(screen.getByText('Explainable Compliance')).toBeInTheDocument();
    expect(screen.getByText('Self-Updating Framework')).toBeInTheDocument();
  });
  
  it('renders the footer quote', () => {
    render(<UACInfographicStatic />);
    
    // Check if the footer quote is rendered
    expect(screen.getByText(/The UAC is not just a product. It's a platform. A movement./)).toBeInTheDocument();
  });
});
