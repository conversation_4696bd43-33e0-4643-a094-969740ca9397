/**
 * Stress Test Helper Functions
 * 
 * This file contains helper functions for the Artillery stress tests.
 */

/**
 * Generate random data for testing data normalization
 * @param {Object} userContext - Artillery user context
 * @param {Object} events - Artillery events
 * @param {Function} done - Callback function
 */
function generateRandomData(userContext, events, done) {
  // Generate a random data structure
  const data = {
    id: Math.floor(Math.random() * 1000000),
    timestamp: new Date().toISOString(),
    source: 'stress-test',
    type: ['compliance', 'security', 'privacy'][Math.floor(Math.random() * 3)],
    attributes: {
      priority: ['high', 'medium', 'low'][Math.floor(Math.random() * 3)],
      category: ['network', 'identity', 'data', 'application'][Math.floor(Math.random() * 4)],
      tags: Array.from({ length: Math.floor(Math.random() * 5) + 1 }, () => 
        ['gcp', 'aws', 'azure', 'on-prem', 'hybrid', 'container', 'serverless'][Math.floor(Math.random() * 7)]
      )
    },
    metrics: {
      count: Math.floor(Math.random() * 100),
      duration: Math.random() * 1000,
      score: Math.random() * 100
    },
    items: Array.from({ length: Math.floor(Math.random() * 10) + 1 }, (_, i) => ({
      id: `item-${i}`,
      name: `Test Item ${i}`,
      value: Math.random() * 100,
      status: ['active', 'inactive', 'pending'][Math.floor(Math.random() * 3)]
    }))
  };

  // Add data to the user context
  userContext.vars.data = data;
  
  return done();
}

/**
 * Generate a random API key for authentication
 * @param {Object} userContext - Artillery user context
 * @param {Object} events - Artillery events
 * @param {Function} done - Callback function
 */
function generateApiKey(userContext, events, done) {
  // Generate a random API key
  const apiKey = Buffer.from(Math.random().toString(36).substring(2, 15) + 
                             Math.random().toString(36).substring(2, 15))
                       .toString('base64');
  
  // Add API key to the user context
  userContext.vars.apiKey = apiKey;
  
  return done();
}

/**
 * Log response details for debugging
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Object} userContext - Artillery user context
 * @param {Object} events - Artillery events
 * @param {Function} done - Callback function
 */
function logResponse(req, res, userContext, events, done) {
  console.log(`Response: ${res.statusCode} - ${JSON.stringify(res.body).substring(0, 100)}...`);
  return done();
}

// Export functions
module.exports = {
  generateRandomData,
  generateApiKey,
  logResponse
};
