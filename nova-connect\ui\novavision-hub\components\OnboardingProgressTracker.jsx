/**
 * OnboardingProgressTracker Component
 * 
 * A component for tracking and displaying onboarding progress.
 */

import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n';
import { useAccessibility } from '../accessibility';
import { useOnboarding } from '../onboarding/OnboardingContext';

/**
 * OnboardingProgressTracker component
 * 
 * @param {Object} props - Component props
 * @param {Array} props.tours - List of tour IDs to track
 * @param {boolean} [props.showCompleted=true] - Whether to show completed tours
 * @param {boolean} [props.showRecommended=true] - Whether to show recommended tours
 * @param {boolean} [props.showReset=true] - Whether to show reset button
 * @param {Function} [props.onTourStart] - Function to call when a tour is started
 * @param {Function} [props.onTourReset] - Function to call when a tour is reset
 * @param {string} [props.className=''] - Additional CSS class names
 * @param {Object} [props.style={}] - Additional inline styles
 * @returns {React.ReactElement} OnboardingProgressTracker component
 */
const OnboardingProgressTracker = ({
  tours,
  showCompleted = true,
  showRecommended = true,
  showReset = true,
  onTourStart,
  onTourReset,
  className = '',
  style = {}
}) => {
  // Hooks
  const { translate } = useI18n();
  const { settings } = useAccessibility();
  const {
    tours: registeredTours,
    completedTours,
    isTourCompleted,
    startTour,
    resetTour,
    resetAllTours
  } = useOnboarding();
  
  // State
  const [toursList, setToursList] = useState([]);
  const [completedCount, setCompletedCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  
  // Update tours list
  useEffect(() => {
    const tourList = tours.map(tourId => {
      const tourConfig = registeredTours[tourId] || {};
      const completed = isTourCompleted(tourId);
      
      return {
        id: tourId,
        title: tourConfig.title || tourId,
        description: tourConfig.description || '',
        completed,
        steps: tourConfig.steps || []
      };
    });
    
    setToursList(tourList);
    setCompletedCount(tourList.filter(tour => tour.completed).length);
    setTotalCount(tourList.length);
  }, [tours, registeredTours, completedTours, isTourCompleted]);
  
  // Handle tour start
  const handleTourStart = (tourId) => {
    if (onTourStart) {
      onTourStart(tourId);
    }
    
    startTour(tourId);
  };
  
  // Handle tour reset
  const handleTourReset = (tourId) => {
    if (onTourReset) {
      onTourReset(tourId);
    }
    
    resetTour(tourId);
  };
  
  // Handle reset all tours
  const handleResetAll = () => {
    if (onTourReset) {
      onTourReset();
    }
    
    resetAllTours();
  };
  
  // Calculate progress percentage
  const progressPercentage = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;
  
  return (
    <div className={`onboarding-progress-tracker ${className}`} style={style}>
      {/* Header */}
      <div className="onboarding-progress-tracker__header">
        <h2 className="onboarding-progress-tracker__title">
          {translate('onboarding.progressTitle', 'Onboarding Progress')}
        </h2>
        
        {showReset && (
          <button
            className="onboarding-progress-tracker__reset-all"
            onClick={handleResetAll}
            aria-label={translate('onboarding.resetAll', 'Reset All Tours')}
          >
            {translate('onboarding.resetAll', 'Reset All')}
          </button>
        )}
      </div>
      
      {/* Progress bar */}
      <div className="onboarding-progress-tracker__progress">
        <div className="onboarding-progress-tracker__progress-bar">
          <div
            className="onboarding-progress-tracker__progress-indicator"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        
        <div className="onboarding-progress-tracker__progress-text">
          {translate('onboarding.progressText', '{{completed}} of {{total}} completed', {
            completed: completedCount,
            total: totalCount
          })}
        </div>
      </div>
      
      {/* Tours list */}
      <div className="onboarding-progress-tracker__tours">
        {/* Recommended tours */}
        {showRecommended && toursList.filter(tour => !tour.completed).length > 0 && (
          <div className="onboarding-progress-tracker__section">
            <h3 className="onboarding-progress-tracker__section-title">
              {translate('onboarding.recommendedTours', 'Recommended Tours')}
            </h3>
            
            <ul className="onboarding-progress-tracker__list">
              {toursList
                .filter(tour => !tour.completed)
                .map(tour => (
                  <li key={tour.id} className="onboarding-progress-tracker__item">
                    <div className="onboarding-progress-tracker__item-content">
                      <h4 className="onboarding-progress-tracker__item-title">
                        {tour.title}
                      </h4>
                      
                      <p className="onboarding-progress-tracker__item-description">
                        {tour.description}
                      </p>
                      
                      <div className="onboarding-progress-tracker__item-meta">
                        {translate('onboarding.stepsCount', '{{count}} steps', {
                          count: tour.steps.length
                        })}
                      </div>
                    </div>
                    
                    <div className="onboarding-progress-tracker__item-actions">
                      <button
                        className="onboarding-progress-tracker__start"
                        onClick={() => handleTourStart(tour.id)}
                      >
                        {translate('onboarding.startTour', 'Start')}
                      </button>
                    </div>
                  </li>
                ))}
            </ul>
          </div>
        )}
        
        {/* Completed tours */}
        {showCompleted && toursList.filter(tour => tour.completed).length > 0 && (
          <div className="onboarding-progress-tracker__section">
            <h3 className="onboarding-progress-tracker__section-title">
              {translate('onboarding.completedTours', 'Completed Tours')}
            </h3>
            
            <ul className="onboarding-progress-tracker__list">
              {toursList
                .filter(tour => tour.completed)
                .map(tour => (
                  <li key={tour.id} className="onboarding-progress-tracker__item onboarding-progress-tracker__item--completed">
                    <div className="onboarding-progress-tracker__item-content">
                      <h4 className="onboarding-progress-tracker__item-title">
                        {tour.title}
                      </h4>
                      
                      <p className="onboarding-progress-tracker__item-description">
                        {tour.description}
                      </p>
                      
                      <div className="onboarding-progress-tracker__item-meta">
                        {translate('onboarding.stepsCount', '{{count}} steps', {
                          count: tour.steps.length
                        })}
                      </div>
                    </div>
                    
                    <div className="onboarding-progress-tracker__item-actions">
                      {showReset && (
                        <button
                          className="onboarding-progress-tracker__reset"
                          onClick={() => handleTourReset(tour.id)}
                        >
                          {translate('onboarding.resetTour', 'Reset')}
                        </button>
                      )}
                      
                      <button
                        className="onboarding-progress-tracker__restart"
                        onClick={() => handleTourStart(tour.id)}
                      >
                        {translate('onboarding.restartTour', 'Restart')}
                      </button>
                    </div>
                  </li>
                ))}
            </ul>
          </div>
        )}
        
        {/* No tours */}
        {toursList.length === 0 && (
          <div className="onboarding-progress-tracker__empty">
            {translate('onboarding.noTours', 'No onboarding tours available.')}
          </div>
        )}
      </div>
    </div>
  );
};

OnboardingProgressTracker.propTypes = {
  tours: PropTypes.arrayOf(PropTypes.string).isRequired,
  showCompleted: PropTypes.bool,
  showRecommended: PropTypes.bool,
  showReset: PropTypes.bool,
  onTourStart: PropTypes.func,
  onTourReset: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default OnboardingProgressTracker;
