%% Cross-Module Data Processing Pipeline
%% Comphyology Patent Diagram - Figure X
%% USPTO-Compliant Black & White Styling

graph TD
    %% Main Components
    subgraph NovaFuse_System["NovaFuse Cross-Module Data Processing"]
        %% Data Flow
        A[Raw Data Input] --> B{Data Ingestion &\nNormalization}
        B --> C[Comphyology Core /\nCoherence Buffer]

        %% Processing Modules
        C --> D[NovaShield\n(Security Coherence)]
        C --> E[NovaTrack\n(Compliance Coherence)]
        C --> F[NovaFlowX\n(Workflow Coherence)]
        C --> G[NEPI Engine\n(Pattern Coherence)]
        C --> H[Other Nova Modules / CSEs]

        %% Unified Storage
        D -->|Processed Data| I[(Unified Coherence\nData Store)]
        E -->|Processed Data| I
        F -->|Processed Data| I
        G -->|Processed Data| I
        H -->|Processed Data| I

        %% Governance & Output
        I --> J[<PERSON><PERSON> (C-AIaaS)\nGovernance & Optimization]
        J --> K[Coherent Output / Action]
    end

    %% Styling for USPTO Compliance
    classDef input fill:#ffffff,stroke:#000000,stroke-width:1.5px,shape:rect
    classDef process fill:#ffffff,stroke:#000000,stroke-width:1.5px,shape:rect
    classDef module fill:#ffffff,stroke:#000000,stroke-width:1.5px,shape:rect,stroke-dasharray: 5 5
    classDef storage fill:#ffffff,stroke:#000000,stroke-width:1.5px,shape:cylinder
    classDef decision fill:#ffffff,stroke:#000000,stroke-width:1.5px,shape:diamond
    classDef output fill:#ffffff,stroke:#000000,stroke-width:1.5px,shape:rect,stroke-dasharray: 3 3
    
    %% Apply styles
    class A input
    class B decision
    class C,G process
    class D,E,F,H module
    class I storage
    class J process
    class K output
    
    %% Reference Numbers (1100 series for this diagram)
    A:::reference1100
    B:::reference1110
    C:::reference1120
    D:::reference1130
    E:::reference1140
    F:::reference1150
    G:::reference1160
    H:::reference1170
    I:::reference1180
    J:::reference1190
    K:::reference1200
    
    %% Hide reference numbers from display but keep in source
    classDef reference1100,reference1110,reference1120,reference1130,reference1140,reference1150,
    reference1160,reference1170,reference1180,reference1190,reference1200
        fill:none,stroke:none,font-size:0,width:0,height:0
    
    %% Diagram Metadata
    linkStyle default stroke:#000000,stroke-width:1.5px,fill:none
    
    %% Legend (not shown in final diagram but useful for documentation)
    %% Legend:
    %% - Rectangles: Processes/Components
    %% - Dashed rectangles: Processing Modules
    %% - Diamond: Decision Point
    %% - Cylinder: Data Storage
    %% - Dashed rectangle: Output/Action
    %% - Solid arrows: Data Flow
