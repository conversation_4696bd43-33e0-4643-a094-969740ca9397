/**
 * Cross-Domain Validation
 *
 * This module provides sophisticated cross-domain validation rules for the
 * Finite Universe Principle defense system. It ensures that data from one
 * domain does not contaminate another domain, maintaining the containerization
 * pillar of the Finite Universe Principle.
 * 
 * Key features:
 * 1. Sophisticated cross-domain validation rules
 * 2. Domain boundary detection algorithms
 * 3. Cross-domain contamination prevention
 * 4. Domain-specific validation policies
 */

const EventEmitter = require('events');

/**
 * CrossDomainValidator class
 * 
 * Provides sophisticated cross-domain validation to prevent contamination.
 */
class CrossDomainValidator extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      strictMode: true, // Throw errors on validation failures
      enableContaminationPrevention: true, // Prevent cross-domain contamination
      enableDomainDetection: true, // Enable automatic domain detection
      ...options
    };

    // Initialize validation rules
    this.validationRules = {
      cyber: this._createCyberValidationRules(),
      financial: this._createFinancialValidationRules(),
      medical: this._createMedicalValidationRules(),
      universal: this._createUniversalValidationRules()
    };

    // Initialize domain detection rules
    this.detectionRules = {
      cyber: this._createCyberDetectionRules(),
      financial: this._createFinancialDetectionRules(),
      medical: this._createMedicalDetectionRules()
    };

    // Initialize metrics
    this.metrics = {
      validationCount: 0,
      validationFailures: 0,
      contaminationPrevented: 0,
      domainDetections: 0,
      domainDetectionFailures: 0
    };

    if (this.options.enableLogging) {
      console.log('CrossDomainValidator initialized with options:', this.options);
    }
  }

  /**
   * Validate data across domains
   * @param {any} data - Data to validate
   * @param {string} sourceDomain - Source domain
   * @param {string} targetDomain - Target domain
   * @returns {Object} - Validation result
   */
  validateCrossDomain(data, sourceDomain, targetDomain) {
    if (!data || typeof data !== 'object') {
      return { valid: true };
    }
    
    this.metrics.validationCount++;
    
    // Get validation rules for target domain
    const rules = this.validationRules[targetDomain] || this.validationRules.universal;
    
    // Validate data
    const validationResult = rules.validate(data, sourceDomain);
    
    if (!validationResult.valid) {
      this.metrics.validationFailures++;
      
      this.emit('validation-failure', {
        sourceDomain,
        targetDomain,
        reason: validationResult.reason,
        data: this._sanitizeDataForLogging(data)
      });
      
      if (this.options.strictMode) {
        throw new Error(`Cross-domain validation failed: ${validationResult.reason}`);
      }
    }
    
    return validationResult;
  }

  /**
   * Prevent cross-domain contamination
   * @param {any} data - Data to sanitize
   * @param {string} sourceDomain - Source domain
   * @param {string} targetDomain - Target domain
   * @returns {any} - Sanitized data
   */
  preventContamination(data, sourceDomain, targetDomain) {
    if (!this.options.enableContaminationPrevention) {
      return data;
    }
    
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    // Get validation rules for target domain
    const rules = this.validationRules[targetDomain] || this.validationRules.universal;
    
    // Validate data
    const validationResult = rules.validate(data, sourceDomain);
    
    if (!validationResult.valid) {
      this.metrics.contaminationPrevented++;
      
      // Apply sanitization rules
      const sanitizedData = rules.sanitize(data, sourceDomain);
      
      this.emit('contamination-prevented', {
        sourceDomain,
        targetDomain,
        reason: validationResult.reason,
        data: this._sanitizeDataForLogging(data)
      });
      
      return sanitizedData;
    }
    
    return data;
  }

  /**
   * Detect the domain of data
   * @param {any} data - Data to detect domain for
   * @returns {string|null} - Detected domain or null if not detected
   */
  detectDomain(data) {
    if (!this.options.enableDomainDetection) {
      return null;
    }
    
    if (!data || typeof data !== 'object') {
      return null;
    }
    
    // Check each domain's detection rules
    for (const domain in this.detectionRules) {
      if (this.detectionRules[domain].detect(data)) {
        this.metrics.domainDetections++;
        return domain;
      }
    }
    
    this.metrics.domainDetectionFailures++;
    return null;
  }

  /**
   * Create cyber domain validation rules
   * @returns {Object} - Cyber domain validation rules
   * @private
   */
  _createCyberValidationRules() {
    return {
      validate: (data, sourceDomain) => {
        // Validate data from other domains against cyber domain rules
        
        // Check for financial data contamination
        if (sourceDomain === 'financial' && (
            data.balance !== undefined ||
            data.interestRate !== undefined ||
            data.transactionVolume !== undefined ||
            data.profitMargin !== undefined
        )) {
          return {
            valid: false,
            reason: 'Financial data contamination in cyber domain'
          };
        }
        
        // Check for medical data contamination
        if (sourceDomain === 'medical' && (
            data.heartRate !== undefined ||
            data.bloodPressure !== undefined ||
            data.temperature !== undefined ||
            data.oxygenLevel !== undefined
        )) {
          return {
            valid: false,
            reason: 'Medical data contamination in cyber domain'
          };
        }
        
        return { valid: true };
      },
      sanitize: (data, sourceDomain) => {
        // Sanitize data for cyber domain
        const sanitizedData = { ...data };
        
        // Remove financial data
        if (sourceDomain === 'financial') {
          delete sanitizedData.balance;
          delete sanitizedData.interestRate;
          delete sanitizedData.transactionVolume;
          delete sanitizedData.profitMargin;
        }
        
        // Remove medical data
        if (sourceDomain === 'medical') {
          delete sanitizedData.heartRate;
          delete sanitizedData.bloodPressure;
          delete sanitizedData.temperature;
          delete sanitizedData.oxygenLevel;
        }
        
        return sanitizedData;
      }
    };
  }

  /**
   * Create financial domain validation rules
   * @returns {Object} - Financial domain validation rules
   * @private
   */
  _createFinancialValidationRules() {
    return {
      validate: (data, sourceDomain) => {
        // Validate data from other domains against financial domain rules
        
        // Check for cyber data contamination
        if (sourceDomain === 'cyber' && (
            data.securityScore !== undefined ||
            data.threatLevel !== undefined ||
            data.encryptionStrength !== undefined
        )) {
          return {
            valid: false,
            reason: 'Cyber data contamination in financial domain'
          };
        }
        
        // Check for medical data contamination
        if (sourceDomain === 'medical' && (
            data.heartRate !== undefined ||
            data.bloodPressure !== undefined ||
            data.temperature !== undefined ||
            data.oxygenLevel !== undefined
        )) {
          return {
            valid: false,
            reason: 'Medical data contamination in financial domain'
          };
        }
        
        return { valid: true };
      },
      sanitize: (data, sourceDomain) => {
        // Sanitize data for financial domain
        const sanitizedData = { ...data };
        
        // Remove cyber data
        if (sourceDomain === 'cyber') {
          delete sanitizedData.securityScore;
          delete sanitizedData.threatLevel;
          delete sanitizedData.encryptionStrength;
        }
        
        // Remove medical data
        if (sourceDomain === 'medical') {
          delete sanitizedData.heartRate;
          delete sanitizedData.bloodPressure;
          delete sanitizedData.temperature;
          delete sanitizedData.oxygenLevel;
        }
        
        return sanitizedData;
      }
    };
  }

  /**
   * Create medical domain validation rules
   * @returns {Object} - Medical domain validation rules
   * @private
   */
  _createMedicalValidationRules() {
    return {
      validate: (data, sourceDomain) => {
        // Validate data from other domains against medical domain rules
        
        // Check for cyber data contamination
        if (sourceDomain === 'cyber' && (
            data.securityScore !== undefined ||
            data.threatLevel !== undefined ||
            data.encryptionStrength !== undefined
        )) {
          return {
            valid: false,
            reason: 'Cyber data contamination in medical domain'
          };
        }
        
        // Check for financial data contamination
        if (sourceDomain === 'financial' && (
            data.balance !== undefined ||
            data.interestRate !== undefined ||
            data.transactionVolume !== undefined ||
            data.profitMargin !== undefined
        )) {
          return {
            valid: false,
            reason: 'Financial data contamination in medical domain'
          };
        }
        
        return { valid: true };
      },
      sanitize: (data, sourceDomain) => {
        // Sanitize data for medical domain
        const sanitizedData = { ...data };
        
        // Remove cyber data
        if (sourceDomain === 'cyber') {
          delete sanitizedData.securityScore;
          delete sanitizedData.threatLevel;
          delete sanitizedData.encryptionStrength;
        }
        
        // Remove financial data
        if (sourceDomain === 'financial') {
          delete sanitizedData.balance;
          delete sanitizedData.interestRate;
          delete sanitizedData.transactionVolume;
          delete sanitizedData.profitMargin;
        }
        
        return sanitizedData;
      }
    };
  }

  /**
   * Create universal domain validation rules
   * @returns {Object} - Universal domain validation rules
   * @private
   */
  _createUniversalValidationRules() {
    return {
      validate: (data, sourceDomain) => {
        // Universal domain accepts all data
        return { valid: true };
      },
      sanitize: (data, sourceDomain) => {
        // No sanitization needed for universal domain
        return data;
      }
    };
  }

  /**
   * Create cyber domain detection rules
   * @returns {Object} - Cyber domain detection rules
   * @private
   */
  _createCyberDetectionRules() {
    return {
      detect: (data) => {
        // Detect if data belongs to cyber domain
        return data.securityScore !== undefined || 
               data.threatLevel !== undefined ||
               data.encryptionStrength !== undefined ||
               data._domain === 'cyber';
      }
    };
  }

  /**
   * Create financial domain detection rules
   * @returns {Object} - Financial domain detection rules
   * @private
   */
  _createFinancialDetectionRules() {
    return {
      detect: (data) => {
        // Detect if data belongs to financial domain
        return data.balance !== undefined || 
               data.interestRate !== undefined ||
               data.transactionVolume !== undefined ||
               data.profitMargin !== undefined ||
               data._domain === 'financial';
      }
    };
  }

  /**
   * Create medical domain detection rules
   * @returns {Object} - Medical domain detection rules
   * @private
   */
  _createMedicalDetectionRules() {
    return {
      detect: (data) => {
        // Detect if data belongs to medical domain
        return data.heartRate !== undefined || 
               data.bloodPressure !== undefined ||
               data.temperature !== undefined ||
               data.oxygenLevel !== undefined ||
               data._domain === 'medical';
      }
    };
  }

  /**
   * Sanitize data for logging
   * @param {any} data - Data to sanitize
   * @returns {any} - Sanitized data
   * @private
   */
  _sanitizeDataForLogging(data) {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    // Create a shallow copy
    const sanitized = { ...data };
    
    // Remove potentially sensitive fields
    delete sanitized.patientId;
    delete sanitized.patientName;
    delete sanitized.dateOfBirth;
    delete sanitized.ssn;
    delete sanitized.accountNumber;
    delete sanitized.password;
    
    return sanitized;
  }

  /**
   * Get validator metrics
   * @returns {Object} - Validator metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Reset validator metrics
   */
  resetMetrics() {
    this.metrics = {
      validationCount: 0,
      validationFailures: 0,
      contaminationPrevented: 0,
      domainDetections: 0,
      domainDetectionFailures: 0
    };
    
    this.emit('metrics-reset');
  }
}

/**
 * Create a cross-domain validator with recommended settings
 * @param {Object} options - Configuration options
 * @returns {CrossDomainValidator} - Configured cross-domain validator
 */
function createCrossDomainValidator(options = {}) {
  return new CrossDomainValidator({
    enableLogging: true,
    strictMode: true,
    enableContaminationPrevention: true,
    enableDomainDetection: true,
    ...options
  });
}

module.exports = {
  CrossDomainValidator,
  createCrossDomainValidator
};
