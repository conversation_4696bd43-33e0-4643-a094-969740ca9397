import { NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs'
import { prisma } from '@/lib/prisma'

export async function GET(req: Request) {
  const { userId } = auth()
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const categories = await prisma.category.findMany({
      where: {
        userId: userId
      },
      include: {
        metrics: true,
        products: true
      }
    })

    return NextResponse.json(categories)
  } catch (error) {
    console.error('Error fetching categories:', error)
    return NextResponse.json({ error: 'Failed to fetch categories' }, { status: 500 })
  }
}

export async function POST(req: Request) {
  const { userId } = auth()
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const body = await req.json()
    
    // Calculate initial triadic metrics
    const metrics = calculateTriadicMetrics(body.products)

    const category = await prisma.category.create({
      data: {
        userId,
        name: body.name,
        icon: body.icon,
        metrics: {
          create: {
            psi: metrics.psi,
            phi: metrics.phi,
            kappa: metrics.kappa
          }
        }
      }
    })

    return NextResponse.json(category)
  } catch (error) {
    console.error('Error creating category:', error)
    return NextResponse.json({ error: 'Failed to create category' }, { status: 500 })
  }
}

function calculateTriadicMetrics(products: any[]) {
  // Basic metrics calculation - would be more complex in a real system
  const totalRevenue = products.reduce((sum, p) => sum + p.revenue, 0)
  const totalConversions = products.reduce((sum, p) => sum + p.conversions, 0)
  const totalProducts = products.length

  return {
    psi: (totalRevenue / totalProducts) * 100 || 0,
    phi: (totalConversions / totalProducts) * 100 || 0,
    kappa: (totalRevenue / totalConversions) * 10 || 0
  }
}
