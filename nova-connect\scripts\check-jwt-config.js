/**
 * JWT Configuration Check
 * 
 * This script checks for proper JWT configuration in the application.
 * It analyzes the server.js, auth files, and other relevant files to ensure that JWT is properly configured.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Files to check
const filesToCheck = [
  path.join(__dirname, '..', 'server.js'),
  path.join(__dirname, '..', 'app.js'),
  path.join(__dirname, '..', 'index.js'),
  path.join(__dirname, '..', 'auth', 'authentication-manager.js'),
  path.join(__dirname, '..', 'middleware', 'auth.js'),
  path.join(__dirname, '..', 'config', 'auth.js')
];

// Environment variables to check
const envVarsToCheck = [
  'JWT_SECRET',
  'JWT_EXPIRATION',
  'JWT_ISSUER',
  'JWT_AUDIENCE'
];

// Results
const results = {
  jwtFound: false,
  jwtImportFound: false,
  jwtConfigured: false,
  jwtSecretConfigured: false,
  jwtExpirationConfigured: false,
  jwtAlgorithmConfigured: false,
  jwtVerificationConfigured: false,
  issues: []
};

// Check if jsonwebtoken is installed
try {
  execSync('npm list jsonwebtoken', { stdio: 'pipe' });
  results.jwtFound = true;
} catch (error) {
  results.issues.push('jsonwebtoken is not installed. Run: npm install jsonwebtoken --save');
}

// Check files for JWT configuration
for (const file of filesToCheck) {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    
    // Check for JWT import
    if (content.includes('require(\'jsonwebtoken\')') || 
        content.includes('require("jsonwebtoken")') || 
        content.includes('from \'jsonwebtoken\'') || 
        content.includes('from "jsonwebtoken"')) {
      results.jwtImportFound = true;
    }
    
    // Check for JWT configuration
    if (content.includes('jwt.sign(') || content.includes('sign(')) {
      results.jwtConfigured = true;
    }
    
    // Check for JWT secret
    if (content.includes('process.env.JWT_SECRET') || 
        content.includes('JWT_SECRET') || 
        content.includes('jwtSecret')) {
      results.jwtSecretConfigured = true;
    }
    
    // Check for JWT expiration
    if (content.includes('expiresIn') || 
        content.includes('process.env.JWT_EXPIRATION') || 
        content.includes('JWT_EXPIRATION')) {
      results.jwtExpirationConfigured = true;
    }
    
    // Check for JWT algorithm
    if (content.includes('algorithm:') || 
        content.includes('algorithms:')) {
      results.jwtAlgorithmConfigured = true;
    }
    
    // Check for JWT verification
    if (content.includes('jwt.verify(') || content.includes('verify(')) {
      results.jwtVerificationConfigured = true;
    }
  }
}

// Check for environment variables
const envFile = path.join(__dirname, '..', '.env');
if (fs.existsSync(envFile)) {
  const envContent = fs.readFileSync(envFile, 'utf8');
  
  for (const envVar of envVarsToCheck) {
    if (!envContent.includes(envVar)) {
      results.issues.push(`Environment variable ${envVar} is not defined in .env file`);
    }
  }
}

// Generate report
let report = '# JWT Configuration Check\n\n';

if (!results.jwtFound) {
  report += '❌ jsonwebtoken is not installed\n';
} else {
  report += '✅ jsonwebtoken is installed\n';
}

if (!results.jwtImportFound) {
  report += '❌ jsonwebtoken import not found in any checked files\n';
} else {
  report += '✅ jsonwebtoken import found\n';
}

if (!results.jwtConfigured) {
  report += '❌ JWT signing is not configured\n';
} else {
  report += '✅ JWT signing is configured\n';
}

if (!results.jwtSecretConfigured) {
  report += '❌ JWT secret is not configured\n';
} else {
  report += '✅ JWT secret is configured\n';
}

if (!results.jwtExpirationConfigured) {
  report += '❌ JWT expiration is not configured\n';
} else {
  report += '✅ JWT expiration is configured\n';
}

if (!results.jwtAlgorithmConfigured) {
  report += '❌ JWT algorithm is not explicitly configured\n';
} else {
  report += '✅ JWT algorithm is explicitly configured\n';
}

if (!results.jwtVerificationConfigured) {
  report += '❌ JWT verification is not configured\n';
} else {
  report += '✅ JWT verification is configured\n';
}

if (results.issues.length > 0) {
  report += '\n## Issues\n\n';
  for (const issue of results.issues) {
    report += `- ${issue}\n`;
  }
}

// Recommended configuration
report += '\n## Recommended JWT Configuration\n\n';
report += '### Environment Variables\n\n';
report += '```\nJWT_SECRET=your-secure-secret-at-least-32-characters\n';
report += 'JWT_EXPIRATION=1h\n';
report += 'JWT_ISSUER=novafuse-uac\n';
report += 'JWT_AUDIENCE=novafuse-api\n```\n\n';

report += '### JWT Signing\n\n';
report += '```javascript\nconst jwt = require(\'jsonwebtoken\');\n\n';
report += 'const generateToken = (user) => {\n';
report += '  const payload = {\n';
report += '    sub: user.id,\n';
report += '    name: user.name,\n';
report += '    email: user.email,\n';
report += '    role: user.role,\n';
report += '    permissions: user.permissions\n';
report += '  };\n\n';
report += '  const options = {\n';
report += '    expiresIn: process.env.JWT_EXPIRATION || \'1h\',\n';
report += '    issuer: process.env.JWT_ISSUER || \'novafuse-uac\',\n';
report += '    audience: process.env.JWT_AUDIENCE || \'novafuse-api\',\n';
report += '    algorithm: \'HS256\'\n';
report += '  };\n\n';
report += '  return jwt.sign(payload, process.env.JWT_SECRET, options);\n';
report += '};\n```\n\n';

report += '### JWT Verification Middleware\n\n';
report += '```javascript\nconst jwt = require(\'jsonwebtoken\');\n\n';
report += 'const authenticateJWT = (req, res, next) => {\n';
report += '  const authHeader = req.headers.authorization;\n\n';
report += '  if (!authHeader || !authHeader.startsWith(\'Bearer \')) {\n';
report += '    return res.status(401).json({ error: \'Unauthorized\' });\n';
report += '  }\n\n';
report += '  const token = authHeader.split(\' \')[1];\n\n';
report += '  try {\n';
report += '    const options = {\n';
report += '      issuer: process.env.JWT_ISSUER || \'novafuse-uac\',\n';
report += '      audience: process.env.JWT_AUDIENCE || \'novafuse-api\',\n';
report += '      algorithms: [\'HS256\']\n';
report += '    };\n\n';
report += '    const decoded = jwt.verify(token, process.env.JWT_SECRET, options);\n';
report += '    req.user = decoded;\n';
report += '    next();\n';
report += '  } catch (error) {\n';
report += '    return res.status(401).json({ error: \'Invalid token\' });\n';
report += '  }\n';
report += '};\n```\n';

// Additional recommendations
report += '\n## Additional Recommendations\n\n';
report += '1. **Use a Strong Secret**: The JWT secret should be at least 32 characters long and randomly generated.\n\n';
report += '2. **Rotate Secrets**: Implement a mechanism to rotate JWT secrets periodically without invalidating existing tokens.\n\n';
report += '3. **Include Only Necessary Claims**: Only include necessary information in the JWT payload to minimize token size.\n\n';
report += '4. **Set Appropriate Expiration**: Set an appropriate expiration time based on your security requirements.\n\n';
report += '5. **Implement Refresh Tokens**: Use refresh tokens for obtaining new access tokens without requiring re-authentication.\n\n';
report += '6. **Blacklist Compromised Tokens**: Implement a token blacklist for invalidating compromised tokens.\n\n';
report += '7. **Use HTTPS**: Always use HTTPS to prevent token interception.\n\n';
report += '8. **Validate All Claims**: Validate all claims during token verification, including issuer, audience, and expiration.\n';

// Output report
console.log(report);

// Exit with appropriate code
if (!results.jwtFound || !results.jwtConfigured || !results.jwtSecretConfigured || 
    !results.jwtExpirationConfigured || !results.jwtVerificationConfigured) {
  process.exit(1);
} else {
  process.exit(0);
}
