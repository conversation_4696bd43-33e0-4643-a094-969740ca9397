/**
 * Health check script for NovaConnect UAC
 * 
 * This script is loaded via NODE_OPTIONS to provide health check functionality
 * in the distroless container environment.
 */

const http = require('http');
const process = require('process');

// Health check interval in milliseconds
const HEALTH_CHECK_INTERVAL = 30000;

// Health check timeout in milliseconds
const HEALTH_CHECK_TIMEOUT = 5000;

// Health check retries
const HEALTH_CHECK_RETRIES = 3;

// Health check port
const PORT = process.env.PORT || 3001;

// Health check path
const HEALTH_CHECK_PATH = '/health';

// Health check function
const checkHealth = () => {
  const options = {
    hostname: 'localhost',
    port: PORT,
    path: HEALTH_CHECK_PATH,
    method: 'GET',
    timeout: HEALTH_CHECK_TIMEOUT
  };

  const req = http.request(options, (res) => {
    if (res.statusCode !== 200) {
      console.error(`Health check failed with status code: ${res.statusCode}`);
      // Don't exit the process on failure, just log the error
    }
  });

  req.on('error', (error) => {
    console.error(`Health check error: ${error.message}`);
    // Don't exit the process on error, just log the error
  });

  req.on('timeout', () => {
    console.error('Health check timed out');
    req.destroy();
    // Don't exit the process on timeout, just log the error
  });

  req.end();
};

// Start health check after the application has started
setTimeout(() => {
  // Run health check at regular intervals
  setInterval(checkHealth, HEALTH_CHECK_INTERVAL);
}, 30000); // Wait 30 seconds before starting health checks
