@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900;
    @apply text-gray-100;
    @apply font-sans;
  }
}

@layer components {
  .glass-card {
    @apply bg-white/5;
    @apply backdrop-blur-lg;
    @apply rounded-2xl;
    @apply border;
    @apply border-white/10;
    @apply p-6;
  }

  .glass-button {
    @apply bg-purple-600;
    @apply text-white;
    @apply px-4;
    @apply py-2;
    @apply rounded-lg;
    @apply hover:bg-purple-700;
    @apply transition-colors;
  }

  .glass-input {
    @apply w-full;
    @apply px-4;
    @apply py-2;
    @apply rounded-lg;
    @apply border;
    @apply border-gray-300;
    @apply focus:outline-none;
    @apply focus:ring-2;
    @apply focus:ring-purple-500;
    @apply bg-gray-50;
  }
}
