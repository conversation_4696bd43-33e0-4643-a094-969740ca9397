"""
NovaFlowX (NUWO) - Universal Workflow Orchestrator.

A system for automating compliance workflows, evidence collection, and remediation actions.

Key Differentiation: Self-optimizing process routing
"""

# Import from legacy module for backward compatibility
try:
    from ucto.automation import <PERSON>mationManager, ScheduleTrigger, EventTrigger, CreateRequirementAction, CollectEvidenceAction
    has_automation = True
except ImportError:
    has_automation = False
    AutomationManager = None
    ScheduleTrigger = None
    EventTrigger = None
    CreateRequirementAction = None
    CollectEvidenceAction = None

__version__ = '0.1.0'
__all__ = []

# Add automation components to __all__ if available
if has_automation:
    __all__.extend(['AutomationManager', 'ScheduleTrigger', 'EventTrigger', 'CreateRequirementAction', 'CollectEvidenceAction'])
