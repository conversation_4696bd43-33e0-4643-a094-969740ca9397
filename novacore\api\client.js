/**
 * NovaCore API Client
 *
 * This client provides methods for interacting with the NovaCore API,
 * the foundation of the NovaFuse Cyber-Safety Platform.
 *
 * NovaCore embeds compliance, security, and trust directly into the API layer,
 * making Cyber-Safety inherent rather than bolted on.
 */

const axios = require('axios');

class NovaCoreClient {
  /**
   * Create a new NovaCore API client
   * @param {Object} options - Client options
   * @param {string} options.baseUrl - API base URL
   * @param {string} options.apiKey - API key
   * @param {number} options.timeout - Request timeout in milliseconds
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || 'http://localhost:3001';
    this.apiKey = options.apiKey;
    this.timeout = options.timeout || 30000;

    // Create axios instance
    this.client = axios.create({
      baseURL: this.baseUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-API-Key': this.apiKey
      }
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      response => response,
      error => {
        // Format error response
        const formattedError = {
          message: error.message,
          status: error.response?.status,
          data: error.response?.data
        };

        return Promise.reject(formattedError);
      }
    );
  }

  /**
   * Check API health
   * @returns {Promise<Object>} - Health status
   */
  async checkHealth() {
    const response = await this.client.get('/health');
    return response.data;
  }

  /**
   * Get API information
   * @returns {Promise<Object>} - API information
   */
  async getApiInfo() {
    const response = await this.client.get('/');
    return response.data;
  }

  // Evidence API methods

  /**
   * Create a new evidence record
   * @param {Object} data - Evidence data
   * @returns {Promise<Object>} - Created evidence
   */
  async createEvidence(data) {
    const response = await this.client.post('/api/evidence', data);
    return response.data;
  }

  /**
   * Get all evidence records
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Evidence records with pagination
   */
  async getAllEvidence(params = {}) {
    const response = await this.client.get('/api/evidence', { params });
    return response.data;
  }

  /**
   * Get evidence by ID
   * @param {string} id - Evidence ID
   * @returns {Promise<Object>} - Evidence record
   */
  async getEvidenceById(id) {
    const response = await this.client.get(`/api/evidence/${id}`);
    return response.data;
  }

  /**
   * Update evidence by ID
   * @param {string} id - Evidence ID
   * @param {Object} data - Updated evidence data
   * @returns {Promise<Object>} - Updated evidence
   */
  async updateEvidence(id, data) {
    const response = await this.client.put(`/api/evidence/${id}`, data);
    return response.data;
  }

  /**
   * Delete evidence by ID
   * @param {string} id - Evidence ID
   * @returns {Promise<Object>} - Deletion result
   */
  async deleteEvidence(id) {
    const response = await this.client.delete(`/api/evidence/${id}`);
    return response.data;
  }

  /**
   * Update evidence verification status
   * @param {string} id - Evidence ID
   * @param {Object} data - Verification data
   * @returns {Promise<Object>} - Updated evidence
   */
  async updateVerificationStatus(id, data) {
    const response = await this.client.patch(`/api/evidence/${id}/verification`, data);
    return response.data;
  }

  /**
   * Find evidence by framework and control
   * @param {string} framework - Framework name
   * @param {string} control - Control ID
   * @returns {Promise<Object>} - Evidence records
   */
  async findEvidenceByFrameworkAndControl(framework, control) {
    const response = await this.client.get(`/api/evidence/framework/${framework}/control/${control}`);
    return response.data;
  }

  /**
   * Find evidence by tags
   * @param {Array<string>} tags - Tags to search for
   * @returns {Promise<Object>} - Evidence records
   */
  async findEvidenceByTags(tags) {
    const tagsString = Array.isArray(tags) ? tags.join(',') : tags;
    const response = await this.client.get(`/api/evidence/tags`, { params: { tags: tagsString } });
    return response.data;
  }

  // Blockchain API methods

  /**
   * Verify evidence on blockchain
   * @param {string} evidenceId - Evidence ID
   * @returns {Promise<Object>} - Verification record
   */
  async verifyEvidenceOnBlockchain(evidenceId) {
    const response = await this.client.post(`/api/blockchain/evidence/${evidenceId}/verify`);
    return response.data;
  }

  /**
   * Check verification status
   * @param {string} id - Verification ID
   * @returns {Promise<Object>} - Verification status
   */
  async checkVerificationStatus(id) {
    const response = await this.client.get(`/api/blockchain/verification/${id}/status`);
    return response.data;
  }

  /**
   * Get verification by ID
   * @param {string} id - Verification ID
   * @returns {Promise<Object>} - Verification record
   */
  async getVerificationById(id) {
    const response = await this.client.get(`/api/blockchain/verification/${id}`);
    return response.data;
  }

  /**
   * Get verification by evidence ID
   * @param {string} evidenceId - Evidence ID
   * @returns {Promise<Object>} - Verification record
   */
  async getVerificationByEvidenceId(evidenceId) {
    const response = await this.client.get(`/api/blockchain/evidence/${evidenceId}/verification`);
    return response.data;
  }

  /**
   * Verify hash against blockchain
   * @param {string} hash - Hash to verify
   * @returns {Promise<Object>} - Verification result
   */
  async verifyHash(hash) {
    const response = await this.client.get(`/api/blockchain/verify/${hash}`);
    return response.data;
  }

  // Connector API methods

  /**
   * Create a new connector
   * @param {Object} data - Connector data
   * @returns {Promise<Object>} - Created connector
   */
  async createConnector(data) {
    const response = await this.client.post('/api/connectors', data);
    return response.data;
  }

  /**
   * Get all connectors
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Connectors with pagination
   */
  async getAllConnectors(params = {}) {
    const response = await this.client.get('/api/connectors', { params });
    return response.data;
  }

  /**
   * Get connector by ID
   * @param {string} id - Connector ID
   * @returns {Promise<Object>} - Connector
   */
  async getConnectorById(id) {
    const response = await this.client.get(`/api/connectors/${id}`);
    return response.data;
  }

  /**
   * Update connector by ID
   * @param {string} id - Connector ID
   * @param {Object} data - Updated connector data
   * @returns {Promise<Object>} - Updated connector
   */
  async updateConnector(id, data) {
    const response = await this.client.put(`/api/connectors/${id}`, data);
    return response.data;
  }

  /**
   * Delete connector by ID
   * @param {string} id - Connector ID
   * @returns {Promise<Object>} - Deletion result
   */
  async deleteConnector(id) {
    const response = await this.client.delete(`/api/connectors/${id}`);
    return response.data;
  }

  /**
   * Test connector connection
   * @param {string} id - Connector ID
   * @param {Object} credentials - Authentication credentials
   * @returns {Promise<Object>} - Test result
   */
  async testConnection(id, credentials) {
    const response = await this.client.post(`/api/connectors/${id}/test`, credentials);
    return response.data;
  }

  /**
   * Execute connector endpoint
   * @param {string} id - Connector ID
   * @param {string} endpointId - Endpoint ID
   * @param {Object} data - Request data
   * @returns {Promise<Object>} - Execution result
   */
  async executeEndpoint(id, endpointId, data) {
    const response = await this.client.post(`/api/connectors/${id}/endpoints/${endpointId}/execute`, data);
    return response.data;
  }

  /**
   * Find connectors by category
   * @param {string} category - Category name
   * @returns {Promise<Object>} - Connectors
   */
  async findConnectorsByCategory(category) {
    const response = await this.client.get(`/api/connectors/category/${category}`);
    return response.data;
  }

  /**
   * Find connectors by tags
   * @param {Array<string>} tags - Tags to search for
   * @returns {Promise<Object>} - Connectors
   */
  async findConnectorsByTags(tags) {
    const tagsString = Array.isArray(tags) ? tags.join(',') : tags;
    const response = await this.client.get(`/api/connectors/tags`, { params: { tags: tagsString } });
    return response.data;
  }

  /**
   * Find active connectors
   * @returns {Promise<Object>} - Active connectors
   */
  async findActiveConnectors() {
    const response = await this.client.get(`/api/connectors/status/active`);
    return response.data;
  }

  // Cyber-Safety API methods

  /**
   * Get safety score for an operation
   * @param {string} operationId - Operation ID
   * @returns {Promise<Object>} - Safety score
   */
  async getSafetyScore(operationId) {
    const response = await this.client.get(`/api/cyber-safety/score/${operationId}`);
    return response.data;
  }

  /**
   * Verify safety of an operation
   * @param {string} operationId - Operation ID
   * @returns {Promise<Object>} - Verification result
   */
  async verifySafety(operationId) {
    const response = await this.client.get(`/api/cyber-safety/verify/${operationId}`);
    return response.data;
  }

  /**
   * Assess risk for an operation
   * @param {string} operationId - Operation ID
   * @returns {Promise<Object>} - Risk assessment
   */
  async assessRisk(operationId) {
    const response = await this.client.get(`/api/cyber-safety/risk/${operationId}`);
    return response.data;
  }

  /**
   * Map operation to compliance frameworks
   * @param {string} operationId - Operation ID
   * @returns {Promise<Object>} - Compliance mapping
   */
  async mapToCompliance(operationId) {
    const response = await this.client.get(`/api/cyber-safety/compliance/${operationId}`);
    return response.data;
  }

  /**
   * Get safety policies
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Safety policies
   */
  async getSafetyPolicies(params = {}) {
    const response = await this.client.get('/api/cyber-safety/policies', { params });
    return response.data;
  }

  /**
   * Create safety policy
   * @param {Object} data - Policy data
   * @returns {Promise<Object>} - Created policy
   */
  async createSafetyPolicy(data) {
    const response = await this.client.post('/api/cyber-safety/policies', data);
    return response.data;
  }

  /**
   * Update safety policy
   * @param {string} id - Policy ID
   * @param {Object} data - Updated policy data
   * @returns {Promise<Object>} - Updated policy
   */
  async updateSafetyPolicy(id, data) {
    const response = await this.client.put(`/api/cyber-safety/policies/${id}`, data);
    return response.data;
  }

  /**
   * Delete safety policy
   * @param {string} id - Policy ID
   * @returns {Promise<Object>} - Deletion result
   */
  async deleteSafetyPolicy(id) {
    const response = await this.client.delete(`/api/cyber-safety/policies/${id}`);
    return response.data;
  }
}

module.exports = NovaCoreClient;
