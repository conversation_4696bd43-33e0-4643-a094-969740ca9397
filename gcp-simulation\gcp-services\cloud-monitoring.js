/**
 * Cloud Monitoring Simulator
 * 
 * This file simulates the Google Cloud Monitoring service for the NovaFuse GCP simulation.
 * It provides endpoints for metrics, alerts, and compliance monitoring.
 */

const express = require('express');
const cors = require('cors');
const morgan = require('morgan');

// Create Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));

// In-memory storage for metrics, alerts, and dashboards
const monitoring = {
  metrics: {
    'compliance/score': {
      name: 'compliance/score',
      displayName: 'Compliance Score',
      description: 'Overall compliance score across all frameworks',
      type: 'GAUGE',
      unit: 'percent',
      metricKind: 'GAUGE',
      valueType: 'DOUBLE',
      points: [
        { timestamp: '2023-06-01T00:00:00Z', value: 82 },
        { timestamp: '2023-06-08T00:00:00Z', value: 85 },
        { timestamp: '2023-06-15T00:00:00Z', value: 87 },
        { timestamp: '2023-06-22T00:00:00Z', value: 90 },
        { timestamp: '2023-06-29T00:00:00Z', value: 92 }
      ]
    },
    'compliance/framework/gdpr': {
      name: 'compliance/framework/gdpr',
      displayName: 'GDPR Compliance Score',
      description: 'Compliance score for GDPR framework',
      type: 'GAUGE',
      unit: 'percent',
      metricKind: 'GAUGE',
      valueType: 'DOUBLE',
      points: [
        { timestamp: '2023-06-01T00:00:00Z', value: 80 },
        { timestamp: '2023-06-08T00:00:00Z', value: 82 },
        { timestamp: '2023-06-15T00:00:00Z', value: 85 },
        { timestamp: '2023-06-22T00:00:00Z', value: 87 },
        { timestamp: '2023-06-29T00:00:00Z', value: 87 }
      ]
    },
    'compliance/framework/hipaa': {
      name: 'compliance/framework/hipaa',
      displayName: 'HIPAA Compliance Score',
      description: 'Compliance score for HIPAA framework',
      type: 'GAUGE',
      unit: 'percent',
      metricKind: 'GAUGE',
      valueType: 'DOUBLE',
      points: [
        { timestamp: '2023-06-01T00:00:00Z', value: 85 },
        { timestamp: '2023-06-08T00:00:00Z', value: 88 },
        { timestamp: '2023-06-15T00:00:00Z', value: 90 },
        { timestamp: '2023-06-22T00:00:00Z', value: 92 },
        { timestamp: '2023-06-29T00:00:00Z', value: 92 }
      ]
    },
    'compliance/framework/pci-dss': {
      name: 'compliance/framework/pci-dss',
      displayName: 'PCI-DSS Compliance Score',
      description: 'Compliance score for PCI-DSS framework',
      type: 'GAUGE',
      unit: 'percent',
      metricKind: 'GAUGE',
      valueType: 'DOUBLE',
      points: [
        { timestamp: '2023-06-01T00:00:00Z', value: 78 },
        { timestamp: '2023-06-08T00:00:00Z', value: 82 },
        { timestamp: '2023-06-15T00:00:00Z', value: 85 },
        { timestamp: '2023-06-22T00:00:00Z', value: 88 },
        { timestamp: '2023-06-29T00:00:00Z', value: 88 }
      ]
    },
    'compliance/framework/soc2': {
      name: 'compliance/framework/soc2',
      displayName: 'SOC2 Compliance Score',
      description: 'Compliance score for SOC2 framework',
      type: 'GAUGE',
      unit: 'percent',
      metricKind: 'GAUGE',
      valueType: 'DOUBLE',
      points: [
        { timestamp: '2023-06-01T00:00:00Z', value: 84 },
        { timestamp: '2023-06-08T00:00:00Z', value: 86 },
        { timestamp: '2023-06-15T00:00:00Z', value: 88 },
        { timestamp: '2023-06-22T00:00:00Z', value: 90 },
        { timestamp: '2023-06-29T00:00:00Z', value: 90 }
      ]
    },
    'security/findings/count': {
      name: 'security/findings/count',
      displayName: 'Security Findings Count',
      description: 'Count of security findings by severity',
      type: 'GAUGE',
      unit: 'count',
      metricKind: 'GAUGE',
      valueType: 'INT64',
      labels: ['severity'],
      points: [
        { timestamp: '2023-06-29T00:00:00Z', value: 5, labels: { severity: 'CRITICAL' } },
        { timestamp: '2023-06-29T00:00:00Z', value: 12, labels: { severity: 'HIGH' } },
        { timestamp: '2023-06-29T00:00:00Z', value: 28, labels: { severity: 'MEDIUM' } },
        { timestamp: '2023-06-29T00:00:00Z', value: 45, labels: { severity: 'LOW' } }
      ]
    },
    'security/remediation/time': {
      name: 'security/remediation/time',
      displayName: 'Mean Time to Remediate',
      description: 'Average time to remediate security findings by severity',
      type: 'GAUGE',
      unit: 'hours',
      metricKind: 'GAUGE',
      valueType: 'DOUBLE',
      labels: ['severity'],
      points: [
        { timestamp: '2023-06-29T00:00:00Z', value: 4.5, labels: { severity: 'CRITICAL' } },
        { timestamp: '2023-06-29T00:00:00Z', value: 24.2, labels: { severity: 'HIGH' } },
        { timestamp: '2023-06-29T00:00:00Z', value: 72.8, labels: { severity: 'MEDIUM' } },
        { timestamp: '2023-06-29T00:00:00Z', value: 168.5, labels: { severity: 'LOW' } }
      ]
    },
    'compliance/controls/status': {
      name: 'compliance/controls/status',
      displayName: 'Compliance Controls Status',
      description: 'Count of compliance controls by status',
      type: 'GAUGE',
      unit: 'count',
      metricKind: 'GAUGE',
      valueType: 'INT64',
      labels: ['status'],
      points: [
        { timestamp: '2023-06-29T00:00:00Z', value: 156, labels: { status: 'PASS' } },
        { timestamp: '2023-06-29T00:00:00Z', value: 24, labels: { status: 'PARTIAL' } },
        { timestamp: '2023-06-29T00:00:00Z', value: 12, labels: { status: 'FAIL' } }
      ]
    },
    'compliance/evidence/count': {
      name: 'compliance/evidence/count',
      displayName: 'Compliance Evidence Count',
      description: 'Count of compliance evidence records by type',
      type: 'GAUGE',
      unit: 'count',
      metricKind: 'GAUGE',
      valueType: 'INT64',
      labels: ['type'],
      points: [
        { timestamp: '2023-06-29T00:00:00Z', value: 45, labels: { type: 'IAM_POLICY' } },
        { timestamp: '2023-06-29T00:00:00Z', value: 78, labels: { type: 'AUDIT_LOG' } },
        { timestamp: '2023-06-29T00:00:00Z', value: 32, labels: { type: 'STORAGE_POLICY' } },
        { timestamp: '2023-06-29T00:00:00Z', value: 24, labels: { type: 'KMS_CONFIG' } },
        { timestamp: '2023-06-29T00:00:00Z', value: 18, labels: { type: 'FIREWALL_CONFIG' } }
      ]
    }
  },
  alertPolicies: {
    'compliance-score-drop': {
      name: 'compliance-score-drop',
      displayName: 'Compliance Score Drop',
      description: 'Alert when compliance score drops by more than 5%',
      enabled: true,
      conditions: [
        {
          name: 'compliance-score-condition',
          displayName: 'Compliance Score Condition',
          conditionThreshold: {
            filter: 'metric.type="custom.googleapis.com/compliance/score"',
            comparison: 'COMPARISON_LT',
            thresholdValue: -5,
            duration: '0s',
            trigger: {
              count: 1
            }
          }
        }
      ],
      alertStrategy: {
        notificationRate: {
          period: '300s'
        },
        autoClose: '86400s'
      },
      notificationChannels: [
        '<EMAIL>',
        'slack-compliance-alerts'
      ],
      documentation: {
        content: 'This alert triggers when the overall compliance score drops by more than 5% compared to the previous measurement. This could indicate a significant compliance issue that requires immediate attention.',
        mimeType: 'text/markdown'
      },
      userLabels: {
        severity: 'critical',
        category: 'compliance'
      }
    },
    'critical-finding-detected': {
      name: 'critical-finding-detected',
      displayName: 'Critical Security Finding Detected',
      description: 'Alert when a critical security finding is detected',
      enabled: true,
      conditions: [
        {
          name: 'critical-finding-condition',
          displayName: 'Critical Finding Condition',
          conditionThreshold: {
            filter: 'metric.type="custom.googleapis.com/security/findings/count" AND metric.labels.severity="CRITICAL"',
            comparison: 'COMPARISON_GT',
            thresholdValue: 0,
            duration: '0s',
            trigger: {
              count: 1
            }
          }
        }
      ],
      alertStrategy: {
        notificationRate: {
          period: '60s'
        },
        autoClose: '0s'
      },
      notificationChannels: [
        '<EMAIL>',
        'slack-security-alerts'
      ],
      documentation: {
        content: 'This alert triggers when a critical security finding is detected. Critical findings require immediate attention and remediation.',
        mimeType: 'text/markdown'
      },
      userLabels: {
        severity: 'critical',
        category: 'security'
      }
    },
    'compliance-control-failure': {
      name: 'compliance-control-failure',
      displayName: 'Compliance Control Failure',
      description: 'Alert when a compliance control fails',
      enabled: true,
      conditions: [
        {
          name: 'control-failure-condition',
          displayName: 'Control Failure Condition',
          conditionThreshold: {
            filter: 'metric.type="custom.googleapis.com/compliance/controls/status" AND metric.labels.status="FAIL"',
            comparison: 'COMPARISON_GT',
            thresholdValue: 10,
            duration: '0s',
            trigger: {
              count: 1
            }
          }
        }
      ],
      alertStrategy: {
        notificationRate: {
          period: '300s'
        },
        autoClose: '86400s'
      },
      notificationChannels: [
        '<EMAIL>',
        'slack-compliance-alerts'
      ],
      documentation: {
        content: 'This alert triggers when the number of failed compliance controls exceeds 10. This could indicate a significant compliance issue that requires attention.',
        mimeType: 'text/markdown'
      },
      userLabels: {
        severity: 'high',
        category: 'compliance'
      }
    }
  },
  dashboards: {
    'compliance-overview': {
      name: 'compliance-overview',
      displayName: 'Compliance Overview',
      description: 'Overview of compliance status across all frameworks',
      widgets: [
        {
          title: 'Overall Compliance Score',
          type: 'GAUGE',
          gauge: {
            metric: 'compliance/score',
            min: 0,
            max: 100,
            thresholds: [
              { value: 60, color: 'RED' },
              { value: 80, color: 'YELLOW' },
              { value: 90, color: 'GREEN' }
            ]
          }
        },
        {
          title: 'Compliance Score Trend',
          type: 'LINE',
          line: {
            metrics: ['compliance/score'],
            timeRange: '30d'
          }
        },
        {
          title: 'Framework Compliance Scores',
          type: 'BAR',
          bar: {
            metrics: [
              'compliance/framework/gdpr',
              'compliance/framework/hipaa',
              'compliance/framework/pci-dss',
              'compliance/framework/soc2'
            ]
          }
        },
        {
          title: 'Control Status',
          type: 'PIE',
          pie: {
            metric: 'compliance/controls/status',
            groupBy: 'status'
          }
        }
      ]
    },
    'security-posture': {
      name: 'security-posture',
      displayName: 'Security Posture',
      description: 'Overview of security posture and findings',
      widgets: [
        {
          title: 'Security Findings by Severity',
          type: 'PIE',
          pie: {
            metric: 'security/findings/count',
            groupBy: 'severity'
          }
        },
        {
          title: 'Mean Time to Remediate',
          type: 'BAR',
          bar: {
            metric: 'security/remediation/time',
            groupBy: 'severity'
          }
        },
        {
          title: 'Critical Findings Trend',
          type: 'LINE',
          line: {
            metrics: ['security/findings/count'],
            filter: 'metric.labels.severity="CRITICAL"',
            timeRange: '30d'
          }
        }
      ]
    },
    'evidence-management': {
      name: 'evidence-management',
      displayName: 'Evidence Management',
      description: 'Overview of compliance evidence collection and management',
      widgets: [
        {
          title: 'Evidence Records by Type',
          type: 'PIE',
          pie: {
            metric: 'compliance/evidence/count',
            groupBy: 'type'
          }
        },
        {
          title: 'Evidence Collection Trend',
          type: 'LINE',
          line: {
            metrics: ['compliance/evidence/count'],
            timeRange: '30d'
          }
        }
      ]
    }
  },
  serviceMapping: {
    services: [
      {
        name: 'BigQuery',
        regions: ['us-east1', 'us-central1', 'europe-west1'],
        complianceImpact: [
          { framework: 'SOC2', controls: ['CC7.2', 'CC6.1'], severity: 'HIGH' },
          { framework: 'GDPR', controls: ['art5-1e', 'art32-1b'], severity: 'MEDIUM' },
          { framework: 'HIPAA', controls: ['164.312(a)(2)(ii)'], severity: 'HIGH' }
        ],
        slaThreshold: 99.9,
        criticalOperations: ['query', 'dataExport', 'auditLogGeneration']
      },
      {
        name: 'Cloud Storage',
        regions: ['us-east1', 'us-central1', 'europe-west1'],
        complianceImpact: [
          { framework: 'PCI-DSS', controls: ['3.4', '10.5'], severity: 'CRITICAL' },
          { framework: 'GDPR', controls: ['art5-1f', 'art32-1a'], severity: 'HIGH' },
          { framework: 'HIPAA', controls: ['164.312(a)(2)(iv)'], severity: 'CRITICAL' }
        ],
        slaThreshold: 99.95,
        criticalOperations: ['objectRetrieval', 'objectStorage', 'accessControl']
      },
      {
        name: 'Cloud Functions',
        regions: ['us-east1', 'us-central1', 'europe-west1'],
        complianceImpact: [
          { framework: 'SOC2', controls: ['CC8.1'], severity: 'MEDIUM' },
          { framework: 'GDPR', controls: ['art32-1b'], severity: 'MEDIUM' },
          { framework: 'HIPAA', controls: ['164.308(a)(1)(ii)(D)'], severity: 'MEDIUM' }
        ],
        slaThreshold: 99.5,
        criticalOperations: ['functionExecution', 'eventProcessing']
      }
    ],
    alertPolicies: [
      {
        name: 'SOC2ComplianceAlert',
        condition: 'service.sla < mapping.slaThreshold AND mapping.complianceImpact.framework = "SOC2"',
        notificationChannels: ['<EMAIL>', '<EMAIL>'],
        remediationSteps: [
          'Document the incident in the compliance management system',
          'Assess the impact on SOC2 controls',
          'Implement compensating controls if necessary',
          'Prepare documentation for auditors'
        ]
      }
    ]
  }
};

/**
 * @route GET /monitoring/metrics
 * @description List all metrics
 * @access Private
 */
app.get('/monitoring/metrics', (req, res) => {
  const metrics = Object.values(monitoring.metrics).map(metric => ({
    name: metric.name,
    displayName: metric.displayName,
    description: metric.description,
    type: metric.type,
    unit: metric.unit
  }));
  
  res.json({
    metrics
  });
});

/**
 * @route GET /monitoring/metrics/:name
 * @description Get a specific metric
 * @access Private
 */
app.get('/monitoring/metrics/:name', (req, res) => {
  const { name } = req.params;
  
  if (!monitoring.metrics[name]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Metric ${name} not found`
      }
    });
  }
  
  res.json({
    metric: monitoring.metrics[name]
  });
});

/**
 * @route GET /monitoring/metrics/:name/data
 * @description Get data for a specific metric
 * @access Private
 */
app.get('/monitoring/metrics/:name/data', (req, res) => {
  const { name } = req.params;
  const { startTime, endTime, filter } = req.query;
  
  if (!monitoring.metrics[name]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Metric ${name} not found`
      }
    });
  }
  
  const metric = monitoring.metrics[name];
  let points = [...metric.points];
  
  // Filter by time range if specified
  if (startTime) {
    const startTimestamp = new Date(startTime).getTime();
    points = points.filter(point => new Date(point.timestamp).getTime() >= startTimestamp);
  }
  
  if (endTime) {
    const endTimestamp = new Date(endTime).getTime();
    points = points.filter(point => new Date(point.timestamp).getTime() <= endTimestamp);
  }
  
  // Filter by labels if specified
  if (filter && metric.labels) {
    // Simple filter parsing (e.g., 'severity=CRITICAL')
    const filterParts = filter.split('=');
    if (filterParts.length === 2) {
      const [key, value] = filterParts;
      points = points.filter(point => point.labels && point.labels[key] === value.replace(/"/g, ''));
    }
  }
  
  res.json({
    metric: {
      name: metric.name,
      displayName: metric.displayName,
      description: metric.description,
      type: metric.type,
      unit: metric.unit
    },
    points
  });
});

/**
 * @route POST /monitoring/metrics/:name/data
 * @description Add data point for a specific metric
 * @access Private
 */
app.post('/monitoring/metrics/:name/data', (req, res) => {
  const { name } = req.params;
  const { value, timestamp, labels } = req.body;
  
  if (!monitoring.metrics[name]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Metric ${name} not found`
      }
    });
  }
  
  if (value === undefined) {
    return res.status(400).json({
      error: {
        code: 400,
        message: 'Value is required'
      }
    });
  }
  
  const now = new Date().toISOString();
  const point = {
    timestamp: timestamp || now,
    value
  };
  
  if (labels) {
    point.labels = labels;
  }
  
  monitoring.metrics[name].points.push(point);
  
  res.status(201).json({
    point
  });
});

/**
 * @route GET /monitoring/alertPolicies
 * @description List all alert policies
 * @access Private
 */
app.get('/monitoring/alertPolicies', (req, res) => {
  const alertPolicies = Object.values(monitoring.alertPolicies).map(policy => ({
    name: policy.name,
    displayName: policy.displayName,
    description: policy.description,
    enabled: policy.enabled
  }));
  
  res.json({
    alertPolicies
  });
});

/**
 * @route GET /monitoring/alertPolicies/:name
 * @description Get a specific alert policy
 * @access Private
 */
app.get('/monitoring/alertPolicies/:name', (req, res) => {
  const { name } = req.params;
  
  if (!monitoring.alertPolicies[name]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Alert policy ${name} not found`
      }
    });
  }
  
  res.json({
    alertPolicy: monitoring.alertPolicies[name]
  });
});

/**
 * @route POST /monitoring/alertPolicies
 * @description Create a new alert policy
 * @access Private
 */
app.post('/monitoring/alertPolicies', (req, res) => {
  const { name, displayName, description, conditions, notificationChannels } = req.body;
  
  if (!name || !displayName || !conditions) {
    return res.status(400).json({
      error: {
        code: 400,
        message: 'Name, displayName, and conditions are required'
      }
    });
  }
  
  if (monitoring.alertPolicies[name]) {
    return res.status(409).json({
      error: {
        code: 409,
        message: `Alert policy ${name} already exists`
      }
    });
  }
  
  monitoring.alertPolicies[name] = {
    name,
    displayName,
    description: description || '',
    enabled: true,
    conditions,
    alertStrategy: {
      notificationRate: {
        period: '300s'
      },
      autoClose: '86400s'
    },
    notificationChannels: notificationChannels || [],
    documentation: {
      content: description || '',
      mimeType: 'text/markdown'
    },
    userLabels: {
      severity: 'medium',
      category: 'custom'
    }
  };
  
  res.status(201).json({
    alertPolicy: monitoring.alertPolicies[name]
  });
});

/**
 * @route GET /monitoring/dashboards
 * @description List all dashboards
 * @access Private
 */
app.get('/monitoring/dashboards', (req, res) => {
  const dashboards = Object.values(monitoring.dashboards).map(dashboard => ({
    name: dashboard.name,
    displayName: dashboard.displayName,
    description: dashboard.description
  }));
  
  res.json({
    dashboards
  });
});

/**
 * @route GET /monitoring/dashboards/:name
 * @description Get a specific dashboard
 * @access Private
 */
app.get('/monitoring/dashboards/:name', (req, res) => {
  const { name } = req.params;
  
  if (!monitoring.dashboards[name]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Dashboard ${name} not found`
      }
    });
  }
  
  res.json({
    dashboard: monitoring.dashboards[name]
  });
});

/**
 * @route GET /monitoring/serviceMapping
 * @description Get service to compliance mapping
 * @access Private
 */
app.get('/monitoring/serviceMapping', (req, res) => {
  res.json({
    serviceMapping: monitoring.serviceMapping
  });
});

/**
 * @route GET /monitoring/serviceMapping/:service
 * @description Get compliance mapping for a specific service
 * @access Private
 */
app.get('/monitoring/serviceMapping/:service', (req, res) => {
  const { service } = req.params;
  
  const serviceMapping = monitoring.serviceMapping.services.find(s => s.name === service);
  
  if (!serviceMapping) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Service ${service} not found`
      }
    });
  }
  
  res.json({
    service: serviceMapping
  });
});

/**
 * @route POST /monitoring/incidents
 * @description Simulate a service incident
 * @access Private
 */
app.post('/monitoring/incidents', (req, res) => {
  const { service, region, severity, description } = req.body;
  
  if (!service || !region || !severity) {
    return res.status(400).json({
      error: {
        code: 400,
        message: 'Service, region, and severity are required'
      }
    });
  }
  
  // Find the service mapping
  const serviceMapping = monitoring.serviceMapping.services.find(s => s.name === service);
  
  if (!serviceMapping) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Service ${service} not found`
      }
    });
  }
  
  // Check if the region is valid for the service
  if (!serviceMapping.regions.includes(region)) {
    return res.status(400).json({
      error: {
        code: 400,
        message: `Region ${region} is not valid for service ${service}`
      }
    });
  }
  
  // Determine compliance impact
  const complianceImpact = serviceMapping.complianceImpact.filter(impact => {
    return impact.severity === severity;
  });
  
  // Create the incident
  const incident = {
    id: `incident-${Date.now()}`,
    service,
    region,
    severity,
    description: description || `Simulated incident for ${service} in ${region}`,
    startTime: new Date().toISOString(),
    endTime: null,
    status: 'OPEN',
    complianceImpact
  };
  
  // Return the incident
  res.status(201).json({
    incident
  });
});

// Start server
const PORT = process.env.CLOUD_MONITORING_PORT || 8086;
app.listen(PORT, () => {
  console.log(`Cloud Monitoring simulator running on port ${PORT}`);
});

module.exports = app;
