import { useState } from "react";
import Head from "next/head";
import Link from "next/link";

export default function Pricing() {
  const [billingPeriod, setBillingPeriod] = useState("monthly");
  const [showEnterprise, setShowEnterprise] = useState(false);

  const plans = {
    monthly: [
      {
        name: "Basic",
        price: "$49",
        description: "Perfect for startups and small businesses",
        features: [
          "5 API connectors",
          "10,000 API calls per month",
          "Basic support",
          "Standard SLA",
          "Community access"
        ],
        cta: "Get Started",
        popular: false
      },
      {
        name: "Pro",
        price: "$149",
        description: "For growing businesses with advanced needs",
        features: [
          "20 API connectors",
          "100,000 API calls per month",
          "Priority support",
          "Enhanced SLA",
          "Advanced analytics",
          "Custom connectors"
        ],
        cta: "Get Started",
        popular: true
      },
      {
        name: "Enterprise",
        price: "Custom",
        description: "For large organizations with complex requirements",
        features: [
          "Unlimited API connectors",
          "Unlimited API calls",
          "24/7 dedicated support",
          "Custom SLA",
          "Advanced security features",
          "Custom integrations",
          "Dedicated account manager"
        ],
        cta: "Contact Sales",
        popular: false
      }
    ],
    annual: [
      {
        name: "Basic",
        price: "$39",
        description: "Perfect for startups and small businesses",
        features: [
          "5 API connectors",
          "10,000 API calls per month",
          "Basic support",
          "Standard SLA",
          "Community access"
        ],
        cta: "Get Started",
        popular: false,
        savings: "Save $120/year"
      },
      {
        name: "Pro",
        price: "$119",
        description: "For growing businesses with advanced needs",
        features: [
          "20 API connectors",
          "100,000 API calls per month",
          "Priority support",
          "Enhanced SLA",
          "Advanced analytics",
          "Custom connectors"
        ],
        cta: "Get Started",
        popular: true,
        savings: "Save $360/year"
      },
      {
        name: "Enterprise",
        price: "Custom",
        description: "For large organizations with complex requirements",
        features: [
          "Unlimited API connectors",
          "Unlimited API calls",
          "24/7 dedicated support",
          "Custom SLA",
          "Advanced security features",
          "Custom integrations",
          "Dedicated account manager"
        ],
        cta: "Contact Sales",
        popular: false
      }
    ]
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>NovaFuse Pricing</title>
        <meta name="description" content="NovaFuse API Superstore pricing plans" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl sm:tracking-tight lg:text-6xl">
            Simple, transparent pricing
          </h1>
          <p className="mt-5 max-w-xl mx-auto text-xl text-gray-500">
            Choose the plan that's right for your business
          </p>

          {/* Billing period toggle */}
          <div className="mt-8 flex justify-center">
            <div className="relative bg-gray-100 p-1 rounded-lg inline-flex">
              <button
                type="button"
                className={`${
                  billingPeriod === "monthly"
                    ? "bg-white shadow-sm"
                    : "bg-transparent"
                } relative py-2 px-6 border-transparent rounded-md text-sm font-medium whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-blue-500 focus:z-10`}
                onClick={() => setBillingPeriod("monthly")}
              >
                Monthly
              </button>
              <button
                type="button"
                className={`${
                  billingPeriod === "annual"
                    ? "bg-white shadow-sm"
                    : "bg-transparent"
                } relative py-2 px-6 border-transparent rounded-md text-sm font-medium whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-blue-500 focus:z-10`}
                onClick={() => setBillingPeriod("annual")}
              >
                Annual <span className="text-blue-600 font-semibold">Save 20%</span>
              </button>
            </div>
          </div>
        </div>

        {/* Pricing cards */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3 lg:gap-8 mb-12">
          {plans[billingPeriod].map((plan, index) => (
            <div
              key={plan.name}
              className={`bg-white rounded-lg shadow-lg overflow-hidden ${
                plan.popular ? "ring-2 ring-blue-500" : ""
              }`}
            >
              {plan.popular && (
                <div className="bg-blue-500 text-white text-center py-2 text-sm font-semibold">
                  MOST POPULAR
                </div>
              )}
              <div className="px-6 py-8">
                <h2 className="text-2xl font-bold text-gray-900">{plan.name}</h2>
                <p className="mt-4 text-gray-500">{plan.description}</p>
                <p className="mt-8">
                  <span className="text-4xl font-extrabold text-gray-900">{plan.price}</span>
                  {plan.price !== "Custom" && (
                    <span className="text-base font-medium text-gray-500">
                      {billingPeriod === "monthly" ? "/month" : "/month, billed annually"}
                    </span>
                  )}
                </p>
                {plan.savings && (
                  <p className="mt-2 text-sm text-green-600 font-medium">{plan.savings}</p>
                )}
                <ul className="mt-8 space-y-4">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <div className="flex-shrink-0">
                        <svg
                          className="h-6 w-6 text-green-500"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      </div>
                      <p className="ml-3 text-base text-gray-700">{feature}</p>
                    </li>
                  ))}
                </ul>
                <div className="mt-8">
                  <Link
                    href={plan.name === "Enterprise" ? "/contact-sales" : "/signup"}
                    className={`block w-full ${
                      plan.popular ? "bg-blue-600 hover:bg-blue-700" : "bg-gray-600 hover:bg-gray-700"
                    } border border-transparent rounded-md py-3 px-5 text-center font-medium text-white`}
                  >
                    {plan.cta}
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Enterprise section */}
        <div className="bg-blue-900 rounded-lg shadow-inner p-8 mb-12">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white">
              Need a custom solution?
            </h2>
            <p className="mt-4 text-lg text-blue-100">
              We offer tailored enterprise solutions for organizations with complex requirements.
            </p>
          </div>

          <div className="max-w-3xl mx-auto">
            <button
              onClick={() => setShowEnterprise(!showEnterprise)}
              className="flex justify-between items-center w-full text-left"
            >
              <h3 className="text-xl font-semibold text-blue-100">
                Enterprise features
              </h3>
              <svg
                className={`h-6 w-6 text-blue-100 transform ${
                  showEnterprise ? "rotate-180" : ""
                }`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {showEnterprise && (
              <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="bg-white rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900">
                    Dedicated Infrastructure
                  </h4>
                  <p className="mt-2 text-sm text-gray-600">
                    Isolated, dedicated infrastructure for enhanced security and performance.
                  </p>
                </div>
                <div className="bg-white rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900">
                    Custom SLAs
                  </h4>
                  <p className="mt-2 text-sm text-gray-600">
                    Tailored service level agreements to meet your specific requirements.
                  </p>
                </div>
                <div className="bg-white rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900">
                    Advanced Security
                  </h4>
                  <p className="mt-2 text-sm text-gray-600">
                    Enhanced security features including custom encryption and VPC peering.
                  </p>
                </div>
                <div className="bg-white rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900">
                    24/7 Support
                  </h4>
                  <p className="mt-2 text-sm text-gray-600">
                    Round-the-clock support with dedicated account management.
                  </p>
                </div>
                <div className="bg-white rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900">
                    Custom Integrations
                  </h4>
                  <p className="mt-2 text-sm text-gray-600">
                    Bespoke integrations with your existing systems and workflows.
                  </p>
                </div>
                <div className="bg-white rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900">
                    Advanced Analytics
                  </h4>
                  <p className="mt-2 text-sm text-gray-600">
                    Comprehensive analytics and reporting tailored to your business needs.
                  </p>
                </div>
              </div>
            )}

            <div className="mt-8 text-center">
              <Link
                href="/contact-sales"
                className="inline-flex items-center px-6 py-3 border border-blue-100 text-base font-medium rounded-md shadow-sm text-blue-900 bg-white hover:bg-blue-50"
              >
                Contact our sales team
              </Link>
            </div>
          </div>
        </div>

        {/* FAQ section */}
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">
            Frequently asked questions
          </h2>

          <div className="space-y-6">
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <button
                className="w-full px-6 py-4 text-left focus:outline-none"
                onClick={() => {}}
              >
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">
                    How are API calls counted?
                  </h3>
                  <svg
                    className="h-6 w-6 text-gray-500"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
                <div className="mt-2 text-base text-gray-500">
                  An API call is counted each time you make a request to one of our API endpoints. Batch operations count as multiple calls based on the number of items processed.
                </div>
              </button>
            </div>

            <div className="bg-white shadow rounded-lg overflow-hidden">
              <button
                className="w-full px-6 py-4 text-left focus:outline-none"
                onClick={() => {}}
              >
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">
                    What happens if I exceed my plan limits?
                  </h3>
                  <svg
                    className="h-6 w-6 text-gray-500"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
                <div className="mt-2 text-base text-gray-500">
                  If you exceed your plan limits, you'll be charged for additional usage at our standard overage rates. You can also upgrade to a higher plan at any time.
                </div>
              </button>
            </div>

            <div className="bg-white shadow rounded-lg overflow-hidden">
              <button
                className="w-full px-6 py-4 text-left focus:outline-none"
                onClick={() => {}}
              >
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">
                    Can I change plans at any time?
                  </h3>
                  <svg
                    className="h-6 w-6 text-gray-500"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
                <div className="mt-2 text-base text-gray-500">
                  Yes, you can upgrade your plan at any time. Downgrades take effect at the end of your current billing cycle.
                </div>
              </button>
            </div>

            <div className="bg-white shadow rounded-lg overflow-hidden">
              <button
                className="w-full px-6 py-4 text-left focus:outline-none"
                onClick={() => {}}
              >
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">
                    Do you offer a free trial?
                  </h3>
                  <svg
                    className="h-6 w-6 text-gray-500"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
                <div className="mt-2 text-base text-gray-500">
                  Yes, we offer a 14-day free trial for all plans. No credit card required.
                </div>
              </button>
            </div>
          </div>

          <div className="mt-8 text-center">
            <p className="text-base text-gray-600">
              Have more questions?{" "}
              <Link
                href="/support"
                className="text-blue-600 font-medium hover:text-blue-500"
              >
                Contact our support team
              </Link>
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
