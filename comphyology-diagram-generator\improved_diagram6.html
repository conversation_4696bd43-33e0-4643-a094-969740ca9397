<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>6. Meta-Field Schema</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1000px;
            height: 800px;
            position: relative;
            border: 2px solid black;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
        }
        .element {
            position: absolute;
            border-radius: 0; /* Square corners for patent diagrams */
            padding: 20px 20px 20px 50px; /* Extra padding on left for number */
            z-index: 2;
            background-color: white;
            border: 2px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
        .dashed {
            border-top: 2px dashed black;
            height: 0;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .bold-formula {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>6. Meta-Field Schema</h1>

    <div class="diagram-container">
        <!-- Meta-Field Schema -->
        <div class="element" style="top: 50px; left: 300px; width: 400px; font-weight: bold; font-size: 20px;">
            Meta-Field Schema
            <div class="element-number">1</div>
        </div>

        <!-- Four fundamental dimensions -->
        <div class="element" style="top: 150px; left: 100px; width: 250px; font-weight: bold; font-size: 16px;">
            G (Governance Layer)
            <div class="element-number">2</div>
        </div>

        <div class="element" style="top: 150px; left: 375px; width: 250px; font-weight: bold; font-size: 16px;">
            D (Data Layer)
            <div class="element-number">3</div>
        </div>

        <div class="element" style="top: 150px; left: 650px; width: 250px; font-weight: bold; font-size: 16px;">
            R (Response/Action Layer)
            <div class="element-number">4</div>
        </div>

        <!-- Descriptions -->
        <div class="element" style="top: 200px; left: 100px; width: 250px; font-size: 14px;">
            Structures, rules, principles, and authorities
            <div class="element-number">6</div>
        </div>

        <div class="element" style="top: 200px; left: 375px; width: 250px; font-size: 14px;">
            Flow, content, quality, and characteristics of information
            <div class="element-number">7</div>
        </div>

        <div class="element" style="top: 200px; left: 650px; width: 250px; font-size: 14px;">
            Behaviors, actions, feedback loops, and adaptive mechanisms
            <div class="element-number">8</div>
        </div>

        <div class="element" style="top: 280px; left: 375px; width: 250px; font-weight: bold; font-size: 16px;">
            <span class="bold-formula">π</span> (Trust Factor)
            <div class="element-number">5</div>
        </div>

        <div class="element" style="top: 330px; left: 375px; width: 250px; font-size: 14px;">
            System stability, transparency, integrity, and coherent evolution
            <div class="element-number">9</div>
        </div>

        <!-- Equation -->
        <div class="element" style="top: 400px; left: 300px; width: 400px; font-size: 16px;">
            Meta-Field = <span class="bold-formula">∑(Gₙ⊗Dₙ⊗Rₙ)·πⁿ</span>
            <div class="element-number">10</div>
        </div>

        <!-- Implementation components -->
        <div class="element" style="top: 500px; left: 100px; width: 200px; font-size: 14px;">
            Layer Abstraction Engine
            <div class="element-number">11</div>
        </div>

        <div class="element" style="top: 500px; left: 325px; width: 250px; font-size: 14px;">
            Cross-Layer Integration Module
            <div class="element-number">12</div>
        </div>

        <div class="element" style="top: 500px; left: 550px; width: 200px; font-size: 14px;">
            <span class="bold-formula">π</span>-Weighted Aggregator
            <div class="element-number">13</div>
        </div>

        <div class="element" style="top: 500px; left: 775px; width: 200px; font-size: 14px;">
            Universal Representation Generator
            <div class="element-number">14</div>
        </div>

        <div class="element" style="top: 600px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Technical Implementation: Schema Processing System
            <div class="element-number">15</div>
        </div>

        <!-- Connections -->
        <!-- Connect Meta-Field Schema to dimensions -->
        <!-- Lines removed as requested -->

        <!-- Connect dimensions to descriptions -->
        <div class="connection" style="top: 175px; left: 225px; width: 2px; height: 25px;"></div>
        <div class="connection" style="top: 175px; left: 500px; width: 2px; height: 25px;"></div>
        <div class="connection" style="top: 175px; left: 775px; width: 2px; height: 25px;"></div>

        <!-- Connect to Trust Factor -->
        <div class="connection" style="top: 225px; left: 225px; width: 150px; height: 2px;"></div>
        <div class="connection" style="top: 225px; left: 375px; width: 2px; height: 55px;"></div>

        <div class="connection" style="top: 225px; left: 500px; width: 2px; height: 55px;"></div>

        <div class="connection" style="top: 225px; left: 775px; width: 150px; height: 2px;"></div>
        <div class="connection" style="top: 225px; left: 625px; width: 2px; height: 55px;"></div>

        <!-- Connect Trust Factor to description -->
        <div class="connection" style="top: 305px; left: 500px; width: 2px; height: 25px;"></div>

        <!-- Connect to equation -->
        <!-- Lines removed as requested -->

        <!-- Connect equation to implementation components -->
        <div class="connection" style="top: 450px; left: 450px; width: 2px; height: 50px;"></div>

        <!-- Connect implementation components to implementation label -->
        <div class="connection" style="top: 550px; left: 450px; width: 2px; height: 50px;"></div>

        <!-- Connect #10 to line between #12 and #13 -->
        <div class="connection" style="top: 450px; left: 450px; width: 100px; height: 2px;"></div>
    </div>
</body>
</html>
