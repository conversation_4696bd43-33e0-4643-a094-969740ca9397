# NovaGRC APIs

NovaGRC APIs is a comprehensive suite of Governance, Risk, and Compliance (GRC) APIs that provide organizations with the tools they need to manage their GRC programs effectively.

## Overview

NovaGRC APIs is a key component of the NovaFuse platform, providing the GRC functionality that powers various NovaFuse products. These APIs can be used individually or together to create a comprehensive GRC solution.

## API Suite

NovaGRC APIs includes the following APIs:

1. **Privacy Management API**
   - Data processing activities
   - Data subject requests
   - Consent management
   - Privacy notices
   - Data breach management

2. **Regulatory Compliance API**
   - Compliance frameworks
   - Regulatory requirements
   - Compliance assessments
   - Gap analysis
   - Remediation tracking

3. **Security Assessment API**
   - Vulnerability management
   - Security policies
   - Security incidents
   - Security scans
   - Threat intelligence

4. **Control Testing API**
   - Control inventory
   - Test plans
   - Test execution
   - Evidence collection
   - Control effectiveness

5. **ESG API**
   - Environmental metrics
   - Social responsibility
   - Governance practices
   - ESG reporting
   - Stakeholder engagement

6. **Compliance Automation API**
   - Automated assessments
   - Continuous monitoring
   - Workflow automation
   - Integration with security tools
   - Compliance reporting

## Architecture

NovaGRC APIs are built using a microservices architecture with the following components:

- **API Gateway**: Routes requests to the appropriate API
- **Authentication Service**: Manages API authentication and authorization
- **API Services**: Individual API implementations
- **Data Layer**: MongoDB database for storing GRC data
- **Integration Layer**: Connects with external systems and data sources

## Implementation Status

| API | Status | Completion | Notes |
|-----|--------|------------|-------|
| Privacy Management API | Complete | 100% | All endpoints implemented and tested |
| Regulatory Compliance API | In Progress | 75% | Models and validation complete, controllers in progress |
| Security Assessment API | In Progress | 60% | Core functionality implemented |
| Control Testing API | In Progress | 50% | Basic endpoints implemented |
| ESG API | In Progress | 70% | Most endpoints implemented |
| Compliance Automation API | Planned | 0% | Not yet started |

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- MongoDB
- Docker (optional, for containerized deployment)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/Dartan1983/nova-grc-apis.git
   cd nova-grc-apis
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Configure environment variables:
   ```
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Start the service:
   ```
   npm start
   ```

### Development

1. Start in development mode:
   ```
   npm run dev
   ```

2. Run tests:
   ```
   npm test
   ```

3. Run with test coverage:
   ```
   npm run test:coverage
   ```

## API Documentation

### Privacy Management API

#### Endpoints

- `GET /api/privacy/management/processing-activities` - List all processing activities
- `GET /api/privacy/management/processing-activities/{id}` - Get a specific processing activity
- `POST /api/privacy/management/processing-activities` - Create a new processing activity
- `PUT /api/privacy/management/processing-activities/{id}` - Update a processing activity
- `DELETE /api/privacy/management/processing-activities/{id}` - Delete a processing activity

- `GET /api/privacy/management/dsr` - List all data subject requests
- `GET /api/privacy/management/dsr/{id}` - Get a specific data subject request
- `POST /api/privacy/management/dsr` - Create a new data subject request
- `PUT /api/privacy/management/dsr/{id}` - Update a data subject request
- `DELETE /api/privacy/management/dsr/{id}` - Delete a data subject request

### Regulatory Compliance API

#### Endpoints

- `GET /api/compliance/frameworks` - List all compliance frameworks
- `GET /api/compliance/frameworks/{id}` - Get a specific compliance framework
- `POST /api/compliance/frameworks` - Create a new compliance framework
- `PUT /api/compliance/frameworks/{id}` - Update a compliance framework
- `DELETE /api/compliance/frameworks/{id}` - Delete a compliance framework

- `GET /api/compliance/requirements` - List all regulatory requirements
- `GET /api/compliance/requirements/{id}` - Get a specific regulatory requirement
- `POST /api/compliance/requirements` - Create a new regulatory requirement
- `PUT /api/compliance/requirements/{id}` - Update a regulatory requirement
- `DELETE /api/compliance/requirements/{id}` - Delete a regulatory requirement

### Security Assessment API

#### Endpoints

- `GET /api/security/assessment/vulnerabilities` - List all vulnerabilities
- `GET /api/security/assessment/vulnerabilities/{id}` - Get a specific vulnerability
- `POST /api/security/assessment/vulnerabilities` - Create a new vulnerability
- `PUT /api/security/assessment/vulnerabilities/{id}` - Update a vulnerability
- `DELETE /api/security/assessment/vulnerabilities/{id}` - Delete a vulnerability

- `GET /api/security/assessment/policies` - List all security policies
- `GET /api/security/assessment/policies/{id}` - Get a specific security policy
- `POST /api/security/assessment/policies` - Create a new security policy
- `PUT /api/security/assessment/policies/{id}` - Update a security policy
- `DELETE /api/security/assessment/policies/{id}` - Delete a security policy

## Folder Structure

```
nova-grc-apis/
├── apis/
│   ├── privacy/
│   │   └── management/
│   │       ├── controllers/
│   │       ├── models/
│   │       ├── routes/
│   │       ├── services/
│   │       ├── validation/
│   │       └── index.js
│   ├── compliance/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── services/
│   │   ├── validation/
│   │   └── index.js
│   ├── security/
│   │   └── assessment/
│   │       ├── controllers/
│   │       ├── models/
│   │       ├── routes/
│   │       ├── services/
│   │       ├── validation/
│   │       └── index.js
│   ├── control/
│   │   └── testing/
│   │       ├── controllers/
│   │       ├── models/
│   │       ├── routes/
│   │       ├── services/
│   │       ├── validation/
│   │       └── index.js
│   └── esg/
│       ├── controllers/
│       ├── models/
│       ├── routes/
│       ├── services/
│       ├── validation/
│       └── index.js
├── common/
│   ├── middleware/
│   ├── models/
│   ├── services/
│   └── utils/
├── config/
├── docs/
├── tests/
└── server.js
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

For more information, contact the NovaFuse <NAME_EMAIL>.
