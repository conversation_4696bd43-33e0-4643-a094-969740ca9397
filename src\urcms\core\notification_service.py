"""
Notification Service for the Universal Regulatory Change Management System.

This module provides functionality for notifying stakeholders of regulatory changes,
with support for multiple notification channels, customizable templates, and
integration with external notification systems.
"""

import logging
import json
import os
import time
from datetime import datetime
from string import Template
from typing import Dict, List, Any, Optional, Callable, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NotificationService:
    """
    Service for notifying stakeholders of regulatory changes.

    This class is responsible for sending notifications about regulatory changes
    to relevant stakeholders through various channels, with support for
    customizable templates and integration with external notification systems.
    """

    # Default notification templates
    DEFAULT_TEMPLATES = {
        'email': {
            'subject': 'Regulatory Change: ${title}',
            'body': '''
Dear ${stakeholder_name},

A regulatory change has been detected that may impact your organization:

Title: ${title}
Description: ${description}
Regulation: ${regulation_type}
Impact Level: ${impact_level}
Effective Date: ${effective_date}

Affected Requirements:
${affected_requirements}

Required Actions:
${required_actions}

Please review the change and take appropriate action.

Regards,
The Compliance Team
'''
        },
        'slack': {
            'message': '''
*Regulatory Change: ${title}*
*Impact Level: ${impact_level}*

${description}

*Regulation:* ${regulation_type}
*Effective Date:* ${effective_date}

*Affected Requirements:*
${affected_requirements}

*Required Actions:*
${required_actions}
'''
        },
        'sms': {
            'message': 'Regulatory Change: ${title} (${impact_level} impact) - Effective: ${effective_date}'
        },
        'dashboard': {
            'title': 'Regulatory Change: ${title}',
            'message': '${description} - Impact Level: ${impact_level} - Effective: ${effective_date}'
        },
        'teams': {
            'title': 'Regulatory Change: ${title}',
            'message': '''
A regulatory change has been detected that may impact your organization:

Title: ${title}
Description: ${description}
Regulation: ${regulation_type}
Impact Level: ${impact_level}
Effective Date: ${effective_date}

Please review the change and take appropriate action.
'''
        },
        'webhook': {
            'payload': '''
{
    "title": "${title}",
    "description": "${description}",
    "regulation_type": "${regulation_type}",
    "impact_level": "${impact_level}",
    "effective_date": "${effective_date}",
    "change_id": "${change_id}"
}
'''
        }
    }

    def __init__(self, templates_dir: Optional[str] = None, external_config_path: Optional[str] = None):
        """
        Initialize the Notification Service.

        Args:
            templates_dir: Path to a directory containing notification templates
            external_config_path: Path to a JSON file containing external notification system configurations
        """
        logger.info("Initializing Notification Service")

        # Initialize the notification channels
        self.notification_channels: Dict[str, Callable] = {}

        # Initialize the stakeholders
        self.stakeholders: Dict[str, Dict[str, Any]] = {}

        # Initialize the notification templates
        self.templates: Dict[str, Dict[str, str]] = self.DEFAULT_TEMPLATES.copy()

        # Initialize the external notification systems
        self.external_systems: Dict[str, Dict[str, Any]] = {}

        # Initialize the notification history
        self.notification_history: List[Dict[str, Any]] = []

        # Register default notification channels
        self._register_default_channels()

        # Register default stakeholders
        self._register_default_stakeholders()

        # Load custom templates if provided
        if templates_dir and os.path.exists(templates_dir):
            self._load_templates_from_directory(templates_dir)

        # Load external notification system configurations if provided
        if external_config_path and os.path.exists(external_config_path):
            self._load_external_systems(external_config_path)
        else:
            # Load default external systems
            self._load_default_external_systems()

        logger.info(f"Notification Service initialized with {len(self.notification_channels)} channels, {len(self.templates)} templates, and {len(self.external_systems)} external systems")

    def _register_default_channels(self) -> None:
        """Register default notification channels."""
        self.register_channel('email', self._send_email_notification)
        self.register_channel('slack', self._send_slack_notification)
        self.register_channel('sms', self._send_sms_notification)
        self.register_channel('dashboard', self._send_dashboard_notification)
        self.register_channel('teams', self._send_teams_notification)
        self.register_channel('webhook', self._send_webhook_notification)

    def _load_templates_from_directory(self, directory: str) -> None:
        """
        Load notification templates from a directory.

        Args:
            directory: Path to the directory containing template files
        """
        try:
            # Get all JSON files in the directory
            template_files = [f for f in os.listdir(directory) if f.endswith('.json')]

            for template_file in template_files:
                try:
                    # Load the template
                    file_path = os.path.join(directory, template_file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        template_def = json.load(f)

                    # Get the channel ID from the filename (without extension)
                    channel_id = os.path.splitext(template_file)[0]

                    # Add the template
                    self.templates[channel_id] = template_def
                    logger.info(f"Loaded template for channel: {channel_id} from {file_path}")

                except Exception as e:
                    logger.error(f"Failed to load template from {template_file}: {e}")

        except Exception as e:
            logger.error(f"Failed to load templates from directory {directory}: {e}")

    def _load_default_external_systems(self) -> None:
        """Load default external notification system configurations."""
        # Slack webhook
        self.external_systems['slack_webhook'] = {
            'type': 'webhook',
            'name': 'Slack Webhook',
            'description': 'Send notifications to Slack via webhook',
            'url': 'https://hooks.slack.com/services/your-webhook-url',
            'headers': {
                'Content-Type': 'application/json'
            },
            'channel': 'slack'
        }

        # Microsoft Teams webhook
        self.external_systems['teams_webhook'] = {
            'type': 'webhook',
            'name': 'Microsoft Teams Webhook',
            'description': 'Send notifications to Microsoft Teams via webhook',
            'url': 'https://outlook.office.com/webhook/your-webhook-url',
            'headers': {
                'Content-Type': 'application/json'
            },
            'channel': 'teams'
        }

        # Email SMTP
        self.external_systems['email_smtp'] = {
            'type': 'smtp',
            'name': 'Email SMTP',
            'description': 'Send email notifications via SMTP',
            'host': 'smtp.example.com',
            'port': 587,
            'username': '<EMAIL>',
            'password': 'your-password',
            'from_address': '<EMAIL>',
            'channel': 'email'
        }

        # SMS API
        self.external_systems['sms_api'] = {
            'type': 'api',
            'name': 'SMS API',
            'description': 'Send SMS notifications via API',
            'url': 'https://api.example.com/sms',
            'method': 'POST',
            'headers': {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer your-api-key'
            },
            'channel': 'sms'
        }

        # Generic webhook
        self.external_systems['generic_webhook'] = {
            'type': 'webhook',
            'name': 'Generic Webhook',
            'description': 'Send notifications to a generic webhook',
            'url': 'https://api.example.com/webhook',
            'headers': {
                'Content-Type': 'application/json',
                'X-API-Key': 'your-api-key'
            },
            'channel': 'webhook'
        }

    def _load_external_systems(self, config_path: str) -> None:
        """
        Load external notification system configurations from a JSON file.

        Args:
            config_path: Path to the JSON file containing external system configurations
        """
        try:
            # Load the external system configurations
            with open(config_path, 'r', encoding='utf-8') as f:
                external_systems = json.load(f)

            # Add each external system configuration
            for system_id, system_config in external_systems.items():
                # Validate the external system configuration
                if self._validate_external_system(system_config):
                    # Add the external system configuration
                    self.external_systems[system_id] = system_config
                    logger.info(f"Loaded external system: {system_id} from {config_path}")
                else:
                    logger.warning(f"Invalid external system configuration in {config_path}: {system_id}")

        except Exception as e:
            logger.error(f"Failed to load external systems from {config_path}: {e}")

    def _validate_external_system(self, system_config: Dict[str, Any]) -> bool:
        """
        Validate an external system configuration.

        Args:
            system_config: The external system configuration to validate

        Returns:
            True if the external system configuration is valid, False otherwise
        """
        # Check required fields
        required_fields = ['type', 'name', 'description', 'channel']
        for field in required_fields:
            if field not in system_config:
                logger.warning(f"Missing required field in external system configuration: {field}")
                return False

        # Check type-specific required fields
        system_type = system_config.get('type')
        if system_type == 'webhook':
            if 'url' not in system_config:
                logger.warning("Missing required field 'url' for webhook external system")
                return False
        elif system_type == 'smtp':
            for field in ['host', 'port', 'from_address']:
                if field not in system_config:
                    logger.warning(f"Missing required field '{field}' for SMTP external system")
                    return False
        elif system_type == 'api':
            for field in ['url', 'method']:
                if field not in system_config:
                    logger.warning(f"Missing required field '{field}' for API external system")
                    return False
        else:
            logger.warning(f"Unsupported external system type: {system_type}")
            return False

        return True

    def _register_default_stakeholders(self) -> None:
        """Register default stakeholders."""
        # Compliance team
        self.stakeholders['compliance_team'] = {
            'name': 'Compliance Team',
            'description': 'Team responsible for compliance',
            'notification_channels': ['email', 'slack', 'dashboard'],
            'notification_preferences': {
                'gdpr': ['email', 'slack', 'dashboard'],
                'hipaa': ['email', 'slack', 'dashboard'],
                'soc2': ['email', 'slack', 'dashboard']
            }
        }

        # Legal team
        self.stakeholders['legal_team'] = {
            'name': 'Legal Team',
            'description': 'Team responsible for legal matters',
            'notification_channels': ['email', 'dashboard'],
            'notification_preferences': {
                'gdpr': ['email', 'dashboard'],
                'hipaa': ['email', 'dashboard'],
                'soc2': ['email', 'dashboard']
            }
        }

        # IT team
        self.stakeholders['it_team'] = {
            'name': 'IT Team',
            'description': 'Team responsible for IT systems',
            'notification_channels': ['email', 'slack', 'dashboard'],
            'notification_preferences': {
                'gdpr': ['email', 'dashboard'],
                'hipaa': ['email', 'slack', 'dashboard'],
                'soc2': ['email', 'slack', 'dashboard']
            }
        }

        # Security team
        self.stakeholders['security_team'] = {
            'name': 'Security Team',
            'description': 'Team responsible for security',
            'notification_channels': ['email', 'slack', 'sms', 'dashboard'],
            'notification_preferences': {
                'gdpr': ['email', 'slack', 'dashboard'],
                'hipaa': ['email', 'slack', 'sms', 'dashboard'],
                'soc2': ['email', 'slack', 'dashboard']
            }
        }

        # Executive team
        self.stakeholders['executive_team'] = {
            'name': 'Executive Team',
            'description': 'Executive leadership team',
            'notification_channels': ['email', 'dashboard'],
            'notification_preferences': {
                'gdpr': ['email', 'dashboard'],
                'hipaa': ['email', 'dashboard'],
                'soc2': ['email', 'dashboard']
            }
        }

    def register_channel(self, channel_id: str, channel_func: Callable) -> None:
        """
        Register a notification channel.

        Args:
            channel_id: The ID of the channel
            channel_func: The channel function
        """
        self.notification_channels[channel_id] = channel_func
        logger.info(f"Registered notification channel: {channel_id}")

    def register_stakeholder(self, stakeholder_id: str, stakeholder: Dict[str, Any]) -> None:
        """
        Register a stakeholder.

        Args:
            stakeholder_id: The ID of the stakeholder
            stakeholder: The stakeholder definition
        """
        self.stakeholders[stakeholder_id] = stakeholder
        logger.info(f"Registered stakeholder: {stakeholder_id}")

    def send_notification(self, change: Dict[str, Any], impact: Dict[str, Any]) -> None:
        """
        Send notifications about a regulatory change.

        Args:
            change: The regulatory change
            impact: The impact analysis
        """
        logger.info(f"Sending notifications for regulatory change: {change.get('id')}")

        # Get the regulation type
        regulation_type = change.get('regulation_type')

        # Determine the stakeholders to notify
        stakeholders_to_notify = self._determine_stakeholders_to_notify(regulation_type, impact)

        # Send notifications to each stakeholder
        for stakeholder_id in stakeholders_to_notify:
            self._send_stakeholder_notification(stakeholder_id, change, impact)

    def _determine_stakeholders_to_notify(self, regulation_type: str, impact: Dict[str, Any]) -> List[str]:
        """
        Determine which stakeholders to notify about a regulatory change.

        Args:
            regulation_type: The type of regulation
            impact: The impact analysis

        Returns:
            List of stakeholder IDs
        """
        # Get the impact level
        impact_level = impact.get('impact_level', 'medium')

        # In a real implementation, this would determine which stakeholders
        # to notify based on the regulation type, impact level, and other factors

        # For now, implement a simple filtering logic:
        # - For high impact changes, notify all stakeholders
        # - For medium impact changes, notify compliance and legal teams
        # - For low impact changes, notify only the compliance team

        if impact_level == 'high':
            # Notify all stakeholders
            return list(self.stakeholders.keys())
        elif impact_level == 'medium':
            # Notify compliance and legal teams
            return [s_id for s_id in self.stakeholders.keys()
                   if 'compliance' in s_id or 'legal' in s_id]
        else:
            # Notify only the compliance team
            return [s_id for s_id in self.stakeholders.keys()
                   if 'compliance' in s_id]

    def _send_stakeholder_notification(self, stakeholder_id: str, change: Dict[str, Any], impact: Dict[str, Any]) -> None:
        """
        Send notifications to a stakeholder.

        Args:
            stakeholder_id: The ID of the stakeholder
            change: The regulatory change
            impact: The impact analysis
        """
        logger.info(f"Sending notifications to stakeholder: {stakeholder_id}")

        # Get the stakeholder
        stakeholder = self.stakeholders.get(stakeholder_id)
        if not stakeholder:
            logger.warning(f"Stakeholder not found: {stakeholder_id}")
            return

        # Get the regulation type
        regulation_type = change.get('regulation_type')

        # Get the notification preferences for this regulation type
        notification_preferences = stakeholder.get('notification_preferences', {}).get(regulation_type, [])

        # If no specific preferences for this regulation type, use the general notification channels
        if not notification_preferences:
            notification_preferences = stakeholder.get('notification_channels', [])

        # Send notifications through each preferred channel
        for channel_id in notification_preferences:
            self._send_channel_notification(channel_id, stakeholder_id, change, impact)

    def _send_channel_notification(self, channel_id: str, stakeholder_id: str, change: Dict[str, Any], impact: Dict[str, Any]) -> None:
        """
        Send a notification through a specific channel.

        Args:
            channel_id: The ID of the channel
            stakeholder_id: The ID of the stakeholder
            change: The regulatory change
            impact: The impact analysis
        """
        logger.info(f"Sending notification through channel: {channel_id} to stakeholder: {stakeholder_id}")

        # Get the channel function
        channel_func = self.notification_channels.get(channel_id)
        if not channel_func:
            logger.warning(f"Notification channel not found: {channel_id}")
            return

        try:
            # Call the channel function
            channel_func(stakeholder_id, change, impact)
        except Exception as e:
            logger.error(f"Error sending notification through channel {channel_id} to stakeholder {stakeholder_id}: {e}")

    def _prepare_template_data(self, stakeholder_id: str, change: Dict[str, Any], impact: Dict[str, Any]) -> Dict[str, str]:
        """
        Prepare data for template rendering.

        Args:
            stakeholder_id: The ID of the stakeholder
            change: The regulatory change
            impact: The impact analysis

        Returns:
            Dictionary of template variables
        """
        # Get the stakeholder
        stakeholder = self.stakeholders.get(stakeholder_id, {})

        # Format affected requirements
        affected_requirements = ""
        for req in change.get('affected_requirements', []):
            affected_requirements += f"- {req}\n"

        # Format required actions
        required_actions = ""
        for action in impact.get('required_actions', []):
            required_actions += f"- {action.get('description')} (Priority: {action.get('priority')})\n"

        # Prepare the template data
        template_data = {
            'stakeholder_id': stakeholder_id,
            'stakeholder_name': stakeholder.get('name', stakeholder_id),
            'change_id': change.get('id', ''),
            'title': change.get('title', ''),
            'description': change.get('description', ''),
            'regulation_type': change.get('regulation_type', ''),
            'change_date': change.get('change_date', ''),
            'effective_date': change.get('effective_date', ''),
            'impact_level': impact.get('impact_level', ''),
            'risk_score': str(impact.get('risk_score', '')),
            'affected_requirements': affected_requirements,
            'required_actions': required_actions
        }

        return template_data

    def _render_template(self, template_str: str, template_data: Dict[str, str]) -> str:
        """
        Render a template with the provided data.

        Args:
            template_str: The template string
            template_data: The template data

        Returns:
            The rendered template
        """
        # Create a template object
        template = Template(template_str)

        # Render the template
        try:
            return template.safe_substitute(template_data)
        except Exception as e:
            logger.error(f"Error rendering template: {e}")
            return template_str

    def _send_email_notification(self, stakeholder_id: str, change: Dict[str, Any], impact: Dict[str, Any]) -> None:
        """
        Send an email notification.

        Args:
            stakeholder_id: The ID of the stakeholder
            change: The regulatory change
            impact: The impact analysis
        """
        # Prepare the template data
        template_data = self._prepare_template_data(stakeholder_id, change, impact)

        # Get the email templates
        email_templates = self.templates.get('email', {})
        subject_template = email_templates.get('subject', 'Regulatory Change: ${title}')
        body_template = email_templates.get('body', 'A regulatory change has been detected: ${description}')

        # Render the templates
        subject = self._render_template(subject_template, template_data)
        body = self._render_template(body_template, template_data)

        # Check if we have an external system for email
        for system_id, system_config in self.external_systems.items():
            if system_config.get('channel') == 'email' and system_config.get('type') == 'smtp':
                # In a real implementation, this would send an email via SMTP
                logger.info(f"Sending email notification to stakeholder: {stakeholder_id} via external system: {system_id}")
                logger.info(f"SMTP Host: {system_config.get('host')}:{system_config.get('port')}")
                logger.info(f"From: {system_config.get('from_address')}")
                logger.info(f"Subject: {subject}")
                logger.info(f"Body: {body}")
                return

        # If no external system, just log the notification
        logger.info(f"Sending email notification to stakeholder: {stakeholder_id}")
        logger.info(f"Email subject: {subject}")
        logger.info(f"Email body: {body}")

    def _send_slack_notification(self, stakeholder_id: str, change: Dict[str, Any], impact: Dict[str, Any]) -> None:
        """
        Send a Slack notification.

        Args:
            stakeholder_id: The ID of the stakeholder
            change: The regulatory change
            impact: The impact analysis
        """
        # Prepare the template data
        template_data = self._prepare_template_data(stakeholder_id, change, impact)

        # Get the Slack template
        slack_templates = self.templates.get('slack', {})
        message_template = slack_templates.get('message', 'Regulatory Change: ${title} - ${description}')

        # Render the template
        message = self._render_template(message_template, template_data)

        # Check if we have an external system for Slack
        for system_id, system_config in self.external_systems.items():
            if system_config.get('channel') == 'slack' and system_config.get('type') == 'webhook':
                # In a real implementation, this would send a Slack message via webhook
                logger.info(f"Sending Slack notification to stakeholder: {stakeholder_id} via external system: {system_id}")
                logger.info(f"Webhook URL: {system_config.get('url')}")
                logger.info(f"Message: {message}")
                return

        # If no external system, just log the notification
        logger.info(f"Sending Slack notification to stakeholder: {stakeholder_id}")
        logger.info(f"Slack message: {message}")

    def _send_sms_notification(self, stakeholder_id: str, change: Dict[str, Any], impact: Dict[str, Any]) -> None:
        """
        Send an SMS notification.

        Args:
            stakeholder_id: The ID of the stakeholder
            change: The regulatory change
            impact: The impact analysis
        """
        # Prepare the template data
        template_data = self._prepare_template_data(stakeholder_id, change, impact)

        # Get the SMS template
        sms_templates = self.templates.get('sms', {})
        message_template = sms_templates.get('message', 'Regulatory Change: ${title}')

        # Render the template
        message = self._render_template(message_template, template_data)

        # Check if we have an external system for SMS
        for system_id, system_config in self.external_systems.items():
            if system_config.get('channel') == 'sms' and system_config.get('type') == 'api':
                # In a real implementation, this would send an SMS via API
                logger.info(f"Sending SMS notification to stakeholder: {stakeholder_id} via external system: {system_id}")
                logger.info(f"API URL: {system_config.get('url')}")
                logger.info(f"Method: {system_config.get('method')}")
                logger.info(f"Message: {message}")
                return

        # If no external system, just log the notification
        logger.info(f"Sending SMS notification to stakeholder: {stakeholder_id}")
        logger.info(f"SMS message: {message}")

    def _send_dashboard_notification(self, stakeholder_id: str, change: Dict[str, Any], impact: Dict[str, Any]) -> None:
        """
        Send a dashboard notification.

        Args:
            stakeholder_id: The ID of the stakeholder
            change: The regulatory change
            impact: The impact analysis
        """
        # Prepare the template data
        template_data = self._prepare_template_data(stakeholder_id, change, impact)

        # Get the dashboard templates
        dashboard_templates = self.templates.get('dashboard', {})
        title_template = dashboard_templates.get('title', 'Regulatory Change: ${title}')
        message_template = dashboard_templates.get('message', '${description} - Impact Level: ${impact_level}')

        # Render the templates
        title = self._render_template(title_template, template_data)
        message = self._render_template(message_template, template_data)

        # In a real implementation, this would send a dashboard notification
        # For now, just log the notification
        logger.info(f"Sending dashboard notification to stakeholder: {stakeholder_id}")
        logger.info(f"Dashboard notification title: {title}")
        logger.info(f"Dashboard notification message: {message}")

    def _send_teams_notification(self, stakeholder_id: str, change: Dict[str, Any], impact: Dict[str, Any]) -> None:
        """
        Send a Microsoft Teams notification.

        Args:
            stakeholder_id: The ID of the stakeholder
            change: The regulatory change
            impact: The impact analysis
        """
        # Prepare the template data
        template_data = self._prepare_template_data(stakeholder_id, change, impact)

        # Get the Teams templates
        teams_templates = self.templates.get('teams', {})
        title_template = teams_templates.get('title', 'Regulatory Change: ${title}')
        message_template = teams_templates.get('message', 'A regulatory change has been detected: ${description}')

        # Render the templates
        title = self._render_template(title_template, template_data)
        message = self._render_template(message_template, template_data)

        # Check if we have an external system for Teams
        for system_id, system_config in self.external_systems.items():
            if system_config.get('channel') == 'teams' and system_config.get('type') == 'webhook':
                # In a real implementation, this would send a Teams message via webhook
                logger.info(f"Sending Teams notification to stakeholder: {stakeholder_id} via external system: {system_id}")
                logger.info(f"Webhook URL: {system_config.get('url')}")
                logger.info(f"Title: {title}")
                logger.info(f"Message: {message}")
                return

        # If no external system, just log the notification
        logger.info(f"Sending Teams notification to stakeholder: {stakeholder_id}")
        logger.info(f"Teams notification title: {title}")
        logger.info(f"Teams notification message: {message}")

    def _send_webhook_notification(self, stakeholder_id: str, change: Dict[str, Any], impact: Dict[str, Any]) -> None:
        """
        Send a webhook notification.

        Args:
            stakeholder_id: The ID of the stakeholder
            change: The regulatory change
            impact: The impact analysis
        """
        # Prepare the template data
        template_data = self._prepare_template_data(stakeholder_id, change, impact)

        # Get the webhook template
        webhook_templates = self.templates.get('webhook', {})
        payload_template = webhook_templates.get('payload', '{"title": "${title}", "description": "${description}"}')

        # Render the template
        payload = self._render_template(payload_template, template_data)

        # Check if we have an external system for webhook
        for system_id, system_config in self.external_systems.items():
            if system_config.get('channel') == 'webhook' and system_config.get('type') == 'webhook':
                # In a real implementation, this would send a webhook notification
                logger.info(f"Sending webhook notification to stakeholder: {stakeholder_id} via external system: {system_id}")
                logger.info(f"Webhook URL: {system_config.get('url')}")
                logger.info(f"Payload: {payload}")
                return

        # If no external system, just log the notification
        logger.info(f"Sending webhook notification to stakeholder: {stakeholder_id}")
        logger.info(f"Webhook payload: {payload}")
