eccf8262385e700bce827cefd7037959
/**
 * Security tests for the NovaConnect API
 */

const {
  expect
} = require('chai');
const supertest = require('supertest');
const mongoose = require('mongoose');
const {
  MongoMemoryServer
} = require('mongodb-memory-server');
const jwt = require('jsonwebtoken');

// Import the app
const app = require('../../server');
describe('NovaConnect API Security Tests', () => {
  let mongoServer;
  let request;
  let validApiKey;
  let validJwtToken;
  before(async () => {
    // Create an in-memory MongoDB server
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();

    // Connect to the in-memory database
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    // Create a supertest request object
    request = supertest(app);

    // Create a valid API key for testing
    validApiKey = 'test-api-key-12345';

    // Create a valid JWT token for testing
    const jwtSecret = process.env.JWT_SECRET || 'test-jwt-secret';
    validJwtToken = jwt.sign({
      userId: 'test-user-id',
      role: 'admin'
    }, jwtSecret, {
      expiresIn: '1h'
    });
  });
  after(async () => {
    // Disconnect from the database
    await mongoose.disconnect();
    // Stop the in-memory MongoDB server
    await mongoServer.stop();
  });
  describe('Authentication', () => {
    it('should reject requests without authentication', async () => {
      const response = await request.get('/api/connectors');
      expect(response.status).to.equal(401);
    });
    it('should accept requests with valid API key', async () => {
      const response = await request.get('/api/connectors').set('X-API-Key', validApiKey);
      expect(response.status).to.equal(200);
    });
    it('should reject requests with invalid API key', async () => {
      const response = await request.get('/api/connectors').set('X-API-Key', 'invalid-api-key');
      expect(response.status).to.equal(401);
    });
    it('should accept requests with valid JWT token', async () => {
      const response = await request.get('/api/connectors').set('Authorization', `Bearer ${validJwtToken}`);
      expect(response.status).to.equal(200);
    });
    it('should reject requests with invalid JWT token', async () => {
      const response = await request.get('/api/connectors').set('Authorization', 'Bearer invalid-token');
      expect(response.status).to.equal(401);
    });
    it('should reject requests with expired JWT token', async () => {
      // Create an expired JWT token
      const jwtSecret = process.env.JWT_SECRET || 'test-jwt-secret';
      const expiredToken = jwt.sign({
        userId: 'test-user-id',
        role: 'admin'
      }, jwtSecret, {
        expiresIn: '-1h'
      } // Expired 1 hour ago
      );
      const response = await request.get('/api/connectors').set('Authorization', `Bearer ${expiredToken}`);
      expect(response.status).to.equal(401);
    });
  });
  describe('Authorization', () => {
    it('should allow admin users to access admin endpoints', async () => {
      // Create an admin JWT token
      const jwtSecret = process.env.JWT_SECRET || 'test-jwt-secret';
      const adminToken = jwt.sign({
        userId: 'admin-user-id',
        role: 'admin'
      }, jwtSecret, {
        expiresIn: '1h'
      });
      const response = await request.get('/api/admin/users').set('Authorization', `Bearer ${adminToken}`);
      expect(response.status).to.equal(200);
    });
    it('should deny regular users access to admin endpoints', async () => {
      // Create a regular user JWT token
      const jwtSecret = process.env.JWT_SECRET || 'test-jwt-secret';
      const userToken = jwt.sign({
        userId: 'regular-user-id',
        role: 'user'
      }, jwtSecret, {
        expiresIn: '1h'
      });
      const response = await request.get('/api/admin/users').set('Authorization', `Bearer ${userToken}`);
      expect(response.status).to.equal(403);
    });
  });
  describe('Rate Limiting', () => {
    it('should rate limit excessive requests', async () => {
      // Make multiple requests in quick succession
      const requests = [];
      for (let i = 0; i < 110; i++) {
        requests.push(request.get('/api/connectors').set('X-API-Key', validApiKey));
      }

      // Wait for all requests to complete
      const responses = await Promise.all(requests);

      // At least one request should be rate limited
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).to.be.greaterThan(0);
    });
  });
  describe('Input Validation', () => {
    it('should reject invalid input', async () => {
      const invalidConnector = {
        // Missing required fields
        type: 'http'
      };
      const response = await request.post('/api/connectors').set('X-API-Key', validApiKey).send(invalidConnector);
      expect(response.status).to.equal(400);
      expect(response.body).to.have.property('error');
    });
    it('should sanitize input to prevent XSS', async () => {
      const xssConnector = {
        name: '<script>alert("XSS")</script>',
        type: 'http',
        description: 'Test connector with XSS payload'
      };
      const response = await request.post('/api/connectors').set('X-API-Key', validApiKey).send(xssConnector);
      expect(response.status).to.equal(201);
      expect(response.body.name).to.not.include('<script>');
    });
    it('should prevent SQL injection', async () => {
      const sqlInjectionQuery = "'; DROP TABLE users; --";
      const response = await request.get(`/api/connectors?query=${sqlInjectionQuery}`).set('X-API-Key', validApiKey);
      expect(response.status).to.equal(200);
      // The query should be sanitized and not cause any errors
    });
  });
  describe('Security Headers', () => {
    it('should include security headers in responses', async () => {
      const response = await request.get('/health');
      expect(response.headers).to.have.property('x-content-type-options');
      expect(response.headers['x-content-type-options']).to.equal('nosniff');
      expect(response.headers).to.have.property('x-frame-options');
      expect(response.headers['x-frame-options']).to.equal('DENY');
      expect(response.headers).to.have.property('content-security-policy');
      expect(response.headers).to.have.property('strict-transport-security');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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