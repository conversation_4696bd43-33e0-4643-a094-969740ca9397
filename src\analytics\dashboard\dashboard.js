/**
 * Comphyological Analytics Dashboard
 *
 * This script initializes and manages the analytics dashboard.
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Analytics Dashboard initialized');

    // Initialize analytics core
    let analytics;
    try {
        analytics = new AnalyticsCore({
            historyLength: 1000,
            samplingRate: 100, // ms
            goldenRatio: 0.618033988749895,
            targetFrequency: 396, // Hz
            patternDetectionThreshold: 0.05,
            predictionHorizon: 100,
            optimizationIterations: 10
        });

        // Store analytics instance globally for access from other modules
        window.analyticsInstance = analytics;
    } catch (error) {
        console.error('Failed to initialize AnalyticsCore:', error);
        return;
    }

    // Set up event listeners
    setupEventListeners(analytics);

    // Initialize charts
    if (typeof initializeCharts === 'function') {
        initializeCharts();
    } else {
        console.error('Charts module not loaded properly');
    }

    // Start with demo data
    generateDemoData(analytics);
});

/**
 * Set up event listeners
 * @param {AnalyticsCore} analytics - Analytics core instance
 */
function setupEventListeners(analytics) {
    // Start button
    document.getElementById('start-button').addEventListener('click', function() {
        console.log('Starting analytics');
        analytics.start();
        updateUIState(true);
    });

    // Stop button
    document.getElementById('stop-button').addEventListener('click', function() {
        console.log('Stopping analytics');
        analytics.stop();
        updateUIState(false);
    });

    // Reset button
    document.getElementById('reset-button').addEventListener('click', function() {
        console.log('Resetting analytics');
        resetDashboard();
    });

    // Time range selector
    document.getElementById('time-range').addEventListener('change', function() {
        console.log('Time range changed:', this.value);
        updateCharts(this.value);
    });

    // Sensitivity analysis button
    document.getElementById('run-sensitivity-analysis').addEventListener('click', function() {
        console.log('Running sensitivity analysis');
        const variationRange = parseFloat(document.getElementById('variation-range').value);
        runSensitivityAnalysis(analytics, variationRange);
    });

    // Parameter tuning button
    document.getElementById('run-parameter-tuning').addEventListener('click', function() {
        console.log('Running parameter tuning');
        const iterations = parseInt(document.getElementById('tuning-iterations').value);
        runParameterTuning(analytics, iterations);
    });

    // Apply tuning results button
    document.getElementById('apply-tuning-results').addEventListener('click', function() {
        console.log('Applying tuning results');
        applyTuningResults(analytics);
    });

    // Range input value displays
    document.getElementById('variation-range').addEventListener('input', function() {
        document.getElementById('variation-range-value').textContent = this.value;
    });

    document.getElementById('tuning-iterations').addEventListener('input', function() {
        document.getElementById('tuning-iterations-value').textContent = this.value;
    });
}

/**
 * Update UI state based on analytics running state
 * @param {boolean} isRunning - Whether analytics is running
 */
function updateUIState(isRunning) {
    document.getElementById('start-button').disabled = isRunning;
    document.getElementById('stop-button').disabled = !isRunning;
}

/**
 * Initialize charts
 */
function initializeCharts() {
    console.log('Initializing charts');

    // This is a placeholder - we'll implement the actual chart initialization later
}

/**
 * Generate demo data
 * @param {AnalyticsCore} analytics - Analytics core instance
 */
function generateDemoData(analytics) {
    console.log('Generating demo data');

    // Generate some initial data points
    for (let i = 0; i < 10; i++) {
        const timestamp = Date.now() - (10 - i) * 1000;
        const comphyon = Math.random() * 0.1;
        const frequency = 396 + comphyon * 100;
        const isQuantumSilence = comphyon < 0.01;

        // Process data point
        analytics.processData({
            timestamp,
            comphyon,
            frequency,
            isQuantumSilence,
            csdeValue: 0.618,
            csfeValue: 0.618,
            csmeValue: 0.618
        });
    }

    // Update dashboard with initial data
    updateDashboard(analytics);
}

/**
 * Update dashboard with latest analytics data
 * @param {AnalyticsCore} analytics - Analytics core instance
 */
function updateDashboard(analytics) {
    console.log('Updating dashboard');

    // This is a placeholder - we'll implement the actual dashboard update later

    // For now, just update the basic metrics
    updateBasicMetrics(analytics);
}

/**
 * Update basic metrics display
 * @param {AnalyticsCore} analytics - Analytics core instance
 */
function updateBasicMetrics(analytics) {
    // Get latest data point
    const history = analytics.getHistory();
    if (history.length === 0) return;

    const latestPoint = history[history.length - 1];

    // Update Comphyon value
    document.getElementById('comphyon-value').textContent = latestPoint.comphyon.toFixed(6);

    // Update Frequency value
    document.getElementById('frequency-value').textContent = latestPoint.frequency.toFixed(6) + ' Hz';

    // Update Quantum Silence value
    const quantumSilenceElement = document.getElementById('quantum-silence-value');
    quantumSilenceElement.textContent = latestPoint.isQuantumSilence ? 'Yes' : 'No';
    quantumSilenceElement.className = latestPoint.isQuantumSilence ? 'metric-value quantum-silence-yes' : 'metric-value';

    // Update Harmony value
    const harmony = 1 - Math.abs(latestPoint.comphyon);
    document.getElementById('harmony-value').textContent = harmony.toFixed(3);
}

/**
 * Update charts with latest data
 * @param {string} timeRange - Time range to display
 */
function updateCharts(timeRange) {
    console.log('Updating charts for time range:', timeRange);

    // Get analytics data
    const analytics = window.analyticsInstance;
    if (!analytics) {
        console.error('Analytics instance not available');
        return;
    }

    // Get history and predictions
    const history = analytics.getHistory();
    const predictions = analytics.getPredictions();

    // Update charts if the charts module is loaded
    if (typeof window.updateCharts === 'function') {
        window.updateCharts(history, predictions, timeRange);
    } else {
        console.error('Charts module not loaded properly');
    }
}

/**
 * Reset dashboard
 */
function resetDashboard() {
    console.log('Resetting dashboard');

    // This is a placeholder - we'll implement the actual dashboard reset later
}

/**
 * Run sensitivity analysis
 * @param {AnalyticsCore} analytics - Analytics core instance
 * @param {number} variationRange - Variation range
 */
function runSensitivityAnalysis(analytics, variationRange) {
    console.log('Running sensitivity analysis with variation range:', variationRange);

    // This is a placeholder - we'll implement the actual sensitivity analysis later
}

/**
 * Run parameter tuning
 * @param {AnalyticsCore} analytics - Analytics core instance
 * @param {number} iterations - Number of iterations
 */
function runParameterTuning(analytics, iterations) {
    console.log('Running parameter tuning with iterations:', iterations);

    // This is a placeholder - we'll implement the actual parameter tuning later
}

/**
 * Apply tuning results
 * @param {AnalyticsCore} analytics - Analytics core instance
 */
function applyTuningResults(analytics) {
    console.log('Applying tuning results');

    // This is a placeholder - we'll implement the actual application of tuning results later
}
