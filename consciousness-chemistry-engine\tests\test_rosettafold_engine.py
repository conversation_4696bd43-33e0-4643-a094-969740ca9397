"""
Tests for the RoseTTAFoldEngine implementation.
"""

import os
import unittest
import tempfile
from unittest.mock import patch, MagicMock, ANY

from src.rosettafold_engine import RoseTT<PERSON>oldEng<PERSON>, FoldingEngineError

class TestRoseTTAFoldEngine(unittest.TestCase):
    """Test cases for RoseTTAFoldEngine."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_sequence = "ACDEFGHIKLMNPQRSTVWY"  # Valid protein sequence
        self.temp_dir = tempfile.mkdtemp()
        
        # Mock configuration
        self.config = {
            'rosettafold_path': '/fake/rosettafold',
            'output_dir': self.temp_dir,
            'mode': 'hybrid',
            'use_quantum': True,
            'quantum_backend': 'qsim',
            'quantum_circuit_depth': 100,
            'quantum_shots': 1000,
            'quantum_layers': 2,
            'debug': True  # Keep temp files for inspection
        }
    
    def tearDown(self):
        """Clean up after tests."""
        # Clean up temporary directory
        for root, dirs, files in os.walk(self.temp_dir, topdown=False):
            for name in files:
                os.remove(os.path.join(root, name))
            for name in dirs:
                os.rmdir(os.path.join(root, name))
        os.rmdir(self.temp_dir)
    
    @patch('subprocess.Popen')
    @patch('os.path.exists', return_value=True)
    @patch('os.makedirs')
    def test_init_with_preset(self, mock_makedirs, mock_exists, mock_popen):
        """Test initialization with preset configurations."""
        # Test classical mode
        engine = RoseTTAFoldEngine(config='classical')
        self.assertEqual(engine.config['mode'], 'classical')
        self.assertFalse(engine.use_quantum)
        
        # Test hybrid mode
        engine = RoseTTAFoldEngine(config='hybrid')
        self.assertEqual(engine.config['mode'], 'hybrid')
        self.assertTrue(engine.use_quantum)
        self.assertEqual(engine.quantum_backend, 'qsim')
        
        # Test quantum mode
        engine = RoseTTAFoldEngine(config='quantum')
        self.assertEqual(engine.config['mode'], 'quantum')
        self.assertTrue(engine.use_quantum)
        self.assertGreater(engine.quantum_circuit_depth, 100)
    
    @patch('subprocess.Popen')
    @patch('os.path.exists', return_value=True)
    @patch('os.makedirs')
    def test_predict_success(self, mock_makedirs, mock_exists, mock_popen):
        """Test successful prediction."""
        # Setup mock process
        mock_process = MagicMock()
        mock_process.stdout = ["RoseTTAFold: Starting prediction", "RoseTTAFold: Done"]
        mock_process.wait.return_value = 0
        mock_popen.return_value = mock_process
        
        # Create engine and run prediction
        engine = RoseTTAFoldEngine(config=self.config)
        
        # Mock file operations
        with patch('builtins.open', unittest.mock.mock_open()):
            with patch('os.path.join', side_effect=lambda *args: os.path.join(*args)):
                with patch('os.path.isfile', return_value=True):
                    result = engine.predict(self.test_sequence)
        
        # Verify results
        self.assertEqual(result['status'], 'COMPLETED')
        self.assertIn('pdb_path', result)
        self.assertIn('output_dir', result)
        self.assertIn('processing_time_seconds', result)
        self.assertIn('quantum_info', result)
        self.assertTrue(result['quantum_info']['used_quantum'])
    
    @patch('subprocess.Popen')
    def test_predict_failure(self, mock_popen):
        """Test prediction failure."""
        # Setup mock process that fails
        mock_process = MagicMock()
        mock_process.stdout = ["RoseTTAFold: Error occurred"]
        mock_process.wait.return_value = 1
        mock_popen.return_value = mock_process
        
        # Create engine and expect failure
        engine = RoseTTAFoldEngine(config=self.config)
        
        with self.assertRaises(FoldingEngineError):
            with patch('builtins.open', unittest.mock.mock_open()):
                with patch('os.path.join', side_effect=lambda *args: os.path.join(*args)):
                    engine.predict(self.test_sequence)
    
    @patch('subprocess.Popen')
    @patch('os.path.exists', return_value=True)
    def test_quantum_parameters(self, mock_exists, mock_popen):
        """Test quantum parameter handling."""
        # Setup mock process
        mock_process = MagicMock()
        mock_process.wait.return_value = 0
        mock_popen.return_value = mock_process
        
        # Test with custom quantum parameters
        config = self.config.copy()
        config.update({
            'quantum_backend': 'qiskit',
            'quantum_circuit_depth': 200,
            'quantum_shots': 2000,
            'quantum_layers': 3
        })
        
        engine = RoseTTAFoldEngine(config=config)
        
        # Verify parameters
        self.assertEqual(engine.quantum_backend, 'qiskit')
        self.assertEqual(engine.quantum_circuit_depth, 200)
        self.assertEqual(engine.quantum_shots, 2000)
        self.assertEqual(engine.quantum_layers, 3)
    
    @patch('subprocess.Popen')
    @patch('os.path.exists', return_value=True)
    def test_consciousness_optimization(self, mock_exists, mock_popen):
        """Test consciousness optimization configuration."""
        # Setup mock process
        mock_process = MagicMock()
        mock_process.wait.return_value = 0
        mock_popen.return_value = mock_process
        
        # Create engine with consciousness optimization
        config = self.config.copy()
        config.update({
            'psi_optimization': True,
            'fib_constraints': {
                'enabled': True,
                'tolerance': 0.2
            }
        })
        
        engine = RoseTTAFoldEngine(config=config)
        
        # Verify configuration
        self.assertTrue(engine.config.get('psi_optimization', False))
        self.assertTrue(engine.config.get('fib_constraints', {}).get('enabled', False))
        self.assertEqual(engine.config.get('fib_constraints', {}).get('tolerance', 0), 0.2)

if __name__ == '__main__':
    unittest.main()
