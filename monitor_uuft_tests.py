#!/usr/bin/env python3
"""
UUFT Test Supervisor

This script monitors CSDE/UUFT tests running in both Docker and GCP environments,
providing automated alerts and analysis of test results.

Based on Orion's suggestion for continuous monitoring with the 18/82 rhythm.
"""

import os
import json
import time
import datetime
import subprocess
import threading
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

# Configuration
DOCKER_RESULTS_DIR = "results"
GCP_RESULTS_DIR = "gcp_results"
MONITOR_INTERVAL = 18  # Check every 18 seconds (18/82 rhythm)
ALERT_THRESHOLD = 82  # Alert if performance is below 82% of target

# Create results directories if they don't exist
os.makedirs(DOCKER_RESULTS_DIR, exist_ok=True)
os.makedirs(GCP_RESULTS_DIR, exist_ok=True)
os.makedirs("monitor_reports", exist_ok=True)

# Global state
docker_status = {"status": "PENDING", "last_update": None, "metrics": {}}
gcp_status = {"status": "PENDING", "last_update": None, "metrics": {}}
test_history = {"docker": {}, "gcp": {}}

def get_docker_metrics():
    """
    Get metrics from Docker test results
    """
    global docker_status
    
    # Check if Docker container is running
    try:
        result = subprocess.run(
            ["docker", "ps", "--filter", "name=novafuse/csde-testing", "--format", "{{.Status}}"],
            capture_output=True, text=True, check=False
        )
        if "Up" in result.stdout:
            docker_status["status"] = "RUNNING"
        else:
            # Check if container exited
            result = subprocess.run(
                ["docker", "ps", "-a", "--filter", "name=novafuse/csde-testing", "--format", "{{.Status}}"],
                capture_output=True, text=True, check=False
            )
            if "Exited" in result.stdout:
                docker_status["status"] = "COMPLETE"
    except Exception as e:
        print(f"Error checking Docker status: {e}")
    
    # Check for results files
    try:
        result_files = list(Path(DOCKER_RESULTS_DIR).glob("*_results.json"))
        if result_files:
            # Get the most recent file
            latest_file = max(result_files, key=os.path.getmtime)
            docker_status["last_update"] = datetime.datetime.fromtimestamp(
                os.path.getmtime(latest_file)
            ).isoformat()
            
            # Read metrics from file
            with open(latest_file, "r") as f:
                metrics = json.load(f)
                docker_status["metrics"] = metrics
                
                # Update test history
                test_name = latest_file.stem.replace("_results", "")
                if test_name not in test_history["docker"]:
                    test_history["docker"][test_name] = []
                test_history["docker"][test_name].append(metrics)
    except Exception as e:
        print(f"Error reading Docker results: {e}")
    
    return docker_status

def get_gcp_metrics():
    """
    Get metrics from GCP test results
    """
    global gcp_status
    
    # Check for results files
    try:
        result_files = list(Path(GCP_RESULTS_DIR).glob("*_results.json"))
        if result_files:
            # Get the most recent file
            latest_file = max(result_files, key=os.path.getmtime)
            gcp_status["last_update"] = datetime.datetime.fromtimestamp(
                os.path.getmtime(latest_file)
            ).isoformat()
            
            # Read metrics from file
            with open(latest_file, "r") as f:
                metrics = json.load(f)
                gcp_status["metrics"] = metrics
                
                # Update test history
                test_name = latest_file.stem.replace("_results", "")
                if test_name not in test_history["gcp"]:
                    test_history["gcp"][test_name] = []
                test_history["gcp"][test_name].append(metrics)
                
            # If we have results, consider it complete
            gcp_status["status"] = "COMPLETE"
    except Exception as e:
        print(f"Error reading GCP results: {e}")
    
    return gcp_status

def analyze(results, env="Docker"):
    """
    Analyze test results and generate alerts
    """
    print(f"\n=== {env} Test Analysis ===")
    print(f"Status: {results['status']}")
    print(f"Last Update: {results['last_update']}")
    
    if "metrics" in results and results["metrics"]:
        metrics = results["metrics"]
        
        # Check if we have test results
        if "test_results" in metrics:
            tests = metrics["test_results"]
            
            # Analyze each test
            for test_name, test_results in tests.items():
                print(f"\n{test_name.upper()}:")
                
                # Check if test passed
                if "meets_target" in test_results:
                    passed = test_results["meets_target"]
                    print(f"  Passed: {passed}")
                
                # Check for specific metrics
                if test_name == "latency":
                    if "average_latency_ms" in test_results:
                        latency = test_results["average_latency_ms"]
                        target = test_results.get("target_latency_ms", 0.07)
                        performance = (target / latency) * 100 if latency > 0 else 0
                        print(f"  Latency: {latency:.6f} ms (Target: {target:.6f} ms)")
                        print(f"  Performance: {performance:.2f}% of target")
                        
                        # Alert if performance is below threshold
                        if performance < ALERT_THRESHOLD:
                            print(f"  ALERT: Latency performance below {ALERT_THRESHOLD}% threshold!")
                
                elif test_name == "throughput":
                    if "events_per_second" in test_results:
                        throughput = test_results["events_per_second"]
                        target = test_results.get("target_throughput", 69000)
                        performance = (throughput / target) * 100 if target > 0 else 0
                        print(f"  Throughput: {throughput:.2f} events/sec (Target: {target} events/sec)")
                        print(f"  Performance: {performance:.2f}% of target")
                        
                        # Alert if performance is below threshold
                        if performance < ALERT_THRESHOLD:
                            print(f"  ALERT: Throughput performance below {ALERT_THRESHOLD}% threshold!")
                
                elif test_name == "resource_allocation":
                    if "improvement_percentage" in test_results:
                        improvement = test_results["improvement_percentage"]
                        print(f"  Improvement: {improvement:.2f}%")
                        
                        # Alert if improvement is negative
                        if improvement < 0:
                            print(f"  ALERT: Resource allocation shows negative improvement!")
                
                elif test_name == "pattern_preservation":
                    if "average_preservation" in test_results:
                        preservation = test_results["average_preservation"]
                        target = test_results.get("target_preservation", 0.85)
                        performance = (preservation / target) * 100 if target > 0 else 0
                        print(f"  Pattern Preservation: {preservation:.4f} (Target: {target:.4f})")
                        print(f"  Performance: {performance:.2f}% of target")
                        
                        # Alert if performance is below threshold
                        if performance < ALERT_THRESHOLD:
                            print(f"  ALERT: Pattern preservation below {ALERT_THRESHOLD}% threshold!")
            
            # Overall assessment
            if "success_rate" in metrics:
                success_rate = metrics["success_rate"]
                print(f"\nOverall Success Rate: {success_rate:.2f}%")
                
                # Alert if success rate is below threshold
                if success_rate < ALERT_THRESHOLD:
                    print(f"ALERT: Overall success rate below {ALERT_THRESHOLD}% threshold!")
            
            # Generate report
            generate_report(env.lower(), metrics)

def generate_report(env, metrics):
    """
    Generate a visual report of test results
    """
    try:
        # Create figure
        plt.figure(figsize=(12, 8))
        plt.suptitle(f"UUFT/CSDE Test Results - {env.upper()}", fontsize=16)
        
        # Get test results
        if "test_results" in metrics:
            tests = metrics["test_results"]
            
            # Plot test results
            test_names = list(tests.keys())
            passed = [tests[t].get("meets_target", False) for t in test_names]
            
            # Create bar chart of test results
            plt.subplot(2, 2, 1)
            plt.bar(test_names, [1 if p else 0 for p in passed], color=['green' if p else 'red' for p in passed])
            plt.title("Test Results")
            plt.ylabel("Passed (1) / Failed (0)")
            plt.xticks(rotation=45, ha='right')
            
            # Plot specific metrics if available
            metrics_to_plot = []
            
            # Latency
            if "latency" in tests and "average_latency_ms" in tests["latency"]:
                latency = tests["latency"]["average_latency_ms"]
                target = tests["latency"].get("target_latency_ms", 0.07)
                metrics_to_plot.append(("Latency (ms)", latency, target))
            
            # Throughput
            if "throughput" in tests and "events_per_second" in tests["throughput"]:
                throughput = tests["throughput"]["events_per_second"]
                target = tests["throughput"].get("target_throughput", 69000)
                metrics_to_plot.append(("Throughput (events/sec)", throughput, target))
            
            # Resource allocation
            if "resource_allocation" in tests and "improvement_percentage" in tests["resource_allocation"]:
                improvement = tests["resource_allocation"]["improvement_percentage"]
                metrics_to_plot.append(("Resource Improvement (%)", improvement, 0))
            
            # Pattern preservation
            if "pattern_preservation" in tests and "average_preservation" in tests["pattern_preservation"]:
                preservation = tests["pattern_preservation"]["average_preservation"]
                target = tests["pattern_preservation"].get("target_preservation", 0.85)
                metrics_to_plot.append(("Pattern Preservation", preservation, target))
            
            # Plot metrics
            if metrics_to_plot:
                plt.subplot(2, 2, 2)
                metric_names = [m[0] for m in metrics_to_plot]
                metric_values = [m[1] for m in metrics_to_plot]
                metric_targets = [m[2] for m in metrics_to_plot]
                
                x = np.arange(len(metric_names))
                width = 0.35
                
                plt.bar(x - width/2, metric_values, width, label='Actual')
                plt.bar(x + width/2, metric_targets, width, label='Target')
                
                plt.title("Performance Metrics")
                plt.xticks(x, metric_names, rotation=45, ha='right')
                plt.legend()
            
            # Plot success rate
            if "success_rate" in metrics:
                plt.subplot(2, 2, 3)
                success_rate = metrics["success_rate"]
                plt.pie([success_rate, 100 - success_rate], 
                        labels=[f"Passed ({success_rate:.1f}%)", f"Failed ({100-success_rate:.1f}%)"],
                        colors=['green', 'red'], autopct='%1.1f%%')
                plt.title("Overall Success Rate")
            
            # Plot test history if available
            if env in test_history and test_history[env]:
                plt.subplot(2, 2, 4)
                history_data = []
                
                for test_name, history in test_history[env].items():
                    if history:
                        # Extract a key metric for each test type
                        if test_name == "latency" and "average_latency_ms" in history[-1]:
                            history_data.append((test_name, history[-1]["average_latency_ms"], "ms"))
                        elif test_name == "throughput" and "events_per_second" in history[-1]:
                            history_data.append((test_name, history[-1]["events_per_second"], "events/sec"))
                        elif test_name == "resource_allocation" and "improvement_percentage" in history[-1]:
                            history_data.append((test_name, history[-1]["improvement_percentage"], "%"))
                        elif test_name == "pattern_preservation" and "average_preservation" in history[-1]:
                            history_data.append((test_name, history[-1]["average_preservation"], "score"))
                
                if history_data:
                    plt.bar([h[0] for h in history_data], [h[1] for h in history_data])
                    plt.title("Latest Test Metrics")
                    plt.xticks(rotation=45, ha='right')
                    
                    # Add value labels
                    for i, (name, value, unit) in enumerate(history_data):
                        plt.text(i, value, f"{value:.2f} {unit}", ha='center', va='bottom')
            
            # Save figure
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            plt.tight_layout()
            plt.savefig(f"monitor_reports/{env}_report_{timestamp}.png")
            plt.close()
            
            print(f"Report saved to monitor_reports/{env}_report_{timestamp}.png")
    except Exception as e:
        print(f"Error generating report: {e}")

def monitor_uuft_tests():
    """
    Monitor UUFT tests in Docker and GCP environments
    """
    print("Starting UUFT Test Supervisor...")
    print(f"Monitoring interval: {MONITOR_INTERVAL} seconds")
    print(f"Alert threshold: {ALERT_THRESHOLD}%")
    
    while True:
        try:
            # Get metrics
            docker_results = get_docker_metrics()
            gcp_results = get_gcp_metrics()
            
            # Print status
            print(f"\n=== UUFT Test Status ({datetime.datetime.now().isoformat()}) ===")
            print(f"Docker: {docker_results['status']}")
            print(f"GCP: {gcp_results['status']}")
            
            # Analyze results if complete
            if docker_results["status"] == "COMPLETE":
                analyze(docker_results, env="Docker")
            
            if gcp_results["status"] == "COMPLETE":
                analyze(gcp_results, env="GCP")
            
            # Wait for next check
            time.sleep(MONITOR_INTERVAL)
        except KeyboardInterrupt:
            print("\nMonitoring stopped by user.")
            break
        except Exception as e:
            print(f"Error in monitoring loop: {e}")
            time.sleep(MONITOR_INTERVAL)

if __name__ == "__main__":
    monitor_uuft_tests()
