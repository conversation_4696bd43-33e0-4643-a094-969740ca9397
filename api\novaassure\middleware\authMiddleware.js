/**
 * Authentication Middleware
 * 
 * This middleware handles authentication for the NovaAssure API.
 */

const jwt = require('jsonwebtoken');
const config = require('../../config');
const logger = require('../utils/logger');

/**
 * Authenticate user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function authenticate(req, res, next) {
  try {
    // Get authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      logger.warn('Missing authorization header', { path: req.path, ip: req.ip });
      return res.status(401).json({ error: 'Authentication required' });
    }
    
    // Extract token
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : authHeader;
    
    // Verify token
    jwt.verify(token, config.jwt.secretKey, (err, decoded) => {
      if (err) {
        logger.warn('Invalid token', { path: req.path, ip: req.ip, error: err.message });
        return res.status(401).json({ error: 'Invalid token' });
      }
      
      // Set user in request
      req.user = decoded;
      
      // Log successful authentication
      logger.info('User authenticated', { 
        userId: decoded.id, 
        path: req.path
      });
      
      next();
    });
  } catch (error) {
    logger.error('Authentication error', { path: req.path, ip: req.ip, error: error.message });
    return res.status(500).json({ error: 'Authentication error' });
  }
}

/**
 * Check if user has required role
 * @param {string[]} roles - Required roles
 * @returns {Function} - Middleware function
 */
function hasRole(roles) {
  return (req, res, next) => {
    try {
      // Check if user is authenticated
      if (!req.user) {
        logger.warn('User not authenticated', { path: req.path, ip: req.ip });
        return res.status(401).json({ error: 'Authentication required' });
      }
      
      // Check if user has required role
      if (!roles.includes(req.user.role)) {
        logger.warn('Insufficient permissions', { 
          userId: req.user.id, 
          path: req.path, 
          userRole: req.user.role, 
          requiredRoles: roles 
        });
        return res.status(403).json({ error: 'Insufficient permissions' });
      }
      
      next();
    } catch (error) {
      logger.error('Role check error', { path: req.path, ip: req.ip, error: error.message });
      return res.status(500).json({ error: 'Authorization error' });
    }
  };
}

/**
 * Check if user has permission
 * @param {string} permission - Required permission
 * @returns {Function} - Middleware function
 */
function hasPermission(permission) {
  return (req, res, next) => {
    try {
      // Check if user is authenticated
      if (!req.user) {
        logger.warn('User not authenticated', { path: req.path, ip: req.ip });
        return res.status(401).json({ error: 'Authentication required' });
      }
      
      // Check if user has required permission
      if (!req.user.permissions || !req.user.permissions.includes(permission)) {
        logger.warn('Insufficient permissions', { 
          userId: req.user.id, 
          path: req.path, 
          requiredPermission: permission 
        });
        return res.status(403).json({ error: 'Insufficient permissions' });
      }
      
      next();
    } catch (error) {
      logger.error('Permission check error', { path: req.path, ip: req.ip, error: error.message });
      return res.status(500).json({ error: 'Authorization error' });
    }
  };
}

module.exports = {
  authenticate,
  hasRole,
  hasPermission
};
