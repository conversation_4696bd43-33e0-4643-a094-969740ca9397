import axios from 'axios';

// API URL
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

/**
 * Get all available frameworks
 * @returns {Promise<Object>} - All available frameworks
 */
export const getAllFrameworks = async () => {
  try {
    const response = await axios.get(`${API_URL}/mapping/frameworks`);
    return response.data;
  } catch (error) {
    console.error('Error getting frameworks:', error);
    throw error;
  }
};

/**
 * Get a specific framework by ID
 * @param {string} frameworkId - ID of the framework to retrieve
 * @returns {Promise<Object>} - Framework details
 */
export const getFramework = async (frameworkId) => {
  try {
    const response = await axios.get(`${API_URL}/mapping/frameworks/${frameworkId}`);
    return response.data;
  } catch (error) {
    console.error(`Error getting framework ${frameworkId}:`, error);
    throw error;
  }
};

/**
 * Map a control from one framework to another
 * @param {string} sourceFramework - Source framework ID
 * @param {string} sourceControl - Source control ID
 * @param {string} targetFramework - Target framework ID
 * @returns {Promise<Object>} - Mapping result
 */
export const mapControl = async (sourceFramework, sourceControl, targetFramework) => {
  try {
    const response = await axios.get(`${API_URL}/mapping/${sourceFramework}/${sourceControl}/${targetFramework}`);
    return response.data;
  } catch (error) {
    console.error(`Error mapping control ${sourceControl} from ${sourceFramework} to ${targetFramework}:`, error);
    throw error;
  }
};

/**
 * Map multiple controls from one framework to another
 * @param {string} sourceFramework - Source framework ID
 * @param {Array<string>} sourceControls - Array of source control IDs
 * @param {string} targetFramework - Target framework ID
 * @returns {Promise<Array<Object>>} - Array of mapping results
 */
export const mapControls = async (sourceFramework, sourceControls, targetFramework) => {
  try {
    const response = await axios.post(`${API_URL}/mapping/${sourceFramework}/${targetFramework}`, {
      sourceControls
    });
    return response.data;
  } catch (error) {
    console.error(`Error mapping controls from ${sourceFramework} to ${targetFramework}:`, error);
    throw error;
  }
};

/**
 * Get all possible mappings for a control
 * @param {string} sourceFramework - Source framework ID
 * @param {string} sourceControl - Source control ID
 * @returns {Promise<Object>} - All possible mappings for the control
 */
export const getAllMappingsForControl = async (sourceFramework, sourceControl) => {
  try {
    const response = await axios.get(`${API_URL}/mapping/${sourceFramework}/${sourceControl}/all`);
    return response.data;
  } catch (error) {
    console.error(`Error getting all mappings for control ${sourceControl} from ${sourceFramework}:`, error);
    throw error;
  }
};

/**
 * Get all mappings between two frameworks
 * @param {string} sourceFramework - Source framework ID
 * @param {string} targetFramework - Target framework ID
 * @returns {Promise<Object>} - All mappings between the two frameworks
 */
export const getFrameworkToFrameworkMappings = async (sourceFramework, targetFramework) => {
  try {
    const response = await axios.get(`${API_URL}/mapping/${sourceFramework}/to/${targetFramework}`);
    return response.data;
  } catch (error) {
    console.error(`Error getting mappings from ${sourceFramework} to ${targetFramework}:`, error);
    throw error;
  }
};

/**
 * Calculate compliance coverage across frameworks
 * @param {string} sourceFramework - Source framework ID
 * @param {Array<string>} implementedControls - Array of implemented control IDs
 * @param {string} targetFramework - Target framework ID
 * @returns {Promise<Object>} - Compliance coverage calculation
 */
export const calculateComplianceCoverage = async (sourceFramework, implementedControls, targetFramework) => {
  try {
    const response = await axios.post(`${API_URL}/mapping/${sourceFramework}/coverage/${targetFramework}`, {
      implementedControls
    });
    return response.data;
  } catch (error) {
    console.error(`Error calculating compliance coverage from ${sourceFramework} to ${targetFramework}:`, error);
    throw error;
  }
};
