/**
 * NovaFuse Universal API Connector Authentication Manager
 * 
 * This module handles authentication for API connectors, supporting various
 * authentication methods including API Key, Basic Auth, OAuth2, JWT, and AWS Signature V4.
 */

const crypto = require('crypto');
const axios = require('axios');
const qs = require('querystring');

// Secure credential vault (in a production environment, this would use a proper secret manager)
const credentialVault = new Map();

class AuthenticationManager {
  constructor() {
    this.authHandlers = {
      'API_KEY': this.handleApiKeyAuth.bind(this),
      'BASIC': this.handleBasicAuth.bind(this),
      'OAUTH2': this.handleOAuth2Auth.bind(this),
      'JWT': this.handleJwtAuth.bind(this),
      'AWS_SIG_V4': this.handleAwsSigV4Auth.bind(this),
      'CUSTOM': this.handleCustomAuth.bind(this)
    };
  }

  /**
   * Initialize the authentication manager
   */
  async initialize() {
    console.log('Authentication Manager initialized');
    return true;
  }

  /**
   * Store credentials securely
   * 
   * @param {string} connectorId - The ID of the connector
   * @param {Object} credentials - The credentials to store
   * @returns {string} - The credential ID
   */
  storeCredentials(connectorId, credentials) {
    // Generate a unique credential ID
    const credentialId = `${connectorId}-${Date.now()}-${crypto.randomBytes(4).toString('hex')}`;
    
    // Encrypt sensitive fields (in a production environment, this would use proper encryption)
    const encryptedCredentials = this.encryptSensitiveFields(credentials);
    
    // Store the credentials
    credentialVault.set(credentialId, {
      connectorId,
      credentials: encryptedCredentials,
      createdAt: new Date().toISOString()
    });
    
    return credentialId;
  }

  /**
   * Retrieve credentials
   * 
   * @param {string} credentialId - The ID of the credentials to retrieve
   * @returns {Object|null} - The decrypted credentials or null if not found
   */
  getCredentials(credentialId) {
    const entry = credentialVault.get(credentialId);
    
    if (!entry) {
      return null;
    }
    
    // Decrypt sensitive fields
    return this.decryptSensitiveFields(entry.credentials);
  }

  /**
   * Delete credentials
   * 
   * @param {string} credentialId - The ID of the credentials to delete
   * @returns {boolean} - Whether the deletion was successful
   */
  deleteCredentials(credentialId) {
    return credentialVault.delete(credentialId);
  }

  /**
   * Encrypt sensitive fields in credentials
   * 
   * @param {Object} credentials - The credentials to encrypt
   * @returns {Object} - The credentials with sensitive fields encrypted
   */
  encryptSensitiveFields(credentials) {
    // In a production environment, this would use proper encryption
    // For now, we'll just mark sensitive fields
    const encryptedCredentials = { ...credentials };
    
    for (const [key, value] of Object.entries(credentials)) {
      if (key.includes('secret') || key.includes('password') || key.includes('token') || key.includes('key')) {
        encryptedCredentials[key] = `[ENCRYPTED]${value}`;
      }
    }
    
    return encryptedCredentials;
  }

  /**
   * Decrypt sensitive fields in credentials
   * 
   * @param {Object} encryptedCredentials - The credentials with sensitive fields encrypted
   * @returns {Object} - The decrypted credentials
   */
  decryptSensitiveFields(encryptedCredentials) {
    // In a production environment, this would use proper decryption
    // For now, we'll just unmark sensitive fields
    const decryptedCredentials = { ...encryptedCredentials };
    
    for (const [key, value] of Object.entries(encryptedCredentials)) {
      if (typeof value === 'string' && value.startsWith('[ENCRYPTED]')) {
        decryptedCredentials[key] = value.substring(11);
      }
    }
    
    return decryptedCredentials;
  }

  /**
   * Authenticate a request based on the connector's authentication configuration
   * 
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @param {Object} request - The request to authenticate
   * @returns {Object} - The authenticated request
   */
  async authenticateRequest(connector, credentials, request) {
    const authType = connector.authentication.type;
    
    if (!this.authHandlers[authType]) {
      throw new Error(`Unsupported authentication type: ${authType}`);
    }
    
    return this.authHandlers[authType](connector, credentials, request);
  }

  /**
   * Test a connection using the provided credentials
   * 
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @returns {Object} - The test result
   */
  async testConnection(connector, credentials) {
    try {
      const testConfig = connector.authentication.testConnection;
      
      if (!testConfig) {
        throw new Error('No test connection configuration found');
      }
      
      // Build the test request
      const request = {
        method: testConfig.method,
        url: `${connector.configuration.baseUrl}${testConfig.endpoint}`,
        headers: { ...connector.configuration.headers }
      };
      
      // Authenticate the request
      const authenticatedRequest = await this.authenticateRequest(connector, credentials, request);
      
      // Send the request
      const response = await axios(authenticatedRequest);
      
      // Check if the response matches the expected pattern
      if (testConfig.expectedResponse) {
        if (testConfig.expectedResponse.status && response.status !== testConfig.expectedResponse.status) {
          return {
            success: false,
            message: `Expected status ${testConfig.expectedResponse.status}, got ${response.status}`
          };
        }
        
        if (testConfig.expectedResponse.body) {
          // TODO: Implement response body validation
        }
      }
      
      return {
        success: true,
        message: 'Connection test successful',
        data: {
          status: response.status,
          statusText: response.statusText
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `Connection test failed: ${error.message}`,
        error: error.response ? {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        } : error
      };
    }
  }

  /**
   * Handle API Key authentication
   * 
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @param {Object} request - The request to authenticate
   * @returns {Object} - The authenticated request
   */
  handleApiKeyAuth(connector, credentials, request) {
    const authConfig = connector.authentication;
    const fields = authConfig.fields;
    
    // Determine where to place the API key (header, query, etc.)
    const placement = authConfig.placement || 'header';
    const paramName = authConfig.paramName || 'x-api-key';
    const apiKey = credentials[Object.keys(fields)[0]]; // Assume the first field is the API key
    
    if (!apiKey) {
      throw new Error('API key not provided');
    }
    
    const authenticatedRequest = { ...request };
    
    if (placement === 'header') {
      authenticatedRequest.headers = {
        ...authenticatedRequest.headers,
        [paramName]: apiKey
      };
    } else if (placement === 'query') {
      const url = new URL(authenticatedRequest.url);
      url.searchParams.append(paramName, apiKey);
      authenticatedRequest.url = url.toString();
    }
    
    return authenticatedRequest;
  }

  /**
   * Handle Basic authentication
   * 
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @param {Object} request - The request to authenticate
   * @returns {Object} - The authenticated request
   */
  handleBasicAuth(connector, credentials, request) {
    const authConfig = connector.authentication;
    const fields = authConfig.fields;
    
    // Get username and password fields
    const usernameField = Object.keys(fields).find(key => 
      key.includes('username') || key.includes('user') || key.includes('email')
    );
    
    const passwordField = Object.keys(fields).find(key => 
      key.includes('password') || key.includes('secret') || key.includes('token')
    );
    
    if (!usernameField || !passwordField) {
      throw new Error('Username or password field not found in authentication configuration');
    }
    
    const username = credentials[usernameField];
    const password = credentials[passwordField];
    
    if (!username || !password) {
      throw new Error('Username or password not provided');
    }
    
    // Create Basic Auth header
    const base64Credentials = Buffer.from(`${username}:${password}`).toString('base64');
    
    return {
      ...request,
      headers: {
        ...request.headers,
        'Authorization': `Basic ${base64Credentials}`
      }
    };
  }

  /**
   * Handle OAuth2 authentication
   * 
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @param {Object} request - The request to authenticate
   * @returns {Object} - The authenticated request
   */
  async handleOAuth2Auth(connector, credentials, request) {
    const authConfig = connector.authentication;
    const oauth2Config = authConfig.oauth2Config;
    
    if (!oauth2Config) {
      throw new Error('OAuth2 configuration not found');
    }
    
    // Check if we have a valid access token
    if (credentials.access_token && credentials.expires_at && new Date(credentials.expires_at) > new Date()) {
      return {
        ...request,
        headers: {
          ...request.headers,
          'Authorization': `Bearer ${credentials.access_token}`
        }
      };
    }
    
    // If not, get a new access token
    let tokenResponse;
    
    if (oauth2Config.grantType === 'client_credentials') {
      // Client credentials flow
      tokenResponse = await this.getClientCredentialsToken(oauth2Config, credentials);
    } else if (oauth2Config.grantType === 'password') {
      // Password flow
      tokenResponse = await this.getPasswordToken(oauth2Config, credentials);
    } else if (oauth2Config.grantType === 'authorization_code') {
      // Authorization code flow (requires user interaction, not implemented here)
      throw new Error('Authorization code flow not implemented');
    } else {
      throw new Error(`Unsupported OAuth2 grant type: ${oauth2Config.grantType}`);
    }
    
    // Update credentials with new token
    credentials.access_token = tokenResponse.access_token;
    credentials.refresh_token = tokenResponse.refresh_token;
    credentials.expires_at = new Date(Date.now() + tokenResponse.expires_in * 1000).toISOString();
    
    // Return authenticated request
    return {
      ...request,
      headers: {
        ...request.headers,
        'Authorization': `Bearer ${tokenResponse.access_token}`
      }
    };
  }

  /**
   * Get an OAuth2 token using the client credentials grant type
   * 
   * @param {Object} oauth2Config - The OAuth2 configuration
   * @param {Object} credentials - The authentication credentials
   * @returns {Object} - The token response
   */
  async getClientCredentialsToken(oauth2Config, credentials) {
    const tokenUrl = oauth2Config.tokenUrl;
    const clientId = credentials.client_id;
    const clientSecret = credentials.client_secret;
    
    if (!tokenUrl || !clientId || !clientSecret) {
      throw new Error('Missing required OAuth2 parameters');
    }
    
    const data = {
      grant_type: 'client_credentials',
      client_id: clientId,
      client_secret: clientSecret
    };
    
    if (oauth2Config.scopes && oauth2Config.scopes.length > 0) {
      data.scope = oauth2Config.scopes.join(' ');
    }
    
    const response = await axios.post(tokenUrl, qs.stringify(data), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    return response.data;
  }

  /**
   * Get an OAuth2 token using the password grant type
   * 
   * @param {Object} oauth2Config - The OAuth2 configuration
   * @param {Object} credentials - The authentication credentials
   * @returns {Object} - The token response
   */
  async getPasswordToken(oauth2Config, credentials) {
    const tokenUrl = oauth2Config.tokenUrl;
    const clientId = credentials.client_id;
    const clientSecret = credentials.client_secret;
    const username = credentials.username;
    const password = credentials.password;
    
    if (!tokenUrl || !clientId || !clientSecret || !username || !password) {
      throw new Error('Missing required OAuth2 parameters');
    }
    
    const data = {
      grant_type: 'password',
      client_id: clientId,
      client_secret: clientSecret,
      username: username,
      password: password
    };
    
    if (oauth2Config.scopes && oauth2Config.scopes.length > 0) {
      data.scope = oauth2Config.scopes.join(' ');
    }
    
    const response = await axios.post(tokenUrl, qs.stringify(data), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    return response.data;
  }

  /**
   * Handle JWT authentication
   * 
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @param {Object} request - The request to authenticate
   * @returns {Object} - The authenticated request
   */
  handleJwtAuth(connector, credentials, request) {
    // For now, just use the provided JWT token
    const token = credentials.token;
    
    if (!token) {
      throw new Error('JWT token not provided');
    }
    
    return {
      ...request,
      headers: {
        ...request.headers,
        'Authorization': `Bearer ${token}`
      }
    };
  }

  /**
   * Handle AWS Signature V4 authentication
   * 
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @param {Object} request - The request to authenticate
   * @returns {Object} - The authenticated request
   */
  handleAwsSigV4Auth(connector, credentials, request) {
    // This is a simplified implementation of AWS Signature V4
    // In a production environment, use the AWS SDK
    
    const accessKeyId = credentials.accessKeyId;
    const secretAccessKey = credentials.secretAccessKey;
    const region = credentials.region;
    const service = credentials.service || 'execute-api';
    
    if (!accessKeyId || !secretAccessKey || !region) {
      throw new Error('Missing required AWS credentials');
    }
    
    // For now, just add the credentials as headers
    // In a real implementation, this would calculate the signature
    return {
      ...request,
      headers: {
        ...request.headers,
        'X-Amz-Date': new Date().toISOString().replace(/[:-]|\.\d{3}/g, ''),
        'X-Amz-Security-Token': credentials.sessionToken || '',
        'X-Api-Key': accessKeyId
      }
    };
  }

  /**
   * Handle custom authentication
   * 
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @param {Object} request - The request to authenticate
   * @returns {Object} - The authenticated request
   */
  handleCustomAuth(connector, credentials, request) {
    // Custom authentication is implemented by the connector
    // This is just a placeholder
    return request;
  }
}

// Create and export a singleton instance
const authenticationManager = new AuthenticationManager();

module.exports = authenticationManager;
