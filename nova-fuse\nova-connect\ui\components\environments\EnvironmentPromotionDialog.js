/**
 * Environment Promotion Dialog Component
 * 
 * This component allows users to promote configuration from one environment to another.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Checkbox,
  Chip,
  CircularProgress,
  Dialog, 
  DialogActions, 
  DialogContent, 
  DialogTitle, 
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormLabel,
  Grid,
  MenuItem,
  Paper,
  Select,
  Typography
} from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { useEnvironment } from '../../contexts/EnvironmentContext';
import { environmentApi } from '../../services/api';

const EnvironmentPromotionDialog = ({ open, onClose, sourceEnvironment }) => {
  const { environments } = useEnvironment();
  
  const [targetEnvironmentId, setTargetEnvironmentId] = useState('');
  const [options, setOptions] = useState({
    includeConnectors: true,
    includeConfig: true,
    includeCredentials: false
  });
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  
  // Reset form when dialog opens or source environment changes
  React.useEffect(() => {
    if (open) {
      setTargetEnvironmentId('');
      setOptions({
        includeConnectors: true,
        includeConfig: true,
        includeCredentials: false
      });
      setResult(null);
    }
  }, [open, sourceEnvironment]);
  
  // Handle target environment change
  const handleTargetChange = (event) => {
    setTargetEnvironmentId(event.target.value);
  };
  
  // Handle option change
  const handleOptionChange = (event) => {
    setOptions({
      ...options,
      [event.target.name]: event.target.checked
    });
  };
  
  // Handle promotion
  const handlePromote = async () => {
    if (!sourceEnvironment || !targetEnvironmentId) {
      return;
    }
    
    try {
      setLoading(true);
      setResult(null);
      
      // Promote connectors if selected
      let connectorResult = null;
      if (options.includeConnectors) {
        const response = await environmentApi.promoteConnectors(
          sourceEnvironment.id,
          targetEnvironmentId
        );
        connectorResult = response.data;
      }
      
      // Promote environment (configuration) if selected
      let configResult = null;
      if (options.includeConfig) {
        const response = await environmentApi.promoteEnvironment(
          sourceEnvironment.id,
          targetEnvironmentId
        );
        configResult = response.data;
      }
      
      // Set result
      setResult({
        connectors: connectorResult,
        config: configResult
      });
    } catch (error) {
      console.error('Error promoting environment:', error);
      alert(`Failed to promote environment: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  // Get environment by ID
  const getEnvironmentById = (id) => {
    return environments.find(env => env.id === id) || null;
  };
  
  // Get environment color
  const getEnvironmentColor = (environment) => {
    return environment?.color || '#2196f3';
  };
  
  // Get target environment
  const targetEnvironment = getEnvironmentById(targetEnvironmentId);
  
  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
    >
      <DialogTitle>
        Promote Environment
      </DialogTitle>
      
      <DialogContent>
        {!result ? (
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Promote configuration from one environment to another
            </Typography>
            
            <Typography variant="body2" color="textSecondary" paragraph>
              This will copy selected components from the source environment to the target environment.
              Existing components in the target environment may be overwritten.
            </Typography>
            
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={12} md={5}>
                <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Source Environment
                  </Typography>
                  
                  {sourceEnvironment && (
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                      <Chip
                        size="small"
                        label=""
                        sx={{
                          bgcolor: getEnvironmentColor(sourceEnvironment),
                          width: 16,
                          height: 16,
                          mr: 1
                        }}
                      />
                      <Typography variant="body1">
                        {sourceEnvironment.name}
                      </Typography>
                    </Box>
                  )}
                </Paper>
              </Grid>
              
              <Grid item xs={12} md={2} sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <ArrowForwardIcon fontSize="large" color="action" />
              </Grid>
              
              <Grid item xs={12} md={5}>
                <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Target Environment
                  </Typography>
                  
                  <FormControl fullWidth sx={{ mt: 2 }}>
                    <Select
                      value={targetEnvironmentId}
                      onChange={handleTargetChange}
                      displayEmpty
                    >
                      <MenuItem value="" disabled>
                        Select target environment
                      </MenuItem>
                      {environments
                        .filter(env => env.id !== sourceEnvironment?.id)
                        .map((env) => (
                          <MenuItem key={env.id} value={env.id}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Chip
                                size="small"
                                label=""
                                sx={{
                                  bgcolor: getEnvironmentColor(env),
                                  width: 16,
                                  height: 16,
                                  mr: 1
                                }}
                              />
                              {env.name}
                            </Box>
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </Paper>
              </Grid>
              
              <Grid item xs={12}>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <FormControl component="fieldset">
                    <FormLabel component="legend">Promotion Options</FormLabel>
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={options.includeConnectors}
                            onChange={handleOptionChange}
                            name="includeConnectors"
                          />
                        }
                        label="Include Connectors"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={options.includeConfig}
                            onChange={handleOptionChange}
                            name="includeConfig"
                          />
                        }
                        label="Include Configuration"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={options.includeCredentials}
                            onChange={handleOptionChange}
                            name="includeCredentials"
                          />
                        }
                        label="Include Credentials (use with caution)"
                      />
                    </FormGroup>
                  </FormControl>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        ) : (
          <Box>
            <Typography variant="h6" gutterBottom>
              Promotion Complete
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Typography variant="body1" sx={{ mr: 1 }}>
                From:
              </Typography>
              <Chip
                size="small"
                label=""
                sx={{
                  bgcolor: getEnvironmentColor(sourceEnvironment),
                  width: 16,
                  height: 16,
                  mr: 1
                }}
              />
              <Typography variant="body1" sx={{ mr: 3 }}>
                {sourceEnvironment.name}
              </Typography>
              
              <Typography variant="body1" sx={{ mr: 1 }}>
                To:
              </Typography>
              <Chip
                size="small"
                label=""
                sx={{
                  bgcolor: getEnvironmentColor(targetEnvironment),
                  width: 16,
                  height: 16,
                  mr: 1
                }}
              />
              <Typography variant="body1">
                {targetEnvironment.name}
              </Typography>
            </Box>
            
            <Divider sx={{ my: 2 }} />
            
            {result.connectors && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Connector Promotion Results
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="success.main">
                        {result.connectors.created.length}
                      </Typography>
                      <Typography variant="body2">
                        Connectors Created
                      </Typography>
                    </Paper>
                  </Grid>
                  
                  <Grid item xs={12} sm={4}>
                    <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="info.main">
                        {result.connectors.updated.length}
                      </Typography>
                      <Typography variant="body2">
                        Connectors Updated
                      </Typography>
                    </Paper>
                  </Grid>
                  
                  <Grid item xs={12} sm={4}>
                    <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="text.secondary">
                        {result.connectors.unchanged.length}
                      </Typography>
                      <Typography variant="body2">
                        Connectors Unchanged
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
                
                {result.connectors.errors.length > 0 && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2" color="error">
                      Errors ({result.connectors.errors.length})
                    </Typography>
                    
                    <Paper variant="outlined" sx={{ p: 2, mt: 1, bgcolor: 'error.light' }}>
                      {result.connectors.errors.map((error, index) => (
                        <Typography key={index} variant="body2">
                          {error.connector.name}: {error.error}
                        </Typography>
                      ))}
                    </Paper>
                  </Box>
                )}
              </Box>
            )}
            
            {result.config && (
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Configuration Promotion Results
                </Typography>
                
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="body2">
                    Status: {result.config.status}
                  </Typography>
                  
                  {result.config.filesCopied && (
                    <Typography variant="body2">
                      Files Copied: {result.config.filesCopied}
                    </Typography>
                  )}
                </Paper>
              </Box>
            )}
          </Box>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>
          {result ? 'Close' : 'Cancel'}
        </Button>
        
        {!result && (
          <Button 
            onClick={handlePromote} 
            variant="contained" 
            disabled={loading || !targetEnvironmentId || (!options.includeConnectors && !options.includeConfig)}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'Promoting...' : 'Promote'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default EnvironmentPromotionDialog;
