import React from 'react';

/**
 * Partner DNA Analysis Component
 * 
 * This component displays a personalized analysis of a partner's needs, pain points,
 * and strategic objectives based on the "Partner DNA" analysis.
 */
const PartnerDNAAnalysis = ({
  partner = {},
  painPoints = [],
  strategicObjectives = [],
  competitiveAdvantages = [],
  integrationPoints = []
}) => {
  return (
    <div className="partner-dna-analysis">
      <div className="bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-6">
        <div className="flex items-center mb-4">
          <div className="bg-white bg-opacity-10 p-2 rounded-full mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold">{partner.name} DNA Analysis</h2>
        </div>
        
        <p className="text-gray-300 mb-6">
          Based on our analysis of {partner.name}'s public data, leadership interviews, and market positioning, we've identified the following key insights that inform our integration strategy.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Pain Points */}
          <div className="bg-blue-800 bg-opacity-30 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <span className="text-red-400 mr-2">⚠️</span>
              <span>Pain Points</span>
            </h3>
            {painPoints.length > 0 ? (
              <ul className="space-y-2">
                {painPoints.map((point, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-red-400 mr-2">•</span>
                    <span className="text-gray-300">{point}</span>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-400 italic">No pain points identified.</p>
            )}
          </div>
          
          {/* Strategic Objectives */}
          <div className="bg-blue-800 bg-opacity-30 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <span className="text-blue-400 mr-2">🎯</span>
              <span>Strategic Objectives</span>
            </h3>
            {strategicObjectives.length > 0 ? (
              <ul className="space-y-2">
                {strategicObjectives.map((objective, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-blue-400 mr-2">•</span>
                    <span className="text-gray-300">{objective}</span>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-400 italic">No strategic objectives identified.</p>
            )}
          </div>
          
          {/* Competitive Advantages */}
          <div className="bg-blue-800 bg-opacity-30 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <span className="text-green-400 mr-2">💪</span>
              <span>Competitive Advantages</span>
            </h3>
            {competitiveAdvantages.length > 0 ? (
              <ul className="space-y-2">
                {competitiveAdvantages.map((advantage, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-green-400 mr-2">•</span>
                    <span className="text-gray-300">{advantage}</span>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-400 italic">No competitive advantages identified.</p>
            )}
          </div>
          
          {/* Integration Points */}
          <div className="bg-blue-800 bg-opacity-30 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <span className="text-purple-400 mr-2">🔌</span>
              <span>Integration Points</span>
            </h3>
            {integrationPoints.length > 0 ? (
              <ul className="space-y-2">
                {integrationPoints.map((point, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-purple-400 mr-2">•</span>
                    <span className="text-gray-300">{point}</span>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-400 italic">No integration points identified.</p>
            )}
          </div>
        </div>
        
        {/* NovaFuse Value Proposition */}
        {partner.valueProposition && (
          <div className="mt-6 bg-blue-700 bg-opacity-30 border border-blue-500 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-2">NovaFuse Value Proposition</h3>
            <p className="text-gray-300 italic">"{partner.valueProposition}"</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PartnerDNAAnalysis;
