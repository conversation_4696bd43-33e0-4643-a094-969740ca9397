.nova-vision-trinity-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: '<PERSON><PERSON>', 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

.nova-vision-trinity-demo-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #1a1a2e;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: #2196F3;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.nova-vision-trinity-demo-error {
  max-width: 600px;
  margin: 100px auto;
  padding: 20px;
  background-color: #f44336;
  color: white;
  border-radius: 8px;
  text-align: center;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h1 {
  margin: 0;
  font-size: 32px;
  background: linear-gradient(45deg, #4CAF50, #2196F3, #9C27B0);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientShift 5s ease infinite;
}

.demo-header p {
  margin: 10px 0 0;
  font-size: 18px;
  color: #666;
}

.demo-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  gap: 15px;
}

.mode-button {
  background-color: #f5f5f5;
  color: #333;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.mode-button:hover {
  background-color: #e0e0e0;
}

.mode-button.active {
  background-color: #2196F3;
  color: white;
}

.logging-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-left: 20px;
}

.logging-toggle input {
  margin-right: 8px;
}

.demo-content {
  margin-bottom: 30px;
}

.demo-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ddd;
  color: #666;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .demo-controls {
    flex-direction: column;
    gap: 10px;
  }
  
  .mode-button {
    width: 100%;
  }
  
  .logging-toggle {
    margin-left: 0;
    margin-top: 10px;
  }
}
