{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 1, "numPassedTests": 7, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 7, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1747982249841, "success": false, "testResults": [{"leaks": false, "numFailingTests": 0, "numPassingTests": 7, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1747982254674, "runtime": 3815, "slow": false, "start": 1747982250859}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\novaproof\\evidence-verification.test.js", "testResults": [{"ancestorTitles": ["NovaProof API Integration Tests", "Evidence Endpoints"], "duration": 51, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Evidence Endpoints GET /api/v1/evidence should return a list of evidence items", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "GET /api/v1/evidence should return a list of evidence items"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Evidence Endpoints"], "duration": 87, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Evidence Endpoints POST /api/v1/evidence should create a new evidence item", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "POST /api/v1/evidence should create a new evidence item"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Evidence Endpoints"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Evidence Endpoints GET /api/v1/evidence/:id should return a specific evidence item", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "GET /api/v1/evidence/:id should return a specific evidence item"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Verification Endpoints"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Verification Endpoints POST /api/v1/verification should verify an evidence item", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "POST /api/v1/verification should verify an evidence item"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Verification Endpoints"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Verification Endpoints GET /api/v1/verification should return a list of verifications", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "GET /api/v1/verification should return a list of verifications"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Verification Endpoints"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Verification Endpoints GET /api/v1/verification/:id should return a specific verification", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "GET /api/v1/verification/:id should return a specific verification"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Verification Endpoints"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Verification Endpoints GET /api/v1/verification/:id/proof should return the proof for a verification", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "GET /api/v1/verification/:id/proof should return the proof for a verification"}], "failureMessage": null}], "wasInterrupted": false}