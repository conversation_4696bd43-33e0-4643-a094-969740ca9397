# RoseTTAFold Engine

A high-performance, hybrid quantum-classical protein folding engine based on RoseTTAFold with consciousness-aware optimizations.

## Features

- **Hybrid Quantum-Classical Computation**: Leverage quantum computing for enhanced protein structure prediction
- **Multiple Operation Modes**:
  - `classical`: Standard RoseTTAFold execution
  - `hybrid`: Quantum-enhanced classical computation (recommended)
  - `quantum`: Full quantum mode (experimental)
- **Consciousness-Aware Folding**: Ψ-score optimization for biologically relevant structures
- **Fibonacci Constraints**: Ensure structural stability using golden ratio constraints
- **GPU Acceleration**: Optimized for NVIDIA GPUs with CUDA support
- **Flexible Backend Support**: Compatible with multiple quantum backends (qsim, qiskit, etc.)

## Installation

1. Install the required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Set up RoseTTAFold:
   ```bash
   git clone https://github.com/RosettaCommons/RoseTTAFold.git
   cd RoseTTAFold
   ./install_dependencies.sh
   ```

3. Set environment variables:
   ```bash
   export ROSETTAFOLD_PATH=/path/to/rosettafold
   ```

## Quick Start

```python
from src.rosettafold_engine import RoseTTAFoldEngine

# Initialize with hybrid mode (quantum-enhanced classical computation)
engine = RoseTTAFoldEngine(
    config={
        'mode': 'hybrid',
        'rosettafold_path': '/path/to/rosettafold',
        'output_dir': './output',
        'gpu_id': 0,
        'psi_optimization': True,
        'fib_constraints': {
            'enabled': True,
            'tolerance': 0.1
        }
    }
)

# Run prediction
result = engine.predict("ACDEFGHIKLMNPQRSTVWY")
print(f"Predicted structure saved to: {result['pdb_path']}")
```

## Configuration Options

### Core Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `mode` | str | 'hybrid' | Operation mode: 'classical', 'hybrid', or 'quantum' |
| `rosettafold_path` | str | Required | Path to local RoseTTAFold installation |
| `output_dir` | str | './rosettafold_output' | Directory to save output files |
| `gpu_id` | int | 0 | ID of the GPU to use |
| `debug` | bool | False | Enable debug mode (keeps temporary files) |

### Quantum Computing Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `use_quantum` | bool | True | Enable quantum computation |
| `quantum_backend` | str | 'qsim' | Quantum backend to use ('qsim', 'qiskit', etc.) |
| `quantum_circuit_depth` | int | 100 (hybrid), 500 (quantum) | Depth of quantum circuit |
| `quantum_shots` | int | 1000 (hybrid), 5000 (quantum) | Number of quantum measurements |
| `quantum_layers` | int | 2 (hybrid), 4 (quantum) | Number of quantum layers |

### Consciousness Optimization

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `psi_optimization` | bool | False | Enable consciousness optimization |
| `fib_constraints.enabled` | bool | False | Enable Fibonacci constraints |
| `fib_constraints.tolerance` | float | 0.1 | Tolerance for Fibonacci constraints |

## Example: Running with Different Modes

### Classical Mode

```python
engine = RoseTTAFoldEngine(
    config={
        'mode': 'classical',
        'rosettafold_path': '/path/to/rosettafold',
        'output_dir': './classical_output'
    }
)
```

### Hybrid Mode (Recommended)

```python
engine = RoseTTAFoldEngine(
    config={
        'mode': 'hybrid',
        'rosettafold_path': '/path/to/rosettafold',
        'output_dir': './hybrid_output',
        'quantum_backend': 'qsim',
        'quantum_circuit_depth': 100,
        'quantum_shots': 1000,
        'quantum_layers': 2,
        'psi_optimization': True,
        'fib_constraints': {
            'enabled': True,
            'tolerance': 0.1
        }
    }
)
```

### Quantum Mode (Experimental)

```python
engine = RoseTTAFoldEngine(
    config={
        'mode': 'quantum',
        'rosettafold_path': '/path/to/rosettafold',
        'output_dir': './quantum_output',
        'quantum_backend': 'qsim',
        'quantum_circuit_depth': 500,
        'quantum_shots': 5000,
        'quantum_layers': 4,
        'psi_optimization': True
    }
)
```

## Command Line Interface

Run predictions directly from the command line:

```bash
python examples/run_rosettafold.py \
  --sequence ACEDGFIHKMLNQPSRTWV \
  --output-dir ./results \
  --rosettafold-path /path/to/rosettafold \
  --mode hybrid
```

## Output Format

The prediction result is returned as a dictionary with the following structure:

```python
{
    'status': 'COMPLETED',  # or 'FAILED'
    'pdb_path': '/path/to/output.pdb',  # Path to predicted structure
    'output_dir': '/path/to/output/dir',  # Output directory
    'processing_time_seconds': 123.45,  # Time taken for prediction
    'sequence_length': 20,  # Length of input sequence
    'quantum_info': {  # Quantum computation details
        'used_quantum': True,
        'quantum_backend': 'qsim',
        'quantum_circuit_depth': 100,
        'quantum_shots': 1000,
        'quantum_layers': 2
    },
    'config_used': {  # Configuration used for this prediction
        'mode': 'hybrid',
        'psi_optimization': True,
        'fib_constraints': {'enabled': True, 'tolerance': 0.1}
    },
    'metadata': {  # Additional metadata
        'job_name': 'rosettafold_1234567890',
        'timestamp': '2023-01-01T12:00:00.000000',
        'parameters': {}  # Any additional parameters passed to predict()
    }
}
```

## Troubleshooting

### Common Issues

1. **Missing Dependencies**:
   ```bash
   # Install required Python packages
   pip install -r requirements.txt
   ```

2. **CUDA Errors**:
   - Ensure CUDA is properly installed
   - Check that your GPU has sufficient memory
   - Try reducing batch size or using a smaller model

3. **Quantum Backend Not Found**:
   - Install the required quantum backend (e.g., `pip install qsimcirq`)
   - Check that the backend is properly configured

4. **RoseTTAFold Not Found**:
   - Set the `ROSETTAFOLD_PATH` environment variable
   - Or provide the path in the configuration

### Debugging

Enable debug mode for more detailed logs:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Performance Tips

1. **GPU Memory**:
   - Use smaller batch sizes for long sequences
   - Enable mixed precision training with `use_mixed_precision: True`

2. **Quantum Computation**:
   - Start with hybrid mode before trying full quantum mode
   - Reduce `quantum_circuit_depth` for faster but potentially less accurate results
   - Increase `quantum_shots` for more accurate quantum measurements

3. **Caching**:
   - Enable caching to avoid redundant computations
   - Use `force_refresh=True` to force recomputation when needed

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
