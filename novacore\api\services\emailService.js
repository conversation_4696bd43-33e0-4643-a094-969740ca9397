/**
 * Email Service
 * 
 * This service handles sending emails for various purposes.
 */

const nodemailer = require('nodemailer');
const fs = require('fs');
const path = require('path');
const handlebars = require('handlebars');
const config = require('../config/config');
const logger = require('../config/logger');

// Create nodemailer transporter
const transporter = nodemailer.createTransport({
  host: config.email.host,
  port: config.email.port,
  secure: config.email.secure,
  auth: {
    user: config.email.auth.user,
    pass: config.email.auth.pass
  }
});

/**
 * Send an email
 * @param {Object} options - Email options
 * @returns {Promise} - Promise that resolves when email is sent
 */
const sendEmail = async (options) => {
  try {
    // Send email
    const info = await transporter.sendMail({
      from: options.from || config.email.from,
      to: options.to,
      cc: options.cc,
      bcc: options.bcc,
      subject: options.subject,
      text: options.text,
      html: options.html,
      attachments: options.attachments
    });
    
    logger.info(`Email sent: ${info.messageId}`);
    return info;
  } catch (error) {
    logger.error('Error sending email:', error);
    throw error;
  }
};

/**
 * Send an email using a template
 * @param {Object} options - Email options
 * @param {string} templateName - Template name
 * @param {Object} templateData - Template data
 * @returns {Promise} - Promise that resolves when email is sent
 */
const sendTemplateEmail = async (options, templateName, templateData) => {
  try {
    // Get template paths
    const htmlTemplatePath = path.join(__dirname, '..', 'templates', 'emails', `${templateName}.html`);
    const textTemplatePath = path.join(__dirname, '..', 'templates', 'emails', `${templateName}.txt`);
    
    // Read templates
    const htmlTemplate = fs.readFileSync(htmlTemplatePath, 'utf8');
    const textTemplate = fs.readFileSync(textTemplatePath, 'utf8');
    
    // Compile templates
    const compiledHtmlTemplate = handlebars.compile(htmlTemplate);
    const compiledTextTemplate = handlebars.compile(textTemplate);
    
    // Render templates
    const html = compiledHtmlTemplate(templateData);
    const text = compiledTextTemplate(templateData);
    
    // Send email
    return sendEmail({
      ...options,
      html,
      text
    });
  } catch (error) {
    logger.error('Error sending template email:', error);
    throw error;
  }
};

/**
 * Send a test recruitment email
 * @param {Object} participant - Participant data
 * @param {Object} testingInfo - Testing information
 * @returns {Promise} - Promise that resolves when email is sent
 */
const sendTestRecruitmentEmail = async (participant, testingInfo) => {
  try {
    // Prepare template data
    const templateData = {
      name: participant.name,
      email: participant.email,
      format: testingInfo.format,
      incentive: testingInfo.incentive,
      startDate: testingInfo.startDate,
      endDate: testingInfo.endDate,
      signupUrl: testingInfo.signupUrl,
      contactEmail: testingInfo.contactEmail,
      senderName: testingInfo.senderName,
      senderTitle: testingInfo.senderTitle,
      logoUrl: config.app.url + '/images/logo.png',
      visualizationExampleUrl: config.app.url + '/images/visualization-example.png',
      companyAddress: config.company.address,
      websiteUrl: config.company.website
    };
    
    // Send email
    return sendTemplateEmail(
      {
        to: participant.email,
        subject: 'Invitation to Participate in Cyber-Safety Visualization User Testing'
      },
      'test-recruitment',
      templateData
    );
  } catch (error) {
    logger.error('Error sending test recruitment email:', error);
    throw error;
  }
};

/**
 * Send a test confirmation email
 * @param {Object} participant - Participant data
 * @param {Object} testSession - Test session data
 * @returns {Promise} - Promise that resolves when email is sent
 */
const sendTestConfirmationEmail = async (participant, testSession) => {
  try {
    // Prepare template data
    const templateData = {
      name: participant.name,
      email: participant.email,
      testDate: testSession.date,
      testTime: testSession.time,
      testLocation: testSession.location,
      testFormat: testSession.format,
      testDuration: testSession.duration,
      contactEmail: testSession.contactEmail,
      senderName: testSession.senderName,
      senderTitle: testSession.senderTitle,
      logoUrl: config.app.url + '/images/logo.png',
      companyAddress: config.company.address,
      websiteUrl: config.company.website
    };
    
    // Send email
    return sendTemplateEmail(
      {
        to: participant.email,
        subject: 'Confirmation: Cyber-Safety Visualization User Testing Session'
      },
      'test-confirmation',
      templateData
    );
  } catch (error) {
    logger.error('Error sending test confirmation email:', error);
    throw error;
  }
};

/**
 * Send a test reminder email
 * @param {Object} participant - Participant data
 * @param {Object} testSession - Test session data
 * @returns {Promise} - Promise that resolves when email is sent
 */
const sendTestReminderEmail = async (participant, testSession) => {
  try {
    // Prepare template data
    const templateData = {
      name: participant.name,
      email: participant.email,
      testDate: testSession.date,
      testTime: testSession.time,
      testLocation: testSession.location,
      testFormat: testSession.format,
      testDuration: testSession.duration,
      contactEmail: testSession.contactEmail,
      senderName: testSession.senderName,
      senderTitle: testSession.senderTitle,
      logoUrl: config.app.url + '/images/logo.png',
      companyAddress: config.company.address,
      websiteUrl: config.company.website
    };
    
    // Send email
    return sendTemplateEmail(
      {
        to: participant.email,
        subject: 'Reminder: Cyber-Safety Visualization User Testing Session'
      },
      'test-reminder',
      templateData
    );
  } catch (error) {
    logger.error('Error sending test reminder email:', error);
    throw error;
  }
};

/**
 * Send a test thank you email
 * @param {Object} participant - Participant data
 * @param {Object} testSession - Test session data
 * @returns {Promise} - Promise that resolves when email is sent
 */
const sendTestThankYouEmail = async (participant, testSession) => {
  try {
    // Prepare template data
    const templateData = {
      name: participant.name,
      email: participant.email,
      incentive: testSession.incentive,
      feedbackSummary: testSession.feedbackSummary,
      contactEmail: testSession.contactEmail,
      senderName: testSession.senderName,
      senderTitle: testSession.senderTitle,
      logoUrl: config.app.url + '/images/logo.png',
      companyAddress: config.company.address,
      websiteUrl: config.company.website
    };
    
    // Send email
    return sendTemplateEmail(
      {
        to: participant.email,
        subject: 'Thank You for Participating in Cyber-Safety Visualization User Testing'
      },
      'test-thank-you',
      templateData
    );
  } catch (error) {
    logger.error('Error sending test thank you email:', error);
    throw error;
  }
};

module.exports = {
  sendEmail,
  sendTemplateEmail,
  sendTestRecruitmentEmail,
  sendTestConfirmationEmail,
  sendTestReminderEmail,
  sendTestThankYouEmail
};
