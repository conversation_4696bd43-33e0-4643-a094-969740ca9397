version: '3.8'

services:
  # NovaFuse API service to test
  novafuse-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=test
      - PORT=3000
      - MONGODB_URI=mongodb://mongo:27017/novafuse-test
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=test-jwt-secret
      - API_KEY=test-api-key
      - LOG_LEVEL=info
    depends_on:
      - mongo
      - redis
    networks:
      - security-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

  # MongoDB for test data
  mongo:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    networks:
      - security-test-network

  # Redis for caching
  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    networks:
      - security-test-network

  # OWASP ZAP for security scanning
  zap:
    image: owasp/zap2docker-stable
    command: zap-baseline.py -t http://novafuse-api:3000 -r zap-report.html
    volumes:
      - ./security-reports:/zap/wrk
    depends_on:
      - novafuse-api
    networks:
      - security-test-network

  # Semgrep for static code analysis
  semgrep:
    image: returntocorp/semgrep
    volumes:
      - .:/src
    command: --config=p/owasp-top-ten --config=p/javascript --json > /src/security-reports/semgrep-results.json
    networks:
      - security-test-network

  # Trivy for container scanning
  trivy:
    image: aquasec/trivy
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./security-reports:/reports
    command: image --format json --output /reports/trivy-results.json novafuse-api
    depends_on:
      - novafuse-api
    networks:
      - security-test-network

  # Artillery for load testing
  artillery:
    image: artilleryio/artillery
    volumes:
      - ./tests/performance:/tests
      - ./performance-reports:/reports
    command: run /tests/load-test.yml -o /reports/load-test-results.json
    depends_on:
      - novafuse-api
    networks:
      - security-test-network

  # k6 for performance testing
  k6:
    image: grafana/k6
    volumes:
      - ./tests/performance:/tests
      - ./performance-reports:/reports
    command: run /tests/stress-test.js -o json=/reports/stress-test-results.json
    depends_on:
      - novafuse-api
    networks:
      - security-test-network

  # Test runner for automated tests
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.test
    volumes:
      - ./:/app
      - ./test-results:/app/test-results
    environment:
      - NODE_ENV=test
      - API_URL=http://novafuse-api:3000
      - MONGODB_URI=mongodb://mongo:27017/novafuse-test
    depends_on:
      - novafuse-api
      - mongo
      - redis
    networks:
      - security-test-network
    command: npm run test:security

networks:
  security-test-network:
    driver: bridge

volumes:
  mongo-data:
