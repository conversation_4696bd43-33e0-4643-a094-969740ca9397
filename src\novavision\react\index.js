/**
 * NovaVision React Components
 * 
 * This module exports all the React components for NovaVision.
 */

// Export core components
export { default as UUICProvider, useUUICContext } from './UUICProvider';
export { default as useUUIC, useUUICConfig, useUUICData } from './useUUIC';
export { 
  default as UUI<PERSON>enderer,
  UUICSchemaRenderer,
  FormRenderer,
  DashboardRenderer,
  ReportRenderer
} from './UUICRenderer';
export { default as UUICBridge } from './UUICBridge';
export { default as UUICComponentRegistry } from './UUICComponentRegistry';
export { uuicConfig } from './uuicConfig';

// Export a convenience function for rendering UI schemas
export const renderUISchema = (schema, data, options) => {
  return {
    type: 'UUICBridge',
    props: {
      schema,
      data,
      ...options
    }
  };
};
