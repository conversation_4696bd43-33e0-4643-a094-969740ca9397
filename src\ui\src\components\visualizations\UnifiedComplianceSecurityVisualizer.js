import React, { useRef, useEffect, useState } from 'react';
import { Box, CircularProgress, Typography, Paper, Grid, Tooltip } from '@mui/material';
import { styled } from '@mui/material/styles';

// Styled components
const VisualizerContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  height: '100%',
  boxShadow: theme.shadows[3],
  borderRadius: '8px',
  overflow: 'hidden'
}));

const SankeyNode = styled(Box)(({ theme, nodeType, completeness }) => {
  // Calculate background color based on node type
  const getNodeColor = (type) => {
    switch (type) {
      case 'requirement':
        return 'rgba(33, 150, 243, 0.8)'; // Blue
      case 'control':
        return 'rgba(76, 175, 80, 0.8)'; // Green
      case 'implementation':
        return 'rgba(255, 152, 0, 0.8)'; // Orange
      default:
        return 'rgba(158, 158, 158, 0.8)'; // Grey
    }
  };
  
  // Calculate border color based on completeness
  const getBorderColor = (complete) => {
    if (complete >= 0.8) return 'rgba(76, 175, 80, 1)'; // High completeness - green
    if (complete >= 0.5) return 'rgba(255, 235, 59, 1)'; // Medium completeness - yellow
    if (complete >= 0.3) return 'rgba(255, 152, 0, 1)'; // Low completeness - orange
    return 'rgba(244, 67, 54, 1)'; // Very low completeness - red
  };
  
  return {
    backgroundColor: getNodeColor(nodeType),
    border: `2px solid ${getBorderColor(completeness)}`,
    borderRadius: '4px',
    padding: theme.spacing(1),
    margin: theme.spacing(0.5),
    transition: 'all 0.3s ease',
    cursor: 'pointer',
    '&:hover': {
      transform: 'scale(1.05)',
      zIndex: 1
    }
  };
});

const SankeyLink = styled(Box)(({ theme, linkStrength, efficiency }) => {
  // Calculate color based on efficiency
  const getLinkColor = (eff) => {
    if (eff >= 0.8) return 'rgba(76, 175, 80, 0.6)'; // High efficiency - green
    if (eff >= 0.5) return 'rgba(255, 235, 59, 0.6)'; // Medium efficiency - yellow
    if (eff >= 0.3) return 'rgba(255, 152, 0, 0.6)'; // Low efficiency - orange
    return 'rgba(244, 67, 54, 0.6)'; // Very low efficiency - red
  };
  
  return {
    backgroundColor: getLinkColor(efficiency),
    height: `${Math.max(2, linkStrength * 20)}px`,
    margin: '2px 0',
    transition: 'all 0.3s ease'
  };
});

const LegendItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(0.5)
}));

const LegendColor = styled(Box)(({ theme, color }) => ({
  width: 16,
  height: 16,
  backgroundColor: color,
  marginRight: theme.spacing(1),
  borderRadius: '2px'
}));

/**
 * UnifiedComplianceSecurityVisualizer component
 * 
 * Creates a visualization that shows how compliance requirements directly map to security controls.
 * Features include:
 * - Sankey diagram showing flow from requirements to implementation
 * - Status indicators for implementation completeness
 * - Efficiency metrics showing redundancy and coverage
 * - Impact analysis for proposed changes
 */
function UnifiedComplianceSecurityVisualizer({
  complianceData = {
    requirements: [
      { id: 'req1', name: 'Data Protection', domain: 'grc', completeness: 0.8 },
      { id: 'req2', name: 'Access Control', domain: 'grc', completeness: 0.6 },
      { id: 'req3', name: 'Incident Response', domain: 'grc', completeness: 0.4 }
    ],
    controls: [
      { id: 'ctrl1', name: 'Encryption', domain: 'it', completeness: 0.9 },
      { id: 'ctrl2', name: 'Authentication', domain: 'it', completeness: 0.7 },
      { id: 'ctrl3', name: 'Monitoring', domain: 'cybersecurity', completeness: 0.5 },
      { id: 'ctrl4', name: 'Firewalls', domain: 'cybersecurity', completeness: 0.8 }
    ],
    implementations: [
      { id: 'impl1', name: 'AES-256', domain: 'it', completeness: 0.9 },
      { id: 'impl2', name: 'MFA', domain: 'it', completeness: 0.6 },
      { id: 'impl3', name: 'SIEM', domain: 'cybersecurity', completeness: 0.4 },
      { id: 'impl4', name: 'Next-Gen FW', domain: 'cybersecurity', completeness: 0.7 }
    ],
    links: [
      { source: 'req1', target: 'ctrl1', strength: 0.8, efficiency: 0.9 },
      { source: 'req1', target: 'ctrl3', strength: 0.4, efficiency: 0.5 },
      { source: 'req2', target: 'ctrl2', strength: 0.7, efficiency: 0.8 },
      { source: 'req2', target: 'ctrl4', strength: 0.5, efficiency: 0.6 },
      { source: 'req3', target: 'ctrl3', strength: 0.6, efficiency: 0.7 },
      { source: 'ctrl1', target: 'impl1', strength: 0.9, efficiency: 0.9 },
      { source: 'ctrl2', target: 'impl2', strength: 0.6, efficiency: 0.7 },
      { source: 'ctrl3', target: 'impl3', strength: 0.4, efficiency: 0.5 },
      { source: 'ctrl4', target: 'impl4', strength: 0.7, efficiency: 0.8 }
    ]
  },
  impactAnalysis = {
    proposedChanges: [
      { id: 'change1', target: 'ctrl2', impact: 0.7, description: 'Upgrade to biometric authentication' },
      { id: 'change2', target: 'impl3', impact: 0.5, description: 'Implement AI-based threat detection' }
    ]
  },
  options = {
    showLegend: true,
    showEfficiencyMetrics: true,
    showImpactAnalysis: true,
    showTooltips: true,
    highlightDomains: true
  },
  width = '100%',
  height = '100%'
}) {
  // State for loading and error handling
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // State for selected elements
  const [selectedElement, setSelectedElement] = useState(null);
  const [highlightedLinks, setHighlightedLinks] = useState([]);
  
  // Domain colors
  const domainColors = {
    grc: 'rgba(51, 102, 204, 0.8)',      // Blue
    it: 'rgba(51, 204, 51, 0.8)',         // Green
    cybersecurity: 'rgba(204, 51, 51, 0.8)'  // Red
  };
  
  // Calculate efficiency metrics
  const [efficiencyMetrics, setEfficiencyMetrics] = useState({
    overall: 0,
    byDomain: {
      grc: 0,
      it: 0,
      cybersecurity: 0
    },
    redundancy: 0,
    coverage: 0
  });
  
  // Calculate efficiency metrics
  useEffect(() => {
    try {
      // Calculate overall efficiency
      let totalEfficiency = 0;
      let linkCount = 0;
      
      // Domain-specific metrics
      const domainEfficiency = {
        grc: { total: 0, count: 0 },
        it: { total: 0, count: 0 },
        cybersecurity: { total: 0, count: 0 }
      };
      
      // Calculate redundancy (controls implementing multiple requirements)
      const controlToRequirements = {};
      
      // Calculate coverage (requirements implemented by controls)
      const requirementToControls = {};
      
      // Process links
      complianceData.links.forEach(link => {
        // Overall efficiency
        totalEfficiency += link.efficiency || 0;
        linkCount++;
        
        // Find source and target domains
        const sourceItem = 
          complianceData.requirements.find(r => r.id === link.source) ||
          complianceData.controls.find(c => c.id === link.source);
        
        const targetItem = 
          complianceData.controls.find(c => c.id === link.target) ||
          complianceData.implementations.find(i => i.id === link.target);
        
        if (sourceItem && targetItem) {
          // Domain efficiency
          if (sourceItem.domain) {
            domainEfficiency[sourceItem.domain].total += link.efficiency || 0;
            domainEfficiency[sourceItem.domain].count++;
          }
          
          // Redundancy calculation
          if (sourceItem.id.startsWith('req') && targetItem.id.startsWith('ctrl')) {
            if (!controlToRequirements[targetItem.id]) {
              controlToRequirements[targetItem.id] = [];
            }
            controlToRequirements[targetItem.id].push(sourceItem.id);
            
            // Coverage calculation
            if (!requirementToControls[sourceItem.id]) {
              requirementToControls[sourceItem.id] = [];
            }
            requirementToControls[sourceItem.id].push(targetItem.id);
          }
        }
      });
      
      // Calculate average efficiency
      const overallEfficiency = linkCount > 0 ? totalEfficiency / linkCount : 0;
      
      // Calculate domain efficiencies
      const domainEfficiencies = {};
      Object.keys(domainEfficiency).forEach(domain => {
        domainEfficiencies[domain] = domainEfficiency[domain].count > 0 
          ? domainEfficiency[domain].total / domainEfficiency[domain].count 
          : 0;
      });
      
      // Calculate redundancy score (average number of requirements per control)
      let totalRequirements = 0;
      Object.values(controlToRequirements).forEach(reqs => {
        totalRequirements += reqs.length;
      });
      const redundancyScore = Object.keys(controlToRequirements).length > 0 
        ? totalRequirements / Object.keys(controlToRequirements).length 
        : 0;
      
      // Calculate coverage score (percentage of requirements with at least one control)
      const coveredRequirements = Object.keys(requirementToControls).length;
      const totalRequirementCount = complianceData.requirements.length;
      const coverageScore = totalRequirementCount > 0 
        ? coveredRequirements / totalRequirementCount 
        : 0;
      
      // Update efficiency metrics
      setEfficiencyMetrics({
        overall: overallEfficiency,
        byDomain: domainEfficiencies,
        redundancy: redundancyScore,
        coverage: coverageScore
      });
      
      setIsLoading(false);
    } catch (err) {
      console.error('Error calculating efficiency metrics:', err);
      setError(err.message || 'Error calculating efficiency metrics');
      setIsLoading(false);
    }
  }, [complianceData]);
  
  // Handle node click
  const handleNodeClick = (nodeId) => {
    setSelectedElement(nodeId);
    
    // Find all links connected to this node
    const connectedLinks = complianceData.links.filter(
      link => link.source === nodeId || link.target === nodeId
    );
    
    setHighlightedLinks(connectedLinks.map(link => `${link.source}-${link.target}`));
  };
  
  // Helper function to get tooltip content
  const getNodeTooltip = (nodeId) => {
    // Find node in requirements, controls, or implementations
    const node = 
      complianceData.requirements.find(r => r.id === nodeId) ||
      complianceData.controls.find(c => c.id === nodeId) ||
      complianceData.implementations.find(i => i.id === nodeId);
    
    if (!node) return '';
    
    // Find impact analysis for this node
    const impact = impactAnalysis.proposedChanges.find(c => c.target === nodeId);
    
    return (
      <Box sx={{ p: 1, maxWidth: 200 }}>
        <Typography variant="subtitle2" gutterBottom>
          {node.name}
        </Typography>
        <Typography variant="body2">
          Domain: {node.domain.toUpperCase()}
        </Typography>
        <Typography variant="body2">
          Completeness: {(node.completeness * 100).toFixed(0)}%
        </Typography>
        {impact && (
          <>
            <Typography variant="body2" sx={{ mt: 1, fontWeight: 'bold' }}>
              Proposed Change:
            </Typography>
            <Typography variant="body2">
              {impact.description}
            </Typography>
            <Typography variant="body2">
              Impact: {(impact.impact * 100).toFixed(0)}%
            </Typography>
          </>
        )}
      </Box>
    );
  };
  
  // Helper function to get link tooltip
  const getLinkTooltip = (sourceId, targetId) => {
    const link = complianceData.links.find(
      l => l.source === sourceId && l.target === targetId
    );
    
    if (!link) return '';
    
    return (
      <Box sx={{ p: 1, maxWidth: 200 }}>
        <Typography variant="subtitle2" gutterBottom>
          Connection Details
        </Typography>
        <Typography variant="body2">
          Strength: {(link.strength * 100).toFixed(0)}%
        </Typography>
        <Typography variant="body2">
          Efficiency: {(link.efficiency * 100).toFixed(0)}%
        </Typography>
      </Box>
    );
  };

  return (
    <Box
      sx={{
        width,
        height,
        position: 'relative',
        p: 2
      }}
    >
      {isLoading ? (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%'
          }}
        >
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: 'error.main'
          }}
        >
          <Typography variant="body1" color="error">
            Error: {error}
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={2}>
          {/* Main Visualizer */}
          <Grid item xs={12} md={options.showLegend ? 9 : 12}>
            <VisualizerContainer>
              <Typography variant="h6" gutterBottom>
                Unified Compliance-Security Visualizer
              </Typography>
              
              <Box sx={{ display: 'flex', height: 'calc(100% - 40px)' }}>
                {/* Requirements Column */}
                <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                  <Typography variant="subtitle1" align="center" gutterBottom>
                    Requirements
                  </Typography>
                  
                  <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-around' }}>
                    {complianceData.requirements.map(req => (
                      <Box key={req.id} sx={{ display: 'flex', alignItems: 'center' }}>
                        {options.showTooltips ? (
                          <Tooltip
                            title={getNodeTooltip(req.id)}
                            arrow
                            placement="left"
                          >
                            <SankeyNode
                              nodeType="requirement"
                              completeness={req.completeness}
                              onClick={() => handleNodeClick(req.id)}
                              sx={{
                                flex: 1,
                                boxShadow: selectedElement === req.id ? '0 0 0 2px #2196f3' : 'none',
                                ...(options.highlightDomains && { borderLeft: `4px solid ${domainColors[req.domain]}` })
                              }}
                            >
                              <Typography variant="body2" noWrap>
                                {req.name}
                              </Typography>
                            </SankeyNode>
                          </Tooltip>
                        ) : (
                          <SankeyNode
                            nodeType="requirement"
                            completeness={req.completeness}
                            onClick={() => handleNodeClick(req.id)}
                            sx={{
                              flex: 1,
                              boxShadow: selectedElement === req.id ? '0 0 0 2px #2196f3' : 'none',
                              ...(options.highlightDomains && { borderLeft: `4px solid ${domainColors[req.domain]}` })
                            }}
                          >
                            <Typography variant="body2" noWrap>
                              {req.name}
                            </Typography>
                          </SankeyNode>
                        )}
                      </Box>
                    ))}
                  </Box>
                </Box>
                
                {/* Links from Requirements to Controls */}
                <Box sx={{ width: 40, display: 'flex', flexDirection: 'column', justifyContent: 'space-around' }}>
                  {complianceData.requirements.map(req => (
                    <Box key={req.id} sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-around' }}>
                      {complianceData.links
                        .filter(link => link.source === req.id)
                        .map(link => (
                          options.showTooltips ? (
                            <Tooltip
                              key={`${link.source}-${link.target}`}
                              title={getLinkTooltip(link.source, link.target)}
                              arrow
                              placement="top"
                            >
                              <SankeyLink
                                linkStrength={link.strength}
                                efficiency={link.efficiency}
                                sx={{
                                  opacity: highlightedLinks.includes(`${link.source}-${link.target}`) ? 1 : 0.3
                                }}
                              />
                            </Tooltip>
                          ) : (
                            <SankeyLink
                              key={`${link.source}-${link.target}`}
                              linkStrength={link.strength}
                              efficiency={link.efficiency}
                              sx={{
                                opacity: highlightedLinks.includes(`${link.source}-${link.target}`) ? 1 : 0.3
                              }}
                            />
                          )
                        ))
                      }
                    </Box>
                  ))}
                </Box>
                
                {/* Controls Column */}
                <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                  <Typography variant="subtitle1" align="center" gutterBottom>
                    Controls
                  </Typography>
                  
                  <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-around' }}>
                    {complianceData.controls.map(ctrl => (
                      <Box key={ctrl.id} sx={{ display: 'flex', alignItems: 'center' }}>
                        {options.showTooltips ? (
                          <Tooltip
                            title={getNodeTooltip(ctrl.id)}
                            arrow
                            placement="top"
                          >
                            <SankeyNode
                              nodeType="control"
                              completeness={ctrl.completeness}
                              onClick={() => handleNodeClick(ctrl.id)}
                              sx={{
                                flex: 1,
                                boxShadow: selectedElement === ctrl.id ? '0 0 0 2px #2196f3' : 'none',
                                ...(options.highlightDomains && { borderLeft: `4px solid ${domainColors[ctrl.domain]}` })
                              }}
                            >
                              <Typography variant="body2" noWrap>
                                {ctrl.name}
                              </Typography>
                            </SankeyNode>
                          </Tooltip>
                        ) : (
                          <SankeyNode
                            nodeType="control"
                            completeness={ctrl.completeness}
                            onClick={() => handleNodeClick(ctrl.id)}
                            sx={{
                              flex: 1,
                              boxShadow: selectedElement === ctrl.id ? '0 0 0 2px #2196f3' : 'none',
                              ...(options.highlightDomains && { borderLeft: `4px solid ${domainColors[ctrl.domain]}` })
                            }}
                          >
                            <Typography variant="body2" noWrap>
                              {ctrl.name}
                            </Typography>
                          </SankeyNode>
                        )}
                      </Box>
                    ))}
                  </Box>
                </Box>
                
                {/* Links from Controls to Implementations */}
                <Box sx={{ width: 40, display: 'flex', flexDirection: 'column', justifyContent: 'space-around' }}>
                  {complianceData.controls.map(ctrl => (
                    <Box key={ctrl.id} sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-around' }}>
                      {complianceData.links
                        .filter(link => link.source === ctrl.id)
                        .map(link => (
                          options.showTooltips ? (
                            <Tooltip
                              key={`${link.source}-${link.target}`}
                              title={getLinkTooltip(link.source, link.target)}
                              arrow
                              placement="top"
                            >
                              <SankeyLink
                                linkStrength={link.strength}
                                efficiency={link.efficiency}
                                sx={{
                                  opacity: highlightedLinks.includes(`${link.source}-${link.target}`) ? 1 : 0.3
                                }}
                              />
                            </Tooltip>
                          ) : (
                            <SankeyLink
                              key={`${link.source}-${link.target}`}
                              linkStrength={link.strength}
                              efficiency={link.efficiency}
                              sx={{
                                opacity: highlightedLinks.includes(`${link.source}-${link.target}`) ? 1 : 0.3
                              }}
                            />
                          )
                        ))
                      }
                    </Box>
                  ))}
                </Box>
                
                {/* Implementations Column */}
                <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                  <Typography variant="subtitle1" align="center" gutterBottom>
                    Implementations
                  </Typography>
                  
                  <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-around' }}>
                    {complianceData.implementations.map(impl => (
                      <Box key={impl.id} sx={{ display: 'flex', alignItems: 'center' }}>
                        {options.showTooltips ? (
                          <Tooltip
                            title={getNodeTooltip(impl.id)}
                            arrow
                            placement="right"
                          >
                            <SankeyNode
                              nodeType="implementation"
                              completeness={impl.completeness}
                              onClick={() => handleNodeClick(impl.id)}
                              sx={{
                                flex: 1,
                                boxShadow: selectedElement === impl.id ? '0 0 0 2px #2196f3' : 'none',
                                ...(options.highlightDomains && { borderLeft: `4px solid ${domainColors[impl.domain]}` })
                              }}
                            >
                              <Typography variant="body2" noWrap>
                                {impl.name}
                              </Typography>
                            </SankeyNode>
                          </Tooltip>
                        ) : (
                          <SankeyNode
                            nodeType="implementation"
                            completeness={impl.completeness}
                            onClick={() => handleNodeClick(impl.id)}
                            sx={{
                              flex: 1,
                              boxShadow: selectedElement === impl.id ? '0 0 0 2px #2196f3' : 'none',
                              ...(options.highlightDomains && { borderLeft: `4px solid ${domainColors[impl.domain]}` })
                            }}
                          >
                            <Typography variant="body2" noWrap>
                              {impl.name}
                            </Typography>
                          </SankeyNode>
                        )}
                      </Box>
                    ))}
                  </Box>
                </Box>
              </Box>
            </VisualizerContainer>
          </Grid>
          
          {/* Legend and Metrics */}
          {options.showLegend && (
            <Grid item xs={12} md={3}>
              <VisualizerContainer>
                <Typography variant="h6" gutterBottom>
                  Legend & Metrics
                </Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Node Types
                  </Typography>
                  <LegendItem>
                    <LegendColor color="rgba(33, 150, 243, 0.8)" />
                    <Typography variant="body2">Requirements</Typography>
                  </LegendItem>
                  <LegendItem>
                    <LegendColor color="rgba(76, 175, 80, 0.8)" />
                    <Typography variant="body2">Controls</Typography>
                  </LegendItem>
                  <LegendItem>
                    <LegendColor color="rgba(255, 152, 0, 0.8)" />
                    <Typography variant="body2">Implementations</Typography>
                  </LegendItem>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Domains
                  </Typography>
                  <LegendItem>
                    <LegendColor color={domainColors.grc} />
                    <Typography variant="body2">GRC</Typography>
                  </LegendItem>
                  <LegendItem>
                    <LegendColor color={domainColors.it} />
                    <Typography variant="body2">IT</Typography>
                  </LegendItem>
                  <LegendItem>
                    <LegendColor color={domainColors.cybersecurity} />
                    <Typography variant="body2">Cybersecurity</Typography>
                  </LegendItem>
                </Box>
                
                {options.showEfficiencyMetrics && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Efficiency Metrics
                    </Typography>
                    <Typography variant="body2">
                      Overall Efficiency: {(efficiencyMetrics.overall * 100).toFixed(0)}%
                    </Typography>
                    <Typography variant="body2">
                      GRC Efficiency: {(efficiencyMetrics.byDomain.grc * 100).toFixed(0)}%
                    </Typography>
                    <Typography variant="body2">
                      IT Efficiency: {(efficiencyMetrics.byDomain.it * 100).toFixed(0)}%
                    </Typography>
                    <Typography variant="body2">
                      Cybersecurity Efficiency: {(efficiencyMetrics.byDomain.cybersecurity * 100).toFixed(0)}%
                    </Typography>
                    <Typography variant="body2">
                      Control Redundancy: {efficiencyMetrics.redundancy.toFixed(1)}x
                    </Typography>
                    <Typography variant="body2">
                      Requirement Coverage: {(efficiencyMetrics.coverage * 100).toFixed(0)}%
                    </Typography>
                  </Box>
                )}
                
                {options.showImpactAnalysis && impactAnalysis.proposedChanges.length > 0 && (
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Proposed Changes
                    </Typography>
                    {impactAnalysis.proposedChanges.map((change, index) => (
                      <Box key={index} sx={{ mb: 1 }}>
                        <Typography variant="body2" fontWeight="bold">
                          {change.description}
                        </Typography>
                        <Typography variant="body2">
                          Impact: {(change.impact * 100).toFixed(0)}%
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                )}
              </VisualizerContainer>
            </Grid>
          )}
        </Grid>
      )}
    </Box>
  );
}

export default UnifiedComplianceSecurityVisualizer;
