const Joi = require('joi');

// Validation schemas
const schemas = {
  createAssessment: Joi.object({
    title: Joi.string().required().min(3).max(100),
    description: Joi.string().optional().max(500),
    type: Joi.string().required().valid('internal', 'external', 'vendor', 'compliance'),
    status: Joi.string().required().valid('planned', 'in-progress', 'completed', 'cancelled'),
    scope: Joi.string().optional().max(500),
    startDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    endDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    assignedTo: Joi.string().optional().max(100)
  }),
  
  updateAssessment: Joi.object({
    title: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    type: Joi.string().optional().valid('internal', 'external', 'vendor', 'compliance'),
    status: Joi.string().optional().valid('planned', 'in-progress', 'completed', 'cancelled'),
    scope: Joi.string().optional().max(500),
    startDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    endDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    assignedTo: Joi.string().optional().max(100)
  }).min(1), // At least one field must be provided
  
  createFinding: Joi.object({
    title: Joi.string().required().min(3).max(100),
    description: Joi.string().optional().max(500),
    severity: Joi.string().required().valid('critical', 'high', 'medium', 'low', 'info'),
    status: Joi.string().required().valid('open', 'in-remediation', 'remediated', 'accepted', 'false-positive'),
    remediation: Joi.string().optional().max(500),
    assignedTo: Joi.string().optional().max(100),
    dueDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/) // YYYY-MM-DD format
  }),
  
  updateFinding: Joi.object({
    title: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    severity: Joi.string().optional().valid('critical', 'high', 'medium', 'low', 'info'),
    status: Joi.string().optional().valid('open', 'in-remediation', 'remediated', 'accepted', 'false-positive'),
    remediation: Joi.string().optional().max(500),
    assignedTo: Joi.string().optional().max(100),
    dueDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/) // YYYY-MM-DD format
  }).min(1) // At least one field must be provided
};

/**
 * Middleware to validate request data against a schema
 * @param {string} schemaName - Name of the schema to validate against
 * @returns {Function} Express middleware function
 */
const validateRequest = (schemaName) => {
  return (req, res, next) => {
    const schema = schemas[schemaName];
    
    if (!schema) {
      return res.status(500).json({
        error: 'Internal Server Error',
        message: `Validation schema '${schemaName}' not found`
      });
    }
    
    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // Return all errors, not just the first one
      stripUnknown: true // Remove unknown fields
    });
    
    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        error: 'Bad Request',
        message: errorMessage
      });
    }
    
    // Replace request body with validated value
    req.body = value;
    next();
  };
};

module.exports = {
  validateRequest
};
