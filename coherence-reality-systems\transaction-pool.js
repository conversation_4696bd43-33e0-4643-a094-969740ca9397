// Use a safe reference to globalThis that works in all environments
const globalObj = typeof globalThis !== 'undefined' ? globalThis : window || global;

// Use global BigInt if available, otherwise fall back to a polyfill
const BigInt = globalObj.BigInt || require('big-integer');

// Import Transaction class only when needed to avoid circular dependencies
let Transaction;

try {
  Transaction = require('./transaction');
} catch (error) {
  // Handle case where transaction.js might not be available yet
  console.warn('Transaction class not available during initialization');
}

class TransactionPool {
  constructor() {
    this.pool = new Map(); // txHash -> transaction
    this.pendingNonces = new Map(); // address -> nonce
  }

  /**
   * Add a transaction to the pool
   * @param {Transaction} transaction - Transaction to add
   * @returns {boolean} - True if added, false if rejected
   */
  add(transaction) {
    // Lazy load Transaction class if not available
    if (!Transaction && !global.Transaction) {
      try {
        Transaction = require('./transaction');
      } catch (error) {
        console.error('Failed to load Transaction class:', error);
        return false;
      }
    }

    // Verify transaction
    if (!this._isValidTransaction(transaction)) {
      return false;
    }

    const txHash = transaction.hash ? transaction.hash() : null;
    if (!txHash) {
      console.error('Transaction missing hash method');
      return false;
    }
    
    // Check if transaction already exists
    if (this.pool.has(txHash)) {
      return false;
    }

    // Add to pool
    this.pool.set(txHash, transaction);
    
    // Update pending nonce for sender (convert to string to ensure consistent type)
    this._updatePendingNonce(transaction.from, transaction.nonce.toString());
    
    return true;
  }

  /**
   * Get a transaction by hash
   * @param {string} txHash - Transaction hash
   * @returns {Transaction|undefined} - Transaction if found
   */
  get(txHash) {
    return this.pool.get(txHash);
  }

  /**
   * Get all transactions in the pool
   * @returns {Array<Transaction>} - Array of transactions
   */
  getAll() {
    return Array.from(this.pool.values());
  }

  /**
   * Get transactions by sender
   * @param {string} address - Sender address
   * @returns {Array<Transaction>} - Array of transactions
   */
  getBySender(address) {
    return this.getAll().filter(tx => tx.from.toLowerCase() === address.toLowerCase());
  }

  /**
   * Remove transactions from the pool
   * @param {Array<string>} txHashes - Array of transaction hashes to remove
   */
  remove(txHashes) {
    txHashes.forEach(hash => this.pool.delete(hash));
  }

  /**
   * Clear the transaction pool
   */
  clear() {
    this.pool.clear();
    this.pendingNonces.clear();
  }

  /**
   * Get the next nonce for an address
   * @param {string} address - Account address
   * @param {number} currentNonce - Current nonce from account state
   * @returns {string} - Next nonce as a string
   */
  getNextNonce(address, currentNonce = '0') {
    const pendingNonce = this.pendingNonces.get(address.toLowerCase()) || '0';
    const currentNonceBN = BigInt(currentNonce.toString());
    const pendingNonceBN = BigInt(pendingNonce.toString());
    
    // Return the higher nonce + 1
    const nextNonce = (pendingNonceBN > currentNonceBN 
      ? pendingNonceBN 
      : currentNonceBN) + BigInt(1);
      
    return nextNonce.toString();
  }

  /**
   * Check if a transaction is valid
   * @private
   * @param {Transaction} transaction - Transaction to validate
   * @returns {boolean} - True if valid
   */
  _isValidTransaction(transaction) {
    // Basic validation
    if (!transaction.from || !transaction.to || !transaction.value || !transaction.nonce) {
      return false;
    }

    // Verify signature
    if (!transaction.verify()) {
      return false;
    }

    // Check nonce is not too far in the future
    // This is a basic check, actual nonce validation happens during block execution
    const currentNonce = this.pendingNonces.get(transaction.from.toLowerCase()) || '0';
    const txNonce = BigInt(transaction.nonce.toString());
    const maxFutureNonce = BigInt(currentNonce.toString()) + BigInt(100);
    
    if (txNonce > maxFutureNonce) {
      return false;
    }

    return true;
  }

  /**
   * Update the pending nonce for an address
   * @private
   * @param {string} address - Account address
   * @param {string} nonce - Transaction nonce
   */
  _updatePendingNonce(address, nonce) {
    const currentNonce = this.pendingNonces.get(address.toLowerCase()) || '0';
    const newNonce = BigInt(nonce.toString());
    const currentNonceBN = BigInt(currentNonce.toString());
    
    if (newNonce > currentNonceBN) {
      this.pendingNonces.set(address.toLowerCase(), newNonce.toString());
    }
  }
}

module.exports = TransactionPool;
