"""
Universal Compliance Visualization Framework (UCVF).

A framework that transforms complex compliance data into intuitive visualizations
tailored to different stakeholders.
"""

from .core.visualization_engine import VisualizationEngine
from .core.role_manager import RoleManager
from .core.template_manager import TemplateManager
from .core.data_transformer import DataTransformer
from .core.test_result_subscriber import initialize_test_result_subscriber, TestResultSubscriber

__version__ = '0.1.0'
__all__ = [
    'VisualizationEngine',
    'RoleManager',
    'TemplateManager',
    'DataTransformer',
    'TestResultSubscriber',
    'initialize_test_result_subscriber'
]

# Initialize the test result subscriber
test_result_subscriber = initialize_test_result_subscriber()
