/**
 * Integration tests for the Certifications & Accreditation Connector
 */

const nock = require('nock');
const CertificationsAccreditationConnector = require('../../../../connector/implementations/certifications-accreditation');

// Mock logger
jest.mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));

describe('CertificationsAccreditationConnector Integration', () => {
  let connector;
  const baseUrl = 'https://api.test.com';
  
  beforeAll(() => {
    // Disable real HTTP requests
    nock.disableNetConnect();
  });
  
  afterAll(() => {
    // Enable real HTTP requests
    nock.enableNetConnect();
  });
  
  beforeEach(() => {
    // Reset nock
    nock.cleanAll();
    
    // Create connector instance
    connector = new CertificationsAccreditationConnector({
      baseUrl
    }, {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      redirectUri: 'https://test-redirect.com'
    });
    
    // Mock authentication
    nock(baseUrl)
      .post('/oauth2/token')
      .reply(200, {
        access_token: 'test-access-token',
        expires_in: 3600
      });
  });
  
  describe('Certification Management', () => {
    it('should list certifications', async () => {
      // Mock certifications endpoint
      const mockCertifications = {
        data: [
          {
            id: 'cert-1',
            name: 'ISO 27001',
            description: 'Information Security Management System certification',
            type: 'security',
            status: 'active',
            issuer: 'International Organization for Standardization'
          },
          {
            id: 'cert-2',
            name: 'SOC 2 Type II',
            description: 'Service Organization Control 2 Type II',
            type: 'security',
            status: 'active',
            issuer: 'American Institute of CPAs'
          }
        ],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      
      nock(baseUrl)
        .get('/certifications')
        .query({ status: 'active' })
        .reply(200, mockCertifications);
      
      // Initialize connector
      await connector.initialize();
      
      // List certifications
      const result = await connector.listCertifications({ status: 'active' });
      
      // Verify result
      expect(result).toEqual(mockCertifications);
    });
    
    it('should get a specific certification', async () => {
      // Mock certification endpoint
      const mockCertification = {
        id: 'cert-123',
        name: 'ISO 27001',
        description: 'Information Security Management System certification',
        type: 'security',
        status: 'active',
        issuer: 'International Organization for Standardization',
        issuedDate: '2023-01-15',
        expirationDate: '2026-01-14',
        requirements: [
          {
            id: 'req-1',
            name: 'Information Security Policy',
            description: 'Establish and maintain an information security policy',
            status: 'met'
          },
          {
            id: 'req-2',
            name: 'Risk Assessment',
            description: 'Conduct regular risk assessments',
            status: 'met'
          }
        ],
        documents: [
          {
            id: 'doc-1',
            name: 'ISO 27001 Certificate',
            type: 'pdf',
            url: 'https://example.com/documents/iso27001-cert.pdf'
          }
        ]
      };
      
      nock(baseUrl)
        .get('/certifications/cert-123')
        .reply(200, mockCertification);
      
      // Initialize connector
      await connector.initialize();
      
      // Get certification
      const result = await connector.getCertification('cert-123');
      
      // Verify result
      expect(result).toEqual(mockCertification);
    });
    
    it('should create a new certification', async () => {
      // Certification data
      const certificationData = {
        name: 'HIPAA Compliance',
        description: 'Health Insurance Portability and Accountability Act compliance',
        type: 'healthcare',
        issuer: 'Department of Health and Human Services'
      };
      
      // Mock response
      const mockResponse = {
        id: 'cert-new',
        ...certificationData,
        status: 'pending',
        createdAt: '2023-06-15T10:30:00Z',
        updatedAt: '2023-06-15T10:30:00Z'
      };
      
      nock(baseUrl)
        .post('/certifications', certificationData)
        .reply(201, mockResponse);
      
      // Initialize connector
      await connector.initialize();
      
      // Create certification
      const result = await connector.createCertification(certificationData);
      
      // Verify result
      expect(result).toEqual(mockResponse);
    });
    
    it('should update an existing certification', async () => {
      // Certification update data
      const certificationId = 'cert-123';
      const updateData = {
        name: 'ISO 27001:2022',
        description: 'Updated Information Security Management System certification'
      };
      
      // Mock response
      const mockResponse = {
        id: certificationId,
        name: 'ISO 27001:2022',
        description: 'Updated Information Security Management System certification',
        type: 'security',
        status: 'active',
        issuer: 'International Organization for Standardization',
        updatedAt: '2023-06-15T11:45:00Z'
      };
      
      nock(baseUrl)
        .put(`/certifications/${certificationId}`, updateData)
        .reply(200, mockResponse);
      
      // Initialize connector
      await connector.initialize();
      
      // Update certification
      const result = await connector.updateCertification(certificationId, updateData);
      
      // Verify result
      expect(result).toEqual(mockResponse);
    });
    
    it('should delete a certification', async () => {
      // Certification ID
      const certificationId = 'cert-123';
      
      nock(baseUrl)
        .delete(`/certifications/${certificationId}`)
        .reply(204);
      
      // Initialize connector
      await connector.initialize();
      
      // Delete certification
      await connector.deleteCertification(certificationId);
      
      // If no error is thrown, the test passes
      expect(true).toBe(true);
    });
  });
  
  describe('Assessment Management', () => {
    it('should list assessments', async () => {
      // Mock assessments endpoint
      const mockAssessments = {
        data: [
          {
            id: 'assess-1',
            name: 'ISO 27001 Annual Assessment',
            description: 'Annual surveillance audit for ISO 27001',
            status: 'in_progress',
            certificationId: 'cert-123',
            certificationName: 'ISO 27001'
          },
          {
            id: 'assess-2',
            name: 'ISO 27001 Gap Analysis',
            description: 'Pre-certification gap analysis',
            status: 'completed',
            certificationId: 'cert-123',
            certificationName: 'ISO 27001'
          }
        ],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      
      nock(baseUrl)
        .get('/assessments')
        .query({ certificationId: 'cert-123' })
        .reply(200, mockAssessments);
      
      // Initialize connector
      await connector.initialize();
      
      // List assessments
      const result = await connector.listAssessments({ certificationId: 'cert-123' });
      
      // Verify result
      expect(result).toEqual(mockAssessments);
    });
    
    it('should get a specific assessment', async () => {
      // Mock assessment endpoint
      const mockAssessment = {
        id: 'assess-123',
        name: 'ISO 27001 Annual Assessment',
        description: 'Annual surveillance audit for ISO 27001',
        status: 'in_progress',
        certificationId: 'cert-123',
        certificationName: 'ISO 27001',
        startDate: '2023-06-01',
        endDate: '2023-06-15',
        assessor: {
          id: 'assessor-1',
          name: 'John Smith',
          organization: 'Certification Body Inc.'
        },
        controls: [
          {
            id: 'control-1',
            name: 'A.5.1.1 Information Security Policies',
            description: 'A set of policies for information security shall be defined, approved by management, published and communicated to employees and relevant external parties.',
            status: 'compliant'
          }
        ]
      };
      
      nock(baseUrl)
        .get('/assessments/assess-123')
        .reply(200, mockAssessment);
      
      // Initialize connector
      await connector.initialize();
      
      // Get assessment
      const result = await connector.getAssessment('assess-123');
      
      // Verify result
      expect(result).toEqual(mockAssessment);
    });
    
    it('should create an assessment', async () => {
      // Assessment data
      const assessmentData = {
        name: 'ISO 27001 Recertification',
        description: 'Full recertification audit for ISO 27001',
        certificationId: 'cert-123',
        startDate: '2023-09-01',
        endDate: '2023-09-15'
      };
      
      // Mock response
      const mockResponse = {
        id: 'assess-new',
        ...assessmentData,
        status: 'planned',
        certificationName: 'ISO 27001',
        createdAt: '2023-06-15T10:30:00Z',
        updatedAt: '2023-06-15T10:30:00Z'
      };
      
      nock(baseUrl)
        .post('/assessments', assessmentData)
        .reply(201, mockResponse);
      
      // Initialize connector
      await connector.initialize();
      
      // Create assessment
      const result = await connector.createAssessment(assessmentData);
      
      // Verify result
      expect(result).toEqual(mockResponse);
    });
  });
  
  describe('Evidence Management', () => {
    it('should list evidence', async () => {
      // Mock evidence endpoint
      const mockEvidence = {
        data: [
          {
            id: 'evidence-1',
            name: 'Information Security Policy Document',
            description: 'Current version of the Information Security Policy',
            type: 'document',
            assessmentId: 'assess-123',
            controlId: 'control-1'
          },
          {
            id: 'evidence-2',
            name: 'Risk Assessment Report',
            description: 'Latest risk assessment report',
            type: 'document',
            assessmentId: 'assess-123',
            controlId: 'control-2'
          }
        ],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      
      nock(baseUrl)
        .get('/evidence')
        .query({ assessmentId: 'assess-123' })
        .reply(200, mockEvidence);
      
      // Initialize connector
      await connector.initialize();
      
      // List evidence
      const result = await connector.listEvidence({ assessmentId: 'assess-123' });
      
      // Verify result
      expect(result).toEqual(mockEvidence);
    });
    
    it('should get specific evidence', async () => {
      // Mock evidence endpoint
      const mockEvidence = {
        id: 'evidence-123',
        name: 'Information Security Policy Document',
        description: 'Current version of the Information Security Policy',
        type: 'document',
        assessmentId: 'assess-123',
        controlId: 'control-1',
        url: 'https://example.com/documents/security-policy.pdf',
        metadata: {
          version: '2.3',
          lastReviewed: '2023-05-15',
          approvedBy: 'Security Committee'
        }
      };
      
      nock(baseUrl)
        .get('/evidence/evidence-123')
        .reply(200, mockEvidence);
      
      // Initialize connector
      await connector.initialize();
      
      // Get evidence
      const result = await connector.getEvidence('evidence-123');
      
      // Verify result
      expect(result).toEqual(mockEvidence);
    });
    
    it('should create evidence', async () => {
      // Evidence data
      const evidenceData = {
        name: 'Access Control Policy',
        description: 'Access control policy document',
        type: 'document',
        assessmentId: 'assess-123',
        controlId: 'control-3',
        url: 'https://example.com/documents/access-control-policy.pdf'
      };
      
      // Mock response
      const mockResponse = {
        id: 'evidence-new',
        ...evidenceData,
        createdAt: '2023-06-15T10:30:00Z',
        updatedAt: '2023-06-15T10:30:00Z'
      };
      
      nock(baseUrl)
        .post('/evidence', evidenceData)
        .reply(201, mockResponse);
      
      // Initialize connector
      await connector.initialize();
      
      // Create evidence
      const result = await connector.createEvidence(evidenceData);
      
      // Verify result
      expect(result).toEqual(mockResponse);
    });
  });
  
  describe('Error Handling', () => {
    it('should handle authentication errors', async () => {
      // Clean previous nock mocks
      nock.cleanAll();
      
      // Mock authentication error
      nock(baseUrl)
        .post('/oauth2/token')
        .reply(401, {
          error: 'invalid_client',
          error_description: 'Invalid client credentials'
        });
      
      // Try to initialize connector
      await expect(connector.initialize()).rejects.toThrow('Authentication failed');
    });
    
    it('should handle not found errors', async () => {
      // Mock not found error
      nock(baseUrl)
        .get('/certifications/non-existent')
        .reply(404, {
          error: 'not_found',
          error_description: 'Certification not found'
        });
      
      // Initialize connector
      await connector.initialize();
      
      // Try to get non-existent certification
      await expect(connector.getCertification('non-existent')).rejects.toThrow('Error getting certification');
    });
    
    it('should handle validation errors', async () => {
      // Certification data with missing required fields
      const invalidData = {
        description: 'Invalid Certification'
        // Missing required field: name
      };
      
      // Mock validation error
      nock(baseUrl)
        .post('/certifications', invalidData)
        .reply(400, {
          error: 'validation_error',
          error_description: 'Validation failed',
          errors: [
            { field: 'name', message: 'Name is required' }
          ]
        });
      
      // Initialize connector
      await connector.initialize();
      
      // Try to create invalid certification
      await expect(connector.createCertification(invalidData)).rejects.toThrow('name is required');
    });
  });
});
