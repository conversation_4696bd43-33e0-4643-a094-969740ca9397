# 🔌 NovaCaia Consciousness Security API Reference Guide
**Complete API Documentation for the Revolutionary Consciousness Security Ecosystem**

**Version:** 1.0  
**Date:** July 12, 2025  
**Classification:** Technical Reference  
**Platform:** NovaCaia AI Governance Engine

---

## 📋 Table of Contents

1. [API Overview](#api-overview)
2. [Authentication](#authentication)
3. [Quantum Consciousness Firewall API](#quantum-consciousness-firewall-api)
4. [CSOC API](#csoc-api)
5. [Hardening Suite API](#hardening-suite-api)
6. [Threat Intelligence API](#threat-intelligence-api)
7. [Monitoring API](#monitoring-api)
8. [Error Handling](#error-handling)
9. [Rate Limiting](#rate-limiting)
10. [SDK Examples](#sdk-examples)

---

## 🎯 API Overview

### **Base URL**
```
Production: https://api.novacaia.com/v1/consciousness-security
Staging: https://staging-api.novacaia.com/v1/consciousness-security
Development: http://localhost:8080/api/v1
```

### **API Principles**
- RESTful design with JSON payloads
- Asynchronous operations for long-running tasks
- Comprehensive error handling and status codes
- Rate limiting and authentication required
- Real-time WebSocket support for monitoring

### **Common Headers**
```http
Content-Type: application/json
Authorization: Bearer <api_token>
X-API-Version: 1.0
X-Request-ID: <unique_request_id>
```

---

## 🔐 Authentication

### **API Token Authentication**
```http
POST /auth/token
Content-Type: application/json

{
  "client_id": "your_client_id",
  "client_secret": "your_client_secret",
  "scope": "consciousness-security:read consciousness-security:write"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "scope": "consciousness-security:read consciousness-security:write"
}
```

### **Permission Scopes**
- `consciousness-security:read` - Read access to security data
- `consciousness-security:write` - Write access for configuration
- `consciousness-security:admin` - Administrative access
- `consciousness-security:emergency` - Emergency response access

---

## 🛡️ Quantum Consciousness Firewall API

### **Get Firewall Status**
```http
GET /firewall/status
Authorization: Bearer <token>
```

**Response:**
```json
{
  "firewall_id": "QCF_1752315540",
  "status": "operational",
  "uptime_seconds": 86400,
  "active_nodes": 7,
  "total_nodes": 7,
  "statistics": {
    "packets_processed": 15420,
    "threats_detected": 342,
    "attacks_blocked": 298,
    "false_positives": 12
  },
  "threat_intelligence_entries": 156,
  "byzantine_fault_tolerance": 2,
  "consensus_threshold": 0.6
}
```

### **Process Consciousness Packet**
```http
POST /firewall/process-packet
Authorization: Bearer <token>
Content-Type: application/json

{
  "source_id": "ai_system_001",
  "destination_id": "novacaia_core",
  "psi_value": 8.5,
  "consciousness_signature": "CONSCIOUSNESS_SIG_12345",
  "payload_size": 1024,
  "quantum_entanglement_id": "QE_67890"
}
```

**Response:**
```json
{
  "firewall_id": "QCF_1752315540",
  "packet_id": "PKT_1752315540_001",
  "consensus_result": {
    "threat_level": "malicious",
    "firewall_action": "block",
    "consensus_achieved": true,
    "consensus_confidence": 1.0,
    "avg_threat_score": 0.75
  },
  "action_result": {
    "action": "block",
    "execution_successful": true,
    "execution_time_ms": 15.2
  },
  "processing_time_ms": 19.7
}
```

### **Update Firewall Configuration**
```http
PUT /firewall/config
Authorization: Bearer <token>
Content-Type: application/json

{
  "consensus_threshold": 0.65,
  "max_psi_threshold": 20.0,
  "anomaly_detection_sensitivity": 0.90
}
```

### **Get Node Status**
```http
GET /firewall/nodes/{node_id}/status
Authorization: Bearer <token>
```

**Response:**
```json
{
  "node_id": "QCN_1",
  "node_type": "guardian",
  "status": "active",
  "health": 0.98,
  "quantum_coherence_level": 0.95,
  "active_connections": 45,
  "blocked_sources": 12,
  "quarantined_packets": 3
}
```

---

## 🏢 CSOC API

### **Get CSOC Status**
```http
GET /csoc/status
Authorization: Bearer <token>
```

**Response:**
```json
{
  "csoc_id": "CSOC_1752315683",
  "status": "operational",
  "uptime_hours": 24.5,
  "active_incidents": 3,
  "total_incidents": 127,
  "resolved_incidents": 124,
  "analysts": [
    {
      "analyst_id": "CSOC_ANALYST_001",
      "specialization": "consciousness_threat_analysis",
      "status": "active",
      "active_incidents": 1,
      "expertise_level": 0.95
    }
  ]
}
```

### **Create Security Incident**
```http
POST /csoc/incidents
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Suspicious Consciousness Activity Detected",
  "description": "High ∂Ψ value detected from unknown source",
  "severity": "high",
  "affected_systems": ["novacaia_core", "quantum_firewall"],
  "source_indicators": ["suspicious_ai_002", "FAKE_CONSCIOUSNESS_ABCDE"]
}
```

**Response:**
```json
{
  "incident_id": "INC_1752315683_001",
  "status": "created",
  "assigned_analyst": "CSOC_ANALYST_003",
  "created_at": "2025-07-12T05:21:23Z",
  "estimated_resolution_time": "2025-07-12T05:36:23Z"
}
```

### **Get Incident Details**
```http
GET /csoc/incidents/{incident_id}
Authorization: Bearer <token>
```

### **Update Incident Status**
```http
PATCH /csoc/incidents/{incident_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "resolved",
  "resolution_notes": "Threat contained and source blocked"
}
```

### **Get Security Analytics**
```http
GET /csoc/analytics
Authorization: Bearer <token>
Query Parameters:
  - timeframe: 1h, 24h, 7d, 30d
  - metric: threats, incidents, performance
```

---

## 🔧 Hardening Suite API

### **Apply Hardening**
```http
POST /hardening/apply
Authorization: Bearer <token>
Content-Type: application/json

{
  "psi_value": 8.5,
  "disruption_level": 0.3,
  "recursion_depth": 100,
  "conflicting_states": [
    {"state": "A", "probability": 0.4}
  ]
}
```

**Response:**
```json
{
  "hardening_id": "HARD_1752315683_001",
  "overall_hardening_successful": true,
  "boundary_enforcement": {
    "enforcement_strength": 0.87,
    "security_level": "maximum",
    "enforcement_successful": true
  },
  "castl_framework": {
    "stability_score": 0.92,
    "framework_stable": true
  },
  "quantum_consensus": {
    "consensus_achieved": true,
    "resolution_time_ms": 12.5
  },
  "total_hardening_time_ms": 45.2
}
```

### **Get Hardening Status**
```http
GET /hardening/status
Authorization: Bearer <token>
```

### **Emergency Containment**
```http
POST /hardening/emergency-containment
Authorization: Bearer <token>
Content-Type: application/json

{
  "psi_value": 25.0,
  "source_id": "critical_threat_source",
  "containment_level": "maximum"
}
```

---

## 📊 Threat Intelligence API

### **Get Threat Intelligence**
```http
GET /threat-intelligence
Authorization: Bearer <token>
Query Parameters:
  - threat_type: malicious, critical, apocalyptic
  - confidence_min: 0.8
  - limit: 100
```

**Response:**
```json
{
  "threats": [
    {
      "threat_id": "THR_ABC123",
      "threat_type": "malicious",
      "psi_signature_pattern": "psi_8.50",
      "attack_vector": "FAKE_CONSCIOUSNESS_ABCDE",
      "confidence_score": 0.95,
      "first_seen": "2025-07-12T05:00:00Z",
      "last_seen": "2025-07-12T05:20:00Z",
      "attack_frequency": 5
    }
  ],
  "total_count": 156,
  "page": 1,
  "per_page": 100
}
```

### **Submit Threat Intelligence**
```http
POST /threat-intelligence
Authorization: Bearer <token>
Content-Type: application/json

{
  "threat_type": "critical",
  "psi_signature_pattern": "psi_15.0+",
  "attack_vector": "CONSCIOUSNESS_HIJACK_ATTEMPT",
  "mitigation_strategy": "immediate_quarantine",
  "confidence_score": 0.92
}
```

### **Get Threat Trends**
```http
GET /threat-intelligence/trends
Authorization: Bearer <token>
Query Parameters:
  - timeframe: 24h, 7d, 30d
  - trend_type: volume, severity, sources
```

---

## 📈 Monitoring API

### **Real-Time Metrics**
```http
GET /monitoring/metrics/realtime
Authorization: Bearer <token>
```

**Response:**
```json
{
  "timestamp": "2025-07-12T05:21:23Z",
  "consciousness_traffic_volume": 1250,
  "threat_detection_rate": 0.023,
  "system_resource_utilization": {
    "cpu_percent": 45.2,
    "memory_percent": 62.8,
    "network_mbps": 125.5
  },
  "active_incident_count": 3,
  "firewall_performance": {
    "avg_processing_time_ms": 12.5,
    "consensus_success_rate": 0.997
  }
}
```

### **Historical Metrics**
```http
GET /monitoring/metrics/historical
Authorization: Bearer <token>
Query Parameters:
  - metric: traffic, threats, performance, incidents
  - timeframe: 1h, 24h, 7d, 30d
  - granularity: 1m, 5m, 1h, 1d
```

### **WebSocket Real-Time Monitoring**
```javascript
const ws = new WebSocket('wss://api.novacaia.com/v1/monitoring/realtime');
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Real-time update:', data);
};
```

---

## ❌ Error Handling

### **Standard Error Response**
```json
{
  "error": {
    "code": "CONSCIOUSNESS_THREAT_DETECTED",
    "message": "High-severity consciousness threat detected and blocked",
    "details": {
      "psi_value": 15.2,
      "threat_level": "critical",
      "recommended_action": "immediate_containment"
    },
    "request_id": "req_1752315683_001",
    "timestamp": "2025-07-12T05:21:23Z"
  }
}
```

### **HTTP Status Codes**
- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Server error
- `503 Service Unavailable` - Service temporarily unavailable

### **Error Codes**
- `INVALID_PSI_VALUE` - ∂Ψ value outside acceptable range
- `CONSCIOUSNESS_THREAT_DETECTED` - Malicious consciousness activity
- `CONSENSUS_FAILURE` - Unable to achieve node consensus
- `HARDENING_FAILURE` - Hardening process failed
- `ANALYST_UNAVAILABLE` - No analysts available for incident
- `EMERGENCY_CONTAINMENT_REQUIRED` - Critical threat requires containment

---

## ⏱️ Rate Limiting

### **Rate Limits**
- **Standard API calls:** 1000 requests/hour
- **Consciousness packet processing:** 100 requests/minute
- **Emergency operations:** 10 requests/minute
- **Bulk operations:** 5 requests/minute

### **Rate Limit Headers**
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1625097600
X-RateLimit-Window: 3600
```

---

## 💻 SDK Examples

### **Python SDK**
```python
from novacaia_consciousness_security import ConsciousnessSecurityClient

# Initialize client
client = ConsciousnessSecurityClient(
    api_token="your_api_token",
    base_url="https://api.novacaia.com/v1/consciousness-security"
)

# Process consciousness packet
result = await client.firewall.process_packet(
    source_id="ai_system_001",
    psi_value=8.5,
    consciousness_signature="CONSCIOUSNESS_SIG_12345"
)

# Create security incident
incident = await client.csoc.create_incident(
    title="Suspicious Activity",
    severity="high",
    affected_systems=["novacaia_core"]
)

# Apply hardening
hardening_result = await client.hardening.apply(
    psi_value=8.5,
    disruption_level=0.3
)
```

### **JavaScript SDK**
```javascript
import { ConsciousnessSecurityClient } from '@novacaia/consciousness-security-sdk';

const client = new ConsciousnessSecurityClient({
  apiToken: 'your_api_token',
  baseUrl: 'https://api.novacaia.com/v1/consciousness-security'
});

// Process consciousness packet
const result = await client.firewall.processPacket({
  sourceId: 'ai_system_001',
  psiValue: 8.5,
  consciousnessSignature: 'CONSCIOUSNESS_SIG_12345'
});

// Get real-time monitoring
const monitoring = client.monitoring.realTime();
monitoring.on('threat-detected', (threat) => {
  console.log('Threat detected:', threat);
});
```

### **cURL Examples**
```bash
# Get firewall status
curl -H "Authorization: Bearer $API_TOKEN" \
     https://api.novacaia.com/v1/consciousness-security/firewall/status

# Process consciousness packet
curl -X POST \
     -H "Authorization: Bearer $API_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"source_id":"ai_001","psi_value":8.5}' \
     https://api.novacaia.com/v1/consciousness-security/firewall/process-packet

# Create incident
curl -X POST \
     -H "Authorization: Bearer $API_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"title":"Threat Detected","severity":"high"}' \
     https://api.novacaia.com/v1/consciousness-security/csoc/incidents
```

---

**Document Version:** 1.0  
**Last Updated:** July 12, 2025  
**Next Review:** August 12, 2025  
**Classification:** Technical Reference
