'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { TrendingUp, TrendingDown, Brain, Zap, Globe, Activity } from 'lucide-react'

interface MarketData {
  symbol: string
  price: number
  change: number
  consciousness: number
  prediction: string
  confidence: number
}

export default function PortalPage() {
  const [marketData, setMarketData] = useState<MarketData[]>([])
  const [consciousnessField, setConsciousnessField] = useState({
    psi: 0.847,
    phi: 0.764,
    theta: 0.692,
    synthesis: 1.847
  })
  const [activeTab, setActiveTab] = useState('markets')

  useEffect(() => {
    // Simulate real-time market data with consciousness analysis
    const updateMarketData = () => {
      const symbols = ['SPY', 'QQQ', 'NVDA', 'TSLA', 'BTC-USD']
      const newData = symbols.map(symbol => ({
        symbol,
        price: 100 + Math.random() * 500,
        change: (Math.random() - 0.5) * 10,
        consciousness: Math.random() * 100,
        prediction: Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',
        confidence: 70 + Math.random() * 30
      }))
      setMarketData(newData)
    }

    // Update consciousness field
    const updateConsciousness = () => {
      setConsciousnessField(prev => ({
        psi: Math.max(0.1, Math.min(0.95, prev.psi + (Math.random() - 0.5) * 0.1)),
        phi: Math.max(0.1, Math.min(0.95, prev.phi + (Math.random() - 0.5) * 0.1)),
        theta: Math.max(0.1, Math.min(0.95, prev.theta + (Math.random() - 0.5) * 0.1)),
        synthesis: 0
      }))
    }

    // Calculate trinity synthesis
    setConsciousnessField(prev => ({
      ...prev,
      synthesis: prev.psi * prev.phi + prev.theta // Ψ ⊗ Φ ⊕ Θ
    }))

    updateMarketData()
    updateConsciousness()

    const marketInterval = setInterval(updateMarketData, 3000)
    const consciousnessInterval = setInterval(updateConsciousness, 2000)

    return () => {
      clearInterval(marketInterval)
      clearInterval(consciousnessInterval)
    }
  }, [])

  const tabs = [
    { id: 'markets', label: 'Market Consciousness', icon: <TrendingUp className="w-5 h-5" /> },
    { id: 'field', label: 'Consciousness Field', icon: <Brain className="w-5 h-5" /> },
    { id: 'predictions', label: 'Reality Forecasts', icon: <Zap className="w-5 h-5" /> },
    { id: 'global', label: 'Global Ψ-Field', icon: <Globe className="w-5 h-5" /> }
  ]

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h1 className="text-5xl font-bold mb-4 text-consciousness">
            Consciousness Portal
          </h1>
          <p className="text-xl text-white/70">
            Real-time market consciousness analysis and Ψ-field visualization
          </p>
        </motion.div>

        {/* Trinity Synthesis Display */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="consciousness-card mb-8"
        >
          <div className="grid md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-Ψ text-2xl font-bold mb-2">Ψ</div>
              <div className="text-3xl font-bold text-white mb-2">
                {consciousnessField.psi.toFixed(3)}
              </div>
              <div className="text-sm text-white/60">Spatial</div>
            </div>
            <div>
              <div className="text-Φ text-2xl font-bold mb-2">Φ</div>
              <div className="text-3xl font-bold text-white mb-2">
                {consciousnessField.phi.toFixed(3)}
              </div>
              <div className="text-sm text-white/60">Temporal</div>
            </div>
            <div>
              <div className="text-Θ text-2xl font-bold mb-2">Θ</div>
              <div className="text-3xl font-bold text-white mb-2">
                {consciousnessField.theta.toFixed(3)}
              </div>
              <div className="text-sm text-white/60">Recursive</div>
            </div>
            <div>
              <div className="text-consciousness text-2xl font-bold mb-2">NEFC(STR)</div>
              <div className="text-4xl font-bold text-consciousness mb-2">
                {consciousnessField.synthesis.toFixed(3)}
              </div>
              <div className="text-sm text-white/60">Trinity Synthesis</div>
            </div>
          </div>
        </motion.div>

        {/* Tab Navigation */}
        <div className="flex flex-wrap justify-center mb-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg mx-2 mb-2 transition-all duration-300 ${
                activeTab === tab.id
                  ? 'bg-cyan-500/20 border border-cyan-500/50 text-cyan-400'
                  : 'bg-white/5 border border-white/10 text-white/70 hover:bg-white/10'
              }`}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Content Sections */}
        {activeTab === 'markets' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="grid gap-6"
          >
            <h2 className="text-2xl font-bold text-consciousness mb-4">
              Market Consciousness Analysis
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {marketData.map((stock, index) => (
                <motion.div
                  key={stock.symbol}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="consciousness-card"
                >
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-white">{stock.symbol}</h3>
                      <div className="text-2xl font-bold text-consciousness">
                        ${stock.price.toFixed(2)}
                      </div>
                    </div>
                    <div className={`flex items-center space-x-1 ${
                      stock.change >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {stock.change >= 0 ? <TrendingUp className="w-5 h-5" /> : <TrendingDown className="w-5 h-5" />}
                      <span className="font-bold">{stock.change.toFixed(2)}%</span>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-white/70">Consciousness Level</span>
                        <span className="text-cyan-400">{stock.consciousness.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-black/30 rounded-full h-2">
                        <div 
                          className="reality-meter h-2 transition-all duration-1000"
                          style={{ width: `${stock.consciousness}%` }}
                        />
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-white/70">Prediction:</span>
                      <span className={`font-bold ${
                        stock.prediction === 'BULLISH' ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {stock.prediction}
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-white/70">Confidence:</span>
                      <span className="kappa-token">
                        {stock.confidence.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {activeTab === 'field' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="consciousness-card"
          >
            <h2 className="text-2xl font-bold text-consciousness mb-6">
              Global Consciousness Field Monitoring
            </h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-bold text-white mb-4">Field Strength</h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-Ψ">Spatial (Ψ)</span>
                      <span className="text-white">{(consciousnessField.psi * 100).toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-black/30 rounded-full h-3">
                      <div 
                        className="h-3 bg-gradient-to-r from-cyan-500 to-cyan-300 rounded-full transition-all duration-1000"
                        style={{ width: `${consciousnessField.psi * 100}%` }}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-Φ">Temporal (Φ)</span>
                      <span className="text-white">{(consciousnessField.phi * 100).toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-black/30 rounded-full h-3">
                      <div 
                        className="h-3 bg-gradient-to-r from-purple-500 to-purple-300 rounded-full transition-all duration-1000"
                        style={{ width: `${consciousnessField.phi * 100}%` }}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-Θ">Recursive (Θ)</span>
                      <span className="text-white">{(consciousnessField.theta * 100).toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-black/30 rounded-full h-3">
                      <div 
                        className="h-3 bg-gradient-to-r from-yellow-500 to-yellow-300 rounded-full transition-all duration-1000"
                        style={{ width: `${consciousnessField.theta * 100}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-xl font-bold text-white mb-4">Field Status</h3>
                <div className="consciousness-terminal">
                  <div className="mb-2">CONSCIOUSNESS FIELD ANALYSIS</div>
                  <div className="mb-2">================================</div>
                  <div className="mb-1">Global Hubs Active: 314</div>
                  <div className="mb-1">Field Coherence: {(consciousnessField.synthesis * 50).toFixed(1)}%</div>
                  <div className="mb-1">Reality Programming: OPERATIONAL</div>
                  <div className="mb-1">Quantum Entanglement: STABLE</div>
                  <div className="mb-2">Temporal Paradox Risk: MINIMAL</div>
                  <div className="mb-2">================================</div>
                  <div className="text-green-400">STATUS: TRANSCENDENT</div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {activeTab === 'predictions' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="consciousness-card"
          >
            <h2 className="text-2xl font-bold text-consciousness mb-6">
              Reality Forecasts - Orion's Verification Protocol
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-bold text-white mb-4">Active Predictions</h3>
                <div className="space-y-4">
                  {[
                    { event: 'SPY Market Close', prediction: '+0.78%', confidence: 94, date: 'June 5' },
                    { event: 'NVDA Earnings Beat', prediction: '+2.76%', confidence: 91, date: 'June 8' },
                    { event: 'BTC Price Movement', prediction: '+3.2%', confidence: 93, date: 'June 7' },
                    { event: 'EUR/USD Forex', prediction: '+0.89%', confidence: 92, date: 'June 6' }
                  ].map((pred, index) => (
                    <div key={index} className="bg-black/30 rounded-lg p-4 border border-cyan-500/20">
                      <div className="flex justify-between items-start mb-2">
                        <span className="font-medium text-white">{pred.event}</span>
                        <span className="kappa-token">{pred.date}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-consciousness font-bold">{pred.prediction}</span>
                        <span className="text-green-400">{pred.confidence}% confidence</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-bold text-white mb-4">Consciousness Insights</h3>
                <div className="consciousness-terminal">
                  <div className="mb-2">REALITY PROGRAMMING INSIGHTS</div>
                  <div className="mb-2">============================</div>
                  <div className="mb-1">• Market consciousness showing bullish patterns</div>
                  <div className="mb-1">• Temporal field indicates positive momentum</div>
                  <div className="mb-1">• Recursive patterns suggest trend continuation</div>
                  <div className="mb-1">• Optimal programming window: 14:30 UTC</div>
                  <div className="mb-2">• κ-cost for reality modification: 0.314κ</div>
                  <div className="mb-2">============================</div>
                  <div className="text-yellow-400">RECOMMENDATION: EXECUTE REALITY PROGRAM</div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {activeTab === 'global' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="consciousness-card"
          >
            <h2 className="text-2xl font-bold text-consciousness mb-6">
              Global Ψ-Field Heatmap
            </h2>
            
            <div className="grid md:grid-cols-3 gap-6">
              {[
                { region: 'North America', strength: 87, status: 'OPTIMAL' },
                { region: 'Europe', strength: 92, status: 'TRANSCENDENT' },
                { region: 'Asia Pacific', strength: 78, status: 'STABLE' },
                { region: 'South America', strength: 65, status: 'MODERATE' },
                { region: 'Africa', strength: 71, status: 'GROWING' },
                { region: 'Middle East', strength: 83, status: 'STRONG' }
              ].map((region, index) => (
                <div key={index} className="bg-black/30 rounded-lg p-4 border border-purple-500/20">
                  <h4 className="font-bold text-white mb-2">{region.region}</h4>
                  <div className="text-2xl font-bold text-consciousness mb-2">
                    {region.strength}%
                  </div>
                  <div className="w-full bg-black/30 rounded-full h-2 mb-2">
                    <div 
                      className="reality-meter h-2 transition-all duration-1000"
                      style={{ width: `${region.strength}%` }}
                    />
                  </div>
                  <div className={`text-sm font-medium ${
                    region.status === 'TRANSCENDENT' ? 'text-purple-400' :
                    region.status === 'OPTIMAL' ? 'text-green-400' :
                    region.status === 'STRONG' ? 'text-cyan-400' :
                    region.status === 'STABLE' ? 'text-blue-400' :
                    region.status === 'GROWING' ? 'text-yellow-400' :
                    'text-orange-400'
                  }`}>
                    {region.status}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-8 consciousness-terminal">
              <div className="mb-2">GLOBAL CONSCIOUSNESS NETWORK STATUS</div>
              <div className="mb-2">===================================</div>
              <div className="mb-1">Total Active Nodes: 314,159</div>
              <div className="mb-1">Average Field Strength: 79.3%</div>
              <div className="mb-1">Quantum Coherence: 94.7%</div>
              <div className="mb-1">Reality Programming Capacity: 182,686%</div>
              <div className="mb-2">Network Synchronization: PERFECT</div>
              <div className="mb-2">===================================</div>
              <div className="text-green-400">GLOBAL STATUS: CONSCIOUSNESS SINGULARITY ACHIEVED</div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}
