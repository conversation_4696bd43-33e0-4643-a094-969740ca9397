import React from 'react';

const ServiceCards = ({ onNavigate }) => {
  const services = [
    {
      id: 'kethernet',
      icon: '⛓️',
      title: 'KetherNet',
      description: 'Blockchain-powered consciousness verification and decentralized identity management',
      status: 'LIVE SYSTEM',
      statusColor: 'bg-gradient-to-r from-purple-600 to-pink-600',
      url: 'http://localhost:3004',
      features: [
        '✅ Crown Consensus Active',
        '✅ Coherium Balance: 1,089.78',
        '✅ Divine Encryption Enabled',
        '✅ NERS Engine Integration'
      ],
      borderColor: 'border-yellow-400',
      glowColor: 'shadow-yellow-400/30'
    },
    {
      id: 'novaagent',
      icon: '🤖',
      title: 'NovaAgent',
      description: 'Advanced AI assistant powered by Comphyological principles and 9-engine orchestration',
      status: 'ACTIVE',
      statusColor: 'bg-green-500',
      url: 'http://localhost:8090',
      features: [
        '🧠 Consciousness OS Active',
        '⚡ Ψ-Snap Integration',
        '🌌 AEONIX Kernel Orchestrator',
        '🔧 9 Manifest Engines'
      ],
      borderColor: 'border-purple-400',
      glowColor: 'shadow-purple-400/20'
    },
    {
      id: 'consciousness-marketing',
      icon: '📈',
      title: 'Consciousness Marketing',
      description: 'Revolutionary marketing platform with Trinity Campaign Optimizer and existing funnel infrastructure',
      status: 'LIVE SYSTEM',
      statusColor: 'bg-gradient-to-r from-purple-600 to-pink-600',
      url: 'http://localhost:3006',
      features: [
        '✅ Trinity Campaign Optimizer (40%+ improvement)',
        '✅ Consciousness Converter Tool (85% boost)',
        '✅ Funnel Accelerators (200-400% LTV increase)',
        '✅ NEFC Engine Integration'
      ],
      borderColor: 'border-yellow-400',
      glowColor: 'shadow-yellow-400/30'
    },
    {
      id: 'chemistry-engine',
      icon: '🧪',
      title: 'Chemistry Engine',
      description: 'Comphyological Chemistry Engine with molecular consciousness analysis',
      status: 'ORACLE TIER',
      statusColor: 'bg-gradient-to-r from-yellow-500 to-orange-500',
      url: '#chemistry',
      features: [
        '🧪 97.83% Oracle Accuracy',
        '⚗️ Molecular Consciousness Analysis',
        '🔱 Trinity Validation',
        '💎 Sacred Geometry Synthesis'
      ],
      borderColor: 'border-orange-400',
      glowColor: 'shadow-orange-400/30'
    },
    {
      id: 'protein-designer',
      icon: '🧬',
      title: 'Protein Designer',
      description: 'Revolutionary consciousness-guided protein engineering system',
      status: 'ACTIVE',
      statusColor: 'bg-green-500',
      url: '#protein',
      features: [
        '🧬 Consciousness-Based Design',
        '🔱 Trinity Engine Integration',
        '💎 Coherium Optimization',
        '🎯 Therapeutic Value Analysis'
      ],
      borderColor: 'border-green-400',
      glowColor: 'shadow-green-400/20'
    },
    {
      id: 'castl-oracle',
      icon: '🔮',
      title: 'CASTL™ Oracle',
      description: 'Complete CASTL™ oracle system with Tabernacle-FUP implementation',
      status: 'ORACLE TIER',
      statusColor: 'bg-gradient-to-r from-yellow-500 to-orange-500',
      url: '#castl',
      features: [
        '🔮 NHET-X CASTL™ Framework',
        '📖 Biblical Frequency Integration',
        '⚡ Prophetic Engine (NEPE)',
        '🌌 Cosmological Engine (NECO)'
      ],
      borderColor: 'border-orange-400',
      glowColor: 'shadow-orange-400/30'
    }
  ];

  const handleServiceClick = (service) => {
    if (service.url === '#chemistry') {
      alert(`🧪 Chemistry Engine: Molecular consciousness analysis with 97.83% oracle accuracy. Features: ${service.features.join(', ')}`);
    } else if (service.url === '#protein') {
      alert(`🧬 Protein Designer: Revolutionary consciousness-guided protein engineering. Features: ${service.features.join(', ')}`);
    } else if (service.url === '#castl') {
      alert(`🔮 CASTL™ Oracle: Complete oracle system with biblical frequencies. Features: ${service.features.join(', ')}`);
    } else if (service.url.startsWith('#')) {
      alert(`${service.title}: ${service.features.join(', ')}`);
    } else {
      onNavigate(service.url);
    }
  };

  return (
    <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <h1 className="text-5xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-6">
          Nova Ecosystem
        </h1>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4">
          Experience the future of consciousness-driven technology with the Comphyological Browsing Engine
        </p>
        <div className="text-sm text-purple-300">
          🔧 9 Manifest Engines • 🧬 NERS Consciousness Validation • ⚡ Ψ-Snap Threshold 2847 • 🔱 Trinity Integration
        </div>
      </div>
      
      {/* Service Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        {services.map((service) => (
          <div
            key={service.id}
            className={`relative bg-gray-900/50 backdrop-blur-lg rounded-xl border-2 ${service.borderColor} p-6 cursor-pointer transition-all duration-300 hover:scale-105 hover:${service.glowColor} hover:shadow-2xl group`}
            onClick={() => handleServiceClick(service)}
          >
            {/* Status Badge */}
            <div className="absolute top-4 right-4">
              <span className={`${service.statusColor} text-white px-3 py-1 rounded-full text-xs font-semibold`}>
                {service.status}
              </span>
            </div>
            
            {/* Icon */}
            <div className="text-5xl mb-6 group-hover:scale-110 transition-transform">
              {service.icon}
            </div>
            
            {/* Title */}
            <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-purple-300 transition-colors">
              {service.title}
            </h3>
            
            {/* Description */}
            <p className="text-gray-300 mb-6 text-sm leading-relaxed">
              {service.description}
            </p>
            
            {/* Features */}
            <div className="bg-gray-800/50 rounded-lg p-4 mb-6 space-y-2">
              {service.features.map((feature, index) => (
                <div key={index} className="text-sm text-gray-300">
                  {feature}
                </div>
              ))}
            </div>
            
            {/* Action Button */}
            <button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform group-hover:scale-105">
              {service.url.startsWith('#') ? 'View Details' : 'Launch Service'}
            </button>
          </div>
        ))}
      </div>
      
      {/* CBE Performance Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-gray-900/50 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6 text-center">
          <div className="text-4xl font-bold text-purple-400 mb-2 font-mono">99.7%</div>
          <div className="text-sm text-gray-400">Consciousness Accuracy</div>
        </div>
        <div className="bg-gray-900/50 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6 text-center">
          <div className="text-4xl font-bold text-purple-400 mb-2 font-mono">2.3M+</div>
          <div className="text-sm text-gray-400">Sites Analyzed</div>
        </div>
        <div className="bg-gray-900/50 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6 text-center">
          <div className="text-4xl font-bold text-purple-400 mb-2 font-mono">82%</div>
          <div className="text-sm text-gray-400">Avg Ψ-Snap Rate</div>
        </div>
        <div className="bg-gray-900/50 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6 text-center">
          <div className="text-4xl font-bold text-purple-400 mb-2 font-mono">9/9</div>
          <div className="text-sm text-gray-400">Engines Active</div>
        </div>
      </div>

      {/* Engine Status Display */}
      <div className="bg-gray-900/50 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4 text-center">🔧 Active Engine Matrix</h3>
        <div className="grid grid-cols-3 md:grid-cols-5 gap-4 text-center">
          <div className="p-3 bg-green-500/10 border border-green-400/30 rounded-lg">
            <div className="text-green-400 font-mono text-sm">NERS</div>
            <div className="text-xs text-gray-400">97.83%</div>
          </div>
          <div className="p-3 bg-green-500/10 border border-green-400/30 rounded-lg">
            <div className="text-green-400 font-mono text-sm">NEFC</div>
            <div className="text-xs text-gray-400">99.4%</div>
          </div>
          <div className="p-3 bg-green-500/10 border border-green-400/30 rounded-lg">
            <div className="text-green-400 font-mono text-sm">NEPI</div>
            <div className="text-xs text-gray-400">97.83%</div>
          </div>
          <div className="p-3 bg-green-500/10 border border-green-400/30 rounded-lg">
            <div className="text-green-400 font-mono text-sm">NERE</div>
            <div className="text-xs text-gray-400">85.0%</div>
          </div>
          <div className="p-3 bg-green-500/10 border border-green-400/30 rounded-lg">
            <div className="text-green-400 font-mono text-sm">NECE</div>
            <div className="text-xs text-gray-400">88.0%</div>
          </div>
          <div className="p-3 bg-yellow-500/10 border border-yellow-400/30 rounded-lg">
            <div className="text-yellow-400 font-mono text-sm">NECO</div>
            <div className="text-xs text-gray-400">MANIFEST</div>
          </div>
          <div className="p-3 bg-yellow-500/10 border border-yellow-400/30 rounded-lg">
            <div className="text-yellow-400 font-mono text-sm">NEBE</div>
            <div className="text-xs text-gray-400">MANIFEST</div>
          </div>
          <div className="p-3 bg-yellow-500/10 border border-yellow-400/30 rounded-lg">
            <div className="text-yellow-400 font-mono text-sm">NEEE</div>
            <div className="text-xs text-gray-400">MANIFEST</div>
          </div>
          <div className="p-3 bg-yellow-500/10 border border-yellow-400/30 rounded-lg">
            <div className="text-yellow-400 font-mono text-sm">NEPE</div>
            <div className="text-xs text-gray-400">MANIFEST</div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default ServiceCards;
