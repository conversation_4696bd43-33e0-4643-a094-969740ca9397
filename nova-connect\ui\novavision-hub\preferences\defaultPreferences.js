/**
 * Default Preferences
 * 
 * This module provides default user preferences for the NovaVision Hub.
 */

/**
 * Default preferences
 */
export const defaultPreferences = {
  // Dashboard preferences
  dashboards: {
    // Default dashboard
    default: {
      // Dashboard layout
      layout: {
        columns: 3,
        rowHeight: 200,
        gap: 16,
        compactType: 'vertical'
      },
      // Dashboard widgets
      widgets: [
        {
          id: 'metrics',
          type: 'metrics',
          title: 'Key Metrics',
          position: { x: 0, y: 0, w: 3, h: 1 },
          settings: {
            showTrends: true,
            columns: 3
          }
        },
        {
          id: 'compliance-chart',
          type: 'chart',
          title: 'Compliance by Framework',
          position: { x: 0, y: 1, w: 2, h: 2 },
          settings: {
            chartType: 'radar',
            showLegend: true
          }
        },
        {
          id: 'security-chart',
          type: 'chart',
          title: 'Security Threats',
          position: { x: 2, y: 1, w: 1, h: 2 },
          settings: {
            chartType: 'bar',
            showLegend: true
          }
        },
        {
          id: 'alerts',
          type: 'alerts',
          title: 'Recent Alerts',
          position: { x: 0, y: 3, w: 3, h: 2 },
          settings: {
            maxItems: 5,
            showSeverity: true,
            showTimestamp: true
          }
        }
      ],
      // Dashboard settings
      settings: {
        refreshInterval: 300, // 5 minutes in seconds
        title: 'Overview Dashboard',
        description: 'Overview of key metrics and alerts',
        defaultTab: 'overview'
      }
    },
    // Risk dashboard
    risk: {
      // Dashboard layout
      layout: {
        columns: 2,
        rowHeight: 250,
        gap: 16,
        compactType: 'vertical'
      },
      // Dashboard widgets
      widgets: [
        {
          id: 'risk-score',
          type: 'metrics',
          title: 'Risk Scores',
          position: { x: 0, y: 0, w: 2, h: 1 },
          settings: {
            showTrends: true,
            columns: 2
          }
        },
        {
          id: 'risk-map',
          type: 'graph',
          title: 'Risk Relationship Map',
          position: { x: 0, y: 1, w: 2, h: 2 },
          settings: {
            layout: 'force',
            nodeSize: 'value',
            nodeColor: 'category',
            showLegend: true
          }
        },
        {
          id: 'risk-heatmap',
          type: 'heatmap',
          title: 'Risk Heatmap',
          position: { x: 0, y: 3, w: 1, h: 2 },
          settings: {
            showLegend: true,
            showLabels: true
          }
        },
        {
          id: 'risk-treemap',
          type: 'treemap',
          title: 'Risk Distribution',
          position: { x: 1, y: 3, w: 1, h: 2 },
          settings: {
            showLabels: true
          }
        }
      ],
      // Dashboard settings
      settings: {
        refreshInterval: 600, // 10 minutes in seconds
        title: 'Risk Dashboard',
        description: 'Detailed view of risk metrics and relationships',
        defaultTab: 'overview'
      }
    }
  },
  
  // UI preferences
  ui: {
    // Theme preferences
    theme: {
      // Theme name
      name: 'Default',
      // Color mode
      colorMode: 'light',
      // Enable system preference detection
      enableSystemPreference: true
    },
    // Layout preferences
    layout: {
      // Sidebar collapsed state
      sidebarCollapsed: false,
      // Content width
      contentWidth: 'fluid', // 'fluid' or 'fixed'
      // Show breadcrumbs
      showBreadcrumbs: true,
      // Show footer
      showFooter: true
    },
    // Accessibility preferences
    accessibility: {
      // Enable high contrast mode
      highContrast: false,
      // Enable reduced motion
      reducedMotion: false,
      // Font size
      fontSize: 'medium' // 'small', 'medium', 'large'
    }
  },
  
  // Data preferences
  data: {
    // Default refresh interval in seconds
    refreshInterval: 300, // 5 minutes
    // Default date range
    defaultDateRange: 'last7Days', // 'today', 'yesterday', 'last7Days', 'last30Days', 'thisMonth', 'lastMonth', 'custom'
    // Custom date range
    customDateRange: {
      start: null,
      end: null
    },
    // Default data view
    defaultView: 'chart', // 'chart', 'table', 'card'
    // Default sort order
    defaultSortOrder: 'desc', // 'asc', 'desc'
    // Default sort field
    defaultSortField: 'timestamp',
    // Default page size
    defaultPageSize: 10
  },
  
  // Notification preferences
  notifications: {
    // Enable desktop notifications
    enableDesktopNotifications: true,
    // Enable email notifications
    enableEmailNotifications: false,
    // Enable in-app notifications
    enableInAppNotifications: true,
    // Notification settings by type
    byType: {
      // Critical alerts
      critical: {
        desktop: true,
        email: true,
        inApp: true
      },
      // High alerts
      high: {
        desktop: true,
        email: false,
        inApp: true
      },
      // Medium alerts
      medium: {
        desktop: false,
        email: false,
        inApp: true
      },
      // Low alerts
      low: {
        desktop: false,
        email: false,
        inApp: true
      }
    }
  }
};
