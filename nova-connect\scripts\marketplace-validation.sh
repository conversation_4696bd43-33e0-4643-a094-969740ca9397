#!/bin/bash
# NovaConnect UAC Google Cloud Marketplace Validation Script
# This script validates the NovaConnect UAC configuration for Google Cloud Marketplace

# Set variables
PROJECT_ID=${PROJECT_ID:-"novafuse-marketplace"}
REGION=${REGION:-"us-central1"}
ZONE=${ZONE:-"us-central1-a"}
CLUSTER_NAME=${CLUSTER_NAME:-"nova-cluster"}
NAMESPACE=${NAMESPACE:-"nova-marketplace"}
RELEASE_NAME=${RELEASE_NAME:-"novafuse-uac"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to print section header
print_section() {
  echo -e "\n${YELLOW}==== $1 ====${NC}\n"
}

# Function to check if command exists
check_command() {
  if ! command -v $1 &> /dev/null; then
    echo -e "${RED}Error: $1 is required but not installed.${NC}"
    exit 1
  fi
}

# Function to check if file exists
check_file() {
  if [ ! -f "$1" ]; then
    echo -e "${RED}Error: File $1 not found.${NC}"
    exit 1
  else
    echo -e "${GREEN}✓ File $1 exists.${NC}"
  fi
}

# Function to check if directory exists
check_directory() {
  if [ ! -d "$1" ]; then
    echo -e "${RED}Error: Directory $1 not found.${NC}"
    exit 1
  else
    echo -e "${GREEN}✓ Directory $1 exists.${NC}"
  fi
}

# Check required commands
print_section "Checking required commands"
check_command "gcloud"
check_command "kubectl"
check_command "docker"
check_command "helm"
check_command "jq"

# Check required files
print_section "Checking required files"
check_file "marketplace/schema.yaml"
check_file "k8s/marketplace/deployment.yaml"
check_file "k8s/marketplace/service.yaml"
check_file "k8s/marketplace/configmap.yaml"
check_file "k8s/marketplace/secret.yaml"
check_file "k8s/marketplace/autoscaling.yaml"
check_file "Dockerfile"

# Check required directories
print_section "Checking required directories"
check_directory "marketplace/chart"
check_directory "monitoring/dashboards"

# Validate schema.yaml
print_section "Validating schema.yaml"
if ! grep -q "schemaVersion: v2" marketplace/schema.yaml; then
  echo -e "${RED}Error: schema.yaml must have schemaVersion: v2${NC}"
  exit 1
else
  echo -e "${GREEN}✓ schema.yaml has correct schemaVersion.${NC}"
fi

if ! grep -q "applicationApiVersion: v1beta1" marketplace/schema.yaml; then
  echo -e "${RED}Error: schema.yaml must have applicationApiVersion: v1beta1${NC}"
  exit 1
else
  echo -e "${GREEN}✓ schema.yaml has correct applicationApiVersion.${NC}"
fi

if ! grep -q "publishedVersion:" marketplace/schema.yaml; then
  echo -e "${RED}Error: schema.yaml must have publishedVersion${NC}"
  exit 1
else
  echo -e "${GREEN}✓ schema.yaml has publishedVersion.${NC}"
fi

if ! grep -q "billing:" marketplace/schema.yaml; then
  echo -e "${YELLOW}Warning: schema.yaml does not have billing configuration.${NC}"
else
  echo -e "${GREEN}✓ schema.yaml has billing configuration.${NC}"
fi

# Validate Dockerfile
print_section "Validating Dockerfile"
if ! grep -q "FROM.*distroless" Dockerfile; then
  echo -e "${YELLOW}Warning: Dockerfile does not use distroless base image.${NC}"
else
  echo -e "${GREEN}✓ Dockerfile uses distroless base image.${NC}"
fi

if ! grep -q "HEALTHCHECK" Dockerfile; then
  echo -e "${YELLOW}Warning: Dockerfile does not have HEALTHCHECK instruction.${NC}"
else
  echo -e "${GREEN}✓ Dockerfile has HEALTHCHECK instruction.${NC}"
fi

# Validate Kubernetes manifests
print_section "Validating Kubernetes manifests"
if ! grep -q "apiVersion: apps/v1" k8s/marketplace/deployment.yaml; then
  echo -e "${RED}Error: deployment.yaml must have apiVersion: apps/v1${NC}"
  exit 1
else
  echo -e "${GREEN}✓ deployment.yaml has correct apiVersion.${NC}"
fi

if ! grep -q "kind: Service" k8s/marketplace/service.yaml; then
  echo -e "${RED}Error: service.yaml must have kind: Service${NC}"
  exit 1
else
  echo -e "${GREEN}✓ service.yaml has correct kind.${NC}"
fi

if ! grep -q "kind: ConfigMap" k8s/marketplace/configmap.yaml; then
  echo -e "${RED}Error: configmap.yaml must have kind: ConfigMap${NC}"
  exit 1
else
  echo -e "${GREEN}✓ configmap.yaml has correct kind.${NC}"
fi

if ! grep -q "kind: Secret" k8s/marketplace/secret.yaml; then
  echo -e "${RED}Error: secret.yaml must have kind: Secret${NC}"
  exit 1
else
  echo -e "${GREEN}✓ secret.yaml has correct kind.${NC}"
fi

if ! grep -q "kind: HorizontalPodAutoscaler" k8s/marketplace/autoscaling.yaml; then
  echo -e "${RED}Error: autoscaling.yaml must have kind: HorizontalPodAutoscaler${NC}"
  exit 1
else
  echo -e "${GREEN}✓ autoscaling.yaml has correct kind.${NC}"
fi

# Validate Helm chart
print_section "Validating Helm chart"
if ! grep -q "apiVersion:" marketplace/chart/Chart.yaml; then
  echo -e "${RED}Error: Chart.yaml must have apiVersion${NC}"
  exit 1
else
  echo -e "${GREEN}✓ Chart.yaml has apiVersion.${NC}"
fi

# Validate monitoring dashboards
print_section "Validating monitoring dashboards"
if ! [ -f "monitoring/dashboards/marketplace-dashboard.json" ]; then
  echo -e "${YELLOW}Warning: marketplace-dashboard.json not found.${NC}"
else
  echo -e "${GREEN}✓ marketplace-dashboard.json exists.${NC}"
fi

if ! [ -f "monitoring/dashboards/compliance-dashboard.json" ]; then
  echo -e "${YELLOW}Warning: compliance-dashboard.json not found.${NC}"
else
  echo -e "${GREEN}✓ compliance-dashboard.json exists.${NC}"
fi

# Check Docker image build
print_section "Checking Docker image build"
echo -e "${YELLOW}Building Docker image to validate Dockerfile...${NC}"
if ! docker build -t novafuse-uac:test . > /dev/null 2>&1; then
  echo -e "${RED}Error: Docker image build failed.${NC}"
  exit 1
else
  echo -e "${GREEN}✓ Docker image build successful.${NC}"
  docker rmi novafuse-uac:test > /dev/null 2>&1
fi

# Summary
print_section "Validation Summary"
echo -e "${GREEN}✓ NovaConnect UAC is ready for Google Cloud Marketplace submission.${NC}"
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Create a Google Cloud project for the marketplace listing"
echo "2. Build and push the Docker image to Google Container Registry"
echo "3. Create a deployer image for the marketplace"
echo "4. Test the deployment in a GKE cluster"
echo "5. Submit the application to Google Cloud Marketplace"

exit 0
