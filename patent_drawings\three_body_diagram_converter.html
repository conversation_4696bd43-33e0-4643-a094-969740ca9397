<!DOCTYPE html>
<html>
<head>
    <title>Three-Body Problem Diagram Converter</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        #diagram {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            min-height: 600px;
        }
        button {
            padding: 10px 20px;
            margin: 10px 5px 10px 0;
            font-size: 16px;
            cursor: pointer;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Three-Body Problem Diagram</h1>
    <button onclick="downloadSVG()">Download SVG</button>
    <button onclick="copyToClipboard()">Copy Mermaid Code</button>
    
    <div id="diagram" class="mermaid">
        %% Three-Body Problem Reframing Diagram
        graph TD
            %% Main Components
            subgraph Comphyological_Solution["Comphyological Solution to Complex Interactions"]
                %% Three Body Components
                A[Body 1\n(System Component)]
                B[Body 2\n(System Component)]
                C[Body 3\n(System Component)]

                %% Traditional Approach Path
                A -- Traditional Interaction --> B
                B -- Traditional Interaction --> C
                C -- Traditional Interaction --> A
                
                %% Traditional Outcome
                A & B & C -- Traditional Approach --> D[Unpredictable Outcomes\n(High ∂Ψ)]
                
                %% Comphyological Solution Path
                A & B & C -- Comphyological\nGovernance (Ψᶜ) --> E{Coherence\nOptimization\nEngine}
                E -- Enforces --> F[UUFT Triadic\nStability]
                F --> G[Predictable, Stable\nInteractions (∂Ψ=0)]
            end

            %% Styling for USPTO Compliance
            classDef body fill:#ffffff,stroke:#000000,stroke-width:1.5px,shape:circle
            classDef process fill:#ffffff,stroke:#000000,stroke-width:1.5px,shape:rect
            classDef decision fill:#ffffff,stroke:#000000,stroke-width:1.5px,shape:diamond
            classDef outcome fill:#ffffff,stroke:#000000,stroke-width:1.5px,shape:rect,stroke-dasharray: 5 5
            
            %% Apply styles
            class A,B,C body
            class E decision
            class F,G process
            class D outcome
    </div>

    <h3>Mermaid Code:</h3>
    <pre id="mermaid-code">
%% Three-Body Problem Reframing Diagram
graph TD
    %% Main Components
    subgraph Comphyological_Solution["Comphyological Solution to Complex Interactions"]
        %% Three Body Components
        A[Body 1\n(System Component)]
        B[Body 2\n(System Component)]
        C[Body 3\n(System Component)]

        %% Traditional Approach Path
        A -- Traditional Interaction --> B
        B -- Traditional Interaction --> C
        C -- Traditional Interaction --> A
        
        %% Traditional Outcome
        A & B & C -- Traditional Approach --> D[Unpredictable Outcomes\n(High ∂Ψ)]
        
        %% Comphyological Solution Path
        A & B & C -- Comphyological\nGovernance (Ψᶜ) --> E{Coherence\nOptimization\nEngine}
        E -- Enforces --> F[UUFT Triadic\nStability]
        F --> G[Predictable, Stable\nInteractions (∂Ψ=0)]
    end

    %% Styling for USPTO Compliance
    classDef body fill:#ffffff,stroke:#000000,stroke-width:1.5px,shape:circle
    classDef process fill:#ffffff,stroke:#000000,stroke-width:1.5px,shape:rect
    classDef decision fill:#ffffff,stroke:#000000,stroke-width:1.5px,shape:diamond
    classDef outcome fill:#ffffff,stroke:#000000,stroke-width:1.5px,shape:rect,stroke-dasharray: 5 5
    
    %% Apply styles
    class A,B,C body
    class E decision
    class F,G process
    class D outcome
    </pre>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'Arial, sans-serif'
        });

        // Download the diagram as SVG
        function downloadSVG() {
            const svg = document.querySelector('#diagram svg');
            if (!svg) {
                alert('No diagram to download. Please try again.');
                return;
            }
            
            // Serialize the SVG
            const serializer = new XMLSerializer();
            let source = serializer.serializeToString(svg);
            
            // Add XML declaration and namespaces if not present
            if(!source.match(/^<svg[^>]+xmlns="http\:\/\/www\.w3\.org\/2000\/svg"/)){
                source = source.replace(/^<svg/, '<svg xmlns="http://www.w3.org/2000/svg"');
            }
            if(!source.match(/^<svg[^>]+"http\:\/\/www\.w3\.org\/1999\/xlink"/)){
                source = source.replace(/^<svg/, '<svg xmlns:xlink="http://www.w3.org/1999/xlink"');
            }
            
            // Add XML declaration
            source = '<?xml version="1.0" standalone="no"?>\r\n' + source;
            
            // Create a download link
            const url = "data:image/svg+xml;charset=utf-8," + encodeURIComponent(source);
            const link = document.createElement('a');
            link.download = 'three_body_problem_diagram.svg';
            link.href = url;
            link.click();
        }

        // Copy Mermaid code to clipboard
        function copyToClipboard() {
            const codeElement = document.getElementById('mermaid-code');
            const range = document.createRange();
            range.selectNode(codeElement);
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(range);
            document.execCommand('copy');
            window.getSelection().removeAllRanges();
            
            // Show feedback
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Copied!';
            setTimeout(() => {
                button.textContent = originalText;
            }, 2000);
        }
    </script>
</body>
</html>
