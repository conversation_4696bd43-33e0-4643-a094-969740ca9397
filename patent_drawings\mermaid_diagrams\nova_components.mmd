graph TD
    subgraph NovaFuse_Components["NovaFuse Components Architecture"]
        Core[Core Trinity]
        Connection[Connection Trinity]
        Intelligence[Intelligence Trinity]
        Visualization[Visualization Trinity]
        Advanced[Advanced Trinity]
    end
    Core --> Connection --> Intelligence --> Visualization --> Advanced
    
    %% USPTO Compliance
    classDef uspto fill:#fff,stroke:#000,stroke-width:2px
    class Core,Connection,Intelligence,Visualization,Advanced,NovaFuse_Components uspto
    
    %% Reference Numbers (200 series for components)
    Core:::reference200
    Connection:::reference210
    Intelligence:::reference220
    Visualization:::reference230
    Advanced:::reference240
    
    %% Component Details
    Core_Details["• NovaCore\n• NovaSecure\n• NovaNet"]:::details
    Connection_Details[Connection\n• NovaConnect\n• NovaSync\n• NovaNode]:::details
    Intel_Details[Intelligence\n• NovaLearn\n• NovaPredict\n• NovaOrchestrate]:::details
    
    %% Legend
    Legend[NovaFuse Components Architecture]:::legend
    
    %% Styling
    classDef reference200,reference210,reference220,reference230,reference240 fill:none,stroke:none,font-size:8pt
    classDef details fill:#f9f9f9,stroke:#ddd,stroke-width:1px,font-size:8pt
    classDef legend fill:#f0f0f0,stroke:#ccc,stroke-width:1px
