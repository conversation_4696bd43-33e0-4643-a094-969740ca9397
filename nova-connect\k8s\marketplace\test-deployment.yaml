apiVersion: apps/v1
kind: Deployment
metadata:
  name: novafuse-uac
  labels:
    app: novafuse-uac
    component: api
    release: test
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: helm
spec:
  replicas: 3
  selector:
    matchLabels:
      app: novafuse-uac
      component: api
      release: test
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: novafuse-uac
        component: api
        release: test
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/path: "/metrics"
        prometheus.io/port: "3001"
    spec:
      serviceAccountName: novafuse-uac-sa
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: novafuse-uac
          image: "gcr.io/novafuse-production/novafuse-uac:1.0.0"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 3001
              protocol: TCP
          env:
            - name: NODE_ENV
              value: "production"
            - name: PORT
              value: "3001"
            - name: HOST
              value: "0.0.0.0"
            - name: LOG_LEVEL
              value: "info"
            - name: MONGODB_URI
              valueFrom:
                secretKeyRef:
                  name: novafuse-uac-mongodb
                  key: uri
            - name: REDIS_URI
              valueFrom:
                secretKeyRef:
                  name: novafuse-uac-redis
                  key: uri
            - name: API_KEY
              valueFrom:
                secretKeyRef:
                  name: novafuse-uac-secrets
                  key: api-key
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: novafuse-uac-secrets
                  key: jwt-secret
            - name: CORS_ORIGIN
              value: "*"
            - name: CLUSTER_ENABLED
              value: "true"
            - name: CACHE_ENABLED
              value: "true"
            - name: COMPRESSION_ENABLED
              value: "true"
            - name: RATE_LIMIT_ENABLED
              value: "true"
            - name: RATE_LIMIT_WINDOW_MS
              value: "60000"
            - name: RATE_LIMIT_MAX
              value: "100"
            - name: HELMET_ENABLED
              value: "true"
            - name: CSRF_ENABLED
              value: "true"
            - name: IP_FILTERING_ENABLED
              value: "false"
            - name: FEATURE_FLAG_ENABLED
              value: "true"
            - name: FEATURE_FLAG_SOURCE
              value: "file"
            - name: DEFAULT_TIER
              value: "core"
            - name: GCP_PROJECT_ID
              value: "novafuse-production"
            - name: GCP_REGION
              value: "us-central1"
            - name: GCP_MONITORING_ENABLED
              value: "true"
            - name: GCP_LOGGING_ENABLED
              value: "true"
            - name: GCP_ERROR_REPORTING_ENABLED
              value: "true"
            - name: GCP_TRACING_ENABLED
              value: "true"
            - name: GCP_PROFILING_ENABLED
              value: "true"
            - name: GCP_SECRET_MANAGER_ENABLED
              value: "true"
            - name: SERVICE_NAME
              value: "novafuse-uac"
            - name: SERVICE_VERSION
              value: "1.0.0"
          volumeMounts:
            - name: config
              mountPath: /app/config
            - name: feature-flags
              mountPath: /app/config/feature_flags.json
              subPath: feature_flags.json
            - name: tmp
              mountPath: /tmp
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          startupProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 12
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
      volumes:
        - name: config
          configMap:
            name: novafuse-uac-config
        - name: feature-flags
          configMap:
            name: novafuse-uac-feature-flags
        - name: tmp
          emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - novafuse-uac
                topologyKey: kubernetes.io/hostname
      terminationGracePeriodSeconds: 60
