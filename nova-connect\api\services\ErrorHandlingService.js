/**
 * Error Handling Service
 * 
 * This service provides centralized error handling and recovery mechanisms for NovaConnect UAC.
 * It integrates with Sentry for error tracking and implements various recovery strategies.
 */

const Sentry = require('@sentry/node');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const { SecretManagerServiceClient } = require('@google-cloud/secret-manager');
const secretManager = new SecretManagerServiceClient();

// Error types
const ErrorTypes = {
  VALIDATION: 'validation_error',
  AUTHENTICATION: 'authentication_error',
  AUTHORIZATION: 'authorization_error',
  NOT_FOUND: 'not_found_error',
  TIMEOUT: 'timeout_error',
  RATE_LIMIT: 'rate_limit_error',
  CONNECTOR: 'connector_error',
  DATABASE: 'database_error',
  NETWORK: 'network_error',
  INTERNAL: 'internal_error'
};

// Recovery strategies
const RecoveryStrategies = {
  RETRY: 'retry',
  CIRCUIT_BREAKER: 'circuit_breaker',
  FALLBACK: 'fallback',
  TIMEOUT: 'timeout',
  BULKHEAD: 'bulkhead'
};

class ErrorHandlingService {
  constructor() {
    this.initialized = false;
    this.circuitBreakers = new Map();
    this.retryPolicies = new Map();
    this.fallbackHandlers = new Map();
    this.timeoutHandlers = new Map();
    this.bulkheadLimits = new Map();
    
    // Initialize Sentry if enabled
    if (process.env.SENTRY_ENABLED === 'true') {
      this._initializeSentry();
    }
  }
  
  /**
   * Initialize Sentry for error tracking
   */
  async _initializeSentry() {
    try {
      let dsn = process.env.SENTRY_DSN;
      
      // If DSN is not provided in environment variables, try to get it from Secret Manager
      if (!dsn && process.env.GOOGLE_CLOUD_PROJECT && process.env.SENTRY_DSN_SECRET_NAME) {
        dsn = await this._getSecretFromSecretManager(process.env.SENTRY_DSN_SECRET_NAME);
      }
      
      if (!dsn) {
        logger.warn('Sentry DSN not found. Error tracking will be disabled.');
        return;
      }
      
      Sentry.init({
        dsn,
        environment: process.env.NODE_ENV || 'development',
        release: process.env.npm_package_version || '1.0.0',
        integrations: [
          new Sentry.Integrations.Http({ tracing: true }),
          new Sentry.Integrations.Express({ app: null }),
          new Sentry.Integrations.Mongo()
        ],
        tracesSampleRate: 1.0,
        attachStacktrace: true,
        normalizeDepth: 10
      });
      
      this.initialized = true;
      logger.info('Sentry initialized for error tracking');
    } catch (error) {
      logger.error('Failed to initialize Sentry', { error });
    }
  }
  
  /**
   * Get a secret from Google Cloud Secret Manager
   * @param {string} secretName - Secret name
   * @returns {Promise<string>} - Secret value
   */
  async _getSecretFromSecretManager(secretName) {
    try {
      const projectId = process.env.GOOGLE_CLOUD_PROJECT;
      const name = `projects/${projectId}/secrets/${secretName}/versions/latest`;
      const [version] = await secretManager.accessSecretVersion({ name });
      return version.payload.data.toString('utf8');
    } catch (error) {
      logger.error('Failed to get secret from Secret Manager', { error, secretName });
      return null;
    }
  }
  
  /**
   * Handle an error
   * @param {Error} error - Error object
   * @param {Object} context - Error context
   * @returns {Object} - Error response
   */
  handleError(error, context = {}) {
    // Generate error ID for tracking
    const errorId = uuidv4();
    
    // Determine error type
    const errorType = this._determineErrorType(error);
    
    // Log the error
    this._logError(error, errorType, errorId, context);
    
    // Track the error in Sentry if initialized
    if (this.initialized) {
      this._trackErrorInSentry(error, errorType, errorId, context);
    }
    
    // Create error response
    const errorResponse = this._createErrorResponse(error, errorType, errorId);
    
    // Apply recovery strategy if available
    const recoveryResult = this._applyRecoveryStrategy(error, errorType, context);
    if (recoveryResult) {
      errorResponse.recovery = recoveryResult;
    }
    
    return errorResponse;
  }
  
  /**
   * Determine the type of error
   * @param {Error} error - Error object
   * @returns {string} - Error type
   */
  _determineErrorType(error) {
    if (error.name === 'ValidationError' || error.name === 'ValidatorError') {
      return ErrorTypes.VALIDATION;
    }
    
    if (error.name === 'UnauthorizedError' || error.name === 'JsonWebTokenError') {
      return ErrorTypes.AUTHENTICATION;
    }
    
    if (error.name === 'ForbiddenError') {
      return ErrorTypes.AUTHORIZATION;
    }
    
    if (error.name === 'NotFoundError' || error.message.includes('not found')) {
      return ErrorTypes.NOT_FOUND;
    }
    
    if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
      return ErrorTypes.TIMEOUT;
    }
    
    if (error.name === 'RateLimitError' || error.message.includes('rate limit')) {
      return ErrorTypes.RATE_LIMIT;
    }
    
    if (error.name === 'ConnectorError' || error.message.includes('connector')) {
      return ErrorTypes.CONNECTOR;
    }
    
    if (error.name === 'MongoError' || error.name === 'MongooseError') {
      return ErrorTypes.DATABASE;
    }
    
    if (error.name === 'NetworkError' || error.message.includes('network')) {
      return ErrorTypes.NETWORK;
    }
    
    return ErrorTypes.INTERNAL;
  }
  
  /**
   * Log an error
   * @param {Error} error - Error object
   * @param {string} errorType - Error type
   * @param {string} errorId - Error ID
   * @param {Object} context - Error context
   */
  _logError(error, errorType, errorId, context) {
    logger.error(`[${errorId}] ${errorType}: ${error.message}`, {
      errorId,
      errorType,
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack,
      context
    });
  }
  
  /**
   * Track an error in Sentry
   * @param {Error} error - Error object
   * @param {string} errorType - Error type
   * @param {string} errorId - Error ID
   * @param {Object} context - Error context
   */
  _trackErrorInSentry(error, errorType, errorId, context) {
    Sentry.withScope((scope) => {
      // Add error context to Sentry scope
      scope.setTag('error_type', errorType);
      scope.setTag('error_id', errorId);
      
      // Add additional context
      if (context.user) {
        scope.setUser(context.user);
      }
      
      if (context.tags) {
        Object.entries(context.tags).forEach(([key, value]) => {
          scope.setTag(key, value);
        });
      }
      
      if (context.extra) {
        Object.entries(context.extra).forEach(([key, value]) => {
          scope.setExtra(key, value);
        });
      }
      
      // Capture the error
      Sentry.captureException(error);
    });
  }
  
  /**
   * Create an error response
   * @param {Error} error - Error object
   * @param {string} errorType - Error type
   * @param {string} errorId - Error ID
   * @returns {Object} - Error response
   */
  _createErrorResponse(error, errorType, errorId) {
    // Map error types to HTTP status codes
    const statusCodes = {
      [ErrorTypes.VALIDATION]: 400,
      [ErrorTypes.AUTHENTICATION]: 401,
      [ErrorTypes.AUTHORIZATION]: 403,
      [ErrorTypes.NOT_FOUND]: 404,
      [ErrorTypes.TIMEOUT]: 408,
      [ErrorTypes.RATE_LIMIT]: 429,
      [ErrorTypes.CONNECTOR]: 502,
      [ErrorTypes.DATABASE]: 500,
      [ErrorTypes.NETWORK]: 503,
      [ErrorTypes.INTERNAL]: 500
    };
    
    // Create error response
    const errorResponse = {
      error: {
        id: errorId,
        type: errorType,
        message: this._getSafeErrorMessage(error, errorType),
        status: statusCodes[errorType] || 500
      }
    };
    
    // Add validation errors if available
    if (errorType === ErrorTypes.VALIDATION && error.errors) {
      errorResponse.error.validation = error.errors;
    }
    
    // Add stack trace in development mode
    if (process.env.NODE_ENV === 'development') {
      errorResponse.error.stack = error.stack;
    }
    
    return errorResponse;
  }
  
  /**
   * Get a safe error message for the client
   * @param {Error} error - Error object
   * @param {string} errorType - Error type
   * @returns {string} - Safe error message
   */
  _getSafeErrorMessage(error, errorType) {
    // In production, return generic messages for certain error types
    if (process.env.NODE_ENV === 'production') {
      switch (errorType) {
        case ErrorTypes.INTERNAL:
          return 'An internal server error occurred';
        case ErrorTypes.DATABASE:
          return 'A database error occurred';
        case ErrorTypes.NETWORK:
          return 'A network error occurred';
        default:
          return error.message;
      }
    }
    
    return error.message;
  }
  
  /**
   * Apply a recovery strategy for the error
   * @param {Error} error - Error object
   * @param {string} errorType - Error type
   * @param {Object} context - Error context
   * @returns {Object|null} - Recovery result or null
   */
  _applyRecoveryStrategy(error, errorType, context) {
    // Determine the appropriate recovery strategy based on error type
    let strategy = null;
    
    switch (errorType) {
      case ErrorTypes.TIMEOUT:
      case ErrorTypes.NETWORK:
      case ErrorTypes.CONNECTOR:
        strategy = RecoveryStrategies.RETRY;
        break;
      case ErrorTypes.RATE_LIMIT:
        strategy = RecoveryStrategies.CIRCUIT_BREAKER;
        break;
      case ErrorTypes.DATABASE:
        strategy = RecoveryStrategies.FALLBACK;
        break;
      default:
        return null;
    }
    
    // Apply the recovery strategy
    switch (strategy) {
      case RecoveryStrategies.RETRY:
        return this._applyRetryStrategy(context);
      case RecoveryStrategies.CIRCUIT_BREAKER:
        return this._applyCircuitBreakerStrategy(context);
      case RecoveryStrategies.FALLBACK:
        return this._applyFallbackStrategy(context);
      case RecoveryStrategies.TIMEOUT:
        return this._applyTimeoutStrategy(context);
      case RecoveryStrategies.BULKHEAD:
        return this._applyBulkheadStrategy(context);
      default:
        return null;
    }
  }
  
  /**
   * Apply retry strategy
   * @param {Object} context - Error context
   * @returns {Object} - Recovery result
   */
  _applyRetryStrategy(context) {
    const { resource } = context;
    
    // Get retry policy for the resource
    const retryPolicy = this.retryPolicies.get(resource) || {
      maxRetries: 3,
      initialDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2,
      jitter: true
    };
    
    // Calculate retry delay with exponential backoff and optional jitter
    const retryCount = context.retryCount || 0;
    let delay = retryPolicy.initialDelay * Math.pow(retryPolicy.backoffFactor, retryCount);
    delay = Math.min(delay, retryPolicy.maxDelay);
    
    // Add jitter to prevent thundering herd problem
    if (retryPolicy.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5);
    }
    
    return {
      strategy: RecoveryStrategies.RETRY,
      retryCount,
      maxRetries: retryPolicy.maxRetries,
      delay: Math.round(delay),
      retryAfter: new Date(Date.now() + delay).toISOString()
    };
  }
  
  /**
   * Apply circuit breaker strategy
   * @param {Object} context - Error context
   * @returns {Object} - Recovery result
   */
  _applyCircuitBreakerStrategy(context) {
    const { resource } = context;
    
    // Get or create circuit breaker for the resource
    let circuitBreaker = this.circuitBreakers.get(resource);
    if (!circuitBreaker) {
      circuitBreaker = {
        state: 'closed',
        failureCount: 0,
        lastFailureTime: null,
        failureThreshold: 5,
        resetTimeout: 30000
      };
      this.circuitBreakers.set(resource, circuitBreaker);
    }
    
    // Update circuit breaker state
    circuitBreaker.failureCount++;
    circuitBreaker.lastFailureTime = Date.now();
    
    // Check if circuit breaker should trip
    if (circuitBreaker.state === 'closed' && circuitBreaker.failureCount >= circuitBreaker.failureThreshold) {
      circuitBreaker.state = 'open';
      
      // Schedule circuit breaker reset
      setTimeout(() => {
        circuitBreaker.state = 'half-open';
        circuitBreaker.failureCount = 0;
      }, circuitBreaker.resetTimeout);
    }
    
    return {
      strategy: RecoveryStrategies.CIRCUIT_BREAKER,
      state: circuitBreaker.state,
      failureCount: circuitBreaker.failureCount,
      failureThreshold: circuitBreaker.failureThreshold,
      resetAfter: new Date(circuitBreaker.lastFailureTime + circuitBreaker.resetTimeout).toISOString()
    };
  }
  
  /**
   * Apply fallback strategy
   * @param {Object} context - Error context
   * @returns {Object} - Recovery result
   */
  _applyFallbackStrategy(context) {
    const { resource } = context;
    
    // Get fallback handler for the resource
    const fallbackHandler = this.fallbackHandlers.get(resource);
    if (!fallbackHandler) {
      return {
        strategy: RecoveryStrategies.FALLBACK,
        available: false
      };
    }
    
    // Execute fallback handler
    try {
      const fallbackResult = fallbackHandler(context);
      return {
        strategy: RecoveryStrategies.FALLBACK,
        available: true,
        result: fallbackResult
      };
    } catch (error) {
      logger.error('Fallback handler failed', { error, resource });
      return {
        strategy: RecoveryStrategies.FALLBACK,
        available: true,
        success: false
      };
    }
  }
  
  /**
   * Apply timeout strategy
   * @param {Object} context - Error context
   * @returns {Object} - Recovery result
   */
  _applyTimeoutStrategy(context) {
    const { resource } = context;
    
    // Get timeout handler for the resource
    const timeoutHandler = this.timeoutHandlers.get(resource);
    if (!timeoutHandler) {
      return {
        strategy: RecoveryStrategies.TIMEOUT,
        available: false
      };
    }
    
    // Execute timeout handler
    try {
      const timeoutResult = timeoutHandler(context);
      return {
        strategy: RecoveryStrategies.TIMEOUT,
        available: true,
        result: timeoutResult
      };
    } catch (error) {
      logger.error('Timeout handler failed', { error, resource });
      return {
        strategy: RecoveryStrategies.TIMEOUT,
        available: true,
        success: false
      };
    }
  }
  
  /**
   * Apply bulkhead strategy
   * @param {Object} context - Error context
   * @returns {Object} - Recovery result
   */
  _applyBulkheadStrategy(context) {
    const { resource } = context;
    
    // Get bulkhead limit for the resource
    const bulkheadLimit = this.bulkheadLimits.get(resource);
    if (!bulkheadLimit) {
      return {
        strategy: RecoveryStrategies.BULKHEAD,
        available: false
      };
    }
    
    return {
      strategy: RecoveryStrategies.BULKHEAD,
      available: true,
      limit: bulkheadLimit
    };
  }
  
  /**
   * Register a retry policy for a resource
   * @param {string} resource - Resource name
   * @param {Object} policy - Retry policy
   */
  registerRetryPolicy(resource, policy) {
    this.retryPolicies.set(resource, {
      maxRetries: policy.maxRetries || 3,
      initialDelay: policy.initialDelay || 1000,
      maxDelay: policy.maxDelay || 10000,
      backoffFactor: policy.backoffFactor || 2,
      jitter: policy.jitter !== false
    });
  }
  
  /**
   * Register a circuit breaker for a resource
   * @param {string} resource - Resource name
   * @param {Object} options - Circuit breaker options
   */
  registerCircuitBreaker(resource, options) {
    this.circuitBreakers.set(resource, {
      state: 'closed',
      failureCount: 0,
      lastFailureTime: null,
      failureThreshold: options.failureThreshold || 5,
      resetTimeout: options.resetTimeout || 30000
    });
  }
  
  /**
   * Register a fallback handler for a resource
   * @param {string} resource - Resource name
   * @param {Function} handler - Fallback handler
   */
  registerFallbackHandler(resource, handler) {
    this.fallbackHandlers.set(resource, handler);
  }
  
  /**
   * Register a timeout handler for a resource
   * @param {string} resource - Resource name
   * @param {Function} handler - Timeout handler
   */
  registerTimeoutHandler(resource, handler) {
    this.timeoutHandlers.set(resource, handler);
  }
  
  /**
   * Register a bulkhead limit for a resource
   * @param {string} resource - Resource name
   * @param {number} limit - Bulkhead limit
   */
  registerBulkheadLimit(resource, limit) {
    this.bulkheadLimits.set(resource, limit);
  }
  
  /**
   * Create a wrapped function with retry logic
   * @param {Function} fn - Function to wrap
   * @param {Object} options - Retry options
   * @returns {Function} - Wrapped function
   */
  withRetry(fn, options = {}) {
    const retryOptions = {
      maxRetries: options.maxRetries || 3,
      initialDelay: options.initialDelay || 1000,
      maxDelay: options.maxDelay || 10000,
      backoffFactor: options.backoffFactor || 2,
      jitter: options.jitter !== false,
      retryableErrors: options.retryableErrors || [ErrorTypes.TIMEOUT, ErrorTypes.NETWORK, ErrorTypes.CONNECTOR]
    };
    
    return async (...args) => {
      let lastError;
      
      for (let retryCount = 0; retryCount <= retryOptions.maxRetries; retryCount++) {
        try {
          // If not the first attempt, wait before retrying
          if (retryCount > 0) {
            // Calculate delay with exponential backoff and optional jitter
            let delay = retryOptions.initialDelay * Math.pow(retryOptions.backoffFactor, retryCount - 1);
            delay = Math.min(delay, retryOptions.maxDelay);
            
            // Add jitter to prevent thundering herd problem
            if (retryOptions.jitter) {
              delay = delay * (0.5 + Math.random() * 0.5);
            }
            
            await new Promise(resolve => setTimeout(resolve, delay));
          }
          
          // Execute the function
          return await fn(...args);
        } catch (error) {
          lastError = error;
          
          // Determine error type
          const errorType = this._determineErrorType(error);
          
          // Check if error is retryable
          if (!retryOptions.retryableErrors.includes(errorType)) {
            throw error;
          }
          
          // Log retry attempt
          if (retryCount < retryOptions.maxRetries) {
            logger.warn(`Retrying after ${errorType} (attempt ${retryCount + 1}/${retryOptions.maxRetries})`, {
              errorMessage: error.message,
              retryCount: retryCount + 1,
              maxRetries: retryOptions.maxRetries
            });
          }
        }
      }
      
      // If we've exhausted all retries, throw the last error
      throw lastError;
    };
  }
  
  /**
   * Create a wrapped function with circuit breaker logic
   * @param {Function} fn - Function to wrap
   * @param {Object} options - Circuit breaker options
   * @returns {Function} - Wrapped function
   */
  withCircuitBreaker(fn, options = {}) {
    const resource = options.resource || fn.name || 'anonymous';
    
    // Register circuit breaker if not already registered
    if (!this.circuitBreakers.has(resource)) {
      this.registerCircuitBreaker(resource, options);
    }
    
    return async (...args) => {
      const circuitBreaker = this.circuitBreakers.get(resource);
      
      // Check circuit breaker state
      if (circuitBreaker.state === 'open') {
        throw new Error(`Circuit breaker for ${resource} is open`);
      }
      
      try {
        // Execute the function
        const result = await fn(...args);
        
        // If circuit breaker is half-open and the call succeeded, close it
        if (circuitBreaker.state === 'half-open') {
          circuitBreaker.state = 'closed';
          circuitBreaker.failureCount = 0;
        }
        
        return result;
      } catch (error) {
        // Update circuit breaker state
        circuitBreaker.failureCount++;
        circuitBreaker.lastFailureTime = Date.now();
        
        // Check if circuit breaker should trip
        if (circuitBreaker.state === 'closed' && circuitBreaker.failureCount >= circuitBreaker.failureThreshold) {
          circuitBreaker.state = 'open';
          
          // Schedule circuit breaker reset
          setTimeout(() => {
            circuitBreaker.state = 'half-open';
            circuitBreaker.failureCount = 0;
          }, circuitBreaker.resetTimeout);
        }
        
        throw error;
      }
    };
  }
  
  /**
   * Create a wrapped function with timeout logic
   * @param {Function} fn - Function to wrap
   * @param {Object} options - Timeout options
   * @returns {Function} - Wrapped function
   */
  withTimeout(fn, options = {}) {
    const timeoutMs = options.timeoutMs || 30000;
    
    return async (...args) => {
      return new Promise((resolve, reject) => {
        // Create a timeout
        const timeoutId = setTimeout(() => {
          const timeoutError = new Error(`Operation timed out after ${timeoutMs}ms`);
          timeoutError.name = 'TimeoutError';
          reject(timeoutError);
        }, timeoutMs);
        
        // Execute the function
        fn(...args)
          .then(result => {
            clearTimeout(timeoutId);
            resolve(result);
          })
          .catch(error => {
            clearTimeout(timeoutId);
            reject(error);
          });
      });
    };
  }
  
  /**
   * Create a wrapped function with fallback logic
   * @param {Function} fn - Function to wrap
   * @param {Function} fallbackFn - Fallback function
   * @returns {Function} - Wrapped function
   */
  withFallback(fn, fallbackFn) {
    return async (...args) => {
      try {
        // Execute the primary function
        return await fn(...args);
      } catch (error) {
        // Log the error
        logger.warn('Primary function failed, using fallback', {
          error: error.message,
          function: fn.name || 'anonymous'
        });
        
        // Execute the fallback function
        return await fallbackFn(...args);
      }
    };
  }
  
  /**
   * Create a wrapped function with bulkhead logic
   * @param {Function} fn - Function to wrap
   * @param {Object} options - Bulkhead options
   * @returns {Function} - Wrapped function
   */
  withBulkhead(fn, options = {}) {
    const resource = options.resource || fn.name || 'anonymous';
    const limit = options.limit || 10;
    let activeCount = 0;
    
    // Register bulkhead limit
    this.registerBulkheadLimit(resource, limit);
    
    return async (...args) => {
      // Check if bulkhead limit is reached
      if (activeCount >= limit) {
        const bulkheadError = new Error(`Bulkhead limit reached for ${resource}`);
        bulkheadError.name = 'BulkheadError';
        throw bulkheadError;
      }
      
      // Increment active count
      activeCount++;
      
      try {
        // Execute the function
        return await fn(...args);
      } finally {
        // Decrement active count
        activeCount--;
      }
    };
  }
}

module.exports = new ErrorHandlingService();
