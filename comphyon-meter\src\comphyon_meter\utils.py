"""
ComphyonΨᶜ Utilities - Helper functions for the ComphyonΨᶜ Meter.
"""

import json
import time
import numpy as np
import os
from datetime import datetime

def generate_random_tensor(mean=0.7, std=0.1, size=4):
    """
    Generate a random tensor for testing.
    
    Args:
        mean: Mean value for the normal distribution
        std: Standard deviation for the normal distribution
        size: Size of the tensor
        
    Returns:
        numpy.ndarray: Random tensor
    """
    return np.clip(np.random.normal(mean, std, size), 0.1, 0.99)

def save_metrics_to_json(metrics, filepath):
    """
    Save metrics to a JSON file.
    
    Args:
        metrics: Metrics dictionary or list of metrics dictionaries
        filepath: Path to save the JSON file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with open(filepath, 'w') as f:
            json.dump(metrics, f, indent=2)
        return True
    except Exception as e:
        print(f"Error saving metrics to JSON: {e}")
        return False

def load_metrics_from_json(filepath):
    """
    Load metrics from a JSON file.
    
    Args:
        filepath: Path to the JSON file
        
    Returns:
        dict or list: Loaded metrics
    """
    try:
        with open(filepath, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading metrics from JSON: {e}")
        return None

def create_metrics_log_directory():
    """
    Create a directory for storing metrics logs.
    
    Returns:
        str: Path to the created directory
    """
    base_dir = os.path.join(os.getcwd(), 'logs')
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = os.path.join(base_dir, f'metrics_{timestamp}')
    
    os.makedirs(log_dir, exist_ok=True)
    return log_dir

def format_metrics_for_display(metrics):
    """
    Format metrics for display in a terminal or dashboard.
    
    Args:
        metrics: Metrics dictionary from ComphyonCalculator
        
    Returns:
        str: Formatted metrics string
    """
    if not metrics:
        return "No metrics available"
    
    timestamp = datetime.fromtimestamp(metrics['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
    
    formatted = f"ComphyonΨᶜ Metrics - {timestamp}\n"
    formatted += "=" * 50 + "\n"
    formatted += f"Velocity: {metrics['velocity']:.4f} Cph-Flux\n"
    formatted += f"Acceleration: {metrics['acceleration']:.4f} Cph\n"
    formatted += "-" * 50 + "\n"
    
    if 'domain_energies' in metrics:
        formatted += "Domain Energies:\n"
        for domain, energy in metrics['domain_energies'].items():
            formatted += f"  {domain}: {energy:.4f}\n"
        formatted += "-" * 50 + "\n"
    
    if 'gradients' in metrics:
        formatted += "Energy Gradients:\n"
        for domain, gradient in metrics['gradients'].items():
            formatted += f"  {domain}: {gradient:.4f}\n"
    
    return formatted

def calculate_safety_status(acceleration):
    """
    Calculate safety status based on ComphyonΨᶜ Acceleration.
    
    Args:
        acceleration: ComphyonΨᶜ Acceleration value
        
    Returns:
        tuple: (status, color) where status is one of 'Safe', 'Warning', 'Critical', 'Emergency'
    """
    if acceleration < 1.0:
        return 'Safe', 'green'
    elif acceleration < 2.5:
        return 'Warning', 'yellow'
    elif acceleration < 5.0:
        return 'Critical', 'orange'
    else:
        return 'Emergency', 'red'
