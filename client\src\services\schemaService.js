/**
 * Schema Service
 * 
 * Service for fetching and managing form schemas.
 */

import axios from 'axios';

const API_URL = '/api/v1/schemas';

/**
 * Get schema by entity type
 * @param {string} entityType - Entity type
 * @returns {Promise<Object>} - Schema
 */
export const getSchema = async (entityType) => {
  try {
    const response = await axios.get(`${API_URL}/${entityType}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching schema for ${entityType}:`, error);
    throw error;
  }
};

/**
 * List all schemas
 * @returns {Promise<Array>} - List of schemas
 */
export const listSchemas = async () => {
  try {
    const response = await axios.get(API_URL);
    return response.data;
  } catch (error) {
    console.error('Error fetching schemas:', error);
    throw error;
  }
};

/**
 * Save schema
 * @param {Object} schema - Schema to save
 * @returns {Promise<Object>} - Saved schema
 */
export const saveSchema = async (schema) => {
  try {
    const response = await axios.post(API_URL, schema);
    return response.data;
  } catch (error) {
    console.error('Error saving schema:', error);
    throw error;
  }
};

/**
 * Delete schema
 * @param {string} entityType - Entity type
 * @returns {Promise<Object>} - Response
 */
export const deleteSchema = async (entityType) => {
  try {
    const response = await axios.delete(`${API_URL}/${entityType}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting schema for ${entityType}:`, error);
    throw error;
  }
};

/**
 * Get local schema (for development/testing)
 * @param {string} formId - Form ID
 * @returns {Object} - Schema
 */
export const getLocalSchema = (formId) => {
  // Sample schemas for development/testing
  const schemas = {
    'user-registration': {
      fields: [
        {
          name: 'email',
          label: 'Email Address',
          type: 'email',
          required: true,
          description: "We'll never share your email."
        },
        {
          name: 'password',
          label: 'Password',
          type: 'password',
          required: true
        },
        {
          name: 'confirmPassword',
          label: 'Confirm Password',
          type: 'password',
          required: true
        },
        {
          name: 'role',
          label: 'Role',
          type: 'select',
          options: [
            { label: 'Admin', value: 'admin' },
            { label: 'User', value: 'user' }
          ],
          required: true
        }
      ],
      submitLabel: 'Create Account'
    },
    'user-login': {
      fields: [
        {
          name: 'email',
          label: 'Email Address',
          type: 'email',
          required: true
        },
        {
          name: 'password',
          label: 'Password',
          type: 'password',
          required: true
        },
        {
          name: 'rememberMe',
          label: 'Remember Me',
          type: 'checkbox'
        }
      ],
      submitLabel: 'Sign In'
    },
    'control-create': {
      fields: [
        {
          name: 'name',
          label: 'Control Name',
          type: 'text',
          required: true,
          placeholder: 'Enter control name'
        },
        {
          name: 'description',
          label: 'Description',
          type: 'textarea',
          required: true,
          placeholder: 'Enter control description'
        },
        {
          name: 'framework',
          label: 'Framework',
          type: 'select',
          options: [
            { label: 'SOC 2', value: 'soc2' },
            { label: 'GDPR', value: 'gdpr' },
            { label: 'HIPAA', value: 'hipaa' },
            { label: 'ISO 27001', value: 'iso27001' },
            { label: 'PCI DSS', value: 'pci-dss' }
          ],
          required: true
        },
        {
          name: 'category',
          label: 'Category',
          type: 'select',
          options: [
            { label: 'Access Control', value: 'access-control' },
            { label: 'Data Protection', value: 'data-protection' },
            { label: 'Network Security', value: 'network-security' },
            { label: 'Logging & Monitoring', value: 'logging-monitoring' },
            { label: 'Incident Response', value: 'incident-response' },
            { label: 'Business Continuity', value: 'business-continuity' },
            { label: 'Compliance', value: 'compliance' }
          ],
          required: true
        },
        {
          name: 'status',
          label: 'Status',
          type: 'radio',
          options: [
            { label: 'Active', value: 'active' },
            { label: 'Inactive', value: 'inactive' },
            { label: 'Draft', value: 'draft' }
          ],
          required: true
        }
      ],
      submitLabel: 'Create Control'
    }
  };

  return schemas[formId] || null;
};

export default {
  getSchema,
  listSchemas,
  saveSchema,
  deleteSchema,
  getLocalSchema
};
