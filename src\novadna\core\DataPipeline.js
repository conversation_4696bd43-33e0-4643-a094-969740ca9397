/**
 * DataPipeline.js
 * 
 * This module implements a zero-storage data pipeline for NovaDNA.
 * It ensures that medical data flows securely between endpoints without
 * being stored centrally, maintaining privacy and security.
 */

const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const EventEmitter = require('events');

/**
 * DataPipeline class for handling secure data flow without central storage
 */
class DataPipeline extends EventEmitter {
  constructor(options = {}) {
    super();
    this.encryptionAlgorithm = options.encryptionAlgorithm || 'aes-256-gcm';
    this.compressionEnabled = options.compressionEnabled !== false;
    this.maxDataSize = options.maxDataSize || 10 * 1024 * 1024; // 10MB
    this.activeTransfers = new Map();
    this.transferTimeout = options.transferTimeout || 60000; // 60 seconds
    this.securityLevel = options.securityLevel || 'high';
  }

  /**
   * Create a new data transfer session
   * @param {Object} metadata - Metadata about the transfer
   * @returns {Object} - The transfer session details
   */
  createTransferSession(metadata = {}) {
    const sessionId = uuidv4();
    const encryptionKey = crypto.randomBytes(32).toString('hex');
    const iv = crypto.randomBytes(16).toString('hex');
    
    const session = {
      sessionId,
      created: new Date().toISOString(),
      status: 'CREATED',
      encryptionKey,
      iv,
      metadata: {
        ...metadata,
        securityLevel: this.securityLevel,
        createdAt: new Date().toISOString()
      },
      chunks: [],
      completedChunks: 0,
      totalChunks: 0
    };
    
    this.activeTransfers.set(sessionId, session);
    
    // Set timeout to clean up abandoned sessions
    setTimeout(() => {
      const currentSession = this.activeTransfers.get(sessionId);
      if (currentSession && currentSession.status !== 'COMPLETED') {
        currentSession.status = 'TIMEOUT';
        this.emit('session:timeout', { sessionId });
        this.activeTransfers.delete(sessionId);
      }
    }, this.transferTimeout);
    
    return {
      sessionId,
      encryptionKey,
      iv,
      created: session.created,
      expiresIn: this.transferTimeout / 1000
    };
  }

  /**
   * Encrypt data for secure transfer
   * @param {Buffer|String} data - The data to encrypt
   * @param {String} encryptionKey - The encryption key
   * @param {String} iv - The initialization vector
   * @returns {Object} - The encrypted data
   * @private
   */
  encryptData(data, encryptionKey, iv) {
    const key = Buffer.from(encryptionKey, 'hex');
    const ivBuffer = Buffer.from(iv, 'hex');
    const cipher = crypto.createCipheriv(this.encryptionAlgorithm, key, ivBuffer);
    
    const dataBuffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
    const encrypted = Buffer.concat([cipher.update(dataBuffer), cipher.final()]);
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted: encrypted.toString('base64'),
      authTag: authTag.toString('base64')
    };
  }

  /**
   * Decrypt data received from secure transfer
   * @param {String} encryptedData - The encrypted data (base64)
   * @param {String} authTag - The authentication tag (base64)
   * @param {String} encryptionKey - The encryption key
   * @param {String} iv - The initialization vector
   * @returns {Buffer} - The decrypted data
   * @private
   */
  decryptData(encryptedData, authTag, encryptionKey, iv) {
    const key = Buffer.from(encryptionKey, 'hex');
    const ivBuffer = Buffer.from(iv, 'hex');
    const decipher = crypto.createDecipheriv(this.encryptionAlgorithm, key, ivBuffer);
    
    decipher.setAuthTag(Buffer.from(authTag, 'base64'));
    
    const encrypted = Buffer.from(encryptedData, 'base64');
    return Buffer.concat([decipher.update(encrypted), decipher.final()]);
  }

  /**
   * Send data through the pipeline
   * @param {Object} data - The data to send
   * @param {String} sessionId - The transfer session ID
   * @returns {Object} - The transfer result
   */
  async sendData(data, sessionId) {
    const session = this.activeTransfers.get(sessionId);
    
    if (!session) {
      throw new Error(`Session ${sessionId} not found or expired`);
    }
    
    if (session.status !== 'CREATED') {
      throw new Error(`Session ${sessionId} is in invalid state: ${session.status}`);
    }
    
    const dataString = typeof data === 'string' ? data : JSON.stringify(data);
    const dataBuffer = Buffer.from(dataString);
    
    if (dataBuffer.length > this.maxDataSize) {
      throw new Error(`Data size exceeds maximum allowed size of ${this.maxDataSize} bytes`);
    }
    
    // Encrypt the data
    const { encrypted, authTag } = this.encryptData(
      dataBuffer,
      session.encryptionKey,
      session.iv
    );
    
    // Update session status
    session.status = 'DATA_READY';
    session.dataHash = crypto
      .createHash('sha256')
      .update(dataBuffer)
      .digest('hex');
    
    this.emit('data:ready', {
      sessionId,
      dataHash: session.dataHash
    });
    
    return {
      sessionId,
      status: session.status,
      dataHash: session.dataHash,
      encrypted,
      authTag
    };
  }

  /**
   * Receive and process data from the pipeline
   * @param {String} encryptedData - The encrypted data (base64)
   * @param {String} authTag - The authentication tag (base64)
   * @param {String} sessionId - The transfer session ID
   * @returns {Object} - The received data and metadata
   */
  async receiveData(encryptedData, authTag, sessionId) {
    const session = this.activeTransfers.get(sessionId);
    
    if (!session) {
      throw new Error(`Session ${sessionId} not found or expired`);
    }
    
    if (session.status !== 'DATA_READY') {
      throw new Error(`Session ${sessionId} is in invalid state: ${session.status}`);
    }
    
    try {
      // Decrypt the data
      const decrypted = this.decryptData(
        encryptedData,
        authTag,
        session.encryptionKey,
        session.iv
      );
      
      // Verify data integrity
      const dataHash = crypto
        .createHash('sha256')
        .update(decrypted)
        .digest('hex');
      
      if (dataHash !== session.dataHash) {
        throw new Error('Data integrity check failed');
      }
      
      // Update session status
      session.status = 'COMPLETED';
      
      this.emit('data:received', {
        sessionId,
        dataHash
      });
      
      // Parse the data if it's JSON
      let parsedData;
      try {
        parsedData = JSON.parse(decrypted.toString());
      } catch (e) {
        parsedData = decrypted.toString();
      }
      
      return {
        data: parsedData,
        metadata: session.metadata,
        sessionId,
        dataHash
      };
    } catch (error) {
      session.status = 'ERROR';
      session.error = error.message;
      
      this.emit('data:error', {
        sessionId,
        error: error.message
      });
      
      throw error;
    } finally {
      // Clean up the session after processing
      setTimeout(() => {
        this.activeTransfers.delete(sessionId);
      }, 5000);
    }
  }

  /**
   * Get the status of a transfer session
   * @param {String} sessionId - The transfer session ID
   * @returns {Object} - The session status
   */
  getSessionStatus(sessionId) {
    const session = this.activeTransfers.get(sessionId);
    
    if (!session) {
      return {
        found: false,
        error: 'Session not found or expired'
      };
    }
    
    return {
      found: true,
      status: session.status,
      created: session.created,
      dataHash: session.dataHash,
      error: session.error
    };
  }

  /**
   * Close a transfer session
   * @param {String} sessionId - The transfer session ID
   * @returns {Boolean} - Whether the session was successfully closed
   */
  closeSession(sessionId) {
    const session = this.activeTransfers.get(sessionId);
    
    if (!session) {
      return false;
    }
    
    this.activeTransfers.delete(sessionId);
    this.emit('session:closed', { sessionId });
    
    return true;
  }
}

module.exports = DataPipeline;
