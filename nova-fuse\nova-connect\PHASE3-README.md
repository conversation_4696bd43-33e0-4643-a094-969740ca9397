# NovaConnect Universal API Connector - Phase 3: Testing & Management

NovaConnect is a powerful Universal API Connector that allows you to connect, test, and monitor APIs from various sources. This document describes the implementation of Phase 3: Testing & Management for NovaConnect.

## Latest Updates: Workflow Automation, Data Export/Import, Advanced Reporting, Custom Branding, Single Sign-On (SSO), Advanced Governance Controls, Team Collaboration, Multi-Environment Support, Advanced Analytics, Enhanced Security & GraphQL Support

NovaConnect now includes workflow automation, data export/import, advanced reporting, custom branding, single sign-on (SSO), advanced governance controls, team collaboration, multi-environment support, comprehensive analytics, security features, and GraphQL support:

### Workflow Automation

- **Visual Workflow Builder**: Create workflows with a drag-and-drop interface
- **Multiple Trigger Types**: Trigger workflows manually, on schedule, by events, or via API
- **Conditional Logic**: Add conditions and branching to workflows
- **Action Library**: Use pre-built actions for common tasks
- **Custom Actions**: Create custom actions for specific needs
- **Workflow Monitoring**: Track workflow execution and troubleshoot issues

### Data Export/Import

- **Configuration Export**: Export configuration data for connectors, credentials, environments, teams, and policies
- **Selective Export**: Choose which types of data to export
- **Import Validation**: Validate imported data before applying changes
- **Import Preview**: Preview changes before applying them
- **Import Conflict Resolution**: Resolve conflicts between imported data and existing data
- **Backup & Restore**: Use export/import for backup and restore operations

### Advanced Reporting

- **Report Templates**: Create and manage report templates for different data sources
- **Custom Reports**: Generate custom reports with flexible parameters
- **Scheduled Reports**: Schedule reports to run automatically at specified intervals
- **Export Options**: Export reports in various formats (JSON, CSV, PDF, Excel)
- **Report Sharing**: Share reports with team members or external stakeholders
- **Interactive Dashboards**: Create interactive dashboards with multiple reports

### Custom Branding

- **Theme Management**: Create and manage custom themes with colors, typography, and spacing
- **Organization Branding**: Customize logos, favicons, and login backgrounds
- **White Labeling**: Remove NovaConnect branding and use custom domains
- **Custom CSS & JavaScript**: Add custom CSS and JavaScript for complete customization
- **Custom Email Branding**: Customize email templates and sender information
- **Multi-Tenant Branding**: Different branding for different organizations

### Single Sign-On (SSO)

- **SAML 2.0 Support**: Integration with SAML-based identity providers
- **OpenID Connect Support**: Integration with OIDC-based identity providers
- **Multiple Identity Providers**: Configure multiple identity providers
- **Domain-Based Auto-Discovery**: Automatically select the right identity provider based on email domain
- **Just-in-Time User Provisioning**: Automatically create user accounts on first login
- **Attribute Mapping**: Map identity provider attributes to user properties

### Advanced Governance Controls

- **Change Request Workflow**: Formal approval process for changes to resources
- **Compliance Policies**: Define and enforce compliance policies for resources
- **Resource Locking**: Prevent accidental changes to critical resources
- **Policy Violation Tracking**: Monitor and manage policy violations
- **Approval Workflows**: Multi-level approval for sensitive changes
- **Governance Dashboard**: Visibility into compliance status and pending approvals

### Team Collaboration

- **Team Management**: Create and manage teams for collaborative work
- **Team Roles**: Assign owner, admin, and member roles within teams
- **Team Invitations**: Invite users to join teams via email
- **Role-Based Access Control**: Granular permissions based on team roles
- **Audit Logging**: Comprehensive audit logs for all team actions
- **Team Resources**: Share resources within teams for better collaboration

### Multi-Environment Support

- **Environment Management**: Create and manage multiple environments (dev, test, prod)
- **Environment-Specific Configuration**: Maintain separate configurations for each environment
- **Environment Promotion**: Easily promote configuration from one environment to another
- **Configuration Comparison**: Compare configuration between environments
- **Environment Switching**: Quickly switch between environments in the UI
- **Visual Indicators**: Color-coded environment indicators for clarity

### Advanced Analytics

- **API Usage Tracking**: Monitor API usage patterns and trends
- **Performance Metrics**: Track response times and identify bottlenecks
- **Error Tracking**: Monitor and analyze API errors
- **Interactive Dashboard**: Visualize analytics data with charts and tables
- **Endpoint Analysis**: Identify most used and slowest endpoints
- **Usage Patterns**: Analyze usage by time of day and date

### Security Enhancements

- **JWT Authentication**: Secure user authentication with JSON Web Tokens
- **Role-Based Access Control**: Granular permissions based on user roles
- **API Key Management**: Create and manage API keys for programmatic access
- **Rate Limiting**: Prevent abuse with configurable rate limits
- **Secure Password Storage**: Passwords are hashed using bcrypt

### GraphQL Support

- Connect to any GraphQL endpoint
- Explore GraphQL schemas through introspection
- Build and execute GraphQL queries with a visual query builder
- View and analyze GraphQL responses
- Configure authentication and headers for GraphQL APIs
- Create and manage GraphQL subscriptions for real-time data
- Monitor subscription messages and errors
- Support for multiple subscription protocols (Apollo and GraphQL over WebSocket)

## Project Structure

The project is divided into two main parts:

### Frontend (UI)

The frontend is built with Next.js and Material-UI, providing a modern and responsive user interface for managing API connectors.

```
/ui
  /components
    /testing         # Components for testing API connectors
    /management      # Components for managing connectors
    /monitoring      # Components for monitoring connector health
    /graphql         # Components for GraphQL support
  /layouts           # Layout components
  /pages             # Next.js pages
  /services          # API services
  /styles            # CSS styles
```

### Backend (API)

The backend is built with Express.js, providing a RESTful API for the frontend to interact with.

```
/api
  /controllers       # API controllers
  /middleware        # Express middleware
  /routes            # API routes
  /services          # Business logic
    /GraphQLService.js  # GraphQL operations
  /utils             # Utility functions
  /data              # Data storage (JSON files)
  app.js             # Main application file
```

## Features

### Testing & Validation

- **Request Simulator**: Test API endpoints with custom parameters and headers
- **Response Viewer**: View API responses with syntax highlighting
- **Request History**: View and reuse previous requests
- **Validation Rules**: Define and test validation rules against API responses
- **Error Scenario Testing**: Simulate various error scenarios to test error handling
- **GraphQL Query Builder**: Build and execute GraphQL queries with schema support
- **GraphQL Schema Explorer**: Explore GraphQL schemas through introspection
- **GraphQL Subscription Manager**: Create and manage real-time GraphQL subscriptions

### Connector Management

- **Connector List**: View, filter, and sort all connectors
- **Connector Details**: View detailed information about a connector
- **Endpoint Management**: View and manage connector endpoints
- **Version History**: Track changes to connectors over time
- **Configuration**: Configure connector settings
- **GraphQL Configuration**: Configure GraphQL-specific settings and authentication

### Monitoring & Alerting

- **Health Dashboard**: Monitor the health of all connectors
- **Health Charts**: View health metrics over time
- **Alert Management**: View and manage alerts
- **Alert Configuration**: Configure alert rules and notification settings

## Getting Started

### Prerequisites

- Node.js 14.x or higher
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies for the frontend:
   ```
   cd ui
   npm install
   ```
3. Install dependencies for the backend:
   ```
   cd api
   npm install
   ```

### Running the Application

1. Start the backend server:
   ```
   cd api
   npm run dev
   ```
2. Start the frontend development server:
   ```
   cd ui
   npm run dev
   ```
3. Open your browser and navigate to `http://localhost:3000`

## API Documentation

### Connector API

- `GET /api/connectors`: Get all connectors
- `GET /api/connectors/:id`: Get connector by ID
- `POST /api/connectors`: Create a new connector
- `PUT /api/connectors/:id`: Update a connector
- `DELETE /api/connectors/:id`: Delete a connector
- `POST /api/connectors/:id/duplicate`: Duplicate a connector
- `GET /api/connectors/:id/versions`: Get connector versions

### Testing API

- `POST /api/testing/execute`: Execute a connector endpoint
- `GET /api/testing/history/:connectorId/:endpointId`: Get request history
- `POST /api/testing/validate`: Validate response against rules
- `POST /api/testing/simulate-error`: Simulate error scenarios

### GraphQL API

- `POST /api/graphql/execute`: Execute a GraphQL query
- `POST /api/graphql/schema`: Fetch GraphQL schema through introspection
- `POST /api/graphql/validate`: Validate a GraphQL query
- `POST /api/graphql/sample`: Generate a sample GraphQL query

### GraphQL Subscription API

- `POST /api/graphql/subscriptions`: Create a new GraphQL subscription
- `GET /api/graphql/subscriptions`: Get all active subscriptions
- `GET /api/graphql/subscriptions/:id`: Get subscription by ID
- `DELETE /api/graphql/subscriptions/:id`: Cancel a subscription
- `GET /api/graphql/subscriptions/:id/messages`: Get subscription messages
- `GET /api/graphql/subscriptions/:id/errors`: Get subscription errors

### Authentication API

- `POST /api/auth/register`: Register a new user
- `POST /api/auth/login`: Login a user
- `POST /api/auth/logout`: Logout a user
- `GET /api/auth/me`: Get current user
- `GET /api/auth/users`: Get all users (admin only)
- `GET /api/auth/users/:id`: Get user by ID
- `PUT /api/auth/users/:id`: Update user
- `DELETE /api/auth/users/:id`: Delete user

### API Key API

- `POST /api/auth/api-keys`: Create a new API key
- `GET /api/auth/api-keys`: Get API keys for current user
- `DELETE /api/auth/api-keys/:id`: Delete API key
- `GET /api/api-keys`: Get all API keys (admin only)
- `GET /api/api-keys/:id`: Get API key by ID
- `PUT /api/api-keys/:id`: Update API key
- `DELETE /api/api-keys/user/:userId`: Revoke all API keys for a user (admin only)

### Analytics API

- `GET /api/analytics/dashboard`: Get dashboard analytics
- `GET /api/analytics/usage`: Get usage analytics
- `GET /api/analytics/performance`: Get performance analytics
- `GET /api/analytics/errors`: Get error analytics

### Environment API

- `GET /api/environments`: Get all environments
- `GET /api/environments/default`: Get default environment
- `POST /api/environments`: Create a new environment
- `GET /api/environments/:id`: Get environment by ID
- `PUT /api/environments/:id`: Update an environment
- `DELETE /api/environments/:id`: Delete an environment
- `POST /api/environments/:id/default`: Set default environment
- `POST /api/environments/promote`: Promote environment
- `GET /api/environments/:id/config`: Get environment configuration
- `POST /api/environments/compare`: Compare environment configurations
- `GET /api/environments/:environmentId/connectors`: Get all connectors for an environment
- `POST /api/environments/:environmentId/connectors`: Create a new connector for an environment
- `GET /api/environments/:environmentId/connectors/:connectorId`: Get connector by ID for an environment
- `PUT /api/environments/:environmentId/connectors/:connectorId`: Update a connector for an environment
- `DELETE /api/environments/:environmentId/connectors/:connectorId`: Delete a connector for an environment
- `POST /api/environments/connectors/clone`: Clone a connector between environments
- `POST /api/environments/connectors/promote`: Promote all connectors from one environment to another

### Team API

- `GET /api/teams`: Get all teams
- `GET /api/teams/my`: Get teams for current user
- `POST /api/teams`: Create a new team
- `GET /api/teams/:id`: Get team by ID
- `PUT /api/teams/:id`: Update a team
- `DELETE /api/teams/:id`: Delete a team
- `GET /api/teams/:id/members`: Get team members
- `POST /api/teams/:id/members`: Add team member
- `PUT /api/teams/:id/members/:memberId`: Update team member
- `DELETE /api/teams/:id/members/:memberId`: Remove team member
- `GET /api/teams/:id/invitations`: Get team invitations
- `POST /api/teams/:id/invitations`: Create team invitation
- `DELETE /api/teams/:id/invitations/:invitationId`: Cancel team invitation
- `GET /api/teams/invitations/my`: Get my invitations
- `POST /api/teams/invitations/:id/accept`: Accept team invitation
- `POST /api/teams/invitations/:id/decline`: Decline team invitation

### Role Permission API

- `GET /api/roles/roles`: Get all roles
- `GET /api/roles/roles/:id`: Get role by ID
- `POST /api/roles/roles`: Create a new role
- `PUT /api/roles/roles/:id`: Update a role
- `DELETE /api/roles/roles/:id`: Delete a role
- `GET /api/roles/permissions`: Get all permissions
- `GET /api/roles/roles/:id/permissions`: Get permissions for a role
- `POST /api/roles/roles/:id/permissions`: Add permission to role
- `DELETE /api/roles/roles/:id/permissions/:permissionId`: Remove permission from role
- `GET /api/roles/users/:id/roles`: Get roles for a user
- `GET /api/roles/users/me/roles`: Get my roles
- `POST /api/roles/users/:id/roles`: Assign role to user
- `DELETE /api/roles/users/:id/roles/:roleId`: Remove role from user
- `GET /api/roles/users/:id/permissions`: Get permissions for a user
- `GET /api/roles/users/me/permissions`: Get my permissions

### Audit API

- `GET /api/audit`: Get audit logs
- `GET /api/audit/:id`: Get audit log by ID
- `GET /api/audit/resource/:resourceType/:resourceId`: Get audit logs for a resource
- `GET /api/audit/user/:id`: Get audit logs for a user
- `GET /api/audit/user/me`: Get my audit logs
- `GET /api/audit/team/:id`: Get audit logs for a team

### Change Request API

- `GET /api/change-requests`: Get all change requests
- `GET /api/change-requests/my`: Get change requests for current user
- `GET /api/change-requests/approval`: Get change requests for approval
- `POST /api/change-requests`: Create a new change request
- `GET /api/change-requests/:id`: Get change request by ID
- `PUT /api/change-requests/:id`: Update a change request
- `DELETE /api/change-requests/:id`: Delete a change request
- `GET /api/change-requests/:id/comments`: Get change request comments
- `POST /api/change-requests/:id/comments`: Add comment to change request
- `PUT /api/change-requests/:id/comments/:commentId`: Update comment
- `DELETE /api/change-requests/:id/comments/:commentId`: Delete comment
- `GET /api/change-requests/:id/approvals`: Get change request approvals
- `POST /api/change-requests/:id/approve`: Approve change request
- `POST /api/change-requests/:id/reject`: Reject change request
- `POST /api/change-requests/:id/implement`: Implement change request

### Policy API

- `GET /api/policies`: Get all policies
- `GET /api/policies/team/:id`: Get policies for a team
- `POST /api/policies`: Create a new policy
- `GET /api/policies/:id`: Get policy by ID
- `PUT /api/policies/:id`: Update a policy
- `DELETE /api/policies/:id`: Delete a policy
- `POST /api/policies/evaluate/:resourceType`: Evaluate a resource against policies
- `GET /api/policies/violations`: Get policy violations
- `GET /api/policies/violations/:id`: Get policy violation by ID
- `PUT /api/policies/violations/:id`: Update policy violation
- `POST /api/policies/:policyId/violations`: Record a policy violation

### Resource Lock API

- `GET /api/resource-locks`: Get all resource locks
- `GET /api/resource-locks/team/:id`: Get resource locks for a team
- `GET /api/resource-locks/check/:resourceType/:resourceId`: Check if a resource is locked
- `GET /api/resource-locks/can-modify/:resourceType/:resourceId`: Check if a user can modify a resource
- `POST /api/resource-locks/cleanup`: Clean up expired locks
- `POST /api/resource-locks`: Create a new resource lock
- `GET /api/resource-locks/:id`: Get resource lock by ID
- `PUT /api/resource-locks/:id`: Update a resource lock
- `DELETE /api/resource-locks/:id`: Delete a resource lock (unlock)
- `GET /api/resource-locks/:id/overrides`: Get lock overrides for a lock
- `POST /api/resource-locks/:id/overrides`: Create a lock override
- `DELETE /api/resource-locks/:id/overrides/:overrideId`: Delete a lock override

### Identity Provider API

- `GET /api/identity-providers`: Get all identity providers
- `POST /api/identity-providers`: Create a new identity provider
- `GET /api/identity-providers/:id`: Get identity provider by ID
- `PUT /api/identity-providers/:id`: Update an identity provider
- `DELETE /api/identity-providers/:id`: Delete an identity provider
- `POST /api/identity-providers/:id/test`: Test an identity provider connection
- `GET /api/identity-providers/:id/saml/metadata`: Generate SAML metadata
- `GET /api/identity-providers/:id/oidc/configuration`: Get OIDC configuration

### SSO Authentication API

- `GET /api/sso/provider/domain/:domain`: Get provider by domain
- `GET /api/sso/initiate/:providerId`: Initiate SSO authentication
- `POST /api/sso/callback/saml`: Process SAML callback
- `GET /api/sso/callback/oidc`: Process OIDC callback
- `POST /api/sso/logout/:providerId`: Initiate SSO logout
- `POST /api/sso/logout/callback/saml`: Process SAML logout callback
- `POST /api/sso/refresh`: Refresh OIDC tokens
- `POST /api/sso/cleanup`: Clean up expired data

### Theme API

- `GET /api/themes`: Get all themes
- `POST /api/themes`: Create a new theme
- `GET /api/themes/:id`: Get theme by ID
- `PUT /api/themes/:id`: Update a theme
- `DELETE /api/themes/:id`: Delete a theme
- `POST /api/themes/:id/clone`: Clone a theme
- `GET /api/themes/organization/:organizationId`: Get organization theme
- `POST /api/themes/organization/:organizationId`: Set organization theme
- `DELETE /api/themes/organization/:organizationId`: Reset organization theme to default
- `GET /api/themes/:id/css`: Get theme CSS
- `GET /api/themes/organization/:organizationId/css`: Get organization theme CSS

### Branding API

- `GET /api/branding/organization/:organizationId`: Get organization branding
- `PUT /api/branding/organization/:organizationId`: Update organization branding
- `DELETE /api/branding/organization/:organizationId`: Reset organization branding to default
- `POST /api/branding/organization/:organizationId/assets`: Upload branding asset
- `GET /api/branding/assets/:fileName`: Get branding asset
- `DELETE /api/branding/organization/:organizationId/assets/:fileName`: Delete branding asset
- `GET /api/branding/package/:organizationId`: Get complete branding package

### White Label API

- `GET /api/white-label/organization/:organizationId`: Get white label settings
- `PUT /api/white-label/organization/:organizationId`: Update white label settings
- `GET /api/white-label/organization/:organizationId/domains`: Get custom domains
- `POST /api/white-label/organization/:organizationId/domains`: Add custom domain
- `POST /api/white-label/organization/:organizationId/domains/:domain/verify`: Verify custom domain
- `DELETE /api/white-label/organization/:organizationId/domains/:domain`: Delete custom domain
- `GET /api/white-label/organization/:organizationId/domains/:domain/instructions`: Get domain verification instructions
- `GET /api/white-label/package/:domain`: Get white label package by domain

### Report API

- `GET /api/reports/templates`: Get all report templates
- `GET /api/reports/templates/type/:type`: Get report templates by type
- `GET /api/reports/templates/:id`: Get report template by ID
- `POST /api/reports/templates`: Create a new report template
- `PUT /api/reports/templates/:id`: Update a report template
- `DELETE /api/reports/templates/:id`: Delete a report template
- `POST /api/reports/templates/:id/clone`: Clone a report template
- `POST /api/reports/generate/:templateId`: Generate a report
- `GET /api/reports`: Get all reports
- `GET /api/reports/my`: Get my reports
- `GET /api/reports/:id`: Get report by ID
- `DELETE /api/reports/:id`: Delete a report
- `GET /api/reports/:id/export/:format`: Export a report
- `GET /api/reports/scheduled`: Get all scheduled reports
- `GET /api/reports/scheduled/my`: Get my scheduled reports
- `GET /api/reports/scheduled/:id`: Get scheduled report by ID
- `POST /api/reports/scheduled`: Create a scheduled report
- `PUT /api/reports/scheduled/:id`: Update a scheduled report
- `DELETE /api/reports/scheduled/:id`: Delete a scheduled report
- `POST /api/reports/scheduled/run`: Run scheduled reports

### Export/Import API

- `GET /api/export-import/exports`: Get all exports
- `GET /api/export-import/exports/my`: Get my exports
- `GET /api/export-import/exports/:id`: Get export by ID
- `POST /api/export-import/exports`: Create a new export
- `DELETE /api/export-import/exports/:id`: Delete an export
- `GET /api/export-import/exports/:id/download`: Download export data
- `GET /api/export-import/imports`: Get all imports
- `GET /api/export-import/imports/my`: Get my imports
- `GET /api/export-import/imports/:id`: Get import by ID
- `POST /api/export-import/imports`: Create a new import
- `POST /api/export-import/imports/:id/process`: Process an import
- `DELETE /api/export-import/imports/:id`: Delete an import

### Workflow API

- `GET /api/workflows`: Get all workflows
- `GET /api/workflows/my`: Get my workflows
- `GET /api/workflows/:id`: Get workflow by ID
- `POST /api/workflows`: Create a new workflow
- `PUT /api/workflows/:id`: Update a workflow
- `DELETE /api/workflows/:id`: Delete a workflow
- `POST /api/workflows/:id/enable`: Enable a workflow
- `POST /api/workflows/:id/disable`: Disable a workflow
- `POST /api/workflows/:id/execute`: Execute a workflow
- `GET /api/workflows/runs`: Get all workflow runs
- `GET /api/workflows/:id/runs`: Get workflow runs for a workflow
- `GET /api/workflows/runs/:id`: Get workflow run by ID
- `POST /api/workflows/scheduled/process`: Process scheduled workflows
- `POST /api/workflows/events/:eventType`: Trigger event workflows

### Monitoring API

- `GET /api/monitoring/health`: Get health status of all connectors
- `GET /api/monitoring/health/:id`: Get health status of a specific connector
- `POST /api/monitoring/health/:id/check`: Run health check for a connector
- `GET /api/monitoring/alerts`: Get all alerts
- `GET /api/monitoring/alerts/:id`: Get alert by ID
- `PUT /api/monitoring/alerts/:id/acknowledge`: Acknowledge alert
- `PUT /api/monitoring/alerts/:id/resolve`: Resolve alert
- `POST /api/monitoring/alerts/:id/comments`: Add comment to alert
- `GET /api/monitoring/config/alerts`: Get alert configuration
- `PUT /api/monitoring/config/alerts`: Update alert configuration

### Credential API

- `GET /api/credentials`: Get all credentials
- `GET /api/credentials/:id`: Get credential by ID
- `POST /api/credentials`: Create a new credential
- `PUT /api/credentials/:id`: Update a credential
- `DELETE /api/credentials/:id`: Delete a credential

## Next Steps

To complete the implementation of NovaConnect, the following steps are recommended:

1. **Implement Authentication**: Add user authentication and authorization
2. **Add Data Persistence**: Replace JSON file storage with a database
3. **Implement Scheduled Health Checks**: Add a job scheduler for regular health checks
4. **Add Notification Services**: Implement email, Slack, and webhook notifications
5. **Enhance Error Handling**: Add more robust error handling and logging
6. **Add Unit and Integration Tests**: Increase test coverage
7. **Implement CI/CD Pipeline**: Set up continuous integration and deployment

## License

This project is licensed under the MIT License - see the LICENSE file for details.
