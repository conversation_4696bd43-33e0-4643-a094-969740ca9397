/**
 * TabPanel Component
 * 
 * A reusable tab panel component for organizing content into tabs.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';

/**
 * TabPanel component
 * 
 * @param {Object} props - Component props
 * @param {Array} props.tabs - Tab configuration
 * @param {string} props.tabs[].id - Tab ID
 * @param {string} props.tabs[].label - Tab label
 * @param {React.ReactNode} props.tabs[].content - Tab content
 * @param {string} [props.tabs[].icon] - Tab icon
 * @param {string} [props.tabs[].badge] - Tab badge text
 * @param {string} [props.tabs[].badgeColor] - Tab badge color
 * @param {string} [props.defaultTab] - Default active tab ID
 * @param {string} [props.variant='default'] - Tab variant (default, pills, underline, bordered)
 * @param {string} [props.orientation='horizontal'] - Tab orientation (horizontal, vertical)
 * @param {Function} [props.onTabChange] - Function to call when tab changes
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} TabPanel component
 */
const TabPanel = ({
  tabs,
  defaultTab,
  variant = 'default',
  orientation = 'horizontal',
  onTabChange,
  className = '',
  style = {}
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab || (tabs.length > 0 ? tabs[0].id : ''));
  
  // Handle tab click
  const handleTabClick = (tabId) => {
    setActiveTab(tabId);
    if (onTabChange) {
      onTabChange(tabId);
    }
  };
  
  // Get tab list class based on variant and orientation
  const getTabListClass = () => {
    const baseClass = 'flex';
    
    // Orientation class
    const orientationClass = orientation === 'vertical' ? 'flex-col' : 'flex-row';
    
    // Variant-specific classes
    const variantClasses = {
      default: 'border-b border-gray-200',
      pills: 'space-x-1',
      underline: 'border-b border-gray-200',
      bordered: 'border-b border-gray-200'
    };
    
    return `${baseClass} ${orientationClass} ${variantClasses[variant] || variantClasses.default}`;
  };
  
  // Get tab class based on variant, orientation, and active state
  const getTabClass = (tabId) => {
    const isActive = activeTab === tabId;
    const baseClass = 'flex items-center px-4 py-2 text-sm font-medium cursor-pointer transition-colors duration-200';
    
    // Orientation-specific classes
    const orientationClass = orientation === 'vertical' ? 'justify-start' : '';
    
    // Variant and active state classes
    const variantActiveClasses = {
      default: isActive
        ? 'text-blue-600 border-b-2 border-blue-600 -mb-px'
        : 'text-gray-500 hover:text-gray-700 hover:border-gray-300',
      pills: isActive
        ? 'bg-blue-600 text-white rounded-md'
        : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md',
      underline: isActive
        ? 'text-blue-600 border-b-2 border-blue-600 -mb-px'
        : 'text-gray-500 hover:text-gray-700 hover:border-gray-300',
      bordered: isActive
        ? 'text-blue-600 bg-white border-t border-l border-r border-gray-200 rounded-t-md -mb-px'
        : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50 border border-transparent'
    };
    
    return `${baseClass} ${orientationClass} ${variantActiveClasses[variant] || variantActiveClasses.default}`;
  };
  
  // Get tab content container class based on orientation
  const getTabContentContainerClass = () => {
    return orientation === 'vertical'
      ? 'flex-1 ml-4'
      : 'mt-4';
  };
  
  // Render tab badge
  const renderTabBadge = (badge, badgeColor) => {
    if (!badge) return null;
    
    const badgeColorClass = badgeColor || 'bg-gray-200 text-gray-800';
    
    return (
      <span className={`ml-2 px-2 py-0.5 text-xs font-medium rounded-full ${badgeColorClass}`}>
        {badge}
      </span>
    );
  };
  
  // Get active tab content
  const getActiveTabContent = () => {
    const activeTabData = tabs.find(tab => tab.id === activeTab);
    return activeTabData ? activeTabData.content : null;
  };
  
  return (
    <div
      className={`${orientation === 'vertical' ? 'flex' : ''} ${className}`}
      style={style}
      data-testid="tab-panel"
    >
      <div className={getTabListClass()} role="tablist" data-testid="tab-list">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={getTabClass(tab.id)}
            role="tab"
            aria-selected={activeTab === tab.id}
            aria-controls={`tab-panel-${tab.id}`}
            id={`tab-${tab.id}`}
            onClick={() => handleTabClick(tab.id)}
            data-testid={`tab-${tab.id}`}
          >
            {tab.icon && <span className="mr-2">{tab.icon}</span>}
            {tab.label}
            {renderTabBadge(tab.badge, tab.badgeColor)}
          </button>
        ))}
      </div>
      
      <div
        className={getTabContentContainerClass()}
        role="tabpanel"
        aria-labelledby={`tab-${activeTab}`}
        id={`tab-panel-${activeTab}`}
        data-testid="tab-content"
      >
        {getActiveTabContent()}
      </div>
    </div>
  );
};

TabPanel.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      content: PropTypes.node.isRequired,
      icon: PropTypes.node,
      badge: PropTypes.string,
      badgeColor: PropTypes.string
    })
  ).isRequired,
  defaultTab: PropTypes.string,
  variant: PropTypes.oneOf(['default', 'pills', 'underline', 'bordered']),
  orientation: PropTypes.oneOf(['horizontal', 'vertical']),
  onTabChange: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default TabPanel;
