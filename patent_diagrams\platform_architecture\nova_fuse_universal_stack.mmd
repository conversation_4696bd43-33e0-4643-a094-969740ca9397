# NovaFuse Universal Stack
# File: nova_fuse_universal_stack.mmd
# Description: Illustrates the layered architecture of the NovaFuse platform
# Created: 2025-07-06

%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#fff', 'primaryTextColor': '#000', 'primaryBorderColor': '#000', 'lineColor': '#000', 'secondaryColor': '#fff', 'tertiaryColor': '#fff'}}}%%

graph TD
    subgraph "NovaFuse Universal Stack"
        A["External Systems & Users"] --> B(Interface Layer: NovaAlign Studio)
        B --> C(Application Layer: Nova Modules & CSEs)
        C --> D(Governance Layer: Cadence C-AIaaS)
        D --> E(Foundation Layer: Comphyology Core)
        E -->|Feedback| C
        E -->|Governance| D
    end

    %% Styling for USPTO compliance (black and white)
    classDef default fill:#fff,stroke:#000,stroke-width:1px,color:#000
    classDef layer fill:#fff,stroke:#000,stroke-width:2px,stroke-dasharray: 5 5,color:#000
    classDef external fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:box3d
    
    class A external
    class B,C,D,E layer
