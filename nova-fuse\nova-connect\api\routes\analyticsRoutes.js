/**
 * Analytics Routes
 */

const express = require('express');
const router = express.Router();
const AnalyticsController = require('../controllers/AnalyticsController');
const { authenticate, hasRole } = require('../middleware/authMiddleware');

// All routes require authentication and admin role
router.use(authenticate);
router.use(hasRole('admin'));

// Get dashboard analytics
router.get('/dashboard', (req, res, next) => {
  AnalyticsController.getDashboardAnalytics(req, res, next);
});

// Get usage analytics
router.get('/usage', (req, res, next) => {
  AnalyticsController.getUsageAnalytics(req, res, next);
});

// Get performance analytics
router.get('/performance', (req, res, next) => {
  AnalyticsController.getPerformanceAnalytics(req, res, next);
});

// Get error analytics
router.get('/errors', (req, res, next) => {
  AnalyticsController.getErrorAnalytics(req, res, next);
});

// Report Template Routes

// Get all report templates
router.get('/report-templates', (req, res, next) => {
  AnalyticsController.getReportTemplates(req, res, next);
});

// Get report template by ID
router.get('/report-templates/:id', (req, res, next) => {
  AnalyticsController.getReportTemplateById(req, res, next);
});

// Create report template
router.post('/report-templates', (req, res, next) => {
  AnalyticsController.createReportTemplate(req, res, next);
});

// Update report template
router.put('/report-templates/:id', (req, res, next) => {
  AnalyticsController.updateReportTemplate(req, res, next);
});

// Delete report template
router.delete('/report-templates/:id', (req, res, next) => {
  AnalyticsController.deleteReportTemplate(req, res, next);
});

// Report Routes

// Get all reports
router.get('/reports', (req, res, next) => {
  AnalyticsController.getReports(req, res, next);
});

// Get report by ID
router.get('/reports/:id', (req, res, next) => {
  AnalyticsController.getReportById(req, res, next);
});

// Generate report from template
router.post('/reports/generate/:templateId', (req, res, next) => {
  AnalyticsController.generateReport(req, res, next);
});

// Delete report
router.delete('/reports/:id', (req, res, next) => {
  AnalyticsController.deleteReport(req, res, next);
});

// Schedule report
router.post('/report-templates/:templateId/schedule', (req, res, next) => {
  AnalyticsController.scheduleReport(req, res, next);
});

module.exports = router;
