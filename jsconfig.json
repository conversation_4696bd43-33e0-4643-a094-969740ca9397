{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"]
    },
    "jsx": "react",
    "module": "esnext",
    "target": "es2020",
    "moduleResolution": "node"
  },
  "exclude": [
    // Standard exclusions
    "node_modules",
    ".next",
    "out",

    // Large directories
    ".venv",
    "testing-environment",
    "marketplace-ui",
    "src",
    "patent-diagrams-new",
    "connector-templates",
    "nova-fuse",
    "nova-marketplace",
    "nova-connect",

    // Test and result directories
    "coverage",
    "test-reports",
    "*_results",
    "lbl-tcp-3",
    "uac-demo",
    "gcp-simulation",

    // Additional directories to exclude
    "public",
    "docs",
    ".nyc_output",
    "cypress",
    "tests",
    "test",
    "__mocks__",

    // Comphyology and related directories (if not actively working on them)
    "comphyology*",
    "comphyon*",
    "cognitive-metrology",

    // Demo and example directories
    "*demo*",
    "examples",

    // Patent and documentation directories
    "patent*",

    // Backup and temporary directories
    "site-backup",
    "temp*"
  ]
}
