
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse Partner SDK Test Results</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f8f9fa;
      color: #212529;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #0a84ff;
      color: white;
      padding: 20px;
      text-align: center;
      margin-bottom: 20px;
      border-radius: 5px;
    }
    .card {
      background-color: white;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      overflow: hidden;
    }
    .card-header {
      background-color: #0a84ff;
      color: white;
      padding: 15px 20px;
      font-weight: bold;
      font-size: 18px;
    }
    .card-body {
      padding: 20px;
    }
    .chart-container {
      height: 300px;
      margin-bottom: 20px;
    }
    .metrics {
      display: flex;
      flex-wrap: wrap;
      margin: 0 -10px;
    }
    .metric {
      flex: 1;
      min-width: 200px;
      margin: 10px;
      background-color: white;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 20px;
      text-align: center;
    }
    .metric-value {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
      color: #0a84ff;
    }
    .metric-label {
      font-size: 14px;
      color: #6c757d;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #e9ecef;
    }
    th {
      background-color: #f8f9fa;
      font-weight: bold;
    }
    tr:hover {
      background-color: #f8f9fa;
    }
    .footer {
      text-align: center;
      margin-top: 40px;
      padding: 20px;
      color: #6c757d;
      font-size: 14px;
    }
    .logo {
      text-align: center;
      margin-bottom: 20px;
    }
    .logo img {
      height: 60px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">
        <h1>NovaFuse</h1>
      </div>
      <h1>Partner SDK Test Results</h1>
      <p>Test Partner: Test Partner, Inc. (TEST-PARTNER-001)</p>
    </div>
    
    <div class="card">
      <div class="card-header">Revenue Summary</div>
      <div class="card-body">
        <div class="metrics">
          <div class="metric">
            <div class="metric-label">Total Revenue</div>
            <div class="metric-value">$65.60</div>
          </div>
          <div class="metric">
            <div class="metric-label">Total Transactions</div>
            <div class="metric-value">3</div>
          </div>
          <div class="metric">
            <div class="metric-label">Average Transaction</div>
            <div class="metric-value">$21.87</div>
          </div>
          <div class="metric">
            <div class="metric-label">Revenue Share</div>
            <div class="metric-value">82%</div>
          </div>
        </div>
        
        <div class="chart-container">
          <canvas id="revenueChart"></canvas>
        </div>
      </div>
    </div>
    
    <div class="card">
      <div class="card-header">Usage Summary</div>
      <div class="card-body">
        <div class="metrics">
          <div class="metric">
            <div class="metric-label">Total Usage</div>
            <div class="metric-value">3</div>
          </div>
          
            <div class="metric">
              <div class="metric-label">Analyze Operations</div>
              <div class="metric-value">1</div>
            </div>
          
            <div class="metric">
              <div class="metric-label">Remediate Operations</div>
              <div class="metric-value">1</div>
            </div>
          
            <div class="metric">
              <div class="metric-label">Report Operations</div>
              <div class="metric-value">1</div>
            </div>
          
        </div>
        
        <div class="chart-container">
          <canvas id="usageChart"></canvas>
        </div>
      </div>
    </div>
    
    <div class="card">
      <div class="card-header">Analysis Results</div>
      <div class="card-body">
        <div class="metrics">
          <div class="metric">
            <div class="metric-label">CSDE Value</div>
            <div class="metric-value">1094991.59</div>
          </div>
          <div class="metric">
            <div class="metric-label">Performance Factor</div>
            <div class="metric-value">3142.00x</div>
          </div>
          <div class="metric">
            <div class="metric-label">ML Enhanced</div>
            <div class="metric-value">No</div>
          </div>
          <div class="metric">
            <div class="metric-label">Revenue</div>
            <div class="metric-value">$20.50</div>
          </div>
        </div>
        
        <h3>Remediation Actions</h3>
        <table>
          <thead>
            <tr>
              <th>Title</th>
              <th>Priority</th>
              <th>Type</th>
              <th>Automation</th>
            </tr>
          </thead>
          <tbody>
            
              <tr>
                <td>Remediate Account Management</td>
                <td>CRITICAL</td>
                <td>compliance</td>
                <td>high</td>
              </tr>
            
              <tr>
                <td>Remediate Least Functionality</td>
                <td>HIGH</td>
                <td>compliance</td>
                <td>high</td>
              </tr>
            
              <tr>
                <td>Optimize IAM Role Configuration</td>
                <td>MEDIUM</td>
                <td>gcp</td>
                <td>high</td>
              </tr>
            
              <tr>
                <td>Optimize VPC Network Security</td>
                <td>LOW</td>
                <td>gcp</td>
                <td>high</td>
              </tr>
            
              <tr>
                <td>Implement Self-Destructing Compliance Servers</td>
                <td>LOW</td>
                <td>cyber-safety</td>
                <td>high</td>
              </tr>
            
          </tbody>
        </table>
      </div>
    </div>
    
    <div class="card">
      <div class="card-header">Remediation Results</div>
      <div class="card-body">
        <div class="metrics">
          <div class="metric">
            <div class="metric-label">Actions Selected</div>
            <div class="metric-value">3</div>
          </div>
          <div class="metric">
            <div class="metric-label">Actions Remediated</div>
            <div class="metric-value">3</div>
          </div>
          <div class="metric">
            <div class="metric-label">Success Rate</div>
            <div class="metric-value">100.00%</div>
          </div>
          <div class="metric">
            <div class="metric-label">Revenue</div>
            <div class="metric-value">$41.00</div>
          </div>
        </div>
        
        <div class="chart-container">
          <canvas id="remediationChart"></canvas>
        </div>
      </div>
    </div>
    
    <div class="footer">
      <p>NovaFuse Partner SDK Test Report</p>
      <p>Generated on 5/4/2025, 1:26:53 PM</p>
    </div>
  </div>
  
  <script>
    // Revenue chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
      type: 'bar',
      data: {
        labels: ["analyze","remediate","report"],
        datasets: [{
          label: 'Revenue by Operation',
          data: [20.5,41,4.1],
          backgroundColor: 'rgba(10, 132, 255, 0.6)',
          borderColor: 'rgba(10, 132, 255, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Revenue ($)'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Operation'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Revenue by Operation'
          }
        }
      }
    });
    
    // Usage chart
    const usageCtx = document.getElementById('usageChart').getContext('2d');
    new Chart(usageCtx, {
      type: 'pie',
      data: {
        labels: ["analyze","remediate","report"],
        datasets: [{
          label: 'Usage by Operation',
          data: [1,1,1],
          backgroundColor: [
            'rgba(10, 132, 255, 0.6)',
            'rgba(255, 149, 0, 0.6)',
            'rgba(52, 199, 89, 0.6)'
          ],
          borderColor: [
            'rgba(10, 132, 255, 1)',
            'rgba(255, 149, 0, 1)',
            'rgba(52, 199, 89, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: 'Usage by Operation'
          }
        }
      }
    });
    
    // Remediation chart
    const remediationCtx = document.getElementById('remediationChart').getContext('2d');
    new Chart(remediationCtx, {
      type: 'bar',
      data: {
        labels: ['Selected', 'Remediated', 'Failed'],
        datasets: [{
          label: 'Remediation Actions',
          data: [
            3,
            3,
            0
          ],
          backgroundColor: [
            'rgba(10, 132, 255, 0.6)',
            'rgba(52, 199, 89, 0.6)',
            'rgba(255, 69, 58, 0.6)'
          ],
          borderColor: [
            'rgba(10, 132, 255, 1)',
            'rgba(52, 199, 89, 1)',
            'rgba(255, 69, 58, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Actions'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Status'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Remediation Actions'
          }
        }
      }
    });
  </script>
</body>
</html>
