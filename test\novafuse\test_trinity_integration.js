/**
 * NovaFuse Trinity Integration Test
 * 
 * This script tests the integration of the Comphyological Trinity and
 * <PERSON><PERSON><PERSON><PERSON> Meter-Governor with the NovaFuse platform components.
 */

const fs = require('fs');
const path = require('path');
const NovaTrinityIntegration = require('../../src/novafuse/trinity_integration');
const TrinityDashboard = require('../../src/novafuse/trinity_dashboard');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../resonance_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Test NovaFuse Trinity Integration
 */
function testNovaTrinityIntegration() {
  console.log('=== Testing NovaFuse Trinity Integration ===');
  
  // Create integration
  const integration = new NovaTrinityIntegration({
    enableNovaConnect: true,
    enableNovaCore: true,
    enableNovaVision: true,
    enableNovaPulse: true,
    enableNovaShield: true,
    logIntegration: true
  });
  
  // Test NovaConnect integration (API boundary validation)
  console.log('\nTesting NovaConnect integration (API boundary validation):');
  
  const apiRequests = [
    { method: 'GET', path: '/api/users', params: { limit: 10 } },
    { method: 'POST', path: '/api/users', body: { name: 'John Doe', email: '<EMAIL>' } },
    { method: 'PUT', path: '/api/users/123', body: { name: 'Jane Doe', email: '<EMAIL>' } },
    { method: 'DELETE', path: '/api/users/123' }
  ];
  
  const apiResults = apiRequests.map(request => {
    try {
      const result = integration.novaConnectIntegration.validateRequest(request, { domain: 'cyber' });
      console.log(`API request validated: ${request.method} ${request.path}`);
      return { request, result, valid: true };
    } catch (error) {
      console.log(`API request validation error: ${error.message}`);
      return { request, error: error.message, valid: false };
    }
  });
  
  // Test NovaCore integration (State management)
  console.log('\nTesting NovaCore integration (State management):');
  
  const stateTransitions = [
    {
      current: { status: 'pending', progress: 0.5, lastUpdated: Date.now() - 1000 },
      new: { status: 'processing', progress: 0.7, lastUpdated: Date.now() }
    },
    {
      current: { status: 'processing', progress: 0.7, lastUpdated: Date.now() - 1000 },
      new: { status: 'completed', progress: 1.0, lastUpdated: Date.now() }
    },
    {
      current: { status: 'completed', progress: 1.0, lastUpdated: Date.now() - 1000 },
      new: { status: 'archived', progress: 1.0, lastUpdated: Date.now() }
    }
  ];
  
  const stateResults = stateTransitions.map(transition => {
    try {
      const result = integration.novaCoreIntegration.validateStateTransition(
        transition.current,
        transition.new,
        { domain: 'cyber' }
      );
      console.log(`State transition validated: ${transition.current.status} -> ${transition.new.status}`);
      return { transition, result, valid: true };
    } catch (error) {
      console.log(`State transition validation error: ${error.message}`);
      return { transition, error: error.message, valid: false };
    }
  });
  
  // Test NovaVision integration (Cross-domain visualization)
  console.log('\nTesting NovaVision integration (Cross-domain visualization):');
  
  const domains = ['cyber', 'financial', 'medical'];
  const crossDomainResults = [];
  
  for (const sourceDomain of domains) {
    for (const targetDomain of domains) {
      if (sourceDomain !== targetDomain) {
        try {
          const result = integration.novaVisionIntegration.translateDomain(
            { value: 0.6, risk: 'medium' },
            sourceDomain,
            targetDomain
          );
          console.log(`Domain translated: ${sourceDomain} -> ${targetDomain}`);
          crossDomainResults.push({
            sourceDomain,
            targetDomain,
            result,
            valid: true
          });
        } catch (error) {
          console.log(`Domain translation error: ${error.message}`);
          crossDomainResults.push({
            sourceDomain,
            targetDomain,
            error: error.message,
            valid: false
          });
        }
      }
    }
  }
  
  // Test NovaShield integration (Security enforcement)
  console.log('\nTesting NovaShield integration (Security enforcement):');
  
  const securityContexts = [
    { user: 'admin', role: 'administrator', permissions: ['read', 'write', 'delete'] },
    { user: 'user', role: 'standard', permissions: ['read'] },
    { user: 'guest', role: 'guest', permissions: [] }
  ];
  
  const securityResults = securityContexts.map(context => {
    try {
      const result = integration.novaShieldIntegration.validateSecurity(context);
      console.log(`Security context validated: ${context.role}`);
      return { context, result, valid: true };
    } catch (error) {
      console.log(`Security validation error: ${error.message}`);
      return { context, error: error.message, valid: false };
    }
  });
  
  // Get metrics
  const metrics = integration.getMetrics();
  console.log('\nIntegration Metrics:', JSON.stringify(metrics.integration, null, 2));
  
  return {
    integration,
    apiResults,
    stateResults,
    crossDomainResults,
    securityResults,
    metrics
  };
}

/**
 * Test Trinity Dashboard
 */
function testTrinityDashboard(integration) {
  console.log('\n=== Testing Trinity Dashboard ===');
  
  // Create dashboard
  const dashboard = new TrinityDashboard({
    integration,
    createIntegration: false,
    refreshInterval: 500,
    logDashboard: true
  });
  
  // Register event listeners
  dashboard.on('dashboard-refresh', (data) => {
    console.log(`Dashboard refreshed: ${new Date(data.timestamp).toLocaleTimeString()}`);
  });
  
  dashboard.on('alert', (data) => {
    console.log(`Alert: ${data.type} - ${data.value}`);
  });
  
  dashboard.on('critical-alert', (data) => {
    console.log(`CRITICAL ALERT: ${data.type} - ${data.value}`);
  });
  
  // Wait for a few refreshes
  console.log('\nWaiting for dashboard refreshes...');
  
  return new Promise((resolve) => {
    setTimeout(() => {
      // Get metrics
      const metrics = dashboard.getMetrics();
      console.log('\nDashboard Metrics:', JSON.stringify(metrics.dashboard, null, 2));
      
      // Stop refresh interval
      dashboard.stopRefreshInterval();
      
      resolve({
        dashboard,
        metrics
      });
    }, 2000);
  });
}

/**
 * Generate HTML report
 */
function generateHtmlReport(results) {
  console.log('\n=== Generating HTML Report ===');
  
  const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse Trinity Integration Demo</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .card {
      background-color: #f9f9f9;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
      flex: 1;
      min-width: 300px;
    }
    .integration-info {
      background-color: #f0f8ff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      border-left: 5px solid #0066cc;
    }
    .nova-connect {
      border-left-color: #cc0000;
    }
    .nova-core {
      border-left-color: #009900;
    }
    .nova-vision {
      border-left-color: #9900cc;
    }
    .nova-shield {
      border-left-color: #ff9900;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .valid {
      color: #009900;
      font-weight: bold;
    }
    .invalid {
      color: #cc0000;
    }
    footer {
      margin-top: 40px;
      text-align: center;
      color: #666;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <h1>NovaFuse Trinity Integration Demo</h1>
  <p>Generated: ${new Date().toLocaleString()}</p>
  
  <div class="integration-info">
    <h2>NovaFuse Trinity Integration</h2>
    <p>This demo demonstrates the integration of the Comphyological Trinity and Comphyon Meter-Governor with the NovaFuse platform components.</p>
  </div>
  
  <h2>NovaConnect Integration</h2>
  
  <div class="card nova-connect">
    <h3>API Boundary Validation</h3>
    <p>The First Law is applied at API boundaries to ensure that only resonant states are externalized.</p>
    
    <table>
      <tr>
        <th>Method</th>
        <th>Path</th>
        <th>Status</th>
      </tr>
      ${results.integration.apiResults.map(result => `
      <tr>
        <td>${result.request.method}</td>
        <td>${result.request.path}</td>
        <td class="${result.valid ? 'valid' : 'invalid'}">${result.valid ? 'Valid' : 'Invalid'}</td>
      </tr>
      `).join('')}
    </table>
  </div>
  
  <h2>NovaCore Integration</h2>
  
  <div class="card nova-core">
    <h3>State Management</h3>
    <p>The Second Law is applied to internal state transitions to ensure coherence and self-similarity.</p>
    
    <table>
      <tr>
        <th>Current State</th>
        <th>New State</th>
        <th>Status</th>
      </tr>
      ${results.integration.stateResults.map(result => `
      <tr>
        <td>${result.transition.current.status}</td>
        <td>${result.transition.new.status}</td>
        <td class="${result.valid ? 'valid' : 'invalid'}">${result.valid ? 'Valid' : 'Invalid'}</td>
      </tr>
      `).join('')}
    </table>
  </div>
  
  <h2>NovaVision Integration</h2>
  
  <div class="card nova-vision">
    <h3>Cross-Domain Visualization</h3>
    <p>The Third Law is applied to cross-domain translations to ensure integrity across domains.</p>
    
    <table>
      <tr>
        <th>Source Domain</th>
        <th>Target Domain</th>
        <th>Status</th>
      </tr>
      ${results.integration.crossDomainResults.map(result => `
      <tr>
        <td>${result.sourceDomain}</td>
        <td>${result.targetDomain}</td>
        <td class="${result.valid ? 'valid' : 'invalid'}">${result.valid ? 'Valid' : 'Invalid'}</td>
      </tr>
      `).join('')}
    </table>
  </div>
  
  <h2>NovaShield Integration</h2>
  
  <div class="card nova-shield">
    <h3>Security Enforcement</h3>
    <p>The Trinity-Comphyon Bridge is applied to security contexts to ensure compliance with all three laws.</p>
    
    <table>
      <tr>
        <th>User</th>
        <th>Role</th>
        <th>Status</th>
      </tr>
      ${results.integration.securityResults.map(result => `
      <tr>
        <td>${result.context.user}</td>
        <td>${result.context.role}</td>
        <td class="${result.valid ? 'valid' : 'invalid'}">${result.valid ? 'Valid' : 'Invalid'}</td>
      </tr>
      `).join('')}
    </table>
  </div>
  
  <h2>Dashboard Metrics</h2>
  
  <div class="container">
    <div class="card">
      <h3>Integration Metrics</h3>
      <ul>
        <li>API Validations: ${results.integration.metrics.integration.apiValidations}</li>
        <li>State Transitions: ${results.integration.metrics.integration.stateTransitions}</li>
        <li>Cross-Domain Operations: ${results.integration.metrics.integration.crossDomainOperations}</li>
        <li>Security Enforcements: ${results.integration.metrics.integration.securityEnforcements}</li>
        <li>Total Operations: ${results.integration.metrics.integration.totalOperations}</li>
      </ul>
    </div>
    
    <div class="card">
      <h3>Dashboard Metrics</h3>
      <ul>
        <li>Dashboard Refreshes: ${results.dashboard.metrics.dashboard.dashboardRefreshes}</li>
        <li>Alerts: ${results.dashboard.metrics.dashboard.alerts}</li>
        <li>Critical Alerts: ${results.dashboard.metrics.dashboard.criticalAlerts}</li>
        <li>Total Operations: ${results.dashboard.metrics.dashboard.totalOperations}</li>
      </ul>
    </div>
  </div>
  
  <footer>
    <p>NovaFuse Trinity Integration Demo - Copyright © ${new Date().getFullYear()}</p>
    <p><em>"The universe counts in 3s. Now, so do we."</em></p>
  </footer>
</body>
</html>`;
  
  // Save HTML report
  const reportPath = path.join(RESULTS_DIR, 'novafuse_trinity_integration_demo_report.html');
  fs.writeFileSync(reportPath, htmlContent);
  
  console.log(`HTML report saved to ${reportPath}`);
  
  return {
    htmlContent,
    reportPath
  };
}

/**
 * Main function
 */
async function main() {
  console.log('=== NovaFuse Trinity Integration Demo ===');
  
  // Test integration
  const integrationResults = testNovaTrinityIntegration();
  
  // Test dashboard
  const dashboardResults = await testTrinityDashboard(integrationResults.integration);
  
  // Generate HTML report
  const results = {
    integration: integrationResults,
    dashboard: dashboardResults
  };
  
  const reportResults = generateHtmlReport(results);
  
  // Save results to JSON file
  fs.writeFileSync(
    path.join(RESULTS_DIR, 'novafuse_trinity_integration_demo_results.json'),
    JSON.stringify(results, null, 2)
  );
  
  console.log(`\nResults saved to ${path.join(RESULTS_DIR, 'novafuse_trinity_integration_demo_results.json')}`);
  console.log(`HTML report saved to ${reportResults.reportPath}`);
  console.log('\nOpen the HTML report to view the results in a browser.');
}

// Run main function
main();
