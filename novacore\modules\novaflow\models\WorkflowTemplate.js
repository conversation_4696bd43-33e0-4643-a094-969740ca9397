/**
 * NovaCore Workflow Template Model
 * 
 * This model defines the schema for workflow templates in the NovaFlow module.
 * NovaFlow is the Universal Compliance Workflow Orchestrator (UCWO) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define task template schema
const taskTemplateSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  type: { 
    type: String, 
    enum: [
      'manual', 
      'automated', 
      'approval', 
      'notification', 
      'integration', 
      'decision', 
      'evidence_collection',
      'assessment',
      'verification',
      'reporting'
    ], 
    default: 'manual' 
  },
  priority: { 
    type: String, 
    enum: ['low', 'medium', 'high', 'critical'], 
    default: 'medium' 
  },
  estimatedDuration: { 
    type: Number, // in minutes
    default: 60
  },
  assignToRole: { 
    type: String, 
    trim: true 
  },
  dependencies: [{ 
    type: String, 
    trim: true 
  }],
  completionCriteria: { 
    type: String, 
    enum: [
      'manual', 
      'automatic', 
      'approval_required', 
      'evidence_required', 
      'condition'
    ], 
    default: 'manual' 
  },
  completionCondition: { 
    type: String, 
    trim: true 
  },
  automationConfig: {
    serviceType: { 
      type: String, 
      trim: true 
    },
    actionName: { 
      type: String, 
      trim: true 
    },
    parameters: { 
      type: Map, 
      of: Schema.Types.Mixed 
    },
    retryConfig: {
      maxRetries: { 
        type: Number, 
        default: 3 
      },
      retryInterval: { 
        type: Number, 
        default: 300 // seconds
      }
    }
  },
  integrationConfig: {
    connectorType: { 
      type: String, 
      trim: true 
    },
    endpoint: { 
      type: String, 
      trim: true 
    },
    method: { 
      type: String, 
      enum: ['GET', 'POST', 'PUT', 'DELETE'], 
      default: 'GET' 
    },
    headers: { 
      type: Map, 
      of: String 
    },
    payloadTemplate: { 
      type: Schema.Types.Mixed 
    },
    responseMapping: { 
      type: Map, 
      of: String 
    }
  },
  evidenceConfig: {
    evidenceTypeId: { 
      type: String, 
      trim: true 
    },
    sourceType: { 
      type: String, 
      trim: true 
    },
    controlCategory: { 
      type: String, 
      trim: true 
    },
    verificationRequired: { 
      type: Boolean, 
      default: false 
    }
  },
  notificationConfig: {
    templateId: { 
      type: String, 
      trim: true 
    },
    channels: [{ 
      type: String, 
      enum: ['email', 'sms', 'in_app', 'webhook'], 
      default: 'email' 
    }],
    recipientRoles: [{ 
      type: String, 
      trim: true 
    }]
  },
  decisionConfig: {
    conditions: [{
      field: { 
        type: String, 
        trim: true 
      },
      operator: { 
        type: String, 
        enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than'], 
        default: 'equals' 
      },
      value: { 
        type: Schema.Types.Mixed 
      },
      nextTaskId: { 
        type: String, 
        trim: true 
      }
    }],
    defaultNextTaskId: { 
      type: String, 
      trim: true 
    }
  },
  instructions: { 
    type: String, 
    trim: true 
  },
  tags: [{ 
    type: String, 
    trim: true 
  }]
}, { _id: false });

// Define stage template schema
const stageTemplateSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  order: { 
    type: Number, 
    default: 0 
  },
  tasks: [taskTemplateSchema],
  completionCriteria: { 
    type: String, 
    enum: [
      'all_tasks', 
      'any_task', 
      'specific_tasks', 
      'percentage'
    ], 
    default: 'all_tasks' 
  },
  completionConfig: {
    taskIds: [{ 
      type: String, 
      trim: true 
    }],
    percentage: { 
      type: Number, 
      min: 0, 
      max: 100 
    }
  },
  estimatedDuration: { 
    type: Number, // in days
    default: 1
  }
}, { _id: false });

// Define trigger template schema
const triggerTemplateSchema = new Schema({
  type: { 
    type: String, 
    enum: [
      'manual', 
      'scheduled', 
      'event', 
      'api', 
      'condition'
    ], 
    default: 'manual' 
  },
  config: {
    scheduleTemplate: { 
      type: String, 
      trim: true // cron expression or template
    },
    eventType: { 
      type: String, 
      trim: true 
    },
    eventSource: { 
      type: String, 
      trim: true 
    },
    conditionTemplate: { 
      type: String, 
      trim: true 
    },
    apiEndpoint: { 
      type: String, 
      trim: true 
    }
  },
  enabledByDefault: { 
    type: Boolean, 
    default: true 
  }
}, { _id: false });

// Define data field schema
const dataFieldSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  type: { 
    type: String, 
    enum: [
      'string', 
      'number', 
      'boolean', 
      'date', 
      'object', 
      'array', 
      'reference'
    ], 
    default: 'string' 
  },
  required: { 
    type: Boolean, 
    default: false 
  },
  defaultValue: { 
    type: Schema.Types.Mixed 
  },
  validation: {
    pattern: { 
      type: String, 
      trim: true 
    },
    min: { 
      type: Number 
    },
    max: { 
      type: Number 
    },
    enum: [{ 
      type: Schema.Types.Mixed 
    }]
  },
  referenceType: { 
    type: String, 
    trim: true 
  }
}, { _id: false });

// Define workflow template schema
const workflowTemplateSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  organizationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Organization', 
    required: true 
  },
  type: { 
    type: String, 
    enum: [
      'compliance', 
      'assessment', 
      'evidence_collection', 
      'remediation', 
      'certification', 
      'audit', 
      'custom'
    ], 
    default: 'compliance' 
  },
  category: { 
    type: String, 
    trim: true 
  },
  status: { 
    type: String, 
    enum: [
      'draft', 
      'active', 
      'inactive', 
      'archived'
    ], 
    default: 'draft' 
  },
  priority: { 
    type: String, 
    enum: ['low', 'medium', 'high', 'critical'], 
    default: 'medium' 
  },
  stages: [stageTemplateSchema],
  triggers: [triggerTemplateSchema],
  dataModel: [dataFieldSchema],
  variables: [dataFieldSchema],
  integrationRequirements: [{
    name: { 
      type: String, 
      required: true, 
      trim: true 
    },
    type: { 
      type: String, 
      required: true, 
      trim: true 
    },
    required: { 
      type: Boolean, 
      default: true 
    },
    description: { 
      type: String, 
      trim: true 
    }
  }],
  permissions: [{
    role: { 
      type: String, 
      required: true, 
      trim: true 
    },
    actions: [{ 
      type: String, 
      enum: ['view', 'edit', 'execute', 'manage'], 
      default: 'view' 
    }]
  }],
  estimatedDuration: { 
    type: Number, // in days
    default: 7
  },
  tags: [{ 
    type: String, 
    trim: true 
  }],
  frameworks: [{ 
    type: String, 
    trim: true 
  }],
  applicableEntityTypes: [{ 
    type: String, 
    trim: true 
  }],
  version: { 
    type: String, 
    default: '1.0' 
  },
  previousVersion: { 
    type: Schema.Types.ObjectId, 
    ref: 'WorkflowTemplate' 
  },
  isDefault: { 
    type: Boolean, 
    default: false 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
workflowTemplateSchema.index({ organizationId: 1 });
workflowTemplateSchema.index({ name: 1, organizationId: 1 });
workflowTemplateSchema.index({ type: 1 });
workflowTemplateSchema.index({ category: 1 });
workflowTemplateSchema.index({ status: 1 });
workflowTemplateSchema.index({ priority: 1 });
workflowTemplateSchema.index({ tags: 1 });
workflowTemplateSchema.index({ frameworks: 1 });
workflowTemplateSchema.index({ applicableEntityTypes: 1 });
workflowTemplateSchema.index({ version: 1 });
workflowTemplateSchema.index({ isDefault: 1 });
workflowTemplateSchema.index({ createdAt: 1 });

// Add methods
workflowTemplateSchema.methods.isActive = function() {
  return this.status === 'active';
};

workflowTemplateSchema.methods.getStage = function(stageId) {
  return this.stages.find(stage => stage.id === stageId);
};

workflowTemplateSchema.methods.getTask = function(taskId) {
  for (const stage of this.stages) {
    const task = stage.tasks.find(task => task.id === taskId);
    if (task) {
      return { stage, task };
    }
  }
  return null;
};

workflowTemplateSchema.methods.getTotalTasks = function() {
  return this.stages.reduce((sum, stage) => sum + stage.tasks.length, 0);
};

// Add statics
workflowTemplateSchema.statics.findByOrganization = function(organizationId) {
  return this.find({ organizationId });
};

workflowTemplateSchema.statics.findActive = function(organizationId) {
  return this.find({ 
    organizationId, 
    status: 'active' 
  });
};

workflowTemplateSchema.statics.findByType = function(organizationId, type) {
  return this.find({ 
    organizationId, 
    type 
  });
};

workflowTemplateSchema.statics.findByFramework = function(organizationId, framework) {
  return this.find({ 
    organizationId, 
    frameworks: framework 
  });
};

workflowTemplateSchema.statics.findDefault = function(organizationId, type) {
  return this.findOne({ 
    organizationId, 
    type, 
    isDefault: true, 
    status: 'active' 
  });
};

workflowTemplateSchema.statics.findByEntityType = function(organizationId, entityType) {
  return this.find({ 
    organizationId, 
    applicableEntityTypes: entityType 
  });
};

// Create model
const WorkflowTemplate = mongoose.model('WorkflowTemplate', workflowTemplateSchema);

module.exports = WorkflowTemplate;
