// Set up global test environment
jest.setTimeout(30000); // 30 seconds timeout for all tests

// Mock console.error to avoid cluttering test output
const originalConsoleError = console.error;
console.error = (...args) => {
  // Suppress Next.js Link warnings and other common React warnings
  if (typeof args[0] === 'string' && (
    args[0].includes('Warning:') ||
    args[0].includes('Invalid <Link> with <a> child')
  )) {
    return;
  }
  originalConsoleError(...args);
};

// Add global test utilities
global.waitFor = async (condition, timeout = 5000, interval = 100) => {
  const startTime = Date.now();
  while (Date.now() - startTime < timeout) {
    if (await condition()) {
      return true;
    }
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  throw new Error(`Timeout waiting for condition after ${timeout}ms`);
};

// Add custom matchers
expect.extend({
  toBeWithinRange(received, floor, ceiling) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () => `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false,
      };
    }
  },
  toHaveResponseTime(received, maxTime) {
    const pass = received <= maxTime;
    if (pass) {
      return {
        message: () => `expected response time ${received}ms to be greater than ${maxTime}ms`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected response time ${received}ms to be less than or equal to ${maxTime}ms`,
        pass: false,
      };
    }
  },
  toBeValidConnector(received) {
    // Check if the object has the required connector properties
    const hasMetadata = received && typeof received.metadata === 'object';
    const hasAuthentication = received && typeof received.authentication === 'object';
    const hasConfiguration = received && typeof received.configuration === 'object';
    const hasEndpoints = received && Array.isArray(received.endpoints);

    const pass = hasMetadata && hasAuthentication && hasConfiguration && hasEndpoints;

    if (pass) {
      return {
        message: () => 'expected object not to be a valid connector',
        pass: true,
      };
    } else {
      return {
        message: () => 'expected object to be a valid connector with metadata, authentication, configuration, and endpoints',
        pass: false,
      };
    }
  },
  toHaveSecureEndpoints(received) {
    // Check if all endpoints use HTTPS
    const baseUrl = received && received.configuration && received.configuration.baseUrl;
    const pass = baseUrl && baseUrl.startsWith('https://');

    if (pass) {
      return {
        message: () => `expected baseUrl ${baseUrl} not to use HTTPS`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected baseUrl ${baseUrl} to use HTTPS`,
        pass: false,
      };
    }
  },
});
