/**
 * NovaFuse Universal API Connector - Connector Registry Integration Tests
 * 
 * This module tests the connector registry service.
 */

const {
  createTestSuite,
  assertTrue,
  assertFalse,
  assertEqual,
  assertNotEqual,
  assertDefined,
  assertNull,
  assertNotNull,
  assertThrows,
  assertDoesNotThrow
} = require('./test-framework');

const connectorRegistryService = require('../../src/connectors/services/connector-registry');

// Create test suite
const suite = createTestSuite('Connector Registry Integration Tests');

// Test data
let testConnector = null;

// Setup and teardown
suite.beforeAll(async () => {
  // Clear any existing connectors
  connectorRegistryService.connectors.clear();
});

suite.afterAll(async () => {
  // Clean up
  connectorRegistryService.connectors.clear();
});

// Tests
suite.test('should create a connector', async () => {
  // Create connector
  testConnector = await connectorRegistryService.createConnector({
    name: 'Test Connector',
    description: 'A test connector for integration testing',
    version: '1.0.0',
    type: 'source',
    status: 'draft'
  });
  
  // Assertions
  assertNotNull(testConnector, 'Connector should not be null');
  assertDefined(testConnector.id, 'Connector ID should be defined');
  assertEqual(testConnector.name, 'Test Connector', 'Connector name should match');
  assertEqual(testConnector.description, 'A test connector for integration testing', 'Connector description should match');
  assertEqual(testConnector.version, '1.0.0', 'Connector version should match');
  assertEqual(testConnector.type, 'source', 'Connector type should match');
  assertEqual(testConnector.status, 'draft', 'Connector status should match');
});

suite.test('should get a connector by ID', async () => {
  // Get connector
  const connector = await connectorRegistryService.getConnector(testConnector.id);
  
  // Assertions
  assertNotNull(connector, 'Connector should not be null');
  assertEqual(connector.id, testConnector.id, 'Connector ID should match');
  assertEqual(connector.name, testConnector.name, 'Connector name should match');
});

suite.test('should get all connectors', async () => {
  // Get all connectors
  const connectors = await connectorRegistryService.getAllConnectors();
  
  // Assertions
  assertNotNull(connectors, 'Connectors should not be null');
  assertTrue(Array.isArray(connectors), 'Connectors should be an array');
  assertTrue(connectors.length > 0, 'Connectors should not be empty');
  
  // Find our test connector
  const connector = connectors.find(c => c.id === testConnector.id);
  assertNotNull(connector, 'Test connector should be in the list');
});

suite.test('should update a connector', async () => {
  // Update connector
  const updatedConnector = await connectorRegistryService.updateConnector(testConnector.id, {
    description: 'An updated test connector'
  });
  
  // Assertions
  assertNotNull(updatedConnector, 'Updated connector should not be null');
  assertEqual(updatedConnector.id, testConnector.id, 'Connector ID should not change');
  assertEqual(updatedConnector.description, 'An updated test connector', 'Connector description should be updated');
  
  // Update our reference
  testConnector = updatedConnector;
});

suite.test('should delete a connector', async () => {
  // Delete connector
  const result = await connectorRegistryService.deleteConnector(testConnector.id);
  
  // Assertions
  assertTrue(result, 'Delete should return true');
  
  // Try to get the deleted connector
  try {
    await connectorRegistryService.getConnector(testConnector.id);
    throw new Error('Should have thrown an error');
  } catch (error) {
    assertTrue(error.message.includes('not found'), 'Error should indicate connector not found');
  }
});

// Run the test suite
async function runTests() {
  const results = await suite.run();
  
  if (results.failed > 0) {
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  suite,
  runTests
};
