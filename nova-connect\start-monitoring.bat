@echo off
REM Start the monitoring infrastructure for NovaConnect UAC

REM Create necessary directories
mkdir monitoring\prometheus 2>nul
mkdir monitoring\grafana\provisioning\datasources 2>nul
mkdir monitoring\grafana\provisioning\dashboards 2>nul

REM Start the monitoring infrastructure
docker-compose -f docker-compose.monitoring.yml up -d

REM Wait for services to start
echo Waiting for services to start...
timeout /t 5 /nobreak > nul

REM Display information
echo Monitoring infrastructure started successfully!
echo.
echo Grafana: http://localhost:3000 (admin/admin)
echo Prometheus: http://localhost:9090
echo Zipkin: http://localhost:9411
echo Jaeger: http://localhost:16686
echo NovaConnect: http://localhost:3001
echo.
echo Press any key to open these URLs in your browser...
pause > nul

REM Open URLs in browser
start http://localhost:3000
start http://localhost:9090
start http://localhost:9411
start http://localhost:16686
start http://localhost:3001
