apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-novafuse-uac-config
  labels:
    app: novafuse-uac
    component: api
    release: {{ .Release.Name }}
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: helm
data:
  production.json: |-
    {
      "server": {
        "port": 3001,
        "host": "0.0.0.0",
        "cors": {
          "origin": {{ .Values.corsOrigin | quote }},
          "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
          "allowedHeaders": ["Content-Type", "Authorization", "X-API-Key", "X-Request-ID"],
          "exposedHeaders": ["X-Request-ID", "X-RateLimit-Limit", "X-RateLimit-Remaining", "X-RateLimit-Reset"],
          "credentials": true,
          "maxAge": 86400
        }
      },
      "logging": {
        "level": {{ .Values.logLevel | quote }},
        "format": "json",
        "colorize": false,
        "timestamp": true
      },
      "security": {
        "helmet": {
          "enabled": {{ .Values.helmetEnabled }}
        },
        "csrf": {
          "enabled": {{ .Values.csrfEnabled }}
        },
        "rateLimit": {
          "enabled": {{ .Values.rateLimitEnabled }},
          "windowMs": {{ .Values.rateLimitWindowMs }},
          "max": {{ .Values.rateLimitMax }}
        },
        "ipFiltering": {
          "enabled": {{ .Values.ipFilteringEnabled }},
          "allowedIps": []
        }
      },
      "performance": {
        "cluster": {
          "enabled": {{ .Values.clusterEnabled }}
        },
        "cache": {
          "enabled": {{ .Values.cacheEnabled }},
          "ttl": 300
        },
        "compression": {
          "enabled": {{ .Values.compressionEnabled }}
        }
      },
      "featureFlags": {
        "enabled": true,
        "source": "file",
        "filePath": "config/feature_flags.json",
        "defaultTier": {{ .Values.tier | quote }}
      },
      "monitoring": {
        "metrics": {
          "enabled": true,
          "path": "/metrics"
        },
        "health": {
          "enabled": true,
          "path": "/health"
        },
        "sentry": {
          "enabled": true
        }
      },
      "gcp": {
        "enabled": true,
        "projectId": {{ .Values.gcpProjectId | quote }},
        "region": {{ .Values.gcpRegion | quote }},
        "zone": {{ .Values.gcpZone | quote }},
        "monitoring": {
          "enabled": true
        },
        "logging": {
          "enabled": true
        },
        "errorReporting": {
          "enabled": true
        },
        "tracing": {
          "enabled": true
        },
        "profiling": {
          "enabled": true
        },
        "debugging": {
          "enabled": false
        },
        "secretManager": {
          "enabled": true
        }
      }
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-novafuse-uac-feature-flags
  labels:
    app: novafuse-uac
    component: api
    release: {{ .Release.Name }}
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: helm
data:
  feature_flags.json: |-
    [
      {
        "id": "core.basic_connectors",
        "name": "Basic Connectors",
        "description": "Access to basic connectors",
        "category": "core",
        "enabled": true,
        "tiers": ["core", "secure", "enterprise", "ai_boost"]
      },
      {
        "id": "core.manual_execution",
        "name": "Manual Execution",
        "description": "Manual execution of connectors",
        "category": "core",
        "enabled": true,
        "tiers": ["core", "secure", "enterprise", "ai_boost"],
        "limits": {
          "core": { "operations_per_day": 100 },
          "secure": { "operations_per_day": 1000 },
          "enterprise": { "operations_per_day": 10000 },
          "ai_boost": { "operations_per_day": -1 }
        }
      },
      {
        "id": "workflow.scheduled",
        "name": "Scheduled Execution",
        "description": "Scheduled execution of connectors",
        "category": "workflow",
        "enabled": true,
        "tiers": ["secure", "enterprise", "ai_boost"],
        "limits": {
          "secure": { "schedules": 10 },
          "enterprise": { "schedules": 100 },
          "ai_boost": { "schedules": -1 }
        }
      },
      {
        "id": "workflow.triggers",
        "name": "Event Triggers",
        "description": "Event-based triggers for connectors",
        "category": "workflow",
        "enabled": true,
        "tiers": ["secure", "enterprise", "ai_boost"],
        "limits": {
          "secure": { "triggers": 10 },
          "enterprise": { "triggers": 100 },
          "ai_boost": { "triggers": -1 }
        }
      },
      {
        "id": "export.config",
        "name": "Configuration Export",
        "description": "Export connector configurations",
        "category": "export",
        "enabled": true,
        "tiers": ["secure", "enterprise", "ai_boost"]
      },
      {
        "id": "import.config",
        "name": "Configuration Import",
        "description": "Import connector configurations",
        "category": "import",
        "enabled": true,
        "tiers": ["secure", "enterprise", "ai_boost"]
      },
      {
        "id": "security.audit",
        "name": "Security Audit",
        "description": "Security audit logging",
        "category": "security",
        "enabled": true,
        "tiers": ["secure", "enterprise", "ai_boost"]
      },
      {
        "id": "security.encryption",
        "name": "Field Encryption",
        "description": "Field-level encryption",
        "category": "security",
        "enabled": true,
        "tiers": ["secure", "enterprise", "ai_boost"]
      },
      {
        "id": "monitoring.alerts",
        "name": "Monitoring Alerts",
        "description": "Monitoring and alerting",
        "category": "monitoring",
        "enabled": true,
        "tiers": ["enterprise", "ai_boost"]
      },
      {
        "id": "monitoring.dashboards",
        "name": "Custom Dashboards",
        "description": "Custom monitoring dashboards",
        "category": "monitoring",
        "enabled": true,
        "tiers": ["enterprise", "ai_boost"],
        "limits": {
          "enterprise": { "dashboards": 5 },
          "ai_boost": { "dashboards": -1 }
        }
      },
      {
        "id": "analytics.reports",
        "name": "Analytics Reports",
        "description": "Analytics and reporting",
        "category": "analytics",
        "enabled": true,
        "tiers": ["enterprise", "ai_boost"],
        "limits": {
          "enterprise": { "reports": 10 },
          "ai_boost": { "reports": -1 }
        }
      },
      {
        "id": "analytics.export",
        "name": "Analytics Export",
        "description": "Export analytics data",
        "category": "analytics",
        "enabled": true,
        "tiers": ["enterprise", "ai_boost"]
      },
      {
        "id": "ai.normalization",
        "name": "AI Normalization",
        "description": "AI-powered data normalization",
        "category": "ai",
        "enabled": true,
        "tiers": ["ai_boost"],
        "limits": {
          "ai_boost": { "operations_per_day": 1000 }
        }
      },
      {
        "id": "ai.insights",
        "name": "AI Insights",
        "description": "AI-powered insights",
        "category": "ai",
        "enabled": true,
        "tiers": ["ai_boost"],
        "limits": {
          "ai_boost": { "operations_per_day": 100 }
        }
      },
      {
        "id": "governance.policies",
        "name": "Governance Policies",
        "description": "Governance policy management",
        "category": "governance",
        "enabled": true,
        "tiers": ["enterprise", "ai_boost"],
        "limits": {
          "enterprise": { "policies": 10 },
          "ai_boost": { "policies": -1 }
        }
      },
      {
        "id": "governance.compliance",
        "name": "Compliance Reporting",
        "description": "Compliance reporting",
        "category": "governance",
        "enabled": true,
        "tiers": ["enterprise", "ai_boost"]
      }
    ]
