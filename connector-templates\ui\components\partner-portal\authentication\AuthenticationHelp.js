import React from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Accordion, 
  AccordionSummary, 
  AccordionDetails,
  Link,
  Divider
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon
} from '@mui/icons-material';

/**
 * Authentication Help Component
 * 
 * This component provides helpful information about different authentication methods.
 */
const AuthenticationHelp = ({ authType }) => {
  // Help content for each authentication type
  const helpContent = {
    'API_KEY': {
      title: 'API Key Authentication',
      description: 'API keys are simple tokens that are included in the request header, query parameter, or as part of the URL.',
      fields: [
        { name: 'apiKey', description: 'The API key provided by the service' }
      ],
      examples: [
        { title: 'Header Example', code: 'X-API-Key: your_api_key_here' },
        { title: 'Query Parameter Example', code: 'https://api.example.com/data?api_key=your_api_key_here' }
      ],
      tips: [
        'Store API keys securely and never expose them in client-side code',
        'Many APIs allow you to restrict API keys to specific IP addresses',
        'Rotate API keys periodically for better security'
      ],
      resources: [
        { title: 'API Key Best Practices', url: 'https://cloud.google.com/endpoints/docs/openapi/when-why-api-key' }
      ]
    },
    'BASIC': {
      title: 'Basic Authentication',
      description: 'Basic authentication uses a username and password encoded in base64 format.',
      fields: [
        { name: 'username', description: 'The username for authentication' },
        { name: 'password', description: 'The password for authentication' }
      ],
      examples: [
        { title: 'Authorization Header', code: 'Authorization: Basic dXNlcm5hbWU6cGFzc3dvcmQ=' }
      ],
      tips: [
        'Basic auth should only be used over HTTPS connections',
        'The username and password are encoded, not encrypted',
        'Consider using a more secure authentication method for production systems'
      ],
      resources: [
        { title: 'Basic Authentication Specification', url: 'https://tools.ietf.org/html/rfc7617' }
      ]
    },
    'OAUTH2': {
      title: 'OAuth 2.0 Authentication',
      description: 'OAuth 2.0 is a protocol that allows a user to grant a third-party website or application access to their resources, without necessarily revealing their credentials.',
      fields: [
        { name: 'clientId', description: 'The client ID provided by the service' },
        { name: 'clientSecret', description: 'The client secret provided by the service' },
        { name: 'accessToken', description: 'The access token obtained from the OAuth flow' },
        { name: 'refreshToken', description: 'The refresh token used to obtain new access tokens' }
      ],
      examples: [
        { title: 'Authorization Header', code: 'Authorization: Bearer your_access_token_here' }
      ],
      tips: [
        'OAuth 2.0 has different grant types for different use cases',
        'Store refresh tokens securely',
        'Implement token refresh logic to handle expired access tokens'
      ],
      resources: [
        { title: 'OAuth 2.0 Simplified', url: 'https://www.oauth.com/' },
        { title: 'OAuth 2.0 Specification', url: 'https://tools.ietf.org/html/rfc6749' }
      ]
    },
    'JWT': {
      title: 'JWT Authentication',
      description: 'JSON Web Tokens are an open standard that defines a compact and self-contained way for securely transmitting information between parties as a JSON object.',
      fields: [
        { name: 'token', description: 'The JWT token' }
      ],
      examples: [
        { title: 'Authorization Header', code: 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }
      ],
      tips: [
        'JWTs contain three parts: header, payload, and signature',
        'Never store sensitive information in a JWT as the payload can be decoded',
        'Set appropriate expiration times for JWTs'
      ],
      resources: [
        { title: 'JWT Introduction', url: 'https://jwt.io/introduction/' },
        { title: 'JWT Debugger', url: 'https://jwt.io/' }
      ]
    },
    'AWS_SIG_V4': {
      title: 'AWS Signature Version 4',
      description: 'AWS Signature Version 4 is a signing process for AWS API requests.',
      fields: [
        { name: 'accessKeyId', description: 'AWS Access Key ID' },
        { name: 'secretAccessKey', description: 'AWS Secret Access Key' },
        { name: 'region', description: 'AWS Region' }
      ],
      examples: [
        { title: 'Authorization Header', code: 'Authorization: AWS4-HMAC-SHA256 Credential=AKIAIOSFODNN7EXAMPLE/20130524/us-east-1/s3/aws4_request, SignedHeaders=host;range;x-amz-date, Signature=fe5f80f77d5fa3beca038a248ff027d0445342fe2855ddc963176630326f1024' }
      ],
      tips: [
        'AWS Signature V4 is complex and requires precise implementation',
        'Use AWS SDKs when possible to handle signing automatically',
        'Ensure your system clock is synchronized for accurate request timestamps'
      ],
      resources: [
        { title: 'AWS Signature V4 Documentation', url: 'https://docs.aws.amazon.com/general/latest/gr/signature-version-4.html' }
      ]
    },
    'CUSTOM': {
      title: 'Custom Authentication',
      description: 'Custom authentication allows you to define your own authentication method for specialized APIs.',
      fields: [
        { name: 'Custom fields', description: 'Define the fields required by your custom authentication method' }
      ],
      examples: [
        { title: 'Example', code: 'Depends on your custom authentication requirements' }
      ],
      tips: [
        'Document your custom authentication method thoroughly',
        'Consider security implications of your custom method',
        'Test your custom authentication method extensively'
      ],
      resources: [
        { title: 'API Security Best Practices', url: 'https://owasp.org/www-project-api-security/' }
      ]
    }
  };

  // Get help content for the selected authentication type
  const content = helpContent[authType] || helpContent['API_KEY'];

  return (
    <Paper sx={{ p: 3, mb: 3, backgroundColor: 'rgba(37, 99, 235, 0.05)' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <InfoIcon color="primary" sx={{ mr: 1 }} />
        <Typography variant="h6">{content.title}</Typography>
      </Box>
      
      <Typography variant="body1" paragraph>
        {content.description}
      </Typography>
      
      <Divider sx={{ my: 2 }} />
      
      {/* Common Fields */}
      <Typography variant="subtitle2" gutterBottom>
        Common Fields
      </Typography>
      <Box sx={{ mb: 2 }}>
        {content.fields.map((field, index) => (
          <Box key={index} sx={{ mb: 1 }}>
            <Typography variant="body2" component="span" sx={{ fontWeight: 'bold' }}>
              {field.name}:
            </Typography>
            <Typography variant="body2" component="span" sx={{ ml: 1 }}>
              {field.description}
            </Typography>
          </Box>
        ))}
      </Box>
      
      {/* Examples */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Examples</Typography>
        </AccordionSummary>
        <AccordionDetails>
          {content.examples.map((example, index) => (
            <Box key={index} sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                {example.title}
              </Typography>
              <Box 
                component="pre" 
                sx={{ 
                  p: 1, 
                  backgroundColor: 'rgba(0,0,0,0.04)', 
                  borderRadius: 1,
                  fontSize: '0.75rem',
                  overflow: 'auto'
                }}
              >
                {example.code}
              </Box>
            </Box>
          ))}
        </AccordionDetails>
      </Accordion>
      
      {/* Tips */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Tips</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
            {content.tips.map((tip, index) => (
              <li key={index}>
                <Typography variant="body2">{tip}</Typography>
              </li>
            ))}
          </ul>
        </AccordionDetails>
      </Accordion>
      
      {/* Resources */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Resources</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
            {content.resources.map((resource, index) => (
              <li key={index}>
                <Link 
                  href={resource.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                >
                  {resource.title}
                </Link>
              </li>
            ))}
          </ul>
        </AccordionDetails>
      </Accordion>
    </Paper>
  );
};

export default AuthenticationHelp;
