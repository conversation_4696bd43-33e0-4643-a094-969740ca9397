# NovaConnect + CSDE Integration

This README provides instructions for deploying and using the NovaConnect + CSDE integration in a production environment.

## Overview

The NovaConnect + CSDE integration leverages the Cyber-Safety Domain Engine (CSDE) to enhance NovaConnect's data transformation capabilities using the Universal Unified Field Theory (UUFT) equation.

Key benefits of this integration:

- **Enhanced Data Transformation**: Leverage the UUFT equation for more accurate and efficient data transformations
- **Improved Performance**: Achieve up to 3,142x performance improvement for data processing
- **Advanced Analytics**: Gain insights from the CSDE's advanced analytics capabilities
- **Automated Remediation**: Get remediation recommendations from the CSDE

## Prerequisites

- Docker and Docker Compose
- Node.js 14+ (for development)
- Access to the CSDE codebase

## Directory Structure

```
nova-connect/
├── src/
│   ├── connectors/
│   │   └── csde-connector.js       # CSDE connector implementation
│   ├── integrations/
│   │   └── csde-integration.js     # CSDE integration implementation
├── config/
│   ├── connectors/
│   │   └── csde-connector.json     # CSDE connector configuration
├── monitoring/
│   ├── prometheus/
│   │   └── prometheus.yml          # Prometheus configuration
│   ├── grafana/
│   │   └── provisioning/
│   │       └── dashboards/
│   │           └── csde-integration-dashboard.json  # Grafana dashboard
├── docker-compose.yml              # Docker Compose configuration
├── deploy-csde-integration.sh      # Deployment script (Linux/macOS)
├── deploy-csde-integration.ps1     # Deployment script (Windows)
└── README-CSDE-Integration.md      # This README
```

## Deployment

### Automated Deployment

#### Windows

```powershell
.\deploy-csde-integration.ps1
```

#### Linux/macOS

```bash
chmod +x deploy-csde-integration.sh
./deploy-csde-integration.sh
```

### Manual Deployment

1. **Set Environment Variables**

   Create a `.env` file with the following content:

   ```
   NODE_ENV=production
   PORT=3001
   CSDE_API_URL=http://csde-api:3010
   LOG_LEVEL=info
   METRICS_ENABLED=true
   TRACING_ENABLED=true
   ```

2. **Build and Start Containers**

   ```bash
   docker-compose build
   docker-compose up -d
   ```

3. **Verify Deployment**

   Access the following URLs to verify that the services are running:

   - NovaConnect API: http://localhost:3001
   - CSDE API: http://localhost:3010
   - Grafana: http://localhost:3002 (username: admin, password: admin)
   - Prometheus: http://localhost:9090

## Usage

### Using the CSDE Connector

The CSDE connector can be used like any other NovaConnect connector:

```javascript
const connector = await connectorRegistry.getConnector('csde-connector');

// Transform data using CSDE
const transformedData = await connector.transform(data, {
  rules: [
    {
      source: 'id',
      target: 'finding_id',
      transform: 'uppercase'
    },
    // More rules...
  ]
});

// Process a batch of data items
const batchResults = await connector.processBatch(items, {
  concurrency: 5
});

// Get connector health status
const health = await connector.getHealth();

// Clear connector cache
await connector.clearCache();

// Reset connector metrics
await connector.resetMetrics();
```

### Using the CSDE Integration Directly

You can also use the CSDE integration directly:

```javascript
const CSEDIntegration = require('../src/integrations/csde-integration');

// Create CSDE integration
const csdeIntegration = new CSEDIntegration({
  csdeApiUrl: process.env.CSDE_API_URL || 'http://localhost:3010',
  enableCaching: true,
  enableMetrics: true,
  cacheSize: 1000
});

// Transform data using CSDE
const result = await csdeIntegration.transform(data, rules);

// Get metrics
const metrics = csdeIntegration.getMetrics();
console.log('Average Latency:', metrics.averageLatency, 'ms');
console.log('Cache Hits:', metrics.cacheHits);
console.log('Cache Misses:', metrics.cacheMisses);

// Clear cache
csdeIntegration.clearCache();

// Reset metrics
csdeIntegration.resetMetrics();
```

## Monitoring

### Prometheus Metrics

The following metrics are available in Prometheus:

- `csde_api_requests_total`: Total number of CSDE API requests
- `csde_api_errors_total`: Total number of CSDE API errors
- `csde_api_request_duration_seconds`: CSDE API request duration
- `csde_api_cache_hits_total`: Total number of CSDE API cache hits
- `csde_api_cache_misses_total`: Total number of CSDE API cache misses
- `csde_api_performance_factor`: CSDE performance factor

### Grafana Dashboard

A Grafana dashboard is available at http://localhost:3002 (username: admin, password: admin). The dashboard includes the following panels:

- CSDE API Request Rate
- CSDE API Response Time
- Total CSDE API Requests
- Total CSDE API Errors
- CSDE API Cache Hit Rate
- CSDE Performance Factor

## Troubleshooting

### Common Issues

#### CSDE API Connection Issues

If you're having trouble connecting to the CSDE API:

1. Check that the CSDE API is running: `docker-compose ps`
2. Verify the CSDE API URL: `echo $CSDE_API_URL`
3. Check the CSDE API logs: `docker-compose logs csde-api`

#### Performance Issues

If you're experiencing performance issues:

1. Enable caching: `enableCaching: true`
2. Increase cache size: `cacheSize: 1000`
3. Use batch processing for large datasets: `connector.processBatch(items, { concurrency: 5 })`
4. Monitor performance metrics in Grafana

#### Error Handling

Common errors and how to handle them:

1. **CSDE API Not Available**: Implement a fallback to standard transformation
2. **Invalid Input Data**: Validate data before sending to CSDE API
3. **Timeout**: Implement retry logic with exponential backoff

## References

- [CSDE Integration Guide](./docs/CSDE_Integration_Guide.md)
- [NovaConnect Documentation](./README.md)
- [CSDE Documentation](../src/csde/README.md)
