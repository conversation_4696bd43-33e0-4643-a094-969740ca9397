const Joi = require('joi');

// Validation schemas
const schemas = {
  // Disclosure validation schemas
  createDisclosure: Joi.object({
    title: Joi.string().required().min(3).max(100),
    description: Joi.string().optional().max(500),
    regulationType: Joi.string().required().valid('mandatory', 'voluntary'),
    regulationName: Joi.string().required().max(100),
    jurisdiction: Joi.string().required().max(100),
    applicabilityDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    reportingFrequency: Joi.string().required().valid('annual', 'semi-annual', 'quarterly', 'one-time'),
    nextDueDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    status: Joi.string().required().valid('not-started', 'in-progress', 'submitted', 'approved', 'rejected'),
    assignedTo: Joi.string().optional().max(100),
    frameworkIds: Joi.array().items(Joi.string()).optional(),
    metricIds: Joi.array().items(Joi.string()).optional(),
    documents: Joi.array().items(
      Joi.object({
        name: Joi.string().required().min(1).max(100),
        type: Joi.string().required().max(20),
        url: Joi.string().required().uri()
      })
    ).optional()
  }),
  
  updateDisclosure: Joi.object({
    title: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    regulationType: Joi.string().optional().valid('mandatory', 'voluntary'),
    regulationName: Joi.string().optional().max(100),
    jurisdiction: Joi.string().optional().max(100),
    applicabilityDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    reportingFrequency: Joi.string().optional().valid('annual', 'semi-annual', 'quarterly', 'one-time'),
    nextDueDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    status: Joi.string().optional().valid('not-started', 'in-progress', 'submitted', 'approved', 'rejected'),
    assignedTo: Joi.string().optional().allow(null, '').max(100),
    frameworkIds: Joi.array().items(Joi.string()).optional(),
    metricIds: Joi.array().items(Joi.string()).optional()
  }).min(1), // At least one field must be provided
  
  addDocument: Joi.object({
    name: Joi.string().required().min(1).max(100),
    type: Joi.string().required().max(20),
    url: Joi.string().required().uri()
  }),
  
  addSubmission: Joi.object({
    date: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    status: Joi.string().required().valid('submitted', 'approved', 'rejected'),
    notes: Joi.string().optional().max(500)
  }),
  
  // Regulation validation schemas
  createRegulation: Joi.object({
    name: Joi.string().required().min(3).max(100),
    description: Joi.string().required().max(500),
    type: Joi.string().required().valid('mandatory', 'voluntary'),
    category: Joi.string().required().valid('environmental', 'social', 'governance', 'general'),
    jurisdiction: Joi.string().required().max(100),
    issuingAuthority: Joi.string().required().max(100),
    effectiveDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    reportingFrequency: Joi.string().required().valid('annual', 'semi-annual', 'quarterly', 'one-time'),
    website: Joi.string().optional().uri().max(200),
    requirements: Joi.array().items(
      Joi.object({
        name: Joi.string().required().min(1).max(100),
        description: Joi.string().required().max(500)
      })
    ).optional(),
    frameworkIds: Joi.array().items(Joi.string()).optional()
  }),
  
  updateRegulation: Joi.object({
    name: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    type: Joi.string().optional().valid('mandatory', 'voluntary'),
    category: Joi.string().optional().valid('environmental', 'social', 'governance', 'general'),
    jurisdiction: Joi.string().optional().max(100),
    issuingAuthority: Joi.string().optional().max(100),
    effectiveDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    reportingFrequency: Joi.string().optional().valid('annual', 'semi-annual', 'quarterly', 'one-time'),
    website: Joi.string().optional().uri().max(200),
    requirements: Joi.array().items(
      Joi.object({
        id: Joi.string().optional(),
        name: Joi.string().required().min(1).max(100),
        description: Joi.string().required().max(500)
      })
    ).optional(),
    frameworkIds: Joi.array().items(Joi.string()).optional()
  }).min(1) // At least one field must be provided
};

/**
 * Middleware to validate request data against a schema
 * @param {string} schemaName - Name of the schema to validate against
 * @returns {Function} Express middleware function
 */
const validateRequest = (schemaName) => {
  return (req, res, next) => {
    const schema = schemas[schemaName];
    
    if (!schema) {
      return res.status(500).json({
        error: 'Internal Server Error',
        message: `Validation schema '${schemaName}' not found`
      });
    }
    
    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // Return all errors, not just the first one
      stripUnknown: true // Remove unknown fields
    });
    
    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        error: 'Bad Request',
        message: errorMessage
      });
    }
    
    // Replace request body with validated value
    req.body = value;
    next();
  };
};

module.exports = {
  validateRequest
};
