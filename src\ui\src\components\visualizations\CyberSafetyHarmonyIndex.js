import React, { useRef, useEffect, useState } from 'react';
import { Box, CircularProgress, Typography, Paper, Grid } from '@mui/material';
import { Chart as ChartJS, RadialLinearScale, PointElement, LineElement, Filler, Tooltip, Legend } from 'chart.js';
import { Radar } from 'react-chartjs-2';
import { styled } from '@mui/material/styles';

// Register ChartJS components
ChartJS.register(RadialLinearScale, PointElement, LineElement, Filler, Tooltip, Legend);

// Styled components
const HarmonyScoreBox = styled(Paper)(({ theme, score }) => ({
  padding: theme.spacing(2),
  textAlign: 'center',
  backgroundColor: score >= 0.8 ? '#e8f5e9' : score >= 0.6 ? '#fff9c4' : '#ffebee',
  borderRadius: '8px',
  boxShadow: theme.shadows[3],
  transition: 'all 0.3s ease'
}));

const TrendIndicator = styled(Box)(({ theme, trend }) => ({
  display: 'inline-flex',
  alignItems: 'center',
  color: trend === 'improving' ? '#4caf50' : trend === 'declining' ? '#f44336' : '#757575',
  marginLeft: theme.spacing(1)
}));

/**
 * CyberSafetyHarmonyIndex component
 * 
 * Creates a dashboard visualization that shows the alignment and harmony 
 * between the three domains (GRC, IT, Cybersecurity) using a radar chart
 * and harmony score calculated using the 3-6-9-12-13 pattern.
 */
function CyberSafetyHarmonyIndex({
  domainData = {
    grc: { score: 0.7, metrics: { governance: 0.6, risk: 0.7, compliance: 0.8 } },
    it: { score: 0.8, metrics: { infrastructure: 0.8, applications: 0.7, data: 0.9 } },
    cybersecurity: { score: 0.6, metrics: { prevention: 0.5, detection: 0.6, response: 0.7 } }
  },
  harmonyHistory = [], // Array of historical harmony scores for trend analysis
  options = {
    showTrend: true,
    showDetails: true,
    colorScheme: 'default',
    enableAnimation: true
  },
  width = '100%',
  height = '100%'
}) {
  // State for loading and error handling
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // State for calculated values
  const [harmonyScore, setHarmonyScore] = useState(0);
  const [harmonyTrend, setHarmonyTrend] = useState('stable');
  const [resonancePattern, setResonancePattern] = useState([]);
  
  // Calculate harmony score and trend
  useEffect(() => {
    try {
      // Calculate domain average scores
      const grcScore = domainData.grc.score;
      const itScore = domainData.it.score;
      const cyberScore = domainData.cybersecurity.score;
      
      // Calculate harmony using the 3-6-9-12-13 pattern
      // The closer the scores are to the pattern, the higher the harmony
      
      // Step 1: Calculate the balance between domains (should be close to 0.3, 0.6, 0.9)
      const sortedScores = [grcScore, itScore, cyberScore].sort((a, b) => a - b);
      
      // Calculate how close each score is to the ideal pattern
      const patternFit = [
        1 - Math.abs(sortedScores[0] - 0.3) / 0.3,
        1 - Math.abs(sortedScores[1] - 0.6) / 0.6,
        1 - Math.abs(sortedScores[2] - 0.9) / 0.9
      ];
      
      // Calculate average pattern fit
      const patternFitScore = patternFit.reduce((sum, val) => sum + val, 0) / 3;
      
      // Step 2: Calculate the cross-domain coherence
      // Ideal differences between domains should follow the 3-6-9 pattern
      const diffs = [
        Math.abs(grcScore - itScore),
        Math.abs(itScore - cyberScore),
        Math.abs(grcScore - cyberScore)
      ];
      
      // Calculate how close the differences are to the ideal pattern (0.3)
      const diffFit = diffs.map(diff => 1 - Math.abs(diff - 0.3) / 0.3);
      const diffFitScore = diffFit.reduce((sum, val) => sum + val, 0) / 3;
      
      // Step 3: Calculate overall harmony score (weighted average)
      const calculatedHarmonyScore = (patternFitScore * 0.6) + (diffFitScore * 0.4);
      setHarmonyScore(calculatedHarmonyScore);
      
      // Calculate trend if history is available
      if (harmonyHistory.length > 1) {
        const prevScore = harmonyHistory[harmonyHistory.length - 1];
        const diff = calculatedHarmonyScore - prevScore;
        
        if (diff > 0.05) {
          setHarmonyTrend('improving');
        } else if (diff < -0.05) {
          setHarmonyTrend('declining');
        } else {
          setHarmonyTrend('stable');
        }
      }
      
      // Calculate resonance pattern
      const newResonancePattern = [
        { name: '3', value: patternFit[0] },
        { name: '6', value: patternFit[1] },
        { name: '9', value: patternFit[2] },
        { name: '12', value: diffFit[0] },
        { name: '13', value: (diffFit[1] + diffFit[2]) / 2 }
      ];
      setResonancePattern(newResonancePattern);
      
      setIsLoading(false);
    } catch (err) {
      console.error('Error calculating harmony index:', err);
      setError(err.message || 'Error calculating harmony index');
      setIsLoading(false);
    }
  }, [domainData, harmonyHistory]);
  
  // Prepare chart data
  const chartData = {
    labels: [
      'GRC: Governance', 
      'GRC: Risk', 
      'GRC: Compliance',
      'IT: Infrastructure',
      'IT: Applications',
      'IT: Data',
      'Cyber: Prevention',
      'Cyber: Detection',
      'Cyber: Response'
    ],
    datasets: [
      {
        label: 'Current State',
        data: [
          domainData.grc.metrics.governance,
          domainData.grc.metrics.risk,
          domainData.grc.metrics.compliance,
          domainData.it.metrics.infrastructure,
          domainData.it.metrics.applications,
          domainData.it.metrics.data,
          domainData.cybersecurity.metrics.prevention,
          domainData.cybersecurity.metrics.detection,
          domainData.cybersecurity.metrics.response
        ],
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgb(54, 162, 235)',
        borderWidth: 2,
        pointBackgroundColor: 'rgb(54, 162, 235)',
        pointRadius: 4
      },
      {
        label: 'Ideal State',
        data: [0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9],
        backgroundColor: 'rgba(255, 99, 132, 0.0)',
        borderColor: 'rgba(255, 99, 132, 0.5)',
        borderWidth: 1,
        borderDash: [5, 5],
        pointRadius: 0
      }
    ]
  };
  
  // Chart options
  const chartOptions = {
    scales: {
      r: {
        angleLines: {
          display: true
        },
        suggestedMin: 0,
        suggestedMax: 1
      }
    },
    plugins: {
      legend: {
        position: 'bottom'
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${context.raw.toFixed(2)}`;
          }
        }
      }
    },
    animation: options.enableAnimation ? {
      duration: 2000,
      easing: 'easeOutQuart'
    } : false
  };
  
  // Helper function to get harmony status text
  const getHarmonyStatusText = (score) => {
    if (score >= 0.8) return 'Excellent Harmony';
    if (score >= 0.6) return 'Good Harmony';
    if (score >= 0.4) return 'Moderate Dissonance';
    return 'Significant Dissonance';
  };
  
  // Helper function to get trend icon
  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'improving':
        return '↑';
      case 'declining':
        return '↓';
      default:
        return '→';
    }
  };

  return (
    <Box
      sx={{
        width,
        height,
        position: 'relative',
        p: 2
      }}
    >
      {isLoading ? (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%'
          }}
        >
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: 'error.main'
          }}
        >
          <Typography variant="body1" color="error">
            Error: {error}
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={2}>
          {/* Harmony Score */}
          <Grid item xs={12} md={4}>
            <HarmonyScoreBox score={harmonyScore}>
              <Typography variant="h6" gutterBottom>
                Cyber-Safety Harmony Index
              </Typography>
              <Typography variant="h3" component="div" sx={{ mb: 1 }}>
                {(harmonyScore * 100).toFixed(0)}%
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {getHarmonyStatusText(harmonyScore)}
                {options.showTrend && (
                  <TrendIndicator trend={harmonyTrend}>
                    {getTrendIcon(harmonyTrend)} {harmonyTrend.charAt(0).toUpperCase() + harmonyTrend.slice(1)}
                  </TrendIndicator>
                )}
              </Typography>
              
              {options.showDetails && (
                <Box sx={{ mt: 2, textAlign: 'left' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Domain Scores:
                  </Typography>
                  <Typography variant="body2">
                    GRC: {(domainData.grc.score * 100).toFixed(0)}%
                  </Typography>
                  <Typography variant="body2">
                    IT: {(domainData.it.score * 100).toFixed(0)}%
                  </Typography>
                  <Typography variant="body2">
                    Cybersecurity: {(domainData.cybersecurity.score * 100).toFixed(0)}%
                  </Typography>
                </Box>
              )}
              
              {options.showDetails && resonancePattern.length > 0 && (
                <Box sx={{ mt: 2, textAlign: 'left' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    3-6-9-12-13 Resonance:
                  </Typography>
                  {resonancePattern.map((item, index) => (
                    <Typography key={index} variant="body2">
                      {item.name}: {(item.value * 100).toFixed(0)}%
                    </Typography>
                  ))}
                </Box>
              )}
            </HarmonyScoreBox>
          </Grid>
          
          {/* Radar Chart */}
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 2, height: '100%', boxShadow: 3 }}>
              <Typography variant="h6" gutterBottom>
                Domain Balance Analysis
              </Typography>
              <Box sx={{ height: 'calc(100% - 40px)' }}>
                <Radar data={chartData} options={chartOptions} />
              </Box>
            </Paper>
          </Grid>
        </Grid>
      )}
    </Box>
  );
}

export default CyberSafetyHarmonyIndex;
