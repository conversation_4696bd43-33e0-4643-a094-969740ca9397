/**
 * Fast Fourier Transform (FFT) Utility
 * 
 * This module implements a Fast Fourier Transform utility for converting
 * time-domain signals to frequency-domain spectra. It is used by the
 * Resonance Listener to analyze the frequency components of system oscillations.
 */

/**
 * FFT class
 */
class FFT {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      // Sampling options
      sampleRate: 1000, // Hz
      
      // Frequency analysis options
      minFrequency: 0.1, // Hz
      maxFrequency: 100, // Hz
      frequencyResolution: 0.1, // Hz
      
      // Window function options
      windowFunction: 'hann', // 'none', 'hann', 'hamming', 'blackman'
      
      ...options
    };
  }
  
  /**
   * Transform time-domain signal to frequency-domain spectrum
   * @param {Array} signal - Time-domain signal
   * @returns {Object} - Frequency-domain spectrum
   */
  transform(signal) {
    // Apply window function
    const windowedSignal = this._applyWindow(signal);
    
    // Pad signal to power of 2 length for FFT
    const paddedSignal = this._padSignal(windowedSignal);
    
    // Compute FFT
    const fftResult = this._computeFFT(paddedSignal);
    
    // Extract frequencies and amplitudes
    const { frequencies, amplitudes } = this._extractFrequencyData(fftResult);
    
    return {
      frequencies,
      amplitudes,
      signal,
      windowedSignal,
      fftResult
    };
  }
  
  /**
   * Apply window function to signal
   * @param {Array} signal - Time-domain signal
   * @returns {Array} - Windowed signal
   * @private
   */
  _applyWindow(signal) {
    const N = signal.length;
    const windowedSignal = new Array(N);
    
    switch (this.options.windowFunction) {
      case 'hann':
        // Hann window: w(n) = 0.5 * (1 - cos(2π*n/(N-1)))
        for (let n = 0; n < N; n++) {
          const window = 0.5 * (1 - Math.cos(2 * Math.PI * n / (N - 1)));
          windowedSignal[n] = signal[n] * window;
        }
        break;
        
      case 'hamming':
        // Hamming window: w(n) = 0.54 - 0.46 * cos(2π*n/(N-1))
        for (let n = 0; n < N; n++) {
          const window = 0.54 - 0.46 * Math.cos(2 * Math.PI * n / (N - 1));
          windowedSignal[n] = signal[n] * window;
        }
        break;
        
      case 'blackman':
        // Blackman window: w(n) = 0.42 - 0.5 * cos(2π*n/(N-1)) + 0.08 * cos(4π*n/(N-1))
        for (let n = 0; n < N; n++) {
          const window = 0.42 - 0.5 * Math.cos(2 * Math.PI * n / (N - 1)) + 0.08 * Math.cos(4 * Math.PI * n / (N - 1));
          windowedSignal[n] = signal[n] * window;
        }
        break;
        
      case 'none':
      default:
        // No window function
        for (let n = 0; n < N; n++) {
          windowedSignal[n] = signal[n];
        }
        break;
    }
    
    return windowedSignal;
  }
  
  /**
   * Pad signal to power of 2 length for FFT
   * @param {Array} signal - Time-domain signal
   * @returns {Array} - Padded signal
   * @private
   */
  _padSignal(signal) {
    const N = signal.length;
    
    // Find next power of 2
    const nextPow2 = Math.pow(2, Math.ceil(Math.log2(N)));
    
    // Pad signal with zeros
    const paddedSignal = new Array(nextPow2).fill(0);
    for (let i = 0; i < N; i++) {
      paddedSignal[i] = signal[i];
    }
    
    return paddedSignal;
  }
  
  /**
   * Compute FFT of signal
   * @param {Array} signal - Time-domain signal
   * @returns {Array} - Complex FFT result
   * @private
   */
  _computeFFT(signal) {
    const N = signal.length;
    
    // Base case
    if (N === 1) {
      return [{ real: signal[0], imag: 0 }];
    }
    
    // Check if N is a power of 2
    if (N & (N - 1)) {
      throw new Error('FFT length must be a power of 2');
    }
    
    // Split signal into even and odd indices
    const even = new Array(N / 2);
    const odd = new Array(N / 2);
    
    for (let i = 0; i < N / 2; i++) {
      even[i] = signal[2 * i];
      odd[i] = signal[2 * i + 1];
    }
    
    // Recursively compute FFT of even and odd parts
    const evenFFT = this._computeFFT(even);
    const oddFFT = this._computeFFT(odd);
    
    // Combine results
    const result = new Array(N);
    
    for (let k = 0; k < N / 2; k++) {
      // Complex exponential factor
      const theta = -2 * Math.PI * k / N;
      const re = Math.cos(theta);
      const im = Math.sin(theta);
      
      // Complex multiplication: oddFFT[k] * e^(-2πi*k/N)
      const oddReal = oddFFT[k].real * re - oddFFT[k].imag * im;
      const oddImag = oddFFT[k].real * im + oddFFT[k].imag * re;
      
      // First half of result
      result[k] = {
        real: evenFFT[k].real + oddReal,
        imag: evenFFT[k].imag + oddImag
      };
      
      // Second half of result
      result[k + N / 2] = {
        real: evenFFT[k].real - oddReal,
        imag: evenFFT[k].imag - oddImag
      };
    }
    
    return result;
  }
  
  /**
   * Extract frequency data from FFT result
   * @param {Array} fftResult - Complex FFT result
   * @returns {Object} - Frequencies and amplitudes
   * @private
   */
  _extractFrequencyData(fftResult) {
    const N = fftResult.length;
    const sampleRate = this.options.sampleRate;
    
    // Calculate frequency resolution
    const df = sampleRate / N;
    
    // Initialize arrays
    const frequencies = [];
    const amplitudes = [];
    
    // Extract frequencies and amplitudes
    for (let i = 0; i < N / 2; i++) {
      const frequency = i * df;
      
      // Skip frequencies outside the specified range
      if (frequency < this.options.minFrequency || frequency > this.options.maxFrequency) {
        continue;
      }
      
      // Calculate amplitude (magnitude of complex number)
      const real = fftResult[i].real;
      const imag = fftResult[i].imag;
      const amplitude = Math.sqrt(real * real + imag * imag) / (N / 2);
      
      frequencies.push(frequency);
      amplitudes.push(amplitude);
    }
    
    return { frequencies, amplitudes };
  }
  
  /**
   * Inverse FFT to convert frequency-domain spectrum back to time-domain signal
   * @param {Array} fftResult - Complex FFT result
   * @returns {Array} - Time-domain signal
   */
  inverse(fftResult) {
    const N = fftResult.length;
    
    // Create conjugate of FFT result
    const conjugate = new Array(N);
    for (let i = 0; i < N; i++) {
      conjugate[i] = {
        real: fftResult[i].real,
        imag: -fftResult[i].imag
      };
    }
    
    // Compute FFT of conjugate
    const result = this._computeFFT(conjugate);
    
    // Extract real part and normalize
    const signal = new Array(N);
    for (let i = 0; i < N; i++) {
      signal[i] = result[i].real / N;
    }
    
    return signal;
  }
  
  /**
   * Generate a test signal
   * @param {number} duration - Duration in seconds
   * @param {Array} frequencies - Array of frequencies to include
   * @param {Array} amplitudes - Array of amplitudes for each frequency
   * @returns {Array} - Test signal
   */
  generateTestSignal(duration, frequencies, amplitudes) {
    const N = Math.floor(duration * this.options.sampleRate);
    const signal = new Array(N).fill(0);
    
    // Generate signal
    for (let i = 0; i < N; i++) {
      const t = i / this.options.sampleRate;
      
      for (let j = 0; j < frequencies.length; j++) {
        signal[i] += amplitudes[j] * Math.sin(2 * Math.PI * frequencies[j] * t);
      }
    }
    
    return signal;
  }
}

module.exports = FFT;
