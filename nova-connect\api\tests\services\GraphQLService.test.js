/**
 * GraphQL Service Tests
 */

const GraphQLService = require('../../services/GraphQLService');
const axios = require('axios');

// Mock axios
jest.mock('axios');

describe('GraphQLService', () => {
  let graphqlService;
  
  beforeEach(() => {
    graphqlService = new GraphQLService();
    jest.clearAllMocks();
  });
  
  describe('executeQuery', () => {
    it('should execute a GraphQL query successfully', async () => {
      // Mock axios response
      axios.mockResolvedValueOnce({
        data: {
          data: {
            user: {
              id: '123',
              name: 'Test User'
            }
          }
        },
        status: 200,
        statusText: 'OK',
        headers: {
          'content-type': 'application/json'
        }
      });
      
      const endpoint = 'https://api.example.com/graphql';
      const query = `
        query {
          user(id: "123") {
            id
            name
          }
        }
      `;
      
      const result = await graphqlService.executeQuery(endpoint, query);
      
      // Check that axios was called correctly
      expect(axios).toHaveBeenCalledWith({
        url: endpoint,
        method: 'POST',
        headers: {},
        data: {
          query,
          variables: {}
        }
      });
      
      // Check the result
      expect(result.data).toEqual({
        data: {
          user: {
            id: '123',
            name: 'Test User'
          }
        }
      });
      expect(result.status).toBe(200);
      expect(result.statusText).toBe('OK');
      expect(result.headers).toEqual({
        'content-type': 'application/json'
      });
      expect(result.responseTime).toBeGreaterThanOrEqual(0);
    });
    
    it('should handle GraphQL errors', async () => {
      // Mock axios response with GraphQL error
      axios.mockResolvedValueOnce({
        data: {
          errors: [
            {
              message: 'Field "userr" does not exist on type "Query"',
              locations: [{ line: 2, column: 3 }],
              path: ['userr']
            }
          ],
          data: null
        },
        status: 200,
        statusText: 'OK',
        headers: {
          'content-type': 'application/json'
        }
      });
      
      const endpoint = 'https://api.example.com/graphql';
      const query = `
        query {
          userr(id: "123") {
            id
            name
          }
        }
      `;
      
      const result = await graphqlService.executeQuery(endpoint, query);
      
      // Check the result
      expect(result.data).toEqual({
        errors: [
          {
            message: 'Field "userr" does not exist on type "Query"',
            locations: [{ line: 2, column: 3 }],
            path: ['userr']
          }
        ],
        data: null
      });
      expect(result.status).toBe(200);
    });
    
    it('should handle network errors', async () => {
      // Mock axios rejection
      axios.mockRejectedValueOnce({
        request: {},
        message: 'Network Error'
      });
      
      const endpoint = 'https://api.example.com/graphql';
      const query = `
        query {
          user(id: "123") {
            id
            name
          }
        }
      `;
      
      await expect(graphqlService.executeQuery(endpoint, query)).rejects.toThrow('Network Error');
    });
    
    it('should handle server errors', async () => {
      // Mock axios response with server error
      axios.mockResolvedValueOnce({
        data: {
          errors: [
            {
              message: 'Internal server error',
              extensions: {
                code: 'INTERNAL_SERVER_ERROR'
              }
            }
          ],
          data: null
        },
        status: 500,
        statusText: 'Internal Server Error',
        headers: {
          'content-type': 'application/json'
        }
      });
      
      const endpoint = 'https://api.example.com/graphql';
      const query = `
        query {
          user(id: "123") {
            id
            name
          }
        }
      `;
      
      const result = await graphqlService.executeQuery(endpoint, query);
      
      // Check the result
      expect(result.status).toBe(500);
      expect(result.data.errors[0].message).toBe('Internal server error');
    });
  });
  
  describe('validateQuery', () => {
    it('should validate a valid query', () => {
      const query = `
        query {
          user(id: "123") {
            id
            name
          }
        }
      `;
      
      const schema = { types: [] }; // Mock schema
      
      const result = graphqlService.validateQuery(query, schema);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toEqual([]);
    });
    
    it('should detect syntax errors in a query', () => {
      const query = `
        query {
          user(id: "123") {
            id
            name
          }
        
      `; // Missing closing brace
      
      const schema = { types: [] }; // Mock schema
      
      const result = graphqlService.validateQuery(query, schema);
      
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toContain('Mismatched curly braces');
    });
  });
  
  describe('generateSampleQuery', () => {
    it('should generate a sample query based on schema', () => {
      const schema = {
        queryType: { name: 'Query' },
        types: [
          {
            name: 'Query',
            kind: 'OBJECT',
            fields: [
              {
                name: 'user',
                args: [
                  {
                    name: 'id',
                    type: { name: 'ID' }
                  }
                ],
                type: { name: 'User', kind: 'OBJECT' }
              }
            ]
          },
          {
            name: 'User',
            kind: 'OBJECT',
            fields: [
              { name: 'id', type: { name: 'ID' } },
              { name: 'name', type: { name: 'String' } },
              { name: 'email', type: { name: 'String' } }
            ]
          }
        ]
      };
      
      const sampleQuery = graphqlService.generateSampleQuery(schema, 'query');
      
      expect(sampleQuery).toContain('query {');
      expect(sampleQuery).toContain('user');
      expect(sampleQuery).toContain('id:');
    });
  });
});
