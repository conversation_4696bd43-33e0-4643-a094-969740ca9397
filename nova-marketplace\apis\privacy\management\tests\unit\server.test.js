/**
 * Server Tests
 *
 * This file contains unit tests for the server.js file.
 */

// Mock dependencies
jest.mock('express', () => {
  const mockApp = {
    use: jest.fn(),
    listen: jest.fn().mockReturnValue({
      close: jest.fn()
    })
  };
  const mockExpress = jest.fn().mockReturnValue(mockApp);
  mockExpress.json = jest.fn().mockReturnValue('json-middleware');
  mockExpress.urlencoded = jest.fn().mockReturnValue('urlencoded-middleware');
  return mockExpress;
});

jest.mock('body-parser', () => ({
  json: jest.fn().mockReturnValue('body-parser-json'),
  urlencoded: jest.fn().mockReturnValue('body-parser-urlencoded')
}));

jest.mock('cors', () => jest.fn().mockReturnValue('cors-middleware'));

jest.mock('../../config/database', () => ({
  connect: jest.fn().mockResolvedValue()
}));

jest.mock('../../config/logger', () => ({
  info: jest.fn(),
  error: jest.fn()
}));

jest.mock('../../middleware/logger', () => ({
  httpLogger: jest.fn().mockReturnValue('http-logger-middleware'),
  requestLogger: jest.fn().mockReturnValue('request-logger-middleware'),
  responseLogger: jest.fn().mockReturnValue('response-logger-middleware'),
  errorLogger: jest.fn().mockReturnValue('error-logger-middleware')
}));

jest.mock('../../config/swagger', () => ({
  setupSwagger: jest.fn()
}));

jest.mock('../../index', () => 'privacy-management-api');

// Mock process.exit
const originalExit = process.exit;
process.exit = jest.fn();

// Mock process.on
const originalOn = process.on;
process.on = jest.fn();

describe('Server', () => {
  let server;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();

    // Reset environment variables
    process.env.NODE_ENV = 'test';
    process.env.PORT = '3000';
  });

  afterAll(() => {
    process.exit = originalExit;
    process.on = originalOn;
  });

  describe('Middleware Setup', () => {
    it('should create an Express app', () => {
      // Import the server module
      server = require('../../server');

      // Verify server exports an app
      expect(server.app).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should set up error handling for unhandled routes', () => {
      // Import the server module
      server = require('../../server');

      // Create test objects
      const req = { originalUrl: '/not-found' };
      const res = {};
      const next = jest.fn();

      // Find the 404 handler middleware
      const express = require('express');
      const app = express();
      const notFoundHandlerCall = app.use.mock.calls.find(call =>
        typeof call[0] === 'function' && call[0].toString().includes('Not Found')
      );

      // Execute the middleware if found
      if (notFoundHandlerCall) {
        const notFoundHandler = notFoundHandlerCall[0];
        notFoundHandler(req, res, next);

        // Verify next was called with a 404 error
        expect(next).toHaveBeenCalledWith(expect.objectContaining({
          status: 404,
          message: expect.stringContaining('/not-found')
        }));
      } else {
        // If middleware not found, fail the test
        fail('404 handler middleware not found');
      }
    });

    it('should set up global error handler', () => {
      // Import the server module
      server = require('../../server');

      // Create test objects
      const err = new Error('Test error');
      const req = {};
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      // Find the error handler middleware (4 parameters)
      const express = require('express');
      const app = express();
      const errorHandlerCall = app.use.mock.calls.find(call =>
        typeof call[0] === 'function' && call[0].length === 4
      );

      // Execute the middleware if found
      if (errorHandlerCall) {
        const errorHandler = errorHandlerCall[0];

        // Test in development mode
        process.env.NODE_ENV = 'development';
        errorHandler(err, req, res, next);

        expect(res.status).toHaveBeenCalledWith(500);
        expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
          error: expect.objectContaining({
            message: 'Test error',
            status: 500,
            stack: expect.any(String)
          })
        }));

        // Reset mocks
        jest.clearAllMocks();

        // Test with custom status
        const customErr = new Error('Custom error');
        customErr.status = 400;

        errorHandler(customErr, req, res, next);

        expect(res.status).toHaveBeenCalledWith(400);

        // Reset mocks
        jest.clearAllMocks();

        // Test in production mode
        process.env.NODE_ENV = 'production';
        errorHandler(err, req, res, next);

        expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
          error: expect.objectContaining({
            stack: undefined
          })
        }));
      } else {
        // If middleware not found, fail the test
        fail('Error handler middleware not found');
      }
    });
  });

  describe('Process Event Handlers', () => {
    it('should set up handlers for unhandled rejections and exceptions', () => {
      // Import the server module
      server = require('../../server');

      // Verify process event handlers were registered
      expect(process.on).toHaveBeenCalledWith('unhandledRejection', expect.any(Function));
      expect(process.on).toHaveBeenCalledWith('uncaughtException', expect.any(Function));

      // Get the handlers
      const unhandledRejectionCall = process.on.mock.calls.find(call => call[0] === 'unhandledRejection');
      const uncaughtExceptionCall = process.on.mock.calls.find(call => call[0] === 'uncaughtException');

      // Test unhandledRejection handler
      if (unhandledRejectionCall) {
        const rejectionError = new Error('Unhandled rejection');
        unhandledRejectionCall[1](rejectionError);

        expect(process.exit).toHaveBeenCalledWith(1);
        const logger = require('../../config/logger');
        expect(logger.error).toHaveBeenCalled();
      } else {
        fail('Unhandled rejection handler not found');
      }

      // Reset mocks
      jest.clearAllMocks();

      // Test uncaughtException handler
      if (uncaughtExceptionCall) {
        const exceptionError = new Error('Uncaught exception');
        uncaughtExceptionCall[1](exceptionError);

        expect(process.exit).toHaveBeenCalledWith(1);
        const logger = require('../../config/logger');
        expect(logger.error).toHaveBeenCalled();
      } else {
        fail('Uncaught exception handler not found');
      }
    });
  });

  describe('Server Startup', () => {
    it('should use the PORT environment variable', async () => {
      // Set PORT environment variable
      process.env.PORT = '4000';

      // Import the server module
      server = require('../../server');

      // Call startServer
      await server.startServer();

      // Verify server was started with the correct port
      const express = require('express');
      const app = express();
      expect(app.listen).toHaveBeenCalledWith('4000', expect.any(Function));

      // Call the listen callback
      const listenCallback = app.listen.mock.calls[0][1];
      if (listenCallback) {
        listenCallback();

        // Verify logger was called
        const logger = require('../../config/logger');
        expect(logger.info).toHaveBeenCalled();
      }
    });

    it('should use default port 3000 if PORT is not set', async () => {
      // Unset PORT environment variable
      delete process.env.PORT;

      // Import the server module
      server = require('../../server');

      // Call startServer
      await server.startServer();

      // Verify server was started with the default port
      const express = require('express');
      const app = express();
      expect(app.listen).toHaveBeenCalledWith(3000, expect.any(Function));
    });

    it('should handle database connection errors', async () => {
      // Mock database connection failure
      const { connect } = require('../../config/database');
      connect.mockRejectedValueOnce(new Error('Database connection failed'));

      // Import the server module
      server = require('../../server');

      // Call startServer
      await server.startServer();

      // Verify error was logged and process.exit was called
      const logger = require('../../config/logger');
      expect(logger.error).toHaveBeenCalled();
      expect(process.exit).toHaveBeenCalledWith(1);
    });
  });
});
