/**
 * NovaVision Hub Component
 * 
 * This component demonstrates how to use the NovaVision Hub to integrate all Nova components.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import NovaVision from '@novafuse/uui-core';
import { NovaVisionBridge } from '../novavision-integration';
import NovaVisionHub from './NovaVisionHub';

/**
 * NovaVision Hub component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaCore - NovaCore instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} props.novaGraph - NovaGraph instance
 * @param {Object} props.novaDNA - NovaDNA instance
 * @param {Object} props.novaPulse - NovaPulse instance
 * @param {Object} props.novaThink - NovaThink instance
 * @param {Object} props.novaFlowX - NovaFlowX instance
 * @param {Object} props.novaProof - NovaProof instance
 * @param {Object} props.novaStore - NovaStore instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} NovaVision Hub component
 */
const NovaVisionHubComponent = ({
  novaConnect,
  novaCore,
  novaShield,
  novaTrack,
  novaGraph,
  novaDNA,
  novaPulse,
  novaThink,
  novaFlowX,
  novaProof,
  novaStore,
  enableLogging = false
}) => {
  const [novaVision, setNovaVision] = useState(null);
  const [hub, setHub] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState(null);
  const [schema, setSchema] = useState(null);
  const [activeComponent, setActiveComponent] = useState('dashboard');
  const [activeSchemaType, setActiveSchemaType] = useState('dashboard');
  
  // Initialize NovaVision
  useEffect(() => {
    try {
      // Create NovaVision instance
      const novaVisionInstance = new NovaVision({
        theme: 'default',
        responsive: true,
        accessibilityLevel: 'AA',
        regulationAware: true,
        aiOptimization: true,
        consistencyEnforcement: true
      });
      
      setNovaVision(novaVisionInstance);
    } catch (err) {
      console.error('Error initializing NovaVision', err);
      setError(err);
    }
  }, []);
  
  // Initialize NovaVision Hub
  useEffect(() => {
    if (!novaVision) {
      return;
    }
    
    const initializeHub = async () => {
      try {
        if (enableLogging) {
          console.log('Initializing NovaVision Hub...');
        }
        
        // Create hub instance
        const hubInstance = new NovaVisionHub({
          novaVision,
          novaConnect,
          novaCore,
          novaShield,
          novaTrack,
          novaGraph,
          novaDNA,
          novaPulse,
          novaThink,
          novaFlowX,
          novaProof,
          novaStore,
          enableLogging
        });
        
        // Initialize hub
        await hubInstance.initialize();
        
        setHub(hubInstance);
        setIsInitialized(true);
        
        if (enableLogging) {
          console.log('NovaVision Hub initialized successfully');
        }
      } catch (err) {
        console.error('Error initializing NovaVision Hub', err);
        setError(err);
      }
    };
    
    initializeHub();
  }, [
    novaVision,
    novaConnect,
    novaCore,
    novaShield,
    novaTrack,
    novaGraph,
    novaDNA,
    novaPulse,
    novaThink,
    novaFlowX,
    novaProof,
    novaStore,
    enableLogging
  ]);
  
  // Load schema when active component or schema type changes
  useEffect(() => {
    if (!isInitialized) {
      return;
    }
    
    const loadSchema = async () => {
      try {
        if (enableLogging) {
          console.log(`Loading schema for ${activeComponent}.${activeSchemaType}...`);
        }
        
        let newSchema;
        
        if (activeComponent === 'dashboard') {
          // Load integrated dashboard schema
          newSchema = await hub.getIntegratedDashboardSchema();
        } else {
          // Load component-specific schema
          newSchema = await hub.getUISchema(activeComponent, activeSchemaType);
        }
        
        setSchema(newSchema);
        
        if (enableLogging) {
          console.log(`Schema loaded for ${activeComponent}.${activeSchemaType}`);
        }
      } catch (err) {
        console.error(`Error loading schema for ${activeComponent}.${activeSchemaType}`, err);
        setError(err);
      }
    };
    
    loadSchema();
  }, [hub, isInitialized, activeComponent, activeSchemaType, enableLogging]);
  
  // Handle actions
  const handleAction = async (action, data) => {
    if (!isInitialized) {
      return;
    }
    
    try {
      if (enableLogging) {
        console.log(`Handling action ${action}...`, data);
      }
      
      // Handle navigation actions
      if (action.startsWith('navigate.')) {
        const [, component, schemaType] = action.split('.');
        
        setActiveComponent(component);
        setActiveSchemaType(schemaType || 'dashboard');
        
        return;
      }
      
      // Handle component-specific actions
      await hub.handleAction(action, data);
    } catch (err) {
      console.error(`Error handling action ${action}`, err);
      setError(err);
    }
  };
  
  if (error) {
    return (
      <div className="error">
        <h3>Error</h3>
        <p>{error.message}</p>
      </div>
    );
  }
  
  if (!isInitialized) {
    return (
      <div className="loading">
        <p>Initializing NovaVision Hub...</p>
      </div>
    );
  }
  
  return (
    <div className="nova-vision-hub">
      <header className="nova-vision-hub__header">
        <h1>NovaFuse Platform</h1>
        <nav className="nova-vision-hub__nav">
          <button
            className={`nova-vision-hub__nav-item ${activeComponent === 'dashboard' ? 'active' : ''}`}
            onClick={() => {
              setActiveComponent('dashboard');
              setActiveSchemaType('dashboard');
            }}
          >
            Dashboard
          </button>
          {novaConnect && (
            <button
              className={`nova-vision-hub__nav-item ${activeComponent === 'novaConnect' ? 'active' : ''}`}
              onClick={() => {
                setActiveComponent('novaConnect');
                setActiveSchemaType('dashboard');
              }}
            >
              NovaConnect
            </button>
          )}
          {novaCore && (
            <button
              className={`nova-vision-hub__nav-item ${activeComponent === 'novaCore' ? 'active' : ''}`}
              onClick={() => {
                setActiveComponent('novaCore');
                setActiveSchemaType('dashboard');
              }}
            >
              NovaCore
            </button>
          )}
          {novaShield && (
            <button
              className={`nova-vision-hub__nav-item ${activeComponent === 'novaShield' ? 'active' : ''}`}
              onClick={() => {
                setActiveComponent('novaShield');
                setActiveSchemaType('dashboard');
              }}
            >
              NovaShield
            </button>
          )}
          {novaTrack && (
            <button
              className={`nova-vision-hub__nav-item ${activeComponent === 'novaTrack' ? 'active' : ''}`}
              onClick={() => {
                setActiveComponent('novaTrack');
                setActiveSchemaType('dashboard');
              }}
            >
              NovaTrack
            </button>
          )}
          {/* Add more navigation items for other Nova components */}
        </nav>
      </header>
      
      <main className="nova-vision-hub__content">
        {schema ? (
          <NovaVisionBridge
            novaVision={novaVision}
            schema={schema}
            onAction={handleAction}
            enableLogging={enableLogging}
          />
        ) : (
          <div className="loading">
            <p>Loading schema...</p>
          </div>
        )}
      </main>
    </div>
  );
};

NovaVisionHubComponent.propTypes = {
  novaConnect: PropTypes.object,
  novaCore: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  novaGraph: PropTypes.object,
  novaDNA: PropTypes.object,
  novaPulse: PropTypes.object,
  novaThink: PropTypes.object,
  novaFlowX: PropTypes.object,
  novaProof: PropTypes.object,
  novaStore: PropTypes.object,
  enableLogging: PropTypes.bool
};

export default NovaVisionHubComponent;
