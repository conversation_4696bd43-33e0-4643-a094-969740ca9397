{"frameworks": {"gdpr": {"id": "520d0188-ca64-49d4-9c96-d5af3340352a", "name": "GDPR", "controls": 3}, "soc2": {"id": "6c0c0f15-7b5a-41a3-a5ea-b8453aaf574e", "name": "SOC 2", "controls": 3}, "hipaa": {"id": "c90a346f-9962-4f65-8803-fb41bbf8d0a6", "name": "HIPAA", "controls": 3}}, "mappings": {"total": 12, "gdpr_to_soc2": 2, "soc2_to_hipaa": 3, "gdpr_to_hipaa": 1}, "coverage": {"gdpr_to_soc2": {"source_framework_id": "520d0188-ca64-49d4-9c96-d5af3340352a", "target_framework_id": "6c0c0f15-7b5a-41a3-a5ea-b8453aaf574e", "source_controls": 3, "target_controls": 3, "mapped_source_controls": 2, "mapped_target_controls": 2, "source_coverage": 0.6666666666666666, "target_coverage": 0.6666666666666666, "mappings": 2}, "soc2_to_hipaa": {"source_framework_id": "6c0c0f15-7b5a-41a3-a5ea-b8453aaf574e", "target_framework_id": "c90a346f-9962-4f65-8803-fb41bbf8d0a6", "source_controls": 3, "target_controls": 3, "mapped_source_controls": 3, "mapped_target_controls": 3, "source_coverage": 1.0, "target_coverage": 1.0, "mappings": 3}}}