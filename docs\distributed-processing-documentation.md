# Enhanced Distributed Processing Documentation

## Overview

The Enhanced Distributed Processing components provide advanced capabilities for the Finite Universe Principle defense system, enabling dynamic node discovery, task prioritization, and capability-based routing. These components work together to create a robust and flexible distributed processing system.

## Components

### Node Discovery

The Node Discovery component enables automatic discovery of nodes in a distributed system. It uses UDP broadcast to discover and advertise nodes, allowing for dynamic scaling and fault tolerance.

#### Features

- **Dynamic Node Discovery**: Automatically discover nodes in the network
- **Node Advertisement**: Advertise node capabilities and status
- **Heartbeat Mechanism**: Monitor node health through periodic heartbeats
- **Capability Registration**: Register node capabilities for capability-based routing

#### Usage

```javascript
const { createNodeDiscovery } = require('./finite-universe-principle');

// Create node discovery for master node
const nodeDiscovery = createNodeDiscovery({
  enableLogging: true,
  isMaster: true,
  nodeId: 'master-node',
  capabilities: ['default', 'master', 'routing']
});

// Register event listeners
nodeDiscovery.on('node-discovered', (data) => {
  console.log('Node discovered:', data.nodeId);
});

nodeDiscovery.on('node-updated', (data) => {
  console.log('Node updated:', data.nodeId);
});

nodeDiscovery.on('node-timeout', (data) => {
  console.log('Node timed out:', data.nodeId);
});

// Start node discovery
nodeDiscovery.start();

// Get discovered nodes
const nodes = nodeDiscovery.getNodes();

// Get node information
const nodeInfo = nodeDiscovery.getNodeInfo('node-id');

// Update node information
nodeDiscovery.updateNodeInfo({
  id: 'node-id',
  status: 'active',
  load: 0.5
});

// Stop node discovery
nodeDiscovery.stop();

// Dispose resources
nodeDiscovery.dispose();
```

### Priority Queue

The Priority Queue component enables task prioritization for distributed processing. It provides a priority-based queue with preemption and fairness capabilities.

#### Features

- **Task Prioritization**: Process tasks based on priority
- **Preemption**: Preempt lower-priority tasks for higher-priority tasks
- **Fairness**: Ensure fair processing of tasks across priority levels
- **Task Retry**: Retry failed tasks with configurable retry policies

#### Usage

```javascript
const { createPriorityQueue } = require('./finite-universe-principle');

// Create priority queue
const priorityQueue = createPriorityQueue({
  enableLogging: true,
  priorityLevels: 10,
  defaultPriority: 5,
  preemptionEnabled: true,
  fairnessEnabled: true,
  fairnessThreshold: 5
});

// Register event listeners
priorityQueue.on('enqueue', (data) => {
  console.log(`Task ${data.taskId} enqueued with priority ${data.priority}`);
});

priorityQueue.on('dequeue', (data) => {
  console.log(`Task ${data.taskId} dequeued with priority ${data.priority}`);
});

priorityQueue.on('complete', (data) => {
  console.log(`Task ${data.taskId} completed with result:`, data.result);
});

priorityQueue.on('preempt', (data) => {
  console.log(`Task ${data.taskId} preempted`);
});

// Enqueue a task
const taskId = priorityQueue.enqueue({ data: 'Task data' }, 2); // High priority

// Dequeue a task
const task = priorityQueue.dequeue();

// Complete a task
priorityQueue.complete(task.id, { result: 'Task result' });

// Fail a task
priorityQueue.fail(task.id, new Error('Task failed'), true); // Retry

// Preempt a task
priorityQueue.preempt(task.id);

// Check if queue is empty
const isEmpty = priorityQueue.isEmpty();

// Get queue size
const size = priorityQueue.size();

// Get queue statistics
const stats = priorityQueue.getStats();

// Dispose resources
priorityQueue.dispose();
```

### Capability Router

The Capability Router component enables capability-based routing of tasks to nodes with specific capabilities. It provides load balancing and capability matching strategies.

#### Features

- **Capability-Based Routing**: Route tasks to nodes with specific capabilities
- **Load Balancing**: Balance load across nodes with matching capabilities
- **Capability Matching**: Match task requirements with node capabilities
- **Node Registration**: Register nodes with specific capabilities

#### Usage

```javascript
const { createCapabilityRouter } = require('./finite-universe-principle');

// Create capability router
const capabilityRouter = createCapabilityRouter({
  enableLogging: true,
  defaultCapability: 'default',
  loadBalancingStrategy: 'least-loaded', // 'least-loaded', 'round-robin', 'random'
  capabilityMatchingStrategy: 'subset' // 'exact', 'subset', 'best-match'
});

// Register event listeners
capabilityRouter.on('node-registered', (data) => {
  console.log('Node registered:', data.nodeId);
});

capabilityRouter.on('task-routed', (data) => {
  console.log('Task routed:', data.taskId, 'to node:', data.nodeId);
});

// Register a node with capabilities
capabilityRouter.registerNode({
  id: 'node1',
  capabilities: ['default', 'compute', 'storage'],
  load: 0.5
});

// Route a task to a node with matching capabilities
const node = capabilityRouter.routeTask(
  { id: 'task1', data: 'Task data' },
  ['compute', 'storage']
);

// Get all nodes
const nodes = capabilityRouter.getNodes();

// Get node information
const nodeInfo = capabilityRouter.getNodeInfo('node1');

// Get all capabilities
const capabilities = capabilityRouter.getCapabilities();

// Get nodes with specific capability
const nodesWithCapability = capabilityRouter.getNodesWithCapability('compute');

// Dispose resources
capabilityRouter.dispose();
```

### Enhanced Distributed Processor

The Enhanced Distributed Processor integrates all the above components to provide a complete distributed processing solution. It enables dynamic node discovery, task prioritization, and capability-based routing.

#### Features

- **Dynamic Node Discovery**: Automatically discover nodes in the network
- **Task Prioritization**: Process tasks based on priority
- **Capability-Based Routing**: Route tasks to nodes with specific capabilities
- **Load Balancing**: Balance load across nodes
- **Fault Tolerance**: Handle node failures and task retries

#### Usage

```javascript
const { createDistributedComponents } = require('./finite-universe-principle');

// Create distributed components
const components = createDistributedComponents({
  enableLogging: true,
  isMaster: true,
  nodeId: 'master-node',
  capabilities: ['default', 'master', 'routing', 'compute', 'storage']
});

// Start distributed processor
components.distributedProcessor.start();

// Register processor functions
components.distributedProcessor.registerProcessor('compute', async (data) => {
  console.log('Computing:', data);
  return { result: data * 2 };
});

// Process data with priority and capabilities
const result = await components.distributedProcessor.process(
  5, // Data
  'compute', // Processor type
  'math', // Domain
  2, // Priority (0-9, 0 is highest)
  ['compute'] // Required capabilities
);

// Stop distributed processor
components.distributedProcessor.stop();

// Dispose resources
components.distributedProcessor.dispose();
```

## Integration with Defense System

The enhanced distributed processing components can be integrated with the Finite Universe Principle defense system to provide a complete solution for distributed processing of defense-related tasks.

```javascript
const {
  createCompleteDefenseSystem,
  createDistributedComponents
} = require('./finite-universe-principle');

// Create complete defense system
const defenseSystem = createCompleteDefenseSystem({
  enableLogging: true,
  enableMonitoring: true,
  strictMode: true
});

// Create distributed components
const components = createDistributedComponents({
  enableLogging: true,
  isMaster: true,
  nodeId: 'master-node',
  capabilities: ['default', 'master', 'routing', 'compute', 'storage']
});

// Start distributed processor
components.distributedProcessor.start();

// Register processor function that uses the defense system
components.distributedProcessor.registerProcessor('defense', async (data, domain) => {
  return await defenseSystem.processData(data, domain);
});

// Process data through the defense system
const result = await components.distributedProcessor.process(
  {
    securityScore: 8,
    threatLevel: 3,
    encryptionStrength: 256
  },
  'defense',
  'cyber',
  2, // High priority
  ['compute', 'storage'] // Required capabilities
);

// Stop distributed processor
components.distributedProcessor.stop();

// Dispose resources
components.distributedProcessor.dispose();
defenseSystem.dispose();
```

## Examples

See the `examples` directory for complete examples:

- `distributed-processing-example.js`: Demonstrates how to use the enhanced distributed processing components

## License

MIT
