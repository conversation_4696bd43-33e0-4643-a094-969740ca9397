/**
 * NovaCore NovaFlow Services Index
 * 
 * This file exports all services for the NovaFlow module.
 * NovaFlow is the Universal Compliance Workflow Orchestrator (UCWO) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const WorkflowService = require('./WorkflowService');
const WorkflowTemplateService = require('./WorkflowTemplateService');

module.exports = {
  WorkflowService,
  WorkflowTemplateService
};
