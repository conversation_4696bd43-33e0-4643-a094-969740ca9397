/**
 * NovaVisionComponents.js
 * 
 * This module defines UI components for NovaDNA using NovaVision.
 * It provides UI schemas for emergency profile management and access interfaces.
 */

/**
 * NovaVisionComponents class for NovaDNA UI components
 */
class NovaVisionComponents {
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || '/novadna';
    this.theme = options.theme || 'default';
    this.components = this._initializeComponents();
  }

  /**
   * Get the UI schema for the emergency profile creation form
   * @returns {Object} - The UI schema
   */
  getProfileCreationSchema() {
    return this.components.profileCreation;
  }

  /**
   * Get the UI schema for the emergency profile view
   * @param {String} accessLevel - The access level
   * @returns {Object} - The UI schema
   */
  getProfileViewSchema(accessLevel = 'standard') {
    const schema = { ...this.components.profileView };
    
    // Adjust schema based on access level
    if (accessLevel === 'basic') {
      schema.sections = schema.sections.filter(section => 
        ['demographics', 'emergencyContacts'].includes(section.id)
      );
    } else if (accessLevel === 'standard') {
      schema.sections = schema.sections.filter(section => 
        !['insuranceInfo', 'notes'].includes(section.id)
      );
    }
    
    return schema;
  }

  /**
   * Get the UI schema for the emergency access interface
   * @returns {Object} - The UI schema
   */
  getEmergencyAccessSchema() {
    return this.components.emergencyAccess;
  }

  /**
   * Get the UI schema for the form factor management interface
   * @returns {Object} - The UI schema
   */
  getFormFactorManagementSchema() {
    return this.components.formFactorManagement;
  }

  /**
   * Get the UI schema for the emergency service authentication interface
   * @returns {Object} - The UI schema
   */
  getServiceAuthenticationSchema() {
    return this.components.serviceAuthentication;
  }

  /**
   * Get the UI schema for the security monitoring dashboard
   * @returns {Object} - The UI schema
   */
  getSecurityDashboardSchema() {
    return this.components.securityDashboard;
  }

  /**
   * Initialize UI components
   * @returns {Object} - The initialized components
   * @private
   */
  _initializeComponents() {
    return {
      profileCreation: {
        type: 'form',
        title: 'Create Emergency Medical Profile',
        description: 'Enter your emergency medical information',
        submitUrl: `${this.baseUrl}/api/profiles`,
        submitMethod: 'POST',
        sections: [
          {
            id: 'demographics',
            title: 'Personal Information',
            fields: [
              {
                id: 'fullName',
                label: 'Full Name',
                type: 'text',
                required: true,
                placeholder: 'Enter your full name'
              },
              {
                id: 'dateOfBirth',
                label: 'Date of Birth',
                type: 'date',
                required: true
              },
              {
                id: 'bloodType',
                label: 'Blood Type',
                type: 'select',
                required: true,
                options: [
                  { value: 'A+', label: 'A+' },
                  { value: 'A-', label: 'A-' },
                  { value: 'B+', label: 'B+' },
                  { value: 'B-', label: 'B-' },
                  { value: 'AB+', label: 'AB+' },
                  { value: 'AB-', label: 'AB-' },
                  { value: 'O+', label: 'O+' },
                  { value: 'O-', label: 'O-' },
                  { value: 'Unknown', label: 'Unknown' }
                ]
              }
            ]
          },
          {
            id: 'emergencyContacts',
            title: 'Emergency Contacts',
            fields: [
              {
                id: 'emergencyContacts',
                type: 'array',
                minItems: 1,
                itemTemplate: {
                  type: 'object',
                  fields: [
                    {
                      id: 'name',
                      label: 'Name',
                      type: 'text',
                      required: true
                    },
                    {
                      id: 'relationship',
                      label: 'Relationship',
                      type: 'text',
                      required: true
                    },
                    {
                      id: 'phone',
                      label: 'Phone',
                      type: 'tel',
                      required: true
                    }
                  ]
                }
              }
            ]
          },
          {
            id: 'allergies',
            title: 'Allergies',
            fields: [
              {
                id: 'allergies',
                type: 'array',
                itemTemplate: {
                  type: 'object',
                  fields: [
                    {
                      id: 'name',
                      label: 'Allergy',
                      type: 'text',
                      required: true
                    },
                    {
                      id: 'severity',
                      label: 'Severity',
                      type: 'select',
                      options: [
                        { value: 'Mild', label: 'Mild' },
                        { value: 'Moderate', label: 'Moderate' },
                        { value: 'Severe', label: 'Severe' },
                        { value: 'Life-threatening', label: 'Life-threatening' }
                      ]
                    },
                    {
                      id: 'reactions',
                      label: 'Reactions',
                      type: 'array',
                      itemTemplate: {
                        type: 'text'
                      }
                    }
                  ]
                }
              }
            ]
          },
          {
            id: 'medications',
            title: 'Medications',
            fields: [
              {
                id: 'medications',
                type: 'array',
                itemTemplate: {
                  type: 'object',
                  fields: [
                    {
                      id: 'name',
                      label: 'Medication',
                      type: 'text',
                      required: true
                    },
                    {
                      id: 'dosage',
                      label: 'Dosage',
                      type: 'text',
                      required: true
                    },
                    {
                      id: 'frequency',
                      label: 'Frequency',
                      type: 'text',
                      required: true
                    },
                    {
                      id: 'purpose',
                      label: 'Purpose',
                      type: 'text'
                    }
                  ]
                }
              }
            ]
          },
          {
            id: 'medicalConditions',
            title: 'Medical Conditions',
            fields: [
              {
                id: 'medicalConditions',
                type: 'array',
                itemTemplate: {
                  type: 'object',
                  fields: [
                    {
                      id: 'name',
                      label: 'Condition',
                      type: 'text',
                      required: true
                    },
                    {
                      id: 'diagnosisDate',
                      label: 'Diagnosis Date',
                      type: 'date'
                    },
                    {
                      id: 'notes',
                      label: 'Notes',
                      type: 'textarea'
                    }
                  ]
                }
              }
            ]
          },
          {
            id: 'advancedDirectives',
            title: 'Advanced Directives',
            fields: [
              {
                id: 'dnr',
                label: 'Do Not Resuscitate (DNR)',
                type: 'boolean'
              },
              {
                id: 'organDonor',
                label: 'Organ Donor',
                type: 'boolean'
              }
            ]
          },
          {
            id: 'insuranceInfo',
            title: 'Insurance Information',
            fields: [
              {
                id: 'insuranceInfo.provider',
                label: 'Insurance Provider',
                type: 'text'
              },
              {
                id: 'insuranceInfo.policyNumber',
                label: 'Policy Number',
                type: 'text'
              },
              {
                id: 'insuranceInfo.groupNumber',
                label: 'Group Number',
                type: 'text'
              },
              {
                id: 'insuranceInfo.phone',
                label: 'Insurance Phone',
                type: 'tel'
              }
            ]
          },
          {
            id: 'primaryCareProvider',
            title: 'Primary Care Provider',
            fields: [
              {
                id: 'primaryCareProvider.name',
                label: 'Provider Name',
                type: 'text'
              },
              {
                id: 'primaryCareProvider.phone',
                label: 'Provider Phone',
                type: 'tel'
              },
              {
                id: 'primaryCareProvider.address',
                label: 'Provider Address',
                type: 'textarea'
              }
            ]
          },
          {
            id: 'notes',
            title: 'Additional Notes',
            fields: [
              {
                id: 'notes',
                label: 'Additional Medical Notes',
                type: 'textarea',
                placeholder: 'Enter any additional information that may be important in an emergency'
              }
            ]
          }
        ]
      },
      
      profileView: {
        type: 'view',
        title: 'Emergency Medical Profile',
        dataUrl: `${this.baseUrl}/api/profiles/{profileId}`,
        sections: [
          {
            id: 'demographics',
            title: 'Personal Information',
            fields: [
              {
                id: 'fullName',
                label: 'Full Name',
                type: 'text'
              },
              {
                id: 'dateOfBirth',
                label: 'Date of Birth',
                type: 'date'
              },
              {
                id: 'bloodType',
                label: 'Blood Type',
                type: 'text'
              }
            ]
          },
          {
            id: 'emergencyContacts',
            title: 'Emergency Contacts',
            fields: [
              {
                id: 'emergencyContacts',
                type: 'table',
                columns: [
                  {
                    id: 'name',
                    label: 'Name'
                  },
                  {
                    id: 'relationship',
                    label: 'Relationship'
                  },
                  {
                    id: 'phone',
                    label: 'Phone',
                    format: 'phone'
                  }
                ]
              }
            ]
          },
          {
            id: 'allergies',
            title: 'Allergies',
            fields: [
              {
                id: 'allergies',
                type: 'table',
                columns: [
                  {
                    id: 'name',
                    label: 'Allergy'
                  },
                  {
                    id: 'severity',
                    label: 'Severity'
                  },
                  {
                    id: 'reactions',
                    label: 'Reactions',
                    format: 'list'
                  }
                ]
              }
            ]
          },
          {
            id: 'medications',
            title: 'Medications',
            fields: [
              {
                id: 'medications',
                type: 'table',
                columns: [
                  {
                    id: 'name',
                    label: 'Medication'
                  },
                  {
                    id: 'dosage',
                    label: 'Dosage'
                  },
                  {
                    id: 'frequency',
                    label: 'Frequency'
                  },
                  {
                    id: 'purpose',
                    label: 'Purpose'
                  }
                ]
              }
            ]
          },
          {
            id: 'medicalConditions',
            title: 'Medical Conditions',
            fields: [
              {
                id: 'medicalConditions',
                type: 'table',
                columns: [
                  {
                    id: 'name',
                    label: 'Condition'
                  },
                  {
                    id: 'diagnosisDate',
                    label: 'Diagnosis Date',
                    format: 'date'
                  },
                  {
                    id: 'notes',
                    label: 'Notes'
                  }
                ]
              }
            ]
          },
          {
            id: 'advancedDirectives',
            title: 'Advanced Directives',
            fields: [
              {
                id: 'dnr',
                label: 'Do Not Resuscitate (DNR)',
                type: 'boolean'
              },
              {
                id: 'organDonor',
                label: 'Organ Donor',
                type: 'boolean'
              }
            ]
          },
          {
            id: 'insuranceInfo',
            title: 'Insurance Information',
            fields: [
              {
                id: 'insuranceInfo.provider',
                label: 'Insurance Provider',
                type: 'text'
              },
              {
                id: 'insuranceInfo.policyNumber',
                label: 'Policy Number',
                type: 'text'
              },
              {
                id: 'insuranceInfo.groupNumber',
                label: 'Group Number',
                type: 'text'
              },
              {
                id: 'insuranceInfo.phone',
                label: 'Insurance Phone',
                type: 'tel',
                format: 'phone'
              }
            ]
          },
          {
            id: 'primaryCareProvider',
            title: 'Primary Care Provider',
            fields: [
              {
                id: 'primaryCareProvider.name',
                label: 'Provider Name',
                type: 'text'
              },
              {
                id: 'primaryCareProvider.phone',
                label: 'Provider Phone',
                type: 'tel',
                format: 'phone'
              },
              {
                id: 'primaryCareProvider.address',
                label: 'Provider Address',
                type: 'text'
              }
            ]
          },
          {
            id: 'notes',
            title: 'Additional Notes',
            fields: [
              {
                id: 'notes',
                label: 'Additional Medical Notes',
                type: 'textarea'
              }
            ]
          }
        ],
        actions: [
          {
            id: 'print',
            label: 'Print',
            icon: 'print',
            action: 'print'
          },
          {
            id: 'share',
            label: 'Share',
            icon: 'share',
            action: 'openModal',
            modalId: 'shareProfile'
          }
        ]
      },
      
      emergencyAccess: {
        type: 'view',
        title: 'Emergency Medical Access',
        description: 'Access emergency medical information',
        sections: [
          {
            id: 'scanner',
            title: 'Scan QR Code or NFC Tag',
            components: [
              {
                type: 'qrScanner',
                id: 'qrScanner',
                onScan: {
                  action: 'fetchData',
                  url: `${this.baseUrl}/api/emergency/access`,
                  method: 'POST',
                  resultHandler: 'displayProfile'
                }
              },
              {
                type: 'nfcReader',
                id: 'nfcReader',
                onRead: {
                  action: 'fetchData',
                  url: `${this.baseUrl}/api/emergency/access`,
                  method: 'POST',
                  resultHandler: 'displayProfile'
                }
              }
            ]
          },
          {
            id: 'manualEntry',
            title: 'Manual Entry',
            fields: [
              {
                id: 'formFactorId',
                label: 'Form Factor ID',
                type: 'text',
                required: true,
                placeholder: 'Enter the ID from the wristband or sticker'
              },
              {
                id: 'accessCode',
                label: 'Access Code',
                type: 'text',
                required: true,
                placeholder: 'Enter the access code'
              }
            ],
            actions: [
              {
                id: 'submit',
                label: 'Access Profile',
                action: 'fetchData',
                url: `${this.baseUrl}/api/emergency/access`,
                method: 'POST',
                resultHandler: 'displayProfile'
              }
            ]
          }
        ]
      },
      
      formFactorManagement: {
        type: 'dashboard',
        title: 'Manage Form Factors',
        description: 'Create and manage physical form factors for your emergency profile',
        dataUrl: `${this.baseUrl}/api/profiles/{profileId}/formFactors`,
        sections: [
          {
            id: 'currentFormFactors',
            title: 'Current Form Factors',
            components: [
              {
                type: 'table',
                id: 'formFactorsTable',
                columns: [
                  {
                    id: 'type',
                    label: 'Type'
                  },
                  {
                    id: 'createdAt',
                    label: 'Created',
                    format: 'date'
                  },
                  {
                    id: 'accessLevel',
                    label: 'Access Level'
                  },
                  {
                    id: 'actions',
                    label: 'Actions',
                    format: 'actions',
                    actions: [
                      {
                        id: 'view',
                        label: 'View',
                        icon: 'eye',
                        action: 'openModal',
                        modalId: 'viewFormFactor'
                      },
                      {
                        id: 'revoke',
                        label: 'Revoke',
                        icon: 'trash',
                        action: 'fetchData',
                        url: `${this.baseUrl}/api/profiles/{profileId}/formFactors/{formFactorId}`,
                        method: 'DELETE',
                        confirmMessage: 'Are you sure you want to revoke this form factor? This action cannot be undone.'
                      }
                    ]
                  }
                ]
              }
            ]
          },
          {
            id: 'createFormFactor',
            title: 'Create New Form Factor',
            components: [
              {
                type: 'tabs',
                id: 'formFactorTabs',
                tabs: [
                  {
                    id: 'qrCode',
                    label: 'QR Code',
                    components: [
                      {
                        type: 'form',
                        id: 'qrCodeForm',
                        fields: [
                          {
                            id: 'accessLevel',
                            label: 'Access Level',
                            type: 'select',
                            options: [
                              { value: 'basic', label: 'Basic (Name, DOB, Blood Type, Contacts)' },
                              { value: 'standard', label: 'Standard (Basic + Medical Info)' },
                              { value: 'full', label: 'Full (All Information)' }
                            ],
                            defaultValue: 'standard'
                          }
                        ],
                        actions: [
                          {
                            id: 'generate',
                            label: 'Generate QR Code',
                            action: 'fetchData',
                            url: `${this.baseUrl}/api/profiles/{profileId}/formFactors/qr`,
                            method: 'POST',
                            resultHandler: 'displayQRCode'
                          }
                        ]
                      }
                    ]
                  },
                  {
                    id: 'wristband',
                    label: 'Wristband',
                    components: [
                      {
                        type: 'form',
                        id: 'wristbandForm',
                        fields: [
                          {
                            id: 'accessLevel',
                            label: 'Access Level',
                            type: 'select',
                            options: [
                              { value: 'basic', label: 'Basic (Name, DOB, Blood Type, Contacts)' },
                              { value: 'standard', label: 'Standard (Basic + Medical Info)' },
                              { value: 'full', label: 'Full (All Information)' }
                            ],
                            defaultValue: 'standard'
                          }
                        ],
                        actions: [
                          {
                            id: 'generate',
                            label: 'Generate Wristband',
                            action: 'fetchData',
                            url: `${this.baseUrl}/api/profiles/{profileId}/formFactors/wristband`,
                            method: 'POST',
                            resultHandler: 'displayWristband'
                          }
                        ]
                      }
                    ]
                  },
                  {
                    id: 'vehicleSticker',
                    label: 'Vehicle Sticker',
                    components: [
                      {
                        type: 'form',
                        id: 'vehicleStickerForm',
                        fields: [
                          {
                            id: 'accessLevel',
                            label: 'Access Level',
                            type: 'select',
                            options: [
                              { value: 'basic', label: 'Basic (Name, DOB, Blood Type, Contacts)' },
                              { value: 'standard', label: 'Standard (Basic + Medical Info)' },
                              { value: 'full', label: 'Full (All Information)' }
                            ],
                            defaultValue: 'standard'
                          },
                          {
                            id: 'vehicleInfo.make',
                            label: 'Vehicle Make',
                            type: 'text',
                            required: true
                          },
                          {
                            id: 'vehicleInfo.model',
                            label: 'Vehicle Model',
                            type: 'text',
                            required: true
                          },
                          {
                            id: 'vehicleInfo.year',
                            label: 'Vehicle Year',
                            type: 'number',
                            required: true
                          },
                          {
                            id: 'vehicleInfo.licensePlate',
                            label: 'License Plate',
                            type: 'text'
                          }
                        ],
                        actions: [
                          {
                            id: 'generate',
                            label: 'Generate Vehicle Sticker',
                            action: 'fetchData',
                            url: `${this.baseUrl}/api/profiles/{profileId}/formFactors/vehicle`,
                            method: 'POST',
                            resultHandler: 'displayVehicleSticker'
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      },
      
      serviceAuthentication: {
        type: 'form',
        title: 'Emergency Service Authentication',
        description: 'Authenticate as an emergency service provider',
        submitUrl: `${this.baseUrl}/api/auth/service`,
        submitMethod: 'POST',
        fields: [
          {
            id: 'apiKey',
            label: 'API Key',
            type: 'text',
            required: true,
            placeholder: 'Enter your service API key'
          },
          {
            id: 'apiSecret',
            label: 'API Secret',
            type: 'password',
            required: true,
            placeholder: 'Enter your service API secret'
          }
        ],
        actions: [
          {
            id: 'authenticate',
            label: 'Authenticate',
            primary: true
          }
        ]
      },
      
      securityDashboard: {
        type: 'dashboard',
        title: 'Security Monitoring',
        description: 'Monitor security events and incidents',
        dataUrl: `${this.baseUrl}/api/security/dashboard`,
        refreshInterval: 60000, // 1 minute
        sections: [
          {
            id: 'summary',
            title: 'Security Summary',
            components: [
              {
                type: 'stats',
                id: 'securityStats',
                stats: [
                  {
                    id: 'activeIncidents',
                    label: 'Active Incidents',
                    color: 'red'
                  },
                  {
                    id: 'accessAttempts',
                    label: 'Access Attempts (24h)',
                    color: 'blue'
                  },
                  {
                    id: 'anomaliesDetected',
                    label: 'Anomalies Detected (24h)',
                    color: 'orange'
                  },
                  {
                    id: 'securityScore',
                    label: 'Security Score',
                    format: 'percentage',
                    color: 'green'
                  }
                ]
              }
            ]
          },
          {
            id: 'incidents',
            title: 'Security Incidents',
            components: [
              {
                type: 'table',
                id: 'incidentsTable',
                dataPath: 'incidents',
                columns: [
                  {
                    id: 'timestamp',
                    label: 'Time',
                    format: 'datetime'
                  },
                  {
                    id: 'type',
                    label: 'Type'
                  },
                  {
                    id: 'severity',
                    label: 'Severity',
                    format: 'badge'
                  },
                  {
                    id: 'description',
                    label: 'Description'
                  },
                  {
                    id: 'status',
                    label: 'Status',
                    format: 'badge'
                  },
                  {
                    id: 'actions',
                    label: 'Actions',
                    format: 'actions',
                    actions: [
                      {
                        id: 'view',
                        label: 'View Details',
                        icon: 'eye',
                        action: 'openModal',
                        modalId: 'incidentDetails'
                      },
                      {
                        id: 'resolve',
                        label: 'Resolve',
                        icon: 'check',
                        action: 'fetchData',
                        url: `${this.baseUrl}/api/security/incidents/{incidentId}/resolve`,
                        method: 'POST',
                        confirmMessage: 'Are you sure you want to mark this incident as resolved?'
                      }
                    ]
                  }
                ]
              }
            ]
          },
          {
            id: 'accessLog',
            title: 'Recent Access Log',
            components: [
              {
                type: 'table',
                id: 'accessLogTable',
                dataPath: 'accessLog',
                columns: [
                  {
                    id: 'timestamp',
                    label: 'Time',
                    format: 'datetime'
                  },
                  {
                    id: 'profileId',
                    label: 'Profile'
                  },
                  {
                    id: 'accessType',
                    label: 'Access Type'
                  },
                  {
                    id: 'serviceId',
                    label: 'Service'
                  },
                  {
                    id: 'location',
                    label: 'Location'
                  },
                  {
                    id: 'anomalyScore',
                    label: 'Anomaly Score',
                    format: 'percentage'
                  }
                ]
              }
            ]
          }
        ]
      }
    };
  }
}

module.exports = NovaVisionComponents;
