/**
 * NovaAssure Routes
 *
 * This file defines the routes for the NovaAssure (UCTF) component.
 */

const express = require('express');
const router = express.Router();

// Import route modules
const controlRoutes = require('./controlRoutes');
const testPlanRoutes = require('./testPlanRoutes');
const testExecutionRoutes = require('./testExecutionRoutes');
const evidenceRoutes = require('./evidenceRoutes');
const reportRoutes = require('./reportRoutes');

// Use route modules
router.use('/controls', controlRoutes);
router.use('/test-plans', testPlanRoutes);
router.use('/test-execution', testExecutionRoutes);
router.use('/evidence', evidenceRoutes);
router.use('/reports', reportRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    component: 'NovaAssure',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
