# API Authentication

NovaAlign uses JWT (JSON Web Tokens) for secure API authentication. All API requests must include a valid JWT token in the `Authorization` header.

## Obtaining an API Key

1. Log in to the NovaAlign dashboard
2. Navigate to **Settings** > **API Keys**
3. Click **Generate New API Key**
4. Copy the generated token (you won't be able to see it again)

## Making Authenticated Requests

Include the JWT token in the `Authorization` header of your requests:

```http
GET /api/v1/systems HTTP/1.1
Host: api.novaalign.ai
Authorization: Bearer your-jwt-token-here
Content-Type: application/json
```

## Token Expiration

- Access tokens expire after 24 hours by default
- Refresh tokens expire after 30 days
- You can configure these values in your environment variables

## Rate Limiting

- 1000 requests per hour per API key
- 100 requests per minute per endpoint
- Headers are included in the response:
  - `X-RateLimit-Limit`: Total requests allowed
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Time when limit resets (UTC timestamp)

## Error Responses

| Status Code | Error Code | Description |
|-------------|------------|-------------|
| 401 | UNAUTHORIZED | Missing or invalid token |
| 403 | FORBIDDEN | Insufficient permissions |
| 429 | TOO_MANY_REQUESTS | Rate limit exceeded |

## Best Practices

1. **Never expose your API keys** in client-side code or public repositories
2. **Rotate your API keys** regularly
3. **Use environment variables** to store sensitive credentials
4. **Implement proper error handling** for authentication failures
5. **Monitor your usage** to avoid hitting rate limits

## Example: Obtaining a Token

```bash
curl -X POST https://api.novaalign.ai/auth/token \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"your-password"}'
```

Response:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 86400,
  "refresh_token": "def50200ae5d4a2e8b9a3e7c8b5d6f7a..."
}
```
