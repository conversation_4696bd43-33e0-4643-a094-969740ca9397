/**
 * CSDE Service
 * 
 * This module provides functions to interact with the CSDE Advanced API.
 */

import axios from 'axios';

// Base URL for CSDE Advanced API
const API_BASE_URL = '/api/csde-advanced';

/**
 * Fetch dashboard data from the CSDE Advanced API
 * @returns {Promise<Object>} - Dashboard data
 */
export const fetchDashboardData = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/dashboard/data`);
    
    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.error || 'Failed to fetch dashboard data');
    }
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    throw error;
  }
};

/**
 * Fetch metrics from the CSDE Advanced API
 * @returns {Promise<Object>} - Metrics data
 */
export const fetchMetrics = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/metrics`);
    
    if (response.data.success) {
      return response.data.metrics;
    } else {
      throw new Error(response.data.error || 'Failed to fetch metrics');
    }
  } catch (error) {
    console.error('Error fetching metrics:', error);
    throw error;
  }
};

/**
 * Fetch health status from the CSDE Advanced API
 * @returns {Promise<Object>} - Health status data
 */
export const fetchHealth = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/health`);
    
    if (response.data.success) {
      return response.data.health;
    } else {
      throw new Error(response.data.error || 'Failed to fetch health status');
    }
  } catch (error) {
    console.error('Error fetching health status:', error);
    throw error;
  }
};

/**
 * Process data using the CSDE Advanced API
 * @param {Object} data - Data to process
 * @param {Object} options - Processing options
 * @returns {Promise<Object>} - Processing result
 */
export const processData = async (data, options = {}) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/process`, {
      data,
      options
    });
    
    if (response.data.success) {
      return response.data.result;
    } else {
      throw new Error(response.data.error || 'Failed to process data');
    }
  } catch (error) {
    console.error('Error processing data:', error);
    throw error;
  }
};

/**
 * Process data offline using the CSDE Advanced API
 * @param {Object} data - Data to process
 * @param {Object} options - Processing options
 * @returns {Promise<Object>} - Processing result
 */
export const processDataOffline = async (data, options = {}) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/offline`, {
      data,
      options
    });
    
    if (response.data.success) {
      return response.data.result;
    } else {
      throw new Error(response.data.error || 'Failed to process data offline');
    }
  } catch (error) {
    console.error('Error processing data offline:', error);
    throw error;
  }
};

/**
 * Get offline processing result from the CSDE Advanced API
 * @param {string} processingId - Processing ID
 * @returns {Promise<Object>} - Processing result
 */
export const getOfflineProcessingResult = async (processingId) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/offline/${processingId}`);
    
    if (response.data.success) {
      return response.data.result;
    } else {
      throw new Error(response.data.error || 'Failed to get offline processing result');
    }
  } catch (error) {
    console.error('Error getting offline processing result:', error);
    throw error;
  }
};

/**
 * Predict cross-domain insights using the CSDE Advanced API
 * @param {Object} data - Data to predict from
 * @param {string} targetDomain - Target domain
 * @param {Object} options - Prediction options
 * @returns {Promise<Object>} - Prediction result
 */
export const predictCrossDomain = async (data, targetDomain, options = {}) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/predict`, {
      data,
      targetDomain,
      options
    });
    
    if (response.data.success) {
      return response.data.result;
    } else {
      throw new Error(response.data.error || 'Failed to predict cross-domain insights');
    }
  } catch (error) {
    console.error('Error predicting cross-domain insights:', error);
    throw error;
  }
};

/**
 * Map compliance controls using the CSDE Advanced API
 * @param {Object} implementationData - Implementation data
 * @param {string} primaryFramework - Primary framework
 * @param {Array<string>} targetFrameworks - Target frameworks
 * @param {Object} options - Mapping options
 * @returns {Promise<Object>} - Mapping result
 */
export const mapCompliance = async (implementationData, primaryFramework, targetFrameworks, options = {}) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/compliance`, {
      implementationData,
      primaryFramework,
      targetFrameworks,
      options
    });
    
    if (response.data.success) {
      return response.data.result;
    } else {
      throw new Error(response.data.error || 'Failed to map compliance controls');
    }
  } catch (error) {
    console.error('Error mapping compliance controls:', error);
    throw error;
  }
};

/**
 * Reset metrics using the CSDE Advanced API
 * @returns {Promise<Object>} - Reset result
 */
export const resetMetrics = async () => {
  try {
    const response = await axios.post(`${API_BASE_URL}/metrics/reset`);
    
    if (response.data.success) {
      return response.data;
    } else {
      throw new Error(response.data.error || 'Failed to reset metrics');
    }
  } catch (error) {
    console.error('Error resetting metrics:', error);
    throw error;
  }
};

export default {
  fetchDashboardData,
  fetchMetrics,
  fetchHealth,
  processData,
  processDataOffline,
  getOfflineProcessingResult,
  predictCrossDomain,
  mapCompliance,
  resetMetrics
};
