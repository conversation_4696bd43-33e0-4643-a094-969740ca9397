/**
 * Quantum Constants
 * 
 * This module defines constants used throughout the quantum components,
 * ensuring consistent bounded values across the system.
 * 
 * All constants are derived from the π10³ resolution principle and
 * enforce the bounded calculus of Comphyology.
 */

// Mathematical constants
const PI = Math.PI;
const PI_10_CUBED = PI * Math.pow(10, 3);
const GOLDEN_RATIO = (1 + Math.sqrt(5)) / 2;

/**
 * Maximum safe bounds for different domains
 * 
 * These constants replace the use of Infinity in the system,
 * ensuring all operations remain within finite boundaries.
 * 
 * The values are derived from the π10³ resolution principle
 * and are specific to each domain.
 */
const MAX_SAFE_BOUNDS = {
  // Universal domain bounds
  UNIVERSAL: {
    // General maximum value (π10³ * 10)
    MAX_VALUE: PI_10_CUBED * 10,
    // Minimum non-zero value (1 / (π10³ * 10))
    MIN_VALUE: 1 / (PI_10_CUBED * 10),
    // Maximum tensor dimension
    MAX_DIMENSION: 1000,
    // Maximum tensor size (elements)
    MAX_TENSOR_SIZE: 1000000,
    // Maximum coherence value
    MAX_COHERENCE: 1.0,
    // Maximum entropy containment
    MAX_ENTROPY_CONTAINMENT: 0.05
  },
  
  // Cyber domain bounds
  CYBER: {
    // Maximum value for cyber domain
    MAX_VALUE: PI_10_CUBED * 8,
    // Minimum non-zero value for cyber domain
    MIN_VALUE: 1 / (PI_10_CUBED * 8),
    // Maximum tensor dimension for cyber domain
    MAX_DIMENSION: 800,
    // Maximum tensor size for cyber domain
    MAX_TENSOR_SIZE: 800000,
    // Maximum coherence value for cyber domain
    MAX_COHERENCE: 1.0,
    // Maximum entropy containment for cyber domain
    MAX_ENTROPY_CONTAINMENT: 0.04
  },
  
  // Financial domain bounds
  FINANCIAL: {
    // Maximum value for financial domain
    MAX_VALUE: PI_10_CUBED * 6,
    // Minimum non-zero value for financial domain
    MIN_VALUE: 1 / (PI_10_CUBED * 6),
    // Maximum tensor dimension for financial domain
    MAX_DIMENSION: 600,
    // Maximum tensor size for financial domain
    MAX_TENSOR_SIZE: 600000,
    // Maximum coherence value for financial domain
    MAX_COHERENCE: 1.0,
    // Maximum entropy containment for financial domain
    MAX_ENTROPY_CONTAINMENT: 0.03
  },
  
  // Biological domain bounds
  BIOLOGICAL: {
    // Maximum value for biological domain
    MAX_VALUE: PI_10_CUBED * 9,
    // Minimum non-zero value for biological domain
    MIN_VALUE: 1 / (PI_10_CUBED * 9),
    // Maximum tensor dimension for biological domain
    MAX_DIMENSION: 900,
    // Maximum tensor size for biological domain
    MAX_TENSOR_SIZE: 900000,
    // Maximum coherence value for biological domain
    MAX_COHERENCE: 1.0,
    // Maximum entropy containment for biological domain
    MAX_ENTROPY_CONTAINMENT: 0.035
  }
};

/**
 * Saturation functions for different domains
 * 
 * These functions ensure that values remain within the bounded
 * limits of each domain, replacing infinity-based operations.
 */
const saturate = {
  /**
   * Saturate a value to the universal domain bounds
   * @param {number} value - Value to saturate
   * @returns {number} - Saturated value
   */
  universal(value) {
    if (typeof value !== 'number' || !Number.isFinite(value)) {
      return 0; // Default safe value
    }
    
    return Math.max(
      -MAX_SAFE_BOUNDS.UNIVERSAL.MAX_VALUE,
      Math.min(MAX_SAFE_BOUNDS.UNIVERSAL.MAX_VALUE, value)
    );
  },
  
  /**
   * Saturate a value to the cyber domain bounds
   * @param {number} value - Value to saturate
   * @returns {number} - Saturated value
   */
  cyber(value) {
    if (typeof value !== 'number' || !Number.isFinite(value)) {
      return 0; // Default safe value
    }
    
    return Math.max(
      -MAX_SAFE_BOUNDS.CYBER.MAX_VALUE,
      Math.min(MAX_SAFE_BOUNDS.CYBER.MAX_VALUE, value)
    );
  },
  
  /**
   * Saturate a value to the financial domain bounds
   * @param {number} value - Value to saturate
   * @returns {number} - Saturated value
   */
  financial(value) {
    if (typeof value !== 'number' || !Number.isFinite(value)) {
      return 0; // Default safe value
    }
    
    return Math.max(
      -MAX_SAFE_BOUNDS.FINANCIAL.MAX_VALUE,
      Math.min(MAX_SAFE_BOUNDS.FINANCIAL.MAX_VALUE, value)
    );
  },
  
  /**
   * Saturate a value to the biological domain bounds
   * @param {number} value - Value to saturate
   * @returns {number} - Saturated value
   */
  biological(value) {
    if (typeof value !== 'number' || !Number.isFinite(value)) {
      return 0; // Default safe value
    }
    
    return Math.max(
      -MAX_SAFE_BOUNDS.BIOLOGICAL.MAX_VALUE,
      Math.min(MAX_SAFE_BOUNDS.BIOLOGICAL.MAX_VALUE, value)
    );
  },
  
  /**
   * Saturate a value to the bounds of a specific domain
   * @param {string} domain - Domain to saturate to
   * @param {number} value - Value to saturate
   * @returns {number} - Saturated value
   */
  forDomain(domain, value) {
    switch (domain) {
      case 'cyber':
        return saturate.cyber(value);
      case 'financial':
        return saturate.financial(value);
      case 'biological':
        return saturate.biological(value);
      case 'universal':
      default:
        return saturate.universal(value);
    }
  }
};

/**
 * Asymptotic thresholding functions
 * 
 * These functions implement Comphyon-based asymptotic thresholding,
 * which allows values to approach but never reach boundaries.
 */
const asymptotic = {
  /**
   * Apply asymptotic thresholding to a value
   * @param {number} value - Value to threshold
   * @param {number} bound - Boundary value
   * @returns {number} - Thresholded value
   */
  threshold(value, bound) {
    if (typeof value !== 'number' || !Number.isFinite(value)) {
      return 0; // Default safe value
    }
    
    // Ensure bound is positive
    bound = Math.abs(bound);
    
    // Apply asymptotic function: bound * tanh(value / bound)
    // This approaches but never exceeds the bound
    return bound * Math.tanh(value / bound);
  },
  
  /**
   * Apply asymptotic thresholding to a value for a specific domain
   * @param {string} domain - Domain to threshold for
   * @param {number} value - Value to threshold
   * @returns {number} - Thresholded value
   */
  forDomain(domain, value) {
    const bounds = MAX_SAFE_BOUNDS[domain.toUpperCase()] || MAX_SAFE_BOUNDS.UNIVERSAL;
    return asymptotic.threshold(value, bounds.MAX_VALUE);
  }
};

module.exports = {
  PI,
  PI_10_CUBED,
  GOLDEN_RATIO,
  MAX_SAFE_BOUNDS,
  saturate,
  asymptotic
};
