/**
 * Compliance Status Model
 * 
 * This model defines the schema for compliance status tracking in the Privacy Management API.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const evidenceSchema = new Schema({
  type: { 
    type: String, 
    enum: ['document', 'screenshot', 'link', 'text', 'other'], 
    default: 'document' 
  },
  title: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  url: { 
    type: String, 
    trim: true 
  },
  fileId: { 
    type: String, 
    trim: true 
  },
  fileName: { 
    type: String, 
    trim: true 
  },
  fileType: { 
    type: String, 
    trim: true 
  },
  fileSize: { 
    type: Number 
  },
  content: { 
    type: String 
  },
  addedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  addedAt: { 
    type: Date, 
    default: Date.now 
  },
  metadata: { 
    type: Object 
  }
}, {
  _id: true
});

const requirementStatusSchema = new Schema({
  requirement: { 
    type: Schema.Types.ObjectId, 
    ref: 'ComplianceRequirement', 
    required: true 
  },
  requirementCode: { 
    type: String, 
    required: true, 
    trim: true 
  },
  requirementTitle: { 
    type: String, 
    required: true, 
    trim: true 
  },
  status: { 
    type: String, 
    enum: ['compliant', 'non-compliant', 'in-progress', 'not-started', 'not-applicable'], 
    default: 'not-started' 
  },
  evidence: [evidenceSchema],
  notes: { 
    type: String 
  },
  assignedTo: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  dueDate: { 
    type: Date 
  },
  completedDate: { 
    type: Date 
  },
  lastUpdatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  lastUpdatedAt: { 
    type: Date 
  },
  history: [{
    status: { 
      type: String, 
      enum: ['compliant', 'non-compliant', 'in-progress', 'not-started', 'not-applicable'] 
    },
    notes: { 
      type: String 
    },
    updatedBy: { 
      type: Schema.Types.ObjectId, 
      ref: 'User' 
    },
    updatedAt: { 
      type: Date, 
      default: Date.now 
    }
  }]
}, {
  _id: true
});

const complianceStatusSchema = new Schema({
  entityType: { 
    type: String, 
    required: true, 
    enum: ['organization', 'system', 'process', 'vendor', 'dataset'], 
    default: 'organization' 
  },
  entityId: { 
    type: String, 
    required: true 
  },
  framework: { 
    type: Schema.Types.ObjectId, 
    ref: 'RegulatoryFramework', 
    required: true 
  },
  frameworkName: { 
    type: String, 
    required: true, 
    trim: true 
  },
  frameworkCode: { 
    type: String, 
    required: true, 
    trim: true 
  },
  frameworkVersion: { 
    type: String, 
    required: true, 
    trim: true 
  },
  status: { 
    type: String, 
    enum: ['compliant', 'non-compliant', 'in-progress', 'not-started', 'not-applicable'], 
    default: 'not-started' 
  },
  progress: { 
    type: Number, 
    min: 0, 
    max: 100, 
    default: 0 
  },
  requirementStatuses: [requirementStatusSchema],
  lastAssessment: { 
    type: Date 
  },
  nextAssessment: { 
    type: Date 
  },
  assessmentFrequency: { 
    type: String, 
    enum: ['monthly', 'quarterly', 'semi-annual', 'annual', 'bi-annual', 'custom'], 
    default: 'annual' 
  },
  assessmentFrequencyDays: { 
    type: Number 
  },
  notes: { 
    type: String 
  },
  metadata: { 
    type: Object 
  }
}, {
  timestamps: true
});

// Add compound index for entity and framework
complianceStatusSchema.index({ 
  entityType: 1, 
  entityId: 1, 
  framework: 1 
}, { 
  unique: true 
});

// Add index for status
complianceStatusSchema.index({ 
  status: 1 
});

// Add index for last assessment
complianceStatusSchema.index({ 
  lastAssessment: 1 
});

// Add index for next assessment
complianceStatusSchema.index({ 
  nextAssessment: 1 
});

// Create model
const ComplianceStatus = mongoose.model('ComplianceStatus', complianceStatusSchema);

module.exports = ComplianceStatus;
