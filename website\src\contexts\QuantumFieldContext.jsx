import React, { createContext, useContext, useReducer, useEffect, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import useQuantumWorker from '../hooks/useQuantumWorker';

// Initial state
const initialState = {
  entropy: 0.5,
  coherence: 1.0,
  amplitude: Math.sqrt(0.5),
  phase: 0,
  timestamp: Date.now(),
  isReady: false,
  entropyHistory: [],
  isQuantumActive: true,
  performance: {
    lastRenderTime: 0,
    frameCount: 0,
    fps: 0,
    lastFpsUpdate: 0
  }
};

// Action types
const ACTION_TYPES = {
  UPDATE_QUANTUM_STATE: 'UPDATE_QUANTUM_STATE',
  UPDATE_PARAMS: 'UPDATE_PARAMS',
  TOGGLE_QUANTUM: 'TOGGLE_QUANTUM',
  ADD_ENTROPY_MEASUREMENT: 'ADD_ENTROPY_MEASUREMENT',
  RESET: 'RESET'
};

// Reducer function
function quantumReducer(state, action) {
  switch (action.type) {
    case ACTION_TYPES.UPDATE_QUANTUM_STATE:
      return {
        ...state,
        ...action.payload,
        timestamp: action.payload.timestamp || Date.now()
      };
      
    case ACTION_TYPES.UPDATE_PARAMS:
      return {
        ...state,
        ...action.payload,
        amplitude: action.payload.entropy !== undefined 
          ? Math.sqrt(1 - action.payload.entropy) 
          : state.amplitude
      };
      
    case ACTION_TYPES.TOGGLE_QUANTUM:
      return {
        ...state,
        isQuantumActive: action.payload !== undefined 
          ? action.payload 
          : !state.isQuantumActive
      };
      
    case ACTION_TYPES.ADD_ENTROPY_MEASUREMENT: {
      const newMeasurement = {
        entropy: action.payload.entropy,
        coherence: action.payload.coherence || state.coherence,
        timestamp: action.payload.timestamp || Date.now(),
        mode: state.isQuantumActive ? 'quantum' : 'classical',
        anomaly: action.payload.anomaly
      };
      
      return {
        ...state,
        entropyHistory: [
          ...state.entropyHistory.slice(-99), // Keep last 100 measurements
          newMeasurement
        ]
      };
    }
      
    case ACTION_TYPES.RESET:
      return {
        ...initialState,
        entropyHistory: [],
        isQuantumActive: state.isQuantumActive
      };
      
    default:
      return state;
  }
}

// Create context
const QuantumFieldContext = createContext();

// Provider component
export const QuantumFieldProvider = ({ children }) => {
  const [state, dispatch] = useReducer(quantumReducer, initialState);
  
  // Initialize quantum worker
  const { 
    entropy, 
    coherence, 
    amplitude, 
    phase, 
    timestamp,
    updateParams,
    isReady 
  } = useQuantumWorker(initialState.entropy, initialState.coherence);
  
  // Update state when quantum worker updates
  useEffect(() => {
    dispatch({
      type: ACTION_TYPES.UPDATE_QUANTUM_STATE,
      payload: { entropy, coherence, amplitude, phase, timestamp, isReady }
    });
  }, [entropy, coherence, amplitude, phase, timestamp, isReady]);
  
  // Add entropy measurement to history periodically
  useEffect(() => {
    if (!isReady) return;
    
    const interval = setInterval(() => {
      dispatch({
        type: ACTION_TYPES.ADD_ENTROPY_MEASUREMENT,
        payload: { entropy, coherence }
      });
    }, 1000); // Log every second
    
    return () => clearInterval(interval);
  }, [entropy, coherence, isReady]);
  
  // Update quantum parameters
  const setQuantumParams = useCallback((params) => {
    updateParams(params);
    dispatch({
      type: ACTION_TYPES.UPDATE_PARAMS,
      payload: params
    });
  }, [updateParams]);
  
  // Toggle quantum/classical mode
  const toggleQuantumMode = useCallback((isActive) => {
    dispatch({
      type: ACTION_TYPES.TOGGLE_QUANTUM,
      payload: isActive
    });
  }, []);
  
  // Add manual measurement
  const addMeasurement = useCallback((measurement) => {
    dispatch({
      type: ACTION_TYPES.ADD_ENTROPY_MEASUREMENT,
      payload: {
        entropy: measurement.entropy,
        coherence: measurement.coherence,
        timestamp: measurement.timestamp || Date.now(),
        anomaly: measurement.anomaly
      }
    });
  }, []);
  
  // Reset quantum state
  const reset = useCallback(() => {
    dispatch({ type: ACTION_TYPES.RESET });
    updateParams({
      entropy: initialState.entropy,
      coherence: initialState.coherence
    });
  }, [updateParams]);
  
  // Memoize context value
  const contextValue = useMemo(() => ({
    ...state,
    setQuantumParams,
    toggleQuantumMode,
    addMeasurement,
    reset,
    isReady
  }), [state, setQuantumParams, toggleQuantumMode, addMeasurement, reset, isReady]);
  
  return (
    <QuantumFieldContext.Provider value={contextValue}>
      {children}
    </QuantumFieldContext.Provider>
  );
};

QuantumFieldProvider.propTypes = {
  children: PropTypes.node.isRequired
};

// Custom hook for using the quantum field context
export const useQuantumField = () => {
  const context = useContext(QuantumFieldContext);
  if (context === undefined) {
    throw new Error('useQuantumField must be used within a QuantumFieldProvider');
  }
  return context;
};

export default QuantumFieldContext;
