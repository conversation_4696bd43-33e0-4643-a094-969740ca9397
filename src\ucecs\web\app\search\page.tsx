'use client';

import { useState } from 'react';
import MainLayout from '@/components/MainLayout';
import { FiSearch, FiFilter, FiDownload, FiEye, FiLink } from 'react-icons/fi';

// Mock data for search results
const mockEvidenceResults = [
  { id: 'ev-001', name: 'Password Policy', category: 'Policies', status: 'Valid', date: '2023-10-15' },
  { id: 'ev-002', name: 'Firewall Configuration', category: 'Configurations', status: 'Valid', date: '2023-10-14' },
  { id: 'ev-003', name: 'User Access Review', category: 'Reports', status: 'Pending', date: '2023-10-13' },
];

const mockRequirementResults = [
  { 
    id: 'req-001', 
    name: 'Password Policy', 
    framework: 'ISO 27001', 
    control: 'A.9.4.3', 
    status: 'Compliant',
    lastAssessed: '2023-10-15',
  },
  { 
    id: 'req-002', 
    name: 'Access Control', 
    framework: 'NIST CSF', 
    control: 'PR.AC-1', 
    status: 'Partially Compliant',
    lastAssessed: '2023-10-14',
  },
];

export default function SearchPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState('all');
  const [isAdvancedSearch, setIsAdvancedSearch] = useState(false);
  const [advancedFilters, setAdvancedFilters] = useState({
    dateFrom: '',
    dateTo: '',
    category: '',
    framework: '',
    status: '',
  });
  const [searchResults, setSearchResults] = useState<{
    evidence: typeof mockEvidenceResults,
    requirements: typeof mockRequirementResults
  }>({
    evidence: [],
    requirements: []
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!searchQuery.trim()) return;
    
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      // Filter mock data based on search query
      const evidenceResults = searchType === 'all' || searchType === 'evidence'
        ? mockEvidenceResults.filter(item => 
            item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.category.toLowerCase().includes(searchQuery.toLowerCase())
          )
        : [];
      
      const requirementResults = searchType === 'all' || searchType === 'requirements'
        ? mockRequirementResults.filter(item => 
            item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.framework.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.control.toLowerCase().includes(searchQuery.toLowerCase())
          )
        : [];
      
      setSearchResults({
        evidence: evidenceResults,
        requirements: requirementResults
      });
      
      setIsLoading(false);
    }, 500);
  };

  const handleAdvancedFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setAdvancedFilters({
      ...advancedFilters,
      [name]: value,
    });
  };

  const clearSearch = () => {
    setSearchQuery('');
    setSearchType('all');
    setAdvancedFilters({
      dateFrom: '',
      dateTo: '',
      category: '',
      framework: '',
      status: '',
    });
    setSearchResults({
      evidence: [],
      requirements: []
    });
  };

  return (
    <MainLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Search</h1>
        <p className="text-gray-500 dark:text-gray-400">Search for evidence and requirements</p>
      </div>

      <div className="card mb-6">
        <form onSubmit={handleSearch}>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="flex-1">
              <div className="relative">
                <input
                  type="text"
                  className="input pl-10 w-full"
                  placeholder="Search for evidence or requirements..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiSearch className="text-gray-400" />
                </div>
              </div>
            </div>
            <div className="w-full md:w-48">
              <select
                className="input w-full"
                value={searchType}
                onChange={(e) => setSearchType(e.target.value)}
              >
                <option value="all">All</option>
                <option value="evidence">Evidence</option>
                <option value="requirements">Requirements</option>
              </select>
            </div>
            <div>
              <button
                type="submit"
                className="btn btn-primary w-full md:w-auto"
                disabled={isLoading}
              >
                {isLoading ? 'Searching...' : 'Search'}
              </button>
            </div>
          </div>

          <div className="flex items-center justify-between mb-4">
            <button
              type="button"
              className="text-primary text-sm flex items-center"
              onClick={() => setIsAdvancedSearch(!isAdvancedSearch)}
            >
              <FiFilter className="mr-1" />
              {isAdvancedSearch ? 'Hide Advanced Search' : 'Advanced Search'}
            </button>
            {(searchResults.evidence.length > 0 || searchResults.requirements.length > 0) && (
              <button
                type="button"
                className="text-gray-500 text-sm"
                onClick={clearSearch}
              >
                Clear Search
              </button>
            )}
          </div>

          {isAdvancedSearch && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label htmlFor="dateFrom" className="label">From Date</label>
                <input
                  type="date"
                  id="dateFrom"
                  name="dateFrom"
                  className="input"
                  value={advancedFilters.dateFrom}
                  onChange={handleAdvancedFilterChange}
                />
              </div>
              <div>
                <label htmlFor="dateTo" className="label">To Date</label>
                <input
                  type="date"
                  id="dateTo"
                  name="dateTo"
                  className="input"
                  value={advancedFilters.dateTo}
                  onChange={handleAdvancedFilterChange}
                />
              </div>
              <div>
                <label htmlFor="status" className="label">Status</label>
                <select
                  id="status"
                  name="status"
                  className="input"
                  value={advancedFilters.status}
                  onChange={handleAdvancedFilterChange}
                >
                  <option value="">All Statuses</option>
                  <option value="Valid">Valid</option>
                  <option value="Invalid">Invalid</option>
                  <option value="Pending">Pending</option>
                  <option value="Compliant">Compliant</option>
                  <option value="Non-Compliant">Non-Compliant</option>
                  <option value="Partially Compliant">Partially Compliant</option>
                </select>
              </div>
              {searchType !== 'requirements' && (
                <div>
                  <label htmlFor="category" className="label">Category</label>
                  <select
                    id="category"
                    name="category"
                    className="input"
                    value={advancedFilters.category}
                    onChange={handleAdvancedFilterChange}
                  >
                    <option value="">All Categories</option>
                    <option value="Policies">Policies</option>
                    <option value="Procedures">Procedures</option>
                    <option value="Configurations">Configurations</option>
                    <option value="Logs">Logs</option>
                    <option value="Reports">Reports</option>
                  </select>
                </div>
              )}
              {searchType !== 'evidence' && (
                <div>
                  <label htmlFor="framework" className="label">Framework</label>
                  <select
                    id="framework"
                    name="framework"
                    className="input"
                    value={advancedFilters.framework}
                    onChange={handleAdvancedFilterChange}
                  >
                    <option value="">All Frameworks</option>
                    <option value="ISO 27001">ISO 27001</option>
                    <option value="NIST CSF">NIST CSF</option>
                    <option value="GDPR">GDPR</option>
                    <option value="HIPAA">HIPAA</option>
                    <option value="PCI DSS">PCI DSS</option>
                  </select>
                </div>
              )}
            </div>
          )}
        </form>
      </div>

      {/* Search Results */}
      {(searchResults.evidence.length > 0 || searchResults.requirements.length > 0) && (
        <div className="space-y-6">
          {/* Evidence Results */}
          {searchResults.evidence.length > 0 && (
            <div className="card">
              <div className="flex justify-between mb-4">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">Evidence Results</h2>
                <button className="btn btn-outline flex items-center text-sm">
                  <FiDownload className="mr-1" />
                  Export
                </button>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        ID
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Name
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Category
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-secondary-light divide-y divide-gray-200 dark:divide-gray-700">
                    {searchResults.evidence.map((item) => (
                      <tr key={item.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          {item.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {item.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {item.category}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              item.status === 'Valid'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                : item.status === 'Invalid'
                                ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                                : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                            }`}
                          >
                            {item.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {item.date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <button className="text-primary hover:text-primary-dark" title="View">
                              <FiEye />
                            </button>
                            <button className="text-primary hover:text-primary-dark" title="Link to Requirement">
                              <FiLink />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Requirements Results */}
          {searchResults.requirements.length > 0 && (
            <div className="card">
              <div className="flex justify-between mb-4">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">Requirements Results</h2>
                <button className="btn btn-outline flex items-center text-sm">
                  <FiDownload className="mr-1" />
                  Export
                </button>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        ID
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Name
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Framework
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Control
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Last Assessed
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-secondary-light divide-y divide-gray-200 dark:divide-gray-700">
                    {searchResults.requirements.map((item) => (
                      <tr key={item.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          {item.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {item.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {item.framework}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {item.control}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              item.status === 'Compliant'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                : item.status === 'Non-Compliant'
                                ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                                : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                            }`}
                          >
                            {item.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {item.lastAssessed}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <button className="text-primary hover:text-primary-dark" title="View">
                              <FiEye />
                            </button>
                            <button className="text-primary hover:text-primary-dark" title="Link Evidence">
                              <FiLink />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      )}

      {/* No Results */}
      {searchQuery && !isLoading && searchResults.evidence.length === 0 && searchResults.requirements.length === 0 && (
        <div className="card py-12">
          <div className="text-center">
            <FiSearch className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-white">No results found</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              We couldn't find anything matching your search. Try adjusting your search terms or filters.
            </p>
          </div>
        </div>
      )}
    </MainLayout>
  );
}
