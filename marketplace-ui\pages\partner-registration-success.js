import Head from "next/head";
import { useRouter } from "next/router";

export default function PartnerRegistrationSuccess() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>Registration Successful | NovaFuse Partner Program</title>
        <meta name="description" content="Your application to the NovaFuse Partner Program has been submitted successfully" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-gray-900">Registration Successful</h1>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Application Submitted Successfully!</h2>
          
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Thank you for applying to the NovaFuse Partner Program. We've received your application and our team will review it shortly. You should receive a confirmation email within the next few minutes.
          </p>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8 max-w-2xl mx-auto">
            <h3 className="font-semibold text-blue-800 mb-2">What happens next?</h3>
            <ol className="text-left text-blue-700 space-y-2 pl-5">
              <li>Our partner team will review your application (typically within 1-2 business days)</li>
              <li>You'll receive an email with your partner account credentials</li>
              <li>You'll get access to our integration documentation and SDK</li>
              <li>Our technical team will schedule an onboarding call to help you get started</li>
            </ol>
          </div>
          
          <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <button 
              onClick={() => router.push("/")}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700"
            >
              Return to Marketplace
            </button>
            <button 
              onClick={() => window.open("mailto:<EMAIL>")}
              className="border border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-blue-50"
            >
              Contact Partner Team
            </button>
          </div>
        </div>
      </main>

      <footer className="bg-gray-800 text-white py-12 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} NovaFuse. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
