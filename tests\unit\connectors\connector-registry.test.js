/**
 * Unit tests for the Connector Registry Service
 */

const connectorRegistryService = require('../../../src/connectors/services/connector-registry');
const { generateTestConnector } = require('../../utils/test-helpers');
const cache = require('../../../src/utils/cache');

// Mock dependencies
jest.mock('../../../src/utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }))
}));

jest.mock('../../../src/utils/performance-monitor', () => ({
  recordRequest: jest.fn()
}));

describe('Connector Registry Service', () => {
  // Clear all mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Clear the connectors map
    connectorRegistryService.connectors.clear();
    
    // Clear the cache
    if (cache.cache) {
      cache.cache.clear();
    }
  });
  
  describe('getAllConnectors', () => {
    it('should return an empty array when no connectors exist', async () => {
      const connectors = await connectorRegistryService.getAllConnectors();
      
      expect(connectors).toEqual([]);
      expect(connectors.length).toBe(0);
    });
    
    it('should return all connectors', async () => {
      // Create test connectors
      const connector1 = generateTestConnector({ name: 'Connector 1' });
      const connector2 = generateTestConnector({ name: 'Connector 2' });
      
      // Add connectors to the registry
      connectorRegistryService.connectors.set(connector1.id, connector1);
      connectorRegistryService.connectors.set(connector2.id, connector2);
      
      const connectors = await connectorRegistryService.getAllConnectors();
      
      expect(connectors.length).toBe(2);
      expect(connectors).toEqual(expect.arrayContaining([
        expect.objectContaining({ id: connector1.id, name: 'Connector 1' }),
        expect.objectContaining({ id: connector2.id, name: 'Connector 2' })
      ]));
    });
    
    it('should filter connectors by status', async () => {
      // Create test connectors with different statuses
      const connector1 = generateTestConnector({ name: 'Connector 1', status: 'draft' });
      const connector2 = generateTestConnector({ name: 'Connector 2', status: 'published' });
      
      // Add connectors to the registry
      connectorRegistryService.connectors.set(connector1.id, connector1);
      connectorRegistryService.connectors.set(connector2.id, connector2);
      
      const connectors = await connectorRegistryService.getAllConnectors({ status: 'published' });
      
      expect(connectors.length).toBe(1);
      expect(connectors[0]).toEqual(expect.objectContaining({ 
        id: connector2.id, 
        name: 'Connector 2',
        status: 'published'
      }));
    });
    
    it('should filter connectors by type', async () => {
      // Create test connectors with different types
      const connector1 = generateTestConnector({ name: 'Connector 1', type: 'source' });
      const connector2 = generateTestConnector({ name: 'Connector 2', type: 'destination' });
      
      // Add connectors to the registry
      connectorRegistryService.connectors.set(connector1.id, connector1);
      connectorRegistryService.connectors.set(connector2.id, connector2);
      
      const connectors = await connectorRegistryService.getAllConnectors({ type: 'destination' });
      
      expect(connectors.length).toBe(1);
      expect(connectors[0]).toEqual(expect.objectContaining({ 
        id: connector2.id, 
        name: 'Connector 2',
        type: 'destination'
      }));
    });
    
    it('should return connectors from cache when available', async () => {
      // Create test connectors
      const connector1 = generateTestConnector({ name: 'Connector 1' });
      const connector2 = generateTestConnector({ name: 'Connector 2' });
      
      // Add connectors to the registry
      connectorRegistryService.connectors.set(connector1.id, connector1);
      connectorRegistryService.connectors.set(connector2.id, connector2);
      
      // First call should get connectors from storage
      await connectorRegistryService.getAllConnectors();
      
      // Add a new connector that shouldn't be in the cached result
      const connector3 = generateTestConnector({ name: 'Connector 3' });
      connectorRegistryService.connectors.set(connector3.id, connector3);
      
      // Second call should get connectors from cache
      const connectors = await connectorRegistryService.getAllConnectors();
      
      // Should only have the first two connectors
      expect(connectors.length).toBe(2);
      expect(connectors).toEqual(expect.arrayContaining([
        expect.objectContaining({ id: connector1.id, name: 'Connector 1' }),
        expect.objectContaining({ id: connector2.id, name: 'Connector 2' })
      ]));
      
      // Should not have the third connector
      expect(connectors).not.toEqual(expect.arrayContaining([
        expect.objectContaining({ id: connector3.id, name: 'Connector 3' })
      ]));
    });
  });
  
  describe('getConnector', () => {
    it('should throw an error when connector does not exist', async () => {
      await expect(connectorRegistryService.getConnector('non-existent-id'))
        .rejects.toThrow('Connector not found with ID: non-existent-id');
    });
    
    it('should return a connector by ID', async () => {
      // Create test connector
      const connector = generateTestConnector({ name: 'Test Connector' });
      
      // Add connector to the registry
      connectorRegistryService.connectors.set(connector.id, connector);
      
      const result = await connectorRegistryService.getConnector(connector.id);
      
      expect(result).toEqual(expect.objectContaining({ 
        id: connector.id, 
        name: 'Test Connector'
      }));
    });
    
    it('should return connector from cache when available', async () => {
      // Create test connector
      const connector = generateTestConnector({ name: 'Test Connector' });
      
      // Add connector to the registry
      connectorRegistryService.connectors.set(connector.id, connector);
      
      // First call should get connector from storage
      await connectorRegistryService.getConnector(connector.id);
      
      // Update the connector in storage
      const updatedConnector = { ...connector, name: 'Updated Connector' };
      connectorRegistryService.connectors.set(connector.id, updatedConnector);
      
      // Second call should get connector from cache
      const result = await connectorRegistryService.getConnector(connector.id);
      
      // Should have the original name, not the updated one
      expect(result).toEqual(expect.objectContaining({ 
        id: connector.id, 
        name: 'Test Connector'
      }));
    });
  });
  
  describe('createConnector', () => {
    it('should create a new connector', async () => {
      const connectorData = {
        name: 'New Connector',
        description: 'A new test connector',
        version: '1.0.0',
        type: 'source'
      };
      
      const connector = await connectorRegistryService.createConnector(connectorData);
      
      expect(connector).toEqual(expect.objectContaining({ 
        name: 'New Connector',
        description: 'A new test connector',
        version: '1.0.0',
        type: 'source'
      }));
      
      // Verify connector was added to the registry
      expect(connectorRegistryService.connectors.get(connector.id)).toBe(connector);
    });
    
    it('should throw an error when connector data is invalid', async () => {
      const invalidData = {
        // Missing required name and description
        version: '1.0.0',
        type: 'source'
      };
      
      await expect(connectorRegistryService.createConnector(invalidData))
        .rejects.toThrow('Invalid connector data');
    });
  });
  
  describe('updateConnector', () => {
    it('should update an existing connector', async () => {
      // Create test connector
      const connector = generateTestConnector({ 
        name: 'Original Name',
        description: 'Original description'
      });
      
      // Add connector to the registry
      connectorRegistryService.connectors.set(connector.id, connector);
      
      const updateData = {
        name: 'Updated Name',
        description: 'Updated description'
      };
      
      const updatedConnector = await connectorRegistryService.updateConnector(connector.id, updateData);
      
      expect(updatedConnector).toEqual(expect.objectContaining({ 
        id: connector.id,
        name: 'Updated Name',
        description: 'Updated description'
      }));
      
      // Verify connector was updated in the registry
      expect(connectorRegistryService.connectors.get(connector.id)).toBe(updatedConnector);
    });
    
    it('should throw an error when connector does not exist', async () => {
      await expect(connectorRegistryService.updateConnector('non-existent-id', { name: 'Updated Name' }))
        .rejects.toThrow('Connector not found with ID: non-existent-id');
    });
    
    it('should throw an error when update data is invalid', async () => {
      // Create test connector
      const connector = generateTestConnector();
      
      // Add connector to the registry
      connectorRegistryService.connectors.set(connector.id, connector);
      
      const invalidData = {
        name: '', // Empty name is invalid
        description: ''
      };
      
      await expect(connectorRegistryService.updateConnector(connector.id, invalidData))
        .rejects.toThrow('Invalid connector data');
    });
  });
  
  describe('deleteConnector', () => {
    it('should delete an existing connector', async () => {
      // Create test connector
      const connector = generateTestConnector();
      
      // Add connector to the registry
      connectorRegistryService.connectors.set(connector.id, connector);
      
      const result = await connectorRegistryService.deleteConnector(connector.id);
      
      expect(result).toBe(true);
      
      // Verify connector was removed from the registry
      expect(connectorRegistryService.connectors.has(connector.id)).toBe(false);
    });
    
    it('should throw an error when connector does not exist', async () => {
      await expect(connectorRegistryService.deleteConnector('non-existent-id'))
        .rejects.toThrow('Connector not found with ID: non-existent-id');
    });
  });
});
