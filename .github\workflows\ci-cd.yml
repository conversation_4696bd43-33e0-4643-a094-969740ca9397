name: NovaConnect UAC CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  GKE_CLUSTER: novafuse-cluster
  GKE_ZONE: us-central1-a
  IMAGE: novafuse-uac
  REGISTRY_HOSTNAME: gcr.io

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:4.4
        ports:
          - 27017:27017
      redis:
        image: redis:6
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Lint code
        run: npm run lint
      
      - name: Run unit tests
        run: npm run test:unit
      
      - name: Run integration tests
        run: npm run test:integration
        env:
          MONGODB_URI: mongodb://localhost:27017/novafuse-test
          REDIS_URI: redis://localhost:6379
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: test-results
          path: coverage/
  
  build:
    name: Build
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true
      
      - name: Configure Docker for GCR
        run: gcloud auth configure-docker ${{ env.REGISTRY_HOSTNAME }}
      
      - name: Get version
        id: package-version
        uses: martinbeentjes/npm-get-version-action@v1.3.1
      
      - name: Build Docker image
        run: |
          docker build \
            --tag "${{ env.REGISTRY_HOSTNAME }}/${{ env.PROJECT_ID }}/${{ env.IMAGE }}:${{ steps.package-version.outputs.current-version }}" \
            --tag "${{ env.REGISTRY_HOSTNAME }}/${{ env.PROJECT_ID }}/${{ env.IMAGE }}:latest" \
            .
      
      - name: Push Docker image
        run: |
          docker push "${{ env.REGISTRY_HOSTNAME }}/${{ env.PROJECT_ID }}/${{ env.IMAGE }}:${{ steps.package-version.outputs.current-version }}"
          docker push "${{ env.REGISTRY_HOSTNAME }}/${{ env.PROJECT_ID }}/${{ env.IMAGE }}:latest"
  
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: (github.event_name == 'push' && github.ref == 'refs/heads/develop') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true
      
      - name: Get GKE credentials
        uses: google-github-actions/get-gke-credentials@v1
        with:
          cluster_name: ${{ env.GKE_CLUSTER }}
          location: ${{ env.GKE_ZONE }}
      
      - name: Get version
        id: package-version
        uses: martinbeentjes/npm-get-version-action@v1.3.1
      
      - name: Deploy to GKE
        run: |
          # Update deployment image
          kubectl set image deployment/novafuse-uac novafuse-uac=${{ env.REGISTRY_HOSTNAME }}/${{ env.PROJECT_ID }}/${{ env.IMAGE }}:${{ steps.package-version.outputs.current-version }} --namespace=staging
          
          # Verify deployment
          kubectl rollout status deployment/novafuse-uac --namespace=staging
      
      - name: Run database migrations
        run: |
          # Apply database migrations
          kubectl exec -it deployment/novafuse-uac --namespace=staging -- npm run migrate
      
      - name: Run smoke tests
        run: |
          # Get service URL
          SERVICE_URL=$(kubectl get service novafuse-uac --namespace=staging -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
          
          # Run smoke tests
          curl -f http://$SERVICE_URL/health
          curl -f http://$SERVICE_URL/metrics
  
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: (github.event_name == 'push' && github.ref == 'refs/heads/main') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true
      
      - name: Get GKE credentials
        uses: google-github-actions/get-gke-credentials@v1
        with:
          cluster_name: ${{ env.GKE_CLUSTER }}
          location: ${{ env.GKE_ZONE }}
      
      - name: Get version
        id: package-version
        uses: martinbeentjes/npm-get-version-action@v1.3.1
      
      - name: Deploy to GKE
        run: |
          # Update deployment image
          kubectl set image deployment/novafuse-uac novafuse-uac=${{ env.REGISTRY_HOSTNAME }}/${{ env.PROJECT_ID }}/${{ env.IMAGE }}:${{ steps.package-version.outputs.current-version }} --namespace=production
          
          # Verify deployment
          kubectl rollout status deployment/novafuse-uac --namespace=production
      
      - name: Run database migrations
        run: |
          # Apply database migrations
          kubectl exec -it deployment/novafuse-uac --namespace=production -- npm run migrate
      
      - name: Run smoke tests
        run: |
          # Get service URL
          SERVICE_URL=$(kubectl get service novafuse-uac --namespace=production -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
          
          # Run smoke tests
          curl -f http://$SERVICE_URL/health
          curl -f http://$SERVICE_URL/metrics
  
  marketplace-submission:
    name: Google Cloud Marketplace Submission
    runs-on: ubuntu-latest
    needs: deploy-production
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true
      
      - name: Get version
        id: package-version
        uses: martinbeentjes/npm-get-version-action@v1.3.1
      
      - name: Prepare marketplace package
        run: |
          # Create marketplace package directory
          mkdir -p marketplace-package
          
          # Copy deployment files
          cp -r k8s/production/* marketplace-package/
          
          # Copy documentation
          cp -r docs/* marketplace-package/
          
          # Create marketplace metadata
          cat > marketplace-package/marketplace.yaml << EOF
          name: NovaConnect UAC
          version: ${{ steps.package-version.outputs.current-version }}
          description: Universal API Connector for seamless API integration
          icon: https://novafuse.io/images/logo.png
          documentationUrl: https://docs.novafuse.io
          supportUrl: https://novafuse.io/support
          categories:
            - API Management
            - Integration
            - Compliance
          EOF
      
      - name: Submit to marketplace
        run: |
          # Submit to marketplace
          gcloud beta marketplace solutions submit \
            --project ${{ env.PROJECT_ID }} \
            --solution-id novafuse-uac \
            --package-path marketplace-package
