import React from 'react';
import {
  <PERSON>agram<PERSON>rame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  VerticalArrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow
} from '../components/DiagramComponents';

const RegulatoryKillSwitch = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>REGULATORY KILL SWITCH</ContainerLabel>
      </ContainerBox>
      
      {/* Main flow components */}
      <ComponentBox left="80px" top="100px" width="130px">
        <ComponentNumber>601</ComponentNumber>
        <ComponentLabel>Fraud Detection</ComponentLabel>
        System
      </ComponentBox>
      
      <Arrow left="210px" top="130px" width="100px" />
      
      <ComponentBox left="320px" top="100px" width="130px">
        <ComponentNumber>602</ComponentNumber>
        <ComponentLabel>Risk Assessment</ComponentLabel>
        Engine
      </ComponentBox>
      
      <Arrow left="450px" top="130px" width="100px" />
      
      <ComponentBox left="560px" top="100px" width="130px">
        <ComponentNumber>603</ComponentNumber>
        <ComponentLabel>Kill Switch</ComponentLabel>
        Trigger
      </ComponentBox>
      
      <Arrow left="625px" top="160px" width="2px" height="100px" />
      
      <ComponentBox left="560px" top="260px" width="130px">
        <ComponentNumber>604</ComponentNumber>
        <ComponentLabel>Agency Report</ComponentLabel>
        Generator
      </ComponentBox>
      
      {/* Regulatory Rule Engine */}
      <ComponentBox left="320px" top="260px" width="130px">
        <ComponentNumber>605</ComponentNumber>
        <ComponentLabel>Regulatory Rule</ComponentLabel>
        Engine
      </ComponentBox>
      
      {/* Connecting arrows */}
      <VerticalArrow left="145px" top="160px" height="100px" />
      
      <Arrow left="145px" top="260px" width="175px" />
      
      <Arrow left="450px" top="290px" width="110px" />
      
      {/* Curved arrow from Kill Switch to Regulatory Rule */}
      <CurvedArrow width="240" height="160" left="560" top="160">
        <path
          d="M 65,0 Q 0,80 -110,100"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="-110,100 -100,92 -103,102"
          fill="#333"
        />
      </CurvedArrow>
      
      {/* Additional components */}
      <ComponentBox left="80px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>606</ComponentNumber>
        <ComponentLabel>FinCEN Form 111</ComponentLabel>
        Generator
      </ComponentBox>
      
      <ComponentBox left="240px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>607</ComponentNumber>
        <ComponentLabel>Notification</ComponentLabel>
        System
      </ComponentBox>
      
      <ComponentBox left="400px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>608</ComponentNumber>
        <ComponentLabel>Evidence</ComponentLabel>
        Preservation
      </ComponentBox>
      
      <ComponentBox left="560px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>609</ComponentNumber>
        <ComponentLabel>Audit Trail</ComponentLabel>
        Generator
      </ComponentBox>
    </DiagramFrame>
  );
};

export default RegulatoryKillSwitch;
