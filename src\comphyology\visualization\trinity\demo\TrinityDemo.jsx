import React, { useState, useEffect } from 'react';
import TrinityDashboard from '../TrinityDashboard';
import { UniversalRippleStack } from '../../../universal_ripple';
import './TrinityDemo.css';

/**
 * Mock NovaConnect
 */
class MockNovaConnect {
  constructor() {
    this.topics = new Map();
    this.subscribers = new Map();
    console.log('MockNovaConnect initialized');
  }
  
  async publish(topic, message) {
    console.log(`[NovaConnect] Publishing to topic: ${topic}`);
    
    // Store message
    if (!this.topics.has(topic)) {
      this.topics.set(topic, []);
    }
    
    this.topics.get(topic).push({
      message,
      timestamp: new Date()
    });
    
    // Notify subscribers
    if (this.subscribers.has(topic)) {
      for (const callback of this.subscribers.get(topic)) {
        try {
          callback(message, topic);
        } catch (error) {
          console.error(`Error notifying subscriber for topic ${topic}:`, error);
        }
      }
    }
    
    return Promise.resolve();
  }
  
  async subscribe(topic, callback) {
    console.log(`[NovaConnect] Subscribing to topic: ${topic}`);
    
    if (!this.subscribers.has(topic)) {
      this.subscribers.set(topic, []);
    }
    
    this.subscribers.get(topic).push(callback);
    
    return Promise.resolve();
  }
  
  async unsubscribe(topic, callback) {
    console.log(`[NovaConnect] Unsubscribing from topic: ${topic}`);
    
    if (this.subscribers.has(topic)) {
      if (callback) {
        const index = this.subscribers.get(topic).indexOf(callback);
        
        if (index !== -1) {
          this.subscribers.get(topic).splice(index, 1);
        }
      } else {
        this.subscribers.delete(topic);
      }
    }
    
    return Promise.resolve();
  }
}

/**
 * Mock NovaThink
 */
class MockNovaThink {
  constructor() {
    this.decisions = [];
    this.beforeDecisionCallbacks = [];
    this.afterDecisionCallbacks = [];
    console.log('MockNovaThink initialized');
  }
  
  makeDecision(context) {
    console.log(`[NovaThink] Making decision with context:`, context);
    
    // Call before decision callbacks
    let enhancedContext = { ...context };
    
    for (const callback of this.beforeDecisionCallbacks) {
      enhancedContext = callback(enhancedContext);
    }
    
    // Make decision
    const decision = {
      id: Math.random().toString(36).substring(2, 15),
      context: enhancedContext,
      result: {
        action: 'approve',
        confidence: 0.85,
        reasoning: 'Decision based on context analysis'
      },
      fairness: 0.9,
      transparency: 0.8,
      ethicalTensor: 0.75,
      accountability: 0.85,
      timestamp: new Date()
    };
    
    // Store decision
    this.decisions.push(decision);
    
    // Call after decision callbacks
    let enhancedDecision = { ...decision };
    
    for (const callback of this.afterDecisionCallbacks) {
      enhancedDecision = callback(enhancedDecision);
    }
    
    return enhancedDecision;
  }
  
  onBeforeDecision(callback) {
    this.beforeDecisionCallbacks.push(callback);
  }
  
  offBeforeDecision(callback) {
    const index = this.beforeDecisionCallbacks.indexOf(callback);
    
    if (index !== -1) {
      this.beforeDecisionCallbacks.splice(index, 1);
    }
  }
  
  onAfterDecision(callback) {
    this.afterDecisionCallbacks.push(callback);
  }
  
  offAfterDecision(callback) {
    const index = this.afterDecisionCallbacks.indexOf(callback);
    
    if (index !== -1) {
      this.afterDecisionCallbacks.splice(index, 1);
    }
  }
}

/**
 * Trinity Demo Component
 */
const TrinityDemo = () => {
  const [rippleStack, setRippleStack] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Initialize the Universal Ripple Stack
  useEffect(() => {
    try {
      // Create mock components
      const novaConnect = new MockNovaConnect();
      const novaThink = new MockNovaThink();
      
      // Initialize Universal Ripple Stack
      console.log('Initializing Universal Ripple Stack...');
      const stack = new UniversalRippleStack({
        novaConnect,
        novaThink,
        enableLogging: true,
        autoStart: false
      });
      
      // Start the stack
      console.log('Starting Universal Ripple Stack...');
      stack.start().then(() => {
        setRippleStack(stack);
        setIsLoading(false);
        
        // Register some data
        console.log('Registering data...');
        
        const threatData = {
          type: 'threat',
          entropy: 0.7,
          phase: Math.PI / 2,
          certainty: 0.8,
          direction: Math.PI / 4,
          magnitude: 0.6
        };
        
        const complianceData = {
          type: 'compliance',
          complexity: 0.6,
          adaptability: 0.7,
          resonance: 0.8,
          environmentalPressure: 0.5
        };
        
        const decisionData = {
          type: 'decision',
          fairness: 0.8,
          transparency: 0.7,
          ethicalTensor: 0.9,
          accountability: 0.8
        };
        
        stack.registerData(threatData);
        stack.registerData(complianceData);
        stack.registerData(decisionData);
      }).catch(err => {
        console.error('Error starting Universal Ripple Stack:', err);
        setError('Failed to start Universal Ripple Stack');
        setIsLoading(false);
      });
    } catch (err) {
      console.error('Error initializing Universal Ripple Stack:', err);
      setError('Failed to initialize Universal Ripple Stack');
      setIsLoading(false);
    }
    
    // Cleanup
    return () => {
      if (rippleStack) {
        rippleStack.stop().catch(err => {
          console.error('Error stopping Universal Ripple Stack:', err);
        });
      }
    };
  }, []);
  
  // Make a decision every 5 seconds
  useEffect(() => {
    if (!rippleStack) return;
    
    const decisionInterval = setInterval(() => {
      try {
        // Make a decision
        const decisionContext = {
          user: 'admin',
          action: 'approve',
          resource: 'sensitive-data',
          riskLevel: Math.random() > 0.5 ? 'medium' : 'low'
        };
        
        rippleStack.makeDecision(decisionContext);
      } catch (err) {
        console.error('Error making decision:', err);
      }
    }, 5000);
    
    return () => clearInterval(decisionInterval);
  }, [rippleStack]);
  
  if (isLoading) {
    return (
      <div className="trinity-demo-loading">
        <div className="loading-spinner"></div>
        <p>Initializing Trinity Visualization...</p>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="trinity-demo-error">
        <h2>Error</h2>
        <p>{error}</p>
      </div>
    );
  }
  
  return (
    <div className="trinity-demo">
      <TrinityDashboard universalRippleStack={rippleStack} />
      
      <div className="trinity-demo-controls">
        <h3>Demo Controls</h3>
        <button onClick={() => {
          const threatData = {
            type: 'threat',
            entropy: Math.random(),
            phase: Math.random() * Math.PI * 2,
            certainty: Math.random(),
            direction: Math.random() * Math.PI * 2,
            magnitude: Math.random()
          };
          
          rippleStack.registerData(threatData);
        }}>
          Register Threat Data
        </button>
        
        <button onClick={() => {
          const complianceData = {
            type: 'compliance',
            complexity: Math.random(),
            adaptability: Math.random(),
            resonance: Math.random(),
            environmentalPressure: Math.random()
          };
          
          rippleStack.registerData(complianceData);
        }}>
          Register Compliance Data
        </button>
        
        <button onClick={() => {
          const decisionData = {
            type: 'decision',
            fairness: Math.random(),
            transparency: Math.random(),
            ethicalTensor: Math.random(),
            accountability: Math.random()
          };
          
          rippleStack.registerData(decisionData);
        }}>
          Register Decision Data
        </button>
        
        <button onClick={() => {
          const decisionContext = {
            user: 'admin',
            action: 'approve',
            resource: 'sensitive-data',
            riskLevel: 'medium'
          };
          
          rippleStack.makeDecision(decisionContext);
        }}>
          Make Decision
        </button>
      </div>
      
      <div className="trinity-demo-info">
        <h3>About the Trinity Visualization</h3>
        <p>
          This visualization demonstrates the "3-in-1 nested Trinities - wheels within wheels" 
          concept of the NovaFuse architecture. The three wheels represent the three levels of 
          the Trinity:
        </p>
        <ul>
          <li><strong>Outer Wheel (Green):</strong> The Ripple Effect Layers - Direct Impact, Adjacent Resonance, Field Saturation</li>
          <li><strong>Middle Wheel (Blue):</strong> The Mathematical Constants - π (Governance), φ (Detection), e (Response)</li>
          <li><strong>Inner Wheel (Purple):</strong> The Implementation Patterns - Quantum State Vectors, Resonance Patterns, Field Matrices</li>
        </ul>
        <p>
          The particles flowing between the wheels represent the energy and information 
          flowing through the system, creating a self-reinforcing cycle of enhancement.
        </p>
      </div>
    </div>
  );
};

export default TrinityDemo;
