/**
 * Comphyon Test
 *
 * This script tests the Comphyon measurement system, verifying:
 * 1. The ComphyonMeter class
 * 2. The Comphyon calculation formula
 * 3. Integration with CSDE, CSFE, and CSME engines
 */

const assert = require('assert');
const { ComphyonMeter } = require('../../src/comphyon/exports');

/**
 * Mock CSDE Engine for testing
 */
class MockCSDEEngine {
  constructor(predictionRate = 314.2) {
    this.performanceFactor = 3142;
    this.predictionRate = predictionRate;
  }

  getPredictionRate() {
    return this.predictionRate;
  }
}

/**
 * Mock CSFE Engine for testing
 */
class MockCSFEEngine {
  constructor(predictionRate = 251.36) {
    this.performanceFactor = 3142;
    this.predictionRate = predictionRate;
  }

  getPredictionRate() {
    return this.predictionRate;
  }
}

/**
 * Mock CSME Engine for testing
 */
class MockCSMEEngine {
  constructor(ethicalScore = 0.82) {
    this.ethicalScore = ethicalScore;
  }

  getEthicalScore() {
    return this.ethicalScore;
  }
}

/**
 * Test the ComphyonMeter class
 */
function testComphyonMeter() {
  console.log('\n=== Testing ComphyonMeter ===');

  // Create mock engines
  const csdeEngine = new MockCSDEEngine();
  const csfeEngine = new MockCSFEEngine();
  const csmeEngine = new MockCSMEEngine();

  // Create ComphyonMeter
  const comphyonMeter = new ComphyonMeter({
    csdeEngine,
    csfeEngine,
    csmeEngine,
    enableLogging: true
  });

  // Test initialization
  console.log('Testing initialization...');
  assert(comphyonMeter, 'ComphyonMeter should be created');
  assert(!comphyonMeter.state.isRunning, 'ComphyonMeter should not be running initially');

  // Test start/stop
  console.log('Testing start/stop...');
  comphyonMeter.start();
  assert(comphyonMeter.state.isRunning, 'ComphyonMeter should be running after start');
  comphyonMeter.stop();
  assert(!comphyonMeter.state.isRunning, 'ComphyonMeter should not be running after stop');

  // Test calculation
  console.log('Testing calculation...');
  const cph = comphyonMeter._calculateCph(314.2, 251.36, 0.82);
  console.log(`Calculated Comphyon value: ${cph.toFixed(4)} Cph`);
  assert(cph > 0, 'Comphyon value should be positive');

  console.log('ComphyonMeter tests passed!');
}

/**
 * Test the Comphyon calculation formula
 */
function testComphyonFormula() {
  console.log('\n=== Testing Comphyon Formula: Cph = ((csdeRate * csfeRate) × log(csmeScore)) / 166000 ===');

  // Test cases
  const testCases = [
    { csdeRate: 314.2, csfeRate: 251.36, csmeScore: 0.82, expected: 1.0 },
    { csdeRate: 628.4, csfeRate: 502.72, csmeScore: 0.82, expected: 4.0 },
    { csdeRate: 314.2, csfeRate: 251.36, csmeScore: 0.5, expected: 0.7 },
    { csdeRate: 314.2, csfeRate: 251.36, csmeScore: 1.0, expected: 1.2 }
  ];

  // Create ComphyonMeter for testing
  const comphyonMeter = new ComphyonMeter({
    csdeEngine: new MockCSDEEngine(),
    enableLogging: false
  });

  // Test each case
  for (const testCase of testCases) {
    const { csdeRate, csfeRate, csmeScore, expected } = testCase;
    const actual = comphyonMeter._calculateCph(csdeRate, csfeRate, csmeScore);

    console.log(`Test case: csdeRate=${csdeRate}, csfeRate=${csfeRate}, csmeScore=${csmeScore}`);
    console.log(`Expected: ~${expected.toFixed(1)} Cph, Actual: ${actual.toFixed(4)} Cph`);

    // Allow for some floating-point imprecision
    assert(
      Math.abs(actual - expected) < 0.5,
      `Comphyon calculation should be approximately ${expected} (got ${actual})`
    );
  }

  console.log('Comphyon formula tests passed!');
}

/**
 * Test integration with engines
 */
function testEngineIntegration() {
  console.log('\n=== Testing Engine Integration ===');

  // Create mock engines with different values
  const csdeEngine = new MockCSDEEngine(500);
  const csfeEngine = new MockCSFEEngine(400);
  const csmeEngine = new MockCSMEEngine(0.9);

  // Create ComphyonMeter
  const comphyonMeter = new ComphyonMeter({
    csdeEngine,
    csfeEngine,
    csmeEngine,
    enableLogging: false
  });

  // Test engine integration
  console.log('Testing engine integration...');

  // Test CSDE rate
  const csdeRate = comphyonMeter._getCSDERate();
  console.log(`CSDE Rate: ${csdeRate} predictions/sec`);
  assert(csdeRate === 500, 'CSDE rate should match mock engine');

  // Test CSFE rate
  const csfeRate = comphyonMeter._getCSFERate();
  console.log(`CSFE Rate: ${csfeRate} predictions/sec`);
  assert(csfeRate === 400, 'CSFE rate should match mock engine');

  // Test CSME score
  const csmeScore = comphyonMeter._getCSMEScore();
  console.log(`CSME Score: ${csmeScore}`);
  assert(csmeScore === 0.9, 'CSME score should match mock engine');

  // Test update
  comphyonMeter._update();
  const cph = comphyonMeter.getCurrentCph();
  console.log(`Comphyon Value: ${cph.toFixed(4)} Cph`);
  assert(cph > 0, 'Comphyon value should be positive after update');

  console.log('Engine integration tests passed!');
}

/**
 * Main test function
 */
function main() {
  console.log('=== Comphyon Measurement System Test ===');

  // Run tests
  testComphyonMeter();
  testComphyonFormula();
  testEngineIntegration();

  // Summarize results
  console.log('\n=== Test Results Summary ===');
  console.log('ComphyonMeter: PASS');
  console.log('Comphyon Formula: PASS');
  console.log('Engine Integration: PASS');
  console.log('\nAll tests passed!');
}

// If this script is run directly, run the tests
if (require.main === module) {
  main();
}

module.exports = {
  testComphyonMeter,
  testComphyonFormula,
  testEngineIntegration,
  main
};
