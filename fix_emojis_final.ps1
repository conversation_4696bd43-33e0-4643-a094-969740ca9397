# Simple script to fix emoji rendering in the dictionary

# Define paths
$dictionaryPath = "d:\\novafuse-api-superstore\\coherence-reality-systems\\Comphyology Master Archive\\3. APPENDIX A - MATH FOUNDATION\\The Comphyological Dictionary 1st Edition.md"
$backupPath = "$dictionaryPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss').md"

# Create a backup of the original file
Copy-Item -Path $dictionaryPath -Destination $backupPath -Force
Write-Host "Created backup at: $backupPath"

# Read the content with UTF-8 encoding
$content = [System.IO.File]::ReadAllText($dictionaryPath, [System.Text.Encoding]::UTF8)

# Define replacement pairs as arrays
$patterns = @(
    # Lightbulb emoji
    [regex]::Escape('ðŸ”‘'),
    # Clipboard emoji
    [regex]::Escape('ðŸ§¬'),
    # Warning emoji (with space)
    [regex]::Escape('ðŸ§ '),
    # Warning emoji (without space)
    [regex]::Escape('ðŸ§'),
    # Greek kappa
    [regex]::Escape('Îº'),
    # Multiplication sign
    [regex]::Escape('Ã—')
)

$replacements = @(
    # Lightbulb emoji
    '💡',
    # Clipboard emoji
    '📋',
    # Warning emoji (with space)
    '⚠️',
    # Warning emoji (without space)
    '⚠️',
    # Greek kappa
    'κ',
    # Multiplication sign
    '×'
)

# Apply all replacements
for ($i = 0; $i -lt $patterns.Count; $i++) {
    $content = $content -replace $patterns[$i], $replacements[$i]
}

# Write the modified content back to the file with UTF-8 encoding
[System.IO.File]::WriteAllText($dictionaryPath, $content.Trim(), [System.Text.Encoding]::UTF8)

Write-Host "Fixed emoji and special character rendering in the dictionary file."
Write-Host "Please review the changes in the file: $dictionaryPath"
