import React, { useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, CircularProgress } from '@mui/material';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import TensorControls from './pages/TensorControls';
import VisualizationControls from './pages/VisualizationControls';
import VisualizationDemo from './pages/VisualizationDemo';
import EnhancedVisualizationDemo from './pages/EnhancedVisualizationDemo';
import AnalyticsControls from './pages/AnalyticsControls';
import Settings from './pages/Settings';
import { useControl } from './contexts/ControlContext';

function App() {
  const { isConnected, isConnecting } = useControl();
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize the app when connected
  React.useEffect(() => {
    if (isConnected && !isInitialized) {
      setIsInitialized(true);
    }
  }, [isConnected, isInitialized]);

  // Show loading screen while connecting
  if (isConnecting || !isConnected) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
          bgcolor: 'background.default',
        }}
      >
        <CircularProgress size={60} />
        <Box sx={{ mt: 2, color: 'text.primary' }}>
          {isConnecting ? 'Connecting to server...' : 'Not connected to server'}
        </Box>
      </Box>
    );
  }

  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/tensor" element={<TensorControls />} />
        <Route path="/visualization" element={<VisualizationControls />} />
        <Route path="/analytics" element={<AnalyticsControls />} />
        <Route path="/settings" element={<Settings />} />
      </Routes>
    </Layout>
  );
}

export default App;
