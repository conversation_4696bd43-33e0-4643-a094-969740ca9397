/**
 * NovaFuse Universal API Connector - Credential Repository
 * 
 * This module provides data access methods for API credentials.
 */

const Credential = require('../models/credential');
const { createLogger } = require('../../utils/logger');

const logger = createLogger('credential-repository');

/**
 * Credential Repository
 * 
 * Provides methods for accessing and manipulating API credentials.
 */
class CredentialRepository {
  /**
   * Get a credential by ID
   * @param {string} id - Credential ID
   * @returns {Promise<Object>} Credential
   */
  async getCredentialById(id) {
    try {
      logger.debug(`Getting credential by ID: ${id}`);
      return await Credential.findOne({ id, isActive: true });
    } catch (error) {
      logger.error(`Error getting credential by ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get credentials by connector ID
   * @param {string} connectorId - Connector ID
   * @param {string} ownerId - Owner ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Credentials
   */
  async getCredentialsByConnector(connectorId, ownerId, options = {}) {
    try {
      const { limit = 100, skip = 0 } = options;
      
      logger.debug(`Getting credentials for connector: ${connectorId}`);
      
      return await Credential.find({ connectorId, ownerId, isActive: true })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      logger.error(`Error getting credentials for connector ${connectorId}:`, error);
      throw error;
    }
  }

  /**
   * Get credentials by owner
   * @param {string} ownerId - Owner ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Credentials
   */
  async getCredentialsByOwner(ownerId, options = {}) {
    try {
      const { limit = 100, skip = 0 } = options;
      
      logger.debug(`Getting credentials for owner: ${ownerId}`);
      
      return await Credential.find({ ownerId, isActive: true })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      logger.error(`Error getting credentials for owner ${ownerId}:`, error);
      throw error;
    }
  }

  /**
   * Create a credential
   * @param {Object} credentialData - Credential data
   * @returns {Promise<Object>} Created credential
   */
  async createCredential(credentialData) {
    try {
      logger.debug('Creating credential');
      
      const credential = new Credential(credentialData);
      return await credential.save();
    } catch (error) {
      logger.error('Error creating credential:', error);
      throw error;
    }
  }

  /**
   * Update a credential
   * @param {string} id - Credential ID
   * @param {Object} credentialData - Updated credential data
   * @returns {Promise<Object>} Updated credential
   */
  async updateCredential(id, credentialData) {
    try {
      logger.debug(`Updating credential: ${id}`);
      
      // Ensure ID is not changed
      delete credentialData.id;
      
      // Update timestamp
      credentialData.updatedAt = new Date();
      
      return await Credential.findOneAndUpdate(
        { id, isActive: true },
        { $set: credentialData },
        { new: true, runValidators: true }
      );
    } catch (error) {
      logger.error(`Error updating credential ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a credential
   * @param {string} id - Credential ID
   * @returns {Promise<boolean>} Whether the credential was deleted
   */
  async deleteCredential(id) {
    try {
      logger.debug(`Deleting credential: ${id}`);
      
      // Soft delete by setting isActive to false
      const result = await Credential.findOneAndUpdate(
        { id },
        { $set: { isActive: false, updatedAt: new Date() } }
      );
      
      return !!result;
    } catch (error) {
      logger.error(`Error deleting credential ${id}:`, error);
      throw error;
    }
  }

  /**
   * Hard delete a credential (for testing and admin purposes)
   * @param {string} id - Credential ID
   * @returns {Promise<boolean>} Whether the credential was deleted
   */
  async hardDeleteCredential(id) {
    try {
      logger.debug(`Hard deleting credential: ${id}`);
      
      const result = await Credential.deleteOne({ id });
      return result.deletedCount > 0;
    } catch (error) {
      logger.error(`Error hard deleting credential ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update credential test result
   * @param {string} id - Credential ID
   * @param {boolean} success - Whether the test was successful
   * @param {string} message - Test result message
   * @returns {Promise<Object>} Updated credential
   */
  async updateTestResult(id, success, message) {
    try {
      logger.debug(`Updating test result for credential: ${id}`);
      
      const credential = await Credential.findOne({ id, isActive: true });
      
      if (!credential) {
        throw new Error(`Credential ${id} not found`);
      }
      
      return await credential.updateTestResult(success, message);
    } catch (error) {
      logger.error(`Error updating test result for credential ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update last used timestamp
   * @param {string} id - Credential ID
   * @returns {Promise<Object>} Updated credential
   */
  async updateLastUsed(id) {
    try {
      logger.debug(`Updating last used timestamp for credential: ${id}`);
      
      const credential = await Credential.findOne({ id, isActive: true });
      
      if (!credential) {
        throw new Error(`Credential ${id} not found`);
      }
      
      return await credential.updateLastUsed();
    } catch (error) {
      logger.error(`Error updating last used timestamp for credential ${id}:`, error);
      throw error;
    }
  }

  /**
   * Count credentials
   * @param {Object} filter - Filter criteria
   * @returns {Promise<number>} Credential count
   */
  async countCredentials(filter = {}) {
    try {
      logger.debug('Counting credentials');
      
      // Add isActive filter by default
      const query = { isActive: true, ...filter };
      
      return await Credential.countDocuments(query);
    } catch (error) {
      logger.error('Error counting credentials:', error);
      throw error;
    }
  }

  /**
   * Get decrypted credentials
   * @param {string} id - Credential ID
   * @returns {Promise<Object>} Decrypted credentials
   */
  async getDecryptedCredentials(id) {
    try {
      logger.debug(`Getting decrypted credentials for: ${id}`);
      
      const credential = await Credential.findOne({ id, isActive: true });
      
      if (!credential) {
        throw new Error(`Credential ${id} not found`);
      }
      
      return credential.getDecryptedCredentials();
    } catch (error) {
      logger.error(`Error getting decrypted credentials for ${id}:`, error);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new CredentialRepository();
