# NovaFuse API Documentation

This document provides comprehensive documentation for the NovaFuse APIs.

## Overview

NovaFuse provides a suite of APIs for Governance, Risk, and Compliance (GRC) management. These APIs are organized into the following categories:

1. **Privacy Management API**: Manage data privacy and compliance
2. **Regulatory Compliance API**: Manage regulatory compliance
3. **Security Assessment API**: Manage security assessments
4. **Control Testing API**: Manage control testing
5. **ESG API**: Manage environmental, social, and governance data
6. **Compliance Automation API**: Automate compliance processes

## Authentication

All NovaFuse APIs require authentication. The following authentication methods are supported:

### JWT Authentication

JWT authentication is used for user authentication. The JWT token is passed in the `Authorization` header as a Bearer token.

```
Authorization: Bearer <token>
```

To obtain a JWT token, use the authentication endpoint:

```
POST /api/auth/login
```

Request body:
```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```

Response:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 3600
}
```

### API Key Authentication

API key authentication is used for service-to-service authentication. The API key is passed in the `X-API-Key` header.

```
X-API-Key: <api-key>
```

## Privacy Management API

The Privacy Management API provides endpoints for managing data privacy and compliance.

### Data Processing Activities

#### List Data Processing Activities

```
GET /api/privacy/management/processing-activities
```

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `sort`: Sort field (default: createdAt)
- `order`: Sort order (asc or desc, default: desc)
- `search`: Search term

Response:
```json
{
  "data": [
    {
      "id": "1",
      "name": "Customer Data Processing",
      "description": "Processing of customer data for order fulfillment",
      "dataCategories": ["Personal Data", "Contact Information"],
      "purposes": ["Order Fulfillment", "Customer Support"],
      "legalBasis": "Contract",
      "retention": "2 years",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalItems": 1,
    "totalPages": 1
  }
}
```

#### Get Data Processing Activity

```
GET /api/privacy/management/processing-activities/{id}
```

Response:
```json
{
  "id": "1",
  "name": "Customer Data Processing",
  "description": "Processing of customer data for order fulfillment",
  "dataCategories": ["Personal Data", "Contact Information"],
  "purposes": ["Order Fulfillment", "Customer Support"],
  "legalBasis": "Contract",
  "retention": "2 years",
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

#### Create Data Processing Activity

```
POST /api/privacy/management/processing-activities
```

Request body:
```json
{
  "name": "Customer Data Processing",
  "description": "Processing of customer data for order fulfillment",
  "dataCategories": ["Personal Data", "Contact Information"],
  "purposes": ["Order Fulfillment", "Customer Support"],
  "legalBasis": "Contract",
  "retention": "2 years"
}
```

Response:
```json
{
  "id": "1",
  "name": "Customer Data Processing",
  "description": "Processing of customer data for order fulfillment",
  "dataCategories": ["Personal Data", "Contact Information"],
  "purposes": ["Order Fulfillment", "Customer Support"],
  "legalBasis": "Contract",
  "retention": "2 years",
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

#### Update Data Processing Activity

```
PUT /api/privacy/management/processing-activities/{id}
```

Request body:
```json
{
  "name": "Customer Data Processing",
  "description": "Processing of customer data for order fulfillment and customer support",
  "dataCategories": ["Personal Data", "Contact Information"],
  "purposes": ["Order Fulfillment", "Customer Support"],
  "legalBasis": "Contract",
  "retention": "3 years"
}
```

Response:
```json
{
  "id": "1",
  "name": "Customer Data Processing",
  "description": "Processing of customer data for order fulfillment and customer support",
  "dataCategories": ["Personal Data", "Contact Information"],
  "purposes": ["Order Fulfillment", "Customer Support"],
  "legalBasis": "Contract",
  "retention": "3 years",
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-02T00:00:00.000Z"
}
```

#### Delete Data Processing Activity

```
DELETE /api/privacy/management/processing-activities/{id}
```

Response:
```json
{
  "message": "Data processing activity deleted successfully"
}
```

### Data Subject Requests

#### List Data Subject Requests

```
GET /api/privacy/management/dsr
```

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `sort`: Sort field (default: createdAt)
- `order`: Sort order (asc or desc, default: desc)
- `status`: Filter by status (pending, in-progress, completed, rejected)

Response:
```json
{
  "data": [
    {
      "id": "1",
      "type": "access",
      "status": "pending",
      "subject": {
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "details": "Request for access to personal data",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalItems": 1,
    "totalPages": 1
  }
}
```

## Regulatory Compliance API

The Regulatory Compliance API provides endpoints for managing regulatory compliance.

### Compliance Frameworks

#### List Compliance Frameworks

```
GET /api/compliance/frameworks
```

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `sort`: Sort field (default: createdAt)
- `order`: Sort order (asc or desc, default: desc)
- `search`: Search term

Response:
```json
{
  "data": [
    {
      "id": "1",
      "name": "GDPR",
      "description": "General Data Protection Regulation",
      "version": "2016/679",
      "category": "Privacy",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalItems": 1,
    "totalPages": 1
  }
}
```

## Security Assessment API

The Security Assessment API provides endpoints for managing security assessments.

### Vulnerabilities

#### List Vulnerabilities

```
GET /api/security/assessment/vulnerabilities
```

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `sort`: Sort field (default: createdAt)
- `order`: Sort order (asc or desc, default: desc)
- `severity`: Filter by severity (low, medium, high, critical)

Response:
```json
{
  "data": [
    {
      "id": "1",
      "name": "SQL Injection",
      "description": "SQL injection vulnerability in login form",
      "severity": "high",
      "status": "open",
      "discoveredAt": "2023-01-01T00:00:00.000Z",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalItems": 1,
    "totalPages": 1
  }
}
```

## Control Testing API

The Control Testing API provides endpoints for managing control testing.

### Controls

#### List Controls

```
GET /api/control/testing/controls
```

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `sort`: Sort field (default: createdAt)
- `order`: Sort order (asc or desc, default: desc)
- `category`: Filter by category (administrative, technical, physical)

Response:
```json
{
  "data": [
    {
      "id": "1",
      "name": "Password Policy",
      "description": "Password policy requiring complex passwords",
      "category": "technical",
      "status": "active",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalItems": 1,
    "totalPages": 1
  }
}
```

## ESG API

The ESG API provides endpoints for managing environmental, social, and governance data.

### Environmental Metrics

#### List Environmental Metrics

```
GET /api/esg/environmental/metrics
```

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `sort`: Sort field (default: createdAt)
- `order`: Sort order (asc or desc, default: desc)
- `category`: Filter by category (emissions, energy, water, waste)

Response:
```json
{
  "data": [
    {
      "id": "1",
      "name": "Carbon Emissions",
      "description": "Carbon emissions in metric tons",
      "category": "emissions",
      "value": 100,
      "unit": "metric tons",
      "period": "2023-Q1",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalItems": 1,
    "totalPages": 1
  }
}
```

## Compliance Automation API

The Compliance Automation API provides endpoints for automating compliance processes.

### Automated Assessments

#### List Automated Assessments

```
GET /api/compliance/automation/assessments
```

Query parameters:
- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `sort`: Sort field (default: createdAt)
- `order`: Sort order (asc or desc, default: desc)
- `status`: Filter by status (pending, in-progress, completed, failed)

Response:
```json
{
  "data": [
    {
      "id": "1",
      "name": "GDPR Compliance Assessment",
      "description": "Automated assessment of GDPR compliance",
      "status": "completed",
      "result": {
        "compliant": true,
        "score": 85,
        "findings": []
      },
      "startedAt": "2023-01-01T00:00:00.000Z",
      "completedAt": "2023-01-01T01:00:00.000Z",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T01:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalItems": 1,
    "totalPages": 1
  }
}
```

## Error Handling

All NovaFuse APIs use consistent error handling. Errors are returned as JSON objects with the following structure:

```json
{
  "error": "Error type",
  "message": "Error message",
  "details": {
    "field": "Error details for specific field"
  }
}
```

### Common Error Types

- `ValidationError`: The request data is invalid
- `AuthenticationError`: Authentication failed
- `AuthorizationError`: The user does not have permission to perform the action
- `NotFoundError`: The requested resource was not found
- `ConflictError`: The request conflicts with the current state of the resource
- `InternalServerError`: An unexpected error occurred on the server

### HTTP Status Codes

- `200 OK`: The request was successful
- `201 Created`: The resource was created successfully
- `204 No Content`: The request was successful but there is no content to return
- `400 Bad Request`: The request data is invalid
- `401 Unauthorized`: Authentication failed
- `403 Forbidden`: The user does not have permission to perform the action
- `404 Not Found`: The requested resource was not found
- `409 Conflict`: The request conflicts with the current state of the resource
- `500 Internal Server Error`: An unexpected error occurred on the server

## Rate Limiting

NovaFuse APIs implement rate limiting to prevent abuse. The rate limits are as follows:

- `100` requests per minute per IP address
- `1000` requests per hour per IP address

Rate limit information is included in the response headers:

- `X-RateLimit-Limit`: The maximum number of requests allowed in the current time window
- `X-RateLimit-Remaining`: The number of requests remaining in the current time window
- `X-RateLimit-Reset`: The time at which the current rate limit window resets (Unix timestamp)

If the rate limit is exceeded, the API will return a `429 Too Many Requests` response with the following body:

```json
{
  "error": "RateLimitExceeded",
  "message": "Rate limit exceeded. Please try again later."
}
```

## Pagination

All NovaFuse APIs that return lists of resources support pagination. Pagination is controlled by the following query parameters:

- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)

Pagination information is included in the response:

```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalItems": 100,
    "totalPages": 10
  }
}
```

## Sorting

All NovaFuse APIs that return lists of resources support sorting. Sorting is controlled by the following query parameters:

- `sort`: Sort field (default varies by endpoint)
- `order`: Sort order (asc or desc, default: desc)

## Filtering

All NovaFuse APIs that return lists of resources support filtering. Filtering is controlled by query parameters that vary by endpoint. Common filter parameters include:

- `search`: Search term (searches across multiple fields)
- `status`: Filter by status
- `category`: Filter by category
- `startDate`: Filter by start date
- `endDate`: Filter by end date

## Versioning

NovaFuse APIs are versioned using URL path versioning. The current version is v1:

```
/api/v1/privacy/management/processing-activities
```

## Conclusion

This document provides comprehensive documentation for the NovaFuse APIs. For more information, please contact the NovaFuse team.
