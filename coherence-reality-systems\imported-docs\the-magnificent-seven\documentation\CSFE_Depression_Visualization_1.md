# CSFE Depression Visualization: Step 1

## Objective
Create a simple visualization component to display the CSFE Depression Prediction results in an intuitive format.

## Approach
We'll create a basic HTML/JavaScript visualization that can be easily integrated into a dashboard or report.

## Implementation

```html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSFE Depression Prediction Dashboard</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .dashboard {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      padding: 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .header h1 {
      color: #333;
      margin-bottom: 5px;
    }
    .header p {
      color: #666;
      margin-top: 0;
    }
    .probability-gauge {
      text-align: center;
      margin-bottom: 30px;
    }
    .gauge-value {
      font-size: 48px;
      font-weight: bold;
      margin: 10px 0;
    }
    .gauge-label {
      font-size: 18px;
      color: #666;
    }
    .timeline-chart, .indicators-chart {
      height: 300px;
      margin-bottom: 30px;
    }
    .warning-level {
      text-align: center;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 30px;
      font-size: 24px;
      font-weight: bold;
    }
    .warning-green {
      background-color: #d4edda;
      color: #155724;
    }
    .warning-yellow {
      background-color: #fff3cd;
      color: #856404;
    }
    .warning-orange {
      background-color: #ffe5d0;
      color: #fd7e14;
    }
    .warning-red {
      background-color: #f8d7da;
      color: #721c24;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    .card h2 {
      margin-top: 0;
      color: #333;
      font-size: 20px;
    }
    .grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }
    @media (max-width: 768px) {
      .grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="dashboard">
    <div class="header">
      <h1>CSFE Depression Prediction Dashboard</h1>
      <p>Unified Field Theory Application to Financial Depression Prediction (2027-2031)</p>
    </div>
    
    <div class="probability-gauge">
      <div class="gauge-label">Overall Depression Probability</div>
      <div class="gauge-value" id="probability-value">78%</div>
      <canvas id="gauge-chart" width="200" height="100"></canvas>
    </div>
    
    <div class="warning-level warning-orange" id="warning-level">
      ELEVATED RISK
    </div>
    
    <div class="card">
      <h2>Timeline Probability (2027-2031)</h2>
      <div class="timeline-chart">
        <canvas id="timeline-chart"></canvas>
      </div>
    </div>
    
    <div class="grid">
      <div class="card">
        <h2>Key Market Indicators</h2>
        <div class="indicators-chart">
          <canvas id="market-chart"></canvas>
        </div>
      </div>
      
      <div class="card">
        <h2>Key Economic Indicators</h2>
        <div class="indicators-chart">
          <canvas id="economic-chart"></canvas>
        </div>
      </div>
    </div>
    
    <div class="card">
      <h2>Key Sentiment Indicators</h2>
      <div class="indicators-chart">
        <canvas id="sentiment-chart"></canvas>
      </div>
    </div>
    
    <div class="card">
      <h2>Recommended Actions</h2>
      <ul id="recommended-actions">
        <li>Begin phased implementation of depression safeguards</li>
        <li>Reduce exposure to high-risk assets</li>
        <li>Prepare policy response options</li>
        <li>Stress test financial systems</li>
      </ul>
    </div>
  </div>

  <script>
    // Sample CSFE Depression Prediction result
    const csfeResult = {
      csfeValue: 27584.32,
      depressionProbability: 0.78,
      performanceFactor: 3142,
      timelineProbability: {
        years: [
          { year: 2027, probability: 0.15 },
          { year: 2028, probability: 0.25 },
          { year: 2029, probability: 0.35 },
          { year: 2030, probability: 0.18 },
          { year: 2031, probability: 0.07 }
        ],
        peakYear: 2029
      },
      marketComponent: {
        features: {
          yieldCurveInversion: 0.7,
          equityValuations: 0.8,
          creditSpreads: 0.65,
          marketBreadth: 0.3,
          volatilityPatterns: 0.7
        }
      },
      economicComponent: {
        features: {
          debtCycles: 0.85,
          monetaryPolicy: 0.7,
          fiscalPolicy: 0.75,
          laborMarket: 0.4,
          demographicShifts: 0.65
        }
      },
      sentimentComponent: {
        features: {
          investorSentiment: 0.3,
          consumerConfidence: 0.35,
          mediaSentiment: 0.3,
          corporateBehavior: 0.25,
          policyUncertainty: 0.75
        }
      }
    };

    // Update probability value
    document.getElementById('probability-value').textContent = `${Math.round(csfeResult.depressionProbability * 100)}%`;

    // Update warning level
    const warningLevel = document.getElementById('warning-level');
    if (csfeResult.depressionProbability >= 0.8) {
      warningLevel.className = 'warning-level warning-red';
      warningLevel.textContent = 'HIGH PROBABILITY OF DEPRESSION';
    } else if (csfeResult.depressionProbability >= 0.6) {
      warningLevel.className = 'warning-level warning-orange';
      warningLevel.textContent = 'ELEVATED RISK';
    } else if (csfeResult.depressionProbability >= 0.3) {
      warningLevel.className = 'warning-level warning-yellow';
      warningLevel.textContent = 'EARLY WARNING SIGNS';
    } else {
      warningLevel.className = 'warning-level warning-green';
      warningLevel.textContent = 'NORMAL CONDITIONS';
    }

    // Create gauge chart
    const gaugeCtx = document.getElementById('gauge-chart').getContext('2d');
    new Chart(gaugeCtx, {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [csfeResult.depressionProbability, 1 - csfeResult.depressionProbability],
          backgroundColor: [
            csfeResult.depressionProbability >= 0.8 ? '#dc3545' :
            csfeResult.depressionProbability >= 0.6 ? '#fd7e14' :
            csfeResult.depressionProbability >= 0.3 ? '#ffc107' : '#28a745',
            '#e9ecef'
          ],
          borderWidth: 0
        }]
      },
      options: {
        cutout: '80%',
        circumference: 180,
        rotation: 270,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: false
          }
        }
      }
    });

    // Create timeline chart
    const timelineCtx = document.getElementById('timeline-chart').getContext('2d');
    new Chart(timelineCtx, {
      type: 'bar',
      data: {
        labels: csfeResult.timelineProbability.years.map(y => y.year),
        datasets: [{
          label: 'Depression Probability',
          data: csfeResult.timelineProbability.years.map(y => y.probability * 100),
          backgroundColor: csfeResult.timelineProbability.years.map(y => 
            y.year === csfeResult.timelineProbability.peakYear ? '#fd7e14' : '#6c757d'
          ),
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Probability (%)'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Year'
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          title: {
            display: true,
            text: `Peak Probability: ${csfeResult.timelineProbability.peakYear}`
          }
        }
      }
    });

    // Create market indicators chart
    const marketCtx = document.getElementById('market-chart').getContext('2d');
    new Chart(marketCtx, {
      type: 'radar',
      data: {
        labels: Object.keys(csfeResult.marketComponent.features).map(key => 
          key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
        ),
        datasets: [{
          label: 'Risk Level',
          data: Object.values(csfeResult.marketComponent.features).map(v => v * 100),
          backgroundColor: 'rgba(253, 126, 20, 0.2)',
          borderColor: '#fd7e14',
          borderWidth: 2,
          pointBackgroundColor: '#fd7e14'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          r: {
            angleLines: {
              display: true
            },
            suggestedMin: 0,
            suggestedMax: 100
          }
        }
      }
    });

    // Create economic indicators chart
    const economicCtx = document.getElementById('economic-chart').getContext('2d');
    new Chart(economicCtx, {
      type: 'radar',
      data: {
        labels: Object.keys(csfeResult.economicComponent.features).map(key => 
          key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
        ),
        datasets: [{
          label: 'Risk Level',
          data: Object.values(csfeResult.economicComponent.features).map(v => v * 100),
          backgroundColor: 'rgba(220, 53, 69, 0.2)',
          borderColor: '#dc3545',
          borderWidth: 2,
          pointBackgroundColor: '#dc3545'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          r: {
            angleLines: {
              display: true
            },
            suggestedMin: 0,
            suggestedMax: 100
          }
        }
      }
    });

    // Create sentiment indicators chart
    const sentimentCtx = document.getElementById('sentiment-chart').getContext('2d');
    new Chart(sentimentCtx, {
      type: 'radar',
      data: {
        labels: Object.keys(csfeResult.sentimentComponent.features).map(key => 
          key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
        ),
        datasets: [{
          label: 'Risk Level',
          data: Object.values(csfeResult.sentimentComponent.features).map(v => v * 100),
          backgroundColor: 'rgba(255, 193, 7, 0.2)',
          borderColor: '#ffc107',
          borderWidth: 2,
          pointBackgroundColor: '#ffc107'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          r: {
            angleLines: {
              display: true
            },
            suggestedMin: 0,
            suggestedMax: 100
          }
        }
      }
    });
  </script>
</body>
</html>
```

## How to Use

1. Save this HTML file as `csfe_depression_dashboard.html`
2. Open it in a web browser to see the visualization
3. To use with real CSFE Depression Engine results:
   - Replace the sample `csfeResult` object with actual results from the engine
   - The dashboard will automatically update to reflect the actual data

## Features

1. **Overall Depression Probability**: Gauge showing the probability of a depression in the 2027-2031 timeframe
2. **Warning Level**: Color-coded warning level based on depression probability
3. **Timeline Probability**: Bar chart showing probability distribution across the target years
4. **Key Indicators**: Radar charts showing risk levels for market, economic, and sentiment indicators
5. **Recommended Actions**: List of recommended actions based on the current risk level

## Next Steps

1. Connect the visualization to the actual CSFE Depression Engine
2. Add historical comparison charts to show how current indicators compare to past depressions
3. Implement interactive features to explore different scenarios
4. Create a printable report format for sharing results

This visualization provides an intuitive way to understand the CSFE Depression Prediction results and helps demonstrate the power of the unified field theory when applied to financial depression prediction.
