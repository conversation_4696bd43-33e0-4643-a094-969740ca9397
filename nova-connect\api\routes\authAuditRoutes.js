/**
 * Authentication Audit Routes
 */

const express = require('express');
const router = express.Router();
const AuthAuditController = require('../controllers/AuthAuditController');
const { authenticate } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticate);

// Get authentication audit logs
router.get('/', (req, res, next) => {
  AuthAuditController.getAuthAuditLogs(req, res, next);
});

// Get authentication audit logs for a specific user
router.get('/user/:userId', (req, res, next) => {
  AuthAuditController.getUserAuthAuditLogs(req, res, next);
});

// Get login history for a specific user
router.get('/user/:userId/login-history', (req, res, next) => {
  AuthAuditController.getUserLoginHistory(req, res, next);
});

// Get failed login attempts
router.get('/failed-logins', (req, res, next) => {
  AuthAuditController.getFailedLoginAttempts(req, res, next);
});

// Get authentication activity summary
router.get('/summary', (req, res, next) => {
  AuthAuditController.getAuthActivitySummary(req, res, next);
});

module.exports = router;
