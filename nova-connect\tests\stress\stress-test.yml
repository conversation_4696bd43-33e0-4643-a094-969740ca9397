config:
  target: "http://localhost:3001"
  phases:
    - duration: 60
      arrivalRate: 5
      rampTo: 50
      name: "Warm up phase"
    - duration: 120
      arrivalRate: 50
      rampTo: 100
      name: "Ramp up load"
    - duration: 300
      arrivalRate: 100
      name: "Sustained load"
    - duration: 60
      arrivalRate: 100
      rampTo: 200
      name: "Peak load"
  defaults:
    headers:
      Content-Type: "application/json"
      Accept: "application/json"
  plugins:
    metrics-by-endpoint: {}
    expect: {}
  processor: "./stress-test-functions.js"

scenarios:
  - name: "Health check endpoints"
    weight: 10
    flow:
      - get:
          url: "/health"
          expect:
            - statusCode: 200
            - contentType: "application/json"
            - hasProperty: "status"
      - get:
          url: "/health/detailed"
          expect:
            - statusCode: 200
            - contentType: "application/json"
            - hasProperty: "memory"
      - get:
          url: "/health/ready"
          expect:
            - statusCode: 200
            - contentType: "application/json"
            - hasProperty: "status"

  - name: "API endpoints"
    weight: 70
    flow:
      - get:
          url: "/api/connectors"
          expect:
            - statusCode: 200
            - contentType: "application/json"
      - post:
          url: "/api/connectors/test"
          json:
            name: "test-connector"
            type: "api"
            config:
              url: "https://httpbin.org/anything"
              method: "POST"
              headers:
                Content-Type: "application/json"
              body: 
                test: true
          expect:
            - statusCode: 200
            - contentType: "application/json"
      - get:
          url: "/api/connectors/test"
          expect:
            - statusCode: 200
            - contentType: "application/json"

  - name: "Data normalization"
    weight: 20
    flow:
      - function: "generateRandomData"
      - post:
          url: "/api/normalize"
          json: "{{ data }}"
          expect:
            - statusCode: 200
            - contentType: "application/json"
            - hasProperty: "normalized"
