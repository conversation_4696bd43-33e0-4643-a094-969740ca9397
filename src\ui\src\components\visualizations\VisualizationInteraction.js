import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Button,
  ButtonGroup,
  Tooltip,
  Slider,
  Typography,
  Popover,
  Paper,
  Divider,
  FormControlLabel,
  Switch,
  IconButton
} from '@mui/material';
import {
  TouchApp as SelectIcon,
  FilterAlt as FilterIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  PanTool as PanIcon,
  Rotate90DegreesCcw as RotateIcon,
  Refresh as ResetIcon,
  HighlightAlt as HighlightIcon,
  Info as InfoIcon
} from '@mui/icons-material';

/**
 * VisualizationInteraction component
 * 
 * Provides controls for interacting with visualizations
 */
function VisualizationInteraction({
  canvasRef,
  visualizationType,
  onInteractionChange,
  onSelectionChange,
  onFilterChange
}) {
  const [interactionMode, setInteractionMode] = useState('orbit');
  const [selectionMode, setSelectionMode] = useState('none');
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);
  const [infoAnchorEl, setInfoAnchorEl] = useState(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [rotationSpeed, setRotationSpeed] = useState(1);
  const [autoRotate, setAutoRotate] = useState(true);
  const [showAxes, setShowAxes] = useState(true);
  const [showGrid, setShowGrid] = useState(true);
  const [valueThreshold, setValueThreshold] = useState([0, 1]);
  const [selectedPoints, setSelectedPoints] = useState([]);
  const [hoveredPoint, setHoveredPoint] = useState(null);
  const raycasterRef = useRef(null);
  
  // Initialize raycaster for selection
  useEffect(() => {
    if (typeof window.THREE !== 'undefined') {
      raycasterRef.current = new window.THREE.Raycaster();
    }
  }, []);
  
  // Handle interaction mode change
  useEffect(() => {
    if (onInteractionChange) {
      onInteractionChange({
        mode: interactionMode,
        zoomLevel,
        rotationSpeed,
        autoRotate,
        showAxes,
        showGrid
      });
    }
  }, [interactionMode, zoomLevel, rotationSpeed, autoRotate, showAxes, showGrid, onInteractionChange]);
  
  // Handle selection mode change
  useEffect(() => {
    if (onSelectionChange) {
      onSelectionChange({
        mode: selectionMode,
        selectedPoints
      });
    }
  }, [selectionMode, selectedPoints, onSelectionChange]);
  
  // Handle filter change
  useEffect(() => {
    if (onFilterChange) {
      onFilterChange({
        valueThreshold
      });
    }
  }, [valueThreshold, onFilterChange]);
  
  // Handle interaction mode button click
  const handleInteractionModeClick = (mode) => {
    setInteractionMode(mode);
  };
  
  // Handle selection mode button click
  const handleSelectionModeClick = (mode) => {
    setSelectionMode(mode === selectionMode ? 'none' : mode);
  };
  
  // Handle filter button click
  const handleFilterClick = (event) => {
    setFilterAnchorEl(event.currentTarget);
  };
  
  // Handle filter close
  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };
  
  // Handle info button click
  const handleInfoClick = (event) => {
    setInfoAnchorEl(event.currentTarget);
  };
  
  // Handle info close
  const handleInfoClose = () => {
    setInfoAnchorEl(null);
  };
  
  // Handle zoom change
  const handleZoomChange = (event, newValue) => {
    setZoomLevel(newValue);
  };
  
  // Handle rotation speed change
  const handleRotationSpeedChange = (event, newValue) => {
    setRotationSpeed(newValue);
  };
  
  // Handle auto rotate change
  const handleAutoRotateChange = (event) => {
    setAutoRotate(event.target.checked);
  };
  
  // Handle show axes change
  const handleShowAxesChange = (event) => {
    setShowAxes(event.target.checked);
  };
  
  // Handle show grid change
  const handleShowGridChange = (event) => {
    setShowGrid(event.target.checked);
  };
  
  // Handle value threshold change
  const handleValueThresholdChange = (event, newValue) => {
    setValueThreshold(newValue);
  };
  
  // Handle reset
  const handleReset = () => {
    setZoomLevel(1);
    setRotationSpeed(1);
    setAutoRotate(true);
    setShowAxes(true);
    setShowGrid(true);
    setValueThreshold([0, 1]);
    setSelectedPoints([]);
    setHoveredPoint(null);
    setInteractionMode('orbit');
    setSelectionMode('none');
  };
  
  // Check if filter is active
  const isFilterOpen = Boolean(filterAnchorEl);
  const filterId = isFilterOpen ? 'filter-popover' : undefined;
  
  // Check if info is active
  const isInfoOpen = Boolean(infoAnchorEl);
  const infoId = isInfoOpen ? 'info-popover' : undefined;
  
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
      {/* Interaction Mode Buttons */}
      <ButtonGroup size="small" aria-label="interaction mode">
        <Tooltip title="Orbit Mode">
          <Button
            variant={interactionMode === 'orbit' ? 'contained' : 'outlined'}
            onClick={() => handleInteractionModeClick('orbit')}
          >
            <RotateIcon fontSize="small" />
          </Button>
        </Tooltip>
        <Tooltip title="Pan Mode">
          <Button
            variant={interactionMode === 'pan' ? 'contained' : 'outlined'}
            onClick={() => handleInteractionModeClick('pan')}
          >
            <PanIcon fontSize="small" />
          </Button>
        </Tooltip>
        <Tooltip title="Zoom Mode">
          <Button
            variant={interactionMode === 'zoom' ? 'contained' : 'outlined'}
            onClick={() => handleInteractionModeClick('zoom')}
          >
            <ZoomInIcon fontSize="small" />
          </Button>
        </Tooltip>
      </ButtonGroup>
      
      {/* Selection and Filter Buttons */}
      <ButtonGroup size="small" aria-label="selection and filter">
        <Tooltip title="Select Points">
          <Button
            variant={selectionMode === 'point' ? 'contained' : 'outlined'}
            onClick={() => handleSelectionModeClick('point')}
          >
            <SelectIcon fontSize="small" />
          </Button>
        </Tooltip>
        <Tooltip title="Highlight Region">
          <Button
            variant={selectionMode === 'region' ? 'contained' : 'outlined'}
            onClick={() => handleSelectionModeClick('region')}
          >
            <HighlightIcon fontSize="small" />
          </Button>
        </Tooltip>
        <Tooltip title="Filter Values">
          <Button
            aria-describedby={filterId}
            variant={isFilterOpen ? 'contained' : 'outlined'}
            onClick={handleFilterClick}
          >
            <FilterIcon fontSize="small" />
          </Button>
        </Tooltip>
      </ButtonGroup>
      
      {/* Reset and Info Buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Tooltip title="Reset View">
          <IconButton size="small" onClick={handleReset}>
            <ResetIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Visualization Info">
          <IconButton
            size="small"
            aria-describedby={infoId}
            onClick={handleInfoClick}
          >
            <InfoIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>
      
      {/* Filter Popover */}
      <Popover
        id={filterId}
        open={isFilterOpen}
        anchorEl={filterAnchorEl}
        onClose={handleFilterClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
      >
        <Paper sx={{ p: 2, width: 250 }}>
          <Typography variant="subtitle2" gutterBottom>
            Filter Values
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <Typography gutterBottom>Value Threshold</Typography>
          <Slider
            value={valueThreshold}
            onChange={handleValueThresholdChange}
            valueLabelDisplay="auto"
            min={0}
            max={1}
            step={0.01}
            sx={{ mb: 2 }}
          />
          
          <Typography variant="caption" color="text.secondary">
            Only show values between {valueThreshold[0]} and {valueThreshold[1]}
          </Typography>
        </Paper>
      </Popover>
      
      {/* Info Popover */}
      <Popover
        id={infoId}
        open={isInfoOpen}
        anchorEl={infoAnchorEl}
        onClose={handleInfoClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
      >
        <Paper sx={{ p: 2, width: 250 }}>
          <Typography variant="subtitle2" gutterBottom>
            Visualization Settings
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <Typography gutterBottom>Zoom Level</Typography>
          <Slider
            value={zoomLevel}
            onChange={handleZoomChange}
            min={0.1}
            max={5}
            step={0.1}
            valueLabelDisplay="auto"
            sx={{ mb: 2 }}
          />
          
          <Typography gutterBottom>Rotation Speed</Typography>
          <Slider
            value={rotationSpeed}
            onChange={handleRotationSpeedChange}
            min={0}
            max={5}
            step={0.1}
            valueLabelDisplay="auto"
            sx={{ mb: 2 }}
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={autoRotate}
                onChange={handleAutoRotateChange}
                size="small"
              />
            }
            label="Auto Rotate"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={showAxes}
                onChange={handleShowAxesChange}
                size="small"
              />
            }
            label="Show Axes"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={showGrid}
                onChange={handleShowGridChange}
                size="small"
              />
            }
            label="Show Grid"
          />
          
          {hoveredPoint && (
            <>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2" gutterBottom>
                Hovered Point
              </Typography>
              <Typography variant="body2">
                Value: {hoveredPoint.value.toFixed(4)}
              </Typography>
              <Typography variant="body2">
                Position: ({hoveredPoint.position.x.toFixed(2)}, {hoveredPoint.position.y.toFixed(2)}, {hoveredPoint.position.z.toFixed(2)})
              </Typography>
            </>
          )}
          
          {selectedPoints.length > 0 && (
            <>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2" gutterBottom>
                Selected Points: {selectedPoints.length}
              </Typography>
              <Typography variant="body2">
                Average Value: {(selectedPoints.reduce((sum, point) => sum + point.value, 0) / selectedPoints.length).toFixed(4)}
              </Typography>
            </>
          )}
        </Paper>
      </Popover>
    </Box>
  );
}

export default VisualizationInteraction;
