/**
 * Tests for the encryption utility
 */

const encryption = require('../../utils/encryption');
const fs = require('fs');
const path = require('path');
const os = require('os');

describe('Encryption Utility', () => {
  describe('Core Functions', () => {
    test('generateRandomBytes should generate random bytes of specified length', () => {
      const length = 32;
      const bytes = encryption.generateRandomBytes(length);
      
      expect(bytes).toBeInstanceOf(Buffer);
      expect(bytes.length).toBe(length);
    });
    
    test('generateRandomString should generate random string of specified length', () => {
      const length = 32;
      const str = encryption.generateRandomString(length);
      
      expect(typeof str).toBe('string');
      expect(str.length).toBe(length);
    });
    
    test('deriveKey should derive a key from a password', () => {
      const password = 'test-password';
      const { key, salt } = encryption.deriveKey(password);
      
      expect(key).toBeInstanceOf(Buffer);
      expect(salt).toBeInstanceOf(Buffer);
      expect(key.length).toBe(encryption.CONFIG.symmetric.keyLength);
      expect(salt.length).toBe(encryption.CONFIG.keyDerivation.saltLength);
      
      // Deriving the key again with the same salt should produce the same key
      const { key: key2 } = encryption.deriveKey(password, salt);
      expect(Buffer.compare(key, key2)).toBe(0);
    });
    
    test('createHash should create a hash with salt', () => {
      const data = 'test-data';
      const { hash, salt } = encryption.createHash(data);
      
      expect(typeof hash).toBe('string');
      expect(typeof salt).toBe('string');
      
      // Verifying the hash should return true
      expect(encryption.verifyHash(data, hash, salt)).toBe(true);
      
      // Verifying with wrong data should return false
      expect(encryption.verifyHash('wrong-data', hash, salt)).toBe(false);
    });
  });
  
  describe('Symmetric Encryption', () => {
    test('encryptSymmetric and decryptSymmetric should work with string data', () => {
      const data = 'test-data';
      const key = encryption.generateRandomBytes(encryption.CONFIG.symmetric.keyLength);
      
      const { encrypted, iv, authTag } = encryption.encryptSymmetric(data, key);
      
      expect(typeof encrypted).toBe('string');
      expect(typeof iv).toBe('string');
      expect(typeof authTag).toBe('string');
      
      const decrypted = encryption.decryptSymmetric(encrypted, iv, authTag, key);
      
      expect(decrypted).toBe(data);
    });
    
    test('encryptSymmetric and decryptSymmetric should work with Buffer data', () => {
      const data = Buffer.from('test-data');
      const key = encryption.generateRandomBytes(encryption.CONFIG.symmetric.keyLength);
      
      const { encrypted, iv, authTag } = encryption.encryptSymmetric(data, key);
      
      expect(typeof encrypted).toBe('string');
      expect(typeof iv).toBe('string');
      expect(typeof authTag).toBe('string');
      
      const decrypted = encryption.decryptSymmetric(encrypted, iv, authTag, key, null);
      
      expect(decrypted).toBeInstanceOf(Buffer);
      expect(Buffer.compare(data, decrypted)).toBe(0);
    });
    
    test('encryptSymmetric and decryptSymmetric should work with password-derived key', () => {
      const data = 'test-data';
      const password = 'test-password';
      
      const { encrypted, iv, authTag } = encryption.encryptSymmetric(data, password);
      
      expect(typeof encrypted).toBe('string');
      expect(typeof iv).toBe('string');
      expect(typeof authTag).toBe('string');
      
      const decrypted = encryption.decryptSymmetric(encrypted, iv, authTag, password);
      
      expect(decrypted).toBe(data);
    });
    
    test('decryptSymmetric should throw an error with wrong key', () => {
      const data = 'test-data';
      const key = encryption.generateRandomBytes(encryption.CONFIG.symmetric.keyLength);
      const wrongKey = encryption.generateRandomBytes(encryption.CONFIG.symmetric.keyLength);
      
      const { encrypted, iv, authTag } = encryption.encryptSymmetric(data, key);
      
      expect(() => {
        encryption.decryptSymmetric(encrypted, iv, authTag, wrongKey);
      }).toThrow();
    });
  });
  
  describe('Asymmetric Encryption', () => {
    test('generateKeyPair should generate a key pair', () => {
      const { publicKey, privateKey } = encryption.generateKeyPair();
      
      expect(typeof publicKey).toBe('string');
      expect(typeof privateKey).toBe('string');
      expect(publicKey).toContain('-----BEGIN PUBLIC KEY-----');
      expect(privateKey).toContain('-----BEGIN PRIVATE KEY-----');
    });
    
    test('encryptAsymmetric and decryptAsymmetric should work with string data', () => {
      const data = 'test-data';
      const { publicKey, privateKey } = encryption.generateKeyPair();
      
      const encrypted = encryption.encryptAsymmetric(data, publicKey);
      
      expect(typeof encrypted).toBe('string');
      
      const decrypted = encryption.decryptAsymmetric(encrypted, privateKey);
      
      expect(decrypted).toBe(data);
    });
    
    test('encryptAsymmetric and decryptAsymmetric should work with Buffer data', () => {
      const data = Buffer.from('test-data');
      const { publicKey, privateKey } = encryption.generateKeyPair();
      
      const encrypted = encryption.encryptAsymmetric(data, publicKey);
      
      expect(typeof encrypted).toBe('string');
      
      const decrypted = encryption.decryptAsymmetric(encrypted, privateKey, null);
      
      expect(decrypted).toBeInstanceOf(Buffer);
      expect(Buffer.compare(data, decrypted)).toBe(0);
    });
    
    test('decryptAsymmetric should throw an error with wrong key', () => {
      const data = 'test-data';
      const { publicKey } = encryption.generateKeyPair();
      const { privateKey: wrongPrivateKey } = encryption.generateKeyPair();
      
      const encrypted = encryption.encryptAsymmetric(data, publicKey);
      
      expect(() => {
        encryption.decryptAsymmetric(encrypted, wrongPrivateKey);
      }).toThrow();
    });
    
    test('createSignature and verifySignature should work', () => {
      const data = 'test-data';
      const { publicKey, privateKey } = encryption.generateKeyPair();
      
      const signature = encryption.createSignature(data, privateKey);
      
      expect(typeof signature).toBe('string');
      
      const isValid = encryption.verifySignature(data, signature, publicKey);
      
      expect(isValid).toBe(true);
      
      // Verifying with wrong data should return false
      const isInvalid = encryption.verifySignature('wrong-data', signature, publicKey);
      
      expect(isInvalid).toBe(false);
    });
  });
  
  describe('API Key Functions', () => {
    test('encryptApiKey and decryptApiKey should work', () => {
      const apiKey = 'test-api-key';
      const masterKey = 'test-master-key';
      
      const encryptedApiKey = encryption.encryptApiKey(apiKey, masterKey);
      
      expect(typeof encryptedApiKey).toBe('string');
      expect(encryptedApiKey.split(':').length).toBe(3);
      
      const decryptedApiKey = encryption.decryptApiKey(encryptedApiKey, masterKey);
      
      expect(decryptedApiKey).toBe(apiKey);
    });
    
    test('decryptApiKey should throw an error with wrong master key', () => {
      const apiKey = 'test-api-key';
      const masterKey = 'test-master-key';
      const wrongMasterKey = 'wrong-master-key';
      
      const encryptedApiKey = encryption.encryptApiKey(apiKey, masterKey);
      
      expect(() => {
        encryption.decryptApiKey(encryptedApiKey, wrongMasterKey);
      }).toThrow();
    });
  });
  
  describe('Utility Functions', () => {
    test('maskSensitiveData should mask sensitive data', () => {
      const data = '1234567890123456';
      const masked = encryption.maskSensitiveData(data);
      
      expect(masked).toBe('1234********3456');
      
      // Short data should be fully masked
      const shortData = '1234';
      const maskedShort = encryption.maskSensitiveData(shortData);
      
      expect(maskedShort).toBe('****');
    });
    
    test('encryptFile and decryptFile should work', async () => {
      const tempDir = os.tmpdir();
      const inputPath = path.join(tempDir, 'test-input.txt');
      const encryptedPath = path.join(tempDir, 'test-encrypted.json');
      const outputPath = path.join(tempDir, 'test-output.txt');
      
      const data = 'test-file-data';
      const key = encryption.generateRandomBytes(encryption.CONFIG.symmetric.keyLength);
      
      // Create test input file
      fs.writeFileSync(inputPath, data);
      
      // Encrypt the file
      await encryption.encryptFile(inputPath, encryptedPath, key);
      
      // Check that the encrypted file exists and is valid JSON
      expect(fs.existsSync(encryptedPath)).toBe(true);
      const encryptedContent = JSON.parse(fs.readFileSync(encryptedPath, 'utf8'));
      expect(encryptedContent).toHaveProperty('encrypted');
      expect(encryptedContent).toHaveProperty('iv');
      expect(encryptedContent).toHaveProperty('authTag');
      expect(encryptedContent).toHaveProperty('metadata');
      
      // Decrypt the file
      await encryption.decryptFile(encryptedPath, outputPath, key);
      
      // Check that the decrypted file matches the original
      expect(fs.existsSync(outputPath)).toBe(true);
      const outputContent = fs.readFileSync(outputPath, 'utf8');
      expect(outputContent).toBe(data);
      
      // Clean up
      fs.unlinkSync(inputPath);
      fs.unlinkSync(encryptedPath);
      fs.unlinkSync(outputPath);
    });
    
    test('secureCompare should compare strings securely', () => {
      const a = 'test-string';
      const b = 'test-string';
      const c = 'different-string';
      
      expect(encryption.secureCompare(a, b)).toBe(true);
      expect(encryption.secureCompare(a, c)).toBe(false);
      expect(encryption.secureCompare(a, a.substring(0, a.length - 1))).toBe(false);
      expect(encryption.secureCompare(null, b)).toBe(false);
      expect(encryption.secureCompare(a, null)).toBe(false);
    });
  });
});
