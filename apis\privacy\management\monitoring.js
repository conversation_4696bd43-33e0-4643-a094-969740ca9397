/**
 * Monitoring Module
 * 
 * This module provides monitoring functionality for the Privacy Management API.
 */

const promClient = require('prom-client');
const logger = require('./logging');

// Create a Registry to register metrics
const register = new promClient.Registry();

// Add default metrics (CPU, memory, etc.)
promClient.collectDefaultMetrics({ register });

// Create custom metrics

// HTTP request counter
const httpRequestsTotal = new promClient.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code'],
  registers: [register],
});

// HTTP request duration histogram
const httpRequestDurationSeconds = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'HTTP request duration in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10],
  registers: [register],
});

// API error counter
const apiErrorsTotal = new promClient.Counter({
  name: 'api_errors_total',
  help: 'Total number of API errors',
  labelNames: ['method', 'route', 'error_type'],
  registers: [register],
});

// Integration request counter
const integrationRequestsTotal = new promClient.Counter({
  name: 'integration_requests_total',
  help: 'Total number of integration requests',
  labelNames: ['integration', 'action', 'status'],
  registers: [register],
});

// Integration request duration histogram
const integrationRequestDurationSeconds = new promClient.Histogram({
  name: 'integration_request_duration_seconds',
  help: 'Integration request duration in seconds',
  labelNames: ['integration', 'action'],
  buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60],
  registers: [register],
});

// Data subject request counter
const dataSubjectRequestsTotal = new promClient.Counter({
  name: 'data_subject_requests_total',
  help: 'Total number of data subject requests',
  labelNames: ['request_type', 'status'],
  registers: [register],
});

// Data subject request processing time histogram
const dataSubjectRequestProcessingTimeSeconds = new promClient.Histogram({
  name: 'data_subject_request_processing_time_seconds',
  help: 'Data subject request processing time in seconds',
  labelNames: ['request_type'],
  buckets: [1, 5, 10, 30, 60, 300, 600, 1800, 3600],
  registers: [register],
});

// Cache hit ratio gauge
const cacheHitRatio = new promClient.Gauge({
  name: 'cache_hit_ratio',
  help: 'Cache hit ratio',
  registers: [register],
});

// Active connections gauge
const activeConnections = new promClient.Gauge({
  name: 'active_connections',
  help: 'Number of active connections',
  registers: [register],
});

/**
 * HTTP monitoring middleware for Express
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const httpMonitoringMiddleware = (req, res, next) => {
  const start = Date.now();
  
  // Get route pattern if available, otherwise use the URL
  const route = req.route ? req.baseUrl + req.route.path : req.path;
  
  // Increment active connections
  activeConnections.inc();
  
  // Log when the response is finished
  res.on('finish', () => {
    // Decrement active connections
    activeConnections.dec();
    
    // Calculate request duration
    const duration = (Date.now() - start) / 1000;
    
    // Record metrics
    httpRequestsTotal.inc({ method: req.method, route, status_code: res.statusCode });
    httpRequestDurationSeconds.observe({ method: req.method, route, status_code: res.statusCode }, duration);
    
    // Log errors
    if (res.statusCode >= 400) {
      apiErrorsTotal.inc({ method: req.method, route, error_type: res.statusCode >= 500 ? 'server_error' : 'client_error' });
    }
  });
  
  next();
};

/**
 * Record an integration request
 * @param {string} integration - Integration name
 * @param {string} action - Action name
 * @param {string} status - Status (success or error)
 * @param {number} duration - Request duration in seconds
 */
const recordIntegrationRequest = (integration, action, status, duration) => {
  integrationRequestsTotal.inc({ integration, action, status });
  integrationRequestDurationSeconds.observe({ integration, action }, duration);
  
  logger.info(`Integration request: ${integration} ${action} ${status} ${duration}s`);
};

/**
 * Record a data subject request
 * @param {string} requestType - Request type
 * @param {string} status - Status
 */
const recordDataSubjectRequest = (requestType, status) => {
  dataSubjectRequestsTotal.inc({ request_type: requestType, status });
  
  logger.info(`Data subject request: ${requestType} ${status}`);
};

/**
 * Record data subject request processing time
 * @param {string} requestType - Request type
 * @param {number} processingTime - Processing time in seconds
 */
const recordDataSubjectRequestProcessingTime = (requestType, processingTime) => {
  dataSubjectRequestProcessingTimeSeconds.observe({ request_type: requestType }, processingTime);
  
  logger.info(`Data subject request processing time: ${requestType} ${processingTime}s`);
};

/**
 * Update cache hit ratio
 * @param {number} hits - Number of cache hits
 * @param {number} misses - Number of cache misses
 */
const updateCacheHitRatio = (hits, misses) => {
  const total = hits + misses;
  const ratio = total > 0 ? hits / total : 0;
  
  cacheHitRatio.set(ratio);
  
  logger.debug(`Cache hit ratio: ${ratio} (${hits}/${total})`);
};

/**
 * Get metrics in Prometheus format
 * @returns {Promise<string>} - Metrics in Prometheus format
 */
const getMetrics = async () => {
  return register.metrics();
};

/**
 * Express route handler for metrics endpoint
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const metricsHandler = async (req, res) => {
  try {
    res.set('Content-Type', register.contentType);
    res.end(await getMetrics());
  } catch (error) {
    logger.error('Error generating metrics', { error });
    res.status(500).send('Error generating metrics');
  }
};

module.exports = {
  httpMonitoringMiddleware,
  recordIntegrationRequest,
  recordDataSubjectRequest,
  recordDataSubjectRequestProcessingTime,
  updateCacheHitRatio,
  getMetrics,
  metricsHandler,
};
