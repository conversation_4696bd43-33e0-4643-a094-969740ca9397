/**
 * Dashboard UI
 *
 * This module provides a web-based UI for the Finite Universe Principle
 * monitoring dashboard. It visualizes metrics, alerts, and provides
 * real-time monitoring of the defense system.
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const { MonitoringDashboard } = require('../monitoring-dashboard');
const { createAnomalyDetector, createPredictiveAnalytics } = require('../ml');

/**
 * DashboardUI class
 *
 * Provides a web-based UI for the monitoring dashboard.
 */
class DashboardUI {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      port: 3000,
      updateInterval: 1000, // Update interval in milliseconds
      enableLogging: true,
      enableRealTimeUpdates: true,
      ...options
    };

    // Initialize dashboard
    this.dashboard = options.dashboard || new MonitoringDashboard({
      enableLogging: this.options.enableLogging,
      monitoringInterval: this.options.updateInterval
    });

    // Initialize ML components
    this.anomalyDetector = options.anomalyDetector || createAnomalyDetector({
      enableLogging: this.options.enableLogging,
      anomalyThreshold: 3.0,
      learningRate: 0.1,
      historyLength: 100
    });

    this.predictiveAnalytics = options.predictiveAnalytics || createPredictiveAnalytics({
      enableLogging: this.options.enableLogging,
      forecastHorizon: 10,
      confidenceLevel: 0.95,
      seasonalityPeriod: 24
    });

    // Initialize Express app
    this.app = express();
    this.server = http.createServer(this.app);
    this.io = socketIo(this.server);

    // Initialize update interval
    this.updateInterval = null;

    // Configure Express
    this._configureExpress();

    // Configure Socket.IO
    this._configureSocketIO();

    if (this.options.enableLogging) {
      console.log('DashboardUI initialized with options:', this.options);
    }
  }

  /**
   * Configure Express
   * @private
   */
  _configureExpress() {
    // Serve static files from the 'public' directory
    this.app.use(express.static(path.join(__dirname, 'public')));

    // API routes
    this.app.get('/api/metrics', (req, res) => {
      res.json(this.dashboard.getCurrentMetrics());
    });

    this.app.get('/api/metrics/history', (req, res) => {
      res.json(this.dashboard.getMetricsHistory());
    });

    this.app.get('/api/alerts', (req, res) => {
      res.json(this.dashboard.getAlerts());
    });

    // Serve the main HTML file for all other routes
    this.app.get('*', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'index.html'));
    });
  }

  /**
   * Configure Socket.IO
   * @private
   */
  _configureSocketIO() {
    this.io.on('connection', (socket) => {
      if (this.options.enableLogging) {
        console.log('Client connected');
      }

      // Send initial data
      socket.emit('metrics', this.dashboard.getCurrentMetrics());
      socket.emit('alerts', this.dashboard.getAlerts());

      // Send initial ML data
      socket.emit('anomalies', this.anomalyDetector.getAnomalyStats());
      socket.emit('forecasts', {
        boundaryViolations: this.predictiveAnalytics.getForecasts().boundaryViolations || [],
        validationFailures: this.predictiveAnalytics.getForecasts().validationFailures || [],
        accuracy: this.predictiveAnalytics.getAccuracy() || {
          boundaryViolations: 0,
          validationFailures: 0,
          domainViolations: { cyber: 0, financial: 0, medical: 0 }
        }
      });

      // Send initial ML settings
      socket.emit('ml-settings', {
        anomalyDetector: {
          anomalyThreshold: this.anomalyDetector.options.anomalyThreshold,
          learningRate: this.anomalyDetector.options.learningRate,
          historyLength: this.anomalyDetector.options.historyLength
        },
        predictiveAnalytics: {
          forecastHorizon: this.predictiveAnalytics.options.forecastHorizon,
          confidenceLevel: this.predictiveAnalytics.options.confidenceLevel,
          seasonalityPeriod: this.predictiveAnalytics.options.seasonalityPeriod
        }
      });

      // Handle ML settings updates
      socket.on('update-ml-settings', (settings) => {
        if (this.options.enableLogging) {
          console.log('Updating ML settings:', settings);
        }

        // Update anomaly detector settings
        if (settings.anomalyDetector) {
          this.anomalyDetector.options.anomalyThreshold = settings.anomalyDetector.anomalyThreshold;
          this.anomalyDetector.options.learningRate = settings.anomalyDetector.learningRate;
          this.anomalyDetector.options.historyLength = settings.anomalyDetector.historyLength;
        }

        // Update predictive analytics settings
        if (settings.predictiveAnalytics) {
          this.predictiveAnalytics.options.forecastHorizon = settings.predictiveAnalytics.forecastHorizon;
          this.predictiveAnalytics.options.confidenceLevel = settings.predictiveAnalytics.confidenceLevel;
          this.predictiveAnalytics.options.seasonalityPeriod = settings.predictiveAnalytics.seasonalityPeriod;
        }

        // Broadcast updated settings to all clients
        this.io.emit('ml-settings', settings);
      });

      // Handle disconnect
      socket.on('disconnect', () => {
        if (this.options.enableLogging) {
          console.log('Client disconnected');
        }
      });
    });
  }

  /**
   * Start the dashboard UI
   * @returns {Promise} - Promise that resolves when the server is started
   */
  start() {
    return new Promise((resolve) => {
      // Start the server
      this.server.listen(this.options.port, () => {
        if (this.options.enableLogging) {
          console.log(`Dashboard UI server running on port ${this.options.port}`);
        }

        // Start the dashboard
        this.dashboard.startMonitoring();

        // Start real-time updates if enabled
        if (this.options.enableRealTimeUpdates) {
          this._startRealTimeUpdates();
        }

        resolve();
      });
    });
  }

  /**
   * Stop the dashboard UI
   */
  stop() {
    // Stop real-time updates
    this._stopRealTimeUpdates();

    // Stop the dashboard
    this.dashboard.stopMonitoring();

    // Close the server
    this.server.close();

    if (this.options.enableLogging) {
      console.log('Dashboard UI server stopped');
    }
  }

  /**
   * Start real-time updates
   * @private
   */
  _startRealTimeUpdates() {
    if (this.updateInterval) {
      return;
    }

    this.updateInterval = setInterval(() => {
      // Get current metrics and alerts
      const metrics = this.dashboard.getCurrentMetrics();
      const alerts = this.dashboard.getAlerts();

      // Process metrics through ML components
      const anomalyResults = this.anomalyDetector.processData(metrics);
      const forecastResults = this.predictiveAnalytics.processMetrics(metrics);

      // Broadcast to all connected clients
      this.io.emit('metrics', metrics);
      this.io.emit('alerts', alerts);

      // Broadcast ML data
      this.io.emit('anomalies', anomalyResults);
      this.io.emit('forecasts', {
        boundaryViolations: forecastResults.forecasts.boundaryViolations || [],
        validationFailures: forecastResults.forecasts.validationFailures || [],
        accuracy: forecastResults.accuracy || {
          boundaryViolations: 0,
          validationFailures: 0,
          domainViolations: { cyber: 0, financial: 0, medical: 0 }
        }
      });
    }, this.options.updateInterval);

    if (this.options.enableLogging) {
      console.log('Real-time updates started');
    }
  }

  /**
   * Stop real-time updates
   * @private
   */
  _stopRealTimeUpdates() {
    if (!this.updateInterval) {
      return;
    }

    clearInterval(this.updateInterval);
    this.updateInterval = null;

    if (this.options.enableLogging) {
      console.log('Real-time updates stopped');
    }
  }

  /**
   * Register a component with the dashboard
   * @param {string} type - Component type
   * @param {Object} component - Component to register
   */
  registerComponent(type, component) {
    switch (type) {
      case 'boundaryEnforcer':
        this.dashboard.registerBoundaryEnforcer(component);
        break;
      case 'domainIntegration':
        this.dashboard.registerDomainIntegration(component);
        break;
      case 'crossDomainValidator':
        this.dashboard.registerCrossDomainValidator(component);
        break;
      case 'domainAdapter':
        if (!component.domain) {
          throw new Error('Domain adapter must have a domain property');
        }
        this.dashboard.registerDomainAdapter(component.domain, component.adapter);
        break;
      default:
        throw new Error(`Unknown component type: ${type}`);
    }

    if (this.options.enableLogging) {
      console.log(`Component registered: ${type}`);
    }
  }

  /**
   * Get the dashboard
   * @returns {MonitoringDashboard} - Monitoring dashboard
   */
  getDashboard() {
    return this.dashboard;
  }

  /**
   * Get the Express app
   * @returns {Object} - Express app
   */
  getApp() {
    return this.app;
  }

  /**
   * Get the HTTP server
   * @returns {Object} - HTTP server
   */
  getServer() {
    return this.server;
  }

  /**
   * Get the Socket.IO instance
   * @returns {Object} - Socket.IO instance
   */
  getIO() {
    return this.io;
  }

  /**
   * Get the anomaly detector
   * @returns {Object} - Anomaly detector
   */
  getAnomalyDetector() {
    return this.anomalyDetector;
  }

  /**
   * Get the predictive analytics
   * @returns {Object} - Predictive analytics
   */
  getPredictiveAnalytics() {
    return this.predictiveAnalytics;
  }
}

/**
 * Create a dashboard UI with recommended settings
 * @param {Object} options - Configuration options
 * @returns {DashboardUI} - Configured dashboard UI
 */
function createDashboardUI(options = {}) {
  // Create ML components if not provided
  const anomalyDetector = options.anomalyDetector || createAnomalyDetector({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    anomalyThreshold: 3.0,
    learningRate: 0.1,
    historyLength: 100
  });

  const predictiveAnalytics = options.predictiveAnalytics || createPredictiveAnalytics({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    forecastHorizon: 10,
    confidenceLevel: 0.95,
    seasonalityPeriod: 24
  });

  return new DashboardUI({
    port: 3000,
    updateInterval: 1000,
    enableLogging: true,
    enableRealTimeUpdates: true,
    anomalyDetector,
    predictiveAnalytics,
    ...options
  });
}

module.exports = {
  DashboardUI,
  createDashboardUI
};
