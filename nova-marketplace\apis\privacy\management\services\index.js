/**
 * Services Index
 *
 * This file exports all services for the Privacy Management API.
 */

const integrationRegistry = require('./integrationRegistry');
const authenticationManager = require('./authenticationManager');
const requestExecutor = require('./requestExecutor');
const dataTransformer = require('./dataTransformer');
const authService = require('./authService');
const subjectRequestService = require('./subjectRequestService');
const dataBreachService = require('./dataBreachService');
const privacyNoticeService = require('./privacyNoticeService');

module.exports = {
  integrationRegistry,
  authenticationManager,
  requestExecutor,
  dataTransformer,
  authService,
  subjectRequestService,
  dataBreachService,
  privacyNoticeService
};
