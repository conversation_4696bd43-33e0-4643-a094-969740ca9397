/**
 * CSDE UI Server
 * 
 * This module provides a simple Express server to serve the CSDE UI.
 */

const express = require('express');
const path = require('path');
const cors = require('cors');
const { CSDEEngine } = require('../index');

// Initialize Express app
const app = express();
const port = process.env.PORT || 3011;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname)));

// Initialize CSDE Engine
const csdeEngine = new CSDEEngine();

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// Calculate CSDE
app.post('/api/calculate', (req, res) => {
  try {
    const { complianceData, gcpData, cyberSafetyData } = req.body;
    
    // Validate input
    if (!complianceData || !gcpData || !cyberSafetyData) {
      return res.status(400).json({
        error: 'Missing required data',
        message: 'complianceData, gcpData, and cyberSafetyData are required'
      });
    }
    
    // Calculate CSDE
    const result = csdeEngine.calculate(complianceData, gcpData, cyberSafetyData);
    
    // Return result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Error calculating CSDE:', error);
    res.status(500).json({
      error: 'CSDE calculation failed',
      message: error.message
    });
  }
});

// Get metrics
app.get('/api/metrics', (req, res) => {
  try {
    const metrics = csdeEngine.getMetrics();
    res.json({
      success: true,
      metrics
    });
  } catch (error) {
    console.error('Error getting metrics:', error);
    res.status(500).json({
      error: 'Failed to get metrics',
      message: error.message
    });
  }
});

// Start server
if (require.main === module) {
  app.listen(port, () => {
    console.log(`CSDE UI server listening on port ${port}`);
    console.log(`Visit http://localhost:${port} to view the CSDE Dashboard`);
  });
}

module.exports = app;
