/**
 * API Example
 *
 * This example demonstrates how to use the Finite Universe Principle API
 * for third-party integration.
 */

const {
  // API
  createFiniteUniverseAPI
} = require('../src/quantum/finite-universe-principle');

/**
 * Start the API server
 */
async function startAPIServer() {
  console.log('Starting API Example...');

  // Create API server
  const api = createFiniteUniverseAPI({
    port: 3001,
    enableLogging: true,
    enableCors: true,
    apiPrefix: '/api/v1'
  });

  // Start the API server
  await api.start();
  console.log(`API server started on http://localhost:${api.options.port}`);

  // Generate some test data
  generateTestData(api);

  // Handle process termination
  process.on('SIGINT', () => {
    console.log('Stopping API server...');
    api.stop();
    process.exit();
  });

  // Print API endpoints
  printAPIEndpoints(api.options.apiPrefix);
}

/**
 * Generate test data for the API
 * @param {Object} api - API instance
 */
function generateTestData(api) {
  // Generate data at regular intervals
  setInterval(async () => {
    // Process cyber domain data
    await api.defenseSystem.processData({
      securityScore: Math.random() * 10,
      threatLevel: Math.random() * 5,
      encryptionStrength: Math.random() > 0.9 ? Infinity : Math.random() * 1000
    }, 'cyber');
    
    // Process financial domain data
    await api.defenseSystem.processData({
      balance: Math.random() * 10000,
      interestRate: Math.random() * 1.5,
      transactionVolume: Math.random() > 0.9 ? 1e20 : Math.random() * 1000000
    }, 'financial');
    
    // Process medical domain data
    await api.defenseSystem.processData({
      heartRate: Math.random() * 200,
      bloodPressure: Math.random() > 0.95 ? -50 : Math.random() * 200,
      temperature: Math.random() > 0.95 ? 60 : 36 + Math.random() * 5
    }, 'medical');
  }, 5000);
}

/**
 * Print API endpoints
 * @param {string} prefix - API prefix
 */
function printAPIEndpoints(prefix) {
  console.log('\nAPI Endpoints:');
  console.log(`GET    ${prefix}/health                - Health check`);
  console.log(`POST   ${prefix}/process               - Process data`);
  console.log(`GET    ${prefix}/metrics               - Get current metrics`);
  console.log(`GET    ${prefix}/metrics/history       - Get metrics history`);
  console.log(`GET    ${prefix}/alerts                - Get alerts`);
  console.log(`POST   ${prefix}/tests/run             - Run tests`);
  console.log(`POST   ${prefix}/metrics/reset         - Reset metrics`);
  
  console.log('\nExample API usage with curl:');
  console.log(`curl -X GET http://localhost:3001${prefix}/health`);
  console.log(`curl -X POST http://localhost:3001${prefix}/process -H "Content-Type: application/json" -d '{"data":{"securityScore":8,"threatLevel":3},"domain":"cyber"}'`);
  console.log(`curl -X GET http://localhost:3001${prefix}/metrics`);
}

/**
 * Example client usage
 */
function exampleClientUsage() {
  console.log('\nExample client usage:');
  console.log(`
// JavaScript client example
async function callAPI() {
  // Process data
  const response = await fetch('http://localhost:3001/api/v1/process', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      data: {
        securityScore: 8,
        threatLevel: 3,
        encryptionStrength: 256
      },
      domain: 'cyber'
    })
  });
  
  const result = await response.json();
  console.log('Processed data:', result);
  
  // Get metrics
  const metricsResponse = await fetch('http://localhost:3001/api/v1/metrics');
  const metrics = await metricsResponse.json();
  console.log('Current metrics:', metrics);
}

callAPI();
  `);
}

// Start the API server
startAPIServer().then(() => {
  exampleClientUsage();
}).catch(error => {
  console.error('Error starting API server:', error);
});
