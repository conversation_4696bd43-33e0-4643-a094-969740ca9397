/**
 * TensorDataService
 * 
 * This service provides methods for fetching tensor data from the backend.
 */

import webSocketService from './WebSocketService';

class TensorDataService {
  /**
   * Get tensor by ID
   * @param {string} id - Tensor ID
   * @returns {Promise<Object>} - Promise that resolves with the tensor
   */
  async getTensor(id) {
    try {
      const response = await webSocketService.send({
        component: 'tensor',
        type: 'get-tensor',
        id
      });
      
      return response.result?.tensor || null;
    } catch (error) {
      console.error(`Error fetching tensor ${id}:`, error);
      throw error;
    }
  }
  
  /**
   * Get all tensors
   * @returns {Promise<Array>} - Promise that resolves with an array of tensors
   */
  async getAllTensors() {
    try {
      const response = await webSocketService.send({
        component: 'tensor',
        type: 'get-all-tensors'
      });
      
      return response.result?.tensors || [];
    } catch (error) {
      console.error('Error fetching all tensors:', error);
      throw error;
    }
  }
  
  /**
   * Get tensor history
   * @param {string} id - Tensor ID
   * @param {number} [limit=100] - Maximum number of history entries
   * @returns {Promise<Array>} - Promise that resolves with an array of tensor history entries
   */
  async getTensorHistory(id, limit = 100) {
    try {
      const response = await webSocketService.send({
        component: 'tensor',
        type: 'get-tensor-history',
        id,
        limit
      });
      
      return response.result?.history || [];
    } catch (error) {
      console.error(`Error fetching tensor history for ${id}:`, error);
      throw error;
    }
  }
  
  /**
   * Get tensor metrics
   * @param {string} id - Tensor ID
   * @returns {Promise<Object>} - Promise that resolves with tensor metrics
   */
  async getTensorMetrics(id) {
    try {
      const response = await webSocketService.send({
        component: 'tensor',
        type: 'get-tensor-metrics',
        id
      });
      
      return response.result?.metrics || {};
    } catch (error) {
      console.error(`Error fetching tensor metrics for ${id}:`, error);
      throw error;
    }
  }
  
  /**
   * Subscribe to tensor updates
   * @param {string} id - Tensor ID
   * @param {Function} callback - Callback function to handle updates
   * @returns {Function} - Unsubscribe function
   */
  subscribeTensorUpdates(id, callback) {
    const handleUpdate = (data) => {
      if (data.id === id) {
        callback(data);
      }
    };
    
    // Subscribe to tensor updates channel
    webSocketService.subscribe('tensor-updates')
      .catch(error => console.error('Error subscribing to tensor updates:', error));
    
    // Add event listener
    webSocketService.addEventListener('message:tensor-updates', handleUpdate);
    
    // Return unsubscribe function
    return () => {
      webSocketService.removeEventListener('message:tensor-updates', handleUpdate);
    };
  }
  
  /**
   * Get real-time tensor data stream
   * @param {string} id - Tensor ID
   * @param {number} [interval=1000] - Update interval in milliseconds
   * @returns {Promise<Object>} - Promise that resolves with stream control object
   */
  async getRealtimeTensorStream(id, interval = 1000) {
    try {
      // Start the stream
      const response = await webSocketService.send({
        component: 'tensor',
        type: 'start-tensor-stream',
        id,
        interval
      });
      
      const streamId = response.result?.streamId;
      
      if (!streamId) {
        throw new Error('Failed to start tensor stream');
      }
      
      // Create stream control object
      const streamControl = {
        streamId,
        isActive: true,
        
        // Stop the stream
        stop: async () => {
          if (!streamControl.isActive) return;
          
          try {
            await webSocketService.send({
              component: 'tensor',
              type: 'stop-tensor-stream',
              streamId
            });
            
            streamControl.isActive = false;
          } catch (error) {
            console.error(`Error stopping tensor stream ${streamId}:`, error);
            throw error;
          }
        },
        
        // Change the update interval
        setInterval: async (newInterval) => {
          if (!streamControl.isActive) return;
          
          try {
            await webSocketService.send({
              component: 'tensor',
              type: 'update-tensor-stream',
              streamId,
              interval: newInterval
            });
          } catch (error) {
            console.error(`Error updating tensor stream ${streamId}:`, error);
            throw error;
          }
        }
      };
      
      return streamControl;
    } catch (error) {
      console.error(`Error starting tensor stream for ${id}:`, error);
      throw error;
    }
  }
  
  /**
   * Generate sample tensor data
   * @param {Object} options - Options for generating sample data
   * @returns {Object} - Sample tensor data
   */
  generateSampleTensor(options = {}) {
    const {
      dataType = 'sine',
      dataSize = 100,
      noiseLevel = 0,
      dimensions = [10, 10, 1]
    } = options;
    
    const values = [];
    
    switch (dataType) {
      case 'sine':
        for (let i = 0; i < dataSize; i++) {
          const t = i / dataSize;
          const value = Math.sin(t * Math.PI * 4) * 0.5 + 0.5;
          values.push(value + (Math.random() - 0.5) * noiseLevel);
        }
        break;
        
      case 'cosine':
        for (let i = 0; i < dataSize; i++) {
          const t = i / dataSize;
          const value = Math.cos(t * Math.PI * 4) * 0.5 + 0.5;
          values.push(value + (Math.random() - 0.5) * noiseLevel);
        }
        break;
        
      case 'square':
        for (let i = 0; i < dataSize; i++) {
          const t = i / dataSize;
          const value = Math.sin(t * Math.PI * 4) > 0 ? 1 : 0;
          values.push(value + (Math.random() - 0.5) * noiseLevel);
        }
        break;
        
      case 'sawtooth':
        for (let i = 0; i < dataSize; i++) {
          const t = i / dataSize;
          const value = (t * 4) % 1;
          values.push(value + (Math.random() - 0.5) * noiseLevel);
        }
        break;
        
      case 'triangle':
        for (let i = 0; i < dataSize; i++) {
          const t = i / dataSize;
          const value = Math.abs(((t * 4) % 2) - 1);
          values.push(value + (Math.random() - 0.5) * noiseLevel);
        }
        break;
        
      case 'random':
        for (let i = 0; i < dataSize; i++) {
          values.push(Math.random());
        }
        break;
        
      case 'harmonics':
        for (let i = 0; i < dataSize; i++) {
          const t = i / dataSize;
          const value = (
            Math.sin(t * Math.PI * 2) * 0.5 +
            Math.sin(t * Math.PI * 4) * 0.25 +
            Math.sin(t * Math.PI * 8) * 0.125 +
            Math.sin(t * Math.PI * 16) * 0.0625
          ) * 0.5 + 0.5;
          values.push(value + (Math.random() - 0.5) * noiseLevel);
        }
        break;
        
      case 'resonance':
        for (let i = 0; i < dataSize; i++) {
          const t = i / dataSize;
          const resonance = 0.1; // Resonance factor
          const value = Math.sin(t * Math.PI * 4) / (1 - resonance * Math.sin(t * Math.PI * 4));
          // Normalize to [0, 1]
          const normalizedValue = (value + 10) / 20;
          values.push(Math.max(0, Math.min(1, normalizedValue + (Math.random() - 0.5) * noiseLevel)));
        }
        break;
        
      default:
        for (let i = 0; i < dataSize; i++) {
          values.push(0.5);
        }
    }
    
    return {
      values,
      dimensions,
      health: 0.95,
      entropyContainment: 0.02,
      timestamp: Date.now()
    };
  }
}

// Create singleton instance
const tensorDataService = new TensorDataService();

export default tensorDataService;
