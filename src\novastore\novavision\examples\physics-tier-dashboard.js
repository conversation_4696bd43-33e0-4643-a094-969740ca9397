/**
 * Physics Tier Dashboard Metadata Example
 * 
 * This file demonstrates how to define metadata for a Physics tier dashboard
 * that will be used by NovaVision to generate the UI.
 */

const { dashboard, widget } = require('../metadata-schema');

/**
 * Physics Tier Dashboard Metadata
 */
const physicsTierDashboard = {
  ...dashboard,
  id: 'physics-dashboard',
  name: 'Physics Tier Dashboard',
  description: 'Real-time monitoring dashboard for the Physics tier',
  tier: 'physics',
  componentType: 'nervous-system',
  visualizationType: 'dashboard',
  
  dataSource: {
    endpoint: '/api/v1/nervous-system/metrics',
    method: 'GET',
    refreshInterval: 1000,
    cache: false
  },
  
  layout: {
    template: 'grid',
    sections: [
      {
        id: 'header',
        name: 'Header',
        layout: 'flex',
        items: [
          {
            id: 'title',
            type: 'text',
            content: 'Physics Tier Dashboard'
          },
          {
            id: 'status',
            type: 'status',
            dataPath: 'status'
          }
        ]
      },
      {
        id: 'metrics',
        name: 'Key Metrics',
        layout: 'grid',
        items: [
          {
            id: 'latency',
            type: 'metric',
            name: 'Latency',
            dataPath: 'metrics.averageLatency',
            format: 'time',
            unit: 'ms',
            threshold: {
              warning: 0.05,
              critical: 0.07
            }
          },
          {
            id: 'throughput',
            type: 'metric',
            name: 'Throughput',
            dataPath: 'metrics.throughput',
            format: 'number',
            unit: 'events/sec',
            threshold: {
              warning: 60000,
              critical: 50000
            }
          },
          {
            id: 'success-rate',
            type: 'metric',
            name: 'Success Rate',
            dataPath: 'metrics.successRate',
            format: 'percentage',
            threshold: {
              warning: 99.9,
              critical: 99
            }
          },
          {
            id: 'wilson-loops',
            type: 'metric',
            name: 'Wilson Loops',
            dataPath: 'metrics.wilsonLoops.total',
            format: 'number'
          }
        ]
      },
      {
        id: 'charts',
        name: 'Performance Charts',
        layout: 'grid',
        items: [
          {
            id: 'latency-chart',
            type: 'chart',
            name: 'Latency Over Time',
            chartType: 'line',
            dataPath: 'metrics.latencyHistory',
            xAxis: {
              dataKey: 'timestamp',
              name: 'Time',
              type: 'time'
            },
            yAxis: {
              dataKey: 'value',
              name: 'Latency (ms)',
              type: 'number'
            },
            threshold: {
              warning: 0.05,
              critical: 0.07
            }
          },
          {
            id: 'throughput-chart',
            type: 'chart',
            name: 'Throughput Over Time',
            chartType: 'line',
            dataPath: 'metrics.throughputHistory',
            xAxis: {
              dataKey: 'timestamp',
              name: 'Time',
              type: 'time'
            },
            yAxis: {
              dataKey: 'value',
              name: 'Throughput (events/sec)',
              type: 'number'
            }
          }
        ]
      },
      {
        id: 'wilson-loops',
        name: 'Wilson Loops',
        layout: 'table',
        items: [
          {
            id: 'wilson-loops-table',
            type: 'table',
            name: 'Active Wilson Loops',
            dataPath: 'wilsonLoops',
            columns: [
              {
                id: 'id',
                name: 'ID',
                dataPath: 'id',
                sortable: true
              },
              {
                id: 'status',
                name: 'Status',
                dataPath: 'status',
                sortable: true,
                format: 'status'
              },
              {
                id: 'created-at',
                name: 'Created At',
                dataPath: 'createdAt',
                sortable: true,
                format: 'datetime'
              },
              {
                id: 'csde-value',
                name: 'CSDE Value',
                dataPath: 'csdeValue',
                sortable: true,
                format: 'number'
              },
              {
                id: 'actions',
                name: 'Actions',
                type: 'actions',
                actions: [
                  {
                    id: 'view',
                    name: 'View',
                    icon: 'visibility',
                    handler: 'viewWilsonLoop'
                  },
                  {
                    id: 'validate',
                    name: 'Validate',
                    icon: 'check',
                    handler: 'validateWilsonLoop',
                    condition: 'item.status === "OPEN"'
                  },
                  {
                    id: 'close',
                    name: 'Close',
                    icon: 'close',
                    handler: 'closeWilsonLoop',
                    condition: 'item.status === "VALIDATED"'
                  }
                ]
              }
            ],
            pagination: {
              enabled: true,
              pageSize: 10,
              pageSizeOptions: [5, 10, 20, 50]
            },
            sorting: {
              enabled: true,
              defaultColumn: 'created-at',
              defaultDirection: 'desc'
            },
            filtering: {
              enabled: true,
              filters: [
                {
                  id: 'status',
                  name: 'Status',
                  type: 'select',
                  options: [
                    { value: 'OPEN', label: 'Open' },
                    { value: 'VALIDATED', label: 'Validated' },
                    { value: 'CLOSED', label: 'Closed' }
                  ]
                }
              ]
            }
          }
        ]
      },
      {
        id: 'remediation',
        name: 'Remediation Actions',
        layout: 'table',
        items: [
          {
            id: 'remediation-table',
            type: 'table',
            name: 'Recent Remediation Actions',
            dataPath: 'remediationActions',
            columns: [
              {
                id: 'id',
                name: 'ID',
                dataPath: 'id',
                sortable: true
              },
              {
                id: 'type',
                name: 'Type',
                dataPath: 'type',
                sortable: true
              },
              {
                id: 'status',
                name: 'Status',
                dataPath: 'status',
                sortable: true,
                format: 'status'
              },
              {
                id: 'created-at',
                name: 'Created At',
                dataPath: 'createdAt',
                sortable: true,
                format: 'datetime'
              },
              {
                id: 'actions',
                name: 'Actions',
                type: 'actions',
                actions: [
                  {
                    id: 'view',
                    name: 'View',
                    icon: 'visibility',
                    handler: 'viewRemediationAction'
                  }
                ]
              }
            ],
            pagination: {
              enabled: true,
              pageSize: 10,
              pageSizeOptions: [5, 10, 20, 50]
            },
            sorting: {
              enabled: true,
              defaultColumn: 'created-at',
              defaultDirection: 'desc'
            }
          }
        ]
      }
    ]
  },
  
  dashboard: {
    refreshInterval: 1000,
    widgets: [
      {
        ...widget,
        id: 'latency-gauge',
        name: 'Latency',
        type: 'gauge',
        size: 'small',
        position: { x: 0, y: 0, width: 1, height: 1 },
        dataSource: {
          endpoint: '/api/v1/nervous-system/metrics',
          method: 'GET',
          refreshInterval: 1000
        },
        config: {
          value: 'metrics.averageLatency',
          min: 0,
          max: 0.1,
          thresholds: [
            { value: 0.05, color: 'success' },
            { value: 0.07, color: 'warning' },
            { value: 0.1, color: 'danger' }
          ],
          unit: 'ms',
          decimals: 3
        }
      },
      {
        ...widget,
        id: 'throughput-gauge',
        name: 'Throughput',
        type: 'gauge',
        size: 'small',
        position: { x: 1, y: 0, width: 1, height: 1 },
        dataSource: {
          endpoint: '/api/v1/nervous-system/metrics',
          method: 'GET',
          refreshInterval: 1000
        },
        config: {
          value: 'metrics.throughput',
          min: 0,
          max: 100000,
          thresholds: [
            { value: 50000, color: 'danger' },
            { value: 60000, color: 'warning' },
            { value: 69000, color: 'success' }
          ],
          unit: 'events/sec',
          decimals: 0
        }
      },
      {
        ...widget,
        id: 'success-rate-gauge',
        name: 'Success Rate',
        type: 'gauge',
        size: 'small',
        position: { x: 2, y: 0, width: 1, height: 1 },
        dataSource: {
          endpoint: '/api/v1/nervous-system/metrics',
          method: 'GET',
          refreshInterval: 1000
        },
        config: {
          value: 'metrics.successRate',
          min: 95,
          max: 100,
          thresholds: [
            { value: 99, color: 'danger' },
            { value: 99.9, color: 'warning' },
            { value: 100, color: 'success' }
          ],
          unit: '%',
          decimals: 2
        }
      }
    ]
  },
  
  actions: [
    {
      id: 'refresh',
      name: 'Refresh',
      type: 'button',
      handler: 'refreshDashboard',
      icon: 'refresh'
    },
    {
      id: 'export',
      name: 'Export',
      type: 'button',
      handler: 'exportDashboard',
      icon: 'download'
    }
  ],
  
  permissions: ['physics-tier:view'],
  featureFlags: ['physics-tier-dashboard']
};

module.exports = physicsTierDashboard;
