/**
 * Onboarding Service
 * 
 * This module provides utilities for managing user onboarding.
 */

/**
 * Onboarding Service class
 */
class OnboardingService {
  /**
   * Constructor
   * 
   * @param {Object} options - Service options
   * @param {string} [options.storageKey='novavision_onboarding'] - Local storage key for onboarding state
   * @param {string} [options.apiUrl='/api/onboarding'] - API URL for onboarding data
   * @param {Function} [options.onError] - Error handler
   */
  constructor(options = {}) {
    this.storageKey = options.storageKey || 'novavision_onboarding';
    this.apiUrl = options.apiUrl || '/api/onboarding';
    this.onError = options.onError || console.error;
    this.tours = {};
  }
  
  /**
   * Register a tour
   * 
   * @param {string} tourId - Tour ID
   * @param {Object} tourConfig - Tour configuration
   */
  registerTour(tourId, tourConfig) {
    this.tours[tourId] = tourConfig;
  }
  
  /**
   * Get a tour configuration
   * 
   * @param {string} tourId - Tour ID
   * @returns {Object|null} - Tour configuration
   */
  getTour(tourId) {
    return this.tours[tourId] || null;
  }
  
  /**
   * Get all registered tours
   * 
   * @returns {Object} - All registered tours
   */
  getAllTours() {
    return { ...this.tours };
  }
  
  /**
   * Save onboarding progress
   * 
   * @param {string} tourId - Tour ID
   * @param {boolean} completed - Whether the tour was completed
   * @param {number} lastStep - Last completed step
   * @returns {Promise<Object>} - Saved progress
   */
  async saveProgress(tourId, completed, lastStep) {
    try {
      // Save to local storage
      const progress = this.getLocalProgress();
      
      progress[tourId] = {
        completed,
        lastStep,
        timestamp: new Date().toISOString()
      };
      
      localStorage.setItem(this.storageKey, JSON.stringify(progress));
      
      // Save to API if available
      try {
        const response = await fetch(`${this.apiUrl}/progress`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            tourId,
            completed,
            lastStep
          })
        });
        
        if (!response.ok) {
          throw new Error('Failed to save onboarding progress to API');
        }
        
        return await response.json();
      } catch (apiError) {
        // Fail silently if API is not available
        console.warn('Failed to save onboarding progress to API:', apiError);
        
        return {
          tourId,
          completed,
          lastStep,
          timestamp: new Date().toISOString(),
          source: 'local'
        };
      }
    } catch (error) {
      this.onError('Error saving onboarding progress:', error);
      throw error;
    }
  }
  
  /**
   * Get onboarding progress
   * 
   * @returns {Object} - Onboarding progress
   */
  getLocalProgress() {
    try {
      const progress = localStorage.getItem(this.storageKey);
      return progress ? JSON.parse(progress) : {};
    } catch (error) {
      this.onError('Error getting onboarding progress:', error);
      return {};
    }
  }
  
  /**
   * Get onboarding progress from API
   * 
   * @returns {Promise<Object>} - Onboarding progress
   */
  async getApiProgress() {
    try {
      const response = await fetch(`${this.apiUrl}/progress`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to get onboarding progress from API');
      }
      
      return await response.json();
    } catch (error) {
      this.onError('Error getting onboarding progress from API:', error);
      
      // Return local progress if API is not available
      return {
        progress: this.getLocalProgress(),
        source: 'local'
      };
    }
  }
  
  /**
   * Sync local progress with API
   * 
   * @returns {Promise<Object>} - Synced progress
   */
  async syncProgress() {
    try {
      // Get local progress
      const localProgress = this.getLocalProgress();
      
      // Get API progress
      let apiProgress;
      try {
        const response = await fetch(`${this.apiUrl}/progress`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        if (!response.ok) {
          throw new Error('Failed to get onboarding progress from API');
        }
        
        apiProgress = await response.json();
      } catch (apiError) {
        // Use local progress if API is not available
        console.warn('Failed to get onboarding progress from API:', apiError);
        apiProgress = { progress: {} };
      }
      
      // Merge progress (prefer API for completed tours)
      const mergedProgress = { ...localProgress };
      
      Object.entries(apiProgress.progress || {}).forEach(([tourId, tourProgress]) => {
        // If API has a completed tour or a more recent timestamp, use it
        if (
          tourProgress.completed ||
          !mergedProgress[tourId] ||
          (tourProgress.timestamp && mergedProgress[tourId].timestamp && 
           new Date(tourProgress.timestamp) > new Date(mergedProgress[tourId].timestamp))
        ) {
          mergedProgress[tourId] = tourProgress;
        }
      });
      
      // Save merged progress to local storage
      localStorage.setItem(this.storageKey, JSON.stringify(mergedProgress));
      
      // Save merged progress to API
      try {
        await fetch(`${this.apiUrl}/progress/sync`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            progress: mergedProgress
          })
        });
      } catch (syncError) {
        // Fail silently if API is not available
        console.warn('Failed to sync onboarding progress with API:', syncError);
      }
      
      return {
        progress: mergedProgress,
        source: 'merged'
      };
    } catch (error) {
      this.onError('Error syncing onboarding progress:', error);
      
      return {
        progress: this.getLocalProgress(),
        source: 'local'
      };
    }
  }
  
  /**
   * Reset onboarding progress
   * 
   * @param {string} [tourId] - Tour ID to reset (if not provided, all tours will be reset)
   * @returns {Promise<Object>} - Reset result
   */
  async resetProgress(tourId) {
    try {
      // Reset local progress
      const progress = this.getLocalProgress();
      
      if (tourId) {
        delete progress[tourId];
      } else {
        Object.keys(progress).forEach(key => {
          delete progress[key];
        });
      }
      
      localStorage.setItem(this.storageKey, JSON.stringify(progress));
      
      // Reset API progress
      try {
        const response = await fetch(`${this.apiUrl}/progress/reset`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            tourId
          })
        });
        
        if (!response.ok) {
          throw new Error('Failed to reset onboarding progress in API');
        }
        
        return await response.json();
      } catch (apiError) {
        // Fail silently if API is not available
        console.warn('Failed to reset onboarding progress in API:', apiError);
        
        return {
          success: true,
          source: 'local'
        };
      }
    } catch (error) {
      this.onError('Error resetting onboarding progress:', error);
      throw error;
    }
  }
  
  /**
   * Get recommended tours for the user
   * 
   * @returns {Promise<Array>} - Recommended tours
   */
  async getRecommendedTours() {
    try {
      // Get completed tours
      const progress = this.getLocalProgress();
      const completedTourIds = Object.entries(progress)
        .filter(([, tourProgress]) => tourProgress.completed)
        .map(([tourId]) => tourId);
      
      // Get all tours
      const allTours = this.getAllTours();
      
      // Filter out completed tours and sort by priority
      const recommendedTours = Object.entries(allTours)
        .filter(([tourId]) => !completedTourIds.includes(tourId))
        .map(([tourId, tourConfig]) => ({
          id: tourId,
          ...tourConfig,
          priority: tourConfig.priority || 0
        }))
        .sort((a, b) => b.priority - a.priority);
      
      return recommendedTours;
    } catch (error) {
      this.onError('Error getting recommended tours:', error);
      return [];
    }
  }
}

// Create singleton instance
const onboardingService = new OnboardingService();

export default onboardingService;
