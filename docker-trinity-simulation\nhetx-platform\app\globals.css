@tailwind base;
@tailwind components;
@tailwind utilities;

/* NHET-X Consciousness Styling */
@layer base {
  :root {
    --consciousness-primary: 0 255 255; /* <PERSON>an */
    --consciousness-secondary: 255 0 255; /* Magenta */
    --consciousness-tertiary: 255 255 0; /* Yellow */
    --consciousness-bg: 0 0 0; /* Black */
    --consciousness-surface: 20 20 40; /* Dark Blue */
  }

  * {
    @apply border-gray-800;
  }

  body {
    @apply bg-black text-white;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Consciousness Field Animations */
@keyframes consciousness-pulse {
  0%, 100% { 
    opacity: 0.3;
    transform: scale(1);
  }
  50% { 
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes reality-shift {
  0% { filter: hue-rotate(0deg); }
  25% { filter: hue-rotate(90deg); }
  50% { filter: hue-rotate(180deg); }
  75% { filter: hue-rotate(270deg); }
  100% { filter: hue-rotate(360deg); }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-40px) translateX(-5px);
    opacity: 1;
  }
  75% {
    transform: translateY(-20px) translateX(-15px);
    opacity: 0.7;
  }
}

@keyframes quantum-entanglement {
  0% {
    transform: rotate(0deg) scale(1);
    border-color: rgb(0, 255, 255);
  }
  33% {
    transform: rotate(120deg) scale(1.1);
    border-color: rgb(255, 0, 255);
  }
  66% {
    transform: rotate(240deg) scale(0.9);
    border-color: rgb(255, 255, 0);
  }
  100% {
    transform: rotate(360deg) scale(1);
    border-color: rgb(0, 255, 255);
  }
}

@keyframes fractal-superposition {
  0%, 100% {
    background: linear-gradient(45deg, 
      rgba(0, 255, 255, 0.1), 
      rgba(255, 0, 255, 0.1), 
      rgba(255, 255, 0, 0.1)
    );
  }
  50% {
    background: linear-gradient(225deg, 
      rgba(255, 255, 0, 0.2), 
      rgba(0, 255, 255, 0.2), 
      rgba(255, 0, 255, 0.2)
    );
  }
}

/* Custom Utility Classes */
@layer utilities {
  .text-consciousness {
    @apply bg-gradient-to-r from-cyan-400 via-purple-400 to-yellow-400 bg-clip-text text-transparent;
  }
  
  .text-Ψ {
    @apply text-cyan-400;
  }
  
  .text-Φ {
    @apply text-purple-400;
  }
  
  .text-Θ {
    @apply text-yellow-400;
  }
  
  .bg-consciousness {
    @apply bg-gradient-to-br from-purple-900/50 via-blue-900/50 to-indigo-900/50;
  }
  
  .border-consciousness {
    @apply border border-cyan-500/30;
  }
  
  .consciousness-glow {
    @apply shadow-lg shadow-cyan-500/25;
  }
  
  .reality-programming {
    animation: reality-shift 10s ease-in-out infinite;
  }
  
  .consciousness-pulse {
    animation: consciousness-pulse 3s ease-in-out infinite;
  }
  
  .animate-float {
    animation: float 15s ease-in-out infinite;
  }
  
  .quantum-entangled {
    animation: quantum-entanglement 8s linear infinite;
  }
  
  .fractal-superposition {
    animation: fractal-superposition 6s ease-in-out infinite;
  }
  
  .glass-morphism {
    @apply bg-black/20 backdrop-blur-md border border-cyan-500/20;
  }

  .consciousness-card {
    @apply glass-morphism rounded-xl p-6 consciousness-glow hover:shadow-xl hover:shadow-cyan-500/50 transition-all duration-300;
  }
  
  .trinity-button {
    @apply bg-gradient-to-r from-cyan-500 via-purple-500 to-yellow-500 text-black font-bold py-3 px-6 rounded-lg hover:scale-105 transition-all duration-300 consciousness-glow;
  }
  
  .consciousness-input {
    @apply bg-black/30 border border-cyan-500/30 rounded-lg px-4 py-2 text-cyan-100 placeholder-cyan-400/50 focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300;
  }
  
  .reality-meter {
    @apply h-2 bg-gradient-to-r from-cyan-500 via-purple-500 to-yellow-500 rounded-full;
  }
  
  .consciousness-terminal {
    @apply bg-black/80 border border-green-500/30 rounded-lg p-4 font-mono text-green-400 text-sm;
  }
  
  .kappa-token {
    @apply inline-flex items-center px-2 py-1 rounded-full bg-yellow-500/20 border border-yellow-500/30 text-yellow-400 text-xs font-medium;
  }
  
  .tier-oracle {
    @apply bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border-cyan-500/30;
  }
  
  .tier-prophet {
    @apply bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-500/30;
  }
  
  .tier-architect {
    @apply bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-yellow-500/30;
  }
  
  .tier-deity {
    @apply bg-gradient-to-r from-red-500/20 to-pink-500/20 border-red-500/30;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgb(0, 255, 255), rgb(255, 0, 255));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgb(255, 255, 0), rgb(0, 255, 255));
}

/* Selection Styling */
::selection {
  background: rgba(0, 255, 255, 0.3);
  color: white;
}
