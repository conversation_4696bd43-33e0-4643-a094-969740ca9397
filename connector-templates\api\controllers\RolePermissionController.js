/**
 * Role Permission Controller
 * 
 * This controller handles API requests related to role and permission management.
 */

const RolePermissionService = require('../services/RolePermissionService');
const AuditService = require('../services/AuditService');
const { ValidationError } = require('../utils/errors');

class RolePermissionController {
  constructor() {
    this.rolePermissionService = new RolePermissionService();
    this.auditService = new AuditService();
  }

  /**
   * Get all roles
   */
  async getAllRoles(req, res, next) {
    try {
      const roles = await this.rolePermissionService.getAllRoles();
      res.json(roles);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'role',
        resourceId: 'all',
        details: { count: roles.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get role by ID
   */
  async getRoleById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Role ID is required');
      }
      
      const role = await this.rolePermissionService.getRoleById(id);
      res.json(role);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'role',
        resourceId: id,
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new role
   */
  async createRole(req, res, next) {
    try {
      const roleData = req.body;
      
      if (!roleData) {
        throw new ValidationError('Role data is required');
      }
      
      const role = await this.rolePermissionService.createRole(roleData);
      res.status(201).json(role);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'CREATE',
        resourceType: 'role',
        resourceId: role.id,
        details: { name: role.name },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a role
   */
  async updateRole(req, res, next) {
    try {
      const { id } = req.params;
      const roleData = req.body;
      
      if (!id) {
        throw new ValidationError('Role ID is required');
      }
      
      if (!roleData) {
        throw new ValidationError('Role data is required');
      }
      
      const role = await this.rolePermissionService.updateRole(id, roleData);
      res.json(role);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'UPDATE',
        resourceType: 'role',
        resourceId: id,
        details: { name: role.name },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a role
   */
  async deleteRole(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Role ID is required');
      }
      
      // Get role before deletion for audit log
      const role = await this.rolePermissionService.getRoleById(id);
      
      const result = await this.rolePermissionService.deleteRole(id);
      res.json(result);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'DELETE',
        resourceType: 'role',
        resourceId: id,
        details: { name: role.name },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all permissions
   */
  async getAllPermissions(req, res, next) {
    try {
      const permissions = await this.rolePermissionService.getAllPermissions();
      res.json(permissions);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'permission',
        resourceId: 'all',
        details: { count: permissions.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get permissions for a role
   */
  async getPermissionsForRole(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Role ID is required');
      }
      
      const permissions = await this.rolePermissionService.getPermissionsForRole(id);
      res.json(permissions);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'permission',
        resourceId: 'role',
        details: { roleId: id, count: permissions.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Add permission to role
   */
  async addPermissionToRole(req, res, next) {
    try {
      const { id } = req.params;
      const { permissionId } = req.body;
      
      if (!id) {
        throw new ValidationError('Role ID is required');
      }
      
      if (!permissionId) {
        throw new ValidationError('Permission ID is required');
      }
      
      const rolePermission = await this.rolePermissionService.addPermissionToRole(id, permissionId);
      res.status(201).json(rolePermission);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'CREATE',
        resourceType: 'role_permission',
        resourceId: `${id}_${permissionId}`,
        details: { roleId: id, permissionId },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Remove permission from role
   */
  async removePermissionFromRole(req, res, next) {
    try {
      const { id, permissionId } = req.params;
      
      if (!id) {
        throw new ValidationError('Role ID is required');
      }
      
      if (!permissionId) {
        throw new ValidationError('Permission ID is required');
      }
      
      const result = await this.rolePermissionService.removePermissionFromRole(id, permissionId);
      res.json(result);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'DELETE',
        resourceType: 'role_permission',
        resourceId: `${id}_${permissionId}`,
        details: { roleId: id, permissionId },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get roles for a user
   */
  async getRolesForUser(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('User ID is required');
      }
      
      const roles = await this.rolePermissionService.getRolesForUser(id);
      res.json(roles);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'role',
        resourceId: 'user',
        details: { userId: id, count: roles.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get my roles
   */
  async getMyRoles(req, res, next) {
    try {
      const roles = await this.rolePermissionService.getRolesForUser(req.user.id);
      res.json(roles);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'role',
        resourceId: 'my',
        details: { count: roles.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Assign role to user
   */
  async assignRoleToUser(req, res, next) {
    try {
      const { id } = req.params;
      const { roleId } = req.body;
      
      if (!id) {
        throw new ValidationError('User ID is required');
      }
      
      if (!roleId) {
        throw new ValidationError('Role ID is required');
      }
      
      const userRole = await this.rolePermissionService.assignRoleToUser(id, roleId);
      res.status(201).json(userRole);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'CREATE',
        resourceType: 'user_role',
        resourceId: `${id}_${roleId}`,
        details: { userId: id, roleId },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Remove role from user
   */
  async removeRoleFromUser(req, res, next) {
    try {
      const { id, roleId } = req.params;
      
      if (!id) {
        throw new ValidationError('User ID is required');
      }
      
      if (!roleId) {
        throw new ValidationError('Role ID is required');
      }
      
      const result = await this.rolePermissionService.removeRoleFromUser(id, roleId);
      res.json(result);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'DELETE',
        resourceType: 'user_role',
        resourceId: `${id}_${roleId}`,
        details: { userId: id, roleId },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get permissions for a user
   */
  async getPermissionsForUser(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('User ID is required');
      }
      
      const permissions = await this.rolePermissionService.getPermissionsForUser(id);
      res.json(permissions);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'permission',
        resourceId: 'user',
        details: { userId: id, count: permissions.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get my permissions
   */
  async getMyPermissions(req, res, next) {
    try {
      const permissions = await this.rolePermissionService.getPermissionsForUser(req.user.id);
      res.json(permissions);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'permission',
        resourceId: 'my',
        details: { count: permissions.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new RolePermissionController();
