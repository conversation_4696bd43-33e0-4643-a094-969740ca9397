import React, { useState, useRef, useEffect } from 'react';

const AgreementViewer = ({ 
  agreementTitle, 
  agreementContent, 
  agreementVersion, 
  agreementDate,
  downloadFileName
}) => {
  const [isFullyScrolled, setIsFullyScrolled] = useState(false);
  const agreementRef = useRef(null);
  
  // Track scrolling to detect when user has viewed the entire document
  const handleScroll = () => {
    if (!agreementRef.current) return;
    
    const { scrollTop, scrollHeight, clientHeight } = agreementRef.current;
    // Consider it "read" when scrolled to 90% of the document
    if (scrollTop + clientHeight >= scrollHeight * 0.9) {
      setIsFullyScrolled(true);
    }
  };
  
  // Generate PDF for download
  const handleDownload = () => {
    // In a real implementation, this would generate a PDF
    // For now, we'll just create a text file with the content
    const element = document.createElement('a');
    const file = new Blob([agreementContent], {type: 'text/plain'});
    element.href = URL.createObjectURL(file);
    element.download = downloadFileName || `${agreementTitle.replace(/\s+/g, '-').toLowerCase()}.txt`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };
  
  return (
    <div className="w-full max-w-4xl mx-auto bg-gray-50 rounded-lg shadow-md overflow-hidden">
      <div className="bg-blue-900 text-white px-6 py-4 flex justify-between items-center">
        <h2 className="text-xl font-bold">{agreementTitle}</h2>
        <div className="text-sm">
          Version {agreementVersion} • {agreementDate}
        </div>
      </div>
      
      <div 
        ref={agreementRef}
        onScroll={handleScroll}
        className="h-96 overflow-y-auto p-6 text-gray-800 bg-white border-b border-gray-200"
      >
        <div className="prose max-w-none">
          {agreementContent}
        </div>
      </div>
      
      <div className="bg-gray-50 px-6 py-3 flex justify-between items-center">
        <div className="text-sm text-gray-500">
          {isFullyScrolled ? 
            <span className="text-green-600">✓ You've reviewed this document</span> : 
            <span>Please review the entire document</span>
          }
        </div>
        <button
          onClick={handleDownload}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          Download PDF
        </button>
      </div>
    </div>
  );
};

export default AgreementViewer;
