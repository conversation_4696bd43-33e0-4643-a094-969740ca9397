/**
 * Privacy Notice Model
 * 
 * Represents a privacy notice or privacy policy document that informs
 * data subjects about how their personal data is processed.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const privacyNoticeSchema = new Schema({
  title: {
    type: String,
    required: true
  },
  version: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['Draft', 'Under Review', 'Published', 'Archived', 'Superseded'],
    default: 'Draft'
  },
  type: {
    type: String,
    enum: ['Website Privacy Policy', 'Mobile App Privacy Notice', 'Cookie Policy', 'Employee Privacy Notice', 'Customer Privacy Notice', 'Marketing Privacy Notice', 'Other'],
    required: true
  },
  applicableRegions: [{
    type: String
  }],
  applicableCountries: [{
    type: String
  }],
  regulatoryFrameworks: [{
    type: String,
    enum: ['GDPR', 'CCPA', 'LGPD', 'PIPEDA', 'POPIA', 'APPI', 'Other']
  }],
  targetAudience: {
    type: String,
    enum: ['General Public', 'Customers', 'Employees', 'Job Applicants', 'Website Visitors', 'Mobile App Users', 'Children', 'Other'],
    required: true
  },
  effectiveDate: {
    type: Date,
    required: true
  },
  lastUpdatedDate: {
    type: Date,
    required: true
  },
  nextReviewDate: Date,
  content: {
    type: String,
    required: true
  },
  contentSummary: String,
  contentFormat: {
    type: String,
    enum: ['HTML', 'Markdown', 'Plain Text', 'PDF', 'Other'],
    default: 'HTML'
  },
  languages: [{
    code: {
      type: String,
      required: true
    },
    name: String,
    content: String,
    url: String
  }],
  publicationLocations: [{
    type: {
      type: String,
      enum: ['Website', 'Mobile App', 'Email', 'Physical Location', 'Document', 'Other']
    },
    details: String,
    url: String
  }],
  processingActivities: [{
    type: Schema.Types.ObjectId,
    ref: 'ProcessingActivity'
  }],
  dataCategories: [{
    category: String,
    description: String,
    purpose: String,
    legalBasis: String,
    retentionPeriod: String
  }],
  dataSubjectRights: [{
    right: {
      type: String,
      enum: ['Access', 'Rectification', 'Erasure', 'Restriction', 'Portability', 'Objection', 'Automated Decision Making', 'Withdraw Consent', 'Complain']
    },
    description: String,
    exerciseMethod: String
  }],
  dataRecipients: [{
    category: String,
    purpose: String,
    safeguards: String
  }],
  internationalTransfers: {
    exists: {
      type: Boolean,
      default: false
    },
    countries: [String],
    safeguards: String
  },
  contactInformation: {
    organization: String,
    address: String,
    email: String,
    phone: String,
    dpo: {
      name: String,
      email: String,
      phone: String
    },
    supervisoryAuthority: {
      name: String,
      website: String,
      contactDetails: String
    }
  },
  cookieInformation: {
    categories: [{
      name: String,
      description: String,
      required: Boolean,
      duration: String,
      cookies: [{
        name: String,
        purpose: String,
        domain: String,
        duration: String
      }]
    }]
  },
  changeHistory: [{
    version: String,
    date: Date,
    summary: String,
    details: String,
    author: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  approvals: [{
    role: String,
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    date: Date,
    comments: String
  }],
  documents: [{
    name: String,
    description: String,
    url: String,
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],
  readabilityMetrics: {
    fleschReadingEase: Number,
    fleschKincaidGradeLevel: Number,
    wordCount: Number,
    averageSentenceLength: Number
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Add text index for search functionality
privacyNoticeSchema.index({
  title: 'text',
  contentSummary: 'text',
  'languages.name': 'text'
});

// Method to get the current published version
privacyNoticeSchema.statics.getCurrentPublishedVersion = async function(type, region) {
  const query = {
    type,
    status: 'Published'
  };
  
  if (region) {
    query.applicableRegions = region;
  }
  
  return this.findOne(query)
    .sort({ effectiveDate: -1 })
    .exec();
};

// Method to archive a notice
privacyNoticeSchema.methods.archive = function(userId) {
  this.status = 'Archived';
  this.updatedBy = userId;
  
  // Add to change history
  this.changeHistory.push({
    version: this.version,
    date: new Date(),
    summary: 'Notice archived',
    details: 'Privacy notice has been archived',
    author: userId
  });
};

// Method to publish a notice
privacyNoticeSchema.methods.publish = function(userId, effectiveDate) {
  this.status = 'Published';
  this.effectiveDate = effectiveDate || new Date();
  this.lastUpdatedDate = new Date();
  this.updatedBy = userId;
  
  // Add to change history
  this.changeHistory.push({
    version: this.version,
    date: new Date(),
    summary: 'Notice published',
    details: `Privacy notice version ${this.version} has been published`,
    author: userId
  });
};

// Method to create a new version
privacyNoticeSchema.methods.createNewVersion = function(newVersion, userId) {
  // Create a copy of the current notice
  const newNotice = this.toObject();
  
  // Remove _id to create a new document
  delete newNotice._id;
  
  // Update version information
  newNotice.version = newVersion;
  newNotice.status = 'Draft';
  newNotice.createdBy = userId;
  newNotice.updatedBy = userId;
  newNotice.createdAt = new Date();
  newNotice.updatedAt = new Date();
  
  // Add to change history
  newNotice.changeHistory.push({
    version: newVersion,
    date: new Date(),
    summary: 'New version created',
    details: `New version ${newVersion} created based on version ${this.version}`,
    author: userId
  });
  
  // Clear approvals for the new version
  newNotice.approvals = [];
  
  return new this.constructor(newNotice);
};

const PrivacyNotice = mongoose.model('PrivacyNotice', privacyNoticeSchema);

module.exports = PrivacyNotice;
