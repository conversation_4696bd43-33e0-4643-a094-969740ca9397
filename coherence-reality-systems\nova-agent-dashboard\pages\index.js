import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import NovaAgentStatus from '../components/NovaAgentStatus';
import CoherenceMetrics from '../components/CoherenceMetrics';
import SystemHealth from '../components/SystemHealth';
import PsiSnapIndicator from '../components/PsiSnapIndicator';
import NovaAgentConsole from '../components/NovaAgentConsole';

export default function NovaAgentDashboard() {
  const [agentData, setAgentData] = useState(null);
  const [coherenceData, setCoherenceData] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState(null);

  // Fetch Nova Agent data
  useEffect(() => {
    const fetchAgentData = async () => {
      try {
        const [statusResponse, coherenceResponse] = await Promise.all([
          fetch('http://localhost:8090/status'),
          fetch('http://localhost:8090/coherence')
        ]);

        if (statusResponse.ok && coherenceResponse.ok) {
          const statusData = await statusResponse.json();
          const coherenceData = await coherenceResponse.json();
          
          setAgentData(statusData);
          setCoherenceData(coherenceData);
          setIsConnected(true);
          setError(null);
        } else {
          throw new Error('Failed to fetch Nova Agent data');
        }
      } catch (err) {
        setError(err.message);
        setIsConnected(false);
      }
    };

    // Initial fetch
    fetchAgentData();

    // Set up polling every 2 seconds
    const interval = setInterval(fetchAgentData, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* Header */}
      <header className="bg-gray-800/50 backdrop-blur-xl border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-nova-400 to-coherence-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">N</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-white">Nova Agent</h1>
                  <p className="text-sm text-gray-400">Coherence Operating System</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Connection Status */}
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
                <span className="text-sm text-gray-300">
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              
              {/* Version */}
              {agentData && (
                <div className="text-sm text-gray-400">
                  v{agentData.version}
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-900/20 border border-red-500/30 rounded-lg p-6"
          >
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm">!</span>
              </div>
              <div>
                <h3 className="text-red-400 font-medium">Nova Agent Connection Error</h3>
                <p className="text-red-300 text-sm mt-1">{error}</p>
                <p className="text-red-200 text-xs mt-2">
                  Make sure nova-agent-api.exe is running on port 8080
                </p>
              </div>
            </div>
          </motion.div>
        ) : (
          <div className="space-y-8">
            {/* Top Row - Status and Ψ-Snap */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <NovaAgentStatus agentData={agentData} isConnected={isConnected} />
              <PsiSnapIndicator agentData={agentData} coherenceData={coherenceData} />
            </div>

            {/* Middle Row - Coherence Metrics */}
            <CoherenceMetrics coherenceData={coherenceData} />

            {/* Bottom Row - System Health and Console */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <SystemHealth agentData={agentData} coherenceData={coherenceData} />
              <NovaAgentConsole isConnected={isConnected} />
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
