<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hardware/Software Implementation Layer Diagram</title>
    <style>

        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }

        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 800px;
            height: 70px; /* Extended height for adequate space */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: visible; /* Prevents content from spilling out */
        }

        /* Ensure Universal Pattern Elements stay within main container */
        .universal-pattern-elements {
            position: relative;
            width: 650px;
            margin-top: 450px; /* Position it near the bottom of the main container */
            margin-left: 50px; /* Center it within the main container */
            z-index: 1;
        }

        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: visible; /* Allow content to be fully visible */
            min-height: 100px; /* Minimum height */
            min-width: 200px; /* Minimum width */
        }

        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Prevents content from spilling out */
            text-overflow: ellipsis; /* Shows ellipsis for overflowing text */
        }

        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically */
            align-items: center;
            text-align: center;
            padding: 5px; /* Reduced padding */
            overflow: visible; /* Allow content to be fully visible */
            border-top-left-radius: 0; /* Flat corner for number */
            min-height: 50px; /* Minimum height */
            min-width: 100px; /* Minimum width */
        }

        /* Component Number Styles - Integrated into corner */

        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 0 0 6px 0; /* Rounded on bottom-right corner only */
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px; /* Smaller font */
            font-weight: bold;
            z-index: 2;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }

        .component-label {
            font-weight: bold;
            margin-top: 10px; /* Reduced margin */
            margin-bottom: 2px; /* Reduced margin */
            font-size: 12px; /* Smaller font */
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }

        .component-content {
            font-size: 10px; /* Smaller font */
            text-align: center;
            width: 100%;
            padding: 0;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            line-height: 1.2; /* Tighter line spacing */
        }

        /* Arrow Styles */
        .arrow-line {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            z-index: 0;
        }

        .arrow-head {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 0;
        }

        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }

        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }

        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }

        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }

        /* SVG Styles */
        svg {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 0;
        }

        /* Equation Styles */
        .equation {
            font-weight: bold;
            font-size: 14px;
            margin-top: 5px;
            text-align: center;
        }
    
    </style>
</head>
<body>
    <h1>FIG. 5: Hardware/Software Implementation Layer Diagram</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 580px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">HARDWARE/SOFTWARE IMPLEMENTATION LAYER</div>
        </div>

        <!-- Hardware Layer -->
        <div class="container-box" style="width: 700px; height: 580px; left: 50px; top: 70px;">
            <div class="container-label" style="font-size: 16px;">HARDWARE LAYER</div>
        </div>

        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 100px; top: 110px; width: 150px; height: 100px;">
            <div class="component-number-inside">701</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Tensor Processing Units</div>
            <div style="font-size: 12px; text-align: center;">
                Specialized for UUFT Tensor Operations
            </div>
        </div>

        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 300px; top: 110px; width: 150px; height: 100px;">
            <div class="component-number-inside">702</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Field-Programmable Gate Arrays</div>
            <div style="font-size: 12px; text-align: center;">
                Reconfigurable for Fusion Operations
            </div>
        </div>

        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 500px; top: 110px; width: 150px; height: 100px;">
            <div class="component-number-inside">703</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Application-Specific ICs</div>
            <div style="font-size: 12px; text-align: center;">
                Optimized for π10³ Calculations
            </div>
        </div>

        <!-- Middleware Layer -->
        <div class="container-box" style="width: 700px; height: 580px; left: 50px; top: 250px;">
            <div class="container-label" style="font-size: 16px;">MIDDLEWARE LAYER</div>
        </div>

        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 100px; top: 290px; width: 150px; height: 100px;">
            <div class="component-number-inside">704</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Trinitarian Processing Framework</div>
            <div style="font-size: 12px; text-align: center;">
                Source/Validation/Integration
            </div>
        </div>

        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 300px; top: 290px; width: 150px; height: 100px;">
            <div class="component-number-inside">705</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">18/82 Resource Allocator</div>
            <div style="font-size: 12px; text-align: center;">
                Optimal Resource Distribution
            </div>
        </div>

        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 500px; top: 290px; width: 150px; height: 100px;">
            <div class="component-number-inside">706</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Cross-Domain Pattern Detector</div>
            <div style="font-size: 12px; text-align: center;">
                Multi-domain Intelligence
            </div>
        </div>

        <!-- Software Layer -->
        <div class="container-box" style="width: 700px; height: 580px; left: 50px; top: 430px;">
            <div class="container-label" style="font-size: 16px;">SOFTWARE LAYER</div>
        </div>

        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 100px; top: 470px; width: 150px; height: 100px;">
            <div class="component-number-inside">707</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">NovaFuse Universal Components</div>
            <div style="font-size: 12px; text-align: center;">
                13 Core Software Modules
            </div>
        </div>

        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 300px; top: 470px; width: 150px; height: 100px;">
            <div class="component-number-inside">708</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Domain-Specific Adaptors</div>
            <div style="font-size: 12px; text-align: center;">
                Industry-Specific Implementations
            </div>
        </div>

        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 500px; top: 470px; width: 150px; height: 100px;">
            <div class="component-number-inside">709</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">User Interface Layer</div>
            <div style="font-size: 12px; text-align: center;">
                NovaVision Framework
            </div>
        </div>

        <!-- Connecting arrows -->
        <div class="arrow arrow-down" style="left: 175px; top: 190px;"></div>
        <div class="arrow arrow-down" style="left: 375px; top: 190px;"></div>
        <div class="arrow arrow-down" style="left: 575px; top: 190px;"></div>

        <div class="arrow arrow-down" style="left: 175px; top: 370px;"></div>
        <div class="arrow arrow-down" style="left: 375px; top: 370px;"></div>
        <div class="arrow arrow-down" style="left: 575px; top: 370px;"></div>

        <!-- Legend -->
        <div class="legend" style="position: absolute; right: 10px; bottom: 10px; background-color: white; border: 1px solid #ddd; border-radius: 4px; padding: 5px; z-index: 10; width: 200px;">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Hardware Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Middleware Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Software Components</div>
            </div>
        </div>

        <div class="inventor-label" style="position: absolute; left: 10px; bottom: 10px; font-size: 12px; font-style: italic; color: #333;">Inventor: David Nigel Irvin</div>
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
</div>
</body>
</html>





