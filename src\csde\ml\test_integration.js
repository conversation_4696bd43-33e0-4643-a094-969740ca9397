/**
 * Test CSDE ML Integration
 * 
 * This script tests the CSDE ML integration.
 */

const fs = require('fs');
const path = require('path');
const CSDEMLIntegration = require('./csde_ml_integration');

// Configuration
const config = {
  outputDir: path.join(__dirname, 'data')
};

// Create output directory if it doesn't exist
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Initialize CSDE ML Integration
const csdeMLIntegration = new CSDEMLIntegration();

// Get model info
const modelInfo = csdeMLIntegration.getModelInfo();
console.log('Model Info:', modelInfo);

// Create test input
const testInput = {
  complianceData: {
    complianceScore: 0.65,
    controls: [
      {
        id: 'NIST-AC-2',
        name: 'Account Management',
        description: 'The organization needs to implement account management procedures',
        severity: 'high',
        status: 'non-compliant',
        framework: 'NIST 800-53'
      },
      {
        id: 'NIST-CM-7',
        name: 'Least Functionality',
        description: 'The organization needs to configure systems to provide only essential capabilities',
        severity: 'medium',
        status: 'partial',
        framework: 'NIST 800-53'
      },
      {
        id: 'NIST-SC-7',
        name: 'Boundary Protection',
        description: 'The organization needs to implement boundary protection mechanisms',
        severity: 'high',
        status: 'compliant',
        framework: 'NIST 800-53'
      }
    ]
  },
  gcpData: {
    integrationScore: 0.75,
    services: [
      {
        id: 'GCP-IAM-1',
        name: 'IAM Role Configuration',
        description: 'IAM roles need to be configured with least privilege',
        severity: 'high',
        status: 'non-optimal',
        service: 'Cloud IAM'
      },
      {
        id: 'GCP-VPC-1',
        name: 'VPC Network Security',
        description: 'VPC network security needs to be enhanced',
        severity: 'medium',
        status: 'partial',
        service: 'VPC Network'
      },
      {
        id: 'GCP-KMS-1',
        name: 'Key Management',
        description: 'Cloud KMS keys need to be properly managed',
        severity: 'high',
        status: 'optimal',
        service: 'Cloud KMS'
      }
    ]
  },
  cyberSafetyData: {
    safetyScore: 0.55,
    controls: [
      {
        id: 'CS-P3-1',
        name: 'Self-Destructing Compliance Servers',
        description: 'Implement self-destructing compliance servers with hardware-enforced geo-fencing',
        severity: 'high',
        status: 'not-implemented',
        pillar: 'Pillar 3'
      },
      {
        id: 'CS-P9-1',
        name: 'Post-Quantum Immutable Compliance Journal',
        description: 'Implement post-quantum immutable compliance journal',
        severity: 'medium',
        status: 'partial',
        pillar: 'Pillar 9'
      },
      {
        id: 'CS-P12-1',
        name: 'C-Suite Directive to Code Compiler',
        description: 'Implement C-Suite Directive to Code Compiler',
        severity: 'medium',
        status: 'implemented',
        pillar: 'Pillar 12'
      }
    ]
  }
};

// Calculate CSDE with ML enhancement
console.log('Calculating CSDE with ML enhancement...');
const result = csdeMLIntegration.calculate(
  testInput.complianceData,
  testInput.gcpData,
  testInput.cyberSafetyData
);

// Save result
fs.writeFileSync(
  path.join(config.outputDir, 'integration_result.json'),
  JSON.stringify(result, null, 2)
);

console.log(`Result saved to ${path.join(config.outputDir, 'integration_result.json')}`);

// Display key metrics
console.log('\nKey Metrics:');
console.log(`CSDE Value: ${result.csdeValue.toFixed(2)}`);
console.log(`Performance Factor: ${result.performanceFactor.toFixed(2)}x`);
console.log(`ML Enhanced: ${result.mlEnhanced}`);

// Display ML insights
if (result.mlInsights) {
  console.log('\nML Insights:');
  console.log(`Compliance Status: ${result.mlInsights.complianceStatus.level} (${(result.mlInsights.complianceStatus.score * 100).toFixed(2)}%)`);
  console.log(`GCP Status: ${result.mlInsights.gcpStatus.level} (${(result.mlInsights.gcpStatus.score * 100).toFixed(2)}%)`);
  console.log(`Cyber-Safety Status: ${result.mlInsights.cyberSafetyStatus.level} (${(result.mlInsights.cyberSafetyStatus.score * 100).toFixed(2)}%)`);
  
  console.log('\nTop Improvement Areas:');
  result.mlInsights.improvementAreas.slice(0, 3).forEach((area, index) => {
    console.log(`${index + 1}. ${area.area} (${area.priority.toUpperCase()}): ${area.description}`);
  });
  
  console.log('\nTop Recommendations:');
  result.mlInsights.recommendations.slice(0, 3).forEach((rec, index) => {
    console.log(`${index + 1}. ${rec.action} (${rec.priority.toUpperCase()}): ${rec.description}`);
  });
}

// Display top remediation actions
console.log('\nTop Remediation Actions:');
result.remediationActions.slice(0, 5).forEach((action, index) => {
  console.log(`${index + 1}. ${action.title} (${action.priority.toUpperCase()}): ${action.description}`);
  if (action.mlRecommended) {
    console.log(`   ML Recommended: Yes (${(action.mlConfidence * 100).toFixed(2)}% confidence)`);
  }
});

console.log('\nDone!');
