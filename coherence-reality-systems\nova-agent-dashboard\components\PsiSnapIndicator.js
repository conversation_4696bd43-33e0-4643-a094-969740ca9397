import { motion } from 'framer-motion';
import { BoltIcon, StarIcon } from '@heroicons/react/24/outline';

export default function PsiSnapIndicator({ agentData, coherenceData }) {
  if (!agentData || !coherenceData) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6"
      >
        <div className="animate-pulse">
          <div className="h-6 bg-gray-700 rounded mb-4"></div>
          <div className="h-32 bg-gray-700 rounded"></div>
        </div>
      </motion.div>
    );
  }

  const psiLevel = coherenceData.psi_level || agentData.coherence;
  const snapPoint = coherenceData.snap_point || 0.82;
  const isSnapped = agentData.psi_snap;
  const currentPhase = coherenceData.current_phase || 'internal_coherence';

  const getPhaseColor = (phase) => {
    switch (phase) {
      case 'externalization': return 'text-green-400';
      case 'internal_coherence': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  };

  const getPhaseDescription = (phase) => {
    switch (phase) {
      case 'externalization': 
        return 'System ready for enterprise integration';
      case 'internal_coherence': 
        return 'Building internal system coherence';
      default: 
        return 'Initializing coherence protocols';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`backdrop-blur-xl rounded-lg border p-6 ${
        isSnapped 
          ? 'bg-green-900/20 border-green-500/30 animate-coherence' 
          : 'bg-gray-800/50 border-gray-600'
      }`}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          {isSnapped ? (
            <BoltIcon className="w-6 h-6 text-green-400 animate-pulse" />
          ) : (
            <StarIcon className="w-6 h-6 text-yellow-400" />
          )}
          <div>
            <h3 className="text-lg font-semibold text-white">Ψ-Snap Indicator</h3>
            <p className="text-sm text-gray-400">82/18 Comphyological Model</p>
          </div>
        </div>
        
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${
          isSnapped 
            ? 'bg-green-500/20 text-green-400' 
            : 'bg-yellow-500/20 text-yellow-400'
        }`}>
          {isSnapped ? '⚡ SNAPPED' : '🔄 BUILDING'}
        </div>
      </div>

      {/* Progress Circle */}
      <div className="flex items-center justify-center mb-6">
        <div className="relative w-32 h-32">
          {/* Background Circle */}
          <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
            <circle
              cx="50"
              cy="50"
              r="40"
              stroke="currentColor"
              strokeWidth="8"
              fill="transparent"
              className="text-gray-700"
            />
            {/* Progress Circle */}
            <circle
              cx="50"
              cy="50"
              r="40"
              stroke="currentColor"
              strokeWidth="8"
              fill="transparent"
              strokeDasharray={`${2 * Math.PI * 40}`}
              strokeDashoffset={`${2 * Math.PI * 40 * (1 - psiLevel)}`}
              className={`transition-all duration-1000 ${
                isSnapped ? 'text-green-400' : 'text-yellow-400'
              }`}
              strokeLinecap="round"
            />
            {/* Snap Point Marker */}
            <circle
              cx={50 + 40 * Math.cos(2 * Math.PI * snapPoint - Math.PI / 2)}
              cy={50 + 40 * Math.sin(2 * Math.PI * snapPoint - Math.PI / 2)}
              r="3"
              fill="currentColor"
              className="text-purple-400"
            />
          </svg>
          
          {/* Center Text */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className={`text-2xl font-bold ${
                isSnapped ? 'text-green-400' : 'text-yellow-400'
              }`}>
                {(psiLevel * 100).toFixed(1)}%
              </div>
              <div className="text-xs text-gray-400">Coherence</div>
            </div>
          </div>
        </div>
      </div>

      {/* Phase Information */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-400">Current Phase</span>
          <span className={`text-sm font-medium ${getPhaseColor(currentPhase)}`}>
            {currentPhase.replace('_', ' ').toUpperCase()}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-400">Ψ-Snap Point</span>
          <span className="text-sm font-medium text-purple-400">
            {(snapPoint * 100).toFixed(0)}%
          </span>
        </div>

        <div className="pt-2 border-t border-gray-700">
          <p className="text-xs text-gray-400">
            {getPhaseDescription(currentPhase)}
          </p>
        </div>
      </div>

      {/* 82/18 Model Indicator */}
      <div className="mt-4 p-3 bg-purple-900/20 border border-purple-500/30 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="text-sm">
            <span className="text-purple-400 font-medium">82%</span>
            <span className="text-gray-400"> Internal Coherence</span>
          </div>
          <div className="text-sm">
            <span className="text-purple-400 font-medium">18%</span>
            <span className="text-gray-400"> Externalization</span>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
