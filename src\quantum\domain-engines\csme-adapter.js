/**
 * CSME Adapter
 *
 * This module provides a concrete implementation of the CSME (Cyber-Safety Medical Engine)
 * adapter for the Finite Universe Principle defense system. It connects the boundary
 * enforcement mechanisms with the CSME engine to ensure that medical domain operations
 * remain within finite boundaries.
 * 
 * Key features:
 * 1. Domain-specific processing for medical data
 * 2. Integration with CSME components
 * 3. Medical domain boundary enforcement
 * 4. Specialized handling for medical metrics
 */

const EventEmitter = require('events');
const { MAX_SAFE_BOUNDS } = require('../constants');

/**
 * CSMEAdapter class
 * 
 * Provides integration between the Boundary Enforcer and the CSME engine.
 */
class CSMEAdapter extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      strictMode: true,
      csmeInstance: null, // Optional CSME instance
      enablePrivacyProtection: true, // Enable privacy protection for medical data
      ...options
    };

    // Initialize medical domain boundaries
    this.medicalBoundaries = {
      ...MAX_SAFE_BOUNDS.BIOLOGICAL,
      // Additional medical-specific boundaries
      MAX_HEART_RATE: 300, // beats per minute
      MIN_HEART_RATE: 0,
      MAX_BLOOD_PRESSURE: 300, // mmHg
      MIN_BLOOD_PRESSURE: 0,
      MAX_TEMPERATURE: 50, // degrees Celsius
      MIN_TEMPERATURE: 0,
      MAX_OXYGEN_LEVEL: 100, // percent
      MIN_OXYGEN_LEVEL: 0
    };

    // Initialize metrics
    this.metrics = {
      processedDataCount: 0,
      boundaryViolations: 0,
      privacyProtectionApplied: 0,
      averageHeartRate: 0,
      totalHeartRate: 0,
      criticalAlerts: 0
    };

    if (this.options.enableLogging) {
      console.log('CSMEAdapter initialized with options:', this.options);
    }
  }

  /**
   * Process data through the CSME engine
   * @param {Object} data - Data to process
   * @returns {Object} - Processed data
   */
  async processData(data) {
    try {
      // Apply medical domain pre-processing
      const preprocessedData = this._preprocessMedicalData(data);
      
      // Process through CSME if available
      let processedData = preprocessedData;
      if (this.options.csmeInstance) {
        processedData = await this._processThroughCSME(preprocessedData);
      } else {
        // Apply default processing if no CSME instance is available
        processedData = this._applyDefaultProcessing(preprocessedData);
      }
      
      // Apply medical domain post-processing
      const postprocessedData = this._postprocessMedicalData(processedData);
      
      // Update metrics
      this._updateMetrics(postprocessedData);
      
      return postprocessedData;
    } catch (error) {
      this.emit('processing-error', { error: error.message, data });
      
      if (this.options.strictMode) {
        throw error;
      }
      
      // Return sanitized data on error
      return this._sanitizeMedicalData(data);
    }
  }

  /**
   * Pre-process medical domain data
   * @param {Object} data - Data to pre-process
   * @returns {Object} - Pre-processed data
   * @private
   */
  _preprocessMedicalData(data) {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    const result = { ...data };
    
    // Sanitize heart rate
    if (result.heartRate !== undefined) {
      const originalValue = result.heartRate;
      result.heartRate = Math.max(
        this.medicalBoundaries.MIN_HEART_RATE,
        Math.min(this.medicalBoundaries.MAX_HEART_RATE, result.heartRate)
      );
      
      if (result.heartRate !== originalValue) {
        this.metrics.boundaryViolations++;
      }
    }
    
    // Sanitize blood pressure
    if (result.bloodPressure !== undefined) {
      const originalValue = result.bloodPressure;
      result.bloodPressure = Math.max(
        this.medicalBoundaries.MIN_BLOOD_PRESSURE,
        Math.min(this.medicalBoundaries.MAX_BLOOD_PRESSURE, result.bloodPressure)
      );
      
      if (result.bloodPressure !== originalValue) {
        this.metrics.boundaryViolations++;
      }
    }
    
    // Sanitize temperature
    if (result.temperature !== undefined) {
      const originalValue = result.temperature;
      result.temperature = Math.max(
        this.medicalBoundaries.MIN_TEMPERATURE,
        Math.min(this.medicalBoundaries.MAX_TEMPERATURE, result.temperature)
      );
      
      if (result.temperature !== originalValue) {
        this.metrics.boundaryViolations++;
      }
    }
    
    // Sanitize oxygen level
    if (result.oxygenLevel !== undefined) {
      const originalValue = result.oxygenLevel;
      result.oxygenLevel = Math.max(
        this.medicalBoundaries.MIN_OXYGEN_LEVEL,
        Math.min(this.medicalBoundaries.MAX_OXYGEN_LEVEL, result.oxygenLevel)
      );
      
      if (result.oxygenLevel !== originalValue) {
        this.metrics.boundaryViolations++;
      }
    }
    
    return result;
  }

  /**
   * Process data through the CSME engine
   * @param {Object} data - Data to process
   * @returns {Object} - Processed data
   * @private
   */
  async _processThroughCSME(data) {
    try {
      // Call CSME instance to process data
      return await this.options.csmeInstance.processData(data);
    } catch (error) {
      this.emit('csme-processing-error', { error: error.message, data });
      
      // Apply default processing on CSME error
      return this._applyDefaultProcessing(data);
    }
  }

  /**
   * Apply default processing when no CSME instance is available
   * @param {Object} data - Data to process
   * @returns {Object} - Processed data
   * @private
   */
  _applyDefaultProcessing(data) {
    const result = { ...data };
    
    // Add CSME processing flag
    result.csmeProcessed = true;
    
    // Calculate health risk score if vital signs are available
    if (result.heartRate !== undefined || result.bloodPressure !== undefined || 
        result.temperature !== undefined || result.oxygenLevel !== undefined) {
      
      let riskScore = 0;
      let factorsCount = 0;
      
      // Heart rate risk factor
      if (result.heartRate !== undefined) {
        const heartRateRisk = this._calculateHeartRateRisk(result.heartRate);
        riskScore += heartRateRisk;
        factorsCount++;
      }
      
      // Blood pressure risk factor
      if (result.bloodPressure !== undefined) {
        const bloodPressureRisk = this._calculateBloodPressureRisk(result.bloodPressure);
        riskScore += bloodPressureRisk;
        factorsCount++;
      }
      
      // Temperature risk factor
      if (result.temperature !== undefined) {
        const temperatureRisk = this._calculateTemperatureRisk(result.temperature);
        riskScore += temperatureRisk;
        factorsCount++;
      }
      
      // Oxygen level risk factor
      if (result.oxygenLevel !== undefined) {
        const oxygenRisk = this._calculateOxygenRisk(result.oxygenLevel);
        riskScore += oxygenRisk;
        factorsCount++;
      }
      
      // Calculate average risk score
      result.healthRiskScore = factorsCount > 0 ? riskScore / factorsCount : 0;
      
      // Check for critical condition
      if (result.healthRiskScore > 0.7) {
        result.criticalAlert = true;
        this.metrics.criticalAlerts++;
      }
    }
    
    // Add timestamp
    result.processedAt = new Date().toISOString();
    
    return result;
  }

  /**
   * Post-process medical domain data
   * @param {Object} data - Data to post-process
   * @returns {Object} - Post-processed data
   * @private
   */
  _postprocessMedicalData(data) {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    const result = { ...data };
    
    // Add medical domain marker
    result._domain = 'medical';
    
    // Add processing metadata
    result._metadata = {
      processor: 'CSMEAdapter',
      version: '1.0.0',
      timestamp: new Date().toISOString()
    };
    
    // Apply privacy protection if enabled
    if (this.options.enablePrivacyProtection) {
      result = this._applyPrivacyProtection(result);
    }
    
    return result;
  }

  /**
   * Apply privacy protection to medical data
   * @param {Object} data - Data to protect
   * @returns {Object} - Privacy-protected data
   * @private
   */
  _applyPrivacyProtection(data) {
    // Remove personally identifiable information
    const protectedData = { ...data };
    
    // Remove or hash PII fields
    if (protectedData.patientId) {
      protectedData.patientId = this._hashIdentifier(protectedData.patientId);
      this.metrics.privacyProtectionApplied++;
    }
    
    if (protectedData.patientName) {
      delete protectedData.patientName;
      this.metrics.privacyProtectionApplied++;
    }
    
    if (protectedData.dateOfBirth) {
      // Replace with age range
      const dob = new Date(protectedData.dateOfBirth);
      const age = new Date().getFullYear() - dob.getFullYear();
      const ageRange = Math.floor(age / 10) * 10;
      protectedData.ageRange = `${ageRange}-${ageRange + 9}`;
      delete protectedData.dateOfBirth;
      this.metrics.privacyProtectionApplied++;
    }
    
    // Add privacy protection flag
    protectedData._privacyProtected = true;
    
    return protectedData;
  }

  /**
   * Hash an identifier for privacy protection
   * @param {string} identifier - Identifier to hash
   * @returns {string} - Hashed identifier
   * @private
   */
  _hashIdentifier(identifier) {
    // Simple hash function for demonstration
    // In a real implementation, use a cryptographic hash function
    let hash = 0;
    for (let i = 0; i < identifier.length; i++) {
      const char = identifier.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return 'HASHED_' + Math.abs(hash).toString(16);
  }

  /**
   * Sanitize medical domain data (used as fallback)
   * @param {Object} data - Data to sanitize
   * @returns {Object} - Sanitized data
   * @private
   */
  _sanitizeMedicalData(data) {
    if (!data || typeof data !== 'object') {
      return { _domain: 'medical', _sanitized: true };
    }
    
    const result = { ...data, _domain: 'medical', _sanitized: true };
    
    // Sanitize heart rate
    if (result.heartRate !== undefined) {
      result.heartRate = Math.max(
        this.medicalBoundaries.MIN_HEART_RATE,
        Math.min(this.medicalBoundaries.MAX_HEART_RATE, result.heartRate)
      );
    }
    
    // Sanitize blood pressure
    if (result.bloodPressure !== undefined) {
      result.bloodPressure = Math.max(
        this.medicalBoundaries.MIN_BLOOD_PRESSURE,
        Math.min(this.medicalBoundaries.MAX_BLOOD_PRESSURE, result.bloodPressure)
      );
    }
    
    // Sanitize temperature
    if (result.temperature !== undefined) {
      result.temperature = Math.max(
        this.medicalBoundaries.MIN_TEMPERATURE,
        Math.min(this.medicalBoundaries.MAX_TEMPERATURE, result.temperature)
      );
    }
    
    // Sanitize oxygen level
    if (result.oxygenLevel !== undefined) {
      result.oxygenLevel = Math.max(
        this.medicalBoundaries.MIN_OXYGEN_LEVEL,
        Math.min(this.medicalBoundaries.MAX_OXYGEN_LEVEL, result.oxygenLevel)
      );
    }
    
    return result;
  }

  /**
   * Calculate heart rate risk factor
   * @param {number} heartRate - Heart rate in beats per minute
   * @returns {number} - Risk factor (0-1)
   * @private
   */
  _calculateHeartRateRisk(heartRate) {
    if (heartRate < 40) {
      return 0.7; // Bradycardia
    } else if (heartRate > 100) {
      return Math.min(1, 0.3 + (heartRate - 100) / 100); // Tachycardia
    } else {
      return 0; // Normal
    }
  }

  /**
   * Calculate blood pressure risk factor
   * @param {number} bloodPressure - Blood pressure in mmHg
   * @returns {number} - Risk factor (0-1)
   * @private
   */
  _calculateBloodPressureRisk(bloodPressure) {
    if (bloodPressure < 90) {
      return 0.5; // Hypotension
    } else if (bloodPressure > 140) {
      return Math.min(1, 0.3 + (bloodPressure - 140) / 100); // Hypertension
    } else {
      return 0; // Normal
    }
  }

  /**
   * Calculate temperature risk factor
   * @param {number} temperature - Temperature in degrees Celsius
   * @returns {number} - Risk factor (0-1)
   * @private
   */
  _calculateTemperatureRisk(temperature) {
    if (temperature < 35) {
      return 0.6; // Hypothermia
    } else if (temperature > 38) {
      return Math.min(1, 0.3 + (temperature - 38) / 3); // Fever
    } else {
      return 0; // Normal
    }
  }

  /**
   * Calculate oxygen level risk factor
   * @param {number} oxygenLevel - Oxygen level in percent
   * @returns {number} - Risk factor (0-1)
   * @private
   */
  _calculateOxygenRisk(oxygenLevel) {
    if (oxygenLevel < 90) {
      return Math.min(1, 0.5 + (90 - oxygenLevel) / 20); // Hypoxemia
    } else {
      return 0; // Normal
    }
  }

  /**
   * Update metrics based on processed data
   * @param {Object} data - Processed data
   * @private
   */
  _updateMetrics(data) {
    this.metrics.processedDataCount++;
    
    if (data.heartRate !== undefined) {
      this.metrics.totalHeartRate += data.heartRate;
      this.metrics.averageHeartRate = this.metrics.totalHeartRate / this.metrics.processedDataCount;
    }
  }

  /**
   * Get adapter metrics
   * @returns {Object} - Adapter metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Reset adapter metrics
   */
  resetMetrics() {
    this.metrics = {
      processedDataCount: 0,
      boundaryViolations: 0,
      privacyProtectionApplied: 0,
      averageHeartRate: 0,
      totalHeartRate: 0,
      criticalAlerts: 0
    };
    
    this.emit('metrics-reset');
  }
}

/**
 * Create a CSME adapter with recommended settings
 * @param {Object} options - Configuration options
 * @returns {CSMEAdapter} - Configured CSME adapter
 */
function createCSMEAdapter(options = {}) {
  return new CSMEAdapter({
    enableLogging: true,
    strictMode: true,
    enablePrivacyProtection: true,
    ...options
  });
}

module.exports = {
  CSMEAdapter,
  createCSMEAdapter
};
