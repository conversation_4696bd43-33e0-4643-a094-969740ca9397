{"entity": "Control", "entityPlural": "Controls", "apiEndpoint": "/api/v1/novaassure/controls", "fields": [{"name": "name", "label": "Control Name", "type": "text", "required": true, "placeholder": "Enter control name"}, {"name": "description", "label": "Description", "type": "textarea", "required": true, "placeholder": "Enter control description"}, {"name": "framework", "label": "Framework", "type": "select", "options": [{"label": "SOC 2", "value": "soc2"}, {"label": "GDPR", "value": "gdpr"}, {"label": "HIPAA", "value": "hipaa"}, {"label": "ISO 27001", "value": "iso27001"}, {"label": "PCI DSS", "value": "pci-dss"}], "required": true}, {"name": "category", "label": "Category", "type": "select", "options": [{"label": "Access Control", "value": "access-control"}, {"label": "Data Protection", "value": "data-protection"}, {"label": "Network Security", "value": "network-security"}, {"label": "Logging & Monitoring", "value": "logging-monitoring"}, {"label": "Incident Response", "value": "incident-response"}, {"label": "Business Continuity", "value": "business-continuity"}, {"label": "Compliance", "value": "compliance"}], "required": true}, {"name": "status", "label": "Status", "type": "radio", "options": [{"label": "Active", "value": "active"}, {"label": "Inactive", "value": "inactive"}, {"label": "Draft", "value": "draft"}], "required": true}, {"name": "riskLevel", "label": "Risk Level", "type": "select", "options": [{"label": "Low", "value": "low"}, {"label": "Medium", "value": "medium"}, {"label": "High", "value": "high"}, {"label": "Critical", "value": "critical"}], "required": true}, {"name": "implementationDate", "label": "Implementation Date", "type": "date", "required": false}], "submitLabel": "Save Control"}