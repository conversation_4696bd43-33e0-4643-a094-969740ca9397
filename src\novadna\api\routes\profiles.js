/**
 * Profiles API Routes
 * 
 * This module provides API routes for managing emergency medical profiles.
 */

const express = require('express');
const router = express.Router();
const { validateProfile } = require('../middleware/validation');
const { authenticateUser, authorizeProfile } = require('../middleware/auth');

/**
 * @route   POST /api/profiles
 * @desc    Create a new emergency profile
 * @access  Private
 */
router.post('/', authenticateUser, validateProfile, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const profileData = req.body;
    
    // Add user ID to profile data
    profileData.userId = req.user.id;
    
    // Create the profile
    const result = await novaDNA.createEmergencyProfile(profileData);
    
    res.status(201).json({
      status: 'success',
      data: {
        profileId: result.profile.profileId,
        createdAt: result.profile.createdAt,
        verificationId: result.verification.verificationId
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/profiles/:profileId
 * @desc    Get an emergency profile
 * @access  Private
 */
router.get('/:profileId', authenticateUser, authorizeProfile, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { profileId } = req.params;
    
    // In a real implementation, this would fetch the profile from a database
    // For now, we'll assume we have the profile
    const profile = { profileId };
    
    // Get filtered profile based on user's access level
    const filteredProfile = novaDNA.emergencyProfile.getFilteredProfile(profile, 'FULL');
    
    res.json({
      status: 'success',
      data: filteredProfile
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   PUT /api/profiles/:profileId
 * @desc    Update an emergency profile
 * @access  Private
 */
router.put('/:profileId', authenticateUser, authorizeProfile, validateProfile, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { profileId } = req.params;
    const updates = req.body;
    
    // In a real implementation, this would fetch the profile from a database
    // For now, we'll assume we have the profile
    const profile = { profileId };
    
    // Update the profile
    const updatedProfile = novaDNA.emergencyProfile.updateProfile(profile, updates);
    
    // Create verification for the updated profile
    const verification = await novaDNA.blockchainVerifier.createVerification(
      updatedProfile,
      {
        action: 'PROFILE_UPDATED',
        userId: req.user.id
      }
    );
    
    // Create audit trail
    await novaDNA.novaProofConnector.createAuditTrail(
      profileId,
      'PROFILE_UPDATED',
      {
        verificationId: verification.verificationId,
        userId: req.user.id
      }
    );
    
    res.json({
      status: 'success',
      data: {
        profileId: updatedProfile.profileId,
        updatedAt: updatedProfile.updatedAt,
        verificationId: verification.verificationId
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   DELETE /api/profiles/:profileId
 * @desc    Delete an emergency profile
 * @access  Private
 */
router.delete('/:profileId', authenticateUser, authorizeProfile, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { profileId } = req.params;
    
    // In a real implementation, this would delete the profile from a database
    // For now, we'll just create an audit trail
    
    // Create verification for the deletion
    const verification = await novaDNA.blockchainVerifier.createVerification(
      { profileId, action: 'DELETE' },
      {
        action: 'PROFILE_DELETED',
        userId: req.user.id
      }
    );
    
    // Create audit trail
    await novaDNA.novaProofConnector.createAuditTrail(
      profileId,
      'PROFILE_DELETED',
      {
        verificationId: verification.verificationId,
        userId: req.user.id
      }
    );
    
    res.json({
      status: 'success',
      data: {
        profileId,
        deletedAt: new Date().toISOString(),
        verificationId: verification.verificationId
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/profiles/:profileId/access-logs
 * @desc    Get access logs for a profile
 * @access  Private
 */
router.get('/:profileId/access-logs', authenticateUser, authorizeProfile, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { profileId } = req.params;
    const { startDate, endDate, serviceId, accessLevel, limit } = req.query;
    
    // Get access logs
    const logs = novaDNA.progressiveDisclosureSystem.getAccessLogs(profileId, {
      startDate,
      endDate,
      serviceId,
      accessLevel,
      limit: limit ? parseInt(limit, 10) : undefined
    });
    
    res.json({
      status: 'success',
      data: logs
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/profiles/:profileId/verification
 * @desc    Get verification status for a profile
 * @access  Private
 */
router.get('/:profileId/verification', authenticateUser, authorizeProfile, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { profileId } = req.params;
    
    // In a real implementation, this would fetch the verification from a database
    // For now, we'll assume we have the verification ID
    const verificationId = `verification-${profileId}`;
    
    // Get verification status
    const status = novaDNA.blockchainVerifier.getVerificationStatus(verificationId, {
      actor: req.user.id,
      role: 'OWNER',
      ownerVerification: true
    });
    
    if (!status.found) {
      return res.status(404).json({
        status: 'error',
        error: 'Verification not found'
      });
    }
    
    res.json({
      status: 'success',
      data: status
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
