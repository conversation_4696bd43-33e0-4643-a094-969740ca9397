/**
 * Enhanced Audit Controller
 * 
 * This controller provides advanced audit logging functionality including:
 * - Comprehensive filtering
 * - Export capabilities
 * - Analytics and reporting
 * - Tenant-specific audit logs
 */

const AuditService = require('../services/AuditService');
const { ValidationError, AuthorizationError } = require('../utils/errors');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const writeFile = promisify(fs.writeFile);
const csv = require('csv-stringify');
const stringify = promisify(csv.stringify);
const logger = require('../../config/logger');

class EnhancedAuditController {
  constructor() {
    this.auditService = new AuditService();
  }

  /**
   * Get audit logs with advanced filtering
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAuditLogs(req, res, next) {
    try {
      // Parse filters from query parameters
      const filters = {
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        userId: req.query.userId,
        action: req.query.action,
        resourceType: req.query.resourceType,
        resourceId: req.query.resourceId,
        status: req.query.status,
        teamId: req.query.teamId,
        environmentId: req.query.environmentId,
        tenantId: req.query.tenantId,
        page: req.query.page ? parseInt(req.query.page) : 1,
        limit: req.query.limit ? parseInt(req.query.limit) : 100,
        sortBy: req.query.sortBy || 'timestamp',
        sortOrder: req.query.sortOrder || 'desc'
      };

      // Validate date filters
      if (filters.startDate && isNaN(Date.parse(filters.startDate))) {
        throw new ValidationError('Invalid startDate format');
      }
      if (filters.endDate && isNaN(Date.parse(filters.endDate))) {
        throw new ValidationError('Invalid endDate format');
      }

      // Validate pagination
      if (filters.page < 1) {
        throw new ValidationError('Page must be greater than 0');
      }
      if (filters.limit < 1 || filters.limit > 1000) {
        throw new ValidationError('Limit must be between 1 and 1000');
      }

      // Get audit logs
      const auditLogs = await this.auditService.getAuditLogs(filters);

      // Add pagination metadata
      const response = {
        logs: auditLogs.logs,
        pagination: {
          page: filters.page,
          limit: filters.limit,
          totalItems: auditLogs.total,
          totalPages: Math.ceil(auditLogs.total / filters.limit)
        },
        filters: filters
      };

      res.json(response);

      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'audit_log',
        resourceId: 'all',
        details: { filters, count: auditLogs.logs.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      logger.error('Error getting audit logs', { error: error.message });
      next(error);
    }
  }

  /**
   * Export audit logs to CSV
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async exportAuditLogs(req, res, next) {
    try {
      // Parse filters from query parameters (same as getAuditLogs)
      const filters = {
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        userId: req.query.userId,
        action: req.query.action,
        resourceType: req.query.resourceType,
        resourceId: req.query.resourceId,
        status: req.query.status,
        teamId: req.query.teamId,
        environmentId: req.query.environmentId,
        tenantId: req.query.tenantId,
        sortBy: req.query.sortBy || 'timestamp',
        sortOrder: req.query.sortOrder || 'desc',
        // For exports, we don't use pagination - get all logs
        limit: 10000
      };

      // Get audit logs
      const auditLogs = await this.auditService.getAuditLogs(filters);

      // Convert to CSV
      const csvData = await stringify(auditLogs.logs, {
        header: true,
        columns: [
          'id',
          'timestamp',
          'userId',
          'action',
          'resourceType',
          'resourceId',
          'status',
          'ip',
          'userAgent',
          'teamId',
          'environmentId',
          'tenantId'
        ]
      });

      // Set headers for file download
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="audit_logs_${new Date().toISOString()}.csv"`);
      
      // Send CSV data
      res.send(csvData);

      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'EXPORT',
        resourceType: 'audit_log',
        resourceId: 'all',
        details: { filters, count: auditLogs.logs.length, format: 'csv' },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      logger.error('Error exporting audit logs', { error: error.message });
      next(error);
    }
  }

  /**
   * Get audit log statistics
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAuditLogStats(req, res, next) {
    try {
      // Parse filters from query parameters
      const filters = {
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        userId: req.query.userId,
        teamId: req.query.teamId,
        environmentId: req.query.environmentId,
        tenantId: req.query.tenantId
      };

      // Get audit logs
      const auditLogs = await this.auditService.getAuditLogs(filters);

      // Calculate statistics
      const stats = {
        totalEvents: auditLogs.logs.length,
        byAction: {},
        byResourceType: {},
        byStatus: {},
        byUser: {},
        byDay: {},
        byHour: {}
      };

      // Process logs to generate statistics
      auditLogs.logs.forEach(log => {
        // Count by action
        stats.byAction[log.action] = (stats.byAction[log.action] || 0) + 1;
        
        // Count by resource type
        stats.byResourceType[log.resourceType] = (stats.byResourceType[log.resourceType] || 0) + 1;
        
        // Count by status
        stats.byStatus[log.status] = (stats.byStatus[log.status] || 0) + 1;
        
        // Count by user
        if (log.userId) {
          stats.byUser[log.userId] = (stats.byUser[log.userId] || 0) + 1;
        }
        
        // Count by day
        const day = log.timestamp.split('T')[0];
        stats.byDay[day] = (stats.byDay[day] || 0) + 1;
        
        // Count by hour
        const hour = log.timestamp.split('T')[1].split(':')[0];
        stats.byHour[hour] = (stats.byHour[hour] || 0) + 1;
      });

      res.json(stats);

      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'audit_log_stats',
        resourceId: 'all',
        details: { filters },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      logger.error('Error getting audit log stats', { error: error.message });
      next(error);
    }
  }

  /**
   * Get tenant-specific audit logs
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getTenantAuditLogs(req, res, next) {
    try {
      const { tenantId } = req.params;
      
      if (!tenantId) {
        throw new ValidationError('Tenant ID is required');
      }
      
      // Check if user has permission to access tenant logs
      // This would typically be handled by middleware, but adding an extra check here
      if (!req.user.isSuperAdmin && !req.user.tenantId === tenantId) {
        throw new AuthorizationError('Unauthorized: Cannot access logs for this tenant');
      }
      
      // Parse filters from query parameters
      const filters = {
        ...req.query,
        tenantId,
        page: req.query.page ? parseInt(req.query.page) : 1,
        limit: req.query.limit ? parseInt(req.query.limit) : 100
      };
      
      const auditLogs = await this.auditService.getAuditLogs(filters);
      
      // Add pagination metadata
      const response = {
        logs: auditLogs.logs,
        pagination: {
          page: filters.page,
          limit: filters.limit,
          totalItems: auditLogs.total,
          totalPages: Math.ceil(auditLogs.total / filters.limit)
        },
        filters: filters
      };
      
      res.json(response);
      
      // Log audit event
      this.auditService.logTenantEvent(tenantId, {
        userId: req.user.id,
        action: 'GET',
        resourceType: 'audit_log',
        resourceId: 'tenant',
        details: { filters, count: auditLogs.logs.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      logger.error('Error getting tenant audit logs', { error: error.message });
      next(error);
    }
  }
}

module.exports = new EnhancedAuditController();
