/**
 * NovaCore Encryption Utilities
 * 
 * This file provides utility functions for encryption and decryption.
 */

const crypto = require('crypto');
const config = require('../../config');
const logger = require('../../config/logger');

/**
 * Encrypt data using AES-256-GCM
 * @param {string|Object} data - Data to encrypt
 * @param {string} [key] - Encryption key (defaults to config key)
 * @returns {Object} - Encrypted data with IV and auth tag
 */
function encrypt(data, key = config.encryption.secretKey) {
  try {
    // Convert data to string if it's an object
    const dataString = typeof data === 'object' ? JSON.stringify(data) : String(data);
    
    // Generate random IV
    const iv = crypto.randomBytes(16);
    
    // Create cipher
    const cipher = crypto.createCipheriv(
      config.encryption.algorithm,
      Buffer.from(key, 'hex'),
      iv
    );
    
    // Encrypt data
    let encrypted = cipher.update(dataString, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // Get auth tag
    const authTag = cipher.getAuthTag().toString('hex');
    
    return {
      iv: iv.toString('hex'),
      encrypted,
      authTag
    };
  } catch (error) {
    logger.error('Encryption error:', { error });
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Decrypt data using AES-256-GCM
 * @param {Object} encryptedData - Encrypted data object
 * @param {string} encryptedData.iv - Initialization vector
 * @param {string} encryptedData.encrypted - Encrypted data
 * @param {string} encryptedData.authTag - Authentication tag
 * @param {string} [key] - Decryption key (defaults to config key)
 * @param {boolean} [parseJson=true] - Whether to parse result as JSON
 * @returns {string|Object} - Decrypted data
 */
function decrypt(encryptedData, key = config.encryption.secretKey, parseJson = true) {
  try {
    // Create decipher
    const decipher = crypto.createDecipheriv(
      config.encryption.algorithm,
      Buffer.from(key, 'hex'),
      Buffer.from(encryptedData.iv, 'hex')
    );
    
    // Set auth tag
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    
    // Decrypt data
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    // Parse JSON if requested
    if (parseJson) {
      try {
        return JSON.parse(decrypted);
      } catch (e) {
        // If not valid JSON, return as string
        return decrypted;
      }
    }
    
    return decrypted;
  } catch (error) {
    logger.error('Decryption error:', { error });
    throw new Error('Failed to decrypt data');
  }
}

/**
 * Generate a hash for data
 * @param {string|Object} data - Data to hash
 * @param {string} [algorithm='sha256'] - Hash algorithm
 * @returns {string} - Data hash
 */
function hash(data, algorithm = 'sha256') {
  try {
    // Convert data to string if it's an object
    const dataString = typeof data === 'object' ? JSON.stringify(data) : String(data);
    
    // Create hash
    return crypto
      .createHash(algorithm)
      .update(dataString)
      .digest('hex');
  } catch (error) {
    logger.error('Hashing error:', { error });
    throw new Error('Failed to hash data');
  }
}

/**
 * Generate a HMAC signature
 * @param {string|Object} data - Data to sign
 * @param {string} key - Signing key
 * @param {string} [algorithm='sha256'] - HMAC algorithm
 * @returns {string} - HMAC signature
 */
function hmac(data, key, algorithm = 'sha256') {
  try {
    // Convert data to string if it's an object
    const dataString = typeof data === 'object' ? JSON.stringify(data) : String(data);
    
    // Create HMAC
    return crypto
      .createHmac(algorithm, key)
      .update(dataString)
      .digest('hex');
  } catch (error) {
    logger.error('HMAC error:', { error });
    throw new Error('Failed to create HMAC');
  }
}

/**
 * Generate a random encryption key
 * @param {number} [length=32] - Key length in bytes
 * @returns {string} - Random key in hex
 */
function generateKey(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Verify a hash against data
 * @param {string|Object} data - Data to verify
 * @param {string} hash - Hash to verify against
 * @param {string} [algorithm='sha256'] - Hash algorithm
 * @returns {boolean} - Verification result
 */
function verifyHash(data, hash, algorithm = 'sha256') {
  try {
    const computedHash = this.hash(data, algorithm);
    return crypto.timingSafeEqual(
      Buffer.from(computedHash, 'hex'),
      Buffer.from(hash, 'hex')
    );
  } catch (error) {
    logger.error('Hash verification error:', { error });
    return false;
  }
}

module.exports = {
  encrypt,
  decrypt,
  hash,
  hmac,
  generateKey,
  verifyHash
};
