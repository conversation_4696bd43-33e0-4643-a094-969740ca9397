/**
 * Domain Translator
 * 
 * This module implements the Third Law of Comphyological Governance:
 * 
 * "A system shall harmonize external interactions through translational resonance,
 * preserving integrity across domains."
 * 
 * It ensures that cross-domain interactions maintain resonance and integrity
 * by providing resonant mappings between different domains.
 */

const EventEmitter = require('events');
const ResonanceValidator = require('./resonance_validator');

/**
 * Domain Translator class
 */
class DomainTranslator extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      // Domains supported by the translator
      domains: ['cyber', 'financial', 'medical'],
      
      // Whether to preserve resonance during translation
      preserveResonance: true,
      
      // Translation fidelity target (0-1)
      translationFidelity: 0.9,
      
      // Whether to log translations
      logTranslations: false,
      
      // Whether to use strict mode for resonance validation
      strictMode: false,
      
      ...options
    };
    
    // Initialize resonance validator
    this.resonanceValidator = new ResonanceValidator({
      strictMode: this.options.strictMode,
      resonanceLock: this.options.preserveResonance
    });
    
    // Initialize metrics
    this.metrics = {
      translations: 0,
      resonancePreserved: 0,
      resonanceViolations: 0,
      crossDomainFidelity: 0,
      domainPairs: {}
    };
    
    // Initialize domain-specific resonance maps
    this._initializeResonanceMaps();
  }
  
  /**
   * Initialize domain-specific resonance maps
   * @private
   */
  _initializeResonanceMaps() {
    // Domain-specific resonance maps
    this.resonanceMaps = {
      cyber: {
        0.03: { value: 0.03, representation: 'minimal_access', risk: 'very_low' },
        0.06: { value: 0.06, representation: 'standard_access', risk: 'low' },
        0.09: { value: 0.09, representation: 'elevated_access', risk: 'low_medium' },
        0.12: { value: 0.12, representation: 'privileged_access', risk: 'medium' },
        0.13: { value: 0.13, representation: 'admin_access', risk: 'medium_high' },
        0.3: { value: 0.3, representation: 'low_risk', risk: 'low' },
        0.6: { value: 0.6, representation: 'medium_risk', risk: 'medium' },
        0.9: { value: 0.9, representation: 'high_risk', risk: 'high' },
        3: { value: 3, representation: 'low_priority', risk: 'low' },
        6: { value: 6, representation: 'medium_priority', risk: 'medium' },
        9: { value: 9, representation: 'high_priority', risk: 'high' },
        12: { value: 12, representation: 'critical_priority', risk: 'very_high' },
        13: { value: 13, representation: 'emergency_priority', risk: 'extreme' }
      },
      financial: {
        0.03: { value: 0.03, representation: 'micro_transaction', risk: 'very_low' },
        0.06: { value: 0.06, representation: 'small_transaction', risk: 'low' },
        0.09: { value: 0.09, representation: 'medium_transaction', risk: 'low_medium' },
        0.12: { value: 0.12, representation: 'large_transaction', risk: 'medium' },
        0.13: { value: 0.13, representation: 'major_transaction', risk: 'medium_high' },
        0.3: { value: 0.3, representation: 'low_volatility', risk: 'low' },
        0.6: { value: 0.6, representation: 'medium_volatility', risk: 'medium' },
        0.9: { value: 0.9, representation: 'high_volatility', risk: 'high' },
        3: { value: 3, representation: 'low_value', risk: 'low' },
        6: { value: 6, representation: 'medium_value', risk: 'medium' },
        9: { value: 9, representation: 'high_value', risk: 'high' },
        12: { value: 12, representation: 'premium_value', risk: 'very_high' },
        13: { value: 13, representation: 'exceptional_value', risk: 'extreme' }
      },
      medical: {
        0.03: { value: 0.03, representation: 'routine_care', risk: 'very_low' },
        0.06: { value: 0.06, representation: 'standard_care', risk: 'low' },
        0.09: { value: 0.09, representation: 'enhanced_care', risk: 'low_medium' },
        0.12: { value: 0.12, representation: 'specialized_care', risk: 'medium' },
        0.13: { value: 0.13, representation: 'advanced_care', risk: 'medium_high' },
        0.3: { value: 0.3, representation: 'low_severity', risk: 'low' },
        0.6: { value: 0.6, representation: 'medium_severity', risk: 'medium' },
        0.9: { value: 0.9, representation: 'high_severity', risk: 'high' },
        3: { value: 3, representation: 'low_urgency', risk: 'low' },
        6: { value: 6, representation: 'medium_urgency', risk: 'medium' },
        9: { value: 9, representation: 'high_urgency', risk: 'high' },
        12: { value: 12, representation: 'critical_urgency', risk: 'very_high' },
        13: { value: 13, representation: 'life_threatening', risk: 'extreme' }
      }
    };
    
    // Initialize domain pair metrics
    for (const sourceDomain of this.options.domains) {
      for (const targetDomain of this.options.domains) {
        if (sourceDomain !== targetDomain) {
          const pairKey = `${sourceDomain}->${targetDomain}`;
          this.metrics.domainPairs[pairKey] = {
            translations: 0,
            resonancePreserved: 0,
            fidelity: 0
          };
        }
      }
    }
  }
  
  /**
   * Map a value from one domain to another
   * @param {number|Object} value - Value to map
   * @param {string} sourceDomain - Source domain
   * @param {string} targetDomain - Target domain
   * @param {Object} context - Additional context information
   * @returns {Object} - Translation result
   */
  map(value, sourceDomain, targetDomain, context = {}) {
    // Validate domains
    if (!this.options.domains.includes(sourceDomain)) {
      throw new Error(`Invalid source domain: ${sourceDomain}`);
    }
    
    if (!this.options.domains.includes(targetDomain)) {
      throw new Error(`Invalid target domain: ${targetDomain}`);
    }
    
    // If source and target domains are the same, return the value
    if (sourceDomain === targetDomain) {
      return {
        value,
        representation: this._getRepresentation(value, targetDomain),
        sourceDomain,
        targetDomain,
        originalValue: value,
        resonantValue: value,
        resonancePreserved: true,
        context
      };
    }
    
    // Handle different types of value
    if (typeof value === 'object' && value !== null) {
      return this._mapObject(value, sourceDomain, targetDomain, context);
    } else if (typeof value === 'number') {
      return this._mapNumber(value, sourceDomain, targetDomain, context);
    } else {
      throw new Error(`Unsupported value type: ${typeof value}`);
    }
  }
  
  /**
   * Map a numeric value from one domain to another
   * @param {number} value - Numeric value to map
   * @param {string} sourceDomain - Source domain
   * @param {string} targetDomain - Target domain
   * @param {Object} context - Additional context information
   * @returns {Object} - Translation result
   * @private
   */
  _mapNumber(value, sourceDomain, targetDomain, context = {}) {
    // Ensure value is resonant if preserveResonance is enabled
    let resonantValue = value;
    let wasHarmonized = false;
    
    if (this.options.preserveResonance) {
      const validationResult = this.resonanceValidator.validate(value);
      
      if (!validationResult.isResonant) {
        if (validationResult.isValid && validationResult.harmonizedValue !== null) {
          resonantValue = validationResult.harmonizedValue;
          wasHarmonized = true;
        } else {
          throw new Error(`Value ${value} is not resonant and cannot be harmonized`);
        }
      }
    }
    
    // Get domain-specific representation in source domain
    const sourceRepresentation = this._getRepresentation(resonantValue, sourceDomain);
    
    // Get equivalent resonant value in target domain
    const targetValue = resonantValue; // In our implementation, we preserve the numeric value
    
    // Get domain-specific representation in target domain
    const targetRepresentation = this._getRepresentation(targetValue, targetDomain);
    
    // Update metrics
    this.metrics.translations++;
    
    const pairKey = `${sourceDomain}->${targetDomain}`;
    this.metrics.domainPairs[pairKey].translations++;
    
    if (resonantValue === targetValue) {
      this.metrics.resonancePreserved++;
      this.metrics.domainPairs[pairKey].resonancePreserved++;
    } else {
      this.metrics.resonanceViolations++;
    }
    
    this.metrics.crossDomainFidelity = this.metrics.resonancePreserved / this.metrics.translations;
    this.metrics.domainPairs[pairKey].fidelity = this.metrics.domainPairs[pairKey].resonancePreserved / 
                                                this.metrics.domainPairs[pairKey].translations;
    
    // Create translation result
    const translationResult = {
      value: targetValue,
      representation: targetRepresentation,
      sourceDomain,
      targetDomain,
      originalValue: value,
      resonantValue,
      wasHarmonized,
      resonancePreserved: resonantValue === targetValue,
      sourceRepresentation,
      context
    };
    
    // Emit translation event
    this.emit('value-translated', translationResult);
    
    // Log translation if enabled
    if (this.options.logTranslations) {
      console.log(`Value translated: ${value} (${sourceDomain}) -> ${targetValue} (${targetDomain})`);
      console.log(`Representation: ${sourceRepresentation} -> ${targetRepresentation}`);
    }
    
    return translationResult;
  }
  
  /**
   * Map an object value from one domain to another
   * @param {Object} value - Object value to map
   * @param {string} sourceDomain - Source domain
   * @param {string} targetDomain - Target domain
   * @param {Object} context - Additional context information
   * @returns {Object} - Translation result
   * @private
   */
  _mapObject(value, sourceDomain, targetDomain, context = {}) {
    // Create a copy of the value to avoid modifying the original
    const mappedValue = { ...value };
    
    // Map each numeric property
    for (const key in mappedValue) {
      if (typeof mappedValue[key] === 'number') {
        const translationResult = this._mapNumber(mappedValue[key], sourceDomain, targetDomain, {
          ...context,
          property: key
        });
        
        mappedValue[key] = translationResult.value;
      } else if (typeof mappedValue[key] === 'object' && mappedValue[key] !== null) {
        mappedValue[key] = this._mapObject(mappedValue[key], sourceDomain, targetDomain, {
          ...context,
          property: key
        });
      }
    }
    
    return mappedValue;
  }
  
  /**
   * Get domain-specific representation for a value
   * @param {number} value - Value to get representation for
   * @param {string} domain - Domain
   * @returns {string} - Domain-specific representation
   * @private
   */
  _getRepresentation(value, domain) {
    // Find the closest resonant value in the domain
    const resonantValues = Object.keys(this.resonanceMaps[domain]).map(Number);
    
    const closestValue = resonantValues.reduce((closest, current) => {
      return Math.abs(value - current) < Math.abs(value - closest) ? current : closest;
    }, resonantValues[0]);
    
    // Get representation for the closest resonant value
    return this.resonanceMaps[domain][closestValue].representation;
  }
  
  /**
   * Get metrics
   * @returns {Object} - Current metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      translations: 0,
      resonancePreserved: 0,
      resonanceViolations: 0,
      crossDomainFidelity: 0,
      domainPairs: {}
    };
    
    // Reset domain pair metrics
    for (const sourceDomain of this.options.domains) {
      for (const targetDomain of this.options.domains) {
        if (sourceDomain !== targetDomain) {
          const pairKey = `${sourceDomain}->${targetDomain}`;
          this.metrics.domainPairs[pairKey] = {
            translations: 0,
            resonancePreserved: 0,
            fidelity: 0
          };
        }
      }
    }
  }
}

module.exports = DomainTranslator;
