/**
 * NovaFuse Universal API Connector - Logger
 * 
 * This module provides logging functionality for the UAC.
 */

const winston = require('winston');
const { format } = winston;

// Get log level from environment
const LOG_LEVEL = process.env.LOG_LEVEL || 'info';

// Create custom format
const customFormat = format.combine(
  format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  format.errors({ stack: true }),
  format.splat(),
  format.json()
);

// Create console transport
const consoleTransport = new winston.transports.Console({
  format: format.combine(
    format.colorize(),
    format.printf(({ timestamp, level, message, module, ...meta }) => {
      const moduleStr = module ? `[${module}] ` : '';
      const metaStr = Object.keys(meta).length ? `\n${JSON.stringify(meta, null, 2)}` : '';
      return `${timestamp} ${level}: ${moduleStr}${message}${metaStr}`;
    })
  )
});

// Create default logger
const defaultLogger = winston.createLogger({
  level: LOG_LEVEL,
  format: customFormat,
  defaultMeta: { service: 'nova-connect' },
  transports: [consoleTransport]
});

// Add file transport in production
if (process.env.NODE_ENV === 'production') {
  defaultLogger.add(new winston.transports.File({ 
    filename: 'logs/error.log', 
    level: 'error',
    maxsize: 10485760, // 10MB
    maxFiles: 10
  }));
  
  defaultLogger.add(new winston.transports.File({ 
    filename: 'logs/combined.log',
    maxsize: 10485760, // 10MB
    maxFiles: 10
  }));
}

/**
 * Create a logger for a specific module
 * @param {string} module - Module name
 * @returns {winston.Logger} Logger instance
 */
function createLogger(module) {
  return defaultLogger.child({ module });
}

module.exports = {
  createLogger,
  defaultLogger
};
