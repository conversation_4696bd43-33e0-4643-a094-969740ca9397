import { useState, useEffect } from "react";
import Head from "next/head";

export default function PartnerPortal() {
  const [activeTab, setActiveTab] = useState("dashboard");
  const [loading, setLoading] = useState(true);
  const [apiUsage, setApiUsage] = useState([]);
  const [revenue, setRevenue] = useState({
    current: 0,
    previous: 0,
    projected: 0
  });
  const [integrations, setIntegrations] = useState([]);

  useEffect(() => {
    // In a real implementation, this would fetch from an API
    setTimeout(() => {
      // Mock API usage data
      const usageData = [
        { date: "2025-01-01", calls: 1250, revenue: 125.00 },
        { date: "2025-01-02", calls: 1340, revenue: 134.00 },
        { date: "2025-01-03", calls: 1100, revenue: 110.00 },
        { date: "2025-01-04", calls: 1420, revenue: 142.00 },
        { date: "2025-01-05", calls: 1550, revenue: 155.00 },
        { date: "2025-01-06", calls: 1700, revenue: 170.00 },
        { date: "2025-01-07", calls: 1850, revenue: 185.00 },
      ];
      
      setApiUsage(usageData);
      
      // Mock revenue data
      setRevenue({
        current: 1021.00,
        previous: 875.00,
        projected: 4500.00
      });
      
      // Mock integrations data
      setIntegrations([
        {
          id: "int-001",
          name: "User Authentication API",
          status: "active",
          calls: 12500,
          revenue: 1250.00,
          lastUpdated: "2025-01-07"
        },
        {
          id: "int-002",
          name: "Compliance Reporting API",
          status: "active",
          calls: 8700,
          revenue: 870.00,
          lastUpdated: "2025-01-05"
        },
        {
          id: "int-003",
          name: "Risk Assessment API",
          status: "pending",
          calls: 0,
          revenue: 0,
          lastUpdated: "2025-01-02"
        }
      ]);
      
      setLoading(false);
    }, 1000);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>NovaFuse Partner Portal</title>
        <meta name="description" content="NovaFuse Partner Portal - Manage your API integrations" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8 flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">NovaFuse Partner Portal</h1>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500">
              Partner: <span className="font-semibold text-gray-900">Acme Corporation</span>
            </div>
            <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
              Account Settings
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="flex -mb-px">
            <button
              onClick={() => setActiveTab("dashboard")}
              className={`py-4 px-6 font-medium text-sm ${
                activeTab === "dashboard"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Dashboard
            </button>
            <button
              onClick={() => setActiveTab("integrations")}
              className={`py-4 px-6 font-medium text-sm ${
                activeTab === "integrations"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Integrations
            </button>
            <button
              onClick={() => setActiveTab("analytics")}
              className={`py-4 px-6 font-medium text-sm ${
                activeTab === "analytics"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Analytics
            </button>
            <button
              onClick={() => setActiveTab("documentation")}
              className={`py-4 px-6 font-medium text-sm ${
                activeTab === "documentation"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Documentation
            </button>
            <button
              onClick={() => setActiveTab("settings")}
              className={`py-4 px-6 font-medium text-sm ${
                activeTab === "settings"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Settings
            </button>
          </nav>
        </div>

        {/* Dashboard Tab */}
        {activeTab === "dashboard" && (
          <div>
            <h2 className="text-2xl font-bold mb-6">Partner Dashboard</h2>
            
            {loading ? (
              <p>Loading dashboard data...</p>
            ) : (
              <>
                {/* Revenue Summary */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold mb-2">Current Month Revenue</h3>
                    <p className="text-3xl font-bold text-blue-600">${revenue.current.toFixed(2)}</p>
                    <p className="text-sm text-gray-500 mt-2">
                      {revenue.current > revenue.previous ? (
                        <span className="text-green-500">↑ {(((revenue.current - revenue.previous) / revenue.previous) * 100).toFixed(1)}%</span>
                      ) : (
                        <span className="text-red-500">↓ {(((revenue.previous - revenue.current) / revenue.previous) * 100).toFixed(1)}%</span>
                      )}
                      {" "}vs last month
                    </p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold mb-2">API Calls</h3>
                    <p className="text-3xl font-bold text-blue-600">
                      {apiUsage.reduce((total, day) => total + day.calls, 0).toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-500 mt-2">This month</p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold mb-2">Projected Annual Revenue</h3>
                    <p className="text-3xl font-bold text-blue-600">${revenue.projected.toFixed(2)}</p>
                    <p className="text-sm text-gray-500 mt-2">Based on current usage</p>
                  </div>
                </div>
                
                {/* Recent Activity */}
                <div className="bg-white p-6 rounded-lg shadow mb-8">
                  <h3 className="text-lg font-semibold mb-4">Recent API Usage</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead>
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">API Calls</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {apiUsage.map((day, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{day.date}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{day.calls.toLocaleString()}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${day.revenue.toFixed(2)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
                
                {/* Integration Status */}
                <div className="bg-white p-6 rounded-lg shadow">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Integration Status</h3>
                    <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">View All</button>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead>
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Integration</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">API Calls</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {integrations.map((integration) => (
                          <tr key={integration.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{integration.name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                integration.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {integration.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{integration.calls.toLocaleString()}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${integration.revenue.toFixed(2)}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{integration.lastUpdated}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </>
            )}
          </div>
        )}

        {/* Integrations Tab */}
        {activeTab === "integrations" && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Your Integrations</h2>
              <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Create New Integration
              </button>
            </div>
            
            {loading ? (
              <p>Loading integrations...</p>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {integrations.map((integration) => (
                  <div key={integration.id} className="bg-white border rounded-lg shadow overflow-hidden">
                    <div className="p-6">
                      <div className="flex justify-between items-start mb-4">
                        <h3 className="text-lg font-semibold">{integration.name}</h3>
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          integration.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {integration.status}
                        </span>
                      </div>
                      <div className="mb-4">
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-gray-500">API Calls:</span>
                          <span className="font-medium">{integration.calls.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-gray-500">Revenue:</span>
                          <span className="font-medium">${integration.revenue.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Last Updated:</span>
                          <span className="font-medium">{integration.lastUpdated}</span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button className="flex-1 bg-blue-600 text-white px-3 py-1.5 rounded text-sm hover:bg-blue-700">
                          Manage
                        </button>
                        <button className="flex-1 border border-blue-600 text-blue-600 px-3 py-1.5 rounded text-sm hover:bg-blue-50">
                          View Docs
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
                
                {/* Add New Integration Card */}
                <div className="bg-white border border-dashed rounded-lg shadow-sm overflow-hidden">
                  <div className="p-6 flex flex-col items-center justify-center h-full">
                    <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold mb-2">Create New Integration</h3>
                    <p className="text-gray-500 text-sm text-center mb-4">
                      Add a new API integration to expand your offerings
                    </p>
                    <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                      Get Started
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === "analytics" && (
          <div>
            <h2 className="text-2xl font-bold mb-6">Analytics</h2>
            <p className="text-gray-500">Detailed analytics coming soon...</p>
          </div>
        )}

        {/* Documentation Tab */}
        {activeTab === "documentation" && (
          <div>
            <h2 className="text-2xl font-bold mb-6">API Documentation</h2>
            <p className="text-gray-500">API documentation coming soon...</p>
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === "settings" && (
          <div>
            <h2 className="text-2xl font-bold mb-6">Partner Settings</h2>
            <p className="text-gray-500">Settings coming soon...</p>
          </div>
        )}
      </main>

      <footer className="bg-gray-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} NovaFuse. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
