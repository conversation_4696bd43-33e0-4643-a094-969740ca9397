import React, { useState, useEffect } from 'react';
import AutoForm from '../components/AutoForm';
import SchemaBuilder from '../components/SchemaBuilder';
import { getLocalSchema } from '../services/schemaService';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import 'react-tabs/style/react-tabs.css';

/**
 * Universal UI Demo Page
 * 
 * A demo page to showcase the universal UI components.
 */
const UniversalUIDemo = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [selectedForm, setSelectedForm] = useState('user-registration');
  const [formSchema, setFormSchema] = useState(null);
  const [formData, setFormData] = useState({});
  const [formResult, setFormResult] = useState(null);
  const [customSchema, setCustomSchema] = useState(null);
  
  // Load form schema
  useEffect(() => {
    const schema = getLocalSchema(selectedForm);
    setFormSchema(schema);
    setFormData({});
    setFormResult(null);
  }, [selectedForm]);
  
  // Handle form submission
  const handleFormSubmit = (data) => {
    setFormResult(data);
    console.log('Form submitted:', data);
  };
  
  // Handle schema save
  const handleSchemaSave = (schema) => {
    setCustomSchema(schema);
    setActiveTab(2); // Switch to Custom Form tab
  };
  
  return (
    <div className="container mt-4">
      <h1 className="mb-4">Universal UI Demo</h1>
      
      <Tabs selectedIndex={activeTab} onSelect={index => setActiveTab(index)}>
        <TabList>
          <Tab>Sample Forms</Tab>
          <Tab>Schema Builder</Tab>
          {customSchema && <Tab>Custom Form</Tab>}
        </TabList>
        
        <TabPanel>
          <div className="row mt-4">
            <div className="col-md-4">
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">Select Form</h3>
                </div>
                <div className="card-body">
                  <div className="form-group">
                    <select
                      className="form-control"
                      value={selectedForm}
                      onChange={(e) => setSelectedForm(e.target.value)}
                    >
                      <option value="user-registration">User Registration</option>
                      <option value="user-login">User Login</option>
                      <option value="control-create">Create Control</option>
                    </select>
                  </div>
                </div>
              </div>
              
              {formSchema && (
                <div className="card mt-3">
                  <div className="card-header">
                    <h3 className="card-title">Form Schema</h3>
                  </div>
                  <div className="card-body">
                    <pre className="bg-light p-3 rounded">
                      {JSON.stringify(formSchema, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
            
            <div className="col-md-4">
              {formSchema && (
                <div className="card">
                  <div className="card-header">
                    <h3 className="card-title">Auto-Generated Form</h3>
                  </div>
                  <div className="card-body">
                    <AutoForm
                      schema={formSchema}
                      onSubmit={handleFormSubmit}
                      initialValues={formData}
                    />
                  </div>
                </div>
              )}
            </div>
            
            <div className="col-md-4">
              {formResult && (
                <div className="card">
                  <div className="card-header">
                    <h3 className="card-title">Form Result</h3>
                  </div>
                  <div className="card-body">
                    <pre className="bg-light p-3 rounded">
                      {JSON.stringify(formResult, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          </div>
        </TabPanel>
        
        <TabPanel>
          <div className="row mt-4">
            <div className="col-md-12">
              <SchemaBuilder onSave={handleSchemaSave} />
            </div>
          </div>
        </TabPanel>
        
        {customSchema && (
          <TabPanel>
            <div className="row mt-4">
              <div className="col-md-6">
                <div className="card">
                  <div className="card-header">
                    <h3 className="card-title">Custom Form</h3>
                  </div>
                  <div className="card-body">
                    <AutoForm
                      schema={customSchema}
                      onSubmit={(data) => {
                        console.log('Custom form submitted:', data);
                        alert('Form submitted successfully!');
                      }}
                    />
                  </div>
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="card">
                  <div className="card-header">
                    <h3 className="card-title">Custom Schema</h3>
                  </div>
                  <div className="card-body">
                    <pre className="bg-light p-3 rounded">
                      {JSON.stringify(customSchema, null, 2)}
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          </TabPanel>
        )}
      </Tabs>
      
      <div className="mt-5 p-3 bg-light rounded">
        <h3>How It Works</h3>
        <p>
          This demo showcases a universal UI system that automatically generates forms based on JSON schemas.
          The system consists of two main components:
        </p>
        <ul>
          <li><strong>AutoForm</strong>: A React component that renders a form based on a schema</li>
          <li><strong>SchemaBuilder</strong>: A visual tool for creating form schemas</li>
        </ul>
        <p>
          The schema defines the form's fields, validation rules, and appearance. This approach allows for:
        </p>
        <ul>
          <li>Dynamic form generation without writing code</li>
          <li>Consistent UI across the application</li>
          <li>Easy maintenance and updates</li>
          <li>API-driven UI that can adapt to changes</li>
        </ul>
        <p>
          In a production environment, these schemas would be stored in a database and served by an API,
          allowing for complete UI customization without code changes.
        </p>
      </div>
    </div>
  );
};

export default UniversalUIDemo;
