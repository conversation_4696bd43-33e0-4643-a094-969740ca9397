import React from 'react';
import DiagramTemplate from '../DiagramTemplate';

// Define the diagram data
const elements = [
  {
    id: 'alignment-architecture',
    top: 50,
    left: 350,
    width: 300,
    text: '3-6-9-12-13 Alignment Architecture',
    number: '1',
    bold: true,
    fontSize: '20px',
    backgroundColor: '#e6f7ff'
  },
  // 3-Point Alignment
  {
    id: 'three-point',
    top: 120,
    left: 350,
    width: 300,
    text: '3-Point Alignment (Core Infrastructure)',
    number: '2',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'governance-infra',
    top: 180,
    left: 150,
    width: 200,
    text: 'Governance Infrastructure',
    number: '3',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'detection-infra',
    top: 180,
    left: 400,
    width: 200,
    text: 'Detection Infrastructure',
    number: '4',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'response-infra',
    top: 180,
    left: 650,
    width: 200,
    text: 'Response Infrastructure',
    number: '5',
    fontSize: '14px',
    backgroundColor: '#fff2e8'
  },
  // 6-Point Alignment
  {
    id: 'six-point',
    top: 250,
    left: 350,
    width: 300,
    text: '6-Point Alignment (Data Processing)',
    number: '6',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'data-ingestion',
    top: 310,
    left: 50,
    width: 150,
    text: 'Data Ingestion',
    number: '7',
    fontSize: '12px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'data-normalization',
    top: 310,
    left: 225,
    width: 150,
    text: 'Data Normalization',
    number: '8',
    fontSize: '12px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'data-quality',
    top: 310,
    left: 400,
    width: 150,
    text: 'Data Quality Assessment',
    number: '9',
    fontSize: '12px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'pattern-detection',
    top: 310,
    left: 575,
    width: 150,
    text: 'Pattern Detection',
    number: '10',
    fontSize: '12px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'decision-engine',
    top: 310,
    left: 750,
    width: 150,
    text: 'Decision Engine',
    number: '11',
    fontSize: '12px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'action-execution',
    top: 310,
    left: 925,
    width: 150,
    text: 'Action Execution',
    number: '12',
    fontSize: '12px',
    backgroundColor: '#fff2e8'
  },
  // 9-Point Alignment
  {
    id: 'nine-point',
    top: 380,
    left: 350,
    width: 300,
    text: '9-Point Alignment (Industry Applications)',
    number: '13',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'healthcare',
    top: 440,
    left: 50,
    width: 120,
    text: 'Healthcare',
    number: '14',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'financial',
    top: 440,
    left: 180,
    width: 120,
    text: 'Financial Services',
    number: '15',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'manufacturing',
    top: 440,
    left: 310,
    width: 120,
    text: 'Manufacturing',
    number: '16',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'energy',
    top: 440,
    left: 440,
    width: 120,
    text: 'Energy',
    number: '17',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'retail',
    top: 440,
    left: 570,
    width: 120,
    text: 'Retail',
    number: '18',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'government',
    top: 440,
    left: 700,
    width: 120,
    text: 'Government',
    number: '19',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'education',
    top: 440,
    left: 830,
    width: 120,
    text: 'Education',
    number: '20',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'transportation',
    top: 440,
    left: 960,
    width: 120,
    text: 'Transportation',
    number: '21',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'ai-governance',
    top: 490,
    left: 500,
    width: 120,
    text: 'AI Governance',
    number: '22',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  // 12-Point Alignment
  {
    id: 'twelve-point',
    top: 540,
    left: 350,
    width: 300,
    text: '12 Integration Points',
    number: '23',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  // 13-Point Alignment
  {
    id: 'thirteen-point',
    top: 600,
    left: 350,
    width: 300,
    text: '13 NovaFuse Components',
    number: '24',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  }
];

const connections = [
  // Connect Alignment Architecture to 3-Point
  {
    start: { x: 500, y: 100 },
    end: { x: 500, y: 120 },
    type: 'arrow'
  },
  // Connect 3-Point to components
  {
    start: { x: 400, y: 170 },
    end: { x: 250, y: 180 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 170 },
    end: { x: 500, y: 180 },
    type: 'arrow'
  },
  {
    start: { x: 600, y: 170 },
    end: { x: 750, y: 180 },
    type: 'arrow'
  },
  // Connect 3-Point to 6-Point
  {
    start: { x: 500, y: 220 },
    end: { x: 500, y: 250 },
    type: 'arrow'
  },
  // Connect 6-Point to components
  {
    start: { x: 350, y: 300 },
    end: { x: 125, y: 310 },
    type: 'arrow'
  },
  {
    start: { x: 400, y: 300 },
    end: { x: 300, y: 310 },
    type: 'arrow'
  },
  {
    start: { x: 450, y: 300 },
    end: { x: 475, y: 310 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 300 },
    end: { x: 650, y: 310 },
    type: 'arrow'
  },
  {
    start: { x: 550, y: 300 },
    end: { x: 825, y: 310 },
    type: 'arrow'
  },
  {
    start: { x: 600, y: 300 },
    end: { x: 1000, y: 310 },
    type: 'arrow'
  },
  // Connect 6-Point to 9-Point
  {
    start: { x: 500, y: 350 },
    end: { x: 500, y: 380 },
    type: 'arrow'
  },
  // Connect 9-Point to industry applications
  {
    start: { x: 350, y: 430 },
    end: { x: 110, y: 440 },
    type: 'arrow'
  },
  {
    start: { x: 375, y: 430 },
    end: { x: 240, y: 440 },
    type: 'arrow'
  },
  {
    start: { x: 400, y: 430 },
    end: { x: 370, y: 440 },
    type: 'arrow'
  },
  {
    start: { x: 425, y: 430 },
    end: { x: 500, y: 440 },
    type: 'arrow'
  },
  {
    start: { x: 450, y: 430 },
    end: { x: 630, y: 440 },
    type: 'arrow'
  },
  {
    start: { x: 475, y: 430 },
    end: { x: 760, y: 440 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 430 },
    end: { x: 890, y: 440 },
    type: 'arrow'
  },
  {
    start: { x: 525, y: 430 },
    end: { x: 1020, y: 440 },
    type: 'arrow'
  },
  {
    start: { x: 550, y: 430 },
    end: { x: 560, y: 490 },
    type: 'arrow'
  },
  // Connect 9-Point to 12-Point
  {
    start: { x: 500, y: 510 },
    end: { x: 500, y: 540 },
    type: 'arrow'
  },
  // Connect 12-Point to 13-Point
  {
    start: { x: 500, y: 580 },
    end: { x: 500, y: 600 },
    type: 'arrow'
  }
];

const AlignmentArchitecture: React.FC = () => {
  return (
    <DiagramTemplate 
      elements={elements} 
      connections={connections} 
      width="1100px" 
      height="650px" 
    />
  );
};

export default AlignmentArchitecture;
