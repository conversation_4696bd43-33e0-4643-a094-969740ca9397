import { NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs'
import { prisma } from '@/lib/prisma'

interface ConversionData {
  productId: string
  networkId: string
  revenue: number
  triadicMetrics: {
    psi: number
    phi: number
    kappa: number
  }
  status: 'pending' | 'completed' | 'failed'
}

export async function POST(req: Request) {
  const { userId } = auth()
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const body = await req.json() as ConversionData
    
    // Get product and network details
    const product = await prisma.product.findUnique({
      where: {
        id: body.productId
      }
    })

    const network = await prisma.affiliateNetwork.findUnique({
      where: {
        id: body.networkId
      }
    })

    if (!product || !network) {
      return NextResponse.json({ error: 'Product or network not found' }, { status: 404 })
    }

    // Create conversion
    const conversion = await prisma.conversion.create({
      data: {
        userId,
        productId: body.productId,
        networkId: body.networkId,
        revenue: body.revenue,
        status: body.status,
        triadicMetrics: {
          create: {
            psi: body.triadicMetrics.psi,
            phi: body.triadicMetrics.phi,
            kappa: body.triadicMetrics.kappa
          }
        }
      }
    })

    // Update network stats
    await prisma.affiliateNetwork.update({
      where: { id: body.networkId },
      data: {
        stats: {
          increment: {
            totalRevenue: body.revenue,
            totalConversions: 1
          }
        }
      }
    })

    return NextResponse.json(conversion)
  } catch (error) {
    console.error('Error creating conversion:', error)
    return NextResponse.json({ error: 'Failed to create conversion' }, { status: 500 })
  }
}

export async function GET(req: Request) {
  const { userId } = auth()
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const conversions = await prisma.conversion.findMany({
      where: {
        userId: userId
      },
      include: {
        product: true,
        network: true,
        triadicMetrics: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(conversions)
  } catch (error) {
    console.error('Error fetching conversions:', error)
    return NextResponse.json({ error: 'Failed to fetch conversions' }, { status: 500 })
  }
}
