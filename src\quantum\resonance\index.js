/**
 * Resonance Module
 *
 * This module exports resonance-related components for the Comphyological framework.
 * It includes:
 * 1. Basic Resonance Listener
 * 2. Advanced Resonance Detector with harmonic detection and forecasting
 * 3. Resonance Control System for maintaining quantum silence
 * 4. FFT implementation for spectral analysis
 */

const { ResonanceListener, createResonanceListener } = require('./resonance_listener');
const { AdvancedResonanceDetector, createAdvancedResonanceDetector } = require('./advanced_detection');
const { ResonanceControlSystem, createResonanceControlSystem } = require('./control_system');
const { FFT } = require('./fft');

/**
 * Create all resonance components
 * @param {Object} options - Configuration options
 * @returns {Object} - Object containing all resonance components
 */
function createResonanceComponents(options = {}) {
  // Create resonance listener
  const resonanceListener = createResonanceListener({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    targetFrequency: options.targetFrequency || 396,
    precisionFFT: options.precisionFFT || 0.001,
    quantumVacuumNoise: options.quantumVacuumNoise !== undefined ? options.quantumVacuumNoise : true,
    crossDomainPhaseAlignment: options.crossDomainPhaseAlignment !== undefined ? options.crossDomainPhaseAlignment : true,
    silenceThreshold: options.silenceThreshold || 0.001,
    detectionInterval: options.detectionInterval || 100,
    maxHistoryLength: options.maxHistoryLength || 100,
    ...options.resonanceListenerOptions
  });

  // Create advanced resonance detector
  const advancedResonanceDetector = createAdvancedResonanceDetector({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    targetFrequency: options.targetFrequency || 396,
    precisionFFT: options.precisionFFT || 0.001,
    quantumVacuumNoise: options.quantumVacuumNoise !== undefined ? options.quantumVacuumNoise : true,
    crossDomainPhaseAlignment: options.crossDomainPhaseAlignment !== undefined ? options.crossDomainPhaseAlignment : true,
    silenceThreshold: options.silenceThreshold || 0.001,
    adaptiveThreshold: options.adaptiveThreshold !== undefined ? options.adaptiveThreshold : true,
    adaptiveRate: options.adaptiveRate || 0.01,
    harmonicDetection: options.harmonicDetection !== undefined ? options.harmonicDetection : true,
    harmonicThreshold: options.harmonicThreshold || 0.05,
    forecastingWindow: options.forecastingWindow || 10,
    resonantSlopeWindow: options.resonantSlopeWindow || 5,
    sampleRate: options.sampleRate || 44100,
    fftSize: options.fftSize || 4096,
    maxHistoryLength: options.maxHistoryLength || 100,
    ...options.advancedResonanceDetectorOptions
  });

  // Create resonance control system
  const resonanceControlSystem = createResonanceControlSystem({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    targetFrequency: options.targetFrequency || 396,
    targetComphyon: options.targetComphyon || 0,
    controlInterval: options.controlInterval || 200,
    feedbackGain: options.feedbackGain || 0.1,
    adaptiveGain: options.adaptiveGain !== undefined ? options.adaptiveGain : true,
    minGain: options.minGain || 0.01,
    maxGain: options.maxGain || 0.5,
    weightAdjustmentRate: options.weightAdjustmentRate || 0.05,
    parameterAdjustmentRate: options.parameterAdjustmentRate || 0.02,
    stabilizationWindow: options.stabilizationWindow || 5,
    harmonicEntrainment: options.harmonicEntrainment !== undefined ? options.harmonicEntrainment : true,
    entrainmentStrength: options.entrainmentStrength || 0.1,
    optimizationStrategy: options.optimizationStrategy || 'gradient',
    optimizationStepSize: options.optimizationStepSize || 0.01,
    optimizationIterations: options.optimizationIterations || 10,
    maxHistoryLength: options.maxHistoryLength || 100,
    ...options.resonanceControlSystemOptions
  });

  return {
    resonanceListener,
    advancedResonanceDetector,
    resonanceControlSystem
  };
}

/**
 * Create an enhanced resonance listener that uses the advanced resonance detector
 * @param {Object} options - Configuration options
 * @returns {Object} - Enhanced resonance listener
 */
function createEnhancedResonanceListener(options = {}) {
  // Create advanced resonance detector
  const detector = createAdvancedResonanceDetector({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    targetFrequency: options.targetFrequency || 396,
    precisionFFT: options.precisionFFT || 0.001,
    quantumVacuumNoise: options.quantumVacuumNoise !== undefined ? options.quantumVacuumNoise : true,
    crossDomainPhaseAlignment: options.crossDomainPhaseAlignment !== undefined ? options.crossDomainPhaseAlignment : true,
    silenceThreshold: options.silenceThreshold || 0.001,
    adaptiveThreshold: options.adaptiveThreshold !== undefined ? options.adaptiveThreshold : true,
    adaptiveRate: options.adaptiveRate || 0.01,
    harmonicDetection: options.harmonicDetection !== undefined ? options.harmonicDetection : true,
    harmonicThreshold: options.harmonicThreshold || 0.05,
    forecastingWindow: options.forecastingWindow || 10,
    resonantSlopeWindow: options.resonantSlopeWindow || 5,
    sampleRate: options.sampleRate || 44100,
    fftSize: options.fftSize || 4096,
    maxHistoryLength: options.maxHistoryLength || 100,
    ...options.advancedResonanceDetectorOptions
  });

  // Create enhanced listener
  const enhancedListener = {
    // Internal state
    _tensorCore: null,
    _interval: null,
    _detectionInterval: options.detectionInterval || 100,

    // Start listening
    startListening(tensorCore) {
      this._tensorCore = tensorCore;

      // Clear existing interval if any
      if (this._interval) {
        clearInterval(this._interval);
      }

      // Set up interval
      this._interval = setInterval(() => {
        // Get latest metrics from tensor core
        const metrics = this._tensorCore.getMetrics();

        // Analyze resonance
        detector.analyzeResonance({
          comphyon: metrics.lastComphyonValue,
          energies: metrics.lastEnergies || { csde: 0, csfe: 0, csme: 0 },
          weights: metrics.lastWeights || { csde: 1/3, csfe: 1/3, csme: 1/3 }
        });
      }, this._detectionInterval);

      return this;
    },

    // Stop listening
    stopListening() {
      if (this._interval) {
        clearInterval(this._interval);
        this._interval = null;
      }

      return this;
    },

    // Get resonance state
    getResonanceState() {
      return detector.getResonanceState();
    },

    // Get resonance history
    getResonanceHistory() {
      return detector.getResonanceHistory();
    },

    // Get harmonics
    getHarmonics() {
      return detector.getResonanceState().harmonics;
    },

    // Get forecast
    getForecast() {
      return detector.getResonanceState().forecast;
    },

    // Get resonant slope
    getResonantSlope() {
      return detector.getResonanceState().resonantSlope;
    }
  };

  return enhancedListener;
}

module.exports = {
  // Resonance Listener
  ResonanceListener,
  createResonanceListener,

  // Advanced Resonance Detector
  AdvancedResonanceDetector,
  createAdvancedResonanceDetector,

  // Resonance Control System
  ResonanceControlSystem,
  createResonanceControlSystem,

  // Enhanced Resonance Listener
  createEnhancedResonanceListener,

  // FFT
  FFT,

  // Factory function
  createResonanceComponents
};
