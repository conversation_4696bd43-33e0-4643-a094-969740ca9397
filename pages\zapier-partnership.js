import React from 'react';

export default function ZapierPartnership() {
  return (
    <div>
      {/* Hero Section */}
      <div className="bg-secondary p-8 rounded-lg mb-8 relative overflow-hidden">
        <div className="relative z-10">
          <h2 className="text-3xl font-bold mb-4">Exclusive Founding Partner Opportunity</h2>
          <p className="text-xl mb-6 max-w-3xl">
            Join NovaFuse API Superstore as a founding partner and connect your automation platform with the enterprise GRC market.
          </p>
          <div className="flex flex-wrap gap-4">
            <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg">
              <div className="text-3xl font-bold text-blue-400">85%</div>
              <div className="text-gray-300">Revenue Share</div>
            </div>
            <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg">
              <div className="text-3xl font-bold text-blue-400">$500K+</div>
              <div className="text-gray-300">Annual Revenue Potential</div>
            </div>
            <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg">
              <div className="text-3xl font-bold text-blue-400">1,000+</div>
              <div className="text-gray-300">Enterprise GRC Customers</div>
            </div>
            <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg">
              <div className="text-3xl font-bold text-blue-400">2 Weeks</div>
              <div className="text-gray-300">Integration Timeline</div>
            </div>
          </div>
        </div>
        <div className="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-l from-blue-500 to-transparent opacity-10"></div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        {/* Left Column */}
        <div>
          <div className="bg-secondary p-6 rounded-lg mb-8">
            <h3 className="text-2xl font-bold mb-4">Why Zapier + NovaFuse?</h3>
            
            <div className="space-y-4">
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-orange-600 flex items-center justify-center text-white font-bold mr-3">1</div>
                <div>
                  <h4 className="text-lg font-semibold">New Revenue Stream</h4>
                  <p className="text-gray-300">Generate new revenue from enterprise GRC customers with our 85% revenue share model—the highest in the industry.</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-orange-600 flex items-center justify-center text-white font-bold mr-3">2</div>
                <div>
                  <h4 className="text-lg font-semibold">Enterprise Expansion</h4>
                  <p className="text-gray-300">Expand your footprint in regulated industries (finance, healthcare, government) through NovaFuse's enterprise relationships.</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-orange-600 flex items-center justify-center text-white font-bold mr-3">3</div>
                <div>
                  <h4 className="text-lg font-semibold">Minimal Integration Effort</h4>
                  <p className="text-gray-300">Our open SDK and webhook-based integration requires minimal development resources—we'll even help build it.</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-orange-600 flex items-center justify-center text-white font-bold mr-3">4</div>
                <div>
                  <h4 className="text-lg font-semibold">Featured Partner Status</h4>
                  <p className="text-gray-300">As a founding partner, you'll receive premium placement in our marketplace and co-marketing opportunities.</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-secondary p-6 rounded-lg">
            <h3 className="text-2xl font-bold mb-4">Integration Use Cases</h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="text-lg font-semibold text-orange-500">Compliance Alert Workflows</h4>
                <p className="text-gray-300">Automatically trigger workflows when compliance issues are detected, notifying stakeholders and creating tasks in project management tools.</p>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold text-orange-500">Audit Evidence Collection</h4>
                <p className="text-gray-300">Streamline audit processes by automatically collecting evidence from various systems and storing it in a central repository.</p>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold text-orange-500">Policy Distribution & Acknowledgment</h4>
                <p className="text-gray-300">Automate the distribution of policies to employees and track acknowledgments through existing communication channels.</p>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold text-orange-500">Risk Assessment Automation</h4>
                <p className="text-gray-300">Trigger risk assessments based on events in other systems and distribute findings to relevant stakeholders.</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Right Column */}
        <div>
          <div className="bg-secondary p-6 rounded-lg mb-8">
            <h3 className="text-2xl font-bold mb-4">Technical Integration</h3>
            
            <p className="text-gray-300 mb-4">
              Integration with NovaFuse is straightforward using our webhook-based API. Here's a simple example of how it works:
            </p>
            
            <div className="bg-gray-900 p-4 rounded-lg text-green-400 mb-6 overflow-x-auto">
              <pre>{`// 1. Register NovaFuse webhook in Zapier
const webhook = {
  url: "https://api.novafuse.io/webhooks",
  events: ["compliance.alert", "risk.assessment"],
  auth: {
    type: "api_key",
    key: "YOUR_API_KEY"
  }
};

// 2. Create a Zap that triggers on NovaFuse events
const zap = {
  trigger: {
    app: "novafuse",
    event: "compliance.alert"
  },
  action: {
    app: "slack",
    event: "send_message",
    params: {
      channel: "#compliance-alerts",
      message: "Compliance alert: {{trigger.data.title}}"
    }
  }
};`}</pre>
            </div>
            
            <p className="text-gray-300">
              Our SDK provides pre-built triggers and actions for common GRC workflows, making it easy to create powerful integrations with minimal code.
            </p>
          </div>
          
          <div className="bg-secondary p-6 rounded-lg">
            <h3 className="text-2xl font-bold mb-4">Revenue Projection</h3>
            
            <div className="overflow-x-auto">
              <table className="w-full text-left">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="py-2">Year</th>
                    <th className="py-2">Customers</th>
                    <th className="py-2">API Calls/Month</th>
                    <th className="py-2">Annual Revenue</th>
                    <th className="py-2">Zapier Share (85%)</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-700">
                    <td className="py-2">Year 1</td>
                    <td className="py-2">50</td>
                    <td className="py-2">100,000</td>
                    <td className="py-2">$60,000</td>
                    <td className="py-2">$51,000</td>
                  </tr>
                  <tr className="border-b border-gray-700">
                    <td className="py-2">Year 2</td>
                    <td className="py-2">200</td>
                    <td className="py-2">500,000</td>
                    <td className="py-2">$300,000</td>
                    <td className="py-2">$255,000</td>
                  </tr>
                  <tr>
                    <td className="py-2">Year 3</td>
                    <td className="py-2">500</td>
                    <td className="py-2">2,000,000</td>
                    <td className="py-2">$1,200,000</td>
                    <td className="py-2">$1,020,000</td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <p className="text-gray-300 mt-4">
              Revenue is based on API call volume with a rate of $0.005 per call. Actual results may vary based on customer adoption and usage patterns.
            </p>
          </div>
        </div>
      </div>
      
      {/* Bottom Section: Next Steps */}
      <div className="bg-secondary p-6 rounded-lg">
        <h3 className="text-2xl font-bold mb-4 text-center">Next Steps</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="p-4 border border-blue-600 rounded-lg">
            <div className="text-3xl font-bold text-center mb-2">1</div>
            <h4 className="text-lg font-semibold text-center mb-2">Initial Discussion</h4>
            <p className="text-gray-300 text-center">30-minute call to discuss partnership details and answer questions.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <div className="text-3xl font-bold text-center mb-2">2</div>
            <h4 className="text-lg font-semibold text-center mb-2">Technical Review</h4>
            <p className="text-gray-300 text-center">Deep dive into the API and integration requirements with your technical team.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <div className="text-3xl font-bold text-center mb-2">3</div>
            <h4 className="text-lg font-semibold text-center mb-2">Partnership Agreement</h4>
            <p className="text-gray-300 text-center">Sign partnership agreement with revenue sharing terms and go-to-market plan.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <div className="text-3xl font-bold text-center mb-2">4</div>
            <h4 className="text-lg font-semibold text-center mb-2">Launch</h4>
            <p className="text-gray-300 text-center">Joint announcement and marketing campaign to promote the integration.</p>
          </div>
        </div>
        
        <div className="mt-8 text-center">
          <button className="bg-orange-600 text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-opacity-90 transition-colors">
            Schedule Partnership Discussion
          </button>
        </div>
      </div>
    </div>
  );
}
