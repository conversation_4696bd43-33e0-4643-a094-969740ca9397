/**
 * Database Connection
 * 
 * This module provides a connection to the MongoDB database.
 */

const mongoose = require('mongoose');
const config = require('../../config');
const logger = require('./logger');

/**
 * Connect to MongoDB
 * @returns {Promise<void>}
 */
async function connect() {
  try {
    await mongoose.connect(config.database.uri, config.database.options);
    logger.info('Connected to MongoDB');
  } catch (error) {
    logger.error('Failed to connect to MongoDB', error);
    throw error;
  }
}

/**
 * Disconnect from MongoDB
 * @returns {Promise<void>}
 */
async function disconnect() {
  try {
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  } catch (error) {
    logger.error('Failed to disconnect from MongoDB', error);
    throw error;
  }
}

/**
 * Get MongoDB connection
 * @returns {mongoose.Connection} - MongoDB connection
 */
function getConnection() {
  return mongoose.connection;
}

module.exports = {
  connect,
  disconnect,
  getConnection
};
