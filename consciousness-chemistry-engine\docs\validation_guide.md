# ConsciousNovaFold Validation Guide

This guide provides an in-depth explanation of the validation process in ConsciousNovaFold, including the scientific rationale behind validation thresholds and how to interpret validation results. It also covers disease-specific validation for proteins like CFTR (Cystic Fibrosis Transmembrane Conductance Regulator).

## Validation Components

### 1. Neural-Emotional Resonance Score (NERS)

- **Purpose**: Measures how well a protein's structure aligns with known stable folds
- **Scale**: 0.0 to 1.0
- **Threshold**: 0.7 (standard), 0.65 (Fibonacci-optimized sequences)
- **Interpretation**:
  - ≥ 0.7: Excellent structural integrity
  - 0.65-0.7: Acceptable with review (Fibonacci sequences)
  - < 0.65: Poor structural integrity


### 2. Disease-Specific Validation (CFTR)

- **Purpose**: Validates CFTR protein sequences against known pathogenic mutations and structural requirements

- **Components**:
  - Mutation analysis (e.g., F508del, G551D)
  - Domain integrity checks
  - Sequence similarity to wild-type

- **Impact**:

  - Identifies CF-causing mutations
  - Validates structural integrity of CFTR domains
  - Provides therapeutic insights for Cystic Fibrosis


### 2. Neural-Emotional Potential Index (NEPI)

- **Purpose**: Evaluates the evolutionary conservation and functional potential
- **Scale**: 0.0 to 1.0
- **Threshold**: 0.5
- **Interpretation**:
  - ≥ 0.7: High functional potential
  - 0.5-0.7: Moderate functional potential
  - < 0.5: Low functional potential

### 3. Nova Ethical Filter Component (NEFC)

- **Purpose**: Identifies potentially harmful or unstable folds
- **Scale**: 0.0 to 1.0
- **Threshold**: 0.6
- **Interpretation**:
  - ≥ 0.6: Ethically acceptable
  - < 0.6: Potentially harmful or unstable

## Edge Case Handling

### Fibonacci-Optimized Sequences
- **Special Consideration**: Sequences following Fibonacci patterns may have unique structural properties
- **Adjustments**:
  - NERS threshold relaxed to 0.65
  - Additional validation steps for structural stability

### High-Risk Sequences
- **Identification**: Sequences matching known harmful patterns (e.g., prion-like)
- **Handling**:
  - Automatic rejection by NEFC
  - Detailed logging of rejection reasons
  - Optional review process for borderline cases

## Running Validation

### Command Line
```bash
# Basic validation
python -m src.conscious_novafold validate --sequence ACEDEF...

# With detailed output
python -m src.conscious_novafold validate --sequence ACEDEF... --verbose

# Validate from FASTA file
python -m src.conscious_novafold validate --fasta input.fasta

# Enable CFTR-specific validation
python -m src.conscious_novafold validate --sequence ACEDEF... --enable-cftr
```

### Programmatic Usage
#### Basic Validation
```python
from src.conscious_novafold import ConsciousNovaFold
from src.novafold_client import NovaFoldClient

# Initialize
novafold = NovaFoldClient()
folder = ConsciousNovaFold(novafold)

# Validate sequence
result = folder.fold("ACDEFGHIKLMNPQRSTVWY")

# Check validation results
if result['metrics']['trinity_report']['scores']['passed']:
    print("Sequence passed all validations")
else:
    print("Validation failed:", result['metrics']['trinity_report']['rejection_reasons'])
```

#### CFTR-Specific Validation
```python
from src.metrics.trinity_validator import TrinityValidator

# Initialize with CFTR validation enabled
validator = TrinityValidator(enable_cftr_validation=True)

# Validate a CFTR sequence
result = validator.validate({
    'sequence': "MQRSPLEKASVVSKLFFSWTRPILRKGYRQRLELSDIYQIPSVDSADNLSEKLEREWDRE...",
    'structure': None  # Optional: Add structure if available
})

# Check CFTR-specific results
if result.get('cftr_validation', {}).get('is_cftr', False):
    print("This is a CFTR protein")
    if not result['cftr_validation']['validation_passed']:
        print("Pathogenic mutations found:")
        for mut_id, status in result['cftr_validation']['mutation_status'].items():
            if status.get('present', False):
                print(f"- {mut_id} at position {status['position']}")
```

## Disease-Specific Validation: CFTR

### CFTR-Specific Validation

#### Common CF-Causing Mutations
1. **F508del**
   - **Prevalence**: ~70% of CF patients
   - **Impact**: Causes misfolding and degradation
   - **Therapies**: Trikafta, Orkambi, Symdeko

2. **G551D**
   - **Prevalence**: ~4% of CF patients
   - **Impact**: Gating mutation - reduces channel open probability
   - **Therapies**: Kalydeco (Ivacaftor)

3. **G542X**
   - **Prevalence**: ~2% of CF patients
   - **Impact**: Nonsense mutation - premature stop codon
   - **Therapies**: Trikafta (elexacaftor/tezacaftor/ivacaftor)

### Domain-Specific Validation
CFTR domains are validated for structural integrity:
- **MSD1/NBD1 Interface**: Critical for stability
- **R Domain**: Phosphorylation sites for regulation
- **NBD2**: ATP binding and hydrolysis
- **MSD2**: Second membrane-spanning domain

## Troubleshooting

### Common Issues

#### Low NERS Score in CFTR
- **Cause**: Common in CF-causing mutations like F508del
- **Solutions**:
  - Check for known CF-causing mutations
  - Verify domain integrity
  - Consider CFTR-specific folding parameters

#### CFTR Validation Failures
- **Cause**: Pathogenic mutations or structural defects
- **Solutions**:
  - Review mutation analysis in results
  - Check domain integrity scores
  - Consult CFTR-specific validation guidelines

#### Low NERS Score (General)
- **Cause**: Unusual structural features or poor folding
- **Solutions**:
  - Review sequence for errors
  - Check for unusual amino acid patterns
  - Consider alternative folding parameters

#### NEFC Rejection
- **Cause**: Sequence matches known harmful patterns
- **Solutions**:
  - Review sequence against known harmful motifs
  - Consider alternative sequences
  - Contact ethics committee for review if needed

### Performance Considerations
- **Sequence Length**: Validation time increases with sequence length
- **Hardware Requirements**: GPU acceleration recommended for sequences > 1000 AA
- **Memory Usage**: Approximately 1GB per 1000 AA

## Best Practices

1. **Pre-validation Checks**
   - Verify sequence format
   - Check for ambiguous residues
   - Validate sequence length

2. **Interpretation**
   - Consider all validation metrics together
   - Review detailed validation reports
   - Consult domain experts for borderline cases

3. **Documentation**
   - Keep records of validation results
   - Document any manual overrides
   - Report any false positives/negatives

## CFTR Validation API Reference

### `CFTRValidator` Class

#### `validate(sequence: str) -> CFTRValidationResult`
Validates a CFTR protein sequence.

**Parameters**:
- `sequence`: Protein sequence to validate

**Returns**:
- `CFTRValidationResult` with validation details

#### `get_mutation_impact(mutation: str) -> Dict`
Gets impact information for a specific CFTR mutation.

**Parameters**:
- `mutation`: Mutation ID (e.g., 'F508del', 'G551D')

**Returns**:
- Dictionary with mutation details and impact information

### Integration with `TrinityValidator`

Enable CFTR validation by setting `enable_cftr_validation=True` when initializing `TrinityValidator`.

## Appendix

### Validation Thresholds
| Component | Threshold | Description |
|-----------|-----------|-------------|
| NERS | 0.7 | Standard structural integrity |
| NERS (Fib) | 0.65 | Fibonacci-optimized sequences |
| NEPI | 0.5 | Functional potential |
| NEFC | 0.6 | Ethical acceptability |
| CFTR Identity | 0.8 | Minimum sequence identity to wild-type CFTR |
| Domain Integrity | 0.9 | Minimum domain integrity score |

### Example Validation Report
```json
{
  "sequence": "ACDEFGHIKLMNPQRSTVWY",
  "metrics": {
    "trinity_report": {
      "scores": {
        "passed": true,
        "ners": 0.85,
        "nepi": 0.72,
        "nefc": 0.81
      },
      "validation": {
        "ners": {
          "score": 0.85,
          "threshold": 0.7,
          "passed": true
        },
        "nepi": {
          "score": 0.72,
          "threshold": 0.5,
          "passed": true
        },
        "nefc": {
          "score": 0.81,
          "threshold": 0.6,
          "passed": true
        }
      }
    }
  }
}
```
