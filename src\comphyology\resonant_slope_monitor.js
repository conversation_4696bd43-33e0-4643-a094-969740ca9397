/**
 * Resonant Slope Monitor
 *
 * This module implements the Resonant Slope (Ψᴿ = dΨₑ/dt) monitoring system,
 * which predicts when a system is likely to fall out of harmonic alignment
 * before it actually happens, enabling preemptive harmonization.
 *
 * Part of the dual-axis operational framework:
 * 1. Harmonic Enforcement (3-6-9-12-13) - What states are allowed
 * 2. Ψₑ Dynamics (Velocity + Acceleration) - How states evolve
 */

const EventEmitter = require('events');

/**
 * Resonant Slope Monitor class
 */
class ResonantSlopeMonitor extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      slopeThreshold: 0.13, // Threshold for slope warning
      criticalSlopeThreshold: 0.3, // Threshold for critical slope warning
      velocityThreshold: 0.6, // Threshold for velocity warning
      accelerationThreshold: 0.09, // Threshold for acceleration warning
      samplingRate: 100, // ms between samples
      historyLength: 10, // Number of samples to keep
      resonancePatternEnforcement: true, // Whether to enforce 3-6-9-12-13 pattern on slopes
      autoModeSwitch: true, // Whether to automatically switch modes based on slope
      ...options
    };

    // Initialize history arrays
    this.psiEHistory = [];
    this.velocityHistory = [];
    this.accelerationHistory = [];
    this.slopeHistory = [];

    // Initialize current values
    this.currentPsiE = 0;
    this.currentVelocity = 0;
    this.currentAcceleration = 0;
    this.currentSlope = 0;

    // Initialize mode
    this.currentMode = "Standard";

    // Initialize metrics
    this.metrics = {
      warnings: 0,
      criticalWarnings: 0,
      modeChanges: 0,
      harmonizationEvents: 0,
      totalSamples: 0,
      averageSlope: 0,
      maxSlope: 0,
      minSlope: 0,
      resonantSlopePercentage: 0
    };

    // Initialize timestamp of last sample
    this.lastSampleTime = null;

    // Initialize automatic sampling if samplingRate > 0
    if (this.options.samplingRate > 0) {
      this.startAutomaticSampling();
    }
  }

  /**
   * Start automatic sampling
   */
  startAutomaticSampling() {
    if (this.samplingInterval) {
      clearInterval(this.samplingInterval);
    }

    this.samplingInterval = setInterval(() => {
      // This is just a placeholder - in a real implementation,
      // we would get the current psiE value from the system
      this.addSample(this.currentPsiE);
    }, this.options.samplingRate);
  }

  /**
   * Stop automatic sampling
   */
  stopAutomaticSampling() {
    if (this.samplingInterval) {
      clearInterval(this.samplingInterval);
      this.samplingInterval = null;
    }
  }

  /**
   * Add a sample to the monitor
   * @param {number} psiE - Current Ψₑ value
   * @returns {Object} - Warning information if slope exceeds threshold
   */
  addSample(psiE) {
    const timestamp = Date.now();
    this.metrics.totalSamples++;

    // Calculate time difference if we have a previous sample
    let timeDiff = 0;
    if (this.lastSampleTime !== null) {
      timeDiff = (timestamp - this.lastSampleTime) / 1000; // Convert to seconds
    }
    this.lastSampleTime = timestamp;

    // Store current psiE
    this.currentPsiE = psiE;
    this.psiEHistory.push({ value: psiE, timestamp });

    // Keep history within limit
    if (this.psiEHistory.length > this.options.historyLength) {
      this.psiEHistory.shift();
    }

    // Calculate velocity, acceleration, and slope if we have enough samples
    if (this.psiEHistory.length >= 2) {
      // Calculate velocity (first derivative of psiE)
      const velocity = this.calculateVelocity();
      this.currentVelocity = velocity;
      this.velocityHistory.push({ value: velocity, timestamp });

      // Keep velocity history within limit
      if (this.velocityHistory.length > this.options.historyLength) {
        this.velocityHistory.shift();
      }

      // Initialize acceleration
      let acceleration = 0;

      // Calculate acceleration (second derivative of psiE) if we have enough velocity samples
      if (this.velocityHistory.length >= 2) {
        acceleration = this.calculateAcceleration();
        this.currentAcceleration = acceleration;
        this.accelerationHistory.push({ value: acceleration, timestamp });

        // Keep acceleration history within limit
        if (this.accelerationHistory.length > this.options.historyLength) {
          this.accelerationHistory.shift();
        }
      }

      // Calculate resonant slope (derivative of velocity and acceleration)
      const slope = this.calculateResonantSlope();
      this.currentSlope = slope;
      this.slopeHistory.push({ value: slope, timestamp });

      // Keep slope history within limit
      if (this.slopeHistory.length > this.options.historyLength) {
        this.slopeHistory.shift();
      }

      // Update metrics
      this.updateMetrics(slope);

      // Check if slope exceeds threshold
      const warning = this.checkWarningThresholds(slope, velocity, acceleration);

      // Emit warning event if needed
      if (warning.warning) {
        this.emit('slope-warning', warning);
      }

      // Switch mode if auto mode switch is enabled
      if (this.options.autoModeSwitch && warning.warning) {
        this.switchMode(warning.recommendedMode);
      }

      return warning;
    }

    return { warning: false };
  }

  /**
   * Calculate velocity (first derivative of psiE)
   * @returns {number} - Velocity
   * @private
   */
  calculateVelocity() {
    // Get the two most recent samples
    const newest = this.psiEHistory[this.psiEHistory.length - 1];
    const previous = this.psiEHistory[this.psiEHistory.length - 2];

    // Calculate time difference in seconds
    const timeDiff = (newest.timestamp - previous.timestamp) / 1000;

    // Calculate psiE difference
    const psiEDiff = newest.value - previous.value;

    // Calculate velocity (first derivative)
    const velocity = psiEDiff / timeDiff;

    // Apply resonance pattern enforcement if enabled
    if (this.options.resonancePatternEnforcement) {
      return this.enforceResonancePattern(velocity);
    }

    return velocity;
  }

  /**
   * Calculate acceleration (second derivative of psiE)
   * @returns {number} - Acceleration
   * @private
   */
  calculateAcceleration() {
    // Get the two most recent velocity samples
    const newest = this.velocityHistory[this.velocityHistory.length - 1];
    const previous = this.velocityHistory[this.velocityHistory.length - 2];

    // Calculate time difference in seconds
    const timeDiff = (newest.timestamp - previous.timestamp) / 1000;

    // Calculate velocity difference
    const velocityDiff = newest.value - previous.value;

    // Calculate acceleration (second derivative)
    const acceleration = velocityDiff / timeDiff;

    // Apply resonance pattern enforcement if enabled
    if (this.options.resonancePatternEnforcement) {
      return this.enforceResonancePattern(acceleration);
    }

    return acceleration;
  }

  /**
   * Calculate resonant slope (Ψᴿ = dΨₑ/dt)
   * @returns {number} - Resonant slope
   * @private
   */
  calculateResonantSlope() {
    // Resonant slope is a weighted combination of velocity and acceleration
    const velocityWeight = 0.6; // Aligned with 3-6-9 pattern (0.6)
    const accelerationWeight = 0.3; // Aligned with 3-6-9 pattern (0.3)

    // Calculate weighted slope
    const slope = (this.currentVelocity * velocityWeight) + (this.currentAcceleration * accelerationWeight);

    // Apply resonance pattern enforcement if enabled
    if (this.options.resonancePatternEnforcement) {
      return this.enforceResonancePattern(slope);
    }

    return slope;
  }

  /**
   * Enforce resonance pattern on a value
   * @param {number} value - Value to enforce pattern on
   * @returns {number} - Resonant value
   * @private
   */
  enforceResonancePattern(value) {
    // Define resonance pattern values
    const resonancePattern = [0.03, 0.06, 0.09, 0.12, 0.3, 0.6, 0.9];

    // Find the closest resonance pattern value
    let closestValue = resonancePattern[0];
    let minDistance = Math.abs(value - closestValue);

    for (let i = 1; i < resonancePattern.length; i++) {
      const distance = Math.abs(value - resonancePattern[i]);
      if (distance < minDistance) {
        minDistance = distance;
        closestValue = resonancePattern[i];
      }
    }

    return closestValue;
  }

  /**
   * Check if values exceed warning thresholds
   * @param {number} slope - Current resonant slope
   * @param {number} velocity - Current velocity
   * @param {number} acceleration - Current acceleration
   * @returns {Object} - Warning information
   * @private
   */
  checkWarningThresholds(slope, velocity, acceleration) {
    // Check if slope exceeds critical threshold
    if (Math.abs(slope) > this.options.criticalSlopeThreshold) {
      this.metrics.criticalWarnings++;
      return {
        warning: true,
        critical: true,
        slope,
        velocity,
        acceleration,
        message: `CRITICAL: Resonant Slope (${slope.toFixed(3)}) exceeds critical threshold (${this.options.criticalSlopeThreshold})`,
        recommendedMode: "Strict Enforcement"
      };
    }

    // Check if slope exceeds warning threshold
    if (Math.abs(slope) > this.options.slopeThreshold) {
      this.metrics.warnings++;
      return {
        warning: true,
        critical: false,
        slope,
        velocity,
        acceleration,
        message: `WARNING: Resonant Slope (${slope.toFixed(3)}) exceeds threshold (${this.options.slopeThreshold})`,
        recommendedMode: "Harmonizing Filter"
      };
    }

    // Check if velocity exceeds threshold
    if (Math.abs(velocity) > this.options.velocityThreshold) {
      this.metrics.warnings++;
      return {
        warning: true,
        critical: false,
        slope,
        velocity,
        acceleration,
        message: `WARNING: Velocity (${velocity.toFixed(3)}) exceeds threshold (${this.options.velocityThreshold})`,
        recommendedMode: "Velocity Control"
      };
    }

    // Check if acceleration exceeds threshold
    if (Math.abs(acceleration) > this.options.accelerationThreshold) {
      this.metrics.warnings++;
      return {
        warning: true,
        critical: false,
        slope,
        velocity,
        acceleration,
        message: `WARNING: Acceleration (${acceleration.toFixed(3)}) exceeds threshold (${this.options.accelerationThreshold})`,
        recommendedMode: "Acceleration Control"
      };
    }

    return { warning: false };
  }

  /**
   * Update metrics with new slope value
   * @param {number} slope - Current resonant slope
   * @private
   */
  updateMetrics(slope) {
    // Update average slope
    const totalSlopes = this.slopeHistory.length;
    const totalSlopeValue = this.slopeHistory.reduce((sum, s) => sum + s.value, 0);
    this.metrics.averageSlope = totalSlopeValue / totalSlopes;

    // Update max and min slope
    this.metrics.maxSlope = Math.max(this.metrics.maxSlope, slope);
    this.metrics.minSlope = Math.min(this.metrics.minSlope, slope);

    // Update resonant slope percentage
    const resonantSlopes = this.slopeHistory.filter(s => {
      const absSlope = Math.abs(s.value);
      return (
        absSlope === 0.03 || absSlope === 0.06 || absSlope === 0.09 ||
        absSlope === 0.12 || absSlope === 0.3 || absSlope === 0.6 ||
        absSlope === 0.9
      );
    }).length;

    this.metrics.resonantSlopePercentage = (resonantSlopes / totalSlopes) * 100;
  }

  /**
   * Switch the current mode
   * @param {string} mode - New mode
   */
  switchMode(mode) {
    if (this.currentMode !== mode) {
      const previousMode = this.currentMode;
      this.currentMode = mode;
      this.metrics.modeChanges++;

      // Emit mode change event
      this.emit('mode-change', {
        previousMode,
        newMode: mode,
        timestamp: Date.now(),
        metrics: this.getMetrics()
      });
    }
  }

  /**
   * Get the current mode
   * @returns {string} - Current mode
   */
  getCurrentMode() {
    return this.currentMode;
  }

  /**
   * Get current metrics
   * @returns {Object} - Current metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      currentPsiE: this.currentPsiE,
      currentVelocity: this.currentVelocity,
      currentAcceleration: this.currentAcceleration,
      currentSlope: this.currentSlope,
      currentMode: this.currentMode
    };
  }

  /**
   * Reset the monitor
   */
  reset() {
    // Clear history arrays
    this.psiEHistory = [];
    this.velocityHistory = [];
    this.accelerationHistory = [];
    this.slopeHistory = [];

    // Reset current values
    this.currentPsiE = 0;
    this.currentVelocity = 0;
    this.currentAcceleration = 0;
    this.currentSlope = 0;

    // Reset mode
    this.currentMode = "Standard";

    // Reset metrics
    this.metrics = {
      warnings: 0,
      criticalWarnings: 0,
      modeChanges: 0,
      harmonizationEvents: 0,
      totalSamples: 0,
      averageSlope: 0,
      maxSlope: 0,
      minSlope: 0,
      resonantSlopePercentage: 0
    };

    // Reset timestamp of last sample
    this.lastSampleTime = null;

    // Emit reset event
    this.emit('reset', {
      timestamp: Date.now()
    });
  }
}

module.exports = ResonantSlopeMonitor;
