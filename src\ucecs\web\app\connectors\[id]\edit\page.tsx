'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import MainLayout from '@/components/MainLayout';
import { FiArrowLeft, FiSave } from 'react-icons/fi';
import { getConnectorById, updateConnector } from '@/services/novaCoreApi';

// Mock connector data (same as in the details page)
const mockConnector = {
  id: 'conn-001',
  name: 'AWS Security Hub',
  description: 'Collects security findings from AWS Security Hub',
  type: 'aws',
  status: 'active',
  config: {
    baseUrl: 'https://securityhub.us-east-1.amazonaws.com',
    apiVersion: '2018-10-26',
  },
  authentication: {
    type: 'aws_iam',
    credentials: {
      accessKey: 'AKIAXXXXXXXXXXXXXXXX',
      secretKey: '****************************************',
      region: 'us-east-1',
    },
  },
  schedule: {
    frequency: 'daily',
    time: '02:00',
    timezone: 'UTC',
    enabled: true,
    lastRun: '2023-10-15T02:00:00Z',
    nextRun: '2023-10-16T02:00:00Z',
  },
  lastCollection: '2023-10-15T02:00:00Z',
  evidenceCount: 128,
  createdAt: '2023-09-01T10:15:30Z',
  createdBy: 'admin',
  updatedAt: '2023-10-01T14:22:45Z',
  updatedBy: 'admin',
};

// Authentication types
const authTypes = [
  { id: 'api_key', name: 'API Key', fields: ['apiKey', 'apiKeyHeader'] },
  { id: 'oauth2', name: 'OAuth 2.0', fields: ['clientId', 'clientSecret', 'authorizationUrl', 'tokenUrl', 'scope'] },
  { id: 'basic', name: 'Basic Auth', fields: ['username', 'password'] },
  { id: 'aws_iam', name: 'AWS IAM', fields: ['accessKey', 'secretKey', 'region'] },
  { id: 'azure_ad', name: 'Azure AD', fields: ['tenantId', 'clientId', 'clientSecret'] },
  { id: 'gcp_service_account', name: 'GCP Service Account', fields: ['projectId', 'privateKey'] },
];

export default function EditConnectorPage() {
  const params = useParams();
  const router = useRouter();
  const [connector, setConnector] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    baseUrl: '',
    apiVersion: '',
    authFields: {} as Record<string, string>,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load connector data
  useEffect(() => {
    const loadConnector = async () => {
      try {
        // In a real implementation, this would fetch the connector from the API
        // const result = await getConnectorById(params.id as string);
        // setConnector(result);
        
        // For now, use mock data
        setConnector(mockConnector);
        
        // Initialize form data
        setFormData({
          name: mockConnector.name,
          description: mockConnector.description || '',
          baseUrl: mockConnector.config.baseUrl,
          apiVersion: mockConnector.config.apiVersion,
          authFields: { ...mockConnector.authentication.credentials },
        });
        
        setLoading(false);
      } catch (error) {
        console.error('Error loading connector:', error);
        setLoading(false);
      }
    };
    
    loadConnector();
  }, [params.id]);

  // Handle form field change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle auth field change
  const handleAuthFieldChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      authFields: {
        ...formData.authFields,
        [field]: value,
      },
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!connector) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Prepare connector data
      const updatedConnector = {
        ...connector,
        name: formData.name,
        description: formData.description,
        config: {
          ...connector.config,
          baseUrl: formData.baseUrl,
          apiVersion: formData.apiVersion,
        },
        authentication: {
          ...connector.authentication,
          credentials: formData.authFields,
        },
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin', // In a real app, this would be the current user
      };
      
      // In a real implementation, this would update the connector via API
      // await updateConnector(connector.id, updatedConnector);
      
      // For now, just log the data and redirect
      console.log('Updating connector:', updatedConnector);
      
      // Redirect to connector details page
      router.push(`/connectors/${connector.id}`);
    } catch (error) {
      console.error('Error updating connector:', error);
      alert('Error updating connector. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get auth type fields
  const getAuthTypeFields = () => {
    if (!connector) return [];
    
    const authType = authTypes.find(at => at.id === connector.authentication.type);
    return authType ? authType.fields : [];
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="card p-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">Loading connector...</p>
        </div>
      </MainLayout>
    );
  }

  if (!connector) {
    return (
      <MainLayout>
        <div className="card p-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">Connector not found</p>
          <Link href="/connectors" className="btn btn-primary mt-4">
            Back to Connectors
          </Link>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="mb-6 flex items-center">
        <Link href={`/connectors/${connector.id}`} className="mr-4">
          <FiArrowLeft className="h-5 w-5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" />
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Edit Connector</h1>
          <p className="text-gray-500 dark:text-gray-400">{connector.name}</p>
        </div>
      </div>

      <div className="card">
        <form onSubmit={handleSubmit}>
          {/* Basic Information */}
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="name" className="label">Connector Name</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  className="input"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div>
                <label htmlFor="description" className="label">Description</label>
                <input
                  type="text"
                  id="description"
                  name="description"
                  className="input"
                  value={formData.description}
                  onChange={handleChange}
                />
              </div>
            </div>
          </div>
          
          {/* Connection Settings */}
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Connection Settings</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="baseUrl" className="label">Base URL</label>
                <input
                  type="text"
                  id="baseUrl"
                  name="baseUrl"
                  className="input"
                  value={formData.baseUrl}
                  onChange={handleChange}
                  required
                />
              </div>
              <div>
                <label htmlFor="apiVersion" className="label">API Version</label>
                <input
                  type="text"
                  id="apiVersion"
                  name="apiVersion"
                  className="input"
                  value={formData.apiVersion}
                  onChange={handleChange}
                />
              </div>
            </div>
          </div>
          
          {/* Authentication */}
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Authentication</h2>
            <div className="mb-4">
              <label htmlFor="authType" className="label">Authentication Type</label>
              <input
                type="text"
                id="authType"
                className="input"
                value={connector.authentication.type.replace('_', ' ')}
                disabled
              />
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Authentication type cannot be changed. Create a new connector if you need to use a different authentication method.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {getAuthTypeFields().map((field) => (
                <div key={field}>
                  <label htmlFor={field} className="label">
                    {field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1')}
                  </label>
                  <input
                    type={field.includes('password') || field.includes('secret') || field.includes('key') ? 'password' : 'text'}
                    id={field}
                    className="input"
                    value={formData.authFields[field] || ''}
                    onChange={(e) => handleAuthFieldChange(field, e.target.value)}
                    required
                  />
                </div>
              ))}
            </div>
          </div>
          
          {/* Actions */}
          <div className="flex justify-between">
            <Link href={`/connectors/${connector.id}`} className="btn btn-outline">
              Cancel
            </Link>
            <button
              type="submit"
              className="btn btn-primary flex items-center"
              disabled={isSubmitting}
            >
              <FiSave className="mr-2" />
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </MainLayout>
  );
}
