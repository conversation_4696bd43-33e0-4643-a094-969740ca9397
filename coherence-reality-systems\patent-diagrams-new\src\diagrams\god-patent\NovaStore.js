import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText,
  InventorLabel
} from '../../components/DiagramComponents';

const NovaStore = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel>NOVASTORE: PARTNER EMPOWERMENT MODEL</ContainerLabel>
      </ContainerBox>
      
      {/* NovaStore Core */}
      <ContainerBox width="700px" height="120px" left="50px" top="70px">
        <ContainerLabel>NOVASTORE MARKETPLACE</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="100px" top="110px" width="150px" height="60px">
        <ComponentNumber>601</ComponentNumber>
        <ComponentLabel>Component Registry</ComponentLabel>
        Verified Modules
      </ComponentBox>
      
      <ComponentBox left="270px" top="110px" width="150px" height="60px">
        <ComponentNumber>602</ComponentNumber>
        <ComponentLabel>Smart Contract Licensing</ComponentLabel>
        Automated Enforcement
      </ComponentBox>
      
      <ComponentBox left="440px" top="110px" width="150px" height="60px">
        <ComponentNumber>603</ComponentNumber>
        <ComponentLabel>Verification Pipeline</ComponentLabel>
        Compliance Testing
      </ComponentBox>
      
      <ComponentBox left="610px" top="110px" width="150px" height="60px">
        <ComponentNumber>604</ComponentNumber>
        <ComponentLabel>API Gateway</ComponentLabel>
        Secure Access
      </ComponentBox>
      
      {/* Revenue Model */}
      <ContainerBox width="700px" height="120px" left="50px" top="210px">
        <ContainerLabel>PARTNER EMPOWERMENT REVENUE MODEL</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="175px" top="250px" width="200px" height="60px">
        <ComponentNumber>605</ComponentNumber>
        <ComponentLabel>Partner Revenue Share</ComponentLabel>
        82%
      </ComponentBox>
      
      <ComponentBox left="425px" top="250px" width="200px" height="60px">
        <ComponentNumber>606</ComponentNumber>
        <ComponentLabel>Platform Revenue Share</ComponentLabel>
        18%
      </ComponentBox>
      
      {/* Integration Components */}
      <ContainerBox width="700px" height="120px" left="50px" top="350px">
        <ContainerLabel>INTEGRATION COMPONENTS</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="100px" top="390px" width="150px" height="60px">
        <ComponentNumber>607</ComponentNumber>
        <ComponentLabel>Royalty Distribution</ComponentLabel>
        Automated Payments
      </ComponentBox>
      
      <ComponentBox left="270px" top="390px" width="150px" height="60px">
        <ComponentNumber>608</ComponentNumber>
        <ComponentLabel>Certification Process</ComponentLabel>
        Quality Assurance
      </ComponentBox>
      
      <ComponentBox left="440px" top="390px" width="150px" height="60px">
        <ComponentNumber>609</ComponentNumber>
        <ComponentLabel>Usage Analytics</ComponentLabel>
        Performance Metrics
      </ComponentBox>
      
      <ComponentBox left="610px" top="390px" width="150px" height="60px">
        <ComponentNumber>610</ComponentNumber>
        <ComponentLabel>Governance Model</ComponentLabel>
        Ecosystem Integrity
      </ComponentBox>
      
      {/* Ecosystem Impact */}
      <ContainerBox width="700px" height="100px" left="50px" top="490px">
        <ContainerLabel>ECOSYSTEM IMPACT</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="175px" top="530px" width="200px" height="40px">
        <ComponentNumber>611</ComponentNumber>
        <ComponentLabel>Economic Incentives</ComponentLabel>
        Security-First Development
      </ComponentBox>
      
      <ComponentBox left="425px" top="530px" width="200px" height="40px">
        <ComponentNumber>612</ComponentNumber>
        <ComponentLabel>Cross-Industry Application</ComponentLabel>
        Regulatory Compliance
      </ComponentBox>
      
      {/* Connecting arrows */}
      <Arrow left="175px" top="170px" width="2px" height="40px" />
      <Arrow left="345px" top="170px" width="2px" height="40px" />
      <Arrow left="515px" top="170px" width="2px" height="40px" />
      <Arrow left="685px" top="170px" width="2px" height="40px" />
      
      <Arrow left="275px" top="310px" width="2px" height="40px" />
      <Arrow left="525px" top="310px" width="2px" height="40px" />
      
      <Arrow left="175px" top="450px" width="2px" height="40px" />
      <Arrow left="345px" top="450px" width="2px" height="40px" />
      <Arrow left="515px" top="450px" width="2px" height="40px" />
      <Arrow left="685px" top="450px" width="2px" height="40px" />
      
      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>NovaStore Components</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>Revenue Model (82/18 Split)</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>Integration Components</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>Ecosystem Impact</LegendText>
        </LegendItem>
      </DiagramLegend>
      
      {/* Inventor Label */}
      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default NovaStore;
