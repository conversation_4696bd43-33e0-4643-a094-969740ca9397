/**
 * GraphQL Resolvers
 * 
 * This file defines the GraphQL resolvers for NovaConnect.
 */

const { GraphQLScalarType } = require('graphql');
const { Kind } = require('graphql/language');
const connectorRegistry = require('../../registry/connector-registry');
const authenticationManager = require('../../auth/authentication-manager');
const connectorExecutor = require('../../executor/connector-executor');
const RBACService = require('../services/RBACService');
const BillingService = require('../services/BillingService');
const MarketplaceService = require('../services/MarketplaceService');
const { PubSub } = require('graphql-subscriptions');

// Initialize services
const rbacService = new RBACService();
const billingService = new BillingService();
const marketplaceService = new MarketplaceService();
const pubsub = new PubSub();

// Define execution completed event
const EXECUTION_COMPLETED = 'EXECUTION_COMPLETED';

// Custom JSON scalar type
const JSONScalar = new GraphQLScalarType({
  name: 'JSON',
  description: 'The `JSON` scalar type represents JSON values as specified by ECMA-404',
  serialize(value) {
    return value;
  },
  parseValue(value) {
    return value;
  },
  parseLiteral(ast) {
    switch (ast.kind) {
      case Kind.STRING:
        return JSON.parse(ast.value);
      case Kind.OBJECT:
        return ast.fields.reduce((obj, field) => {
          obj[field.name.value] = parseLiteral(field.value);
          return obj;
        }, {});
      default:
        return null;
    }
  }
});

// Define resolvers
const resolvers = {
  JSON: JSONScalar,
  
  Query: {
    // Connector queries
    connectors: () => connectorRegistry.getAllConnectors(),
    connector: (_, { id }) => connectorRegistry.getConnector(id),
    connectorsByCategory: (_, { category }) => connectorRegistry.getConnectorsByCategory(category),
    searchConnectors: (_, { query }) => connectorRegistry.searchConnectors(query),
    
    // Credential queries
    credentials: () => authenticationManager.getAllCredentials(),
    credential: (_, { id }) => authenticationManager.getCredentials(id),
    
    // RBAC queries
    roles: () => rbacService.getAllRoles(),
    role: (_, { id }) => rbacService.getRoleById(id),
    permissions: () => rbacService.getAllPermissions(),
    permission: (_, { id }) => rbacService.getPermissionById(id),
    userRoles: (_, { userId }) => rbacService.getUserRoles(userId),
    userPermissions: (_, { userId }) => rbacService.getUserPermissions(userId),
    hasPermission: (_, { userId, permissionId }) => rbacService.hasPermission(userId, permissionId),
    
    // Billing queries
    billingPlans: () => billingService.getBillingPlans(),
    billingPlan: (_, { id }) => billingService.getBillingPlan(id),
    billingCycles: () => billingService.getBillingCycles(),
    billingCycle: (_, { id }) => billingService.getBillingCycle(id),
    calculatePrice: (_, { planId, cycleId }) => billingService.calculatePrice(planId, cycleId),
    
    // Marketplace queries
    marketplacePlans: () => marketplaceService.getMarketplacePlans(),
    marketplacePlan: (_, { id }) => marketplaceService.getMarketplacePlan(id),
    tenantStatus: (_, { id }) => marketplaceService.billingService.getCustomerEntitlements(id)
  },
  
  Mutation: {
    // Connector mutations
    registerConnector: async (_, { input }) => {
      await connectorRegistry.registerConnector(input);
      return input;
    },
    updateConnector: async (_, { id, input }) => {
      await connectorRegistry.updateConnector(id, input);
      return { id, ...input };
    },
    deleteConnector: async (_, { id }) => {
      await connectorRegistry.deleteConnector(id);
      return true;
    },
    
    // Credential mutations
    storeCredentials: (_, { connectorId, credentials }) => {
      const credentialId = authenticationManager.storeCredentials(connectorId, credentials);
      return { id: credentialId, connectorId };
    },
    deleteCredentials: (_, { id }) => {
      const success = authenticationManager.deleteCredentials(id);
      return success;
    },
    testConnection: async (_, { id, connectorId }) => {
      const connector = connectorRegistry.getConnector(connectorId);
      const credentials = authenticationManager.getCredentials(id);
      
      if (!connector) {
        throw new Error(`Connector '${connectorId}' not found`);
      }
      
      if (!credentials) {
        throw new Error(`Credentials '${id}' not found`);
      }
      
      return authenticationManager.testConnection(connector, credentials);
    },
    
    // Execution mutations
    executeEndpoint: async (_, { connectorId, endpointId, credentialId, parameters }) => {
      try {
        const result = await connectorExecutor.executeEndpoint(connectorId, endpointId, credentialId, parameters);
        
        // Publish execution completed event
        pubsub.publish(EXECUTION_COMPLETED, {
          executionCompleted: {
            id: result.id || `exec-${Date.now()}`,
            connectorId,
            endpointId,
            status: result.error ? 'error' : 'success',
            data: result.error ? null : result,
            error: result.error ? result.error.message : null,
            startTime: result.startTime,
            endTime: result.endTime,
            duration: result.duration
          }
        });
        
        return {
          id: result.id || `exec-${Date.now()}`,
          connectorId,
          endpointId,
          status: result.error ? 'error' : 'success',
          data: result.error ? null : result,
          error: result.error ? result.error.message : null,
          startTime: result.startTime,
          endTime: result.endTime,
          duration: result.duration
        };
      } catch (error) {
        return {
          id: `exec-${Date.now()}`,
          connectorId,
          endpointId,
          status: 'error',
          data: null,
          error: error.message,
          startTime: new Date().toISOString(),
          endTime: new Date().toISOString(),
          duration: 0
        };
      }
    },
    
    // RBAC mutations
    createRole: (_, { input }) => rbacService.createRole(input),
    updateRole: (_, { id, input }) => rbacService.updateRole(id, input),
    deleteRole: async (_, { id }) => {
      await rbacService.deleteRole(id);
      return true;
    },
    createPermission: (_, { input }) => rbacService.createPermission(input),
    updatePermission: (_, { id, input }) => rbacService.updatePermission(id, input),
    deletePermission: async (_, { id }) => {
      await rbacService.deletePermission(id);
      return true;
    },
    assignRoleToUser: async (_, { userId, roleId }) => {
      await rbacService.assignRoleToUser(userId, roleId);
      return true;
    },
    removeRoleFromUser: async (_, { userId, roleId }) => {
      await rbacService.removeRoleFromUser(userId, roleId);
      return true;
    },
    
    // Billing mutations
    createSubscription: (_, { tenantId, planId, cycleId }) => 
      billingService.createSubscription(tenantId, planId, cycleId),
    reportUsage: async (_, { subscriptionId, metricId, quantity }) => {
      await billingService.reportUsage(subscriptionId, metricId, quantity);
      return true;
    },
    
    // Marketplace mutations
    provisionTenant: (_, { input }) => marketplaceService.provisionTenant(input),
    updateTenantPlan: (_, { tenantId, planId }) => marketplaceService.updateTenantPlan(tenantId, planId),
    deprovisionTenant: async (_, { tenantId }) => {
      await marketplaceService.deprovisionTenant(tenantId);
      return true;
    }
  },
  
  Subscription: {
    executionCompleted: {
      subscribe: (_, { connectorId, endpointId }) => {
        // Filter by connectorId and endpointId if provided
        return pubsub.asyncIterator(EXECUTION_COMPLETED);
      }
    }
  }
};

module.exports = resolvers;
