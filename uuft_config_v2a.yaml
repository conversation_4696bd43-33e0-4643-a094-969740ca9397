# Refined configuration based on CSDE test results and <PERSON>'s feedback

environment: "docker"  # options: docker | gcp

resource_allocation:
  method: entropy_weighted
  critical_task_weight: 1.10          # Slight increase for better balance
  standard_task_floor: 0.60           # Ensure standard task completion stays reasonable
  feedback_loop_enabled: true         # Allow auto-adjustment during runtime
  optimization_strategy: pareto       # Continue using Pareto optimization

pattern_preservation:
  use_skip_connections: true
  skip_connection_weight: 0.25        # Reduced from 0.75 to prevent overpreservation
  attention_enabled: false            # Disable faulty attention mechanism for now
  contrastive_loss_weight: 0.3        # Lowered for stability, but still maintaining pattern trace

pi10_scaling:
  enabled: true
  base_multiplier: 3141.59
  dynamic_scaling_enabled: true
  variance_threshold: 0.10            # Looser threshold to avoid overreaction
  scaling_factor_adjustment: simple   # Revert to coefficient of variation approach
  domain_specific_calibration: false  # Turn off until future refinement

fusion:
  operator_chain: "(A ⊗ B ⊕ C) × π"
  normalization: "layer_norm"
  domain_encoders:
    cybersecurity: 0.618
    financial: 0.667
    healthcare: 0.6
    physics: 0.577
  feature_bifurcation: false          # Disable complex bifurcation for now

logging:
  log_level: info
  save_outputs: true
  output_dir: "/logs/uuft_test_runs_v2a"
  versioning_enabled: true

hardware:
  use_gpu: true
  parallel_processing: true
  max_threads: 8                      # Reduced thread count for stability testing

metadata:
  experiment_id: "uuft_csde_v2a"
  tester: "Auggie"
  timestamp: "2025-05-10T16:00:00Z"
  notes: "Simplified configuration after regression. Focused on stabilization across all tests."
