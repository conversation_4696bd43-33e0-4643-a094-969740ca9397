"""
Example of using validation chains with the Validator Manager.

This example demonstrates how to create and use validation chains
with the enhanced Validator Manager.
"""

import os
import sys
import json
import logging

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.validator_manager import ValidatorManager, ValidationMode, ValidationLevel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the validation chain example."""
    # Create a validator manager
    validator_manager = ValidatorManager()
    
    # Create a validation chain for API response validation
    chain_id = "api_response_chain"
    chain_name = "API Response Validation Chain"
    chain_description = "Chain for validating API responses"
    
    # Define the validators in the chain
    validators = [
        {
            "id": "api_status",
            "name": "API Status Code Validator",
            "description": "Validates that the API status code is 200"
        },
        {
            "id": "api_response",
            "name": "API Response Validator",
            "description": "Validates that the API response has the expected fields"
        }
    ]
    
    try:
        # Create the validation chain
        chain = validator_manager.create_validation_chain(
            chain_id=chain_id,
            name=chain_name,
            description=chain_description,
            validators=validators,
            mode=ValidationMode.SEQUENTIAL,
            required_score=80.0,
            required_level=ValidationLevel.HIGH
        )
        
        logger.info(f"Created validation chain: {json.dumps(chain.to_dict(), indent=2)}")
        
        # Create a custom validation script
        script_id = "custom_api_validator"
        script_content = """
# Custom API Validator Script
# This script validates that the API response contains specific fields

# Get the evidence data
data = evidence.get('data', {})
response = data.get('response', {})

# Check if the response has the required fields
required_fields = ['id', 'name', 'status', 'created_at']
missing_fields = [field for field in required_fields if field not in response]

# Create the validation result
if not missing_fields:
    result = ValidationResult(
        validator_id='custom_api_validator',
        is_valid=True,
        confidence_level=ValidationLevel.HIGH,
        score=100,
        details={
            'fields_checked': required_fields,
            'all_fields_present': True
        }
    )
else:
    result = ValidationResult(
        validator_id='custom_api_validator',
        is_valid=False,
        confidence_level=ValidationLevel.HIGH,
        score=0,
        details={
            'fields_checked': required_fields,
            'missing_fields': missing_fields
        },
        errors=[f"Missing required fields: {', '.join(missing_fields)}"]
    )
"""
        
        # Create the validation script
        script = validator_manager.create_validation_script(
            script_id=script_id,
            content=script_content,
            name="Custom API Validator",
            description="Validates that the API response contains specific fields",
            author="NovaFuse",
            version="1.0.0",
            confidence_level="HIGH",
            tags=["api", "validation", "custom"]
        )
        
        logger.info(f"Created validation script: {script_id}")
        
        # Update the validation chain to include the custom script
        validators.append({
            "id": script_id,
            "name": "Custom API Validator",
            "description": "Validates that the API response contains specific fields"
        })
        
        updated_chain = validator_manager.update_validation_chain(
            chain_id=chain_id,
            validators=validators
        )
        
        logger.info(f"Updated validation chain: {json.dumps(updated_chain.to_dict(), indent=2)}")
        
        # Create a sample API response evidence
        evidence = {
            'type': 'api',
            'source': 'rest_api',
            'data': {
                'url': 'https://api.example.com/users/123',
                'method': 'GET',
                'status_code': 200,
                'response': {
                    'id': 123,
                    'name': 'John Doe',
                    'email': '<EMAIL>',
                    'status': 'active',
                    'created_at': '2023-01-01T00:00:00Z'
                }
            }
        }
        
        # Validate the evidence using the chain
        result = validator_manager.validate(chain_id, evidence)
        
        logger.info(f"Validation result: {json.dumps(result.to_dict(), indent=2)}")
        
        # Create an invalid API response evidence (missing required field)
        invalid_evidence = {
            'type': 'api',
            'source': 'rest_api',
            'data': {
                'url': 'https://api.example.com/users/456',
                'method': 'GET',
                'status_code': 200,
                'response': {
                    'id': 456,
                    'name': 'Jane Smith',
                    'email': '<EMAIL>'
                    # Missing 'status' and 'created_at' fields
                }
            }
        }
        
        # Validate the invalid evidence using the chain
        invalid_result = validator_manager.validate(chain_id, invalid_evidence)
        
        logger.info(f"Invalid validation result: {json.dumps(invalid_result.to_dict(), indent=2)}")
        
        # Clean up
        validator_manager.delete_validation_chain(chain_id)
        validator_manager.delete_validation_script(script_id)
        
    except Exception as e:
        logger.error(f"Error: {e}")

if __name__ == "__main__":
    main()
