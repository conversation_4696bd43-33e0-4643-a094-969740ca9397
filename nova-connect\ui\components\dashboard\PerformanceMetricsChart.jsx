import React from 'react';
import { Box, Typography, useTheme } from '@mui/material';
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  ComposedChart,
  Line,
  Area
} from 'recharts';

/**
 * Performance Metrics Chart Component
 * 
 * Displays charts for performance metrics across all CSDE features.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.metrics - Performance metrics
 */
const PerformanceMetricsChart = ({ metrics }) => {
  const theme = useTheme();
  
  // If no metrics are available, show a message
  if (!metrics) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%' 
      }}>
        <Typography variant="body2" color="text.secondary">
          No performance metrics available
        </Typography>
      </Box>
    );
  }
  
  // Prepare data for requests chart
  const requestsData = [
    {
      name: 'Offline Processing',
      total: metrics.offlineProcessing.totalRequests,
      successful: metrics.offlineProcessing.successfulRequests,
      failed: metrics.offlineProcessing.failedRequests,
      successRate: metrics.offlineProcessing.totalRequests > 0 
        ? metrics.offlineProcessing.successfulRequests / metrics.offlineProcessing.totalRequests 
        : 1
    },
    {
      name: 'Cross-Domain',
      total: metrics.crossDomainPrediction.totalRequests,
      successful: metrics.crossDomainPrediction.successfulRequests,
      failed: metrics.crossDomainPrediction.failedRequests,
      successRate: metrics.crossDomainPrediction.totalRequests > 0 
        ? metrics.crossDomainPrediction.successfulRequests / metrics.crossDomainPrediction.totalRequests 
        : 1
    },
    {
      name: 'Compliance',
      total: metrics.complianceMapping.totalRequests,
      successful: metrics.complianceMapping.successfulRequests,
      failed: metrics.complianceMapping.failedRequests,
      successRate: metrics.complianceMapping.totalRequests > 0 
        ? metrics.complianceMapping.successfulRequests / metrics.complianceMapping.totalRequests 
        : 1
    }
  ];
  
  // Prepare data for latency chart
  const latencyData = [
    {
      name: 'Offline Processing',
      latency: metrics.offlineProcessing.averageLatency,
      throughput: metrics.offlineProcessing.totalRequests
    },
    {
      name: 'Cross-Domain',
      latency: metrics.crossDomainPrediction.averageLatency,
      throughput: metrics.crossDomainPrediction.totalRequests
    },
    {
      name: 'Compliance',
      latency: metrics.complianceMapping.averageLatency,
      throughput: metrics.complianceMapping.totalRequests
    }
  ];
  
  // Calculate overall metrics
  const totalRequests = 
    metrics.offlineProcessing.totalRequests + 
    metrics.crossDomainPrediction.totalRequests + 
    metrics.complianceMapping.totalRequests;
  
  const successfulRequests = 
    metrics.offlineProcessing.successfulRequests + 
    metrics.crossDomainPrediction.successfulRequests + 
    metrics.complianceMapping.successfulRequests;
  
  const failedRequests = 
    metrics.offlineProcessing.failedRequests + 
    metrics.crossDomainPrediction.failedRequests + 
    metrics.complianceMapping.failedRequests;
  
  const overallSuccessRate = totalRequests > 0 
    ? successfulRequests / totalRequests 
    : 1;
  
  const averageLatency = successfulRequests > 0 
    ? (
        metrics.offlineProcessing.totalLatency + 
        metrics.crossDomainPrediction.totalLatency + 
        metrics.complianceMapping.totalLatency
      ) / successfulRequests 
    : 0;
  
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      {/* Summary Metrics */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-around', 
        mb: 1 
      }}>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Total Requests
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {totalRequests}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Success Rate
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {(overallSuccessRate * 100).toFixed(1)}%
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Avg. Latency
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {averageLatency.toFixed(2)}ms
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Failed Requests
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {failedRequests}
          </Typography>
        </Box>
      </Box>
      
      {/* Charts */}
      <Box sx={{ display: 'flex', flexDirection: 'column', flexGrow: 1 }}>
        {/* Requests Chart */}
        <Box sx={{ height: '50%' }}>
          <Typography variant="caption" color="text.secondary" align="center" sx={{ display: 'block' }}>
            Requests by Feature
          </Typography>
          <ResponsiveContainer width="100%" height="90%">
            <ComposedChart
              data={requestsData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" tick={{ fontSize: 10 }} />
              <YAxis 
                yAxisId="left"
                tick={{ fontSize: 10 }}
              />
              <YAxis 
                yAxisId="right"
                orientation="right"
                domain={[0, 1]}
                tickFormatter={(value) => `${(value * 100).toFixed(0)}%`}
                tick={{ fontSize: 10 }}
              />
              <Tooltip 
                formatter={(value, name) => {
                  if (name === 'successRate') return [`${(value * 100).toFixed(1)}%`, 'Success Rate'];
                  return [value, name];
                }}
              />
              <Legend />
              <Bar 
                yAxisId="left"
                dataKey="successful" 
                name="Successful" 
                stackId="a" 
                fill={theme.palette.success.main} 
              />
              <Bar 
                yAxisId="left"
                dataKey="failed" 
                name="Failed" 
                stackId="a" 
                fill={theme.palette.error.main} 
              />
              <Line 
                yAxisId="right"
                type="monotone" 
                dataKey="successRate" 
                name="Success Rate" 
                stroke={theme.palette.info.main} 
                strokeWidth={2}
                dot={{ r: 5 }}
              />
            </ComposedChart>
          </ResponsiveContainer>
        </Box>
        
        {/* Latency Chart */}
        <Box sx={{ height: '50%' }}>
          <Typography variant="caption" color="text.secondary" align="center" sx={{ display: 'block' }}>
            Latency by Feature
          </Typography>
          <ResponsiveContainer width="100%" height="90%">
            <ComposedChart
              data={latencyData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" tick={{ fontSize: 10 }} />
              <YAxis 
                yAxisId="left"
                label={{ 
                  value: 'Latency (ms)', 
                  angle: -90, 
                  position: 'insideLeft',
                  style: { fontSize: 10 }
                }}
                tick={{ fontSize: 10 }}
              />
              <YAxis 
                yAxisId="right"
                orientation="right"
                label={{ 
                  value: 'Throughput (requests)', 
                  angle: 90, 
                  position: 'insideRight',
                  style: { fontSize: 10 }
                }}
                tick={{ fontSize: 10 }}
              />
              <Tooltip />
              <Legend />
              <Bar 
                yAxisId="left"
                dataKey="latency" 
                name="Avg. Latency (ms)" 
                fill={theme.palette.primary.main} 
              />
              <Area
                yAxisId="right"
                type="monotone"
                dataKey="throughput"
                name="Throughput"
                fill={theme.palette.secondary.light}
                stroke={theme.palette.secondary.main}
                fillOpacity={0.3}
              />
            </ComposedChart>
          </ResponsiveContainer>
        </Box>
      </Box>
    </Box>
  );
};

export default PerformanceMetricsChart;
