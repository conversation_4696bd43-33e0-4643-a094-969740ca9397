/**
 * NovaConnect Mock API Server
 * 
 * This server simulates various API behaviors for testing the Universal API Connector.
 */

const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');

// Create Express app
const app = express();
const port = process.env.PORT || 3005;

// Configure middleware
app.use(cors());
app.use(bodyParser.json());

// Track request history for assertions
const requestHistory = [];

// Clear request history
app.post('/clear-history', (req, res) => {
  requestHistory.length = 0;
  res.status(200).json({ success: true, message: 'Request history cleared' });
});

// Get request history
app.get('/history', (req, res) => {
  res.status(200).json(requestHistory);
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'healthy' });
});

// Record all requests
app.use((req, res, next) => {
  const requestData = {
    method: req.method,
    path: req.path,
    query: req.query,
    headers: req.headers,
    body: req.body,
    timestamp: new Date().toISOString()
  };
  
  requestHistory.push(requestData);
  next();
});

// === API Key Authentication ===
app.get('/api-key/resource', (req, res) => {
  const apiKey = req.headers['x-api-key'];
  
  if (!apiKey) {
    return res.status(401).json({ error: 'API key is required' });
  }
  
  if (apiKey !== 'valid-api-key') {
    return res.status(403).json({ error: 'Invalid API key' });
  }
  
  res.status(200).json({ success: true, data: { id: 'resource-1', name: 'Test Resource' } });
});

// === Basic Authentication ===
app.get('/basic-auth/resource', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Basic ')) {
    return res.status(401).json({ error: 'Basic authentication is required' });
  }
  
  const base64Credentials = authHeader.split(' ')[1];
  const credentials = Buffer.from(base64Credentials, 'base64').toString('utf8');
  const [username, password] = credentials.split(':');
  
  if (username !== 'testuser' || password !== 'testpassword') {
    return res.status(403).json({ error: 'Invalid credentials' });
  }
  
  res.status(200).json({ success: true, data: { id: 'resource-1', name: 'Test Resource' } });
});

// === OAuth2 Authentication ===
app.post('/oauth2/token', (req, res) => {
  const { grant_type, client_id, client_secret } = req.body;
  
  if (grant_type !== 'client_credentials') {
    return res.status(400).json({ error: 'Unsupported grant type' });
  }
  
  if (client_id !== 'test-client-id' || client_secret !== 'test-client-secret') {
    return res.status(403).json({ error: 'Invalid client credentials' });
  }
  
  res.status(200).json({
    access_token: 'test-access-token',
    token_type: 'Bearer',
    expires_in: 3600
  });
});

app.get('/oauth2/resource', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Bearer token is required' });
  }
  
  const token = authHeader.split(' ')[1];
  
  if (token !== 'test-access-token') {
    return res.status(403).json({ error: 'Invalid token' });
  }
  
  res.status(200).json({ success: true, data: { id: 'resource-1', name: 'Test Resource' } });
});

// === JWT Authentication ===
app.get('/jwt/resource', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Bearer token is required' });
  }
  
  const token = authHeader.split(' ')[1];
  
  if (token !== 'test-jwt-token') {
    return res.status(403).json({ error: 'Invalid token' });
  }
  
  res.status(200).json({ success: true, data: { id: 'resource-1', name: 'Test Resource' } });
});

// === AWS SigV4 Authentication ===
app.get('/aws/resource', (req, res) => {
  const amzDate = req.headers['x-amz-date'];
  const securityToken = req.headers['x-amz-security-token'];
  const apiKey = req.headers['x-api-key'];
  
  if (!amzDate || !apiKey) {
    return res.status(401).json({ error: 'AWS SigV4 headers are required' });
  }
  
  // In a real implementation, we would validate the signature
  // For testing, we just check that the headers are present
  
  res.status(200).json({ success: true, data: { id: 'resource-1', name: 'Test Resource' } });
});

// === Custom Authentication ===
app.get('/custom/resource', (req, res) => {
  const customHeader = req.headers['x-custom-auth'];
  
  if (!customHeader) {
    return res.status(401).json({ error: 'Custom authentication header is required' });
  }
  
  if (customHeader !== 'custom-auth-value') {
    return res.status(403).json({ error: 'Invalid custom authentication' });
  }
  
  res.status(200).json({ success: true, data: { id: 'resource-1', name: 'Test Resource' } });
});

// === Error Handling ===
app.get('/error/400', (req, res) => {
  res.status(400).json({ error: 'Bad Request', message: 'Invalid parameters' });
});

app.get('/error/401', (req, res) => {
  res.status(401).json({ error: 'Unauthorized', message: 'Authentication required' });
});

app.get('/error/403', (req, res) => {
  res.status(403).json({ error: 'Forbidden', message: 'Insufficient permissions' });
});

app.get('/error/404', (req, res) => {
  res.status(404).json({ error: 'Not Found', message: 'Resource not found' });
});

app.get('/error/429', (req, res) => {
  res.status(429).json({ error: 'Too Many Requests', message: 'Rate limit exceeded' });
});

app.get('/error/500', (req, res) => {
  res.status(500).json({ error: 'Internal Server Error', message: 'Something went wrong' });
});

app.get('/error/timeout', (req, res) => {
  // Simulate a timeout by not responding
  setTimeout(() => {
    res.status(200).json({ success: true });
  }, 60000);
});

app.get('/error/malformed-json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.status(200).send('{"malformed: json}');
});

// === Data Transformation ===
app.get('/transform/findings', (req, res) => {
  res.status(200).json({
    Findings: [
      {
        Id: 'finding-1',
        Title: 'Security Group allows unrestricted access',
        Description: 'Security group allows unrestricted access to port 22',
        Severity: 'HIGH',
        CreatedAt: '2023-01-01T00:00:00Z',
        UpdatedAt: '2023-01-02T00:00:00Z',
        Status: 'ACTIVE',
        Compliance: {
          Status: 'FAILED',
          RelatedRequirements: ['PCI DSS 1.2.1', 'CIS 4.1']
        },
        Resources: [
          {
            Id: 'sg-12345',
            Type: 'AwsEc2SecurityGroup',
            Region: 'us-east-1'
          }
        ]
      },
      {
        Id: 'finding-2',
        Title: 'S3 bucket allows public access',
        Description: 'S3 bucket allows public read access',
        Severity: 'CRITICAL',
        CreatedAt: '2023-01-03T00:00:00Z',
        UpdatedAt: '2023-01-04T00:00:00Z',
        Status: 'ACTIVE',
        Compliance: {
          Status: 'FAILED',
          RelatedRequirements: ['PCI DSS 1.3.1', 'CIS 3.1']
        },
        Resources: [
          {
            Id: 'bucket-12345',
            Type: 'AwsS3Bucket',
            Region: 'us-east-1'
          }
        ]
      }
    ]
  });
});

// === Parameter Handling ===
app.get('/parameters/query', (req, res) => {
  const { param1, param2 } = req.query;
  
  if (!param1) {
    return res.status(400).json({ error: 'param1 is required' });
  }
  
  res.status(200).json({
    success: true,
    params: {
      param1,
      param2: param2 || 'default'
    }
  });
});

app.get('/parameters/path/:id', (req, res) => {
  const { id } = req.params;
  
  if (!id) {
    return res.status(400).json({ error: 'id is required' });
  }
  
  res.status(200).json({
    success: true,
    params: {
      id
    }
  });
});

app.post('/parameters/body', (req, res) => {
  const { param1, param2 } = req.body;
  
  if (!param1) {
    return res.status(400).json({ error: 'param1 is required' });
  }
  
  res.status(200).json({
    success: true,
    params: {
      param1,
      param2: param2 || 'default'
    }
  });
});

// === Rate Limiting ===
let requestCount = 0;
const rateLimit = 5; // 5 requests per minute
const resetTime = Date.now() + 60000; // Reset after 1 minute

app.get('/rate-limited', (req, res) => {
  requestCount++;
  
  if (requestCount > rateLimit) {
    return res.status(429).json({
      error: 'Rate limit exceeded',
      limit: rateLimit,
      remaining: 0,
      reset: resetTime
    });
  }
  
  res.setHeader('X-RateLimit-Limit', rateLimit);
  res.setHeader('X-RateLimit-Remaining', rateLimit - requestCount);
  res.setHeader('X-RateLimit-Reset', resetTime);
  
  res.status(200).json({
    success: true,
    message: 'Rate limited endpoint',
    count: requestCount
  });
});

// Reset rate limit counter every minute
setInterval(() => {
  requestCount = 0;
}, 60000);

// Start the server
const server = app.listen(port, () => {
  console.log(`Mock API server listening on port ${port}`);
});

// Export for testing
module.exports = { app, server };
