/**
 * NovaFuse Universal API Connector Credential Service
 *
 * This module provides secure credential management with local encryption
 * for development environments and preparation for Google Cloud Secret Manager in production.
 */

// Note: In production, uncomment the following line and install the dependency
// const { SecretManagerServiceClient } = require('@google-cloud/secret-manager');
const crypto = require('crypto');
const { createLogger } = require('../utils/logger');

const logger = createLogger('credential-service');

class CredentialService {
  constructor(options = {}) {
    this.options = {
      useSecretManager: process.env.GCP_SECRET_MANAGER_ENABLED === 'true',
      projectId: process.env.GCP_PROJECT_ID,
      encryptionKey: process.env.ENCRYPTION_KEY || 'development-key-only-for-testing-not-for-prod',
      ...options
    };

    // Initialize in-memory cache for development environments
    this.credentialCache = new Map();

    // Initialize Secret Manager client if enabled
    if (this.options.useSecretManager) {
      try {
        // In production, uncomment the following line and install the dependency
        // this.secretManager = new SecretManagerServiceClient();
        logger.info('Secret Manager would be initialized in production');
        logger.warn('Using local encryption for now (Secret Manager integration requires dependency installation)');
      } catch (error) {
        logger.error('Failed to initialize Secret Manager client', { error });
        throw error;
      }
    } else {
      logger.warn('Secret Manager is disabled. Using local encryption for credentials (NOT RECOMMENDED FOR PRODUCTION)');
    }
  }

  /**
   * Store credentials securely
   *
   * @param {string} connectorId - The ID of the connector
   * @param {Object} credentials - The credentials to store
   * @param {string} ownerId - The ID of the owner
   * @param {string} name - The name of the credential
   * @returns {Promise<string>} - The credential ID
   */
  async storeCredentials(connectorId, credentials, ownerId, name) {
    try {
      // Generate a unique credential ID
      const credentialId = `${connectorId}-${Date.now()}-${crypto.randomBytes(4).toString('hex')}`;

      // Create metadata object
      const metadata = {
        id: credentialId,
        connectorId,
        ownerId,
        name,
        createdAt: new Date().toISOString()
      };

      // Create the credential package
      const credentialPackage = {
        metadata,
        credentials
      };

      if (this.options.useSecretManager) {
        // Store in Secret Manager
        await this._storeInSecretManager(credentialId, credentialPackage);
      } else {
        // Store locally with encryption
        const encryptedData = this._encryptData(JSON.stringify(credentialPackage));
        this.credentialCache.set(credentialId, encryptedData);
      }

      logger.info(`Credentials stored for connector: ${connectorId}`, { credentialId });
      return credentialId;
    } catch (error) {
      logger.error(`Error storing credentials for connector ${connectorId}:`, { error });
      throw error;
    }
  }

  /**
   * Retrieve credentials
   *
   * @param {string} credentialId - The ID of the credentials to retrieve
   * @returns {Promise<Object>} - The credentials
   */
  async getCredentials(credentialId) {
    try {
      logger.debug(`Getting credentials: ${credentialId}`);

      let credentialPackage;

      if (this.options.useSecretManager) {
        // Retrieve from Secret Manager
        credentialPackage = await this._getFromSecretManager(credentialId);
      } else {
        // Retrieve from local cache
        const encryptedData = this.credentialCache.get(credentialId);

        if (!encryptedData) {
          throw new Error(`Credentials not found: ${credentialId}`);
        }

        const decryptedData = this._decryptData(encryptedData);
        credentialPackage = JSON.parse(decryptedData);
      }

      return credentialPackage.credentials;
    } catch (error) {
      logger.error(`Error getting credentials ${credentialId}:`, { error });
      throw error;
    }
  }

  /**
   * Delete credentials
   *
   * @param {string} credentialId - The ID of the credentials to delete
   * @returns {Promise<boolean>} - Whether the deletion was successful
   */
  async deleteCredentials(credentialId) {
    try {
      logger.debug(`Deleting credentials: ${credentialId}`);

      if (this.options.useSecretManager) {
        // Delete from Secret Manager
        await this._deleteFromSecretManager(credentialId);
      } else {
        // Delete from local cache
        this.credentialCache.delete(credentialId);
      }

      logger.info(`Credentials deleted: ${credentialId}`);
      return true;
    } catch (error) {
      logger.error(`Error deleting credentials ${credentialId}:`, { error });
      throw error;
    }
  }

  /**
   * Store credential package in Secret Manager
   *
   * @param {string} credentialId - The credential ID
   * @param {Object} credentialPackage - The credential package to store
   * @returns {Promise<void>}
   * @private
   */
  async _storeInSecretManager(credentialId, credentialPackage) {
    try {
      const secretId = `novafuse-credential-${credentialId}`;
      const secretData = JSON.stringify(credentialPackage);

      // In production, uncomment the following code and install the dependency
      /*
      // Create the secret
      const [secret] = await this.secretManager.createSecret({
        parent: `projects/${this.options.projectId}`,
        secretId,
        secret: {
          replication: {
            automatic: {}
          }
        }
      });

      // Add the secret version
      await this.secretManager.addSecretVersion({
        parent: secret.name,
        payload: {
          data: Buffer.from(secretData, 'utf8')
        }
      });
      */

      // For now, store in local cache
      const encryptedData = this._encryptData(secretData);
      this.credentialCache.set(credentialId, encryptedData);

      logger.debug(`Credentials would be stored in Secret Manager in production: ${secretId}`);
      logger.debug(`Credentials stored in local cache for now: ${credentialId}`);
    } catch (error) {
      logger.error('Error storing credentials:', { error });
      throw error;
    }
  }

  /**
   * Get credential package from Secret Manager
   *
   * @param {string} credentialId - The credential ID
   * @returns {Promise<Object>} - The credential package
   * @private
   */
  async _getFromSecretManager(credentialId) {
    try {
      // In production, this would be used to construct the secret name
      // const secretId = `novafuse-credential-${credentialId}`;

      // In production, uncomment the following code and install the dependency
      /*
      const name = `projects/${this.options.projectId}/secrets/${secretId}/versions/latest`;
      const [version] = await this.secretManager.accessSecretVersion({ name });
      const secretData = version.payload.data.toString('utf8');
      return JSON.parse(secretData);
      */

      // For now, get from local cache
      const encryptedData = this.credentialCache.get(credentialId);

      if (!encryptedData) {
        throw new Error(`Credentials not found: ${credentialId}`);
      }

      const decryptedData = this._decryptData(encryptedData);
      return JSON.parse(decryptedData);
    } catch (error) {
      logger.error('Error getting credentials:', { error });
      throw error;
    }
  }

  /**
   * Delete credential from Secret Manager
   *
   * @param {string} credentialId - The credential ID
   * @returns {Promise<void>}
   * @private
   */
  async _deleteFromSecretManager(credentialId) {
    try {
      const secretId = `novafuse-credential-${credentialId}`;

      // In production, uncomment the following code and install the dependency
      /*
      const name = `projects/${this.options.projectId}/secrets/${secretId}`;
      await this.secretManager.deleteSecret({ name });
      */

      // For now, delete from local cache
      const deleted = this.credentialCache.delete(credentialId);

      if (!deleted) {
        logger.warn(`Credentials not found for deletion: ${credentialId}`);
      }

      logger.debug(`Credentials would be deleted from Secret Manager in production: ${secretId}`);
      logger.debug(`Credentials deleted from local cache: ${credentialId}`);
    } catch (error) {
      logger.error('Error deleting credentials:', { error });
      throw error;
    }
  }

  /**
   * Encrypt data using AES-256-GCM
   *
   * @param {string} data - The data to encrypt
   * @returns {Object} - The encrypted data
   * @private
   */
  _encryptData(data) {
    try {
      const iv = crypto.randomBytes(16);
      const key = Buffer.from(this.options.encryptionKey, 'hex');

      const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);

      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      const authTag = cipher.getAuthTag().toString('hex');

      return {
        iv: iv.toString('hex'),
        encrypted,
        authTag
      };
    } catch (error) {
      logger.error('Error encrypting data:', { error });
      throw error;
    }
  }

  /**
   * Decrypt data using AES-256-GCM
   *
   * @param {Object} encryptedData - The encrypted data
   * @returns {string} - The decrypted data
   * @private
   */
  _decryptData(encryptedData) {
    try {
      const { iv, encrypted, authTag } = encryptedData;

      const key = Buffer.from(this.options.encryptionKey, 'hex');
      const decipher = crypto.createDecipheriv(
        'aes-256-gcm',
        key,
        Buffer.from(iv, 'hex')
      );

      decipher.setAuthTag(Buffer.from(authTag, 'hex'));

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      logger.error('Error decrypting data:', { error });
      throw error;
    }
  }
}

module.exports = new CredentialService();
