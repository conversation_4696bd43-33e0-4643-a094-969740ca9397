/**
 * Test Status Cards Component
 * 
 * This component displays cards with information about test statuses.
 */

import React from 'react';
import { CheckCircle, XCircle, AlertCircle, Clock } from 'lucide-react';

interface TestStatusCardsProps {
  passed: number;
  failed: number;
  pending: number;
  inconclusive: number;
}

export const TestStatusCards: React.FC<TestStatusCardsProps> = ({
  passed,
  failed,
  pending,
  inconclusive,
}) => {
  const cards = [
    {
      title: 'Tests Passed',
      value: passed,
      icon: <CheckCircle className="h-5 w-5 text-green-500" />,
      color: 'bg-green-50 border-green-200',
      textColor: 'text-green-700',
    },
    {
      title: 'Tests Failed',
      value: failed,
      icon: <XCircle className="h-5 w-5 text-red-500" />,
      color: 'bg-red-50 border-red-200',
      textColor: 'text-red-700',
    },
    {
      title: 'Tests Pending',
      value: pending,
      icon: <Clock className="h-5 w-5 text-blue-500" />,
      color: 'bg-blue-50 border-blue-200',
      textColor: 'text-blue-700',
    },
    {
      title: 'Tests Inconclusive',
      value: inconclusive,
      icon: <AlertCircle className="h-5 w-5 text-yellow-500" />,
      color: 'bg-yellow-50 border-yellow-200',
      textColor: 'text-yellow-700',
    },
  ];
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {cards.map((card, index) => (
        <div 
          key={index} 
          className={`p-4 rounded-lg border ${card.color} flex items-center`}
        >
          <div className="mr-4">{card.icon}</div>
          <div>
            <div className={`text-sm font-medium ${card.textColor}`}>{card.title}</div>
            <div className="text-2xl font-bold">{card.value}</div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default TestStatusCards;
