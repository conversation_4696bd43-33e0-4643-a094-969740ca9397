/**
 * Data Breach Service Tests
 *
 * This file contains unit tests for the Data Breach service.
 */

// Mock the models module before importing the service
jest.mock('../../models', () => {
  // Create mock instances
  const mockDataBreach = {
    _id: 'mock-id-1',
    title: 'Test Breach',
    description: 'This is a test breach',
    severity: 'high',
    status: 'active',
    breachDate: new Date('2023-01-01'),
    detectionDate: new Date('2023-01-02'),
    containmentDate: null,
    remediationDate: null,
    resolutionDate: null,
    affectedDataCategories: ['Personal Data', 'Financial Data'],
    affectedDataSubjects: ['Customers'],
    approximateSubjectsCount: 1500,
    supervisoryAuthorityNotified: false,
    dataSubjectsNotified: false,
    save: jest.fn().mockResolvedValue({
      _id: 'mock-id-1',
      title: 'Test Breach',
      severity: 'high'
    }),
    remove: jest.fn().mockResolvedValue(true),
    toJSON: jest.fn().mockReturnValue({ _id: 'mock-id-1' })
  };

  // Create mock constructor
  const MockDataBreach = jest.fn(() => mockDataBreach);

  // Add static methods
  MockDataBreach.find = jest.fn().mockReturnValue({
    sort: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockResolvedValue([mockDataBreach])
  });

  MockDataBreach.findById = jest.fn().mockResolvedValue(mockDataBreach);
  MockDataBreach.countDocuments = jest.fn().mockResolvedValue(1);

  return {
    DataBreach: MockDataBreach
  };
});

// Mock the logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn()
  }
}));

// Import the service and mocked dependencies
const { dataBreachService } = require('../../services');
const { DataBreach } = require('../../models');
const { logger } = require('../../utils/logger');

describe('Data Breach Service', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('getAllDataBreaches', () => {
    it('should return all data breaches with pagination', async () => {
      // Setup options
      const options = {
        page: 1,
        limit: 10,
        filter: { status: 'active' },
        sort: { detectionDate: -1 }
      };

      // Call the service method
      const result = await dataBreachService.getAllDataBreaches(options);

      // Verify the model methods were called correctly
      expect(DataBreach.find).toHaveBeenCalledWith(options.filter);
      expect(DataBreach.countDocuments).toHaveBeenCalledWith(options.filter);

      // Verify the result
      expect(result).toEqual({
        data: expect.any(Array),
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });

      // Verify logging
      expect(logger.error).not.toHaveBeenCalled();
    });

    it('should use default options if not provided', async () => {
      // Call the service method with empty options
      const result = await dataBreachService.getAllDataBreaches({});

      // Verify the model methods were called with default values
      expect(DataBreach.find).toHaveBeenCalledWith({});
      expect(DataBreach.countDocuments).toHaveBeenCalledWith({});

      // Verify the result
      expect(result).toEqual({
        data: expect.any(Array),
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Database error');
      DataBreach.find.mockImplementationOnce(() => {
        throw error;
      });

      // Call the service method and expect it to throw
      await expect(dataBreachService.getAllDataBreaches({}))
        .rejects
        .toThrow('Database error');

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting data breaches'));
    });
  });

  describe('getDataBreachById', () => {
    it('should return a data breach by ID', async () => {
      // Call the service method
      const result = await dataBreachService.getDataBreachById('mock-id-1');

      // Verify the model methods were called correctly
      expect(DataBreach.findById).toHaveBeenCalledWith('mock-id-1');

      // Verify the result
      expect(result).toEqual(expect.objectContaining({
        _id: 'mock-id-1',
        title: 'Test Breach'
      }));

      // Verify logging
      expect(logger.error).not.toHaveBeenCalled();
    });

    it('should throw NotFoundError if the data breach is not found', async () => {
      // Setup model to return null
      DataBreach.findById.mockResolvedValueOnce(null);

      // Call the service method and expect it to throw
      await expect(dataBreachService.getDataBreachById('non-existent-id'))
        .rejects
        .toMatchObject({
          name: 'NotFoundError',
          message: 'Data breach not found'
        });

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting data breach by ID'));
    });

    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Database error');
      DataBreach.findById.mockRejectedValueOnce(error);

      // Call the service method and expect it to throw
      await expect(dataBreachService.getDataBreachById('mock-id-1'))
        .rejects
        .toThrow('Database error');

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting data breach by ID'));
    });
  });

  describe('createDataBreach', () => {
    it('should create a new data breach', async () => {
      // Setup breach data
      const breachData = {
        title: 'Test Breach',
        description: 'This is a test breach',
        severity: 'high',
        breachDate: new Date(),
        detectionDate: new Date()
      };

      // Call the service method
      const result = await dataBreachService.createDataBreach(breachData);

      // Verify the model constructor was called with the correct data
      expect(DataBreach).toHaveBeenCalledWith(breachData);

      // Verify save was called
      expect(DataBreach().save).toHaveBeenCalled();

      // Verify the result
      expect(result).toEqual(expect.objectContaining({
        _id: 'mock-id-1',
        title: 'Test Breach'
      }));

      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Created new data breach'));
      expect(logger.error).not.toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      // Setup breach data
      const breachData = {
        title: 'Test Breach',
        description: 'This is a test breach',
        severity: 'high'
      };

      // Setup error
      const error = new Error('Database error');
      DataBreach().save.mockRejectedValueOnce(error);

      // Call the service method and expect it to throw
      await expect(dataBreachService.createDataBreach(breachData))
        .rejects
        .toThrow('Database error');

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error creating data breach'));
    });
  });

  describe('updateDataBreach', () => {
    it('should update a data breach', async () => {
      // Setup update data
      const updateData = {
        title: 'Updated Breach',
        severity: 'critical'
      };

      // Call the service method
      const result = await dataBreachService.updateDataBreach('mock-id-1', updateData);

      // Verify the model methods were called correctly
      expect(DataBreach.findById).toHaveBeenCalledWith('mock-id-1');

      // Verify save was called
      expect(DataBreach().save).toHaveBeenCalled();

      // Verify the result
      expect(result).toEqual(expect.objectContaining({
        _id: 'mock-id-1'
      }));

      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Updated data breach'));
      expect(logger.error).not.toHaveBeenCalled();
    });

    it('should update status-related dates when status changes to contained', async () => {
      // Setup mock data breach with status
      const mockDataBreach = {
        _id: 'mock-id-1',
        status: 'active',
        supervisoryAuthorityNotified: false,
        dataSubjectsNotified: false,
        save: jest.fn().mockResolvedValue({
          _id: 'mock-id-1',
          status: 'contained',
          containmentDate: expect.any(Date)
        })
      };

      // Override the findById mock for this test
      DataBreach.findById.mockResolvedValueOnce(mockDataBreach);

      // Setup update data with status change
      const updateData = {
        status: 'contained'
      };

      // Call the service method
      const result = await dataBreachService.updateDataBreach('mock-id-1', updateData);

      // Verify the model methods were called correctly
      expect(DataBreach.findById).toHaveBeenCalledWith('mock-id-1');

      // Verify the data breach was updated correctly
      expect(mockDataBreach.status).toBe('contained');
      expect(mockDataBreach.containmentDate).toBeInstanceOf(Date);

      // Verify save was called
      expect(mockDataBreach.save).toHaveBeenCalled();

      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Updated data breach'));
    });

    it('should update status-related dates when status changes to remediated', async () => {
      // Setup mock data breach with status
      const mockDataBreach = {
        _id: 'mock-id-1',
        status: 'contained',
        supervisoryAuthorityNotified: false,
        dataSubjectsNotified: false,
        save: jest.fn().mockResolvedValue({
          _id: 'mock-id-1',
          status: 'remediated',
          remediationDate: expect.any(Date)
        })
      };

      // Override the findById mock for this test
      DataBreach.findById.mockResolvedValueOnce(mockDataBreach);

      // Setup update data with status change
      const updateData = {
        status: 'remediated'
      };

      // Call the service method
      const result = await dataBreachService.updateDataBreach('mock-id-1', updateData);

      // Verify the data breach was updated correctly
      expect(mockDataBreach.status).toBe('remediated');
      expect(mockDataBreach.remediationDate).toBeInstanceOf(Date);

      // Verify save was called
      expect(mockDataBreach.save).toHaveBeenCalled();
    });

    it('should update status-related dates when status changes to resolved', async () => {
      // Setup mock data breach with status
      const mockDataBreach = {
        _id: 'mock-id-1',
        status: 'remediated',
        supervisoryAuthorityNotified: false,
        dataSubjectsNotified: false,
        save: jest.fn().mockResolvedValue({
          _id: 'mock-id-1',
          status: 'resolved',
          resolutionDate: expect.any(Date)
        })
      };

      // Override the findById mock for this test
      DataBreach.findById.mockResolvedValueOnce(mockDataBreach);

      // Setup update data with status change
      const updateData = {
        status: 'resolved'
      };

      // Call the service method
      const result = await dataBreachService.updateDataBreach('mock-id-1', updateData);

      // Verify the data breach was updated correctly
      expect(mockDataBreach.status).toBe('resolved');
      expect(mockDataBreach.resolutionDate).toBeInstanceOf(Date);

      // Verify save was called
      expect(mockDataBreach.save).toHaveBeenCalled();
    });

    it('should update notification-related dates when supervisory authority notification status changes', async () => {
      // Setup mock data breach with notification status
      const mockDataBreach = {
        _id: 'mock-id-1',
        supervisoryAuthorityNotified: false,
        dataSubjectsNotified: false,
        save: jest.fn().mockResolvedValue({
          _id: 'mock-id-1',
          supervisoryAuthorityNotified: true,
          supervisoryAuthorityNotificationDate: expect.any(Date)
        })
      };

      // Override the findById mock for this test
      DataBreach.findById.mockResolvedValueOnce(mockDataBreach);

      // Setup update data with notification status change
      const updateData = {
        supervisoryAuthorityNotified: true
      };

      // Call the service method
      const result = await dataBreachService.updateDataBreach('mock-id-1', updateData);

      // Verify the data breach was updated correctly
      expect(mockDataBreach.supervisoryAuthorityNotified).toBe(true);
      expect(mockDataBreach.supervisoryAuthorityNotificationDate).toBeInstanceOf(Date);

      // Verify save was called
      expect(mockDataBreach.save).toHaveBeenCalled();
    });

    it('should update notification-related dates when data subjects notification status changes', async () => {
      // Setup mock data breach with notification status
      const mockDataBreach = {
        _id: 'mock-id-1',
        supervisoryAuthorityNotified: false,
        dataSubjectsNotified: false,
        save: jest.fn().mockResolvedValue({
          _id: 'mock-id-1',
          dataSubjectsNotified: true,
          dataSubjectNotificationDate: expect.any(Date)
        })
      };

      // Override the findById mock for this test
      DataBreach.findById.mockResolvedValueOnce(mockDataBreach);

      // Setup update data with notification status change
      const updateData = {
        dataSubjectsNotified: true
      };

      // Call the service method
      const result = await dataBreachService.updateDataBreach('mock-id-1', updateData);

      // Verify the data breach was updated correctly
      expect(mockDataBreach.dataSubjectsNotified).toBe(true);
      expect(mockDataBreach.dataSubjectNotificationDate).toBeInstanceOf(Date);

      // Verify save was called
      expect(mockDataBreach.save).toHaveBeenCalled();
    });

    it('should throw NotFoundError if the data breach is not found', async () => {
      // Setup update data
      const updateData = {
        title: 'Updated Breach'
      };

      // Setup model to return null
      DataBreach.findById.mockResolvedValueOnce(null);

      // Call the service method and expect it to throw
      await expect(dataBreachService.updateDataBreach('non-existent-id', updateData))
        .rejects
        .toMatchObject({
          name: 'NotFoundError',
          message: 'Data breach not found'
        });

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting data breach by ID'));
    });

    it('should handle errors', async () => {
      // Setup update data
      const updateData = {
        title: 'Updated Breach'
      };

      // Setup error
      const error = new Error('Database error');
      DataBreach.findById.mockRejectedValueOnce(error);

      // Call the service method and expect it to throw
      await expect(dataBreachService.updateDataBreach('mock-id-1', updateData))
        .rejects
        .toThrow('Database error');

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error updating data breach'));
    });
  });

  describe('deleteDataBreach', () => {
    it('should delete a data breach', async () => {
      // Call the service method
      const result = await dataBreachService.deleteDataBreach('mock-id-1');

      // Verify the model methods were called correctly
      expect(DataBreach.findById).toHaveBeenCalledWith('mock-id-1');

      // Verify remove was called
      expect(DataBreach().remove).toHaveBeenCalled();

      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Deleted data breach'));
      expect(logger.error).not.toHaveBeenCalled();
    });

    it('should throw NotFoundError if the data breach is not found', async () => {
      // Setup model to return null
      DataBreach.findById.mockResolvedValueOnce(null);

      // Call the service method and expect it to throw
      await expect(dataBreachService.deleteDataBreach('non-existent-id'))
        .rejects
        .toMatchObject({
          name: 'NotFoundError',
          message: 'Data breach not found'
        });

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting data breach by ID'));
    });

    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Database error');
      DataBreach.findById.mockRejectedValueOnce(error);

      // Call the service method and expect it to throw
      await expect(dataBreachService.deleteDataBreach('mock-id-1'))
        .rejects
        .toThrow('Database error');

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error deleting data breach'));
    });
  });

  describe('sendBreachNotifications', () => {
    it('should send notifications for a data breach', async () => {
      // Setup notification data
      const notificationData = {
        notificationType: 'supervisory-authority',
        notificationMethod: 'email',
        notificationContent: 'This is a test notification'
      };

      // Setup mock data breach
      const mockDataBreach = {
        _id: 'mock-id-1',
        supervisoryAuthorityNotified: false,
        dataSubjectsNotified: false,
        save: jest.fn().mockResolvedValue({
          _id: 'mock-id-1',
          supervisoryAuthorityNotified: true,
          supervisoryAuthorityNotificationDate: expect.any(Date)
        })
      };

      // Override the findById mock for this test
      DataBreach.findById.mockResolvedValueOnce(mockDataBreach);

      // Call the service method
      const result = await dataBreachService.sendBreachNotifications('mock-id-1', notificationData);

      // Verify the model methods were called correctly
      expect(DataBreach.findById).toHaveBeenCalledWith('mock-id-1');

      // Verify the data breach was updated correctly
      expect(mockDataBreach.supervisoryAuthorityNotified).toBe(true);
      expect(mockDataBreach.supervisoryAuthorityNotificationDate).toBeInstanceOf(Date);

      // Verify save was called
      expect(mockDataBreach.save).toHaveBeenCalled();

      // Verify the result
      expect(result).toEqual(expect.objectContaining({
        id: 'mock-id-1',
        notificationType: 'supervisory-authority',
        notificationMethod: 'email'
      }));

      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Sent supervisory-authority notifications'));
      expect(logger.error).not.toHaveBeenCalled();
    });

    it('should handle data subject notifications', async () => {
      // Setup notification data for data subjects
      const notificationData = {
        notificationType: 'data-subjects',
        notificationMethod: 'email',
        notificationContent: 'This is a test notification'
      };

      // Setup mock data breach
      const mockDataBreach = {
        _id: 'mock-id-1',
        supervisoryAuthorityNotified: false,
        dataSubjectsNotified: false,
        save: jest.fn().mockResolvedValue({
          _id: 'mock-id-1',
          dataSubjectsNotified: true,
          dataSubjectNotificationDate: expect.any(Date),
          dataSubjectNotificationMethod: 'email',
          dataSubjectNotificationContent: 'This is a test notification'
        })
      };

      // Override the findById mock for this test
      DataBreach.findById.mockResolvedValueOnce(mockDataBreach);

      // Call the service method
      const result = await dataBreachService.sendBreachNotifications('mock-id-1', notificationData);

      // Verify the data breach was updated correctly
      expect(mockDataBreach.dataSubjectsNotified).toBe(true);
      expect(mockDataBreach.dataSubjectNotificationDate).toBeInstanceOf(Date);
      expect(mockDataBreach.dataSubjectNotificationMethod).toBe('email');
      expect(mockDataBreach.dataSubjectNotificationContent).toBe('This is a test notification');

      // Verify the result
      expect(result).toEqual(expect.objectContaining({
        id: 'mock-id-1',
        notificationType: 'data-subjects',
        notificationMethod: 'email'
      }));
    });

    it('should throw ValidationError if notification data is incomplete', async () => {
      // Setup incomplete notification data
      const notificationData = {
        notificationType: 'supervisory-authority',
        // Missing notificationMethod and notificationContent
      };

      // Call the service method and expect it to throw
      await expect(dataBreachService.sendBreachNotifications('mock-id-1', notificationData))
        .rejects
        .toMatchObject({
          name: 'ValidationError',
          message: 'Notification type, method, and content are required'
        });

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error sending breach notifications'));
    });

    it('should throw NotFoundError if the data breach is not found', async () => {
      // Setup notification data
      const notificationData = {
        notificationType: 'supervisory-authority',
        notificationMethod: 'email',
        notificationContent: 'This is a test notification'
      };

      // Setup model to return null
      DataBreach.findById.mockResolvedValueOnce(null);

      // Call the service method and expect it to throw
      await expect(dataBreachService.sendBreachNotifications('non-existent-id', notificationData))
        .rejects
        .toMatchObject({
          name: 'NotFoundError',
          message: 'Data breach not found'
        });

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting data breach by ID'));
    });

    it('should handle errors', async () => {
      // Setup notification data
      const notificationData = {
        notificationType: 'supervisory-authority',
        notificationMethod: 'email',
        notificationContent: 'This is a test notification'
      };

      // Setup error
      const error = new Error('Database error');
      DataBreach.findById.mockRejectedValueOnce(error);

      // Call the service method and expect it to throw
      await expect(dataBreachService.sendBreachNotifications('mock-id-1', notificationData))
        .rejects
        .toThrow('Database error');

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error sending breach notifications'));
    });
  });

  describe('generateBreachReport', () => {
    it('should generate a report for a data breach', async () => {
      // Call the service method
      const result = await dataBreachService.generateBreachReport('mock-id-1');

      // Verify the model methods were called correctly
      expect(DataBreach.findById).toHaveBeenCalledWith('mock-id-1');

      // Verify the result
      expect(result).toEqual(expect.objectContaining({
        id: 'mock-id-1',
        timeline: expect.any(Object),
        impact: expect.any(Object),
        response: expect.any(Object),
        notifications: expect.any(Object),
        generatedAt: expect.any(Date)
      }));

      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Generated report'));
      expect(logger.error).not.toHaveBeenCalled();
    });

    it('should throw NotFoundError if the data breach is not found', async () => {
      // Setup model to return null
      DataBreach.findById.mockResolvedValueOnce(null);

      // Call the service method and expect it to throw
      await expect(dataBreachService.generateBreachReport('non-existent-id'))
        .rejects
        .toMatchObject({
          name: 'NotFoundError',
          message: 'Data breach not found'
        });

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting data breach by ID'));
    });

    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Database error');
      DataBreach.findById.mockRejectedValueOnce(error);

      // Call the service method and expect it to throw
      await expect(dataBreachService.generateBreachReport('mock-id-1'))
        .rejects
        .toThrow('Database error');

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error generating breach report'));
    });
  });

  describe('assessNotificationRequirements', () => {
    it('should assess notification requirements for a data breach', async () => {
      // Call the service method
      const result = await dataBreachService.assessNotificationRequirements('mock-id-1');

      // Verify the model methods were called correctly
      expect(DataBreach.findById).toHaveBeenCalledWith('mock-id-1');

      // Verify the result
      expect(result).toEqual(expect.objectContaining({
        id: 'mock-id-1',
        authorityNotification: expect.objectContaining({
          required: expect.any(Boolean),
          threshold: expect.any(String),
          reasoning: expect.any(String)
        }),
        dataSubjectNotification: expect.objectContaining({
          required: expect.any(Boolean),
          reasoning: expect.any(String)
        }),
        factors: expect.objectContaining({
          containsHighRiskData: expect.any(Boolean),
          largeScale: expect.any(Boolean)
        }),
        assessedAt: expect.any(Date)
      }));

      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Assessed notification requirements'));
      expect(logger.error).not.toHaveBeenCalled();
    });

    it('should throw NotFoundError if the data breach is not found', async () => {
      // Setup model to return null
      DataBreach.findById.mockResolvedValueOnce(null);

      // Call the service method and expect it to throw
      await expect(dataBreachService.assessNotificationRequirements('non-existent-id'))
        .rejects
        .toMatchObject({
          name: 'NotFoundError',
          message: 'Data breach not found'
        });

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting data breach by ID'));
    });

    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Database error');
      DataBreach.findById.mockRejectedValueOnce(error);

      // Call the service method and expect it to throw
      await expect(dataBreachService.assessNotificationRequirements('mock-id-1'))
        .rejects
        .toThrow('Database error');

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error assessing notification requirements'));
    });
  });
});
