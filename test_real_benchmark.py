#!/usr/bin/env python3
"""
Test Real AI Benchmark Suite Locally
Tests the π-coherence scheduler and benchmark framework before Docker deployment
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'novapi-benchmark'))

def test_pi_scheduler():
    """Test the π-coherence scheduler"""
    print("🧭 Testing π-Coherence Scheduler...")
    
    try:
        from benchmarks.pi_scheduler import (
            get_next_interval, get_all_intervals, 
            PiCoherenceTimer, pi_delay, get_pi_stats
        )
        
        # Test interval loading
        intervals = get_all_intervals()
        print(f"   ✅ π-Intervals loaded: {len(intervals)} intervals")
        print(f"   🔢 Sequence: {[round(i*1000, 2) for i in intervals]}ms")
        
        # Test interval cycling
        print("   🔄 Testing interval cycling:")
        for i in range(6):
            interval = get_next_interval()
            print(f"      Interval {i+1}: {interval:.5f}s ({interval*1000:.2f}ms)")
        
        # Test timer
        timer = PiCoherenceTimer()
        print("   ⏱️  Testing π-coherence timer...")
        for i in range(3):
            result = timer.apply_delay()
            print(f"      Delay {i+1}: Target={result['target_interval']:.5f}s, "
                  f"Actual={result['actual_delay']:.5f}s")
        
        stats = timer.get_stats()
        print(f"   📊 Timer stats: {stats['total_delays']} delays, "
              f"avg={stats['average_delay']:.5f}s")
        
        return True
        
    except Exception as e:
        print(f"   ❌ π-Scheduler test failed: {e}")
        return False

def test_metrics_logger():
    """Test the metrics logger"""
    print("\n📊 Testing Metrics Logger...")
    
    try:
        from metrics.logger import log_result, calculate_performance_summary, print_performance_report
        
        # Test logging
        test_result = {
            "model": "test_model",
            "control_latency_ms": 45.2,
            "pi_latency_ms": 42.8,
            "gain_percent": 5.3
        }
        
        log_result("test_benchmark", test_result)
        print("   ✅ Result logged successfully")
        
        # Test summary calculation
        summary = calculate_performance_summary()
        print(f"   ✅ Summary calculated: {summary.get('total_benchmark_runs', 0)} runs")
        
        # Test performance report
        print("   📋 Performance report:")
        print_performance_report()
        
        return True
        
    except Exception as e:
        print(f"   ❌ Metrics logger test failed: {e}")
        return False

def test_benchmark_structure():
    """Test benchmark file structure"""
    print("\n📁 Testing Benchmark Structure...")
    
    required_files = [
        'novapi-benchmark/benchmarks/pi_scheduler.py',
        'novapi-benchmark/metrics/logger.py',
        'novapi-benchmark/benchmark_nlp_tokens.py',
        'novapi-benchmark/benchmark_vision.py',
        'novapi-benchmark/requirements.txt',
        'novapi-benchmark/run_benchmark.sh'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - MISSING")
            all_exist = False
    
    return all_exist

def test_dependencies():
    """Test if AI dependencies are available"""
    print("\n🔧 Testing AI Dependencies...")
    
    dependencies = {
        'torch': 'PyTorch',
        'torchvision': 'TorchVision', 
        'transformers': 'HuggingFace Transformers',
        'psutil': 'System monitoring'
    }
    
    available = {}
    for module, name in dependencies.items():
        try:
            __import__(module)
            print(f"   ✅ {name} - Available")
            available[module] = True
        except ImportError:
            print(f"   ❌ {name} - Not installed")
            available[module] = False
    
    return available

def main():
    """Run all tests"""
    print("🧪 Testing Real AI Benchmark Suite")
    print("=" * 60)
    
    # Test structure
    structure_ok = test_benchmark_structure()
    
    # Test π-scheduler
    scheduler_ok = test_pi_scheduler()
    
    # Test metrics logger
    logger_ok = test_metrics_logger()
    
    # Test dependencies
    deps = test_dependencies()
    
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY:")
    print(f"   File Structure: {'✅ PASS' if structure_ok else '❌ FAIL'}")
    print(f"   π-Scheduler: {'✅ PASS' if scheduler_ok else '❌ FAIL'}")
    print(f"   Metrics Logger: {'✅ PASS' if logger_ok else '❌ FAIL'}")
    print(f"   PyTorch: {'✅ Available' if deps.get('torch') else '❌ Missing'}")
    print(f"   Transformers: {'✅ Available' if deps.get('transformers') else '❌ Missing'}")
    
    if all([structure_ok, scheduler_ok, logger_ok]):
        print("\n🎉 BENCHMARK FRAMEWORK: READY")
        
        if deps.get('torch') and deps.get('transformers'):
            print("🚀 REAL AI MODELS: READY FOR TESTING")
            print("\nNext steps:")
            print("   1. Build Docker image: docker build -f Dockerfile.novasentient -t novapi-real .")
            print("   2. Run container: docker run -d --name novapi-real novapi-real")
            print("   3. Monitor logs: docker logs -f novapi-real")
        else:
            print("⚠️  REAL AI MODELS: Dependencies missing")
            print("   Install with: pip install torch torchvision transformers")
            print("   Or run in Docker container (will install automatically)")
    else:
        print("\n❌ BENCHMARK FRAMEWORK: Issues detected")
        print("   Fix the failing tests before proceeding")
    
    return structure_ok and scheduler_ok and logger_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
