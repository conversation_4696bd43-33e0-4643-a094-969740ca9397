/**
 * Run All RBAC Tests
 * 
 * This script runs all RBAC tests: unit, integration, and performance.
 */

const { spawn } = require('child_process');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  
  fg: {
    black: '\x1b[30m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m'
  },
  
  bg: {
    black: '\x1b[40m',
    red: '\x1b[41m',
    green: '\x1b[42m',
    yellow: '\x1b[43m',
    blue: '\x1b[44m',
    magenta: '\x1b[45m',
    cyan: '\x1b[46m',
    white: '\x1b[47m'
  }
};

// Helper function to run a command
const runCommand = (command, args, options = {}) => {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      ...options
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });
    
    child.on('error', (error) => {
      reject(error);
    });
  });
};

// Helper function to print a section header
const printSectionHeader = (title) => {
  console.log('\n');
  console.log(`${colors.fg.cyan}${colors.bright}======================================${colors.reset}`);
  console.log(`${colors.fg.cyan}${colors.bright}  ${title}${colors.reset}`);
  console.log(`${colors.fg.cyan}${colors.bright}======================================${colors.reset}`);
  console.log('\n');
};

// Main function to run all tests
const runAllTests = async () => {
  try {
    const cwd = path.resolve(__dirname, '..');
    
    // Print welcome message
    console.log(`${colors.fg.green}${colors.bright}Starting RBAC Test Suite${colors.reset}`);
    console.log(`${colors.fg.green}Running tests from: ${cwd}${colors.reset}`);
    
    // Run unit tests
    printSectionHeader('Running RBAC Unit Tests');
    await runCommand('node', ['./node_modules/jest/bin/jest.js', 'tests/unit/rbac-service.test.js', '--verbose'], { cwd });
    
    // Run integration tests
    printSectionHeader('Running RBAC Integration Tests');
    await runCommand('node', ['./node_modules/jest/bin/jest.js', 'tests/integration/rbac-api.test.js', '--verbose'], { cwd });
    
    // Run performance tests
    printSectionHeader('Running RBAC Performance Tests');
    await runCommand('node', ['./node_modules/jest/bin/jest.js', 'tests/performance/rbac-performance.test.js', '--verbose'], { cwd });
    
    // Print success message
    console.log(`\n${colors.fg.green}${colors.bright}All RBAC tests completed successfully!${colors.reset}\n`);
  } catch (error) {
    console.error(`\n${colors.fg.red}${colors.bright}Error running tests: ${error.message}${colors.reset}\n`);
    process.exit(1);
  }
};

// Run the tests
runAllTests();
