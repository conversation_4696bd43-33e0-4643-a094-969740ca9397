/**
 * Data Subject Request Controller
 *
 * This controller handles operations related to data subject requests.
 */

const { subjectRequestService } = require('../services');

// Get all data subject requests with pagination and filtering
const getAllSubjectRequests = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    // Build filter object
    const filter = {};

    if (req.query.status) {
      filter.status = req.query.status;
    }

    if (req.query.requestType) {
      filter.requestType = req.query.requestType;
    }

    if (req.query.email) {
      filter['dataSubject.email'] = req.query.email;
    }

    if (req.query.search) {
      filter.$text = { $search: req.query.search };
    }

    // Date range filtering
    if (req.query.startDate || req.query.endDate) {
      filter.createdAt = {};

      if (req.query.startDate) {
        filter.createdAt.$gte = new Date(req.query.startDate);
      }

      if (req.query.endDate) {
        filter.createdAt.$lte = new Date(req.query.endDate);
      }
    }

    // Build sort object
    const sort = {};

    if (req.query.sortBy) {
      sort[req.query.sortBy] = req.query.sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort by creation date descending
    }

    // Use the service to get the requests
    const result = await subjectRequestService.getAllRequests({
      page,
      limit,
      filter,
      sort
    });

    res.json(result);
  } catch (error) {
    next(error);
  }
};

// Get a specific data subject request by ID
const getSubjectRequestById = async (req, res, next) => {
  try {
    const request = await subjectRequestService.getRequestById(req.params.id);

    res.json({
      data: request
    });
  } catch (error) {
    next(error);
  }
};

// Create a new data subject request
const createSubjectRequest = async (req, res, next) => {
  try {
    const request = await subjectRequestService.createRequest(req.body);

    res.status(201).json({
      data: request,
      message: 'Data subject request created successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Update a data subject request
const updateSubjectRequest = async (req, res, next) => {
  try {
    const request = await subjectRequestService.updateRequest(req.params.id, req.body);

    res.json({
      data: request,
      message: 'Data subject request updated successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Process a data subject request
const processSubjectRequest = async (req, res, next) => {
  try {
    const request = await subjectRequestService.processRequest(req.params.id);

    res.json({
      data: request,
      message: 'Data subject request processing initiated'
    });
  } catch (error) {
    next(error);
  }
};

// Generate a data export for a data subject request
const generateDataExport = async (req, res, next) => {
  try {
    const exportData = await subjectRequestService.generateDataExport(req.params.id);

    res.json({
      data: exportData,
      message: 'Data export generated successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Identify affected systems for a data subject request
const identifyAffectedSystems = async (req, res, next) => {
  try {
    const result = await subjectRequestService.identifyAffectedSystems(req.params.id);

    res.json({
      data: result,
      message: 'Affected systems identified successfully'
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllSubjectRequests,
  getSubjectRequestById,
  createSubjectRequest,
  updateSubjectRequest,
  processSubjectRequest,
  generateDataExport,
  identifyAffectedSystems
};
