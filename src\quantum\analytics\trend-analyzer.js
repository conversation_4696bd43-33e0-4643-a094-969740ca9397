/**
 * Trend Analyzer
 *
 * This module provides trend analysis capabilities for the Finite Universe
 * Principle defense system, enabling identification of patterns and trends.
 */

const EventEmitter = require('events');

/**
 * TrendAnalyzer class
 * 
 * Provides trend analysis capabilities for identifying patterns and trends.
 */
class TrendAnalyzer extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      historyLength: options.historyLength || 1000, // Number of data points to keep in history
      analysisInterval: options.analysisInterval || 60000, // 1 minute
      trendThreshold: options.trendThreshold || 0.1, // 10% change
      correlationThreshold: options.correlationThreshold || 0.7, // 0.7 correlation coefficient
      seasonalityPeriods: options.seasonalityPeriods || [24, 168, 720], // hours, days, weeks
      ...options
    };

    // Initialize metrics history
    this.metricsHistory = [];
    
    // Initialize trend analysis results
    this.trendAnalysis = {
      boundaryViolations: {
        trend: 0,
        seasonality: false,
        forecast: []
      },
      validationFailures: {
        trend: 0,
        seasonality: false,
        forecast: []
      },
      domainViolations: {
        cyber: {
          trend: 0,
          seasonality: false,
          forecast: []
        },
        financial: {
          trend: 0,
          seasonality: false,
          forecast: []
        },
        medical: {
          trend: 0,
          seasonality: false,
          forecast: []
        }
      }
    };
    
    // Initialize correlation analysis results
    this.correlationAnalysis = {
      boundaryViolations: {},
      validationFailures: {},
      domainViolations: {
        cyber: {},
        financial: {},
        medical: {}
      }
    };
    
    // Initialize analysis interval
    this.analysisInterval = null;

    if (this.options.enableLogging) {
      console.log('TrendAnalyzer initialized with options:', this.options);
    }
  }

  /**
   * Start trend analysis
   */
  start() {
    if (this.analysisInterval) {
      return;
    }

    // Start analysis interval
    this.analysisInterval = setInterval(() => {
      this.analyzeMetrics();
    }, this.options.analysisInterval);

    if (this.options.enableLogging) {
      console.log('TrendAnalyzer started');
    }
    
    // Emit start event
    this.emit('start');
  }

  /**
   * Stop trend analysis
   */
  stop() {
    if (!this.analysisInterval) {
      return;
    }

    // Clear analysis interval
    clearInterval(this.analysisInterval);
    this.analysisInterval = null;

    if (this.options.enableLogging) {
      console.log('TrendAnalyzer stopped');
    }
    
    // Emit stop event
    this.emit('stop');
  }

  /**
   * Process metrics for trend analysis
   * @param {Object} metrics - Metrics to process
   * @returns {Object} - Trend analysis results
   */
  processMetrics(metrics) {
    // Add metrics to history
    this._addToHistory(metrics);
    
    // Analyze metrics if enough history
    if (this.metricsHistory.length >= 10) {
      this.analyzeMetrics();
    }
    
    return {
      trendAnalysis: this.trendAnalysis,
      correlationAnalysis: this.correlationAnalysis
    };
  }

  /**
   * Add metrics to history
   * @param {Object} metrics - Metrics to add
   * @private
   */
  _addToHistory(metrics) {
    // Add metrics to history
    this.metricsHistory.push({
      timestamp: new Date(),
      metrics: { ...metrics }
    });
    
    // Trim history if needed
    if (this.metricsHistory.length > this.options.historyLength) {
      this.metricsHistory.shift();
    }
  }

  /**
   * Analyze metrics for trends and correlations
   */
  analyzeMetrics() {
    if (this.metricsHistory.length < 10) {
      if (this.options.enableLogging) {
        console.log('Not enough history for trend analysis');
      }
      return;
    }
    
    // Analyze trends
    this._analyzeTrends();
    
    // Analyze correlations
    this._analyzeCorrelations();
    
    // Analyze seasonality
    this._analyzeSeasonality();
    
    // Generate forecasts
    this._generateForecasts();
    
    // Emit analysis event
    this.emit('analysis', {
      trendAnalysis: this.trendAnalysis,
      correlationAnalysis: this.correlationAnalysis
    });
    
    if (this.options.enableLogging) {
      console.log('Metrics analyzed');
    }
  }

  /**
   * Analyze trends in metrics
   * @private
   */
  _analyzeTrends() {
    // Extract time series data
    const boundaryViolations = this.metricsHistory.map(entry => entry.metrics.boundaryViolations || 0);
    const validationFailures = this.metricsHistory.map(entry => entry.metrics.validationFailures || 0);
    
    const cyberViolations = this.metricsHistory.map(entry => 
      entry.metrics.domainMetrics?.cyber?.boundaryViolations || 0
    );
    
    const financialViolations = this.metricsHistory.map(entry => 
      entry.metrics.domainMetrics?.financial?.boundaryViolations || 0
    );
    
    const medicalViolations = this.metricsHistory.map(entry => 
      entry.metrics.domainMetrics?.medical?.boundaryViolations || 0
    );
    
    // Calculate trends
    this.trendAnalysis.boundaryViolations.trend = this._calculateTrend(boundaryViolations);
    this.trendAnalysis.validationFailures.trend = this._calculateTrend(validationFailures);
    this.trendAnalysis.domainViolations.cyber.trend = this._calculateTrend(cyberViolations);
    this.trendAnalysis.domainViolations.financial.trend = this._calculateTrend(financialViolations);
    this.trendAnalysis.domainViolations.medical.trend = this._calculateTrend(medicalViolations);
    
    // Check for significant trends
    if (Math.abs(this.trendAnalysis.boundaryViolations.trend) > this.options.trendThreshold) {
      this.emit('significant-trend', {
        metric: 'boundaryViolations',
        trend: this.trendAnalysis.boundaryViolations.trend
      });
    }
    
    if (Math.abs(this.trendAnalysis.validationFailures.trend) > this.options.trendThreshold) {
      this.emit('significant-trend', {
        metric: 'validationFailures',
        trend: this.trendAnalysis.validationFailures.trend
      });
    }
    
    for (const domain of ['cyber', 'financial', 'medical']) {
      if (Math.abs(this.trendAnalysis.domainViolations[domain].trend) > this.options.trendThreshold) {
        this.emit('significant-trend', {
          metric: `${domain}Violations`,
          trend: this.trendAnalysis.domainViolations[domain].trend
        });
      }
    }
  }

  /**
   * Calculate trend in time series data
   * @param {Array} data - Time series data
   * @returns {number} - Trend value (-1 to 1)
   * @private
   */
  _calculateTrend(data) {
    if (data.length < 2) {
      return 0;
    }
    
    // Use simple linear regression to calculate trend
    const n = data.length;
    const indices = Array.from({ length: n }, (_, i) => i);
    
    // Calculate means
    const meanX = indices.reduce((sum, x) => sum + x, 0) / n;
    const meanY = data.reduce((sum, y) => sum + y, 0) / n;
    
    // Calculate slope
    let numerator = 0;
    let denominator = 0;
    
    for (let i = 0; i < n; i++) {
      numerator += (indices[i] - meanX) * (data[i] - meanY);
      denominator += Math.pow(indices[i] - meanX, 2);
    }
    
    if (denominator === 0) {
      return 0;
    }
    
    const slope = numerator / denominator;
    
    // Normalize slope to range -1 to 1
    const maxAbsValue = Math.max(...data.map(y => Math.abs(y)));
    if (maxAbsValue === 0) {
      return 0;
    }
    
    // Return normalized trend
    return slope * (n / maxAbsValue);
  }

  /**
   * Analyze correlations between metrics
   * @private
   */
  _analyzeCorrelations() {
    // Extract time series data
    const boundaryViolations = this.metricsHistory.map(entry => entry.metrics.boundaryViolations || 0);
    const validationFailures = this.metricsHistory.map(entry => entry.metrics.validationFailures || 0);
    
    const cyberViolations = this.metricsHistory.map(entry => 
      entry.metrics.domainMetrics?.cyber?.boundaryViolations || 0
    );
    
    const financialViolations = this.metricsHistory.map(entry => 
      entry.metrics.domainMetrics?.financial?.boundaryViolations || 0
    );
    
    const medicalViolations = this.metricsHistory.map(entry => 
      entry.metrics.domainMetrics?.medical?.boundaryViolations || 0
    );
    
    // Calculate correlations
    this.correlationAnalysis.boundaryViolations.validationFailures = 
      this._calculateCorrelation(boundaryViolations, validationFailures);
    
    this.correlationAnalysis.boundaryViolations.cyberViolations = 
      this._calculateCorrelation(boundaryViolations, cyberViolations);
    
    this.correlationAnalysis.boundaryViolations.financialViolations = 
      this._calculateCorrelation(boundaryViolations, financialViolations);
    
    this.correlationAnalysis.boundaryViolations.medicalViolations = 
      this._calculateCorrelation(boundaryViolations, medicalViolations);
    
    this.correlationAnalysis.validationFailures.cyberViolations = 
      this._calculateCorrelation(validationFailures, cyberViolations);
    
    this.correlationAnalysis.validationFailures.financialViolations = 
      this._calculateCorrelation(validationFailures, financialViolations);
    
    this.correlationAnalysis.validationFailures.medicalViolations = 
      this._calculateCorrelation(validationFailures, medicalViolations);
    
    this.correlationAnalysis.domainViolations.cyber.financialViolations = 
      this._calculateCorrelation(cyberViolations, financialViolations);
    
    this.correlationAnalysis.domainViolations.cyber.medicalViolations = 
      this._calculateCorrelation(cyberViolations, medicalViolations);
    
    this.correlationAnalysis.domainViolations.financial.medicalViolations = 
      this._calculateCorrelation(financialViolations, medicalViolations);
    
    // Check for significant correlations
    for (const metric1 in this.correlationAnalysis) {
      for (const metric2 in this.correlationAnalysis[metric1]) {
        const correlation = this.correlationAnalysis[metric1][metric2];
        
        if (Math.abs(correlation) > this.options.correlationThreshold) {
          this.emit('significant-correlation', {
            metric1,
            metric2,
            correlation
          });
        }
      }
    }
  }

  /**
   * Calculate correlation between two time series
   * @param {Array} data1 - First time series
   * @param {Array} data2 - Second time series
   * @returns {number} - Correlation coefficient (-1 to 1)
   * @private
   */
  _calculateCorrelation(data1, data2) {
    if (data1.length !== data2.length || data1.length < 2) {
      return 0;
    }
    
    const n = data1.length;
    
    // Calculate means
    const mean1 = data1.reduce((sum, x) => sum + x, 0) / n;
    const mean2 = data2.reduce((sum, x) => sum + x, 0) / n;
    
    // Calculate correlation coefficient
    let numerator = 0;
    let denominator1 = 0;
    let denominator2 = 0;
    
    for (let i = 0; i < n; i++) {
      numerator += (data1[i] - mean1) * (data2[i] - mean2);
      denominator1 += Math.pow(data1[i] - mean1, 2);
      denominator2 += Math.pow(data2[i] - mean2, 2);
    }
    
    if (denominator1 === 0 || denominator2 === 0) {
      return 0;
    }
    
    return numerator / Math.sqrt(denominator1 * denominator2);
  }

  /**
   * Analyze seasonality in metrics
   * @private
   */
  _analyzeSeasonality() {
    // Implement seasonality detection
    // This is a simplified implementation
    
    // For each metric, check for repeating patterns
    const metrics = [
      { name: 'boundaryViolations', data: this.metricsHistory.map(entry => entry.metrics.boundaryViolations || 0) },
      { name: 'validationFailures', data: this.metricsHistory.map(entry => entry.metrics.validationFailures || 0) },
      { name: 'cyberViolations', data: this.metricsHistory.map(entry => entry.metrics.domainMetrics?.cyber?.boundaryViolations || 0) },
      { name: 'financialViolations', data: this.metricsHistory.map(entry => entry.metrics.domainMetrics?.financial?.boundaryViolations || 0) },
      { name: 'medicalViolations', data: this.metricsHistory.map(entry => entry.metrics.domainMetrics?.medical?.boundaryViolations || 0) }
    ];
    
    for (const metric of metrics) {
      let seasonalityDetected = false;
      
      // Check for seasonality at different periods
      for (const period of this.options.seasonalityPeriods) {
        if (metric.data.length >= period * 2) {
          const seasonality = this._detectSeasonality(metric.data, period);
          
          if (seasonality) {
            seasonalityDetected = true;
            
            this.emit('seasonality-detected', {
              metric: metric.name,
              period
            });
            
            break;
          }
        }
      }
      
      // Update seasonality flag
      switch (metric.name) {
        case 'boundaryViolations':
          this.trendAnalysis.boundaryViolations.seasonality = seasonalityDetected;
          break;
        case 'validationFailures':
          this.trendAnalysis.validationFailures.seasonality = seasonalityDetected;
          break;
        case 'cyberViolations':
          this.trendAnalysis.domainViolations.cyber.seasonality = seasonalityDetected;
          break;
        case 'financialViolations':
          this.trendAnalysis.domainViolations.financial.seasonality = seasonalityDetected;
          break;
        case 'medicalViolations':
          this.trendAnalysis.domainViolations.medical.seasonality = seasonalityDetected;
          break;
      }
    }
  }

  /**
   * Detect seasonality in time series data
   * @param {Array} data - Time series data
   * @param {number} period - Seasonality period
   * @returns {boolean} - True if seasonality detected, false otherwise
   * @private
   */
  _detectSeasonality(data, period) {
    if (data.length < period * 2) {
      return false;
    }
    
    // Calculate autocorrelation at lag = period
    const autocorrelation = this._calculateAutocorrelation(data, period);
    
    // Check if autocorrelation is significant
    return autocorrelation > this.options.correlationThreshold;
  }

  /**
   * Calculate autocorrelation at specified lag
   * @param {Array} data - Time series data
   * @param {number} lag - Lag
   * @returns {number} - Autocorrelation coefficient (-1 to 1)
   * @private
   */
  _calculateAutocorrelation(data, lag) {
    if (data.length <= lag) {
      return 0;
    }
    
    const n = data.length - lag;
    
    // Calculate mean
    const mean = data.reduce((sum, x) => sum + x, 0) / data.length;
    
    // Calculate autocorrelation
    let numerator = 0;
    let denominator = 0;
    
    for (let i = 0; i < n; i++) {
      numerator += (data[i] - mean) * (data[i + lag] - mean);
      denominator += Math.pow(data[i] - mean, 2);
    }
    
    if (denominator === 0) {
      return 0;
    }
    
    return numerator / denominator;
  }

  /**
   * Generate forecasts for metrics
   * @private
   */
  _generateForecasts() {
    // Generate forecasts for each metric
    this.trendAnalysis.boundaryViolations.forecast = this._generateForecast(
      this.metricsHistory.map(entry => entry.metrics.boundaryViolations || 0),
      this.trendAnalysis.boundaryViolations.seasonality
    );
    
    this.trendAnalysis.validationFailures.forecast = this._generateForecast(
      this.metricsHistory.map(entry => entry.metrics.validationFailures || 0),
      this.trendAnalysis.validationFailures.seasonality
    );
    
    this.trendAnalysis.domainViolations.cyber.forecast = this._generateForecast(
      this.metricsHistory.map(entry => entry.metrics.domainMetrics?.cyber?.boundaryViolations || 0),
      this.trendAnalysis.domainViolations.cyber.seasonality
    );
    
    this.trendAnalysis.domainViolations.financial.forecast = this._generateForecast(
      this.metricsHistory.map(entry => entry.metrics.domainMetrics?.financial?.boundaryViolations || 0),
      this.trendAnalysis.domainViolations.financial.seasonality
    );
    
    this.trendAnalysis.domainViolations.medical.forecast = this._generateForecast(
      this.metricsHistory.map(entry => entry.metrics.domainMetrics?.medical?.boundaryViolations || 0),
      this.trendAnalysis.domainViolations.medical.seasonality
    );
  }

  /**
   * Generate forecast for time series data
   * @param {Array} data - Time series data
   * @param {boolean} seasonality - Whether seasonality is present
   * @returns {Array} - Forecast values
   * @private
   */
  _generateForecast(data, seasonality) {
    if (data.length < 10) {
      return [];
    }
    
    // Use simple exponential smoothing for non-seasonal data
    // and Holt-Winters for seasonal data
    if (seasonality) {
      return this._holtwintersForecast(data);
    } else {
      return this._exponentialSmoothingForecast(data);
    }
  }

  /**
   * Generate forecast using exponential smoothing
   * @param {Array} data - Time series data
   * @returns {Array} - Forecast values
   * @private
   */
  _exponentialSmoothingForecast(data, alpha = 0.3) {
    const n = data.length;
    const forecastHorizon = 10;
    
    // Initialize with last value
    let level = data[n - 1];
    let trend = data[n - 1] - data[n - 2];
    
    // Generate forecasts
    const forecasts = [];
    for (let i = 1; i <= forecastHorizon; i++) {
      forecasts.push(Math.max(0, Math.round(level + i * trend)));
    }
    
    return forecasts;
  }

  /**
   * Generate forecast using Holt-Winters method
   * @param {Array} data - Time series data
   * @returns {Array} - Forecast values
   * @private
   */
  _holtwintersForecast(data) {
    // Simplified implementation
    return this._exponentialSmoothingForecast(data, 0.5);
  }

  /**
   * Get trend analysis results
   * @returns {Object} - Trend analysis results
   */
  getTrendAnalysis() {
    return { ...this.trendAnalysis };
  }

  /**
   * Get correlation analysis results
   * @returns {Object} - Correlation analysis results
   */
  getCorrelationAnalysis() {
    return { ...this.correlationAnalysis };
  }

  /**
   * Dispose resources
   */
  dispose() {
    this.stop();
    
    // Clear history
    this.metricsHistory = [];
    
    if (this.options.enableLogging) {
      console.log('TrendAnalyzer disposed');
    }
  }
}

/**
 * Create a trend analyzer with recommended settings
 * @param {Object} options - Configuration options
 * @returns {TrendAnalyzer} - Configured trend analyzer
 */
function createTrendAnalyzer(options = {}) {
  return new TrendAnalyzer({
    enableLogging: true,
    historyLength: 1000,
    analysisInterval: 60000,
    trendThreshold: 0.1,
    correlationThreshold: 0.7,
    ...options
  });
}

module.exports = {
  TrendAnalyzer,
  createTrendAnalyzer
};
