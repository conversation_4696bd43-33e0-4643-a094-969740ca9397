/**
 * Data Breach Controller
 *
 * This controller handles operations related to data breaches.
 */

const { dataBreachService } = require('../services');

// Get all data breaches with pagination and filtering
const getAllDataBreaches = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    // Build filter object
    const filter = {};

    if (req.query.status) {
      filter.status = req.query.status;
    }

    if (req.query.severity) {
      filter.severity = req.query.severity;
    }

    if (req.query.search) {
      filter.$text = { $search: req.query.search };
    }

    // Date range filtering
    if (req.query.startDate || req.query.endDate) {
      filter.detectionDate = {};

      if (req.query.startDate) {
        filter.detectionDate.$gte = new Date(req.query.startDate);
      }

      if (req.query.endDate) {
        filter.detectionDate.$lte = new Date(req.query.endDate);
      }
    }

    // Build sort object
    const sort = {};

    if (req.query.sortBy) {
      sort[req.query.sortBy] = req.query.sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.detectionDate = -1; // Default sort by detection date descending
    }

    // Use the service to get the data breaches
    const result = await dataBreachService.getAllDataBreaches({
      page,
      limit,
      filter,
      sort
    });

    res.json(result);
  } catch (error) {
    next(error);
  }
};

// Get a specific data breach by ID
const getDataBreachById = async (req, res, next) => {
  try {
    const dataBreach = await dataBreachService.getDataBreachById(req.params.id);

    res.json({
      data: dataBreach
    });
  } catch (error) {
    next(error);
  }
};

// Create a new data breach
const createDataBreach = async (req, res, next) => {
  try {
    const dataBreach = await dataBreachService.createDataBreach(req.body);

    res.status(201).json({
      data: dataBreach,
      message: 'Data breach created successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Update a data breach
const updateDataBreach = async (req, res, next) => {
  try {
    const dataBreach = await dataBreachService.updateDataBreach(req.params.id, req.body);

    res.json({
      data: dataBreach,
      message: 'Data breach updated successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Delete a data breach
const deleteDataBreach = async (req, res, next) => {
  try {
    await dataBreachService.deleteDataBreach(req.params.id);

    res.json({
      message: 'Data breach deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Send notifications for a data breach
const sendBreachNotifications = async (req, res, next) => {
  try {
    const { notificationType, notificationMethod, notificationContent } = req.body;

    if (!notificationType || !notificationMethod || !notificationContent) {
      const error = new Error('Notification type, method, and content are required');
      error.name = 'ValidationError';
      throw error;
    }

    const result = await dataBreachService.sendBreachNotifications(req.params.id, {
      notificationType,
      notificationMethod,
      notificationContent
    });

    res.json({
      data: result,
      message: `${notificationType === 'supervisory-authority' ? 'Supervisory authority' : 'Data subjects'} notified successfully`
    });
  } catch (error) {
    next(error);
  }
};

// Generate a breach notification report
const generateBreachReport = async (req, res, next) => {
  try {
    const report = await dataBreachService.generateBreachReport(req.params.id);

    res.json({
      data: report,
      message: 'Breach report generated successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Assess notification requirements for a data breach
const assessNotificationRequirements = async (req, res, next) => {
  try {
    const assessment = await dataBreachService.assessNotificationRequirements(req.params.id);

    res.json({
      data: assessment,
      message: 'Notification requirements assessed successfully'
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllDataBreaches,
  getDataBreachById,
  createDataBreach,
  updateDataBreach,
  deleteDataBreach,
  sendBreachNotifications,
  generateBreachReport,
  assessNotificationRequirements
};
