import { useState, useEffect } from "react";
import Head from "next/head";

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState("overview");
  const [loading, setLoading] = useState(true);
  const [apiUsage, setApiUsage] = useState([]);
  const [connectors, setConnectors] = useState([]);

  useEffect(() => {
    // In a real implementation, this would fetch from an API
    setTimeout(() => {
      // Mock API usage data
      const usageData = [
        { date: "2025-01-01", calls: 1250, success: 1200, failed: 50 },
        { date: "2025-01-02", calls: 1340, success: 1290, failed: 50 },
        { date: "2025-01-03", calls: 1100, success: 1050, failed: 50 },
        { date: "2025-01-04", calls: 1420, success: 1370, failed: 50 },
        { date: "2025-01-05", calls: 1550, success: 1500, failed: 50 },
        { date: "2025-01-06", calls: 1700, success: 1650, failed: 50 },
        { date: "2025-01-07", calls: 1850, success: 1800, failed: 50 },
      ];
      
      setApiUsage(usageData);
      
      // Mock connectors data
      setConnectors([
        {
          id: "conn-001",
          name: "Google Cloud Security",
          status: "active",
          calls: 12500,
          lastUsed: "2025-01-07"
        },
        {
          id: "conn-002",
          name: "Microsoft Defender for Cloud",
          status: "active",
          calls: 8700,
          lastUsed: "2025-01-05"
        },
        {
          id: "conn-003",
          name: "AWS Security Hub",
          status: "active",
          calls: 9200,
          lastUsed: "2025-01-06"
        }
      ]);
      
      setLoading(false);
    }, 1000);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>NovaFuse Dashboard</title>
        <meta name="description" content="NovaFuse Dashboard - Monitor your API usage" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <div className="flex space-x-4">
            <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
              Create Connector
            </button>
            <button className="border border-blue-600 text-blue-600 px-4 py-2 rounded hover:bg-blue-50">
              View Documentation
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="flex -mb-px">
            <button
              onClick={() => setActiveTab("overview")}
              className={`py-4 px-6 font-medium text-sm ${
                activeTab === "overview"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab("connectors")}
              className={`py-4 px-6 font-medium text-sm ${
                activeTab === "connectors"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Connectors
            </button>
            <button
              onClick={() => setActiveTab("analytics")}
              className={`py-4 px-6 font-medium text-sm ${
                activeTab === "analytics"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Analytics
            </button>
            <button
              onClick={() => setActiveTab("settings")}
              className={`py-4 px-6 font-medium text-sm ${
                activeTab === "settings"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Settings
            </button>
          </nav>
        </div>

        {/* Overview Tab */}
        {activeTab === "overview" && (
          <div>
            <h2 className="text-2xl font-bold mb-6">Overview</h2>
            
            {loading ? (
              <p>Loading dashboard data...</p>
            ) : (
              <>
                {/* API Usage Summary */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold mb-2">Total API Calls</h3>
                    <p className="text-3xl font-bold text-blue-600">
                      {apiUsage.reduce((total, day) => total + day.calls, 0).toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-500 mt-2">Last 7 days</p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold mb-2">Success Rate</h3>
                    <p className="text-3xl font-bold text-green-600">
                      {(apiUsage.reduce((total, day) => total + day.success, 0) / 
                        apiUsage.reduce((total, day) => total + day.calls, 0) * 100).toFixed(1)}%
                    </p>
                    <p className="text-sm text-gray-500 mt-2">Last 7 days</p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold mb-2">Active Connectors</h3>
                    <p className="text-3xl font-bold text-blue-600">{connectors.length}</p>
                    <p className="text-sm text-gray-500 mt-2">Currently active</p>
                  </div>
                </div>
                
                {/* Recent Activity */}
                <div className="bg-white p-6 rounded-lg shadow mb-8">
                  <h3 className="text-lg font-semibold mb-4">Recent API Usage</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead>
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">API Calls</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Success</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Failed</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {apiUsage.map((day, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{day.date}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{day.calls.toLocaleString()}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{day.success.toLocaleString()}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{day.failed.toLocaleString()}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </>
            )}
          </div>
        )}

        {/* Connectors Tab */}
        {activeTab === "connectors" && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Your Connectors</h2>
              <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Add New Connector
              </button>
            </div>
            
            {loading ? (
              <p>Loading connectors...</p>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {connectors.map((connector) => (
                  <div key={connector.id} className="bg-white border rounded-lg shadow overflow-hidden">
                    <div className="p-6">
                      <div className="flex justify-between items-start mb-4">
                        <h3 className="text-lg font-semibold">{connector.name}</h3>
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          connector.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {connector.status}
                        </span>
                      </div>
                      <div className="mb-4">
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-gray-500">API Calls:</span>
                          <span className="font-medium">{connector.calls.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Last Used:</span>
                          <span className="font-medium">{connector.lastUsed}</span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button className="flex-1 bg-blue-600 text-white px-3 py-1.5 rounded text-sm hover:bg-blue-700">
                          Manage
                        </button>
                        <button className="flex-1 border border-blue-600 text-blue-600 px-3 py-1.5 rounded text-sm hover:bg-blue-50">
                          View Docs
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
                
                {/* Add New Connector Card */}
                <div className="bg-white border border-dashed rounded-lg shadow-sm overflow-hidden">
                  <div className="p-6 flex flex-col items-center justify-center h-full">
                    <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold mb-2">Add New Connector</h3>
                    <p className="text-gray-500 text-sm text-center mb-4">
                      Connect to a new API or service
                    </p>
                    <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                      Get Started
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === "analytics" && (
          <div>
            <h2 className="text-2xl font-bold mb-6">Analytics</h2>
            <p className="text-gray-500">Detailed analytics coming soon...</p>
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === "settings" && (
          <div>
            <h2 className="text-2xl font-bold mb-6">Settings</h2>
            <p className="text-gray-500">Settings coming soon...</p>
          </div>
        )}
      </main>
    </div>
  );
}
