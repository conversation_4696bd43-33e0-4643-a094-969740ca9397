#!/usr/bin/env python3
"""
UUFT Comprehensive Test Runner

This script runs all 20 UUFT test files in sequence.

Usage:
    python run_all_uuft_tests.py
"""

import os
import sys
import importlib
import subprocess
import time
import json
from datetime import datetime

def run_test_file(test_file):
    """Run a single UUFT test file"""
    print("\n" + "="*80)
    print(f"Running {test_file}")
    print("="*80)

    # Get the module name (without .py extension)
    module_name = os.path.splitext(test_file)[0]

    try:
        # Import the module
        module = importlib.import_module(module_name)

        # Look for main function or other entry points
        if hasattr(module, 'main'):
            module.main()
        elif hasattr(module, 'run_test'):
            module.run_test()
        else:
            # If no entry point is found, execute the module directly
            print(f"No explicit entry point found in {test_file}. Running module directly.")

            # Find all functions that might be test functions
            test_functions = [
                name for name in dir(module)
                if callable(getattr(module, name))
                and name.startswith(('test_', 'analyze_', 'extract_', 'compare_', 'predict_', 'validate_'))
            ]

            if test_functions:
                print(f"Found potential test functions: {', '.join(test_functions)}")
                for func_name in test_functions:
                    print(f"\nExecuting {func_name}:")
                    try:
                        result = getattr(module, func_name)()
                        if result:
                            print(f"Result: {result}")
                    except Exception as e:
                        print(f"Error executing {func_name}: {e}")
            else:
                print("No test functions found. The module may contain data or helper functions only.")

        return True
    except Exception as e:
        print(f"Error running {test_file}: {e}")
        return False

def uncomment_test_code(test_file):
    """Uncomment test code in the file if needed"""
    with open(test_file, 'r') as f:
        content = f.read()

    # Check if there are commented test execution lines
    if '# print(' in content or '# formalized_' in content:
        print(f"Found commented test code in {test_file}. Uncommenting for execution...")

        # Create a temporary file with uncommented code
        temp_file = f"{test_file}.temp"

        # Uncomment specific patterns
        content = content.replace('# print(', 'print(')
        content = content.replace('# formalized_', 'formalized_')

        # Save to temporary file
        with open(temp_file, 'w') as f:
            f.write(content)

        return temp_file

    return None

def cleanup_temp_file(temp_file):
    """Clean up temporary file if it exists"""
    if temp_file and os.path.exists(temp_file):
        os.remove(temp_file)

def run_unit_tests():
    """Run the UUFT unit tests"""
    print("\n" + "="*80)
    print("Running UUFT Unit Tests")
    print("="*80)

    try:
        # Run the unit tests
        result = subprocess.run(["python", "-m", "unittest", "test_uuft.py"], capture_output=True, text=True)

        # Print the output
        print(result.stdout)

        if result.stderr:
            print("Errors:")
            print(result.stderr)

        # Return success status
        return result.returncode == 0
    except Exception as e:
        print(f"Error running unit tests: {e}")
        return False

def main():
    """Main function to run all tests"""
    print("="*80)
    print("UUFT Comprehensive Test Runner")
    print("="*80)
    print(f"Started at: {datetime.now().isoformat()}")

    # Get all UUFT test files
    test_files = sorted([f for f in os.listdir('.') if f.startswith('UUFT_test_') and f.endswith('.py')])

    if not test_files:
        print("No UUFT test files found.")
        return False

    print(f"Found {len(test_files)} UUFT test files:")
    for i, file in enumerate(test_files, 1):
        print(f"{i}. {file}")

    # Start time
    start_time = time.time()

    # Results
    results = {}

    # First run the unit tests
    unit_tests_passed = run_unit_tests()
    results["test_uuft.py"] = {
        "success": unit_tests_passed,
        "execution_time": 0.0  # We don't measure this separately
    }

    # Run each test file
    for test_file in test_files:
        file_start_time = time.time()

        # Uncomment test code if needed
        temp_file = uncomment_test_code(test_file)

        # Run the test
        if temp_file:
            success = run_test_file(temp_file)
            cleanup_temp_file(temp_file)
        else:
            success = run_test_file(test_file)

        file_end_time = time.time()
        execution_time = file_end_time - file_start_time

        # Store result
        results[test_file] = {
            "success": success,
            "execution_time": execution_time
        }

    # End time
    end_time = time.time()
    total_execution_time = end_time - start_time

    # Print summary
    print("\n" + "="*80)
    print("UUFT Test Summary")
    print("="*80)

    successful_tests = sum(1 for result in results.values() if result["success"])
    print(f"Tests Run: {len(results)}")
    print(f"Successful: {successful_tests}")
    print(f"Failed: {len(results) - successful_tests}")
    print(f"Total Execution Time: {total_execution_time:.2f} seconds")

    # Print detailed results
    print("\nDetailed Results:")
    for test_file, result in results.items():
        status = "PASSED" if result["success"] else "FAILED"
        print(f"{test_file}: {status} ({result['execution_time']:.2f}s)")

    # Save results to file
    results_file = "uuft_test_results.json"
    with open(results_file, 'w') as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "tests_run": len(results),
            "successful_tests": successful_tests,
            "failed_tests": len(results) - successful_tests,
            "total_execution_time": total_execution_time,
            "detailed_results": {k: v for k, v in results.items()}
        }, f, indent=2)

    print(f"\nResults saved to {results_file}")

    # Return success if all tests passed
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
