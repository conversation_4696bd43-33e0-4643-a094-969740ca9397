/**
 * NovaUI Home Page
 * 
 * This is the main entry point for the NovaUI application.
 */

import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { ProductProvider, PRODUCTS } from '../packages/feature-flags/ProductContext';

/**
 * Home Page
 * @returns {React.ReactNode} - The rendered component
 */
export default function Home() {
  const router = useRouter();
  
  // Redirect to dashboard
  useEffect(() => {
    router.push('/dashboard');
  }, [router]);
  
  return (
    <ProductProvider initialProduct={PRODUCTS.NOVA_PRIME}>
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-800">NovaFuse</h1>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    </ProductProvider>
  );
}
