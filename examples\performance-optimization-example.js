/**
 * Performance Optimization Example
 *
 * This example demonstrates how to use the performance optimization components
 * for the Finite Universe Principle defense system, including caching, batching,
 * and throttling.
 */

const {
  // Complete defense system
  createCompleteDefenseSystem,
  
  // Performance optimization components
  createCacheManager,
  createPerformanceOptimizer,
  createPerformanceComponents
} = require('../src/quantum/finite-universe-principle');

/**
 * Example 1: Basic Caching
 * 
 * This example demonstrates how to use the cache manager.
 */
async function example1() {
  console.log('\n=== Example 1: Basic Caching ===\n');

  // Create cache manager
  const cacheManager = createCacheManager({
    enableLogging: true,
    maxCacheSize: 100,
    ttl: 60 * 1000 // 1 minute
  });

  // Register event listener for cache events
  cacheManager.on('cache-set', (data) => {
    console.log('Cache set:', data.key);
  });

  cacheManager.on('cache-hit', (data) => {
    console.log('Cache hit:', data.key);
  });

  // Generate cache key
  const key1 = cacheManager.generateKey({ id: 1, name: 'test' }, 'cyber');
  console.log('Generated key:', key1);

  // Set item in cache
  cacheManager.set(key1, { result: 'test result' });

  // Get item from cache
  const cachedItem = cacheManager.get(key1);
  console.log('Cached item:', cachedItem);

  // Get cache statistics
  console.log('Cache statistics:', cacheManager.getStats());

  // Dispose cache manager
  cacheManager.dispose();
}

/**
 * Example 2: Performance Optimizer
 * 
 * This example demonstrates how to use the performance optimizer.
 */
async function example2() {
  console.log('\n=== Example 2: Performance Optimizer ===\n');

  // Create performance optimizer
  const optimizer = createPerformanceOptimizer({
    enableLogging: true,
    enableCaching: true,
    enableBatching: true,
    enableThrottling: true,
    batchSize: 5,
    batchInterval: 100,
    throttleLimit: 10,
    throttleWindow: 1000
  });

  // Register event listeners
  optimizer.on('cache-hit', (data) => {
    console.log('Cache hit:', data.domain);
  });

  optimizer.on('cache-miss', (data) => {
    console.log('Cache miss:', data.domain);
  });

  optimizer.on('batch-execute', (data) => {
    console.log('Batch executed:', data.size);
  });

  optimizer.on('throttled', (data) => {
    console.log('Throttled:', data.domain);
  });

  // Define process function
  const processData = async (data, domain) => {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 50));
    
    return {
      processed: true,
      data,
      domain,
      timestamp: new Date().toISOString()
    };
  };

  // Process data with caching
  console.log('Processing data with caching...');
  
  const data1 = { id: 1, value: 'test' };
  const result1 = await optimizer.process(processData, data1, 'cyber');
  console.log('Result 1:', result1);
  
  // Process same data again (should be cached)
  const result2 = await optimizer.process(processData, data1, 'cyber');
  console.log('Result 2 (cached):', result2);
  
  // Process different data
  const data2 = { id: 2, value: 'test2' };
  const result3 = await optimizer.process(processData, data2, 'cyber');
  console.log('Result 3:', result3);

  // Process data with batching
  console.log('\nProcessing data with batching...');
  
  // Process multiple items (should be batched)
  const promises = [];
  for (let i = 0; i < 10; i++) {
    promises.push(optimizer.process(processData, { id: i + 10, value: `batch${i}` }, 'financial'));
  }
  
  const batchResults = await Promise.all(promises);
  console.log('Batch results count:', batchResults.length);

  // Process data with throttling
  console.log('\nProcessing data with throttling...');
  
  // Process many items quickly (should trigger throttling)
  const throttlePromises = [];
  for (let i = 0; i < 20; i++) {
    throttlePromises.push(optimizer.process(processData, { id: i + 100, value: `throttle${i}` }, 'medical'));
  }
  
  const throttleResults = await Promise.all(throttlePromises);
  console.log('Throttle results count:', throttleResults.length);

  // Get performance statistics
  console.log('\nPerformance statistics:', optimizer.getStats());

  // Dispose optimizer
  optimizer.dispose();
}

/**
 * Example 3: Integrated Performance Optimization
 * 
 * This example demonstrates how to use performance optimization with the defense system.
 */
async function example3() {
  console.log('\n=== Example 3: Integrated Performance Optimization ===\n');

  // Create complete defense system
  const defenseSystem = createCompleteDefenseSystem({
    enableLogging: false, // Disable logging for cleaner output
    enableMonitoring: true,
    strictMode: true
  });

  // Create performance components
  const performanceComponents = createPerformanceComponents({
    cacheManagerOptions: {
      enableLogging: true,
      maxCacheSize: 1000,
      ttl: 60 * 1000
    },
    performanceOptimizerOptions: {
      enableLogging: true,
      enableCaching: true,
      enableBatching: true,
      enableThrottling: true,
      batchSize: 10,
      batchInterval: 100,
      throttleLimit: 100,
      throttleWindow: 1000
    }
  });

  // Extract components
  const { cacheManager, performanceOptimizer } = performanceComponents;

  // Register event listeners
  performanceOptimizer.on('cache-hit', (data) => {
    console.log('Cache hit:', data.domain);
  });

  performanceOptimizer.on('batch-execute', (data) => {
    console.log('Batch executed:', data.size);
  });

  // Define process function that uses the defense system
  const processWithDefenseSystem = async (data, domain) => {
    return await defenseSystem.processData(data, domain);
  };

  // Process data through the defense system with performance optimization
  console.log('Processing data through the defense system with performance optimization...');
  
  // Process data with caching
  const data1 = {
    securityScore: 8,
    threatLevel: 3,
    encryptionStrength: 256
  };
  
  const result1 = await performanceOptimizer.process(processWithDefenseSystem, data1, 'cyber');
  console.log('Result 1:', result1);
  
  // Process same data again (should be cached)
  const result2 = await performanceOptimizer.process(processWithDefenseSystem, data1, 'cyber');
  console.log('Result 2 (cached):', result2);
  
  // Process data with batching
  console.log('\nProcessing data with batching...');
  
  // Process multiple items (should be batched)
  const promises = [];
  for (let i = 0; i < 5; i++) {
    promises.push(performanceOptimizer.process(
      processWithDefenseSystem,
      {
        balance: 1000 + i * 100,
        interestRate: 0.05,
        transactionVolume: 10000 + i * 1000
      },
      'financial'
    ));
  }
  
  const batchResults = await Promise.all(promises);
  console.log('Batch results count:', batchResults.length);

  // Get performance statistics
  console.log('\nPerformance statistics:', performanceOptimizer.getStats());
  
  // Get cache statistics
  console.log('\nCache statistics:', cacheManager.getStats());

  // Dispose resources
  performanceOptimizer.dispose();
  defenseSystem.dispose();
}

/**
 * Run all examples
 */
async function runAllExamples() {
  await example1();
  await example2();
  await example3();
}

// Run all examples
runAllExamples().catch(error => {
  console.error('Error running examples:', error);
});
