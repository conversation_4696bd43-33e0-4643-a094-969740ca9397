/**
 * Common Validation Schemas
 * 
 * This file contains common validation schemas used across multiple endpoints.
 */

const Joi = require('joi');
const { commonSchemas: baseCommonSchemas } = require('../../utils/requestValidator');

/**
 * Extended common schemas
 */
const commonSchemas = {
  ...baseCommonSchemas,
  
  // Pagination schema
  pagination: Joi.object({
    page: baseCommonSchemas.page,
    limit: baseCommonSchemas.limit,
    sortBy: baseCommonSchemas.sortBy,
    sortOrder: baseCommonSchemas.sortOrder
  }),
  
  // Date range schema
  dateRange: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional()
  }),
  
  // Search schema
  search: Joi.object({
    query: Joi.string().allow('').optional(),
    fields: Joi.array().items(Joi.string()).optional()
  }),
  
  // Filter schema
  filter: Joi.object({
    field: Joi.string().required(),
    operator: Joi.string().valid('eq', 'ne', 'gt', 'gte', 'lt', 'lte', 'in', 'nin', 'contains', 'startsWith', 'endsWith').required(),
    value: Joi.alternatives().try(
      Joi.string(),
      Joi.number(),
      Joi.boolean(),
      Joi.array().items(Joi.alternatives().try(Joi.string(), Joi.number(), Joi.boolean()))
    ).required()
  }),
  
  // Filters schema
  filters: Joi.array().items(Joi.object({
    field: Joi.string().required(),
    operator: Joi.string().valid('eq', 'ne', 'gt', 'gte', 'lt', 'lte', 'in', 'nin', 'contains', 'startsWith', 'endsWith').required(),
    value: Joi.alternatives().try(
      Joi.string(),
      Joi.number(),
      Joi.boolean(),
      Joi.array().items(Joi.alternatives().try(Joi.string(), Joi.number(), Joi.boolean()))
    ).required()
  })),
  
  // Sort schema
  sort: Joi.object({
    field: Joi.string().required(),
    order: Joi.string().valid('asc', 'desc').required()
  }),
  
  // Sorts schema
  sorts: Joi.array().items(Joi.object({
    field: Joi.string().required(),
    order: Joi.string().valid('asc', 'desc').required()
  })),
  
  // Export schema
  export: Joi.object({
    format: Joi.string().valid('csv', 'json', 'excel', 'pdf').required(),
    fileName: Joi.string().optional()
  }),
  
  // ID array schema
  idArray: Joi.array().items(baseCommonSchemas.id),
  
  // UUID array schema
  uuidArray: Joi.array().items(baseCommonSchemas.uuid),
  
  // Email array schema
  emailArray: Joi.array().items(baseCommonSchemas.email),
  
  // Connector type schema
  connectorType: Joi.string().valid(
    'aws', 'azure', 'gcp', 'github', 'gitlab', 'bitbucket',
    'jira', 'confluence', 'slack', 'teams', 'salesforce',
    'servicenow', 'zendesk', 'okta', 'auth0', 'custom'
  ),
  
  // Connector status schema
  connectorStatus: Joi.string().valid('active', 'inactive', 'error', 'pending'),
  
  // Report type schema
  reportType: Joi.string().valid('compliance', 'performance', 'security', 'custom'),
  
  // Report format schema
  reportFormat: Joi.string().valid('pdf', 'csv', 'excel', 'json'),
  
  // Schedule type schema
  scheduleType: Joi.string().valid('daily', 'weekly', 'monthly', 'quarterly', 'custom'),
  
  // Role schema
  role: Joi.string().valid('admin', 'manager', 'user', 'viewer'),
  
  // Permission schema
  permission: Joi.string().pattern(/^[a-z]+:[a-z]+$/),
  
  // Permission array schema
  permissionArray: Joi.array().items(Joi.string().pattern(/^[a-z]+:[a-z]+$/)),
  
  // Team schema
  team: Joi.object({
    id: baseCommonSchemas.id.optional(),
    name: Joi.string().required(),
    description: Joi.string().allow('').optional()
  }),
  
  // User schema
  user: Joi.object({
    id: baseCommonSchemas.id.optional(),
    email: baseCommonSchemas.email.required(),
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    role: Joi.string().valid('admin', 'manager', 'user', 'viewer').required()
  }),
  
  // Credential schema
  credential: Joi.object({
    id: baseCommonSchemas.id.optional(),
    name: Joi.string().required(),
    type: Joi.string().required(),
    data: Joi.object().required()
  }),
  
  // Webhook schema
  webhook: Joi.object({
    id: baseCommonSchemas.id.optional(),
    name: Joi.string().required(),
    url: Joi.string().uri().required(),
    events: Joi.array().items(Joi.string()).required(),
    active: Joi.boolean().default(true)
  }),
  
  // API key schema
  apiKey: Joi.object({
    id: baseCommonSchemas.id.optional(),
    name: Joi.string().required(),
    expiresAt: Joi.date().iso().optional(),
    permissions: Joi.array().items(Joi.string().pattern(/^[a-z]+:[a-z]+$/)).optional()
  }),
  
  // Environment schema
  environment: Joi.object({
    id: baseCommonSchemas.id.optional(),
    name: Joi.string().required(),
    description: Joi.string().allow('').optional(),
    type: Joi.string().valid('development', 'staging', 'production').required()
  }),
  
  // Tenant schema
  tenant: Joi.object({
    id: baseCommonSchemas.id.optional(),
    name: Joi.string().required(),
    domain: Joi.string().domain().required(),
    plan: Joi.string().valid('free', 'basic', 'premium', 'enterprise').required(),
    status: Joi.string().valid('active', 'inactive', 'suspended').required()
  })
};

module.exports = {
  commonSchemas
};
