apiVersion: monitoring.googleapis.com/v1
kind: MonitoringDashboard
metadata:
  name: novaconnect-performance-dashboard
  namespace: novaconnect
spec:
  displayName: "NovaConnect Performance Dashboard"
  gridLayout:
    columns: 2
    widgets:
      - title: "API Request Rate"
        xyChart:
          dataSets:
            - timeSeriesQuery:
                timeSeriesFilter:
                  filter: 'metric.type="custom.googleapis.com/novaconnect/api_requests_total" resource.type="k8s_container" resource.label."namespace_name"="novaconnect"'
                  aggregation:
                    alignmentPeriod: 60s
                    perSeriesAligner: ALIGN_RATE
                    crossSeriesReducer: REDUCE_SUM
                    groupByFields:
                      - "metric.label.endpoint"
          yAxis:
            scale: LINEAR
            label: "Requests/second"
      
      - title: "API Response Time (p95)"
        xyChart:
          dataSets:
            - timeSeriesQuery:
                timeSeriesFilter:
                  filter: 'metric.type="custom.googleapis.com/novaconnect/api_response_time" resource.type="k8s_container" resource.label."namespace_name"="novaconnect"'
                  aggregation:
                    alignmentPeriod: 60s
                    perSeriesAligner: ALIGN_PERCENTILE_95
                    crossSeriesReducer: REDUCE_MEAN
                    groupByFields:
                      - "metric.label.endpoint"
          yAxis:
            scale: LINEAR
            label: "Response Time (ms)"
      
      - title: "Data Normalization Time (p95)"
        xyChart:
          dataSets:
            - timeSeriesQuery:
                timeSeriesFilter:
                  filter: 'metric.type="custom.googleapis.com/novaconnect/normalization_time" resource.type="k8s_container" resource.label."namespace_name"="novaconnect"'
                  aggregation:
                    alignmentPeriod: 60s
                    perSeriesAligner: ALIGN_PERCENTILE_95
                    crossSeriesReducer: REDUCE_MEAN
          yAxis:
            scale: LINEAR
            label: "Normalization Time (ms)"
      
      - title: "Remediation Execution Time (p95)"
        xyChart:
          dataSets:
            - timeSeriesQuery:
                timeSeriesFilter:
                  filter: 'metric.type="custom.googleapis.com/novaconnect/remediation_time" resource.type="k8s_container" resource.label."namespace_name"="novaconnect"'
                  aggregation:
                    alignmentPeriod: 60s
                    perSeriesAligner: ALIGN_PERCENTILE_95
                    crossSeriesReducer: REDUCE_MEAN
          yAxis:
            scale: LINEAR
            label: "Remediation Time (ms)"
      
      - title: "Memory Usage"
        xyChart:
          dataSets:
            - timeSeriesQuery:
                timeSeriesFilter:
                  filter: 'metric.type="kubernetes.io/container/memory/used_bytes" resource.type="k8s_container" resource.label."namespace_name"="novaconnect"'
                  aggregation:
                    alignmentPeriod: 60s
                    perSeriesAligner: ALIGN_MEAN
                    crossSeriesReducer: REDUCE_MEAN
                    groupByFields:
                      - "resource.label.pod_name"
          yAxis:
            scale: LINEAR
            label: "Memory (bytes)"
      
      - title: "CPU Usage"
        xyChart:
          dataSets:
            - timeSeriesQuery:
                timeSeriesFilter:
                  filter: 'metric.type="kubernetes.io/container/cpu/usage_time" resource.type="k8s_container" resource.label."namespace_name"="novaconnect"'
                  aggregation:
                    alignmentPeriod: 60s
                    perSeriesAligner: ALIGN_RATE
                    crossSeriesReducer: REDUCE_MEAN
                    groupByFields:
                      - "resource.label.pod_name"
          yAxis:
            scale: LINEAR
            label: "CPU (cores)"
---
apiVersion: monitoring.googleapis.com/v1
kind: AlertPolicy
metadata:
  name: novaconnect-high-latency-alert
  namespace: novaconnect
spec:
  displayName: "NovaConnect High Latency Alert"
  conditions:
    - displayName: "High API Response Time"
      conditionThreshold:
        filter: 'metric.type="custom.googleapis.com/novaconnect/api_response_time" resource.type="k8s_container" resource.label."namespace_name"="novaconnect"'
        aggregations:
          - alignmentPeriod: 60s
            perSeriesAligner: ALIGN_PERCENTILE_95
            crossSeriesReducer: REDUCE_MEAN
        comparison: COMPARISON_GT
        thresholdValue: 100
        duration: 300s
  alertStrategy:
    autoClose: 86400s
  combiner: OR
  notificationChannels:
    - "projects/novafuse-project/notificationChannels/email-ops-team"
---
apiVersion: monitoring.googleapis.com/v1
kind: AlertPolicy
metadata:
  name: novaconnect-high-error-rate-alert
  namespace: novaconnect
spec:
  displayName: "NovaConnect High Error Rate Alert"
  conditions:
    - displayName: "High API Error Rate"
      conditionThreshold:
        filter: 'metric.type="custom.googleapis.com/novaconnect/api_errors_total" resource.type="k8s_container" resource.label."namespace_name"="novaconnect"'
        aggregations:
          - alignmentPeriod: 60s
            perSeriesAligner: ALIGN_RATE
            crossSeriesReducer: REDUCE_SUM
        comparison: COMPARISON_GT
        thresholdValue: 5
        duration: 300s
  alertStrategy:
    autoClose: 86400s
  combiner: OR
  notificationChannels:
    - "projects/novafuse-project/notificationChannels/email-ops-team"
---
apiVersion: logging.googleapis.com/v1
kind: LogSink
metadata:
  name: novaconnect-logs
  namespace: novaconnect
spec:
  destination: "storage.googleapis.com/novafuse-project-logs"
  filter: 'resource.type="k8s_container" AND resource.labels.namespace_name="novaconnect"'
  includeChildren: true
---
apiVersion: logging.googleapis.com/v1
kind: LogMetric
metadata:
  name: novaconnect-error-count
  namespace: novaconnect
spec:
  filter: 'resource.type="k8s_container" AND resource.labels.namespace_name="novaconnect" AND severity>=ERROR'
  metricDescriptor:
    metricKind: DELTA
    valueType: INT64
    unit: "1"
    labels:
      - key: severity
        valueType: STRING
      - key: pod_name
        valueType: STRING
  labelExtractors:
    severity: 'EXTRACT(severity)'
    pod_name: 'EXTRACT(resource.labels.pod_name)'
