import React from 'react';
import Head from 'next/head';
import Sidebar from '../components/Sidebar';
import PartnerKnowledgeBase from '../components/PartnerKnowledgeBase';

export default function PartnerKnowledgeBasePage() {
  // SEO metadata
  const pageProps = {
    title: 'Partner Knowledge Base - NovaFuse',
    description: 'Everything you need to know about partnering with NovaFuse. Explore our comprehensive resources designed to help you succeed as a NovaFuse partner.',
    keywords: 'NovaFuse partner, partner knowledge base, partner resources, partner program, partner benefits, partner integration, compliance enablement, NovaFuse',
    canonical: 'https://novafuse.io/partner-knowledge-base',
    ogImage: '/images/partner-knowledge-base-og-image.png'
  };

  const sidebarItems = [
    { type: 'category', label: 'Partner Resources', items: [
      { label: 'Partner Ecosystem', href: '/partner-ecosystem' },
      { label: 'Partner Empowerment', href: '/partner-empowerment' },
      { label: 'Partner Onboarding', href: '/partner-onboarding' },
      { label: 'API Documentation', href: '/api-docs' }
    ]},
    { type: 'category', label: 'Preferred Partners', items: [
      { label: 'Zapier', href: '/zapier-partnership' },
      { label: 'Okta', href: '/okta-partnership' },
      { label: 'Airtable', href: '/airtable-partnership' },
      { label: 'Google Cloud', href: '/google-partnership-portal' }
    ]}
  ];

  return (
    <>
      <Head>
        <title>{pageProps.title}</title>
        <meta name="description" content={pageProps.description} />
        <meta name="keywords" content={pageProps.keywords} />
        <meta name="author" content="NovaFuse" />
        <meta name="robots" content="index, follow" />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content={pageProps.canonical} />
        <meta property="og:title" content={pageProps.title} />
        <meta property="og:description" content={pageProps.description} />
        <meta property="og:image" content={pageProps.ogImage} />

        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content={pageProps.canonical} />
        <meta property="twitter:title" content={pageProps.title} />
        <meta property="twitter:description" content={pageProps.description} />
        <meta property="twitter:image" content={pageProps.ogImage} />

        {/* Canonical URL */}
        <link rel="canonical" href={pageProps.canonical} />
      </Head>
      <div className="flex flex-col md:flex-row gap-8">
      {/* Sidebar - Hidden on mobile, shown on desktop */}
      <div className="hidden md:block md:w-1/4 lg:w-1/5">
        <div className="sticky top-4">
          <Sidebar items={sidebarItems} title="Partner Knowledge Base" />
        </div>
      </div>

      {/* Mobile Sidebar Toggle - Shown on mobile only */}
      <div className="md:hidden mb-4">
        <select
          className="w-full bg-secondary text-white border border-gray-700 rounded p-2"
          onChange={(e) => {
            if (e.target.value) window.location.href = e.target.value;
          }}
          defaultValue=""
        >
          <option value="" disabled>Navigate to...</option>
          {sidebarItems.map((item, index) => (
            item.type === 'category' ? (
              <optgroup key={index} label={item.label}>
                {item.items.map((subItem, subIndex) => (
                  <option key={`${index}-${subIndex}`} value={subItem.href}>
                    {subItem.label}
                  </option>
                ))}
              </optgroup>
            ) : (
              <option key={index} value={item.href}>
                {item.label}
              </option>
            )
          ))}
        </select>
      </div>

      {/* Main Content */}
      <div className="w-full md:w-3/4 lg:w-4/5">
        {/* Hero Section */}
        <div className="accent-bg text-white rounded-lg p-8 mb-8">
          <h1 className="text-3xl font-bold mb-4">NovaFuse Partner Knowledge Base</h1>
          <p className="text-xl mb-6">
            Everything you need to know about partnering with NovaFuse. Explore our comprehensive resources
            designed to help you succeed as a NovaFuse partner.
          </p>
          <div className="flex flex-wrap gap-4">
            <a href="#overview" className="bg-white text-blue-600 px-6 py-3 rounded-lg font-bold hover:bg-blue-50 inline-block">
              Explore Benefits
            </a>
            <a href="/partner-onboarding" className="border border-white text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-900 hover:bg-opacity-20 inline-block">
              Become a Partner
            </a>
          </div>
        </div>

        {/* Knowledge Base Component */}
        <PartnerKnowledgeBase />

        {/* CTA Section */}
        <div className="bg-secondary rounded-lg p-8 mt-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Transform Compliance into a Competitive Advantage?</h2>
          <p className="text-xl text-gray-300 mb-6 max-w-3xl mx-auto">
            Join NovaFuse's partner ecosystem and start offering compliance as a feature, not a burden.
            Our Partner Empowerment approach ensures your success every step of the way.
          </p>
          <a href="/partner-onboarding" className="accent-bg text-white px-8 py-4 rounded-lg font-bold hover:bg-blue-700 inline-block">
            Apply to Become a Partner
          </a>
        </div>
      </div>
    </div>
    </>
  );
}
