
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tensor Health Dashboard - test-tensor</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      background-color: #f8f9fa;
    }
    .dashboard-header {
      margin-bottom: 20px;
    }
    .card {
      margin-bottom: 20px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .metric-value {
      font-size: 24px;
      font-weight: bold;
    }
    .metric-label {
      font-size: 14px;
      color: #6c757d;
    }
    .chart-container {
      position: relative;
      height: 250px;
    }
    .status-indicator {
      width: 15px;
      height: 15px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 5px;
    }
    .status-good {
      background-color: #28a745;
    }
    .status-warning {
      background-color: #ffc107;
    }
    .status-critical {
      background-color: #dc3545;
    }
    .auto-refresh {
      font-size: 12px;
      color: #6c757d;
    }
  </style>
</head>
<body>
  <div class="container-fluid">
    <div class="dashboard-header">
      <div class="row">
        <div class="col-md-6">
          <h1>Tensor Health Dashboard</h1>
          <p>Tensor ID: test-tensor</p>
          <p class="auto-refresh">Auto-refreshes every 5 seconds</p>
        </div>
        <div class="col-md-6 text-end">
          <p>Last Updated: 5/16/2025, 11:15:23 PM</p>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-3">
        <div class="card">
          <div class="card-body text-center">
            <div class="metric-label">Health</div>
            <div class="metric-value">
              <span class="status-indicator status-good"></span>
              1.000
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-body text-center">
            <div class="metric-label">Entropy Containment</div>
            <div class="metric-value">
              <span class="status-indicator status-good"></span>
              0.010
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-body text-center">
            <div class="metric-label">Healing Cycles</div>
            <div class="metric-value">0</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-body text-center">
            <div class="metric-label">Domain</div>
            <div class="metric-value">cyber</div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Health History</h5>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="healthChart"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Entropy History</h5>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="entropyChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Drift History</h5>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="driftChart"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Healing Efficiency</h5>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="healingChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    
        <div class="card">
          <div class="card-header">
            <h5>Entropy Forecast</h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <p><strong>Current Entropy:</strong> 0.050</p>
                <p><strong>Forecasted Entropy:</strong> 0.050</p>
                <p><strong>Trend:</strong> stable</p>
              </div>
              <div class="col-md-6">
                <p><strong>Confidence:</strong> 50.0%</p>
                <p><strong>Forecast Window:</strong> 5 cycles</p>
                <p><strong>Seasonality Detected:</strong> No</p>
              </div>
            </div>
          </div>
        </div>
      
    
  </div>
  
  <script>
    // Health Chart
    const healthCtx = document.getElementById('healthChart').getContext('2d');
    new Chart(healthCtx, {
      type: 'line',
      data: {
        datasets: [{
          label: 'Health',
          data: [{"x":"11:10:12 PM","y":"1.000"},{"x":"11:10:13 PM","y":"0.787"},{"x":"11:10:13 PM","y":"0.890"}],
          borderColor: 'rgba(40, 167, 69, 1)',
          backgroundColor: 'rgba(40, 167, 69, 0.1)',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            max: 1
          }
        }
      }
    });
    
    // Entropy Chart
    const entropyCtx = document.getElementById('entropyChart').getContext('2d');
    new Chart(entropyCtx, {
      type: 'line',
      data: {
        datasets: [{
          label: 'Entropy Containment',
          data: [{"x":"11:10:12 PM","y":"0.010"},{"x":"11:10:13 PM","y":"0.050"},{"x":"11:10:13 PM","y":"0.050"}],
          borderColor: 'rgba(220, 53, 69, 1)',
          backgroundColor: 'rgba(220, 53, 69, 0.1)',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            max: 0.05
          }
        }
      }
    });
    
    // Drift Chart
    const driftCtx = document.getElementById('driftChart').getContext('2d');
    new Chart(driftCtx, {
      type: 'line',
      data: {
        datasets: [{
          label: 'Drift Factor',
          data: [],
          borderColor: 'rgba(255, 193, 7, 1)',
          backgroundColor: 'rgba(255, 193, 7, 0.1)',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
    
    // Healing Chart
    const healingCtx = document.getElementById('healingChart').getContext('2d');
    new Chart(healingCtx, {
      type: 'line',
      data: {
        datasets: [{
          label: 'Healing Efficiency',
          data: [{"x":"11:10:13 PM","y":"0.151"},{"x":"11:10:13 PM","y":"0.023"}],
          borderColor: 'rgba(13, 110, 253, 1)',
          backgroundColor: 'rgba(13, 110, 253, 0.1)',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            max: 1
          }
        }
      }
    });
    
    // Auto-refresh
    setTimeout(() => {
      location.reload();
    }, 5000);
  </script>
</body>
</html>
    