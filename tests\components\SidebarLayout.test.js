import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import SidebarLayout from '../../components/SidebarLayout';

// Mock the Layout component
jest.mock('../../components/Layout', () => {
  return function MockLayout({ children, showNavigation }) {
    return (
      <div data-testid="mock-layout" data-show-navigation={showNavigation.toString()}>
        {children}
      </div>
    );
  };
});

// Mock the Sidebar component
jest.mock('../../components/Sidebar', () => {
  return function MockSidebar({ items, title }) {
    return (
      <div data-testid="mock-sidebar">
        <h3>{title || 'Navigation'}</h3>
        <ul>
          {items.map((item, index) => (
            <li key={index} data-testid="sidebar-item">
              {item.label}
            </li>
          ))}
        </ul>
      </div>
    );
  };
});

describe('SidebarLayout', () => {
  const sidebarItems = [
    { label: 'Item 1', href: '/item1' },
    { label: 'Item 2', href: '/item2' },
    { label: 'Item 3', href: '/item3' }
  ];

  it('renders the Layout component with showNavigation=false', () => {
    render(
      <SidebarLayout sidebarItems={sidebarItems} sidebarTitle="Sidebar Title">
        <div>Content</div>
      </SidebarLayout>
    );

    // Check if Layout component is rendered with showNavigation=false
    const layout = screen.getByTestId('mock-layout');
    expect(layout).toBeInTheDocument();
    expect(layout).toHaveAttribute('data-show-navigation', 'false');
  });

  it('renders the Sidebar component with correct props', () => {
    render(
      <SidebarLayout sidebarItems={sidebarItems} sidebarTitle="Sidebar Title">
        <div>Content</div>
      </SidebarLayout>
    );

    // Check if Sidebar component is rendered
    const sidebar = screen.getByTestId('mock-sidebar');
    expect(sidebar).toBeInTheDocument();

    // Check if title is passed correctly
    expect(sidebar).toHaveTextContent('Sidebar Title');

    // Check if items are passed correctly
    const sidebarItemElements = screen.getAllByTestId('sidebar-item');
    expect(sidebarItemElements).toHaveLength(3);
    expect(sidebarItemElements[0]).toHaveTextContent('Item 1');
    expect(sidebarItemElements[1]).toHaveTextContent('Item 2');
    expect(sidebarItemElements[2]).toHaveTextContent('Item 3');
  });

  it('renders children correctly', () => {
    render(
      <SidebarLayout sidebarItems={sidebarItems} sidebarTitle="Sidebar Title">
        <div data-testid="content">Content</div>
      </SidebarLayout>
    );

    // Check if children are rendered
    expect(screen.getByTestId('content')).toBeInTheDocument();
    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('renders mobile dropdown with correct options', () => {
    render(
      <SidebarLayout sidebarItems={sidebarItems} sidebarTitle="Sidebar Title">
        <div>Content</div>
      </SidebarLayout>
    );

    // Check if mobile dropdown is rendered
    const dropdown = screen.getByRole('combobox');
    expect(dropdown).toBeInTheDocument();

    // Check if dropdown has the correct options
    const options = screen.getAllByRole('option');
    expect(options.length).toBe(4); // 3 items + "Navigate to..." placeholder

    // Check if the placeholder option is rendered
    expect(options[0]).toHaveTextContent('Navigate to...');
    expect(options[0]).toBeDisabled();

    // Check if item options are rendered
    expect(options[1]).toHaveTextContent('Item 1');
    expect(options[1]).toHaveValue('/item1');
    expect(options[2]).toHaveTextContent('Item 2');
    expect(options[2]).toHaveValue('/item2');
    expect(options[3]).toHaveTextContent('Item 3');
    expect(options[3]).toHaveValue('/item3');
  });

  it('renders nested sidebar items correctly in mobile dropdown', () => {
    const nestedSidebarItems = [
      {
        type: 'category',
        label: 'Category 1',
        items: [
          { label: 'Sub Item 1', href: '/sub1' },
          { label: 'Sub Item 2', href: '/sub2' }
        ]
      },
      { label: 'Regular Item', href: '/regular' }
    ];

    render(
      <SidebarLayout sidebarItems={nestedSidebarItems} sidebarTitle="Sidebar Title">
        <div>Content</div>
      </SidebarLayout>
    );

    // Check if mobile dropdown is rendered
    const dropdown = screen.getByRole('combobox');
    expect(dropdown).toBeInTheDocument();

    // Check if dropdown has the correct options
    const options = screen.getAllByRole('option');
    expect(options.length).toBe(4); // 1 placeholder + 2 sub-items + 1 regular item

    // Check if the placeholder option is rendered
    expect(options[0]).toHaveTextContent('Navigate to...');

    // Check if sub-item options are rendered
    expect(options[1]).toHaveTextContent('Sub Item 1');
    expect(options[1]).toHaveValue('/sub1');
    expect(options[2]).toHaveTextContent('Sub Item 2');
    expect(options[2]).toHaveValue('/sub2');

    // Check if regular item option is rendered
    expect(options[3]).toHaveTextContent('Regular Item');
    expect(options[3]).toHaveValue('/regular');

    // Check if optgroup is rendered
    const optgroup = screen.getByRole('group');
    expect(optgroup).toBeInTheDocument();
    expect(optgroup).toHaveAttribute('label', 'Category 1');
  });

  it('navigates when an option is selected', () => {
    // Mock window.location.href
    const originalLocation = window.location;
    delete window.location;
    window.location = { href: '' };

    render(
      <SidebarLayout sidebarItems={sidebarItems} sidebarTitle="Sidebar Title">
        <div>Content</div>
      </SidebarLayout>
    );

    // Select an option
    const dropdown = screen.getByRole('combobox');
    fireEvent.change(dropdown, { target: { value: '/item2' } });

    // Check if navigation occurred
    expect(window.location.href).toBe('/item2');

    // Restore window.location
    window.location = originalLocation;
  });
});
