# NovaFuse CSDE Testing Project

This repository contains the implementation and testing framework for validating the Cyber-Safety Decision Engine (CSDE) performance claims as described in the NovaFuse God Patent.

## Overview

The CSDE is based on the Universal Unified Field Theory (UUFT) equation: (A ⊗ B ⊕ C) × π10³, where:
- A represents compliance data
- B represents cloud infrastructure data
- C represents threat intelligence
- ⊗ is the tensor product operator
- ⊕ is the fusion operator with golden ratio weighting
- π10³ is the scaling factor

This testing framework aims to validate the following claims:
1. 3,142x performance improvement over traditional methods
2. Sub-millisecond latency (≤0.07ms)
3. High throughput (69,000 events/sec)
4. Efficiency gains from the 18/82 resource allocation principle

## Repository Structure

```
csde-testing/
├── csde/                   # Core CSDE implementation
│   ├── core.py             # CSDE equation implementation
│   ├── data_connectors.py  # GCP data connectors
│   └── utils.py            # Utility functions
├── benchmarks/             # Benchmarking code
│   ├── baseline.py         # Traditional approach implementation
│   ├── csde_benchmark.py   # CSDE benchmarking code
│   ├── compare.py          # Comparison framework
│   └── run_all.py          # Script to run all benchmarks
├── reporting/              # Results reporting
│   ├── generate_report.py  # Report generation
│   └── templates/          # Report templates
├── results/                # Test results directory
├── tests/                  # Unit tests
├── requirements.txt        # Python dependencies
└── README.md               # This file
```

## Getting Started

### Prerequisites

- GCP account with appropriate permissions
- Python 3.8+
- GPU-enabled environment (recommended)

### Installation

1. Clone this repository:
```bash
git clone https://github.com/novafuse/csde-testing.git
cd csde-testing
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up GCP authentication:
```bash
gcloud auth application-default login
```

4. Set your project ID:
```bash
export PROJECT_ID=your-gcp-project-id
```

### Running Tests

1. Run all benchmarks:
```bash
python -m benchmarks.run_all --project-id=$PROJECT_ID
```

2. Compare with traditional approach:
```bash
python -m benchmarks.compare --project-id=$PROJECT_ID
```

3. Generate performance report:
```bash
python -m reporting.generate_report --results-dir=results
```

## Docker Testing Results

Initial testing was performed in a Docker container with limited resources. The results are documented in [CSDE_Docker_Test_Results.md](CSDE_Docker_Test_Results.md).

## GCP Testing Guide

For detailed instructions on setting up and running tests in GCP, see [CSDE_GCP_Testing_Guide.md](CSDE_GCP_Testing_Guide.md).

## Contributing

1. Create a feature branch
2. Make your changes
3. Run tests
4. Submit a pull request

## License

Proprietary - NovaFuse, Inc.

## Contact

For questions or support, contact <EMAIL>.
