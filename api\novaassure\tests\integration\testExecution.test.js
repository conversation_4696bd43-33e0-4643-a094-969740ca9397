/**
 * Test Execution Integration Tests
 * 
 * This file contains integration tests for the test execution API.
 */

const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../../../app');
const { TestExecution, TestPlan, Control } = require('../../models');
const jwt = require('jsonwebtoken');
const config = require('../../../config');

// Create test token
const testUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  role: 'admin'
};
const testToken = jwt.sign(testUser, config.jwt.secretKey, { expiresIn: '1h' });

describe('Test Execution API', () => {
  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(process.env.MONGODB_URI_TEST || 'mongodb://localhost:27017/novafuse-test', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    // Clear database
    await TestExecution.deleteMany({});
    await TestPlan.deleteMany({});
    await Control.deleteMany({});
  });
  
  afterAll(async () => {
    // Disconnect from database
    await mongoose.disconnect();
  });
  
  describe('GET /api/v1/novaassure/test-execution', () => {
    beforeEach(async () => {
      // Create test data
      await TestExecution.create([
        {
          testPlan: 'test-plan-id-1',
          status: 'completed',
          executedBy: 'test-user-id',
          startedAt: new Date('2023-01-01'),
          completedAt: new Date('2023-01-02'),
          results: [
            {
              control: 'control-id-1',
              status: 'pass',
              notes: 'Test passed',
              evidence: [],
              executedBy: 'test-user-id',
              executedAt: new Date('2023-01-01')
            }
          ]
        },
        {
          testPlan: 'test-plan-id-2',
          status: 'in-progress',
          executedBy: 'test-user-id',
          startedAt: new Date('2023-01-03')
        }
      ]);
    });
    
    afterEach(async () => {
      // Clear test data
      await TestExecution.deleteMany({});
    });
    
    it('should return all test executions', async () => {
      const response = await request(app)
        .get('/api/v1/novaassure/test-execution')
        .set('Authorization', `Bearer ${testToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.length).toBe(2);
      expect(response.body.data[0].status).toBe('completed');
      expect(response.body.data[1].status).toBe('in-progress');
    });
    
    it('should filter test executions by status', async () => {
      const response = await request(app)
        .get('/api/v1/novaassure/test-execution?status=completed')
        .set('Authorization', `Bearer ${testToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.length).toBe(1);
      expect(response.body.data[0].status).toBe('completed');
    });
    
    it('should filter test executions by test plan', async () => {
      const response = await request(app)
        .get('/api/v1/novaassure/test-execution?testPlanId=test-plan-id-1')
        .set('Authorization', `Bearer ${testToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.length).toBe(1);
      expect(response.body.data[0].testPlan).toBe('test-plan-id-1');
    });
    
    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .get('/api/v1/novaassure/test-execution');
      
      expect(response.status).toBe(401);
    });
  });
  
  describe('POST /api/v1/novaassure/test-execution/start', () => {
    beforeEach(async () => {
      // Create test data
      await TestPlan.create({
        _id: 'test-plan-id',
        name: 'Test Plan',
        description: 'Test plan description',
        framework: 'soc2',
        controls: ['control-id-1', 'control-id-2'],
        status: 'active'
      });
    });
    
    afterEach(async () => {
      // Clear test data
      await TestPlan.deleteMany({});
      await TestExecution.deleteMany({});
    });
    
    it('should start a new test execution', async () => {
      const response = await request(app)
        .post('/api/v1/novaassure/test-execution/start')
        .set('Authorization', `Bearer ${testToken}`)
        .send({
          testPlanId: 'test-plan-id',
          notes: 'Test execution notes'
        });
      
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.testPlan).toBe('test-plan-id');
      expect(response.body.data.status).toBe('in-progress');
      expect(response.body.data.executedBy).toBe('test-user-id');
      expect(response.body.data.notes).toBe('Test execution notes');
      
      // Check database
      const testExecution = await TestExecution.findById(response.body.data._id);
      expect(testExecution).not.toBeNull();
      expect(testExecution.testPlan.toString()).toBe('test-plan-id');
      expect(testExecution.status).toBe('in-progress');
      expect(testExecution.executedBy).toBe('test-user-id');
      expect(testExecution.notes).toBe('Test execution notes');
    });
    
    it('should return 400 if test plan ID is missing', async () => {
      const response = await request(app)
        .post('/api/v1/novaassure/test-execution/start')
        .set('Authorization', `Bearer ${testToken}`)
        .send({
          notes: 'Test execution notes'
        });
      
      expect(response.status).toBe(400);
    });
    
    it('should return 404 if test plan is not found', async () => {
      const response = await request(app)
        .post('/api/v1/novaassure/test-execution/start')
        .set('Authorization', `Bearer ${testToken}`)
        .send({
          testPlanId: 'non-existent-test-plan-id',
          notes: 'Test execution notes'
        });
      
      expect(response.status).toBe(404);
    });
    
    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .post('/api/v1/novaassure/test-execution/start')
        .send({
          testPlanId: 'test-plan-id',
          notes: 'Test execution notes'
        });
      
      expect(response.status).toBe(401);
    });
  });
  
  describe('POST /api/v1/novaassure/test-execution/{id}/complete', () => {
    beforeEach(async () => {
      // Create test data
      await TestExecution.create({
        _id: 'test-execution-id',
        testPlan: 'test-plan-id',
        status: 'in-progress',
        executedBy: 'test-user-id',
        startedAt: new Date('2023-01-01')
      });
    });
    
    afterEach(async () => {
      // Clear test data
      await TestExecution.deleteMany({});
    });
    
    it('should complete a test execution', async () => {
      const response = await request(app)
        .post('/api/v1/novaassure/test-execution/test-execution-id/complete')
        .set('Authorization', `Bearer ${testToken}`)
        .send({
          results: [
            {
              controlId: 'control-id-1',
              status: 'pass',
              notes: 'Test passed',
              evidenceIds: []
            },
            {
              controlId: 'control-id-2',
              status: 'fail',
              notes: 'Test failed',
              evidenceIds: []
            }
          ],
          notes: 'Test execution completed'
        });
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('completed');
      expect(response.body.data.results.length).toBe(2);
      expect(response.body.data.results[0].status).toBe('pass');
      expect(response.body.data.results[1].status).toBe('fail');
      expect(response.body.data.notes).toBe('Test execution completed');
      
      // Check database
      const testExecution = await TestExecution.findById('test-execution-id');
      expect(testExecution).not.toBeNull();
      expect(testExecution.status).toBe('completed');
      expect(testExecution.results.length).toBe(2);
      expect(testExecution.results[0].status).toBe('pass');
      expect(testExecution.results[1].status).toBe('fail');
      expect(testExecution.notes).toBe('Test execution completed');
      expect(testExecution.completedAt).not.toBeNull();
    });
    
    it('should return 400 if results are missing', async () => {
      const response = await request(app)
        .post('/api/v1/novaassure/test-execution/test-execution-id/complete')
        .set('Authorization', `Bearer ${testToken}`)
        .send({
          notes: 'Test execution completed'
        });
      
      expect(response.status).toBe(400);
    });
    
    it('should return 404 if test execution is not found', async () => {
      const response = await request(app)
        .post('/api/v1/novaassure/test-execution/non-existent-test-execution-id/complete')
        .set('Authorization', `Bearer ${testToken}`)
        .send({
          results: [
            {
              controlId: 'control-id-1',
              status: 'pass',
              notes: 'Test passed',
              evidenceIds: []
            }
          ],
          notes: 'Test execution completed'
        });
      
      expect(response.status).toBe(404);
    });
    
    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .post('/api/v1/novaassure/test-execution/test-execution-id/complete')
        .send({
          results: [
            {
              controlId: 'control-id-1',
              status: 'pass',
              notes: 'Test passed',
              evidenceIds: []
            }
          ],
          notes: 'Test execution completed'
        });
      
      expect(response.status).toBe(401);
    });
  });
});
