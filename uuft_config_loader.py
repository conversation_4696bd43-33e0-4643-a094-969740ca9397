#!/usr/bin/env python3
"""
UUFT Configuration Loader

This module loads and validates the UUFT configuration from a YAML file.
It provides a unified configuration interface for all UUFT/CSDE components.
"""

import os
import yaml
import datetime
import logging
from typing import Dict, Any, Optional, List, Union

# Default configuration values
DEFAULT_CONFIG = {
    "experiment_id": f"uuft-test-{datetime.datetime.now().strftime('%Y%m%d-%H%M%S')}",
    "environment": "docker",
    "timestamp": datetime.datetime.now().isoformat(),
    
    "resource_allocation": {
        "mode": "optimized",
        "critical_weight": 0.82,
        "standard_weight": 0.18,
        "dynamic_balancing": True
    },
    
    "fusion": {
        "operator_chain": "(A ⊗ B ⊕ C) × π",
        "pi_scalar": 1000,
        "normalization": "layer_norm",
        "skip_connections": True,
        "domain_encoders": {
            "domain_a": "financial_encoder_v1",
            "domain_b": "healthcare_encoder_v1"
        },
        "attention_enabled": False,
        "residual_injection": False
    },
    
    "cross_domain_translation": {
        "contrastive_loss_enabled": False,
        "pattern_preservation_metric": "cosine_similarity",
        "loss_weight": 0.5
    },
    
    "prediction": {
        "model_type": "transformer",
        "accuracy_metric": "f1_score",
        "calibration": False
    },
    
    "profiling": {
        "enabled": False,
        "tools": ["cProfile"],
        "report_format": "text"
    },
    
    "logging": {
        "output_dir": "./logs",
        "log_level": "INFO",
        "versioning_enabled": False
    }
}

class UUFTConfig:
    """
    UUFT Configuration Manager
    
    This class loads, validates, and provides access to the UUFT configuration.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the configuration manager
        
        Args:
            config_path: Path to the configuration YAML file (optional)
        """
        self.config = DEFAULT_CONFIG.copy()
        self.config_path = config_path
        
        if config_path and os.path.exists(config_path):
            self.load_config(config_path)
        
        # Set up logging
        self._setup_logging()
    
    def load_config(self, config_path: str) -> None:
        """
        Load configuration from a YAML file
        
        Args:
            config_path: Path to the configuration YAML file
        """
        try:
            with open(config_path, 'r') as f:
                user_config = yaml.safe_load(f)
            
            # Update configuration with user values
            self._update_config(self.config, user_config)
            
            # Validate configuration
            self._validate_config()
            
            logging.info(f"Loaded configuration from {config_path}")
        except Exception as e:
            logging.error(f"Error loading configuration from {config_path}: {e}")
            logging.warning("Using default configuration")
    
    def _update_config(self, config: Dict[str, Any], updates: Dict[str, Any]) -> None:
        """
        Recursively update configuration dictionary
        
        Args:
            config: Configuration dictionary to update
            updates: Dictionary with updates
        """
        for key, value in updates.items():
            if key in config and isinstance(value, dict) and isinstance(config[key], dict):
                self._update_config(config[key], value)
            else:
                config[key] = value
    
    def _validate_config(self) -> None:
        """
        Validate configuration values
        """
        # Validate resource allocation
        ra_config = self.config["resource_allocation"]
        if ra_config["mode"] not in ["equal", "optimized", "entropy_weighted"]:
            logging.warning(f"Invalid resource allocation mode: {ra_config['mode']}. Using 'optimized'.")
            ra_config["mode"] = "optimized"
        
        # Validate weights sum to 1.0
        total_weight = ra_config["critical_weight"] + ra_config["standard_weight"]
        if abs(total_weight - 1.0) > 0.001:
            logging.warning(f"Resource allocation weights do not sum to 1.0: {total_weight}. Normalizing.")
            ra_config["critical_weight"] /= total_weight
            ra_config["standard_weight"] /= total_weight
        
        # Validate fusion configuration
        fusion_config = self.config["fusion"]
        if fusion_config["normalization"] not in ["batch_norm", "layer_norm", "none"]:
            logging.warning(f"Invalid normalization: {fusion_config['normalization']}. Using 'layer_norm'.")
            fusion_config["normalization"] = "layer_norm"
        
        # Validate pi_scalar
        if fusion_config["pi_scalar"] != "auto" and not isinstance(fusion_config["pi_scalar"], (int, float)):
            logging.warning(f"Invalid pi_scalar: {fusion_config['pi_scalar']}. Using 1000.")
            fusion_config["pi_scalar"] = 1000
        
        # Validate logging configuration
        log_config = self.config["logging"]
        if log_config["log_level"] not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            logging.warning(f"Invalid log level: {log_config['log_level']}. Using 'INFO'.")
            log_config["log_level"] = "INFO"
    
    def _setup_logging(self) -> None:
        """
        Set up logging based on configuration
        """
        log_config = self.config["logging"]
        
        # Create output directory if it doesn't exist
        os.makedirs(log_config["output_dir"], exist_ok=True)
        
        # Set up logging
        log_level = getattr(logging, log_config["log_level"])
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # Set up file handler
        log_file = os.path.join(
            log_config["output_dir"],
            f"{self.config['experiment_id']}.log"
        )
        
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value
        
        Args:
            key: Configuration key (can use dot notation for nested keys)
            default: Default value if key is not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        Set a configuration value
        
        Args:
            key: Configuration key (can use dot notation for nested keys)
            value: Value to set
        """
        keys = key.split('.')
        config = self.config
        
        # Navigate to the parent of the key
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
    
    def save(self, path: Optional[str] = None) -> None:
        """
        Save configuration to a YAML file
        
        Args:
            path: Path to save the configuration (optional)
        """
        save_path = path or self.config_path or "uuft_config.yaml"
        
        try:
            with open(save_path, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False)
            
            logging.info(f"Saved configuration to {save_path}")
        except Exception as e:
            logging.error(f"Error saving configuration to {save_path}: {e}")
    
    def __str__(self) -> str:
        """
        String representation of the configuration
        
        Returns:
            YAML string of the configuration
        """
        return yaml.dump(self.config, default_flow_style=False)

# Singleton instance
_config_instance = None

def get_config(config_path: Optional[str] = None) -> UUFTConfig:
    """
    Get the configuration instance
    
    Args:
        config_path: Path to the configuration YAML file (optional)
        
    Returns:
        Configuration instance
    """
    global _config_instance
    
    if _config_instance is None:
        _config_instance = UUFTConfig(config_path)
    
    return _config_instance

if __name__ == "__main__":
    # Example usage
    import argparse
    
    parser = argparse.ArgumentParser(description="UUFT Configuration Loader")
    parser.add_argument("--config", help="Path to configuration YAML file")
    parser.add_argument("--validate", action="store_true", help="Validate configuration and exit")
    args = parser.parse_args()
    
    # Load configuration
    config = get_config(args.config)
    
    if args.validate:
        print("Configuration is valid.")
        exit(0)
    
    # Print configuration
    print(config)
