/**
 * @swagger
 * tags:
 *   name: ESG Metrics
 *   description: ESG (Environmental, Social, Governance) Metrics API
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     ESGMetric:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the ESG metric
 *         name:
 *           type: string
 *           description: Name of the ESG metric
 *         description:
 *           type: string
 *           description: Description of the ESG metric
 *         category:
 *           type: string
 *           enum: [environmental, social, governance]
 *           description: Category of the ESG metric
 *         subcategory:
 *           type: string
 *           description: Subcategory of the ESG metric
 *         unit:
 *           type: string
 *           description: Unit of measurement for the metric
 *         dataType:
 *           type: string
 *           enum: [numeric, percentage, boolean, text, date]
 *           description: Data type of the metric value
 *         framework:
 *           type: string
 *           description: ESG framework this metric belongs to (e.g., GRI, SASB, TCFD)
 *         targetValue:
 *           type: string
 *           description: Target value for the metric
 *         targetDate:
 *           type: string
 *           format: date
 *           description: Target date for achieving the target value
 *         owner:
 *           type: string
 *           description: Owner of the metric
 *         status:
 *           type: string
 *           enum: [active, inactive, archived]
 *           description: Status of the metric
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the metric was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the metric was last updated
 *       required:
 *         - id
 *         - name
 *         - category
 *         - dataType
 *         - status
 *         - createdAt
 *         - updatedAt
 *     
 *     ESGMetricInput:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Name of the ESG metric
 *         description:
 *           type: string
 *           description: Description of the ESG metric
 *         category:
 *           type: string
 *           enum: [environmental, social, governance]
 *           description: Category of the ESG metric
 *         subcategory:
 *           type: string
 *           description: Subcategory of the ESG metric
 *         unit:
 *           type: string
 *           description: Unit of measurement for the metric
 *         dataType:
 *           type: string
 *           enum: [numeric, percentage, boolean, text, date]
 *           description: Data type of the metric value
 *         framework:
 *           type: string
 *           description: ESG framework this metric belongs to (e.g., GRI, SASB, TCFD)
 *         targetValue:
 *           type: string
 *           description: Target value for the metric
 *         targetDate:
 *           type: string
 *           format: date
 *           description: Target date for achieving the target value
 *         owner:
 *           type: string
 *           description: Owner of the metric
 *         status:
 *           type: string
 *           enum: [active, inactive, archived]
 *           description: Status of the metric
 *       required:
 *         - name
 *         - category
 *         - dataType
 *         - status
 *     
 *     ESGDataPoint:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the data point
 *         metricId:
 *           type: string
 *           description: ID of the ESG metric this data point belongs to
 *         value:
 *           type: string
 *           description: Value of the data point
 *         date:
 *           type: string
 *           format: date
 *           description: Date the data point was recorded for
 *         period:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, annually]
 *           description: Reporting period for the data point
 *         source:
 *           type: string
 *           description: Source of the data
 *         notes:
 *           type: string
 *           description: Additional notes about the data point
 *         verificationStatus:
 *           type: string
 *           enum: [unverified, verified, rejected]
 *           description: Verification status of the data point
 *         verifiedBy:
 *           type: string
 *           description: Person who verified the data point
 *         verifiedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the data point was verified
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the data point was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the data point was last updated
 *       required:
 *         - id
 *         - metricId
 *         - value
 *         - date
 *         - period
 *         - verificationStatus
 *         - createdAt
 *         - updatedAt
 *     
 *     ESGDataPointInput:
 *       type: object
 *       properties:
 *         value:
 *           type: string
 *           description: Value of the data point
 *         date:
 *           type: string
 *           format: date
 *           description: Date the data point was recorded for
 *         period:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, annually]
 *           description: Reporting period for the data point
 *         source:
 *           type: string
 *           description: Source of the data
 *         notes:
 *           type: string
 *           description: Additional notes about the data point
 *         verificationStatus:
 *           type: string
 *           enum: [unverified, verified, rejected]
 *           description: Verification status of the data point
 *         verifiedBy:
 *           type: string
 *           description: Person who verified the data point
 *         verifiedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the data point was verified
 *       required:
 *         - value
 *         - date
 *         - period
 *         - verificationStatus
 *     
 *     ESGInitiative:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the ESG initiative
 *         name:
 *           type: string
 *           description: Name of the ESG initiative
 *         description:
 *           type: string
 *           description: Description of the ESG initiative
 *         category:
 *           type: string
 *           enum: [environmental, social, governance]
 *           description: Category of the ESG initiative
 *         startDate:
 *           type: string
 *           format: date
 *           description: Start date of the initiative
 *         endDate:
 *           type: string
 *           format: date
 *           description: End date of the initiative
 *         status:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *           description: Status of the initiative
 *         owner:
 *           type: string
 *           description: Owner of the initiative
 *         budget:
 *           type: number
 *           description: Budget allocated for the initiative
 *         metrics:
 *           type: array
 *           items:
 *             type: string
 *           description: IDs of ESG metrics associated with this initiative
 *         goals:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               description:
 *                 type: string
 *               targetDate:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [not-started, in-progress, completed, cancelled]
 *           description: Goals of the initiative
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the initiative was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the initiative was last updated
 *       required:
 *         - id
 *         - name
 *         - category
 *         - status
 *         - createdAt
 *         - updatedAt
 *     
 *     ESGInitiativeInput:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Name of the ESG initiative
 *         description:
 *           type: string
 *           description: Description of the ESG initiative
 *         category:
 *           type: string
 *           enum: [environmental, social, governance]
 *           description: Category of the ESG initiative
 *         startDate:
 *           type: string
 *           format: date
 *           description: Start date of the initiative
 *         endDate:
 *           type: string
 *           format: date
 *           description: End date of the initiative
 *         status:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *           description: Status of the initiative
 *         owner:
 *           type: string
 *           description: Owner of the initiative
 *         budget:
 *           type: number
 *           description: Budget allocated for the initiative
 *         metrics:
 *           type: array
 *           items:
 *             type: string
 *           description: IDs of ESG metrics associated with this initiative
 *         goals:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               description:
 *                 type: string
 *               targetDate:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [not-started, in-progress, completed, cancelled]
 *           description: Goals of the initiative
 *       required:
 *         - name
 *         - category
 *         - status
 */
