/**
 * NovaFuse Universal API Connector - API Usage Repository Tests
 */

const { apiUsageRepository } = require('../../../src/data/repositories');
const ApiUsage = require('../../../src/data/models/api-usage');

// Mock the ApiUsage model
jest.mock('../../../src/data/models/api-usage', () => {
  return {
    recordUsage: jest.fn(),
    getStatistics: jest.fn(),
    find: jest.fn().mockReturnThis(),
    aggregate: jest.fn(),
    sort: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis()
  };
});

describe('API Usage Repository', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  const mockUsageData = {
    partnerId: 'partner-123',
    connectorId: 'test-connector-1.0',
    endpointId: 'get-data',
    credentialId: 'cred-12345',
    requestId: 'req-12345',
    success: true,
    statusCode: 200,
    duration: 150,
    requestSize: 100,
    responseSize: 500,
    ipAddress: '***********',
    userAgent: 'Mozilla/5.0',
    billable: true
  };
  
  const mockStatistics = {
    totalRequests: 100,
    successfulRequests: 90,
    failedRequests: 10,
    totalDuration: 15000,
    averageDuration: 150,
    totalCost: 50,
    minDuration: 50,
    maxDuration: 500,
    successRate: 90
  };
  
  it('should record API usage', async () => {
    ApiUsage.recordUsage.mockResolvedValue(mockUsageData);
    
    const result = await apiUsageRepository.recordUsage(mockUsageData);
    
    expect(ApiUsage.recordUsage).toHaveBeenCalledWith(mockUsageData);
    expect(result).toEqual(mockUsageData);
  });
  
  it('should get statistics', async () => {
    ApiUsage.getStatistics.mockResolvedValue(mockStatistics);
    
    const statistics = await apiUsageRepository.getStatistics({ partnerId: 'partner-123' }, 'month');
    
    expect(ApiUsage.getStatistics).toHaveBeenCalledWith({ partnerId: 'partner-123' }, 'month');
    expect(statistics).toEqual(mockStatistics);
  });
  
  it('should get usage by partner', async () => {
    ApiUsage.find.mockImplementation(() => ({
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockResolvedValue([mockUsageData])
    }));
    
    const usage = await apiUsageRepository.getUsageByPartner('partner-123');
    
    expect(ApiUsage.find).toHaveBeenCalledWith({ partnerId: 'partner-123' });
    expect(usage).toEqual([mockUsageData]);
  });
  
  it('should get usage by connector', async () => {
    ApiUsage.find.mockImplementation(() => ({
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockResolvedValue([mockUsageData])
    }));
    
    const usage = await apiUsageRepository.getUsageByConnector('test-connector-1.0');
    
    expect(ApiUsage.find).toHaveBeenCalledWith({ connectorId: 'test-connector-1.0' });
    expect(usage).toEqual([mockUsageData]);
  });
  
  it('should get usage by endpoint', async () => {
    ApiUsage.find.mockImplementation(() => ({
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockResolvedValue([mockUsageData])
    }));
    
    const usage = await apiUsageRepository.getUsageByEndpoint('test-connector-1.0', 'get-data');
    
    expect(ApiUsage.find).toHaveBeenCalledWith({ connectorId: 'test-connector-1.0', endpointId: 'get-data' });
    expect(usage).toEqual([mockUsageData]);
  });
  
  it('should get usage by credential', async () => {
    ApiUsage.find.mockImplementation(() => ({
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockResolvedValue([mockUsageData])
    }));
    
    const usage = await apiUsageRepository.getUsageByCredential('cred-12345');
    
    expect(ApiUsage.find).toHaveBeenCalledWith({ credentialId: 'cred-12345' });
    expect(usage).toEqual([mockUsageData]);
  });
  
  it('should get billable usage', async () => {
    ApiUsage.find.mockImplementation(() => ({
      sort: jest.fn().mockResolvedValue([mockUsageData])
    }));
    
    const usage = await apiUsageRepository.getBillableUsage('partner-123');
    
    expect(ApiUsage.find).toHaveBeenCalledWith({ partnerId: 'partner-123', billable: true });
    expect(usage).toEqual([mockUsageData]);
  });
  
  it('should get usage summary', async () => {
    ApiUsage.getStatistics.mockResolvedValue({
      ...mockStatistics,
      _timeframeFilter: { $gte: new Date() }
    });
    
    ApiUsage.aggregate.mockResolvedValueOnce([
      { _id: 'test-connector-1.0', totalRequests: 50, successfulRequests: 45, totalDuration: 7500, totalCost: 25 }
    ]).mockResolvedValueOnce([
      { _id: { connectorId: 'test-connector-1.0', endpointId: 'get-data' }, totalRequests: 50, successfulRequests: 45, totalDuration: 7500, totalCost: 25 }
    ]);
    
    const summary = await apiUsageRepository.getUsageSummary('partner-123', 'month');
    
    expect(ApiUsage.getStatistics).toHaveBeenCalledWith({ partnerId: 'partner-123' }, 'month');
    expect(ApiUsage.aggregate).toHaveBeenCalledTimes(2);
    expect(summary).toHaveProperty('statistics');
    expect(summary).toHaveProperty('connectorBreakdown');
    expect(summary).toHaveProperty('endpointBreakdown');
  });
});
