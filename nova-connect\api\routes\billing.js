/**
 * NovaConnect UAC Billing Routes
 * 
 * This module provides routes for handling GCP Marketplace billing webhooks.
 */

const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const logger = require('../../config/logger');
const BillingService = require('../services/BillingService');
const billingService = new BillingService();

/**
 * Marketplace webhook handler
 * 
 * This endpoint handles GCP Marketplace billing webhooks.
 */
router.post('/marketplace/webhook', [
  body('type').isString().notEmpty(),
  body('customerId').isString().notEmpty(),
  body('entitlement').optional(),
  body('timestamp').optional().isISO8601()
], async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.warn('Invalid marketplace webhook payload', { errors: errors.array() });
      return res.status(400).json({ errors: errors.array() });
    }
    
    const event = req.body;
    logger.info('Received marketplace webhook', { type: event.type, customerId: event.customerId });
    
    // Process the event
    switch (event.type) {
      case 'ENTITLEMENT_CREATED':
        await billingService.enableFeatures(event.customerId, event.entitlement);
        break;
      
      case 'ENTITLEMENT_UPDATED':
        await billingService.updateFeatures(event.customerId, event.entitlement);
        break;
      
      case 'ENTITLEMENT_DELETED':
        await billingService.disableFeatures(event.customerId, event.entitlement);
        break;
      
      case 'ENTITLEMENT_ACTIVATED':
        await billingService.activateFeatures(event.customerId, event.entitlement);
        break;
      
      case 'ENTITLEMENT_SUSPENDED':
        await billingService.suspendFeatures(event.customerId, event.entitlement);
        break;
      
      default:
        logger.warn('Unknown marketplace webhook event type', { type: event.type });
    }
    
    // Always return 200 to acknowledge receipt
    res.status(200).json({ status: 'success' });
  } catch (error) {
    logger.error('Error processing marketplace webhook', { error: error.message, stack: error.stack });
    
    // Always return 200 to acknowledge receipt, even on error
    // This prevents GCP from retrying the webhook unnecessarily
    res.status(200).json({ status: 'error', message: 'Error processing webhook, but acknowledged' });
  }
});

/**
 * Get customer entitlements
 */
router.get('/customers/:customerId/entitlements', async (req, res) => {
  try {
    const { customerId } = req.params;
    
    // Get customer entitlements
    const entitlements = await billingService.getCustomerEntitlements(customerId);
    
    res.status(200).json(entitlements);
  } catch (error) {
    logger.error('Error getting customer entitlements', { error: error.message, customerId: req.params.customerId });
    res.status(500).json({ error: 'Error getting customer entitlements' });
  }
});

/**
 * Get customer usage
 */
router.get('/customers/:customerId/usage', async (req, res) => {
  try {
    const { customerId } = req.params;
    const { startDate, endDate } = req.query;
    
    // Get customer usage
    const usage = await billingService.getCustomerUsage(customerId, startDate, endDate);
    
    res.status(200).json(usage);
  } catch (error) {
    logger.error('Error getting customer usage', { error: error.message, customerId: req.params.customerId });
    res.status(500).json({ error: 'Error getting customer usage' });
  }
});

/**
 * Report usage
 */
router.post('/customers/:customerId/report-usage', [
  body('metricName').isString().notEmpty(),
  body('quantity').isInt({ min: 1 }),
  body('timestamp').optional().isISO8601()
], async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    const { customerId } = req.params;
    const { metricName, quantity, timestamp } = req.body;
    
    // Report usage
    await billingService.reportUsage(customerId, metricName, quantity, timestamp);
    
    res.status(200).json({ status: 'success' });
  } catch (error) {
    logger.error('Error reporting usage', { 
      error: error.message, 
      customerId: req.params.customerId,
      metricName: req.body.metricName,
      quantity: req.body.quantity
    });
    res.status(500).json({ error: 'Error reporting usage' });
  }
});

module.exports = router;
