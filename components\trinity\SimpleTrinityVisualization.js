import React, { useEffect, useRef, useState } from 'react';

/**
 * Simple Trinity Visualization Component
 * 
 * A simplified 2D canvas-based visualization of the Trinity concept
 * that works without Three.js dependencies.
 */
const SimpleTrinityVisualization = ({ width = '100%', height = '500px' }) => {
  const canvasRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const animationRef = useRef(null);
  
  // Constants for the visualization
  const COLORS = {
    BACKGROUND: '#111133',
    OUTER_WHEEL: '#4CAF50', // Green
    MIDDLE_WHEEL: '#2196F3', // Blue
    INNER_WHEEL: '#9C27B0', // Purple
    PARTICLES: ['#F44336', '#FF9800', '#FFEB3B'] // Red, Orange, Yellow
  };
  
  const WHEEL_PROPERTIES = {
    OUTER: {
      RADIUS: 180,
      WIDTH: 10,
      ROTATION_SPEED: 0.001
    },
    MIDDLE: {
      RADIUS: 120,
      WIDTH: 8,
      ROTATION_SPEED: 0.002
    },
    INNER: {
      RADIUS: 60,
      WIDTH: 6,
      ROTATION_SPEED: 0.003
    }
  };
  
  // Particles state
  const particlesRef = useRef([]);
  
  // Initialize the canvas
  useEffect(() => {
    if (isInitialized || !canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    // Set canvas size
    const resizeCanvas = () => {
      const container = canvas.parentElement;
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;
    };
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    // Create particles
    createParticles();
    
    // Start animation
    const animate = () => {
      // Clear canvas
      ctx.fillStyle = COLORS.BACKGROUND;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Draw wheels
      drawWheels(ctx, canvas.width / 2, canvas.height / 2);
      
      // Draw and update particles
      updateAndDrawParticles(ctx, canvas.width / 2, canvas.height / 2);
      
      // Continue animation
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animate();
    setIsInitialized(true);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isInitialized]);
  
  // Create particles
  const createParticles = () => {
    const particleCount = 50;
    const particles = [];
    
    for (let i = 0; i < particleCount; i++) {
      const angle = Math.random() * Math.PI * 2;
      const radius = WHEEL_PROPERTIES.OUTER.RADIUS;
      const type = Math.floor(Math.random() * 3); // 0: threat, 1: compliance, 2: decision
      const aspect = Math.floor(Math.random() * 3); // 0: pi, 1: phi, 2: e
      
      particles.push({
        x: Math.cos(angle) * radius,
        y: Math.sin(angle) * radius,
        size: 3 + Math.random() * 3,
        color: COLORS.PARTICLES[type],
        speed: 0.5 + Math.random() * 1,
        angle,
        radius,
        type,
        aspect,
        wheel: 'outer',
        targetWheel: 'middle',
        progress: 0,
        pulsePhase: Math.random() * Math.PI * 2,
        pulseSpeed: 0.05 + Math.random() * 0.05,
        trail: []
      });
    }
    
    particlesRef.current = particles;
  };
  
  // Draw wheels
  const drawWheels = (ctx, centerX, centerY) => {
    const time = performance.now() * 0.001;
    
    // Draw outer wheel
    ctx.beginPath();
    ctx.arc(
      centerX, 
      centerY, 
      WHEEL_PROPERTIES.OUTER.RADIUS, 
      0, 
      Math.PI * 2
    );
    ctx.strokeStyle = COLORS.OUTER_WHEEL;
    ctx.lineWidth = WHEEL_PROPERTIES.OUTER.WIDTH;
    ctx.stroke();
    
    // Draw middle wheel
    ctx.beginPath();
    ctx.ellipse(
      centerX, 
      centerY, 
      WHEEL_PROPERTIES.MIDDLE.RADIUS, 
      WHEEL_PROPERTIES.MIDDLE.RADIUS * 0.8,
      time * WHEEL_PROPERTIES.MIDDLE.ROTATION_SPEED,
      0, 
      Math.PI * 2
    );
    ctx.strokeStyle = COLORS.MIDDLE_WHEEL;
    ctx.lineWidth = WHEEL_PROPERTIES.MIDDLE.WIDTH;
    ctx.stroke();
    
    // Draw inner wheel
    ctx.beginPath();
    ctx.ellipse(
      centerX, 
      centerY, 
      WHEEL_PROPERTIES.INNER.RADIUS, 
      WHEEL_PROPERTIES.INNER.RADIUS * 0.9,
      time * WHEEL_PROPERTIES.INNER.ROTATION_SPEED * 2,
      0, 
      Math.PI * 2
    );
    ctx.strokeStyle = COLORS.INNER_WHEEL;
    ctx.lineWidth = WHEEL_PROPERTIES.INNER.WIDTH;
    ctx.stroke();
    
    // Draw Trinity nodes
    drawTrinityNodes(ctx, centerX, centerY, time);
  };
  
  // Draw Trinity nodes
  const drawTrinityNodes = (ctx, centerX, centerY, time) => {
    // Outer wheel nodes
    const outerNodes = [
      { name: 'Direct Impact', angle: time * 0.1, color: '#F44336' },
      { name: 'Adjacent Resonance', angle: time * 0.1 + Math.PI * 2/3, color: '#FF9800' },
      { name: 'Field Saturation', angle: time * 0.1 + Math.PI * 4/3, color: '#FFEB3B' }
    ];
    
    outerNodes.forEach(node => {
      const x = centerX + Math.cos(node.angle) * WHEEL_PROPERTIES.OUTER.RADIUS;
      const y = centerY + Math.sin(node.angle) * WHEEL_PROPERTIES.OUTER.RADIUS;
      
      ctx.beginPath();
      ctx.arc(x, y, 8, 0, Math.PI * 2);
      ctx.fillStyle = node.color;
      ctx.fill();
    });
    
    // Middle wheel nodes
    const middleNodes = [
      { name: 'Pi (Governance)', angle: time * 0.2, color: '#4CAF50' },
      { name: 'Phi (Detection)', angle: time * 0.2 + Math.PI * 2/3, color: '#2196F3' },
      { name: 'e (Response)', angle: time * 0.2 + Math.PI * 4/3, color: '#9C27B0' }
    ];
    
    middleNodes.forEach(node => {
      const x = centerX + Math.cos(node.angle) * WHEEL_PROPERTIES.MIDDLE.RADIUS;
      const y = centerY + Math.sin(node.angle) * WHEEL_PROPERTIES.MIDDLE.RADIUS * 0.8;
      
      ctx.beginPath();
      ctx.arc(x, y, 6, 0, Math.PI * 2);
      ctx.fillStyle = node.color;
      ctx.fill();
    });
    
    // Inner wheel nodes
    const innerNodes = [
      { name: 'Quantum State Vectors', angle: time * 0.3, color: '#E91E63' },
      { name: 'Resonance Patterns', angle: time * 0.3 + Math.PI * 2/3, color: '#00BCD4' },
      { name: 'Field Matrices', angle: time * 0.3 + Math.PI * 4/3, color: '#8BC34A' }
    ];
    
    innerNodes.forEach(node => {
      const x = centerX + Math.cos(node.angle) * WHEEL_PROPERTIES.INNER.RADIUS;
      const y = centerY + Math.sin(node.angle) * WHEEL_PROPERTIES.INNER.RADIUS * 0.9;
      
      ctx.beginPath();
      ctx.arc(x, y, 4, 0, Math.PI * 2);
      ctx.fillStyle = node.color;
      ctx.fill();
    });
  };
  
  // Update and draw particles
  const updateAndDrawParticles = (ctx, centerX, centerY) => {
    const particles = particlesRef.current;
    
    particles.forEach(particle => {
      // Update particle position
      particle.progress += particle.speed * 0.01;
      
      if (particle.progress >= 1) {
        // Reset progress and change target
        particle.progress = 0;
        
        if (particle.wheel === 'outer' && particle.targetWheel === 'middle') {
          // Move from outer to middle
          particle.wheel = 'middle';
          particle.targetWheel = 'inner';
          particle.radius = WHEEL_PROPERTIES.MIDDLE.RADIUS;
          particle.angle = Math.random() * Math.PI * 2;
        } else if (particle.wheel === 'middle' && particle.targetWheel === 'inner') {
          // Move from middle to inner
          particle.wheel = 'inner';
          particle.targetWheel = 'outer';
          particle.radius = WHEEL_PROPERTIES.INNER.RADIUS;
          particle.angle = Math.random() * Math.PI * 2;
        } else {
          // Move from inner to outer
          particle.wheel = 'outer';
          particle.targetWheel = 'middle';
          particle.radius = WHEEL_PROPERTIES.OUTER.RADIUS;
          particle.angle = Math.random() * Math.PI * 2;
        }
      }
      
      // Calculate new position
      let targetRadius, targetAngle;
      
      if (particle.targetWheel === 'middle') {
        targetRadius = WHEEL_PROPERTIES.MIDDLE.RADIUS;
        targetAngle = particle.angle + Math.PI * particle.progress;
      } else if (particle.targetWheel === 'inner') {
        targetRadius = WHEEL_PROPERTIES.INNER.RADIUS;
        targetAngle = particle.angle + Math.PI * particle.progress;
      } else {
        targetRadius = WHEEL_PROPERTIES.OUTER.RADIUS;
        targetAngle = particle.angle + Math.PI * particle.progress;
      }
      
      // Interpolate position
      const currentRadius = particle.radius * (1 - particle.progress) + targetRadius * particle.progress;
      const currentAngle = particle.angle * (1 - particle.progress) + targetAngle * particle.progress;
      
      // Apply aspect-specific modulation
      let x = centerX + Math.cos(currentAngle) * currentRadius;
      let y = centerY + Math.sin(currentAngle) * currentRadius;
      
      if (particle.aspect === 0) { // Pi
        const piModulation = Math.sin(particle.progress * Math.PI * 2) * 5;
        x += piModulation;
        y += piModulation;
      } else if (particle.aspect === 1) { // Phi
        const phiAngle = particle.progress * Math.PI * 2 * 1.618;
        const phiRadius = 5 * Math.pow(0.618, particle.progress * 3);
        x += Math.cos(phiAngle) * phiRadius;
        y += Math.sin(phiAngle) * phiRadius;
      } else if (particle.aspect === 2) { // e
        const eModulation = 5 * Math.pow(Math.E, particle.progress - 1);
        x += eModulation;
        y += eModulation;
      }
      
      // Update trail
      if (particle.trail.length > 5) {
        particle.trail.pop();
      }
      particle.trail.unshift({ x, y });
      
      // Draw trail
      if (particle.trail.length > 1) {
        ctx.beginPath();
        ctx.moveTo(particle.trail[0].x, particle.trail[0].y);
        
        for (let i = 1; i < particle.trail.length; i++) {
          ctx.lineTo(particle.trail[i].x, particle.trail[i].y);
        }
        
        ctx.strokeStyle = particle.color;
        ctx.globalAlpha = 0.3;
        ctx.lineWidth = particle.size / 2;
        ctx.stroke();
        ctx.globalAlpha = 1;
      }
      
      // Update pulse
      particle.pulsePhase += particle.pulseSpeed;
      const pulseFactor = 1 + Math.sin(particle.pulsePhase) * 0.3;
      
      // Draw particle
      ctx.beginPath();
      ctx.arc(x, y, particle.size * pulseFactor, 0, Math.PI * 2);
      ctx.fillStyle = particle.color;
      ctx.fill();
    });
  };
  
  return (
    <div style={{ 
      width, 
      height, 
      position: 'relative', 
      overflow: 'hidden',
      backgroundColor: COLORS.BACKGROUND,
      borderRadius: '8px'
    }}>
      <canvas 
        ref={canvasRef} 
        style={{ 
          width: '100%', 
          height: '100%', 
          display: 'block' 
        }}
      />
      <div style={{
        position: 'absolute',
        bottom: '10px',
        left: '10px',
        color: 'white',
        fontSize: '12px',
        opacity: 0.7,
        padding: '5px 10px',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        borderRadius: '4px'
      }}>
        Trinity Visualization - Powered by Comphyology (Ψᶜ)
      </div>
    </div>
  );
};

export default SimpleTrinityVisualization;
