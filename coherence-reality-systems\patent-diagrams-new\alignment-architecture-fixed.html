<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3-6-9-12-13 Alignment Architecture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 900px;
            height: 700px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            box-sizing: border-box;
        }
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%;
        }
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            box-sizing: border-box;
            font-size: 14px;
            line-height: 1.2;
        }
        .component-label {
            font-weight: bold;
            margin-bottom: 4px;
        }
        .component-number {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 20px;
            height: 20px;
            background-color: #333;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
        }
        .arrow {
            position: absolute;
            background-color: #333;
            width: 2px;
        }
        .legend {
            position: absolute;
            right: 10px;
            bottom: -90px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            width: 200px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border: 1px solid #333;
        }
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: -90px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>FIG. 5: 3-6-9-12-13 Alignment Architecture</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 850px; height: 650px; left: 25px; top: 20px;">
            <div class="container-label">CYBER-SAFETY FRAMEWORK: 3-6-9-12-13 ALIGNMENT ARCHITECTURE</div>
        </div>
        
        <!-- 3 Core Infrastructure Components -->
        <div class="container-box" style="width: 800px; height: 120px; left: 50px; top: 70px;">
            <div class="container-label">3 CORE INFRASTRUCTURE COMPONENTS</div>
        </div>
        
        <div class="component-box" style="left: 100px; top: 110px; width: 200px; height: 60px;">
            <div class="component-number">501</div>
            <div class="component-label">Pillar 3</div>
            Self-Destructing Compliance Servers
        </div>
        
        <div class="component-box" style="left: 350px; top: 110px; width: 200px; height: 60px;">
            <div class="component-number">502</div>
            <div class="component-label">Pillar 9</div>
            Post-Quantum Immutable Journal
        </div>
        
        <div class="component-box" style="left: 600px; top: 110px; width: 200px; height: 60px;">
            <div class="component-number">503</div>
            <div class="component-label">Pillar 12</div>
            C-Suite Directive to Code Compiler
        </div>
        
        <!-- 6 Data Processing Components -->
        <div class="container-box" style="width: 800px; height: 120px; left: 50px; top: 210px;">
            <div class="container-label">6 DATA PROCESSING COMPONENTS</div>
        </div>
        
        <div class="component-box" style="left: 75px; top: 250px; width: 120px; height: 60px;">
            <div class="component-number">504</div>
            <div class="component-label">Nova 3</div>
            NovaTrack
        </div>
        
        <div class="component-box" style="left: 205px; top: 250px; width: 120px; height: 60px;">
            <div class="component-number">505</div>
            <div class="component-label">Nova 6</div>
            NovaFlowX
        </div>
        
        <div class="component-box" style="left: 335px; top: 250px; width: 120px; height: 60px;">
            <div class="component-number">506</div>
            <div class="component-label">Nova 9</div>
            NovaThink
        </div>
        
        <div class="component-box" style="left: 465px; top: 250px; width: 120px; height: 60px;">
            <div class="component-number">507</div>
            <div class="component-label">Nova 12</div>
            NovaDNA
        </div>
        
        <div class="component-box" style="left: 595px; top: 250px; width: 120px; height: 60px;">
            <div class="component-number">508</div>
            <div class="component-label">Pillar 6</div>
            Cost Optimizer
        </div>
        
        <div class="component-box" style="left: 725px; top: 250px; width: 120px; height: 60px;">
            <div class="component-number">509</div>
            <div class="component-label">Pillar 7</div>
            Training Data
        </div>
        
        <!-- 9 Continuances -->
        <div class="container-box" style="width: 800px; height: 120px; left: 50px; top: 350px;">
            <div class="container-label">9 INDUSTRY-SPECIFIC CONTINUANCES</div>
        </div>
        
        <div class="component-box" style="left: 60px; top: 390px; width: 80px; height: 60px;">
            <div class="component-number">510</div>
            <div class="component-label">C1</div>
            Financial
        </div>
        
        <div class="component-box" style="left: 150px; top: 390px; width: 80px; height: 60px;">
            <div class="component-number">511</div>
            <div class="component-label">C2</div>
            Healthcare
        </div>
        
        <div class="component-box" style="left: 240px; top: 390px; width: 80px; height: 60px;">
            <div class="component-number">512</div>
            <div class="component-label">C3</div>
            Education
        </div>
        
        <div class="component-box" style="left: 330px; top: 390px; width: 80px; height: 60px;">
            <div class="component-number">513</div>
            <div class="component-label">C4</div>
            Government
        </div>
        
        <div class="component-box" style="left: 420px; top: 390px; width: 80px; height: 60px;">
            <div class="component-number">514</div>
            <div class="component-label">C5</div>
            Infrastructure
        </div>
        
        <div class="component-box" style="left: 510px; top: 390px; width: 80px; height: 60px;">
            <div class="component-number">515</div>
            <div class="component-label">C6</div>
            AI Governance
        </div>
        
        <div class="component-box" style="left: 600px; top: 390px; width: 80px; height: 60px;">
            <div class="component-number">516</div>
            <div class="component-label">C7</div>
            Supply Chain
        </div>
        
        <div class="component-box" style="left: 690px; top: 390px; width: 80px; height: 60px;">
            <div class="component-number">517</div>
            <div class="component-label">C8</div>
            Insurance
        </div>
        
        <div class="component-box" style="left: 780px; top: 390px; width: 80px; height: 60px;">
            <div class="component-number">518</div>
            <div class="component-label">C9</div>
            Mobile/IoT
        </div>
        
        <!-- 12 Pillars -->
        <div class="container-box" style="width: 800px; height: 80px; left: 50px; top: 490px;">
            <div class="container-label">12 PILLARS</div>
        </div>
        
        <div class="component-box" style="left: 425px; top: 520px; width: 100px; height: 30px;">
            <div class="component-number">519</div>
            <div class="component-label">P1-P12</div>
        </div>
        
        <!-- 12+1 Novas -->
        <div class="container-box" style="width: 800px; height: 80px; left: 50px; top: 590px;">
            <div class="container-label">12+1 UNIVERSAL NOVAS</div>
        </div>
        
        <div class="component-box" style="left: 425px; top: 620px; width: 100px; height: 30px;">
            <div class="component-number">520</div>
            <div class="component-label">N1-N13</div>
        </div>
        
        <!-- Connecting arrows -->
        <div class="arrow" style="left: 200px; top: 190px; height: 20px;"></div>
        <div class="arrow" style="left: 450px; top: 190px; height: 20px;"></div>
        <div class="arrow" style="left: 700px; top: 190px; height: 20px;"></div>
        
        <div class="arrow" style="left: 135px; top: 330px; height: 20px;"></div>
        <div class="arrow" style="left: 265px; top: 330px; height: 20px;"></div>
        <div class="arrow" style="left: 395px; top: 330px; height: 20px;"></div>
        <div class="arrow" style="left: 525px; top: 330px; height: 20px;"></div>
        <div class="arrow" style="left: 655px; top: 330px; height: 20px;"></div>
        <div class="arrow" style="left: 785px; top: 330px; height: 20px;"></div>
        
        <div class="arrow" style="left: 100px; top: 450px; height: 40px;"></div>
        <div class="arrow" style="left: 190px; top: 450px; height: 40px;"></div>
        <div class="arrow" style="left: 280px; top: 450px; height: 40px;"></div>
        <div class="arrow" style="left: 370px; top: 450px; height: 40px;"></div>
        <div class="arrow" style="left: 460px; top: 450px; height: 40px;"></div>
        <div class="arrow" style="left: 550px; top: 450px; height: 40px;"></div>
        <div class="arrow" style="left: 640px; top: 450px; height: 40px;"></div>
        <div class="arrow" style="left: 730px; top: 450px; height: 40px;"></div>
        <div class="arrow" style="left: 820px; top: 450px; height: 40px;"></div>
        
        <div class="arrow" style="left: 475px; top: 570px; height: 20px;"></div>
        
        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Core Infrastructure (3)</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Data Processing (6)</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Continuances (9)</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Pillars (12)</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Novas (12+1)</div>
            </div>
        </div>
        
        <!-- Inventor Label -->
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
