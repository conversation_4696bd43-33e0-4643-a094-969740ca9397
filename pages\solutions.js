import React from 'react';
import Link from 'next/link';
import PageWithSidebar from '../components/PageWithSidebar';

const Solutions = () => {
  const sidebarItems = [
    { label: 'By Industry', href: '/solutions/by-industry' },
    { label: 'By Use Case', href: '/solutions/by-use-case' },
    { label: 'By Framework', href: '/solutions/by-framework' },
    { label: 'Back to Home', href: '/' },
  ];

  return (
    <PageWithSidebar title="NovaFuse Solutions" sidebarItems={sidebarItems}>
      <div className="space-y-12">
        {/* Hero Section */}
        <div className="bg-blue-900 p-8 rounded-lg shadow-lg">
          <h1 className="text-3xl font-bold mb-4">NovaFuse Solutions</h1>
          <p className="text-xl mb-6">
            Explore our comprehensive compliance solutions tailored to your specific needs.
          </p>
        </div>

        {/* Solutions Categories */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* By Industry */}
          <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg border border-blue-400 hover:border-blue-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200">
            <div className="text-center mb-4">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white mb-2 shadow-md border border-blue-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-bold mb-2 text-center text-blue-100">By Industry</h3>
            <p className="text-center text-blue-100 mb-4">
              Solutions tailored to specific industries and their unique regulatory requirements.
            </p>
            <div className="text-center">
              <Link href="/solutions/by-industry" className="inline-block bg-blue-800 bg-opacity-50 hover:bg-opacity-70 text-white px-4 py-2 rounded-lg transition-all duration-200">
                Explore Industry Solutions
              </Link>
            </div>
          </div>

          {/* By Use Case */}
          <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg border border-blue-400 hover:border-blue-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200">
            <div className="text-center mb-4">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white mb-2 shadow-md border border-blue-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-bold mb-2 text-center text-blue-100">By Use Case</h3>
            <p className="text-center text-blue-100 mb-4">
              Solutions designed for specific compliance and governance use cases.
            </p>
            <div className="text-center">
              <Link href="/solutions/by-use-case" className="inline-block bg-blue-800 bg-opacity-50 hover:bg-opacity-70 text-white px-4 py-2 rounded-lg transition-all duration-200">
                Explore Use Case Solutions
              </Link>
            </div>
          </div>

          {/* By Framework */}
          <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg border border-blue-400 hover:border-blue-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200">
            <div className="text-center mb-4">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white mb-2 shadow-md border border-blue-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-bold mb-2 text-center text-blue-100">By Framework</h3>
            <p className="text-center text-blue-100 mb-4">
              Solutions mapped to specific compliance frameworks and standards.
            </p>
            <div className="text-center">
              <Link href="/solutions/by-framework" className="inline-block bg-blue-800 bg-opacity-50 hover:bg-opacity-70 text-white px-4 py-2 rounded-lg transition-all duration-200">
                Explore Framework Solutions
              </Link>
            </div>
          </div>
        </div>

        {/* Featured Solutions */}
        <div>
          <h2 className="text-2xl font-bold mb-6">Featured Solutions</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
                <h3 className="text-xl font-semibold mb-3">HIPAA Compliance Suite</h3>
                <p className="text-gray-300 mb-3">
                  Comprehensive solution for healthcare organizations to achieve and maintain HIPAA compliance.
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-blue-400">Healthcare</span>
                  <Link href="/solutions/by-industry#healthcare" className="text-sm text-purple-400 hover:text-purple-300">
                    Learn More →
                  </Link>
                </div>
              </div>

              <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
                <h3 className="text-xl font-semibold mb-3">SOC 2 Automation</h3>
                <p className="text-gray-300 mb-3">
                  Streamline SOC 2 compliance with automated evidence collection, control testing, and reporting.
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-blue-400">Technology</span>
                  <Link href="/solutions/by-framework#soc2" className="text-sm text-purple-400 hover:text-purple-300">
                    Learn More →
                  </Link>
                </div>
              </div>

              <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
                <h3 className="text-xl font-semibold mb-3">Privacy Management</h3>
                <p className="text-gray-300 mb-3">
                  Manage GDPR, CCPA, and other privacy regulations with our comprehensive privacy solution.
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-blue-400">Cross-Industry</span>
                  <Link href="/solutions/by-use-case#privacy" className="text-sm text-purple-400 hover:text-purple-300">
                    Learn More →
                  </Link>
                </div>
              </div>

              <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
                <h3 className="text-xl font-semibold mb-3">Third-Party Risk Management</h3>
                <p className="text-gray-300 mb-3">
                  Assess, monitor, and manage risks associated with vendors and third-party relationships.
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-blue-400">Cross-Industry</span>
                  <Link href="/solutions/by-use-case#third-party-risk" className="text-sm text-purple-400 hover:text-purple-300">
                    Learn More →
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-900 to-purple-900 p-8 rounded-lg shadow-lg border border-blue-400">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Ready to Transform Your Compliance Program?</h2>
            <p className="mb-6">
              Contact us to discuss how NovaFuse can address your specific compliance challenges.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/contact" className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                Contact Us
              </Link>
              <Link href="/uac-demo" className="bg-white text-blue-700 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                See the UAC Demo
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
};

export default Solutions;
