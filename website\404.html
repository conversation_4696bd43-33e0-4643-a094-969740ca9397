<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found | NovaFuse</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .error-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 60vh;
            text-align: center;
            padding: 2rem;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #2563eb 0%, #60a5fa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .error-message {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .error-description {
            font-size: 1.25rem;
            color: var(--secondary-text);
            max-width: 600px;
            margin: 0 auto 2rem auto;
        }
        
        .suggested-links {
            margin-top: 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            max-width: 800px;
            width: 100%;
        }
        
        .suggested-link {
            background-color: var(--secondary-bg);
            padding: 1rem;
            border-radius: 0.5rem;
            transition: transform 0.3s ease;
            text-align: left;
        }
        
        .suggested-link:hover {
            transform: translateY(-5px);
        }
        
        .suggested-link-icon {
            margin-bottom: 0.5rem;
            color: var(--accent-color);
        }
    </style>
</head>
<body>
    <!-- Include Header and Navigation -->
    <div id="header-container"></div>

    <main class="container">
        <div class="error-container">
            <div class="error-code">404</div>
            <h1 class="error-message">Page Not Found</h1>
            <p class="error-description">
                The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
            </p>
            <div class="flex space-x-4">
                <a href="index.html" class="btn btn-primary">
                    <i class="fas fa-home mr-2"></i> Go to Homepage
                </a>
                <a href="javascript:history.back()" class="btn btn-outline">
                    <i class="fas fa-arrow-left mr-2"></i> Go Back
                </a>
            </div>
            
            <h2 class="text-xl font-bold mt-8 mb-4">You might be looking for:</h2>
            <div class="suggested-links">
                <a href="novaconnect-uac.html" class="suggested-link">
                    <div class="suggested-link-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <h3 class="font-bold">NovaConnect UAC</h3>
                    <p class="text-sm text-secondary">Universal API Connector</p>
                </a>
                <a href="novagrc-suite.html" class="suggested-link">
                    <div class="suggested-link-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="font-bold">NovaGRC Suite</h3>
                    <p class="text-sm text-secondary">Governance, Risk, and Compliance</p>
                </a>
                <a href="partner-empowerment.html" class="suggested-link">
                    <div class="suggested-link-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3 class="font-bold">Partner Empowerment</h3>
                    <p class="text-sm text-secondary">Our Revolutionary Model</p>
                </a>
                <a href="resources.html" class="suggested-link">
                    <div class="suggested-link-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <h3 class="font-bold">Resources</h3>
                    <p class="text-sm text-secondary">White Papers, Case Studies, Blog</p>
                </a>
            </div>
            
            <div class="mt-8">
                <p class="text-secondary">
                    If you believe this is an error, please <a href="contact.html" class="text-accent-color">contact us</a>.
                </p>
            </div>
        </div>
    </main>

    <!-- Include Footer -->
    <div id="footer-container"></div>

    <script src="js/main.js"></script>
    <script>
        // Load header and footer components
        document.addEventListener('DOMContentLoaded', function() {
            // Load header
            fetch('components/header.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('header-container').innerHTML = data;
                });
            
            // Load footer
            fetch('components/footer.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('footer-container').innerHTML = data;
                });
        });
    </script>
</body>
</html>
