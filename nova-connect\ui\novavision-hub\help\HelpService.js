/**
 * Help Service
 * 
 * This module provides utilities for managing help content.
 */

/**
 * Help Service class
 */
class HelpService {
  /**
   * Constructor
   * 
   * @param {Object} options - Service options
   * @param {string} [options.apiUrl='/api/help'] - API URL for help content
   * @param {Function} [options.onError] - Error handler
   */
  constructor(options = {}) {
    this.apiUrl = options.apiUrl || '/api/help';
    this.onError = options.onError || console.error;
    this.helpContent = {};
  }
  
  /**
   * Register help content
   * 
   * @param {string} helpId - Help ID
   * @param {Object} content - Help content
   */
  registerHelpContent(helpId, content) {
    this.helpContent[helpId] = content;
  }
  
  /**
   * Register multiple help content items
   * 
   * @param {Object} contentMap - Map of help ID to content
   */
  registerMultipleHelpContent(contentMap) {
    this.helpContent = {
      ...this.helpContent,
      ...contentMap
    };
  }
  
  /**
   * Get help content
   * 
   * @param {string} helpId - Help ID
   * @returns {Object|null} Help content
   */
  getHelpContent(helpId) {
    return this.helpContent[helpId] || null;
  }
  
  /**
   * Get all help content
   * 
   * @returns {Object} All help content
   */
  getAllHelpContent() {
    return { ...this.helpContent };
  }
  
  /**
   * Search help content
   * 
   * @param {string} query - Search query
   * @returns {Array} Search results
   */
  searchHelpContent(query) {
    if (!query) {
      return [];
    }
    
    const lowerQuery = query.toLowerCase();
    
    return Object.entries(this.helpContent)
      .filter(([helpId, content]) => {
        const title = content.title || '';
        const description = content.description || '';
        const keywords = content.keywords || [];
        
        return (
          title.toLowerCase().includes(lowerQuery) ||
          description.toLowerCase().includes(lowerQuery) ||
          keywords.some(keyword => keyword.toLowerCase().includes(lowerQuery))
        );
      })
      .map(([helpId, content]) => ({
        helpId,
        ...content
      }));
  }
  
  /**
   * Fetch help content from API
   * 
   * @param {string} [helpId] - Help ID to fetch (if not provided, all help content will be fetched)
   * @returns {Promise<Object>} Help content
   */
  async fetchHelpContent(helpId) {
    try {
      const url = helpId ? `${this.apiUrl}/${helpId}` : this.apiUrl;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch help content: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (helpId) {
        this.registerHelpContent(helpId, data);
        return data;
      } else {
        this.registerMultipleHelpContent(data);
        return data;
      }
    } catch (error) {
      this.onError('Error fetching help content:', error);
      
      // Return local content if API is not available
      if (helpId) {
        return this.getHelpContent(helpId);
      } else {
        return this.getAllHelpContent();
      }
    }
  }
  
  /**
   * Load help content from a static source
   * 
   * @param {Object} content - Help content
   */
  loadStaticHelpContent(content) {
    this.registerMultipleHelpContent(content);
  }
  
  /**
   * Generate help content for a component
   * 
   * @param {string} componentName - Component name
   * @param {Object} componentProps - Component props
   * @returns {Object} Help content
   */
  generateComponentHelp(componentName, componentProps) {
    // Basic help content based on component name
    const baseContent = {
      title: `Help for ${componentName}`,
      description: `This is the ${componentName} component.`,
      keywords: [componentName, 'help', 'component']
    };
    
    // Add prop-specific help
    if (componentProps) {
      const propsHelp = Object.entries(componentProps).map(([propName, propValue]) => {
        return {
          name: propName,
          description: `The ${propName} prop controls ${this.getGenericPropDescription(propName, propValue)}.`,
          value: typeof propValue === 'object' ? JSON.stringify(propValue) : String(propValue)
        };
      });
      
      return {
        ...baseContent,
        props: propsHelp
      };
    }
    
    return baseContent;
  }
  
  /**
   * Get a generic description for a prop
   * 
   * @param {string} propName - Prop name
   * @param {any} propValue - Prop value
   * @returns {string} Prop description
   */
  getGenericPropDescription(propName, propValue) {
    // Common prop name patterns
    if (propName.startsWith('on') && typeof propValue === 'function') {
      return `what happens when the ${propName.slice(2)} event occurs`;
    }
    
    if (propName.startsWith('is') || propName.startsWith('has') || propName.startsWith('should')) {
      return `whether the component ${propName.replace(/^(is|has|should)/, '').toLowerCase()}`;
    }
    
    if (propName.includes('color')) {
      return 'the color of the component';
    }
    
    if (propName.includes('size')) {
      return 'the size of the component';
    }
    
    if (propName.includes('style')) {
      return 'the visual appearance of the component';
    }
    
    if (propName.includes('className')) {
      return 'additional CSS classes for styling';
    }
    
    // Default description
    return 'an aspect of the component';
  }
}

// Create singleton instance
const helpService = new HelpService();

// Load default help content
helpService.loadStaticHelpContent({
  'novavision-hub': {
    title: 'NovaVision Hub',
    description: 'The NovaVision Hub is a unified dashboard for all NovaFuse components.',
    content: `
      # NovaVision Hub
      
      The NovaVision Hub provides a unified interface for interacting with all NovaFuse components.
      
      ## Key Features
      
      - **Unified Dashboard**: View and interact with all NovaFuse components in one place
      - **Component Integration**: Seamlessly integrate with NovaConnect, NovaCore, NovaShield, and more
      - **Customizable UI**: Customize the dashboard to suit your needs
      - **Responsive Design**: Works on desktop and mobile devices
      
      ## Getting Started
      
      1. Navigate to the dashboard
      2. Select a component to view
      3. Use the component-specific controls to interact with the component
    `,
    keywords: ['novavision', 'hub', 'dashboard', 'novafuse', 'components']
  },
  'novaconnect': {
    title: 'NovaConnect',
    description: 'NovaConnect is the integration component of the NovaFuse platform.',
    content: `
      # NovaConnect
      
      NovaConnect is the integration component of the NovaFuse platform. It allows you to connect to various data sources and systems.
      
      ## Key Features
      
      - **Data Integration**: Connect to various data sources
      - **API Management**: Manage APIs and connectors
      - **Workflow Automation**: Automate workflows between systems
      - **Data Transformation**: Transform data between formats
      
      ## Getting Started
      
      1. Navigate to the NovaConnect section
      2. Create a new connector
      3. Configure the connector settings
      4. Test the connection
      5. Deploy the connector
    `,
    keywords: ['novaconnect', 'integration', 'connector', 'api', 'workflow']
  },
  'novacore': {
    title: 'NovaCore',
    description: 'NovaCore is the central processing component of the NovaFuse platform.',
    content: `
      # NovaCore
      
      NovaCore is the central processing component of the NovaFuse platform. It provides the core functionality for data processing, decision making, and policy enforcement.
      
      ## Key Features
      
      - **Data Processing**: Process data from various sources
      - **Decision Engine**: Make decisions based on rules and policies
      - **Policy Enforcement**: Enforce policies across the platform
      - **Event Processing**: Process events in real-time
      
      ## Getting Started
      
      1. Navigate to the NovaCore section
      2. Create a new policy
      3. Configure the policy rules
      4. Test the policy
      5. Deploy the policy
    `,
    keywords: ['novacore', 'processing', 'decision', 'policy', 'rules']
  },
  'novaproof': {
    title: 'NovaProof',
    description: 'NovaProof is the verification and validation component of the NovaFuse platform.',
    content: `
      # NovaProof
      
      NovaProof is the verification and validation component of the NovaFuse platform. It provides tools for verifying data integrity, validating compliance, and generating audit trails.
      
      ## Key Features
      
      - **Data Verification**: Verify the integrity of data
      - **Compliance Validation**: Validate compliance with regulations
      - **Audit Trails**: Generate tamper-proof audit trails
      - **Evidence Collection**: Collect and store evidence
      
      ## Getting Started
      
      1. Navigate to the NovaProof section
      2. Create a new verification task
      3. Configure the verification parameters
      4. Run the verification
      5. View the results
    `,
    keywords: ['novaproof', 'verification', 'validation', 'audit', 'compliance']
  }
});

export default helpService;
