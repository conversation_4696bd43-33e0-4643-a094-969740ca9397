"""
Unit tests for the Storage Manager.

This module contains unit tests for the Storage Manager, including tests for
versioning, encryption, access control, and audit logging.
"""

import os
import json
import uuid
import tempfile
import unittest
from unittest.mock import patch, MagicMock

from src.ucecs.core.storage_manager import (
    StorageManager,
    AccessLevel,
    EncryptionType,
    StorageOperation,
    AuditLogEntry,
    EvidenceVersion
)

class TestStorageManager(unittest.TestCase):
    """Test cases for the Storage Manager."""
    
    def setUp(self):
        """Set up the test environment."""
        # Create temporary directories for the test
        self.temp_dir = tempfile.mkdtemp()
        self.audit_log_dir = os.path.join(self.temp_dir, 'audit_logs')
        self.versions_dir = os.path.join(self.temp_dir, 'evidence_versions')
        self.access_control_dir = os.path.join(self.temp_dir, 'access_control')
        self.encryption_keys_dir = os.path.join(self.temp_dir, 'encryption_keys')
        
        # Create the directories
        os.makedirs(self.audit_log_dir, exist_ok=True)
        os.makedirs(self.versions_dir, exist_ok=True)
        os.makedirs(self.access_control_dir, exist_ok=True)
        os.makedirs(self.encryption_keys_dir, exist_ok=True)
        
        # Create a Storage Manager
        self.storage_manager = StorageManager(
            audit_log_dir=self.audit_log_dir,
            versions_dir=self.versions_dir,
            access_control_dir=self.access_control_dir,
            encryption_keys_dir=self.encryption_keys_dir,
            enable_versioning=True,
            enable_encryption=True,
            enable_access_control=True,
            enable_audit_logging=True,
            default_encryption_type=EncryptionType.AES_256,
            max_versions_per_evidence=5,
            current_user_id="admin"
        )
        
        # Create a sample evidence item
        self.evidence_id = str(uuid.uuid4())
        self.evidence = {
            'id': self.evidence_id,
            'type': 'document',
            'source': 'user_upload',
            'data': {
                'title': 'Sample Document',
                'content': 'This is a sample document for testing the Storage Manager.'
            },
            'metadata': {
                'tags': ['sample', 'document', 'test']
            }
        }
    
    def tearDown(self):
        """Clean up the test environment."""
        # Remove the temporary directories
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_store_and_retrieve(self):
        """Test storing and retrieving evidence."""
        # Store the evidence
        with patch.object(self.storage_manager.storage_providers['file_system'], 'store', return_value=f"file:///evidence/{self.evidence_id}.json"):
            storage_location = self.storage_manager.store(
                provider_id='file_system',
                evidence=self.evidence,
                user_id='admin'
            )
        
        # Check the storage location
        self.assertEqual(storage_location, f"file:///evidence/{self.evidence_id}.json")
        
        # Retrieve the evidence
        with patch.object(self.storage_manager.storage_providers['file_system'], 'retrieve', return_value=self.evidence):
            retrieved_evidence = self.storage_manager.retrieve(
                provider_id='file_system',
                evidence_id=self.evidence_id,
                user_id='admin'
            )
        
        # Check the retrieved evidence
        self.assertEqual(retrieved_evidence['id'], self.evidence_id)
        self.assertEqual(retrieved_evidence['type'], 'document')
        self.assertEqual(retrieved_evidence['data']['title'], 'Sample Document')
    
    def test_versioning(self):
        """Test evidence versioning."""
        # Store the evidence with versioning
        with patch.object(self.storage_manager.storage_providers['file_system'], 'store', return_value=f"file:///evidence/{self.evidence_id}.json"):
            storage_location = self.storage_manager.store(
                provider_id='file_system',
                evidence=self.evidence,
                user_id='admin',
                create_version=True,
                version_comment='Initial version'
            )
        
        # Check that a version was created
        self.assertIn(self.evidence_id, self.storage_manager.evidence_versions)
        self.assertEqual(len(self.storage_manager.evidence_versions[self.evidence_id]), 1)
        
        # Update the evidence
        updated_evidence = self.evidence.copy()
        updated_evidence['data']['content'] = 'This is an updated sample document.'
        
        # Store the updated evidence with versioning
        with patch.object(self.storage_manager.storage_providers['file_system'], 'store', return_value=f"file:///evidence/{self.evidence_id}.json"):
            storage_location = self.storage_manager.store(
                provider_id='file_system',
                evidence=updated_evidence,
                user_id='admin',
                create_version=True,
                version_comment='Updated content'
            )
        
        # Check that another version was created
        self.assertEqual(len(self.storage_manager.evidence_versions[self.evidence_id]), 2)
        
        # Get the evidence versions
        versions = self.storage_manager.get_evidence_versions(
            evidence_id=self.evidence_id,
            user_id='admin'
        )
        
        # Check the versions
        self.assertEqual(len(versions), 2)
        self.assertEqual(versions[0]['comment'], 'Updated content')
        self.assertEqual(versions[1]['comment'], 'Initial version')
        
        # Restore the initial version
        with patch.object(self.storage_manager.storage_providers['file_system'], 'store', return_value=f"file:///evidence/{self.evidence_id}.json"):
            storage_location = self.storage_manager.restore_evidence_version(
                evidence_id=self.evidence_id,
                version_id=versions[1]['version_id'],
                provider_id='file_system',
                user_id='admin'
            )
        
        # Check that another version was created
        self.assertEqual(len(self.storage_manager.evidence_versions[self.evidence_id]), 3)
    
    def test_encryption(self):
        """Test evidence encryption."""
        # Store the evidence with encryption
        with patch.object(self.storage_manager.storage_providers['file_system'], 'store', return_value=f"file:///evidence/{self.evidence_id}.json"):
            storage_location = self.storage_manager.store(
                provider_id='file_system',
                evidence=self.evidence,
                user_id='admin',
                encrypt=True,
                encryption_type=EncryptionType.AES_256
            )
        
        # Check that encryption keys were created
        self.assertIn(self.evidence_id, self.storage_manager.encryption_keys)
        self.assertEqual(self.storage_manager.encryption_keys[self.evidence_id]['type'], 'aes_256')
        
        # Mock the encrypted evidence
        encrypted_evidence = self.evidence.copy()
        encrypted_evidence['encrypted'] = True
        encrypted_evidence['encryption_type'] = 'aes_256'
        
        # Retrieve and decrypt the evidence
        with patch.object(self.storage_manager.storage_providers['file_system'], 'retrieve', return_value=encrypted_evidence):
            retrieved_evidence = self.storage_manager.retrieve(
                provider_id='file_system',
                evidence_id=self.evidence_id,
                user_id='admin',
                decrypt=True
            )
        
        # Check that the evidence was decrypted
        self.assertFalse(retrieved_evidence.get('encrypted', False))
    
    def test_access_control(self):
        """Test evidence access control."""
        # Store the evidence
        with patch.object(self.storage_manager.storage_providers['file_system'], 'store', return_value=f"file:///evidence/{self.evidence_id}.json"):
            storage_location = self.storage_manager.store(
                provider_id='file_system',
                evidence=self.evidence,
                user_id='admin'
            )
        
        # Check that access control was initialized
        self.assertIn(self.evidence_id, self.storage_manager.access_control)
        self.assertEqual(self.storage_manager.access_control[self.evidence_id]['admin'], AccessLevel.ADMIN)
        
        # Grant access to a user
        self.storage_manager.grant_access(
            evidence_id=self.evidence_id,
            user_id='user1',
            access_level=AccessLevel.READ,
            granter_id='admin'
        )
        
        # Check that access was granted
        self.assertIn('user1', self.storage_manager.access_control[self.evidence_id])
        self.assertEqual(self.storage_manager.access_control[self.evidence_id]['user1'], AccessLevel.READ)
        
        # Get access control information
        access_control = self.storage_manager.get_access_control(
            evidence_id=self.evidence_id,
            user_id='admin'
        )
        
        # Check the access control information
        self.assertIn('user1', access_control)
        self.assertEqual(access_control['user1'], 'READ')
        
        # Revoke access from the user
        self.storage_manager.revoke_access(
            evidence_id=self.evidence_id,
            user_id='user1',
            revoker_id='admin'
        )
        
        # Check that access was revoked
        self.assertNotIn('user1', self.storage_manager.access_control[self.evidence_id])
    
    def test_audit_logging(self):
        """Test audit logging."""
        # Store the evidence
        with patch.object(self.storage_manager.storage_providers['file_system'], 'store', return_value=f"file:///evidence/{self.evidence_id}.json"):
            storage_location = self.storage_manager.store(
                provider_id='file_system',
                evidence=self.evidence,
                user_id='admin'
            )
        
        # Check that audit logs were created
        self.assertIn(self.evidence_id, self.storage_manager.audit_logs)
        self.assertEqual(len(self.storage_manager.audit_logs[self.evidence_id]), 1)
        self.assertEqual(self.storage_manager.audit_logs[self.evidence_id][0].operation, StorageOperation.STORE)
        
        # Retrieve the evidence
        with patch.object(self.storage_manager.storage_providers['file_system'], 'retrieve', return_value=self.evidence):
            retrieved_evidence = self.storage_manager.retrieve(
                provider_id='file_system',
                evidence_id=self.evidence_id,
                user_id='admin'
            )
        
        # Check that another audit log entry was created
        self.assertEqual(len(self.storage_manager.audit_logs[self.evidence_id]), 2)
        self.assertEqual(self.storage_manager.audit_logs[self.evidence_id][1].operation, StorageOperation.RETRIEVE)
        
        # Get the audit logs
        audit_logs = self.storage_manager.get_audit_logs(
            evidence_id=self.evidence_id,
            user_id='admin'
        )
        
        # Check the audit logs
        self.assertEqual(len(audit_logs), 2)
        self.assertEqual(audit_logs[0]['operation'], 'store')
        self.assertEqual(audit_logs[1]['operation'], 'retrieve')
    
    def test_delete(self):
        """Test deleting evidence."""
        # Store the evidence
        with patch.object(self.storage_manager.storage_providers['file_system'], 'store', return_value=f"file:///evidence/{self.evidence_id}.json"):
            storage_location = self.storage_manager.store(
                provider_id='file_system',
                evidence=self.evidence,
                user_id='admin',
                create_version=True,
                encrypt=True
            )
        
        # Delete the evidence
        with patch.object(self.storage_manager.storage_providers['file_system'], 'delete'):
            self.storage_manager.delete(
                provider_id='file_system',
                evidence_id=self.evidence_id,
                user_id='admin',
                delete_versions=True,
                delete_audit_logs=False
            )
        
        # Check that versions and encryption keys were deleted
        self.assertNotIn(self.evidence_id, self.storage_manager.evidence_versions)
        self.assertNotIn(self.evidence_id, self.storage_manager.encryption_keys)
        self.assertNotIn(self.evidence_id, self.storage_manager.access_control)
        
        # Check that audit logs were not deleted
        self.assertIn(self.evidence_id, self.storage_manager.audit_logs)
        self.assertEqual(len(self.storage_manager.audit_logs[self.evidence_id]), 2)
        self.assertEqual(self.storage_manager.audit_logs[self.evidence_id][1].operation, StorageOperation.DELETE)

if __name__ == '__main__':
    unittest.main()
