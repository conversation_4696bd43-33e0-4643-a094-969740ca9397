"""
Visualization Engine for the Universal Compliance Visualization Framework.

This module provides the main engine for generating role-based compliance visualizations.
"""

import logging
from typing import Dict, List, Any, Optional

from .role_manager import <PERSON>Manager
from .template_manager import TemplateManager
from .data_transformer import DataTransformer

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VisualizationEngine:
    """
    Main engine for generating role-based compliance visualizations.
    
    This class orchestrates the process of transforming compliance data into
    visualizations tailored to specific stakeholder roles.
    """
    
    def __init__(self):
        """Initialize the Visualization Engine."""
        logger.info("Initializing Visualization Engine")
        
        # Initialize the role manager
        self.role_manager = RoleManager()
        
        # Initialize the template manager
        self.template_manager = TemplateManager()
        
        # Initialize the data transformer
        self.data_transformer = DataTransformer()
        
        logger.info("Visualization Engine initialized")
    
    def generate_visualization(self, 
                              data: Dict[str, Any], 
                              role: str, 
                              visualization_type: str,
                              context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate a visualization based on the provided data, role, and visualization type.
        
        Args:
            data: The compliance data to visualize
            role: The stakeholder role (e.g., 'board', 'ciso', 'compliance_manager')
            visualization_type: The type of visualization to generate
            context: Additional context for the visualization
            
        Returns:
            A dictionary containing the visualization data
        """
        logger.info(f"Generating {visualization_type} visualization for {role} role")
        
        # Get the role configuration
        role_config = self.role_manager.get_role_config(role)
        
        # Get the appropriate template
        template = self.template_manager.get_template(role, visualization_type)
        
        # Transform the data based on the role and template
        transformed_data = self.data_transformer.transform_data(data, role_config, template)
        
        # Apply the template to the transformed data
        visualization = template.apply(transformed_data, context)
        
        logger.info(f"Visualization generated successfully for {role} role")
        
        return visualization
    
    def get_available_visualizations(self, role: str) -> List[str]:
        """
        Get the list of available visualizations for a specific role.
        
        Args:
            role: The stakeholder role
            
        Returns:
            A list of available visualization types
        """
        return self.template_manager.get_available_templates(role)
    
    def get_available_roles(self) -> List[str]:
        """
        Get the list of available stakeholder roles.
        
        Returns:
            A list of available roles
        """
        return self.role_manager.get_available_roles()
    
    def register_custom_template(self, role: str, template_name: str, template: Any) -> None:
        """
        Register a custom visualization template.
        
        Args:
            role: The stakeholder role
            template_name: The name of the template
            template: The template object
        """
        self.template_manager.register_template(role, template_name, template)
        
    def register_custom_role(self, role: str, config: Dict[str, Any]) -> None:
        """
        Register a custom stakeholder role.
        
        Args:
            role: The stakeholder role
            config: The role configuration
        """
        self.role_manager.register_role(role, config)
