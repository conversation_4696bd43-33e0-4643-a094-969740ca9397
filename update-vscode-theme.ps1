$settingsPath = "$env:APPDATA\Code\User\settings.json"
Write-Host "Settings path: $settingsPath"

if (Test-Path $settingsPath) {
    Write-Host "Settings file found. Updating..."
    
    try {
        $settings = Get-Content $settingsPath -Raw | ConvertFrom-Json
        
        # Create workbench.colorCustomizations if it doesn't exist
        if (-not ($settings.PSObject.Properties.Name -contains 'workbench.colorCustomizations')) {
            $settings | Add-Member -Type NoteProperty -Name 'workbench.colorCustomizations' -Value @{}
        }
        
        # Update color settings
        $colorCustomizations = $settings.'workbench.colorCustomizations'
        
        # Convert to PSObject if it's not already
        if ($null -eq $colorCustomizations) {
            $colorCustomizations = New-Object PSObject
            $settings.'workbench.colorCustomizations' = $colorCustomizations
        }
        
        # Set the darkest blue theme colors
        $colorCustomizations | Add-Member -Type NoteProperty -Name 'activityBar.background' -Value '#0D1B2A' -Force
        $colorCustomizations | Add-Member -Type NoteProperty -Name 'activityBar.foreground' -Value '#ffffff' -Force
        $colorCustomizations | Add-Member -Type NoteProperty -Name 'titleBar.activeBackground' -Value '#0D1B2A' -Force
        $colorCustomizations | Add-Member -Type NoteProperty -Name 'titleBar.activeForeground' -Value '#ffffff' -Force
        $colorCustomizations | Add-Member -Type NoteProperty -Name 'statusBar.background' -Value '#0D1B2A' -Force
        $colorCustomizations | Add-Member -Type NoteProperty -Name 'statusBar.foreground' -Value '#ffffff' -Force
        
        # Save the updated settings
        $settings | ConvertTo-Json -Depth 10 | Set-Content $settingsPath
        
        Write-Host "Settings updated successfully with dark blue theme (#0D1B2A)."
    }
    catch {
        Write-Host "Error updating settings: $_"
    }
}
else {
    Write-Host "Settings file not found at $settingsPath"
}
