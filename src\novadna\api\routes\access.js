/**
 * Access API Routes
 * 
 * This module provides API routes for emergency access to medical profiles.
 */

const express = require('express');
const router = express.Router();
const { validateAccessRequest, validateOverrideRequest } = require('../middleware/validation');
const { authenticateService } = require('../middleware/auth');

/**
 * @route   POST /api/access/emergency
 * @desc    Access a profile in an emergency
 * @access  Private (Service)
 */
router.post('/emergency', authenticateService, validateAccessRequest, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { formFactorId, accessCode, context } = req.body;
    
    // Add service information to context
    const accessContext = {
      ...context,
      serviceId: req.service.id,
      responderType: req.service.type
    };
    
    // Access the profile
    const result = await novaDNA.accessEmergencyProfile(formFactorId, accessCode, accessContext);
    
    res.json({
      status: 'success',
      data: {
        profile: result,
        accessedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/access/override
 * @desc    Emergency override access to a profile
 * @access  Private (Service)
 */
router.post('/override', authenticateService, validateOverrideRequest, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { profileId, reason, emergencyType, severityLevel } = req.body;
    
    // Create override request
    const overrideRequest = {
      serviceId: req.service.id,
      userId: req.user ? req.user.id : undefined,
      reason,
      emergencyType,
      severityLevel,
      location: req.body.location,
      deviceInfo: req.body.deviceInfo
    };
    
    // Access the profile with override
    const result = await novaDNA.emergencyOverrideAccess(profileId, overrideRequest);
    
    res.json({
      status: 'success',
      data: {
        profile: result.profile,
        override: {
          overrideId: result.override.overrideId,
          expiresAt: result.override.expiresAt,
          status: result.override.status
        },
        accessedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/access/override/:overrideId/complete
 * @desc    Complete an emergency override
 * @access  Private (Service)
 */
router.post('/override/:overrideId/complete', authenticateService, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { overrideId } = req.params;
    const { token, result } = req.body;
    
    if (!token) {
      return res.status(400).json({
        status: 'error',
        error: 'Override token is required'
      });
    }
    
    // Complete the override
    const completionResult = novaDNA.breakGlassProtocol.completeOverride(overrideId, token, result);
    
    if (!completionResult.success) {
      return res.status(400).json({
        status: 'error',
        error: completionResult.error
      });
    }
    
    res.json({
      status: 'success',
      data: {
        overrideId,
        status: completionResult.status,
        completedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/access/override/:overrideId/terminate
 * @desc    Terminate an emergency override
 * @access  Private (Service)
 */
router.post('/override/:overrideId/terminate', authenticateService, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { overrideId } = req.params;
    const { token, reason } = req.body;
    
    if (!token) {
      return res.status(400).json({
        status: 'error',
        error: 'Override token is required'
      });
    }
    
    // Terminate the override
    const terminationResult = novaDNA.breakGlassProtocol.terminateOverride(overrideId, token, reason);
    
    if (!terminationResult.success) {
      return res.status(400).json({
        status: 'error',
        error: terminationResult.error
      });
    }
    
    res.json({
      status: 'success',
      data: {
        overrideId,
        status: terminationResult.status,
        terminatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/access/override/:overrideId/review
 * @desc    Review an emergency override
 * @access  Private (Admin)
 */
router.post('/override/:overrideId/review', authenticateService, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { overrideId } = req.params;
    const { status, notes } = req.body;
    
    // Create review
    const review = {
      reviewerId: req.user ? req.user.id : req.service.id,
      reviewerName: req.user ? req.user.name : req.service.name,
      status,
      notes
    };
    
    // Review the override
    const reviewResult = novaDNA.breakGlassProtocol.reviewOverride(overrideId, review);
    
    if (!reviewResult.success) {
      return res.status(400).json({
        status: 'error',
        error: reviewResult.error
      });
    }
    
    res.json({
      status: 'success',
      data: {
        overrideId,
        reviewId: reviewResult.reviewId,
        status: reviewResult.status,
        reviewedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/access/override/:overrideId
 * @desc    Get override details
 * @access  Private (Service)
 */
router.get('/override/:overrideId', authenticateService, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { overrideId } = req.params;
    
    // Get override details
    const details = novaDNA.breakGlassProtocol.getOverrideDetails(overrideId);
    
    if (!details.found) {
      return res.status(404).json({
        status: 'error',
        error: details.error
      });
    }
    
    res.json({
      status: 'success',
      data: details.override
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/access/override-logs
 * @desc    Get override logs
 * @access  Private (Admin)
 */
router.get('/override-logs', authenticateService, async (req, res, next) => {
  try {
    const { novaDNA } = req;
    const { overrideId, serviceId, userId, action, status, startDate, endDate, limit } = req.query;
    
    // Get override logs
    const logs = novaDNA.breakGlassProtocol.getOverrideLogs({
      overrideId,
      serviceId,
      userId,
      action,
      status,
      startDate,
      endDate,
      limit: limit ? parseInt(limit, 10) : undefined
    });
    
    res.json({
      status: 'success',
      data: logs
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
