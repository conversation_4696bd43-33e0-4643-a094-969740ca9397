/**
 * PasswordStrengthMeter Component
 * 
 * A component for displaying password strength.
 */

import React from 'react';
import PropTypes from 'prop-types';
import { useSecurity } from '../security/SecurityContext';
import { useTheme } from '../theme/ThemeContext';
import { useI18n } from '../i18n/I18nContext';

/**
 * PasswordStrengthMeter component
 * 
 * @param {Object} props - Component props
 * @param {string} props.password - Password to check
 * @param {boolean} [props.showFeedback=true] - Whether to show feedback
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} PasswordStrengthMeter component
 */
const PasswordStrengthMeter = ({
  password,
  showFeedback = true,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const { translate } = useI18n();
  const { checkPasswordStrength } = useSecurity();
  
  // Check password strength
  const strengthCheck = checkPasswordStrength(password);
  const { score, feedback } = strengthCheck;
  
  // Get strength label
  const getStrengthLabel = () => {
    switch (score) {
      case 0:
        return translate('security.veryWeak', 'Very Weak');
      case 1:
        return translate('security.weak', 'Weak');
      case 2:
        return translate('security.fair', 'Fair');
      case 3:
        return translate('security.good', 'Good');
      case 4:
      case 5:
        return translate('security.strong', 'Strong');
      default:
        return translate('security.unknown', 'Unknown');
    }
  };
  
  // Get strength color
  const getStrengthColor = () => {
    switch (score) {
      case 0:
        return 'bg-error';
      case 1:
        return 'bg-warning';
      case 2:
        return 'bg-warning';
      case 3:
        return 'bg-success';
      case 4:
      case 5:
        return 'bg-success';
      default:
        return 'bg-divider';
    }
  };
  
  // Get strength width
  const getStrengthWidth = () => {
    switch (score) {
      case 0:
        return 'w-1/5';
      case 1:
        return 'w-2/5';
      case 2:
        return 'w-3/5';
      case 3:
        return 'w-4/5';
      case 4:
      case 5:
        return 'w-full';
      default:
        return 'w-0';
    }
  };
  
  return (
    <div
      className={`${className}`}
      style={style}
      data-testid="password-strength-meter"
    >
      <div className="space-y-2">
        {/* Strength meter */}
        <div className="h-1 w-full bg-background rounded-full overflow-hidden">
          <div
            className={`h-full ${getStrengthColor()} transition-all duration-300 ease-in-out ${getStrengthWidth()}`}
          ></div>
        </div>
        
        {/* Strength label */}
        <div className="flex justify-between items-center">
          <div className="text-xs text-textSecondary">
            {translate('security.passwordStrength', 'Password Strength')}
          </div>
          
          <div className={`text-xs font-medium ${
            score <= 1 ? 'text-error' : score <= 2 ? 'text-warning' : 'text-success'
          }`}>
            {getStrengthLabel()}
          </div>
        </div>
        
        {/* Feedback */}
        {showFeedback && password && (
          <div className="space-y-1 mt-1">
            {feedback.warning && (
              <div className="text-xs text-error">
                {feedback.warning}
              </div>
            )}
            
            {feedback.suggestions && feedback.suggestions.length > 0 && (
              <ul className="text-xs text-textSecondary space-y-1 list-disc list-inside">
                {feedback.suggestions.map((suggestion, index) => (
                  <li key={index}>{suggestion}</li>
                ))}
              </ul>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

PasswordStrengthMeter.propTypes = {
  password: PropTypes.string.isRequired,
  showFeedback: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object
};

export default PasswordStrengthMeter;
