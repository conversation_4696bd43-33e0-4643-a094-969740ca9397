const express = require('express');
const { validateRequest } = require('./validation');
const controllers = require('./controllers');

const router = express.Router();

/**
 * @swagger
 * /governance/esg/reports:
 *   get:
 *     summary: Get a list of ESG reports
 *     description: Returns a paginated list of ESG reports with optional filtering
 *     tags: [ESG Reports]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: reportType
 *         in: query
 *         description: Filter by report type
 *         schema:
 *           type: string
 *           enum: [annual, quarterly, sustainability, impact, custom]
 *       - name: status
 *         in: query
 *         description: Filter by report status
 *         schema:
 *           type: string
 *           enum: [draft, in-review, published, archived]
 *       - name: framework
 *         in: query
 *         description: Filter by ESG framework
 *         schema:
 *           type: string
 *       - name: startDate
 *         in: query
 *         description: Filter by reporting period start date (format YYYY-MM-DD)
 *         schema:
 *           type: string
 *           format: date
 *       - name: endDate
 *         in: query
 *         description: Filter by reporting period end date (format YYYY-MM-DD)
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ESGReport'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/', controllers.getReports);

/**
 * @swagger
 * /governance/esg/reports/{id}:
 *   get:
 *     summary: Get a specific ESG report
 *     description: Returns a specific ESG report by ID
 *     tags: [ESG Reports]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG report ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGReport'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/:id', controllers.getReportById);

/**
 * @swagger
 * /governance/esg/reports:
 *   post:
 *     summary: Create a new ESG report
 *     description: Creates a new ESG report
 *     tags: [ESG Reports]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               reportType:
 *                 type: string
 *                 enum: [annual, quarterly, sustainability, impact, custom]
 *               reportingPeriod:
 *                 type: object
 *                 properties:
 *                   startDate:
 *                     type: string
 *                     format: date
 *                   endDate:
 *                     type: string
 *                     format: date
 *               frameworks:
 *                 type: array
 *                 items:
 *                   type: string
 *               metrics:
 *                 type: array
 *                 items:
 *                   type: string
 *               sections:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     title:
 *                       type: string
 *                     content:
 *                       type: string
 *                     order:
 *                       type: integer
 *               attachments:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     fileType:
 *                       type: string
 *                     url:
 *                       type: string
 *             required:
 *               - title
 *               - reportType
 *               - reportingPeriod
 *     responses:
 *       201:
 *         description: Report created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGReport'
 *                 message:
 *                   type: string
 *                   example: ESG report created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/', validateRequest('createReport'), controllers.createReport);

/**
 * @swagger
 * /governance/esg/reports/{id}:
 *   put:
 *     summary: Update an ESG report
 *     description: Updates an existing ESG report
 *     tags: [ESG Reports]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG report ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               reportType:
 *                 type: string
 *                 enum: [annual, quarterly, sustainability, impact, custom]
 *               reportingPeriod:
 *                 type: object
 *                 properties:
 *                   startDate:
 *                     type: string
 *                     format: date
 *                   endDate:
 *                     type: string
 *                     format: date
 *               status:
 *                 type: string
 *                 enum: [draft, in-review, published, archived]
 *               frameworks:
 *                 type: array
 *                 items:
 *                   type: string
 *               metrics:
 *                 type: array
 *                 items:
 *                   type: string
 *               sections:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     title:
 *                       type: string
 *                     content:
 *                       type: string
 *                     order:
 *                       type: integer
 *               attachments:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     fileType:
 *                       type: string
 *                     url:
 *                       type: string
 *     responses:
 *       200:
 *         description: Report updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGReport'
 *                 message:
 *                   type: string
 *                   example: ESG report updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/:id', validateRequest('updateReport'), controllers.updateReport);

/**
 * @swagger
 * /governance/esg/reports/{id}:
 *   delete:
 *     summary: Delete an ESG report
 *     description: Deletes an existing ESG report
 *     tags: [ESG Reports]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG report ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Report deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: ESG report deleted successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/:id', controllers.deleteReport);

/**
 * @swagger
 * /governance/esg/reports/{id}/publish:
 *   post:
 *     summary: Publish an ESG report
 *     description: Changes the status of an ESG report to published
 *     tags: [ESG Reports]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG report ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               publishedAt:
 *                 type: string
 *                 format: date-time
 *                 description: Optional date and time when the report should be considered published
 *     responses:
 *       200:
 *         description: Report published successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGReport'
 *                 message:
 *                   type: string
 *                   example: ESG report published successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/:id/publish', validateRequest('publishReport'), controllers.publishReport);

/**
 * @swagger
 * /governance/esg/reports/templates:
 *   get:
 *     summary: Get a list of report templates
 *     description: Returns a paginated list of report templates with optional filtering
 *     tags: [ESG Reports]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: reportType
 *         in: query
 *         description: Filter by report type
 *         schema:
 *           type: string
 *           enum: [annual, quarterly, sustainability, impact, custom]
 *       - name: framework
 *         in: query
 *         description: Filter by ESG framework
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ReportTemplate'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/templates', controllers.getTemplates);

/**
 * @swagger
 * /governance/esg/reports/templates/{id}:
 *   get:
 *     summary: Get a specific report template
 *     description: Returns a specific report template by ID
 *     tags: [ESG Reports]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Report template ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ReportTemplate'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/templates/:id', controllers.getTemplateById);

/**
 * @swagger
 * /governance/esg/reports/templates:
 *   post:
 *     summary: Create a new report template
 *     description: Creates a new report template
 *     tags: [ESG Reports]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               reportType:
 *                 type: string
 *                 enum: [annual, quarterly, sustainability, impact, custom]
 *               frameworks:
 *                 type: array
 *                 items:
 *                   type: string
 *               structure:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     title:
 *                       type: string
 *                     description:
 *                       type: string
 *                     order:
 *                       type: integer
 *                     metricCategories:
 *                       type: array
 *                       items:
 *                         type: string
 *               isDefault:
 *                 type: boolean
 *             required:
 *               - name
 *               - reportType
 *               - structure
 *     responses:
 *       201:
 *         description: Template created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ReportTemplate'
 *                 message:
 *                   type: string
 *                   example: Report template created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/templates', validateRequest('createTemplate'), controllers.createTemplate);

/**
 * @swagger
 * /governance/esg/reports/templates/{id}:
 *   put:
 *     summary: Update a report template
 *     description: Updates an existing report template
 *     tags: [ESG Reports]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Report template ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               reportType:
 *                 type: string
 *                 enum: [annual, quarterly, sustainability, impact, custom]
 *               frameworks:
 *                 type: array
 *                 items:
 *                   type: string
 *               structure:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     title:
 *                       type: string
 *                     description:
 *                       type: string
 *                     order:
 *                       type: integer
 *                     metricCategories:
 *                       type: array
 *                       items:
 *                         type: string
 *               isDefault:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Template updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ReportTemplate'
 *                 message:
 *                   type: string
 *                   example: Report template updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/templates/:id', validateRequest('updateTemplate'), controllers.updateTemplate);

/**
 * @swagger
 * /governance/esg/reports/templates/{id}:
 *   delete:
 *     summary: Delete a report template
 *     description: Deletes an existing report template
 *     tags: [ESG Reports]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Report template ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Template deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Report template deleted successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/templates/:id', controllers.deleteTemplate);

/**
 * @swagger
 * /governance/esg/reports/templates/{templateId}/create-report:
 *   post:
 *     summary: Create a report from a template
 *     description: Creates a new ESG report based on a template
 *     tags: [ESG Reports]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: templateId
 *         in: path
 *         description: Report template ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 description: Optional title for the new report
 *               reportingPeriod:
 *                 type: object
 *                 properties:
 *                   startDate:
 *                     type: string
 *                     format: date
 *                   endDate:
 *                     type: string
 *                     format: date
 *                 description: Optional reporting period for the new report
 *     responses:
 *       201:
 *         description: Report created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGReport'
 *                 message:
 *                   type: string
 *                   example: ESG report created from template successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/templates/:templateId/create-report', controllers.createReportFromTemplate);

module.exports = router;
