import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  Typography, 
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';

/**
 * RiskHeatmap Component
 * 
 * Displays a heatmap visualization of risk intensity by category over time.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.data - Heatmap data object
 * @param {string[]} props.data.timePoints - Array of time points (e.g., months)
 * @param {string[]} props.data.categories - Array of risk categories
 * @param {number[][]} props.data.intensityValues - 2D array of intensity values
 * @param {Object} props.data.complianceImpact - Mapping of categories to affected frameworks
 */
const RiskHeatmap = ({ data }) => {
  const [selectedTimePoint, setSelectedTimePoint] = useState(0);
  const [hoveredCell, setHoveredCell] = useState(null);

  // Default data if none provided
  const defaultData = {
    timePoints: ["<PERSON>", "Feb", "Mar", "Apr", "May", "Jun"],
    categories: ["Data Security", "Network Security", "Data Protection", "Security Operations", "Application Security"],
    intensityValues: [
      [0.8, 0.7, 0.6, 0.5, 0.3, 0.2],
      [0.5, 0.6, 0.7, 0.4, 0.3, 0.2],
      [0.3, 0.4, 0.6, 0.7, 0.5, 0.3],
      [0.4, 0.5, 0.3, 0.2, 0.1, 0.1],
      [0.6, 0.5, 0.4, 0.5, 0.6, 0.4]
    ],
    complianceImpact: {
      "Data Security": ["GDPR", "HIPAA", "PCI-DSS"],
      "Network Security": ["PCI-DSS", "SOC2", "HIPAA"],
      "Data Protection": ["GDPR", "PCI-DSS", "HIPAA"],
      "Security Operations": ["SOC2", "PCI-DSS", "HIPAA"],
      "Application Security": ["GDPR", "PCI-DSS", "SOC2"]
    }
  };

  // Use provided data or default
  const heatmapData = data || defaultData;

  // Get color for cell based on intensity value
  const getCellColor = (value) => {
    // Red gradient from light to dark
    const r = Math.floor(255);
    const g = Math.floor(255 * (1 - value));
    const b = Math.floor(255 * (1 - value));
    return `rgb(${r}, ${g}, ${b})`;
  };

  // Get text color based on background color intensity
  const getTextColor = (value) => {
    return value > 0.5 ? 'white' : 'black';
  };

  // Handle time point change
  const handleTimePointChange = (event) => {
    setSelectedTimePoint(event.target.value);
  };

  // Handle cell hover
  const handleCellHover = (categoryIndex) => {
    setHoveredCell(categoryIndex);
  };

  // Handle cell hover exit
  const handleCellExit = () => {
    setHoveredCell(null);
  };

  return (
    <Card sx={{ minWidth: 275 }}>
      <CardHeader
        title="Risk Heatmap"
        subheader="Risk intensity by category over time"
        action={
          <FormControl sx={{ minWidth: 120 }} size="small">
            <InputLabel id="time-point-select-label">Time</InputLabel>
            <Select
              labelId="time-point-select-label"
              id="time-point-select"
              value={selectedTimePoint}
              label="Time"
              onChange={handleTimePointChange}
            >
              {heatmapData.timePoints.map((timePoint, index) => (
                <MenuItem key={timePoint} value={index}>{timePoint}</MenuItem>
              ))}
            </Select>
          </FormControl>
        }
      />
      <CardContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Risk intensity for {heatmapData.timePoints[selectedTimePoint]}
          </Typography>
          
          {/* Heatmap */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            {heatmapData.categories.map((category, categoryIndex) => {
              const value = heatmapData.intensityValues[categoryIndex][selectedTimePoint];
              const isHovered = hoveredCell === categoryIndex;
              
              return (
                <Box 
                  key={category}
                  sx={{ 
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    p: 1,
                    borderRadius: 1,
                    backgroundColor: getCellColor(value),
                    color: getTextColor(value),
                    transition: 'all 0.2s',
                    transform: isHovered ? 'scale(1.02)' : 'scale(1)',
                    boxShadow: isHovered ? 2 : 0
                  }}
                  onMouseEnter={() => handleCellHover(categoryIndex)}
                  onMouseLeave={handleCellExit}
                >
                  <Typography variant="body2" fontWeight="medium">
                    {category}
                  </Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {Math.round(value * 100)}%
                  </Typography>
                </Box>
              );
            })}
          </Box>
        </Box>
        
        {/* Compliance Impact */}
        {hoveredCell !== null && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              Compliance Impact
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {heatmapData.complianceImpact[heatmapData.categories[hoveredCell]].map((framework) => (
                <Box 
                  key={framework}
                  sx={{ 
                    px: 1, 
                    py: 0.5, 
                    borderRadius: 1, 
                    backgroundColor: 'primary.main',
                    color: 'white',
                    fontSize: '0.75rem',
                    fontWeight: 'bold'
                  }}
                >
                  {framework}
                </Box>
              ))}
            </Box>
          </Box>
        )}
        
        {/* Legend */}
        <Box sx={{ mt: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="caption" color="text.secondary">
            Low Risk
          </Typography>
          <Box sx={{ 
            width: '60%', 
            height: 10, 
            borderRadius: 5,
            background: 'linear-gradient(to right, #ffebee, #ffcdd2, #ef9a9a, #e57373, #ef5350, #f44336)'
          }} />
          <Typography variant="caption" color="text.secondary">
            High Risk
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default RiskHeatmap;
