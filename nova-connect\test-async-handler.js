/**
 * Simple test script for the async handler utility
 */

try {
  console.log('Loading async handler utility...');
  const { asyncHandler, retryWithBackoff, circuitBreaker } = require('./src/utils/async-handler');

  console.log('Testing async handler utility...');

  // Mock Express objects
  const req = {};
  const res = {};
  const next = (error) => {
    console.log(`Next called with error: ${error ? error.message : 'none'}`);
    return error;
  };

  // Test asyncHandler with success
  console.log('\n--- Testing asyncHandler with success ---');
  const successHandler = asyncHandler(async (req, res) => {
    console.log('Handler executed successfully');
    return 'success';
  });

  successHandler(req, res, next);
  console.log('Success handler completed');

  // Test asyncHandler with error
  console.log('\n--- Testing asyncHandler with error ---');
  const errorHandler = asyncHandler(async (req, res) => {
    console.log('<PERSON><PERSON> throwing error');
    throw new Error('Test error');
  });

  errorHandler(req, res, next);
  console.log('Error handler completed');

  // Test retryWithBackoff
  console.log('\n--- Testing retryWithBackoff ---');

  // Success on first try
  const successFn = async () => {
    console.log('Function succeeded on first try');
    return 'success';
  };

  retryWithBackoff(successFn, { maxRetries: 3, initialDelay: 100 })
    .then(result => {
      console.log(`retryWithBackoff success result: ${result}`);
    })
    .catch(error => {
      console.error('retryWithBackoff should not fail:', error);
    });

  // Success after retry
  let attempts = 0;
  const retryFn = async () => {
    attempts++;
    console.log(`Attempt ${attempts}`);
    if (attempts < 2) {
      throw new Error('Temporary error');
    }
    return 'success after retry';
  };

  // We'll use setTimeout to avoid blocking
  setTimeout(() => {
    retryWithBackoff(retryFn, { maxRetries: 3, initialDelay: 100 })
      .then(result => {
        console.log(`retryWithBackoff retry result: ${result}`);
      })
      .catch(error => {
        console.error('retryWithBackoff retry should not fail:', error);
      });
  }, 500);

  // Test circuitBreaker
  console.log('\n--- Testing circuitBreaker ---');

  // Success case
  const successCircuitFn = async () => {
    console.log('Circuit function succeeded');
    return 'circuit success';
  };

  const protectedSuccessFn = circuitBreaker(successCircuitFn, { failureThreshold: 2 });

  protectedSuccessFn()
    .then(result => {
      console.log(`circuitBreaker success result: ${result}`);
    })
    .catch(error => {
      console.error('circuitBreaker should not fail:', error);
    });

  // Failure case that opens circuit
  let circuitAttempts = 0;
  const failingCircuitFn = async () => {
    circuitAttempts++;
    console.log(`Circuit attempt ${circuitAttempts}`);
    throw new Error('Circuit error');
  };

  const protectedFailingFn = circuitBreaker(failingCircuitFn, { failureThreshold: 2 });

  // First attempt
  setTimeout(() => {
    protectedFailingFn()
      .then(result => {
        console.error('First circuit call should fail');
      })
      .catch(error => {
        console.log(`First circuit error: ${error.message}`);
      });
  }, 1000);

  // Second attempt - should open circuit
  setTimeout(() => {
    protectedFailingFn()
      .then(result => {
        console.error('Second circuit call should fail');
      })
      .catch(error => {
        console.log(`Second circuit error: ${error.message}`);
      });
  }, 1500);

  // Third attempt - circuit should be open
  setTimeout(() => {
    protectedFailingFn()
      .then(result => {
        console.error('Third circuit call should fail with circuit open');
      })
      .catch(error => {
        console.log(`Third circuit error: ${error.message}`);
      });
  }, 2000);

  // Wait for all tests to complete
  setTimeout(() => {
    console.log('\nAll async handler tests completed!');
  }, 3000);
} catch (error) {
  console.error('Test failed:', error);
}
