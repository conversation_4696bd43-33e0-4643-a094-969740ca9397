/**
 * NovaFuse Universal API Connector - Connector Registry Model
 * 
 * This module defines the data model for the connector registry.
 */

const { v4: uuidv4 } = require('uuid');
const { createLogger } = require('../../utils/logger');

const logger = createLogger('connector-registry');

/**
 * Connector status enum
 */
const ConnectorStatus = {
  DRAFT: 'draft',
  TESTING: 'testing',
  PUBLISHED: 'published',
  DEPRECATED: 'deprecated',
  RETIRED: 'retired'
};

/**
 * Connector type enum
 */
const ConnectorType = {
  SOURCE: 'source',
  DESTINATION: 'destination',
  TRANSFORMATION: 'transformation'
};

/**
 * Connector category enum
 */
const ConnectorCategory = {
  AUTHENTICATION: 'authentication',
  CLOUD_STORAGE: 'cloud_storage',
  CRM: 'crm',
  DATABASE: 'database',
  EMAIL: 'email',
  ERP: 'erp',
  FILE: 'file',
  GOVERNANCE: 'governance',
  IDENTITY: 'identity',
  MESSAGING: 'messaging',
  MONITORING: 'monitoring',
  PAYMENT: 'payment',
  SECURITY: 'security',
  TICKETING: 'ticketing',
  UTILITY: 'utility',
  OTHER: 'other'
};

/**
 * Connector model class
 */
class Connector {
  /**
   * Create a new connector
   * 
   * @param {Object} data - The connector data
   */
  constructor(data = {}) {
    this.id = data.id || uuidv4();
    this.name = data.name || '';
    this.description = data.description || '';
    this.version = data.version || '1.0.0';
    this.type = data.type || ConnectorType.SOURCE;
    this.category = data.category || ConnectorCategory.OTHER;
    this.status = data.status || ConnectorStatus.DRAFT;
    this.author = data.author || '';
    this.organization = data.organization || 'NovaFuse';
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = data.updatedAt || new Date().toISOString();
    this.publishedAt = data.publishedAt || null;
    this.deprecatedAt = data.deprecatedAt || null;
    this.retiredAt = data.retiredAt || null;
    this.tags = data.tags || [];
    this.icon = data.icon || null;
    this.documentationUrl = data.documentationUrl || null;
    this.sourceCodeUrl = data.sourceCodeUrl || null;
    this.supportUrl = data.supportUrl || null;
    this.license = data.license || 'NovaFuse License';
    this.dependencies = data.dependencies || [];
    this.configSchema = data.configSchema || { type: 'object', properties: {} };
    this.authType = data.authType || 'none';
    this.capabilities = data.capabilities || [];
    this.rateLimit = data.rateLimit || null;
    this.metrics = data.metrics || {
      usageCount: 0,
      successRate: 0,
      averageResponseTime: 0,
      lastUsed: null
    };
    this.settings = data.settings || {};
  }

  /**
   * Validate the connector
   * 
   * @returns {Object} - Validation result
   */
  validate() {
    const errors = [];

    // Required fields
    if (!this.name) {
      errors.push('Name is required');
    }

    if (!this.description) {
      errors.push('Description is required');
    }

    if (!this.version) {
      errors.push('Version is required');
    }

    // Enum validations
    if (!Object.values(ConnectorType).includes(this.type)) {
      errors.push(`Type must be one of: ${Object.values(ConnectorType).join(', ')}`);
    }

    if (!Object.values(ConnectorCategory).includes(this.category)) {
      errors.push(`Category must be one of: ${Object.values(ConnectorCategory).join(', ')}`);
    }

    if (!Object.values(ConnectorStatus).includes(this.status)) {
      errors.push(`Status must be one of: ${Object.values(ConnectorStatus).join(', ')}`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Update the connector
   * 
   * @param {Object} data - The data to update
   * @returns {Connector} - The updated connector
   */
  update(data) {
    // Fields that cannot be updated directly
    const protectedFields = ['id', 'createdAt', 'publishedAt', 'deprecatedAt', 'retiredAt', 'metrics'];

    // Update fields
    Object.keys(data).forEach(key => {
      if (!protectedFields.includes(key)) {
        this[key] = data[key];
      }
    });

    // Update updatedAt
    this.updatedAt = new Date().toISOString();

    return this;
  }

  /**
   * Publish the connector
   * 
   * @returns {Connector} - The published connector
   */
  publish() {
    // Validate before publishing
    const validation = this.validate();
    if (!validation.valid) {
      throw new Error(`Cannot publish connector: ${validation.errors.join(', ')}`);
    }

    this.status = ConnectorStatus.PUBLISHED;
    this.publishedAt = new Date().toISOString();
    this.updatedAt = new Date().toISOString();

    logger.info(`Connector ${this.name} (${this.id}) published at version ${this.version}`);

    return this;
  }

  /**
   * Deprecate the connector
   * 
   * @param {string} reason - The reason for deprecation
   * @returns {Connector} - The deprecated connector
   */
  deprecate(reason) {
    this.status = ConnectorStatus.DEPRECATED;
    this.deprecatedAt = new Date().toISOString();
    this.updatedAt = new Date().toISOString();
    this.settings.deprecationReason = reason || 'No reason provided';

    logger.info(`Connector ${this.name} (${this.id}) deprecated: ${reason}`);

    return this;
  }

  /**
   * Retire the connector
   * 
   * @param {string} reason - The reason for retirement
   * @returns {Connector} - The retired connector
   */
  retire(reason) {
    this.status = ConnectorStatus.RETIRED;
    this.retiredAt = new Date().toISOString();
    this.updatedAt = new Date().toISOString();
    this.settings.retirementReason = reason || 'No reason provided';

    logger.info(`Connector ${this.name} (${this.id}) retired: ${reason}`);

    return this;
  }

  /**
   * Create a new version of the connector
   * 
   * @param {string} version - The new version
   * @returns {Connector} - The new connector version
   */
  createNewVersion(version) {
    const newConnector = new Connector({
      ...this,
      id: uuidv4(),
      version,
      status: ConnectorStatus.DRAFT,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      publishedAt: null,
      deprecatedAt: null,
      retiredAt: null,
      metrics: {
        usageCount: 0,
        successRate: 0,
        averageResponseTime: 0,
        lastUsed: null
      }
    });

    logger.info(`New version ${version} created for connector ${this.name}`);

    return newConnector;
  }

  /**
   * Track connector usage
   * 
   * @param {boolean} success - Whether the usage was successful
   * @param {number} responseTime - The response time in milliseconds
   */
  trackUsage(success, responseTime) {
    // Update metrics
    this.metrics.usageCount += 1;
    this.metrics.lastUsed = new Date().toISOString();

    // Update success rate
    const previousSuccesses = this.metrics.successRate * (this.metrics.usageCount - 1);
    const newSuccesses = previousSuccesses + (success ? 1 : 0);
    this.metrics.successRate = newSuccesses / this.metrics.usageCount;

    // Update average response time
    const previousTotal = this.metrics.averageResponseTime * (this.metrics.usageCount - 1);
    const newTotal = previousTotal + responseTime;
    this.metrics.averageResponseTime = newTotal / this.metrics.usageCount;

    logger.debug(`Usage tracked for connector ${this.name} (${this.id}): success=${success}, responseTime=${responseTime}ms`);
  }

  /**
   * Convert to JSON
   * 
   * @returns {Object} - The JSON representation
   */
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      version: this.version,
      type: this.type,
      category: this.category,
      status: this.status,
      author: this.author,
      organization: this.organization,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      publishedAt: this.publishedAt,
      deprecatedAt: this.deprecatedAt,
      retiredAt: this.retiredAt,
      tags: this.tags,
      icon: this.icon,
      documentationUrl: this.documentationUrl,
      sourceCodeUrl: this.sourceCodeUrl,
      supportUrl: this.supportUrl,
      license: this.license,
      dependencies: this.dependencies,
      configSchema: this.configSchema,
      authType: this.authType,
      capabilities: this.capabilities,
      rateLimit: this.rateLimit,
      metrics: this.metrics,
      settings: this.settings
    };
  }
}

module.exports = {
  Connector,
  ConnectorStatus,
  ConnectorType,
  ConnectorCategory
};
