/**
 * Privacy Patterns
 * Defines patterns for identifying different types of personal data
 */

/**
 * Privacy pattern definitions for identifying personal data
 */
class PrivacyPatterns {
  constructor() {
    // Base patterns that apply globally
    this.basePatterns = [
      {
        id: 'email',
        name: 'Email Address',
        category: 'contact',
        description: 'Email address pattern',
        regex: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}\b/,
        strictRegex: /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/,
        baseConfidence: 0.9,
        sensitivity: 'medium'
      },
      {
        id: 'phone',
        name: 'Phone Number',
        category: 'contact',
        description: 'Generic phone number pattern',
        regex: /\b(\+\d{1,3}[\s.-]?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}\b/,
        strictRegex: null, // Varies by region
        baseConfidence: 0.7,
        sensitivity: 'medium'
      },
      {
        id: 'ip_address',
        name: 'IP Address',
        category: 'technical',
        description: 'IPv4 or IPv6 address',
        regex: /\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b|([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)/,
        strictRegex: null,
        baseConfidence: 0.9,
        sensitivity: 'low'
      },
      {
        id: 'credit_card',
        name: 'Credit Card Number',
        category: 'financial',
        description: 'Credit card number pattern with Luhn algorithm validation',
        regex: /\b(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|6(?:011|5[0-9]{2})[0-9]{12}|(?:2131|1800|35\d{3})\d{11})\b/,
        strictRegex: null, // Requires Luhn algorithm check
        baseConfidence: 0.8,
        sensitivity: 'high',
        validate: this._validateCreditCard
      },
      {
        id: 'name',
        name: 'Person Name',
        category: 'identity',
        description: 'Common name patterns',
        regex: /\b[A-Z][a-z]+(?: [A-Z][a-z]+)+\b/,
        strictRegex: null,
        baseConfidence: 0.6, // Lower confidence due to potential false positives
        sensitivity: 'medium'
      },
      {
        id: 'address',
        name: 'Postal Address',
        category: 'contact',
        description: 'Common address patterns',
        regex: /\b\d+\s+[A-Za-z\s,]+(?:Avenue|Lane|Road|Boulevard|Drive|Street|Ave|Dr|Rd|Blvd|Ln|St)\.?\s+(?:[A-Za-z]+\s*,\s*)?[A-Z]{2}\s+\d{5}(?:-\d{4})?\b/i,
        strictRegex: null,
        baseConfidence: 0.7,
        sensitivity: 'medium'
      },
      {
        id: 'dob',
        name: 'Date of Birth',
        category: 'identity',
        description: 'Date of birth in common formats',
        regex: /\b(?:\d{1,2}[-/]\d{1,2}[-/]\d{2,4}|\d{4}[-/]\d{1,2}[-/]\d{1,2})\b/,
        strictRegex: null,
        baseConfidence: 0.6, // Lower confidence as could be any date
        sensitivity: 'high'
      }
    ];

    // Region-specific patterns
    this.regionalPatterns = {
      'gdpr': [
        {
          id: 'eu_national_id',
          name: 'EU National ID',
          category: 'government_id',
          description: 'Various EU national ID formats',
          regex: /\b[A-Z]{2}[0-9]{6}[A-Z0-9]{1,3}\b/,
          strictRegex: null,
          baseConfidence: 0.7,
          sensitivity: 'high'
        },
        {
          id: 'eu_vat',
          name: 'EU VAT Number',
          category: 'financial',
          description: 'EU VAT number format',
          regex: /\b[A-Z]{2}[0-9]{8,12}\b/,
          strictRegex: null,
          baseConfidence: 0.8,
          sensitivity: 'medium'
        }
      ],
      'ccpa': [
        {
          id: 'ssn',
          name: 'Social Security Number',
          category: 'government_id',
          description: 'US Social Security Number',
          regex: /\b[0-9]{3}[-]?[0-9]{2}[-]?[0-9]{4}\b/,
          strictRegex: /^[0-9]{3}[-]?[0-9]{2}[-]?[0-9]{4}$/,
          baseConfidence: 0.9,
          sensitivity: 'high'
        },
        {
          id: 'drivers_license',
          name: 'US Drivers License',
          category: 'government_id',
          description: 'US Drivers License formats',
          regex: /\b[A-Z][0-9]{7}\b|\b[A-Z]{1,2}[0-9]{6,7}\b/,
          strictRegex: null,
          baseConfidence: 0.7,
          sensitivity: 'high'
        }
      ],
      'hipaa': [
        {
          id: 'medical_record',
          name: 'Medical Record Number',
          category: 'health',
          description: 'Medical record number formats',
          regex: /\bMR[N]?#?\s*[0-9]{6,10}\b/i,
          strictRegex: null,
          baseConfidence: 0.8,
          sensitivity: 'high'
        },
        {
          id: 'health_insurance',
          name: 'Health Insurance Number',
          category: 'health',
          description: 'Health insurance ID formats',
          regex: /\b[A-Z]{3}[0-9]{9,12}\b|\b[A-Z][0-9]{2}-[0-9]{2}-[0-9]{4}\b/,
          strictRegex: null,
          baseConfidence: 0.7,
          sensitivity: 'high'
        }
      ]
    };
  }

  /**
   * Get patterns for a specific region/regulation
   * @param {string} region - Region identifier (e.g., 'gdpr', 'ccpa', 'hipaa')
   * @returns {Array} Combined list of base and region-specific patterns
   */
  getRegionalPatterns(region) {
    // Start with base patterns
    const patterns = [...this.basePatterns];
    
    // Add region-specific patterns if available
    if (region && this.regionalPatterns[region.toLowerCase()]) {
      patterns.push(...this.regionalPatterns[region.toLowerCase()]);
    }
    
    // Compile all regex patterns for efficiency
    return patterns.map(pattern => ({
      ...pattern,
      regex: new RegExp(pattern.regex),
      strictRegex: pattern.strictRegex ? new RegExp(pattern.strictRegex) : null
    }));
  }

  /**
   * Get all available patterns
   * @returns {Array} All patterns from all regions
   */
  getAllPatterns() {
    const patterns = [...this.basePatterns];
    
    // Add all region-specific patterns
    Object.values(this.regionalPatterns).forEach(regionPatterns => {
      patterns.push(...regionPatterns);
    });
    
    // Compile all regex patterns for efficiency
    return patterns.map(pattern => ({
      ...pattern,
      regex: new RegExp(pattern.regex),
      strictRegex: pattern.strictRegex ? new RegExp(pattern.strictRegex) : null
    }));
  }

  /**
   * Add a custom pattern
   * @param {Object} pattern - Pattern definition
   * @param {string} region - Region to add the pattern to (optional)
   */
  addPattern(pattern, region = null) {
    if (!pattern.id || !pattern.regex) {
      throw new Error('Pattern must have at least id and regex properties');
    }
    
    // Ensure the pattern has all required properties
    const fullPattern = {
      ...pattern,
      name: pattern.name || pattern.id,
      category: pattern.category || 'custom',
      description: pattern.description || '',
      baseConfidence: pattern.baseConfidence || 0.7,
      sensitivity: pattern.sensitivity || 'medium'
    };
    
    if (region && this.regionalPatterns[region.toLowerCase()]) {
      this.regionalPatterns[region.toLowerCase()].push(fullPattern);
    } else {
      this.basePatterns.push(fullPattern);
    }
  }

  /**
   * Validate a credit card number using the Luhn algorithm
   * @private
   * @param {string} value - Credit card number to validate
   * @returns {boolean} True if valid
   */
  _validateCreditCard(value) {
    // Remove non-digit characters
    const digits = value.replace(/\D/g, '');
    
    // Check if length is valid (13-19 digits)
    if (digits.length < 13 || digits.length > 19) {
      return false;
    }
    
    // Luhn algorithm implementation
    let sum = 0;
    let double = false;
    
    // Process from right to left
    for (let i = digits.length - 1; i >= 0; i--) {
      let digit = parseInt(digits.charAt(i), 10);
      
      // Double every second digit
      if (double) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }
      
      sum += digit;
      double = !double;
    }
    
    // Valid if sum is divisible by 10
    return sum % 10 === 0;
  }
}

module.exports = new PrivacyPatterns();
