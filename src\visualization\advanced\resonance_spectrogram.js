/**
 * Resonance Spectrogram Visualization
 * 
 * This module provides a real-time spectrogram visualization for resonance frequencies
 * using HTML5 Canvas. It visualizes frequency components, quantum silence states,
 * and harmonic patterns.
 */

/**
 * ResonanceSpectrogram class
 */
class ResonanceSpectrogram {
  /**
   * Constructor
   * @param {HTMLElement} container - Container element
   * @param {Object} options - Configuration options
   */
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      width: container.clientWidth,
      height: container.clientHeight,
      backgroundColor: '#111133',
      foregroundColor: '#ffffff',
      targetFrequency: 396, // Hz - the "OM Tone"
      targetFrequencyColor: '#ff0000',
      quantumSilenceColor: '#00ff00',
      historyLength: 100,
      minFrequency: 0,
      maxFrequency: 1000,
      minAmplitude: 0,
      maxAmplitude: 1,
      showTargetLine: true,
      showHarmonics: true,
      showWaterfall: true,
      waterfallAlpha: 0.05,
      updateInterval: 50, // ms
      ...options
    };
    
    // Initialize state
    this.state = {
      isInitialized: false,
      isRunning: false,
      updateInterval: null,
      history: [],
      currentFrequency: this.options.targetFrequency,
      currentAmplitude: 0,
      isQuantumSilence: false,
      harmonics: [],
      frequencyData: new Array(this.options.historyLength).fill(0),
      amplitudeData: new Array(this.options.historyLength).fill(0),
      silenceData: new Array(this.options.historyLength).fill(false),
      waterfallData: []
    };
    
    // Initialize canvas
    this.initCanvas();
    
    // Initialize event listeners
    this.initEventListeners();
    
    // Mark as initialized
    this.state.isInitialized = true;
  }
  
  /**
   * Initialize canvas
   */
  initCanvas() {
    // Create main canvas
    this.canvas = document.createElement('canvas');
    this.canvas.width = this.options.width;
    this.canvas.height = this.options.height;
    this.canvas.style.display = 'block';
    this.canvas.style.backgroundColor = this.options.backgroundColor;
    
    this.ctx = this.canvas.getContext('2d');
    
    // Create waterfall canvas if enabled
    if (this.options.showWaterfall) {
      this.waterfallCanvas = document.createElement('canvas');
      this.waterfallCanvas.width = this.options.width;
      this.waterfallCanvas.height = this.options.height;
      
      this.waterfallCtx = this.waterfallCanvas.getContext('2d');
      this.waterfallCtx.fillStyle = this.options.backgroundColor;
      this.waterfallCtx.fillRect(0, 0, this.options.width, this.options.height);
    }
    
    // Add canvas to container
    this.container.appendChild(this.canvas);
  }
  
  /**
   * Initialize event listeners
   */
  initEventListeners() {
    // Handle window resize
    window.addEventListener('resize', this.onWindowResize.bind(this));
    
    // Handle container resize
    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        if (entry.target === this.container) {
          this.onContainerResize();
        }
      }
    });
    
    resizeObserver.observe(this.container);
  }
  
  /**
   * Handle window resize
   */
  onWindowResize() {
    this.onContainerResize();
  }
  
  /**
   * Handle container resize
   */
  onContainerResize() {
    const width = this.container.clientWidth;
    const height = this.container.clientHeight;
    
    this.options.width = width;
    this.options.height = height;
    
    this.canvas.width = width;
    this.canvas.height = height;
    
    if (this.options.showWaterfall) {
      this.waterfallCanvas.width = width;
      this.waterfallCanvas.height = height;
      
      // Clear waterfall canvas
      this.waterfallCtx.fillStyle = this.options.backgroundColor;
      this.waterfallCtx.fillRect(0, 0, width, height);
    }
    
    // Redraw
    this.draw();
  }
  
  /**
   * Start visualization
   */
  start() {
    if (this.state.isRunning) {
      return;
    }
    
    this.state.isRunning = true;
    
    // Set up update interval
    this.state.updateInterval = setInterval(() => {
      this.draw();
    }, this.options.updateInterval);
  }
  
  /**
   * Stop visualization
   */
  stop() {
    if (!this.state.isRunning) {
      return;
    }
    
    this.state.isRunning = false;
    
    // Clear update interval
    if (this.state.updateInterval) {
      clearInterval(this.state.updateInterval);
      this.state.updateInterval = null;
    }
  }
  
  /**
   * Update with new data
   * @param {Object} data - Resonance data
   */
  update(data) {
    // Extract data
    const {
      frequency = this.state.currentFrequency,
      amplitude = this.state.currentAmplitude,
      isQuantumSilence = this.state.isQuantumSilence,
      harmonics = this.state.harmonics
    } = data;
    
    // Update state
    this.state.currentFrequency = frequency;
    this.state.currentAmplitude = amplitude;
    this.state.isQuantumSilence = isQuantumSilence;
    this.state.harmonics = harmonics;
    
    // Update history
    this.updateHistory(frequency, amplitude, isQuantumSilence);
    
    // Draw if running
    if (this.state.isRunning) {
      this.draw();
    }
  }
  
  /**
   * Update history
   * @param {number} frequency - Frequency
   * @param {number} amplitude - Amplitude
   * @param {boolean} isQuantumSilence - Whether quantum silence is detected
   */
  updateHistory(frequency, amplitude, isQuantumSilence) {
    // Update frequency data
    this.state.frequencyData.push(frequency);
    this.state.frequencyData.shift();
    
    // Update amplitude data
    this.state.amplitudeData.push(amplitude);
    this.state.amplitudeData.shift();
    
    // Update silence data
    this.state.silenceData.push(isQuantumSilence);
    this.state.silenceData.shift();
    
    // Update waterfall data if enabled
    if (this.options.showWaterfall) {
      // Generate frequency spectrum
      const spectrum = this.generateSpectrum(frequency, amplitude);
      
      // Add to waterfall data
      this.state.waterfallData.push(spectrum);
      
      // Limit waterfall data length
      if (this.state.waterfallData.length > this.options.height) {
        this.state.waterfallData.shift();
      }
    }
  }
  
  /**
   * Generate frequency spectrum
   * @param {number} frequency - Center frequency
   * @param {number} amplitude - Amplitude
   * @returns {Array} Spectrum data
   */
  generateSpectrum(frequency, amplitude) {
    const { width, minFrequency, maxFrequency } = this.options;
    
    // Create spectrum array
    const spectrum = new Array(width).fill(0);
    
    // Calculate frequency range per pixel
    const freqRange = maxFrequency - minFrequency;
    const freqPerPixel = freqRange / width;
    
    // Generate Gaussian distribution around center frequency
    const sigma = 5; // Width of the Gaussian
    
    for (let i = 0; i < width; i++) {
      const pixelFreq = minFrequency + i * freqPerPixel;
      const distance = Math.abs(pixelFreq - frequency);
      
      // Gaussian function
      spectrum[i] = amplitude * Math.exp(-(distance * distance) / (2 * sigma * sigma));
    }
    
    // Add harmonics if enabled
    if (this.options.showHarmonics && this.state.harmonics.length > 0) {
      for (const harmonic of this.state.harmonics) {
        const harmonicFreq = harmonic.frequency;
        const harmonicAmp = harmonic.amplitude;
        
        for (let i = 0; i < width; i++) {
          const pixelFreq = minFrequency + i * freqPerPixel;
          const distance = Math.abs(pixelFreq - harmonicFreq);
          
          // Gaussian function for harmonic
          spectrum[i] += harmonicAmp * Math.exp(-(distance * distance) / (2 * sigma * sigma));
        }
      }
    }
    
    return spectrum;
  }
  
  /**
   * Draw visualization
   */
  draw() {
    const { width, height, backgroundColor, foregroundColor } = this.options;
    
    // Clear canvas
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(0, 0, width, height);
    
    // Draw waterfall if enabled
    if (this.options.showWaterfall) {
      this.drawWaterfall();
    }
    
    // Draw frequency line
    this.drawFrequencyLine();
    
    // Draw target frequency line if enabled
    if (this.options.showTargetLine) {
      this.drawTargetLine();
    }
    
    // Draw harmonics if enabled
    if (this.options.showHarmonics) {
      this.drawHarmonics();
    }
    
    // Draw current frequency and amplitude
    this.drawCurrentValues();
  }
  
  /**
   * Draw waterfall
   */
  drawWaterfall() {
    const { width, height, waterfallAlpha } = this.options;
    
    // Shift waterfall down
    this.waterfallCtx.globalAlpha = 1;
    this.waterfallCtx.drawImage(this.waterfallCanvas, 0, 0, width, height - 1, 0, 1, width, height - 1);
    
    // Draw new spectrum at the top
    if (this.state.waterfallData.length > 0) {
      const spectrum = this.state.waterfallData[this.state.waterfallData.length - 1];
      
      for (let i = 0; i < width; i++) {
        const value = spectrum[i];
        
        // Calculate color based on value
        const r = Math.floor(value * 255);
        const g = Math.floor(value * 128);
        const b = Math.floor(value * 255);
        
        this.waterfallCtx.fillStyle = `rgb(${r}, ${g}, ${b})`;
        this.waterfallCtx.fillRect(i, 0, 1, 1);
      }
    }
    
    // Draw waterfall on main canvas
    this.ctx.globalAlpha = waterfallAlpha;
    this.ctx.drawImage(this.waterfallCanvas, 0, 0);
    this.ctx.globalAlpha = 1;
  }
  
  /**
   * Draw frequency line
   */
  drawFrequencyLine() {
    const { width, height, foregroundColor, minFrequency, maxFrequency, minAmplitude, maxAmplitude } = this.options;
    
    // Set line style
    this.ctx.strokeStyle = foregroundColor;
    this.ctx.lineWidth = 2;
    this.ctx.beginPath();
    
    // Draw frequency line
    for (let i = 0; i < this.state.frequencyData.length; i++) {
      const x = (i / (this.state.frequencyData.length - 1)) * width;
      const frequency = this.state.frequencyData[i];
      const amplitude = this.state.amplitudeData[i];
      const isQuantumSilence = this.state.silenceData[i];
      
      // Normalize frequency and amplitude
      const normalizedFreq = (frequency - minFrequency) / (maxFrequency - minFrequency);
      const normalizedAmp = (amplitude - minAmplitude) / (maxAmplitude - minAmplitude);
      
      // Calculate y position (inverted because canvas y increases downward)
      const y = height - normalizedFreq * height;
      
      // Set color based on quantum silence
      if (isQuantumSilence) {
        this.ctx.strokeStyle = this.options.quantumSilenceColor;
      } else {
        this.ctx.strokeStyle = foregroundColor;
      }
      
      // Draw line segment
      if (i === 0) {
        this.ctx.moveTo(x, y);
      } else {
        this.ctx.lineTo(x, y);
      }
    }
    
    this.ctx.stroke();
  }
  
  /**
   * Draw target frequency line
   */
  drawTargetLine() {
    const { width, height, targetFrequency, targetFrequencyColor, minFrequency, maxFrequency } = this.options;
    
    // Normalize target frequency
    const normalizedTarget = (targetFrequency - minFrequency) / (maxFrequency - minFrequency);
    
    // Calculate y position
    const y = height - normalizedTarget * height;
    
    // Draw target line
    this.ctx.strokeStyle = targetFrequencyColor;
    this.ctx.lineWidth = 1;
    this.ctx.setLineDash([5, 5]);
    this.ctx.beginPath();
    this.ctx.moveTo(0, y);
    this.ctx.lineTo(width, y);
    this.ctx.stroke();
    this.ctx.setLineDash([]);
    
    // Draw target label
    this.ctx.fillStyle = targetFrequencyColor;
    this.ctx.font = '12px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'bottom';
    this.ctx.fillText(`${targetFrequency} Hz`, 5, y - 5);
  }
  
  /**
   * Draw harmonics
   */
  drawHarmonics() {
    const { width, height, minFrequency, maxFrequency } = this.options;
    
    // Draw each harmonic
    for (const harmonic of this.state.harmonics) {
      const frequency = harmonic.frequency;
      const amplitude = harmonic.amplitude;
      const ratio = harmonic.ratio || 1;
      
      // Normalize frequency
      const normalizedFreq = (frequency - minFrequency) / (maxFrequency - minFrequency);
      
      // Calculate y position
      const y = height - normalizedFreq * height;
      
      // Calculate color based on ratio
      const hue = (ratio * 360) % 360;
      const color = `hsl(${hue}, 80%, 50%)`;
      
      // Draw harmonic line
      this.ctx.strokeStyle = color;
      this.ctx.lineWidth = 1;
      this.ctx.setLineDash([2, 2]);
      this.ctx.beginPath();
      this.ctx.moveTo(0, y);
      this.ctx.lineTo(width, y);
      this.ctx.stroke();
      this.ctx.setLineDash([]);
      
      // Draw harmonic label
      this.ctx.fillStyle = color;
      this.ctx.font = '10px Arial';
      this.ctx.textAlign = 'right';
      this.ctx.textBaseline = 'bottom';
      this.ctx.fillText(`${frequency.toFixed(2)} Hz (${ratio.toFixed(2)}x)`, width - 5, y - 2);
    }
  }
  
  /**
   * Draw current values
   */
  drawCurrentValues() {
    const { width, height, foregroundColor, quantumSilenceColor } = this.options;
    
    // Set text style
    this.ctx.font = '14px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'top';
    
    // Draw current frequency
    this.ctx.fillStyle = foregroundColor;
    this.ctx.fillText(`Frequency: ${this.state.currentFrequency.toFixed(2)} Hz`, 10, 10);
    
    // Draw current amplitude
    this.ctx.fillText(`Amplitude: ${this.state.currentAmplitude.toFixed(4)}`, 10, 30);
    
    // Draw quantum silence status
    this.ctx.fillStyle = this.state.isQuantumSilence ? quantumSilenceColor : foregroundColor;
    this.ctx.fillText(`Quantum Silence: ${this.state.isQuantumSilence ? 'Yes' : 'No'}`, 10, 50);
    
    // Draw harmonics count
    this.ctx.fillStyle = foregroundColor;
    this.ctx.fillText(`Harmonics: ${this.state.harmonics.length}`, 10, 70);
  }
  
  /**
   * Dispose
   */
  dispose() {
    // Stop visualization
    this.stop();
    
    // Remove event listeners
    window.removeEventListener('resize', this.onWindowResize);
    
    // Remove canvas
    if (this.canvas.parentNode) {
      this.canvas.parentNode.removeChild(this.canvas);
    }
  }
}

// Export
if (typeof module !== 'undefined') {
  module.exports = { ResonanceSpectrogram };
}
