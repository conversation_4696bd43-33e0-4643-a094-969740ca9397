/**
 * Error Handler Middleware
 * 
 * This middleware catches errors thrown in route handlers and
 * returns appropriate error responses.
 */

const errorHandler = (err, req, res, next) => {
  // Log the error for debugging
  console.error('Error:', err);
  
  // Default error status and message
  let status = 500;
  let message = 'Internal Server Error';
  let errorType = 'ServerError';
  
  // Handle specific error types
  if (err.name === 'ValidationError') {
    status = 400;
    message = err.message || 'Validation failed';
    errorType = 'ValidationError';
  } else if (err.name === 'UnauthorizedError') {
    status = 401;
    message = err.message || 'Unauthorized';
    errorType = 'UnauthorizedError';
  } else if (err.name === 'ForbiddenError') {
    status = 403;
    message = err.message || 'Forbidden';
    errorType = 'ForbiddenError';
  } else if (err.name === 'NotFoundError') {
    status = 404;
    message = err.message || 'Resource not found';
    errorType = 'NotFoundError';
  } else if (err.name === 'ConflictError') {
    status = 409;
    message = err.message || 'Conflict';
    errorType = 'ConflictError';
  } else if (err.name === 'RateLimitError') {
    status = 429;
    message = err.message || 'Too many requests';
    errorType = 'RateLimitError';
  }
  
  // Return the error response
  res.status(status).json({
    error: errorType,
    message: message
  });
};

module.exports = errorHandler;
