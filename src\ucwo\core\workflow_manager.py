"""
Workflow Manager for the Universal Compliance Workflow Orchestrator.

This module provides functionality for managing workflow definitions.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WorkflowManager:
    """
    Manager for workflow definitions.
    
    This class is responsible for loading, validating, and providing access to
    workflow definitions.
    """
    
    def __init__(self, workflows_dir: Optional[str] = None):
        """
        Initialize the Workflow Manager.
        
        Args:
            workflows_dir: Path to a directory containing workflow definitions
        """
        logger.info("Initializing Workflow Manager")
        
        # Initialize the workflows dictionary
        self.workflows: Dict[str, Dict[str, Any]] = {}
        
        # Load default workflows
        self._load_default_workflows()
        
        # Load custom workflows if provided
        if workflows_dir and os.path.exists(workflows_dir):
            self._load_workflows_from_directory(workflows_dir)
        
        logger.info(f"Workflow Manager initialized with {len(self.workflows)} workflows")
    
    def _load_default_workflows(self) -> None:
        """Load the default workflow definitions."""
        # GDPR Data Subject Request workflow
        self.workflows['gdpr_dsr'] = {
            'id': 'gdpr_dsr',
            'name': 'GDPR Data Subject Request',
            'description': 'Workflow for handling GDPR Data Subject Requests',
            'version': '1.0.0',
            'start_step': 'validate_request',
            'steps': {
                'validate_request': {
                    'id': 'validate_request',
                    'name': 'Validate Request',
                    'description': 'Validate the data subject request',
                    'type': 'task',
                    'task_id': 'validate_dsr',
                    'next_step': 'identify_data_subject'
                },
                'identify_data_subject': {
                    'id': 'identify_data_subject',
                    'name': 'Identify Data Subject',
                    'description': 'Identify the data subject',
                    'type': 'task',
                    'task_id': 'identify_data_subject',
                    'next_step': 'check_request_type'
                },
                'check_request_type': {
                    'id': 'check_request_type',
                    'name': 'Check Request Type',
                    'description': 'Check the type of data subject request',
                    'type': 'decision',
                    'condition': {
                        'type': 'equals',
                        'left': {'type': 'output', 'key': 'request_type'},
                        'right': {'type': 'literal', 'value': 'access'}
                    },
                    'true_step': 'process_access_request',
                    'false_step': 'check_erasure_request'
                },
                'check_erasure_request': {
                    'id': 'check_erasure_request',
                    'name': 'Check Erasure Request',
                    'description': 'Check if the request is an erasure request',
                    'type': 'decision',
                    'condition': {
                        'type': 'equals',
                        'left': {'type': 'output', 'key': 'request_type'},
                        'right': {'type': 'literal', 'value': 'erasure'}
                    },
                    'true_step': 'process_erasure_request',
                    'false_step': 'process_other_request'
                },
                'process_access_request': {
                    'id': 'process_access_request',
                    'name': 'Process Access Request',
                    'description': 'Process a data access request',
                    'type': 'task',
                    'task_id': 'process_access_request',
                    'next_step': 'prepare_response'
                },
                'process_erasure_request': {
                    'id': 'process_erasure_request',
                    'name': 'Process Erasure Request',
                    'description': 'Process a data erasure request',
                    'type': 'task',
                    'task_id': 'process_erasure_request',
                    'next_step': 'prepare_response'
                },
                'process_other_request': {
                    'id': 'process_other_request',
                    'name': 'Process Other Request',
                    'description': 'Process other types of data subject requests',
                    'type': 'task',
                    'task_id': 'process_other_request',
                    'next_step': 'prepare_response'
                },
                'prepare_response': {
                    'id': 'prepare_response',
                    'name': 'Prepare Response',
                    'description': 'Prepare a response to the data subject',
                    'type': 'task',
                    'task_id': 'prepare_dsr_response',
                    'next_step': 'send_response'
                },
                'send_response': {
                    'id': 'send_response',
                    'name': 'Send Response',
                    'description': 'Send the response to the data subject',
                    'type': 'task',
                    'task_id': 'send_dsr_response',
                    'next_step': 'end'
                },
                'end': {
                    'id': 'end',
                    'name': 'End',
                    'description': 'End the workflow',
                    'type': 'end'
                }
            }
        }
        
        # Incident Response workflow
        self.workflows['incident_response'] = {
            'id': 'incident_response',
            'name': 'Incident Response',
            'description': 'Workflow for handling security incidents',
            'version': '1.0.0',
            'start_step': 'detect_incident',
            'steps': {
                'detect_incident': {
                    'id': 'detect_incident',
                    'name': 'Detect Incident',
                    'description': 'Detect and log a security incident',
                    'type': 'task',
                    'task_id': 'detect_incident',
                    'next_step': 'assess_severity'
                },
                'assess_severity': {
                    'id': 'assess_severity',
                    'name': 'Assess Severity',
                    'description': 'Assess the severity of the incident',
                    'type': 'task',
                    'task_id': 'assess_incident_severity',
                    'next_step': 'check_high_severity'
                },
                'check_high_severity': {
                    'id': 'check_high_severity',
                    'name': 'Check High Severity',
                    'description': 'Check if the incident is high severity',
                    'type': 'decision',
                    'condition': {
                        'type': 'equals',
                        'left': {'type': 'output', 'key': 'severity'},
                        'right': {'type': 'literal', 'value': 'high'}
                    },
                    'true_step': 'escalate_incident',
                    'false_step': 'contain_incident'
                },
                'escalate_incident': {
                    'id': 'escalate_incident',
                    'name': 'Escalate Incident',
                    'description': 'Escalate the incident to senior management',
                    'type': 'task',
                    'task_id': 'escalate_incident',
                    'next_step': 'contain_incident'
                },
                'contain_incident': {
                    'id': 'contain_incident',
                    'name': 'Contain Incident',
                    'description': 'Contain the security incident',
                    'type': 'task',
                    'task_id': 'contain_incident',
                    'next_step': 'investigate_incident'
                },
                'investigate_incident': {
                    'id': 'investigate_incident',
                    'name': 'Investigate Incident',
                    'description': 'Investigate the security incident',
                    'type': 'task',
                    'task_id': 'investigate_incident',
                    'next_step': 'check_breach'
                },
                'check_breach': {
                    'id': 'check_breach',
                    'name': 'Check Data Breach',
                    'description': 'Check if the incident involves a data breach',
                    'type': 'decision',
                    'condition': {
                        'type': 'equals',
                        'left': {'type': 'output', 'key': 'is_data_breach'},
                        'right': {'type': 'literal', 'value': True}
                    },
                    'true_step': 'initiate_breach_notification',
                    'false_step': 'remediate_incident'
                },
                'initiate_breach_notification': {
                    'id': 'initiate_breach_notification',
                    'name': 'Initiate Breach Notification',
                    'description': 'Initiate the data breach notification process',
                    'type': 'task',
                    'task_id': 'initiate_breach_notification',
                    'next_step': 'remediate_incident'
                },
                'remediate_incident': {
                    'id': 'remediate_incident',
                    'name': 'Remediate Incident',
                    'description': 'Remediate the security incident',
                    'type': 'task',
                    'task_id': 'remediate_incident',
                    'next_step': 'document_incident'
                },
                'document_incident': {
                    'id': 'document_incident',
                    'name': 'Document Incident',
                    'description': 'Document the security incident',
                    'type': 'task',
                    'task_id': 'document_incident',
                    'next_step': 'end'
                },
                'end': {
                    'id': 'end',
                    'name': 'End',
                    'description': 'End the workflow',
                    'type': 'end'
                }
            }
        }
        
        # Vendor Assessment workflow
        self.workflows['vendor_assessment'] = {
            'id': 'vendor_assessment',
            'name': 'Vendor Assessment',
            'description': 'Workflow for assessing vendor compliance',
            'version': '1.0.0',
            'start_step': 'initiate_assessment',
            'steps': {
                'initiate_assessment': {
                    'id': 'initiate_assessment',
                    'name': 'Initiate Assessment',
                    'description': 'Initiate a vendor assessment',
                    'type': 'task',
                    'task_id': 'initiate_vendor_assessment',
                    'next_step': 'send_questionnaire'
                },
                'send_questionnaire': {
                    'id': 'send_questionnaire',
                    'name': 'Send Questionnaire',
                    'description': 'Send a compliance questionnaire to the vendor',
                    'type': 'task',
                    'task_id': 'send_vendor_questionnaire',
                    'next_step': 'wait_for_response'
                },
                'wait_for_response': {
                    'id': 'wait_for_response',
                    'name': 'Wait for Response',
                    'description': 'Wait for the vendor to respond to the questionnaire',
                    'type': 'task',
                    'task_id': 'wait_for_vendor_response',
                    'next_step': 'check_response_received'
                },
                'check_response_received': {
                    'id': 'check_response_received',
                    'name': 'Check Response Received',
                    'description': 'Check if a response has been received from the vendor',
                    'type': 'decision',
                    'condition': {
                        'type': 'equals',
                        'left': {'type': 'output', 'key': 'response_received'},
                        'right': {'type': 'literal', 'value': True}
                    },
                    'true_step': 'review_response',
                    'false_step': 'send_reminder'
                },
                'send_reminder': {
                    'id': 'send_reminder',
                    'name': 'Send Reminder',
                    'description': 'Send a reminder to the vendor',
                    'type': 'task',
                    'task_id': 'send_vendor_reminder',
                    'next_step': 'wait_for_response'
                },
                'review_response': {
                    'id': 'review_response',
                    'name': 'Review Response',
                    'description': 'Review the vendor\'s response',
                    'type': 'task',
                    'task_id': 'review_vendor_response',
                    'next_step': 'check_additional_info_needed'
                },
                'check_additional_info_needed': {
                    'id': 'check_additional_info_needed',
                    'name': 'Check Additional Info Needed',
                    'description': 'Check if additional information is needed from the vendor',
                    'type': 'decision',
                    'condition': {
                        'type': 'equals',
                        'left': {'type': 'output', 'key': 'additional_info_needed'},
                        'right': {'type': 'literal', 'value': True}
                    },
                    'true_step': 'request_additional_info',
                    'false_step': 'assess_risk'
                },
                'request_additional_info': {
                    'id': 'request_additional_info',
                    'name': 'Request Additional Info',
                    'description': 'Request additional information from the vendor',
                    'type': 'task',
                    'task_id': 'request_additional_vendor_info',
                    'next_step': 'wait_for_additional_info'
                },
                'wait_for_additional_info': {
                    'id': 'wait_for_additional_info',
                    'name': 'Wait for Additional Info',
                    'description': 'Wait for the vendor to provide additional information',
                    'type': 'task',
                    'task_id': 'wait_for_additional_vendor_info',
                    'next_step': 'review_response'
                },
                'assess_risk': {
                    'id': 'assess_risk',
                    'name': 'Assess Risk',
                    'description': 'Assess the vendor\'s risk level',
                    'type': 'task',
                    'task_id': 'assess_vendor_risk',
                    'next_step': 'check_high_risk'
                },
                'check_high_risk': {
                    'id': 'check_high_risk',
                    'name': 'Check High Risk',
                    'description': 'Check if the vendor is high risk',
                    'type': 'decision',
                    'condition': {
                        'type': 'equals',
                        'left': {'type': 'output', 'key': 'risk_level'},
                        'right': {'type': 'literal', 'value': 'high'}
                    },
                    'true_step': 'escalate_for_review',
                    'false_step': 'generate_assessment_report'
                },
                'escalate_for_review': {
                    'id': 'escalate_for_review',
                    'name': 'Escalate for Review',
                    'description': 'Escalate the vendor assessment for senior review',
                    'type': 'task',
                    'task_id': 'escalate_vendor_assessment',
                    'next_step': 'generate_assessment_report'
                },
                'generate_assessment_report': {
                    'id': 'generate_assessment_report',
                    'name': 'Generate Assessment Report',
                    'description': 'Generate a vendor assessment report',
                    'type': 'task',
                    'task_id': 'generate_vendor_assessment_report',
                    'next_step': 'end'
                },
                'end': {
                    'id': 'end',
                    'name': 'End',
                    'description': 'End the workflow',
                    'type': 'end'
                }
            }
        }
    
    def _load_workflows_from_directory(self, directory: str) -> None:
        """
        Load workflow definitions from a directory.
        
        Args:
            directory: Path to the directory containing workflow definition files
        """
        try:
            # Get all JSON files in the directory
            workflow_files = [f for f in os.listdir(directory) if f.endswith('.json')]
            
            for workflow_file in workflow_files:
                try:
                    # Load the workflow definition
                    file_path = os.path.join(directory, workflow_file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        workflow_def = json.load(f)
                    
                    # Validate the workflow definition
                    if self._validate_workflow(workflow_def):
                        # Add the workflow definition
                        self.workflows[workflow_def['id']] = workflow_def
                        logger.info(f"Loaded workflow: {workflow_def['id']} from {file_path}")
                    else:
                        logger.warning(f"Invalid workflow definition in {file_path}")
                
                except Exception as e:
                    logger.error(f"Failed to load workflow from {workflow_file}: {e}")
            
        except Exception as e:
            logger.error(f"Failed to load workflows from directory {directory}: {e}")
    
    def _validate_workflow(self, workflow_def: Dict[str, Any]) -> bool:
        """
        Validate a workflow definition.
        
        Args:
            workflow_def: The workflow definition to validate
            
        Returns:
            True if the workflow definition is valid, False otherwise
        """
        # Check required fields
        required_fields = ['id', 'name', 'description', 'version', 'start_step', 'steps']
        for field in required_fields:
            if field not in workflow_def:
                logger.warning(f"Missing required field in workflow definition: {field}")
                return False
        
        # Check that the start step exists
        start_step = workflow_def['start_step']
        if start_step not in workflow_def['steps']:
            logger.warning(f"Start step not found in workflow steps: {start_step}")
            return False
        
        # Check that all steps have required fields
        for step_id, step_def in workflow_def['steps'].items():
            if 'id' not in step_def or step_def['id'] != step_id:
                logger.warning(f"Step ID mismatch: {step_id} != {step_def.get('id')}")
                return False
            
            if 'type' not in step_def:
                logger.warning(f"Missing step type in step: {step_id}")
                return False
            
            # Check step-specific required fields
            step_type = step_def['type']
            
            if step_type == 'task':
                if 'task_id' not in step_def:
                    logger.warning(f"Missing task_id in task step: {step_id}")
                    return False
                
                if 'next_step' in step_def and step_def['next_step'] not in workflow_def['steps']:
                    logger.warning(f"Next step not found in workflow steps: {step_def['next_step']}")
                    return False
            
            elif step_type == 'decision':
                if 'condition' not in step_def:
                    logger.warning(f"Missing condition in decision step: {step_id}")
                    return False
                
                if 'true_step' not in step_def or step_def['true_step'] not in workflow_def['steps']:
                    logger.warning(f"True step not found in workflow steps: {step_def.get('true_step')}")
                    return False
                
                if 'false_step' not in step_def or step_def['false_step'] not in workflow_def['steps']:
                    logger.warning(f"False step not found in workflow steps: {step_def.get('false_step')}")
                    return False
            
            elif step_type == 'parallel':
                if 'steps' not in step_def:
                    logger.warning(f"Missing steps in parallel step: {step_id}")
                    return False
                
                for parallel_step in step_def['steps']:
                    if parallel_step not in workflow_def['steps']:
                        logger.warning(f"Parallel step not found in workflow steps: {parallel_step}")
                        return False
                
                if 'next_step' in step_def and step_def['next_step'] not in workflow_def['steps']:
                    logger.warning(f"Next step not found in workflow steps: {step_def['next_step']}")
                    return False
            
            elif step_type == 'end':
                # No additional validation needed for end steps
                pass
            
            else:
                logger.warning(f"Unknown step type: {step_type}")
                return False
        
        return True
    
    def get_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Get a workflow definition.
        
        Args:
            workflow_id: The ID of the workflow
            
        Returns:
            The workflow definition
            
        Raises:
            ValueError: If the workflow does not exist
        """
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow not found: {workflow_id}")
        
        return self.workflows[workflow_id]
    
    def get_all_workflows(self) -> List[Dict[str, Any]]:
        """
        Get all workflow definitions.
        
        Returns:
            List of workflow definitions
        """
        return list(self.workflows.values())
    
    def register_workflow(self, workflow_def: Dict[str, Any]) -> None:
        """
        Register a new workflow definition or update an existing one.
        
        Args:
            workflow_def: The workflow definition
            
        Raises:
            ValueError: If the workflow definition is invalid
        """
        # Validate the workflow definition
        if not self._validate_workflow(workflow_def):
            raise ValueError("Invalid workflow definition")
        
        # Add or update the workflow definition
        self.workflows[workflow_def['id']] = workflow_def
        logger.info(f"Registered workflow: {workflow_def['id']}")
    
    def save_workflow(self, workflow_id: str, file_path: str) -> None:
        """
        Save a workflow definition to a file.
        
        Args:
            workflow_id: The ID of the workflow
            file_path: Path to the output file
            
        Raises:
            ValueError: If the workflow does not exist
        """
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow not found: {workflow_id}")
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.workflows[workflow_id], f, indent=2)
            
            logger.info(f"Saved workflow {workflow_id} to {file_path}")
        except Exception as e:
            logger.error(f"Failed to save workflow {workflow_id} to {file_path}: {e}")
            raise
