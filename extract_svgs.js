const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

const inputDir = 'D:/novafuse-api-superstore/consolidated_diagrams';
const outputDir = 'D:/novafuse-api-superstore/consolidated_diagrams/svgs';

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

// Process each HTML file
fs.readdirSync(inputDir)
    .filter(file => file.endsWith('.html'))
    .forEach(file => {
        const filePath = path.join(inputDir, file);
        const html = fs.readFileSync(filePath, 'utf-8');
        const dom = new JSDOM(html);
        const document = dom.window.document;
        
        // Extract all SVGs
        const svgs = document.querySelectorAll('svg');
        svgs.forEach((svg, index) => {
            const title = document.querySelector(`#diagram${index + 1} .diagram-title`)?.textContent || 
                         `diagram_${path.basename(file, '.html')}_${index + 1}`;
            const safeTitle = title.replace(/[^\w\s-]/g, '').replace(/\s+/g, '_').toLowerCase();
            const outputFile = path.join(outputDir, `${safeTitle}.svg`);
            
            // Add XML declaration and proper SVG attributes
            let svgContent = svg.outerHTML;
            if (!svgContent.includes('xmlns="http://www.w3.org/2000/svg"')) {
                svgContent = svgContent.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"');
            }
            
            const xmlHeader = '<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n';
            fs.writeFileSync(outputFile, xmlHeader + svgContent);
            console.log(`Extracted: ${outputFile}`);
        });
    });

console.log('SVG extraction complete!');
