/**
 * NovaFuse Universal API Connector - Logger
 * 
 * This module provides a logging utility.
 */

/**
 * Create a logger instance
 * 
 * @param {string} module - The module name
 * @returns {Object} - The logger instance
 */
function createLogger(module) {
  return {
    info: (message, meta = {}) => {
      console.log(`[INFO] [${module}] ${message}`, meta);
    },
    debug: (message, meta = {}) => {
      console.log(`[DEBUG] [${module}] ${message}`, meta);
    },
    warn: (message, meta = {}) => {
      console.warn(`[WARN] [${module}] ${message}`, meta);
    },
    error: (message, meta = {}) => {
      console.error(`[ERROR] [${module}] ${message}`, meta);
    }
  };
}

module.exports = {
  createLogger
};
