import React, { useState, useEffect } from 'react';
import { Box, Container, Typography, Paper, Grid, FormControl, InputLabel, Select, MenuItem, Slider, Switch, FormControlLabel, Button, CircularProgress } from '@mui/material';
import TriDomainTensorVisualization from '../TriDomainTensorVisualization';
import CyberSafetyHarmonyIndex from '../CyberSafetyHarmonyIndex';
import RiskControlFusionMap from '../RiskControlFusionMap';
import CyberSafetyResonanceSpectrogram from '../CyberSafetyResonanceSpectrogram';
import UnifiedComplianceSecurityVisualizer from '../UnifiedComplianceSecurityVisualizer';

// Mock data service to simulate API calls
import { fetchMockData, generateRandomData } from './mockDataService';

/**
 * CyberSafetyVisualizationTestHarness
 *
 * A test harness for the Cyber-Safety fusion visualizations.
 * This component allows testing the visualizations with:
 * - Mock data
 * - Random data
 * - Real data from API endpoints
 *
 * It also provides controls to adjust visualization parameters.
 */
function CyberSafetyVisualizationTestHarness() {
  // State for selected visualization
  const [selectedVisualization, setSelectedVisualization] = useState('tri_domain_tensor');

  // State for data source
  const [dataSource, setDataSource] = useState('mock');

  // State for data loading
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // State for visualization data
  const [visualizationData, setVisualizationData] = useState(null);

  // State for visualization options
  const [options, setOptions] = useState({
    showAxes: true,
    showGrid: true,
    rotationSpeed: 1,
    showLabels: true,
    highlightFusionPoints: true,
    connectionScale: 1.0,
    showTrend: true,
    showDetails: true,
    enableAnimation: true,
    showLegend: true,
    showEfficiencyIndicators: true,
    highlightGaps: true,
    showTooltips: true,
    showPredictions: true,
    showCrossDomainFlows: true,
    highlightDissonance: true,
    highlightDomains: true,
    showImpactAnalysis: true
  });

  // State for API endpoint (for real data)
  const [apiEndpoint, setApiEndpoint] = useState('/api/cyber-safety/visualizations');

  // State for random data parameters
  const [randomDataParams, setRandomDataParams] = useState({
    domainCount: 3,
    connectionCount: 5,
    minHealth: 0.3,
    maxHealth: 0.9
  });

  // Load data based on selected data source
  useEffect(() => {
    loadData();
  }, [dataSource, selectedVisualization, apiEndpoint, randomDataParams]);

  // Load data function
  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      let data;

      switch (dataSource) {
        case 'mock':
          data = await fetchMockData(selectedVisualization);
          break;

        case 'random':
          data = generateRandomData(selectedVisualization, randomDataParams);
          break;

        case 'api':
          const response = await fetch(`${apiEndpoint}?type=${selectedVisualization}`);
          if (!response.ok) {
            throw new Error(`API error: ${response.status} ${response.statusText}`);
          }
          data = await response.json();
          break;

        default:
          throw new Error(`Unknown data source: ${dataSource}`);
      }

      setVisualizationData(data);
    } catch (err) {
      console.error('Error loading data:', err);
      setError(err.message || 'Error loading data');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle option change
  const handleOptionChange = (option, value) => {
    setOptions(prevOptions => ({
      ...prevOptions,
      [option]: value
    }));
  };

  // Handle random data parameter change
  const handleRandomParamChange = (param, value) => {
    setRandomDataParams(prevParams => ({
      ...prevParams,
      [param]: value
    }));
  };

  // Render the selected visualization
  const renderVisualization = () => {
    if (isLoading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 500 }}>
          <CircularProgress />
        </Box>
      );
    }

    if (error) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 500 }}>
          <Typography color="error">{error}</Typography>
        </Box>
      );
    }

    if (!visualizationData) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 500 }}>
          <Typography>No data available. Please select a data source and visualization type.</Typography>
        </Box>
      );
    }

    switch (selectedVisualization) {
      case 'tri_domain_tensor':
        return (
          <TriDomainTensorVisualization
            domainData={visualizationData}
            options={options}
            width="100%"
            height={500}
          />
        );

      case 'cyber_safety_harmony_index':
        return (
          <CyberSafetyHarmonyIndex
            domainData={visualizationData.domainData}
            harmonyHistory={visualizationData.harmonyHistory}
            options={options}
            width="100%"
            height={500}
          />
        );

      case 'risk_control_fusion_map':
        return (
          <RiskControlFusionMap
            riskData={visualizationData.riskData}
            controlData={visualizationData.controlData}
            options={options}
            width="100%"
            height={500}
          />
        );

      case 'cyber_safety_resonance_spectrogram':
        return (
          <CyberSafetyResonanceSpectrogram
            domainData={visualizationData.domainData}
            predictionData={visualizationData.predictionData}
            options={options}
            width="100%"
            height={500}
          />
        );

      case 'unified_compliance_security_visualizer':
        return (
          <UnifiedComplianceSecurityVisualizer
            complianceData={visualizationData.complianceData}
            impactAnalysis={visualizationData.impactAnalysis}
            options={options}
            width="100%"
            height={500}
          />
        );

      default:
        return (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 500 }}>
            <Typography>Unknown visualization type: {selectedVisualization}</Typography>
          </Box>
        );
    }
  };

  // Render visualization options based on selected visualization
  const renderOptions = () => {
    const commonOptions = (
      <>
        <FormControlLabel
          control={
            <Switch
              checked={options.showLabels}
              onChange={(e) => handleOptionChange('showLabels', e.target.checked)}
            />
          }
          label="Show Labels"
        />

        <FormControlLabel
          control={
            <Switch
              checked={options.showTooltips}
              onChange={(e) => handleOptionChange('showTooltips', e.target.checked)}
            />
          }
          label="Show Tooltips"
        />
      </>
    );

    switch (selectedVisualization) {
      case 'tri_domain_tensor':
        return (
          <>
            {commonOptions}

            <FormControlLabel
              control={
                <Switch
                  checked={options.showAxes}
                  onChange={(e) => handleOptionChange('showAxes', e.target.checked)}
                />
              }
              label="Show Axes"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.showGrid}
                  onChange={(e) => handleOptionChange('showGrid', e.target.checked)}
                />
              }
              label="Show Grid"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.highlightFusionPoints}
                  onChange={(e) => handleOptionChange('highlightFusionPoints', e.target.checked)}
                />
              }
              label="Highlight Fusion Points"
            />

            <Box sx={{ width: 300, mt: 2 }}>
              <Typography gutterBottom>Rotation Speed</Typography>
              <Slider
                value={options.rotationSpeed}
                min={0}
                max={3}
                step={0.1}
                onChange={(e, value) => handleOptionChange('rotationSpeed', value)}
                valueLabelDisplay="auto"
              />
            </Box>

            <Box sx={{ width: 300, mt: 2 }}>
              <Typography gutterBottom>Connection Scale</Typography>
              <Slider
                value={options.connectionScale}
                min={0.1}
                max={3}
                step={0.1}
                onChange={(e, value) => handleOptionChange('connectionScale', value)}
                valueLabelDisplay="auto"
              />
            </Box>
          </>
        );

      case 'cyber_safety_harmony_index':
        return (
          <>
            {commonOptions}

            <FormControlLabel
              control={
                <Switch
                  checked={options.showTrend}
                  onChange={(e) => handleOptionChange('showTrend', e.target.checked)}
                />
              }
              label="Show Trend"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.showDetails}
                  onChange={(e) => handleOptionChange('showDetails', e.target.checked)}
                />
              }
              label="Show Details"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.enableAnimation}
                  onChange={(e) => handleOptionChange('enableAnimation', e.target.checked)}
                />
              }
              label="Enable Animation"
            />
          </>
        );

      case 'risk_control_fusion_map':
        return (
          <>
            {commonOptions}

            <FormControlLabel
              control={
                <Switch
                  checked={options.showLegend}
                  onChange={(e) => handleOptionChange('showLegend', e.target.checked)}
                />
              }
              label="Show Legend"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.showEfficiencyIndicators}
                  onChange={(e) => handleOptionChange('showEfficiencyIndicators', e.target.checked)}
                />
              }
              label="Show Efficiency Indicators"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.highlightGaps}
                  onChange={(e) => handleOptionChange('highlightGaps', e.target.checked)}
                />
              }
              label="Highlight Gaps"
            />
          </>
        );

      case 'cyber_safety_resonance_spectrogram':
        return (
          <>
            {commonOptions}

            <FormControlLabel
              control={
                <Switch
                  checked={options.showAxes}
                  onChange={(e) => handleOptionChange('showAxes', e.target.checked)}
                />
              }
              label="Show Axes"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.showGrid}
                  onChange={(e) => handleOptionChange('showGrid', e.target.checked)}
                />
              }
              label="Show Grid"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.showPredictions}
                  onChange={(e) => handleOptionChange('showPredictions', e.target.checked)}
                />
              }
              label="Show Predictions"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.showCrossDomainFlows}
                  onChange={(e) => handleOptionChange('showCrossDomainFlows', e.target.checked)}
                />
              }
              label="Show Cross-Domain Flows"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.highlightDissonance}
                  onChange={(e) => handleOptionChange('highlightDissonance', e.target.checked)}
                />
              }
              label="Highlight Dissonance"
            />

            <Box sx={{ width: 300, mt: 2 }}>
              <Typography gutterBottom>Rotation Speed</Typography>
              <Slider
                value={options.rotationSpeed}
                min={0}
                max={3}
                step={0.1}
                onChange={(e, value) => handleOptionChange('rotationSpeed', value)}
                valueLabelDisplay="auto"
              />
            </Box>
          </>
        );

      case 'unified_compliance_security_visualizer':
        return (
          <>
            {commonOptions}

            <FormControlLabel
              control={
                <Switch
                  checked={options.showLegend}
                  onChange={(e) => handleOptionChange('showLegend', e.target.checked)}
                />
              }
              label="Show Legend"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.showEfficiencyMetrics}
                  onChange={(e) => handleOptionChange('showEfficiencyMetrics', e.target.checked)}
                />
              }
              label="Show Efficiency Metrics"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.showImpactAnalysis}
                  onChange={(e) => handleOptionChange('showImpactAnalysis', e.target.checked)}
                />
              }
              label="Show Impact Analysis"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={options.highlightDomains}
                  onChange={(e) => handleOptionChange('highlightDomains', e.target.checked)}
                />
              }
              label="Highlight Domains"
            />
          </>
        );

      default:
        return null;
    }
  };

  return (
    <Container maxWidth="xl">
      <Typography variant="h4" gutterBottom>
        Cyber-Safety Visualization Test Harness
      </Typography>

      <Grid container spacing={3}>
        {/* Controls */}
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Controls
            </Typography>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Visualization Type</InputLabel>
              <Select
                value={selectedVisualization}
                label="Visualization Type"
                onChange={(e) => setSelectedVisualization(e.target.value)}
              >
                <MenuItem value="tri_domain_tensor">Tri-Domain Tensor Visualization</MenuItem>
                <MenuItem value="cyber_safety_harmony_index">Cyber-Safety Harmony Index</MenuItem>
                <MenuItem value="risk_control_fusion_map">Risk-Control Fusion Map</MenuItem>
                <MenuItem value="cyber_safety_resonance_spectrogram">Cyber-Safety Resonance Spectrogram</MenuItem>
                <MenuItem value="unified_compliance_security_visualizer">Unified Compliance-Security Visualizer</MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Data Source</InputLabel>
              <Select
                value={dataSource}
                label="Data Source"
                onChange={(e) => setDataSource(e.target.value)}
              >
                <MenuItem value="mock">Mock Data</MenuItem>
                <MenuItem value="random">Random Data</MenuItem>
                <MenuItem value="api">API Data</MenuItem>
              </Select>
            </FormControl>

            {dataSource === 'api' && (
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>API Endpoint</InputLabel>
                <Select
                  value={apiEndpoint}
                  label="API Endpoint"
                  onChange={(e) => setApiEndpoint(e.target.value)}
                >
                  <MenuItem value="/api/cyber-safety/visualizations">Default Endpoint</MenuItem>
                  <MenuItem value="/api/cyber-safety/visualizations/real-time">Real-Time Data Endpoint</MenuItem>
                  <MenuItem value="/api/cyber-safety/visualizations/historical">Historical Data Endpoint</MenuItem>
                </Select>
              </FormControl>
            )}

            {dataSource === 'random' && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Random Data Parameters
                </Typography>

                <Box sx={{ width: '100%', mt: 1 }}>
                  <Typography variant="body2" gutterBottom>
                    Domain Count: {randomDataParams.domainCount}
                  </Typography>
                  <Slider
                    value={randomDataParams.domainCount}
                    min={2}
                    max={5}
                    step={1}
                    onChange={(e, value) => handleRandomParamChange('domainCount', value)}
                    valueLabelDisplay="auto"
                  />
                </Box>

                <Box sx={{ width: '100%', mt: 1 }}>
                  <Typography variant="body2" gutterBottom>
                    Connection Count: {randomDataParams.connectionCount}
                  </Typography>
                  <Slider
                    value={randomDataParams.connectionCount}
                    min={1}
                    max={10}
                    step={1}
                    onChange={(e, value) => handleRandomParamChange('connectionCount', value)}
                    valueLabelDisplay="auto"
                  />
                </Box>

                <Box sx={{ width: '100%', mt: 1 }}>
                  <Typography variant="body2" gutterBottom>
                    Health Range: {randomDataParams.minHealth.toFixed(1)} - {randomDataParams.maxHealth.toFixed(1)}
                  </Typography>
                  <Slider
                    value={[randomDataParams.minHealth, randomDataParams.maxHealth]}
                    min={0}
                    max={1}
                    step={0.1}
                    onChange={(e, value) => {
                      handleRandomParamChange('minHealth', value[0]);
                      handleRandomParamChange('maxHealth', value[1]);
                    }}
                    valueLabelDisplay="auto"
                  />
                </Box>
              </Box>
            )}

            <Button
              variant="contained"
              color="primary"
              fullWidth
              onClick={loadData}
              disabled={isLoading}
            >
              {isLoading ? 'Loading...' : 'Refresh Data'}
            </Button>
          </Paper>

          <Paper sx={{ p: 2, mt: 2 }}>
            <Typography variant="h6" gutterBottom>
              Visualization Options
            </Typography>

            {renderOptions()}
          </Paper>
        </Grid>

        {/* Visualization */}
        <Grid item xs={12} md={9}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              {selectedVisualization.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
            </Typography>

            {renderVisualization()}
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
}

export default CyberSafetyVisualizationTestHarness;
