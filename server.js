/**
 * NovaFuse API Superstore - Main Server
 *
 * This is the entry point for the NovaFuse API Superstore application.
 * It sets up the Express server and connects to the database.
 */

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const mongoose = require('mongoose');
const winston = require('winston');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const path = require('path');

// Import Advanced Analytics Engine Integration (internal name preserved for compatibility)
const { ComphyologyFrameworkIntegration } = require('./src/comphyology/integration/comphyology_framework_integration');

// Initialize Advanced Analytics Engine
const comphyologyFrameworkIntegration = new ComphyologyFrameworkIntegration({
  enableUUFT: true,
  enableNestedTrinity: true,
  enable1882Principle: true,
  enablePiPhiEScoring: true,
  enableFiniteUniverseMath: true
});

// Create Express app
const app = express();
const PORT = process.env.PORT || 3002;

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'nova-marketplace' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Middleware

// Add security headers with helmet
app.use(helmet());

// Configure CORS
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
}));

// Parse JSON and URL-encoded bodies
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Request logging
app.use(morgan('dev'));

// Configure rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: {
    error: 'Too Many Requests',
    message: 'You have exceeded the rate limit. Please try again later.'
  },
  // Skip rate limiting in development and test environments
  skip: () => process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test'
});

// Apply rate limiting to all API routes
app.use('/api', apiLimiter);

// Apply Advanced Analytics Engine middleware
app.use((req, res, next) => {
  // Attach Advanced Analytics Engine to request (internal name preserved for compatibility)
  req.comphyologyFramework = comphyologyFrameworkIntegration;
  next();
});

// MongoDB connection configuration
const connectToMongoDB = async () => {
  if (process.env.NODE_ENV === 'test') {
    logger.info('Skipping MongoDB connection in test environment');
    return;
  }

  try {
    // Connection options with improved settings
    const mongoOptions = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000, // Timeout after 5 seconds
      socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
      family: 4 // Use IPv4, skip trying IPv6
    };

    // Get connection string from environment or use default
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/nova-marketplace';

    await mongoose.connect(mongoURI, mongoOptions);
    logger.info('Connected to MongoDB successfully');

    // Set up connection event handlers
    mongoose.connection.on('error', (err) => {
      logger.error('MongoDB connection error:', { error: err.message });
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected. Attempting to reconnect...');
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('Reconnected to MongoDB');
    });

    // Handle application termination
    process.on('SIGINT', async () => {
      try {
        await mongoose.connection.close();
        logger.info('MongoDB connection closed through app termination');
        process.exit(0);
      } catch (err) {
        logger.error('Error closing MongoDB connection:', { error: err.message });
        process.exit(1);
      }
    });

  } catch (err) {
    logger.error('Failed to connect to MongoDB:', { error: err.message });
    // Don't exit the process, allow the application to run without DB if needed
    // This is useful for development with mock data
  }
};

// Initialize MongoDB connection
connectToMongoDB();

// Routes
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to NovaFuse API Superstore',
    version: '1.0.0',
    components: {
      novaConnect: 'Universal API Connector',
      novaMarketplace: 'GRC API Marketplace',
      novaVision: 'Universal UI Connector',
      advancedAnalytics: 'Advanced Analytics Engine'
    },
    links: [
      { name: 'Connector Dashboard', url: '/connector-dashboard' },
      { name: 'NovaVision Examples', url: '/vision-examples' },
      { name: 'Advanced Analytics Dashboard', url: '/system-analytics/advanced-dashboard?key=nova-advanced-analytics-2023' }
    ]
  });
});

// NovaMarketplace API
app.use('/api/marketplace', require('./src/routes/marketplace-routes'));

// NovaConnect API
app.use('/api/connect/connectors', require('./src/routes/connector-routes'));

// Advanced Analytics Engine API (obfuscated name for Comphyology Framework)
app.use('/api/system-analytics', require('./src/routes/comphyology-routes'));

// NovaVision API
app.use('/api/vision', (req, res) => {
  res.json({
    message: 'NovaVision API',
    status: 'Active',
    features: [
      'Dynamic UI Generation',
      'UI Schema Validation',
      'Form Builder',
      'Dashboard Builder',
      'Report Builder',
      'Regulation-Aware UI Rendering',
      'Real-Time Regulation Switching',
      'AI-Powered Interface Optimization',
      'Cross-Platform Consistency Enforcement'
    ],
    endpoints: [
      '/api/vision/schema - UI Schema Generation API',
      '/api/vision/render - UI Rendering API',
      '/api/vision/examples/form - Form Example',
      '/api/vision/examples/dashboard - Dashboard Example',
      '/api/vision/examples/report - Report Example',
      '/api/vision/examples/advanced - Advanced Features Example'
    ]
  });
});

// NovaVision Example APIs
app.get('/api/vision/examples/form', (req, res) => {
  const { novaVision } = require('./src/novavision');
  const formExample = require('./src/novavision/examples/simple-form-example');
  res.json({
    message: 'NovaVision Form Example',
    schema: formExample.formSchema,
    renderedForm: formExample.renderedForm
  });
});

app.get('/api/vision/examples/dashboard', (req, res) => {
  const { novaVision } = require('./src/novavision');
  const dashboardExample = require('./src/novavision/examples/dashboard-example');
  res.json({
    message: 'NovaVision Dashboard Example',
    schema: dashboardExample.dashboardSchema,
    renderedDashboard: dashboardExample.renderedDashboard
  });
});

app.get('/api/vision/examples/report', (req, res) => {
  const { novaVision } = require('./src/novavision');
  const reportExample = require('./src/novavision/examples/report-example');
  res.json({
    message: 'NovaVision Report Example',
    schema: reportExample.reportSchema,
    renderedReport: reportExample.renderedReport
  });
});

app.get('/api/vision/examples/advanced', async (req, res) => {
  const { novaVision } = require('./src/novavision');
  const advancedExample = require('./src/novavision/examples/advanced-features-example');

  try {
    // Run the examples
    const regulationAwareUI = await advancedExample.demonstrateRegulationAwareUI();
    const realTimeRegulationSwitching = await advancedExample.demonstrateRealTimeRegulationSwitching();
    const aiPoweredOptimization = await advancedExample.demonstrateAIPoweredOptimization();
    const consistencyEnforcement = await advancedExample.demonstrateConsistencyEnforcement();

    res.json({
      message: 'NovaVision Advanced Features Example',
      regulationAwareUI,
      realTimeRegulationSwitching,
      aiPoweredOptimization,
      consistencyEnforcement
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error running advanced features example',
      error: error.message
    });
  }
});

// Serve connector dashboard
app.get('/connector-dashboard', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'connector-dashboard.html'));
});

// Serve NovaVision examples
app.get('/vision-examples', (req, res) => {
  res.json({
    message: 'NovaVision Examples',
    examples: [
      {
        name: 'Simple Form Example',
        description: 'A simple form example using NovaVision',
        endpoint: '/api/vision/examples/form'
      },
      {
        name: 'Dashboard Example',
        description: 'A dashboard example using NovaVision',
        endpoint: '/api/vision/examples/dashboard'
      },
      {
        name: 'Report Example',
        description: 'A report example using NovaVision',
        endpoint: '/api/vision/examples/report'
      },
      {
        name: 'Advanced Features Example',
        description: 'Advanced features of NovaVision including regulation-aware UI, real-time regulation switching, AI-powered optimization, and consistency enforcement',
        endpoint: '/api/vision/examples/advanced'
      }
    ]
  });
});

// Serve Advanced Analytics Dashboard (restricted access)
app.get('/system-analytics/advanced-dashboard', (req, res) => {
  // Check for authorization
  const authHeader = req.headers.authorization;
  const apiKey = req.query.key;

  // List of authorized IPs (for demonstration - would be in a secure config in production)
  const authorizedIPs = ['127.0.0.1', '::1', '::ffff:127.0.0.1'];

  // Check if request comes from an authorized IP
  const clientIP = req.ip ||
                  req.connection.remoteAddress ||
                  req.socket.remoteAddress ||
                  (req.connection.socket ? req.connection.socket.remoteAddress : null);

  // Simple API key for demonstration (would use a more secure method in production)
  const validApiKey = 'nova-advanced-analytics-2023';

  if (
    (authorizedIPs.includes(clientIP) && apiKey === validApiKey) ||
    (authHeader && authHeader === `Bearer ${validApiKey}`)
  ) {
    // Log access for audit purposes
    logger.info(`Authorized access to Advanced Analytics Dashboard from ${clientIP}`);
    res.sendFile(path.join(__dirname, 'public', 'comphyology.html'));
  } else {
    // Log unauthorized attempt
    logger.warn(`Unauthorized access attempt to Advanced Analytics Dashboard from ${clientIP}`);
    res.status(403).json({
      error: 'Access denied. This system requires proper authorization.',
      message: 'Please contact your system administrator for access.'
    });
  }
});

app.use('/api/connect', (req, res) => {
  res.json({
    message: 'NovaConnect API',
    status: 'Active',
    features: [
      'Universal API Connectivity',
      'Strong Security',
      'Data Transformation',
      'Comprehensive Testing'
    ],
    endpoints: [
      '/api/connect/connectors - Connector Registry API',
      '/connector-dashboard - Connector Management Dashboard'
    ]
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error', { error: err.message, stack: err.stack });
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'production' ? 'An unexpected error occurred' : err.message
  });
});

// Start the server
app.listen(PORT, () => {
  // Initialize Advanced Analytics Engine component alignments
  try {
    comphyologyFrameworkIntegration.initializeComponentAlignments();
    logger.info('Advanced Analytics Engine initialized successfully');
  } catch (error) {
    logger.error('Error initializing Advanced Analytics Engine', { error: error.message });
  }

  logger.info(`Server running on port ${PORT}`);
  console.log(`NovaFuse API Superstore running on http://localhost:${PORT}`);
  console.log(`Advanced Analytics Engine active with proprietary performance optimization`);
});

module.exports = app;
