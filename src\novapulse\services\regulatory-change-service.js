/**
 * NovaPulse+ - Regulatory Change Service
 * 
 * This service provides regulatory change management capabilities.
 */

const { createLogger } = require('../../utils/logger');
const { v4: uuidv4 } = require('uuid');

const logger = createLogger('regulatory-change-service');

// In-memory storage for regulatory changes (would be replaced with a database in production)
const regulatoryChanges = [
  {
    id: 'change-001',
    title: 'GDPR Article 28 Update',
    regulation: 'GDPR',
    description: 'Updated requirements for data processor contracts',
    source: 'EU Commission',
    sourceUrl: 'https://ec.europa.eu/example',
    publicationDate: '2023-02-15T00:00:00.000Z',
    effectiveDate: '2023-06-01T00:00:00.000Z',
    deadline: '2023-05-31T00:00:00.000Z',
    status: 'pending',
    priority: 'high',
    categories: ['data-protection', 'contracts'],
    regions: ['EU'],
    industries: ['all'],
    requirements: [
      {
        id: 'req-001',
        description: 'Update data processor contracts to include new clauses',
        status: 'pending'
      },
      {
        id: 'req-002',
        description: 'Review existing data processor relationships',
        status: 'pending'
      }
    ],
    createdAt: '2023-02-16T00:00:00.000Z',
    updatedAt: '2023-02-16T00:00:00.000Z'
  },
  {
    id: 'change-002',
    title: 'PCI DSS 4.0 Implementation',
    regulation: 'PCI DSS',
    description: 'Implementation of PCI DSS version 4.0 requirements',
    source: 'PCI Security Standards Council',
    sourceUrl: 'https://www.pcisecuritystandards.org/example',
    publicationDate: '2023-01-10T00:00:00.000Z',
    effectiveDate: '2024-03-31T00:00:00.000Z',
    deadline: '2024-03-30T00:00:00.000Z',
    status: 'planning',
    priority: 'high',
    categories: ['payment-security', 'data-security'],
    regions: ['global'],
    industries: ['financial', 'retail', 'e-commerce'],
    requirements: [
      {
        id: 'req-003',
        description: 'Implement enhanced authentication requirements',
        status: 'pending'
      },
      {
        id: 'req-004',
        description: 'Update security policies and procedures',
        status: 'pending'
      },
      {
        id: 'req-005',
        description: 'Enhance encryption requirements',
        status: 'pending'
      }
    ],
    createdAt: '2023-01-15T00:00:00.000Z',
    updatedAt: '2023-01-15T00:00:00.000Z'
  },
  {
    id: 'change-003',
    title: 'CCPA Amendment',
    regulation: 'CCPA',
    description: 'Amendment to California Consumer Privacy Act requirements',
    source: 'California State Legislature',
    sourceUrl: 'https://leginfo.legislature.ca.gov/example',
    publicationDate: '2023-03-05T00:00:00.000Z',
    effectiveDate: '2023-07-01T00:00:00.000Z',
    deadline: '2023-06-30T00:00:00.000Z',
    status: 'pending',
    priority: 'medium',
    categories: ['data-protection', 'consumer-rights'],
    regions: ['US-CA'],
    industries: ['all'],
    requirements: [
      {
        id: 'req-006',
        description: 'Update privacy notices',
        status: 'pending'
      },
      {
        id: 'req-007',
        description: 'Enhance data subject request processes',
        status: 'pending'
      }
    ],
    createdAt: '2023-03-10T00:00:00.000Z',
    updatedAt: '2023-03-10T00:00:00.000Z'
  }
];

/**
 * Get all regulatory changes
 * 
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - List of regulatory changes
 */
async function getAllRegulatoryChanges(filters = {}) {
  logger.debug('Getting all regulatory changes', { filters });
  
  let filteredChanges = [...regulatoryChanges];
  
  // Apply filters if provided
  if (filters.regulation) {
    filteredChanges = filteredChanges.filter(c => c.regulation === filters.regulation);
  }
  
  if (filters.status) {
    filteredChanges = filteredChanges.filter(c => c.status === filters.status);
  }
  
  if (filters.priority) {
    filteredChanges = filteredChanges.filter(c => c.priority === filters.priority);
  }
  
  if (filters.region) {
    filteredChanges = filteredChanges.filter(c => c.regions.includes(filters.region));
  }
  
  if (filters.industry) {
    filteredChanges = filteredChanges.filter(c => 
      c.industries.includes(filters.industry) || c.industries.includes('all')
    );
  }
  
  if (filters.category) {
    filteredChanges = filteredChanges.filter(c => c.categories.includes(filters.category));
  }
  
  if (filters.effectiveAfter) {
    const effectiveAfterDate = new Date(filters.effectiveAfter);
    filteredChanges = filteredChanges.filter(c => 
      new Date(c.effectiveDate) >= effectiveAfterDate
    );
  }
  
  if (filters.effectiveBefore) {
    const effectiveBeforeDate = new Date(filters.effectiveBefore);
    filteredChanges = filteredChanges.filter(c => 
      new Date(c.effectiveDate) <= effectiveBeforeDate
    );
  }
  
  return filteredChanges;
}

/**
 * Get a regulatory change by ID
 * 
 * @param {string} changeId - Regulatory change ID
 * @returns {Promise<Object>} - Regulatory change
 */
async function getRegulatoryChange(changeId) {
  logger.debug('Getting regulatory change', { changeId });
  
  const change = regulatoryChanges.find(c => c.id === changeId);
  
  if (!change) {
    logger.warn('Regulatory change not found', { changeId });
    throw new Error(`Regulatory change not found with ID: ${changeId}`);
  }
  
  return change;
}

/**
 * Create a new regulatory change
 * 
 * @param {Object} changeData - Regulatory change data
 * @returns {Promise<Object>} - Created regulatory change
 */
async function createRegulatoryChange(changeData) {
  logger.info('Creating regulatory change', { 
    regulation: changeData.regulation,
    title: changeData.title
  });
  
  // Validate required fields
  if (!changeData.title) {
    throw new Error('Regulatory change title is required');
  }
  
  if (!changeData.regulation) {
    throw new Error('Regulation is required');
  }
  
  // Create regulatory change object
  const regulatoryChange = {
    id: changeData.id || `change-${uuidv4()}`,
    title: changeData.title,
    regulation: changeData.regulation,
    description: changeData.description || '',
    source: changeData.source || 'Unknown',
    sourceUrl: changeData.sourceUrl || null,
    publicationDate: changeData.publicationDate || new Date().toISOString(),
    effectiveDate: changeData.effectiveDate || null,
    deadline: changeData.deadline || null,
    status: changeData.status || 'pending',
    priority: changeData.priority || 'medium',
    categories: changeData.categories || [],
    regions: changeData.regions || ['global'],
    industries: changeData.industries || ['all'],
    requirements: changeData.requirements || [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  // Add to regulatory changes
  regulatoryChanges.push(regulatoryChange);
  
  return regulatoryChange;
}

/**
 * Update a regulatory change
 * 
 * @param {string} changeId - Regulatory change ID
 * @param {Object} changeData - Regulatory change data to update
 * @returns {Promise<Object>} - Updated regulatory change
 */
async function updateRegulatoryChange(changeId, changeData) {
  logger.info('Updating regulatory change', { changeId });
  
  // Find regulatory change
  const index = regulatoryChanges.findIndex(c => c.id === changeId);
  
  if (index === -1) {
    logger.warn('Regulatory change not found', { changeId });
    throw new Error(`Regulatory change not found with ID: ${changeId}`);
  }
  
  // Update regulatory change
  const updatedChange = {
    ...regulatoryChanges[index],
    ...changeData,
    id: changeId, // Ensure ID doesn't change
    updatedAt: new Date().toISOString()
  };
  
  regulatoryChanges[index] = updatedChange;
  
  return updatedChange;
}

/**
 * Update regulatory change status
 * 
 * @param {string} changeId - Regulatory change ID
 * @param {string} status - New status
 * @returns {Promise<Object>} - Updated regulatory change
 */
async function updateRegulatoryChangeStatus(changeId, status) {
  logger.info('Updating regulatory change status', { changeId, status });
  
  // Find regulatory change
  const index = regulatoryChanges.findIndex(c => c.id === changeId);
  
  if (index === -1) {
    logger.warn('Regulatory change not found', { changeId });
    throw new Error(`Regulatory change not found with ID: ${changeId}`);
  }
  
  // Update status
  const updatedChange = {
    ...regulatoryChanges[index],
    status,
    updatedAt: new Date().toISOString()
  };
  
  regulatoryChanges[index] = updatedChange;
  
  return updatedChange;
}

/**
 * Update requirement status
 * 
 * @param {string} changeId - Regulatory change ID
 * @param {string} requirementId - Requirement ID
 * @param {string} status - New status
 * @returns {Promise<Object>} - Updated regulatory change
 */
async function updateRequirementStatus(changeId, requirementId, status) {
  logger.info('Updating requirement status', { changeId, requirementId, status });
  
  // Find regulatory change
  const index = regulatoryChanges.findIndex(c => c.id === changeId);
  
  if (index === -1) {
    logger.warn('Regulatory change not found', { changeId });
    throw new Error(`Regulatory change not found with ID: ${changeId}`);
  }
  
  // Find requirement
  const requirementIndex = regulatoryChanges[index].requirements.findIndex(r => r.id === requirementId);
  
  if (requirementIndex === -1) {
    logger.warn('Requirement not found', { changeId, requirementId });
    throw new Error(`Requirement not found with ID: ${requirementId}`);
  }
  
  // Update requirement status
  const updatedRequirements = [...regulatoryChanges[index].requirements];
  updatedRequirements[requirementIndex] = {
    ...updatedRequirements[requirementIndex],
    status
  };
  
  // Update regulatory change
  const updatedChange = {
    ...regulatoryChanges[index],
    requirements: updatedRequirements,
    updatedAt: new Date().toISOString()
  };
  
  regulatoryChanges[index] = updatedChange;
  
  return updatedChange;
}

/**
 * Delete a regulatory change
 * 
 * @param {string} changeId - Regulatory change ID
 * @returns {Promise<boolean>} - Whether the regulatory change was deleted
 */
async function deleteRegulatoryChange(changeId) {
  logger.info('Deleting regulatory change', { changeId });
  
  // Find regulatory change
  const index = regulatoryChanges.findIndex(c => c.id === changeId);
  
  if (index === -1) {
    logger.warn('Regulatory change not found', { changeId });
    throw new Error(`Regulatory change not found with ID: ${changeId}`);
  }
  
  // Remove regulatory change
  regulatoryChanges.splice(index, 1);
  
  return true;
}

module.exports = {
  getAllRegulatoryChanges,
  getRegulatoryChange,
  createRegulatoryChange,
  updateRegulatoryChange,
  updateRegulatoryChangeStatus,
  updateRequirementStatus,
  deleteRegulatoryChange
};
