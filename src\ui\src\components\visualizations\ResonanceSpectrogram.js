import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { Box, CircularProgress } from '@mui/material';

/**
 * ResonanceSpectrogram component
 * 
 * Renders a 3D visualization of a resonance spectrogram using Three.js
 */
function ResonanceSpectrogram({
  tensor,
  options = {
    renderMode: 'medium',
    showAxes: true,
    showGrid: true,
    rotationSpeed: 1,
    colorScheme: 'default'
  },
  width = '100%',
  height = '100%'
}) {
  const containerRef = useRef(null);
  const rendererRef = useRef(null);
  const sceneRef = useRef(null);
  const cameraRef = useRef(null);
  const controlsRef = useRef(null);
  const animationFrameRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize Three.js scene
  useEffect(() => {
    if (!containerRef.current) return;

    try {
      // Create scene
      const scene = new THREE.Scene();
      scene.background = new THREE.Color(0x121212);
      sceneRef.current = scene;

      // Create camera
      const camera = new THREE.PerspectiveCamera(
        75,
        containerRef.current.clientWidth / containerRef.current.clientHeight,
        0.1,
        1000
      );
      camera.position.z = 5;
      cameraRef.current = camera;

      // Create renderer
      const renderer = new THREE.WebGLRenderer({ antialias: true });
      renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
      containerRef.current.appendChild(renderer.domElement);
      rendererRef.current = renderer;

      // Create controls
      const controls = new OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.25;
      controls.enableZoom = true;
      controlsRef.current = controls;

      // Add ambient light
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
      scene.add(ambientLight);

      // Add directional light
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(1, 1, 1);
      scene.add(directionalLight);

      // Add axes helper if enabled
      if (options.showAxes) {
        const axesHelper = new THREE.AxesHelper(5);
        scene.add(axesHelper);
      }

      // Add grid helper if enabled
      if (options.showGrid) {
        const gridHelper = new THREE.GridHelper(10, 10);
        scene.add(gridHelper);
      }

      // Animation loop
      const animate = () => {
        animationFrameRef.current = requestAnimationFrame(animate);
        
        // Update controls
        if (controlsRef.current) {
          controlsRef.current.update();
        }
        
        // Render scene
        if (rendererRef.current && sceneRef.current && cameraRef.current) {
          rendererRef.current.render(sceneRef.current, cameraRef.current);
        }
      };

      // Start animation loop
      animate();

      // Handle window resize
      const handleResize = () => {
        if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;
        
        const width = containerRef.current.clientWidth;
        const height = containerRef.current.clientHeight;
        
        cameraRef.current.aspect = width / height;
        cameraRef.current.updateProjectionMatrix();
        
        rendererRef.current.setSize(width, height);
      };

      window.addEventListener('resize', handleResize);

      // Visualization is ready
      setIsLoading(false);

      // Clean up
      return () => {
        window.removeEventListener('resize', handleResize);
        
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
        
        if (rendererRef.current && containerRef.current) {
          containerRef.current.removeChild(rendererRef.current.domElement);
        }
        
        if (sceneRef.current) {
          // Dispose of all geometries and materials
          sceneRef.current.traverse((object) => {
            if (object.geometry) {
              object.geometry.dispose();
            }
            
            if (object.material) {
              if (Array.isArray(object.material)) {
                object.material.forEach((material) => material.dispose());
              } else {
                object.material.dispose();
              }
            }
          });
        }
        
        rendererRef.current = null;
        sceneRef.current = null;
        cameraRef.current = null;
        controlsRef.current = null;
      };
    } catch (err) {
      console.error('Error initializing Three.js:', err);
      setError(err.message || 'Error initializing visualization');
      setIsLoading(false);
    }
  }, [options.showAxes, options.showGrid]);

  // Update visualization when tensor data changes
  useEffect(() => {
    if (!sceneRef.current || !tensor || isLoading) return;

    try {
      // Remove existing spectrogram
      const existingSpectrogram = sceneRef.current.getObjectByName('spectrogram');
      if (existingSpectrogram) {
        sceneRef.current.remove(existingSpectrogram);
        
        // Dispose of geometry and material
        if (existingSpectrogram.geometry) {
          existingSpectrogram.geometry.dispose();
        }
        
        if (existingSpectrogram.material) {
          if (Array.isArray(existingSpectrogram.material)) {
            existingSpectrogram.material.forEach((material) => material.dispose());
          } else {
            existingSpectrogram.material.dispose();
          }
        }
      }

      // Create spectrogram group
      const spectrogramGroup = new THREE.Group();
      spectrogramGroup.name = 'spectrogram';

      // Get tensor values
      const values = tensor.values || [];
      
      // Generate frequency and time data
      const numFrequencies = Math.ceil(Math.sqrt(values.length));
      const numTimeSteps = Math.ceil(values.length / numFrequencies);
      
      // Create spectrogram surface
      const geometry = new THREE.PlaneGeometry(
        numTimeSteps,
        numFrequencies,
        numTimeSteps - 1,
        numFrequencies - 1
      );
      
      const vertices = geometry.attributes.position.array;
      const colorMap = getColorMap(options.colorScheme);
      const colors = [];
      
      // Update vertices based on tensor values
      for (let i = 0; i < numFrequencies; i++) {
        for (let j = 0; j < numTimeSteps; j++) {
          const index = i * numTimeSteps + j;
          const vertexIndex = index * 3;
          
          // Set z position based on tensor value
          if (index < values.length) {
            vertices[vertexIndex + 2] = values[index] * 2; // Scale for better visibility
            
            // Add color based on value
            const color = colorMap(values[index]);
            colors.push(color.r / 255, color.g / 255, color.b / 255);
          } else {
            vertices[vertexIndex + 2] = 0;
            colors.push(0.5, 0.5, 0.5); // Gray for missing values
          }
        }
      }
      
      geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
      
      const material = new THREE.MeshPhongMaterial({
        vertexColors: true,
        side: THREE.DoubleSide,
        flatShading: options.renderMode === 'low',
        shininess: 50
      });
      
      const surface = new THREE.Mesh(geometry, material);
      surface.rotation.x = -Math.PI / 2;
      surface.position.set(-numTimeSteps / 2, 0, -numFrequencies / 2);
      spectrogramGroup.add(surface);
      
      // Add frequency lines
      const lineGeometry = new THREE.BufferGeometry();
      const lineMaterial = new THREE.LineBasicMaterial({ color: 0x888888 });
      
      for (let i = 0; i < numFrequencies; i++) {
        const linePoints = [];
        
        for (let j = 0; j < numTimeSteps; j++) {
          const index = i * numTimeSteps + j;
          const value = index < values.length ? values[index] * 2 : 0;
          
          linePoints.push(
            new THREE.Vector3(
              j - numTimeSteps / 2,
              value,
              i - numFrequencies / 2
            )
          );
        }
        
        const lineGeometry = new THREE.BufferGeometry().setFromPoints(linePoints);
        const line = new THREE.Line(lineGeometry, lineMaterial);
        spectrogramGroup.add(line);
      }
      
      // Add resonance peaks
      const peakGeometry = new THREE.SphereGeometry(0.1, 16, 16);
      const peakMaterial = new THREE.MeshPhongMaterial({ color: 0xff0000 });
      
      // Find local maxima in the data
      for (let i = 1; i < numFrequencies - 1; i++) {
        for (let j = 1; j < numTimeSteps - 1; j++) {
          const index = i * numTimeSteps + j;
          
          if (index < values.length) {
            const value = values[index];
            const neighbors = [
              values[index - numTimeSteps - 1] || 0,
              values[index - numTimeSteps] || 0,
              values[index - numTimeSteps + 1] || 0,
              values[index - 1] || 0,
              values[index + 1] || 0,
              values[index + numTimeSteps - 1] || 0,
              values[index + numTimeSteps] || 0,
              values[index + numTimeSteps + 1] || 0
            ];
            
            // Check if value is a local maximum
            if (neighbors.every(neighbor => value > neighbor) && value > 0.7) {
              const peak = new THREE.Mesh(peakGeometry, peakMaterial);
              peak.position.set(
                j - numTimeSteps / 2,
                value * 2,
                i - numFrequencies / 2
              );
              spectrogramGroup.add(peak);
            }
          }
        }
      }

      // Add spectrogram to scene
      sceneRef.current.add(spectrogramGroup);

      // Adjust camera position
      if (cameraRef.current) {
        const maxDimension = Math.max(numTimeSteps, numFrequencies);
        cameraRef.current.position.set(0, maxDimension, maxDimension);
        cameraRef.current.lookAt(0, 0, 0);
        
        if (controlsRef.current) {
          controlsRef.current.update();
        }
      }
    } catch (err) {
      console.error('Error updating spectrogram visualization:', err);
      setError(err.message || 'Error updating visualization');
    }
  }, [tensor, options.colorScheme, options.renderMode, isLoading]);

  // Update rotation speed
  useEffect(() => {
    if (!sceneRef.current || isLoading) return;

    const spectrogramGroup = sceneRef.current.getObjectByName('spectrogram');
    if (!spectrogramGroup) return;

    // Clear existing rotation animation
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    // Animation loop with rotation
    const animate = () => {
      animationFrameRef.current = requestAnimationFrame(animate);
      
      // Rotate spectrogram based on rotation speed
      if (spectrogramGroup && options.rotationSpeed > 0) {
        spectrogramGroup.rotation.y += 0.01 * options.rotationSpeed;
      }
      
      // Update controls
      if (controlsRef.current) {
        controlsRef.current.update();
      }
      
      // Render scene
      if (rendererRef.current && sceneRef.current && cameraRef.current) {
        rendererRef.current.render(sceneRef.current, cameraRef.current);
      }
    };

    // Start animation loop
    animate();

    // Clean up
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [options.rotationSpeed, isLoading]);

  // Helper function to get color map based on color scheme
  const getColorMap = (colorScheme) => {
    switch (colorScheme) {
      case 'rainbow':
        return (value) => {
          const h = (1 - value) * 240; // Hue (0 to 240)
          const s = 1; // Saturation
          const l = 0.5; // Lightness
          
          return hslToRgb(h, s, l);
        };
        
      case 'heatmap':
        return (value) => {
          const r = Math.floor(value * 255);
          const g = Math.floor((1 - Math.abs(value - 0.5) * 2) * 255);
          const b = Math.floor((1 - value) * 255);
          
          return { r, g, b };
        };
        
      case 'grayscale':
        return (value) => {
          const intensity = Math.floor(value * 255);
          return { r: intensity, g: intensity, b: intensity };
        };
        
      case 'default':
      default:
        return (value) => {
          if (value < 0.33) {
            return { r: 0, g: Math.floor(value * 3 * 255), b: 255 };
          } else if (value < 0.66) {
            return { r: 0, g: 255, b: Math.floor((1 - (value - 0.33) * 3) * 255) };
          } else {
            return { r: Math.floor((value - 0.66) * 3 * 255), g: 255, b: 0 };
          }
        };
    }
  };

  // Helper function to convert HSL to RGB
  const hslToRgb = (h, s, l) => {
    let r, g, b;

    if (s === 0) {
      r = g = b = l; // achromatic
    } else {
      const hue2rgb = (p, q, t) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
      };

      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      
      r = hue2rgb(p, q, (h / 360) + 1/3);
      g = hue2rgb(p, q, h / 360);
      b = hue2rgb(p, q, (h / 360) - 1/3);
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  };

  return (
    <Box
      ref={containerRef}
      sx={{
        width,
        height,
        position: 'relative',
        overflow: 'hidden',
        borderRadius: 1,
        bgcolor: 'background.paper'
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1
          }}
        >
          <CircularProgress />
        </Box>
      )}
      
      {error && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1,
            color: 'error.main',
            p: 2,
            textAlign: 'center'
          }}
        >
          {error}
        </Box>
      )}
    </Box>
  );
}

export default ResonanceSpectrogram;
