#!/usr/bin/env node

/**
 * Simple π-Coherence Test - Standalone Version
 * Tests π-aligned timing vs standard timing for performance validation
 */

const http = require('http');
const https = require('https');
const { performance } = require('perf_hooks');

// π-Coherence timing intervals (in milliseconds)
const PI_TIMING = {
    FAST: 31.42,
    MEDIUM: 42.53,
    SLOW: 53.64,
    TIMEOUT: 3142
};

// Standard timing
const STANDARD_TIMING = {
    FAST: 10,
    MEDIUM: 100,
    SLOW: 1000,
    TIMEOUT: 5000
};

class SimplePiTest {
    constructor() {
        this.results = {
            standard: { times: [], errors: 0, coherence: 0 },
            piTiming: { times: [], errors: 0, coherence: 0 }
        };
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    calculateCoherence(duration) {
        const piIntervals = [31.42, 42.53, 53.64, 64.75, 75.86, 86.97];
        const closest = piIntervals.reduce((prev, curr) => 
            Math.abs(curr - duration) < Math.abs(prev - duration) ? curr : prev
        );
        return Math.max(0, 1.0 - (Math.abs(duration - closest) / closest));
    }

    async makeRequest(url, timeout) {
        return new Promise((resolve, reject) => {
            const start = performance.now();
            const protocol = url.startsWith('https') ? https : http;
            
            const req = protocol.get(url, { timeout }, (res) => {
                const end = performance.now();
                resolve({
                    success: true,
                    duration: end - start,
                    status: res.statusCode
                });
            });

            req.on('error', (err) => {
                const end = performance.now();
                resolve({
                    success: false,
                    duration: end - start,
                    error: err.message
                });
            });

            req.on('timeout', () => {
                req.destroy();
                const end = performance.now();
                resolve({
                    success: false,
                    duration: end - start,
                    error: 'timeout'
                });
            });
        });
    }

    async runTest(timingConfig, label, iterations = 20) {
        console.log(`\n🔬 Running ${label} test (${iterations} iterations)...`);
        
        const results = { times: [], errors: 0, coherenceScores: [] };
        const testUrls = [
            'http://httpbin.org/delay/0',
            'http://httpbin.org/status/200',
            'http://httpbin.org/json'
        ];

        for (let i = 0; i < iterations; i++) {
            // Pre-operation timing
            await this.sleep(timingConfig.FAST);
            
            // Make request
            const url = testUrls[i % testUrls.length];
            const result = await this.makeRequest(url, timingConfig.TIMEOUT);
            
            // Post-operation timing
            await this.sleep(timingConfig.MEDIUM * 0.5);
            
            results.times.push(result.duration);
            if (!result.success) results.errors++;
            
            const coherence = this.calculateCoherence(result.duration);
            results.coherenceScores.push(coherence);
            
            // Progress indicator
            if ((i + 1) % 5 === 0) {
                process.stdout.write(`\r  Progress: ${((i + 1) / iterations * 100).toFixed(0)}%`);
            }
        }
        
        // Calculate metrics
        const avgTime = results.times.reduce((a, b) => a + b, 0) / results.times.length;
        const avgCoherence = results.coherenceScores.reduce((a, b) => a + b, 0) / results.coherenceScores.length;
        const successRate = (iterations - results.errors) / iterations;
        
        console.log(`\n  ✅ Completed: ${iterations} requests`);
        console.log(`  📊 Success Rate: ${(successRate * 100).toFixed(1)}%`);
        console.log(`  ⚡ Avg Time: ${avgTime.toFixed(1)}ms`);
        console.log(`  🔱 Coherence: ${avgCoherence.toFixed(3)}`);
        
        return {
            avgTime,
            successRate,
            coherence: avgCoherence,
            errors: results.errors,
            times: results.times
        };
    }

    async runComparison() {
        console.log('🚀 Starting Simple π-Coherence Test');
        console.log('📋 Testing against httpbin.org endpoints');
        console.log('⏱️  Expected duration: ~2 minutes\n');

        try {
            // Test standard timing
            this.results.standard = await this.runTest(STANDARD_TIMING, 'STANDARD');
            
            // Brief pause
            console.log('\n⏸️  Pausing between tests...');
            await this.sleep(2000);
            
            // Test π-coherence timing
            this.results.piTiming = await this.runTest(PI_TIMING, 'π-COHERENCE');
            
            // Generate report
            this.generateReport();
            
        } catch (error) {
            console.error('❌ Test failed:', error.message);
        }
    }

    generateReport() {
        console.log('\n' + '='.repeat(60));
        console.log('🔱 π-COHERENCE TEST RESULTS');
        console.log('='.repeat(60));
        
        const standard = this.results.standard;
        const piTiming = this.results.piTiming;
        
        // Calculate improvements
        const efficiencyGain = standard.avgTime / piTiming.avgTime;
        const coherenceImprovement = piTiming.coherence - standard.coherence;
        const errorReduction = standard.errors > 0 ? 
            (standard.errors - piTiming.errors) / standard.errors : 0;
        
        console.log('\n📊 COMPARISON:');
        console.log(`┌─────────────────────┬─────────────┬─────────────┬─────────────┐`);
        console.log(`│ Metric              │ Standard    │ π-Coherence │ Improvement │`);
        console.log(`├─────────────────────┼─────────────┼─────────────┼─────────────┤`);
        console.log(`│ Avg Response Time   │ ${standard.avgTime.toFixed(1).padStart(9)}ms │ ${piTiming.avgTime.toFixed(1).padStart(9)}ms │ ${efficiencyGain.toFixed(2).padStart(9)}× │`);
        console.log(`│ Success Rate        │ ${(standard.successRate * 100).toFixed(1).padStart(8)}% │ ${(piTiming.successRate * 100).toFixed(1).padStart(8)}% │ ${((piTiming.successRate - standard.successRate) * 100).toFixed(1).padStart(8)}% │`);
        console.log(`│ Coherence Score     │ ${standard.coherence.toFixed(3).padStart(11)} │ ${piTiming.coherence.toFixed(3).padStart(11)} │ ${coherenceImprovement.toFixed(3).padStart(11)} │`);
        console.log(`│ Error Count         │ ${standard.errors.toString().padStart(11)} │ ${piTiming.errors.toString().padStart(11)} │ ${(errorReduction * 100).toFixed(1).padStart(10)}% │`);
        console.log(`└─────────────────────┴─────────────┴─────────────┴─────────────┘`);
        
        // Assessment
        console.log('\n🎯 ASSESSMENT:');
        let score = 0;
        
        if (efficiencyGain >= 3.0) {
            console.log(`   🏆 BREAKTHROUGH: ${efficiencyGain.toFixed(2)}× efficiency gain!`);
            score += 40;
        } else if (efficiencyGain >= 2.0) {
            console.log(`   ✅ SIGNIFICANT: ${efficiencyGain.toFixed(2)}× efficiency improvement`);
            score += 30;
        } else if (efficiencyGain >= 1.2) {
            console.log(`   📈 MODERATE: ${efficiencyGain.toFixed(2)}× efficiency improvement`);
            score += 20;
        } else {
            console.log(`   ⚠️  MINIMAL: ${efficiencyGain.toFixed(2)}× efficiency change`);
            score += 5;
        }
        
        if (coherenceImprovement > 0.1) {
            console.log(`   🔱 COHERENCE: Strong improvement (+${coherenceImprovement.toFixed(3)})`);
            score += 30;
        } else if (coherenceImprovement > 0.05) {
            console.log(`   🔱 COHERENCE: Moderate improvement (+${coherenceImprovement.toFixed(3)})`);
            score += 20;
        } else if (coherenceImprovement > 0) {
            console.log(`   🔱 COHERENCE: Slight improvement (+${coherenceImprovement.toFixed(3)})`);
            score += 10;
        }
        
        if (errorReduction > 0.2) {
            console.log(`   🛡️  RELIABILITY: Error reduction (${(errorReduction * 100).toFixed(1)}%)`);
            score += 20;
        }
        
        if (piTiming.successRate > standard.successRate) {
            score += 10;
        }
        
        // Final verdict
        console.log('\n🏆 FINAL VERDICT:');
        if (score >= 80) {
            console.log('   🎉 BREAKTHROUGH SUCCESS - π-Coherence Principle VALIDATED!');
            console.log('   ✅ Ready for production deployment');
        } else if (score >= 60) {
            console.log('   🎯 SIGNIFICANT SUCCESS - π-Coherence shows promise');
            console.log('   📋 Continue testing with larger systems');
        } else if (score >= 40) {
            console.log('   📈 MODERATE SUCCESS - Some improvements observed');
            console.log('   🔧 Refine timing intervals and retest');
        } else {
            console.log('   ⚠️  NEEDS IMPROVEMENT - Minimal benefits observed');
            console.log('   🔍 Investigate system-specific factors');
        }
        
        console.log(`\n📊 Overall Score: ${score}/100`);
        
        console.log('\n' + '='.repeat(60));
        console.log('🔱 TEST COMPLETE');
        console.log('='.repeat(60));
    }
}

// Run the test
if (require.main === module) {
    const test = new SimplePiTest();
    test.runComparison()
        .then(() => {
            console.log('\n✅ π-Coherence test completed successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n❌ Test failed:', error);
            process.exit(1);
        });
}

module.exports = SimplePiTest;
