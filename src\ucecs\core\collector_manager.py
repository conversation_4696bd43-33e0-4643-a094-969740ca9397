"""
Collector Manager for the Universal Compliance Evidence Collection System.

This module provides functionality for managing evidence collectors, including
scheduled collection, collection templates, and various collection strategies.
"""

import os
import json
import importlib
import logging
import threading
import time
import datetime
import uuid
import copy
from typing import Dict, List, Any, Optional, Callable, Union, Set, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CollectorManager:
    """
    Manager for evidence collectors.

    This class is responsible for registering, managing, and executing
    evidence collectors, with support for scheduled collection, collection
    templates, and various collection strategies.
    """

    # Collection strategies
    COLLECTION_STRATEGIES = {
        'FULL': 'full',           # Collect all evidence
        'INCREMENTAL': 'incremental',  # Collect only new evidence since last collection
        'DELTA': 'delta'          # Collect only changes since last collection
    }

    # Schedule frequencies
    SCHEDULE_FREQUENCIES = {
        'HOURLY': 'hourly',
        'DAILY': 'daily',
        'WEEKLY': 'weekly',
        'MONTHLY': 'monthly',
        'QUARTERLY': 'quarterly',
        'ANNUALLY': 'annually',
        'CUSTOM': 'custom'
    }

    def __init__(self, collectors_dir: Optional[str] = None,
                 templates_dir: Optional[str] = None,
                 schedules_dir: Optional[str] = None):
        """
        Initialize the Collector Manager.

        Args:
            collectors_dir: Path to a directory containing collector implementations
            templates_dir: Path to a directory containing collection templates
            schedules_dir: Path to a directory containing collection schedules
        """
        logger.info("Initializing Collector Manager")

        # Initialize the collectors dictionary
        self.collectors: Dict[str, Callable] = {}

        # Initialize the collection templates dictionary
        self.templates: Dict[str, Dict[str, Any]] = {}

        # Initialize the collection schedules dictionary
        self.schedules: Dict[str, Dict[str, Any]] = {}

        # Initialize the schedule threads dictionary
        self.schedule_threads: Dict[str, threading.Thread] = {}

        # Initialize the schedule stop events dictionary
        self.schedule_stop_events: Dict[str, threading.Event] = {}

        # Initialize the last collection times dictionary
        self.last_collection_times: Dict[str, Dict[str, datetime.datetime]] = {}

        # Initialize the collection history dictionary
        self.collection_history: Dict[str, List[Dict[str, Any]]] = {}

        # Set the templates directory
        self.templates_dir = templates_dir or os.path.join(os.getcwd(), 'collection_templates')

        # Create the templates directory if it doesn't exist
        os.makedirs(self.templates_dir, exist_ok=True)

        # Set the schedules directory
        self.schedules_dir = schedules_dir or os.path.join(os.getcwd(), 'collection_schedules')

        # Create the schedules directory if it doesn't exist
        os.makedirs(self.schedules_dir, exist_ok=True)

        # Register default collectors
        self._register_default_collectors()

        # Load custom collectors if provided
        if collectors_dir and os.path.exists(collectors_dir):
            self._load_collectors_from_directory(collectors_dir)

        # Load collection templates
        self._load_templates()

        # Load collection schedules
        self._load_schedules()

        # Start scheduled collections
        self._start_scheduled_collections()

        logger.info(f"Collector Manager initialized with {len(self.collectors)} collectors, {len(self.templates)} templates, and {len(self.schedules)} schedules")

    def _register_default_collectors(self) -> None:
        """Register default collector implementations."""
        # File system collectors
        self.register_collector('file_system', self._collect_from_file_system)

        # Database collectors
        self.register_collector('database', self._collect_from_database)

        # API collectors
        self.register_collector('api', self._collect_from_api)

        # Cloud service collectors
        self.register_collector('aws', self._collect_from_aws)
        self.register_collector('azure', self._collect_from_azure)
        self.register_collector('gcp', self._collect_from_gcp)

        # System collectors
        self.register_collector('system_logs', self._collect_from_system_logs)
        self.register_collector('system_config', self._collect_from_system_config)

        # Application collectors
        self.register_collector('application_logs', self._collect_from_application_logs)
        self.register_collector('application_config', self._collect_from_application_config)

        # Issue tracking collectors
        self.register_collector('jira', self._collect_from_jira)
        self.register_collector('github', self._collect_from_github)
        self.register_collector('servicenow', self._collect_from_servicenow)

        # CI/CD collectors
        self.register_collector('jenkins', self._collect_from_jenkins)
        self.register_collector('github_actions', self._collect_from_github_actions)

        # Container collectors - commented out until implemented
        # self.register_collector('docker', self._collect_from_docker)
        # self.register_collector('kubernetes', self._collect_from_kubernetes)

    def _load_collectors_from_directory(self, directory: str) -> None:
        """
        Load collector implementations from a directory.

        Args:
            directory: Path to the directory containing collector modules
        """
        try:
            # Get all Python files in the directory
            collector_files = [f for f in os.listdir(directory) if f.endswith('.py') and not f.startswith('__')]

            for collector_file in collector_files:
                try:
                    # Import the module
                    module_name = collector_file[:-3]  # Remove .py extension
                    module_path = os.path.join(directory, collector_file)
                    spec = importlib.util.spec_from_file_location(module_name, module_path)
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)

                    # Find all collector functions in the module
                    for attr_name in dir(module):
                        attr = getattr(module, attr_name)
                        if callable(attr) and attr_name.startswith('collect_'):
                            # Register the collector
                            collector_id = attr_name[8:]  # Remove 'collect_' prefix
                            self.register_collector(collector_id, attr)
                            logger.info(f"Loaded collector {collector_id} from {collector_file}")

                except Exception as e:
                    logger.error(f"Failed to load collectors from {collector_file}: {e}")

        except Exception as e:
            logger.error(f"Failed to load collectors from directory {directory}: {e}")

    def register_collector(self, collector_id: str, collector_func: Callable) -> None:
        """
        Register a collector implementation.

        Args:
            collector_id: The ID of the collector
            collector_func: The collector implementation function
        """
        self.collectors[collector_id] = collector_func
        logger.info(f"Registered collector: {collector_id}")

    def collect(self, collector_id: str, parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Collect evidence using a specific collector.

        Args:
            collector_id: The ID of the collector
            parameters: Parameters for the collector

        Returns:
            The collected evidence

        Raises:
            ValueError: If the collector does not exist
        """
        logger.info(f"Collecting evidence using collector: {collector_id}")

        if collector_id not in self.collectors:
            raise ValueError(f"Collector not found: {collector_id}")

        try:
            # Execute the collector
            collector_func = self.collectors[collector_id]
            evidence_data = collector_func(parameters or {})

            logger.info(f"Evidence collected successfully using collector: {collector_id}")

            return evidence_data
        except Exception as e:
            logger.error(f"Failed to collect evidence using collector {collector_id}: {e}")
            raise

    def get_collector_types(self) -> List[str]:
        """
        Get all registered collector IDs.

        Returns:
            List of collector IDs
        """
        return list(self.collectors.keys())

    def _load_templates(self) -> None:
        """Load collection templates from disk."""
        try:
            # Get all JSON files in the templates directory
            template_files = [f for f in os.listdir(self.templates_dir) if f.endswith('.json')]

            for template_file in template_files:
                try:
                    # Load the template
                    file_path = os.path.join(self.templates_dir, template_file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        template = json.load(f)

                    # Get the template ID from the filename (without extension)
                    template_id = os.path.splitext(template_file)[0]

                    # Add the template to the in-memory dictionary
                    self.templates[template_id] = template

                    logger.info(f"Loaded collection template: {template_id}")

                except Exception as e:
                    logger.error(f"Failed to load collection template from {template_file}: {e}")

            logger.info(f"Loaded {len(self.templates)} collection templates")

        except Exception as e:
            logger.error(f"Failed to load collection templates from directory {self.templates_dir}: {e}")

    def _save_template(self, template_id: str) -> None:
        """
        Save a collection template to disk.

        Args:
            template_id: The ID of the template
        """
        try:
            # Get the template
            template = self.templates.get(template_id)

            if not template:
                logger.warning(f"No template found with ID: {template_id}")
                return

            # Save the template to disk
            file_path = os.path.join(self.templates_dir, f"{template_id}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(template, f, indent=2)

            logger.info(f"Saved collection template: {template_id}")

        except Exception as e:
            logger.error(f"Failed to save collection template {template_id}: {e}")

    def _load_schedules(self) -> None:
        """Load collection schedules from disk."""
        try:
            # Get all JSON files in the schedules directory
            schedule_files = [f for f in os.listdir(self.schedules_dir) if f.endswith('.json')]

            for schedule_file in schedule_files:
                try:
                    # Load the schedule
                    file_path = os.path.join(self.schedules_dir, schedule_file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        schedule = json.load(f)

                    # Get the schedule ID from the filename (without extension)
                    schedule_id = os.path.splitext(schedule_file)[0]

                    # Add the schedule to the in-memory dictionary
                    self.schedules[schedule_id] = schedule

                    logger.info(f"Loaded collection schedule: {schedule_id}")

                except Exception as e:
                    logger.error(f"Failed to load collection schedule from {schedule_file}: {e}")

            logger.info(f"Loaded {len(self.schedules)} collection schedules")

        except Exception as e:
            logger.error(f"Failed to load collection schedules from directory {self.schedules_dir}: {e}")

    def _save_schedule(self, schedule_id: str) -> None:
        """
        Save a collection schedule to disk.

        Args:
            schedule_id: The ID of the schedule
        """
        try:
            # Get the schedule
            schedule = self.schedules.get(schedule_id)

            if not schedule:
                logger.warning(f"No schedule found with ID: {schedule_id}")
                return

            # Save the schedule to disk
            file_path = os.path.join(self.schedules_dir, f"{schedule_id}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(schedule, f, indent=2)

            logger.info(f"Saved collection schedule: {schedule_id}")

        except Exception as e:
            logger.error(f"Failed to save collection schedule {schedule_id}: {e}")

    def _start_scheduled_collections(self) -> None:
        """Start all scheduled collections."""
        for schedule_id, schedule in self.schedules.items():
            # Skip disabled schedules
            if not schedule.get('enabled', True):
                logger.info(f"Skipping disabled schedule: {schedule_id}")
                continue

            # Start the schedule
            self._start_schedule(schedule_id)

    def _start_schedule(self, schedule_id: str) -> None:
        """
        Start a scheduled collection.

        Args:
            schedule_id: The ID of the schedule
        """
        # Get the schedule
        schedule = self.schedules.get(schedule_id)

        if not schedule:
            logger.warning(f"No schedule found with ID: {schedule_id}")
            return

        # Check if the schedule is already running
        if schedule_id in self.schedule_threads and self.schedule_threads[schedule_id].is_alive():
            logger.warning(f"Schedule {schedule_id} is already running")
            return

        # Create a stop event
        stop_event = threading.Event()
        self.schedule_stop_events[schedule_id] = stop_event

        # Create and start the schedule thread
        thread = threading.Thread(
            target=self._run_schedule,
            args=(schedule_id, stop_event),
            daemon=True
        )
        self.schedule_threads[schedule_id] = thread
        thread.start()

        logger.info(f"Started schedule: {schedule_id}")

    def _stop_schedule(self, schedule_id: str) -> None:
        """
        Stop a scheduled collection.

        Args:
            schedule_id: The ID of the schedule
        """
        # Check if the schedule is running
        if schedule_id not in self.schedule_threads or not self.schedule_threads[schedule_id].is_alive():
            logger.warning(f"Schedule {schedule_id} is not running")
            return

        # Set the stop event
        self.schedule_stop_events[schedule_id].set()

        # Wait for the thread to stop
        self.schedule_threads[schedule_id].join(timeout=5)

        # Check if the thread is still alive
        if self.schedule_threads[schedule_id].is_alive():
            logger.warning(f"Schedule {schedule_id} did not stop gracefully")
        else:
            logger.info(f"Stopped schedule: {schedule_id}")

        # Remove the thread and stop event
        del self.schedule_threads[schedule_id]
        del self.schedule_stop_events[schedule_id]

    def _run_schedule(self, schedule_id: str, stop_event: threading.Event) -> None:
        """
        Run a scheduled collection.

        Args:
            schedule_id: The ID of the schedule
            stop_event: Event to signal the thread to stop
        """
        # Get the schedule
        schedule = self.schedules.get(schedule_id)

        if not schedule:
            logger.warning(f"No schedule found with ID: {schedule_id}")
            return

        # Get the schedule parameters
        frequency = schedule.get('frequency')
        template_id = schedule.get('template_id')
        collector_id = schedule.get('collector_id')
        parameters = schedule.get('parameters', {})
        strategy = schedule.get('strategy', self.COLLECTION_STRATEGIES['FULL'])

        # Initialize the last collection time
        if schedule_id not in self.last_collection_times:
            self.last_collection_times[schedule_id] = {}

        # Initialize the collection history
        if schedule_id not in self.collection_history:
            self.collection_history[schedule_id] = []

        # Calculate the interval based on the frequency
        interval_seconds = self._calculate_interval(frequency, schedule)

        # Run the schedule until stopped
        while not stop_event.is_set():
            try:
                # Check if it's time to collect
                now = datetime.datetime.now(datetime.timezone.utc)
                last_collection_time = self.last_collection_times.get(schedule_id, {}).get(collector_id)

                if last_collection_time is None or (now - last_collection_time).total_seconds() >= interval_seconds:
                    # Collect the evidence
                    if template_id:
                        # Use the template
                        evidence_data = self.collect_with_template(template_id, strategy)
                    else:
                        # Use the collector directly
                        evidence_data = self.collect_with_strategy(collector_id, parameters, strategy, last_collection_time)

                    # Update the last collection time
                    self.last_collection_times.setdefault(schedule_id, {})[collector_id] = now

                    # Add to the collection history
                    self.collection_history.setdefault(schedule_id, []).append({
                        'timestamp': now.isoformat(),
                        'collector_id': collector_id,
                        'template_id': template_id,
                        'strategy': strategy,
                        'result': evidence_data
                    })

                    logger.info(f"Scheduled collection completed for schedule: {schedule_id}")

            except Exception as e:
                logger.error(f"Error in scheduled collection for schedule {schedule_id}: {e}")

            # Wait for the next interval or until stopped
            stop_event.wait(min(interval_seconds, 60))  # Check at least every minute

    def _calculate_interval(self, frequency: str, schedule: Dict[str, Any]) -> int:
        """
        Calculate the interval in seconds based on the frequency.

        Args:
            frequency: The frequency of the schedule
            schedule: The schedule configuration

        Returns:
            The interval in seconds
        """
        if frequency == self.SCHEDULE_FREQUENCIES['HOURLY']:
            return 3600  # 1 hour
        elif frequency == self.SCHEDULE_FREQUENCIES['DAILY']:
            return 86400  # 24 hours
        elif frequency == self.SCHEDULE_FREQUENCIES['WEEKLY']:
            return 604800  # 7 days
        elif frequency == self.SCHEDULE_FREQUENCIES['MONTHLY']:
            return 2592000  # 30 days
        elif frequency == self.SCHEDULE_FREQUENCIES['QUARTERLY']:
            return 7776000  # 90 days
        elif frequency == self.SCHEDULE_FREQUENCIES['ANNUALLY']:
            return 31536000  # 365 days
        elif frequency == self.SCHEDULE_FREQUENCIES['CUSTOM']:
            # Get the custom interval in seconds
            return schedule.get('interval_seconds', 86400)  # Default to daily
        else:
            logger.warning(f"Unknown frequency: {frequency}, defaulting to daily")
            return 86400  # Default to daily

    def create_template(self, template_id: str, name: str, description: str,
                       collector_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new collection template.

        Args:
            template_id: The ID of the template
            name: The name of the template
            description: The description of the template
            collector_id: The ID of the collector to use
            parameters: The parameters for the collector

        Returns:
            The created template

        Raises:
            ValueError: If the template ID already exists
            ValueError: If the collector does not exist
        """
        # Check if the template ID already exists
        if template_id in self.templates:
            raise ValueError(f"Template ID already exists: {template_id}")

        # Check if the collector exists
        if collector_id not in self.collectors:
            raise ValueError(f"Collector not found: {collector_id}")

        # Create the template
        template = {
            'id': template_id,
            'name': name,
            'description': description,
            'collector_id': collector_id,
            'parameters': parameters,
            'created_at': datetime.datetime.now(datetime.timezone.utc).isoformat(),
            'updated_at': datetime.datetime.now(datetime.timezone.utc).isoformat()
        }

        # Add the template to the in-memory dictionary
        self.templates[template_id] = template

        # Save the template to disk
        self._save_template(template_id)

        logger.info(f"Created collection template: {template_id}")

        return template

    def update_template(self, template_id: str, name: Optional[str] = None,
                       description: Optional[str] = None, collector_id: Optional[str] = None,
                       parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Update an existing collection template.

        Args:
            template_id: The ID of the template
            name: The new name of the template
            description: The new description of the template
            collector_id: The new collector ID
            parameters: The new parameters for the collector

        Returns:
            The updated template

        Raises:
            ValueError: If the template does not exist
            ValueError: If the collector does not exist
        """
        # Check if the template exists
        if template_id not in self.templates:
            raise ValueError(f"Template not found: {template_id}")

        # Get the template
        template = self.templates[template_id]

        # Update the template
        if name is not None:
            template['name'] = name

        if description is not None:
            template['description'] = description

        if collector_id is not None:
            # Check if the collector exists
            if collector_id not in self.collectors:
                raise ValueError(f"Collector not found: {collector_id}")

            template['collector_id'] = collector_id

        if parameters is not None:
            template['parameters'] = parameters

        # Update the timestamp
        template['updated_at'] = datetime.datetime.now(datetime.timezone.utc).isoformat()

        # Save the template to disk
        self._save_template(template_id)

        logger.info(f"Updated collection template: {template_id}")

        return template

    def delete_template(self, template_id: str) -> None:
        """
        Delete a collection template.

        Args:
            template_id: The ID of the template

        Raises:
            ValueError: If the template does not exist
        """
        # Check if the template exists
        if template_id not in self.templates:
            raise ValueError(f"Template not found: {template_id}")

        # Remove the template from the in-memory dictionary
        del self.templates[template_id]

        # Remove the template file from disk
        file_path = os.path.join(self.templates_dir, f"{template_id}.json")
        if os.path.exists(file_path):
            os.remove(file_path)

        logger.info(f"Deleted collection template: {template_id}")

    def get_template(self, template_id: str) -> Dict[str, Any]:
        """
        Get a collection template.

        Args:
            template_id: The ID of the template

        Returns:
            The template

        Raises:
            ValueError: If the template does not exist
        """
        # Check if the template exists
        if template_id not in self.templates:
            raise ValueError(f"Template not found: {template_id}")

        return self.templates[template_id]

    def get_templates(self) -> List[Dict[str, Any]]:
        """
        Get all collection templates.

        Returns:
            List of templates
        """
        return list(self.templates.values())

    def create_schedule(self, schedule_id: str, name: str, description: str,
                       frequency: str, collector_id: Optional[str] = None,
                       template_id: Optional[str] = None, parameters: Optional[Dict[str, Any]] = None,
                       strategy: str = 'full', enabled: bool = True) -> Dict[str, Any]:
        """
        Create a new collection schedule.

        Args:
            schedule_id: The ID of the schedule
            name: The name of the schedule
            description: The description of the schedule
            frequency: The frequency of the schedule
            collector_id: The ID of the collector to use
            template_id: The ID of the template to use
            parameters: The parameters for the collector
            strategy: The collection strategy
            enabled: Whether the schedule is enabled

        Returns:
            The created schedule

        Raises:
            ValueError: If the schedule ID already exists
            ValueError: If the collector does not exist
            ValueError: If the template does not exist
            ValueError: If neither collector_id nor template_id is provided
        """
        # Check if the schedule ID already exists
        if schedule_id in self.schedules:
            raise ValueError(f"Schedule ID already exists: {schedule_id}")

        # Check if either collector_id or template_id is provided
        if collector_id is None and template_id is None:
            raise ValueError("Either collector_id or template_id must be provided")

        # Check if the collector exists
        if collector_id is not None and collector_id not in self.collectors:
            raise ValueError(f"Collector not found: {collector_id}")

        # Check if the template exists
        if template_id is not None and template_id not in self.templates:
            raise ValueError(f"Template not found: {template_id}")

        # Create the schedule
        schedule = {
            'id': schedule_id,
            'name': name,
            'description': description,
            'frequency': frequency,
            'collector_id': collector_id,
            'template_id': template_id,
            'parameters': parameters or {},
            'strategy': strategy,
            'enabled': enabled,
            'created_at': datetime.datetime.now(datetime.timezone.utc).isoformat(),
            'updated_at': datetime.datetime.now(datetime.timezone.utc).isoformat()
        }

        # Add the schedule to the in-memory dictionary
        self.schedules[schedule_id] = schedule

        # Save the schedule to disk
        self._save_schedule(schedule_id)

        # Start the schedule if enabled
        if enabled:
            self._start_schedule(schedule_id)

        logger.info(f"Created collection schedule: {schedule_id}")

        return schedule

    def update_schedule(self, schedule_id: str, name: Optional[str] = None,
                       description: Optional[str] = None, frequency: Optional[str] = None,
                       collector_id: Optional[str] = None, template_id: Optional[str] = None,
                       parameters: Optional[Dict[str, Any]] = None, strategy: Optional[str] = None,
                       enabled: Optional[bool] = None) -> Dict[str, Any]:
        """
        Update an existing collection schedule.

        Args:
            schedule_id: The ID of the schedule
            name: The new name of the schedule
            description: The new description of the schedule
            frequency: The new frequency of the schedule
            collector_id: The new collector ID
            template_id: The new template ID
            parameters: The new parameters for the collector
            strategy: The new collection strategy
            enabled: Whether the schedule is enabled

        Returns:
            The updated schedule

        Raises:
            ValueError: If the schedule does not exist
            ValueError: If the collector does not exist
            ValueError: If the template does not exist
        """
        # Check if the schedule exists
        if schedule_id not in self.schedules:
            raise ValueError(f"Schedule not found: {schedule_id}")

        # Get the schedule
        schedule = self.schedules[schedule_id]

        # Check if the schedule is running
        was_running = schedule.get('enabled', True) and schedule_id in self.schedule_threads and self.schedule_threads[schedule_id].is_alive()

        # Stop the schedule if it's running
        if was_running:
            self._stop_schedule(schedule_id)

        # Update the schedule
        if name is not None:
            schedule['name'] = name

        if description is not None:
            schedule['description'] = description

        if frequency is not None:
            schedule['frequency'] = frequency

        if collector_id is not None:
            # Check if the collector exists
            if collector_id not in self.collectors:
                raise ValueError(f"Collector not found: {collector_id}")

            schedule['collector_id'] = collector_id

        if template_id is not None:
            # Check if the template exists
            if template_id not in self.templates:
                raise ValueError(f"Template not found: {template_id}")

            schedule['template_id'] = template_id

        if parameters is not None:
            schedule['parameters'] = parameters

        if strategy is not None:
            schedule['strategy'] = strategy

        if enabled is not None:
            schedule['enabled'] = enabled

        # Update the timestamp
        schedule['updated_at'] = datetime.datetime.now(datetime.timezone.utc).isoformat()

        # Save the schedule to disk
        self._save_schedule(schedule_id)

        # Restart the schedule if it was running and is still enabled
        if was_running and schedule.get('enabled', True):
            self._start_schedule(schedule_id)
        # Start the schedule if it wasn't running but is now enabled
        elif not was_running and schedule.get('enabled', True):
            self._start_schedule(schedule_id)

        logger.info(f"Updated collection schedule: {schedule_id}")

        return schedule

    def delete_schedule(self, schedule_id: str) -> None:
        """
        Delete a collection schedule.

        Args:
            schedule_id: The ID of the schedule

        Raises:
            ValueError: If the schedule does not exist
        """
        # Check if the schedule exists
        if schedule_id not in self.schedules:
            raise ValueError(f"Schedule not found: {schedule_id}")

        # Stop the schedule if it's running
        if schedule_id in self.schedule_threads and self.schedule_threads[schedule_id].is_alive():
            self._stop_schedule(schedule_id)

        # Remove the schedule from the in-memory dictionary
        del self.schedules[schedule_id]

        # Remove the schedule file from disk
        file_path = os.path.join(self.schedules_dir, f"{schedule_id}.json")
        if os.path.exists(file_path):
            os.remove(file_path)

        logger.info(f"Deleted collection schedule: {schedule_id}")

    def get_schedule(self, schedule_id: str) -> Dict[str, Any]:
        """
        Get a collection schedule.

        Args:
            schedule_id: The ID of the schedule

        Returns:
            The schedule

        Raises:
            ValueError: If the schedule does not exist
        """
        # Check if the schedule exists
        if schedule_id not in self.schedules:
            raise ValueError(f"Schedule not found: {schedule_id}")

        return self.schedules[schedule_id]

    def get_schedules(self) -> List[Dict[str, Any]]:
        """
        Get all collection schedules.

        Returns:
            List of schedules
        """
        return list(self.schedules.values())

    def collect_with_template(self, template_id: str, strategy: str = 'full') -> Dict[str, Any]:
        """
        Collect evidence using a template.

        Args:
            template_id: The ID of the template
            strategy: The collection strategy

        Returns:
            The collected evidence

        Raises:
            ValueError: If the template does not exist
        """
        # Check if the template exists
        if template_id not in self.templates:
            raise ValueError(f"Template not found: {template_id}")

        # Get the template
        template = self.templates[template_id]

        # Get the collector ID and parameters
        collector_id = template.get('collector_id')
        parameters = template.get('parameters', {})

        # Collect the evidence
        return self.collect_with_strategy(collector_id, parameters, strategy)

    def collect_with_strategy(self, collector_id: str, parameters: Dict[str, Any],
                             strategy: str = 'full',
                             last_collection_time: Optional[datetime.datetime] = None) -> Dict[str, Any]:
        """
        Collect evidence using a specific strategy.

        Args:
            collector_id: The ID of the collector
            parameters: The parameters for the collector
            strategy: The collection strategy
            last_collection_time: The time of the last collection

        Returns:
            The collected evidence

        Raises:
            ValueError: If the collector does not exist
            ValueError: If the strategy is not supported
        """
        # Check if the strategy is supported
        if strategy not in self.COLLECTION_STRATEGIES.values():
            raise ValueError(f"Unsupported collection strategy: {strategy}")

        # Add strategy-specific parameters
        strategy_parameters = copy.deepcopy(parameters)

        if strategy == self.COLLECTION_STRATEGIES['INCREMENTAL'] and last_collection_time:
            # Add the last collection time to the parameters
            strategy_parameters['last_collection_time'] = last_collection_time.isoformat()

        elif strategy == self.COLLECTION_STRATEGIES['DELTA'] and last_collection_time:
            # Add the last collection time to the parameters
            strategy_parameters['last_collection_time'] = last_collection_time.isoformat()
            # Add the delta flag to the parameters
            strategy_parameters['delta_only'] = True

        # Collect the evidence
        return self.collect(collector_id, strategy_parameters)

    # Default collector implementations

    def _collect_from_file_system(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from the file system.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from file system")

        # Get parameters
        file_path = parameters.get('file_path')
        if not file_path:
            raise ValueError("Missing required parameter: file_path")

        # In a real implementation, this would read the file and collect evidence
        # For now, return a placeholder result
        return {
            'type': 'file',
            'source': 'file_system',
            'file_path': file_path,
            'content': f"Content of {file_path}",
            'metadata': {
                'size': 1024,
                'created_at': '2023-06-15T10:30:00Z',
                'modified_at': '2023-06-15T10:30:00Z'
            }
        }

    def _collect_from_database(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from a database.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from database")

        # Get parameters
        connection_string = parameters.get('connection_string')
        query = parameters.get('query')

        if not connection_string:
            raise ValueError("Missing required parameter: connection_string")

        if not query:
            raise ValueError("Missing required parameter: query")

        # In a real implementation, this would execute the query and collect evidence
        # For now, return a placeholder result
        return {
            'type': 'database',
            'source': 'database',
            'connection_string': connection_string,
            'query': query,
            'results': [
                {'id': 1, 'name': 'Record 1'},
                {'id': 2, 'name': 'Record 2'},
                {'id': 3, 'name': 'Record 3'}
            ],
            'metadata': {
                'rows': 3,
                'execution_time': 0.1,
                'executed_at': '2023-06-15T10:30:00Z'
            }
        }

    def _collect_from_api(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from an API.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from API")

        # Get parameters
        url = parameters.get('url')
        method = parameters.get('method', 'GET')
        headers = parameters.get('headers', {})
        body = parameters.get('body')

        if not url:
            raise ValueError("Missing required parameter: url")

        # In a real implementation, this would make an API request and collect evidence
        # For now, return a placeholder result
        return {
            'type': 'api',
            'source': 'api',
            'url': url,
            'method': method,
            'headers': headers,
            'body': body,
            'response': {
                'status_code': 200,
                'headers': {'Content-Type': 'application/json'},
                'body': '{"status": "success", "data": {"id": 1, "name": "API Response"}}'
            },
            'metadata': {
                'request_time': 0.2,
                'requested_at': '2023-06-15T10:30:00Z'
            }
        }

    def _collect_from_aws(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from AWS.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from AWS")

        # Get parameters
        service = parameters.get('service')
        region = parameters.get('region')
        action = parameters.get('action')
        params = parameters.get('params', {})

        if not service:
            raise ValueError("Missing required parameter: service")

        if not action:
            raise ValueError("Missing required parameter: action")

        # In a real implementation, this would make an AWS API request and collect evidence
        # For now, return a placeholder result
        return {
            'type': 'cloud',
            'source': 'aws',
            'service': service,
            'region': region,
            'action': action,
            'params': params,
            'response': {
                'status': 'success',
                'data': {'id': 1, 'name': 'AWS Response'}
            },
            'metadata': {
                'request_time': 0.3,
                'requested_at': '2023-06-15T10:30:00Z'
            }
        }

    def _collect_from_azure(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from Azure.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from Azure")

        # Get parameters
        resource = parameters.get('resource')
        subscription_id = parameters.get('subscription_id')
        resource_group = parameters.get('resource_group')
        api_version = parameters.get('api_version')

        if not resource:
            raise ValueError("Missing required parameter: resource")

        if not subscription_id:
            raise ValueError("Missing required parameter: subscription_id")

        # In a real implementation, this would make an Azure API request and collect evidence
        # For now, return a placeholder result
        return {
            'type': 'cloud',
            'source': 'azure',
            'resource': resource,
            'subscription_id': subscription_id,
            'resource_group': resource_group,
            'api_version': api_version,
            'response': {
                'status': 'success',
                'data': {'id': 1, 'name': 'Azure Response'}
            },
            'metadata': {
                'request_time': 0.3,
                'requested_at': '2023-06-15T10:30:00Z'
            }
        }

    def _collect_from_gcp(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from GCP.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from GCP")

        # Get parameters
        service = parameters.get('service')
        project_id = parameters.get('project_id')
        method = parameters.get('method')
        params = parameters.get('params', {})

        if not service:
            raise ValueError("Missing required parameter: service")

        if not project_id:
            raise ValueError("Missing required parameter: project_id")

        if not method:
            raise ValueError("Missing required parameter: method")

        # In a real implementation, this would make a GCP API request and collect evidence
        # For now, return a placeholder result
        return {
            'type': 'cloud',
            'source': 'gcp',
            'service': service,
            'project_id': project_id,
            'method': method,
            'params': params,
            'response': {
                'status': 'success',
                'data': {'id': 1, 'name': 'GCP Response'}
            },
            'metadata': {
                'request_time': 0.3,
                'requested_at': '2023-06-15T10:30:00Z'
            }
        }

    def _collect_from_system_logs(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from system logs.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from system logs")

        # Get parameters
        log_file = parameters.get('log_file')
        start_time = parameters.get('start_time')
        end_time = parameters.get('end_time')
        filter_pattern = parameters.get('filter_pattern')

        if not log_file:
            raise ValueError("Missing required parameter: log_file")

        # In a real implementation, this would read system logs and collect evidence
        # For now, return a placeholder result
        return {
            'type': 'logs',
            'source': 'system_logs',
            'log_file': log_file,
            'start_time': start_time,
            'end_time': end_time,
            'filter_pattern': filter_pattern,
            'entries': [
                {'timestamp': '2023-06-15T10:25:00Z', 'level': 'INFO', 'message': 'System started'},
                {'timestamp': '2023-06-15T10:26:00Z', 'level': 'INFO', 'message': 'User logged in'},
                {'timestamp': '2023-06-15T10:27:00Z', 'level': 'ERROR', 'message': 'Failed to connect to database'}
            ],
            'metadata': {
                'entries_count': 3,
                'collected_at': '2023-06-15T10:30:00Z'
            }
        }

    def _collect_from_system_config(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from system configuration.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from system configuration")

        # Get parameters
        config_path = parameters.get('config_path')

        if not config_path:
            raise ValueError("Missing required parameter: config_path")

        # In a real implementation, this would read system configuration and collect evidence
        # For now, return a placeholder result
        return {
            'type': 'config',
            'source': 'system_config',
            'config_path': config_path,
            'config': {
                'setting1': 'value1',
                'setting2': 'value2',
                'setting3': 'value3'
            },
            'metadata': {
                'collected_at': '2023-06-15T10:30:00Z'
            }
        }

    def _collect_from_application_logs(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from application logs.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from application logs")

        # Get parameters
        application = parameters.get('application')
        log_file = parameters.get('log_file')
        start_time = parameters.get('start_time')
        end_time = parameters.get('end_time')
        filter_pattern = parameters.get('filter_pattern')

        if not application:
            raise ValueError("Missing required parameter: application")

        if not log_file:
            raise ValueError("Missing required parameter: log_file")

        # In a real implementation, this would read application logs and collect evidence
        # For now, return a placeholder result
        return {
            'type': 'logs',
            'source': 'application_logs',
            'application': application,
            'log_file': log_file,
            'start_time': start_time,
            'end_time': end_time,
            'filter_pattern': filter_pattern,
            'entries': [
                {'timestamp': '2023-06-15T10:25:00Z', 'level': 'INFO', 'message': 'Application started'},
                {'timestamp': '2023-06-15T10:26:00Z', 'level': 'INFO', 'message': 'User authenticated'},
                {'timestamp': '2023-06-15T10:27:00Z', 'level': 'ERROR', 'message': 'Failed to process request'}
            ],
            'metadata': {
                'entries_count': 3,
                'collected_at': '2023-06-15T10:30:00Z'
            }
        }

    def _collect_from_application_config(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from application configuration.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from application configuration")

        # Get parameters
        application = parameters.get('application')
        config_path = parameters.get('config_path')

        if not application:
            raise ValueError("Missing required parameter: application")

        if not config_path:
            raise ValueError("Missing required parameter: config_path")

        # In a real implementation, this would read application configuration and collect evidence
        # For now, return a placeholder result
        return {
            'type': 'config',
            'source': 'application_config',
            'application': application,
            'config_path': config_path,
            'config': {
                'setting1': 'value1',
                'setting2': 'value2',
                'setting3': 'value3'
            },
            'metadata': {
                'collected_at': '2023-06-15T10:30:00Z'
            }
        }

    def _collect_from_jira(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from Jira.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from Jira")

        # Get parameters
        url = parameters.get('url')
        project_key = parameters.get('project_key')
        issue_type = parameters.get('issue_type')
        jql = parameters.get('jql')
        api_token = parameters.get('api_token')
        username = parameters.get('username')
        max_results = parameters.get('max_results', 100)
        last_collection_time = parameters.get('last_collection_time')
        delta_only = parameters.get('delta_only', False)

        if not url:
            raise ValueError("Missing required parameter: url")

        if not api_token:
            raise ValueError("Missing required parameter: api_token")

        if not username:
            raise ValueError("Missing required parameter: username")

        # If neither project_key nor jql is provided, raise an error
        if not project_key and not jql:
            raise ValueError("Either project_key or jql must be provided")

        # Build JQL query if not provided
        if not jql:
            jql = f"project = {project_key}"
            if issue_type:
                jql += f" AND issuetype = {issue_type}"

            # For incremental or delta collection
            if last_collection_time:
                jql += f" AND updated >= '{last_collection_time}'"

        # In a real implementation, this would make a Jira API request and collect evidence
        # For now, return a placeholder result
        return {
            'type': 'issue_tracking',
            'source': 'jira',
            'url': url,
            'project_key': project_key,
            'issue_type': issue_type,
            'jql': jql,
            'max_results': max_results,
            'issues': [
                {
                    'key': 'PROJ-1',
                    'summary': 'First issue',
                    'status': 'Open',
                    'created': '2023-06-01T10:00:00Z',
                    'updated': '2023-06-15T10:00:00Z',
                    'assignee': 'john.doe',
                    'reporter': 'jane.smith',
                    'priority': 'High',
                    'labels': ['compliance', 'security'],
                    'fields': {
                        'custom_field_1': 'value1',
                        'custom_field_2': 'value2'
                    }
                },
                {
                    'key': 'PROJ-2',
                    'summary': 'Second issue',
                    'status': 'In Progress',
                    'created': '2023-06-02T10:00:00Z',
                    'updated': '2023-06-14T10:00:00Z',
                    'assignee': 'jane.smith',
                    'reporter': 'john.doe',
                    'priority': 'Medium',
                    'labels': ['compliance'],
                    'fields': {
                        'custom_field_1': 'value3',
                        'custom_field_2': 'value4'
                    }
                }
            ],
            'metadata': {
                'total_issues': 2,
                'collection_strategy': 'full' if not delta_only else 'delta',
                'collected_at': datetime.datetime.now(datetime.timezone.utc).isoformat()
            }
        }

    def _collect_from_github(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from GitHub.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from GitHub")

        # Get parameters
        owner = parameters.get('owner')
        repo = parameters.get('repo')
        token = parameters.get('token')
        issue_state = parameters.get('issue_state', 'all')  # all, open, closed
        pr_state = parameters.get('pr_state', 'all')  # all, open, closed
        include_issues = parameters.get('include_issues', True)
        include_prs = parameters.get('include_prs', True)
        include_commits = parameters.get('include_commits', False)
        include_workflows = parameters.get('include_workflows', False)
        max_results = parameters.get('max_results', 100)
        last_collection_time = parameters.get('last_collection_time')
        delta_only = parameters.get('delta_only', False)

        if not owner:
            raise ValueError("Missing required parameter: owner")

        if not repo:
            raise ValueError("Missing required parameter: repo")

        if not token:
            raise ValueError("Missing required parameter: token")

        # In a real implementation, this would make GitHub API requests and collect evidence
        # For now, return a placeholder result
        result = {
            'type': 'source_control',
            'source': 'github',
            'owner': owner,
            'repo': repo,
            'metadata': {
                'collection_strategy': 'full' if not delta_only else 'delta',
                'collected_at': datetime.datetime.now(datetime.timezone.utc).isoformat()
            }
        }

        # Add issues if requested
        if include_issues:
            result['issues'] = [
                {
                    'number': 1,
                    'title': 'First issue',
                    'state': 'open',
                    'created_at': '2023-06-01T10:00:00Z',
                    'updated_at': '2023-06-15T10:00:00Z',
                    'closed_at': None,
                    'user': 'john-doe',
                    'assignees': ['jane-smith'],
                    'labels': ['bug', 'compliance'],
                    'body': 'This is the first issue'
                },
                {
                    'number': 2,
                    'title': 'Second issue',
                    'state': 'closed',
                    'created_at': '2023-06-02T10:00:00Z',
                    'updated_at': '2023-06-14T10:00:00Z',
                    'closed_at': '2023-06-14T10:00:00Z',
                    'user': 'jane-smith',
                    'assignees': ['john-doe'],
                    'labels': ['enhancement', 'compliance'],
                    'body': 'This is the second issue'
                }
            ]
            result['metadata']['total_issues'] = 2

        # Add PRs if requested
        if include_prs:
            result['pull_requests'] = [
                {
                    'number': 3,
                    'title': 'First PR',
                    'state': 'open',
                    'created_at': '2023-06-03T10:00:00Z',
                    'updated_at': '2023-06-15T10:00:00Z',
                    'closed_at': None,
                    'merged_at': None,
                    'user': 'john-doe',
                    'assignees': ['jane-smith'],
                    'labels': ['enhancement', 'compliance'],
                    'body': 'This is the first PR',
                    'base': 'main',
                    'head': 'feature-branch'
                },
                {
                    'number': 4,
                    'title': 'Second PR',
                    'state': 'closed',
                    'created_at': '2023-06-04T10:00:00Z',
                    'updated_at': '2023-06-14T10:00:00Z',
                    'closed_at': '2023-06-14T10:00:00Z',
                    'merged_at': '2023-06-14T10:00:00Z',
                    'user': 'jane-smith',
                    'assignees': ['john-doe'],
                    'labels': ['bug', 'compliance'],
                    'body': 'This is the second PR',
                    'base': 'main',
                    'head': 'bugfix-branch'
                }
            ]
            result['metadata']['total_prs'] = 2

        # Add commits if requested
        if include_commits:
            result['commits'] = [
                {
                    'sha': 'abc123',
                    'message': 'First commit',
                    'author': 'john-doe',
                    'date': '2023-06-05T10:00:00Z',
                    'files_changed': 3,
                    'additions': 100,
                    'deletions': 50
                },
                {
                    'sha': 'def456',
                    'message': 'Second commit',
                    'author': 'jane-smith',
                    'date': '2023-06-06T10:00:00Z',
                    'files_changed': 2,
                    'additions': 50,
                    'deletions': 20
                }
            ]
            result['metadata']['total_commits'] = 2

        # Add workflows if requested
        if include_workflows:
            result['workflows'] = [
                {
                    'id': 1,
                    'name': 'CI',
                    'path': '.github/workflows/ci.yml',
                    'state': 'active',
                    'runs': [
                        {
                            'id': 101,
                            'status': 'completed',
                            'conclusion': 'success',
                            'created_at': '2023-06-07T10:00:00Z',
                            'updated_at': '2023-06-07T10:10:00Z',
                            'trigger': 'push',
                            'branch': 'main'
                        },
                        {
                            'id': 102,
                            'status': 'completed',
                            'conclusion': 'failure',
                            'created_at': '2023-06-08T10:00:00Z',
                            'updated_at': '2023-06-08T10:10:00Z',
                            'trigger': 'pull_request',
                            'branch': 'feature-branch'
                        }
                    ]
                },
                {
                    'id': 2,
                    'name': 'CD',
                    'path': '.github/workflows/cd.yml',
                    'state': 'active',
                    'runs': [
                        {
                            'id': 201,
                            'status': 'completed',
                            'conclusion': 'success',
                            'created_at': '2023-06-09T10:00:00Z',
                            'updated_at': '2023-06-09T10:10:00Z',
                            'trigger': 'push',
                            'branch': 'main'
                        }
                    ]
                }
            ]
            result['metadata']['total_workflows'] = 2

        return result

    def _collect_from_servicenow(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from ServiceNow.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from ServiceNow")

        # Get parameters
        instance_url = parameters.get('instance_url')
        username = parameters.get('username')
        password = parameters.get('password')
        table = parameters.get('table')
        query = parameters.get('query')
        fields = parameters.get('fields')
        max_results = parameters.get('max_results', 100)
        last_collection_time = parameters.get('last_collection_time')
        delta_only = parameters.get('delta_only', False)

        if not instance_url:
            raise ValueError("Missing required parameter: instance_url")

        if not username:
            raise ValueError("Missing required parameter: username")

        if not password:
            raise ValueError("Missing required parameter: password")

        if not table:
            raise ValueError("Missing required parameter: table")

        # Build query if not provided and last_collection_time is available
        if not query and last_collection_time and delta_only:
            query = f"sys_updated_on>={last_collection_time}"

        # In a real implementation, this would make a ServiceNow API request and collect evidence
        # For now, return a placeholder result

        # Determine the record type based on the table
        record_type = 'incident'
        if table == 'change_request':
            record_type = 'change'
        elif table == 'problem':
            record_type = 'problem'
        elif table == 'sc_request':
            record_type = 'request'
        elif table == 'cmdb_ci':
            record_type = 'configuration_item'

        return {
            'type': 'itsm',
            'source': 'servicenow',
            'instance_url': instance_url,
            'table': table,
            'query': query,
            'fields': fields,
            'records': [
                {
                    'sys_id': 'abc123',
                    'number': 'INC0001',
                    'short_description': 'First incident',
                    'description': 'This is the first incident',
                    'state': 'Open',
                    'priority': '1 - Critical',
                    'assigned_to': 'john.doe',
                    'opened_by': 'jane.smith',
                    'opened_at': '2023-06-01T10:00:00Z',
                    'updated_at': '2023-06-15T10:00:00Z',
                    'closed_at': None,
                    'category': 'Software',
                    'subcategory': 'Application',
                    'custom_fields': {
                        'compliance_related': 'true',
                        'security_incident': 'false'
                    }
                },
                {
                    'sys_id': 'def456',
                    'number': 'INC0002',
                    'short_description': 'Second incident',
                    'description': 'This is the second incident',
                    'state': 'Closed',
                    'priority': '2 - High',
                    'assigned_to': 'jane.smith',
                    'opened_by': 'john.doe',
                    'opened_at': '2023-06-02T10:00:00Z',
                    'updated_at': '2023-06-14T10:00:00Z',
                    'closed_at': '2023-06-14T10:00:00Z',
                    'category': 'Hardware',
                    'subcategory': 'Server',
                    'custom_fields': {
                        'compliance_related': 'true',
                        'security_incident': 'true'
                    }
                }
            ],
            'metadata': {
                'total_records': 2,
                'record_type': record_type,
                'collection_strategy': 'full' if not delta_only else 'delta',
                'collected_at': datetime.datetime.now(datetime.timezone.utc).isoformat()
            }
        }

    def _collect_from_jenkins(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from Jenkins.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from Jenkins")

        # Get parameters
        url = parameters.get('url')
        username = parameters.get('username')
        api_token = parameters.get('api_token')
        job_name = parameters.get('job_name')
        include_builds = parameters.get('include_builds', True)
        include_test_results = parameters.get('include_test_results', False)
        include_artifacts = parameters.get('include_artifacts', False)
        max_builds = parameters.get('max_builds', 10)
        last_collection_time = parameters.get('last_collection_time')
        delta_only = parameters.get('delta_only', False)

        if not url:
            raise ValueError("Missing required parameter: url")

        if not username:
            raise ValueError("Missing required parameter: username")

        if not api_token:
            raise ValueError("Missing required parameter: api_token")

        if not job_name:
            raise ValueError("Missing required parameter: job_name")

        # In a real implementation, this would make Jenkins API requests and collect evidence
        # For now, return a placeholder result
        result = {
            'type': 'ci_cd',
            'source': 'jenkins',
            'url': url,
            'job_name': job_name,
            'metadata': {
                'collection_strategy': 'full' if not delta_only else 'delta',
                'collected_at': datetime.datetime.now(datetime.timezone.utc).isoformat()
            }
        }

        # Add builds if requested
        if include_builds:
            result['builds'] = [
                {
                    'number': 1,
                    'result': 'SUCCESS',
                    'timestamp': '2023-06-01T10:00:00Z',
                    'duration': 120,
                    'url': f"{url}/job/{job_name}/1/",
                    'parameters': {
                        'BRANCH': 'main',
                        'ENV': 'dev'
                    },
                    'changes': [
                        {
                            'commit_id': 'abc123',
                            'author': 'john-doe',
                            'message': 'First commit'
                        }
                    ]
                },
                {
                    'number': 2,
                    'result': 'FAILURE',
                    'timestamp': '2023-06-02T10:00:00Z',
                    'duration': 90,
                    'url': f"{url}/job/{job_name}/2/",
                    'parameters': {
                        'BRANCH': 'feature-branch',
                        'ENV': 'dev'
                    },
                    'changes': [
                        {
                            'commit_id': 'def456',
                            'author': 'jane-smith',
                            'message': 'Second commit'
                        }
                    ]
                }
            ]
            result['metadata']['total_builds'] = 2

        # Add test results if requested
        if include_test_results:
            result['test_results'] = [
                {
                    'build_number': 1,
                    'total': 100,
                    'passed': 98,
                    'failed': 2,
                    'skipped': 0,
                    'duration': 60,
                    'suites': [
                        {
                            'name': 'Unit Tests',
                            'total': 80,
                            'passed': 78,
                            'failed': 2,
                            'skipped': 0,
                            'duration': 30,
                            'cases': [
                                {
                                    'name': 'Test Case 1',
                                    'class_name': 'TestClass1',
                                    'status': 'PASSED',
                                    'duration': 0.5
                                },
                                {
                                    'name': 'Test Case 2',
                                    'class_name': 'TestClass1',
                                    'status': 'FAILED',
                                    'duration': 0.5,
                                    'error_message': 'Assertion failed'
                                }
                            ]
                        },
                        {
                            'name': 'Integration Tests',
                            'total': 20,
                            'passed': 20,
                            'failed': 0,
                            'skipped': 0,
                            'duration': 30
                        }
                    ]
                },
                {
                    'build_number': 2,
                    'total': 100,
                    'passed': 95,
                    'failed': 5,
                    'skipped': 0,
                    'duration': 65,
                    'suites': [
                        {
                            'name': 'Unit Tests',
                            'total': 80,
                            'passed': 75,
                            'failed': 5,
                            'skipped': 0,
                            'duration': 35
                        },
                        {
                            'name': 'Integration Tests',
                            'total': 20,
                            'passed': 20,
                            'failed': 0,
                            'skipped': 0,
                            'duration': 30
                        }
                    ]
                }
            ]

        # Add artifacts if requested
        if include_artifacts:
            result['artifacts'] = [
                {
                    'build_number': 1,
                    'artifacts': [
                        {
                            'name': 'app.jar',
                            'path': 'target/app.jar',
                            'size': 1024000,
                            'url': f"{url}/job/{job_name}/1/artifact/target/app.jar"
                        },
                        {
                            'name': 'test-report.xml',
                            'path': 'target/surefire-reports/TEST-TestClass1.xml',
                            'size': 5120,
                            'url': f"{url}/job/{job_name}/1/artifact/target/surefire-reports/TEST-TestClass1.xml"
                        }
                    ]
                },
                {
                    'build_number': 2,
                    'artifacts': [
                        {
                            'name': 'app.jar',
                            'path': 'target/app.jar',
                            'size': 1025000,
                            'url': f"{url}/job/{job_name}/2/artifact/target/app.jar"
                        },
                        {
                            'name': 'test-report.xml',
                            'path': 'target/surefire-reports/TEST-TestClass1.xml',
                            'size': 5200,
                            'url': f"{url}/job/{job_name}/2/artifact/target/surefire-reports/TEST-TestClass1.xml"
                        }
                    ]
                }
            ]

        return result

    def _collect_from_github_actions(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect evidence from GitHub Actions.

        Args:
            parameters: Parameters for the collector

        Returns:
            The collected evidence
        """
        logger.info("Collecting evidence from GitHub Actions")

        # Get parameters
        owner = parameters.get('owner')
        repo = parameters.get('repo')
        token = parameters.get('token')
        workflow_id = parameters.get('workflow_id')
        branch = parameters.get('branch')
        status = parameters.get('status')  # completed, in_progress, queued
        conclusion = parameters.get('conclusion')  # success, failure, cancelled, skipped
        include_jobs = parameters.get('include_jobs', True)
        include_steps = parameters.get('include_steps', False)
        include_artifacts = parameters.get('include_artifacts', False)
        max_runs = parameters.get('max_runs', 10)
        last_collection_time = parameters.get('last_collection_time')
        delta_only = parameters.get('delta_only', False)

        if not owner:
            raise ValueError("Missing required parameter: owner")

        if not repo:
            raise ValueError("Missing required parameter: repo")

        if not token:
            raise ValueError("Missing required parameter: token")

        # In a real implementation, this would make GitHub API requests and collect evidence
        # For now, return a placeholder result
        result = {
            'type': 'ci_cd',
            'source': 'github_actions',
            'owner': owner,
            'repo': repo,
            'workflow_id': workflow_id,
            'branch': branch,
            'metadata': {
                'collection_strategy': 'full' if not delta_only else 'delta',
                'collected_at': datetime.datetime.now(datetime.timezone.utc).isoformat()
            }
        }

        # Add workflow runs
        result['workflow_runs'] = [
            {
                'id': 1,
                'name': 'CI',
                'workflow_id': 123,
                'run_number': 1,
                'status': 'completed',
                'conclusion': 'success',
                'created_at': '2023-06-01T10:00:00Z',
                'updated_at': '2023-06-01T10:10:00Z',
                'url': f"https://github.com/{owner}/{repo}/actions/runs/1",
                'head_branch': 'main',
                'head_sha': 'abc123',
                'event': 'push',
                'run_attempt': 1,
                'run_started_at': '2023-06-01T10:00:00Z',
                'trigger_actor': 'john-doe'
            },
            {
                'id': 2,
                'name': 'CI',
                'workflow_id': 123,
                'run_number': 2,
                'status': 'completed',
                'conclusion': 'failure',
                'created_at': '2023-06-02T10:00:00Z',
                'updated_at': '2023-06-02T10:10:00Z',
                'url': f"https://github.com/{owner}/{repo}/actions/runs/2",
                'head_branch': 'feature-branch',
                'head_sha': 'def456',
                'event': 'pull_request',
                'run_attempt': 1,
                'run_started_at': '2023-06-02T10:00:00Z',
                'trigger_actor': 'jane-smith'
            }
        ]
        result['metadata']['total_workflow_runs'] = 2

        # Add jobs if requested
        if include_jobs:
            result['jobs'] = [
                {
                    'workflow_run_id': 1,
                    'jobs': [
                        {
                            'id': 101,
                            'name': 'build',
                            'status': 'completed',
                            'conclusion': 'success',
                            'started_at': '2023-06-01T10:00:00Z',
                            'completed_at': '2023-06-01T10:05:00Z',
                            'runner_name': 'ubuntu-latest',
                            'runner_group': 'GitHub Actions',
                            'url': f"https://github.com/{owner}/{repo}/actions/runs/1/jobs/101"
                        },
                        {
                            'id': 102,
                            'name': 'test',
                            'status': 'completed',
                            'conclusion': 'success',
                            'started_at': '2023-06-01T10:05:00Z',
                            'completed_at': '2023-06-01T10:10:00Z',
                            'runner_name': 'ubuntu-latest',
                            'runner_group': 'GitHub Actions',
                            'url': f"https://github.com/{owner}/{repo}/actions/runs/1/jobs/102"
                        }
                    ]
                },
                {
                    'workflow_run_id': 2,
                    'jobs': [
                        {
                            'id': 201,
                            'name': 'build',
                            'status': 'completed',
                            'conclusion': 'success',
                            'started_at': '2023-06-02T10:00:00Z',
                            'completed_at': '2023-06-02T10:05:00Z',
                            'runner_name': 'ubuntu-latest',
                            'runner_group': 'GitHub Actions',
                            'url': f"https://github.com/{owner}/{repo}/actions/runs/2/jobs/201"
                        },
                        {
                            'id': 202,
                            'name': 'test',
                            'status': 'completed',
                            'conclusion': 'failure',
                            'started_at': '2023-06-02T10:05:00Z',
                            'completed_at': '2023-06-02T10:10:00Z',
                            'runner_name': 'ubuntu-latest',
                            'runner_group': 'GitHub Actions',
                            'url': f"https://github.com/{owner}/{repo}/actions/runs/2/jobs/202"
                        }
                    ]
                }
            ]

        # Add steps if requested
        if include_steps and include_jobs:
            for job_group in result['jobs']:
                for job in job_group['jobs']:
                    job['steps'] = [
                        {
                            'name': 'Checkout',
                            'status': 'completed',
                            'conclusion': 'success',
                            'number': 1,
                            'started_at': job['started_at'],
                            'completed_at': job['started_at']  # Just a placeholder
                        },
                        {
                            'name': 'Setup Node.js',
                            'status': 'completed',
                            'conclusion': 'success',
                            'number': 2,
                            'started_at': job['started_at'],  # Just a placeholder
                            'completed_at': job['started_at']  # Just a placeholder
                        },
                        {
                            'name': 'Install dependencies',
                            'status': 'completed',
                            'conclusion': 'success',
                            'number': 3,
                            'started_at': job['started_at'],  # Just a placeholder
                            'completed_at': job['started_at']  # Just a placeholder
                        },
                        {
                            'name': 'Run tests',
                            'status': 'completed',
                            'conclusion': 'failure' if job['name'] == 'test' and job['conclusion'] == 'failure' else 'success',
                            'number': 4,
                            'started_at': job['started_at'],  # Just a placeholder
                            'completed_at': job['completed_at']  # Just a placeholder
                        }
                    ]

        # Add artifacts if requested
        if include_artifacts:
            result['artifacts'] = [
                {
                    'workflow_run_id': 1,
                    'artifacts': [
                        {
                            'id': 1001,
                            'name': 'build-artifacts',
                            'size_in_bytes': 1024000,
                            'url': f"https://github.com/{owner}/{repo}/actions/runs/1/artifacts/1001",
                            'created_at': '2023-06-01T10:10:00Z',
                            'expires_at': '2023-07-01T10:10:00Z'
                        },
                        {
                            'id': 1002,
                            'name': 'test-results',
                            'size_in_bytes': 5120,
                            'url': f"https://github.com/{owner}/{repo}/actions/runs/1/artifacts/1002",
                            'created_at': '2023-06-01T10:10:00Z',
                            'expires_at': '2023-07-01T10:10:00Z'
                        }
                    ]
                },
                {
                    'workflow_run_id': 2,
                    'artifacts': [
                        {
                            'id': 2001,
                            'name': 'build-artifacts',
                            'size_in_bytes': 1025000,
                            'url': f"https://github.com/{owner}/{repo}/actions/runs/2/artifacts/2001",
                            'created_at': '2023-06-02T10:10:00Z',
                            'expires_at': '2023-07-02T10:10:00Z'
                        },
                        {
                            'id': 2002,
                            'name': 'test-results',
                            'size_in_bytes': 5200,
                            'url': f"https://github.com/{owner}/{repo}/actions/runs/2/artifacts/2002",
                            'created_at': '2023-06-02T10:10:00Z',
                            'expires_at': '2023-07-02T10:10:00Z'
                        }
                    ]
                }
            ]

        return result
