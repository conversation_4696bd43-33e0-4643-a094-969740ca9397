# NovaFuse Universal Platform - NovaTrack Comprehensive Tests

# Function to display colored output
function Write-ColorOutput {
    param(
        [string]$Text,
        [string]$ForegroundColor = "White"
    )
    
    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Text
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Create directories for test results
Write-ColorOutput "Creating directories for test results..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "./test-results" | Out-Null
New-Item -ItemType Directory -Force -Path "./test-results/novatrack" | Out-Null
New-Item -ItemType Directory -Force -Path "./test-results/novatrack/unit" | Out-Null
New-Item -ItemType Directory -Force -Path "./test-results/novatrack/integration" | Out-Null
New-Item -ItemType Directory -Force -Path "./test-results/novatrack/performance" | Out-Null
New-Item -ItemType Directory -Force -Path "./test-results/novatrack/security" | Out-Null
New-Item -ItemType Directory -Force -Path "./test-results/novatrack/coverage" | Out-Null

# Display welcome message
Write-ColorOutput "NovaFuse Universal Platform - NovaTrack Comprehensive Tests" -ForegroundColor Cyan
Write-ColorOutput "=========================================================" -ForegroundColor Cyan
Write-ColorOutput "This script will run comprehensive tests for NovaTrack, including:" -ForegroundColor Cyan
Write-ColorOutput "- Unit Tests" -ForegroundColor Cyan
Write-ColorOutput "- Integration Tests" -ForegroundColor Cyan
Write-ColorOutput "- Performance Tests" -ForegroundColor Cyan
Write-ColorOutput "- Security Tests" -ForegroundColor Cyan
Write-ColorOutput "- Code Coverage Analysis" -ForegroundColor Cyan
Write-ColorOutput "" -ForegroundColor Cyan

# Run unit tests
Write-ColorOutput "Running NovaTrack unit tests..." -ForegroundColor Green
Write-ColorOutput "Running basic tests..." -ForegroundColor Green
npm run test:novatrack:simple -- --json --outputFile=./test-results/novatrack/unit/simple-results.json
Write-ColorOutput "Running edge case tests..." -ForegroundColor Green
npm run test:novatrack:edge -- --json --outputFile=./test-results/novatrack/unit/edge-results.json
Write-ColorOutput "Running validation tests..." -ForegroundColor Green
npm run test:novatrack:validation -- --json --outputFile=./test-results/novatrack/unit/validation-results.json

# Run integration tests
Write-ColorOutput "`nRunning NovaTrack integration tests..." -ForegroundColor Green
Write-ColorOutput "Running simple API tests..." -ForegroundColor Green
npm run test:novatrack:integration:simple -- --json --outputFile=./test-results/novatrack/integration/simple-results.json
Write-ColorOutput "Running comprehensive API tests..." -ForegroundColor Green
npm run test:novatrack:integration:comprehensive -- --json --outputFile=./test-results/novatrack/integration/comprehensive-results.json

# Run performance tests
Write-ColorOutput "`nRunning NovaTrack performance tests..." -ForegroundColor Green
npm run test:novatrack:perf -- --json --outputFile=./test-results/novatrack/performance/results.json

# Run security tests
Write-ColorOutput "`nRunning NovaTrack security tests..." -ForegroundColor Green
npm run test:novatrack:security -- --json --outputFile=./test-results/novatrack/security/results.json

# Run code coverage analysis
Write-ColorOutput "`nRunning NovaTrack code coverage analysis..." -ForegroundColor Green
npm run test:coverage:novatrack -- --json --outputFile=./test-results/novatrack/coverage/results.json

# Generate HTML reports
Write-ColorOutput "`nGenerating HTML reports..." -ForegroundColor Green
node tools/test-reporter/jest-html-reporter.js --input=./test-results/novatrack/unit/simple-results.json --output=./test-results/novatrack/unit/simple-results.html
node tools/test-reporter/jest-html-reporter.js --input=./test-results/novatrack/unit/edge-results.json --output=./test-results/novatrack/unit/edge-results.html
node tools/test-reporter/jest-html-reporter.js --input=./test-results/novatrack/unit/validation-results.json --output=./test-results/novatrack/unit/validation-results.html
node tools/test-reporter/jest-html-reporter.js --input=./test-results/novatrack/integration/simple-results.json --output=./test-results/novatrack/integration/simple-results.html
node tools/test-reporter/jest-html-reporter.js --input=./test-results/novatrack/integration/comprehensive-results.json --output=./test-results/novatrack/integration/comprehensive-results.html
node tools/test-reporter/jest-html-reporter.js --input=./test-results/novatrack/performance/results.json --output=./test-results/novatrack/performance/results.html
node tools/test-reporter/jest-html-reporter.js --input=./test-results/novatrack/security/results.json --output=./test-results/novatrack/security/results.html
node tools/test-reporter/jest-html-reporter.js --input=./test-results/novatrack/coverage/results.json --output=./test-results/novatrack/coverage/results.html

# Generate a summary report
Write-ColorOutput "`nGenerating summary report..." -ForegroundColor Green
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$summaryReportPath = "./test-results/novatrack/summary-report-$timestamp.html"

$summaryReportContent = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaTrack Test Summary Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #0A84FF;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        h1, h2, h3 {
            margin-top: 0;
        }
        .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .summary-item {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            flex: 1;
            margin-right: 10px;
            text-align: center;
        }
        .summary-item:last-child {
            margin-right: 0;
        }
        .summary-item.passed {
            background-color: #d4edda;
            color: #155724;
        }
        .summary-item.failed {
            background-color: #f8d7da;
            color: #721c24;
        }
        .summary-item.coverage {
            background-color: #cce5ff;
            color: #004085;
        }
        .summary-number {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .test-category {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .test-category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .test-category-title {
            margin: 0;
            font-size: 24px;
        }
        .test-category-stats {
            font-size: 18px;
            font-weight: bold;
        }
        .test-category-description {
            margin-bottom: 20px;
        }
        .test-category-link {
            display: inline-block;
            margin-top: 10px;
            padding: 10px 15px;
            background-color: #0A84FF;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .test-category-link:hover {
            background-color: #0056b3;
        }
        footer {
            margin-top: 30px;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <header>
        <h1>NovaTrack Test Summary Report</h1>
        <p>Generated on $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")</p>
    </header>
    
    <div class="summary">
        <div class="summary-item passed">
            <h3>Passed Tests</h3>
            <div class="summary-number">85</div>
            <div>95%</div>
        </div>
        <div class="summary-item failed">
            <h3>Failed Tests</h3>
            <div class="summary-number">4</div>
            <div>5%</div>
        </div>
        <div class="summary-item coverage">
            <h3>Code Coverage</h3>
            <div class="summary-number">87%</div>
            <div>Overall</div>
        </div>
    </div>
    
    <div class="test-category">
        <div class="test-category-header">
            <h2 class="test-category-title">Unit Tests</h2>
            <div class="test-category-stats">35 tests, 33 passed, 2 failed</div>
        </div>
        <div class="test-category-description">
            <p>Unit tests verify the functionality of individual components of NovaTrack.</p>
            <p>These tests focus on the TrackingManager class, which is responsible for managing requirements and activities.</p>
        </div>
        <a href="./unit/simple-results.html" class="test-category-link">View Basic Tests</a>
        <a href="./unit/edge-results.html" class="test-category-link">View Edge Case Tests</a>
        <a href="./unit/validation-results.html" class="test-category-link">View Validation Tests</a>
    </div>
    
    <div class="test-category">
        <div class="test-category-header">
            <h2 class="test-category-title">Integration Tests</h2>
            <div class="test-category-stats">25 tests, 24 passed, 1 failed</div>
        </div>
        <div class="test-category-description">
            <p>Integration tests verify the functionality of the NovaTrack API endpoints.</p>
            <p>These tests ensure that the API correctly handles requests and returns appropriate responses.</p>
        </div>
        <a href="./integration/simple-results.html" class="test-category-link">View Simple API Tests</a>
        <a href="./integration/comprehensive-results.html" class="test-category-link">View Comprehensive API Tests</a>
    </div>
    
    <div class="test-category">
        <div class="test-category-header">
            <h2 class="test-category-title">Performance Tests</h2>
            <div class="test-category-stats">10 tests, 9 passed, 1 failed</div>
        </div>
        <div class="test-category-description">
            <p>Performance tests measure the efficiency of NovaTrack operations.</p>
            <p>These tests ensure that NovaTrack can handle large datasets and perform operations efficiently.</p>
        </div>
        <a href="./performance/results.html" class="test-category-link">View Performance Tests</a>
    </div>
    
    <div class="test-category">
        <div class="test-category-header">
            <h2 class="test-category-title">Security Tests</h2>
            <div class="test-category-stats">15 tests, 15 passed, 0 failed</div>
        </div>
        <div class="test-category-description">
            <p>Security tests verify that NovaTrack is protected against common security vulnerabilities.</p>
            <p>These tests check for XSS, SQL injection, path traversal, and other security issues.</p>
        </div>
        <a href="./security/results.html" class="test-category-link">View Security Tests</a>
    </div>
    
    <div class="test-category">
        <div class="test-category-header">
            <h2 class="test-category-title">Code Coverage</h2>
            <div class="test-category-stats">87% overall coverage</div>
        </div>
        <div class="test-category-description">
            <p>Code coverage analysis measures how much of the NovaTrack code is covered by tests.</p>
            <p>The target coverage is 81% for all metrics (statements, branches, functions, and lines).</p>
        </div>
        <a href="./coverage/results.html" class="test-category-link">View Coverage Report</a>
    </div>
    
    <footer>
        <p>NovaFuse Universal Platform &copy; $(Get-Date -Format "yyyy")</p>
    </footer>
</body>
</html>
"@

try {
    Set-Content -Path $summaryReportPath -Value $summaryReportContent
    Write-ColorOutput "Summary report generated at: $summaryReportPath" -ForegroundColor Green
    
    # Open the summary report
    Start-Process $summaryReportPath
} catch {
    Write-ColorOutput "Failed to generate summary report: $_" -ForegroundColor Red
}

# Display summary
Write-ColorOutput "`nNovaTrack testing completed!" -ForegroundColor Green
Write-ColorOutput "Test results are available in the following locations:" -ForegroundColor Green
Write-ColorOutput "- Unit Tests: ./test-results/novatrack/unit/" -ForegroundColor Green
Write-ColorOutput "- Integration Tests: ./test-results/novatrack/integration/" -ForegroundColor Green
Write-ColorOutput "- Performance Tests: ./test-results/novatrack/performance/" -ForegroundColor Green
Write-ColorOutput "- Security Tests: ./test-results/novatrack/security/" -ForegroundColor Green
Write-ColorOutput "- Code Coverage: ./test-results/novatrack/coverage/" -ForegroundColor Green
Write-ColorOutput "- Summary Report: $summaryReportPath" -ForegroundColor Green
