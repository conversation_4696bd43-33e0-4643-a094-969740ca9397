/**
 * Risk API Routes
 * 
 * This file defines the API routes for risk-related functionality.
 */

const express = require('express');
const router = express.Router();
const { riskScenarios, riskHeatmapData } = require('../../models');

// Feature flag middleware
const checkFeatureAccess = (req, res, next) => {
  // In a real implementation, this would check the user's product tier
  // For simulation, we'll check if the feature flag is enabled in the request
  const productTier = req.headers['x-product-tier'] || 'novaPrime';
  
  // For simulation, we'll allow all features for novaPrime
  if (productTier === 'novaPrime') {
    return next();
  }
  
  // For other tiers, we'll check the feature flag configuration
  // This is a simplified implementation for simulation purposes
  const featureFlags = {
    novaCore: ['privacy-management', 'security-assessment', 'regulatory-compliance'],
    novaShield: ['security-assessment', 'control-testing'],
    novaLearn: ['regulatory-compliance'],
    novaAssistAI: ['privacy-management', 'security-assessment', 'regulatory-compliance', 'control-testing', 'esg']
  };
  
  if (featureFlags[productTier] && (
    featureFlags[productTier].includes('security-assessment') || 
    featureFlags[productTier].includes('regulatory-compliance')
  )) {
    return next();
  }
  
  return res.status(403).json({
    error: 'Feature not available',
    message: `This feature is not available in your current plan (${productTier})`
  });
};

/**
 * @route GET /risk/scenarios
 * @description Get all risk scenarios
 * @access Private
 */
router.get('/scenarios', checkFeatureAccess, (req, res) => {
  // Get query parameters for filtering
  const { category, status, minRisk } = req.query;
  
  // Filter scenarios based on query parameters
  let filteredScenarios = [...riskScenarios];
  
  if (category) {
    filteredScenarios = filteredScenarios.filter(scenario => 
      scenario.category.toLowerCase() === category.toLowerCase()
    );
  }
  
  if (status) {
    filteredScenarios = filteredScenarios.filter(scenario => 
      scenario.status.toLowerCase() === status.toLowerCase()
    );
  }
  
  if (minRisk) {
    filteredScenarios = filteredScenarios.filter(scenario => {
      const riskLevels = {
        'Low': 1,
        'Medium': 2,
        'High': 3,
        'Critical': 4
      };
      
      return riskLevels[scenario.overallRisk] >= riskLevels[minRisk];
    });
  }
  
  // Return filtered scenarios
  res.json({
    data: filteredScenarios.map(scenario => ({
      id: scenario.id,
      name: scenario.name,
      description: scenario.description,
      category: scenario.category,
      likelihood: scenario.likelihood,
      impact: scenario.impact,
      overallRisk: scenario.overallRisk,
      status: scenario.status,
      affectedServicesCount: scenario.affectedServices.length,
      mitigationControlsCount: scenario.mitigationControls.length
    }))
  });
});

/**
 * @route GET /risk/scenarios/:id
 * @description Get a specific risk scenario by ID
 * @access Private
 */
router.get('/scenarios/:id', checkFeatureAccess, (req, res) => {
  const { id } = req.params;
  
  // Find the scenario by ID
  const scenario = riskScenarios.find(s => s.id === id);
  
  if (!scenario) {
    return res.status(404).json({
      error: 'Risk scenario not found',
      message: `No risk scenario found with ID ${id}`
    });
  }
  
  // Return the scenario
  res.json({
    data: scenario
  });
});

/**
 * @route GET /risk/heatmap
 * @description Get risk heatmap data
 * @access Private
 */
router.get('/heatmap', checkFeatureAccess, (req, res) => {
  // Return the heatmap data
  res.json({
    data: riskHeatmapData
  });
});

/**
 * @route GET /risk/summary
 * @description Get a summary of risk status
 * @access Private
 */
router.get('/summary', checkFeatureAccess, (req, res) => {
  // Calculate summary statistics
  const summary = {
    totalScenarios: riskScenarios.length,
    riskLevels: {
      Critical: 0,
      High: 0,
      Medium: 0,
      Low: 0
    },
    categories: {},
    affectedServices: {},
    complianceImpact: {}
  };
  
  // Process each scenario
  riskScenarios.forEach(scenario => {
    // Count by risk level
    summary.riskLevels[scenario.overallRisk]++;
    
    // Count by category
    if (!summary.categories[scenario.category]) {
      summary.categories[scenario.category] = 0;
    }
    summary.categories[scenario.category]++;
    
    // Count affected services
    scenario.affectedServices.forEach(service => {
      if (!summary.affectedServices[service.name]) {
        summary.affectedServices[service.name] = 0;
      }
      summary.affectedServices[service.name]++;
    });
    
    // Count compliance impact
    scenario.complianceImpact.forEach(impact => {
      if (!summary.complianceImpact[impact.framework]) {
        summary.complianceImpact[impact.framework] = 0;
      }
      summary.complianceImpact[impact.framework]++;
    });
  });
  
  // Calculate overall risk score (0-100)
  const riskWeights = {
    Critical: 1.0,
    High: 0.7,
    Medium: 0.4,
    Low: 0.1
  };
  
  let weightedSum = 0;
  let totalWeight = 0;
  
  Object.keys(summary.riskLevels).forEach(level => {
    weightedSum += summary.riskLevels[level] * riskWeights[level];
    totalWeight += summary.riskLevels[level];
  });
  
  summary.overallRiskScore = totalWeight > 0 
    ? Math.round((weightedSum / totalWeight) * 100) 
    : 0;
  
  // Return the summary
  res.json({
    data: summary
  });
});

module.exports = router;
