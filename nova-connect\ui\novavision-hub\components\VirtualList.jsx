/**
 * VirtualList Component
 * 
 * A component for efficiently rendering large lists by only rendering visible items.
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { usePerformance } from '../performance/usePerformance';

/**
 * VirtualList component
 * 
 * @param {Object} props - Component props
 * @param {Array} props.items - List items
 * @param {Function} props.renderItem - Function to render an item
 * @param {number} [props.itemHeight=50] - Item height in pixels
 * @param {number} [props.overscan=5] - Number of items to render outside of the visible area
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @param {string} [props.scrollToItemId] - ID of item to scroll to
 * @param {Function} [props.getItemId] - Function to get item ID
 * @param {boolean} [props.useWindowScroll=false] - Whether to use window scroll instead of container scroll
 * @returns {React.ReactElement} VirtualList component
 */
const VirtualList = ({
  items,
  renderItem,
  itemHeight = 50,
  overscan = 5,
  className = '',
  style = {},
  scrollToItemId,
  getItemId = (item) => item.id,
  useWindowScroll = false
}) => {
  const { measureOperation } = usePerformance('VirtualList');
  
  // Refs
  const containerRef = useRef(null);
  const scrollPositionRef = useRef(0);
  const resizeObserverRef = useRef(null);
  
  // State
  const [containerHeight, setContainerHeight] = useState(0);
  const [scrollTop, setScrollTop] = useState(0);
  
  // Calculate visible items
  const visibleItems = measureOperation('calculateVisibleItems', () => {
    if (containerHeight === 0) return [];
    
    const totalHeight = items.length * itemHeight;
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    
    const visibleItems = [];
    
    for (let i = startIndex; i <= endIndex; i++) {
      visibleItems.push({
        index: i,
        item: items[i],
        style: {
          position: 'absolute',
          top: i * itemHeight,
          left: 0,
          right: 0,
          height: itemHeight
        }
      });
    }
    
    return visibleItems;
  });
  
  // Handle scroll
  const handleScroll = useCallback(() => {
    const scrollPosition = useWindowScroll
      ? window.scrollY
      : containerRef.current.scrollTop;
    
    // Only update if scroll position changed significantly
    if (Math.abs(scrollPosition - scrollPositionRef.current) > 5) {
      scrollPositionRef.current = scrollPosition;
      setScrollTop(scrollPosition);
    }
  }, [useWindowScroll]);
  
  // Handle resize
  const handleResize = useCallback(() => {
    if (containerRef.current) {
      const height = useWindowScroll
        ? window.innerHeight
        : containerRef.current.clientHeight;
      
      setContainerHeight(height);
    }
  }, [useWindowScroll]);
  
  // Initialize
  useEffect(() => {
    // Initial measurements
    handleResize();
    
    // Add scroll event listener
    const scrollTarget = useWindowScroll ? window : containerRef.current;
    scrollTarget.addEventListener('scroll', handleScroll, { passive: true });
    
    // Add resize observer
    if ('ResizeObserver' in window) {
      resizeObserverRef.current = new ResizeObserver(handleResize);
      resizeObserverRef.current.observe(
        useWindowScroll ? document.documentElement : containerRef.current
      );
    } else {
      // Fallback for browsers without ResizeObserver
      window.addEventListener('resize', handleResize);
    }
    
    // Cleanup
    return () => {
      scrollTarget.removeEventListener('scroll', handleScroll);
      
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      } else {
        window.removeEventListener('resize', handleResize);
      }
    };
  }, [handleScroll, handleResize, useWindowScroll]);
  
  // Scroll to item
  useEffect(() => {
    if (scrollToItemId && containerRef.current) {
      const itemIndex = items.findIndex(item => getItemId(item) === scrollToItemId);
      
      if (itemIndex !== -1) {
        const scrollTarget = useWindowScroll ? window : containerRef.current;
        const scrollPosition = itemIndex * itemHeight;
        
        scrollTarget.scrollTo({
          top: scrollPosition,
          behavior: 'smooth'
        });
      }
    }
  }, [scrollToItemId, items, getItemId, itemHeight, useWindowScroll]);
  
  // Calculate total height
  const totalHeight = items.length * itemHeight;
  
  // Render
  return (
    <div
      ref={containerRef}
      className={`relative overflow-auto ${className}`}
      style={{
        height: useWindowScroll ? '100%' : '100%',
        ...style
      }}
      data-testid="virtual-list"
    >
      <div
        className="relative"
        style={{ height: totalHeight }}
        data-testid="virtual-list-inner"
      >
        {visibleItems.map(({ index, item, style }) => (
          <div
            key={getItemId(item) || index}
            style={style}
            data-testid={`virtual-list-item-${index}`}
          >
            {renderItem(item, index)}
          </div>
        ))}
      </div>
    </div>
  );
};

VirtualList.propTypes = {
  items: PropTypes.array.isRequired,
  renderItem: PropTypes.func.isRequired,
  itemHeight: PropTypes.number,
  overscan: PropTypes.number,
  className: PropTypes.string,
  style: PropTypes.object,
  scrollToItemId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  getItemId: PropTypes.func,
  useWindowScroll: PropTypes.bool
};

export default VirtualList;
