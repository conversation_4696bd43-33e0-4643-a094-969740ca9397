apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}-novafuse-uac
  labels:
    app: novafuse-uac
    component: api
    release: {{ .Release.Name }}
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: helm
spec:
  replicas: {{ .Values.replicas }}
  selector:
    matchLabels:
      app: novafuse-uac
      component: api
      release: {{ .Release.Name }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: novafuse-uac
        component: api
        release: {{ .Release.Name }}
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/path: "/metrics"
        prometheus.io/port: "3001"
    spec:
      serviceAccountName: {{ .Release.Name }}-novafuse-uac-sa
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: novafuse-uac
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 3001
              protocol: TCP
          env:
            - name: NODE_ENV
              value: "production"
            - name: PORT
              value: "3001"
            - name: HOST
              value: "0.0.0.0"
            - name: LOG_LEVEL
              value: {{ .Values.logLevel | quote }}
            - name: MONGODB_URI
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-novafuse-uac-mongodb
                  key: uri
            - name: REDIS_URI
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-novafuse-uac-redis
                  key: uri
            - name: API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-novafuse-uac-secrets
                  key: api-key
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-novafuse-uac-secrets
                  key: jwt-secret
            - name: CORS_ORIGIN
              value: {{ .Values.corsOrigin | quote }}
            - name: CLUSTER_ENABLED
              value: {{ .Values.clusterEnabled | quote }}
            - name: CACHE_ENABLED
              value: {{ .Values.cacheEnabled | quote }}
            - name: COMPRESSION_ENABLED
              value: {{ .Values.compressionEnabled | quote }}
            - name: RATE_LIMIT_ENABLED
              value: {{ .Values.rateLimitEnabled | quote }}
            - name: RATE_LIMIT_WINDOW_MS
              value: {{ .Values.rateLimitWindowMs | quote }}
            - name: RATE_LIMIT_MAX
              value: {{ .Values.rateLimitMax | quote }}
            - name: HELMET_ENABLED
              value: {{ .Values.helmetEnabled | quote }}
            - name: CSRF_ENABLED
              value: {{ .Values.csrfEnabled | quote }}
            - name: IP_FILTERING_ENABLED
              value: {{ .Values.ipFilteringEnabled | quote }}
            - name: FEATURE_FLAG_ENABLED
              value: "true"
            - name: FEATURE_FLAG_SOURCE
              value: "file"
            - name: DEFAULT_TIER
              value: {{ .Values.tier | quote }}
            - name: GCP_PROJECT_ID
              value: {{ .Values.gcpProjectId | quote }}
            - name: GCP_REGION
              value: {{ .Values.gcpRegion | quote }}
            - name: GCP_MONITORING_ENABLED
              value: "true"
            - name: GCP_LOGGING_ENABLED
              value: "true"
            - name: GCP_ERROR_REPORTING_ENABLED
              value: "true"
            - name: GCP_TRACING_ENABLED
              value: "true"
            - name: GCP_PROFILING_ENABLED
              value: "true"
            - name: GCP_SECRET_MANAGER_ENABLED
              value: "true"
            - name: SERVICE_NAME
              value: "novafuse-uac"
            - name: SERVICE_VERSION
              value: {{ .Values.image.tag | quote }}
          volumeMounts:
            - name: config
              mountPath: /app/config
            - name: feature-flags
              mountPath: /app/config/feature_flags.json
              subPath: feature_flags.json
            - name: tmp
              mountPath: /tmp
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          startupProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 12
          resources:
            limits:
              cpu: {{ .Values.resources.limits.cpu }}
              memory: {{ .Values.resources.limits.memory }}
            requests:
              cpu: {{ .Values.resources.requests.cpu }}
              memory: {{ .Values.resources.requests.memory }}
      volumes:
        - name: config
          configMap:
            name: {{ .Release.Name }}-novafuse-uac-config
        - name: feature-flags
          configMap:
            name: {{ .Release.Name }}-novafuse-uac-feature-flags
        - name: tmp
          emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - novafuse-uac
                topologyKey: kubernetes.io/hostname
      terminationGracePeriodSeconds: 60
