/**
 * Button Component
 * 
 * A customizable button component that follows the NovaConnect design system.
 */

import React from 'react';
import PropTypes from 'prop-types';
import './Button.css';

/**
 * Button component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.variant='primary'] - Button variant (primary, secondary, success, danger, warning, info, link)
 * @param {string} [props.size='md'] - Button size (sm, md, lg)
 * @param {boolean} [props.outlined=false] - Whether the button is outlined
 * @param {boolean} [props.disabled=false] - Whether the button is disabled
 * @param {boolean} [props.fullWidth=false] - Whether the button takes full width
 * @param {string} [props.type='button'] - Button type (button, submit, reset)
 * @param {Function} [props.onClick] - Click handler
 * @param {React.ReactNode} props.children - Button content
 * @param {string} [props.className] - Additional CSS class
 * @returns {React.ReactElement} Button component
 */
const Button = ({
  variant = 'primary',
  size = 'md',
  outlined = false,
  disabled = false,
  fullWidth = false,
  type = 'button',
  onClick,
  children,
  className = '',
  ...rest
}) => {
  const baseClass = 'nova-button';
  const variantClass = `${baseClass}--${variant}`;
  const sizeClass = `${baseClass}--${size}`;
  const outlinedClass = outlined ? `${baseClass}--outlined` : '';
  const fullWidthClass = fullWidth ? `${baseClass}--full-width` : '';
  
  const buttonClasses = [
    baseClass,
    variantClass,
    sizeClass,
    outlinedClass,
    fullWidthClass,
    className
  ].filter(Boolean).join(' ');
  
  return (
    <button
      type={type}
      className={buttonClasses}
      disabled={disabled}
      onClick={onClick}
      {...rest}
    >
      {children}
    </button>
  );
};

Button.propTypes = {
  variant: PropTypes.oneOf(['primary', 'secondary', 'success', 'danger', 'warning', 'info', 'link']),
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  outlined: PropTypes.bool,
  disabled: PropTypes.bool,
  fullWidth: PropTypes.bool,
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
  onClick: PropTypes.func,
  children: PropTypes.node.isRequired,
  className: PropTypes.string
};

export default Button;
