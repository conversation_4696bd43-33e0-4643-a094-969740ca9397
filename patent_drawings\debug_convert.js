const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('=== Mermaid to SVG Debug ===');
console.log(`Current directory: ${__dirname}`);

// Check if mermaid_diagrams directory exists
const mermaidDir = path.join(__dirname, 'mermaid_diagrams');
console.log(`\nChecking for mermaid_diagrams directory: ${mermaidDir}`);

if (!fs.existsSync(mermaidDir)) {
    console.error('❌ Error: mermaid_diagrams directory not found');
    process.exit(1);
}
console.log('✅ mermaid_diagrams directory exists');

// Check if there are .mmd files
const mermaidFiles = fs.readdirSync(mermaidDir)
    .filter(file => file.endsWith('.mmd'));

if (mermaidFiles.length === 0) {
    console.error('❌ Error: No .mmd files found in mermaid_diagrams directory');
    process.exit(1);
}
console.log(`✅ Found ${mermaidFiles.length} .mmd files`);

// Create SVG directory if it doesn't exist
const svgDir = path.join(__dirname, 'svg');
if (!fs.existsSync(svgDir)) {
    console.log(`\nCreating SVG directory: ${svgDir}`);
    fs.mkdirSync(svgDir);
} else {
    console.log(`\nSVG directory exists: ${svgDir}`);
}

// Test with just the first file first
const testFile = mermaidFiles[0];
const inputPath = path.join(mermaidDir, testFile);
const outputPath = path.join(svgDir, testFile.replace(/\.mmd$/, '.svg'));

console.log(`\n=== Testing with file: ${testFile} ===`);
console.log(`Input: ${inputPath}`);
console.log(`Output: ${outputPath}`);

// Check if input file exists and is readable
try {
    const stats = fs.statSync(inputPath);
    console.log(`✅ Input file exists (${stats.size} bytes)`);
    
    // Read first few lines of the file
    const content = fs.readFileSync(inputPath, 'utf8');
    console.log(`\n=== File Preview (first 5 lines) ===`);
    console.log(content.split('\n').slice(0, 5).join('\n'));
    console.log('...\n');
    
} catch (err) {
    console.error(`❌ Error reading input file: ${err.message}`);
    process.exit(1);
}

// Try to convert the file
console.log('\n=== Attempting conversion ===');
try {
    const cmd = `npx @mermaid-js/mermaid-cli -i "${inputPath}" -o "${outputPath}"`;
    console.log(`Running: ${cmd}`);
    
    const output = execSync(cmd, { stdio: 'pipe' });
    console.log('\n✅ Conversion command executed successfully');
    console.log('Output:', output.toString());
    
    // Check if output file was created
    if (fs.existsSync(outputPath)) {
        const stats = fs.statSync(outputPath);
        console.log(`✅ Output file created: ${outputPath} (${stats.size} bytes)`);
    } else {
        console.error('❌ Output file was not created');
    }
    
} catch (error) {
    console.error('\n❌ Conversion failed:');
    console.error('Error message:', error.message);
    console.error('\nError output:');
    console.error(error.stderr ? error.stderr.toString() : 'No error output');
    console.error('\nStandard output:');
    console.error(error.stdout ? error.stdout.toString() : 'No standard output');
}
