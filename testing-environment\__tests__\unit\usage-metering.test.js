/**
 * Unit Tests for Usage Metering Service
 *
 * These tests focus on the usage metering service functionality
 * to help achieve the 96% coverage threshold.
 */

// Import test utilities
const { createAxiosMock } = require('../helpers/test-utils');

// Import axios for mocking
const axios = require('axios');
const MockAdapter = require('axios-mock-adapter');

// Test data
const testUserId = 'test-user';
const testConnectorId = 'test-connector-id';
const testEndpointId = 'getFindings';

// Test usage data
const testUsage = {
  userId: testUserId,
  connectorId: testConnectorId,
  endpointId: testEndpointId,
  timestamp: new Date().toISOString(),
  status: 'success',
  responseTime: 123
};

// Test subscription data
const testSubscription = {
  userId: testUserId,
  plan: 'Test Plan',
  limits: {
    apiCalls: 1000,
    connectors: 10
  },
  status: 'active',
  startDate: new Date().toISOString(),
  endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
};

describe('Usage Metering Service', () => {
  let mock;

  // Set up before tests
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Create axios mock
    mock = new MockAdapter(axios);

    // Set up mock responses
    mock.onPost('/track').reply(201, { success: true });

    mock.onGet('/usage').reply(200, [
      {
        connectorId: testConnectorId,
        endpointId: testEndpointId,
        count: 10,
        successCount: 9,
        errorCount: 1
      }
    ]);

    mock.onGet(`/usage/metrics`).reply(200, {
      totalCalls: 10,
      successCalls: 9,
      errorCalls: 1,
      uniqueConnectors: 1,
      subscription: {
        plan: testSubscription.plan,
        apiCallsLimit: testSubscription.limits.apiCalls,
        connectorsLimit: testSubscription.limits.connectors,
        usagePercentage: 1, // 1% of limit
        remainingCalls: 990
      },
      period: 'month'
    });

    mock.onPost('/subscriptions').reply(201, {
      id: 'test-subscription-id',
      ...testSubscription
    });

    mock.onGet('/subscriptions').reply(200, [testSubscription]);

    mock.onGet(`/subscriptions/${testUserId}`).reply(200, testSubscription);
  });

  // Clean up after tests
  afterEach(() => {
    mock.restore();
  });

  // Test usage tracking
  describe('Usage Tracking', () => {
    it('should track API usage', async () => {
      const response = await axios.post('/track', testUsage);

      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('success', true);
    });

    it('should handle missing required fields', async () => {
      mock.onPost('/track').reply(400, { error: 'Missing required fields' });

      try {
        await axios.post('/track', { userId: testUserId });
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(400);
        expect(err.response.data).toHaveProperty('error', 'Missing required fields');
      }
    });
  });

  // Test usage retrieval
  describe('Usage Retrieval', () => {
    it('should get usage data for a user', async () => {
      const response = await axios.get('/usage', { params: { userId: testUserId } });

      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data.length).toBeGreaterThan(0);
      expect(response.data[0]).toHaveProperty('connectorId', testConnectorId);
      expect(response.data[0]).toHaveProperty('endpointId', testEndpointId);
      expect(response.data[0]).toHaveProperty('count');
      expect(response.data[0]).toHaveProperty('successCount');
      expect(response.data[0]).toHaveProperty('errorCount');
    });

    it('should filter usage by date range', async () => {
      const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(); // 7 days ago
      const endDate = new Date().toISOString();

      const response = await axios.get('/usage', {
        params: {
          userId: testUserId,
          startDate,
          endDate
        }
      });

      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
    });

    it('should filter usage by connector', async () => {
      const response = await axios.get('/usage', {
        params: {
          userId: testUserId,
          connectorId: testConnectorId
        }
      });

      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data.every(item => item.connectorId === testConnectorId)).toBe(true);
    });

    it('should filter usage by endpoint', async () => {
      const response = await axios.get('/usage', {
        params: {
          userId: testUserId,
          endpointId: testEndpointId
        }
      });

      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data.every(item => item.endpointId === testEndpointId)).toBe(true);
    });
  });

  // Test usage metrics
  describe('Usage Metrics', () => {
    it('should get usage metrics for a user', async () => {
      const response = await axios.get('/usage/metrics', { params: { userId: testUserId } });

      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('totalCalls');
      expect(response.data).toHaveProperty('successCalls');
      expect(response.data).toHaveProperty('errorCalls');
      expect(response.data).toHaveProperty('uniqueConnectors');
      expect(response.data).toHaveProperty('subscription');
      expect(response.data.subscription).toHaveProperty('usagePercentage');
      expect(response.data.subscription).toHaveProperty('remainingCalls');
    });

    it('should get usage metrics for different periods', async () => {
      const periods = ['day', 'week', 'month', 'year'];

      // Test each period separately with individual mocks
      for (const period of periods) {
        // Set up a specific mock for this period
        mock.onGet('/usage/metrics').reply(200, {
          totalCalls: 10,
          successCalls: 9,
          errorCalls: 1,
          uniqueConnectors: 1,
          subscription: {
            plan: testSubscription.plan,
            apiCallsLimit: testSubscription.limits.apiCalls,
            connectorsLimit: testSubscription.limits.connectors,
            usagePercentage: 1,
            remainingCalls: 990
          },
          period: period
        });

        const response = await axios.get('/usage/metrics', {
          params: {
            userId: testUserId,
            period
          }
        });

        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('period', period);
      }
    });

    it('should handle users without subscriptions', async () => {
      mock.onGet('/usage/metrics').reply(200, {
        totalCalls: 10,
        successCalls: 9,
        errorCalls: 1,
        uniqueConnectors: 1,
        subscription: null,
        period: 'month'
      });

      const response = await axios.get('/usage/metrics', { params: { userId: 'user-without-subscription' } });

      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('subscription', null);
    });
  });

  // Test subscription management
  describe('Subscription Management', () => {
    it('should create a subscription', async () => {
      const response = await axios.post('/subscriptions', testSubscription);

      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      expect(response.data).toHaveProperty('userId', testUserId);
      expect(response.data).toHaveProperty('plan', testSubscription.plan);
      expect(response.data).toHaveProperty('limits');
      expect(response.data.limits).toHaveProperty('apiCalls', testSubscription.limits.apiCalls);
      expect(response.data.limits).toHaveProperty('connectors', testSubscription.limits.connectors);
    });

    it('should get subscriptions', async () => {
      const response = await axios.get('/subscriptions');

      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data.length).toBeGreaterThan(0);
      expect(response.data[0]).toHaveProperty('userId', testUserId);
    });

    it('should get a subscription by user ID', async () => {
      const response = await axios.get(`/subscriptions/${testUserId}`);

      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('userId', testUserId);
      expect(response.data).toHaveProperty('plan', testSubscription.plan);
    });

    it('should handle missing subscription', async () => {
      mock.onGet('/subscriptions/non-existent').reply(404, { error: 'Subscription not found' });

      try {
        await axios.get('/subscriptions/non-existent');
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(404);
        expect(err.response.data).toHaveProperty('error', 'Subscription not found');
      }
    });
  });

  // Test error handling
  describe('Error Handling', () => {
    it('should handle invalid date formats', async () => {
      mock.onGet('/usage').reply(400, { error: 'Invalid date format' });

      try {
        await axios.get('/usage', {
          params: {
            userId: testUserId,
            startDate: 'invalid-date'
          }
        });
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(400);
        expect(err.response.data).toHaveProperty('error', 'Invalid date format');
      }
    });

    it('should handle invalid period values', async () => {
      mock.onGet('/usage/metrics').reply(400, { error: 'Invalid period' });

      try {
        await axios.get('/usage/metrics', {
          params: {
            userId: testUserId,
            period: 'invalid-period'
          }
        });
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(400);
        expect(err.response.data).toHaveProperty('error', 'Invalid period');
      }
    });
  });
});
