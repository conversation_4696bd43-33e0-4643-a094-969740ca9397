/**
 * NovaCore Evidence Service
 * 
 * This service provides functionality for managing evidence records.
 */

const { Evidence } = require('../models');
const logger = require('../../config/logger');
const { ValidationError, NotFoundError } = require('../utils/errors');
const crypto = require('crypto');

class EvidenceService {
  /**
   * Create a new evidence record
   * @param {Object} data - Evidence data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created evidence
   */
  async createEvidence(data, userId) {
    try {
      logger.info('Creating new evidence record', { type: data.type });
      
      // Generate hash for content if not provided
      if (data.content && !data.content.hash) {
        data.content.hash = this._generateContentHash(data.content.data);
      }
      
      // Set created by
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Create evidence
      const evidence = new Evidence(data);
      await evidence.save();
      
      logger.info('Evidence record created successfully', { id: evidence._id });
      
      return evidence;
    } catch (error) {
      logger.error('Error creating evidence record', { error });
      throw error;
    }
  }
  
  /**
   * Get all evidence records with optional filtering
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Evidence records with pagination info
   */
  async getAllEvidence(filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
      
      // Build query
      const query = {};
      
      // Apply filters
      if (filter.type) {
        query.type = filter.type;
      }
      
      if (filter.category) {
        query.category = filter.category;
      }
      
      if (filter.status) {
        query.status = filter.status;
      }
      
      if (filter.framework) {
        query['metadata.framework'] = filter.framework;
      }
      
      if (filter.control) {
        query['metadata.control'] = filter.control;
      }
      
      if (filter.tags) {
        query['metadata.tags'] = { $all: Array.isArray(filter.tags) ? filter.tags : [filter.tags] };
      }
      
      if (filter.verificationStatus) {
        query['verification.status'] = filter.verificationStatus;
      }
      
      if (filter.createdAfter) {
        query.createdAt = { $gte: new Date(filter.createdAfter) };
      }
      
      if (filter.createdBefore) {
        query.createdAt = { ...query.createdAt, $lte: new Date(filter.createdBefore) };
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [evidence, total] = await Promise.all([
        Evidence.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        Evidence.countDocuments(query)
      ]);
      
      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;
      
      return {
        data: evidence,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting evidence records', { error });
      throw error;
    }
  }
  
  /**
   * Get evidence by ID
   * @param {string} id - Evidence ID
   * @returns {Promise<Object>} - Evidence record
   */
  async getEvidenceById(id) {
    try {
      const evidence = await Evidence.findById(id);
      
      if (!evidence) {
        throw new NotFoundError(`Evidence with ID ${id} not found`);
      }
      
      return evidence;
    } catch (error) {
      logger.error('Error getting evidence by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Update evidence by ID
   * @param {string} id - Evidence ID
   * @param {Object} data - Updated evidence data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated evidence
   */
  async updateEvidence(id, data, userId) {
    try {
      // Get existing evidence
      const evidence = await this.getEvidenceById(id);
      
      // Update content hash if content data changed
      if (data.content && data.content.data) {
        data.content.hash = this._generateContentHash(data.content.data);
      }
      
      // Set updated by
      data.updatedBy = userId;
      
      // Update evidence
      Object.assign(evidence, data);
      await evidence.save();
      
      logger.info('Evidence record updated successfully', { id });
      
      return evidence;
    } catch (error) {
      logger.error('Error updating evidence', { id, error });
      throw error;
    }
  }
  
  /**
   * Delete evidence by ID
   * @param {string} id - Evidence ID
   * @returns {Promise<boolean>} - Deletion success
   */
  async deleteEvidence(id) {
    try {
      const result = await Evidence.findByIdAndDelete(id);
      
      if (!result) {
        throw new NotFoundError(`Evidence with ID ${id} not found`);
      }
      
      logger.info('Evidence record deleted successfully', { id });
      
      return true;
    } catch (error) {
      logger.error('Error deleting evidence', { id, error });
      throw error;
    }
  }
  
  /**
   * Update evidence verification status
   * @param {string} id - Evidence ID
   * @param {Object} verificationData - Verification data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated evidence
   */
  async updateVerificationStatus(id, verificationData, userId) {
    try {
      // Get existing evidence
      const evidence = await this.getEvidenceById(id);
      
      // Update verification data
      evidence.verification = {
        ...evidence.verification,
        ...verificationData,
        verifiedAt: new Date(),
        verifiedBy: userId
      };
      
      // Update status if verified
      if (verificationData.status === 'verified') {
        evidence.status = 'verified';
      }
      
      // Set updated by
      evidence.updatedBy = userId;
      
      // Save changes
      await evidence.save();
      
      logger.info('Evidence verification status updated', { id, status: verificationData.status });
      
      return evidence;
    } catch (error) {
      logger.error('Error updating evidence verification status', { id, error });
      throw error;
    }
  }
  
  /**
   * Find evidence by framework and control
   * @param {string} framework - Framework name
   * @param {string} control - Control ID
   * @returns {Promise<Array>} - Evidence records
   */
  async findByFrameworkAndControl(framework, control) {
    try {
      return await Evidence.findByFrameworkAndControl(framework, control);
    } catch (error) {
      logger.error('Error finding evidence by framework and control', { framework, control, error });
      throw error;
    }
  }
  
  /**
   * Find evidence by tags
   * @param {Array<string>} tags - Tags to search for
   * @returns {Promise<Array>} - Evidence records
   */
  async findByTags(tags) {
    try {
      return await Evidence.findByTags(tags);
    } catch (error) {
      logger.error('Error finding evidence by tags', { tags, error });
      throw error;
    }
  }
  
  /**
   * Generate hash for content data
   * @param {*} data - Content data
   * @returns {string} - Content hash
   * @private
   */
  _generateContentHash(data) {
    const content = typeof data === 'object' ? JSON.stringify(data) : String(data);
    return crypto.createHash('sha256').update(content).digest('hex');
  }
}

module.exports = new EvidenceService();
