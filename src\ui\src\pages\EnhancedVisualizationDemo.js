import React, { useState } from 'react';
import {
  Box,
  Grid,
  Typo<PERSON>,
  Card,
  CardContent,
  Divider,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  FormControlLabel,
  Button,
  Slider,
  TextField,
  Paper
} from '@mui/material';
import EnhancedVisualizationContainer from '../components/visualizations/EnhancedVisualizationContainer';
import tensorDataService from '../services/TensorDataService';

/**
 * EnhancedVisualizationDemo component
 * 
 * Demonstrates all the enhanced visualization features
 */
function EnhancedVisualizationDemo() {
  // State
  const [dataType, setDataType] = useState('sine');
  const [dataSize, setDataSize] = useState(100);
  const [noiseLevel, setNoiseLevel] = useState(0.1);
  const [useRealtime, setUseRealtime] = useState(false);
  const [realtimeInterval, setRealtimeInterval] = useState(1000);
  const [tensorId, setTensorId] = useState('demo-tensor');
  const [visualizationType, setVisualizationType] = useState('3d_tensor_visualization');
  const [showControls, setShowControls] = useState(true);
  const [regenerateKey, setRegenerateKey] = useState(0);
  
  // Generate tensor data
  const handleGenerateTensor = () => {
    // Increment key to force re-render
    setRegenerateKey(prevKey => prevKey + 1);
  };
  
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Enhanced Visualization Demo
      </Typography>
      
      <Typography variant="body1" paragraph>
        This demo showcases all the enhanced visualization features, including real-time data, export functionality, interactive controls, performance optimization, and user preferences.
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Data Options
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel id="data-type-label">Data Type</InputLabel>
              <Select
                labelId="data-type-label"
                id="data-type"
                value={dataType}
                label="Data Type"
                onChange={(e) => setDataType(e.target.value)}
              >
                <MenuItem value="sine">Sine Wave</MenuItem>
                <MenuItem value="cosine">Cosine Wave</MenuItem>
                <MenuItem value="square">Square Wave</MenuItem>
                <MenuItem value="sawtooth">Sawtooth Wave</MenuItem>
                <MenuItem value="triangle">Triangle Wave</MenuItem>
                <MenuItem value="random">Random</MenuItem>
                <MenuItem value="harmonics">Harmonics</MenuItem>
                <MenuItem value="resonance">Resonance</MenuItem>
              </Select>
            </FormControl>
            
            <Typography gutterBottom>Data Size</Typography>
            <Slider
              value={dataSize}
              onChange={(e, newValue) => setDataSize(newValue)}
              min={10}
              max={200}
              step={10}
              valueLabelDisplay="auto"
              sx={{ mb: 2 }}
            />
            
            <Typography gutterBottom>Noise Level</Typography>
            <Slider
              value={noiseLevel}
              onChange={(e, newValue) => setNoiseLevel(newValue)}
              min={0}
              max={1}
              step={0.1}
              valueLabelDisplay="auto"
              sx={{ mb: 2 }}
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={useRealtime}
                  onChange={(e) => setUseRealtime(e.target.checked)}
                />
              }
              label="Use Real-time Updates"
              sx={{ display: 'block', mb: 1 }}
            />
            
            {useRealtime && (
              <>
                <Typography gutterBottom>Update Interval (ms)</Typography>
                <Slider
                  value={realtimeInterval}
                  onChange={(e, newValue) => setRealtimeInterval(newValue)}
                  min={100}
                  max={5000}
                  step={100}
                  valueLabelDisplay="auto"
                  sx={{ mb: 2 }}
                />
              </>
            )}
            
            <TextField
              label="Tensor ID"
              value={tensorId}
              onChange={(e) => setTensorId(e.target.value)}
              fullWidth
              sx={{ mb: 2 }}
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={showControls}
                  onChange={(e) => setShowControls(e.target.checked)}
                />
              }
              label="Show Controls"
              sx={{ display: 'block', mb: 2 }}
            />
            
            <Button
              variant="contained"
              color="primary"
              onClick={handleGenerateTensor}
              fullWidth
            >
              Regenerate Data
            </Button>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={9}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel id="visualization-type-label">Visualization Type</InputLabel>
                <Select
                  labelId="visualization-type-label"
                  id="visualization-type"
                  value={visualizationType}
                  label="Visualization Type"
                  onChange={(e) => setVisualizationType(e.target.value)}
                >
                  <MenuItem value="3d_tensor_visualization">3D Tensor Visualization</MenuItem>
                  <MenuItem value="resonance_spectrogram">Resonance Spectrogram</MenuItem>
                  <MenuItem value="phase_space_visualization">Phase Space Visualization</MenuItem>
                  <MenuItem value="harmonic_pattern_explorer">Harmonic Pattern Explorer</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <EnhancedVisualizationContainer
                key={`${visualizationType}-${tensorId}-${regenerateKey}`}
                tensorId={tensorId}
                visualizationType={visualizationType}
                dimensions={getDimensions(dataType, dataSize)}
                options={{
                  renderMode: 'high',
                  showAxes: true,
                  showGrid: true,
                  rotationSpeed: 1,
                  colorScheme: 'default'
                }}
                height="600px"
                title={`${getVisualizationTitle(visualizationType)} - ${dataType.charAt(0).toUpperCase() + dataType.slice(1)} Data`}
                showControls={showControls}
                useRealtime={useRealtime}
                realtimeInterval={realtimeInterval}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Features Demonstrated
                </Typography>
                <Divider sx={{ mb: 2 }} />
                
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Typography variant="subtitle1" gutterBottom>
                      Real-Time Data
                    </Typography>
                    <Typography variant="body2">
                      Enable real-time updates to see the visualization update automatically. Adjust the update interval to control the frequency of updates.
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Typography variant="subtitle1" gutterBottom>
                      Export Functionality
                    </Typography>
                    <Typography variant="body2">
                      Click the Export button to export the visualization as an image, video, or GIF. You can also take a quick screenshot.
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Typography variant="subtitle1" gutterBottom>
                      Interactive Controls
                    </Typography>
                    <Typography variant="body2">
                      Use the controls on the right side of the visualization to interact with it. You can orbit, pan, zoom, select points, and filter values.
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Typography variant="subtitle1" gutterBottom>
                      Performance Optimization
                    </Typography>
                    <Typography variant="body2">
                      Click the Monitor button to see performance metrics. You can also adjust optimization settings to improve performance.
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Typography variant="subtitle1" gutterBottom>
                      User Preferences
                    </Typography>
                    <Typography variant="body2">
                      Click the Preferences button to save and load visualization presets. You can also set default preferences and import/export all preferences.
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Typography variant="subtitle1" gutterBottom>
                      Multiple Visualization Types
                    </Typography>
                    <Typography variant="body2">
                      Try different visualization types to see how they represent the same data in different ways. Each type has its own unique features and options.
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
}

// Helper function to get dimensions based on data type and size
function getDimensions(dataType, dataSize) {
  switch (dataType) {
    case 'harmonics':
    case 'resonance':
      return [dataSize, 1, 1]; // 1D array for harmonic analysis
      
    case 'random':
      if (dataSize <= 25) {
        return [5, 5, 1]; // 2D grid
      } else if (dataSize <= 125) {
        return [5, 5, 5]; // 3D grid
      } else {
        return [10, 10, 1]; // Larger 2D grid
      }
      
    default:
      if (dataSize <= 10) {
        return [dataSize, 1, 1]; // 1D array
      } else {
        const size = Math.ceil(Math.sqrt(dataSize));
        return [size, size, 1]; // 2D grid
      }
  }
}

// Helper function to get visualization title
function getVisualizationTitle(visualizationType) {
  switch (visualizationType) {
    case '3d_tensor_visualization':
      return '3D Tensor Visualization';
    case 'resonance_spectrogram':
      return 'Resonance Spectrogram';
    case 'phase_space_visualization':
      return 'Phase Space Visualization';
    case 'harmonic_pattern_explorer':
      return 'Harmonic Pattern Explorer';
    default:
      return 'Visualization';
  }
}

export default EnhancedVisualizationDemo;
