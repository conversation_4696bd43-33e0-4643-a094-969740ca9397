.compliance-badge {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
}

.compliance-badge.quantum {
  border-left: 4px solid var(--compliance-color, #8a2be2);
}

.compliance-badge.classical {
  border-left: 4px solid #4a5568;
}

.badge-header {
  display: flex;
  align-items: center;
  padding: 1rem 1.25rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e2e8f0;
}

.badge-icon {
  font-size: 1.5rem;
  margin-right: 0.75rem;
  color: var(--compliance-color, #8a2be2);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(138, 43, 226, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.compliance-badge.classical .badge-icon {
  background: rgba(74, 85, 104, 0.1);
  color: #4a5568;
}

.badge-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.mode-indicator {
  font-size: 0.8rem;
  font-weight: 500;
  color: #718096;
  margin-left: 0.5rem;
}

.badge-content {
  padding: 1.25rem;
}

.compliance-level {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px dashed #e2e8f0;
}

.level-label {
  font-size: 0.9rem;
  color: #718096;
  margin-right: 0.5rem;
}

.level-value {
  font-weight: 600;
  color: var(--compliance-color, #8a2be2);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background: rgba(138, 43, 226, 0.1);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
}

.compliance-badge.classical .level-value {
  background: rgba(74, 85, 104, 0.1);
  color: #4a5568;
}

.compliance-description {
  font-size: 0.95rem;
  color: #4a5568;
  line-height: 1.5;
  margin-bottom: 1.25rem;
}

.quantum-info {
  color: var(--compliance-color, #8a2be2);
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.quantum-info::before {
  content: '→';
  font-weight: bold;
  margin-right: 0.25rem;
}

.security-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 1.25rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #4a5568;
  background: #f8f9fa;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  border: 1px solid #e2e8f0;
}

.feature-icon {
  font-size: 1rem;
}

.badge-footer {
  padding: 0.75rem 1.25rem;
  background: #f8f9fa;
  border-top: 1px solid #e2e8f0;
  font-size: 0.8rem;
  color: #a0aec0;
  text-align: right;
}

/* Specific level styling */
.compliance-badge.low {
  --compliance-color: #38a169;
}

.compliance-badge.standard {
  --compliance-color: #3182ce;
}

.compliance-badge.high {
  --compliance-color: #d69e2e;
}

.compliance-badge.critical {
  --compliance-color: #e53e3e;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(229, 62, 62, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(229, 62, 62, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(229, 62, 62, 0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .security-features {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .feature {
    width: 100%;
    justify-content: center;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .compliance-badge {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .badge-header {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .badge-title {
    color: #e2e8f0;
  }
  
  .mode-indicator {
    color: #a0aec0;
  }
  
  .compliance-level {
    border-color: #4a5568;
  }
  
  .compliance-description {
    color: #cbd5e0;
  }
  
  .feature {
    background: #2d3748;
    border-color: #4a5568;
    color: #cbd5e0;
  }
  
  .badge-footer {
    background: #2d3748;
    border-color: #4a5568;
    color: #a0aec0;
  }
  
  .compliance-badge.low {
    --compliance-color: #68d391;
  }
  
  .compliance-badge.standard {
    --compliance-color: #63b3ed;
  }
  
  .compliance-badge.high {
    --compliance-color: #f6ad55;
  }
  
  .compliance-badge.critical {
    --compliance-color: #fc8181;
  }
}
