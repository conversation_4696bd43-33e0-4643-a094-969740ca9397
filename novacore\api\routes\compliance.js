/**
 * Compliance Routes
 * 
 * This file defines the routes for compliance-related functionality.
 */

const express = require('express');
const router = express.Router();
const zeroTrust = require('../middleware/zeroTrust');
const badgeSystem = require('../services/badgeSystem');
const complianceKit = require('../services/complianceKit');
const logger = require('../utils/logger');

// Apply Zero Trust middleware to all routes
router.use(zeroTrust.contextAwareAuth);
router.use(zeroTrust.leastPrivilege);
router.use(zeroTrust.validateRequests);

/**
 * @swagger
 * /api/v1/compliance/badges/{organizationId}/{badgeType}:
 *   get:
 *     summary: Get compliance badge
 *     description: Get a compliance badge for an organization
 *     tags: [Compliance]
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ID
 *       - in: path
 *         name: badgeType
 *         required: true
 *         schema:
 *           type: string
 *         description: Badge type (e.g., soc2, gdpr, hipaa)
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [compliant, partial, noncompliant, unknown]
 *         description: Override compliance status
 *       - in: query
 *         name: style
 *         schema:
 *           type: string
 *           enum: [flat, gradient, 3d]
 *           default: flat
 *         description: Badge style
 *       - in: query
 *         name: size
 *         schema:
 *           type: string
 *           enum: [small, medium, large]
 *           default: medium
 *         description: Badge size
 *       - in: query
 *         name: showDate
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Whether to show date on badge
 *     responses:
 *       200:
 *         description: Badge image
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Server error
 */
router.get('/badges/:organizationId/:badgeType', async (req, res) => {
  try {
    // Get parameters
    const { organizationId, badgeType } = req.params;
    const { status, style, size, showDate } = req.query;
    
    // Validate parameters
    if (!organizationId || !badgeType) {
      return res.status(400).json({ error: 'Organization ID and badge type are required' });
    }
    
    // Get compliance status if not provided
    let badgeStatus = status;
    if (!badgeStatus) {
      badgeStatus = await badgeSystem.getComplianceStatus(organizationId, badgeType);
    }
    
    // Generate badge
    const badge = await badgeSystem.generateBadge({
      organizationId,
      badgeType,
      status: badgeStatus,
      style,
      size,
      showDate: showDate !== 'false'
    });
    
    // Log badge generation
    logger.info('Badge generated', {
      organizationId,
      badgeType,
      status: badgeStatus,
      style,
      size
    });
    
    // Set response headers
    res.setHeader('Content-Type', 'image/png');
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    
    // Send badge
    res.send(badge);
  } catch (error) {
    logger.error('Badge generation error:', error);
    res.status(500).json({ error: 'Failed to generate badge' });
  }
});

/**
 * @swagger
 * /api/v1/compliance/badges/verify:
 *   post:
 *     summary: Verify compliance badge
 *     description: Verify a compliance badge
 *     tags: [Compliance]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - badge
 *             properties:
 *               badge:
 *                 type: string
 *                 format: binary
 *                 description: Badge image
 *     responses:
 *       200:
 *         description: Verification result
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Server error
 */
router.post('/badges/verify', async (req, res) => {
  try {
    // Validate request
    if (!req.files || !req.files.badge) {
      return res.status(400).json({ error: 'Badge image is required' });
    }
    
    // Get badge image
    const badgeBuffer = req.files.badge.data;
    
    // Verify badge
    const verification = await badgeSystem.verifyBadge(badgeBuffer);
    
    // Log badge verification
    logger.info('Badge verified', {
      verified: verification.verified,
      organizationId: verification.organizationId,
      badgeType: verification.badgeType
    });
    
    // Return verification result
    res.status(200).json(verification);
  } catch (error) {
    logger.error('Badge verification error:', error);
    res.status(500).json({ error: 'Failed to verify badge' });
  }
});

/**
 * @swagger
 * /api/v1/compliance/badges/{organizationId}:
 *   get:
 *     summary: Get badge URLs
 *     description: Get all badge URLs for an organization
 *     tags: [Compliance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ID
 *     responses:
 *       200:
 *         description: Badge URLs
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/badges/:organizationId', async (req, res) => {
  try {
    // Get organization ID
    const { organizationId } = req.params;
    
    // Validate parameters
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }
    
    // Check authorization
    if (req.user.organizationId !== organizationId && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Unauthorized to access this organization' });
    }
    
    // Get badge URLs
    const badgeUrls = await complianceKit.getBadgeUrls(organizationId);
    
    // Log badge URLs retrieval
    logger.info('Badge URLs retrieved', {
      userId: req.user.id,
      organizationId
    });
    
    // Return badge URLs
    res.status(200).json(badgeUrls);
  } catch (error) {
    logger.error('Badge URLs error:', error);
    res.status(500).json({ error: 'Failed to retrieve badge URLs' });
  }
});

/**
 * @swagger
 * /api/v1/compliance/verify/{organizationId}/{badgeType}:
 *   get:
 *     summary: Get badge verification page
 *     description: Get a verification page for a badge
 *     tags: [Compliance]
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ID
 *       - in: path
 *         name: badgeType
 *         required: true
 *         schema:
 *           type: string
 *         description: Badge type (e.g., soc2, gdpr, hipaa)
 *     responses:
 *       200:
 *         description: Verification page HTML
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Server error
 */
router.get('/verify/:organizationId/:badgeType', async (req, res) => {
  try {
    // Get parameters
    const { organizationId, badgeType } = req.params;
    
    // Validate parameters
    if (!organizationId || !badgeType) {
      return res.status(400).json({ error: 'Organization ID and badge type are required' });
    }
    
    // Create verification page
    const html = await badgeSystem.createVerificationPage(organizationId, badgeType);
    
    // Log verification page creation
    logger.info('Verification page created', {
      organizationId,
      badgeType
    });
    
    // Set response headers
    res.setHeader('Content-Type', 'text/html');
    
    // Send verification page
    res.send(html);
  } catch (error) {
    logger.error('Verification page error:', error);
    res.status(500).json({ error: 'Failed to create verification page' });
  }
});

/**
 * @swagger
 * /api/v1/compliance/page/{organizationId}:
 *   get:
 *     summary: Get compliance page
 *     description: Get a compliance page for an organization
 *     tags: [Compliance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ID
 *       - in: query
 *         name: frameworks
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Frameworks to include
 *       - in: query
 *         name: includeHistory
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Whether to include compliance history
 *       - in: query
 *         name: includeEvidence
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Whether to include evidence
 *     responses:
 *       200:
 *         description: Compliance page markdown
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/page/:organizationId', async (req, res) => {
  try {
    // Get parameters
    const { organizationId } = req.params;
    const { frameworks, includeHistory, includeEvidence } = req.query;
    
    // Validate parameters
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }
    
    // Check authorization
    if (req.user.organizationId !== organizationId && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Unauthorized to access this organization' });
    }
    
    // Parse frameworks
    const parsedFrameworks = frameworks ? 
      (Array.isArray(frameworks) ? frameworks : frameworks.split(',')) : 
      ['soc2', 'gdpr', 'hipaa', 'iso27001'];
    
    // Generate compliance page
    const markdown = await complianceKit.generateCompliancePage({
      organizationId,
      frameworks: parsedFrameworks,
      includeHistory: includeHistory !== 'false',
      includeEvidence: includeEvidence !== 'false'
    });
    
    // Log compliance page generation
    logger.info('Compliance page generated', {
      userId: req.user.id,
      organizationId,
      frameworks: parsedFrameworks
    });
    
    // Return compliance page
    res.status(200).json({ markdown });
  } catch (error) {
    logger.error('Compliance page error:', error);
    res.status(500).json({ error: 'Failed to generate compliance page' });
  }
});

/**
 * @swagger
 * /api/v1/compliance/overview/{organizationId}:
 *   get:
 *     summary: Get compliance overview
 *     description: Get a compliance overview for an organization
 *     tags: [Compliance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ID
 *     responses:
 *       200:
 *         description: Compliance overview
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/overview/:organizationId', async (req, res) => {
  try {
    // Get parameters
    const { organizationId } = req.params;
    
    // Validate parameters
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }
    
    // Check authorization
    if (req.user.organizationId !== organizationId && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Unauthorized to access this organization' });
    }
    
    // Generate compliance overview
    const overview = await complianceKit.generateComplianceOverview(organizationId);
    
    // Log compliance overview generation
    logger.info('Compliance overview generated', {
      userId: req.user.id,
      organizationId
    });
    
    // Return compliance overview
    res.status(200).json(overview);
  } catch (error) {
    logger.error('Compliance overview error:', error);
    res.status(500).json({ error: 'Failed to generate compliance overview' });
  }
});

/**
 * @swagger
 * /api/v1/compliance/charter:
 *   get:
 *     summary: Get compliance charter
 *     description: Get the NovaFuse Compliance Charter
 *     tags: [Compliance]
 *     responses:
 *       200:
 *         description: Compliance charter markdown
 *       500:
 *         description: Server error
 */
router.get('/charter', async (req, res) => {
  try {
    // Get compliance charter
    const charter = await complianceKit.getComplianceCharter();
    
    // Log compliance charter retrieval
    logger.info('Compliance charter retrieved');
    
    // Return compliance charter
    res.status(200).json({ markdown: charter });
  } catch (error) {
    logger.error('Compliance charter error:', error);
    res.status(500).json({ error: 'Failed to retrieve compliance charter' });
  }
});

/**
 * @swagger
 * /api/v1/compliance/framework:
 *   get:
 *     summary: Get Cyber-Safety Framework
 *     description: Get the NovaFuse Cyber-Safety Framework
 *     tags: [Compliance]
 *     responses:
 *       200:
 *         description: Cyber-Safety Framework
 *       500:
 *         description: Server error
 */
router.get('/framework', async (req, res) => {
  try {
    // Get Cyber-Safety Framework
    const framework = await complianceKit.getCyberSafetyFramework();
    
    // Log framework retrieval
    logger.info('Cyber-Safety Framework retrieved');
    
    // Return framework
    res.status(200).json(framework);
  } catch (error) {
    logger.error('Cyber-Safety Framework error:', error);
    res.status(500).json({ error: 'Failed to retrieve Cyber-Safety Framework' });
  }
});

module.exports = router;
