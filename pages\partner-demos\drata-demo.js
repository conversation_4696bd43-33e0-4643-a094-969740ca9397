import React from 'react';
import DemoTemplate from '../../components/demo-framework/DemoTemplate';
import PageWithSidebar from '../../components/PageWithSidebar';

export default function DrataDemo() {
  // SEO metadata
  const pageProps = {
    title: 'Drata + NovaFuse: Proactive Compliance Demo - NovaFuse',
    description: 'Experience how NovaFuse\'s predictive risk engine layers atop Drata\'s framework monitoring for proactive compliance management.',
    keywords: 'Drata integration, NovaFuse demo, predictive compliance, AI compliance, GRC integration',
    canonical: 'https://novafuse.io/partner-demos/drata-demo',
    ogImage: '/images/partner-demos/drata-demo-og.png'
  };

  // Partner-specific information based on "Partner DNA" analysis
  const partnerInfo = {
    name: 'Drata',
    logo: '/images/partners/drata-logo.png',
    tagline: 'Drata + NovaFuse: Proactive Compliance',
    demoTitle: 'Predictive Compliance Engine',
    demoDescription: 'Layer NovaFuse\'s predictive risk engine atop Drata\'s framework monitoring',
    valueProposition: 'Transform Drata from reactive checklist compliance to proactive risk prevention',
    overviewText: 'This demo showcases how NovaFuse\'s predictive analytics and AI-powered compliance engine can integrate with Drata\'s framework monitoring to provide proactive compliance management. Instead of reacting to compliance issues after they occur, your team can predict and prevent them before they happen.',
    styles: {
      primaryColor: '#4F46E5', // Drata purple
      secondaryColor: '#1E40AF',
      accentColor: '#818CF8'
    },
    primaryCta: {
      text: 'Schedule a War Room Session',
      url: '/contact?partner=drata&type=war-room'
    },
    secondaryCta: {
      text: 'View Integration Documentation',
      url: '/documentation/integrations/drata'
    },
    customTabs: [
      {
        id: 'roi-calculator',
        label: 'ROI Calculator',
        content: `
          <div class="bg-gray-800 p-6 rounded-lg">
            <h3 class="text-xl font-semibold mb-4">Compliance ROI Calculator</h3>
            <p class="text-gray-300 mb-6">See the potential ROI of implementing NovaFuse with your Drata instance:</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 class="font-medium mb-3">Input Parameters</h4>
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm text-gray-400 mb-1">Number of compliance frameworks</label>
                    <input type="number" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2" value="3" />
                  </div>
                  <div>
                    <label class="block text-sm text-gray-400 mb-1">Average hours per audit</label>
                    <input type="number" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2" value="120" />
                  </div>
                  <div>
                    <label class="block text-sm text-gray-400 mb-1">Number of audits per year</label>
                    <input type="number" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2" value="4" />
                  </div>
                  <div>
                    <label class="block text-sm text-gray-400 mb-1">Average hourly cost ($)</label>
                    <input type="number" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2" value="150" />
                  </div>
                </div>
              </div>
              
              <div>
                <h4 class="font-medium mb-3">Projected Savings</h4>
                <div class="bg-gray-900 p-4 rounded-lg mb-4">
                  <div class="flex justify-between items-center mb-2">
                    <span class="text-gray-400">Time Reduction:</span>
                    <span class="text-green-500 font-bold">68%</span>
                  </div>
                  <div class="flex justify-between items-center mb-2">
                    <span class="text-gray-400">Annual Hours Saved:</span>
                    <span class="text-green-500 font-bold">326.4 hours</span>
                  </div>
                  <div class="flex justify-between items-center mb-2">
                    <span class="text-gray-400">Annual Cost Savings:</span>
                    <span class="text-green-500 font-bold">$48,960</span>
                  </div>
                  <div class="flex justify-between items-center mb-2">
                    <span class="text-gray-400">3-Year ROI:</span>
                    <span class="text-green-500 font-bold">412%</span>
                  </div>
                </div>
                
                <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded">
                  Calculate My Custom ROI
                </button>
              </div>
            </div>
          </div>
        `
      }
    ]
  };

  // Relevant slots from the 144-slot ecosystem framework
  const ecosystemSlots = [
    {
      id: 'Slot 12',
      name: 'Predictive Compliance',
      description: 'AI-powered predictive analytics for compliance risk'
    },
    {
      id: 'Slot 27',
      name: 'Audit Automation',
      description: 'Automated evidence collection and audit preparation'
    },
    {
      id: 'Slot 43',
      name: 'Compliance Horizon',
      description: 'Future-looking compliance gap prediction'
    }
  ];

  // Key features to highlight
  const features = [
    {
      icon: '🔮',
      title: 'Predictive Analytics',
      description: 'Identify compliance gaps before they become issues'
    },
    {
      icon: '🤖',
      title: 'AI Compliance Engine',
      description: 'Automated compliance monitoring and remediation'
    },
    {
      icon: '📊',
      title: 'Executive Dashboards',
      description: 'Real-time compliance visibility for leadership'
    }
  ];

  // UI components to showcase
  const uiComponents = [
    {
      name: 'Compliance Horizon Dashboard',
      description: 'Visualize upcoming compliance requirements and potential gaps',
      image: '/images/demos/drata/compliance-horizon.png'
    },
    {
      name: 'Risk Prediction Heatmap',
      description: 'Identify high-risk areas across your compliance landscape',
      image: '/images/demos/drata/risk-heatmap.png'
    },
    {
      name: 'Drata Integration Panel',
      description: 'Seamlessly connect NovaFuse with your existing Drata instance',
      image: '/images/demos/drata/drata-integration.png'
    },
    {
      name: 'Executive Summary View',
      description: 'Board-ready compliance status and risk overview',
      image: '/images/demos/drata/executive-summary.png'
    }
  ];

  // API endpoints to demonstrate
  const apiEndpoints = [
    {
      method: 'GET',
      path: '/api/compliance/predictive/risk-score',
      description: 'Get predictive risk scores for compliance controls',
      example: {
        "overall_risk_score": 87,
        "framework_scores": {
          "SOC2": 92,
          "HIPAA": 85,
          "GDPR": 78
        },
        "high_risk_controls": [
          {
            "id": "AC-17",
            "name": "Remote Access",
            "risk_score": 68,
            "prediction": "Potential gap in 14 days"
          }
        ]
      }
    },
    {
      method: 'POST',
      path: '/api/integrations/drata/sync',
      description: 'Synchronize compliance data between Drata and NovaFuse',
      example: {
        "sync_id": "sync-12345",
        "status": "success",
        "items_synced": 287,
        "frameworks_synced": ["SOC2", "HIPAA", "GDPR"],
        "timestamp": "2023-07-15T14:22:31Z"
      }
    },
    {
      method: 'GET',
      path: '/api/compliance/horizon/gaps',
      description: 'Identify potential compliance gaps before they occur',
      example: {
        "predicted_gaps": [
          {
            "control_id": "AC-17",
            "framework": "NIST 800-53",
            "confidence": 0.92,
            "predicted_date": "2023-08-01",
            "remediation_suggestions": [
              "Update remote access policy",
              "Implement MFA for all remote connections"
            ]
          }
        ]
      }
    }
  ];

  // UAC capabilities to highlight
  const uacCapabilities = [
    {
      name: 'Drata Data Connector',
      description: 'Seamlessly connect to your Drata instance to extract and enhance compliance data',
      features: [
        'Real-time data synchronization',
        'Bidirectional updates',
        'Automated evidence collection',
        'Control mapping across frameworks'
      ]
    },
    {
      name: 'Predictive Compliance Engine',
      description: 'AI-powered analytics to predict and prevent compliance issues before they occur',
      features: [
        'Machine learning risk models',
        'Pattern recognition across compliance data',
        'Anomaly detection',
        'Continuous learning and improvement'
      ]
    },
    {
      name: 'Multi-Framework Orchestration',
      description: 'Manage multiple compliance frameworks with unified controls and evidence',
      features: [
        'Cross-framework control mapping',
        'Unified evidence repository',
        'Compliance requirement deduplication',
        'Framework-specific reporting'
      ]
    }
  ];

  // App Store modules to feature
  const appStoreModules = [
    {
      name: 'Compliance Horizon',
      description: 'AI-powered gap predictor for proactive compliance management',
      icon: '🔮',
      tags: ['AI', 'Predictive', 'Risk Management']
    },
    {
      name: 'Drata Connector Pro',
      description: 'Enhanced integration with Drata for seamless data flow',
      icon: '🔌',
      tags: ['Integration', 'Data Sync', 'Automation']
    },
    {
      name: 'Executive Insights',
      description: 'Board-ready dashboards and reports for compliance oversight',
      icon: '📊',
      tags: ['Reporting', 'Dashboards', 'Executive']
    },
    {
      name: 'Audit Accelerator',
      description: 'Streamline audit preparation and evidence collection',
      icon: '🚀',
      tags: ['Audit', 'Evidence', 'Efficiency']
    },
    {
      name: 'Control Mapper',
      description: 'Map controls across multiple frameworks for unified compliance',
      icon: '🗺️',
      tags: ['Mapping', 'Controls', 'Frameworks']
    },
    {
      name: 'Risk Radar',
      description: 'Real-time risk monitoring and alerting',
      icon: '📡',
      tags: ['Risk', 'Monitoring', 'Alerts']
    }
  ];

  // Sidebar items
  const sidebarItems = [
    { type: 'category', label: 'Drata + NovaFuse', items: [
      { label: 'Demo Overview', href: '#overview' },
      { label: 'Predictive Analytics', href: '#predictive' },
      { label: 'Integration Features', href: '#integration' },
      { label: 'ROI Calculator', href: '#roi' }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'Integration Documentation', href: '/documentation/integrations/drata' },
      { label: 'Case Studies', href: '/resources/case-studies' },
      { label: 'Schedule War Room', href: '/contact?partner=drata&type=war-room' }
    ]},
    { type: 'category', label: 'Other Partner Demos', items: [
      { label: 'Postman Demo', href: '/partner-demos/postman-demo' },
      { label: 'Tines Demo', href: '/partner-demos/tines-demo' },
      { label: 'Swimlane Demo', href: '/partner-demos/swimlane-demo' },
      { label: 'View All Partners', href: '/partner-demos' }
    ]}
  ];

  return (
    <PageWithSidebar
      title={pageProps.title}
      description={pageProps.description}
      sidebarItems={sidebarItems}
    >
      <DemoTemplate
        partner={partnerInfo}
        demoType="war-room"
        ecosystemSlots={ecosystemSlots}
        features={features}
        uiComponents={uiComponents}
        apiEndpoints={apiEndpoints}
        uacCapabilities={uacCapabilities}
        appStoreModules={appStoreModules}
      />
    </PageWithSidebar>
  );
}
