/**
 * GraphQL Subscription Service
 * 
 * This service handles GraphQL subscriptions for real-time data.
 */

const WebSocket = require('ws');
const { parse } = require('graphql');
const { ValidationError } = require('../utils/errors');
const axios = require('axios');

class GraphQLSubscriptionService {
  constructor() {
    this.activeSubscriptions = new Map();
    this.nextSubscriptionId = 1;
  }

  /**
   * Create a new subscription
   * 
   * @param {string} endpoint - GraphQL endpoint URL
   * @param {string} query - GraphQL subscription query
   * @param {Object} variables - Query variables
   * @param {Object} headers - HTTP headers
   * @param {Object} auth - Authentication details
   * @param {Function} onMessage - Callback for subscription messages
   * @param {Function} onError - Callback for subscription errors
   * @returns {Promise<Object>} - Subscription details
   */
  async createSubscription(endpoint, query, variables = {}, headers = {}, auth = null, onMessage, onError) {
    try {
      if (!endpoint) {
        throw new ValidationError('GraphQL endpoint URL is required');
      }
      
      if (!query) {
        throw new ValidationError('GraphQL subscription query is required');
      }
      
      if (!onMessage || typeof onMessage !== 'function') {
        throw new ValidationError('Message callback is required');
      }
      
      // Validate that the query is a subscription
      this.validateSubscriptionQuery(query);
      
      // Check if the endpoint supports subscriptions via introspection
      const supportsSubscriptions = await this.checkSubscriptionSupport(endpoint, headers, auth);
      
      if (!supportsSubscriptions) {
        throw new ValidationError('The GraphQL endpoint does not support subscriptions');
      }
      
      // Determine the subscription protocol to use
      const protocol = await this.determineSubscriptionProtocol(endpoint);
      
      // Create the subscription
      const subscriptionId = this.nextSubscriptionId++;
      const subscriptionDetails = {
        id: subscriptionId,
        endpoint,
        query,
        variables,
        headers,
        auth,
        protocol,
        status: 'connecting',
        createdAt: new Date().toISOString(),
        lastMessageAt: null,
        messageCount: 0,
        errorCount: 0
      };
      
      // Store the subscription
      this.activeSubscriptions.set(subscriptionId, subscriptionDetails);
      
      // Connect to the WebSocket endpoint
      const wsEndpoint = this.getWebSocketEndpoint(endpoint, protocol);
      const ws = new WebSocket(wsEndpoint, protocol === 'graphql-ws' ? 'graphql-ws' : 'graphql-transport-ws');
      
      // Set up WebSocket event handlers
      ws.on('open', () => {
        console.log(`Subscription ${subscriptionId} connected to ${wsEndpoint}`);
        subscriptionDetails.status = 'connected';
        
        // Initialize the connection based on the protocol
        if (protocol === 'graphql-ws') {
          // Apollo protocol
          ws.send(JSON.stringify({
            type: 'connection_init',
            payload: { headers, ...auth }
          }));
        } else {
          // GraphQL over WebSocket protocol
          ws.send(JSON.stringify({
            id: '1',
            type: 'connection_init',
            payload: { headers, ...auth }
          }));
        }
        
        // Start the subscription
        const startMessage = {
          id: subscriptionId.toString(),
          type: 'start',
          payload: {
            query,
            variables
          }
        };
        
        ws.send(JSON.stringify(startMessage));
      });
      
      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          
          if (message.type === 'connection_ack') {
            console.log(`Subscription ${subscriptionId} acknowledged`);
          } else if (message.type === 'data' || message.type === 'next') {
            // Handle subscription data
            subscriptionDetails.lastMessageAt = new Date().toISOString();
            subscriptionDetails.messageCount++;
            
            if (onMessage) {
              onMessage({
                id: subscriptionId,
                data: message.payload.data,
                timestamp: new Date().toISOString()
              });
            }
          } else if (message.type === 'error') {
            // Handle subscription error
            subscriptionDetails.errorCount++;
            
            if (onError) {
              onError({
                id: subscriptionId,
                errors: message.payload,
                timestamp: new Date().toISOString()
              });
            }
          } else if (message.type === 'complete') {
            // Subscription completed
            console.log(`Subscription ${subscriptionId} completed`);
            subscriptionDetails.status = 'completed';
            ws.close();
          }
        } catch (error) {
          console.error(`Error processing subscription message: ${error.message}`);
          
          if (onError) {
            onError({
              id: subscriptionId,
              errors: [{ message: `Error processing message: ${error.message}` }],
              timestamp: new Date().toISOString()
            });
          }
        }
      });
      
      ws.on('error', (error) => {
        console.error(`Subscription ${subscriptionId} error: ${error.message}`);
        subscriptionDetails.status = 'error';
        subscriptionDetails.errorCount++;
        
        if (onError) {
          onError({
            id: subscriptionId,
            errors: [{ message: error.message }],
            timestamp: new Date().toISOString()
          });
        }
      });
      
      ws.on('close', (code, reason) => {
        console.log(`Subscription ${subscriptionId} closed: ${code} - ${reason}`);
        subscriptionDetails.status = 'closed';
        
        // Remove the subscription after a delay
        setTimeout(() => {
          this.activeSubscriptions.delete(subscriptionId);
        }, 60000); // Keep subscription details for 1 minute after closing
      });
      
      // Store the WebSocket connection
      subscriptionDetails.ws = ws;
      
      return {
        id: subscriptionId,
        status: 'connecting',
        endpoint: wsEndpoint,
        protocol
      };
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Cancel a subscription
   * 
   * @param {number} subscriptionId - Subscription ID
   * @returns {Object} - Result of cancellation
   */
  cancelSubscription(subscriptionId) {
    const subscription = this.activeSubscriptions.get(subscriptionId);
    
    if (!subscription) {
      throw new Error(`Subscription with ID ${subscriptionId} not found`);
    }
    
    if (subscription.ws && subscription.ws.readyState === WebSocket.OPEN) {
      // Send stop message
      const stopMessage = {
        id: subscriptionId.toString(),
        type: 'stop'
      };
      
      subscription.ws.send(JSON.stringify(stopMessage));
      
      // Close the connection
      subscription.ws.close();
    }
    
    // Update subscription status
    subscription.status = 'cancelled';
    
    return {
      id: subscriptionId,
      status: 'cancelled',
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * Get all active subscriptions
   * 
   * @returns {Array} - List of active subscriptions
   */
  getActiveSubscriptions() {
    const subscriptions = [];
    
    for (const [id, subscription] of this.activeSubscriptions.entries()) {
      // Don't include the WebSocket connection in the response
      const { ws, ...details } = subscription;
      subscriptions.push(details);
    }
    
    return subscriptions;
  }
  
  /**
   * Get subscription by ID
   * 
   * @param {number} subscriptionId - Subscription ID
   * @returns {Object} - Subscription details
   */
  getSubscription(subscriptionId) {
    const subscription = this.activeSubscriptions.get(subscriptionId);
    
    if (!subscription) {
      throw new Error(`Subscription with ID ${subscriptionId} not found`);
    }
    
    // Don't include the WebSocket connection in the response
    const { ws, ...details } = subscription;
    
    return details;
  }
  
  /**
   * Validate that the query is a subscription
   * 
   * @param {string} query - GraphQL query
   */
  validateSubscriptionQuery(query) {
    try {
      const document = parse(query);
      
      // Check if there's at least one subscription operation
      const hasSubscription = document.definitions.some(
        def => def.kind === 'OperationDefinition' && def.operation === 'subscription'
      );
      
      if (!hasSubscription) {
        throw new ValidationError('Query must contain a subscription operation');
      }
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      
      throw new ValidationError(`Invalid GraphQL query: ${error.message}`);
    }
  }
  
  /**
   * Check if the endpoint supports subscriptions
   * 
   * @param {string} endpoint - GraphQL endpoint URL
   * @param {Object} headers - HTTP headers
   * @param {Object} auth - Authentication details
   * @returns {Promise<boolean>} - Whether subscriptions are supported
   */
  async checkSubscriptionSupport(endpoint, headers = {}, auth = null) {
    try {
      // Prepare request headers
      const requestHeaders = { ...headers };
      
      // Add authentication if provided
      if (auth) {
        switch (auth.type) {
          case 'bearer':
            requestHeaders['Authorization'] = `Bearer ${auth.token}`;
            break;
          case 'api_key':
            requestHeaders[auth.headerName || 'X-API-Key'] = auth.apiKey;
            break;
          // Add other auth types as needed
        }
      }
      
      // GraphQL introspection query to check for subscription support
      const introspectionQuery = `
        query {
          __schema {
            subscriptionType {
              name
              fields {
                name
              }
            }
          }
        }
      `;
      
      const response = await axios({
        url: endpoint,
        method: 'POST',
        headers: requestHeaders,
        data: {
          query: introspectionQuery
        }
      });
      
      const schema = response.data?.data?.__schema;
      
      return !!schema?.subscriptionType;
    } catch (error) {
      console.error(`Error checking subscription support: ${error.message}`);
      return false;
    }
  }
  
  /**
   * Determine the subscription protocol to use
   * 
   * @param {string} endpoint - GraphQL endpoint URL
   * @returns {Promise<string>} - Protocol to use
   */
  async determineSubscriptionProtocol(endpoint) {
    // For now, we'll default to the Apollo protocol
    // In a real implementation, we would try to detect the protocol
    return 'graphql-ws';
  }
  
  /**
   * Get the WebSocket endpoint from the HTTP endpoint
   * 
   * @param {string} endpoint - GraphQL HTTP endpoint URL
   * @param {string} protocol - Subscription protocol
   * @returns {string} - WebSocket endpoint URL
   */
  getWebSocketEndpoint(endpoint, protocol) {
    // Convert HTTP(S) to WS(S)
    let wsEndpoint = endpoint.replace(/^http/, 'ws');
    
    // Add subscription endpoint if needed
    if (protocol === 'graphql-ws' && !wsEndpoint.endsWith('/graphql')) {
      wsEndpoint = wsEndpoint.endsWith('/') ? `${wsEndpoint}graphql` : `${wsEndpoint}/graphql`;
    } else if (protocol === 'graphql-transport-ws' && !wsEndpoint.endsWith('/subscriptions')) {
      wsEndpoint = wsEndpoint.endsWith('/') ? `${wsEndpoint}subscriptions` : `${wsEndpoint}/subscriptions`;
    }
    
    return wsEndpoint;
  }
}

module.exports = GraphQLSubscriptionService;
