/**
 * Privacy Notices Routes
 * 
 * Routes for managing privacy notices and policies.
 */

const express = require('express');
const { check } = require('express-validator');
const PrivacyNoticeController = require('../controllers/PrivacyNoticeController');
const auth = require('../../../middleware/auth');

const router = express.Router();

/**
 * @route   GET /api/privacy/management/notices
 * @desc    Get all privacy notices
 * @access  Private
 */
router.get('/', auth, PrivacyNoticeController.getAllPrivacyNotices);

/**
 * @route   GET /api/privacy/management/notices/:id
 * @desc    Get a single privacy notice
 * @access  Private
 */
router.get('/:id', auth, PrivacyNoticeController.getPrivacyNotice);

/**
 * @route   POST /api/privacy/management/notices
 * @desc    Create a new privacy notice
 * @access  Private
 */
router.post('/', [
  auth,
  [
    check('title', 'Title is required').not().isEmpty(),
    check('version', 'Version is required').not().isEmpty(),
    check('type', 'Type is required').not().isEmpty(),
    check('targetAudience', 'Target audience is required').not().isEmpty(),
    check('effectiveDate', 'Effective date is required').not().isEmpty(),
    check('content', 'Content is required').not().isEmpty()
  ]
], PrivacyNoticeController.createPrivacyNotice);

/**
 * @route   PUT /api/privacy/management/notices/:id
 * @desc    Update a privacy notice
 * @access  Private
 */
router.put('/:id', [
  auth,
  [
    check('title', 'Title is required').optional().not().isEmpty(),
    check('version', 'Version is required').optional().not().isEmpty(),
    check('type', 'Type is required').optional().not().isEmpty(),
    check('targetAudience', 'Target audience is required').optional().not().isEmpty(),
    check('effectiveDate', 'Effective date is required').optional().not().isEmpty(),
    check('content', 'Content is required').optional().not().isEmpty()
  ]
], PrivacyNoticeController.updatePrivacyNotice);

/**
 * @route   DELETE /api/privacy/management/notices/:id
 * @desc    Delete a privacy notice
 * @access  Private
 */
router.delete('/:id', auth, PrivacyNoticeController.deletePrivacyNotice);

/**
 * @route   POST /api/privacy/management/notices/:id/publish
 * @desc    Publish a privacy notice
 * @access  Private
 */
router.post('/:id/publish', [
  auth,
  [
    check('effectiveDate', 'Effective date is required').not().isEmpty()
  ]
], PrivacyNoticeController.publishPrivacyNotice);

/**
 * @route   POST /api/privacy/management/notices/:id/archive
 * @desc    Archive a privacy notice
 * @access  Private
 */
router.post('/:id/archive', auth, PrivacyNoticeController.archivePrivacyNotice);

/**
 * @route   POST /api/privacy/management/notices/:id/new-version
 * @desc    Create a new version of a privacy notice
 * @access  Private
 */
router.post('/:id/new-version', [
  auth,
  [
    check('version', 'Version is required').not().isEmpty()
  ]
], PrivacyNoticeController.createNewVersion);

/**
 * @route   POST /api/privacy/management/notices/:id/languages
 * @desc    Add a language version to a privacy notice
 * @access  Private
 */
router.post('/:id/languages', [
  auth,
  [
    check('code', 'Language code is required').not().isEmpty(),
    check('name', 'Language name is required').not().isEmpty(),
    check('content', 'Content is required').not().isEmpty()
  ]
], PrivacyNoticeController.addLanguage);

/**
 * @route   PUT /api/privacy/management/notices/:id/languages/:code
 * @desc    Update a language version of a privacy notice
 * @access  Private
 */
router.put('/:id/languages/:code', [
  auth,
  [
    check('content', 'Content is required').not().isEmpty()
  ]
], PrivacyNoticeController.updateLanguage);

/**
 * @route   DELETE /api/privacy/management/notices/:id/languages/:code
 * @desc    Delete a language version from a privacy notice
 * @access  Private
 */
router.delete('/:id/languages/:code', auth, PrivacyNoticeController.deleteLanguage);

/**
 * @route   POST /api/privacy/management/notices/:id/approvals
 * @desc    Add an approval to a privacy notice
 * @access  Private
 */
router.post('/:id/approvals', [
  auth,
  [
    check('role', 'Role is required').not().isEmpty(),
    check('user', 'User is required').not().isEmpty(),
    check('comments', 'Comments are required').not().isEmpty()
  ]
], PrivacyNoticeController.addApproval);

/**
 * @route   GET /api/privacy/management/notices/current/:type
 * @desc    Get the current published version of a privacy notice by type
 * @access  Private
 */
router.get('/current/:type', auth, PrivacyNoticeController.getCurrentPublishedVersion);

/**
 * @route   GET /api/privacy/management/notices/current/:type/:region
 * @desc    Get the current published version of a privacy notice by type and region
 * @access  Private
 */
router.get('/current/:type/:region', auth, PrivacyNoticeController.getCurrentPublishedVersionByRegion);

module.exports = router;
