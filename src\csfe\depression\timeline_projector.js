/**
 * Timeline Projector for Depression Prediction
 *
 * This module projects the timeline for a potential depression in the 2027-2031 timeframe,
 * including onset, peak, and recovery phases.
 */

class TimelineProjector {
  /**
   * Create a new Timeline Projector instance
   * @param {Object} targetTimeframe - Target timeframe for depression prediction
   * @param {Object} options - Configuration options
   */
  constructor(targetTimeframe = { start: 2027, end: 2031 }, options = {}) {
    this.targetTimeframe = targetTimeframe;
    this.options = {
      enableQuarterlyProjection: true,
      enablePhaseIdentification: true,
      enableSeverityProjection: true,
      enableMarketProjection: true,
      ...options
    };

    console.log('Timeline Projector initialized');
  }

  /**
   * Project timeline for depression
   * @param {Number} csfeValue - CSFE value
   * @param {Number} baseProb - Base depression probability
   * @param {Object} timelineProb - Timeline probability distribution
   * @returns {Object} - Timeline projection
   */
  projectTimeline(csfeValue, baseProb, timelineProb, economicData = null) {
    console.log('Projecting depression timeline');

    try {
      // Identify most likely onset year
      const onsetYear = this._identifyOnsetYear(timelineProb, economicData);

      // Project quarterly timeline
      const quarterlyProjection = this.options.enableQuarterlyProjection ?
        this._projectQuarterlyTimeline(onsetYear, baseProb, economicData) : null;

      // Identify phases
      const phases = this.options.enablePhaseIdentification ?
        this._identifyPhases(onsetYear, baseProb, economicData) : null;

      // Project severity over time
      const severityProjection = this.options.enableSeverityProjection ?
        this._projectSeverity(onsetYear, baseProb, economicData) : null;

      // Project market impact
      const marketProjection = this.options.enableMarketProjection ?
        this._projectMarketImpact(onsetYear, baseProb, csfeValue, economicData) : null;

      return {
        onsetYear,
        peakYear: phases ? phases.peak.year : null,
        recoveryYear: phases ? phases.recovery.endYear : null,
        quarterlyProjection,
        phases,
        severityProjection,
        marketProjection,
        confidence: this._calculateConfidence(baseProb, timelineProb),
        projectedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error projecting depression timeline:', error);
      throw new Error(`Timeline projection failed: ${error.message}`);
    }
  }

  /**
   * Identify most likely onset year
   * @param {Object} timelineProb - Timeline probability distribution
   * @returns {Number} - Onset year
   * @private
   */
  _identifyOnsetYear(timelineProb, economicData = null) {
    // Check for pandemic-depression cycle data
    if (economicData && economicData.pandemicDepressionCycle) {
      const pandemicData = economicData.pandemicDepressionCycle;
      const expectedDepressionYear = pandemicData.lastPandemic + pandemicData.historicalCycleYears;

      // Check if expected depression year is within our timeframe
      if (expectedDepressionYear >= this.targetTimeframe.start &&
          expectedDepressionYear <= this.targetTimeframe.end) {
        console.log(`Pandemic-Depression Cycle: Using expected depression year ${expectedDepressionYear} based on ${pandemicData.lastPandemic} pandemic + ${pandemicData.historicalCycleYears} year cycle`);
        return expectedDepressionYear;
      }
    }

    // If no pandemic cycle or expected year outside timeframe, use highest probability
    const sortedYears = Object.entries(timelineProb)
      .sort(([, a], [, b]) => b - a);

    return parseInt(sortedYears[0][0]);
  }

  /**
   * Project quarterly timeline
   * @param {Number} onsetYear - Onset year
   * @param {Number} baseProb - Base depression probability
   * @returns {Object} - Quarterly projection
   * @private
   */
  _projectQuarterlyTimeline(onsetYear, baseProb) {
    // In a real implementation, this would use sophisticated analysis
    // For now, use a simplified approach

    const quarterlyProjection = {};
    const startYear = onsetYear - 1;
    const endYear = onsetYear + 3;

    // Generate quarterly projection
    for (let year = startYear; year <= endYear; year++) {
      for (let quarter = 1; quarter <= 4; quarter++) {
        const quarterKey = `${year}Q${quarter}`;

        // Calculate distance from onset (in quarters)
        const quartersFromOnset = (year - onsetYear) * 4 + quarter - 1;

        // Calculate probability based on distance from onset
        let probability;

        if (quartersFromOnset < 0) {
          // Pre-onset: increasing probability
          probability = baseProb * (1 + quartersFromOnset / 8);
        } else if (quartersFromOnset <= 4) {
          // First year of depression: high probability
          probability = baseProb;
        } else {
          // Recovery phase: decreasing probability
          probability = baseProb * (1 - (quartersFromOnset - 4) / 12);
        }

        // Ensure probability is between 0 and 1
        probability = Math.max(0, Math.min(1, probability));

        quarterlyProjection[quarterKey] = probability;
      }
    }

    return quarterlyProjection;
  }

  /**
   * Identify depression phases
   * @param {Number} onsetYear - Onset year
   * @param {Number} baseProb - Base depression probability
   * @returns {Object} - Depression phases
   * @private
   */
  _identifyPhases(onsetYear, baseProb, economicData = null) {
    // In a real implementation, this would use sophisticated analysis
    // For now, use a simplified approach

    // Calculate phase durations based on probability
    // Higher probability = longer depression
    let preDuration = 1; // Pre-depression warning phase
    let onsetDuration = 0.5; // Onset phase
    let peakDuration = baseProb * 2; // Peak phase
    let recoveryDuration = baseProb * 3; // Recovery phase

    // Adjust phase durations based on pandemic-depression cycle
    if (economicData && economicData.pandemicDepressionCycle) {
      const pandemicData = economicData.pandemicDepressionCycle;
      const currentYear = new Date().getFullYear();
      const yearsSincePandemic = currentYear - pandemicData.lastPandemic;
      const yearsUntilDepression = pandemicData.historicalCycleYears - yearsSincePandemic;

      // If we're close to the expected depression year based on the pandemic cycle
      if (yearsUntilDepression >= 0 && yearsUntilDepression <= 3) {
        // Adjust phase durations based on pandemic severity and cycle strength
        const cycleFactor = pandemicData.cycleStrength * pandemicData.pandemicSeverity;

        // More severe pandemic and stronger cycle = longer and more severe depression
        preDuration = Math.max(1, preDuration * (1 + cycleFactor * 0.5));
        onsetDuration = Math.max(0.5, onsetDuration * (1 + cycleFactor * 0.3));
        peakDuration = Math.max(baseProb * 2, peakDuration * (1 + cycleFactor));
        recoveryDuration = Math.max(baseProb * 3, recoveryDuration * (1 + cycleFactor * 1.5));

        console.log(`Pandemic-Depression Cycle: Adjusted phase durations based on pandemic cycle (factor: ${cycleFactor})`);
      }
    }

    // Calculate phase timing
    const preStart = onsetYear - preDuration;
    const onsetStart = onsetYear;
    const peakStart = onsetStart + onsetDuration;
    const peakEnd = peakStart + peakDuration;
    const recoveryStart = peakEnd;
    const recoveryEnd = recoveryStart + recoveryDuration;

    return {
      pre: {
        name: 'Pre-Depression Warning',
        startYear: preStart,
        endYear: onsetStart,
        duration: preDuration,
        description: 'Early warning signs appear in financial markets and economic indicators.'
      },
      onset: {
        name: 'Depression Onset',
        startYear: onsetStart,
        endYear: peakStart,
        duration: onsetDuration,
        description: 'Economic contraction begins, with rapid deterioration in key indicators.'
      },
      peak: {
        name: 'Peak Depression',
        startYear: peakStart,
        endYear: peakEnd,
        duration: peakDuration,
        year: Math.floor(peakStart + peakDuration / 2),
        description: 'Maximum economic contraction, with high unemployment and market distress.'
      },
      recovery: {
        name: 'Recovery Phase',
        startYear: recoveryStart,
        endYear: recoveryEnd,
        duration: recoveryDuration,
        description: 'Gradual economic improvement, though still below pre-depression levels.'
      }
    };
  }

  /**
   * Project severity over time
   * @param {Number} onsetYear - Onset year
   * @param {Number} baseProb - Base depression probability
   * @returns {Object} - Severity projection
   * @private
   */
  _projectSeverity(onsetYear, baseProb, economicData = null) {
    // In a real implementation, this would use sophisticated analysis
    // For now, use a simplified approach

    const severityProjection = {};
    const startYear = onsetYear - 1;
    const endYear = onsetYear + 4;

    // Define severity metrics
    const metrics = [
      'gdpContraction',
      'unemploymentRate',
      'marketDecline',
      'creditDefault',
      'consumerSpending'
    ];

    // Generate severity projection
    for (let year = startYear; year <= endYear; year++) {
      // Calculate distance from onset
      const yearsFromOnset = year - onsetYear;

      // Check for pandemic-depression cycle data
      let pandemicCycleFactor = 0;
      if (economicData && economicData.pandemicDepressionCycle) {
        const pandemicData = economicData.pandemicDepressionCycle;

        // Calculate years since last pandemic
        const currentYear = new Date().getFullYear();
        const yearsSincePandemic = currentYear - pandemicData.lastPandemic;

        // Calculate years until expected depression based on historical cycle
        const yearsUntilDepression = pandemicData.historicalCycleYears - yearsSincePandemic;

        // Calculate pandemic-depression cycle factor
        if (yearsUntilDepression >= 0 && yearsUntilDepression <= 3) {
          pandemicCycleFactor = (1 - yearsUntilDepression / 5) * pandemicData.cycleStrength * pandemicData.pandemicSeverity;
        }
      }

      // Calculate severity factor based on distance from onset
      let severityFactor;

      if (yearsFromOnset < 0) {
        // Pre-onset: low severity
        severityFactor = 0.2 * baseProb * (1 + yearsFromOnset);
      } else if (yearsFromOnset === 0) {
        // Onset year: moderate severity
        severityFactor = 0.6 * baseProb;
      } else if (yearsFromOnset === 1) {
        // Peak year: high severity
        severityFactor = baseProb;
      } else {
        // Recovery phase: decreasing severity
        severityFactor = baseProb * (1 - (yearsFromOnset - 1) / 3);
      }

      // Apply pandemic cycle factor if applicable
      if (pandemicCycleFactor > 0) {
        // Increase severity based on pandemic cycle factor
        severityFactor = Math.max(severityFactor, severityFactor * (1 + pandemicCycleFactor));
        console.log(`Pandemic-Depression Cycle: Adjusted severity for year ${year} with factor ${pandemicCycleFactor}`);
      }

      // Ensure severity factor is between 0 and 1
      severityFactor = Math.max(0, Math.min(1, severityFactor));

      // Calculate severity metrics
      const yearSeverity = {
        overall: severityFactor
      };

      metrics.forEach(metric => {
        // Add some variation to each metric
        const variation = (Math.random() * 0.4) - 0.2; // -0.2 to 0.2
        yearSeverity[metric] = Math.max(0, Math.min(1, severityFactor + variation));
      });

      severityProjection[year] = yearSeverity;
    }

    return severityProjection;
  }

  /**
   * Project market impact
   * @param {Number} onsetYear - Onset year
   * @param {Number} baseProb - Base depression probability
   * @param {Number} csfeValue - CSFE value
   * @returns {Object} - Market projection
   * @private
   */
  _projectMarketImpact(onsetYear, baseProb, csfeValue, economicData = null) {
    // In a real implementation, this would use sophisticated analysis
    // For now, use a simplified approach

    const marketProjection = {};
    const startYear = onsetYear - 1;
    const endYear = onsetYear + 4;

    // Define asset classes
    const assetClasses = [
      'equity',
      'fixed_income',
      'commodities',
      'forex',
      'crypto'
    ];

    // Generate market projection
    for (let year = startYear; year <= endYear; year++) {
      // Calculate distance from onset
      const yearsFromOnset = year - onsetYear;

      // Check for pandemic-depression cycle data
      let pandemicCycleFactor = 0;
      if (economicData && economicData.pandemicDepressionCycle) {
        const pandemicData = economicData.pandemicDepressionCycle;

        // Calculate years since last pandemic
        const currentYear = new Date().getFullYear();
        const yearsSincePandemic = currentYear - pandemicData.lastPandemic;

        // Calculate years until expected depression based on historical cycle
        const yearsUntilDepression = pandemicData.historicalCycleYears - yearsSincePandemic;

        // Calculate pandemic-depression cycle factor
        if (yearsUntilDepression >= 0 && yearsUntilDepression <= 3) {
          pandemicCycleFactor = (1 - yearsUntilDepression / 5) * pandemicData.cycleStrength * pandemicData.pandemicSeverity;
        }
      }

      // Calculate market impact factor based on distance from onset
      let impactFactor;

      if (yearsFromOnset < 0) {
        // Pre-onset: moderate decline
        impactFactor = 0.3 * baseProb * (1 + yearsFromOnset);
      } else if (yearsFromOnset === 0) {
        // Onset year: sharp decline
        impactFactor = 0.7 * baseProb;
      } else if (yearsFromOnset === 1) {
        // Peak year: maximum decline
        impactFactor = baseProb;
      } else {
        // Recovery phase: improving markets
        impactFactor = baseProb * (1 - (yearsFromOnset - 1) / 3);
      }

      // Apply pandemic cycle factor if applicable
      if (pandemicCycleFactor > 0) {
        // Increase impact based on pandemic cycle factor
        impactFactor = Math.max(impactFactor, impactFactor * (1 + pandemicCycleFactor));
        console.log(`Pandemic-Depression Cycle: Adjusted market impact for year ${year} with factor ${pandemicCycleFactor}`);
      }

      // Ensure impact factor is between 0 and 1
      impactFactor = Math.max(0, Math.min(1, impactFactor));

      // Calculate market metrics
      const yearMarket = {
        overall: {
          direction: impactFactor > 0.5 ? 'bearish' : (impactFactor > 0.3 ? 'neutral' : 'bullish'),
          volatility: 10 + impactFactor * 40, // 10-50% volatility
          decline: impactFactor * 60 // 0-60% decline
        },
        assetClasses: {}
      };

      // Calculate impact on each asset class
      assetClasses.forEach(assetClass => {
        let assetImpact;

        switch (assetClass) {
          case 'equity':
            // Equities decline sharply in depression
            assetImpact = impactFactor * 1.2;
            break;
          case 'fixed_income':
            // Fixed income may benefit from flight to safety
            assetImpact = impactFactor * 0.5;
            break;
          case 'commodities':
            // Commodities mixed but generally decline
            assetImpact = impactFactor * 0.8;
            break;
          case 'forex':
            // Forex depends on relative economic strength
            assetImpact = impactFactor * 0.7;
            break;
          case 'crypto':
            // Crypto highly volatile
            assetImpact = impactFactor * 1.5;
            break;
          default:
            assetImpact = impactFactor;
        }

        // Ensure asset impact is between 0 and 1
        assetImpact = Math.max(0, Math.min(1, assetImpact));

        yearMarket.assetClasses[assetClass] = {
          direction: assetImpact > 0.5 ? 'bearish' : (assetImpact > 0.3 ? 'neutral' : 'bullish'),
          volatility: 10 + assetImpact * 50, // 10-60% volatility
          decline: assetImpact * 70, // 0-70% decline
          expectedReturn: assetImpact > 0.5 ? -20 * assetImpact : 10 * (1 - assetImpact) // -20% to +10%
        };
      });

      marketProjection[year] = yearMarket;
    }

    return marketProjection;
  }

  /**
   * Calculate confidence in timeline projection
   * @param {Number} baseProb - Base depression probability
   * @param {Object} timelineProb - Timeline probability distribution
   * @returns {Number} - Confidence score (0-1)
   * @private
   */
  _calculateConfidence(baseProb, timelineProb) {
    // In a real implementation, this would use sophisticated analysis
    // For now, use a simplified approach

    // Base confidence on depression probability
    const baseFactor = baseProb;

    // Adjust based on timeline distribution
    // More concentrated distribution = higher confidence
    const probValues = Object.values(timelineProb);
    const maxProb = Math.max(...probValues);
    const avgProb = probValues.reduce((sum, val) => sum + val, 0) / probValues.length;
    const concentration = maxProb / avgProb;

    // Calculate confidence
    const confidence = baseFactor * (0.5 + 0.5 * concentration);

    // Adjust for distance to target timeframe
    const currentYear = new Date().getFullYear();
    const yearsToStart = this.targetTimeframe.start - currentYear;
    const timeAdjustment = Math.max(0.5, 1 - (yearsToStart / 10));

    return Math.min(1, confidence * timeAdjustment);
  }
}

module.exports = TimelineProjector;
