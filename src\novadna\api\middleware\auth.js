/**
 * Authentication Middleware
 * 
 * This module provides middleware for authentication and authorization.
 */

/**
 * Authenticate a service
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function authenticateService(req, res, next) {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      status: 'error',
      error: 'Authentication required'
    });
  }
  
  const token = authHeader.split(' ')[1];
  
  // In a real implementation, this would verify the token
  // For now, we'll simulate token verification
  
  if (!token || !token.startsWith('service-token-')) {
    return res.status(401).json({
      status: 'error',
      error: 'Invalid token'
    });
  }
  
  // Simulate service lookup
  req.service = {
    id: 'service-123',
    name: 'Test Emergency Service',
    type: 'PARAMEDIC'
  };
  
  // Check if user info is included in the token
  if (token.includes('user')) {
    req.user = {
      id: 'user-123',
      name: '<PERSON>',
      role: 'PARAMEDIC'
    };
  }
  
  next();
}

/**
 * Authenticate a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function authenticateUser(req, res, next) {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      status: 'error',
      error: 'Authentication required'
    });
  }
  
  const token = authHeader.split(' ')[1];
  
  // In a real implementation, this would verify the token
  // For now, we'll simulate token verification
  
  if (!token || !token.startsWith('user-token-')) {
    return res.status(401).json({
      status: 'error',
      error: 'Invalid token'
    });
  }
  
  // Simulate user lookup
  req.user = {
    id: 'user-123',
    name: 'John Doe',
    email: '<EMAIL>'
  };
  
  next();
}

/**
 * Authorize access to a profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function authorizeProfile(req, res, next) {
  const { profileId } = req.params;
  const { user } = req;
  
  // In a real implementation, this would check if the user has access to the profile
  // For now, we'll simulate authorization
  
  // Simulate profile lookup
  const profileOwner = 'user-123';
  
  if (user.id !== profileOwner) {
    return res.status(403).json({
      status: 'error',
      error: 'Not authorized to access this profile'
    });
  }
  
  next();
}

/**
 * Authorize admin access
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function authorizeAdmin(req, res, next) {
  const { user } = req;
  
  // In a real implementation, this would check if the user is an admin
  // For now, we'll simulate admin authorization
  
  if (!user || user.role !== 'ADMIN') {
    return res.status(403).json({
      status: 'error',
      error: 'Admin access required'
    });
  }
  
  next();
}

/**
 * Authorize emergency service
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function authorizeEmergencyService(req, res, next) {
  const { service } = req;
  
  // In a real implementation, this would check if the service is an emergency service
  // For now, we'll simulate emergency service authorization
  
  const emergencyServiceTypes = ['PARAMEDIC', 'EMT', 'DOCTOR', 'NURSE', 'HOSPITAL'];
  
  if (!service || !emergencyServiceTypes.includes(service.type)) {
    return res.status(403).json({
      status: 'error',
      error: 'Emergency service access required'
    });
  }
  
  next();
}

module.exports = {
  authenticateService,
  authenticateUser,
  authorizeProfile,
  authorizeAdmin,
  authorizeEmergencyService
};
