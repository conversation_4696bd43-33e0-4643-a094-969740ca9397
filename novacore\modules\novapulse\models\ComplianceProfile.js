/**
 * NovaCore Compliance Profile Model
 * 
 * This model defines the schema for compliance profiles in the NovaPulse module.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define framework applicability schema
const frameworkApplicabilitySchema = new Schema({
  frameworkId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Framework', 
    required: true 
  },
  frameworkName: { 
    type: String, 
    required: true, 
    trim: true 
  },
  version: { 
    type: String, 
    trim: true 
  },
  applicabilityReason: { 
    type: String, 
    enum: [
      'regulatory_requirement', 
      'contractual_obligation', 
      'industry_standard', 
      'business_requirement', 
      'risk_mitigation', 
      'voluntary'
    ], 
    required: true 
  },
  applicabilityDetails: { 
    type: String 
  },
  priority: { 
    type: String, 
    enum: ['low', 'medium', 'high', 'critical'], 
    default: 'medium' 
  },
  status: { 
    type: String, 
    enum: [
      'planned', 
      'in_progress', 
      'compliant', 
      'partially_compliant', 
      'non_compliant', 
      'not_applicable'
    ], 
    default: 'planned' 
  },
  compliancePercentage: { 
    type: Number, 
    min: 0, 
    max: 100, 
    default: 0 
  },
  lastAssessmentDate: { 
    type: Date 
  },
  nextAssessmentDate: { 
    type: Date 
  },
  certificationStatus: {
    certified: { 
      type: Boolean, 
      default: false 
    },
    certificationDate: { 
      type: Date 
    },
    expirationDate: { 
      type: Date 
    },
    certificationAuthority: { 
      type: String, 
      trim: true 
    },
    certificateId: { 
      type: String, 
      trim: true 
    }
  },
  exemptions: [{
    controlId: { 
      type: String, 
      required: true, 
      trim: true 
    },
    reason: { 
      type: String, 
      required: true 
    },
    approvedBy: { 
      type: Schema.Types.ObjectId, 
      ref: 'User' 
    },
    approvedDate: { 
      type: Date 
    },
    expirationDate: { 
      type: Date 
    },
    compensatingControls: [{ 
      type: String, 
      trim: true 
    }]
  }],
  scopeExclusions: [{
    type: { 
      type: String, 
      enum: [
        'system', 
        'process', 
        'department', 
        'location', 
        'data_type', 
        'other'
      ], 
      required: true 
    },
    name: { 
      type: String, 
      required: true, 
      trim: true 
    },
    reason: { 
      type: String, 
      required: true 
    },
    approvedBy: { 
      type: Schema.Types.ObjectId, 
      ref: 'User' 
    },
    approvedDate: { 
      type: Date 
    }
  }],
  notes: { 
    type: String 
  }
}, { _id: false });

// Define regulation applicability schema
const regulationApplicabilitySchema = new Schema({
  regulationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Regulation', 
    required: true 
  },
  regulationName: { 
    type: String, 
    required: true, 
    trim: true 
  },
  version: { 
    type: String, 
    trim: true 
  },
  applicabilityReason: { 
    type: String, 
    enum: [
      'jurisdiction', 
      'industry', 
      'data_types', 
      'business_activities', 
      'size', 
      'voluntary'
    ], 
    required: true 
  },
  applicabilityDetails: { 
    type: String 
  },
  priority: { 
    type: String, 
    enum: ['low', 'medium', 'high', 'critical'], 
    default: 'medium' 
  },
  status: { 
    type: String, 
    enum: [
      'planned', 
      'in_progress', 
      'compliant', 
      'partially_compliant', 
      'non_compliant', 
      'not_applicable'
    ], 
    default: 'planned' 
  },
  compliancePercentage: { 
    type: Number, 
    min: 0, 
    max: 100, 
    default: 0 
  },
  lastAssessmentDate: { 
    type: Date 
  },
  nextAssessmentDate: { 
    type: Date 
  },
  exemptions: [{
    requirementId: { 
      type: String, 
      required: true, 
      trim: true 
    },
    reason: { 
      type: String, 
      required: true 
    },
    approvedBy: { 
      type: Schema.Types.ObjectId, 
      ref: 'User' 
    },
    approvedDate: { 
      type: Date 
    },
    expirationDate: { 
      type: Date 
    },
    compensatingControls: [{ 
      type: String, 
      trim: true 
    }]
  }],
  scopeExclusions: [{
    type: { 
      type: String, 
      enum: [
        'system', 
        'process', 
        'department', 
        'location', 
        'data_type', 
        'other'
      ], 
      required: true 
    },
    name: { 
      type: String, 
      required: true, 
      trim: true 
    },
    reason: { 
      type: String, 
      required: true 
    },
    approvedBy: { 
      type: Schema.Types.ObjectId, 
      ref: 'User' 
    },
    approvedDate: { 
      type: Date 
    }
  }],
  notes: { 
    type: String 
  }
}, { _id: false });

// Define data inventory schema
const dataInventorySchema = new Schema({
  dataType: { 
    type: String, 
    required: true, 
    trim: true 
  },
  classification: { 
    type: String, 
    enum: [
      'public', 
      'internal', 
      'confidential', 
      'restricted', 
      'regulated'
    ], 
    required: true 
  },
  categories: [{ 
    type: String, 
    trim: true 
  }],
  volume: { 
    type: String, 
    enum: ['small', 'medium', 'large', 'very_large'], 
    default: 'medium' 
  },
  locations: [{ 
    type: String, 
    trim: true 
  }],
  systems: [{ 
    type: String, 
    trim: true 
  }],
  retentionPeriod: { 
    type: String, 
    trim: true 
  },
  regulatoryRequirements: [{
    regulationId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Regulation' 
    },
    regulationName: { 
      type: String, 
      trim: true 
    },
    requirementId: { 
      type: String, 
      trim: true 
    }
  }]
}, { _id: false });

// Define business activity schema
const businessActivitySchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String 
  },
  category: { 
    type: String, 
    trim: true 
  },
  dataTypes: [{ 
    type: String, 
    trim: true 
  }],
  departments: [{ 
    type: String, 
    trim: true 
  }],
  systems: [{ 
    type: String, 
    trim: true 
  }],
  regulatoryRequirements: [{
    regulationId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Regulation' 
    },
    regulationName: { 
      type: String, 
      trim: true 
    },
    requirementId: { 
      type: String, 
      trim: true 
    }
  }]
}, { _id: false });

// Define compliance profile schema
const complianceProfileSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    required: true 
  },
  organizationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Organization', 
    required: true 
  },
  organizationDetails: {
    name: { 
      type: String, 
      required: true, 
      trim: true 
    },
    industry: { 
      type: String, 
      trim: true 
    },
    subIndustry: { 
      type: String, 
      trim: true 
    },
    size: { 
      type: String, 
      enum: ['small', 'medium', 'large', 'enterprise'], 
      default: 'medium' 
    },
    employeeCount: { 
      type: Number 
    },
    annualRevenue: { 
      type: Number 
    },
    publiclyTraded: { 
      type: Boolean, 
      default: false 
    },
    foundedYear: { 
      type: Number 
    }
  },
  jurisdictions: [{
    country: { 
      type: String, 
      required: true, 
      trim: true 
    },
    region: { 
      type: String, 
      trim: true 
    },
    city: { 
      type: String, 
      trim: true 
    },
    isPrimary: { 
      type: Boolean, 
      default: false 
    },
    hasOperations: { 
      type: Boolean, 
      default: true 
    },
    hasCustomers: { 
      type: Boolean, 
      default: false 
    },
    hasEmployees: { 
      type: Boolean, 
      default: false 
    },
    hasDataStorage: { 
      type: Boolean, 
      default: false 
    }
  }],
  dataInventory: [dataInventorySchema],
  businessActivities: [businessActivitySchema],
  applicableFrameworks: [frameworkApplicabilitySchema],
  applicableRegulations: [regulationApplicabilitySchema],
  overallComplianceStatus: {
    status: { 
      type: String, 
      enum: [
        'not_assessed', 
        'non_compliant', 
        'partially_compliant', 
        'mostly_compliant', 
        'compliant'
      ], 
      default: 'not_assessed' 
    },
    compliancePercentage: { 
      type: Number, 
      min: 0, 
      max: 100, 
      default: 0 
    },
    lastAssessmentDate: { 
      type: Date 
    },
    nextAssessmentDate: { 
      type: Date 
    }
  },
  complianceOfficer: {
    userId: { 
      type: Schema.Types.ObjectId, 
      ref: 'User' 
    },
    name: { 
      type: String, 
      trim: true 
    },
    email: { 
      type: String, 
      trim: true 
    },
    phone: { 
      type: String, 
      trim: true 
    }
  },
  assessmentSchedule: {
    frequency: { 
      type: String, 
      enum: [
        'monthly', 
        'quarterly', 
        'semi_annual', 
        'annual', 
        'biennial', 
        'custom'
      ], 
      default: 'annual' 
    },
    customFrequency: { 
      type: String, 
      trim: true 
    },
    nextScheduledDate: { 
      type: Date 
    },
    lastCompletedDate: { 
      type: Date 
    }
  },
  tags: [{ 
    type: String, 
    trim: true 
  }],
  status: { 
    type: String, 
    enum: ['draft', 'active', 'archived'], 
    default: 'draft' 
  },
  metadata: {
    type: Map,
    of: Schema.Types.Mixed
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
complianceProfileSchema.index({ name: 1 });
complianceProfileSchema.index({ organizationId: 1 });
complianceProfileSchema.index({ 'organizationDetails.industry': 1 });
complianceProfileSchema.index({ 'organizationDetails.size': 1 });
complianceProfileSchema.index({ 'jurisdictions.country': 1 });
complianceProfileSchema.index({ 'jurisdictions.region': 1 });
complianceProfileSchema.index({ 'dataInventory.dataType': 1 });
complianceProfileSchema.index({ 'dataInventory.classification': 1 });
complianceProfileSchema.index({ 'applicableFrameworks.frameworkId': 1 });
complianceProfileSchema.index({ 'applicableFrameworks.status': 1 });
complianceProfileSchema.index({ 'applicableRegulations.regulationId': 1 });
complianceProfileSchema.index({ 'applicableRegulations.status': 1 });
complianceProfileSchema.index({ 'overallComplianceStatus.status': 1 });
complianceProfileSchema.index({ status: 1 });
complianceProfileSchema.index({ tags: 1 });
complianceProfileSchema.index({ createdAt: 1 });
complianceProfileSchema.index({ updatedAt: 1 });

// Add methods
complianceProfileSchema.methods.getApplicableFramework = function(frameworkId) {
  if (!this.applicableFrameworks || this.applicableFrameworks.length === 0) {
    return null;
  }
  
  return this.applicableFrameworks.find(framework => 
    framework.frameworkId.toString() === frameworkId.toString()
  );
};

complianceProfileSchema.methods.getApplicableRegulation = function(regulationId) {
  if (!this.applicableRegulations || this.applicableRegulations.length === 0) {
    return null;
  }
  
  return this.applicableRegulations.find(regulation => 
    regulation.regulationId.toString() === regulationId.toString()
  );
};

complianceProfileSchema.methods.getDataInventoryItem = function(dataType) {
  if (!this.dataInventory || this.dataInventory.length === 0) {
    return null;
  }
  
  return this.dataInventory.find(item => item.dataType === dataType);
};

complianceProfileSchema.methods.getBusinessActivity = function(name) {
  if (!this.businessActivities || this.businessActivities.length === 0) {
    return null;
  }
  
  return this.businessActivities.find(activity => activity.name === name);
};

complianceProfileSchema.methods.getPrimaryJurisdiction = function() {
  if (!this.jurisdictions || this.jurisdictions.length === 0) {
    return null;
  }
  
  return this.jurisdictions.find(jurisdiction => jurisdiction.isPrimary) || this.jurisdictions[0];
};

complianceProfileSchema.methods.updateOverallComplianceStatus = function() {
  if (!this.applicableFrameworks || this.applicableFrameworks.length === 0) {
    this.overallComplianceStatus.status = 'not_assessed';
    this.overallComplianceStatus.compliancePercentage = 0;
    return;
  }
  
  // Calculate weighted average of framework compliance percentages
  let totalWeight = 0;
  let weightedSum = 0;
  
  for (const framework of this.applicableFrameworks) {
    let weight = 1;
    
    // Assign weights based on priority
    switch (framework.priority) {
      case 'critical':
        weight = 4;
        break;
      case 'high':
        weight = 3;
        break;
      case 'medium':
        weight = 2;
        break;
      case 'low':
        weight = 1;
        break;
    }
    
    totalWeight += weight;
    weightedSum += framework.compliancePercentage * weight;
  }
  
  const overallPercentage = totalWeight > 0 ? Math.round(weightedSum / totalWeight) : 0;
  this.overallComplianceStatus.compliancePercentage = overallPercentage;
  
  // Determine status based on percentage
  if (overallPercentage >= 90) {
    this.overallComplianceStatus.status = 'compliant';
  } else if (overallPercentage >= 75) {
    this.overallComplianceStatus.status = 'mostly_compliant';
  } else if (overallPercentage >= 50) {
    this.overallComplianceStatus.status = 'partially_compliant';
  } else if (overallPercentage > 0) {
    this.overallComplianceStatus.status = 'non_compliant';
  } else {
    this.overallComplianceStatus.status = 'not_assessed';
  }
  
  // Update last assessment date
  this.overallComplianceStatus.lastAssessmentDate = new Date();
  
  // Calculate next assessment date based on schedule
  const nextDate = new Date();
  
  switch (this.assessmentSchedule.frequency) {
    case 'monthly':
      nextDate.setMonth(nextDate.getMonth() + 1);
      break;
    case 'quarterly':
      nextDate.setMonth(nextDate.getMonth() + 3);
      break;
    case 'semi_annual':
      nextDate.setMonth(nextDate.getMonth() + 6);
      break;
    case 'annual':
      nextDate.setFullYear(nextDate.getFullYear() + 1);
      break;
    case 'biennial':
      nextDate.setFullYear(nextDate.getFullYear() + 2);
      break;
    case 'custom':
      // Custom frequency would need specific handling
      nextDate.setMonth(nextDate.getMonth() + 3); // Default to quarterly
      break;
  }
  
  this.overallComplianceStatus.nextAssessmentDate = nextDate;
  this.assessmentSchedule.nextScheduledDate = nextDate;
  this.assessmentSchedule.lastCompletedDate = new Date();
};

// Add statics
complianceProfileSchema.statics.findByOrganization = function(organizationId) {
  return this.find({ organizationId, status: 'active' });
};

complianceProfileSchema.statics.findByFramework = function(frameworkId) {
  return this.find({
    'applicableFrameworks.frameworkId': frameworkId,
    status: 'active'
  });
};

complianceProfileSchema.statics.findByRegulation = function(regulationId) {
  return this.find({
    'applicableRegulations.regulationId': regulationId,
    status: 'active'
  });
};

complianceProfileSchema.statics.findByComplianceStatus = function(status) {
  return this.find({
    'overallComplianceStatus.status': status,
    status: 'active'
  });
};

complianceProfileSchema.statics.findByJurisdiction = function(country, region) {
  const query = { status: 'active' };
  
  if (country) {
    query['jurisdictions.country'] = country;
  }
  
  if (region) {
    query['jurisdictions.region'] = region;
  }
  
  return this.find(query);
};

complianceProfileSchema.statics.findByDataType = function(dataType) {
  return this.find({
    'dataInventory.dataType': dataType,
    status: 'active'
  });
};

// Create model
const ComplianceProfile = mongoose.model('ComplianceProfile', complianceProfileSchema);

module.exports = ComplianceProfile;
