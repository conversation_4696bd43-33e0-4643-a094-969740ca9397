/**
 * CSFE Adapter
 *
 * This module provides a concrete implementation of the CSFE (Cyber-Safety Financial Engine)
 * adapter for the Finite Universe Principle defense system. It connects the boundary
 * enforcement mechanisms with the CSFE engine to ensure that financial domain operations
 * remain within finite boundaries.
 * 
 * Key features:
 * 1. Domain-specific processing for financial data
 * 2. Integration with CSFE components
 * 3. Financial domain boundary enforcement
 * 4. Specialized handling for financial metrics
 */

const EventEmitter = require('events');
const { MAX_SAFE_BOUNDS } = require('../constants');

/**
 * CSFEAdapter class
 * 
 * Provides integration between the Boundary Enforcer and the CSFE engine.
 */
class CSFEAdapter extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      strictMode: true,
      csfeInstance: null, // Optional CSFE instance
      precisionDigits: 2, // Number of decimal places for financial values
      ...options
    };

    // Initialize financial domain boundaries
    this.financialBoundaries = {
      ...MAX_SAFE_BOUNDS.FINANCIAL,
      // Additional financial-specific boundaries
      MAX_INTEREST_RATE: 1.0, // 100%
      MIN_INTEREST_RATE: 0.0, // 0%
      MAX_TRANSACTION_VOLUME: 1e12, // 1 trillion
      MIN_TRANSACTION_VOLUME: 0.0,
      MAX_PROFIT_MARGIN: 1.0, // 100%
      MIN_PROFIT_MARGIN: -1.0 // -100%
    };

    // Initialize metrics
    this.metrics = {
      processedDataCount: 0,
      boundaryViolations: 0,
      totalTransactionVolume: 0,
      averageInterestRate: 0,
      totalInterestRate: 0,
      roundingAdjustments: 0
    };

    if (this.options.enableLogging) {
      console.log('CSFEAdapter initialized with options:', this.options);
    }
  }

  /**
   * Process data through the CSFE engine
   * @param {Object} data - Data to process
   * @returns {Object} - Processed data
   */
  async processData(data) {
    try {
      // Apply financial domain pre-processing
      const preprocessedData = this._preprocessFinancialData(data);
      
      // Process through CSFE if available
      let processedData = preprocessedData;
      if (this.options.csfeInstance) {
        processedData = await this._processThroughCSFE(preprocessedData);
      } else {
        // Apply default processing if no CSFE instance is available
        processedData = this._applyDefaultProcessing(preprocessedData);
      }
      
      // Apply financial domain post-processing
      const postprocessedData = this._postprocessFinancialData(processedData);
      
      // Update metrics
      this._updateMetrics(postprocessedData);
      
      return postprocessedData;
    } catch (error) {
      this.emit('processing-error', { error: error.message, data });
      
      if (this.options.strictMode) {
        throw error;
      }
      
      // Return sanitized data on error
      return this._sanitizeFinancialData(data);
    }
  }

  /**
   * Pre-process financial domain data
   * @param {Object} data - Data to pre-process
   * @returns {Object} - Pre-processed data
   * @private
   */
  _preprocessFinancialData(data) {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    const result = { ...data };
    
    // Round balance to specified precision
    if (result.balance !== undefined) {
      const originalValue = result.balance;
      result.balance = this._roundToPrecision(result.balance);
      
      if (result.balance !== originalValue) {
        this.metrics.roundingAdjustments++;
      }
    }
    
    // Sanitize interest rate
    if (result.interestRate !== undefined) {
      const originalValue = result.interestRate;
      result.interestRate = Math.max(
        this.financialBoundaries.MIN_INTEREST_RATE,
        Math.min(this.financialBoundaries.MAX_INTEREST_RATE, result.interestRate)
      );
      
      if (result.interestRate !== originalValue) {
        this.metrics.boundaryViolations++;
      }
    }
    
    // Sanitize transaction volume
    if (result.transactionVolume !== undefined) {
      const originalValue = result.transactionVolume;
      result.transactionVolume = Math.max(
        this.financialBoundaries.MIN_TRANSACTION_VOLUME,
        Math.min(this.financialBoundaries.MAX_TRANSACTION_VOLUME, result.transactionVolume)
      );
      
      if (result.transactionVolume !== originalValue) {
        this.metrics.boundaryViolations++;
      }
    }
    
    // Sanitize profit margin
    if (result.profitMargin !== undefined) {
      const originalValue = result.profitMargin;
      result.profitMargin = Math.max(
        this.financialBoundaries.MIN_PROFIT_MARGIN,
        Math.min(this.financialBoundaries.MAX_PROFIT_MARGIN, result.profitMargin)
      );
      
      if (result.profitMargin !== originalValue) {
        this.metrics.boundaryViolations++;
      }
    }
    
    return result;
  }

  /**
   * Process data through the CSFE engine
   * @param {Object} data - Data to process
   * @returns {Object} - Processed data
   * @private
   */
  async _processThroughCSFE(data) {
    try {
      // Call CSFE instance to process data
      return await this.options.csfeInstance.processData(data);
    } catch (error) {
      this.emit('csfe-processing-error', { error: error.message, data });
      
      // Apply default processing on CSFE error
      return this._applyDefaultProcessing(data);
    }
  }

  /**
   * Apply default processing when no CSFE instance is available
   * @param {Object} data - Data to process
   * @returns {Object} - Processed data
   * @private
   */
  _applyDefaultProcessing(data) {
    const result = { ...data };
    
    // Add CSFE processing flag
    result.csfeProcessed = true;
    
    // Calculate financial risk score if interest rate and transaction volume are available
    if (result.interestRate !== undefined && result.transactionVolume !== undefined) {
      // Higher interest rates and transaction volumes increase risk
      const interestRiskFactor = result.interestRate * 5; // Scale to 0-5
      const volumeRiskFactor = Math.min(5, Math.log10(Math.max(1, result.transactionVolume)) / 2); // Scale to 0-5
      result.financialRiskScore = this._roundToPrecision(interestRiskFactor + volumeRiskFactor);
    }
    
    // Add timestamp
    result.processedAt = new Date().toISOString();
    
    return result;
  }

  /**
   * Post-process financial domain data
   * @param {Object} data - Data to post-process
   * @returns {Object} - Post-processed data
   * @private
   */
  _postprocessFinancialData(data) {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    const result = { ...data };
    
    // Add financial domain marker
    result._domain = 'financial';
    
    // Add processing metadata
    result._metadata = {
      processor: 'CSFEAdapter',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      precision: this.options.precisionDigits
    };
    
    // Ensure all financial values are properly rounded
    if (result.balance !== undefined) {
      result.balance = this._roundToPrecision(result.balance);
    }
    
    if (result.interestRate !== undefined) {
      result.interestRate = this._roundToPrecision(result.interestRate);
    }
    
    if (result.financialRiskScore !== undefined) {
      result.financialRiskScore = this._roundToPrecision(result.financialRiskScore);
    }
    
    return result;
  }

  /**
   * Sanitize financial domain data (used as fallback)
   * @param {Object} data - Data to sanitize
   * @returns {Object} - Sanitized data
   * @private
   */
  _sanitizeFinancialData(data) {
    if (!data || typeof data !== 'object') {
      return { _domain: 'financial', _sanitized: true };
    }
    
    const result = { ...data, _domain: 'financial', _sanitized: true };
    
    // Round balance to specified precision
    if (result.balance !== undefined) {
      result.balance = this._roundToPrecision(result.balance);
    }
    
    // Sanitize interest rate
    if (result.interestRate !== undefined) {
      result.interestRate = Math.max(
        this.financialBoundaries.MIN_INTEREST_RATE,
        Math.min(this.financialBoundaries.MAX_INTEREST_RATE, result.interestRate)
      );
    }
    
    // Sanitize transaction volume
    if (result.transactionVolume !== undefined) {
      result.transactionVolume = Math.max(
        this.financialBoundaries.MIN_TRANSACTION_VOLUME,
        Math.min(this.financialBoundaries.MAX_TRANSACTION_VOLUME, result.transactionVolume)
      );
    }
    
    // Sanitize profit margin
    if (result.profitMargin !== undefined) {
      result.profitMargin = Math.max(
        this.financialBoundaries.MIN_PROFIT_MARGIN,
        Math.min(this.financialBoundaries.MAX_PROFIT_MARGIN, result.profitMargin)
      );
    }
    
    return result;
  }

  /**
   * Round a value to the specified precision
   * @param {number} value - Value to round
   * @returns {number} - Rounded value
   * @private
   */
  _roundToPrecision(value) {
    if (typeof value !== 'number' || !Number.isFinite(value)) {
      return 0;
    }
    
    const factor = Math.pow(10, this.options.precisionDigits);
    return Math.round(value * factor) / factor;
  }

  /**
   * Update metrics based on processed data
   * @param {Object} data - Processed data
   * @private
   */
  _updateMetrics(data) {
    this.metrics.processedDataCount++;
    
    if (data.transactionVolume !== undefined) {
      this.metrics.totalTransactionVolume += data.transactionVolume;
    }
    
    if (data.interestRate !== undefined) {
      this.metrics.totalInterestRate += data.interestRate;
      this.metrics.averageInterestRate = this.metrics.totalInterestRate / this.metrics.processedDataCount;
    }
  }

  /**
   * Get adapter metrics
   * @returns {Object} - Adapter metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Reset adapter metrics
   */
  resetMetrics() {
    this.metrics = {
      processedDataCount: 0,
      boundaryViolations: 0,
      totalTransactionVolume: 0,
      averageInterestRate: 0,
      totalInterestRate: 0,
      roundingAdjustments: 0
    };
    
    this.emit('metrics-reset');
  }
}

/**
 * Create a CSFE adapter with recommended settings
 * @param {Object} options - Configuration options
 * @returns {CSFEAdapter} - Configured CSFE adapter
 */
function createCSFEAdapter(options = {}) {
  return new CSFEAdapter({
    enableLogging: true,
    strictMode: true,
    precisionDigits: 2,
    ...options
  });
}

module.exports = {
  CSFEAdapter,
  createCSFEAdapter
};
