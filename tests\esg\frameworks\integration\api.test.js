const request = require('supertest');
const express = require('express');
const router = require('../../../../apis/esg/frameworks/routes');
const models = require('../../../../apis/esg/frameworks/models');

// Mock the models
jest.mock('../../../../apis/esg/frameworks/models', () => ({
  esgFrameworks: [
    {
      id: 'esg-f-********',
      name: 'Global Reporting Initiative (GRI)',
      description: 'The GRI Standards create a common language for organizations to report on their sustainability impacts.',
      version: '2021',
      category: 'reporting',
      website: 'https://www.globalreporting.org',
      status: 'active',
      elements: [
        {
          id: 'gri-201',
          code: 'GRI 201',
          name: 'Economic Performance',
          description: 'Economic value generated and distributed',
          category: 'economic',
          metrics: ['revenue', 'operating-costs', 'employee-wages']
        },
        {
          id: 'gri-305',
          code: 'GRI 305',
          name: 'Emissions',
          description: 'Direct and indirect greenhouse gas emissions',
          category: 'environmental',
          metrics: ['scope-1-emissions', 'scope-2-emissions']
        }
      ],
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    },
    {
      id: 'esg-f-********',
      name: 'Sustainability Accounting Standards Board (SASB)',
      description: 'SASB Standards guide the disclosure of financially material sustainability information.',
      version: '2022',
      category: 'reporting',
      website: 'https://www.sasb.org',
      status: 'active',
      elements: [
        {
          id: 'sasb-ghg',
          code: 'GHG-1',
          name: 'Greenhouse Gas Emissions',
          description: 'Accounting metrics for greenhouse gas emissions',
          category: 'environmental',
          metrics: ['scope-1-emissions', 'scope-2-emissions', 'emissions-reduction']
        }
      ],
      createdAt: '2023-01-15T00:00:00Z',
      updatedAt: '2023-01-15T00:00:00Z'
    }
  ],
  frameworkMappings: [
    {
      id: 'mapping-12345',
      sourceFrameworkId: 'esg-f-********',
      sourceElementId: 'gri-305',
      targetFrameworkId: 'esg-f-********',
      targetElementId: 'sasb-ghg',
      mappingType: 'equivalent',
      description: 'GRI 305 Emissions maps to SASB GHG-1',
      confidence: 'high',
      createdAt: '2023-02-01T00:00:00Z',
      updatedAt: '2023-02-01T00:00:00Z'
    }
  ]
}));

// Create a test Express app
const app = express();
app.use(express.json());
app.use('/governance/esg/frameworks', router);

describe('ESG Frameworks API Integration Tests', () => {
  describe('GET /governance/esg/frameworks', () => {
    it('should return all frameworks with default pagination', async () => {
      const response = await request(app).get('/governance/esg/frameworks');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination).toEqual({
        total: 2,
        page: 1,
        limit: 10,
        pages: 1
      });
    });

    it('should filter frameworks by category', async () => {
      const response = await request(app).get('/governance/esg/frameworks?category=reporting');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data[0].category).toBe('reporting');
    });
  });

  describe('GET /governance/esg/frameworks/:id', () => {
    it('should return a specific framework by ID', async () => {
      const response = await request(app).get('/governance/esg/frameworks/esg-f-********');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('esg-f-********');
      expect(response.body.data.name).toBe('Global Reporting Initiative (GRI)');
    });

    it('should return 404 if framework not found', async () => {
      const response = await request(app).get('/governance/esg/frameworks/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /governance/esg/frameworks', () => {
    it('should create a new framework', async () => {
      const newFramework = {
        name: 'Task Force on Climate-related Financial Disclosures (TCFD)',
        description: 'Framework for climate-related financial disclosures',
        version: '2021',
        category: 'reporting',
        website: 'https://www.fsb-tcfd.org',
        status: 'active',
        elements: [
          {
            code: 'TCFD-1',
            name: 'Governance',
            description: 'Organization\'s governance around climate-related risks and opportunities',
            category: 'governance',
            metrics: ['board-oversight', 'management-role']
          }
        ]
      };

      const response = await request(app)
        .post('/governance/esg/frameworks')
        .send(newFramework);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'ESG framework created successfully');
      expect(response.body.data.name).toBe('Task Force on Climate-related Financial Disclosures (TCFD)');
      expect(response.body.data.category).toBe('reporting');
    });

    it('should return 400 for invalid input', async () => {
      const invalidFramework = {
        // Missing required fields
        description: 'Invalid framework'
      };

      const response = await request(app)
        .post('/governance/esg/frameworks')
        .send(invalidFramework);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /governance/esg/frameworks/:id', () => {
    it('should update an existing framework', async () => {
      const updatedFramework = {
        name: 'Updated Framework Name',
        status: 'inactive'
      };

      const response = await request(app)
        .put('/governance/esg/frameworks/esg-f-********')
        .send(updatedFramework);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'ESG framework updated successfully');
      expect(response.body.data.name).toBe('Updated Framework Name');
      expect(response.body.data.status).toBe('inactive');
    });

    it('should return 404 if framework not found', async () => {
      const response = await request(app)
        .put('/governance/esg/frameworks/non-existent-id')
        .send({ name: 'Updated Name' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('DELETE /governance/esg/frameworks/:id', () => {
    it('should delete an existing framework', async () => {
      const response = await request(app).delete('/governance/esg/frameworks/esg-f-********');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'ESG framework deleted successfully');
    });

    it('should return 404 if framework not found', async () => {
      const response = await request(app).delete('/governance/esg/frameworks/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /governance/esg/frameworks/:id/elements', () => {
    it('should return elements for a specific framework', async () => {
      const response = await request(app).get('/governance/esg/frameworks/esg-f-********/elements');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data[0].id).toBe('gri-201');
    });

    it('should return 404 if framework not found', async () => {
      const response = await request(app).get('/governance/esg/frameworks/non-existent-id/elements');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /governance/esg/frameworks/:id/elements/:elementId', () => {
    it('should return a specific element by ID', async () => {
      const response = await request(app).get('/governance/esg/frameworks/esg-f-********/elements/gri-201');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('gri-201');
      expect(response.body.data.name).toBe('Economic Performance');
    });

    it('should return 404 if element not found', async () => {
      const response = await request(app).get('/governance/esg/frameworks/esg-f-********/elements/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /governance/esg/frameworks/:id/elements', () => {
    it('should add a new element to a framework', async () => {
      const newElement = {
        code: 'GRI 403',
        name: 'Occupational Health and Safety',
        description: 'Management approach and metrics for occupational health and safety',
        category: 'social',
        metrics: ['injury-rate', 'lost-day-rate']
      };

      const response = await request(app)
        .post('/governance/esg/frameworks/esg-f-********/elements')
        .send(newElement);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Framework element added successfully');
      expect(response.body.data.code).toBe('GRI 403');
      expect(response.body.data.name).toBe('Occupational Health and Safety');
    });

    it('should return 400 for invalid input', async () => {
      const invalidElement = {
        // Missing required fields
        description: 'Invalid element'
      };

      const response = await request(app)
        .post('/governance/esg/frameworks/esg-f-********/elements')
        .send(invalidElement);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /governance/esg/frameworks/:id/elements/:elementId', () => {
    it('should update an existing element', async () => {
      const updatedElement = {
        name: 'Updated Element Name',
        description: 'Updated description'
      };

      const response = await request(app)
        .put('/governance/esg/frameworks/esg-f-********/elements/gri-201')
        .send(updatedElement);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Framework element updated successfully');
      expect(response.body.data.name).toBe('Updated Element Name');
      expect(response.body.data.description).toBe('Updated description');
    });

    it('should return 404 if element not found', async () => {
      const response = await request(app)
        .put('/governance/esg/frameworks/esg-f-********/elements/non-existent-id')
        .send({ name: 'Updated Name' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('DELETE /governance/esg/frameworks/:id/elements/:elementId', () => {
    it('should remove an element from a framework', async () => {
      const response = await request(app).delete('/governance/esg/frameworks/esg-f-********/elements/gri-201');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Framework element removed successfully');
    });

    it('should return 404 if element not found', async () => {
      const response = await request(app).delete('/governance/esg/frameworks/esg-f-********/elements/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /governance/esg/frameworks/:id/mappings', () => {
    it('should return mappings for a specific framework', async () => {
      const response = await request(app).get('/governance/esg/frameworks/esg-f-********/mappings');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].id).toBe('mapping-12345');
    });

    it('should return empty array if no mappings found', async () => {
      const response = await request(app).get('/governance/esg/frameworks/non-existent-id/mappings');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(0);
    });
  });

  describe('POST /governance/esg/frameworks/mappings', () => {
    it('should create a new mapping between frameworks', async () => {
      const newMapping = {
        sourceFrameworkId: 'esg-f-********',
        sourceElementId: 'gri-201',
        targetFrameworkId: 'esg-f-********',
        targetElementId: 'sasb-ghg',
        mappingType: 'related',
        description: 'Economic performance relates to GHG emissions',
        confidence: 'medium'
      };

      const response = await request(app)
        .post('/governance/esg/frameworks/mappings')
        .send(newMapping);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Framework mapping created successfully');
      expect(response.body.data.sourceFrameworkId).toBe('esg-f-********');
      expect(response.body.data.targetFrameworkId).toBe('esg-f-********');
      expect(response.body.data.mappingType).toBe('related');
    });

    it('should return 400 for invalid input', async () => {
      const invalidMapping = {
        // Missing required fields
        description: 'Invalid mapping'
      };

      const response = await request(app)
        .post('/governance/esg/frameworks/mappings')
        .send(invalidMapping);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /governance/esg/frameworks/mappings/:id', () => {
    it('should update an existing mapping', async () => {
      const updatedMapping = {
        mappingType: 'partial',
        confidence: 'low'
      };

      const response = await request(app)
        .put('/governance/esg/frameworks/mappings/mapping-12345')
        .send(updatedMapping);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Framework mapping updated successfully');
      expect(response.body.data.mappingType).toBe('partial');
      expect(response.body.data.confidence).toBe('low');
    });

    it('should return 404 if mapping not found', async () => {
      const response = await request(app)
        .put('/governance/esg/frameworks/mappings/non-existent-id')
        .send({ mappingType: 'partial' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('DELETE /governance/esg/frameworks/mappings/:id', () => {
    it('should delete an existing mapping', async () => {
      const response = await request(app).delete('/governance/esg/frameworks/mappings/mapping-12345');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Framework mapping deleted successfully');
    });

    it('should return 404 if mapping not found', async () => {
      const response = await request(app).delete('/governance/esg/frameworks/mappings/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });
});
