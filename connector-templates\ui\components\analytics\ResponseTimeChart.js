/**
 * Response Time Chart Component
 * 
 * This component displays a line chart of API response times over time.
 */

import React from 'react';
import { 
  ResponsiveContainer, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend 
} from 'recharts';

const ResponseTimeChart = ({ data, xAxisLabel = 'Date' }) => {
  // Format date for tooltip
  const formatDate = (value) => {
    if (!value) return '';
    
    // If it's a full ISO date, format it nicely
    if (value.includes('T')) {
      const date = new Date(value);
      return date.toLocaleDateString();
    }
    
    // Otherwise, return as is (for hour labels, etc.)
    return value;
  };
  
  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart
        data={data}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis 
          dataKey="date" 
          tickFormatter={formatDate}
          label={{ value: xAxisLabel, position: 'insideBottomRight', offset: -10 }}
        />
        <YAxis 
          label={{ value: 'Response Time (ms)', angle: -90, position: 'insideLeft' }}
        />
        <Tooltip 
          formatter={(value) => [`${Math.round(value)}ms`, 'Response Time']}
          labelFormatter={formatDate}
        />
        <Legend />
        <Line 
          type="monotone" 
          dataKey="averageResponseTime" 
          name="Avg. Response Time" 
          stroke="#82ca9d" 
          activeDot={{ r: 8 }} 
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

export default ResponseTimeChart;
