import React from 'react';

export default function OktaPartnership() {
  return (
    <div>
      {/* Hero Section */}
      <div className="bg-secondary p-8 rounded-lg mb-8 relative overflow-hidden">
        <div className="relative z-10">
          <h2 className="text-3xl font-bold mb-4">Exclusive Founding Partner Opportunity</h2>
          <p className="text-xl mb-6 max-w-3xl">
            Join NovaFuse API Superstore as a founding partner and connect your identity platform with the enterprise GRC market.
          </p>
          <div className="flex flex-wrap gap-4">
            <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg">
              <div className="text-3xl font-bold text-blue-400">85%</div>
              <div className="text-gray-300">Revenue Share</div>
            </div>
            <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg">
              <div className="text-3xl font-bold text-blue-400">$750K+</div>
              <div className="text-gray-300">Annual Revenue Potential</div>
            </div>
            <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg">
              <div className="text-3xl font-bold text-blue-400">1,000+</div>
              <div className="text-gray-300">Enterprise GRC Customers</div>
            </div>
            <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg">
              <div className="text-3xl font-bold text-blue-400">2 Weeks</div>
              <div className="text-gray-300">Integration Timeline</div>
            </div>
          </div>
        </div>
        <div className="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-l from-blue-800 to-transparent opacity-10"></div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        {/* Left Column */}
        <div>
          <div className="bg-secondary p-6 rounded-lg mb-8">
            <h3 className="text-2xl font-bold mb-4">Why Okta + NovaFuse?</h3>
            
            <div className="space-y-4">
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-blue-800 flex items-center justify-center text-white font-bold mr-3">1</div>
                <div>
                  <h4 className="text-lg font-semibold">New Revenue Stream</h4>
                  <p className="text-gray-300">Generate new revenue from enterprise GRC customers with our 85% revenue share model—the highest in the industry.</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-blue-800 flex items-center justify-center text-white font-bold mr-3">2</div>
                <div>
                  <h4 className="text-lg font-semibold">Deeper Enterprise Relationships</h4>
                  <p className="text-gray-300">Extend your identity platform into GRC workflows, creating stickier customer relationships in regulated industries.</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-blue-800 flex items-center justify-center text-white font-bold mr-3">3</div>
                <div>
                  <h4 className="text-lg font-semibold">Minimal Integration Effort</h4>
                  <p className="text-gray-300">Our open SDK and OIDC-based integration requires minimal development resources—we'll even help build it.</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-blue-800 flex items-center justify-center text-white font-bold mr-3">4</div>
                <div>
                  <h4 className="text-lg font-semibold">Featured Partner Status</h4>
                  <p className="text-gray-300">As a founding partner, you'll receive premium placement in our marketplace and co-marketing opportunities.</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-secondary p-6 rounded-lg">
            <h3 className="text-2xl font-bold mb-4">Integration Use Cases</h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="text-lg font-semibold text-blue-800">Identity-Driven Compliance</h4>
                <p className="text-gray-300">Automatically map Okta users and groups to compliance roles and responsibilities, ensuring proper segregation of duties.</p>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold text-blue-800">Access Certification Automation</h4>
                <p className="text-gray-300">Streamline access reviews by connecting Okta identity data with compliance requirements for SOX, HIPAA, and other regulations.</p>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold text-blue-800">Risk-Based Authentication</h4>
                <p className="text-gray-300">Adjust authentication requirements based on risk scores and compliance status from NovaFuse.</p>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold text-blue-800">Compliance-Aware Provisioning</h4>
                <p className="text-gray-300">Ensure new user provisioning adheres to compliance requirements and automatically documents approvals.</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Right Column */}
        <div>
          <div className="bg-secondary p-6 rounded-lg mb-8">
            <h3 className="text-2xl font-bold mb-4">Technical Integration</h3>
            
            <p className="text-gray-300 mb-4">
              Integration with NovaFuse leverages Okta's OIDC capabilities for seamless identity management. Here's a simple example:
            </p>
            
            <div className="bg-gray-900 p-4 rounded-lg text-green-400 mb-6 overflow-x-auto">
              <pre>{`// 1. Configure Okta as an Identity Provider for NovaFuse
const oktaConfig = {
  clientId: "YOUR_CLIENT_ID",
  issuer: "https://your-org.okta.com",
  redirectUri: "https://api.novafuse.io/callback",
  scopes: ["openid", "profile", "email", "groups"]
};

// 2. Map Okta groups to NovaFuse roles
const roleMapping = {
  "okta.admin": "novafuse.admin",
  "okta.compliance": "novafuse.compliance_manager",
  "okta.auditor": "novafuse.auditor",
  "okta.risk": "novafuse.risk_manager"
};

// 3. Implement user synchronization
async function syncOktaUsers() {
  const oktaUsers = await oktaClient.listUsers();
  
  for (const user of oktaUsers) {
    await novafuseClient.upsertUser({
      externalId: user.id,
      email: user.profile.email,
      firstName: user.profile.firstName,
      lastName: user.profile.lastName,
      groups: user.groups.map(g => roleMapping[g.name] || g.name)
    });
  }
}`}</pre>
            </div>
            
            <p className="text-gray-300">
              Our SDK provides pre-built components for Okta integration, making it easy to implement SSO, user provisioning, and access control with minimal code.
            </p>
          </div>
          
          <div className="bg-secondary p-6 rounded-lg">
            <h3 className="text-2xl font-bold mb-4">Revenue Projection</h3>
            
            <div className="overflow-x-auto">
              <table className="w-full text-left">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="py-2">Year</th>
                    <th className="py-2">Customers</th>
                    <th className="py-2">API Calls/Month</th>
                    <th className="py-2">Annual Revenue</th>
                    <th className="py-2">Okta Share (85%)</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-700">
                    <td className="py-2">Year 1</td>
                    <td className="py-2">25</td>
                    <td className="py-2">250,000</td>
                    <td className="py-2">$150,000</td>
                    <td className="py-2">$127,500</td>
                  </tr>
                  <tr className="border-b border-gray-700">
                    <td className="py-2">Year 2</td>
                    <td className="py-2">100</td>
                    <td className="py-2">1,000,000</td>
                    <td className="py-2">$600,000</td>
                    <td className="py-2">$510,000</td>
                  </tr>
                  <tr>
                    <td className="py-2">Year 3</td>
                    <td className="py-2">250</td>
                    <td className="py-2">2,500,000</td>
                    <td className="py-2">$1,500,000</td>
                    <td className="py-2">$1,275,000</td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <p className="text-gray-300 mt-4">
              Revenue is based on API call volume with a rate of $0.005 per call. Actual results may vary based on customer adoption and usage patterns.
            </p>
          </div>
        </div>
      </div>
      
      {/* Enterprise Security Section */}
      <div className="bg-secondary p-6 rounded-lg mb-8">
        <h3 className="text-2xl font-bold mb-4 text-center">Enterprise Security & Compliance</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 border border-blue-600 rounded-lg">
            <h4 className="text-lg font-semibold mb-2">Identity-First Security</h4>
            <p className="text-gray-300">NovaFuse's integration with Okta ensures that identity is the foundation of all GRC processes, aligning with Zero Trust principles.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <h4 className="text-lg font-semibold mb-2">Compliance Automation</h4>
            <p className="text-gray-300">Automate compliance tasks like access reviews, segregation of duties, and user certification through the Okta integration.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <h4 className="text-lg font-semibold mb-2">Audit-Ready Reporting</h4>
            <p className="text-gray-300">Generate audit-ready reports that demonstrate compliance with identity and access management requirements.</p>
          </div>
        </div>
      </div>
      
      {/* Bottom Section: Next Steps */}
      <div className="bg-secondary p-6 rounded-lg">
        <h3 className="text-2xl font-bold mb-4 text-center">Next Steps</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="p-4 border border-blue-600 rounded-lg">
            <div className="text-3xl font-bold text-center mb-2">1</div>
            <h4 className="text-lg font-semibold text-center mb-2">Initial Discussion</h4>
            <p className="text-gray-300 text-center">30-minute call to discuss partnership details and answer questions.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <div className="text-3xl font-bold text-center mb-2">2</div>
            <h4 className="text-lg font-semibold text-center mb-2">Technical Review</h4>
            <p className="text-gray-300 text-center">Deep dive into the API and integration requirements with your technical team.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <div className="text-3xl font-bold text-center mb-2">3</div>
            <h4 className="text-lg font-semibold text-center mb-2">Partnership Agreement</h4>
            <p className="text-gray-300 text-center">Sign partnership agreement with revenue sharing terms and go-to-market plan.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <div className="text-3xl font-bold text-center mb-2">4</div>
            <h4 className="text-lg font-semibold text-center mb-2">Launch</h4>
            <p className="text-gray-300 text-center">Joint announcement and marketing campaign to promote the integration.</p>
          </div>
        </div>
        
        <div className="mt-8 text-center">
          <button className="bg-blue-800 text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-opacity-90 transition-colors">
            Schedule Partnership Discussion
          </button>
        </div>
      </div>
    </div>
  );
}
