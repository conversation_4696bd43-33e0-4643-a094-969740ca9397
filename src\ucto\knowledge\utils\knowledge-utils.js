/**
 * Knowledge Utilities for the Universal Compliance Tracking Optimizer.
 *
 * This module provides utility functions for the UCTO Compliance Knowledge Base.
 */

/**
 * Format a date for display.
 * @param {string} dateString - ISO date string
 * @param {string} format - Date format (short, medium, long)
 * @returns {string} Formatted date string
 */
function formatDate(dateString, format = 'medium') {
  if (!dateString) {
    return '';
  }
  
  const date = new Date(dateString);
  
  switch (format) {
    case 'short':
      return date.toLocaleDateString();
    case 'long':
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    case 'medium':
    default:
      return date.toLocaleDateString();
  }
}

/**
 * Generate a slug from a string.
 * @param {string} text - Text to generate slug from
 * @returns {string} Slug
 */
function generateSlug(text) {
  if (!text) {
    return '';
  }
  
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

/**
 * Generate a citation for a resource.
 * @param {Object} resource - Resource data
 * @returns {string} Citation
 */
function generateCitation(resource) {
  if (!resource) {
    return '';
  }
  
  const parts = [];
  
  if (resource.author) {
    parts.push(resource.author);
  }
  
  if (resource.name) {
    parts.push(`"${resource.name}"`);
  }
  
  if (resource.created_at) {
    const date = new Date(resource.created_at);
    parts.push(`(${date.getFullYear()})`);
  }
  
  if (resource.url) {
    parts.push(`Retrieved from ${resource.url}`);
  }
  
  return parts.join('. ');
}

/**
 * Extract keywords from text.
 * @param {string} text - Text to extract keywords from
 * @returns {Array} Keywords
 */
function extractKeywords(text) {
  if (!text) {
    return [];
  }
  
  // Convert to lowercase
  const lowerText = text.toLowerCase();
  
  // Remove punctuation
  const noPunctuation = lowerText.replace(/[^\w\s]/g, ' ');
  
  // Split into tokens
  const tokens = noPunctuation.split(/\s+/).filter(token => token.length > 0);
  
  // Remove stop words
  const stopWords = [
    'a', 'an', 'the', 'and', 'or', 'but', 'if', 'then', 'else', 'when', 'to', 'of', 'for', 'with', 'by', 'at', 'from',
    'in', 'on', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will',
    'would', 'shall', 'should', 'may', 'might', 'must', 'can', 'could'
  ];
  
  const filteredTokens = tokens.filter(token => !stopWords.includes(token));
  
  // Count token frequency
  const tokenCounts = {};
  
  for (const token of filteredTokens) {
    if (!tokenCounts[token]) {
      tokenCounts[token] = 0;
    }
    
    tokenCounts[token]++;
  }
  
  // Sort tokens by frequency
  const sortedTokens = Object.entries(tokenCounts)
    .sort((a, b) => b[1] - a[1])
    .map(entry => entry[0]);
  
  // Return top 10 tokens
  return sortedTokens.slice(0, 10);
}

/**
 * Calculate similarity between two texts.
 * @param {string} text1 - First text
 * @param {string} text2 - Second text
 * @returns {number} Similarity score (0-1)
 */
function calculateTextSimilarity(text1, text2) {
  if (!text1 || !text2) {
    return 0;
  }
  
  // Convert to lowercase
  const lowerText1 = text1.toLowerCase();
  const lowerText2 = text2.toLowerCase();
  
  // Remove punctuation
  const noPunctuation1 = lowerText1.replace(/[^\w\s]/g, ' ');
  const noPunctuation2 = lowerText2.replace(/[^\w\s]/g, ' ');
  
  // Split into tokens
  const tokens1 = noPunctuation1.split(/\s+/).filter(token => token.length > 0);
  const tokens2 = noPunctuation2.split(/\s+/).filter(token => token.length > 0);
  
  // Remove stop words
  const stopWords = [
    'a', 'an', 'the', 'and', 'or', 'but', 'if', 'then', 'else', 'when', 'to', 'of', 'for', 'with', 'by', 'at', 'from',
    'in', 'on', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will',
    'would', 'shall', 'should', 'may', 'might', 'must', 'can', 'could'
  ];
  
  const filteredTokens1 = tokens1.filter(token => !stopWords.includes(token));
  const filteredTokens2 = tokens2.filter(token => !stopWords.includes(token));
  
  // Calculate Jaccard similarity
  const set1 = new Set(filteredTokens1);
  const set2 = new Set(filteredTokens2);
  
  const intersection = new Set([...set1].filter(token => set2.has(token)));
  const union = new Set([...set1, ...set2]);
  
  return intersection.size / union.size;
}

/**
 * Calculate control mapping strength.
 * @param {Object} control1 - First control
 * @param {Object} control2 - Second control
 * @returns {string} Mapping strength (exact, strong, moderate, weak)
 */
function calculateControlMappingStrength(control1, control2) {
  if (!control1 || !control2) {
    return 'weak';
  }
  
  // Calculate similarity between control descriptions
  const descriptionSimilarity = calculateTextSimilarity(control1.description, control2.description);
  
  // Calculate similarity between control objectives
  const objectiveSimilarity = calculateTextSimilarity(control1.objective, control2.objective);
  
  // Calculate overall similarity
  const overallSimilarity = (descriptionSimilarity + objectiveSimilarity) / 2;
  
  // Determine mapping strength
  if (overallSimilarity >= 0.8) {
    return 'exact';
  } else if (overallSimilarity >= 0.6) {
    return 'strong';
  } else if (overallSimilarity >= 0.4) {
    return 'moderate';
  } else {
    return 'weak';
  }
}

/**
 * Generate a summary of a control.
 * @param {Object} control - Control data
 * @returns {string} Summary
 */
function generateControlSummary(control) {
  if (!control) {
    return '';
  }
  
  const parts = [];
  
  parts.push(`Control: ${control.name}`);
  
  if (control.control_number) {
    parts.push(`ID: ${control.control_number}`);
  }
  
  if (control.objective) {
    parts.push(`Objective: ${control.objective}`);
  }
  
  return parts.join('\n');
}

/**
 * Generate a summary of a framework.
 * @param {Object} framework - Framework data
 * @returns {string} Summary
 */
function generateFrameworkSummary(framework) {
  if (!framework) {
    return '';
  }
  
  const parts = [];
  
  parts.push(`Framework: ${framework.name}`);
  
  if (framework.version) {
    parts.push(`Version: ${framework.version}`);
  }
  
  if (framework.issuer) {
    parts.push(`Issuer: ${framework.issuer}`);
  }
  
  if (framework.description) {
    parts.push(`Description: ${framework.description}`);
  }
  
  return parts.join('\n');
}

module.exports = {
  formatDate,
  generateSlug,
  generateCitation,
  extractKeywords,
  calculateTextSimilarity,
  calculateControlMappingStrength,
  generateControlSummary,
  generateFrameworkSummary
};
