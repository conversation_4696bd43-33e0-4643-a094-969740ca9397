# Request Validation

NovaConnect provides comprehensive request validation to ensure data integrity and security. This document describes the request validation system and how to use it.

## Overview

The request validation system provides the following features:

- **Schema-Based Validation**: All API endpoints are validated against predefined schemas
- **Consistent Error Handling**: Validation errors are returned in a consistent format
- **Detailed Error Messages**: Validation errors include detailed information about what went wrong
- **Support for Multiple Validation Types**: Validation for request body, query parameters, and URL parameters
- **Common Validation Schemas**: Reusable validation schemas for common data types

## Validation Middleware

The validation middleware is the core of the request validation system. It validates incoming requests against predefined schemas and returns detailed error messages if validation fails.

### Usage

```javascript
const { validate } = require('../middleware/validationMiddleware');
const { createUserSchema } = require('../validation');

router.post('/users', validate(createUserSchema), (req, res, next) => {
  // Request is validated, proceed with handling
});
```

### Validation Schema Format

Validation schemas are defined using Joi and follow this format:

```javascript
const Joi = require('joi');

const createUserSchema = {
  body: Joi.object({
    name: Joi.string().required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(8).required()
  }),
  query: Joi.object({
    // Query parameters validation
  }),
  params: Joi.object({
    // URL parameters validation
  })
};
```

## Common Validation Schemas

NovaConnect provides common validation schemas for frequently used data types:

```javascript
const { commonSchemas } = require('../validation');

// Use common schemas in your validation
const schema = {
  body: Joi.object({
    id: commonSchemas.id,
    email: commonSchemas.email,
    password: commonSchemas.password
  })
};
```

### Available Common Schemas

- `id`: MongoDB ObjectId validation
- `uuid`: UUID validation
- `email`: Email validation
- `password`: Password validation (minimum 8 characters)
- `date`: ISO date validation
- `page`: Pagination page number
- `limit`: Pagination limit
- `sortBy`: Sort field
- `sortOrder`: Sort order (asc, desc)
- `search`: Search query
- `boolean`: Boolean value
- `array`: Array value
- `object`: Object value
- `number`: Number value
- `string`: String value
- `enum`: Enumeration of allowed values
- `pagination`: Pagination parameters
- `dateRange`: Date range parameters
- `search`: Search parameters
- `filter`: Filter parameters
- `filters`: Multiple filter parameters
- `sort`: Sort parameters
- `sorts`: Multiple sort parameters
- `export`: Export parameters
- `idArray`: Array of IDs
- `uuidArray`: Array of UUIDs
- `emailArray`: Array of emails
- `connectorType`: Connector type enumeration
- `connectorStatus`: Connector status enumeration
- `reportType`: Report type enumeration
- `reportFormat`: Report format enumeration
- `scheduleType`: Schedule type enumeration
- `role`: User role enumeration
- `permission`: Permission string
- `permissionArray`: Array of permissions
- `team`: Team object
- `user`: User object
- `credential`: Credential object
- `webhook`: Webhook object
- `apiKey`: API key object
- `environment`: Environment object
- `tenant`: Tenant object

## Validation Error Handling

When validation fails, the validation middleware returns a detailed error response:

```json
{
  "error": {
    "type": "Validation Error",
    "message": "Validation failed",
    "code": "VALIDATION_ERROR",
    "status": 400,
    "correlationId": "550e8400-e29b-41d4-a716-************",
    "details": {
      "body": [
        {
          "path": "name",
          "message": "Name is required",
          "type": "any.required"
        },
        {
          "path": "email",
          "message": "Email must be a valid email address",
          "type": "string.email"
        }
      ]
    }
  }
}
```

## Advanced Validation

### Array Validation

You can validate arrays of items using the `validateArray` middleware:

```javascript
const { validateArray } = require('../middleware/validationMiddleware');

const itemSchema = Joi.object({
  name: Joi.string().required(),
  value: Joi.number().required()
});

router.post('/items', validateArray(itemSchema, 'items'), (req, res, next) => {
  // Each item in req.body.items is validated
});
```

### Conditional Validation

You can apply different validation schemas based on request properties:

```javascript
const { validateConditional } = require('../middleware/validationMiddleware');

const schemaSelector = (req) => {
  if (req.query.type === 'user') {
    return userSchema;
  } else if (req.query.type === 'team') {
    return teamSchema;
  }
  return null;
};

router.get('/resources', validateConditional(schemaSelector), (req, res, next) => {
  // Request is validated against the selected schema
});
```

### ID Validation

You can validate ID parameters:

```javascript
const { validateId } = require('../middleware/validationMiddleware');

router.get('/users/:userId', validateId('userId'), (req, res, next) => {
  // userId is validated
});
```

### Pagination Validation

You can validate pagination parameters:

```javascript
const { validatePagination } = require('../middleware/validationMiddleware');

router.get('/users', validatePagination({
  maxLimit: 100,
  defaultLimit: 10,
  sortFields: ['name', 'email', 'createdAt'],
  defaultSortField: 'createdAt'
}), (req, res, next) => {
  // Pagination parameters are validated and available in req.pagination
});
```

### Date Range Validation

You can validate date range parameters:

```javascript
const { validateDateRange } = require('../middleware/validationMiddleware');

router.get('/logs', validateDateRange({
  required: true
}), (req, res, next) => {
  // Date range parameters are validated and available in req.dateRange
});
```

## Validation Best Practices

1. **Validate All Input**: Always validate all input from external sources
2. **Use Common Schemas**: Use common schemas for consistent validation
3. **Be Specific**: Define validation rules as specifically as possible
4. **Provide Clear Error Messages**: Customize error messages to be clear and helpful
5. **Sanitize Input**: Use `stripUnknown: true` to remove unknown properties
6. **Validate at the Edge**: Validate input as early as possible in the request lifecycle
7. **Test Validation**: Write tests for validation rules
8. **Document Validation**: Document validation requirements in API documentation
9. **Use Consistent Patterns**: Use consistent validation patterns across the API
10. **Keep Schemas DRY**: Reuse validation schemas where possible
