"""
ComphyonΨᶜ Feedback System - Feedback mechanisms for the ComphyonΨᶜ Governor.
"""

import time
import numpy as np
from collections import deque

class FeedbackSystem:
    """
    Feedback system for the ComphyonΨᶜ Governor.
    
    Implements closed-loop control for stable operation.
    """
    
    def __init__(self, history_length=100):
        """
        Initialize the FeedbackSystem.
        
        Args:
            history_length: Length of the metrics history to maintain
        """
        self.metrics_history = deque(maxlen=history_length)
        self.control_history = deque(maxlen=history_length)
        self.pid_controllers = {
            'acceleration': PIDController(kp=0.5, ki=0.1, kd=0.2),
            'velocity': PIDController(kp=0.3, ki=0.05, kd=0.1)
        }
    
    def add_metrics(self, metrics):
        """
        Add metrics to the history.
        
        Args:
            metrics: Metrics dictionary from ComphyonMeter
        """
        self.metrics_history.append(metrics)
    
    def add_control_action(self, control_action):
        """
        Add control action to the history.
        
        Args:
            control_action: Control action dictionary
        """
        self.control_history.append(control_action)
    
    def calculate_feedback(self, metrics, target_values):
        """
        Calculate feedback adjustments based on metrics and target values.
        
        Args:
            metrics: Current metrics dictionary from ComphyonMeter
            target_values: Dictionary of target values for different metrics
            
        Returns:
            dict: Feedback adjustments
        """
        feedback = {}
        
        # Calculate PID control for acceleration
        if 'acceleration' in metrics and 'acceleration' in target_values:
            feedback['acceleration'] = self.pid_controllers['acceleration'].update(
                metrics['acceleration'], target_values['acceleration'])
        
        # Calculate PID control for velocity
        if 'velocity' in metrics and 'velocity' in target_values:
            feedback['velocity'] = self.pid_controllers['velocity'].update(
                metrics['velocity'], target_values['velocity'])
        
        return feedback
    
    def analyze_effectiveness(self):
        """
        Analyze the effectiveness of control actions.
        
        Returns:
            dict: Analysis results
        """
        if len(self.metrics_history) < 10 or len(self.control_history) < 5:
            return {'status': 'insufficient_data'}
        
        # Extract metrics and control actions
        recent_metrics = list(self.metrics_history)[-10:]
        recent_controls = list(self.control_history)[-5:]
        
        # Calculate metrics trends
        acceleration_values = [m.get('acceleration', 0) for m in recent_metrics]
        velocity_values = [m.get('velocity', 0) for m in recent_metrics]
        
        acceleration_trend = np.polyfit(range(len(acceleration_values)), acceleration_values, 1)[0]
        velocity_trend = np.polyfit(range(len(velocity_values)), velocity_values, 1)[0]
        
        # Determine if controls are effective
        controls_effective = (
            (acceleration_trend < 0 or abs(acceleration_trend) < 0.01) and
            (velocity_trend < 0 or abs(velocity_trend) < 0.1)
        )
        
        return {
            'status': 'effective' if controls_effective else 'ineffective',
            'acceleration_trend': acceleration_trend,
            'velocity_trend': velocity_trend,
            'recommendation': 'maintain' if controls_effective else 'increase_control'
        }
    
    def reset(self):
        """Reset the feedback system."""
        self.metrics_history.clear()
        self.control_history.clear()
        for controller in self.pid_controllers.values():
            controller.reset()


class PIDController:
    """
    PID controller for closed-loop control.
    """
    
    def __init__(self, kp=1.0, ki=0.0, kd=0.0):
        """
        Initialize the PID controller.
        
        Args:
            kp: Proportional gain
            ki: Integral gain
            kd: Derivative gain
        """
        self.kp = kp
        self.ki = ki
        self.kd = kd
        
        self.last_error = 0
        self.integral = 0
        self.last_time = None
    
    def update(self, current_value, target_value):
        """
        Update the PID controller.
        
        Args:
            current_value: Current value of the controlled variable
            target_value: Target value of the controlled variable
            
        Returns:
            float: Control output
        """
        # Calculate error
        error = target_value - current_value
        
        # Get current time
        current_time = time.time()
        
        # Initialize last_time if not set
        if self.last_time is None:
            self.last_time = current_time
        
        # Calculate time delta
        dt = current_time - self.last_time
        
        # Avoid division by zero
        if dt == 0:
            return 0
        
        # Calculate integral term
        self.integral += error * dt
        
        # Calculate derivative term
        derivative = (error - self.last_error) / dt
        
        # Calculate control output
        output = (self.kp * error) + (self.ki * self.integral) + (self.kd * derivative)
        
        # Update state
        self.last_error = error
        self.last_time = current_time
        
        return output
    
    def reset(self):
        """Reset the PID controller."""
        self.last_error = 0
        self.integral = 0
        self.last_time = None
