# Contributing to NovaFuse

Thank you for your interest in contributing to NovaFuse! This document provides guidelines and instructions for contributing to this project.

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. Please read it before contributing.

## How to Contribute

### Reporting Bugs

If you find a bug, please report it by creating an issue in the GitHub repository. When filing an issue, please include:

- A clear and descriptive title
- A detailed description of the issue
- Steps to reproduce the bug
- Expected behavior
- Actual behavior
- Screenshots or code snippets (if applicable)
- Environment information (OS, browser, version, etc.)

### Suggesting Enhancements

If you have an idea for an enhancement, please create an issue in the GitHub repository. When suggesting an enhancement, please include:

- A clear and descriptive title
- A detailed description of the enhancement
- The motivation for the enhancement
- Any potential implementation details
- Any potential drawbacks or trade-offs

### Pull Requests

We welcome pull requests! To submit a pull request:

1. Fork the repository
2. Create a new branch for your changes
3. Make your changes
4. Write or update tests for your changes
5. Ensure all tests pass
6. Submit a pull request

When submitting a pull request, please:

- Reference any related issues
- Include a clear and descriptive title
- Provide a detailed description of the changes
- Update documentation as needed
- Follow the coding style and conventions of the project

## Development Setup

### Prerequisites

- Python 3.8 or higher
- pip
- virtualenv (recommended)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/novafuse/[repository-name].git
   cd [repository-name]
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows, use `venv\Scripts\activate`
   ```

3. Install dependencies:
   ```bash
   pip install -e .
   pip install -r requirements-dev.txt
   ```

### Running Tests

To run the tests:

```bash
pytest
```

To run the tests with coverage:

```bash
pytest --cov=src
```

## Coding Style

We follow the PEP 8 style guide for Python code. Please ensure your code adheres to this style guide.

We use the following tools to enforce coding style:

- flake8: For linting
- black: For code formatting
- isort: For import sorting

You can run these tools with:

```bash
flake8 src tests
black src tests
isort src tests
```

## Documentation

Please ensure that your code is well-documented. This includes:

- Docstrings for all public modules, classes, and functions
- Comments for complex or non-obvious code
- Updated README.md or other documentation as needed

## License

By contributing to this project, you agree that your contributions will be licensed under the project's license.

## Questions

If you have any questions about contributing, please create an issue in the GitHub repository or contact the project maintainers.

Thank you for your contributions!