/**
 * Test Runner Script
 *
 * This script provides a simple way to run NovaDNA tests.
 * Usage: node run.js [test-name]
 */

// Get command line arguments
const args = process.argv.slice(2);
const testName = args[0];

/**
 * Run NovaVision integration tests
 */
async function runNovaVisionTests() {
  console.log('=== Running NovaVision Integration Tests ===');

  try {
    // Import NovaVisionComponents and NovaVisionIntegration
    const NovaVisionComponents = require('../ui/NovaVisionComponents');
    const NovaVisionIntegration = require('../ui/integration/NovaVisionIntegration');
    const { runUIComponentTests } = require('./integration/UIComponentsTest');

    // Create test instances
    const novaVisionComponents = new NovaVisionComponents({
      baseUrl: '/novadna',
      theme: 'emergency'
    });

    const novaVisionIntegration = new NovaVisionIntegration({
      apiBaseUrl: '/api',
      novaVisionComponents
    });

    // Test emergency access UI
    console.log('\nTesting Emergency Access UI...');
    const emergencyAccessSchema = novaVisionIntegration.getEmergencyAccessUI();
    console.log('- Schema generated successfully');
    console.log(`- Title: ${emergencyAccessSchema.title}`);
    console.log(`- Sections: ${emergencyAccessSchema.sections.length}`);

    // Test emergency override UI
    console.log('\nTesting Emergency Override UI...');
    const emergencyOverrideSchema = novaVisionIntegration.getEmergencyOverrideUI();
    console.log('- Schema generated successfully');
    console.log(`- Title: ${emergencyOverrideSchema.title}`);
    console.log(`- Sections: ${emergencyOverrideSchema.sections.length}`);

    // Test profile view UI
    console.log('\nTesting Profile View UI...');
    const basicProfileSchema = novaVisionIntegration.getProfileViewUI('basic');
    console.log('- Basic schema generated successfully');
    console.log(`- Sections: ${basicProfileSchema.sections.length}`);

    const standardProfileSchema = novaVisionIntegration.getProfileViewUI('standard');
    console.log('- Standard schema generated successfully');
    console.log(`- Sections: ${standardProfileSchema.sections.length}`);

    const fullProfileSchema = novaVisionIntegration.getProfileViewUI('full');
    console.log('- Full schema generated successfully');
    console.log(`- Sections: ${fullProfileSchema.sections.length}`);

    // Test security dashboard UI
    console.log('\nTesting Security Dashboard UI...');
    const securityDashboardSchema = novaVisionIntegration.getSecurityDashboardUI();
    console.log('- Schema generated successfully');
    console.log(`- Title: ${securityDashboardSchema.title}`);
    console.log(`- Sections: ${securityDashboardSchema.sections.length}`);

    // Run UI component tests
    console.log('\nRunning UI component tests...');
    const uiComponentTestsResult = await runUIComponentTests();

    if (!uiComponentTestsResult) {
      throw new Error('UI component tests failed');
    }

    console.log('\n✅ NovaVision integration tests passed');
    return true;
  } catch (error) {
    console.error('\n❌ NovaVision integration tests failed:', error);
    return false;
  }
}

/**
 * Run healthcare integration tests
 */
async function runHealthcareTests() {
  console.log('=== Running Healthcare Integration Tests ===');

  try {
    // Import required modules
    const NovaConnectAdapter = require('../integration/NovaConnectAdapter');
    const HealthcareIntegration = require('../integration/healthcare/HealthcareIntegration');
    const DataSourcePrioritization = require('../integration/healthcare/DataSourcePrioritization');
    const SecureTemporaryCache = require('../integration/healthcare/SecureTemporaryCache');
    const { runIntegrationComponentTests } = require('./integration/IntegrationComponentsTest');

    // Initialize NovaConnectAdapter with mock configuration
    const novaConnectAdapter = new NovaConnectAdapter({
      apiUrl: 'http://localhost:3000/api/novaconnect',
      apiKey: 'test-api-key',
      apiSecret: 'test-api-secret'
    });

    // Mock the client to prevent actual API calls
    novaConnectAdapter.client = {
      get: async () => ({ data: [] }),
      post: async () => ({ data: { success: true } })
    };

    // Initialize HealthcareIntegration
    const healthcareIntegration = new HealthcareIntegration({
      novaConnectAdapter,
      cacheEnabled: true,
      encryptionEnabled: true
    });

    // Test emergency session management
    console.log('\nTesting Emergency Session Management...');
    const session = healthcareIntegration.startEmergencySession({
      emergencyType: 'CARDIAC',
      emergencySeverity: 'HIGH',
      responderType: 'PARAMEDIC',
      locationType: 'AMBULANCE'
    });

    console.log('- Session created successfully');
    console.log(`- Session ID: ${session.sessionId}`);
    console.log(`- Emergency Type: ${session.context.emergencyType}`);
    console.log(`- Expires At: ${session.expiresAt}`);

    // Test data source prioritization
    console.log('\nTesting Data Source Prioritization...');
    const dataSourcePrioritization = new DataSourcePrioritization();

    const dataSources = [
      {
        id: 'epic-1',
        type: 'EHR',
        provider: 'Epic',
        availableDataTypes: ['demographics', 'allergies', 'medications', 'conditions'],
        lastUpdated: new Date().toISOString()
      },
      {
        id: 'cerner-1',
        type: 'EHR',
        provider: 'Cerner',
        availableDataTypes: ['demographics', 'allergies', 'medications'],
        lastUpdated: new Date(Date.now() - 86400000).toISOString() // 1 day ago
      }
    ];

    const priorities = dataSourcePrioritization.prioritizeDataSources(dataSources, {
      emergencyType: 'CARDIAC',
      emergencySeverity: 'HIGH'
    });

    console.log('- Data sources prioritized successfully');
    console.log(`- Highest priority: ${priorities[0].id}`);

    // Test secure temporary cache
    console.log('\nTesting Secure Temporary Cache...');
    const secureTemporaryCache = new SecureTemporaryCache({
      enabled: true,
      encryptionEnabled: true
    });

    secureTemporaryCache.store('test-key', { name: 'Test Data', value: 123 });
    const retrievedData = secureTemporaryCache.retrieve('test-key');

    console.log('- Data stored and retrieved successfully');
    console.log(`- Retrieved data: ${JSON.stringify(retrievedData)}`);

    const stats = secureTemporaryCache.getStats();
    console.log('- Cache statistics retrieved successfully');
    console.log(`- Enabled: ${stats.enabled}`);
    console.log(`- Encryption Enabled: ${stats.encryptionEnabled}`);
    console.log(`- Total Entries: ${stats.totalEntries}`);

    // End emergency session
    const endResult = healthcareIntegration.endEmergencySession(session.sessionId);
    console.log('\n- Session ended successfully:', endResult);

    // Run integration component tests
    console.log('\nRunning integration component tests...');
    const integrationComponentTestsResult = runIntegrationComponentTests();

    if (!integrationComponentTestsResult) {
      throw new Error('Integration component tests failed');
    }

    console.log('\n✅ Healthcare integration tests passed');
    return true;
  } catch (error) {
    console.error('\n❌ Healthcare integration tests failed:', error);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('=== NovaDNA Test Runner ===');
  console.log('Running all tests...\n');

  let allTestsPassed = true;

  // Run NovaVision integration tests
  const novaVisionPassed = await runNovaVisionTests();
  if (!novaVisionPassed) {
    allTestsPassed = false;
  }

  console.log('');

  // Run healthcare integration tests
  const healthcarePassed = await runHealthcareTests();
  if (!healthcarePassed) {
    allTestsPassed = false;
  }

  console.log('');

  // Summary
  if (allTestsPassed) {
    console.log('=== Test Summary ===');
    console.log('✅ All tests passed successfully!');
  } else {
    console.log('=== Test Summary ===');
    console.error('❌ Some tests failed. See above for details.');
    process.exit(1);
  }
}

// Run specific test or all tests
async function main() {
  try {
    if (!testName) {
      // Run all tests
      await runAllTests();
    } else {
      // Run specific test
      let testResult;

      if (testName.toLowerCase() === 'novavision') {
        testResult = await runNovaVisionTests();
        if (!testResult) {
          process.exit(1);
        }
      } else if (testName.toLowerCase() === 'healthcare') {
        testResult = await runHealthcareTests();
        if (!testResult) {
          process.exit(1);
        }
      } else {
        console.error(`Unknown test: ${testName}`);
        console.log('Available tests:');
        console.log('- novavision: NovaVision integration tests');
        console.log('- healthcare: Healthcare integration tests');
        console.log('- (no argument): Run all tests');
        process.exit(1);
      }
    }
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run the main function
main();
