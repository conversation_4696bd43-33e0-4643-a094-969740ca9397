"""
Demo script for the Universal Vendor Risk Management System (UVRMS).

This script demonstrates how to use the UVRMS to manage vendor information,
assessments, risks, and monitoring.
"""

import os
import sys
import json
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the UVRMS
from uvrms import VendorManager, AssessmentManager, RiskManager, MonitoringManager
from uvrms.utils.vendor_utils import calculate_vendor_risk_score

def alert_handler(alert: Dict[str, Any]) -> None:
    """
    Custom handler for monitoring alerts.
    
    Args:
        alert: The alert data
    """
    logger.info(f"Alert received: {alert.get('id')}")
    logger.info(f"Alert type: {alert.get('result', {}).get('type')}")
    logger.info(f"Alert severity: {alert.get('result', {}).get('severity')}")
    
    # In a real implementation, this would send notifications, create tickets, etc.
    logger.info("Alert handled")

def main():
    """Run the UVRMS demo."""
    logger.info("Starting UVRMS demo")
    
    # Create output directory if it doesn't exist
    output_dir = os.path.join(os.path.dirname(__file__), 'uvrms_output')
    os.makedirs(output_dir, exist_ok=True)
    
    # Initialize the managers
    vendor_manager = VendorManager()
    assessment_manager = AssessmentManager()
    risk_manager = RiskManager()
    monitoring_manager = MonitoringManager()
    
    # Register an alert handler
    monitoring_manager.register_alert_handler('security', alert_handler)
    
    # Create a vendor
    logger.info("Creating a vendor")
    
    vendor = vendor_manager.create_vendor({
        'name': 'Acme Corporation',
        'description': 'Provider of cloud services',
        'contact_info': {
            'email': '<EMAIL>',
            'phone': '************',
            'address': '123 Main St, Anytown, USA'
        },
        'services': ['cloud_storage', 'data_processing', 'analytics'],
        'categories': ['technology', 'data_services']
    })
    
    logger.info(f"Vendor created: {vendor.get('id')}")
    
    # Save the vendor to a file
    vendor_path = os.path.join(output_dir, f"{vendor.get('id')}.json")
    
    with open(vendor_path, 'w', encoding='utf-8') as f:
        json.dump(vendor, f, indent=2)
    
    logger.info(f"Saved vendor to {vendor_path}")
    
    # Create an assessment for the vendor
    logger.info("Creating an assessment for the vendor")
    
    assessment = assessment_manager.create_assessment(
        vendor['id'],
        'security_assessment',
        {
            'name': 'Security Assessment for Acme Corporation',
            'description': 'Assessment of Acme Corporation security controls'
        }
    )
    
    logger.info(f"Assessment created: {assessment.get('id')}")
    
    # Save the assessment to a file
    assessment_path = os.path.join(output_dir, f"{assessment.get('id')}.json")
    
    with open(assessment_path, 'w', encoding='utf-8') as f:
        json.dump(assessment, f, indent=2)
    
    logger.info(f"Saved assessment to {assessment_path}")
    
    # Answer some questions in the assessment
    logger.info("Answering questions in the assessment")
    
    # Find the security policies section
    security_policies_section = None
    for section in assessment['sections']:
        if section['id'] == 'security_policies':
            security_policies_section = section
            break
    
    if security_policies_section:
        # Answer the first question
        question_id = security_policies_section['questions'][0]['id']
        assessment = assessment_manager.answer_question(
            assessment['id'],
            'security_policies',
            question_id,
            'yes',
            'The vendor has a comprehensive information security policy'
        )
        
        logger.info(f"Answered question: {question_id}")
    
    # Update the assessment status
    logger.info("Updating assessment status")
    
    assessment = assessment_manager.update_assessment(
        assessment['id'],
        {
            'status': 'in_progress'
        }
    )
    
    logger.info(f"Assessment status updated: {assessment.get('status')}")
    
    # Create a risk for the vendor
    logger.info("Creating a risk for the vendor")
    
    risk = risk_manager.create_risk({
        'vendor_id': vendor['id'],
        'category_id': 'security',
        'name': 'Data breach risk',
        'description': 'Risk of data breach due to inadequate security controls',
        'impact': 'high',
        'likelihood': 'medium',
        'status': 'identified'
    })
    
    logger.info(f"Risk created: {risk.get('id')}")
    
    # Save the risk to a file
    risk_path = os.path.join(output_dir, f"{risk.get('id')}.json")
    
    with open(risk_path, 'w', encoding='utf-8') as f:
        json.dump(risk, f, indent=2)
    
    logger.info(f"Saved risk to {risk_path}")
    
    # Add a control to the risk
    logger.info("Adding a control to the risk")
    
    risk = risk_manager.add_control(
        risk['id'],
        {
            'name': 'Data encryption',
            'description': 'Implement data encryption for all sensitive data',
            'type': 'preventive',
            'status': 'planned',
            'effectiveness': 'high'
        }
    )
    
    logger.info(f"Control added to risk: {risk.get('id')}")
    
    # Create a monitoring configuration for the vendor
    logger.info("Creating a monitoring configuration for the vendor")
    
    monitoring_config = monitoring_manager.create_monitoring_config({
        'vendor_id': vendor['id'],
        'name': 'Security monitoring for Acme Corporation',
        'description': 'Continuous monitoring of security indicators',
        'handler_id': 'security_monitoring',
        'parameters': {
            'check_ssl': True,
            'check_vulnerabilities': True
        },
        'frequency': 'daily',
        'alert_threshold': 'medium'
    })
    
    logger.info(f"Monitoring configuration created: {monitoring_config.get('id')}")
    
    # Save the monitoring configuration to a file
    monitoring_config_path = os.path.join(output_dir, f"{monitoring_config.get('id')}.json")
    
    with open(monitoring_config_path, 'w', encoding='utf-8') as f:
        json.dump(monitoring_config, f, indent=2)
    
    logger.info(f"Saved monitoring configuration to {monitoring_config_path}")
    
    # Add a monitoring result
    logger.info("Adding a monitoring result")
    
    monitoring_result = {
        'type': 'security',
        'severity': 'medium',
        'findings': [
            {
                'id': 'SEC-001',
                'description': 'Vendor security certificate is expiring soon',
                'details': 'The SSL certificate will expire in 30 days'
            }
        ]
    }
    
    monitoring_manager.add_monitoring_result(monitoring_config['id'], monitoring_result)
    
    logger.info("Monitoring result added")
    
    # Get monitoring results
    logger.info("Getting monitoring results")
    
    results = monitoring_manager.get_monitoring_results(monitoring_config['id'])
    
    logger.info(f"Found {len(results)} monitoring results")
    
    # Save the monitoring results to a file
    results_path = os.path.join(output_dir, f"{monitoring_config.get('id')}_results.json")
    
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Saved monitoring results to {results_path}")
    
    # Calculate vendor risk score
    logger.info("Calculating vendor risk score")
    
    # Get all assessments for the vendor
    vendor_assessments = assessment_manager.get_vendor_assessments(vendor['id'])
    
    # Get all risks for the vendor
    vendor_risks = risk_manager.get_vendor_risks(vendor['id'])
    
    # Calculate the risk score
    risk_score = calculate_vendor_risk_score(vendor, vendor_assessments, vendor_risks)
    
    logger.info(f"Vendor risk score: {risk_score.get('overall_score')}")
    logger.info(f"Vendor risk level: {risk_score.get('risk_level')}")
    
    # Save the risk score to a file
    risk_score_path = os.path.join(output_dir, f"{vendor.get('id')}_risk_score.json")
    
    with open(risk_score_path, 'w', encoding='utf-8') as f:
        json.dump(risk_score, f, indent=2)
    
    logger.info(f"Saved risk score to {risk_score_path}")
    
    logger.info("UVRMS demo completed successfully")

if __name__ == "__main__":
    main()
