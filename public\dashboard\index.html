<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Quality Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .dashboard-header {
            background-color: #0d6efd;
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .metric-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        .metric-card:hover {
            transform: translateY(-5px);
        }
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
        }
        .metric-title {
            font-size: 1.2rem;
            color: #6c757d;
        }
        .chart-container {
            height: 300px;
            margin-bottom: 30px;
        }
        .compliance-badge {
            font-size: 0.9rem;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        .status-indicator {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-green {
            background-color: #28a745;
        }
        .status-yellow {
            background-color: #ffc107;
        }
        .status-red {
            background-color: #dc3545;
        }
        .framework-card {
            height: 100%;
        }
        .last-updated {
            font-size: 0.8rem;
            color: #6c757d;
            text-align: right;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1>NovaFuse Quality Dashboard</h1>
                    <p class="mb-0"><strong>Trust, Automated.</strong> Real-time metrics proving it.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <span class="badge bg-light text-dark">Last Updated: <span id="last-updated-time">June 15, 2023 09:45 AM</span></span>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Trust Automation Score -->
        <div class="row mb-4">
            <div class="col-12 mb-4">
                <div class="card metric-card bg-white">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">Trust Automation Score™</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <div class="metric-value" style="font-size: 4rem; color: #0066cc;">97.3%</div>
                                <div class="text-success">
                                    <small><i class="bi bi-arrow-up"></i> +2.1% this quarter</small>
                                </div>
                                <p class="mt-2">Trust that used to take <span class="text-danger">teams of people</span> now happens <span class="text-success">automatically</span>.</p>
                            </div>
                            <div class="col-md-8">
                                <div class="progress mb-3" style="height: 25px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 98%" aria-valuenow="98" aria-valuemin="0" aria-valuemax="100">Automated Evidence Collection: 98%</div>
                                </div>
                                <div class="progress mb-3" style="height: 25px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 99%" aria-valuenow="99" aria-valuemin="0" aria-valuemax="100">Cross-Framework Mapping: 99%</div>
                                </div>
                                <div class="progress mb-3" style="height: 25px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 96%" aria-valuenow="96" aria-valuemin="0" aria-valuemax="100">Regulatory Change Detection: 96%</div>
                                </div>
                                <div class="progress" style="height: 25px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 97%" aria-valuenow="97" aria-valuemin="0" aria-valuemax="100">Continuous Compliance: 97%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card bg-white">
                    <div class="card-body text-center">
                        <h5 class="metric-title">Test Coverage</h5>
                        <div class="metric-value text-primary">95.2%</div>
                        <div class="text-success">
                            <small><i class="bi bi-arrow-up"></i> +2.3% this month</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card bg-white">
                    <div class="card-body text-center">
                        <h5 class="metric-title">Test Pass Rate</h5>
                        <div class="metric-value text-success">99.8%</div>
                        <div class="text-success">
                            <small><i class="bi bi-arrow-up"></i> +0.3% this month</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card bg-white">
                    <div class="card-body text-center">
                        <h5 class="metric-title">Security Score</h5>
                        <div class="metric-value text-success">A+</div>
                        <div class="text-success">
                            <small><i class="bi bi-shield-check"></i> OWASP Compliant</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card bg-white">
                    <div class="card-body text-center">
                        <h5 class="metric-title">Mutation Score</h5>
                        <div class="metric-value text-primary">92.7%</div>
                        <div class="text-success">
                            <small><i class="bi bi-arrow-up"></i> +5.1% this month</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Coverage by Component -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card metric-card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">Test Coverage by Component</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="coverageChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card metric-card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">Test Execution Trends</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="executionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Compliance Framework Coverage -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card metric-card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">Compliance Framework Coverage</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="card framework-card">
                                    <div class="card-body">
                                        <h5 class="card-title">GDPR <span class="status-indicator status-green"></span></h5>
                                        <div class="progress mb-3">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: 98%" aria-valuenow="98" aria-valuemin="0" aria-valuemax="100">98%</div>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary compliance-badge">Data Protection</span>
                                            <span class="badge bg-primary compliance-badge">Consent</span>
                                            <span class="badge bg-primary compliance-badge">Rights</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card framework-card">
                                    <div class="card-body">
                                        <h5 class="card-title">HIPAA <span class="status-indicator status-green"></span></h5>
                                        <div class="progress mb-3">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: 97%" aria-valuenow="97" aria-valuemin="0" aria-valuemax="100">97%</div>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary compliance-badge">Privacy</span>
                                            <span class="badge bg-primary compliance-badge">Security</span>
                                            <span class="badge bg-primary compliance-badge">Breach</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card framework-card">
                                    <div class="card-body">
                                        <h5 class="card-title">PCI DSS <span class="status-indicator status-green"></span></h5>
                                        <div class="progress mb-3">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: 99%" aria-valuenow="99" aria-valuemin="0" aria-valuemax="100">99%</div>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary compliance-badge">Network</span>
                                            <span class="badge bg-primary compliance-badge">Data</span>
                                            <span class="badge bg-primary compliance-badge">Access</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card framework-card">
                                    <div class="card-body">
                                        <h5 class="card-title">SOC 2 <span class="status-indicator status-green"></span></h5>
                                        <div class="progress mb-3">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: 96%" aria-valuenow="96" aria-valuemin="0" aria-valuemax="100">96%</div>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary compliance-badge">Security</span>
                                            <span class="badge bg-primary compliance-badge">Availability</span>
                                            <span class="badge bg-primary compliance-badge">Processing</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Testing Results -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card metric-card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">OWASP Top 10 Coverage</h5>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Vulnerability</th>
                                    <th>Status</th>
                                    <th>Tests</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>A01:2021 - Broken Access Control</td>
                                    <td><span class="status-indicator status-green"></span> Protected</td>
                                    <td>42</td>
                                </tr>
                                <tr>
                                    <td>A02:2021 - Cryptographic Failures</td>
                                    <td><span class="status-indicator status-green"></span> Protected</td>
                                    <td>38</td>
                                </tr>
                                <tr>
                                    <td>A03:2021 - Injection</td>
                                    <td><span class="status-indicator status-green"></span> Protected</td>
                                    <td>56</td>
                                </tr>
                                <tr>
                                    <td>A04:2021 - Insecure Design</td>
                                    <td><span class="status-indicator status-green"></span> Protected</td>
                                    <td>31</td>
                                </tr>
                                <tr>
                                    <td>A05:2021 - Security Misconfiguration</td>
                                    <td><span class="status-indicator status-green"></span> Protected</td>
                                    <td>27</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card metric-card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">Recent Security Tests</h5>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Test Type</th>
                                    <th>Result</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Jun 15, 2023</td>
                                    <td>Penetration Test</td>
                                    <td><span class="badge bg-success">Passed</span></td>
                                </tr>
                                <tr>
                                    <td>Jun 14, 2023</td>
                                    <td>SAST Scan</td>
                                    <td><span class="badge bg-success">Passed</span></td>
                                </tr>
                                <tr>
                                    <td>Jun 13, 2023</td>
                                    <td>DAST Scan</td>
                                    <td><span class="badge bg-success">Passed</span></td>
                                </tr>
                                <tr>
                                    <td>Jun 12, 2023</td>
                                    <td>Dependency Scan</td>
                                    <td><span class="badge bg-success">Passed</span></td>
                                </tr>
                                <tr>
                                    <td>Jun 11, 2023</td>
                                    <td>API Security Scan</td>
                                    <td><span class="badge bg-success">Passed</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cross-Framework Mapping -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card metric-card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">Cross-Framework Mapping Coverage</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="mappingChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="last-updated">
            Data refreshed automatically every 15 minutes. Last refresh: <span id="current-time"></span>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script>
        // Update current time
        document.getElementById('current-time').textContent = new Date().toLocaleString();

        // Coverage Chart
        const coverageCtx = document.getElementById('coverageChart').getContext('2d');
        const coverageChart = new Chart(coverageCtx, {
            type: 'bar',
            data: {
                labels: ['UI Components', 'API Endpoints', 'Authentication', 'Data Processing', 'Compliance Logic', 'Cross-Framework Mapping'],
                datasets: [{
                    label: 'Statement Coverage',
                    data: [96.2, 98.5, 99.1, 94.8, 97.3, 95.9],
                    backgroundColor: 'rgba(13, 110, 253, 0.7)',
                    borderColor: 'rgba(13, 110, 253, 1)',
                    borderWidth: 1
                }, {
                    label: 'Branch Coverage',
                    data: [94.1, 97.2, 98.5, 93.6, 96.8, 94.7],
                    backgroundColor: 'rgba(25, 135, 84, 0.7)',
                    borderColor: 'rgba(25, 135, 84, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 80,
                        max: 100
                    }
                }
            }
        });

        // Execution Chart
        const executionCtx = document.getElementById('executionChart').getContext('2d');
        const executionChart = new Chart(executionCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Test Pass Rate',
                    data: [98.2, 98.5, 98.9, 99.1, 99.5, 99.8],
                    backgroundColor: 'rgba(25, 135, 84, 0.2)',
                    borderColor: 'rgba(25, 135, 84, 1)',
                    borderWidth: 2,
                    tension: 0.3
                }, {
                    label: 'Test Coverage',
                    data: [89.5, 91.2, 92.8, 93.6, 94.5, 95.2],
                    backgroundColor: 'rgba(13, 110, 253, 0.2)',
                    borderColor: 'rgba(13, 110, 253, 1)',
                    borderWidth: 2,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 85,
                        max: 100
                    }
                }
            }
        });

        // Mapping Chart
        const mappingCtx = document.getElementById('mappingChart').getContext('2d');
        const mappingChart = new Chart(mappingCtx, {
            type: 'radar',
            data: {
                labels: ['GDPR → HIPAA', 'GDPR → PCI DSS', 'GDPR → SOC 2', 'HIPAA → PCI DSS', 'HIPAA → SOC 2', 'PCI DSS → SOC 2'],
                datasets: [{
                    label: 'Mapping Coverage',
                    data: [96, 94, 97, 95, 98, 93],
                    backgroundColor: 'rgba(13, 110, 253, 0.2)',
                    borderColor: 'rgba(13, 110, 253, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(13, 110, 253, 1)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: false,
                        min: 80,
                        max: 100
                    }
                }
            }
        });
    </script>
</body>
</html>
