{"name": "novaconnect-deployment", "version": "1.0.0", "description": "NovaConnect Deployment Package", "main": "index.js", "scripts": {"start": "node ../server.js", "test": "jest"}, "dependencies": {"express": "^4.17.1", "cors": "^2.8.5", "helmet": "^4.6.0", "morgan": "^1.10.0", "winston": "^3.3.3", "dotenv": "^10.0.0", "mongoose": "^6.0.12", "express-rate-limit": "^5.5.0", "@google-cloud/bigquery": "^5.9.1", "@google-cloud/security-center": "^4.1.0", "@google-cloud/kms": "^2.7.0"}, "devDependencies": {"jest": "^27.3.1", "supertest": "^6.1.6"}, "engines": {"node": ">=14.0.0"}, "private": true, "license": "MIT"}