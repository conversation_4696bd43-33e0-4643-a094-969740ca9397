/**
 * CHAEONIX AGGRESSION CONTROL PANEL
 * Real-time display of 9-engine consensus and Comphyological aggression control
 * Shows how the system dynamically adjusts trading aggression
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BoltIcon, 
  FireIcon, 
  ShieldCheckIcon,
  BeakerIcon,
  CpuChipIcon,
  ChartBarIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';

const AGGRESSION_COLORS = {
  DORMANT: 'text-gray-400 bg-gray-800/30',
  CONSERVATIVE: 'text-blue-400 bg-blue-900/30',
  MODERATE: 'text-yellow-400 bg-yellow-900/30',
  AGGRESSIVE: 'text-orange-400 bg-orange-900/30',
  TRANSCENDENT: 'text-purple-400 bg-purple-900/30'
};

const AGGRESSION_ICONS = {
  DORMANT: ShieldCheckIcon,
  CONSERVATIVE: BeakerIcon,
  MODERATE: AdjustmentsHorizontalIcon,
  AGGRESSIVE: FireIcon,
  TRANSCENDENT: BoltIcon
};

export default function AggressionControlPanel() {
  const [aggressionData, setAggressionData] = useState({
    level: 'MODERATE',
    aggression_score: 0.65,
    trading_params: {
      position_size_multiplier: 1.0,
      trading_frequency_multiplier: 1.0,
      max_risk_per_trade: 0.02,
      max_concurrent_positions: 3
    },
    engine_status: {
      engine_confidences: {
        NEFC: { weighted_confidence: 0.85 },
        NERS: { weighted_confidence: 0.82 },
        NEPI: { weighted_confidence: 0.88 },
        NERE: { weighted_confidence: 0.78 },
        NECE: { weighted_confidence: 0.75 },
        NECO: { weighted_confidence: 0.70 },
        NEBE: { weighted_confidence: 0.73 },
        NEEE: { weighted_confidence: 0.68 },
        NEPE: { weighted_confidence: 0.76 }
      },
      comphyological_factors: {
        csm_score: 0.75,
        compphyon_score: 0.68,
        market_coherence: 0.82,
        phi_alignment: 1.234
      }
    }
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Initial load
    updateAggressionData();
    
    // Update every 30 seconds
    const interval = setInterval(() => {
      updateAggressionData();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const updateAggressionData = async () => {
    try {
      const response = await fetch('/api/trading/aggression-engine');
      const data = await response.json();

      if (data.success && data.current_analysis) {
        // Ensure the data structure is complete
        const analysisData = {
          level: data.current_analysis.level || 'MODERATE',
          aggression_score: data.current_analysis.aggression_score || 0.65,
          trading_params: {
            position_size_multiplier: data.current_analysis.trading_params?.position_size_multiplier || 1.0,
            trading_frequency_multiplier: data.current_analysis.trading_params?.trading_frequency_multiplier || 1.0,
            max_risk_per_trade: data.current_analysis.trading_params?.max_risk_per_trade || 0.02,
            max_concurrent_positions: data.current_analysis.trading_params?.max_concurrent_positions || 3
          },
          engine_status: {
            engine_confidences: data.current_analysis.engine_status?.engine_confidences || aggressionData.engine_status.engine_confidences,
            comphyological_factors: data.current_analysis.engine_status?.comphyological_factors || aggressionData.engine_status.comphyological_factors
          }
        };

        setAggressionData(analysisData);
        setIsLoading(false);
      } else {
        console.warn('Invalid aggression engine response:', data);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Aggression engine error:', error);
      setIsLoading(false);
    }
  };

  const AggressionIcon = AGGRESSION_ICONS[aggressionData.level] || AdjustmentsHorizontalIcon;
  const aggressionColor = AGGRESSION_COLORS[aggressionData.level] || AGGRESSION_COLORS.MODERATE;

  const getEngineConfidenceColor = (confidence) => {
    if (confidence >= 0.9) return 'text-purple-400';
    if (confidence >= 0.8) return 'text-green-400';
    if (confidence >= 0.7) return 'text-yellow-400';
    return 'text-red-400';
  };

  if (isLoading) {
    return (
      <div className="p-6 rounded-lg border border-gray-600 bg-gray-800/50">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-600 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-600 rounded w-3/4"></div>
            <div className="h-4 bg-gray-600 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6 rounded-lg border border-orange-500/30 bg-gradient-to-br from-orange-900/20 to-red-900/20 backdrop-blur-sm"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <CpuChipIcon className="w-6 h-6 text-orange-400" />
          <div>
            <h3 className="text-xl font-bold text-white">
              Aggression Control Engine
            </h3>
            <p className="text-sm text-gray-400">
              9-Engine Consensus + Comphyological Analysis
            </p>
          </div>
        </div>
        
        <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${aggressionColor}`}>
          <AggressionIcon className="w-5 h-5" />
          <span className="font-bold">
            {aggressionData.level}
          </span>
        </div>
      </div>

      {/* Aggression Score */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-400">Aggression Score</span>
          <span className="text-lg font-bold text-white">
            {(aggressionData.aggression_score * 100).toFixed(1)}%
          </span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-3">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${aggressionData.aggression_score * 100}%` }}
            transition={{ duration: 1, ease: "easeOut" }}
            className={`h-3 rounded-full ${
              aggressionData.aggression_score >= 0.9 ? 'bg-purple-500' :
              aggressionData.aggression_score >= 0.75 ? 'bg-orange-500' :
              aggressionData.aggression_score >= 0.6 ? 'bg-yellow-500' :
              aggressionData.aggression_score >= 0.4 ? 'bg-blue-500' : 'bg-gray-500'
            }`}
          />
        </div>
      </div>

      {/* Trading Parameters */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="p-3 rounded-lg bg-gray-800/50 border border-gray-600">
          <div className="text-sm text-gray-400 mb-1">Position Size</div>
          <div className="text-lg font-bold text-white">
            {aggressionData.trading_params.position_size_multiplier.toFixed(1)}x
          </div>
        </div>
        
        <div className="p-3 rounded-lg bg-gray-800/50 border border-gray-600">
          <div className="text-sm text-gray-400 mb-1">Risk Per Trade</div>
          <div className="text-lg font-bold text-white">
            {(aggressionData.trading_params.max_risk_per_trade * 100).toFixed(1)}%
          </div>
        </div>
        
        <div className="p-3 rounded-lg bg-gray-800/50 border border-gray-600">
          <div className="text-sm text-gray-400 mb-1">Frequency</div>
          <div className="text-lg font-bold text-white">
            {aggressionData.trading_params.trading_frequency_multiplier.toFixed(1)}x
          </div>
        </div>
        
        <div className="p-3 rounded-lg bg-gray-800/50 border border-gray-600">
          <div className="text-sm text-gray-400 mb-1">Max Positions</div>
          <div className="text-lg font-bold text-white">
            {aggressionData.trading_params.max_concurrent_positions}
          </div>
        </div>
      </div>

      {/* Engine Confidences */}
      <div className="mb-6">
        <h4 className="text-lg font-semibold text-white mb-3">🔱 9-Engine Confidence Matrix</h4>
        <div className="grid grid-cols-3 gap-2 text-sm">
          {aggressionData?.engine_status?.engine_confidences ?
            Object.entries(aggressionData.engine_status.engine_confidences).map(([engine, data]) => (
              <div key={engine} className="flex items-center justify-between p-2 rounded bg-gray-800/30">
                <span className="text-gray-300 font-medium">{engine}</span>
                <span className={`font-bold ${getEngineConfidenceColor(data?.weighted_confidence || 0)}`}>
                  {((data?.weighted_confidence || 0) * 100).toFixed(0)}%
                </span>
              </div>
            )) : (
              <div className="col-span-3 text-center text-gray-400 py-4">
                Loading engine data...
              </div>
            )
          }
        </div>
      </div>

      {/* Comphyological Factors */}
      <div>
        <h4 className="text-lg font-semibold text-white mb-3">🔬 Comphyological Analysis</h4>
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-400">CSM Score:</span>
            <span className="text-blue-400 font-medium">
              {((aggressionData?.engine_status?.comphyological_factors?.csm_score || 0) * 100).toFixed(1)}%
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">COMPPHYON Score:</span>
            <span className="text-purple-400 font-medium">
              {((aggressionData?.engine_status?.comphyological_factors?.compphyon_score || 0) * 100).toFixed(1)}%
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Market Coherence:</span>
            <span className="text-green-400 font-medium">
              {((aggressionData?.engine_status?.comphyological_factors?.market_coherence || 0) * 100).toFixed(1)}%
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">φ-Alignment:</span>
            <span className="text-yellow-400 font-medium">
              {(aggressionData?.engine_status?.comphyological_factors?.phi_alignment || 0).toFixed(3)}
            </span>
          </div>
        </div>
      </div>

      {/* Status Indicator */}
      <div className="mt-6 p-3 rounded-lg bg-gradient-to-r from-orange-900/30 to-red-900/30 border border-orange-500/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <ChartBarIcon className="w-4 h-4 text-orange-400" />
            <span className="text-sm text-orange-300">Dynamic Aggression Control</span>
          </div>
          <span className="text-orange-400 font-mono text-sm">
            ACTIVE
          </span>
        </div>
      </div>
    </motion.div>
  );
}
