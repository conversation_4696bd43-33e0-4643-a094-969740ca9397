/**
 * Simple Integration Tests for NovaTrack API
 */

const { describe, it, beforeAll, afterAll, expect } = require('@jest/globals');
const request = require('supertest');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import test environment configuration
const testEnv = require('../../setup/test-environment');

// Import the test server
const { createTestServer } = require('./server');

// Import test data generator
const { generateRequirement } = require('../../data/novatrack-test-data');

describe('NovaTrack API - Simple Tests', () => {
  let app;
  let server;
  let tempDir;
  let trackingManager;

  beforeAll(async () => {
    // Create a temporary directory for test data
    if (testEnv.isDocker) {
      // In Docker, use a fixed path
      tempDir = '/app/tmp/novatrack-api-test';
      // Ensure the directory exists
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
    } else {
      // Locally, use a temporary directory
      tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'novatrack-api-test-'));
    }

    // Create the test server
    const testServer = createTestServer({ tempDir });
    app = testServer.app;
    trackingManager = testServer.trackingManager;

    // Start the server
    const port = testEnv.isDocker ? 3002 : 3001;
    server = app.listen(port);

    console.log(`Running API tests in ${testEnv.isDocker ? 'Docker' : 'local'} environment`);
    console.log(`Using test directory: ${tempDir}`);
    console.log(`Server started on port ${port}`);
  });

  afterAll(async () => {
    // Close the server
    if (server) {
      server.close();
    }

    // Clean up the temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });

  describe('GET /health', () => {
    it('should return a 200 status code', async () => {
      // Act
      const response = await request(app).get('/health');

      // Assert
      expect(response.status).toBe(200);
      expect(response.body.status).toBe('ok');
      expect(response.body.timestamp).toBeDefined();
    });
  });

  describe('POST /api/v1/novatrack/requirements', () => {
    it('should create a new requirement', async () => {
      // Arrange
      const requirementData = generateRequirement();

      // Act
      const response = await request(app)
        .post('/api/v1/novatrack/requirements')
        .send(requirementData);

      // Assert
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBeDefined();
      expect(response.body.data.name).toBe(requirementData.name);
      expect(response.body.data.framework).toBe(requirementData.framework);
    });

    it('should return a 400 error for invalid requirement data', async () => {
      // Arrange
      const invalidRequirementData = {
        // Missing required name field
        description: 'Implement processes for handling data subject rights requests',
        framework: 'GDPR',
        category: 'privacy',
        priority: 'high',
        status: 'in_progress',
        due_date: '2023-12-31',
        assigned_to: 'privacy_officer',
        tags: ['gdpr', 'data_subject_rights', 'privacy']
      };

      // Act
      const response = await request(app)
        .post('/api/v1/novatrack/requirements')
        .send(invalidRequirementData);

      // Assert
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });
});
