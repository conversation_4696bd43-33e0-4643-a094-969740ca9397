/**
 * NovaFuse Universal API Connector - Dashboard Service
 * 
 * This module provides a service for generating monitoring dashboards.
 */

const os = require('os');
const { createLogger } = require('../utils/logger');
const metricsService = require('./metrics-service');
const healthService = require('./health-service');

const logger = createLogger('dashboard-service');

/**
 * Dashboard Service class for generating monitoring dashboards
 */
class DashboardService {
  constructor() {
    // Initialize dashboard data
    this.dashboardData = {
      system: {},
      metrics: {},
      health: {},
      errors: {},
      requests: {}
    };
    
    // Initialize update interval
    this.updateInterval = null;
    
    logger.info('Dashboard service initialized');
  }

  /**
   * Start the dashboard service
   * 
   * @param {number} updateInterval - The update interval in milliseconds
   */
  start(updateInterval = 60000) {
    // Stop any existing interval
    this.stop();
    
    // Update dashboard data immediately
    this.updateDashboardData();
    
    // Set up update interval
    this.updateInterval = setInterval(() => {
      this.updateDashboardData();
    }, updateInterval);
    
    logger.info(`Dashboard service started with update interval of ${updateInterval}ms`);
  }

  /**
   * Stop the dashboard service
   */
  stop() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
      
      logger.info('Dashboard service stopped');
    }
  }

  /**
   * Update dashboard data
   */
  async updateDashboardData() {
    try {
      // Update system data
      this.updateSystemData();
      
      // Update metrics data
      this.updateMetricsData();
      
      // Update health data
      await this.updateHealthData();
      
      logger.debug('Dashboard data updated');
    } catch (error) {
      logger.error('Error updating dashboard data:', { error });
    }
  }

  /**
   * Update system data
   */
  updateSystemData() {
    this.dashboardData.system = {
      hostname: os.hostname(),
      platform: os.platform(),
      arch: os.arch(),
      release: os.release(),
      uptime: os.uptime(),
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem()
      },
      cpu: {
        count: os.cpus().length,
        model: os.cpus()[0].model,
        load: os.loadavg()
      },
      network: {
        interfaces: Object.entries(os.networkInterfaces()).reduce((acc, [name, interfaces]) => {
          acc[name] = interfaces.map(iface => ({
            address: iface.address,
            netmask: iface.netmask,
            family: iface.family,
            mac: iface.mac,
            internal: iface.internal
          }));
          return acc;
        }, {})
      },
      process: {
        pid: process.pid,
        version: process.version,
        memoryUsage: process.memoryUsage(),
        uptime: process.uptime()
      }
    };
  }

  /**
   * Update metrics data
   */
  updateMetricsData() {
    // Get metrics from metrics service
    const metrics = metricsService.metrics;
    
    // Process counters
    const counters = {};
    for (const [name, countersMap] of metrics.counters.entries()) {
      counters[name] = Array.from(countersMap.entries()).map(([labelKey, value]) => ({
        labels: this._parseLabelKey(labelKey),
        value
      }));
    }
    
    // Process gauges
    const gauges = {};
    for (const [name, gaugesMap] of metrics.gauges.entries()) {
      gauges[name] = Array.from(gaugesMap.entries()).map(([labelKey, value]) => ({
        labels: this._parseLabelKey(labelKey),
        value
      }));
    }
    
    // Process histograms
    const histograms = {};
    for (const [name, histogramsMap] of metrics.histograms.entries()) {
      histograms[name] = Array.from(histogramsMap.entries()).map(([labelKey, histogram]) => ({
        labels: this._parseLabelKey(labelKey),
        sum: histogram.sum,
        count: histogram.count,
        buckets: histogram.buckets
      }));
    }
    
    // Process summaries
    const summaries = {};
    for (const [name, summariesMap] of metrics.summaries.entries()) {
      summaries[name] = Array.from(summariesMap.entries()).map(([labelKey, summary]) => ({
        labels: this._parseLabelKey(labelKey),
        sum: summary.sum,
        count: summary.count,
        values: summary.values.length
      }));
    }
    
    // Update metrics data
    this.dashboardData.metrics = {
      counters,
      gauges,
      histograms,
      summaries
    };
    
    // Extract request metrics
    this.updateRequestMetrics();
    
    // Extract error metrics
    this.updateErrorMetrics();
  }

  /**
   * Update request metrics
   */
  updateRequestMetrics() {
    const requests = {
      total: 0,
      byMethod: {},
      byPath: {},
      byStatus: {},
      responseTime: {
        avg: 0,
        p50: 0,
        p95: 0,
        p99: 0
      }
    };
    
    // Get request metrics
    const requestsTotal = this.dashboardData.metrics.counters['nova_connect_http_requests_total'] || [];
    const requestDuration = this.dashboardData.metrics.histograms['nova_connect_http_request_duration_seconds'] || [];
    
    // Process request totals
    for (const metric of requestsTotal) {
      const { method, path, status } = metric.labels;
      const value = metric.value;
      
      // Update total
      requests.total += value;
      
      // Update by method
      requests.byMethod[method] = (requests.byMethod[method] || 0) + value;
      
      // Update by path
      requests.byPath[path] = (requests.byPath[path] || 0) + value;
      
      // Update by status
      requests.byStatus[status] = (requests.byStatus[status] || 0) + value;
    }
    
    // Process request durations
    if (requestDuration.length > 0) {
      // Calculate average response time
      let totalDuration = 0;
      let totalCount = 0;
      
      for (const metric of requestDuration) {
        totalDuration += metric.sum;
        totalCount += metric.count;
      }
      
      if (totalCount > 0) {
        requests.responseTime.avg = totalDuration / totalCount;
      }
      
      // Note: In a real implementation, we would calculate percentiles from the histogram buckets
      // This is a simplified implementation
    }
    
    // Update request data
    this.dashboardData.requests = requests;
  }

  /**
   * Update error metrics
   */
  updateErrorMetrics() {
    const errors = {
      total: 0,
      byType: {},
      byCode: {},
      byPath: {}
    };
    
    // Get error metrics
    const errorsTotal = this.dashboardData.metrics.counters['nova_connect_errors_total'] || [];
    const errorsByPath = this.dashboardData.metrics.counters['nova_connect_errors_by_path_total'] || [];
    
    // Process error totals
    for (const metric of errorsTotal) {
      const { error_type, error_code } = metric.labels;
      const value = metric.value;
      
      // Update total
      errors.total += value;
      
      // Update by type
      errors.byType[error_type] = (errors.byType[error_type] || 0) + value;
      
      // Update by code
      errors.byCode[error_code] = (errors.byCode[error_code] || 0) + value;
    }
    
    // Process errors by path
    for (const metric of errorsByPath) {
      const { path, error_type } = metric.labels;
      const value = metric.value;
      
      // Update by path
      if (!errors.byPath[path]) {
        errors.byPath[path] = {
          total: 0,
          byType: {}
        };
      }
      
      errors.byPath[path].total += value;
      errors.byPath[path].byType[error_type] = (errors.byPath[path].byType[error_type] || 0) + value;
    }
    
    // Update error data
    this.dashboardData.errors = errors;
  }

  /**
   * Update health data
   */
  async updateHealthData() {
    try {
      // Run health checks
      const healthChecks = await healthService.runChecks();
      
      // Update health data
      this.dashboardData.health = healthChecks;
    } catch (error) {
      logger.error('Error updating health data:', { error });
      
      this.dashboardData.health = {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }

  /**
   * Get dashboard data
   * 
   * @returns {Object} - The dashboard data
   */
  getDashboardData() {
    return {
      ...this.dashboardData,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Parse label key into an object
   * 
   * @param {string} labelKey - The label key
   * @returns {Object} - The parsed labels
   * @private
   */
  _parseLabelKey(labelKey) {
    const labels = {};
    
    labelKey.split(',').forEach(pair => {
      const [key, value] = pair.split(':');
      labels[key] = value;
    });
    
    return labels;
  }
}

// Create singleton instance
const dashboardService = new DashboardService();

module.exports = dashboardService;
