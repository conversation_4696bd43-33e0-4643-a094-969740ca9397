/**
 * Validation Patterns
 * 
 * This file defines common validation patterns used across the Privacy Management API.
 */

// MongoDB ObjectId pattern (24 hexadecimal characters)
const objectIdPattern = /^[0-9a-fA-F]{24}$/;

// Email pattern
const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

// URL pattern
const urlPattern = /^(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;

// Phone number pattern (international format)
const phonePattern = /^\+?[1-9]\d{1,14}$/;

// UUID pattern
const uuidPattern = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;

// ISO date pattern (YYYY-MM-DD)
const isoDatePattern = /^\d{4}-\d{2}-\d{2}$/;

// ISO datetime pattern (YYYY-MM-DDTHH:mm:ss.sssZ)
const isoDatetimePattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z?$/;

// IP address pattern (IPv4 and IPv6)
const ipPattern = /^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$|^(([a-fA-F]|[0-9a-fA-F][0-9a-fA-F])*):(([a-fA-F]|[0-9a-fA-F][0-9a-fA-F])*:)*((([a-fA-F]|[0-9a-fA-F][0-9a-fA-F])+)|((([a-fA-F]|[0-9a-fA-F][0-9a-fA-F])+:)*(([a-fA-F]|[0-9a-fA-F][0-9a-fA-F])+)?))$/;

// Username pattern (alphanumeric, underscore, hyphen, 3-30 characters)
const usernamePattern = /^[a-zA-Z0-9_-]{3,30}$/;

// Password pattern (at least 8 characters, at least one uppercase, one lowercase, one number)
const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d\w\W]{8,}$/;

module.exports = {
  objectIdPattern,
  emailPattern,
  urlPattern,
  phonePattern,
  uuidPattern,
  isoDatePattern,
  isoDatetimePattern,
  ipPattern,
  usernamePattern,
  passwordPattern
};
