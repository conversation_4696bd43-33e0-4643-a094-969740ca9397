const axios = require('axios');
const { performance } = require('perf_hooks');
const { randomBytes } = require('crypto');
const fs = require('fs');
const path = require('path');

// Configuration
const SERVER_URL = 'http://localhost:8080';
const TEST_DURATION = 30000; // 30 seconds per test
const TEST_ACCOUNTS = 10; // Reduced from 20 to 10 for better performance
const REQUESTS_PER_SECOND = 5; // Reduced from 10 to 5 to prevent overwhelming the server

// Test accounts
const accounts = Array(TEST_ACCOUNTS).fill().map((_, i) => ({
  id: `test-node-${i}`,
  address: '0x' + randomBytes(20).toString('hex'),
  balance: 0,
  nonce: 0
}));

// Test results
const results = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  responseTimes: [],
  errors: new Map()
};

// Create results directory
const RESULTS_DIR = path.join(__dirname, 'test-results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR);
}

// Helper function to save results
function saveResults() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    .replace('T', '_')
    .replace('Z', '');
  const filename = `perf-test-${timestamp}.json`;
  const filepath = path.join(RESULTS_DIR, filename);
  
  const resultData = {
    timestamp: new Date().toISOString(),
    duration: TEST_DURATION / 1000,
    ...results,
    errors: Object.fromEntries(results.errors)
  };
  
  fs.writeFileSync(filepath, JSON.stringify(resultData, null, 2));
  return filepath;
}

// Send a single transaction
async function sendTransaction(fromAccount, toAccount) {
  const start = performance.now();
  
  try {
    await axios.post(
      `${SERVER_URL}/aetherium/send`,
      {
        from: fromAccount.address,
        to: toAccount.address,
        value: '**********000000', // 0.001 AE
        maxFeePerGas: '**********',
        maxPriorityFeePerGas: '**********',
        data: '0x' + randomBytes(32).toString('hex')
      },
      {
        headers: { 'x-node-id': fromAccount.id },
        timeout: 10000
      }
    );
    
    const duration = performance.now() - start;
    results.successfulRequests++;
    results.responseTimes.push(duration);
    return { success: true, duration };
  } catch (error) {
    const duration = performance.now() - start;
    const errorMsg = error.response?.data?.error || error.message;
    results.failedRequests++;
    results.errors.set(errorMsg, (results.errors.get(errorMsg) || 0) + 1);
    return { success: false, duration, error: errorMsg };
  } finally {
    results.totalRequests++;
  }
}

// Main test function
async function runTest() {
  console.log('🚀 Starting KetherNet Performance Test\n');
  
  // Check server status
  try {
    console.log('🔍 Checking server status...');
    const { data: status } = await axios.get(`${SERVER_URL}/health`);
    console.log(`✅ Server online | Status: ${status.status}`);
  } catch (error) {
    console.error('❌ Server not responding:', error.message);
    process.exit(1);
  }
  
  // Initialize test accounts
  console.log(`\n🔧 Initializing ${accounts.length} test accounts...`);
  for (const account of accounts) {
    try {
      await axios.post(
        `${SERVER_URL}/aetherium/faucet`,
        { address: account.address },
        { headers: { 'x-node-id': account.id } }
      );
      process.stdout.write('.');
    } catch (error) {
      console.warn(`\n⚠️ Failed to fund account ${account.address}:`, error.message);
    }
  }
  console.log('\n✅ Test accounts initialized');
  
  // Run test
  console.log(`\n🚀 Running test for ${TEST_DURATION/1000} seconds at ${REQUESTS_PER_SECOND} TPS...`);
  
  const startTime = performance.now();
  const endTime = startTime + TEST_DURATION;
  let lastPrint = startTime;
  
  // Main test loop
  while (performance.now() < endTime) {
    const batchPromises = [];
    const batchStart = performance.now();
    
    // Create batch of requests
    for (let i = 0; i < REQUESTS_PER_SECOND; i++) {
      const fromAccount = accounts[Math.floor(Math.random() * accounts.length)];
      const toAccount = accounts[Math.floor(Math.random() * accounts.length)];
      batchPromises.push(sendTransaction(fromAccount, toAccount));
    }
    
    // Wait for batch to complete
    await Promise.all(batchPromises);
    
    // Print progress every second
    if (performance.now() - lastPrint >= 1000) {
      const elapsed = (performance.now() - startTime) / 1000;
      const tps = results.successfulRequests / elapsed;
      process.stdout.write(`\r⏱  Elapsed: ${elapsed.toFixed(1)}s | TPS: ${tps.toFixed(1)} | Success: ${results.successfulRequests} | Failed: ${results.failedRequests}`);
      lastPrint = performance.now();
    }
    
    // Calculate delay to maintain target TPS
    const batchTime = performance.now() - batchStart;
    const targetBatchTime = 1000; // 1 second per batch
    if (batchTime < targetBatchTime) {
      await new Promise(resolve => setTimeout(resolve, targetBatchTime - batchTime));
    }
  }
  
  // Calculate final statistics
  const totalTime = (performance.now() - startTime) / 1000;
  const tps = results.successfulRequests / totalTime;
  const successRate = (results.successfulRequests / results.totalRequests) * 100;
  
  // Calculate response time percentiles
  const sortedTimes = results.responseTimes.sort((a, b) => a - b);
  const p50 = sortedTimes[Math.floor(sortedTimes.length * 0.5)] || 0;
  const p90 = sortedTimes[Math.floor(sortedTimes.length * 0.9)] || 0;
  const p99 = sortedTimes[Math.floor(sortedTimes.length * 0.99)] || p90;
  
  // Print final results
  console.log('\n\n📊 Test Results:');
  console.log('='.repeat(50));
  console.log(`Duration: ${totalTime.toFixed(2)}s`);
  console.log(`Total Requests: ${results.totalRequests}`);
  console.log(`Successful: ${results.successfulRequests} (${successRate.toFixed(2)}%)`);
  console.log(`Failed: ${results.failedRequests}`);
  console.log(`Throughput: ${tps.toFixed(2)} TPS`);
  console.log('\n⏱️  Response Times (ms):');
  console.log(`  Average: ${(results.responseTimes.reduce((a, b) => a + b, 0) / results.responseTimes.length || 0).toFixed(2)}`);
  console.log(`  Min: ${Math.min(...results.responseTimes) || 0}`);
  console.log(`  Max: ${Math.max(...results.responseTimes) || 0}`);
  console.log(`  p50: ${p50.toFixed(2)}`);
  console.log(`  p90: ${p90.toFixed(2)}`);
  console.log(`  p99: ${p99.toFixed(2)}`);
  
  // Save results to file
  const resultFile = saveResults();
  console.log(`\n💾 Results saved to: ${resultFile}`);
  
  // Print errors if any
  if (results.errors.size > 0) {
    console.log('\n❌ Errors encountered:');
    for (const [error, count] of results.errors.entries()) {
      console.log(`  ${count}x ${error}`);
    }
  }
  
  console.log('\n🏁 Test completed');
}

// Run the test
runTest().catch(console.error);
