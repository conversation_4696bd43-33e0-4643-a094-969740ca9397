/**
 * Performance Test Script for Cyber-Safety Visualizations
 * 
 * This script runs performance tests on the Cyber-Safety fusion visualizations
 * to ensure they perform well with large datasets.
 * 
 * Usage:
 * node runPerformanceTests.js
 */

const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');
const puppeteer = require('puppeteer');

// Test configuration
const TEST_CONFIG = {
  // Visualization types to test
  visualizationTypes: [
    'tri_domain_tensor',
    'cyber_safety_harmony_index',
    'risk_control_fusion_map',
    'cyber_safety_resonance_spectrogram',
    'unified_compliance_security_visualizer'
  ],
  
  // Data sizes to test
  dataSizes: {
    small: {
      domainCount: 3,
      connectionCount: 5,
      pointsPerDomain: 100
    },
    medium: {
      domainCount: 5,
      connectionCount: 10,
      pointsPerDomain: 500
    },
    large: {
      domainCount: 8,
      connectionCount: 20,
      pointsPerDomain: 1000
    },
    extraLarge: {
      domainCount: 10,
      connectionCount: 50,
      pointsPerDomain: 5000
    }
  },
  
  // Detail levels to test
  detailLevels: ['low', 'medium', 'high'],
  
  // Number of test iterations
  iterations: 3,
  
  // Test timeout in milliseconds
  timeout: 60000,
  
  // Output file for test results
  outputFile: 'performance-test-results.json'
};

/**
 * Generate test data for a specific visualization type and size
 * @param {string} visualizationType - The type of visualization
 * @param {Object} sizeConfig - The size configuration
 * @returns {Object} - The test data
 */
function generateTestData(visualizationType, sizeConfig) {
  const { domainCount, connectionCount, pointsPerDomain } = sizeConfig;
  
  // Generate domain names
  const domains = [];
  const baseDomains = ['grc', 'it', 'cybersecurity', 'medical', 'financial', 'legal', 'operational', 'physical', 'social', 'environmental'];
  
  for (let i = 0; i < domainCount; i++) {
    domains.push(i < baseDomains.length ? baseDomains[i] : `domain${i + 1}`);
  }
  
  // Generate random data based on visualization type
  switch (visualizationType) {
    case 'tri_domain_tensor': {
      const data = {};
      
      // Generate domain data
      domains.forEach(domain => {
        data[domain] = {
          values: Array.from({ length: pointsPerDomain }, () => Math.random()),
          health: Math.random() * 0.5 + 0.5,
          entropyContainment: Math.random() * 0.1
        };
      });
      
      // Generate connections
      data.connections = [];
      for (let i = 0; i < connectionCount; i++) {
        const sourceIndex = Math.floor(Math.random() * domains.length);
        let targetIndex;
        
        do {
          targetIndex = Math.floor(Math.random() * domains.length);
        } while (targetIndex === sourceIndex);
        
        data.connections.push({
          source: domains[sourceIndex],
          target: domains[targetIndex],
          strength: Math.random() * 0.7 + 0.3
        });
      }
      
      return data;
    }
    
    case 'cyber_safety_harmony_index': {
      const domainData = {};
      
      // Generate domain data
      domains.forEach(domain => {
        const metrics = {};
        
        // Generate metrics for each domain
        for (let i = 0; i < 3; i++) {
          metrics[`metric${i + 1}`] = Math.random() * 0.7 + 0.3;
        }
        
        domainData[domain] = {
          score: Math.random() * 0.7 + 0.3,
          metrics
        };
      });
      
      // Generate harmony history
      const harmonyHistory = Array.from({ length: pointsPerDomain / 10 }, () => Math.random() * 0.7 + 0.3);
      
      return {
        domainData,
        harmonyHistory
      };
    }
    
    case 'risk_control_fusion_map': {
      const riskData = {};
      const controlData = {};
      
      // Generate domain data
      domains.forEach(domain => {
        riskData[domain] = {};
        controlData[domain] = {};
        
        // Generate categories for each domain
        for (let i = 0; i < 3; i++) {
          const category = `category${i + 1}`;
          riskData[domain][category] = Math.random() * 0.7 + 0.3;
          controlData[domain][category] = Math.random() * 0.7 + 0.3;
        }
      });
      
      return {
        riskData,
        controlData
      };
    }
    
    case 'cyber_safety_resonance_spectrogram': {
      const domainData = {};
      
      // Generate domain data
      domains.forEach(domain => {
        domainData[domain] = {
          values: Array.from({ length: pointsPerDomain }, () => Math.random()),
          frequency: Math.random() * 0.7 + 0.3,
          amplitude: Math.random() * 0.5 + 0.5,
          phase: Math.random() * Math.PI
        };
      });
      
      // Generate cross-domain flows
      domainData.crossDomainFlows = [];
      for (let i = 0; i < connectionCount; i++) {
        const sourceIndex = Math.floor(Math.random() * domains.length);
        let targetIndex;
        
        do {
          targetIndex = Math.floor(Math.random() * domains.length);
        } while (targetIndex === sourceIndex);
        
        domainData.crossDomainFlows.push({
          source: domains[sourceIndex],
          target: domains[targetIndex],
          strength: Math.random() * 0.7 + 0.3,
          frequency: Math.random() * 0.7 + 0.3
        });
      }
      
      // Generate prediction data
      const criticalPoints = [];
      const criticalPointCount = Math.min(10, pointsPerDomain / 100);
      
      for (let i = 0; i < criticalPointCount; i++) {
        criticalPoints.push({
          timeStep: Math.floor(Math.random() * 10) + 1,
          severity: Math.random() * 0.7 + 0.3,
          description: `Critical point ${i + 1}`
        });
      }
      
      return {
        domainData,
        predictionData: {
          timeHorizon: 10,
          dissonanceProbability: Math.random() * 0.5,
          criticalPoints
        }
      };
    }
    
    case 'unified_compliance_security_visualizer': {
      // Generate requirements
      const requirementCount = Math.min(20, pointsPerDomain / 50);
      const requirements = [];
      
      for (let i = 0; i < requirementCount; i++) {
        const domain = domains[Math.floor(Math.random() * domains.length)];
        
        requirements.push({
          id: `req${i + 1}`,
          name: `Requirement ${i + 1}`,
          domain,
          completeness: Math.random() * 0.7 + 0.3
        });
      }
      
      // Generate controls
      const controlCount = Math.min(30, pointsPerDomain / 30);
      const controls = [];
      
      for (let i = 0; i < controlCount; i++) {
        const domain = domains[Math.floor(Math.random() * domains.length)];
        
        controls.push({
          id: `ctrl${i + 1}`,
          name: `Control ${i + 1}`,
          domain,
          completeness: Math.random() * 0.7 + 0.3
        });
      }
      
      // Generate implementations
      const implementationCount = Math.min(30, pointsPerDomain / 30);
      const implementations = [];
      
      for (let i = 0; i < implementationCount; i++) {
        const domain = domains[Math.floor(Math.random() * domains.length)];
        
        implementations.push({
          id: `impl${i + 1}`,
          name: `Implementation ${i + 1}`,
          domain,
          completeness: Math.random() * 0.7 + 0.3
        });
      }
      
      // Generate links
      const links = [];
      
      // Links from requirements to controls
      for (let i = 0; i < requirementCount; i++) {
        const req = requirements[i];
        const linkCount = Math.floor(Math.random() * 3) + 1;
        
        for (let j = 0; j < linkCount; j++) {
          const controlIndex = Math.floor(Math.random() * controlCount);
          
          links.push({
            source: req.id,
            target: controls[controlIndex].id,
            strength: Math.random() * 0.7 + 0.3,
            efficiency: Math.random() * 0.7 + 0.3
          });
        }
      }
      
      // Links from controls to implementations
      for (let i = 0; i < controlCount; i++) {
        const ctrl = controls[i];
        const linkCount = Math.floor(Math.random() * 2) + 1;
        
        for (let j = 0; j < linkCount; j++) {
          const implIndex = Math.floor(Math.random() * implementationCount);
          
          links.push({
            source: ctrl.id,
            target: implementations[implIndex].id,
            strength: Math.random() * 0.7 + 0.3,
            efficiency: Math.random() * 0.7 + 0.3
          });
        }
      }
      
      return {
        complianceData: {
          requirements,
          controls,
          implementations,
          links
        },
        impactAnalysis: {
          proposedChanges: [
            { id: 'change1', target: controls[0].id, impact: 0.7, description: 'Test change 1' },
            { id: 'change2', target: implementations[0].id, impact: 0.5, description: 'Test change 2' }
          ]
        }
      };
    }
    
    default:
      return {};
  }
}

/**
 * Run performance tests for all visualization types, data sizes, and detail levels
 */
async function runPerformanceTests() {
  console.log('Starting performance tests...');
  
  const results = {
    timestamp: new Date().toISOString(),
    config: TEST_CONFIG,
    tests: []
  };
  
  // Launch browser
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    // Create a new page
    const page = await browser.newPage();
    
    // Set viewport
    await page.setViewport({
      width: 1280,
      height: 800
    });
    
    // Expose function to measure rendering performance
    await page.exposeFunction('measurePerformance', async (visualizationType, dataSize, detailLevel, data) => {
      // Start performance measurement
      const start = performance.now();
      
      // Create visualization component
      const component = `
        <div id="visualization-container" style="width: 1200px; height: 700px;">
          <div id="visualization"></div>
        </div>
      `;
      
      // Add component to page
      await page.evaluate((component) => {
        document.body.innerHTML = component;
      }, component);
      
      // Render visualization
      await page.evaluate((visualizationType, data, detailLevel) => {
        // Mock visualization rendering
        const container = document.getElementById('visualization');
        container.innerHTML = `Rendering ${visualizationType} with ${Object.keys(data).length} data points at ${detailLevel} detail level...`;
        
        // Simulate rendering time based on data size and detail level
        const renderTime = Object.keys(data).length * (detailLevel === 'high' ? 0.1 : detailLevel === 'medium' ? 0.05 : 0.02);
        
        return new Promise(resolve => {
          setTimeout(() => {
            container.innerHTML += '<br>Rendering complete!';
            resolve();
          }, renderTime);
        });
      }, visualizationType, data, detailLevel);
      
      // End performance measurement
      const end = performance.now();
      
      // Calculate metrics
      const renderTime = end - start;
      const memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024; // MB
      
      return {
        renderTime,
        memoryUsage
      };
    });
    
    // Run tests for each visualization type, data size, and detail level
    for (const visualizationType of TEST_CONFIG.visualizationTypes) {
      console.log(`Testing ${visualizationType}...`);
      
      for (const [sizeKey, sizeConfig] of Object.entries(TEST_CONFIG.dataSizes)) {
        console.log(`  Data size: ${sizeKey}`);
        
        // Generate test data
        const testData = generateTestData(visualizationType, sizeConfig);
        
        for (const detailLevel of TEST_CONFIG.detailLevels) {
          console.log(`    Detail level: ${detailLevel}`);
          
          // Run multiple iterations
          const iterationResults = [];
          
          for (let i = 0; i < TEST_CONFIG.iterations; i++) {
            console.log(`      Iteration ${i + 1}/${TEST_CONFIG.iterations}`);
            
            // Measure performance
            const metrics = await page.evaluate(
              (visualizationType, dataSize, detailLevel, data) => {
                return window.measurePerformance(visualizationType, dataSize, detailLevel, data);
              },
              visualizationType,
              sizeKey,
              detailLevel,
              testData
            );
            
            iterationResults.push(metrics);
          }
          
          // Calculate average metrics
          const avgRenderTime = iterationResults.reduce((sum, result) => sum + result.renderTime, 0) / TEST_CONFIG.iterations;
          const avgMemoryUsage = iterationResults.reduce((sum, result) => sum + result.memoryUsage, 0) / TEST_CONFIG.iterations;
          
          // Add test result
          results.tests.push({
            visualizationType,
            dataSize: sizeKey,
            detailLevel,
            metrics: {
              avgRenderTime,
              avgMemoryUsage,
              iterations: iterationResults
            }
          });
          
          console.log(`      Average render time: ${avgRenderTime.toFixed(2)}ms`);
          console.log(`      Average memory usage: ${avgMemoryUsage.toFixed(2)}MB`);
        }
      }
    }
    
    // Write results to file
    fs.writeFileSync(
      path.join(__dirname, TEST_CONFIG.outputFile),
      JSON.stringify(results, null, 2)
    );
    
    console.log(`Performance test results written to ${TEST_CONFIG.outputFile}`);
  } catch (error) {
    console.error('Error running performance tests:', error);
  } finally {
    // Close browser
    await browser.close();
  }
}

// Run the tests
runPerformanceTests();
