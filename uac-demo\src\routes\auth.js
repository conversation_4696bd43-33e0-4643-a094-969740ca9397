/**
 * Authentication Routes
 * 
 * This module defines the authentication routes for the UAC demo.
 */

const express = require('express');
const router = express.Router();
const { getLogger } = require('../core/logger');

const logger = getLogger('auth-routes');

// Middleware to check authentication
const checkAuth = (req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  const token = authHeader.split(' ')[1];
  
  try {
    const decoded = req.uac.authManager.verifyToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    logger.error('Authentication failed', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};

// Login route
router.post('/login', (req, res) => {
  const { username, password } = req.body;
  
  if (!username || !password) {
    return res.status(400).json({ error: 'Username and password are required' });
  }
  
  try {
    const result = req.uac.authManager.authenticateUser(username, password);
    res.json(result);
  } catch (error) {
    logger.error('Login failed', error);
    res.status(401).json({ error: error.message });
  }
});

// Register route
router.post('/register', checkAuth, (req, res) => {
  // Only admins can register new users
  if (req.user.role !== req.uac.authManager.ROLES.ADMIN) {
    return res.status(403).json({ error: 'Forbidden' });
  }
  
  const { username, password, role, name } = req.body;
  
  if (!username || !password) {
    return res.status(400).json({ error: 'Username and password are required' });
  }
  
  try {
    const user = req.uac.authManager.registerUser(username, password, { role, name });
    res.status(201).json(user);
  } catch (error) {
    logger.error('User registration failed', error);
    res.status(400).json({ error: error.message });
  }
});

// Get current user
router.get('/me', checkAuth, (req, res) => {
  const user = req.uac.authManager.getUser(req.user.username);
  
  if (!user) {
    return res.status(404).json({ error: 'User not found' });
  }
  
  res.json(user);
});

// List users (admin only)
router.get('/users', checkAuth, (req, res) => {
  if (req.user.role !== req.uac.authManager.ROLES.ADMIN) {
    return res.status(403).json({ error: 'Forbidden' });
  }
  
  const users = req.uac.authManager.listUsers();
  res.json(users);
});

// Logout route
router.post('/logout', checkAuth, (req, res) => {
  const authHeader = req.headers.authorization;
  const token = authHeader.split(' ')[1];
  
  req.uac.authManager.revokeToken(token);
  res.json({ message: 'Logged out successfully' });
});

module.exports = router;
