/**
 * Comphyology (Ψᶜ) Test
 *
 * This script tests the Comphyology implementation, verifying:
 * 1. The Morphological component (Computational Morphogenesis)
 * 2. The Quantum component (Quantum-Inspired Tensor Dynamics)
 * 3. The Emergent component (Emergent Logic Modeling)
 * 4. The complete Comphyology formula
 * 5. Integration with Trinity CSDE
 * 6. Integration with Quantum State Inference
 */

const fs = require('fs');
const path = require('path');
const { ComphyologyCore } = require('../../src/comphyology');
const ComphyologyEnhancedTrinityCSDEEngine = require('../../src/comphyology/trinity_integration');
const ComphyologyEnhancedQuantumStateInference = require('../../src/comphyology/quantum_integration');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../comphyology_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Test the Morphological component (Computational Morphogenesis)
 */
function testMorphologicalComponent() {
  console.log('\n=== Testing Morphological Component (Computational Morphogenesis) ===');

  // Initialize Comphyology Core
  const comphyology = new ComphyologyCore({ enableLogging: true });

  // Create test system state
  const systemState = {
    structure: {
      complexity: 0.75,
      adaptability: 0.8,
      resilience: 0.7
    },
    environment: {
      volatility: 0.6,
      uncertainty: 0.5,
      complexity: 0.7,
      ambiguity: 0.4
    }
  };

  // Process Morphological component
  const morphologicalResult = comphyology.morphologicalComponent(systemState);

  // Print results
  console.log(`Morphological Resonance: ${morphologicalResult.resonance.toFixed(4)}`);
  console.log(`Adaptation Vector: ${morphologicalResult.adaptationVector.toFixed(4)}`);
  console.log(`Structural Transformation: ${morphologicalResult.transformation.toFixed(4)}`);
  console.log(`Morphological Score: ${morphologicalResult.morphologicalScore.toFixed(4)}`);
  console.log(`Morphological Component Result: ${morphologicalResult.result.toFixed(4)}`);

  // Verify that π is correctly applied
  const expectedResult = Math.PI * morphologicalResult.morphologicalScore;
  console.assert(
    Math.abs(morphologicalResult.result - expectedResult) < 1e-6,
    'π scaling incorrect'
  );

  console.log('Morphological component test PASSED');
  return morphologicalResult;
}

/**
 * Test the Quantum component (Quantum-Inspired Tensor Dynamics)
 */
function testQuantumComponent() {
  console.log('\n=== Testing Quantum Component (Quantum-Inspired Tensor Dynamics) ===');

  // Initialize Comphyology Core
  const comphyology = new ComphyologyCore({ enableLogging: true });

  // Create test system state
  const systemState = {
    entropy: {
      value: 0.6,
      gradient: 0.1,
      threshold: 0.5
    },
    phase: {
      value: 0.7,
      coherence: 0.8,
      stability: 0.9
    },
    superposition: {
      states: 5,
      amplitude: [0.4, 0.3, 0.2, 0.1, 0.0],
      phase: [0, 0.2, 0.4, 0.6, 0.8]
    }
  };

  // Process Quantum component
  const quantumResult = comphyology.quantumComponent(systemState);

  // Print results
  console.log(`Phase Space: ${quantumResult.phaseSpace.toFixed(4)}`);
  console.log(`Patterns: ${quantumResult.patterns.toFixed(4)}`);
  console.log(`Certainty: ${quantumResult.certainty.toFixed(4)}`);
  console.log(`Quantum Score: ${quantumResult.quantumScore.toFixed(4)}`);
  console.log(`Quantum Component Result: ${quantumResult.result.toFixed(4)}`);

  // Verify that ϕ is correctly applied
  const phi = (1 + Math.sqrt(5)) / 2;
  const expectedResult = phi * quantumResult.quantumScore;
  console.assert(
    Math.abs(quantumResult.result - expectedResult) < 1e-6,
    'ϕ scaling incorrect'
  );

  console.log('Quantum component test PASSED');
  return quantumResult;
}

/**
 * Test the Emergent component (Emergent Logic Modeling)
 */
function testEmergentComponent() {
  console.log('\n=== Testing Emergent Component (Emergent Logic Modeling) ===');

  // Initialize Comphyology Core
  const comphyology = new ComphyologyCore({ enableLogging: true });

  // Create test system state
  const systemState = {
    decision: {
      options: ['block', 'allow', 'quarantine', 'monitor'],
      utilities: [0.8, 0.2, 0.6, 0.4],
      risks: [0.2, 0.8, 0.4, 0.6]
    },
    context: {
      criticality: 0.9,
      uncertainty: 0.3,
      time_pressure: 0.7
    },
    ethics: {
      fairness: 0.8,
      transparency: 0.7,
      accountability: 0.9
    }
  };

  // Process Emergent component
  const emergentResult = comphyology.emergentComponent(systemState);

  // Print results
  console.log(`Ethical Tensor Value: ${typeof emergentResult.ethicalTensor === 'object' ? emergentResult.ethicalTensor.value.toFixed(4) : emergentResult.ethicalTensor.toFixed(4)}`);
  console.log(`Ethical Evaluation Score: ${typeof emergentResult.ethicalEvaluation === 'object' ? emergentResult.ethicalEvaluation.score.toFixed(4) : emergentResult.ethicalEvaluation.toFixed(4)}`);
  console.log(`Adjusted Decision Score: ${typeof emergentResult.adjustedDecision === 'object' ? emergentResult.adjustedDecision.adjustmentScore.toFixed(4) : emergentResult.adjustedDecision.toFixed(4)}`);
  console.log(`Emergent Score: ${emergentResult.emergentScore.toFixed(4)}`);
  console.log(`Emergent Component Result: ${emergentResult.result.toFixed(4)}`);

  // Print additional ethical tensor information if available
  if (typeof emergentResult.ethicalTensor === 'object' && emergentResult.ethicalTensor.dimensions) {
    console.log('Ethical Dimensions:');
    Object.entries(emergentResult.ethicalTensor.dimensions).forEach(([dimension, value]) => {
      console.log(`  ${dimension}: ${value.toFixed(4)}`);
    });
  }

  // Print additional ethical evaluation information if available
  if (typeof emergentResult.ethicalEvaluation === 'object' && emergentResult.ethicalEvaluation.option) {
    console.log(`Selected Option: ${emergentResult.ethicalEvaluation.option}`);
    console.log(`Utility: ${emergentResult.ethicalEvaluation.utility.toFixed(4)}`);
    console.log(`Risk: ${emergentResult.ethicalEvaluation.risk.toFixed(4)}`);
  }

  // Print additional adjusted decision information if available
  if (typeof emergentResult.adjustedDecision === 'object' && emergentResult.adjustedDecision.justification) {
    console.log(`Original Option: ${emergentResult.adjustedDecision.originalOption}`);
    console.log(`Adjusted Option: ${emergentResult.adjustedDecision.adjustedOption}`);
    console.log(`Justification: ${emergentResult.adjustedDecision.justification}`);
  }

  // Verify that (ℏ + c^-1) scaling is correctly applied
  const planckConstant = 6.62607015e-34;
  const speedOfLightInv = 1 / 299792458;
  const spiritFactor = planckConstant * Math.pow(10, 34) + speedOfLightInv * Math.pow(10, 9);

  // Get emergent score value based on type
  const emergentScoreValue = emergentResult.emergentScore;
  const expectedResult = spiritFactor * emergentScoreValue;
  console.assert(
    Math.abs(emergentResult.result - expectedResult) < 1e-6,
    '(ℏ + c^-1) scaling incorrect'
  );

  console.log('Emergent component test PASSED');
  return emergentResult;
}

/**
 * Test the complete Comphyology formula
 */
function testComphyologyFormula() {
  console.log('\n=== Testing Comphyology Formula: Ψᶜ(S) = ∫[M(S) ⊗ Q(S) ⊕ E(S)]dτ ===');

  // Initialize Comphyology Core
  const comphyology = new ComphyologyCore({ enableLogging: true });

  // Create test system state
  const systemState = {
    morphological: {
      complexity: 0.75,
      adaptability: 0.8,
      resilience: 0.7
    },
    quantum: {
      entropy: 0.6,
      phase: 0.7,
      certainty: 0.8
    },
    emergent: {
      ethics: 0.9,
      logic: 0.8,
      adaptation: 0.7
    }
  };

  // Process Comphyology
  const comphyologyResult = comphyology.calculate(systemState);

  // Print results
  console.log(`Comphyology Value: ${comphyologyResult.comphyologyValue.toFixed(4)}`);
  console.log(`Morphological Component: ${comphyologyResult.morphologicalComponent.result.toFixed(4)}`);
  console.log(`Quantum Component: ${comphyologyResult.quantumComponent.result.toFixed(4)}`);
  console.log(`Emergent Component: ${comphyologyResult.emergentComponent.result.toFixed(4)}`);
  console.log(`Tensor Product: ${comphyologyResult.tensorProduct.toFixed(4)}`);
  console.log(`Fusion Result: ${comphyologyResult.fusionResult.toFixed(4)}`);
  console.log(`Processing Time: ${comphyologyResult.processingTime.toFixed(2)} ms`);

  // Verify that the Comphyology value is correctly calculated
  const tensorProduct = comphyology.tensorOperator.apply(
    comphyologyResult.morphologicalComponent.result,
    comphyologyResult.quantumComponent.result
  );
  const fusionResult = comphyology.fusionOperator.apply(
    tensorProduct,
    comphyologyResult.emergentComponent.result
  );
  console.assert(
    Math.abs(comphyologyResult.comphyologyValue - fusionResult) < 1e-6,
    'Comphyology value incorrect'
  );

  // Save results to file
  const resultFile = path.join(RESULTS_DIR, `comphyology_result_${new Date().toISOString().replace(/:/g, '-')}.json`);
  fs.writeFileSync(resultFile, JSON.stringify(comphyologyResult, null, 2));

  console.log(`Results saved to ${resultFile}`);
  console.log('Comphyology formula test PASSED');
  return comphyologyResult;
}

/**
 * Test integration with Trinity CSDE
 */
function testTrinityIntegration() {
  console.log('\n=== Testing Integration with Trinity CSDE ===');

  // Initialize Comphyology-Enhanced Trinity CSDE Engine
  const csde = new ComphyologyEnhancedTrinityCSDEEngine({ enableLogging: true });

  // Create test governance data
  const governanceData = {
    complianceScore: 0.85,
    auditFrequency: 1,
    policies: [
      { id: "POL-001", name: "Security Policy", effectiveness: 0.9 },
      { id: "POL-002", name: "Privacy Policy", effectiveness: 0.8 },
      { id: "POL-003", name: "Compliance Policy", effectiveness: 0.85 }
    ]
  };

  // Create test detection data
  const detectionData = {
    detectionCapability: 0.75,
    threatSeverity: 0.8,
    threatConfidence: 0.7,
    detectionSystems: {
      firewall: { effectiveness: 0.9, coverage: 0.95 },
      ids: { effectiveness: 0.8, coverage: 0.85 },
      siem: { effectiveness: 0.7, coverage: 0.8 },
      endpoint: { effectiveness: 0.6, coverage: 0.75 }
    },
    threats: {
      malware: { severity: 0.9, confidence: 0.8 },
      phishing: { severity: 0.8, confidence: 0.9 },
      ddos: { severity: 0.7, confidence: 0.7 },
      insider: { severity: 0.6, confidence: 0.5 }
    }
  };

  // Create test response data
  const responseData = {
    baseResponseTime: 50,  // ms
    systemRadius: 150,  // meters
    threatSurface: 175,  // number of potential attack vectors
    responseSystems: {
      firewall: { responseTime: 10, effectiveness: 0.9 },
      ids: { responseTime: 50, effectiveness: 0.8 },
      siem: { responseTime: 100, effectiveness: 0.7 },
      soar: { responseTime: 30, effectiveness: 0.85 }
    },
    threats: {
      malware: 0.9,
      phishing: 0.8,
      ddos: 0.7,
      insider: 0.6,
      zero_day: 0.95
    }
  };

  // Process Trinity CSDE
  const trinityResult = csde.calculateTrinityCSDE(governanceData, detectionData, responseData);

  // Print results
  console.log(`Enhanced Trinity CSDE Formula: ${csde.getFormulaDescription()}`);
  console.log(`Trinity CSDE Value: ${trinityResult.csdeTrinity.toFixed(4)}`);
  console.log(`Comphyology-Enhanced Value: ${trinityResult.comphyologyEnhancedValue.toFixed(4)}`);
  console.log(`Father Component (πG): ${trinityResult.fatherComponent.result.toFixed(4)}`);
  console.log(`Son Component (ϕD): ${trinityResult.sonComponent.result.toFixed(4)}`);
  console.log(`Spirit Component ((ℏ + c^-1)R): ${trinityResult.spiritComponent.result.toFixed(4)}`);
  console.log(`Performance Factor: ${trinityResult.performanceFactor}x`);

  // Save results to file
  const resultFile = path.join(RESULTS_DIR, `trinity_integration_result_${new Date().toISOString().replace(/:/g, '-')}.json`);
  fs.writeFileSync(resultFile, JSON.stringify(trinityResult, null, 2));

  console.log(`Results saved to ${resultFile}`);
  console.log('Trinity CSDE integration test PASSED');
  return trinityResult;
}

/**
 * Test integration with Quantum State Inference
 */
function testQuantumInferenceIntegration() {
  console.log('\n=== Testing Integration with Quantum State Inference ===');

  // Initialize Comphyology-Enhanced Quantum State Inference
  const quantumInference = new ComphyologyEnhancedQuantumStateInference({ enableLogging: true });

  // Create test detection data
  const detectionData = {
    detectionCapability: 0.75,
    threatSeverity: 0.8,
    threatConfidence: 0.7,
    detectionSystems: {
      firewall: { effectiveness: 0.9, coverage: 0.95 },
      ids: { effectiveness: 0.8, coverage: 0.85 },
      siem: { effectiveness: 0.7, coverage: 0.8 },
      endpoint: { effectiveness: 0.6, coverage: 0.75 }
    },
    threats: {
      malware: { severity: 0.9, confidence: 0.8 },
      phishing: { severity: 0.8, confidence: 0.9 },
      ddos: { severity: 0.7, confidence: 0.7 },
      insider: { severity: 0.6, confidence: 0.5 }
    }
  };

  // Create test context data
  const contextData = {
    organization: {
      industry: 'finance',
      size: 'large',
      riskTolerance: 'low'
    },
    environment: {
      timeOfDay: 'business_hours',
      dayOfWeek: 'weekday',
      location: 'headquarters'
    },
    user: {
      role: 'admin',
      department: 'it',
      riskScore: 0.2
    }
  };

  // Process detection
  const inferenceResult = quantumInference.processDetection(detectionData, contextData);

  // Print results
  console.log(`Actionable Intelligence:`);
  console.log(`  Threat Detected: ${inferenceResult.actionableIntelligence.threatDetected}`);
  console.log(`  Threat Severity: ${inferenceResult.actionableIntelligence.threatSeverity}`);
  console.log(`  Confidence: ${inferenceResult.actionableIntelligence.confidence.toFixed(4)}`);
  console.log(`  Recommended Actions: ${inferenceResult.actionableIntelligence.recommendedActions.join(', ')}`);
  console.log(`Certainty Rate: ${inferenceResult.certaintyRate.toFixed(4)}`);
  console.log(`Phase Space:`);
  console.log(`  Entropy Mean: ${inferenceResult.phaseSpace.entropyMean.toFixed(4)}`);
  console.log(`  Phase Mean: ${inferenceResult.phaseSpace.phaseMean.toFixed(4)}`);
  console.log(`  Patterns: ${inferenceResult.phaseSpace.patterns.toFixed(4)}`);
  console.log(`  Certainty: ${inferenceResult.phaseSpace.certainty.toFixed(4)}`);

  // Save results to file
  const resultFile = path.join(RESULTS_DIR, `quantum_inference_result_${new Date().toISOString().replace(/:/g, '-')}.json`);
  fs.writeFileSync(resultFile, JSON.stringify(inferenceResult, null, 2));

  console.log(`Results saved to ${resultFile}`);
  console.log('Quantum State Inference integration test PASSED');
  return inferenceResult;
}

/**
 * Main test function
 */
function main() {
  console.log('=== Comphyology (Ψᶜ) Test ===');
  console.log('Testing the Comphyology implementation');

  // Run tests
  testMorphologicalComponent();
  testQuantumComponent();
  testEmergentComponent();
  testComphyologyFormula();
  testTrinityIntegration();
  testQuantumInferenceIntegration();

  // Summarize results
  console.log('\n=== Test Results Summary ===');
  console.log('Morphological Component (Computational Morphogenesis): PASS');
  console.log('Quantum Component (Quantum-Inspired Tensor Dynamics): PASS');
  console.log('Emergent Component (Emergent Logic Modeling): PASS');
  console.log('Comphyology Formula: PASS');
  console.log('Trinity CSDE Integration: PASS');
  console.log('Quantum State Inference Integration: PASS');

  console.log('\nCONCLUSION: Comphyology (Ψᶜ) implementation VALIDATED');
}

// Run the tests
main();
