/**
 * Data Subject Request Model
 * 
 * Represents a request from a data subject to exercise their rights
 * under privacy regulations such as GDPR, CCPA, etc.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const dataSubjectRequestSchema = new Schema({
  reference: {
    type: String,
    required: true,
    unique: true
  },
  requestType: {
    type: String,
    required: true,
    enum: [
      'Access',
      'Rectification',
      'Erasure',
      'Restriction',
      'Portability',
      'Objection',
      'AutomatedDecisionMaking',
      'Withdraw Consent',
      'Do Not Sell',
      'Other'
    ]
  },
  requestDetails: {
    type: String,
    required: true
  },
  dataSubject: {
    name: {
      type: String,
      required: true
    },
    email: {
      type: String,
      required: true
    },
    phone: String,
    address: String,
    identificationMethod: {
      type: String,
      enum: ['Email Verification', 'ID Document', 'Account Login', 'Other'],
      required: true
    },
    identificationDetails: String,
    identityVerified: {
      type: Boolean,
      default: false
    }
  },
  requestDate: {
    type: Date,
    default: Date.now,
    required: true
  },
  dueDate: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ['New', 'Verification Pending', 'In Progress', 'Awaiting Approval', 'Completed', 'Denied', 'Withdrawn'],
    default: 'New'
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  processingActivities: [{
    type: Schema.Types.ObjectId,
    ref: 'ProcessingActivity'
  }],
  systems: [{
    name: String,
    status: {
      type: String,
      enum: ['Pending', 'Completed', 'Not Applicable', 'Failed'],
      default: 'Pending'
    },
    notes: String
  }],
  thirdParties: [{
    name: String,
    contactDetails: String,
    notificationDate: Date,
    responseDate: Date,
    status: {
      type: String,
      enum: ['Not Notified', 'Notified', 'Completed', 'No Response'],
      default: 'Not Notified'
    },
    notes: String
  }],
  exemptions: {
    applied: {
      type: Boolean,
      default: false
    },
    reason: String,
    legalBasis: String
  },
  response: {
    date: Date,
    method: {
      type: String,
      enum: ['Email', 'Mail', 'Portal', 'In Person', 'Other']
    },
    details: String,
    documents: [{
      name: String,
      type: String,
      url: String,
      uploadDate: Date
    }]
  },
  timeline: [{
    date: {
      type: Date,
      default: Date.now
    },
    action: String,
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: String
  }],
  internalNotes: String,
  regulatoryFramework: {
    type: String,
    enum: ['GDPR', 'CCPA', 'LGPD', 'PIPEDA', 'POPIA', 'Other'],
    required: true
  },
  extensionApplied: {
    applied: {
      type: Boolean,
      default: false
    },
    reason: String,
    newDueDate: Date,
    notificationDate: Date
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Add text index for search functionality
dataSubjectRequestSchema.index({
  reference: 'text',
  'dataSubject.name': 'text',
  'dataSubject.email': 'text',
  requestDetails: 'text'
});

// Add method to check if request is overdue
dataSubjectRequestSchema.methods.isOverdue = function() {
  const today = new Date();
  const dueDate = this.extensionApplied.applied ? this.extensionApplied.newDueDate : this.dueDate;
  return today > dueDate && !['Completed', 'Denied', 'Withdrawn'].includes(this.status);
};

// Add method to calculate days remaining
dataSubjectRequestSchema.methods.daysRemaining = function() {
  const today = new Date();
  const dueDate = this.extensionApplied.applied ? this.extensionApplied.newDueDate : this.dueDate;
  const diffTime = dueDate - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays > 0 ? diffDays : 0;
};

// Add pre-save hook to set due date if not provided
dataSubjectRequestSchema.pre('save', function(next) {
  if (!this.dueDate) {
    const requestDate = this.requestDate || new Date();
    // Default to 30 days for GDPR, adjust for other regulations
    let daysToAdd = 30;
    if (this.regulatoryFramework === 'CCPA') {
      daysToAdd = 45;
    }
    this.dueDate = new Date(requestDate);
    this.dueDate.setDate(this.dueDate.getDate() + daysToAdd);
  }
  next();
});

const DataSubjectRequest = mongoose.model('DataSubjectRequest', dataSubjectRequestSchema);

module.exports = DataSubjectRequest;
