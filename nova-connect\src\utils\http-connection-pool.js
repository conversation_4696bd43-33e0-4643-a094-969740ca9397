/**
 * HTTP Connection Pool
 * 
 * This module provides a connection pool for HTTP requests.
 * It uses keep-alive connections to improve performance and reliability.
 */

const http = require('http');
const https = require('https');
const axios = require('axios');
const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * HTTP Connection Pool
 */
class HttpConnectionPool extends EventEmitter {
  /**
   * Create a new HTTP Connection Pool
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      maxSockets: options.maxSockets || 100,
      maxFreeSockets: options.maxFreeSockets || 10,
      timeout: options.timeout || 60000,
      keepAlive: options.keepAlive !== false,
      keepAliveMsecs: options.keepAliveMsecs || 1000,
      maxTotalSockets: options.maxTotalSockets || 200,
      maxCachedSessions: options.maxCachedSessions || 100,
      enableMetrics: options.enableMetrics !== false,
      logger: options.logger || console,
      ...options
    };
    
    // Create HTTP and HTTPS agents with keep-alive
    this.httpAgent = new http.Agent({
      keepAlive: this.options.keepAlive,
      keepAliveMsecs: this.options.keepAliveMsecs,
      maxSockets: this.options.maxSockets,
      maxFreeSockets: this.options.maxFreeSockets,
      timeout: this.options.timeout
    });
    
    this.httpsAgent = new https.Agent({
      keepAlive: this.options.keepAlive,
      keepAliveMsecs: this.options.keepAliveMsecs,
      maxSockets: this.options.maxSockets,
      maxFreeSockets: this.options.maxFreeSockets,
      timeout: this.options.timeout,
      maxCachedSessions: this.options.maxCachedSessions
    });
    
    // Initialize clients map
    this.clients = new Map();
    
    // Initialize metrics
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalLatency: 0,
      averageLatency: 0,
      activeConnections: 0,
      maxConcurrentConnections: 0,
      requestsPerEndpoint: new Map()
    };
    
    this.options.logger.info('HTTP Connection Pool initialized', {
      maxSockets: this.options.maxSockets,
      maxFreeSockets: this.options.maxFreeSockets,
      keepAlive: this.options.keepAlive
    });
  }
  
  /**
   * Get or create an axios client for a base URL
   * @param {string} baseUrl - Base URL for the client
   * @param {Object} clientOptions - Additional client options
   * @returns {Object} - Axios client
   */
  getClient(baseUrl, clientOptions = {}) {
    // Check if client already exists
    if (this.clients.has(baseUrl)) {
      return this.clients.get(baseUrl);
    }
    
    // Determine if URL is HTTPS
    const isHttps = baseUrl.startsWith('https://');
    
    // Create axios client with appropriate agent
    const client = axios.create({
      baseURL: baseUrl,
      timeout: clientOptions.timeout || this.options.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...clientOptions.headers
      },
      httpAgent: isHttps ? undefined : this.httpAgent,
      httpsAgent: isHttps ? this.httpsAgent : undefined,
      maxRedirects: clientOptions.maxRedirects || 5,
      maxContentLength: clientOptions.maxContentLength || 50 * 1024 * 1024, // 50MB
      maxBodyLength: clientOptions.maxBodyLength || 50 * 1024 * 1024, // 50MB
      decompress: clientOptions.decompress !== false
    });
    
    // Add request interceptor for metrics
    client.interceptors.request.use(
      (config) => {
        // Add start time for latency calculation
        config.metadata = { startTime: performance.now() };
        
        // Update metrics
        if (this.options.enableMetrics) {
          this.metrics.totalRequests++;
          this.metrics.activeConnections++;
          
          // Update max concurrent connections
          if (this.metrics.activeConnections > this.metrics.maxConcurrentConnections) {
            this.metrics.maxConcurrentConnections = this.metrics.activeConnections;
          }
          
          // Update requests per endpoint
          const endpoint = config.url || '/';
          const endpointMetrics = this.metrics.requestsPerEndpoint.get(endpoint) || { count: 0 };
          endpointMetrics.count++;
          this.metrics.requestsPerEndpoint.set(endpoint, endpointMetrics);
        }
        
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
    
    // Add response interceptor for metrics
    client.interceptors.response.use(
      (response) => {
        // Calculate latency
        const latency = performance.now() - response.config.metadata.startTime;
        
        // Update metrics
        if (this.options.enableMetrics) {
          this.metrics.successfulRequests++;
          this.metrics.totalLatency += latency;
          this.metrics.averageLatency = this.metrics.totalLatency / this.metrics.successfulRequests;
          this.metrics.activeConnections--;
        }
        
        return response;
      },
      (error) => {
        // Calculate latency if possible
        if (error.config && error.config.metadata) {
          const latency = performance.now() - error.config.metadata.startTime;
          
          // Update metrics
          if (this.options.enableMetrics) {
            this.metrics.failedRequests++;
            this.metrics.totalLatency += latency;
            this.metrics.averageLatency = this.metrics.totalLatency / 
              (this.metrics.successfulRequests + this.metrics.failedRequests);
            this.metrics.activeConnections--;
          }
        }
        
        return Promise.reject(error);
      }
    );
    
    // Store client
    this.clients.set(baseUrl, client);
    
    return client;
  }
  
  /**
   * Make an HTTP request
   * @param {string} baseUrl - Base URL for the request
   * @param {Object} requestConfig - Request configuration
   * @returns {Promise<Object>} - Response
   */
  async request(baseUrl, requestConfig) {
    const client = this.getClient(baseUrl);
    return client(requestConfig);
  }
  
  /**
   * Make a GET request
   * @param {string} baseUrl - Base URL for the request
   * @param {string} url - URL path
   * @param {Object} config - Request configuration
   * @returns {Promise<Object>} - Response
   */
  async get(baseUrl, url, config = {}) {
    const client = this.getClient(baseUrl);
    return client.get(url, config);
  }
  
  /**
   * Make a POST request
   * @param {string} baseUrl - Base URL for the request
   * @param {string} url - URL path
   * @param {Object} data - Request data
   * @param {Object} config - Request configuration
   * @returns {Promise<Object>} - Response
   */
  async post(baseUrl, url, data, config = {}) {
    const client = this.getClient(baseUrl);
    return client.post(url, data, config);
  }
  
  /**
   * Make a PUT request
   * @param {string} baseUrl - Base URL for the request
   * @param {string} url - URL path
   * @param {Object} data - Request data
   * @param {Object} config - Request configuration
   * @returns {Promise<Object>} - Response
   */
  async put(baseUrl, url, data, config = {}) {
    const client = this.getClient(baseUrl);
    return client.put(url, data, config);
  }
  
  /**
   * Make a DELETE request
   * @param {string} baseUrl - Base URL for the request
   * @param {string} url - URL path
   * @param {Object} config - Request configuration
   * @returns {Promise<Object>} - Response
   */
  async delete(baseUrl, url, config = {}) {
    const client = this.getClient(baseUrl);
    return client.delete(url, config);
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      requestsPerEndpoint: Object.fromEntries(this.metrics.requestsPerEndpoint)
    };
  }
  
  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalLatency: 0,
      averageLatency: 0,
      activeConnections: 0,
      maxConcurrentConnections: 0,
      requestsPerEndpoint: new Map()
    };
  }
  
  /**
   * Close all connections
   */
  close() {
    // Destroy HTTP agent
    if (this.httpAgent) {
      this.httpAgent.destroy();
    }
    
    // Destroy HTTPS agent
    if (this.httpsAgent) {
      this.httpsAgent.destroy();
    }
    
    // Clear clients
    this.clients.clear();
    
    this.options.logger.info('HTTP Connection Pool closed');
  }
}

// Create singleton instance
const httpConnectionPool = new HttpConnectionPool();

module.exports = httpConnectionPool;
