/**
 * Encryption Utilities
 * 
 * This module provides encryption and hashing utilities for the Privacy Management API.
 * It uses AES-256-GCM for encryption and SHA-256 for hashing.
 */

const crypto = require('crypto');
const { logger } = require('./logger');

// Encryption settings
const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16;
const KEY_LENGTH = 32;
const AUTH_TAG_LENGTH = 16;

// Default encryption key - in production, this should be stored in environment variables
const DEFAULT_ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 
  crypto.randomBytes(KEY_LENGTH).toString('hex');

/**
 * Encrypt data using AES-256-GCM
 * 
 * @param {any} data - Data to encrypt (string, object, number, etc.)
 * @param {string} key - Optional encryption key (defaults to environment variable or generated key)
 * @returns {string} - Encrypted data in format: iv:encryptedData:authTag
 */
function encrypt(data, key = DEFAULT_ENCRYPTION_KEY) {
  try {
    // Convert data to string if it's not already
    const dataString = typeof data === 'string' ? data : JSON.stringify(data);
    
    // Generate a random initialization vector
    const iv = crypto.randomBytes(IV_LENGTH);
    
    // Create cipher using key and iv
    const cipher = crypto.createCipheriv(
      ALGORITHM, 
      Buffer.from(key, 'hex').slice(0, KEY_LENGTH), 
      iv
    );
    
    // Encrypt the data
    let encrypted = cipher.update(dataString, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // Get the authentication tag
    const authTag = cipher.getAuthTag().toString('hex');
    
    // Return iv:encryptedData:authTag
    return `${iv.toString('hex')}:${encrypted}:${authTag}`;
  } catch (error) {
    logger.error(`Encryption error: ${error.message}`);
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Decrypt data that was encrypted with AES-256-GCM
 * 
 * @param {string} encryptedData - Data in format: iv:encryptedData:authTag
 * @param {string} key - Optional encryption key (defaults to environment variable or generated key)
 * @returns {any} - Decrypted data
 */
function decrypt(encryptedData, key = DEFAULT_ENCRYPTION_KEY) {
  try {
    // Split the encrypted data into iv, data, and auth tag
    const parts = encryptedData.split(':');
    if (parts.length !== 3) {
      throw new Error('Invalid encrypted data format');
    }
    
    const iv = Buffer.from(parts[0], 'hex');
    const encrypted = parts[1];
    const authTag = Buffer.from(parts[2], 'hex');
    
    // Create decipher
    const decipher = crypto.createDecipheriv(
      ALGORITHM, 
      Buffer.from(key, 'hex').slice(0, KEY_LENGTH), 
      iv
    );
    
    // Set auth tag
    decipher.setAuthTag(authTag);
    
    // Decrypt the data
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    // Try to parse as JSON, return as is if not valid JSON
    try {
      return JSON.parse(decrypted);
    } catch (e) {
      return decrypted;
    }
  } catch (error) {
    logger.error(`Decryption error: ${error.message}`);
    throw new Error('Failed to decrypt data');
  }
}

/**
 * Hash data using SHA-256
 * 
 * @param {any} data - Data to hash
 * @param {string} salt - Optional salt to add to the hash
 * @returns {string} - Hashed data
 */
function hashData(data, salt = '') {
  try {
    // Convert data to string if it's not already
    const dataString = typeof data === 'string' ? data : JSON.stringify(data);
    
    // Create hash
    const hash = crypto.createHash('sha256');
    hash.update(dataString + salt);
    
    return hash.digest('hex');
  } catch (error) {
    logger.error(`Hashing error: ${error.message}`);
    throw new Error('Failed to hash data');
  }
}

/**
 * Generate a random key
 * 
 * @param {number} length - Length of the key in bytes
 * @returns {string} - Random key in hex format
 */
function generateKey(length = KEY_LENGTH) {
  return crypto.randomBytes(length).toString('hex');
}

module.exports = {
  encrypt,
  decrypt,
  hashData,
  generateKey
};
