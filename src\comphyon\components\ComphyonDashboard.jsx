import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  CardHeader, 
  Grid, 
  Paper, 
  Typography, 
  useTheme 
} from '@mui/material';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Toolt<PERSON>, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import ComphyonGauge from './ComphyonGauge';

/**
 * ComphyonDashboard Component
 * 
 * Displays a dashboard with Comphyon metrics, including a gauge and historical charts.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.comphyonMeter - ComphyonMeter instance
 * @param {boolean} props.autoRefresh - Whether to auto-refresh the dashboard
 * @param {number} props.refreshInterval - Refresh interval in milliseconds
 */
const ComphyonDashboard = ({ 
  comphyonMeter, 
  autoRefresh = true, 
  refreshInterval = 1000 
}) => {
  const theme = useTheme();
  
  // State for dashboard data
  const [dashboardData, setDashboardData] = useState({
    currentCph: 0,
    csdeRate: 0,
    csfeRate: 0,
    csmeScore: 0,
    history: [],
    metrics: {
      calculationTimeMs: 0,
      updateFrequency: 0
    }
  });
  
  // Effect to update dashboard data
  useEffect(() => {
    // Function to update dashboard data
    const updateDashboardData = () => {
      if (!comphyonMeter) return;
      
      // Get current values
      const currentCph = comphyonMeter.getCurrentCph();
      const { csdeRate, csfeRate, csmeScore } = comphyonMeter.state;
      const metrics = comphyonMeter.getMetrics();
      
      // Get history
      const history = comphyonMeter.getHistory();
      
      // Create chart data
      const chartData = [];
      for (let i = 0; i < history.timestamps.length; i++) {
        chartData.push({
          time: new Date(history.timestamps[i]).toLocaleTimeString(),
          cph: history.cphValues[i],
          csdeRate: history.csdeRates[i],
          csfeRate: history.csfeRates[i],
          csmeScore: history.csmeScores[i] * 3 // Scale for visibility
        });
      }
      
      // Update state
      setDashboardData({
        currentCph,
        csdeRate,
        csfeRate,
        csmeScore,
        history: chartData,
        metrics
      });
    };
    
    // Update immediately
    updateDashboardData();
    
    // Set up interval for auto-refresh
    let intervalId;
    if (autoRefresh && comphyonMeter) {
      intervalId = setInterval(updateDashboardData, refreshInterval);
    }
    
    // Set up event listener for updates
    if (comphyonMeter) {
      comphyonMeter.on('update', updateDashboardData);
    }
    
    // Clean up
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
      if (comphyonMeter) {
        comphyonMeter.removeListener('update', updateDashboardData);
      }
    };
  }, [comphyonMeter, autoRefresh, refreshInterval]);
  
  // Additional metrics for the gauge
  const gaugeMetrics = {
    'CSDE Rate': `${dashboardData.csdeRate.toFixed(2)}/s`,
    'CSFE Rate': `${dashboardData.csfeRate.toFixed(2)}/s`,
    'CSME Score': dashboardData.csmeScore.toFixed(2),
    'Update Rate': `${dashboardData.metrics.updateFrequency.toFixed(2)}/s`
  };
  
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Comphyon Dashboard
      </Typography>
      <Typography variant="subtitle1" color="text.secondary" gutterBottom>
        Real-time measurement of emergent intelligence in the NovaFuse platform
      </Typography>
      
      <Grid container spacing={3}>
        {/* Comphyon Gauge */}
        <Grid item xs={12} md={4}>
          <ComphyonGauge 
            value={dashboardData.currentCph} 
            metrics={gaugeMetrics}
            size={220}
          />
        </Grid>
        
        {/* Comphyon History Chart */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader 
              title="Comphyon History" 
              subheader="Historical Comphyon values and contributing factors"
            />
            <CardContent>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={dashboardData.history}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="cph" 
                      name="Comphyon (Cph)" 
                      stroke={theme.palette.primary.main} 
                      strokeWidth={2}
                      dot={false}
                      activeDot={{ r: 8 }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="csdeRate" 
                      name="CSDE Rate (scaled)" 
                      stroke={theme.palette.success.main} 
                      strokeWidth={1}
                      dot={false}
                      strokeDasharray="5 5"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="csfeRate" 
                      name="CSFE Rate (scaled)" 
                      stroke={theme.palette.warning.main} 
                      strokeWidth={1}
                      dot={false}
                      strokeDasharray="5 5"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="csmeScore" 
                      name="CSME Score (scaled)" 
                      stroke={theme.palette.info.main} 
                      strokeWidth={1}
                      dot={false}
                      strokeDasharray="5 5"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Comphyon Formula Explanation */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Understanding Comphyons
            </Typography>
            <Typography variant="body1" paragraph>
              The Comphyon (Cph) is NovaFuse's unit of measure for emergent intelligence, where 1 Cph = 3,142 predictions/sec under a unified compliance-driven structure.
            </Typography>
            <Typography variant="body1" paragraph>
              The Comphyon value is calculated using the formula:
            </Typography>
            <Typography variant="body2" sx={{ fontFamily: 'monospace', my: 2, p: 2, bgcolor: 'grey.100' }}>
              Cph = ((csdeRate * csfeRate) × log(csmeScore)) / 3142
            </Typography>
            <Typography variant="body1">
              Where:
            </Typography>
            <ul>
              <li>
                <Typography variant="body2">
                  <strong>csdeRate</strong>: Predictions per second from the CSDE engine
                </Typography>
              </li>
              <li>
                <Typography variant="body2">
                  <strong>csfeRate</strong>: Predictions per second from the CSFE engine
                </Typography>
              </li>
              <li>
                <Typography variant="body2">
                  <strong>csmeScore</strong>: Ethical score from the CSME engine (0-1)
                </Typography>
              </li>
              <li>
                <Typography variant="body2">
                  <strong>3142</strong>: Normalization factor derived from π10³
                </Typography>
              </li>
            </ul>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ComphyonDashboard;
