/**
 * NovaNexxus Factory
 *
 * This module provides factory functions for creating NovaNexxus instances
 * with various configurations.
 */

const { NovaNexxus, NovaNexxusStatus } = require('./index');
const { NovaNexxusCSEDIntegration, CSEDOperationType, CSEDDomainType } = require('./csde-integration');

/**
 * Create a NovaNexxus instance
 * @param {Object} options - Configuration options
 * @returns {NovaNexxus} - The NovaNexxus instance
 */
function createNovaNexxus(options = {}) {
  return new NovaNexxus(options);
}

/**
 * Create a NovaNexxus instance and initialize it
 * @param {Object} options - Configuration options
 * @returns {Promise<NovaNexxus>} - A promise that resolves to the initialized NovaNexxus instance
 */
async function createAndInitializeNovaNexxus(options = {}) {
  const novanexxus = createNovaNexxus(options);
  await novanexxus.initialize();
  return novanexxus;
}

/**
 * Create a NovaNexxus instance with all available components
 * @param {Object} options - Configuration options
 * @returns {NovaNexxus} - The NovaNexxus instance
 */
function createFullNovaNexxus(options = {}) {
  // Import components
  const {
    TensorRuntime,
    EventProcessor,
    ControlSystem
  } = require('../novacore');

  const {
    BlockchainVerificationManager
  } = require('../novaproof');

  // Import NovaRollups
  const NovaRollups = require('../novarollups');

  // Create NovaCore components
  const tensorRuntime = new TensorRuntime({
    enableLogging: options.enableLogging
  });

  const eventProcessor = new EventProcessor({
    enableLogging: options.enableLogging,
    maxConcurrentEvents: 20
  });

  const controlSystem = new ControlSystem({
    enableLogging: options.enableLogging,
    maxConcurrentLoops: 10
  });

  // Create NovaProof components
  const blockchainManager = new BlockchainVerificationManager({
    enableLogging: options.enableLogging
  });

  // Create NovaRollups instance
  const novaRollups = new NovaRollups({
    enableLogging: options.enableLogging,
    enableMetrics: options.enableMetrics,
    maxBatchSize: options.maxBatchSize || 10000,
    targetLatency: options.targetLatency || 500
  });

  // Create NovaNexxus with all components
  return new NovaNexxus({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    enableMetrics: options.enableMetrics !== undefined ? options.enableMetrics : true,
    enableCaching: options.enableCaching !== undefined ? options.enableCaching : true,
    novaCore: {
      tensorRuntime,
      eventProcessor,
      controlSystem,
      processRequest: async (path, method, data, options) => {
        console.log(`Processing NovaCore request: ${method} ${path}`);

        // Handle different NovaCore requests
        if (path === '/tensor/process' && method === 'POST') {
          return await tensorRuntime.processTensor(data.tensor, data.operation, data.options);
        }

        throw new Error(`Unknown NovaCore request: ${method} ${path}`);
      }
    },
    novaProof: {
      blockchainManager,
      processRequest: async (path, method, data, options) => {
        console.log(`Processing NovaProof request: ${method} ${path}`);

        // Handle different NovaProof requests
        if (path === '/evidence/verify' && method === 'POST') {
          return await blockchainManager.verifyEvidence(data.evidence, data.options);
        }

        throw new Error(`Unknown NovaProof request: ${method} ${path}`);
      }
    },
    novaRollups: {
      instance: novaRollups,
      processRequest: async (path, method, data, options) => {
        console.log(`Processing NovaRollups request: ${method} ${path}`);

        // Handle different NovaRollups requests
        if (path === '/batch/create' && method === 'POST') {
          return await novaRollups.createBatch(data);
        } else if (path === '/proof/verify' && method === 'POST') {
          return await novaRollups.verifyProof(data);
        } else if (path === '/compliance/validate' && method === 'POST') {
          return await novaRollups.validateCompliance(data.data, data.regulation);
        }

        throw new Error(`Unknown NovaRollups request: ${method} ${path}`);
      }
    },
    novaConnect: options.novaConnect,
    novaVision: options.novaVision,
    csdeApiUrl: options.csdeApiUrl || process.env.CSDE_API_URL,
    csdeApiKey: options.csdeApiKey || process.env.CSDE_API_KEY,
    ...options
  });
}

/**
 * Create a NovaNexxus instance with all available components and initialize it
 * @param {Object} options - Configuration options
 * @returns {Promise<NovaNexxus>} - A promise that resolves to the initialized NovaNexxus instance
 */
async function createAndInitializeFullNovaNexxus(options = {}) {
  const novanexxus = createFullNovaNexxus(options);
  await novanexxus.initialize();
  return novanexxus;
}

module.exports = {
  createNovaNexxus,
  createAndInitializeNovaNexxus,
  createFullNovaNexxus,
  createAndInitializeFullNovaNexxus,
  NovaNexxusStatus,
  CSEDOperationType,
  CSEDDomainType
};
