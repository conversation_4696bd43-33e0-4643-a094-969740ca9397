/**
 * Analytics Summary Component
 * 
 * This component displays summary statistics for the analytics dashboard.
 */

import React from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Grid, 
  Typography 
} from '@mui/material';
import RequestsIcon from '@mui/icons-material/BarChart';
import TimeIcon from '@mui/icons-material/Timer';
import SuccessIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import EndpointIcon from '@mui/icons-material/Link';
import UserIcon from '@mui/icons-material/Person';

const SummaryCard = ({ title, value, icon, color }) => (
  <Card variant="outlined">
    <CardContent>
      <Grid container spacing={2} alignItems="center">
        <Grid item>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 48,
              height: 48,
              borderRadius: '50%',
              bgcolor: `${color}.light`,
              color: `${color}.main`
            }}
          >
            {icon}
          </Box>
        </Grid>
        <Grid item xs>
          <Typography variant="h5" component="div">
            {value}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            {title}
          </Typography>
        </Grid>
      </Grid>
    </CardContent>
  </Card>
);

const AnalyticsSummary = ({ summary }) => {
  return (
    <Grid container spacing={2}>
      <Grid item xs={12} sm={6} md={4} lg={2}>
        <SummaryCard
          title="Total Requests"
          value={summary.totalRequests.toLocaleString()}
          icon={<RequestsIcon />}
          color="primary"
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={4} lg={2}>
        <SummaryCard
          title="Avg. Response Time"
          value={`${Math.round(summary.averageResponseTime)}ms`}
          icon={<TimeIcon />}
          color="info"
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={4} lg={2}>
        <SummaryCard
          title="Success Rate"
          value={`${Math.round(summary.successRate)}%`}
          icon={<SuccessIcon />}
          color="success"
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={4} lg={2}>
        <SummaryCard
          title="Error Rate"
          value={`${Math.round(summary.errorRate)}%`}
          icon={<ErrorIcon />}
          color="error"
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={4} lg={2}>
        <SummaryCard
          title="Unique Endpoints"
          value={summary.uniqueEndpoints}
          icon={<EndpointIcon />}
          color="secondary"
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={4} lg={2}>
        <SummaryCard
          title="Unique Users"
          value={summary.uniqueUsers}
          icon={<UserIcon />}
          color="warning"
        />
      </Grid>
    </Grid>
  );
};

export default AnalyticsSummary;
