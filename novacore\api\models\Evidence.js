/**
 * NovaCore Evidence Model
 * 
 * This model defines the schema for evidence records in the NovaCore API.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define metadata schema
const metadataSchema = new Schema({
  source: { 
    type: String, 
    required: true, 
    trim: true 
  },
  collector: { 
    type: String, 
    trim: true 
  },
  collectionDate: { 
    type: Date, 
    default: Date.now 
  },
  expiryDate: { 
    type: Date 
  },
  tags: [{ 
    type: String, 
    trim: true 
  }],
  framework: { 
    type: String, 
    trim: true 
  },
  control: { 
    type: String, 
    trim: true 
  },
  additionalProperties: { 
    type: Map, 
    of: Schema.Types.Mixed 
  }
}, { _id: false });

// Define content schema
const contentSchema = new Schema({
  format: { 
    type: String, 
    required: true, 
    enum: ['json', 'text', 'binary', 'pdf', 'image', 'html', 'xml'], 
    default: 'json' 
  },
  mimeType: { 
    type: String, 
    trim: true 
  },
  data: { 
    type: Schema.Types.Mixed, 
    required: true 
  },
  size: { 
    type: Number 
  },
  hash: { 
    type: String, 
    trim: true 
  },
  encrypted: { 
    type: Boolean, 
    default: false 
  }
}, { _id: false });

// Define verification schema
const verificationSchema = new Schema({
  status: { 
    type: String, 
    enum: ['pending', 'verified', 'failed'], 
    default: 'pending' 
  },
  verifiedAt: { 
    type: Date 
  },
  verifiedBy: { 
    type: String, 
    trim: true 
  },
  method: { 
    type: String, 
    enum: ['manual', 'automated', 'blockchain'], 
    default: 'manual' 
  },
  blockchainVerificationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'BlockchainVerification' 
  },
  score: { 
    type: Number, 
    min: 0, 
    max: 100 
  },
  comments: { 
    type: String, 
    trim: true 
  }
}, { _id: false });

// Define evidence schema
const evidenceSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  type: { 
    type: String, 
    required: true, 
    trim: true 
  },
  category: { 
    type: String, 
    required: true, 
    trim: true 
  },
  status: { 
    type: String, 
    enum: ['draft', 'collected', 'validated', 'invalid', 'stored', 'verified', 'expired'], 
    default: 'draft' 
  },
  metadata: { 
    type: metadataSchema, 
    default: () => ({}) 
  },
  content: { 
    type: contentSchema, 
    required: true 
  },
  verification: { 
    type: verificationSchema, 
    default: () => ({}) 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
evidenceSchema.index({ type: 1 });
evidenceSchema.index({ category: 1 });
evidenceSchema.index({ status: 1 });
evidenceSchema.index({ 'metadata.tags': 1 });
evidenceSchema.index({ 'metadata.framework': 1, 'metadata.control': 1 });
evidenceSchema.index({ 'verification.status': 1 });
evidenceSchema.index({ createdAt: 1 });
evidenceSchema.index({ 'metadata.expiryDate': 1 });

// Add methods
evidenceSchema.methods.isExpired = function() {
  if (!this.metadata.expiryDate) {
    return false;
  }
  
  return new Date() > this.metadata.expiryDate;
};

evidenceSchema.methods.isVerified = function() {
  return this.verification.status === 'verified';
};

// Add statics
evidenceSchema.statics.findByFrameworkAndControl = function(framework, control) {
  return this.find({
    'metadata.framework': framework,
    'metadata.control': control
  });
};

evidenceSchema.statics.findByTags = function(tags) {
  return this.find({
    'metadata.tags': { $all: tags }
  });
};

// Create model
const Evidence = mongoose.model('Evidence', evidenceSchema);

module.exports = Evidence;
