/**
 * NovaCore Vendor Service
 * 
 * This service provides functionality for managing vendors.
 */

const { Vendor, Assessment, Document } = require('../models');
const logger = require('../../../config/logger');
const { ValidationError, NotFoundError } = require('../../../api/utils/errors');

class VendorService {
  /**
   * Create a new vendor
   * @param {Object} data - Vendor data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created vendor
   */
  async createVendor(data, userId) {
    try {
      logger.info('Creating new vendor', { organizationId: data.organizationId });
      
      // Set created by
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Create vendor
      const vendor = new Vendor(data);
      await vendor.save();
      
      logger.info('Vendor created successfully', { id: vendor._id });
      
      return vendor;
    } catch (error) {
      logger.error('Error creating vendor', { error });
      throw error;
    }
  }
  
  /**
   * Get all vendors for an organization
   * @param {string} organizationId - Organization ID
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Vendors with pagination info
   */
  async getAllVendors(organizationId, filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
      
      // Build query
      const query = { organizationId };
      
      // Apply filters
      if (filter.status) {
        query.status = filter.status;
      }
      
      if (filter.tier) {
        query.tier = filter.tier;
      }
      
      if (filter.tags) {
        query.tags = { $all: Array.isArray(filter.tags) ? filter.tags : [filter.tags] };
      }
      
      if (filter.cyberSafetyCertified) {
        query.cyberSafetyCertified = filter.cyberSafetyCertified === 'true';
      }
      
      if (filter.search) {
        query.$or = [
          { name: { $regex: filter.search, $options: 'i' } },
          { description: { $regex: filter.search, $options: 'i' } }
        ];
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [vendors, total] = await Promise.all([
        Vendor.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        Vendor.countDocuments(query)
      ]);
      
      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;
      
      return {
        data: vendors,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting vendors', { error });
      throw error;
    }
  }
  
  /**
   * Get vendor by ID
   * @param {string} id - Vendor ID
   * @returns {Promise<Object>} - Vendor
   */
  async getVendorById(id) {
    try {
      const vendor = await Vendor.findById(id);
      
      if (!vendor) {
        throw new NotFoundError(`Vendor with ID ${id} not found`);
      }
      
      return vendor;
    } catch (error) {
      logger.error('Error getting vendor by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Update vendor
   * @param {string} id - Vendor ID
   * @param {Object} data - Updated vendor data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated vendor
   */
  async updateVendor(id, data, userId) {
    try {
      // Get existing vendor
      const vendor = await this.getVendorById(id);
      
      // Set updated by
      data.updatedBy = userId;
      
      // Update vendor
      Object.assign(vendor, data);
      await vendor.save();
      
      logger.info('Vendor updated successfully', { id });
      
      return vendor;
    } catch (error) {
      logger.error('Error updating vendor', { id, error });
      throw error;
    }
  }
  
  /**
   * Delete vendor
   * @param {string} id - Vendor ID
   * @returns {Promise<boolean>} - Deletion success
   */
  async deleteVendor(id) {
    try {
      const result = await Vendor.findByIdAndDelete(id);
      
      if (!result) {
        throw new NotFoundError(`Vendor with ID ${id} not found`);
      }
      
      logger.info('Vendor deleted successfully', { id });
      
      return true;
    } catch (error) {
      logger.error('Error deleting vendor', { id, error });
      throw error;
    }
  }
  
  /**
   * Get vendor assessments
   * @param {string} id - Vendor ID
   * @returns {Promise<Array>} - Assessments
   */
  async getVendorAssessments(id) {
    try {
      // Get vendor
      await this.getVendorById(id);
      
      // Get assessments
      const assessments = await Assessment.findByVendor(id);
      
      return assessments;
    } catch (error) {
      logger.error('Error getting vendor assessments', { id, error });
      throw error;
    }
  }
  
  /**
   * Get vendor documents
   * @param {string} id - Vendor ID
   * @returns {Promise<Array>} - Documents
   */
  async getVendorDocuments(id) {
    try {
      // Get vendor
      await this.getVendorById(id);
      
      // Get documents
      const documents = await Document.findByVendor(id);
      
      return documents;
    } catch (error) {
      logger.error('Error getting vendor documents', { id, error });
      throw error;
    }
  }
  
  /**
   * Update vendor compliance status
   * @param {string} id - Vendor ID
   * @param {Object} data - Compliance status data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated vendor
   */
  async updateComplianceStatus(id, data, userId) {
    try {
      // Get vendor
      const vendor = await this.getVendorById(id);
      
      // Find existing compliance status
      const existingIndex = vendor.complianceStatus.findIndex(
        status => status.framework === data.framework
      );
      
      if (existingIndex >= 0) {
        // Update existing status
        vendor.complianceStatus[existingIndex] = {
          ...vendor.complianceStatus[existingIndex],
          ...data
        };
      } else {
        // Add new status
        vendor.complianceStatus.push(data);
      }
      
      // Set updated by
      vendor.updatedBy = userId;
      
      // Save vendor
      await vendor.save();
      
      logger.info('Vendor compliance status updated successfully', { id });
      
      return vendor;
    } catch (error) {
      logger.error('Error updating vendor compliance status', { id, error });
      throw error;
    }
  }
  
  /**
   * Update vendor risk score
   * @param {string} id - Vendor ID
   * @param {Object} data - Risk score data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated vendor
   */
  async updateRiskScore(id, data, userId) {
    try {
      // Get vendor
      const vendor = await this.getVendorById(id);
      
      // Update risk score
      vendor.riskScore = {
        ...vendor.riskScore,
        ...data,
        lastUpdated: new Date()
      };
      
      // Set updated by
      vendor.updatedBy = userId;
      
      // Save vendor
      await vendor.save();
      
      logger.info('Vendor risk score updated successfully', { id });
      
      return vendor;
    } catch (error) {
      logger.error('Error updating vendor risk score', { id, error });
      throw error;
    }
  }
  
  /**
   * Update vendor Cyber-Safety certification
   * @param {string} id - Vendor ID
   * @param {Object} data - Certification data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated vendor
   */
  async updateCyberSafetyCertification(id, data, userId) {
    try {
      // Get vendor
      const vendor = await this.getVendorById(id);
      
      // Update certification
      vendor.cyberSafetyCertified = data.certified;
      vendor.cyberSafetyScore = data.score;
      vendor.cyberSafetyCertificationDate = data.certificationDate || new Date();
      vendor.cyberSafetyCertificationExpiration = data.expirationDate;
      
      // Set updated by
      vendor.updatedBy = userId;
      
      // Save vendor
      await vendor.save();
      
      logger.info('Vendor Cyber-Safety certification updated successfully', { id });
      
      return vendor;
    } catch (error) {
      logger.error('Error updating vendor Cyber-Safety certification', { id, error });
      throw error;
    }
  }
  
  /**
   * Get vendor dashboard
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Object>} - Dashboard data
   */
  async getVendorDashboard(organizationId) {
    try {
      // Get all vendors
      const vendors = await Vendor.findByOrganization(organizationId);
      
      // Get all assessments
      const assessments = await Assessment.findByOrganization(organizationId);
      
      // Calculate statistics
      const totalVendors = vendors.length;
      const activeVendors = vendors.filter(vendor => vendor.status === 'active').length;
      const criticalVendors = vendors.filter(vendor => vendor.tier === 'critical').length;
      const certifiedVendors = vendors.filter(vendor => vendor.cyberSafetyCertified).length;
      
      const totalAssessments = assessments.length;
      const completedAssessments = assessments.filter(assessment => assessment.status === 'completed').length;
      const pendingAssessments = assessments.filter(assessment => assessment.status === 'in_progress' || assessment.status === 'pending_review').length;
      
      // Calculate average risk score
      const avgRiskScore = vendors.reduce((sum, vendor) => {
        return sum + (vendor.riskScore.overall || 0);
      }, 0) / (totalVendors || 1);
      
      // Calculate compliance status
      const complianceFrameworks = new Set();
      const complianceStatus = {};
      
      vendors.forEach(vendor => {
        vendor.complianceStatus.forEach(status => {
          complianceFrameworks.add(status.framework);
          
          if (!complianceStatus[status.framework]) {
            complianceStatus[status.framework] = {
              compliant: 0,
              nonCompliant: 0,
              inProgress: 0,
              notApplicable: 0,
              unknown: 0
            };
          }
          
          switch (status.status) {
            case 'compliant':
              complianceStatus[status.framework].compliant++;
              break;
            case 'non_compliant':
              complianceStatus[status.framework].nonCompliant++;
              break;
            case 'in_progress':
              complianceStatus[status.framework].inProgress++;
              break;
            case 'not_applicable':
              complianceStatus[status.framework].notApplicable++;
              break;
            default:
              complianceStatus[status.framework].unknown++;
          }
        });
      });
      
      return {
        vendorStats: {
          total: totalVendors,
          active: activeVendors,
          critical: criticalVendors,
          certified: certifiedVendors
        },
        assessmentStats: {
          total: totalAssessments,
          completed: completedAssessments,
          pending: pendingAssessments
        },
        riskStats: {
          avgRiskScore: Math.round(avgRiskScore),
          highRiskVendors: vendors.filter(vendor => (vendor.riskScore.overall || 0) > 75).length,
          mediumRiskVendors: vendors.filter(vendor => (vendor.riskScore.overall || 0) > 50 && (vendor.riskScore.overall || 0) <= 75).length,
          lowRiskVendors: vendors.filter(vendor => (vendor.riskScore.overall || 0) <= 50).length
        },
        complianceStats: {
          frameworks: Array.from(complianceFrameworks),
          status: complianceStatus
        }
      };
    } catch (error) {
      logger.error('Error getting vendor dashboard', { error });
      throw error;
    }
  }
}

module.exports = new VendorService();
