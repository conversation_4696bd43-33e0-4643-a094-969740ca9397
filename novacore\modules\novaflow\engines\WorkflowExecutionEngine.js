/**
 * NovaCore Workflow Execution Engine
 *
 * This engine is responsible for executing workflows and managing their state.
 * NovaFlow is the Universal Compliance Workflow Orchestrator (UCWO) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const { WorkflowExecution } = require('../models');
const TaskExecutionEngine = require('./TaskExecutionEngine');
const { VerificationCheckpointEngine } = require('./verification');
const logger = require('../../../config/logger');
const { ValidationError, NotFoundError } = require('../../../api/utils/errors');

class WorkflowExecutionEngine {
  /**
   * Initialize a new workflow execution
   * @param {Object} execution - Workflow execution object
   * @returns {Promise<Object>} - Updated execution
   */
  async initializeExecution(execution) {
    try {
      logger.info('Initializing workflow execution', { executionId: execution._id });

      // Set execution status to in_progress
      execution.status = 'in_progress';
      execution.startedAt = new Date();

      // Initialize first stage
      if (execution.stages.length > 0) {
        const firstStage = execution.stages[0];
        firstStage.status = 'in_progress';
        firstStage.startedAt = new Date();

        // Find tasks for first stage
        const stageTasks = execution.tasks.filter(task => task.stageId === firstStage.stageId);

        // Initialize tasks with no dependencies
        for (const task of stageTasks) {
          const taskDependencies = this._getTaskDependencies(task.taskId, execution);

          if (taskDependencies.length === 0) {
            task.status = 'pending';
          }
        }
      }

      // Save execution
      await execution.save();

      logger.info('Workflow execution initialized successfully', { executionId: execution._id });

      return execution;
    } catch (error) {
      logger.error('Error initializing workflow execution', { executionId: execution._id, error });
      throw error;
    }
  }

  /**
   * Process workflow execution
   * @param {string} executionId - Execution ID
   * @returns {Promise<Object>} - Updated execution
   */
  async processExecution(executionId) {
    try {
      logger.info('Processing workflow execution', { executionId });

      // Get execution
      const execution = await WorkflowExecution.findById(executionId);

      if (!execution) {
        throw new NotFoundError(`Workflow execution with ID ${executionId} not found`);
      }

      // Check if execution is in a processable state
      if (execution.status !== 'in_progress' && execution.status !== 'pending') {
        logger.info('Workflow execution is not in a processable state', {
          executionId,
          status: execution.status
        });

        return execution;
      }

      // Process pending tasks
      const pendingTasks = execution.tasks.filter(task => task.status === 'pending');

      for (const task of pendingTasks) {
        // Check if task dependencies are met
        const taskDependencies = this._getTaskDependencies(task.taskId, execution);
        const dependenciesMet = taskDependencies.every(depTask =>
          depTask.status === 'completed' || depTask.status === 'skipped'
        );

        if (dependenciesMet) {
          // Process task based on type
          await this._processTask(task, execution);
        }
      }

      // Check if current stage is complete
      const currentStage = execution.stages.find(stage => stage.stageId === execution.currentStageId);

      if (currentStage && currentStage.status === 'in_progress') {
        const stageTasks = execution.tasks.filter(task => task.stageId === currentStage.stageId);
        const stageComplete = this._isStageComplete(currentStage, stageTasks);

        if (stageComplete) {
          // Process verification checkpoints for the stage
          await this._processVerificationCheckpoints(currentStage, execution);

          // Complete current stage
          currentStage.status = 'completed';
          currentStage.completedAt = new Date();

          // Move to next stage
          const nextStage = this._getNextStage(currentStage.stageId, execution);

          if (nextStage) {
            // Initialize next stage
            nextStage.status = 'in_progress';
            nextStage.startedAt = new Date();
            execution.currentStageId = nextStage.stageId;

            // Initialize tasks with no dependencies
            const nextStageTasks = execution.tasks.filter(task => task.stageId === nextStage.stageId);

            for (const task of nextStageTasks) {
              const taskDependencies = this._getTaskDependencies(task.taskId, execution);

              if (taskDependencies.length === 0) {
                task.status = 'pending';
              }
            }
          } else {
            // All stages complete
            execution.status = 'completed';
            execution.completedAt = new Date();
          }
        }
      }

      // Update completion percentage
      execution.completionPercentage = execution.calculateCompletionPercentage();

      // Save execution
      await execution.save();

      logger.info('Workflow execution processed successfully', { executionId });

      return execution;
    } catch (error) {
      logger.error('Error processing workflow execution', { executionId, error });
      throw error;
    }
  }

  /**
   * Process verification checkpoints for a stage
   * @param {Object} stage - Stage object
   * @param {Object} execution - Execution object
   * @returns {Promise<void>}
   * @private
   */
  async _processVerificationCheckpoints(stage, execution) {
    try {
      logger.info('Processing verification checkpoints for stage', {
        executionId: execution._id,
        stageId: stage.stageId
      });

      // Check if execution has checkpoints
      if (!execution.checkpoints || execution.checkpoints.length === 0) {
        logger.info('No checkpoints found for execution', { executionId: execution._id });
        return;
      }

      // Get checkpoints for stage
      const stageCheckpoints = execution.checkpoints.filter(checkpoint =>
        checkpoint.stageId === stage.stageId && checkpoint.status === 'pending'
      );

      if (stageCheckpoints.length === 0) {
        logger.info('No pending checkpoints found for stage', {
          executionId: execution._id,
          stageId: stage.stageId
        });
        return;
      }

      // Process each checkpoint
      for (const checkpoint of stageCheckpoints) {
        try {
          // Process checkpoint
          await VerificationCheckpointEngine.processCheckpoint(
            checkpoint,
            execution,
            { userId: 'system' }
          );

          logger.info('Checkpoint processed successfully', {
            executionId: execution._id,
            checkpointId: checkpoint.id
          });
        } catch (error) {
          logger.error('Error processing checkpoint', {
            executionId: execution._id,
            checkpointId: checkpoint.id,
            error
          });

          // Update checkpoint status
          checkpoint.status = 'error';
          checkpoint.error = {
            message: error.message,
            code: error.code || 'CHECKPOINT_PROCESSING_ERROR',
            details: error.details
          };
        }
      }
    } catch (error) {
      logger.error('Error processing verification checkpoints', {
        executionId: execution._id,
        stageId: stage.stageId,
        error
      });

      throw error;
    }
  }

  /**
   * Complete task in workflow execution
   * @param {string} executionId - Execution ID
   * @param {string} taskId - Task ID
   * @param {Object} data - Task completion data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated execution
   */
  async completeTask(executionId, taskId, data, userId) {
    try {
      logger.info('Completing task in workflow execution', { executionId, taskId });

      // Get execution
      const execution = await WorkflowExecution.findById(executionId);

      if (!execution) {
        throw new NotFoundError(`Workflow execution with ID ${executionId} not found`);
      }

      // Find task
      const task = execution.tasks.find(t => t.taskId === taskId);

      if (!task) {
        throw new NotFoundError(`Task with ID ${taskId} not found in execution ${executionId}`);
      }

      // Validate task status
      if (task.status !== 'in_progress' && task.status !== 'pending') {
        throw new ValidationError(`Cannot complete task with status ${task.status}`);
      }

      // Update task
      task.status = 'completed';
      task.completedAt = new Date();
      task.result = data.result;
      task.output = data.output;
      task.notes = data.notes;
      task.executedBy = userId;

      // For approval tasks, add approval details
      if (task.type === 'approval') {
        task.approvalDetails = {
          approvedBy: userId,
          approvedAt: new Date(),
          comments: data.notes,
          evidence: data.evidence
        };
      }

      // Process dependent tasks
      const dependentTasks = this._getDependentTasks(taskId, execution);

      for (const depTask of dependentTasks) {
        // Check if all dependencies are met
        const taskDependencies = this._getTaskDependencies(depTask.taskId, execution);
        const dependenciesMet = taskDependencies.every(depTask =>
          depTask.status === 'completed' || depTask.status === 'skipped'
        );

        if (dependenciesMet) {
          depTask.status = 'pending';
        }
      }

      // Update completion percentage
      execution.completionPercentage = execution.calculateCompletionPercentage();

      // Save execution
      await execution.save();

      // Process execution to advance workflow
      await this.processExecution(executionId);

      logger.info('Task completed successfully', { executionId, taskId });

      return execution;
    } catch (error) {
      logger.error('Error completing task', { executionId, taskId, error });
      throw error;
    }
  }

  /**
   * Fail task in workflow execution
   * @param {string} executionId - Execution ID
   * @param {string} taskId - Task ID
   * @param {Object} data - Task failure data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated execution
   */
  async failTask(executionId, taskId, data, userId) {
    try {
      logger.info('Failing task in workflow execution', { executionId, taskId });

      // Get execution
      const execution = await WorkflowExecution.findById(executionId);

      if (!execution) {
        throw new NotFoundError(`Workflow execution with ID ${executionId} not found`);
      }

      // Find task
      const task = execution.tasks.find(t => t.taskId === taskId);

      if (!task) {
        throw new NotFoundError(`Task with ID ${taskId} not found in execution ${executionId}`);
      }

      // Validate task status
      if (task.status !== 'in_progress' && task.status !== 'pending') {
        throw new ValidationError(`Cannot fail task with status ${task.status}`);
      }

      // Update task
      task.status = 'failed';
      task.completedAt = new Date();
      task.error = {
        message: data.message,
        code: data.code,
        details: data.details
      };
      task.notes = data.notes;
      task.executedBy = userId;

      // Check if task is critical
      const isCritical = task.priority === 'critical';

      if (isCritical) {
        // Fail workflow execution
        execution.status = 'failed';
        execution.error = {
          message: `Critical task ${taskId} failed: ${data.message}`,
          code: data.code,
          details: data.details
        };
      }

      // Update completion percentage
      execution.completionPercentage = execution.calculateCompletionPercentage();

      // Save execution
      await execution.save();

      logger.info('Task failed', { executionId, taskId });

      return execution;
    } catch (error) {
      logger.error('Error failing task', { executionId, taskId, error });
      throw error;
    }
  }

  /**
   * Skip task in workflow execution
   * @param {string} executionId - Execution ID
   * @param {string} taskId - Task ID
   * @param {Object} data - Task skip data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated execution
   */
  async skipTask(executionId, taskId, data, userId) {
    try {
      logger.info('Skipping task in workflow execution', { executionId, taskId });

      // Get execution
      const execution = await WorkflowExecution.findById(executionId);

      if (!execution) {
        throw new NotFoundError(`Workflow execution with ID ${executionId} not found`);
      }

      // Find task
      const task = execution.tasks.find(t => t.taskId === taskId);

      if (!task) {
        throw new NotFoundError(`Task with ID ${taskId} not found in execution ${executionId}`);
      }

      // Validate task status
      if (task.status !== 'in_progress' && task.status !== 'pending') {
        throw new ValidationError(`Cannot skip task with status ${task.status}`);
      }

      // Update task
      task.status = 'skipped';
      task.completedAt = new Date();
      task.notes = data.notes;
      task.executedBy = userId;

      // Process dependent tasks
      const dependentTasks = this._getDependentTasks(taskId, execution);

      for (const depTask of dependentTasks) {
        // Check if all dependencies are met
        const taskDependencies = this._getTaskDependencies(depTask.taskId, execution);
        const dependenciesMet = taskDependencies.every(depTask =>
          depTask.status === 'completed' || depTask.status === 'skipped'
        );

        if (dependenciesMet) {
          depTask.status = 'pending';
        }
      }

      // Update completion percentage
      execution.completionPercentage = execution.calculateCompletionPercentage();

      // Save execution
      await execution.save();

      // Process execution to advance workflow
      await this.processExecution(executionId);

      logger.info('Task skipped successfully', { executionId, taskId });

      return execution;
    } catch (error) {
      logger.error('Error skipping task', { executionId, taskId, error });
      throw error;
    }
  }

  /**
   * Pause workflow execution
   * @param {string} executionId - Execution ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated execution
   */
  async pauseExecution(executionId, userId) {
    try {
      logger.info('Pausing workflow execution', { executionId });

      // Get execution
      const execution = await WorkflowExecution.findById(executionId);

      if (!execution) {
        throw new NotFoundError(`Workflow execution with ID ${executionId} not found`);
      }

      // Validate execution status
      if (execution.status !== 'in_progress' && execution.status !== 'pending') {
        throw new ValidationError(`Cannot pause execution with status ${execution.status}`);
      }

      // Update execution
      execution.status = 'paused';
      execution.updatedBy = userId;

      // Save execution
      await execution.save();

      logger.info('Workflow execution paused successfully', { executionId });

      return execution;
    } catch (error) {
      logger.error('Error pausing workflow execution', { executionId, error });
      throw error;
    }
  }

  /**
   * Resume workflow execution
   * @param {string} executionId - Execution ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated execution
   */
  async resumeExecution(executionId, userId) {
    try {
      logger.info('Resuming workflow execution', { executionId });

      // Get execution
      const execution = await WorkflowExecution.findById(executionId);

      if (!execution) {
        throw new NotFoundError(`Workflow execution with ID ${executionId} not found`);
      }

      // Validate execution status
      if (execution.status !== 'paused') {
        throw new ValidationError(`Cannot resume execution with status ${execution.status}`);
      }

      // Update execution
      execution.status = 'in_progress';
      execution.updatedBy = userId;

      // Save execution
      await execution.save();

      // Process execution to advance workflow
      await this.processExecution(executionId);

      logger.info('Workflow execution resumed successfully', { executionId });

      return execution;
    } catch (error) {
      logger.error('Error resuming workflow execution', { executionId, error });
      throw error;
    }
  }

  /**
   * Cancel workflow execution
   * @param {string} executionId - Execution ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated execution
   */
  async cancelExecution(executionId, userId) {
    try {
      logger.info('Cancelling workflow execution', { executionId });

      // Get execution
      const execution = await WorkflowExecution.findById(executionId);

      if (!execution) {
        throw new NotFoundError(`Workflow execution with ID ${executionId} not found`);
      }

      // Validate execution status
      if (execution.status === 'completed' || execution.status === 'cancelled') {
        throw new ValidationError(`Cannot cancel execution with status ${execution.status}`);
      }

      // Update execution
      execution.status = 'cancelled';
      execution.updatedBy = userId;

      // Save execution
      await execution.save();

      logger.info('Workflow execution cancelled successfully', { executionId });

      return execution;
    } catch (error) {
      logger.error('Error cancelling workflow execution', { executionId, error });
      throw error;
    }
  }

  /**
   * Process task
   * @param {Object} task - Task object
   * @param {Object} execution - Execution object
   * @returns {Promise<void>}
   * @private
   */
  async _processTask(task, execution) {
    // Set task to in_progress
    task.status = 'in_progress';
    task.startedAt = new Date();

    // Process based on task type
    if (task.type === 'automated') {
      try {
        // Process automated task
        const result = await TaskExecutionEngine.executeAutomatedTask(task, execution);

        // Update task
        task.status = 'completed';
        task.completedAt = new Date();
        task.result = result;
      } catch (error) {
        // Handle task failure
        task.status = 'failed';
        task.completedAt = new Date();
        task.error = {
          message: error.message,
          code: error.code || 'TASK_EXECUTION_ERROR',
          stack: error.stack,
          details: error.details
        };

        logger.error('Error executing automated task', {
          executionId: execution._id,
          taskId: task.taskId,
          error
        });
      }
    } else if (task.type === 'integration') {
      try {
        // Process integration task
        const result = await TaskExecutionEngine.executeIntegrationTask(task, execution);

        // Update task
        task.status = 'completed';
        task.completedAt = new Date();
        task.result = result;
      } catch (error) {
        // Handle task failure
        task.status = 'failed';
        task.completedAt = new Date();
        task.error = {
          message: error.message,
          code: error.code || 'TASK_EXECUTION_ERROR',
          stack: error.stack,
          details: error.details
        };

        logger.error('Error executing integration task', {
          executionId: execution._id,
          taskId: task.taskId,
          error
        });
      }
    } else if (task.type === 'decision') {
      try {
        // Process decision task
        const result = await TaskExecutionEngine.executeDecisionTask(task, execution);

        // Update task
        task.status = 'completed';
        task.completedAt = new Date();
        task.result = result;

        // Update decision details
        task.decisionDetails = {
          condition: result.condition,
          evaluationResult: result.evaluationResult,
          nextTaskId: result.nextTaskId
        };
      } catch (error) {
        // Handle task failure
        task.status = 'failed';
        task.completedAt = new Date();
        task.error = {
          message: error.message,
          code: error.code || 'TASK_EXECUTION_ERROR',
          stack: error.stack,
          details: error.details
        };

        logger.error('Error executing decision task', {
          executionId: execution._id,
          taskId: task.taskId,
          error
        });
      }
    } else if (task.type === 'notification') {
      try {
        // Process notification task
        const result = await TaskExecutionEngine.executeNotificationTask(task, execution);

        // Update task
        task.status = 'completed';
        task.completedAt = new Date();
        task.result = result;
      } catch (error) {
        // Handle task failure
        task.status = 'failed';
        task.completedAt = new Date();
        task.error = {
          message: error.message,
          code: error.code || 'TASK_EXECUTION_ERROR',
          stack: error.stack,
          details: error.details
        };

        logger.error('Error executing notification task', {
          executionId: execution._id,
          taskId: task.taskId,
          error
        });
      }
    } else if (task.type === 'evidence_collection') {
      try {
        // Process evidence collection task
        const result = await TaskExecutionEngine.executeEvidenceCollectionTask(task, execution);

        // Update task
        task.status = 'completed';
        task.completedAt = new Date();
        task.result = result;

        // Update evidence details
        task.evidenceDetails = {
          evidenceId: result.evidenceId,
          controlId: result.controlId,
          verificationStatus: result.verificationStatus,
          verificationId: result.verificationId
        };
      } catch (error) {
        // Handle task failure
        task.status = 'failed';
        task.completedAt = new Date();
        task.error = {
          message: error.message,
          code: error.code || 'TASK_EXECUTION_ERROR',
          stack: error.stack,
          details: error.details
        };

        logger.error('Error executing evidence collection task', {
          executionId: execution._id,
          taskId: task.taskId,
          error
        });
      }
    }
    // For manual, approval, and other task types, leave as in_progress
    // They will be completed via the completeTask method
  }

  /**
   * Get task dependencies
   * @param {string} taskId - Task ID
   * @param {Object} execution - Execution object
   * @returns {Array} - Task dependencies
   * @private
   */
  _getTaskDependencies(taskId, execution) {
    const task = execution.tasks.find(t => t.taskId === taskId);

    if (!task || !task.dependencies || task.dependencies.length === 0) {
      return [];
    }

    return execution.tasks.filter(t => task.dependencies.includes(t.taskId));
  }

  /**
   * Get dependent tasks
   * @param {string} taskId - Task ID
   * @param {Object} execution - Execution object
   * @returns {Array} - Dependent tasks
   * @private
   */
  _getDependentTasks(taskId, execution) {
    return execution.tasks.filter(task =>
      task.dependencies && task.dependencies.includes(taskId)
    );
  }

  /**
   * Check if stage is complete
   * @param {Object} stage - Stage object
   * @param {Array} tasks - Stage tasks
   * @returns {boolean} - Whether stage is complete
   * @private
   */
  _isStageComplete(stage, tasks) {
    if (stage.completionCriteria === 'all_tasks') {
      // All tasks must be completed or skipped
      return tasks.every(task =>
        task.status === 'completed' || task.status === 'skipped'
      );
    } else if (stage.completionCriteria === 'any_task') {
      // At least one task must be completed
      return tasks.some(task => task.status === 'completed');
    } else if (stage.completionCriteria === 'specific_tasks') {
      // Specific tasks must be completed
      const requiredTaskIds = stage.completionConfig.taskIds || [];

      if (requiredTaskIds.length === 0) {
        return false;
      }

      const requiredTasks = tasks.filter(task =>
        requiredTaskIds.includes(task.taskId)
      );

      return requiredTasks.every(task =>
        task.status === 'completed' || task.status === 'skipped'
      );
    } else if (stage.completionCriteria === 'percentage') {
      // Percentage of tasks must be completed
      const percentage = stage.completionConfig.percentage || 100;
      const completedTasks = tasks.filter(task =>
        task.status === 'completed' || task.status === 'skipped'
      ).length;

      return (completedTasks / tasks.length) * 100 >= percentage;
    }

    // Default to all tasks
    return tasks.every(task =>
      task.status === 'completed' || task.status === 'skipped'
    );
  }

  /**
   * Get next stage
   * @param {string} currentStageId - Current stage ID
   * @param {Object} execution - Execution object
   * @returns {Object} - Next stage
   * @private
   */
  _getNextStage(currentStageId, execution) {
    const currentStageIndex = execution.stages.findIndex(stage =>
      stage.stageId === currentStageId
    );

    if (currentStageIndex === -1 || currentStageIndex >= execution.stages.length - 1) {
      return null;
    }

    return execution.stages[currentStageIndex + 1];
  }
}

module.exports = new WorkflowExecutionEngine();
