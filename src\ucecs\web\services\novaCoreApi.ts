/**
 * NovaCore API Service
 * 
 * This service provides integration with the NovaCore API for NovaSphere.
 */

import axios from 'axios';

// Create axios instance for NovaCore API
const novaCoreApi = axios.create({
  baseURL: process.env.NEXT_PUBLIC_NOVACORE_API_URL || 'http://localhost:5000/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Evidence API

/**
 * Get all evidence with optional filtering
 */
export const getEvidence = async (query = {}, page = 1, pageSize = 10) => {
  const response = await novaCoreApi.get('/evidence', {
    params: {
      ...query,
      page,
      pageSize,
    },
  });
  return response.data;
};

/**
 * Get evidence by ID
 */
export const getEvidenceById = async (id: string) => {
  const response = await novaCoreApi.get(`/evidence/${id}`);
  return response.data;
};

/**
 * Create new evidence
 */
export const createEvidence = async (evidence: any) => {
  const response = await novaCoreApi.post('/evidence', evidence);
  return response.data;
};

/**
 * Update evidence
 */
export const updateEvidence = async (id: string, updates: any) => {
  const response = await novaCoreApi.put(`/evidence/${id}`, updates);
  return response.data;
};

/**
 * Delete evidence
 */
export const deleteEvidence = async (id: string) => {
  const response = await novaCoreApi.delete(`/evidence/${id}`);
  return response.data;
};

/**
 * Create new version of evidence
 */
export const createEvidenceVersion = async (id: string, version: any) => {
  const response = await novaCoreApi.post(`/evidence/${id}/versions`, version);
  return response.data;
};

/**
 * Get all versions of evidence
 */
export const getEvidenceVersions = async (id: string) => {
  const response = await novaCoreApi.get(`/evidence/${id}/versions`);
  return response.data;
};

/**
 * Update evidence status
 */
export const updateEvidenceStatus = async (id: string, status: string) => {
  const response = await novaCoreApi.put(`/evidence/${id}/status`, { status });
  return response.data;
};

/**
 * Verify evidence on blockchain
 */
export const verifyEvidence = async (id: string) => {
  const response = await novaCoreApi.post(`/evidence/${id}/verify`);
  return response.data;
};

// Connector API

/**
 * Get all connectors with optional filtering
 */
export const getConnectors = async (query = {}, page = 1, pageSize = 10) => {
  const response = await novaCoreApi.get('/connectors', {
    params: {
      ...query,
      page,
      pageSize,
    },
  });
  return response.data;
};

/**
 * Get connector by ID
 */
export const getConnectorById = async (id: string) => {
  const response = await novaCoreApi.get(`/connectors/${id}`);
  return response.data;
};

/**
 * Create new connector
 */
export const createConnector = async (connector: any) => {
  const response = await novaCoreApi.post('/connectors', connector);
  return response.data;
};

/**
 * Update connector
 */
export const updateConnector = async (id: string, updates: any) => {
  const response = await novaCoreApi.put(`/connectors/${id}`, updates);
  return response.data;
};

/**
 * Delete connector
 */
export const deleteConnector = async (id: string) => {
  const response = await novaCoreApi.delete(`/connectors/${id}`);
  return response.data;
};

/**
 * Activate connector
 */
export const activateConnector = async (id: string) => {
  const response = await novaCoreApi.post(`/connectors/${id}/activate`);
  return response.data;
};

/**
 * Deactivate connector
 */
export const deactivateConnector = async (id: string) => {
  const response = await novaCoreApi.post(`/connectors/${id}/deactivate`);
  return response.data;
};

/**
 * Test connector connection
 */
export const testConnector = async (id: string) => {
  const response = await novaCoreApi.post(`/connectors/${id}/test`);
  return response.data;
};

/**
 * Create collection job for connector
 */
export const createCollectionJob = async (id: string, job: any) => {
  const response = await novaCoreApi.post(`/connectors/${id}/jobs`, job);
  return response.data;
};

/**
 * Schedule collection for connector
 */
export const scheduleCollection = async (id: string, schedule: any) => {
  const response = await novaCoreApi.post(`/connectors/${id}/schedule`, { schedule });
  return response.data;
};

// Job API

/**
 * Get all jobs with optional filtering
 */
export const getJobs = async (query = {}, page = 1, pageSize = 10) => {
  const response = await novaCoreApi.get('/jobs', {
    params: {
      ...query,
      page,
      pageSize,
    },
  });
  return response.data;
};

/**
 * Get job by ID
 */
export const getJobById = async (id: string) => {
  const response = await novaCoreApi.get(`/jobs/${id}`);
  return response.data;
};

/**
 * Execute job
 */
export const executeJob = async (id: string) => {
  const response = await novaCoreApi.post(`/jobs/${id}/execute`);
  return response.data;
};

/**
 * Cancel job
 */
export const cancelJob = async (id: string) => {
  const response = await novaCoreApi.post(`/jobs/${id}/cancel`);
  return response.data;
};

export default novaCoreApi;
