/**
 * Create Control Test Page
 * 
 * This page provides a form for creating a new control test.
 */

import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { useAuth } from '../../../contexts/AuthContext';
import ProtectedRoute from '../../../components/ProtectedRoute';
import useNovaAssureApi from '../../../hooks/useNovaAssureApi';
import ErrorDisplay from '../../../components/ui/error-display';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { ArrowLeft, Save } from 'lucide-react';

export default function CreateControlTestPage() {
  return (
    <ProtectedRoute>
      <CreateControlTestContent />
    </ProtectedRoute>
  );
}

function CreateControlTestContent() {
  const router = useRouter();
  const { api, callApi } = useNovaAssureApi();
  const [error, setError] = useState<Error | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    frameworkId: '',
    controlId: '',
    type: 'manual',
    frequency: 'monthly',
    riskLevel: 'medium',
    tags: '',
    status: 'draft'
  });
  
  // Mock frameworks and controls for the form
  const frameworks = [
    { id: 'framework1', name: 'SOC 2' },
    { id: 'framework2', name: 'HIPAA' },
    { id: 'framework3', name: 'GDPR' },
    { id: 'framework4', name: 'ISO 27001' }
  ];
  
  const controls = [
    { id: 'control1', name: 'Access Control', frameworkId: 'framework1' },
    { id: 'control2', name: 'System Operations', frameworkId: 'framework1' },
    { id: 'control3', name: 'Risk Management', frameworkId: 'framework1' },
    { id: 'control4', name: 'Privacy Notices', frameworkId: 'framework2' },
    { id: 'control5', name: 'Data Protection', frameworkId: 'framework3' },
    { id: 'control6', name: 'Information Security', frameworkId: 'framework4' }
  ];
  
  // Filter controls based on selected framework
  const filteredControls = controls.filter(
    control => !formData.frameworkId || control.frameworkId === formData.frameworkId
  );
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Reset control if framework changes
    if (name === 'frameworkId') {
      setFormData(prev => ({ ...prev, controlId: '' }));
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    
    try {
      // Process tags from comma-separated string to array
      const tags = formData.tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag !== '');
      
      // Prepare data for API
      const testData = {
        name: formData.name,
        description: formData.description,
        frameworkId: formData.frameworkId,
        controlId: formData.controlId,
        type: formData.type,
        frequency: formData.frequency,
        riskLevel: formData.riskLevel,
        tags,
        status: formData.status,
        testSteps: [],
        evidenceRequirements: [],
        automationConfig: formData.type !== 'manual' ? {
          platform: 'generic',
          scriptType: 'javascript',
          scriptContent: '',
          parameters: {},
          timeout: 300,
          retryCount: 3,
          retryDelay: 5,
          successCriteria: '',
          failureCriteria: ''
        } : null
      };
      
      // Call API to create test
      const createdTest = await callApi(() => api.createControlTest(testData));
      
      // Redirect to the new test page
      router.push(`/novaassure/tests/${createdTest._id}`);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to create test'));
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleDismissError = () => {
    setError(null);
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button 
            variant="outline" 
            size="sm" 
            className="mr-4"
            onClick={() => router.push('/novaassure')}
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">Create Control Test</h1>
        </div>
      </div>
      
      {error && (
        <div className="mb-6">
          <ErrorDisplay 
            message="Error creating test" 
            details={error.message}
            severity="error"
            onDismiss={handleDismissError}
          />
        </div>
      )}
      
      <Card>
        <CardHeader>
          <CardTitle>Test Information</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} id="create-test-form">
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Test Name*
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="draft">Draft</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  rows={3}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Framework*
                  </label>
                  <select
                    name="frameworkId"
                    value={formData.frameworkId}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  >
                    <option value="">Select Framework</option>
                    {frameworks.map(framework => (
                      <option key={framework.id} value={framework.id}>
                        {framework.name}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Control*
                  </label>
                  <select
                    name="controlId"
                    value={formData.controlId}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                    disabled={!formData.frameworkId}
                  >
                    <option value="">Select Control</option>
                    {filteredControls.map(control => (
                      <option key={control.id} value={control.id}>
                        {control.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Test Type
                  </label>
                  <select
                    name="type"
                    value={formData.type}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="manual">Manual</option>
                    <option value="automated">Automated</option>
                    <option value="hybrid">Hybrid</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Frequency
                  </label>
                  <select
                    name="frequency"
                    value={formData.frequency}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                    <option value="quarterly">Quarterly</option>
                    <option value="annually">Annually</option>
                    <option value="continuous">Continuous</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Risk Level
                  </label>
                  <select
                    name="riskLevel"
                    value={formData.riskLevel}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="critical">Critical</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tags (comma separated)
                </label>
                <input
                  type="text"
                  name="tags"
                  value={formData.tags}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="e.g. access, security, quarterly"
                />
              </div>
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button 
            variant="outline" 
            onClick={() => router.push('/novaassure')}
          >
            Cancel
          </Button>
          <Button 
            type="submit"
            form="create-test-form"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>Creating...</>
            ) : (
              <>
                <Save className="h-4 w-4 mr-1" />
                Create Test
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
      
      <div className="mt-6 text-center text-sm text-gray-500">
        <p>After creating the test, you'll be able to add test steps, evidence requirements, and automation configuration.</p>
      </div>
    </div>
  );
}
