<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Finite Universe Principle - Analytics Dashboard</title>
  
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
  
  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  
  <!-- Socket.IO -->
  <script src="https://cdn.jsdelivr.net/npm/socket.io-client@4.6.1/dist/socket.io.min.js"></script>
  
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f8f9fa;
      padding-top: 20px;
    }
    
    .card {
      margin-bottom: 20px;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .card-header {
      border-radius: 10px 10px 0 0;
      font-weight: 600;
    }
    
    .chart-container {
      position: relative;
      height: 300px;
      width: 100%;
    }
    
    .correlation-matrix {
      width: 100%;
      height: 300px;
      overflow: auto;
    }
    
    .correlation-cell {
      text-align: center;
      padding: 8px;
    }
    
    .correlation-high {
      background-color: rgba(40, 167, 69, 0.2);
    }
    
    .correlation-medium {
      background-color: rgba(255, 193, 7, 0.2);
    }
    
    .correlation-low {
      background-color: rgba(220, 53, 69, 0.2);
    }
    
    .pattern-item {
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 5px;
      background-color: #f8f9fa;
      border-left: 4px solid #0d6efd;
    }
    
    .anomaly-item {
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 5px;
    }
    
    .anomaly-critical {
      background-color: rgba(220, 53, 69, 0.1);
      border-left: 4px solid #dc3545;
    }
    
    .anomaly-high {
      background-color: rgba(253, 126, 20, 0.1);
      border-left: 4px solid #fd7e14;
    }
    
    .anomaly-medium {
      background-color: rgba(255, 193, 7, 0.1);
      border-left: 4px solid #ffc107;
    }
    
    .anomaly-low {
      background-color: rgba(13, 202, 240, 0.1);
      border-left: 4px solid #0dcaf0;
    }
    
    .nav-tabs .nav-link {
      color: #495057;
    }
    
    .nav-tabs .nav-link.active {
      font-weight: 600;
    }
    
    .badge-trend-positive {
      background-color: #198754;
    }
    
    .badge-trend-negative {
      background-color: #dc3545;
    }
    
    .badge-trend-neutral {
      background-color: #6c757d;
    }
  </style>
</head>
<body>
  <div class="container-fluid">
    <div class="row mb-4">
      <div class="col-md-12">
        <h1 class="display-5 mb-0">
          <i class="bi bi-graph-up-arrow me-2"></i>
          Finite Universe Principle - Analytics Dashboard
        </h1>
        <p class="text-muted">Advanced analytics and insights for boundary enforcement</p>
      </div>
    </div>
    
    <!-- Overview Section -->
    <div class="row mb-3">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">
              <i class="bi bi-speedometer2 me-2"></i>
              Overview
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3">
                <div class="card h-100">
                  <div class="card-body text-center">
                    <h6 class="card-title text-muted">Boundary Violations</h6>
                    <h2 id="boundary-violations-count">0</h2>
                    <span id="boundary-violations-trend" class="badge rounded-pill badge-trend-neutral">0%</span>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card h-100">
                  <div class="card-body text-center">
                    <h6 class="card-title text-muted">Validation Failures</h6>
                    <h2 id="validation-failures-count">0</h2>
                    <span id="validation-failures-trend" class="badge rounded-pill badge-trend-neutral">0%</span>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card h-100">
                  <div class="card-body text-center">
                    <h6 class="card-title text-muted">Detected Anomalies</h6>
                    <h2 id="anomalies-count">0</h2>
                    <span id="anomalies-trend" class="badge rounded-pill badge-trend-neutral">0%</span>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card h-100">
                  <div class="card-body text-center">
                    <h6 class="card-title text-muted">Detected Patterns</h6>
                    <h2 id="patterns-count">0</h2>
                    <span id="patterns-trend" class="badge rounded-pill badge-trend-neutral">0%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Main Content -->
    <div class="row">
      <div class="col-md-12">
        <ul class="nav nav-tabs" id="analyticsTabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link active" id="trends-tab" data-bs-toggle="tab" data-bs-target="#trends" type="button" role="tab">
              <i class="bi bi-graph-up me-1"></i> Trends
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="correlations-tab" data-bs-toggle="tab" data-bs-target="#correlations" type="button" role="tab">
              <i class="bi bi-diagram-3 me-1"></i> Correlations
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="patterns-tab" data-bs-toggle="tab" data-bs-target="#patterns" type="button" role="tab">
              <i class="bi bi-repeat me-1"></i> Patterns
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="anomalies-tab" data-bs-toggle="tab" data-bs-target="#anomalies" type="button" role="tab">
              <i class="bi bi-exclamation-triangle me-1"></i> Anomalies
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="forecasts-tab" data-bs-toggle="tab" data-bs-target="#forecasts" type="button" role="tab">
              <i class="bi bi-calendar-check me-1"></i> Forecasts
            </button>
          </li>
        </ul>
        
        <div class="tab-content mt-3" id="analyticsTabContent">
          <!-- Trends Tab -->
          <div class="tab-pane fade show active" id="trends" role="tabpanel">
            <div class="row">
              <div class="col-md-8">
                <div class="card">
                  <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Trend Analysis</h5>
                  </div>
                  <div class="card-body">
                    <div class="chart-container">
                      <canvas id="trends-chart"></canvas>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="card">
                  <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Trend Metrics</h5>
                  </div>
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-sm">
                        <thead>
                          <tr>
                            <th>Metric</th>
                            <th>Trend</th>
                            <th>Seasonality</th>
                          </tr>
                        </thead>
                        <tbody id="trend-metrics-table">
                          <!-- Trend metrics will be added here dynamically -->
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Correlations Tab -->
          <div class="tab-pane fade" id="correlations" role="tabpanel">
            <div class="row">
              <div class="col-md-8">
                <div class="card">
                  <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Correlation Matrix</h5>
                  </div>
                  <div class="card-body">
                    <div class="correlation-matrix">
                      <table class="table table-bordered" id="correlation-matrix-table">
                        <!-- Correlation matrix will be added here dynamically -->
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="card">
                  <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Significant Correlations</h5>
                  </div>
                  <div class="card-body">
                    <div id="significant-correlations">
                      <!-- Significant correlations will be added here dynamically -->
                      <p class="text-muted">No significant correlations detected</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Patterns Tab -->
          <div class="tab-pane fade" id="patterns" role="tabpanel">
            <div class="row">
              <div class="col-md-8">
                <div class="card">
                  <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Pattern Visualization</h5>
                  </div>
                  <div class="card-body">
                    <div class="chart-container">
                      <canvas id="patterns-chart"></canvas>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="card">
                  <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Detected Patterns</h5>
                  </div>
                  <div class="card-body">
                    <div id="detected-patterns">
                      <!-- Detected patterns will be added here dynamically -->
                      <p class="text-muted">No patterns detected</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Anomalies Tab -->
          <div class="tab-pane fade" id="anomalies" role="tabpanel">
            <div class="row">
              <div class="col-md-8">
                <div class="card">
                  <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Anomaly Classification</h5>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-6">
                        <h6>By Category</h6>
                        <div class="chart-container" style="height: 200px;">
                          <canvas id="anomalies-category-chart"></canvas>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <h6>By Severity</h6>
                        <div class="chart-container" style="height: 200px;">
                          <canvas id="anomalies-severity-chart"></canvas>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="card">
                  <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Recent Anomalies</h5>
                  </div>
                  <div class="card-body">
                    <div id="recent-anomalies">
                      <!-- Recent anomalies will be added here dynamically -->
                      <p class="text-muted">No recent anomalies</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Forecasts Tab -->
          <div class="tab-pane fade" id="forecasts" role="tabpanel">
            <div class="row">
              <div class="col-md-8">
                <div class="card">
                  <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Forecast Visualization</h5>
                  </div>
                  <div class="card-body">
                    <div class="chart-container">
                      <canvas id="forecasts-chart"></canvas>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="card">
                  <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Forecast Metrics</h5>
                  </div>
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-sm">
                        <thead>
                          <tr>
                            <th>Metric</th>
                            <th>Current</th>
                            <th>Forecast</th>
                            <th>Change</th>
                          </tr>
                        </thead>
                        <tbody id="forecast-metrics-table">
                          <!-- Forecast metrics will be added here dynamically -->
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  
  <!-- Analytics Dashboard JS -->
  <script src="analytics.js"></script>
</body>
</html>
