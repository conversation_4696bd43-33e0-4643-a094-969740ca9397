"""
Assessment model for the Universal Vendor Risk Management System.

This module provides the Assessment class for representing vendor assessments.
"""

import uuid
import datetime
from typing import Dict, List, Any, Optional


class Assessment:
    """
    Assessment class for representing vendor assessments.

    This class represents a vendor assessment in the Universal Vendor Risk Management System.
    """

    def __init__(self,
                vendor_id: str,
                assessment_type: str,
                framework: str,
                due_date: str,
                questions: List[Dict[str, Any]],
                status: str = 'pending',
                responses: Optional[List[Dict[str, Any]]] = None,
                score: Optional[float] = None,
                findings: Optional[List[Dict[str, Any]]] = None,
                metadata: Optional[Dict[str, Any]] = None,
                assessment_id: Optional[str] = None,
                created_at: Optional[str] = None,
                updated_at: Optional[str] = None,
                completed_at: Optional[str] = None):
        """
        Initialize an Assessment.

        Args:
            vendor_id: The ID of the vendor being assessed
            assessment_type: The type of assessment (e.g., 'security', 'privacy', 'compliance')
            framework: The compliance framework (e.g., 'SOC 2', 'GDPR', 'HIPAA')
            due_date: The due date for the assessment
            questions: The assessment questions
            status: The status of the assessment (e.g., 'pending', 'in_progress', 'completed')
            responses: The responses to the assessment questions
            score: The assessment score
            findings: The assessment findings
            metadata: Additional metadata about the assessment
            assessment_id: The ID of the assessment (generated if not provided)
            created_at: The creation timestamp (generated if not provided)
            updated_at: The last update timestamp (generated if not provided)
            completed_at: The completion timestamp
        """
        self.vendor_id = vendor_id
        self.assessment_type = assessment_type
        self.framework = framework
        self.due_date = due_date
        self.questions = questions
        self.status = status
        self.responses = responses or []
        self.score = score
        self.findings = findings or []
        self.metadata = metadata or {}
        self.assessment_id = assessment_id or str(uuid.uuid4())
        self.created_at = created_at or datetime.datetime.now().isoformat()
        self.updated_at = updated_at or self.created_at
        self.completed_at = completed_at

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Assessment':
        """
        Create an Assessment from a dictionary.

        Args:
            data: Dictionary containing assessment data

        Returns:
            An Assessment instance
        """
        return cls(
            vendor_id=data.get('vendor_id', ''),
            assessment_type=data.get('assessment_type', ''),
            framework=data.get('framework', ''),
            due_date=data.get('due_date', ''),
            questions=data.get('questions', []),
            status=data.get('status', 'pending'),
            responses=data.get('responses', []),
            score=data.get('score', None),
            findings=data.get('findings', []),
            metadata=data.get('metadata', {}),
            assessment_id=data.get('assessment_id', None),
            created_at=data.get('created_at', None),
            updated_at=data.get('updated_at', None),
            completed_at=data.get('completed_at', None)
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the Assessment to a dictionary.

        Returns:
            Dictionary representation of the Assessment
        """
        return {
            'assessment_id': self.assessment_id,
            'vendor_id': self.vendor_id,
            'assessment_type': self.assessment_type,
            'framework': self.framework,
            'due_date': self.due_date,
            'questions': self.questions,
            'status': self.status,
            'responses': self.responses,
            'score': self.score,
            'findings': self.findings,
            'metadata': self.metadata,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'completed_at': self.completed_at
        }

    def update_status(self, status: str) -> None:
        """
        Update the status of the assessment.

        Args:
            status: The new status
        """
        self.status = status
        self.updated_at = datetime.datetime.now().isoformat()

        if status == 'completed' and not self.completed_at:
            self.completed_at = self.updated_at

    def add_response(self, question_id: str, response: Dict[str, Any]) -> None:
        """
        Add a response to the assessment.

        Args:
            question_id: The ID of the question
            response: The response data
        """
        # Check if the question exists
        question_exists = any(q.get('id') == question_id for q in self.questions)
        if not question_exists:
            raise ValueError(f"Question with ID {question_id} does not exist")

        # Check if a response already exists for this question
        existing_response_index = next((i for i, r in enumerate(self.responses)
                                      if r.get('question_id') == question_id), None)

        # Add or update the response
        response_data = {
            'question_id': question_id,
            'response': response.get('response', ''),
            'notes': response.get('notes', ''),
            'attachments': response.get('attachments', []),
            'timestamp': datetime.datetime.now().isoformat()
        }

        if existing_response_index is not None:
            self.responses[existing_response_index] = response_data
        else:
            self.responses.append(response_data)

        self.updated_at = datetime.datetime.now().isoformat()

        # Update status if all questions have responses
        if len(self.responses) == len(self.questions) and self.status == 'in_progress':
            self.update_status('completed')

    def calculate_score(self) -> float:
        """
        Calculate the assessment score based on responses.

        Returns:
            The calculated score
        """
        if not self.responses:
            return 0.0

        # In a real implementation, this would calculate a score based on the responses
        # For now, return a placeholder score
        total_questions = len(self.questions)
        answered_questions = len(self.responses)

        if total_questions == 0:
            return 0.0

        # Simple scoring: percentage of questions answered
        score = (answered_questions / total_questions) * 100.0

        self.score = score
        self.updated_at = datetime.datetime.now().isoformat()

        return score

    def add_finding(self, finding: Dict[str, Any]) -> None:
        """
        Add a finding to the assessment.

        Args:
            finding: The finding data
        """
        finding_data = {
            'id': finding.get('id', str(uuid.uuid4())),
            'title': finding.get('title', ''),
            'description': finding.get('description', ''),
            'severity': finding.get('severity', 'low'),
            'status': finding.get('status', 'open'),
            'remediation': finding.get('remediation', ''),
            'timestamp': datetime.datetime.now().isoformat()
        }

        self.findings.append(finding_data)
        self.updated_at = datetime.datetime.now().isoformat()

    def is_overdue(self) -> bool:
        """
        Check if the assessment is overdue.

        Returns:
            True if the assessment is overdue, False otherwise
        """
        if self.status == 'completed':
            return False

        due_date = datetime.datetime.fromisoformat(self.due_date)
        current_date = datetime.datetime.now()

        return current_date > due_date

    def get_completion_percentage(self) -> float:
        """
        Get the completion percentage of the assessment.

        Returns:
            The completion percentage
        """
        if not self.questions:
            return 0.0

        total_questions = len(self.questions)
        answered_questions = len(self.responses)

        return (answered_questions / total_questions) * 100.0