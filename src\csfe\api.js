/**
 * CSFE (Cyber-Safety Finance Equation) API
 * 
 * This module implements a RESTful API for the CSFE engine.
 */

const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const { CSFEEngine } = require('./index');

// Create Express app
const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Create CSFE Engine instance
const csfeEngine = new CSFEEngine();

// Routes
app.get('/', (req, res) => {
  res.json({
    name: 'CSFE API',
    version: '1.0.0',
    description: 'Cyber-Safety Finance Equation API',
    endpoints: [
      { method: 'POST', path: '/calculate', description: 'Calculate CSFE value' },
      { method: 'POST', path: '/predict', description: 'Generate financial predictions' },
      { method: 'GET', path: '/health', description: 'Health check' }
    ]
  });
});

/**
 * Calculate CSFE value
 * 
 * POST /calculate
 * 
 * Request body:
 * {
 *   "marketData": { ... },
 *   "economicData": { ... },
 *   "sentimentData": { ... }
 * }
 */
app.post('/calculate', (req, res) => {
  try {
    const { marketData, economicData, sentimentData } = req.body;
    
    // Validate input
    if (!marketData || !economicData || !sentimentData) {
      return res.status(400).json({
        error: 'Missing required data',
        message: 'marketData, economicData, and sentimentData are required'
      });
    }
    
    // Calculate CSFE value
    const result = csfeEngine.calculate(marketData, economicData, sentimentData);
    
    // Return result
    res.json(result);
  } catch (error) {
    console.error('Error calculating CSFE value:', error);
    res.status(500).json({
      error: 'CSFE calculation failed',
      message: error.message
    });
  }
});

/**
 * Generate financial predictions
 * 
 * POST /predict
 * 
 * Request body:
 * {
 *   "marketData": { ... },
 *   "economicData": { ... },
 *   "sentimentData": { ... }
 * }
 */
app.post('/predict', (req, res) => {
  try {
    const { marketData, economicData, sentimentData } = req.body;
    
    // Validate input
    if (!marketData || !economicData || !sentimentData) {
      return res.status(400).json({
        error: 'Missing required data',
        message: 'marketData, economicData, and sentimentData are required'
      });
    }
    
    // Calculate CSFE value
    const result = csfeEngine.calculate(marketData, economicData, sentimentData);
    
    // Return only the predictions part
    res.json({
      csfeValue: result.csfeValue,
      financialPredictions: result.financialPredictions,
      calculatedAt: result.calculatedAt
    });
  } catch (error) {
    console.error('Error generating financial predictions:', error);
    res.status(500).json({
      error: 'Financial prediction generation failed',
      message: error.message
    });
  }
});

/**
 * Health check
 * 
 * GET /health
 */
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

/**
 * Start the server
 */
function start() {
  return new Promise((resolve, reject) => {
    try {
      const server = app.listen(port, () => {
        console.log(`CSFE API running on port ${port}`);
        resolve(server);
      });
    } catch (error) {
      console.error('Failed to start CSFE API:', error);
      reject(error);
    }
  });
}

// Export the app and start function
module.exports = {
  app,
  start
};

// Start the server if this file is run directly
if (require.main === module) {
  start().catch(error => {
    console.error('Failed to start CSFE API:', error);
    process.exit(1);
  });
}
