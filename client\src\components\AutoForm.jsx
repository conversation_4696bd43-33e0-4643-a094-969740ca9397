import React, { useState } from 'react';
import PropTypes from 'prop-types';

/**
 * AutoForm Component
 * 
 * A component that automatically generates a form based on a schema.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.schema - Form schema
 * @param {Function} props.onSubmit - Form submission handler
 * @param {Object} props.initialValues - Initial form values
 * @param {string} props.className - Additional CSS class
 */
const AutoForm = ({ schema, onSubmit, initialValues = {}, className = '' }) => {
  const [formData, setFormData] = useState(initialValues);
  const [errors, setErrors] = useState({});

  // Handle form field change
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
    
    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};
    let isValid = true;

    schema.fields.forEach(field => {
      if (field.required && !formData[field.name]) {
        newErrors[field.name] = `${field.label || field.name} is required`;
        isValid = false;
      }

      // Email validation
      if (field.type === 'email' && formData[field.name]) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData[field.name])) {
          newErrors[field.name] = 'Please enter a valid email address';
          isValid = false;
        }
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  // Render form field based on type
  const renderField = (field) => {
    const { name, label, type, required, description, options, placeholder } = field;
    const value = formData[name] || '';
    const error = errors[name];

    switch (type) {
      case 'text':
      case 'email':
      case 'password':
      case 'number':
      case 'tel':
      case 'url':
        return (
          <div className="form-group" key={name}>
            <label htmlFor={name}>{label || name}{required && <span className="required">*</span>}</label>
            <input
              type={type}
              id={name}
              name={name}
              value={value}
              onChange={handleChange}
              className={`form-control ${error ? 'is-invalid' : ''}`}
              placeholder={placeholder}
              required={required}
            />
            {description && <small className="form-text text-muted">{description}</small>}
            {error && <div className="invalid-feedback">{error}</div>}
          </div>
        );

      case 'textarea':
        return (
          <div className="form-group" key={name}>
            <label htmlFor={name}>{label || name}{required && <span className="required">*</span>}</label>
            <textarea
              id={name}
              name={name}
              value={value}
              onChange={handleChange}
              className={`form-control ${error ? 'is-invalid' : ''}`}
              placeholder={placeholder}
              required={required}
              rows={4}
            />
            {description && <small className="form-text text-muted">{description}</small>}
            {error && <div className="invalid-feedback">{error}</div>}
          </div>
        );

      case 'select':
        return (
          <div className="form-group" key={name}>
            <label htmlFor={name}>{label || name}{required && <span className="required">*</span>}</label>
            <select
              id={name}
              name={name}
              value={value}
              onChange={handleChange}
              className={`form-control ${error ? 'is-invalid' : ''}`}
              required={required}
            >
              <option value="">Select {label || name}</option>
              {options && options.map((option, index) => (
                <option key={index} value={option.value || option}>
                  {option.label || option}
                </option>
              ))}
            </select>
            {description && <small className="form-text text-muted">{description}</small>}
            {error && <div className="invalid-feedback">{error}</div>}
          </div>
        );

      case 'checkbox':
        return (
          <div className="form-group form-check" key={name}>
            <input
              type="checkbox"
              id={name}
              name={name}
              checked={!!value}
              onChange={handleChange}
              className={`form-check-input ${error ? 'is-invalid' : ''}`}
              required={required}
            />
            <label className="form-check-label" htmlFor={name}>
              {label || name}{required && <span className="required">*</span>}
            </label>
            {description && <small className="form-text text-muted">{description}</small>}
            {error && <div className="invalid-feedback">{error}</div>}
          </div>
        );

      case 'radio':
        return (
          <div className="form-group" key={name}>
            <label>{label || name}{required && <span className="required">*</span>}</label>
            <div>
              {options && options.map((option, index) => (
                <div className="form-check" key={index}>
                  <input
                    type="radio"
                    id={`${name}-${index}`}
                    name={name}
                    value={option.value || option}
                    checked={value === (option.value || option)}
                    onChange={handleChange}
                    className={`form-check-input ${error ? 'is-invalid' : ''}`}
                    required={required}
                  />
                  <label className="form-check-label" htmlFor={`${name}-${index}`}>
                    {option.label || option}
                  </label>
                </div>
              ))}
            </div>
            {description && <small className="form-text text-muted">{description}</small>}
            {error && <div className="invalid-feedback">{error}</div>}
          </div>
        );

      case 'date':
        return (
          <div className="form-group" key={name}>
            <label htmlFor={name}>{label || name}{required && <span className="required">*</span>}</label>
            <input
              type="date"
              id={name}
              name={name}
              value={value}
              onChange={handleChange}
              className={`form-control ${error ? 'is-invalid' : ''}`}
              required={required}
            />
            {description && <small className="form-text text-muted">{description}</small>}
            {error && <div className="invalid-feedback">{error}</div>}
          </div>
        );

      default:
        return (
          <div className="form-group" key={name}>
            <label htmlFor={name}>{label || name}{required && <span className="required">*</span>}</label>
            <input
              type="text"
              id={name}
              name={name}
              value={value}
              onChange={handleChange}
              className={`form-control ${error ? 'is-invalid' : ''}`}
              placeholder={placeholder}
              required={required}
            />
            {description && <small className="form-text text-muted">{description}</small>}
            {error && <div className="invalid-feedback">{error}</div>}
          </div>
        );
    }
  };

  return (
    <form onSubmit={handleSubmit} className={className} noValidate>
      {schema.fields.map(field => renderField(field))}
      <div className="form-group">
        <button type="submit" className="btn btn-primary">
          {schema.submitLabel || 'Submit'}
        </button>
      </div>
    </form>
  );
};

AutoForm.propTypes = {
  schema: PropTypes.shape({
    fields: PropTypes.arrayOf(
      PropTypes.shape({
        name: PropTypes.string.isRequired,
        label: PropTypes.string,
        type: PropTypes.string,
        required: PropTypes.bool,
        description: PropTypes.string,
        options: PropTypes.arrayOf(
          PropTypes.oneOfType([
            PropTypes.string,
            PropTypes.shape({
              label: PropTypes.string.isRequired,
              value: PropTypes.string.isRequired
            })
          ])
        ),
        placeholder: PropTypes.string
      })
    ).isRequired,
    submitLabel: PropTypes.string
  }).isRequired,
  onSubmit: PropTypes.func.isRequired,
  initialValues: PropTypes.object,
  className: PropTypes.string
};

export default AutoForm;
