version: '3.8'

services:
  # NovaCortex Core Service
  novacortex:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - NODE_ENV=test
      - MONGODB_URI=mongodb://mongodb:27017/novacortex-test
      - REDIS_URI=redis://redis:6379/0
      - PORT=3010
      - ENABLE_COHERENCE_TESTS=true
      - ENABLE_CASTL_TESTS=true
      - ENABLE_PI_RHYTHM_TESTS=true
    ports:
      - "3010:3010"
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3010/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  # MongoDB for test data
  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Redis for caching and pub/sub
  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Test Runner
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.test-runner
    environment:
      - NOVACORTEX_URL=http://novacortex:3010
      - PYTHONPATH=/app
    volumes:
      - ./tests:/app/tests
      - ./reports:/app/reports
    depends_on:
      novacortex:
        condition: service_healthy

  # Monitoring Dashboard (Optional)
  monitoring:
    image: grafana/grafana:9.3.2
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus

  # Metrics Collection
  prometheus:
    image: prom/prometheus:v2.40.1
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'

  # Test Results Visualization (Optional)
  allure:
    image: frankescobar/allure-docker-service
    environment:
      - CHECK_RESULTS_EVERY_SECONDS=5
      - KEEP_HISTORY=TRUE
    ports:
      - "5050:5050"
    volumes:
      - ./reports/allure-results:/app/allure-results
    depends_on:
      - test-runner

volumes:
  mongodb_data:
  redis_data:
  grafana_data:
  prometheus_data:
  reports:

networks:
  default:
    name: novacortex-test
    driver: bridge
