/**
 * NovaCore Assessment Service
 * 
 * This service provides functionality for managing vendor assessments.
 */

const { Assessment, AssessmentTemplate, Vendor, Document } = require('../models');
const logger = require('../../../config/logger');
const { ValidationError, NotFoundError } = require('../../../api/utils/errors');
const { v4: uuidv4 } = require('uuid');

class AssessmentService {
  /**
   * Create a new assessment
   * @param {Object} data - Assessment data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created assessment
   */
  async createAssessment(data, userId) {
    try {
      logger.info('Creating new assessment', { 
        organizationId: data.organizationId,
        vendorId: data.vendorId
      });
      
      // Validate vendor
      const vendor = await Vendor.findById(data.vendorId);
      
      if (!vendor) {
        throw new ValidationError(`Vendor with ID ${data.vendorId} not found`);
      }
      
      // If template ID is provided, use it to populate sections
      if (data.templateId) {
        const template = await AssessmentTemplate.findById(data.templateId);
        
        if (!template) {
          throw new ValidationError(`Assessment template with ID ${data.templateId} not found`);
        }
        
        // Copy sections from template
        data.sections = JSON.parse(JSON.stringify(template.sections));
        data.frameworks = template.frameworks;
        data.passingScore = template.passingScore;
      }
      
      // Set created by
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Create assessment
      const assessment = new Assessment(data);
      await assessment.save();
      
      logger.info('Assessment created successfully', { id: assessment._id });
      
      return assessment;
    } catch (error) {
      logger.error('Error creating assessment', { error });
      throw error;
    }
  }
  
  /**
   * Get all assessments for an organization
   * @param {string} organizationId - Organization ID
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Assessments with pagination info
   */
  async getAllAssessments(organizationId, filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
      
      // Build query
      const query = { organizationId };
      
      // Apply filters
      if (filter.vendorId) {
        query.vendorId = filter.vendorId;
      }
      
      if (filter.status) {
        query.status = filter.status;
      }
      
      if (filter.frameworks) {
        query.frameworks = { $all: Array.isArray(filter.frameworks) ? filter.frameworks : [filter.frameworks] };
      }
      
      if (filter.tags) {
        query.tags = { $all: Array.isArray(filter.tags) ? filter.tags : [filter.tags] };
      }
      
      if (filter.cyberSafetyCertified) {
        query.cyberSafetyCertified = filter.cyberSafetyCertified === 'true';
      }
      
      if (filter.search) {
        query.$or = [
          { name: { $regex: filter.search, $options: 'i' } },
          { description: { $regex: filter.search, $options: 'i' } }
        ];
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [assessments, total] = await Promise.all([
        Assessment.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        Assessment.countDocuments(query)
      ]);
      
      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;
      
      return {
        data: assessments,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting assessments', { error });
      throw error;
    }
  }
  
  /**
   * Get assessment by ID
   * @param {string} id - Assessment ID
   * @returns {Promise<Object>} - Assessment
   */
  async getAssessmentById(id) {
    try {
      const assessment = await Assessment.findById(id);
      
      if (!assessment) {
        throw new NotFoundError(`Assessment with ID ${id} not found`);
      }
      
      return assessment;
    } catch (error) {
      logger.error('Error getting assessment by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Update assessment
   * @param {string} id - Assessment ID
   * @param {Object} data - Updated assessment data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated assessment
   */
  async updateAssessment(id, data, userId) {
    try {
      // Get existing assessment
      const assessment = await this.getAssessmentById(id);
      
      // Set updated by
      data.updatedBy = userId;
      
      // Update assessment
      Object.assign(assessment, data);
      await assessment.save();
      
      logger.info('Assessment updated successfully', { id });
      
      return assessment;
    } catch (error) {
      logger.error('Error updating assessment', { id, error });
      throw error;
    }
  }
  
  /**
   * Delete assessment
   * @param {string} id - Assessment ID
   * @returns {Promise<boolean>} - Deletion success
   */
  async deleteAssessment(id) {
    try {
      const result = await Assessment.findByIdAndDelete(id);
      
      if (!result) {
        throw new NotFoundError(`Assessment with ID ${id} not found`);
      }
      
      logger.info('Assessment deleted successfully', { id });
      
      return true;
    } catch (error) {
      logger.error('Error deleting assessment', { id, error });
      throw error;
    }
  }
  
  /**
   * Submit answer for assessment
   * @param {string} id - Assessment ID
   * @param {Object} data - Answer data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated assessment
   */
  async submitAnswer(id, data, userId) {
    try {
      // Get assessment
      const assessment = await this.getAssessmentById(id);
      
      // Validate question ID
      const questionExists = assessment.sections.some(section => 
        section.questions.some(question => question.id === data.questionId)
      );
      
      if (!questionExists) {
        throw new ValidationError(`Question with ID ${data.questionId} not found in assessment`);
      }
      
      // Find existing answer
      const existingAnswerIndex = assessment.answers.findIndex(
        answer => answer.questionId === data.questionId
      );
      
      if (existingAnswerIndex >= 0) {
        // Update existing answer
        assessment.answers[existingAnswerIndex] = {
          ...assessment.answers[existingAnswerIndex],
          ...data
        };
      } else {
        // Add new answer
        assessment.answers.push(data);
      }
      
      // Calculate score for this answer
      this._calculateAnswerScore(assessment, data.questionId);
      
      // Update section completion status
      this._updateSectionCompletionStatus(assessment);
      
      // Calculate overall score
      assessment.calculateScore();
      
      // Update assessment status if all questions are answered
      if (assessment.getCompletionPercentage() === 100 && assessment.status === 'draft') {
        assessment.status = 'in_progress';
      }
      
      // Set updated by
      assessment.updatedBy = userId;
      
      // Save assessment
      await assessment.save();
      
      logger.info('Assessment answer submitted successfully', { id, questionId: data.questionId });
      
      return assessment;
    } catch (error) {
      logger.error('Error submitting assessment answer', { id, error });
      throw error;
    }
  }
  
  /**
   * Submit assessment for review
   * @param {string} id - Assessment ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated assessment
   */
  async submitForReview(id, userId) {
    try {
      // Get assessment
      const assessment = await this.getAssessmentById(id);
      
      // Validate assessment status
      if (assessment.status !== 'draft' && assessment.status !== 'in_progress') {
        throw new ValidationError(`Assessment with ID ${id} cannot be submitted for review (current status: ${assessment.status})`);
      }
      
      // Update assessment status
      assessment.status = 'pending_review';
      assessment.updatedBy = userId;
      
      // Save assessment
      await assessment.save();
      
      logger.info('Assessment submitted for review successfully', { id });
      
      return assessment;
    } catch (error) {
      logger.error('Error submitting assessment for review', { id, error });
      throw error;
    }
  }
  
  /**
   * Complete assessment review
   * @param {string} id - Assessment ID
   * @param {Object} data - Review data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated assessment
   */
  async completeReview(id, data, userId) {
    try {
      // Get assessment
      const assessment = await this.getAssessmentById(id);
      
      // Validate assessment status
      if (assessment.status !== 'pending_review') {
        throw new ValidationError(`Assessment with ID ${id} cannot be reviewed (current status: ${assessment.status})`);
      }
      
      // Update assessment
      assessment.status = 'completed';
      assessment.reviewedDate = new Date();
      assessment.reviewedBy = userId;
      assessment.result = data.result || this._determineResult(assessment);
      assessment.findings = data.findings || [];
      assessment.updatedBy = userId;
      
      // If Cyber-Safety certification is requested
      if (data.cyberSafetyCertify) {
        assessment.cyberSafetyCertified = true;
        assessment.cyberSafetyScore = assessment.score ? Math.round((assessment.score / assessment.maxScore) * 100) : 0;
        
        // Update vendor certification
        const vendor = await Vendor.findById(assessment.vendorId);
        if (vendor) {
          vendor.cyberSafetyCertified = true;
          vendor.cyberSafetyScore = assessment.cyberSafetyScore;
          vendor.cyberSafetyCertificationDate = new Date();
          vendor.cyberSafetyCertificationExpiration = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 1 year
          vendor.updatedBy = userId;
          await vendor.save();
        }
      }
      
      // Save assessment
      await assessment.save();
      
      logger.info('Assessment review completed successfully', { id });
      
      return assessment;
    } catch (error) {
      logger.error('Error completing assessment review', { id, error });
      throw error;
    }
  }
  
  /**
   * Add finding to assessment
   * @param {string} id - Assessment ID
   * @param {Object} data - Finding data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated assessment
   */
  async addFinding(id, data, userId) {
    try {
      // Get assessment
      const assessment = await this.getAssessmentById(id);
      
      // Generate finding ID if not provided
      if (!data.id) {
        data.id = `finding-${uuidv4().substring(0, 8)}`;
      }
      
      // Add created by and timestamps
      data.createdBy = userId;
      data.createdAt = new Date();
      data.updatedBy = userId;
      data.updatedAt = new Date();
      
      // Add finding
      assessment.findings.push(data);
      assessment.updatedBy = userId;
      
      // Save assessment
      await assessment.save();
      
      logger.info('Assessment finding added successfully', { id, findingId: data.id });
      
      return assessment;
    } catch (error) {
      logger.error('Error adding assessment finding', { id, error });
      throw error;
    }
  }
  
  /**
   * Update finding in assessment
   * @param {string} id - Assessment ID
   * @param {string} findingId - Finding ID
   * @param {Object} data - Updated finding data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated assessment
   */
  async updateFinding(id, findingId, data, userId) {
    try {
      // Get assessment
      const assessment = await this.getAssessmentById(id);
      
      // Find finding
      const findingIndex = assessment.findings.findIndex(finding => finding.id === findingId);
      
      if (findingIndex === -1) {
        throw new NotFoundError(`Finding with ID ${findingId} not found in assessment`);
      }
      
      // Update finding
      assessment.findings[findingIndex] = {
        ...assessment.findings[findingIndex],
        ...data,
        updatedBy: userId,
        updatedAt: new Date()
      };
      
      assessment.updatedBy = userId;
      
      // Save assessment
      await assessment.save();
      
      logger.info('Assessment finding updated successfully', { id, findingId });
      
      return assessment;
    } catch (error) {
      logger.error('Error updating assessment finding', { id, findingId, error });
      throw error;
    }
  }
  
  /**
   * Get assessment documents
   * @param {string} id - Assessment ID
   * @returns {Promise<Array>} - Documents
   */
  async getAssessmentDocuments(id) {
    try {
      // Get assessment
      await this.getAssessmentById(id);
      
      // Get documents
      const documents = await Document.findByAssessment(id);
      
      return documents;
    } catch (error) {
      logger.error('Error getting assessment documents', { id, error });
      throw error;
    }
  }
  
  /**
   * Calculate answer score
   * @param {Object} assessment - Assessment
   * @param {string} questionId - Question ID
   * @private
   */
  _calculateAnswerScore(assessment, questionId) {
    // Find question
    let question = null;
    let section = null;
    
    for (const s of assessment.sections) {
      const q = s.questions.find(q => q.id === questionId);
      if (q) {
        question = q;
        section = s;
        break;
      }
    }
    
    if (!question) {
      return;
    }
    
    // Find answer
    const answer = assessment.answers.find(a => a.questionId === questionId);
    
    if (!answer) {
      return;
    }
    
    // Calculate score based on question type
    let score = 0;
    
    if (question.type === 'yes_no') {
      score = answer.value === 'yes' ? 1 : 0;
    } else if (question.type === 'multiple_choice' && question.options) {
      const option = question.options.find(o => o.value === answer.value);
      score = option ? (option.score || 0) : 0;
    } else if (question.type === 'rating') {
      score = parseInt(answer.value, 10) || 0;
    } else {
      // For text and file_upload, score is 1 if answered
      score = answer.value ? 1 : 0;
    }
    
    // Apply question weight
    score *= (question.weight || 1);
    
    // Update answer score
    answer.score = score;
  }
  
  /**
   * Update section completion status
   * @param {Object} assessment - Assessment
   * @private
   */
  _updateSectionCompletionStatus(assessment) {
    for (const section of assessment.sections) {
      const totalQuestions = section.questions.length;
      
      if (totalQuestions === 0) {
        section.completionStatus = 'completed';
        continue;
      }
      
      const answeredQuestions = section.questions.filter(question => 
        assessment.answers.some(answer => answer.questionId === question.id)
      ).length;
      
      if (answeredQuestions === 0) {
        section.completionStatus = 'not_started';
      } else if (answeredQuestions < totalQuestions) {
        section.completionStatus = 'in_progress';
      } else {
        section.completionStatus = 'completed';
      }
    }
  }
  
  /**
   * Determine assessment result
   * @param {Object} assessment - Assessment
   * @returns {string} - Result
   * @private
   */
  _determineResult(assessment) {
    if (!assessment.score || !assessment.maxScore) {
      return 'not_applicable';
    }
    
    const scorePercentage = (assessment.score / assessment.maxScore) * 100;
    
    if (scorePercentage >= (assessment.passingScore || 70)) {
      return 'pass';
    } else if (scorePercentage >= (assessment.passingScore * 0.8 || 56)) {
      return 'conditional_pass';
    } else {
      return 'fail';
    }
  }
}

module.exports = new AssessmentService();
