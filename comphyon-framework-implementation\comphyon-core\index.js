/**
 * Comphyon Core
 * 
 * This module exports all components of the Comphyon Core system.
 * The Comphyon Core provides a unified system that integrates all components
 * of the Comphyology framework.
 */

const ComphyonIntegrationLayer = require('./integration-layer');
const ComphyonSystem = require('./comphyon-system');

/**
 * Create a Comphyon System
 * @param {Object} options - Configuration options
 * @returns {ComphyonSystem} - Comphyon System instance
 */
function createComphyonSystem(options = {}) {
  return new ComphyonSystem(options);
}

module.exports = {
  ComphyonIntegrationLayer,
  ComphyonSystem,
  createComphyonSystem
};
