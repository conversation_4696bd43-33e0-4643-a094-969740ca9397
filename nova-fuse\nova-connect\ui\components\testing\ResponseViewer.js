/**
 * Response Viewer Component
 * 
 * This component displays API response data with syntax highlighting and formatting options.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Chip, 
  Divider, 
  FormControl, 
  Grid, 
  InputLabel, 
  MenuItem, 
  Paper, 
  Select, 
  Tab, 
  Tabs, 
  Typography 
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DownloadIcon from '@mui/icons-material/Download';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

const ResponseViewer = ({ response }) => {
  const [activeTab, setActiveTab] = useState('body');
  const [format, setFormat] = useState('json');
  
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  const handleFormatChange = (event) => {
    setFormat(event.target.value);
  };
  
  const handleCopyToClipboard = () => {
    let contentToCopy = '';
    
    if (activeTab === 'body') {
      contentToCopy = JSON.stringify(response.data, null, 2);
    } else if (activeTab === 'headers') {
      contentToCopy = JSON.stringify(response.headers, null, 2);
    } else if (activeTab === 'raw') {
      contentToCopy = JSON.stringify(response, null, 2);
    }
    
    navigator.clipboard.writeText(contentToCopy);
  };
  
  const handleDownload = () => {
    let contentToDownload = '';
    let fileName = '';
    
    if (activeTab === 'body') {
      contentToDownload = JSON.stringify(response.data, null, 2);
      fileName = 'response-body.json';
    } else if (activeTab === 'headers') {
      contentToDownload = JSON.stringify(response.headers, null, 2);
      fileName = 'response-headers.json';
    } else if (activeTab === 'raw') {
      contentToDownload = JSON.stringify(response, null, 2);
      fileName = 'response-raw.json';
    }
    
    const blob = new Blob([contentToDownload], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  
  const formatResponse = (data) => {
    if (format === 'json') {
      return JSON.stringify(data, null, 2);
    } else if (format === 'xml') {
      // This is a very simple JSON to XML conversion for demonstration
      // In a real implementation, you would use a proper JSON to XML converter
      const jsonToXml = (obj, indent = '') => {
        let xml = '';
        for (const prop in obj) {
          if (obj.hasOwnProperty(prop)) {
            if (Array.isArray(obj[prop])) {
              for (const item of obj[prop]) {
                xml += `${indent}<${prop}>\n`;
                xml += jsonToXml(item, indent + '  ');
                xml += `${indent}</${prop}>\n`;
              }
            } else if (typeof obj[prop] === 'object' && obj[prop] !== null) {
              xml += `${indent}<${prop}>\n`;
              xml += jsonToXml(obj[prop], indent + '  ');
              xml += `${indent}</${prop}>\n`;
            } else {
              xml += `${indent}<${prop}>${obj[prop]}</${prop}>\n`;
            }
          }
        }
        return xml;
      };
      
      return `<?xml version="1.0" encoding="UTF-8"?>\n<response>\n${jsonToXml(data, '  ')}</response>`;
    }
    
    return JSON.stringify(data, null, 2);
  };
  
  const getLanguage = () => {
    return format === 'xml' ? 'xml' : 'json';
  };
  
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Response
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Chip 
            label={`Status: ${response.status} ${response.statusText}`} 
            color={response.status >= 200 && response.status < 300 ? 'success' : 'error'}
            sx={{ mr: 2 }}
          />
          
          <FormControl sx={{ minWidth: 120, mr: 2 }}>
            <InputLabel id="format-select-label">Format</InputLabel>
            <Select
              labelId="format-select-label"
              id="format-select"
              value={format}
              label="Format"
              onChange={handleFormatChange}
              size="small"
            >
              <MenuItem value="json">JSON</MenuItem>
              <MenuItem value="xml">XML</MenuItem>
            </Select>
          </FormControl>
          
          <Button
            variant="outlined"
            size="small"
            startIcon={<ContentCopyIcon />}
            onClick={handleCopyToClipboard}
            sx={{ mr: 1 }}
          >
            Copy
          </Button>
          
          <Button
            variant="outlined"
            size="small"
            startIcon={<DownloadIcon />}
            onClick={handleDownload}
          >
            Download
          </Button>
        </Box>
      </Box>
      
      <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
        <Tab label="Body" value="body" />
        <Tab label="Headers" value="headers" />
        <Tab label="Raw" value="raw" />
      </Tabs>
      
      <Paper variant="outlined" sx={{ p: 0, overflow: 'auto', maxHeight: 500 }}>
        {activeTab === 'body' && (
          <SyntaxHighlighter language={getLanguage()} style={vscDarkPlus} showLineNumbers>
            {formatResponse(response.data)}
          </SyntaxHighlighter>
        )}
        
        {activeTab === 'headers' && (
          <SyntaxHighlighter language="json" style={vscDarkPlus} showLineNumbers>
            {JSON.stringify(response.headers, null, 2)}
          </SyntaxHighlighter>
        )}
        
        {activeTab === 'raw' && (
          <SyntaxHighlighter language="json" style={vscDarkPlus} showLineNumbers>
            {JSON.stringify(response, null, 2)}
          </SyntaxHighlighter>
        )}
      </Paper>
    </Box>
  );
};

export default ResponseViewer;
