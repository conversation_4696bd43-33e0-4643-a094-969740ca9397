/**
 * Comphyology Utilities
 * 
 * This module provides utility functions for Comphyology components.
 */

/**
 * Generate a UUID
 * 
 * @returns {string} - UUID
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Deep clone an object
 * 
 * @param {Object} obj - Object to clone
 * @returns {Object} - Cloned object
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item));
  }
  
  const clone = {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      clone[key] = deepClone(obj[key]);
    }
  }
  
  return clone;
}

/**
 * Format number with fixed precision
 * 
 * @param {number} num - Number to format
 * @param {number} precision - Precision
 * @returns {string} - Formatted number
 */
function formatNumber(num, precision = 4) {
  return num.toFixed(precision);
}

/**
 * Calculate mean of an array
 * 
 * @param {Array} arr - Array of numbers
 * @returns {number} - Mean
 */
function calculateMean(arr) {
  if (!Array.isArray(arr) || arr.length === 0) {
    return 0;
  }
  
  const sum = arr.reduce((acc, val) => acc + val, 0);
  return sum / arr.length;
}

/**
 * Calculate standard deviation of an array
 * 
 * @param {Array} arr - Array of numbers
 * @returns {number} - Standard deviation
 */
function calculateStdDev(arr) {
  if (!Array.isArray(arr) || arr.length <= 1) {
    return 0;
  }
  
  const mean = calculateMean(arr);
  const variance = arr.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / arr.length;
  return Math.sqrt(variance);
}

/**
 * Normalize an array to [0, 1] range
 * 
 * @param {Array} arr - Array of numbers
 * @returns {Array} - Normalized array
 */
function normalizeArray(arr) {
  if (!Array.isArray(arr) || arr.length === 0) {
    return [];
  }
  
  const min = Math.min(...arr);
  const max = Math.max(...arr);
  
  if (min === max) {
    return arr.map(() => 0.5);
  }
  
  return arr.map(val => (val - min) / (max - min));
}

/**
 * Generate a random number in a range
 * 
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @returns {number} - Random number
 */
function randomInRange(min, max) {
  return min + Math.random() * (max - min);
}

/**
 * Generate a random integer in a range
 * 
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @returns {number} - Random integer
 */
function randomIntInRange(min, max) {
  return Math.floor(randomInRange(min, max + 1));
}

/**
 * Shuffle an array
 * 
 * @param {Array} arr - Array to shuffle
 * @returns {Array} - Shuffled array
 */
function shuffleArray(arr) {
  const result = [...arr];
  
  for (let i = result.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [result[i], result[j]] = [result[j], result[i]];
  }
  
  return result;
}

/**
 * Debounce a function
 * 
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} - Debounced function
 */
function debounce(func, wait) {
  let timeout;
  
  return function(...args) {
    const context = this;
    clearTimeout(timeout);
    
    timeout = setTimeout(() => {
      func.apply(context, args);
    }, wait);
  };
}

/**
 * Throttle a function
 * 
 * @param {Function} func - Function to throttle
 * @param {number} limit - Limit in milliseconds
 * @returns {Function} - Throttled function
 */
function throttle(func, limit) {
  let inThrottle;
  
  return function(...args) {
    const context = this;
    
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
}

/**
 * Memoize a function
 * 
 * @param {Function} func - Function to memoize
 * @returns {Function} - Memoized function
 */
function memoize(func) {
  const cache = new Map();
  
  return function(...args) {
    const key = JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    const result = func.apply(this, args);
    cache.set(key, result);
    
    return result;
  };
}

/**
 * Format date as ISO string
 * 
 * @param {Date} date - Date to format
 * @returns {string} - Formatted date
 */
function formatDate(date) {
  return date.toISOString();
}

/**
 * Format date as local string
 * 
 * @param {Date} date - Date to format
 * @returns {string} - Formatted date
 */
function formatDateLocal(date) {
  return date.toLocaleString();
}

/**
 * Check if a value is a number
 * 
 * @param {*} value - Value to check
 * @returns {boolean} - Whether the value is a number
 */
function isNumber(value) {
  return typeof value === 'number' && !isNaN(value);
}

/**
 * Check if a value is an object
 * 
 * @param {*} value - Value to check
 * @returns {boolean} - Whether the value is an object
 */
function isObject(value) {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

/**
 * Check if a value is an array
 * 
 * @param {*} value - Value to check
 * @returns {boolean} - Whether the value is an array
 */
function isArray(value) {
  return Array.isArray(value);
}

/**
 * Check if a value is a string
 * 
 * @param {*} value - Value to check
 * @returns {boolean} - Whether the value is a string
 */
function isString(value) {
  return typeof value === 'string';
}

/**
 * Check if a value is a function
 * 
 * @param {*} value - Value to check
 * @returns {boolean} - Whether the value is a function
 */
function isFunction(value) {
  return typeof value === 'function';
}

/**
 * Check if a value is undefined
 * 
 * @param {*} value - Value to check
 * @returns {boolean} - Whether the value is undefined
 */
function isUndefined(value) {
  return typeof value === 'undefined';
}

/**
 * Check if a value is null
 * 
 * @param {*} value - Value to check
 * @returns {boolean} - Whether the value is null
 */
function isNull(value) {
  return value === null;
}

/**
 * Check if a value is null or undefined
 * 
 * @param {*} value - Value to check
 * @returns {boolean} - Whether the value is null or undefined
 */
function isNullOrUndefined(value) {
  return isNull(value) || isUndefined(value);
}

module.exports = {
  generateUUID,
  deepClone,
  formatNumber,
  calculateMean,
  calculateStdDev,
  normalizeArray,
  randomInRange,
  randomIntInRange,
  shuffleArray,
  debounce,
  throttle,
  memoize,
  formatDate,
  formatDateLocal,
  isNumber,
  isObject,
  isArray,
  isString,
  isFunction,
  isUndefined,
  isNull,
  isNullOrUndefined
};
