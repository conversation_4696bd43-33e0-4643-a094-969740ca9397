/**
 * NovaConnect UAC GCP Load Test Functions
 * 
 * This file contains functions used by the GCP load test.
 */

// Generate a random string
function randomString(length = 10) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Generate a random connector payload
function generateConnectorPayload(context, events, done) {
  const connector = {
    name: `Test Connector ${randomString(5)}`,
    description: `Test connector created by load test at ${new Date().toISOString()}`,
    type: ['http', 'aws', 'azure', 'gcp'][Math.floor(Math.random() * 4)],
    config: {
      baseUrl: 'https://api.example.com',
      authType: 'api_key',
      apiKey: `test-api-key-${randomString(10)}`,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    },
    status: 'active',
    tags: ['test', 'load-test', 'gcp']
  };

  context.vars.connector = connector;
  return done();
}

// Update a connector payload
function updateConnectorPayload(context, events, done) {
  const connector = context.vars.connector;
  
  const updatedConnector = {
    ...connector,
    name: `${connector.name} (Updated)`,
    description: `${connector.description} - Updated at ${new Date().toISOString()}`,
    config: {
      ...connector.config,
      headers: {
        ...connector.config.headers,
        'X-Custom-Header': randomString(10)
      }
    },
    tags: [...connector.tags, 'updated']
  };

  context.vars.updatedConnector = updatedConnector;
  return done();
}

// Generate a random normalization payload
function generateNormalizationPayload(context, events, done) {
  const sourceData = {
    id: randomString(10),
    name: `Test Data ${randomString(5)}`,
    created_at: new Date().toISOString(),
    status: ['active', 'inactive', 'pending'][Math.floor(Math.random() * 3)],
    metadata: {
      source: 'load-test',
      environment: 'gcp',
      version: '1.0.0'
    },
    items: Array.from({ length: Math.floor(Math.random() * 5) + 1 }, (_, i) => ({
      id: `item-${i}`,
      value: Math.random() * 100,
      label: `Item ${i}`
    }))
  };

  const normalization = {
    source: 'test-source',
    sourceData: sourceData,
    targetFormat: 'standard',
    options: {
      includeMetadata: true,
      convertDates: true,
      normalizeStrings: true
    }
  };

  context.vars.normalization = normalization;
  return done();
}

// Generate a random endpoint payload
function generateEndpointPayload(context, events, done) {
  const endpoint = {
    name: `Test Endpoint ${randomString(5)}`,
    description: `Test endpoint created by load test at ${new Date().toISOString()}`,
    path: `/api/v1/test/${randomString(8)}`,
    method: ['GET', 'POST', 'PUT', 'DELETE'][Math.floor(Math.random() * 4)],
    requestSchema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        value: { type: 'number' }
      },
      required: ['id']
    },
    responseSchema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        value: { type: 'number' },
        timestamp: { type: 'string', format: 'date-time' }
      },
      required: ['id', 'timestamp']
    },
    status: 'active',
    tags: ['test', 'load-test', 'gcp']
  };

  context.vars.endpoint = endpoint;
  return done();
}

// Update an endpoint payload
function updateEndpointPayload(context, events, done) {
  const endpoint = context.vars.endpoint;
  
  const updatedEndpoint = {
    ...endpoint,
    name: `${endpoint.name} (Updated)`,
    description: `${endpoint.description} - Updated at ${new Date().toISOString()}`,
    requestSchema: {
      ...endpoint.requestSchema,
      properties: {
        ...endpoint.requestSchema.properties,
        metadata: { type: 'object' }
      }
    },
    tags: [...endpoint.tags, 'updated']
  };

  context.vars.updatedEndpoint = updatedEndpoint;
  return done();
}

module.exports = {
  generateConnectorPayload,
  updateConnectorPayload,
  generateNormalizationPayload,
  generateEndpointPayload,
  updateEndpointPayload
};
