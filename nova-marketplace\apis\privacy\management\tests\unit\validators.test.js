/**
 * Validators Tests
 *
 * This file contains unit tests for the validators utility.
 */

const validators = require('../../utils/validators');

describe('Validators', () => {
  describe('isValidEmail', () => {
    it('should return true for valid email addresses', () => {
      expect(validators.isValidEmail('<EMAIL>')).toBe(true);
      expect(validators.isValidEmail('<EMAIL>')).toBe(true);
      expect(validators.isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should return false for invalid email addresses', () => {
      expect(validators.isValidEmail('')).toBe(false);
      expect(validators.isValidEmail('test')).toBe(false);
      expect(validators.isValidEmail('test@')).toBe(false);
      expect(validators.isValidEmail('@example.com')).toBe(false);
      expect(validators.isValidEmail('test@example')).toBe(false);
      expect(validators.isValidEmail('test@.com')).toBe(false);
      expect(validators.isValidEmail('test@example.')).toBe(false);
      expect(validators.isValidEmail('test@exam ple.com')).toBe(false);
    });
  });

  describe('isValidPhone', () => {
    it('should return true for valid phone numbers', () => {
      expect(validators.isValidPhone('1234567890')).toBe(true);
      expect(validators.isValidPhone('+1234567890')).toBe(true);
      expect(validators.isValidPhone('12345678901')).toBe(true);
      expect(validators.isValidPhone('+12345678901')).toBe(true);
    });

    it('should return false for invalid phone numbers', () => {
      expect(validators.isValidPhone('')).toBe(false);
      expect(validators.isValidPhone('123')).toBe(false);
      expect(validators.isValidPhone('123456789')).toBe(false);
      expect(validators.isValidPhone('123456789012345678901')).toBe(false);
      expect(validators.isValidPhone('************')).toBe(false);
      expect(validators.isValidPhone('(*************')).toBe(false);
      expect(validators.isValidPhone('abc1234567890')).toBe(false);
    });
  });

  describe('isValidDate', () => {
    it('should return true for valid dates', () => {
      expect(validators.isValidDate(new Date())).toBe(true);
      expect(validators.isValidDate('2023-06-15')).toBe(true);
      expect(validators.isValidDate('2023-06-15T12:00:00Z')).toBe(true);
    });

    it('should return false for invalid dates', () => {
      expect(validators.isValidDate('')).toBe(false);
      expect(validators.isValidDate(null)).toBe(false);
      expect(validators.isValidDate(undefined)).toBe(false);
      expect(validators.isValidDate('invalid-date')).toBe(false);
      expect(validators.isValidDate('2023-13-15')).toBe(false);
      expect(validators.isValidDate('2023-06-32')).toBe(false);
    });
  });

  describe('isValidDateRange', () => {
    it('should return true for valid date ranges', () => {
      expect(validators.isValidDateRange({
        start: '2023-06-01',
        end: '2023-06-15'
      })).toBe(true);

      expect(validators.isValidDateRange({
        start: new Date('2023-06-01'),
        end: new Date('2023-06-15')
      })).toBe(true);

      expect(validators.isValidDateRange({
        start: '2023-06-01',
        end: '2023-06-01'
      })).toBe(true);
    });

    it('should return false for invalid date ranges', () => {
      expect(validators.isValidDateRange(null)).toBe(false);
      expect(validators.isValidDateRange({})).toBe(false);
      expect(validators.isValidDateRange({ start: '2023-06-01' })).toBe(false);
      expect(validators.isValidDateRange({ end: '2023-06-15' })).toBe(false);

      expect(validators.isValidDateRange({
        start: '2023-06-15',
        end: '2023-06-01'
      })).toBe(false);

      expect(validators.isValidDateRange({
        start: 'invalid-date',
        end: '2023-06-15'
      })).toBe(false);

      expect(validators.isValidDateRange({
        start: '2023-06-01',
        end: 'invalid-date'
      })).toBe(false);
    });
  });

  describe('isValidUrl', () => {
    it('should return true for valid URLs', () => {
      expect(validators.isValidUrl('https://example.com')).toBe(true);
      expect(validators.isValidUrl('http://example.com')).toBe(true);
      expect(validators.isValidUrl('https://example.com/path')).toBe(true);
      expect(validators.isValidUrl('https://example.com/path?query=value')).toBe(true);
      expect(validators.isValidUrl('https://example.com/path#fragment')).toBe(true);
      expect(validators.isValidUrl('https://sub.example.com')).toBe(true);
    });

    it('should return false for invalid URLs', () => {
      expect(validators.isValidUrl('')).toBe(false);
      expect(validators.isValidUrl('example.com')).toBe(false);
      expect(validators.isValidUrl('example')).toBe(false);
      expect(validators.isValidUrl('http:/example.com')).toBe(false);
      expect(validators.isValidUrl('http://example com')).toBe(false);
      expect(validators.isValidUrl('ftp://example.com')).toBe(false); // Only http/https are valid
    });
  });

  describe('isValidIp', () => {
    it('should return true for valid IPv4 addresses', () => {
      expect(validators.isValidIp('***********')).toBe(true);
      expect(validators.isValidIp('127.0.0.1')).toBe(true);
      expect(validators.isValidIp('0.0.0.0')).toBe(true);
      expect(validators.isValidIp('***************')).toBe(true);
    });

    it('should return true for valid IPv6 addresses', () => {
      expect(validators.isValidIp('2001:0db8:85a3:0000:0000:8a2e:0370:7334')).toBe(true);
    });

    it('should return false for invalid IP addresses', () => {
      expect(validators.isValidIp('')).toBe(false);
      expect(validators.isValidIp('192.168.0')).toBe(false);
      expect(validators.isValidIp('***********.1')).toBe(false);
      expect(validators.isValidIp('192.168.0.256')).toBe(false);
      expect(validators.isValidIp('192.168.0.-1')).toBe(false);
      expect(validators.isValidIp('192.168.0.a')).toBe(false);
      expect(validators.isValidIp('2001:0db8:85a3:0000:0000:8a2e:0370')).toBe(false);
    });
  });

  describe('isValidDsrType', () => {
    it('should return true for valid DSR types', () => {
      expect(validators.isValidDsrType('access')).toBe(true);
      expect(validators.isValidDsrType('rectification')).toBe(true);
      expect(validators.isValidDsrType('erasure')).toBe(true);
      expect(validators.isValidDsrType('restriction')).toBe(true);
      expect(validators.isValidDsrType('portability')).toBe(true);
      expect(validators.isValidDsrType('objection')).toBe(true);
      expect(validators.isValidDsrType('automated_decision')).toBe(true);
    });

    it('should return false for invalid DSR types', () => {
      expect(validators.isValidDsrType('')).toBe(false);
      expect(validators.isValidDsrType('invalid')).toBe(false);
      expect(validators.isValidDsrType('ACCESS')).toBe(false);
      expect(validators.isValidDsrType('access ')).toBe(false);
      expect(validators.isValidDsrType(' access')).toBe(false);
    });
  });

  describe('isValidConsentType', () => {
    it('should return true for valid consent types', () => {
      expect(validators.isValidConsentType('marketing')).toBe(true);
      expect(validators.isValidConsentType('analytics')).toBe(true);
      expect(validators.isValidConsentType('profiling')).toBe(true);
      expect(validators.isValidConsentType('third_party')).toBe(true);
      expect(validators.isValidConsentType('research')).toBe(true);
      expect(validators.isValidConsentType('other')).toBe(true);
    });

    it('should return false for invalid consent types', () => {
      expect(validators.isValidConsentType('')).toBe(false);
      expect(validators.isValidConsentType('invalid')).toBe(false);
      expect(validators.isValidConsentType('MARKETING')).toBe(false);
      expect(validators.isValidConsentType('marketing ')).toBe(false);
      expect(validators.isValidConsentType(' marketing')).toBe(false);
    });
  });

  describe('isValidLegalBasis', () => {
    it('should return true for valid legal bases', () => {
      expect(validators.isValidLegalBasis('consent')).toBe(true);
      expect(validators.isValidLegalBasis('contract')).toBe(true);
      expect(validators.isValidLegalBasis('legal_obligation')).toBe(true);
      expect(validators.isValidLegalBasis('vital_interests')).toBe(true);
      expect(validators.isValidLegalBasis('public_interest')).toBe(true);
      expect(validators.isValidLegalBasis('legitimate_interests')).toBe(true);
    });

    it('should return false for invalid legal bases', () => {
      expect(validators.isValidLegalBasis('')).toBe(false);
      expect(validators.isValidLegalBasis('invalid')).toBe(false);
      expect(validators.isValidLegalBasis('CONSENT')).toBe(false);
      expect(validators.isValidLegalBasis('consent ')).toBe(false);
      expect(validators.isValidLegalBasis(' consent')).toBe(false);
    });
  });

  describe('isValidBreachSeverity', () => {
    it('should return true for valid breach severities', () => {
      expect(validators.isValidBreachSeverity('low')).toBe(true);
      expect(validators.isValidBreachSeverity('medium')).toBe(true);
      expect(validators.isValidBreachSeverity('high')).toBe(true);
      expect(validators.isValidBreachSeverity('critical')).toBe(true);
    });

    it('should return false for invalid breach severities', () => {
      expect(validators.isValidBreachSeverity('')).toBe(false);
      expect(validators.isValidBreachSeverity('invalid')).toBe(false);
      expect(validators.isValidBreachSeverity('LOW')).toBe(false);
      expect(validators.isValidBreachSeverity('low ')).toBe(false);
      expect(validators.isValidBreachSeverity(' low')).toBe(false);
    });
  });

  describe('isValidNotificationPriority', () => {
    it('should return true for valid notification priorities', () => {
      expect(validators.isValidNotificationPriority('low')).toBe(true);
      expect(validators.isValidNotificationPriority('medium')).toBe(true);
      expect(validators.isValidNotificationPriority('high')).toBe(true);
    });

    it('should return false for invalid notification priorities', () => {
      expect(validators.isValidNotificationPriority('')).toBe(false);
      expect(validators.isValidNotificationPriority('invalid')).toBe(false);
      expect(validators.isValidNotificationPriority('LOW')).toBe(false);
      expect(validators.isValidNotificationPriority('low ')).toBe(false);
      expect(validators.isValidNotificationPriority(' low')).toBe(false);
    });
  });
});
