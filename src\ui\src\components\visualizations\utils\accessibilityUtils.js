/**
 * Accessibility Utilities for Cyber-Safety Visualizations
 * 
 * This module provides utilities to enhance the accessibility of visualizations.
 */

/**
 * Generate a descriptive text summary of visualization data for screen readers
 * @param {string} visualizationType - Type of visualization
 * @param {Object} data - Visualization data
 * @returns {string} - Descriptive text summary
 */
export const generateAccessibleDescription = (visualizationType, data) => {
  if (!data) return 'No data available for this visualization.';
  
  switch (visualizationType) {
    case 'triDomainTensor':
      return generateTriDomainTensorDescription(data);
    case 'harmonyIndex':
      return generateHarmonyIndexDescription(data);
    case 'riskControlFusion':
      return generateRiskControlFusionDescription(data);
    case 'resonanceSpectrogram':
      return generateResonanceSpectrogramDescription(data);
    case 'unifiedComplianceSecurity':
      return generateUnifiedComplianceSecurityDescription(data);
    default:
      return 'Visualization data is available. Please use the interactive controls to explore the data.';
  }
};

/**
 * Generate a descriptive text summary for Tri-Domain Tensor visualization
 * @param {Object} data - Tri-Domain Tensor data
 * @returns {string} - Descriptive text summary
 */
const generateTriDomainTensorDescription = (data) => {
  if (!data) return 'No data available for the Tri-Domain Tensor visualization.';
  
  const domains = Object.keys(data).filter(key => key !== 'connections');
  const connectionCount = data.connections ? data.connections.length : 0;
  
  let description = 'Tri-Domain Tensor Visualization showing the relationship between ';
  description += domains.join(', ') + ' domains. ';
  
  // Add domain health information
  domains.forEach(domain => {
    if (data[domain] && data[domain].health !== undefined) {
      description += `${domain} domain health is ${Math.round(data[domain].health * 100)}%. `;
    }
    
    if (data[domain] && data[domain].entropyContainment !== undefined) {
      description += `${domain} domain entropy containment is ${Math.round(data[domain].entropyContainment * 100)}%. `;
    }
  });
  
  // Add connection information
  description += `There are ${connectionCount} connections between domains. `;
  
  if (connectionCount > 0) {
    // Find strongest connection
    const strongestConnection = [...data.connections].sort((a, b) => b.strength - a.strength)[0];
    description += `The strongest connection is between ${strongestConnection.source} and ${strongestConnection.target} domains with a strength of ${Math.round(strongestConnection.strength * 100)}%. `;
  }
  
  return description;
};

/**
 * Generate a descriptive text summary for Harmony Index visualization
 * @param {Object} data - Harmony Index data
 * @returns {string} - Descriptive text summary
 */
const generateHarmonyIndexDescription = (data) => {
  if (!data) return 'No data available for the Cyber-Safety Harmony Index visualization.';
  
  const { domainData, harmonyHistory } = data;
  const domains = Object.keys(domainData || {});
  
  let description = 'Cyber-Safety Harmony Index Visualization showing alignment between domains. ';
  
  // Add domain score information
  domains.forEach(domain => {
    if (domainData[domain] && domainData[domain].score !== undefined) {
      description += `${domain} domain score is ${Math.round(domainData[domain].score * 100)}%. `;
    }
  });
  
  // Add harmony trend information
  if (harmonyHistory && harmonyHistory.length > 0) {
    const currentHarmony = harmonyHistory[harmonyHistory.length - 1];
    const previousHarmony = harmonyHistory[0];
    const trend = currentHarmony > previousHarmony ? 'increasing' : (currentHarmony < previousHarmony ? 'decreasing' : 'stable');
    
    description += `Overall harmony is ${Math.round(currentHarmony * 100)}% and is ${trend} over time. `;
  }
  
  return description;
};

/**
 * Generate a descriptive text summary for Risk-Control Fusion visualization
 * @param {Object} data - Risk-Control Fusion data
 * @returns {string} - Descriptive text summary
 */
const generateRiskControlFusionDescription = (data) => {
  if (!data) return 'No data available for the Risk-Control Fusion Map visualization.';
  
  const { riskData, controlData } = data;
  const domains = Object.keys(riskData || {});
  
  let description = 'Risk-Control Fusion Map Visualization showing risks and controls across domains. ';
  
  // Calculate average risk and control levels for each domain
  domains.forEach(domain => {
    if (riskData[domain] && controlData[domain]) {
      const riskCategories = Object.keys(riskData[domain]);
      const controlCategories = Object.keys(controlData[domain]);
      
      // Calculate average risk
      const avgRisk = riskCategories.reduce((sum, category) => sum + riskData[domain][category], 0) / riskCategories.length;
      
      // Calculate average control
      const avgControl = controlCategories.reduce((sum, category) => sum + controlData[domain][category], 0) / controlCategories.length;
      
      // Determine coverage status
      const coverageStatus = avgControl >= avgRisk ? 'adequate' : 'inadequate';
      
      description += `${domain} domain has an average risk level of ${Math.round(avgRisk * 100)}% with ${Math.round(avgControl * 100)}% control coverage, which is ${coverageStatus}. `;
    }
  });
  
  return description;
};

/**
 * Generate a descriptive text summary for Resonance Spectrogram visualization
 * @param {Object} data - Resonance Spectrogram data
 * @returns {string} - Descriptive text summary
 */
const generateResonanceSpectrogramDescription = (data) => {
  if (!data) return 'No data available for the Cyber-Safety Resonance Spectrogram visualization.';
  
  const { domainData, predictionData } = data;
  const domains = Object.keys(domainData || {}).filter(key => key !== 'crossDomainFlows');
  
  let description = 'Cyber-Safety Resonance Spectrogram Visualization showing resonance patterns between domains. ';
  
  // Add domain frequency information
  domains.forEach(domain => {
    if (domainData[domain]) {
      const { frequency, amplitude, phase } = domainData[domain];
      
      if (frequency !== undefined && amplitude !== undefined) {
        description += `${domain} domain has a frequency of ${Math.round(frequency * 100)}% and amplitude of ${Math.round(amplitude * 100)}%. `;
      }
    }
  });
  
  // Add prediction information
  if (predictionData) {
    const { dissonanceProbability, criticalPoints } = predictionData;
    
    if (dissonanceProbability !== undefined) {
      description += `The probability of dissonance is ${Math.round(dissonanceProbability * 100)}%. `;
    }
    
    if (criticalPoints && criticalPoints.length > 0) {
      description += `There are ${criticalPoints.length} critical points identified in the prediction horizon. `;
      
      // Find most severe critical point
      const mostSevere = [...criticalPoints].sort((a, b) => b.severity - a.severity)[0];
      description += `The most severe critical point occurs at time step ${mostSevere.timeStep} with a severity of ${Math.round(mostSevere.severity * 100)}%. `;
    }
  }
  
  return description;
};

/**
 * Generate a descriptive text summary for Unified Compliance-Security visualization
 * @param {Object} data - Unified Compliance-Security data
 * @returns {string} - Descriptive text summary
 */
const generateUnifiedComplianceSecurityDescription = (data) => {
  if (!data) return 'No data available for the Unified Compliance-Security Visualizer.';
  
  const { complianceData, impactAnalysis } = data;
  
  let description = 'Unified Compliance-Security Visualizer showing how compliance requirements map to security controls. ';
  
  // Add compliance data information
  if (complianceData) {
    const { requirements, controls, implementations, links } = complianceData;
    
    description += `There are ${requirements ? requirements.length : 0} compliance requirements, `;
    description += `${controls ? controls.length : 0} security controls, and `;
    description += `${implementations ? implementations.length : 0} implementations. `;
    
    if (links && links.length > 0) {
      description += `There are ${links.length} connections between requirements, controls, and implementations. `;
    }
  }
  
  // Add impact analysis information
  if (impactAnalysis && impactAnalysis.proposedChanges) {
    description += `There are ${impactAnalysis.proposedChanges.length} proposed changes that could improve compliance and security. `;
    
    if (impactAnalysis.proposedChanges.length > 0) {
      // Find highest impact change
      const highestImpact = [...impactAnalysis.proposedChanges].sort((a, b) => b.impact - a.impact)[0];
      description += `The highest impact change is "${highestImpact.description}" with an impact of ${Math.round(highestImpact.impact * 100)}%. `;
    }
  }
  
  return description;
};

/**
 * Generate keyboard navigation instructions for visualizations
 * @param {string} visualizationType - Type of visualization
 * @returns {string} - Keyboard navigation instructions
 */
export const generateKeyboardInstructions = (visualizationType) => {
  const commonInstructions = [
    'Use Tab key to navigate between interactive elements.',
    'Use Enter or Space to activate buttons and controls.',
    'Use Escape to exit fullscreen mode or close dialogs.'
  ];
  
  const specificInstructions = {
    triDomainTensor: [
      'Use arrow keys to rotate the 3D tensor visualization.',
      'Use + and - keys to zoom in and out.',
      'Use number keys 1-3 to focus on specific domains.'
    ],
    harmonyIndex: [
      'Use left and right arrow keys to navigate through harmony history.',
      'Use up and down arrow keys to switch between domains.'
    ],
    riskControlFusion: [
      'Use arrow keys to navigate the risk-control map.',
      'Use Shift + arrow keys to navigate between risk and control views.'
    ],
    resonanceSpectrogram: [
      'Use arrow keys to navigate the spectrogram.',
      'Use Shift + left/right to move through time.',
      'Use Shift + up/down to adjust frequency scale.'
    ],
    unifiedComplianceSecurity: [
      'Use arrow keys to navigate between nodes.',
      'Use Enter to expand or collapse node details.',
      'Use Shift + arrow keys to navigate between node types.'
    ]
  };
  
  return [
    ...commonInstructions,
    ...(specificInstructions[visualizationType] || [])
  ].join('\n');
};

/**
 * Generate color blind friendly color schemes for visualizations
 * @param {string} visualizationType - Type of visualization
 * @returns {Object} - Color schemes
 */
export const getAccessibleColorScheme = (visualizationType) => {
  // Base color schemes that are color blind friendly
  const colorSchemes = {
    default: ['#0072B2', '#E69F00', '#009E73', '#CC79A7', '#56B4E9', '#D55E00', '#F0E442', '#000000'],
    categorical: ['#0072B2', '#E69F00', '#009E73', '#CC79A7', '#56B4E9', '#D55E00', '#F0E442', '#000000'],
    sequential: ['#f7fbff', '#deebf7', '#c6dbef', '#9ecae1', '#6baed6', '#4292c6', '#2171b5', '#084594'],
    diverging: ['#d73027', '#f46d43', '#fdae61', '#fee090', '#e0f3f8', '#abd9e9', '#74add1', '#4575b4']
  };
  
  // Return specific color scheme based on visualization type
  switch (visualizationType) {
    case 'triDomainTensor':
      return {
        domains: {
          grc: colorSchemes.default[0],
          it: colorSchemes.default[1],
          cybersecurity: colorSchemes.default[2]
        },
        connections: colorSchemes.sequential,
        background: '#ffffff'
      };
    
    case 'harmonyIndex':
      return {
        domains: {
          grc: colorSchemes.default[0],
          it: colorSchemes.default[1],
          cybersecurity: colorSchemes.default[2]
        },
        history: colorSchemes.default[3],
        background: '#ffffff'
      };
    
    case 'riskControlFusion':
      return {
        risk: colorSchemes.diverging.slice(0, 4),
        control: colorSchemes.diverging.slice(4),
        background: '#ffffff'
      };
    
    case 'resonanceSpectrogram':
      return {
        domains: {
          grc: colorSchemes.default[0],
          it: colorSchemes.default[1],
          cybersecurity: colorSchemes.default[2]
        },
        spectrogram: colorSchemes.sequential,
        background: '#ffffff'
      };
    
    case 'unifiedComplianceSecurity':
      return {
        requirements: colorSchemes.default[0],
        controls: colorSchemes.default[1],
        implementations: colorSchemes.default[2],
        links: colorSchemes.default[7],
        background: '#ffffff'
      };
    
    default:
      return {
        primary: colorSchemes.default,
        background: '#ffffff'
      };
  }
};

/**
 * Higher-order component (HOC) to add accessibility features to visualizations
 * @param {React.Component} WrappedComponent - Component to wrap
 * @returns {React.Component} - Wrapped component with accessibility features
 */
export const withAccessibility = (WrappedComponent) => {
  return function AccessibleVisualization(props) {
    const { visualizationType, ...rest } = props;
    
    // Generate accessible description
    const accessibleDescription = generateAccessibleDescription(visualizationType, props.data);
    
    // Generate keyboard instructions
    const keyboardInstructions = generateKeyboardInstructions(visualizationType);
    
    // Get accessible color scheme
    const colorScheme = getAccessibleColorScheme(visualizationType);
    
    // Add accessibility props
    const accessibilityProps = {
      ...rest,
      visualizationType,
      'aria-label': `${visualizationType} Visualization`,
      'aria-description': accessibleDescription,
      role: 'figure',
      tabIndex: 0,
      colorScheme,
      keyboardInstructions
    };
    
    return <WrappedComponent {...accessibilityProps} />;
  };
};
