/**
 * Health Endpoint API Tests
 * 
 * Tests for the API health check endpoint.
 */

const { request, app } = require('./setup');

describe('Health Endpoint', () => {
  it('should return 200 OK with status information', async () => {
    const response = await request(app)
      .get('/')
      .expect('Content-Type', /json/)
      .expect(200);
    
    // Verify response structure
    expect(response.body).toHaveProperty('message');
    expect(response.body).toHaveProperty('version');
    expect(response.body).toHaveProperty('components');
    expect(response.body.components).toHaveProperty('novaConnect');
    expect(response.body.components).toHaveProperty('novaMarketplace');
  });
});

describe('API Health Check', () => {
  it('should return 200 OK for the marketplace API health check', async () => {
    const response = await request(app)
      .get('/api/marketplace')
      .expect('Content-Type', /json/)
      .expect(200);
    
    // Verify response structure
    expect(response.body).toHaveProperty('message');
    expect(response.body).toHaveProperty('categories');
    expect(Array.isArray(response.body.categories)).toBe(true);
  });

  it('should return 200 OK for the connect API health check', async () => {
    const response = await request(app)
      .get('/api/connect')
      .expect('Content-Type', /json/)
      .expect(200);
    
    // Verify response structure
    expect(response.body).toHaveProperty('message');
    expect(response.body).toHaveProperty('status');
    expect(response.body).toHaveProperty('features');
    expect(response.body).toHaveProperty('endpoints');
    expect(Array.isArray(response.body.features)).toBe(true);
    expect(Array.isArray(response.body.endpoints)).toBe(true);
  });
});
