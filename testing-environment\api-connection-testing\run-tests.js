/**
 * NovaConnect API Connection Test Runner
 * 
 * This script runs all API connection tests for the Universal API Connector.
 */

const path = require('path');
const { spawn } = require('child_process');
const { startAllServices, stopAllServices } = require('./setup');

// Configuration
const testFiles = [
  'authentication.test.js',
  'data-transformation.test.js',
  'error-handling.test.js',
  'parameter-handling.test.js',
  'rate-limiting.test.js'
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  
  black: '\x1b[30m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  
  bgBlack: '\x1b[40m',
  bgRed: '\x1b[41m',
  bgGreen: '\x1b[42m',
  bgYellow: '\x1b[43m',
  bgBlue: '\x1b[44m',
  bgMagenta: '\x1b[45m',
  bgCyan: '\x1b[46m',
  bgWhite: '\x1b[47m'
};

/**
 * Run a test file
 * 
 * @param {string} testFile - The test file to run
 * @returns {Promise<boolean>} - True if the test passed, false otherwise
 */
function runTest(testFile) {
  return new Promise((resolve) => {
    console.log(`\n${colors.bright}${colors.cyan}Running test: ${testFile}${colors.reset}\n`);
    
    const testPath = path.join(__dirname, 'tests', testFile);
    const jest = spawn('npx', ['jest', testPath, '--verbose'], {
      stdio: 'inherit'
    });
    
    jest.on('close', (code) => {
      const passed = code === 0;
      
      if (passed) {
        console.log(`\n${colors.bright}${colors.green}✓ Test passed: ${testFile}${colors.reset}\n`);
      } else {
        console.log(`\n${colors.bright}${colors.red}✗ Test failed: ${testFile}${colors.reset}\n`);
      }
      
      resolve(passed);
    });
  });
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log(`\n${colors.bright}${colors.magenta}=== NovaConnect API Connection Tests ===${colors.reset}\n`);
  
  try {
    // Start all services
    console.log(`${colors.bright}${colors.yellow}Starting services...${colors.reset}`);
    await startAllServices();
    
    // Run each test file
    const results = [];
    
    for (const testFile of testFiles) {
      const passed = await runTest(testFile);
      results.push({ testFile, passed });
    }
    
    // Print summary
    console.log(`\n${colors.bright}${colors.magenta}=== Test Summary ===${colors.reset}\n`);
    
    let passCount = 0;
    let failCount = 0;
    
    for (const { testFile, passed } of results) {
      if (passed) {
        console.log(`${colors.green}✓ ${testFile}${colors.reset}`);
        passCount++;
      } else {
        console.log(`${colors.red}✗ ${testFile}${colors.reset}`);
        failCount++;
      }
    }
    
    const totalTests = results.length;
    const passRate = (passCount / totalTests) * 100;
    
    console.log(`\n${colors.bright}${colors.white}Total: ${totalTests}${colors.reset}`);
    console.log(`${colors.bright}${colors.green}Passed: ${passCount} (${passRate.toFixed(2)}%)${colors.reset}`);
    console.log(`${colors.bright}${colors.red}Failed: ${failCount}${colors.reset}`);
    
    // Set exit code based on test results
    if (failCount > 0) {
      process.exitCode = 1;
    }
  } catch (error) {
    console.error(`\n${colors.bright}${colors.red}Error running tests: ${error.message}${colors.reset}`);
    process.exitCode = 1;
  } finally {
    // Stop all services
    console.log(`\n${colors.bright}${colors.yellow}Stopping services...${colors.reset}`);
    stopAllServices();
  }
}

// Run all tests
runAllTests();
