/**
 * Performance Benchmarking Framework for NovaConnect Universal API Connector
 * 
 * This module provides performance benchmarking capabilities.
 */

const { performance } = require('perf_hooks');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

class PerformanceBenchmark {
  /**
   * Create a new PerformanceBenchmark
   * 
   * @param {Object} options - Benchmark options
   */
  constructor(options = {}) {
    this.options = {
      outputDir: options.outputDir || 'benchmarks',
      historySize: options.historySize || 10,
      thresholds: options.thresholds || {
        authHandshake: 500, // ms
        transformation: 200, // ms
        encryption: 50, // ms
        decryption: 50, // ms
        apiRequest: 1000 // ms
      }
    };
    
    this.benchmarkHistory = {};
    this.currentRun = {
      timestamp: new Date().toISOString(),
      metrics: {}
    };
    
    if (!fs.existsSync(this.options.outputDir)) {
      fs.mkdirSync(this.options.outputDir, { recursive: true });
    }
    
    this.loadHistory();
  }

  /**
   * Load benchmark history
   */
  loadHistory() {
    const historyPath = path.join(this.options.outputDir, 'benchmark-history.json');
    
    if (fs.existsSync(historyPath)) {
      try {
        this.benchmarkHistory = JSON.parse(fs.readFileSync(historyPath, 'utf8'));
      } catch (error) {
        console.error('Failed to load benchmark history:', error.message);
        this.benchmarkHistory = {};
      }
    }
  }

  /**
   * Save benchmark history
   * 
   * @returns {string} - Path to the benchmark report
   */
  saveHistory() {
    const historyPath = path.join(this.options.outputDir, 'benchmark-history.json');
    
    // Add current run to history
    for (const [metricName, metricData] of Object.entries(this.currentRun.metrics)) {
      if (!this.benchmarkHistory[metricName]) {
        this.benchmarkHistory[metricName] = [];
      }
      
      this.benchmarkHistory[metricName].push({
        timestamp: this.currentRun.timestamp,
        ...metricData
      });
      
      // Limit history size
      if (this.benchmarkHistory[metricName].length > this.options.historySize) {
        this.benchmarkHistory[metricName] = this.benchmarkHistory[metricName].slice(-this.options.historySize);
      }
    }
    
    fs.writeFileSync(historyPath, JSON.stringify(this.benchmarkHistory, null, 2));
    
    // Save current run
    const runPath = path.join(this.options.outputDir, `benchmark-${this.currentRun.timestamp.replace(/[:.]/g, '-')}.json`);
    fs.writeFileSync(runPath, JSON.stringify(this.currentRun, null, 2));
    
    return runPath;
  }

  /**
   * Measure the performance of a function
   * 
   * @param {string} name - Metric name
   * @param {Function} fn - Function to measure
   * @param {...any} args - Arguments to pass to the function
   * @returns {Object} - Measurement result
   */
  async measure(name, fn, ...args) {
    const start = performance.now();
    let result;
    let error;
    
    try {
      result = await fn(...args);
    } catch (err) {
      error = err;
    }
    
    const end = performance.now();
    const duration = end - start;
    
    // Record metric
    this.currentRun.metrics[name] = {
      duration,
      success: !error,
      threshold: this.options.thresholds[name] || null,
      exceedsThreshold: this.options.thresholds[name] ? duration > this.options.thresholds[name] : false
    };
    
    if (error) {
      throw error;
    }
    
    return {
      result,
      duration,
      exceedsThreshold: this.currentRun.metrics[name].exceedsThreshold
    };
  }

  /**
   * Get trend data for a metric
   * 
   * @param {string} metricName - Metric name
   * @returns {Object|null} - Trend data
   */
  getMetricTrend(metricName) {
    if (!this.benchmarkHistory[metricName]) {
      return null;
    }
    
    const history = this.benchmarkHistory[metricName];
    
    return {
      metricName,
      current: history[history.length - 1].duration,
      min: Math.min(...history.map(h => h.duration)),
      max: Math.max(...history.map(h => h.duration)),
      avg: history.reduce((sum, h) => sum + h.duration, 0) / history.length,
      trend: history.length > 1 ? 
        (history[history.length - 1].duration - history[history.length - 2].duration) : 0
    };
  }

  /**
   * Generate a benchmark report
   * 
   * @returns {Object} - Benchmark report
   */
  generateReport() {
    const report = {
      timestamp: this.currentRun.timestamp,
      metrics: {},
      thresholdViolations: []
    };
    
    for (const [metricName, metricData] of Object.entries(this.currentRun.metrics)) {
      report.metrics[metricName] = {
        ...metricData,
        trend: this.getMetricTrend(metricName)
      };
      
      if (metricData.exceedsThreshold) {
        report.thresholdViolations.push({
          metricName,
          duration: metricData.duration,
          threshold: metricData.threshold
        });
      }
    }
    
    return report;
  }

  /**
   * Generate an HTML report
   * 
   * @returns {string} - Path to the HTML report
   */
  generateHtmlReport() {
    const report = this.generateReport();
    
    const html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>NovaConnect Performance Benchmark</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
          }
          h1, h2, h3 {
            color: #0066cc;
          }
          .metric {
            background: #f5f5f5;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .metric.exceeds {
            background: #ffe6e6;
            border-left: 5px solid #cc0000;
          }
          .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
          }
          .metric-name {
            font-size: 1.2em;
            font-weight: bold;
          }
          .metric-duration {
            font-size: 1.2em;
            font-weight: bold;
          }
          .metric-threshold {
            color: #666;
          }
          .metric-trend {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
          }
          .trend-value {
            font-weight: bold;
          }
          .trend-value.positive {
            color: #cc0000;
          }
          .trend-value.negative {
            color: #00cc00;
          }
          .timestamp {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 20px;
          }
          .violations {
            margin-top: 30px;
          }
          .violations h2 {
            color: #cc0000;
          }
        </style>
      </head>
      <body>
        <h1>NovaConnect Performance Benchmark</h1>
        <div class="timestamp">Generated on: ${new Date(report.timestamp).toLocaleString()}</div>
        
        <h2>Metrics</h2>
        ${Object.entries(report.metrics).map(([name, data]) => `
          <div class="metric ${data.exceedsThreshold ? 'exceeds' : ''}">
            <div class="metric-header">
              <div class="metric-name">${name}</div>
              <div class="metric-duration">${data.duration.toFixed(2)} ms</div>
            </div>
            <div class="metric-threshold">
              Threshold: ${data.threshold ? `${data.threshold} ms` : 'None'}
              ${data.exceedsThreshold ? ' (Exceeded)' : ''}
            </div>
            ${data.trend ? `
              <div class="metric-trend">
                <div>Trend: <span class="trend-value ${data.trend.trend > 0 ? 'positive' : 'negative'}">${data.trend.trend > 0 ? '+' : ''}${data.trend.trend.toFixed(2)} ms</span></div>
                <div>Min: ${data.trend.min.toFixed(2)} ms</div>
                <div>Max: ${data.trend.max.toFixed(2)} ms</div>
                <div>Avg: ${data.trend.avg.toFixed(2)} ms</div>
              </div>
            ` : ''}
          </div>
        `).join('')}
        
        ${report.thresholdViolations.length > 0 ? `
          <div class="violations">
            <h2>Threshold Violations (${report.thresholdViolations.length})</h2>
            <ul>
              ${report.thresholdViolations.map(violation => `
                <li>
                  <strong>${violation.metricName}</strong>: ${violation.duration.toFixed(2)} ms 
                  (Threshold: ${violation.threshold} ms)
                </li>
              `).join('')}
            </ul>
          </div>
        ` : ''}
      </body>
      </html>
    `;
    
    const reportPath = path.join(this.options.outputDir, 'benchmark-report.html');
    fs.writeFileSync(reportPath, html);
    
    return reportPath;
  }
}

module.exports = PerformanceBenchmark;
