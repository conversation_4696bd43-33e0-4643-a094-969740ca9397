/**
 * Comphyon System Tests
 * 
 * This script runs tests for the Comphyon System.
 */

const { TestRunner } = require('./test-framework');
const { createComphyonSystemTestSuite } = require('./comphyon-system-tests');

/**
 * Run system tests
 */
async function runTests() {
  console.log('=== Comphyon System Tests ===\n');
  
  // Create test runner
  const testRunner = new TestRunner({
    enableLogging: true,
    parallelSuites: false
  });
  
  // Add test suite
  testRunner.addSuite(createComphyonSystemTestSuite());
  
  // Run tests
  try {
    const result = await testRunner.run();
    
    // Exit with appropriate code
    process.exit(result.failed > 0 ? 1 : 0);
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run tests
runTests();
