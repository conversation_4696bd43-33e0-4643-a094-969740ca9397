import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Alert, 
  IconButton, 
  Collapse,
  Button
} from '@mui/material';
import {
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  ContentCopy as ContentCopyIcon
} from '@mui/icons-material';

/**
 * Test Connection Result Component
 * 
 * This component displays the result of a connection test.
 */
const TestConnectionResult = ({ result, onClose }) => {
  const [expanded, setExpanded] = useState(false);

  // Handle copying response to clipboard
  const handleCopyResponse = () => {
    if (result) {
      navigator.clipboard.writeText(JSON.stringify(result, null, 2));
    }
  };

  return (
    <Box>
      <Alert 
        severity={result.success ? "success" : "error"}
        action={
          <IconButton
            aria-label="close"
            color="inherit"
            size="small"
            onClick={onClose}
          >
            <CloseIcon fontSize="inherit" />
          </IconButton>
        }
        sx={{ mb: 2 }}
      >
        <Typography variant="subtitle2">
          {result.success ? 'Connection Successful!' : 'Connection Failed'}
        </Typography>
        <Typography variant="body2">
          {result.message}
        </Typography>
      </Alert>
      
      {/* Response Details */}
      <Box sx={{ mb: 2 }}>
        <Button
          variant="text"
          size="small"
          endIcon={expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          onClick={() => setExpanded(!expanded)}
        >
          {expanded ? 'Hide Details' : 'Show Details'}
        </Button>
      </Box>
      
      <Collapse in={expanded}>
        <Paper variant="outlined" sx={{ p: 2, backgroundColor: 'background.default' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle2">Response Details</Typography>
            <Button
              variant="text"
              size="small"
              startIcon={<ContentCopyIcon />}
              onClick={handleCopyResponse}
            >
              Copy
            </Button>
          </Box>
          
          {result.success ? (
            <Box>
              <Typography variant="body2" component="div">
                <strong>Status:</strong> {result.data?.status || 'N/A'}
              </Typography>
              <Typography variant="body2" component="div">
                <strong>Status Text:</strong> {result.data?.statusText || 'N/A'}
              </Typography>
              {result.data?.headers && (
                <Box sx={{ mt: 1 }}>
                  <Typography variant="body2" component="div">
                    <strong>Headers:</strong>
                  </Typography>
                  <Box 
                    component="pre" 
                    sx={{ 
                      mt: 1, 
                      p: 1, 
                      backgroundColor: 'rgba(0,0,0,0.04)', 
                      borderRadius: 1,
                      fontSize: '0.75rem',
                      overflow: 'auto',
                      maxHeight: '150px'
                    }}
                  >
                    {JSON.stringify(result.data.headers, null, 2)}
                  </Box>
                </Box>
              )}
              {result.data?.data && (
                <Box sx={{ mt: 1 }}>
                  <Typography variant="body2" component="div">
                    <strong>Response Body:</strong>
                  </Typography>
                  <Box 
                    component="pre" 
                    sx={{ 
                      mt: 1, 
                      p: 1, 
                      backgroundColor: 'rgba(0,0,0,0.04)', 
                      borderRadius: 1,
                      fontSize: '0.75rem',
                      overflow: 'auto',
                      maxHeight: '200px'
                    }}
                  >
                    {typeof result.data.data === 'object' 
                      ? JSON.stringify(result.data.data, null, 2) 
                      : result.data.data}
                  </Box>
                </Box>
              )}
            </Box>
          ) : (
            <Box>
              {result.error && (
                <Box sx={{ mt: 1 }}>
                  <Typography variant="body2" component="div">
                    <strong>Error Details:</strong>
                  </Typography>
                  <Box 
                    component="pre" 
                    sx={{ 
                      mt: 1, 
                      p: 1, 
                      backgroundColor: 'rgba(255,0,0,0.04)', 
                      borderRadius: 1,
                      fontSize: '0.75rem',
                      overflow: 'auto',
                      maxHeight: '200px',
                      color: 'error.main'
                    }}
                  >
                    {typeof result.error === 'object' 
                      ? JSON.stringify(result.error, null, 2) 
                      : result.error}
                  </Box>
                </Box>
              )}
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Check your authentication credentials and try again. If the problem persists, verify that the API endpoint is correct and accessible.
              </Typography>
            </Box>
          )}
        </Paper>
      </Collapse>
    </Box>
  );
};

export default TestConnectionResult;
