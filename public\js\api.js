/**
 * API Client for the Privacy Management API
 */
class ApiClient {
  constructor() {
    this.baseUrl = '/privacy/management';
    this.token = localStorage.getItem('token');
  }

  /**
   * Set the authentication token
   * @param {string} token - JWT token
   */
  setToken(token) {
    this.token = token;
    localStorage.setItem('token', token);
  }

  /**
   * Clear the authentication token
   */
  clearToken() {
    this.token = null;
    localStorage.removeItem('token');
  }

  /**
   * Check if the user is authenticated
   * @returns {boolean} - Whether the user is authenticated
   */
  isAuthenticated() {
    return !!this.token;
  }

  /**
   * Get the headers for API requests
   * @returns {Object} - Headers object
   */
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json'
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  /**
   * Make an API request
   * @param {string} method - HTTP method
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @returns {Promise<Object>} - Response data
   */
  async request(method, endpoint, data = null) {
    const url = `${this.baseUrl}${endpoint}`;
    const options = {
      method,
      headers: this.getHeaders()
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);

      // Handle authentication errors
      if (response.status === 401) {
        this.clearToken();
        window.location.reload();
        throw new Error('Authentication failed');
      }

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.message || 'API request failed');
      }

      return responseData;
    } catch (error) {
      console.error('API request error:', error);
      throw error;
    }
  }

  /**
   * Login to the API
   * @param {string} username - Username
   * @param {string} password - Password
   * @returns {Promise<Object>} - Login result
   */
  async login(username, password) {
    const response = await this.request('POST', '/auth/login', { username, password });
    
    if (response.token) {
      this.setToken(response.token);
    }
    
    return response;
  }

  /**
   * Logout from the API
   */
  logout() {
    this.clearToken();
  }

  /**
   * Get dashboard metrics
   * @returns {Promise<Object>} - Dashboard metrics
   */
  async getDashboardMetrics() {
    return this.request('GET', '/dashboard/metrics');
  }

  /**
   * Get data processing activities
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Data processing activities
   */
  async getDataProcessingActivities(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request('GET', `/processing-activities${queryString ? `?${queryString}` : ''}`);
  }

  /**
   * Get data subject requests
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Data subject requests
   */
  async getDataSubjectRequests(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request('GET', `/subject-requests${queryString ? `?${queryString}` : ''}`);
  }

  /**
   * Get consent records
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Consent records
   */
  async getConsentRecords(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request('GET', `/consent-records${queryString ? `?${queryString}` : ''}`);
  }

  /**
   * Get privacy notices
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Privacy notices
   */
  async getPrivacyNotices(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request('GET', `/privacy-notices${queryString ? `?${queryString}` : ''}`);
  }

  /**
   * Get data breaches
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Data breaches
   */
  async getDataBreaches(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request('GET', `/data-breaches${queryString ? `?${queryString}` : ''}`);
  }

  /**
   * Get notifications
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Notifications
   */
  async getNotifications(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request('GET', `/notifications${queryString ? `?${queryString}` : ''}`);
  }

  /**
   * Generate a report
   * @param {string} reportType - Report type
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Report data
   */
  async generateReport(reportType, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request('GET', `/reports/${reportType}${queryString ? `?${queryString}` : ''}`);
  }

  /**
   * Get integrations
   * @returns {Promise<Object>} - Integrations
   */
  async getIntegrations() {
    return this.request('GET', '/integrations');
  }

  /**
   * Execute an integration action
   * @param {string} integrationId - Integration ID
   * @param {string} action - Action to execute
   * @param {Object} data - Action data
   * @returns {Promise<Object>} - Action result
   */
  async executeIntegrationAction(integrationId, action, data) {
    return this.request('POST', `/integrations/${integrationId}/${action}`, data);
  }
}

// Create a global API client instance
const api = new ApiClient();
