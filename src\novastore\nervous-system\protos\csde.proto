syntax = "proto3";

package novafuse.csde;

// CSDE Service - Implements the Cyber-Safety Domain Engine with sub-millisecond latency
service CSEDService {
  // Calculate CSDE value for a given compliance scenario
  rpc CalculateCSDE(CalculateCSEDRequest) returns (CalculateCSEDResponse);
  
  // Process a security event in real-time
  rpc ProcessEvent(ProcessEventRequest) returns (ProcessEventResponse);
  
  // Execute remediation actions with π10³ scaling
  rpc ExecuteRemediation(ExecuteRemediationRequest) returns (ExecuteRemediationResponse);
  
  // Get metrics for the CSDE service
  rpc GetMetrics(GetMetricsRequest) returns (GetMetricsResponse);
  
  // Stream security events for real-time processing
  rpc StreamEvents(stream EventStreamRequest) returns (stream EventStreamResponse);
  
  // Create and manage Wilson loops for closed-loop validation
  rpc ManageWilsonLoop(WilsonLoopRequest) returns (WilsonLoopResponse);
}

// Request to calculate CSDE value
message CalculateCSEDRequest {
  // Client identifier
  string client_id = 1;
  
  // Client name
  string client_name = 2;
  
  // Compliance data (JSON string)
  string compliance_data = 3;
  
  // GCP integration data (JSON string)
  string gcp_data = 4;
  
  // Cyber-Safety data (JSON string)
  string cyber_safety_data = 5;
  
  // Options for calculation
  CalculationOptions options = 6;
}

// Options for CSDE calculation
message CalculationOptions {
  // Enable Wilson loop enforcement
  bool enable_wilson_loop = 1;
  
  // Enable remediation generation
  bool enable_remediation = 2;
  
  // Enable caching
  bool enable_caching = 3;
  
  // Domain for calculation
  string domain = 4;
  
  // Optimization level (1-5)
  int32 optimization_level = 5;
}

// Response from CSDE calculation
message CalculateCSEDResponse {
  // CSDE value
  double csde_value = 1;
  
  // Performance factor (3,142x)
  int32 performance_factor = 2;
  
  // Remediation actions (JSON string)
  string remediation_actions = 3;
  
  // Wilson loop ID (if enabled)
  string wilson_loop_id = 4;
  
  // Processing time in milliseconds
  double processing_time = 5;
}

// Request to process a security event
message ProcessEventRequest {
  // Client identifier
  string client_id = 1;
  
  // Event data (JSON string)
  string event_data = 2;
  
  // Event type
  string event_type = 3;
  
  // Event severity
  EventSeverity severity = 4;
  
  // Event timestamp
  int64 timestamp = 5;
}

// Event severity levels
enum EventSeverity {
  UNKNOWN = 0;
  LOW = 1;
  MEDIUM = 2;
  HIGH = 3;
  CRITICAL = 4;
}

// Response from event processing
message ProcessEventResponse {
  // Whether the event was processed successfully
  bool success = 1;
  
  // Event ID
  string event_id = 2;
  
  // Remediation actions (JSON string)
  string remediation_actions = 3;
  
  // Wilson loop ID (if enabled)
  string wilson_loop_id = 4;
  
  // Processing time in milliseconds
  double processing_time = 5;
}

// Request to execute remediation actions
message ExecuteRemediationRequest {
  // Client identifier
  string client_id = 1;
  
  // Remediation actions (JSON string)
  string remediation_actions = 2;
  
  // Wilson loop ID (if available)
  string wilson_loop_id = 3;
  
  // Scaling factor (default: π10³)
  double scaling_factor = 4;
}

// Response from remediation execution
message ExecuteRemediationResponse {
  // Whether the remediation was executed successfully
  bool success = 1;
  
  // Execution ID
  string execution_id = 2;
  
  // Number of actions executed
  int32 actions_executed = 3;
  
  // Number of actions failed
  int32 actions_failed = 4;
  
  // Execution time in milliseconds
  double execution_time = 5;
}

// Request to get metrics
message GetMetricsRequest {
  // Client identifier
  string client_id = 1;
  
  // Metrics types to include
  repeated string metric_types = 2;
}

// Response with metrics
message GetMetricsResponse {
  // Physics tier metrics (JSON string)
  string physics_metrics = 1;
  
  // Transition tier metrics (JSON string)
  string transition_metrics = 2;
  
  // Legacy tier metrics (JSON string)
  string legacy_metrics = 3;
}

// Request for event streaming
message EventStreamRequest {
  // Client identifier
  string client_id = 1;
  
  // Event data (JSON string)
  string event_data = 2;
  
  // Event type
  string event_type = 3;
  
  // Event severity
  EventSeverity severity = 4;
  
  // Event timestamp
  int64 timestamp = 5;
}

// Response for event streaming
message EventStreamResponse {
  // Whether the event was processed successfully
  bool success = 1;
  
  // Event ID
  string event_id = 2;
  
  // Remediation actions (JSON string)
  string remediation_actions = 3;
  
  // Wilson loop ID (if enabled)
  string wilson_loop_id = 4;
  
  // Processing time in milliseconds
  double processing_time = 5;
}

// Request for Wilson loop management
message WilsonLoopRequest {
  // Client identifier
  string client_id = 1;
  
  // Wilson loop ID
  string wilson_loop_id = 2;
  
  // Operation type
  WilsonLoopOperation operation = 3;
  
  // Operation data (JSON string)
  string operation_data = 4;
}

// Wilson loop operation types
enum WilsonLoopOperation {
  CREATE = 0;
  UPDATE = 1;
  CLOSE = 2;
  VALIDATE = 3;
  GET = 4;
}

// Response for Wilson loop management
message WilsonLoopResponse {
  // Whether the operation was successful
  bool success = 1;
  
  // Wilson loop ID
  string wilson_loop_id = 2;
  
  // Wilson loop status
  string status = 3;
  
  // Wilson loop data (JSON string)
  string loop_data = 4;
}
