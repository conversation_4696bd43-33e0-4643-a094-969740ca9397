/**
 * Policy Controller
 * 
 * This controller handles API requests related to compliance policies.
 */

const PolicyService = require('../services/PolicyService');
const { ValidationError } = require('../utils/errors');

class PolicyController {
  constructor() {
    this.policyService = new PolicyService();
  }

  /**
   * Get all policies
   */
  async getAllPolicies(req, res, next) {
    try {
      const filters = req.query;
      const policies = await this.policyService.getAllPolicies(filters);
      res.json(policies);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get policies for a team
   */
  async getPoliciesForTeam(req, res, next) {
    try {
      const { id } = req.params;
      const filters = req.query;
      
      if (!id) {
        throw new ValidationError('Team ID is required');
      }
      
      const policies = await this.policyService.getPoliciesForTeam(id, filters);
      res.json(policies);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get policy by ID
   */
  async getPolicyById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Policy ID is required');
      }
      
      const policy = await this.policyService.getPolicyById(id);
      res.json(policy);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new policy
   */
  async createPolicy(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Policy data is required');
      }
      
      const policy = await this.policyService.createPolicy(data, req.user.id);
      res.status(201).json(policy);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a policy
   */
  async updatePolicy(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Policy ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Policy data is required');
      }
      
      const policy = await this.policyService.updatePolicy(id, data, req.user.id);
      res.json(policy);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a policy
   */
  async deletePolicy(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Policy ID is required');
      }
      
      const result = await this.policyService.deletePolicy(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Evaluate a resource against policies
   */
  async evaluateResource(req, res, next) {
    try {
      const { resourceType } = req.params;
      const { resource, environmentId, teamId } = req.body;
      
      if (!resourceType) {
        throw new ValidationError('Resource type is required');
      }
      
      if (!resource) {
        throw new ValidationError('Resource data is required');
      }
      
      const result = await this.policyService.evaluateResource(
        resourceType, 
        resource, 
        environmentId, 
        teamId
      );
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get policy violations
   */
  async getPolicyViolations(req, res, next) {
    try {
      const filters = req.query;
      const violations = await this.policyService.getPolicyViolations(filters);
      res.json(violations);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get policy violation by ID
   */
  async getPolicyViolationById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Policy violation ID is required');
      }
      
      const violation = await this.policyService.getPolicyViolationById(id);
      res.json(violation);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update policy violation
   */
  async updatePolicyViolation(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Policy violation ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Policy violation data is required');
      }
      
      const violation = await this.policyService.updatePolicyViolation(id, data, req.user.id);
      res.json(violation);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Record a policy violation
   */
  async recordPolicyViolation(req, res, next) {
    try {
      const { policyId } = req.params;
      const { resourceType, resourceId, violations } = req.body;
      
      if (!policyId) {
        throw new ValidationError('Policy ID is required');
      }
      
      if (!resourceType) {
        throw new ValidationError('Resource type is required');
      }
      
      if (!resourceId) {
        throw new ValidationError('Resource ID is required');
      }
      
      if (!violations || !Array.isArray(violations)) {
        throw new ValidationError('Violations array is required');
      }
      
      const violation = await this.policyService.recordPolicyViolation(
        policyId,
        resourceType,
        resourceId,
        violations,
        req.user.id
      );
      
      res.status(201).json(violation);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new PolicyController();
