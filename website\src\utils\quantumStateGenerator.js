import { QuantumState } from '@novafuse/nova-quantum';

/**
 * Generates a quantum state with the given parameters
 * @param {Object} params - Parameters for state generation
 * @param {number} [params.entropy=0.5] - Entropy level (0-1)
 * @param {number} [params.coherence=0.7] - Coherence level (0-1)
 * @param {number} [params.entanglement=0.3] - Entanglement level (0-1)
 * @param {number} [params.qubits=3] - Number of qubits
 * @returns {QuantumState} Generated quantum state
 */
export const generateQuantumState = ({
  entropy = 0.5,
  coherence = 0.7,
  entanglement = 0.3,
  qubits = 3,
} = {}) => {
  // Create a new quantum state with the specified number of qubits
  const state = new QuantumState(qubits);
  
  // Apply entropy (randomness) to the state
  if (entropy > 0) {
    state.applyEntropy(entropy);
  }
  
  // Apply coherence (purity) to the state
  if (coherence < 1) {
    state.applyDecoherence(1 - coherence);
  }
  
  // Apply entanglement between qubits
  if (entanglement > 0) {
    const maxEntangledPairs = Math.floor(qubits / 2);
    const numEntangledPairs = Math.ceil(entanglement * maxEntangledPairs);
    
    for (let i = 0; i < numEntangledPairs; i++) {
      const q1 = (i * 2) % qubits;
      const q2 = (q1 + 1) % qubits;
      
      if (q1 !== q2) {
        state.entangleQubits(q1, q2, entanglement);
      }
    }
  }
  
  return state;
};

/**
 * Generates an animated sequence of quantum states
 * @param {Object} options - Animation options
 * @param {number} [options.duration=5000] - Animation duration in ms
 * @param {Function} [options.easing=t => t] - Easing function
 * @param {Function} onUpdate - Callback for each frame
 * @returns {Function} Function to cancel the animation
 */
export const animateQuantumState = ({
  duration = 5000,
  easing = t => t,
  onUpdate,
}) => {
  if (typeof onUpdate !== 'function') {
    throw new Error('onUpdate callback is required');
  }
  
  const startTime = performance.now();
  let animationFrameId;
  
  const animate = (currentTime) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    const easedProgress = easing(progress);
    
    // Generate a new state based on the animation progress
    const state = generateQuantumState({
      entropy: 0.3 + 0.4 * Math.sin(easedProgress * Math.PI * 2) ** 2,
      coherence: 0.5 + 0.4 * Math.cos(easedProgress * Math.PI) ** 2,
      entanglement: 0.2 + 0.6 * Math.sin(easedProgress * Math.PI / 2) ** 2,
    });
    
    // Call the update callback with the new state
    onUpdate(state, {
      progress: easedProgress,
      time: elapsed,
      entropy: 0.3 + 0.4 * Math.sin(easedProgress * Math.PI * 2) ** 2,
      coherence: 0.5 + 0.4 * Math.cos(easedProgress * Math.PI) ** 2,
      entanglement: 0.2 + 0.6 * Math.sin(easedProgress * Math.PI / 2) ** 2,
    });
    
    // Continue the animation if not complete
    if (progress < 1) {
      animationFrameId = requestAnimationFrame(animate);
    }
  };
  
  // Start the animation
  animationFrameId = requestAnimationFrame(animate);
  
  // Return a function to cancel the animation
  return () => {
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
    }
  };
};

/**
 * Calculates quantum metrics from a state
 * @param {QuantumState} state - The quantum state to analyze
 * @returns {Object} Calculated metrics
 */
export const calculateQuantumMetrics = (state) => {
  if (!(state instanceof QuantumState)) {
    throw new Error('Invalid quantum state');
  }
  
  const entropy = state.calculateEntropy();
  const coherence = state.calculateCoherence();
  const entanglement = state.calculateEntanglement();
  
  // Calculate a composite quantum score (0-1)
  const qScore = Math.max(0, Math.min(1, 
    (coherence * 0.6 + (1 - entropy) * 0.3 + entanglement * 0.1)
  ));
  
  return {
    entropy,
    coherence,
    entanglement,
    qScore,
    qubits: state.qubitCount,
    dimensions: state.dimensions,
    isPure: state.isPure(),
    isMixed: state.isMixed(),
    isEntangled: state.isEntangled(),
  };
};

// Export a default state for quick initialization
export const DEFAULT_QUANTUM_STATE = generateQuantumState({
  entropy: 0.5,
  coherence: 0.7,
  entanglement: 0.3,
  qubits: 3,
});
