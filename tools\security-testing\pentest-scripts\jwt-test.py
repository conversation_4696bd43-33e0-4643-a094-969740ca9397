#!/usr/bin/env python3
"""
JWT Token Security Testing Script for NovaFuse

This script tests JWT token implementations for common security vulnerabilities:
1. None algorithm vulnerability
2. Weak signature verification
3. Missing expiration claims
4. Sensitive data in token payload
5. Token brute force attacks
"""

import argparse
import json
import jwt
import requests
import time
import sys
from datetime import datetime, timedelta

class JWTSecurityTester:
    def __init__(self, url, token=None, verbose=False):
        self.url = url
        self.token = token
        self.verbose = verbose
        self.results = {
            "none_algorithm": {"vulnerable": False, "details": ""},
            "weak_signature": {"vulnerable": False, "details": ""},
            "missing_expiration": {"vulnerable": False, "details": ""},
            "sensitive_data": {"vulnerable": False, "details": ""},
            "brute_force": {"vulnerable": False, "details": ""}
        }
        
        # If no token provided, try to get one
        if not self.token:
            self.token = self.get_token()
    
    def get_token(self):
        """Attempt to get a JWT token from the API"""
        try:
            # Try common authentication endpoints
            auth_endpoints = [
                "/api/auth/login",
                "/api/v1/auth/login",
                "/auth/login",
                "/login",
                "/api/token"
            ]
            
            # Test credentials
            credentials = {
                "username": "<EMAIL>",
                "password": "Password123!"
            }
            
            for endpoint in auth_endpoints:
                try:
                    response = requests.post(f"{self.url}{endpoint}", json=credentials, timeout=5)
                    if response.status_code == 200:
                        # Look for token in response
                        data = response.json()
                        if "token" in data:
                            return data["token"]
                        elif "access_token" in data:
                            return data["access_token"]
                        elif "jwt" in data:
                            return data["jwt"]
                except Exception as e:
                    if self.verbose:
                        print(f"Error trying endpoint {endpoint}: {str(e)}")
                    continue
            
            print("Could not automatically obtain a JWT token. Please provide one with --token.")
            return None
        except Exception as e:
            print(f"Error getting token: {str(e)}")
            return None
    
    def test_none_algorithm(self):
        """Test for the 'none' algorithm vulnerability"""
        if not self.token:
            return
        
        try:
            # Decode the token without verification to get the payload
            decoded = jwt.decode(self.token, options={"verify_signature": False})
            
            # Create a new token with the 'none' algorithm
            none_token = jwt.encode(decoded, key='', algorithm='none')
            
            # Test if the server accepts this token
            headers = {"Authorization": f"Bearer {none_token}"}
            response = requests.get(f"{self.url}/api/v1/users", headers=headers)
            
            if response.status_code < 400:  # If not an error response
                self.results["none_algorithm"]["vulnerable"] = True
                self.results["none_algorithm"]["details"] = "Server accepted a token signed with the 'none' algorithm"
            else:
                self.results["none_algorithm"]["details"] = "Server rejected token with 'none' algorithm (good)"
        except Exception as e:
            self.results["none_algorithm"]["details"] = f"Error during test: {str(e)}"
    
    def test_weak_signature(self):
        """Test for weak signature verification"""
        if not self.token:
            return
        
        try:
            # Decode the token without verification to get the payload
            decoded = jwt.decode(self.token, options={"verify_signature": False})
            
            # Common weak secrets to try
            weak_secrets = ["secret", "key", "password", "1234567890", "SECRET_KEY", "jwt_secret", "your-secret-key"]
            
            for secret in weak_secrets:
                try:
                    # Create a new token with the weak secret
                    weak_token = jwt.encode(decoded, secret, algorithm='HS256')
                    
                    # Test if the server accepts this token
                    headers = {"Authorization": f"Bearer {weak_token}"}
                    response = requests.get(f"{self.url}/api/v1/users", headers=headers)
                    
                    if response.status_code < 400:  # If not an error response
                        self.results["weak_signature"]["vulnerable"] = True
                        self.results["weak_signature"]["details"] = f"Server accepted a token signed with a weak secret: '{secret}'"
                        break
                except:
                    continue
            
            if not self.results["weak_signature"]["vulnerable"]:
                self.results["weak_signature"]["details"] = "Server rejected tokens with weak signatures (good)"
        except Exception as e:
            self.results["weak_signature"]["details"] = f"Error during test: {str(e)}"
    
    def test_missing_expiration(self):
        """Test for missing expiration claim"""
        if not self.token:
            return
        
        try:
            # Decode the token without verification to get the payload
            decoded = jwt.decode(self.token, options={"verify_signature": False})
            
            # Check if expiration claim exists
            if "exp" not in decoded:
                self.results["missing_expiration"]["vulnerable"] = True
                self.results["missing_expiration"]["details"] = "Token does not contain an expiration claim"
            else:
                # Check if expiration is reasonable (not too far in the future)
                exp_time = datetime.fromtimestamp(decoded["exp"])
                now = datetime.now()
                if exp_time > now + timedelta(days=30):
                    self.results["missing_expiration"]["vulnerable"] = True
                    self.results["missing_expiration"]["details"] = f"Token has a very long expiration time: {exp_time}"
                else:
                    self.results["missing_expiration"]["details"] = f"Token has a reasonable expiration time: {exp_time}"
        except Exception as e:
            self.results["missing_expiration"]["details"] = f"Error during test: {str(e)}"
    
    def test_sensitive_data(self):
        """Test for sensitive data in token payload"""
        if not self.token:
            return
        
        try:
            # Decode the token without verification to get the payload
            decoded = jwt.decode(self.token, options={"verify_signature": False})
            
            # Check for common sensitive data patterns
            sensitive_fields = ["password", "secret", "credit_card", "ssn", "social_security", "dob", "birth_date", "address"]
            found_sensitive = []
            
            for key in decoded:
                key_lower = key.lower()
                for field in sensitive_fields:
                    if field in key_lower:
                        found_sensitive.append(key)
            
            if found_sensitive:
                self.results["sensitive_data"]["vulnerable"] = True
                self.results["sensitive_data"]["details"] = f"Token contains potentially sensitive data in fields: {', '.join(found_sensitive)}"
            else:
                self.results["sensitive_data"]["details"] = "No obvious sensitive data found in token payload"
        except Exception as e:
            self.results["sensitive_data"]["details"] = f"Error during test: {str(e)}"
    
    def run_all_tests(self):
        """Run all JWT security tests"""
        if not self.token:
            print("No JWT token available. Cannot run tests.")
            return
        
        print(f"Testing JWT token: {self.token[:20]}...")
        
        print("1. Testing for 'none' algorithm vulnerability...")
        self.test_none_algorithm()
        
        print("2. Testing for weak signature verification...")
        self.test_weak_signature()
        
        print("3. Testing for missing expiration claim...")
        self.test_missing_expiration()
        
        print("4. Testing for sensitive data in token payload...")
        self.test_sensitive_data()
        
        return self.results
    
    def print_results(self):
        """Print test results in a readable format"""
        print("\n===== JWT Security Test Results =====")
        
        vulnerable_count = sum(1 for test in self.results.values() if test["vulnerable"])
        
        if vulnerable_count == 0:
            print("✅ No JWT vulnerabilities detected")
        else:
            print(f"❌ Found {vulnerable_count} potential JWT vulnerabilities")
        
        for test_name, result in self.results.items():
            status = "❌ VULNERABLE" if result["vulnerable"] else "✅ SECURE"
            print(f"\n{test_name.replace('_', ' ').upper()}: {status}")
            print(f"  {result['details']}")
        
        print("\n===================================")

def main():
    parser = argparse.ArgumentParser(description="JWT Token Security Testing Tool for NovaFuse")
    parser.add_argument("--url", required=True, help="Target API URL")
    parser.add_argument("--token", help="JWT token to test (optional)")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    
    args = parser.parse_args()
    
    tester = JWTSecurityTester(args.url, args.token, args.verbose)
    tester.run_all_tests()
    tester.print_results()

if __name__ == "__main__":
    main()
