/**
 * <PERSON>ert Details Page
 * 
 * This page displays detailed information about a specific alert.
 */

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  CircularProgress, 
  Typography 
} from '@mui/material';
import { useRouter } from 'next/router';
import DashboardLayout from '../../../layouts/DashboardLayout';
import AlertDetails from '../../../components/monitoring/AlertDetails';

const AlertDetailsPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const [alert, setAlert] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    if (id) {
      fetchAlertDetails();
    }
  }, [id]);
  
  const fetchAlertDetails = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // In a real implementation, this would fetch alert details from the API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock alert data
      const mockAlert = {
        id: id,
        connector: {
          id: 'conn1',
          name: 'GitHub API Connector',
          version: '1.0.0',
          category: 'development',
          status: 'active'
        },
        severity: 'high',
        status: 'active',
        message: 'High response time detected',
        description: 'The GitHub API connector is experiencing high response times exceeding the configured threshold.',
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        details: {
          responseTime: 1250,
          threshold: 1000,
          endpoint: '/repos/{owner}/{repo}/issues'
        },
        comments: [
          {
            user: 'System',
            timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
            text: 'Alert created'
          }
        ],
        timeline: [
          {
            timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
            message: 'Alert created'
          }
        ]
      };
      
      setAlert(mockAlert);
    } catch (error) {
      console.error('Error fetching alert details:', error);
      setError('Failed to load alert details. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <DashboardLayout>
      <Container maxWidth="xl">
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Box sx={{ textAlign: 'center', py: 5 }}>
            <Typography variant="h6" color="error" gutterBottom>
              {error}
            </Typography>
            <Typography variant="body1">
              Alert ID: {id}
            </Typography>
          </Box>
        ) : alert ? (
          <AlertDetails alert={alert} />
        ) : (
          <Box sx={{ textAlign: 'center', py: 5 }}>
            <Typography variant="h6" gutterBottom>
              Alert not found
            </Typography>
            <Typography variant="body1">
              The alert with ID {id} could not be found.
            </Typography>
          </Box>
        )}
      </Container>
    </DashboardLayout>
  );
};

export default AlertDetailsPage;
