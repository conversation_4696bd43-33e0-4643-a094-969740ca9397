import React, { useState } from 'react';
import Sidebar from '../components/Sidebar';
import Link from 'next/link';

export default function GooglePartnershipPortal() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [password, setPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const sidebarItems = [
    { type: 'category', label: 'Partnership Portal', items: [
      { label: 'Overview', href: '#overview' },
      { label: 'Partnership Principles', href: '#principles' },
      { label: 'Strategic Options', href: '#options' },
      { label: 'Infrastructure', href: '#infrastructure' },
      { label: 'Strategic Alignment', href: '#alignment' },
      { label: 'Mutual Benefits', href: '#benefits' }
    ]}
  ];

  const handleLogin = (e) => {
    e.preventDefault();
    // This is just for demo purposes - in a real implementation, 
    // this would be handled securely on the server
    if (password === 'GoogleCloud2024') {
      setIsLoggedIn(true);
      setErrorMessage('');
    } else {
      setErrorMessage('Invalid access code. Please try again or contact your NovaGRC representative.');
    }
  };

  return (
    <div>
      {!isLoggedIn ? (
        // Password Protection Overlay
        <div className="fixed inset-0 bg-primary bg-opacity-95 z-50 flex items-center justify-center">
          <div className="bg-secondary p-8 rounded-lg shadow-lg max-w-md w-full">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold mb-2">Google Strategic Partnership Portal</h2>
              <p className="text-gray-400">This page is exclusively for Google Cloud representatives.</p>
            </div>
            
            <form onSubmit={handleLogin}>
              <div className="mb-4">
                <label htmlFor="password" className="block text-gray-300 mb-2">Access Code</label>
                <input 
                  type="password" 
                  id="password" 
                  className="w-full p-3 rounded bg-gray-800 border border-gray-700 text-white" 
                  placeholder="Enter your access code"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
              
              {errorMessage && (
                <div className="mb-4 text-red-500 text-center">
                  {errorMessage}
                </div>
              )}
              
              <button type="submit" className="w-full accent-bg text-white py-3 rounded font-bold hover:bg-blue-700">
                Access Portal
              </button>
            </form>
            
            <div className="mt-6 text-center text-sm text-gray-400">
              <p>Need access? Contact <a href="mailto:<EMAIL>" className="text-blue-400 hover:underline"><EMAIL></a></p>
            </div>
          </div>
        </div>
      ) : (
        // Main Content
        <div className="flex flex-col md:flex-row gap-8">
          {/* Sidebar - Hidden on mobile, shown on desktop */}
          <div className="hidden md:block md:w-1/4 lg:w-1/5">
            <div className="sticky top-4">
              <Sidebar items={sidebarItems} title="Google Partnership" />
            </div>
          </div>

          {/* Mobile Sidebar Toggle - Shown on mobile only */}
          <div className="md:hidden mb-4">
            <select 
              className="w-full bg-secondary text-white border border-gray-700 rounded p-2"
              onChange={(e) => {
                if (e.target.value) window.location.href = e.target.value;
              }}
              defaultValue=""
            >
              <option value="" disabled>Navigate to...</option>
              {sidebarItems.map((item, index) => (
                item.type === 'category' ? (
                  <optgroup key={index} label={item.label}>
                    {item.items.map((subItem, subIndex) => (
                      <option key={`${index}-${subIndex}`} value={subItem.href}>
                        {subItem.label}
                      </option>
                    ))}
                  </optgroup>
                ) : (
                  <option key={index} value={item.href}>
                    {item.label}
                  </option>
                )
              ))}
            </select>
          </div>

          {/* Main Content */}
          <div className="w-full md:w-3/4 lg:w-4/5">
            {/* Welcome Section */}
            <section id="overview" className="bg-secondary p-8 rounded-lg mb-8">
              <h2 className="text-2xl font-bold mb-4">Welcome, Google Cloud Team</h2>
              <p className="text-xl mb-6">
                Thank you for your interest in NovaGRC. This portal provides exclusive access to our strategic partnership plans and infrastructure projections.
              </p>
              <p className="text-gray-300">
                NovaGRC is pioneering Cyber-Safety—an evolution of cybersecurity focused on prevention-first risk management. Our platform integrates AI-Powered Compliance, Gamified User Engagement, and an API-Centric Architecture, all built natively on Google Cloud.
              </p>
            </section>
            
            {/* Google Cloud Partnership Alignment */}
            <section id="principles" className="mb-12">
              <h2 className="text-2xl font-bold mb-6">Alignment with Google Cloud Partnership Principles</h2>
              <p className="text-gray-300 mb-6">
                NovaGRC's approach is fundamentally aligned with Google Cloud's core partnership principles. Here's how we embody these values:
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                <div className="bg-secondary p-6 rounded-lg pl-6 border-l-4 border-blue-600">
                  <h3 className="text-xl font-bold mb-3 text-blue-400">Open Platform</h3>
                  <p className="text-gray-300 mb-4">
                    NovaFuse API Superstore is built on open standards with our FuseCore integration layer open-sourced under Apache 2.0. We support multiple programming languages (JavaScript, Python, Java) and avoid vendor lock-in through standardized APIs.
                  </p>
                  <ul className="list-disc list-inside text-gray-300 space-y-1">
                    <li>Open-core model for our integration framework</li>
                    <li>RESTful APIs with OpenAPI specifications</li>
                    <li>Language-agnostic integration capabilities</li>
                  </ul>
                </div>
                
                <div className="bg-secondary p-6 rounded-lg pl-6 border-l-4 border-red-600">
                  <h3 className="text-xl font-bold mb-3 text-red-400">Customer Choice</h3>
                  <p className="text-gray-300 mb-4">
                    Our modular architecture allows customers to adopt NovaGRC at their own pace, integrating with their existing tools and workflows. We meet customers where they are in their compliance journey.
                  </p>
                  <ul className="list-disc list-inside text-gray-300 space-y-1">
                    <li>Modular deployment options (SaaS, API, embedded)</li>
                    <li>Integration with existing GRC tools and workflows</li>
                    <li>Flexible adoption paths from basic to advanced compliance</li>
                  </ul>
                </div>
                
                <div className="bg-secondary p-6 rounded-lg pl-6 border-l-4 border-yellow-600">
                  <h3 className="text-xl font-bold mb-3 text-yellow-400">Innovation</h3>
                  <p className="text-gray-300 mb-4">
                    As an AI-first compliance platform, NovaGRC leverages Google's AI capabilities to create unique, differentiated offerings in the GRC space. Our gamification approach to compliance is revolutionary in the industry.
                  </p>
                  <ul className="list-disc list-inside text-gray-300 space-y-1">
                    <li>AI-powered compliance predictions and recommendations</li>
                    <li>Industry-first gamification of compliance (NovaPlay, NovaQuest, NovaXP)</li>
                    <li>API-first architecture enabling novel integrations</li>
                  </ul>
                </div>
                
                <div className="bg-secondary p-6 rounded-lg pl-6 border-l-4 border-green-600">
                  <h3 className="text-xl font-bold mb-3 text-green-400">Trust</h3>
                  <p className="text-gray-300 mb-4">
                    As a GRC platform, trust is our foundation. We implement Google Cloud's security best practices and maintain transparency in our operations, data handling, and partnership approach.
                  </p>
                  <ul className="list-disc list-inside text-gray-300 space-y-1">
                    <li>Comprehensive security controls and compliance with standards</li>
                    <li>Transparent data handling and privacy practices</li>
                    <li>Commitment to continuous security improvement</li>
                  </ul>
                </div>
              </div>
              
              <div className="bg-secondary p-6 rounded-lg mb-8">
                <h3 className="text-xl font-bold mb-4">Partner Advantage Program Alignment</h3>
                <p className="text-gray-300 mb-4">
                  NovaGRC is committed to advancing through Google Cloud's Partner Advantage Program levels by meeting and exceeding the required criteria:
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-gray-800 p-4 rounded-lg">
                    <h4 className="font-semibold mb-2 text-blue-400">Certifications & Training</h4>
                    <p className="text-gray-300">
                      Our development team will complete Google Cloud certifications in Cloud Architecture, Data Engineering, and Security Engineering to demonstrate our technical proficiency.
                    </p>
                  </div>
                  
                  <div className="bg-gray-800 p-4 rounded-lg">
                    <h4 className="font-semibold mb-2 text-blue-400">Customer Success</h4>
                    <p className="text-gray-300">
                      We will document and share case studies of successful NovaGRC implementations on Google Cloud, highlighting the business value and technical excellence.
                    </p>
                  </div>
                  
                  <div className="bg-gray-800 p-4 rounded-lg">
                    <h4 className="font-semibold mb-2 text-blue-400">Specializations & Expertise</h4>
                    <p className="text-gray-300">
                      We aim to achieve Security, AI/ML, and Application Development specializations to showcase our expertise in these critical areas.
                    </p>
                  </div>
                </div>
              </div>
            </section>
            
            {/* Strategic Partnership Plans */}
            <section id="options" className="mb-12">
              <h2 className="text-2xl font-bold mb-6">Strategic Partnership Options</h2>
              <p className="text-gray-300 mb-6">
                We've developed three strategic partnership models that align NovaGRC's growth with Google Cloud's ecosystem. Each plan represents a different approach to our collaboration, with varying levels of support and commitment.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {/* Plan A */}
                <div className="bg-secondary rounded-lg overflow-hidden border-t-4 border-blue-500 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                  <div className="p-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-2xl font-bold text-blue-400">Plan A</h3>
                      <span className="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">Preferred</span>
                    </div>
                    <h4 className="text-lg font-semibold mb-4">Standard Cloud Credits Partnership</h4>
                    <p className="text-gray-300 mb-6">
                      A traditional startup credits program that enables NovaGRC to build and scale on Google Cloud from day one.
                    </p>
                    <div className="mb-6">
                      <h5 className="font-semibold mb-2">Key Elements:</h5>
                      <ul className="list-disc list-inside text-gray-300 space-y-1">
                        <li>$100,000 in Google Cloud & AI Credits</li>
                        <li>12-18 month runway for infrastructure</li>
                        <li>Full deployment of NovaFuse API Superstore</li>
                        <li>Continued Vertex AI model tuning</li>
                      </ul>
                    </div>
                    <div className="mb-4">
                      <h5 className="font-semibold mb-2">Allocation Plan:</h5>
                      <ul className="list-disc list-inside text-gray-300 space-y-1">
                        <li>First 12 months infrastructure: $60,000</li>
                        <li>AI model training/optimization: $20,000</li>
                        <li>Scaling buffer for spikes: $20,000</li>
                      </ul>
                    </div>
                    <div className="mt-6 text-center">
                      <a href="#" className="accent-bg text-white px-4 py-2 rounded font-bold hover:bg-blue-700 inline-block">
                        View Detailed Plan
                      </a>
                    </div>
                  </div>
                </div>
                
                {/* Plan B */}
                <div className="bg-secondary rounded-lg overflow-hidden border-t-4 border-purple-500 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                  <div className="p-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-2xl font-bold text-purple-400">Plan B</h3>
                      <span className="bg-purple-900 text-purple-200 text-xs px-2 py-1 rounded">Alternative</span>
                    </div>
                    <h4 className="text-lg font-semibold mb-4">Adjusted Revenue Partnership</h4>
                    <p className="text-gray-300 mb-6">
                      A capital-efficient approach with partial credits and adjusted revenue sharing to ensure sustainable growth.
                    </p>
                    <div className="mb-6">
                      <h5 className="font-semibold mb-2">Key Elements:</h5>
                      <ul className="list-disc list-inside text-gray-300 space-y-1">
                        <li>$20K-50K in Google Cloud Credits</li>
                        <li>Adjusted revenue sharing model</li>
                        <li>Phased deployment strategy</li>
                        <li>Infrastructure optimization focus</li>
                      </ul>
                    </div>
                    <div className="mb-4">
                      <h5 className="font-semibold mb-2">Revenue Adjustments:</h5>
                      <ul className="list-disc list-inside text-gray-300 space-y-1">
                        <li>Tier 1 Partners: 70% revenue share</li>
                        <li>Early partner onboarding fees</li>
                        <li>Minimum API call commitments</li>
                      </ul>
                    </div>
                    <div className="mt-6 text-center">
                      <a href="#" className="accent-bg text-white px-4 py-2 rounded font-bold hover:bg-blue-700 inline-block">
                        View Detailed Plan
                      </a>
                    </div>
                  </div>
                </div>
                
                {/* Plan C */}
                <div className="bg-secondary rounded-lg overflow-hidden border-t-4 border-green-500 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                  <div className="p-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-2xl font-bold text-green-400">Plan C</h3>
                      <span className="bg-green-900 text-green-200 text-xs px-2 py-1 rounded">Strategic</span>
                    </div>
                    <h4 className="text-lg font-semibold mb-4">Strategic Independence</h4>
                    <p className="text-gray-300 mb-6">
                      A fully independent approach that demonstrates NovaGRC's commitment to Google Cloud regardless of credits.
                    </p>
                    <div className="mb-6">
                      <h5 className="font-semibold mb-2">Key Elements:</h5>
                      <ul className="list-disc list-inside text-gray-300 space-y-1">
                        <li>GCP or Bust - By Strategic Design</li>
                        <li>Revenue-First Product Launch</li>
                        <li>Phased Product Expansion</li>
                        <li>Strategic Partner Co-Investment</li>
                      </ul>
                    </div>
                    <div className="mb-4">
                      <h5 className="font-semibold mb-2">Year 1 Targets:</h5>
                      <ul className="list-disc list-inside text-gray-300 space-y-1">
                        <li>Q1: 10 customers ($3,000/month)</li>
                        <li>Q4: 100 customers ($45,000/month)</li>
                        <li>Break-even: Q3 (50 customers)</li>
                      </ul>
                    </div>
                    <div className="mt-6 text-center">
                      <a href="#" className="accent-bg text-white px-4 py-2 rounded font-bold hover:bg-blue-700 inline-block">
                        View Detailed Plan
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </section>
            
            {/* Infrastructure Projections */}
            <section id="infrastructure" className="bg-secondary p-8 rounded-lg mb-12">
              <h2 className="text-2xl font-bold mb-6">Infrastructure Projections</h2>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="text-left p-2">Category</th>
                      <th className="text-left p-2">Key Services</th>
                      <th className="text-left p-2">Estimated Monthly Cost</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Core API Infrastructure</td>
                      <td className="p-2">Kong, PostgreSQL, Redis, Load Balancer</td>
                      <td className="p-2">~$1,050</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Mock API Services</td>
                      <td className="p-2">GKE, Cloud Functions</td>
                      <td className="p-2">~$400</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">AI/ML Components</td>
                      <td className="p-2">Vertex AI, Document AI, Natural Language API</td>
                      <td className="p-2">~$1,800</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Storage & Processing</td>
                      <td className="p-2">Cloud Storage, BigQuery, Pub/Sub</td>
                      <td className="p-2">~$600</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Security & IAM</td>
                      <td className="p-2">Cloud Armor, Secret Manager, IAM</td>
                      <td className="p-2">~$150</td>
                    </tr>
                    <tr>
                      <td className="p-2">Dev/Test Environments</td>
                      <td className="p-2">Duplicate Infra @ 50% scale</td>
                      <td className="p-2">~$1,200</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              
              <div className="mt-8">
                <h3 className="text-xl font-bold mb-4">Traffic & Scaling Projections</h3>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left p-2">Timeframe</th>
                        <th className="text-left p-2">API Calls/Day</th>
                        <th className="text-left p-2">Monthly Infra Est.</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b border-gray-700">
                        <td className="p-2">Months 1–3</td>
                        <td className="p-2">10K–50K</td>
                        <td className="p-2">~$3,000</td>
                      </tr>
                      <tr className="border-b border-gray-700">
                        <td className="p-2">Months 4–6</td>
                        <td className="p-2">50K–200K</td>
                        <td className="p-2">~$5,000</td>
                      </tr>
                      <tr className="border-b border-gray-700">
                        <td className="p-2">Months 7–12</td>
                        <td className="p-2">200K–500K</td>
                        <td className="p-2">~$8,000</td>
                      </tr>
                      <tr>
                        <td className="p-2">Year 2</td>
                        <td className="p-2">Up to 2M+</td>
                        <td className="p-2">~$20,000</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </section>
            
            {/* Strategic Alignment */}
            <section id="alignment" className="bg-secondary p-8 rounded-lg mb-12">
              <h2 className="text-2xl font-bold mb-6">Strategic Alignment with Google Cloud</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h3 className="text-xl font-bold mb-3">AI-First Architecture</h3>
                  <p className="text-gray-300">
                    Vertex AI, Document AI, and Natural Language API are integrated into every layer of NovaGRC, creating a showcase for Google's AI capabilities in the GRC space.
                  </p>
                </div>
                
                <div className="border-l-4 border-blue-500 pl-4">
                  <h3 className="text-xl font-bold mb-3">Scalable Reference Architecture</h3>
                  <p className="text-gray-300">
                    NovaGRC serves as a model for how GRC platforms can run natively on Google Cloud, demonstrating best practices for security, compliance, and scalability.
                  </p>
                </div>
                
                <div className="border-l-4 border-blue-500 pl-4">
                  <h3 className="text-xl font-bold mb-3">Workspace Integration</h3>
                  <p className="text-gray-300">
                    Our compliance AI is built for Google Docs, Sheets, and Gmail environments, creating a seamless experience for Google Workspace users.
                  </p>
                </div>
                
                <div className="border-l-4 border-blue-500 pl-4">
                  <h3 className="text-xl font-bold mb-3">Ecosystem Readiness</h3>
                  <p className="text-gray-300">
                    Our partner strategy is designed to drive exponential API usage and showcase Google's tech stack to enterprises in regulated industries.
                  </p>
                </div>
              </div>
            </section>
            
            {/* Mutual Benefits */}
            <section id="benefits" className="bg-secondary p-8 rounded-lg mb-12">
              <h2 className="text-2xl font-bold mb-6 text-center">Mutual Benefits</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-bold mb-4 text-blue-400">Benefits for Google Cloud</h3>
                  <ul className="list-disc list-inside text-gray-300 space-y-2">
                    <li>Showcase AI-driven GRC use case for regulated industries</li>
                    <li>Expand Google Cloud adoption in compliance-focused enterprises</li>
                    <li>Demonstrate the power of Google's AI offerings in a critical business function</li>
                    <li>Access to NovaGRC's partner ecosystem of 50+ compliance-related companies</li>
                    <li>Case studies and reference architecture for GRC on Google Cloud</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-bold mb-4 text-blue-400">Benefits for NovaGRC</h3>
                  <ul className="list-disc list-inside text-gray-300 space-y-2">
                    <li>Access to Google Cloud's enterprise-grade infrastructure and AI capabilities</li>
                    <li>Technical support and guidance from Google Cloud experts</li>
                    <li>Potential co-marketing opportunities with Google Cloud</li>
                    <li>Credibility and trust from being built on Google Cloud</li>
                    <li>Scalability to support rapid growth and expansion</li>
                  </ul>
                </div>
              </div>
            </section>
            
            {/* Call to Action */}
            <section className="bg-secondary p-8 rounded-lg text-center">
              <h2 className="text-2xl font-bold mb-4">Ready to Build the Future of Cyber-Safety Together?</h2>
              <p className="text-xl mb-6 max-w-3xl mx-auto">
                NovaGRC is committed to Google Cloud as our exclusive platform. Let's discuss how we can create a strategic partnership that benefits both organizations.
              </p>
              <button className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700">
                Schedule a Partnership Discussion
              </button>
            </section>
          </div>
        </div>
      )}
    </div>
  );
}
