/**
 * Policy Service
 * 
 * This service handles compliance policies for governance controls.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const TeamService = require('./TeamService');
const AuditService = require('./AuditService');

class PolicyService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.policiesFile = path.join(this.dataDir, 'policies.json');
    this.policyViolationsFile = path.join(this.dataDir, 'policy_violations.json');
    this.teamService = new TeamService(dataDir);
    this.auditService = new AuditService(dataDir);
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
      
      // Initialize files if they don't exist
      await this.initializeFile(this.policiesFile, []);
      await this.initializeFile(this.policyViolationsFile, []);
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get all policies
   */
  async getAllPolicies(filters = {}) {
    const policies = await this.loadData(this.policiesFile);
    
    // Apply filters
    let filteredPolicies = policies;
    
    if (filters.enabled !== undefined) {
      filteredPolicies = filteredPolicies.filter(policy => policy.enabled === filters.enabled);
    }
    
    if (filters.resourceType) {
      filteredPolicies = filteredPolicies.filter(policy => policy.resourceType === filters.resourceType);
    }
    
    if (filters.teamId) {
      filteredPolicies = filteredPolicies.filter(policy => policy.teamId === filters.teamId);
    }
    
    if (filters.environmentId) {
      filteredPolicies = filteredPolicies.filter(policy => policy.environmentId === filters.environmentId);
    }
    
    if (filters.severity) {
      filteredPolicies = filteredPolicies.filter(policy => policy.severity === filters.severity);
    }
    
    // Sort by created date (newest first)
    filteredPolicies.sort((a, b) => new Date(b.created) - new Date(a.created));
    
    return filteredPolicies;
  }

  /**
   * Get policies for a team
   */
  async getPoliciesForTeam(teamId, filters = {}) {
    return this.getAllPolicies({ ...filters, teamId });
  }

  /**
   * Get policy by ID
   */
  async getPolicyById(id) {
    const policies = await this.loadData(this.policiesFile);
    const policy = policies.find(p => p.id === id);
    
    if (!policy) {
      throw new NotFoundError(`Policy with ID ${id} not found`);
    }
    
    return policy;
  }

  /**
   * Create a new policy
   */
  async createPolicy(data, userId) {
    if (!data.name) {
      throw new ValidationError('Policy name is required');
    }
    
    if (!data.resourceType) {
      throw new ValidationError('Resource type is required');
    }
    
    if (!data.rules || !Array.isArray(data.rules) || data.rules.length === 0) {
      throw new ValidationError('At least one policy rule is required');
    }
    
    // Validate rules
    for (const rule of data.rules) {
      if (!rule.field) {
        throw new ValidationError('Rule field is required');
      }
      
      if (!rule.operator) {
        throw new ValidationError('Rule operator is required');
      }
      
      if (rule.value === undefined) {
        throw new ValidationError('Rule value is required');
      }
    }
    
    // If team ID is provided, check if user is a member with admin or owner role
    if (data.teamId) {
      try {
        await this.teamService.checkTeamPermission(data.teamId, userId, ['owner', 'admin']);
      } catch (error) {
        throw new AuthorizationError('You do not have permission to create policies for this team');
      }
    }
    
    const policies = await this.loadData(this.policiesFile);
    
    // Create new policy
    const newPolicy = {
      id: uuidv4(),
      name: data.name,
      description: data.description || '',
      resourceType: data.resourceType,
      rules: data.rules,
      enabled: data.enabled !== undefined ? data.enabled : true,
      severity: data.severity || 'medium',
      action: data.action || 'warn',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      createdBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    policies.push(newPolicy);
    await this.saveData(this.policiesFile, policies);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'CREATE',
      resourceType: 'policy',
      resourceId: newPolicy.id,
      details: {
        name: newPolicy.name,
        resourceType: newPolicy.resourceType,
        action: newPolicy.action
      },
      teamId: newPolicy.teamId,
      environmentId: newPolicy.environmentId
    });
    
    return newPolicy;
  }

  /**
   * Update a policy
   */
  async updatePolicy(id, data, userId) {
    const policies = await this.loadData(this.policiesFile);
    const index = policies.findIndex(p => p.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Policy with ID ${id} not found`);
    }
    
    const policy = policies[index];
    
    // Check if user has permission to update
    if (policy.createdBy !== userId) {
      // If not the creator, check if user is team owner or admin
      if (policy.teamId) {
        try {
          await this.teamService.checkTeamPermission(policy.teamId, userId, ['owner', 'admin']);
        } catch (error) {
          throw new AuthorizationError('You do not have permission to update this policy');
        }
      } else {
        throw new AuthorizationError('You do not have permission to update this policy');
      }
    }
    
    // Validate rules if provided
    if (data.rules) {
      if (!Array.isArray(data.rules) || data.rules.length === 0) {
        throw new ValidationError('At least one policy rule is required');
      }
      
      for (const rule of data.rules) {
        if (!rule.field) {
          throw new ValidationError('Rule field is required');
        }
        
        if (!rule.operator) {
          throw new ValidationError('Rule operator is required');
        }
        
        if (rule.value === undefined) {
          throw new ValidationError('Rule value is required');
        }
      }
    }
    
    // Update policy
    const updatedPolicy = {
      ...policy,
      ...data,
      id, // Don't allow changing the ID
      createdBy: policy.createdBy, // Don't allow changing the creator
      updated: new Date().toISOString()
    };
    
    policies[index] = updatedPolicy;
    await this.saveData(this.policiesFile, policies);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UPDATE',
      resourceType: 'policy',
      resourceId: id,
      details: {
        name: updatedPolicy.name,
        enabled: updatedPolicy.enabled
      },
      teamId: updatedPolicy.teamId,
      environmentId: updatedPolicy.environmentId
    });
    
    return updatedPolicy;
  }

  /**
   * Delete a policy
   */
  async deletePolicy(id, userId) {
    const policies = await this.loadData(this.policiesFile);
    const index = policies.findIndex(p => p.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Policy with ID ${id} not found`);
    }
    
    const policy = policies[index];
    
    // Check if user has permission to delete
    if (policy.createdBy !== userId) {
      // If not the creator, check if user is team owner or admin
      if (policy.teamId) {
        try {
          await this.teamService.checkTeamPermission(policy.teamId, userId, ['owner', 'admin']);
        } catch (error) {
          throw new AuthorizationError('You do not have permission to delete this policy');
        }
      } else {
        throw new AuthorizationError('You do not have permission to delete this policy');
      }
    }
    
    // Remove the policy
    policies.splice(index, 1);
    await this.saveData(this.policiesFile, policies);
    
    // Remove all violations for this policy
    await this.deletePolicyViolations(id);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'DELETE',
      resourceType: 'policy',
      resourceId: id,
      details: {
        name: policy.name
      },
      teamId: policy.teamId,
      environmentId: policy.environmentId
    });
    
    return { success: true, message: `Policy ${id} deleted` };
  }

  /**
   * Evaluate a resource against policies
   */
  async evaluateResource(resourceType, resource, environmentId = null, teamId = null) {
    // Get applicable policies
    const policies = await this.getAllPolicies({
      enabled: true,
      resourceType
    });
    
    // Filter policies by environment and team if provided
    let applicablePolicies = policies;
    
    if (environmentId) {
      applicablePolicies = applicablePolicies.filter(policy => 
        !policy.environmentId || policy.environmentId === environmentId
      );
    }
    
    if (teamId) {
      applicablePolicies = applicablePolicies.filter(policy => 
        !policy.teamId || policy.teamId === teamId
      );
    }
    
    // Evaluate each policy
    const violations = [];
    
    for (const policy of applicablePolicies) {
      const policyViolations = this.evaluatePolicy(policy, resource);
      
      if (policyViolations.length > 0) {
        violations.push({
          policy,
          violations: policyViolations
        });
      }
    }
    
    return {
      resource,
      violations,
      compliant: violations.length === 0
    };
  }

  /**
   * Evaluate a resource against a specific policy
   */
  evaluatePolicy(policy, resource) {
    const violations = [];
    
    // Evaluate each rule
    for (const rule of policy.rules) {
      const { field, operator, value } = rule;
      
      // Get field value from resource
      const fieldValue = this.getFieldValue(resource, field);
      
      // Evaluate rule
      const ruleViolated = !this.evaluateRule(fieldValue, operator, value);
      
      if (ruleViolated) {
        violations.push({
          rule,
          fieldValue,
          message: rule.message || `Field '${field}' with value '${fieldValue}' violates rule: ${operator} ${value}`
        });
      }
    }
    
    return violations;
  }

  /**
   * Get field value from resource using dot notation
   */
  getFieldValue(resource, field) {
    const parts = field.split('.');
    let value = resource;
    
    for (const part of parts) {
      if (value === null || value === undefined) {
        return undefined;
      }
      
      value = value[part];
    }
    
    return value;
  }

  /**
   * Evaluate a rule
   */
  evaluateRule(fieldValue, operator, ruleValue) {
    switch (operator) {
      case 'equals':
        return fieldValue === ruleValue;
      case 'notEquals':
        return fieldValue !== ruleValue;
      case 'contains':
        return typeof fieldValue === 'string' && fieldValue.includes(ruleValue);
      case 'notContains':
        return typeof fieldValue === 'string' && !fieldValue.includes(ruleValue);
      case 'startsWith':
        return typeof fieldValue === 'string' && fieldValue.startsWith(ruleValue);
      case 'endsWith':
        return typeof fieldValue === 'string' && fieldValue.endsWith(ruleValue);
      case 'greaterThan':
        return fieldValue > ruleValue;
      case 'lessThan':
        return fieldValue < ruleValue;
      case 'greaterThanOrEquals':
        return fieldValue >= ruleValue;
      case 'lessThanOrEquals':
        return fieldValue <= ruleValue;
      case 'in':
        return Array.isArray(ruleValue) && ruleValue.includes(fieldValue);
      case 'notIn':
        return Array.isArray(ruleValue) && !ruleValue.includes(fieldValue);
      case 'exists':
        return fieldValue !== undefined && fieldValue !== null;
      case 'notExists':
        return fieldValue === undefined || fieldValue === null;
      case 'isTrue':
        return fieldValue === true;
      case 'isFalse':
        return fieldValue === false;
      case 'matches':
        return typeof fieldValue === 'string' && new RegExp(ruleValue).test(fieldValue);
      default:
        return false;
    }
  }

  /**
   * Record a policy violation
   */
  async recordPolicyViolation(policyId, resourceType, resourceId, violations, userId = null) {
    // Get policy
    const policy = await this.getPolicyById(policyId);
    
    const policyViolations = await this.loadData(this.policyViolationsFile);
    
    // Create new violation
    const newViolation = {
      id: uuidv4(),
      policyId,
      policyName: policy.name,
      resourceType,
      resourceId,
      violations,
      status: 'open',
      severity: policy.severity,
      teamId: policy.teamId,
      environmentId: policy.environmentId,
      createdBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    policyViolations.push(newViolation);
    await this.saveData(this.policyViolationsFile, policyViolations);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'VIOLATION',
      resourceType: 'policy',
      resourceId: policyId,
      details: {
        policyName: policy.name,
        resourceType,
        resourceId,
        violationCount: violations.length
      },
      teamId: policy.teamId,
      environmentId: policy.environmentId
    });
    
    return newViolation;
  }

  /**
   * Get policy violations
   */
  async getPolicyViolations(filters = {}) {
    const violations = await this.loadData(this.policyViolationsFile);
    
    // Apply filters
    let filteredViolations = violations;
    
    if (filters.status) {
      filteredViolations = filteredViolations.filter(v => v.status === filters.status);
    }
    
    if (filters.policyId) {
      filteredViolations = filteredViolations.filter(v => v.policyId === filters.policyId);
    }
    
    if (filters.resourceType) {
      filteredViolations = filteredViolations.filter(v => v.resourceType === filters.resourceType);
    }
    
    if (filters.resourceId) {
      filteredViolations = filteredViolations.filter(v => v.resourceId === filters.resourceId);
    }
    
    if (filters.teamId) {
      filteredViolations = filteredViolations.filter(v => v.teamId === filters.teamId);
    }
    
    if (filters.environmentId) {
      filteredViolations = filteredViolations.filter(v => v.environmentId === filters.environmentId);
    }
    
    if (filters.severity) {
      filteredViolations = filteredViolations.filter(v => v.severity === filters.severity);
    }
    
    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      filteredViolations = filteredViolations.filter(v => new Date(v.created) >= startDate);
    }
    
    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      filteredViolations = filteredViolations.filter(v => new Date(v.created) <= endDate);
    }
    
    // Sort by created date (newest first)
    filteredViolations.sort((a, b) => new Date(b.created) - new Date(a.created));
    
    return filteredViolations;
  }

  /**
   * Get policy violation by ID
   */
  async getPolicyViolationById(id) {
    const violations = await this.loadData(this.policyViolationsFile);
    const violation = violations.find(v => v.id === id);
    
    if (!violation) {
      throw new NotFoundError(`Policy violation with ID ${id} not found`);
    }
    
    return violation;
  }

  /**
   * Update policy violation
   */
  async updatePolicyViolation(id, data, userId) {
    const violations = await this.loadData(this.policyViolationsFile);
    const index = violations.findIndex(v => v.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Policy violation with ID ${id} not found`);
    }
    
    const violation = violations[index];
    
    // Check if user has permission to update
    if (violation.teamId) {
      try {
        await this.teamService.checkTeamPermission(violation.teamId, userId, ['owner', 'admin']);
      } catch (error) {
        throw new AuthorizationError('You do not have permission to update this policy violation');
      }
    }
    
    // Update violation
    const updatedViolation = {
      ...violation,
      ...data,
      id, // Don't allow changing the ID
      updated: new Date().toISOString()
    };
    
    violations[index] = updatedViolation;
    await this.saveData(this.policyViolationsFile, violations);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UPDATE',
      resourceType: 'policy_violation',
      resourceId: id,
      details: {
        policyName: violation.policyName,
        status: updatedViolation.status
      },
      teamId: violation.teamId,
      environmentId: violation.environmentId
    });
    
    return updatedViolation;
  }

  /**
   * Delete policy violations for a policy
   */
  async deletePolicyViolations(policyId) {
    const violations = await this.loadData(this.policyViolationsFile);
    const updatedViolations = violations.filter(v => v.policyId !== policyId);
    
    if (updatedViolations.length !== violations.length) {
      await this.saveData(this.policyViolationsFile, updatedViolations);
    }
    
    return { success: true, message: `Violations for policy ${policyId} deleted` };
  }
}

module.exports = PolicyService;
