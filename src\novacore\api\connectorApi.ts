/**
 * connectorApi.ts
 * 
 * API endpoints for connector management in the NovaCore system.
 */

import express from 'express';
import { ConnectorService } from '../services/ConnectorService';
import { EvidenceService } from '../services/EvidenceService';
import {
  ConnectorType,
  AuthenticationType,
  AuthenticationConfig,
  ConnectorConfig,
  CollectionSchedule,
  ConnectorStatus,
} from '../models/Connector';

import {
  JobPriority,
  JobType,
} from '../models/CollectionJob';

// Create router
const router = express.Router();

// Create services
const evidenceService = new EvidenceService();
const connectorService = new ConnectorService(evidenceService);

/**
 * GET /api/connectors
 * Get all connectors with optional filtering
 */
router.get('/', async (req, res) => {
  try {
    const { page = 1, pageSize = 10, ...query } = req.query;
    
    const result = await connectorService.searchConnectors(
      query,
      Number(page),
      Number(pageSize),
    );
    
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

/**
 * GET /api/connectors/:id
 * Get connector by ID
 */
router.get('/:id', async (req, res) => {
  try {
    const connector = await connectorService.getConnector(req.params.id);
    res.json(connector);
  } catch (error) {
    res.status(404).json({ error: error.message });
  }
});

/**
 * POST /api/connectors
 * Create new connector
 */
router.post('/', async (req, res) => {
  try {
    const {
      name,
      type,
      authentication,
      config,
      description,
      schedule,
      metadata,
      tags,
    } = req.body;
    
    // Validate required fields
    if (!name || !type || !authentication || !config) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Get user from request (in a real app, this would come from authentication)
    const user = req.user || { id: 'system', organization: 'default' };
    
    // Create connector
    const connector = await connectorService.createConnector(
      name,
      type as ConnectorType,
      authentication as AuthenticationConfig,
      config as ConnectorConfig,
      user.id,
      user.organization,
      description,
      schedule as CollectionSchedule,
      metadata,
      tags,
    );
    
    res.status(201).json(connector);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * PUT /api/connectors/:id
 * Update connector
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;
    
    // Get user from request (in a real app, this would come from authentication)
    const user = req.user || { id: 'system' };
    
    // Update connector
    const connector = await connectorService.updateConnector(
      id,
      updates,
      user.id,
    );
    
    res.json(connector);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * DELETE /api/connectors/:id
 * Delete connector
 */
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Delete connector
    await connectorService.deleteConnector(id);
    
    res.status(204).end();
  } catch (error) {
    res.status(404).json({ error: error.message });
  }
});

/**
 * POST /api/connectors/:id/activate
 * Activate connector
 */
router.post('/:id/activate', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get user from request (in a real app, this would come from authentication)
    const user = req.user || { id: 'system' };
    
    // Activate connector
    const connector = await connectorService.activateConnector(id, user.id);
    
    res.json(connector);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * POST /api/connectors/:id/deactivate
 * Deactivate connector
 */
router.post('/:id/deactivate', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get user from request (in a real app, this would come from authentication)
    const user = req.user || { id: 'system' };
    
    // Deactivate connector
    const connector = await connectorService.deactivateConnector(id, user.id);
    
    res.json(connector);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * POST /api/connectors/:id/test
 * Test connector connection
 */
router.post('/:id/test', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Test connection
    const healthCheck = await connectorService.testConnection(id);
    
    res.json(healthCheck);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * POST /api/connectors/:id/jobs
 * Create collection job for connector
 */
router.post('/:id/jobs', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      priority = JobPriority.MEDIUM,
      type = JobType.MANUAL,
      parameters,
      scheduledAt,
    } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Get user from request (in a real app, this would come from authentication)
    const user = req.user || { id: 'system' };
    
    // Create job
    const job = await connectorService.createCollectionJob(
      id,
      name,
      priority as JobPriority,
      type as JobType,
      user.id,
      parameters,
      scheduledAt ? new Date(scheduledAt) : undefined,
    );
    
    res.status(201).json(job);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * GET /api/connectors/:id/jobs
 * Get collection jobs for connector
 */
router.get('/:id/jobs', async (req, res) => {
  try {
    const { id } = req.params;
    
    // In a real implementation, this would retrieve jobs for the connector
    // For now, we'll return an empty array
    res.json([]);
  } catch (error) {
    res.status(404).json({ error: error.message });
  }
});

/**
 * POST /api/connectors/:id/schedule
 * Schedule collection for connector
 */
router.post('/:id/schedule', async (req, res) => {
  try {
    const { id } = req.params;
    const { schedule } = req.body;
    
    // Validate required fields
    if (!schedule || schedule.frequency === undefined) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Get user from request (in a real app, this would come from authentication)
    const user = req.user || { id: 'system' };
    
    // Schedule collection
    const connector = await connectorService.scheduleCollection(
      id,
      schedule as CollectionSchedule,
      user.id,
    );
    
    res.json(connector);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * GET /api/connectors/scheduled
 * Get scheduled collections
 */
router.get('/scheduled', async (req, res) => {
  try {
    // Get scheduled collections
    const connectors = await connectorService.getScheduledCollections();
    
    res.json(connectors);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

export default router;
