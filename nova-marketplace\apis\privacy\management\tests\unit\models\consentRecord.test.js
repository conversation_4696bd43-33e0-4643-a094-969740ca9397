/**
 * Consent Record Model Tests
 *
 * This file contains unit tests for the Consent Record model.
 */

const mongoose = require('mongoose');
const ConsentRecord = require('../../../models/consentRecord');

describe('Consent Record Model', () => {
  // Test data
  const validConsentRecordData = {
    dataSubjectName: '<PERSON>',
    dataSubjectEmail: '<EMAIL>',
    consentType: 'marketing',
    consentDetails: 'Consent for marketing emails',
    purposes: ['Email Marketing', 'Product Updates'],
    dataCategories: ['Contact Information', 'Preferences'],
    collectionMethod: 'web-form',
    collectionTimestamp: new Date()
  };

  beforeAll(async () => {
    // Connect to a test database before running tests
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/test', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
  });

  afterAll(async () => {
    // Disconnect from the test database after tests
    await mongoose.connection.close();
  });

  afterEach(async () => {
    // Clean up the database after each test
    await ConsentRecord.deleteMany({});
  });

  describe('Schema Validation', () => {
    it('should create a valid consent record', async () => {
      // Create a new consent record
      const consentRecord = new ConsentRecord(validConsentRecordData);

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Assertions
      expect(savedRecord._id).toBeDefined();
      expect(savedRecord.dataSubjectName).toBe(validConsentRecordData.dataSubjectName);
      expect(savedRecord.dataSubjectEmail).toBe(validConsentRecordData.dataSubjectEmail);
      expect(savedRecord.consentType).toBe(validConsentRecordData.consentType);
      expect(savedRecord.consentDetails).toBe(validConsentRecordData.consentDetails);
      expect(savedRecord.purposes).toEqual(validConsentRecordData.purposes);
      expect(savedRecord.dataCategories).toEqual(validConsentRecordData.dataCategories);
      expect(savedRecord.collectionMethod).toBe(validConsentRecordData.collectionMethod);
      expect(savedRecord.status).toBe('active'); // Default value
      expect(savedRecord.createdAt).toBeDefined();
      expect(savedRecord.updatedAt).toBeDefined();
    });

    it('should fail validation when required fields are missing', async () => {
      // Create a new consent record with missing required fields
      const consentRecord = new ConsentRecord({
        dataSubjectName: 'John Doe'
        // Missing other required fields
      });

      // Attempt to save the consent record
      let error;
      try {
        await consentRecord.save();
      } catch (err) {
        error = err;
      }

      // Assertions
      expect(error).toBeDefined();
      expect(error.name).toBe('ValidationError');
      expect(error.errors.dataSubjectEmail).toBeDefined();
      expect(error.errors.consentType).toBeDefined();
      expect(error.errors.consentDetails).toBeDefined();
      expect(error.errors.collectionMethod).toBeDefined();
      expect(error.errors.collectionTimestamp).toBeDefined();
    });

    it('should fail validation when enum fields have invalid values', async () => {
      // Create a new consent record with invalid enum values
      const consentRecord = new ConsentRecord({
        ...validConsentRecordData,
        consentType: 'invalid-type',
        collectionMethod: 'invalid-method'
      });

      // Attempt to save the consent record
      let error;
      try {
        await consentRecord.save();
      } catch (err) {
        error = err;
      }

      // Assertions
      expect(error).toBeDefined();
      expect(error.name).toBe('ValidationError');
      expect(error.errors.consentType).toBeDefined();
      expect(error.errors.collectionMethod).toBeDefined();
    });

    it('should allow optional fields to be omitted', async () => {
      // Create a new consent record with only required fields
      const consentRecord = new ConsentRecord(validConsentRecordData);

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Assertions
      expect(savedRecord._id).toBeDefined();
      expect(savedRecord.dataSubjectId).toBeUndefined();
      expect(savedRecord.collectionLocation).toBeUndefined();
      expect(savedRecord.ipAddress).toBeUndefined();
      expect(savedRecord.userAgent).toBeUndefined();
      expect(savedRecord.formVersion).toBeUndefined();
      expect(savedRecord.withdrawalTimestamp).toBeUndefined();
      expect(savedRecord.withdrawalMethod).toBeUndefined();
      expect(savedRecord.withdrawalReason).toBeUndefined();
      expect(savedRecord.expiryDate).toBeUndefined();
      expect(savedRecord.proofOfConsent).toBeUndefined();
      expect(savedRecord.proofType).toBeUndefined();
      expect(savedRecord.notes).toEqual([]);
    });

    it('should allow third parties to be specified', async () => {
      // Create a new consent record with third parties
      const consentRecord = new ConsentRecord({
        ...validConsentRecordData,
        thirdParties: [
          {
            name: 'Third Party A',
            purpose: 'Analytics',
            location: 'US'
          },
          {
            name: 'Third Party B',
            purpose: 'Marketing'
            // Location is optional
          }
        ]
      });

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Assertions
      expect(savedRecord._id).toBeDefined();
      expect(savedRecord.thirdParties).toHaveLength(2);
      expect(savedRecord.thirdParties[0].name).toBe('Third Party A');
      expect(savedRecord.thirdParties[0].purpose).toBe('Analytics');
      expect(savedRecord.thirdParties[0].location).toBe('US');
      expect(savedRecord.thirdParties[1].name).toBe('Third Party B');
      expect(savedRecord.thirdParties[1].purpose).toBe('Marketing');
      expect(savedRecord.thirdParties[1].location).toBeUndefined();
    });

    it('should fail validation when third parties are missing required fields', async () => {
      // Create a new consent record with invalid third parties
      const consentRecord = new ConsentRecord({
        ...validConsentRecordData,
        thirdParties: [
          {
            name: 'Third Party A'
            // Missing required purpose field
          }
        ]
      });

      // Attempt to save the consent record
      let error;
      try {
        await consentRecord.save();
      } catch (err) {
        error = err;
      }

      // Assertions
      expect(error).toBeDefined();
      expect(error.name).toBe('ValidationError');
      expect(error.errors['thirdParties.0.purpose']).toBeDefined();
    });

    it('should allow notes to be added', async () => {
      // Create a new consent record with notes
      const consentRecord = new ConsentRecord({
        ...validConsentRecordData,
        notes: [
          {
            content: 'Initial consent obtained',
            createdBy: 'system'
          }
        ]
      });

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Assertions
      expect(savedRecord._id).toBeDefined();
      expect(savedRecord.notes).toHaveLength(1);
      expect(savedRecord.notes[0].content).toBe('Initial consent obtained');
      expect(savedRecord.notes[0].createdBy).toBe('system');
      expect(savedRecord.notes[0].createdAt).toBeDefined();
    });

    it('should fail validation when notes are missing required fields', async () => {
      // Create a new consent record with invalid notes
      const consentRecord = new ConsentRecord({
        ...validConsentRecordData,
        notes: [
          {
            content: 'Initial consent obtained'
            // Missing required createdBy field
          }
        ]
      });

      // Attempt to save the consent record
      let error;
      try {
        await consentRecord.save();
      } catch (err) {
        error = err;
      }

      // Assertions
      expect(error).toBeDefined();
      expect(error.name).toBe('ValidationError');
      expect(error.errors['notes.0.createdBy']).toBeDefined();
    });
  });

  describe('Virtuals', () => {
    it('should correctly determine if consent is expired', async () => {
      // Create a consent record with an expiry date in the past
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 10); // 10 days ago

      const consentRecord = new ConsentRecord({
        ...validConsentRecordData,
        expiryDate: pastDate
      });

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Assertions
      expect(savedRecord.isExpired).toBe(true);
    });

    it('should correctly determine if consent is not expired', async () => {
      // Create a consent record with an expiry date in the future
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 10); // 10 days in the future

      const consentRecord = new ConsentRecord({
        ...validConsentRecordData,
        expiryDate: futureDate
      });

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Assertions
      expect(savedRecord.isExpired).toBe(false);
    });

    it('should return false for isExpired when no expiry date is set', async () => {
      // Create a consent record without an expiry date
      const consentRecord = new ConsentRecord(validConsentRecordData);

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Assertions
      expect(savedRecord.isExpired).toBe(false);
    });

    it('should return true for isExpired when status is withdrawn and has expiry date', async () => {
      // Create a consent record with withdrawn status and expiry date
      const consentRecord = new ConsentRecord({
        ...validConsentRecordData,
        status: 'withdrawn',
        withdrawalTimestamp: new Date(),
        withdrawalMethod: 'email',
        withdrawalReason: 'No longer interested',
        expiryDate: new Date() // Need to set an expiry date for isExpired to be true
      });

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Assertions
      expect(savedRecord.isExpired).toBe(true);
    });

    it('should return true for isExpired when status is expired', async () => {
      // Create a consent record with expired status
      const consentRecord = new ConsentRecord({
        ...validConsentRecordData,
        status: 'expired',
        expiryDate: new Date()
      });

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Assertions
      expect(savedRecord.isExpired).toBe(true);
    });

    it('should correctly calculate time remaining until expiry', async () => {
      // Create a consent record with an expiry date in the future
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 10); // 10 days in the future

      const consentRecord = new ConsentRecord({
        ...validConsentRecordData,
        expiryDate: futureDate
      });

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Assertions
      expect(savedRecord.timeRemaining).toBe(10);
    });

    it('should return 0 for timeRemaining when consent is expired', async () => {
      // Create a consent record with an expiry date in the past
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 10); // 10 days ago

      const consentRecord = new ConsentRecord({
        ...validConsentRecordData,
        expiryDate: pastDate
      });

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Assertions
      expect(savedRecord.timeRemaining).toBe(0);
    });

    it('should return null for timeRemaining when no expiry date is set', async () => {
      // Create a consent record without an expiry date
      const consentRecord = new ConsentRecord(validConsentRecordData);

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Assertions
      expect(savedRecord.timeRemaining).toBeNull();
    });

    it('should return 0 for timeRemaining when status is withdrawn', async () => {
      // Create a consent record with withdrawn status
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 10); // 10 days in the future

      const consentRecord = new ConsentRecord({
        ...validConsentRecordData,
        status: 'withdrawn',
        withdrawalTimestamp: new Date(),
        expiryDate: futureDate
      });

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Assertions
      expect(savedRecord.timeRemaining).toBe(0);
    });

    it('should return 0 for timeRemaining when status is expired', async () => {
      // Create a consent record with expired status
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 10); // 10 days in the future

      const consentRecord = new ConsentRecord({
        ...validConsentRecordData,
        status: 'expired',
        expiryDate: futureDate
      });

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Assertions
      expect(savedRecord.timeRemaining).toBe(0);
    });
  });

  describe('Middleware', () => {
    it('should update the updatedAt field on save', async () => {
      // Create a new consent record
      const consentRecord = new ConsentRecord(validConsentRecordData);

      // Save the consent record
      const savedRecord = await consentRecord.save();

      // Store the original updatedAt value
      const originalUpdatedAt = savedRecord.updatedAt;

      // Wait a moment to ensure the timestamps will be different
      await new Promise(resolve => setTimeout(resolve, 100));

      // Update the consent record
      savedRecord.consentDetails = 'Updated consent details';
      await savedRecord.save();

      // Assertions
      expect(savedRecord.updatedAt).not.toEqual(originalUpdatedAt);
    });
  });
});
