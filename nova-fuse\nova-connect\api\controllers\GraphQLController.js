/**
 * GraphQL Controller
 * 
 * This controller handles API requests related to GraphQL operations.
 */

const GraphQLService = require('../services/GraphQLService');
const { ValidationError } = require('../utils/errors');

class GraphQLController {
  constructor() {
    this.graphqlService = new GraphQLService();
  }

  /**
   * Execute a GraphQL query
   */
  async executeQuery(req, res, next) {
    try {
      const { endpoint, query, variables, headers, auth } = req.body;
      
      if (!endpoint) {
        throw new ValidationError('GraphQL endpoint URL is required');
      }
      
      if (!query) {
        throw new ValidationError('GraphQL query is required');
      }
      
      const result = await this.graphqlService.executeQuery(
        endpoint,
        query,
        variables || {},
        headers || {},
        auth || null
      );
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Fetch GraphQL schema
   */
  async fetchSchema(req, res, next) {
    try {
      const { endpoint, headers, auth } = req.body;
      
      if (!endpoint) {
        throw new ValidationError('GraphQL endpoint URL is required');
      }
      
      const schema = await this.graphqlService.fetchSchema(
        endpoint,
        headers || {},
        auth || null
      );
      
      res.json(schema);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Validate a GraphQL query
   */
  async validateQuery(req, res, next) {
    try {
      const { query, schema } = req.body;
      
      if (!query) {
        throw new ValidationError('GraphQL query is required');
      }
      
      if (!schema) {
        throw new ValidationError('GraphQL schema is required');
      }
      
      const result = this.graphqlService.validateQuery(query, schema);
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Generate a sample GraphQL query
   */
  async generateSampleQuery(req, res, next) {
    try {
      const { schema, operationType } = req.body;
      
      if (!schema) {
        throw new ValidationError('GraphQL schema is required');
      }
      
      const sampleQuery = this.graphqlService.generateSampleQuery(
        schema,
        operationType || 'query'
      );
      
      res.json({ query: sampleQuery });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new GraphQLController();
