/**
 * Dashboard Module
 */
class Dashboard {
  constructor(apiClient) {
    this.api = apiClient;
    this.dsrChart = null;
    
    // Initialize the dashboard when the DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
      if (this.api.isAuthenticated()) {
        this.loadDashboard();
      }
    });
  }
  
  /**
   * Load the dashboard data
   */
  async loadDashboard() {
    try {
      const response = await this.api.getDashboardMetrics();
      const metrics = response.data;
      
      this.updateMetricsCards(metrics);
      this.updateComplianceGaps(metrics);
      this.loadRecentNotifications();
      this.loadRecentActivities();
      this.createDsrChart(metrics);
    } catch (error) {
      console.error('Error loading dashboard:', error);
      alert('Failed to load dashboard data');
    }
  }
  
  /**
   * Update the metrics cards with the latest data
   * @param {Object} metrics - Dashboard metrics
   */
  updateMetricsCards(metrics) {
    // Update the metrics cards
    document.getElementById('pending-dsrs-count').textContent = metrics.dsrMetrics.pendingRequests;
    document.getElementById('active-consents-count').textContent = metrics.consentMetrics.activeConsents;
    document.getElementById('open-breaches-count').textContent = metrics.dataBreachMetrics.openBreaches;
    document.getElementById('compliance-score').textContent = `${Math.round(metrics.complianceMetrics.overallComplianceScore)}%`;
  }
  
  /**
   * Update the compliance gaps list
   * @param {Object} metrics - Dashboard metrics
   */
  updateComplianceGaps(metrics) {
    const complianceGapsList = document.getElementById('compliance-gaps-list');
    complianceGapsList.innerHTML = '';
    
    if (metrics.complianceGaps && metrics.complianceGaps.length > 0) {
      metrics.complianceGaps.forEach(gap => {
        const li = document.createElement('li');
        li.className = 'list-group-item d-flex justify-content-between align-items-center';
        
        const badgeClass = gap.complianceRate < 50 ? 'bg-danger' : (gap.complianceRate < 80 ? 'bg-warning' : 'bg-success');
        
        li.innerHTML = `
          ${gap.area}: ${gap.description}
          <span class="badge ${badgeClass}">${Math.round(gap.complianceRate)}%</span>
        `;
        
        complianceGapsList.appendChild(li);
      });
    } else {
      const li = document.createElement('li');
      li.className = 'list-group-item';
      li.textContent = 'No compliance gaps found';
      complianceGapsList.appendChild(li);
    }
  }
  
  /**
   * Load recent notifications
   */
  async loadRecentNotifications() {
    try {
      const response = await this.api.getNotifications({ limit: 5 });
      const notifications = response.data;
      
      const notificationsList = document.getElementById('recent-notifications-list');
      notificationsList.innerHTML = '';
      
      if (notifications && notifications.length > 0) {
        notifications.forEach(notification => {
          const li = document.createElement('li');
          li.className = `list-group-item notification-item priority-${notification.priority}`;
          
          const date = new Date(notification.createdAt);
          const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
          
          li.innerHTML = `
            <div>
              <strong>${notification.title}</strong>
              <p class="mb-0">${notification.message}</p>
              <small class="text-muted">${formattedDate}</small>
            </div>
          `;
          
          notificationsList.appendChild(li);
        });
      } else {
        const li = document.createElement('li');
        li.className = 'list-group-item';
        li.textContent = 'No recent notifications';
        notificationsList.appendChild(li);
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
      
      const notificationsList = document.getElementById('recent-notifications-list');
      notificationsList.innerHTML = '<li class="list-group-item">Failed to load notifications</li>';
    }
  }
  
  /**
   * Load recent activities
   */
  async loadRecentActivities() {
    try {
      const response = await this.api.getDataProcessingActivities({ limit: 5, sortBy: 'updatedAt', sortOrder: 'desc' });
      const activities = response.data;
      
      const activitiesList = document.getElementById('recent-activities-list');
      activitiesList.innerHTML = '';
      
      if (activities && activities.length > 0) {
        activities.forEach(activity => {
          const li = document.createElement('li');
          li.className = 'list-group-item';
          
          const date = new Date(activity.updatedAt);
          const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
          
          const statusBadgeClass = activity.status === 'active' ? 'bg-success' : (activity.status === 'inactive' ? 'bg-warning' : 'bg-secondary');
          
          li.innerHTML = `
            <div>
              <strong>${activity.name}</strong>
              <span class="badge ${statusBadgeClass}">${activity.status}</span>
              <p class="mb-0">${activity.purpose}</p>
              <small class="text-muted">Updated: ${formattedDate}</small>
            </div>
          `;
          
          activitiesList.appendChild(li);
        });
      } else {
        const li = document.createElement('li');
        li.className = 'list-group-item';
        li.textContent = 'No recent activities';
        activitiesList.appendChild(li);
      }
    } catch (error) {
      console.error('Error loading activities:', error);
      
      const activitiesList = document.getElementById('recent-activities-list');
      activitiesList.innerHTML = '<li class="list-group-item">Failed to load activities</li>';
    }
  }
  
  /**
   * Create the DSR chart
   * @param {Object} metrics - Dashboard metrics
   */
  createDsrChart(metrics) {
    const ctx = document.getElementById('dsr-chart').getContext('2d');
    
    // Destroy existing chart if it exists
    if (this.dsrChart) {
      this.dsrChart.destroy();
    }
    
    // Create a new chart
    this.dsrChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        datasets: [{
          label: 'Completion Rate (%)',
          data: [65, 70, 75, 80, 85, 90, 92, 94, 95, 96, 97, 98],
          borderColor: 'rgba(75, 192, 192, 1)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          tension: 0.4,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
          },
          tooltip: {
            mode: 'index',
            intersect: false,
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
            ticks: {
              callback: function(value) {
                return value + '%';
              }
            }
          }
        }
      }
    });
  }
}

// Initialize the dashboard when the API client is available
const dashboard = new Dashboard(api);
