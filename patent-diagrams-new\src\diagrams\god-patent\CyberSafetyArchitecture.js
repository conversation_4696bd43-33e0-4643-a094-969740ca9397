import React from 'react';
import {
  Di<PERSON>ram<PERSON>rame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText
} from '../../components/DiagramComponents';

const CyberSafetyArchitecture = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>CYBER-SAFETY PROTOCOL ARCHITECTURE</ContainerLabel>
      </ContainerBox>
      
      {/* Core Protocol Layer */}
      <ContainerBox width="700px" height="100px" left="50px" top="70px">
        <ContainerLabel>CYBER-SAFETY PROTOCOL CORE</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="100px" top="110px" width="120px">
        <ComponentNumber>101</ComponentNumber>
        <ComponentLabel>Native Unification</ComponentLabel>
        Engine
      </ComponentBox>
      
      <ComponentBox left="250px" top="110px" width="120px">
        <ComponentNumber>102</ComponentNumber>
        <ComponentLabel>Dynamic UI</ComponentLabel>
        Enforcement
      </ComponentBox>
      
      <ComponentBox left="400px" top="110px" width="120px">
        <ComponentNumber>103</ComponentNumber>
        <ComponentLabel>Cross-Domain</ComponentLabel>
        Intelligence
      </ComponentBox>
      
      <ComponentBox left="550px" top="110px" width="120px">
        <ComponentNumber>104</ComponentNumber>
        <ComponentLabel>Protocol</ComponentLabel>
        Orchestration
      </ComponentBox>
      
      {/* Universal Components Layer */}
      <ContainerBox width="700px" height="150px" left="50px" top="200px">
        <ContainerLabel>UNIVERSAL COMPONENTS</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="80px" top="240px" width="100px" height="40px">
        <ComponentNumber>105</ComponentNumber>
        <ComponentLabel>NovaCore</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="190px" top="240px" width="100px" height="40px">
        <ComponentNumber>106</ComponentNumber>
        <ComponentLabel>NovaShield</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="300px" top="240px" width="100px" height="40px">
        <ComponentNumber>107</ComponentNumber>
        <ComponentLabel>NovaTrack</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="410px" top="240px" width="100px" height="40px">
        <ComponentNumber>108</ComponentNumber>
        <ComponentLabel>NovaLearn</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="520px" top="240px" width="100px" height="40px">
        <ComponentNumber>109</ComponentNumber>
        <ComponentLabel>NovaView</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="630px" top="240px" width="100px" height="40px">
        <ComponentNumber>110</ComponentNumber>
        <ComponentLabel>NovaFlowX</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="80px" top="290px" width="100px" height="40px">
        <ComponentNumber>111</ComponentNumber>
        <ComponentLabel>NovaPulse+</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="190px" top="290px" width="100px" height="40px">
        <ComponentNumber>112</ComponentNumber>
        <ComponentLabel>NovaProof</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="300px" top="290px" width="100px" height="40px">
        <ComponentNumber>113</ComponentNumber>
        <ComponentLabel>NovaThink</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="410px" top="290px" width="100px" height="40px">
        <ComponentNumber>114</ComponentNumber>
        <ComponentLabel>NovaConnect</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="520px" top="290px" width="100px" height="40px">
        <ComponentNumber>115</ComponentNumber>
        <ComponentLabel>NovaVision</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="630px" top="290px" width="100px" height="40px">
        <ComponentNumber>116</ComponentNumber>
        <ComponentLabel>NovaDNA</ComponentLabel>
      </ComponentBox>
      
      {/* Implementation Layer */}
      <ContainerBox width="700px" height="100px" left="50px" top="380px">
        <ContainerLabel>INDUSTRY-SPECIFIC IMPLEMENTATIONS</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="80px" top="420px" width="100px" height="40px" style={{ opacity: 0.7 }}>
        <ComponentNumber>117</ComponentNumber>
        <ComponentLabel>Healthcare</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="190px" top="420px" width="100px" height="40px" style={{ opacity: 0.7 }}>
        <ComponentNumber>118</ComponentNumber>
        <ComponentLabel>Financial</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="300px" top="420px" width="100px" height="40px" style={{ opacity: 0.7 }}>
        <ComponentNumber>119</ComponentNumber>
        <ComponentLabel>Education</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="410px" top="420px" width="100px" height="40px" style={{ opacity: 0.7 }}>
        <ComponentNumber>120</ComponentNumber>
        <ComponentLabel>Government</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="520px" top="420px" width="100px" height="40px" style={{ opacity: 0.7 }}>
        <ComponentNumber>121</ComponentNumber>
        <ComponentLabel>Infrastructure</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="630px" top="420px" width="100px" height="40px" style={{ opacity: 0.7 }}>
        <ComponentNumber>122</ComponentNumber>
        <ComponentLabel>Mobile/IoT</ComponentLabel>
      </ComponentBox>
      
      {/* Connecting arrows */}
      <Arrow left="160px" top="170px" width="2px" height="30px" />
      <Arrow left="310px" top="170px" width="2px" height="30px" />
      <Arrow left="460px" top="170px" width="2px" height="30px" />
      <Arrow left="610px" top="170px" width="2px" height="30px" />
      
      <Arrow left="160px" top="350px" width="2px" height="30px" />
      <Arrow left="310px" top="350px" width="2px" height="30px" />
      <Arrow left="460px" top="350px" width="2px" height="30px" />
      <Arrow left="610px" top="350px" width="2px" height="30px" />
      
      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Protocol Core</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Universal Components</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Industry Implementations</LegendText>
        </LegendItem>
      </DiagramLegend>
    </DiagramFrame>
  );
};

export default CyberSafetyArchitecture;
