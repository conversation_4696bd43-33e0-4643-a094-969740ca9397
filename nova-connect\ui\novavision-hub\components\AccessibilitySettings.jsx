/**
 * AccessibilitySettings Component
 * 
 * A component for managing accessibility settings.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useAccessibility } from '../accessibility/AccessibilityContext';
import { useTheme } from '../theme/ThemeContext';
import { Animated } from './Animated';
import { useI18n } from '../i18n/I18nContext';

/**
 * AccessibilitySettings component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.variant='panel'] - Variant ('panel', 'dialog', or 'inline')
 * @param {boolean} [props.showTitle=true] - Whether to show the title
 * @param {boolean} [props.showDescription=true] - Whether to show the description
 * @param {boolean} [props.showResetButton=true] - Whether to show the reset button
 * @param {Function} [props.onClose] - Callback when the component is closed
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} AccessibilitySettings component
 */
const AccessibilitySettings = ({
  variant = 'panel',
  showTitle = true,
  showDescription = true,
  showResetButton = true,
  onClose,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const { settings, updateSettings, toggleSetting, resetSettings } = useAccessibility();
  const { translate } = useI18n();
  
  // State
  const [activeTab, setActiveTab] = useState('visual');
  
  // Handle setting change
  const handleSettingChange = (setting, value) => {
    updateSettings({ [setting]: value });
  };
  
  // Handle setting toggle
  const handleSettingToggle = (setting) => {
    toggleSetting(setting);
  };
  
  // Handle reset
  const handleReset = () => {
    resetSettings();
  };
  
  // Handle close
  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };
  
  // Tabs
  const tabs = [
    {
      id: 'visual',
      label: translate('accessibility.visual', 'Visual'),
      settings: [
        {
          id: 'highContrast',
          label: translate('accessibility.highContrast', 'High Contrast'),
          description: translate('accessibility.highContrastDescription', 'Increases contrast for better readability'),
          value: settings.highContrast
        },
        {
          id: 'largeText',
          label: translate('accessibility.largeText', 'Large Text'),
          description: translate('accessibility.largeTextDescription', 'Increases text size for better readability'),
          value: settings.largeText
        },
        {
          id: 'reducedMotion',
          label: translate('accessibility.reducedMotion', 'Reduced Motion'),
          description: translate('accessibility.reducedMotionDescription', 'Reduces or eliminates animations and transitions'),
          value: settings.reducedMotion
        }
      ]
    },
    {
      id: 'interaction',
      label: translate('accessibility.interaction', 'Interaction'),
      settings: [
        {
          id: 'keyboardNavigation',
          label: translate('accessibility.keyboardNavigation', 'Keyboard Navigation'),
          description: translate('accessibility.keyboardNavigationDescription', 'Enables navigation using keyboard shortcuts'),
          value: settings.keyboardNavigation
        },
        {
          id: 'focusVisible',
          label: translate('accessibility.focusVisible', 'Focus Visible'),
          description: translate('accessibility.focusVisibleDescription', 'Makes focused elements more visible'),
          value: settings.focusVisible
        },
        {
          id: 'screenReader',
          label: translate('accessibility.screenReader', 'Screen Reader'),
          description: translate('accessibility.screenReaderDescription', 'Optimizes for screen readers'),
          value: settings.screenReader
        }
      ]
    }
  ];
  
  // Get active tab
  const activeTabData = tabs.find(tab => tab.id === activeTab);
  
  // Panel variant
  if (variant === 'panel') {
    return (
      <div
        className={`bg-surface rounded-lg shadow-lg overflow-hidden ${className}`}
        style={style}
        data-testid="accessibility-settings-panel"
      >
        {/* Header */}
        {showTitle && (
          <div className="bg-background p-4 border-b border-divider flex justify-between items-center">
            <h2 className="text-xl font-bold text-textPrimary">
              {translate('accessibility.accessibilitySettings', 'Accessibility Settings')}
            </h2>
            
            {onClose && (
              <button
                type="button"
                className="text-textSecondary hover:text-textPrimary p-1 rounded-full"
                onClick={handleClose}
                aria-label={translate('common.close', 'Close')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        )}
        
        {/* Description */}
        {showDescription && (
          <div className="p-4 border-b border-divider">
            <p className="text-textSecondary">
              {translate('accessibility.settingsDescription', 'Customize accessibility settings to improve your experience.')}
            </p>
          </div>
        )}
        
        {/* Tabs */}
        <div className="flex border-b border-divider">
          {tabs.map(tab => (
            <button
              key={tab.id}
              type="button"
              className={`flex-1 py-3 px-4 text-center font-medium ${
                activeTab === tab.id
                  ? 'text-primary border-b-2 border-primary'
                  : 'text-textSecondary hover:text-textPrimary'
              }`}
              onClick={() => setActiveTab(tab.id)}
              aria-selected={activeTab === tab.id}
              role="tab"
            >
              {tab.label}
            </button>
          ))}
        </div>
        
        {/* Settings */}
        <div className="p-4">
          {activeTabData && (
            <Animated animation="fadeIn" className="space-y-4">
              {activeTabData.settings.map(setting => (
                <div key={setting.id} className="flex items-start">
                  <div className="flex-1">
                    <div className="font-medium text-textPrimary">{setting.label}</div>
                    <div className="text-sm text-textSecondary">{setting.description}</div>
                  </div>
                  <div className="ml-4">
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only"
                        checked={setting.value}
                        onChange={() => handleSettingToggle(setting.id)}
                      />
                      <div className={`relative w-11 h-6 bg-background border border-divider rounded-full transition-colors duration-200 ease-in-out ${setting.value ? 'bg-primary border-primary' : ''}`}>
                        <div className={`absolute left-0.5 top-0.5 bg-white w-5 h-5 rounded-full transition-transform duration-200 ease-in-out ${setting.value ? 'transform translate-x-5' : ''}`}></div>
                      </div>
                    </label>
                  </div>
                </div>
              ))}
            </Animated>
          )}
        </div>
        
        {/* Footer */}
        {showResetButton && (
          <div className="bg-background p-4 border-t border-divider">
            <button
              type="button"
              className="text-textSecondary hover:text-textPrimary"
              onClick={handleReset}
            >
              {translate('common.resetToDefaults', 'Reset to Defaults')}
            </button>
          </div>
        )}
      </div>
    );
  }
  
  // Dialog variant
  if (variant === 'dialog') {
    return (
      <div
        className={`fixed inset-0 z-50 flex items-center justify-center p-4 ${className}`}
        style={style}
        data-testid="accessibility-settings-dialog"
      >
        <div className="fixed inset-0 bg-black bg-opacity-50" onClick={handleClose}></div>
        
        <Animated
          animation="zoomIn"
          className="bg-surface rounded-lg shadow-lg overflow-hidden w-full max-w-md relative"
        >
          {/* Header */}
          {showTitle && (
            <div className="bg-background p-4 border-b border-divider flex justify-between items-center">
              <h2 className="text-xl font-bold text-textPrimary">
                {translate('accessibility.accessibilitySettings', 'Accessibility Settings')}
              </h2>
              
              <button
                type="button"
                className="text-textSecondary hover:text-textPrimary p-1 rounded-full"
                onClick={handleClose}
                aria-label={translate('common.close', 'Close')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          )}
          
          {/* Description */}
          {showDescription && (
            <div className="p-4 border-b border-divider">
              <p className="text-textSecondary">
                {translate('accessibility.settingsDescription', 'Customize accessibility settings to improve your experience.')}
              </p>
            </div>
          )}
          
          {/* Tabs */}
          <div className="flex border-b border-divider">
            {tabs.map(tab => (
              <button
                key={tab.id}
                type="button"
                className={`flex-1 py-3 px-4 text-center font-medium ${
                  activeTab === tab.id
                    ? 'text-primary border-b-2 border-primary'
                    : 'text-textSecondary hover:text-textPrimary'
                }`}
                onClick={() => setActiveTab(tab.id)}
                aria-selected={activeTab === tab.id}
                role="tab"
              >
                {tab.label}
              </button>
            ))}
          </div>
          
          {/* Settings */}
          <div className="p-4 max-h-80 overflow-y-auto">
            {activeTabData && (
              <Animated animation="fadeIn" className="space-y-4">
                {activeTabData.settings.map(setting => (
                  <div key={setting.id} className="flex items-start">
                    <div className="flex-1">
                      <div className="font-medium text-textPrimary">{setting.label}</div>
                      <div className="text-sm text-textSecondary">{setting.description}</div>
                    </div>
                    <div className="ml-4">
                      <label className="inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only"
                          checked={setting.value}
                          onChange={() => handleSettingToggle(setting.id)}
                        />
                        <div className={`relative w-11 h-6 bg-background border border-divider rounded-full transition-colors duration-200 ease-in-out ${setting.value ? 'bg-primary border-primary' : ''}`}>
                          <div className={`absolute left-0.5 top-0.5 bg-white w-5 h-5 rounded-full transition-transform duration-200 ease-in-out ${setting.value ? 'transform translate-x-5' : ''}`}></div>
                        </div>
                      </label>
                    </div>
                  </div>
                ))}
              </Animated>
            )}
          </div>
          
          {/* Footer */}
          <div className="bg-background p-4 border-t border-divider flex justify-between">
            {showResetButton && (
              <button
                type="button"
                className="text-textSecondary hover:text-textPrimary"
                onClick={handleReset}
              >
                {translate('common.resetToDefaults', 'Reset to Defaults')}
              </button>
            )}
            
            <button
              type="button"
              className="bg-primary text-primaryContrast px-4 py-2 rounded-md hover:bg-primaryDark transition-colors duration-200"
              onClick={handleClose}
            >
              {translate('common.done', 'Done')}
            </button>
          </div>
        </Animated>
      </div>
    );
  }
  
  // Inline variant
  return (
    <div
      className={`${className}`}
      style={style}
      data-testid="accessibility-settings-inline"
    >
      {/* Title */}
      {showTitle && (
        <h2 className="text-xl font-bold text-textPrimary mb-4">
          {translate('accessibility.accessibilitySettings', 'Accessibility Settings')}
        </h2>
      )}
      
      {/* Description */}
      {showDescription && (
        <p className="text-textSecondary mb-4">
          {translate('accessibility.settingsDescription', 'Customize accessibility settings to improve your experience.')}
        </p>
      )}
      
      {/* Settings */}
      <div className="space-y-4">
        {tabs.map(tab => (
          <div key={tab.id}>
            <h3 className="text-lg font-medium text-textPrimary mb-2">{tab.label}</h3>
            <div className="space-y-4">
              {tab.settings.map(setting => (
                <div key={setting.id} className="flex items-start">
                  <div className="flex-1">
                    <div className="font-medium text-textPrimary">{setting.label}</div>
                    <div className="text-sm text-textSecondary">{setting.description}</div>
                  </div>
                  <div className="ml-4">
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only"
                        checked={setting.value}
                        onChange={() => handleSettingToggle(setting.id)}
                      />
                      <div className={`relative w-11 h-6 bg-background border border-divider rounded-full transition-colors duration-200 ease-in-out ${setting.value ? 'bg-primary border-primary' : ''}`}>
                        <div className={`absolute left-0.5 top-0.5 bg-white w-5 h-5 rounded-full transition-transform duration-200 ease-in-out ${setting.value ? 'transform translate-x-5' : ''}`}></div>
                      </div>
                    </label>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
      
      {/* Reset button */}
      {showResetButton && (
        <div className="mt-4">
          <button
            type="button"
            className="text-textSecondary hover:text-textPrimary"
            onClick={handleReset}
          >
            {translate('common.resetToDefaults', 'Reset to Defaults')}
          </button>
        </div>
      )}
    </div>
  );
};

AccessibilitySettings.propTypes = {
  variant: PropTypes.oneOf(['panel', 'dialog', 'inline']),
  showTitle: PropTypes.bool,
  showDescription: PropTypes.bool,
  showResetButton: PropTypes.bool,
  onClose: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default AccessibilitySettings;
