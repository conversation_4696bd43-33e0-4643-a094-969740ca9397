/**
 * NovaFuse Universal API Connector Validator
 * 
 * This module provides validation utilities for connector templates.
 */

/**
 * Connector Validator
 * 
 * Validates connector templates against schema requirements.
 */
class ConnectorValidator {
  /**
   * Validate a connector template
   * @param {Object} connector - Connector template to validate
   * @returns {Object} - Validation result with isValid and errors properties
   */
  static validateConnector(connector) {
    const errors = [];
    const result = { isValid: true, errors };
    
    if (!connector) {
      result.isValid = false;
      errors.push('Connector template is required');
      return result;
    }
    
    // Validate metadata
    this.validateMetadata(connector, errors);
    
    // Validate authentication
    this.validateAuthentication(connector, errors);
    
    // Validate configuration
    this.validateConfiguration(connector, errors);
    
    // Validate endpoints
    this.validateEndpoints(connector, errors);
    
    // Update isValid based on errors
    result.isValid = errors.length === 0;
    
    return result;
  }

  /**
   * Validate connector metadata
   * @param {Object} connector - Connector template
   * @param {Array} errors - Array to add errors to
   */
  static validateMetadata(connector, errors) {
    if (!connector.metadata) {
      errors.push('Connector metadata is required');
      return;
    }
    
    const { metadata } = connector;
    
    // Check required fields
    if (!metadata.name) {
      errors.push('Connector name is required');
    } else if (typeof metadata.name !== 'string') {
      errors.push('Connector name must be a string');
    }
    
    if (!metadata.version) {
      errors.push('Connector version is required');
    } else if (typeof metadata.version !== 'string') {
      errors.push('Connector version must be a string');
    } else if (!this.isValidVersion(metadata.version)) {
      errors.push('Connector version must be in semver format (e.g., 1.0.0)');
    }
    
    if (!metadata.category) {
      errors.push('Connector category is required');
    } else if (typeof metadata.category !== 'string') {
      errors.push('Connector category must be a string');
    }
    
    if (!metadata.description) {
      errors.push('Connector description is required');
    } else if (typeof metadata.description !== 'string') {
      errors.push('Connector description must be a string');
    }
    
    // Check optional fields
    if (metadata.author && typeof metadata.author !== 'string') {
      errors.push('Connector author must be a string');
    }
    
    if (metadata.tags) {
      if (!Array.isArray(metadata.tags)) {
        errors.push('Connector tags must be an array');
      } else {
        for (const tag of metadata.tags) {
          if (typeof tag !== 'string') {
            errors.push('Connector tags must be strings');
            break;
          }
        }
      }
    }
    
    if (metadata.icon && typeof metadata.icon !== 'string') {
      errors.push('Connector icon must be a string URL');
    }
    
    if (metadata.documentationUrl && typeof metadata.documentationUrl !== 'string') {
      errors.push('Connector documentation URL must be a string');
    }
  }

  /**
   * Validate connector authentication
   * @param {Object} connector - Connector template
   * @param {Array} errors - Array to add errors to
   */
  static validateAuthentication(connector, errors) {
    if (!connector.authentication) {
      errors.push('Connector authentication is required');
      return;
    }
    
    const { authentication } = connector;
    
    // Check authentication type
    if (!authentication.type) {
      errors.push('Authentication type is required');
    } else if (typeof authentication.type !== 'string') {
      errors.push('Authentication type must be a string');
    } else if (!['API_KEY', 'BASIC', 'OAUTH2', 'CUSTOM', 'NONE'].includes(authentication.type)) {
      errors.push('Authentication type must be one of: API_KEY, BASIC, OAUTH2, CUSTOM, NONE');
    }
    
    // Check authentication fields
    if (!authentication.fields) {
      errors.push('Authentication fields are required');
      return;
    }
    
    if (typeof authentication.fields !== 'object') {
      errors.push('Authentication fields must be an object');
      return;
    }
    
    // Validate each field
    for (const [key, field] of Object.entries(authentication.fields)) {
      if (typeof field !== 'object') {
        errors.push(`Authentication field '${key}' must be an object`);
        continue;
      }
      
      if (!field.type) {
        errors.push(`Authentication field '${key}' must have a type`);
      } else if (typeof field.type !== 'string') {
        errors.push(`Authentication field '${key}' type must be a string`);
      } else if (!['string', 'number', 'boolean', 'object', 'array'].includes(field.type)) {
        errors.push(`Authentication field '${key}' type must be one of: string, number, boolean, object, array`);
      }
      
      if (field.required !== undefined && typeof field.required !== 'boolean') {
        errors.push(`Authentication field '${key}' required must be a boolean`);
      }
      
      if (field.sensitive !== undefined && typeof field.sensitive !== 'boolean') {
        errors.push(`Authentication field '${key}' sensitive must be a boolean`);
      }
      
      if (field.description && typeof field.description !== 'string') {
        errors.push(`Authentication field '${key}' description must be a string`);
      }
    }
    
    // Validate OAuth2 config if applicable
    if (authentication.type === 'OAUTH2' && authentication.oauth2Config) {
      this.validateOAuth2Config(authentication.oauth2Config, errors);
    }
  }

  /**
   * Validate OAuth2 configuration
   * @param {Object} oauth2Config - OAuth2 configuration
   * @param {Array} errors - Array to add errors to
   */
  static validateOAuth2Config(oauth2Config, errors) {
    if (typeof oauth2Config !== 'object') {
      errors.push('OAuth2 configuration must be an object');
      return;
    }
    
    // Check required fields
    if (!oauth2Config.tokenUrl) {
      errors.push('OAuth2 token URL is required');
    } else if (typeof oauth2Config.tokenUrl !== 'string') {
      errors.push('OAuth2 token URL must be a string');
    }
    
    // Check optional fields
    if (oauth2Config.authorizationUrl && typeof oauth2Config.authorizationUrl !== 'string') {
      errors.push('OAuth2 authorization URL must be a string');
    }
    
    if (oauth2Config.scopes) {
      if (!Array.isArray(oauth2Config.scopes)) {
        errors.push('OAuth2 scopes must be an array');
      } else {
        for (const scope of oauth2Config.scopes) {
          if (typeof scope !== 'string') {
            errors.push('OAuth2 scopes must be strings');
            break;
          }
        }
      }
    }
    
    if (oauth2Config.grantType && typeof oauth2Config.grantType !== 'string') {
      errors.push('OAuth2 grant type must be a string');
    }
  }

  /**
   * Validate connector configuration
   * @param {Object} connector - Connector template
   * @param {Array} errors - Array to add errors to
   */
  static validateConfiguration(connector, errors) {
    if (!connector.configuration) {
      errors.push('Connector configuration is required');
      return;
    }
    
    const { configuration } = connector;
    
    // Check required fields
    if (!configuration.baseUrl) {
      errors.push('Configuration base URL is required');
    } else if (typeof configuration.baseUrl !== 'string') {
      errors.push('Configuration base URL must be a string');
    } else if (!this.isValidUrl(configuration.baseUrl)) {
      errors.push('Configuration base URL must be a valid URL');
    }
    
    // Check optional fields
    if (configuration.headers && typeof configuration.headers !== 'object') {
      errors.push('Configuration headers must be an object');
    }
    
    if (configuration.timeout !== undefined) {
      if (typeof configuration.timeout !== 'number') {
        errors.push('Configuration timeout must be a number');
      } else if (configuration.timeout <= 0) {
        errors.push('Configuration timeout must be greater than 0');
      }
    }
    
    if (configuration.rateLimit) {
      if (typeof configuration.rateLimit !== 'object') {
        errors.push('Configuration rate limit must be an object');
      } else {
        if (configuration.rateLimit.requests !== undefined) {
          if (typeof configuration.rateLimit.requests !== 'number') {
            errors.push('Configuration rate limit requests must be a number');
          } else if (configuration.rateLimit.requests <= 0) {
            errors.push('Configuration rate limit requests must be greater than 0');
          }
        }
        
        if (configuration.rateLimit.period !== undefined && typeof configuration.rateLimit.period !== 'string') {
          errors.push('Configuration rate limit period must be a string');
        }
      }
    }
    
    if (configuration.retryPolicy) {
      if (typeof configuration.retryPolicy !== 'object') {
        errors.push('Configuration retry policy must be an object');
      } else {
        if (configuration.retryPolicy.maxRetries !== undefined) {
          if (typeof configuration.retryPolicy.maxRetries !== 'number') {
            errors.push('Configuration retry policy max retries must be a number');
          } else if (configuration.retryPolicy.maxRetries < 0) {
            errors.push('Configuration retry policy max retries must be greater than or equal to 0');
          }
        }
        
        if (configuration.retryPolicy.backoffStrategy !== undefined && typeof configuration.retryPolicy.backoffStrategy !== 'string') {
          errors.push('Configuration retry policy backoff strategy must be a string');
        }
      }
    }
  }

  /**
   * Validate connector endpoints
   * @param {Object} connector - Connector template
   * @param {Array} errors - Array to add errors to
   */
  static validateEndpoints(connector, errors) {
    if (!connector.endpoints) {
      errors.push('Connector endpoints are required');
      return;
    }
    
    if (!Array.isArray(connector.endpoints)) {
      errors.push('Connector endpoints must be an array');
      return;
    }
    
    if (connector.endpoints.length === 0) {
      errors.push('Connector must have at least one endpoint');
      return;
    }
    
    // Validate each endpoint
    for (let i = 0; i < connector.endpoints.length; i++) {
      const endpoint = connector.endpoints[i];
      
      if (typeof endpoint !== 'object') {
        errors.push(`Endpoint at index ${i} must be an object`);
        continue;
      }
      
      // Check required fields
      if (!endpoint.id) {
        errors.push(`Endpoint at index ${i} must have an ID`);
      } else if (typeof endpoint.id !== 'string') {
        errors.push(`Endpoint at index ${i} ID must be a string`);
      }
      
      if (!endpoint.name) {
        errors.push(`Endpoint at index ${i} must have a name`);
      } else if (typeof endpoint.name !== 'string') {
        errors.push(`Endpoint at index ${i} name must be a string`);
      }
      
      if (!endpoint.path) {
        errors.push(`Endpoint at index ${i} must have a path`);
      } else if (typeof endpoint.path !== 'string') {
        errors.push(`Endpoint at index ${i} path must be a string`);
      }
      
      if (!endpoint.method) {
        errors.push(`Endpoint at index ${i} must have a method`);
      } else if (typeof endpoint.method !== 'string') {
        errors.push(`Endpoint at index ${i} method must be a string`);
      } else if (!['GET', 'POST', 'PUT', 'PATCH', 'DELETE'].includes(endpoint.method)) {
        errors.push(`Endpoint at index ${i} method must be one of: GET, POST, PUT, PATCH, DELETE`);
      }
      
      // Validate parameters if present
      if (endpoint.parameters) {
        this.validateEndpointParameters(endpoint.parameters, i, errors);
      }
      
      // Validate response if present
      if (endpoint.response) {
        this.validateEndpointResponse(endpoint.response, i, errors);
      }
      
      // Validate pagination if present
      if (endpoint.pagination) {
        this.validateEndpointPagination(endpoint.pagination, i, errors);
      }
    }
    
    // Check for duplicate endpoint IDs
    const endpointIds = connector.endpoints.map(e => e.id);
    const duplicateIds = endpointIds.filter((id, index) => endpointIds.indexOf(id) !== index);
    
    if (duplicateIds.length > 0) {
      errors.push(`Duplicate endpoint IDs found: ${duplicateIds.join(', ')}`);
    }
  }

  /**
   * Validate endpoint parameters
   * @param {Object} parameters - Endpoint parameters
   * @param {number} endpointIndex - Index of the endpoint
   * @param {Array} errors - Array to add errors to
   */
  static validateEndpointParameters(parameters, endpointIndex, errors) {
    if (typeof parameters !== 'object') {
      errors.push(`Endpoint at index ${endpointIndex} parameters must be an object`);
      return;
    }
    
    // Validate path parameters
    if (parameters.path) {
      if (typeof parameters.path !== 'object') {
        errors.push(`Endpoint at index ${endpointIndex} path parameters must be an object`);
      } else {
        for (const [key, param] of Object.entries(parameters.path)) {
          this.validateParameter(param, key, 'path', endpointIndex, errors);
        }
      }
    }
    
    // Validate query parameters
    if (parameters.query) {
      if (typeof parameters.query !== 'object') {
        errors.push(`Endpoint at index ${endpointIndex} query parameters must be an object`);
      } else {
        for (const [key, param] of Object.entries(parameters.query)) {
          this.validateParameter(param, key, 'query', endpointIndex, errors);
        }
      }
    }
    
    // Validate body parameters
    if (parameters.body) {
      if (typeof parameters.body !== 'object') {
        errors.push(`Endpoint at index ${endpointIndex} body parameters must be an object`);
      } else {
        if (parameters.body.required !== undefined && typeof parameters.body.required !== 'boolean') {
          errors.push(`Endpoint at index ${endpointIndex} body required must be a boolean`);
        }
        
        if (parameters.body.properties && typeof parameters.body.properties !== 'object') {
          errors.push(`Endpoint at index ${endpointIndex} body properties must be an object`);
        }
      }
    }
  }

  /**
   * Validate a parameter
   * @param {Object} param - Parameter to validate
   * @param {string} key - Parameter key
   * @param {string} type - Parameter type (path, query, body)
   * @param {number} endpointIndex - Index of the endpoint
   * @param {Array} errors - Array to add errors to
   */
  static validateParameter(param, key, type, endpointIndex, errors) {
    if (typeof param !== 'object') {
      errors.push(`Endpoint at index ${endpointIndex} ${type} parameter '${key}' must be an object`);
      return;
    }
    
    if (param.type && typeof param.type !== 'string') {
      errors.push(`Endpoint at index ${endpointIndex} ${type} parameter '${key}' type must be a string`);
    }
    
    if (param.required !== undefined && typeof param.required !== 'boolean') {
      errors.push(`Endpoint at index ${endpointIndex} ${type} parameter '${key}' required must be a boolean`);
    }
    
    if (param.description && typeof param.description !== 'string') {
      errors.push(`Endpoint at index ${endpointIndex} ${type} parameter '${key}' description must be a string`);
    }
  }

  /**
   * Validate endpoint response
   * @param {Object} response - Endpoint response
   * @param {number} endpointIndex - Index of the endpoint
   * @param {Array} errors - Array to add errors to
   */
  static validateEndpointResponse(response, endpointIndex, errors) {
    if (typeof response !== 'object') {
      errors.push(`Endpoint at index ${endpointIndex} response must be an object`);
      return;
    }
    
    if (response.successCode !== undefined) {
      if (typeof response.successCode !== 'number') {
        errors.push(`Endpoint at index ${endpointIndex} response success code must be a number`);
      } else if (response.successCode < 200 || response.successCode >= 300) {
        errors.push(`Endpoint at index ${endpointIndex} response success code must be in the 2xx range`);
      }
    }
    
    if (response.dataPath && typeof response.dataPath !== 'string') {
      errors.push(`Endpoint at index ${endpointIndex} response data path must be a string`);
    }
    
    if (response.schema && typeof response.schema !== 'object') {
      errors.push(`Endpoint at index ${endpointIndex} response schema must be an object`);
    }
  }

  /**
   * Validate endpoint pagination
   * @param {Object} pagination - Endpoint pagination
   * @param {number} endpointIndex - Index of the endpoint
   * @param {Array} errors - Array to add errors to
   */
  static validateEndpointPagination(pagination, endpointIndex, errors) {
    if (typeof pagination !== 'object') {
      errors.push(`Endpoint at index ${endpointIndex} pagination must be an object`);
      return;
    }
    
    if (!pagination.type) {
      errors.push(`Endpoint at index ${endpointIndex} pagination type is required`);
    } else if (typeof pagination.type !== 'string') {
      errors.push(`Endpoint at index ${endpointIndex} pagination type must be a string`);
    } else if (!['offset', 'token', 'page', 'cursor'].includes(pagination.type)) {
      errors.push(`Endpoint at index ${endpointIndex} pagination type must be one of: offset, token, page, cursor`);
    }
    
    if (!pagination.parameters) {
      errors.push(`Endpoint at index ${endpointIndex} pagination parameters are required`);
    } else if (typeof pagination.parameters !== 'object') {
      errors.push(`Endpoint at index ${endpointIndex} pagination parameters must be an object`);
    } else {
      // Validate based on pagination type
      switch (pagination.type) {
        case 'offset':
          if (!pagination.parameters.limit) {
            errors.push(`Endpoint at index ${endpointIndex} pagination offset requires a limit parameter`);
          }
          if (!pagination.parameters.offset) {
            errors.push(`Endpoint at index ${endpointIndex} pagination offset requires an offset parameter`);
          }
          break;
        case 'token':
          if (!pagination.parameters.nextPageToken) {
            errors.push(`Endpoint at index ${endpointIndex} pagination token requires a nextPageToken parameter`);
          }
          if (!pagination.parameters.pageToken) {
            errors.push(`Endpoint at index ${endpointIndex} pagination token requires a pageToken parameter`);
          }
          break;
        case 'page':
          if (!pagination.parameters.page) {
            errors.push(`Endpoint at index ${endpointIndex} pagination page requires a page parameter`);
          }
          if (!pagination.parameters.pageSize) {
            errors.push(`Endpoint at index ${endpointIndex} pagination page requires a pageSize parameter`);
          }
          break;
        case 'cursor':
          if (!pagination.parameters.cursor) {
            errors.push(`Endpoint at index ${endpointIndex} pagination cursor requires a cursor parameter`);
          }
          break;
      }
    }
  }

  /**
   * Check if a string is a valid URL
   * @param {string} url - URL to check
   * @returns {boolean} - Whether the URL is valid
   */
  static isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if a string is a valid semver version
   * @param {string} version - Version to check
   * @returns {boolean} - Whether the version is valid
   */
  static isValidVersion(version) {
    const semverRegex = /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/;
    return semverRegex.test(version);
  }
}

module.exports = ConnectorValidator;
