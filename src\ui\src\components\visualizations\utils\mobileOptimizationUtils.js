/**
 * Mobile Optimization Utilities for Cyber-Safety Visualizations
 * 
 * This module provides utilities to optimize visualizations for mobile devices.
 */

/**
 * Optimize data for mobile devices by reducing complexity
 * @param {string} visualizationType - Type of visualization
 * @param {Object} data - Visualization data
 * @param {string} screenSize - Screen size (xs, sm, md, lg, xl)
 * @returns {Object} - Optimized data
 */
export const optimizeDataForMobile = (visualizationType, data, screenSize) => {
  if (!data) return data;
  
  // Skip optimization for larger screens
  if (screenSize === 'lg' || screenSize === 'xl') {
    return data;
  }
  
  switch (visualizationType) {
    case 'triDomainTensor':
      return optimizeTriDomainTensorData(data, screenSize);
    case 'harmonyIndex':
      return optimizeHarmonyIndexData(data, screenSize);
    case 'riskControlFusion':
      return optimizeRiskControlFusionData(data, screenSize);
    case 'resonanceSpectrogram':
      return optimizeResonanceSpectrogramData(data, screenSize);
    case 'unifiedComplianceSecurity':
      return optimizeUnifiedComplianceSecurityData(data, screenSize);
    default:
      return data;
  }
};

/**
 * Optimize Tri-Domain Tensor data for mobile devices
 * @param {Object} data - Tri-Domain Tensor data
 * @param {string} screenSize - Screen size (xs, sm, md)
 * @returns {Object} - Optimized data
 */
const optimizeTriDomainTensorData = (data, screenSize) => {
  if (!data) return data;
  
  // Create a copy of the data
  const optimizedData = { ...data };
  
  // Optimize connections
  if (optimizedData.connections) {
    // Reduce number of connections for smaller screens
    if (screenSize === 'xs') {
      // Keep only the strongest connections (top 30%)
      const sortedConnections = [...optimizedData.connections].sort((a, b) => b.strength - a.strength);
      const cutoff = Math.ceil(sortedConnections.length * 0.3);
      optimizedData.connections = sortedConnections.slice(0, cutoff);
    } else if (screenSize === 'sm') {
      // Keep only the strongest connections (top 50%)
      const sortedConnections = [...optimizedData.connections].sort((a, b) => b.strength - a.strength);
      const cutoff = Math.ceil(sortedConnections.length * 0.5);
      optimizedData.connections = sortedConnections.slice(0, cutoff);
    } else if (screenSize === 'md') {
      // Keep only the strongest connections (top 70%)
      const sortedConnections = [...optimizedData.connections].sort((a, b) => b.strength - a.strength);
      const cutoff = Math.ceil(sortedConnections.length * 0.7);
      optimizedData.connections = sortedConnections.slice(0, cutoff);
    }
  }
  
  return optimizedData;
};

/**
 * Optimize Harmony Index data for mobile devices
 * @param {Object} data - Harmony Index data
 * @param {string} screenSize - Screen size (xs, sm, md)
 * @returns {Object} - Optimized data
 */
const optimizeHarmonyIndexData = (data, screenSize) => {
  if (!data) return data;
  
  // Create a copy of the data
  const optimizedData = { ...data };
  
  // Optimize harmony history
  if (optimizedData.harmonyHistory) {
    // Reduce number of history points for smaller screens
    if (screenSize === 'xs') {
      // Keep only every 4th point
      optimizedData.harmonyHistory = optimizedData.harmonyHistory.filter((_, index) => index % 4 === 0);
    } else if (screenSize === 'sm') {
      // Keep only every 3rd point
      optimizedData.harmonyHistory = optimizedData.harmonyHistory.filter((_, index) => index % 3 === 0);
    } else if (screenSize === 'md') {
      // Keep only every 2nd point
      optimizedData.harmonyHistory = optimizedData.harmonyHistory.filter((_, index) => index % 2 === 0);
    }
  }
  
  return optimizedData;
};

/**
 * Optimize Risk-Control Fusion data for mobile devices
 * @param {Object} data - Risk-Control Fusion data
 * @param {string} screenSize - Screen size (xs, sm, md)
 * @returns {Object} - Optimized data
 */
const optimizeRiskControlFusionData = (data, screenSize) => {
  if (!data) return data;
  
  // Create a copy of the data
  const optimizedData = { ...data };
  
  // Optimize risk and control data
  if (optimizedData.riskData && optimizedData.controlData) {
    // For xs screens, keep only the most important categories
    if (screenSize === 'xs') {
      // Keep only top 3 categories for each domain
      Object.keys(optimizedData.riskData).forEach(domain => {
        const categories = Object.entries(optimizedData.riskData[domain]);
        const sortedCategories = categories.sort((a, b) => b[1] - a[1]);
        const topCategories = sortedCategories.slice(0, 3);
        
        optimizedData.riskData[domain] = Object.fromEntries(topCategories);
      });
      
      Object.keys(optimizedData.controlData).forEach(domain => {
        const categories = Object.entries(optimizedData.controlData[domain]);
        const sortedCategories = categories.sort((a, b) => b[1] - a[1]);
        const topCategories = sortedCategories.slice(0, 3);
        
        optimizedData.controlData[domain] = Object.fromEntries(topCategories);
      });
    } else if (screenSize === 'sm') {
      // Keep only top 5 categories for each domain
      Object.keys(optimizedData.riskData).forEach(domain => {
        const categories = Object.entries(optimizedData.riskData[domain]);
        const sortedCategories = categories.sort((a, b) => b[1] - a[1]);
        const topCategories = sortedCategories.slice(0, 5);
        
        optimizedData.riskData[domain] = Object.fromEntries(topCategories);
      });
      
      Object.keys(optimizedData.controlData).forEach(domain => {
        const categories = Object.entries(optimizedData.controlData[domain]);
        const sortedCategories = categories.sort((a, b) => b[1] - a[1]);
        const topCategories = sortedCategories.slice(0, 5);
        
        optimizedData.controlData[domain] = Object.fromEntries(topCategories);
      });
    }
  }
  
  return optimizedData;
};

/**
 * Optimize Resonance Spectrogram data for mobile devices
 * @param {Object} data - Resonance Spectrogram data
 * @param {string} screenSize - Screen size (xs, sm, md)
 * @returns {Object} - Optimized data
 */
const optimizeResonanceSpectrogramData = (data, screenSize) => {
  if (!data) return data;
  
  // Create a copy of the data
  const optimizedData = { ...data };
  
  // Optimize spectrogram data
  if (optimizedData.spectrogramData) {
    // Reduce resolution for smaller screens
    if (screenSize === 'xs') {
      // Reduce to 25% resolution
      optimizedData.spectrogramData = reduceSpectrogramResolution(optimizedData.spectrogramData, 0.25);
    } else if (screenSize === 'sm') {
      // Reduce to 50% resolution
      optimizedData.spectrogramData = reduceSpectrogramResolution(optimizedData.spectrogramData, 0.5);
    } else if (screenSize === 'md') {
      // Reduce to 75% resolution
      optimizedData.spectrogramData = reduceSpectrogramResolution(optimizedData.spectrogramData, 0.75);
    }
  }
  
  // Optimize prediction data
  if (optimizedData.predictionData && optimizedData.predictionData.criticalPoints) {
    // Reduce number of critical points for smaller screens
    if (screenSize === 'xs') {
      // Keep only the most severe critical points (top 30%)
      const sortedPoints = [...optimizedData.predictionData.criticalPoints].sort((a, b) => b.severity - a.severity);
      const cutoff = Math.ceil(sortedPoints.length * 0.3);
      optimizedData.predictionData.criticalPoints = sortedPoints.slice(0, cutoff);
    } else if (screenSize === 'sm') {
      // Keep only the most severe critical points (top 50%)
      const sortedPoints = [...optimizedData.predictionData.criticalPoints].sort((a, b) => b.severity - a.severity);
      const cutoff = Math.ceil(sortedPoints.length * 0.5);
      optimizedData.predictionData.criticalPoints = sortedPoints.slice(0, cutoff);
    } else if (screenSize === 'md') {
      // Keep only the most severe critical points (top 70%)
      const sortedPoints = [...optimizedData.predictionData.criticalPoints].sort((a, b) => b.severity - a.severity);
      const cutoff = Math.ceil(sortedPoints.length * 0.7);
      optimizedData.predictionData.criticalPoints = sortedPoints.slice(0, cutoff);
    }
  }
  
  return optimizedData;
};

/**
 * Reduce spectrogram resolution
 * @param {Array} spectrogramData - Spectrogram data
 * @param {number} factor - Resolution factor (0-1)
 * @returns {Array} - Reduced resolution spectrogram data
 */
const reduceSpectrogramResolution = (spectrogramData, factor) => {
  if (!spectrogramData || !Array.isArray(spectrogramData)) return spectrogramData;
  
  // Skip if factor is 1 (no reduction)
  if (factor >= 1) return spectrogramData;
  
  // Calculate step size
  const step = Math.ceil(1 / factor);
  
  // Filter data
  return spectrogramData.filter((_, index) => index % step === 0);
};

/**
 * Optimize Unified Compliance-Security data for mobile devices
 * @param {Object} data - Unified Compliance-Security data
 * @param {string} screenSize - Screen size (xs, sm, md)
 * @returns {Object} - Optimized data
 */
const optimizeUnifiedComplianceSecurityData = (data, screenSize) => {
  if (!data) return data;
  
  // Create a copy of the data
  const optimizedData = { ...data };
  
  // Optimize compliance data
  if (optimizedData.complianceData) {
    // Reduce number of requirements, controls, and implementations for smaller screens
    if (screenSize === 'xs') {
      // Keep only top 5 items
      if (optimizedData.complianceData.requirements) {
        optimizedData.complianceData.requirements = optimizedData.complianceData.requirements.slice(0, 5);
      }
      
      if (optimizedData.complianceData.controls) {
        optimizedData.complianceData.controls = optimizedData.complianceData.controls.slice(0, 5);
      }
      
      if (optimizedData.complianceData.implementations) {
        optimizedData.complianceData.implementations = optimizedData.complianceData.implementations.slice(0, 5);
      }
    } else if (screenSize === 'sm') {
      // Keep only top 10 items
      if (optimizedData.complianceData.requirements) {
        optimizedData.complianceData.requirements = optimizedData.complianceData.requirements.slice(0, 10);
      }
      
      if (optimizedData.complianceData.controls) {
        optimizedData.complianceData.controls = optimizedData.complianceData.controls.slice(0, 10);
      }
      
      if (optimizedData.complianceData.implementations) {
        optimizedData.complianceData.implementations = optimizedData.complianceData.implementations.slice(0, 10);
      }
    } else if (screenSize === 'md') {
      // Keep only top 15 items
      if (optimizedData.complianceData.requirements) {
        optimizedData.complianceData.requirements = optimizedData.complianceData.requirements.slice(0, 15);
      }
      
      if (optimizedData.complianceData.controls) {
        optimizedData.complianceData.controls = optimizedData.complianceData.controls.slice(0, 15);
      }
      
      if (optimizedData.complianceData.implementations) {
        optimizedData.complianceData.implementations = optimizedData.complianceData.implementations.slice(0, 15);
      }
    }
    
    // Update links to match the filtered items
    if (optimizedData.complianceData.links) {
      const requirementIds = optimizedData.complianceData.requirements?.map(r => r.id) || [];
      const controlIds = optimizedData.complianceData.controls?.map(c => c.id) || [];
      const implementationIds = optimizedData.complianceData.implementations?.map(i => i.id) || [];
      
      optimizedData.complianceData.links = optimizedData.complianceData.links.filter(link => {
        return (
          (link.source.startsWith('req_') ? requirementIds.includes(link.source) : true) &&
          (link.source.startsWith('ctrl_') ? controlIds.includes(link.source) : true) &&
          (link.source.startsWith('impl_') ? implementationIds.includes(link.source) : true) &&
          (link.target.startsWith('req_') ? requirementIds.includes(link.target) : true) &&
          (link.target.startsWith('ctrl_') ? controlIds.includes(link.target) : true) &&
          (link.target.startsWith('impl_') ? implementationIds.includes(link.target) : true)
        );
      });
    }
  }
  
  return optimizedData;
};

/**
 * Get mobile-optimized layout options for visualizations
 * @param {string} visualizationType - Type of visualization
 * @param {string} screenSize - Screen size (xs, sm, md, lg, xl)
 * @returns {Object} - Layout options
 */
export const getMobileLayout = (visualizationType, screenSize) => {
  // Default layout options
  const defaultLayout = {
    showLegend: screenSize !== 'xs',
    showTooltips: true,
    showControls: screenSize !== 'xs',
    showTitle: true,
    showAxisLabels: screenSize !== 'xs',
    compactMode: screenSize === 'xs' || screenSize === 'sm',
    minHeight: screenSize === 'xs' ? 250 : screenSize === 'sm' ? 300 : 400,
    padding: screenSize === 'xs' ? 10 : screenSize === 'sm' ? 15 : 20
  };
  
  // Visualization-specific layout options
  switch (visualizationType) {
    case 'triDomainTensor':
      return {
        ...defaultLayout,
        rotation3D: screenSize === 'xs' ? false : true,
        showLabels: screenSize !== 'xs',
        showConnections: true,
        showDomainDetails: screenSize !== 'xs'
      };
    
    case 'harmonyIndex':
      return {
        ...defaultLayout,
        showHistory: screenSize !== 'xs',
        showDomainBreakdown: screenSize !== 'xs',
        compactControls: screenSize === 'xs' || screenSize === 'sm'
      };
    
    case 'riskControlFusion':
      return {
        ...defaultLayout,
        showHeatmap: true,
        showBarCharts: screenSize !== 'xs',
        showDomainTabs: true,
        compactControls: screenSize === 'xs' || screenSize === 'sm'
      };
    
    case 'resonanceSpectrogram':
      return {
        ...defaultLayout,
        showFrequencyScale: screenSize !== 'xs',
        showTimeScale: true,
        showPredictions: screenSize !== 'xs',
        showDomainSelectors: screenSize !== 'xs'
      };
    
    case 'unifiedComplianceSecurity':
      return {
        ...defaultLayout,
        showNodeLabels: screenSize !== 'xs',
        showLinkLabels: screenSize === 'lg' || screenSize === 'xl',
        showNodeDetails: screenSize !== 'xs',
        compactNodes: screenSize === 'xs' || screenSize === 'sm'
      };
    
    default:
      return defaultLayout;
  }
};

/**
 * Get touch-friendly interaction options for visualizations
 * @param {string} visualizationType - Type of visualization
 * @returns {Object} - Interaction options
 */
export const getTouchInteractions = (visualizationType) => {
  // Default touch interaction options
  const defaultInteractions = {
    touchZoom: true,
    touchPan: true,
    touchRotate: false,
    touchThreshold: 10, // Minimum distance in pixels for touch events
    doubleTapDelay: 300, // Delay in milliseconds for double tap
    longPressDelay: 500, // Delay in milliseconds for long press
    tooltipDelay: 500 // Delay in milliseconds for showing tooltips on touch
  };
  
  // Visualization-specific interaction options
  switch (visualizationType) {
    case 'triDomainTensor':
      return {
        ...defaultInteractions,
        touchRotate: true,
        rotationSensitivity: 0.5,
        zoomSensitivity: 0.2
      };
    
    case 'harmonyIndex':
      return {
        ...defaultInteractions,
        swipeToNavigateHistory: true,
        tapToSelectDomain: true
      };
    
    case 'riskControlFusion':
      return {
        ...defaultInteractions,
        swipeToChangeDomain: true,
        pinchToZoomHeatmap: true
      };
    
    case 'resonanceSpectrogram':
      return {
        ...defaultInteractions,
        pinchToZoomSpectrogram: true,
        doubleTapToResetZoom: true
      };
    
    case 'unifiedComplianceSecurity':
      return {
        ...defaultInteractions,
        tapToExpandNode: true,
        doubleTapToFocus: true,
        pinchToZoomGraph: true
      };
    
    default:
      return defaultInteractions;
  }
};

/**
 * Higher-order component (HOC) to add mobile optimization to visualizations
 * @param {React.Component} WrappedComponent - Component to wrap
 * @returns {React.Component} - Wrapped component with mobile optimization
 */
export const withMobileOptimization = (WrappedComponent) => {
  return function MobileOptimizedVisualization(props) {
    const { visualizationType, data, screenSize, interactionMode, ...rest } = props;
    
    // Optimize data for mobile
    const optimizedData = optimizeDataForMobile(visualizationType, data, screenSize);
    
    // Get mobile layout options
    const layoutOptions = getMobileLayout(visualizationType, screenSize);
    
    // Get touch interaction options if needed
    const interactionOptions = interactionMode === 'touch' 
      ? getTouchInteractions(visualizationType)
      : {};
    
    // Combine props
    const mobileProps = {
      ...rest,
      visualizationType,
      data: optimizedData,
      screenSize,
      interactionMode,
      layoutOptions,
      interactionOptions
    };
    
    return <WrappedComponent {...mobileProps} />;
  };
};
