/**
 * Onboarding Example Page
 * 
 * This page demonstrates the onboarding functionality.
 */

import React, { useState, useEffect } from 'react';
import { 
  OnboardingTour,
  OnboardingSpotlight,
  OnboardingProgressTracker,
  ThemeProvider,
  PreferencesProvider,
  OfflineProvider,
  I18nProvider,
  AccessibilityProvider,
  OnboardingProvider,
  DashboardCard,
  TabPanel,
  ResponsiveLayout,
  MetricsCard,
  ChartCard,
  StatusIndicator
} from '../nova-connect/ui/novavision-hub';

/**
 * Onboarding Example Page
 * @returns {React.ReactNode} - The rendered component
 */
export default function OnboardingExamplePage() {
  // State
  const [showProgressTracker, setShowProgressTracker] = useState(false);
  
  // Define tours
  const dashboardTour = {
    tourId: 'dashboard-tour',
    title: 'Dashboard Tour',
    description: 'Learn how to use the dashboard',
    steps: [
      {
        title: 'Welcome to NovaVision Hub',
        description: 'This tour will guide you through the main features of the NovaVision Hub dashboard.',
        position: 'bottom',
        targetSelector: '#welcome-card'
      },
      {
        title: 'Metrics Overview',
        description: 'Here you can see key metrics and performance indicators at a glance.',
        position: 'bottom',
        targetSelector: '#metrics-card'
      },
      {
        title: 'Status Indicators',
        description: 'These indicators show the current status of your systems and components.',
        position: 'right',
        targetSelector: '#status-card'
      },
      {
        title: 'Data Visualization',
        description: 'Interactive charts help you visualize trends and patterns in your data.',
        position: 'top',
        targetSelector: '#chart-card'
      },
      {
        title: 'Tour Complete',
        description: 'You\'ve completed the dashboard tour! You can now explore the dashboard on your own.',
        position: 'bottom',
        targetSelector: '#welcome-card'
      }
    ]
  };
  
  const featureTour = {
    tourId: 'feature-tour',
    title: 'Feature Tour',
    description: 'Explore advanced features',
    steps: [
      {
        title: 'Advanced Features',
        description: 'Let\'s explore some of the advanced features of NovaVision Hub.',
        position: 'bottom',
        targetSelector: '#welcome-card'
      },
      {
        title: 'Customization Options',
        description: 'You can customize the dashboard layout, colors, and components to suit your needs.',
        position: 'bottom',
        targetSelector: '#customize-button'
      },
      {
        title: 'Data Filtering',
        description: 'Use filters to focus on specific data points or time periods.',
        position: 'right',
        targetSelector: '#filter-button'
      },
      {
        title: 'Export Options',
        description: 'Export your data and visualizations in various formats for reporting.',
        position: 'left',
        targetSelector: '#export-button'
      },
      {
        title: 'Tour Complete',
        description: 'You\'ve completed the feature tour! You now know how to use the advanced features.',
        position: 'bottom',
        targetSelector: '#welcome-card'
      }
    ]
  };
  
  // Toggle progress tracker
  const toggleProgressTracker = () => {
    setShowProgressTracker(!showProgressTracker);
  };
  
  return (
    <ThemeProvider>
      <PreferencesProvider>
        <OfflineProvider>
          <I18nProvider>
            <AccessibilityProvider>
              <OnboardingProvider>
                <div className="p-8">
                  <header className="mb-8">
                    <h1 className="text-3xl font-bold mb-4">NovaVision Hub Onboarding</h1>
                    <p className="text-lg text-gray-600 mb-4">
                      This example demonstrates the onboarding functionality of NovaVision Hub.
                    </p>
                    
                    <div className="flex space-x-4">
                      <button
                        id="customize-button"
                        className="px-4 py-2 bg-blue-500 text-white rounded-md"
                        onClick={toggleProgressTracker}
                      >
                        {showProgressTracker ? 'Hide Progress Tracker' : 'Show Progress Tracker'}
                      </button>
                      
                      <button
                        id="filter-button"
                        className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md"
                      >
                        Filter Data
                      </button>
                      
                      <button
                        id="export-button"
                        className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md"
                      >
                        Export Data
                      </button>
                    </div>
                  </header>
                  
                  {/* Progress Tracker */}
                  {showProgressTracker && (
                    <div className="mb-8">
                      <OnboardingProgressTracker
                        tours={['dashboard-tour', 'feature-tour']}
                        showCompleted={true}
                        showRecommended={true}
                        showReset={true}
                      />
                    </div>
                  )}
                  
                  {/* Dashboard Content */}
                  <div className="mb-8">
                    <ResponsiveLayout
                      layouts={{
                        xs: 1,
                        sm: 2,
                        md: 2,
                        lg: 4,
                        xl: 4
                      }}
                      gap={4}
                    >
                      {/* Welcome Card */}
                      <DashboardCard
                        id="welcome-card"
                        title="Welcome to NovaVision Hub"
                        className="col-span-2"
                      >
                        <div className="p-4">
                          <p className="mb-4">
                            Welcome to the NovaVision Hub dashboard. This dashboard provides a comprehensive view of your NovaFuse components.
                          </p>
                          <p>
                            Use the onboarding tours to learn how to use the dashboard and its features.
                          </p>
                        </div>
                      </DashboardCard>
                      
                      {/* Metrics Card */}
                      <MetricsCard
                        id="metrics-card"
                        title="Key Metrics"
                        metrics={[
                          {
                            label: 'Active Users',
                            value: 1254,
                            change: 5.2,
                            changeType: 'increase'
                          },
                          {
                            label: 'Compliance Score',
                            value: 92,
                            suffix: '%',
                            change: 3.1,
                            changeType: 'increase'
                          }
                        ]}
                      />
                      
                      {/* Status Card */}
                      <DashboardCard
                        id="status-card"
                        title="System Status"
                      >
                        <div className="p-4 space-y-4">
                          <div className="flex justify-between items-center">
                            <span>NovaConnect</span>
                            <StatusIndicator status="success" label="Operational" />
                          </div>
                          <div className="flex justify-between items-center">
                            <span>NovaShield</span>
                            <StatusIndicator status="success" label="Operational" />
                          </div>
                          <div className="flex justify-between items-center">
                            <span>NovaTrack</span>
                            <StatusIndicator status="warning" label="Degraded" />
                          </div>
                          <div className="flex justify-between items-center">
                            <span>NovaDNA</span>
                            <StatusIndicator status="success" label="Operational" />
                          </div>
                        </div>
                      </DashboardCard>
                    </ResponsiveLayout>
                  </div>
                  
                  {/* Chart Card */}
                  <ChartCard
                    id="chart-card"
                    title="Performance Trends"
                    className="mb-8"
                  >
                    <div className="h-64 flex items-center justify-center">
                      <p className="text-gray-500">
                        [Chart visualization would be displayed here]
                      </p>
                    </div>
                  </ChartCard>
                  
                  {/* Onboarding Tours */}
                  <OnboardingTour
                    tourId={dashboardTour.tourId}
                    title={dashboardTour.title}
                    description={dashboardTour.description}
                    steps={dashboardTour.steps}
                    autoStart={false}
                  />
                  
                  <OnboardingTour
                    tourId={featureTour.tourId}
                    title={featureTour.title}
                    description={featureTour.description}
                    steps={featureTour.steps}
                    autoStart={false}
                  />
                </div>
              </OnboardingProvider>
            </AccessibilityProvider>
          </I18nProvider>
        </OfflineProvider>
      </PreferencesProvider>
    </ThemeProvider>
  );
}
