/**
 * ProductSwitcher Component
 * 
 * A reusable component for switching between different product tiers.
 * This component allows users to easily switch between NovaPrime, NovaCore,
 * NovaShield, NovaLearn, and NovaAssistAI to see how the UI changes based on
 * the feature flags for each product.
 */

import React from 'react';
import { useContext } from 'react';
import { ProductContext, PRODUCTS } from '../../packages/feature-flags/ProductContext';

/**
 * ProductSwitcher component
 * @param {object} props - Component props
 * @param {string} [props.className] - Additional CSS classes
 * @returns {React.ReactNode} - The rendered component
 */
function ProductSwitcher({ className = '' }) {
  const { product, setProduct, isProductActive } = useContext(ProductContext);
  
  // Format product name for display (e.g., novaPrime -> Nova Prime)
  const formatProductName = (productKey) => {
    return productKey
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase());
  };
  
  return (
    <div className={`product-switcher bg-white p-4 rounded shadow mb-6 ${className}`}>
      <h3 className="text-lg font-semibold mb-2">Product Tier</h3>
      <p className="text-sm text-gray-600 mb-3">
        Switch between product tiers to see how features are enabled/disabled.
      </p>
      <div className="flex flex-wrap gap-2">
        {Object.values(PRODUCTS).map((productOption) => (
          <button
            key={productOption}
            onClick={() => setProduct(productOption)}
            className={`px-3 py-1 rounded transition-colors ${
              isProductActive(productOption)
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
            }`}
          >
            {formatProductName(productOption)}
          </button>
        ))}
      </div>
    </div>
  );
}

export default ProductSwitcher;
