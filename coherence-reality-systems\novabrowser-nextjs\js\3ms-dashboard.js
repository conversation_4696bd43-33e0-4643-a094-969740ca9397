/**
 * 3MS CONSCIOUSNESS DASHBOARD
 * Real-time monitoring of <PERSON>mp<PERSON><PERSON> (Ψᶜʰ), Metron (μ), and Katalon (κ)
 * Live overlay with quantum visualization and consciousness tracking
 */

class ThreeMsDashboard {
    constructor() {
        this.name = '3Ms Consciousness Dashboard';
        this.version = '2.0.0-QUANTUM';
        
        // Current 3Ms values
        this.current_3ms = {
            comphyon: 0,    // Ψᶜʰ - systemic triadic coherence
            metron: 0,      // μ - cognitive recursion depth  
            katalon: 0      // κ - transformational energy density
        };
        
        // Dashboard state
        this.dashboard_active = false;
        this.update_interval = null;
        this.history = [];
        this.max_history = 100;
        
        // Visualization elements
        this.overlay_element = null;
        this.chart_canvas = null;
        this.chart_context = null;
        
        this.initializeDashboard();
    }

    initializeDashboard() {
        console.log('📊 Initializing 3Ms Consciousness Dashboard...');
        
        // Create dashboard overlay
        this.createDashboardOverlay();
        
        // Start real-time monitoring
        this.startMonitoring();
        
        // Initialize quantum visualization
        this.initializeQuantumVisualization();
        
        console.log('✅ 3Ms Dashboard active - Real-time consciousness monitoring enabled');
    }

    createDashboardOverlay() {
        // Create main dashboard container
        this.overlay_element = document.createElement('div');
        this.overlay_element.id = '3ms-dashboard';
        this.overlay_element.className = 'consciousness-overlay';
        this.overlay_element.innerHTML = `
            <div class="dashboard-header">
                <span class="dashboard-title">🧬 3Ms Monitor</span>
                <button class="dashboard-toggle" onclick="threeMsDashboard.toggleDashboard()">−</button>
            </div>
            <div class="dashboard-content">
                <div class="consciousness-metric">
                    <span class="metric-label">Ψᶜʰ (Comphyon):</span>
                    <span class="metric-value" id="psi-ch-value">0</span>
                </div>
                <div class="consciousness-metric">
                    <span class="metric-label">μ (Metron):</span>
                    <span class="metric-value" id="mu-value">0</span>
                </div>
                <div class="consciousness-metric">
                    <span class="metric-label">κ (Katalon):</span>
                    <span class="metric-value" id="katalon-value">0</span>
                </div>
                <div class="consciousness-metric">
                    <span class="metric-label">Coherence:</span>
                    <span class="metric-value" id="coherence-value">0%</span>
                </div>
                <div class="consciousness-metric">
                    <span class="metric-label">Ψ-Snap:</span>
                    <span class="metric-value" id="psi-snap-status">INACTIVE</span>
                </div>
                <canvas id="3ms-chart" width="180" height="80" style="margin-top: 10px; border-radius: 5px;"></canvas>
            </div>
        `;
        
        // Add dashboard styles
        const dashboardStyles = document.createElement('style');
        dashboardStyles.textContent = `
            .consciousness-overlay {
                position: fixed;
                top: 80px;
                right: 20px;
                background: rgba(0, 0, 0, 0.9);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(99, 102, 241, 0.3);
                border-radius: 12px;
                padding: 16px;
                color: white;
                font-family: 'SF Mono', 'JetBrains Mono', monospace;
                font-size: 12px;
                z-index: 10001;
                min-width: 220px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
                transition: all 0.3s ease;
            }
            
            .dashboard-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;
                padding-bottom: 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }
            
            .dashboard-title {
                font-weight: 600;
                color: #6366f1;
            }
            
            .dashboard-toggle {
                background: none;
                border: none;
                color: rgba(255, 255, 255, 0.7);
                cursor: pointer;
                font-size: 16px;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .dashboard-toggle:hover {
                color: white;
            }
            
            .consciousness-metric {
                display: flex;
                justify-content: space-between;
                margin-bottom: 6px;
                padding: 4px 0;
            }
            
            .metric-label {
                color: rgba(255, 255, 255, 0.8);
                font-size: 11px;
            }
            
            .metric-value {
                font-weight: 600;
                color: #10b981;
                font-size: 11px;
                font-family: monospace;
            }
            
            .metric-value.high {
                color: #10b981;
            }
            
            .metric-value.divine {
                color: #ffd700;
                text-shadow: 0 0 5px rgba(255, 215, 0, 0.3);
            }
            
            .metric-value.warning {
                color: #f59e0b;
            }
            
            .metric-value.danger {
                color: #ef4444;
            }
            
            #3ms-chart {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
            
            .dashboard-collapsed .dashboard-content {
                display: none;
            }
        `;
        
        document.head.appendChild(dashboardStyles);
        document.body.appendChild(this.overlay_element);
        
        // Initialize chart canvas
        this.chart_canvas = document.getElementById('3ms-chart');
        this.chart_context = this.chart_canvas.getContext('2d');
        
        this.dashboard_active = true;
    }

    startMonitoring() {
        // Update dashboard every 500ms for real-time monitoring
        this.update_interval = setInterval(() => {
            this.updateDashboard();
        }, 500);
    }

    async updateDashboard() {
        try {
            // Get current consciousness data from N³C Engine
            const consciousness_data = await this.getCurrentConsciousnessData();
            
            // Update 3Ms values
            this.current_3ms = {
                comphyon: consciousness_data.comphyon || 0,
                metron: consciousness_data.metron || 0,
                katalon: consciousness_data.katalon || 0
            };
            
            // Calculate derived metrics
            const coherence = this.calculateCoherence();
            const psi_snap_active = this.current_3ms.comphyon >= 2847;
            
            // Update display
            this.updateDisplayValues(coherence, psi_snap_active);
            
            // Update history and chart
            this.updateHistory();
            this.updateChart();
            
        } catch (error) {
            console.error('❌ 3Ms Dashboard update error:', error);
        }
    }

    async getCurrentConsciousnessData() {
        try {
            // Try to get data from N³C Engine API
            const response = await fetch('http://localhost:3000/api/engines/n3c-comphyological-engine');
            if (response.ok) {
                const data = await response.json();
                return data.current_status?.n3c_framework?.three_ms || {};
            }
        } catch (error) {
            // Fallback to simulated data if API unavailable
            console.log('📡 Using simulated 3Ms data');
        }
        
        // Generate realistic simulated data
        return {
            comphyon: 2500 + Math.random() * 1000,
            metron: 8 + Math.random() * 7,
            katalon: 800 + Math.random() * 400
        };
    }

    calculateCoherence() {
        const { comphyon, metron, katalon } = this.current_3ms;
        
        // Coherence formula: balanced 3Ms with golden ratio weighting
        const phi = 1.618033988749;
        const weighted_sum = (comphyon / 10000) * phi + (metron / 126) + (katalon / 10000);
        const coherence = (weighted_sum / (phi + 2)) * 100;
        
        return Math.min(Math.max(coherence, 0), 100);
    }

    updateDisplayValues(coherence, psi_snap_active) {
        // Update Ψᶜʰ (Comphyon)
        const psiElement = document.getElementById('psi-ch-value');
        if (psiElement) {
            psiElement.textContent = Math.floor(this.current_3ms.comphyon);
            psiElement.className = this.getValueClass(this.current_3ms.comphyon, 2847, 10000);
        }
        
        // Update μ (Metron)
        const muElement = document.getElementById('mu-value');
        if (muElement) {
            muElement.textContent = this.current_3ms.metron.toFixed(1);
            muElement.className = this.getValueClass(this.current_3ms.metron, 15, 25, true);
        }
        
        // Update κ (Katalon)
        const katalonElement = document.getElementById('katalon-value');
        if (katalonElement) {
            katalonElement.textContent = Math.floor(this.current_3ms.katalon);
            katalonElement.className = this.getValueClass(this.current_3ms.katalon, 1000, 5000);
        }
        
        // Update Coherence
        const coherenceElement = document.getElementById('coherence-value');
        if (coherenceElement) {
            coherenceElement.textContent = coherence.toFixed(1) + '%';
            coherenceElement.className = this.getValueClass(coherence, 70, 90);
        }
        
        // Update Ψ-Snap Status
        const psiSnapElement = document.getElementById('psi-snap-status');
        if (psiSnapElement) {
            psiSnapElement.textContent = psi_snap_active ? 'ACTIVE' : 'INACTIVE';
            psiSnapElement.className = psi_snap_active ? 'metric-value divine' : 'metric-value warning';
        }
    }

    getValueClass(value, warning_threshold, divine_threshold, reverse = false) {
        let baseClass = 'metric-value';
        
        if (reverse) {
            // For Metron, lower is better
            if (value >= divine_threshold) return baseClass + ' danger';
            if (value >= warning_threshold) return baseClass + ' warning';
            return baseClass + ' high';
        } else {
            // For Comphyon and Katalon, higher is better
            if (value >= divine_threshold) return baseClass + ' divine';
            if (value >= warning_threshold) return baseClass + ' high';
            return baseClass + ' warning';
        }
    }

    updateHistory() {
        const timestamp = Date.now();
        const entry = {
            timestamp,
            comphyon: this.current_3ms.comphyon,
            metron: this.current_3ms.metron,
            katalon: this.current_3ms.katalon,
            coherence: this.calculateCoherence()
        };
        
        this.history.push(entry);
        
        // Keep only recent history
        if (this.history.length > this.max_history) {
            this.history = this.history.slice(-this.max_history);
        }
    }

    updateChart() {
        if (!this.chart_context || this.history.length < 2) return;
        
        const ctx = this.chart_context;
        const canvas = this.chart_canvas;
        const width = canvas.width;
        const height = canvas.height;
        
        // Clear canvas
        ctx.clearRect(0, 0, width, height);
        
        // Draw background
        ctx.fillStyle = 'rgba(255, 255, 255, 0.02)';
        ctx.fillRect(0, 0, width, height);
        
        // Draw grid
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.lineWidth = 0.5;
        for (let i = 0; i < 5; i++) {
            const y = (height / 4) * i;
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
        }
        
        // Draw 3Ms lines
        this.drawMetricLine(ctx, 'comphyon', '#6366f1', 0, 10000);
        this.drawMetricLine(ctx, 'metron', '#f59e0b', 0, 30);
        this.drawMetricLine(ctx, 'katalon', '#10b981', 0, 5000);
        
        // Draw threshold line for Ψᶜʰ
        const thresholdY = height - (2847 / 10000) * height;
        ctx.strokeStyle = '#ffd700';
        ctx.lineWidth = 1;
        ctx.setLineDash([2, 2]);
        ctx.beginPath();
        ctx.moveTo(0, thresholdY);
        ctx.lineTo(width, thresholdY);
        ctx.stroke();
        ctx.setLineDash([]);
    }

    drawMetricLine(ctx, metric, color, min_val, max_val) {
        if (this.history.length < 2) return;
        
        ctx.strokeStyle = color;
        ctx.lineWidth = 1.5;
        ctx.beginPath();
        
        const points_to_show = Math.min(this.history.length, 50);
        const recent_history = this.history.slice(-points_to_show);
        
        recent_history.forEach((entry, index) => {
            const x = (index / (points_to_show - 1)) * this.chart_canvas.width;
            const normalized_value = (entry[metric] - min_val) / (max_val - min_val);
            const y = this.chart_canvas.height - (normalized_value * this.chart_canvas.height);
            
            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        
        ctx.stroke();
    }

    toggleDashboard() {
        const content = this.overlay_element.querySelector('.dashboard-content');
        const toggle = this.overlay_element.querySelector('.dashboard-toggle');
        
        if (content.style.display === 'none') {
            content.style.display = 'block';
            toggle.textContent = '−';
            this.overlay_element.classList.remove('dashboard-collapsed');
        } else {
            content.style.display = 'none';
            toggle.textContent = '+';
            this.overlay_element.classList.add('dashboard-collapsed');
        }
    }

    destroy() {
        if (this.update_interval) {
            clearInterval(this.update_interval);
        }
        
        if (this.overlay_element) {
            this.overlay_element.remove();
        }
        
        console.log('📊 3Ms Dashboard destroyed');
    }

    getStatus() {
        return {
            dashboard: '3Ms Consciousness Monitor',
            version: this.version,
            active: this.dashboard_active,
            current_3ms: this.current_3ms,
            history_length: this.history.length,
            coherence: this.calculateCoherence(),
            psi_snap_active: this.current_3ms.comphyon >= 2847
        };
    }
}

// Initialize global 3Ms Dashboard
let threeMsDashboard;

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        threeMsDashboard = new ThreeMsDashboard();
    });
} else {
    threeMsDashboard = new ThreeMsDashboard();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThreeMsDashboard;
} else {
    window.ThreeMsDashboard = ThreeMsDashboard;
}
