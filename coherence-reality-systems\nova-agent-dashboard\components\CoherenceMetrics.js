import { motion } from 'framer-motion';
import { ChartBarIcon } from '@heroicons/react/24/outline';

export default function CoherenceMetrics({ coherenceData }) {
  if (!coherenceData) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6"
      >
        <div className="animate-pulse">
          <div className="h-6 bg-gray-700 rounded mb-4"></div>
          <div className="grid grid-cols-3 gap-4">
            <div className="h-24 bg-gray-700 rounded"></div>
            <div className="h-24 bg-gray-700 rounded"></div>
            <div className="h-24 bg-gray-700 rounded"></div>
          </div>
        </div>
      </motion.div>
    );
  }

  const metrics = coherenceData.metrics || {
    structural_coherence: 0,
    functional_alignment: 0,
    relational_integrity: 0
  };

  const getMetricColor = (value) => {
    if (value >= 0.9) return 'text-green-400';
    if (value >= 0.8) return 'text-yellow-400';
    if (value >= 0.7) return 'text-orange-400';
    return 'text-red-400';
  };

  const getMetricBgColor = (value) => {
    if (value >= 0.9) return 'bg-green-500/20 border-green-500/30';
    if (value >= 0.8) return 'bg-yellow-500/20 border-yellow-500/30';
    if (value >= 0.7) return 'bg-orange-500/20 border-orange-500/30';
    return 'bg-red-500/20 border-red-500/30';
  };

  const metricsArray = [
    {
      key: 'structural_coherence',
      name: 'Structural Coherence',
      description: 'System architecture alignment',
      value: metrics.structural_coherence,
      icon: '🏗️'
    },
    {
      key: 'functional_alignment',
      name: 'Functional Alignment',
      description: 'Component synchronization',
      value: metrics.functional_alignment,
      icon: '⚙️'
    },
    {
      key: 'relational_integrity',
      name: 'Relational Integrity',
      description: 'Inter-system coherence',
      value: metrics.relational_integrity,
      icon: '🔗'
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6"
    >
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <ChartBarIcon className="w-6 h-6 text-coherence-400" />
        <div>
          <h3 className="text-lg font-semibold text-white">Coherence Metrics</h3>
          <p className="text-sm text-gray-400">3 Critical Aspects</p>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {metricsArray.map((metric, index) => (
          <motion.div
            key={metric.key}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`p-4 rounded-lg border ${getMetricBgColor(metric.value)}`}
          >
            {/* Metric Header */}
            <div className="flex items-center justify-between mb-3">
              <span className="text-2xl">{metric.icon}</span>
              <div className={`text-2xl font-bold ${getMetricColor(metric.value)}`}>
                {(metric.value * 100).toFixed(1)}%
              </div>
            </div>

            {/* Metric Info */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-white">{metric.name}</h4>
              <p className="text-xs text-gray-400">{metric.description}</p>
            </div>

            {/* Progress Bar */}
            <div className="mt-3">
              <div className="w-full bg-gray-700 rounded-full h-2">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${metric.value * 100}%` }}
                  transition={{ duration: 1, delay: index * 0.2 }}
                  className={`h-2 rounded-full ${
                    metric.value >= 0.9 ? 'bg-green-400' :
                    metric.value >= 0.8 ? 'bg-yellow-400' :
                    metric.value >= 0.7 ? 'bg-orange-400' : 'bg-red-400'
                  }`}
                />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Overall Coherence Score */}
      <div className="mt-6 pt-6 border-t border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-white">Overall Coherence</h4>
            <p className="text-xs text-gray-400">Consciousness ≡ Coherence ≡ Optimization</p>
          </div>
          <div className="text-right">
            <div className={`text-xl font-bold ${getMetricColor(
              (metrics.structural_coherence + metrics.functional_alignment + metrics.relational_integrity) / 3
            )}`}>
              {(((metrics.structural_coherence + metrics.functional_alignment + metrics.relational_integrity) / 3) * 100).toFixed(1)}%
            </div>
            <div className="text-xs text-gray-400">Avg Score</div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
