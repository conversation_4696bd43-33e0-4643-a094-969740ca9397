const axios = require('axios');
const fs = require('fs');
const path = require('path');

async function testConnection() {
  try {
    // Read credentials from .env
    const envPath = path.join(__dirname, '../.env');
    if (!fs.existsSync(envPath)) {
      throw new Error('No .env file found! Run setup-clickbank.js first.');
    }

    // Load environment variables
    require('dotenv').config({ path: envPath });

    const username = process.env.CLICKBANK_USERNAME;
    const password = process.env.CLICKBANK_PASSWORD;

    if (!username || !password) {
      throw new Error('Missing ClickBank credentials in .env file');
    }

    console.log('🔍 Testing ClickBank connection...');

    // Test API endpoint
    const testUrl = 'https://api.clickbank.com/rest/1.3/products/test';

    // Make test request
    const response = await axios.get(testUrl, {
      auth: {
        username,
        password
      },
      headers: {
        'Accept': 'application/json'
      }
    });

    // Check response
    if (response.status === 200) {
      console.log('✅ Connection successful!');
      console.log('✅ Credentials valid!');
      console.log('✅ API access confirmed!');
      
      // Show basic account info
      console.log('\nAccount Information:');
      console.log(`- Username: ${username}`);
      console.log(`- API Version: ${response.data.version}`);
      console.log(`- Account Status: ${response.data.status}`);
      
    } else {
      throw new Error(`API error: ${response.statusText}`);
    }

  } catch (error) {
    console.error('❌ Connection test failed:');
    console.error(error.message);
    
    // Show detailed error information
    if (error.response) {
      console.error('\nAPI Error Details:');
      console.error(`Status: ${error.response.status}`);
      console.error(`Message: ${error.response.data.message}`);
    }
  }
}

// Run test
testConnection();
