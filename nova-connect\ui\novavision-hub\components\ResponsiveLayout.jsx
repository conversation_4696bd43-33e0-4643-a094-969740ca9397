/**
 * ResponsiveLayout Component
 * 
 * A responsive layout component that adapts to different screen sizes.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

// Screen size breakpoints (in pixels)
const BREAKPOINTS = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
};

/**
 * ResponsiveLayout component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Layout content
 * @param {Object} [props.layouts] - Layout configurations for different screen sizes
 * @param {number} [props.layouts.xs] - Number of columns for extra small screens
 * @param {number} [props.layouts.sm] - Number of columns for small screens
 * @param {number} [props.layouts.md] - Number of columns for medium screens
 * @param {number} [props.layouts.lg] - Number of columns for large screens
 * @param {number} [props.layouts.xl] - Number of columns for extra large screens
 * @param {number} [props.layouts['2xl']] - Number of columns for 2x extra large screens
 * @param {number} [props.gap=4] - Gap between grid items (in multiples of 0.25rem)
 * @param {boolean} [props.autoHeight=true] - Whether to automatically adjust item heights
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} ResponsiveLayout component
 */
const ResponsiveLayout = ({
  children,
  layouts = {
    xs: 1,
    sm: 1,
    md: 2,
    lg: 3,
    xl: 4,
    '2xl': 4
  },
  gap = 4,
  autoHeight = true,
  className = '',
  style = {}
}) => {
  const [currentBreakpoint, setCurrentBreakpoint] = useState('xs');
  
  // Update current breakpoint when window size changes
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      
      // Determine current breakpoint
      if (width >= BREAKPOINTS['2xl']) {
        setCurrentBreakpoint('2xl');
      } else if (width >= BREAKPOINTS.xl) {
        setCurrentBreakpoint('xl');
      } else if (width >= BREAKPOINTS.lg) {
        setCurrentBreakpoint('lg');
      } else if (width >= BREAKPOINTS.md) {
        setCurrentBreakpoint('md');
      } else if (width >= BREAKPOINTS.sm) {
        setCurrentBreakpoint('sm');
      } else {
        setCurrentBreakpoint('xs');
      }
    };
    
    // Initial check
    handleResize();
    
    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  // Get current number of columns
  const columns = layouts[currentBreakpoint] || 1;
  
  // Generate grid template columns CSS
  const gridTemplateColumns = `repeat(${columns}, minmax(0, 1fr))`;
  
  // Generate gap CSS
  const gapSize = `${gap * 0.25}rem`;
  
  return (
    <div
      className={`grid ${className}`}
      style={{
        gridTemplateColumns,
        gap: gapSize,
        ...style
      }}
      data-testid="responsive-layout"
      data-breakpoint={currentBreakpoint}
    >
      {React.Children.map(children, (child, index) => (
        <div
          key={index}
          className={`${autoHeight ? 'h-auto' : 'h-full'}`}
          data-testid={`grid-item-${index}`}
        >
          {child}
        </div>
      ))}
    </div>
  );
};

ResponsiveLayout.propTypes = {
  children: PropTypes.node.isRequired,
  layouts: PropTypes.shape({
    xs: PropTypes.number,
    sm: PropTypes.number,
    md: PropTypes.number,
    lg: PropTypes.number,
    xl: PropTypes.number,
    '2xl': PropTypes.number
  }),
  gap: PropTypes.number,
  autoHeight: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object
};

export default ResponsiveLayout;
