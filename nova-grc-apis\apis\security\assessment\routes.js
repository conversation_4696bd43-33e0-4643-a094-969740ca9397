const express = require('express');
const { validateRequest } = require('./validation');
const controllers = require('./controllers');

const router = express.Router();

/**
 * @swagger
 * /security/assessment/assessments:
 *   get:
 *     summary: Get a list of security assessments
 *     description: Returns a paginated list of security assessments with optional filtering
 *     tags: [Security Assessment]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: type
 *         in: query
 *         description: Filter by assessment type
 *         schema:
 *           type: string
 *           enum: [internal, external, vendor, compliance]
 *       - name: status
 *         in: query
 *         description: Filter by assessment status
 *         schema:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *       - name: startDate
 *         in: query
 *         description: Filter by start date (format YYYY-MM-DD)
 *         schema:
 *           type: string
 *       - name: endDate
 *         in: query
 *         description: Filter by end date (format YYYY-MM-DD)
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/SecurityAssessment'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/assessments', controllers.getAssessments);

/**
 * @swagger
 * /security/assessment/assessments/{id}:
 *   get:
 *     summary: Get a specific security assessment
 *     description: Returns a specific security assessment by ID
 *     tags: [Security Assessment]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Security assessment ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/SecurityAssessment'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/assessments/:id', controllers.getAssessmentById);

/**
 * @swagger
 * /security/assessment/assessments:
 *   post:
 *     summary: Create a new security assessment
 *     description: Creates a new security assessment
 *     tags: [Security Assessment]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SecurityAssessmentInput'
 *     responses:
 *       201:
 *         description: Assessment created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/SecurityAssessment'
 *                 message:
 *                   type: string
 *                   example: Security assessment created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/assessments', validateRequest('createAssessment'), controllers.createAssessment);

/**
 * @swagger
 * /security/assessment/assessments/{id}:
 *   put:
 *     summary: Update a security assessment
 *     description: Updates an existing security assessment
 *     tags: [Security Assessment]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Security assessment ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SecurityAssessmentInput'
 *     responses:
 *       200:
 *         description: Assessment updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/SecurityAssessment'
 *                 message:
 *                   type: string
 *                   example: Security assessment updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/assessments/:id', validateRequest('updateAssessment'), controllers.updateAssessment);

/**
 * @swagger
 * /security/assessment/assessments/{id}:
 *   delete:
 *     summary: Delete a security assessment
 *     description: Deletes an existing security assessment
 *     tags: [Security Assessment]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Security assessment ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Assessment deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Security assessment deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/assessments/:id', controllers.deleteAssessment);

/**
 * @swagger
 * /security/assessment/assessments/{id}/findings:
 *   get:
 *     summary: Get findings for a security assessment
 *     description: Returns all findings associated with a specific security assessment
 *     tags: [Security Assessment]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Security assessment ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/SecurityFinding'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/assessments/:id/findings', controllers.getAssessmentFindings);

/**
 * @swagger
 * /security/assessment/assessments/{id}/findings:
 *   post:
 *     summary: Add a finding to a security assessment
 *     description: Adds a new security finding to an existing assessment
 *     tags: [Security Assessment]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Security assessment ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SecurityFindingInput'
 *     responses:
 *       201:
 *         description: Finding added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/SecurityFinding'
 *                 message:
 *                   type: string
 *                   example: Finding added to assessment successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/assessments/:id/findings', validateRequest('createFinding'), controllers.addFindingToAssessment);

/**
 * @swagger
 * /security/assessment/findings:
 *   get:
 *     summary: Get a list of security findings
 *     description: Returns a paginated list of security findings with optional filtering
 *     tags: [Security Assessment]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: severity
 *         in: query
 *         description: Filter by finding severity
 *         schema:
 *           type: string
 *           enum: [critical, high, medium, low, info]
 *       - name: status
 *         in: query
 *         description: Filter by finding status
 *         schema:
 *           type: string
 *           enum: [open, in-remediation, remediated, accepted, false-positive]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/SecurityFinding'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/findings', controllers.getFindings);

/**
 * @swagger
 * /security/assessment/findings/{id}:
 *   get:
 *     summary: Get a specific security finding
 *     description: Returns a specific security finding by ID
 *     tags: [Security Assessment]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Security finding ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/SecurityFinding'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/findings/:id', controllers.getFindingById);

/**
 * @swagger
 * /security/assessment/findings/{id}:
 *   put:
 *     summary: Update a security finding
 *     description: Updates an existing security finding
 *     tags: [Security Assessment]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Security finding ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SecurityFindingInput'
 *     responses:
 *       200:
 *         description: Finding updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/SecurityFinding'
 *                 message:
 *                   type: string
 *                   example: Security finding updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/findings/:id', validateRequest('updateFinding'), controllers.updateFinding);

/**
 * @swagger
 * /security/assessment/types:
 *   get:
 *     summary: Get assessment types
 *     description: Returns a list of security assessment types
 *     tags: [Security Assessment]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/types', controllers.getAssessmentTypes);

module.exports = router;
