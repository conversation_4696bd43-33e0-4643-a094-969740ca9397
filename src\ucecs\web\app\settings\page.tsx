'use client';

import { useState } from 'react';
import Link from 'next/link';
import MainLayout from '@/components/MainLayout';
import { FiSave, FiUser, FiServer, FiShield, FiMail, FiDatabase, FiFlag, FiExternalLink } from 'react-icons/fi';
import { useFeatureFlags } from '@/contexts/FeatureFlagContext';
import { FeatureFlag } from '@/utils/features/featureFlags';

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('general');
  const { isEnabled } = useFeatureFlags();

  return (
    <MainLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
        <p className="text-gray-500 dark:text-gray-400">Configure your NovaSphere system</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <div className="md:col-span-1">
          <div className="card">
            <nav className="space-y-1">
              <button
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md w-full ${
                  activeTab === 'general'
                    ? 'bg-primary text-white'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                onClick={() => setActiveTab('general')}
              >
                <FiServer className="mr-3 h-5 w-5" />
                General
              </button>
              <button
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md w-full ${
                  activeTab === 'user'
                    ? 'bg-primary text-white'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                onClick={() => setActiveTab('user')}
              >
                <FiUser className="mr-3 h-5 w-5" />
                User Profile
              </button>
              <button
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md w-full ${
                  activeTab === 'security'
                    ? 'bg-primary text-white'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                onClick={() => setActiveTab('security')}
              >
                <FiShield className="mr-3 h-5 w-5" />
                Security
              </button>
              <button
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md w-full ${
                  activeTab === 'notifications'
                    ? 'bg-primary text-white'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                onClick={() => setActiveTab('notifications')}
              >
                <FiMail className="mr-3 h-5 w-5" />
                Notifications
              </button>
              <button
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md w-full ${
                  activeTab === 'storage'
                    ? 'bg-primary text-white'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                onClick={() => setActiveTab('storage')}
              >
                <FiDatabase className="mr-3 h-5 w-5" />
                Storage
              </button>
              <button
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md w-full ${
                  activeTab === 'features'
                    ? 'bg-primary text-white'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                onClick={() => setActiveTab('features')}
              >
                <FiFlag className="mr-3 h-5 w-5" />
                Features
              </button>
            </nav>
          </div>
        </div>

        {/* Settings Content */}
        <div className="md:col-span-3">
          <div className="card">
            {/* General Settings */}
            {activeTab === 'general' && (
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">General Settings</h2>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="systemName" className="label">System Name</label>
                    <input
                      type="text"
                      id="systemName"
                      name="systemName"
                      className="input"
                      defaultValue="NovaFuse NovaSphere"
                    />
                  </div>
                  <div>
                    <label htmlFor="apiUrl" className="label">API URL</label>
                    <input
                      type="text"
                      id="apiUrl"
                      name="apiUrl"
                      className="input"
                      defaultValue="http://localhost:5000/api/v1"
                    />
                  </div>
                  <div>
                    <label htmlFor="timezone" className="label">Timezone</label>
                    <select
                      id="timezone"
                      name="timezone"
                      className="input"
                      defaultValue="UTC"
                    >
                      <option value="UTC">UTC</option>
                      <option value="America/New_York">Eastern Time (ET)</option>
                      <option value="America/Chicago">Central Time (CT)</option>
                      <option value="America/Denver">Mountain Time (MT)</option>
                      <option value="America/Los_Angeles">Pacific Time (PT)</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="dateFormat" className="label">Date Format</label>
                    <select
                      id="dateFormat"
                      name="dateFormat"
                      className="input"
                      defaultValue="YYYY-MM-DD"
                    >
                      <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                      <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                      <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                    </select>
                  </div>
                  <div className="flex justify-end">
                    <button className="btn btn-primary flex items-center">
                      <FiSave className="mr-2" />
                      Save Changes
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* User Profile Settings */}
            {activeTab === 'user' && (
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">User Profile</h2>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  Update your user profile information
                </p>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="firstName" className="label">First Name</label>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        className="input"
                        defaultValue="John"
                      />
                    </div>
                    <div>
                      <label htmlFor="lastName" className="label">Last Name</label>
                      <input
                        type="text"
                        id="lastName"
                        name="lastName"
                        className="input"
                        defaultValue="Doe"
                      />
                    </div>
                  </div>
                  <div>
                    <label htmlFor="email" className="label">Email</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      className="input"
                      defaultValue="<EMAIL>"
                    />
                  </div>
                  <div className="flex justify-end">
                    <button className="btn btn-primary flex items-center">
                      <FiSave className="mr-2" />
                      Save Changes
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Security Settings */}
            {activeTab === 'security' && (
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Security Settings</h2>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  Manage your security settings and change your password
                </p>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="currentPassword" className="label">Current Password</label>
                    <input
                      type="password"
                      id="currentPassword"
                      name="currentPassword"
                      className="input"
                    />
                  </div>
                  <div>
                    <label htmlFor="newPassword" className="label">New Password</label>
                    <input
                      type="password"
                      id="newPassword"
                      name="newPassword"
                      className="input"
                    />
                  </div>
                  <div>
                    <label htmlFor="confirmPassword" className="label">Confirm New Password</label>
                    <input
                      type="password"
                      id="confirmPassword"
                      name="confirmPassword"
                      className="input"
                    />
                  </div>
                  <div className="flex justify-end">
                    <button className="btn btn-primary flex items-center">
                      <FiSave className="mr-2" />
                      Update Password
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Notifications Settings */}
            {activeTab === 'notifications' && (
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Notification Settings</h2>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  Configure how you receive notifications
                </p>
                <div className="space-y-4">
                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                        defaultChecked
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Email notifications
                      </span>
                    </label>
                  </div>
                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                        defaultChecked
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        In-app notifications
                      </span>
                    </label>
                  </div>
                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        SMS notifications
                      </span>
                    </label>
                  </div>
                  <div className="flex justify-end">
                    <button className="btn btn-primary flex items-center">
                      <FiSave className="mr-2" />
                      Save Changes
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Storage Settings */}
            {activeTab === 'storage' && (
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Storage Settings</h2>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  Configure storage options for evidence
                </p>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="storageType" className="label">Default Storage Type</label>
                    <select
                      id="storageType"
                      name="storageType"
                      className="input"
                      defaultValue="local"
                    >
                      <option value="local">Local File System</option>
                      <option value="s3">Amazon S3</option>
                      <option value="gcs">Google Cloud Storage</option>
                      <option value="azure">Azure Blob Storage</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="retentionPeriod" className="label">Default Retention Period (days)</label>
                    <input
                      type="number"
                      id="retentionPeriod"
                      name="retentionPeriod"
                      className="input"
                      defaultValue="365"
                      min="1"
                    />
                  </div>
                  <div className="flex justify-end">
                    <button className="btn btn-primary flex items-center">
                      <FiSave className="mr-2" />
                      Save Changes
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Feature Settings */}
            {activeTab === 'features' && (
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Feature Settings</h2>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  Manage system features and capabilities
                </p>

                <div className="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 p-4 mb-6">
                  <div className="flex">
                    <div className="ml-3">
                      <p className="text-sm text-blue-700 dark:text-blue-200">
                        For advanced feature management, visit the <Link href="/admin/features" className="text-blue-600 dark:text-blue-300 underline">Feature Flags Admin</Link> page.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">Dark Mode</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Enable dark mode UI theme</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={isEnabled(FeatureFlag.DARK_MODE)}
                        readOnly
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">Advanced Dashboard</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Enable advanced dashboard features</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={isEnabled(FeatureFlag.ADVANCED_DASHBOARD)}
                        readOnly
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">Customizable Dashboard</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Allow customization of dashboard layout</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={isEnabled(FeatureFlag.CUSTOMIZABLE_DASHBOARD)}
                        readOnly
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">AI Assistant</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Enable AI-powered compliance assistant</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={isEnabled(FeatureFlag.EXPERIMENTAL_AI_ASSISTANT)}
                        readOnly
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="mt-6">
                    <Link href="/admin/features" className="btn btn-primary w-full">
                      Manage All Features
                    </Link>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
