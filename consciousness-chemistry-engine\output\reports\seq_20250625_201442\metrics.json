{"psi_score": 0.66, "fibonacci_analysis": {"residue_distances": [{"start": 13, "end": 19, "score": 0.9152476158708784, "ratio": 1.603050913593996, "golden_ratio_diff": 0.009260049702339592}, {"start": 2, "end": 8, "score": 0.7583916849328419, "ratio": 1.5664866849608412, "golden_ratio_diff": 0.031857985770051424}, {"start": 1, "end": 6, "score": 0.7580021379672502, "ratio": 1.6696909361859342, "golden_ratio_diff": 0.03192574927053904}, {"start": 9, "end": 19, "score": 0.7323610909667044, "ratio": 1.5589034782859656, "golden_ratio_diff": 0.03654466523883959}, {"start": 4, "end": 7, "score": 0.6988876129121452, "ratio": 1.6877462096764675, "golden_ratio_diff": 0.043084521963863545}, {"start": 1, "end": 7, "score": 0.6973567990960581, "ratio": 1.547813552645824, "golden_ratio_diff": 0.043398616217156}, {"start": 8, "end": 13, "score": 0.6614524773307815, "ratio": 1.5352190598987123, "golden_ratio_diff": 0.05118244080593511}, {"start": 2, "end": 7, "score": 0.6447451701990718, "ratio": 1.7071877220160132, "golden_ratio_diff": 0.05510003738240329}, {"start": 2, "end": 5, "score": 0.5983883505990221, "ratio": 1.7266292343555587, "golden_ratio_diff": 0.06711555280094289}, {"start": 9, "end": 14, "score": 0.5859932334749189, "ratio": 1.5037191841509276, "golden_ratio_diff": 0.07065043465946455}, {"start": 11, "end": 19, "score": 0.5781009471003729, "ratio": 1.4999495934834637, "golden_ratio_diff": 0.07298016981563168}, {"start": 8, "end": 11, "score": 0.5619515802543511, "ratio": 1.744161800606182, "golden_ratio_diff": 0.07795127465383744}, {"start": 2, "end": 11, "score": 0.5467180442908282, "ratio": 1.483883375196281, "golden_ratio_diff": 0.0829096387877857}, {"start": 9, "end": 12, "score": 0.5236161327786556, "ratio": 1.7652420750947229, "golden_ratio_diff": 0.09097960077993295}, {"start": 14, "end": 16, "score": 0.5209432547196868, "ratio": 1.76682756451479, "golden_ratio_diff": 0.09195948713033777}, {"start": 8, "end": 19, "score": 0.5121505827520887, "ratio": 1.4639080412097658, "golden_ratio_diff": 0.09525507412808303}, {"start": 4, "end": 8, "score": 0.5054580757646082, "ratio": 1.4597249853643628, "golden_ratio_diff": 0.09784034481738102}, {"start": 0, "end": 4, "score": 0.5033574020808421, "ratio": 1.4583890524934127, "golden_ratio_diff": 0.09866599673831643}, {"start": 1, "end": 8, "score": 0.5013858474183888, "ratio": 1.457125049994879, "golden_ratio_diff": 0.09944719324427508}], "torsion_angles": [{"start": 0, "end": 2, "score": 0.8803459957997555, "ratio": 1.6400258245292054, "golden_ratio_diff": 0.013591701986619933}, {"start": 2, "end": 5, "score": 0.8667955250968319, "ratio": 1.5931689125460426, "golden_ratio_diff": 0.015367462226836902}, {"start": 0, "end": 4, "score": 0.5335465002902308, "ratio": 1.7594907533039112, "golden_ratio_diff": 0.08742508843297346}], "overall_score": 0.6402361846225597}, "fibonacci_alignment": {"closest_fibonacci": 21, "difference": 1, "ratio": 0.9523809523809523, "alignment_score": 0.9545454545454545}, "trinity_validation": {"ners": 0.6540708553867679, "nepi": 0.5389999999999999, "nefc": 0.694, "overall": 0.6315283421547071, "passed": false}, "trinity_report": {"scores": {"ners": 0.6540708553867679, "nepi": 0.5389999999999999, "nefc": 0.694, "overall": 0.6315283421547071, "passed": false}, "thresholds": {"ners": 0.7, "nepi": 0.5, "nefc": 0.6}, "weights": {"ners": 0.4, "nepi": 0.3, "nefc": 0.3}, "validation": {"ners": {"score": 0.6540708553867679, "threshold": 0.7, "passed": false, "description": "Neural-Emotional Resonance Score: Measures structural harmony and consciousness resonance."}, "nepi": {"score": 0.5389999999999999, "threshold": 0.5, "passed": true, "description": "Neural-Emotional Potential Index: Evaluates functional potential and adaptability."}, "nefc": {"score": 0.694, "threshold": 0.6, "passed": true, "description": "Neural-Emotional Field Coherence: Assesses field coherence and quantum effects."}, "overall": {"score": 0.6315283421547071, "passed": false, "description": "Overall validation status based on all metrics."}}}}