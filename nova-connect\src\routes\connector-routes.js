/**
 * NovaFuse Universal API Connector - Connector Routes
 *
 * This module provides routes for managing connectors.
 */

const express = require('express');
const router = express.Router();
const { asyncHandler } = require('../utils/async-handler');
const { ValidationError } = require('../errors');
const connectorRegistryService = require('../connectors/services/connector-registry');
const connectorConfigService = require('../connectors/services/connector-config');
const connectorRuntimeService = require('../connectors/services/connector-runtime');

/**
 * @route GET /connectors
 * @description Get all connectors
 * @access Public
 */
router.get('/', asyncHandler(async (req, res) => {
  const filters = {
    status: req.query.status,
    type: req.query.type,
    category: req.query.category,
    tag: req.query.tag,
    search: req.query.search,
    sortBy: req.query.sortBy,
    sortOrder: req.query.sortOrder
  };

  const connectors = await connectorRegistryService.getAllConnectors(filters);

  res.json({
    count: connectors.length,
    connectors
  });
}));

/**
 * @route GET /connectors/:id
 * @description Get a connector by ID
 * @access Public
 */
router.get('/:id', asyncHandler(async (req, res) => {
  const connector = await connectorRegistryService.getConnector(req.params.id);

  res.json(connector);
}));

/**
 * @route POST /connectors
 * @description Create a new connector
 * @access Public
 */
router.post('/', asyncHandler(async (req, res) => {
  if (!req.body.name) {
    throw new ValidationError('Name is required');
  }

  if (!req.body.description) {
    throw new ValidationError('Description is required');
  }

  const connector = await connectorRegistryService.createConnector(req.body);

  res.status(201).json(connector);
}));

/**
 * @route PUT /connectors/:id
 * @description Update a connector
 * @access Public
 */
router.put('/:id', asyncHandler(async (req, res) => {
  const connector = await connectorRegistryService.updateConnector(req.params.id, req.body);

  res.json(connector);
}));

/**
 * @route DELETE /connectors/:id
 * @description Delete a connector
 * @access Public
 */
router.delete('/:id', asyncHandler(async (req, res) => {
  await connectorRegistryService.deleteConnector(req.params.id);

  res.status(204).end();
}));

/**
 * @route POST /connectors/:id/publish
 * @description Publish a connector
 * @access Public
 */
router.post('/:id/publish', asyncHandler(async (req, res) => {
  const connector = await connectorRegistryService.publishConnector(req.params.id);

  res.json(connector);
}));

/**
 * @route POST /connectors/:id/deprecate
 * @description Deprecate a connector
 * @access Public
 */
router.post('/:id/deprecate', asyncHandler(async (req, res) => {
  if (!req.body.reason) {
    throw new ValidationError('Reason is required');
  }

  const connector = await connectorRegistryService.deprecateConnector(req.params.id, req.body.reason);

  res.json(connector);
}));

/**
 * @route POST /connectors/:id/retire
 * @description Retire a connector
 * @access Public
 */
router.post('/:id/retire', asyncHandler(async (req, res) => {
  if (!req.body.reason) {
    throw new ValidationError('Reason is required');
  }

  const connector = await connectorRegistryService.retireConnector(req.params.id, req.body.reason);

  res.json(connector);
}));

/**
 * @route POST /connectors/:id/versions
 * @description Create a new version of a connector
 * @access Public
 */
router.post('/:id/versions', asyncHandler(async (req, res) => {
  if (!req.body.version) {
    throw new ValidationError('Version is required');
  }

  const connector = await connectorRegistryService.createConnectorVersion(req.params.id, req.body.version);

  res.status(201).json(connector);
}));

/**
 * @route GET /connectors/:id/metrics
 * @description Get connector metrics
 * @access Public
 */
router.get('/:id/metrics', asyncHandler(async (req, res) => {
  const metrics = await connectorRegistryService.getConnectorMetrics(req.params.id);

  res.json(metrics);
}));

/**
 * @route GET /connectors/:id/dependencies
 * @description Get connector dependencies
 * @access Public
 */
router.get('/:id/dependencies', asyncHandler(async (req, res) => {
  const dependencies = await connectorRegistryService.getConnectorDependencies(req.params.id);

  res.json({
    count: dependencies.length,
    dependencies
  });
}));

/**
 * @route GET /connectors/:id/dependents
 * @description Get connectors that depend on a connector
 * @access Public
 */
router.get('/:id/dependents', asyncHandler(async (req, res) => {
  const dependents = await connectorRegistryService.getDependentConnectors(req.params.id);

  res.json({
    count: dependents.length,
    dependents
  });
}));

/**
 * @route GET /connectors/:id/configs
 * @description Get configurations for a connector
 * @access Public
 */
router.get('/:id/configs', asyncHandler(async (req, res) => {
  const configs = await connectorConfigService.getConfigurationsForConnector(req.params.id);

  res.json({
    count: configs.length,
    configs
  });
}));

/**
 * @route POST /connectors/:id/execute
 * @description Execute a connector
 * @access Public
 */
router.post('/:id/execute', asyncHandler(async (req, res) => {
  if (!req.body.configId) {
    throw new ValidationError('Configuration ID is required');
  }

  const options = {
    timeout: req.body.timeout || 30000,
    parameters: req.body.parameters || {}
  };

  const result = await connectorRuntimeService.executeConnector(req.params.id, req.body.configId, options);

  res.json(result);
}));

/**
 * @route GET /connectors/executions/:executionId
 * @description Get execution status
 * @access Public
 */
router.get('/executions/:executionId', asyncHandler(async (req, res) => {
  const status = connectorRuntimeService.getExecutionStatus(req.params.executionId);

  res.json(status);
}));

/**
 * @route GET /connectors/executions
 * @description Get all executions
 * @access Public
 */
router.get('/executions', asyncHandler(async (req, res) => {
  const executions = connectorRuntimeService.getAllExecutions();

  res.json({
    count: executions.length,
    executions
  });
}));

/**
 * @route POST /connectors/executions/:executionId/cancel
 * @description Cancel an execution
 * @access Public
 */
router.post('/executions/:executionId/cancel', asyncHandler(async (req, res) => {
  const cancelled = connectorRuntimeService.cancelExecution(req.params.executionId);

  if (!cancelled) {
    res.status(400).json({
      error: {
        message: 'Execution could not be cancelled',
        code: 'EXECUTION_CANCEL_FAILED'
      }
    });
    return;
  }

  res.json({
    success: true,
    message: 'Execution cancelled'
  });
}));

module.exports = router;
