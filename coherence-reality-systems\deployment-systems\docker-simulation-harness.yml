version: '3.8'

services:
  # KetherNet Simulation Test Runner
  kethernet-simulator:
    build:
      context: .
      dockerfile: Dockerfile.simulator
    container_name: kethernet-simulator
    depends_on:
      - kethernet-blockchain
      - novadna-identity
      - novashield-security
      - trinity-gateway
    environment:
      - SIMULATION_MODE=full
      - CONSCIOUSNESS_THRESHOLD=2847
      - COHERENCE_VALIDATION=true
      - TRINITY_STACK_ENABLED=true
    volumes:
      - ./simulation-results:/app/results
      - ./kethernet-simulation-suite.py:/app/simulator.py
    networks:
      - trinity-network
    command: ["python", "/app/simulator.py"]
    
  # Network Traffic Generator
  traffic-generator:
    image: alpine:latest
    container_name: traffic-generator
    depends_on:
      - kethernet-simulator
    environment:
      - TARGET_HOSTS=kethernet-blockchain:8080,novadna-identity:8083,novashield-security:8085
      - TRAFFIC_PATTERNS=consciousness_filtered,trinity_validated,threat_simulation
    volumes:
      - ./traffic-scripts:/scripts
    networks:
      - trinity-network
    command: |
      sh -c "
        apk add --no-cache curl wrk apache2-utils &&
        echo '🌊 Starting traffic generation...' &&
        while true; do
          # Basic HTTP load test
          wrk -t4 -c50 -d10s http://kethernet-blockchain:8080/health
          
          # Consciousness-filtered requests
          for psi in 0.12 0.52 0.82 0.95 2.847; do
            curl -H 'X-Consciousness-Level: $$psi' http://kethernet-blockchain:8080/validate
          done
          
          # Trinity stack validation
          curl -X POST http://novadna-identity:8083/auth -d '{\"consciousness_validated\": true}'
          curl -X POST http://novashield-security:8085/threat-scan -d '{\"source\": \"simulator\"}'
          
          sleep 30
        done
      "
    
  # Threat Simulation Engine
  threat-simulator:
    image: alpine:latest
    container_name: threat-simulator
    depends_on:
      - novashield-security
    environment:
      - THREAT_TYPES=port_scan,malformed_headers,consciousness_bypass,ddos_simulation
      - AUTO_BLOCK_TEST=true
    networks:
      - trinity-network
    command: |
      sh -c "
        apk add --no-cache nmap curl hping3 &&
        echo '🛡️ Starting threat simulation...' &&
        while true; do
          # Port scan simulation
          nmap -p 8080-8086 novashield-security
          
          # Malformed header attacks
          curl -H 'X-Malicious: ☠️' http://novashield-security:8085/
          curl -H 'X-Consciousness-Level: -999' http://kethernet-blockchain:8080/
          
          # DDoS simulation (low intensity)
          for i in \$$(seq 1 10); do
            curl http://novashield-security:8085/ &
          done
          wait
          
          sleep 60
        done
      "
    
  # Consciousness Evolution Tracker
  evolution-tracker:
    build:
      context: .
      dockerfile: Dockerfile.evolution
    container_name: evolution-tracker
    depends_on:
      - novadna-identity
      - postgres-consciousness
    environment:
      - EVOLUTION_TRACKING=true
      - CONSCIOUSNESS_VALIDATION=true
      - ZK_PROOF_ENABLED=true
    volumes:
      - ./evolution-data:/app/data
    networks:
      - trinity-network
    command: |
      python -c "
      import asyncio
      import aiohttp
      import json
      import time
      from datetime import datetime
      
      async def track_evolution():
          users = ['user_001', 'user_002', 'user_003']
          events = ['consciousness_upgrade', 'coherence_training', 'divine_alignment']
          
          async with aiohttp.ClientSession() as session:
              while True:
                  for user in users:
                      event = events[int(time.time()) % len(events)]
                      delta = 0.05 + (hash(user + event) % 20) / 100
                      
                      payload = {
                          'user_id': user,
                          'event_type': event,
                          'consciousness_delta': delta,
                          'timestamp': datetime.now().isoformat()
                      }
                      
                      try:
                          async with session.post('http://novadna-identity:8083/evolution-update',
                                                json=payload) as response:
                              print(f'Evolution tracked: {user} → {event} (+{delta:.3f})')
                      except Exception as e:
                          print(f'Evolution tracking failed: {e}')
                      
                      await asyncio.sleep(5)
                  
                  await asyncio.sleep(30)
      
      asyncio.run(track_evolution())
      "
    
  # Performance Monitor
  performance-monitor:
    image: prom/prometheus:latest
    container_name: performance-monitor
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus-simulation.yml:/etc/prometheus/prometheus.yml
    networks:
      - trinity-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    
  # Simulation Dashboard
  simulation-dashboard:
    image: grafana/grafana:latest
    container_name: simulation-dashboard
    ports:
      - "3001:3000"  # Different port to avoid conflict with main dashboard
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=consciousness2847
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - ./grafana-simulation-dashboards:/var/lib/grafana/dashboards
      - ./grafana-simulation-config.ini:/etc/grafana/grafana.ini
    networks:
      - trinity-network
    depends_on:
      - performance-monitor
    
  # Results Aggregator
  results-aggregator:
    image: python:3.9-alpine
    container_name: results-aggregator
    volumes:
      - ./simulation-results:/results
      - ./aggregation-scripts:/scripts
    networks:
      - trinity-network
    command: |
      sh -c "
        pip install pandas matplotlib seaborn &&
        echo '📊 Starting results aggregation...' &&
        while true; do
          python /scripts/aggregate_results.py
          sleep 300  # Aggregate every 5 minutes
        done
      "

networks:
  trinity-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  simulation-results:
  evolution-data:
  grafana-simulation-data:
