#!/usr/bin/env node

/**
 * NovaConnect π-Coherence A/B Testing Implementation
 * Tests standard timing vs π-aligned timing for performance improvements
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// π-Coherence timing intervals (in milliseconds)
const PI_TIMING = {
    FAST: 31.42,        // High-frequency operations
    MEDIUM: 42.53,      // Standard operations
    SLOW: 53.64,        // Background operations
    HEARTBEAT: 314.2,   // System heartbeat
    TIMEOUT: 3142,      // Operation timeout (3.142 seconds)
    RETRY_DELAY: 42.53, // Retry delay
    HEALTH_CHECK: 314.2 // Health check interval
};

// Standard timing (current NovaConnect defaults)
const STANDARD_TIMING = {
    FAST: 10,           // Arbitrary fast timing
    MEDIUM: 100,        // Arbitrary medium timing
    SLOW: 1000,         // Current retry delay
    HEARTBEAT: 30000,   // Current health check
    TIMEOUT: 30000,     // Current timeout
    RETRY_DELAY: 1000,  // Current retry delay
    HEALTH_CHECK: 30000 // Current health check
};

class PiCoherenceTest {
    constructor() {
        this.results = {
            standard: {
                requests: [],
                errors: 0,
                totalTime: 0,
                avgResponseTime: 0,
                successRate: 0,
                coherenceScore: 0
            },
            piTiming: {
                requests: [],
                errors: 0,
                totalTime: 0,
                avgResponseTime: 0,
                successRate: 0,
                coherenceScore: 0
            }
        };
        
        this.testConfig = {
            iterations: 50,
            baseUrl: process.env.NOVA_CONNECT_URL || 'https://httpbin.org',
            endpoints: ['/delay/0', '/status/200', '/json', '/uuid', '/ip'],
            concurrency: 3
        };
    }

    /**
     * Create HTTP client with specific timing configuration
     */
    createClient(timingConfig, label) {
        return axios.create({
            baseURL: this.testConfig.baseUrl,
            timeout: timingConfig.TIMEOUT,
            headers: {
                'Content-Type': 'application/json',
                'X-Test-Config': label,
                'X-Pi-Coherence': label === 'pi-timing' ? 'enabled' : 'disabled'
            }
        });
    }

    /**
     * Simulate API operations with specific timing
     */
    async simulateOperation(client, timingConfig, operationType = 'MEDIUM') {
        const startTime = Date.now();
        
        try {
            // Pre-operation π-timing delay
            await this.sleep(timingConfig[operationType]);
            
            // Random endpoint selection
            const endpoint = this.testConfig.endpoints[
                Math.floor(Math.random() * this.testConfig.endpoints.length)
            ];
            
            // Make request
            const response = await client.get(endpoint);
            
            // Post-operation π-timing delay
            await this.sleep(timingConfig[operationType] * 0.5);
            
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            return {
                success: true,
                duration,
                status: response.status,
                endpoint,
                coherence: this.calculateCoherence(duration, timingConfig[operationType])
            };
            
        } catch (error) {
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            return {
                success: false,
                duration,
                error: error.message,
                endpoint: error.config?.url || 'unknown',
                coherence: 0
            };
        }
    }

    /**
     * Calculate coherence score based on timing alignment
     */
    calculateCoherence(actualDuration, expectedInterval) {
        // Measure how well actual timing aligns with π-intervals
        const piIntervals = [31.42, 42.53, 53.64, 64.75, 75.86, 86.97];
        
        // Find closest π-interval
        const closestInterval = piIntervals.reduce((prev, curr) => 
            Math.abs(curr - actualDuration) < Math.abs(prev - actualDuration) ? curr : prev
        );
        
        // Calculate alignment score (1.0 = perfect alignment, 0.0 = no alignment)
        const alignment = 1.0 - (Math.abs(actualDuration - closestInterval) / closestInterval);
        return Math.max(alignment, 0);
    }

    /**
     * Sleep for specified milliseconds
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Run test with specific timing configuration
     */
    async runTestSuite(timingConfig, label) {
        console.log(`\n🔬 Running ${label} test suite...`);
        console.log(`Timeout: ${timingConfig.TIMEOUT}ms, Retry: ${timingConfig.RETRY_DELAY}ms`);
        
        const client = this.createClient(timingConfig, label);
        const results = {
            requests: [],
            errors: 0,
            totalTime: 0,
            coherenceScores: []
        };
        
        const startTime = Date.now();
        
        // Run concurrent batches
        const batchSize = this.testConfig.concurrency;
        const totalBatches = Math.ceil(this.testConfig.iterations / batchSize);
        
        for (let batch = 0; batch < totalBatches; batch++) {
            const batchPromises = [];
            const remainingIterations = Math.min(batchSize, this.testConfig.iterations - (batch * batchSize));
            
            for (let i = 0; i < remainingIterations; i++) {
                // Vary operation types
                const operationType = i % 3 === 0 ? 'FAST' : i % 3 === 1 ? 'MEDIUM' : 'SLOW';
                batchPromises.push(this.simulateOperation(client, timingConfig, operationType));
            }
            
            // Wait for batch completion
            const batchResults = await Promise.all(batchPromises);
            
            // Process batch results
            batchResults.forEach(result => {
                results.requests.push(result);
                if (!result.success) {
                    results.errors++;
                }
                if (result.coherence) {
                    results.coherenceScores.push(result.coherence);
                }
            });
            
            // Progress indicator
            const progress = ((batch + 1) / totalBatches * 100).toFixed(1);
            process.stdout.write(`\r  Progress: ${progress}% (${batch + 1}/${totalBatches} batches)`);
            
            // Inter-batch delay using timing configuration
            if (batch < totalBatches - 1) {
                await this.sleep(timingConfig.MEDIUM);
            }
        }
        
        const endTime = Date.now();
        results.totalTime = endTime - startTime;
        
        // Calculate metrics
        const successfulRequests = results.requests.filter(r => r.success);
        results.avgResponseTime = successfulRequests.length > 0 
            ? successfulRequests.reduce((sum, r) => sum + r.duration, 0) / successfulRequests.length 
            : 0;
        results.successRate = successfulRequests.length / results.requests.length;
        results.coherenceScore = results.coherenceScores.length > 0
            ? results.coherenceScores.reduce((sum, score) => sum + score, 0) / results.coherenceScores.length
            : 0;
        
        console.log(`\n  ✅ Completed: ${results.requests.length} requests in ${results.totalTime}ms`);
        console.log(`  📊 Success Rate: ${(results.successRate * 100).toFixed(1)}%`);
        console.log(`  ⚡ Avg Response: ${results.avgResponseTime.toFixed(1)}ms`);
        console.log(`  🔱 Coherence Score: ${results.coherenceScore.toFixed(3)}`);
        
        return results;
    }

    /**
     * Run complete A/B test comparison
     */
    async runComparison() {
        console.log('🚀 Starting NovaConnect π-Coherence A/B Test');
        console.log(`📋 Test Configuration:`);
        console.log(`   Iterations: ${this.testConfig.iterations}`);
        console.log(`   Concurrency: ${this.testConfig.concurrency}`);
        console.log(`   Base URL: ${this.testConfig.baseUrl}`);
        console.log(`   Endpoints: ${this.testConfig.endpoints.join(', ')}`);
        
        try {
            // Test standard timing
            this.results.standard = await this.runTestSuite(STANDARD_TIMING, 'STANDARD');
            
            // Brief pause between tests
            console.log('\n⏸️  Pausing between test suites...');
            await this.sleep(2000);
            
            // Test π-coherence timing
            this.results.piTiming = await this.runTestSuite(PI_TIMING, 'π-COHERENCE');
            
            // Generate comparison report
            this.generateReport();
            
        } catch (error) {
            console.error('❌ Test failed:', error.message);
            throw error;
        }
    }

    /**
     * Generate comprehensive comparison report
     */
    generateReport() {
        console.log('\n' + '='.repeat(80));
        console.log('🔱 π-COHERENCE A/B TEST RESULTS');
        console.log('='.repeat(80));
        
        const standard = this.results.standard;
        const piTiming = this.results.piTiming;
        
        // Calculate improvements
        const efficiencyGain = piTiming.avgResponseTime > 0 
            ? standard.avgResponseTime / piTiming.avgResponseTime 
            : 1;
        const errorReduction = standard.errors > 0 
            ? (standard.errors - piTiming.errors) / standard.errors 
            : 0;
        const coherenceImprovement = piTiming.coherenceScore - standard.coherenceScore;
        const throughputImprovement = standard.totalTime > 0 
            ? standard.totalTime / piTiming.totalTime 
            : 1;
        
        // Performance comparison table
        console.log('\n📊 PERFORMANCE COMPARISON:');
        console.log('┌─────────────────────────┬─────────────────┬─────────────────┬─────────────────┐');
        console.log('│ Metric                  │ Standard        │ π-Coherence     │ Improvement     │');
        console.log('├─────────────────────────┼─────────────────┼─────────────────┼─────────────────┤');
        console.log(`│ Avg Response Time       │ ${standard.avgResponseTime.toFixed(1).padStart(13)}ms │ ${piTiming.avgResponseTime.toFixed(1).padStart(13)}ms │ ${efficiencyGain.toFixed(2).padStart(13)}× │`);
        console.log(`│ Success Rate            │ ${(standard.successRate * 100).toFixed(1).padStart(12)}% │ ${(piTiming.successRate * 100).toFixed(1).padStart(12)}% │ ${((piTiming.successRate - standard.successRate) * 100).toFixed(1).padStart(12)}% │`);
        console.log(`│ Error Count             │ ${standard.errors.toString().padStart(15)} │ ${piTiming.errors.toString().padStart(15)} │ ${(errorReduction * 100).toFixed(1).padStart(12)}% │`);
        console.log(`│ Total Time              │ ${standard.totalTime.toString().padStart(13)}ms │ ${piTiming.totalTime.toString().padStart(13)}ms │ ${throughputImprovement.toFixed(2).padStart(13)}× │`);
        console.log(`│ Coherence Score         │ ${standard.coherenceScore.toFixed(3).padStart(15)} │ ${piTiming.coherenceScore.toFixed(3).padStart(15)} │ ${coherenceImprovement.toFixed(3).padStart(15)} │`);
        console.log('└─────────────────────────┴─────────────────┴─────────────────┴─────────────────┘');
        
        // Key findings
        console.log('\n🔍 KEY FINDINGS:');
        
        if (efficiencyGain >= 3.0) {
            console.log(`   🎯 BREAKTHROUGH: ${efficiencyGain.toFixed(2)}× efficiency gain (approaching π-target of 3.142×)`);
        } else if (efficiencyGain >= 2.0) {
            console.log(`   ✅ SIGNIFICANT: ${efficiencyGain.toFixed(2)}× efficiency improvement`);
        } else if (efficiencyGain >= 1.2) {
            console.log(`   📈 MODERATE: ${efficiencyGain.toFixed(2)}× efficiency improvement`);
        } else {
            console.log(`   ⚠️  MINIMAL: ${efficiencyGain.toFixed(2)}× efficiency change`);
        }
        
        if (coherenceImprovement > 0.1) {
            console.log(`   🔱 COHERENCE: Significant coherence improvement (+${coherenceImprovement.toFixed(3)})`);
        } else if (coherenceImprovement > 0.05) {
            console.log(`   🔱 COHERENCE: Moderate coherence improvement (+${coherenceImprovement.toFixed(3)})`);
        } else {
            console.log(`   🔱 COHERENCE: Minimal coherence change (${coherenceImprovement.toFixed(3)})`);
        }
        
        if (errorReduction > 0.5) {
            console.log(`   🛡️  RELIABILITY: Major error reduction (${(errorReduction * 100).toFixed(1)}%)`);
        } else if (errorReduction > 0.2) {
            console.log(`   🛡️  RELIABILITY: Moderate error reduction (${(errorReduction * 100).toFixed(1)}%)`);
        }
        
        // Success assessment
        console.log('\n🎯 SUCCESS ASSESSMENT:');
        let successScore = 0;
        
        if (efficiencyGain >= 3.0) successScore += 40;
        else if (efficiencyGain >= 2.0) successScore += 30;
        else if (efficiencyGain >= 1.5) successScore += 20;
        else if (efficiencyGain >= 1.2) successScore += 10;
        
        if (coherenceImprovement >= 0.1) successScore += 30;
        else if (coherenceImprovement >= 0.05) successScore += 20;
        else if (coherenceImprovement >= 0.01) successScore += 10;
        
        if (errorReduction >= 0.5) successScore += 20;
        else if (errorReduction >= 0.2) successScore += 15;
        else if (errorReduction >= 0.1) successScore += 10;
        
        if (piTiming.successRate > standard.successRate) successScore += 10;
        
        let successLevel;
        if (successScore >= 90) successLevel = "🏆 BREAKTHROUGH SUCCESS";
        else if (successScore >= 70) successLevel = "🎯 SIGNIFICANT SUCCESS";
        else if (successScore >= 50) successLevel = "📈 MODERATE SUCCESS";
        else if (successScore >= 30) successLevel = "⚡ PARTIAL SUCCESS";
        else successLevel = "⚠️  NEEDS IMPROVEMENT";
        
        console.log(`   ${successLevel} (Score: ${successScore}/100)`);
        
        // Recommendations
        console.log('\n💡 RECOMMENDATIONS:');
        if (efficiencyGain >= 2.0) {
            console.log('   ✅ Deploy π-coherence timing to production');
            console.log('   ✅ Expand testing to other NovaFuse systems');
            console.log('   ✅ Document π-coherence as standard practice');
        } else if (efficiencyGain >= 1.5) {
            console.log('   📋 Continue testing with refined π-intervals');
            console.log('   📋 Test with higher load scenarios');
            console.log('   📋 Monitor long-term stability');
        } else {
            console.log('   🔧 Adjust π-timing intervals');
            console.log('   🔧 Test different endpoint combinations');
            console.log('   🔧 Investigate system-specific optimizations');
        }
        
        // Save detailed results
        this.saveResults();
        
        console.log('\n' + '='.repeat(80));
        console.log('🔱 π-COHERENCE TEST COMPLETE');
        console.log('='.repeat(80));
    }

    /**
     * Save detailed results to file
     */
    saveResults() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `pi-coherence-test-results-${timestamp}.json`;
        const filepath = path.join(__dirname, 'test-results', filename);
        
        // Ensure directory exists
        const dir = path.dirname(filepath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        
        const reportData = {
            timestamp: new Date().toISOString(),
            testConfig: this.testConfig,
            timingConfigs: {
                standard: STANDARD_TIMING,
                piTiming: PI_TIMING
            },
            results: this.results,
            summary: {
                efficiencyGain: this.results.standard.avgResponseTime / this.results.piTiming.avgResponseTime,
                coherenceImprovement: this.results.piTiming.coherenceScore - this.results.standard.coherenceScore,
                errorReduction: (this.results.standard.errors - this.results.piTiming.errors) / Math.max(this.results.standard.errors, 1)
            }
        };
        
        fs.writeFileSync(filepath, JSON.stringify(reportData, null, 2));
        console.log(`\n💾 Detailed results saved to: ${filepath}`);
    }
}

// Run the test if called directly
if (require.main === module) {
    const test = new PiCoherenceTest();
    
    test.runComparison()
        .then(() => {
            console.log('\n✅ Test completed successfully');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n❌ Test failed:', error);
            process.exit(1);
        });
}

module.exports = PiCoherenceTest;
