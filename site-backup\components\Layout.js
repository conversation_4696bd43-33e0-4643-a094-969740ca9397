import React from 'react';
import Head from 'next/head';
import FloatingNovaConcierge from './FloatingNovaConcierge';

const Layout = ({ children, title = 'NovaFuse API Superstore' }) => {
  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content="NovaFuse API Superstore: A comprehensive marketplace for GRC APIs" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen flex flex-col bg-primary text-primary">
        {/* Navigation is now handled separately */}

        <main className="flex-grow container mx-auto px-4 py-8">
          {children}
        </main>

        {/* Footer is now handled in _app.js */}
      </div>

      {/* Floating NovaConcierge Widget */}
      <FloatingNovaConcierge />
    </>
  );
};

export default Layout;
