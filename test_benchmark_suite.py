#!/usr/bin/env python3
"""
Test NovaPi Benchmark Suite
Local testing of AI performance optimization with π-coherence
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'novapi-benchmark'))

def test_benchmark_suite():
    print("🧪 Testing NovaPi Benchmark Suite")
    print("=" * 60)
    
    try:
        # Test π-Coherence Scheduler
        print("🧭 Testing π-Coherence Scheduler...")
        from pi_scheduler import PiScheduler, PiTimingMode, pi_benchmark
        
        scheduler = PiScheduler()
        print(f"   ✅ Scheduler initialized with {len(scheduler.pi_intervals)} intervals")
        
        # Test basic operation scheduling
        def test_operation():
            import time
            time.sleep(0.005)  # 5ms operation
            return "operation_complete"
        
        result = scheduler.schedule_operation(test_operation, PiTimingMode.STANDARD_INFERENCE, "test_op")
        print(f"   ✅ Operation scheduled: π-optimized = {result['pi_optimized']}")
        print(f"   📊 Performance improvement: {result['metrics'].performance_improvement:.2f}%")
        
        # Test AI Benchmarker
        print("\n🔬 Testing AI Benchmarker...")
        from ai_benchmarker import AIBenchmarker
        
        benchmarker = AIBenchmarker("test_results")
        print("   ✅ AI Benchmarker initialized")
        
        # Test text model benchmark (will use mock if transformers not available)
        print("   🧠 Running text model benchmark...")
        text_result = benchmarker.benchmark_text_model("distilbert-base-uncased", batch_size=8, iterations=3)
        
        print(f"   📈 Text model results:")
        print(f"      Latency improvement: {text_result.improvement_latency_percent:+.1f}%")
        print(f"      Throughput improvement: {text_result.improvement_throughput_percent:+.1f}%")
        print(f"      Memory improvement: {text_result.improvement_memory_percent:+.1f}%")
        
        # Test vision model benchmark
        print("   👁️  Running vision model benchmark...")
        vision_result = benchmarker.benchmark_vision_model("resnet18", batch_size=4, iterations=3)
        
        print(f"   📈 Vision model results:")
        print(f"      Latency improvement: {vision_result.improvement_latency_percent:+.1f}%")
        print(f"      Throughput improvement: {vision_result.improvement_throughput_percent:+.1f}%")
        print(f"      Memory improvement: {vision_result.improvement_memory_percent:+.1f}%")
        
        # Test comprehensive benchmark
        print("\n🏆 Running comprehensive benchmark...")
        summary = benchmarker.run_comprehensive_benchmark()
        
        print(f"\n📊 COMPREHENSIVE BENCHMARK RESULTS:")
        print(f"   Models tested: {summary['total_models_tested']}")
        print(f"   Average latency improvement: {summary['average_improvements']['latency_percent']:+.1f}%")
        print(f"   Average throughput improvement: {summary['average_improvements']['throughput_percent']:+.1f}%")
        print(f"   Average memory improvement: {summary['average_improvements']['memory_percent']:+.1f}%")
        print(f"   Best performing model: {summary['best_performing_model']}")
        print(f"   π-Coherence effective: {'✅ YES' if summary['pi_coherence_effective'] else '❌ NO'}")
        
        # Test π-intervals configuration
        print("\n🔧 Testing π-intervals configuration...")
        import json
        with open('novapi-benchmark/π_intervals.json', 'r') as f:
            intervals = json.load(f)
        
        print(f"   ✅ π-intervals loaded: {len(intervals) - 1} timing modes")  # -1 for sequence_info
        print(f"   🧭 Sequence: {intervals['sequence_info']['sequence']}")
        print(f"   📐 Pattern: {intervals['sequence_info']['pattern']}")
        
        print(f"\n✅ NovaPi Benchmark Suite Test COMPLETE!")
        print(f"🧭 π-Coherence optimization: FUNCTIONAL")
        print(f"🔬 AI benchmarking: OPERATIONAL")
        print(f"📊 Performance measurement: ACTIVE")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_benchmark_suite()
    sys.exit(0 if success else 1)
