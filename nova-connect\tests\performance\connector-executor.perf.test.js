/**
 * Performance tests for the Connector Executor
 */

const axios = require('axios');
const MockAdapter = require('axios-mock-adapter');
const connectorExecutor = require('../../connector-executor');
const connectorRegistry = require('../../registry/connector-registry');
const { mockGoogleCloudConnector } = require('../mocks/mock-connector');

// Mock axios
const mockAxios = new MockAdapter(axios);

// Mock connector registry
jest.mock('../../registry/connector-registry', () => ({
  getConnector: jest.fn(),
  initialize: jest.fn().mockResolvedValue(true)
}));

describe('Connector Executor Performance', () => {
  beforeEach(() => {
    // Reset mocks
    mockAxios.reset();
    jest.clearAllMocks();
    
    // Mock connector registry response
    connectorRegistry.getConnector.mockReturnValue(mockGoogleCloudConnector);
    
    // Mock axios response
    mockAxios.onGet().reply(200, {
      findings: [
        {
          name: 'finding-1',
          parent: 'organizations/123/sources/456',
          resourceName: 'projects/my-project/instances/my-instance',
          state: 'ACTIVE',
          category: 'VULNERABILITY',
          severity: 'HIGH',
          eventTime: '2023-06-01T00:00:00Z',
          createTime: '2023-06-01T00:00:00Z'
        }
      ],
      nextPageToken: ''
    });
  });
  
  test('should handle 100 concurrent requests efficiently', async () => {
    // Create 100 concurrent requests
    const requests = Array.from({ length: 100 }).map(() => 
      connectorExecutor.executeConnector(
        'google-cloud-security-1.0.0',
        'list-findings',
        {
          path: {
            organizationId: '123',
            sourceId: '456'
          },
          auth: {
            token: 'test-token'
          }
        }
      )
    );
    
    // Start timer
    const startTime = Date.now();
    
    // Execute all requests
    const results = await Promise.all(requests);
    
    // End timer
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Verify all requests were successful
    expect(results.every(result => result.success)).toBe(true);
    
    // Verify performance
    console.log(`Executed 100 requests in ${duration}ms (${duration / 100}ms per request)`);
    
    // Performance should be under 500ms total for 100 requests
    expect(duration).toBeLessThan(500);
    
    // Verify connector registry was called 100 times
    expect(connectorRegistry.getConnector).toHaveBeenCalledTimes(100);
    
    // Verify axios was called 100 times
    expect(mockAxios.history.get.length).toBe(100);
  });
  
  test('should maintain performance with large response payloads', async () => {
    // Create a large response payload
    const largePayload = {
      findings: Array.from({ length: 1000 }).map((_, i) => ({
        name: `finding-${i}`,
        parent: 'organizations/123/sources/456',
        resourceName: 'projects/my-project/instances/my-instance',
        state: 'ACTIVE',
        category: 'VULNERABILITY',
        severity: 'HIGH',
        eventTime: '2023-06-01T00:00:00Z',
        createTime: '2023-06-01T00:00:00Z',
        // Add more fields to increase payload size
        details: {
          description: 'A very detailed description of the finding that takes up a lot of space',
          recommendation: 'A very detailed recommendation that takes up a lot of space',
          references: Array.from({ length: 10 }).map((_, j) => ({
            url: `https://example.com/reference/${j}`,
            description: `Reference ${j} with a lot of text to increase payload size`
          }))
        }
      })),
      nextPageToken: ''
    };
    
    // Mock axios response with large payload
    mockAxios.reset();
    mockAxios.onGet().reply(200, largePayload);
    
    // Start timer
    const startTime = Date.now();
    
    // Execute request
    const result = await connectorExecutor.executeConnector(
      'google-cloud-security-1.0.0',
      'list-findings',
      {
        path: {
          organizationId: '123',
          sourceId: '456'
        },
        auth: {
          token: 'test-token'
        }
      }
    );
    
    // End timer
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Verify request was successful
    expect(result.success).toBe(true);
    expect(result.data).toHaveLength(1000);
    
    // Verify performance
    console.log(`Processed large payload (1000 items) in ${duration}ms`);
    
    // Performance should be under 100ms for processing large payload
    expect(duration).toBeLessThan(100);
  });
  
  test('should handle rate limiting gracefully', async () => {
    // Mock axios to simulate rate limiting
    mockAxios.reset();
    
    // First 50 requests succeed
    for (let i = 0; i < 50; i++) {
      mockAxios.onGet().replyOnce(200, {
        findings: [{ name: `finding-${i}` }],
        nextPageToken: ''
      });
    }
    
    // Next 50 requests get rate limited
    for (let i = 0; i < 50; i++) {
      mockAxios.onGet().replyOnce(429, {
        error: {
          code: 429,
          message: 'Rate limit exceeded',
          status: 'RESOURCE_EXHAUSTED'
        }
      });
    }
    
    // Create 100 concurrent requests
    const requests = Array.from({ length: 100 }).map(() => 
      connectorExecutor.executeConnector(
        'google-cloud-security-1.0.0',
        'list-findings',
        {
          path: {
            organizationId: '123',
            sourceId: '456'
          },
          auth: {
            token: 'test-token'
          }
        }
      )
    );
    
    // Execute all requests
    const results = await Promise.all(requests);
    
    // Verify results
    const successfulRequests = results.filter(result => result.success);
    const failedRequests = results.filter(result => !result.success);
    
    expect(successfulRequests.length).toBe(50);
    expect(failedRequests.length).toBe(50);
    expect(failedRequests.every(result => result.statusCode === 429)).toBe(true);
    
    // Verify metrics were updated correctly
    const metrics = connectorExecutor.getMetrics();
    expect(metrics.totalRequests).toBe(100);
    expect(metrics.successfulRequests).toBe(50);
    expect(metrics.failedRequests).toBe(50);
  });
});
