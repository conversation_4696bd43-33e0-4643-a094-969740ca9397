import { useState, useEffect } from 'react';
import Head from 'next/head';
import { motion } from 'framer-motion';
import styles from '../styles/Home.module.css';
import { Triadic<PERSON>ard, ConsciousnessMeter, QuantumToggle, Triadic<PERSON>hart, EthicalImpactMap } from '../components/ui/ComphyonicElements';
import NovaConnect from '../utils/nova-connect';
import { config } from '../config';
import ProductScanner from '../components/ProductScanner';
import PerformanceMetrics from '../components/PerformanceMetrics';

// Triadic data constants
const triadicMetrics = {
  psi: 75,
  phi: 85,
  kappa: 90
};

// Ethical impact data
const ethicalImpactData = [
  { value: 85, positive: true },
  { value: 75, positive: true },
  { value: 65, positive: false },
  { value: 90, positive: true },
  { value: 80, positive: true }
];

export default function Dashboard() {
  const [products, setProducts] = useState([]);
  const [stats, setStats] = useState({
    conversionRate: 0,
    monthlySales: 0,
    totalRevenue: 0
  });
  const [scanning, setScanning] = useState(false);
  const [quantumState, setQuantumState] = useState(false);



  const scanProducts = async () => {
    setScanning(true);
    try {
      const novaConnect = new NovaConnect(config);
      
      // Connect to ShareASale
      const connectResult = await novaConnect.connect({
        endpoint: 'https://api.shareasale.com',
        credentials: config.credentials
      });

      if (!connectResult.success) {
        setScanning(false);
        return;
      }

      // Fetch products
      const products = await novaConnect.request('products/list', 'GET');
      
      if (products.success) {
        const productData = products.data;
        setProducts(productData);
        
        // Calculate stats
        const totalRevenue = productData.reduce((sum, product) => sum + (product.price || 0) * (product.sales || 0), 0);
        const monthlySales = productData.reduce((sum, product) => sum + (product.sales || 0), 0);
        const conversionRate = monthlySales > 0 ? (totalRevenue / monthlySales) / 100 : 0;
        
        setStats({
          conversionRate,
          monthlySales,
          totalRevenue
        });
      } else {
        setScanning(false);
      }

      setScanning(false);
    } catch (error) {
      console.error('Scan error:', error);
      setScanning(false);
    }
  };

  useEffect(() => {
    // Initialize dashboard
  }, []);



  const handleScan = () => {
    scanProducts();
  };

  const handleProductSelect = (product) => {
    console.log(product);
  };

  return (
    <div className={styles.container}>
      <Head>
        <title>Triadic Affiliate Dashboard</title>
        <meta name="description" content="Consciousness-based affiliate marketing dashboard with NovaShield protection" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className={styles.main}>
        <div className={styles.grid}>
          <TriadicCard 
            title="Product Scanner" 
            icon="🔍" 
          >
            <ProductScanner 
              onScan={handleScan} 
              onProductSelect={handleProductSelect}
              scanning={scanning}
            />
          </TriadicCard>

          <TriadicCard 
            title="Performance Metrics" 
            icon="📊" 
          >
            <PerformanceMetrics stats={stats} products={products} />
          </TriadicCard>

          <TriadicCard 
            title="Consciousness Monitor"
            icon="🧠"
          >
            <ConsciousnessMeter 
              level={75} 
              label="Consciousness Level" 
            />
            <QuantumToggle 
              active={quantumState}
              onToggle={() => setQuantumState(!quantumState)}
            />
          </TriadicCard>

          <TriadicCard 
            title="Triadic Impact" 
            icon="🔄"
          >
            <TriadicChart data={triadicMetrics} />
          </TriadicCard>

          <TriadicCard 
            title="Ethical Impact Map" 
            icon="🌐"
          >
            <EthicalImpactMap data={ethicalImpactData} />
          </TriadicCard>
        </div>

        <div className="mt-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex flex-col items-center"
          >
            <p className="text-gray-600">
              &copy; 2025 NovaFuse Technologies
            </p>
            <p className="text-gray-600">
              Powered by Triadic Consciousness Framework
            </p>
          </motion.div>
        </div>
      </main>
    </div>
  );
}
