/**
 * Multi-Factor Authentication Service
 *
 * This module provides multi-factor authentication capabilities for the Finite Universe
 * Principle defense system, enabling secure authentication with multiple factors.
 */

const EventEmitter = require('events');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');

/**
 * MFAService class
 * 
 * Provides multi-factor authentication capabilities.
 */
class MFAService extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      tokenExpiration: options.tokenExpiration || 3600, // 1 hour in seconds
      maxFailedAttempts: options.maxFailedAttempts || 5,
      lockoutDuration: options.lockoutDuration || 900, // 15 minutes in seconds
      requiredFactors: options.requiredFactors || 2,
      supportedFactors: options.supportedFactors || ['password', 'totp', 'email', 'sms', 'biometric'],
      ...options
    };

    // Initialize user registry
    this.users = new Map();
    
    // Initialize session registry
    this.sessions = new Map();
    
    // Initialize token registry
    this.tokens = new Map();
    
    // Initialize failed attempts registry
    this.failedAttempts = new Map();
    
    // Initialize locked accounts registry
    this.lockedAccounts = new Map();
    
    // Initialize factor providers registry
    this.factorProviders = new Map();

    if (this.options.enableLogging) {
      console.log('MFAService initialized with options:', {
        ...this.options,
        // Hide sensitive options in logs
        secretKey: this.options.secretKey ? '***' : undefined
      });
    }
  }

  /**
   * Register a user
   * @param {Object} user - User information
   * @returns {Object} - Registered user
   */
  registerUser(user) {
    // Validate user information
    if (!user || !user.username) {
      throw new Error('Invalid user information');
    }
    
    // Check if user already exists
    if (this.users.has(user.username)) {
      throw new Error(`User ${user.username} already exists`);
    }
    
    // Generate user ID if not provided
    const userId = user.id || uuidv4();
    
    // Create user object
    const userObj = {
      id: userId,
      username: user.username,
      factors: user.factors || {},
      requiredFactors: user.requiredFactors || this.options.requiredFactors,
      createdAt: Date.now(),
      status: 'active'
    };
    
    // Register user
    this.users.set(user.username, userObj);
    
    if (this.options.enableLogging) {
      console.log(`User ${user.username} registered`);
    }
    
    // Emit user-registered event
    this.emit('user-registered', { username: user.username });
    
    return { ...userObj, factors: { ...userObj.factors } };
  }

  /**
   * Register a factor for a user
   * @param {string} username - Username
   * @param {string} factorType - Factor type
   * @param {Object} factorData - Factor data
   * @returns {boolean} - True if factor was registered, false otherwise
   */
  registerFactor(username, factorType, factorData) {
    // Check if factor type is supported
    if (!this.options.supportedFactors.includes(factorType)) {
      throw new Error(`Factor type ${factorType} is not supported`);
    }
    
    // Check if user exists
    if (!this.users.has(username)) {
      throw new Error(`User ${username} does not exist`);
    }
    
    // Get user
    const user = this.users.get(username);
    
    // Check if factor provider exists
    if (this.factorProviders.has(factorType)) {
      // Get factor provider
      const provider = this.factorProviders.get(factorType);
      
      // Validate factor data
      if (!provider.validateFactorData(factorData)) {
        throw new Error(`Invalid factor data for ${factorType}`);
      }
      
      // Register factor with provider
      const factorId = provider.registerFactor(username, factorData);
      
      // Update user factors
      user.factors[factorType] = {
        id: factorId,
        registeredAt: Date.now(),
        lastUsed: null
      };
    } else {
      // Register factor without provider
      user.factors[factorType] = {
        ...factorData,
        registeredAt: Date.now(),
        lastUsed: null
      };
    }
    
    // Update user
    this.users.set(username, user);
    
    if (this.options.enableLogging) {
      console.log(`Factor ${factorType} registered for user ${username}`);
    }
    
    // Emit factor-registered event
    this.emit('factor-registered', { username, factorType });
    
    return true;
  }

  /**
   * Register a factor provider
   * @param {string} factorType - Factor type
   * @param {Object} provider - Factor provider
   * @returns {boolean} - True if provider was registered, false otherwise
   */
  registerFactorProvider(factorType, provider) {
    // Check if factor type is supported
    if (!this.options.supportedFactors.includes(factorType)) {
      throw new Error(`Factor type ${factorType} is not supported`);
    }
    
    // Check if provider has required methods
    if (!provider.validateFactorData || !provider.verifyFactor || !provider.registerFactor) {
      throw new Error(`Invalid factor provider for ${factorType}`);
    }
    
    // Register provider
    this.factorProviders.set(factorType, provider);
    
    if (this.options.enableLogging) {
      console.log(`Factor provider registered for ${factorType}`);
    }
    
    // Emit provider-registered event
    this.emit('provider-registered', { factorType });
    
    return true;
  }

  /**
   * Initiate authentication
   * @param {string} username - Username
   * @returns {Object} - Authentication session
   */
  initiateAuth(username) {
    // Check if user exists
    if (!this.users.has(username)) {
      throw new Error(`User ${username} does not exist`);
    }
    
    // Get user
    const user = this.users.get(username);
    
    // Check if account is locked
    if (this.isAccountLocked(username)) {
      throw new Error(`Account ${username} is locked`);
    }
    
    // Generate session ID
    const sessionId = uuidv4();
    
    // Create session object
    const session = {
      id: sessionId,
      username,
      requiredFactors: user.requiredFactors,
      completedFactors: [],
      status: 'pending',
      createdAt: Date.now(),
      expiresAt: Date.now() + (this.options.tokenExpiration * 1000)
    };
    
    // Register session
    this.sessions.set(sessionId, session);
    
    if (this.options.enableLogging) {
      console.log(`Authentication initiated for user ${username}`);
    }
    
    // Emit auth-initiated event
    this.emit('auth-initiated', { username, sessionId });
    
    return {
      sessionId,
      requiredFactors: user.requiredFactors,
      availableFactors: Object.keys(user.factors)
    };
  }

  /**
   * Verify a factor
   * @param {string} sessionId - Session ID
   * @param {string} factorType - Factor type
   * @param {Object} factorData - Factor data
   * @returns {Object} - Verification result
   */
  verifyFactor(sessionId, factorType, factorData) {
    // Check if session exists
    if (!this.sessions.has(sessionId)) {
      throw new Error(`Session ${sessionId} does not exist`);
    }
    
    // Get session
    const session = this.sessions.get(sessionId);
    
    // Check if session is expired
    if (session.expiresAt < Date.now()) {
      this.sessions.delete(sessionId);
      throw new Error(`Session ${sessionId} has expired`);
    }
    
    // Check if session is already completed
    if (session.status === 'completed') {
      throw new Error(`Session ${sessionId} is already completed`);
    }
    
    // Check if factor is already completed
    if (session.completedFactors.includes(factorType)) {
      throw new Error(`Factor ${factorType} is already completed for session ${sessionId}`);
    }
    
    // Get user
    const user = this.users.get(session.username);
    
    // Check if user has the factor
    if (!user.factors[factorType]) {
      throw new Error(`User ${session.username} does not have factor ${factorType}`);
    }
    
    let verified = false;
    
    // Check if factor provider exists
    if (this.factorProviders.has(factorType)) {
      // Get factor provider
      const provider = this.factorProviders.get(factorType);
      
      // Verify factor with provider
      verified = provider.verifyFactor(session.username, user.factors[factorType].id, factorData);
    } else {
      // Verify factor without provider
      verified = this._verifyFactorWithoutProvider(factorType, user.factors[factorType], factorData);
    }
    
    if (verified) {
      // Update factor last used
      user.factors[factorType].lastUsed = Date.now();
      
      // Update user
      this.users.set(session.username, user);
      
      // Update session
      session.completedFactors.push(factorType);
      
      // Check if all required factors are completed
      if (session.completedFactors.length >= session.requiredFactors) {
        session.status = 'completed';
        
        // Generate token
        const token = this._generateToken(session.username);
        
        if (this.options.enableLogging) {
          console.log(`Authentication completed for user ${session.username}`);
        }
        
        // Emit auth-completed event
        this.emit('auth-completed', { username: session.username, sessionId });
        
        return {
          verified: true,
          status: 'completed',
          token
        };
      }
      
      if (this.options.enableLogging) {
        console.log(`Factor ${factorType} verified for user ${session.username}`);
      }
      
      // Emit factor-verified event
      this.emit('factor-verified', { username: session.username, sessionId, factorType });
      
      return {
        verified: true,
        status: 'pending',
        completedFactors: session.completedFactors,
        remainingFactors: session.requiredFactors - session.completedFactors.length
      };
    } else {
      // Increment failed attempts
      this._incrementFailedAttempts(session.username);
      
      if (this.options.enableLogging) {
        console.log(`Factor ${factorType} verification failed for user ${session.username}`);
      }
      
      // Emit factor-failed event
      this.emit('factor-failed', { username: session.username, sessionId, factorType });
      
      return {
        verified: false,
        status: 'failed',
        error: 'Factor verification failed'
      };
    }
  }

  /**
   * Verify a token
   * @param {string} token - Token
   * @returns {Object} - Verification result
   */
  verifyToken(token) {
    // Check if token exists
    if (!this.tokens.has(token)) {
      return { verified: false, error: 'Invalid token' };
    }
    
    // Get token data
    const tokenData = this.tokens.get(token);
    
    // Check if token is expired
    if (tokenData.expiresAt < Date.now()) {
      this.tokens.delete(token);
      return { verified: false, error: 'Token expired' };
    }
    
    return {
      verified: true,
      username: tokenData.username,
      expiresAt: tokenData.expiresAt
    };
  }

  /**
   * Invalidate a token
   * @param {string} token - Token
   * @returns {boolean} - True if token was invalidated, false otherwise
   */
  invalidateToken(token) {
    // Check if token exists
    if (!this.tokens.has(token)) {
      return false;
    }
    
    // Get token data
    const tokenData = this.tokens.get(token);
    
    // Delete token
    this.tokens.delete(token);
    
    if (this.options.enableLogging) {
      console.log(`Token invalidated for user ${tokenData.username}`);
    }
    
    // Emit token-invalidated event
    this.emit('token-invalidated', { username: tokenData.username });
    
    return true;
  }

  /**
   * Check if account is locked
   * @param {string} username - Username
   * @returns {boolean} - True if account is locked, false otherwise
   */
  isAccountLocked(username) {
    // Check if account is locked
    if (this.lockedAccounts.has(username)) {
      const lockData = this.lockedAccounts.get(username);
      
      // Check if lock is expired
      if (lockData.expiresAt < Date.now()) {
        // Remove lock
        this.lockedAccounts.delete(username);
        
        // Reset failed attempts
        this.failedAttempts.delete(username);
        
        return false;
      }
      
      return true;
    }
    
    return false;
  }

  /**
   * Increment failed attempts
   * @param {string} username - Username
   * @private
   */
  _incrementFailedAttempts(username) {
    // Get failed attempts
    const attempts = this.failedAttempts.get(username) || 0;
    
    // Increment attempts
    const newAttempts = attempts + 1;
    
    // Update failed attempts
    this.failedAttempts.set(username, newAttempts);
    
    // Check if max attempts reached
    if (newAttempts >= this.options.maxFailedAttempts) {
      // Lock account
      this.lockedAccounts.set(username, {
        lockedAt: Date.now(),
        expiresAt: Date.now() + (this.options.lockoutDuration * 1000)
      });
      
      if (this.options.enableLogging) {
        console.log(`Account ${username} locked due to too many failed attempts`);
      }
      
      // Emit account-locked event
      this.emit('account-locked', { username });
    }
  }

  /**
   * Verify factor without provider
   * @param {string} factorType - Factor type
   * @param {Object} factorData - Factor data
   * @param {Object} inputData - Input data
   * @returns {boolean} - True if factor is verified, false otherwise
   * @private
   */
  _verifyFactorWithoutProvider(factorType, factorData, inputData) {
    switch (factorType) {
      case 'password':
        // Verify password
        return this._verifyPassword(factorData, inputData);
      
      case 'totp':
        // Verify TOTP
        return this._verifyTOTP(factorData, inputData);
      
      default:
        // Unsupported factor type
        return false;
    }
  }

  /**
   * Verify password
   * @param {Object} factorData - Factor data
   * @param {Object} inputData - Input data
   * @returns {boolean} - True if password is verified, false otherwise
   * @private
   */
  _verifyPassword(factorData, inputData) {
    // Check if password is provided
    if (!inputData.password) {
      return false;
    }
    
    // Check if hash and salt are available
    if (!factorData.hash || !factorData.salt) {
      return false;
    }
    
    // Hash input password
    const hash = crypto.pbkdf2Sync(
      inputData.password,
      factorData.salt,
      10000,
      64,
      'sha512'
    ).toString('hex');
    
    // Compare hashes
    return hash === factorData.hash;
  }

  /**
   * Verify TOTP
   * @param {Object} factorData - Factor data
   * @param {Object} inputData - Input data
   * @returns {boolean} - True if TOTP is verified, false otherwise
   * @private
   */
  _verifyTOTP(factorData, inputData) {
    // Check if TOTP code is provided
    if (!inputData.code) {
      return false;
    }
    
    // Check if secret is available
    if (!factorData.secret) {
      return false;
    }
    
    // In a real implementation, this would verify the TOTP code
    // For now, we'll just check if the code is 6 digits
    return /^\d{6}$/.test(inputData.code);
  }

  /**
   * Generate a token
   * @param {string} username - Username
   * @returns {string} - Generated token
   * @private
   */
  _generateToken(username) {
    // Generate token
    const token = uuidv4();
    
    // Create token data
    const tokenData = {
      username,
      createdAt: Date.now(),
      expiresAt: Date.now() + (this.options.tokenExpiration * 1000)
    };
    
    // Register token
    this.tokens.set(token, tokenData);
    
    return token;
  }

  /**
   * Dispose resources
   */
  dispose() {
    // Clear registries
    this.users.clear();
    this.sessions.clear();
    this.tokens.clear();
    this.failedAttempts.clear();
    this.lockedAccounts.clear();
    this.factorProviders.clear();
    
    if (this.options.enableLogging) {
      console.log('MFAService disposed');
    }
  }
}

/**
 * Create a multi-factor authentication service with recommended settings
 * @param {Object} options - Configuration options
 * @returns {MFAService} - Configured multi-factor authentication service
 */
function createMFAService(options = {}) {
  return new MFAService({
    enableLogging: true,
    tokenExpiration: 3600,
    maxFailedAttempts: 5,
    lockoutDuration: 900,
    requiredFactors: 2,
    supportedFactors: ['password', 'totp', 'email', 'sms', 'biometric'],
    ...options
  });
}

module.exports = {
  MFAService,
  createMFAService
};
