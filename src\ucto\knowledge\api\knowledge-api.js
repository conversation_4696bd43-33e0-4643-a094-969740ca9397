/**
 * Knowledge API for the Universal Compliance Tracking Optimizer.
 *
 * This module provides API endpoints for the UCTO Compliance Knowledge Base.
 */

const KnowledgeManager = require('../core/knowledge-manager');

/**
 * Knowledge API handler.
 */
class KnowledgeAPI {
  /**
   * Initialize the Knowledge API.
   * @param {Object} options - API options
   */
  constructor(options = {}) {
    console.log("Initializing Knowledge API");
    
    // Create a knowledge manager
    this.knowledgeManager = new KnowledgeManager(options);
    
    console.log("Knowledge API initialized");
  }
  
  /**
   * Get the knowledge schema.
   * @returns {Object} The knowledge schema
   */
  getSchema() {
    return this.knowledgeManager.getSchema();
  }
  
  /**
   * Get entity types.
   * @returns {Array} Entity types
   */
  getEntityTypes() {
    return this.knowledgeManager.getEntityTypes();
  }
  
  /**
   * Get relationship types.
   * @returns {Array} Relationship types
   */
  getRelationshipTypes() {
    return this.knowledgeManager.getRelationshipTypes();
  }
  
  /**
   * Create an entity.
   * @param {string} entityType - Type of entity
   * @param {Object} entityData - Entity data
   * @returns {Object} Created entity
   */
  createEntity(entityType, entityData) {
    return this.knowledgeManager.createEntity(entityType, entityData);
  }
  
  /**
   * Get an entity by ID.
   * @param {string} entityType - Type of entity
   * @param {string} entityId - Entity ID
   * @returns {Object} Entity
   */
  getEntity(entityType, entityId) {
    return this.knowledgeManager.getEntity(entityType, entityId);
  }
  
  /**
   * Get all entities of a type.
   * @param {string} entityType - Type of entity
   * @returns {Array} Entities
   */
  getEntities(entityType) {
    return this.knowledgeManager.getEntities(entityType);
  }
  
  /**
   * Update an entity.
   * @param {string} entityType - Type of entity
   * @param {string} entityId - Entity ID
   * @param {Object} updates - Updates to apply
   * @returns {Object} Updated entity
   */
  updateEntity(entityType, entityId, updates) {
    return this.knowledgeManager.updateEntity(entityType, entityId, updates);
  }
  
  /**
   * Delete an entity.
   * @param {string} entityType - Type of entity
   * @param {string} entityId - Entity ID
   * @returns {boolean} Success
   */
  deleteEntity(entityType, entityId) {
    return this.knowledgeManager.deleteEntity(entityType, entityId);
  }
  
  /**
   * Create a relationship between entities.
   * @param {string} relationshipType - Type of relationship
   * @param {string} sourceType - Type of source entity
   * @param {string} sourceId - ID of source entity
   * @param {string} targetType - Type of target entity
   * @param {string} targetId - ID of target entity
   * @param {Object} properties - Relationship properties
   * @returns {Object} Created relationship
   */
  createRelationship(relationshipType, sourceType, sourceId, targetType, targetId, properties = {}) {
    return this.knowledgeManager.createRelationship(relationshipType, sourceType, sourceId, targetType, targetId, properties);
  }
  
  /**
   * Get relationships for an entity.
   * @param {string} entityType - Type of entity
   * @param {string} entityId - Entity ID
   * @param {string} relationshipType - Type of relationship (optional)
   * @returns {Array} Relationships
   */
  getEntityRelationships(entityType, entityId, relationshipType = null) {
    return this.knowledgeManager.getEntityRelationships(entityType, entityId, relationshipType);
  }
  
  /**
   * Delete a relationship.
   * @param {string} relationshipType - Type of relationship
   * @param {string} relationshipId - Relationship ID
   * @returns {boolean} Success
   */
  deleteRelationship(relationshipType, relationshipId) {
    return this.knowledgeManager.deleteRelationship(relationshipType, relationshipId);
  }
  
  /**
   * Search the knowledge base.
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Array} Search results
   */
  search(query, options = {}) {
    return this.knowledgeManager.search(query, options);
  }
  
  /**
   * Get frameworks.
   * @returns {Array} Frameworks
   */
  getFrameworks() {
    return this.knowledgeManager.getEntities('framework');
  }
  
  /**
   * Get a framework by ID.
   * @param {string} frameworkId - Framework ID
   * @returns {Object} Framework
   */
  getFramework(frameworkId) {
    return this.knowledgeManager.getEntity('framework', frameworkId);
  }
  
  /**
   * Get controls for a framework.
   * @param {string} frameworkId - Framework ID
   * @returns {Array} Controls
   */
  getFrameworkControls(frameworkId) {
    const controls = this.knowledgeManager.getEntities('control');
    return controls.filter(control => control.framework_id === frameworkId);
  }
  
  /**
   * Get a control by ID.
   * @param {string} controlId - Control ID
   * @returns {Object} Control
   */
  getControl(controlId) {
    return this.knowledgeManager.getEntity('control', controlId);
  }
  
  /**
   * Get guidance for a control.
   * @param {string} controlId - Control ID
   * @returns {Array} Guidance
   */
  getControlGuidance(controlId) {
    const guidance = this.knowledgeManager.getEntities('guidance');
    return guidance.filter(g => g.control_id === controlId);
  }
  
  /**
   * Get resources for a control.
   * @param {string} controlId - Control ID
   * @returns {Array} Resources
   */
  getControlResources(controlId) {
    const resources = this.knowledgeManager.getEntities('resource');
    return resources.filter(r => r.control_id === controlId);
  }
  
  /**
   * Get related controls for a control.
   * @param {string} controlId - Control ID
   * @returns {Array} Related controls
   */
  getRelatedControls(controlId) {
    const relationships = this.knowledgeManager.getEntityRelationships('control', controlId, 'control_to_control');
    
    const relatedControls = [];
    
    for (const relationship of relationships) {
      let relatedControlId;
      
      if (relationship.sourceId === controlId) {
        relatedControlId = relationship.targetId;
      } else {
        relatedControlId = relationship.sourceId;
      }
      
      const relatedControl = this.knowledgeManager.getEntity('control', relatedControlId);
      
      if (relatedControl) {
        relatedControls.push({
          control: relatedControl,
          relationship: relationship.properties
        });
      }
    }
    
    return relatedControls;
  }
  
  /**
   * Get glossary terms.
   * @returns {Array} Glossary terms
   */
  getGlossaryTerms() {
    return this.knowledgeManager.getEntities('glossary_term');
  }
  
  /**
   * Get FAQs.
   * @returns {Array} FAQs
   */
  getFAQs() {
    return this.knowledgeManager.getEntities('faq');
  }
}

module.exports = KnowledgeAPI;
