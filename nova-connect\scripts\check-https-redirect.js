/**
 * HTTPS Redirect Check
 * 
 * This script checks for proper HTTPS redirect configuration in the application.
 * It analyzes the server.js and other relevant files to ensure that HTTPS redirect is properly configured.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Files to check
const filesToCheck = [
  path.join(__dirname, '..', 'server.js'),
  path.join(__dirname, '..', 'app.js'),
  path.join(__dirname, '..', 'index.js'),
  path.join(__dirname, '..', 'middleware', 'security.js')
];

// Results
const results = {
  httpsRedirectFound: false,
  helmetHstsFound: false,
  expressHttpsRedirectFound: false,
  manualHttpsRedirectFound: false,
  issues: []
};

// Check if express-sslify is installed
try {
  execSync('npm list express-sslify', { stdio: 'pipe' });
  results.expressHttpsRedirectFound = true;
} catch (error) {
  // Not installed, but that's okay if using another method
}

// Check files for HTTPS redirect configuration
for (const file of filesToCheck) {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    
    // Check for Helmet HSTS
    if (content.includes('helmet.hsts(') || 
        content.includes('hsts:') || 
        content.includes('hsts(')) {
      results.helmetHstsFound = true;
    }
    
    // Check for express-sslify
    if (content.includes('require(\'express-sslify\')') || 
        content.includes('require("express-sslify")') || 
        content.includes('from \'express-sslify\'') || 
        content.includes('from "express-sslify"')) {
      results.expressHttpsRedirectFound = true;
    }
    
    // Check for manual HTTPS redirect
    if ((content.includes('req.headers[\'x-forwarded-proto\']') || 
         content.includes('req.headers["x-forwarded-proto"]') || 
         content.includes('req.secure')) && 
        content.includes('redirect')) {
      results.manualHttpsRedirectFound = true;
    }
    
    // Overall HTTPS redirect check
    if (results.helmetHstsFound || results.expressHttpsRedirectFound || results.manualHttpsRedirectFound) {
      results.httpsRedirectFound = true;
    }
  }
}

// Generate report
let report = '# HTTPS Redirect Configuration Check\n\n';

if (!results.httpsRedirectFound) {
  report += '❌ HTTPS redirect is not configured\n';
} else {
  report += '✅ HTTPS redirect is configured\n';
}

if (results.helmetHstsFound) {
  report += '✅ Helmet HSTS is configured\n';
} else {
  report += '❌ Helmet HSTS is not configured\n';
}

if (results.expressHttpsRedirectFound) {
  report += '✅ express-sslify is configured\n';
} else {
  report += '❓ express-sslify is not used (may be using another method)\n';
}

if (results.manualHttpsRedirectFound) {
  report += '✅ Manual HTTPS redirect is configured\n';
} else if (!results.expressHttpsRedirectFound) {
  report += '❌ Manual HTTPS redirect is not configured\n';
}

if (results.issues.length > 0) {
  report += '\n## Issues\n\n';
  for (const issue of results.issues) {
    report += `- ${issue}\n`;
  }
}

// Recommended configuration
report += '\n## Recommended HTTPS Redirect Configuration\n\n';
report += '### Using Helmet HSTS\n\n';
report += '```javascript\nconst helmet = require(\'helmet\');\n\n';
report += 'app.use(\n';
report += '  helmet.hsts({\n';
report += '    maxAge: 31536000, // 1 year in seconds\n';
report += '    includeSubDomains: true,\n';
report += '    preload: true\n';
report += '  })\n';
report += ');\n```\n\n';

report += '### Using express-sslify\n\n';
report += '```javascript\nconst enforce = require(\'express-sslify\');\n\n';
report += '// Use in production only\n';
report += 'if (process.env.NODE_ENV === \'production\') {\n';
report += '  app.use(enforce.HTTPS({ trustProtoHeader: true }));\n';
report += '}\n```\n\n';

report += '### Manual HTTPS Redirect\n\n';
report += '```javascript\n// Redirect to HTTPS\n';
report += 'app.use((req, res, next) => {\n';
report += '  // For Heroku, Nginx, or other proxy setups\n';
report += '  if (req.headers[\'x-forwarded-proto\'] === \'http\') {\n';
report += '    return res.redirect(\'https://\' + req.hostname + req.url);\n';
report += '  }\n\n';
report += '  // For direct HTTPS support\n';
report += '  if (!req.secure) {\n';
report += '    return res.redirect(\'https://\' + req.hostname + req.url);\n';
report += '  }\n\n';
report += '  next();\n';
report += '});\n```\n';

// Additional recommendations
report += '\n## Additional Recommendations\n\n';
report += '1. **Environment-Specific Configuration**: Only enforce HTTPS in production environments.\n\n';
report += '```javascript\nif (process.env.NODE_ENV === \'production\') {\n';
report += '  // HTTPS enforcement code here\n';
report += '}\n```\n\n';

report += '2. **Proxy Awareness**: Be aware of your deployment environment. Different environments may require different configurations:\n\n';
report += '   - **Heroku**: Use `trustProtoHeader: true` with express-sslify\n';
report += '   - **AWS with ELB**: Use `trustProtoHeader: true` with express-sslify\n';
report += '   - **Nginx**: Use `trustProtoHeader: true` with express-sslify\n';
report += '   - **Azure**: Use `trustAzureHeader: true` with express-sslify\n\n';

report += '3. **Combine with Helmet**: Use Helmet\'s HSTS middleware in conjunction with HTTPS redirection for comprehensive security.\n\n';

report += '4. **Testing**: Test your HTTPS redirection in a staging environment that mimics your production setup.\n\n';

report += '5. **HSTS Preload List**: Consider submitting your domain to the HSTS preload list (https://hstspreload.org/) for maximum security.\n';

// Output report
console.log(report);

// Exit with appropriate code
if (!results.httpsRedirectFound) {
  process.exit(1);
} else {
  process.exit(0);
}
