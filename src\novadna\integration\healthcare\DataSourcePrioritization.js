/**
 * DataSourcePrioritization.js
 * 
 * This module provides data source prioritization logic for emergency medical data.
 * It helps determine which data sources to use and in what order.
 */

/**
 * DataSourcePrioritization class for prioritizing data sources
 */
class DataSourcePrioritization {
  constructor(options = {}) {
    this.defaultPriorities = options.defaultPriorities || this._getDefaultPriorities();
    this.customPriorities = options.customPriorities || {};
    this.dataSourceRatings = new Map();
  }

  /**
   * Prioritize data sources for a specific emergency context
   * @param {Array} dataSources - The available data sources
   * @param {Object} context - The emergency context
   * @returns {Array} - The prioritized data sources
   */
  prioritizeDataSources(dataSources, context = {}) {
    if (!dataSources || !Array.isArray(dataSources) || dataSources.length === 0) {
      return [];
    }
    
    // Get priorities for this context
    const priorities = this._getPrioritiesForContext(context);
    
    // Score each data source
    const scoredSources = dataSources.map(source => {
      const score = this._calculateSourceScore(source, priorities, context);
      
      return {
        source,
        score
      };
    });
    
    // Sort by score (highest first)
    scoredSources.sort((a, b) => b.score - a.score);
    
    // Return prioritized sources
    return scoredSources.map(item => item.source);
  }

  /**
   * Rate a data source based on quality and reliability
   * @param {String} sourceId - The data source ID
   * @param {Object} rating - The rating information
   * @returns {Boolean} - Whether the rating was stored successfully
   */
  rateDataSource(sourceId, rating) {
    if (!sourceId || !rating || typeof rating.score !== 'number') {
      return false;
    }
    
    // Validate score
    if (rating.score < 0 || rating.score > 10) {
      return false;
    }
    
    // Get existing rating or create new one
    const existingRating = this.dataSourceRatings.get(sourceId) || {
      totalScore: 0,
      count: 0,
      ratings: []
    };
    
    // Add new rating
    existingRating.totalScore += rating.score;
    existingRating.count += 1;
    existingRating.ratings.push({
      score: rating.score,
      reason: rating.reason,
      timestamp: new Date().toISOString()
    });
    
    // Calculate average
    existingRating.averageScore = existingRating.totalScore / existingRating.count;
    
    // Store rating
    this.dataSourceRatings.set(sourceId, existingRating);
    
    return true;
  }

  /**
   * Get data source ratings
   * @returns {Object} - The data source ratings
   */
  getDataSourceRatings() {
    const ratings = {};
    
    for (const [sourceId, rating] of this.dataSourceRatings.entries()) {
      ratings[sourceId] = {
        averageScore: rating.averageScore,
        count: rating.count,
        lastRated: rating.ratings[rating.ratings.length - 1].timestamp
      };
    }
    
    return ratings;
  }

  /**
   * Set custom priorities for a specific context
   * @param {String} contextType - The context type
   * @param {Object} priorities - The priorities
   * @returns {Boolean} - Whether the priorities were set successfully
   */
  setCustomPriorities(contextType, priorities) {
    if (!contextType || !priorities) {
      return false;
    }
    
    this.customPriorities[contextType] = priorities;
    return true;
  }

  /**
   * Get default priorities
   * @returns {Object} - The default priorities
   * @private
   */
  _getDefaultPriorities() {
    return {
      // Default priorities for all contexts
      DEFAULT: {
        sourceTypes: {
          EHR: 10,
          HIE: 8,
          PERSONAL: 6,
          INSURANCE: 4
        },
        providers: {
          Epic: 10,
          Cerner: 9,
          Allscripts: 8,
          Meditech: 7,
          athenahealth: 7
        },
        dataTypes: {
          demographics: 10,
          allergies: 9,
          medications: 9,
          conditions: 8,
          advancedDirectives: 8,
          contacts: 7,
          insurance: 5
        }
      },
      
      // Priorities for cardiac emergencies
      CARDIAC: {
        sourceTypes: {
          EHR: 10,
          HIE: 8,
          PERSONAL: 6,
          INSURANCE: 4
        },
        providers: {
          Epic: 10,
          Cerner: 9,
          Allscripts: 8,
          Meditech: 7,
          athenahealth: 7
        },
        dataTypes: {
          demographics: 10,
          allergies: 9,
          medications: 10, // Higher priority for medications
          conditions: 10,  // Higher priority for conditions
          cardiology: 10,  // Highest priority for cardiology data
          vitals: 9,
          procedures: 8,
          implants: 9,
          advancedDirectives: 8,
          contacts: 7,
          insurance: 5
        }
      },
      
      // Priorities for trauma emergencies
      TRAUMA: {
        sourceTypes: {
          EHR: 10,
          HIE: 8,
          PERSONAL: 6,
          INSURANCE: 4
        },
        providers: {
          Epic: 10,
          Cerner: 9,
          Allscripts: 8,
          Meditech: 7,
          athenahealth: 7
        },
        dataTypes: {
          demographics: 10,
          allergies: 9,
          bloodType: 10,    // Highest priority for blood type
          medications: 9,
          conditions: 8,
          imaging: 9,       // Higher priority for imaging
          surgeries: 9,     // Higher priority for surgeries
          advancedDirectives: 8,
          contacts: 7,
          insurance: 5
        }
      },
      
      // Priorities for allergic emergencies
      ALLERGIC: {
        sourceTypes: {
          EHR: 10,
          HIE: 8,
          PERSONAL: 6,
          INSURANCE: 4
        },
        providers: {
          Epic: 10,
          Cerner: 9,
          Allscripts: 8,
          Meditech: 7,
          athenahealth: 7
        },
        dataTypes: {
          demographics: 10,
          allergies: 10,     // Highest priority for allergies
          immunology: 10,    // Highest priority for immunology
          medications: 9,
          conditions: 8,
          vitals: 9,
          advancedDirectives: 8,
          contacts: 7,
          insurance: 5
        }
      }
    };
  }

  /**
   * Get priorities for a specific context
   * @param {Object} context - The emergency context
   * @returns {Object} - The priorities
   * @private
   */
  _getPrioritiesForContext(context) {
    const emergencyType = context.emergencyType || 'DEFAULT';
    
    // Check if we have custom priorities for this context
    if (this.customPriorities[emergencyType]) {
      return {
        ...this.defaultPriorities.DEFAULT,
        ...this.customPriorities[emergencyType]
      };
    }
    
    // Check if we have default priorities for this context
    if (this.defaultPriorities[emergencyType]) {
      return this.defaultPriorities[emergencyType];
    }
    
    // Use default priorities
    return this.defaultPriorities.DEFAULT;
  }

  /**
   * Calculate score for a data source
   * @param {Object} source - The data source
   * @param {Object} priorities - The priorities
   * @param {Object} context - The emergency context
   * @returns {Number} - The calculated score
   * @private
   */
  _calculateSourceScore(source, priorities, context) {
    let score = 0;
    
    // Score based on source type
    if (source.type && priorities.sourceTypes[source.type]) {
      score += priorities.sourceTypes[source.type];
    }
    
    // Score based on provider
    if (source.provider && priorities.providers[source.provider]) {
      score += priorities.providers[source.provider];
    }
    
    // Score based on available data types
    if (source.availableDataTypes && Array.isArray(source.availableDataTypes)) {
      for (const dataType of source.availableDataTypes) {
        if (priorities.dataTypes[dataType]) {
          score += priorities.dataTypes[dataType];
        }
      }
    }
    
    // Score based on freshness
    if (source.lastUpdated) {
      const ageInHours = (Date.now() - new Date(source.lastUpdated).getTime()) / (1000 * 60 * 60);
      
      if (ageInHours < 24) {
        score += 10; // Data less than 24 hours old
      } else if (ageInHours < 72) {
        score += 8;  // Data less than 3 days old
      } else if (ageInHours < 168) {
        score += 5;  // Data less than 7 days old
      } else if (ageInHours < 720) {
        score += 3;  // Data less than 30 days old
      } else {
        score += 1;  // Data more than 30 days old
      }
    }
    
    // Score based on previous ratings
    const rating = this.dataSourceRatings.get(source.id);
    if (rating) {
      score += rating.averageScore;
    }
    
    // Score based on emergency severity
    if (context.emergencySeverity === 'CRITICAL') {
      // For critical emergencies, prioritize speed and availability
      if (source.responseTime && source.responseTime < 1000) {
        score += 10; // Fast response time
      }
      
      if (source.availability && source.availability > 0.99) {
        score += 10; // High availability
      }
    }
    
    return score;
  }
}

module.exports = DataSourcePrioritization;
