/**
 * NovaFuse Universal API Connector - Logger Tests
 */

const winston = require('winston');
const { createLogger, defaultLogger } = require('../../src/utils/logger');

// Mock winston
jest.mock('winston', () => {
  const mockFormat = {
    combine: jest.fn().mockReturnThis(),
    timestamp: jest.fn().mockReturnThis(),
    errors: jest.fn().mockReturnThis(),
    splat: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    colorize: jest.fn().mockReturnThis(),
    printf: jest.fn().mockReturnThis()
  };
  
  const mockLogger = {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    child: jest.fn().mockReturnThis(),
    add: jest.fn()
  };
  
  return {
    format: mockFormat,
    createLogger: jest.fn().mockReturnValue(mockLogger),
    transports: {
      Console: jest.fn(),
      File: jest.fn()
    }
  };
});

describe('Logger', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('should create a logger', () => {
    const logger = createLogger('test-module');
    
    expect(winston.createLogger).toHaveBeenCalled();
    expect(logger).toBeDefined();
  });
  
  it('should create a child logger with module name', () => {
    const mockChildLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    };
    
    defaultLogger.child = jest.fn().mockReturnValue(mockChildLogger);
    
    const logger = createLogger('test-module');
    
    expect(defaultLogger.child).toHaveBeenCalledWith({ module: 'test-module' });
    expect(logger).toEqual(mockChildLogger);
  });
  
  it('should log messages with different levels', () => {
    const mockChildLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    };
    
    defaultLogger.child = jest.fn().mockReturnValue(mockChildLogger);
    
    const logger = createLogger('test-module');
    
    logger.info('Info message');
    logger.error('Error message');
    logger.warn('Warning message');
    logger.debug('Debug message');
    
    expect(mockChildLogger.info).toHaveBeenCalledWith('Info message');
    expect(mockChildLogger.error).toHaveBeenCalledWith('Error message');
    expect(mockChildLogger.warn).toHaveBeenCalledWith('Warning message');
    expect(mockChildLogger.debug).toHaveBeenCalledWith('Debug message');
  });
  
  it('should add file transports in production environment', () => {
    const originalNodeEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';
    
    // Re-require the logger to trigger the production code path
    jest.resetModules();
    jest.mock('winston', () => {
      const mockFormat = {
        combine: jest.fn().mockReturnThis(),
        timestamp: jest.fn().mockReturnThis(),
        errors: jest.fn().mockReturnThis(),
        splat: jest.fn().mockReturnThis(),
        json: jest.fn().mockReturnThis(),
        colorize: jest.fn().mockReturnThis(),
        printf: jest.fn().mockReturnThis()
      };
      
      const mockLogger = {
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
        child: jest.fn().mockReturnThis(),
        add: jest.fn()
      };
      
      return {
        format: mockFormat,
        createLogger: jest.fn().mockReturnValue(mockLogger),
        transports: {
          Console: jest.fn(),
          File: jest.fn()
        }
      };
    });
    
    const logger = require('../../src/utils/logger');
    
    expect(winston.transports.File).toHaveBeenCalledTimes(2);
    
    // Restore environment
    process.env.NODE_ENV = originalNodeEnv;
  });
});
