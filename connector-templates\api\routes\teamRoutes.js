/**
 * Team Routes
 */

const express = require('express');
const router = express.Router();
const TeamController = require('../controllers/TeamController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticate);

// Get all teams (admin only)
router.get('/', hasPermission('team:read'), (req, res, next) => {
  TeamController.getAllTeams(req, res, next);
});

// Get teams for current user
router.get('/my', (req, res, next) => {
  TeamController.getMyTeams(req, res, next);
});

// Create a new team
router.post('/', hasPermission('team:create'), (req, res, next) => {
  TeamController.createTeam(req, res, next);
});

// Get team by ID
router.get('/:id', (req, res, next) => {
  TeamController.getTeamById(req, res, next);
});

// Update a team
router.put('/:id', (req, res, next) => {
  TeamController.updateTeam(req, res, next);
});

// Delete a team
router.delete('/:id', (req, res, next) => {
  TeamController.deleteTeam(req, res, next);
});

// Get team members
router.get('/:id/members', (req, res, next) => {
  TeamController.getTeamMembers(req, res, next);
});

// Add team member
router.post('/:id/members', (req, res, next) => {
  TeamController.addTeamMember(req, res, next);
});

// Update team member
router.put('/:id/members/:memberId', (req, res, next) => {
  TeamController.updateTeamMember(req, res, next);
});

// Remove team member
router.delete('/:id/members/:memberId', (req, res, next) => {
  TeamController.removeTeamMember(req, res, next);
});

// Get team invitations
router.get('/:id/invitations', (req, res, next) => {
  TeamController.getTeamInvitations(req, res, next);
});

// Create team invitation
router.post('/:id/invitations', hasPermission('team:invite'), (req, res, next) => {
  TeamController.createTeamInvitation(req, res, next);
});

// Cancel team invitation
router.delete('/:id/invitations/:invitationId', (req, res, next) => {
  TeamController.cancelTeamInvitation(req, res, next);
});

// Get my invitations
router.get('/invitations/my', (req, res, next) => {
  TeamController.getMyInvitations(req, res, next);
});

// Accept team invitation
router.post('/invitations/:id/accept', (req, res, next) => {
  TeamController.acceptTeamInvitation(req, res, next);
});

// Decline team invitation
router.post('/invitations/:id/decline', (req, res, next) => {
  TeamController.declineTeamInvitation(req, res, next);
});

module.exports = router;
