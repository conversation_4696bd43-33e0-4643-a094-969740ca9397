"""
Evidence utilities for the Universal Compliance Evidence Collection System.

This module provides utility functions for working with compliance evidence.
"""

import json
import logging
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_evidence(file_path: str) -> Dict[str, Any]:
    """
    Load evidence from a JSON file.
    
    Args:
        file_path: Path to the JSON file
        
    Returns:
        The evidence
        
    Raises:
        FileNotFoundError: If the file does not exist
        json.JSONDecodeError: If the file is not valid JSON
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            evidence = json.load(f)
        
        logger.info(f"Loaded evidence from {file_path}")
        return evidence
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        raise
    except json.JSONDecodeError:
        logger.error(f"Invalid <PERSON>SO<PERSON> in file: {file_path}")
        raise

def save_evidence(evidence: Dict[str, Any], file_path: str) -> None:
    """
    Save evidence to a JSON file.
    
    Args:
        evidence: The evidence
        file_path: Path to the output JSON file
        
    Raises:
        IOError: If the file cannot be written
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(evidence, f, indent=2)
        
        logger.info(f"Saved evidence to {file_path}")
    except IOError:
        logger.error(f"Failed to save evidence to {file_path}")
        raise

def validate_evidence_schema(evidence: Dict[str, Any]) -> List[str]:
    """
    Validate the schema of evidence.
    
    Args:
        evidence: The evidence to validate
        
    Returns:
        List of validation errors, empty if valid
    """
    errors = []
    
    # Check required fields
    required_fields = ['id', 'type', 'data']
    for field in required_fields:
        if field not in evidence:
            errors.append(f"Missing required field: {field}")
    
    # If any required fields are missing, return early
    if errors:
        return errors
    
    # Check that data is a dictionary
    if not isinstance(evidence['data'], dict):
        errors.append(f"Data must be a dictionary in evidence: {evidence['id']}")
    
    return errors

def filter_sensitive_data(evidence: Dict[str, Any], sensitive_fields: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Filter sensitive data from evidence.
    
    Args:
        evidence: The evidence to filter
        sensitive_fields: List of sensitive field names to remove
        
    Returns:
        The filtered evidence
    """
    if sensitive_fields is None:
        sensitive_fields = ['credentials', 'password', 'secret', 'key', 'token']
    
    filtered_evidence = evidence.copy()
    
    # Filter sensitive fields from the data
    if 'data' in filtered_evidence and isinstance(filtered_evidence['data'], dict):
        for field in sensitive_fields:
            if field in filtered_evidence['data']:
                filtered_evidence['data'][field] = '***REDACTED***'
    
    # Filter sensitive fields from the metadata
    if 'metadata' in filtered_evidence and isinstance(filtered_evidence['metadata'], dict):
        for field in sensitive_fields:
            if field in filtered_evidence['metadata']:
                filtered_evidence['metadata'][field] = '***REDACTED***'
    
    return filtered_evidence

def merge_evidence(base_evidence: Dict[str, Any], overlay_evidence: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge two evidence dictionaries, with overlay_evidence taking precedence.
    
    Args:
        base_evidence: The base evidence
        overlay_evidence: The overlay evidence
        
    Returns:
        The merged evidence
    """
    merged_evidence = base_evidence.copy()
    
    for key, value in overlay_evidence.items():
        if key in merged_evidence and isinstance(merged_evidence[key], dict) and isinstance(value, dict):
            merged_evidence[key] = merge_evidence(merged_evidence[key], value)
        else:
            merged_evidence[key] = value
    
    return merged_evidence

def get_evidence_summary(evidence: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get a summary of evidence.
    
    Args:
        evidence: The evidence
        
    Returns:
        The evidence summary
    """
    summary = {
        'id': evidence.get('id'),
        'type': evidence.get('type'),
        'status': evidence.get('status'),
        'created_at': evidence.get('created_at'),
        'updated_at': evidence.get('updated_at')
    }
    
    # Add validation results if available
    if 'validation_results' in evidence and evidence['validation_results']:
        summary['is_valid'] = evidence['validation_results'].get('is_valid', False)
    
    # Add storage location if available
    if 'storage_location' in evidence and evidence['storage_location']:
        summary['storage_location'] = evidence['storage_location']
    
    return summary

def get_evidence_by_type(evidence_list: List[Dict[str, Any]], evidence_type: str) -> List[Dict[str, Any]]:
    """
    Get evidence by type.
    
    Args:
        evidence_list: List of evidence
        evidence_type: The type of evidence to filter
        
    Returns:
        List of evidence of the specified type
    """
    return [e for e in evidence_list if e.get('type') == evidence_type]

def get_evidence_by_status(evidence_list: List[Dict[str, Any]], status: str) -> List[Dict[str, Any]]:
    """
    Get evidence by status.
    
    Args:
        evidence_list: List of evidence
        status: The status of evidence to filter
        
    Returns:
        List of evidence with the specified status
    """
    return [e for e in evidence_list if e.get('status') == status]

def get_evidence_by_validation(evidence_list: List[Dict[str, Any]], is_valid: bool) -> List[Dict[str, Any]]:
    """
    Get evidence by validation status.
    
    Args:
        evidence_list: List of evidence
        is_valid: Whether the evidence is valid
        
    Returns:
        List of evidence with the specified validation status
    """
    return [
        e for e in evidence_list
        if 'validation_results' in e and e['validation_results'] and e['validation_results'].get('is_valid') == is_valid
    ]
