/**
 * Self-Healing Feedback Loop Example
 *
 * This example demonstrates how self-healing tensors can reduce entropy
 * containment through repeated feedback loops and healing cycles.
 */

const SelfHealingTensor = require('../src/quantum/self-healing-tensor');
const DomainTransitionMapper = require('../src/quantum/domain-transition-mapper');
const TimeDriftAnalyzer = require('../src/quantum/time-drift-analyzer');
const TensorHealthDashboard = require('../dashboard/tensor-health-dashboard');
const fs = require('fs');
const path = require('path');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../feedback_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Run self-healing feedback loop experiment
 */
function runSelfHealingExperiment() {
  console.log('=== Self-Healing Feedback Loop Experiment ===\n');

  // Create self-healing tensor manager with 3-6-9-12-13 resonance pattern
  const selfHealer = new SelfHealingTensor({
    healingThreshold: 0.6, // Aligned with 3-6-9 pattern (0.6)
    healingFactor: 0.6, // Aligned with 3-6-9 pattern (0.6)
    maxHealingCycles: 12, // Aligned with 3-6-9-12 pattern (12)
    autoHeal: false, // We'll control healing manually
    entropyContainmentTarget: 0.03, // Aligned with 3-6-9 pattern (0.03)
    learningRate: 0.03, // Aligned with 3-6-9 pattern (0.03)
    targetEffectiveness: 0.6, // Aligned with 3-6-9 pattern (0.6)
    adaptiveThreshold: true,
    resonanceLock: true // Enable resonance lock for optimal healing
  });

  // Create domain mapper
  const domainMapper = new DomainTransitionMapper();

  // Create time drift analyzer
  const timeDriftAnalyzer = new TimeDriftAnalyzer();

  // Register event listeners
  selfHealer.on('tensor-healed', (data) => {
    console.log(`Tensor healed: cycle=${data.healingCycle}, health improvement=${data.healthImprovement.toFixed(3)}, entropy reduction=${data.entropyReduction.toFixed(3)}`);
  });

  // Add listener for resonance optimization events
  selfHealer.on('resonance-optimization', (data) => {
    console.log(`🔮 Resonance optimization (${data.type}): ${data.message}`);
  });

  domainMapper.on('tensor-mapped', (data) => {
    console.log(`Tensor mapped: ${data.fromDomain} -> ${data.toDomain}, consistency=${data.consistency.toFixed(3)}`);
  });

  timeDriftAnalyzer.on('time-drift-detected', (data) => {
    console.log(`Time drift detected: factor=${data.driftFactor.toFixed(3)}, compensated=${data.driftCompensated}`);
  });

  // Create initial tensor
  const tensorId = 'test-tensor';
  const initialTensor = {
    dimensions: [3],
    values: [0.7, 0.8, 0.9]
  };

  // Register tensor with self-healer
  console.log('Registering tensor with self-healer...');
  const selfHealingTensor = selfHealer.registerTensor(tensorId, initialTensor, 'cyber');
  console.log('Initial tensor:', selfHealingTensor);

  // Register tensor with domain mapper
  console.log('\nRegistering tensor with domain mapper...');
  domainMapper.registerTensor(tensorId, initialTensor, 'cyber');

  // Register tensor with time drift analyzer
  console.log('\nRegistering tensor with time drift analyzer...');
  timeDriftAnalyzer.registerTensor(tensorId, initialTensor, 'cyber');

  // Create dashboard
  console.log('\nCreating tensor health dashboard...');
  const dashboard = new TensorHealthDashboard({
    updateInterval: 5000,
    dashboardDir: path.join(__dirname, '../dashboard_output')
  });

  // Register tensor with dashboard
  dashboard.registerTensor(tensorId, selfHealingTensor, selfHealer, timeDriftAnalyzer);
  console.log(`Dashboard created at: ${path.join(__dirname, '../dashboard_output', `tensor-dashboard-${tensorId}.html`)}`);

  // Experiment results
  const experimentResults = {
    initialState: {
      health: selfHealingTensor.health,
      entropyContainment: selfHealingTensor.entropyContainment,
      values: [...selfHealingTensor.values]
    },
    feedbackLoops: [],
    finalState: null
  };

  // Run feedback loops
  console.log('\n=== Running Feedback Loops ===');
  const numLoops = 5;
  const damagePerLoop = 0.2;

  for (let i = 0; i < numLoops; i++) {
    console.log(`\n--- Feedback Loop ${i + 1} ---`);

    // Step 1: Apply damage to tensor
    console.log('Applying damage...');
    const damagedTensor = selfHealer.damageTensor(tensorId, damagePerLoop);
    console.log(`Damaged tensor: health=${damagedTensor.health.toFixed(3)}, entropy=${damagedTensor.entropyContainment.toFixed(3)}`);

    // Step 2: Map to financial domain
    console.log('\nMapping to financial domain...');
    const financialTensor = domainMapper.mapToDomain(tensorId, 'financial');
    console.log(`Financial domain tensor: domain=${financialTensor.domain}`);

    // Step 3: Simulate time drift with cycle count for warm-start
    console.log('\nSimulating time drift...');
    const futureTime = Date.now() + (3600000 * (i + 1)); // i+1 hours in the future
    const driftResult = timeDriftAnalyzer.simulateTimeDrift(tensorId, futureTime, i);
    console.log(`Drift result: detected=${driftResult.driftDetected}, compensated=${driftResult.driftCompensated}, factor=${driftResult.driftFactor.toFixed(3)}`);

    // Step 4: Map back to cyber domain
    console.log('\nMapping back to cyber domain...');
    const cyberTensor = domainMapper.mapToDomain(tensorId, 'cyber');
    console.log(`Cyber domain tensor: domain=${cyberTensor.domain}`);

    // Apply entropy decay with resonance-optimized decay rate
    if (i > 0 && i % 3 === 0) { // Apply decay every 3 loops (aligned with 3-6-9 pattern)
      console.log('\nApplying entropy decay with resonance-optimized rate...');
      // Pass null to use the resonance-optimized decay rate
      const decayResult = selfHealer.applyEntropyDecay(tensorId, null);
      if (decayResult.decayApplied) {
        console.log(`Entropy decay applied: ${decayResult.entropyBefore.toFixed(3)} -> ${decayResult.entropyAfter.toFixed(3)}, reduction=${decayResult.entropyReduction.toFixed(3)}`);
      } else {
        console.log(`Entropy decay not applied: ${decayResult.reason}`);
      }
    }

    // Step 5: Perform healing cycles with resonance-optimized cycle count based on damage level
    console.log('\nPerforming healing cycles...');
    console.log(`Using damage level ${damagePerLoop} to determine optimal healing cycles (3-6-9-12 pattern)`);

    // Pass damage level directly and set isDamageLevel=true to use resonance optimization
    const healingResult = selfHealer.performRepeatedHealing(tensorId, damagePerLoop, true);
    console.log(`Healing result: cycles attempted=${healingResult.cyclesAttempted}, cycles performed=${healingResult.cyclesPerformed}, initial entropy=${healingResult.initialEntropyContainment.toFixed(3)}, final entropy=${healingResult.finalEntropyContainment.toFixed(3)}`);
    console.log(`Outcome category: ${healingResult.outcome.category}, effectiveness: ${healingResult.outcome.effectiveness.overall.toFixed(3)}`);

    // Log healing diagnostics
    console.log('\nHealing diagnostics:');
    healingResult.diagnostics.forEach(diagnostic => {
      console.log(`  Cycle ${diagnostic.cycle}: ${diagnostic.attempted ? 'Attempted' : 'Not attempted'}, ${diagnostic.performed ? 'Performed' : 'Not performed'}`);
      console.log(`    Reason: ${diagnostic.reason || 'N/A'}`);
      if (diagnostic.improvement) {
        console.log(`    Improvement: health=${diagnostic.improvement.health.toFixed(3)}, entropy=${diagnostic.improvement.entropy.toFixed(3)}`);
      }
      if (diagnostic.thresholdDetails) {
        console.log(`    Threshold: base=${diagnostic.thresholdDetails.baseThreshold.toFixed(3)}, entropy-weighted=${diagnostic.thresholdDetails.entropyWeightedThreshold.toFixed(3)}, factor=${diagnostic.thresholdDetails.entropyFactor.toFixed(3)}`);
      }
      if (diagnostic.efficiencyScore) {
        console.log(`    Efficiency: health=${diagnostic.efficiencyScore.health.toFixed(3)}, entropy=${diagnostic.efficiencyScore.entropy.toFixed(3)}, combined=${diagnostic.efficiencyScore.combined.toFixed(3)}`);
      }
    });

    // Store loop results
    experimentResults.feedbackLoops.push({
      loop: i + 1,
      damageLevel: damagePerLoop,
      afterDamage: {
        health: damagedTensor.health,
        entropyContainment: damagedTensor.entropyContainment
      },
      afterDomainTransition: {
        consistency: domainMapper.validateMapping(tensorId).consistency
      },
      afterTimeDrift: {
        driftFactor: driftResult.driftFactor,
        driftCompensated: driftResult.driftCompensated
      },
      afterHealing: {
        health: healingResult.finalHealth,
        entropyContainment: healingResult.finalEntropyContainment,
        cyclesAttempted: healingResult.cyclesAttempted,
        cyclesPerformed: healingResult.cyclesPerformed,
        outcomeCategory: healingResult.outcome.category,
        effectiveness: healingResult.outcome.effectiveness.overall,
        diagnostics: healingResult.diagnostics
      }
    });
  }

  // Get final state
  const finalTensor = selfHealer.getTensor(tensorId);
  experimentResults.finalState = {
    health: finalTensor.health,
    entropyContainment: finalTensor.entropyContainment,
    values: [...finalTensor.values],
    healingCycles: finalTensor.healingCycles
  };

  // Get entropy forecast
  console.log('\nGenerating entropy forecast...');
  try {
    const forecast = selfHealer.forecastEntropy(tensorId);
    console.log(`Entropy forecast: ${forecast.forecastedEntropy.toFixed(3)} (${forecast.trend}, confidence: ${(forecast.confidence * 100).toFixed(1)}%)`);

    if (forecast.forecastedEntropy > 0.03) {
      console.log(`WARNING: Forecasted entropy exceeds recommended threshold (0.03). Consider additional entropy decay.`);
    }
  } catch (error) {
    console.log(`Entropy forecast not available: ${error.message}`);
  }

  // Get threshold adjustment history
  console.log('\nThreshold adjustment history:');
  const thresholdHistory = selfHealer.getThresholdHistory(tensorId);
  if (thresholdHistory.length > 0) {
    console.log(`Initial threshold: ${thresholdHistory[0].oldThreshold.toFixed(3)}`);
    console.log(`Final threshold: ${thresholdHistory[thresholdHistory.length - 1].newThreshold.toFixed(3)}`);
    console.log(`Adjustments: ${thresholdHistory.length}`);
  } else {
    console.log('No threshold adjustments made.');
  }

  // Print summary
  console.log('\n=== Experiment Summary ===');
  console.log(`Initial health: ${experimentResults.initialState.health.toFixed(3)}`);
  console.log(`Final health: ${experimentResults.finalState.health.toFixed(3)}`);
  console.log(`Initial entropy containment: ${experimentResults.initialState.entropyContainment.toFixed(3)}`);
  console.log(`Final entropy containment: ${experimentResults.finalState.entropyContainment.toFixed(3)}`);
  console.log(`Total healing cycles: ${experimentResults.finalState.healingCycles}`);

  // Open dashboard
  console.log(`\nDashboard available at: ${path.join(__dirname, '../dashboard_output', `tensor-dashboard-${tensorId}.html`)}`);
  console.log('Please open the dashboard in your browser to view real-time metrics.');

  // Generate HTML report
  generateReport(experimentResults);

  return experimentResults;
}

/**
 * Generate HTML report
 * @param {Object} results - Experiment results
 */
function generateReport(results) {
  const reportPath = path.join(RESULTS_DIR, 'self-healing-feedback-report.html');

  // Generate HTML
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Self-Healing Feedback Loop Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .summary-box {
      background-color: #fff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      flex: 1;
      margin: 0 10px;
      text-align: center;
    }
    .summary-box h3 {
      margin-top: 0;
    }
    .summary-box .value {
      font-size: 36px;
      font-weight: bold;
      margin: 10px 0;
    }
    .chart-container {
      background-color: #fff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
    }
    .feedback-loop {
      background-color: #fff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
    }
    .feedback-loop h3 {
      margin-top: 0;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .metrics {
      display: flex;
      flex-wrap: wrap;
    }
    .metric {
      flex: 1;
      min-width: 200px;
      margin: 10px;
      padding: 15px;
      background-color: #f9f9f9;
      border-radius: 5px;
    }
    .metric h4 {
      margin-top: 0;
      color: #555;
    }
    .metric .value {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }
    .chart {
      width: 100%;
      height: 300px;
      margin-top: 20px;
    }
    .resonance-info {
      background-color: #f0f8ff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      border-left: 5px solid #0066cc;
    }
    .resonance-info h2 {
      color: #0066cc;
      margin-top: 0;
    }
    .resonance-info ul {
      margin-bottom: 0;
    }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <h1>Self-Healing Feedback Loop Report</h1>
  <p>Generated: ${new Date().toLocaleString()}</p>
  <p><strong>🔮 Optimized with 3-6-9-12-13 Resonance Pattern</strong></p>

  <div class="summary">
    <div class="summary-box">
      <h3>Initial Health</h3>
      <div class="value">${results.initialState.health.toFixed(3)}</div>
    </div>
    <div class="summary-box">
      <h3>Final Health</h3>
      <div class="value">${results.finalState.health.toFixed(3)}</div>
    </div>
    <div class="summary-box">
      <h3>Initial Entropy</h3>
      <div class="value">${results.initialState.entropyContainment.toFixed(3)}</div>
    </div>
    <div class="summary-box">
      <h3>Final Entropy</h3>
      <div class="value">${results.finalState.entropyContainment.toFixed(3)}</div>
    </div>
    <div class="summary-box">
      <h3>Healing Cycles</h3>
      <div class="value">${results.finalState.healingCycles}</div>
    </div>
  </div>

  <div class="resonance-info">
    <h2>3-6-9-12-13 Resonance Pattern</h2>
    <p>This experiment uses the 3-6-9-12-13 resonance pattern for optimized self-healing:</p>
    <ul>
      <li><strong>Healing Cycles:</strong> Optimized to 3, 6, 9, or 12 cycles based on damage level</li>
      <li><strong>Healing Thresholds:</strong> Aligned to 0.3, 0.6, 0.9 for maximum resonance</li>
      <li><strong>Decay Rates:</strong> Optimized to 0.03, 0.06, 0.09, 0.12, 0.13 based on entropy level</li>
      <li><strong>Healing Factors:</strong> Dynamically adjusted to 0.3, 0.6, 0.9 based on cycle count</li>
    </ul>
  </div>

  <div class="chart-container">
    <h2>Entropy Containment Over Feedback Loops</h2>
    <canvas id="entropyChart" class="chart"></canvas>
  </div>

  <div class="chart-container">
    <h2>Health Over Feedback Loops</h2>
    <canvas id="healthChart" class="chart"></canvas>
  </div>

  <h2>Feedback Loop Details</h2>

  ${results.feedbackLoops.map(loop => `
    <div class="feedback-loop">
      <h3>Feedback Loop ${loop.loop}</h3>

      <div class="metrics">
        <div class="metric">
          <h4>Damage Level</h4>
          <div class="value">${loop.damageLevel.toFixed(3)}</div>
        </div>

        <div class="metric">
          <h4>Health After Damage</h4>
          <div class="value">${loop.afterDamage.health.toFixed(3)}</div>
        </div>

        <div class="metric">
          <h4>Entropy After Damage</h4>
          <div class="value">${loop.afterDamage.entropyContainment.toFixed(3)}</div>
        </div>
      </div>

      <div class="metrics">
        <div class="metric">
          <h4>Domain Mapping Consistency</h4>
          <div class="value">${loop.afterDomainTransition.consistency.toFixed(3)}</div>
        </div>

        <div class="metric">
          <h4>Time Drift Factor</h4>
          <div class="value">${loop.afterTimeDrift.driftFactor.toFixed(3)}</div>
        </div>

        <div class="metric">
          <h4>Drift Compensated</h4>
          <div class="value">${loop.afterTimeDrift.driftCompensated ? 'Yes' : 'No'}</div>
        </div>
      </div>

      <div class="metrics">
        <div class="metric">
          <h4>Health After Healing</h4>
          <div class="value">${loop.afterHealing.health.toFixed(3)}</div>
        </div>

        <div class="metric">
          <h4>Entropy After Healing</h4>
          <div class="value">${loop.afterHealing.entropyContainment.toFixed(3)}</div>
        </div>

        <div class="metric">
          <h4>Healing Cycles</h4>
          <div class="value">${loop.afterHealing.cyclesPerformed}/${loop.afterHealing.cyclesAttempted}</div>
        </div>

        <div class="metric">
          <h4>Healing Reason</h4>
          <div class="value">${loop.afterHealing.diagnostics && loop.afterHealing.diagnostics.length > 0 ?
            loop.afterHealing.diagnostics[loop.afterHealing.diagnostics.length - 1].reason || 'N/A' : 'N/A'}</div>
        </div>

        <div class="metric">
          <h4>Outcome Category</h4>
          <div class="value">${loop.afterHealing.outcomeCategory || 'N/A'}</div>
        </div>

        <div class="metric">
          <h4>Healing Effectiveness</h4>
          <div class="value">${loop.afterHealing.effectiveness ? loop.afterHealing.effectiveness.toFixed(3) : 'N/A'}</div>
        </div>
      </div>
    </div>
  `).join('')}

  <script>
    // Entropy Chart
    const entropyCtx = document.getElementById('entropyChart').getContext('2d');
    new Chart(entropyCtx, {
      type: 'line',
      data: {
        labels: ['Initial', ${results.feedbackLoops.map(loop => `'Loop ${loop.loop}'`).join(', ')}],
        datasets: [
          {
            label: 'After Damage',
            data: [${results.initialState.entropyContainment}, ${results.feedbackLoops.map(loop => loop.afterDamage.entropyContainment).join(', ')}],
            borderColor: 'rgba(255, 99, 132, 1)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.4
          },
          {
            label: 'After Healing',
            data: [${results.initialState.entropyContainment}, ${results.feedbackLoops.map(loop => loop.afterHealing.entropyContainment).join(', ')}],
            borderColor: 'rgba(54, 162, 235, 1)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            tension: 0.4
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Entropy Containment'
            }
          }
        }
      }
    });

    // Health Chart
    const healthCtx = document.getElementById('healthChart').getContext('2d');
    new Chart(healthCtx, {
      type: 'line',
      data: {
        labels: ['Initial', ${results.feedbackLoops.map(loop => `'Loop ${loop.loop}'`).join(', ')}],
        datasets: [
          {
            label: 'After Damage',
            data: [${results.initialState.health}, ${results.feedbackLoops.map(loop => loop.afterDamage.health).join(', ')}],
            borderColor: 'rgba(255, 99, 132, 1)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.4
          },
          {
            label: 'After Healing',
            data: [${results.initialState.health}, ${results.feedbackLoops.map(loop => loop.afterHealing.health).join(', ')}],
            borderColor: 'rgba(54, 162, 235, 1)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            tension: 0.4
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            max: 1,
            title: {
              display: true,
              text: 'Health'
            }
          }
        }
      }
    });
  </script>

  <footer>
    <p>NovaFuse Self-Healing Feedback Loop with 3-6-9-12-13 Resonance Pattern - Copyright © ${new Date().getFullYear()}</p>
    <p><em>"The universe counts in 3s. Now, so do we."</em></p>
  </footer>
</body>
</html>
  `;

  // Write report to file
  fs.writeFileSync(reportPath, html);

  console.log(`\nReport saved to: ${reportPath}`);
}

// Run experiment
runSelfHealingExperiment();
