/**
 * Feature Flag Middleware
 * 
 * This middleware checks if a feature is enabled for the user's product tier.
 * It can be applied to API routes to control access based on product tier.
 */

const { getFeatureFlags } = require('../config/featureFlags');

/**
 * Middleware to check if a feature is enabled for the user's product tier
 * @param {string} feature - The feature to check
 * @returns {function} Express middleware function
 */
const checkFeatureAccess = (feature) => {
  return (req, res, next) => {
    // Get user from request (assuming authentication middleware has run)
    const { user } = req;
    
    if (!user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required to access this resource'
      });
    }
    
    // Get user's product tier
    const productTier = user.productTier ? user.productTier.toLowerCase() : 'novacore'; // Default to NovaCore
    
    // Get feature flags configuration
    const featureFlags = getFeatureFlags();
    
    // Check if the feature is available for the user's product tier
    const hasAccess = featureFlags[feature] && featureFlags[feature][productTier];
    
    if (!hasAccess) {
      return res.status(403).json({
        error: 'Feature not available',
        message: `The requested feature is not available in your current plan.`,
        upgradeInfo: {
          feature,
          currentTier: productTier,
          availableIn: Object.keys(featureFlags[feature] || {})
            .filter(tier => featureFlags[feature][tier])
            .map(tier => tier.charAt(0).toUpperCase() + tier.slice(1)),
          upgradeUrl: '/upgrade'
        }
      });
    }
    
    // Add feature flag info to request for logging/analytics
    req.featureAccess = {
      feature,
      productTier,
      hasAccess: true
    };
    
    next();
  };
};

/**
 * Get all features available for a specific product tier
 * @param {string} productTier - The product tier to check
 * @returns {Array} Array of feature names available for the product tier
 */
const getAvailableFeatures = (productTier) => {
  const featureFlags = getFeatureFlags();
  const normalizedTier = productTier.toLowerCase();
  
  return Object.keys(featureFlags).filter(feature => 
    featureFlags[feature] && featureFlags[feature][normalizedTier]
  );
};

module.exports = {
  checkFeatureAccess,
  getAvailableFeatures
};
