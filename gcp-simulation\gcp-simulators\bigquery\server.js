const express = require('express');
const app = express();
app.use(express.json());
app.get('/health', (req, res) => res.json({ status: 'ok' }));
app.post('/token', (req, res) => res.json({ access_token: 'simulation-token', expires_in: 3600 }));
app.post('/v2/projects/:projectId/queries', (req, res) => res.json({ jobComplete: true, rows: [{ f: [{ v: 'result1' }] }] }));
app.get('/v2/projects/:projectId/datasets/:datasetId', (req, res) => res.json({ id: req.params.datasetId, friendlyName: 'Test Dataset' }));
app.listen(8083, () => console.log('BigQuery Simulator running on port 8083'));
