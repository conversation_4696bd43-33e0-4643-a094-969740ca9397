/**
 * Dashboard API for the Universal Compliance Tracking Optimizer.
 *
 * This module provides API endpoints for the UCTO Unified Dashboard.
 */

const DashboardManager = require('../core/dashboard-manager');

/**
 * Dashboard API handler.
 */
class DashboardAPI {
  /**
   * Initialize the Dashboard API.
   * @param {Object} options - API options
   */
  constructor(options = {}) {
    console.log("Initializing Dashboard API");
    
    // Create a dashboard manager
    this.dashboardManager = new DashboardManager(options.dataDir);
    
    console.log("Dashboard API initialized");
  }
  
  /**
   * Get the dashboard schema.
   * @returns {Object} The dashboard schema
   */
  getSchema() {
    return this.dashboardManager.getDashboardSchema();
  }
  
  /**
   * Get dashboard data.
   * @param {string} userId - ID of the user
   * @param {Object} filters - Optional filters for the dashboard data
   * @returns {Promise<Object>} Dashboard data
   */
  async getDashboardData(userId, filters = {}) {
    return this.dashboardManager.getDashboardData(userId, filters);
  }
  
  /**
   * Get compliance score data.
   * @param {string} userId - ID of the user
   * @param {Object} filters - Optional filters for the data
   * @returns {Promise<Object>} Compliance score data
   */
  async getComplianceScore(userId, filters = {}) {
    const componentData = await this.dashboardManager._getComponentData(
      { type: 'compliance-score-card', title: 'Compliance Score' },
      { id: 'compliance-score', cacheEnabled: true },
      filters
    );
    
    return componentData;
  }
  
  /**
   * Get framework coverage data.
   * @param {string} userId - ID of the user
   * @param {Object} filters - Optional filters for the data
   * @returns {Promise<Object>} Framework coverage data
   */
  async getFrameworkCoverage(userId, filters = {}) {
    const componentData = await this.dashboardManager._getComponentData(
      { type: 'framework-summary', title: 'Framework Coverage' },
      { id: 'framework-coverage', cacheEnabled: true },
      filters
    );
    
    return componentData;
  }
  
  /**
   * Get requirement status data.
   * @param {string} userId - ID of the user
   * @param {Object} filters - Optional filters for the data
   * @returns {Promise<Object>} Requirement status data
   */
  async getRequirementStatus(userId, filters = {}) {
    const componentData = await this.dashboardManager._getComponentData(
      { type: 'status-summary', title: 'Requirement Status' },
      { id: 'requirement-status', cacheEnabled: true },
      filters
    );
    
    return componentData;
  }
  
  /**
   * Get requirements data.
   * @param {string} userId - ID of the user
   * @param {Object} filters - Optional filters for the data
   * @returns {Promise<Object>} Requirements data
   */
  async getRequirements(userId, filters = {}) {
    const componentData = await this.dashboardManager._getComponentData(
      { type: 'requirements-table', title: 'Requirements' },
      { id: 'requirements-list', cacheEnabled: true },
      filters
    );
    
    return componentData;
  }
  
  /**
   * Get activities data.
   * @param {string} userId - ID of the user
   * @param {Object} filters - Optional filters for the data
   * @returns {Promise<Object>} Activities data
   */
  async getActivities(userId, filters = {}) {
    const componentData = await this.dashboardManager._getComponentData(
      { type: 'activities-table', title: 'Activities' },
      { id: 'activities-list', cacheEnabled: true },
      filters
    );
    
    return componentData;
  }
  
  /**
   * Clear the dashboard data cache.
   */
  clearCache() {
    this.dashboardManager.clearCache();
  }
}

module.exports = DashboardAPI;
