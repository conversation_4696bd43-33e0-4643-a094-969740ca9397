/**
 * Connector Health Details Page
 * 
 * This page displays detailed health information for a specific connector.
 */

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  CircularProgress, 
  Typography 
} from '@mui/material';
import { useRouter } from 'next/router';
import DashboardLayout from '../../../layouts/DashboardLayout';
import ConnectorHealthDetails from '../../../components/monitoring/ConnectorHealthDetails';

const ConnectorHealthDetailsPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const [connector, setConnector] = useState(null);
  const [healthData, setHealthData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    if (id) {
      fetchConnectorDetails();
    }
  }, [id]);
  
  const fetchConnectorDetails = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // In a real implementation, this would fetch connector details from the API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock connector data
      const mockConnector = {
        id: id,
        name: 'GitHub API Connector',
        description: 'Connect to GitHub API for repository management',
        category: 'development',
        version: '1.0.0',
        status: 'active',
        baseUrl: 'https://api.github.com'
      };
      
      // Mock health data
      const mockHealthData = {
        id: id,
        name: 'GitHub API Connector',
        status: 'warning',
        responseTime: 850,
        successRate: 92,
        requestCount: 1250,
        errorCount: 100,
        lastCheck: new Date(),
        issues: [
          {
            type: 'performance',
            message: 'High response time',
            timestamp: new Date(Date.now() - Math.floor(Math.random() * 20 * 60 * 1000))
          }
        ],
        history: {
          status: Array(24).fill(0).map((_, i) => {
            const hourRandom = Math.random();
            if (hourRandom > 0.95) {
              return 'error';
            } else if (hourRandom > 0.85) {
              return 'warning';
            } else {
              return 'healthy';
            }
          }),
          responseTimes: Array(24).fill(0).map(() => Math.floor(Math.random() * 900) + 100)
        }
      };
      
      setConnector(mockConnector);
      setHealthData(mockHealthData);
    } catch (error) {
      console.error('Error fetching connector details:', error);
      setError('Failed to load connector details. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <DashboardLayout>
      <Container maxWidth="xl">
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Box sx={{ textAlign: 'center', py: 5 }}>
            <Typography variant="h6" color="error" gutterBottom>
              {error}
            </Typography>
            <Typography variant="body1">
              Connector ID: {id}
            </Typography>
          </Box>
        ) : connector && healthData ? (
          <ConnectorHealthDetails connector={connector} healthData={healthData} />
        ) : (
          <Box sx={{ textAlign: 'center', py: 5 }}>
            <Typography variant="h6" gutterBottom>
              Connector not found
            </Typography>
            <Typography variant="body1">
              The connector with ID {id} could not be found.
            </Typography>
          </Box>
        )}
      </Container>
    </DashboardLayout>
  );
};

export default ConnectorHealthDetailsPage;
