import React from 'react';
import { Box, Typography, useTheme } from '@mui/material';
import { 
  <PERSON><PERSON>hart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Bar
} from 'recharts';

/**
 * Offline Processing Chart Component
 * 
 * Displays charts for offline processing metrics.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.data - Offline processing data
 * @param {Object} props.metrics - Offline processing metrics
 */
const OfflineProcessingChart = ({ data, metrics }) => {
  const theme = useTheme();
  
  // If no data is available, show a message
  if (!data || !data.history || data.history.length === 0) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%' 
      }}>
        <Typography variant="body2" color="text.secondary">
          No offline processing data available
        </Typography>
      </Box>
    );
  }
  
  // Format timestamp for display
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  };
  
  // Prepare data for charts
  const chartData = data.history.map(item => ({
    time: formatTimestamp(item.timestamp),
    timestamp: item.timestamp,
    latency: item.processingTime,
    priority: item.priority === 'high' ? 3 : item.priority === 'medium' ? 2 : 1,
    status: item.status
  }));
  
  // Prepare data for priority distribution
  const priorityData = [
    { name: 'High', value: data.history.filter(item => item.priority === 'high').length },
    { name: 'Medium', value: data.history.filter(item => item.priority === 'medium').length },
    { name: 'Low', value: data.history.filter(item => item.priority === 'low').length }
  ];
  
  // Prepare data for status distribution
  const statusData = [
    { name: 'Completed', value: data.history.filter(item => item.status === 'completed').length },
    { name: 'Processing', value: data.history.filter(item => item.status === 'processing').length },
    { name: 'Queued', value: data.history.filter(item => item.status === 'queued').length },
    { name: 'Failed', value: data.history.filter(item => item.status === 'failed').length }
  ];
  
  // Calculate success rate
  const successRate = metrics.totalRequests > 0 
    ? metrics.successfulRequests / metrics.totalRequests 
    : 1;
  
  // Format success rate as percentage
  const successRateFormatted = `${(successRate * 100).toFixed(1)}%`;
  
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      {/* Summary Metrics */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-around', 
        mb: 1 
      }}>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Success Rate
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {successRateFormatted}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Avg. Latency
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {metrics.averageLatency.toFixed(2)}ms
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Total Requests
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {metrics.totalRequests}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Queue Size
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {data.queueSize || 0}
          </Typography>
        </Box>
      </Box>
      
      {/* Charts */}
      <Box sx={{ display: 'flex', flexGrow: 1 }}>
        {/* Processing Time Chart */}
        <Box sx={{ width: '60%', height: '100%' }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="time" 
                tick={{ fontSize: 10 }}
              />
              <YAxis 
                yAxisId="left"
                label={{ 
                  value: 'Latency (ms)', 
                  angle: -90, 
                  position: 'insideLeft',
                  style: { fontSize: 10 }
                }}
                tick={{ fontSize: 10 }}
              />
              <YAxis 
                yAxisId="right" 
                orientation="right" 
                domain={[0, 3]}
                ticks={[1, 2, 3]}
                tickFormatter={(value) => value === 1 ? 'Low' : value === 2 ? 'Med' : 'High'}
                tick={{ fontSize: 10 }}
              />
              <Tooltip 
                formatter={(value, name) => {
                  if (name === 'latency') return [`${value}ms`, 'Processing Time'];
                  if (name === 'priority') return [
                    value === 1 ? 'Low' : value === 2 ? 'Medium' : 'High', 
                    'Priority'
                  ];
                  return [value, name];
                }}
              />
              <Legend />
              <Line 
                yAxisId="left"
                type="monotone" 
                dataKey="latency" 
                name="Processing Time" 
                stroke={theme.palette.primary.main} 
                activeDot={{ r: 8 }} 
              />
              <Line 
                yAxisId="right"
                type="stepAfter" 
                dataKey="priority" 
                name="Priority" 
                stroke={theme.palette.secondary.main} 
                strokeDasharray="5 5"
              />
            </LineChart>
          </ResponsiveContainer>
        </Box>
        
        {/* Distribution Charts */}
        <Box sx={{ width: '40%', height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* Priority Distribution */}
          <Box sx={{ height: '50%' }}>
            <Typography variant="caption" color="text.secondary" align="center" sx={{ display: 'block' }}>
              Priority Distribution
            </Typography>
            <ResponsiveContainer width="100%" height="85%">
              <BarChart
                data={priorityData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" tick={{ fontSize: 10 }} />
                <YAxis tick={{ fontSize: 10 }} />
                <Tooltip />
                <Bar 
                  dataKey="value" 
                  name="Count" 
                  fill={theme.palette.info.main} 
                />
              </BarChart>
            </ResponsiveContainer>
          </Box>
          
          {/* Status Distribution */}
          <Box sx={{ height: '50%' }}>
            <Typography variant="caption" color="text.secondary" align="center" sx={{ display: 'block' }}>
              Status Distribution
            </Typography>
            <ResponsiveContainer width="100%" height="85%">
              <BarChart
                data={statusData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" tick={{ fontSize: 10 }} />
                <YAxis tick={{ fontSize: 10 }} />
                <Tooltip />
                <Bar 
                  dataKey="value" 
                  name="Count" 
                  fill={theme.palette.success.main} 
                />
              </BarChart>
            </ResponsiveContainer>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default OfflineProcessingChart;
