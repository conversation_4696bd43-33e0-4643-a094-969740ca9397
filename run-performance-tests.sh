#!/bin/bash

# NovaConnect Performance Test Runner
# This script runs the NovaConnect performance tests in a Docker environment

# Set error handling
set -e

# Configuration
DOCKER_COMPOSE_FILE="docker-compose.test.yml"

# Function to display colored output
function echo_color() {
    local color=$1
    local message=$2
    
    case $color in
        "red")
            echo -e "\033[0;31m$message\033[0m"
            ;;
        "green")
            echo -e "\033[0;32m$message\033[0m"
            ;;
        "yellow")
            echo -e "\033[0;33m$message\033[0m"
            ;;
        "cyan")
            echo -e "\033[0;36m$message\033[0m"
            ;;
        *)
            echo "$message"
            ;;
    esac
}

# Main execution
echo_color "cyan" "NovaConnect Performance Test Runner"
echo_color "cyan" "==================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo_color "red" "Error: Docker is not running. Please start Docker and try again."
    exit 1
fi

# Start the Docker environment
echo_color "yellow" "Starting Docker test environment..."
docker-compose -f $DOCKER_COMPOSE_FILE up -d

# Wait for services to be ready
echo_color "yellow" "Waiting for services to be ready..."
sleep 10

# Run the performance tests
echo_color "yellow" "Running NovaConnect performance tests..."
docker-compose -f $DOCKER_COMPOSE_FILE run --rm test-runner npm run test:performance

# Get the exit code
TEST_RESULT=$?

# Stop the Docker environment
echo_color "yellow" "Stopping Docker test environment..."
docker-compose -f $DOCKER_COMPOSE_FILE down

# Display test result
if [ $TEST_RESULT -eq 0 ]; then
    echo_color "green" "Performance tests passed successfully!"
else
    echo_color "red" "Performance tests failed. Check the test report for details."
fi

# Exit with the test result
exit $TEST_RESULT
