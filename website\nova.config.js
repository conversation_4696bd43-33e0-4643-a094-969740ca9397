/**
 * Nova Quantum Visualization Configuration
 * Optimized for high-performance quantum state visualization
 */

module.exports = {
  // WebAssembly optimizations
  wasm: {
    // Enable WebAssembly acceleration for quantum math operations
    quantumMath: true,
    
    // Enable WebAssembly-accelerated encryption
    encryption: true,
    
    // Memory configuration
    memory: {
      // Maximum WebAssembly memory in MB
      maxMemory: 2048,
      
      // Enable memory growth (allows the Wasm memory to grow as needed)
      growOnDemand: true,
      
      // Enable shared memory for multi-threading
      sharedMemory: true
    },
    
    // Threading configuration
    threads: {
      // Enable Web Workers for parallel processing
      enabled: true,
      
      // Number of worker threads (0 = auto-detect based on CPU cores)
      count: 0,
      
      // Enable thread pooling
      pool: {
        enabled: true,
        minThreads: 2,
        maxThreads: 8
      }
    }
  },
  
  // Texture compression settings
  textures: {
    // Default texture format (ASTC for mobile, BC7 for desktop)
    format: 'auto',
    
    // Compression quality (low, medium, high, ultra)
    quality: 'high',
    
    // Generate mipmaps
    mipmaps: true,
    
    // Enable texture atlasing
    atlas: {
      enabled: true,
      maxSize: 4096
    },
    
    // Formats configuration
    formats: {
      astc: {
        blockSize: 4,
        quality: 'thorough',
        effort: 5
      },
      etc: {
        quality: 'high',
        effort: 3
      },
      pvrtc: {
        bitsPerPixel: 4,
        quality: 'good'
      }
    }
  },
  
  // Quantum visualization settings
  quantum: {
    // Maximum number of qubits to render
    maxQubits: 1000000,
    
    // Target FPS for visualization
    targetFPS: 90,
    
    // Dynamic quality adjustment
    dynamicQuality: {
      enabled: true,
      minFPS: 30,
      targetFPS: 60,
      maxDowngrade: 0.5
    },
    
    // Data streaming settings
    streaming: {
      // Enable chunked data loading
      chunked: true,
      
      // Chunk size in KB
      chunkSize: 64,
      
      // Compression settings
      compression: {
        algorithm: 'quantum-delta',
        precision: 'auto',
        level: 6
      },
      
      // Prefetching
      prefetch: {
        enabled: true,
        lookahead: 3
      }
    },
    
    // GPU acceleration settings
    gpu: {
      // Enable WebGL 2.0 features
      webgl2: true,
      
      // Enable WebGL extensions
      extensions: [
        'OES_texture_float',
        'OES_texture_float_linear',
        'OES_element_index_uint',
        'EXT_color_buffer_float',
        'WEBGL_compressed_texture_astc',
        'WEBGL_compressed_texture_etc',
        'WEBGL_compressed_texture_s3tc'
      ],
      
      // GPU memory budget in MB
      memoryBudget: 1024
    }
  },
  
  // Development settings
  development: {
    // Enable performance monitoring
    performance: {
      enabled: true,
      // Log FPS to console
      logFPS: true,
      // Log memory usage
      logMemory: true,
      // Log GPU information
      logGPU: true
    },
    
    // Debug visualization
    debug: {
      // Show wireframe
      wireframe: false,
      // Show bounding boxes
      bounds: false,
      // Show performance stats
      stats: true,
      // Show coordinate axes
      axes: false
    }
  },
  
  // Build configuration
  build: {
    // Output directory for optimized assets
    outputDir: 'dist',
    
    // Source maps
    sourceMap: true,
    
    // Minification
    minify: true,
    
    // Asset hashing for cache busting
    hashFilenames: true
  }
};
