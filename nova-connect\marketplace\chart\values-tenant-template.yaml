# Tenant-specific values for NovaConnect UAC
tenant:
  id: ${TENANT_ID}
  name: ${TENANT_NAME}
  tier: ${TIER}

replicaCount: ${REPLICA_COUNT}

resources:
  requests:
    cpu: ${CPU_REQUEST}
    memory: ${MEMORY_REQUEST}
  limits:
    cpu: ${CPU_LIMIT}
    memory: ${MEMORY_LIMIT}

ingress:
  enabled: true
  hosts:
    - host: tenant-${TENANT_ID}.novafuse.io
      paths:
        - path: /
          pathType: Prefix

mongodb:
  uri: mongodb://mongodb-tenant-${TENANT_ID}:27017/novafuse

redis:
  uri: redis://redis-tenant-${TENANT_ID}:6379

# Tenant-specific encryption key
encryption:
  keyName: projects/${PROJECT_ID}/locations/global/keyRings/tenant-${TENANT_ID}-keyring/cryptoKeys/tenant-${TENANT_ID}-key

# Configuration values
config:
  NODE_ENV: production
  PORT: 3001
  LOG_LEVEL: info
  TENANT_ID: ${TENANT_ID}
  MONGODB_URI: mongodb://mongodb-tenant-${TENANT_ID}:27017/novafuse
  REDIS_URI: redis://redis-tenant-${TENANT_ID}:6379
  ENCRYPTION_KEY_NAME: projects/${PROJECT_ID}/locations/global/keyRings/tenant-${TENANT_ID}-keyring/cryptoKeys/tenant-${TENANT_ID}-key

# Secret values (these will be stored in a Kubernetes Secret)
secrets:
  API_KEY: ${API_KEY}
  JWT_SECRET: ${JWT_SECRET}
  CSRF_SECRET: ${CSRF_SECRET}
