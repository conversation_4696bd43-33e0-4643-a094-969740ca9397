const controllers = require('../../../../apis/esg/reports/controllers');
const models = require('../../../../apis/esg/reports/models');

// Mock the models
jest.mock('../../../../apis/esg/reports/models', () => ({
  esgReports: [
    {
      id: 'esg-r-12345678',
      title: 'Annual Sustainability Report 2023',
      description: 'Comprehensive report on our sustainability initiatives and performance',
      reportType: 'sustainability',
      status: 'published',
      publishDate: '2023-03-15',
      reportingPeriod: {
        startDate: '2022-01-01',
        endDate: '2022-12-31'
      },
      frameworks: ['GRI', 'SASB'],
      metrics: ['carbon-emissions', 'water-usage', 'diversity-metrics'],
      owner: 'Sustainability Team',
      contributors: ['<PERSON>', '<PERSON>'],
      reviewers: ['Executive Board'],
      attachments: [
        {
          id: 'att-12345',
          name: 'Full Report PDF',
          type: 'pdf',
          url: 'https://example.com/reports/sustainability-2023.pdf',
          uploadedBy: '<PERSON>',
          uploadedAt: '2023-03-10T00:00:00Z'
        }
      ],
      createdAt: '2023-01-15T00:00:00Z',
      updatedAt: '2023-03-15T00:00:00Z'
    },
    {
      id: 'esg-r-87654321',
      title: 'Q1 2023 ESG Update',
      description: 'Quarterly update on ESG metrics and initiatives',
      reportType: 'quarterly-update',
      status: 'draft',
      reportingPeriod: {
        startDate: '2023-01-01',
        endDate: '2023-03-31'
      },
      frameworks: ['SASB'],
      metrics: ['carbon-emissions', 'energy-usage'],
      owner: 'ESG Reporting Team',
      contributors: ['Jane Smith'],
      reviewers: [],
      attachments: [],
      createdAt: '2023-04-01T00:00:00Z',
      updatedAt: '2023-04-05T00:00:00Z'
    }
  ],
  reportTemplates: [
    {
      id: 'template-12345',
      name: 'Annual Sustainability Report Template',
      description: 'Template for annual sustainability reporting',
      reportType: 'sustainability',
      frameworks: ['GRI', 'SASB'],
      sections: [
        {
          title: 'Executive Summary',
          description: 'Overview of key sustainability achievements',
          required: true,
          order: 1
        },
        {
          title: 'Environmental Performance',
          description: 'Details of environmental metrics and initiatives',
          required: true,
          order: 2
        },
        {
          title: 'Social Impact',
          description: 'Details of social initiatives and metrics',
          required: true,
          order: 3
        },
        {
          title: 'Governance',
          description: 'Overview of governance structure and policies',
          required: true,
          order: 4
        }
      ],
      createdAt: '2022-12-01T00:00:00Z',
      updatedAt: '2022-12-01T00:00:00Z'
    }
  ]
}));

// Mock Express request and response
const mockRequest = (params = {}, query = {}, body = {}) => ({
  params,
  query,
  body
});

const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

describe('ESG Reports Controllers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getReports', () => {
    it('should return all reports with default pagination', () => {
      const req = mockRequest({}, {});
      const res = mockResponse();

      controllers.getReports(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgReports,
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter reports by status', () => {
      const req = mockRequest({}, { status: 'published' });
      const res = mockResponse();

      controllers.getReports(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.esgReports[0]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter reports by reportType', () => {
      const req = mockRequest({}, { reportType: 'quarterly-update' });
      const res = mockResponse();

      controllers.getReports(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.esgReports[1]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should handle errors', () => {
      const req = mockRequest();
      const res = mockResponse();

      // Force an error
      jest.spyOn(console, 'error').mockImplementation(() => {});
      jest.spyOn(Array.prototype, 'filter').mockImplementation(() => {
        throw new Error('Test error');
      });

      controllers.getReports(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Internal Server Error',
        message: 'Test error'
      });

      // Restore console.error
      console.error.mockRestore();
    });
  });

  describe('getReportById', () => {
    it('should return a specific report by ID', () => {
      const req = mockRequest({ id: 'esg-r-12345678' });
      const res = mockResponse();

      controllers.getReportById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgReports[0]
      });
    });

    it('should return 404 if report not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getReportById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG report with ID non-existent-id not found'
      });
    });
  });

  describe('createReport', () => {
    it('should create a new report', () => {
      const newReport = {
        title: 'New ESG Report',
        description: 'Description of new report',
        reportType: 'sustainability',
        status: 'draft',
        reportingPeriod: {
          startDate: '2023-01-01',
          endDate: '2023-12-31'
        },
        frameworks: ['GRI'],
        metrics: ['carbon-emissions'],
        owner: 'Test Owner'
      };

      const req = mockRequest({}, {}, newReport);
      const res = mockResponse();

      // Mock Date and UUID
      const originalDateNow = Date.now;
      Date.now = jest.fn(() => 1577836800000); // 2020-01-01T00:00:00Z
      global.Date = jest.fn(() => ({
        toISOString: () => '2020-01-01T00:00:00Z'
      }));
      jest.mock('uuid', () => ({
        v4: jest.fn(() => '00000000-0000-0000-0000-000000000000')
      }));

      controllers.createReport(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          title: 'New ESG Report',
          reportType: 'sustainability'
        }),
        message: 'ESG report created successfully'
      }));

      // Restore Date
      Date.now = originalDateNow;
    });
  });

  describe('updateReport', () => {
    it('should update an existing report', () => {
      const updatedReport = {
        title: 'Updated Report Title',
        status: 'published'
      };

      const req = mockRequest({ id: 'esg-r-12345678' }, {}, updatedReport);
      const res = mockResponse();

      controllers.updateReport(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'esg-r-12345678',
          title: 'Updated Report Title',
          status: 'published'
        }),
        message: 'ESG report updated successfully'
      }));
    });

    it('should return 404 if report not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { title: 'Updated Title' });
      const res = mockResponse();

      controllers.updateReport(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG report with ID non-existent-id not found'
      });
    });
  });

  describe('deleteReport', () => {
    it('should delete an existing report', () => {
      const req = mockRequest({ id: 'esg-r-12345678' });
      const res = mockResponse();

      controllers.deleteReport(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'ESG report deleted successfully'
      });
    });

    it('should return 404 if report not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.deleteReport(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG report with ID non-existent-id not found'
      });
    });
  });

  describe('getReportTemplates', () => {
    it('should return all report templates', () => {
      const req = mockRequest();
      const res = mockResponse();

      controllers.getReportTemplates(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.reportTemplates
      });
    });
  });

  describe('getReportTemplateById', () => {
    it('should return a specific report template by ID', () => {
      const req = mockRequest({ id: 'template-12345' });
      const res = mockResponse();

      controllers.getReportTemplateById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.reportTemplates[0]
      });
    });

    it('should return 404 if template not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getReportTemplateById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Report template with ID non-existent-id not found'
      });
    });
  });

  describe('createReportTemplate', () => {
    it('should create a new report template', () => {
      const newTemplate = {
        name: 'New Report Template',
        description: 'Description of new template',
        reportType: 'quarterly-update',
        frameworks: ['SASB'],
        sections: [
          {
            title: 'Executive Summary',
            description: 'Overview of key metrics',
            required: true,
            order: 1
          }
        ]
      };

      const req = mockRequest({}, {}, newTemplate);
      const res = mockResponse();

      controllers.createReportTemplate(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          name: 'New Report Template',
          reportType: 'quarterly-update'
        }),
        message: 'Report template created successfully'
      }));
    });
  });

  describe('updateReportTemplate', () => {
    it('should update an existing report template', () => {
      const updatedTemplate = {
        name: 'Updated Template Name',
        sections: [
          {
            title: 'New Section',
            description: 'New section description',
            required: true,
            order: 5
          }
        ]
      };

      const req = mockRequest({ id: 'template-12345' }, {}, updatedTemplate);
      const res = mockResponse();

      controllers.updateReportTemplate(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'template-12345',
          name: 'Updated Template Name'
        }),
        message: 'Report template updated successfully'
      }));
    });

    it('should return 404 if template not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { name: 'Updated Name' });
      const res = mockResponse();

      controllers.updateReportTemplate(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Report template with ID non-existent-id not found'
      });
    });
  });

  describe('deleteReportTemplate', () => {
    it('should delete an existing report template', () => {
      const req = mockRequest({ id: 'template-12345' });
      const res = mockResponse();

      controllers.deleteReportTemplate(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'Report template deleted successfully'
      });
    });

    it('should return 404 if template not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.deleteReportTemplate(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Report template with ID non-existent-id not found'
      });
    });
  });
});
