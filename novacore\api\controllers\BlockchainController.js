/**
 * NovaCore Blockchain Controller
 * 
 * This controller handles API requests related to blockchain verification.
 */

const { BlockchainService } = require('../services');
const logger = require('../../config/logger');

class BlockchainController {
  /**
   * Verify evidence on blockchain
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async verifyEvidence(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const verification = await BlockchainService.verifyEvidence(req.params.evidenceId, userId);
      
      res.status(200).json({
        success: true,
        data: verification
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Check verification status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async checkVerificationStatus(req, res, next) {
    try {
      const verification = await BlockchainService.checkVerificationStatus(req.params.id);
      
      res.status(200).json({
        success: true,
        data: verification
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get verification by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getVerificationById(req, res, next) {
    try {
      const verification = await BlockchainService.getVerificationById(req.params.id);
      
      res.status(200).json({
        success: true,
        data: verification
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get verification by evidence ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getVerificationByEvidenceId(req, res, next) {
    try {
      const verification = await BlockchainService.getVerificationByEvidenceId(req.params.evidenceId);
      
      res.status(200).json({
        success: true,
        data: verification
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Verify hash against blockchain
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async verifyHash(req, res, next) {
    try {
      const { hash } = req.params;
      const result = await BlockchainService.verifyHash(hash);
      
      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new BlockchainController();
