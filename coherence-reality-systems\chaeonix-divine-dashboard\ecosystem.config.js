module.exports = {
  apps: [{
    name: 'chaeonix',
    script: 'npm',
    args: 'run dev',
    cwd: './chaeonix-divine-dashboard',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'development',
      PORT: 3141
    },
    error_file: './logs/chaeonix-error.log',
    out_file: './logs/chaeonix-out.log',
    log_file: './logs/chaeonix-combined.log',
    time: true
  }]
};
