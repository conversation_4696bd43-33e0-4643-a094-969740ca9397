/**
 * Comphyon Comprehensive Test Runner
 * 
 * This script runs all tests for the Comphyon framework, including both
 * standard tests and NEPI tests.
 */

const { spawn } = require('child_process');
const path = require('path');

/**
 * Run a test script
 * @param {string} scriptPath - Path to the test script
 * @returns {Promise<number>} - Exit code
 */
function runTestScript(scriptPath) {
  return new Promise((resolve, reject) => {
    console.log(`\nRunning ${path.basename(scriptPath)}...\n`);
    
    const child = spawn('node', [scriptPath], {
      stdio: 'inherit'
    });
    
    child.on('close', (code) => {
      resolve(code);
    });
    
    child.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('=== Comphyon Comprehensive Test Suite ===\n');
  
  try {
    // Run standard tests
    console.log('=== Running Standard Tests ===');
    const standardTestsExitCode = await runTestScript(path.join(__dirname, 'run-tests.js'));
    
    // Run NEPI tests
    console.log('\n=== Running NEPI Tests ===');
    const nepiTestsExitCode = await runTestScript(path.join(__dirname, 'nepi', 'run-nepi-tests.js'));
    
    // Check exit codes
    if (standardTestsExitCode !== 0 || nepiTestsExitCode !== 0) {
      console.error('\n❌ Some tests failed.');
      process.exit(1);
    }
    
    console.log('\n✅ All tests passed!');
    process.exit(0);
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run all tests
runAllTests();
