# GCP Marketplace Billing Integration

NovaConnect UAC integrates with Google Cloud Marketplace for billing and entitlement management. This document explains how the integration works and how to use it.

## Overview

The GCP Marketplace billing integration provides:

- **Entitlement Management**: Handling entitlements from GCP Marketplace
- **Usage-Based Billing**: Reporting usage to GCP Marketplace for billing
- **Tenant-Specific Billing**: Supporting tenant-specific usage reporting
- **Billing Webhooks**: Processing billing-related events from GCP Marketplace

## Key Components

### Billing Service

The `BillingService` handles all billing-related functionality:

- **Entitlement Management**: Enabling, updating, disabling, activating, and suspending features based on entitlements
- **Usage Reporting**: Tracking and reporting usage to GCP Marketplace
- **Tenant-Specific Usage**: Supporting tenant-specific usage reporting
- **BigQuery Integration**: Logging billing data to BigQuery for analytics

### Billing Controller

The `BillingController` provides API endpoints for billing-related operations:

- **Entitlement Management**: Endpoints for managing entitlements
- **Usage Reporting**: Endpoints for reporting usage
- **Webhook Handling**: Endpoint for processing GCP Marketplace webhooks

### Feature Service Integration

The billing integration works with the `FeatureService` to enable and disable features based on entitlements:

- **Tier-Based Features**: Features are enabled based on the subscription tier
- **Tenant-Specific Features**: Features can be customized for specific tenants
- **Feature Flags**: Feature flags control access to specific features

## Entitlement Management

### Entitlement Lifecycle

1. **Creation**: When a customer purchases NovaConnect UAC on GCP Marketplace, an entitlement is created
2. **Update**: When a customer changes their subscription tier, the entitlement is updated
3. **Activation**: When a customer activates their subscription, the entitlement is activated
4. **Suspension**: When a customer's subscription is suspended, the entitlement is suspended
5. **Deletion**: When a customer cancels their subscription, the entitlement is deleted

### Entitlement Structure

```json
{
  "id": "entitlement-123",
  "plan": "enterprise",
  "status": "ACTIVE",
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

### Subscription Tiers

NovaConnect UAC offers the following subscription tiers on GCP Marketplace:

- **Core**: Basic functionality for connecting APIs and automating workflows
- **Secure**: Enhanced security and compliance features
- **Enterprise**: Advanced enterprise features for large-scale deployments
- **AI Boost**: AI-powered features for intelligent automation

## Usage-Based Billing

### Usage Metrics

NovaConnect UAC reports the following usage metrics to GCP Marketplace:

- **API Calls**: Number of API calls made
- **Data Transfer**: Amount of data transferred (in bytes)
- **Storage**: Amount of storage used (in bytes)
- **Compute Time**: Amount of compute time used (in milliseconds)

### Usage Reporting

Usage is reported to GCP Marketplace in real-time using the Cloud Billing API:

```javascript
// Report usage to GCP Marketplace
const reportRequest = {
  parent: `projects/${process.env.GCP_PROJECT_ID}`,
  resourceName: `projects/${process.env.GCP_PROJECT_ID}/services/novafuse.googleapis.com`,
  usageMetric: metricName,
  usageValue: quantity,
  timestamp: timestamp,
  labels: {
    tenant_id: tenantId
  }
};

await cloudBillingClient.reportUsage(reportRequest);
```

### Tenant-Specific Usage

For tenant-specific usage, the tenant ID is included in the usage report:

```javascript
// Report tenant-specific usage
await billingService.reportTenantUsage('tenant-123', 'api-calls', 10);
```

## Webhook Integration

GCP Marketplace sends webhooks for billing-related events:

- **ENTITLEMENT_CREATION**: When a new entitlement is created
- **ENTITLEMENT_UPDATE**: When an existing entitlement is updated
- **ENTITLEMENT_DELETION**: When an entitlement is deleted
- **ENTITLEMENT_ACTIVATION**: When an entitlement is activated
- **ENTITLEMENT_SUSPENSION**: When an entitlement is suspended

The webhook endpoint is:

```
POST /api/billing/webhook
```

## BigQuery Integration

Billing data is logged to BigQuery for analytics:

- **Entitlements**: Entitlement events are logged to the `billing.entitlements` table
- **Usage**: Usage events are logged to the `billing.usage` table

## API Endpoints

### Entitlement Management

- `POST /api/billing/entitlements`: Create a new entitlement
- `PUT /api/billing/entitlements`: Update an existing entitlement
- `DELETE /api/billing/entitlements`: Delete an entitlement
- `POST /api/billing/entitlements/activate`: Activate an entitlement
- `POST /api/billing/entitlements/suspend`: Suspend an entitlement

### Usage Reporting

- `POST /api/billing/usage`: Report usage
- `POST /api/billing/usage/tenant`: Report tenant-specific usage

### Webhook

- `POST /api/billing/webhook`: Process GCP Marketplace webhook

### Queries

- `GET /api/billing/entitlements/:customerId`: Get customer entitlements
- `GET /api/billing/usage/:customerId`: Get customer usage

## Configuration

The following environment variables are used for GCP Marketplace billing integration:

- `GCP_ENABLED`: Whether GCP integration is enabled (default: `false`)
- `GCP_PROJECT_ID`: Google Cloud project ID
- `GCP_REGION`: Google Cloud region (default: `us-central1`)
- `GCP_BILLING_ACCOUNT`: Google Cloud billing account ID

## Testing

To test the GCP Marketplace billing integration locally:

1. Set up a local development environment with the required environment variables
2. Use the API endpoints to simulate entitlement management and usage reporting
3. Use the `gcloud` CLI to simulate GCP Marketplace webhooks

Example:

```bash
# Simulate entitlement creation
curl -X POST http://localhost:3000/api/billing/entitlements \
  -H "Content-Type: application/json" \
  -d '{"customerId": "test-customer", "entitlement": {"plan": "enterprise"}}'

# Simulate usage reporting
curl -X POST http://localhost:3000/api/billing/usage \
  -H "Content-Type: application/json" \
  -d '{"customerId": "test-customer", "metricName": "api-calls", "quantity": 10}'

# Simulate tenant-specific usage reporting
curl -X POST http://localhost:3000/api/billing/usage/tenant \
  -H "Content-Type: application/json" \
  -d '{"tenantId": "test-tenant", "metricName": "api-calls", "quantity": 10}'

# Simulate GCP Marketplace webhook
curl -X POST http://localhost:3000/api/billing/webhook \
  -H "Content-Type: application/json" \
  -d '{"event": "ENTITLEMENT_CREATION", "resource": {"customerId": "test-customer", "plan": "enterprise"}}'
```

## Conclusion

The GCP Marketplace billing integration provides a seamless way to manage entitlements and report usage for NovaConnect UAC. It supports tenant-specific billing and integrates with BigQuery for analytics.
