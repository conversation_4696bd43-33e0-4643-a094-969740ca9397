import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'
import { useWebSocket } from '@/lib/useWebSocket'
import { auth } from '@clerk/nextjs'
import { calculateTriadicMetrics, getTriadicColor, formatTriadicMetric } from '@/lib/triadicMetrics'
import { Conversion, Product } from '@/types/websocket'

interface Category {
  id: string
  name: string
  icon: string
  products: Product[]
  conversions: Conversion[]
  metrics: {
    psi: number
    phi: number
    kappa: number
  }
}

export function ProductCategories() {
  const { userId } = auth()
  const { data, connected } = useWebSocket(userId || '')
  const [categories, setCategories] = useState<Category[]>([])
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories')
        const data = await response.json()
        
        // Calculate triadic metrics for each category
        const categoriesWithMetrics = data.map((category: any) => ({
          ...category,
          metrics: calculateTriadicMetrics(category.conversions, category.products)
        }))
        
        setCategories(categoriesWithMetrics)
      } catch (error) {
        console.error('Error fetching categories:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchCategories()
  }, [])

  useEffect(() => {
    if (data?.type === 'conversion' && data.data) {
      const conversion = data.data as Conversion
      setCategories(prev => {
        const existingIndex = prev.findIndex(c => c.id === conversion.categoryId)
        if (existingIndex !== -1) {
          const updatedCategory = {
            ...prev[existingIndex],
            conversions: [...prev[existingIndex].conversions, conversion]
          }
          return [...prev.slice(0, existingIndex), updatedCategory, ...prev.slice(existingIndex + 1)]
        }
        return prev
      })
    }
  }, [data])

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(index => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-4 rounded-lg border bg-gray-50 animate-pulse"
          >
            <div className="h-8 w-32 bg-gray-200 rounded mb-4" />
            <div className="h-4 w-24 bg-gray-200 rounded" />
          </motion.div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Product Categories</h2>
        <button
          onClick={() => window.location.href = '/categories/new'}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Add Category
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {categories.map((category) => (
          <motion.div
            key={category.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`p-4 rounded-lg border cursor-pointer hover:border-purple-500 transition-all ${
              selectedCategory?.id === category.id ? 'border-purple-500' : 'border-white/10'
            }`}
            onClick={() => setSelectedCategory(category)}
          >
            <div className="flex items-center space-x-4">
              <div className={`w-4 h-4 rounded-full ${
                getTriadicColor(category.metrics.psi)
              }`} />
              <div>
                <h3 className="font-semibold">{category.name}</h3>
                <p className="text-sm text-gray-400">
                  {category.products.length} products
                </p>
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <div className="flex space-x-2">
                <span className={`px-2 py-1 rounded-full text-xs ${getTriadicColor(category.metrics.psi)}`}>
                  PSI: {formatTriadicMetric(category.metrics.psi)}
                </span>
                <span className={`px-2 py-1 rounded-full text-xs ${getTriadicColor(category.metrics.phi)}`}>
                  PHI: {formatTriadicMetric(category.metrics.phi)}
                </span>
                <span className={`px-2 py-1 rounded-full text-xs ${getTriadicColor(category.metrics.kappa)}`}>
                  KAPPA: {formatTriadicMetric(category.metrics.kappa)}
                </span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {selectedCategory && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-6 p-4 bg-white/5 backdrop-blur-lg rounded-lg border border-white/10"
        >
          <h3 className="text-lg font-semibold mb-4">Category Details</h3>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-semibold mb-2">Performance Metrics</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <span className={`text-purple-500 ${
                      selectedCategory.metrics.psi >= 80 ? 'font-bold' : ''
                    }`}>PSI: {formatTriadicMetric(selectedCategory.metrics.psi)}</span>
                    <span className={`text-blue-500 ${
                      selectedCategory.metrics.phi >= 80 ? 'font-bold' : ''
                    }`}>PHI: {formatTriadicMetric(selectedCategory.metrics.phi)}</span>
                    <span className={`text-green-500 ${
                      selectedCategory.metrics.kappa >= 80 ? 'font-bold' : ''
                    }`}>KAPPA: {formatTriadicMetric(selectedCategory.metrics.kappa)}</span>
                  </div>
                  <p className="text-sm text-gray-400">
                    Conversion Rate: {selectedCategory.conversions.filter(c => c.status === 'completed').length / selectedCategory.conversions.length * 100}%
                  </p>
                </div>
              </div>
              <div>
                <h4 className="text-sm font-semibold mb-2">Product Overview</h4>
                <div className="space-y-2">
                  <p className="text-sm text-gray-400">
                    Total Products: {selectedCategory.products.length}
                  </p>
                  <p className="text-sm text-gray-400">
                    Active Products: {selectedCategory.products.filter(p => p.status === 'active').length}
                  </p>
                  <p className="text-sm text-gray-400">
                    Total Revenue: ${selectedCategory.conversions.reduce((sum, c) => sum + c.revenue, 0).toFixed(2)}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                onClick={() => setSelectedCategory(null)}
              >
                Close Details
              </button>
            </div>
          </div>
        </motion.div>
      )}

      <div className="mt-4">
        <h4 className="text-sm font-semibold mb-2">WebSocket Status</h4>
        <div className={`px-2 py-1 rounded-full text-xs ${
          connected ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`}>
          {connected ? 'Connected' : 'Disconnected'}
        </div>
      </div>
    </div>
  )
}
