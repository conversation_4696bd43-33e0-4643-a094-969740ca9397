"""
Amazon Braket backend for quantum computing in protein folding.

This module provides an implementation of the QuantumBackend interface
using Amazon Braket for executing quantum circuits on various quantum devices.
"""

import os
import time
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
import json
import boto3
from braket.aws import AwsDevice, AwsQuantumTask
from braket.circuits import Circuit, Observable, QubitSet
from braket.devices import LocalSimulator

from . import QuantumBackend

class BraketBackend(QuantumBackend):
    """Amazon Braket implementation of the QuantumBackend interface."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the Braket backend.
        
        Args:
            config: Configuration dictionary with the following optional keys:
                - device_arn: ARN of the Braket device to use (default: local simulator)
                - s3_bucket: S3 bucket for storing results (required for quantum devices)
                - s3_prefix: S3 prefix for storing results (default: 'quantum-braket')
                - poll_timeout_seconds: Timeout for polling results (default: 300)
                - poll_interval_seconds: Interval for polling results (default: 5)
                - shots: Number of shots for measurements (default: 1000)
        """
        super().__init__(config)
        self.device_arn = self.config.get('device_arn')
        self.s3_bucket = self.config.get('s3_bucket')
        self.s3_prefix = self.config.get('s3_prefix', 'quantum-braket')
        self.poll_timeout = self.config.get('poll_timeout_seconds', 300)
        self.poll_interval = self.config.get('poll_interval_seconds', 5)
        self.shots = self.config.get('shots', 1000)
        
        # Initialize AWS session and device
        self.device = None
        self._initialize_device()
    
    def _initialize_device(self) -> None:
        """Initialize the Braket device."""
        try:
            if self.device_arn:
                # Use the specified quantum device
                self.device = AwsDevice(self.device_arn)
                
                # Verify S3 bucket is specified for quantum devices
                if not self.s3_bucket:
                    raise ValueError("s3_bucket is required when using quantum devices")
            else:
                # Fall back to local simulator
                self.device = LocalSimulator()
                
        except Exception as e:
            raise ValueError(f"Failed to initialize Braket device: {str(e)}")
    
    def _create_circuit(self, num_qubits: int, depth: int) -> Tuple[Circuit, Dict]:
        """Create a parameterized quantum circuit.
        
        Args:
            num_qubits: Number of qubits in the circuit
            depth: Depth of the quantum circuit
            
        Returns:
            A tuple of (circuit, params) where params are the parameter names
        """
        circuit = Circuit()
        params = {}
        
        # Add Hadamard gates to all qubits
        for i in range(num_qubits):
            circuit.h(i)
        
        # Add parameterized layers
        for d in range(depth):
            # Add parameterized rotations
            for i in range(num_qubits):
                # Create unique parameter names
                rx_param = f"rx_{d}_{i}"
                ry_param = f"ry_{d}_{i}"
                rz_param = f"rz_{d}_{i}"
                
                # Add parameterized gates
                circuit.rx(i, rx_param).ry(i, ry_param).rz(i, rz_param)
                
                # Store parameter names
                params[f"{rx_param}"] = np.random.uniform(0, 2 * np.pi)
                params[f"{ry_param}"] = np.random.uniform(0, 2 * np.pi)
                params[f"{rz_param}"] = np.random.uniform(0, 2 * np.pi)
            
            # Add entangling gates (linear nearest neighbor)
            for i in range(num_qubits - 1):
                circuit.cnot(i, (i + 1) % num_qubits)
        
        return circuit, params
    
    def run_circuit(
        self, 
        circuit: Any,  # Not used, we define our own circuit
        num_qubits: int,
        depth: int,
        shots: int
    ) -> Dict[str, int]:
        """Run a quantum circuit and return the measurement results.
        
        Args:
            circuit: Not used (we define our own circuit)
            num_qubits: Number of qubits in the circuit
            depth: Depth of the quantum circuit
            shots: Number of measurement shots
            
        Returns:
            Dictionary mapping measurement outcomes to counts
        """
        # Create a new circuit
        circuit, params = self._create_circuit(num_qubits, depth)
        
        # Execute the circuit
        try:
            if isinstance(self.device, LocalSimulator):
                # Run locally
                result = self.device.run(circuit, shots=shots).result()
                counts = result.measurement_counts
            else:
                # Run on AWS
                task = self.device.run(
                    circuit,
                    s3_destination_folder=(self.s3_bucket, self.s3_prefix),
                    shots=shots
                )
                
                # Wait for the task to complete
                task_id = task.id
                print(f"Task {task_id} submitted to {self.device.name}")
                print(f"Polling for results (timeout: {self.poll_timeout}s)...")
                
                start_time = time.time()
                while time.time() - start_time < self.poll_timeout:
                    status = task.state()
                    print(f"Status: {status}")
                    
                    if status in AwsQuantumTask.TERMINAL_STATES:
                        break
                        
                    time.sleep(self.poll_interval)
                
                if status != 'COMPLETED':
                    raise RuntimeError(f"Task {task_id} failed with status: {status}")
                
                # Get results
                result = task.result()
                counts = result.measurement_counts
                
                print(f"Task {task_id} completed successfully")
                print(f"Results saved to: s3://{self.s3_bucket}/{self.s3_prefix}/{task_id}/")
            
            # Convert counts to the expected format
            return {''.join(map(str, k)): v for k, v in counts.items()}
            
        except Exception as e:
            raise RuntimeError(f"Error running Braket circuit: {str(e)}")
    
    def get_quantum_volume(self) -> int:
        """Get the quantum volume of the backend."""
        if isinstance(self.device, LocalSimulator):
            return 1024  # High value for local simulator
            
        # For real quantum devices, return their quantum volume if available
        try:
            if hasattr(self.device, 'properties'):
                props = self.device.properties
                if hasattr(props, 'deviceCapabilities'):
                    capabilities = json.loads(props.deviceCapabilities)
                    return capabilities.get('deviceParameters', {}).get('quantumVolume', 0)
        except:
            pass
            
        return 32  # Default minimum quantum volume
    
    def get_backend_info(self) -> Dict[str, Any]:
        """Get information about the backend."""
        info = {
            'name': 'braket',
            'device_arn': self.device_arn or 'local',
            'shots': self.shots,
            'quantum_volume': self.get_quantum_volume(),
            'is_simulator': isinstance(self.device, LocalSimulator)
        }
        
        # Add device-specific information
        if not info['is_simulator']:
            info.update({
                'device_name': self.device.name,
                'device_type': self.device.type,
                'device_status': self.device.status,
                'provider_name': self.device.provider_name,
                's3_bucket': self.s3_bucket,
                's3_prefix': self.s3_prefix
            })
        
        return info
    
    @classmethod
    def is_available(cls) -> bool:
        """Check if Amazon Braket is available."""
        try:
            import boto3
            from braket.aws import AwsDevice
            from braket.devices import LocalSimulator
            
            # Check if AWS credentials are configured
            try:
                boto3.Session().get_credentials()
                return True
            except:
                # Can still use local simulator without AWS credentials
                return True
                
        except ImportError:
            return False
