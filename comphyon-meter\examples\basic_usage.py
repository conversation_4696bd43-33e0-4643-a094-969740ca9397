"""
Basic usage example for the ComphyonΨᶜ Meter.
"""

import sys
import os
import time
import numpy as np
import matplotlib.pyplot as plt

# Add the parent directory to the path so we can import the package
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.comphyon_meter import ComphyonMeter
from src.comphyon_meter.utils import generate_random_tensor, format_metrics_for_display

def main():
    """
    Demonstrate basic usage of the ComphyonΨᶜ Meter.
    """
    print("ComphyonΨᶜ Meter - Basic Usage Example")
    print("======================================")
    
    # Initialize the meter
    meter = ComphyonMeter()
    
    # Generate sample tensor data
    csde_tensor = [0.75, 0.85, 0.65, 0.90]  # [G, D, A₁, c₁]
    csfe_tensor = [0.65, 0.70, 0.80, 0.80]  # [F₁, P, A₂, c₂]
    csme_tensor = [0.70, 0.90, 0.60, 0.85]  # [T, I, E, c₃]
    
    # Calculate ComphyonΨᶜ metrics
    metrics = meter.calculate(csde_tensor, csfe_tensor, csme_tensor)
    
    # Display the metrics
    print("\nSingle Measurement:")
    print(format_metrics_for_display(metrics))
    
    # Generate a time series of measurements
    print("\nGenerating time series of measurements...")
    history = []
    
    for i in range(20):
        # Add some random variation to the tensors
        csde_tensor = [0.75 + np.random.normal(0, 0.05), 
                       0.85 + np.random.normal(0, 0.05),
                       0.65 + np.random.normal(0, 0.05),
                       0.90 + np.random.normal(0, 0.05)]
        
        csfe_tensor = [0.65 + np.random.normal(0, 0.05),
                       0.70 + np.random.normal(0, 0.05),
                       0.80 + np.random.normal(0, 0.05),
                       0.80 + np.random.normal(0, 0.05)]
        
        csme_tensor = [0.70 + np.random.normal(0, 0.05),
                       0.90 + np.random.normal(0, 0.05),
                       0.60 + np.random.normal(0, 0.05),
                       0.85 + np.random.normal(0, 0.05)]
        
        # Clip values to valid range
        csde_tensor = [max(0.1, min(0.99, x)) for x in csde_tensor]
        csfe_tensor = [max(0.1, min(0.99, x)) for x in csfe_tensor]
        csme_tensor = [max(0.1, min(0.99, x)) for x in csme_tensor]
        
        # Calculate metrics
        metrics = meter.calculate(csde_tensor, csfe_tensor, csme_tensor)
        history.append(metrics)
        
        # Simulate time passing
        time.sleep(0.1)
    
    print(f"Generated {len(history)} measurements.")
    
    # Display the latest metrics
    print("\nLatest Measurement:")
    print(format_metrics_for_display(history[-1]))
    
    # Plot the metrics
    print("\nPlotting metrics...")
    fig = meter.visualizer.plot_metrics_time_series(history)
    plt.show()
    
    # Plot domain energies
    fig = meter.visualizer.plot_domain_energies(history)
    plt.show()
    
    # Create a dashboard
    fig = meter.visualizer.create_dashboard(history)
    plt.show()
    
    print("\nExample completed successfully.")

if __name__ == "__main__":
    main()
