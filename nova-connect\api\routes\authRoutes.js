/**
 * Authentication Routes
 */

const express = require('express');
const router = express.Router();
const Joi = require('joi');
const AuthController = require('../controllers/AuthController');
const { authenticate, hasRole } = require('../middleware/authMiddleware');
const { validate } = require('../middleware/validationMiddleware');
const {
  commonSchemas,
  loginSchema,
  registerSchema,
  refreshTokenSchema,
  changePasswordSchema,
  forgotPasswordSchema,
  resetPasswordSchema,
  validateTokenSchema,
  getUserSchema,
  updateUserSchema,
  deleteUserSchema,
  listUsersSchema
} = require('../validation');

// Public routes
router.post('/register', validate(registerSchema), (req, res, next) => {
  AuthController.register(req, res, next);
});

router.post('/login', validate(loginSchema), (req, res, next) => {
  AuthController.login(req, res, next);
});

router.post('/refresh-token', validate(refreshTokenSchema), (req, res, next) => {
  AuthController.refreshToken(req, res, next);
});

router.post('/forgot-password', validate(forgotPasswordSchema), (req, res, next) => {
  AuthController.forgotPassword(req, res, next);
});

router.post('/reset-password', validate(resetPasswordSchema), (req, res, next) => {
  AuthController.resetPassword(req, res, next);
});

router.post('/validate-token', validate(validateTokenSchema), (req, res, next) => {
  AuthController.validateToken(req, res, next);
});

// Protected routes
router.post('/logout', authenticate, (req, res, next) => {
  AuthController.logout(req, res, next);
});

router.get('/me', authenticate, (req, res, next) => {
  AuthController.getCurrentUser(req, res, next);
});

router.post('/change-password', authenticate, validate(changePasswordSchema), (req, res, next) => {
  AuthController.changePassword(req, res, next);
});

// User management routes (admin only)
router.get('/users', authenticate, hasRole('admin'), validate(listUsersSchema), (req, res, next) => {
  AuthController.getAllUsers(req, res, next);
});

// User routes
router.get('/users/:userId', authenticate, validate(getUserSchema), (req, res, next) => {
  AuthController.getUserById(req, res, next);
});

router.put('/users/:userId', authenticate, validate(updateUserSchema), (req, res, next) => {
  AuthController.updateUser(req, res, next);
});

router.delete('/users/:userId', authenticate, validate(deleteUserSchema), (req, res, next) => {
  AuthController.deleteUser(req, res, next);
});

// API key routes
router.post('/api-keys', authenticate, validate({ body: commonSchemas.apiKey }), (req, res, next) => {
  AuthController.createApiKey(req, res, next);
});

router.get('/api-keys', authenticate, (req, res, next) => {
  AuthController.getApiKeys(req, res, next);
});

router.delete('/api-keys/:id', authenticate, validate({ params: Joi.object({ id: commonSchemas.id }) }), (req, res, next) => {
  AuthController.deleteApiKey(req, res, next);
});

module.exports = router;
