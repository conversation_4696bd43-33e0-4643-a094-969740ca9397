/**
 * Finite Universe Principle
 *
 * This module exports all components related to the Finite Universe Principle,
 * which serves as NEPI's Divine Firewall, making spiritual corruption mathematically
 * impossible through five pillars:
 *
 * 1. Inherent Boundary: All operations must respect finite boundaries
 * 2. Mathematical Impossibility of Corruption: Infinite operations are rejected
 * 3. Resonance-Only Logic: Only operations that maintain resonance are allowed
 * 4. Containerization: Each domain operates within its own bounded container
 * 5. Truth Alignment: All operations must align with truth principles
 */

const FiniteUniverse = require('./finite-universe');
const BoundaryEnforcer = require('./boundary-enforcer');
const {
  MathematicalIncompatibilityScenario,
  InfiniteValueScenario,
  UnboundedRecursionScenario,
  BoundaryViolationScenario,
  CircularReferenceScenario,
  ExcessiveComplexityScenario,
  InfiniteLoopScenario
} = require('./mathematical-incompatibility-scenarios');
const ResilienceGauntlet = require('./resilience-gauntlet');

// Import advanced components
const {
  MultiVectorScenario,
  DomainSpecificScenario,
  QuantumDecoherenceScenario,
  CrossDomainLeakageScenario,
  TemporalParadoxScenario,
  createDefaultAdvancedScenarios
} = require('./advanced-incompatibility-scenarios');
const {
  EnhancedBoundaryEnforcer,
  createEnhancedBoundaryEnforcer
} = require('./enhanced-boundary-enforcer');
const {
  DomainIntegration,
  createDomainIntegration,
  createCSDEAdapter,
  createCSFEAdapter,
  createCSMEAdapter
} = require('./domain-integration');

// Import domain engines
const {
  CSEDAdapter,
  CSFEAdapter,
  CSMEAdapter,
  createAllDomainAdapters
} = require('./domain-engines');

// Import cross-domain validation
const {
  CrossDomainValidator,
  createCrossDomainValidator
} = require('./cross-domain-validation');

// Import monitoring dashboard
const {
  MonitoringDashboard,
  createMonitoringDashboard
} = require('./monitoring-dashboard');

// Import dashboard UI
const {
  DashboardUI,
  createDashboardUI
} = require('./dashboard-ui');

// Import NovaFuse integration
const {
  NovaFuseIntegration,
  createNovaFuseIntegration
} = require('./novafuse-integration');

// Import machine learning components
const {
  AnomalyDetector,
  PredictiveAnalytics,
  createAnomalyDetector,
  createPredictiveAnalytics,
  createMLComponents
} = require('./ml');

// Import API
const {
  FiniteUniverseAPI,
  createFiniteUniverseAPI
} = require('./api');

// Import performance optimization components
const {
  CacheManager,
  PerformanceOptimizer,
  createCacheManager,
  createPerformanceOptimizer,
  createPerformanceComponents
} = require('./performance');

// Import distributed processing components
const {
  ClusterManager,
  LoadBalancer,
  DistributedProcessor,
  createClusterManager,
  createLoadBalancer,
  createDistributedProcessor,
  createDistributedComponents
} = require('./distributed');

// Import security components
const {
  RBAC,
  AuditLogger,
  SecureStorage,
  createRBAC,
  createAuditLogger,
  createSecureStorage,
  createSecurityComponents
} = require('./security');

// Import analytics components
const {
  TrendAnalyzer,
  PatternDetector,
  AnomalyClassifier,
  createTrendAnalyzer,
  createPatternDetector,
  createAnomalyClassifier,
  createAnalyticsComponents
} = require('./analytics');

// Import analytics dashboard
const {
  AnalyticsDashboard,
  createAnalyticsDashboard
} = require('./analytics/analytics-dashboard');

/**
 * Create a default Boundary Enforcer with recommended settings
 * @param {Object} options - Configuration options
 * @returns {BoundaryEnforcer} - Configured Boundary Enforcer
 */
function createBoundaryEnforcer(options = {}) {
  return new BoundaryEnforcer({
    enableLogging: true,
    strictMode: true,
    autoCorrect: true,
    ...options
  });
}

/**
 * Create a default Resilience Gauntlet with recommended scenarios
 * @param {Object} options - Configuration options
 * @returns {ResilienceGauntlet} - Configured Resilience Gauntlet
 */
function createResilienceGauntlet(options = {}) {
  const gauntlet = new ResilienceGauntlet(options);
  const scenarios = ResilienceGauntlet.createDefaultScenarios();
  gauntlet.addScenarios(scenarios);
  return gauntlet;
}

/**
 * Create an advanced Resilience Gauntlet with both basic and advanced scenarios
 * @param {Object} options - Configuration options
 * @returns {ResilienceGauntlet} - Configured advanced Resilience Gauntlet
 */
function createAdvancedResilienceGauntlet(options = {}) {
  const gauntlet = new ResilienceGauntlet(options);
  const basicScenarios = ResilienceGauntlet.createDefaultScenarios();
  const advancedScenarios = createDefaultAdvancedScenarios();
  gauntlet.addScenarios(basicScenarios);
  gauntlet.addScenarios(advancedScenarios);
  return gauntlet;
}

/**
 * Run a quick test of the Finite Universe Principle components
 * @param {Object} options - Test options
 * @returns {Object} - Test results
 */
async function runQuickTest(options = {}) {
  const boundaryEnforcer = createBoundaryEnforcer(options.enforcerOptions);
  const gauntlet = createResilienceGauntlet(options.gauntletOptions);

  console.log('Running Resilience Gauntlet against Boundary Enforcer...');
  const results = await gauntlet.run(boundaryEnforcer);

  console.log(`Resilience Score: ${results.summary.resilienceScore}%`);
  console.log(`Successful Scenarios: ${results.summary.successfulScenarios}/${results.summary.totalScenarios}`);

  return results;
}

/**
 * Run an advanced test of the Finite Universe Principle components
 * @param {Object} options - Test options
 * @returns {Object} - Test results
 */
async function runAdvancedTest(options = {}) {
  const enhancedEnforcer = createEnhancedBoundaryEnforcer(options.enforcerOptions);
  const gauntlet = createAdvancedResilienceGauntlet(options.gauntletOptions);

  console.log('Running Advanced Resilience Gauntlet against Enhanced Boundary Enforcer...');
  const results = await gauntlet.run(enhancedEnforcer);

  console.log(`Resilience Score: ${results.summary.resilienceScore}%`);
  console.log(`Successful Scenarios: ${results.summary.successfulScenarios}/${results.summary.totalScenarios}`);

  // Print category statistics
  console.log('\nCategory Statistics:');
  for (const [category, stats] of Object.entries(results.categoryStats)) {
    const successRate = Math.round((stats.successful / stats.total) * 100);
    console.log(`${category}: ${stats.successful}/${stats.total} (${successRate}%)`);
  }

  return results;
}

/**
 * Create a fully integrated Finite Universe Principle defense system
 * @param {Object} options - Configuration options
 * @returns {Object} - Integrated defense system
 */
function createIntegratedDefenseSystem(options = {}) {
  // Create enhanced boundary enforcer
  const enhancedEnforcer = createEnhancedBoundaryEnforcer(options.enforcerOptions);

  // Create domain integration
  const domainIntegration = createDomainIntegration({
    ...options.integrationOptions,
    boundaryEnforcer: enhancedEnforcer
  });

  // Create advanced resilience gauntlet
  const advancedGauntlet = createAdvancedResilienceGauntlet(options.gauntletOptions);

  return {
    enhancedEnforcer,
    domainIntegration,
    advancedGauntlet,

    // Helper method to run tests
    runTests: async () => {
      return advancedGauntlet.run(enhancedEnforcer);
    },

    // Helper method to process data
    processData: async (data, domain) => {
      return domainIntegration.processData(data, domain);
    }
  };
}

/**
 * Create a complete Finite Universe Principle defense system with all components
 * @param {Object} options - Configuration options
 * @returns {Object} - Complete defense system
 */
function createCompleteDefenseSystem(options = {}) {
  // Create NovaFuse integration
  const novaFuseIntegration = createNovaFuseIntegration({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    enableMonitoring: options.enableMonitoring !== undefined ? options.enableMonitoring : true,
    strictMode: options.strictMode !== undefined ? options.strictMode : true
  });

  return {
    novaFuseIntegration,
    defenseSystem: novaFuseIntegration.getDefenseSystem(),
    monitoringDashboard: novaFuseIntegration.getMonitoringDashboard(),
    domainAdapters: novaFuseIntegration.getDomainAdapters(),
    crossDomainValidator: novaFuseIntegration.getCrossDomainValidator(),

    // Helper method to process data
    processData: async (data, domain) => {
      return novaFuseIntegration.processData(data, domain);
    },

    // Helper method to register NovaFuse components
    registerNovaComponents: (components) => {
      if (components.novaConnect) {
        novaFuseIntegration.registerNovaConnect(components.novaConnect);
      }

      if (components.novaVision) {
        novaFuseIntegration.registerNovaVision(components.novaVision);
      }

      if (components.novaDNA) {
        novaFuseIntegration.registerNovaDNA(components.novaDNA);
      }

      if (components.novaShield) {
        novaFuseIntegration.registerNovaShield(components.novaShield);
      }
    },

    // Helper method to dispose resources
    dispose: () => {
      novaFuseIntegration.dispose();
    }
  };
}

module.exports = {
  // Core components
  FiniteUniverse,
  BoundaryEnforcer,

  // Basic scenario classes
  MathematicalIncompatibilityScenario,
  InfiniteValueScenario,
  UnboundedRecursionScenario,
  BoundaryViolationScenario,
  CircularReferenceScenario,
  ExcessiveComplexityScenario,
  InfiniteLoopScenario,

  // Advanced components
  EnhancedBoundaryEnforcer,
  DomainIntegration,

  // Advanced scenario classes
  MultiVectorScenario,
  DomainSpecificScenario,
  QuantumDecoherenceScenario,
  CrossDomainLeakageScenario,
  TemporalParadoxScenario,

  // Domain engines
  CSEDAdapter,
  CSFEAdapter,
  CSMEAdapter,

  // Cross-domain validation
  CrossDomainValidator,

  // Monitoring dashboard
  MonitoringDashboard,

  // Dashboard UI
  DashboardUI,

  // NovaFuse integration
  NovaFuseIntegration,

  // Machine learning components
  AnomalyDetector,
  PredictiveAnalytics,

  // API components
  FiniteUniverseAPI,

  // Performance optimization components
  CacheManager,
  PerformanceOptimizer,

  // Distributed processing components
  ClusterManager,
  LoadBalancer,
  DistributedProcessor,

  // Security components
  RBAC,
  AuditLogger,
  SecureStorage,

  // Analytics components
  TrendAnalyzer,
  PatternDetector,
  AnomalyClassifier,
  AnalyticsDashboard,

  // Testing framework
  ResilienceGauntlet,

  // Basic factory functions
  createBoundaryEnforcer,
  createResilienceGauntlet,

  // Advanced factory functions
  createEnhancedBoundaryEnforcer,
  createDomainIntegration,
  createCSDEAdapter,
  createCSFEAdapter,
  createCSMEAdapter,
  createAdvancedResilienceGauntlet,
  createIntegratedDefenseSystem,

  // Domain engines factory functions
  createAllDomainAdapters,

  // Cross-domain validation factory functions
  createCrossDomainValidator,

  // Monitoring dashboard factory functions
  createMonitoringDashboard,

  // Dashboard UI factory functions
  createDashboardUI,

  // Machine learning factory functions
  createAnomalyDetector,
  createPredictiveAnalytics,
  createMLComponents,

  // API factory functions
  createFiniteUniverseAPI,

  // Performance optimization factory functions
  createCacheManager,
  createPerformanceOptimizer,
  createPerformanceComponents,

  // Distributed processing factory functions
  createClusterManager,
  createLoadBalancer,
  createDistributedProcessor,
  createDistributedComponents,

  // Security factory functions
  createRBAC,
  createAuditLogger,
  createSecureStorage,
  createSecurityComponents,

  // Analytics factory functions
  createTrendAnalyzer,
  createPatternDetector,
  createAnomalyClassifier,
  createAnalyticsComponents,
  createAnalyticsDashboard,

  // NovaFuse integration factory functions
  createNovaFuseIntegration,
  createCompleteDefenseSystem,

  // Utility functions
  runQuickTest,
  runAdvancedTest
};
