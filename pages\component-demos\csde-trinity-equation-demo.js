import React, { useState } from 'react';
import Head from 'next/head';
import { motion } from 'framer-motion';

// Import layout components
import MainLayout from '../../components/layouts/MainLayout';
import PageHeader from '../../components/common/PageHeader';
import Section from '../../components/common/Section';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Tabs from '../../components/common/Tabs';

// Import the CSDE Trinity Equation component
import CSDETrinityEquation from '../../components/trinity/CSDETrinityEquation';

/**
 * CSDE Trinity Equation Demo Page
 * 
 * This page showcases the CSDE Trinity Equation visualization,
 * demonstrating how the mathematical constants drive the Cyber-Safety Dynamic Engine.
 */
const CSDETrinityEquationDemo = () => {
  const [activeTab, setActiveTab] = useState('visualization');
  
  // Tab configuration
  const tabs = [
    { id: 'visualization', label: 'Visualization' },
    { id: 'explanation', label: 'Explanation' },
    { id: 'code', label: 'Implementation' },
    { id: 'applications', label: 'Applications' }
  ];
  
  return (
    <>
      <Head>
        <title>CSDE Trinity Equation | NovaFuse</title>
        <meta name="description" content="Experience the CSDE Trinity Equation visualization, demonstrating how mathematical constants drive the Cyber-Safety Dynamic Engine." />
      </Head>
      
      <MainLayout>
        <PageHeader
          title="CSDE Trinity Equation"
          subtitle="πG + ϕD + (ℏ+c⁻¹)R"
          gradient="from-green-500 via-blue-500 to-purple-500"
        />
        
        <Section className="mb-8">
          <Card className="mb-6 p-6">
            <h2 className="text-2xl font-semibold mb-4">About the CSDE Trinity Equation</h2>
            <p className="mb-4">
              The CSDE Trinity Equation (CSDE_Trinity = πG + ϕD + (ℏ+c⁻¹)R) is the mathematical foundation 
              of NovaFuse's Cyber-Safety Dynamic Engine. This equation combines universal constants with 
              the three components of the Trinity architecture.
            </p>
            <p>
              Each component of the equation corresponds to a member of the Trinity:
              Father (π-driven Governance), Son (ϕ-tuned Detection), and Spirit ((ℏ+c⁻¹)-framed Response).
            </p>
          </Card>
          
          <Tabs
            tabs={tabs}
            activeTab={activeTab}
            onChange={setActiveTab}
            className="mb-6"
          />
          
          {activeTab === 'visualization' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-gray-900 rounded-lg overflow-hidden"
              style={{ height: '700px' }}
            >
              <CSDETrinityEquation width="100%" height="100%" />
            </motion.div>
          )}
          
          {activeTab === 'explanation' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="mb-6 p-6">
                <h3 className="text-xl font-semibold mb-4">The Mathematical Constants</h3>
                <p className="mb-4">
                  The CSDE Trinity Equation leverages universal constants to create a balanced and harmonious system:
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                  <div className="bg-green-100 dark:bg-green-900/30 p-4 rounded-lg border border-green-200 dark:border-green-800">
                    <h4 className="text-lg font-medium text-green-700 dark:text-green-400 mb-2">π (Pi) - Governance</h4>
                    <p className="text-sm mb-3">The Father component, representing structure and order.</p>
                    <ul className="list-disc list-inside text-sm space-y-1">
                      <li><strong>Value:</strong> 3.14159...</li>
                      <li><strong>Application:</strong> Compliance cycles and policy design</li>
                      <li><strong>Effect:</strong> Creates stable, circular patterns that ensure governance is comprehensive</li>
                    </ul>
                  </div>
                  
                  <div className="bg-blue-100 dark:bg-blue-900/30 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h4 className="text-lg font-medium text-blue-700 dark:text-blue-400 mb-2">ϕ (Phi) - Detection</h4>
                    <p className="text-sm mb-3">The Son component, representing harmony and balance.</p>
                    <ul className="list-disc list-inside text-sm space-y-1">
                      <li><strong>Value:</strong> 0.61803...</li>
                      <li><strong>Application:</strong> Threat weighting and signal processing</li>
                      <li><strong>Effect:</strong> Creates optimal detection patterns that minimize false positives/negatives</li>
                    </ul>
                  </div>
                  
                  <div className="bg-purple-100 dark:bg-purple-900/30 p-4 rounded-lg border border-purple-200 dark:border-purple-800 md:col-span-2">
                    <h4 className="text-lg font-medium text-purple-700 dark:text-purple-400 mb-2">(ℏ+c⁻¹) - Response</h4>
                    <p className="text-sm mb-3">The Spirit component, combining quantum precision (ℏ) with speed (c⁻¹).</p>
                    <ul className="list-disc list-inside text-sm space-y-1">
                      <li><strong>ℏ (Planck's constant):</strong> 6.62607015×10⁻³⁴ - Represents quantum precision</li>
                      <li><strong>c⁻¹ (Inverse speed of light):</strong> 1/299,792,458 - Represents response time limits</li>
                      <li><strong>Application:</strong> Response time limits and mitigation surface</li>
                      <li><strong>Effect:</strong> Creates a balance between speed and precision in response actions</li>
                    </ul>
                  </div>
                </div>
              </Card>
              
              <Card className="p-6">
                <h3 className="text-xl font-semibold mb-4">The 18/82 Principle in the CSDE Trinity</h3>
                <p className="mb-4">
                  The 18/82 principle is applied across all three Trinity components:
                </p>
                <ul className="list-disc list-inside space-y-2 mb-4">
                  <li><strong>Governance (πG):</strong> 18% policy design, 82% compliance enforcement</li>
                  <li><strong>Detection (ϕD):</strong> 18% baseline signals, 82% threat weight</li>
                  <li><strong>Response (ℏ+c⁻¹)R:</strong> 18% reaction time, 82% mitigation surface</li>
                </ul>
                <p>
                  This consistent application of the 18/82 principle creates a harmonious system where
                  each component reinforces the others, creating a self-sustaining Cyber-Safety engine.
                </p>
              </Card>
            </motion.div>
          )}
          
          {activeTab === 'code' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="mb-6 p-6">
                <h3 className="text-xl font-semibold mb-4">Implementation Details</h3>
                <p className="mb-4">
                  The CSDE Trinity Equation is implemented in code as follows:
                </p>
                
                <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mb-4">
                  <h4 className="text-lg font-medium mb-2">CSDE Trinity Calculation</h4>
                  <pre className="bg-gray-200 dark:bg-gray-900 p-3 rounded text-xs overflow-x-auto">
                    {`// Constants
const PI = Math.PI;
const PHI = 0.618033988749895;
const PLANCK = 6.62607015e-34;
const SPEED_OF_LIGHT = 299792458;
const SPEED_OF_LIGHT_INVERSE = 1 / SPEED_OF_LIGHT;

// Calculate CSDE Trinity value
const calculateCSDETrinity = (governance, detection, response) => {
  const governanceTerm = PI * governance;
  const detectionTerm = PHI * detection;
  const responseTerm = (PLANCK + SPEED_OF_LIGHT_INVERSE) * response;
  
  return governanceTerm + detectionTerm + responseTerm;
};`}
                  </pre>
                </div>
                
                <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mb-4">
                  <h4 className="text-lg font-medium mb-2">18/82 Implementation</h4>
                  <pre className="bg-gray-200 dark:bg-gray-900 p-3 rounded text-xs overflow-x-auto">
                    {`// Apply 18/82 principle to governance
const applyGovernancePrinciple = (policyDesign, complianceEnforcement) => {
  // Validate 18/82 ratio
  const total = policyDesign + complianceEnforcement;
  const policyRatio = policyDesign / total;
  const complianceRatio = complianceEnforcement / total;
  
  if (Math.abs(policyRatio - 0.18) > 0.01 || Math.abs(complianceRatio - 0.82) > 0.01) {
    console.warn('Governance not following 18/82 principle');
  }
  
  return {
    policyEffectiveness: policyDesign * PI,
    complianceEffectiveness: complianceEnforcement * PI,
    totalGovernance: (policyDesign + complianceEnforcement) * PI
  };
};`}
                  </pre>
                </div>
                
                <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                  <h4 className="text-lg font-medium mb-2">Trinity Balance Calculation</h4>
                  <pre className="bg-gray-200 dark:bg-gray-900 p-3 rounded text-xs overflow-x-auto">
                    {`// Calculate Trinity balance
const calculateTrinityBalance = (governance, detection, response) => {
  const governanceTerm = PI * governance;
  const detectionTerm = PHI * detection;
  const responseTerm = (PLANCK + SPEED_OF_LIGHT_INVERSE) * response;
  
  const total = governanceTerm + detectionTerm + responseTerm;
  
  // Calculate ideal ratios based on universal constants
  const idealGovernanceRatio = PI / (PI + PHI + (PLANCK + SPEED_OF_LIGHT_INVERSE));
  const idealDetectionRatio = PHI / (PI + PHI + (PLANCK + SPEED_OF_LIGHT_INVERSE));
  const idealResponseRatio = (PLANCK + SPEED_OF_LIGHT_INVERSE) / (PI + PHI + (PLANCK + SPEED_OF_LIGHT_INVERSE));
  
  // Calculate actual ratios
  const actualGovernanceRatio = governanceTerm / total;
  const actualDetectionRatio = detectionTerm / total;
  const actualResponseRatio = responseTerm / total;
  
  // Calculate balance (1.0 = perfect balance)
  const balance = 1.0 - (
    Math.abs(actualGovernanceRatio - idealGovernanceRatio) +
    Math.abs(actualDetectionRatio - idealDetectionRatio) +
    Math.abs(actualResponseRatio - idealResponseRatio)
  ) / 2;
  
  return balance;
};`}
                  </pre>
                </div>
              </Card>
            </motion.div>
          )}
          
          {activeTab === 'applications' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="mb-6 p-6">
                <h3 className="text-xl font-semibold mb-4">Practical Applications</h3>
                <p className="mb-4">
                  The CSDE Trinity Equation has numerous practical applications in cybersecurity and compliance:
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 className="text-lg font-medium mb-2">Risk Assessment</h4>
                    <p className="text-sm">
                      The equation provides a mathematical framework for assessing cyber risk by balancing:
                    </p>
                    <ul className="list-disc list-inside text-sm mt-2">
                      <li>Policy effectiveness (πG)</li>
                      <li>Threat detection capability (ϕD)</li>
                      <li>Incident response readiness ((ℏ+c⁻¹)R)</li>
                    </ul>
                  </div>
                  
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 className="text-lg font-medium mb-2">Resource Allocation</h4>
                    <p className="text-sm">
                      Organizations can use the equation to optimize resource allocation:
                    </p>
                    <ul className="list-disc list-inside text-sm mt-2">
                      <li>18% to policy development, 82% to enforcement</li>
                      <li>18% to baseline monitoring, 82% to threat analysis</li>
                      <li>18% to response planning, 82% to mitigation capabilities</li>
                    </ul>
                  </div>
                  
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 className="text-lg font-medium mb-2">Compliance Automation</h4>
                    <p className="text-sm">
                      The equation drives NovaFuse's compliance automation by:
                    </p>
                    <ul className="list-disc list-inside text-sm mt-2">
                      <li>Creating circular compliance cycles (π-driven)</li>
                      <li>Optimizing control selection (ϕ-tuned)</li>
                      <li>Balancing speed and precision in remediation ((ℏ+c⁻¹)-framed)</li>
                    </ul>
                  </div>
                  
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 className="text-lg font-medium mb-2">Performance Metrics</h4>
                    <p className="text-sm">
                      The equation provides key performance indicators:
                    </p>
                    <ul className="list-disc list-inside text-sm mt-2">
                      <li>Trinity Balance Score (overall system health)</li>
                      <li>Component Effectiveness Ratings</li>
                      <li>18/82 Principle Adherence Metrics</li>
                    </ul>
                  </div>
                </div>
                
                <div className="mt-8">
                  <h4 className="text-lg font-medium mb-2">Real-World Impact</h4>
                  <p className="mb-4">
                    Organizations implementing the CSDE Trinity Equation have experienced:
                  </p>
                  <ul className="list-disc list-inside space-y-2">
                    <li><strong>30% reduction in false positives/negatives</strong> in threat detection</li>
                    <li><strong>42% improvement in incident response times</strong></li>
                    <li><strong>78% increase in compliance automation efficiency</strong></li>
                    <li><strong>3,142x performance improvement</strong> across multiple domains</li>
                  </ul>
                </div>
              </Card>
              
              <div className="flex justify-center mt-8">
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => setActiveTab('visualization')}
                >
                  Experience the Visualization
                </Button>
              </div>
            </motion.div>
          )}
        </Section>
      </MainLayout>
    </>
  );
};

export default CSDETrinityEquationDemo;
