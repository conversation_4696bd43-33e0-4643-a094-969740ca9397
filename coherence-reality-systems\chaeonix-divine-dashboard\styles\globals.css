@tailwind base;
@tailwind components;
@tailwind utilities;

/* CHAEONIX Divine Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700;800&display=swap');

:root {
  --phi: 1.618033988749;
  --phi-inverse: 0.618033988749;
  --phi-squared-inverse: 0.236067977499;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  background: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%);
  min-height: 100vh;
}

body {
  color: rgb(255, 255, 255);
  background: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%);
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.8);
}

/* Divine Animations */
@keyframes divine-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes coherence-wave {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) scale(1.02);
    opacity: 0.9;
  }
}

@keyframes fibonacci-spiral {
  0% {
    transform: rotate(0deg) scale(1);
  }
  25% {
    transform: rotate(90deg) scale(var(--phi));
  }
  50% {
    transform: rotate(180deg) scale(1);
  }
  75% {
    transform: rotate(270deg) scale(var(--phi-inverse));
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

@keyframes divine-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(139, 92, 246, 0.6);
  }
}

/* Utility Classes */
.divine-pulse {
  animation: divine-pulse 2s ease-in-out infinite;
}

.coherence-wave {
  animation: coherence-wave 3s ease-in-out infinite;
}

.fibonacci-spiral {
  animation: fibonacci-spiral 8s linear infinite;
}

.divine-glow {
  animation: divine-glow 2s ease-in-out infinite;
}

/* Glass Morphism */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Divine Grid */
.divine-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(calc(300px * var(--phi-inverse)), 1fr));
  gap: calc(1rem * var(--phi-inverse));
}

/* Phi-based Spacing */
.phi-spacing > * + * {
  margin-top: calc(1rem * var(--phi-inverse));
}

.phi-padding {
  padding: calc(1rem * var(--phi-inverse));
}

.phi-margin {
  margin: calc(1rem * var(--phi-inverse));
}

/* Custom Components */
.chaeonix-button {
  @apply px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-lg;
  @apply hover:from-purple-700 hover:to-pink-700 transition-all duration-300;
  @apply shadow-lg hover:shadow-xl transform hover:scale-105;
}

.chaeonix-card {
  @apply bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6;
  @apply hover:border-purple-400/50 transition-all duration-300;
}

.chaeonix-input {
  @apply w-full px-4 py-2 bg-gray-900/50 border border-gray-600 rounded-lg;
  @apply text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none;
  @apply transition-all duration-300;
}

.chaeonix-select {
  @apply w-full px-4 py-2 bg-gray-900/50 border border-gray-600 rounded-lg;
  @apply text-white focus:border-purple-400 focus:outline-none;
  @apply transition-all duration-300;
}

/* Market Domain Colors */
.stocks-theme {
  --primary-color: #ef4444;
  --secondary-color: #dc2626;
  --accent-color: #fca5a5;
}

.crypto-theme {
  --primary-color: #f59e0b;
  --secondary-color: #d97706;
  --accent-color: #fcd34d;
}

.forex-theme {
  --primary-color: #10b981;
  --secondary-color: #059669;
  --accent-color: #6ee7b7;
}

/* Engine Status Indicators */
.engine-operational {
  @apply bg-green-500/20 border-green-400 text-green-400;
}

.engine-warning {
  @apply bg-yellow-500/20 border-yellow-400 text-yellow-400;
}

.engine-error {
  @apply bg-red-500/20 border-red-400 text-red-400;
}

.engine-unknown {
  @apply bg-gray-500/20 border-gray-400 text-gray-400;
}

/* Coherence Level Indicators */
.coherence-divine {
  @apply text-purple-400 bg-purple-500/20 border-purple-400;
}

.coherence-extreme {
  @apply text-green-400 bg-green-500/20 border-green-400;
}

.coherence-high {
  @apply text-yellow-400 bg-yellow-500/20 border-yellow-400;
}

.coherence-moderate {
  @apply text-orange-400 bg-orange-500/20 border-orange-400;
}

.coherence-low {
  @apply text-red-400 bg-red-500/20 border-red-400;
}

/* Responsive Design */
@media (max-width: 768px) {
  .divine-grid {
    grid-template-columns: 1fr;
  }
  
  .chaeonix-card {
    @apply p-4;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .chaeonix-card {
    @apply border-gray-300 bg-white;
  }
}

/* Dark Mode Overrides */
@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .divine-pulse,
  .coherence-wave,
  .fibonacci-spiral,
  .divine-glow {
    animation: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .chaeonix-card {
    @apply border-white bg-black;
  }
  
  .chaeonix-button {
    @apply bg-white text-black border-2 border-black;
  }
}
