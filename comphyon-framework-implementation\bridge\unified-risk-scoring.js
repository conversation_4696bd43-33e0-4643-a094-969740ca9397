/**
 * Unified Risk Scoring
 * 
 * This module implements the Unified Risk Scoring component of the Bridge.
 * It calculates cross-domain risk scores and provides a unified view of risk.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * UnifiedRiskScoring class
 */
class UnifiedRiskScoring extends EventEmitter {
  /**
   * Create a new UnifiedRiskScoring instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      enableMetrics: true,
      historySize: 100, // Number of historical data points to keep
      domainWeights: {
        cyber: 0.33,
        financial: 0.33,
        biological: 0.34
      },
      thresholds: {
        risk: {
          low: 0.3,
          medium: 0.6,
          high: 0.8,
          critical: 0.95
        }
      },
      ...options
    };
    
    // Initialize state
    this.state = {
      unifiedRiskScore: 0.5,
      domainRiskScores: {
        cyber: 0.5,
        financial: 0.5,
        biological: 0.5
      },
      riskStatus: 'medium', // low, medium, high, critical
      riskFactors: new Map(), // id -> risk factor object
      riskHistory: [],
      domainCorrelations: {
        cyberFinancial: 0.5,
        cyberBiological: 0.3,
        financialBiological: 0.4
      },
      isRunning: false,
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalUpdates: 0,
      riskFactorsAdded: 0,
      riskFactorsRemoved: 0,
      statusChanges: 0
    };
    
    if (this.options.enableLogging) {
      console.log('UnifiedRiskScoring initialized');
    }
  }
  
  /**
   * Start the unified risk scoring
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('UnifiedRiskScoring is already running');
      }
      return false;
    }
    
    this.state.isRunning = true;
    
    if (this.options.enableLogging) {
      console.log('UnifiedRiskScoring started');
    }
    
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the unified risk scoring
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('UnifiedRiskScoring is not running');
      }
      return false;
    }
    
    this.state.isRunning = false;
    
    if (this.options.enableLogging) {
      console.log('UnifiedRiskScoring stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Update domain risk score
   * @param {string} domain - Domain (cyber, financial, biological)
   * @param {number} score - Risk score (0-1)
   * @param {Object} metadata - Additional metadata
   * @returns {number} - Updated unified risk score
   */
  updateDomainRiskScore(domain, score, metadata = {}) {
    const startTime = performance.now();
    
    // Validate domain
    if (!['cyber', 'financial', 'biological'].includes(domain)) {
      throw new Error(`Invalid domain: ${domain}`);
    }
    
    // Validate score
    if (typeof score !== 'number' || score < 0 || score > 1) {
      throw new Error('Risk score must be a number between 0 and 1');
    }
    
    // Update domain risk score
    this.state.domainRiskScores[domain] = score;
    this.state.lastUpdateTime = Date.now();
    
    // Update unified risk score
    this._updateUnifiedRiskScore();
    
    // Add to history
    this.state.riskHistory.push({
      unifiedRiskScore: this.state.unifiedRiskScore,
      domainRiskScores: { ...this.state.domainRiskScores },
      riskStatus: this.state.riskStatus,
      timestamp: Date.now(),
      updatedDomain: domain,
      metadata
    });
    
    // Limit history size
    if (this.state.riskHistory.length > this.options.historySize) {
      this.state.riskHistory.shift();
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    
    // Emit update event
    this.emit('risk-update', {
      unifiedRiskScore: this.state.unifiedRiskScore,
      domainRiskScores: { ...this.state.domainRiskScores },
      riskStatus: this.state.riskStatus,
      updatedDomain: domain,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`UnifiedRiskScoring: Updated ${domain} risk score to ${score.toFixed(4)}, unified risk score is ${this.state.unifiedRiskScore.toFixed(4)} (${this.state.riskStatus})`);
    }
    
    return this.state.unifiedRiskScore;
  }
  
  /**
   * Add risk factor
   * @param {Object} factor - Risk factor object
   * @returns {Object} - Added risk factor
   */
  addRiskFactor(factor) {
    const startTime = performance.now();
    
    if (!factor || typeof factor !== 'object') {
      throw new Error('Risk factor must be an object');
    }
    
    if (!factor.id) {
      factor.id = `risk-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    
    // Set default values
    factor = {
      domain: 'cross-domain', // cyber, financial, biological, cross-domain
      type: 'generic', // specific risk factor type
      impact: 0.5, // 0-1 (impact on risk score)
      likelihood: 0.5, // 0-1 (likelihood of occurrence)
      status: 'active', // active, mitigated, resolved
      addedAt: Date.now(),
      ...factor
    };
    
    // Add to state
    this.state.riskFactors.set(factor.id, factor);
    
    // Update risk scores
    this._applyRiskFactors();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.riskFactorsAdded++;
    
    // Emit event
    this.emit('risk-factor-added', factor);
    
    if (this.options.enableLogging) {
      console.log(`UnifiedRiskScoring: Added ${factor.domain} risk factor ${factor.id}`);
    }
    
    return factor;
  }
  
  /**
   * Update risk factor
   * @param {string} factorId - Risk factor ID
   * @param {Object} updates - Updates to apply
   * @returns {Object} - Updated risk factor
   */
  updateRiskFactor(factorId, updates) {
    const startTime = performance.now();
    
    if (!factorId || !this.state.riskFactors.has(factorId)) {
      throw new Error(`Risk factor ${factorId} not found`);
    }
    
    if (!updates || typeof updates !== 'object') {
      throw new Error('Updates must be an object');
    }
    
    // Get current factor
    const factor = this.state.riskFactors.get(factorId);
    
    // Apply updates
    const updatedFactor = {
      ...factor,
      ...updates,
      updatedAt: Date.now()
    };
    
    // Update state
    this.state.riskFactors.set(factorId, updatedFactor);
    
    // Update risk scores
    this._applyRiskFactors();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    
    // Emit event
    this.emit('risk-factor-updated', updatedFactor);
    
    if (this.options.enableLogging) {
      console.log(`UnifiedRiskScoring: Updated risk factor ${factorId}`);
    }
    
    return updatedFactor;
  }
  
  /**
   * Remove risk factor
   * @param {string} factorId - Risk factor ID
   * @returns {boolean} - Success status
   */
  removeRiskFactor(factorId) {
    const startTime = performance.now();
    
    if (!factorId || !this.state.riskFactors.has(factorId)) {
      return false;
    }
    
    // Get factor
    const factor = this.state.riskFactors.get(factorId);
    
    // Remove from state
    this.state.riskFactors.delete(factorId);
    
    // Update risk scores
    this._applyRiskFactors();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.riskFactorsRemoved++;
    
    // Emit event
    this.emit('risk-factor-removed', factor);
    
    if (this.options.enableLogging) {
      console.log(`UnifiedRiskScoring: Removed risk factor ${factorId}`);
    }
    
    return true;
  }
  
  /**
   * Update domain correlations
   * @param {Object} correlations - Domain correlations
   * @returns {Object} - Updated correlations
   */
  updateDomainCorrelations(correlations) {
    if (!correlations || typeof correlations !== 'object') {
      throw new Error('Correlations must be an object');
    }
    
    const {
      cyberFinancial = this.state.domainCorrelations.cyberFinancial,
      cyberBiological = this.state.domainCorrelations.cyberBiological,
      financialBiological = this.state.domainCorrelations.financialBiological
    } = correlations;
    
    // Update state
    this.state.domainCorrelations = {
      cyberFinancial: this._clamp(cyberFinancial),
      cyberBiological: this._clamp(cyberBiological),
      financialBiological: this._clamp(financialBiological)
    };
    
    // Update unified risk score
    this._updateUnifiedRiskScore();
    
    // Emit update event
    this.emit('correlation-update', {
      domainCorrelations: this.state.domainCorrelations,
      timestamp: Date.now()
    });
    
    return this.state.domainCorrelations;
  }
  
  /**
   * Get unified risk score
   * @returns {number} - Unified risk score
   */
  getUnifiedRiskScore() {
    return this.state.unifiedRiskScore;
  }
  
  /**
   * Get domain risk scores
   * @returns {Object} - Domain risk scores
   */
  getDomainRiskScores() {
    return { ...this.state.domainRiskScores };
  }
  
  /**
   * Get risk status
   * @returns {string} - Risk status
   */
  getRiskStatus() {
    return this.state.riskStatus;
  }
  
  /**
   * Get domain correlations
   * @returns {Object} - Domain correlations
   */
  getDomainCorrelations() {
    return { ...this.state.domainCorrelations };
  }
  
  /**
   * Get risk factors
   * @param {string} domain - Optional domain filter
   * @returns {Array} - Risk factors
   */
  getRiskFactors(domain) {
    const factors = Array.from(this.state.riskFactors.values());
    
    if (domain) {
      return factors.filter(f => f.domain === domain);
    }
    
    return factors;
  }
  
  /**
   * Get risk history
   * @param {number} limit - Maximum number of history items to return
   * @returns {Array} - Risk history
   */
  getRiskHistory(limit = 10) {
    return this.state.riskHistory.slice(0, limit);
  }
  
  /**
   * Get current state
   * @returns {Object} - Current state
   */
  getState() {
    return {
      unifiedRiskScore: this.state.unifiedRiskScore,
      domainRiskScores: { ...this.state.domainRiskScores },
      riskStatus: this.state.riskStatus,
      domainCorrelations: { ...this.state.domainCorrelations },
      riskFactorCount: this.state.riskFactors.size,
      riskHistory: [...this.state.riskHistory],
      isRunning: this.state.isRunning,
      lastUpdateTime: this.state.lastUpdateTime
    };
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Update unified risk score
   * @private
   */
  _updateUnifiedRiskScore() {
    const { domainRiskScores, domainCorrelations } = this.state;
    const { domainWeights } = this.options;
    
    // Calculate base weighted score
    const baseScore = (
      domainWeights.cyber * domainRiskScores.cyber +
      domainWeights.financial * domainRiskScores.financial +
      domainWeights.biological * domainRiskScores.biological
    );
    
    // Calculate correlation factor
    // Higher correlations increase risk (systemic risk)
    const correlationFactor = (
      domainCorrelations.cyberFinancial +
      domainCorrelations.cyberBiological +
      domainCorrelations.financialBiological
    ) / 3;
    
    // Apply 18/82 principle to base score and correlation factor
    const unifiedRiskScore = (
      0.18 * baseScore +
      0.82 * correlationFactor
    );
    
    // Update state
    this.state.unifiedRiskScore = this._clamp(unifiedRiskScore);
    
    // Update risk status
    this._updateRiskStatus();
  }
  
  /**
   * Update risk status
   * @private
   */
  _updateRiskStatus() {
    const { unifiedRiskScore } = this.state;
    const { thresholds } = this.options;
    
    let newStatus = 'low';
    
    if (unifiedRiskScore >= thresholds.risk.critical) {
      newStatus = 'critical';
    } else if (unifiedRiskScore >= thresholds.risk.high) {
      newStatus = 'high';
    } else if (unifiedRiskScore >= thresholds.risk.medium) {
      newStatus = 'medium';
    }
    
    // If status changed, update metrics and emit event
    if (newStatus !== this.state.riskStatus) {
      this.state.riskStatus = newStatus;
      this.metrics.statusChanges++;
      
      // Emit status change event
      this.emit('status-change', {
        riskStatus: this.state.riskStatus,
        timestamp: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`UnifiedRiskScoring: Risk status changed to ${this.state.riskStatus}`);
      }
    }
  }
  
  /**
   * Apply risk factors to domain risk scores
   * @private
   */
  _applyRiskFactors() {
    // Start with baseline risk scores
    const baselineScores = {
      cyber: 0.3,
      financial: 0.3,
      biological: 0.3
    };
    
    // Apply risk factors
    for (const factor of this.state.riskFactors.values()) {
      if (factor.status === 'active') {
        const riskContribution = factor.impact * factor.likelihood;
        
        if (factor.domain === 'cross-domain') {
          // Apply to all domains
          baselineScores.cyber += riskContribution * 0.33;
          baselineScores.financial += riskContribution * 0.33;
          baselineScores.biological += riskContribution * 0.34;
        } else if (baselineScores[factor.domain] !== undefined) {
          // Apply to specific domain
          baselineScores[factor.domain] += riskContribution;
        }
      }
    }
    
    // Ensure scores are between 0 and 1
    for (const domain in baselineScores) {
      baselineScores[domain] = this._clamp(baselineScores[domain]);
    }
    
    // Update domain risk scores
    this.state.domainRiskScores = baselineScores;
    
    // Update unified risk score
    this._updateUnifiedRiskScore();
  }
  
  /**
   * Calculate risk score for a specific scenario
   * @param {Object} scenario - Risk scenario
   * @returns {Object} - Risk assessment
   */
  calculateScenarioRisk(scenario) {
    if (!scenario || typeof scenario !== 'object') {
      throw new Error('Scenario must be an object');
    }
    
    const {
      cyberRisk = this.state.domainRiskScores.cyber,
      financialRisk = this.state.domainRiskScores.financial,
      biologicalRisk = this.state.domainRiskScores.biological,
      cyberFinancialCorrelation = this.state.domainCorrelations.cyberFinancial,
      cyberBiologicalCorrelation = this.state.domainCorrelations.cyberBiological,
      financialBiologicalCorrelation = this.state.domainCorrelations.financialBiological
    } = scenario;
    
    // Calculate base weighted score
    const baseScore = (
      this.options.domainWeights.cyber * cyberRisk +
      this.options.domainWeights.financial * financialRisk +
      this.options.domainWeights.biological * biologicalRisk
    );
    
    // Calculate correlation factor
    const correlationFactor = (
      cyberFinancialCorrelation +
      cyberBiologicalCorrelation +
      financialBiologicalCorrelation
    ) / 3;
    
    // Apply 18/82 principle
    const riskScore = (
      0.18 * baseScore +
      0.82 * correlationFactor
    );
    
    // Determine risk status
    let riskStatus = 'low';
    if (riskScore >= this.options.thresholds.risk.critical) {
      riskStatus = 'critical';
    } else if (riskScore >= this.options.thresholds.risk.high) {
      riskStatus = 'high';
    } else if (riskScore >= this.options.thresholds.risk.medium) {
      riskStatus = 'medium';
    }
    
    return {
      riskScore: this._clamp(riskScore),
      riskStatus,
      domainRiskScores: {
        cyber: this._clamp(cyberRisk),
        financial: this._clamp(financialRisk),
        biological: this._clamp(biologicalRisk)
      },
      domainCorrelations: {
        cyberFinancial: this._clamp(cyberFinancialCorrelation),
        cyberBiological: this._clamp(cyberBiologicalCorrelation),
        financialBiological: this._clamp(financialBiologicalCorrelation)
      },
      timestamp: Date.now()
    };
  }
  
  /**
   * Clamp value between 0 and 1
   * @param {number} value - Value to clamp
   * @returns {number} - Clamped value
   * @private
   */
  _clamp(value) {
    return Math.max(0, Math.min(1, value));
  }
}

module.exports = UnifiedRiskScoring;
