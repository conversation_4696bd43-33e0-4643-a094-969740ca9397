/**
 * IP Restriction Routes
 */

const express = require('express');
const router = express.Router();
const IpRestrictionController = require('../controllers/IpRestrictionController');
const { authenticate, hasRole } = require('../middleware/authMiddleware');

// All routes require authentication and admin role
router.use(authenticate);
router.use(hasRole('admin'));

// Get IP restrictions
router.get('/', (req, res, next) => {
  IpRestrictionController.getRestrictions(req, res, next);
});

// Update IP restrictions configuration
router.put('/config', (req, res, next) => {
  IpRestrictionController.updateConfig(req, res, next);
});

// Add IP to allowlist
router.post('/allowlist', (req, res, next) => {
  IpRestrictionController.addToAllowlist(req, res, next);
});

// Remove IP from allowlist
router.delete('/allowlist', (req, res, next) => {
  IpRestrictionController.removeFromAllowlist(req, res, next);
});

// Add IP to blocklist
router.post('/blocklist', (req, res, next) => {
  IpRestrictionController.addToBlocklist(req, res, next);
});

// Remove IP from blocklist
router.delete('/blocklist', (req, res, next) => {
  IpRestrictionController.removeFromBlocklist(req, res, next);
});

// Add rule
router.post('/rules', (req, res, next) => {
  IpRestrictionController.addRule(req, res, next);
});

// Remove rule
router.delete('/rules', (req, res, next) => {
  IpRestrictionController.removeRule(req, res, next);
});

// Reset to default
router.post('/reset', (req, res, next) => {
  IpRestrictionController.resetToDefault(req, res, next);
});

module.exports = router;
