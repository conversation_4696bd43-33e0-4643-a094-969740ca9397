/**
 * NovaFuse Application
 *
 * This is the main application component that wraps all pages.
 * It provides global context providers and layout components.
 */

import React from 'react';
import { AppProps } from 'next/app';
import { useRouter } from 'next/router';
import { AuthProvider } from '../contexts/AuthContext';
import AppLayout from '../components/layout/AppLayout';
import '../styles/globals.css';

function NovaFuseApp({ Component, pageProps }: AppProps) {
  const router = useRouter();

  // Pages that don't need the app layout
  const noLayoutPages = ['/login', '/forgot-password', '/reset-password'];
  const needsLayout = !noLayoutPages.includes(router.pathname);

  return (
    <AuthProvider>
      {needsLayout ? (
        <AppLayout>
          <Component {...pageProps} />
        </AppLayout>
      ) : (
        <Component {...pageProps} />
      )}
    </AuthProvider>
  );
}

export default NovaFuseApp;
