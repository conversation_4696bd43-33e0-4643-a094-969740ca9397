<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>12+1 Universal Novas</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 800px;
            height: 600px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            box-sizing: border-box;
        }
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
        }
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            box-sizing: border-box;
            font-size: 14px;
            line-height: 1.2;
        }
        .component-label {
            font-weight: bold;
            margin-bottom: 4px;
        }
        .component-number {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 20px;
            height: 20px;
            background-color: #333;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
        }
        .arrow {
            position: absolute;
            background-color: #333;
            width: 2px;
        }
        .legend {
            position: absolute;
            right: 10px;
            bottom: -90px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            width: 200px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border: 1px solid #333;
        }
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: -90px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>FIG. 3: 12+1 Universal Novas</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 600px; left: 25px; top: 20px;">
            <div class="container-label">CYBER-SAFETY FRAMEWORK: 12 UNIVERSAL NOVAS</div>
        </div>
        
        <!-- Top Row - Novas 1-4 -->
        <div class="component-box" style="left: 100px; top: 80px; width: 150px; height: 100px;">
            <div class="component-number">301</div>
            <div class="component-label">Nova 1</div>
            NovaCore
        </div>
        
        <div class="component-box" style="left: 270px; top: 80px; width: 150px; height: 100px;">
            <div class="component-number">302</div>
            <div class="component-label">Nova 2</div>
            NovaShield
        </div>
        
        <div class="component-box" style="left: 440px; top: 80px; width: 150px; height: 100px;">
            <div class="component-number">303</div>
            <div class="component-label">Nova 3</div>
            NovaTrack
        </div>
        
        <div class="component-box" style="left: 610px; top: 80px; width: 150px; height: 100px;">
            <div class="component-number">304</div>
            <div class="component-label">Nova 4</div>
            NovaLearn
        </div>
        
        <!-- Middle Row - Novas 5-8 -->
        <div class="component-box" style="left: 100px; top: 200px; width: 150px; height: 100px;">
            <div class="component-number">305</div>
            <div class="component-label">Nova 5</div>
            NovaView
        </div>
        
        <div class="component-box" style="left: 270px; top: 200px; width: 150px; height: 100px;">
            <div class="component-number">306</div>
            <div class="component-label">Nova 6</div>
            NovaFlowX
        </div>
        
        <div class="component-box" style="left: 440px; top: 200px; width: 150px; height: 100px;">
            <div class="component-number">307</div>
            <div class="component-label">Nova 7</div>
            NovaPulse+
        </div>
        
        <div class="component-box" style="left: 610px; top: 200px; width: 150px; height: 100px;">
            <div class="component-number">308</div>
            <div class="component-label">Nova 8</div>
            NovaProof
        </div>
        
        <!-- Bottom Row - Novas 9-12 -->
        <div class="component-box" style="left: 100px; top: 320px; width: 150px; height: 100px;">
            <div class="component-number">309</div>
            <div class="component-label">Nova 9</div>
            NovaThink
        </div>
        
        <div class="component-box" style="left: 270px; top: 320px; width: 150px; height: 100px;">
            <div class="component-number">310</div>
            <div class="component-label">Nova 10</div>
            NovaConnect
        </div>
        
        <div class="component-box" style="left: 440px; top: 320px; width: 150px; height: 100px;">
            <div class="component-number">311</div>
            <div class="component-label">Nova 11</div>
            NovaVision
        </div>
        
        <div class="component-box" style="left: 610px; top: 320px; width: 150px; height: 100px;">
            <div class="component-number">312</div>
            <div class="component-label">Nova 12</div>
            NovaDNA
        </div>
        
        <!-- NovaStore - The 13th Nova -->
        <div class="container-box" style="width: 700px; height: 120px; left: 80px; top: 450px;">
            <div class="container-label">THE 13TH NOVA</div>
        </div>
        
        <div class="component-box" style="left: 355px; top: 490px; width: 150px; height: 60px;">
            <div class="component-number">313</div>
            <div class="component-label">Nova 13</div>
            NovaStore
        </div>
        
        <!-- Connecting arrows -->
        <div class="arrow" style="left: 175px; top: 180px; height: 20px;"></div>
        <div class="arrow" style="left: 345px; top: 180px; height: 20px;"></div>
        <div class="arrow" style="left: 515px; top: 180px; height: 20px;"></div>
        <div class="arrow" style="left: 685px; top: 180px; height: 20px;"></div>
        
        <div class="arrow" style="left: 175px; top: 300px; height: 20px;"></div>
        <div class="arrow" style="left: 345px; top: 300px; height: 20px;"></div>
        <div class="arrow" style="left: 515px; top: 300px; height: 20px;"></div>
        <div class="arrow" style="left: 685px; top: 300px; height: 20px;"></div>
        
        <div class="arrow" style="left: 175px; top: 420px; height: 30px;"></div>
        <div class="arrow" style="left: 345px; top: 420px; height: 30px;"></div>
        <div class="arrow" style="left: 515px; top: 420px; height: 30px;"></div>
        <div class="arrow" style="left: 685px; top: 420px; height: 30px;"></div>
        
        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>12 Universal Novas</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>13th Nova (NovaStore)</div>
            </div>
        </div>
        
        <!-- Inventor Label -->
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
