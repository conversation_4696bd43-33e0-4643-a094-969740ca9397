const express = require("express");
const cors = require("cors");
const morgan = require("morgan");

const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan("dev"));

// Create router with prefix
const router = express.Router();
app.use('/security', router);

// Direct health check route (no prefix)
app.get("/health", (req, res) => {
  res.json({ status: "healthy", timestamp: new Date().toISOString() });
});

// Sample data
const vulnerabilities = [
  {
    id: "vuln-001",
    title: "SQL Injection in Login Form",
    severity: "critical",
    status: "open",
    discoveredDate: "2024-10-01",
    affectedSystems: ["web-app-1", "api-gateway"],
    description: "A SQL injection vulnerability was found in the login form...",
    remediation: "Update input validation and use parameterized queries."
  },
  {
    id: "vuln-002",
    title: "Outdated SSL Certificate",
    severity: "medium",
    status: "in_progress",
    discoveredDate: "2024-09-15",
    affectedSystems: ["web-app-2"],
    description: "The SSL certificate for web-app-2 is using an outdated encryption algorithm...",
    remediation: "Update SSL certificate to use modern encryption standards."
  },
  {
    id: "vuln-003",
    title: "Weak Password Policy",
    severity: "high",
    status: "open",
    discoveredDate: "2024-09-20",
    affectedSystems: ["identity-provider", "user-management"],
    description: "The current password policy does not enforce sufficient complexity...",
    remediation: "Update password policy to require stronger passwords."
  },
  {
    id: "vuln-004",
    title: "Unpatched Server Vulnerability",
    severity: "high",
    status: "closed",
    discoveredDate: "2024-08-10",
    closedDate: "2024-08-15",
    affectedSystems: ["database-server-1"],
    description: "Database server is running an unpatched version with known vulnerabilities...",
    remediation: "Apply latest security patches to the database server."
  }
];

const securityPolicies = [
  {
    id: "sec-pol-001",
    title: "Data Classification Policy",
    category: "data-security",
    status: "active",
    lastUpdated: "2024-07-15",
    description: "This policy defines how data should be classified based on sensitivity..."
  },
  {
    id: "sec-pol-002",
    title: "Access Control Policy",
    category: "access-management",
    status: "active",
    lastUpdated: "2024-08-20",
    description: "This policy defines the requirements for access control..."
  },
  {
    id: "sec-pol-003",
    title: "Incident Response Policy",
    category: "incident-management",
    status: "draft",
    lastUpdated: "2024-10-05",
    description: "This policy outlines the procedures for responding to security incidents..."
  }
];

const securityIncidents = [
  {
    id: "inc-001",
    title: "Phishing Attack",
    severity: "high",
    status: "resolved",
    reportedDate: "2024-09-10",
    resolvedDate: "2024-09-12",
    affectedUsers: 5,
    description: "Several employees received sophisticated phishing emails...",
    resolution: "Affected accounts were reset, additional security training provided."
  },
  {
    id: "inc-002",
    title: "Unauthorized Access Attempt",
    severity: "critical",
    status: "investigating",
    reportedDate: "2024-10-05",
    resolvedDate: null,
    affectedUsers: 0,
    description: "Multiple failed login attempts detected from suspicious IP addresses...",
    resolution: null
  },
  {
    id: "inc-003",
    title: "Data Leak",
    severity: "critical",
    status: "mitigated",
    reportedDate: "2024-08-15",
    resolvedDate: "2024-08-20",
    affectedUsers: 150,
    description: "Customer data was accidentally exposed through a misconfigured API...",
    resolution: "API configuration corrected, affected customers notified."
  }
];

// Routes

// Vulnerabilities
router.get("/vulnerabilities", (req, res) => {
  let result = [...vulnerabilities];

  // Apply filters
  if (req.query.severity) {
    result = result.filter(v => v.severity === req.query.severity);
  }

  if (req.query.status) {
    result = result.filter(v => v.status === req.query.status);
  }

  res.json({
    data: result,
    total: result.length,
    page: 1,
    limit: result.length
  });
});

router.get("/vulnerabilities/:id", (req, res) => {
  const vulnerability = vulnerabilities.find(v => v.id === req.params.id);

  if (!vulnerability) {
    return res.status(404).json({ error: "Vulnerability not found" });
  }

  res.json({ data: vulnerability });
});

// Security Policies
router.get("/policies", (req, res) => {
  let result = [...securityPolicies];

  // Apply filters
  if (req.query.category) {
    result = result.filter(p => p.category === req.query.category);
  }

  if (req.query.status) {
    result = result.filter(p => p.status === req.query.status);
  }

  res.json({
    data: result,
    total: result.length,
    page: 1,
    limit: result.length
  });
});

router.get("/policies/:id", (req, res) => {
  const policy = securityPolicies.find(p => p.id === req.params.id);

  if (!policy) {
    return res.status(404).json({ error: "Security policy not found" });
  }

  res.json({ data: policy });
});

// Security Incidents
router.get("/incidents", (req, res) => {
  let result = [...securityIncidents];

  // Apply filters
  if (req.query.severity) {
    result = result.filter(i => i.severity === req.query.severity);
  }

  if (req.query.status) {
    result = result.filter(i => i.status === req.query.status);
  }

  res.json({
    data: result,
    total: result.length,
    page: 1,
    limit: result.length
  });
});

router.get("/incidents/:id", (req, res) => {
  const incident = securityIncidents.find(i => i.id === req.params.id);

  if (!incident) {
    return res.status(404).json({ error: "Security incident not found" });
  }

  res.json({ data: incident });
});

// Security Scan endpoint
router.post("/scan", (req, res) => {
  // Simulate a security scan
  const scanId = "scan-" + Math.floor(Math.random() * 1000);
  const timestamp = new Date().toISOString();

  // Generate random findings
  const findings = [];
  const severities = ["low", "medium", "high", "critical"];
  const numFindings = Math.floor(Math.random() * 5) + 1;

  for (let i = 0; i < numFindings; i++) {
    findings.push({
      id: `finding-${scanId}-${i}`,
      severity: severities[Math.floor(Math.random() * severities.length)],
      description: `Sample security finding ${i + 1}`,
      recommendation: "This is a sample recommendation for this finding."
    });
  }

  res.json({
    data: {
      scanId,
      timestamp,
      status: "completed",
      findings,
      summary: {
        total: findings.length,
        critical: findings.filter(f => f.severity === "critical").length,
        high: findings.filter(f => f.severity === "high").length,
        medium: findings.filter(f => f.severity === "medium").length,
        low: findings.filter(f => f.severity === "low").length
      }
    }
  });
});

// Start server
app.listen(port, () => {
  console.log(`Security mock API running on port ${port}`);
});
