/**
 * Simple Schema Controller
 * 
 * This controller provides API endpoints for simple schema management.
 */

const { normalizeSchema } = require('./simpleSchema');
const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');

/**
 * Get schema for entity
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getSchema = async (req, res) => {
  try {
    const { entity } = req.params;
    
    // Check if we have a schema file
    const schemaPath = path.join(__dirname, 'schemas', `${entity.toLowerCase()}.json`);
    
    try {
      // Try to read schema
      const schemaContent = await fs.readFile(schemaPath, 'utf8');
      const schema = JSON.parse(schemaContent);
      
      // Normalize schema
      const normalizedSchema = normalizeSchema(schema);
      
      return res.json({
        success: true,
        data: normalizedSchema
      });
    } catch (err) {
      if (err.code === 'ENOENT') {
        return res.status(404).json({
          success: false,
          error: `No schema found for ${entity}`
        });
      } else {
        throw err;
      }
    }
  } catch (error) {
    logger.error(`Error getting schema for ${req.params.entity}`, error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get schema'
    });
  }
};

/**
 * Save schema
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const saveSchema = async (req, res) => {
  try {
    const schema = req.body;
    
    // Validate schema
    if (!schema.entity || !Array.isArray(schema.fields)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid schema format'
      });
    }
    
    // Create schemas directory if it doesn't exist
    const schemasDir = path.join(__dirname, 'schemas');
    try {
      await fs.mkdir(schemasDir, { recursive: true });
    } catch (err) {
      if (err.code !== 'EEXIST') {
        throw err;
      }
    }
    
    // Normalize schema
    const normalizedSchema = normalizeSchema(schema);
    
    // Save schema
    const schemaPath = path.join(schemasDir, `${schema.entity.toLowerCase()}.json`);
    await fs.writeFile(schemaPath, JSON.stringify(normalizedSchema, null, 2), 'utf8');
    
    res.json({
      success: true,
      message: `Schema for ${schema.entity} saved successfully`,
      data: normalizedSchema
    });
  } catch (error) {
    logger.error('Error saving schema', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to save schema'
    });
  }
};

/**
 * List all schemas
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const listSchemas = async (req, res) => {
  try {
    const schemas = [];
    
    // Get schemas
    const schemasDir = path.join(__dirname, 'schemas');
    try {
      const files = await fs.readdir(schemasDir);
      
      for (const file of files) {
        if (file.endsWith('.json')) {
          const content = await fs.readFile(path.join(schemasDir, file), 'utf8');
          const schema = JSON.parse(content);
          
          schemas.push({
            entity: schema.entity,
            entityPlural: schema.entityPlural || `${schema.entity}s`,
            apiEndpoint: schema.apiEndpoint || `/api/v1/${schema.entity.toLowerCase()}s`,
            fieldCount: schema.fields.length
          });
        }
      }
    } catch (err) {
      if (err.code !== 'ENOENT') {
        throw err;
      }
    }
    
    res.json({
      success: true,
      data: schemas
    });
  } catch (error) {
    logger.error('Error listing schemas', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to list schemas'
    });
  }
};

/**
 * Delete schema
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteSchema = async (req, res) => {
  try {
    const { entity } = req.params;
    
    // Check if schema exists
    const schemaPath = path.join(__dirname, 'schemas', `${entity.toLowerCase()}.json`);
    
    try {
      await fs.access(schemaPath);
    } catch (err) {
      return res.status(404).json({
        success: false,
        error: `No schema found for ${entity}`
      });
    }
    
    // Delete schema
    await fs.unlink(schemaPath);
    
    res.json({
      success: true,
      message: `Schema for ${entity} deleted successfully`
    });
  } catch (error) {
    logger.error(`Error deleting schema for ${req.params.entity}`, error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to delete schema'
    });
  }
};

module.exports = {
  getSchema,
  saveSchema,
  listSchemas,
  deleteSchema
};
