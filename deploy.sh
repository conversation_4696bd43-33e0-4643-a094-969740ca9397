#!/bin/bash

# NovaFuse Deployment Script
# This script deploys the NovaFuse application using Docker Compose

# Set the environment
ENV=${1:-development}
echo "Deploying NovaFuse to $ENV environment..."

# Pull the latest code
echo "Pulling the latest code..."
git pull

# Build and start the containers
echo "Building and starting the containers..."
docker-compose -f docker-compose.yml -f docker-compose.$ENV.yml up -d --build

# Wait for the containers to start
echo "Waiting for the containers to start..."
sleep 10

# Check the status of the containers
echo "Checking the status of the containers..."
docker-compose ps

echo "Deployment completed successfully!"
