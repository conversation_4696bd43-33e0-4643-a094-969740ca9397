@echo off
setlocal enabledelayedexpansion

set "file=d:\\novafuse-api-superstore\\coherence-reality-systems\\Comphyology Master Archive\\3. APPENDIX A - MATH FOUNDATION\\The Comphyological Dictionary 1st Edition.md"
set "backup=%file%.backup.%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%"

:: Create backup
copy "%file%" "%backup%" >nul
echo Created backup at: %backup%

:: Replace emojis and special characters
powershell -Command "(Get-Content -Path '%file%' -Raw) -replace 'ðŸ”‘', '💡' -replace 'ðŸ§¬', '📋' -replace 'ðŸ§ ', '⚠️' -replace 'ðŸ§', '⚠️' -replace 'Îº', 'κ' -replace 'Ã—', '×' | Set-Content -Path '%file%' -Encoding UTF8"

echo Fixed emoji and special character rendering in the dictionary file.
echo Please review the changes in the file: %file%
