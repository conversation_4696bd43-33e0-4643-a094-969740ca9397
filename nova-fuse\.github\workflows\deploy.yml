name: Deploy

on:
  push:
    branches: [ master ]
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 16
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test

      - name: Build
        run: npm run build

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ghcr.io/${{ github.repository_owner }}/nova-fuse:latest,ghcr.io/${{ github.repository_owner }}/nova-fuse:${{ github.sha }}

      # Add deployment steps here (e.g., deploy to Kubernetes, AWS, etc.)
      # Example for Kubernetes:
      # - name: Deploy to Kubernetes
      #   uses: Azure/k8s-deploy@v1
      #   with:
      #     namespace: novafuse
      #     manifests: |
      #       kubernetes/deployment.yaml
      #       kubernetes/service.yaml
      #     images: |
      #       novafuse/nova-fuse:${{ github.sha }}
