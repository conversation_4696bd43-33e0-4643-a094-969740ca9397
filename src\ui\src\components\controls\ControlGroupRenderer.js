import React from 'react';
import { Grid } from '@mui/material';
import ControlGroup from './ControlGroup';
import Control from './Control';
import { useControl } from '../../contexts/ControlContext';

/**
 * ControlGroupRenderer component
 * 
 * Renders a group of controls
 */
function ControlGroupRenderer({ groupId, title, columns = 1 }) {
  const { getControls } = useControl();
  const controls = getControls(groupId);
  const controlIds = Object.keys(controls);

  if (controlIds.length === 0) {
    return null;
  }

  return (
    <ControlGroup title={title}>
      <Grid container spacing={2}>
        {controlIds.map((controlId) => (
          <Grid item xs={12} md={12 / columns} key={controlId}>
            <Control controlId={controlId} control={controls[controlId]} />
          </Grid>
        ))}
      </Grid>
    </ControlGroup>
  );
}

export default ControlGroupRenderer;
