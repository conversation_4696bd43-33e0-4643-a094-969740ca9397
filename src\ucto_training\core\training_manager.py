"""
Training Manager for the Universal Compliance Training Optimizer.

This module provides functionality for managing compliance training programs.
"""

import os
import json
import logging
import uuid
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrainingManager:
    """
    Manager for compliance training programs.
    
    This class is responsible for creating, retrieving, updating, and managing
    compliance training programs.
    """
    
    def __init__(self, training_dir: Optional[str] = None):
        """
        Initialize the Training Manager.
        
        Args:
            training_dir: Path to a directory for storing training data
        """
        logger.info("Initializing Training Manager")
        
        # Set the training directory
        self.training_dir = training_dir or os.path.join(os.getcwd(), 'training_data')
        
        # Create the training directory if it doesn't exist
        os.makedirs(self.training_dir, exist_ok=True)
        
        # Dictionary to store training programs in memory
        self.programs: Dict[str, Dict[str, Any]] = {}
        
        # Load training programs from disk
        self._load_programs_from_disk()
        
        logger.info(f"Training Manager initialized with {len(self.programs)} programs")
    
    def create_program(self, program_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new training program.
        
        Args:
            program_data: The training program data
            
        Returns:
            The created training program
            
        Raises:
            ValueError: If the program data is invalid
        """
        logger.info("Creating new training program")
        
        # Validate the program data
        self._validate_program_data(program_data)
        
        # Generate a unique program ID
        program_id = str(uuid.uuid4())
        
        # Create the program object
        program = {
            'id': program_id,
            'name': program_data['name'],
            'description': program_data.get('description', ''),
            'framework': program_data['framework'],
            'modules': program_data.get('modules', []),
            'target_audience': program_data.get('target_audience', []),
            'status': program_data.get('status', 'draft'),
            'created_at': self._get_current_timestamp(),
            'updated_at': self._get_current_timestamp()
        }
        
        # Store the program in memory
        self.programs[program_id] = program
        
        # Store the program on disk
        self._save_program_to_disk(program)
        
        logger.info(f"Training program created: {program_id}")
        
        return program
    
    def get_program(self, program_id: str) -> Dict[str, Any]:
        """
        Get a training program.
        
        Args:
            program_id: The ID of the program
            
        Returns:
            The training program
            
        Raises:
            ValueError: If the program does not exist
        """
        logger.info(f"Getting training program: {program_id}")
        
        if program_id not in self.programs:
            raise ValueError(f"Training program not found: {program_id}")
        
        return self.programs[program_id]
    
    def update_program(self, program_id: str, program_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a training program.
        
        Args:
            program_id: The ID of the program
            program_data: The updated program data
            
        Returns:
            The updated training program
            
        Raises:
            ValueError: If the program does not exist
        """
        logger.info(f"Updating training program: {program_id}")
        
        # Check if the program exists
        if program_id not in self.programs:
            raise ValueError(f"Training program not found: {program_id}")
        
        # Get the existing program
        program = self.programs[program_id]
        
        # Update the program data
        if 'name' in program_data:
            program['name'] = program_data['name']
        
        if 'description' in program_data:
            program['description'] = program_data['description']
        
        if 'framework' in program_data:
            program['framework'] = program_data['framework']
        
        if 'modules' in program_data:
            program['modules'] = program_data['modules']
        
        if 'target_audience' in program_data:
            program['target_audience'] = program_data['target_audience']
        
        if 'status' in program_data:
            program['status'] = program_data['status']
        
        # Update the updated_at timestamp
        program['updated_at'] = self._get_current_timestamp()
        
        # Store the updated program on disk
        self._save_program_to_disk(program)
        
        logger.info(f"Training program updated: {program_id}")
        
        return program
    
    def delete_program(self, program_id: str) -> None:
        """
        Delete a training program.
        
        Args:
            program_id: The ID of the program
            
        Raises:
            ValueError: If the program does not exist
        """
        logger.info(f"Deleting training program: {program_id}")
        
        # Check if the program exists
        if program_id not in self.programs:
            raise ValueError(f"Training program not found: {program_id}")
        
        # Remove the program from memory
        del self.programs[program_id]
        
        # Remove the program from disk
        self._delete_program_from_disk(program_id)
        
        logger.info(f"Training program deleted: {program_id}")
    
    def get_all_programs(self) -> List[Dict[str, Any]]:
        """
        Get all training programs.
        
        Returns:
            List of training programs
        """
        logger.info("Getting all training programs")
        
        return list(self.programs.values())
    
    def get_programs_by_framework(self, framework: str) -> List[Dict[str, Any]]:
        """
        Get all training programs for a framework.
        
        Args:
            framework: The framework
            
        Returns:
            List of training programs for the framework
        """
        logger.info(f"Getting training programs for framework: {framework}")
        
        return [p for p in self.programs.values() if p.get('framework') == framework]
    
    def get_programs_by_status(self, status: str) -> List[Dict[str, Any]]:
        """
        Get all training programs with a specific status.
        
        Args:
            status: The status
            
        Returns:
            List of training programs with the specified status
        """
        logger.info(f"Getting training programs with status: {status}")
        
        return [p for p in self.programs.values() if p.get('status') == status]
    
    def add_module_to_program(self, program_id: str, module_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a module to a training program.
        
        Args:
            program_id: The ID of the program
            module_data: The module data
            
        Returns:
            The updated training program
            
        Raises:
            ValueError: If the program does not exist
            ValueError: If the module data is invalid
        """
        logger.info(f"Adding module to training program: {program_id}")
        
        # Check if the program exists
        if program_id not in self.programs:
            raise ValueError(f"Training program not found: {program_id}")
        
        # Validate the module data
        if 'name' not in module_data:
            raise ValueError("Module name is required")
        
        # Generate a unique module ID
        module_id = str(uuid.uuid4())
        
        # Create the module object
        module = {
            'id': module_id,
            'name': module_data['name'],
            'description': module_data.get('description', ''),
            'content_type': module_data.get('content_type', 'text'),
            'content': module_data.get('content', ''),
            'duration': module_data.get('duration', 0),
            'order': module_data.get('order', 0)
        }
        
        # Get the existing program
        program = self.programs[program_id]
        
        # Add the module to the program
        program['modules'].append(module)
        
        # Update the updated_at timestamp
        program['updated_at'] = self._get_current_timestamp()
        
        # Store the updated program on disk
        self._save_program_to_disk(program)
        
        logger.info(f"Module added to training program: {program_id}")
        
        return program
    
    def _validate_program_data(self, program_data: Dict[str, Any]) -> None:
        """
        Validate training program data.
        
        Args:
            program_data: The program data to validate
            
        Raises:
            ValueError: If the program data is invalid
        """
        # Check required fields
        if 'name' not in program_data:
            raise ValueError("Program name is required")
        
        if 'framework' not in program_data:
            raise ValueError("Program framework is required")
        
        # Validate status if provided
        if 'status' in program_data:
            valid_statuses = ['draft', 'active', 'inactive', 'archived']
            if program_data['status'] not in valid_statuses:
                raise ValueError(f"Invalid status: {program_data['status']}")
    
    def _load_programs_from_disk(self) -> None:
        """Load training programs from disk."""
        try:
            # Get all JSON files in the training directory
            program_files = [f for f in os.listdir(self.training_dir) if f.endswith('.json')]
            
            for program_file in program_files:
                try:
                    # Load the program from disk
                    file_path = os.path.join(self.training_dir, program_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        program = json.load(f)
                    
                    # Store the program in memory
                    program_id = program.get('id')
                    
                    if program_id:
                        self.programs[program_id] = program
                        logger.info(f"Loaded training program from disk: {program_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load training program from {program_file}: {e}")
            
            logger.info(f"Loaded {len(self.programs)} training programs from disk")
        
        except Exception as e:
            logger.error(f"Failed to load training programs from disk: {e}")
    
    def _save_program_to_disk(self, program: Dict[str, Any]) -> None:
        """
        Save a training program to disk.
        
        Args:
            program: The program to save
        """
        try:
            # Get the program ID
            program_id = program.get('id')
            
            if not program_id:
                raise ValueError("Program ID is missing")
            
            # Create the file path
            file_path = os.path.join(self.training_dir, f"{program_id}.json")
            
            # Save the program to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(program, f, indent=2)
            
            logger.info(f"Saved training program to disk: {program_id}")
        
        except Exception as e:
            logger.error(f"Failed to save training program to disk: {e}")
    
    def _delete_program_from_disk(self, program_id: str) -> None:
        """
        Delete a training program from disk.
        
        Args:
            program_id: The ID of the program
        """
        try:
            # Create the file path
            file_path = os.path.join(self.training_dir, f"{program_id}.json")
            
            # Check if the file exists
            if os.path.exists(file_path):
                # Delete the file
                os.remove(file_path)
                logger.info(f"Deleted training program from disk: {program_id}")
            else:
                logger.warning(f"Training program file not found on disk: {program_id}")
        
        except Exception as e:
            logger.error(f"Failed to delete training program from disk: {e}")
    
    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.
        
        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
