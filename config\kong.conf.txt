# Kong configuration file

database = postgres
pg_host = kong-database
pg_port = 5432
pg_user = kong
pg_password = kong_password
pg_database = kong

admin_listen = 0.0.0.0:8001, 0.0.0.0:8444 ssl
proxy_listen = 0.0.0.0:8000, 0.0.0.0:8443 ssl

plugins = bundled,key-auth,rate-limiting,cors

# Enable the Admin API
admin_access_log = /dev/stdout
admin_error_log = /dev/stderr
proxy_access_log = /dev/stdout
proxy_error_log = /dev/stderr