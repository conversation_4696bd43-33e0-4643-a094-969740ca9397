@echo off
REM NovaAgent WASM Build Script - Following Exact Guide

echo 🔧 Building NovaAgent WASM Module...

REM Step 3: Build and Optimize (from guide)

REM Build WASM module
echo 📦 Building WASM target...
cargo build --target wasm32-unknown-unknown --release
if %ERRORLEVEL% NEQ 0 (
    echo ❌ WASM build failed
    pause
    exit /b 1
)

REM Generate JS bindings
echo 🔗 Generating JS bindings...
wasm-bindgen target\wasm32-unknown-unknown\release\novabrowser.wasm --out-dir .\pkg --target web --no-typescript
if %ERRORLEVEL% NEQ 0 (
    echo ❌ JS binding generation failed
    pause
    exit /b 1
)

REM Optimize with wasm-opt (binaryen)
echo ⚡ Optimizing WASM...
wasm-opt -Oz -o pkg\nova_agent_optimized.wasm pkg\novabrowser_bg.wasm
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ WASM optimization failed (continuing without optimization)
)

echo ✅ NovaAgent WASM build complete!
echo 📁 Output: .\pkg\
echo 🎯 Expected: Ψ-Scan functionality ready for browser integration
pause
