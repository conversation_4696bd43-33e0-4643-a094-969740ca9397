<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patent Diagram Generator - Mermaid to SVG Converter</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        :root {
            --uspto-black: #000000;
            --uspto-white: #FFFFFF;
            --uspto-gray1: #333333;
            --uspto-gray2: #666666;
            --uspto-gray3: #999999;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            color: var(--uspto-gray1);
        }
        
        .header {
            background-color: var(--uspto-gray1);
            color: white;
            padding: 25px;
            margin: -20px -20px 30px -20px;
            border-radius: 5px 5px 0 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 1.8em;
        }
        
        .header p {
            margin: 10px 0 0;
            opacity: 0.9;
        }
        
        .controls {
            background-color: white;
            padding: 25px;
            border-radius: 5px;
            margin-bottom: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .section-title {
            color: var(--uspto-gray1);
            border-bottom: 2px solid var(--uspto-gray3);
            padding-bottom: 10px;
            margin-top: 0;
        }
        
        .file-input-container {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed var(--uspto-gray3);
            border-radius: 5px;
            text-align: center;
            background-color: #fafafa;
            transition: all 0.3s ease;
        }
        
        .file-input-container:hover {
            border-color: var(--uspto-gray2);
            background-color: #f5f5f5;
        }
        
        .file-input {
            display: none;
        }
        
        .file-label {
            display: inline-block;
            padding: 15px 25px;
            background-color: var(--uspto-gray1);
            color: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }
        
        .file-label:hover {
            background-color: var(--uspto-gray2);
            transform: translateY(-2px);
        }
        
        .file-count {
            display: block;
            margin-top: 15px;
            color: var(--uspto-gray2);
            font-size: 0.9em;
        }
        
        .progress-container {
            width: 100%;
            background-color: #f0f0f0;
            border-radius: 5px;
            margin: 20px 0;
            overflow: hidden;
            display: none;
        }
        
        .progress-bar {
            width: 0%;
            height: 30px;
            background-color: var(--uspto-gray1);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: width 0.3s ease;
        }
        
        .status {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        
        .status.success {
            background-color: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #c8e6c9;
        }
        
        .status.error {
            background-color: #ffebee;
            color: #c62828;
            border: 1px solid #ffcdd2;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: var(--uspto-gray1);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .btn:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .btn-primary {
            background-color: var(--uspto-gray1);
        }
        
        .btn-download {
            background-color: #2e7d32;
        }
        
        .btn-download-all {
            background-color: #1565c0;
        }
        
        .btn:disabled {
            background-color: var(--uspto-gray3);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .action-buttons {
            margin: 20px 0;
            text-align: center;
        }
        
        .diagrams-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .diagram-container {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
        }
        
        .diagram-header {
            background-color: var(--uspto-gray1);
            color: white;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .diagram-title {
            margin: 0;
            font-size: 1.1em;
            font-weight: bold;
        }
        
        .diagram-number {
            background-color: white;
            color: var(--uspto-gray1);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .diagram-content {
            padding: 30px;
            min-height: 500px;  /* Increased from 300px */
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            position: relative;
            overflow: auto;  /* Add scrollbars if needed */
            transform-origin: top left;
            transform: scale(1.5);  /* Scale up the entire diagram */
        }
        
        .diagram-actions {
            padding: 15px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .diagram-filename {
            font-family: monospace;
            color: var(--uspto-gray2);
            font-size: 0.9em;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .no-diagrams {
            text-align: center;
            padding: 50px;
            color: var(--uspto-gray2);
            font-style: italic;
            grid-column: 1 / -1;
        }
        
        .footer {
            margin-top: 50px;
            text-align: center;
            color: var(--uspto-gray2);
            font-size: 0.9em;
            padding: 20px;
            border-top: 1px solid #eee;
        }
        
        @media (max-width: 768px) {
            .diagrams-container {
                grid-template-columns: 1fr;
            }
            
            .controls {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Patent Diagram Generator</h1>
        <p>Convert Mermaid diagrams to USPTO-compliant SVGs</p>
    </div>
    
    <div class="controls">
        <h2 class="section-title">1. Select Mermaid Diagram Files</h2>
        
        <div class="file-input-container" id="drop-zone">
            <input type="file" id="mermaid-files" class="file-input" multiple accept=".mmd,.md,.txt">
            <label for="mermaid-files" class="file-label">
                <span id="browse-text">Browse Files</span>
                <span class="file-count" id="file-count">or drag and drop files here</span>
            </label>
        </div>
        
        <div class="progress-container" id="batch-progress">
            <div class="progress-bar" id="progress-bar">0%</div>
        </div>
        
        <div id="status" class="status"></div>
        
        <div class="action-buttons">
            <button id="process-btn" class="btn btn-primary" disabled>
                <span class="btn-text">Process Files</span>
            </button>
            <button id="download-all-btn" class="btn btn-download-all" disabled>
                Download All SVGs
            </button>
            <button id="clear-all-btn" class="btn" disabled>
                Clear All
            </button>
        </div>
    </div>
    
    <h2 class="section-title">2. Processed Diagrams</h2>
    <div id="diagrams-container" class="diagrams-container">
        <div class="no-diagrams">
            No diagrams processed yet. Select Mermaid files to begin.
        </div>
    </div>
    
    <div class="footer">
        <p>Patent Diagram Generator v1.0.0 | For USPTO Patent Submission</p>
    </div>

    <script>
        // Initialize Mermaid with version 11.8.0 compatible settings
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            themeVariables: {
                // Basic colors
                primaryColor: '#f4f4f4',
                primaryTextColor: '#333333',
                primaryBorderColor: '#333333',
                lineColor: '#333333',
                secondaryColor: '#e0e0e0',
                tertiaryColor: '#f4f4f4',
                // Text
                fontFamily: 'Arial, sans-serif',
                fontSize: '14px',
                textColor: '#333333',
                // Node styling
                nodeTextColor: '#333333',
                nodeBorder: '1px solid #333333',
                nodeBackground: '#f4f4f4',
                mainBkg: '#f4f4f4',
                // Cluster styling
                clusterBkg: '#e0e0e0',
                clusterBorder: '1px solid #333333',
                // Edge styling
                defaultLinkColor: '#333333',
                titleColor: '#333333',
                edgeLabelBackground: '#ffffff',
                edgeLabelBackgroundOpacity: 0.9,
                edgeLabelColor: '#333333',
                // Math text
                mathTextColor: '#333333',
                // Border radius
                borderRadius: 4,
                // Arrow settings
                arrowheadColor: '#333333',
                arrowMarkerColor: '#333333',
                // Font weights
                fontWeight: 'normal',
                // Opacity
                opacity: 0.9
            },
            // Flowchart specific settings
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis',
                defaultRenderer: 'dagre',
                nodeSpacing: 50,
                rankSpacing: 100,
                padding: 8,
                // Better text handling
                useHtmlLabels: true,
                // Better arrow handling
                arrowMarkerAbsolute: true,
                // Better edge routing
                rankDir: 'TB',
                // Better node placement
                ranker: 'network-simplex',
                // Better edge routing
                edgeMinimization: true,
                // Better label placement
                ranker: 'tight-tree',
                // Better edge routing
                edgeMinimization: true,
                // Better node placement
                nodeSpacing: 100,
                rankSpacing: 100,
                // Better label placement
                ranker: 'tight-tree',
                // Better edge routing
                edgeMinimization: true
            },
            gantt: {
                barHeight: 20,
                fontSize: 12,
                barGap: 4,
                topPadding: 25,
                leftPadding: 75,
                gridLineStartPadding: 35
            },
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: false
            },
            class: {
                titleTopMargin: 15,
                divider: {
                    margin: 5,
                    color: '#000000'
                },
                padding: 10
            }
        });

        // DOM Elements
        const fileInput = document.getElementById('mermaid-files');
        const dropZone = document.getElementById('drop-zone');
        const fileCount = document.getElementById('file-count');
        const processBtn = document.getElementById('process-btn');
        const downloadAllBtn = document.getElementById('download-all-btn');
        const clearAllBtn = document.getElementById('clear-all-btn');
        const progressBar = document.getElementById('progress-bar');
        const progressContainer = document.getElementById('batch-progress');
        const statusDiv = document.getElementById('status');
        const diagramsContainer = document.getElementById('diagrams-container');
        const browseText = document.getElementById('browse-text');
        
        // State
        let files = [];
        let processedDiagrams = [];
        
        // Event Listeners
        fileInput.addEventListener('change', handleFileSelect);
        processBtn.addEventListener('click', processBatch);
        downloadAllBtn.addEventListener('click', downloadAllDiagrams);
        clearAllBtn.addEventListener('click', clearAll);
        
        // Drag and drop
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });
        
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });
        
        dropZone.addEventListener('drop', handleDrop, false);
        
        // Functions
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        function highlight() {
            dropZone.style.borderColor = 'var(--uspto-gray1)';
            dropZone.style.backgroundColor = '#f0f0f0';
        }
        
        function unhighlight() {
            dropZone.style.borderColor = 'var(--uspto-gray3)';
            dropZone.style.backgroundColor = '#fafafa';
        }
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const droppedFiles = dt.files;
            
            if (droppedFiles.length > 0) {
                files = Array.from(droppedFiles);
                updateFileDisplay();
            }
        }
        
        function handleFileSelect(e) {
            files = Array.from(e.target.files);
            updateFileDisplay();
        }
        
        function updateFileDisplay() {
            if (files.length === 0) {
                fileCount.textContent = 'or drag and drop files here';
                browseText.textContent = 'Browse Files';
                processBtn.disabled = true;
                processBtn.querySelector('.btn-text').textContent = 'Process Files';
            } else {
                const fileText = files.length === 1 ? 'file' : 'files';
                fileCount.textContent = `${files.length} ${fileText} selected`;
                browseText.textContent = 'Add More Files';
                processBtn.disabled = false;
            }
            
            clearAllBtn.disabled = files.length === 0;
        }
        
        async function processBatch() {
            if (files.length === 0) {
                showStatus('Please select at least one file', 'error');
                return;
            }
            
            // Reset state
            processedDiagrams = [];
            updateProcessedDiagramsDisplay();
            
            // Show progress
            progressContainer.style.display = 'block';
            updateProgress(0);
            
            // Disable buttons during processing
            processBtn.disabled = true;
            processBtn.innerHTML = '<div class="loading"></div> Processing...';
            downloadAllBtn.disabled = true;
            clearAllBtn.disabled = true;
            
            // Process files
            let successCount = 0;
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                updateProgress(((i) / files.length) * 100);
                
                try {
                    const content = await readFileAsText(file);
                    const filename = file.name.replace(/\.(mmd|md|txt)$/i, '');
                    const diagramId = `diagram-${Date.now()}-${i}`;
                    
                    // Render diagram
                    try {
                        const { svg } = await mermaid.render(diagramId, content);
                        
                        processedDiagrams.push({
                            id: diagramId,
                            filename: filename,
                            content: svg,
                            index: i
                        });
                        
                        successCount++;
                    } catch (error) {
                        console.error(`Error rendering ${file.name}:`, error);
                        processedDiagrams.push({
                            id: diagramId,
                            filename: filename,
                            error: `Error rendering diagram: ${error.message}`,
                            index: i
                        });
                    }
                    
                    // Update UI after each diagram
                    updateProcessedDiagramsDisplay();
                    
                } catch (error) {
                    console.error(`Error processing ${file.name}:`, error);
                    processedDiagrams.push({
                        id: `error-${Date.now()}-${i}`,
                        filename: file.name,
                        error: `Error processing file: ${error.message}`,
                        index: i
                    });
                    updateProcessedDiagramsDisplay();
                }
            }
            
            // Update progress to 100%
            updateProgress(100);
            
            // Update UI
            processBtn.disabled = false;
            processBtn.innerHTML = '<span class="btn-text">Process Files</span>';
            downloadAllBtn.disabled = processedDiagrams.length === 0;
            clearAllBtn.disabled = false;
            
            // Show completion status
            if (successCount === files.length) {
                showStatus(`Successfully processed ${successCount} of ${files.length} files`, 'success');
            } else {
                const failedCount = files.length - successCount;
                showStatus(
                    `Processed ${successCount} of ${files.length} files (${failedCount} failed)`,
                    successCount > 0 ? 'success' : 'error'
                );
            }
        }
        
        function updateProgress(percent) {
            const rounded = Math.round(percent);
            progressBar.style.width = `${rounded}%`;
            progressBar.textContent = `${rounded}%`;
            
            // Update progress bar color based on completion
            if (rounded < 30) {
                progressBar.style.backgroundColor = '#e53935'; // Red
            } else if (rounded < 70) {
                progressBar.style.backgroundColor = '#fb8c00'; // Orange
            } else if (rounded < 100) {
                progressBar.style.backgroundColor = '#43a047'; // Green
            } else {
                progressBar.style.backgroundColor = '#2e7d32'; // Dark green
            }
        }
        
        function updateProcessedDiagramsDisplay() {
            if (processedDiagrams.length === 0) {
                diagramsContainer.innerHTML = `
                    <div class="no-diagrams">
                        No diagrams processed yet. Select Mermaid files and click "Process Files".
                    </div>
                `;
                return;
            }
            
            // Sort diagrams by their original index
            const sortedDiagrams = [...processedDiagrams].sort((a, b) => a.index - b.index);
            
            diagramsContainer.innerHTML = sortedDiagrams.map((diagram, idx) => {
                const figureNum = idx + 1;
                const displayName = diagram.filename.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                
                if (diagram.error) {
                    return `
                        <div class="diagram-container">
                            <div class="diagram-header">
                                <h3 class="diagram-title">Error Processing Diagram</h3>
                                <div class="diagram-number">${figureNum}</div>
                            </div>
                            <div class="diagram-content" style="color: #c62828; padding: 20px;">
                                <p><strong>File:</strong> ${diagram.filename}</p>
                                <p><strong>Error:</strong> ${diagram.error}</p>
                            </div>
                            <div class="diagram-actions">
                                <span class="diagram-filename">${diagram.filename}</span>
                            </div>
                        </div>
                    `;
                }
                
                return `
                    <div class="diagram-container">
                        <div class="diagram-header">
                            <h3 class="diagram-title">${displayName}</h3>
                            <div class="diagram-number">${figureNum}</div>
                        </div>
                        <div class="diagram-content">
                            ${diagram.content}
                        </div>
                        <div class="diagram-actions">
                            <span class="diagram-filename">${diagram.filename}</span>
                            <button class="btn btn-download" onclick="downloadDiagram(${idx})">
                                Download SVG
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        function downloadDiagram(index) {
            const diagram = processedDiagrams[index];
            if (!diagram || diagram.error) return;
            
            const svgElement = document.querySelector(`#${diagram.id} svg`);
            if (!svgElement) return;
            
            // Add XML declaration and SVG doctype
            const serializer = new XMLSerializer();
            let source = serializer.serializeToString(svgElement);
            
            // Ensure proper XML declaration and DOCTYPE
            if (!source.match(/^<\?xml/)) {
                source = '<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n' + source;
            }
            
            // Add viewBox and size attributes for better scaling
            source = source.replace(
                /<svg([^>]*)>/, 
                '<svg$1 width="100%" height="100%" viewBox="0 0 1200 800" preserveAspectRatio="xMidYMid meet">'
            );
            
            if (!source.match(/^<\!DOCTYPE/)) {
                const svgIndex = source.indexOf('<svg');
                source = source.substring(0, svgIndex) + 
                         '<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">\n' + 
                         source.substring(svgIndex);
            }
            
            // Create download link
            const blob = new Blob([source], { type: 'image/svg+xml;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `Figure_${index + 1}_${diagram.filename}.svg`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
        
        function downloadAllDiagrams() {
            if (processedDiagrams.length === 0) return;
            
            // Download each diagram one by one
            processedDiagrams.forEach((_, index) => {
                // Add a small delay between downloads to avoid browser blocking
                setTimeout(() => {
                    downloadDiagram(index);
                }, index * 300);
            });
        }
        
        function clearAll() {
            // Reset file input
            fileInput.value = '';
            files = [];
            processedDiagrams = [];
            
            // Reset UI
            updateFileDisplay();
            updateProcessedDiagramsDisplay();
            progressContainer.style.display = 'none';
            statusDiv.style.display = 'none';
            downloadAllBtn.disabled = true;
            clearAllBtn.disabled = true;
        }
        
        function showStatus(message, type = 'info') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                statusDiv.style.opacity = '0';
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                    statusDiv.style.opacity = '1';
                }, 500);
            }, 5000);
        }
        
        function readFileAsText(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = event => resolve(event.target.result);
                reader.onerror = error => reject(error);
                reader.readAsText(file);
            });
        }
        
        // Make functions available globally for inline event handlers
        window.downloadDiagram = downloadDiagram;
    </script>
</body>
</html>
