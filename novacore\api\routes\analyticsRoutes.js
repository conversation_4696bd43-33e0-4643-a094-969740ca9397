/**
 * Analytics Routes
 * 
 * This file defines the routes for the analytics API.
 */

const express = require('express');
const router = express.Router();
const { asyncHandler } = require('../middleware/asyncHandler');
const { authenticate, authorize } = require('../middleware/authMiddleware');
const analyticsController = require('../controllers/AnalyticsController');

/**
 * @route   POST /api/analytics/visualization-events
 * @desc    Record visualization events
 * @access  Public
 */
router.post(
  '/visualization-events',
  async<PERSON><PERSON><PERSON>(analyticsController.recordVisualizationEvents)
);

/**
 * @route   GET /api/analytics/visualization
 * @desc    Get visualization analytics
 * @access  Private
 */
router.get(
  '/visualization',
  authenticate,
  authorize('read:analytics'),
  as<PERSON><PERSON><PERSON><PERSON>(analyticsController.getVisualizationAnalytics)
);

module.exports = router;
