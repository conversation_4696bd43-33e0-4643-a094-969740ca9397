name: benches

on:
  push:
    branches:
      - dev
  workflow_dispatch:

env:
  RUST_BACKTRACE: 1
  CARGO_PROFILE_DEV_DEBUG: 0 # This would add unnecessary bloat to the target folder, decreasing cache efficiency.
  LC_ALL: en_US.UTF-8 # This prevents strace from changing its number format to use commas.

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  bench:
    strategy:
      fail-fast: false
      matrix:
        rust: [nightly]
        platform:
          - { target: x86_64-unknown-linux-gnu, os: ubuntu-latest }

    runs-on: ${{ matrix.platform.os }}

    steps:
      - uses: actions/checkout@v4

      - name: install Rust ${{ matrix.rust }}
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ matrix.rust }}
          components: rust-src
          targets: ${{ matrix.platform.target }}

      - name: Setup python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          architecture: x64

      - name: install dependencies
        run: |
          python -m pip install --upgrade pip
          sudo apt-get update
          sudo apt-get install -y --no-install-recommends \
            libwebkit2gtk-4.1-dev libayatana-appindicator3-dev \
            xvfb \
            at-spi2-core
          wget https://github.com/sharkdp/hyperfine/releases/download/v1.18.0/hyperfine_1.18.0_amd64.deb
          sudo dpkg -i hyperfine_1.18.0_amd64.deb
          pip install memory_profiler

      - uses: Swatinem/rust-cache@v2
        with:
          workspaces: |
            .
            bench/tests

      - name: run benchmarks
        run: |
          cargo +nightly build --release -Z build-std=std,panic_abort -Z build-std-features=panic_immediate_abort --target ${{ matrix.platform.target }} --manifest-path bench/tests/Cargo.toml
          xvfb-run --auto-servernum cargo run --manifest-path ./bench/Cargo.toml --bin run_benchmark

      - name: clone benchmarks_results
        if: github.repository == 'tauri-apps/wry' && github.ref == 'refs/heads/dev'
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.BENCH_PAT }}
          path: gh-pages
          repository: tauri-apps/benchmark_results

      - name: push new benchmarks
        if: github.repository == 'tauri-apps/wry' && github.ref == 'refs/heads/dev'
        run: |
          cargo run --manifest-path ./bench/Cargo.toml --bin build_benchmark_jsons
          cd gh-pages
          git pull
          git config user.name "tauri-bench"
          git config user.email "<EMAIL>"
          git add .
          git commit --message "Update WRY benchmarks"
          git push origin gh-pages

      - name: Print worker info
        run: |
          cat /proc/cpuinfo
          cat /proc/meminfo
