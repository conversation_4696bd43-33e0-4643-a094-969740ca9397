/**
 * Test script for GraphQL service
 */

const GraphQLService = require('./services/GraphQLService');

// Create an instance of the GraphQL service
const graphqlService = new GraphQLService();

// Test endpoint (public GraphQL API)
const endpoint = 'https://countries.trevorblades.com/';

// Simple query to test
const query = `
  query {
    countries {
      name
      code
      capital
    }
  }
`;

console.log('Testing GraphQL service...');
console.log(`Endpoint: ${endpoint}`);
console.log(`Query: ${query}`);

// Execute the query
graphqlService.executeQuery(endpoint, query)
  .then(result => {
    console.log('Query executed successfully!');
    console.log('Response status:', result.status);
    console.log('Response time:', result.responseTime, 'ms');
    
    if (result.data && result.data.data && result.data.data.countries) {
      console.log('Number of countries:', result.data.data.countries.length);
      console.log('First 3 countries:');
      result.data.data.countries.slice(0, 3).forEach(country => {
        console.log(`- ${country.name} (${country.code}): Capital - ${country.capital || 'N/A'}`);
      });
    } else {
      console.log('No countries data found in response');
      console.log('Response data:', JSON.stringify(result.data, null, 2));
    }
  })
  .catch(error => {
    console.error('Error executing query:', error.message);
  });

// Test schema introspection
console.log('\nTesting schema introspection...');
graphqlService.fetchSchema(endpoint)
  .then(schema => {
    console.log('Schema fetched successfully!');
    console.log('Query type:', schema.queryType.name);
    console.log('Number of types:', schema.types.length);
    
    // Find the Country type
    const countryType = schema.types.find(type => type.name === 'Country');
    if (countryType) {
      console.log('\nCountry type fields:');
      countryType.fields.forEach(field => {
        console.log(`- ${field.name}: ${field.type.name || 'Object'}`);
      });
    }
  })
  .catch(error => {
    console.error('Error fetching schema:', error.message);
  });

// Test sample query generation
console.log('\nTesting sample query generation...');
graphqlService.fetchSchema(endpoint)
  .then(schema => {
    const sampleQuery = graphqlService.generateSampleQuery(schema, 'query');
    console.log('Generated sample query:');
    console.log(sampleQuery);
  })
  .catch(error => {
    console.error('Error generating sample query:', error.message);
  });
