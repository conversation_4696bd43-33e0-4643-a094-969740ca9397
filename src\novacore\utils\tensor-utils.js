/**
 * tensor-utils.js
 * 
 * This file provides utility functions for working with tensors in the NovaCore system.
 * These utilities help with common tensor operations, conversions, and analysis.
 */

const Tensor = require('../models/Tensor');

/**
 * Create a tensor from a JSON object
 * @param {Object} json - The JSON representation of a tensor
 * @returns {Tensor} - A new tensor created from the JSON
 */
function tensorFromJSON(json) {
  return Tensor.fromJSON(json);
}

/**
 * Convert a tensor to a JSON string
 * @param {Tensor} tensor - The tensor to convert
 * @returns {String} - A JSON string representation of the tensor
 */
function tensorToJSONString(tensor) {
  return JSON.stringify(tensor.toJSON());
}

/**
 * Create a tensor from an array of objects
 * @param {Array} data - Array of objects to convert to a tensor
 * @param {Object} options - Options for creating the tensor
 * @returns {Tensor} - A new tensor created from the array
 */
function tensorFromArray(data, options = {}) {
  if (!Array.isArray(data)) {
    throw new Error('Data must be an array');
  }
  
  // Extract dimensions from the data if not provided
  const dimensions = options.dimensions || inferDimensionsFromArray(data);
  
  // Convert array to tensor data format
  const tensorData = {
    values: data,
    originalFormat: 'array',
    length: data.length
  };
  
  return new Tensor(tensorData, dimensions, options);
}

/**
 * Infer dimensions from an array of data
 * @param {Array} data - The array to infer dimensions from
 * @returns {Array} - Array of dimension objects
 */
function inferDimensionsFromArray(data) {
  const dimensions = [
    { name: 'items', size: data.length }
  ];
  
  // If the array items are objects with the same keys, add those as dimensions
  if (data.length > 0 && typeof data[0] === 'object' && data[0] !== null) {
    const keys = Object.keys(data[0]);
    
    // Check if all items have the same keys
    const allHaveSameKeys = data.every(item => {
      return keys.every(key => key in item);
    });
    
    if (allHaveSameKeys) {
      dimensions.push({ name: 'attributes', size: keys.length });
    }
  }
  
  return dimensions;
}

/**
 * Convert a tensor to an array
 * @param {Tensor} tensor - The tensor to convert
 * @returns {Array} - An array representation of the tensor
 */
function tensorToArray(tensor) {
  if (!(tensor instanceof Tensor)) {
    throw new Error('Input must be a Tensor instance');
  }
  
  // If the tensor was created from an array, return the original values
  if (tensor.data.originalFormat === 'array' && Array.isArray(tensor.data.values)) {
    return tensor.data.values;
  }
  
  // Otherwise, create a new array from the tensor data
  // This is a simplified implementation - in a real system, this would be more complex
  return [tensor.data];
}

/**
 * Merge multiple tensors into a single tensor
 * @param {Array<Tensor>} tensors - Array of tensors to merge
 * @param {Object} options - Merge options
 * @returns {Tensor} - A new tensor created by merging the input tensors
 */
function mergeTensors(tensors, options = {}) {
  if (!Array.isArray(tensors) || tensors.length === 0) {
    throw new Error('Tensors must be a non-empty array');
  }
  
  tensors.forEach(tensor => {
    if (!(tensor instanceof Tensor)) {
      throw new Error('All elements must be Tensor instances');
    }
  });
  
  const mergeMethod = options.method || 'concatenate';
  
  switch (mergeMethod) {
    case 'concatenate':
      return concatenateTensors(tensors, options);
    
    case 'average':
      return averageTensors(tensors, options);
    
    case 'stack':
      return stackTensors(tensors, options);
    
    default:
      throw new Error(`Unknown merge method: ${mergeMethod}`);
  }
}

/**
 * Concatenate tensors along a specified dimension
 * @param {Array<Tensor>} tensors - Array of tensors to concatenate
 * @param {Object} options - Concatenation options
 * @returns {Tensor} - A new tensor created by concatenating the input tensors
 */
function concatenateTensors(tensors, options = {}) {
  const dimensionName = options.dimension || 'items';
  
  // Create a new set of dimensions
  const firstTensor = tensors[0];
  const newDimensions = [...firstTensor.dimensions];
  
  // Find the dimension to concatenate along
  const dimensionIndex = newDimensions.findIndex(d => d.name === dimensionName);
  if (dimensionIndex === -1) {
    // If the dimension doesn't exist, add it
    newDimensions.push({ name: dimensionName, size: tensors.length });
  } else {
    // If the dimension exists, update its size
    const totalSize = tensors.reduce((sum, tensor) => {
      const dim = tensor.dimensions.find(d => d.name === dimensionName);
      return sum + (dim ? dim.size : 1);
    }, 0);
    
    newDimensions[dimensionIndex].size = totalSize;
  }
  
  // Create a new tensor with the concatenated data
  const concatenatedData = {
    tensors: tensors.map(t => t.id),
    concatenationDimension: dimensionName,
    originalFormat: 'concatenated'
  };
  
  return new Tensor(concatenatedData, newDimensions, {
    metadata: {
      ...firstTensor.metadata,
      concatenated: true,
      sourceTensors: tensors.map(t => t.id)
    }
  });
}

/**
 * Average multiple tensors
 * @param {Array<Tensor>} tensors - Array of tensors to average
 * @param {Object} options - Averaging options
 * @returns {Tensor} - A new tensor created by averaging the input tensors
 */
function averageTensors(tensors, options = {}) {
  // This is a simplified implementation - in a real system, this would perform actual averaging
  
  // Use the dimensions of the first tensor
  const firstTensor = tensors[0];
  
  // Create a new tensor with the averaged data
  const averagedData = {
    tensors: tensors.map(t => t.id),
    averagingMethod: options.averagingMethod || 'mean',
    originalFormat: 'averaged'
  };
  
  return new Tensor(averagedData, firstTensor.dimensions, {
    metadata: {
      ...firstTensor.metadata,
      averaged: true,
      sourceTensors: tensors.map(t => t.id)
    }
  });
}

/**
 * Stack multiple tensors along a new dimension
 * @param {Array<Tensor>} tensors - Array of tensors to stack
 * @param {Object} options - Stacking options
 * @returns {Tensor} - A new tensor created by stacking the input tensors
 */
function stackTensors(tensors, options = {}) {
  const dimensionName = options.dimension || 'stack';
  
  // Create a new set of dimensions
  const firstTensor = tensors[0];
  const newDimensions = [...firstTensor.dimensions];
  
  // Add the stack dimension
  newDimensions.push({ name: dimensionName, size: tensors.length });
  
  // Create a new tensor with the stacked data
  const stackedData = {
    tensors: tensors.map(t => t.id),
    stackDimension: dimensionName,
    originalFormat: 'stacked'
  };
  
  return new Tensor(stackedData, newDimensions, {
    metadata: {
      ...firstTensor.metadata,
      stacked: true,
      sourceTensors: tensors.map(t => t.id)
    }
  });
}

/**
 * Calculate the similarity between two tensors
 * @param {Tensor} tensor1 - The first tensor
 * @param {Tensor} tensor2 - The second tensor
 * @param {Object} options - Similarity calculation options
 * @returns {Number} - A similarity score between 0 and 1
 */
function calculateTensorSimilarity(tensor1, tensor2, options = {}) {
  if (!(tensor1 instanceof Tensor) || !(tensor2 instanceof Tensor)) {
    throw new Error('Both inputs must be Tensor instances');
  }
  
  const method = options.method || 'hash';
  
  switch (method) {
    case 'hash':
      // Compare tensor hashes
      return tensor1.hash() === tensor2.hash() ? 1 : 0;
    
    case 'dimensions':
      // Compare tensor dimensions
      return calculateDimensionSimilarity(tensor1, tensor2);
    
    case 'metadata':
      // Compare tensor metadata
      return calculateMetadataSimilarity(tensor1, tensor2);
    
    default:
      throw new Error(`Unknown similarity method: ${method}`);
  }
}

/**
 * Calculate the similarity between tensor dimensions
 * @param {Tensor} tensor1 - The first tensor
 * @param {Tensor} tensor2 - The second tensor
 * @returns {Number} - A similarity score between 0 and 1
 */
function calculateDimensionSimilarity(tensor1, tensor2) {
  const dims1 = tensor1.dimensions;
  const dims2 = tensor2.dimensions;
  
  if (dims1.length === 0 || dims2.length === 0) {
    return 0;
  }
  
  // Count matching dimensions
  let matchCount = 0;
  
  dims1.forEach(dim1 => {
    const matchingDim = dims2.find(dim2 => dim2.name === dim1.name);
    if (matchingDim && matchingDim.size === dim1.size) {
      matchCount++;
    }
  });
  
  // Calculate similarity as the ratio of matching dimensions to total unique dimensions
  const uniqueDimNames = new Set([
    ...dims1.map(d => d.name),
    ...dims2.map(d => d.name)
  ]);
  
  return matchCount / uniqueDimNames.size;
}

/**
 * Calculate the similarity between tensor metadata
 * @param {Tensor} tensor1 - The first tensor
 * @param {Tensor} tensor2 - The second tensor
 * @returns {Number} - A similarity score between 0 and 1
 */
function calculateMetadataSimilarity(tensor1, tensor2) {
  const meta1 = tensor1.metadata;
  const meta2 = tensor2.metadata;
  
  if (!meta1 || !meta2) {
    return 0;
  }
  
  // Count matching metadata keys and values
  let matchCount = 0;
  let totalKeys = 0;
  
  Object.keys(meta1).forEach(key => {
    totalKeys++;
    if (meta2[key] === meta1[key]) {
      matchCount++;
    }
  });
  
  // Add keys that are in meta2 but not in meta1
  Object.keys(meta2).forEach(key => {
    if (!(key in meta1)) {
      totalKeys++;
    }
  });
  
  return totalKeys === 0 ? 0 : matchCount / totalKeys;
}

module.exports = {
  tensorFromJSON,
  tensorToJSONString,
  tensorFromArray,
  tensorToArray,
  mergeTensors,
  calculateTensorSimilarity
};
