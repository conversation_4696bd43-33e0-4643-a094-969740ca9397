/**
 * Dynamic Badge System Service
 * 
 * This service provides functionality for generating and verifying
 * dynamic compliance badges.
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const { createCanvas, loadImage } = require('canvas');
const logger = require('../utils/logger');
const config = require('../../config');
const encryption = require('../utils/encryption');
const blockchainEvidence = require('./blockchainEvidence');

// Badge templates directory
const TEMPLATES_DIR = path.join(__dirname, '../../assets/badge-templates');

// Badge colors
const BADGE_COLORS = {
  compliant: '#4CAF50', // Green
  partial: '#FF9800',   // Orange
  noncompliant: '#F44336', // Red
  unknown: '#9E9E9E'    // Gray
};

/**
 * Generate a compliance badge
 * @param {Object} options - Badge options
 * @param {string} options.organizationId - Organization ID
 * @param {string} options.badgeType - Badge type (e.g., 'soc2', 'gdpr', 'hipaa')
 * @param {string} options.status - Compliance status ('compliant', 'partial', 'noncompliant', 'unknown')
 * @param {string} options.style - Badge style ('flat', 'gradient', '3d')
 * @param {string} options.size - Badge size ('small', 'medium', 'large')
 * @param {boolean} options.showDate - Whether to show date on badge
 * @returns {Promise<Buffer>} - Badge image buffer
 */
async function generateBadge(options) {
  try {
    // Validate options
    if (!options || !options.organizationId || !options.badgeType) {
      throw new Error('Invalid badge options');
    }
    
    // Set default values
    const badgeOptions = {
      status: options.status || 'unknown',
      style: options.style || 'flat',
      size: options.size || 'medium',
      showDate: options.showDate !== false
    };
    
    // Get badge dimensions based on size
    const dimensions = getBadgeDimensions(badgeOptions.size);
    
    // Create canvas
    const canvas = createCanvas(dimensions.width, dimensions.height);
    const ctx = canvas.getContext('2d');
    
    // Draw badge background
    await drawBadgeBackground(ctx, badgeOptions, dimensions);
    
    // Draw badge content
    await drawBadgeContent(ctx, options.organizationId, options.badgeType, badgeOptions, dimensions);
    
    // Convert canvas to buffer
    const buffer = canvas.toBuffer('image/png');
    
    // Sign badge
    const signedBadge = signBadge(buffer, options.organizationId, options.badgeType);
    
    return signedBadge;
  } catch (error) {
    logger.error('Badge generation error:', error);
    
    // Return a default error badge
    return generateErrorBadge(error.message);
  }
}

/**
 * Get badge dimensions based on size
 * @param {string} size - Badge size ('small', 'medium', 'large')
 * @returns {Object} - Badge dimensions
 */
function getBadgeDimensions(size) {
  switch (size) {
    case 'small':
      return { width: 120, height: 30 };
    case 'large':
      return { width: 240, height: 60 };
    case 'medium':
    default:
      return { width: 180, height: 45 };
  }
}

/**
 * Draw badge background
 * @param {CanvasRenderingContext2D} ctx - Canvas context
 * @param {Object} options - Badge options
 * @param {Object} dimensions - Badge dimensions
 * @returns {Promise<void>}
 */
async function drawBadgeBackground(ctx, options, dimensions) {
  // Get badge color
  const color = BADGE_COLORS[options.status] || BADGE_COLORS.unknown;
  
  // Draw background based on style
  switch (options.style) {
    case 'gradient':
      // Create gradient
      const gradient = ctx.createLinearGradient(0, 0, 0, dimensions.height);
      gradient.addColorStop(0, lightenColor(color, 20));
      gradient.addColorStop(1, darkenColor(color, 20));
      
      // Draw rounded rectangle
      ctx.fillStyle = gradient;
      roundRect(ctx, 0, 0, dimensions.width, dimensions.height, 5, true, false);
      break;
      
    case '3d':
      // Draw main background
      ctx.fillStyle = color;
      roundRect(ctx, 0, 2, dimensions.width, dimensions.height - 2, 5, true, false);
      
      // Draw shadow
      ctx.fillStyle = darkenColor(color, 30);
      roundRect(ctx, 0, dimensions.height - 5, dimensions.width, 5, { bl: 5, br: 5 }, true, false);
      
      // Draw highlight
      ctx.fillStyle = lightenColor(color, 30);
      roundRect(ctx, 0, 0, dimensions.width, 5, { tl: 5, tr: 5 }, true, false);
      break;
      
    case 'flat':
    default:
      // Draw simple rounded rectangle
      ctx.fillStyle = color;
      roundRect(ctx, 0, 0, dimensions.width, dimensions.height, 5, true, false);
      break;
  }
}

/**
 * Draw badge content
 * @param {CanvasRenderingContext2D} ctx - Canvas context
 * @param {string} organizationId - Organization ID
 * @param {string} badgeType - Badge type
 * @param {Object} options - Badge options
 * @param {Object} dimensions - Badge dimensions
 * @returns {Promise<void>}
 */
async function drawBadgeContent(ctx, organizationId, badgeType, options, dimensions) {
  // Set text color (white or black depending on background brightness)
  const backgroundColor = BADGE_COLORS[options.status] || BADGE_COLORS.unknown;
  const textColor = isColorBright(backgroundColor) ? '#000000' : '#FFFFFF';
  ctx.fillStyle = textColor;
  
  // Set font size based on badge size
  let fontSize;
  switch (options.size) {
    case 'small':
      fontSize = 10;
      break;
    case 'large':
      fontSize = 20;
      break;
    case 'medium':
    default:
      fontSize = 14;
      break;
  }
  
  // Set font
  ctx.font = `bold ${fontSize}px Arial`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  
  // Get badge text
  const badgeText = getBadgeText(badgeType, options.status);
  
  // Draw badge text
  ctx.fillText(badgeText, dimensions.width / 2, dimensions.height / 2);
  
  // Draw date if requested
  if (options.showDate) {
    const dateText = new Date().toLocaleDateString();
    ctx.font = `${fontSize * 0.7}px Arial`;
    ctx.fillText(dateText, dimensions.width / 2, dimensions.height - 8);
  }
  
  // Draw verification code
  const verificationCode = generateVerificationCode(organizationId, badgeType, options.status);
  ctx.font = `${fontSize * 0.5}px Arial`;
  ctx.fillText(verificationCode, dimensions.width - 20, 8);
}

/**
 * Get badge text based on type and status
 * @param {string} badgeType - Badge type
 * @param {string} status - Compliance status
 * @returns {string} - Badge text
 */
function getBadgeText(badgeType, status) {
  // Format badge type
  let formattedType = badgeType.toUpperCase();
  
  // Special formatting for common frameworks
  if (badgeType.toLowerCase() === 'soc2') {
    formattedType = 'SOC 2';
  } else if (badgeType.toLowerCase() === 'hipaa') {
    formattedType = 'HIPAA';
  } else if (badgeType.toLowerCase() === 'gdpr') {
    formattedType = 'GDPR';
  }
  
  // Format status
  let formattedStatus;
  switch (status) {
    case 'compliant':
      formattedStatus = 'Compliant';
      break;
    case 'partial':
      formattedStatus = 'Partially Compliant';
      break;
    case 'noncompliant':
      formattedStatus = 'Non-Compliant';
      break;
    case 'unknown':
    default:
      formattedStatus = 'Status Unknown';
      break;
  }
  
  return `${formattedType} ${formattedStatus}`;
}

/**
 * Generate a verification code for the badge
 * @param {string} organizationId - Organization ID
 * @param {string} badgeType - Badge type
 * @param {string} status - Compliance status
 * @returns {string} - Verification code
 */
function generateVerificationCode(organizationId, badgeType, status) {
  const data = `${organizationId}:${badgeType}:${status}:${new Date().toISOString().split('T')[0]}`;
  const hash = crypto.createHash('sha256').update(data).digest('hex');
  return hash.substring(0, 8);
}

/**
 * Sign a badge image
 * @param {Buffer} badgeBuffer - Badge image buffer
 * @param {string} organizationId - Organization ID
 * @param {string} badgeType - Badge type
 * @returns {Buffer} - Signed badge buffer
 */
function signBadge(badgeBuffer, organizationId, badgeType) {
  try {
    // Create signature
    const signature = encryption.hmac(badgeBuffer, config.encryption.secretKey);
    
    // Create metadata
    const metadata = {
      organizationId,
      badgeType,
      timestamp: new Date().toISOString(),
      signature
    };
    
    // Convert metadata to JSON
    const metadataJson = JSON.stringify(metadata);
    
    // Encode metadata as base64
    const metadataBase64 = Buffer.from(metadataJson).toString('base64');
    
    // Create a new PNG with metadata
    // In a real implementation, this would embed the metadata in the PNG chunks
    // For this placeholder, we'll just return the original buffer
    
    return badgeBuffer;
  } catch (error) {
    logger.error('Badge signing error:', error);
    return badgeBuffer;
  }
}

/**
 * Generate an error badge
 * @param {string} errorMessage - Error message
 * @returns {Buffer} - Error badge buffer
 */
function generateErrorBadge(errorMessage) {
  try {
    // Create canvas
    const canvas = createCanvas(180, 45);
    const ctx = canvas.getContext('2d');
    
    // Draw background
    ctx.fillStyle = '#F44336'; // Red
    roundRect(ctx, 0, 0, 180, 45, 5, true, false);
    
    // Draw text
    ctx.fillStyle = '#FFFFFF'; // White
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('Badge Error', 90, 22);
    
    // Convert canvas to buffer
    return canvas.toBuffer('image/png');
  } catch (error) {
    logger.error('Error badge generation error:', error);
    
    // Return an empty buffer
    return Buffer.from([]);
  }
}

/**
 * Verify a badge
 * @param {Buffer} badgeBuffer - Badge buffer
 * @returns {Promise<Object>} - Verification result
 */
async function verifyBadge(badgeBuffer) {
  try {
    // In a real implementation, this would extract and verify the metadata from the PNG chunks
    // For this placeholder, we'll return a mock verification result
    
    return {
      verified: true,
      organizationId: 'mock-org-id',
      badgeType: 'mock-badge-type',
      timestamp: new Date().toISOString(),
      status: 'compliant'
    };
  } catch (error) {
    logger.error('Badge verification error:', error);
    
    return {
      verified: false,
      error: error.message
    };
  }
}

/**
 * Get compliance status for an organization
 * @param {string} organizationId - Organization ID
 * @param {string} frameworkId - Framework ID
 * @returns {Promise<string>} - Compliance status
 */
async function getComplianceStatus(organizationId, frameworkId) {
  try {
    // In a real implementation, this would query the database for the organization's compliance status
    // For this placeholder, we'll return a mock status
    
    // Mock statuses for different frameworks
    const mockStatuses = {
      soc2: 'compliant',
      gdpr: 'partial',
      hipaa: 'compliant',
      iso27001: 'compliant',
      nist: 'partial',
      default: 'unknown'
    };
    
    // Return status for the requested framework, or default if not found
    return mockStatuses[frameworkId?.toLowerCase()] || mockStatuses.default;
  } catch (error) {
    logger.error('Get compliance status error:', error);
    return 'unknown';
  }
}

/**
 * Create a verification page for a badge
 * @param {string} organizationId - Organization ID
 * @param {string} badgeType - Badge type
 * @returns {Promise<string>} - Verification page HTML
 */
async function createVerificationPage(organizationId, badgeType) {
  try {
    // Get organization details
    const organization = await getOrganization(organizationId);
    
    // Get compliance status
    const status = await getComplianceStatus(organizationId, badgeType);
    
    // Generate badge
    const badge = await generateBadge({
      organizationId,
      badgeType,
      status,
      style: 'flat',
      size: 'large',
      showDate: true
    });
    
    // Convert badge to base64
    const badgeBase64 = badge.toString('base64');
    
    // Create verification page HTML
    const html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>NovaFuse Badge Verification</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
          }
          .badge-container {
            text-align: center;
            margin: 30px 0;
          }
          .verification-details {
            background-color: #f5f5f5;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 30px;
          }
          .verification-details h2 {
            margin-top: 0;
          }
          .verification-details table {
            width: 100%;
            border-collapse: collapse;
          }
          .verification-details table td {
            padding: 8px;
            border-bottom: 1px solid #ddd;
          }
          .verification-details table td:first-child {
            font-weight: bold;
            width: 200px;
          }
          .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
          }
          .status-compliant {
            background-color: #4CAF50;
          }
          .status-partial {
            background-color: #FF9800;
          }
          .status-noncompliant {
            background-color: #F44336;
          }
          .status-unknown {
            background-color: #9E9E9E;
          }
          .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 0.8em;
            color: #666;
          }
        </style>
      </head>
      <body>
        <h1>NovaFuse Badge Verification</h1>
        
        <div class="badge-container">
          <img src="data:image/png;base64,${badgeBase64}" alt="${badgeType} Badge">
        </div>
        
        <div class="verification-details">
          <h2>Verification Details</h2>
          <table>
            <tr>
              <td>Organization:</td>
              <td>${organization.name}</td>
            </tr>
            <tr>
              <td>Badge Type:</td>
              <td>${badgeType.toUpperCase()}</td>
            </tr>
            <tr>
              <td>Verification Date:</td>
              <td>${new Date().toLocaleDateString()}</td>
            </tr>
            <tr>
              <td>Status:</td>
              <td><span class="status status-${status}">${status.charAt(0).toUpperCase() + status.slice(1)}</span></td>
            </tr>
            <tr>
              <td>Verification Code:</td>
              <td>${generateVerificationCode(organizationId, badgeType, status)}</td>
            </tr>
          </table>
        </div>
        
        <div class="compliance-details">
          <h2>Compliance Details</h2>
          <p>
            This badge certifies that ${organization.name} has been assessed against the ${badgeType.toUpperCase()} framework
            and has been found to be ${status === 'compliant' ? 'fully compliant' : status === 'partial' ? 'partially compliant' : 'non-compliant'}.
          </p>
          
          <p>
            The assessment was conducted by NovaFuse, a trusted provider of compliance automation solutions.
            The assessment included a comprehensive review of ${organization.name}'s policies, procedures, and controls.
          </p>
          
          <p>
            This badge is updated in real-time and reflects the current compliance status of ${organization.name}.
            If you have any questions about this verification, please contact <a href="mailto:<EMAIL>"><EMAIL></a>.
          </p>
        </div>
        
        <div class="footer">
          <p>Powered by NovaFuse - Trust, Automated</p>
          <p>&copy; ${new Date().getFullYear()} NovaFuse. All rights reserved.</p>
        </div>
      </body>
      </html>
    `;
    
    return html;
  } catch (error) {
    logger.error('Create verification page error:', error);
    
    // Return error page
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>NovaFuse Badge Verification Error</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
          }
          .error {
            background-color: #ffebee;
            border-radius: 5px;
            padding: 20px;
            margin: 30px 0;
          }
          .error h2 {
            color: #F44336;
          }
        </style>
      </head>
      <body>
        <h1>NovaFuse Badge Verification</h1>
        
        <div class="error">
          <h2>Verification Error</h2>
          <p>An error occurred while verifying this badge. Please try again later.</p>
          <p>Error details: ${error.message}</p>
        </div>
        
        <div class="footer">
          <p>Powered by NovaFuse - Trust, Automated</p>
          <p>&copy; ${new Date().getFullYear()} NovaFuse. All rights reserved.</p>
        </div>
      </body>
      </html>
    `;
  }
}

/**
 * Get organization details
 * @param {string} organizationId - Organization ID
 * @returns {Promise<Object>} - Organization details
 */
async function getOrganization(organizationId) {
  try {
    // In a real implementation, this would query the database for the organization
    // For this placeholder, we'll return a mock organization
    
    return {
      id: organizationId,
      name: 'NovaFuse, Inc.',
      website: 'https://novafuse.com',
      industry: 'Technology',
      size: 'Small',
      createdAt: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Get organization error:', error);
    
    // Return a default organization
    return {
      id: organizationId,
      name: 'Unknown Organization',
      website: '',
      industry: '',
      size: '',
      createdAt: new Date().toISOString()
    };
  }
}

// Helper functions for drawing and colors

/**
 * Draw a rounded rectangle
 * @param {CanvasRenderingContext2D} ctx - Canvas context
 * @param {number} x - X coordinate
 * @param {number} y - Y coordinate
 * @param {number} width - Rectangle width
 * @param {number} height - Rectangle height
 * @param {number|Object} radius - Corner radius
 * @param {boolean} fill - Whether to fill the rectangle
 * @param {boolean} stroke - Whether to stroke the rectangle
 */
function roundRect(ctx, x, y, width, height, radius, fill, stroke) {
  if (typeof radius === 'number') {
    radius = { tl: radius, tr: radius, br: radius, bl: radius };
  } else {
    radius = { ...{ tl: 0, tr: 0, br: 0, bl: 0 }, ...radius };
  }
  
  ctx.beginPath();
  ctx.moveTo(x + radius.tl, y);
  ctx.lineTo(x + width - radius.tr, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius.tr);
  ctx.lineTo(x + width, y + height - radius.br);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);
  ctx.lineTo(x + radius.bl, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius.bl);
  ctx.lineTo(x, y + radius.tl);
  ctx.quadraticCurveTo(x, y, x + radius.tl, y);
  ctx.closePath();
  
  if (fill) {
    ctx.fill();
  }
  
  if (stroke) {
    ctx.stroke();
  }
}

/**
 * Lighten a color
 * @param {string} color - Color in hex format
 * @param {number} percent - Percent to lighten
 * @returns {string} - Lightened color
 */
function lightenColor(color, percent) {
  const num = parseInt(color.replace('#', ''), 16);
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) + amt;
  const G = (num >> 8 & 0x00FF) + amt;
  const B = (num & 0x0000FF) + amt;
  
  return '#' + (
    0x1000000 +
    (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)
  ).toString(16).slice(1);
}

/**
 * Darken a color
 * @param {string} color - Color in hex format
 * @param {number} percent - Percent to darken
 * @returns {string} - Darkened color
 */
function darkenColor(color, percent) {
  const num = parseInt(color.replace('#', ''), 16);
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) - amt;
  const G = (num >> 8 & 0x00FF) - amt;
  const B = (num & 0x0000FF) - amt;
  
  return '#' + (
    0x1000000 +
    (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)
  ).toString(16).slice(1);
}

/**
 * Check if a color is bright
 * @param {string} color - Color in hex format
 * @returns {boolean} - Whether color is bright
 */
function isColorBright(color) {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 155;
}

module.exports = {
  generateBadge,
  verifyBadge,
  getComplianceStatus,
  createVerificationPage
};
