# UUFT Testing Framework

This framework is designed to test the Universal Unified Field Theory (UUFT) across different domains using real-world datasets. The framework tests for four key patterns:

1. **18/82 Distribution Pattern**: Tests whether systems naturally divide into an approximate 18% and 82% distribution.
2. **Pi Relationships**: Tests whether the mathematical constant π (3.14159...) and π×10³ (3,141.59...) appear as values or ratios.
3. **Trinity Patterns**: Tests for three-part structures with source, manifestation, and integration components.
4. **Nested/Fractal Patterns**: Tests whether the same patterns recur across different scales.

## Directory Structure

```
D:/Archives/
├── Cosmological/    # Cosmological datasets
├── Biological/      # Biological datasets
├── Social/          # Social datasets
├── Technological/   # Technological datasets
└── Results/         # Analysis results and visualizations
```

## Installation

1. Clone this repository:
   ```
   git clone <repository-url>
   cd uuft-testing-framework
   ```

2. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

## Usage

### Running the Complete Framework

To run the entire UUFT testing framework:

```
python run_uuft_testing.py
```

This will:
1. Set up the directory structure
2. Download datasets (if not already present)
3. Test for 18/82 patterns
4. Test for Pi relationships
5. Test for Trinity patterns
6. Test for Nested/Fractal patterns
7. Perform cross-domain analysis
8. Generate a comprehensive report

### Running Individual Tests

You can also run individual tests:

```
python uuft_framework.py         # Set up directory structure and download datasets
python uuft_1882_pattern.py      # Test for 18/82 patterns
python uuft_pi_relationships.py  # Test for Pi relationships
python uuft_trinity_patterns.py  # Test for Trinity patterns
python uuft_nested_patterns.py   # Test for Nested/Fractal patterns
python uuft_cross_domain_analysis.py  # Perform cross-domain analysis
```

## Results

All results are saved in the `D:/Archives/Results/` directory:

- **JSON Files**: Detailed results for each pattern test
- **PNG Files**: Visualizations of pattern detection
- **Cross-Domain Analysis Report**: Comprehensive analysis of patterns across domains

## Datasets

The framework uses the following datasets:

### Cosmological
- **Planck Mission Data**: Dark matter, dark energy, and baryonic matter distributions
- **WMAP Data**: Cosmic microwave background radiation measurements

### Biological
- **Gene Expression Data**: Expression levels across different genes and samples

### Social
- **Gini Index Data**: Wealth inequality measurements across countries and years

### Technological
- **Network Traffic Data**: Packet sizes, flow durations, and other network metrics

## Adding Custom Datasets

To add your own datasets:

1. Place the dataset files in the appropriate domain directory
2. Modify the corresponding test script to load and analyze your dataset
3. Run the individual test or the complete framework

## Requirements

- Python 3.7+
- NumPy
- Pandas
- Matplotlib
- SciPy
- Scikit-learn
- Seaborn
- PyWavelets
- Requests

## License

This project is licensed under the MIT License - see the LICENSE file for details.
