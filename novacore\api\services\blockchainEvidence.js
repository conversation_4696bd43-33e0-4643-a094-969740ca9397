/**
 * Blockchain Evidence Service
 * 
 * This service provides functionality for storing and verifying evidence
 * using blockchain technology.
 */

const crypto = require('crypto');
const { ethers } = require('ethers');
const logger = require('../utils/logger');
const config = require('../../config');
const encryption = require('../utils/encryption');

// Placeholder for blockchain contract ABI
// This would be replaced with the actual ABI in production
const CONTRACT_ABI = [
  "function submitEvidence(bytes32 merkleRoot) public returns (uint256)",
  "function verifyEvidence(bytes32 merkleRoot, uint256 submissionId) public view returns (bool)",
  "function getSubmissionTimestamp(uint256 submissionId) public view returns (uint256)"
];

// Placeholder for blockchain contract address
// This would be replaced with the actual contract address in production
const CONTRACT_ADDRESS = config.blockchain?.contractAddress || '******************************************';

/**
 * Initialize blockchain provider and contract
 * @returns {Object} - Provider and contract
 */
function initializeBlockchain() {
  try {
    // Create provider
    const provider = new ethers.providers.JsonRpcProvider(
      config.blockchain?.rpcUrl || 'http://localhost:8545'
    );
    
    // Create wallet
    const wallet = new ethers.Wallet(
      config.blockchain?.privateKey || ethers.Wallet.createRandom().privateKey,
      provider
    );
    
    // Create contract
    const contract = new ethers.Contract(
      CONTRACT_ADDRESS,
      CONTRACT_ABI,
      wallet
    );
    
    return { provider, wallet, contract };
  } catch (error) {
    logger.error('Blockchain initialization error:', error);
    
    // Return mock objects for development/testing
    return {
      provider: {
        getBlockNumber: async () => 12345678
      },
      wallet: {
        address: '******************************************'
      },
      contract: {
        submitEvidence: async (merkleRoot) => {
          logger.info('Mock blockchain submission', { merkleRoot });
          return { wait: async () => ({ status: 1 }) };
        },
        verifyEvidence: async (merkleRoot, submissionId) => {
          logger.info('Mock blockchain verification', { merkleRoot, submissionId });
          return true;
        },
        getSubmissionTimestamp: async (submissionId) => {
          return Math.floor(Date.now() / 1000) - 3600; // 1 hour ago
        }
      }
    };
  }
}

// Initialize blockchain
const { provider, wallet, contract } = initializeBlockchain();

/**
 * Hash evidence using SHA-256
 * @param {Buffer|string} evidence - Evidence to hash
 * @returns {string} - Evidence hash
 */
function hashEvidence(evidence) {
  return crypto.createHash('sha256')
    .update(Buffer.isBuffer(evidence) ? evidence : Buffer.from(evidence))
    .digest('hex');
}

/**
 * Construct a Merkle tree from evidence hashes
 * @param {string[]} hashes - Array of evidence hashes
 * @returns {Object} - Merkle tree with root and proofs
 */
function constructMerkleTree(hashes) {
  if (!hashes || hashes.length === 0) {
    throw new Error('No hashes provided');
  }
  
  // If only one hash, it's the root
  if (hashes.length === 1) {
    return {
      root: hashes[0],
      proofs: { [hashes[0]]: [] }
    };
  }
  
  // Ensure even number of hashes by duplicating the last one if needed
  const workingHashes = [...hashes];
  if (workingHashes.length % 2 !== 0) {
    workingHashes.push(workingHashes[workingHashes.length - 1]);
  }
  
  // Initialize proofs object
  const proofs = {};
  for (const hash of hashes) {
    proofs[hash] = [];
  }
  
  // Build the tree bottom-up
  let currentLevel = workingHashes;
  
  while (currentLevel.length > 1) {
    const nextLevel = [];
    
    // Process pairs of hashes
    for (let i = 0; i < currentLevel.length; i += 2) {
      const left = currentLevel[i];
      const right = currentLevel[i + 1];
      
      // Hash the pair
      const combined = hashPair(left, right);
      nextLevel.push(combined);
      
      // Update proofs for all leaves under this node
      for (const hash of hashes) {
        if (isLeafUnderNode(hash, left, right, proofs)) {
          if (left === hash || isLeafUnderNode(hash, left, null, proofs)) {
            proofs[hash].push(right);
          } else {
            proofs[hash].push(left);
          }
        }
      }
    }
    
    currentLevel = nextLevel;
  }
  
  return {
    root: currentLevel[0],
    proofs
  };
}

/**
 * Check if a leaf is under a node in the Merkle tree
 * @param {string} leaf - Leaf hash
 * @param {string} left - Left child hash
 * @param {string} right - Right child hash
 * @param {Object} proofs - Proofs object
 * @returns {boolean} - Whether leaf is under node
 */
function isLeafUnderNode(leaf, left, right, proofs) {
  return leaf === left || leaf === right || 
    (proofs[leaf] && (proofs[leaf].includes(left) || proofs[leaf].includes(right)));
}

/**
 * Hash a pair of hashes
 * @param {string} left - Left hash
 * @param {string} right - Right hash
 * @returns {string} - Combined hash
 */
function hashPair(left, right) {
  // Sort hashes to ensure consistent ordering
  const sorted = [left, right].sort();
  
  // Combine and hash
  return crypto.createHash('sha256')
    .update(Buffer.from(sorted[0] + sorted[1], 'hex'))
    .digest('hex');
}

/**
 * Verify a Merkle proof
 * @param {string} leafHash - Leaf hash to verify
 * @param {string[]} proof - Merkle proof
 * @param {string} root - Merkle root
 * @returns {boolean} - Whether proof is valid
 */
function verifyMerkleProof(leafHash, proof, root) {
  let currentHash = leafHash;
  
  // Apply each proof element
  for (const proofElement of proof) {
    currentHash = hashPair(currentHash, proofElement);
  }
  
  // Check if resulting hash matches the root
  return currentHash === root;
}

/**
 * Submit evidence to the blockchain
 * @param {Array<Buffer|string>} evidenceItems - Array of evidence items
 * @returns {Promise<Object>} - Submission result
 */
async function submitEvidence(evidenceItems) {
  try {
    // Hash each evidence item
    const hashes = evidenceItems.map(item => hashEvidence(item));
    
    // Construct Merkle tree
    const { root, proofs } = constructMerkleTree(hashes);
    
    // Convert root to bytes32
    const rootBytes32 = '0x' + root;
    
    // Submit to blockchain
    logger.info('Submitting evidence to blockchain', { 
      merkleRoot: root,
      evidenceCount: evidenceItems.length
    });
    
    // Submit transaction
    const tx = await contract.submitEvidence(rootBytes32);
    
    // Wait for transaction to be mined
    const receipt = await tx.wait();
    
    // Check transaction status
    if (receipt.status !== 1) {
      throw new Error('Blockchain transaction failed');
    }
    
    // Get submission ID from transaction logs
    // In a real implementation, this would parse the event logs
    // For this placeholder, we'll use a timestamp
    const submissionId = Date.now().toString();
    
    // Get block information
    const blockNumber = receipt.blockNumber;
    const block = await provider.getBlock(blockNumber);
    
    logger.info('Evidence submitted to blockchain', {
      merkleRoot: root,
      submissionId,
      blockNumber,
      blockHash: block.hash,
      timestamp: block.timestamp
    });
    
    // Return submission details
    return {
      merkleRoot: root,
      proofs,
      submissionId,
      blockNumber,
      blockHash: block.hash,
      timestamp: block.timestamp,
      transactionHash: receipt.transactionHash
    };
  } catch (error) {
    logger.error('Evidence submission error:', error);
    
    // For development/testing, return mock data
    if (process.env.NODE_ENV !== 'production') {
      const mockRoot = crypto.randomBytes(32).toString('hex');
      const mockProofs = {};
      evidenceItems.forEach(item => {
        const hash = hashEvidence(item);
        mockProofs[hash] = [crypto.randomBytes(32).toString('hex')];
      });
      
      return {
        merkleRoot: mockRoot,
        proofs: mockProofs,
        submissionId: Date.now().toString(),
        blockNumber: 12345678,
        blockHash: '0x' + crypto.randomBytes(32).toString('hex'),
        timestamp: Math.floor(Date.now() / 1000),
        transactionHash: '0x' + crypto.randomBytes(32).toString('hex'),
        isMock: true
      };
    }
    
    throw error;
  }
}

/**
 * Verify evidence against blockchain
 * @param {Buffer|string} evidence - Evidence to verify
 * @param {string[]} proof - Merkle proof
 * @param {string} merkleRoot - Merkle root
 * @param {string} submissionId - Submission ID
 * @returns {Promise<Object>} - Verification result
 */
async function verifyEvidence(evidence, proof, merkleRoot, submissionId) {
  try {
    // Hash evidence
    const evidenceHash = hashEvidence(evidence);
    
    // Verify Merkle proof
    const proofValid = verifyMerkleProof(evidenceHash, proof, merkleRoot);
    if (!proofValid) {
      return {
        verified: false,
        reason: 'Invalid Merkle proof'
      };
    }
    
    // Convert root to bytes32
    const rootBytes32 = '0x' + merkleRoot;
    
    // Verify on blockchain
    const verified = await contract.verifyEvidence(rootBytes32, submissionId);
    if (!verified) {
      return {
        verified: false,
        reason: 'Evidence not found on blockchain'
      };
    }
    
    // Get submission timestamp
    const timestamp = await contract.getSubmissionTimestamp(submissionId);
    
    return {
      verified: true,
      evidenceHash,
      merkleRoot,
      submissionId,
      timestamp: Number(timestamp)
    };
  } catch (error) {
    logger.error('Evidence verification error:', error);
    
    // For development/testing, return mock data
    if (process.env.NODE_ENV !== 'production') {
      return {
        verified: true,
        evidenceHash: hashEvidence(evidence),
        merkleRoot,
        submissionId,
        timestamp: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
        isMock: true
      };
    }
    
    throw error;
  }
}

/**
 * Create a blockchain evidence record
 * @param {Object} evidence - Evidence object
 * @param {string} evidence.type - Evidence type
 * @param {string} evidence.content - Evidence content
 * @param {Object} evidence.metadata - Evidence metadata
 * @returns {Promise<Object>} - Evidence record
 */
async function createEvidenceRecord(evidence) {
  try {
    // Validate evidence
    if (!evidence || !evidence.type || !evidence.content) {
      throw new Error('Invalid evidence');
    }
    
    // Create evidence record
    const evidenceRecord = {
      id: crypto.randomUUID(),
      type: evidence.type,
      content: evidence.content,
      metadata: evidence.metadata || {},
      createdAt: new Date().toISOString()
    };
    
    // Hash the evidence record
    const evidenceHash = hashEvidence(JSON.stringify(evidenceRecord));
    
    // Submit to blockchain
    const submission = await submitEvidence([evidenceHash]);
    
    // Add blockchain information to record
    const blockchainRecord = {
      ...evidenceRecord,
      blockchain: {
        merkleRoot: submission.merkleRoot,
        proof: submission.proofs[evidenceHash],
        submissionId: submission.submissionId,
        blockNumber: submission.blockNumber,
        blockHash: submission.blockHash,
        timestamp: submission.timestamp,
        transactionHash: submission.transactionHash
      }
    };
    
    return blockchainRecord;
  } catch (error) {
    logger.error('Create evidence record error:', error);
    throw error;
  }
}

/**
 * Verify an evidence record
 * @param {Object} evidenceRecord - Evidence record to verify
 * @returns {Promise<Object>} - Verification result
 */
async function verifyEvidenceRecord(evidenceRecord) {
  try {
    // Validate evidence record
    if (!evidenceRecord || !evidenceRecord.blockchain) {
      throw new Error('Invalid evidence record');
    }
    
    // Extract blockchain information
    const { 
      merkleRoot, 
      proof, 
      submissionId 
    } = evidenceRecord.blockchain;
    
    // Create a copy of the record without blockchain information
    const recordWithoutBlockchain = { ...evidenceRecord };
    delete recordWithoutBlockchain.blockchain;
    
    // Hash the record
    const evidenceHash = hashEvidence(JSON.stringify(recordWithoutBlockchain));
    
    // Verify on blockchain
    const verification = await verifyEvidence(
      evidenceHash,
      proof,
      merkleRoot,
      submissionId
    );
    
    return verification;
  } catch (error) {
    logger.error('Verify evidence record error:', error);
    throw error;
  }
}

module.exports = {
  hashEvidence,
  constructMerkleTree,
  verifyMerkleProof,
  submitEvidence,
  verifyEvidence,
  createEvidenceRecord,
  verifyEvidenceRecord
};
