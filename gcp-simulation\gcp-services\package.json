{"name": "gcp-services", "version": "1.0.0", "description": "GCP Service Simulators for NovaFuse", "main": "index.js", "scripts": {"start:storage": "node cloud-storage.js", "start:functions": "node cloud-functions.js", "start:monitoring": "node cloud-monitoring.js", "start:all": "concurrently \"npm run start:storage\" \"npm run start:functions\" \"npm run start:monitoring\""}, "dependencies": {"axios": "^0.27.2", "cors": "^2.8.5", "express": "^4.18.1", "morgan": "^1.10.0"}, "devDependencies": {"concurrently": "^7.2.2"}}