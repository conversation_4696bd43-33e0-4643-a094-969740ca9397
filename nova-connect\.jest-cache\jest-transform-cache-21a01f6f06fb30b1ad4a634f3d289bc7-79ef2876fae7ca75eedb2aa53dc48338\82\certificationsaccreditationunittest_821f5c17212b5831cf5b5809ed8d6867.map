{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "createLogger", "jest", "fn", "info", "error", "debug", "warn", "require", "axios", "CertificationsAccreditationConnector", "describe", "connector", "mockConfig", "mockCredentials", "beforeEach", "clearAllMocks", "baseUrl", "clientId", "clientSecret", "redirectUri", "it", "expect", "config", "toEqual", "credentials", "toBe", "connectorWithDefaults", "authenticate", "mockResolvedValue", "initialize", "toHaveBeenCalled", "connectorWithoutCredentials", "not", "post", "data", "access_token", "expires_in", "toHaveBeenCalledWith", "grant_type", "client_id", "client_secret", "scope", "headers", "accessToken", "tokenExpiry", "toBeDefined", "errorMessage", "mockRejectedValue", "Error", "rejects", "toThrow", "Date", "now", "getAuthHeaders", "mockImplementation", "mockResponse", "id", "name", "pagination", "page", "limit", "totalItems", "totalPages", "get", "params", "status", "result", "listCertifications", "description", "certificationId", "getCertification", "listAssessments", "assessmentId", "getAssessment", "listEvidence", "type", "evidenceId", "getEvidence"], "sources": ["certifications-accreditation.unit.test.js"], "sourcesContent": ["/**\n * Unit tests for the Certifications & Accreditation Connector\n */\n\nconst axios = require('axios');\nconst CertificationsAccreditationConnector = require('../../../../connector/implementations/certifications-accreditation');\n\n// Mock axios\njest.mock('axios');\n\n// Mock logger\njest.mock('../../../../utils/logger', () => ({\n  createLogger: jest.fn(() => ({\n    info: jest.fn(),\n    error: jest.fn(),\n    debug: jest.fn(),\n    warn: jest.fn()\n  }))\n}));\n\ndescribe('CertificationsAccreditationConnector', () => {\n  let connector;\n  let mockConfig;\n  let mockCredentials;\n  \n  beforeEach(() => {\n    // Reset mocks\n    jest.clearAllMocks();\n    \n    // Mock config and credentials\n    mockConfig = {\n      baseUrl: 'https://api.test.com'\n    };\n    \n    mockCredentials = {\n      clientId: 'test-client-id',\n      clientSecret: 'test-client-secret',\n      redirectUri: 'https://test-redirect.com'\n    };\n    \n    // Create connector instance\n    connector = new CertificationsAccreditationConnector(mockConfig, mockCredentials);\n  });\n  \n  describe('constructor', () => {\n    it('should initialize with provided config and credentials', () => {\n      expect(connector.config).toEqual(mockConfig);\n      expect(connector.credentials).toEqual(mockCredentials);\n      expect(connector.baseUrl).toBe(mockConfig.baseUrl);\n    });\n    \n    it('should use default baseUrl if not provided', () => {\n      const connectorWithDefaults = new CertificationsAccreditationConnector();\n      expect(connectorWithDefaults.baseUrl).toBe('https://api.example.com');\n    });\n  });\n  \n  describe('initialize', () => {\n    it('should authenticate if credentials are provided', async () => {\n      // Mock authenticate method\n      connector.authenticate = jest.fn().mockResolvedValue();\n      \n      await connector.initialize();\n      \n      expect(connector.authenticate).toHaveBeenCalled();\n    });\n    \n    it('should not authenticate if credentials are not provided', async () => {\n      // Create connector without credentials\n      const connectorWithoutCredentials = new CertificationsAccreditationConnector(mockConfig, {});\n      \n      // Mock authenticate method\n      connectorWithoutCredentials.authenticate = jest.fn().mockResolvedValue();\n      \n      await connectorWithoutCredentials.initialize();\n      \n      expect(connectorWithoutCredentials.authenticate).not.toHaveBeenCalled();\n    });\n  });\n  \n  describe('authenticate', () => {\n    it('should make a POST request to the token endpoint', async () => {\n      // Mock axios post response\n      axios.post.mockResolvedValue({\n        data: {\n          access_token: 'test-access-token',\n          expires_in: 3600\n        }\n      });\n      \n      await connector.authenticate();\n      \n      expect(axios.post).toHaveBeenCalledWith(\n        `${mockConfig.baseUrl}/oauth2/token`,\n        {\n          grant_type: 'client_credentials',\n          client_id: mockCredentials.clientId,\n          client_secret: mockCredentials.clientSecret,\n          scope: 'read:certifications write:certifications read:assessments write:assessments read:evidence write:evidence'\n        },\n        {\n          headers: {\n            'Content-Type': 'application/x-www-form-urlencoded',\n            'Accept': 'application/json'\n          }\n        }\n      );\n      \n      expect(connector.accessToken).toBe('test-access-token');\n      expect(connector.tokenExpiry).toBeDefined();\n    });\n    \n    it('should throw an error if authentication fails', async () => {\n      // Mock axios post error\n      const errorMessage = 'Authentication failed';\n      axios.post.mockRejectedValue(new Error(errorMessage));\n      \n      await expect(connector.authenticate()).rejects.toThrow(`Authentication failed: ${errorMessage}`);\n    });\n  });\n  \n  describe('getAuthHeaders', () => {\n    it('should return authorization headers with access token', async () => {\n      // Set access token and expiry\n      connector.accessToken = 'test-access-token';\n      connector.tokenExpiry = Date.now() + 3600000; // 1 hour from now\n      \n      const headers = await connector.getAuthHeaders();\n      \n      expect(headers).toEqual({\n        'Authorization': 'Bearer test-access-token'\n      });\n    });\n    \n    it('should authenticate if access token is not set', async () => {\n      // Mock authenticate method\n      connector.authenticate = jest.fn().mockImplementation(() => {\n        connector.accessToken = 'new-access-token';\n        connector.tokenExpiry = Date.now() + 3600000;\n      });\n      \n      const headers = await connector.getAuthHeaders();\n      \n      expect(connector.authenticate).toHaveBeenCalled();\n      expect(headers).toEqual({\n        'Authorization': 'Bearer new-access-token'\n      });\n    });\n  });\n  \n  describe('listCertifications', () => {\n    it('should make a GET request to the certifications endpoint', async () => {\n      // Mock getAuthHeaders method\n      connector.getAuthHeaders = jest.fn().mockResolvedValue({\n        'Authorization': 'Bearer test-access-token'\n      });\n      \n      // Mock axios get response\n      const mockResponse = {\n        data: {\n          data: [\n            { id: 'cert-1', name: 'ISO 27001' },\n            { id: 'cert-2', name: 'SOC 2 Type II' }\n          ],\n          pagination: {\n            page: 1,\n            limit: 20,\n            totalItems: 2,\n            totalPages: 1\n          }\n        }\n      };\n      axios.get.mockResolvedValue(mockResponse);\n      \n      const params = { status: 'active', limit: 50 };\n      const result = await connector.listCertifications(params);\n      \n      expect(axios.get).toHaveBeenCalledWith(\n        `${mockConfig.baseUrl}/certifications`,\n        {\n          params,\n          headers: {\n            'Authorization': 'Bearer test-access-token',\n            'Content-Type': 'application/json',\n            'Accept': 'application/json'\n          }\n        }\n      );\n      \n      expect(result).toEqual(mockResponse.data);\n    });\n    \n    it('should throw an error if the request fails', async () => {\n      // Mock getAuthHeaders method\n      connector.getAuthHeaders = jest.fn().mockResolvedValue({\n        'Authorization': 'Bearer test-access-token'\n      });\n      \n      // Mock axios get error\n      const errorMessage = 'Request failed';\n      axios.get.mockRejectedValue(new Error(errorMessage));\n      \n      await expect(connector.listCertifications()).rejects.toThrow(`Error listing certifications: ${errorMessage}`);\n    });\n  });\n  \n  describe('getCertification', () => {\n    it('should make a GET request to the specific certification endpoint', async () => {\n      // Mock getAuthHeaders method\n      connector.getAuthHeaders = jest.fn().mockResolvedValue({\n        'Authorization': 'Bearer test-access-token'\n      });\n      \n      // Mock axios get response\n      const mockResponse = {\n        data: {\n          id: 'cert-123',\n          name: 'ISO 27001',\n          description: 'Information Security Management System certification'\n        }\n      };\n      axios.get.mockResolvedValue(mockResponse);\n      \n      const certificationId = 'cert-123';\n      const result = await connector.getCertification(certificationId);\n      \n      expect(axios.get).toHaveBeenCalledWith(\n        `${mockConfig.baseUrl}/certifications/${certificationId}`,\n        {\n          headers: {\n            'Authorization': 'Bearer test-access-token',\n            'Content-Type': 'application/json',\n            'Accept': 'application/json'\n          }\n        }\n      );\n      \n      expect(result).toEqual(mockResponse.data);\n    });\n    \n    it('should throw an error if certificationId is not provided', async () => {\n      await expect(connector.getCertification()).rejects.toThrow('Certification ID is required');\n    });\n  });\n  \n  describe('listAssessments', () => {\n    it('should make a GET request to the assessments endpoint', async () => {\n      // Mock getAuthHeaders method\n      connector.getAuthHeaders = jest.fn().mockResolvedValue({\n        'Authorization': 'Bearer test-access-token'\n      });\n      \n      // Mock axios get response\n      const mockResponse = {\n        data: {\n          data: [\n            { id: 'assess-1', name: 'ISO 27001 Annual Assessment' },\n            { id: 'assess-2', name: 'SOC 2 Type II Audit' }\n          ],\n          pagination: {\n            page: 1,\n            limit: 20,\n            totalItems: 2,\n            totalPages: 1\n          }\n        }\n      };\n      axios.get.mockResolvedValue(mockResponse);\n      \n      const params = { status: 'in_progress', certificationId: 'cert-123' };\n      const result = await connector.listAssessments(params);\n      \n      expect(axios.get).toHaveBeenCalledWith(\n        `${mockConfig.baseUrl}/assessments`,\n        {\n          params,\n          headers: {\n            'Authorization': 'Bearer test-access-token',\n            'Content-Type': 'application/json',\n            'Accept': 'application/json'\n          }\n        }\n      );\n      \n      expect(result).toEqual(mockResponse.data);\n    });\n  });\n  \n  describe('getAssessment', () => {\n    it('should make a GET request to the specific assessment endpoint', async () => {\n      // Mock getAuthHeaders method\n      connector.getAuthHeaders = jest.fn().mockResolvedValue({\n        'Authorization': 'Bearer test-access-token'\n      });\n      \n      // Mock axios get response\n      const mockResponse = {\n        data: {\n          id: 'assess-123',\n          name: 'ISO 27001 Annual Assessment',\n          status: 'in_progress'\n        }\n      };\n      axios.get.mockResolvedValue(mockResponse);\n      \n      const assessmentId = 'assess-123';\n      const result = await connector.getAssessment(assessmentId);\n      \n      expect(axios.get).toHaveBeenCalledWith(\n        `${mockConfig.baseUrl}/assessments/${assessmentId}`,\n        {\n          headers: {\n            'Authorization': 'Bearer test-access-token',\n            'Content-Type': 'application/json',\n            'Accept': 'application/json'\n          }\n        }\n      );\n      \n      expect(result).toEqual(mockResponse.data);\n    });\n    \n    it('should throw an error if assessmentId is not provided', async () => {\n      await expect(connector.getAssessment()).rejects.toThrow('Assessment ID is required');\n    });\n  });\n  \n  describe('listEvidence', () => {\n    it('should make a GET request to the evidence endpoint', async () => {\n      // Mock getAuthHeaders method\n      connector.getAuthHeaders = jest.fn().mockResolvedValue({\n        'Authorization': 'Bearer test-access-token'\n      });\n      \n      // Mock axios get response\n      const mockResponse = {\n        data: {\n          data: [\n            { id: 'evidence-1', name: 'Information Security Policy Document' },\n            { id: 'evidence-2', name: 'Risk Assessment Report' }\n          ],\n          pagination: {\n            page: 1,\n            limit: 20,\n            totalItems: 2,\n            totalPages: 1\n          }\n        }\n      };\n      axios.get.mockResolvedValue(mockResponse);\n      \n      const params = { assessmentId: 'assess-123' };\n      const result = await connector.listEvidence(params);\n      \n      expect(axios.get).toHaveBeenCalledWith(\n        `${mockConfig.baseUrl}/evidence`,\n        {\n          params,\n          headers: {\n            'Authorization': 'Bearer test-access-token',\n            'Content-Type': 'application/json',\n            'Accept': 'application/json'\n          }\n        }\n      );\n      \n      expect(result).toEqual(mockResponse.data);\n    });\n  });\n  \n  describe('getEvidence', () => {\n    it('should make a GET request to the specific evidence endpoint', async () => {\n      // Mock getAuthHeaders method\n      connector.getAuthHeaders = jest.fn().mockResolvedValue({\n        'Authorization': 'Bearer test-access-token'\n      });\n      \n      // Mock axios get response\n      const mockResponse = {\n        data: {\n          id: 'evidence-123',\n          name: 'Information Security Policy Document',\n          type: 'document'\n        }\n      };\n      axios.get.mockResolvedValue(mockResponse);\n      \n      const evidenceId = 'evidence-123';\n      const result = await connector.getEvidence(evidenceId);\n      \n      expect(axios.get).toHaveBeenCalledWith(\n        `${mockConfig.baseUrl}/evidence/${evidenceId}`,\n        {\n          headers: {\n            'Authorization': 'Bearer test-access-token',\n            'Content-Type': 'application/json',\n            'Accept': 'application/json'\n          }\n        }\n      );\n      \n      expect(result).toEqual(mockResponse.data);\n    });\n    \n    it('should throw an error if evidenceId is not provided', async () => {\n      await expect(connector.getEvidence()).rejects.toThrow('Evidence ID is required');\n    });\n  });\n});\n"], "mappings": "AAOA;AACAA,WAAA,GAAKC,IAAI,CAAC,OAAO,CAAC;;AAElB;AACAD,WAAA,GAAKC,IAAI,CAAC,0BAA0B,EAAE,OAAO;EAC3CC,YAAY,EAAEC,IAAI,CAACC,EAAE,CAAC,OAAO;IAC3BC,IAAI,EAAEF,IAAI,CAACC,EAAE,CAAC,CAAC;IACfE,KAAK,EAAEH,IAAI,CAACC,EAAE,CAAC,CAAC;IAChBG,KAAK,EAAEJ,IAAI,CAACC,EAAE,CAAC,CAAC;IAChBI,IAAI,EAAEL,IAAI,CAACC,EAAE,CAAC;EAChB,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AAAC,SAAAJ,YAAA;EAAA;IAAAG;EAAA,IAAAM,OAAA;EAAAT,WAAA,GAAAA,CAAA,KAAAG,IAAA;EAAA,OAAAA,IAAA;AAAA;AAlBJ;AACA;AACA;;AAEA,MAAMO,KAAK,GAAGD,OAAO,CAAC,OAAO,CAAC;AAC9B,MAAME,oCAAoC,GAAGF,OAAO,CAAC,oEAAoE,CAAC;AAe1HG,QAAQ,CAAC,sCAAsC,EAAE,MAAM;EACrD,IAAIC,SAAS;EACb,IAAIC,UAAU;EACd,IAAIC,eAAe;EAEnBC,UAAU,CAAC,MAAM;IACf;IACAb,IAAI,CAACc,aAAa,CAAC,CAAC;;IAEpB;IACAH,UAAU,GAAG;MACXI,OAAO,EAAE;IACX,CAAC;IAEDH,eAAe,GAAG;MAChBI,QAAQ,EAAE,gBAAgB;MAC1BC,YAAY,EAAE,oBAAoB;MAClCC,WAAW,EAAE;IACf,CAAC;;IAED;IACAR,SAAS,GAAG,IAAIF,oCAAoC,CAACG,UAAU,EAAEC,eAAe,CAAC;EACnF,CAAC,CAAC;EAEFH,QAAQ,CAAC,aAAa,EAAE,MAAM;IAC5BU,EAAE,CAAC,wDAAwD,EAAE,MAAM;MACjEC,MAAM,CAACV,SAAS,CAACW,MAAM,CAAC,CAACC,OAAO,CAACX,UAAU,CAAC;MAC5CS,MAAM,CAACV,SAAS,CAACa,WAAW,CAAC,CAACD,OAAO,CAACV,eAAe,CAAC;MACtDQ,MAAM,CAACV,SAAS,CAACK,OAAO,CAAC,CAACS,IAAI,CAACb,UAAU,CAACI,OAAO,CAAC;IACpD,CAAC,CAAC;IAEFI,EAAE,CAAC,4CAA4C,EAAE,MAAM;MACrD,MAAMM,qBAAqB,GAAG,IAAIjB,oCAAoC,CAAC,CAAC;MACxEY,MAAM,CAACK,qBAAqB,CAACV,OAAO,CAAC,CAACS,IAAI,CAAC,yBAAyB,CAAC;IACvE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFf,QAAQ,CAAC,YAAY,EAAE,MAAM;IAC3BU,EAAE,CAAC,iDAAiD,EAAE,YAAY;MAChE;MACAT,SAAS,CAACgB,YAAY,GAAG1B,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC0B,iBAAiB,CAAC,CAAC;MAEtD,MAAMjB,SAAS,CAACkB,UAAU,CAAC,CAAC;MAE5BR,MAAM,CAACV,SAAS,CAACgB,YAAY,CAAC,CAACG,gBAAgB,CAAC,CAAC;IACnD,CAAC,CAAC;IAEFV,EAAE,CAAC,yDAAyD,EAAE,YAAY;MACxE;MACA,MAAMW,2BAA2B,GAAG,IAAItB,oCAAoC,CAACG,UAAU,EAAE,CAAC,CAAC,CAAC;;MAE5F;MACAmB,2BAA2B,CAACJ,YAAY,GAAG1B,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC0B,iBAAiB,CAAC,CAAC;MAExE,MAAMG,2BAA2B,CAACF,UAAU,CAAC,CAAC;MAE9CR,MAAM,CAACU,2BAA2B,CAACJ,YAAY,CAAC,CAACK,GAAG,CAACF,gBAAgB,CAAC,CAAC;IACzE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpB,QAAQ,CAAC,cAAc,EAAE,MAAM;IAC7BU,EAAE,CAAC,kDAAkD,EAAE,YAAY;MACjE;MACAZ,KAAK,CAACyB,IAAI,CAACL,iBAAiB,CAAC;QAC3BM,IAAI,EAAE;UACJC,YAAY,EAAE,mBAAmB;UACjCC,UAAU,EAAE;QACd;MACF,CAAC,CAAC;MAEF,MAAMzB,SAAS,CAACgB,YAAY,CAAC,CAAC;MAE9BN,MAAM,CAACb,KAAK,CAACyB,IAAI,CAAC,CAACI,oBAAoB,CACrC,GAAGzB,UAAU,CAACI,OAAO,eAAe,EACpC;QACEsB,UAAU,EAAE,oBAAoB;QAChCC,SAAS,EAAE1B,eAAe,CAACI,QAAQ;QACnCuB,aAAa,EAAE3B,eAAe,CAACK,YAAY;QAC3CuB,KAAK,EAAE;MACT,CAAC,EACD;QACEC,OAAO,EAAE;UACP,cAAc,EAAE,mCAAmC;UACnD,QAAQ,EAAE;QACZ;MACF,CACF,CAAC;MAEDrB,MAAM,CAACV,SAAS,CAACgC,WAAW,CAAC,CAAClB,IAAI,CAAC,mBAAmB,CAAC;MACvDJ,MAAM,CAACV,SAAS,CAACiC,WAAW,CAAC,CAACC,WAAW,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEFzB,EAAE,CAAC,+CAA+C,EAAE,YAAY;MAC9D;MACA,MAAM0B,YAAY,GAAG,uBAAuB;MAC5CtC,KAAK,CAACyB,IAAI,CAACc,iBAAiB,CAAC,IAAIC,KAAK,CAACF,YAAY,CAAC,CAAC;MAErD,MAAMzB,MAAM,CAACV,SAAS,CAACgB,YAAY,CAAC,CAAC,CAAC,CAACsB,OAAO,CAACC,OAAO,CAAC,0BAA0BJ,YAAY,EAAE,CAAC;IAClG,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpC,QAAQ,CAAC,gBAAgB,EAAE,MAAM;IAC/BU,EAAE,CAAC,uDAAuD,EAAE,YAAY;MACtE;MACAT,SAAS,CAACgC,WAAW,GAAG,mBAAmB;MAC3ChC,SAAS,CAACiC,WAAW,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;;MAE9C,MAAMV,OAAO,GAAG,MAAM/B,SAAS,CAAC0C,cAAc,CAAC,CAAC;MAEhDhC,MAAM,CAACqB,OAAO,CAAC,CAACnB,OAAO,CAAC;QACtB,eAAe,EAAE;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFH,EAAE,CAAC,gDAAgD,EAAE,YAAY;MAC/D;MACAT,SAAS,CAACgB,YAAY,GAAG1B,IAAI,CAACC,EAAE,CAAC,CAAC,CAACoD,kBAAkB,CAAC,MAAM;QAC1D3C,SAAS,CAACgC,WAAW,GAAG,kBAAkB;QAC1ChC,SAAS,CAACiC,WAAW,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO;MAC9C,CAAC,CAAC;MAEF,MAAMV,OAAO,GAAG,MAAM/B,SAAS,CAAC0C,cAAc,CAAC,CAAC;MAEhDhC,MAAM,CAACV,SAAS,CAACgB,YAAY,CAAC,CAACG,gBAAgB,CAAC,CAAC;MACjDT,MAAM,CAACqB,OAAO,CAAC,CAACnB,OAAO,CAAC;QACtB,eAAe,EAAE;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFb,QAAQ,CAAC,oBAAoB,EAAE,MAAM;IACnCU,EAAE,CAAC,0DAA0D,EAAE,YAAY;MACzE;MACAT,SAAS,CAAC0C,cAAc,GAAGpD,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC0B,iBAAiB,CAAC;QACrD,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA,MAAM2B,YAAY,GAAG;QACnBrB,IAAI,EAAE;UACJA,IAAI,EAAE,CACJ;YAAEsB,EAAE,EAAE,QAAQ;YAAEC,IAAI,EAAE;UAAY,CAAC,EACnC;YAAED,EAAE,EAAE,QAAQ;YAAEC,IAAI,EAAE;UAAgB,CAAC,CACxC;UACDC,UAAU,EAAE;YACVC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,EAAE;YACTC,UAAU,EAAE,CAAC;YACbC,UAAU,EAAE;UACd;QACF;MACF,CAAC;MACDtD,KAAK,CAACuD,GAAG,CAACnC,iBAAiB,CAAC2B,YAAY,CAAC;MAEzC,MAAMS,MAAM,GAAG;QAAEC,MAAM,EAAE,QAAQ;QAAEL,KAAK,EAAE;MAAG,CAAC;MAC9C,MAAMM,MAAM,GAAG,MAAMvD,SAAS,CAACwD,kBAAkB,CAACH,MAAM,CAAC;MAEzD3C,MAAM,CAACb,KAAK,CAACuD,GAAG,CAAC,CAAC1B,oBAAoB,CACpC,GAAGzB,UAAU,CAACI,OAAO,iBAAiB,EACtC;QACEgD,MAAM;QACNtB,OAAO,EAAE;UACP,eAAe,EAAE,0BAA0B;UAC3C,cAAc,EAAE,kBAAkB;UAClC,QAAQ,EAAE;QACZ;MACF,CACF,CAAC;MAEDrB,MAAM,CAAC6C,MAAM,CAAC,CAAC3C,OAAO,CAACgC,YAAY,CAACrB,IAAI,CAAC;IAC3C,CAAC,CAAC;IAEFd,EAAE,CAAC,4CAA4C,EAAE,YAAY;MAC3D;MACAT,SAAS,CAAC0C,cAAc,GAAGpD,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC0B,iBAAiB,CAAC;QACrD,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA,MAAMkB,YAAY,GAAG,gBAAgB;MACrCtC,KAAK,CAACuD,GAAG,CAAChB,iBAAiB,CAAC,IAAIC,KAAK,CAACF,YAAY,CAAC,CAAC;MAEpD,MAAMzB,MAAM,CAACV,SAAS,CAACwD,kBAAkB,CAAC,CAAC,CAAC,CAAClB,OAAO,CAACC,OAAO,CAAC,iCAAiCJ,YAAY,EAAE,CAAC;IAC/G,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpC,QAAQ,CAAC,kBAAkB,EAAE,MAAM;IACjCU,EAAE,CAAC,kEAAkE,EAAE,YAAY;MACjF;MACAT,SAAS,CAAC0C,cAAc,GAAGpD,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC0B,iBAAiB,CAAC;QACrD,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA,MAAM2B,YAAY,GAAG;QACnBrB,IAAI,EAAE;UACJsB,EAAE,EAAE,UAAU;UACdC,IAAI,EAAE,WAAW;UACjBW,WAAW,EAAE;QACf;MACF,CAAC;MACD5D,KAAK,CAACuD,GAAG,CAACnC,iBAAiB,CAAC2B,YAAY,CAAC;MAEzC,MAAMc,eAAe,GAAG,UAAU;MAClC,MAAMH,MAAM,GAAG,MAAMvD,SAAS,CAAC2D,gBAAgB,CAACD,eAAe,CAAC;MAEhEhD,MAAM,CAACb,KAAK,CAACuD,GAAG,CAAC,CAAC1B,oBAAoB,CACpC,GAAGzB,UAAU,CAACI,OAAO,mBAAmBqD,eAAe,EAAE,EACzD;QACE3B,OAAO,EAAE;UACP,eAAe,EAAE,0BAA0B;UAC3C,cAAc,EAAE,kBAAkB;UAClC,QAAQ,EAAE;QACZ;MACF,CACF,CAAC;MAEDrB,MAAM,CAAC6C,MAAM,CAAC,CAAC3C,OAAO,CAACgC,YAAY,CAACrB,IAAI,CAAC;IAC3C,CAAC,CAAC;IAEFd,EAAE,CAAC,0DAA0D,EAAE,YAAY;MACzE,MAAMC,MAAM,CAACV,SAAS,CAAC2D,gBAAgB,CAAC,CAAC,CAAC,CAACrB,OAAO,CAACC,OAAO,CAAC,8BAA8B,CAAC;IAC5F,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxC,QAAQ,CAAC,iBAAiB,EAAE,MAAM;IAChCU,EAAE,CAAC,uDAAuD,EAAE,YAAY;MACtE;MACAT,SAAS,CAAC0C,cAAc,GAAGpD,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC0B,iBAAiB,CAAC;QACrD,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA,MAAM2B,YAAY,GAAG;QACnBrB,IAAI,EAAE;UACJA,IAAI,EAAE,CACJ;YAAEsB,EAAE,EAAE,UAAU;YAAEC,IAAI,EAAE;UAA8B,CAAC,EACvD;YAAED,EAAE,EAAE,UAAU;YAAEC,IAAI,EAAE;UAAsB,CAAC,CAChD;UACDC,UAAU,EAAE;YACVC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,EAAE;YACTC,UAAU,EAAE,CAAC;YACbC,UAAU,EAAE;UACd;QACF;MACF,CAAC;MACDtD,KAAK,CAACuD,GAAG,CAACnC,iBAAiB,CAAC2B,YAAY,CAAC;MAEzC,MAAMS,MAAM,GAAG;QAAEC,MAAM,EAAE,aAAa;QAAEI,eAAe,EAAE;MAAW,CAAC;MACrE,MAAMH,MAAM,GAAG,MAAMvD,SAAS,CAAC4D,eAAe,CAACP,MAAM,CAAC;MAEtD3C,MAAM,CAACb,KAAK,CAACuD,GAAG,CAAC,CAAC1B,oBAAoB,CACpC,GAAGzB,UAAU,CAACI,OAAO,cAAc,EACnC;QACEgD,MAAM;QACNtB,OAAO,EAAE;UACP,eAAe,EAAE,0BAA0B;UAC3C,cAAc,EAAE,kBAAkB;UAClC,QAAQ,EAAE;QACZ;MACF,CACF,CAAC;MAEDrB,MAAM,CAAC6C,MAAM,CAAC,CAAC3C,OAAO,CAACgC,YAAY,CAACrB,IAAI,CAAC;IAC3C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxB,QAAQ,CAAC,eAAe,EAAE,MAAM;IAC9BU,EAAE,CAAC,+DAA+D,EAAE,YAAY;MAC9E;MACAT,SAAS,CAAC0C,cAAc,GAAGpD,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC0B,iBAAiB,CAAC;QACrD,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA,MAAM2B,YAAY,GAAG;QACnBrB,IAAI,EAAE;UACJsB,EAAE,EAAE,YAAY;UAChBC,IAAI,EAAE,6BAA6B;UACnCQ,MAAM,EAAE;QACV;MACF,CAAC;MACDzD,KAAK,CAACuD,GAAG,CAACnC,iBAAiB,CAAC2B,YAAY,CAAC;MAEzC,MAAMiB,YAAY,GAAG,YAAY;MACjC,MAAMN,MAAM,GAAG,MAAMvD,SAAS,CAAC8D,aAAa,CAACD,YAAY,CAAC;MAE1DnD,MAAM,CAACb,KAAK,CAACuD,GAAG,CAAC,CAAC1B,oBAAoB,CACpC,GAAGzB,UAAU,CAACI,OAAO,gBAAgBwD,YAAY,EAAE,EACnD;QACE9B,OAAO,EAAE;UACP,eAAe,EAAE,0BAA0B;UAC3C,cAAc,EAAE,kBAAkB;UAClC,QAAQ,EAAE;QACZ;MACF,CACF,CAAC;MAEDrB,MAAM,CAAC6C,MAAM,CAAC,CAAC3C,OAAO,CAACgC,YAAY,CAACrB,IAAI,CAAC;IAC3C,CAAC,CAAC;IAEFd,EAAE,CAAC,uDAAuD,EAAE,YAAY;MACtE,MAAMC,MAAM,CAACV,SAAS,CAAC8D,aAAa,CAAC,CAAC,CAAC,CAACxB,OAAO,CAACC,OAAO,CAAC,2BAA2B,CAAC;IACtF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxC,QAAQ,CAAC,cAAc,EAAE,MAAM;IAC7BU,EAAE,CAAC,oDAAoD,EAAE,YAAY;MACnE;MACAT,SAAS,CAAC0C,cAAc,GAAGpD,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC0B,iBAAiB,CAAC;QACrD,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA,MAAM2B,YAAY,GAAG;QACnBrB,IAAI,EAAE;UACJA,IAAI,EAAE,CACJ;YAAEsB,EAAE,EAAE,YAAY;YAAEC,IAAI,EAAE;UAAuC,CAAC,EAClE;YAAED,EAAE,EAAE,YAAY;YAAEC,IAAI,EAAE;UAAyB,CAAC,CACrD;UACDC,UAAU,EAAE;YACVC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,EAAE;YACTC,UAAU,EAAE,CAAC;YACbC,UAAU,EAAE;UACd;QACF;MACF,CAAC;MACDtD,KAAK,CAACuD,GAAG,CAACnC,iBAAiB,CAAC2B,YAAY,CAAC;MAEzC,MAAMS,MAAM,GAAG;QAAEQ,YAAY,EAAE;MAAa,CAAC;MAC7C,MAAMN,MAAM,GAAG,MAAMvD,SAAS,CAAC+D,YAAY,CAACV,MAAM,CAAC;MAEnD3C,MAAM,CAACb,KAAK,CAACuD,GAAG,CAAC,CAAC1B,oBAAoB,CACpC,GAAGzB,UAAU,CAACI,OAAO,WAAW,EAChC;QACEgD,MAAM;QACNtB,OAAO,EAAE;UACP,eAAe,EAAE,0BAA0B;UAC3C,cAAc,EAAE,kBAAkB;UAClC,QAAQ,EAAE;QACZ;MACF,CACF,CAAC;MAEDrB,MAAM,CAAC6C,MAAM,CAAC,CAAC3C,OAAO,CAACgC,YAAY,CAACrB,IAAI,CAAC;IAC3C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxB,QAAQ,CAAC,aAAa,EAAE,MAAM;IAC5BU,EAAE,CAAC,6DAA6D,EAAE,YAAY;MAC5E;MACAT,SAAS,CAAC0C,cAAc,GAAGpD,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC0B,iBAAiB,CAAC;QACrD,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA,MAAM2B,YAAY,GAAG;QACnBrB,IAAI,EAAE;UACJsB,EAAE,EAAE,cAAc;UAClBC,IAAI,EAAE,sCAAsC;UAC5CkB,IAAI,EAAE;QACR;MACF,CAAC;MACDnE,KAAK,CAACuD,GAAG,CAACnC,iBAAiB,CAAC2B,YAAY,CAAC;MAEzC,MAAMqB,UAAU,GAAG,cAAc;MACjC,MAAMV,MAAM,GAAG,MAAMvD,SAAS,CAACkE,WAAW,CAACD,UAAU,CAAC;MAEtDvD,MAAM,CAACb,KAAK,CAACuD,GAAG,CAAC,CAAC1B,oBAAoB,CACpC,GAAGzB,UAAU,CAACI,OAAO,aAAa4D,UAAU,EAAE,EAC9C;QACElC,OAAO,EAAE;UACP,eAAe,EAAE,0BAA0B;UAC3C,cAAc,EAAE,kBAAkB;UAClC,QAAQ,EAAE;QACZ;MACF,CACF,CAAC;MAEDrB,MAAM,CAAC6C,MAAM,CAAC,CAAC3C,OAAO,CAACgC,YAAY,CAACrB,IAAI,CAAC;IAC3C,CAAC,CAAC;IAEFd,EAAE,CAAC,qDAAqD,EAAE,YAAY;MACpE,MAAMC,MAAM,CAACV,SAAS,CAACkE,WAAW,CAAC,CAAC,CAAC,CAAC5B,OAAO,CAACC,OAAO,CAAC,yBAAyB,CAAC;IAClF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}