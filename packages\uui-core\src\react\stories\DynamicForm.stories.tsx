import React from 'react';
import { ComponentStory, ComponentMeta } from '@storybook/react';
import RegulatoryContextProvider, { useRegulatoryContext } from '../RegulatoryContextProvider';

export default {
  title: 'UUIC/DynamicForm',
  component: DynamicForm,
  argTypes: {
    jurisdiction: {
      control: {
        type: 'select',
        options: ['eu', 'us-healthcare', 'us-finance', 'us-general', 'global']
      }
    },
    userRole: {
      control: {
        type: 'select',
        options: ['admin', 'compliance-officer', 'data-processor', 'auditor', 'user']
      }
    }
  }
} as ComponentMeta<typeof DynamicForm>;

// Create a template
const Template: ComponentStory<typeof DynamicForm> = (args) => (
  <RegulatoryContextProvider jurisdiction={args.jurisdiction} userRole={args.userRole}>
    <DynamicForm {...args} />
  </RegulatoryContextProvider>
);

// Dynamic Form component
function DynamicForm({ formTitle = 'User Registration' }) {
  const { activeRegulations, jurisdiction, userRole, loading } = useRegulatoryContext();
  
  if (loading) {
    return <div>Loading regulations...</div>;
  }
  
  // Determine which fields to show based on active regulations
  const showGDPRFields = activeRegulations.includes('GDPR');
  const showHIPAAFields = activeRegulations.includes('HIPAA');
  const showPCIDSSFields = activeRegulations.includes('PCI_DSS');
  
  return (
    <div style={{ fontFamily: 'Arial, sans-serif', maxWidth: '600px', margin: '0 auto', padding: '20px' }}>
      <div style={{ backgroundColor: '#f5f5f5', padding: '10px', marginBottom: '20px', borderRadius: '4px' }}>
        <strong>Active Jurisdiction:</strong> {jurisdiction} | <strong>User Role:</strong> {userRole}
      </div>
      
      <h2>{formTitle}</h2>
      
      <form style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
        {/* Basic Fields - Always shown */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
          <label htmlFor="firstName">First Name</label>
          <input 
            type="text" 
            id="firstName" 
            name="firstName" 
            style={{ padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }} 
            required 
          />
        </div>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
          <label htmlFor="lastName">Last Name</label>
          <input 
            type="text" 
            id="lastName" 
            name="lastName" 
            style={{ padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }} 
            required 
          />
        </div>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
          <label htmlFor="email">Email Address</label>
          <input 
            type="email" 
            id="email" 
            name="email" 
            style={{ padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }} 
            required 
          />
        </div>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
          <label htmlFor="password">Password</label>
          <input 
            type="password" 
            id="password" 
            name="password" 
            style={{ padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }} 
            required 
            minLength={showPCIDSSFields ? 8 : 6}
          />
          {showPCIDSSFields && (
            <small style={{ color: '#666' }}>PCI DSS requires passwords to be at least 8 characters</small>
          )}
        </div>
        
        {/* GDPR-specific Fields */}
        {showGDPRFields && (
          <div style={{ 
            backgroundColor: '#e6f7ff', 
            padding: '15px', 
            borderRadius: '4px',
            marginTop: '10px',
            marginBottom: '10px',
            border: '1px solid #91d5ff'
          }}>
            <h3 style={{ margin: '0 0 10px 0', color: '#0050b3' }}>GDPR Compliance</h3>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px' }}>
              <input 
                type="checkbox" 
                id="dataProcessingConsent" 
                name="dataProcessingConsent" 
                required 
              />
              <label htmlFor="dataProcessingConsent">
                I consent to the processing of my personal data
              </label>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px' }}>
              <input 
                type="checkbox" 
                id="marketingConsent" 
                name="marketingConsent" 
              />
              <label htmlFor="marketingConsent">
                I consent to receiving marketing communications
              </label>
            </div>
            
            <div style={{ fontSize: '0.9em', color: '#666', marginTop: '10px' }}>
              <strong>Data Retention Notice:</strong> Your data will be retained for 2 years after your last activity.
            </div>
            
            <div style={{ fontSize: '0.9em', color: '#666', marginTop: '10px' }}>
              <strong>Your Rights Under GDPR:</strong> You have the right to access, rectify, erase, restrict processing, object to processing, and data portability.
            </div>
          </div>
        )}
        
        {/* HIPAA-specific Fields */}
        {showHIPAAFields && (
          <div style={{ 
            backgroundColor: '#f6ffed', 
            padding: '15px', 
            borderRadius: '4px',
            marginTop: '10px',
            marginBottom: '10px',
            border: '1px solid #b7eb8f'
          }}>
            <h3 style={{ margin: '0 0 10px 0', color: '#389e0d' }}>HIPAA Compliance</h3>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px' }}>
              <input 
                type="checkbox" 
                id="hipaaAcknowledgement" 
                name="hipaaAcknowledgement" 
                required 
              />
              <label htmlFor="hipaaAcknowledgement">
                I acknowledge that I have received the HIPAA Privacy Notice
              </label>
            </div>
            
            <div style={{ fontSize: '0.9em', color: '#666', marginTop: '10px' }}>
              <strong>PHI Notice:</strong> This form may collect Protected Health Information (PHI).
            </div>
            
            <div style={{ fontSize: '0.9em', color: '#666', marginTop: '10px' }}>
              <strong>Authorization for Disclosure:</strong> By submitting this form, you authorize the disclosure of your health information for the purposes specified.
            </div>
          </div>
        )}
        
        <button 
          type="submit" 
          style={{ 
            padding: '10px 15px', 
            backgroundColor: '#1890ff', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer',
            marginTop: '10px'
          }}
        >
          Submit
        </button>
      </form>
    </div>
  );
}

// Create stories
export const EU = Template.bind({});
EU.args = {
  jurisdiction: 'eu',
  userRole: 'user',
  formTitle: 'User Registration (EU)'
};

export const USHealthcare = Template.bind({});
USHealthcare.args = {
  jurisdiction: 'us-healthcare',
  userRole: 'user',
  formTitle: 'Patient Registration (US Healthcare)'
};

export const USFinance = Template.bind({});
USFinance.args = {
  jurisdiction: 'us-finance',
  userRole: 'user',
  formTitle: 'Account Registration (US Finance)'
};

export const Global = Template.bind({});
Global.args = {
  jurisdiction: 'global',
  userRole: 'user',
  formTitle: 'Global User Registration'
};
