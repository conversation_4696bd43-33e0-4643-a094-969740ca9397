#!/usr/bin/env node

/**
 * Combine Swagger Files Script
 * 
 * This script combines all Swagger YAML files into a single file.
 */

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const logger = require('../config/logger');

// Paths
const swaggerDir = path.join(__dirname, '../swagger');
const outputFile = path.join(swaggerDir, 'combined.yaml');

// Main function
async function combineSwaggerFiles() {
  try {
    logger.info('Combining Swagger files...');
    
    // Read main index file
    const indexPath = path.join(swaggerDir, 'index.yaml');
    const indexContent = fs.readFileSync(indexPath, 'utf8');
    const indexYaml = yaml.load(indexContent);
    
    // Initialize combined paths and components
    indexYaml.paths = {};
    indexYaml.components = indexYaml.components || {};
    indexYaml.components.schemas = indexYaml.components.schemas || {};
    
    // Get all YAML files except index and combined
    const files = fs.readdirSync(swaggerDir)
      .filter(file => 
        file.endsWith('.yaml') && 
        file !== 'index.yaml' && 
        file !== 'combined.yaml'
      );
    
    // Process each file
    for (const file of files) {
      logger.info(`Processing ${file}...`);
      
      const filePath = path.join(swaggerDir, file);
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const fileYaml = yaml.load(fileContent);
      
      // Merge paths
      if (fileYaml.paths) {
        Object.assign(indexYaml.paths, fileYaml.paths);
      }
      
      // Merge components schemas
      if (fileYaml.components && fileYaml.components.schemas) {
        Object.assign(indexYaml.components.schemas, fileYaml.components.schemas);
      }
      
      // Merge tags
      if (fileYaml.tags) {
        // Filter out duplicate tags
        const existingTagNames = (indexYaml.tags || []).map(tag => tag.name);
        const newTags = fileYaml.tags.filter(tag => !existingTagNames.includes(tag.name));
        
        if (newTags.length > 0) {
          indexYaml.tags = (indexYaml.tags || []).concat(newTags);
        }
      }
    }
    
    // Write combined file
    const combinedYaml = yaml.dump(indexYaml, { lineWidth: -1 });
    fs.writeFileSync(outputFile, combinedYaml, 'utf8');
    
    logger.info(`Combined Swagger file written to ${outputFile}`);
  } catch (error) {
    logger.error('Error combining Swagger files:', error);
    process.exit(1);
  }
}

// Run the script
combineSwaggerFiles();
