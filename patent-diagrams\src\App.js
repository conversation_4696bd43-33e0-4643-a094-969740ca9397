import React, { useState } from 'react';
import styled from 'styled-components';
import './App.css';

// Import all diagram components
import FinancialServicesArchitecture from './diagrams/FinancialServicesArchitecture';
import AutomatedAuditTrail from './diagrams/AutomatedAuditTrail';
import ExplainableAI from './diagrams/ExplainableAI';
import DeFiCompliance from './diagrams/DeFiCompliance';
import IoTPaymentSecurity from './diagrams/IoTPaymentSecurity';
import RegulatoryKillSwitch from './diagrams/RegulatoryKillSwitch';
import DynamicRiskScoring from './diagrams/DynamicRiskScoring';
import SelfLearningFraud from './diagrams/SelfLearningFraud';
import CrossBorderCompliance from './diagrams/CrossBorderCompliance';
import FraudComplianceBridge from './diagrams/FraudComplianceBridge';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 30px;
`;

const Title = styled.h1`
  font-size: 24px;
  color: #333;
`;

const DiagramSelector = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
`;

const Button = styled.button`
  padding: 8px 16px;
  background-color: ${props => props.active ? '#555555' : '#f0f0f0'};
  color: ${props => props.active ? 'white' : '#333'};
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${props => props.active ? '#666666' : '#e0e0e0'};
  }
`;

const DiagramContainer = styled.div`
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 30px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
`;

const DiagramTitle = styled.h2`
  font-size: 20px;
  color: #333;
  text-align: center;
  margin-bottom: 30px;
`;

const Instructions = styled.div`
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-top: 30px;
`;

function App() {
  const [activeDiagram, setActiveDiagram] = useState('architecture');

  const diagrams = [
    { id: 'architecture', name: 'FIG. 1: Financial Services System Architecture', component: <FinancialServicesArchitecture /> },
    { id: 'auditTrail', name: 'FIG. 2: Automated Audit Trail Generation', component: <AutomatedAuditTrail /> },
    { id: 'explainableAI', name: 'FIG. 3: Explainable AI with Rule Attribution', component: <ExplainableAI /> },
    { id: 'defiCompliance', name: 'FIG. 4: DeFi Smart Contract Compliance Layer', component: <DeFiCompliance /> },
    { id: 'iotSecurity', name: 'FIG. 5: IoT Payment Device PCI-DSS Validation', component: <IoTPaymentSecurity /> },
    { id: 'killSwitch', name: 'FIG. 6: Regulatory Kill Switch', component: <RegulatoryKillSwitch /> },
    { id: 'riskScoring', name: 'FIG. 7: Dynamic Risk Scoring Engine', component: <DynamicRiskScoring /> },
    { id: 'selfLearning', name: 'FIG. 8: Self-Learning Fraud System', component: <SelfLearningFraud /> },
    { id: 'crossBorder', name: 'FIG. 9: Cross-Border Transaction Compliance', component: <CrossBorderCompliance /> },
    { id: 'fraudBridge', name: 'FIG. 10: Fraud-to-Compliance Bridge API', component: <FraudComplianceBridge /> },
  ];

  const activeDiagramObj = diagrams.find(d => d.id === activeDiagram);

  return (
    <Container>
      <Header>
        <Title>Financial Services Continuance Patent: Diagrams</Title>
      </Header>

      <DiagramSelector>
        {diagrams.map(diagram => (
          <Button
            key={diagram.id}
            active={activeDiagram === diagram.id}
            onClick={() => setActiveDiagram(diagram.id)}
          >
            {diagram.name.split(':')[0]}
          </Button>
        ))}
      </DiagramSelector>

      <DiagramContainer>
        <DiagramTitle>{activeDiagramObj.name}</DiagramTitle>
        {activeDiagramObj.component}
      </DiagramContainer>

      <Instructions>
        <h3>Instructions for Taking Screenshots:</h3>
        <ol>
          <li>Select the diagram you want to capture using the buttons above</li>
          <li>Use your operating system's screenshot tool to capture the diagram</li>
          <li>For Windows: Use Windows+Shift+S to open the snipping tool</li>
          <li>For Mac: Use Command+Shift+4 to capture a selected area</li>
          <li>Save the screenshot with the figure number and name</li>
          <li>Repeat for all diagrams</li>
        </ol>
        <p>These diagrams are designed to be patent-compliant with clear component relationships and data flows.</p>
      </Instructions>
    </Container>
  );
}

export default App;
