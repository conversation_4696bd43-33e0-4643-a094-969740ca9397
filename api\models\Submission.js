const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const SubmissionSchema = new Schema({
  connectorName: {
    type: String,
    required: true,
    trim: true
  },
  vendorName: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  longDescription: {
    type: String,
    trim: true
  },
  category: {
    type: String,
    required: true,
    enum: ['data-privacy', 'security', 'healthcare', 'financial', 'other']
  },
  framework: {
    type: String,
    required: true,
    enum: ['gdpr', 'hipaa', 'soc2', 'pci-dss', 'iso-27001', 'ccpa', 'nist', 'finra', 'fedramp', 'other']
  },
  price: {
    type: String,
    required: true,
    trim: true
  },
  features: {
    type: String,
    trim: true
  },
  integrations: {
    type: String,
    trim: true
  },
  submittedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'in-review', 'approved', 'rejected'],
    default: 'pending'
  },
  reviewNotes: {
    type: String,
    trim: true
  },
  reviewedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewedAt: Date,
  connectorId: {
    type: Schema.Types.ObjectId,
    ref: 'Connector'
  },
  contactEmail: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  contactPhone: {
    type: String,
    trim: true
  },
  website: {
    type: String,
    trim: true
  },
  submittedAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Submission', SubmissionSchema);
