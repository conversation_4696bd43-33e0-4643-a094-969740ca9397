<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse Large File Upload</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    
    h1 {
      color: #0A84FF;
      border-bottom: 2px solid #0A84FF;
      padding-bottom: 10px;
    }
    
    .upload-container {
      border: 2px dashed #ccc;
      border-radius: 10px;
      padding: 20px;
      text-align: center;
      margin: 20px 0;
      transition: all 0.3s;
    }
    
    .upload-container.drag-over {
      border-color: #0A84FF;
      background-color: rgba(10, 132, 255, 0.1);
    }
    
    .file-input {
      display: none;
    }
    
    .select-button {
      background-color: #0A84FF;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
      margin: 10px 0;
    }
    
    .select-button:hover {
      background-color: #0066cc;
    }
    
    .file-list {
      margin-top: 20px;
      text-align: left;
    }
    
    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      border-bottom: 1px solid #eee;
    }
    
    .file-name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 10px;
    }
    
    .file-size {
      color: #666;
      margin-right: 10px;
    }
    
    .remove-button {
      background-color: #ff3b30;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 3px;
      cursor: pointer;
    }
    
    .progress-container {
      width: 100%;
      height: 20px;
      background-color: #f0f0f0;
      border-radius: 10px;
      margin-top: 5px;
      overflow: hidden;
    }
    
    .progress-bar {
      height: 100%;
      background-color: #0A84FF;
      width: 0%;
      transition: width 0.3s;
    }
    
    .upload-button {
      background-color: #34c759;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 20px;
      width: 100%;
    }
    
    .upload-button:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
    
    .result-container {
      margin-top: 20px;
      padding: 15px;
      border-radius: 5px;
      background-color: #f8f9fa;
      border: 1px solid #ddd;
      display: none;
    }
    
    .result-success {
      border-color: #34c759;
      background-color: rgba(52, 199, 89, 0.1);
    }
    
    .result-error {
      border-color: #ff3b30;
      background-color: rgba(255, 59, 48, 0.1);
    }
    
    pre {
      background-color: #f0f0f0;
      padding: 10px;
      border-radius: 5px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>NovaFuse Large File Upload</h1>
  
  <p>This demo showcases the ability to upload and process large files using streaming technology.</p>
  
  <div class="upload-container" id="dropZone">
    <p>Drag and drop files here or</p>
    <input type="file" id="fileInput" class="file-input" multiple>
    <button class="select-button" id="selectButton">Select Files</button>
    <p>Maximum file size: 1GB</p>
  </div>
  
  <div class="file-list" id="fileList"></div>
  
  <button class="upload-button" id="uploadButton" disabled>Upload Files</button>
  
  <div class="result-container" id="resultContainer">
    <h3>Upload Result</h3>
    <pre id="resultContent"></pre>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const dropZone = document.getElementById('dropZone');
      const fileInput = document.getElementById('fileInput');
      const selectButton = document.getElementById('selectButton');
      const fileList = document.getElementById('fileList');
      const uploadButton = document.getElementById('uploadButton');
      const resultContainer = document.getElementById('resultContainer');
      const resultContent = document.getElementById('resultContent');
      
      let files = [];
      
      // Handle file selection button
      selectButton.addEventListener('click', function() {
        fileInput.click();
      });
      
      // Handle file input change
      fileInput.addEventListener('change', function() {
        handleFiles(this.files);
      });
      
      // Handle drag and drop events
      dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropZone.classList.add('drag-over');
      });
      
      dropZone.addEventListener('dragleave', function() {
        dropZone.classList.remove('drag-over');
      });
      
      dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dropZone.classList.remove('drag-over');
        handleFiles(e.dataTransfer.files);
      });
      
      // Handle upload button
      uploadButton.addEventListener('click', uploadFiles);
      
      // Function to handle selected files
      function handleFiles(fileList) {
        for (let i = 0; i < fileList.length; i++) {
          const file = fileList[i];
          
          // Check if file is already in the list
          if (files.some(f => f.name === file.name && f.size === file.size)) {
            continue;
          }
          
          files.push(file);
          
          // Create file item element
          const fileItem = document.createElement('div');
          fileItem.className = 'file-item';
          fileItem.dataset.fileName = file.name;
          
          // File name
          const fileName = document.createElement('div');
          fileName.className = 'file-name';
          fileName.textContent = file.name;
          
          // File size
          const fileSize = document.createElement('div');
          fileSize.className = 'file-size';
          fileSize.textContent = formatFileSize(file.size);
          
          // Remove button
          const removeButton = document.createElement('button');
          removeButton.className = 'remove-button';
          removeButton.textContent = 'Remove';
          removeButton.addEventListener('click', function() {
            files = files.filter(f => f.name !== file.name);
            fileItem.remove();
            updateUploadButton();
          });
          
          // Progress container
          const progressContainer = document.createElement('div');
          progressContainer.className = 'progress-container';
          progressContainer.style.display = 'none';
          
          // Progress bar
          const progressBar = document.createElement('div');
          progressBar.className = 'progress-bar';
          progressContainer.appendChild(progressBar);
          
          // Add elements to file item
          fileItem.appendChild(fileName);
          fileItem.appendChild(fileSize);
          fileItem.appendChild(removeButton);
          fileItem.appendChild(progressContainer);
          
          // Add file item to list
          document.getElementById('fileList').appendChild(fileItem);
        }
        
        updateUploadButton();
      }
      
      // Function to update upload button state
      function updateUploadButton() {
        uploadButton.disabled = files.length === 0;
      }
      
      // Function to format file size
      function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      }
      
      // Function to upload files
      async function uploadFiles() {
        // Disable upload button
        uploadButton.disabled = true;
        
        // Reset result container
        resultContainer.style.display = 'none';
        resultContainer.className = 'result-container';
        
        try {
          // Upload each file
          for (const file of files) {
            await uploadFile(file);
          }
          
          // Show success message
          resultContainer.style.display = 'block';
          resultContainer.classList.add('result-success');
          resultContent.textContent = 'All files uploaded successfully!';
          
          // Clear file list
          files = [];
          fileList.innerHTML = '';
          updateUploadButton();
        } catch (error) {
          // Show error message
          resultContainer.style.display = 'block';
          resultContainer.classList.add('result-error');
          resultContent.textContent = 'Error: ' + error.message;
          
          // Re-enable upload button
          uploadButton.disabled = false;
        }
      }
      
      // Function to upload a single file
      function uploadFile(file) {
        return new Promise((resolve, reject) => {
          // Get file item element
          const fileItem = document.querySelector(`.file-item[data-file-name="${file.name}"]`);
          const progressContainer = fileItem.querySelector('.progress-container');
          const progressBar = fileItem.querySelector('.progress-bar');
          const removeButton = fileItem.querySelector('.remove-button');
          
          // Show progress container
          progressContainer.style.display = 'block';
          
          // Disable remove button
          removeButton.disabled = true;
          
          // Create form data
          const formData = new FormData();
          formData.append('file', file);
          
          // Create XMLHttpRequest
          const xhr = new XMLHttpRequest();
          
          // Track upload progress
          xhr.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
              const percentComplete = (e.loaded / e.total) * 100;
              progressBar.style.width = percentComplete + '%';
            }
          });
          
          // Handle response
          xhr.addEventListener('load', function() {
            if (xhr.status >= 200 && xhr.status < 300) {
              // Success
              progressBar.style.width = '100%';
              resolve(JSON.parse(xhr.responseText));
            } else {
              // Error
              reject(new Error(xhr.statusText || 'Upload failed'));
            }
          });
          
          // Handle error
          xhr.addEventListener('error', function() {
            reject(new Error('Network error'));
          });
          
          // Handle abort
          xhr.addEventListener('abort', function() {
            reject(new Error('Upload aborted'));
          });
          
          // Open request
          xhr.open('POST', '/api/v1/large-files/upload');
          
          // Send form data
          xhr.send(formData);
        });
      }
    });
  </script>
</body>
</html>
