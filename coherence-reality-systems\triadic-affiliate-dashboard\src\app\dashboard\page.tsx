import { motion } from 'framer-motion'
import { ProductScanner } from '@/components/ProductScanner'
import { PerformanceMetrics } from '@/components/PerformanceMetrics'
import { TriadicMetrics } from '@/components/TriadicMetrics'
import { ConsciousnessMonitor } from '@/components/ConsciousnessMonitor'
import { AffiliateNetworks } from '@/components/dashboard/AffiliateNetworks'
import { ProductCategories } from '@/components/dashboard/ProductCategories'
import { ConversionTracker } from '@/components/dashboard/ConversionTracker'

export default function Dashboard() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {/* Main Dashboard Widgets */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10"
        >
          <h2 className="text-xl font-bold mb-4">Product Scanner</h2>
          <ProductScanner />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10"
        >
          <h2 className="text-xl font-bold mb-4">Performance Metrics</h2>
          <PerformanceMetrics />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10"
        >
          <h2 className="text-xl font-bold mb-4">Triadic Impact</h2>
          <TriadicMetrics />
        </motion.div>

        {/* Additional Dashboard Sections */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10"
        >
          <h2 className="text-xl font-bold mb-4">Affiliate Networks</h2>
          <AffiliateNetworks />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10"
        >
          <h2 className="text-xl font-bold mb-4">Product Categories</h2>
          <ProductCategories />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10"
        >
          <h2 className="text-xl font-bold mb-4">Conversion Tracker</h2>
          <ConversionTracker />
        </motion.div>
      </div>
    </div>
  )
}
