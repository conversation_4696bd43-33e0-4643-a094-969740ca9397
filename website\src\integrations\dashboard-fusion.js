import { useEffect, useRef, useMemo } from 'react';
import { useNova3DScene } from '@novafuse/nova-vision-3d';
import { useQuantumStream } from '../hooks/useQuantumStream';
import { useAdaptiveStream } from '../hooks/useAdaptiveStream';
import { validateMessageSecurity } from '../config/quantum-security';
import { encodeQuantumMessage } from '../utils/quantumCodec';

// Default configuration for the dashboard
const DASHBOARD_CONFIG = {
  // Stream configuration
  stream: {
    id: 'quantum-governance',
    reconnectAttempts: 5,
    reconnectDelay: 1000,
    binaryMode: true,
    deltaEncoding: true
  },
  
  // Visualization settings
  visualization: {
    initialQuality: 'high',
    maxFps: 60,
    minFps: 30,
    showStats: true,
    showControls: true,
    showGrid: true,
    showAxes: false
  },
  
  // Performance settings
  performance: {
    gpuAcceleration: true,
    gpuMinSpec: {
      vram: 2, // GB
      api: 'webgl2',
      fallback: 'webgl'
    },
    workerCount: navigator.hardwareConcurrency || 4
  }
};

/**
 * Quantum Dashboard Fusion Component
 * 
 * Integrates quantum streaming, 3D visualization, and adaptive quality control
 * into a cohesive dashboard experience.
 * 
 * @param {Object} props - Component props
 * @param {string} [props.streamId] - Quantum stream ID
 * @param {Object} [props.config] - Configuration overrides
 * @param {function} [props.onStateUpdate] - Callback for state updates
 * @param {function} [props.onError] - Error handler
 * @returns {JSX.Element} - Dashboard component
 */
export function QuantumDashboardFusion({
  streamId = DASHBOARD_CONFIG.stream.id,
  config: userConfig = {},
  onStateUpdate,
  onError
}) {
  // Merge default config with user overrides
  const config = useMemo(() => ({
    stream: { ...DASHBOARD_CONFIG.stream, ...userConfig.stream },
    visualization: { ...DASHBOARD_CONFIG.visualization, ...userConfig.visualization },
    performance: { ...DASHBOARD_CONFIG.performance, ...userConfig.performance }
  }), [userConfig]);
  
  // Initialize Nova3D scene
  const sceneRef = useNova3DScene('quantum-dashboard', {
    antialias: true,
    alpha: true,
    powerPreference: 'high-performance',
    ...config.visualization
  });
  
  // Initialize quantum stream
  const { 
    data: quantumData, 
    stats: streamStats, 
    error: streamError,
    isConnected,
    send: sendToStream
  } = useQuantumStream({
    streamId,
    autoConnect: true,
    binaryMode: config.stream.binaryMode,
    deltaEncoding: config.stream.deltaEncoding,
    onMessage: handleQuantumMessage,
    onError: handleStreamError
  });
  
  // Initialize adaptive quality control
  const {
    quality,
    profile: qualityProfile,
    recordFrame,
    getMetrics: getPerformanceMetrics
  } = useAdaptiveStream({
    initialQuality: config.visualization.initialQuality,
    targetFps: config.visualization.maxFps,
    minFps: config.visualization.minFps,
    onQualityChange: handleQualityChange
  });
  
  // Track performance metrics
  const lastFrameTime = useRef(0);
  const frameTimes = useRef([]);
  
  /**
   * Handle incoming quantum messages
   */
  function handleQuantumMessage(message, isBinary) {
    try {
      // Validate message security
      if (!validateMessageSecurity(message, {
        maxAge: 5000, // 5 seconds
        requireSignatures: true
      })) {
        throw new Error('Invalid message security');
      }
      
      // Update 3D scene with new quantum data
      if (sceneRef.current) {
        const startTime = performance.now();
        
        sceneRef.current.updateQuantumState({
          qubits: message.qubitMatrix,
          entanglement: message.entanglementGraph,
          coherence: message.coherenceScores,
          timestamp: message.timestamp,
          quality: qualityProfile
        });
        
        // Record frame time for performance monitoring
        const frameTime = performance.now() - startTime;
        recordFrame(frameTime);
        
        // Update frame time tracking
        const now = performance.now();
        frameTimes.current.push(now - lastFrameTime.current);
        lastFrameTime.current = now;
        
        // Keep only recent frame times (last 60 frames)
        if (frameTimes.current.length > 60) {
          frameTimes.current.shift();
        }
      }
      
      // Notify parent component
      onStateUpdate?.(message);
      
    } catch (error) {
      console.error('Error processing quantum message:', error);
      handleStreamError(error);
    }
  }
  
  /**
   * Handle stream errors
   */
  function handleStreamError(error) {
    console.error('Quantum stream error:', error);
    
    // Notify parent component
    onError?.(error);
    
    // Try to recover from certain errors
    if (error.isRecoverable) {
      setTimeout(() => {
        console.log('Attempting to recover from error...');
        // Reconnect or take other recovery actions
      }, 1000);
    }
  }
  
  /**
   * Handle quality level changes
   */
  function handleQualityChange(newQuality, oldQuality, metrics) {
    console.log(`Quality changed: ${oldQuality} -> ${newQuality}`, metrics);
    
    // Apply quality settings to 3D scene
    if (sceneRef.current) {
      sceneRef.current.setQuality(QUALITY_PROFILES[newQuality]);
    }
    
    // Adjust stream parameters based on quality
    const updateRate = QUALITY_PROFILES[newQuality].updateRate;
    // TODO: Send quality update to stream
  }
  
  /**
   * Get current performance metrics
   */
  function getCombinedMetrics() {
    const perfMetrics = getPerformanceMetrics();
    const avgFrameTime = frameTimes.current.length > 0 
      ? frameTimes.current.reduce((a, b) => a + b, 0) / frameTimes.current.length 
      : 0;
    
    return {
      ...perfMetrics,
      stream: {
        ...streamStats,
        isConnected,
        quality
      },
      rendering: {
        frameTime: avgFrameTime,
        fps: avgFrameTime > 0 ? 1000 / avgFrameTime : 0,
        quality,
        ...perfMetrics.rendering
      },
      timestamp: Date.now()
    };
  }
  
  // Set up scene on mount
  useEffect(() => {
    if (!sceneRef.current) return;
    
    // Configure initial scene settings
    sceneRef.current.setQuality(qualityProfile);
    sceneRef.current.setCameraPosition(0, 5, 10);
    sceneRef.current.lookAt(0, 0, 0);
    
    // Set up lighting
    sceneRef.current.addLight({
      type: 'directional',
      color: 0xffffff,
      intensity: 1,
      position: [5, 10, 5],
      castShadow: true
    });
    
    // Set up environment
    sceneRef.current.setEnvironment({
      background: 0x0a0a12,
      fog: {
        color: 0x0a0a12,
        near: 1,
        far: 50
      }
    });
    
    // Start animation loop
    let animationFrameId;
    const animate = () => {
      if (sceneRef.current) {
        sceneRef.current.render();
      }
      animationFrameId = requestAnimationFrame(animate);
    };
    
    animationFrameId = requestAnimationFrame(animate);
    
    // Clean up
    return () => {
      cancelAnimationFrame(animationFrameId);
      if (sceneRef.current) {
        sceneRef.current.dispose();
      }
    };
  }, [sceneRef, qualityProfile]);
  
  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (sceneRef.current) {
        sceneRef.current.setSize(window.innerWidth, window.innerHeight);
      }
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [sceneRef]);
  
  // No DOM rendering - this is a headless component that manages state
  return null;
}

// Quality profiles for the dashboard
const QUALITY_PROFILES = {
  high: {
    resolution: 1.0,
    msaa: 4,
    shadows: true,
    particles: 10000,
    lodBias: 1.0,
    physicsRate: 60,
    physicsSubsteps: 2,
    updateRate: 60,
    deltaEncoding: true,
    binaryProtocol: true,
    bloom: true,
    ssao: true,
    motionBlur: true,
    stats: true,
    wireframe: false
  },
  medium: {
    resolution: 0.8,
    msaa: 2,
    shadows: true,
    particles: 5000,
    lodBias: 0.8,
    physicsRate: 45,
    physicsSubsteps: 1,
    updateRate: 45,
    deltaEncoding: true,
    binaryProtocol: true,
    bloom: true,
    ssao: false,
    motionBlur: false,
    stats: true,
    wireframe: false
  },
  low: {
    resolution: 0.6,
    msaa: 1,
    shadows: false,
    particles: 2000,
    lodBias: 0.6,
    physicsRate: 30,
    physicsSubsteps: 1,
    updateRate: 30,
    deltaEncoding: true,
    binaryProtocol: true,
    bloom: false,
    ssao: false,
    motionBlur: false,
    stats: false,
    wireframe: false
  }
};

/**
 * Higher-order component for easy integration with React components
 */
export function withQuantumDashboard(Component, config = {}) {
  return function WrappedComponent(props) {
    const dashboardRef = useRef();
    const [metrics, setMetrics] = useState({});
    
    // Update metrics periodically
    useEffect(() => {
      const interval = setInterval(() => {
        if (dashboardRef.current) {
          setMetrics(dashboardRef.current.getMetrics());
        }
      }, 1000);
      
      return () => clearInterval(interval);
    }, []);
    
    return (
      <>
        <QuantumDashboardFusion
          ref={dashboardRef}
          config={config}
          onStateUpdate={props.onStateUpdate}
          onError={props.onError}
        />
        <Component {...props} quantumMetrics={metrics} />
      </>
    );
  };
}
