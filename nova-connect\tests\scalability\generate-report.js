/**
 * NovaConnect UAC Scalability Test Report Generator
 * 
 * This script generates a comprehensive report from scalability test results.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  resultsDir: path.join(__dirname, 'results'),
  reportFile: path.join(__dirname, 'results', 'scalability-report.md'),
  tests: [
    {
      name: 'API Load Test',
      script: 'api-load-test.js',
      description: 'Tests the performance and scalability of NovaConnect UAC API endpoints'
    },
    {
      name: 'Normalization Load Test',
      script: 'normalization-load-test.js',
      description: 'Tests the performance and scalability of NovaConnect UAC data normalization'
    },
    {
      name: 'Connector Load Test',
      script: 'connector-load-test.js',
      description: 'Tests the performance and scalability of NovaConnect UAC connectors'
    }
  ]
};

/**
 * Generate a report from test results
 */
function generateReport() {
  console.log('Generating report...');
  
  // Read test results
  const results = [];
  
  for (const test of config.tests) {
    const summaryFile = path.join(config.resultsDir, `${test.script.replace('.js', '')}-summary.json`);
    
    try {
      if (fs.existsSync(summaryFile)) {
        const summary = JSON.parse(fs.readFileSync(summaryFile, 'utf8'));
        results.push({
          test,
          summary
        });
      }
    } catch (error) {
      console.error(`Error reading summary file for ${test.name}: ${error.message}`);
    }
  }
  
  // Create report content
  let report = `# NovaConnect UAC Scalability Test Report\n\n`;
  report += `Generated on: ${new Date().toISOString()}\n\n`;
  
  // Add system information
  report += `## System Information\n\n`;
  report += `- Test Environment: Docker\n`;
  report += `- API Base URL: http://nova-connect:3001\n\n`;
  
  // Add summary
  report += `## Summary\n\n`;
  report += `| Test | HTTP Requests | Failed Requests | Avg Response Time | P95 Response Time | RPS |\n`;
  report += `| --- | --- | --- | --- | --- | --- |\n`;
  
  for (const result of results) {
    const metrics = result.summary.metrics;
    const httpReqs = metrics['http_reqs'] ? metrics['http_reqs'].values.count : 0;
    const failedReqs = metrics['http_req_failed'] ? metrics['http_req_failed'].values.passes : 0;
    const avgResponseTime = metrics['http_req_duration'] ? metrics['http_req_duration'].values.avg.toFixed(2) : 0;
    const p95ResponseTime = metrics['http_req_duration'] ? metrics['http_req_duration'].values['p(95)'].toFixed(2) : 0;
    const rps = httpReqs / (result.summary.testRunDurationMs / 1000);
    
    report += `| ${result.test.name} | ${httpReqs} | ${failedReqs} | ${avgResponseTime}ms | ${p95ResponseTime}ms | ${rps.toFixed(2)} |\n`;
  }
  
  report += `\n`;
  
  // Add detailed results
  report += `## Detailed Results\n\n`;
  
  for (const result of results) {
    report += `### ${result.test.name}\n\n`;
    report += `${result.test.description}\n\n`;
    
    // Add metrics
    report += `#### Metrics\n\n`;
    report += `| Metric | Avg | Min | Med | P90 | P95 | P99 | Max |\n`;
    report += `| --- | --- | --- | --- | --- | --- | --- | --- |\n`;
    
    const metrics = result.summary.metrics;
    
    // HTTP request duration
    const httpReqDuration = metrics['http_req_duration'];
    if (httpReqDuration) {
      report += `| HTTP Request Duration | ${httpReqDuration.values.avg.toFixed(2)}ms | ${httpReqDuration.values.min.toFixed(2)}ms | ${httpReqDuration.values.med.toFixed(2)}ms | ${httpReqDuration.values['p(90)'].toFixed(2)}ms | ${httpReqDuration.values['p(95)'].toFixed(2)}ms | ${httpReqDuration.values['p(99)'].toFixed(2)}ms | ${httpReqDuration.values.max.toFixed(2)}ms |\n`;
    }
    
    // Data normalization duration
    const dataNormalizationDuration = metrics['data_normalization_duration'];
    if (dataNormalizationDuration) {
      report += `| Data Normalization Duration | ${dataNormalizationDuration.values.avg.toFixed(2)}ms | ${dataNormalizationDuration.values.min.toFixed(2)}ms | ${dataNormalizationDuration.values.med.toFixed(2)}ms | ${dataNormalizationDuration.values['p(90)'].toFixed(2)}ms | ${dataNormalizationDuration.values['p(95)'].toFixed(2)}ms | ${dataNormalizationDuration.values['p(99)'].toFixed(2)}ms | ${dataNormalizationDuration.values.max.toFixed(2)}ms |\n`;
    }
    
    // Connector execution duration
    const connectorExecutionDuration = metrics['connector_execution_duration'];
    if (connectorExecutionDuration) {
      report += `| Connector Execution Duration | ${connectorExecutionDuration.values.avg.toFixed(2)}ms | ${connectorExecutionDuration.values.min.toFixed(2)}ms | ${connectorExecutionDuration.values.med.toFixed(2)}ms | ${connectorExecutionDuration.values['p(90)'].toFixed(2)}ms | ${connectorExecutionDuration.values['p(95)'].toFixed(2)}ms | ${connectorExecutionDuration.values['p(99)'].toFixed(2)}ms | ${connectorExecutionDuration.values.max.toFixed(2)}ms |\n`;
    }
    
    // Error rate
    const errorRate = metrics['error_rate'];
    if (errorRate) {
      report += `| Error Rate | ${(errorRate.values.rate * 100).toFixed(2)}% | - | - | - | - | - | - |\n`;
    }
    
    report += `\n`;
    
    // Add checks
    report += `#### Checks\n\n`;
    report += `| Check | Pass Rate |\n`;
    report += `| --- | --- |\n`;
    
    for (const [name, check] of Object.entries(result.summary.root_group.checks)) {
      const passRate = (check.passes / (check.passes + check.fails) * 100).toFixed(2);
      report += `| ${name} | ${passRate}% |\n`;
    }
    
    report += `\n`;
    
    // Add thresholds
    report += `#### Thresholds\n\n`;
    
    if (Object.keys(result.summary.metrics).length > 0) {
      report += `| Threshold | Result |\n`;
      report += `| --- | --- |\n`;
      
      for (const [name, metric] of Object.entries(result.summary.metrics)) {
        if (metric.thresholds) {
          for (const [threshold, thresholdResult] of Object.entries(metric.thresholds)) {
            report += `| ${name} ${threshold} | ${thresholdResult.ok ? '✅ Pass' : '❌ Fail'} |\n`;
          }
        }
      }
    } else {
      report += `No thresholds defined.\n`;
    }
    
    report += `\n`;
  }
  
  // Add recommendations
  report += `## Recommendations\n\n`;
  
  // Check for performance issues
  let hasPerformanceIssues = false;
  let hasErrorIssues = false;
  let hasScalabilityIssues = false;
  
  for (const result of results) {
    const metrics = result.summary.metrics;
    
    // Check HTTP request duration
    const httpReqDuration = metrics['http_req_duration'];
    if (httpReqDuration && httpReqDuration.values['p(95)'] > 500) {
      hasPerformanceIssues = true;
    }
    
    // Check error rate
    const errorRate = metrics['error_rate'];
    if (errorRate && errorRate.values.rate > 0.01) {
      hasErrorIssues = true;
    }
    
    // Check for failed thresholds
    for (const [name, metric] of Object.entries(metrics)) {
      if (metric.thresholds) {
        for (const [threshold, thresholdResult] of Object.entries(metric.thresholds)) {
          if (!thresholdResult.ok) {
            hasScalabilityIssues = true;
          }
        }
      }
    }
  }
  
  if (hasPerformanceIssues) {
    report += `### Performance Issues\n\n`;
    report += `Some API endpoints have high response times (P95 > 500ms). Consider the following optimizations:\n\n`;
    report += `- Implement caching for frequently accessed data\n`;
    report += `- Optimize database queries\n`;
    report += `- Use connection pooling for external services\n`;
    report += `- Implement request batching for high-volume operations\n\n`;
  }
  
  if (hasErrorIssues) {
    report += `### Error Issues\n\n`;
    report += `The error rate is higher than expected (> 1%). Consider the following improvements:\n\n`;
    report += `- Enhance error handling and recovery mechanisms\n`;
    report += `- Implement circuit breakers for external services\n`;
    report += `- Add retry logic for transient failures\n`;
    report += `- Improve input validation\n\n`;
  }
  
  if (hasScalabilityIssues) {
    report += `### Scalability Issues\n\n`;
    report += `Some thresholds were not met during the tests. Consider the following scalability enhancements:\n\n`;
    report += `- Implement horizontal scaling\n`;
    report += `- Use a load balancer\n`;
    report += `- Optimize resource utilization\n`;
    report += `- Implement database sharding\n`;
    report += `- Use a message queue for asynchronous processing\n\n`;
  }
  
  if (!hasPerformanceIssues && !hasErrorIssues && !hasScalabilityIssues) {
    report += `No significant issues were found during the scalability tests. The NovaConnect UAC is performing well under the tested load.\n\n`;
    report += `To further enhance performance and scalability, consider the following proactive measures:\n\n`;
    report += `- Implement a CDN for static assets\n`;
    report += `- Use a distributed cache for frequently accessed data\n`;
    report += `- Implement database read replicas\n`;
    report += `- Set up auto-scaling based on load metrics\n`;
    report += `- Implement a monitoring and alerting system\n\n`;
  }
  
  // Add Google Cloud Marketplace readiness assessment
  report += `## Google Cloud Marketplace Readiness Assessment\n\n`;
  report += `Based on the scalability test results, here is an assessment of NovaConnect UAC's readiness for Google Cloud Marketplace:\n\n`;
  
  // Calculate overall score
  let performanceScore = hasPerformanceIssues ? 2 : 4;
  let reliabilityScore = hasErrorIssues ? 2 : 4;
  let scalabilityScore = hasScalabilityIssues ? 2 : 4;
  
  // Adjust scores based on metrics
  for (const result of results) {
    const metrics = result.summary.metrics;
    
    // Adjust performance score
    const httpReqDuration = metrics['http_req_duration'];
    if (httpReqDuration) {
      if (httpReqDuration.values['p(95)'] < 100) {
        performanceScore = 5; // Excellent
      } else if (httpReqDuration.values['p(95)'] < 300) {
        performanceScore = 4; // Good
      } else if (httpReqDuration.values['p(95)'] < 500) {
        performanceScore = 3; // Acceptable
      }
    }
    
    // Adjust reliability score
    const errorRate = metrics['error_rate'];
    if (errorRate) {
      if (errorRate.values.rate < 0.001) {
        reliabilityScore = 5; // Excellent
      } else if (errorRate.values.rate < 0.005) {
        reliabilityScore = 4; // Good
      } else if (errorRate.values.rate < 0.01) {
        reliabilityScore = 3; // Acceptable
      }
    }
    
    // Adjust scalability score based on RPS
    const httpReqs = metrics['http_reqs'] ? metrics['http_reqs'].values.count : 0;
    const rps = httpReqs / (result.summary.testRunDurationMs / 1000);
    
    if (rps > 1000) {
      scalabilityScore = 5; // Excellent
    } else if (rps > 500) {
      scalabilityScore = 4; // Good
    } else if (rps > 100) {
      scalabilityScore = 3; // Acceptable
    }
  }
  
  // Calculate overall score
  const overallScore = Math.round((performanceScore + reliabilityScore + scalabilityScore) / 3);
  
  // Add scores to report
  report += `| Category | Score | Assessment |\n`;
  report += `| --- | --- | --- |\n`;
  report += `| Performance | ${performanceScore}/5 | ${getScoreDescription(performanceScore)} |\n`;
  report += `| Reliability | ${reliabilityScore}/5 | ${getScoreDescription(reliabilityScore)} |\n`;
  report += `| Scalability | ${scalabilityScore}/5 | ${getScoreDescription(scalabilityScore)} |\n`;
  report += `| **Overall** | **${overallScore}/5** | **${getScoreDescription(overallScore)}** |\n\n`;
  
  // Add marketplace readiness assessment
  report += `### Marketplace Readiness\n\n`;
  
  if (overallScore >= 4) {
    report += `✅ **NovaConnect UAC is ready for Google Cloud Marketplace submission.**\n\n`;
    report += `The system demonstrates excellent performance, reliability, and scalability under load. It meets or exceeds the requirements for Google Cloud Marketplace.\n\n`;
  } else if (overallScore >= 3) {
    report += `⚠️ **NovaConnect UAC is almost ready for Google Cloud Marketplace submission.**\n\n`;
    report += `The system demonstrates good performance, reliability, and scalability, but there are some areas that could be improved before submission.\n\n`;
  } else {
    report += `❌ **NovaConnect UAC needs improvement before Google Cloud Marketplace submission.**\n\n`;
    report += `The system has significant performance, reliability, or scalability issues that should be addressed before submission to Google Cloud Marketplace.\n\n`;
  }
  
  // Add next steps
  report += `### Next Steps\n\n`;
  
  if (hasPerformanceIssues) {
    report += `1. Address performance issues identified in this report\n`;
  }
  
  if (hasErrorIssues) {
    report += `${hasPerformanceIssues ? '2' : '1'}. Address error handling issues identified in this report\n`;
  }
  
  if (hasScalabilityIssues) {
    report += `${hasPerformanceIssues && hasErrorIssues ? '3' : (hasPerformanceIssues || hasErrorIssues ? '2' : '1')}. Address scalability issues identified in this report\n`;
  }
  
  report += `${hasPerformanceIssues || hasErrorIssues || hasScalabilityIssues ? (hasPerformanceIssues && hasErrorIssues && hasScalabilityIssues ? '4' : (hasPerformanceIssues && hasErrorIssues) || (hasPerformanceIssues && hasScalabilityIssues) || (hasErrorIssues && hasScalabilityIssues) ? '3' : '2') : '1'}. Complete Google Cloud Marketplace technical requirements\n`;
  report += `${hasPerformanceIssues || hasErrorIssues || hasScalabilityIssues ? (hasPerformanceIssues && hasErrorIssues && hasScalabilityIssues ? '5' : (hasPerformanceIssues && hasErrorIssues) || (hasPerformanceIssues && hasScalabilityIssues) || (hasErrorIssues && hasScalabilityIssues) ? '4' : '3') : '2'}. Prepare marketing materials and documentation\n`;
  report += `${hasPerformanceIssues || hasErrorIssues || hasScalabilityIssues ? (hasPerformanceIssues && hasErrorIssues && hasScalabilityIssues ? '6' : (hasPerformanceIssues && hasErrorIssues) || (hasPerformanceIssues && hasScalabilityIssues) || (hasErrorIssues && hasScalabilityIssues) ? '5' : '4') : '3'}. Submit NovaConnect UAC to Google Cloud Marketplace\n`;
  
  // Write report to file
  fs.writeFileSync(config.reportFile, report);
  
  console.log(`Report generated: ${config.reportFile}`);
}

/**
 * Get description for score
 * @param {number} score - Score (1-5)
 * @returns {string} - Score description
 */
function getScoreDescription(score) {
  switch (score) {
    case 5:
      return 'Excellent';
    case 4:
      return 'Good';
    case 3:
      return 'Acceptable';
    case 2:
      return 'Needs Improvement';
    case 1:
      return 'Poor';
    default:
      return 'Unknown';
  }
}

// Run the report generation
generateReport();
