/**
 * Authentication Service for NovaFuse
 * 
 * This service handles authentication for the NovaFuse platform, including:
 * - User login/logout
 * - Token management
 * - Session persistence
 * - Authentication state
 */

import axios from 'axios';
import jwtDecode from 'jwt-decode';

// Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  organizationId: string;
  permissions: string[];
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

export interface DecodedToken {
  sub: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  organizationId: string;
  permissions: string[];
  exp: number;
}

class AuthService {
  private baseUrl: string;
  private tokenKey: string = 'novafuse_token';
  private userKey: string = 'novafuse_user';
  private refreshTokenTimeout: NodeJS.Timeout | null = null;

  constructor(baseUrl = '/api/v1/auth') {
    this.baseUrl = baseUrl;
  }

  /**
   * Login user with email and password
   * @param credentials User credentials
   * @returns Promise with user data
   */
  async login(credentials: LoginCredentials): Promise<User> {
    try {
      const response = await axios.post(`${this.baseUrl}/login`, credentials);
      const { token, user } = response.data;
      
      // Store token and user data
      this.setToken(token);
      this.setUser(user);
      
      // Start token refresh timer
      this.startRefreshTokenTimer(token);
      
      return user;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(error.response.data.message || 'Login failed');
      }
      throw new Error('Login failed. Please try again.');
    }
  }

  /**
   * Logout user
   */
  logout(): void {
    // Remove token and user data
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);
    
    // Stop refresh timer
    this.stopRefreshTokenTimer();
    
    // Redirect to login page
    window.location.href = '/login';
  }

  /**
   * Check if user is authenticated
   * @returns Boolean indicating if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = this.getToken();
    if (!token) return false;
    
    // Check if token is expired
    try {
      const decoded = jwtDecode<DecodedToken>(token);
      const isExpired = decoded.exp * 1000 < Date.now();
      
      if (isExpired) {
        this.logout();
        return false;
      }
      
      return true;
    } catch (error) {
      this.logout();
      return false;
    }
  }

  /**
   * Get current user
   * @returns User object or null if not authenticated
   */
  getCurrentUser(): User | null {
    if (!this.isAuthenticated()) return null;
    
    const userJson = localStorage.getItem(this.userKey);
    if (!userJson) return null;
    
    try {
      return JSON.parse(userJson) as User;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get authentication token
   * @returns JWT token or null if not authenticated
   */
  getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  /**
   * Set authentication token
   * @param token JWT token
   */
  private setToken(token: string): void {
    localStorage.setItem(this.tokenKey, token);
  }

  /**
   * Set user data
   * @param user User object
   */
  private setUser(user: User): void {
    localStorage.setItem(this.userKey, JSON.stringify(user));
  }

  /**
   * Refresh authentication token
   * @returns Promise with new token
   */
  async refreshToken(): Promise<string> {
    try {
      const response = await axios.post(`${this.baseUrl}/refresh-token`, {}, {
        headers: {
          Authorization: `Bearer ${this.getToken()}`
        }
      });
      
      const { token } = response.data;
      this.setToken(token);
      
      // Restart refresh timer
      this.startRefreshTokenTimer(token);
      
      return token;
    } catch (error) {
      this.logout();
      throw new Error('Session expired. Please login again.');
    }
  }

  /**
   * Start token refresh timer
   * @param token JWT token
   */
  private startRefreshTokenTimer(token: string): void {
    // Clear any existing timer
    this.stopRefreshTokenTimer();
    
    // Decode token to get expiration
    try {
      const decoded = jwtDecode<DecodedToken>(token);
      const expires = new Date(decoded.exp * 1000);
      
      // Set refresh timer to 1 minute before expiration
      const timeout = expires.getTime() - Date.now() - (60 * 1000);
      
      if (timeout > 0) {
        this.refreshTokenTimeout = setTimeout(() => this.refreshToken(), timeout);
      } else {
        // Token is already expired or about to expire
        this.logout();
      }
    } catch (error) {
      console.error('Error starting refresh timer:', error);
    }
  }

  /**
   * Stop token refresh timer
   */
  private stopRefreshTokenTimer(): void {
    if (this.refreshTokenTimeout) {
      clearTimeout(this.refreshTokenTimeout);
      this.refreshTokenTimeout = null;
    }
  }

  /**
   * Check if user has required permissions
   * @param requiredPermissions Array of required permissions
   * @returns Boolean indicating if user has all required permissions
   */
  hasPermissions(requiredPermissions: string[]): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;
    
    // Check if user has all required permissions
    return requiredPermissions.every(permission => 
      user.permissions.includes(permission)
    );
  }

  /**
   * Get user's organization ID
   * @returns Organization ID or null if not authenticated
   */
  getOrganizationId(): string | null {
    const user = this.getCurrentUser();
    return user ? user.organizationId : null;
  }
}

// Create singleton instance
const authService = new AuthService();

export default authService;
