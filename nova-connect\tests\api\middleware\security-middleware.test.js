/**
 * NovaFuse Universal API Connector Security Middleware Tests
 * 
 * This module contains tests for the security middleware.
 */

const { createRateLimiter, createInputValidator, createSecurityHeaders } = require('../../../api/middleware/security-middleware');
const express = require('express');
const request = require('supertest');

describe('Security Middleware', () => {
  describe('Rate Limiter', () => {
    let app;
    
    beforeEach(() => {
      app = express();
      app.use(express.json());
      
      // Add rate limiter middleware
      app.use(createRateLimiter({
        windowMs: 1000, // 1 second
        maxRequests: 3 // 3 requests per second
      }));
      
      // Add test route
      app.get('/test', (req, res) => {
        res.status(200).json({ message: 'Success' });
      });
    });
    
    test('should allow requests within rate limit', async () => {
      // Make 3 requests (within limit)
      for (let i = 0; i < 3; i++) {
        const response = await request(app).get('/test');
        expect(response.status).toBe(200);
        expect(response.body).toEqual({ message: 'Success' });
        expect(response.headers['x-ratelimit-limit']).toBe('3');
        expect(parseInt(response.headers['x-ratelimit-remaining'])).toBeLessThanOrEqual(3 - i - 1);
      }
    });
    
    test('should block requests exceeding rate limit', async () => {
      // Make 3 requests (within limit)
      for (let i = 0; i < 3; i++) {
        const response = await request(app).get('/test');
        expect(response.status).toBe(200);
      }
      
      // Make 4th request (exceeding limit)
      const response = await request(app).get('/test');
      expect(response.status).toBe(429);
      expect(response.body).toEqual({
        error: 'Too many requests',
        message: 'Rate limit exceeded. Please try again later.'
      });
      expect(response.headers['x-ratelimit-limit']).toBe('3');
      expect(response.headers['x-ratelimit-remaining']).toBe('0');
      expect(response.headers['retry-after']).toBeDefined();
    });
    
    test('should reset rate limit after window', async () => {
      // Make 3 requests (within limit)
      for (let i = 0; i < 3; i++) {
        const response = await request(app).get('/test');
        expect(response.status).toBe(200);
      }
      
      // Make 4th request (exceeding limit)
      const response = await request(app).get('/test');
      expect(response.status).toBe(429);
      
      // Wait for rate limit window to reset
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Make another request (should be allowed)
      const newResponse = await request(app).get('/test');
      expect(newResponse.status).toBe(200);
      expect(newResponse.body).toEqual({ message: 'Success' });
    });
  });
  
  describe('Input Validator', () => {
    let app;
    
    beforeEach(() => {
      app = express();
      app.use(express.json());
      
      // Add input validator middleware
      app.use(createInputValidator());
      
      // Add test routes
      app.get('/test/:id', (req, res) => {
        res.status(200).json({ id: req.params.id });
      });
      
      app.get('/query', (req, res) => {
        res.status(200).json({ query: req.query });
      });
      
      app.post('/body', (req, res) => {
        res.status(200).json({ body: req.body });
      });
    });
    
    test('should allow valid path parameters', async () => {
      const response = await request(app).get('/test/123');
      expect(response.status).toBe(200);
      expect(response.body).toEqual({ id: '123' });
    });
    
    test('should block path parameters with XSS', async () => {
      const response = await request(app).get('/test/<script>alert(1)</script>');
      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid parameter');
    });
    
    test('should allow valid query parameters', async () => {
      const response = await request(app).get('/query?name=test&age=25');
      expect(response.status).toBe(200);
      expect(response.body).toEqual({ query: { name: 'test', age: '25' } });
    });
    
    test('should block query parameters with XSS', async () => {
      const response = await request(app).get('/query?name=<script>alert(1)</script>');
      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid parameter');
    });
    
    test('should allow valid body parameters', async () => {
      const response = await request(app)
        .post('/body')
        .send({ name: 'test', age: 25 });
      
      expect(response.status).toBe(200);
      expect(response.body).toEqual({ body: { name: 'test', age: 25 } });
    });
    
    test('should block body parameters with XSS', async () => {
      const response = await request(app)
        .post('/body')
        .send({ name: '<script>alert(1)</script>' });
      
      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid request body');
    });
    
    test('should block body parameters with command injection', async () => {
      const response = await request(app)
        .post('/body')
        .send({ command: 'rm -rf /' });
      
      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid request body');
    });
    
    test('should validate nested objects', async () => {
      const response = await request(app)
        .post('/body')
        .send({
          user: {
            name: 'test',
            profile: {
              bio: '<script>alert(1)</script>'
            }
          }
        });
      
      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid request body');
    });
  });
  
  describe('Security Headers', () => {
    let app;
    
    beforeEach(() => {
      app = express();
      
      // Add security headers middleware
      app.use(createSecurityHeaders());
      
      // Add test route
      app.get('/test', (req, res) => {
        res.status(200).json({ message: 'Success' });
      });
    });
    
    test('should add security headers to response', async () => {
      const response = await request(app).get('/test');
      
      expect(response.status).toBe(200);
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['strict-transport-security']).toBe('max-age=31536000; includeSubDomains');
      expect(response.headers['content-security-policy']).toBe("default-src 'self'");
      expect(response.headers['referrer-policy']).toBe('no-referrer');
    });
  });
});
