# Run tests for NovaConnect

# Start the Docker environment
Write-Host "Starting Docker environment..." -ForegroundColor Green
docker-compose up -d

# Wait for services to be ready
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Run the integration tests
Write-Host "Running integration tests..." -ForegroundColor Green
Set-Location -Path .\tests
npm test

# Run the load tests
Write-Host "Running load tests..." -ForegroundColor Green
Set-Location -Path ..\load-tests
npm test

# Run the end-to-end tests
Write-Host "Running end-to-end tests..." -ForegroundColor Green
Set-Location -Path ..\cypress
npm test

# Print test results summary
Write-Host "Test results summary:" -ForegroundColor Green
Write-Host "--------------------" -ForegroundColor Green

# Stop the Docker environment
Write-Host "Stopping Docker environment..." -ForegroundColor Yellow
Set-Location -Path ..
docker-compose down

Write-Host "Testing completed!" -ForegroundColor Green
