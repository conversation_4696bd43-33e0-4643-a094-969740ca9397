/**
 * NovaVision - Form Builder Component
 * 
 * This component builds UI schemas for forms.
 */

const { createLogger } = require('../../utils/logger');

const logger = createLogger('form-builder');

/**
 * Form Builder class
 */
class FormBuilder {
  constructor(options = {}) {
    this.options = {
      theme: options.theme || 'default',
      responsive: options.responsive !== false,
      accessibilityLevel: options.accessibilityLevel || 'AA',
      ...options
    };
    
    logger.debug('Form Builder initialized', {
      theme: this.options.theme,
      responsive: this.options.responsive
    });
  }
  
  /**
   * Build form schema
   * 
   * @param {Object} formConfig - Form configuration
   * @returns {Object} - Form schema
   */
  buildFormSchema(formConfig) {
    logger.debug('Building form schema', { formId: formConfig.id });
    
    // Validate form config
    this._validateFormConfig(formConfig);
    
    // Create base schema
    const schema = {
      type: 'form',
      id: formConfig.id,
      title: formConfig.title,
      description: formConfig.description,
      submitUrl: formConfig.submitUrl,
      method: formConfig.method || 'POST',
      sections: [],
      actions: [],
      metadata: {
        createdAt: new Date().toISOString(),
        version: '1.0',
        theme: this.options.theme,
        responsive: this.options.responsive
      }
    };
    
    // Add sections
    if (formConfig.sections && Array.isArray(formConfig.sections)) {
      schema.sections = formConfig.sections.map(section => this._buildSection(section));
    }
    
    // Add fields directly to the form if no sections
    if (formConfig.fields && Array.isArray(formConfig.fields)) {
      schema.sections.push({
        id: 'default-section',
        fields: formConfig.fields.map(field => this._buildField(field))
      });
    }
    
    // Add actions
    if (formConfig.actions && Array.isArray(formConfig.actions)) {
      schema.actions = formConfig.actions.map(action => this._buildAction(action));
    } else {
      // Add default submit action
      schema.actions.push({
        id: 'submit',
        type: 'submit',
        label: 'Submit',
        primary: true
      });
      
      // Add default cancel action
      schema.actions.push({
        id: 'cancel',
        type: 'button',
        label: 'Cancel',
        primary: false
      });
    }
    
    return schema;
  }
  
  /**
   * Validate form configuration
   * 
   * @param {Object} formConfig - Form configuration
   * @private
   */
  _validateFormConfig(formConfig) {
    if (!formConfig) {
      throw new Error('Form configuration is required');
    }
    
    if (!formConfig.id) {
      throw new Error('Form ID is required');
    }
    
    if (!formConfig.title) {
      throw new Error('Form title is required');
    }
    
    if (!formConfig.submitUrl) {
      throw new Error('Form submit URL is required');
    }
    
    if (!formConfig.sections && !formConfig.fields) {
      throw new Error('Form must have either sections or fields');
    }
  }
  
  /**
   * Build form section
   * 
   * @param {Object} sectionConfig - Section configuration
   * @returns {Object} - Section schema
   * @private
   */
  _buildSection(sectionConfig) {
    logger.debug('Building form section', { sectionId: sectionConfig.id });
    
    // Validate section config
    if (!sectionConfig.id) {
      throw new Error('Section ID is required');
    }
    
    // Create section schema
    const section = {
      id: sectionConfig.id,
      title: sectionConfig.title,
      description: sectionConfig.description,
      collapsible: sectionConfig.collapsible || false,
      collapsed: sectionConfig.collapsed || false,
      fields: []
    };
    
    // Add fields
    if (sectionConfig.fields && Array.isArray(sectionConfig.fields)) {
      section.fields = sectionConfig.fields.map(field => this._buildField(field));
    }
    
    return section;
  }
  
  /**
   * Build form field
   * 
   * @param {Object} fieldConfig - Field configuration
   * @returns {Object} - Field schema
   * @private
   */
  _buildField(fieldConfig) {
    logger.debug('Building form field', { 
      fieldId: fieldConfig.id,
      fieldType: fieldConfig.type
    });
    
    // Validate field config
    if (!fieldConfig.id) {
      throw new Error('Field ID is required');
    }
    
    if (!fieldConfig.type) {
      throw new Error('Field type is required');
    }
    
    // Create base field schema
    const field = {
      id: fieldConfig.id,
      type: fieldConfig.type,
      label: fieldConfig.label,
      description: fieldConfig.description,
      required: fieldConfig.required || false,
      disabled: fieldConfig.disabled || false,
      hidden: fieldConfig.hidden || false,
      defaultValue: fieldConfig.defaultValue,
      placeholder: fieldConfig.placeholder,
      helpText: fieldConfig.helpText,
      validation: fieldConfig.validation || {}
    };
    
    // Add type-specific properties
    switch (fieldConfig.type) {
      case 'text':
      case 'password':
      case 'email':
      case 'url':
      case 'tel':
        field.minLength = fieldConfig.minLength;
        field.maxLength = fieldConfig.maxLength;
        field.pattern = fieldConfig.pattern;
        break;
        
      case 'number':
      case 'range':
        field.min = fieldConfig.min;
        field.max = fieldConfig.max;
        field.step = fieldConfig.step;
        break;
        
      case 'select':
      case 'radio':
      case 'checkbox':
        field.options = fieldConfig.options || [];
        field.multiple = fieldConfig.multiple || false;
        break;
        
      case 'textarea':
        field.rows = fieldConfig.rows || 3;
        field.cols = fieldConfig.cols;
        field.minLength = fieldConfig.minLength;
        field.maxLength = fieldConfig.maxLength;
        break;
        
      case 'date':
      case 'time':
      case 'datetime-local':
        field.min = fieldConfig.min;
        field.max = fieldConfig.max;
        break;
        
      case 'file':
        field.accept = fieldConfig.accept;
        field.multiple = fieldConfig.multiple || false;
        field.maxSize = fieldConfig.maxSize;
        break;
        
      case 'group':
        field.fields = fieldConfig.fields ? fieldConfig.fields.map(f => this._buildField(f)) : [];
        break;
    }
    
    // Add conditional display logic
    if (fieldConfig.displayWhen) {
      field.displayWhen = fieldConfig.displayWhen;
    }
    
    return field;
  }
  
  /**
   * Build form action
   * 
   * @param {Object} actionConfig - Action configuration
   * @returns {Object} - Action schema
   * @private
   */
  _buildAction(actionConfig) {
    logger.debug('Building form action', { 
      actionId: actionConfig.id,
      actionType: actionConfig.type
    });
    
    // Validate action config
    if (!actionConfig.id) {
      throw new Error('Action ID is required');
    }
    
    if (!actionConfig.type) {
      throw new Error('Action type is required');
    }
    
    // Create action schema
    const action = {
      id: actionConfig.id,
      type: actionConfig.type,
      label: actionConfig.label,
      primary: actionConfig.primary || false,
      disabled: actionConfig.disabled || false,
      hidden: actionConfig.hidden || false
    };
    
    // Add type-specific properties
    switch (actionConfig.type) {
      case 'submit':
        action.submitUrl = actionConfig.submitUrl;
        action.method = actionConfig.method || 'POST';
        break;
        
      case 'button':
      case 'link':
        action.url = actionConfig.url;
        action.target = actionConfig.target;
        action.onClick = actionConfig.onClick;
        break;
    }
    
    return action;
  }
}

module.exports = FormBuilder;
