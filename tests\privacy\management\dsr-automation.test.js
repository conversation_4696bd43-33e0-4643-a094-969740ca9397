const request = require('supertest');
const app = require('./mockApp');

describe('Privacy Management API - DSR Automation', () => {
  describe('GET /privacy/management/data-systems', () => {
    it('should return a list of data systems', async () => {
      const response = await request(app)
        .get('/privacy/management/data-systems');

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0].id).toBeDefined();
      expect(response.body.data[0].name).toBeDefined();
      expect(response.body.data[0].dataCategories).toBeDefined();
    });

    it('should filter data systems by request type', async () => {
      const response = await request(app)
        .get('/privacy/management/data-systems')
        .query({ requestType: 'access' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);

      // All returned systems should support the 'access' request type
      response.body.data.forEach(system => {
        expect(system.supportedRequestTypes).toContain('access');
      });
    });
  });

  describe('GET /privacy/management/data-systems/:id', () => {
    it('should return a specific data system', async () => {
      // First get all systems to find a valid ID
      const allSystemsResponse = await request(app)
        .get('/privacy/management/data-systems');

      const systemId = allSystemsResponse.body.data[0].id;

      const response = await request(app)
        .get(`/privacy/management/data-systems/${systemId}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(systemId);
      expect(response.body.data.name).toBeDefined();
      expect(response.body.data.dataCategories).toBeDefined();
    });

    it('should return 404 for non-existent data system', async () => {
      const response = await request(app)
        .get('/privacy/management/data-systems/non-existent');

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Not Found');
    });
  });

  describe('POST /privacy/management/subject-requests/:id/process', () => {
    it('should process a data subject request', async () => {
      // First create a data subject request
      const newRequest = {
        requestType: 'access',
        dataSubjectName: 'John Doe',
        dataSubjectEmail: '<EMAIL>',
        requestDetails: 'I would like to access all my personal data',
        status: 'pending'
      };

      const createResponse = await request(app)
        .post('/privacy/management/subject-requests')
        .send(newRequest);

      expect(createResponse.status).toBe(201);

      const requestId = createResponse.body.data.id;

      const response = await request(app)
        .post(`/privacy/management/subject-requests/${requestId}/process`);

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.automationStatus).toBeDefined();
      expect(response.body.data.automationStatus.systemStatuses).toBeDefined();
      expect(Array.isArray(response.body.data.automationStatus.systemStatuses)).toBe(true);
      expect(response.body.message).toBeDefined();
    });

    it('should return 400 for non-existent request', async () => {
      const response = await request(app)
        .post('/privacy/management/subject-requests/non-existent/process');

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Bad Request');
    });
  });

  describe('GET /privacy/management/subject-requests/:id/export', () => {
    it('should generate a data export', async () => {
      // First create a data subject request
      const newRequest = {
        requestType: 'access',
        dataSubjectName: 'Jane Smith',
        dataSubjectEmail: '<EMAIL>',
        requestDetails: 'I would like to access all my personal data',
        status: 'pending'
      };

      const createResponse = await request(app)
        .post('/privacy/management/subject-requests')
        .send(newRequest);

      expect(createResponse.status).toBe(201);

      const requestId = createResponse.body.data.id;

      const response = await request(app)
        .get(`/privacy/management/subject-requests/${requestId}/export`);

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.requestId).toBe(requestId);
      expect(response.body.data.dataSubjectName).toBe(newRequest.dataSubjectName);
      expect(response.body.data.dataSubjectEmail).toBe(newRequest.dataSubjectEmail);
      expect(response.body.data.systems).toBeDefined();
      expect(Array.isArray(response.body.data.systems)).toBe(true);
      expect(response.body.message).toBeDefined();
    });

    it('should return 400 for non-access request', async () => {
      // First create a data subject request that is not an access request
      const newRequest = {
        requestType: 'erasure',
        dataSubjectName: 'Bob Johnson',
        dataSubjectEmail: '<EMAIL>',
        requestDetails: 'I would like to delete all my personal data',
        status: 'pending'
      };

      const createResponse = await request(app)
        .post('/privacy/management/subject-requests')
        .send(newRequest);

      expect(createResponse.status).toBe(201);

      const requestId = createResponse.body.data.id;

      const response = await request(app)
        .get(`/privacy/management/subject-requests/${requestId}/export`);

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Bad Request');
    });
  });

  describe('GET /privacy/management/subject-requests/:id/affected-systems', () => {
    it('should determine affected systems for a request', async () => {
      // First create a data subject request
      const newRequest = {
        requestType: 'access',
        dataSubjectName: 'Alice Brown',
        dataSubjectEmail: '<EMAIL>',
        requestDetails: 'I would like to access all my personal data',
        status: 'pending'
      };

      const createResponse = await request(app)
        .post('/privacy/management/subject-requests')
        .send(newRequest);

      expect(createResponse.status).toBe(201);

      const requestId = createResponse.body.data.id;

      const response = await request(app)
        .get(`/privacy/management/subject-requests/${requestId}/affected-systems`);

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);

      // All returned systems should support the 'access' request type
      response.body.data.forEach(system => {
        expect(system.supportedRequestTypes).toContain('access');
      });
    });

    it('should return 404 for non-existent request', async () => {
      const response = await request(app)
        .get('/privacy/management/subject-requests/non-existent/affected-systems');

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Not Found');
    });
  });
});
