/**
 * NovaActuary™ Health Check
 * Container health validation for production deployment
 */

const { performance } = require('perf_hooks');

async function performHealthCheck() {
  const startTime = performance.now();
  const healthStatus = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0-REVOLUTIONARY',
    checks: {}
  };

  try {
    // Check 1: NovaActuary™ Core Module Loading
    console.log('🔍 Health Check: Loading NovaActuary™ Core...');
    const { NovaActuary } = require('./index');
    const novaActuary = new NovaActuary();
    healthStatus.checks.coreModule = {
      status: 'pass',
      message: `${novaActuary.name} v${novaActuary.version} loaded successfully`
    };

    // Check 2: Component Integration
    console.log('🔍 Health Check: Validating Component Integration...');
    const componentCheck = {
      novaConnect: novaActuary.constructor.name === 'NovaActuary',
      csmPRS: novaActuary.csmPRSValidator !== undefined,
      comphyology: novaActuary.comphyologyCore !== undefined,
      trinityOracle: novaActuary.trinityOracle !== undefined
    };
    
    const allComponentsHealthy = Object.values(componentCheck).every(check => check === true);
    healthStatus.checks.componentIntegration = {
      status: allComponentsHealthy ? 'pass' : 'fail',
      details: componentCheck,
      message: allComponentsHealthy ? 'All components integrated' : 'Component integration issues detected'
    };

    // Check 3: Mathematical Framework Validation
    console.log('🔍 Health Check: Validating Mathematical Framework...');
    const mathCheck = {
      psiZeroThreshold: novaActuary.actuarialConfig.psiZeroThreshold === 0.1,
      piCoherenceSequence: novaActuary.actuarialConfig.piCoherenceSequence.length === 10,
      goldenRatio: Math.abs(novaActuary.actuarialConfig.goldenRatio - 1.618033988749) < 0.000001,
      consciousnessThreshold: novaActuary.actuarialConfig.consciousnessThreshold === 0.618
    };
    
    const mathFrameworkHealthy = Object.values(mathCheck).every(check => check === true);
    healthStatus.checks.mathematicalFramework = {
      status: mathFrameworkHealthy ? 'pass' : 'fail',
      details: mathCheck,
      message: mathFrameworkHealthy ? 'Mathematical framework validated' : 'Mathematical framework issues detected'
    };

    // Check 4: Performance Test
    console.log('🔍 Health Check: Performance Validation...');
    const testClient = {
      name: 'Health Check Client',
      aiSystems: { name: 'Test AI', type: 'health_check', domain: 'validation' },
      financialData: { revenue: 1000000, assets: 5000000, liabilities: 1500000, riskScore: 0.3 },
      testData: { privacyCompliance: 0.8, securityScore: 0.85, fairnessMetrics: 0.75 }
    };

    const perfStartTime = performance.now();
    const assessment = await novaActuary.performActuarialAssessment(testClient);
    const perfEndTime = performance.now();
    const processingTime = perfEndTime - perfStartTime;

    const performanceHealthy = processingTime < 1000 && assessment.novaactuary_validated === true;
    healthStatus.checks.performance = {
      status: performanceHealthy ? 'pass' : 'fail',
      processingTime: `${processingTime.toFixed(2)}ms`,
      validated: assessment.novaactuary_validated,
      message: performanceHealthy ? 'Performance within acceptable limits' : 'Performance issues detected'
    };

    // Check 5: Memory and Resource Usage
    console.log('🔍 Health Check: Resource Usage Validation...');
    const memUsage = process.memoryUsage();
    const memoryHealthy = memUsage.heapUsed < 500 * 1024 * 1024; // 500MB limit
    
    healthStatus.checks.resources = {
      status: memoryHealthy ? 'pass' : 'warn',
      memory: {
        heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
        external: `${Math.round(memUsage.external / 1024 / 1024)}MB`
      },
      message: memoryHealthy ? 'Resource usage normal' : 'High memory usage detected'
    };

    // Overall Health Assessment
    const allChecksPass = Object.values(healthStatus.checks).every(check => check.status === 'pass');
    const hasWarnings = Object.values(healthStatus.checks).some(check => check.status === 'warn');
    
    if (allChecksPass) {
      healthStatus.status = 'healthy';
      healthStatus.message = 'NovaActuary™ is fully operational';
    } else if (hasWarnings) {
      healthStatus.status = 'degraded';
      healthStatus.message = 'NovaActuary™ operational with warnings';
    } else {
      healthStatus.status = 'unhealthy';
      healthStatus.message = 'NovaActuary™ has critical issues';
    }

    const endTime = performance.now();
    healthStatus.healthCheckDuration = `${(endTime - startTime).toFixed(2)}ms`;

    // Output results
    console.log('\n🏥 NOVAACTUARY™ HEALTH CHECK RESULTS');
    console.log('=' .repeat(50));
    console.log(`📊 Overall Status: ${healthStatus.status.toUpperCase()}`);
    console.log(`⏱️  Health Check Duration: ${healthStatus.healthCheckDuration}`);
    console.log(`🕐 Timestamp: ${healthStatus.timestamp}`);
    
    console.log('\n📋 Component Checks:');
    Object.entries(healthStatus.checks).forEach(([checkName, checkResult]) => {
      const statusIcon = checkResult.status === 'pass' ? '✅' : checkResult.status === 'warn' ? '⚠️' : '❌';
      console.log(`   ${statusIcon} ${checkName}: ${checkResult.status.toUpperCase()} - ${checkResult.message}`);
    });

    if (healthStatus.status === 'healthy') {
      console.log('\n🎉 NovaActuary™ is ready for production deployment!');
      process.exit(0);
    } else if (healthStatus.status === 'degraded') {
      console.log('\n⚠️  NovaActuary™ operational but requires attention');
      process.exit(0);
    } else {
      console.log('\n💥 NovaActuary™ has critical issues requiring immediate attention');
      process.exit(1);
    }

  } catch (error) {
    healthStatus.status = 'unhealthy';
    healthStatus.error = error.message;
    healthStatus.checks.criticalError = {
      status: 'fail',
      message: `Critical error during health check: ${error.message}`
    };

    console.error('\n💥 NOVAACTUARY™ HEALTH CHECK FAILED');
    console.error('=' .repeat(50));
    console.error(`❌ Error: ${error.message}`);
    console.error(`📍 Stack: ${error.stack}`);
    
    process.exit(1);
  }
}

// Run health check
performHealthCheck();
