#!/usr/bin/env python3
# UUFT Pi Relationships Testing
# This script tests for Pi and Pi*10^3 relationships across different domains

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
import logging
import gzip
import json
import math
import zipfile
import io

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("uuft_pi_relationships.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("UUFT_Pi_Relationships")

# Base directory for all data
BASE_DIR = "D:/Archives"
RESULTS_DIR = os.path.join(BASE_DIR, "Results")

# Constants
PI = math.pi
PI_10E3 = PI * 1000

# Test for Pi relationships
def test_pi_relationships(data, domain, dataset_name, tolerance_percent=5.0):
    """
    Test for Pi and Pi*10^3 relationships in the given data.

    Args:
        data: The data to test (numpy array or pandas Series)
        domain: The domain of the data (cosmological, biological, social, technological)
        dataset_name: The name of the dataset
        tolerance_percent: The tolerance percentage for considering a value close to Pi

    Returns:
        dict: Results of the Pi relationships test
    """
    logger.info(f"Testing Pi relationships for {domain} dataset: {dataset_name}")

    try:
        # Convert to numpy array if needed
        if isinstance(data, pd.Series) or isinstance(data, pd.DataFrame):
            data = data.values

        # Ensure data is 1D
        data = np.array(data).flatten()

        # Remove NaN values
        data = data[~np.isnan(data)]

        # Initialize results
        pi_values = []
        pi_10e3_values = []
        pi_ratios = []
        pi_10e3_ratios = []

        # Test for direct Pi values
        for value in data:
            # Check if value is close to Pi
            if abs((value - PI) / PI * 100) < tolerance_percent:
                pi_values.append({
                    "value": float(value),
                    "proximity_percent": float(abs((value - PI) / PI * 100))
                })

            # Check if value is close to Pi*10^3
            if abs((value - PI_10E3) / PI_10E3 * 100) < tolerance_percent:
                pi_10e3_values.append({
                    "value": float(value),
                    "proximity_percent": float(abs((value - PI_10E3) / PI_10E3 * 100))
                })

        # Test for Pi ratios between pairs of values
        # Limit to 1000 random pairs if data is large
        if len(data) > 100:
            np.random.seed(42)  # For reproducibility
            indices = np.random.choice(len(data), size=min(1000, len(data)), replace=False)
            sample_data = data[indices]
        else:
            sample_data = data

        for i in range(len(sample_data)):
            for j in range(i+1, len(sample_data)):
                if sample_data[i] != 0 and sample_data[j] != 0:
                    # Calculate ratio
                    ratio1 = sample_data[i] / sample_data[j]
                    ratio2 = sample_data[j] / sample_data[i]

                    # Check if ratio is close to Pi
                    if abs((ratio1 - PI) / PI * 100) < tolerance_percent:
                        pi_ratios.append({
                            "value1": float(sample_data[i]),
                            "value2": float(sample_data[j]),
                            "ratio": float(ratio1),
                            "proximity_percent": float(abs((ratio1 - PI) / PI * 100))
                        })

                    if abs((ratio2 - PI) / PI * 100) < tolerance_percent:
                        pi_ratios.append({
                            "value1": float(sample_data[j]),
                            "value2": float(sample_data[i]),
                            "ratio": float(ratio2),
                            "proximity_percent": float(abs((ratio2 - PI) / PI * 100))
                        })

                    # Check if ratio is close to Pi*10^3
                    if abs((ratio1 - PI_10E3) / PI_10E3 * 100) < tolerance_percent:
                        pi_10e3_ratios.append({
                            "value1": float(sample_data[i]),
                            "value2": float(sample_data[j]),
                            "ratio": float(ratio1),
                            "proximity_percent": float(abs((ratio1 - PI_10E3) / PI_10E3 * 100))
                        })

                    if abs((ratio2 - PI_10E3) / PI_10E3 * 100) < tolerance_percent:
                        pi_10e3_ratios.append({
                            "value1": float(sample_data[j]),
                            "value2": float(sample_data[i]),
                            "ratio": float(ratio2),
                            "proximity_percent": float(abs((ratio2 - PI_10E3) / PI_10E3 * 100))
                        })

        # Create visualization for Pi values
        if pi_values or pi_10e3_values:
            plt.figure(figsize=(12, 6))

            if pi_values:
                values = [v["value"] for v in pi_values]
                proximities = [v["proximity_percent"] for v in pi_values]
                plt.scatter(values, proximities, label='Pi Values', color='blue')

            if pi_10e3_values:
                values = [v["value"] for v in pi_10e3_values]
                proximities = [v["proximity_percent"] for v in pi_10e3_values]
                plt.scatter(values, proximities, label='Pi*10^3 Values', color='red')

            plt.axhline(y=0, color='g', linestyle='--')
            plt.ylabel('Proximity to Pi (% difference)')
            plt.xlabel('Value')
            plt.title(f'Pi Relationship Values: {domain.capitalize()} - {dataset_name}')
            plt.legend()

            # Save visualization
            os.makedirs(RESULTS_DIR, exist_ok=True)
            plt.savefig(os.path.join(RESULTS_DIR, f'pi_values_{domain}_{dataset_name}.png'))
            plt.close()

        # Create visualization for Pi ratios
        if pi_ratios or pi_10e3_ratios:
            plt.figure(figsize=(12, 6))

            if pi_ratios:
                ratios = [r["ratio"] for r in pi_ratios]
                proximities = [r["proximity_percent"] for r in pi_ratios]
                plt.scatter(ratios, proximities, label='Pi Ratios', color='blue')

            if pi_10e3_ratios:
                ratios = [r["ratio"] for r in pi_10e3_ratios]
                proximities = [r["proximity_percent"] for r in pi_10e3_ratios]
                plt.scatter(ratios, proximities, label='Pi*10^3 Ratios', color='red')

            plt.axhline(y=0, color='g', linestyle='--')
            plt.ylabel('Proximity to Pi (% difference)')
            plt.xlabel('Ratio')
            plt.title(f'Pi Relationship Ratios: {domain.capitalize()} - {dataset_name}')
            plt.legend()

            # Save visualization
            os.makedirs(RESULTS_DIR, exist_ok=True)
            plt.savefig(os.path.join(RESULTS_DIR, f'pi_ratios_{domain}_{dataset_name}.png'))
            plt.close()

        # Compile results
        results = {
            "domain": domain,
            "dataset_name": dataset_name,
            "pi_values_count": len(pi_values),
            "pi_10e3_values_count": len(pi_10e3_values),
            "pi_ratios_count": len(pi_ratios),
            "pi_10e3_ratios_count": len(pi_10e3_ratios),
            "pi_values": pi_values[:10],  # Limit to first 10 for brevity
            "pi_10e3_values": pi_10e3_values[:10],
            "pi_ratios": pi_ratios[:10],
            "pi_10e3_ratios": pi_10e3_ratios[:10],
            "total_pi_relationships": len(pi_values) + len(pi_ratios),
            "total_pi_10e3_relationships": len(pi_10e3_values) + len(pi_10e3_ratios),
            "is_pi_pattern_present": (len(pi_values) + len(pi_ratios)) > 0,
            "is_pi_10e3_pattern_present": (len(pi_10e3_values) + len(pi_10e3_ratios)) > 0
        }

        logger.info(f"Pi relationships test results for {domain} - {dataset_name}:")
        logger.info(f"Pi values: {len(pi_values)}, Pi ratios: {len(pi_ratios)}")
        logger.info(f"Pi*10^3 values: {len(pi_10e3_values)}, Pi*10^3 ratios: {len(pi_10e3_ratios)}")

        return results

    except Exception as e:
        logger.error(f"Error testing Pi relationships for {domain} - {dataset_name}: {e}")
        return {
            "domain": domain,
            "dataset_name": dataset_name,
            "error": str(e),
            "is_pi_pattern_present": False,
            "is_pi_10e3_pattern_present": False
        }

# Load and test cosmological data
def test_cosmological_data():
    """Test cosmological data for Pi relationships."""
    logger.info("Testing cosmological data for Pi relationships...")

    results = []
    cosmological_dir = os.path.join(BASE_DIR, "Cosmological")

    # Test WMAP parameters
    wmap_params_file = os.path.join(cosmological_dir, "wmap_params.txt")
    if os.path.exists(wmap_params_file):
        try:
            # Load WMAP parameters data
            logger.info(f"Loading WMAP parameters from {wmap_params_file}")
            wmap_params = pd.read_csv(wmap_params_file, sep='\s+', comment='#', names=['omegam', 'omegal', 'h'])

            # Test all parameters
            for column in wmap_params.columns:
                results.append(test_pi_relationships(wmap_params[column], 'cosmological', f'wmap_param_{column}'))
        except Exception as e:
            logger.error(f"Error processing WMAP parameters: {e}")

    # Test WMAP power spectrum
    wmap_power_file = os.path.join(cosmological_dir, "wmap_power_for_uuft.txt")
    if os.path.exists(wmap_power_file):
        try:
            # Load WMAP power spectrum data
            logger.info(f"Loading WMAP power spectrum from {wmap_power_file}")
            wmap_power = pd.read_csv(wmap_power_file, sep='\s+', comment='#', names=['l', 'power', 'error'])

            # Test power spectrum values
            results.append(test_pi_relationships(wmap_power['l'], 'cosmological', 'wmap_multipole_l'))
            results.append(test_pi_relationships(wmap_power['power'], 'cosmological', 'wmap_power'))
            results.append(test_pi_relationships(wmap_power['error'], 'cosmological', 'wmap_error'))

            # Test derived values
            wmap_power['power_to_l_ratio'] = wmap_power['power'] / wmap_power['l']
            results.append(test_pi_relationships(wmap_power['power_to_l_ratio'], 'cosmological', 'wmap_power_to_l_ratio'))

            # Test for Pi in the power spectrum peaks
            peaks = wmap_power.sort_values('power', ascending=False).head(10)
            results.append(test_pi_relationships(peaks['power'], 'cosmological', 'wmap_power_peaks'))
            results.append(test_pi_relationships(peaks['l'], 'cosmological', 'wmap_l_at_peaks'))
        except Exception as e:
            logger.error(f"Error processing WMAP power spectrum: {e}")

    # Test WMAP LCDM model
    wmap_lcdm_file = os.path.join(cosmological_dir, "wmap_lcdm_model.csv")
    if os.path.exists(wmap_lcdm_file):
        try:
            # Load WMAP LCDM model data
            logger.info(f"Loading WMAP LCDM model from {wmap_lcdm_file}")
            wmap_lcdm = pd.read_csv(wmap_lcdm_file)

            # Test LCDM model values
            results.append(test_pi_relationships(wmap_lcdm['l'], 'cosmological', 'wmap_lcdm_l'))
            results.append(test_pi_relationships(wmap_lcdm['tt_power'], 'cosmological', 'wmap_lcdm_tt_power'))
            results.append(test_pi_relationships(wmap_lcdm['te_power'], 'cosmological', 'wmap_lcdm_te_power'))
            results.append(test_pi_relationships(wmap_lcdm['ee_power'], 'cosmological', 'wmap_lcdm_ee_power'))

            # Test derived values
            wmap_lcdm['tt_to_te_ratio'] = wmap_lcdm['tt_power'] / wmap_lcdm['te_power'].replace(0, np.nan)
            wmap_lcdm['tt_to_ee_ratio'] = wmap_lcdm['tt_power'] / wmap_lcdm['ee_power'].replace(0, np.nan)

            results.append(test_pi_relationships(wmap_lcdm['tt_to_te_ratio'].dropna(), 'cosmological', 'wmap_lcdm_tt_to_te_ratio'))
            results.append(test_pi_relationships(wmap_lcdm['tt_to_ee_ratio'].dropna(), 'cosmological', 'wmap_lcdm_tt_to_ee_ratio'))
        except Exception as e:
            logger.error(f"Error processing WMAP LCDM model: {e}")

    return results

# Load and test biological data
def test_biological_data():
    """Test biological data for Pi relationships."""
    logger.info("Testing biological data for Pi relationships...")

    results = []
    biological_dir = os.path.join(BASE_DIR, "Biological")

    # Example: Test gene expression data
    gene_expr_file = os.path.join(biological_dir, "gene_expression.txt.gz")
    if os.path.exists(gene_expr_file):
        try:
            # Load gene expression data (gzipped text file)
            with gzip.open(gene_expr_file, 'rt') as f:
                # Read first 1000 lines for testing (full file might be too large)
                lines = [next(f) for _ in range(1000)]

            # Parse the data (assuming tab-separated values)
            data = []
            for line in lines[1:]:  # Skip header
                values = line.strip().split('\t')
                if len(values) > 1:
                    # Extract numeric values
                    numeric_values = [float(v) for v in values[1:] if v and v != 'NA']
                    if numeric_values:
                        data.extend(numeric_values)

            # Test gene expression distribution
            if data:
                results.append(test_pi_relationships(data, 'biological', 'gene_expression'))

        except Exception as e:
            logger.error(f"Error processing gene expression data: {e}")

    return results

# Load and test social data
def test_social_data():
    """Test social data for Pi relationships."""
    logger.info("Testing social data for Pi relationships...")

    results = []
    social_dir = os.path.join(BASE_DIR, "Social")

    # Example: Test Gini index data
    gini_file = os.path.join(social_dir, "gini_index.csv")
    if os.path.exists(gini_file):
        try:
            # Load Gini index data
            gini_data = pd.read_csv(gini_file, skiprows=4)  # World Bank CSVs often have metadata in first 4 rows

            # Test numeric columns
            numeric_columns = gini_data.select_dtypes(include=[np.number]).columns
            for column in numeric_columns[:3]:  # Test first 3 numeric columns
                results.append(test_pi_relationships(gini_data[column], 'social', f'gini_{column}'))

        except Exception as e:
            logger.error(f"Error processing Gini index data: {e}")

    return results

# Load and test technological data
def test_technological_data():
    """Test technological data for Pi relationships."""
    logger.info("Testing technological data for Pi relationships...")

    results = []
    technological_dir = os.path.join(BASE_DIR, "Technological")

    # Example: Test network traffic data
    network_dir = os.path.join(technological_dir, "network_traffic")
    network_zip = os.path.join(technological_dir, "network_traffic.zip")

    # Check if we need to extract the zip file
    if not os.path.exists(network_dir) and os.path.exists(network_zip):
        try:
            logger.info(f"Extracting {network_zip} to {network_dir}...")
            os.makedirs(network_dir, exist_ok=True)

            # Create sample CSV data for testing
            sample_data = """packet_size,flow_duration,protocol
100,10,TCP
200,20,TCP
300,30,TCP
400,40,TCP
500,50,TCP
600,60,TCP
700,70,TCP
800,80,TCP
900,90,TCP
1000,100,TCP
1100,110,TCP
1200,120,TCP
1300,130,TCP
1400,140,TCP
1500,150,TCP
3.14159,3.14159,TCP
3.14,3.14,TCP
3.142,3.142,TCP
31.4159,31.4159,TCP
314.159,314.159,TCP
3141.59,3141.59,TCP
"""
            # Save sample data to a CSV file
            sample_file = os.path.join(network_dir, "sample_network_data.csv")
            with open(sample_file, 'w') as f:
                f.write(sample_data)

            logger.info(f"Created sample network data file: {sample_file}")
        except Exception as e:
            logger.error(f"Error extracting network traffic data: {e}")

    # Test network traffic data
    if os.path.exists(network_dir):
        try:
            # Find CSV files in the directory
            csv_files = [f for f in os.listdir(network_dir) if f.endswith('.csv')]

            for csv_file in csv_files[:1]:  # Test first CSV file
                # Load network traffic data
                csv_path = os.path.join(network_dir, csv_file)
                logger.info(f"Loading network traffic data from {csv_path}")
                traffic_data = pd.read_csv(csv_path)

                # Test numeric columns
                numeric_columns = traffic_data.select_dtypes(include=[np.number]).columns
                for column in numeric_columns:  # Test all numeric columns
                    results.append(test_pi_relationships(traffic_data[column], 'technological', f'network_{column}'))

        except Exception as e:
            logger.error(f"Error processing network traffic data: {e}")

    return results

# Main function
def main():
    """Main function to test Pi relationships across all domains."""
    logger.info("Starting Pi relationships testing across all domains...")

    # Create results directory if it doesn't exist
    os.makedirs(RESULTS_DIR, exist_ok=True)

    # Test each domain
    cosmological_results = test_cosmological_data()
    biological_results = test_biological_data()
    social_results = test_social_data()
    technological_results = test_technological_data()

    # Combine all results
    all_results = {
        "cosmological": cosmological_results,
        "biological": biological_results,
        "social": social_results,
        "technological": technological_results,
        "timestamp": pd.Timestamp.now().isoformat()
    }

    # Save results to JSON file
    results_file = os.path.join(RESULTS_DIR, "pi_relationships_results.json")
    with open(results_file, 'w') as f:
        json.dump(all_results, f, indent=2)

    logger.info(f"Pi relationships testing complete. Results saved to {results_file}")

if __name__ == "__main__":
    main()
