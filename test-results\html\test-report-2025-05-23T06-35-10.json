{"numFailedTestSuites": 1, "numFailedTests": 0, "numPassedTestSuites": 0, "numPassedTests": 0, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 1, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 0, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1747982107018, "success": false, "testResults": [{"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    ReferenceError: D:\\novafuse-api-superstore\\tests\\integration\\novaproof\\evidence-verification.test.js: The module factory of `jest.mock()` is not allowed to reference any out-of-scope variables.\n    Invalid variable access: generateEvidenceItem\n    Allowed objects: AbortController, AbortSignal, AggregateError, Array, ArrayBuffer, Atomics, BigInt, BigInt64Array, BigUint64Array, Blob, Boolean, BroadcastChannel, Buffer, ByteLengthQueuingStrategy, CompressionStream, CountQueuingStrategy, Crypto, CryptoKey, CustomEvent, DOMException, DataView, Date, DecompressionStream, Error, EvalError, Event, EventTarget, File, FinalizationRegistry, Float32Array, Float64Array, FormData, Function, Generator, GeneratorFunction, Headers, Infinity, Int16Array, Int32Array, Int8Array, InternalError, Intl, Iterator, JSON, Map, Math, MessageChannel, MessageEvent, MessagePort, NaN, Navigator, Number, Object, Performance, PerformanceEntry, PerformanceMark, PerformanceMeasure, PerformanceObserver, PerformanceObserverEntryList, PerformanceResourceTiming, Promise, Proxy, RangeError, ReadableByteStreamController, ReadableStream, ReadableStreamBYOBReader, ReadableStreamBYOBRequest, ReadableStreamDefaultController, ReadableStreamDefaultReader, ReferenceError, Reflect, RegExp, Request, Response, Set, SharedArrayBuffer, String, SubtleCrypto, Symbol, SyntaxError, TextDecoder, TextDecoderStream, TextEncoder, TextEncoderStream, TransformStream, TransformStreamDefaultController, TypeError, URIError, URL, URLSearchParams, Uint16Array, Uint32Array, Uint8Array, Uint8ClampedArray, WeakMap, WeakRef, WeakSet, WebAssembly, WebSocket, WritableStream, WritableStreamDefaultController, WritableStreamDefaultWriter, __dirname, __filename, arguments, atob, btoa, clearImmediate, clearInterval, clearTimeout, console, crypto, decodeURI, decodeURIComponent, encodeURI, encodeURIComponent, escape, eval, expect, exports, fetch, global, globalThis, isFinite, isNaN, jest, module, navigator, parseFloat, parseInt, performance, process, queueMicrotask, require, setImmediate, setInterval, setTimeout, structuredClone, undefined, unescape.\n    Note: This is a precaution to guard against uninitialized mock variables. If it is ensured that the mock is required lazily, variable names prefixed with `mock` (case insensitive) are permitted.\n\n    \u001b[0m \u001b[90m 25 |\u001b[39m     \u001b[36mif\u001b[39m (req\u001b[33m.\u001b[39mpath \u001b[33m===\u001b[39m \u001b[32m'/'\u001b[39m) {\n     \u001b[90m 26 |\u001b[39m       \u001b[36mif\u001b[39m (req\u001b[33m.\u001b[39mmethod \u001b[33m===\u001b[39m \u001b[32m'GET'\u001b[39m) {\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 27 |\u001b[39m         \u001b[36mconst\u001b[39m items \u001b[33m=\u001b[39m \u001b[33mArray\u001b[39m(\u001b[35m10\u001b[39m)\u001b[33m.\u001b[39mfill()\u001b[33m.\u001b[39mmap(() \u001b[33m=>\u001b[39m generateEvidenceItem())\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 28 |\u001b[39m         \u001b[36mreturn\u001b[39m res\u001b[33m.\u001b[39mstatus(\u001b[35m200\u001b[39m)\u001b[33m.\u001b[39mjson({\n     \u001b[90m 29 |\u001b[39m           items\u001b[33m,\u001b[39m\n     \u001b[90m 30 |\u001b[39m           total\u001b[33m:\u001b[39m \u001b[35m100\u001b[39m\u001b[33m,\u001b[39m\u001b[0m\n\n      \u001b[2mat File.buildCodeFrameError (\u001b[22mnode_modules/@babel/core/src/transformation/file/file.ts\u001b[2m:247:12)\u001b[22m\n      \u001b[2mat NodePath.buildError [as buildCodeFrameError] (\u001b[22mnode_modules/@babel/traverse/src/path/index.ts\u001b[2m:141:21)\u001b[22m\n      \u001b[2mat call (\u001b[22mnode_modules/@babel/traverse/src/visitors.ts\u001b[2m:303:14)\u001b[22m\n      \u001b[2mat NodePath.call [as _call] (\u001b[22mnode_modules/@babel/traverse/src/path/context.ts\u001b[2m:36:20)\u001b[22m\n      \u001b[2mat NodePath.call (\u001b[22mnode_modules/@babel/traverse/src/path/context.ts\u001b[2m:21:18)\u001b[22m\n      \u001b[2mat NodePath.call [as visit] (\u001b[22mnode_modules/@babel/traverse/src/path/context.ts\u001b[2m:97:31)\u001b[22m\n      \u001b[2mat TraversalContext.visit [as visitQueue] (\u001b[22mnode_modules/@babel/traverse/src/context.ts\u001b[2m:148:16)\u001b[22m\n      \u001b[2mat TraversalContext.visitQueue [as visitMultiple] (\u001b[22mnode_modules/@babel/traverse/src/context.ts\u001b[2m:99:17)\u001b[22m\n      \u001b[2mat TraversalContext.visitMultiple [as visit] (\u001b[22mnode_modules/@babel/traverse/src/context.ts\u001b[2m:178:19)\u001b[22m\n      \u001b[2mat visit (\u001b[22mnode_modules/@babel/traverse/src/traverse-node.ts\u001b[2m:40:17)\u001b[22m\n      \u001b[2mat NodePath.visit (\u001b[22mnode_modules/@babel/traverse/src/path/context.ts\u001b[2m:104:33)\u001b[22m\n      \u001b[2mat TraversalContext.visit [as visitQueue] (\u001b[22mnode_modules/@babel/traverse/src/context.ts\u001b[2m:148:16)\u001b[22m\n      \u001b[2mat TraversalContext.visitQueue [as visitSingle] (\u001b[22mnode_modules/@babel/traverse/src/context.ts\u001b[2m:109:19)\u001b[22m\n      \u001b[2mat TraversalContext.visitSingle [as visit] (\u001b[22mnode_modules/@babel/traverse/src/context.ts\u001b[2m:180:19)\u001b[22m\n      \u001b[2mat visit (\u001b[22mnode_modules/@babel/traverse/src/traverse-node.ts\u001b[2m:40:17)\u001b[22m\n      \u001b[2mat traverse (\u001b[22mnode_modules/@babel/traverse/src/index.ts\u001b[2m:83:15)\u001b[22m\n      \u001b[2mat transformFile (\u001b[22mnode_modules/@babel/core/src/transformation/index.ts\u001b[2m:119:15)\u001b[22m\n          at transformFile.next (<anonymous>)\n      \u001b[2mat transformFile (\u001b[22mnode_modules/@babel/core/src/transformation/index.ts\u001b[2m:49:12)\u001b[22m\n          at run.next (<anonymous>)\n      \u001b[2mat transform (\u001b[22mnode_modules/@babel/core/src/transform.ts\u001b[2m:29:20)\u001b[22m\n          at transform.next (<anonymous>)\n      \u001b[2mat evaluateSync (\u001b[22mnode_modules/gensync/index.js\u001b[2m:251:28)\u001b[22m\n      \u001b[2mat sync (\u001b[22mnode_modules/gensync/index.js\u001b[2m:89:14)\u001b[22m\n      \u001b[2mat fn (\u001b[22mnode_modules/@babel/core/src/errors/rewrite-stack-trace.ts\u001b[2m:99:14)\u001b[22m\n      \u001b[2mat transformSync (\u001b[22mnode_modules/@babel/core/src/transform.ts\u001b[2m:66:52)\u001b[22m\n      \u001b[2mat ScriptTransformer.transformSource (\u001b[22mnode_modules/@jest/transform/build/ScriptTransformer.js\u001b[2m:545:31)\u001b[22m\n      \u001b[2mat ScriptTransformer._transformAndBuildScript (\u001b[22mnode_modules/@jest/transform/build/ScriptTransformer.js\u001b[2m:674:40)\u001b[22m\n      \u001b[2mat ScriptTransformer.transform (\u001b[22mnode_modules/@jest/transform/build/ScriptTransformer.js\u001b[2m:726:19)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": {"code": "BABEL_TRANSFORM_ERROR"}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\novaproof\\evidence-verification.test.js", "testResults": []}], "wasInterrupted": false}