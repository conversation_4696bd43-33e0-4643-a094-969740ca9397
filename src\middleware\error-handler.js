/**
 * NovaFuse Universal API Connector - Error Handling Middleware
 * 
 * This module provides middleware for handling errors.
 */

const { createLogger } = require('../utils/logger');

const logger = createLogger('error-handler');

/**
 * Error handling middleware
 * 
 * @param {Error} err - The error object
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function errorHandler(err, req, res, next) {
  // Log error
  logger.error(`Error handling request: ${err.message}`, {
    url: req.url,
    method: req.method
  });
  
  // Check if response has already been sent
  if (res.headersSent) {
    return next(err);
  }
  
  // Get status code from error or default to 500
  const statusCode = err.statusCode || 500;
  
  // Get error code from error or default to INTERNAL_ERROR
  const errorCode = err.code || 'INTERNAL_ERROR';
  
  // Get error details from error or default to empty object
  const errorDetails = err.details || {};
  
  // Send error response
  res.status(statusCode).json({
    error: {
      message: err.message,
      code: errorCode,
      details: errorDetails
    }
  });
}

/**
 * Not found middleware
 * 
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 */
function notFound(req, res) {
  logger.warn(`Route not found: ${req.method} ${req.url}`);
  
  res.status(404).json({
    error: {
      message: `Route not found: ${req.method} ${req.url}`,
      code: 'NOT_FOUND'
    }
  });
}

module.exports = {
  errorHandler,
  notFound
};
