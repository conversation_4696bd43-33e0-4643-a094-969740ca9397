<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSDE Batch Processing</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <style>
    body {
      font-family: 'Roboto', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      color: #333;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      background-color: #1a73e8;
      color: white;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    header h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 400;
    }
    
    header p {
      margin: 5px 0 0;
      font-size: 14px;
      opacity: 0.8;
    }
    
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    
    .card h2 {
      margin-top: 0;
      font-size: 18px;
      font-weight: 500;
      color: #1a73e8;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    
    .button {
      background-color: #1a73e8;
      color: white;
      border: none;
      padding: 10px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      transition: background-color 0.3s;
    }
    
    .button:hover {
      background-color: #0d47a1;
    }
    
    .button i {
      margin-right: 8px;
    }
    
    .button.secondary {
      background-color: #f5f5f5;
      color: #333;
      border: 1px solid #ddd;
    }
    
    .button.secondary:hover {
      background-color: #e0e0e0;
    }
    
    .button.danger {
      background-color: #d32f2f;
    }
    
    .button.danger:hover {
      background-color: #b71c1c;
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
    }
    
    .form-control {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .form-control:focus {
      border-color: #1a73e8;
      outline: none;
    }
    
    .file-upload {
      border: 2px dashed #ddd;
      border-radius: 4px;
      padding: 20px;
      text-align: center;
      cursor: pointer;
      transition: border-color 0.3s;
    }
    
    .file-upload:hover {
      border-color: #1a73e8;
    }
    
    .file-upload i {
      font-size: 48px;
      color: #1a73e8;
      margin-bottom: 10px;
    }
    
    .file-upload p {
      margin: 0;
      color: #666;
    }
    
    .file-list {
      margin-top: 20px;
    }
    
    .file-item {
      display: flex;
      align-items: center;
      padding: 10px;
      border-bottom: 1px solid #eee;
    }
    
    .file-item:last-child {
      border-bottom: none;
    }
    
    .file-item i {
      margin-right: 10px;
      color: #1a73e8;
    }
    
    .file-item .file-name {
      flex-grow: 1;
    }
    
    .file-item .file-size {
      color: #666;
      font-size: 12px;
      margin-right: 10px;
    }
    
    .file-item .file-actions {
      display: flex;
      gap: 5px;
    }
    
    .table {
      width: 100%;
      border-collapse: collapse;
    }
    
    .table th,
    .table td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }
    
    .table th {
      font-weight: 500;
      color: #666;
    }
    
    .table tbody tr:hover {
      background-color: #f9f9f9;
    }
    
    .badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .badge.success {
      background-color: #e8f5e9;
      color: #2e7d32;
    }
    
    .badge.warning {
      background-color: #fff8e1;
      color: #f57f17;
    }
    
    .badge.danger {
      background-color: #ffebee;
      color: #c62828;
    }
    
    .badge.info {
      background-color: #e3f2fd;
      color: #1565c0;
    }
    
    .progress-container {
      width: 100%;
      height: 8px;
      background-color: #e0e0e0;
      border-radius: 4px;
      overflow: hidden;
    }
    
    .progress-bar {
      height: 100%;
      background-color: #1a73e8;
      transition: width 0.3s;
    }
    
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
    
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top: 4px solid #1a73e8;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    footer {
      text-align: center;
      padding: 20px;
      font-size: 12px;
      color: #666;
      border-top: 1px solid #eee;
      margin-top: 40px;
    }
  </style>
</head>
<body>
  <header>
    <div class="container">
      <h1>CSDE Batch Processing</h1>
      <p>Process large volumes of security findings with the Cyber-Safety Domain Engine</p>
    </div>
  </header>
  
  <div class="container">
    <div class="card">
      <h2>Upload Files</h2>
      <form id="uploadForm">
        <div class="file-upload" id="dropZone">
          <input type="file" id="fileInput" multiple accept=".json" style="display: none;">
          <i class="material-icons">cloud_upload</i>
          <p>Drag and drop files here or click to browse</p>
          <p class="small">Accepts JSON files only</p>
        </div>
        
        <div class="file-list" id="fileList"></div>
        
        <div class="button-group">
          <button type="submit" class="button">
            <i class="material-icons">upload</i> Upload Files
          </button>
          <button type="button" class="button secondary" id="clearBtn">
            <i class="material-icons">clear</i> Clear
          </button>
        </div>
      </form>
    </div>
    
    <div class="card">
      <h2>Active Jobs</h2>
      <div id="activeJobs">
        <div class="loading">
          <div class="spinner"></div>
        </div>
      </div>
      
      <div class="button-group">
        <button type="button" class="button" id="refreshJobsBtn">
          <i class="material-icons">refresh</i> Refresh
        </button>
        <button type="button" class="button" id="startProcessingBtn">
          <i class="material-icons">play_arrow</i> Start Processing
        </button>
      </div>
    </div>
    
    <div class="card">
      <h2>Job History</h2>
      <div id="jobHistory">
        <div class="loading">
          <div class="spinner"></div>
        </div>
      </div>
      
      <div class="button-group">
        <button type="button" class="button secondary" id="clearHistoryBtn">
          <i class="material-icons">delete</i> Clear History
        </button>
      </div>
    </div>
  </div>
  
  <footer>
    <div class="container">
      <p>NovaFuse Universal API Connector &copy; 2025 | Powered by NovaVision</p>
    </div>
  </footer>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Elements
      const fileInput = document.getElementById('fileInput');
      const dropZone = document.getElementById('dropZone');
      const fileList = document.getElementById('fileList');
      const uploadForm = document.getElementById('uploadForm');
      const clearBtn = document.getElementById('clearBtn');
      const refreshJobsBtn = document.getElementById('refreshJobsBtn');
      const startProcessingBtn = document.getElementById('startProcessingBtn');
      const clearHistoryBtn = document.getElementById('clearHistoryBtn');
      const activeJobs = document.getElementById('activeJobs');
      const jobHistory = document.getElementById('jobHistory');
      
      // Files to upload
      let filesToUpload = [];
      
      // Initialize
      loadActiveJobs();
      loadJobHistory();
      
      // Set up refresh interval
      const refreshInterval = setInterval(loadActiveJobs, 5000);
      
      // Event listeners
      dropZone.addEventListener('click', () => fileInput.click());
      fileInput.addEventListener('change', handleFileSelect);
      dropZone.addEventListener('dragover', handleDragOver);
      dropZone.addEventListener('drop', handleDrop);
      uploadForm.addEventListener('submit', handleUpload);
      clearBtn.addEventListener('click', clearFiles);
      refreshJobsBtn.addEventListener('click', () => {
        loadActiveJobs();
        loadJobHistory();
      });
      startProcessingBtn.addEventListener('click', startProcessing);
      clearHistoryBtn.addEventListener('click', clearHistory);
      
      // Handle file select
      function handleFileSelect(event) {
        const files = event.target.files;
        addFiles(files);
      }
      
      // Handle drag over
      function handleDragOver(event) {
        event.preventDefault();
        event.stopPropagation();
        dropZone.style.borderColor = '#1a73e8';
      }
      
      // Handle drop
      function handleDrop(event) {
        event.preventDefault();
        event.stopPropagation();
        dropZone.style.borderColor = '#ddd';
        
        const files = event.dataTransfer.files;
        addFiles(files);
      }
      
      // Add files to list
      function addFiles(files) {
        for (const file of files) {
          // Check if file is JSON
          if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
            alert(`File "${file.name}" is not a JSON file`);
            continue;
          }
          
          // Check if file is already in list
          if (filesToUpload.some(f => f.name === file.name && f.size === file.size)) {
            continue;
          }
          
          // Add file to list
          filesToUpload.push(file);
        }
        
        // Update file list
        updateFileList();
      }
      
      // Update file list
      function updateFileList() {
        fileList.innerHTML = '';
        
        if (filesToUpload.length === 0) {
          fileList.innerHTML = '<p>No files selected</p>';
          return;
        }
        
        for (let i = 0; i < filesToUpload.length; i++) {
          const file = filesToUpload[i];
          
          const fileItem = document.createElement('div');
          fileItem.className = 'file-item';
          
          const icon = document.createElement('i');
          icon.className = 'material-icons';
          icon.textContent = 'description';
          
          const fileName = document.createElement('div');
          fileName.className = 'file-name';
          fileName.textContent = file.name;
          
          const fileSize = document.createElement('div');
          fileSize.className = 'file-size';
          fileSize.textContent = formatFileSize(file.size);
          
          const fileActions = document.createElement('div');
          fileActions.className = 'file-actions';
          
          const removeBtn = document.createElement('button');
          removeBtn.className = 'button danger';
          removeBtn.innerHTML = '<i class="material-icons">delete</i>';
          removeBtn.addEventListener('click', () => {
            filesToUpload.splice(i, 1);
            updateFileList();
          });
          
          fileActions.appendChild(removeBtn);
          fileItem.appendChild(icon);
          fileItem.appendChild(fileName);
          fileItem.appendChild(fileSize);
          fileItem.appendChild(fileActions);
          
          fileList.appendChild(fileItem);
        }
      }
      
      // Format file size
      function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      }
      
      // Handle upload
      function handleUpload(event) {
        event.preventDefault();
        
        if (filesToUpload.length === 0) {
          alert('No files selected');
          return;
        }
        
        const formData = new FormData();
        
        for (const file of filesToUpload) {
          formData.append('files', file);
        }
        
        fetch('/csde/batch/upload', {
          method: 'POST',
          body: formData
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Failed to upload files');
            }
            return response.json();
          })
          .then(data => {
            if (data.success) {
              alert(`${data.files.length} files uploaded successfully`);
              clearFiles();
              loadActiveJobs();
            } else {
              throw new Error(data.error || 'Unknown error');
            }
          })
          .catch(error => {
            alert('Error uploading files: ' + error.message);
            console.error('Error uploading files:', error);
          });
      }
      
      // Clear files
      function clearFiles() {
        filesToUpload = [];
        updateFileList();
        fileInput.value = '';
      }
      
      // Load active jobs
      function loadActiveJobs() {
        activeJobs.innerHTML = '<div class="loading"><div class="spinner"></div></div>';
        
        fetch('/csde/batch/status')
          .then(response => {
            if (!response.ok) {
              throw new Error('Failed to load active jobs');
            }
            return response.json();
          })
          .then(data => {
            if (data.success) {
              renderActiveJobs(data);
            } else {
              throw new Error(data.error || 'Unknown error');
            }
          })
          .catch(error => {
            activeJobs.innerHTML = `
              <div class="error">
                <p>Error loading active jobs: ${error.message}</p>
                <button class="button" onclick="loadActiveJobs()">Try Again</button>
              </div>
            `;
            console.error('Error loading active jobs:', error);
          });
      }
      
      // Render active jobs
      function renderActiveJobs(data) {
        if (!data.isProcessing && data.activeJobs.length === 0 && data.queueLength === 0) {
          activeJobs.innerHTML = '<p>No active jobs</p>';
          return;
        }
        
        let html = '';
        
        if (data.activeJobs.length > 0) {
          html += '<h3>Processing</h3>';
          html += '<table class="table">';
          html += '<thead><tr><th>Job ID</th><th>Status</th><th>Progress</th><th>Actions</th></tr></thead>';
          html += '<tbody>';
          
          for (const job of data.activeJobs) {
            const progress = job.stats.totalFindings > 0 ? 
              (job.stats.processedFindings / job.stats.totalFindings) * 100 : 0;
            
            html += '<tr>';
            html += `<td>${job.id}</td>`;
            html += `<td><span class="badge info">${job.status}</span></td>`;
            html += '<td>';
            html += '<div class="progress-container">';
            html += `<div class="progress-bar" style="width: ${progress}%"></div>`;
            html += '</div>';
            html += `<div style="text-align: right; font-size: 12px; margin-top: 5px;">${job.stats.processedFindings} / ${job.stats.totalFindings}</div>`;
            html += '</td>';
            html += '<td>';
            html += `<button class="button danger" onclick="cancelJob('${job.id}')"><i class="material-icons">cancel</i> Cancel</button>`;
            html += '</td>';
            html += '</tr>';
          }
          
          html += '</tbody></table>';
        }
        
        if (data.queueLength > 0) {
          html += '<h3>Queue</h3>';
          html += '<table class="table">';
          html += '<thead><tr><th>Position</th><th>Job ID</th><th>Status</th><th>Actions</th></tr></thead>';
          html += '<tbody>';
          
          for (let i = 0; i < data.jobQueue.length; i++) {
            const job = data.jobQueue[i];
            
            html += '<tr>';
            html += `<td>${i + 1}</td>`;
            html += `<td>${job.id}</td>`;
            html += `<td><span class="badge warning">${job.status}</span></td>`;
            html += '<td>';
            html += `<button class="button danger" onclick="removeFromQueue('${job.id}')"><i class="material-icons">delete</i> Remove</button>`;
            html += '</td>';
            html += '</tr>';
          }
          
          html += '</tbody></table>';
        }
        
        activeJobs.innerHTML = html;
        
        // Add global functions for buttons
        window.cancelJob = function(jobId) {
          if (confirm(`Are you sure you want to cancel job ${jobId}?`)) {
            fetch(`/csde/batch/cancel/${jobId}`, { method: 'POST' })
              .then(response => {
                if (!response.ok) {
                  throw new Error('Failed to cancel job');
                }
                return response.json();
              })
              .then(data => {
                if (data.success) {
                  alert('Job cancelled successfully');
                  loadActiveJobs();
                } else {
                  throw new Error(data.error || 'Unknown error');
                }
              })
              .catch(error => {
                alert('Error cancelling job: ' + error.message);
                console.error('Error cancelling job:', error);
              });
          }
        };
      }
      
      // Load job history
      function loadJobHistory() {
        jobHistory.innerHTML = '<div class="loading"><div class="spinner"></div></div>';
        
        fetch('/csde/batch/history')
          .then(response => {
            if (!response.ok) {
              throw new Error('Failed to load job history');
            }
            return response.json();
          })
          .then(data => {
            if (data.success) {
              renderJobHistory(data.jobHistory);
            } else {
              throw new Error(data.error || 'Unknown error');
            }
          })
          .catch(error => {
            jobHistory.innerHTML = `
              <div class="error">
                <p>Error loading job history: ${error.message}</p>
                <button class="button" onclick="loadJobHistory()">Try Again</button>
              </div>
            `;
            console.error('Error loading job history:', error);
          });
      }
      
      // Render job history
      function renderJobHistory(history) {
        if (history.length === 0) {
          jobHistory.innerHTML = '<p>No job history</p>';
          return;
        }
        
        let html = '<table class="table">';
        html += '<thead><tr><th>Job ID</th><th>Status</th><th>Files</th><th>Findings</th><th>Duration</th><th>Actions</th></tr></thead>';
        html += '<tbody>';
        
        for (const job of history) {
          const duration = job.stats.duration ? 
            (job.stats.duration / 1000).toFixed(2) + 's' : 'N/A';
          
          let statusClass = 'info';
          if (job.status === 'COMPLETED') statusClass = 'success';
          if (job.status === 'FAILED') statusClass = 'danger';
          if (job.status === 'CANCELLED') statusClass = 'warning';
          
          html += '<tr>';
          html += `<td>${job.id}</td>`;
          html += `<td><span class="badge ${statusClass}">${job.status}</span></td>`;
          html += `<td>${job.stats.processedFiles} / ${job.stats.totalFiles}</td>`;
          html += `<td>${job.stats.successfulFindings} / ${job.stats.totalFindings}</td>`;
          html += `<td>${duration}</td>`;
          html += '<td>';
          html += `<button class="button secondary" onclick="viewJobDetails('${job.id}')"><i class="material-icons">visibility</i> View</button>`;
          html += '</td>';
          html += '</tr>';
        }
        
        html += '</tbody></table>';
        
        jobHistory.innerHTML = html;
        
        // Add global function for view button
        window.viewJobDetails = function(jobId) {
          fetch(`/csde/batch/job/${jobId}`)
            .then(response => {
              if (!response.ok) {
                throw new Error('Failed to load job details');
              }
              return response.json();
            })
            .then(data => {
              if (data.success) {
                showJobDetailsModal(data.job);
              } else {
                throw new Error(data.error || 'Unknown error');
              }
            })
            .catch(error => {
              alert('Error loading job details: ' + error.message);
              console.error('Error loading job details:', error);
            });
        };
      }
      
      // Show job details modal
      function showJobDetailsModal(job) {
        // Create modal container
        const modal = document.createElement('div');
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.width = '100%';
        modal.style.height = '100%';
        modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        modal.style.display = 'flex';
        modal.style.justifyContent = 'center';
        modal.style.alignItems = 'center';
        modal.style.zIndex = '1000';
        
        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.style.backgroundColor = 'white';
        modalContent.style.padding = '20px';
        modalContent.style.borderRadius = '8px';
        modalContent.style.maxWidth = '800px';
        modalContent.style.width = '100%';
        modalContent.style.maxHeight = '80vh';
        modalContent.style.overflow = 'auto';
        
        // Set job details HTML
        let html = `<h2>Job Details: ${job.id}</h2>`;
        html += `<p><strong>Status:</strong> ${job.status}</p>`;
        html += `<p><strong>Started:</strong> ${new Date(job.startedAt).toLocaleString()}</p>`;
        
        if (job.completedAt) {
          html += `<p><strong>Completed:</strong> ${new Date(job.completedAt).toLocaleString()}</p>`;
        }
        
        if (job.error) {
          html += `<p><strong>Error:</strong> ${job.error}</p>`;
        }
        
        html += '<h3>Statistics</h3>';
        html += '<table class="table">';
        html += '<tr><td>Total Files</td><td>' + (job.stats.totalFiles || 0) + '</td></tr>';
        html += '<tr><td>Processed Files</td><td>' + (job.stats.processedFiles || 0) + '</td></tr>';
        html += '<tr><td>Total Findings</td><td>' + (job.stats.totalFindings || 0) + '</td></tr>';
        html += '<tr><td>Processed Findings</td><td>' + (job.stats.processedFindings || 0) + '</td></tr>';
        html += '<tr><td>Successful Findings</td><td>' + (job.stats.successfulFindings || 0) + '</td></tr>';
        html += '<tr><td>Failed Findings</td><td>' + (job.stats.failedFindings || 0) + '</td></tr>';
        html += '<tr><td>Remediation Actions</td><td>' + (job.stats.remediationActions || 0) + '</td></tr>';
        
        if (job.stats.duration) {
          html += '<tr><td>Duration</td><td>' + (job.stats.duration / 1000).toFixed(2) + 's</td></tr>';
        }
        
        html += '</table>';
        
        modalContent.innerHTML = html;
        
        // Add close button
        const closeButton = document.createElement('button');
        closeButton.textContent = 'Close';
        closeButton.className = 'button secondary';
        closeButton.style.marginTop = '20px';
        closeButton.addEventListener('click', function() {
          document.body.removeChild(modal);
        });
        
        modalContent.appendChild(closeButton);
        modal.appendChild(modalContent);
        document.body.appendChild(modal);
      }
      
      // Start processing
      function startProcessing() {
        fetch('/csde/batch/start', { method: 'POST' })
          .then(response => {
            if (!response.ok) {
              throw new Error('Failed to start processing');
            }
            return response.json();
          })
          .then(data => {
            if (data.success) {
              alert('Processing started successfully');
              loadActiveJobs();
            } else {
              throw new Error(data.error || 'Unknown error');
            }
          })
          .catch(error => {
            alert('Error starting processing: ' + error.message);
            console.error('Error starting processing:', error);
          });
      }
      
      // Clear history
      function clearHistory() {
        if (confirm('Are you sure you want to clear the job history?')) {
          fetch('/csde/batch/history/clear', { method: 'POST' })
            .then(response => {
              if (!response.ok) {
                throw new Error('Failed to clear history');
              }
              return response.json();
            })
            .then(data => {
              if (data.success) {
                alert('History cleared successfully');
                loadJobHistory();
              } else {
                throw new Error(data.error || 'Unknown error');
              }
            })
            .catch(error => {
              alert('Error clearing history: ' + error.message);
              console.error('Error clearing history:', error);
            });
        }
      }
    });
  </script>
</body>
</html>
