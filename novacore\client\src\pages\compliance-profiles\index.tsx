/**
 * Compliance Profiles Page
 * 
 * This page displays a list of compliance profiles and allows users to manage them.
 * It leverages the existing NovaPrime components and connects them to the NovaPulse API.
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { NovaPulseAPI, ComplianceProfile } from '@/api/novaPulseApi';
import { ComplianceNavigation } from '@/components/compliance/ComplianceNavigation';
import { ComplianceGapChart } from '@/components/dashboard/ComplianceGapChart';
import { ComplianceMetricsChart } from '@/components/dashboard/ComplianceMetricsChart';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Plus, Filter, Download, CheckCircle, AlertCircle, Clock } from 'lucide-react';

export default function ComplianceProfilesPage() {
  const router = useRouter();
  const [complianceProfiles, setComplianceProfiles] = useState<ComplianceProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  const [organizationId, setOrganizationId] = useState('');
  const [selectedProfile, setSelectedProfile] = useState<ComplianceProfile | null>(null);

  const api = new NovaPulseAPI();

  useEffect(() => {
    // Get organization ID from local storage or context
    const orgId = localStorage.getItem('organizationId') || '';
    setOrganizationId(orgId);

    if (orgId) {
      loadComplianceProfiles(orgId);
    }
  }, []);

  const loadComplianceProfiles = async (orgId: string) => {
    setLoading(true);
    try {
      const response = await api.getComplianceProfiles(orgId);
      setComplianceProfiles(response.data);
      
      // Select the first profile by default if available
      if (response.data.length > 0) {
        setSelectedProfile(response.data[0]);
      }
    } catch (error) {
      console.error('Error loading compliance profiles:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleCreateProfile = () => {
    router.push('/compliance-profiles/create');
  };

  const handleProfileClick = (profile: ComplianceProfile) => {
    setSelectedProfile(profile);
    setActiveTab('details');
  };

  const filteredProfiles = complianceProfiles.filter(profile => 
    profile.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    profile.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Prepare data for the compliance gap chart
  const complianceGapData = selectedProfile?.applicableFrameworks.map(framework => ({
    framework: framework.frameworkName,
    compliant: Math.round(framework.complianceStatus.score),
    gap: 100 - Math.round(framework.complianceStatus.score)
  })) || [];

  // Calculate overall compliance statistics
  const overallStats = {
    totalFrameworks: selectedProfile?.applicableFrameworks.length || 0,
    totalRegulations: selectedProfile?.applicableRegulations.length || 0,
    averageScore: selectedProfile?.applicableFrameworks.reduce((sum, fw) => 
      sum + fw.complianceStatus.score, 0) / 
      (selectedProfile?.applicableFrameworks.length || 1) || 0,
    status: selectedProfile?.overallComplianceStatus.status || 'unknown'
  };

  // Calculate control implementation statistics
  const controlStats = {
    implemented: selectedProfile?.applicableFrameworks.reduce((sum, fw) => 
      sum + fw.controlImplementation.implemented, 0) || 0,
    partiallyImplemented: selectedProfile?.applicableFrameworks.reduce((sum, fw) => 
      sum + fw.controlImplementation.partiallyImplemented, 0) || 0,
    notImplemented: selectedProfile?.applicableFrameworks.reduce((sum, fw) => 
      sum + fw.controlImplementation.notImplemented, 0) || 0,
    notApplicable: selectedProfile?.applicableFrameworks.reduce((sum, fw) => 
      sum + fw.controlImplementation.notApplicable, 0) || 0,
    total: selectedProfile?.applicableFrameworks.reduce((sum, fw) => 
      sum + fw.controlImplementation.total, 0) || 0
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Compliance Profiles</h1>
      
      <ComplianceNavigation />
      
      <div className="mb-6 flex flex-col md:flex-row justify-between gap-4">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <input
            type="text"
            placeholder="Search profiles..."
            className="pl-9 pr-4 py-2 border rounded-md w-full md:w-64"
            value={searchQuery}
            onChange={handleSearch}
          />
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm" onClick={handleCreateProfile}>
            <Plus className="h-4 w-4 mr-2" />
            New Profile
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Profiles</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-4">Loading profiles...</div>
              ) : filteredProfiles.length === 0 ? (
                <div className="text-center py-4">
                  No profiles found. {searchQuery && 'Try a different search term.'}
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredProfiles.map(profile => (
                    <div
                      key={profile._id}
                      className={`p-3 rounded-md cursor-pointer transition-colors ${
                        selectedProfile?._id === profile._id
                          ? 'bg-blue-100 border-l-4 border-blue-500'
                          : 'hover:bg-gray-100'
                      }`}
                      onClick={() => handleProfileClick(profile)}
                    >
                      <div className="font-medium">{profile.name}</div>
                      <div className="text-xs text-gray-500">
                        {profile.organizationDetails.industry} | {profile.status}
                      </div>
                      <div className="flex items-center mt-1">
                        <div
                          className={`h-2 flex-grow rounded-full ${
                            profile.overallComplianceStatus.score > 80
                              ? 'bg-green-500'
                              : profile.overallComplianceStatus.score > 60
                              ? 'bg-yellow-500'
                              : 'bg-red-500'
                          }`}
                        ></div>
                        <span className="ml-2 text-xs font-medium">
                          {Math.round(profile.overallComplianceStatus.score)}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        <div className="md:col-span-3">
          {selectedProfile ? (
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="frameworks">Frameworks</TabsTrigger>
                <TabsTrigger value="regulations">Regulations</TabsTrigger>
                <TabsTrigger value="data">Data Inventory</TabsTrigger>
                <TabsTrigger value="details">Details</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>{selectedProfile.name}</CardTitle>
                    <CardDescription>{selectedProfile.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                      <div className="bg-white p-4 rounded-lg border shadow-sm text-center">
                        <div className="text-sm text-gray-500 mb-1">Overall Status</div>
                        <div className={`text-xl font-bold ${
                          overallStats.status === 'compliant' ? 'text-green-600' :
                          overallStats.status === 'partially_compliant' ? 'text-yellow-600' :
                          'text-red-600'
                        }`}>
                          {overallStats.status === 'compliant' ? 'Compliant' :
                           overallStats.status === 'partially_compliant' ? 'Partial' :
                           'Non-Compliant'}
                        </div>
                        <div className="text-sm mt-1">
                          {Math.round(overallStats.averageScore)}% Score
                        </div>
                      </div>
                      
                      <div className="bg-white p-4 rounded-lg border shadow-sm text-center">
                        <div className="text-sm text-gray-500 mb-1">Frameworks</div>
                        <div className="text-xl font-bold">{overallStats.totalFrameworks}</div>
                        <div className="text-sm mt-1">Applied</div>
                      </div>
                      
                      <div className="bg-white p-4 rounded-lg border shadow-sm text-center">
                        <div className="text-sm text-gray-500 mb-1">Regulations</div>
                        <div className="text-xl font-bold">{overallStats.totalRegulations}</div>
                        <div className="text-sm mt-1">Applicable</div>
                      </div>
                      
                      <div className="bg-white p-4 rounded-lg border shadow-sm text-center">
                        <div className="text-sm text-gray-500 mb-1">Controls</div>
                        <div className="text-xl font-bold">{controlStats.total}</div>
                        <div className="text-sm mt-1">
                          <span className="text-green-600">{controlStats.implemented}</span> / 
                          <span className="text-yellow-600">{controlStats.partiallyImplemented}</span> / 
                          <span className="text-red-600">{controlStats.notImplemented}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Compliance Gaps</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ComplianceGapChart data={complianceGapData} />
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardHeader>
                          <CardTitle>Compliance Metrics</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ComplianceMetricsChart />
                        </CardContent>
                      </Card>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="frameworks" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Applicable Frameworks</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {selectedProfile.applicableFrameworks.length === 0 ? (
                      <div className="text-center py-4">No frameworks applied to this profile.</div>
                    ) : (
                      <div className="space-y-4">
                        {selectedProfile.applicableFrameworks.map(framework => (
                          <Card key={framework.frameworkId}>
                            <CardHeader className="pb-2">
                              <div className="flex justify-between">
                                <CardTitle>{framework.frameworkName}</CardTitle>
                                <div className={`px-2 py-1 text-xs rounded-full ${
                                  framework.complianceStatus.status === 'compliant' 
                                    ? 'bg-green-100 text-green-800' 
                                    : framework.complianceStatus.status === 'partially_compliant'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {framework.complianceStatus.status === 'compliant' 
                                    ? 'Compliant' 
                                    : framework.complianceStatus.status === 'partially_compliant'
                                    ? 'Partial'
                                    : 'Non-Compliant'}
                                </div>
                              </div>
                            </CardHeader>
                            <CardContent>
                              <div className="flex justify-between mb-2">
                                <div className="text-sm">Compliance Score</div>
                                <div className="text-sm font-medium">
                                  {Math.round(framework.complianceStatus.score)}%
                                </div>
                              </div>
                              <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                                <div 
                                  className={`h-full rounded-full ${
                                    framework.complianceStatus.score > 80
                                      ? 'bg-green-500'
                                      : framework.complianceStatus.score > 60
                                      ? 'bg-yellow-500'
                                      : 'bg-red-500'
                                  }`}
                                  style={{ width: `${framework.complianceStatus.score}%` }}
                                ></div>
                              </div>
                              
                              <div className="grid grid-cols-4 gap-2 mt-4 text-center text-sm">
                                <div>
                                  <div className="font-medium text-green-600">
                                    {framework.controlImplementation.implemented}
                                  </div>
                                  <div className="text-xs text-gray-500">Implemented</div>
                                </div>
                                <div>
                                  <div className="font-medium text-yellow-600">
                                    {framework.controlImplementation.partiallyImplemented}
                                  </div>
                                  <div className="text-xs text-gray-500">Partial</div>
                                </div>
                                <div>
                                  <div className="font-medium text-red-600">
                                    {framework.controlImplementation.notImplemented}
                                  </div>
                                  <div className="text-xs text-gray-500">Not Implemented</div>
                                </div>
                                <div>
                                  <div className="font-medium text-gray-600">
                                    {framework.controlImplementation.notApplicable}
                                  </div>
                                  <div className="text-xs text-gray-500">N/A</div>
                                </div>
                              </div>
                              
                              <div className="flex justify-between mt-4 text-xs text-gray-500">
                                <div>
                                  Last Assessment: {new Date(framework.complianceStatus.lastAssessment).toLocaleDateString()}
                                </div>
                                <div>
                                  Next Assessment: {new Date(framework.complianceStatus.nextAssessment).toLocaleDateString()}
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="regulations" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Applicable Regulations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {selectedProfile.applicableRegulations.length === 0 ? (
                      <div className="text-center py-4">No regulations applied to this profile.</div>
                    ) : (
                      <div className="space-y-4">
                        {selectedProfile.applicableRegulations.map(regulation => (
                          <Card key={regulation.regulationId}>
                            <CardHeader className="pb-2">
                              <div className="flex justify-between">
                                <CardTitle>{regulation.regulationName}</CardTitle>
                                <div className={`px-2 py-1 text-xs rounded-full ${
                                  regulation.complianceStatus.status === 'compliant' 
                                    ? 'bg-green-100 text-green-800' 
                                    : regulation.complianceStatus.status === 'partially_compliant'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {regulation.complianceStatus.status === 'compliant' 
                                    ? 'Compliant' 
                                    : regulation.complianceStatus.status === 'partially_compliant'
                                    ? 'Partial'
                                    : 'Non-Compliant'}
                                </div>
                              </div>
                            </CardHeader>
                            <CardContent>
                              <div className="flex justify-between mb-2">
                                <div className="text-sm">Compliance Score</div>
                                <div className="text-sm font-medium">
                                  {Math.round(regulation.complianceStatus.score)}%
                                </div>
                              </div>
                              <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                                <div 
                                  className={`h-full rounded-full ${
                                    regulation.complianceStatus.score > 80
                                      ? 'bg-green-500'
                                      : regulation.complianceStatus.score > 60
                                      ? 'bg-yellow-500'
                                      : 'bg-red-500'
                                  }`}
                                  style={{ width: `${regulation.complianceStatus.score}%` }}
                                ></div>
                              </div>
                              
                              <div className="grid grid-cols-4 gap-2 mt-4 text-center text-sm">
                                <div>
                                  <div className="font-medium text-green-600">
                                    {regulation.requirementImplementation.implemented}
                                  </div>
                                  <div className="text-xs text-gray-500">Implemented</div>
                                </div>
                                <div>
                                  <div className="font-medium text-yellow-600">
                                    {regulation.requirementImplementation.partiallyImplemented}
                                  </div>
                                  <div className="text-xs text-gray-500">Partial</div>
                                </div>
                                <div>
                                  <div className="font-medium text-red-600">
                                    {regulation.requirementImplementation.notImplemented}
                                  </div>
                                  <div className="text-xs text-gray-500">Not Implemented</div>
                                </div>
                                <div>
                                  <div className="font-medium text-gray-600">
                                    {regulation.requirementImplementation.notApplicable}
                                  </div>
                                  <div className="text-xs text-gray-500">N/A</div>
                                </div>
                              </div>
                              
                              <div className="flex justify-between mt-4 text-xs text-gray-500">
                                <div>
                                  Last Assessment: {new Date(regulation.complianceStatus.lastAssessment).toLocaleDateString()}
                                </div>
                                <div>
                                  Next Assessment: {new Date(regulation.complianceStatus.nextAssessment).toLocaleDateString()}
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="data" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Data Inventory</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {selectedProfile.dataInventory.length === 0 ? (
                      <div className="text-center py-4">No data inventory items defined.</div>
                    ) : (
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                          <thead>
                            <tr className="bg-gray-50 border-b">
                              <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Data Type</th>
                              <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Classification</th>
                              <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Volume</th>
                              <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Storage</th>
                              <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Processing</th>
                              <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Transfer</th>
                            </tr>
                          </thead>
                          <tbody>
                            {selectedProfile.dataInventory.map((item, index) => (
                              <tr key={index} className="border-b hover:bg-gray-50">
                                <td className="px-4 py-3 text-sm">{item.dataType}</td>
                                <td className="px-4 py-3 text-sm">
                                  <span className={`px-2 py-1 rounded-full text-xs ${
                                    item.classification === 'confidential' 
                                      ? 'bg-red-100 text-red-800' 
                                      : item.classification === 'restricted'
                                      ? 'bg-orange-100 text-orange-800'
                                      : item.classification === 'internal'
                                      ? 'bg-yellow-100 text-yellow-800'
                                      : 'bg-green-100 text-green-800'
                                  }`}>
                                    {item.classification}
                                  </span>
                                </td>
                                <td className="px-4 py-3 text-sm">{item.volume}</td>
                                <td className="px-4 py-3 text-sm">{item.storage}</td>
                                <td className="px-4 py-3 text-sm">{item.processing}</td>
                                <td className="px-4 py-3 text-sm">{item.transfer}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="details" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Profile Details</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-medium mb-4">Organization Details</h3>
                        <div className="space-y-3">
                          <div>
                            <div className="text-sm text-gray-500">Organization Name</div>
                            <div>{selectedProfile.organizationDetails.name}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">Industry</div>
                            <div>{selectedProfile.organizationDetails.industry}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">Size</div>
                            <div>{selectedProfile.organizationDetails.size}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">Type</div>
                            <div>{selectedProfile.organizationDetails.type}</div>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h3 className="text-lg font-medium mb-4">Jurisdictions</h3>
                        {selectedProfile.jurisdictions.length === 0 ? (
                          <div>No jurisdictions defined.</div>
                        ) : (
                          <div className="space-y-2">
                            {selectedProfile.jurisdictions.map((jurisdiction, index) => (
                              <div key={index} className="p-2 border rounded-md">
                                {jurisdiction.isGlobal ? (
                                  <div className="font-medium">Global</div>
                                ) : (
                                  <>
                                    <div className="font-medium">{jurisdiction.country}</div>
                                    {jurisdiction.region && (
                                      <div className="text-sm text-gray-500">{jurisdiction.region}</div>
                                    )}
                                  </>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="mt-6">
                      <h3 className="text-lg font-medium mb-4">Business Activities</h3>
                      {selectedProfile.businessActivities.length === 0 ? (
                        <div>No business activities defined.</div>
                      ) : (
                        <div className="space-y-4">
                          {selectedProfile.businessActivities.map((activity, index) => (
                            <Card key={index}>
                              <CardHeader className="pb-2">
                                <div className="flex justify-between">
                                  <CardTitle className="text-base">{activity.name}</CardTitle>
                                  <div className={`px-2 py-1 text-xs rounded-full ${
                                    activity.riskLevel === 'high' 
                                      ? 'bg-red-100 text-red-800' 
                                      : activity.riskLevel === 'medium'
                                      ? 'bg-yellow-100 text-yellow-800'
                                      : 'bg-green-100 text-green-800'
                                  }`}>
                                    {activity.riskLevel} risk
                                  </div>
                                </div>
                              </CardHeader>
                              <CardContent>
                                <p className="text-sm text-gray-600 mb-2">{activity.description}</p>
                                <div className="flex flex-wrap gap-1">
                                  {activity.dataTypes.map((dataType, i) => (
                                    <span key={i} className="text-xs px-2 py-1 rounded-full border">
                                      {dataType}
                                    </span>
                                  ))}
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex justify-end mt-6 space-x-2">
                      <Button variant="outline" size="sm">
                        Export Profile
                      </Button>
                      <Button size="sm">
                        Edit Profile
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          ) : (
            <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-1">No Profile Selected</h3>
                <p className="text-sm text-gray-500 mb-4">Select a profile from the list or create a new one.</p>
                <Button size="sm" onClick={handleCreateProfile}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Profile
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
