"""
<PERSON><PERSON><PERSON> to run the API server and client example.

This script starts the API server in a separate process and then runs the client example.
"""

import os
import sys
import subprocess
import time
import logging

# Add the parent directory to the path so we can import the UCECS modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the API server and client example."""
    # Get the path to the API server script
    api_script_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                                  'src', 'ucecs', 'api', 'run_api.py')

    # Get the path to the client example script
    client_script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                     'api_client_example.py')

    # Create a temporary directory for the example
    temp_dir = os.path.join(os.getcwd(), 'temp_api_example')
    os.makedirs(temp_dir, exist_ok=True)

    # Create an evidence directory
    evidence_dir = os.path.join(temp_dir, 'evidence_data')
    os.makedirs(evidence_dir, exist_ok=True)

    try:
        # Start the API server in a separate process
        logger.info("Starting API server...")
        api_process = subprocess.Popen([
            sys.executable,
            api_script_path,
            "--evidence-dir", evidence_dir,
            "--port", "5000",
            "--debug"
        ])

        # Wait for the API server to start
        logger.info("Waiting for API server to start...")
        time.sleep(3)

        # Run the client example
        logger.info("Running client example...")
        client_process = subprocess.run([sys.executable, client_script_path], check=True)

        # Wait for user input to stop the server
        input("\nPress Enter to stop the API server...")

    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"Error: {e}")
    finally:
        # Stop the API server
        if 'api_process' in locals():
            logger.info("Stopping API server...")
            api_process.terminate()
            api_process.wait()

        # Clean up the temporary directory
        # Uncomment the following line to delete the temporary directory
        # import shutil; shutil.rmtree(temp_dir)
        pass

if __name__ == "__main__":
    main()
