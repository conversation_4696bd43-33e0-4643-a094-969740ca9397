/**
 * ESG Metrics API - Controllers
 * 
 * This file defines the controllers for the ESG Metrics API.
 */

const { Metric, MetricValue, Target } = require('./models');
const logger = require('../../../utils/logger');

/**
 * Metric Controllers
 */
const metricController = {
  /**
   * Get all metrics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllMetrics: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-createdAt', category, framework, status, tag } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      // Build filter object
      const filter = {};
      if (category) filter.category = category;
      if (framework) filter.framework = framework;
      if (status) filter.status = status;
      if (tag) filter.tags = tag;
      
      const metrics = await Metric.find(filter)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit);
      
      const total = await Metric.countDocuments(filter);
      
      return res.status(200).json({
        success: true,
        data: metrics,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting metrics: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting metrics'
      });
    }
  },
  
  /**
   * Get metric by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getMetricById: async (req, res) => {
    try {
      const metric = await Metric.findById(req.params.id);
      
      if (!metric) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Metric not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: metric
      });
    } catch (error) {
      logger.error(`Error getting metric: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting the metric'
      });
    }
  },
  
  /**
   * Create a new metric
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createMetric: async (req, res) => {
    try {
      const metric = new Metric(req.body);
      await metric.save();
      
      return res.status(201).json({
        success: true,
        data: metric,
        message: 'Metric created successfully'
      });
    } catch (error) {
      logger.error(`Error creating metric: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while creating the metric'
      });
    }
  },
  
  /**
   * Update a metric
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateMetric: async (req, res) => {
    try {
      const metric = await Metric.findByIdAndUpdate(
        req.params.id,
        { ...req.body, updatedAt: Date.now() },
        { new: true, runValidators: true }
      );
      
      if (!metric) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Metric not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: metric,
        message: 'Metric updated successfully'
      });
    } catch (error) {
      logger.error(`Error updating metric: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while updating the metric'
      });
    }
  },
  
  /**
   * Delete a metric
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteMetric: async (req, res) => {
    try {
      const metric = await Metric.findByIdAndDelete(req.params.id);
      
      if (!metric) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Metric not found'
        });
      }
      
      // Delete related metric values and targets
      await MetricValue.deleteMany({ metric: req.params.id });
      await Target.deleteMany({ metric: req.params.id });
      
      return res.status(200).json({
        success: true,
        message: 'Metric and related data deleted successfully'
      });
    } catch (error) {
      logger.error(`Error deleting metric: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while deleting the metric'
      });
    }
  }
};

/**
 * Metric Value Controllers
 */
const metricValueController = {
  /**
   * Get all metric values
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllMetricValues: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-date', metric, dateFrom, dateTo, period, verificationStatus } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      // Build filter object
      const filter = {};
      if (metric) filter.metric = metric;
      if (dateFrom || dateTo) {
        filter.date = {};
        if (dateFrom) filter.date.$gte = new Date(dateFrom);
        if (dateTo) filter.date.$lte = new Date(dateTo);
      }
      if (period) filter.period = period;
      if (verificationStatus) filter.verificationStatus = verificationStatus;
      
      const metricValues = await MetricValue.find(filter)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit)
        .populate('metric');
      
      const total = await MetricValue.countDocuments(filter);
      
      return res.status(200).json({
        success: true,
        data: metricValues,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting metric values: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting metric values'
      });
    }
  },
  
  /**
   * Get metric values by metric ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getMetricValuesByMetricId: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-date', dateFrom, dateTo, period } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      // Build filter object
      const filter = { metric: req.params.metricId };
      if (dateFrom || dateTo) {
        filter.date = {};
        if (dateFrom) filter.date.$gte = new Date(dateFrom);
        if (dateTo) filter.date.$lte = new Date(dateTo);
      }
      if (period) filter.period = period;
      
      const metricValues = await MetricValue.find(filter)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit)
        .populate('metric');
      
      const total = await MetricValue.countDocuments(filter);
      
      return res.status(200).json({
        success: true,
        data: metricValues,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting metric values: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting metric values'
      });
    }
  },
  
  /**
   * Get metric value by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getMetricValueById: async (req, res) => {
    try {
      const metricValue = await MetricValue.findById(req.params.id).populate('metric');
      
      if (!metricValue) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Metric value not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: metricValue
      });
    } catch (error) {
      logger.error(`Error getting metric value: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting the metric value'
      });
    }
  },
  
  /**
   * Create a new metric value
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createMetricValue: async (req, res) => {
    try {
      // Check if metric exists
      const metric = await Metric.findById(req.body.metric);
      
      if (!metric) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Metric not found'
        });
      }
      
      const metricValue = new MetricValue(req.body);
      await metricValue.save();
      
      return res.status(201).json({
        success: true,
        data: metricValue,
        message: 'Metric value created successfully'
      });
    } catch (error) {
      logger.error(`Error creating metric value: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while creating the metric value'
      });
    }
  },
  
  /**
   * Update a metric value
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateMetricValue: async (req, res) => {
    try {
      const metricValue = await MetricValue.findByIdAndUpdate(
        req.params.id,
        { ...req.body, updatedAt: Date.now() },
        { new: true, runValidators: true }
      );
      
      if (!metricValue) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Metric value not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: metricValue,
        message: 'Metric value updated successfully'
      });
    } catch (error) {
      logger.error(`Error updating metric value: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while updating the metric value'
      });
    }
  },
  
  /**
   * Delete a metric value
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteMetricValue: async (req, res) => {
    try {
      const metricValue = await MetricValue.findByIdAndDelete(req.params.id);
      
      if (!metricValue) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Metric value not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        message: 'Metric value deleted successfully'
      });
    } catch (error) {
      logger.error(`Error deleting metric value: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while deleting the metric value'
      });
    }
  },
  
  /**
   * Verify a metric value
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  verifyMetricValue: async (req, res) => {
    try {
      const { verificationStatus, verifiedBy, verificationNotes } = req.body;
      
      const metricValue = await MetricValue.findByIdAndUpdate(
        req.params.id,
        {
          verificationStatus,
          verifiedBy,
          verificationNotes,
          verificationDate: Date.now(),
          updatedAt: Date.now()
        },
        { new: true, runValidators: true }
      );
      
      if (!metricValue) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Metric value not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: metricValue,
        message: 'Metric value verified successfully'
      });
    } catch (error) {
      logger.error(`Error verifying metric value: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while verifying the metric value'
      });
    }
  }
};

/**
 * Target Controllers
 */
const targetController = {
  /**
   * Get all targets
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllTargets: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-targetDate', metric, status } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      // Build filter object
      const filter = {};
      if (metric) filter.metric = metric;
      if (status) filter.status = status;
      
      const targets = await Target.find(filter)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit)
        .populate('metric');
      
      const total = await Target.countDocuments(filter);
      
      return res.status(200).json({
        success: true,
        data: targets,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting targets: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting targets'
      });
    }
  },
  
  /**
   * Get targets by metric ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getTargetsByMetricId: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-targetDate', status } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      // Build filter object
      const filter = { metric: req.params.metricId };
      if (status) filter.status = status;
      
      const targets = await Target.find(filter)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit)
        .populate('metric');
      
      const total = await Target.countDocuments(filter);
      
      return res.status(200).json({
        success: true,
        data: targets,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting targets: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting targets'
      });
    }
  },
  
  /**
   * Get target by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getTargetById: async (req, res) => {
    try {
      const target = await Target.findById(req.params.id).populate('metric');
      
      if (!target) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Target not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: target
      });
    } catch (error) {
      logger.error(`Error getting target: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting the target'
      });
    }
  },
  
  /**
   * Create a new target
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createTarget: async (req, res) => {
    try {
      // Check if metric exists
      const metric = await Metric.findById(req.body.metric);
      
      if (!metric) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Metric not found'
        });
      }
      
      const target = new Target(req.body);
      await target.save();
      
      return res.status(201).json({
        success: true,
        data: target,
        message: 'Target created successfully'
      });
    } catch (error) {
      logger.error(`Error creating target: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while creating the target'
      });
    }
  },
  
  /**
   * Update a target
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateTarget: async (req, res) => {
    try {
      const target = await Target.findByIdAndUpdate(
        req.params.id,
        { ...req.body, updatedAt: Date.now() },
        { new: true, runValidators: true }
      );
      
      if (!target) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Target not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: target,
        message: 'Target updated successfully'
      });
    } catch (error) {
      logger.error(`Error updating target: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while updating the target'
      });
    }
  },
  
  /**
   * Delete a target
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteTarget: async (req, res) => {
    try {
      const target = await Target.findByIdAndDelete(req.params.id);
      
      if (!target) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Target not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        message: 'Target deleted successfully'
      });
    } catch (error) {
      logger.error(`Error deleting target: ${error.message}`, { service: 'esg-metrics-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while deleting the target'
      });
    }
  }
};

module.exports = {
  metricController,
  metricValueController,
  targetController
};
