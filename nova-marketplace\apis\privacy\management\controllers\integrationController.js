/**
 * Integration Controller
 * 
 * This controller handles operations related to integrations with external systems.
 */

const { Integration } = require('../models');

// Get all integrations
const getAllIntegrations = async (req, res, next) => {
  try {
    const integrations = await Integration.find();
    
    res.json({
      data: integrations
    });
  } catch (error) {
    next(error);
  }
};

// Get a specific integration by ID
const getIntegrationById = async (req, res, next) => {
  try {
    const integration = await Integration.findOne({ id: req.params.id });
    
    if (!integration) {
      const error = new Error('Integration not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    res.json({
      data: integration
    });
  } catch (error) {
    next(error);
  }
};

// Create a new integration
const createIntegration = async (req, res, next) => {
  try {
    // Check if integration with the same ID already exists
    const existingIntegration = await Integration.findOne({ id: req.body.id });
    
    if (existingIntegration) {
      const error = new Error(`Integration with ID '${req.body.id}' already exists`);
      error.name = 'ConflictError';
      throw error;
    }
    
    const integration = new Integration(req.body);
    await integration.save();
    
    res.status(201).json({
      data: integration,
      message: 'Integration created successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Update an integration
const updateIntegration = async (req, res, next) => {
  try {
    const integration = await Integration.findOne({ id: req.params.id });
    
    if (!integration) {
      const error = new Error('Integration not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    // Update only the fields that are provided in the request body
    Object.keys(req.body).forEach(key => {
      // Don't allow changing the ID
      if (key !== 'id') {
        integration[key] = req.body[key];
      }
    });
    
    await integration.save();
    
    res.json({
      data: integration,
      message: 'Integration updated successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Delete an integration
const deleteIntegration = async (req, res, next) => {
  try {
    const integration = await Integration.findOne({ id: req.params.id });
    
    if (!integration) {
      const error = new Error('Integration not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    await integration.remove();
    
    res.json({
      message: 'Integration deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Configure an integration
const configureIntegration = async (req, res, next) => {
  try {
    const integration = await Integration.findOne({ id: req.params.id });
    
    if (!integration) {
      const error = new Error('Integration not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    // In a real implementation, this would validate the configuration against the schema
    integration.config = req.body;
    integration.status = 'active';
    
    await integration.save();
    
    res.json({
      data: integration,
      message: 'Integration configured successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Execute an integration action
const executeIntegrationAction = async (req, res, next) => {
  try {
    const integration = await Integration.findOne({ id: req.params.id });
    
    if (!integration) {
      const error = new Error('Integration not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    const action = req.params.action;
    
    if (!integration.capabilities.includes(action)) {
      const error = new Error(`Integration does not support action '${action}'`);
      error.name = 'ValidationError';
      throw error;
    }
    
    if (integration.status !== 'active') {
      const error = new Error('Integration is not active');
      error.name = 'ValidationError';
      throw error;
    }
    
    // In a real implementation, this would execute the action on the external system
    // For now, we'll just return a mock response
    
    // Update metrics
    integration.metrics.totalRequests += 1;
    integration.metrics.successfulRequests += 1;
    
    // Calculate new average response time
    const responseTime = Math.floor(Math.random() * 500) + 100; // Random response time between 100ms and 600ms
    const totalResponseTime = integration.metrics.averageResponseTime * (integration.metrics.totalRequests - 1) + responseTime;
    integration.metrics.averageResponseTime = totalResponseTime / integration.metrics.totalRequests;
    
    await integration.save();
    
    let responseData;
    
    switch (action) {
      case 'data-export':
        responseData = {
          exportId: `export-${Date.now()}`,
          exportTimestamp: new Date(),
          dataCategories: req.body.dataCategories || [],
          data: {
            // Mock data based on the integration type
            [integration.type]: [
              { id: '1', name: 'Sample Data 1' },
              { id: '2', name: 'Sample Data 2' }
            ]
          }
        };
        break;
      
      case 'data-deletion':
        responseData = {
          deletionId: `deletion-${Date.now()}`,
          deletionTimestamp: new Date(),
          dataCategories: req.body.dataCategories || [],
          status: 'completed',
          affectedRecords: Math.floor(Math.random() * 10) + 1 // Random number between 1 and 10
        };
        break;
      
      case 'data-update':
        responseData = {
          updateId: `update-${Date.now()}`,
          updateTimestamp: new Date(),
          status: 'completed',
          updatedFields: Object.keys(req.body.updates || {})
        };
        break;
      
      case 'notifications':
        responseData = {
          notificationId: `notification-${Date.now()}`,
          notificationTimestamp: new Date(),
          status: 'sent',
          channel: req.body.channel || 'default'
        };
        break;
      
      default:
        responseData = {
          actionId: `action-${Date.now()}`,
          actionTimestamp: new Date(),
          status: 'completed'
        };
    }
    
    res.json({
      data: responseData,
      message: `Integration action '${action}' executed successfully`
    });
  } catch (error) {
    next(error);
  }
};

// Check integration health
const checkIntegrationHealth = async (req, res, next) => {
  try {
    const integration = await Integration.findOne({ id: req.params.id });
    
    if (!integration) {
      const error = new Error('Integration not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    // In a real implementation, this would check the health of the integration
    // For now, we'll just return a mock health status
    
    const isHealthy = Math.random() > 0.1; // 90% chance of being healthy
    
    integration.lastChecked = new Date();
    
    if (!isHealthy) {
      integration.status = 'error';
      integration.lastError = {
        message: 'Connection timeout',
        timestamp: new Date(),
        details: {
          errorCode: 'TIMEOUT',
          endpoint: `https://api.${integration.provider.toLowerCase()}.com`
        }
      };
    } else {
      integration.status = 'active';
    }
    
    await integration.save();
    
    res.json({
      data: {
        status: isHealthy ? 'healthy' : 'unhealthy',
        lastChecked: integration.lastChecked,
        responseTime: Math.floor(Math.random() * 500) + 100, // Random response time between 100ms and 600ms
        issues: isHealthy ? [] : [
          {
            type: 'connection',
            message: 'Connection timeout',
            timestamp: new Date()
          }
        ]
      },
      message: isHealthy ? 'Integration is healthy' : 'Integration is unhealthy'
    });
  } catch (error) {
    next(error);
  }
};

// Get integration logs
const getIntegrationLogs = async (req, res, next) => {
  try {
    const integration = await Integration.findOne({ id: req.params.id });
    
    if (!integration) {
      const error = new Error('Integration not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    // In a real implementation, this would retrieve logs from a database
    // For now, we'll just return mock logs
    
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    
    // Generate mock logs
    const logs = Array.from({ length: limit }, (_, i) => {
      const timestamp = new Date();
      timestamp.setHours(timestamp.getHours() - (page - 1) * limit - i);
      
      const actions = ['data-export', 'data-deletion', 'data-update', 'notifications'];
      const action = actions[Math.floor(Math.random() * actions.length)];
      
      const statuses = ['success', 'error'];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      
      return {
        id: `log-${Date.now() - i}`,
        integrationId: integration.id,
        action,
        status,
        timestamp,
        duration: Math.floor(Math.random() * 1000) + 100, // Random duration between 100ms and 1100ms
        requestId: `req-${Date.now() - i}`,
        userId: 'user-123',
        details: {
          parameters: {
            email: '<EMAIL>',
            dataCategories: ['contacts', 'accounts']
          },
          result: status === 'success' ? {
            contacts: 1,
            accounts: 2
          } : {
            error: 'Connection timeout'
          }
        }
      };
    });
    
    res.json({
      data: logs,
      pagination: {
        total: 100, // Mock total
        page,
        limit,
        pages: Math.ceil(100 / limit)
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get integration metrics
const getIntegrationMetrics = async (req, res, next) => {
  try {
    const integration = await Integration.findOne({ id: req.params.id });
    
    if (!integration) {
      const error = new Error('Integration not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    // In a real implementation, this would retrieve metrics from a database
    // For now, we'll just return mock metrics
    
    const period = req.query.period || 'last-7-days';
    
    // Generate mock metrics
    const metrics = {
      requests: {
        total: integration.metrics.totalRequests,
        success: integration.metrics.successfulRequests,
        error: integration.metrics.totalRequests - integration.metrics.successfulRequests,
        byAction: {
          'data-export': Math.floor(integration.metrics.totalRequests * 0.5),
          'data-deletion': Math.floor(integration.metrics.totalRequests * 0.3),
          'data-update': Math.floor(integration.metrics.totalRequests * 0.1),
          'notifications': Math.floor(integration.metrics.totalRequests * 0.1)
        }
      },
      performance: {
        averageResponseTime: integration.metrics.averageResponseTime,
        p95ResponseTime: integration.metrics.averageResponseTime * 1.5,
        p99ResponseTime: integration.metrics.averageResponseTime * 2
      },
      errors: {
        byType: {
          authentication: Math.floor((integration.metrics.totalRequests - integration.metrics.successfulRequests) * 0.4),
          timeout: Math.floor((integration.metrics.totalRequests - integration.metrics.successfulRequests) * 0.3),
          validation: Math.floor((integration.metrics.totalRequests - integration.metrics.successfulRequests) * 0.2),
          other: Math.floor((integration.metrics.totalRequests - integration.metrics.successfulRequests) * 0.1)
        }
      }
    };
    
    res.json({
      data: metrics
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllIntegrations,
  getIntegrationById,
  createIntegration,
  updateIntegration,
  deleteIntegration,
  configureIntegration,
  executeIntegrationAction,
  checkIntegrationHealth,
  getIntegrationLogs,
  getIntegrationMetrics
};
