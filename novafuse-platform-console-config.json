{"novafuse_platform_console": {"name": "NovaFuse Platform Console", "version": "1.0.0", "description": "NovaFuse Coherence Operating System Control Panel", "framework": "React + Tailwind + Next.js + GraphQL", "source_mapping": {"original": "chaeonix-divine-dashboard", "rebranded": "novafuse-platform-console"}}, "rebranding_map": {"application_name": {"from": "CHAEONIX Divine Dashboard", "to": "NovaFuse Platform Console"}, "component_names": {"CDAIEIntelligenceGrid": "NovaFuseIntelligenceGrid", "TriMarketAllocation": "NovaFuseMarketAllocation", "CoherenceFlowMap": "NovaFuseCoherenceMap", "DivineEngineStatus": "NovaModuleStatus", "ChaeonixMetrics": "NovaFuseMetrics"}, "terminology": {"Divine Intelligence": "NovaFuse Intelligence", "CHAEONIX Engine": "NovaCore Engine", "Divine Dashboard": "Platform Console", "Engine Status": "Module Status", "Divine Metrics": "Coherence Metrics"}}, "nova_core_modules": {"NEPI": {"display_name": "NovaCore Intelligence", "description": "Predictive Intelligence Engine", "accuracy": "97.83%", "status": "OPERATIONAL"}, "NEFC": {"display_name": "NovaCore Finance", "description": "Financial Coherence Engine", "returns": "99.4%", "status": "OPERATIONAL"}, "NERS": {"display_name": "NovaCore Resonance", "description": "Emotional Coherence Engine", "coherence": "High", "status": "OPERATIONAL"}, "NERE": {"display_name": "NovaCore Enhancement", "description": "Reality Enhancement Engine", "enhancement": "Active", "status": "OPERATIONAL"}, "NECE": {"display_name": "NovaCore Chemistry", "description": "Chemical Coherence Engine", "optimization": "Enabled", "status": "OPERATIONAL"}}, "ui_configuration": {"theme": {"primary_color": "#00D4FF", "secondary_color": "#7C3AED", "accent_color": "#F59E0B", "background": "gradient-to-br from-slate-900 via-purple-900 to-slate-900"}, "layout": {"header": "NovaFuse Platform Console", "navigation": ["Dashboard", "NovaC<PERSON>", "Coherence Metrics", "System Status", "Settings"], "widgets": ["NovaFuseIntelligenceGrid", "NovaFuseCoherenceMap", "NovaModuleStatus", "CoherenceMetrics"]}}, "api_endpoints": {"base_url": "http://localhost:8000", "endpoints": {"modules": "/api/nova-modules", "coherence": "/api/coherence-metrics", "status": "/api/system-status", "websocket": "ws://localhost:8000/ws"}}, "features": {"real_time_updates": true, "websocket_connection": true, "coherence_visualization": true, "module_management": true, "system_monitoring": true, "responsive_design": true}}