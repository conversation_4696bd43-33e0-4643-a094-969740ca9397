{"metadata": {"name": "Salesforce", "version": "1.0.0", "category": "CRM & Business Applications", "description": "Connect to Salesforce for user management, data protection, and compliance monitoring", "author": "NovaGRC", "tags": ["salesforce", "crm", "business", "compliance", "data protection"], "created": "2025-01-01T00:00:00Z", "updated": "2025-01-01T00:00:00Z", "icon": "https://www.salesforce.com/content/dam/web/en_us/www/images/home/<USER>"}, "authentication": {"type": "OAUTH2", "fields": {"clientId": {"type": "string", "description": "Salesforce Connected App Consumer Key", "required": true}, "clientSecret": {"type": "string", "description": "Salesforce Connected App Consumer Secret", "required": true, "sensitive": true}, "username": {"type": "string", "description": "Salesforce Username", "required": true}, "password": {"type": "string", "description": "Salesforce Password (with Security Token if required)", "required": true, "sensitive": true}}, "oauth2Config": {"tokenUrl": "https://login.salesforce.com/services/oauth2/token", "grantType": "password", "additionalParams": {"username": "{username}", "password": "{password}"}}, "testConnection": {"endpoint": "/services/data/v56.0/sobjects", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "{instanceUrl}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "rateLimit": {"requests": 100, "period": "1m"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "getUsers", "name": "Get Users", "description": "Get users from Salesforce", "path": "/services/data/v56.0/query", "method": "GET", "parameters": {"query": {"q": {"type": "string", "description": "SOQL query", "default": "SELECT Id, Username, Email, Name, Profile.Name, IsActive FROM User"}}}, "pagination": {"type": "nextUrl", "parameters": {"nextUrl": "nextRecordsUrl"}}, "response": {"successCode": 200, "schema": {"totalSize": "integer", "done": "boolean", "records": "array", "nextRecordsUrl": "string"}}}, {"id": "getProfiles", "name": "Get Profiles", "description": "Get profiles from Salesforce", "path": "/services/data/v56.0/query", "method": "GET", "parameters": {"query": {"q": {"type": "string", "description": "SOQL query", "default": "SELECT Id, Name, Description, UserType, PermissionsModifyAllData FROM Profile"}}}, "response": {"successCode": 200, "schema": {"totalSize": "integer", "done": "boolean", "records": "array"}}}, {"id": "getPermissionSets", "name": "Get Permission Sets", "description": "Get permission sets from Salesforce", "path": "/services/data/v56.0/query", "method": "GET", "parameters": {"query": {"q": {"type": "string", "description": "SOQL query", "default": "SELECT Id, Name, Description, IsOwnedByProfile, Profile.Name FROM PermissionSet"}}}, "pagination": {"type": "nextUrl", "parameters": {"nextUrl": "nextRecordsUrl"}}, "response": {"successCode": 200, "schema": {"totalSize": "integer", "done": "boolean", "records": "array", "nextRecordsUrl": "string"}}}, {"id": "getLoginHistory", "name": "Get Login History", "description": "Get login history from Salesforce", "path": "/services/data/v56.0/query", "method": "GET", "parameters": {"query": {"q": {"type": "string", "description": "SOQL query", "default": "SELECT Id, UserId, LoginTime, SourceIp, Status, LoginType FROM LoginHistory ORDER BY LoginTime DESC LIMIT 100"}}}, "response": {"successCode": 200, "schema": {"totalSize": "integer", "done": "boolean", "records": "array"}}}, {"id": "getSetupAuditTrail", "name": "Get Setup Audit Trail", "description": "Get setup audit trail from Salesforce", "path": "/services/data/v56.0/query", "method": "GET", "parameters": {"query": {"q": {"type": "string", "description": "SOQL query", "default": "SELECT Id, Action, CreatedBy.Name, CreatedDate, Display, Section FROM SetupAuditTrail ORDER BY CreatedDate DESC LIMIT 100"}}}, "response": {"successCode": 200, "schema": {"totalSize": "integer", "done": "boolean", "records": "array"}}}, {"id": "getDataExports", "name": "Get Data Exports", "description": "Get data export history from Salesforce", "path": "/services/data/v56.0/query", "method": "GET", "parameters": {"query": {"q": {"type": "string", "description": "SOQL query", "default": "SELECT Id, CreatedById, CreatedDate, Status, Operation FROM ContentDocumentLink WHERE LinkedEntityId IN (SELECT Id FROM User) ORDER BY CreatedDate DESC LIMIT 100"}}}, "response": {"successCode": 200, "schema": {"totalSize": "integer", "done": "boolean", "records": "array"}}}], "mappings": [{"sourceEndpoint": "getUsers", "targetSystem": "NovaGRC", "targetEntity": "Users", "transformations": [{"source": "$.records[*].Id", "target": "userId", "transform": "identity"}, {"source": "$.records[*].Username", "target": "username", "transform": "identity"}, {"source": "$.records[*].Email", "target": "email", "transform": "identity"}, {"source": "$.records[*].Name", "target": "name", "transform": "identity"}, {"source": "$.records[*].Profile.Name", "target": "role", "transform": "identity"}, {"source": "$.records[*].IsActive", "target": "status", "transform": "mapUserStatus"}]}, {"sourceEndpoint": "getLoginHistory", "targetSystem": "NovaGRC", "targetEntity": "ActivityLogs", "transformations": [{"source": "$.records[*].Id", "target": "eventId", "transform": "identity"}, {"source": "$.records[*].LoginTime", "target": "timestamp", "transform": "identity"}, {"source": "$.records[*].UserId", "target": "userId", "transform": "identity"}, {"source": "$.records[*].SourceIp", "target": "sourceIp", "transform": "identity"}, {"source": "$.records[*].Status", "target": "status", "transform": "identity"}, {"source": "$.records[*].LoginType", "target": "eventType", "transform": "identity"}]}, {"sourceEndpoint": "getSetupAuditTrail", "targetSystem": "NovaGRC", "targetEntity": "ConfigurationChanges", "transformations": [{"source": "$.records[*].Id", "target": "changeId", "transform": "identity"}, {"source": "$.records[*].CreatedDate", "target": "timestamp", "transform": "identity"}, {"source": "$.records[*].CreatedBy.Name", "target": "actor", "transform": "identity"}, {"source": "$.records[*].Action", "target": "action", "transform": "identity"}, {"source": "$.records[*].Section", "target": "category", "transform": "identity"}, {"source": "$.records[*].Display", "target": "description", "transform": "identity"}]}], "events": {"polling": [{"endpoint": "getUsers", "interval": "1h", "condition": "hasUserChanges"}, {"endpoint": "getLoginHistory", "interval": "15m", "condition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"endpoint": "getSetupAuditTrail", "interval": "30m", "condition": "hasConfigChanges"}]}}