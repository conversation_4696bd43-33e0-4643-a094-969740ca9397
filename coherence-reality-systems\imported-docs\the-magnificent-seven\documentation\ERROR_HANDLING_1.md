# NovaConnect Error Handling

This document describes the error handling infrastructure for NovaConnect UAC, which provides comprehensive error handling, recovery, and resilience capabilities.

## Overview

NovaConnect's error handling infrastructure consists of the following components:

1. **Error Handling Service**: Centralized service for handling errors
2. **Error Handling Middleware**: Express middleware for handling errors in API requests
3. **Connector Error Handler**: Utility for handling errors in API connectors
4. **Database Error Handler**: Utility for handling errors in database operations
5. **Validation Error Handler**: Utility for handling errors in data validation

## Error Handling Service

The Error Handling Service provides the following features:

### Error Types

The service supports the following error types:

- `validation_error`: Data validation errors
- `authentication_error`: Authentication failures
- `authorization_error`: Authorization failures
- `not_found_error`: Resource not found
- `timeout_error`: Operation timeout
- `rate_limit_error`: Rate limit exceeded
- `connector_error`: API connector errors
- `database_error`: Database operation errors
- `network_error`: Network communication errors
- `internal_error`: Internal server errors

### Recovery Strategies

The service implements the following recovery strategies:

- **Retry**: Automatically retry failed operations with exponential backoff
- **Circuit Breaker**: Prevent cascading failures by failing fast
- **Fallback**: Provide alternative functionality when primary function fails
- **Timeout**: Limit the duration of operations
- **Bulkhead**: Isolate failures to prevent system-wide impact

### Usage

```javascript
const errorHandlingService = require('../services/ErrorHandlingService');

// Handle an error
const errorResponse = errorHandlingService.handleError(error, context);

// Create a function with retry logic
const retryableFunction = errorHandlingService.withRetry(async () => {
  // Function implementation
}, {
  maxRetries: 3,
  initialDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  jitter: true
});

// Create a function with circuit breaker logic
const protectedFunction = errorHandlingService.withCircuitBreaker(async () => {
  // Function implementation
}, {
  resource: 'api',
  failureThreshold: 5,
  resetTimeout: 30000
});

// Create a function with timeout logic
const timedFunction = errorHandlingService.withTimeout(async () => {
  // Function implementation
}, {
  timeoutMs: 5000
});

// Create a function with fallback logic
const fallbackFunction = errorHandlingService.withFallback(
  async () => {
    // Primary function implementation
  },
  async () => {
    // Fallback function implementation
  }
);

// Create a function with bulkhead logic
const isolatedFunction = errorHandlingService.withBulkhead(async () => {
  // Function implementation
}, {
  resource: 'api',
  limit: 10
});
```

## Error Handling Middleware

The Error Handling Middleware provides the following features:

### Middleware Components

- `errorHandler`: Express error handler middleware
- `notFoundHandler`: Express 404 handler middleware
- `asyncHandler`: Wrapper for async route handlers
- `retryHandler`: Middleware for retry functionality
- `circuitBreakerHandler`: Middleware for circuit breaker functionality
- `timeoutHandler`: Middleware for timeout functionality
- `bulkheadHandler`: Middleware for bulkhead functionality

### Usage

```javascript
const { errorHandler, notFoundHandler, asyncHandler, retryHandler, circuitBreakerHandler, timeoutHandler, bulkheadHandler } = require('../middleware/errorHandlingMiddleware');

// Apply circuit breaker to API routes
app.use('/api', circuitBreakerHandler({
  resource: 'api',
  failureThreshold: 5,
  resetTimeout: 30000
}));

// Apply timeout to API routes
app.use('/api', timeoutHandler({
  timeoutMs: 30000
}));

// Apply retry handler to API routes
app.use('/api', retryHandler({
  retryAfter: 1
}));

// Wrap async route handlers
app.get('/api/resource', asyncHandler(async (req, res) => {
  // Route handler implementation
}));

// 404 handler
app.use(notFoundHandler);

// Error handler
app.use(errorHandler);
```

## Connector Error Handler

The Connector Error Handler provides the following features:

### Functions

- `handleConnectorError`: Handle errors from API connectors
- `createConnectorError`: Create connector-specific errors
- `mapErrorCodeToType`: Map connector-specific error codes to standard error types
- `createRetryPolicy`: Create retry policies for connectors
- `createCircuitBreaker`: Create circuit breakers for connectors
- `withConnectorErrorHandling`: Wrap connector functions with error handling

### Usage

```javascript
const connectorErrorHandler = require('../utils/connectorErrorHandler');

// Create a connector function with error handling
const connectorFunction = connectorErrorHandler.withConnectorErrorHandling(async () => {
  // Connector implementation
}, {
  connector: 'aws',
  endpoint: 'ec2',
  operation: 'describeInstances',
  retry: true,
  circuitBreaker: true,
  timeout: true
});

// Handle connector error
const errorResponse = connectorErrorHandler.handleConnectorError(error, {
  connector: 'aws',
  endpoint: 'ec2',
  operation: 'describeInstances'
});
```

## Database Error Handler

The Database Error Handler provides the following features:

### Functions

- `handleDatabaseError`: Handle errors from database operations
- `createDatabaseError`: Create database-specific errors
- `mapErrorCodeToType`: Map database-specific error codes to standard error types
- `createRetryPolicy`: Create retry policies for database operations
- `createCircuitBreaker`: Create circuit breakers for database operations
- `withDatabaseErrorHandling`: Wrap database functions with error handling
- `mongooseErrorHandlingPlugin`: Mongoose plugin for error handling

### Usage

```javascript
const databaseErrorHandler = require('../utils/databaseErrorHandler');

// Create a database function with error handling
const databaseFunction = databaseErrorHandler.withDatabaseErrorHandling(async () => {
  // Database operation implementation
}, {
  model: 'User',
  operation: 'findById',
  retry: true,
  circuitBreaker: true,
  timeout: true
});

// Handle database error
const errorResponse = databaseErrorHandler.handleDatabaseError(error, {
  model: 'User',
  operation: 'findById',
  query: { _id: '123' }
});

// Apply Mongoose plugin
userSchema.plugin(databaseErrorHandler.mongooseErrorHandlingPlugin, {
  modelName: 'User'
});
```

## Validation Error Handler

The Validation Error Handler provides the following features:

### Functions

- `handleValidationError`: Handle errors from data validation
- `createValidationError`: Create validation-specific errors
- `validateSchema`: Validate data against a JSON schema
- `formatValidationErrors`: Format validation errors
- `validationMiddleware`: Express middleware for schema validation
- `withValidationErrorHandling`: Wrap validation functions with error handling

### Usage

```javascript
const validationErrorHandler = require('../utils/validationErrorHandler');

// Define a schema
const schema = {
  type: 'object',
  properties: {
    name: { type: 'string', minLength: 1 },
    email: { type: 'string', format: 'email' },
    age: { type: 'integer', minimum: 18 }
  },
  required: ['name', 'email']
};

// Validate data
const result = validationErrorHandler.validateSchema(schema, data);

if (!result.valid) {
  console.log(result.errors);
}

// Create validation middleware
app.post('/api/users', validationErrorHandler.validationMiddleware(schema), (req, res) => {
  // Route handler implementation
});

// Create a validation function with error handling
const validateUser = validationErrorHandler.withValidationErrorHandling((user) => {
  // Validation implementation
}, {
  path: 'user'
});
```

## Error Response Format

Error responses have the following format:

```json
{
  "error": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "type": "validation_error",
    "message": "Invalid email format",
    "status": 400,
    "validation": [
      {
        "path": "email",
        "property": "email",
        "message": "email must be a valid email",
        "keyword": "format",
        "params": {
          "format": "email"
        },
        "schemaPath": "#/properties/email/format"
      }
    ]
  },
  "recovery": {
    "strategy": "retry",
    "retryCount": 1,
    "maxRetries": 3,
    "delay": 2000,
    "retryAfter": "2023-01-01T00:00:02.000Z"
  }
}
```

## Configuration

Error handling can be configured using environment variables:

```
# Error Handling
ERROR_HANDLING_ENABLED=true
SENTRY_ENABLED=true
SENTRY_DSN=https://your-sentry-dsn
SENTRY_DSN_SECRET_NAME=sentry-dsn

# Retry
DEFAULT_MAX_RETRIES=3
DEFAULT_INITIAL_DELAY=1000
DEFAULT_MAX_DELAY=10000
DEFAULT_BACKOFF_FACTOR=2
DEFAULT_JITTER=true

# Circuit Breaker
DEFAULT_FAILURE_THRESHOLD=5
DEFAULT_RESET_TIMEOUT=30000

# Timeout
DEFAULT_TIMEOUT_MS=30000

# Bulkhead
DEFAULT_BULKHEAD_LIMIT=10
```

## Integration with Google Cloud Marketplace

For Google Cloud Marketplace deployment, the error handling infrastructure automatically integrates with Google Cloud Operations, providing:

1. **Error Tracking**: Errors are tracked in Google Cloud Error Reporting
2. **Error Analysis**: Errors can be analyzed in Google Cloud Logging
3. **Error Alerting**: Alerts can be configured for critical errors
4. **Error Recovery**: Recovery strategies are automatically applied

This integration ensures that NovaConnect meets the reliability requirements for Google Cloud Marketplace.
