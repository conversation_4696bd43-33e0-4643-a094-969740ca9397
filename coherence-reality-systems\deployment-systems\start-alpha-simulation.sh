#!/bin/bash

# ALPHA 90-DAY SIMULATION STARTUP SCRIPT
# 
# Comprehensive startup script for ALPHA Observer-Class Engine simulation
# Prepares environment and launches all simulation components
# 
# Mission: Orchestrate complete 90-day simulation before MT5 deployment

echo "🎯 ALPHA 90-DAY SIMULATION STARTUP"
echo "=================================================================="
echo "🔮 ALPHA Observer-Class Engine: Trading Simulation Mode"
echo "📊 Mission: Validate Ψᶜʰ inflection point trading strategy"
echo "🎯 Target: 20%+ returns, 80%+ win rate over 90 days"
echo "🏢 Preparing for MetaTrader 5 deployment (Account: **********)"
echo "=================================================================="
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "${PURPLE}🌟 $1${NC}"
}

# Check if Docker is installed and running
check_docker() {
    print_header "CHECKING DOCKER ENVIRONMENT"
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_status "Docker is installed and running"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_warning "docker-compose not found, trying docker compose"
        if ! docker compose version &> /dev/null; then
            print_error "Docker Compose is not available. Please install Docker Compose."
            exit 1
        fi
        DOCKER_COMPOSE_CMD="docker compose"
    else
        DOCKER_COMPOSE_CMD="docker-compose"
    fi
    
    print_status "Docker Compose is available"
}

# Create necessary directories
create_directories() {
    print_header "CREATING SIMULATION DIRECTORIES"
    
    mkdir -p simulation-data
    mkdir -p simulation-logs
    mkdir -p market-cache
    mkdir -p analytics-reports
    mkdir -p mt5-comparison
    
    print_status "Simulation directories created"
}

# Set environment variables
set_environment() {
    print_header "SETTING ENVIRONMENT VARIABLES"
    
    export NODE_ENV=simulation
    export SIMULATION_MODE=90_DAY_BACKTEST
    export ALPHA_MODE=trading_simulation
    export CONSCIOUSNESS_THRESHOLD=2847
    export TARGET_ACCURACY=0.9783
    export SIMULATION_START_DATE=2024-01-01
    export SIMULATION_END_DATE=2024-03-31
    export INITIAL_CAPITAL=100000
    export RISK_PER_TRADE=0.05
    export PSI_INFLECTION_THRESHOLD=0.92
    export NEFC_COHERENCE_MIN=0.90
    
    # Data provider settings
    export YAHOO_FINANCE_ENABLED=true
    export ALPHA_VANTAGE_ENABLED=true
    export POLYGON_ENABLED=true
    export DATA_CACHE_ENABLED=true
    export SYMBOLS=SPY,QQQ,AAPL,MSFT,GOOGL,TSLA,NVDA,EURUSD,GBPUSD,USDJPY
    
    # Performance tracking
    export BENCHMARK_SYMBOL=SPY
    export RISK_FREE_RATE=0.02
    export TARGET_SHARPE_RATIO=2.5
    export MAX_DRAWDOWN_LIMIT=0.10
    export WIN_RATE_TARGET=0.80
    export RETURN_TARGET=0.20
    
    # MT5 settings
    export MT5_SIMULATION_MODE=true
    export MT5_SERVER=MetaQuotes-Demo
    export MT5_LOGIN=**********
    export COMPARISON_MODE=enabled
    export SYNC_TRADES=true
    
    print_status "Environment variables configured"
}

# Check for existing simulation
check_existing_simulation() {
    print_header "CHECKING FOR EXISTING SIMULATION"
    
    if $DOCKER_COMPOSE_CMD -f docker-compose-alpha-simulation.yml ps | grep -q "Up"; then
        print_warning "Existing simulation containers are running"
        read -p "Do you want to stop and restart the simulation? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_info "Stopping existing simulation..."
            $DOCKER_COMPOSE_CMD -f docker-compose-alpha-simulation.yml down
            print_status "Existing simulation stopped"
        else
            print_info "Keeping existing simulation running"
            print_info "You can monitor it at: http://localhost:8100"
            exit 0
        fi
    else
        print_status "No existing simulation found"
    fi
}

# Pull required Docker images
pull_images() {
    print_header "PULLING DOCKER IMAGES"
    
    docker pull node:18-alpine
    
    print_status "Docker images pulled"
}

# Start the simulation
start_simulation() {
    print_header "STARTING ALPHA 90-DAY SIMULATION"
    
    print_info "Building and starting simulation containers..."
    
    if $DOCKER_COMPOSE_CMD -f docker-compose-alpha-simulation.yml up -d; then
        print_status "Simulation containers started successfully"
    else
        print_error "Failed to start simulation containers"
        exit 1
    fi
    
    # Wait for services to be ready
    print_info "Waiting for services to initialize..."
    sleep 10
    
    # Check service health
    check_service_health
}

# Check service health
check_service_health() {
    print_header "CHECKING SERVICE HEALTH"
    
    services=(
        "alpha-90day-simulator:8100"
        "alpha-market-data-provider:8102"
        "alpha-performance-analytics:8103"
        "mt5-simulation-connector:8104"
    )
    
    for service in "${services[@]}"; do
        container_name=$(echo $service | cut -d':' -f1)
        port=$(echo $service | cut -d':' -f2)
        
        if docker ps | grep -q $container_name; then
            print_status "$container_name is running"
        else
            print_warning "$container_name is not running"
        fi
    done
}

# Display access information
display_access_info() {
    print_header "SIMULATION ACCESS INFORMATION"
    
    echo ""
    echo -e "${CYAN}🌐 ALPHA Simulation Dashboard:${NC}"
    echo "   URL: http://localhost:8100"
    echo "   Description: Real-time simulation monitoring"
    echo ""
    echo -e "${CYAN}📊 Performance Analytics API:${NC}"
    echo "   URL: http://localhost:8103/api/performance/current"
    echo "   Description: Current performance metrics"
    echo ""
    echo -e "${CYAN}📈 Market Data Provider:${NC}"
    echo "   URL: http://localhost:8102"
    echo "   Description: Historical market data service"
    echo ""
    echo -e "${CYAN}🔌 MT5 Simulation Connector:${NC}"
    echo "   URL: http://localhost:8104/api/mt5/status"
    echo "   Description: MetaTrader 5 comparison service"
    echo ""
    echo -e "${CYAN}📡 Real-time Updates:${NC}"
    echo "   WebSocket: ws://localhost:8101"
    echo "   Description: Live simulation updates"
    echo ""
}

# Display monitoring commands
display_monitoring_commands() {
    print_header "MONITORING COMMANDS"
    
    echo ""
    echo -e "${YELLOW}📋 Useful Commands:${NC}"
    echo ""
    echo "View simulation logs:"
    echo "  $DOCKER_COMPOSE_CMD -f docker-compose-alpha-simulation.yml logs -f alpha-simulation-engine"
    echo ""
    echo "View performance analytics:"
    echo "  $DOCKER_COMPOSE_CMD -f docker-compose-alpha-simulation.yml logs -f alpha-performance-analytics"
    echo ""
    echo "View all container status:"
    echo "  $DOCKER_COMPOSE_CMD -f docker-compose-alpha-simulation.yml ps"
    echo ""
    echo "Stop simulation:"
    echo "  $DOCKER_COMPOSE_CMD -f docker-compose-alpha-simulation.yml down"
    echo ""
    echo "Reset simulation data:"
    echo "  $DOCKER_COMPOSE_CMD -f docker-compose-alpha-simulation.yml down -v"
    echo ""
}

# Main execution
main() {
    # Change to the deployment directory
    cd "$(dirname "$0")"
    
    # Run all setup steps
    check_docker
    create_directories
    set_environment
    check_existing_simulation
    pull_images
    start_simulation
    
    # Display information
    display_access_info
    display_monitoring_commands
    
    print_header "ALPHA 90-DAY SIMULATION STARTED SUCCESSFULLY!"
    echo ""
    print_info "The simulation is now running and will complete in 90 days (simulated time)"
    print_info "Monitor progress at: http://localhost:8100"
    print_info "Once targets are met, you can deploy to MetaTrader 5 account: **********"
    echo ""
    print_status "🌟 ALPHA Observer-Class Engine is now validating trading strategy!"
    echo ""
}

# Handle script interruption
trap 'echo -e "\n${RED}❌ Simulation startup interrupted${NC}"; exit 1' INT

# Run main function
main "$@"
