/**
 * Unit tests for the Certifications & Accreditation Connector
 */

const axios = require('axios');
const CertificationsAccreditationConnector = require('../../../../connector/implementations/certifications-accreditation');

// Mock axios
jest.mock('axios');

// Mock logger
jest.mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));

describe('CertificationsAccreditationConnector', () => {
  let connector;
  let mockConfig;
  let mockCredentials;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock config and credentials
    mockConfig = {
      baseUrl: 'https://api.test.com'
    };
    
    mockCredentials = {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      redirectUri: 'https://test-redirect.com'
    };
    
    // Create connector instance
    connector = new CertificationsAccreditationConnector(mockConfig, mockCredentials);
  });
  
  describe('constructor', () => {
    it('should initialize with provided config and credentials', () => {
      expect(connector.config).toEqual(mockConfig);
      expect(connector.credentials).toEqual(mockCredentials);
      expect(connector.baseUrl).toBe(mockConfig.baseUrl);
    });
    
    it('should use default baseUrl if not provided', () => {
      const connectorWithDefaults = new CertificationsAccreditationConnector();
      expect(connectorWithDefaults.baseUrl).toBe('https://api.example.com');
    });
  });
  
  describe('initialize', () => {
    it('should authenticate if credentials are provided', async () => {
      // Mock authenticate method
      connector.authenticate = jest.fn().mockResolvedValue();
      
      await connector.initialize();
      
      expect(connector.authenticate).toHaveBeenCalled();
    });
    
    it('should not authenticate if credentials are not provided', async () => {
      // Create connector without credentials
      const connectorWithoutCredentials = new CertificationsAccreditationConnector(mockConfig, {});
      
      // Mock authenticate method
      connectorWithoutCredentials.authenticate = jest.fn().mockResolvedValue();
      
      await connectorWithoutCredentials.initialize();
      
      expect(connectorWithoutCredentials.authenticate).not.toHaveBeenCalled();
    });
  });
  
  describe('authenticate', () => {
    it('should make a POST request to the token endpoint', async () => {
      // Mock axios post response
      axios.post.mockResolvedValue({
        data: {
          access_token: 'test-access-token',
          expires_in: 3600
        }
      });
      
      await connector.authenticate();
      
      expect(axios.post).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/oauth2/token`,
        {
          grant_type: 'client_credentials',
          client_id: mockCredentials.clientId,
          client_secret: mockCredentials.clientSecret,
          scope: 'read:certifications write:certifications read:assessments write:assessments read:evidence write:evidence'
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(connector.accessToken).toBe('test-access-token');
      expect(connector.tokenExpiry).toBeDefined();
    });
    
    it('should throw an error if authentication fails', async () => {
      // Mock axios post error
      const errorMessage = 'Authentication failed';
      axios.post.mockRejectedValue(new Error(errorMessage));
      
      await expect(connector.authenticate()).rejects.toThrow(`Authentication failed: ${errorMessage}`);
    });
  });
  
  describe('getAuthHeaders', () => {
    it('should return authorization headers with access token', async () => {
      // Set access token and expiry
      connector.accessToken = 'test-access-token';
      connector.tokenExpiry = Date.now() + 3600000; // 1 hour from now
      
      const headers = await connector.getAuthHeaders();
      
      expect(headers).toEqual({
        'Authorization': 'Bearer test-access-token'
      });
    });
    
    it('should authenticate if access token is not set', async () => {
      // Mock authenticate method
      connector.authenticate = jest.fn().mockImplementation(() => {
        connector.accessToken = 'new-access-token';
        connector.tokenExpiry = Date.now() + 3600000;
      });
      
      const headers = await connector.getAuthHeaders();
      
      expect(connector.authenticate).toHaveBeenCalled();
      expect(headers).toEqual({
        'Authorization': 'Bearer new-access-token'
      });
    });
  });
  
  describe('listCertifications', () => {
    it('should make a GET request to the certifications endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get response
      const mockResponse = {
        data: {
          data: [
            { id: 'cert-1', name: 'ISO 27001' },
            { id: 'cert-2', name: 'SOC 2 Type II' }
          ],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const params = { status: 'active', limit: 50 };
      const result = await connector.listCertifications(params);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/certifications`,
        {
          params,
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if the request fails', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get error
      const errorMessage = 'Request failed';
      axios.get.mockRejectedValue(new Error(errorMessage));
      
      await expect(connector.listCertifications()).rejects.toThrow(`Error listing certifications: ${errorMessage}`);
    });
  });
  
  describe('getCertification', () => {
    it('should make a GET request to the specific certification endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'cert-123',
          name: 'ISO 27001',
          description: 'Information Security Management System certification'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const certificationId = 'cert-123';
      const result = await connector.getCertification(certificationId);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/certifications/${certificationId}`,
        {
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if certificationId is not provided', async () => {
      await expect(connector.getCertification()).rejects.toThrow('Certification ID is required');
    });
  });
  
  describe('listAssessments', () => {
    it('should make a GET request to the assessments endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get response
      const mockResponse = {
        data: {
          data: [
            { id: 'assess-1', name: 'ISO 27001 Annual Assessment' },
            { id: 'assess-2', name: 'SOC 2 Type II Audit' }
          ],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const params = { status: 'in_progress', certificationId: 'cert-123' };
      const result = await connector.listAssessments(params);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/assessments`,
        {
          params,
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
  });
  
  describe('getAssessment', () => {
    it('should make a GET request to the specific assessment endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'assess-123',
          name: 'ISO 27001 Annual Assessment',
          status: 'in_progress'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const assessmentId = 'assess-123';
      const result = await connector.getAssessment(assessmentId);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/assessments/${assessmentId}`,
        {
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if assessmentId is not provided', async () => {
      await expect(connector.getAssessment()).rejects.toThrow('Assessment ID is required');
    });
  });
  
  describe('listEvidence', () => {
    it('should make a GET request to the evidence endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get response
      const mockResponse = {
        data: {
          data: [
            { id: 'evidence-1', name: 'Information Security Policy Document' },
            { id: 'evidence-2', name: 'Risk Assessment Report' }
          ],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const params = { assessmentId: 'assess-123' };
      const result = await connector.listEvidence(params);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/evidence`,
        {
          params,
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
  });
  
  describe('getEvidence', () => {
    it('should make a GET request to the specific evidence endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'evidence-123',
          name: 'Information Security Policy Document',
          type: 'document'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const evidenceId = 'evidence-123';
      const result = await connector.getEvidence(evidenceId);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/evidence/${evidenceId}`,
        {
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if evidenceId is not provided', async () => {
      await expect(connector.getEvidence()).rejects.toThrow('Evidence ID is required');
    });
  });
});
