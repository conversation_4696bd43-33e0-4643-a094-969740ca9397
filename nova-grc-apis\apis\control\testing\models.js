/**
 * @swagger
 * components:
 *   schemas:
 *     Control:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the control
 *         title:
 *           type: string
 *           description: Title of the control
 *         description:
 *           type: string
 *           description: Description of the control
 *         type:
 *           type: string
 *           enum: [preventive, detective, corrective, directive]
 *           description: Type of control
 *         category:
 *           type: string
 *           enum: [administrative, technical, physical]
 *           description: Category of control
 *         status:
 *           type: string
 *           enum: [draft, implemented, under-review, approved, deprecated]
 *           description: Status of the control
 *         owner:
 *           type: string
 *           description: Owner of the control
 *         framework:
 *           type: string
 *           description: Compliance framework the control is associated with
 *         riskLevel:
 *           type: string
 *           enum: [critical, high, medium, low]
 *           description: Risk level the control addresses
 *         testFrequency:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, semi-annually, annually, as-needed]
 *           description: Frequency of control testing
 *         lastTestedDate:
 *           type: string
 *           format: date
 *           description: Date when the control was last tested
 *         nextTestDate:
 *           type: string
 *           format: date
 *           description: Date when the control is scheduled to be tested next
 *         testProcedure:
 *           type: string
 *           description: Procedure for testing the control
 *         testResults:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ControlTestResult'
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the control was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the control was last updated
 *       required:
 *         - id
 *         - title
 *         - type
 *         - category
 *         - status
 *         - owner
 *         - testFrequency
 *         - createdAt
 *         - updatedAt
 *     
 *     ControlInput:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Title of the control
 *         description:
 *           type: string
 *           description: Description of the control
 *         type:
 *           type: string
 *           enum: [preventive, detective, corrective, directive]
 *           description: Type of control
 *         category:
 *           type: string
 *           enum: [administrative, technical, physical]
 *           description: Category of control
 *         status:
 *           type: string
 *           enum: [draft, implemented, under-review, approved, deprecated]
 *           description: Status of the control
 *         owner:
 *           type: string
 *           description: Owner of the control
 *         framework:
 *           type: string
 *           description: Compliance framework the control is associated with
 *         riskLevel:
 *           type: string
 *           enum: [critical, high, medium, low]
 *           description: Risk level the control addresses
 *         testFrequency:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, semi-annually, annually, as-needed]
 *           description: Frequency of control testing
 *         testProcedure:
 *           type: string
 *           description: Procedure for testing the control
 *       required:
 *         - title
 *         - type
 *         - category
 *         - status
 *         - owner
 *         - testFrequency
 *     
 *     ControlTestResult:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the test result
 *         controlId:
 *           type: string
 *           description: ID of the control that was tested
 *         testDate:
 *           type: string
 *           format: date
 *           description: Date when the test was performed
 *         tester:
 *           type: string
 *           description: Person who performed the test
 *         result:
 *           type: string
 *           enum: [pass, fail, inconclusive, not-applicable]
 *           description: Result of the test
 *         evidence:
 *           type: string
 *           description: Evidence supporting the test result
 *         notes:
 *           type: string
 *           description: Additional notes about the test
 *         remediation:
 *           type: string
 *           description: Remediation steps if the test failed
 *         remediationDueDate:
 *           type: string
 *           format: date
 *           description: Due date for remediation if the test failed
 *         remediationStatus:
 *           type: string
 *           enum: [not-required, pending, in-progress, completed, verified]
 *           description: Status of remediation if the test failed
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the test result was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the test result was last updated
 *       required:
 *         - id
 *         - controlId
 *         - testDate
 *         - tester
 *         - result
 *         - createdAt
 *         - updatedAt
 *     
 *     ControlTestResultInput:
 *       type: object
 *       properties:
 *         testDate:
 *           type: string
 *           format: date
 *           description: Date when the test was performed
 *         tester:
 *           type: string
 *           description: Person who performed the test
 *         result:
 *           type: string
 *           enum: [pass, fail, inconclusive, not-applicable]
 *           description: Result of the test
 *         evidence:
 *           type: string
 *           description: Evidence supporting the test result
 *         notes:
 *           type: string
 *           description: Additional notes about the test
 *         remediation:
 *           type: string
 *           description: Remediation steps if the test failed
 *         remediationDueDate:
 *           type: string
 *           format: date
 *           description: Due date for remediation if the test failed
 *         remediationStatus:
 *           type: string
 *           enum: [not-required, pending, in-progress, completed, verified]
 *           description: Status of remediation if the test failed
 *       required:
 *         - testDate
 *         - tester
 *         - result
 */

// Sample controls
const controls = [
  {
    id: 'ctrl-001',
    title: 'Password Complexity Requirements',
    description: 'Passwords must be at least 12 characters long and include uppercase, lowercase, numbers, and special characters',
    type: 'preventive',
    category: 'technical',
    status: 'implemented',
    owner: 'Information Security Team',
    framework: 'NIST 800-53',
    riskLevel: 'high',
    testFrequency: 'quarterly',
    lastTestedDate: '2024-01-15',
    nextTestDate: '2024-04-15',
    testProcedure: 'Review password policy settings in Active Directory and verify they match requirements',
    testResults: [
      {
        id: 'test-001',
        controlId: 'ctrl-001',
        testDate: '2024-01-15',
        tester: 'Jane Smith',
        result: 'pass',
        evidence: 'Screenshot of Active Directory password policy settings',
        notes: 'All settings match requirements',
        remediation: '',
        remediationDueDate: '',
        remediationStatus: 'not-required',
        createdAt: '2024-01-15T14:30:00Z',
        updatedAt: '2024-01-15T14:30:00Z'
      }
    ],
    createdAt: '2023-10-05T09:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z'
  },
  {
    id: 'ctrl-002',
    title: 'Access Review Process',
    description: 'User access to systems and applications must be reviewed quarterly',
    type: 'detective',
    category: 'administrative',
    status: 'implemented',
    owner: 'IT Operations',
    framework: 'ISO 27001',
    riskLevel: 'medium',
    testFrequency: 'quarterly',
    lastTestedDate: '2024-02-10',
    nextTestDate: '2024-05-10',
    testProcedure: 'Verify that quarterly access reviews were completed and documented',
    testResults: [
      {
        id: 'test-002',
        controlId: 'ctrl-002',
        testDate: '2024-02-10',
        tester: 'John Doe',
        result: 'fail',
        evidence: 'Access review documentation for Q4 2023',
        notes: 'Access review was not completed for the Finance department',
        remediation: 'Complete access review for Finance department',
        remediationDueDate: '2024-03-01',
        remediationStatus: 'completed',
        createdAt: '2024-02-10T11:15:00Z',
        updatedAt: '2024-02-28T16:45:00Z'
      }
    ],
    createdAt: '2023-09-15T13:45:00Z',
    updatedAt: '2024-02-28T16:45:00Z'
  },
  {
    id: 'ctrl-003',
    title: 'Data Backup and Recovery',
    description: 'Critical data must be backed up daily and recovery procedures tested monthly',
    type: 'corrective',
    category: 'technical',
    status: 'implemented',
    owner: 'IT Operations',
    framework: 'NIST 800-53',
    riskLevel: 'critical',
    testFrequency: 'monthly',
    lastTestedDate: '2024-03-05',
    nextTestDate: '2024-04-05',
    testProcedure: 'Verify backup logs and perform a test recovery of a sample dataset',
    testResults: [
      {
        id: 'test-003',
        controlId: 'ctrl-003',
        testDate: '2024-03-05',
        tester: 'Sarah Johnson',
        result: 'pass',
        evidence: 'Backup logs and recovery test documentation',
        notes: 'Recovery test completed successfully within the defined RTO',
        remediation: '',
        remediationDueDate: '',
        remediationStatus: 'not-required',
        createdAt: '2024-03-05T10:30:00Z',
        updatedAt: '2024-03-05T10:30:00Z'
      }
    ],
    createdAt: '2023-08-20T11:30:00Z',
    updatedAt: '2024-03-05T10:30:00Z'
  }
];

// Sample test results (separate from controls for direct API access)
const controlTestResults = [
  {
    id: 'test-001',
    controlId: 'ctrl-001',
    testDate: '2024-01-15',
    tester: 'Jane Smith',
    result: 'pass',
    evidence: 'Screenshot of Active Directory password policy settings',
    notes: 'All settings match requirements',
    remediation: '',
    remediationDueDate: '',
    remediationStatus: 'not-required',
    createdAt: '2024-01-15T14:30:00Z',
    updatedAt: '2024-01-15T14:30:00Z'
  },
  {
    id: 'test-002',
    controlId: 'ctrl-002',
    testDate: '2024-02-10',
    tester: 'John Doe',
    result: 'fail',
    evidence: 'Access review documentation for Q4 2023',
    notes: 'Access review was not completed for the Finance department',
    remediation: 'Complete access review for Finance department',
    remediationDueDate: '2024-03-01',
    remediationStatus: 'completed',
    createdAt: '2024-02-10T11:15:00Z',
    updatedAt: '2024-02-28T16:45:00Z'
  },
  {
    id: 'test-003',
    controlId: 'ctrl-003',
    testDate: '2024-03-05',
    tester: 'Sarah Johnson',
    result: 'pass',
    evidence: 'Backup logs and recovery test documentation',
    notes: 'Recovery test completed successfully within the defined RTO',
    remediation: '',
    remediationDueDate: '',
    remediationStatus: 'not-required',
    createdAt: '2024-03-05T10:30:00Z',
    updatedAt: '2024-03-05T10:30:00Z'
  },
  {
    id: 'test-004',
    controlId: 'ctrl-001',
    testDate: '2023-10-15',
    tester: 'Jane Smith',
    result: 'pass',
    evidence: 'Screenshot of Active Directory password policy settings',
    notes: 'All settings match requirements',
    remediation: '',
    remediationDueDate: '',
    remediationStatus: 'not-required',
    createdAt: '2023-10-15T14:30:00Z',
    updatedAt: '2023-10-15T14:30:00Z'
  },
  {
    id: 'test-005',
    controlId: 'ctrl-002',
    testDate: '2023-11-10',
    tester: 'John Doe',
    result: 'pass',
    evidence: 'Access review documentation for Q3 2023',
    notes: 'All access reviews completed on time',
    remediation: '',
    remediationDueDate: '',
    remediationStatus: 'not-required',
    createdAt: '2023-11-10T11:15:00Z',
    updatedAt: '2023-11-10T11:15:00Z'
  }
];

// Control types for reference
const controlTypes = [
  {
    id: 'preventive',
    name: 'Preventive Control',
    description: 'Controls designed to prevent errors or incidents from occurring'
  },
  {
    id: 'detective',
    name: 'Detective Control',
    description: 'Controls designed to detect errors or incidents after they have occurred'
  },
  {
    id: 'corrective',
    name: 'Corrective Control',
    description: 'Controls designed to correct errors or incidents that have been detected'
  },
  {
    id: 'directive',
    name: 'Directive Control',
    description: 'Controls designed to specify actions to achieve a particular outcome'
  }
];

// Control categories for reference
const controlCategories = [
  {
    id: 'administrative',
    name: 'Administrative Control',
    description: 'Controls implemented through policies and procedures'
  },
  {
    id: 'technical',
    name: 'Technical Control',
    description: 'Controls implemented through technology'
  },
  {
    id: 'physical',
    name: 'Physical Control',
    description: 'Controls implemented through physical means'
  }
];

module.exports = {
  controls,
  controlTestResults,
  controlTypes,
  controlCategories
};
