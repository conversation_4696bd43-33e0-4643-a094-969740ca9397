# Clean Religious Language from NovaFuse Codebase
# Replaces all religious terminology with secular technical language

Write-Host "🔧 CLEANING RELIGIOUS LANGUAGE FROM CODEBASE" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Yellow

# Define replacement mappings
$replacements = @{
    # Core religious terms
    'divine' = 'optimal'
    'Divine' = 'Optimal'
    'DIVINE' = 'OPTIMAL'
    'sacred' = 'prime'
    'Sacred' = 'Prime'
    'SACRED' = 'PRIME'
    'holy' = 'coherent'
    'Holy' = 'Coherent'
    'HOLY' = 'COHERENT'
    'blessed' = 'enhanced'
    'Blessed' = 'Enhanced'
    'BLESSED' = 'ENHANCED'

    # Financial terms
    'tithe' = 'platform_fee'
    'Tithe' = 'Platform_Fee'
    'TITHE' = 'PLATFORM_FEE'
    'offering' = 'optimization_fee'
    'Offering' = 'Optimization_Fee'
    'OFFERING' = 'OPTIMIZATION_FEE'
    'divine allocation' = 'platform allocation'
    'Divine Architecture' = 'Platform Architecture'
    'DIVINE ARCHITECTURE' = 'PLATFORM ARCHITECTURE'

    # Constants and technical terms
    'DIVINE_CONSTANTS' = 'MATHEMATICAL_CONSTANTS'
    'divine constants' = 'mathematical constants'
    'Divine Constants' = 'Mathematical Constants'
    'sacred geometry' = 'prime geometry'
    'Sacred Geometry' = 'Prime Geometry'
    'SACRED GEOMETRY' = 'PRIME GEOMETRY'
    'sacred_geometry_optimization' = 'mathematical_optimization'

    # Religious figures and concepts
    'biblical' = 'historical'
    'Biblical' = 'Historical'
    'BIBLICAL' = 'HISTORICAL'
    'scriptural' = 'reference'
    'Scriptural' = 'Reference'
    'SCRIPTURAL' = 'REFERENCE'
    'prophetic' = 'predictive'
    'Prophetic' = 'Predictive'
    'PROPHETIC' = 'PREDICTIVE'
    'miracle' = 'breakthrough'
    'Miracle' = 'Breakthrough'
    'MIRACLE' = 'BREAKTHROUGH'
    'miraculous' = 'exceptional'
    'Miraculous' = 'Exceptional'
    'MIRACULOUS' = 'EXCEPTIONAL'
}

# File extensions to process
$extensions = @('*.py', '*.js', '*.jsx', '*.ts', '*.tsx', '*.md', '*.json', '*.yml', '*.yaml')

# Get all files to process
$files = @()
foreach ($ext in $extensions) {
    $files += Get-ChildItem -Path . -Recurse -Include $ext -File
}

Write-Host "Found $($files.Count) files to process" -ForegroundColor Green

$totalReplacements = 0
$processedFiles = 0

foreach ($file in $files) {
    try {
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        $originalContent = $content
        $fileReplacements = 0

        # Apply all replacements
        foreach ($key in $replacements.Keys) {
            $newValue = $replacements[$key]
            $pattern = [regex]::Escape($key)
            $matches = [regex]::Matches($content, $pattern)
            if ($matches.Count -gt 0) {
                $content = $content -replace $pattern, $newValue
                $fileReplacements += $matches.Count
            }
        }

        # Write back if changes were made
        if ($content -ne $originalContent) {
            Set-Content -Path $file.FullName -Value $content -Encoding UTF8
            Write-Host "✅ $($file.Name): $fileReplacements replacements" -ForegroundColor Green
            $totalReplacements += $fileReplacements
            $processedFiles++
        }
    }
    catch {
        Write-Host "❌ Error processing $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "" -ForegroundColor White
Write-Host "🎉 CLEANUP COMPLETE!" -ForegroundColor Green
Write-Host "Files processed: $processedFiles" -ForegroundColor Green
Write-Host "Total replacements: $totalReplacements" -ForegroundColor Green
Write-Host "" -ForegroundColor White
Write-Host "✅ All religious language has been replaced with secular technical terminology" -ForegroundColor Green