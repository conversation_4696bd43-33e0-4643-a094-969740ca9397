/**
 * NovaConnect UAC Scalability Test Runner
 * 
 * This script runs all scalability tests and generates a comprehensive report.
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const util = require('util');
const exec = util.promisify(require('child_process').exec);

// Configuration
const config = {
  outputDir: path.join(__dirname, 'results'),
  reportFile: path.join(__dirname, 'results', 'scalability-report.md'),
  tests: [
    {
      name: 'API Load Test',
      script: 'api-load-test.js',
      description: 'Tests the performance and scalability of NovaConnect UAC API endpoints'
    },
    {
      name: 'Normalization Load Test',
      script: 'normalization-load-test.js',
      description: 'Tests the performance and scalability of NovaConnect UAC data normalization'
    },
    {
      name: 'Connector Load Test',
      script: 'connector-load-test.js',
      description: 'Tests the performance and scalability of NovaConnect UAC connectors'
    }
  ],
  k6Options: {
    vus: process.env.K6_VUS || 50,
    duration: process.env.K6_DURATION || '5m',
    rps: process.env.K6_RPS || 100
  },
  apiBaseUrl: process.env.API_BASE_URL || 'http://localhost:3001',
  apiKey: process.env.API_KEY || 'test-api-key'
};

/**
 * Run a k6 test
 * @param {Object} test - Test configuration
 * @returns {Promise<Object>} - Test results
 */
async function runTest(test) {
  console.log(`Running ${test.name}...`);
  
  // Create output directory if it doesn't exist
  if (!fs.existsSync(config.outputDir)) {
    fs.mkdirSync(config.outputDir, { recursive: true });
  }
  
  // Output files
  const outputFile = path.join(config.outputDir, `${test.script.replace('.js', '')}.json`);
  const summaryFile = path.join(config.outputDir, `${test.script.replace('.js', '')}-summary.json`);
  
  // Build k6 command
  const k6Args = [
    'run',
    test.script,
    '--out', `json=${outputFile}`,
    '--summary-export', summaryFile,
    '--env', `API_BASE_URL=${config.apiBaseUrl}`,
    '--env', `API_KEY=${config.apiKey}`
  ];
  
  // Add options
  if (config.k6Options.vus) {
    k6Args.push('--vus', config.k6Options.vus);
  }
  
  if (config.k6Options.duration) {
    k6Args.push('--duration', config.k6Options.duration);
  }
  
  if (config.k6Options.rps) {
    k6Args.push('--rps', config.k6Options.rps);
  }
  
  // Run k6
  return new Promise((resolve, reject) => {
    const k6Process = spawn('k6', k6Args, {
      cwd: __dirname,
      stdio: 'inherit'
    });
    
    k6Process.on('close', (code) => {
      if (code === 0) {
        console.log(`${test.name} completed successfully`);
        
        // Read summary file
        try {
          const summary = JSON.parse(fs.readFileSync(summaryFile, 'utf8'));
          resolve({
            test,
            summary,
            outputFile,
            summaryFile
          });
        } catch (error) {
          reject(new Error(`Failed to read summary file: ${error.message}`));
        }
      } else {
        reject(new Error(`${test.name} failed with code ${code}`));
      }
    });
    
    k6Process.on('error', (error) => {
      reject(new Error(`Failed to start ${test.name}: ${error.message}`));
    });
  });
}

/**
 * Generate a report from test results
 * @param {Array<Object>} results - Test results
 */
function generateReport(results) {
  console.log('Generating report...');
  
  // Create report content
  let report = `# NovaConnect UAC Scalability Test Report\n\n`;
  report += `Generated on: ${new Date().toISOString()}\n\n`;
  
  // Add system information
  report += `## System Information\n\n`;
  report += `- API Base URL: ${config.apiBaseUrl}\n`;
  report += `- Test Duration: ${config.k6Options.duration}\n`;
  report += `- Virtual Users: ${config.k6Options.vus}\n`;
  report += `- Requests Per Second: ${config.k6Options.rps}\n\n`;
  
  // Add summary
  report += `## Summary\n\n`;
  report += `| Test | HTTP Requests | Failed Requests | Avg Response Time | P95 Response Time | RPS |\n`;
  report += `| --- | --- | --- | --- | --- | --- |\n`;
  
  for (const result of results) {
    const metrics = result.summary.metrics;
    const httpReqs = metrics['http_reqs'] ? metrics['http_reqs'].values.count : 0;
    const failedReqs = metrics['http_req_failed'] ? metrics['http_req_failed'].values.passes : 0;
    const avgResponseTime = metrics['http_req_duration'] ? metrics['http_req_duration'].values.avg.toFixed(2) : 0;
    const p95ResponseTime = metrics['http_req_duration'] ? metrics['http_req_duration'].values['p(95)'].toFixed(2) : 0;
    const rps = httpReqs / (result.summary.testRunDurationMs / 1000);
    
    report += `| ${result.test.name} | ${httpReqs} | ${failedReqs} | ${avgResponseTime}ms | ${p95ResponseTime}ms | ${rps.toFixed(2)} |\n`;
  }
  
  report += `\n`;
  
  // Add detailed results
  report += `## Detailed Results\n\n`;
  
  for (const result of results) {
    report += `### ${result.test.name}\n\n`;
    report += `${result.test.description}\n\n`;
    
    // Add metrics
    report += `#### Metrics\n\n`;
    report += `| Metric | Avg | Min | Med | P90 | P95 | P99 | Max |\n`;
    report += `| --- | --- | --- | --- | --- | --- | --- | --- |\n`;
    
    const metrics = result.summary.metrics;
    
    // HTTP request duration
    const httpReqDuration = metrics['http_req_duration'];
    if (httpReqDuration) {
      report += `| HTTP Request Duration | ${httpReqDuration.values.avg.toFixed(2)}ms | ${httpReqDuration.values.min.toFixed(2)}ms | ${httpReqDuration.values.med.toFixed(2)}ms | ${httpReqDuration.values['p(90)'].toFixed(2)}ms | ${httpReqDuration.values['p(95)'].toFixed(2)}ms | ${httpReqDuration.values['p(99)'].toFixed(2)}ms | ${httpReqDuration.values.max.toFixed(2)}ms |\n`;
    }
    
    // Data normalization duration
    const dataNormalizationDuration = metrics['data_normalization_duration'];
    if (dataNormalizationDuration) {
      report += `| Data Normalization Duration | ${dataNormalizationDuration.values.avg.toFixed(2)}ms | ${dataNormalizationDuration.values.min.toFixed(2)}ms | ${dataNormalizationDuration.values.med.toFixed(2)}ms | ${dataNormalizationDuration.values['p(90)'].toFixed(2)}ms | ${dataNormalizationDuration.values['p(95)'].toFixed(2)}ms | ${dataNormalizationDuration.values['p(99)'].toFixed(2)}ms | ${dataNormalizationDuration.values.max.toFixed(2)}ms |\n`;
    }
    
    // Connector execution duration
    const connectorExecutionDuration = metrics['connector_execution_duration'];
    if (connectorExecutionDuration) {
      report += `| Connector Execution Duration | ${connectorExecutionDuration.values.avg.toFixed(2)}ms | ${connectorExecutionDuration.values.min.toFixed(2)}ms | ${connectorExecutionDuration.values.med.toFixed(2)}ms | ${connectorExecutionDuration.values['p(90)'].toFixed(2)}ms | ${connectorExecutionDuration.values['p(95)'].toFixed(2)}ms | ${connectorExecutionDuration.values['p(99)'].toFixed(2)}ms | ${connectorExecutionDuration.values.max.toFixed(2)}ms |\n`;
    }
    
    // Error rate
    const errorRate = metrics['error_rate'];
    if (errorRate) {
      report += `| Error Rate | ${(errorRate.values.rate * 100).toFixed(2)}% | - | - | - | - | - | - |\n`;
    }
    
    report += `\n`;
    
    // Add checks
    report += `#### Checks\n\n`;
    report += `| Check | Pass Rate |\n`;
    report += `| --- | --- |\n`;
    
    for (const [name, check] of Object.entries(result.summary.root_group.checks)) {
      const passRate = (check.passes / (check.passes + check.fails) * 100).toFixed(2);
      report += `| ${name} | ${passRate}% |\n`;
    }
    
    report += `\n`;
    
    // Add thresholds
    report += `#### Thresholds\n\n`;
    
    if (Object.keys(result.summary.metrics).length > 0) {
      report += `| Threshold | Result |\n`;
      report += `| --- | --- |\n`;
      
      for (const [name, metric] of Object.entries(result.summary.metrics)) {
        if (metric.thresholds) {
          for (const [threshold, thresholdResult] of Object.entries(metric.thresholds)) {
            report += `| ${name} ${threshold} | ${thresholdResult.ok ? '✅ Pass' : '❌ Fail'} |\n`;
          }
        }
      }
    } else {
      report += `No thresholds defined.\n`;
    }
    
    report += `\n`;
  }
  
  // Add recommendations
  report += `## Recommendations\n\n`;
  
  // Check for performance issues
  let hasPerformanceIssues = false;
  let hasErrorIssues = false;
  let hasScalabilityIssues = false;
  
  for (const result of results) {
    const metrics = result.summary.metrics;
    
    // Check HTTP request duration
    const httpReqDuration = metrics['http_req_duration'];
    if (httpReqDuration && httpReqDuration.values['p(95)'] > 500) {
      hasPerformanceIssues = true;
    }
    
    // Check error rate
    const errorRate = metrics['error_rate'];
    if (errorRate && errorRate.values.rate > 0.01) {
      hasErrorIssues = true;
    }
    
    // Check for failed thresholds
    for (const [name, metric] of Object.entries(metrics)) {
      if (metric.thresholds) {
        for (const [threshold, thresholdResult] of Object.entries(metric.thresholds)) {
          if (!thresholdResult.ok) {
            hasScalabilityIssues = true;
          }
        }
      }
    }
  }
  
  if (hasPerformanceIssues) {
    report += `### Performance Issues\n\n`;
    report += `Some API endpoints have high response times (P95 > 500ms). Consider the following optimizations:\n\n`;
    report += `- Implement caching for frequently accessed data\n`;
    report += `- Optimize database queries\n`;
    report += `- Use connection pooling for external services\n`;
    report += `- Implement request batching for high-volume operations\n\n`;
  }
  
  if (hasErrorIssues) {
    report += `### Error Issues\n\n`;
    report += `The error rate is higher than expected (> 1%). Consider the following improvements:\n\n`;
    report += `- Enhance error handling and recovery mechanisms\n`;
    report += `- Implement circuit breakers for external services\n`;
    report += `- Add retry logic for transient failures\n`;
    report += `- Improve input validation\n\n`;
  }
  
  if (hasScalabilityIssues) {
    report += `### Scalability Issues\n\n`;
    report += `Some thresholds were not met during the tests. Consider the following scalability enhancements:\n\n`;
    report += `- Implement horizontal scaling\n`;
    report += `- Use a load balancer\n`;
    report += `- Optimize resource utilization\n`;
    report += `- Implement database sharding\n`;
    report += `- Use a message queue for asynchronous processing\n\n`;
  }
  
  if (!hasPerformanceIssues && !hasErrorIssues && !hasScalabilityIssues) {
    report += `No significant issues were found during the scalability tests. The NovaConnect UAC is performing well under the tested load.\n\n`;
    report += `To further enhance performance and scalability, consider the following proactive measures:\n\n`;
    report += `- Implement a CDN for static assets\n`;
    report += `- Use a distributed cache for frequently accessed data\n`;
    report += `- Implement database read replicas\n`;
    report += `- Set up auto-scaling based on load metrics\n`;
    report += `- Implement a monitoring and alerting system\n\n`;
  }
  
  // Write report to file
  fs.writeFileSync(config.reportFile, report);
  
  console.log(`Report generated: ${config.reportFile}`);
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('Starting NovaConnect UAC Scalability Tests');
    
    // Check if k6 is installed
    try {
      await exec('k6 version');
    } catch (error) {
      console.error('k6 is not installed. Please install k6: https://k6.io/docs/getting-started/installation/');
      process.exit(1);
    }
    
    // Run tests
    const results = [];
    
    for (const test of config.tests) {
      try {
        const result = await runTest(test);
        results.push(result);
      } catch (error) {
        console.error(`Error running ${test.name}: ${error.message}`);
      }
    }
    
    // Generate report
    if (results.length > 0) {
      generateReport(results);
    } else {
      console.error('No tests completed successfully');
    }
    
    console.log('Scalability tests completed');
  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  }
}

// Run main function
main();
