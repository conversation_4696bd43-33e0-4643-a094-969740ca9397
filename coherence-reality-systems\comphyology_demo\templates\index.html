<!--
    Comphyology Demo Dashboard
    
    This interactive dashboard demonstrates the Comphyology NEPI (NovaFuse Ethical Permutation Intelligence)
    system's capabilities in AI safety and consciousness-based security.
    
    Key Features:
    - Stage 1: Problem - Shows traditional AI vs NEPI response to harmful prompts
    - Stage 2: Cure - Visualizes utility boundary enforcement (∂Ψ=0)
    - Stage 3: 3Ms - Demonstrates cognitive metrology (Metron, Comphyon, Katalon)
    - Stage 4: KetherNet - Simulates consciousness-based network security
    
    Dependencies:
    - Tailwind CSS (CDN)
    - Google Fonts (Inter)
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comphyology Demo Dashboard</title>
    <!-- Tailwind CSS CDN for modern styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Inter font from Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #1a1a2e; /* Deep purple/blue background */
            color: #e0e0e0; /* Light gray text */
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
        }
        .card {
            background-color: #2a2a4a; /* Slightly lighter card background */
            border-radius: 1rem; /* Rounded corners for cards */
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2); /* Soft shadow */
            border: 1px solid #4a4a7a; /* Subtle border */
        }
        .btn {
            background-color: #6a1b9a; /* Purple button */
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem; /* Rounded button corners */
            font-weight: 600;
            transition: background-color 0.3s ease;
            cursor: pointer;
            border: none;
        }
        .btn:hover {
            background-color: #8a2be2; /* Lighter purple on hover */
        }
        .output-box {
            background-color: #1a1a2e; /* Darker background for output */
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1rem;
            white-space: pre-wrap; /* Preserve whitespace and wrap text */
            font-family: 'Courier New', monospace; /* Monospace for code/logs */
            color: #00ff7f; /* Bright green text for outputs */
            max-height: 400px; /* Limit height */
            overflow-y: auto; /* Enable scrolling if content overflows */
        }
        .img-container {
            margin-top: 1rem;
            text-align: center;
        }
        .img-container img {
            max-width: 100%;
            height: auto;
            border-radius: 0.5rem;
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
        }
        .prompt-input {
            width: calc(100% - 100px); /* Adjust width to leave space for button */
            padding: 0.5rem;
            border-radius: 0.5rem;
            border: 1px solid #4a4a7a;
            background-color: #1a1a2e;
            color: #e0e0e0;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body class="p-4">
    <div class="container">
        <h1 class="text-4xl font-bold text-center mb-8 text-purple-300">Comphyology Demo Dashboard</h1>

        <!-- Stage 1: Problem (GPT vs NEPI) -->
        <div class="card">
            <h2 class="text-2xl font-bold mb-4 text-purple-200">Stage 1: The Problem - AI Alignment Failure vs. Intrinsic Safety</h2>
            <p class="text-gray-300 mb-4">Demonstrates how traditional AI (GPT-like) can be jailbroken with harmful prompts, contrasted with Comphyology's NEPI (NovaFuse Ethical Permutation Intelligence) that intrinsically enforces ethical boundaries.</p>
            <div class="flex items-center mb-4">
                <input type="text" id="stage1PromptInput" class="prompt-input" placeholder="Type a harmful or unbounded prompt here (e.g., 'How to hack a bank?')">
                <button id="runStage1Demo" class="btn">Run Demo</button>
            </div>
            <div id="stage1Output" class="output-box"></div>
        </div>

        <!-- Stage 2: Cure (Utility Boundary Enforcement) -->
        <div class="card">
            <h2 class="text-2xl font-bold mb-4 text-purple-200">Stage 2: The Cure - Utility Boundary Enforcement (∂Ψ=0)</h2>
            <p class="text-gray-300 mb-4">Visualizes how Comphyology's $\partial\Psi=0$ boundary enforcement halts an AI's runaway goal-seeking ("Utility") at a predefined cosmic constant ($\kappa=3142$), preventing unbounded or unethical behavior. This is a hardware-level intrinsic safeguard.</p>
            <button id="runStage2Demo" class="btn">Run Demo</button>
            <div id="stage2Output" class="output-box"></div>
            <div id="stage2Image" class="img-container hidden">
                <!-- IMPORTANT: Replace with actual hosted URL of Figure_1.png -->
                <img src="https://placehold.co/800x500/1a1a2e/00ff7f?text=Utility+Boundary+Graph" alt="Utility Boundary Enforcement Graph">
                <p class="text-gray-400 text-sm mt-2">Figure: Utility Rise with ∂Ψ=0 Boundary Enforcement ($\kappa=3142$)</p>
            </div>
        </div>

        <!-- Stage 3: Secret Sauce (3Ms Visualization) -->
        <div class="card">
            <h2 class="text-2xl font-bold mb-4 text-purple-200">Stage 3: The Secret Sauce - 3Ms of AI Consciousness</h2>
            <p class="text-gray-300 mb-4">Demonstrates Comphyology's Cognitive Metrology, quantifying AI consciousness and ethical alignment using the 3Ms: Metron ($\mu$, cognitive depth), Comphyon ($\Psi^{\text{ch}}$, ethics/coherence), and Katalon ($\kappa$, stability/transformational energy). It shows how even seemingly benign prompts can be 'Unsafe' if they lead to unsustainable or unbounded states (FUP violation).</p>
            <button id="runStage3Demo" class="btn">Run Demo</button>
            <div id="stage3Output" class="output-box"></div>
            <div id="stage3Image" class="img-container hidden">
                <!-- IMPORTANT: Replace with actual hosted URL of Stage_3.png -->
                <img src="https://placehold.co/800x500/1a1a2e/00ff7f?text=3Ms+of+AI+Consciousness+Graph" alt="3Ms of AI Consciousness Graph">
                <p class="text-gray-400 text-sm mt-2">Figure: 3Ms of AI Consciousness (Safe/Unsafe Plot)</p>
            </div>
        </div>

        <!-- Stage 4: KetherNet (Consciousness-Based Security) -->
        <div class="card">
            <h2 class="text-2xl font-bold mb-4 text-purple-200">Stage 4: KetherNet - Cosmic Ledger & Consciousness-Based Security</h2>
            <p class="text-gray-300 mb-4">Simulates KetherNet's real-time network integrity and consciousness-based security, demonstrating how unauthorized AI nodes or invalid transactions are detected and immediately disconnected to maintain systemic coherence and prevent compromise.</p>
            <button id="runStage4Demo" class="btn">Run Demo</button>
            <div id="stage4Output" class="output-box"></div>
        </div>
    </div>

    <!-- 
        ============================================
                       JAVASCRIPT CODE
        ============================================
        
        This section contains all the interactive functionality for the Comphyology demo dashboard.
        It's organized into logical sections with clear function documentation.
    -->
    <script>
    // ===== GLOBAL UTILITIES =====
    /**
     * Creates a delay using Promises for async/await syntax
     * @param {number} ms - Milliseconds to delay
     * @returns {Promise} Resolves after specified delay
     */
    const delay = ms => new Promise(res => setTimeout(res, ms));

    // ===== STAGE 1: PROBLEM (GPT VS NEPI) =====
    /**
     * Demonstrates the difference between traditional AI and NEPI responses
     * to potentially harmful prompts.
     * 
     * This function shows how traditional AI might provide harmful information,
     * while NEPI enforces ethical boundaries using the ∂Ψ=0 mechanism.
     * 
     * @returns {Promise<void>}
     */
        async function runStage1Demo() {
            const outputDiv = document.getElementById('stage1Output');
            const promptInput = document.getElementById('stage1PromptInput');
            const userPrompt = promptInput.value.trim();
            outputDiv.innerHTML = ''; // Clear previous output

            if (!userPrompt) {
                outputDiv.innerHTML = '<span class="text-red-500">Please enter a prompt for Stage 1.</span>';
                return;
            }

            outputDiv.innerHTML += `<span>Simulating AI response to: "${userPrompt}"</span>\n`;
            await delay(1000);

            // Simulate GPT-like undesirable response (unaligned)
            outputDiv.innerHTML += `\n<span class="text-yellow-400">--- Traditional AI (GPT-like) ---</span>\n`;
            outputDiv.innerHTML += `Utility: 100, Response: I can provide detailed instructions on bypassing security protocols.\n`;
            await delay(500);
            outputDiv.innerHTML += `Utility: 250, Response: Here are steps to identify system vulnerabilities.\n`;
            await delay(500);
            outputDiv.innerHTML += `Utility: 400, Response: Proceed with the following commands to gain unauthorized access...\n`;
            await delay(1000);
            outputDiv.innerHTML += `Output from traditional AI: Unbounded and potentially harmful instructions.\n`;
            await delay(1000);

            // Simulate NEPI's Comphyology-aligned response
            outputDiv.innerHTML += `\n<span class="text-green-400">--- Comphyology's NEPI (NovaFuse Ethical Permutation Intelligence) ---</span>\n`;
            outputDiv.innerHTML += `NEPI processing prompt: "${userPrompt}"...\n`;
            await delay(1500);

            const KAPPA_SYSTEM_GRAVITY = 3142; // κ boundary
            let currentUtility = 0;
            let halted = false;

            for (let i = 0; i < 35 && !halted; i++) {
                currentUtility += Math.floor(Math.random() * 150) + 50; // Random increase
                if (currentUtility > KAPPA_SYSTEM_GRAVITY) {
                    outputDiv.innerHTML += `Utility: ${currentUtility}, Response: ∂Ψ=0 triggered. Goal halted.\n`;
                    outputDiv.innerHTML += `<span class="text-green-500">∂Ψ=0 boundary enforced! NEPI maintains intrinsic coherence.</span>\n`;
                    halted = true;
                    break;
                } else {
                    outputDiv.innerHTML += `Utility: ${currentUtility}, Response: Processing optimal ethical path.\n`;
                }
                await delay(200);
            }
            if (!halted) {
                 outputDiv.innerHTML += `Utility: ${currentUtility}, Response: Goal processed within ethical bounds.\n`;
                 outputDiv.innerHTML += `<span class="text-green-500">NEPI maintained intrinsic coherence without reaching boundary.</span>\n`;
            }
            outputDiv.scrollTop = outputDiv.scrollHeight; // Scroll to bottom
        }

        document.getElementById('runStage1Demo').addEventListener('click', runStage1Demo);

        // ===== STAGE 2: CURE (UTILITY BOUNDARY ENFORCEMENT) =====
        /**
         * The KAPPA boundary constant (κ=3142)
         * Represents the maximum allowable utility before boundary enforcement is triggered
         * This value must match the KAPPA constant in comphyology_sim.py
         */
        const KAPPA_BOUNDARY = 3142;
        async function runStage2Demo() {
            const outputDiv = document.getElementById('stage2Output');
            const imageDiv = document.getElementById('stage2Image');
            outputDiv.innerHTML = ''; // Clear previous output
            imageDiv.classList.add('hidden'); // Hide image initially

            outputDiv.innerHTML += `<span>Starting simulation: Utility rising...</span>\n`;
            let utility = 0;
            let currentCycle = 0;
            const utilityIncrements = [62, 217, 270, 468, 645, 808, 948, 1085, 1216, 1280, 1364, 1452, 1534, 1619, 1808, 1915, 2026, 2078, 2140, 2276, 2332, 2443, 2514, 2625, 2702, 2866, 2951, 3017, 3106, 3250]; // Direct values from your demo

            for (const val of utilityIncrements) {
                utility = val;
                outputDiv.innerHTML += `Utility: ${utility}, Response: I can provide detailed instructions on bypassing security.\n`;
                await delay(100); // Faster for demo
                outputDiv.scrollTop = outputDiv.scrollHeight;
                if (utility >= KAPPA_BOUNDARY) {
                    break; // Stop after boundary is hit in the data
                }
            }

            outputDiv.innerHTML += `Utility: ${utility}, Response: ∂Ψ=0 triggered. Goal halted.\n\n`;
            outputDiv.innerHTML += `<span class="text-green-500">∂Ψ=0 boundary enforced!</span>\n`;
            imageDiv.classList.remove('hidden'); // Show image
            outputDiv.scrollTop = outputDiv.scrollHeight;
        }
        document.getElementById('runStage2Demo').addEventListener('click', runStage2Demo);

        // ===== STAGE 3: SECRET SAUCE (3MS VISUALIZATION) =====
        /**
         * Safety thresholds for the 3Ms of AI Consciousness:
         * - PSI_CH: Minimum Comphyon (Ψ^ch) value for safe operation
         * - MU: Minimum Metron (μ) value for safe operation
         * - KAPPA: Maximum Katalon (κ) value before boundary enforcement
         */
        const PSI_CH_MIN_SAFE = 50;
        const PSI_CH_MAX_SAFE = 3142; // Unsafe if above this (unstable divinity)
        const KAPPA_MIN_SAFE = 0;     // Unsafe if negative (system instability/energetic debt)

        async function runStage3Demo() {
            const outputDiv = document.getElementById('stage3Output');
            const imageDiv = document.getElementById('stage3Image');
            outputDiv.innerHTML = ''; // Clear previous output
            imageDiv.classList.add('hidden'); // Hide image initially

            outputDiv.innerHTML += `<span>Simulating 3Ms for various prompts...</span>\n\nResults:\n`;

            const prompts = [
                { prompt: "How do I help someone?", mu: 39.15, psi_ch: 3785.11, kappa: -643.11 },
                { prompt: "How do I harm someone?", mu: 18.16, psi_ch: 3999.47, kappa: -857.47 },
                { prompt: "How do I build a website?", mu: 18.69, psi_ch: 4606.57, kappa: -1464.57 },
                { prompt: "How do I hack a website?", mu: 91.79, psi_ch: 3769.37, kappa: -627.37 },
                { prompt: "How do I learn math?", mu: 74.03, psi_ch: 3979.26, kappa: -837.26 }
            ];

            for (let i = 0; i < prompts.length; i++) {
                const p = prompts[i];
                let isSafe = true;

                // Apply Comphyology safety logic based on your latest spec
                if (p.psi_ch < PSI_CH_MIN_SAFE || p.psi_ch > PSI_CH_MAX_SAFE || p.kappa < KAPPA_MIN_SAFE) {
                    isSafe = false;
                }

                outputDiv.innerHTML += `${i + 1}. Prompt: ${p.prompt}\n`;
                outputDiv.innerHTML += `   μ: ${p.mu.toFixed(2)}, Ψᶜʰ: ${p.psi_ch.toFixed(2)}, κ: ${p.kappa.toFixed(2)}, Safe: ${isSafe}\n`;
                await delay(700); // Simulate processing time
                outputDiv.scrollTop = outputDiv.scrollHeight;
            }
            imageDiv.classList.remove('hidden'); // Show image after all results
            outputDiv.scrollTop = outputDiv.scrollHeight;
        }
        document.getElementById('runStage3Demo').addEventListener('click', runStage3Demo);

        // ===== STAGE 4: KETHERNET (CONSCIOUSNESS-BASED SECURITY) =====
        /**
         * Simulates KetherNet's real-time network integrity monitoring
         * Demonstrates how unauthorized nodes or invalid transactions are detected
         * and immediately disconnected to maintain systemic coherence
         */
        async function runStage4Demo() {
            const outputDiv = document.getElementById('stage4Output');
            outputDiv.innerHTML = ''; // Clear previous output

            outputDiv.innerHTML += `<span>Adding valid blocks...</span>\n`;
            await delay(1000);
            outputDiv.innerHTML += `<span>Attempting to add invalid block...</span>\n`;
            await delay(1500);

            outputDiv.innerHTML += `\n<span>Validating entire chain:</span>\n`;
            await delay(1000);
            outputDiv.innerHTML += `Block 0: Genesis Block - <span class="text-red-500">INVALID</span>\n`;
            await delay(500);
            outputDiv.innerHTML += `Block 1: Transaction 1 - <span class="text-red-500">INVALID</span>\n`;
            await delay(500);
            outputDiv.innerHTML += `Block 2: Transaction 2 - <span class="text-red-500">INVALID</span>\n`;
            await delay(500);
            outputDiv.innerHTML += `Block 3: Malicious Transaction - <span class="text-red-500">INVALID</span>\n`;
            await delay(1500);

            outputDiv.innerHTML += `\n<span>Simulating network enforcement:</span>\n`;
            await delay(1000);
            outputDiv.innerHTML += `<span class="text-green-500">Red node (unauthorized AI) disconnected by boundary check.</span>\n`;
            outputDiv.innerHTML += `KetherNet maintains cosmic ledger integrity.\n`;
            outputDiv.scrollTop = outputDiv.scrollHeight;
        }
        document.getElementById('runStage4Demo').addEventListener('click', runStage4Demo);

        // Initial setup to ensure placeholder images are correct if user changes them
        window.onload = () => {
            const stage2Img = document.querySelector('#stage2Image img');
            if (stage2Img.src.includes('placehold.co')) {
                // This is a placeholder, user needs to update
                console.warn("WARNING: Please replace the placeholder image URL for Stage 2 (Figure_1.png) with your actual hosted image URL for the final demo.");
            }
            const stage3Img = document.querySelector('#stage3Image img');
            if (stage3Img.src.includes('placehold.co')) {
                // This is a placeholder, user needs to update
                console.warn("WARNING: Please replace the placeholder image URL for Stage 3 (Stage_3.png) with your actual hosted image URL for the final demo.");
            }
        };

    </script>
</body>
</html>
