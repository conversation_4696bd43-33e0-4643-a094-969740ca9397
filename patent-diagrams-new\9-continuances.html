<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>9 Industry-Specific Continuances</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 800px;
            height: 600px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            box-sizing: border-box;
        }
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
        }
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            box-sizing: border-box;
            font-size: 14px;
            line-height: 1.2;
        }
        .component-label {
            font-weight: bold;
            margin-bottom: 4px;
        }
        .component-number {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 20px;
            height: 20px;
            background-color: #333;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
        }
        .arrow {
            position: absolute;
            background-color: #333;
            width: 2px;
        }
        .legend {
            position: absolute;
            right: 10px;
            bottom: -90px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            width: 200px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border: 1px solid #333;
        }
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: -90px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>FIG. 4: 9 Industry-Specific Continuances</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 600px; left: 25px; top: 20px;">
            <div class="container-label">CYBER-SAFETY FRAMEWORK: 9 CONTINUANCES</div>
        </div>
        
        <!-- Top Row - Continuances 1-3 -->
        <div class="component-box" style="left: 100px; top: 80px; width: 150px; height: 100px;">
            <div class="component-number">401</div>
            <div class="component-label">C1: Financial Services</div>
            PCI-DSS, SOX, GLBA
        </div>
        
        <div class="component-box" style="left: 270px; top: 80px; width: 150px; height: 100px;">
            <div class="component-number">402</div>
            <div class="component-label">C2: Healthcare</div>
            HIPAA, HITECH, FDA
        </div>
        
        <div class="component-box" style="left: 440px; top: 80px; width: 150px; height: 100px;">
            <div class="component-number">403</div>
            <div class="component-label">C3: Education</div>
            FERPA, COPPA
        </div>
        
        <!-- Middle Row - Continuances 4-6 -->
        <div class="component-box" style="left: 100px; top: 200px; width: 150px; height: 100px;">
            <div class="component-number">404</div>
            <div class="component-label">C4: Government & Defense</div>
            FedRAMP, CMMC, FISMA
        </div>
        
        <div class="component-box" style="left: 270px; top: 200px; width: 150px; height: 100px;">
            <div class="component-number">405</div>
            <div class="component-label">C5: Critical Infrastructure</div>
            NERC-CIP, IEC 62443
        </div>
        
        <div class="component-box" style="left: 440px; top: 200px; width: 150px; height: 100px;">
            <div class="component-number">406</div>
            <div class="component-label">C6: AI Governance</div>
            EU AI Act, NIST AI RMF
        </div>
        
        <!-- Bottom Row - Continuances 7-9 -->
        <div class="component-box" style="left: 100px; top: 320px; width: 150px; height: 100px;">
            <div class="component-number">407</div>
            <div class="component-label">C7: Supply Chain</div>
            ISO 28000, NIST CSF
        </div>
        
        <div class="component-box" style="left: 270px; top: 320px; width: 150px; height: 100px;">
            <div class="component-number">408</div>
            <div class="component-label">C8: Insurance</div>
            NAIC Model Law, GDPR
        </div>
        
        <div class="component-box" style="left: 440px; top: 320px; width: 150px; height: 100px;">
            <div class="component-number">409</div>
            <div class="component-label">C9: Mobile/IoT</div>
            CTIA, ETSI EN 303 645
        </div>
        
        <!-- Pillar and Nova Integration -->
        <div class="container-box" style="width: 700px; height: 120px; left: 80px; top: 450px;">
            <div class="container-label">INTEGRATION WITH PILLARS AND NOVAS</div>
        </div>
        
        <div class="component-box" style="left: 100px; top: 490px; width: 150px; height: 60px;">
            <div class="component-number">410</div>
            <div class="component-label">Pillar Integration</div>
            P7 + P10
        </div>
        
        <div class="component-box" style="left: 270px; top: 490px; width: 150px; height: 60px;">
            <div class="component-number">411</div>
            <div class="component-label">Nova Integration</div>
            N7 + N12
        </div>
        
        <div class="component-box" style="left: 440px; top: 490px; width: 150px; height: 60px;">
            <div class="component-number">412</div>
            <div class="component-label">Regulatory Mapping</div>
            Dynamic Controls
        </div>
        
        <div class="component-box" style="left: 610px; top: 490px; width: 150px; height: 60px;">
            <div class="component-number">413</div>
            <div class="component-label">Implementation</div>
            Industry-Specific
        </div>
        
        <!-- Connecting arrows -->
        <div class="arrow" style="left: 175px; top: 180px; height: 20px;"></div>
        <div class="arrow" style="left: 345px; top: 180px; height: 20px;"></div>
        <div class="arrow" style="left: 515px; top: 180px; height: 20px;"></div>
        
        <div class="arrow" style="left: 175px; top: 300px; height: 20px;"></div>
        <div class="arrow" style="left: 345px; top: 300px; height: 20px;"></div>
        <div class="arrow" style="left: 515px; top: 300px; height: 20px;"></div>
        
        <div class="arrow" style="left: 175px; top: 420px; height: 30px;"></div>
        <div class="arrow" style="left: 345px; top: 420px; height: 30px;"></div>
        <div class="arrow" style="left: 515px; top: 420px; height: 30px;"></div>
        
        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Industry-Specific Continuances</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Integration Components</div>
            </div>
        </div>
        
        <!-- Inventor Label -->
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
