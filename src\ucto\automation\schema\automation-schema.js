/**
 * Compliance Automation Framework Schema
 * 
 * This schema defines the structure and components of the UCTO Compliance Automation Framework.
 */

/**
 * Automation schema definition
 * @type {Object}
 */
const automationSchema = {
  // Metadata
  metadata: {
    name: "UCTO Compliance Automation Framework",
    description: "Framework for automating compliance workflows, evidence collection, and remediation actions",
    version: "1.0.0"
  },
  
  // Trigger types
  triggerTypes: [
    {
      id: "schedule",
      name: "Schedule",
      description: "Trigger based on a schedule (cron expression)",
      parameters: [
        {
          name: "schedule",
          type: "string",
          description: "Cron expression for the schedule",
          required: true
        },
        {
          name: "timezone",
          type: "string",
          description: "Timezone for the schedule",
          required: false,
          default: "UTC"
        }
      ]
    },
    {
      id: "event",
      name: "Event",
      description: "Trigger based on an event",
      parameters: [
        {
          name: "eventType",
          type: "string",
          description: "Type of event to trigger on",
          required: true,
          enum: [
            "requirement_created",
            "requirement_updated",
            "requirement_status_changed",
            "activity_created",
            "activity_updated",
            "activity_status_changed",
            "evidence_created",
            "evidence_updated",
            "control_mapped",
            "compliance_gap_detected",
            "resource_shortage_predicted",
            "due_date_approaching",
            "integration_event"
          ]
        },
        {
          name: "conditions",
          type: "object",
          description: "Conditions for the event to trigger",
          required: false
        }
      ]
    },
    {
      id: "api",
      name: "API",
      description: "Trigger based on an API call",
      parameters: [
        {
          name: "endpoint",
          type: "string",
          description: "API endpoint to expose",
          required: true
        },
        {
          name: "method",
          type: "string",
          description: "HTTP method",
          required: true,
          enum: ["GET", "POST", "PUT", "DELETE"]
        },
        {
          name: "authentication",
          type: "boolean",
          description: "Whether authentication is required",
          required: false,
          default: true
        }
      ]
    },
    {
      id: "manual",
      name: "Manual",
      description: "Trigger manually by a user",
      parameters: [
        {
          name: "roles",
          type: "array",
          description: "Roles that can trigger this workflow",
          required: false,
          items: {
            type: "string"
          }
        }
      ]
    },
    {
      id: "condition",
      name: "Condition",
      description: "Trigger based on a condition",
      parameters: [
        {
          name: "condition",
          type: "string",
          description: "Condition expression",
          required: true
        },
        {
          name: "checkInterval",
          type: "string",
          description: "Interval to check the condition (cron expression)",
          required: true
        }
      ]
    }
  ],
  
  // Action types
  actionTypes: [
    {
      id: "create_requirement",
      name: "Create Requirement",
      description: "Create a new compliance requirement",
      parameters: [
        {
          name: "name",
          type: "string",
          description: "Name of the requirement",
          required: true
        },
        {
          name: "description",
          type: "string",
          description: "Description of the requirement",
          required: true
        },
        {
          name: "framework",
          type: "string",
          description: "Compliance framework",
          required: true
        },
        {
          name: "category",
          type: "string",
          description: "Category of the requirement",
          required: false
        },
        {
          name: "priority",
          type: "string",
          description: "Priority of the requirement",
          required: false,
          enum: ["low", "medium", "high", "critical"]
        },
        {
          name: "status",
          type: "string",
          description: "Status of the requirement",
          required: false,
          default: "pending"
        },
        {
          name: "due_date",
          type: "string",
          description: "Due date of the requirement (ISO date string)",
          required: false
        },
        {
          name: "assigned_to",
          type: "string",
          description: "User or role assigned to the requirement",
          required: false
        },
        {
          name: "tags",
          type: "array",
          description: "Tags for the requirement",
          required: false,
          items: {
            type: "string"
          }
        }
      ]
    },
    {
      id: "update_requirement",
      name: "Update Requirement",
      description: "Update an existing compliance requirement",
      parameters: [
        {
          name: "requirement_id",
          type: "string",
          description: "ID of the requirement to update",
          required: true
        },
        {
          name: "updates",
          type: "object",
          description: "Fields to update",
          required: true
        }
      ]
    },
    {
      id: "create_activity",
      name: "Create Activity",
      description: "Create a new compliance activity",
      parameters: [
        {
          name: "name",
          type: "string",
          description: "Name of the activity",
          required: true
        },
        {
          name: "description",
          type: "string",
          description: "Description of the activity",
          required: true
        },
        {
          name: "requirement_id",
          type: "string",
          description: "ID of the associated requirement",
          required: true
        },
        {
          name: "type",
          type: "string",
          description: "Type of activity",
          required: false,
          enum: ["task", "documentation", "audit", "meeting", "training", "other"]
        },
        {
          name: "status",
          type: "string",
          description: "Status of the activity",
          required: false,
          default: "pending"
        },
        {
          name: "start_date",
          type: "string",
          description: "Start date of the activity (ISO date string)",
          required: false
        },
        {
          name: "end_date",
          type: "string",
          description: "End date of the activity (ISO date string)",
          required: false
        },
        {
          name: "assigned_to",
          type: "string",
          description: "User or role assigned to the activity",
          required: false
        },
        {
          name: "notes",
          type: "string",
          description: "Notes for the activity",
          required: false
        }
      ]
    },
    {
      id: "update_activity",
      name: "Update Activity",
      description: "Update an existing compliance activity",
      parameters: [
        {
          name: "activity_id",
          type: "string",
          description: "ID of the activity to update",
          required: true
        },
        {
          name: "updates",
          type: "object",
          description: "Fields to update",
          required: true
        }
      ]
    },
    {
      id: "collect_evidence",
      name: "Collect Evidence",
      description: "Collect evidence for a compliance requirement or activity",
      parameters: [
        {
          name: "name",
          type: "string",
          description: "Name of the evidence",
          required: true
        },
        {
          name: "description",
          type: "string",
          description: "Description of the evidence",
          required: true
        },
        {
          name: "requirement_id",
          type: "string",
          description: "ID of the associated requirement",
          required: false
        },
        {
          name: "activity_id",
          type: "string",
          description: "ID of the associated activity",
          required: false
        },
        {
          name: "type",
          type: "string",
          description: "Type of evidence",
          required: false,
          enum: ["document", "screenshot", "log", "report", "configuration", "other"]
        },
        {
          name: "source",
          type: "string",
          description: "Source of the evidence",
          required: false
        },
        {
          name: "collection_method",
          type: "string",
          description: "Method used to collect the evidence",
          required: false,
          enum: ["manual", "api", "connector", "script", "other"]
        },
        {
          name: "content",
          type: "string",
          description: "Content of the evidence (base64 encoded)",
          required: false
        },
        {
          name: "url",
          type: "string",
          description: "URL to the evidence",
          required: false
        },
        {
          name: "metadata",
          type: "object",
          description: "Additional metadata for the evidence",
          required: false
        }
      ]
    },
    {
      id: "send_notification",
      name: "Send Notification",
      description: "Send a notification to users or systems",
      parameters: [
        {
          name: "recipients",
          type: "array",
          description: "Recipients of the notification",
          required: true,
          items: {
            type: "string"
          }
        },
        {
          name: "subject",
          type: "string",
          description: "Subject of the notification",
          required: true
        },
        {
          name: "message",
          type: "string",
          description: "Message content",
          required: true
        },
        {
          name: "channel",
          type: "string",
          description: "Notification channel",
          required: false,
          enum: ["email", "slack", "teams", "sms", "webhook", "other"],
          default: "email"
        },
        {
          name: "priority",
          type: "string",
          description: "Priority of the notification",
          required: false,
          enum: ["low", "medium", "high", "critical"],
          default: "medium"
        },
        {
          name: "attachments",
          type: "array",
          description: "Attachments for the notification",
          required: false,
          items: {
            type: "object",
            properties: {
              name: {
                type: "string",
                description: "Name of the attachment"
              },
              content: {
                type: "string",
                description: "Content of the attachment (base64 encoded)"
              },
              contentType: {
                type: "string",
                description: "Content type of the attachment"
              }
            }
          }
        }
      ]
    },
    {
      id: "execute_api_call",
      name: "Execute API Call",
      description: "Execute an API call to an external system",
      parameters: [
        {
          name: "url",
          type: "string",
          description: "URL of the API endpoint",
          required: true
        },
        {
          name: "method",
          type: "string",
          description: "HTTP method",
          required: true,
          enum: ["GET", "POST", "PUT", "DELETE", "PATCH"]
        },
        {
          name: "headers",
          type: "object",
          description: "HTTP headers",
          required: false
        },
        {
          name: "body",
          type: "object",
          description: "Request body",
          required: false
        },
        {
          name: "authentication",
          type: "object",
          description: "Authentication details",
          required: false
        },
        {
          name: "timeout",
          type: "number",
          description: "Timeout in milliseconds",
          required: false,
          default: 30000
        }
      ]
    },
    {
      id: "run_script",
      name: "Run Script",
      description: "Run a script or command",
      parameters: [
        {
          name: "script",
          type: "string",
          description: "Script content or command to run",
          required: true
        },
        {
          name: "type",
          type: "string",
          description: "Type of script",
          required: true,
          enum: ["shell", "python", "javascript", "powershell", "other"]
        },
        {
          name: "arguments",
          type: "array",
          description: "Arguments for the script",
          required: false,
          items: {
            type: "string"
          }
        },
        {
          name: "environment",
          type: "object",
          description: "Environment variables",
          required: false
        },
        {
          name: "timeout",
          type: "number",
          description: "Timeout in milliseconds",
          required: false,
          default: 60000
        }
      ]
    },
    {
      id: "generate_report",
      name: "Generate Report",
      description: "Generate a compliance report",
      parameters: [
        {
          name: "name",
          type: "string",
          description: "Name of the report",
          required: true
        },
        {
          name: "description",
          type: "string",
          description: "Description of the report",
          required: true
        },
        {
          name: "type",
          type: "string",
          description: "Type of report",
          required: true,
          enum: ["compliance_status", "gap_analysis", "risk_assessment", "audit_readiness", "evidence_summary", "custom"]
        },
        {
          name: "format",
          type: "string",
          description: "Format of the report",
          required: false,
          enum: ["pdf", "html", "docx", "xlsx", "json", "csv"],
          default: "pdf"
        },
        {
          name: "filters",
          type: "object",
          description: "Filters for the report data",
          required: false
        },
        {
          name: "template",
          type: "string",
          description: "Template for the report",
          required: false
        },
        {
          name: "recipients",
          type: "array",
          description: "Recipients of the report",
          required: false,
          items: {
            type: "string"
          }
        }
      ]
    },
    {
      id: "map_controls",
      name: "Map Controls",
      description: "Map controls between different frameworks",
      parameters: [
        {
          name: "source_framework",
          type: "string",
          description: "Source compliance framework",
          required: true
        },
        {
          name: "target_framework",
          type: "string",
          description: "Target compliance framework",
          required: true
        },
        {
          name: "mapping_type",
          type: "string",
          description: "Type of mapping",
          required: false,
          enum: ["one_to_one", "one_to_many", "many_to_one", "many_to_many"],
          default: "many_to_many"
        },
        {
          name: "mapping_rules",
          type: "array",
          description: "Rules for mapping controls",
          required: false,
          items: {
            type: "object",
            properties: {
              source_control: {
                type: "string",
                description: "Source control ID"
              },
              target_control: {
                type: "string",
                description: "Target control ID"
              },
              mapping_strength: {
                type: "string",
                description: "Strength of the mapping",
                enum: ["exact", "strong", "moderate", "weak"]
              }
            }
          }
        }
      ]
    },
    {
      id: "conditional",
      name: "Conditional Action",
      description: "Execute actions based on conditions",
      parameters: [
        {
          name: "condition",
          type: "string",
          description: "Condition expression",
          required: true
        },
        {
          name: "if_actions",
          type: "array",
          description: "Actions to execute if the condition is true",
          required: true,
          items: {
            type: "object"
          }
        },
        {
          name: "else_actions",
          type: "array",
          description: "Actions to execute if the condition is false",
          required: false,
          items: {
            type: "object"
          }
        }
      ]
    }
  ],
  
  // Workflow template
  workflowTemplate: {
    id: "",
    name: "",
    description: "",
    version: "1.0.0",
    enabled: true,
    trigger: {
      type: "",
      parameters: {}
    },
    actions: [
      {
        id: "",
        type: "",
        name: "",
        description: "",
        parameters: {},
        condition: "",
        onSuccess: {
          actions: []
        },
        onFailure: {
          actions: []
        }
      }
    ],
    variables: [
      {
        name: "",
        type: "",
        description: "",
        defaultValue: "",
        required: false
      }
    ],
    settings: {
      timeout: 3600,
      maxRetries: 3,
      retryDelay: 60,
      logLevel: "info"
    },
    tags: [],
    created_by: "",
    created_at: "",
    updated_by: "",
    updated_at: ""
  }
};

module.exports = automationSchema;
