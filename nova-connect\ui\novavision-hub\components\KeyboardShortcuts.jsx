/**
 * KeyboardShortcuts Component
 * 
 * A component for displaying and managing keyboard shortcuts.
 */

import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useAccessibility } from '../accessibility/AccessibilityContext';
import { useTheme } from '../theme/ThemeContext';
import { Animated } from './Animated';
import { useI18n } from '../i18n/I18nContext';

/**
 * KeyboardShortcuts component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.shortcuts - Keyboard shortcuts
 * @param {boolean} [props.showHelp=false] - Whether to show help dialog
 * @param {Function} [props.onShowHelpChange] - Callback when show help changes
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} KeyboardShortcuts component
 */
const KeyboardShortcuts = ({
  shortcuts,
  showHelp = false,
  onShowHelpChange,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const { settings } = useAccessibility();
  const { translate } = useI18n();
  
  // State
  const [isHelpVisible, setIsHelpVisible] = useState(showHelp);
  const [activeCategory, setActiveCategory] = useState(null);
  
  // Update help visibility when prop changes
  useEffect(() => {
    setIsHelpVisible(showHelp);
  }, [showHelp]);
  
  // Update active category when help becomes visible
  useEffect(() => {
    if (isHelpVisible && shortcuts && Object.keys(shortcuts).length > 0) {
      setActiveCategory(Object.keys(shortcuts)[0]);
    }
  }, [isHelpVisible, shortcuts]);
  
  // Handle key down
  const handleKeyDown = useCallback((event) => {
    // Only handle shortcuts if keyboard navigation is enabled
    if (!settings.keyboardNavigation) {
      return;
    }
    
    // Check if shortcut is registered
    Object.entries(shortcuts).forEach(([category, categoryShortcuts]) => {
      categoryShortcuts.forEach(shortcut => {
        const keys = shortcut.key.split('+').map(key => key.trim().toLowerCase());
        
        // Check if all keys are pressed
        const isShiftRequired = keys.includes('shift');
        const isCtrlRequired = keys.includes('ctrl') || keys.includes('control');
        const isAltRequired = keys.includes('alt');
        const isMetaRequired = keys.includes('meta') || keys.includes('command');
        
        // Get main key (last key in the combination)
        const mainKey = keys[keys.length - 1];
        
        // Check if shortcut matches
        if (
          (isShiftRequired === event.shiftKey) &&
          (isCtrlRequired === event.ctrlKey) &&
          (isAltRequired === event.altKey) &&
          (isMetaRequired === event.metaKey) &&
          event.key.toLowerCase() === mainKey
        ) {
          // Prevent default behavior
          event.preventDefault();
          
          // Execute shortcut action
          if (shortcut.action) {
            shortcut.action();
          }
        }
      });
    });
    
    // Toggle help dialog with '?' key
    if (event.key === '?' && event.shiftKey) {
      event.preventDefault();
      toggleHelp();
    }
  }, [settings.keyboardNavigation, shortcuts]);
  
  // Add key down event listener
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
  
  // Toggle help dialog
  const toggleHelp = () => {
    const newValue = !isHelpVisible;
    setIsHelpVisible(newValue);
    
    if (onShowHelpChange) {
      onShowHelpChange(newValue);
    }
  };
  
  // Format key combination
  const formatKey = (key) => {
    return key
      .split('+')
      .map(k => k.trim())
      .map(k => {
        switch (k.toLowerCase()) {
          case 'ctrl':
          case 'control':
            return 'Ctrl';
          case 'alt':
            return 'Alt';
          case 'shift':
            return 'Shift';
          case 'meta':
          case 'command':
            return 'Cmd';
          case 'arrowup':
            return '↑';
          case 'arrowdown':
            return '↓';
          case 'arrowleft':
            return '←';
          case 'arrowright':
            return '→';
          case 'enter':
            return '↵';
          case 'escape':
            return 'Esc';
          case 'tab':
            return 'Tab';
          case 'space':
            return 'Space';
          case 'backspace':
            return '⌫';
          case 'delete':
            return 'Del';
          case 'home':
            return 'Home';
          case 'end':
            return 'End';
          case 'pageup':
            return 'PgUp';
          case 'pagedown':
            return 'PgDn';
          default:
            return k.length === 1 ? k.toUpperCase() : k;
        }
      })
      .join(' + ');
  };
  
  // Get categories
  const categories = shortcuts ? Object.keys(shortcuts) : [];
  
  // Render help dialog
  if (!isHelpVisible) {
    return null;
  }
  
  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 ${className}`}
      style={style}
      data-testid="keyboard-shortcuts"
    >
      <div className="fixed inset-0 bg-black bg-opacity-50" onClick={toggleHelp}></div>
      
      <Animated
        animation="zoomIn"
        className="bg-surface rounded-lg shadow-lg overflow-hidden w-full max-w-3xl relative"
      >
        {/* Header */}
        <div className="bg-background p-4 border-b border-divider flex justify-between items-center">
          <h2 className="text-xl font-bold text-textPrimary">
            {translate('accessibility.keyboardShortcuts', 'Keyboard Shortcuts')}
          </h2>
          
          <button
            type="button"
            className="text-textSecondary hover:text-textPrimary p-1 rounded-full"
            onClick={toggleHelp}
            aria-label={translate('common.close', 'Close')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Content */}
        <div className="flex h-96">
          {/* Categories */}
          <div className="w-1/3 border-r border-divider overflow-y-auto">
            <ul className="py-2">
              {categories.map(category => (
                <li key={category}>
                  <button
                    type="button"
                    className={`w-full text-left px-4 py-2 ${
                      activeCategory === category
                        ? 'bg-primary bg-opacity-10 text-primary'
                        : 'text-textPrimary hover:bg-surface'
                    }`}
                    onClick={() => setActiveCategory(category)}
                  >
                    {translate(`shortcuts.${category}`, category)}
                  </button>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Shortcuts */}
          <div className="w-2/3 overflow-y-auto p-4">
            {activeCategory && shortcuts[activeCategory] && (
              <Animated animation="fadeIn" className="space-y-4">
                <h3 className="text-lg font-medium text-textPrimary mb-2">
                  {translate(`shortcuts.${activeCategory}`, activeCategory)}
                </h3>
                
                <div className="space-y-2">
                  {shortcuts[activeCategory].map((shortcut, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-textPrimary">
                        {translate(`shortcuts.${shortcut.label}`, shortcut.label)}
                      </span>
                      <span className="flex space-x-1">
                        {shortcut.key.split(',').map((key, keyIndex) => (
                          <span
                            key={keyIndex}
                            className="inline-flex items-center justify-center px-2 py-1 rounded bg-background border border-divider text-textSecondary text-sm font-mono"
                          >
                            {formatKey(key)}
                          </span>
                        ))}
                      </span>
                    </div>
                  ))}
                </div>
              </Animated>
            )}
          </div>
        </div>
        
        {/* Footer */}
        <div className="bg-background p-4 border-t border-divider">
          <p className="text-textSecondary text-sm">
            {translate('accessibility.pressShiftQuestion', 'Press Shift + ? to open this dialog')}
          </p>
        </div>
      </Animated>
    </div>
  );
};

KeyboardShortcuts.propTypes = {
  shortcuts: PropTypes.object.isRequired,
  showHelp: PropTypes.bool,
  onShowHelpChange: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default KeyboardShortcuts;
