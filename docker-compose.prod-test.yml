version: '3.8'

services:
  # MongoDB for data storage
  mongodb:
    image: mongo:latest
    volumes:
      - mongodb_prod_test_data:/data/db
    ports:
      - "27018:27017"
    healthcheck:
      test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 3
    environment:
      - MONGO_INITDB_ROOT_USERNAME=novafuse
      - MONGO_INITDB_ROOT_PASSWORD=novafuse_test
    command: ["--auth"]

  # Redis for caching and pub/sub
  redis:
    image: redis:alpine
    ports:
      - "6380:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    command: ["redis-server", "--requirepass", "novafuse_test"]

  # NovaFuse API
  novafuse-api:
    build:
      context: .
      dockerfile: Dockerfile.prod
      args:
        - NODE_ENV=production
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - MONGODB_URI=*****************************************************************
      - REDIS_URI=redis://:novafuse_test@redis:6379
      - LOG_LEVEL=info
      - ENABLE_METRICS=true
      - QUANTUM_CONFIG=default
      - TRINITY_CSDE_ENABLED=true
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost:3000/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Prometheus for metrics
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_prod_test_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    depends_on:
      - novafuse-api

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3003:3000"
    volumes:
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - grafana_prod_test_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=novafuse_test
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus

  # NovaFuse Test Runner
  novafuse-test:
    build:
      context: .
      dockerfile: Dockerfile.test
      args:
        - NODE_ENV=test
    volumes:
      - ./:/app
      - /app/node_modules
      - ./test-results:/app/test-results
    environment:
      - NODE_ENV=test
      - TEST_MODE=market_readiness
      - API_URL=http://novafuse-api:3000
      - MONGODB_URI=*****************************************************************
      - REDIS_URI=redis://:novafuse_test@redis:6379
      - PROMETHEUS_URL=http://prometheus:9090
      - GRAFANA_URL=http://grafana:3000
    depends_on:
      novafuse-api:
        condition: service_healthy
      prometheus:
        condition: service_started
      grafana:
        condition: service_started
    command: ["npm", "run", "test:wait-and-verify"]

volumes:
  mongodb_prod_test_data:
  prometheus_prod_test_data:
  grafana_prod_test_data:
