{"name": "novafuse-universal-api-connector", "version": "1.0.0", "description": "NovaFuse Universal API Connector for plug-and-play integration", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest"}, "keywords": ["api", "connector", "integration", "novafuse", "universal"], "author": "NovaGRC", "license": "UNLICENSED", "dependencies": {"ajv": "^8.12.0", "ajv-formats": "^2.1.1", "axios": "^1.6.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "jsonpath": "^1.1.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1"}}