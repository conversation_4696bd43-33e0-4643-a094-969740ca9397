# NovaFuse Universal Platform Master Testing Script

# Function to display colored output
function Write-ColorOutput {
    param(
        [string]$Text,
        [string]$ForegroundColor = "White"
    )
    
    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Text
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Create directories for reports
Write-ColorOutput "Creating directories for test reports..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "./test-reports" | Out-Null
New-Item -ItemType Directory -Force -Path "./security-reports" | Out-Null
New-Item -ItemType Directory -Force -Path "./performance-reports" | Out-Null
New-Item -ItemType Directory -Force -Path "./compliance-reports" | Out-Null

# Display welcome message
Write-ColorOutput "NovaFuse Universal Platform Master Testing Script" -ForegroundColor Cyan
Write-ColorOutput "=================================================" -ForegroundColor Cyan
Write-ColorOutput "This script will run all tests for the NovaFuse Universal Platform:" -ForegroundColor Cyan
Write-ColorOutput "1. Functional Tests" -ForegroundColor Cyan
Write-ColorOutput "2. Security Tests" -ForegroundColor Cyan
Write-ColorOutput "3. Compliance Tests" -ForegroundColor Cyan
Write-ColorOutput "4. Performance Tests" -ForegroundColor Cyan
Write-ColorOutput "" -ForegroundColor Cyan

# Ask for confirmation
$confirmation = Read-Host "Do you want to proceed? (y/n)"
if ($confirmation -ne 'y') {
    Write-ColorOutput "Testing aborted." -ForegroundColor Yellow
    exit 0
}

# Start time measurement
$startTime = Get-Date

# Step 1: Run functional tests
Write-ColorOutput "`n[1/4] Running functional tests..." -ForegroundColor Green
try {
    npm run test:unit
    npm run test:integration
    npm run test:e2e
    Write-ColorOutput "Functional tests completed successfully." -ForegroundColor Green
} catch {
    Write-ColorOutput "Error running functional tests: $_" -ForegroundColor Red
}

# Step 2: Run security tests
Write-ColorOutput "`n[2/4] Running security tests..." -ForegroundColor Green
try {
    & .\run-security-tests.ps1
    Write-ColorOutput "Security tests completed successfully." -ForegroundColor Green
} catch {
    Write-ColorOutput "Error running security tests: $_" -ForegroundColor Red
}

# Step 3: Run compliance tests
Write-ColorOutput "`n[3/4] Running compliance tests..." -ForegroundColor Green
try {
    & .\run-compliance-tests.ps1
    Write-ColorOutput "Compliance tests completed successfully." -ForegroundColor Green
} catch {
    Write-ColorOutput "Error running compliance tests: $_" -ForegroundColor Red
}

# Step 4: Run performance tests
Write-ColorOutput "`n[4/4] Running performance tests..." -ForegroundColor Green
try {
    & .\run-performance-tests.ps1
    Write-ColorOutput "Performance tests completed successfully." -ForegroundColor Green
} catch {
    Write-ColorOutput "Error running performance tests: $_" -ForegroundColor Red
}

# Generate master report
Write-ColorOutput "`nGenerating master test report..." -ForegroundColor Yellow
try {
    node tools/generate-master-report.js
    Write-ColorOutput "Master test report generated successfully." -ForegroundColor Green
} catch {
    Write-ColorOutput "Error generating master test report: $_" -ForegroundColor Red
}

# Calculate total time
$endTime = Get-Date
$totalTime = $endTime - $startTime
$formattedTime = "{0:hh\:mm\:ss}" -f $totalTime

# Display summary
Write-ColorOutput "`nTesting completed in $formattedTime!" -ForegroundColor Green
Write-ColorOutput "Reports are available in:" -ForegroundColor Green
Write-ColorOutput "- Functional Tests: ./test-reports/functional-test-report.html" -ForegroundColor Green
Write-ColorOutput "- Security Tests: ./security-reports/security-report.html" -ForegroundColor Green
Write-ColorOutput "- Compliance Tests: ./compliance-reports/compliance-summary-report.html" -ForegroundColor Green
Write-ColorOutput "- Performance Tests: ./performance-reports/performance-report.html" -ForegroundColor Green
Write-ColorOutput "- Master Report: ./test-reports/master-report.html" -ForegroundColor Green
