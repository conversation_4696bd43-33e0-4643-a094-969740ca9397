/**
 * FocusableContainer Component
 * 
 * A component that manages focus for a group of focusable elements.
 */

import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { handleKeyboardNavigation } from '../utils/accessibility';

/**
 * FocusableContainer component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Container content
 * @param {string} [props.orientation='vertical'] - Container orientation (vertical, horizontal, grid)
 * @param {boolean} [props.loop=true] - Whether focus should loop around when reaching the end
 * @param {boolean} [props.autoFocus=false] - Whether to auto-focus the first focusable element
 * @param {string} [props.role='group'] - ARIA role for the container
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} FocusableContainer component
 */
const FocusableContainer = ({
  children,
  orientation = 'vertical',
  loop = true,
  autoFocus = false,
  role = 'group',
  className = '',
  style = {}
}) => {
  const containerRef = useRef(null);
  const [focusableElements, setFocusableElements] = useState([]);
  const [currentFocusIndex, setCurrentFocusIndex] = useState(-1);
  
  // Get all focusable elements within the container
  useEffect(() => {
    if (containerRef.current) {
      const elements = Array.from(
        containerRef.current.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        )
      ).filter(
        element => !element.hasAttribute('disabled') && !element.getAttribute('aria-hidden')
      );
      
      setFocusableElements(elements);
      
      // Auto-focus the first element if enabled
      if (autoFocus && elements.length > 0) {
        elements[0].focus();
        setCurrentFocusIndex(0);
      }
    }
  }, [autoFocus, children]);
  
  // Update current focus index when focus changes
  useEffect(() => {
    const handleFocusChange = () => {
      const focusedElement = document.activeElement;
      const index = focusableElements.indexOf(focusedElement);
      
      if (index !== -1) {
        setCurrentFocusIndex(index);
      }
    };
    
    // Add event listeners
    document.addEventListener('focusin', handleFocusChange);
    
    return () => {
      document.removeEventListener('focusin', handleFocusChange);
    };
  }, [focusableElements]);
  
  // Handle keyboard navigation
  const handleKeyDown = (event) => {
    if (focusableElements.length === 0 || currentFocusIndex === -1) {
      return;
    }
    
    // Define navigation functions
    const focusNext = () => {
      const nextIndex = currentFocusIndex + 1;
      
      if (nextIndex < focusableElements.length) {
        focusableElements[nextIndex].focus();
      } else if (loop) {
        focusableElements[0].focus();
      }
    };
    
    const focusPrevious = () => {
      const prevIndex = currentFocusIndex - 1;
      
      if (prevIndex >= 0) {
        focusableElements[prevIndex].focus();
      } else if (loop) {
        focusableElements[focusableElements.length - 1].focus();
      }
    };
    
    const focusFirst = () => {
      if (focusableElements.length > 0) {
        focusableElements[0].focus();
      }
    };
    
    const focusLast = () => {
      if (focusableElements.length > 0) {
        focusableElements[focusableElements.length - 1].focus();
      }
    };
    
    // Handle keyboard navigation based on orientation
    handleKeyboardNavigation(event, {
      onArrowDown: orientation === 'vertical' || orientation === 'grid' ? (e) => {
        e.preventDefault();
        focusNext();
      } : undefined,
      
      onArrowUp: orientation === 'vertical' || orientation === 'grid' ? (e) => {
        e.preventDefault();
        focusPrevious();
      } : undefined,
      
      onArrowRight: orientation === 'horizontal' || orientation === 'grid' ? (e) => {
        e.preventDefault();
        focusNext();
      } : undefined,
      
      onArrowLeft: orientation === 'horizontal' || orientation === 'grid' ? (e) => {
        e.preventDefault();
        focusPrevious();
      } : undefined,
      
      onHome: (e) => {
        e.preventDefault();
        focusFirst();
      },
      
      onEnd: (e) => {
        e.preventDefault();
        focusLast();
      }
    });
  };
  
  return (
    <div
      ref={containerRef}
      role={role}
      className={className}
      style={style}
      onKeyDown={handleKeyDown}
      data-testid="focusable-container"
      data-orientation={orientation}
    >
      {children}
    </div>
  );
};

FocusableContainer.propTypes = {
  children: PropTypes.node.isRequired,
  orientation: PropTypes.oneOf(['vertical', 'horizontal', 'grid']),
  loop: PropTypes.bool,
  autoFocus: PropTypes.bool,
  role: PropTypes.string,
  className: PropTypes.string,
  style: PropTypes.object
};

export default FocusableContainer;
