/**
 * Privacy Impact Assessment Routes
 * 
 * Routes for managing privacy impact assessments (PIAs).
 */

const express = require('express');
const { check } = require('express-validator');
const PrivacyImpactAssessmentController = require('../controllers/PrivacyImpactAssessmentController');
const auth = require('../../../middleware/auth');

const router = express.Router();

/**
 * @route   GET /api/privacy/management/pia
 * @desc    Get all privacy impact assessments
 * @access  Private
 */
router.get('/', auth, PrivacyImpactAssessmentController.getAllPrivacyImpactAssessments);

/**
 * @route   GET /api/privacy/management/pia/:id
 * @desc    Get a single privacy impact assessment
 * @access  Private
 */
router.get('/:id', auth, PrivacyImpactAssessmentController.getPrivacyImpactAssessment);

/**
 * @route   POST /api/privacy/management/pia
 * @desc    Create a new privacy impact assessment
 * @access  Private
 */
router.post('/', [
  auth,
  [
    check('name', 'Name is required').not().isEmpty(),
    check('description', 'Description is required').not().isEmpty(),
    check('processingActivity', 'Processing activity is required').not().isEmpty(),
    check('initiatedBy', 'Initiator is required').not().isEmpty()
  ]
], PrivacyImpactAssessmentController.createPrivacyImpactAssessment);

/**
 * @route   PUT /api/privacy/management/pia/:id
 * @desc    Update a privacy impact assessment
 * @access  Private
 */
router.put('/:id', [
  auth,
  [
    check('name', 'Name is required').optional().not().isEmpty(),
    check('description', 'Description is required').optional().not().isEmpty(),
    check('status', 'Invalid status').optional().isIn([
      'Draft', 'In Progress', 'Completed', 'Approved', 'Rejected', 'Review Required'
    ])
  ]
], PrivacyImpactAssessmentController.updatePrivacyImpactAssessment);

/**
 * @route   DELETE /api/privacy/management/pia/:id
 * @desc    Delete a privacy impact assessment
 * @access  Private
 */
router.delete('/:id', auth, PrivacyImpactAssessmentController.deletePrivacyImpactAssessment);

/**
 * @route   POST /api/privacy/management/pia/:id/risks
 * @desc    Add a risk to a privacy impact assessment
 * @access  Private
 */
router.post('/:id/risks', [
  auth,
  [
    check('description', 'Description is required').not().isEmpty(),
    check('likelihood', 'Likelihood is required').not().isEmpty(),
    check('impact', 'Impact is required').not().isEmpty(),
    check('riskLevel', 'Risk level is required').not().isEmpty()
  ]
], PrivacyImpactAssessmentController.addRisk);

/**
 * @route   PUT /api/privacy/management/pia/:id/risks/:riskId
 * @desc    Update a risk in a privacy impact assessment
 * @access  Private
 */
router.put('/:id/risks/:riskId', [
  auth,
  [
    check('description', 'Description is required').optional().not().isEmpty(),
    check('likelihood', 'Likelihood is required').optional().not().isEmpty(),
    check('impact', 'Impact is required').optional().not().isEmpty(),
    check('riskLevel', 'Risk level is required').optional().not().isEmpty()
  ]
], PrivacyImpactAssessmentController.updateRisk);

/**
 * @route   DELETE /api/privacy/management/pia/:id/risks/:riskId
 * @desc    Delete a risk from a privacy impact assessment
 * @access  Private
 */
router.delete('/:id/risks/:riskId', auth, PrivacyImpactAssessmentController.deleteRisk);

/**
 * @route   POST /api/privacy/management/pia/:id/mitigations
 * @desc    Add a mitigation action to a privacy impact assessment
 * @access  Private
 */
router.post('/:id/mitigations', [
  auth,
  [
    check('action', 'Action is required').not().isEmpty(),
    check('description', 'Description is required').not().isEmpty(),
    check('owner', 'Owner is required').not().isEmpty(),
    check('dueDate', 'Due date is required').not().isEmpty()
  ]
], PrivacyImpactAssessmentController.addMitigation);

/**
 * @route   PUT /api/privacy/management/pia/:id/mitigations/:mitigationId
 * @desc    Update a mitigation action in a privacy impact assessment
 * @access  Private
 */
router.put('/:id/mitigations/:mitigationId', [
  auth,
  [
    check('action', 'Action is required').optional().not().isEmpty(),
    check('description', 'Description is required').optional().not().isEmpty(),
    check('status', 'Invalid status').optional().isIn([
      'Planned', 'In Progress', 'Completed', 'Delayed', 'Cancelled'
    ])
  ]
], PrivacyImpactAssessmentController.updateMitigation);

/**
 * @route   DELETE /api/privacy/management/pia/:id/mitigations/:mitigationId
 * @desc    Delete a mitigation action from a privacy impact assessment
 * @access  Private
 */
router.delete('/:id/mitigations/:mitigationId', auth, PrivacyImpactAssessmentController.deleteMitigation);

/**
 * @route   POST /api/privacy/management/pia/:id/conclusion
 * @desc    Add or update the conclusion of a privacy impact assessment
 * @access  Private
 */
router.post('/:id/conclusion', [
  auth,
  [
    check('summary', 'Summary is required').not().isEmpty(),
    check('recommendation', 'Recommendation is required').not().isEmpty(),
    check('justification', 'Justification is required').not().isEmpty()
  ]
], PrivacyImpactAssessmentController.updateConclusion);

/**
 * @route   POST /api/privacy/management/pia/:id/approvals
 * @desc    Add an approval to a privacy impact assessment
 * @access  Private
 */
router.post('/:id/approvals', [
  auth,
  [
    check('role', 'Role is required').not().isEmpty(),
    check('user', 'User is required').not().isEmpty(),
    check('decision', 'Decision is required').not().isEmpty()
  ]
], PrivacyImpactAssessmentController.addApproval);

/**
 * @route   GET /api/privacy/management/pia/:id/risk-level
 * @desc    Calculate the overall risk level of a privacy impact assessment
 * @access  Private
 */
router.get('/:id/risk-level', auth, PrivacyImpactAssessmentController.calculateRiskLevel);

/**
 * @route   GET /api/privacy/management/pia/:id/consultation-required
 * @desc    Check if supervisory authority consultation is required
 * @access  Private
 */
router.get('/:id/consultation-required', auth, PrivacyImpactAssessmentController.checkConsultationRequired);

module.exports = router;
