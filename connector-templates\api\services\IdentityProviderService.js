/**
 * Identity Provider Service
 * 
 * This service handles identity provider configuration and integration for SSO.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const AuditService = require('./AuditService');
const crypto = require('crypto');

class IdentityProviderService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.providersFile = path.join(this.dataDir, 'identity_providers.json');
    this.auditService = new AuditService(dataDir);
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
      
      // Initialize files if they don't exist
      await this.initializeFile(this.providersFile, []);
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get all identity providers
   */
  async getAllProviders() {
    const providers = await this.loadData(this.providersFile);
    
    // Remove sensitive data
    return providers.map(provider => this.sanitizeProvider(provider));
  }

  /**
   * Get identity provider by ID
   */
  async getProviderById(id) {
    const providers = await this.loadData(this.providersFile);
    const provider = providers.find(p => p.id === id);
    
    if (!provider) {
      throw new NotFoundError(`Identity provider with ID ${id} not found`);
    }
    
    return this.sanitizeProvider(provider);
  }

  /**
   * Get identity provider by domain
   */
  async getProviderByDomain(domain) {
    const providers = await this.loadData(this.providersFile);
    const provider = providers.find(p => 
      p.domains && p.domains.some(d => d.toLowerCase() === domain.toLowerCase())
    );
    
    if (!provider) {
      return null;
    }
    
    return this.sanitizeProvider(provider);
  }

  /**
   * Create a new identity provider
   */
  async createProvider(data, userId) {
    if (!data.name) {
      throw new ValidationError('Provider name is required');
    }
    
    if (!data.type) {
      throw new ValidationError('Provider type is required');
    }
    
    if (!['saml', 'oidc'].includes(data.type.toLowerCase())) {
      throw new ValidationError('Provider type must be either "saml" or "oidc"');
    }
    
    // Validate type-specific required fields
    if (data.type.toLowerCase() === 'saml') {
      if (!data.entityId) {
        throw new ValidationError('Entity ID is required for SAML providers');
      }
      
      if (!data.ssoUrl) {
        throw new ValidationError('SSO URL is required for SAML providers');
      }
      
      if (!data.x509cert) {
        throw new ValidationError('X.509 certificate is required for SAML providers');
      }
    } else if (data.type.toLowerCase() === 'oidc') {
      if (!data.clientId) {
        throw new ValidationError('Client ID is required for OIDC providers');
      }
      
      if (!data.clientSecret) {
        throw new ValidationError('Client secret is required for OIDC providers');
      }
      
      if (!data.discoveryUrl) {
        throw new ValidationError('Discovery URL is required for OIDC providers');
      }
    }
    
    const providers = await this.loadData(this.providersFile);
    
    // Check for duplicate name
    if (providers.some(p => p.name.toLowerCase() === data.name.toLowerCase())) {
      throw new ValidationError(`Provider with name "${data.name}" already exists`);
    }
    
    // Generate client secret if not provided for OIDC
    if (data.type.toLowerCase() === 'oidc' && !data.clientSecret) {
      data.clientSecret = crypto.randomBytes(32).toString('hex');
    }
    
    // Create new provider
    const newProvider = {
      id: uuidv4(),
      name: data.name,
      description: data.description || '',
      type: data.type.toLowerCase(),
      enabled: data.enabled !== undefined ? data.enabled : true,
      domains: data.domains || [],
      attributeMapping: data.attributeMapping || {
        email: 'email',
        firstName: 'given_name',
        lastName: 'family_name',
        username: 'preferred_username'
      },
      createdBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    // Add type-specific fields
    if (newProvider.type === 'saml') {
      newProvider.entityId = data.entityId;
      newProvider.ssoUrl = data.ssoUrl;
      newProvider.sloUrl = data.sloUrl || null;
      newProvider.x509cert = data.x509cert;
      newProvider.signatureAlgorithm = data.signatureAlgorithm || 'sha256';
      newProvider.digestAlgorithm = data.digestAlgorithm || 'sha256';
      newProvider.nameIdFormat = data.nameIdFormat || 'urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress';
    } else if (newProvider.type === 'oidc') {
      newProvider.clientId = data.clientId;
      newProvider.clientSecret = data.clientSecret;
      newProvider.discoveryUrl = data.discoveryUrl;
      newProvider.scope = data.scope || 'openid profile email';
      newProvider.responseType = data.responseType || 'code';
      newProvider.grantType = data.grantType || 'authorization_code';
    }
    
    providers.push(newProvider);
    await this.saveData(this.providersFile, providers);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'CREATE',
      resourceType: 'identity_provider',
      resourceId: newProvider.id,
      details: {
        name: newProvider.name,
        type: newProvider.type
      }
    });
    
    return this.sanitizeProvider(newProvider);
  }

  /**
   * Update an identity provider
   */
  async updateProvider(id, data, userId) {
    const providers = await this.loadData(this.providersFile);
    const index = providers.findIndex(p => p.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Identity provider with ID ${id} not found`);
    }
    
    const provider = providers[index];
    
    // Check for duplicate name if name is being changed
    if (data.name && data.name !== provider.name && 
        providers.some(p => p.id !== id && p.name.toLowerCase() === data.name.toLowerCase())) {
      throw new ValidationError(`Provider with name "${data.name}" already exists`);
    }
    
    // Update provider
    const updatedProvider = {
      ...provider,
      ...data,
      id, // Don't allow changing the ID
      type: provider.type, // Don't allow changing the type
      createdBy: provider.createdBy, // Don't allow changing the creator
      updated: new Date().toISOString()
    };
    
    providers[index] = updatedProvider;
    await this.saveData(this.providersFile, providers);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UPDATE',
      resourceType: 'identity_provider',
      resourceId: id,
      details: {
        name: updatedProvider.name,
        enabled: updatedProvider.enabled
      }
    });
    
    return this.sanitizeProvider(updatedProvider);
  }

  /**
   * Delete an identity provider
   */
  async deleteProvider(id, userId) {
    const providers = await this.loadData(this.providersFile);
    const index = providers.findIndex(p => p.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Identity provider with ID ${id} not found`);
    }
    
    const provider = providers[index];
    
    // Remove the provider
    providers.splice(index, 1);
    await this.saveData(this.providersFile, providers);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'DELETE',
      resourceType: 'identity_provider',
      resourceId: id,
      details: {
        name: provider.name,
        type: provider.type
      }
    });
    
    return { success: true, message: `Identity provider ${id} deleted` };
  }

  /**
   * Test an identity provider connection
   */
  async testProviderConnection(id, userId) {
    const provider = await this.getProviderById(id);
    
    // Implement provider-specific connection testing
    let testResult = { success: false, message: 'Not implemented' };
    
    if (provider.type === 'saml') {
      // Test SAML connection
      testResult = await this.testSamlConnection(provider);
    } else if (provider.type === 'oidc') {
      // Test OIDC connection
      testResult = await this.testOidcConnection(provider);
    }
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'TEST',
      resourceType: 'identity_provider',
      resourceId: id,
      details: {
        name: provider.name,
        type: provider.type,
        success: testResult.success
      }
    });
    
    return testResult;
  }

  /**
   * Test SAML connection
   */
  async testSamlConnection(provider) {
    try {
      // In a real implementation, this would validate the SAML configuration
      // by making a request to the identity provider's metadata URL
      
      // For now, just check if required fields are present
      if (!provider.entityId || !provider.ssoUrl || !provider.x509cert) {
        return {
          success: false,
          message: 'Missing required SAML configuration'
        };
      }
      
      return {
        success: true,
        message: 'SAML connection test successful'
      };
    } catch (error) {
      return {
        success: false,
        message: `SAML connection test failed: ${error.message}`
      };
    }
  }

  /**
   * Test OIDC connection
   */
  async testOidcConnection(provider) {
    try {
      // In a real implementation, this would validate the OIDC configuration
      // by making a request to the discovery URL and validating the client credentials
      
      // For now, just check if required fields are present
      if (!provider.clientId || !provider.discoveryUrl) {
        return {
          success: false,
          message: 'Missing required OIDC configuration'
        };
      }
      
      return {
        success: true,
        message: 'OIDC connection test successful'
      };
    } catch (error) {
      return {
        success: false,
        message: `OIDC connection test failed: ${error.message}`
      };
    }
  }

  /**
   * Generate SAML metadata
   */
  async generateSamlMetadata(id) {
    const provider = await this.getProviderById(id);
    
    if (provider.type !== 'saml') {
      throw new ValidationError('Provider is not a SAML provider');
    }
    
    // In a real implementation, this would generate SAML metadata XML
    // based on the provider configuration
    
    const metadata = `<?xml version="1.0"?>
<EntityDescriptor xmlns="urn:oasis:names:tc:SAML:2.0:metadata" entityID="${provider.entityId}">
  <SPSSODescriptor AuthnRequestsSigned="true" WantAssertionsSigned="true" protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
    <NameIDFormat>${provider.nameIdFormat}</NameIDFormat>
    <AssertionConsumerService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST" Location="https://example.com/api/auth/saml/callback" index="1"/>
  </SPSSODescriptor>
</EntityDescriptor>`;
    
    return { metadata };
  }

  /**
   * Get OIDC configuration
   */
  async getOidcConfiguration(id) {
    const provider = await this.getProviderById(id);
    
    if (provider.type !== 'oidc') {
      throw new ValidationError('Provider is not an OIDC provider');
    }
    
    // In a real implementation, this would fetch the OIDC configuration
    // from the discovery URL
    
    const configuration = {
      authorization_endpoint: 'https://example.com/oauth2/authorize',
      token_endpoint: 'https://example.com/oauth2/token',
      userinfo_endpoint: 'https://example.com/oauth2/userinfo',
      jwks_uri: 'https://example.com/oauth2/jwks',
      scopes_supported: ['openid', 'profile', 'email'],
      response_types_supported: ['code', 'token', 'id_token', 'code token', 'code id_token', 'token id_token', 'code token id_token'],
      grant_types_supported: ['authorization_code', 'implicit', 'refresh_token'],
      subject_types_supported: ['public', 'pairwise'],
      id_token_signing_alg_values_supported: ['RS256', 'ES256', 'HS256']
    };
    
    return { configuration };
  }

  /**
   * Sanitize provider data for external use
   */
  sanitizeProvider(provider) {
    const sanitized = { ...provider };
    
    // Remove sensitive data
    if (sanitized.type === 'oidc') {
      delete sanitized.clientSecret;
    }
    
    return sanitized;
  }
}

module.exports = IdentityProviderService;
