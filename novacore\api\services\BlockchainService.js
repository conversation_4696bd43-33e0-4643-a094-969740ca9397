/**
 * NovaCore Blockchain Service
 * 
 * This service provides functionality for blockchain verification of evidence.
 */

const { BlockchainVerification, Evidence } = require('../models');
const logger = require('../../config/logger');
const config = require('../../config');
const { NotFoundError } = require('../utils/errors');
const crypto = require('crypto');
const axios = require('axios');

class BlockchainService {
  constructor() {
    this.provider = config.blockchain.provider;
    this.apiKey = config.blockchain.apiKey;
    this.apiUrl = config.blockchain.apiUrl;
    this.contractAddress = config.blockchain.contractAddress;
  }
  
  /**
   * Verify evidence on blockchain
   * @param {string} evidenceId - Evidence ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Blockchain verification record
   */
  async verifyEvidence(evidenceId, userId) {
    try {
      logger.info('Verifying evidence on blockchain', { evidenceId });
      
      // Get evidence
      const evidence = await Evidence.findById(evidenceId);
      
      if (!evidence) {
        throw new NotFoundError(`Evidence with ID ${evidenceId} not found`);
      }
      
      // Check if evidence is already verified
      const existingVerification = await BlockchainVerification.findByEvidenceId(evidenceId);
      
      if (existingVerification && existingVerification.isConfirmed()) {
        logger.info('Evidence already verified on blockchain', { evidenceId });
        return existingVerification;
      }
      
      // Generate hash for evidence
      const hash = this._generateEvidenceHash(evidence);
      
      // Create blockchain verification record
      const verification = new BlockchainVerification({
        evidenceId,
        hash,
        provider: this.provider,
        status: 'pending',
        createdBy: userId
      });
      
      await verification.save();
      
      // Submit hash to blockchain
      const transaction = await this._submitToBlockchain(hash);
      
      // Update verification record with transaction details
      verification.status = 'submitted';
      verification.transaction = transaction;
      await verification.save();
      
      // Update evidence verification status
      evidence.verification.status = 'pending';
      evidence.verification.method = 'blockchain';
      evidence.verification.blockchainVerificationId = verification._id;
      await evidence.save();
      
      logger.info('Evidence submitted for blockchain verification', { 
        evidenceId, 
        verificationId: verification._id,
        txHash: transaction.txHash 
      });
      
      return verification;
    } catch (error) {
      logger.error('Error verifying evidence on blockchain', { evidenceId, error });
      throw error;
    }
  }
  
  /**
   * Check verification status
   * @param {string} verificationId - Blockchain verification ID
   * @returns {Promise<Object>} - Updated verification record
   */
  async checkVerificationStatus(verificationId) {
    try {
      logger.info('Checking blockchain verification status', { verificationId });
      
      // Get verification record
      const verification = await BlockchainVerification.findById(verificationId);
      
      if (!verification) {
        throw new NotFoundError(`Blockchain verification with ID ${verificationId} not found`);
      }
      
      // Skip if already confirmed or failed
      if (verification.status === 'confirmed' || verification.status === 'failed') {
        return verification;
      }
      
      // Check transaction status
      if (verification.transaction && verification.transaction.txHash) {
        const txStatus = await this._checkTransactionStatus(verification.transaction.txHash);
        
        // Update verification record
        verification.transaction = {
          ...verification.transaction,
          ...txStatus
        };
        
        // Update status
        if (txStatus.status === 'confirmed') {
          verification.status = 'confirmed';
          
          // Update evidence verification status
          const evidence = await Evidence.findById(verification.evidenceId);
          if (evidence) {
            evidence.verification.status = 'verified';
            evidence.status = 'verified';
            await evidence.save();
          }
        } else if (txStatus.status === 'failed') {
          verification.status = 'failed';
        }
        
        await verification.save();
      }
      
      return verification;
    } catch (error) {
      logger.error('Error checking blockchain verification status', { verificationId, error });
      throw error;
    }
  }
  
  /**
   * Get verification by ID
   * @param {string} id - Verification ID
   * @returns {Promise<Object>} - Verification record
   */
  async getVerificationById(id) {
    try {
      const verification = await BlockchainVerification.findById(id);
      
      if (!verification) {
        throw new NotFoundError(`Blockchain verification with ID ${id} not found`);
      }
      
      return verification;
    } catch (error) {
      logger.error('Error getting blockchain verification by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Get verification by evidence ID
   * @param {string} evidenceId - Evidence ID
   * @returns {Promise<Object>} - Verification record
   */
  async getVerificationByEvidenceId(evidenceId) {
    try {
      const verification = await BlockchainVerification.findByEvidenceId(evidenceId);
      
      if (!verification) {
        throw new NotFoundError(`Blockchain verification for evidence ID ${evidenceId} not found`);
      }
      
      return verification;
    } catch (error) {
      logger.error('Error getting blockchain verification by evidence ID', { evidenceId, error });
      throw error;
    }
  }
  
  /**
   * Verify hash against blockchain
   * @param {string} hash - Hash to verify
   * @returns {Promise<Object>} - Verification result
   */
  async verifyHash(hash) {
    try {
      logger.info('Verifying hash against blockchain', { hash });
      
      // Check if hash exists in blockchain
      const verification = await BlockchainVerification.findByHash(hash);
      
      if (!verification) {
        return { verified: false, reason: 'Hash not found in blockchain records' };
      }
      
      // Check if verification is confirmed
      if (verification.status !== 'confirmed') {
        return { verified: false, reason: `Verification status is ${verification.status}` };
      }
      
      // Verify on blockchain
      const result = await this._verifyHashOnBlockchain(hash);
      
      return {
        verified: result.verified,
        reason: result.reason,
        verification: {
          id: verification._id,
          evidenceId: verification.evidenceId,
          timestamp: verification.transaction.timestamp,
          blockNumber: verification.transaction.blockNumber,
          txHash: verification.transaction.txHash
        }
      };
    } catch (error) {
      logger.error('Error verifying hash against blockchain', { hash, error });
      throw error;
    }
  }
  
  /**
   * Generate hash for evidence
   * @param {Object} evidence - Evidence record
   * @returns {string} - Evidence hash
   * @private
   */
  _generateEvidenceHash(evidence) {
    // Create a deterministic representation of the evidence
    const content = {
      id: evidence._id.toString(),
      type: evidence.type,
      category: evidence.category,
      contentHash: evidence.content.hash,
      createdAt: evidence.createdAt.toISOString()
    };
    
    return crypto.createHash('sha256').update(JSON.stringify(content)).digest('hex');
  }
  
  /**
   * Submit hash to blockchain
   * @param {string} hash - Hash to submit
   * @returns {Promise<Object>} - Transaction details
   * @private
   */
  async _submitToBlockchain(hash) {
    // In a real implementation, this would interact with a blockchain
    // For now, we'll simulate the process
    
    logger.info('Submitting hash to blockchain', { hash });
    
    // Simulate blockchain transaction
    const txHash = `0x${crypto.randomBytes(32).toString('hex')}`;
    const blockNumber = Math.floor(Math.random() * 1000000) + 10000000;
    const blockHash = `0x${crypto.randomBytes(32).toString('hex')}`;
    const timestamp = new Date();
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      txHash,
      blockNumber,
      blockHash,
      timestamp,
      from: '******************************************',
      to: this.contractAddress || '******************************************',
      status: 'pending',
      confirmations: 0,
      network: 'mainnet'
    };
  }
  
  /**
   * Check transaction status
   * @param {string} txHash - Transaction hash
   * @returns {Promise<Object>} - Transaction status
   * @private
   */
  async _checkTransactionStatus(txHash) {
    // In a real implementation, this would check the status on the blockchain
    // For now, we'll simulate the process
    
    logger.info('Checking transaction status', { txHash });
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Simulate 80% success rate
    const success = Math.random() < 0.8;
    
    if (success) {
      return {
        status: 'confirmed',
        blockNumber: Math.floor(Math.random() * 1000000) + 10000000,
        blockHash: `0x${crypto.randomBytes(32).toString('hex')}`,
        confirmations: Math.floor(Math.random() * 10) + 1,
        gasUsed: Math.floor(Math.random() * 100000) + 50000
      };
    } else {
      return {
        status: 'failed',
        error: 'Transaction reverted'
      };
    }
  }
  
  /**
   * Verify hash on blockchain
   * @param {string} hash - Hash to verify
   * @returns {Promise<Object>} - Verification result
   * @private
   */
  async _verifyHashOnBlockchain(hash) {
    // In a real implementation, this would verify the hash on the blockchain
    // For now, we'll simulate the process
    
    logger.info('Verifying hash on blockchain', { hash });
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Simulate 90% success rate
    const verified = Math.random() < 0.9;
    
    return {
      verified,
      reason: verified ? 'Hash verified on blockchain' : 'Hash not found on blockchain'
    };
  }
}

module.exports = new BlockchainService();
