#!/bin/bash

# NovaConnect Docker Test Runner
# This script runs the NovaConnect tests in a Docker environment

# Set error handling
set -e

# Configuration
DOCKER_COMPOSE_FILE="docker-compose.test.yml"

# Function to display colored output
function echo_color() {
    local color=$1
    local message=$2
    
    case $color in
        "red")
            echo -e "\033[0;31m$message\033[0m"
            ;;
        "green")
            echo -e "\033[0;32m$message\033[0m"
            ;;
        "yellow")
            echo -e "\033[0;33m$message\033[0m"
            ;;
        "cyan")
            echo -e "\033[0;36m$message\033[0m"
            ;;
        *)
            echo "$message"
            ;;
    esac
}

# Function to check if Dock<PERSON> is running
function check_docker_running() {
    if ! docker info > /dev/null 2>&1; then
        echo_color "red" "Error: Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Main execution
echo_color "cyan" "NovaConnect Docker Test Runner"
echo_color "cyan" "=============================="

# Check if Docker is running
check_docker_running

# Check if docker-compose file exists
if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
    echo_color "red" "Error: Docker Compose file '$DOCKER_COMPOSE_FILE' not found."
    exit 1
fi

# Start the Docker environment
echo_color "yellow" "Starting Docker test environment..."
if docker-compose -f $DOCKER_COMPOSE_FILE up -d; then
    echo_color "green" "Docker environment started successfully."
else
    echo_color "red" "Error starting Docker environment."
    exit 1
fi

# Wait for services to be ready
echo_color "yellow" "Waiting for services to be ready..."
sleep 10

# Run the tests
echo_color "yellow" "Running NovaConnect tests..."
if docker-compose -f $DOCKER_COMPOSE_FILE run test-runner; then
    TEST_RESULT=0
    echo_color "green" "All tests passed successfully!"
else
    TEST_RESULT=1
    echo_color "red" "Some tests failed. Check the test report for details."
fi

# Copy test results from the container
echo_color "yellow" "Copying test results..."
CONTAINER_ID=$(docker-compose -f $DOCKER_COMPOSE_FILE ps -q test-runner)
if [ -n "$CONTAINER_ID" ]; then
    docker cp ${CONTAINER_ID}:/app/test-results ./test-results
    docker cp ${CONTAINER_ID}:/app/coverage ./coverage
    echo_color "green" "Test results copied successfully."
else
    echo_color "yellow" "Warning: Could not find test-runner container to copy results."
fi

# Stop the Docker environment
echo_color "yellow" "Stopping Docker test environment..."
if docker-compose -f $DOCKER_COMPOSE_FILE down; then
    echo_color "green" "Docker environment stopped successfully."
else
    echo_color "red" "Error stopping Docker environment."
fi

# Display test report location
echo_color "cyan" "Test Summary:"
echo_color "white" "- Test report: ./test-results/novaconnect-summary.md"
echo_color "white" "- Coverage report: ./coverage/lcov-report/index.html"

# Exit with the test result
exit $TEST_RESULT
