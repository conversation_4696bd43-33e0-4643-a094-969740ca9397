/**
 * NovaFuse Partner SDK - Usage Tracker
 * 
 * This module tracks partner usage for revenue sharing.
 * 
 * @version 1.0.0
 * @license NovaFuse Proprietary
 */

const fs = require('fs');
const path = require('path');

/**
 * Usage Tracker
 * 
 * Tracks partner usage for revenue sharing.
 */
class UsageTracker {
  /**
   * Create a new Usage Tracker instance
   * @param {Object} config - Configuration
   * @param {String} config.partnerId - Partner ID
   * @param {String} config.environment - Environment (production, sandbox)
   */
  constructor(config) {
    this.partnerId = config.partnerId;
    this.environment = config.environment || 'sandbox';
    this.usageData = [];
    
    // In a real implementation, this would connect to a database
    // For now, we'll use a local file for demonstration
    this.usageFile = path.join(__dirname, 'data', `${this.partnerId}_usage.json`);
    
    // Create data directory if it doesn't exist
    const dataDir = path.join(__dirname, 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    // Load existing usage data if available
    this._loadUsageData();
    
    console.log(`Usage Tracker initialized for partner: ${this.partnerId}`);
  }
  
  /**
   * Track usage
   * @param {Object} usage - Usage data
   * @param {String} usage.partnerId - Partner ID
   * @param {String} usage.clientId - Client ID
   * @param {String} usage.operation - Operation (analyze, remediate, report)
   * @param {String} usage.scope - Operation scope
   * @param {String} usage.timestamp - Timestamp
   * @returns {Object} - Tracking result
   */
  async trackUsage(usage) {
    console.log(`Tracking usage: ${usage.operation} for client ${usage.clientId}`);
    
    // Generate usage ID
    const usageId = `USG-${Date.now()}-${Math.floor(Math.random() * 1000000)}`;
    
    // Create usage record
    const usageRecord = {
      id: usageId,
      partnerId: usage.partnerId,
      clientId: usage.clientId,
      operation: usage.operation,
      scope: usage.scope,
      timestamp: usage.timestamp,
      environment: this.environment
    };
    
    // Add to usage data
    this.usageData.push(usageRecord);
    
    // Save usage data
    this._saveUsageData();
    
    return {
      id: usageId,
      recorded: true,
      timestamp: usage.timestamp
    };
  }
  
  /**
   * Get usage information
   * @param {Object} options - Options
   * @param {String} options.startDate - Start date
   * @param {String} options.endDate - End date
   * @param {String} options.clientId - Filter by client ID
   * @param {String} options.operation - Filter by operation
   * @returns {Object} - Usage information
   */
  async getUsageInfo(options = {}) {
    console.log(`Getting usage information for partner: ${this.partnerId}`);
    
    // Filter usage data
    let filteredUsage = [...this.usageData];
    
    if (options.startDate) {
      const startDate = new Date(options.startDate).getTime();
      filteredUsage = filteredUsage.filter(usage => new Date(usage.timestamp).getTime() >= startDate);
    }
    
    if (options.endDate) {
      const endDate = new Date(options.endDate).getTime();
      filteredUsage = filteredUsage.filter(usage => new Date(usage.timestamp).getTime() <= endDate);
    }
    
    if (options.clientId) {
      filteredUsage = filteredUsage.filter(usage => usage.clientId === options.clientId);
    }
    
    if (options.operation) {
      filteredUsage = filteredUsage.filter(usage => usage.operation === options.operation);
    }
    
    // Calculate usage statistics
    const totalUsage = filteredUsage.length;
    
    // Group by operation
    const operationStats = {};
    filteredUsage.forEach(usage => {
      operationStats[usage.operation] = (operationStats[usage.operation] || 0) + 1;
    });
    
    // Group by client
    const clientStats = {};
    filteredUsage.forEach(usage => {
      clientStats[usage.clientId] = (clientStats[usage.clientId] || 0) + 1;
    });
    
    // Group by day
    const dailyStats = {};
    filteredUsage.forEach(usage => {
      const day = usage.timestamp.split('T')[0];
      dailyStats[day] = (dailyStats[day] || 0) + 1;
    });
    
    return {
      totalUsage,
      byOperation: operationStats,
      byClient: clientStats,
      byDay: dailyStats,
      details: filteredUsage
    };
  }
  
  /**
   * Load usage data from file
   * @private
   */
  _loadUsageData() {
    try {
      if (fs.existsSync(this.usageFile)) {
        const data = fs.readFileSync(this.usageFile, 'utf8');
        this.usageData = JSON.parse(data);
        console.log(`Loaded ${this.usageData.length} usage records for partner: ${this.partnerId}`);
      } else {
        this.usageData = [];
        console.log(`No existing usage data for partner: ${this.partnerId}`);
      }
    } catch (error) {
      console.error(`Error loading usage data: ${error.message}`);
      this.usageData = [];
    }
  }
  
  /**
   * Save usage data to file
   * @private
   */
  _saveUsageData() {
    try {
      fs.writeFileSync(this.usageFile, JSON.stringify(this.usageData, null, 2));
      console.log(`Saved ${this.usageData.length} usage records for partner: ${this.partnerId}`);
    } catch (error) {
      console.error(`Error saving usage data: ${error.message}`);
    }
  }
}

module.exports = UsageTracker;
