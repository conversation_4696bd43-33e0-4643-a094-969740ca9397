@echo off
echo ========================================
echo   NovaAlign Studio Deployment Script
echo ========================================
echo.

echo [1/5] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo [2/5] Building the application...
call npm run build
if %errorlevel% neq 0 (
    echo ERROR: Failed to build application
    pause
    exit /b 1
)

echo.
echo [3/5] Building Docker image...
docker build -t novaalign-studio:latest .
if %errorlevel% neq 0 (
    echo ERROR: Failed to build Docker image
    pause
    exit /b 1
)

echo.
echo [4/5] Stopping existing container (if any)...
docker stop novaalign-studio 2>nul
docker rm novaalign-studio 2>nul

echo.
echo [5/5] Starting new container...
docker run -d --name novaalign-studio -p 3004:3000 novaalign-studio:latest
if %errorlevel% neq 0 (
    echo ERROR: Failed to start container
    pause
    exit /b 1
)

echo.
echo ========================================
echo   NovaAlign Studio Deployed Successfully!
echo ========================================
echo.
echo Access NovaAlign Studio at: http://localhost:3004
echo.
echo Container status:
docker ps --filter name=novaalign-studio --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo.
pause
