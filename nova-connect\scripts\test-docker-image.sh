#!/bin/bash
# <PERSON>ript to test the Docker image for NovaConnect UAC

# Set variables
PROJECT_ID=${1:-"novafuse-test"}
IMAGE_NAME=${2:-"novafuse-uac"}
IMAGE_TAG=${3:-"1.0.0"}
FULL_IMAGE_NAME="gcr.io/$PROJECT_ID/$IMAGE_NAME:$IMAGE_TAG"
CONTAINER_NAME="novafuse-uac-test"
MONGODB_CONTAINER="mongodb-test"
REDIS_CONTAINER="redis-test"
NETWORK_NAME="novafuse-test-network"

# Create a Docker network
echo "Creating Docker network $NETWORK_NAME..."
docker network create $NETWORK_NAME || true

# Start MongoDB container
echo "Starting MongoDB container..."
docker run --name $MONGODB_CONTAINER \
  --network $NETWORK_NAME \
  -d mongo:4.4

# Start Redis container
echo "Starting Redis container..."
docker run --name $REDIS_CONTAINER \
  --network $NETWORK_NAME \
  -d redis:6-alpine

# Wait for MongoDB and Redis to start
echo "Waiting for MongoDB and Redis to start..."
sleep 5

# Run the NovaConnect UAC container
echo "Running NovaConnect UAC container..."
docker run --name $CONTAINER_NAME \
  --network $NETWORK_NAME \
  -p 3001:3001 \
  -e NODE_ENV=production \
  -e PORT=3001 \
  -e HOST=0.0.0.0 \
  -e LOG_LEVEL=info \
  -e MONGODB_URI=mongodb://$MONGODB_CONTAINER:27017/novafuse-test \
  -e REDIS_URI=redis://$REDIS_CONTAINER:6379 \
  -e API_KEY=test-api-key-12345 \
  -e JWT_SECRET=test-jwt-secret-12345 \
  -e CORS_ORIGIN=* \
  -e CLUSTER_ENABLED=true \
  -e CACHE_ENABLED=true \
  -e COMPRESSION_ENABLED=true \
  -e RATE_LIMIT_ENABLED=true \
  -e RATE_LIMIT_WINDOW_MS=60000 \
  -e RATE_LIMIT_MAX=100 \
  -e HELMET_ENABLED=true \
  -e CSRF_ENABLED=true \
  -e IP_FILTERING_ENABLED=false \
  -e FEATURE_FLAG_ENABLED=true \
  -e FEATURE_FLAG_SOURCE=file \
  -e DEFAULT_TIER=core \
  -e GCP_PROJECT_ID=$PROJECT_ID \
  -e GCP_REGION=us-central1 \
  -e GCP_MONITORING_ENABLED=false \
  -e GCP_LOGGING_ENABLED=false \
  -e GCP_ERROR_REPORTING_ENABLED=false \
  -e GCP_TRACING_ENABLED=false \
  -e GCP_PROFILING_ENABLED=false \
  -e GCP_SECRET_MANAGER_ENABLED=false \
  -e SERVICE_NAME=novafuse-uac \
  -e SERVICE_VERSION=$IMAGE_TAG \
  -d $FULL_IMAGE_NAME

# Wait for the container to start
echo "Waiting for NovaConnect UAC container to start..."
sleep 10

# Test the health endpoint
echo "Testing health endpoint..."
curl -f http://localhost:3001/health || { echo "Health check failed"; exit 1; }

# Test the metrics endpoint
echo "Testing metrics endpoint..."
curl -f http://localhost:3001/metrics || { echo "Metrics check failed"; exit 1; }

# Test the API endpoints
echo "Testing API endpoints..."
curl -f -H "Authorization: Bearer test-api-key-12345" http://localhost:3001/api/v1/status || { echo "API check failed"; exit 1; }

# Clean up
echo "Cleaning up..."
docker stop $CONTAINER_NAME $MONGODB_CONTAINER $REDIS_CONTAINER
docker rm $CONTAINER_NAME $MONGODB_CONTAINER $REDIS_CONTAINER
docker network rm $NETWORK_NAME

echo "Docker image test complete!"
