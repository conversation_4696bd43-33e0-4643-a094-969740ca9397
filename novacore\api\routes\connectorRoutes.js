/**
 * NovaCore Connector Routes
 * 
 * This file defines the routes for connector management.
 */

const express = require('express');
const router = express.Router();
const { ConnectorController } = require('../controllers');
const { authenticate, authorize } = require('../middleware/authMiddleware');

// Create a new connector
router.post(
  '/',
  authenticate,
  authorize('create:connector'),
  ConnectorController.createConnector
);

// Get all connectors
router.get(
  '/',
  authenticate,
  authorize('read:connector'),
  ConnectorController.getAllConnectors
);

// Get connector by ID
router.get(
  '/:id',
  authenticate,
  authorize('read:connector'),
  ConnectorController.getConnectorById
);

// Update connector by ID
router.put(
  '/:id',
  authenticate,
  authorize('update:connector'),
  ConnectorController.updateConnector
);

// Delete connector by ID
router.delete(
  '/:id',
  authenticate,
  authorize('delete:connector'),
  ConnectorController.deleteConnector
);

// Test connector connection
router.post(
  '/:id/test',
  authenticate,
  authorize('execute:connector'),
  ConnectorController.testConnection
);

// Execute connector endpoint
router.post(
  '/:id/endpoints/:endpointId/execute',
  authenticate,
  authorize('execute:connector'),
  ConnectorController.executeEndpoint
);

// Find connectors by category
router.get(
  '/category/:category',
  authenticate,
  authorize('read:connector'),
  ConnectorController.findByCategory
);

// Find connectors by tags
router.get(
  '/tags',
  authenticate,
  authorize('read:connector'),
  ConnectorController.findByTags
);

// Find active connectors
router.get(
  '/status/active',
  authenticate,
  authorize('read:connector'),
  ConnectorController.findActive
);

module.exports = router;
