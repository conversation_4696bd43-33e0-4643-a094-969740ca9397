"""
NovaTrack (NUCTO) - Universal Compliance Tracking Optimizer.

A system for optimizing compliance tracking and reporting across multiple frameworks
with predictive intelligence capabilities, adaptive workflow optimization, and
integration with other NovaFuse Universal components.

Key Differentiation: AI-driven milestone forecasting
"""

# Import from legacy module for backward compatibility
from ucto.core.tracking_manager import TrackingManager
from ucto.core.optimization_manager import OptimizationManager
from ucto.core.reporting_manager import ReportingManager
from ucto.core.analytics_manager import AnalyticsManager
from ucto.core.control_mapping_manager import ControlMappingManager
from ucto.core.predictive_engine import PredictiveEngine
from ucto.core.adaptive_optimization_manager import AdaptiveOptimizationManager
from ucto.core.integration_manager import IntegrationManager

# Import dashboard components
try:
    from ucto.dashboard import DashboardManager, DashboardAPI, DashboardIntegration
    has_dashboard = True
except ImportError:
    has_dashboard = False

# Import automation components
try:
    from ucto.automation import AutomationManager, ScheduleTrigger, EventTrigger, CreateRequirementAction, CollectEvidenceAction
    has_automation = True
except ImportError:
    has_automation = False

# Import knowledge components
try:
    from ucto.knowledge import KnowledgeManager, KnowledgeAPI
    has_knowledge = True
except ImportError:
    has_knowledge = False

__version__ = '0.2.0'
__all__ = [
    'TrackingManager',
    'OptimizationManager',
    'ReportingManager',
    'AnalyticsManager',
    'ControlMappingManager',
    'PredictiveEngine',
    'AdaptiveOptimizationManager',
    'IntegrationManager'
]

# Add dashboard components to __all__ if available
if has_dashboard:
    __all__.extend(['DashboardManager', 'DashboardAPI', 'DashboardIntegration'])

# Add automation components to __all__ if available
if has_automation:
    __all__.extend(['AutomationManager', 'ScheduleTrigger', 'EventTrigger', 'CreateRequirementAction', 'CollectEvidenceAction'])

# Add knowledge components to __all__ if available
if has_knowledge:
    __all__.extend(['KnowledgeManager', 'KnowledgeAPI'])
