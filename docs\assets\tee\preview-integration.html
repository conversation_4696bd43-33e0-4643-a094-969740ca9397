<!DOCTYPE html>
<html>
<head>
    <title>TEE Integration Map Preview</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', <PERSON>l, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
            color: #333;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            margin-top: 20px;
        }
        .diagram-container {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            min-height: 700px;
            overflow: auto;
        }
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        h1 {
            color: #2E7D32;
            text-align: center;
            margin-bottom: 10px;
        }
        h2 {
            color: #1565C0;
            margin-top: 0;
            font-size: 1.2em;
            border-bottom: 2px solid #E3F2FD;
            padding-bottom: 8px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
            padding: 8px;
            border-radius: 6px;
            transition: all 0.2s;
        }
        .legend-item:hover {
            transform: translateX(5px);
            box-shadow: 2px 2px 8px rgba(0,0,0,0.1);
        }
        .color-box {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 10px;
            flex-shrink: 0;
        }
        .core { background: #2E7D32; }
        .layer { background: #1565C0; }
        .value { background: #6A1B9A; }
        .principle { background: #FF8F00; }
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background: #2E7D32;
            color: white;
            cursor: pointer;
            transition: all 0.2s;
        }
        button:hover {
            background: #1B5E20;
            transform: translateY(-2px);
        }
        .node-details {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #2E7D32;
            display: none;
        }
    </style>
</head>
<body>
    <h1>TEE Integration Map</h1>
    <p style="text-align: center; color: #666; margin-top: 0;">How TEE Connects Across the Comphyology Ecosystem</p>
    
    <div class="controls">
        <button onclick="zoomIn()">Zoom In +</button>
        <button onclick="zoomOut()">Zoom Out -</button>
        <button onclick="resetZoom()">Reset Zoom</button>
    </div>
    
    <div class="container">
        <div class="diagram-container">
            <div id="mermaid-diagram" class="mermaid">
                graph TD
                    %% Core TEE Equation
                    TEE["TEE = (T × E × η) - F\n                    ---
                    Time × Energy × Efficiency - Friction"]
                    
                    %% Core Comphyology Connections
                    TEE --> UUFT["Universal Unification Framework\n                    (Physics Foundation)"]
                    TEE --> 1882["18/82 Principle\n                    (Resource Allocation)"]
                    TEE --> NEPI["NEPI\n                    (Neural Efficiency)"]
                    
                    %% Implementation Layers
                    subgraph Implementation_Layers["Implementation Layers"]
                        AI["AI Systems\n                        - Alignment\n                        - Training\n                        - Inference"]
                        
                        Human["Human Systems\n                        - Productivity\n                        - Decision Making\n                        - Well-being"]
                        
                        Org["Organizational\n                        - Workflows\n                        - Culture\n                        - Governance"]
                    end
                    
                    %% Connect TEE to Implementation
                    TEE --> AI
                    TEE --> Human
                    TEE --> Org
                    
                    %% Value Streams
                    subgraph Value_Streams["Value Streams"]
                        ROI["ROI Optimization\n                        - Resource Allocation\n                        - Cost Reduction"]
                        
                        Innovation["Innovation\n                        - Faster Iteration\n                        - Better Decisions"]
                        
                        Resilience["Resilience\n                        - Sustainable Systems\n                        - Reduced Burnout"]
                    end
                    
                    %% Connect Implementation to Value
                    AI --> ROI
                    Human --> Innovation
                    Org --> Resilience
                    
                    %% Styling
                    classDef core fill:#2E7D32,color:white,stroke:#1B5E20
                    classDef layer fill:#1565C0,color:white,stroke:#0D47A1
                    classDef value fill:#6A1B9A,color:white,stroke:#4A148C
                    classDef principle fill:#FF8F00,color:black,stroke:#E65100
                    
                    class TEE,UUFT,1882,NEPI core
                    class AI,Human,Org layer
                    class ROI,Innovation,Resilience value
                    class Implementation_Layers,Value_Streams principle
            </div>
            
            <div id="node-details" class="node-details">
                <h3 id="detail-title">Node Details</h3>
                <div id="detail-content">Click on any node to see details</div>
            </div>
        </div>
        
        <div class="sidebar">
            <div class="panel">
                <h2>Legend</h2>
                <div class="legend-item">
                    <div class="color-box core"></div>
                    <span>Core Components</span>
                </div>
                <div class="legend-item">
                    <div class="color-box layer"></div>
                    <span>Implementation Layers</span>
                </div>
                <div class="legend-item">
                    <div class="color-box value"></div>
                    <span>Value Streams</span>
                </div>
                <div class="legend-item">
                    <div class="color-box principle"></div>
                    <span>Principles & Frameworks</span>
                </div>
            </div>
            
            <div class="panel">
                <h2>Quick Navigation</h2>
                <button onclick="focusNode('TEE')">TEE Equation</button>
                <button onclick="focusNode('AI')">AI Systems</button>
                <button onclick="focusNode('Human')">Human Systems</button>
                <button onclick="focusNode('Org')">Organizational</button>
            </div>
            
            <div class="panel">
                <h2>How to Use</h2>
                <p>• Click on any node to see details</p>
                <p>• Use zoom controls to adjust view</p>
                <p>• Click quick navigation to jump to sections</p>
                <p>• Hover over legend items to highlight related nodes</p>
            </div>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis',
                nodeSpacing: 50,
                rankSpacing: 100
            }
        });
        
        // Node details data
        const nodeDetails = {
            'TEE': {
                title: 'TEE Equation',
                content: 'The core equation that defines Time-Energy-Efficiency: (Time × Energy × Efficiency) - Friction. This is the foundation for all optimization in Comphyology.'
            },
            'UUFT': {
                title: 'Universal Unification Framework',
                content: 'The physics-based foundation that ensures mathematical rigor and connects to quantum principles.'
            },
            'AI': {
                title: 'AI Systems',
                content: 'Implementation of TEE in artificial intelligence, covering alignment, training efficiency, and optimal inference.'
            },
            'Human': {
                title: 'Human Systems',
                content: 'Application of TEE principles to human productivity, decision making, and overall well-being.'
            },
            'Org': {
                title: 'Organizational Systems',
                content: 'How TEE transforms workflows, culture, and governance at the organizational level.'
            },
            'ROI': {
                title: 'ROI Optimization',
                content: 'Value stream focused on maximizing return through better resource allocation and cost reduction.'
            }
        };
        
        // Add click handlers after diagram renders
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handler to diagram container
            document.querySelector('.mermaid').addEventListener('click', function(e) {
                const node = e.target.closest('span');
                if (node) {
                    const nodeId = node.getAttribute('data-node-id');
                    if (nodeId && nodeDetails[nodeId]) {
                        showNodeDetails(nodeId);
                    }
                }
            });
            
            // Add hover effects to legend
            document.querySelectorAll('.legend-item').forEach(item => {
                item.addEventListener('mouseenter', function() {
                    // Highlight related nodes
                });
                item.addEventListener('mouseleave', function() {
                    // Remove highlights
                });
            });
        });
        
        function showNodeDetails(nodeId) {
            const details = nodeDetails[nodeId] || {
                title: 'Node Details',
                content: 'Detailed information not available for this node.'
            };
            
            const detailsDiv = document.getElementById('node-details');
            document.getElementById('detail-title').textContent = details.title;
            document.getElementById('detail-content').textContent = details.content;
            detailsDiv.style.display = 'block';
        }
        
        function focusNode(nodeId) {
            // In a real implementation, this would pan/zoom to the node
            alert('Focusing on: ' + nodeId);
        }
        
        function zoomIn() {
            const svg = document.querySelector('.mermaid svg');
            if (svg) {
                const currentScale = parseFloat(svg.style.transform?.replace('scale(', '') || '1');
                svg.style.transform = `scale(${currentScale * 1.2})`;
            }
        }
        
        function zoomOut() {
            const svg = document.querySelector('.mermaid svg');
            if (svg) {
                const currentScale = parseFloat(svg.style.transform?.replace('scale(', '') || '1');
                if (currentScale > 0.5) {
                    svg.style.transform = `scale(${currentScale * 0.8})`;
                }
            }
        }
        
        function resetZoom() {
            const svg = document.querySelector('.mermaid svg');
            if (svg) {
                svg.style.transform = 'scale(1)';
            }
        }
    </script>
</body>
</html>
