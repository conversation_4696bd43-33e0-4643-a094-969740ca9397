# NovaFuse API Superstore - Comprehensive Docker Compose Configuration
# Supports local development, testing, and production deployment
version: '3.8'

# Shared networks for service communication
networks:
  novafuse-frontend:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  novafuse-backend:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/16
  novafuse-data:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/16

# Shared volumes for data persistence
volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  logs_data:
    driver: local
  consciousness_data:
    driver: local
  certificates:
    driver: local

# Configuration for secrets management
secrets:
  mongodb_root_password:
    file: ./secrets/mongodb_root_password.txt
  jwt_secret:
    file: ./secrets/jwt_secret.txt
  api_encryption_key:
    file: ./secrets/api_encryption_key.txt
  consciousness_api_key:
    file: ./secrets/consciousness_api_key.txt

services:
  # Main Node.js Application Service
  novafuse-main:
    build:
      context: .
      dockerfile: Dockerfile.main
      target: ${BUILD_TARGET:-production}
      args:
        - NODE_ENV=${NODE_ENV:-production}
    container_name: novafuse-main-${NODE_ENV:-production}
    hostname: novafuse-main
    restart: unless-stopped
    networks:
      - novafuse-frontend
      - novafuse-backend
    ports:
      - "${MAIN_PORT:-3002}:3002"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=3002
      - MONGODB_URI=mongodb://novafuse-mongodb:27017/novafuse
      - REDIS_URL=redis://novafuse-redis:6379
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS:-*}
      - CONSCIOUSNESS_SERVICE_URL=http://novafuse-consciousness:5000
      - ELASTICSEARCH_URL=http://novafuse-elasticsearch:9200
    volumes:
      - logs_data:/app/logs
      - ./config:/app/config:ro
      - ${PWD}/temp:/app/temp
    secrets:
      - mongodb_root_password
      - jwt_secret
      - api_encryption_key
    depends_on:
      novafuse-mongodb:
        condition: service_healthy
      novafuse-redis:
        condition: service_healthy
      novafuse-consciousness:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/privacy/management/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.novafuse-main.rule=Host(`${MAIN_DOMAIN:-localhost}`)"
      - "traefik.http.services.novafuse-main.loadbalancer.server.port=3002"
      - "com.novafuse.service=main"
      - "com.novafuse.environment=${NODE_ENV:-production}"

  # Consciousness Chemistry Engine (Python Service)
  novafuse-consciousness:
    build:
      context: .
      dockerfile: Dockerfile.consciousness-chemistry
      target: ${PYTHON_BUILD_TARGET:-production}
      args:
        - FLASK_ENV=${FLASK_ENV:-production}
    container_name: novafuse-consciousness-${NODE_ENV:-production}
    hostname: novafuse-consciousness
    restart: unless-stopped
    networks:
      - novafuse-backend
      - novafuse-data
    ports:
      - "${CONSCIOUSNESS_PORT:-5000}:5000"
    environment:
      - FLASK_ENV=${FLASK_ENV:-production}
      - PYTHONPATH=/app
      - MONGODB_URI=mongodb://novafuse-mongodb:27017/novafuse
      - REDIS_URL=redis://novafuse-redis:6379
      - ELASTICSEARCH_URL=http://novafuse-elasticsearch:9200
      - GUNICORN_WORKERS=${GUNICORN_WORKERS:-4}
      - GUNICORN_TIMEOUT=120
    volumes:
      - consciousness_data:/app/data
      - logs_data:/app/logs
      - ./consciousness-chemistry-engine/config:/app/config:ro
    secrets:
      - mongodb_root_password
      - consciousness_api_key
    depends_on:
      novafuse-mongodb:
        condition: service_healthy
      novafuse-redis:
        condition: service_healthy
      novafuse-elasticsearch:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 90s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.novafuse-consciousness.rule=Host(`consciousness.${MAIN_DOMAIN:-localhost}`)"
      - "traefik.http.services.novafuse-consciousness.loadbalancer.server.port=5000"
      - "com.novafuse.service=consciousness"
      - "com.novafuse.environment=${NODE_ENV:-production}"

  # MongoDB Database
  novafuse-mongodb:
    image: mongo:7-jammy
    container_name: novafuse-mongodb-${NODE_ENV:-production}
    hostname: novafuse-mongodb
    restart: unless-stopped
    networks:
      - novafuse-data
    ports:
      - "${MONGODB_PORT:-27017}:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGODB_ROOT_USER:-admin}
      - MONGO_INITDB_ROOT_PASSWORD_FILE=/run/secrets/mongodb_root_password
      - MONGO_INITDB_DATABASE=novafuse
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongodb-init.js:/docker-entrypoint-initdb.d/init.js:ro
    secrets:
      - mongodb_root_password
    healthcheck:
      test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    labels:
      - "com.novafuse.service=database"
      - "com.novafuse.environment=${NODE_ENV:-production}"

  # Redis Cache
  novafuse-redis:
    image: redis:7-alpine
    container_name: novafuse-redis-${NODE_ENV:-production}
    hostname: novafuse-redis
    restart: unless-stopped
    networks:
      - novafuse-data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    labels:
      - "com.novafuse.service=cache"
      - "com.novafuse.environment=${NODE_ENV:-production}"

  # Elasticsearch for Search and Analytics
  novafuse-elasticsearch:
    image: elasticsearch:8.11.0
    container_name: novafuse-elasticsearch-${NODE_ENV:-production}
    hostname: novafuse-elasticsearch
    restart: unless-stopped
    networks:
      - novafuse-data
    ports:
      - "${ELASTICSEARCH_PORT:-9200}:9200"
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
      - xpack.security.http.ssl.enabled=false
      - xpack.security.transport.ssl.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200/_cluster/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    labels:
      - "com.novafuse.service=search"
      - "com.novafuse.environment=${NODE_ENV:-production}"

  # Reverse Proxy / Load Balancer (Traefik)
  novafuse-proxy:
    image: traefik:v3.0
    container_name: novafuse-proxy-${NODE_ENV:-production}
    hostname: novafuse-proxy
    restart: unless-stopped
    networks:
      - novafuse-frontend
    ports:
      - "80:80"
      - "443:443"
      - "${TRAEFIK_DASHBOARD_PORT:-8080}:8080"
    environment:
      - TRAEFIK_API_DASHBOARD=true
      - TRAEFIK_API_INSECURE=true
      - TRAEFIK_PROVIDERS_DOCKER=true
      - TRAEFIK_PROVIDERS_DOCKER_EXPOSEDBYDEFAULT=false
      - TRAEFIK_ENTRYPOINTS_WEB_ADDRESS=:80
      - TRAEFIK_ENTRYPOINTS_WEBSECURE_ADDRESS=:443
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - certificates:/certificates
      - ./config/traefik.yml:/etc/traefik/traefik.yml:ro
      - ./config/dynamic:/etc/traefik/dynamic:ro
    labels:
      - "com.novafuse.service=proxy"
      - "com.novafuse.environment=${NODE_ENV:-production}"

  # Monitoring with Prometheus
  novafuse-prometheus:
    image: prom/prometheus:latest
    container_name: novafuse-prometheus-${NODE_ENV:-production}
    hostname: novafuse-prometheus
    restart: unless-stopped
    networks:
      - novafuse-backend
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/rules:/etc/prometheus/rules:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.external-url=http://${MAIN_DOMAIN:-localhost}:9090'
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`monitoring.${MAIN_DOMAIN:-localhost}`)"
      - "traefik.http.services.prometheus.loadbalancer.server.port=9090"
      - "com.novafuse.service=monitoring"
      - "com.novafuse.environment=${NODE_ENV:-production}"

  # Grafana for Dashboards
  novafuse-grafana:
    image: grafana/grafana:latest
    container_name: novafuse-grafana-${NODE_ENV:-production}
    hostname: novafuse-grafana
    restart: unless-stopped
    networks:
      - novafuse-backend
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_ROOT_URL=http://${MAIN_DOMAIN:-localhost}:3000
    volumes:
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    depends_on:
      - novafuse-prometheus
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`dashboards.${MAIN_DOMAIN:-localhost}`)"
      - "traefik.http.services.grafana.loadbalancer.server.port=3000"
      - "com.novafuse.service=dashboards"
      - "com.novafuse.environment=${NODE_ENV:-production}"

  # Log Aggregation with Fluentd
  novafuse-fluentd:
    image: fluentd:v1.16-debian-1
    container_name: novafuse-fluentd-${NODE_ENV:-production}
    hostname: novafuse-fluentd
    restart: unless-stopped
    networks:
      - novafuse-backend
    ports:
      - "24224:24224"
    volumes:
      - logs_data:/fluentd/log
      - ./monitoring/fluentd.conf:/fluentd/etc/fluent.conf:ro
    depends_on:
      - novafuse-elasticsearch
    labels:
      - "com.novafuse.service=logging"
      - "com.novafuse.environment=${NODE_ENV:-production}"

# Development-specific overrides
  novafuse-main-dev:
    extends:
      service: novafuse-main
    profiles:
      - development
    build:
      target: development
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
    command: ["dumb-init", "npm", "run", "dev"]

  novafuse-consciousness-dev:
    extends:
      service: novafuse-consciousness
    profiles:
      - development
    build:
      target: development
    volumes:
      - ./consciousness-chemistry-engine:/app/consciousness-chemistry-engine
      - ./src:/app/src
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=1

# Testing-specific services
  novafuse-test-runner:
    extends:
      service: novafuse-main
    profiles:
      - testing
    build:
      target: testing
    command: ["npm", "run", "test:all"]
    depends_on: []

  novafuse-consciousness-test:
    extends:
      service: novafuse-consciousness
    profiles:
      - testing
    build:
      target: testing
    command: ["python", "-m", "pytest", "-v", "--cov=consciousness-chemistry-engine"]
    depends_on: []
