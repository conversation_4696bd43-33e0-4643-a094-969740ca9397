/**
 * NovaFuse Universal API Connector - Error Metrics Middleware
 * 
 * This module provides middleware for collecting error metrics.
 */

const metricsService = require('./metrics-service');
const { createLogger } = require('../utils/logger');
const { UAConnectorError } = require('../errors');

const logger = createLogger('error-metrics');

// Initialize metrics
const errorsTotal = metricsService.registerCounter(
  'errors_total',
  'Total number of errors',
  ['error_type', 'error_code', 'severity']
);

const errorsByPath = metricsService.registerCounter(
  'errors_by_path_total',
  'Total number of errors by path',
  ['method', 'path', 'error_type', 'status']
);

/**
 * Normalize the path by replacing path parameters with placeholders
 * 
 * @param {string} path - The request path
 * @returns {string} - The normalized path
 */
function normalizePath(path) {
  // Replace numeric IDs with :id
  return path
    .replace(/\/\d+/g, '/:id')
    .replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '/:uuid');
}

/**
 * Error metrics middleware
 * 
 * @param {Error} err - The error
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function errorMetricsMiddleware(err, req, res, next) {
  // Extract error details
  const errorType = err.name || 'Error';
  const errorCode = err instanceof UAConnectorError ? err.code : 'UNKNOWN';
  const severity = err instanceof UAConnectorError ? err.severity : 'error';
  const status = res.statusCode || 500;
  
  // Normalize path
  const path = normalizePath(req.path);
  
  // Record metrics
  errorsTotal.inc(1, { error_type: errorType, error_code: errorCode, severity });
  errorsByPath.inc(1, { method: req.method, path, error_type: errorType, status: status.toString() });
  
  // Log error
  logger.debug(`Error metrics recorded for ${errorType} (${errorCode}) at ${req.method} ${req.originalUrl}`);
  
  // Continue to next error handler
  next(err);
}

module.exports = errorMetricsMiddleware;
