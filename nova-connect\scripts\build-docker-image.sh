#!/bin/bash
# <PERSON>ript to build the Docker image for NovaConnect UAC

# Set variables
PROJECT_ID=${1:-"novafuse-test"}
IMAGE_NAME=${2:-"novafuse-uac"}
IMAGE_TAG=${3:-"1.0.0"}
FULL_IMAGE_NAME="gcr.io/$PROJECT_ID/$IMAGE_NAME:$IMAGE_TAG"

# Build the Docker image
echo "Building Docker image $FULL_IMAGE_NAME..."
docker build -t $FULL_IMAGE_NAME .

# Configure Docker for GCR
echo "Configuring Docker for GCR..."
gcloud auth configure-docker gcr.io

# Push the Docker image to GCR
echo "Pushing Docker image to GCR..."
docker push $FULL_IMAGE_NAME

echo "Docker image build and push complete!"
