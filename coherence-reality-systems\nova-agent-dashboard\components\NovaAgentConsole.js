import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { 
  CommandLineIcon, 
  PlayIcon, 
  ArrowPathIcon,
  DocumentTextIcon,
  WrenchScrewdriverIcon
} from '@heroicons/react/24/outline';

export default function NovaAgentConsole({ isConnected }) {
  const [logs, setLogs] = useState([]);
  const [isExecuting, setIsExecuting] = useState(false);
  const [ws, setWs] = useState(null);
  const logsEndRef = useRef(null);

  // Initialize WebSocket connection
  useEffect(() => {
    if (isConnected) {
      const websocket = new WebSocket('ws://localhost:8090/ws');
      
      websocket.onopen = () => {
        console.log('WebSocket connected');
        addLog('🔌 Connected to Nova Agent', 'success');
        setWs(websocket);
      };

      websocket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        addLog(`📊 Status Update: ${data.status} | Coherence: ${(data.coherence * 100).toFixed(1)}%`, 'info');
      };

      websocket.onclose = () => {
        console.log('WebSocket disconnected');
        addLog('🔌 Disconnected from Nova Agent', 'warning');
        setWs(null);
      };

      websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
        addLog('❌ WebSocket connection error', 'error');
      };

      return () => {
        websocket.close();
      };
    }
  }, [isConnected]);

  // Auto-scroll logs
  useEffect(() => {
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [logs]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { timestamp, message, type }]);
  };

  const executeCommand = async (commandType, description) => {
    setIsExecuting(true);
    addLog(`⚡ Executing: ${description}`, 'command');

    try {
      const response = await fetch('http://localhost:8090/command', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: commandType, payload: {} })
      });

      const result = await response.json();
      
      if (result.success) {
        addLog(`✅ ${result.message}`, 'success');
        if (result.data) {
          addLog(`📄 Data: ${JSON.stringify(result.data)}`, 'data');
        }
      } else {
        addLog(`❌ ${result.message}`, 'error');
      }
    } catch (error) {
      addLog(`❌ Command failed: ${error.message}`, 'error');
    } finally {
      setIsExecuting(false);
    }
  };

  const getLogColor = (type) => {
    switch (type) {
      case 'success': return 'text-green-400';
      case 'error': return 'text-red-400';
      case 'warning': return 'text-yellow-400';
      case 'command': return 'text-blue-400';
      case 'data': return 'text-purple-400';
      default: return 'text-gray-300';
    }
  };

  const commands = [
    {
      type: 'restart_service',
      name: 'Restart Service',
      description: 'Restart Nova Agent service',
      icon: <ArrowPathIcon className="w-4 h-4" />,
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      type: 'scan_vendor',
      name: 'Scan Vendor',
      description: 'Scan for vendor integrations',
      icon: <WrenchScrewdriverIcon className="w-4 h-4" />,
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      type: 'update_module',
      name: 'Update Module',
      description: 'Update system modules',
      icon: <PlayIcon className="w-4 h-4" />,
      color: 'bg-purple-600 hover:bg-purple-700'
    },
    {
      type: 'get_logs',
      name: 'Get Logs',
      description: 'Retrieve system logs',
      icon: <DocumentTextIcon className="w-4 h-4" />,
      color: 'bg-orange-600 hover:bg-orange-700'
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6"
    >
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <CommandLineIcon className="w-6 h-6 text-nova-400" />
        <div>
          <h3 className="text-lg font-semibold text-white">Nova Agent Console</h3>
          <p className="text-sm text-gray-400">Command & Control Interface</p>
        </div>
      </div>

      {/* Command Buttons */}
      <div className="grid grid-cols-2 gap-3 mb-6">
        {commands.map((command) => (
          <motion.button
            key={command.type}
            onClick={() => executeCommand(command.type, command.description)}
            disabled={!isConnected || isExecuting}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-white text-sm font-medium transition-all ${
              !isConnected || isExecuting 
                ? 'bg-gray-600 cursor-not-allowed opacity-50' 
                : command.color
            }`}
          >
            {command.icon}
            <span>{command.name}</span>
          </motion.button>
        ))}
      </div>

      {/* Console Logs */}
      <div className="bg-gray-900/50 rounded-lg border border-gray-700 p-4 h-64 overflow-y-auto">
        <div className="space-y-1">
          {logs.length === 0 ? (
            <p className="text-gray-500 text-sm">Console ready. Execute commands to see output...</p>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="flex items-start space-x-2 text-xs font-mono">
                <span className="text-gray-500 flex-shrink-0">[{log.timestamp}]</span>
                <span className={getLogColor(log.type)}>{log.message}</span>
              </div>
            ))
          )}
          <div ref={logsEndRef} />
        </div>
      </div>

      {/* Status Bar */}
      <div className="mt-4 flex items-center justify-between text-xs">
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${ws ? 'bg-green-400' : 'bg-red-400'}`}></div>
          <span className="text-gray-400">
            {ws ? 'WebSocket Connected' : 'WebSocket Disconnected'}
          </span>
        </div>
        <div className="text-gray-500">
          {logs.length} log entries
        </div>
      </div>
    </motion.div>
  );
}
