/**
 * Simple NovaActuary™ Test
 * Quick validation of core functionality
 */

console.log('🚀 Starting Simple NovaActuary™ Test');

try {
  // Test 1: Basic module loading
  console.log('📦 Testing module imports...');
  
  const { NovaConnect } = require('../novaconnect');
  console.log('✅ NovaConnect imported successfully');
  
  const CSMPRSAITestSuite = require('../novacortex/csm-prs-ai-test-suite');
  console.log('✅ CSM-PRS AI Test Suite imported successfully');
  
  const { ComphyologyCore } = require('../comphyology');
  console.log('✅ Comphyology Core imported successfully');
  
  // Test 2: NovaActuary initialization
  console.log('\n🧠 Testing NovaActuary™ initialization...');
  
  const { NovaActuary } = require('./index');
  const novaActuary = new NovaActuary();
  
  console.log(`✅ NovaActuary™ initialized: ${novaActuary.name} v${novaActuary.version}`);
  
  // Test 3: Basic functionality test
  console.log('\n📊 Testing basic functionality...');
  
  const testClient = {
    name: 'Simple Test Client',
    aiSystems: {
      name: 'Test AI System',
      type: 'basic',
      domain: 'test'
    },
    financialData: {
      revenue: 1000000,
      assets: 5000000,
      liabilities: 1500000,
      riskScore: 0.3
    },
    testData: {
      privacyCompliance: 0.8,
      securityScore: 0.85,
      fairnessMetrics: 0.75
    }
  };
  
  console.log('🔄 Running actuarial assessment...');
  
  const result = await novaActuary.performActuarialAssessment(testClient);
  
  console.log('✅ Assessment completed successfully!');
  console.log(`   Client: ${result.client}`);
  console.log(`   Risk Classification: ${result.risk_classification}`);
  console.log(`   Mathematical Premium: $${result.mathematical_premium.toLocaleString()}`);
  console.log(`   Processing Time: ${result.processing_time_ms.toFixed(2)}ms`);
  console.log(`   ∂Ψ Deviation: ${result.psi_deviation.toFixed(4)}`);
  console.log(`   Certification: ${result.certification_level}`);
  
  // Test 4: Performance metrics
  console.log('\n📈 Performance Metrics:');
  console.log(`   Speed Advantage: ${result.speed_advantage}`);
  console.log(`   Accuracy Advantage: ${result.accuracy_advantage}`);
  console.log(`   NovaActuary Validated: ${result.novaactuary_validated}`);
  
  console.log('\n🎉 All tests passed! NovaActuary™ is working correctly.');
  console.log('🚀 Ready for insurance industry deployment!');
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error('Stack trace:', error.stack);
  process.exit(1);
}
