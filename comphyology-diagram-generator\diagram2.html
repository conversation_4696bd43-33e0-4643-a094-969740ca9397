<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>2. Finite Universe Paradigm</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 800px;
            height: 600px;
            position: relative;
            border: 1px solid #eee;
            margin: 0 auto;
            background-color: white;
        }
        .element {
            position: absolute;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 2;
        }
        .element-number {
            position: absolute;
            top: 5px;
            left: 5px;
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
        }
        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 1;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        .circle {
            position: absolute;
            border-radius: 50%;
            border: 2px solid black;
            z-index: 1;
        }
    </style>
</head>
<body>
    <h1>2. Finite Universe Paradigm</h1>
    
    <div class="diagram-container">
        <!-- Finite Universe Paradigm -->
        <div class="element" style="top: 50px; left: 300px; width: 200px; background-color: #e6f7ff; font-weight: bold; font-size: 18px;">
            Finite Universe Paradigm
            <div class="element-number">1</div>
        </div>
        
        <!-- Core Principles -->
        <div class="element" style="top: 150px; left: 300px; width: 200px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            Core Principles
            <div class="element-number">2</div>
        </div>
        
        <!-- Nested Systems -->
        <div class="element" style="top: 250px; left: 100px; width: 150px; background-color: #f6ffed;">
            Nested Systems
            <div class="element-number">3</div>
        </div>
        
        <!-- Finite Boundaries -->
        <div class="element" style="top: 250px; left: 325px; width: 150px; background-color: #f6ffed;">
            Finite Boundaries
            <div class="element-number">4</div>
        </div>
        
        <!-- Coherent Order -->
        <div class="element" style="top: 250px; left: 550px; width: 150px; background-color: #f6ffed;">
            Coherent Order
            <div class="element-number">5</div>
        </div>
        
        <!-- Mathematical Expression -->
        <div class="element" style="top: 350px; left: 300px; width: 200px; background-color: #fffbe6;">
            U=T[∑(n=1 to 5) Sn⋅(En+In)⋅Φn]
            <div class="element-number">6</div>
        </div>
        
        <!-- Practical Applications -->
        <div class="element" style="top: 450px; left: 300px; width: 200px; background-color: #fff2e8; font-weight: bold; font-size: 16px;">
            Practical Applications
            <div class="element-number">7</div>
        </div>
        
        <!-- Connections -->
        <!-- Title to Core Principles -->
        <div class="connection" style="top: 100px; left: 400px; width: 2px; height: 50px; background-color: black;"></div>
        <div class="arrow" style="top: 140px; left: 395px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Core Principles to Nested Systems -->
        <div class="connection" style="top: 200px; left: 300px; width: 100px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 250px; left: 90px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Core Principles to Finite Boundaries -->
        <div class="connection" style="top: 200px; left: 400px; width: 2px; height: 50px; background-color: black;"></div>
        <div class="arrow" style="top: 240px; left: 395px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Core Principles to Coherent Order -->
        <div class="connection" style="top: 200px; left: 500px; width: 100px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 250px; left: 540px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Principles to Mathematical Expression -->
        <div class="connection" style="top: 300px; left: 400px; width: 2px; height: 50px; background-color: black;"></div>
        <div class="arrow" style="top: 340px; left: 395px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Mathematical Expression to Practical Applications -->
        <div class="connection" style="top: 400px; left: 400px; width: 2px; height: 50px; background-color: black;"></div>
        <div class="arrow" style="top: 440px; left: 395px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Nested Circles Visualization -->
        <div class="circle" style="top: 500px; left: 350px; width: 100px; height: 100px;"></div>
        <div class="circle" style="top: 515px; left: 365px; width: 70px; height: 70px;"></div>
        <div class="circle" style="top: 530px; left: 380px; width: 40px; height: 40px;"></div>
    </div>
</body>
</html>
