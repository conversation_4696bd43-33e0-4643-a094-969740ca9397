/**
 * NovaFuse Universal API Connector Security Middleware
 * 
 * This module provides security middleware for the connector API.
 */

const { RateLimiter, InputValidator } = require('../../src/security');

/**
 * Create rate limiting middleware
 * @param {Object} options - Rate limiter options
 * @returns {Function} - Express middleware
 */
function createRateLimiter(options = {}) {
  const rateLimiter = new RateLimiter(options);
  
  return function rateLimiterMiddleware(req, res, next) {
    if (rateLimiter.isAllowed(req)) {
      // Add rate limit headers
      res.setHeader('X-RateLimit-Limit', rateLimiter.options.maxRequests);
      res.setHeader('X-RateLimit-Remaining', rateLimiter.getRemainingRequests(req));
      res.setHeader('X-RateLimit-Reset', Math.floor(rateLimiter.getResetTime(req) / 1000));
      
      next();
    } else {
      // Rate limit exceeded
      res.setHeader('X-RateLimit-Limit', rateLimiter.options.maxRequests);
      res.setHeader('X-RateLimit-Remaining', 0);
      res.setHeader('X-RateLimit-Reset', Math.floor(rateLimiter.getResetTime(req) / 1000));
      res.setHeader('Retry-After', Math.ceil((rateLimiter.getResetTime(req) - Date.now()) / 1000));
      
      res.status(429).json({
        error: 'Too many requests',
        message: 'Rate limit exceeded. Please try again later.'
      });
    }
  };
}

/**
 * Create input validation middleware
 * @returns {Function} - Express middleware
 */
function createInputValidator() {
  return function inputValidatorMiddleware(req, res, next) {
    try {
      // Validate URL parameters
      for (const param in req.params) {
        if (!InputValidator.isXssSafe(req.params[param]) || 
            !InputValidator.isCommandSafe(req.params[param])) {
          return res.status(400).json({
            error: 'Invalid parameter',
            message: `Parameter '${param}' contains invalid characters`
          });
        }
      }
      
      // Validate query parameters
      for (const param in req.query) {
        if (!InputValidator.isXssSafe(req.query[param]) || 
            !InputValidator.isCommandSafe(req.query[param])) {
          return res.status(400).json({
            error: 'Invalid parameter',
            message: `Query parameter '${param}' contains invalid characters`
          });
        }
      }
      
      // Validate request body
      if (req.body && typeof req.body === 'object') {
        const validateObject = (obj, path = '') => {
          for (const key in obj) {
            const value = obj[key];
            const currentPath = path ? `${path}.${key}` : key;
            
            if (typeof value === 'string') {
              if (!InputValidator.isXssSafe(value) || 
                  !InputValidator.isCommandSafe(value)) {
                throw new Error(`Field '${currentPath}' contains invalid characters`);
              }
            } else if (typeof value === 'object' && value !== null) {
              validateObject(value, currentPath);
            }
          }
        };
        
        try {
          validateObject(req.body);
        } catch (error) {
          return res.status(400).json({
            error: 'Invalid request body',
            message: error.message
          });
        }
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Create security headers middleware
 * @returns {Function} - Express middleware
 */
function createSecurityHeaders() {
  return function securityHeadersMiddleware(req, res, next) {
    // Set security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    res.setHeader('Content-Security-Policy', "default-src 'self'");
    res.setHeader('Referrer-Policy', 'no-referrer');
    
    next();
  };
}

module.exports = {
  createRateLimiter,
  createInputValidator,
  createSecurityHeaders
};
