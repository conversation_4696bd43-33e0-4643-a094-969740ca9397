7408cccdbf43e79fcca486b932f2357c
// Mock services
_getJestObj().mock('../../../api/services/RateLimitService');
_getJestObj().mock('../../../api/services/BruteForceProtectionService');
_getJestObj().mock('../../../api/services/IpRestrictionService');
_getJestObj().mock('../../../api/services/AuthAuditService');
_getJestObj().mock('../../../api/services/AuthService');

// Import mocked services

// Mock JWT token verification
_getJestObj().mock('jsonwebtoken', () => ({
  verify: jest.fn().mockImplementation((token, secret) => {
    if (token === 'valid-admin-token') {
      return {
        sub: 'admin-user-id',
        role: 'admin'
      };
    } else if (token === 'valid-user-token') {
      return {
        sub: 'regular-user-id',
        role: 'user'
      };
    } else {
      throw new Error('Invalid token');
    }
  }),
  sign: jest.fn().mockReturnValue('new-token')
}));
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Security Features Integration Tests
 */

const request = require('supertest');
const app = require('../../../api/app');
const fs = require('fs').promises;
const path = require('path');
const RateLimitService = require('../../../api/services/RateLimitService');
const BruteForceProtectionService = require('../../../api/services/BruteForceProtectionService');
const IpRestrictionService = require('../../../api/services/IpRestrictionService');
const AuthAuditService = require('../../../api/services/AuthAuditService');
const AuthService = require('../../../api/services/AuthService');
describe('Security Features Integration Tests', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock RateLimitService
    RateLimitService.mockImplementation(() => ({
      loadConfig: jest.fn().mockResolvedValue({
        global: {
          enabled: true,
          windowMs: 60000,
          max: 100
        },
        auth: {
          enabled: true,
          windowMs: 900000,
          max: 10
        },
        api: {
          enabled: true,
          windowMs: 60000,
          max: 60
        }
      }),
      createLimiter: jest.fn().mockReturnValue((req, res, next) => next()),
      updateConfig: jest.fn().mockResolvedValue({})
    }));

    // Mock BruteForceProtectionService
    BruteForceProtectionService.mockImplementation(() => ({
      checkLoginAttempt: jest.fn().mockResolvedValue(true),
      handleSuccessfulLogin: jest.fn().mockResolvedValue(true),
      handleFailedLogin: jest.fn().mockResolvedValue({
        attemptsCount: 1,
        maxAttempts: 5,
        remainingAttempts: 4
      }),
      getConfig: jest.fn().mockReturnValue({
        maxAttempts: 5,
        windowMs: 900000,
        blockDuration: 1800000
      }),
      updateConfig: jest.fn().mockResolvedValue({})
    }));

    // Mock IpRestrictionService
    IpRestrictionService.mockImplementation(() => ({
      isAllowed: jest.fn().mockResolvedValue(true),
      loadRestrictions: jest.fn().mockResolvedValue({
        enabled: false,
        mode: 'allowlist',
        allowlist: [],
        blocklist: [],
        rules: []
      }),
      addToAllowlist: jest.fn().mockResolvedValue({}),
      addToBlocklist: jest.fn().mockResolvedValue({}),
      updateConfig: jest.fn().mockResolvedValue({})
    }));

    // Mock AuthAuditService
    AuthAuditService.mockImplementation(() => ({
      logLoginAttempt: jest.fn().mockResolvedValue({
        id: 'log-id'
      }),
      logLogout: jest.fn().mockResolvedValue({
        id: 'log-id'
      }),
      logRegistration: jest.fn().mockResolvedValue({
        id: 'log-id'
      }),
      logTwoFactorAuth: jest.fn().mockResolvedValue({
        id: 'log-id'
      }),
      getAuthAuditLogs: jest.fn().mockResolvedValue({
        logs: [],
        total: 0,
        page: 1,
        limit: 10
      })
    }));

    // Mock AuthService
    AuthService.mockImplementation(() => ({
      login: jest.fn().mockResolvedValue({
        user: {
          id: 'user-id',
          username: 'testuser',
          email: '<EMAIL>',
          role: 'user'
        },
        token: 'valid-user-token',
        refreshToken: 'refresh-token',
        expiresIn: '24h'
      }),
      logout: jest.fn().mockResolvedValue({
        success: true,
        message: 'Logged out successfully'
      }),
      register: jest.fn().mockResolvedValue({
        id: 'user-id',
        username: 'testuser',
        email: '<EMAIL>',
        role: 'user'
      })
    }));
  });
  describe('Rate Limiting', () => {
    it('should allow access to rate limit configuration for admin users', async () => {
      const response = await request(app).get('/api/rate-limits').set('Authorization', 'Bearer valid-admin-token');
      expect(response.status).toBe(200);
    });
    it('should deny access to rate limit configuration for non-admin users', async () => {
      const response = await request(app).get('/api/rate-limits').set('Authorization', 'Bearer valid-user-token');
      expect(response.status).toBe(403);
    });
    it('should allow updating rate limit configuration for admin users', async () => {
      const response = await request(app).put('/api/rate-limits').set('Authorization', 'Bearer valid-admin-token').send({
        rateLimits: {
          anonymous: {
            requests: 30,
            period: 60
          }
        }
      });
      expect(response.status).toBe(200);
    });
  });
  describe('Brute Force Protection', () => {
    it('should allow access to brute force protection configuration for admin users', async () => {
      const response = await request(app).get('/api/brute-force/config').set('Authorization', 'Bearer valid-admin-token');
      expect(response.status).toBe(200);
    });
    it('should deny access to brute force protection configuration for non-admin users', async () => {
      const response = await request(app).get('/api/brute-force/config').set('Authorization', 'Bearer valid-user-token');
      expect(response.status).toBe(403);
    });
    it('should allow updating brute force protection configuration for admin users', async () => {
      const response = await request(app).put('/api/brute-force/config').set('Authorization', 'Bearer valid-admin-token').send({
        maxAttempts: 3,
        windowMs: 600000,
        blockDuration: 3600000
      });
      expect(response.status).toBe(200);
    });
  });
  describe('IP Restrictions', () => {
    it('should allow access to IP restrictions configuration for admin users', async () => {
      const response = await request(app).get('/api/ip-restrictions').set('Authorization', 'Bearer valid-admin-token');
      expect(response.status).toBe(200);
    });
    it('should deny access to IP restrictions configuration for non-admin users', async () => {
      const response = await request(app).get('/api/ip-restrictions').set('Authorization', 'Bearer valid-user-token');
      expect(response.status).toBe(403);
    });
    it('should allow adding IP to allowlist for admin users', async () => {
      const response = await request(app).post('/api/ip-restrictions/allowlist').set('Authorization', 'Bearer valid-admin-token').send({
        ip: '***********'
      });
      expect(response.status).toBe(200);
    });
  });
  describe('Authentication Audit Logging', () => {
    it('should allow access to auth audit logs for admin users', async () => {
      const response = await request(app).get('/api/auth/audit').set('Authorization', 'Bearer valid-admin-token');
      expect(response.status).toBe(200);
    });
    it('should deny access to auth audit logs for non-admin users', async () => {
      const response = await request(app).get('/api/auth/audit').set('Authorization', 'Bearer valid-user-token');
      expect(response.status).toBe(403);
    });
    it('should allow access to user\'s own login history', async () => {
      const response = await request(app).get('/api/auth/audit/user/regular-user-id/login-history').set('Authorization', 'Bearer valid-user-token');
      expect(response.status).toBe(200);
    });
    it('should deny access to another user\'s login history for non-admin users', async () => {
      const response = await request(app).get('/api/auth/audit/user/admin-user-id/login-history').set('Authorization', 'Bearer valid-user-token');
      expect(response.status).toBe(403);
    });
  });
  describe('Authentication with Security Features', () => {
    it('should log successful login attempts', async () => {
      const response = await request(app).post('/api/auth/login').send({
        username: 'testuser',
        password: 'password123'
      });
      expect(response.status).toBe(200);

      // Verify that login attempt was logged
      const authAuditServiceInstance = AuthAuditService.mock.instances[0];
      expect(authAuditServiceInstance.logLoginAttempt).toHaveBeenCalled();

      // Verify that brute force protection was checked
      const bruteForceServiceInstance = BruteForceProtectionService.mock.instances[0];
      expect(bruteForceServiceInstance.checkLoginAttempt).toHaveBeenCalled();
      expect(bruteForceServiceInstance.handleSuccessfulLogin).toHaveBeenCalled();
    });
    it('should log failed login attempts', async () => {
      // Mock AuthService to simulate failed login
      AuthService.mockImplementationOnce(() => ({
        login: jest.fn().mockRejectedValue(new Error('Invalid username or password'))
      }));
      const response = await request(app).post('/api/auth/login').send({
        username: 'testuser',
        password: 'wrongpassword'
      });
      expect(response.status).toBe(500); // In a real app, this would be 401

      // Verify that failed login attempt was handled by brute force protection
      const bruteForceServiceInstance = BruteForceProtectionService.mock.instances[0];
      expect(bruteForceServiceInstance.checkLoginAttempt).toHaveBeenCalled();
      expect(bruteForceServiceInstance.handleFailedLogin).toHaveBeenCalled();
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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