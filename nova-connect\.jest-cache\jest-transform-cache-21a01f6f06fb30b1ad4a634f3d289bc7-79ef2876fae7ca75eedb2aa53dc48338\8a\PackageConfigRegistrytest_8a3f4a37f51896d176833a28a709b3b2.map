{"version": 3, "names": ["PackageConfigRegistry", "require", "fs", "promises", "path", "os", "describe", "packageRegistry", "testDir", "beforeEach", "join", "tmpdir", "Date", "now", "mkdir", "recursive", "Promise", "resolve", "setTimeout", "after<PERSON>ach", "rm", "force", "error", "console", "test", "packages", "getAllPackages", "expect", "toHave<PERSON>ength", "id", "toBe", "pkg", "getPackageById", "toBeDefined", "name", "features", "toContain", "newPackage", "description", "tier", "limits", "connections", "operations_per_day", "createPackage", "updateData", "updatePackage", "deletePackage", "rejects", "toThrow", "setTenantMapping", "mapping", "getTenantMapping", "tenantId", "packageId", "customFeatures", "customLimits", "hasAccessToPackageFeature", "hasTenantFeatureAccess", "hasAccessToCustomFeature", "hasAccessToOtherFeature", "customLimit", "getTenantFeatureLimit", "packageLimit", "nonExistentLimit", "toBeNull", "getTenantAvailableFeatures", "not", "originalLoadData", "loadData", "loadDataMock", "jest", "fn", "mockImplementation", "toHaveBeenCalledTimes", "clearCache"], "sources": ["PackageConfigRegistry.test.js"], "sourcesContent": ["/**\n * Package Configuration Registry Tests\n */\n\nconst PackageConfigRegistry = require('../../../api/services/PackageConfigRegistry');\nconst fs = require('fs').promises;\nconst path = require('path');\nconst os = require('os');\n\ndescribe('PackageConfigRegistry', () => {\n  let packageRegistry;\n  let testDir;\n  \n  beforeEach(async () => {\n    // Create a temporary directory for testing\n    testDir = path.join(os.tmpdir(), `nova-connect-test-${Date.now()}`);\n    await fs.mkdir(testDir, { recursive: true });\n    \n    // Initialize the package registry with the test directory\n    packageRegistry = new PackageConfigRegistry(testDir);\n    \n    // Wait for the data directory to be created\n    await new Promise(resolve => setTimeout(resolve, 100));\n  });\n  \n  afterEach(async () => {\n    // Clean up the test directory\n    try {\n      await fs.rm(testDir, { recursive: true, force: true });\n    } catch (error) {\n      console.error('Error cleaning up test directory:', error);\n    }\n  });\n  \n  test('should initialize with default packages', async () => {\n    const packages = await packageRegistry.getAllPackages();\n    \n    expect(packages).toHaveLength(4);\n    expect(packages[0].id).toBe('core');\n    expect(packages[1].id).toBe('secure');\n    expect(packages[2].id).toBe('enterprise');\n    expect(packages[3].id).toBe('ai_boost');\n  });\n  \n  test('should get package by ID', async () => {\n    const pkg = await packageRegistry.getPackageById('core');\n    \n    expect(pkg).toBeDefined();\n    expect(pkg.id).toBe('core');\n    expect(pkg.name).toBe('NovaConnect Core');\n    expect(pkg.features).toContain('core.basic_connectors');\n  });\n  \n  test('should create a new package', async () => {\n    const newPackage = {\n      id: 'test-package',\n      name: 'Test Package',\n      description: 'A test package',\n      tier: 'test',\n      features: ['test.feature1', 'test.feature2'],\n      limits: {\n        connections: 5,\n        operations_per_day: 500\n      }\n    };\n    \n    await packageRegistry.createPackage(newPackage);\n    \n    const pkg = await packageRegistry.getPackageById('test-package');\n    \n    expect(pkg).toBeDefined();\n    expect(pkg.id).toBe('test-package');\n    expect(pkg.name).toBe('Test Package');\n    expect(pkg.features).toContain('test.feature1');\n  });\n  \n  test('should update a package', async () => {\n    const updateData = {\n      name: 'Updated Core Package',\n      description: 'Updated description'\n    };\n    \n    await packageRegistry.updatePackage('core', updateData);\n    \n    const pkg = await packageRegistry.getPackageById('core');\n    \n    expect(pkg).toBeDefined();\n    expect(pkg.name).toBe('Updated Core Package');\n    expect(pkg.description).toBe('Updated description');\n  });\n  \n  test('should delete a package', async () => {\n    await packageRegistry.deletePackage('core');\n    \n    await expect(packageRegistry.getPackageById('core')).rejects.toThrow('Package with ID core not found');\n  });\n  \n  test('should set and get tenant mapping', async () => {\n    await packageRegistry.setTenantMapping('test-tenant', 'enterprise', ['custom.feature1'], { connections: 200 });\n    \n    const mapping = await packageRegistry.getTenantMapping('test-tenant');\n    \n    expect(mapping).toBeDefined();\n    expect(mapping.tenantId).toBe('test-tenant');\n    expect(mapping.packageId).toBe('enterprise');\n    expect(mapping.customFeatures).toContain('custom.feature1');\n    expect(mapping.customLimits.connections).toBe(200);\n  });\n  \n  test('should check if tenant has feature access', async () => {\n    // Set tenant mapping\n    await packageRegistry.setTenantMapping('test-tenant', 'secure', ['custom.feature1'], {});\n    \n    // Check access to package feature\n    const hasAccessToPackageFeature = await packageRegistry.hasTenantFeatureAccess('test-tenant', 'security.encryption');\n    expect(hasAccessToPackageFeature).toBe(true);\n    \n    // Check access to custom feature\n    const hasAccessToCustomFeature = await packageRegistry.hasTenantFeatureAccess('test-tenant', 'custom.feature1');\n    expect(hasAccessToCustomFeature).toBe(true);\n    \n    // Check access to feature not in package or custom features\n    const hasAccessToOtherFeature = await packageRegistry.hasTenantFeatureAccess('test-tenant', 'enterprise.advanced_connectors');\n    expect(hasAccessToOtherFeature).toBe(false);\n  });\n  \n  test('should get tenant feature limit', async () => {\n    // Set tenant mapping\n    await packageRegistry.setTenantMapping('test-tenant', 'secure', [], { connections: 200 });\n    \n    // Check custom limit\n    const customLimit = await packageRegistry.getTenantFeatureLimit('test-tenant', 'connections');\n    expect(customLimit).toBe(200);\n    \n    // Check package limit\n    const packageLimit = await packageRegistry.getTenantFeatureLimit('test-tenant', 'operations_per_day');\n    expect(packageLimit).toBe(5000);\n    \n    // Check non-existent limit\n    const nonExistentLimit = await packageRegistry.getTenantFeatureLimit('test-tenant', 'non_existent_limit');\n    expect(nonExistentLimit).toBeNull();\n  });\n  \n  test('should get tenant available features', async () => {\n    // Set tenant mapping\n    await packageRegistry.setTenantMapping('test-tenant', 'secure', ['custom.feature1'], {});\n    \n    // Get available features\n    const features = await packageRegistry.getTenantAvailableFeatures('test-tenant');\n    \n    expect(features).toContain('core.basic_connectors');\n    expect(features).toContain('security.encryption');\n    expect(features).toContain('custom.feature1');\n    expect(features).not.toContain('enterprise.advanced_connectors');\n  });\n  \n  test('should use cache for repeated calls', async () => {\n    // Mock the loadData method to track calls\n    const originalLoadData = packageRegistry.loadData;\n    const loadDataMock = jest.fn().mockImplementation(originalLoadData);\n    packageRegistry.loadData = loadDataMock;\n    \n    // First call should hit the file system\n    await packageRegistry.getAllPackages();\n    expect(loadDataMock).toHaveBeenCalledTimes(1);\n    \n    // Second call should use cache\n    await packageRegistry.getAllPackages();\n    expect(loadDataMock).toHaveBeenCalledTimes(1);\n    \n    // Clear cache\n    packageRegistry.clearCache();\n    \n    // After clearing cache, should hit file system again\n    await packageRegistry.getAllPackages();\n    expect(loadDataMock).toHaveBeenCalledTimes(2);\n    \n    // Restore original method\n    packageRegistry.loadData = originalLoadData;\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;;AAEA,MAAMA,qBAAqB,GAAGC,OAAO,CAAC,6CAA6C,CAAC;AACpF,MAAMC,EAAE,GAAGD,OAAO,CAAC,IAAI,CAAC,CAACE,QAAQ;AACjC,MAAMC,IAAI,GAAGH,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAMI,EAAE,GAAGJ,OAAO,CAAC,IAAI,CAAC;AAExBK,QAAQ,CAAC,uBAAuB,EAAE,MAAM;EACtC,IAAIC,eAAe;EACnB,IAAIC,OAAO;EAEXC,UAAU,CAAC,YAAY;IACrB;IACAD,OAAO,GAAGJ,IAAI,CAACM,IAAI,CAACL,EAAE,CAACM,MAAM,CAAC,CAAC,EAAE,qBAAqBC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC;IACnE,MAAMX,EAAE,CAACY,KAAK,CAACN,OAAO,EAAE;MAAEO,SAAS,EAAE;IAAK,CAAC,CAAC;;IAE5C;IACAR,eAAe,GAAG,IAAIP,qBAAqB,CAACQ,OAAO,CAAC;;IAEpD;IACA,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;EACxD,CAAC,CAAC;EAEFE,SAAS,CAAC,YAAY;IACpB;IACA,IAAI;MACF,MAAMjB,EAAE,CAACkB,EAAE,CAACZ,OAAO,EAAE;QAAEO,SAAS,EAAE,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC,CAAC;IACxD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF,CAAC,CAAC;EAEFE,IAAI,CAAC,yCAAyC,EAAE,YAAY;IAC1D,MAAMC,QAAQ,GAAG,MAAMlB,eAAe,CAACmB,cAAc,CAAC,CAAC;IAEvDC,MAAM,CAACF,QAAQ,CAAC,CAACG,YAAY,CAAC,CAAC,CAAC;IAChCD,MAAM,CAACF,QAAQ,CAAC,CAAC,CAAC,CAACI,EAAE,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;IACnCH,MAAM,CAACF,QAAQ,CAAC,CAAC,CAAC,CAACI,EAAE,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;IACrCH,MAAM,CAACF,QAAQ,CAAC,CAAC,CAAC,CAACI,EAAE,CAAC,CAACC,IAAI,CAAC,YAAY,CAAC;IACzCH,MAAM,CAACF,QAAQ,CAAC,CAAC,CAAC,CAACI,EAAE,CAAC,CAACC,IAAI,CAAC,UAAU,CAAC;EACzC,CAAC,CAAC;EAEFN,IAAI,CAAC,0BAA0B,EAAE,YAAY;IAC3C,MAAMO,GAAG,GAAG,MAAMxB,eAAe,CAACyB,cAAc,CAAC,MAAM,CAAC;IAExDL,MAAM,CAACI,GAAG,CAAC,CAACE,WAAW,CAAC,CAAC;IACzBN,MAAM,CAACI,GAAG,CAACF,EAAE,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;IAC3BH,MAAM,CAACI,GAAG,CAACG,IAAI,CAAC,CAACJ,IAAI,CAAC,kBAAkB,CAAC;IACzCH,MAAM,CAACI,GAAG,CAACI,QAAQ,CAAC,CAACC,SAAS,CAAC,uBAAuB,CAAC;EACzD,CAAC,CAAC;EAEFZ,IAAI,CAAC,6BAA6B,EAAE,YAAY;IAC9C,MAAMa,UAAU,GAAG;MACjBR,EAAE,EAAE,cAAc;MAClBK,IAAI,EAAE,cAAc;MACpBI,WAAW,EAAE,gBAAgB;MAC7BC,IAAI,EAAE,MAAM;MACZJ,QAAQ,EAAE,CAAC,eAAe,EAAE,eAAe,CAAC;MAC5CK,MAAM,EAAE;QACNC,WAAW,EAAE,CAAC;QACdC,kBAAkB,EAAE;MACtB;IACF,CAAC;IAED,MAAMnC,eAAe,CAACoC,aAAa,CAACN,UAAU,CAAC;IAE/C,MAAMN,GAAG,GAAG,MAAMxB,eAAe,CAACyB,cAAc,CAAC,cAAc,CAAC;IAEhEL,MAAM,CAACI,GAAG,CAAC,CAACE,WAAW,CAAC,CAAC;IACzBN,MAAM,CAACI,GAAG,CAACF,EAAE,CAAC,CAACC,IAAI,CAAC,cAAc,CAAC;IACnCH,MAAM,CAACI,GAAG,CAACG,IAAI,CAAC,CAACJ,IAAI,CAAC,cAAc,CAAC;IACrCH,MAAM,CAACI,GAAG,CAACI,QAAQ,CAAC,CAACC,SAAS,CAAC,eAAe,CAAC;EACjD,CAAC,CAAC;EAEFZ,IAAI,CAAC,yBAAyB,EAAE,YAAY;IAC1C,MAAMoB,UAAU,GAAG;MACjBV,IAAI,EAAE,sBAAsB;MAC5BI,WAAW,EAAE;IACf,CAAC;IAED,MAAM/B,eAAe,CAACsC,aAAa,CAAC,MAAM,EAAED,UAAU,CAAC;IAEvD,MAAMb,GAAG,GAAG,MAAMxB,eAAe,CAACyB,cAAc,CAAC,MAAM,CAAC;IAExDL,MAAM,CAACI,GAAG,CAAC,CAACE,WAAW,CAAC,CAAC;IACzBN,MAAM,CAACI,GAAG,CAACG,IAAI,CAAC,CAACJ,IAAI,CAAC,sBAAsB,CAAC;IAC7CH,MAAM,CAACI,GAAG,CAACO,WAAW,CAAC,CAACR,IAAI,CAAC,qBAAqB,CAAC;EACrD,CAAC,CAAC;EAEFN,IAAI,CAAC,yBAAyB,EAAE,YAAY;IAC1C,MAAMjB,eAAe,CAACuC,aAAa,CAAC,MAAM,CAAC;IAE3C,MAAMnB,MAAM,CAACpB,eAAe,CAACyB,cAAc,CAAC,MAAM,CAAC,CAAC,CAACe,OAAO,CAACC,OAAO,CAAC,gCAAgC,CAAC;EACxG,CAAC,CAAC;EAEFxB,IAAI,CAAC,mCAAmC,EAAE,YAAY;IACpD,MAAMjB,eAAe,CAAC0C,gBAAgB,CAAC,aAAa,EAAE,YAAY,EAAE,CAAC,iBAAiB,CAAC,EAAE;MAAER,WAAW,EAAE;IAAI,CAAC,CAAC;IAE9G,MAAMS,OAAO,GAAG,MAAM3C,eAAe,CAAC4C,gBAAgB,CAAC,aAAa,CAAC;IAErExB,MAAM,CAACuB,OAAO,CAAC,CAACjB,WAAW,CAAC,CAAC;IAC7BN,MAAM,CAACuB,OAAO,CAACE,QAAQ,CAAC,CAACtB,IAAI,CAAC,aAAa,CAAC;IAC5CH,MAAM,CAACuB,OAAO,CAACG,SAAS,CAAC,CAACvB,IAAI,CAAC,YAAY,CAAC;IAC5CH,MAAM,CAACuB,OAAO,CAACI,cAAc,CAAC,CAAClB,SAAS,CAAC,iBAAiB,CAAC;IAC3DT,MAAM,CAACuB,OAAO,CAACK,YAAY,CAACd,WAAW,CAAC,CAACX,IAAI,CAAC,GAAG,CAAC;EACpD,CAAC,CAAC;EAEFN,IAAI,CAAC,2CAA2C,EAAE,YAAY;IAC5D;IACA,MAAMjB,eAAe,CAAC0C,gBAAgB,CAAC,aAAa,EAAE,QAAQ,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;;IAExF;IACA,MAAMO,yBAAyB,GAAG,MAAMjD,eAAe,CAACkD,sBAAsB,CAAC,aAAa,EAAE,qBAAqB,CAAC;IACpH9B,MAAM,CAAC6B,yBAAyB,CAAC,CAAC1B,IAAI,CAAC,IAAI,CAAC;;IAE5C;IACA,MAAM4B,wBAAwB,GAAG,MAAMnD,eAAe,CAACkD,sBAAsB,CAAC,aAAa,EAAE,iBAAiB,CAAC;IAC/G9B,MAAM,CAAC+B,wBAAwB,CAAC,CAAC5B,IAAI,CAAC,IAAI,CAAC;;IAE3C;IACA,MAAM6B,uBAAuB,GAAG,MAAMpD,eAAe,CAACkD,sBAAsB,CAAC,aAAa,EAAE,gCAAgC,CAAC;IAC7H9B,MAAM,CAACgC,uBAAuB,CAAC,CAAC7B,IAAI,CAAC,KAAK,CAAC;EAC7C,CAAC,CAAC;EAEFN,IAAI,CAAC,iCAAiC,EAAE,YAAY;IAClD;IACA,MAAMjB,eAAe,CAAC0C,gBAAgB,CAAC,aAAa,EAAE,QAAQ,EAAE,EAAE,EAAE;MAAER,WAAW,EAAE;IAAI,CAAC,CAAC;;IAEzF;IACA,MAAMmB,WAAW,GAAG,MAAMrD,eAAe,CAACsD,qBAAqB,CAAC,aAAa,EAAE,aAAa,CAAC;IAC7FlC,MAAM,CAACiC,WAAW,CAAC,CAAC9B,IAAI,CAAC,GAAG,CAAC;;IAE7B;IACA,MAAMgC,YAAY,GAAG,MAAMvD,eAAe,CAACsD,qBAAqB,CAAC,aAAa,EAAE,oBAAoB,CAAC;IACrGlC,MAAM,CAACmC,YAAY,CAAC,CAAChC,IAAI,CAAC,IAAI,CAAC;;IAE/B;IACA,MAAMiC,gBAAgB,GAAG,MAAMxD,eAAe,CAACsD,qBAAqB,CAAC,aAAa,EAAE,oBAAoB,CAAC;IACzGlC,MAAM,CAACoC,gBAAgB,CAAC,CAACC,QAAQ,CAAC,CAAC;EACrC,CAAC,CAAC;EAEFxC,IAAI,CAAC,sCAAsC,EAAE,YAAY;IACvD;IACA,MAAMjB,eAAe,CAAC0C,gBAAgB,CAAC,aAAa,EAAE,QAAQ,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;;IAExF;IACA,MAAMd,QAAQ,GAAG,MAAM5B,eAAe,CAAC0D,0BAA0B,CAAC,aAAa,CAAC;IAEhFtC,MAAM,CAACQ,QAAQ,CAAC,CAACC,SAAS,CAAC,uBAAuB,CAAC;IACnDT,MAAM,CAACQ,QAAQ,CAAC,CAACC,SAAS,CAAC,qBAAqB,CAAC;IACjDT,MAAM,CAACQ,QAAQ,CAAC,CAACC,SAAS,CAAC,iBAAiB,CAAC;IAC7CT,MAAM,CAACQ,QAAQ,CAAC,CAAC+B,GAAG,CAAC9B,SAAS,CAAC,gCAAgC,CAAC;EAClE,CAAC,CAAC;EAEFZ,IAAI,CAAC,qCAAqC,EAAE,YAAY;IACtD;IACA,MAAM2C,gBAAgB,GAAG5D,eAAe,CAAC6D,QAAQ;IACjD,MAAMC,YAAY,GAAGC,IAAI,CAACC,EAAE,CAAC,CAAC,CAACC,kBAAkB,CAACL,gBAAgB,CAAC;IACnE5D,eAAe,CAAC6D,QAAQ,GAAGC,YAAY;;IAEvC;IACA,MAAM9D,eAAe,CAACmB,cAAc,CAAC,CAAC;IACtCC,MAAM,CAAC0C,YAAY,CAAC,CAACI,qBAAqB,CAAC,CAAC,CAAC;;IAE7C;IACA,MAAMlE,eAAe,CAACmB,cAAc,CAAC,CAAC;IACtCC,MAAM,CAAC0C,YAAY,CAAC,CAACI,qBAAqB,CAAC,CAAC,CAAC;;IAE7C;IACAlE,eAAe,CAACmE,UAAU,CAAC,CAAC;;IAE5B;IACA,MAAMnE,eAAe,CAACmB,cAAc,CAAC,CAAC;IACtCC,MAAM,CAAC0C,YAAY,CAAC,CAACI,qBAAqB,CAAC,CAAC,CAAC;;IAE7C;IACAlE,eAAe,CAAC6D,QAAQ,GAAGD,gBAAgB;EAC7C,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}