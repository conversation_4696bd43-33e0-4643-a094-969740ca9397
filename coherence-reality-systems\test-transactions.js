const { generate<PERSON>ri<PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON><PERSON><PERSON>, getAddress, sign } = require('./crypto');
const Transaction = require('./transaction');
const TransactionPool = require('./transaction-pool');
const http = require('http');
const { promisify } = require('util');
const sleep = promisify(setTimeout);

// Configuration
const RPC_URL = 'http://localhost:8080';
const TEST_ITERATIONS = 3;
const TX_DELAY_MS = 1000;

// Generate test accounts
const accounts = [];
for (let i = 0; i < 3; i++) {
  const privateKey = generatePrivateKey();
  const publicKey = getPublicKey(privateKey);
  const address = getAddress(publicKey);
  
  accounts.push({
    privateKey,
    publicKey,
    address,
    nonce: '0'
  });
}

// Helper function to make HTTP requests
async function httpRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request({
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(options.headers || {})
      }
    }, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = responseData ? JSON.parse(responseData) : {};
          if (res.statusCode >= 400) {
            const error = new Error(result.error || 'Request failed');
            error.statusCode = res.statusCode;
            error.response = result;
            reject(error);
          } else {
            resolve(result);
          }
        } catch (error) {
          error.responseData = responseData;
          reject(error);
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Test transaction submission
async function testTransaction(sender, recipient, value, nonce) {
  console.log(`\n🔹 Sending ${value} AE from ${sender.address.substring(0, 10)}... to ${recipient.address.substring(0, 10)}...`);
  
  const tx = new Transaction({
    from: sender.address,
    to: recipient.address,
    value: value.toString(),
    nonce: nonce.toString(),
    gasPrice: '1000000000', // 1 Gwei
    gasLimit: '21000' // Standard gas limit for simple transfers
  });
  
  // Sign the transaction
  const signature = tx.sign(sender.privateKey);
  tx.signature = signature;
  
  // Submit transaction
  try {
    const result = await httpRequest({
      hostname: 'localhost',
      port: 8080,
      path: '/tx',
      method: 'POST'
    }, tx);
    
    console.log('✅ Transaction submitted:', {
      txHash: result.txHash,
      status: result.status,
      message: result.message
    });
    
    return result.txHash;
  } catch (error) {
    console.error('❌ Transaction failed:', error.message);
    if (error.response) {
      console.error('Error details:', error.response);
    }
    throw error;
  }
}

// Test account balance check
async function testGetBalance(address) {
  try {
    const result = await httpRequest({
      hostname: 'localhost',
      port: 8080,
      path: `/account/${address}`,
      method: 'GET'
    });
    
    console.log(`💰 Account ${address.substring(0, 10)}... balance:`, result.balance, 'AE');
    return result.balance;
  } catch (error) {
    console.error('❌ Failed to get balance:', error.message);
    throw error;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting KetherNet transaction tests');
  console.log('====================================');
  
  try {
    // Test server health
    console.log('\n🔄 Checking server health...');
    const health = await httpRequest({
      hostname: 'localhost',
      port: 8080,
      path: '/health',
      method: 'GET'
    });
    
    console.log('✅ Server health:', health.status);
    
    // Test accounts
    const [alice, bob, charlie] = accounts;
    
    // Get initial balances
    console.log('\n📊 Initial account balances:');
    await testGetBalance(alice.address);
    await testGetBalance(bob.address);
    await testGetBalance(charlie.address);
    
    // Test transactions
    console.log('\n🔄 Testing transactions...');
    for (let i = 0; i < TEST_ITERATIONS; i++) {
      console.log(`\n🔄 Test iteration ${i + 1}/${TEST_ITERATIONS}`);
      
      // Alice sends to Bob
      await testTransaction(alice, bob, '1000000000000000000', i * 3);
      await sleep(TX_DELAY_MS);
      
      // Bob sends to Charlie
      await testTransaction(bob, charlie, '500000000000000000', i * 3 + 1);
      await sleep(TX_DELAY_MS);
      
      // Charlie sends to Alice
      await testTransaction(charlie, alice, '250000000000000000', i * 3 + 2);
      await sleep(TX_DELAY_MS);
    }
    
    // Get final balances
    console.log('\n📊 Final account balances:');
    await testGetBalance(alice.address);
    await testGetBalance(bob.address);
    await testGetBalance(charlie.address);
    
    console.log('\n✨ All tests completed successfully!');
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the tests
runTests().catch(console.error);
