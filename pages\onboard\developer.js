import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import AgreementViewer from '../../components/AgreementViewer';
import ConsentForm from '../../components/ConsentForm';

// In a real implementation, this would be fetched from an API or CMS
const DEVELOPER_AGREEMENT = {
  title: "NovaFuse App Store Developer Pact",
  version: "1.0",
  date: "April 23, 2025",
  fileName: "novafuse-app-store-developer-pact-v1.0.pdf",
  content: (
    <>
      <h1>NovaFuse App Store Developer Pact: 18/82 Promo Track</h1>
      <h2>Empowering Next-Gen Compliance Innovators</h2>

      <h3>1. Preamble</h3>
      <p>This Developer Agreement ("Agreement") is made by and between NovaGRC, Inc. d/b/a NovaFuse ("NovaFuse") and the App Developer ("Developer") for participation in the 18/82 App Store Promo Track.</p>

      <p>This Agreement outlines terms governing revenue share, equity eligibility, app certification, and future transition plans, for developers accepted into the first 50-app cohort launching on the NovaFuse App Store.</p>

      <h3>2. Eligibility & Certification</h3>
      <p>To qualify for the 18/82 Promo Track, Developer must:</p>

      <p><strong>NovaCert Approval</strong>: Pass NovaFuse's proprietary certification process including:</p>
      <ul>
        <li>OWASP Top 10 security compliance</li>
        <li>Static and dynamic code analysis</li>
        <li>Penetration testing</li>
        <li>AI-powered bias and ethics screening</li>
        <li>Compliance with all applicable regulatory standards</li>
      </ul>

      <p><strong>Originality</strong>: Submit a product with ≥85% original codebase as determined by NovaFuse's proprietary analysis tools.</p>

      <p><strong>Vertical Exclusivity</strong>: App must represent a unique regulatory use case within the NovaFuse ecosystem, with NovaFuse having sole discretion to determine uniqueness.</p>

      <p><strong>Launch Commitment</strong>: Ship a production-grade app within 18 months of NovaFuse GA, with "production-grade" defined at NovaFuse's sole discretion.</p>

      <p><strong>Performance Bond</strong>: Submit a refundable $10,000 staking bond (returned upon hitting $100K GMV or forfeited if Developer fails to meet obligations).</p>

      <h3>3. Revenue Terms & Duration</h3>
      <p><strong>82% Net Revenue Share</strong>: Paid monthly for 5 years, calculated as:</p>
      <ul>
        <li>Gross Merchandise Volume (GMV) minus payment processing fees, applicable taxes, refunds, and chargebacks</li>
        <li>Payments processed within 30 days of month end</li>
        <li>Minimum performance threshold of $25,000 GMV per quarter after Year 1 to maintain 82% status</li>
      </ul>

      <p><strong>Post-Promo Transition</strong>:</p>
      <ul>
        <li>Year 4: Developer is eligible for "Elite Tier" at 70/30 split + performance bonuses.</li>
        <li>Year 5+: Automatic transition to tiered revenue model unless Developer exceeds $5M GMV/year.</li>
        <li>NovaFuse reserves the right to modify the tiered revenue model with 90 days notice.</li>
      </ul>

      {/* This is just a sample - the full agreement would continue here */}
      <p>... [Full agreement content continues] ...</p>
    </>
  )
};

const DeveloperOnboarding = () => {
  const router = useRouter();
  const [isAgreementRead, setIsAgreementRead] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    userName: '',
    userEmail: '',
    userTitle: '',
    companyName: ''
  });

  // In a real implementation, this would be fetched from the user's session
  const userId = 'temp-user-id';

  const handleConsent = async () => {
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/consent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          userName: formData.userName,
          userEmail: formData.userEmail,
          userTitle: formData.userTitle,
          companyName: formData.companyName,
          agreementType: 'developer',
          agreementVersion: DEVELOPER_AGREEMENT.version,
          agreementTitle: DEVELOPER_AGREEMENT.title
        }),
      });

      const data = await response.json();

      if (data.success) {
        // In a real implementation, you would store the consent in the user's session
        // and redirect to the next step in the onboarding process
        router.push('/partner-signup?agreed=true&type=developer');
      } else {
        // Handle error
        console.error('Failed to record consent:', data.message);
        alert('There was an error recording your consent. Please try again.');
      }
    } catch (error) {
      console.error('Error:', error);
      alert('There was an error recording your consent. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <>
      <Head>
        <title>App Store Developer Agreement | NovaFuse</title>
        <meta name="description" content="Review and accept the NovaFuse App Store Developer Pact" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        <header className="bg-blue-900 text-white py-4">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center">
              <Link href="/" className="text-2xl font-bold">
                NovaFuse
              </Link>
              <Link href="/partner-signup" className="text-sm hover:underline">
                Back to Signup
              </Link>
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl font-bold text-center mb-4">Daring Developer Agreement</h1>
            <p className="text-center text-gray-400 mb-8">From 1882 to 18/82: Revolutionizing the Compliance App Economy</p>

            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="text-xl font-semibold mb-4">Your Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name
                  </label>
                  <input
                    type="text"
                    name="userName"
                    value={formData.userName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    name="userEmail"
                    value={formData.userEmail}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Job Title
                  </label>
                  <input
                    type="text"
                    name="userTitle"
                    value={formData.userTitle}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Company/Developer Name
                  </label>
                  <input
                    type="text"
                    name="companyName"
                    value={formData.companyName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
              </div>
            </div>

            <div className="mb-8">
              <AgreementViewer
                agreementTitle={DEVELOPER_AGREEMENT.title}
                agreementContent={DEVELOPER_AGREEMENT.content}
                agreementVersion={DEVELOPER_AGREEMENT.version}
                agreementDate={DEVELOPER_AGREEMENT.date}
                downloadFileName={DEVELOPER_AGREEMENT.fileName}
                onFullyScrolled={() => setIsAgreementRead(true)}
              />
            </div>

            <ConsentForm
              agreementTitle={DEVELOPER_AGREEMENT.title}
              agreementVersion={DEVELOPER_AGREEMENT.version}
              onConsent={handleConsent}
              isAgreementRead={isAgreementRead}
              isSubmitting={isSubmitting}
            />
          </div>
        </main>

        <footer className="bg-gray-100 py-6 mt-12">
          <div className="container mx-auto px-4 text-center text-sm text-gray-600">
            <p>© {new Date().getFullYear()} NovaGRC, Inc. d/b/a NovaFuse. All rights reserved.</p>
            <p className="mt-2">
              <Link href="/privacy-policy" className="text-blue-600 hover:underline mx-2">
                Privacy Policy
              </Link>
              <Link href="/terms-of-service" className="text-blue-600 hover:underline mx-2">
                Terms of Service
              </Link>
            </p>
          </div>
        </footer>
      </div>
    </>
  );
};

export default DeveloperOnboarding;
