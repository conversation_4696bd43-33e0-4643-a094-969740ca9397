<!DOCTYPE html>
<html>
<head>
    <title>Simple Mermaid to SVG</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@9/dist/mermaid.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        #diagram { border: 1px solid #ccc; padding: 20px; margin: 20px 0; min-height: 300px; }
        textarea { width: 100%; height: 200px; }
        button { padding: 8px 16px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Simple Mermaid to SVG</h1>
    
    <div>
        <button onclick="loadExample()">Load Example</button>
        <button onclick="render()">Render</button>
        <button onclick="downloadSVG()">Download SVG</button>
    </div>
    
    <div>
        <textarea id="mermaidCode" spellcheck="false">graph TD
    A[Start] --> B{Is it?}
    B -->|Yes| C[OK]
    C --> D[Rethink]
    D --> B
    B -->|No| E[End]</textarea>
    </div>
    
    <div id="diagram">
        <!-- Mermaid will render here -->
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose'
        });

        let currentSVG = '';

        // Render function
        async function render() {
            const code = document.getElementById('mermaidCode').value;
            const container = document.getElementById('diagram');
            
            try {
                // Clear previous content
                container.innerHTML = '';
                
                // Insert the mermaid code
                container.innerHTML = `<div class="mermaid">${code}</div>`;
                
                // Initialize and render
                await mermaid.init(undefined, document.querySelectorAll('.mermaid'));
                
                // Store the SVG for downloading
                const svgElement = container.querySelector('svg');
                if (svgElement) {
                    currentSVG = new XMLSerializer().serializeToString(svgElement);
                    console.log('SVG rendered successfully');
                }
            } catch (error) {
                container.innerHTML = `<div style="color:red">Error: ${error.message}</div>`;
                console.error('Rendering error:', error);
            }
        }

        // Download SVG
        function downloadSVG() {
            if (!currentSVG) {
                alert('Please render a diagram first');
                return;
            }
            
            const blob = new Blob([currentSVG], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'diagram.svg';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Load example
        function loadExample() {
            const example = `graph TD
    A[Start] --> B{Is it?}
    B -->|Yes| C[OK]
    C --> D[Rethink]
    D --> B
    B -->|No| E[End]`;
            document.getElementById('mermaidCode').value = example;
            render();
        }

        // Initial render
        render();
    </script>
</body>
</html>
