#!/usr/bin/env python3
"""
Trinity CSDE Test

This script tests the Trinity CSDE implementation, verifying:
1. The Father (Governance) component with π-based compliance cycles
2. The Son (Detection) component with ϕ-based threat weighting
3. The Spirit (Response) component with (ℏ + c^-1)-based adaptive response
4. The complete Trinity CSDE formula
"""

import os
import sys
import json
import math
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# Add the parent directory to the path so we can import the CSDE module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# Import the Trinity CSDE
from csde.trinity_csde import TrinityCSDECore

# Create results directory
RESULTS_DIR = "trinity_csde_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

def test_father_component():
    """
    Test the Father (Governance) component: πG
    """
    print("\n=== Testing Father (Governance) Component: πG ===")
    
    # Initialize Trinity CSDE
    csde = TrinityCSDECore()
    
    # Create test governance data
    governance_data = {
        "compliance_score": 0.85,
        "audit_frequency": 4,
        "policies": [
            {"id": "POL-001", "name": "Access Control Policy", "effectiveness": 0.9},
            {"id": "POL-002", "name": "Data Protection Policy", "effectiveness": 0.8},
            {"id": "POL-003", "name": "Incident Response Policy", "effectiveness": 0.85},
            {"id": "POL-004", "name": "Risk Management Policy", "effectiveness": 0.75},
            {"id": "POL-005", "name": "Governance Policy", "effectiveness": 0.9},
            {"id": "POL-006", "name": "Business Continuity Policy", "effectiveness": 0.8},
            {"id": "POL-007", "name": "Vendor Management Policy", "effectiveness": 0.7},
            {"id": "POL-008", "name": "Acceptable Use Policy", "effectiveness": 0.85},
            {"id": "POL-009", "name": "Security Awareness Policy", "effectiveness": 0.75},
            {"id": "POL-010", "name": "Compliance Policy", "effectiveness": 0.9}
        ]
    }
    
    # Process Father component
    father_result = csde.father_component(governance_data)
    
    # Print results
    print(f"Compliance Score: {governance_data['compliance_score']}")
    print(f"Audit Frequency: {governance_data['audit_frequency']}")
    print(f"Number of Policies: {len(governance_data['policies'])}")
    print(f"π-aligned Audit Cycles: {father_result['audit_cycles']:.4f}")
    print(f"Policy Effectiveness: {father_result['policy_effectiveness']:.4f}")
    print(f"Governance Score: {father_result['governance_score']:.4f}")
    print(f"Father Component Result (πG): {father_result['result']:.4f}")
    
    # Verify that π is correctly applied to audit cycles
    expected_cycles = math.pi * governance_data["audit_frequency"]
    assert abs(father_result["audit_cycles"] - expected_cycles) < 1e-6, "π-based audit cycles incorrect"
    
    # Verify that the governance score is correctly calculated
    expected_score = governance_data["compliance_score"] * expected_cycles * father_result["policy_effectiveness"]
    assert abs(father_result["governance_score"] - expected_score) < 1e-6, "Governance score incorrect"
    
    # Verify that π scaling is correctly applied
    expected_result = math.pi * expected_score
    assert abs(father_result["result"] - expected_result) < 1e-6, "Father component result incorrect"
    
    print("Father component test PASSED")
    return father_result

def test_son_component():
    """
    Test the Son (Detection) component: ϕD
    """
    print("\n=== Testing Son (Detection) Component: ϕD ===")
    
    # Initialize Trinity CSDE
    csde = TrinityCSDECore()
    
    # Create test detection data
    detection_data = {
        "detection_capability": 0.75,
        "threat_severity": 0.8,
        "threat_confidence": 0.7,
        "detection_systems": {
            "firewall": {"effectiveness": 0.9, "coverage": 0.95},
            "ids": {"effectiveness": 0.8, "coverage": 0.85},
            "siem": {"effectiveness": 0.7, "coverage": 0.8},
            "endpoint": {"effectiveness": 0.6, "coverage": 0.75}
        },
        "threats": {
            "malware": {"severity": 0.9, "confidence": 0.8},
            "phishing": {"severity": 0.8, "confidence": 0.9},
            "ddos": {"severity": 0.7, "confidence": 0.7},
            "insider": {"severity": 0.6, "confidence": 0.5}
        }
    }
    
    # Process Son component
    son_result = csde.son_component(detection_data)
    
    # Print results
    print(f"Detection Capability: {detection_data['detection_capability']}")
    print(f"Threat Severity: {detection_data['threat_severity']}")
    print(f"Threat Confidence: {detection_data['threat_confidence']}")
    print(f"ϕ-weighted Threat Weight: {son_result['threat_weight']:.4f}")
    print(f"Detection Score: {son_result['detection_score']:.4f}")
    print(f"Son Component Result (ϕD): {son_result['result']:.4f}")
    
    # Verify that ϕ is correctly applied to threat weighting
    phi = (1 + math.sqrt(5)) / 2  # Golden ratio
    expected_weight = phi * detection_data["threat_severity"] + (1 - phi) * detection_data["threat_confidence"]
    assert abs(son_result["threat_weight"] - expected_weight) < 1e-6, "ϕ-based threat weight incorrect"
    
    # Verify that the detection score is correctly calculated
    expected_score = detection_data["detection_capability"] * expected_weight
    assert abs(son_result["detection_score"] - expected_score) < 1e-6, "Detection score incorrect"
    
    # Verify that ϕ scaling is correctly applied
    expected_result = phi * expected_score
    assert abs(son_result["result"] - expected_result) < 1e-6, "Son component result incorrect"
    
    print("Son component test PASSED")
    return son_result

def test_spirit_component():
    """
    Test the Spirit (Response) component: (ℏ + c^-1)R
    """
    print("\n=== Testing Spirit (Response) Component: (ℏ + c^-1)R ===")
    
    # Initialize Trinity CSDE
    csde = TrinityCSDECore()
    
    # Create test response data
    response_data = {
        "base_response_time": 50,  # ms
        "system_radius": 150,  # meters
        "threat_surface": 175,  # number of potential attack vectors
        "response_systems": {
            "firewall": {"response_time": 10, "effectiveness": 0.9},
            "ids": {"response_time": 50, "effectiveness": 0.8},
            "siem": {"response_time": 100, "effectiveness": 0.7},
            "soar": {"response_time": 30, "effectiveness": 0.85}
        },
        "threats": {
            "malware": 0.9,
            "phishing": 0.8,
            "ddos": 0.7,
            "insider": 0.6,
            "zero_day": 0.95
        }
    }
    
    # Process Spirit component
    spirit_result = csde.spirit_component(response_data)
    
    # Print results
    print(f"Base Response Time: {response_data['base_response_time']} ms")
    print(f"System Radius: {response_data['system_radius']} meters")
    print(f"Threat Surface: {response_data['threat_surface']} vectors")
    print(f"Entropy Threshold (ℏ): {spirit_result['entropy_threshold']:.4e}")
    print(f"Threat Entropy: {spirit_result['threat_entropy']:.4f}")
    print(f"Speed Constraint (c^-1): {spirit_result['speed_constraint']:.4f} ms")
    print(f"Response Time: {spirit_result['response_time']:.4f} ms")
    print(f"Quantum Certainty: {spirit_result['quantum_certainty']:.4f}")
    print(f"Response Score: {spirit_result['response_score']:.4f}")
    print(f"Spirit Factor (ℏ + c^-1): {spirit_result['spirit_factor']:.4e}")
    print(f"Spirit Component Result ((ℏ + c^-1)R): {spirit_result['result']:.4f}")
    
    # Verify that ℏ is correctly applied to entropy threshold
    expected_threshold = csde.PLANCK_CONSTANT * math.log(response_data["threat_surface"])
    assert abs(spirit_result["entropy_threshold"] - expected_threshold) < 1e-40, "ℏ-based entropy threshold incorrect"
    
    # Verify that c^-1 is correctly applied to speed constraint
    expected_constraint = csde.SPEED_OF_LIGHT_INV * response_data["system_radius"] * 10**9
    assert abs(spirit_result["speed_constraint"] - expected_constraint) < 1e-6, "c^-1-based speed constraint incorrect"
    
    # Verify that the response time is within the speed constraint
    assert spirit_result["response_time"] <= spirit_result["speed_constraint"], "Response time exceeds speed constraint"
    
    # Verify that the spirit factor is correctly calculated
    expected_factor = csde.PLANCK_CONSTANT * 10**34 + csde.SPEED_OF_LIGHT_INV * 10**9
    assert abs(spirit_result["spirit_factor"] - expected_factor) < 1e-6, "Spirit factor incorrect"
    
    print("Spirit component test PASSED")
    return spirit_result

def test_trinity_csde():
    """
    Test the complete Trinity CSDE formula: CSDE_Trinity = πG + ϕD + (ℏ + c^-1)R
    """
    print("\n=== Testing Trinity CSDE Formula: CSDE_Trinity = πG + ϕD + (ℏ + c^-1)R ===")
    
    # Initialize Trinity CSDE
    csde = TrinityCSDECore()
    
    # Create test data
    governance_data = {
        "compliance_score": 0.85,
        "audit_frequency": 4,
        "policies": [
            {"id": "POL-001", "name": "Access Control Policy", "effectiveness": 0.9},
            {"id": "POL-002", "name": "Data Protection Policy", "effectiveness": 0.8},
            {"id": "POL-003", "name": "Incident Response Policy", "effectiveness": 0.85},
            {"id": "POL-004", "name": "Risk Management Policy", "effectiveness": 0.75},
            {"id": "POL-005", "name": "Governance Policy", "effectiveness": 0.9},
            {"id": "POL-006", "name": "Business Continuity Policy", "effectiveness": 0.8},
            {"id": "POL-007", "name": "Vendor Management Policy", "effectiveness": 0.7},
            {"id": "POL-008", "name": "Acceptable Use Policy", "effectiveness": 0.85},
            {"id": "POL-009", "name": "Security Awareness Policy", "effectiveness": 0.75},
            {"id": "POL-010", "name": "Compliance Policy", "effectiveness": 0.9}
        ]
    }
    
    detection_data = {
        "detection_capability": 0.75,
        "threat_severity": 0.8,
        "threat_confidence": 0.7,
        "detection_systems": {
            "firewall": {"effectiveness": 0.9, "coverage": 0.95},
            "ids": {"effectiveness": 0.8, "coverage": 0.85},
            "siem": {"effectiveness": 0.7, "coverage": 0.8},
            "endpoint": {"effectiveness": 0.6, "coverage": 0.75}
        },
        "threats": {
            "malware": {"severity": 0.9, "confidence": 0.8},
            "phishing": {"severity": 0.8, "confidence": 0.9},
            "ddos": {"severity": 0.7, "confidence": 0.7},
            "insider": {"severity": 0.6, "confidence": 0.5}
        }
    }
    
    response_data = {
        "base_response_time": 50,  # ms
        "system_radius": 150,  # meters
        "threat_surface": 175,  # number of potential attack vectors
        "response_systems": {
            "firewall": {"response_time": 10, "effectiveness": 0.9},
            "ids": {"response_time": 50, "effectiveness": 0.8},
            "siem": {"response_time": 100, "effectiveness": 0.7},
            "soar": {"response_time": 30, "effectiveness": 0.85}
        },
        "threats": {
            "malware": 0.9,
            "phishing": 0.8,
            "ddos": 0.7,
            "insider": 0.6,
            "zero_day": 0.95
        }
    }
    
    # Process Trinity CSDE
    trinity_result = csde.calculate_trinity_csde(governance_data, detection_data, response_data)
    
    # Print results
    print(f"Trinity CSDE Value: {trinity_result['csde_trinity']:.4f}")
    print(f"Father Component (πG): {trinity_result['father_component']['result']:.4f}")
    print(f"Son Component (ϕD): {trinity_result['son_component']['result']:.4f}")
    print(f"Spirit Component ((ℏ + c^-1)R): {trinity_result['spirit_component']['result']:.4f}")
    print(f"Performance Factor: {trinity_result['performance_factor']}x")
    
    # Verify that the Trinity CSDE value is the sum of the three components
    expected_value = (
        trinity_result["father_component"]["result"] + 
        trinity_result["son_component"]["result"] + 
        trinity_result["spirit_component"]["result"]
    )
    assert abs(trinity_result["csde_trinity"] - expected_value) < 1e-6, "Trinity CSDE value incorrect"
    
    # Save results to file
    result_file = os.path.join(RESULTS_DIR, f"trinity_csde_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(result_file, 'w') as f:
        json.dump(trinity_result, f, indent=2, default=lambda x: float(x) if isinstance(x, np.float32) else x)
    
    print(f"Results saved to {result_file}")
    print("Trinity CSDE test PASSED")
    return trinity_result

def visualize_trinity_components(trinity_result):
    """
    Visualize the Trinity components
    """
    print("\n=== Visualizing Trinity Components ===")
    
    # Extract component results
    father_result = trinity_result["father_component"]["result"]
    son_result = trinity_result["son_component"]["result"]
    spirit_result = trinity_result["spirit_component"]["result"]
    
    # Create figure
    plt.figure(figsize=(12, 8))
    
    # Create data
    components = ['Father (πG)', 'Son (ϕD)', 'Spirit ((ℏ + c^-1)R)']
    values = [father_result, son_result, spirit_result]
    
    # Normalize values for better visualization
    total = sum(values)
    normalized_values = [v / total for v in values]
    
    # Create bar chart
    plt.bar(components, normalized_values, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
    
    # Add labels
    plt.title('Trinity CSDE Components', fontsize=16)
    plt.ylabel('Normalized Contribution', fontsize=14)
    plt.ylim(0, max(normalized_values) * 1.2)
    
    # Add value labels
    for i, v in enumerate(normalized_values):
        plt.text(i, v + 0.01, f"{v:.2f}", ha='center', fontsize=12)
    
    # Add constants labels
    plt.text(0, normalized_values[0] / 2, f"π = {math.pi:.4f}", ha='center', fontsize=12, color='white')
    plt.text(1, normalized_values[1] / 2, f"ϕ = {(1 + math.sqrt(5)) / 2:.4f}", ha='center', fontsize=12, color='white')
    plt.text(2, normalized_values[2] / 2, f"ℏ + c^-1", ha='center', fontsize=12, color='white')
    
    # Save figure
    figure_file = os.path.join(RESULTS_DIR, f"trinity_components_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
    plt.savefig(figure_file, dpi=300, bbox_inches='tight')
    
    print(f"Visualization saved to {figure_file}")

def main():
    """
    Main test function
    """
    print("=== Trinity CSDE Test ===")
    print("Testing the Trinity CSDE implementation: CSDE_Trinity = πG + ϕD + (ℏ + c^-1)R")
    
    # Run tests
    test_father_component()
    test_son_component()
    test_spirit_component()
    trinity_result = test_trinity_csde()
    
    # Visualize results
    try:
        visualize_trinity_components(trinity_result)
    except Exception as e:
        print(f"Visualization failed: {e}")
    
    # Summarize results
    print("\n=== Test Results Summary ===")
    print(f"Father (Governance) Component (πG): PASS")
    print(f"Son (Detection) Component (ϕD): PASS")
    print(f"Spirit (Response) Component ((ℏ + c^-1)R): PASS")
    print(f"Trinity CSDE Formula (CSDE_Trinity = πG + ϕD + (ℏ + c^-1)R): PASS")
    
    print("\nCONCLUSION: Trinity CSDE implementation VALIDATED")

if __name__ == "__main__":
    main()
