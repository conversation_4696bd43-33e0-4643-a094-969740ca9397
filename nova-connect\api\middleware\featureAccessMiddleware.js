/**
 * Feature Access Middleware
 * 
 * This middleware checks if a user has access to a feature.
 */

const FeatureFlagService = require('../services/FeatureFlagService');
const { AuthorizationError } = require('../utils/errors');

const featureFlagService = new FeatureFlagService();

/**
 * Check if user has access to a feature
 * 
 * @param {string} featureId - The feature ID to check
 * @returns {Function} Express middleware
 */
const hasFeatureAccess = (featureId) => {
  return async (req, res, next) => {
    try {
      // Skip check if no user is authenticated
      if (!req.user || !req.user.id) {
        return next(new AuthorizationError('Authentication required'));
      }
      
      // Check if user has access to feature
      const hasAccess = await featureFlagService.hasFeatureAccess(req.user.id, featureId);
      
      if (!hasAccess) {
        return next(new AuthorizationError(`Access to feature '${featureId}' is not included in your subscription plan`));
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Check if user has not reached feature limit
 * 
 * @param {string} featureId - The feature ID to check
 * @param {string} limitKey - The limit key to check
 * @returns {Function} Express middleware
 */
const hasNotReachedFeatureLimit = (featureId, limitKey) => {
  return async (req, res, next) => {
    try {
      // Skip check if no user is authenticated
      if (!req.user || !req.user.id) {
        return next(new AuthorizationError('Authentication required'));
      }
      
      // Check if user has reached feature limit
      const hasReached = await featureFlagService.hasReachedFeatureLimit(req.user.id, featureId, limitKey);
      
      if (hasReached) {
        return next(new AuthorizationError(`You have reached the limit for feature '${featureId}'`));
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Track feature usage
 * 
 * @param {string} featureId - The feature ID to track
 * @param {number} quantity - The quantity to track (default: 1)
 * @returns {Function} Express middleware
 */
const trackFeatureUsage = (featureId, quantity = 1) => {
  return async (req, res, next) => {
    try {
      // Skip tracking if no user is authenticated
      if (!req.user || !req.user.id) {
        return next();
      }
      
      // Track feature usage
      await featureFlagService.trackFeatureUsage(req.user.id, featureId, quantity);
      
      next();
    } catch (error) {
      // Don't fail the request if tracking fails
      console.error(`Error tracking feature usage: ${error.message}`);
      next();
    }
  };
};

module.exports = {
  hasFeatureAccess,
  hasNotReachedFeatureLimit,
  trackFeatureUsage
};
