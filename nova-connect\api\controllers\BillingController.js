/**
 * Billing Controller
 * 
 * This controller handles billing-related operations, including
 * GCP Marketplace entitlements and usage reporting.
 */

const BillingService = require('../services/BillingService');
const logger = require('../../config/logger');

// Initialize services
const billingService = new BillingService();

/**
 * Handle GCP Marketplace entitlement creation
 */
const handleEntitlementCreation = async (req, res, next) => {
  try {
    const { customerId, entitlement } = req.body;
    
    if (!customerId || !entitlement) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID and entitlement are required'
      });
    }
    
    await billingService.enableFeatures(customerId, entitlement);
    
    res.status(200).json({
      message: 'Entitlement created successfully',
      customerId,
      entitlement: {
        ...entitlement,
        status: 'ACTIVE'
      }
    });
  } catch (error) {
    logger.error('Error handling entitlement creation', { error: error.message });
    next(error);
  }
};

/**
 * Handle GCP Marketplace entitlement update
 */
const handleEntitlementUpdate = async (req, res, next) => {
  try {
    const { customerId, entitlement } = req.body;
    
    if (!customerId || !entitlement) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID and entitlement are required'
      });
    }
    
    await billingService.updateFeatures(customerId, entitlement);
    
    res.status(200).json({
      message: 'Entitlement updated successfully',
      customerId,
      entitlement
    });
  } catch (error) {
    logger.error('Error handling entitlement update', { error: error.message });
    next(error);
  }
};

/**
 * Handle GCP Marketplace entitlement deletion
 */
const handleEntitlementDeletion = async (req, res, next) => {
  try {
    const { customerId, entitlement } = req.body;
    
    if (!customerId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID is required'
      });
    }
    
    await billingService.disableFeatures(customerId, entitlement);
    
    res.status(200).json({
      message: 'Entitlement deleted successfully',
      customerId
    });
  } catch (error) {
    logger.error('Error handling entitlement deletion', { error: error.message });
    next(error);
  }
};

/**
 * Handle GCP Marketplace entitlement activation
 */
const handleEntitlementActivation = async (req, res, next) => {
  try {
    const { customerId, entitlement } = req.body;
    
    if (!customerId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID is required'
      });
    }
    
    await billingService.activateFeatures(customerId, entitlement);
    
    res.status(200).json({
      message: 'Entitlement activated successfully',
      customerId
    });
  } catch (error) {
    logger.error('Error handling entitlement activation', { error: error.message });
    next(error);
  }
};

/**
 * Handle GCP Marketplace entitlement suspension
 */
const handleEntitlementSuspension = async (req, res, next) => {
  try {
    const { customerId, entitlement } = req.body;
    
    if (!customerId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID is required'
      });
    }
    
    await billingService.suspendFeatures(customerId, entitlement);
    
    res.status(200).json({
      message: 'Entitlement suspended successfully',
      customerId
    });
  } catch (error) {
    logger.error('Error handling entitlement suspension', { error: error.message });
    next(error);
  }
};

/**
 * Get customer entitlements
 */
const getCustomerEntitlements = async (req, res, next) => {
  try {
    const { customerId } = req.params;
    
    if (!customerId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID is required'
      });
    }
    
    const entitlement = await billingService.getCustomerEntitlements(customerId);
    
    res.status(200).json(entitlement);
  } catch (error) {
    logger.error('Error getting customer entitlements', { error: error.message });
    next(error);
  }
};

/**
 * Get customer usage
 */
const getCustomerUsage = async (req, res, next) => {
  try {
    const { customerId } = req.params;
    const { startDate, endDate } = req.query;
    
    if (!customerId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID is required'
      });
    }
    
    const usage = await billingService.getCustomerUsage(customerId, startDate, endDate);
    
    res.status(200).json(usage);
  } catch (error) {
    logger.error('Error getting customer usage', { error: error.message });
    next(error);
  }
};

/**
 * Report usage
 */
const reportUsage = async (req, res, next) => {
  try {
    const { customerId, metricName, quantity, timestamp, tenantId } = req.body;
    
    if (!customerId || !metricName || quantity === undefined) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID, metric name, and quantity are required'
      });
    }
    
    await billingService.reportUsage(customerId, metricName, quantity, timestamp, tenantId);
    
    res.status(200).json({
      message: 'Usage reported successfully',
      customerId,
      metricName,
      quantity
    });
  } catch (error) {
    logger.error('Error reporting usage', { error: error.message });
    next(error);
  }
};

/**
 * Report tenant usage
 */
const reportTenantUsage = async (req, res, next) => {
  try {
    const { tenantId, metricName, quantity, timestamp } = req.body;
    
    if (!tenantId || !metricName || quantity === undefined) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Tenant ID, metric name, and quantity are required'
      });
    }
    
    await billingService.reportTenantUsage(tenantId, metricName, quantity, timestamp);
    
    res.status(200).json({
      message: 'Tenant usage reported successfully',
      tenantId,
      metricName,
      quantity
    });
  } catch (error) {
    logger.error('Error reporting tenant usage', { error: error.message });
    next(error);
  }
};

/**
 * Handle GCP Marketplace webhook
 */
const handleMarketplaceWebhook = async (req, res, next) => {
  try {
    const { event, resource } = req.body;
    
    if (!event || !resource) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Event and resource are required'
      });
    }
    
    logger.info('Received GCP Marketplace webhook', { event, resource });
    
    // Extract customer ID from resource
    const customerId = resource.customerId || resource.customer_id;
    
    if (!customerId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID is required in resource'
      });
    }
    
    // Handle different event types
    switch (event) {
      case 'ENTITLEMENT_CREATION':
        await billingService.enableFeatures(customerId, resource);
        break;
      case 'ENTITLEMENT_UPDATE':
        await billingService.updateFeatures(customerId, resource);
        break;
      case 'ENTITLEMENT_DELETION':
        await billingService.disableFeatures(customerId, resource);
        break;
      case 'ENTITLEMENT_ACTIVATION':
        await billingService.activateFeatures(customerId, resource);
        break;
      case 'ENTITLEMENT_SUSPENSION':
        await billingService.suspendFeatures(customerId, resource);
        break;
      default:
        logger.warn('Unknown event type', { event });
        return res.status(400).json({
          error: 'Bad Request',
          message: `Unknown event type: ${event}`
        });
    }
    
    res.status(200).json({
      message: 'Webhook processed successfully',
      event,
      customerId
    });
  } catch (error) {
    logger.error('Error handling marketplace webhook', { error: error.message });
    next(error);
  }
};

module.exports = {
  handleEntitlementCreation,
  handleEntitlementUpdate,
  handleEntitlementDeletion,
  handleEntitlementActivation,
  handleEntitlementSuspension,
  getCustomerEntitlements,
  getCustomerUsage,
  reportUsage,
  reportTenantUsage,
  handleMarketplaceWebhook
};
