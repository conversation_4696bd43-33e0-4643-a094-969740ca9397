/**
 * Test server for NovaTrack API integration tests
 */

const express = require('express');
const bodyParser = require('body-parser');

// Import the TrackingManager mock
const { TrackingManager } = require('../../mocks/tracking-manager.mock');

/**
 * Create a test server for NovaTrack API
 * 
 * @param {Object} options - Server options
 * @param {string} options.tempDir - Temporary directory for test data
 * @returns {Object} Express app and TrackingManager instance
 */
function createTestServer(options = {}) {
  const { tempDir } = options;
  
  // Initialize the TrackingManager
  const trackingManager = new TrackingManager(tempDir);
  
  // Create an Express app
  const app = express();
  app.use(bodyParser.json());
  
  // Define API routes
  
  // Get all requirements
  app.get('/api/v1/novatrack/requirements', (req, res) => {
    try {
      const framework = req.query.framework;
      let requirements;
      
      if (framework) {
        requirements = trackingManager.get_requirements_by_framework(framework);
      } else {
        requirements = Object.values(trackingManager.requirements);
      }
      
      res.json({
        success: true,
        data: requirements
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });
  
  // Get a requirement by ID
  app.get('/api/v1/novatrack/requirements/:id', (req, res) => {
    try {
      const requirementId = req.params.id;
      const requirement = trackingManager.get_requirement(requirementId);
      
      res.json({
        success: true,
        data: requirement
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        error: error.message
      });
    }
  });
  
  // Get activities for a requirement
  app.get('/api/v1/novatrack/requirements/:id/activities', (req, res) => {
    try {
      const requirementId = req.params.id;
      const activities = trackingManager.get_requirement_activities(requirementId);
      
      res.json({
        success: true,
        data: activities
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });
  
  // Create a requirement
  app.post('/api/v1/novatrack/requirements', (req, res) => {
    try {
      const requirementData = req.body;
      const requirement = trackingManager.create_requirement(requirementData);
      
      res.status(201).json({
        success: true,
        data: requirement
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });
  
  // Update a requirement
  app.put('/api/v1/novatrack/requirements/:id', (req, res) => {
    try {
      const requirementId = req.params.id;
      const requirementData = req.body;
      const requirement = trackingManager.update_requirement(requirementId, requirementData);
      
      res.json({
        success: true,
        data: requirement
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        error: error.message
      });
    }
  });
  
  // Delete a requirement
  app.delete('/api/v1/novatrack/requirements/:id', (req, res) => {
    try {
      const requirementId = req.params.id;
      trackingManager.delete_requirement(requirementId);
      
      res.json({
        success: true,
        message: `Requirement ${requirementId} deleted successfully`
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        error: error.message
      });
    }
  });
  
  // Create an activity
  app.post('/api/v1/novatrack/activities', (req, res) => {
    try {
      const activityData = req.body;
      const activity = trackingManager.create_activity(activityData);
      
      res.status(201).json({
        success: true,
        data: activity
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });
  
  // Get an activity by ID
  app.get('/api/v1/novatrack/activities/:id', (req, res) => {
    try {
      const activityId = req.params.id;
      const activity = trackingManager.get_activity(activityId);
      
      res.json({
        success: true,
        data: activity
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        error: error.message
      });
    }
  });
  
  // Update an activity
  app.put('/api/v1/novatrack/activities/:id', (req, res) => {
    try {
      const activityId = req.params.id;
      const activityData = req.body;
      const activity = trackingManager.update_activity(activityId, activityData);
      
      res.json({
        success: true,
        data: activity
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        error: error.message
      });
    }
  });
  
  // Delete an activity
  app.delete('/api/v1/novatrack/activities/:id', (req, res) => {
    try {
      const activityId = req.params.id;
      trackingManager.delete_activity(activityId);
      
      res.json({
        success: true,
        message: `Activity ${activityId} deleted successfully`
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        error: error.message
      });
    }
  });
  
  // Health check endpoint
  app.get('/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString()
    });
  });
  
  return { app, trackingManager };
}

module.exports = { createTestServer };
