# deploy.ps1
# KetherNet Deployment Script
# Usage: .\deploy.ps1 [-Profile <string>] [-Validators <int>] [-SkipBuild]

param (
    [Parameter(Mandatory=$false)]
    [ValidateSet('standard', 'gov', 'darpa', 'test')]
    [string]$Profile = 'standard',
    
    [Parameter(Mandatory=$false)]
    [int]$Validators = 7,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipBuild,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

# Set error action preference
$ErrorActionPreference = 'Stop'

# Configuration
$config = @{
    ProjectRoot = $PSScriptRoot
    LogDir = "$PSScriptRoot\logs"
    ConfigDir = "$PSScriptRoot\config"
    MonitoringDir = "$PSScriptRoot\monitoring"
    DockerComposeFiles = @(
        "docker-compose.core.yml"
    )
}

# Import helper functions
function Write-Header($message) {
    Write-Host "\n" + ("=" * 80) -ForegroundColor Cyan
    Write-Host "  $message" -ForegroundColor Cyan
    Write-Host ("=" * 80) -ForegroundColor Cyan
}

function Write-Success($message) {
    Write-Host "✅ $message" -ForegroundColor Green
}

function Write-Warning($message) {
    Write-Host "⚠️  $message" -ForegroundColor Yellow
}

function Write-Error($message) {
    Write-Host "❌ $message" -ForegroundColor Red
}

function Test-CommandExists {
    param($command)
    $exists = $null -ne (Get-Command $command -ErrorAction SilentlyContinue)
    return $exists
}

# Check prerequisites
function Test-Prerequisites {
    Write-Header "Checking Prerequisites"
    
    $prerequisites = @{
        'Docker' = { Test-CommandExists 'docker' }
        'Docker Compose' = { 
            $dockerVersion = docker --version
            $dockerVersion -match 'Docker version \d+\.\d+'
        }
    }
    
    $allPassed = $true
    foreach ($prereq in $prerequisites.GetEnumerator()) {
        $result = & $prereq.Value
        $status = if ($result) { 'OK' } else { 'MISSING' }
        $color = if ($result) { 'Green' } else { 'Red'; $allPassed = $false }
        
        Write-Host ("{0,-20} {1}" -f $prereq.Key, $status) -ForegroundColor $color
    }
    
    if (-not $allPassed) {
        Write-Error "Prerequisite check failed. Please install missing components."
        exit 1
    }
}

# Build Docker images
function Invoke-Build {
    if ($SkipBuild) {
        Write-Warning "Skipping build as requested"
        return
    }
    
    Write-Header "Building Docker Images"
    
    try {
        docker-compose -f docker-compose.core.yml build
        if ($LASTEXITCODE -ne 0) { throw "Build failed" }
        Write-Success "Build completed successfully"
    } catch {
        Write-Error "Build failed: $_"
        exit 1
    }
}

# Deploy based on profile
function Invoke-Deploy {
    param(
        [string]$Profile,
        [int]$Validators
    )
    
    Write-Header "Deploying KetherNet ($Profile profile)"
    
    # Add profile-specific compose files
    $composeFiles = $config.DockerComposeFiles.Clone()
    
    switch ($Profile) {
        'gov' { 
            $composeFiles += 'docker-compose.gov.yml'
            $env:VALIDATOR_THRESHOLD = 2847
        }
        'darpa' {
            $composeFiles += 'docker-compose.gov.yml'
            $env:VALIDATOR_THRESHOLD = $Validators
            $env:SECURITY_TIER = 'darpa'
        }
    }
    
    # Build compose command
    $composeArgs = $composeFiles | ForEach-Object { "-f $_" }
    $composeCmd = "docker-compose $composeArgs"
    
    try {
        # Pull latest images
        Write-Host "\n🔍 Pulling latest images..."
        Invoke-Expression "$composeCmd pull"
        
        # Start services
        Write-Host "\n🚀 Starting services..."
        Invoke-Expression "$composeCmd up -d"
        
        # Show service status
        Write-Host "\n📊 Service Status:"
        Invoke-Expression "$composeCmd ps"
        
        Write-Success "Deployment completed successfully"
        
        # Show access information
        Write-Host "\n🌐 Access Information:"
        Write-Host "- KetherNet API:    http://localhost:8080"
        Write-Host "- Prometheus:       http://localhost:9090"
        Write-Host "- Grafana:          http://localhost:3001"
        Write-Host "- Node Exporter:    http://localhost:9100"
        
    } catch {
        Write-Error "Deployment failed: $_"
        exit 1
    }
}

# Main execution
function Start-Deployment {
    param(
        [string]$Profile,
        [int]$Validators,
        [switch]$Force
    )
    
    Write-Host "\n" + ("*" * 80) -ForegroundColor Blue
    Write-Host "  KetherNet Deployment Tool" -ForegroundColor Blue
    Write-Host ("*" * 80) -ForegroundColor Blue
    
    if (-not $Force) {
        Write-Warning "This will deploy KetherNet with the '$Profile' profile"
        $confirmation = Read-Host "Are you sure you want to continue? (y/N)"
        if ($confirmation -ne 'y') {
            Write-Host "Deployment cancelled"
            exit 0
        }
    }
    
    # Create required directories
    foreach ($dir in $config.LogDir, $config.ConfigDir, $config.MonitoringDir) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    # Execute deployment steps
    Test-Prerequisites
    Invoke-Build
    Invoke-Deploy -Profile $Profile -Validators $Validators
}

# Start the deployment
Start-Deployment -Profile $Profile -Validators $Validators -Force:$Force
