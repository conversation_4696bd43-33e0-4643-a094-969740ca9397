{"extends": "base-connector", "name": "novafuse-governance-connector", "version": "1.0.0", "description": "Governance & Board Compliance connector template for NovaFuse API Superstore", "category": "governance", "base_url": "http://localhost:8000/governance", "endpoints": [{"name": "board_meetings", "path": "/board/meetings", "method": "GET", "description": "Get a list of board meetings", "parameters": [{"name": "start_date", "in": "query", "required": false, "description": "Filter by start date (YYYY-MM-DD)"}, {"name": "end_date", "in": "query", "required": false, "description": "Filter by end date (YYYY-MM-DD)"}, {"name": "status", "in": "query", "required": false, "description": "Filter by status (scheduled, completed, cancelled)"}]}, {"name": "board_meeting_details", "path": "/board/meetings/{id}", "method": "GET", "description": "Get details of a specific board meeting", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Meeting ID"}]}, {"name": "governance_policies", "path": "/policies", "method": "GET", "description": "Get a list of governance policies", "parameters": [{"name": "category", "in": "query", "required": false, "description": "Filter by policy category"}, {"name": "status", "in": "query", "required": false, "description": "Filter by status (active, draft, archived)"}]}, {"name": "governance_policy_details", "path": "/policies/{id}", "method": "GET", "description": "Get details of a specific governance policy", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Policy ID"}]}, {"name": "compliance_reports", "path": "/compliance/reports", "method": "GET", "description": "Get a list of compliance reports", "parameters": [{"name": "framework", "in": "query", "required": false, "description": "Filter by compliance framework"}, {"name": "period", "in": "query", "required": false, "description": "Filter by reporting period"}]}, {"name": "board_resolutions", "path": "/board/resolutions", "method": "GET", "description": "Get a list of board resolutions", "parameters": [{"name": "meeting_id", "in": "query", "required": false, "description": "Filter by meeting ID"}, {"name": "status", "in": "query", "required": false, "description": "Filter by status (approved, pending, rejected)"}]}]}