{"name": "test_metrics_endpoint", "status": "passed", "description": "Test metrics endpoint returns expected data.", "start": 1752966149027, "stop": 1752966149036, "uuid": "7a0f0677-fdb9-40bb-bd8e-d9b6206d623c", "historyId": "d7af41f78a2c016470befe38823f69d8", "testCaseId": "d7af41f78a2c016470befe38823f69d8", "fullName": "tests.novacortex.test_integration#test_metrics_endpoint", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.novacortex"}, {"name": "suite", "value": "test_integration"}, {"name": "host", "value": "d1cae64bda82"}, {"name": "thread", "value": "1-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.novacortex.test_integration"}]}