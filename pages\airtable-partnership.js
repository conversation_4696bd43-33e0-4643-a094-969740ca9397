import React from 'react';

export default function AirtablePartnership() {
  return (
    <div>
      {/* Hero Section */}
      <div className="bg-secondary p-8 rounded-lg mb-8 relative overflow-hidden">
        <div className="relative z-10">
          <h2 className="text-3xl font-bold mb-4">Exclusive Founding Partner Opportunity</h2>
          <p className="text-xl mb-6 max-w-3xl">
            Join NovaFuse API Superstore as a founding partner and connect your no-code database platform with the enterprise GRC market.
          </p>
          <div className="flex flex-wrap gap-4">
            <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg">
              <div className="text-3xl font-bold text-blue-400">85%</div>
              <div className="text-gray-300">Revenue Share</div>
            </div>
            <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg">
              <div className="text-3xl font-bold text-blue-400">$400K+</div>
              <div className="text-gray-300">Annual Revenue Potential</div>
            </div>
            <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg">
              <div className="text-3xl font-bold text-blue-400">1,000+</div>
              <div className="text-gray-300">Enterprise GRC Customers</div>
            </div>
            <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg">
              <div className="text-3xl font-bold text-blue-400">2 Weeks</div>
              <div className="text-gray-300">Integration Timeline</div>
            </div>
          </div>
        </div>
        <div className="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-l from-green-500 to-transparent opacity-10"></div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        {/* Left Column */}
        <div>
          <div className="bg-secondary p-6 rounded-lg mb-8">
            <h3 className="text-2xl font-bold mb-4">Why Airtable + NovaFuse?</h3>
            
            <div className="space-y-4">
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-green-600 flex items-center justify-center text-white font-bold mr-3">1</div>
                <div>
                  <h4 className="text-lg font-semibold">New Revenue Stream</h4>
                  <p className="text-gray-300">Generate new revenue from enterprise GRC customers with our 85% revenue share model—the highest in the industry.</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-green-600 flex items-center justify-center text-white font-bold mr-3">2</div>
                <div>
                  <h4 className="text-lg font-semibold">Enterprise Expansion</h4>
                  <p className="text-gray-300">Expand your footprint in regulated industries by offering compliance-ready templates and workflows.</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-green-600 flex items-center justify-center text-white font-bold mr-3">3</div>
                <div>
                  <h4 className="text-lg font-semibold">Minimal Integration Effort</h4>
                  <p className="text-gray-300">Our open SDK and API-based integration requires minimal development resources—we'll even help build it.</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-green-600 flex items-center justify-center text-white font-bold mr-3">4</div>
                <div>
                  <h4 className="text-lg font-semibold">Featured Partner Status</h4>
                  <p className="text-gray-300">As a founding partner, you'll receive premium placement in our marketplace and co-marketing opportunities.</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-secondary p-6 rounded-lg">
            <h3 className="text-2xl font-bold mb-4">Integration Use Cases</h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="text-lg font-semibold text-green-500">Compliance Task Management</h4>
                <p className="text-gray-300">Track compliance tasks, deadlines, and responsibilities in Airtable with data from NovaFuse.</p>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold text-green-500">Policy & Procedure Library</h4>
                <p className="text-gray-300">Maintain a searchable library of policies and procedures with approval workflows and version control.</p>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold text-green-500">Audit Evidence Collection</h4>
                <p className="text-gray-300">Collect and organize audit evidence in Airtable with automated mapping to compliance requirements.</p>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold text-green-500">Risk Register Management</h4>
                <p className="text-gray-300">Maintain a comprehensive risk register with assessment scores, mitigation plans, and status tracking.</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Right Column */}
        <div>
          <div className="bg-secondary p-6 rounded-lg mb-8">
            <h3 className="text-2xl font-bold mb-4">Technical Integration</h3>
            
            <p className="text-gray-300 mb-4">
              Integration with NovaFuse leverages Airtable's API for seamless data synchronization. Here's a simple example:
            </p>
            
            <div className="bg-gray-900 p-4 rounded-lg text-green-400 mb-6 overflow-x-auto">
              <pre>{`// 1. Configure Airtable connection for NovaFuse
const airtableConfig = {
  apiKey: "YOUR_API_KEY",
  baseId: "YOUR_BASE_ID",
  tables: {
    policies: "tblPolicies",
    risks: "tblRisks",
    controls: "tblControls",
    tasks: "tblTasks"
  }
};

// 2. Sync compliance tasks from NovaFuse to Airtable
async function syncComplianceTasks() {
  // Get tasks from NovaFuse
  const tasks = await novafuseClient.getTasks({
    status: "open",
    type: "compliance"
  });
  
  // Map to Airtable format
  const airtableRecords = tasks.map(task => ({
    fields: {
      Name: task.title,
      Description: task.description,
      DueDate: task.dueDate,
      Status: task.status,
      Priority: task.priority,
      AssignedTo: task.assignee,
      ComplianceRequirement: task.requirement,
      NovaFuseId: task.id
    }
  }));
  
  // Upsert to Airtable
  await airtable.base(airtableConfig.baseId)
    .table(airtableConfig.tables.tasks)
    .create(airtableRecords);
}`}</pre>
            </div>
            
            <p className="text-gray-300">
              Our SDK provides pre-built templates and synchronization utilities for common GRC use cases, making it easy to implement with minimal code.
            </p>
          </div>
          
          <div className="bg-secondary p-6 rounded-lg">
            <h3 className="text-2xl font-bold mb-4">Revenue Projection</h3>
            
            <div className="overflow-x-auto">
              <table className="w-full text-left">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="py-2">Year</th>
                    <th className="py-2">Customers</th>
                    <th className="py-2">API Calls/Month</th>
                    <th className="py-2">Annual Revenue</th>
                    <th className="py-2">Airtable Share (85%)</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-700">
                    <td className="py-2">Year 1</td>
                    <td className="py-2">40</td>
                    <td className="py-2">80,000</td>
                    <td className="py-2">$48,000</td>
                    <td className="py-2">$40,800</td>
                  </tr>
                  <tr className="border-b border-gray-700">
                    <td className="py-2">Year 2</td>
                    <td className="py-2">150</td>
                    <td className="py-2">300,000</td>
                    <td className="py-2">$180,000</td>
                    <td className="py-2">$153,000</td>
                  </tr>
                  <tr>
                    <td className="py-2">Year 3</td>
                    <td className="py-2">400</td>
                    <td className="py-2">800,000</td>
                    <td className="py-2">$480,000</td>
                    <td className="py-2">$408,000</td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <p className="text-gray-300 mt-4">
              Revenue is based on API call volume with a rate of $0.005 per call. Actual results may vary based on customer adoption and usage patterns.
            </p>
          </div>
        </div>
      </div>
      
      {/* Template Showcase Section */}
      <div className="bg-secondary p-6 rounded-lg mb-8">
        <h3 className="text-2xl font-bold mb-4 text-center">Pre-Built GRC Templates</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 border border-green-600 rounded-lg">
            <h4 className="text-lg font-semibold mb-2 text-green-500">Policy Management</h4>
            <p className="text-gray-300 mb-4">Track policies, procedures, and standards with approval workflows and version control.</p>
            <div className="text-sm text-gray-400">
              <div className="flex justify-between mb-1">
                <span>Tables:</span>
                <span>5</span>
              </div>
              <div className="flex justify-between mb-1">
                <span>Automations:</span>
                <span>8</span>
              </div>
              <div className="flex justify-between">
                <span>Views:</span>
                <span>12</span>
              </div>
            </div>
          </div>
          
          <div className="p-4 border border-green-600 rounded-lg">
            <h4 className="text-lg font-semibold mb-2 text-green-500">Risk Assessment</h4>
            <p className="text-gray-300 mb-4">Comprehensive risk register with assessment workflows, mitigation tracking, and reporting.</p>
            <div className="text-sm text-gray-400">
              <div className="flex justify-between mb-1">
                <span>Tables:</span>
                <span>7</span>
              </div>
              <div className="flex justify-between mb-1">
                <span>Automations:</span>
                <span>10</span>
              </div>
              <div className="flex justify-between">
                <span>Views:</span>
                <span>15</span>
              </div>
            </div>
          </div>
          
          <div className="p-4 border border-green-600 rounded-lg">
            <h4 className="text-lg font-semibold mb-2 text-green-500">Audit Management</h4>
            <p className="text-gray-300 mb-4">Plan, execute, and track audits with evidence collection, findings, and remediation tracking.</p>
            <div className="text-sm text-gray-400">
              <div className="flex justify-between mb-1">
                <span>Tables:</span>
                <span>6</span>
              </div>
              <div className="flex justify-between mb-1">
                <span>Automations:</span>
                <span>9</span>
              </div>
              <div className="flex justify-between">
                <span>Views:</span>
                <span>14</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Bottom Section: Next Steps */}
      <div className="bg-secondary p-6 rounded-lg">
        <h3 className="text-2xl font-bold mb-4 text-center">Next Steps</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="p-4 border border-blue-600 rounded-lg">
            <div className="text-3xl font-bold text-center mb-2">1</div>
            <h4 className="text-lg font-semibold text-center mb-2">Initial Discussion</h4>
            <p className="text-gray-300 text-center">30-minute call to discuss partnership details and answer questions.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <div className="text-3xl font-bold text-center mb-2">2</div>
            <h4 className="text-lg font-semibold text-center mb-2">Technical Review</h4>
            <p className="text-gray-300 text-center">Deep dive into the API and integration requirements with your technical team.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <div className="text-3xl font-bold text-center mb-2">3</div>
            <h4 className="text-lg font-semibold text-center mb-2">Partnership Agreement</h4>
            <p className="text-gray-300 text-center">Sign partnership agreement with revenue sharing terms and go-to-market plan.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <div className="text-3xl font-bold text-center mb-2">4</div>
            <h4 className="text-lg font-semibold text-center mb-2">Launch</h4>
            <p className="text-gray-300 text-center">Joint announcement and marketing campaign to promote the integration.</p>
          </div>
        </div>
        
        <div className="mt-8 text-center">
          <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-opacity-90 transition-colors">
            Schedule Partnership Discussion
          </button>
        </div>
      </div>
    </div>
  );
}
