#!/usr/bin/env python3
"""
UUFT Game Theory Analyzer

This module analyzes strategic interactions and decision-making processes for 18/82 patterns
and π-related relationships in game theory scenarios.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
import logging
import json
from collections import defaultdict

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uuft_game.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UUFT_Game')

# Constants
PATTERN_1882_RATIO = 18 / 82
PI = np.pi
PI_10_CUBED = PI * 10**3
RESULTS_DIR = "uuft_results/game"
os.makedirs(RESULTS_DIR, exist_ok=True)

class UUFTGameScenario:
    """Generator for game theory scenarios with configurable UUFT properties."""
    
    def __init__(self, game_type="prisoners_dilemma", num_players=2, num_strategies=2, uuft_bias=0.0):
        """
        Initialize a game theory scenario.
        
        Args:
            game_type: Type of game ("prisoners_dilemma", "stag_hunt", "chicken", "public_goods", "custom")
            num_players: Number of players in the game
            num_strategies: Number of strategies per player
            uuft_bias: Bias parameter for introducing 18/82 patterns (0.0 = none, 1.0 = maximum)
        """
        self.game_type = game_type
        self.num_players = num_players
        self.num_strategies = num_strategies
        self.uuft_bias = uuft_bias
        
        # Generate payoff matrix
        self.payoff_matrix = self._generate_payoff_matrix()
        
        # Generate strategy profiles
        self.strategy_profiles = self._generate_strategy_profiles()
        
        logger.info(f"Created {game_type} game with {num_players} players, {num_strategies} strategies, and UUFT bias {uuft_bias}")
    
    def _generate_payoff_matrix(self):
        """Generate the payoff matrix based on the game type."""
        if self.game_type == "prisoners_dilemma":
            # Classic Prisoner's Dilemma
            # (C,C) -> (-1,-1), (C,D) -> (-3,0), (D,C) -> (0,-3), (D,D) -> (-2,-2)
            payoff_matrix = np.array([
                [[-1, -1], [-3, 0]],
                [[0, -3], [-2, -2]]
            ])
            
        elif self.game_type == "stag_hunt":
            # Stag Hunt
            # (Stag,Stag) -> (2,2), (Stag,Hare) -> (0,1), (Hare,Stag) -> (1,0), (Hare,Hare) -> (1,1)
            payoff_matrix = np.array([
                [[2, 2], [0, 1]],
                [[1, 0], [1, 1]]
            ])
            
        elif self.game_type == "chicken":
            # Chicken Game
            # (Swerve,Swerve) -> (0,0), (Swerve,Straight) -> (-1,1), (Straight,Swerve) -> (1,-1), (Straight,Straight) -> (-10,-10)
            payoff_matrix = np.array([
                [[0, 0], [-1, 1]],
                [[1, -1], [-10, -10]]
            ])
            
        elif self.game_type == "public_goods":
            # Public Goods Game (simplified for 2 players)
            # (Contribute,Contribute) -> (3,3), (Contribute,Free-ride) -> (1,4), (Free-ride,Contribute) -> (4,1), (Free-ride,Free-ride) -> (2,2)
            payoff_matrix = np.array([
                [[3, 3], [1, 4]],
                [[4, 1], [2, 2]]
            ])
            
        elif self.game_type == "custom":
            # Generate random payoff matrix
            payoff_matrix = self._generate_custom_payoff_matrix()
            
        else:
            raise ValueError(f"Unknown game type: {self.game_type}")
        
        # Apply UUFT bias if specified
        if self.uuft_bias > 0:
            payoff_matrix = self._apply_uuft_bias(payoff_matrix)
        
        return payoff_matrix
    
    def _generate_custom_payoff_matrix(self):
        """Generate a custom payoff matrix with random values."""
        # Initialize payoff matrix
        shape = tuple([self.num_strategies] * self.num_players + [self.num_players])
        payoff_matrix = np.random.uniform(-5, 5, shape)
        
        return payoff_matrix
    
    def _apply_uuft_bias(self, payoff_matrix):
        """
        Apply UUFT bias to the payoff matrix.
        
        This modifies the payoffs to more closely follow 18/82 patterns
        based on the uuft_bias parameter.
        """
        if self.uuft_bias <= 0:
            return payoff_matrix
        
        # Create a copy of the payoff matrix to modify
        modified_matrix = payoff_matrix.copy()
        
        # For 2-player, 2-strategy games
        if self.num_players == 2 and self.num_strategies == 2:
            # Modify payoffs to create 18/82 pattern in Nash equilibrium distribution
            # Increase payoff for one equilibrium to make it more likely
            
            # Identify potential equilibria
            p1_best_response = np.zeros(self.num_strategies, dtype=int)
            p2_best_response = np.zeros(self.num_strategies, dtype=int)
            
            for s1 in range(self.num_strategies):
                p2_best_response[s1] = np.argmax(payoff_matrix[s1, :, 1])
            
            for s2 in range(self.num_strategies):
                p1_best_response[s2] = np.argmax(payoff_matrix[:, s2, 0])
            
            # Find Nash equilibria
            equilibria = []
            for s1 in range(self.num_strategies):
                for s2 in range(self.num_strategies):
                    if p1_best_response[s2] == s1 and p2_best_response[s1] == s2:
                        equilibria.append((s1, s2))
            
            if equilibria:
                # Select one equilibrium to enhance
                target_eq = equilibria[0]
                
                # Enhance payoffs for this equilibrium
                boost = 2.0 * self.uuft_bias
                for p in range(self.num_players):
                    modified_matrix[target_eq[0], target_eq[1], p] += boost
        
        # For larger games, apply a more general approach
        else:
            # Flatten the payoff matrix for each player
            for p in range(self.num_players):
                player_payoffs = modified_matrix[..., p].flatten()
                
                # Sort payoffs
                sorted_indices = np.argsort(player_payoffs)
                
                # Apply 18/82 pattern: boost top 18% of payoffs
                top_count = int(len(sorted_indices) * 0.18)
                top_indices = sorted_indices[-top_count:]
                
                # Calculate boost amount
                boost = np.mean(np.abs(player_payoffs)) * self.uuft_bias
                
                # Apply boost to top payoffs
                for idx in top_indices:
                    # Convert flat index back to multi-dimensional index
                    multi_idx = np.unravel_index(idx, modified_matrix[..., p].shape)
                    modified_matrix[multi_idx][p] += boost
        
        return modified_matrix
    
    def _generate_strategy_profiles(self):
        """Generate all possible strategy profiles for the game."""
        # For an n-player game with m strategies each, there are m^n possible strategy profiles
        total_profiles = self.num_strategies ** self.num_players
        
        # Initialize strategy profiles
        profiles = []
        
        # Generate all combinations of strategies
        for i in range(total_profiles):
            profile = []
            temp = i
            
            # Convert to base-m number
            for p in range(self.num_players):
                profile.append(temp % self.num_strategies)
                temp //= self.num_strategies
            
            profiles.append(tuple(profile))
        
        return profiles
    
    def get_payoff(self, strategy_profile):
        """
        Get the payoff for a specific strategy profile.
        
        Args:
            strategy_profile: Tuple of strategies, one for each player
            
        Returns:
            Tuple of payoffs, one for each player
        """
        if len(strategy_profile) != self.num_players:
            raise ValueError(f"Strategy profile must have {self.num_players} elements")
        
        # Extract payoffs from the matrix
        payoffs = []
        for p in range(self.num_players):
            payoffs.append(self.payoff_matrix[tuple(strategy_profile)][p])
        
        return tuple(payoffs)
    
    def find_nash_equilibria(self):
        """
        Find all pure strategy Nash equilibria in the game.
        
        Returns:
            List of equilibrium strategy profiles
        """
        # For 2-player games
        if self.num_players == 2:
            # Initialize best responses
            p1_best_response = np.zeros((self.num_strategies,), dtype=int)
            p2_best_response = np.zeros((self.num_strategies,), dtype=int)
            
            # Find best responses
            for s1 in range(self.num_strategies):
                p2_best_response[s1] = np.argmax(self.payoff_matrix[s1, :, 1])
            
            for s2 in range(self.num_strategies):
                p1_best_response[s2] = np.argmax(self.payoff_matrix[:, s2, 0])
            
            # Find Nash equilibria
            equilibria = []
            for s1 in range(self.num_strategies):
                for s2 in range(self.num_strategies):
                    if p1_best_response[s2] == s1 and p2_best_response[s1] == s2:
                        equilibria.append((s1, s2))
            
            return equilibria
        
        # For n-player games (more complex)
        else:
            # This is a simplified approach for n-player games
            equilibria = []
            
            for profile in self.strategy_profiles:
                is_equilibrium = True
                
                # Check if any player can improve by deviating
                for p in range(self.num_players):
                    current_payoff = self.payoff_matrix[profile][p]
                    
                    # Check all possible deviations for this player
                    for s in range(self.num_strategies):
                        if s == profile[p]:
                            continue
                        
                        # Create deviated profile
                        deviated_profile = list(profile)
                        deviated_profile[p] = s
                        
                        # Check if deviation improves payoff
                        deviated_payoff = self.payoff_matrix[tuple(deviated_profile)][p]
                        
                        if deviated_payoff > current_payoff:
                            is_equilibrium = False
                            break
                    
                    if not is_equilibrium:
                        break
                
                if is_equilibrium:
                    equilibria.append(profile)
            
            return equilibria
    
    def calculate_mixed_equilibrium(self):
        """
        Calculate the mixed strategy Nash equilibrium for 2x2 games.
        
        Returns:
            Tuple of mixed strategies, one for each player
        """
        if self.num_players != 2 or self.num_strategies != 2:
            logger.warning("Mixed equilibrium calculation is only implemented for 2x2 games")
            return None
        
        # Extract payoffs
        a = self.payoff_matrix[0, 0, 0]  # Player 1's payoff for (0,0)
        b = self.payoff_matrix[0, 1, 0]  # Player 1's payoff for (0,1)
        c = self.payoff_matrix[1, 0, 0]  # Player 1's payoff for (1,0)
        d = self.payoff_matrix[1, 1, 0]  # Player 1's payoff for (1,1)
        
        e = self.payoff_matrix[0, 0, 1]  # Player 2's payoff for (0,0)
        f = self.payoff_matrix[0, 1, 1]  # Player 2's payoff for (0,1)
        g = self.payoff_matrix[1, 0, 1]  # Player 2's payoff for (1,0)
        h = self.payoff_matrix[1, 1, 1]  # Player 2's payoff for (1,1)
        
        # Calculate mixed strategy probabilities
        # Player 2's probability of playing strategy 0
        try:
            p2_prob_0 = (d - b) / ((a - b) - (c - d))
        except ZeroDivisionError:
            p2_prob_0 = 0.5  # Default to uniform if division by zero
        
        # Player 1's probability of playing strategy 0
        try:
            p1_prob_0 = (h - g) / ((e - g) - (f - h))
        except ZeroDivisionError:
            p1_prob_0 = 0.5  # Default to uniform if division by zero
        
        # Clamp probabilities to [0, 1]
        p1_prob_0 = max(0, min(1, p1_prob_0))
        p2_prob_0 = max(0, min(1, p2_prob_0))
        
        return ((p1_prob_0, 1 - p1_prob_0), (p2_prob_0, 1 - p2_prob_0))
    
    def visualize_payoff_matrix(self, save_path=None):
        """
        Visualize the payoff matrix for a 2x2 game.
        
        Args:
            save_path: Path to save the visualization
        """
        if self.num_players != 2 or self.num_strategies != 2:
            logger.warning("Payoff matrix visualization is only implemented for 2x2 games")
            return
        
        plt.figure(figsize=(8, 8))
        
        # Create a 2x2 grid
        for i in range(2):
            for j in range(2):
                # Calculate position
                x = j
                y = 1 - i  # Invert y-axis to match standard game theory notation
                
                # Get payoffs
                payoffs = self.payoff_matrix[i, j]
                
                # Create rectangle
                rect = plt.Rectangle((x, y), 1, 1, fill=False, edgecolor='black')
                plt.gca().add_patch(rect)
                
                # Add payoffs
                plt.text(x + 0.5, y + 0.6, f"{payoffs[0]}", ha='center', va='center', fontsize=12)
                plt.text(x + 0.5, y + 0.4, f"{payoffs[1]}", ha='center', va='center', fontsize=12)
        
        # Add strategy labels
        strategy_names = {
            "prisoners_dilemma": ["Cooperate", "Defect"],
            "stag_hunt": ["Stag", "Hare"],
            "chicken": ["Swerve", "Straight"],
            "public_goods": ["Contribute", "Free-ride"],
            "custom": ["Strategy 0", "Strategy 1"]
        }
        
        names = strategy_names.get(self.game_type, ["Strategy 0", "Strategy 1"])
        
        # Add row labels (Player 1's strategies)
        plt.text(-0.1, 1.5, names[0], ha='center', va='center', fontsize=12, rotation=90)
        plt.text(-0.1, 0.5, names[1], ha='center', va='center', fontsize=12, rotation=90)
        
        # Add column labels (Player 2's strategies)
        plt.text(0.5, 2.1, names[0], ha='center', va='center', fontsize=12)
        plt.text(1.5, 2.1, names[1], ha='center', va='center', fontsize=12)
        
        # Add player labels
        plt.text(-0.5, 1.0, "Player 1", ha='center', va='center', fontsize=14, rotation=90)
        plt.text(1.0, 2.5, "Player 2", ha='center', va='center', fontsize=14)
        
        # Set limits and remove ticks
        plt.xlim(-1, 3)
        plt.ylim(-0.5, 3)
        plt.xticks([])
        plt.yticks([])
        
        # Add title
        plt.title(f"{self.game_type.replace('_', ' ').title()} Game", fontsize=16)
        
        # Save if path provided
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Payoff matrix visualization saved to {save_path}")
        
        plt.close()
    
    def to_dict(self):
        """Convert game scenario to dictionary for serialization."""
        return {
            "game_type": self.game_type,
            "num_players": self.num_players,
            "num_strategies": self.num_strategies,
            "uuft_bias": self.uuft_bias,
            "payoff_matrix": self.payoff_matrix.tolist()
        }
    
    @classmethod
    def from_dict(cls, data):
        """Create game scenario from dictionary."""
        game = cls(
            game_type=data["game_type"],
            num_players=data["num_players"],
            num_strategies=data["num_strategies"],
            uuft_bias=data["uuft_bias"]
        )
        
        # Replace generated payoff matrix with saved matrix
        game.payoff_matrix = np.array(data["payoff_matrix"])
        
        return game
