/**
 * Authentication Manager
 * 
 * This module handles authentication and authorization for the UAC.
 * It provides functions for user management, token generation, and access control.
 */

const jwt = require('jsonwebtoken');
const { getLogger } = require('./logger');

const logger = getLogger('auth');

// Default JWT secret (should be overridden in production)
const DEFAULT_JWT_SECRET = 'novafuse-uac-demo-secret';

// User roles
const ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  PARTNER: 'partner',
  GUEST: 'guest'
};

// Permission levels
const PERMISSIONS = {
  READ: 'read',
  WRITE: 'write',
  EXECUTE: 'execute',
  ADMIN: 'admin'
};

/**
 * Setup the Authentication Manager
 * @param {Object} options Configuration options
 * @returns {Object} The auth manager instance
 */
function setupAuthManager(options = {}) {
  // JWT configuration
  const jwtConfig = {
    secret: options.jwtSecret || process.env.JWT_SECRET || DEFAULT_JWT_SECRET,
    expiresIn: options.jwtExpiresIn || '1h'
  };
  
  // In-memory user store (for demo purposes only)
  const users = {};
  
  // In-memory token store (for demo purposes only)
  const tokens = {};
  
  // In-memory role permissions
  const rolePermissions = {
    [ROLES.ADMIN]: [PERMISSIONS.READ, PERMISSIONS.WRITE, PERMISSIONS.EXECUTE, PERMISSIONS.ADMIN],
    [ROLES.USER]: [PERMISSIONS.READ, PERMISSIONS.WRITE, PERMISSIONS.EXECUTE],
    [ROLES.PARTNER]: [PERMISSIONS.READ, PERMISSIONS.EXECUTE],
    [ROLES.GUEST]: [PERMISSIONS.READ]
  };

  /**
   * Register a new user
   * @param {String} username Username
   * @param {String} password Password (hashed in a real implementation)
   * @param {Object} userData Additional user data
   * @returns {Object} Created user
   */
  function registerUser(username, password, userData = {}) {
    logger.info(`Registering user: ${username}`);
    
    if (users[username]) {
      logger.error(`User ${username} already exists`);
      throw new Error(`User ${username} already exists`);
    }
    
    // In a real implementation, the password would be hashed
    const user = {
      username,
      password,
      role: userData.role || ROLES.USER,
      createdAt: new Date(),
      ...userData
    };
    
    users[username] = user;
    
    logger.info(`User ${username} registered successfully`);
    return { ...user, password: undefined };
  }

  /**
   * Authenticate a user
   * @param {String} username Username
   * @param {String} password Password
   * @returns {Object} Authentication result with token
   */
  function authenticateUser(username, password) {
    logger.info(`Authenticating user: ${username}`);
    
    const user = users[username];
    
    if (!user || user.password !== password) {
      logger.error(`Authentication failed for user: ${username}`);
      throw new Error('Invalid username or password');
    }
    
    // Generate JWT token
    const token = jwt.sign(
      { 
        username: user.username,
        role: user.role
      },
      jwtConfig.secret,
      { expiresIn: jwtConfig.expiresIn }
    );
    
    // Store token
    tokens[token] = {
      username,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 3600000) // 1 hour
    };
    
    logger.info(`User ${username} authenticated successfully`);
    return {
      token,
      user: { ...user, password: undefined }
    };
  }

  /**
   * Verify a JWT token
   * @param {String} token JWT token
   * @returns {Object} Decoded token payload
   */
  function verifyToken(token) {
    try {
      return jwt.verify(token, jwtConfig.secret);
    } catch (error) {
      logger.error('Token verification failed', error);
      throw error;
    }
  }

  /**
   * Check if a user has a specific permission
   * @param {String} username Username
   * @param {String} permission Permission to check
   * @returns {Boolean} Whether the user has the permission
   */
  function hasPermission(username, permission) {
    const user = users[username];
    
    if (!user) {
      return false;
    }
    
    const userPermissions = rolePermissions[user.role] || [];
    return userPermissions.includes(permission);
  }

  /**
   * Get a user by username
   * @param {String} username Username
   * @returns {Object|null} User object or null if not found
   */
  function getUser(username) {
    const user = users[username];
    
    if (!user) {
      return null;
    }
    
    return { ...user, password: undefined };
  }

  /**
   * List all users
   * @returns {Array} Array of users
   */
  function listUsers() {
    return Object.values(users).map(user => ({ ...user, password: undefined }));
  }

  /**
   * Revoke a token
   * @param {String} token JWT token
   * @returns {Boolean} Success status
   */
  function revokeToken(token) {
    if (tokens[token]) {
      delete tokens[token];
      return true;
    }
    
    return false;
  }

  // Initialize with demo users
  registerUser('admin', 'admin123', { role: ROLES.ADMIN, name: 'Admin User' });
  registerUser('partner', 'partner123', { role: ROLES.PARTNER, name: 'Partner User' });
  registerUser('user', 'user123', { role: ROLES.USER, name: 'Regular User' });
  registerUser('guest', 'guest123', { role: ROLES.GUEST, name: 'Guest User' });

  // Return the auth manager interface
  return {
    registerUser,
    authenticateUser,
    verifyToken,
    hasPermission,
    getUser,
    listUsers,
    revokeToken,
    ROLES,
    PERMISSIONS
  };
}

module.exports = {
  setupAuthManager
};
