/**
 * Regulatory Compliance Controller
 * 
 * This controller handles requests related to regulatory compliance in the Privacy Management API.
 */

const regulatoryComplianceService = require('../services/regulatoryComplianceService');
const logger = require('../config/logger');

/**
 * Get all regulatory frameworks
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllFrameworks = async (req, res) => {
  try {
    const { 
      status, 
      region, 
      category, 
      search,
      page = 1,
      limit = 20,
      sort = 'name'
    } = req.query;
    
    // Parse sort parameter
    const sortObj = {};
    if (sort) {
      const sortFields = sort.split(',');
      for (const field of sortFields) {
        if (field.startsWith('-')) {
          sortObj[field.substring(1)] = -1;
        } else {
          sortObj[field] = 1;
        }
      }
    }
    
    const filter = {
      status,
      region,
      category,
      search,
      page: parseInt(page),
      limit: parseInt(limit),
      sort: sortObj
    };
    
    const frameworks = await regulatoryComplianceService.getAllFrameworks(filter);
    
    res.status(200).json({
      success: true,
      data: frameworks.data,
      pagination: frameworks.pagination
    });
  } catch (error) {
    logger.error('Error getting regulatory frameworks:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get regulatory frameworks',
      message: error.message
    });
  }
};

/**
 * Get regulatory framework by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworkById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const framework = await regulatoryComplianceService.getFrameworkById(id);
    
    res.status(200).json({
      success: true,
      data: framework
    });
  } catch (error) {
    logger.error(`Error getting regulatory framework with ID ${req.params.id}:`, error);
    
    res.status(error.message.includes('not found') ? 404 : 500).json({
      success: false,
      error: 'Failed to get regulatory framework',
      message: error.message
    });
  }
};

/**
 * Get regulatory framework by code
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworkByCode = async (req, res) => {
  try {
    const { code } = req.params;
    
    const framework = await regulatoryComplianceService.getFrameworkByCode(code);
    
    res.status(200).json({
      success: true,
      data: framework
    });
  } catch (error) {
    logger.error(`Error getting regulatory framework with code ${req.params.code}:`, error);
    
    res.status(error.message.includes('not found') ? 404 : 500).json({
      success: false,
      error: 'Failed to get regulatory framework',
      message: error.message
    });
  }
};

/**
 * Get compliance requirements for a framework
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getRequirementsByFramework = async (req, res) => {
  try {
    const { frameworkId } = req.params;
    const { 
      status, 
      category, 
      search,
      page = 1,
      limit = 50,
      sort = 'section,number'
    } = req.query;
    
    // Parse sort parameter
    const sortObj = {};
    if (sort) {
      const sortFields = sort.split(',');
      for (const field of sortFields) {
        if (field.startsWith('-')) {
          sortObj[field.substring(1)] = -1;
        } else {
          sortObj[field] = 1;
        }
      }
    }
    
    const filter = {
      status,
      category,
      search,
      page: parseInt(page),
      limit: parseInt(limit),
      sort: sortObj
    };
    
    const requirements = await regulatoryComplianceService.getRequirementsByFramework(frameworkId, filter);
    
    res.status(200).json({
      success: true,
      data: requirements.data,
      pagination: requirements.pagination
    });
  } catch (error) {
    logger.error(`Error getting compliance requirements for framework ${req.params.frameworkId}:`, error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get compliance requirements',
      message: error.message
    });
  }
};

/**
 * Get compliance requirement by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getRequirementById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const requirement = await regulatoryComplianceService.getRequirementById(id);
    
    res.status(200).json({
      success: true,
      data: requirement
    });
  } catch (error) {
    logger.error(`Error getting compliance requirement with ID ${req.params.id}:`, error);
    
    res.status(error.message.includes('not found') ? 404 : 500).json({
      success: false,
      error: 'Failed to get compliance requirement',
      message: error.message
    });
  }
};

/**
 * Get compliance status for an entity
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getComplianceStatus = async (req, res) => {
  try {
    const { entityType, entityId, frameworkId } = req.params;
    
    const status = await regulatoryComplianceService.getComplianceStatus(entityType, entityId, frameworkId);
    
    res.status(200).json({
      success: true,
      data: status
    });
  } catch (error) {
    logger.error(`Error getting compliance status for ${req.params.entityType} ${req.params.entityId} and framework ${req.params.frameworkId}:`, error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get compliance status',
      message: error.message
    });
  }
};

/**
 * Update compliance status for a requirement
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateRequirementStatus = async (req, res) => {
  try {
    const { statusId, requirementId } = req.params;
    const updates = req.body;
    
    // Add audit information
    if (req.user) {
      updates.lastUpdatedBy = req.user.id;
      updates.lastUpdatedAt = new Date();
      
      // Add history entry
      if (!updates.history) {
        updates.history = [];
      }
      
      updates.history.push({
        status: updates.status,
        notes: updates.notes,
        updatedBy: req.user.id,
        updatedAt: new Date()
      });
    }
    
    const status = await regulatoryComplianceService.updateRequirementStatus(statusId, requirementId, updates);
    
    res.status(200).json({
      success: true,
      data: status
    });
  } catch (error) {
    logger.error(`Error updating requirement status for compliance status ${req.params.statusId} and requirement ${req.params.requirementId}:`, error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to update requirement status',
      message: error.message
    });
  }
};

/**
 * Generate compliance report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const generateComplianceReport = async (req, res) => {
  try {
    const { entityType, entityId, frameworkId } = req.params;
    
    const report = await regulatoryComplianceService.generateComplianceReport(entityType, entityId, frameworkId);
    
    res.status(200).json({
      success: true,
      data: report
    });
  } catch (error) {
    logger.error(`Error generating compliance report for ${req.params.entityType} ${req.params.entityId} and framework ${req.params.frameworkId}:`, error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to generate compliance report',
      message: error.message
    });
  }
};

/**
 * Map requirements between frameworks
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const mapRequirementsBetweenFrameworks = async (req, res) => {
  try {
    const { sourceFrameworkId, targetFrameworkId } = req.params;
    
    const mapping = await regulatoryComplianceService.mapRequirementsBetweenFrameworks(sourceFrameworkId, targetFrameworkId);
    
    res.status(200).json({
      success: true,
      data: mapping
    });
  } catch (error) {
    logger.error(`Error mapping requirements between frameworks ${req.params.sourceFrameworkId} and ${req.params.targetFrameworkId}:`, error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to map requirements between frameworks',
      message: error.message
    });
  }
};

/**
 * Get regulatory updates
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getRegulatoryUpdates = async (req, res) => {
  try {
    const { 
      region, 
      framework,
      since,
      page = 1,
      limit = 20
    } = req.query;
    
    const filter = {
      region,
      framework,
      since,
      page: parseInt(page),
      limit: parseInt(limit)
    };
    
    const updates = await regulatoryComplianceService.getRegulatoryUpdates(filter);
    
    res.status(200).json({
      success: true,
      data: updates.data,
      pagination: updates.pagination
    });
  } catch (error) {
    logger.error('Error getting regulatory updates:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get regulatory updates',
      message: error.message
    });
  }
};

module.exports = {
  getAllFrameworks,
  getFrameworkById,
  getFrameworkByCode,
  getRequirementsByFramework,
  getRequirementById,
  getComplianceStatus,
  updateRequirementStatus,
  generateComplianceReport,
  mapRequirementsBetweenFrameworks,
  getRegulatoryUpdates
};
