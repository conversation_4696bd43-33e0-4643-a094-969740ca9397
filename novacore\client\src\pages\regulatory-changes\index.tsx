/**
 * Regulatory Changes Page
 * 
 * This page displays a list of regulatory changes and allows users to manage them.
 * It leverages the existing NovaPrime components and connects them to the NovaPulse API.
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { NovaPulseAPI, RegulatoryChange } from '@/api/novaPulseApi';
import { ComplianceNavigation } from '@/components/compliance/ComplianceNavigation';
import { PreventiveActionsTable } from '@/components/regulatory/PreventiveActionsTable';
import { PreventionInfoCards } from '@/components/regulatory/PreventionInfoCards';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Plus, Filter, Download, AlertTriangle, Clock, CheckCircle } from 'lucide-react';

export default function RegulatoryChangesPage() {
  const router = useRouter();
  const [regulatoryChanges, setRegulatoryChanges] = useState<RegulatoryChange[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [organizationId, setOrganizationId] = useState('');
  const [filterPriority, setFilterPriority] = useState('');
  const [filterCategory, setFilterCategory] = useState('');

  const api = new NovaPulseAPI();

  useEffect(() => {
    // Get organization ID from local storage or context
    const orgId = localStorage.getItem('organizationId') || '';
    setOrganizationId(orgId);

    if (orgId) {
      loadRegulatoryChanges(orgId);
    }
  }, []);

  const loadRegulatoryChanges = async (orgId: string) => {
    setLoading(true);
    try {
      const response = await api.getRegulatoryChanges(orgId);
      setRegulatoryChanges(response.data);
    } catch (error) {
      console.error('Error loading regulatory changes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleCreateRegulatoryChange = () => {
    router.push('/regulatory-changes/create');
  };

  const handleRegulatoryChangeClick = (id: string) => {
    router.push(`/regulatory-changes/${id}`);
  };

  const filteredRegulatoryChanges = regulatoryChanges.filter(change => {
    // Apply search filter
    const matchesSearch = 
      change.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      change.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      change.regulationName.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Apply priority filter
    const matchesPriority = !filterPriority || change.priority === filterPriority;
    
    // Apply category filter
    const matchesCategory = !filterCategory || change.category === filterCategory;
    
    return matchesSearch && matchesPriority && matchesCategory;
  });

  // Get unique priorities and categories for filters
  const priorities = [...new Set(regulatoryChanges.map(r => r.priority))];
  const categories = [...new Set(regulatoryChanges.map(r => r.category))];

  // Filter regulatory changes based on active tab
  const tabFilteredChanges = filteredRegulatoryChanges.filter(change => {
    if (activeTab === 'all') return true;
    if (activeTab === 'pending') return change.status === 'pending';
    if (activeTab === 'overdue') {
      return new Date(change.complianceDeadline) < new Date() && 
             change.implementationStatus?.status !== 'completed';
    }
    if (activeTab === 'upcoming') {
      return new Date(change.effectiveDate) > new Date() && 
             change.status === 'upcoming';
    }
    if (activeTab === 'completed') {
      return change.implementationStatus?.status === 'completed';
    }
    return true;
  });

  // Convert regulatory changes to the format expected by PreventiveActionsTable
  const preventiveActions = tabFilteredChanges.map(change => ({
    id: change._id,
    title: change.title,
    description: change.description,
    regulation: change.regulationName,
    deadline: change.complianceDeadline,
    status: change.implementationStatus?.status || 'pending',
    priority: change.priority,
    impact: change.impactAssessment?.level || 'medium',
    progress: change.implementationStatus?.progress || 0,
    actions: change.impactAssessment?.requiredActions?.map(action => ({
      type: action.type,
      description: action.description,
      priority: action.priority,
      dueDate: action.dueDate
    })) || []
  }));

  // Prepare data for the prevention info cards
  const preventionInfo = {
    pending: tabFilteredChanges.filter(c => c.status === 'pending').length,
    overdue: tabFilteredChanges.filter(c => 
      new Date(c.complianceDeadline) < new Date() && 
      c.implementationStatus?.status !== 'completed'
    ).length,
    upcoming: tabFilteredChanges.filter(c => 
      new Date(c.effectiveDate) > new Date() && 
      c.status === 'upcoming'
    ).length,
    completed: tabFilteredChanges.filter(c => 
      c.implementationStatus?.status === 'completed'
    ).length
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Regulatory Changes</h1>
      
      <ComplianceNavigation />
      
      <div className="mb-6">
        <PreventionInfoCards
          pending={preventionInfo.pending}
          overdue={preventionInfo.overdue}
          upcoming={preventionInfo.upcoming}
          completed={preventionInfo.completed}
        />
      </div>
      
      <div className="mb-6 flex flex-col md:flex-row justify-between gap-4">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <input
            type="text"
            placeholder="Search regulatory changes..."
            className="pl-9 pr-4 py-2 border rounded-md w-full md:w-64"
            value={searchQuery}
            onChange={handleSearch}
          />
        </div>
        
        <div className="flex gap-2">
          <select
            className="border rounded-md px-3 py-2 text-sm"
            value={filterPriority}
            onChange={(e) => setFilterPriority(e.target.value)}
          >
            <option value="">All Priorities</option>
            {priorities.map(priority => (
              <option key={priority} value={priority}>{priority}</option>
            ))}
          </select>
          
          <select
            className="border rounded-md px-3 py-2 text-sm"
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
          >
            <option value="">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
          
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm" onClick={handleCreateRegulatoryChange}>
            <Plus className="h-4 w-4 mr-2" />
            New Change
          </Button>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All Changes</TabsTrigger>
          <TabsTrigger value="pending">
            <Clock className="h-4 w-4 mr-2" />
            Pending
          </TabsTrigger>
          <TabsTrigger value="overdue">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Overdue
          </TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
          <TabsTrigger value="completed">
            <CheckCircle className="h-4 w-4 mr-2" />
            Completed
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value={activeTab} className="space-y-4">
          {loading ? (
            <div className="text-center py-8">Loading regulatory changes...</div>
          ) : preventiveActions.length === 0 ? (
            <div className="text-center py-8">
              No regulatory changes found. {searchQuery && 'Try a different search term.'}
            </div>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Regulatory Changes</CardTitle>
              </CardHeader>
              <CardContent>
                <PreventiveActionsTable 
                  actions={preventiveActions} 
                  onActionClick={handleRegulatoryChangeClick}
                />
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
