#!/usr/bin/env python3
"""
π Power Optimization Test

This script tests whether π10³ is uniquely optimal by comparing the performance
of the UUFT equation with different powers of π across multiple domains.
"""

import numpy as np
import matplotlib.pyplot as plt
import math
import os
from datetime import datetime

# Create results directory
RESULTS_DIR = "uuft_test_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

# Constants
PI = math.pi
GOLDEN_RATIO = (1 + math.sqrt(5)) / 2  # φ ≈ 1.618

# Test different powers of π
def test_pi_powers(powers_range=range(-2, 6)):
    """
    Test the UUFT equation with different powers of π.

    Args:
        powers_range: Range of powers to test (default: -2 to 5)

    Returns:
        dict: Results for each power and domain
    """
    print(f"Testing π powers from 10^{min(powers_range)} to 10^{max(powers_range)}")

    # Define test domains with sample data
    domains = {
        "cybersecurity": {
            "A": 10,    # NIST multiplier
            "B": 10,    # GCP multiplier
            "C": 31.42, # Cyber-Safety multiplier
            "expected": 3142.59  # Expected result with π10³
        },
        "medical": {
            "A": 0.25,  # Genomic data weight
            "B": 0.40,  # Proteomic data weight
            "C": 0.35,  # Clinical data weight
            "expected": 3142.59  # Expected result with π10³
        },
        "financial": {
            "A": 0.33,  # Market data weight
            "B": 0.33,  # Economic data weight
            "C": 0.34,  # Sentiment data weight
            "expected": 3142.59  # Expected result with π10³
        },
        "physics": {
            "A": 0.40,  # Strong force weight
            "B": 0.35,  # Electromagnetic force weight
            "C": 0.25,  # Weak force weight
            "expected": 3142.59  # Expected result with π10³
        }
    }

    results = {}

    # Test each power
    for power in powers_range:
        pi_factor = PI * (10 ** power)
        print(f"Testing π × 10^{power} = {pi_factor:.6f}")

        domain_results = {}

        # Test each domain
        for domain_name, domain_data in domains.items():
            # Apply UUFT equation: (A ⊗ B ⊕ C) × π10^power
            A = domain_data["A"]
            B = domain_data["B"]
            C = domain_data["C"]

            # Tensor product (⊗) with golden ratio
            tensor_product = A * B * GOLDEN_RATIO

            # Fusion operator (⊕) with inverse golden ratio
            fusion_result = tensor_product + (C * (1 / GOLDEN_RATIO))

            # Apply pi factor
            result = fusion_result * pi_factor

            # Calculate accuracy (how close to expected result with π10³)
            expected = domain_data["expected"]
            accuracy = 1.0 - abs(result - expected) / expected if expected != 0 else 0.0

            domain_results[domain_name] = {
                "inputs": {"A": A, "B": B, "C": C},
                "tensor_product": tensor_product,
                "fusion_result": fusion_result,
                "final_result": result,
                "expected_result": expected,
                "accuracy": accuracy
            }

        # Calculate average accuracy across domains
        avg_accuracy = np.mean([d["accuracy"] for d in domain_results.values()])

        results[f"10^{power}"] = {
            "pi_factor": pi_factor,
            "domain_results": domain_results,
            "average_accuracy": avg_accuracy
        }

        print(f"  Average accuracy: {avg_accuracy:.4f}")

    return results

def plot_results(results):
    """
    Plot the results of the π power optimization test.

    Args:
        results: Results from test_pi_powers()
    """
    powers = [int(k.split('^')[1]) for k in results.keys()]
    accuracies = [v["average_accuracy"] for v in results.values()]

    plt.figure(figsize=(10, 6))
    plt.plot(powers, accuracies, 'o-', linewidth=2)
    plt.axvline(x=3, color='r', linestyle='--', label='π10³')
    plt.xlabel('Power of 10')
    plt.ylabel('Average Accuracy')
    plt.title('π Power Optimization Test: Average Accuracy vs Power of 10')
    plt.grid(True)
    plt.legend()

    # Save the plot
    plt.savefig(os.path.join(RESULTS_DIR, "pi_power_optimization.png"))
    plt.close()

    # Create a more detailed plot showing each domain
    plt.figure(figsize=(12, 8))

    for domain in next(iter(results.values()))["domain_results"].keys():
        domain_accuracies = [v["domain_results"][domain]["accuracy"] for v in results.values()]
        plt.plot(powers, domain_accuracies, 'o-', label=domain.capitalize())

    plt.axvline(x=3, color='r', linestyle='--', label='π10³')
    plt.xlabel('Power of 10')
    plt.ylabel('Accuracy')
    plt.title('π Power Optimization Test: Domain-Specific Accuracy vs Power of 10')
    plt.grid(True)
    plt.legend()

    # Save the detailed plot
    plt.savefig(os.path.join(RESULTS_DIR, "pi_power_optimization_by_domain.png"))
    plt.close()

def save_results(results):
    """
    Save the results to a text file.

    Args:
        results: Results from test_pi_powers()
    """
    try:
        output_path = os.path.join(RESULTS_DIR, "pi_power_optimization_results.txt")
        print(f"Saving results to {output_path}")

        with open(output_path, "w", encoding="utf-8") as f:
            f.write("Pi Power Optimization Test Results\n")
            f.write("=================================\n")
            f.write(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # Write summary
            f.write("Summary:\n")
            for power, data in results.items():
                f.write(f"  Pi*{power} = {data['pi_factor']:.6f}, Average Accuracy: {data['average_accuracy']:.4f}\n")

            f.write("\n")

            # Write detailed results
            f.write("Detailed Results by Domain:\n")
            for power, data in results.items():
                f.write(f"\nPower: {power}\n")
                f.write(f"  Pi Factor: {data['pi_factor']:.6f}\n")

                for domain, domain_data in data["domain_results"].items():
                    f.write(f"  {domain.capitalize()}:\n")
                    f.write(f"    Inputs: A={domain_data['inputs']['A']}, B={domain_data['inputs']['B']}, C={domain_data['inputs']['C']}\n")
                    f.write(f"    Tensor Product: {domain_data['tensor_product']:.6f}\n")
                    f.write(f"    Fusion Result: {domain_data['fusion_result']:.6f}\n")
                    f.write(f"    Final Result: {domain_data['final_result']:.6f}\n")
                    f.write(f"    Expected Result: {domain_data['expected_result']:.6f}\n")
                    f.write(f"    Accuracy: {domain_data['accuracy']:.4f}\n")

        # Verify the file was written
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            print(f"Successfully wrote results to {output_path}")
        else:
            print(f"Warning: File {output_path} was not created or is empty")

    except Exception as e:
        print(f"Error saving results: {e}")
        # Try an alternative approach
        try:
            print("Trying alternative approach to save results...")
            with open(os.path.join(RESULTS_DIR, "pi_power_optimization_results_alt.txt"), "w", encoding="ascii", errors="replace") as f:
                f.write("Pi Power Optimization Test Results (Alternative Save)\n")
                f.write("=================================================\n")
                f.write(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                # Write simplified summary
                f.write("Summary:\n")
                for power, data in results.items():
                    f.write(f"  Power: {power}, Accuracy: {data['average_accuracy']:.4f}\n")
        except Exception as e2:
            print(f"Alternative save also failed: {e2}")

def main():
    """Run the π power optimization test."""
    print("Running π Power Optimization Test")
    print("================================")

    try:
        # Test powers from 10^-2 to 10^5
        results = test_pi_powers(range(-2, 6))

        # Plot the results
        plot_results(results)

        # Save the results
        save_results(results)

        print("\nTest complete. Results saved to the uuft_test_results directory.")

        # Find the optimal power
        optimal_power = max(results.items(), key=lambda x: x[1]["average_accuracy"])[0]
        optimal_accuracy = results[optimal_power]["average_accuracy"]

        print(f"\nOptimal power: {optimal_power}")
        print(f"Optimal accuracy: {optimal_accuracy:.4f}")

        # Check if π10³ is optimal
        is_pi_10_3_optimal = optimal_power == "10^3"
        print(f"\nIs π10³ optimal? {'Yes' if is_pi_10_3_optimal else 'No'}")

        return results

    except Exception as e:
        print(f"\nError in π Power Optimization Test: {e}")

        # Create a minimal result to avoid breaking the runner script
        minimal_results = {
            "10^3": {
                "pi_factor": 3.14159 * 10**3,
                "average_accuracy": 0.0,
                "domain_results": {
                    "cybersecurity": {"accuracy": 0.0},
                    "medical": {"accuracy": 0.0},
                    "financial": {"accuracy": 0.0},
                    "physics": {"accuracy": 0.0}
                }
            }
        }

        print("Returning minimal results due to error")
        return minimal_results

if __name__ == "__main__":
    main()
