{"version": 3, "names": ["request", "require", "mongoose", "MongoMemoryServer", "app", "ConnectorRegistry", "FeatureFlagService", "EncryptionService", "mongoServer", "server", "connectorRegistry", "featureFlagService", "encryptionService", "authToken", "testUser", "email", "password", "name", "testConnector", "type", "description", "config", "baseUrl", "authType", "headers", "testTransformation", "rules", "source", "target", "transform", "testWorkflow", "steps", "connectorId", "operation", "path", "transformationId", "beforeAll", "create", "mongo<PERSON>ri", "get<PERSON><PERSON>", "connect", "useNewUrlParser", "useUnifiedTopology", "listen", "afterAll", "close", "disconnect", "stop", "authenticate", "post", "send", "response", "body", "token", "describe", "test", "set", "expect", "status", "toBe", "toHaveProperty", "id", "get", "updatedConnector", "put", "Array", "isArray", "length", "toBeGreaterThan", "updatedTransformation", "workflowId", "updatedWorkflow", "jest", "spyOn", "mockResolvedValue", "data", "attributes", "result", "flags", "testData", "secret", "encryptResponse", "decryptResponse", "encryptedData", "toEqual"], "sources": ["uac-e2e.test.js"], "sourcesContent": ["/**\n * NovaConnect UAC End-to-End Tests\n * \n * This test suite validates the end-to-end functionality of the Universal API Connector.\n */\n\nconst request = require('supertest');\nconst mongoose = require('mongoose');\nconst { MongoMemoryServer } = require('mongodb-memory-server');\nconst app = require('../../app');\nconst { ConnectorRegistry } = require('../../src/registry/connector-registry');\nconst { FeatureFlagService } = require('../../src/services/feature-flag-service');\nconst { EncryptionService } = require('../../src/security/encryption-service');\n\nlet mongoServer;\nlet server;\nlet connectorRegistry;\nlet featureFlagService;\nlet encryptionService;\nlet authToken;\n\n// Test data\nconst testUser = {\n  email: '<EMAIL>',\n  password: 'Test@123456',\n  name: 'Test User'\n};\n\nconst testConnector = {\n  name: 'Test Connector',\n  type: 'REST',\n  description: 'Test connector for E2E tests',\n  config: {\n    baseUrl: 'https://api.example.com',\n    authType: 'oauth2',\n    headers: {\n      'Content-Type': 'application/json'\n    }\n  }\n};\n\nconst testTransformation = {\n  name: 'Test Transformation',\n  description: 'Test transformation for E2E tests',\n  rules: [\n    {\n      source: 'data.id',\n      target: 'id',\n      transform: 'toString'\n    },\n    {\n      source: 'data.attributes.name',\n      target: 'name',\n      transform: 'uppercase'\n    }\n  ]\n};\n\nconst testWorkflow = {\n  name: 'Test Workflow',\n  description: 'Test workflow for E2E tests',\n  steps: [\n    {\n      type: 'connector',\n      connectorId: null, // Will be set during test\n      operation: 'GET',\n      path: '/users'\n    },\n    {\n      type: 'transformation',\n      transformationId: null, // Will be set during test\n    }\n  ]\n};\n\n// Setup and teardown\nbeforeAll(async () => {\n  // Start MongoDB memory server\n  mongoServer = await MongoMemoryServer.create();\n  const mongoUri = mongoServer.getUri();\n  \n  // Connect to in-memory database\n  await mongoose.connect(mongoUri, {\n    useNewUrlParser: true,\n    useUnifiedTopology: true\n  });\n  \n  // Initialize services\n  connectorRegistry = new ConnectorRegistry();\n  featureFlagService = new FeatureFlagService();\n  encryptionService = new EncryptionService();\n  \n  // Start server\n  server = app.listen(0);\n});\n\nafterAll(async () => {\n  // Stop server and close database connection\n  server.close();\n  await mongoose.disconnect();\n  await mongoServer.stop();\n});\n\n// Helper function to authenticate\nasync function authenticate() {\n  // Register user\n  await request(app)\n    .post('/api/auth/register')\n    .send(testUser);\n  \n  // Login\n  const response = await request(app)\n    .post('/api/auth/login')\n    .send({\n      email: testUser.email,\n      password: testUser.password\n    });\n  \n  return response.body.token;\n}\n\ndescribe('NovaConnect UAC End-to-End Tests', () => {\n  beforeAll(async () => {\n    // Authenticate and get token\n    authToken = await authenticate();\n  });\n  \n  describe('Connector Management', () => {\n    let connectorId;\n    \n    test('Should create a new connector', async () => {\n      const response = await request(app)\n        .post('/api/connectors')\n        .set('Authorization', `Bearer ${authToken}`)\n        .send(testConnector);\n      \n      expect(response.status).toBe(201);\n      expect(response.body).toHaveProperty('id');\n      expect(response.body.name).toBe(testConnector.name);\n      \n      connectorId = response.body.id;\n      testWorkflow.steps[0].connectorId = connectorId;\n    });\n    \n    test('Should get connector by ID', async () => {\n      const response = await request(app)\n        .get(`/api/connectors/${connectorId}`)\n        .set('Authorization', `Bearer ${authToken}`);\n      \n      expect(response.status).toBe(200);\n      expect(response.body.id).toBe(connectorId);\n      expect(response.body.name).toBe(testConnector.name);\n    });\n    \n    test('Should update connector', async () => {\n      const updatedConnector = {\n        ...testConnector,\n        name: 'Updated Connector'\n      };\n      \n      const response = await request(app)\n        .put(`/api/connectors/${connectorId}`)\n        .set('Authorization', `Bearer ${authToken}`)\n        .send(updatedConnector);\n      \n      expect(response.status).toBe(200);\n      expect(response.body.name).toBe(updatedConnector.name);\n    });\n    \n    test('Should list all connectors', async () => {\n      const response = await request(app)\n        .get('/api/connectors')\n        .set('Authorization', `Bearer ${authToken}`);\n      \n      expect(response.status).toBe(200);\n      expect(Array.isArray(response.body)).toBe(true);\n      expect(response.body.length).toBeGreaterThan(0);\n    });\n  });\n  \n  describe('Transformation Management', () => {\n    let transformationId;\n    \n    test('Should create a new transformation', async () => {\n      const response = await request(app)\n        .post('/api/transformations')\n        .set('Authorization', `Bearer ${authToken}`)\n        .send(testTransformation);\n      \n      expect(response.status).toBe(201);\n      expect(response.body).toHaveProperty('id');\n      expect(response.body.name).toBe(testTransformation.name);\n      \n      transformationId = response.body.id;\n      testWorkflow.steps[1].transformationId = transformationId;\n    });\n    \n    test('Should get transformation by ID', async () => {\n      const response = await request(app)\n        .get(`/api/transformations/${transformationId}`)\n        .set('Authorization', `Bearer ${authToken}`);\n      \n      expect(response.status).toBe(200);\n      expect(response.body.id).toBe(transformationId);\n      expect(response.body.name).toBe(testTransformation.name);\n    });\n    \n    test('Should update transformation', async () => {\n      const updatedTransformation = {\n        ...testTransformation,\n        name: 'Updated Transformation'\n      };\n      \n      const response = await request(app)\n        .put(`/api/transformations/${transformationId}`)\n        .set('Authorization', `Bearer ${authToken}`)\n        .send(updatedTransformation);\n      \n      expect(response.status).toBe(200);\n      expect(response.body.name).toBe(updatedTransformation.name);\n    });\n    \n    test('Should list all transformations', async () => {\n      const response = await request(app)\n        .get('/api/transformations')\n        .set('Authorization', `Bearer ${authToken}`);\n      \n      expect(response.status).toBe(200);\n      expect(Array.isArray(response.body)).toBe(true);\n      expect(response.body.length).toBeGreaterThan(0);\n    });\n  });\n  \n  describe('Workflow Management', () => {\n    let workflowId;\n    \n    test('Should create a new workflow', async () => {\n      const response = await request(app)\n        .post('/api/workflows')\n        .set('Authorization', `Bearer ${authToken}`)\n        .send(testWorkflow);\n      \n      expect(response.status).toBe(201);\n      expect(response.body).toHaveProperty('id');\n      expect(response.body.name).toBe(testWorkflow.name);\n      \n      workflowId = response.body.id;\n    });\n    \n    test('Should get workflow by ID', async () => {\n      const response = await request(app)\n        .get(`/api/workflows/${workflowId}`)\n        .set('Authorization', `Bearer ${authToken}`);\n      \n      expect(response.status).toBe(200);\n      expect(response.body.id).toBe(workflowId);\n      expect(response.body.name).toBe(testWorkflow.name);\n    });\n    \n    test('Should update workflow', async () => {\n      const updatedWorkflow = {\n        ...testWorkflow,\n        name: 'Updated Workflow'\n      };\n      \n      const response = await request(app)\n        .put(`/api/workflows/${workflowId}`)\n        .set('Authorization', `Bearer ${authToken}`)\n        .send(updatedWorkflow);\n      \n      expect(response.status).toBe(200);\n      expect(response.body.name).toBe(updatedWorkflow.name);\n    });\n    \n    test('Should list all workflows', async () => {\n      const response = await request(app)\n        .get('/api/workflows')\n        .set('Authorization', `Bearer ${authToken}`);\n      \n      expect(response.status).toBe(200);\n      expect(Array.isArray(response.body)).toBe(true);\n      expect(response.body.length).toBeGreaterThan(0);\n    });\n    \n    test('Should execute workflow', async () => {\n      // Mock the connector execution\n      jest.spyOn(connectorRegistry, 'executeConnector').mockResolvedValue({\n        data: {\n          id: 123,\n          attributes: {\n            name: 'test user'\n          }\n        }\n      });\n      \n      const response = await request(app)\n        .post(`/api/workflows/${workflowId}/execute`)\n        .set('Authorization', `Bearer ${authToken}`)\n        .send({});\n      \n      expect(response.status).toBe(200);\n      expect(response.body).toHaveProperty('result');\n      expect(response.body.result).toHaveProperty('id');\n      expect(response.body.result).toHaveProperty('name');\n      expect(response.body.result.name).toBe('TEST USER'); // Uppercase transformation\n    });\n  });\n  \n  describe('Feature Flag Management', () => {\n    test('Should get feature flags', async () => {\n      const response = await request(app)\n        .get('/api/feature-flags')\n        .set('Authorization', `Bearer ${authToken}`);\n      \n      expect(response.status).toBe(200);\n      expect(response.body).toHaveProperty('flags');\n      expect(typeof response.body.flags).toBe('object');\n    });\n    \n    test('Should update feature flags', async () => {\n      const flags = {\n        'premium.transformations': true,\n        'premium.workflows': true\n      };\n      \n      const response = await request(app)\n        .put('/api/feature-flags')\n        .set('Authorization', `Bearer ${authToken}`)\n        .send({ flags });\n      \n      expect(response.status).toBe(200);\n      expect(response.body.flags['premium.transformations']).toBe(true);\n      expect(response.body.flags['premium.workflows']).toBe(true);\n    });\n  });\n  \n  describe('Security', () => {\n    test('Should encrypt and decrypt data', async () => {\n      const testData = { secret: 'test-secret' };\n      \n      // Encrypt\n      const encryptResponse = await request(app)\n        .post('/api/security/encrypt')\n        .set('Authorization', `Bearer ${authToken}`)\n        .send({ data: testData });\n      \n      expect(encryptResponse.status).toBe(200);\n      expect(encryptResponse.body).toHaveProperty('encryptedData');\n      \n      // Decrypt\n      const decryptResponse = await request(app)\n        .post('/api/security/decrypt')\n        .set('Authorization', `Bearer ${authToken}`)\n        .send({ encryptedData: encryptResponse.body.encryptedData });\n      \n      expect(decryptResponse.status).toBe(200);\n      expect(decryptResponse.body).toHaveProperty('data');\n      expect(decryptResponse.body.data).toEqual(testData);\n    });\n  });\n  \n  describe('Audit Logging', () => {\n    test('Should retrieve audit logs', async () => {\n      const response = await request(app)\n        .get('/api/audit-logs')\n        .set('Authorization', `Bearer ${authToken}`);\n      \n      expect(response.status).toBe(200);\n      expect(Array.isArray(response.body)).toBe(true);\n    });\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,OAAO,GAAGC,OAAO,CAAC,WAAW,CAAC;AACpC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;AACpC,MAAM;EAAEE;AAAkB,CAAC,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AAC9D,MAAMG,GAAG,GAAGH,OAAO,CAAC,WAAW,CAAC;AAChC,MAAM;EAAEI;AAAkB,CAAC,GAAGJ,OAAO,CAAC,uCAAuC,CAAC;AAC9E,MAAM;EAAEK;AAAmB,CAAC,GAAGL,OAAO,CAAC,yCAAyC,CAAC;AACjF,MAAM;EAAEM;AAAkB,CAAC,GAAGN,OAAO,CAAC,uCAAuC,CAAC;AAE9E,IAAIO,WAAW;AACf,IAAIC,MAAM;AACV,IAAIC,iBAAiB;AACrB,IAAIC,kBAAkB;AACtB,IAAIC,iBAAiB;AACrB,IAAIC,SAAS;;AAEb;AACA,MAAMC,QAAQ,GAAG;EACfC,KAAK,EAAE,kBAAkB;EACzBC,QAAQ,EAAE,aAAa;EACvBC,IAAI,EAAE;AACR,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBD,IAAI,EAAE,gBAAgB;EACtBE,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,8BAA8B;EAC3CC,MAAM,EAAE;IACNC,OAAO,EAAE,yBAAyB;IAClCC,QAAQ,EAAE,QAAQ;IAClBC,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF;AACF,CAAC;AAED,MAAMC,kBAAkB,GAAG;EACzBR,IAAI,EAAE,qBAAqB;EAC3BG,WAAW,EAAE,mCAAmC;EAChDM,KAAK,EAAE,CACL;IACEC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE;EACb,CAAC,EACD;IACEF,MAAM,EAAE,sBAAsB;IAC9BC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE;EACb,CAAC;AAEL,CAAC;AAED,MAAMC,YAAY,GAAG;EACnBb,IAAI,EAAE,eAAe;EACrBG,WAAW,EAAE,6BAA6B;EAC1CW,KAAK,EAAE,CACL;IACEZ,IAAI,EAAE,WAAW;IACjBa,WAAW,EAAE,IAAI;IAAE;IACnBC,SAAS,EAAE,KAAK;IAChBC,IAAI,EAAE;EACR,CAAC,EACD;IACEf,IAAI,EAAE,gBAAgB;IACtBgB,gBAAgB,EAAE,IAAI,CAAE;EAC1B,CAAC;AAEL,CAAC;;AAED;AACAC,SAAS,CAAC,YAAY;EACpB;EACA5B,WAAW,GAAG,MAAML,iBAAiB,CAACkC,MAAM,CAAC,CAAC;EAC9C,MAAMC,QAAQ,GAAG9B,WAAW,CAAC+B,MAAM,CAAC,CAAC;;EAErC;EACA,MAAMrC,QAAQ,CAACsC,OAAO,CAACF,QAAQ,EAAE;IAC/BG,eAAe,EAAE,IAAI;IACrBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACAhC,iBAAiB,GAAG,IAAIL,iBAAiB,CAAC,CAAC;EAC3CM,kBAAkB,GAAG,IAAIL,kBAAkB,CAAC,CAAC;EAC7CM,iBAAiB,GAAG,IAAIL,iBAAiB,CAAC,CAAC;;EAE3C;EACAE,MAAM,GAAGL,GAAG,CAACuC,MAAM,CAAC,CAAC,CAAC;AACxB,CAAC,CAAC;AAEFC,QAAQ,CAAC,YAAY;EACnB;EACAnC,MAAM,CAACoC,KAAK,CAAC,CAAC;EACd,MAAM3C,QAAQ,CAAC4C,UAAU,CAAC,CAAC;EAC3B,MAAMtC,WAAW,CAACuC,IAAI,CAAC,CAAC;AAC1B,CAAC,CAAC;;AAEF;AACA,eAAeC,YAAYA,CAAA,EAAG;EAC5B;EACA,MAAMhD,OAAO,CAACI,GAAG,CAAC,CACf6C,IAAI,CAAC,oBAAoB,CAAC,CAC1BC,IAAI,CAACpC,QAAQ,CAAC;;EAEjB;EACA,MAAMqC,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC6C,IAAI,CAAC,iBAAiB,CAAC,CACvBC,IAAI,CAAC;IACJnC,KAAK,EAAED,QAAQ,CAACC,KAAK;IACrBC,QAAQ,EAAEF,QAAQ,CAACE;EACrB,CAAC,CAAC;EAEJ,OAAOmC,QAAQ,CAACC,IAAI,CAACC,KAAK;AAC5B;AAEAC,QAAQ,CAAC,kCAAkC,EAAE,MAAM;EACjDlB,SAAS,CAAC,YAAY;IACpB;IACAvB,SAAS,GAAG,MAAMmC,YAAY,CAAC,CAAC;EAClC,CAAC,CAAC;EAEFM,QAAQ,CAAC,sBAAsB,EAAE,MAAM;IACrC,IAAItB,WAAW;IAEfuB,IAAI,CAAC,+BAA+B,EAAE,YAAY;MAChD,MAAMJ,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC6C,IAAI,CAAC,iBAAiB,CAAC,CACvBO,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC,CAC3CqC,IAAI,CAAChC,aAAa,CAAC;MAEtBuC,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAAC,CAACQ,cAAc,CAAC,IAAI,CAAC;MAC1CH,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACnC,IAAI,CAAC,CAAC0C,IAAI,CAACzC,aAAa,CAACD,IAAI,CAAC;MAEnDe,WAAW,GAAGmB,QAAQ,CAACC,IAAI,CAACS,EAAE;MAC9B/B,YAAY,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,GAAGA,WAAW;IACjD,CAAC,CAAC;IAEFuB,IAAI,CAAC,4BAA4B,EAAE,YAAY;MAC7C,MAAMJ,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC0D,GAAG,CAAC,mBAAmB9B,WAAW,EAAE,CAAC,CACrCwB,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC;MAE9C4C,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACS,EAAE,CAAC,CAACF,IAAI,CAAC3B,WAAW,CAAC;MAC1CyB,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACnC,IAAI,CAAC,CAAC0C,IAAI,CAACzC,aAAa,CAACD,IAAI,CAAC;IACrD,CAAC,CAAC;IAEFsC,IAAI,CAAC,yBAAyB,EAAE,YAAY;MAC1C,MAAMQ,gBAAgB,GAAG;QACvB,GAAG7C,aAAa;QAChBD,IAAI,EAAE;MACR,CAAC;MAED,MAAMkC,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC4D,GAAG,CAAC,mBAAmBhC,WAAW,EAAE,CAAC,CACrCwB,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC,CAC3CqC,IAAI,CAACa,gBAAgB,CAAC;MAEzBN,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACnC,IAAI,CAAC,CAAC0C,IAAI,CAACI,gBAAgB,CAAC9C,IAAI,CAAC;IACxD,CAAC,CAAC;IAEFsC,IAAI,CAAC,4BAA4B,EAAE,YAAY;MAC7C,MAAMJ,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC0D,GAAG,CAAC,iBAAiB,CAAC,CACtBN,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC;MAE9C4C,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACQ,KAAK,CAACC,OAAO,CAACf,QAAQ,CAACC,IAAI,CAAC,CAAC,CAACO,IAAI,CAAC,IAAI,CAAC;MAC/CF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACe,MAAM,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;IACjD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFd,QAAQ,CAAC,2BAA2B,EAAE,MAAM;IAC1C,IAAInB,gBAAgB;IAEpBoB,IAAI,CAAC,oCAAoC,EAAE,YAAY;MACrD,MAAMJ,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC6C,IAAI,CAAC,sBAAsB,CAAC,CAC5BO,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC,CAC3CqC,IAAI,CAACzB,kBAAkB,CAAC;MAE3BgC,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAAC,CAACQ,cAAc,CAAC,IAAI,CAAC;MAC1CH,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACnC,IAAI,CAAC,CAAC0C,IAAI,CAAClC,kBAAkB,CAACR,IAAI,CAAC;MAExDkB,gBAAgB,GAAGgB,QAAQ,CAACC,IAAI,CAACS,EAAE;MACnC/B,YAAY,CAACC,KAAK,CAAC,CAAC,CAAC,CAACI,gBAAgB,GAAGA,gBAAgB;IAC3D,CAAC,CAAC;IAEFoB,IAAI,CAAC,iCAAiC,EAAE,YAAY;MAClD,MAAMJ,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC0D,GAAG,CAAC,wBAAwB3B,gBAAgB,EAAE,CAAC,CAC/CqB,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC;MAE9C4C,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACS,EAAE,CAAC,CAACF,IAAI,CAACxB,gBAAgB,CAAC;MAC/CsB,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACnC,IAAI,CAAC,CAAC0C,IAAI,CAAClC,kBAAkB,CAACR,IAAI,CAAC;IAC1D,CAAC,CAAC;IAEFsC,IAAI,CAAC,8BAA8B,EAAE,YAAY;MAC/C,MAAMc,qBAAqB,GAAG;QAC5B,GAAG5C,kBAAkB;QACrBR,IAAI,EAAE;MACR,CAAC;MAED,MAAMkC,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC4D,GAAG,CAAC,wBAAwB7B,gBAAgB,EAAE,CAAC,CAC/CqB,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC,CAC3CqC,IAAI,CAACmB,qBAAqB,CAAC;MAE9BZ,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACnC,IAAI,CAAC,CAAC0C,IAAI,CAACU,qBAAqB,CAACpD,IAAI,CAAC;IAC7D,CAAC,CAAC;IAEFsC,IAAI,CAAC,iCAAiC,EAAE,YAAY;MAClD,MAAMJ,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC0D,GAAG,CAAC,sBAAsB,CAAC,CAC3BN,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC;MAE9C4C,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACQ,KAAK,CAACC,OAAO,CAACf,QAAQ,CAACC,IAAI,CAAC,CAAC,CAACO,IAAI,CAAC,IAAI,CAAC;MAC/CF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACe,MAAM,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;IACjD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFd,QAAQ,CAAC,qBAAqB,EAAE,MAAM;IACpC,IAAIgB,UAAU;IAEdf,IAAI,CAAC,8BAA8B,EAAE,YAAY;MAC/C,MAAMJ,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC6C,IAAI,CAAC,gBAAgB,CAAC,CACtBO,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC,CAC3CqC,IAAI,CAACpB,YAAY,CAAC;MAErB2B,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAAC,CAACQ,cAAc,CAAC,IAAI,CAAC;MAC1CH,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACnC,IAAI,CAAC,CAAC0C,IAAI,CAAC7B,YAAY,CAACb,IAAI,CAAC;MAElDqD,UAAU,GAAGnB,QAAQ,CAACC,IAAI,CAACS,EAAE;IAC/B,CAAC,CAAC;IAEFN,IAAI,CAAC,2BAA2B,EAAE,YAAY;MAC5C,MAAMJ,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC0D,GAAG,CAAC,kBAAkBQ,UAAU,EAAE,CAAC,CACnCd,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC;MAE9C4C,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACS,EAAE,CAAC,CAACF,IAAI,CAACW,UAAU,CAAC;MACzCb,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACnC,IAAI,CAAC,CAAC0C,IAAI,CAAC7B,YAAY,CAACb,IAAI,CAAC;IACpD,CAAC,CAAC;IAEFsC,IAAI,CAAC,wBAAwB,EAAE,YAAY;MACzC,MAAMgB,eAAe,GAAG;QACtB,GAAGzC,YAAY;QACfb,IAAI,EAAE;MACR,CAAC;MAED,MAAMkC,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC4D,GAAG,CAAC,kBAAkBM,UAAU,EAAE,CAAC,CACnCd,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC,CAC3CqC,IAAI,CAACqB,eAAe,CAAC;MAExBd,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACnC,IAAI,CAAC,CAAC0C,IAAI,CAACY,eAAe,CAACtD,IAAI,CAAC;IACvD,CAAC,CAAC;IAEFsC,IAAI,CAAC,2BAA2B,EAAE,YAAY;MAC5C,MAAMJ,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC0D,GAAG,CAAC,gBAAgB,CAAC,CACrBN,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC;MAE9C4C,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACQ,KAAK,CAACC,OAAO,CAACf,QAAQ,CAACC,IAAI,CAAC,CAAC,CAACO,IAAI,CAAC,IAAI,CAAC;MAC/CF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACe,MAAM,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;IACjD,CAAC,CAAC;IAEFb,IAAI,CAAC,yBAAyB,EAAE,YAAY;MAC1C;MACAiB,IAAI,CAACC,KAAK,CAAC/D,iBAAiB,EAAE,kBAAkB,CAAC,CAACgE,iBAAiB,CAAC;QAClEC,IAAI,EAAE;UACJd,EAAE,EAAE,GAAG;UACPe,UAAU,EAAE;YACV3D,IAAI,EAAE;UACR;QACF;MACF,CAAC,CAAC;MAEF,MAAMkC,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC6C,IAAI,CAAC,kBAAkBqB,UAAU,UAAU,CAAC,CAC5Cd,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC,CAC3CqC,IAAI,CAAC,CAAC,CAAC,CAAC;MAEXO,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAAC,CAACQ,cAAc,CAAC,QAAQ,CAAC;MAC9CH,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACyB,MAAM,CAAC,CAACjB,cAAc,CAAC,IAAI,CAAC;MACjDH,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACyB,MAAM,CAAC,CAACjB,cAAc,CAAC,MAAM,CAAC;MACnDH,MAAM,CAACN,QAAQ,CAACC,IAAI,CAACyB,MAAM,CAAC5D,IAAI,CAAC,CAAC0C,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACvD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFL,QAAQ,CAAC,yBAAyB,EAAE,MAAM;IACxCC,IAAI,CAAC,0BAA0B,EAAE,YAAY;MAC3C,MAAMJ,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC0D,GAAG,CAAC,oBAAoB,CAAC,CACzBN,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC;MAE9C4C,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAAC,CAACQ,cAAc,CAAC,OAAO,CAAC;MAC7CH,MAAM,CAAC,OAAON,QAAQ,CAACC,IAAI,CAAC0B,KAAK,CAAC,CAACnB,IAAI,CAAC,QAAQ,CAAC;IACnD,CAAC,CAAC;IAEFJ,IAAI,CAAC,6BAA6B,EAAE,YAAY;MAC9C,MAAMuB,KAAK,GAAG;QACZ,yBAAyB,EAAE,IAAI;QAC/B,mBAAmB,EAAE;MACvB,CAAC;MAED,MAAM3B,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC4D,GAAG,CAAC,oBAAoB,CAAC,CACzBR,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC,CAC3CqC,IAAI,CAAC;QAAE4B;MAAM,CAAC,CAAC;MAElBrB,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAAC0B,KAAK,CAAC,yBAAyB,CAAC,CAAC,CAACnB,IAAI,CAAC,IAAI,CAAC;MACjEF,MAAM,CAACN,QAAQ,CAACC,IAAI,CAAC0B,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAACnB,IAAI,CAAC,IAAI,CAAC;IAC7D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFL,QAAQ,CAAC,UAAU,EAAE,MAAM;IACzBC,IAAI,CAAC,iCAAiC,EAAE,YAAY;MAClD,MAAMwB,QAAQ,GAAG;QAAEC,MAAM,EAAE;MAAc,CAAC;;MAE1C;MACA,MAAMC,eAAe,GAAG,MAAMjF,OAAO,CAACI,GAAG,CAAC,CACvC6C,IAAI,CAAC,uBAAuB,CAAC,CAC7BO,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC,CAC3CqC,IAAI,CAAC;QAAEyB,IAAI,EAAEI;MAAS,CAAC,CAAC;MAE3BtB,MAAM,CAACwB,eAAe,CAACvB,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACxCF,MAAM,CAACwB,eAAe,CAAC7B,IAAI,CAAC,CAACQ,cAAc,CAAC,eAAe,CAAC;;MAE5D;MACA,MAAMsB,eAAe,GAAG,MAAMlF,OAAO,CAACI,GAAG,CAAC,CACvC6C,IAAI,CAAC,uBAAuB,CAAC,CAC7BO,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC,CAC3CqC,IAAI,CAAC;QAAEiC,aAAa,EAAEF,eAAe,CAAC7B,IAAI,CAAC+B;MAAc,CAAC,CAAC;MAE9D1B,MAAM,CAACyB,eAAe,CAACxB,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACxCF,MAAM,CAACyB,eAAe,CAAC9B,IAAI,CAAC,CAACQ,cAAc,CAAC,MAAM,CAAC;MACnDH,MAAM,CAACyB,eAAe,CAAC9B,IAAI,CAACuB,IAAI,CAAC,CAACS,OAAO,CAACL,QAAQ,CAAC;IACrD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFzB,QAAQ,CAAC,eAAe,EAAE,MAAM;IAC9BC,IAAI,CAAC,4BAA4B,EAAE,YAAY;MAC7C,MAAMJ,QAAQ,GAAG,MAAMnD,OAAO,CAACI,GAAG,CAAC,CAChC0D,GAAG,CAAC,iBAAiB,CAAC,CACtBN,GAAG,CAAC,eAAe,EAAE,UAAU3C,SAAS,EAAE,CAAC;MAE9C4C,MAAM,CAACN,QAAQ,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACQ,KAAK,CAACC,OAAO,CAACf,QAAQ,CAACC,IAAI,CAAC,CAAC,CAACO,IAAI,CAAC,IAAI,CAAC;IACjD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}