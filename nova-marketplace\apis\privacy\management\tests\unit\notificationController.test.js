/**
 * Notification Controller Tests
 *
 * This file contains unit tests for the notification controller.
 */

const { Notification } = require('../../models');
const notificationController = require('../../controllers/notificationController');

// Mock the Notification model
jest.mock('../../models', () => {
  // Create a mock notification object
  const mockNotification = {
    _id: 'notification-123',
    recipient: '<EMAIL>',
    type: 'data-breach',
    subject: 'Data Breach Notification',
    content: 'There has been a data breach affecting your account.',
    priority: 'high',
    status: 'pending',
    createdAt: new Date(),
    sentAt: null,
    readAt: null,
    save: jest.fn().mockResolvedValue(true),
    remove: jest.fn().mockResolvedValue(true)
  };

  // Create a mock constructor function
  const MockNotification = jest.fn().mockImplementation((data) => {
    return {
      ...mockNotification,
      ...data,
      save: jest.fn().mockResolvedValue(true)
    };
  });

  // Add static methods to the constructor
  MockNotification.find = jest.fn().mockReturnThis();
  MockNotification.findById = jest.fn().mockResolvedValue(mockNotification);
  MockNotification.countDocuments = jest.fn().mockResolvedValue(10);
  MockNotification.sort = jest.fn().mockReturnThis();
  MockNotification.skip = jest.fn().mockReturnThis();
  MockNotification.limit = jest.fn().mockResolvedValue([mockNotification]);

  // Store the mock notification for test assertions
  MockNotification.mockNotification = mockNotification;

  return {
    Notification: MockNotification
  };
});

describe('Notification Controller', () => {
  // Mock Express request and response
  let req;
  let res;
  let next;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup request mock
    req = {
      params: {},
      query: {},
      body: {}
    };

    // Setup response mock
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    // Setup next function mock
    next = jest.fn();
  });

  it('should have the correct functions', () => {
    expect(typeof notificationController.getAllNotifications).toBe('function');
    expect(typeof notificationController.getNotificationById).toBe('function');
    expect(typeof notificationController.createNotification).toBe('function');
    expect(typeof notificationController.markNotificationAsRead).toBe('function');
    expect(typeof notificationController.sendNotification).toBe('function');
    expect(typeof notificationController.generateNotifications).toBe('function');
    expect(typeof notificationController.sendAllPendingNotifications).toBe('function');
    expect(typeof notificationController.getNotificationsByRecipient).toBe('function');
    expect(typeof notificationController.getNotificationsByRelatedEntity).toBe('function');
  });

  describe('getAllNotifications', () => {
    it('should return all notifications with pagination', async () => {
      // Call the controller
      await notificationController.getAllNotifications(req, res, next);

      // Assertions
      expect(Notification.find).toHaveBeenCalledWith({});
      expect(Notification.sort).toHaveBeenCalledWith({ createdAt: -1 });
      expect(Notification.skip).toHaveBeenCalledWith(0);
      expect(Notification.limit).toHaveBeenCalledWith(10);
      expect(Notification.countDocuments).toHaveBeenCalledWith({});
      expect(res.json).toHaveBeenCalledWith({
        data: expect.any(Array),
        pagination: {
          total: 10,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should apply filters from query parameters', async () => {
      // Setup request with query parameters
      req.query = {
        page: '2',
        limit: '5',
        status: 'sent',
        recipient: '<EMAIL>',
        type: 'data-breach',
        priority: 'high',
        search: 'breach',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        sortBy: 'priority',
        sortOrder: 'desc'
      };

      // Call the controller
      await notificationController.getAllNotifications(req, res, next);

      // Assertions
      expect(Notification.find).toHaveBeenCalledWith({
        status: 'sent',
        recipient: '<EMAIL>',
        type: 'data-breach',
        priority: 'high',
        $text: { $search: 'breach' },
        createdAt: {
          $gte: expect.any(Date),
          $lte: expect.any(Date)
        }
      });
      expect(Notification.sort).toHaveBeenCalledWith({ priority: -1 });
      expect(Notification.skip).toHaveBeenCalledWith(5);
      expect(Notification.limit).toHaveBeenCalledWith(5);
    });

    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Database error');
      Notification.find.mockImplementationOnce(() => {
        throw error;
      });

      // Call the controller
      await notificationController.getAllNotifications(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('getNotificationById', () => {
    it('should return a notification by ID', async () => {
      // Setup request with ID parameter
      req.params.id = 'notification-123';

      // Call the controller
      await notificationController.getNotificationById(req, res, next);

      // Assertions
      expect(Notification.findById).toHaveBeenCalledWith('notification-123');
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          _id: 'notification-123'
        })
      });
    });

    it('should handle NotFoundError', async () => {
      // Setup request with ID parameter
      req.params.id = 'non-existent';

      // Setup model to return null
      Notification.findById.mockResolvedValueOnce(null);

      // Call the controller
      await notificationController.getNotificationById(req, res, next);

      // Assertions
      expect(Notification.findById).toHaveBeenCalledWith('non-existent');
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Notification not found'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request with ID parameter
      req.params.id = 'notification-123';

      // Setup error
      const error = new Error('Database error');
      Notification.findById.mockRejectedValueOnce(error);

      // Call the controller
      await notificationController.getNotificationById(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('createNotification', () => {
    it('should create a new notification', async () => {
      // Setup request body
      req.body = {
        recipient: '<EMAIL>',
        type: 'data-breach',
        subject: 'Data Breach Notification',
        content: 'There has been a data breach affecting your account.',
        priority: 'high'
      };

      // Call the controller
      await notificationController.createNotification(req, res, next);

      // Assertions
      expect(Notification).toHaveBeenCalledWith(req.body);
      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          recipient: '<EMAIL>',
          type: 'data-breach'
        }),
        message: 'Notification created successfully'
      });
    });

    it('should handle errors', async () => {
      // Setup request body
      req.body = {
        recipient: '<EMAIL>',
        type: 'data-breach'
      };

      // Setup error
      const error = new Error('Validation error');

      // Mock the Notification constructor to throw an error
      Notification.mockImplementationOnce(() => {
        throw error;
      });

      // Call the controller
      await notificationController.createNotification(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('markNotificationAsRead', () => {
    it('should mark a notification as read', async () => {
      // Setup request with ID parameter
      req.params.id = 'notification-123';

      // Call the controller
      await notificationController.markNotificationAsRead(req, res, next);

      // Assertions
      expect(Notification.findById).toHaveBeenCalledWith('notification-123');
      expect(Notification.mockNotification.status).toBe('read');
      expect(Notification.mockNotification.readAt).toBeInstanceOf(Date);
      expect(Notification.mockNotification.save).toHaveBeenCalled();
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          status: 'read',
          readAt: expect.any(Date)
        }),
        message: 'Notification marked as read'
      });
    });

    it('should handle NotFoundError', async () => {
      // Setup request with ID parameter
      req.params.id = 'non-existent';

      // Setup model to return null
      Notification.findById.mockResolvedValueOnce(null);

      // Call the controller
      await notificationController.markNotificationAsRead(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Notification not found'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request with ID parameter
      req.params.id = 'notification-123';

      // Setup error
      const error = new Error('Database error');
      Notification.findById.mockRejectedValueOnce(error);

      // Call the controller
      await notificationController.markNotificationAsRead(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('sendNotification', () => {
    it('should send a notification', async () => {
      // Setup request with ID parameter
      req.params.id = 'notification-123';

      // Reset the mock notification status to pending before the test
      Notification.mockNotification.status = 'pending';
      Notification.mockNotification.sentAt = null;

      // Call the controller
      await notificationController.sendNotification(req, res, next);

      // Assertions
      expect(Notification.findById).toHaveBeenCalledWith('notification-123');
      expect(Notification.mockNotification.status).toBe('sent');
      expect(Notification.mockNotification.sentAt).toBeInstanceOf(Date);
      expect(Notification.mockNotification.save).toHaveBeenCalled();
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          status: 'sent',
          sentAt: expect.any(Date)
        }),
        message: 'Notification sent successfully'
      });
    });

    it('should handle NotFoundError', async () => {
      // Setup request with ID parameter
      req.params.id = 'non-existent';

      // Setup model to return null
      Notification.findById.mockResolvedValueOnce(null);

      // Call the controller
      await notificationController.sendNotification(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Notification not found'
      }));
    });

    it('should handle ValidationError when notification is not in pending status', async () => {
      // Setup request with ID parameter
      req.params.id = 'notification-123';

      // Setup notification with non-pending status
      const mockNotification = {
        ...Notification.mockNotification,
        status: 'sent',
        sentAt: new Date()
      };
      Notification.findById.mockResolvedValueOnce(mockNotification);

      // Call the controller
      await notificationController.sendNotification(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: "Notification cannot be sent because it is in 'sent' status"
      }));
    });

    it('should handle other errors', async () => {
      // Setup request with ID parameter
      req.params.id = 'notification-123';

      // Setup error
      const error = new Error('Database error');
      Notification.findById.mockRejectedValueOnce(error);

      // Call the controller
      await notificationController.sendNotification(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('generateNotifications', () => {
    it('should generate notifications based on criteria', async () => {
      // Setup request body
      req.body = {
        notificationType: 'data-breach',
        filter: {
          dataCategories: ['personal_data', 'financial_data'],
          severity: 'high'
        }
      };

      // Call the controller
      await notificationController.generateNotifications(req, res, next);

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          generatedCount: expect.any(Number),
          notificationType: 'data-breach',
          filter: expect.any(Object)
        }),
        message: expect.stringMatching(/\d+ notifications generated successfully/)
      });
    });

    it('should handle ValidationError when notification type is not provided', async () => {
      // Setup request body without notification type
      req.body = {
        filter: {
          dataCategories: ['personal_data']
        }
      };

      // Call the controller
      await notificationController.generateNotifications(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Notification type is required'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request body
      req.body = {
        notificationType: 'data-breach',
        filter: {}
      };

      // Setup error
      const error = new Error('Unexpected error');
      jest.spyOn(Math, 'floor').mockImplementationOnce(() => {
        throw error;
      });

      // Call the controller
      await notificationController.generateNotifications(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);

      // Restore Math.floor
      jest.spyOn(Math, 'floor').mockRestore();
    });
  });

  describe('sendAllPendingNotifications', () => {
    it('should send all pending notifications', async () => {
      // Call the controller
      await notificationController.sendAllPendingNotifications(req, res, next);

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          pendingCount: expect.any(Number),
          sentCount: expect.any(Number),
          failedCount: expect.any(Number)
        }),
        message: expect.stringMatching(/\d+ notifications sent successfully, \d+ failed/)
      });
    });

    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Unexpected error');
      jest.spyOn(Math, 'floor').mockImplementationOnce(() => {
        throw error;
      });

      // Call the controller
      await notificationController.sendAllPendingNotifications(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);

      // Restore Math.floor
      jest.spyOn(Math, 'floor').mockRestore();
    });
  });

  describe('getNotificationsByRecipient', () => {
    it('should get notifications by recipient', async () => {
      // Setup request with recipient parameter
      req.params.recipient = '<EMAIL>';

      // Setup mock notifications
      const mockNotifications = [
        { _id: 'notification-1', recipient: '<EMAIL>', type: 'data-breach' },
        { _id: 'notification-2', recipient: '<EMAIL>', type: 'privacy-policy-update' }
      ];
      Notification.find.mockReturnValueOnce({
        sort: jest.fn().mockReturnValue(mockNotifications)
      });

      // Call the controller
      await notificationController.getNotificationsByRecipient(req, res, next);

      // Assertions
      expect(Notification.find).toHaveBeenCalledWith({ recipient: '<EMAIL>' });
      expect(res.json).toHaveBeenCalledWith({
        data: mockNotifications
      });
    });

    it('should handle errors', async () => {
      // Setup request with recipient parameter
      req.params.recipient = '<EMAIL>';

      // Setup error
      const error = new Error('Database error');
      Notification.find.mockImplementationOnce(() => {
        throw error;
      });

      // Call the controller
      await notificationController.getNotificationsByRecipient(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('getNotificationsByRelatedEntity', () => {
    it('should get notifications by related entity', async () => {
      // Setup request with entity parameters
      req.params.entityType = 'data-breach';
      req.params.entityId = 'breach-123';

      // Setup mock notifications
      const mockNotifications = [
        { _id: 'notification-1', relatedEntityType: 'data-breach', relatedEntityId: 'breach-123' },
        { _id: 'notification-2', relatedEntityType: 'data-breach', relatedEntityId: 'breach-123' }
      ];
      Notification.find.mockReturnValueOnce({
        sort: jest.fn().mockReturnValue(mockNotifications)
      });

      // Call the controller
      await notificationController.getNotificationsByRelatedEntity(req, res, next);

      // Assertions
      expect(Notification.find).toHaveBeenCalledWith({
        relatedEntityType: 'data-breach',
        relatedEntityId: 'breach-123'
      });
      expect(res.json).toHaveBeenCalledWith({
        data: mockNotifications
      });
    });

    it('should handle errors', async () => {
      // Setup request with entity parameters
      req.params.entityType = 'data-breach';
      req.params.entityId = 'breach-123';

      // Setup error
      const error = new Error('Database error');
      Notification.find.mockImplementationOnce(() => {
        throw error;
      });

      // Call the controller
      await notificationController.getNotificationsByRelatedEntity(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });
});

