"""
Tests for the ProteinBenchmark class.
"""

import unittest
import os
from pathlib import Path
from unittest.mock import patch, MagicMock
from Bio.SeqRecord import <PERSON>qRecord
from Bio.Seq import Seq
from Bio.PDB.Structure import Structure

# Import the module to test
from src.protein_benchmarks import ProteinBenchmark

class TestProteinBenchmark(unittest.TestCase):
    """Test cases for ProteinBenchmark class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.cache_dir = "test_cache"
        self.benchmark = ProteinBenchmark(cache_dir=self.cache_dir)
        
        # Create test cache directory
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Create a test FASTA file
        self.test_uniprot_id = "P12345"
        self.test_sequence = "ACDEFGHIKLMNPQRSTVWY"
        self.test_fasta = f">sp|{self.test_uniprot_id}|TEST_PROTEIN Test Protein\n{self.test_sequence}"
        
        with open(f"{self.cache_dir}/{self.test_uniprot_id}.fasta", 'w') as f:
            f.write(self.test_fasta)
    
    def tearDown(self):
        """Clean up after tests."""
        # Remove test cache directory
        import shutil
        if os.path.exists(self.cache_dir):
            shutil.rmtree(self.cache_dir)
    
    @patch('requests.get')
    def test_fetch_uniprot_sequence(self, mock_get):
        """Test fetching a sequence from UniProt."""
        # Mock the response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = self.test_fasta
        mock_get.return_value = mock_response
        
        # Test with a new UniProt ID
        seq = self.benchmark.fetch_uniprot_sequence("P54321")
        self.assertIsInstance(seq, SeqRecord)
        self.assertEqual(str(seq.seq), self.test_sequence)
        
        # Test with cached sequence
        seq = self.benchmark.fetch_uniprot_sequence(self.test_uniprot_id)
        self.assertIsInstance(seq, SeqRecord)
        self.assertEqual(str(seq.seq), self.test_sequence)
    
    @patch('requests.get')
    def test_fetch_pdb_structure(self, mock_get):
        """Test fetching a PDB structure."""
        # Mock the PDB file download
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = """
ATOM      1  N   ALA A   1       1.000   1.000   1.000  1.00 10.00           N  
ATOM      2  CA  ALA A   1       2.000   2.000   2.000  1.00 10.00           C  
ATOM      3  C   ALA A   1       3.000   3.000   3.000  1.00 10.00           C  
ATOM      4  O   ALA A   1       4.000   4.000   4.000  1.00 10.00           O  
ATOM      5  CB  ALA A   1       1.000   2.000   3.000  1.00 10.00           C  
        """
        mock_get.return_value = mock_response
        
        # Test fetching a PDB structure
        structure = self.benchmark.fetch_pdb_structure("1ABC")
        self.assertIsNotNone(structure)
        
        # Verify the structure was cached
        self.assertTrue(os.path.exists(f"{self.cache_dir}/1abc.pdb"))
    
    @patch('requests.get')
    def test_validate_secondary_structure(self, mock_get):
        """Test secondary structure validation."""
        # Mock the PDB file download
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = """
HELIX    1   1 ALA A    1  ALA A    5  1                                   5   
SHEET    1 A1 2 ALA A   1  ALA A   5  0                                 
SHEET    1 A2 2 ALA A   8  ALA A  12  0                                 
ATOM      1  N   ALA A   1       1.000   1.000   1.000  1.00 10.00           N  
ATOM      2  CA  ALA A   1       2.000   2.000   2.000  1.00 10.00           C  
        """
        mock_get.return_value = mock_response
        
        # Test validation with a mock secondary structure
        result = self.benchmark.validate_secondary_structure("HHHHHCCCCC", "1ABC")
        self.assertIn("pdb_secondary_structure", result)
        self.assertIn("mock_secondary_structure", result)
        self.assertIn("secondary_structure_similarity", result)
        self.assertIn("is_valid", result)
    
    @patch('requests.get')
    def test_generate_benchmark_report(self, mock_get):
        """Test generating a benchmark report."""
        # Mock the PDB file download and API response
        mock_pdb_response = MagicMock()
        mock_pdb_response.status_code = 200
        mock_pdb_response.text = """
ATOM      1  N   ALA A   1       1.000   1.000   1.000  1.00 10.00           N  
ATOM      2  CA  ALA A   1       2.000   2.000   2.000  1.00 10.00           C  
        """
        
        mock_api_response = MagicMock()
        mock_api_response.status_code = 200
        mock_api_response.json.return_value = {
            "rcsb_id": "1ABC",
            "exptl": [{"method": "X-RAY DIFFRACTION"}],
            "rcsb_primary_citation": {"title": "Test Structure"}
        }
        
        mock_get.side_effect = [mock_pdb_response, mock_api_response]
        
        # Test generating a benchmark report
        mock_data = {
            "sequence": "ACDEFGHIKLMNPQRSTVWY",
            "secondary_structure": "HHHHHCCCCC",
            "metrics": {"test_metric": 0.95}
        }
        
        report = self.benchmark.generate_benchmark_report(mock_data, "1ABC")
        
        # Verify the report structure
        self.assertEqual(report["pdb_id"], "1ABC")
        self.assertIn("sequence_validation", report)
        self.assertIn("secondary_structure_validation", report)
        self.assertIn("metrics_validation", report)

if __name__ == "__main__":
    unittest.main()
