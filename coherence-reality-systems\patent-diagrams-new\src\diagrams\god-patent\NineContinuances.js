import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText,
  InventorLabel
} from '../../components/DiagramComponents';

const NineContinuances = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel>CYBER-SAFETY FRAMEWORK: 9 INDUSTRY FOCUS</ContainerLabel>
      </ContainerBox>

      {/* Top Row - Continuances 1-3 */}
      <ComponentBox left="100px" top="80px" width="150px" height="100px">
        <ComponentNumber>401</ComponentNumber>
        <ComponentLabel>C1: Financial Services</ComponentLabel>
        PCI-DSS, SOX, GLBA
      </ComponentBox>

      <ComponentBox left="270px" top="80px" width="150px" height="100px">
        <ComponentNumber>402</ComponentNumber>
        <ComponentLabel>C2: Healthcare</ComponentLabel>
        HIPAA, HITECH, FDA
      </ComponentBox>

      <ComponentBox left="440px" top="80px" width="150px" height="100px">
        <ComponentNumber>403</ComponentNumber>
        <ComponentLabel>C3: Education</ComponentLabel>
        FERPA, COPPA
      </ComponentBox>

      {/* Middle Row - Continuances 4-6 */}
      <ComponentBox left="100px" top="200px" width="150px" height="100px">
        <ComponentNumber>404</ComponentNumber>
        <ComponentLabel>C4: Government & Defense</ComponentLabel>
        FedRAMP, CMMC, FISMA
      </ComponentBox>

      <ComponentBox left="270px" top="200px" width="150px" height="100px">
        <ComponentNumber>405</ComponentNumber>
        <ComponentLabel>C5: Critical Infrastructure</ComponentLabel>
        NERC-CIP, IEC 62443
      </ComponentBox>

      <ComponentBox left="440px" top="200px" width="150px" height="100px">
        <ComponentNumber>406</ComponentNumber>
        <ComponentLabel>C6: AI Governance</ComponentLabel>
        EU AI Act, NIST AI RMF
      </ComponentBox>

      {/* Bottom Row - Continuances 7-9 */}
      <ComponentBox left="100px" top="320px" width="150px" height="100px">
        <ComponentNumber>407</ComponentNumber>
        <ComponentLabel>C7: Supply Chain</ComponentLabel>
        ISO 28000, NIST CSF
      </ComponentBox>

      <ComponentBox left="270px" top="320px" width="150px" height="100px">
        <ComponentNumber>408</ComponentNumber>
        <ComponentLabel>C8: Insurance</ComponentLabel>
        NAIC Model Law, GDPR
      </ComponentBox>

      <ComponentBox left="440px" top="320px" width="150px" height="100px">
        <ComponentNumber>409</ComponentNumber>
        <ComponentLabel>C9: Mobile/IoT</ComponentLabel>
        CTIA, ETSI EN 303 645
      </ComponentBox>

      {/* Pillar and Nova Integration */}
      <ContainerBox width="700px" height="120px" left="80px" top="450px">
        <ContainerLabel>INTEGRATION WITH PILLARS AND NOVAS</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="100px" top="490px" width="150px" height="60px">
        <ComponentNumber>410</ComponentNumber>
        <ComponentLabel>Pillar Integration</ComponentLabel>
        P7 + P10
      </ComponentBox>

      <ComponentBox left="270px" top="490px" width="150px" height="60px">
        <ComponentNumber>411</ComponentNumber>
        <ComponentLabel>Nova Integration</ComponentLabel>
        N7 + N12
      </ComponentBox>

      <ComponentBox left="440px" top="490px" width="150px" height="60px">
        <ComponentNumber>412</ComponentNumber>
        <ComponentLabel>Regulatory Mapping</ComponentLabel>
        Dynamic Controls
      </ComponentBox>

      <ComponentBox left="610px" top="490px" width="150px" height="60px">
        <ComponentNumber>413</ComponentNumber>
        <ComponentLabel>Implementation</ComponentLabel>
        Industry-Specific
      </ComponentBox>

      {/* Connecting arrows */}
      <Arrow left="175px" top="180px" width="2px" height="20px" />
      <Arrow left="345px" top="180px" width="2px" height="20px" />
      <Arrow left="515px" top="180px" width="2px" height="20px" />

      <Arrow left="175px" top="300px" width="2px" height="20px" />
      <Arrow left="345px" top="300px" width="2px" height="20px" />
      <Arrow left="515px" top="300px" width="2px" height="20px" />

      <Arrow left="175px" top="420px" width="2px" height="30px" />
      <Arrow left="345px" top="420px" width="2px" height="30px" />
      <Arrow left="515px" top="420px" width="2px" height="30px" />

      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>Industry-Specific Continuances</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>Integration Components</LegendText>
        </LegendItem>
      </DiagramLegend>

      {/* Inventor Label */}
      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default NineContinuances;
