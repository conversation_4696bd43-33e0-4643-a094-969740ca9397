/**
 * NovaConnect Encryption Service
 * 
 * FIPS 140-3 compliant encryption service for securing sensitive data
 * in NovaConnect. Supports key rotation, envelope encryption, and
 * integration with Google Cloud KMS.
 */

const crypto = require('crypto');
const { promisify } = require('util');
const { KeyManagementServiceClient } = require('@google-cloud/kms');

// Promisify crypto functions
const randomBytesAsync = promisify(crypto.randomBytes);
const pbkdf2Async = promisify(crypto.pbkdf2);

class EncryptionService {
  constructor(options = {}) {
    this.options = {
      algorithm: 'aes-256-gcm',
      keyLength: 32, // 256 bits
      ivLength: 16, // 128 bits
      saltLength: 32, // 256 bits
      iterations: 100000,
      digest: 'sha256',
      tagLength: 16, // 128 bits
      useGcpKms: false,
      ...options
    };
    
    // Initialize key cache
    this.keyCache = new Map();
    
    // Initialize GCP KMS client if enabled
    if (this.options.useGcpKms) {
      this._initializeKmsClient();
    }
    
    // Initialize metrics
    this.metrics = {
      encryptionOperations: 0,
      decryptionOperations: 0,
      keyRotations: 0,
      totalEncryptionTime: 0,
      totalDecryptionTime: 0,
      averageEncryptionTime: 0,
      averageDecryptionTime: 0
    };
  }
  
  /**
   * Initialize GCP KMS client
   * @private
   */
  _initializeKmsClient() {
    try {
      this.kmsClient = new KeyManagementServiceClient({
        credentials: this.options.gcpCredentials,
        projectId: this.options.gcpProjectId
      });
      
      this.kmsKeyName = this.options.kmsKeyName;
    } catch (error) {
      console.error('Error initializing KMS client:', error);
      throw error;
    }
  }
  
  /**
   * Generate a new encryption key
   * @returns {Object} - Key information
   */
  async generateKey() {
    const startTime = Date.now();
    
    try {
      // Generate a random key
      const key = await randomBytesAsync(this.options.keyLength);
      
      // Generate a unique key ID
      const keyId = crypto.createHash('sha256')
        .update(key)
        .update(Date.now().toString())
        .digest('hex');
      
      // Store key in cache
      this.keyCache.set(keyId, {
        key,
        createdAt: new Date().toISOString(),
        algorithm: this.options.algorithm
      });
      
      return {
        keyId,
        algorithm: this.options.algorithm,
        createdAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating key:', error);
      throw error;
    } finally {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Update metrics
      this.metrics.keyRotations++;
      this.metrics.totalKeyRotationTime = (this.metrics.totalKeyRotationTime || 0) + duration;
      this.metrics.averageKeyRotationTime = this.metrics.totalKeyRotationTime / this.metrics.keyRotations;
    }
  }
  
  /**
   * Encrypt data using the specified key
   * @param {string|Buffer|Object} data - Data to encrypt
   * @param {string} keyId - Key ID to use for encryption
   * @returns {Object} - Encrypted data
   */
  async encrypt(data, keyId) {
    const startTime = Date.now();
    
    try {
      // Convert data to buffer if needed
      const dataBuffer = typeof data === 'object' && !(data instanceof Buffer)
        ? Buffer.from(JSON.stringify(data))
        : Buffer.from(data);
      
      // Get the key
      const keyInfo = this.keyCache.get(keyId);
      
      if (!keyInfo) {
        throw new Error(`Key with ID ${keyId} not found`);
      }
      
      // Generate IV
      const iv = await randomBytesAsync(this.options.ivLength);
      
      // Create cipher
      const cipher = crypto.createCipheriv(
        this.options.algorithm,
        keyInfo.key,
        iv
      );
      
      // Encrypt data
      const encrypted = Buffer.concat([
        cipher.update(dataBuffer),
        cipher.final()
      ]);
      
      // Get authentication tag
      const authTag = cipher.getAuthTag();
      
      // Create encrypted package
      const encryptedPackage = {
        keyId,
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        data: encrypted.toString('base64'),
        algorithm: this.options.algorithm,
        encryptedAt: new Date().toISOString()
      };
      
      return encryptedPackage;
    } catch (error) {
      console.error('Error encrypting data:', error);
      throw error;
    } finally {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Update metrics
      this.metrics.encryptionOperations++;
      this.metrics.totalEncryptionTime += duration;
      this.metrics.averageEncryptionTime = this.metrics.totalEncryptionTime / this.metrics.encryptionOperations;
    }
  }
  
  /**
   * Decrypt data using the specified key
   * @param {Object} encryptedPackage - Encrypted data package
   * @returns {Buffer} - Decrypted data
   */
  async decrypt(encryptedPackage) {
    const startTime = Date.now();
    
    try {
      const { keyId, iv, authTag, data, algorithm } = encryptedPackage;
      
      // Get the key
      const keyInfo = this.keyCache.get(keyId);
      
      if (!keyInfo) {
        throw new Error(`Key with ID ${keyId} not found`);
      }
      
      // Convert base64 strings to buffers
      const ivBuffer = Buffer.from(iv, 'base64');
      const authTagBuffer = Buffer.from(authTag, 'base64');
      const encryptedBuffer = Buffer.from(data, 'base64');
      
      // Create decipher
      const decipher = crypto.createDecipheriv(
        algorithm || this.options.algorithm,
        keyInfo.key,
        ivBuffer
      );
      
      // Set auth tag
      decipher.setAuthTag(authTagBuffer);
      
      // Decrypt data
      const decrypted = Buffer.concat([
        decipher.update(encryptedBuffer),
        decipher.final()
      ]);
      
      return decrypted;
    } catch (error) {
      console.error('Error decrypting data:', error);
      throw error;
    } finally {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Update metrics
      this.metrics.decryptionOperations++;
      this.metrics.totalDecryptionTime += duration;
      this.metrics.averageDecryptionTime = this.metrics.totalDecryptionTime / this.metrics.decryptionOperations;
    }
  }
  
  /**
   * Encrypt data with password-based encryption
   * @param {string|Buffer|Object} data - Data to encrypt
   * @param {string} password - Password to use for encryption
   * @returns {Object} - Encrypted data
   */
  async encryptWithPassword(data, password) {
    const startTime = Date.now();
    
    try {
      // Convert data to buffer if needed
      const dataBuffer = typeof data === 'object' && !(data instanceof Buffer)
        ? Buffer.from(JSON.stringify(data))
        : Buffer.from(data);
      
      // Generate salt
      const salt = await randomBytesAsync(this.options.saltLength);
      
      // Derive key from password
      const key = await pbkdf2Async(
        password,
        salt,
        this.options.iterations,
        this.options.keyLength,
        this.options.digest
      );
      
      // Generate IV
      const iv = await randomBytesAsync(this.options.ivLength);
      
      // Create cipher
      const cipher = crypto.createCipheriv(
        this.options.algorithm,
        key,
        iv
      );
      
      // Encrypt data
      const encrypted = Buffer.concat([
        cipher.update(dataBuffer),
        cipher.final()
      ]);
      
      // Get authentication tag
      const authTag = cipher.getAuthTag();
      
      // Create encrypted package
      const encryptedPackage = {
        salt: salt.toString('base64'),
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        data: encrypted.toString('base64'),
        algorithm: this.options.algorithm,
        iterations: this.options.iterations,
        keyLength: this.options.keyLength,
        digest: this.options.digest,
        encryptedAt: new Date().toISOString()
      };
      
      return encryptedPackage;
    } catch (error) {
      console.error('Error encrypting data with password:', error);
      throw error;
    } finally {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Update metrics
      this.metrics.encryptionOperations++;
      this.metrics.totalEncryptionTime += duration;
      this.metrics.averageEncryptionTime = this.metrics.totalEncryptionTime / this.metrics.encryptionOperations;
    }
  }
  
  /**
   * Decrypt data with password-based encryption
   * @param {Object} encryptedPackage - Encrypted data package
   * @param {string} password - Password to use for decryption
   * @returns {Buffer} - Decrypted data
   */
  async decryptWithPassword(encryptedPackage, password) {
    const startTime = Date.now();
    
    try {
      const { 
        salt, 
        iv, 
        authTag, 
        data, 
        algorithm, 
        iterations, 
        keyLength, 
        digest 
      } = encryptedPackage;
      
      // Convert base64 strings to buffers
      const saltBuffer = Buffer.from(salt, 'base64');
      const ivBuffer = Buffer.from(iv, 'base64');
      const authTagBuffer = Buffer.from(authTag, 'base64');
      const encryptedBuffer = Buffer.from(data, 'base64');
      
      // Derive key from password
      const key = await pbkdf2Async(
        password,
        saltBuffer,
        iterations || this.options.iterations,
        keyLength || this.options.keyLength,
        digest || this.options.digest
      );
      
      // Create decipher
      const decipher = crypto.createDecipheriv(
        algorithm || this.options.algorithm,
        key,
        ivBuffer
      );
      
      // Set auth tag
      decipher.setAuthTag(authTagBuffer);
      
      // Decrypt data
      const decrypted = Buffer.concat([
        decipher.update(encryptedBuffer),
        decipher.final()
      ]);
      
      return decrypted;
    } catch (error) {
      console.error('Error decrypting data with password:', error);
      throw error;
    } finally {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Update metrics
      this.metrics.decryptionOperations++;
      this.metrics.totalDecryptionTime += duration;
      this.metrics.averageDecryptionTime = this.metrics.totalDecryptionTime / this.metrics.decryptionOperations;
    }
  }
  
  /**
   * Encrypt data using Google Cloud KMS
   * @param {string|Buffer|Object} data - Data to encrypt
   * @returns {Object} - Encrypted data
   */
  async encryptWithKms(data) {
    if (!this.options.useGcpKms) {
      throw new Error('GCP KMS is not enabled');
    }
    
    const startTime = Date.now();
    
    try {
      // Convert data to buffer if needed
      const dataBuffer = typeof data === 'object' && !(data instanceof Buffer)
        ? Buffer.from(JSON.stringify(data))
        : Buffer.from(data);
      
      // Encrypt with KMS
      const [encryptResponse] = await this.kmsClient.encrypt({
        name: this.kmsKeyName,
        plaintext: dataBuffer
      });
      
      // Create encrypted package
      const encryptedPackage = {
        data: encryptResponse.ciphertext.toString('base64'),
        kmsKeyName: this.kmsKeyName,
        encryptedAt: new Date().toISOString()
      };
      
      return encryptedPackage;
    } catch (error) {
      console.error('Error encrypting data with KMS:', error);
      throw error;
    } finally {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Update metrics
      this.metrics.encryptionOperations++;
      this.metrics.totalEncryptionTime += duration;
      this.metrics.averageEncryptionTime = this.metrics.totalEncryptionTime / this.metrics.encryptionOperations;
    }
  }
  
  /**
   * Decrypt data using Google Cloud KMS
   * @param {Object} encryptedPackage - Encrypted data package
   * @returns {Buffer} - Decrypted data
   */
  async decryptWithKms(encryptedPackage) {
    if (!this.options.useGcpKms) {
      throw new Error('GCP KMS is not enabled');
    }
    
    const startTime = Date.now();
    
    try {
      const { data, kmsKeyName } = encryptedPackage;
      
      // Convert base64 string to buffer
      const ciphertextBuffer = Buffer.from(data, 'base64');
      
      // Decrypt with KMS
      const [decryptResponse] = await this.kmsClient.decrypt({
        name: kmsKeyName || this.kmsKeyName,
        ciphertext: ciphertextBuffer
      });
      
      return decryptResponse.plaintext;
    } catch (error) {
      console.error('Error decrypting data with KMS:', error);
      throw error;
    } finally {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Update metrics
      this.metrics.decryptionOperations++;
      this.metrics.totalDecryptionTime += duration;
      this.metrics.averageDecryptionTime = this.metrics.totalDecryptionTime / this.metrics.decryptionOperations;
    }
  }
  
  /**
   * Rotate encryption keys
   * @returns {Object} - New key information
   */
  async rotateKeys() {
    // Generate a new key
    const newKey = await this.generateKey();
    
    // Mark old keys as rotated
    for (const [keyId, keyInfo] of this.keyCache.entries()) {
      if (keyId !== newKey.keyId) {
        keyInfo.rotatedAt = new Date().toISOString();
        keyInfo.replacedBy = newKey.keyId;
      }
    }
    
    return newKey;
  }
  
  /**
   * Get metrics for the encryption service
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return this.metrics;
  }
}

module.exports = EncryptionService;
